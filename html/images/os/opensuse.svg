<svg viewBox="0 0 227 227" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><filter id="a" color-interpolation-filters="sRGB"><feGaussianBlur stdDeviation="2.052"/></filter><linearGradient id="b"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><clipPath id="c"><path d="m100 30.156c-38.57 0-69.844 31.27-69.844 69.844s31.274 69.844 69.844 69.844 69.844-31.27 69.844-69.844-31.274-69.844-69.844-69.844zm.334 9.673c33.234 0 60.197 26.94 60.197 60.17 0 33.234-26.966 60.17-60.196 60.17-17.944 0-34.047-7.864-45.07-20.322 4.532-3.518 12.116-2.66 21.455-1.595 4.474.51 9.576 1.092 14.842 1.21 14.712.085 30.54-2.6 40.31-6.87 6.318-2.77 10.362-4.594 12.89-6.894.91-.748 1.39-1.997 1.878-3.293l.334-.875c.41-1.047.997-3.3 1.26-4.528.118-.537.176-1.115-.205-1.415l-1.365.257c-4.286 2.57-14.997 7.466-25.03 7.666-12.433.26-37.492-12.517-40.106-13.866l-.258-.31c-.627-1.494-4.377-10.397-5.17-12.27 18.037 11.878 32.99 18.452 44.452 19.474 12.743 1.134 22.683-5.824 26.935-8.797.828-.566 1.462-1.01 1.722-1.106l.283-.463c-.705-4.48-7.39-26.153-12.425-31.36-1.396-1.427-2.505-2.816-4.76-4.14-18.217-10.622-61.487-17.017-63.511-17.312l-.334.103-.128.283-.18 9.982c-1.484-.492-6.088-2-12.682-3.808 8.604-10.646 21.79-20.09 44.864-20.09zm15.9 31.847c.306-.005.616-.01.925 0 10.207.366 18.227 8.964 17.878 19.165-.182 4.935-2.262 9.494-5.866 12.862-3.614 3.386-8.31 5.15-13.274 4.99-10.19-.38-18.21-8.978-17.88-19.164.176-4.942 2.268-9.53 5.892-12.888 3.385-3.175 7.717-4.896 12.323-4.965zm-.49 5.453c-2.973.18-5.78 1.388-7.975 3.448-2.504 2.35-3.98 5.53-4.09 8.978-.236 7.12 5.336 13.113 12.45 13.377 3.457.106 6.736-1.105 9.26-3.448 2.518-2.36 3.984-5.57 4.09-9.003.24-7.132-5.358-13.102-12.476-13.352-.43-.016-.836-.025-1.26 0zm3.395 6.457c3.16 0 5.71 1.718 5.71 3.833 0 2.105-2.55 3.833-5.71 3.833-3.173 0-5.737-1.732-5.737-3.833 0-2.115 2.564-3.833 5.736-3.833z"/></clipPath><filter id="d" color-interpolation-filters="sRGB"><feGaussianBlur stdDeviation=".705"/></filter><linearGradient id="e"><stop offset="0" stop-color="#8ae234"/><stop offset="1" stop-color="#8ae234" stop-opacity="0"/></linearGradient><filter id="f" color-interpolation-filters="sRGB"><feGaussianBlur stdDeviation="2.886"/></filter><filter id="g" color-interpolation-filters="sRGB" viewBox="0 0 1.694 1.099" x="-.347" y="-.05"><feGaussianBlur stdDeviation="1.336"/></filter><filter id="h" color-interpolation-filters="sRGB"><feGaussianBlur stdDeviation="1.69"/></filter><radialGradient id="i" cx="161.341" cy="161.341" gradientUnits="userSpaceOnUse" r="94.57" xlink:href="#b"/><radialGradient id="j" cx="176.801" cy="183.062" gradientUnits="userSpaceOnUse" r="95.13"><stop offset="0" stop-opacity=".187"/><stop offset="1" stop-opacity="0"/></radialGradient><linearGradient id="k" gradientUnits="userSpaceOnUse" x1="60.836" x2="148.277" y1="18.833" y2="153.705"><stop offset="0" stop-color="#3a7202"/><stop offset=".698" stop-color="#4e9c04"/><stop offset=".75" stop-color="#50a004"/><stop offset="1" stop-color="#7bf508"/></linearGradient><linearGradient id="l" gradientUnits="userSpaceOnUse" x1="100" x2="100" y1="166.722" y2="39.739"><stop offset="0"/><stop offset="1" stop-opacity=".481"/></linearGradient><radialGradient id="m" cx="132" cy="293.599" gradientTransform="matrix(.48127 0 0 -.24526 376.54 -122.8)" gradientUnits="userSpaceOnUse" r="102" xlink:href="#e"/><radialGradient id="n" cx="132.011" cy="37.25" gradientTransform="matrix(.93117 0 0 .88017 317.155 -220.004)" gradientUnits="userSpaceOnUse" r="103.844" xlink:href="#b"/><radialGradient id="o" cx="132" cy="293.599" gradientTransform="matrix(1.12522 0 0 .57343 291.54 -172.524)" gradientUnits="userSpaceOnUse" r="102" xlink:href="#e"/><radialGradient id="p" cx="89.75" cy="20.619" gradientTransform="matrix(1.49735 0 0 .55282 266.34 -180.497)" gradientUnits="userSpaceOnUse" r="59.25" xlink:href="#e"/><radialGradient id="q" cx="105.75" cy="38.507" gradientTransform="matrix(1.2848 0 0 .55566 279.758 -185.14)" gradientUnits="userSpaceOnUse" r="59.25" xlink:href="#b"/><radialGradient id="r" cx="158.062" cy="117.97" gradientTransform="matrix(1.5488 0 0 .9677 219.532 -229.114)" gradientUnits="userSpaceOnUse" r="12.438" xlink:href="#b"/><radialGradient id="s" cx="40.808" cy="81.171" gradientTransform="matrix(.93117 0 0 .99202 316.69 -232.252)" gradientUnits="userSpaceOnUse" r="83.064" xlink:href="#b"/><radialGradient id="t" cx="98" cy="146.194" gradientTransform="matrix(.93117 0 0 .74314 320.88 -195.8)" gradientUnits="userSpaceOnUse" r="13" xlink:href="#b"/><radialGradient id="u" cx="164.25" cy="187.768" gradientTransform="matrix(1.22663 0 0 .24853 272.35 -113.885)" gradientUnits="userSpaceOnUse" r="26" xlink:href="#b"/><radialGradient id="v" cx="93" cy="3.5" gradientUnits="userSpaceOnUse" r="93.5" xlink:href="#b"/><linearGradient id="w" gradientUnits="userSpaceOnUse" x1="130.082" x2="130.082" y1="237.062" y2="76.867"><stop offset="0" stop-color="#e1e4de"/><stop offset=".3" stop-color="#dbded8"/><stop offset="1" stop-color="#fff"/></linearGradient><linearGradient id="x" gradientUnits="userSpaceOnUse" x1="130.082" x2="130.082" y1="237.063" y2="103.918"><stop offset="0" stop-color="#888a85"/><stop offset="1" stop-opacity="0"/></linearGradient><path d="m225 157a87 87 0 1 1 -174 0 87 87 0 1 1 174 0z" filter="url(#a)" transform="matrix(1.22883 0 0 1.22883 -55.998 -79.818)"/><path d="m225 157a87 87 0 1 1 -174 0 87 87 0 1 1 174 0z" fill="url(#w)" stroke="url(#x)" stroke-width=".814" transform="matrix(1.22883 0 0 1.22883 -55.998 -81.047)"/><path d="m204 112a92 92 0 1 1 -184 0 92 92 0 1 1 184 0z" fill="none" stroke="url(#i)" stroke-width="4.857" transform="matrix(1.02944 0 0 1.02944 -2.274 -5.32)"/><path d="m204 112a92 92 0 1 1 -184 0 92 92 0 1 1 184 0z" fill="none" stroke="url(#j)" stroke-width="5.617" transform="matrix(.89012 0 0 .89012 13.33 10.286)"/><path d="m100 30.156c-38.57 0-69.844 31.27-69.844 69.844s31.274 69.844 69.844 69.844 69.844-31.27 69.844-69.844-31.274-69.844-69.844-69.844zm.334 9.673c33.234 0 60.197 26.94 60.197 60.17 0 33.234-26.966 60.17-60.196 60.17-17.944 0-34.047-7.864-45.07-20.322 4.532-3.518 12.116-2.66 21.455-1.595 4.474.51 9.576 1.092 14.842 1.21 14.712.085 30.54-2.6 40.31-6.87 6.318-2.77 10.362-4.594 12.89-6.894.91-.748 1.39-1.997 1.878-3.293l.334-.875c.41-1.047.997-3.3 1.26-4.528.118-.537.176-1.115-.205-1.415l-1.365.257c-4.286 2.57-14.997 7.466-25.03 7.666-12.433.26-37.492-12.517-40.106-13.866l-.258-.31c-.627-1.494-4.377-10.397-5.17-12.27 18.037 11.878 32.99 18.452 44.452 19.474 12.743 1.134 22.683-5.824 26.935-8.797.828-.566 1.462-1.01 1.722-1.106l.283-.463c-.705-4.48-7.39-26.153-12.425-31.36-1.396-1.427-2.505-2.816-4.76-4.14-18.217-10.622-61.487-17.017-63.511-17.312l-.334.103-.128.283-.18 9.982c-1.484-.492-6.088-2-12.682-3.808 8.604-10.646 21.79-20.09 44.864-20.09zm15.9 31.847c.306-.005.616-.01.925 0 10.207.366 18.227 8.964 17.878 19.165-.182 4.935-2.262 9.494-5.866 12.862-3.614 3.386-8.31 5.15-13.274 4.99-10.19-.38-18.21-8.978-17.88-19.164.176-4.942 2.268-9.53 5.892-12.888 3.385-3.175 7.717-4.896 12.323-4.965zm-.49 5.453c-2.973.18-5.78 1.388-7.975 3.448-2.504 2.35-3.98 5.53-4.09 8.978-.236 7.12 5.336 13.113 12.45 13.377 3.457.106 6.736-1.105 9.26-3.448 2.518-2.36 3.984-5.57 4.09-9.003.24-7.132-5.358-13.102-12.476-13.352-.43-.016-.836-.025-1.26 0zm3.395 6.457c3.16 0 5.71 1.718 5.71 3.833 0 2.105-2.55 3.833-5.71 3.833-3.173 0-5.737-1.732-5.737-3.833 0-2.115 2.564-3.833 5.736-3.833z" fill="url(#k)" fill-rule="evenodd" transform="matrix(1.3349 0 0 1.3349 -20.09 -22.817)"/><path d="m100 30.156c-38.57 0-69.844 31.27-69.844 69.844s31.274 69.844 69.844 69.844 69.844-31.27 69.844-69.844-31.274-69.844-69.844-69.844zm.334 9.673c33.234 0 60.197 26.94 60.197 60.17 0 33.234-26.966 60.17-60.196 60.17-17.944 0-34.047-7.864-45.07-20.322 4.532-3.518 12.116-2.66 21.455-1.595 4.474.51 9.576 1.092 14.842 1.21 14.712.085 30.54-2.6 40.31-6.87 6.318-2.77 10.362-4.594 12.89-6.894.91-.748 1.39-1.997 1.878-3.293l.334-.875c.41-1.047.997-3.3 1.26-4.528.118-.537.176-1.115-.205-1.415l-1.365.257c-4.286 2.57-14.997 7.466-25.03 7.666-12.433.26-37.492-12.517-40.106-13.866l-.258-.31c-.627-1.494-4.377-10.397-5.17-12.27 18.037 11.878 32.99 18.452 44.452 19.474 12.743 1.134 22.683-5.824 26.935-8.797.828-.566 1.462-1.01 1.722-1.106l.283-.463c-.705-4.48-7.39-26.153-12.425-31.36-1.396-1.427-2.505-2.816-4.76-4.14-18.217-10.622-61.487-17.017-63.511-17.312l-.334.103-.128.283-.18 9.982c-1.484-.492-6.088-2-12.682-3.808 8.604-10.646 21.79-20.09 44.864-20.09zm15.9 31.847c.306-.005.616-.01.925 0 10.207.366 18.227 8.964 17.878 19.165-.182 4.935-2.262 9.494-5.866 12.862-3.614 3.386-8.31 5.15-13.274 4.99-10.19-.38-18.21-8.978-17.88-19.164.176-4.942 2.268-9.53 5.892-12.888 3.385-3.175 7.717-4.896 12.323-4.965zm-.49 5.453c-2.973.18-5.78 1.388-7.975 3.448-2.504 2.35-3.98 5.53-4.09 8.978-.236 7.12 5.336 13.113 12.45 13.377 3.457.106 6.736-1.105 9.26-3.448 2.518-2.36 3.984-5.57 4.09-9.003.24-7.132-5.358-13.102-12.476-13.352-.43-.016-.836-.025-1.26 0zm3.395 6.457c3.16 0 5.71 1.718 5.71 3.833 0 2.105-2.55 3.833-5.71 3.833-3.173 0-5.737-1.732-5.737-3.833 0-2.115 2.564-3.833 5.736-3.833z" style="opacity:.825;fill:none;stroke-width:1.498;clip-path:url(#c);stroke:url(#l);filter:url(#d)" transform="matrix(1.3349 0 0 1.3349 -19.832 -22.992)"/><g fill-rule="evenodd"><path d="m357.22-106.263c-.01-.53-.025-1.06-.025-1.593 0-45.747 37.127-82.874 82.874-82.874 45.745 0 82.872 37.127 82.872 82.874 0 .532-.015 1.064-.025 1.593-.85-45.012-37.634-81.28-82.848-81.28-45.216 0-82 36.268-82.85 81.28z" fill="url(#m)" opacity=".428" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m419.01 24.863c-57.346 0-103.843 46.492-103.843 103.844 0 .5.024 1.002.03 1.5.803-56.66 46.967-102.344 103.814-102.344s103.012 45.683 103.813 102.344c.007-.498.032-1 .032-1.5 0-57.352-46.497-103.844-103.844-103.844zm-46.406 34.5-.5.157-.187.406s-.255 13.36-.28 14.844c-2.044-.676-8.162-2.674-16.814-5.094-.698.807-1.37 1.62-2.03 2.437 9.803 2.69 16.635 4.926 18.843 5.657.026-1.483.28-14.844.28-14.844l.188-.406.5-.157c3.013.44 67.345 9.96 94.438 25.75 3.35 1.97 4.987 4.034 7.062 6.157 6.847 7.08 15.73 34.596 18 44.343.018-.008.047-.025.063-.03l.437-.688c-1.048-6.66-11.014-38.886-18.5-46.625-2.075-2.123-3.71-4.187-7.062-6.157-27.093-15.79-91.425-25.31-94.438-25.75zm70.75 35.313c-.31-.002-.62.012-.937.03-4.42.27-8.61 2.064-11.875 5.126-3.722 3.494-5.898 8.22-6.063 13.344-.026.76 0 1.51.062 2.25.345-4.834 2.46-9.272 6-12.594 3.265-3.062 7.455-4.856 11.875-5.125.63-.038 1.234-.024 1.875 0 9.823.344 17.673 8.013 18.47 17.594.016-.248.053-.498.06-.75.356-10.602-7.948-19.472-18.53-19.843-.32-.012-.628-.03-.938-.03zm-4.093 16.812c-.2.484-.343.975-.343 1.5 0 3.124 3.814 5.72 8.53 5.72 4.702 0 8.5-2.59 8.5-5.72 0-.524-.114-1.017-.31-1.5-.983 2.41-4.266 4.22-8.19 4.22-3.932 0-7.197-1.813-8.186-4.22zm-23.187 4.032c-.012.207-.024.416-.03.625-.493 15.145 11.442 27.933 26.593 28.5 7.38.238 14.376-2.404 19.75-7.438 5.357-5.008 8.448-11.788 8.718-19.125.028-.8.01-1.59-.03-2.375-.42 7.097-3.483 13.634-8.688 18.5-5.374 5.034-12.37 7.676-19.75 7.438-14.352-.537-25.812-12.04-26.563-26.125zm-32.594 11.812c1.18 2.785 6.755 16.027 7.687 18.25l.375.47c3.886 2.005 41.14 21.01 59.625 20.624 14.918-.297 30.845-7.584 37.22-11.406l2.03-.375c.024.018.04.042.062.062.07-.306.195-.713.25-.97.174-.798.253-1.648-.313-2.092l-2.03.375c-6.375 3.822-22.302 11.11-37.22 11.406-18.486.386-55.74-18.62-59.625-20.625l-.375-.468c-.72-1.72-3.843-9.16-5.938-14.125-.585-.38-1.162-.737-1.75-1.125zm125.5 2.875c-.802 48.72-40.564 87.97-89.47 87.97-25.766 0-48.99-10.914-65.312-28.345-.597.34-1.18.706-1.72 1.125 16.39 18.523 40.352 30.22 67.033 30.22 49.407 0 89.5-40.058 89.5-89.47 0-.5-.022-1-.03-1.5z" fill="#fff" filter="url(#f)" transform="matrix(.89784 0 0 .89784 -262.795 -4.25)"/><path d="m440.078-199.605c-53.4 0-96.695 43.29-96.695 96.696 0 .467.022.933.03 1.398.745-52.762 43.732-95.3 96.665-95.3 52.934 0 95.92 42.538 96.667 95.3.007-.465.03-.93.03-1.397 0-53.404-43.297-96.695-96.697-96.695zm-43.21 32.125-.467.146-.174.378s-.237 12.442-.26 13.822c-1.903-.63-7.6-2.49-15.657-4.743-.65.75-1.277 1.508-1.892 2.27 9.13 2.504 15.49 4.586 17.547 5.267.024-1.38.26-13.822.26-13.822l.176-.38.467-.144c2.804.41 62.708 9.273 87.937 23.977 3.12 1.835 4.644 3.757 6.576 5.734 6.376 6.592 14.647 32.215 16.76 41.29.018-.006.044-.022.06-.028l.406-.64c-.976-6.2-10.256-36.21-17.226-43.416-1.932-1.976-3.456-3.9-6.576-5.732-25.23-14.704-85.133-23.57-87.937-23.978zm65.88 32.882c-.29-.002-.58.01-.874.03-4.116.25-8.017 1.92-11.058 4.77-3.465 3.255-5.492 7.655-5.645 12.427-.023.706.002 1.405.06 2.094.32-4.5 2.29-8.634 5.586-11.727 3.04-2.85 6.942-4.522 11.058-4.772.588-.036 1.15-.023 1.745 0 9.147.32 16.456 7.46 17.197 16.382.016-.232.05-.464.058-.698.33-9.875-7.402-18.134-17.256-18.48-.3-.01-.585-.026-.873-.028zm-3.813 15.655c-.186.45-.32.908-.32 1.397 0 2.908 3.55 5.325 7.943 5.325 4.378 0 7.915-2.413 7.915-5.326 0-.488-.107-.948-.29-1.397-.914 2.245-3.972 3.93-7.625 3.93-3.662 0-6.702-1.69-7.623-3.93zm-21.592 3.754c-.01.194-.022.39-.03.583-.457 14.103 10.656 26.01 24.764 26.538 6.872.223 13.387-2.237 18.39-6.924 4.99-4.664 7.868-10.977 8.12-17.81.025-.744.008-1.48-.03-2.21-.39 6.608-3.242 12.695-8.09 17.226-5.003 4.688-11.518 7.148-18.39 6.926-13.363-.5-24.035-11.212-24.734-24.327zm-30.35 11c1.1 2.593 6.29 14.924 7.158 16.994l.35.437c3.62 1.87 38.308 19.566 55.52 19.206 13.892-.276 28.723-7.062 34.658-10.62l1.89-.35c.023.017.04.04.06.058.066-.285.18-.664.232-.902.163-.745.237-1.536-.29-1.95l-1.892.35c-5.935 3.558-20.766 10.344-34.657 10.62-17.212.36-51.9-17.337-55.52-19.205l-.35-.436c-.67-1.6-3.577-8.53-5.527-13.152-.545-.354-1.082-.686-1.63-1.048zm116.86 2.678c-.745 45.366-37.77 81.913-83.31 81.913-23.992 0-45.617-10.16-60.816-26.392-.556.316-1.097.658-1.6 1.048 15.26 17.248 37.573 28.138 62.417 28.138 46.006 0 83.34-37.3 83.34-83.31 0-.466-.022-.932-.03-1.396z" fill="url(#n)" opacity=".357" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m345.118-105.645c-.01.607-.028 1.217-.028 1.827 0 52.428 42.55 94.978 94.98 94.978 52.427 0 94.977-42.55 94.977-94.978 0-.61-.017-1.22-.028-1.827-.975 51.587-43.133 93.152-94.95 93.152-51.82 0-93.978-41.565-94.952-93.152z" fill="url(#o)" opacity=".633" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m379.31-157.575 17.692 5.354.7-14.9c38.317 6.158 71.66 15.248 91.95 27.237-24.775-10.652-25.098-10.522-53.308-2.095-17.925 5.354-20.486 22.58-20.486 22.58-29.265-5.14-22.624-29.56-36.548-38.177z" fill="url(#p)" opacity=".44" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m379.31-157.575 17.692 5.354.7-14.9c38.317 6.158 71.66 15.248 91.95 27.237-24.775-10.652-27.077-9.57-54.938-3.725-27.85 5.845-30.533 2.256-55.404-13.967z" fill="url(#q)" opacity=".44" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m454.734-119.397c6.745 6.275 14.21 7.754 22.814 1.63 0 0-3.84 13.676-14.666 12.803-11.077-.894-8.148-14.433-8.148-14.433z" fill="url(#r)" opacity=".44" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m500.475-174.365c-.445-.394-.888-.795-1.342-1.182-39.025-33.333-97.75-28.714-131.084 10.31-33.334 39.026-28.714 97.752 10.31 131.085.454.387.92.762 1.378 1.14-37.78-33.523-41.915-91.33-8.97-129.9 32.946-38.573 90.69-43.526 129.707-11.453z" fill="url(#s)" opacity=".373" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m500.475-174.365c-.445-.394-.888-.795-1.342-1.182-39.025-33.333-97.75-28.714-131.084 10.31-33.334 39.026-28.714 97.752 10.31 131.085.454.387.92.762 1.378 1.14-37.87-33.55-44.82-94.056-9.9-131.065 34.81-36.897 91.62-42.36 130.637-10.288z" fill="url(#s)" opacity=".373" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m507.728 157.712 6.01-2.475c6.91-23.96 1.728-44.53-6.01-62.225 5.7 18.974 8.432 41.837 0 64.7z" fill="#73d216" filter="url(#g)" opacity=".373" transform="matrix(.8978 0 0 .8978 -262.795 -4.25)"/><path d="m408.41-98.912 4.422 10.243 19.787 9.08-21.883-6.984-2.328-12.338z" fill="url(#t)" opacity=".373" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/><path d="m452.174-71.675c16.78 4.933 32.882 2.746 48.42-5.354-16.81 11.855-32.85 12.097-48.42 5.355z" fill="url(#u)" opacity=".373" transform="matrix(.9642 0 0 .9642 -310.92 210.536)"/></g><g fill="none" stroke="url(#v)"><path d="m184 97a91 91 0 1 1 -182 0 91 91 0 1 1 182 0z" stroke-width="4.392" transform="matrix(1.1384 0 0 1.1384 7.705 -1.007)"/><path d="m184 97a91 91 0 1 1 -182 0 91 91 0 1 1 182 0z" filter="url(#h)" stroke-width="4.534" transform="matrix(1.1027 0 0 1.1027 11.033 2.464)"/></g></svg>