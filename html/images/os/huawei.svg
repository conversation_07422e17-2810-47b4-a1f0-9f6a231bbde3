<svg viewBox="0 0 300 300.20289" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><linearGradient id="a"><stop offset="0" stop-color="#f9ab95"/><stop offset=".42" stop-color="#ed1c24"/><stop offset=".427" stop-color="#ed1c24"/><stop offset=".873" stop-color="#ed1c24"/><stop offset="1" stop-color="#981015"/></linearGradient><radialGradient id="b" cx="0" cy="0" gradientTransform="matrix(30.6689 0 0 -30.6689 54.74 480.557)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><radialGradient id="c" cx="0" cy="0" gradientTransform="matrix(30.66695 0 0 -30.66695 54.74 480.558)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><radialGradient id="d" cx="0" cy="0" gradientTransform="matrix(30.66283 0 0 -30.66283 54.738 480.553)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><radialGradient id="e" cx="0" cy="0" gradientTransform="matrix(30.66843 0 0 -30.66843 54.738 480.556)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><radialGradient id="f" cx="0" cy="0" gradientTransform="matrix(30.63336 0 0 -30.63335 54.73 480.557)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><radialGradient id="g" cx="0" cy="0" gradientTransform="matrix(30.63312 0 0 -30.63312 54.732 480.558)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><radialGradient id="h" cx="0" cy="0" gradientTransform="matrix(30.64128 0 0 -30.64128 54.736 480.564)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><radialGradient id="i" cx="0" cy="0" gradientTransform="matrix(30.63808 0 0 -30.63808 54.731 480.56)" gradientUnits="userSpaceOnUse" r="1" spreadMethod="pad" xlink:href="#a"/><clipPath id="j"><path d="m0 521.57h368.5v-521.57h-368.5z"/></clipPath><path d="m12.008 270.18h24.315v-19.427h10.907v47.93h-10.907v-20.017h-24.315v20.016h-10.934v-47.929h10.934z" fill="#231f20"/><g clip-path="url(#j)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"><path d="m37.376 444.78c0-1.208.38-1.714 1.67-1.714 1.285 0 1.658.506 1.658 1.714v4.095h1.559v-4.131c0-.872-.106-1.473-.438-1.84-.606-.663-1.495-.937-2.78-.937-1.288 0-2.183.274-2.794.938-.338.37-.422.972-.422 1.839v4.13h1.547zm8.001.102h2.207l-1.123 2.63zm1.97 3.992 3.153-6.834h-1.73l-.682 1.626h-3.218l-.654-1.626h-1.652l3.13 6.834zm3.859.001 1.71-5.254 1.444 5.254h1.838l1.654-5.254 1.598 5.254h1.6l-2.287-6.834h-1.907l-1.578 5.098-1.38-5.098h-1.9l-2.464 6.834zm11.939-2.779h4.303v-1.188h-4.313c.01-1.233.533-1.68 1.764-1.68h2.549v-1.188h-2.623c-.893 0-1.588.065-2.309.6-.766.558-1.133 1.457-1.133 2.692 0 2.416 1.1 3.542 3.487 3.542h2.578v-1.194h-2.549c-1.129 0-1.693-.513-1.754-1.584m5.68 2.779h1.625v-6.834h-1.625z" fill="#231f20"/></g><path d="m31.014 471.791c.14-2.671 2.151-4.255 2.151-4.255 3.24-3.157 11.084-7.141 12.904-8.049.024-.002.119-.036.174.035 0 0 .075.059.037.155-4.991 10.904-11.816 19.172-11.816 19.172s-3.715-3.527-3.45-7.058" fill="url(#i)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/><path d="m31.809 456.488c1.415-2.523 3.801-4.486 6.285-3.883 1.717.429 5.596 3.137 6.875 4.054l-.004.001c.1.087.071.161.071.161-.038.117-.172.117-.172.117v.005z" fill="url(#h)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/><path d="m28.685 462.349c1.416-2.994 4.121-3.902 4.121-3.902 1.247-.513 2.496-.546 2.496-.546.195-.035 7.765-.004 9.792.004.088 0 .135.088.135.088.063.102-.047.198-.047.198v.001c-5.734 3.866-16.849 9.805-16.849 9.805-1.011-3.127.352-5.648.352-5.648" fill="url(#g)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/><path d="m43.993 484.086c-3.457-.894-4.274-4.03-4.274-4.03-.63-1.974.018-4.14.018-4.14 1.15-5.121 6.83-13.531 8.052-15.298.088-.087.153-.054.153-.054a.163.163 0 0 1 .125.161c1.875 18.782-1.974 23.761-1.974 23.761-.567-.053-2.1-.4-2.1-.4" fill="url(#f)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/><path d="m50.313 460.713h.006c.01-.121.1-.146.1-.146.124-.048.187.073.187.073v-.003c1.256 1.809 6.897 10.181 8.052 15.279 0 0 .616 2.47.015 4.14 0 0-.85 3.185-4.313 4.025 0 0-.998.257-2.055.405 0 0-3.869-4.982-1.992-23.773" fill="url(#e)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/><path d="m53.509 456.931s-.116-.016-.147-.102c0 0-.03-.114.052-.172v-.002c1.245-.898 5.032-3.546 6.86-4.061 0 0 3.375-1.15 6.304 3.884l-13.069.457z" fill="url(#d)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/><path d="m53.219 458.198v-.001s-.097-.066-.064-.19c0 0 .055-.097.132-.097v-.005c2.053 0 9.831-.008 10.03.026 0 0 1.004.039 2.246.516 0 0 2.766.882 4.2 4.023 0 0 1.282 2.563.292 5.555 0 0-11.1-5.953-16.836-9.827" fill="url(#c)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/><path d="m52.133 459.731h.006s-.064-.127.033-.209c0 0 .09-.068.176-.015 1.872.93 9.652 4.887 12.875 8.029 0 0 2.041 1.639 2.15 4.27.235 3.658-3.441 7.043-3.441 7.043s-6.813-8.244-11.799-19.118" fill="url(#b)" transform="matrix(7.01337 0 0 -7.01337 -195.092 3398.88)"/></svg>