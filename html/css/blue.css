.navbar-default {
  background-color: #d6e7ff;
  border-color: #0052cc;
}

.widget_header {
  background-color: #0052cc;
}

.navbar-default .navbar-nav .open a:focus,
.navbar-default .navbar-nav .open a:hover {
  background-color: #0052cc44;
}
.navbar-default .navbar-nav .open a{
	  background-color: #0052cc44;
}
.navbar-default .navbar-nav .dropdown-menu li a {
       color: #0052cc;
       background-color: transparent;
}
.dropdown-menu li a {
	 background-color: transparent;
}
.fa-nav-icons {
    color: #0052cc;
}

.fa-col-success {
    color: #3c763d;
}

.fa-col-info {
    color: #31708f;
}

.fa-col-primary {
    color: #357ebd;
}

.fa-col-danger {
    color: #e30613;
}

.icon-theme {
    color: #0052cc;
}

.panel-default>.panel-heading {
  background: #0052cc22;
  color: #0052cccc;
}

.twitter-typeahead .tt-hint {
    border-color: #000 !important;
}

.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.pace-inactive {
  display: none;
}

.pace .pace-progress {
  background: #fff;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 100%;
  width: 100%;
  height: 2px;
}

.pace .pace-progress-inner {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #d6cdbe, 0 0 5px #d6cdbe;
  opacity: 1.0;
  -webkit-transform: rotate(3deg) translate(0px, -4px);
  -moz-transform: rotate(3deg) translate(0px, -4px);
  -ms-transform: rotate(3deg) translate(0px, -4px);
  -o-transform: rotate(3deg) translate(0px, -4px);
  transform: rotate(3deg) translate(0px, -4px);
}

.pace .pace-activity {
  display: block;
  position: fixed;
  z-index: 2000;
  top: 16px;
  right: 45px;
  width: 20px;
  height: 20px;
  border: solid 2px transparent;
  border-top-color: #9a968f;
  border-left-color: #9a968f;
  border-radius: 10px;
  -webkit-animation: pace-spinner 400ms linear infinite;
  -moz-animation: pace-spinner 400ms linear infinite;
  -ms-animation: pace-spinner 400ms linear infinite;
  -o-animation: pace-spinner 400ms linear infinite;
  animation: pace-spinner 400ms linear infinite;
}

@-webkit-keyframes pace-spinner {
  0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }
}
@-moz-keyframes pace-spinner {
  0% { -moz-transform: rotate(0deg); transform: rotate(0deg); }
  100% { -moz-transform: rotate(360deg); transform: rotate(360deg); }
}
@-o-keyframes pace-spinner {
  0% { -o-transform: rotate(0deg); transform: rotate(0deg); }
  100% { -o-transform: rotate(360deg); transform: rotate(360deg); }
}
@-ms-keyframes pace-spinner {
  0% { -ms-transform: rotate(0deg); transform: rotate(0deg); }
  100% { -ms-transform: rotate(360deg); transform: rotate(360deg); }
}
@keyframes pace-spinner {
  0% { transform: rotate(0deg); transform: rotate(0deg); }
  100% { transform: rotate(360deg); transform: rotate(360deg); }
}

.pagemenu-selected {
  background-color: white;
  border: 0;
  box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
}

.dropdown-menu li a:focus, 
.dropdown-menu li a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #0052cc;
}
