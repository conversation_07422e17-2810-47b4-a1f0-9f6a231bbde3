/** qooxdoo v5.0.2 | (c) 2015 1&1 Internet AG, http://1und1.de | http://qooxdoo.org/license */
(function(){
if (!window.qx) window.qx = {};
var qx = window.qx;

if (!qx.$$environment) qx.$$environment = {};
var envinfo = {"json":true,"qx.application":"library.Application","qx.debug":false,"qx.debug.databinding":false,"qx.debug.dispose":false,"qx.debug.io":false,"qx.debug.ui.queue":false,"qx.globalErrorHandling":false,"qx.optimization.basecalls":true,"qx.optimization.comments":true,"qx.optimization.statics":true,"qx.optimization.strings":true,"qx.optimization.variables":true,"qx.optimization.variants":true,"qx.optimization.whitespace":true,"qx.revision":"","qx.theme":"qx.theme.Modern","qx.version":"5.0.2"};
for (var k in envinfo) qx.$$environment[k] = envinfo[k];

qx.$$packageData = {};

/** qooxdoo v5.0.2 | (c) 2015 1&1 Internet AG, http://1und1.de | http://qooxdoo.org/license */
qx.$$packageData['0']={"locales":{},"resources":{},"translations":{"C":{},"en":{}}};
(function(){var b=".prototype",c="function",d="Boolean",e="Error",f="Object.keys requires an object as argument.",g="constructor",h="warn",j="default",k="Null",m="hasOwnProperty",n="Undefined",o="string",p="Object",q="toLocaleString",r="error",s="toString",t="qx.debug",u="()",v="RegExp",w="String",x="info",y="BROKEN_IE",z="isPrototypeOf",A="Date",B="",C="qx.Bootstrap",D="Function",E="]",F="Cannot call super class. Method is not derived: ",G="Array",H="[Class ",I="valueOf",J="Number",K="Class",L="debug",M="ES5",N=".",O="propertyIsEnumerable",P="object";if(!window.qx){window.qx={};};qx.Bootstrap={genericToString:function(){return H+this.classname+E;},createNamespace:function(name,Q){var T=name.split(N);var S=T[0];var parent=qx.$$namespaceRoot&&qx.$$namespaceRoot[S]?qx.$$namespaceRoot:window;for(var i=0,R=T.length-1;i<R;i++ ,S=T[i]){if(!parent[S]){parent=parent[S]={};}else {parent=parent[S];};};parent[S]=Q;return S;},setDisplayName:function(V,U,name){V.displayName=U+N+name+u;},setDisplayNames:function(X,W){for(var name in X){var Y=X[name];if(Y instanceof Function){Y.displayName=W+N+name+u;};};},base:function(ba,bb){if(qx.Bootstrap.DEBUG){if(!qx.Bootstrap.isFunction(ba.callee.base)){throw new Error(F+ba.callee.displayName);};};if(arguments.length===1){return ba.callee.base.call(this);}else {return ba.callee.base.apply(this,Array.prototype.slice.call(arguments,1));};},define:function(name,bm){if(!bm){bm={statics:{}};};var bi;var be=null;qx.Bootstrap.setDisplayNames(bm.statics,name);if(bm.members||bm.extend){qx.Bootstrap.setDisplayNames(bm.members,name+b);bi=bm.construct||new Function;if(bm.extend){this.extendClass(bi,bi,bm.extend,name,bg);};var bd=bm.statics||{};for(var i=0,bf=qx.Bootstrap.keys(bd),l=bf.length;i<l;i++ ){var bc=bf[i];bi[bc]=bd[bc];};be=bi.prototype;be.base=qx.Bootstrap.base;be.name=be.classname=name;var bk=bm.members||{};var bc,bj;for(var i=0,bf=qx.Bootstrap.keys(bk),l=bf.length;i<l;i++ ){bc=bf[i];bj=bk[bc];if(bj instanceof Function&&be[bc]){bj.base=be[bc];};be[bc]=bj;};}else {bi=bm.statics||{};if(qx.Bootstrap.$$registry&&qx.Bootstrap.$$registry[name]){var bl=qx.Bootstrap.$$registry[name];if(this.keys(bi).length!==0){if(bm.defer){bm.defer(bi,be);};for(var bh in bi){bl[bh]=bi[bh];};return bl;};};};bi.$$type=K;if(!bi.hasOwnProperty(s)){bi.toString=this.genericToString;};var bg=name?this.createNamespace(name,bi):B;bi.name=bi.classname=name;bi.basename=bg;bi.$$events=bm.events;if(bm.defer){bm.defer(bi,be);};if(name!=null){qx.Bootstrap.$$registry[name]=bi;};return bi;}};qx.Bootstrap.define(C,{statics:{DEBUG:(function(){var bn=true;if(qx.$$environment&&qx.$$environment[t]===false){bn=false;};return bn;})(),createNamespace:qx.Bootstrap.createNamespace,base:qx.Bootstrap.base,define:qx.Bootstrap.define,setDisplayName:qx.Bootstrap.setDisplayName,setDisplayNames:qx.Bootstrap.setDisplayNames,genericToString:qx.Bootstrap.genericToString,extendClass:function(clazz,construct,superClass,name,basename){var superproto=superClass.prototype;var helper=new Function();helper.prototype=superproto;var proto=new helper();clazz.prototype=proto;proto.name=proto.classname=name;proto.basename=basename;construct.base=superClass;clazz.superclass=superClass;construct.self=clazz.constructor=proto.constructor=clazz;},getByName:function(name){return qx.Bootstrap.$$registry[name];},$$registry:{},objectMergeWith:function(bp,bo,br){if(br===undefined){br=true;};for(var bq in bo){if(br||bp[bq]===undefined){bp[bq]=bo[bq];};};return bp;},__shadowedKeys:[z,m,q,s,I,O,g],keys:({"ES5":Object.keys,"BROKEN_IE":function(bs){if(bs===null||(typeof bs!=P&&typeof bs!=c)){throw new TypeError(f);};var bt=[];var bv=Object.prototype.hasOwnProperty;for(var bw in bs){if(bv.call(bs,bw)){bt.push(bw);};};var bu=qx.Bootstrap.__shadowedKeys;for(var i=0,a=bu,l=a.length;i<l;i++ ){if(bv.call(bs,a[i])){bt.push(a[i]);};};return bt;},"default":function(bx){if(bx===null||(typeof bx!=P&&typeof bx!=c)){throw new TypeError(f);};var by=[];var bz=Object.prototype.hasOwnProperty;for(var bA in bx){if(bz.call(bx,bA)){by.push(bA);};};return by;}})[typeof (Object.keys)==c?M:(function(){for(var bB in {toString:1}){return bB;};})()!==s?y:j],__classToTypeMap:{"[object String]":w,"[object Array]":G,"[object Object]":p,"[object RegExp]":v,"[object Number]":J,"[object Boolean]":d,"[object Date]":A,"[object Function]":D,"[object Error]":e},bind:function(bD,self,bE){var bC=Array.prototype.slice.call(arguments,2,arguments.length);return function(){var bF=Array.prototype.slice.call(arguments,0,arguments.length);return bD.apply(self,bC.concat(bF));};},firstUp:function(bG){return bG.charAt(0).toUpperCase()+bG.substr(1);},firstLow:function(bH){return bH.charAt(0).toLowerCase()+bH.substr(1);},getClass:function(bJ){if(bJ===undefined){return n;}else if(bJ===null){return k;};var bI=Object.prototype.toString.call(bJ);return (qx.Bootstrap.__classToTypeMap[bI]||bI.slice(8,-1));},isString:function(bK){return (bK!==null&&(typeof bK===o||qx.Bootstrap.getClass(bK)==w||bK instanceof String||(!!bK&&!!bK.$$isString)));},isArray:function(bL){return (bL!==null&&(bL instanceof Array||(bL&&qx.data&&qx.data.IListData&&qx.util.OOUtil.hasInterface(bL.constructor,qx.data.IListData))||qx.Bootstrap.getClass(bL)==G||(!!bL&&!!bL.$$isArray)));},isObject:function(bM){return (bM!==undefined&&bM!==null&&qx.Bootstrap.getClass(bM)==p);},isFunction:function(bN){return qx.Bootstrap.getClass(bN)==D;},$$logs:[],debug:function(bP,bO){qx.Bootstrap.$$logs.push([L,arguments]);},info:function(bR,bQ){qx.Bootstrap.$$logs.push([x,arguments]);},warn:function(bT,bS){qx.Bootstrap.$$logs.push([h,arguments]);},error:function(bV,bU){qx.Bootstrap.$$logs.push([r,arguments]);},trace:function(bW){}}});})();(function(){var a="qx.util.OOUtil";qx.Bootstrap.define(a,{statics:{getByInterface:function(d,b){var c,i,l;while(d){if(d.$$implements){c=d.$$flatImplements;for(i=0,l=c.length;i<l;i++ ){if(c[i]===b){return d;};};};d=d.superclass;};return null;},hasInterface:function(f,e){return !!qx.util.OOUtil.getByInterface(f,e);}}});})();(function(){var a="qx.core.Environment",b="default",c=' type)',d="&",e="qx/static/blank.html",f="true",g="|",h="qx.core.Environment for a list of predefined keys.",j="false",k='] found, and no default ("default") given',l=":",m='" (',n=' in variants [',o=".",p="qx.allowUrlSettings",q='No match for variant "',r=" is not a valid key. Please see the API-doc of ",s="qxenv";qx.Bootstrap.define(a,{statics:{_checks:{},_asyncChecks:{},__cache:{},_checksMap:{},_defaults:{"true":true,"qx.allowUrlSettings":false,"qx.allowUrlVariants":false,"qx.debug.property.level":0,"qx.debug":true,"qx.debug.ui.queue":true,"qx.aspects":false,"qx.dynlocale":true,"qx.dyntheme":true,"qx.blankpage":e,"qx.debug.databinding":false,"qx.debug.dispose":false,"qx.optimization.basecalls":false,"qx.optimization.comments":false,"qx.optimization.privates":false,"qx.optimization.strings":false,"qx.optimization.variables":false,"qx.optimization.variants":false,"module.databinding":true,"module.logger":true,"module.property":true,"module.events":true,"qx.nativeScrollBars":false},get:function(w){if(this.__cache[w]!=undefined){return this.__cache[w];};var y=this._checks[w];if(y){var u=y();this.__cache[w]=u;return u;};var t=this._getClassNameFromEnvKey(w);if(t[0]!=undefined){var x=t[0];var v=t[1];var u=x[v]();this.__cache[w]=u;return u;};if(qx.Bootstrap.DEBUG){qx.Bootstrap.warn(w+r+h);qx.Bootstrap.trace(this);};},_getClassNameFromEnvKey:function(D){var F=this._checksMap;if(F[D]!=undefined){var A=F[D];var E=A.lastIndexOf(o);if(E>-1){var C=A.slice(0,E);var z=A.slice(E+1);var B=qx.Bootstrap.getByName(C);if(B!=undefined){return [B,z];};};};return [undefined,undefined];},select:function(H,G){return this.__pickFromValues(this.get(H),G);},__pickFromValues:function(L,K){var J=K[L];if(K.hasOwnProperty(L)){return J;};for(var M in K){if(M.indexOf(g)!=-1){var I=M.split(g);for(var i=0;i<I.length;i++ ){if(I[i]==L){return K[M];};};};};if(K[b]!==undefined){return K[b];};if(qx.Bootstrap.DEBUG){throw new Error(q+L+m+(typeof L)+c+n+qx.Bootstrap.keys(K)+k);};},add:function(O,N){if(this._checks[O]==undefined){if(N instanceof Function){if(!this._checksMap[O]&&N.displayName){this._checksMap[O]=N.displayName.substr(0,N.displayName.length-2);};this._checks[O]=N;}else {this._checks[O]=this.__createCheck(N);};};},addAsync:function(Q,P){if(this._checks[Q]==undefined){this._asyncChecks[Q]=P;};},_initDefaultQxValues:function(){var R=function(T){return function(){return T;};};for(var S in this._defaults){this.add(S,R(this._defaults[S]));};},__importFromGenerator:function(){if(qx&&qx.$$environment){for(var U in qx.$$environment){var V=qx.$$environment[U];this._checks[U]=this.__createCheck(V);};};},__importFromUrl:function(){if(window.document&&window.document.location){var W=window.document.location.search.slice(1).split(d);for(var i=0;i<W.length;i++ ){var ba=W[i].split(l);if(ba.length!=3||ba[0]!=s){continue;};var X=ba[1];var Y=decodeURIComponent(ba[2]);if(Y==f){Y=true;}else if(Y==j){Y=false;}else if(/^(\d|\.)+$/.test(Y)){Y=parseFloat(Y);};this._checks[X]=this.__createCheck(Y);};};},__createCheck:function(bb){return qx.Bootstrap.bind(function(bc){return bc;},null,bb);}},defer:function(bd){bd._initDefaultQxValues();bd.__importFromGenerator();if(bd.get(p)===true){bd.__importFromUrl();};}});})();(function(){var a="[object Opera]",b="function",c="[^\\.0-9]",d="4.0",e="gecko",f="1.9.0.0",g="Version/",h="9.0",i="8.0",j="engine.version",k="Gecko",l="Maple",m="AppleWebKit/",n="Trident",o="Unsupported client: ",p="",q="opera",r="Windows Phone",s="! Assumed gecko version 1.9.0.0 (Firefox 3.0).",t="mshtml",u="engine.name",v="webkit",w="5.0",x=".",y="qx.bom.client.Engine";qx.Bootstrap.define(y,{statics:{getVersion:function(){var B=window.navigator.userAgent;var C=p;if(qx.bom.client.Engine.__isMshtml()){var A=/Trident\/([^\);]+)(\)|;)/.test(B);if(/MSIE\s+([^\);]+)(\)|;)/.test(B)){C=RegExp.$1;if(C<8&&A){if(RegExp.$1==d){C=i;}else if(RegExp.$1==w){C=h;};};}else if(A){var E=/\brv\:(\d+?\.\d+?)\b/.exec(B);if(E){C=E[1];};};}else if(qx.bom.client.Engine.__isOpera()){if(/Opera[\s\/]([0-9]+)\.([0-9])([0-9]*)/.test(B)){if(B.indexOf(g)!=-1){var E=B.match(/Version\/(\d+)\.(\d+)/);C=E[1]+x+E[2].charAt(0)+x+E[2].substring(1,E[2].length);}else {C=RegExp.$1+x+RegExp.$2;if(RegExp.$3!=p){C+=x+RegExp.$3;};};};}else if(qx.bom.client.Engine.__isWebkit()){if(/AppleWebKit\/([^ ]+)/.test(B)){C=RegExp.$1;var D=RegExp(c).exec(C);if(D){C=C.slice(0,D.index);};};}else if(qx.bom.client.Engine.__isGecko()||qx.bom.client.Engine.__isMaple()){if(/rv\:([^\);]+)(\)|;)/.test(B)){C=RegExp.$1;};}else {var z=window.qxFail;if(z&&typeof z===b){C=z().FULLVERSION;}else {C=f;qx.Bootstrap.warn(o+B+s);};};return C;},getName:function(){var name;if(qx.bom.client.Engine.__isMshtml()){name=t;}else if(qx.bom.client.Engine.__isOpera()){name=q;}else if(qx.bom.client.Engine.__isWebkit()){name=v;}else if(qx.bom.client.Engine.__isGecko()||qx.bom.client.Engine.__isMaple()){name=e;}else {var F=window.qxFail;if(F&&typeof F===b){name=F().NAME;}else {name=e;qx.Bootstrap.warn(o+window.navigator.userAgent+s);};};return name;},__isOpera:function(){return window.opera&&Object.prototype.toString.call(window.opera)==a;},__isWebkit:function(){return window.navigator.userAgent.indexOf(m)!=-1;},__isMaple:function(){return window.navigator.userAgent.indexOf(l)!=-1;},__isGecko:function(){return (window.navigator.mozApps||window.navigator.buildID)&&window.navigator.product===k&&window.navigator.userAgent.indexOf(l)==-1&&window.navigator.userAgent.indexOf(n)==-1;},__isMshtml:function(){if(window.navigator.cpuClass&&(/MSIE\s+([^\);]+)(\)|;)/.test(window.navigator.userAgent)||/Trident\/\d+?\.\d+?/.test(window.navigator.userAgent))){return true;};if(qx.bom.client.Engine.__isWindowsPhone()){return true;};return false;},__isWindowsPhone:function(){return window.navigator.userAgent.indexOf(r)>-1;}},defer:function(G){qx.core.Environment.add(j,G.getVersion);qx.core.Environment.add(u,G.getName);}});})();(function(){var a="ecmascript.array.lastindexof",b="function",c="stack",d="ecmascript.array.map",f="ecmascript.date.now",g="ecmascript.array.reduce",h="e",i="qx.bom.client.EcmaScript",j="ecmascript.object.keys",k="ecmascript.error.stacktrace",l="ecmascript.string.trim",m="ecmascript.array.indexof",n="stacktrace",o="ecmascript.error.toString",p="[object Error]",q="ecmascript.array.foreach",r="ecmascript.function.bind",s="ecmascript.array.reduceright",t="ecmascript.array.some",u="ecmascript.array.filter",v="ecmascript.array.every";qx.Bootstrap.define(i,{statics:{getStackTrace:function(){var w;var e=new Error(h);w=e.stack?c:e.stacktrace?n:null;if(!w){try{throw e;}catch(x){e=x;};};return e.stacktrace?n:e.stack?c:null;},getArrayIndexOf:function(){return !!Array.prototype.indexOf;},getArrayLastIndexOf:function(){return !!Array.prototype.lastIndexOf;},getArrayForEach:function(){return !!Array.prototype.forEach;},getArrayFilter:function(){return !!Array.prototype.filter;},getArrayMap:function(){return !!Array.prototype.map;},getArraySome:function(){return !!Array.prototype.some;},getArrayEvery:function(){return !!Array.prototype.every;},getArrayReduce:function(){return !!Array.prototype.reduce;},getArrayReduceRight:function(){return !!Array.prototype.reduceRight;},getErrorToString:function(){return typeof Error.prototype.toString==b&&Error.prototype.toString()!==p;},getFunctionBind:function(){return typeof Function.prototype.bind===b;},getObjectKeys:function(){return !!Object.keys;},getDateNow:function(){return !!Date.now;},getStringTrim:function(){return typeof String.prototype.trim===b;}},defer:function(y){qx.core.Environment.add(m,y.getArrayIndexOf);qx.core.Environment.add(a,y.getArrayLastIndexOf);qx.core.Environment.add(q,y.getArrayForEach);qx.core.Environment.add(u,y.getArrayFilter);qx.core.Environment.add(d,y.getArrayMap);qx.core.Environment.add(t,y.getArraySome);qx.core.Environment.add(v,y.getArrayEvery);qx.core.Environment.add(g,y.getArrayReduce);qx.core.Environment.add(s,y.getArrayReduceRight);qx.core.Environment.add(f,y.getDateNow);qx.core.Environment.add(o,y.getErrorToString);qx.core.Environment.add(k,y.getStackTrace);qx.core.Environment.add(r,y.getFunctionBind);qx.core.Environment.add(j,y.getObjectKeys);qx.core.Environment.add(l,y.getStringTrim);}});})();(function(){var a="function",b="ecmascript.array.lastindexof",c="ecmascript.array.map",d="ecmascript.array.filter",e="Length is 0 and no second argument given",f="qx.lang.normalize.Array",g="ecmascript.array.indexof",h="First argument is not callable",j="ecmascript.array.reduce",k="ecmascript.array.foreach",m="ecmascript.array.reduceright",n="ecmascript.array.some",o="ecmascript.array.every";qx.Bootstrap.define(f,{statics:{indexOf:function(p,q){if(q==null){q=0;}else if(q<0){q=Math.max(0,this.length+q);};for(var i=q;i<this.length;i++ ){if(this[i]===p){return i;};};return -1;},lastIndexOf:function(r,s){if(s==null){s=this.length-1;}else if(s<0){s=Math.max(0,this.length+s);};for(var i=s;i>=0;i-- ){if(this[i]===r){return i;};};return -1;},forEach:function(t,u){var l=this.length;for(var i=0;i<l;i++ ){var v=this[i];if(v!==undefined){t.call(u||window,v,i,this);};};},filter:function(z,w){var x=[];var l=this.length;for(var i=0;i<l;i++ ){var y=this[i];if(y!==undefined){if(z.call(w||window,y,i,this)){x.push(this[i]);};};};return x;},map:function(D,A){var B=[];var l=this.length;for(var i=0;i<l;i++ ){var C=this[i];if(C!==undefined){B[i]=D.call(A||window,C,i,this);};};return B;},some:function(E,F){var l=this.length;for(var i=0;i<l;i++ ){var G=this[i];if(G!==undefined){if(E.call(F||window,G,i,this)){return true;};};};return false;},every:function(H,I){var l=this.length;for(var i=0;i<l;i++ ){var J=this[i];if(J!==undefined){if(!H.call(I||window,J,i,this)){return false;};};};return true;},reduce:function(K,L){if(typeof K!==a){throw new TypeError(h);};if(L===undefined&&this.length===0){throw new TypeError(e);};var M=L===undefined?this[0]:L;for(var i=L===undefined?1:0;i<this.length;i++ ){if(i in this){M=K.call(undefined,M,this[i],i,this);};};return M;},reduceRight:function(N,O){if(typeof N!==a){throw new TypeError(h);};if(O===undefined&&this.length===0){throw new TypeError(e);};var P=O===undefined?this[this.length-1]:O;for(var i=O===undefined?this.length-2:this.length-1;i>=0;i-- ){if(i in this){P=N.call(undefined,P,this[i],i,this);};};return P;}},defer:function(Q){if(!qx.core.Environment.get(g)){Array.prototype.indexOf=Q.indexOf;};if(!qx.core.Environment.get(b)){Array.prototype.lastIndexOf=Q.lastIndexOf;};if(!qx.core.Environment.get(k)){Array.prototype.forEach=Q.forEach;};if(!qx.core.Environment.get(d)){Array.prototype.filter=Q.filter;};if(!qx.core.Environment.get(c)){Array.prototype.map=Q.map;};if(!qx.core.Environment.get(n)){Array.prototype.some=Q.some;};if(!qx.core.Environment.get(o)){Array.prototype.every=Q.every;};if(!qx.core.Environment.get(j)){Array.prototype.reduce=Q.reduce;};if(!qx.core.Environment.get(m)){Array.prototype.reduceRight=Q.reduceRight;};}});})();(function(){var a="mshtml",b="engine.name",c="pop.push.reverse.shift.sort.splice.unshift.join.slice",d="number",e="qx.type.BaseArray",f=".";qx.Bootstrap.define(e,{extend:Array,construct:function(g){},members:{toArray:null,valueOf:null,pop:null,push:null,reverse:null,shift:null,sort:null,splice:null,unshift:null,concat:null,join:null,slice:null,toString:null,indexOf:null,lastIndexOf:null,forEach:null,filter:null,map:null,some:null,every:null}});(function(){function h(p){if((qx.core.Environment.get(b)==a)){j.prototype={length:0,$$isArray:true};var n=c.split(f);for(var length=n.length;length;){j.prototype[n[ --length]]=Array.prototype[n[length]];};};var m=Array.prototype.slice;j.prototype.concat=function(){var r=this.slice(0);for(var i=0,length=arguments.length;i<length;i++ ){var q;if(arguments[i] instanceof j){q=m.call(arguments[i],0);}else if(arguments[i] instanceof Array){q=arguments[i];}else {q=[arguments[i]];};r.push.apply(r,q);};return r;};j.prototype.toString=function(){return m.call(this,0).toString();};j.prototype.toLocaleString=function(){return m.call(this,0).toLocaleString();};j.prototype.constructor=j;j.prototype.indexOf=Array.prototype.indexOf;j.prototype.lastIndexOf=Array.prototype.lastIndexOf;j.prototype.forEach=Array.prototype.forEach;j.prototype.some=Array.prototype.some;j.prototype.every=Array.prototype.every;var o=Array.prototype.filter;var l=Array.prototype.map;j.prototype.filter=function(){var s=new this.constructor;s.push.apply(s,o.apply(this,arguments));return s;};j.prototype.map=function(){var t=new this.constructor;t.push.apply(t,l.apply(this,arguments));return t;};j.prototype.slice=function(){var u=new this.constructor;u.push.apply(u,Array.prototype.slice.apply(this,arguments));return u;};j.prototype.splice=function(){var v=new this.constructor;v.push.apply(v,Array.prototype.splice.apply(this,arguments));return v;};j.prototype.toArray=function(){return Array.prototype.slice.call(this,0);};j.prototype.valueOf=function(){return this.length;};return j;};function j(length){if(arguments.length===1&&typeof length===d){this.length=-1<length&&length===length>>.5?length:this.push(length);}else if(arguments.length){this.push.apply(this,arguments);};};function k(){};k.prototype=[];j.prototype=new k;j.prototype.length=0;qx.type.BaseArray=h(j);})();})();(function(){var a="name",b="qxWeb",c="toString",d="$",e="number",f="_",g="data-qx-class",h="basename",j="classname";qx.Bootstrap.define(b,{extend:qx.type.BaseArray,statics:{__init:[],$$qx:qx,$init:function(p,n){if(p.length&&p.length==1&&p[0]&&p[0].$widget instanceof qxWeb){return p[0].$widget;};var o=[];for(var i=0;i<p.length;i++ ){var m=!!(p[i]&&(p[i].nodeType===1||p[i].nodeType===9||p[i].nodeType===11));if(m){o.push(p[i]);continue;};var k=!!(p[i]&&p[i].history&&p[i].location&&p[i].document);if(k){o.push(p[i]);};};if(p[0]&&p[0].getAttribute&&p[0].getAttribute(g)&&o.length<2){n=qx.Bootstrap.getByName(p[0].getAttribute(g))||n;};var r=qx.lang.Array.cast(o,n);for(var i=0;i<qxWeb.__init.length;i++ ){qxWeb.__init[i].call(r);};return r;},$attach:function(t,s){for(var name in t){if(qxWeb.prototype[name]!=undefined&&Array.prototype[name]==undefined&&s!==true){{};}else {qxWeb.prototype[name]=t[name];};};},$attachStatic:function(v,u){for(var name in v){{};qxWeb[name]=v[name];};},$attachAll:function(y,x){for(var name in y.members){if(name.indexOf(d)!==0&&name.indexOf(f)!==0)qxWeb.prototype[name]=y.members[name];};var w;if(x!=null){qxWeb[x]=qxWeb[x]||{};w=qxWeb[x];}else {w=qxWeb;};for(var name in y.statics){if(name.indexOf(d)!==0&&name.indexOf(f)!==0&&name!==a&&name!==h&&name!==j&&name!==c&&name!==name.toUpperCase())w[name]=y.statics[name];};},$attachInit:function(z){this.__init.push(z);},define:function(name,A){if(A==undefined){A=name;name=null;};return qx.Bootstrap.define.call(qx.Bootstrap,name,A);}},construct:function(C,B){if(!C&&this instanceof qxWeb){return this;};if(!C){C=[];}else if(qx.Bootstrap.isString(C)){if(B instanceof qxWeb&&B.length!=0){B=B[0];};if(B instanceof qxWeb){C=[];}else {C=qx.bom.Selector.query(C,B);};}else if((C.nodeType===1||C.nodeType===9||C.nodeType===11)||(C.history&&C.location&&C.document)){C=[C];};return qxWeb.$init(C,qxWeb);},members:{filter:function(D){if(qx.lang.Type.isFunction(D)){return qxWeb.$init(Array.prototype.filter.call(this,D),this.constructor);};return qxWeb.$init(qx.bom.Selector.matches(D,this),this.constructor);},unique:function(){var E=qx.lang.Array.unique(this);return qxWeb.$init(E,this.constructor);},slice:function(F,G){if(G!==undefined){return qxWeb.$init(Array.prototype.slice.call(this,F,G),this.constructor);};return qxWeb.$init(Array.prototype.slice.call(this,F),this.constructor);},splice:function(H,I,J){return qxWeb.$init(Array.prototype.splice.apply(this,arguments),this.constructor);},map:function(K,L){return qxWeb.$init(Array.prototype.map.apply(this,arguments),qxWeb);},concat:function(N){var M=Array.prototype.slice.call(this,0);for(var i=0;i<arguments.length;i++ ){if(arguments[i] instanceof qxWeb){M=M.concat(Array.prototype.slice.call(arguments[i],0));}else {M.push(arguments[i]);};};return qxWeb.$init(M,this.constructor);},indexOf:function(O,P){if(!O){return -1;};if(!P){P=0;};if(typeof P!==e){return -1;};if(P<0){P=this.length+P;if(P<0){P=0;};};if(qx.lang.Type.isArray(O)){O=O[0];};for(var i=P,l=this.length;i<l;i++ ){if(this[i]===O){return i;};};return -1;},debug:function(){{};return this;},logThis:function(){{var Q,length;};return this;},_forEachElement:function(S,R){for(var i=0,l=this.length;i<l;i++ ){if(this[i]&&(this[i].nodeType===1||this[i].nodeType===11)){S.apply(R||this,[this[i],i,this]);};};return this;},_forEachElementWrapped:function(U,T){this._forEachElement(function(V,X,W){U.apply(this,[qxWeb(V),X,W]);},T);return this;}},defer:function(Y){if(window.q==undefined){q=Y;};}});})();(function(){var a="qx.lang.normalize.Date",b="ecmascript.date.now";qx.Bootstrap.define(a,{statics:{now:function(){return +new Date();}},defer:function(c){if(!qx.core.Environment.get(b)){Date.now=c.now;};}});})();(function(){var a="mshtml",b="engine.name",c="[object Array]",d="qx.lang.Array",e="Cannot clean-up map entry doneObjects[",f="]",g="qx",h="number",j="][",k="string";qx.Bootstrap.define(d,{statics:{cast:function(m,o,p){if(m.constructor===o){return m;};if(qx.data&&qx.data.IListData){if(qx.Class&&qx.Class.hasInterface(m,qx.data.IListData)){var m=m.toArray();};};var n=new o;if((qx.core.Environment.get(b)==a)){if(m.item){for(var i=p||0,l=m.length;i<l;i++ ){n.push(m[i]);};return n;};};if(Object.prototype.toString.call(m)===c&&p==null){n.push.apply(n,m);}else {n.push.apply(n,Array.prototype.slice.call(m,p||0));};return n;},fromArguments:function(q,r){return Array.prototype.slice.call(q,r||0);},fromCollection:function(t){if((qx.core.Environment.get(b)==a)){if(t.item){var s=[];for(var i=0,l=t.length;i<l;i++ ){s[i]=t[i];};return s;};};return Array.prototype.slice.call(t,0);},insertBefore:function(u,w,v){var i=u.indexOf(v);if(i==-1){u.push(w);}else {u.splice(i,0,w);};return u;},insertAfter:function(x,z,y){var i=x.indexOf(y);if(i==-1||i==(x.length-1)){x.push(z);}else {x.splice(i+1,0,z);};return x;},removeAll:function(A){A.length=0;return this;},exclude:function(D,C){{};for(var i=0,E=C.length,B;i<E;i++ ){B=D.indexOf(C[i]);if(B!=-1){D.splice(B,1);};};return D;},remove:function(F,G){var i=F.indexOf(G);if(i!=-1){F.splice(i,1);return G;};},contains:function(H,I){return H.indexOf(I)!==-1;},equals:function(K,J){var length=K.length;if(length!==J.length){return false;};for(var i=0;i<length;i++ ){if(K[i]!==J[i]){return false;};};return true;},sum:function(L){var M=0;for(var i=0,l=L.length;i<l;i++ ){if(L[i]!=undefined){M+=L[i];};};return M;},max:function(N){{};var i,P=N.length,O=N[0];for(i=1;i<P;i++ ){if(N[i]>O){O=N[i];};};return O===undefined?null:O;},min:function(Q){{};var i,S=Q.length,R=Q[0];for(i=1;i<S;i++ ){if(Q[i]<R){R=Q[i];};};return R===undefined?null:R;},unique:function(V){var bg=[],U={},Y={},bb={};var ba,T=0;var be=g+Date.now();var W=false,bc=false,bf=false;for(var i=0,bd=V.length;i<bd;i++ ){ba=V[i];if(ba===null){if(!W){W=true;bg.push(ba);};}else if(ba===undefined){}else if(ba===false){if(!bc){bc=true;bg.push(ba);};}else if(ba===true){if(!bf){bf=true;bg.push(ba);};}else if(typeof ba===k){if(!U[ba]){U[ba]=1;bg.push(ba);};}else if(typeof ba===h){if(!Y[ba]){Y[ba]=1;bg.push(ba);};}else {var X=ba[be];if(X==null){X=ba[be]=T++ ;};if(!bb[X]){bb[X]=ba;bg.push(ba);};};};for(var X in bb){try{delete bb[X][be];}catch(bh){try{bb[X][be]=null;}catch(bi){throw new Error(e+X+j+be+f);};};};return bg;},range:function(bl,stop,bm){if(arguments.length<=1){stop=bl||0;bl=0;};bm=arguments[2]||1;var length=Math.max(Math.ceil((stop-bl)/bm),0);var bj=0;var bk=Array(length);while(bj<length){bk[bj++ ]=bl;bl+=bm;};return bk;}}});})();(function(){var c="-",d="(^|",f="'] ",g="CLASS",h=":disabled",k="div",l="input",n="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",o="nth",p="*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|(",q="type|href|height|width",r=")*)|.*)\\)|)",s="disabled",t="*(?:value|",u="~=",v="previousSibling",w="*(even|odd|(([+-]|)(\\d*)n|)",x="xml:lang",y="only",z="*",A="unsupported lang: ",B="+|((?:^|[^\\\\])(?:\\\\.)*)",C="i",D="\\\\([\\da-f]{1,6}",E="='$1']",F="w#",G="^=",H="*([>+~]|",I="[t^='']",J="*\\)|)",K="+$",L="=",M="unload",N="id",O="text",P="needsContext",Q="nextSibling",R="$=",S="[s!='']:x",T="string",U=")|.)",V="[\\x20\\t\\r\\n\\f]",W="[name=d]",X="*(?:([+-]|)",Y="*((?:-\\d)?\\d*)",cL="#",cM="[selected]",cN="type",cH="ig",cI="parentNode",cJ="href",cK="0x",cS="(",cT="w",cY="even",cU="<div class='a'></div><div class='a i'></div>",cO="g",cP="*\\]",cQ="*\\)|)(?=[^-]|$)",cR="unsupported pseudo: ",dC="w*",eo="*[*^$|!~]?=",da="<select t=''><option selected=''></option></select>",cV=" ",cW="hidden",el="*(?:([*^$|!~]?=)",cX="*,",db="function",dc="^",dd=")",di=")|)|)",dj=":(",dk="onunload",de="button",df="0",dg="^(",dh="option",dq="odd",dr="class",ds="*(\\d+)|))",dt="lang",dl="|=",dm="\\[",dn="name",dp="D",dx="!=",dy="<input/>",en="*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\(",dz="sizzle",du="*=",dv="|",em="Syntax error, unrecognized expression: ",dw=")$",dA="object",dB="?|(",dN="$1",dM=")(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|",dL="*([^\\]'\"]*?)",dR="*(?:''|\"\")",dQ="eq",dP="className",dO=":enabled",dG="of-type",dF="TAG",dE="|$)",dD="<a href='#'></a>",dK="empty",dJ="qx.bom.Selector",dI="^(?:",dH="value",dY="[id='",dX="^#(",dW="[*^$]=",dV="*,:x",ed="*(",ec="^\\.(",eb="",ea="CHILD",dU=",.*:",dT="^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\(",dS="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",eg="$",ef="\\$&",ee=":checked",ek=",",ej="ID",ei="last",eh="HTML";qx.Bootstrap.define(dJ,{statics:{query:null,matches:null}});(function(window){var i,eM,fM,ew,eB,fC,eG,ep,eE,eF,eD,document,fK,fq,fe,eq,ff,eN,fa=dz+-(new Date()),eH=window.document,eW=0,eA=0,es=ey(),fr=ey(),fI=ey(),eT=function(a,b){if(a===b){eF=true;};return 0;},eR=typeof undefined,ft=1<<31,fA=({}).hasOwnProperty,ev=[],ez=ev.pop,fD=ev.push,fG=ev.push,eI=ev.slice,eS=ev.indexOf||function(fN){var i=0,fO=this.length;for(;i<fO;i++ ){if(this[i]===fN){return i;};};return -1;},fd=n,eK=V,fh=dS,fv=fh.replace(cT,F),fH=dm+eK+ed+fh+dd+eK+el+eK+p+fv+di+eK+cP,fg=dj+fh+dM+fH.replace(3,8)+r,fp=new RegExp(dc+eK+B+eK+K,cO),fx=new RegExp(dc+eK+cX+eK+z),eL=new RegExp(dc+eK+H+eK+dd+eK+z),fj=new RegExp(L+eK+dL+eK+cP,cO),fu=new RegExp(fg),eX=new RegExp(dc+fv+eg),fB={"ID":new RegExp(dX+fh+dd),"CLASS":new RegExp(ec+fh+dd),"TAG":new RegExp(dg+fh.replace(cT,dC)+dd),"ATTR":new RegExp(dc+fH),"PSEUDO":new RegExp(dc+fg),"CHILD":new RegExp(dT+eK+w+eK+X+eK+ds+eK+J,C),"bool":new RegExp(dI+fd+dw,C),"needsContext":new RegExp(dc+eK+en+eK+Y+eK+cQ,C)},fl=/^(?:input|select|textarea|button)$/i,et=/^h\d$/i,fz=/^[^{]+\{\s*\[native \w/,fF=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,fc=/[+~]/,fm=/'|\\/g,eu=new RegExp(D+eK+dB+eK+U,cH),fs=function(_,fP,fQ){var fR=cK+fP-0x10000;return fR!==fR||fQ?fP:fR<0?String.fromCharCode(fR+0x10000):String.fromCharCode(fR>>10|0xD800,fR&0x3FF|0xDC00);};try{fG.apply((ev=eI.call(eH.childNodes)),eH.childNodes);ev[eH.childNodes.length].nodeType;}catch(e){fG={apply:ev.length?function(fT,fS){fD.apply(fT,eI.call(fS));}:function(fV,fU){var j=fV.length,i=0;while((fV[j++ ]=fU[i++ ])){};fV.length=j-1;}};};function fL(gg,fX,gb,gd){var gi,fY,m,fW,i,ge,gh,ga,gf,gc;if((fX?fX.ownerDocument||fX:eH)!==document){eD(fX);};fX=fX||document;gb=gb||[];if(!gg||typeof gg!==T){return gb;};if((fW=fX.nodeType)!==1&&fW!==9){return [];};if(fq&&!gd){if((gi=fF.exec(gg))){if((m=gi[1])){if(fW===9){fY=fX.getElementById(m);if(fY&&fY.parentNode){if(fY.id===m){gb.push(fY);return gb;};}else {return gb;};}else {if(fX.ownerDocument&&(fY=fX.ownerDocument.getElementById(m))&&eN(fX,fY)&&fY.id===m){gb.push(fY);return gb;};};}else if(gi[2]){fG.apply(gb,fX.getElementsByTagName(gg));return gb;}else if((m=gi[3])&&eM.getElementsByClassName&&fX.getElementsByClassName){fG.apply(gb,fX.getElementsByClassName(m));return gb;};};if(eM.qsa&&(!fe||!fe.test(gg))){ga=gh=fa;gf=fX;gc=fW===9&&gg;if(fW===1&&fX.nodeName.toLowerCase()!==dA){ge=eV(gg);if((gh=fX.getAttribute(N))){ga=gh.replace(fm,ef);}else {fX.setAttribute(N,ga);};ga=dY+ga+f;i=ge.length;while(i-- ){ge[i]=ga+eQ(ge[i]);};gf=fc.test(gg)&&eC(fX.parentNode)||fX;gc=ge.join(ek);};if(gc){try{fG.apply(gb,gf.querySelectorAll(gc));return gb;}catch(gj){}finally{if(!gh){fX.removeAttribute(N);};};};};};return eG(gg.replace(fp,dN),fX,gb,gd);};function ey(){var gk=[];function gl(gm,gn){if(gk.push(gm+cV)>fM.cacheLength){delete gl[gk.shift()];};return (gl[gm+cV]=gn);};return gl;};function fy(go){go[fa]=true;return go;};function fk(gq){var gp=document.createElement(k);try{return !!gq(gp);}catch(e){return false;}finally{if(gp.parentNode){gp.parentNode.removeChild(gp);};gp=null;};};function fo(gt,gs){var gr=gt.split(dv),i=gt.length;while(i-- ){fM.attrHandle[gr[i]]=gs;};};function eY(a,b){var gv=b&&a,gu=gv&&a.nodeType===1&&b.nodeType===1&&(~b.sourceIndex||ft)-(~a.sourceIndex||ft);if(gu){return gu;};if(gv){while((gv=gv.nextSibling)){if(gv===b){return -1;};};};return a?1:-1;};function fE(gw){return function(gx){var name=gx.nodeName.toLowerCase();return name===l&&gx.type===gw;};};function er(gy){return function(gz){var name=gz.nodeName.toLowerCase();return (name===l||name===de)&&gz.type===gy;};};function fi(gA){return fy(function(gB){gB=+gB;return fy(function(gE,gC){var j,gD=gA([],gE.length,gB),i=gD.length;while(i-- ){if(gE[(j=gD[i])]){gE[j]=!(gC[j]=gE[j]);};};});});};function eC(gF){return gF&&typeof gF.getElementsByTagName!==eR&&gF;};eM=fL.support={};eB=fL.isXML=function(gG){var gH=gG&&(gG.ownerDocument||gG).documentElement;return gH?gH.nodeName!==eh:false;};eD=fL.setDocument=function(gI){var gK,gJ=gI?gI.ownerDocument||gI:eH,parent=gJ.defaultView;if(gJ===document||gJ.nodeType!==9||!gJ.documentElement){return document;};document=gJ;fK=gJ.documentElement;fq=!eB(gJ);if(parent&&parent!==parent.top){if(parent.addEventListener){parent.addEventListener(M,function(){eD();},false);}else if(parent.attachEvent){parent.attachEvent(dk,function(){eD();});};};eM.attributes=fk(function(gL){gL.className=C;return !gL.getAttribute(dP);});eM.getElementsByTagName=fk(function(gM){gM.appendChild(gJ.createComment(eb));return !gM.getElementsByTagName(z).length;});eM.getElementsByClassName=fz.test(gJ.getElementsByClassName)&&fk(function(gN){gN.innerHTML=cU;gN.firstChild.className=C;return gN.getElementsByClassName(C).length===2;});eM.getById=fk(function(gO){fK.appendChild(gO).id=fa;return !gJ.getElementsByName||!gJ.getElementsByName(fa).length;});if(eM.getById){fM.find[ej]=function(gP,gQ){if(typeof gQ.getElementById!==eR&&fq){var m=gQ.getElementById(gP);return m&&m.parentNode?[m]:[];};};fM.filter[ej]=function(gS){var gR=gS.replace(eu,fs);return function(gT){return gT.getAttribute(N)===gR;};};}else {delete fM.find[ej];fM.filter[ej]=function(gV){var gU=gV.replace(eu,fs);return function(gX){var gW=typeof gX.getAttributeNode!==eR&&gX.getAttributeNode(N);return gW&&gW.value===gU;};};};fM.find[dF]=eM.getElementsByTagName?function(gY,ha){if(typeof ha.getElementsByTagName!==eR){return ha.getElementsByTagName(gY);};}:function(he,hf){var hc,hb=[],i=0,hd=hf.getElementsByTagName(he);if(he===z){while((hc=hd[i++ ])){if(hc.nodeType===1){hb.push(hc);};};return hb;};return hd;};fM.find[g]=eM.getElementsByClassName&&function(hg,hh){if(typeof hh.getElementsByClassName!==eR&&fq){return hh.getElementsByClassName(hg);};};eq=[];fe=[];if((eM.qsa=fz.test(gJ.querySelectorAll))){fk(function(hi){hi.innerHTML=da;if(hi.querySelectorAll(I).length){fe.push(dW+eK+dR);};if(!hi.querySelectorAll(cM).length){fe.push(dm+eK+t+fd+dd);};if(!hi.querySelectorAll(ee).length){fe.push(ee);};});fk(function(hk){var hj=gJ.createElement(l);hj.setAttribute(cN,cW);hk.appendChild(hj).setAttribute(dn,dp);if(hk.querySelectorAll(W).length){fe.push(dn+eK+eo);};if(!hk.querySelectorAll(dO).length){fe.push(dO,h);};hk.querySelectorAll(dV);fe.push(dU);});};if((eM.matchesSelector=fz.test((ff=fK.webkitMatchesSelector||fK.mozMatchesSelector||fK.oMatchesSelector||fK.msMatchesSelector)))){fk(function(hl){eM.disconnectedMatch=ff.call(hl,k);ff.call(hl,S);eq.push(dx,fg);});};fe=fe.length&&new RegExp(fe.join(dv));eq=eq.length&&new RegExp(eq.join(dv));gK=fz.test(fK.compareDocumentPosition);eN=gK||fz.test(fK.contains)?function(a,b){var hm=a.nodeType===9?a.documentElement:a,hn=b&&b.parentNode;return a===hn||!!(hn&&hn.nodeType===1&&(hm.contains?hm.contains(hn):a.compareDocumentPosition&&a.compareDocumentPosition(hn)&16));}:function(a,b){if(b){while((b=b.parentNode)){if(b===a){return true;};};};return false;};eT=gK?function(a,b){if(a===b){eF=true;return 0;};var ho=!a.compareDocumentPosition-!b.compareDocumentPosition;if(ho){return ho;};ho=(a.ownerDocument||a)===(b.ownerDocument||b)?a.compareDocumentPosition(b):1;if(ho&1||(!eM.sortDetached&&b.compareDocumentPosition(a)===ho)){if(a===gJ||a.ownerDocument===eH&&eN(eH,a)){return -1;};if(b===gJ||b.ownerDocument===eH&&eN(eH,b)){return 1;};return eE?(eS.call(eE,a)-eS.call(eE,b)):0;};return ho&4?-1:1;}:function(a,b){if(a===b){eF=true;return 0;};var hp,i=0,hq=a.parentNode,hr=b.parentNode,hs=[a],ht=[b];if(!hq||!hr){return a===gJ?-1:b===gJ?1:hq?-1:hr?1:eE?(eS.call(eE,a)-eS.call(eE,b)):0;}else if(hq===hr){return eY(a,b);};hp=a;while((hp=hp.parentNode)){hs.unshift(hp);};hp=b;while((hp=hp.parentNode)){ht.unshift(hp);};while(hs[i]===ht[i]){i++ ;};return i?eY(hs[i],ht[i]):hs[i]===eH?-1:ht[i]===eH?1:0;};return gJ;};fL.matches=function(hu,hv){return fL(hu,null,null,hv);};fL.matchesSelector=function(hx,hw){if((hx.ownerDocument||hx)!==document){eD(hx);};hw=hw.replace(fj,E);if(eM.matchesSelector&&fq&&(!eq||!eq.test(hw))&&(!fe||!fe.test(hw))){try{var hy=ff.call(hx,hw);if(hy||eM.disconnectedMatch||hx.document&&hx.document.nodeType!==11){return hy;};}catch(e){};};return fL(hw,document,null,[hx]).length>0;};fL.contains=function(hA,hz){if((hA.ownerDocument||hA)!==document){eD(hA);};return eN(hA,hz);};fL.attr=function(hC,name){if((hC.ownerDocument||hC)!==document){eD(hC);};var hB=fM.attrHandle[name.toLowerCase()],hD=hB&&fA.call(fM.attrHandle,name.toLowerCase())?hB(hC,name,!fq):undefined;return hD!==undefined?hD:eM.attributes||!fq?hC.getAttribute(name):(hD=hC.getAttributeNode(name))&&hD.specified?hD.value:null;};fL.error=function(hE){throw new Error(em+hE);};fL.uniqueSort=function(hG){var hH,hF=[],j=0,i=0;eF=!eM.detectDuplicates;eE=!eM.sortStable&&hG.slice(0);hG.sort(eT);if(eF){while((hH=hG[i++ ])){if(hH===hG[i]){j=hF.push(i);};};while(j-- ){hG.splice(hF[j],1);};};eE=null;return hG;};ew=fL.getText=function(hK){var hI,hL=eb,i=0,hJ=hK.nodeType;if(!hJ){while((hI=hK[i++ ])){hL+=ew(hI);};}else if(hJ===1||hJ===9||hJ===11){if(typeof hK.textContent===T){return hK.textContent;}else {for(hK=hK.firstChild;hK;hK=hK.nextSibling){hL+=ew(hK);};};}else if(hJ===3||hJ===4){return hK.nodeValue;};return hL;};fM=fL.selectors={cacheLength:50,createPseudo:fy,match:fB,attrHandle:{},find:{},relative:{">":{dir:cI,first:true}," ":{dir:cI},"+":{dir:v,first:true},"~":{dir:v}},preFilter:{"ATTR":function(hM){hM[1]=hM[1].replace(eu,fs);hM[3]=(hM[4]||hM[5]||eb).replace(eu,fs);if(hM[2]===u){hM[3]=cV+hM[3]+cV;};return hM.slice(0,4);},"CHILD":function(hN){hN[1]=hN[1].toLowerCase();if(hN[1].slice(0,3)===o){if(!hN[3]){fL.error(hN[0]);};hN[4]=+(hN[4]?hN[5]+(hN[6]||1):2*(hN[3]===cY||hN[3]===dq));hN[5]=+((hN[7]+hN[8])||hN[3]===dq);}else if(hN[3]){fL.error(hN[0]);};return hN;},"PSEUDO":function(hP){var hQ,hO=!hP[5]&&hP[2];if(fB[ea].test(hP[0])){return null;};if(hP[3]&&hP[4]!==undefined){hP[2]=hP[4];}else if(hO&&fu.test(hO)&&(hQ=eV(hO,true))&&(hQ=hO.indexOf(dd,hO.length-hQ)-hO.length)){hP[0]=hP[0].slice(0,hQ);hP[2]=hO.slice(0,hQ);};return hP.slice(0,3);}},filter:{"TAG":function(hR){var hS=hR.replace(eu,fs).toLowerCase();return hR===z?function(){return true;}:function(hT){return hT.nodeName&&hT.nodeName.toLowerCase()===hS;};},"CLASS":function(hU){var hV=es[hU+cV];return hV||(hV=new RegExp(d+eK+dd+hU+cS+eK+dE))&&es(hU,function(hW){return hV.test(typeof hW.className===T&&hW.className||typeof hW.getAttribute!==eR&&hW.getAttribute(dr)||eb);});},"ATTR":function(name,hX,hY){return function(ia){var ib=fL.attr(ia,name);if(ib==null){return hX===dx;};if(!hX){return true;};ib+=eb;return hX===L?ib===hY:hX===dx?ib!==hY:hX===G?hY&&ib.indexOf(hY)===0:hX===du?hY&&ib.indexOf(hY)>-1:hX===R?hY&&ib.slice(-hY.length)===hY:hX===u?(cV+ib+cV).indexOf(hY)>-1:hX===dl?ib===hY||ib.slice(0,hY.length+1)===hY+c:false;};},"CHILD":function(ij,ic,ii,ik,ie){var ih=ij.slice(0,3)!==o,forward=ij.slice(-4)!==ei,ig=ic===dG;return ik===1&&ie===0?function(il){return !!il.parentNode;}:function(ir,iu,im){var iq,iv,io,iw,ip,is,ix=ih!==forward?Q:v,parent=ir.parentNode,name=ig&&ir.nodeName.toLowerCase(),it=!im&&!ig;if(parent){if(ih){while(ix){io=ir;while((io=io[ix])){if(ig?io.nodeName.toLowerCase()===name:io.nodeType===1){return false;};};is=ix=ij===y&&!is&&Q;};return true;};is=[forward?parent.firstChild:parent.lastChild];if(forward&&it){iv=parent[fa]||(parent[fa]={});iq=iv[ij]||[];ip=iq[0]===eW&&iq[1];iw=iq[0]===eW&&iq[2];io=ip&&parent.childNodes[ip];while((io= ++ip&&io&&io[ix]||(iw=ip=0)||is.pop())){if(io.nodeType===1&& ++iw&&io===ir){iv[ij]=[eW,ip,iw];break;};};}else if(it&&(iq=(ir[fa]||(ir[fa]={}))[ij])&&iq[0]===eW){iw=iq[1];}else {while((io= ++ip&&io&&io[ix]||(iw=ip=0)||is.pop())){if((ig?io.nodeName.toLowerCase()===name:io.nodeType===1)&& ++iw){if(it){(io[fa]||(io[fa]={}))[ij]=[eW,iw];};if(io===ir){break;};};};};iw-=ie;return iw===ik||(iw%ik===0&&iw/ik>=0);};};},"PSEUDO":function(iz,iA){var iy,iB=fM.pseudos[iz]||fM.setFilters[iz.toLowerCase()]||fL.error(cR+iz);if(iB[fa]){return iB(iA);};if(iB.length>1){iy=[iz,iz,eb,iA];return fM.setFilters.hasOwnProperty(iz.toLowerCase())?fy(function(iD,iC){var iE,iF=iB(iD,iA),i=iF.length;while(i-- ){iE=eS.call(iD,iF[i]);iD[iE]=!(iC[iE]=iF[i]);};}):function(iG){return iB(iG,0,iy);};};return iB;}},pseudos:{"not":fy(function(iI){var iH=[],iJ=[],iK=fC(iI.replace(fp,dN));return iK[fa]?fy(function(iP,iM,iQ,iL){var iN,iO=iK(iP,null,iL,[]),i=iP.length;while(i-- ){if((iN=iO[i])){iP[i]=!(iM[i]=iN);};};}):function(iS,iT,iR){iH[0]=iS;iK(iH,null,iR,iJ);return !iJ.pop();};}),"has":fy(function(iU){return function(iV){return fL(iU,iV).length>0;};}),"contains":fy(function(iW){return function(iX){return (iX.textContent||iX.innerText||ew(iX)).indexOf(iW)>-1;};}),"lang":fy(function(iY){if(!eX.test(iY||eb)){fL.error(A+iY);};iY=iY.replace(eu,fs).toLowerCase();return function(jb){var ja;do {if((ja=fq?jb.lang:jb.getAttribute(x)||jb.getAttribute(dt))){ja=ja.toLowerCase();return ja===iY||ja.indexOf(iY+c)===0;};}while((jb=jb.parentNode)&&jb.nodeType===1);return false;};}),"target":function(jd){var jc=window.location&&window.location.hash;return jc&&jc.slice(1)===jd.id;},"root":function(je){return je===fK;},"focus":function(jf){return jf===document.activeElement&&(!document.hasFocus||document.hasFocus())&&!!(jf.type||jf.href||~jf.tabIndex);},"enabled":function(jg){return jg.disabled===false;},"disabled":function(jh){return jh.disabled===true;},"checked":function(ji){var jj=ji.nodeName.toLowerCase();return (jj===l&&!!ji.checked)||(jj===dh&&!!ji.selected);},"selected":function(jk){if(jk.parentNode){jk.parentNode.selectedIndex;};return jk.selected===true;},"empty":function(jl){for(jl=jl.firstChild;jl;jl=jl.nextSibling){if(jl.nodeType<6){return false;};};return true;},"parent":function(jm){return !fM.pseudos[dK](jm);},"header":function(jn){return et.test(jn.nodeName);},"input":function(jo){return fl.test(jo.nodeName);},"button":function(jp){var name=jp.nodeName.toLowerCase();return name===l&&jp.type===de||name===de;},"text":function(jq){var jr;return jq.nodeName.toLowerCase()===l&&jq.type===O&&((jr=jq.getAttribute(cN))==null||jr.toLowerCase()===O);},"first":fi(function(){return [0];}),"last":fi(function(js,length){return [length-1];}),"eq":fi(function(jt,length,ju){return [ju<0?ju+length:ju];}),"even":fi(function(jv,length){var i=0;for(;i<length;i+=2){jv.push(i);};return jv;}),"odd":fi(function(jw,length){var i=1;for(;i<length;i+=2){jw.push(i);};return jw;}),"lt":fi(function(jx,length,jy){var i=jy<0?jy+length:jy;for(; --i>=0;){jx.push(i);};return jx;}),"gt":fi(function(jz,length,jA){var i=jA<0?jA+length:jA;for(; ++i<length;){jz.push(i);};return jz;})}};fM.pseudos[o]=fM.pseudos[dQ];for(i in {radio:true,checkbox:true,file:true,password:true,image:true}){fM.pseudos[i]=fE(i);};for(i in {submit:true,reset:true}){fM.pseudos[i]=er(i);};function fJ(){};fJ.prototype=fM.filters=fM.pseudos;fM.setFilters=new fJ();function eV(jE,jD){var jK,jJ,jB,jI,jF,jH,jG,jC=fr[jE+cV];if(jC){return jD?0:jC.slice(0);};jF=jE;jH=[];jG=fM.preFilter;while(jF){if(!jK||(jJ=fx.exec(jF))){if(jJ){jF=jF.slice(jJ[0].length)||jF;};jH.push((jB=[]));};jK=false;if((jJ=eL.exec(jF))){jK=jJ.shift();jB.push({value:jK,type:jJ[0].replace(fp,cV)});jF=jF.slice(jK.length);};for(jI in fM.filter){if((jJ=fB[jI].exec(jF))&&(!jG[jI]||(jJ=jG[jI](jJ)))){jK=jJ.shift();jB.push({value:jK,type:jI,matches:jJ});jF=jF.slice(jK.length);};};if(!jK){break;};};return jD?jF.length:jF?fL.error(jE):fr(jE,jH).slice(0);};function eQ(jL){var i=0,jM=jL.length,jN=eb;for(;i<jM;i++ ){jN+=jL[i].value;};return jN;};function eO(jO,jP,jQ){var jR=jP.dir,jT=jQ&&jR===cI,jS=eA++ ;return jP.first?function(jV,jW,jU){while((jV=jV[jR])){if(jV.nodeType===1||jT){return jO(jV,jW,jU);};};}:function(jY,kb,jX){var ka,kc,kd=[eW,jS];if(jX){while((jY=jY[jR])){if(jY.nodeType===1||jT){if(jO(jY,kb,jX)){return true;};};};}else {while((jY=jY[jR])){if(jY.nodeType===1||jT){kc=jY[fa]||(jY[fa]={});if((ka=kc[jR])&&ka[0]===eW&&ka[1]===jS){return (kd[2]=ka[2]);}else {kc[jR]=kd;if((kd[2]=jO(jY,kb,jX))){return true;};};};};};};};function eP(ke){return ke.length>1?function(kg,kh,kf){var i=ke.length;while(i-- ){if(!ke[i](kg,kh,kf)){return false;};};return true;}:ke[0];};function fw(kl,ki,kj){var i=0,kk=ki.length;for(;i<kk;i++ ){fL(kl,ki[i],kj);};return kj;};function ex(kp,kn,kq,ks,km){var ko,ku=[],i=0,kr=kp.length,kt=kn!=null;for(;i<kr;i++ ){if((ko=kp[i])){if(!kq||kq(ko,ks,km)){ku.push(ko);if(kt){kn.push(i);};};};};return ku;};function eJ(kz,ky,kx,kw,kv,kA){if(kw&&!kw[fa]){kw=eJ(kw);};if(kv&&!kv[fa]){kv=eJ(kv,kA);};return fy(function(kJ,kE,kK,kB){var kC,i,kG,kI=[],kM=[],kD=kE.length,kL=kJ||fw(ky||z,kK.nodeType?[kK]:kK,[]),kF=kz&&(kJ||!ky)?ex(kL,kI,kz,kK,kB):kL,kH=kx?kv||(kJ?kz:kD||kw)?[]:kE:kF;if(kx){kx(kF,kH,kK,kB);};if(kw){kC=ex(kH,kM);kw(kC,[],kK,kB);i=kC.length;while(i-- ){if((kG=kC[i])){kH[kM[i]]=!(kF[kM[i]]=kG);};};};if(kJ){if(kv||kz){if(kv){kC=[];i=kH.length;while(i-- ){if((kG=kH[i])){kC.push((kF[i]=kG));};};kv(null,(kH=[]),kC,kB);};i=kH.length;while(i-- ){if((kG=kH[i])&&(kC=kv?eS.call(kJ,kG):kI[i])>-1){kJ[kC]=!(kE[kC]=kG);};};};}else {kH=ex(kH===kE?kH.splice(kD,kH.length):kH);if(kv){kv(null,kE,kH,kB);}else {fG.apply(kE,kH);};};});};function fb(kS){var kN,kP,j,kQ=kS.length,kO=fM.relative[kS[0].type],kV=kO||fM.relative[cV],i=kO?1:0,kU=eO(function(kW){return kW===kN;},kV,true),kR=eO(function(kX){return eS.call(kN,kX)>-1;},kV,true),kT=[function(la,lb,kY){return (!kO&&(kY||lb!==ep))||((kN=lb).nodeType?kU(la,lb,kY):kR(la,lb,kY));}];for(;i<kQ;i++ ){if((kP=fM.relative[kS[i].type])){kT=[eO(eP(kT),kP)];}else {kP=fM.filter[kS[i].type].apply(null,kS[i].matches);if(kP[fa]){j= ++i;for(;j<kQ;j++ ){if(fM.relative[kS[j].type]){break;};};return eJ(i>1&&eP(kT),i>1&&eQ(kS.slice(0,i-1).concat({value:kS[i-2].type===cV?z:eb})).replace(fp,dN),kP,i<j&&fb(kS.slice(i,j)),j<kQ&&fb((kS=kS.slice(j))),j<kQ&&eQ(kS));};kT.push(kP);};};return eP(kT);};function eU(lg,ld){var lc=ld.length>0,le=lg.length>0,lf=function(lp,ls,lh,ll,lk){var ln,j,lt,li=0,i=df,lm=lp&&[],lo=[],lj=ep,lu=lp||le&&fM.find[dF](z,lk),lq=(eW+=lj==null?1:Math.random()||0.1),lr=lu.length;if(lk){ep=ls!==document&&ls;};for(;i!==lr&&(ln=lu[i])!=null;i++ ){if(le&&ln){j=0;while((lt=lg[j++ ])){if(lt(ln,ls,lh)){ll.push(ln);break;};};if(lk){eW=lq;};};if(lc){if((ln=!lt&&ln)){li-- ;};if(lp){lm.push(ln);};};};li+=i;if(lc&&i!==li){j=0;while((lt=ld[j++ ])){lt(lm,lo,ls,lh);};if(lp){if(li>0){while(i-- ){if(!(lm[i]||lo[i])){lo[i]=ez.call(ll);};};};lo=ex(lo);};fG.apply(ll,lo);if(lk&&!lp&&lo.length>0&&(li+ld.length)>1){fL.uniqueSort(ll);};};if(lk){eW=lq;ep=lj;};return lm;};return lc?fy(lf):lf;};fC=fL.compile=function(lw,lz){var i,ly=[],lv=[],lx=fI[lw+cV];if(!lx){if(!lz){lz=eV(lw);};i=lz.length;while(i-- ){lx=fb(lz[i]);if(lx[fa]){ly.push(lx);}else {lv.push(lx);};};lx=fI(lw,eU(lv,ly));lx.selector=lw;};return lx;};eG=fL.select=function(lC,lG,lB,lF){var i,lD,lE,lH,find,lA=typeof lC===db&&lC,lI=!lF&&eV((lC=lA.selector||lC));lB=lB||[];if(lI.length===1){lD=lI[0]=lI[0].slice(0);if(lD.length>2&&(lE=lD[0]).type===ej&&eM.getById&&lG.nodeType===9&&fq&&fM.relative[lD[1].type]){lG=(fM.find[ej](lE.matches[0].replace(eu,fs),lG)||[])[0];if(!lG){return lB;}else if(lA){lG=lG.parentNode;};lC=lC.slice(lD.shift().value.length);};i=fB[P].test(lC)?0:lD.length;while(i-- ){lE=lD[i];if(fM.relative[(lH=lE.type)]){break;};if((find=fM.find[lH])){if((lF=find(lE.matches[0].replace(eu,fs),fc.test(lD[0].type)&&eC(lG.parentNode)||lG))){lD.splice(i,1);lC=lF.length&&eQ(lD);if(!lC){fG.apply(lB,lF);return lB;};break;};};};};(lA||fC(lC,lI))(lF,lG,!fq,lB,fc.test(lC)&&eC(lG.parentNode)||lG);return lB;};eM.sortStable=fa.split(eb).sort(eT).join(eb)===fa;eM.detectDuplicates=!!eF;eD();eM.sortDetached=fk(function(lJ){return lJ.compareDocumentPosition(document.createElement(k))&1;});if(!fk(function(lK){lK.innerHTML=dD;return lK.firstChild.getAttribute(cJ)===cL;})){fo(q,function(lL,name,lM){if(!lM){return lL.getAttribute(name,name.toLowerCase()===cN?1:2);};});};if(!eM.attributes||!fk(function(lN){lN.innerHTML=dy;lN.firstChild.setAttribute(dH,eb);return lN.firstChild.getAttribute(dH)===eb;})){fo(dH,function(lO,name,lP){if(!lP&&lO.nodeName.toLowerCase()===l){return lO.defaultValue;};});};if(!fk(function(lQ){return lQ.getAttribute(s)==null;})){fo(fd,function(lS,name,lR){var lT;if(!lR){return lS[name]===true?name.toLowerCase():(lT=lS.getAttributeNode(name))&&lT.specified?lT.value:null;};});};qx.bom.Selector.query=function(lV,lU){return fL(lV,lU);};qx.bom.Selector.matches=function(lX,lW){return fL(lX,null,null,lW);};})(window);})();(function(){var a="Number",b="qx.lang.Type",c="Boolean";qx.Bootstrap.define(b,{statics:{getClass:qx.Bootstrap.getClass,isString:qx.Bootstrap.isString,isArray:qx.Bootstrap.isArray,isObject:qx.Bootstrap.isObject,isFunction:qx.Bootstrap.isFunction,isNumber:function(d){return (d!==null&&(this.getClass(d)==a||d instanceof Number));},isBoolean:function(e){return (e!==null&&(this.getClass(e)==c||e instanceof Boolean));}}});})();(function(){var a="ecmascript.object.keys",b="qx.lang.normalize.Object";qx.Bootstrap.define(b,{statics:{keys:qx.Bootstrap.keys},defer:function(c){if(!qx.core.Environment.get(a)){Object.keys=c.keys;};}});})();(function(){var a='[object Boolean]',b='[object String]',c='constructor',d='[object Date]',e='[object Number]',f='object',g="qx.lang.Object",h='[object RegExp]',j='[object Array]';qx.Bootstrap.define(g,{statics:{isEmpty:function(k){{};for(var m in k){return false;};return true;},getValues:function(o){{};var p=[];var n=Object.keys(o);for(var i=0,l=n.length;i<l;i++ ){p.push(o[n[i]]);};return p;},clone:function(q,t){if(qx.lang.Type.isObject(q)){var r={};for(var s in q){if(t){r[s]=qx.lang.Object.clone(q[s],t);}else {r[s]=q[s];};};return r;}else if(qx.lang.Type.isArray(q)){var r=[];for(var i=0;i<q.length;i++ ){if(t){r[i]=qx.lang.Object.clone(q[i],t);}else {r[i]=q[i];};};return r;};return q;},__equals:function(A,w,u,v){if(A===w){return A!==0||1/A==1/w;};if(A==null||w==null){return A===w;};var z=Object.prototype.toString.call(A);if(z!=Object.prototype.toString.call(w)){return false;};switch(z){case b:return A==String(w);case e:return A!=+A?w!=+w:(A==0?1/A==1/w:A==+w);case d:case a:return +A==+w;case h:return A.source==w.source&&A.global==w.global&&A.multiline==w.multiline&&A.ignoreCase==w.ignoreCase;};if(typeof A!=f||typeof w!=f){return false;};var length=u.length;while(length-- ){if(u[length]==A){return v[length]==w;};};var y=A.constructor,x=w.constructor;if(y!==x&&!(qx.Bootstrap.isFunction(y)&&(y instanceof y)&&qx.Bootstrap.isFunction(x)&&(x instanceof x))&&(c in A&&c in w)){return false;};u.push(A);v.push(w);var D=0,B=true;if(z==j){D=A.length;B=D==w.length;if(B){while(D-- ){if(!(B=qx.lang.Object.__equals(A[D],w[D],u,v))){break;};};};}else {for(var C in A){if(Object.prototype.hasOwnProperty.call(A,C)){D++ ;if(!(B=Object.prototype.hasOwnProperty.call(w,C)&&qx.lang.Object.__equals(A[C],w[C],u,v))){break;};};};if(B){for(C in w){if(Object.prototype.hasOwnProperty.call(w,C)&&!(D-- )){break;};};B=!D;};};u.pop();v.pop();return B;},invert:function(E){{};var F={};for(var G in E){F[E[G].toString()]=G;};return F;},getKeyFromValue:function(H,I){{};for(var J in H){if(H.hasOwnProperty(J)&&H[J]===I){return J;};};return null;},contains:function(K,L){{};return this.getKeyFromValue(K,L)!==null;}}});})();(function(){var a="qx.bom.String";qx.Bootstrap.define(a,{statics:{TO_CHARCODE:{"quot":34,"amp":38,"lt":60,"gt":62,"nbsp":160,"iexcl":161,"cent":162,"pound":163,"curren":164,"yen":165,"brvbar":166,"sect":167,"uml":168,"copy":169,"ordf":170,"laquo":171,"not":172,"shy":173,"reg":174,"macr":175,"deg":176,"plusmn":177,"sup2":178,"sup3":179,"acute":180,"micro":181,"para":182,"middot":183,"cedil":184,"sup1":185,"ordm":186,"raquo":187,"frac14":188,"frac12":189,"frac34":190,"iquest":191,"Agrave":192,"Aacute":193,"Acirc":194,"Atilde":195,"Auml":196,"Aring":197,"AElig":198,"Ccedil":199,"Egrave":200,"Eacute":201,"Ecirc":202,"Euml":203,"Igrave":204,"Iacute":205,"Icirc":206,"Iuml":207,"ETH":208,"Ntilde":209,"Ograve":210,"Oacute":211,"Ocirc":212,"Otilde":213,"Ouml":214,"times":215,"Oslash":216,"Ugrave":217,"Uacute":218,"Ucirc":219,"Uuml":220,"Yacute":221,"THORN":222,"szlig":223,"agrave":224,"aacute":225,"acirc":226,"atilde":227,"auml":228,"aring":229,"aelig":230,"ccedil":231,"egrave":232,"eacute":233,"ecirc":234,"euml":235,"igrave":236,"iacute":237,"icirc":238,"iuml":239,"eth":240,"ntilde":241,"ograve":242,"oacute":243,"ocirc":244,"otilde":245,"ouml":246,"divide":247,"oslash":248,"ugrave":249,"uacute":250,"ucirc":251,"uuml":252,"yacute":253,"thorn":254,"yuml":255,"fnof":402,"Alpha":913,"Beta":914,"Gamma":915,"Delta":916,"Epsilon":917,"Zeta":918,"Eta":919,"Theta":920,"Iota":921,"Kappa":922,"Lambda":923,"Mu":924,"Nu":925,"Xi":926,"Omicron":927,"Pi":928,"Rho":929,"Sigma":931,"Tau":932,"Upsilon":933,"Phi":934,"Chi":935,"Psi":936,"Omega":937,"alpha":945,"beta":946,"gamma":947,"delta":948,"epsilon":949,"zeta":950,"eta":951,"theta":952,"iota":953,"kappa":954,"lambda":955,"mu":956,"nu":957,"xi":958,"omicron":959,"pi":960,"rho":961,"sigmaf":962,"sigma":963,"tau":964,"upsilon":965,"phi":966,"chi":967,"psi":968,"omega":969,"thetasym":977,"upsih":978,"piv":982,"bull":8226,"hellip":8230,"prime":8242,"Prime":8243,"oline":8254,"frasl":8260,"weierp":8472,"image":8465,"real":8476,"trade":8482,"alefsym":8501,"larr":8592,"uarr":8593,"rarr":8594,"darr":8595,"harr":8596,"crarr":8629,"lArr":8656,"uArr":8657,"rArr":8658,"dArr":8659,"hArr":8660,"forall":8704,"part":8706,"exist":8707,"empty":8709,"nabla":8711,"isin":8712,"notin":8713,"ni":8715,"prod":8719,"sum":8721,"minus":8722,"lowast":8727,"radic":8730,"prop":8733,"infin":8734,"ang":8736,"and":8743,"or":8744,"cap":8745,"cup":8746,"int":8747,"there4":8756,"sim":8764,"cong":8773,"asymp":8776,"ne":8800,"equiv":8801,"le":8804,"ge":8805,"sub":8834,"sup":8835,"sube":8838,"supe":8839,"oplus":8853,"otimes":8855,"perp":8869,"sdot":8901,"lceil":8968,"rceil":8969,"lfloor":8970,"rfloor":8971,"lang":9001,"rang":9002,"loz":9674,"spades":9824,"clubs":9827,"hearts":9829,"diams":9830,"OElig":338,"oelig":339,"Scaron":352,"scaron":353,"Yuml":376,"circ":710,"tilde":732,"ensp":8194,"emsp":8195,"thinsp":8201,"zwnj":8204,"zwj":8205,"lrm":8206,"rlm":8207,"ndash":8211,"mdash":8212,"lsquo":8216,"rsquo":8217,"sbquo":8218,"ldquo":8220,"rdquo":8221,"bdquo":8222,"dagger":8224,"Dagger":8225,"permil":8240,"lsaquo":8249,"rsaquo":8250,"euro":8364},escape:function(b){return qx.util.StringEscape.escape(b,qx.bom.String.FROM_CHARCODE);}},defer:function(c){c.FROM_CHARCODE=qx.lang.Object.invert(c.TO_CHARCODE);}});})();(function(){var a="&",b=";",c="qx.util.StringEscape",d="",e="&#";qx.Bootstrap.define(c,{statics:{escape:function(m,j){var g,k=d;for(var i=0,l=m.length;i<l;i++ ){var h=m.charAt(i);var f=h.charCodeAt(0);if(j[f]){g=a+j[f]+b;}else {if(f>0x7F){g=e+f+b;}else {g=h;};};k+=g;};return k;}}});})();(function(){var a='',b="ecmascript.string.trim",c="qx.lang.normalize.String";qx.Bootstrap.define(c,{statics:{trim:function(){return this.replace(/^\s+|\s+$/g,a);}},defer:function(d){if(!qx.core.Environment.get(b)){String.prototype.trim=d.trim;};}});})();(function(){var a="-",b="0",c="",d='\\$1',e='-',f="qx.lang.String",g="undefined";qx.Bootstrap.define(f,{statics:{__stringsMap:{},camelCase:function(i){var h=this.__stringsMap[i];if(!h){h=i.replace(/\-([a-z])/g,function(k,j){return j.toUpperCase();});if(i.indexOf(a)>=0){this.__stringsMap[i]=h;};};return h;},hyphenate:function(m){var l=this.__stringsMap[m];if(!l){l=m.replace(/[A-Z]/g,function(n){return (e+n.charAt(0).toLowerCase());});if(m.indexOf(a)==-1){this.__stringsMap[m]=l;};};return l;},startsWith:function(p,o){return p.indexOf(o)===0;},endsWith:function(r,q){return r.substring(r.length-q.length,r.length)===q;},repeat:function(s,t){return s.length>0?new Array(t+1).join(s):c;},pad:function(v,length,u){var w=length-v.length;if(w>0){if(typeof u===g){u=b;};return this.repeat(u,w)+v;}else {return v;};},firstUp:qx.Bootstrap.firstUp,firstLow:qx.Bootstrap.firstLow,escapeRegexpChars:function(x){return x.replace(/([.*+?^${}()|[\]\/\\])/g,d);}}});})();(function(){var a="qx.lang.Function";qx.Bootstrap.define(a,{statics:{create:function(c,b){{};if(!b){return c;};if(!(b.self||b.args||b.delay!=null||b.periodical!=null||b.attempt)){return c;};return function(event){{};var e=qx.lang.Array.fromArguments(arguments);if(b.args){e=b.args.concat(e);};if(b.delay||b.periodical){var d=function(){return c.apply(b.self||this,e);};{};if(b.delay){return window.setTimeout(d,b.delay);};if(b.periodical){return window.setInterval(d,b.periodical);};}else if(b.attempt){var f=false;try{f=c.apply(b.self||this,e);}catch(g){};return f;}else {return c.apply(b.self||this,e);};};},bind:function(h,self,i){return this.create(h,{self:self,args:arguments.length>2?qx.lang.Array.fromArguments(arguments,2):null});},listener:function(k,self,l){if(arguments.length<3){return function(event){return k.call(self||this,event||window.event);};}else {var j=qx.lang.Array.fromArguments(arguments,2);return function(event){var m=[event||window.event];m.push.apply(m,j);k.apply(self||this,m);};};}}});})();(function(){var a="qx.module.util.String",b="string";qx.Bootstrap.define(a,{statics:{camelCase:function(c){return qx.lang.String.camelCase.call(qx.lang.String,c);},hyphenate:function(d){return qx.lang.String.hyphenate.call(qx.lang.String,d);},firstUp:qx.lang.String.firstUp,firstLow:qx.lang.String.firstLow,startsWith:qx.lang.String.startsWith,endsWith:qx.lang.String.endsWith,escapeRegexpChars:qx.lang.String.escapeRegexpChars,escapeHtml:qx.bom.String.escape},defer:function(e){qxWeb.$attachAll(this,b);}});})();(function(){var a="qx.module.Dataset";qx.Bootstrap.define(a,{members:{setData:function(name,b){this._forEachElement(function(c){qx.bom.element.Dataset.set(c,name,b);});return this;},getData:function(name){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Dataset.get(this[0],name);};},getAllData:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Dataset.getAll(this[0]);};return {};},hasData:function(){return qx.bom.element.Dataset.hasData(this[0]);},removeData:function(name){this._forEachElement(function(d){qx.bom.element.Dataset.remove(d,name);});return this;}},defer:function(e){qxWeb.$attachAll(this);}});})();(function(){var a="qx.bom.element.Dataset",b="data-",c="^data-(.*)";qx.Bootstrap.define(a,{statics:{set:function(e,name,d){if(e.dataset){name=qx.lang.String.camelCase(name);if((d===null)||(d==undefined)){delete e.dataset[name];}else {e.dataset[name]=d;};}else {if((d===null)||(d==undefined)){qx.bom.element.Attribute.reset(e,b+qx.lang.String.hyphenate(name));}else {qx.bom.element.Attribute.set(e,b+qx.lang.String.hyphenate(name),d);};};},get:function(g,name){if(g.dataset){name=qx.lang.String.camelCase(name);return (!g.dataset[name]?undefined:g.dataset[name]);}else {var f=b+qx.lang.String.hyphenate(name);return g.hasAttribute(f)?qx.bom.element.Attribute.get(g,f):undefined;};},getAll:function(l){if(l.dataset){return l.dataset;}else {var h={},j=l.attributes;for(var i=0;i<j.length;i++ ){if(j[i].name.match(RegExp(c))){var k=RegExp.$1;h[qx.lang.String.camelCase(k)]=l.getAttribute(j[i].name);};};return h;};},hasData:function(m){return Object.keys(qxWeb(m).getAllData()).length>0;},remove:function(n,name){this.set(n,name,undefined);}}});})();(function(){var b="function",c="html.video.h264",d="html.element.contains",f='video/ogg; codecs="theora, vorbis"',g="qxtest",h="html.console",i="html.xul",j="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul",k="html.video.ogg",l="http://www.w3.org/TR/SVG11/feature#BasicStructure",m="html.storage.local",n="div",o="qx.bom.client.Html",p="getSelection",q='audio',r='video/mp4; codecs="avc1.42E01E, mp4a.40.2"',s="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==",t="html.audio",u="video",w="url(#default#VML)",x="head",y="audio",z="audio/mpeg",A="org.w3c.dom.svg",B="html.classlist",C="html.svg",D="html.video",E="html.geolocation",F="DOMTokenList",G="html.storage.session",H="1.1",I="html.history.state",J="object",K="html.image.naturaldimensions",L="html.audio.aif",M="audio/x-wav",N='<v:shape id="vml_flag1" adj="1" />',O="html.node.isequalnode",P="html.canvas",Q="audio/ogg",R="",S="html.storage.userdata",T="html.fullscreen",U="number",V="html.element.compareDocumentPosition",W="audio/x-aiff",X="html.audio.au",Y="img",bF="html.selection",bG="selection",bH="html.xpath",bB="$qx_check",bC="test",bD='video',bE="span",bM="html.element.textcontent",bN="geolocation",bW="html.audio.mp3",bA="html.vml",bI="undefined",bJ="html.audio.ogg",bK="none",bL="label",bQ='video/webm; codecs="vp8, vorbis"',ca="html.dataurl",bR="html.webworker",bS="html.dataset",bX="1.0",bO="html.audio.wav",bY="html.filereader",bP="audio/basic",bT="display",bU="html.video.webm",bV="#default#userdata";qx.Bootstrap.define(o,{statics:{getWebWorker:function(){return window.Worker!=null;},getFileReader:function(){return window.FileReader!=null;},getGeoLocation:function(){return bN in navigator;},getAudio:function(){return !!document.createElement(q).canPlayType;},getAudioOgg:function(){if(!qx.bom.client.Html.getAudio()){return R;};var a=document.createElement(y);return a.canPlayType(Q);},getAudioMp3:function(){if(!qx.bom.client.Html.getAudio()){return R;};var a=document.createElement(y);return a.canPlayType(z);},getAudioWav:function(){if(!qx.bom.client.Html.getAudio()){return R;};var a=document.createElement(y);return a.canPlayType(M);},getAudioAu:function(){if(!qx.bom.client.Html.getAudio()){return R;};var a=document.createElement(y);return a.canPlayType(bP);},getAudioAif:function(){if(!qx.bom.client.Html.getAudio()){return R;};var a=document.createElement(y);return a.canPlayType(W);},getVideo:function(){return !!document.createElement(bD).canPlayType;},getVideoOgg:function(){if(!qx.bom.client.Html.getVideo()){return R;};var v=document.createElement(u);return v.canPlayType(f);},getVideoH264:function(){if(!qx.bom.client.Html.getVideo()){return R;};var v=document.createElement(u);return v.canPlayType(r);},getVideoWebm:function(){if(!qx.bom.client.Html.getVideo()){return R;};var v=document.createElement(u);return v.canPlayType(bQ);},getLocalStorage:function(){try{window.localStorage.setItem(bB,bC);window.localStorage.removeItem(bB);return true;}catch(cb){return false;};},getSessionStorage:function(){try{window.sessionStorage.setItem(bB,bC);window.sessionStorage.removeItem(bB);return true;}catch(cc){return false;};},getUserDataStorage:function(){var cd=document.createElement(n);cd.style[bT]=bK;document.getElementsByTagName(x)[0].appendChild(cd);var ce=false;try{cd.addBehavior(bV);cd.load(g);ce=true;}catch(e){};document.getElementsByTagName(x)[0].removeChild(cd);return ce;},getClassList:function(){return !!(document.documentElement.classList&&qx.Bootstrap.getClass(document.documentElement.classList)===F);},getXPath:function(){return !!document.evaluate;},getXul:function(){try{document.createElementNS(j,bL);return true;}catch(e){return false;};},getSvg:function(){return document.implementation&&document.implementation.hasFeature&&(document.implementation.hasFeature(A,bX)||document.implementation.hasFeature(l,H));},getVml:function(){var cf=document.createElement(n);document.body.appendChild(cf);cf.innerHTML=N;cf.firstChild.style.behavior=w;var cg=typeof cf.firstChild.adj==J;document.body.removeChild(cf);return cg;},getCanvas:function(){return !!window.CanvasRenderingContext2D;},getDataUrl:function(ch){var ci=new Image();ci.onload=ci.onerror=function(){window.setTimeout(function(){ch.call(null,(ci.width==1&&ci.height==1));},0);};ci.src=s;},getDataset:function(){return !!document.documentElement.dataset;},getContains:function(){return (typeof document.documentElement.contains!==bI);},getCompareDocumentPosition:function(){return (typeof document.documentElement.compareDocumentPosition===b);},getTextContent:function(){var cj=document.createElement(bE);return (typeof cj.textContent!==bI);},getFullScreen:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled||false;},getConsole:function(){return typeof window.console!==bI;},getNaturalDimensions:function(){var ck=document.createElement(Y);return typeof ck.naturalHeight===U&&typeof ck.naturalWidth===U;},getHistoryState:function(){return (typeof window.onpopstate!==bI&&typeof window.history.replaceState!==bI&&typeof window.history.pushState!==bI);},getSelection:function(){if(typeof window.getSelection===b){return p;};if(typeof document.selection===J){return bG;};return null;},getIsEqualNode:function(){return typeof document.documentElement.isEqualNode===b;}},defer:function(cl){qx.core.Environment.add(bR,cl.getWebWorker);qx.core.Environment.add(bY,cl.getFileReader);qx.core.Environment.add(E,cl.getGeoLocation);qx.core.Environment.add(t,cl.getAudio);qx.core.Environment.add(bJ,cl.getAudioOgg);qx.core.Environment.add(bW,cl.getAudioMp3);qx.core.Environment.add(bO,cl.getAudioWav);qx.core.Environment.add(X,cl.getAudioAu);qx.core.Environment.add(L,cl.getAudioAif);qx.core.Environment.add(D,cl.getVideo);qx.core.Environment.add(k,cl.getVideoOgg);qx.core.Environment.add(c,cl.getVideoH264);qx.core.Environment.add(bU,cl.getVideoWebm);qx.core.Environment.add(m,cl.getLocalStorage);qx.core.Environment.add(G,cl.getSessionStorage);qx.core.Environment.add(S,cl.getUserDataStorage);qx.core.Environment.add(B,cl.getClassList);qx.core.Environment.add(bH,cl.getXPath);qx.core.Environment.add(i,cl.getXul);qx.core.Environment.add(P,cl.getCanvas);qx.core.Environment.add(C,cl.getSvg);qx.core.Environment.add(bA,cl.getVml);qx.core.Environment.add(bS,cl.getDataset);qx.core.Environment.addAsync(ca,cl.getDataUrl);qx.core.Environment.add(d,cl.getContains);qx.core.Environment.add(V,cl.getCompareDocumentPosition);qx.core.Environment.add(bM,cl.getTextContent);qx.core.Environment.add(h,cl.getConsole);qx.core.Environment.add(K,cl.getNaturalDimensions);qx.core.Environment.add(I,cl.getHistoryState);qx.core.Environment.add(bF,cl.getSelection);qx.core.Environment.add(O,cl.getIsEqualNode);qx.core.Environment.add(T,cl.getFullScreen);}});})();(function(){var a="readOnly",b="data-",c="accessKey",d="qx.bom.element.Attribute",e="rowSpan",f="vAlign",g="className",h="textContent",i="htmlFor",j="longDesc",k="cellSpacing",l="frameBorder",m="",n="useMap",o="innerText",p="innerHTML",q="tabIndex",r="dateTime",s="maxLength",t="html.element.textcontent",u="mshtml",v="engine.name",w="cellPadding",x="browser.documentmode",y="colSpan",z="undefined";qx.Bootstrap.define(d,{statics:{__hints:{names:{"class":g,"for":i,html:p,text:qx.core.Environment.get(t)?h:o,colspan:y,rowspan:e,valign:f,datetime:r,accesskey:c,tabindex:q,maxlength:s,readonly:a,longdesc:j,cellpadding:w,cellspacing:k,frameborder:l,usemap:n},runtime:{"html":1,"text":1},bools:{compact:1,nowrap:1,ismap:1,declare:1,noshade:1,checked:1,disabled:1,readOnly:1,multiple:1,selected:1,noresize:1,defer:1,allowTransparency:1},property:{$$html:1,$$widget:1,checked:1,readOnly:1,multiple:1,selected:1,value:1,maxLength:1,className:1,innerHTML:1,innerText:1,textContent:1,htmlFor:1,tabIndex:1},qxProperties:{$$widget:1,$$html:1},propertyDefault:{disabled:false,checked:false,readOnly:false,multiple:false,selected:false,value:m,className:m,innerHTML:m,innerText:m,textContent:m,htmlFor:m,tabIndex:0,maxLength:qx.core.Environment.select(v,{"mshtml":2147483647,"webkit":524288,"default":-1})},removeableProperties:{disabled:1,multiple:1,maxLength:1}},get:function(C,name){var A=this.__hints;var B;name=A.names[name]||name;if(A.property[name]){B=C[name];if(typeof A.propertyDefault[name]!==z&&B==A.propertyDefault[name]){if(typeof A.bools[name]===z){return null;}else {return B;};};}else {B=C.getAttribute(name);if(A.bools[name]&&!(qx.core.Environment.get(v)==u&&parseInt(qx.core.Environment.get(x),10)<=8)){return qx.Bootstrap.isString(B);};};if(A.bools[name]){return !!B;};return B;},set:function(F,name,E){if(typeof E===z){return;};var D=this.__hints;name=D.names[name]||name;if(D.bools[name]&&!qx.lang.Type.isBoolean(E)){E=qx.lang.Type.isString(E);};if(D.property[name]&&(!(F[name]===undefined)||D.qxProperties[name])){if(E==null){if(D.removeableProperties[name]){F.removeAttribute(name);return;}else if(typeof D.propertyDefault[name]!==z){E=D.propertyDefault[name];};};F[name]=E;}else {if((D.bools[name]||E===null)&&name.indexOf(b)!==0){if(E===true){F.setAttribute(name,name);}else if(E===false||E===null){F.removeAttribute(name);};}else {F.setAttribute(name,E);};};},reset:function(G,name){if(name.indexOf(b)===0){G.removeAttribute(name);}else {this.set(G,name,null);};}}});})();(function(){var a="rim_tabletos",b="10.1",c="Darwin",d="10.3",e="Windows Phone",f="os.version",g="10.7",h="2003",i=")",j="iPhone",k="android",l="unix",m="ce",n="7",o="10.11",p="SymbianOS",q="10.5",r="os.name",s="10.12",t="10.9",u="|",v="MacPPC",w="95",x="iPod",y="10.8",z="\.",A="Win64",B="linux",C="me",D="10.2",E="Macintosh",F="Android",G="Windows",H="98",I="ios",J="10",K="vista",L="8",M="blackberry",N="2000",O="8.1",P="(",Q="",R="win",S="Linux",T="10.6",U="BSD",V="10.0",W="10.4",X="Mac OS X",Y="iPad",bu="X11",bv="xp",bw="symbian",bq="qx.bom.client.OperatingSystem",br="g",bs="Win32",bt="10.10",bA="osx",bB="webOS",bC="RIM Tablet OS",bD="BlackBerry",bx="nt4",by=".",bz="MacIntel",bp="webos";qx.Bootstrap.define(bq,{statics:{getName:function(){if(!navigator){return Q;};var bE=navigator.platform||Q;var bF=navigator.userAgent||Q;if(bE.indexOf(G)!=-1||bE.indexOf(bs)!=-1||bE.indexOf(A)!=-1||bF.indexOf(e)!=-1){return R;}else if(bE.indexOf(E)!=-1||bE.indexOf(v)!=-1||bE.indexOf(bz)!=-1||bE.indexOf(X)!=-1){return bA;}else if(bF.indexOf(bC)!=-1){return a;}else if(bF.indexOf(bB)!=-1){return bp;}else if(bE.indexOf(x)!=-1||bE.indexOf(j)!=-1||bE.indexOf(Y)!=-1){return I;}else if(bF.indexOf(F)!=-1){return k;}else if(bE.indexOf(S)!=-1){return B;}else if(bE.indexOf(bu)!=-1||bE.indexOf(U)!=-1||bE.indexOf(c)!=-1){return l;}else if(bE.indexOf(p)!=-1){return bw;}else if(bE.indexOf(bD)!=-1){return M;};return Q;},__ids:{"Windows NT 10.0":J,"Windows NT 6.3":O,"Windows NT 6.2":L,"Windows NT 6.1":n,"Windows NT 6.0":K,"Windows NT 5.2":h,"Windows NT 5.1":bv,"Windows NT 5.0":N,"Windows 2000":N,"Windows NT 4.0":bx,"Win 9x 4.90":C,"Windows CE":m,"Windows 98":H,"Win98":H,"Windows 95":w,"Win95":w,"Mac OS X 10_12":s,"Mac OS X 10.12":s,"Mac OS X 10_11":o,"Mac OS X 10.11":o,"Mac OS X 10_10":bt,"Mac OS X 10.10":bt,"Mac OS X 10_9":t,"Mac OS X 10.9":t,"Mac OS X 10_8":y,"Mac OS X 10.8":y,"Mac OS X 10_7":g,"Mac OS X 10.7":g,"Mac OS X 10_6":T,"Mac OS X 10.6":T,"Mac OS X 10_5":q,"Mac OS X 10.5":q,"Mac OS X 10_4":W,"Mac OS X 10.4":W,"Mac OS X 10_3":d,"Mac OS X 10.3":d,"Mac OS X 10_2":D,"Mac OS X 10.2":D,"Mac OS X 10_1":b,"Mac OS X 10.1":b,"Mac OS X 10_0":V,"Mac OS X 10.0":V},getVersion:function(){var bG=qx.bom.client.OperatingSystem.__getVersionForDesktopOs(navigator.userAgent);if(bG==null){bG=qx.bom.client.OperatingSystem.__getVersionForMobileOs(navigator.userAgent);};if(bG!=null){return bG;}else {return Q;};},__getVersionForDesktopOs:function(bH){var bK=[];for(var bJ in qx.bom.client.OperatingSystem.__ids){bK.push(bJ);};var bL=new RegExp(P+bK.join(u).replace(/\./g,z)+i,br);var bI=bL.exec(bH);if(bI&&bI[1]){return qx.bom.client.OperatingSystem.__ids[bI[1]];};return null;},__getVersionForMobileOs:function(bR){var bM=bR.indexOf(e)!=-1;var bS=bR.indexOf(F)!=-1;var bN=bR.match(/(iPad|iPhone|iPod)/i)?true:false;if(bM){var bU=new RegExp(/Windows Phone (\d+(?:\.\d+)+)/i);var bP=bU.exec(bR);if(bP&&bP[1]){return bP[1];};}else if(bS){var bQ=new RegExp(/ Android (\d+(?:\.\d+)+)/i);var bT=bQ.exec(bR);if(bT&&bT[1]){return bT[1];};}else if(bN){var bV=new RegExp(/(CPU|iPhone|iPod) OS (\d+)_(\d+)(?:_(\d+))*\s+/);var bO=bV.exec(bR);if(bO&&bO[2]&&bO[3]){if(bO[4]){return bO[2]+by+bO[3]+by+bO[4];}else {return bO[2]+by+bO[3];};};};return null;}},defer:function(bW){qx.core.Environment.add(r,bW.getName);qx.core.Environment.add(f,bW.getVersion);}});})();(function(){var a="CSS1Compat",b="IEMobile",c=" OPR/",d="msie",e="android",f="operamini",g="gecko",h="maple",i="AdobeAIR|Titanium|Fluid|Chrome|Android|Epiphany|Konqueror|iCab|iPad|iPhone|OmniWeb|Maxthon|Pre|PhantomJS|Mobile Safari|Safari",j="browser.quirksmode",k="browser.name",l="trident",m="mobile chrome",n=")(/| )([0-9]+\.[0-9])",o="iemobile",p="prism|Fennec|Camino|Kmeleon|Galeon|Netscape|SeaMonkey|Namoroka|Firefox",q="IEMobile|Maxthon|MSIE|Trident",r="opera mobi",s="Mobile Safari",t="Maple",u="operamobile",v="ie",w="mobile safari",x="qx.bom.client.Browser",y="(Maple )([0-9]+\.[0-9]+\.[0-9]*)",z="",A="opera mini",B="(",C="browser.version",D="opera",E="ce",F=")(/|)?([0-9]+\.[0-9])?",G="mshtml",H="Opera Mini|Opera Mobi|Opera",I="edge",J="webkit",K="browser.documentmode",L="5.0",M="Mobile/";qx.Bootstrap.define(x,{statics:{getName:function(){var P=navigator.userAgent;var Q=new RegExp(B+qx.bom.client.Browser.__agents+F);var O=P.match(Q);if(!O){return z;};var name=O[1].toLowerCase();var N=qx.bom.client.Engine.getName();if(N===J){if(P.match(/Edge\/\d+\.\d+/)){name=I;}else if(name===e){name=m;}else if(P.indexOf(s)!==-1||P.indexOf(M)!==-1){name=w;}else if(P.indexOf(c)!=-1){name=D;};}else if(N===G){if(name===d||name===l){name=v;if(qx.bom.client.OperatingSystem.getVersion()===E){name=o;};var Q=new RegExp(b);if(P.match(Q)){name=o;};};}else if(N===D){if(name===r){name=u;}else if(name===A){name=f;};}else if(N===g){if(P.indexOf(t)!==-1){name=h;};};return name;},getVersion:function(){var T=navigator.userAgent;var U=new RegExp(B+qx.bom.client.Browser.__agents+n);var R=T.match(U);if(!R){return z;};var name=R[1].toLowerCase();var S=R[3];if(T.match(/Version(\/| )([0-9]+\.[0-9])/)){S=RegExp.$2;};if(qx.bom.client.Engine.getName()==G){S=qx.bom.client.Engine.getVersion();if(name===d&&qx.bom.client.OperatingSystem.getVersion()==E){S=L;};};if(qx.bom.client.Browser.getName()==h){U=new RegExp(y);R=T.match(U);if(!R){return z;};S=R[2];};if(qx.bom.client.Engine.getName()==J||qx.bom.client.Browser.getName()==D){if(T.match(/OPR(\/| )([0-9]+\.[0-9])/)){S=RegExp.$2;};if(T.match(/Edge\/([\d+\.*]+)/)){S=RegExp.$1;};};return S;},getDocumentMode:function(){if(document.documentMode){return document.documentMode;};return 0;},getQuirksMode:function(){if(qx.bom.client.Engine.getName()==G&&parseFloat(qx.bom.client.Engine.getVersion())>=8){return qx.bom.client.Engine.DOCUMENT_MODE===5;}else {return document.compatMode!==a;};},__agents:{"webkit":i,"gecko":p,"mshtml":q,"opera":H}[qx.bom.client.Engine.getName()]},defer:function(V){qx.core.Environment.add(k,V.getName);qx.core.Environment.add(C,V.getVersion);qx.core.Environment.add(K,V.getDocumentMode);qx.core.Environment.add(j,V.getQuirksMode);}});})();(function(){var a="function",b='loadeddata',c="pointerover",d='pause',f="transitionend",g="gecko",h="browser.name",j='timeupdate',k='canplay',m='loadedmetadata',n="css.transition",o="mobile safari",p="return;",q="browser.documentmode",r="safari",s='play',t='ended',u="",v="qx.bom.Event",w='playing',x="mouseover",y="end-event",z="mshtml",A="engine.name",B='progress',C="webkit",D='volumechange',E='seeked',F="on",G="undefined";qx.Bootstrap.define(v,{statics:{addNativeListener:function(K,J,H,I){if(K.addEventListener){K.addEventListener(J,H,!!I);}else if(K.attachEvent){K.attachEvent(F+J,H);}else if(typeof K[F+J]!=G){K[F+J]=H;}else {{};};},removeNativeListener:function(O,N,L,M){if(O.removeEventListener){O.removeEventListener(N,L,!!M);}else if(O.detachEvent){try{O.detachEvent(F+N,L);}catch(e){if(e.number!==-2146828218){throw e;};};}else if(typeof O[F+N]!=G){O[F+N]=null;}else {{};};},getTarget:function(e){return e.target||e.srcElement;},getRelatedTarget:function(e){if(e.relatedTarget!==undefined){if((qx.core.Environment.get(A)==g)){try{e.relatedTarget&&e.relatedTarget.nodeType;}catch(P){return null;};};return e.relatedTarget;}else if(e.fromElement!==undefined&&(e.type===x||e.type===c)){return e.fromElement;}else if(e.toElement!==undefined){return e.toElement;}else {return null;};},supportsEvent:function(Q,Y){var U=qx.core.Environment.get(h);var V=qx.core.Environment.get(A);if(Y.toLowerCase().indexOf(f)!=-1&&V===z&&qx.core.Environment.get(q)>9){return true;};var W=[o,r];if(V===C&&W.indexOf(U)>-1){var R=[b,B,j,E,k,s,w,d,m,t,D];if(R.indexOf(Y.toLowerCase())>-1){return true;};};if(Q!=window&&Y.toLowerCase().indexOf(f)!=-1){var X=qx.core.Environment.get(n);return (X&&X[y]==Y);};var S=F+Y.toLowerCase();var T=(S in Q);if(!T){T=typeof Q[S]==a;if(!T&&Q.setAttribute){Q.setAttribute(S,p);T=typeof Q[S]==a;Q.removeAttribute(S);};};return T;},getEventName:function(ba,bd){var bb=[u].concat(qx.bom.Style.VENDOR_PREFIXES);for(var i=0,l=bb.length;i<l;i++ ){var bc=bb[i].toLowerCase();if(qx.bom.Event.supportsEvent(ba,bc+bd)){return bc?bc+qx.lang.String.firstUp(bd):bd;};};return null;}}});})();(function(){var a="-",b="qx.bom.Style",c="",d='-',e="Webkit",f="ms",g=":",h=";",j="Moz",k="O",m="string",n="Khtml";qx.Bootstrap.define(b,{statics:{VENDOR_PREFIXES:[e,j,k,f,n],__cssName:{},__supports:null,getPropertyName:function(q){var o=document.documentElement.style;if(o[q]!==undefined){return q;};for(var i=0,l=this.VENDOR_PREFIXES.length;i<l;i++ ){var p=this.VENDOR_PREFIXES[i]+qx.lang.String.firstUp(q);if(o[p]!==undefined){return p;};};return null;},getCssName:function(r){var s=this.__cssName[r];if(!s){s=r.replace(/[A-Z]/g,function(t){return (d+t.charAt(0).toLowerCase());});if((/^ms/.test(s))){s=a+s;};this.__cssName[r]=s;};return s;},getAppliedStyle:function(A,x,z,v){var C=qx.bom.Style.getCssName(x);var w=qx.dom.Node.getWindow(A);var u=(v!==false)?[null].concat(this.VENDOR_PREFIXES):[null];for(var i=0,l=u.length;i<l;i++ ){var y=false;var B=u[i]?a+u[i].toLowerCase()+a+z:z;if(qx.bom.Style.__supports){y=qx.bom.Style.__supports.call(w,C,B);}else {A.style.cssText+=C+g+B+h;y=(typeof A.style[x]==m&&A.style[x]!==c);};if(y){return B;};};return null;}},defer:function(D){if(window.CSS&&window.CSS.supports){qx.bom.Style.__supports=window.CSS.supports.bind(window.CSS);}else if(window.supportsCSS){qx.bom.Style.__supports=window.supportsCSS.bind(window);};}});})();(function(){var b="qx.dom.Node",c="";qx.Bootstrap.define(b,{statics:{ELEMENT:1,TEXT:3,DOCUMENT:9,DOCUMENT_FRAGMENT:11,getDocument:function(d){return d.nodeType===this.DOCUMENT?d:d.ownerDocument||d.document;},getWindow:function(e){if(e.nodeType==null){return e;};if(e.nodeType!==this.DOCUMENT){e=e.ownerDocument;};return e.defaultView||e.parentWindow;},getBodyElement:function(f){return this.getDocument(f).body;},isNode:function(g){return !!(g&&g.nodeType!=null);},isElement:function(h){return !!(h&&h.nodeType===this.ELEMENT);},isDocument:function(j){return !!(j&&j.nodeType===this.DOCUMENT);},isDocumentFragment:function(k){return !!(k&&k.nodeType===this.DOCUMENT_FRAGMENT);},isText:function(l){return !!(l&&l.nodeType===this.TEXT);},isWindow:function(m){return !!(m&&m.history&&m.location&&m.document);},isNodeName:function(n,o){if(!o||!n||!n.nodeName){return false;};return o.toLowerCase()==qx.dom.Node.getName(n);},getName:function(p){if(!p||!p.nodeName){return null;};return p.nodeName.toLowerCase();},getText:function(q){if(!q||!q.nodeType){return null;};switch(q.nodeType){case 1:var i,a=[],r=q.childNodes,length=r.length;for(i=0;i<length;i++ ){a[i]=this.getText(r[i]);};return a.join(c);case 2:case 3:case 4:return q.nodeValue;};return null;},isBlockNode:function(s){if(!qx.dom.Node.isElement(s)){return false;};s=qx.dom.Node.getName(s);return /^(body|form|textarea|fieldset|ul|ol|dl|dt|dd|li|div|hr|p|h[1-6]|quote|pre|table|thead|tbody|tfoot|tr|td|th|iframe|address|blockquote)$/.test(s);}}});})();(function(){var a="qx.bom.client.CssTransition",b="E",c="transitionEnd",d="e",e="nd",f="transition",g="css.transition",h="Trans";qx.Bootstrap.define(a,{statics:{getTransitionName:function(){return qx.bom.Style.getPropertyName(f);},getSupport:function(){var name=qx.bom.client.CssTransition.getTransitionName();if(!name){return null;};var i=qx.bom.Event.getEventName(window,c);i=i==c?i.toLowerCase():i;if(!i){i=name+(name.indexOf(h)>0?b:d)+e;};return {name:name,"end-event":i};}},defer:function(j){qx.core.Environment.add(g,j.getSupport);}});})();(function(){var a="ipod",b="pc",c="ps3",d=")",e="device.type",f="psp",g="wii",h="xbox",i="\.",j="iemobile",k="ipad",l="ds",m="(",n="mobile",o="tablet",p="ontouchstart",q="g",r="iphone",s="|",t="qx.bom.client.Device",u="desktop",v="device.name",w="device.touch",x="undefined",y="device.pixelRatio";qx.Bootstrap.define(t,{statics:{__ids:{"Windows Phone":j,"iPod":a,"iPad":k,"iPhone":r,"PSP":f,"PLAYSTATION 3":c,"Nintendo Wii":g,"Nintendo DS":l,"XBOX":h,"Xbox":h},getName:function(){var B=[];for(var A in qx.bom.client.Device.__ids){B.push(A);};var C=new RegExp(m+B.join(s).replace(/\./g,i)+d,q);var z=C.exec(navigator.userAgent);if(z&&z[1]){return qx.bom.client.Device.__ids[z[1]];};return b;},getType:function(){return qx.bom.client.Device.detectDeviceType(navigator.userAgent);},detectDeviceType:function(D){if(qx.bom.client.Device.detectTabletDevice(D)){return o;}else if(qx.bom.client.Device.detectMobileDevice(D)){return n;};return u;},detectMobileDevice:function(E){return /android.+mobile|ip(hone|od)|bada\/|blackberry|BB10|maemo|opera m(ob|in)i|fennec|NetFront|phone|psp|symbian|IEMobile|windows (ce|phone)|xda/i.test(E);},detectTabletDevice:function(G){var H=(/MSIE 10/i.test(G))&&(/ARM/i.test(G))&&!(/windows phone/i.test(G));var F=(!(/android.+mobile|Tablet PC/i.test(G))&&(/Android|ipad|tablet|playbook|silk|kindle|psp/i.test(G)));return H||F;},getDevicePixelRatio:function(){if(typeof window.devicePixelRatio!==x){return window.devicePixelRatio;};return 1;},getTouch:function(){return ((p in window)||window.navigator.maxTouchPoints>0||window.navigator.msMaxTouchPoints>0);}},defer:function(I){qx.core.Environment.add(v,I.getName);qx.core.Environment.add(w,I.getTouch);qx.core.Environment.add(e,I.getType);qx.core.Environment.add(y,I.getDevicePixelRatio);}});})();(function(){var a="foo",b="function",c="event.mouseevent",d="event.help",e="qx.bom.client.Event",f="event.dispatchevent",g="mousewheel",h="event.touch",j="onhelp",k="event.hashchange",l="PointerEvent",m="pointerEnabled",n="event.customevent",o="documentMode",p="ontouchstart",q="mshtml",r="onhashchange",s="event.mousewheel",t="wheel",u="DOMMouseScroll",v="msPointerEnabled",w="event.mspointer";qx.Bootstrap.define(e,{statics:{getTouch:function(){return (p in window);},getMsPointer:function(){if(l in window){return true;};if(m in window.navigator){return window.navigator.pointerEnabled;}else if(v in window.navigator){return window.navigator.msPointerEnabled;};return false;},getHelp:function(){return (j in document);},getHashChange:function(){var x=qx.bom.client.Engine.getName();var y=r in window;return (x!==q&&y)||(x===q&&o in document&&document.documentMode>=8&&y);},getDispatchEvent:function(){return typeof document.dispatchEvent==b;},getCustomEvent:function(){if(!window.CustomEvent){return false;};try{new window.CustomEvent(a);return true;}catch(z){return false;};},getMouseEvent:function(){if(!window.MouseEvent){return false;};try{new window.MouseEvent(a);return true;}catch(A){return false;};},getMouseWheel:function(B){if(!B){B=window;};var E=[B,B.document,B.document.body];var D=B;var C=u;for(var i=0;i<E.length;i++ ){if(qx.bom.Event.supportsEvent(E[i],t)){C=t;D=E[i];break;};if(qx.bom.Event.supportsEvent(E[i],g)){C=g;D=E[i];break;};};return {type:C,target:D};}},defer:function(F){qx.core.Environment.add(h,F.getTouch);qx.core.Environment.add(c,F.getMouseEvent);qx.core.Environment.add(f,F.getDispatchEvent);qx.core.Environment.add(n,F.getCustomEvent);qx.core.Environment.add(w,F.getMsPointer);qx.core.Environment.add(d,F.getHelp);qx.core.Environment.add(k,F.getHashChange);qx.core.Environment.add(s,F.getMouseWheel);}});})();(function(){var a="engine.name",b="event.mspointer",c="device.type",d="env",e="engine.version",f="qx.module.Environment",g="browser.version",h="event.touch",i="device.name",j="browser.quirksmode",k="browser.name",l="browser.documentmode";qx.Bootstrap.define(f,{statics:{get:function(m){return qx.core.Environment.get(m);},add:function(n,o){qx.core.Environment.add(n,o);return this;}},defer:function(p){qx.core.Environment.get(k);qx.core.Environment.get(g);qx.core.Environment.get(j);qx.core.Environment.get(l);qx.core.Environment.get(a);qx.core.Environment.get(e);qx.core.Environment.get(i);qx.core.Environment.get(c);qx.core.Environment.get(h);qx.core.Environment.get(b);qxWeb.$attachAll(this,d);}});})();(function(){var a="qx.lang.normalize.Function",b="ecmascript.function.bind",c="function",d="Function.prototype.bind called on incompatible ";qx.Bootstrap.define(a,{statics:{bind:function(i){var e=Array.prototype.slice;var h=this;if(typeof h!=c){throw new TypeError(d+h);};var f=e.call(arguments,1);var g=function(){if(this instanceof g){var F=function(){};F.prototype=h.prototype;var self=new F;var j=h.apply(self,f.concat(e.call(arguments)));if(Object(j)===j){return j;};return self;}else {return h.apply(i,f.concat(e.call(arguments)));};};return g;}},defer:function(k){if(!qx.core.Environment.get(b)){Function.prototype.bind=k.bind;};}});})();(function(){var a="ecmascript.error.toString",b="qx.lang.normalize.Error",c=": ",d="Error",e="";qx.Bootstrap.define(b,{statics:{toString:function(){var name=this.name||d;var f=this.message||e;if(name===e&&f===e){return d;};if(name===e){return f;};if(f===e){return name;};return name+c+f;}},defer:function(g){if(!qx.core.Environment.get(a)){Error.prototype.toString=g.toString;};}});})();(function(){var a="qx.module.Polyfill";qx.Bootstrap.define(a,{});})();(function(){var a="mshtml",b="engine.name",c="complete",d="Array",f="pointerout",g="pointerover",h="string",n="load",o="left",p="qx.module.Event",q="undefined",r="DOMContentLoaded",s="browser.documentmode",t="*";qx.Bootstrap.define(p,{statics:{__normalizations:{},__hooks:{on:{},off:{}},__isReady:false,ready:function(u){if(document.readyState===c){window.setTimeout(u,1);return;};var v=function(){qx.module.Event.__isReady=true;u();};qxWeb(window).on(n,v);var w=function(){qxWeb(window).off(n,v);u();};if(qxWeb.env.get(b)!==a||qxWeb.env.get(s)>8){qx.bom.Event.addNativeListener(document,r,w);}else {var z=function(){if(qx.module.Event.__isReady){return;};try{document.documentElement.doScroll(o);if(document.body){w();};}catch(A){window.setTimeout(z,100);};};z();};},$registerEventNormalization:function(E,B){if(!qx.lang.Type.isArray(E)){E=[E];};var C=qx.module.Event.__normalizations;for(var i=0,l=E.length;i<l;i++ ){var D=E[i];if(qx.lang.Type.isFunction(B)){if(!C[D]){C[D]=[];};C[D].push(B);};};},$unregisterEventNormalization:function(I,F){if(!qx.lang.Type.isArray(I)){I=[I];};var G=qx.module.Event.__normalizations;for(var i=0,l=I.length;i<l;i++ ){var H=I[i];if(G[H]){qx.lang.Array.remove(G[H],F);};};},$getEventNormalizationRegistry:function(){return qx.module.Event.__normalizations;},$registerEventHook:function(O,L,K){if(!qx.lang.Type.isArray(O)){O=[O];};var M=qx.module.Event.__hooks.on;for(var i=0,l=O.length;i<l;i++ ){var N=O[i];if(qx.lang.Type.isFunction(L)){if(!M[N]){M[N]=[];};M[N].push(L);};};if(!K){return;};var J=qx.module.Event.__hooks.off;for(var i=0,l=O.length;i<l;i++ ){var N=O[i];if(qx.lang.Type.isFunction(K)){if(!J[N]){J[N]=[];};J[N].push(K);};};},$unregisterEventHook:function(U,R,Q){if(!qx.lang.Type.isArray(U)){U=[U];};var S=qx.module.Event.__hooks.on;for(var i=0,l=U.length;i<l;i++ ){var T=U[i];if(S[T]){qx.lang.Array.remove(S[T],R);};};if(!Q){return;};var P=qx.module.Event.__hooks.off;for(var i=0,l=U.length;i<l;i++ ){var T=U[i];if(P[T]){qx.lang.Array.remove(P[T],Q);};};},$getEventHookRegistry:function(){return qx.module.Event.__hooks;}},members:{on:function(bd,bb,bc,W){for(var i=0;i<this.length;i++ ){var V=this[i];var Y=bc||qxWeb(V);var X=qx.module.Event.__hooks.on;var be=X[t]||[];if(X[bd]){be=be.concat(X[bd]);};for(var j=0,m=be.length;j<m;j++ ){be[j](V,bd,bb,bc);};var ba=function(bf,event){var bh=qx.module.Event.__normalizations;var bg=bh[t]||[];if(bh[bd]){bg=bg.concat(bh[bd]);};for(var x=0,y=bg.length;x<y;x++ ){event=bg[x](event,bf,bd);};bb.apply(this,[event]);}.bind(Y,V);ba.original=bb;qx.bom.Event.addNativeListener(V,bd,ba,W);if(!V.$$emitter){V.$$emitter=new qx.event.Emitter();};V.$$lastlistenerId=V.$$emitter.on(bd,ba,Y);V.$$emitter.getEntryById(V.$$lastlistenerId).useCapture=!!W;if(!V.__listener){V.__listener={};};if(!V.__listener[bd]){V.__listener[bd]={};};V.__listener[bd][V.$$lastlistenerId]=ba;if(!bc){if(!V.__ctx){V.__ctx={};};V.__ctx[V.$$lastlistenerId]=Y;};};return this;},off:function(bt,bo,bq,bj){var br=(bo===null&&bq===null);for(var j=0;j<this.length;j++ ){var bi=this[j];if(!bi.__listener){continue;};var bv=[];if(bt!==null){bv.push(bt);}else {for(var bm in bi.__listener){bv.push(bm);};};for(var i=0,l=bv.length;i<l;i++ ){for(var bu in bi.__listener[bv[i]]){var bs=bi.__listener[bv[i]][bu];if(br||bs==bo||bs.original==bo){var bl=typeof bi.__ctx!==q&&bi.__ctx[bu];var bw;if(!bq&&bl){bw=bi.__ctx[bu];};var bp=bi.$$emitter.off(bv[i],bs,bw||bq);if(br||bs.original==bo){qx.bom.Event.removeNativeListener(bi,bv[i],bs,bj);};if(bp!==null){delete bi.__listener[bv[i]][bu];};if(bl){delete bi.__ctx[bu];};};};var bk=qx.module.Event.__hooks.off;var bn=bk[t]||[];if(bk[bt]){bn=bn.concat(bk[bt]);};for(var k=0,m=bn.length;k<m;k++ ){bn[k](bi,bt,bo,bq);};};};return this;},allOff:function(bx){return this.off(bx||null,null,null);},offById:function(bz){var by=this[0].$$emitter.getEntryById(bz);return this.off(by.name,by.listener.original,by.ctx,by.useCapture);},emit:function(bA,bB){for(var j=0;j<this.length;j++ ){var bC=this[j];if(bC.$$emitter){bC.$$emitter.emit(bA,bB);};};return this;},once:function(bE,bD,bG){var self=this;var bF=function(bH){self.off(bE,bF,bG);bD.call(this,bH);};this.on(bE,bF,bG);return this;},hasListener:function(bL,bJ,bK){if(!this[0]||!this[0].$$emitter||!this[0].$$emitter.getListeners()[bL]){return false;};if(bJ){var bM=this[0].$$emitter.getListeners()[bL];for(var i=0;i<bM.length;i++ ){var bI=false;if(bM[i].listener==bJ){bI=true;};if(bM[i].listener.original&&bM[i].listener.original==bJ){bI=true;};if(bI){if(bK!==undefined){if(bM[i].ctx===bK){return true;};}else {return true;};};};return false;};return this[0].$$emitter.getListeners()[bL].length>0;},copyEventsTo:function(bT){var bR=this.concat();var bS=bT.concat();for(var i=bR.length-1;i>=0;i-- ){var bO=bR[i].getElementsByTagName(t);for(var j=0;j<bO.length;j++ ){bR.push(bO[j]);};};for(var i=bS.length-1;i>=0;i-- ){var bO=bS[i].getElementsByTagName(t);for(var j=0;j<bO.length;j++ ){bS.push(bO[j]);};};bS.forEach(function(bU){bU.$$emitter=null;});for(var i=0;i<bR.length;i++ ){var bN=bR[i];if(!bN.$$emitter){continue;};var bP=bN.$$emitter.getListeners();for(var name in bP){for(var j=bP[name].length-1;j>=0;j-- ){var bQ=bP[name][j].listener;if(bQ.original){bQ=bQ.original;};qxWeb(bS[i]).on(name,bQ,bP[name][j].ctx);};};};},hover:function(bV,bW){this.on(g,bV,this);if(qx.lang.Type.isFunction(bW)){this.on(f,bW,this);};return this;},onMatchTarget:function(bY,ca,cc,cb){cb=cb!==undefined?cb:this;var bX=function(e){var cd=qxWeb(e.getTarget());var ce=typeof ca==h?this.find(ca):qxWeb(ca);if(cd.is(ca)){cc.call(cb,cd,qxWeb.object.clone(e));}else {for(var i=0,l=ce.length;i<l;i++ ){if(cd.isChildOf(qxWeb(ce[i]))){cc.call(cb,cd,qxWeb.object.clone(e));break;};};};};this.forEach(function(cf){var cg={target:ca,type:bY,listener:bX,callback:cc,context:cb};if(!cf.$$matchTargetInfo){cf.$$matchTargetInfo=[];};cf.$$matchTargetInfo.push(cg);});this.on(bY,bX);return this;},offMatchTarget:function(ch,ci,ck,cj){cj=cj!==undefined?cj:this;this.forEach(function(cl){if(cl.$$matchTargetInfo&&qxWeb.type.get(cl.$$matchTargetInfo)==d){var cm=cl.$$matchTargetInfo;for(var i=cm.length-1;i>=0;i-- ){var cn=cm[i];if(cn.type==ch&&cn.callback==ck&&cn.context==cj){this.off(ch,cn.listener);cm.splice(i,1);};};if(cm.length===0){cl.$$matchTargetInfo=null;};};},this);return this;},hasMatchListener:function(ct,co,cp,cq){cq=cq!==undefined?cq:this;for(var j=0,l=this.length;j<l;j++ ){var cs=this[j].$$matchTargetInfo||[];for(var i=cs.length-1;i>=0;i-- ){var cr=cs[i];if(cr.type==ct&&cr.callback==cp&&cr.target==co&&cr.context==cq){return true;};};};return false;}},defer:function(cu){qxWeb.$attachAll(this);qxWeb.$attachStatic({"$registerEventNormalization":cu.$registerEventNormalization,"$unregisterEventNormalization":cu.$unregisterEventNormalization,"$getEventNormalizationRegistry":cu.$getEventNormalizationRegistry,"$registerEventHook":cu.$registerEventHook,"$unregisterEventHook":cu.$unregisterEventHook,"$getEventHookRegistry":cu.$getEventHookRegistry});}});})();(function(){var a="qx.module.event.PointerHandler",b="pointerup",c="event.dispatchevent",d="gesturemove",e="pointerover",f="gesturebegin",g="pointerdown",h="pointermove",i="gesturefinish",j="qx.event.handler.Pointer",k="gesturecancel",l="pointercancel",m="pointerout";qx.Bootstrap.define(a,{statics:{TYPES:[h,e,m,g,b,l,f,d,i,k],register:function(o,n){if(!o.$$pointerHandler){if(!qx.core.Environment.get(c)){if(!o.$$emitter){o.$$emitter=new qx.event.Emitter();};};o.$$pointerHandler=new qx.event.handler.PointerCore(o,o.$$emitter);};},unregister:function(r){if(r.$$pointerHandler){if(r.$$pointerHandler.classname===j){return;};var p=r.$$emitter.getListeners();for(var q in p){if(qx.module.event.PointerHandler.TYPES.indexOf(q)!==-1){if(p[q].length>0){return;};};};r.$$pointerHandler.dispose();r.$$pointerHandler=undefined;};}},defer:function(s){qxWeb.$registerEventHook(s.TYPES,s.register,s.unregister);}});})();(function(){var a="qx.event.Emitter",b="*";qx.Bootstrap.define(a,{extend:Object,statics:{__storage:[]},members:{__listener:null,__any:null,on:function(name,c,d){var e=qx.event.Emitter.__storage.length;this.__getStorage(name).push({listener:c,ctx:d,id:e,name:name});qx.event.Emitter.__storage.push({name:name,listener:c,ctx:d});return e;},once:function(name,f,g){var h=qx.event.Emitter.__storage.length;this.__getStorage(name).push({listener:f,ctx:g,once:true,id:h});qx.event.Emitter.__storage.push({name:name,listener:f,ctx:g});return h;},off:function(name,m,k){var l=this.__getStorage(name);for(var i=l.length-1;i>=0;i-- ){var n=l[i];if(n.listener==m&&n.ctx==k){l.splice(i,1);qx.event.Emitter.__storage[n.id]=null;return n.id;};};return null;},offById:function(p){var o=qx.event.Emitter.__storage[p];if(o){this.off(o.name,o.listener,o.ctx);};return null;},addListener:function(name,q,r){return this.on(name,q,r);},addListenerOnce:function(name,s,t){return this.once(name,s,t);},removeListener:function(name,u,v){this.off(name,u,v);},removeListenerById:function(w){this.offById(w);},emit:function(name,A){var x=this.__getStorage(name).concat();var y=[];for(var i=0;i<x.length;i++ ){var z=x[i];z.listener.call(z.ctx,A);if(z.once){y.push(z);};};y.forEach(function(B){var C=this.__getStorage(name);var D=C.indexOf(B);C.splice(D,1);}.bind(this));x=this.__getStorage(b);for(var i=x.length-1;i>=0;i-- ){var z=x[i];z.listener.call(z.ctx,A);};},getListeners:function(){return this.__listener;},getEntryById:function(F){for(var name in this.__listener){var E=this.__listener[name];for(var i=0,j=E.length;i<j;i++ ){if(E[i].id===F){return E[i];};};};},__getStorage:function(name){if(this.__listener==null){this.__listener={};};if(this.__listener[name]==null){this.__listener[name]=[];};return this.__listener[name];}}});})();(function(){var a="touchmove",b="os.name",c="mousedown",d="event.dispatchevent",e="MSPointerDown",f="gesturemove",g="pointerover",h="touch",k="mouseout",m="ms",n="Processed",o="pointercancel",p="pointerleave",q="touchstart",r="pointerenter",s="mouse",t="event.mspointer",u="mousemove",v="MSPointerCancel",w="gesturefinish",z="browser.documentmode",A="pointerup",B="touchend",C="osx",D="mouseover",E="$$qx",F="pointerdown",G="MSPointerUp",H="pointermove",I="MSPointerOver",J="gecko",K="mshtml",L="engine.name",M="mouseup",N="touchcancel",O="contextmenu",P="gesturecancel",Q="MSPointerMove",R="MSPointerOut",S="gesturebegin",T="qx.event.handler.PointerCore",U=".",V="device.touch",W="pointerout";qx.Bootstrap.define(T,{extend:Object,statics:{MOUSE_TO_POINTER_MAPPING:{mousedown:F,mouseup:A,mousemove:H,mouseout:W,mouseover:g},TOUCH_TO_POINTER_MAPPING:{touchstart:F,touchend:A,touchmove:H,touchcancel:o},MSPOINTER_TO_POINTER_MAPPING:{MSPointerDown:F,MSPointerMove:H,MSPointerUp:A,MSPointerCancel:o,MSPointerLeave:p,MSPointerEnter:r,MSPointerOver:g,MSPointerOut:W},POINTER_TO_GESTURE_MAPPING:{pointerdown:S,pointerup:w,pointercancel:P,pointermove:f},LEFT_BUTTON:(qx.core.Environment.get(L)==K&&qx.core.Environment.get(z)<=8)?1:0,SIM_MOUSE_DISTANCE:25,SIM_MOUSE_DELAY:2500,__lastTouch:null},construct:function(ba,bb){this.__defaultTarget=ba;this.__emitter=bb;this.__eventNames=[];this.__buttonStates=[];this.__activeTouches=[];this._processedFlag=E+this.classname.substr(this.classname.lastIndexOf(U)+1)+n;var Y=qx.core.Environment.get(L);var X=parseInt(qx.core.Environment.get(z),10);if(Y==K&&X==10){this.__eventNames=[e,Q,G,v,I,R,F,H,A,o,g,W];this._initPointerObserver();}else {if(qx.core.Environment.get(t)){this.__nativePointerEvents=true;};this.__eventNames=[F,H,A,o,g,W];this._initPointerObserver();};if(!qx.core.Environment.get(t)){if(qx.core.Environment.get(V)){this.__eventNames=[q,B,a,N];this._initObserver(this._onTouchEvent);};this.__eventNames=[c,M,u,D,k,O];this._initObserver(this._onMouseEvent);};},members:{__defaultTarget:null,__emitter:null,__eventNames:null,__nativePointerEvents:false,__wrappedListener:null,__lastButtonState:0,__buttonStates:null,__primaryIdentifier:null,__activeTouches:null,_processedFlag:null,_initPointerObserver:function(){this._initObserver(this._onPointerEvent);},_initObserver:function(bc,bd){this.__wrappedListener=qx.lang.Function.listener(bc,this);this.__eventNames.forEach(function(be){if(bd&&qx.dom.Node.isDocument(this.__defaultTarget)){if(!this.__defaultTarget.$$emitter){this.__defaultTarget.$$emitter=new qx.event.Emitter();};this.__defaultTarget.$$emitter.on(be,this.__wrappedListener);}else {qx.bom.Event.addNativeListener(this.__defaultTarget,be,this.__wrappedListener);};}.bind(this));},_onPointerEvent:function(bh){if(!qx.core.Environment.get(t)||(qx.core.Environment.get(z)===10&&bh.type.toLowerCase().indexOf(m)==-1)){return;};if(!this.__nativePointerEvents){bh.stopPropagation();};var bf=qx.event.handler.PointerCore.MSPOINTER_TO_POINTER_MAPPING[bh.type]||bh.type;var bi=qx.bom.Event.getTarget(bh);var bg=new qx.event.type.dom.Pointer(bf,bh);this._fireEvent(bg,bf,bi);},_onTouchEvent:function(bl){if(bl[this._processedFlag]){return;};bl[this._processedFlag]=true;var bm=qx.event.handler.PointerCore.TOUCH_TO_POINTER_MAPPING[bl.type];var bo=bl.changedTouches;this._determineActiveTouches(bl.type,bo);if(bl.touches.length<this.__activeTouches.length){for(var i=this.__activeTouches.length-1;i>=0;i-- ){var bq=new qx.event.type.dom.Pointer(o,bl,{identifier:this.__activeTouches[i].identifier,target:bl.target,pointerType:h,pointerId:this.__activeTouches[i].identifier+2});this._fireEvent(bq,o,bl.target);};this.__primaryIdentifier=null;this.__activeTouches=[];return;};if(bl.type==q&&this.__primaryIdentifier===null){this.__primaryIdentifier=bo[0].identifier;};for(var i=0,l=bo.length;i<l;i++ ){var bp=bo[i];var bn=bl.view.document.elementFromPoint(bp.clientX,bp.clientY)||bl.target;var bk={clientX:bp.clientX,clientY:bp.clientY,pageX:bp.pageX,pageY:bp.pageY,identifier:bp.identifier,screenX:bp.screenX,screenY:bp.screenY,target:bn,pointerType:h,pointerId:bp.identifier+2};if(bl.type==q){var bj=new qx.event.type.dom.Pointer(g,bl,bk);this._fireEvent(bj,g,bk.target);};if(bp.identifier==this.__primaryIdentifier){bk.isPrimary=true;bk.button=0;bk.buttons=1;qx.event.handler.PointerCore.__lastTouch={"x":bp.clientX,"y":bp.clientY,"time":new Date().getTime()};};var br=new qx.event.type.dom.Pointer(bm,bl,bk);this._fireEvent(br,bm,bk.target);if(bl.type==B||bl.type==N){var bs=new qx.event.type.dom.Pointer(W,bl,bk);this._fireEvent(bs,W,bl.target);if(this.__primaryIdentifier==bp.identifier){this.__primaryIdentifier=null;};};};},_onMouseEvent:function(bt){if(bt[this._processedFlag]){return;};bt[this._processedFlag]=true;if(this._isSimulatedMouseEvent(bt.clientX,bt.clientY)){return;};if(bt.type==c){this.__buttonStates[bt.which]=1;}else if(bt.type==M){if(qx.core.Environment.get(b)==C&&qx.core.Environment.get(L)==J){if(this.__buttonStates[bt.which]!=1&&bt.ctrlKey){this.__buttonStates[1]=0;};};this.__buttonStates[bt.which]=0;};var bv=qx.event.handler.PointerCore.MOUSE_TO_POINTER_MAPPING[bt.type];var bu=qx.bom.Event.getTarget(bt);var bw=qx.lang.Array.sum(this.__buttonStates);var bz={pointerType:s,pointerId:1};if(this.__lastButtonState!=bw&&bw!==0&&this.__lastButtonState!==0){var bx=new qx.event.type.dom.Pointer(H,bt,bz);this._fireEvent(bx,H,bu);};this.__lastButtonState=bw;if(bt.type==c&&bw>1){return;};if(bt.type==M&&bw>0){return;};if(bt.type==O){this.__buttonStates[bt.which]=0;return;};var by=new qx.event.type.dom.Pointer(bv,bt,bz);this._fireEvent(by,bv,bu);},_determineActiveTouches:function(bD,bC){if(bD==q){for(var i=0;i<bC.length;i++ ){this.__activeTouches.push(bC[i]);};}else if(bD==B||bD==N){var bA=[];for(var i=0;i<this.__activeTouches.length;i++ ){var bB=true;for(var j=0;j<bC.length;j++ ){if(this.__activeTouches[i].identifier==bC[j].identifier){bB=false;break;};};if(bB){bA.push(this.__activeTouches[i]);};};this.__activeTouches=bA;};},_isSimulatedMouseEvent:function(x,y){var bF=qx.event.handler.PointerCore.__lastTouch;if(bF){var bG=new Date().getTime()-bF.time;var bE=qx.event.handler.PointerCore.SIM_MOUSE_DISTANCE;var bI=Math.abs(x-qx.event.handler.PointerCore.__lastTouch.x);var bH=Math.abs(y-qx.event.handler.PointerCore.__lastTouch.y);if(bG<qx.event.handler.PointerCore.SIM_MOUSE_DELAY){if(bI<bE||bH<bE){return true;};};};return false;},_stopObserver:function(){for(var i=0;i<this.__eventNames.length;i++ ){qx.bom.Event.removeNativeListener(this.__defaultTarget,this.__eventNames[i],this.__wrappedListener);};},_fireEvent:function(bK,bJ,bL){bL=bL||bK.target;bJ=bJ||bK.type;var bM;if((bK.pointerType!==s||bK.button<=qx.event.handler.PointerCore.LEFT_BUTTON)&&(bJ==F||bJ==A||bJ==H)){bM=new qx.event.type.dom.Pointer(qx.event.handler.PointerCore.POINTER_TO_GESTURE_MAPPING[bJ],bK);qx.event.type.dom.Pointer.normalize(bM);bM.srcElement=bL;};if(qx.core.Environment.get(d)){if(!this.__nativePointerEvents){bL.dispatchEvent(bK);};if(bM){bL.dispatchEvent(bM);};}else {bK.srcElement=bL;while(bL){if(bL.$$emitter){bK.currentTarget=bL;if(!bK._stopped){bL.$$emitter.emit(bJ,bK);};if(bM&&!bM._stopped){bM.currentTarget=bL;bL.$$emitter.emit(bM.type,bM);};};bL=bL.parentNode;};};},dispose:function(){this._stopObserver();this.__defaultTarget=this.__emitter=null;}}});})();(function(){var a="qx.event.type.dom.Custom",b="UIEvents",c="function",d="event.customevent",e="object";qx.Bootstrap.define(a,{extend:Object,statics:{PROPERTIES:{bubbles:false,cancelable:true}},construct:function(f,g,h){this._type=f;this._event=this._createEvent();this._initEvent(g,h);this._event._original=g;this._event.preventDefault=function(){if(this._original.preventDefault){this._original.preventDefault();}else {try{this._original.returnValue=false;}catch(i){};};};if(this._event.stopPropagation){this._event._nativeStopPropagation=this._event.stopPropagation;};this._event.stopPropagation=function(){this._stopped=true;if(this._nativeStopPropagation){this._original.stopPropagation();this._nativeStopPropagation();}else {this._original.cancelBubble=true;};};return this._event;},members:{_type:null,_event:null,_createEvent:function(){var j;if(qx.core.Environment.get(d)){j=new window.CustomEvent(this._type);}else if(typeof document.createEvent==c){j=document.createEvent(b);}else if(typeof document.createEventObject==e){j={};j.type=this._type;};return j;},_initEvent:function(k,m){m=m||{};var l=qx.lang.Object.clone(qx.event.type.dom.Custom.PROPERTIES);for(var n in m){l[n]=m[n];};if(this._event.initEvent){this._event.initEvent(this._type,l.bubbles,l.cancelable);};for(var n in l){this._event[n]=l[n];};}}});})();(function(){var a="bubbles",b="event.mouseevent",c="getScreenLeft",d="getPointerType",e="touch",f="ctrlKey",g="altKey",h="gecko",j="view",k="os.name",m="button",n="string",o="relatedTarget",p="buttons",q="mouse",r="clientX",s="qx.event.type.dom.Pointer",t="UIEvents",u="ios",v="pageY",w="cancelable",x="screenX",y="shiftKey",z="",A="number",B="detail",C="toElement",D="fromElement",E="getViewportLeft",F="function",G="clientY",H="os.version",I="engine.name",J="undefined",K="getViewportTop",L="screenY",M="getScreenTop",N="pen",O="metaKey",P="pageX",Q="object",R="getDocumentTop",S="which",T="getDocumentLeft";qx.Bootstrap.define(s,{extend:qx.event.type.dom.Custom,statics:{MOUSE_PROPERTIES:[a,w,j,B,x,L,r,G,P,v,f,g,y,O,m,S,o,D,C],POINTER_PROPERTIES:{pointerId:1,width:0,height:0,pressure:0.5,tiltX:0,tiltY:0,pointerType:z,isPrimary:false},READONLY_PROPERTIES:[],BIND_METHODS:[d,E,K,T,R,c,M],getPointerType:function(){if(typeof this.pointerType==n){return this.pointerType;};if(typeof this.pointerType==A){if(this.pointerType==this.MSPOINTER_TYPE_MOUSE){return q;};if(this.pointerType==this.MSPOINTER_TYPE_PEN){return N;};if(this.pointerType==this.MSPOINTER_TYPE_TOUCH){return e;};};return z;},getViewportLeft:function(){return this.clientX;},getViewportTop:function(){return this.clientY;},getDocumentLeft:function(){if(this.pageX!==undefined){return this.pageX;}else {var U=qx.dom.Node.getWindow(this.srcElement);return this.clientX+qx.bom.Viewport.getScrollLeft(U);};},getDocumentTop:function(){if(this.pageY!==undefined){return this.pageY;}else {var V=qx.dom.Node.getWindow(this.srcElement);return this.clientY+qx.bom.Viewport.getScrollTop(V);};},getScreenLeft:function(){return this.screenX;},getScreenTop:function(){return this.screenY;},normalize:function(event){var W=qx.event.type.dom.Pointer.BIND_METHODS;for(var i=0,l=W.length;i<l;i++ ){if(typeof event[W[i]]!=F){event[W[i]]=qx.event.type.dom.Pointer[W[i]].bind(event);};};}},construct:function(X,Y,ba){return qx.event.type.dom.Custom.call(this,X,Y,ba);},members:{_createEvent:function(){var bb;if(qx.core.Environment.get(b)){bb=new window.MouseEvent(this._type);}else if(typeof document.createEvent==F){bb=document.createEvent(t);}else if(typeof document.createEventObject==Q){bb={};bb.type=this._type;};return bb;},_initEvent:function(bc,bd){bd=bd||{};var bg=this._event;var bh={};qx.event.type.dom.Pointer.normalize(bc);Object.keys(qx.event.type.dom.Pointer.POINTER_PROPERTIES).concat(qx.event.type.dom.Pointer.MOUSE_PROPERTIES).forEach(function(bi){if(typeof bd[bi]!==J){bh[bi]=bd[bi];}else if(typeof bc[bi]!==J){bh[bi]=bc[bi];}else if(typeof qx.event.type.dom.Pointer.POINTER_PROPERTIES[bi]!==J){bh[bi]=qx.event.type.dom.Pointer.POINTER_PROPERTIES[bi];};});var bf;switch(bc.which){case 1:bf=1;break;case 2:bf=4;break;case 3:bf=2;break;default:bf=0;};if(bf!==undefined){bh.buttons=bf;bh.pressure=bf?0.5:0;};if(bg.initMouseEvent){bg.initMouseEvent(this._type,bh.bubbles,bh.cancelable,bh.view,bh.detail,bh.screenX,bh.screenY,bh.clientX,bh.clientY,bh.ctrlKey,bh.altKey,bh.shiftKey,bh.metaKey,bh.button,bh.relatedTarget);}else if(bg.initUIEvent){bg.initUIEvent(this._type,bh.bubbles,bh.cancelable,bh.view,bh.detail);};for(var be in bh){if(bg[be]!==bh[be]&&qx.event.type.dom.Pointer.READONLY_PROPERTIES.indexOf(be)===-1){bg[be]=bh[be];};};switch(bg.pointerType){case bc.MSPOINTER_TYPE_MOUSE:bg.pointerType=q;break;case bc.MSPOINTER_TYPE_PEN:bg.pointerType=N;break;case bc.MSPOINTER_TYPE_TOUCH:bg.pointerType=e;break;};if(bg.pointerType==q){bg.isPrimary=true;};}},defer:function(bj){if(qx.core.Environment.get(I)==h){bj.READONLY_PROPERTIES.push(p);}else if(qx.core.Environment.get(k)==u&&parseFloat(qx.core.Environment.get(H))>=8){bj.READONLY_PROPERTIES=bj.READONLY_PROPERTIES.concat(bj.MOUSE_PROPERTIES);};}});})();(function(){var a="ios",b="os.name",c="undefined",d="qx.bom.Viewport";qx.Bootstrap.define(d,{statics:{getWidth:function(e){var e=e||window;var f=e.document;return qx.bom.Document.isStandardMode(e)?f.documentElement.clientWidth:f.body.clientWidth;},getHeight:function(g){var g=g||window;var h=g.document;if(qx.core.Environment.get(b)==a&&window.innerHeight!=h.documentElement.clientHeight){return window.innerHeight;};return qx.bom.Document.isStandardMode(g)?h.documentElement.clientHeight:h.body.clientHeight;},getScrollLeft:function(i){var i=i?i:window;if(typeof i.pageXOffset!==c){return i.pageXOffset;};var j=i.document;return j.documentElement.scrollLeft||j.body.scrollLeft;},getScrollTop:function(k){var k=k?k:window;if(typeof k.pageYOffset!==c){return k.pageYOffset;};var l=k.document;return l.documentElement.scrollTop||l.body.scrollTop;},__getOrientationNormalizer:function(m){var o=this.getWidth(m)>this.getHeight(m)?90:0;var n=m.orientation;if(n==null||Math.abs(n%180)==o){return {"-270":90,"-180":180,"-90":-90,"0":0,"90":90,"180":180,"270":-90};}else {return {"-270":180,"-180":-90,"-90":0,"0":90,"90":180,"180":-90,"270":0};};},__orientationNormalizer:null,getOrientation:function(p){var p=p||window.top;var q=p.orientation;if(q==null){q=this.getWidth(p)>this.getHeight(p)?90:0;}else {if(this.__orientationNormalizer==null){this.__orientationNormalizer=this.__getOrientationNormalizer(p);};q=this.__orientationNormalizer[q];};return q;},isLandscape:function(r){var s=this.getOrientation(r);return s===-90||s===90;}}});})();(function(){var a="engine.name",b="CSS1Compat",c="position:absolute;width:0;height:0;width:1",d="engine.version",e="qx.bom.Document",f="1px",g="div";qx.Bootstrap.define(e,{statics:{isQuirksMode:qx.core.Environment.select(a,{"mshtml":function(h){if(qx.core.Environment.get(d)>=8){return (h||window).document.documentMode===5;}else {return (h||window).document.compatMode!==b;};},"webkit":function(i){if(document.compatMode===undefined){var j=(i||window).document.createElement(g);j.style.cssText=c;return j.style.width===f?true:false;}else {return (i||window).document.compatMode!==b;};},"default":function(k){return (k||window).document.compatMode!==b;}}),isStandardMode:function(l){return !this.isQuirksMode(l);},getWidth:function(m){var o=(m||window).document;var n=qx.bom.Viewport.getWidth(m);var scroll=this.isStandardMode(m)?o.documentElement.scrollWidth:o.body.scrollWidth;return Math.max(scroll,n);},getHeight:function(p){var r=(p||window).document;var q=qx.bom.Viewport.getHeight(p);var scroll=this.isStandardMode(p)?r.documentElement.scrollHeight:r.body.scrollHeight;return Math.max(scroll,q);}}});})();(function(){var a="function",b="*",c="getRelatedTarget",d="getType",e="qx.module.event.Native",f="preventDefault",g="getTarget",h="stopPropagation";qx.Bootstrap.define(e,{statics:{TYPES:[b],FORWARD_METHODS:[g,c],BIND_METHODS:[f,h,d],preventDefault:function(){try{this.keyCode=0;}catch(j){};this.returnValue=false;},stopPropagation:function(){this.cancelBubble=true;},getType:function(){return this._type||this.type;},getTarget:function(){},getRelatedTarget:function(){},getCurrentTarget:function(){},normalize:function(event,n){if(!event){return event;};var m=qx.module.event.Native.FORWARD_METHODS;for(var i=0,l=m.length;i<l;i++ ){event[m[i]]=qx.bom.Event[m[i]].bind(null,event);};var k=qx.module.event.Native.BIND_METHODS;for(var i=0,l=k.length;i<l;i++ ){if(typeof event[k[i]]!=a){event[k[i]]=qx.module.event.Native[k[i]].bind(event);};};event.getCurrentTarget=function(){return event.currentTarget||n;};return event;}},defer:function(o){qxWeb.$registerEventNormalization(o.TYPES,o.normalize);}});})();(function(){var a="-",b="*[data-qx-class]",c="qx.ui.website.Widget",d="*",f="qx-",g="config",h="data-qx-class",i="$$qx-widget-initialized",j="The collection must not contain more than one element.",k="-context",l="qx",m="disabled",n="templates",o=".",p="qx-widget",q="_",r="$$storage_";qx.Bootstrap.define(c,{extend:qxWeb,statics:{widget:function(){var s=new qx.ui.website.Widget(this);s.init();return s;},create:function(t){return new qx.ui.website.Widget(qxWeb.create(t));},initWidgets:function(v){var u=qxWeb(b);if(v){u=u.filter(v);};u._forEachElementWrapped(function(w){w.init();});},toWidgetCollection:function(){var x=this.toArray().map(function(z){return qxWeb(z);});Array.prototype.unshift.call(x,null);var y=qx.core.Wrapper.bind.apply(qx.core.Wrapper,x);return new y();}},construct:function(C,B){var A=qxWeb.call(this,C,B);if(A.length>1){throw new Error(j);};Array.prototype.push.apply(this,Array.prototype.slice.call(A,0,A.length));},members:{__cssPrefix:null,init:function(){if(this.getProperty(i)){return false;};this.setAttribute(h,this.classname);this.addClass(p);this.addClass(this.getCssPrefix());this.setProperty(i,true);this[0].$widget=this;return true;},getCssPrefix:function(){if(!this.__cssPrefix){var D=this.classname.split(o);this.__cssPrefix=f+D[D.length-1].toLowerCase();};return this.__cssPrefix;},setEnabled:function(E){this.setAttribute(m,!E);this.find(d).setAttribute(m,!E);return this;},getEnabled:function(){return !this.getAttribute(m);},setTemplate:function(name,F){return this._setData(n,name,F);},setConfig:function(name,G){return this._setData(g,name,G);},_setData:function(H,name,I){if(!this[r+H]){this[r+H]={};};this[r+H][name]=I;return this;},getTemplate:function(name){return this._getData(n,name);},getConfig:function(name){return this._getData(g,name);},_getData:function(K,name){var L=this[r+K];var J;if(L){J=L[name];};if(J===undefined&&K==g){var M=l+qxWeb.string.firstUp(K)+qxWeb.string.firstUp(name);J=this.getData(M);if(!this[0]||(!this[0].dataset&&J===null)){J=undefined;};try{J=JSON.parse(J);}catch(e){};};if(J===undefined&&this.constructor[q+K]){return this.constructor[q+K][name];};return J;},render:function(){return this;},dispose:function(){this.removeAttribute(h);this.setProperty(g,undefined);this.setProperty(n,undefined);var N=this.classname.replace(/\./g,a)+k;this.setProperty(N,undefined);this.setProperty(i,undefined);this.removeClass(p);this.removeClass(this.getCssPrefix());for(var name in this.constructor.$$events){this.allOff(name);};this[0].$widget=null;return qxWeb.$init(this,qxWeb);}},defer:function(O){qxWeb.$attach({widget:O.widget,toWidgetCollection:O.toWidgetCollection});qxWeb.$attachStatic({initWidgets:O.initWidgets});}});})();(function(){var a="qx.core.Wrapper";qx.Bootstrap.define(a,{extend:Array,construct:function(){for(var i=0,l=arguments.length;i<l;i++ ){this.push(arguments[i]);};var b=arguments[0];for(var name in b){if(this[name]!==undefined){continue;};if(b[name] instanceof Function){this[name]=function(name){var d;var c=Array.prototype.slice.call(arguments,0);c.shift();this.forEach(function(e){var f=e[name].apply(e,c);if(d===undefined){d=f;};});if(d===this[0]){return this;};return d;}.bind(this,name);}else {Object.defineProperty(this,name,{enumerable:true,get:function(name){return this[name];}.bind(b,name),set:function(name,g){this.forEach(function(h){h[name]=g;});}.bind(this,name)});};};}});})();(function(){var a="swipe",b="function",c="getDistance",d="getStartTime",e="getDirection",f="getAxis",g="getDuration",h="getVelocity",j="qx.module.event.Swipe";qx.Bootstrap.define(j,{statics:{TYPES:[a],BIND_METHODS:[d,g,f,e,h,c],getStartTime:function(){return this._original.swipe.startTime;},getDuration:function(){return this._original.swipe.duration;},getAxis:function(){return this._original.swipe.axis;},getDirection:function(){return this._original.swipe.direction;},getVelocity:function(){return this._original.swipe.velocity;},getDistance:function(){return this._original.swipe.distance;},normalize:function(event,m){if(!event){return event;};var k=qx.module.event.Swipe.BIND_METHODS;for(var i=0,l=k.length;i<l;i++ ){if(typeof event[k[i]]!=b){event[k[i]]=qx.module.event.Swipe[k[i]].bind(event);};};return event;}},defer:function(n){qxWeb.$registerEventNormalization(qx.module.event.Swipe.TYPES,n.normalize);}});})();(function(){var a="swipe",b="pinch",c="event.dispatchevent",d="longtap",e="rotate",f="track",g="trackstart",h="trackend",i="tap",j="qx.module.event.GestureHandler",k="roll",l="dbltap";qx.Bootstrap.define(j,{statics:{TYPES:[i,d,a,l,f,g,h,k,e,b],register:function(n,m){if(!n.$$gestureHandler){if(!qx.core.Environment.get(c)){if(!n.$$emitter){n.$$emitter=new qx.event.Emitter();};};n.$$gestureHandler=new qx.event.handler.GestureCore(n,n.$$emitter);};},unregister:function(q){if(q.$$gestureHandler){var o=q.$$emitter.getListeners();for(var p in o){if(qx.module.event.GestureHandler.TYPES.indexOf(p)!==-1){if(o[p].length>0){return;};};};q.$$gestureHandler.dispose();q.$$gestureHandler=undefined;};}},defer:function(r){qxWeb.$registerEventHook(r.TYPES,r.register,r.unregister);}});})();(function(){var a="swipe",b="pinch",c="event.dispatchevent",d="gesturemove",e="touch",f="longtap",g="event.mousewheel",h="roll",i="dblclick",j="wheel",k="rotate",l="trackstart",m="gesturefinish",n="y",o="browser.documentmode",p="dbltap",q="qx.event.handler.GestureCore",r="right",s="mshtml",t="engine.name",u="gesturecancel",v="gesturebegin",w="track",z="trackend",A="left",B="tap",C="down",D="x",E="up";qx.Bootstrap.define(q,{extend:Object,statics:{TYPES:[B,a,f,p,w,l,z,k,b,h],GESTURE_EVENTS:[v,m,d,u],TAP_MAX_DISTANCE:{"touch":40,"mouse":5,"pen":20},DOUBLETAP_MAX_DISTANCE:{"touch":10,"mouse":4,"pen":10},SWIPE_DIRECTION:{x:[A,r],y:[E,C]},LONGTAP_TIME:500,DOUBLETAP_TIME:500,ROLL_FACTOR:18},construct:function(F,G){this.__defaultTarget=F;this.__emitter=G;this.__gesture={};this.__lastTap={};this.__stopMomentum={};this._initObserver();},members:{__defaultTarget:null,__emitter:null,__gesture:null,__eventName:null,__primaryTarget:null,__isMultiPointerGesture:null,__initialAngle:null,__lastTap:null,__rollImpulseId:null,__stopMomentum:null,__initialDistance:null,_initObserver:function(){qx.event.handler.GestureCore.GESTURE_EVENTS.forEach(function(I){qxWeb(this.__defaultTarget).on(I,this.checkAndFireGesture,this);}.bind(this));if(qx.core.Environment.get(t)==s&&qx.core.Environment.get(o)<9){qxWeb(this.__defaultTarget).on(i,this._onDblClick,this);};var H=qx.core.Environment.get(g);qxWeb(H.target).on(H.type,this._fireRoll,this);},_stopObserver:function(){qx.event.handler.GestureCore.GESTURE_EVENTS.forEach(function(K){qxWeb(this.__defaultTarget).off(K,this.checkAndFireGesture,this);}.bind(this));if(qx.core.Environment.get(t)==s&&qx.core.Environment.get(o)<9){qxWeb(this.__defaultTarget).off(i,this._onDblClick,this);};var J=qx.core.Environment.get(g);qxWeb(J.target).off(J.type,this._fireRoll,this);},checkAndFireGesture:function(L,M,N){if(!M){M=L.type;};if(!N){N=qx.bom.Event.getTarget(L);};if(M==v){this.gestureBegin(L,N);}else if(M==d){this.gestureMove(L,N);}else if(M==m){this.gestureFinish(L,N);}else if(M==u){this.gestureCancel(L.pointerId);};},gestureBegin:function(O,P){if(this.__gesture[O.pointerId]){this.__stopLongTapTimer(this.__gesture[O.pointerId]);delete this.__gesture[O.pointerId];};if(this._hasIntermediaryHandler(P)){return;};this.__gesture[O.pointerId]={"startTime":new Date().getTime(),"lastEventTime":new Date().getTime(),"startX":O.clientX,"startY":O.clientY,"clientX":O.clientX,"clientY":O.clientY,"velocityX":0,"velocityY":0,"target":P,"isTap":true,"isPrimary":O.isPrimary,"longTapTimer":window.setTimeout(this.__fireLongTap.bind(this,O,P),qx.event.handler.GestureCore.LONGTAP_TIME)};if(O.isPrimary){this.__isMultiPointerGesture=false;this.__primaryTarget=P;this.__fireTrack(l,O,P);}else {this.__isMultiPointerGesture=true;if(Object.keys(this.__gesture).length===2){this.__initialAngle=this._calcAngle();this.__initialDistance=this._calcDistance();};};},gestureMove:function(R,S){var T=this.__gesture[R.pointerId];if(T){var Q=T.clientX;var U=T.clientY;T.clientX=R.clientX;T.clientY=R.clientY;T.lastEventTime=new Date().getTime();if(Q){T.velocityX=T.clientX-Q;};if(U){T.velocityY=T.clientY-U;};if(Object.keys(this.__gesture).length===2){this.__fireRotate(R,T.target);this.__firePinch(R,T.target);};if(!this.__isMultiPointerGesture){this.__fireTrack(w,R,T.target);this._fireRoll(R,e,T.target);};if(T.isTap){T.isTap=this._isBelowTapMaxDistance(R);if(!T.isTap){this.__stopLongTapTimer(T);};};};},_hasIntermediaryHandler:function(V){while(V&&V!==this.__defaultTarget){if(V.$$gestureHandler){return true;};V=V.parentNode;};return false;},gestureFinish:function(X,Y){if(!this.__gesture[X.pointerId]){return;};var bf=this.__gesture[X.pointerId];this.__stopLongTapTimer(bf);if(this._hasIntermediaryHandler(Y)){return;};this.__handleRollImpulse(bf.velocityX,bf.velocityY,X,bf.target);this.__fireTrack(z,X,bf.target);if(bf.isTap){if(Y!==bf.target){delete this.__gesture[X.pointerId];return;};this._fireEvent(X,B,X.target||Y);var ba=false;if(Object.keys(this.__lastTap).length>0){var be=Date.now()-qx.event.handler.GestureCore.DOUBLETAP_TIME;for(var bg in this.__lastTap){if(bg<be){delete this.__lastTap[bg];}else {var W=this.__lastTap[bg];var bc=this.__isBelowDoubleTapDistance(W.x,W.y,X.clientX,X.clientY,X.getPointerType());var bd=W.target===(X.target||Y);var bh=W.button===X.button;if(bc&&bh&&bd){ba=true;delete this.__lastTap[bg];this._fireEvent(X,p,X.target||Y);};};};};if(!ba){this.__lastTap[Date.now()]={x:X.clientX,y:X.clientY,target:X.target||Y,button:X.button};};}else if(!this._isBelowTapMaxDistance(X)){var bb=this.__getSwipeGesture(X,Y);if(bb){X.swipe=bb;this._fireEvent(X,a,bf.target||Y);};};delete this.__gesture[X.pointerId];},stopMomentum:function(bi){this.__stopMomentum[bi]=true;},gestureCancel:function(bj){if(this.__gesture[bj]){this.__stopLongTapTimer(this.__gesture[bj]);delete this.__gesture[bj];};},updateGestureTarget:function(bk,bl){this.__gesture[bk].target=bl;},__handleRollImpulse:function(bq,br,bn,bo,bt){var bs=bn.timeoutId;if((Math.abs(br)<1&&Math.abs(bq)<1)||this.__stopMomentum[bs]){delete this.__stopMomentum[bs];return;};if(!bt){bt=1;var bp=2.8;br=br/bp;bq=bq/bp;};bt+=0.0006;br=br/bt;bq=bq/bt;var bm=qx.bom.AnimationFrame.request(qx.lang.Function.bind(function(bu,bv,bw,bx,by){this.__handleRollImpulse(bu,bv,bw,bx,by);},this,bq,br,bn,bo,bt));bq=Math.round(bq*100)/100;br=Math.round(br*100)/100;bn.delta={x:-bq,y:-br};bn.momentum=true;bn.timeoutId=bm;this._fireEvent(bn,h,bn.target||bo);},_calcAngle:function(){var bA=null;var bB=null;for(var bz in this.__gesture){var bC=this.__gesture[bz];if(bA===null){bA=bC;}else {bB=bC;};};var x=bA.clientX-bB.clientX;var y=bA.clientY-bB.clientY;return (360+Math.atan2(y,x)*(180/Math.PI))%360;},_calcDistance:function(){var bD=null;var bE=null;for(var bG in this.__gesture){var bH=this.__gesture[bG];if(bD===null){bD=bH;}else {bE=bH;};};var bF=Math.sqrt(Math.pow(bD.clientX-bE.clientX,2)+Math.pow(bD.clientY-bE.clientY,2));return bF;},_isBelowTapMaxDistance:function(bJ){var bK=this._getDeltaCoordinates(bJ);var bI=qx.event.handler.GestureCore.TAP_MAX_DISTANCE[bJ.getPointerType()];if(!bK){return null;};return (Math.abs(bK.x)<=bI&&Math.abs(bK.y)<=bI);},__isBelowDoubleTapDistance:function(bL,bP,bQ,bR,bS){var bO=qx.event.handler.GestureCore;var bM=Math.abs(bL-bQ)<bO.DOUBLETAP_MAX_DISTANCE[bS];var bN=Math.abs(bP-bR)<bO.DOUBLETAP_MAX_DISTANCE[bS];return bM&&bN;},_getDeltaCoordinates:function(bV){var bW=this.__gesture[bV.pointerId];if(!bW){return null;};var bT=bV.clientX-bW.startX;var bU=bV.clientY-bW.startY;var bX=D;if(Math.abs(bT/bU)<1){bX=n;};return {"x":bT,"y":bU,"axis":bX};},_fireEvent:function(ca,cc,cb){if(!this.__defaultTarget){return;};var bY;if(qx.core.Environment.get(c)){bY=new qx.event.type.dom.Custom(cc,ca,{bubbles:true,swipe:ca.swipe,scale:ca.scale,angle:ca.angle,delta:ca.delta,pointerType:ca.pointerType,momentum:ca.momentum});cb.dispatchEvent(bY);}else if(this.__emitter){bY=new qx.event.type.dom.Custom(cc,ca,{target:this.__defaultTarget,currentTarget:this.__defaultTarget,srcElement:this.__defaultTarget,swipe:ca.swipe,scale:ca.scale,angle:ca.angle,delta:ca.delta,pointerType:ca.pointerType,momentum:ca.momentum});this.__emitter.emit(cc,ca);};},_onDblClick:function(cd){var ce=qx.bom.Event.getTarget(cd);this._fireEvent(cd,B,ce);this._fireEvent(cd,p,ce);},__getSwipeGesture:function(ch,ci){var co=this.__gesture[ch.pointerId];if(!co){return null;};var ck=qx.event.handler.GestureCore;var cn=this._getDeltaCoordinates(ch);var cl=new Date().getTime()-co.startTime;var cp=(Math.abs(cn.x)>=Math.abs(cn.y))?D:n;var cf=cn[cp];var cg=ck.SWIPE_DIRECTION[cp][cf<0?0:1];var cm=(cl!==0)?cf/cl:0;var cj={startTime:co.startTime,duration:cl,axis:cp,direction:cg,distance:cf,velocity:cm};return cj;},__fireTrack:function(cq,cr,cs){cr.delta=this._getDeltaCoordinates(cr);this._fireEvent(cr,cq,cr.target||cs);},_fireRoll:function(cu,ct,cv){if(cu.type===qx.core.Environment.get(g).type){cu.delta={x:qx.util.Wheel.getDelta(cu,D)*qx.event.handler.GestureCore.ROLL_FACTOR,y:qx.util.Wheel.getDelta(cu,n)*qx.event.handler.GestureCore.ROLL_FACTOR};cu.delta.axis=Math.abs(cu.delta.x/cu.delta.y)<1?n:D;cu.pointerType=j;}else {var cw=this.__gesture[cu.pointerId];cu.delta={x:-cw.velocityX,y:-cw.velocityY,axis:Math.abs(cw.velocityX/cw.velocityY)<1?n:D};};this._fireEvent(cu,h,cu.target||cv);},__fireRotate:function(cx,cz){if(!cx.isPrimary){var cy=this._calcAngle();cx.angle=Math.round((cy-this.__initialAngle)%360);this._fireEvent(cx,k,this.__primaryTarget);};},__firePinch:function(cC,cD){if(!cC.isPrimary){var cA=this._calcDistance();var cB=cA/this.__initialDistance;cC.scale=(Math.round(cB*100)/100);this._fireEvent(cC,b,this.__primaryTarget);};},__fireLongTap:function(cE,cF){var cG=this.__gesture[cE.pointerId];if(cG){this._fireEvent(cE,f,cE.target||cF);cG.longTapTimer=null;cG.isTap=false;};},__stopLongTapTimer:function(cH){if(cH.longTapTimer){window.clearTimeout(cH.longTapTimer);cH.longTapTimer=null;};},isBelowTapMaxDistance:function(event){var cI=this._calcDelta(event);var cJ=qx.event.handler.GestureCore;return (Math.abs(cI.x)<=cJ.TAP_MAX_DISTANCE&&Math.abs(cI.y)<=cJ.TAP_MAX_DISTANCE);},dispose:function(){for(var cK in this.__gesture){this.__stopLongTapTimer(cK);};this._stopObserver();this.__defaultTarget=this.__emitter=null;}}});})();(function(){var b="ease-in-out",c="Number",d="css.animation.requestframe",e="qx.bom.AnimationFrame",f="frame",g="end",h="linear",j="ease-in",k="ease-out";qx.Bootstrap.define(e,{extend:qx.event.Emitter,events:{"end":undefined,"frame":c},members:{__canceled:false,startSequence:function(l){this.__canceled=false;var m=+(new Date());var n=function(p){if(this.__canceled){this.id=null;return;};if(p>=m+l){this.emit(g);this.id=null;}else {var o=Math.max(p-m,0);this.emit(f,o);this.id=qx.bom.AnimationFrame.request(n,this);};};this.id=qx.bom.AnimationFrame.request(n,this);},cancelSequence:function(){this.__canceled=true;}},statics:{TIMEOUT:30,calculateTiming:function(q,x){if(q==j){var a=[3.1223e-7,0.0757,1.2646,-0.167,-0.4387,0.2654];}else if(q==k){var a=[-7.0198e-8,1.652,-0.551,-0.0458,0.1255,-0.1807];}else if(q==h){return x;}else if(q==b){var a=[2.482e-7,-0.2289,3.3466,-1.0857,-1.7354,0.7034];}else {var a=[-0.0021,0.2472,9.8054,-21.6869,17.7611,-5.1226];};var y=0;for(var i=0;i<a.length;i++ ){y+=a[i]*Math.pow(x,i);};return y;},request:function(r,t){var s=qx.core.Environment.get(d);var u=function(v){if(v<1e10){v=this.__start+v;};v=v||+(new Date());r.call(t,v);};if(s){return window[s](u);}else {return window.setTimeout(function(){u();},qx.bom.AnimationFrame.TIMEOUT);};}},defer:function(w){w.__start=window.performance&&performance.timing&&performance.timing.navigationStart;if(!w.__start){w.__start=Date.now();};}});})();(function(){var a="stylesheet",b="head",c="html.stylesheet.insertrule",d="}",e="html.stylesheet.createstylesheet",f="text/css",g="{",h="qx.bom.Stylesheet",i="link",j="style";qx.Bootstrap.define(h,{statics:{includeFile:function(m,k){if(!k){k=document;};var n=k.createElement(i);n.type=f;n.rel=a;n.href=m;var l=k.getElementsByTagName(b)[0];l.appendChild(n);},createElement:function(o){if(qx.core.Environment.get(e)){var p=document.createStyleSheet();if(o){p.cssText=o;};return p;}else {var q=document.createElement(j);q.type=f;if(o){q.appendChild(document.createTextNode(o));};document.getElementsByTagName(b)[0].appendChild(q);return q.sheet;};},addRule:function(t,u,s){{var r;};if(qx.core.Environment.get(c)){t.insertRule(u+g+s+d,t.cssRules.length);}else {t.addRule(u,s);};}}});})();(function(){var a="qx.dom.Element",b="The tag name is missing!";qx.Bootstrap.define(a,{statics:{getParentElement:function(c){return c.parentNode;},insertEnd:function(d,parent){parent.appendChild(d);return true;},insertBefore:function(e,f){f.parentNode.insertBefore(e,f);return true;},insertAfter:function(g,h){var parent=h.parentNode;if(h==parent.lastChild){parent.appendChild(g);}else {return this.insertBefore(g,h.nextSibling);};return true;},remove:function(i){if(!i.parentNode){return false;};i.parentNode.removeChild(i);return true;},create:function(name,k,j){if(!j){j=window;};if(!name){throw new Error(b);};var m=j.document.createElement(name);for(var l in k){qx.bom.element.Attribute.set(m,l,k[l]);};return m;}}});})();(function(){var a="file",b="+",c="strict",d="anchor",e="query",f="source",g="password",h="host",j="protocol",k="user",l="directory",n="loose",p="relative",q="queryKey",r="qx.util.Uri",s="",t="path",u="authority",v="&",w="port",x="userInfo",y="?",z="=";qx.Bootstrap.define(r,{statics:{parseUri:function(C,B){var D={key:[f,j,u,x,k,g,h,w,p,t,l,a,e,d],q:{name:q,parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}};var o=D,m=D.parser[B?c:n].exec(C),A={},i=14;while(i-- ){A[o.key[i]]=m[i]||s;};A[o.q.name]={};A[o.key[12]].replace(o.q.parser,function(F,G,E){if(G){A[o.q.name][G]=E;};});return A;},appendParamsToUrl:function(H,I){if(I===undefined){return H;};{};if(qx.lang.Type.isObject(I)){I=qx.util.Uri.toParameter(I);};if(!I){return H;};return H+=/\?/.test(H)?v+I:y+I;},toParameter:function(J,N){var M,L=[];for(M in J){if(J.hasOwnProperty(M)){var K=J[M];if(K instanceof Array){for(var i=0;i<K.length;i++ ){this.__toParameterPair(M,K[i],L,N);};}else {this.__toParameterPair(M,K,L,N);};};};return L.join(v);},__toParameterPair:function(R,S,Q,P){var O=window.encodeURIComponent;if(P){Q.push(O(R).replace(/%20/g,b)+z+O(S).replace(/%20/g,b));}else {Q.push(O(R)+z+O(S));};}}});})();(function(){var a="qx.bom.client.Stylesheet",b="html.stylesheet.deleterule",c="html.stylesheet.insertrule",d="function",e="html.stylesheet.createstylesheet",f="html.stylesheet.addimport",g="html.stylesheet.removeimport",h="object";qx.Bootstrap.define(a,{statics:{__getStylesheet:function(){if(!qx.bom.client.Stylesheet.__stylesheet){qx.bom.client.Stylesheet.__stylesheet=qx.bom.Stylesheet.createElement();};return qx.bom.client.Stylesheet.__stylesheet;},getCreateStyleSheet:function(){return typeof document.createStyleSheet===h;},getInsertRule:function(){return typeof qx.bom.client.Stylesheet.__getStylesheet().insertRule===d;},getDeleteRule:function(){return typeof qx.bom.client.Stylesheet.__getStylesheet().deleteRule===d;},getAddImport:function(){return (typeof qx.bom.client.Stylesheet.__getStylesheet().addImport===h);},getRemoveImport:function(){return (typeof qx.bom.client.Stylesheet.__getStylesheet().removeImport===h);}},defer:function(i){qx.core.Environment.add(e,i.getCreateStyleSheet);qx.core.Environment.add(c,i.getInsertRule);qx.core.Environment.add(b,i.getDeleteRule);qx.core.Environment.add(f,i.getAddImport);qx.core.Environment.add(g,i.getRemoveImport);}});})();(function(){var a="oAnimationStart",b="animationend",c="MSAnimationStart",d="oRequestAnimationFrame",f="AnimationFillMode",g="webkitAnimationStart",h="MSAnimationEnd",j="requestAnimationFrame",k="mozRequestAnimationFrame",l="webkitanimationend",m="css.animation.requestframe",n="AnimationPlayState",o="",p="MSAnimationIteration",q="animation",r="oAnimationEnd",s="@",t="animationiteration",u="webkitAnimationEnd",v="webkitRequestAnimationFrame",w=" name",x="qx.bom.client.CssAnimation",y="css.animation",z="oAnimationIteration",A="webkitanimationiteration",B="-keyframes",C="msRequestAnimationFrame",D="@keyframes",E="webkitAnimationIteration",F="animationstart",G="webkitanimationstart";qx.Bootstrap.define(x,{statics:{getSupport:function(){var name=qx.bom.client.CssAnimation.getName();if(name!=null){return {"name":name,"play-state":qx.bom.client.CssAnimation.getPlayState(),"start-event":qx.bom.client.CssAnimation.getAnimationStart(),"iteration-event":qx.bom.client.CssAnimation.getAnimationIteration(),"end-event":qx.bom.client.CssAnimation.getAnimationEnd(),"fill-mode":qx.bom.client.CssAnimation.getFillMode(),"keyframes":qx.bom.client.CssAnimation.getKeyFrames()};};return null;},getFillMode:function(){return qx.bom.Style.getPropertyName(f);},getPlayState:function(){return qx.bom.Style.getPropertyName(n);},getName:function(){return qx.bom.Style.getPropertyName(q);},getAnimationStart:function(){if(qx.bom.Event.supportsEvent(window,G)){return g;};var H={"msAnimation":c,"WebkitAnimation":g,"MozAnimation":F,"OAnimation":a,"animation":F};return H[this.getName()];},getAnimationIteration:function(){if(qx.bom.Event.supportsEvent(window,A)){return E;};var I={"msAnimation":p,"WebkitAnimation":E,"MozAnimation":t,"OAnimation":z,"animation":t};return I[this.getName()];},getAnimationEnd:function(){if(qx.bom.Event.supportsEvent(window,l)){return u;};var J={"msAnimation":h,"WebkitAnimation":u,"MozAnimation":b,"OAnimation":r,"animation":b};return J[this.getName()];},getKeyFrames:function(){var K=qx.bom.Style.VENDOR_PREFIXES;var N=[];for(var i=0;i<K.length;i++ ){var M=s+qx.bom.Style.getCssName(K[i])+B;N.push(M);};N.unshift(D);var L=qx.bom.Stylesheet.createElement();for(var i=0;i<N.length;i++ ){try{qx.bom.Stylesheet.addRule(L,N[i]+w,o);return N[i];}catch(e){};};return null;},getRequestAnimationFrame:function(){var O=[j,C,v,k,d];for(var i=0;i<O.length;i++ ){if(window[O[i]]!=undefined){return O[i];};};return null;}},defer:function(P){qx.core.Environment.add(y,P.getSupport);qx.core.Environment.add(m,P.getRequestAnimationFrame);}});})();(function(){var a="x",b="y",c="qx.util.Wheel";qx.Bootstrap.define(c,{statics:{MAXSCROLL:null,MINSCROLL:null,FACTOR:1,getDelta:function(e,d){if(d===undefined){var f=0;if(e.wheelDelta!==undefined){f=-e.wheelDelta;}else if(e.detail!==0){f=e.detail;}else if(e.deltaY!==undefined){f=e.deltaY;};return this.__normalize(f);};if(d===a){var x=0;if(e.wheelDelta!==undefined){if(e.wheelDeltaX!==undefined){x=e.wheelDeltaX?this.__normalize(-e.wheelDeltaX):0;};}else {if(e.axis&&e.axis==e.HORIZONTAL_AXIS&&(e.detail!==undefined)&&(e.detail>0)){x=this.__normalize(e.detail);}else if(e.deltaX!==undefined){x=this.__normalize(e.deltaX);};};return x;};if(d===b){var y=0;if(e.wheelDelta!==undefined){if(e.wheelDeltaY!==undefined){y=e.wheelDeltaY?this.__normalize(-e.wheelDeltaY):0;}else {y=this.__normalize(-e.wheelDelta);};}else {if(!(e.axis&&e.axis==e.HORIZONTAL_AXIS)&&(e.detail!==undefined)&&(e.detail>0)){y=this.__normalize(e.detail);}else if(e.deltaY!==undefined){y=this.__normalize(e.deltaY);};};return y;};return 0;},__normalize:function(j){var g=Math.abs(j);if(g===0){return 0;};if(qx.util.Wheel.MINSCROLL==null||qx.util.Wheel.MINSCROLL>g){qx.util.Wheel.MINSCROLL=g;this.__recalculateMultiplicator();};if(qx.util.Wheel.MAXSCROLL==null||qx.util.Wheel.MAXSCROLL<g){qx.util.Wheel.MAXSCROLL=g;this.__recalculateMultiplicator();};if(qx.util.Wheel.MAXSCROLL===g&&qx.util.Wheel.MINSCROLL===g){return 2*(j/g);};var h=qx.util.Wheel.MAXSCROLL-qx.util.Wheel.MINSCROLL;var i=(j/h)*Math.log(h)*qx.util.Wheel.FACTOR;return i<0?Math.min(i,-1):Math.max(i,1);},__recalculateMultiplicator:function(){var k=qx.util.Wheel.MAXSCROLL||0;var n=qx.util.Wheel.MINSCROLL||k;if(k<=n){return;};var l=k-n;var m=(k/l)*Math.log(l);if(m==0){m=1;};qx.util.Wheel.FACTOR=6/m;}}});})();(function(){var a="qx.module.Transform",b="";qx.Bootstrap.define(a,{members:{transform:function(c){this._forEachElement(function(d){qx.bom.element.Transform.transform(d,c);});return this;},translate:function(e){return this.transform({translate:e});},scale:function(f){return this.transform({scale:f});},rotate:function(g){return this.transform({rotate:g});},skew:function(h){return this.transform({skew:h});},setTransformOrigin:function(i){this._forEachElement(function(j){qx.bom.element.Transform.setOrigin(j,i);});return this;},getTransformOrigin:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Transform.getOrigin(this[0]);};return b;},setTransformStyle:function(k){this._forEachElement(function(l){qx.bom.element.Transform.setStyle(l,k);});return this;},getTransformStyle:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Transform.getStyle(this[0]);};return b;},setTransformPerspective:function(m){this._forEachElement(function(n){qx.bom.element.Transform.setPerspective(n,m);});return this;},getTransformPerspective:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Transform.getPerspective(this[0]);};return b;},setTransformPerspectiveOrigin:function(o){this._forEachElement(function(p){qx.bom.element.Transform.setPerspectiveOrigin(p,o);});return this;},getTransformPerspectiveOrigin:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Transform.getPerspectiveOrigin(this[0]);};return b;},setTransformBackfaceVisibility:function(q){this._forEachElement(function(r){qx.bom.element.Transform.setBackfaceVisibility(r,q);});return this;},getTransformBackfaceVisibility:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Transform.getBackfaceVisibility(this[0]);};return b;}},defer:function(s){qxWeb.$attachAll(this);}});})();(function(){var a="css.transform.3d",b="backfaceVisibility",c="transformStyle",d="css.transform",e="transformOrigin",f="qx.bom.client.CssTransform",g="transform",h="perspective",i="perspectiveOrigin";qx.Bootstrap.define(f,{statics:{getSupport:function(){var name=qx.bom.client.CssTransform.getName();if(name!=null){return {"name":name,"style":qx.bom.client.CssTransform.getStyle(),"origin":qx.bom.client.CssTransform.getOrigin(),"3d":qx.bom.client.CssTransform.get3D(),"perspective":qx.bom.client.CssTransform.getPerspective(),"perspective-origin":qx.bom.client.CssTransform.getPerspectiveOrigin(),"backface-visibility":qx.bom.client.CssTransform.getBackFaceVisibility()};};return null;},getStyle:function(){return qx.bom.Style.getPropertyName(c);},getPerspective:function(){return qx.bom.Style.getPropertyName(h);},getPerspectiveOrigin:function(){return qx.bom.Style.getPropertyName(i);},getBackFaceVisibility:function(){return qx.bom.Style.getPropertyName(b);},getOrigin:function(){return qx.bom.Style.getPropertyName(e);},getName:function(){return qx.bom.Style.getPropertyName(g);},get3D:function(){return qx.bom.client.CssTransform.getPerspective()!=null;}},defer:function(j){qx.core.Environment.add(d,j.getSupport);qx.core.Environment.add(a,j.get3D);}});})();(function(){var a="backface-visibility",b="css.transform.3d",c=") ",d="px",e="scale",f="Z",g="X",h=", ",j="visible",k=":",l="3d",m="name",n="",o="origin",p="(",q="qx.bom.element.Transform",r="perspective",s="Y",t="css.transform",u="translate",v="perspective-origin",w="hidden",x=";",y=" ",z="style";qx.Bootstrap.define(q,{statics:{__cssKeys:qx.core.Environment.get(t),transform:function(A,C){var D=this.getTransformValue(C);if(this.__cssKeys!=null){var B=this.__cssKeys[m];A.style[B]=D;};},getCss:function(F){var G=this.getTransformValue(F);if(this.__cssKeys!=null){var E=this.__cssKeys[m];return qx.bom.Style.getCssName(E)+k+G+x;};return n;},setOrigin:function(H,I){if(this.__cssKeys!=null){H.style[this.__cssKeys[o]]=I;};},getOrigin:function(J){if(this.__cssKeys!=null){return J.style[this.__cssKeys[o]];};return n;},setStyle:function(K,L){if(this.__cssKeys!=null){K.style[this.__cssKeys[z]]=L;};},getStyle:function(M){if(this.__cssKeys!=null){return M.style[this.__cssKeys[z]];};return n;},setPerspective:function(N,O){if(this.__cssKeys!=null){N.style[this.__cssKeys[r]]=O+d;};},getPerspective:function(P){if(this.__cssKeys!=null){return P.style[this.__cssKeys[r]];};return n;},setPerspectiveOrigin:function(Q,R){if(this.__cssKeys!=null){Q.style[this.__cssKeys[v]]=R;};},getPerspectiveOrigin:function(S){if(this.__cssKeys!=null){var T=S.style[this.__cssKeys[v]];if(T!=n){return T;}else {var V=S.style[this.__cssKeys[v]+g];var U=S.style[this.__cssKeys[v]+s];if(V!=n){return V+y+U;};};};return n;},setBackfaceVisibility:function(W,X){if(this.__cssKeys!=null){W.style[this.__cssKeys[a]]=X?j:w;};},getBackfaceVisibility:function(Y){if(this.__cssKeys!=null){return Y.style[this.__cssKeys[a]]==j;};return true;},getTransformValue:function(bd){var be=n;var ba=[u,e];for(var bb in bd){var bc=bd[bb];if(qx.Bootstrap.isArray(bc)){if(bc.length===3&&ba.indexOf(bb)>-1&&qx.core.Environment.get(b)){be+=this._compute3dProperty(bb,bc);}else {be+=this._computeAxisProperties(bb,bc);};}else {be+=bb+p+bc+c;};};return be.trim();},_compute3dProperty:function(bg,bf){var bh=n;bg+=l;for(var i=0;i<bf.length;i++ ){if(bf[i]==null){bf[i]=0;};};bh+=bg+p+bf.join(h)+c;return bh;},_computeAxisProperties:function(bi,bj){var bl=n;var bk=[g,s,f];for(var i=0;i<bj.length;i++ ){if(bj[i]==null||(i==2&&!qx.core.Environment.get(b))){continue;};bl+=bi+bk[i]+p;bl+=bj[i];bl+=c;};return bl;}}});})();(function(){var a="track",b="qx.module.event.Track",c="function",d="getDelta";qx.Bootstrap.define(b,{statics:{TYPES:[a],BIND_METHODS:[d],getDelta:function(){return this._original.delta;},normalize:function(event,f){if(!event){return event;};var e=qx.module.event.Track.BIND_METHODS;for(var i=0,l=e.length;i<l;i++ ){if(typeof event[e[i]]!=c){event[e[i]]=qx.module.event.Track[e[i]].bind(event);};};return event;}},defer:function(g){qxWeb.$registerEventNormalization(qx.module.event.Track.TYPES,g.normalize);}});})();(function(){var a="swipe",b="-pagination",c="order",d="qx-hbox",f="height",g="px",h=".label",j='</div>',k="pageSwitchDuration",l="qx-flex1",m=".scroll",n="excluded",o="pan-y",p="100%",q="touchAction",r="animationEnd",s="qx-flex-ready",t="center",u="ease",v='-pagination-label"></div>',w="width",x='<div class="',y="table",z="trackstart",A="visible",B="hidden",C="browser.documentmode",D="resize",E="",F="textAlign",G="visibility",H="tap",I='<div class="label">',J="-pagination-label",K="<div>",L="appear",M="right",N="-container",O="table-cell",P="-page",Q="active",R="qx.ui.website.Carousel",S="track",T="trackend",U="offsetWidth",V="left",W="changeActive",X=".",Y="x",bc="msFlexOrder",bd="display";qx.Bootstrap.define(R,{extend:qx.ui.website.Widget,statics:{_config:{pageSwitchDuration:500},carousel:function(){var be=new qx.ui.website.Carousel(this);be.init();return be;}},construct:function(bg,bf){qx.ui.website.Widget.call(this,bg,bf);},members:{__active:null,__pageContainer:null,__scrollContainer:null,__paginationLabels:null,__startPosLeft:null,__pagination:null,_ie9:false,__blocked:false,init:function(){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};this._ie9=qx.core.Environment.get(C)===9;if(this._ie9){this.setConfig(k,10);}else {this.addClass(s);};qxWeb(window).on(D,this._onResize,this);var bh=this.getCssPrefix();this.__scrollContainer=qxWeb.create(K).addClass(bh+N).appendTo(this);this.__pageContainer=qxWeb.create(K).addClass(d).setStyle(f,p).appendTo(this.__scrollContainer);this.__paginationLabels=[];this.__pagination=qxWeb.create(K).addClasses([bh+b,d,l]).setStyle(G,n).appendTo(this);if(this._ie9){this.__pageContainer.setStyle(bd,y);this.__pagination.setStyle(F,t);}else {this.on(z,this._onTrackStart,this).on(S,this._onTrack,this).on(T,this._onTrackEnd,this);};this.on(a,this._onSwipe,this);this.render();return true;},render:function(){var bi=this.find(X+this.getCssPrefix()+P);bi.forEach(function(bj){this.addPage(qxWeb(bj));}.bind(this));if(bi.length>0){this.setActive(bi.eq(0));};return this;},setActive:function(bm){var bk=this.__active;this.__active=bm;this._update();var bl={value:bm,old:bk,target:this};this.emit(W,bl);},getActive:function(){return this.__active;},nextPage:function(){var bn=this._getPages();if(bn.length==0){return this;};var bo=this.getActive().getNext();if(bn.length>2){if(bo.length===0){bo=bn.eq(0);};};if(bo.length>0){this.setActive(bo);};return this;},previousPage:function(){var bq=this._getPages();if(bq.length==0){return this;};var bp=this.getActive().getPrev();if(bq.length>2){if(bp.length==0){bp=bq.eq(bq.length-1);};};if(bp.length>0){this.setActive(bp);};return this;},addPage:function(bs){bs.addClasses([l,this.getCssPrefix()+P]).appendTo(this.__pageContainer);if(this.find(X+this.getCssPrefix()+P).length>this.__paginationLabels.length){var br=this._createPaginationLabel();this.__paginationLabels.push(br);this.__pagination.append(br);};this._updateWidth();if(!this.getActive()){this.setActive(bs);}else if(this._getPages().length>2){this._updateOrder();};if(this._ie9){bs.setStyle(bd,O);};this.find(m).setStyle(q,o);if(this._getPages().length===3&&!this._ie9){this.__scrollContainer.translate([(-this.getWidth())+g,0,0]);};this._updatePagination();},removePage:function(bt){bt.remove();if(this._getPages().length==0){this.__pagination.empty();this.__paginationLabels=[];this.setActive(null);return;};this._updateWidth();if(this.getActive()[0]==bt[0]){this.setActive(this._getPages().eq(0));}else if(this._getPages().length>2){this._updateOrder();}else {this._setOrder(this._getPages(),0);};this.__paginationLabels.splice(bt.priorPosition,1)[0].remove();for(var i=0;i<this.__paginationLabels.length;i++ ){this.__paginationLabels[i].getChildren(h).setHtml((i+1)+E);};this._updatePagination();},_update:function(){if(!this.getActive()){return;};if(this._getPages().length<2){return;}else if(this._getPages().length==2){if(this._getPages()[0]===this.getActive()[0]){this._translateTo(0);}else {this._translateTo(this.getWidth());};this._updatePagination();return;};var bw;if(!this._ie9){var bv=this._updateOrder();if(bv==M){bw=this._getPositionLeft()-this.__scrollContainer.getWidth();}else if(bv==V){bw=this._getPositionLeft()+this.__scrollContainer.getWidth();}else if(this._getPages().length>=3){this._translateTo(this.getWidth());return;}else {return;};if(bw!==undefined){this.__scrollContainer.translate([(-bw)+g,0,0]);this._translateTo(this.getWidth());};}else {var bu=this._getPages().indexOf(this.getActive());bw=bu*this.getWidth();this._translateTo(bw);};this._updatePagination();},_updateOrder:function(){if(this._ie9){return V;};var bz;var bA=this._getPages();var by=this._getOrder(this.getActive());if(by>0){bz=M;}else if(by<0){bz=V;};var bx=bA.indexOf(this.getActive());this._setOrder(this.getActive(),0);var bB=1;for(var i=bx+1;i<bA.length;i++ ){if(bx===0&&i==bA.length-1){bB=-1;};this._setOrder(bA.eq(i),bB++ );};for(i=0;i<bx;i++ ){if(i==bx-1){bB=-1;};this._setOrder(bA.eq(i),bB++ );};return bz;},_updateWidth:function(){if(!this.isRendered()||this.getProperty(U)===0){this.setStyle(G,B);if(!this.hasListener(L,this._updateWidth,this)){this.once(L,this._updateWidth,this);};return;};if(this._getPositionLeft()===0&&this._getPages().length>2&&!this._ie9){this.__scrollContainer.translate([(-this.getWidth())+g,0,0]);};var bC=this.getWidth()*this._getPages().length;this.__pageContainer.setStyle(w,bC+g);this._getPages().setStyle(w,this.getWidth()+g);this.setStyle(G,A);},_onTrackStart:function(){if(this.__blocked){return;};this.__startPosLeft=this._getPositionLeft();this.__scrollContainer.stop().translate([(-Math.round(this.__startPosLeft))+g,0,0]);},_onTrack:function(e){if(this.__blocked){return;};if(e.delta.axis==Y&&this._getPages().length>2){this.__scrollContainer.translate([-(this.__startPosLeft-e.delta.x)+g,0,0]);};},_onTrackEnd:function(){if(this.__startPosLeft==null||this.__blocked){return;};window.setTimeout(function(){if(this._getPages().length<3||this.__scrollContainer.isPlaying()){return;};this.__startPosLeft=null;var bD=this.getWidth();var bG=this._getPages();var bF=this.getActive();if(this._getPositionLeft()<(bD-(bD/2))){var bE=this.getActive().getPrev();if(bE.length==0){bE=bG.eq(bG.length-1);};this.setActive(bE);}else if(this._getPositionLeft()>(bD+bD/2)){var bH=this.getActive().getNext();if(bH.length==0){bH=bG.eq(0);};this.setActive(bH);};if(this.getActive()==bF){this._update();};}.bind(this),0);},_onSwipe:function(e){if(this.__blocked){return;};var bI=Math.abs(e.getVelocity());if(e.getAxis()==Y&&bI>0.25){if(e.getDirection()==V){this.nextPage();}else if(e.getDirection()==M){this.previousPage();};};},_createPaginationLabel:function(){var bJ=this._getPages().length;return qxWeb.create(x+this.getCssPrefix()+v).on(H,this._onPaginationLabelTap,this).append(qxWeb.create(I+bJ+j));},_onPaginationLabelTap:function(e){this.__paginationLabels.forEach(function(bL,bK){if(bL[0]===e.currentTarget){var bM=this._getPages();if(bM.length===2){this.setActive(bM.eq(bK));return;};var bN=bM.indexOf(this.getActive());var bO=bK-bN;this._setOrder(bM,0);this.__scrollContainer.translate([(-bN*this.getWidth())+g,0,0]);this.__blocked=true;this._translateTo((bN+bO)*this.getWidth());this.__scrollContainer.once(r,function(bP){this.__blocked=false;this.__scrollContainer.translate([(-this.getWidth())+g,0,0]);this.setActive(bP);this._updatePagination();}.bind(this,bM.eq(bK)));};}.bind(this));},_updatePagination:function(){this._getPages().length<2?this.__pagination.setStyle(G,n):this.__pagination.setStyle(G,A);this.__pagination.find(X+this.getCssPrefix()+J).removeClass(Q);var bQ=this._getPages();this.__paginationLabels[bQ.indexOf(this.getActive())].addClass(Q);},_onResize:function(){this._updateWidth();if(this._getPages().length>2){this.__scrollContainer.translate([(-this.getWidth())+g,0,0]);};},_translateTo:function(bR){this.__scrollContainer.animate({duration:this.getConfig(k),keep:100,timing:u,keyFrames:{'0':{},'100':{translate:[(-bR)+g,0,0]}}});},_setOrder:function(bS,bT){bS.setStyles({order:bT,msFlexOrder:bT});},_getOrder:function(bU){var bV=parseInt(bU.getStyle(c));if(isNaN(bV)){bV=parseInt(bU.getStyle(bc));};return bV;},_getPages:function(){return this.__pageContainer.find(X+this.getCssPrefix()+P);},_getPositionLeft:function(){var bX=this.__scrollContainer[0].getBoundingClientRect();var bW=this[0].getBoundingClientRect();return -(bX.left-bW.left);},dispose:function(){qxWeb(window).off(D,this._onResize,this);this.off(z,this._onTrackStart,this).off(S,this._onTrack,this).off(a,this._onSwipe,this).off(T,this._onTrackEnd,this);return qx.ui.website.Widget.prototype.dispose.call(this);}},defer:function(bY){qxWeb.$attach({carousel:bY.carousel});}});})();(function(){var a="template",b="span",c="qx.module.Template";qx.Bootstrap.define(c,{statics:{get:function(f,g,e){var d=qx.bom.Template.get(f,g,e);d=qx.module.Template.__wrap(d);return qxWeb.$init([d],qxWeb);},render:function(i,j,h){return qx.bom.Template.render(i,j,h);},renderToNode:function(m,n,l){var k=qx.bom.Template.renderToNode(m,n,l);k=qx.module.Template.__wrap(k);return qxWeb.$init([k],qxWeb);},__wrap:function(o){if(qxWeb.isTextNode(o)){var p=document.createElement(b);p.appendChild(o);o=p;};return o;}},defer:function(q){qxWeb.$attachAll(this,a);}});})();(function(){var a='&#x60;',b='}}',c='" was given as the first ',d='',e='argument for mustache#render(template, view, partials)',f='&amp;',g='&#39;',h='Unclosed tag at ',k='object',l="div",m='2.2.1',n='\\$&',o='Cannot use higher-order sections without the original template',p='function',q='^',r='array',t='&gt;',u='exports',v='&lt;',w='mustache.js',x='.',y="qx.bom.Template",z='Invalid tags: ',A='Unopened section "',B='Invalid template! Template should be a "string" ',C='>',D='\\s*',E='but "',F='{',G='number',H='{{',I='" at ',J='name',K='[object Array]',L='&#x2F;',M='\n',N='&',O='text',P='#',Q='string',R='=',S='/',T='Unclosed section "',U='&quot;',V='&#x3D;',W='}';qx.Bootstrap.define(y,{statics:{version:null,render:null,renderToNode:function(ba,bb,X){var Y=this.render(ba,bb,X);return this._createNodeFromTemplate(Y);},get:function(bd,bf,bc){var be=document.getElementById(bd);return this.renderToNode(be.innerHTML,bf,bc);},_createNodeFromTemplate:function(bg){if(bg.search(/<|>/)===-1){return document.createTextNode(bg);};var bh=qx.dom.Element.create(l);bh.innerHTML=bg;return bh.children[0];}}});(function(){var bi;var bj;(function bk(bl,bm){if(typeof bi===k&&bi&&typeof bi.nodeName!==Q){bm(bi);}else if(typeof bj===p&&bj.amd){bj([u],bm);}else {bl.Mustache={};bm(bl.Mustache);};}(this,function bz(bM){var bv=Object.prototype.toString;var by=Array.isArray||function bO(bN){return bv.call(bN)===K;};function bt(bP){return typeof bP===p;};function bL(bQ){return by(bQ)?r:typeof bQ;};function bH(bR){return bR.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,n);};function bu(bS,bT){return bS!=null&&typeof bS===k&&(bT in bS);};var bA=RegExp.prototype.test;function bC(bU,bV){return bA.call(bU,bV);};var bs=/\S/;function bx(bW){return !bC(bs,bW);};var bD={'&':f,'<':v,'>':t,'"':U,"'":g,'/':L,'`':a,'=':V};function bn(bX){return String(bX).replace(/[&<>"'`=\/]/g,function bY(s){return bD[s];});};var bw=/\s*/;var bK=/\s+/;var bJ=/\s*=/;var bE=/\s*\}/;var bB=/#|\^|\/|>|\{|&|=|!/;function bo(co,cc){if(!co)return [];var cd=[];var cj=[];var cm=[];var ct=false;var cg=false;function ce(){if(ct&&!cg){while(cm.length)delete cj[cm.pop()];}else {cm=[];};ct=false;cg=false;};var ci,ca,cq;function cf(cu){if(typeof cu===Q)cu=cu.split(bK,2);if(!by(cu)||cu.length!==2)throw new Error(z+cu);ci=new RegExp(bH(cu[0])+D);ca=new RegExp(D+bH(cu[1]));cq=new RegExp(D+bH(W+cu[1]));};cf(cc||bM.tags);var cb=new bI(co);var ck,cr,cn,cl,cs,ch;while(!cb.eos()){ck=cb.pos;cn=cb.scanUntil(ci);if(cn){for(var i=0,cp=cn.length;i<cp; ++i){cl=cn.charAt(i);if(bx(cl)){cm.push(cj.length);}else {cg=true;};cj.push([O,cl,ck,ck+1]);ck+=1;if(cl===M)ce();};};if(!cb.scan(ci))break;ct=true;cr=cb.scan(bB)||J;cb.scan(bw);if(cr===R){cn=cb.scanUntil(bJ);cb.scan(bJ);cb.scanUntil(ca);}else if(cr===F){cn=cb.scanUntil(cq);cb.scan(bE);cb.scanUntil(ca);cr=N;}else {cn=cb.scanUntil(ca);};if(!cb.scan(ca))throw new Error(h+cb.pos);cs=[cr,cn,ck,cb.pos];cj.push(cs);if(cr===P||cr===q){cd.push(cs);}else if(cr===S){ch=cd.pop();if(!ch)throw new Error(A+cn+I+ck);if(ch[1]!==cn)throw new Error(T+ch[1]+I+ck);}else if(cr===J||cr===F||cr===N){cg=true;}else if(cr===R){cf(cn);};};ch=cd.pop();if(ch)throw new Error(T+ch[1]+I+cb.pos);return br(bp(cj));};function bp(cx){var cv=[];var cy,cz;for(var i=0,cw=cx.length;i<cw; ++i){cy=cx[i];if(cy){if(cy[0]===O&&cz&&cz[0]===O){cz[1]+=cy[1];cz[3]=cy[3];}else {cv.push(cy);cz=cy;};};};return cv;};function br(cE){var cA=[];var cG=cA;var cB=[];var cF,cC;for(var i=0,cD=cE.length;i<cD; ++i){cF=cE[i];switch(cF[0]){case P:case q:cG.push(cF);cB.push(cF);cG=cF[4]=[];break;case S:cC=cB.pop();cC[5]=cF[2];cG=cB.length>0?cB[cB.length-1][4]:cA;break;default:cG.push(cF);};};return cA;};function bI(cH){this.string=cH;this.tail=cH;this.pos=0;};bI.prototype.eos=function cI(){return this.tail===d;};bI.prototype.scan=function cM(cJ){var cL=this.tail.match(cJ);if(!cL||cL.index!==0)return d;var cK=cL[0];this.tail=this.tail.substring(cK.length);this.pos+=cK.length;return cK;};bI.prototype.scanUntil=function cN(cO){var cQ=this.tail.search(cO),cP;switch(cQ){case -1:cP=this.tail;this.tail=d;break;case 0:cP=d;break;default:cP=this.tail.substring(0,cQ);this.tail=this.tail.substring(cQ);};this.pos+=cP.length;return cP;};function bF(cS,cR){this.view=cS;this.cache={'.':this.view};this.parent=cR;};bF.prototype.push=function cT(cU){return new bF(cU,this);};bF.prototype.lookup=function da(name){var cX=this.cache;var cY;if(cX.hasOwnProperty(name)){cY=cX[name];}else {var dc=this,db,cV,cW=false;while(dc){if(name.indexOf(x)>0){cY=dc.view;db=name.split(x);cV=0;while(cY!=null&&cV<db.length){if(cV===db.length-1)cW=bu(cY,db[cV]);cY=cY[db[cV++ ]];};}else {cY=dc.view[name];cW=bu(dc.view,name);};if(cW)break;dc=dc.parent;};cX[name]=cY;};if(bt(cY))cY=cY.call(this.view);return cY;};function bq(){this.cache={};};bq.prototype.clearCache=function dd(){this.cache={};};bq.prototype.parse=function df(dh,di){var dg=this.cache;var de=dg[dh];if(de==null)de=dg[dh]=bo(dh,di);return de;};bq.prototype.render=function dk(dj,dp,dm){var dl=this.parse(dj);var dn=(dp instanceof bF)?dp:new bF(dp);return this.renderTokens(dl,dn,dm,dj);};bq.prototype.renderTokens=function dy(dv,dx,ds,dz){var dq=d;var dw,dr,dt;for(var i=0,du=dv.length;i<du; ++i){dt=undefined;dw=dv[i];dr=dw[0];if(dr===P)dt=this.renderSection(dw,dx,ds,dz);else if(dr===q)dt=this.renderInverted(dw,dx,ds,dz);else if(dr===C)dt=this.renderPartial(dw,dx,ds,dz);else if(dr===N)dt=this.unescapedValue(dw,dx);else if(dr===J)dt=this.escapedValue(dw,dx);else if(dr===O)dt=this.rawValue(dw);if(dt!==undefined)dq+=dt;};return dq;};bq.prototype.renderSection=function dA(dD,dG,dF,dI){var self=this;var dB=d;var dE=dG.lookup(dD[1]);function dC(dJ){return self.render(dJ,dG,dF);};if(!dE)return;if(by(dE)){for(var j=0,dH=dE.length;j<dH; ++j){dB+=this.renderTokens(dD[4],dG.push(dE[j]),dF,dI);};}else if(typeof dE===k||typeof dE===Q||typeof dE===G){dB+=this.renderTokens(dD[4],dG.push(dE),dF,dI);}else if(bt(dE)){if(typeof dI!==Q)throw new Error(o);dE=dE.call(dG.view,dI.slice(dD[3],dD[5]),dC);if(dE!=null)dB+=dE;}else {dB+=this.renderTokens(dD[4],dG,dF,dI);};return dB;};bq.prototype.renderInverted=function dK(dL,dO,dN,dP){var dM=dO.lookup(dL[1]);if(!dM||(by(dM)&&dM.length===0))return this.renderTokens(dL[4],dO,dN,dP);};bq.prototype.renderPartial=function dQ(dT,dS,dR){if(!dR)return;var dU=bt(dR)?dR(dT[1]):dR[dT[1]];if(dU!=null)return this.renderTokens(this.parse(dU),dS,dR,dU);};bq.prototype.unescapedValue=function dX(dW,dV){var dY=dV.lookup(dW[1]);if(dY!=null)return dY;};bq.prototype.escapedValue=function eb(ec,ea){var ed=ea.lookup(ec[1]);if(ed!=null)return bM.escape(ed);};bq.prototype.rawValue=function ef(ee){return ee[1];};bM.name=w;bM.version=m;bM.tags=[H,b];var bG=new bq();bM.clearCache=function eg(){return bG.clearCache();};bM.parse=function eh(ei,ej){return bG.parse(ei,ej);};bM.render=function el(em,en,ek){if(typeof em!==Q){throw new TypeError(B+E+bL(em)+c+e);};return bG.render(em,en,ek);};bM.to_html=function eo(es,et,eq,ep){var er=bM.render(es,et,eq);if(bt(ep)){ep(er);}else {return er;};};bM.escape=bn;bM.Scanner=bI;bM.Context=bF;bM.Writer=bq;}));qx.bom.Template.version=this.Mustache.version;qx.bom.Template.render=this.Mustache.render;}).call({});})();(function(){var a="-",b="li",c="qx-hbox",d="keydown",f="height",g="px",h="<div class='",j="Left",k="-button-active",l="-page-closed",m="transitionDuration",n="right",o="div",p="orientation",r="changeSelected",s="button",t="transitionProperty",u="mediaQuery",v="> .",w="qx-flex-ready",x="<ul/>",y="change",z="> ul > .",A="> ul.qx-hbox",B="0px",C="-vertical",D="justify",E="-right",F="openedHeight",G="horizontal",H="transitionTimingFunction",I="__qxtransition",J="transitionDelay",K="String",L="browser.documentmode",M="resize",N="align",O="-justify",P="vertical",Q="> ul",R="",S="mshtml",T="number",U="-button",V="<div>",W="-horizontal",X="-left",Y="Right",bC="-container'>",bD="ul",bE="-container",by="<li>",bz="qx.ui.website.Tabs",bA="> ul > li",bB="preselected",bI="engine.name",bR="<li><button>{{{content}}}</button></li>",bJ="block",bO="-page",bF="none",bG="Number",bH="> ul .",bw="transition",bL="left",bx="tap",bM=".",bN="qx-flex1",bP="undefined",bK="id",bQ="qx-flex-justify-end";qx.Bootstrap.define(bz,{extend:qx.ui.website.Widget,statics:{tabs:function(bT,bU,bV){var bS=new qx.ui.website.Tabs(this);if(typeof bU!==bP){bS.setConfig(bB,bU);};bS.init();if(bT){bS.setConfig(N,bT);};if(bV){bS.setConfig(p,bV);};if(bT||bV){bS.render();};return bS;},_templates:{button:bR},_config:{preselected:0,align:bL,orientation:G,mediaQuery:null}},construct:function(bX,bW){qx.ui.website.Widget.call(this,bX,bW);},events:{"changeSelected":bG},members:{__mediaQueryListener:null,init:function(){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};var cg=this.getConfig(u);if(cg){this.setConfig(p,this._initMediaQueryListener(cg));};var cb=this.getConfig(p);this.addClasses([this.getCssPrefix(),this.getCssPrefix()+a+cb,w]);if(this.getChildren(bD).length===0){var cc=qxWeb.create(x);var content=this.getChildren();if(content.length>0){cc.insertBefore(content.eq(0));}else {this.append(cc);};};var ca=this.find(v+this.getCssPrefix()+bE);var cd=this.getChildren(bD).getFirst().getChildren(b).not(bM+this.getCssPrefix()+bO);cd._forEachElementWrapped(function(ch){ch.addClass(this.getCssPrefix()+U);var ck=ch.getData(this.getCssPrefix()+bO);if(!ck){return;};ch.addClass(this.getCssPrefix()+U).on(bx,this._onTap,this);var ci=this._getPage(ch);if(ci.length>0){ci.addClass(this.getCssPrefix()+bO);if(cb==P){this.__deactivateTransition(ci);if(q.getNodeName(ci[0])==o){var cl=q.create(by).addClass(this.getCssPrefix()+bO).setAttribute(bK,ci.getAttribute(bK)).insertAfter(ch[0]);ci.remove().getChildren().appendTo(cl);ci=cl;};this._storePageHeight(ci);}else if(cb==G){if(q.getNodeName(ci[0])==b){var cj=q.create(V).addClass(this.getCssPrefix()+bO).setAttribute(bK,ci.getAttribute(bK));ci.remove().getChildren().appendTo(cj);ci=cj;};};if(cb==G){if(ca.length===0){ca=qxWeb.create(h+this.getCssPrefix()+bC).insertAfter(this.find(Q)[0]);};ci.appendTo(ca[0]);};};this._showPage(null,ch);this.__activateTransition(ci);}.bind(this));if(cb==P&&ca.length==1&&ca.getChildren().length===0){ca.remove();};if(cb==G&&this.getConfig(N)==n&&q.env.get(bI)==S&&q.env.get(L)<10){cd.remove();for(var i=cd.length-1;i>=0;i-- ){this.find(Q).append(cd[i]);};};var cf=cd.filter(bM+this.getCssPrefix()+k);var ce=this.getConfig(bB);if(cf.length===0&&typeof ce==T){cf=cd.eq(ce).addClass(this.getCssPrefix()+k);};if(cf.length>0){var bY=this._getPage(cf);this.__deactivateTransition(bY);this._showPage(cf,null);this.__activateTransition(bY);};this.getChildren(bD).getFirst().on(d,this._onKeyDown,this);if(cb===G){this._applyAlignment(this);};qxWeb(window).on(M,this._onResize,this);return true;},render:function(){var cm=this.getConfig(u);if(cm){this.setConfig(p,this._initMediaQueryListener(cm));};var cn=this.getConfig(p);if(cn===G){return this._renderHorizontal();}else if(cn===P){return this._renderVertical();};},_initMediaQueryListener:function(co){var cp=this.__mediaQueryListener;if(!cp){cp=q.matchMedia(co);this.__mediaQueryListener=cp;cp.on(y,function(cq){this.render();}.bind(this));};if(cp.matches){return G;}else {return P;};},_renderHorizontal:function(){this.removeClass(this.getCssPrefix()+C).addClasses([this.getCssPrefix()+R,this.getCssPrefix()+W]).find(Q).addClass(c);var ct=this.find(v+this.getCssPrefix()+bE);if(ct.length==0){ct=qxWeb.create(h+this.getCssPrefix()+bC).insertAfter(this.find(Q)[0]);};var cs;this.find(z+this.getCssPrefix()+U)._forEachElementWrapped(function(cw){var cv=this.find(cw.getData(this.getCssPrefix()+bO));if(q.getNodeName(cv[0])==b){var cu=q.create(V).addClass(this.getCssPrefix()+bO).setAttribute(bK,cv.getAttribute(bK));cv.remove().getChildren().appendTo(cu);cv=cu;};cv.appendTo(ct[0]);this._switchPages(cv,null);if(cw.hasClass(this.getCssPrefix()+k)){cs=cv;};}.bind(this));if(!cs){var cr=this.find(z+this.getCssPrefix()+U).eq(0).addClass(this.getCssPrefix()+k);cs=this._getPage(cr);};this._switchPages(null,cs);this._applyAlignment(this);this.setEnabled(this.getEnabled());return this;},_renderVertical:function(){this.find(A).removeClass(c);this.removeClasses([this.getCssPrefix()+W]).addClasses([this.getCssPrefix()+R,this.getCssPrefix()+C]).getChildren(bD).getFirst().getChildren(b).not(bM+this.getCssPrefix()+bO)._forEachElementWrapped(function(cx){cx.addClass(this.getCssPrefix()+U);var cy=this._getPage(cx);if(cy.length===0){return;};this.__deactivateTransition(cy);if(q.getNodeName(cy[0])==o){var cz=q.create(by).addClass(this.getCssPrefix()+bO).setAttribute(bK,cy.getAttribute(bK));cy.getChildren().appendTo(cz);cz.insertAfter(cx[0]);cy.remove();cy=cz;};this._storePageHeight(cy);if(cx.hasClass(this.getCssPrefix()+k)){this._switchPages(null,cy);}else {this._switchPages(cy,null);};this.__activateTransition(cy);}.bind(this));this.setEnabled(this.getEnabled());return this;},_onResize:function(){setTimeout(function(){if(this.getConfig(p)==P){this._renderVertical();};}.bind(this),100);},addButton:function(cC,cD){var cE=qxWeb.create(qxWeb.template.render(this.getTemplate(s),{content:cC})).addClass(this.getCssPrefix()+U);var cB=this.find(Q);var cA=cB.getChildren(b);if(cB.hasClass(this.getCssPrefix()+E)&&cA.length>0){cE.insertBefore(cA.getFirst());}else {cE.appendTo(cB);};cE.on(bx,this._onTap,this).addClass(this.getCssPrefix()+U);if(this.find(bH+this.getCssPrefix()+U).length===1){cE.addClass(this.getCssPrefix()+k);};if(cD){cE.setData(this.getCssPrefix()+bO,cD);var cF=this._getPage(cE);cF.addClass(this.getCssPrefix()+bO);if(cE.hasClass(this.getCssPrefix()+k)){this._switchPages(null,cF);}else {this._switchPages(cF,null);};};return this;},select:function(cH){var cG=this.find(z+this.getCssPrefix()+U);var cI=this.find(z+this.getCssPrefix()+k).removeClass(this.getCssPrefix()+k);if(this.getConfig(N)==n){cH=cG.length-1-cH;};var cJ=cG.eq(cH).addClass(this.getCssPrefix()+k);this._showPage(cJ,cI);this.emit(r,cH);return this;},_onTap:function(e){if(!this.getEnabled()){return;};var cM=this.getConfig(p);var cN=e.getCurrentTarget();var cP=this.find(z+this.getCssPrefix()+k);if(cP[0]==cN&&cM==G){return;};cP.removeClass(this.getCssPrefix()+k);if(cM==P){this._showPage(null,cP);if(cP[0]==cN&&cM==P){return;};};var cL;var cO=this.find(z+this.getCssPrefix()+U)._forEachElementWrapped(function(cQ){if(cN===cQ[0]){cL=cQ;};});this._showPage(cL,cP);cL.addClass(this.getCssPrefix()+k);var cK=cO.indexOf(cL[0]);if(this.getConfig(N)==n){cK=cO.length-1-cK;};this.emit(r,cK);},_onKeyDown:function(e){var cU=e.getKeyIdentifier();if(!(cU==j||cU==Y)){return;};var cW=this.getConfig(N)==n;var cT=this.find(z+this.getCssPrefix()+U);if(cW){cT.reverse();};var cV=this.find(z+this.getCssPrefix()+k);var cS;if(cU==Y){if(!cW){cS=cV.getNext(bM+this.getCssPrefix()+U);}else {cS=cV.getPrev(bM+this.getCssPrefix()+U);};}else {if(!cW){cS=cV.getPrev(bM+this.getCssPrefix()+U);}else {cS=cV.getNext(bM+this.getCssPrefix()+U);};};if(cS.length>0){var cR=cT.indexOf(cS);this.select(cR);cS.getChildren(s).focus();};},_showPage:function(da,cY){var db=this._getPage(cY);var cX=this._getPage(da);if(this.getConfig(p)===G&&(db[0]==cX[0])){return;};this._switchPages(db,cX);},_switchPages:function(dd,dc){var df=this.getConfig(p);if(df===G){if(dd){dd.hide();};if(dc){dc.show();};}else if(df===P){if(dd&&dd.length>0){dd.setStyle(f,dd.getHeight()+g);dd[0].offsetHeight;dd.setStyles({"height":B,"paddingTop":B,"paddingBottom":B});dd.addClass(this.getCssPrefix()+l);};if(dc&&dc.length>0){dc.removeClass(this.getCssPrefix()+l);if(!dc.getStyle(bw)||dc.getStyle(bw).indexOf(bF)===0){dc.setStyle(f,R);}else {var de=dc.getProperty(F);if(qxWeb.type.get(de)==K){dc.setStyle(f,de);};};};};},_getPage:function(dg){var dh;if(dg){dh=dg.getData(this.getCssPrefix()+bO);};return this.find(dh);},_applyAlignment:function(di){var dk=di.getConfig(N);var dj=di.find(bA);if(q.env.get(bI)==S&&q.env.get(L)<10){if(dk==bL){di.addClass(this.getCssPrefix()+X);}else {di.removeClass(this.getCssPrefix()+X);};if(dk==D){di.addClass(this.getCssPrefix()+O);}else {di.removeClass(this.getCssPrefix()+O);};if(dk==n){di.addClass(this.getCssPrefix()+E);}else {di.removeClass(this.getCssPrefix()+E);};}else {di.find(Q).addClass(c);if(dk==D){dj.addClass(bN);}else {dj.removeClass(bN);};if(dk==n){di.find(Q).addClass(bQ);}else {di.find(Q).removeClass(bQ);};};},_storePageHeight:function(dq){var dn=this.getCssPrefix()+l;var dl=dq.hasClass(dn);if(dl){dq.removeClass(this.getCssPrefix()+l);};var dm=dq[0].style.display;var dp=dq[0].style.height;dq[0].style.height=R;dq[0].style.display=bJ;dq.setProperty(F,dq.getHeight()+g);if(dl){dq.addClass(this.getCssPrefix()+l);};dq[0].style.height=dp;dq[0].style.display=dm;},__deactivateTransition:function(ds){var dr=ds.getStyles([J,m,t,H]);if(dr.transitionProperty.indexOf(bF)==-1){ds.setProperty(I,dr);ds.setStyle(bw,bF);};},__activateTransition:function(dv){var du=dv.getProperty(I);var dt=dv.getStyle(t);if(du&&dt.indexOf(bF)!=-1){dv.setStyles(du);dv.setProperty(I,R);};},dispose:function(){this.__mediaQueryListener=undefined;var dw=this.getCssPrefix();qxWeb(window).off(M,this._onResize,this);this.find(z+this.getCssPrefix()+U).off(bx,this._onTap,this);this.getChildren(bD).getFirst().off(d,this._onKeyDown,this).setHtml(R);this.setHtml(R).removeClasses([dw,w]);return qx.ui.website.Widget.prototype.dispose.call(this);}},defer:function(dx){qxWeb.$attach({tabs:dx.tabs});}});})();(function(){var a="q";qx.Bootstrap.define(a,{extend:qxWeb});q=qxWeb;})();(function(){var a="none",b="src",c="qx.ui.website.Button",d="<span>",f="bottom-left",g="img",h="tap",i="inline",j="span",k="<img>",l="display";qx.Bootstrap.define(c,{extend:qx.ui.website.Widget,statics:{button:function(n,o){var m=new qx.ui.website.Button(this);m.init();if(n!=null){m.setLabel(n);};if(o!=null){m.setIcon(o);};return m;}},construct:function(q,p){qx.ui.website.Widget.call(this,q,p);},members:{init:function(){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};if(this.getChildren(j)==0){qxWeb.create(d).appendTo(this);};if(this.getChildren(g)==0){qxWeb.create(k).appendTo(this).setStyle(l,a);};return true;},setLabel:function(r){this.getChildren(j).setHtml(r);return this;},getLabel:function(){return this.getChildren(j).getHtml();},setIcon:function(s){var t=this.getChildren(g);t.setAttribute(b,s);t.setStyle(l,s?i:a);return this;},getIcon:function(){return this.getChildren(g).getAttribute(b);},setMenu:function(u){this.on(h,function(e){if(u.getStyle(l)===a){u.placeTo(this,f);u.show();qxWeb(document).once(h,function(){u.hide();});}else {u.hide();};e.stopPropagation();});return this;}},defer:function(v){qxWeb.$attach({button:v.button});}});})();(function(){var a="qx.module.event.Pointer",b="pointerup",c="pointerover",d="pointerdown",e="pointermove",f="pointercancel",g="pointerout";qx.Bootstrap.define(a,{statics:{TYPES:[d,b,e,f,c,g],getPointerType:function(){return false;},getViewportLeft:function(){return false;},getViewportTop:function(){return false;},getDocumentLeft:function(){return false;},getDocumentTop:function(){return false;},getScreenLeft:function(){return false;},getScreenTop:function(){return false;},normalize:function(event,h){if(!event){return event;};qx.event.type.dom.Pointer.normalize(event);return event;}},defer:function(i){qxWeb.$registerEventNormalization(qx.module.event.Pointer.TYPES,i.normalize);}});})();(function(){var a="type",b="qx.module.util.Type";qx.Bootstrap.define(b,{statics:{get:qx.Bootstrap.getClass},defer:function(c){qxWeb.$attachAll(this,a);}});})();(function(){var a="blur",b="keydown",c="px",d="Please provide a Number value for 'value'!",f="true",g="focus",h="paddingLeft",k="changeValue",l="pan-y",m="Left",n="auto",o="cursor",p="changePosition",q="",r="Array",s="{{value}}",t="minimum",u="pointer",v="offset",w="knobContent",y="resize",z="pointerup",A="dragstart",B="Right",C="step",D="pointerdown",E="pointermove",F="false",G="maximum",H="<button>",I="Number",J="css.transform",K="paddingRight",L="touch-action",M="left",N="-knob",O=".",P="undefined",Q="qx.ui.website.Slider";qx.Bootstrap.define(Q,{extend:qx.ui.website.Widget,statics:{_config:{minimum:0,maximum:100,offset:0,step:1},_templates:{knobContent:s},slider:function(T,S){var R=new qx.ui.website.Slider(this);R.init();if(typeof S!==P){R.setConfig(C,S);};if(typeof T!==P){R.setValue(T);}else {R.setValue(R.getConfig(t));};return R;}},construct:function(V,U){qx.ui.website.Widget.call(this,V,U);},events:{"changeValue":I,"changePosition":I},members:{__dragMode:null,_value:0,init:function(){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};var W=this.getCssPrefix();if(!this.getValue()){var X=this.getConfig(C);var Y=qxWeb.type.get(X)==r?X[0]:this.getConfig(t);this._value=Y;};this.on(z,this._onSliderPointerUp,this).on(g,this._onSliderFocus,this).setStyle(L,l);qxWeb(document).on(z,this._onDocPointerUp,this);qxWeb(window).on(y,this._onWindowResize,this);if(this.getChildren(O+W+N).length===0){this.append(qx.ui.website.Widget.create(H).addClass(W+N));};this.getChildren(O+W+N).setAttributes({"draggable":F,"unselectable":f}).setHtml(this._getKnobContent()).on(D,this._onPointerDown,this).on(A,this._onDragStart,this).on(g,this._onKnobFocus,this).on(a,this._onKnobBlur,this);this.render();return true;},getValue:function(){return this._value;},setValue:function(bc){if(qxWeb.type.get(bc)!=I){throw Error(d);};var bb=this.getConfig(C);if(qxWeb.type.get(bb)!=r){var bd=this.getConfig(t);var ba=this.getConfig(G);if(bc<bd){bc=bd;};if(bc>ba){bc=ba;};if(qxWeb.type.get(bb)==I){bc=Math.round(bc/bb)*bb;};};this._value=bc;if(qxWeb.type.get(bb)!=r||bb.indexOf(bc)!=-1){this.__valueToPosition(bc);this.getChildren(O+this.getCssPrefix()+N).setHtml(this._getKnobContent());this.emit(k,bc);};return this;},render:function(){var be=this.getConfig(C);if(qxWeb.type.get(be)==r){this._getPixels();if(be.indexOf(this.getValue())==-1){this.setValue(be[0]);}else {this.setValue(this.getValue());};}else if(qxWeb.type.get(be)==I){this.setValue(Math.round(this.getValue()/be)*be);}else {this.setValue(this.getValue());};this.getChildren(O+this.getCssPrefix()+N).setHtml(this._getKnobContent());return this;},_getKnobContent:function(){return qxWeb.template.render(this.getTemplate(w),{value:this.getValue()});},_getHalfKnobWidth:function(){var bf=this.getChildren(O+this.getCssPrefix()+N).getWidth();return Math.round(parseFloat(bf/2));},_getDragBoundaries:function(){var bh=Math.ceil(parseFloat(this.getStyle(h))||0);var bg=Math.ceil(parseFloat(this.getStyle(K))||0);var bi=this.getConfig(v);return {min:this.getOffset().left+bi+bh,max:this.getOffset().left+this.getWidth()-bi-bg};},_getPixels:function(){var bn=this.getConfig(C);if(qxWeb.type.get(bn)!=r){return [];};var bp=this._getDragBoundaries();var br=[];br.push(bp.min);var bm=bn.length-1;var bj=Math.ceil(parseFloat(this.getStyle(h))||0);var bq=Math.ceil(parseFloat(this.getStyle(K))||0);var bl=this.getWidth()-(this.getConfig(v)*2)-bj-bq;var bo=bl/(bn[bm]-bn[0]);var bk=0;for(var i=1,j=bn.length-1;i<j;i++ ){bk=bn[i]-bn[0];br.push(Math.round(bk*bo)+bp.min);};br.push(bp.max);return br;},_getNearestValue:function(bB){var bD=this._getPixels();if(bD.length===0){var bC=this._getDragBoundaries();var bv=bC.max-bC.min;var bu=bB-bC.min;var by=bu/bv;var bt=this.getConfig(t);var bz=this.getConfig(G);var bx=(bz-bt)*by+bt;if(bx<bt){bx=bt;};if(bx>bz){bx=bz;};var bw=this.getConfig(C);if(qxWeb.type.get(bw)==I){bx=Math.round(bx/bw)*bw;};return bx;};var bA=0,bE=0,bs=0;for(var i=0,j=bD.length;i<j;i++ ){if(bB>=bD[i]){bA=i;bE=bD[i];bs=bD[i+1]||bE;}else {break;};};bA=Math.abs(bB-bE)<=Math.abs(bB-bs)?bA:bA+1;return this.getConfig(C)[bA];},_onSliderPointerUp:function(e){if((e.getDocumentLeft()===0&&e.getDocumentTop()===0)||!this.getEnabled()){return;};this.setValue(this._getNearestValue(e.getDocumentLeft()));},_onPointerDown:function(e){if(this.__dragMode){return;};this.__dragMode=true;qxWeb(document.documentElement).on(E,this._onPointerMove,this).setStyle(o,u);e.stopPropagation();},_onDocPointerUp:function(e){if(this.__dragMode===true){delete this.__dragMode;this.__valueToPosition(this.getValue());qxWeb(document.documentElement).off(E,this._onPointerMove,this).setStyle(o,n);e.stopPropagation();};},_onPointerMove:function(e){e.preventDefault();if(this.__dragMode){var bH=e.getDocumentLeft();var bG=this._getDragBoundaries();var bI=Math.ceil(parseFloat(this.getStyle(h))||0);var bF=bH-this.getOffset().left-this._getHalfKnobWidth()-bI;if(bH>=bG.min&&bH<=bG.max){this.setValue(this._getNearestValue(bH));if(bF>0){this._setKnobPosition(bF);this.emit(p,bF);};};e.stopPropagation();};},_onDragStart:function(e){e.stopPropagation();e.preventDefault();},_onSliderFocus:function(e){this.getChildren(O+this.getCssPrefix()+N).focus();},_onKnobFocus:function(e){this.getChildren(O+this.getCssPrefix()+N).on(b,this._onKeyDown,this);},_onKnobBlur:function(e){this.getChildren(O+this.getCssPrefix()+N).off(b,this._onKeyDown,this);},_onKeyDown:function(e){var bO;var bJ=this.getValue();var bM=this.getConfig(C);var bL=qxWeb.type.get(bM);var bN=e.getKeyIdentifier();var bK;if(bN==B){if(bL===r){bK=bM.indexOf(bJ);if(bK!==undefined){bO=bM[bK+1]||bJ;};}else if(bL===I){bO=bJ+bM;}else {bO=bJ+1;};}else if(bN==m){if(bL===r){bK=bM.indexOf(bJ);if(bK!==undefined){bO=bM[bK-1]||bJ;};}else if(bL===I){bO=bJ-bM;}else {bO=bJ-1;};}else {return;};this.setValue(bO);},_setKnobPosition:function(x){var bP=this.getChildren(O+this.getCssPrefix()+N);if(qxWeb.env.get(J)){bP.translate([x+c,0,0]);}else {bP.setStyle(M,x+c);};},_onWindowResize:function(){if(qxWeb.type.get(this.getConfig(C))==r){this._getPixels();};this.__valueToPosition(this._value);},__valueToPosition:function(bS){var bY=this._getPixels();var bR=Math.ceil(parseFloat(this.getStyle(h))||0);var bQ;if(bY.length>0){bQ=bY[this.getConfig(C).indexOf(bS)]-bR;}else {var bX=this._getDragBoundaries();var bT=bX.max-bX.min;var bU=this.getConfig(G)-this.getConfig(t);var bV=(bS-this.getConfig(t))/bU;bQ=(bT*bV)+bX.min-bR;};var bW=bQ-this.getOffset().left-this._getHalfKnobWidth();this._setKnobPosition(bW);this.emit(p,bW);},dispose:function(){qxWeb(document).off(z,this._onDocPointerUp,this);qxWeb(window).off(y,this._onWindowResize,this);this.off(z,this._onSliderPointerUp,this).off(g,this._onSliderFocus,this);this.getChildren(O+this.getCssPrefix()+N).off(D,this._onPointerDown,this).off(A,this._onDragStart,this).off(g,this._onKnobFocus,this).off(a,this._onKnobBlur,this).off(b,this._onKeyDown,this);this.setHtml(q);return qx.ui.website.Widget.prototype.dispose.call(this);}},defer:function(ca){qxWeb.$attach({slider:ca.slider});}});})();(function(){var c="Tue",d="keydown",f="-weekend",g="Given date range is not valid. Please verify the 'selectableWeekDays' config",h="Mon",j="focus",k="Given date ",l="blur",m="changeValue",n="Space",o="<td colspan='1' class='{{cssPrefix}}-prev-container'><button class='{{cssPrefix}}-prev' {{prevDisabled}} title='Previous Month'>&lt;</button></td>",p="{{#row}}<td class='{{cssPrefix}}-dayname'>{{.}}</td>{{/row}}",q="-next",r="<tr>",s=".qx-calendar-invalid",t="dayRow",u="Sun",v="qx-hidden",w="-today",x="controls",y="-past",z=" is earlier than configured minDate ",A="range",B=" is later than configured maxDate ",C=" ",D="-day[value='",E="May",F="minDate",G="table",H="hideDaysOtherMonth",I="<table class='{{cssPrefix}}-container'><thead>{{{thead}}}</thead><tbody>{{{tbody}}}</tbody></table>",J="value",K="tabindex",L="disabled",M="disabled=disabled",N="September",O="</tr>",P="']",Q="December",R="monthNames",S="disableDaysOtherMonth",T="*",U="",V="April",W="<td colspan='5' class='{{cssPrefix}}-month'>{{month}} {{year}}</td>",X="Thu",Y="Sat",bQ="row",bR="November",bS="-next-month",bM="June",bN="single",bO="Right",bP="October",bX="qx.ui.website.Calendar",bY="-selected",ci='rendered',bK="Left",bT="The given date's week day is not selectable.",bU="March",bV="td",bW="Given date range is not valid. Please verify the 'minDate' and 'maxDate' configs",cc="July",cs="-prev",cd="dayNames",ce="August",bL="selectionMode",ca="{{#row}}<td class='{{cssClass}}'><button class='{{cssPrefix}}-day {{hidden}}' {{disabled}} value='{{date}}'>{{day}}</button></td>{{/row}}",cm="> td > .",cb="January",cf="February",cg="Wed",ch="Date",cn="maxDate",co="tap",cp=".",bJ="Fri",cj="selectableWeekDays",ck="-previous-month",cl="-day",cq="-weekday",cr="<td colspan='1' class='{{cssPrefix}}-next-container'><button class='{{cssPrefix}}-next' {{nextDisabled}} title='Next Month'>&gt;</button></td>";qx.Bootstrap.define(bX,{extend:qx.ui.website.Widget,statics:{_templates:{controls:r+o+W+cr+O,dayRow:r+p+O,row:r+ca+O,table:I},_config:{monthNames:[cb,cf,bU,V,E,bM,cc,ce,N,bP,bR,Q],dayNames:[h,c,cg,X,bJ,Y,u],minDate:null,maxDate:null,selectableWeekDays:[0,1,2,3,4,5,6],selectionMode:bN,hideDaysOtherMonth:false,disableDaysOtherMonth:false},calendar:function(ct){var cu=new qx.ui.website.Calendar(this);cu.init();if(ct!==undefined){cu.setValue(ct);};return cu;}},construct:function(cw,cv){qx.ui.website.Widget.call(this,cw,cv);},events:{"changeValue":ch,"rendered":U},members:{__range:null,_value:null,_shownValue:null,init:function(){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};this.__range=[];var cx=new Date();cx=this._getNormalizedDate(cx);this.showValue(cx);return true;},render:function(){var cz=this.getConfig(F);if(cz){cz=this._getNormalizedDate(cz);};var cy=this.getConfig(cn);if(cy){cy=this._getNormalizedDate(cy);};this.showValue(this._shownValue);return this;},setEnabled:function(cA){this.setAttribute(L,!cA);if(cA===true){this.render();}else {this.find(T).setAttribute(L,!cA);};return this;},setValue:function(cC){var cD=this.getConfig(F);var cB=this.getConfig(cn);if(this.getConfig(bL)==bN){cC=this._getNormalizedDate(cC);if(this.getConfig(cj).indexOf(cC.getDay())==-1){throw new Error(bT);};if(cD){cD=this._getNormalizedDate(cD);if(cC<cD){throw new Error(k+cC.toDateString()+z+cD.toDateString());};};if(cB){cB=this._getNormalizedDate(cB);if(cC>cB){throw new Error(k+cC.toDateString()+B+cB.toDateString());};};}else if(this.getConfig(bL)==A){if(!this.__range){this.__range=cC.map(function(cE){return cE.toDateString();});};if(cC.length==2){cC.sort(function(a,b){return a-b;});cC=this._generateRange(cC);}else {cC[0]=this._getNormalizedDate(cC[0]);};};this._value=cC;this.showValue(cC);if((this.getConfig(bL)==bN)||((this.getConfig(bL)==A)&&(cC.length>=1))){this.emit(m,cC);};return this;},getValue:function(){var cF=this._value;return cF?(qx.Bootstrap.isArray(cF)?cF:new Date(cF)):null;},showValue:function(cH){cH=qx.Bootstrap.isArray(cH)?cH[cH.length-1]:cH;this._shownValue=cH;var cG=this.getCssPrefix();if(this.getAttribute(K)<0){this.setAttribute(K,0);};this.find(cp+cG+cs).off(co,this._prevMonth,this);this.find(cp+cG+q).off(co,this._nextMonth,this);this.find(cp+cG+cl).off(co,this._selectDay,this);this.off(j,this._onFocus,this,true).off(l,this._onBlur,this,true);this.setHtml(this._getTable(cH));this.find(cp+cG+cs).on(co,this._prevMonth,this);this.find(cp+cG+q).on(co,this._nextMonth,this);this.find(bV).not(s).find(cp+cG+cl).on(co,this._selectDay,this);this.on(j,this._onFocus,this,true).on(l,this._onBlur,this,true);this.emit(ci);return this;},_prevMonth:function(){var cI=this._shownValue;this.showValue(new Date(cI.getFullYear(),cI.getMonth()-1));},_nextMonth:function(){var cJ=this._shownValue;this.showValue(new Date(cJ.getFullYear(),cJ.getMonth()+1));},_selectDay:function(e){var cM=qxWeb(e.getTarget());var cL=cM.getAttribute(J);var cN=new Date(cL);if(this.getConfig(bL)==A){var cK=this.__range.slice(0);if(cK.length==2){cK=[];};cK.push(cL);this.__range=cK;cK=cK.map(function(cO){return new Date(cO);});this.setValue(cK);cL=cK;}else {this.setValue(cN);cL=[cL];};cL.forEach(function(cP){this.find(cp+this.getCssPrefix()+D+cP+P).focus();}.bind(this));},_getTable:function(cQ){var cT=qxWeb.template.render(this.getTemplate(x),this._getControlsData(cQ));var cS=qxWeb.template.render(this.getTemplate(t),this._getDayRowData());var cR={thead:cT+cS,tbody:this._getWeekRows(cQ),cssPrefix:this.getCssPrefix()};return qxWeb.template.render(this.getTemplate(G),cR);},_getControlsData:function(cU){var cX=U;var cW=this.getConfig(F);if(cW){cW=this._getNormalizedDate(cW);if(cU.getMonth()<=cW.getMonth()){cX=L;};};var cY=U;var cV=this.getConfig(cn);if(cV){cV=this._getNormalizedDate(cV);if(cU.getMonth()>=cV.getMonth()){cY=L;};};return {month:this.getConfig(R)[cU.getMonth()],year:cU.getFullYear(),cssPrefix:this.getCssPrefix(),prevDisabled:cX,nextDisabled:cY};},_getDayRowData:function(){return {row:this.getConfig(cd),cssPrefix:this.getCssPrefix()};},_getWeekRows:function(dl){dl=qx.Bootstrap.isArray(dl)?dl[dl.length-1]:dl;var dq=[];var dg=null,ds=null;var dr=new Date();var dk=this._getHelpDate(dl);var dd=this.getCssPrefix();var dn=this.getConfig(F);if(dn){dn=this._getNormalizedDate(dn);};var de=this.getConfig(cn);if(de){this._getNormalizedDate(de);};var dj=this.getConfig(H);var db=this.getConfig(S);if(qx.Bootstrap.isArray(this._value)){ds=this._value.map(function(dt){return dt.toDateString();});};for(var da=0;da<6;da++ ){var dp={row:[]};for(var i=0;i<7;i++ ){var df=U;var dc=U;var dh=U;if(dk.getMonth()!==dl.getMonth()){if(dj===true&&da===5&&i===0){break;};if((dk.getMonth()<dl.getMonth()&&dk.getFullYear()==dl.getFullYear())||(dk.getMonth()>dl.getMonth()&&dk.getFullYear()<dl.getFullYear())){df+=dd+ck;}else {df+=dd+bS;};dc+=dj?v:U;dh+=db?M:U;};if((this.getConfig(bL)==A)&&qx.Bootstrap.isArray(this._value)){if(ds.indexOf(dk.toDateString())!=-1){df+=C+dd+bY;};}else {var di=this.__range;if(this._value){dg=this.getConfig(bL)==A?new Date(di[di.length-1]):this._value;df+=dk.toDateString()===dg.toDateString()?C+dd+bY:U;};};var dm=Date.parse(dr)>Date.parse(dk)&&dr.toDateString()!==dk.toDateString();df+=dm?C+dd+y:U;df+=dr.toDateString()===dk.toDateString()?C+dd+w:U;if(dh===U){dh=this.getEnabled()?U:M;if((dn&&dk<dn)||(de&&dk>de)||this.getConfig(cj).indexOf(dk.getDay())==-1){dh=M;};};df+=(dk.getDay()===0||dk.getDay()===6)?C+dd+f:C+dd+cq;dp.row.push({day:dk.getDate(),date:dk.toDateString(),cssPrefix:dd,cssClass:df,disabled:dh,hidden:dc});dk.setDate(dk.getDate()+1);};dq.push(qxWeb.template.render(this.getTemplate(bQ),dp));};return dq.join(U);},_getHelpDate:function(du){var dw=1;var dy=new Date(du.getFullYear(),du.getMonth(),1);var dv=dy.getDay();dy=new Date(du.getFullYear(),du.getMonth(),1,0,0,0);var dx=(7+dv-dw)%7;dy.setDate(dy.getDate()-dx);return dy;},_getNormalizedDate:function(dA){var dz=new Date(dA.getTime());dz.setHours(0);dz.setMinutes(0);dz.setSeconds(0);dz.setMilliseconds(0);return dz;},_onFocus:function(e){this.on(d,this._onKeyDown,this);},_onBlur:function(e){if(this.contains(e.getRelatedTarget()).length===0){this.off(d,this._onKeyDown,this);};},_onKeyDown:function(e){var dB=this.getCssPrefix();var dC=qxWeb(e.getTarget());var dD=e.getKeyIdentifier();var dE=dC.hasClass(dB+cl);if(dE){if(dD==n){this._selectDay(e);}else if(dD==bO){e.preventDefault();this._focusNextDay(dC);}else if(dD==bK){e.preventDefault();this._focusPrevDay(dC);};}else {if(dD==n){if(dC.hasClass(dB+cs)){e.preventDefault();this._prevMonth();this.find(cp+dB+cs).focus();}else if(dC.hasClass(dB+q)){e.preventDefault();this._nextMonth();this.find(cp+dB+q).focus();};}else if(dD==bO){e.preventDefault();this._nextMonth();this.find(cp+dB+q).focus();}else if(dD==bK){e.preventDefault();this._prevMonth();this.find(cp+dB+cs).focus();};};e.stopPropagation();},_focusNextDay:function(dK){var dJ=this.getCssPrefix();var dI=dK.getParents().getNext();if(dI.length>0){dI.getChildren(cp+dJ+cl).focus();}else {var dH=dK.getParents().getParents().getNext();if(dH.length>0){dH.find(cm+dJ+cl).getFirst().focus();}else {this._nextMonth();var dG=new Date(dK.getAttribute(J));var dL=new Date(dG.valueOf());dL.setDate(dG.getDate()+1);var dF=dL.toDateString();this.find(cp+dJ+D+dF+P).focus();};};},_focusPrevDay:function(dR){var dQ=this.getCssPrefix();var dP=dR.getParents().getPrev();if(dP.length>0){dP.getChildren(cp+dQ+cl).focus();}else {var dN=dR.getParents().getParents().getPrev();if(dN.length>0){dN.find(cm+dQ+cl).getLast().focus();}else {this._prevMonth();var dO=new Date(dR.getAttribute(J));var dS=new Date(dO.valueOf());dS.setDate(dO.getDate()-1);var dM=dS.toDateString();this.find(cp+dQ+D+dM+P).focus();};};},_generateRange:function(dV){var dW=[],dT=dV[0];var dX=this.getConfig(F)?this.getConfig(F):new Date(dV[0].toDateString());var dU=this.getConfig(cn)?this.getConfig(cn):new Date(dV[1].toDateString());dX=this._getNormalizedDate(dX);dU=this._getNormalizedDate(dU);while(dT<=dV[1]){dT=this._getNormalizedDate(dT);dW.push(new Date(dT.toDateString()));dT.setDate(dT.getDate()+1);};dW=dW.filter(function(dY){return this.getConfig(cj).indexOf(dY.getDay())!=-1;},this);if(dW.length==0){throw new Error(g);};dW=dW.filter(function(ea){return (ea>=dX)&&(ea<=dU);},this);if(dW.length==0){throw new Error(bW);};return dW;},dispose:function(){var eb=this.getCssPrefix();this.find(cp+eb+cs).off(co,this._prevMonth,this);this.find(cp+eb+q).off(co,this._nextMonth,this);this.find(cp+eb+cl).off(co,this._selectDay,this);this.off(j,this._onFocus,this,true).off(l,this._onBlur,this,true).off(d,this._onKeyDown,this);this.setHtml(U);return qx.ui.website.Widget.prototype.dispose.call(this);}},defer:function(ec){qxWeb.$attach({calendar:ec.calendar});}});})();(function(){var a="</span>",b="blur",c="symbol",d="",f="Right",g="★",h="Number",j="Left",k="qx.ui.website.Rating",l="focus",m="tap",n="<span>",o="tabindex",p="-item-off",q="keydown",r=".",s="length",t="changeValue",u="span",v="-item";qx.Bootstrap.define(k,{extend:qx.ui.website.Widget,statics:{_config:{length:5,symbol:g},rating:function(w,y,length){var x=new qx.ui.website.Rating(this);x.init();var z=false;if(length!=undefined&&length!=x.getConfig(s)){x.setConfig(s,length);z=true;};if(y!=undefined){x.setConfig(c,y);z=true;};if(z){x.render();};if(w!=undefined){x.setValue(w);};return x;}},construct:function(B,A){qx.ui.website.Widget.call(this,B,A);},events:{"changeValue":h},members:{init:function(){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};this._updateSymbolLength();var C=this.getCssPrefix();if(this.getAttribute(o)<0){this.setAttribute(o,0);};this.on(l,this._onFocus,this).on(b,this._onBlur,this).getChildren(u).addClasses([C+v,C+p]).on(m,this._onTap,this);return true;},setValue:function(F){if(this.getValue()==F){return this;};if(F<0){F=0;};var D=this.getCssPrefix();var E=this.getChildren(u);E.removeClass(D+p);E.slice(F,E.length).addClass(D+p);this.emit(t,this.getValue());return this;},getValue:function(){var G=this.getCssPrefix();return this.getChildren(u).not(r+G+p).length;},render:function(){this._updateSymbolLength();},_updateSymbolLength:function(){var H=this.getCssPrefix();var length=this.getConfig(s);var I=this.getChildren();I.setHtml(this.getConfig(c));var J=length-I.length;if(J>0){for(var i=0;i<J;i++ ){qxWeb.create(n+this.getConfig(c)+a).on(m,this._onTap,this).addClasses([H+v,H+p]).appendTo(this);};}else {for(var i=0;i<Math.abs(J);i++ ){this.getChildren().getLast().off(m,this._onTap,this).remove();};};return this;},_onTap:function(e){var K=qxWeb(e.getTarget()).getParents();this.setValue(K.getChildren().indexOf(e.getTarget())+1);},_onFocus:function(e){qxWeb(document.documentElement).on(q,this._onKeyDown,this);},_onBlur:function(e){qxWeb(document.documentElement).off(q,this._onKeyDown,this);},_onKeyDown:function(e){var L=e.getKeyIdentifier();if(L===f){this.setValue(this.getValue()+1);}else if(L===j){this.setValue(this.getValue()-1);};},dispose:function(){qxWeb(document.documentElement).off(q,this._onKeyDown,this);this.off(l,this._onFocus,this).off(b,this._onBlur,this);this.getChildren(u).off(m,this._onTap,this);this.setHtml(d);return qx.ui.website.Widget.prototype.dispose.call(this);}},defer:function(M){qxWeb.$attach({rating:M.rating});}});})();(function(){var a='div#',b='Only the values "',c='changeValue',d='-icon',f='bottom-left',g='bottom-right',h='format',i='<img>',j='display',k='tap',l='datepicker-icon-',m='none',n='left-top',o='left-middle',p='icon',q='Wrong config value for "position"! ',r='<div id="',s='img#',t='readonly',u='left-bottom',v='both',w='qx.ui.website.DatePicker',x='input',y='" are supported!',z='absolute',A='right-bottom',B='right-middle',C='top-left',D='top-right',E='bottom-center',F='mode',G='#',H='position',I='top-center',J='right-top',K='", "',L='"></div>',M='datepicker-calendar-';qx.Bootstrap.define(w,{extend:qx.ui.website.Widget,statics:{__validPositions:null,_config:{format:function(N){return N.toLocaleDateString();},readonly:true,icon:null,mode:x,position:f},datepicker:function(O){var P=new qx.ui.website.DatePicker(this);P.init(O);return P;}},construct:function(R,Q){qx.ui.website.Widget.call(this,R,Q);},members:{_calendarId:null,_iconId:null,_uniqueId:null,getCalendar:function(){var S=qxWeb();S=S.concat(qxWeb(a+this._calendarId));return S;},init:function(T){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};var W=Math.round(Math.random()*10000);this._uniqueId=W;this.__setReadOnly(this);this.__setIcon(this);this.__addInputListener(this);var V=M+W;var U=qxWeb.create(r+V+L).calendar();U.on(k,this._onCalendarTap);U.appendTo(document.body).hide();this._calendarId=V;var X=qxWeb.getDocument(this).body;qxWeb(X).on(k,this._onBodyTap,this);U.on(c,this._calendarChangeValue,this);if(T!==undefined){U.setValue(T);};return true;},render:function(){this.getCalendar().render();this.__setReadOnly(this);this.__setIcon(this);this.__addInputListener(this);this.setEnabled(this.getEnabled());return this;},setConfig:function(name,ba){if(name===H){var Y=qx.ui.website.DatePicker.__validPositions;if(Y.indexOf(ba)===-1){throw new Error(q+b+Y.join(K)+y);};};qx.ui.website.Widget.prototype.setConfig.call(this,name,ba);return this;},_onTap:function(e){if(!this.getEnabled()){return;};var bb=this.getCalendar();if(bb.getStyle(j)==m){bb.setStyle(H,z).show().placeTo(this,this.getConfig(H));}else {bb.hide();};},_onCalendarTap:function(e){e.stopPropagation();},_onBodyTap:function(e){var bd=qxWeb(e.getTarget());if(this.length>0&&bd.length>0&&this[0]==bd[0]){return;};if(this.getConfig(p)!==null){var bc=qxWeb(G+this._iconId);if(bc.length>0&&bd.length>0&&bc[0]==bd[0]){return;};};if(this.getCalendar().isRendered()){var be=qxWeb(e.getTarget());if(be.isChildOf(this.getCalendar())===false){this.getCalendar().hide();};};},_calendarChangeValue:function(e){var bf=this.getConfig(h).call(this,e);this.setValue(bf);this.getCalendar().hide();},__setReadOnly:function(bg){if(bg.getConfig(t)){bg.setAttribute(t,t);}else {bg.removeAttribute(t);};},__setIcon:function(bj){var bk;if(bj.getConfig(p)===null){bk=bj.getNext(s+bj._iconId);if(bk.length===1){bk.off(k,this._onTap,bj);bk.remove();};}else {var bh=l+bj._uniqueId;if(bj._iconId==undefined){bj._iconId=bh;bk=qxWeb.create(i);bk.setAttributes({id:bh,src:bj.getConfig(p)});bk.addClass(this.getCssPrefix()+d);var bi=bj.getConfig(F);if(bi===p||bi===v){if(!bk.hasListener(k,this._onTap,bj)){bk.on(k,this._onTap,bj);};};bk.insertAfter(bj);};};},__addInputListener:function(bl){if(bl.getConfig(F)===p){bl.off(k,bl._onTap);}else {if(!bl.hasListener(k,bl._onTap)){bl.on(k,bl._onTap);};};},dispose:function(){this.removeAttribute(t);this.getNext(s+this._iconId).remove();this.off(k,this._onTap);var bn=qxWeb.getDocument(this).body;qxWeb(bn).off(k,this._onBodyTap,this);this.getCalendar().off(c,this._calendarChangeValue,this).off(k,this._onCalendarTap);var bm=qxWeb(a+this._calendarId);bm.remove();bm.dispose();return qx.ui.website.Widget.prototype.dispose.call(this);}},defer:function(bo){qxWeb.$attach({datepicker:bo.datepicker});bo.__validPositions=[C,I,D,f,E,g,n,o,u,J,B,A];}});})();(function(){var c="-",f="</div>",g="qx-table-input-label",h="qx-table-row-selection",k="qx.ui.website.Table",m="'], #",o="<td class='qx-table-cell' data-qx-table-cell-key='{{ cellKey }}'>",p=" does not exists !",q="' for='",r="<",s="'><input id='",t="qx-table-row-selected",u="selectionChange",v=" input",w="TABLE",z="td",A="[",B="data-qx-table-col-name",C="cellOut",D="qx-table-header",E="caseSensitive",F="tap",G="id",H="Column ",I="tbody td",J="string",K="qqx-table-sort-desc",L="<label>{{& value }}</label>",M="input",N="sort",O='[object Number]',P="='",Q="<table>",R="asc",S=" class='",T="</table>",U="modelChange",V="qx-table-sort-desc",W="cellClick",X="none",Y="tr",cf="columnDefault",cg=" ",ch="checked",cb=".qx-table-cell, .qx-table-header",cc="pointerover",cd="qx-table-cell",ce="radio",cm="qx-table-all-columns",cn="cellRender",ct="Object",co="cellHover",ci="tbody tr",cj="cellKey",ck="<td>",cl="collection should contains only table elements !!",cs="modelApplied",cT="desc",cU='[object String]',cu="checkbox",cp="tbody",cq="A Table header element is required for this widget.",cI="qx-table-sort-",cr=".",cv="' name='",cw="<div class='qx-table-cell-wrapper'>",cx="model must be an Array !!",cB="' ",cX='',cC=".qx-table-header-row",cy="filter",cz="qxWeb",cW=">",cA="Array",cH="table",cY="th",da="String",cJ="_compare",cD="",cE="data-qx-table-cell-key",cF="multiple",cG='tbody',cO="qx-table-sort-asc",cP="TD",db="single",cQ="rowSelection",cK="data-qx-table-col-type",cL="qx-table-selection-input",cM="'></label></",cN="' type='",cR="' /><label class='",cS="undefined",cV="pointerout";qx.Bootstrap.define(k,{extend:qx.ui.website.Widget,construct:function(dd,dc){qx.ui.website.Widget.call(this,dd,dc);},events:{"modelChange":cA,"selectionChange":cz,"cellClick":ct,"cellHover":ct,"cellOut":ct,"modelApplied":cA,"cellRender":ct,"sort":ct,"filter":ct},statics:{_config:{caseSensitive:false,rowSelection:X,sortable:false},_templates:{"columnDefault":o+cw+L+f+ck},table:function(df){var de=new qx.ui.website.Table(this);de.__model=df;de.init();return de;},__isNumber:function(n){return (Object.prototype.toString.call(n)===O||Object.prototype.toString.call(n)===cU)&&!isNaN(parseFloat(n))&&isFinite(n.toString().replace(/^-/,cX));},__isDate:function(dg){var d=new Date(dg);return !isNaN(d.valueOf());},__getIndex:function(di,dj){var dh=-1;for(var i=0,l=di.length;i<l;i++ ){if(di.item(i)==dj){dh=i;break;};};return dh;},__getUID:function(){return ((new Date()).getTime()+cD+Math.floor(Math.random()*1000000)).substr(0,18);},__selectionTypes:[db,cF,X],__internalCellClass:cd,__internalHeaderClass:D,__internalSelectionClass:h,__internalInputClass:cL,__allColumnSelector:cm,__dataColName:B,__dataColType:cK,__dataSortingKey:cE,__modelSortingKey:cj,__inputLabelClass:g,__selectedRowClass:t,__ascSortingClass:cO,__descSortingClass:K},members:{__model:null,__columnMeta:null,__sortingFunction:null,__filterFunction:null,__filterFunc:null,__filters:null,__inputName:null,__hovered:null,__sortingData:null,init:function(){if(!qx.ui.website.Widget.prototype.init.call(this)){return false;};var dk=this.__model;if(qxWeb.getNodeName(this).toUpperCase()!==w){throw new Error(cl);};if(!this[0].tHead){throw new Error(cq);};this.find(I).addClass(cd);this.__inputName=M+qx.ui.website.Table.__getUID();this.__getColumnMetaData(dk);this.setModel(dk);this.setSortingFunction(this.__defaultColumnSort);this.__registerEvents();this.__hovered=null;return true;},setModel:function(dl){if(typeof dl!=cS){if(qx.lang.Type.isArray(dl)){this.__model=dl;this.emit(U,dl);}else {throw new Error(cx);};};return this;},setColumnType:function(dm,dn){this.__checkColumnExistance(dm);this.__columnMeta[dm].type=dn;return this;},getColumnType:function(dp){this.eq(0).__checkColumnExistance(dp);return this.eq(0).__columnMeta[dp].type;},getCell:function(dr,dq){return qxWeb(this.eq(0).__getRoot().rows.item(dr).cells.item(dq));},getRows:function(){return qxWeb(this.eq(0).__getRoot().rows);},setCompareFunction:function(ds,dt){ds=qxWeb.string.firstUp(ds);this.setProperty([cJ+ds],dt);return this;},unsetCompareFunction:function(du){du=qxWeb.string.firstUp(du);var dv=this[cJ+du]||this._compareString;this.setProperty([cJ+du],dv);return this;},getCompareFunction:function(dw){dw=qxWeb.string.firstUp(dw);return this.getProperty(cJ+dw)||this[cJ+dw];},setSortingFunction:function(dx){dx=dx||function(){};this.__sortingFunction=dx;return this;},unsetSortingFunction:function(){this.__sortingFunction=this.__defaultColumnSort;return this;},setFilterFunction:function(dy){this.__filterFunction=dy;return this;},unsetFilterFunction:function(){this.__filterFunction=this.__defaultColumnFilter;return this;},setColumnFilter:function(dz,dA){this.__checkColumnExistance(dz);if(!this.__filterFunc){this.__filterFunc={};};this.__filterFunc[dz]=dA;return this;},getColumnFilter:function(dB){if(this.__filterFunc){return this.__filterFunc[dB];};return null;},setRowFilter:function(dC){if(!this.__filterFunc){this.__filterFunc={};};this.__filterFunc.row=dC;return this;},getRowFilter:function(){if(this.__filterFunc){return this.__filterFunc.row;};return null;},sort:function(dD,dE){this.__checkColumnExistance(dD);this.setSortingClass(dD,dE);this.__sortDOM(this.__sort(dD,dE));this.emit(N,{columName:dD,direction:dE});return this;},filter:function(dG,dF){if(dF){this.__checkColumnExistance(dF);if(dG==cD){this.resetFilter(dF);};}else {dF=qx.ui.website.Table.__allColumnSelector;};if(!this.__filters){this.__filters={};};if(this.__filters[dF]){this.__filters[dF].keyword=dG;this.__getRoot().appendChild(this.__filters[dF].rows);}else {this.__filters[dF]={keyword:dG,rows:document.createDocumentFragment()};};this.__filterDom(dG,dF);this.emit(cy,{columName:dF,keyword:dG});return this;},resetFilter:function(dH){var dJ=null;dJ=this.__filters;if(dJ){if(dH){this.__getRoot().appendChild(dJ[dH].rows);}else {for(var dI in dJ){this.__getRoot().appendChild(dJ[dI].rows);};};};return this;},setContent:function(dL){var dK=this.__extractTableRows(dL);var dM=this.find(cG);dM.empty();dK.appendTo(dM);this.render();return this;},appendContent:function(dO){var dN=this.__extractTableRows(dO);var dP=this.find(cG);dN.appendTo(dP);this.render();return this;},__extractTableRows:function(dS){var dR=qxWeb();if(typeof dS==J){var dQ=dS;dS=qxWeb.create(dS);if(qxWeb.getNodeName(dS)!=cH){dS=qxWeb.create(Q+dQ+T);};dR=dS.find(ci);}else if(qxWeb.isNode(dS)||(dS instanceof qxWeb)){dS=qxWeb(dS);var dT=qxWeb.getNodeName(dS);switch(dT){case cH:dR=qxWeb(dS).find(ci);break;case Y:dR=dS;break;case cp:dR=qxWeb(dS).find(Y);break;};};return dR;},__filterDom:function(dW,dY){var dU=this.__getColumnIndex(dY);var dX=dY==qx.ui.website.Table.__allColumnSelector?this.getRowFilter():this.getColumnFilter(dY);dX=dX||this.__defaultColumnFilter;var dV=this.__getDataRows(),ea={};for(var i=0;i<dV.length;i++ ){ea={columnName:dY,columnIndex:dU,cell:dU>-1?qxWeb(dV[i].cells.item(dU)):null,row:qxWeb(dV[i]),keyword:dW};if(!dX.bind(this)(ea)){this.__filters[dY].rows.appendChild(dV[i]);};};return this;},getSortingData:function(){return this.__sortingData;},render:function(){var ec=this.getSortingData();var eb=this.getConfig(cQ);this.__applyTemplate(this.__model);if(qx.ui.website.Table.__selectionTypes.indexOf(eb)!=-1){this.__processSelectionInputs(eb);};if(ec){this.__sortDOM(this.__sort(ec.columnName,ec.direction));};return this;},__processSelectionInputs:function(ed){switch(ed){case X:qxWeb(cr+qx.ui.website.Table.__internalSelectionClass).remove();break;case cF:case db:this.__createInputs(cu);break;case db:this.__createInputs(ce);break;};return this;},__createInputs:function(ef){this.__createInput(this.__getHeaderRow(),ef);var ee=this.find(cp)[0].getElementsByTagName(Y);for(var i=0;i<ee.length;i++ ){this.__createInput(ee.item(i),ef);};return this;},__createInput:function(ep,eq,ej){var eg=this.getCssPrefix();var el=qx.ui.website.Table;var eh=qxWeb(cr+el.__internalHeaderClass+v);var eo=this.getConfig(cQ);var ei=cD;if(eh.length>0){ei=(eo==cF)&&eh[0].checked?ch:cD;};if(typeof ej==cS){ej=qxWeb.getNodeName(qxWeb(ep.cells.item(0)));};var ek=this.__inputName;var em=(ej==cY)?el.__internalSelectionClass+cg+el.__internalHeaderClass:el.__internalSelectionClass;var es=qxWeb(ep).find(cr+el.__internalSelectionClass);if(es.length>0){if(es[0].type!=eq){es[0].type=eq;};}else {var er=qx.ui.website.Table.__getUID();var en=qxWeb.create(r+ej+S+em+s+er+cv+ek+cB+ei+S+eg+c+eq+cg+el.__internalInputClass+cN+eq+cR+el.__inputLabelClass+q+er+cM+ej+cW);if(ep.cells.item(0)){en.insertBefore(qxWeb(ep.cells.item(0)));}else {en.appendTo(qxWeb(ep));};};},__checkColumnExistance:function(et){var eu=this.__columnMeta;if(eu&&!eu[et]){throw new Error(H+et+p);};},__getHeaderRow:function(){var ev=this[0].tHead;if(!ev){throw new Error(cq);};var ew=ev.rows;if(ew.length==1){return ew.item(0);}else {ew=qxWeb(cC);if(ew.length>0){return ew[0];};};return null;},__getColumnMetaData:function(eB){this.__addClassToHeaderAndFooter(this[0].tHead);this.__addClassToHeaderAndFooter(this[0].tFoot);var eC={},ey=null,ez=null,eA=null;var ex=this.__getHeaderRow();ey=ex.cells;for(var i=0,l=ey.length;i<l;i++ ){eA=qxWeb(ey.item(i));ez=this.__getColumName(eA[0])||qx.ui.website.Table.__getUID();if(!eA[0].getAttribute(qx.ui.website.Table.__dataColName)){eA.setAttribute(qx.ui.website.Table.__dataColName,ez);};eC[ez]={type:eA[0].getAttribute(qx.ui.website.Table.__dataColType)||da,name:ez};};this.__columnMeta=eC;return this;},__addClassToHeaderAndFooter:function(eD){if(eD&&eD.rows.length>0){if(eD.rows.item(0).cells.length>0){var eE=this.__getHeaderRow();if(!qxWeb(eE.cells.item(0)).hasClass(qx.ui.website.Table.__internalHeaderClass)){qxWeb(eE.cells).addClass(qx.ui.website.Table.__internalHeaderClass);};};};return this;},__sortDOM:function(eF){for(var i=0,l=eF.length;i<l;i++ ){if(i){qxWeb(eF[i]).insertAfter(eF[i-1]);}else {qxWeb(eF[i]).insertBefore(qxWeb(this.__getRoot().rows.item(0)));};};return this;},__registerEvents:function(){this.on(F,this.__detectClickedCell);this.on(W,function(eG){if(eG.cell&&eG.cell.hasClass(qx.ui.website.Table.__internalHeaderClass)){this.__sortingFunction.bind(this)(eG);};},this);this.on(cc,this.__cellHover,this);this.on(cV,this.__cellOut,this);return this;},__selectionRendered:function(){return qxWeb(cr+qx.ui.website.Table.__internalSelectionClass).length>0;},__processSelection:function(eN){var eM=qx.ui.website.Table;var eH=qxWeb(cr+eM.__internalInputClass);var eL=eN.find(M);var eP=this.getConfig(cQ);var eI=qxWeb(cr+eM.__internalHeaderClass+v);var eJ=[];if(eP==cF){if(eN.hasClass(eM.__internalHeaderClass)){eH.setAttribute(ch,eL[0].checked);};var eK=true;for(var i=0;i<eH.length;i++ ){if((eH[i]!=eI[0])&&(!eH[i].checked)){eK=false;break;};};eI.setAttribute(ch,eK);eH=eH.toArray();if(eK){qxWeb.array.remove(eH,eI[0]);eJ=eH;}else {eJ=eH.filter(function(eQ){return eQ.checked;});};}else {if(eL[0]!=eI[0]){eJ.push(eL[0]);};};var eO=eJ.map(function(eR){return eR.parentNode.parentNode;});eO=qxWeb(eO);qxWeb(cr+eM.__selectedRowClass).removeClass(eM.__selectedRowClass);eO.addClass(eM.__selectedRowClass);this.emit(u,{rows:qxWeb(eO)});return this;},__fireEvent:function(eT,eX,eS){var fb=eX[0].parentNode,eU=fb.cells;var fa=qx.ui.website.Table.__getIndex(eU,eX[0]);var fd=this.__getHeaderRow();var eW=fd.cells.item(fa);var eV=this.__getColumName(eW);var eY=this.getConfig(cQ)!=X?this.__getColumnIndex(eV)-1:this.__getColumnIndex(eV);var fc={cell:qxWeb(eX),row:qxWeb(fb),target:eS,columnIndex:eY,columnName:eV};this.emit(eT,fc);return fc;},__detectClickedCell:function(e){var ff=e.getTarget();var fe=qxWeb(ff);var fg=qx.ui.website.Table;while(!(fe.hasClass(fg.__internalCellClass)||fe.hasClass(fg.__internalHeaderClass)||fe.hasClass(fg.__internalSelectionClass))){if(fe.hasClass(this.classname)){fe=null;break;};fe=fe.getParents().eq(0);};if(fe.hasClass(fg.__internalSelectionClass)){window.setTimeout(function(){this.__processSelection(fe);}.bind(this),5);}else {if(fe&&fe.length>0){this.__fireEvent(W,fe,ff);};};return this;},__cellHover:function(e){var fi=e.getTarget();var fh=qxWeb(fi);var fj=this.__hovered;if(!fh.hasClass(cd)&&!fh.hasClass(D)){fh=fh.getClosest(cb);};if(fh&&(fh.length>0)&&((fj&&(fj.cell[0]!=fh[0]))||(!fj))&&!fh.hasClass(h)){if(fj){this.emit(C,fj);};this.__hovered=this.__fireEvent(co,fh,fi);};},__cellOut:function(e){var fl=e.getRelatedTarget();var fk=qxWeb(fl);if(this.__hovered){if(!fk.isChildOf(this)){this.emit(C,this.__hovered);this.__hovered=null;}else {if(!fk.hasClass(cd)&&!fk.hasClass(D)){fk=fk.getClosest(cb);if(fk.hasClass(h)){this.emit(C,this.__hovered);this.__hovered=null;};};};};},__applyTemplate:function(fv){if(fv&&fv.length>0){var fq,fz;var fw=this.__getHeaderRow();var fu=null,fm=null;var ft=null;var fx=(this.getConfig(cQ)==db)?ce:cu;if(this.__getRoot().rows.length>fv.length){this.__deleteRows(fv.length);};var fn=0,fy=false;var fp=this.getTemplate(cf);var fo=null;for(var i=0,fr=fv.length;i<fr;i++ ){fz=fv[i];if(!this.__isRowRendered(i)){fu=this.__getRoot().insertRow(i);if(this.__selectionRendered()){this.__createInput(fu,fx,z);};};for(var j=0,fs=fz.length;j<fs;j++ ){fn=this.__selectionRendered()?j+1:j;fo=this.__getColumName(fw.cells.item(fn));fm=this.__getDataForColumn(fo);fp=this.getTemplate(fo)||fp;ft=this.__getRoot().rows.item(i);fq=qxWeb.create(qxWeb.template.render(fp,fv[i][j]))[0];if(fq.nodeName.toUpperCase()!=cP){break;};if(!this.__isCellRendered(i,fn)){ft.appendChild(fq);}else {ft.replaceChild(fq,this.getCell(i,fn)[0]);};this.emit(cn,{cell:fq,row:i,col:j,value:fv[i][j]});};if(i==fr-1){fy=true;};};if(fy){this.emit(cs,fv);};};return this;},__deleteRows:function(fB){var fA=this.__getRoot().rows;while(fA.length>fB){this[0].deleteRow(fA.length);};return this;},__getDataForColumn:function(fC){return this.__columnMeta[fC];},__getRoot:function(){return this[0].tBodies.item(0)||this[0];},__isRowRendered:function(fD){if(this.__getRoot().rows.item(fD)){return true;};return false;},__isCellRendered:function(fE,fF){if(!this.__isRowRendered(fE)){return false;};if(this.__getRoot().rows.item(fE).cells.item(fF)){return true;};return false;},setSortingClass:function(fG,fI){var fH={columnName:fG,direction:fI};this.__sortingData=fH;this.__addSortingClassToCol(this[0].tHead,fG,fI);},__addSortingClassToCol:function(fK,fN,fM){var fL=this.__getHeaderRow();if(fK&&fL){qxWeb(fL.cells).removeClasses([cO,V]);var fJ=qxWeb(A+qx.ui.website.Table.__dataColName+P+fN+m+fN);fJ.addClass(cI+fM);};},__sort:function(fQ,fO){var fS=this.__getDataForColumn(fQ);var fP=qxWeb.string.firstUp(fS.type);if(!this[cJ+fP]&&!this.getProperty(cJ+fP)){fP=da;};var fT=this.getCompareFunction(fP).bind(this);var fU=this.__getDataRows();var fR=this.__getColumnIndex(fQ);return fU.sort(function(a,b){var x=this.__getSortingKey(qxWeb(a.cells.item(fR)));var y=this.__getSortingKey(qxWeb(b.cells.item(fR)));return fT(x,y,fO);}.bind(this));},_compareNumber:function(x,y,fV){x=qx.ui.website.Table.__isNumber(x)?Number(x):0;y=qx.ui.website.Table.__isNumber(y)?Number(y):0;if(fV==R){return x-y;}else if(fV==cT){return y-x;};return 0;},__getColumName:function(fW){return fW.getAttribute(qx.ui.website.Table.__dataColName)||fW.getAttribute(G);},_compareDate:function(x,y,fX){x=qx.ui.website.Table.__isDate(x)?new Date(x):new Date(0);y=qx.ui.website.Table.__isDate(y)?new Date(y):new Date(0);if(fX==R){return x-y;}else if(fX==cT){return y-x;};return 0;},_compareString:function(x,y,fY){if(!this.getConfig(E)){x=x.toLowerCase();y=y.toLowerCase();};if(fY==R){return ((x<y)?-1:((x>y)?1:0));}else if(fY==cT){return ((x>y)?-1:((x<y)?1:0));};return 0;},__getSortingKey:function(ga){return ga.getAttribute(qx.ui.website.Table.__dataSortingKey)||this.__getCellValue(ga);},__getCellValue:function(gb){return gb[0].textContent||gb[0].innerText||cD;},__getDataRows:function(){var gc=this.find(cp)[0].rows,gg=[],gf=null,gd=[];for(var i=0,l=gc.length;i<l;i++ ){gd=gc.item(i).cells;if((gd.length>0)&&(gd[0].nodeName.toUpperCase()!=cP)){continue;};for(var j=0,ge=gd.length;j<ge;j++ ){gf=qxWeb(gd[j]);if(!gf.hasClass(qx.ui.website.Table.__internalCellClass)){gf.addClass(qx.ui.website.Table.__internalCellClass);};};gg.push(gc.item(i));};return gg;},__defaultColumnSort:function(gh){var gj=R;var gi=this.getSortingData();if(gi){if(gh.columnName==gi.columnName){if(gi.direction==gj){gj=cT;};};};if(gh.cell.hasClass(D)){this.sort(gh.columnName,gj);};},__defaultColumnFilter:function(gm){var gl=this.getConfig(E);var gk=gm.columnName==qx.ui.website.Table.__allColumnSelector?gm.row:gm.cell;var gn=this.__getCellValue(gk);if(gl){return gn.indexOf(gm.keyword)!=-1;}else {return gn.toLowerCase().indexOf(gm.keyword.toLowerCase())!=-1;};},__getColumnIndex:function(go){var gq=this.__getHeaderRow();var gp=gq.cells;for(var i=0;i<gp.length;i++ ){if(go==this.__getColumName(gp.item(i))){return i;};};return -1;}},defer:function(gr){qxWeb.$attach({table:gr.table});}});})();(function(){var a="qx.module.util.Object",b="object";qx.Bootstrap.define(a,{statics:{clone:qx.lang.Object.clone,getValues:qx.lang.Object.getValues,invert:qx.lang.Object.invert,contains:qx.lang.Object.contains,merge:function(c,d){var d=qxWeb.array.fromArguments(arguments);var c=d.shift();d.forEach(function(e){c=qx.Bootstrap.objectMergeWith(c,e);});return c;}},defer:function(f){qxWeb.$attachAll(this,b);}});})();(function(){var a="vertical",b="preselected",c="<li><button>{{{content}}}</button></li>",d="qx.ui.website.Accordion",e="orientation";qx.Bootstrap.define(d,{extend:qx.ui.website.Tabs,statics:{_templates:{button:c},accordion:function(f){var g=new qx.ui.website.Accordion(this);g.setConfig(e,a);if(f){g.setConfig(b,f);};g.init();return g;}},construct:function(i,h){qx.ui.website.Tabs.call(this,i,h);},defer:function(j){qxWeb.$attach({accordion:j.accordion});}});})();(function(){var a="getScale",b="qx.module.event.Pinch",c="pinch",d="function";qx.Bootstrap.define(b,{statics:{TYPES:[c],BIND_METHODS:[a],getScale:function(){return this._original.scale;},normalize:function(event,f){if(!event){return event;};var e=qx.module.event.Pinch.BIND_METHODS;for(var i=0,l=e.length;i<l;i++ ){if(typeof event[e[i]]!=d){event[e[i]]=qx.module.event.Pinch[e[i]].bind(event);};};return event;}},defer:function(g){qxWeb.$registerEventNormalization(qx.module.event.Pinch.TYPES,g.normalize);}});})();(function(){var a="array",b="qx.module.util.Array";qx.Bootstrap.define(b,{statics:{cast:qx.lang.Array.cast,equals:qx.lang.Array.equals,exclude:qx.lang.Array.exclude,fromArguments:qx.lang.Array.fromArguments,insertAfter:qx.lang.Array.insertAfter,insertBefore:qx.lang.Array.insertBefore,max:qx.lang.Array.max,min:qx.lang.Array.min,remove:qx.lang.Array.remove,removeAll:qx.lang.Array.removeAll,unique:qx.lang.Array.unique,range:qx.lang.Array.range},defer:function(c){qxWeb.$attachAll(this,a);}});})();(function(){var a="qx.module.TextSelection",b="textarea",c="input";qx.Bootstrap.define(a,{statics:{__isInput:function(d){var e=d.tagName?d.tagName.toLowerCase():null;return (e===c||e===b);},__getTextNode:function(f){for(var i=0,l=f.childNodes.length;i<l;i++ ){if(f.childNodes[i].nodeType===3){return f.childNodes[i];};};return null;}},members:{getTextSelection:function(){var g=this[0];if(g){if(!qx.module.TextSelection.__isInput(g)){g=qx.module.TextSelection.__getTextNode(g);};return g?qx.bom.Selection.get(g):null;};return null;},getTextSelectionLength:function(){var h=this[0];if(h){if(!qx.module.TextSelection.__isInput(h)){h=qx.module.TextSelection.__getTextNode(h);};return h?qx.bom.Selection.getLength(h):null;};return null;},getTextSelectionStart:function(){var j=this[0];if(j){if(!qx.module.TextSelection.__isInput(j)){j=qx.module.TextSelection.__getTextNode(j);};return j?qx.bom.Selection.getStart(j):null;};return null;},getTextSelectionEnd:function(){var k=this[0];if(k){if(!qx.module.TextSelection.__isInput(k)){k=qx.module.TextSelection.__getTextNode(k);};return k?qx.bom.Selection.getEnd(k):null;};return null;},setTextSelection:function(m,n){var o=this[0];if(o){if(!qx.module.TextSelection.__isInput(o)){o=qx.module.TextSelection.__getTextNode(o);};if(o){qx.bom.Selection.set(o,m,n);};};return this;},clearTextSelection:function(){this._forEachElement(function(p){if(!qx.module.TextSelection.__isInput(p)){p=qx.module.TextSelection.__getTextNode(p);};if(p){qx.bom.Selection.clear(p);};});return this;}},defer:function(q){qxWeb.$attachAll(this);}});})();(function(){var a="engine.name",b="qx.bom.Selection",c="character",d="button",e='character',f="#text",g="webkit",h="input",i="gecko",j="EndToEnd",k="opera",l="StartToStart",m="html.selection",n="textarea",o="body";qx.Bootstrap.define(b,{statics:{getSelectionObject:qx.core.Environment.select(m,{"selection":function(p){return p.selection;},"default":function(q){return qx.dom.Node.getWindow(q).getSelection();}}),get:qx.core.Environment.select(m,{"selection":function(r){var s=qx.bom.Range.get(qx.dom.Node.getDocument(r));return s.text;},"default":function(t){if(this.__isInputOrTextarea(t)){return t.value.substring(t.selectionStart,t.selectionEnd);}else {return this.getSelectionObject(qx.dom.Node.getDocument(t)).toString();};}}),getLength:qx.core.Environment.select(m,{"selection":function(u){var w=this.get(u);var v=qx.util.StringSplit.split(w,/\r\n/);return w.length-(v.length-1);},"default":function(x){if(qx.core.Environment.get(a)==k){var B,C,A;if(this.__isInputOrTextarea(x)){var z=x.selectionStart;var y=x.selectionEnd;B=x.value.substring(z,y);C=y-z;}else {B=qx.bom.Selection.get(x);C=B.length;};A=qx.util.StringSplit.split(B,/\r\n/);return C-(A.length-1);};if(this.__isInputOrTextarea(x)){return x.selectionEnd-x.selectionStart;}else {return this.get(x).length;};}}),getStart:qx.core.Environment.select(m,{"selection":function(D){if(this.__isInputOrTextarea(D)){var I=qx.bom.Range.get();if(!D.contains(I.parentElement())){return -1;};var J=qx.bom.Range.get(D);var H=D.value.length;J.moveToBookmark(I.getBookmark());J.moveEnd(e,H);return H-J.text.length;}else {var J=qx.bom.Range.get(D);var F=J.parentElement();var K=qx.bom.Range.get();try{K.moveToElementText(F);}catch(M){return 0;};var E=qx.bom.Range.get(qx.dom.Node.getBodyElement(D));E.setEndPoint(l,J);E.setEndPoint(j,K);if(K.compareEndPoints(l,E)==0){return 0;};var G;var L=0;while(true){G=E.moveStart(c,-1);if(K.compareEndPoints(l,E)==0){break;};if(G==0){break;}else {L++ ;};};return  ++L;};},"default":function(N){if(qx.core.Environment.get(a)===i||qx.core.Environment.get(a)===g){if(this.__isInputOrTextarea(N)){return N.selectionStart;}else {var P=qx.dom.Node.getDocument(N);var O=this.getSelectionObject(P);if(O.anchorOffset<O.focusOffset){return O.anchorOffset;}else {return O.focusOffset;};};};if(this.__isInputOrTextarea(N)){return N.selectionStart;}else {return qx.bom.Selection.getSelectionObject(qx.dom.Node.getDocument(N)).anchorOffset;};}}),getEnd:qx.core.Environment.select(m,{"selection":function(Q){if(this.__isInputOrTextarea(Q)){var V=qx.bom.Range.get();if(!Q.contains(V.parentElement())){return -1;};var W=qx.bom.Range.get(Q);var U=Q.value.length;W.moveToBookmark(V.getBookmark());W.moveStart(e,-U);return W.text.length;}else {var W=qx.bom.Range.get(Q);var S=W.parentElement();var X=qx.bom.Range.get();try{X.moveToElementText(S);}catch(ba){return 0;};var U=X.text.length;var R=qx.bom.Range.get(qx.dom.Node.getBodyElement(Q));R.setEndPoint(j,W);R.setEndPoint(l,X);if(X.compareEndPoints(j,R)==0){return U-1;};var T;var Y=0;while(true){T=R.moveEnd(c,1);if(X.compareEndPoints(j,R)==0){break;};if(T==0){break;}else {Y++ ;};};return U-( ++Y);};},"default":function(bb){if(qx.core.Environment.get(a)===i||qx.core.Environment.get(a)===g){if(this.__isInputOrTextarea(bb)){return bb.selectionEnd;}else {var bd=qx.dom.Node.getDocument(bb);var bc=this.getSelectionObject(bd);if(bc.focusOffset>bc.anchorOffset){return bc.focusOffset;}else {return bc.anchorOffset;};};};if(this.__isInputOrTextarea(bb)){return bb.selectionEnd;}else {return qx.bom.Selection.getSelectionObject(qx.dom.Node.getDocument(bb)).focusOffset;};}}),__isInputOrTextarea:function(be){return qx.dom.Node.isElement(be)&&(be.nodeName.toLowerCase()==h||be.nodeName.toLowerCase()==n);},set:qx.core.Environment.select(m,{"selection":function(bf,bi,bh){var bg;if(qx.dom.Node.isDocument(bf)){bf=bf.body;};if(qx.dom.Node.isElement(bf)||qx.dom.Node.isText(bf)){switch(bf.nodeName.toLowerCase()){case h:case n:case d:if(bh===undefined){bh=bf.value.length;};if(bi>=0&&bi<=bf.value.length&&bh>=0&&bh<=bf.value.length){bg=qx.bom.Range.get(bf);bg.collapse(true);bg.moveStart(c,bi);bg.moveEnd(c,bh-bi);bg.select();return true;};break;case f:if(bh===undefined){bh=bf.nodeValue.length;};if(bi>=0&&bi<=bf.nodeValue.length&&bh>=0&&bh<=bf.nodeValue.length){bg=qx.bom.Range.get(qx.dom.Node.getBodyElement(bf));bg.moveToElementText(bf.parentNode);bg.collapse(true);bg.moveStart(c,bi);bg.moveEnd(c,bh-bi);bg.select();return true;};break;default:if(bh===undefined){bh=bf.childNodes.length-1;};if(bf.childNodes[bi]&&bf.childNodes[bh]){bg=qx.bom.Range.get(qx.dom.Node.getBodyElement(bf));bg.moveToElementText(bf.childNodes[bi]);bg.collapse(true);var bj=qx.bom.Range.get(qx.dom.Node.getBodyElement(bf));bj.moveToElementText(bf.childNodes[bh]);bg.setEndPoint(j,bj);bg.select();return true;};};};return false;},"default":function(bk,bp,bm){var bn=bk.nodeName.toLowerCase();if(qx.dom.Node.isElement(bk)&&(bn==h||bn==n)){if(bm===undefined){bm=bk.value.length;};if(bp>=0&&bp<=bk.value.length&&bm>=0&&bm<=bk.value.length){bk.focus();bk.select();bk.setSelectionRange(bp,bm);return true;};}else {var bq=false;var bl=qx.dom.Node.getWindow(bk).getSelection();var bo=qx.bom.Range.get(bk);if(qx.dom.Node.isText(bk)){if(bm===undefined){bm=bk.length;};if(bp>=0&&bp<bk.length&&bm>=0&&bm<=bk.length){bq=true;};}else if(qx.dom.Node.isElement(bk)){if(bm===undefined){bm=bk.childNodes.length-1;};if(bp>=0&&bk.childNodes[bp]&&bm>=0&&bk.childNodes[bm]){bq=true;};}else if(qx.dom.Node.isDocument(bk)){bk=bk.body;if(bm===undefined){bm=bk.childNodes.length-1;};if(bp>=0&&bk.childNodes[bp]&&bm>=0&&bk.childNodes[bm]){bq=true;};};if(bq){if(!bl.isCollapsed){bl.collapseToStart();};bo.setStart(bk,bp);if(qx.dom.Node.isText(bk)){bo.setEnd(bk,bm);}else {bo.setEndAfter(bk.childNodes[bm]);};if(bl.rangeCount>0){bl.removeAllRanges();};bl.addRange(bo);return true;};};return false;}}),clear:qx.core.Environment.select(m,{"selection":function(br){var bt=qx.bom.Range.get(br);var parent=bt.parentElement();var bu=qx.bom.Range.get(qx.dom.Node.getDocument(br));if(qx.dom.Node.isText(br)){br=br.parentNode;};if(parent==bu.parentElement()&&parent==br){var bs=qx.bom.Selection.getSelectionObject(qx.dom.Node.getDocument(br));bs.empty();};},"default":function(bv){var bA=qx.bom.Selection.getSelectionObject(qx.dom.Node.getDocument(bv));var bw=bv.nodeName.toLowerCase();if(qx.dom.Node.isElement(bv)&&(bw==h||bw==n)){bv.setSelectionRange(0,0);if(qx.bom.Element&&qx.bom.Element.blur){qx.bom.Element.blur(bv);};}else if(qx.dom.Node.isDocument(bv)||bw==o){bA.collapse(bv.body?bv.body:bv,0);}else {var bx=qx.bom.Range.get(bv);if(!bx.collapsed){var by;var bz=bx.commonAncestorContainer;if(qx.dom.Node.isElement(bv)&&qx.dom.Node.isText(bz)){by=bz.parentNode;}else {by=bz;};if(by==bv){bA.collapse(bv,0);};};};}})}});})();(function(){var a="qx.bom.Range",b="text",c="password",d="file",e="submit",f="reset",g="textarea",h="input",i="hidden",j="html.selection",k="button",l="body";qx.Bootstrap.define(a,{statics:{get:qx.core.Environment.select(j,{"selection":function(m){if(qx.dom.Node.isElement(m)){switch(m.nodeName.toLowerCase()){case h:switch(m.type){case b:case c:case i:case k:case f:case d:case e:return m.createTextRange();default:return qx.bom.Selection.getSelectionObject(qx.dom.Node.getDocument(m)).createRange();};break;case g:case l:case k:return m.createTextRange();default:return qx.bom.Selection.getSelectionObject(qx.dom.Node.getDocument(m)).createRange();};}else {if(m==null){m=window;};return qx.bom.Selection.getSelectionObject(qx.dom.Node.getDocument(m)).createRange();};},"default":function(n){var o=qx.dom.Node.getDocument(n);var p=qx.bom.Selection.getSelectionObject(o);if(p.rangeCount>0){return p.getRangeAt(0);}else {return o.createRange();};}})}});})();(function(){var a="m",b="g",c="^",d="",e="qx.util.StringSplit",f="i",g="$(?!\\s)",h="[object RegExp]",j="y";qx.Bootstrap.define(e,{statics:{split:function(k,p,o){if(Object.prototype.toString.call(p)!==h){return String.prototype.split.call(k,p,o);};var r=[],l=0,m=(p.ignoreCase?f:d)+(p.multiline?a:d)+(p.sticky?j:d),p=RegExp(p.source,m+b),n,t,q,u,s=/()??/.exec(d)[1]===undefined;k=k+d;if(!s){n=RegExp(c+p.source+g,m);};if(o===undefined||+o<0){o=Infinity;}else {o=Math.floor(+o);if(!o){return [];};};while(t=p.exec(k)){q=t.index+t[0].length;if(q>l){r.push(k.slice(l,t.index));if(!s&&t.length>1){t[0].replace(n,function(){for(var i=1;i<arguments.length-2;i++ ){if(arguments[i]===undefined){t[i]=undefined;};};});};if(t.length>1&&t.index<k.length){Array.prototype.push.apply(r,t.slice(1));};u=t[0].length;l=q;if(r.length>=o){break;};};if(p.lastIndex===t.index){p.lastIndex++ ;};};if(l===k.length){if(u||!p.test(d)){r.push(d);};}else {r.push(k.slice(l));};return r.length>o?r.slice(0,o):r;}}});})();(function(){var a="",b=";path=",c="=",d=";expires=Thu, 01-Jan-1970 00:00:01 GMT",e="qx.bom.Cookie",f=";expires=",g=";",h=";domain=",i=";secure";qx.Bootstrap.define(e,{statics:{get:function(l){var j=document.cookie.indexOf(l+c);var m=j+l.length+1;if((!j)&&(l!=document.cookie.substring(0,l.length))){return null;};if(j==-1){return null;};var k=document.cookie.indexOf(g,m);if(k==-1){k=document.cookie.length;};return unescape(document.cookie.substring(m,k));},set:function(s,q,p,t,n,o){var r=[s,c,escape(q)];if(p){var u=new Date();u.setTime(u.getTime());r.push(f,new Date(u.getTime()+(p*1000*60*60*24)).toGMTString());};if(t){r.push(b,t);};if(n){r.push(h,n);};if(o){r.push(i);};document.cookie=r.join(a);},del:function(y,v,w){if(!qx.bom.Cookie.get(y)){return;};var x=[y,c];if(v){x.push(b,v);};if(w){x.push(h,w);};x.push(d);document.cookie=x.join(a);}}});})();(function(){var a="qx.module.Cookie",b="cookie";qx.Bootstrap.define(a,{statics:{get:qx.bom.Cookie.get,set:qx.bom.Cookie.set,del:qx.bom.Cookie.del},defer:function(c){qxWeb.$attachAll(this,b);}});})();(function(){var a="display",b="",c="block",d="none",e="_getHeight",f="_getContentWidth",g="_getContentHeight",h="hidden",j="_getWidth",k="qx.module.Css",m="absolute";qx.Bootstrap.define(k,{statics:{_getHeight:function(p){var q=this[0];if(q){if(qx.dom.Node.isElement(q)){var n;if(p){var o={display:c,position:m,visibility:h};n=qx.module.Css.__swap(q,o,e,this);}else {n=qx.bom.element.Dimension.getHeight(q);};return n;}else if(qx.dom.Node.isDocument(q)){return qx.bom.Document.getHeight(qx.dom.Node.getWindow(q));}else if(qx.dom.Node.isWindow(q)){return qx.bom.Viewport.getHeight(q);};};return null;},_getWidth:function(t){var u=this[0];if(u){if(qx.dom.Node.isElement(u)){var r;if(t){var s={display:c,position:m,visibility:h};r=qx.module.Css.__swap(u,s,j,this);}else {r=qx.bom.element.Dimension.getWidth(u);};return r;}else if(qx.dom.Node.isDocument(u)){return qx.bom.Document.getWidth(qx.dom.Node.getWindow(u));}else if(qx.dom.Node.isWindow(u)){return qx.bom.Viewport.getWidth(u);};};return null;},_getContentHeight:function(w){var y=this[0];if(qx.dom.Node.isElement(y)){var x;if(w){var v={position:m,visibility:h,display:c};x=qx.module.Css.__swap(y,v,g,this);}else {x=qx.bom.element.Dimension.getContentHeight(y);};return x;};return null;},_getContentWidth:function(B){var z=this[0];if(qx.dom.Node.isElement(z)){var C;if(B){var A={position:m,visibility:h,display:c};C=qx.module.Css.__swap(z,A,f,this);}else {C=qx.bom.element.Dimension.getContentWidth(z);};return C;};return null;},__displayDefaults:{},__getDisplayDefault:function(G,D){var F=qx.module.Css.__displayDefaults;if(!F[G]){var H=D||document;var E=qxWeb(H.createElement(G)).appendTo(D.body);F[G]=E.getStyle(a);E.remove();};return F[G]||b;},__swap:function(L,I,J,O){var M={};for(var N in I){M[N]=L.style[N];L.style[N]=I[N];};var K=O[J]();for(var N in M){L.style[N]=M[N];};return K;},includeStylesheet:function(Q,P){qx.bom.Stylesheet.includeFile(Q,P);}},members:{getHeight:function(R){return this._getHeight(R);},getWidth:function(S){return this._getWidth(S);},getContentHeight:function(T){return this._getContentHeight(T);},getContentWidth:function(U){return this._getContentWidth(U);},show:function(){this._forEachElementWrapped(function(X){var Y=X.getStyle(a);var W=X[0].$$qPrevDisp;var V;if(Y==d){if(W&&W!=d){V=W;}else {var ba=qxWeb.getDocument(X[0]);V=qx.module.Css.__getDisplayDefault(X[0].tagName,ba);};X.setStyle(a,V);X[0].$$qPrevDisp=d;};});return this;},hide:function(){this._forEachElementWrapped(function(bb){var bc=bb.getStyle(a);if(bc!==d){bb[0].$$qPrevDisp=bc;bb.setStyle(a,d);};});return this;},getPosition:function(){var bd=this[0];if(qx.dom.Node.isElement(bd)){return qx.bom.element.Location.getPosition(bd);};return null;},getOffset:function(be){var bf=this[0];if(bf&&qx.dom.Node.isElement(bf)){return qx.bom.element.Location.get(bf,be);};return null;},setStyle:function(name,bg){if(/\w-\w/.test(name)){name=qx.lang.String.camelCase(name);};this._forEachElement(function(bh){qx.bom.element.Style.set(bh,name,bg);});return this;},getStyle:function(name){if(this[0]&&qx.dom.Node.isElement(this[0])){if(/\w-\w/.test(name)){name=qx.lang.String.camelCase(name);};return qx.bom.element.Style.get(this[0],name);};return null;},setStyles:function(bi){for(var name in bi){this.setStyle(name,bi[name]);};return this;},getStyles:function(bk){var bj={};for(var i=0;i<bk.length;i++ ){bj[bk[i]]=this.getStyle(bk[i]);};return bj;},addClass:function(name){this._forEachElement(function(bl){qx.bom.element.Class.add(bl,name);});return this;},addClasses:function(bm){this._forEachElement(function(bn){qx.bom.element.Class.addClasses(bn,bm);});return this;},removeClass:function(name){this._forEachElement(function(bo){qx.bom.element.Class.remove(bo,name);});return this;},removeClasses:function(bp){this._forEachElement(function(bq){qx.bom.element.Class.removeClasses(bq,bp);});return this;},hasClass:function(name){if(!this[0]||!qx.dom.Node.isElement(this[0])){return false;};return qx.bom.element.Class.has(this[0],name);},getClass:function(){if(!this[0]||!qx.dom.Node.isElement(this[0])){return b;};return qx.bom.element.Class.get(this[0]);},toggleClass:function(name){var br=qx.bom.element.Class;this._forEachElement(function(bs){br.has(bs,name)?br.remove(bs,name):br.add(bs,name);});return this;},toggleClasses:function(bt){for(var i=0,l=bt.length;i<l;i++ ){this.toggleClass(bt[i]);};return this;},replaceClass:function(bv,bu){this._forEachElement(function(bw){qx.bom.element.Class.replace(bw,bv,bu);});return this;}},defer:function(bx){qxWeb.$attachAll(this);qxWeb.$attach({"_getWidth":bx._getWidth,"_getHeight":bx._getHeight,"_getContentHeight":bx._getContentHeight,"_getContentWidth":bx._getContentWidth});}});})();(function(){var a="mshtml",b="engine.name",c="qx.bom.element.Dimension",d="0px",e="paddingRight",f="paddingLeft",g="opera",h="paddingBottom",i="paddingTop",j="overflowX",k="overflowY";qx.Bootstrap.define(c,{statics:{getWidth:function(m){var l=m.getBoundingClientRect();return Math.round(l.right-l.left);},getHeight:function(o){var n=o.getBoundingClientRect();return Math.round(n.bottom-n.top);},__hiddenScrollbars:{visible:true,hidden:true},getContentWidth:function(s){var p=qx.bom.element.Style;var q=qx.bom.element.Style.get(s,j);var r=parseInt(p.get(s,f)||d,10);var v=parseInt(p.get(s,e)||d,10);if(this.__hiddenScrollbars[q]){var u=s.clientWidth;if((qx.core.Environment.get(b)==g)||qx.dom.Node.isBlockNode(s)){u=u-r-v;};if(qx.core.Environment.get(b)==a){if(u===0&&s.offsetHeight===0){return s.offsetWidth;};};return u;}else {if(s.clientWidth>=s.scrollWidth){return Math.max(s.clientWidth,s.scrollWidth)-r-v;}else {var t=s.scrollWidth-r;if(qx.core.Environment.get(b)==a){t-=v;};return t;};};},getContentHeight:function(A){var w=qx.bom.element.Style;var z=qx.bom.element.Style.get(A,k);var y=parseInt(w.get(A,i)||d,10);var x=parseInt(w.get(A,h)||d,10);if(this.__hiddenScrollbars[z]){return A.clientHeight-y-x;}else {if(A.clientHeight>=A.scrollHeight){return Math.max(A.clientHeight,A.scrollHeight)-y-x;}else {return A.scrollHeight-y;};};}}});})();(function(){var a="border-box",b="qx.bom.element.BoxSizing",c="css.boxsizing",d="",e="boxSizing",f="content-box",g=":",h=";";qx.Bootstrap.define(b,{statics:{__nativeBorderBox:{tags:{button:true,select:true},types:{search:true,button:true,submit:true,reset:true,checkbox:true,radio:true}},__usesNativeBorderBox:function(j){var i=this.__nativeBorderBox;return i.tags[j.tagName.toLowerCase()]||i.types[j.type];},compile:function(k){if(qx.core.Environment.get(c)){var l=qx.bom.Style.getCssName(qx.core.Environment.get(c));return l+g+k+h;}else {{};};},get:function(m){if(qx.core.Environment.get(c)){return qx.bom.element.Style.get(m,e,null,false)||d;};if(qx.bom.Document.isStandardMode(qx.dom.Node.getWindow(m))){if(!this.__usesNativeBorderBox(m)){return f;};};return a;},set:function(o,n){if(qx.core.Environment.get(c)){try{o.style[qx.core.Environment.get(c)]=n;}catch(p){{};};}else {{};};},reset:function(q){this.set(q,d);}}});})();(function(){var a="cursor:",b="engine.name",c="",d="mshtml",e="nw-resize",f="engine.version",g="nesw-resize",h="browser.documentmode",i=";",j="nwse-resize",k="qx.bom.element.Cursor",l="ne-resize",m="browser.quirksmode",n="cursor";qx.Bootstrap.define(k,{statics:{__map:{},compile:function(o){return a+(this.__map[o]||o)+i;},get:function(q,p){return qx.bom.element.Style.get(q,n,p,false);},set:function(s,r){s.style.cursor=this.__map[r]||r;},reset:function(t){t.style.cursor=c;}},defer:function(u){if(qx.core.Environment.get(b)==d&&((parseFloat(qx.core.Environment.get(f))<9||qx.core.Environment.get(h)<9)&&!qx.core.Environment.get(m))){u.__map[g]=l;u.__map[j]=e;};}});})();(function(){var a="engine.name",b=");",c="",d=")",e="zoom:1;filter:alpha(opacity=",f="qx.bom.element.Opacity",g="css.opacity",h=";",i="opacity:",j="alpha(opacity=",k="opacity",l="filter";qx.Bootstrap.define(f,{statics:{compile:qx.core.Environment.select(a,{"mshtml":function(m){if(m>=1){m=1;};if(m<0.00001){m=0;};if(qx.core.Environment.get(g)){return i+m+h;}else {return e+(m*100)+b;};},"default":function(n){return i+n+h;}}),set:qx.core.Environment.select(a,{"mshtml":function(q,o){if(qx.core.Environment.get(g)){q.style.opacity=o;}else {var p=qx.bom.element.Style.get(q,l,qx.bom.element.Style.COMPUTED_MODE,false);if(o>=1){o=1;};if(o<0.00001){o=0;};if(!q.currentStyle||!q.currentStyle.hasLayout){q.style.zoom=1;};q.style.filter=p.replace(/alpha\([^\)]*\)/gi,c)+j+o*100+d;};},"default":function(s,r){s.style.opacity=r;}}),reset:qx.core.Environment.select(a,{"mshtml":function(u){if(qx.core.Environment.get(g)){u.style.opacity=c;}else {var t=qx.bom.element.Style.get(u,l,qx.bom.element.Style.COMPUTED_MODE,false);u.style.filter=t.replace(/alpha\([^\)]*\)/gi,c);};},"default":function(v){v.style.opacity=c;}}),get:qx.core.Environment.select(a,{"mshtml":function(z,y){if(qx.core.Environment.get(g)){var w=qx.bom.element.Style.get(z,k,y,false);if(w!=null){return parseFloat(w);};return 1.0;}else {var x=qx.bom.element.Style.get(z,l,y,false);if(x){var w=x.match(/alpha\(opacity=(.*)\)/);if(w&&w[1]){return parseFloat(w[1])/100;};};return 1.0;};},"default":function(C,B){var A=qx.bom.element.Style.get(C,k,B,false);if(A!=null){return parseFloat(A);};return 1.0;}})}});})();(function(){var a="clip:auto;",b="rect(",c=")",d=");",e="",f="px",g="Could not parse clip string: ",h="qx.bom.element.Clip",i="string",j="clip:rect(",k=" ",l="clip",m="rect(auto,auto,auto,auto)",n="rect(auto, auto, auto, auto)",o="auto",p=",";qx.Bootstrap.define(h,{statics:{compile:function(q){if(!q){return a;};var v=q.left;var top=q.top;var u=q.width;var t=q.height;var r,s;if(v==null){r=(u==null?o:u+f);v=o;}else {r=(u==null?o:v+u+f);v=v+f;};if(top==null){s=(t==null?o:t+f);top=o;}else {s=(t==null?o:top+t+f);top=top+f;};return j+top+p+r+p+s+p+v+d;},get:function(z,D){var x=qx.bom.element.Style.get(z,l,D,false);var C,top,A,E;var w,y;if(typeof x===i&&x!==o&&x!==e){x=x.trim();if(/\((.*)\)/.test(x)){var F=RegExp.$1;if(/,/.test(F)){var B=F.split(p);}else {var B=F.split(k);};top=B[0].trim();w=B[1].trim();y=B[2].trim();C=B[3].trim();if(C===o){C=null;};if(top===o){top=null;};if(w===o){w=null;};if(y===o){y=null;};if(top!=null){top=parseInt(top,10);};if(w!=null){w=parseInt(w,10);};if(y!=null){y=parseInt(y,10);};if(C!=null){C=parseInt(C,10);};if(w!=null&&C!=null){A=w-C;}else if(w!=null){A=w;};if(y!=null&&top!=null){E=y-top;}else if(y!=null){E=y;};}else {throw new Error(g+x);};};return {left:C||null,top:top||null,width:A||null,height:E||null};},set:function(L,G){if(!G){L.style.clip=m;return;};var M=G.left;var top=G.top;var K=G.width;var J=G.height;var H,I;if(M==null){H=(K==null?o:K+f);M=o;}else {H=(K==null?o:M+K+f);M=M+f;};if(top==null){I=(J==null?o:J+f);top=o;}else {I=(J==null?o:top+J+f);top=top+f;};L.style.clip=b+top+p+H+p+I+p+M+c;},reset:function(N){N.style.clip=n;}}});})();(function(){var a="css.float",b="foo",c="css.borderimage.standardsyntax",d="detect",e="borderRadius",f="boxSizing",g="stretch",h="css.borderradius",j="content",k="css.inlineblock",l="css.gradient.filter",m="css.appearance",n="css.opacity",o="div",p="pointerEvents",q="css.gradient.radial",r="css.pointerevents",s="input",t="color",u="string",v="borderImage",w="userSelect",x="styleFloat",y="css.textShadow.filter",z="css.usermodify",A="flexbox",B='url("foo.png") 4 4 4 4 fill stretch',C="css.boxmodel",D="qx.bom.client.Css",E="css.boxshadow",F="appearance",G="-ms-flexbox",H="placeholder",I="-moz-none",J="backgroundImage",K="css.textShadow",L="DXImageTransform.Microsoft.Shadow",M="flex",N="css.alphaimageloaderneeded",O="css.gradient.legacywebkit",P="css.flexboxSyntax",Q="linear-gradient(0deg, #fff, #000)",R="textShadow",S="auto",T="css.borderimage",U="foo.png",V="rgba(1, 2, 3, 0.5)",W="color=#666666,direction=45",X="radial-gradient(0px 0px, cover, red 50%, blue 100%)",Y="rgba",bG="(",bH="-webkit-flex",bI='url("foo.png") 4 4 4 4 stretch',bC="css.gradient.linear",bD="DXImageTransform.Microsoft.Gradient",bE="css.userselect",bF="span",bM="css.boxsizing",bN="-webkit-gradient(linear,0% 0%,100% 100%,from(white), to(red))",bO="mshtml",ca="css.rgba",bJ=");",bK="4 fill",bL="none",bA="startColorStr=#550000FF, endColorStr=#55FFFF00",bR="progid:",bB="css.placeholder",bS="css.userselect.none",bT="css.textoverflow",bX="inline-block",bP="-moz-inline-box",bY="textOverflow",bQ="userModify",bU="boxShadow",bV="cssFloat",bW="border";qx.Bootstrap.define(D,{statics:{__WEBKIT_LEGACY_GRADIENT:null,getBoxModel:function(){var content=qx.bom.client.Engine.getName()!==bO||!qx.bom.client.Browser.getQuirksMode();return content?j:bW;},getTextOverflow:function(){return qx.bom.Style.getPropertyName(bY);},getPlaceholder:function(){var i=document.createElement(s);return H in i;},getAppearance:function(){return qx.bom.Style.getPropertyName(F);},getBorderRadius:function(){return qx.bom.Style.getPropertyName(e);},getBoxShadow:function(){return qx.bom.Style.getPropertyName(bU);},getBorderImage:function(){return qx.bom.Style.getPropertyName(v);},getBorderImageSyntax:function(){var cc=qx.bom.client.Css.getBorderImage();if(!cc){return null;};var cb=document.createElement(o);if(cc===v){cb.style[cc]=B;if(cb.style.borderImageSource.indexOf(U)>=0&&cb.style.borderImageSlice.indexOf(bK)>=0&&cb.style.borderImageRepeat.indexOf(g)>=0){return true;};}else {cb.style[cc]=bI;if(cb.style[cc].indexOf(U)>=0){return false;};};return null;},getUserSelect:function(){return qx.bom.Style.getPropertyName(w);},getUserSelectNone:function(){var ce=qx.bom.client.Css.getUserSelect();if(ce){var cd=document.createElement(bF);cd.style[ce]=I;return cd.style[ce]===I?I:bL;};return null;},getUserModify:function(){return qx.bom.Style.getPropertyName(bQ);},getFloat:function(){var cf=document.documentElement.style;return cf.cssFloat!==undefined?bV:cf.styleFloat!==undefined?x:null;},getLinearGradient:function(){qx.bom.client.Css.__WEBKIT_LEGACY_GRADIENT=false;var cj=Q;var cg=document.createElement(o);var ch=qx.bom.Style.getAppliedStyle(cg,J,cj);if(!ch){cj=bN;var ch=qx.bom.Style.getAppliedStyle(cg,J,cj,false);if(ch){qx.bom.client.Css.__WEBKIT_LEGACY_GRADIENT=true;};};if(!ch){return null;};var ci=/(.*?)\(/.exec(ch);return ci?ci[1]:null;},getFilterGradient:function(){return qx.bom.client.Css.__isFilterSupported(bD,bA);},getRadialGradient:function(){var cn=X;var ck=document.createElement(o);var cl=qx.bom.Style.getAppliedStyle(ck,J,cn);if(!cl){return null;};var cm=/(.*?)\(/.exec(cl);return cm?cm[1]:null;},getLegacyWebkitGradient:function(){if(qx.bom.client.Css.__WEBKIT_LEGACY_GRADIENT===null){qx.bom.client.Css.getLinearGradient();};return qx.bom.client.Css.__WEBKIT_LEGACY_GRADIENT;},getRgba:function(){var co;try{co=document.createElement(o);}catch(cp){co=document.createElement();};try{co.style[t]=V;if(co.style[t].indexOf(Y)!=-1){return true;};}catch(cq){};return false;},getBoxSizing:function(){return qx.bom.Style.getPropertyName(f);},getInlineBlock:function(){var cr=document.createElement(bF);cr.style.display=bX;if(cr.style.display==bX){return bX;};cr.style.display=bP;if(cr.style.display!==bP){return bP;};return null;},getOpacity:function(){return (typeof document.documentElement.style.opacity==u);},getTextShadow:function(){return !!qx.bom.Style.getPropertyName(R);},getFilterTextShadow:function(){return qx.bom.client.Css.__isFilterSupported(L,W);},__isFilterSupported:function(cv,ct){var cu=false;var cw=bR+cv+bG+ct+bJ;var cs=document.createElement(o);document.body.appendChild(cs);cs.style.filter=cw;if(cs.filters&&cs.filters.length>0&&cs.filters.item(cv).enabled==true){cu=true;};document.body.removeChild(cs);return cu;},getAlphaImageLoaderNeeded:function(){return qx.bom.client.Engine.getName()==bO&&qx.bom.client.Browser.getDocumentMode()<9;},getPointerEvents:function(){var cx=document.documentElement;if(p in cx.style){var cz=cx.style.pointerEvents;cx.style.pointerEvents=S;cx.style.pointerEvents=b;var cy=cx.style.pointerEvents==S;cx.style.pointerEvents=cz;return cy;};return false;},getFlexboxSyntax:function(){var cB=null;var cA=document.createElement(d);var cC=[{value:M,syntax:M},{value:G,syntax:A},{value:bH,syntax:M}];for(var i=0;i<cC.length;i++ ){try{cA.style.display=cC[i].value;}catch(cD){return null;};if(cA.style.display===cC[i].value){cB=cC[i].syntax;break;};};cA=null;return cB;}},defer:function(cE){qx.core.Environment.add(bT,cE.getTextOverflow);qx.core.Environment.add(bB,cE.getPlaceholder);qx.core.Environment.add(h,cE.getBorderRadius);qx.core.Environment.add(E,cE.getBoxShadow);qx.core.Environment.add(bC,cE.getLinearGradient);qx.core.Environment.add(l,cE.getFilterGradient);qx.core.Environment.add(q,cE.getRadialGradient);qx.core.Environment.add(O,cE.getLegacyWebkitGradient);qx.core.Environment.add(C,cE.getBoxModel);qx.core.Environment.add(ca,cE.getRgba);qx.core.Environment.add(T,cE.getBorderImage);qx.core.Environment.add(c,cE.getBorderImageSyntax);qx.core.Environment.add(z,cE.getUserModify);qx.core.Environment.add(bE,cE.getUserSelect);qx.core.Environment.add(bS,cE.getUserSelectNone);qx.core.Environment.add(m,cE.getAppearance);qx.core.Environment.add(a,cE.getFloat);qx.core.Environment.add(bM,cE.getBoxSizing);qx.core.Environment.add(k,cE.getInlineBlock);qx.core.Environment.add(n,cE.getOpacity);qx.core.Environment.add(K,cE.getTextShadow);qx.core.Environment.add(y,cE.getFilterTextShadow);qx.core.Environment.add(N,cE.getAlphaImageLoaderNeeded);qx.core.Environment.add(r,cE.getPointerEvents);qx.core.Environment.add(P,cE.getFlexboxSyntax);}});})();(function(){var a="css.float",b="qx.bom.element.Style",c="css.borderimage",d="css.userselect",e="css.boxsizing",f="pixelLeft",g='cssFloat',h="css.textoverflow",i="Cascaded styles are not supported in this browser!",j="pixelBottom",k="pixelHeight",l="pixelRight",m="pixelWidth",n="css.appearance",o='float',p="css.usermodify",q="",r="pixelTop",s="px";qx.Bootstrap.define(b,{statics:{__styleNames:null,__cssNames:null,__detectVendorProperties:function(){var u={"appearance":qx.core.Environment.get(n),"userSelect":qx.core.Environment.get(d),"textOverflow":qx.core.Environment.get(h),"borderImage":qx.core.Environment.get(c),"float":qx.core.Environment.get(a),"userModify":qx.core.Environment.get(p),"boxSizing":qx.core.Environment.get(e)};this.__cssNames={};for(var t in qx.lang.Object.clone(u)){if(!u[t]){delete u[t];}else {if(t===o){this.__cssNames[g]=t;}else {this.__cssNames[t]=qx.bom.Style.getCssName(u[t]);};};};this.__styleNames=u;},__getStyleName:function(name){var v=qx.bom.Style.getPropertyName(name);if(v){this.__styleNames[name]=v;};return v;},__mshtmlPixel:{width:m,height:k,left:f,right:l,top:r,bottom:j},__special:{clip:qx.bom.element.Clip,cursor:qx.bom.element.Cursor,opacity:qx.bom.element.Opacity,boxSizing:qx.bom.element.BoxSizing},COMPUTED_MODE:1,CASCADED_MODE:2,LOCAL_MODE:3,set:function(y,name,w,x){{};name=this.__styleNames[name]||this.__getStyleName(name)||name;if(x!==false&&this.__special[name]){this.__special[name].set(y,w);}else {y.style[name]=w!==null?w:q;};},get:function(C,name,E,G){name=this.__styleNames[name]||this.__getStyleName(name)||name;if(G!==false&&this.__special[name]){return this.__special[name].get(C,E);};switch(E){case this.LOCAL_MODE:return C.style[name]||q;case this.CASCADED_MODE:if(C.currentStyle){return C.currentStyle[name]||q;};throw new Error(i);default:var A=qx.dom.Node.getDocument(C);var D=A.defaultView?A.defaultView.getComputedStyle:undefined;if(D!==undefined){var z=D(C,null);if(z&&z[name]){return z[name];};}else {if(!C.currentStyle){return C.style[name]||q;};var I=C.currentStyle[name]||C.style[name]||q;if(/^-?[\.\d]+(px)?$/i.test(I)){return I;};var H=this.__mshtmlPixel[name];if(H&&(H in C.style)){var F=C.style[name];C.style[name]=I||0;var B=C.style[H]+s;C.style[name]=F;return B;};return I;};return C.style[name]||q;};}},defer:function(J){J.__detectVendorProperties();}});})();(function(){var a="borderBottomWidth",b="scroll",c="qx.bom.element.Location",d="gecko",e="paddingLeft",f="borderRightWidth",g="auto",h="static",i="borderTopWidth",j="borderLeftWidth",k="marginBottom",l="marginTop",m="overflowY",n="marginLeft",o="border-box",p="padding",q="paddingBottom",r="paddingTop",s="marginRight",t="browser.quirksmode",u="engine.name",v="position",w="margin",x="paddingRight",y="BODY",z="overflowX",A="border";qx.Bootstrap.define(c,{statics:{__num:function(C,B){return parseInt(qx.bom.element.Style.get(C,B,qx.bom.element.Style.COMPUTED_MODE,false),10)||0;},__computeScroll:function(E){var F=0,top=0;var D=qx.dom.Node.getWindow(E);F-=qx.bom.Viewport.getScrollLeft(D);top-=qx.bom.Viewport.getScrollTop(D);return {left:F,top:top};},__computeBody:qx.core.Environment.select(u,{"mshtml":function(I){var H=qx.dom.Node.getDocument(I);var G=H.body;var J=0;var top=0;J-=G.clientLeft+H.documentElement.clientLeft;top-=G.clientTop+H.documentElement.clientTop;if(!qx.core.Environment.get(t)){J+=this.__num(G,j);top+=this.__num(G,i);};return {left:J,top:top};},"webkit":function(M){var L=qx.dom.Node.getDocument(M);var K=L.body;var N=K.offsetLeft;var top=K.offsetTop;return {left:N,top:top};},"gecko":function(P){var O=qx.dom.Node.getDocument(P).body;var Q=O.offsetLeft;var top=O.offsetTop;if(qx.bom.element.BoxSizing.get(O)!==o){Q+=this.__num(O,j);top+=this.__num(O,i);};return {left:Q,top:top};},"default":function(S){var R=qx.dom.Node.getDocument(S).body;var T=R.offsetLeft;var top=R.offsetTop;return {left:T,top:top};}}),__computeOffset:function(U){var V=U.getBoundingClientRect();return {left:Math.round(V.left),top:Math.round(V.top)};},get:function(bd,be){if(bd.tagName==y){var location=this.__getBodyLocation(bd);var bh=location.left;var top=location.top;}else {var W=this.__computeBody(bd);var bb=this.__computeOffset(bd);var scroll=this.__computeScroll(bd);var bh=bb.left+W.left-scroll.left;var top=bb.top+W.top-scroll.top;};var X;var bc;if(bd instanceof SVGElement){var bi=bd.getBoundingClientRect();X=bi.width;bc=bi.height;}else {X=bd.offsetWidth;bc=bd.offsetHeight;};var Y=bh+X;var ba=top+bc;if(be){if(be==p||be==b){var bg=qx.bom.element.Style.get(bd,z);if(bg==b||bg==g){Y+=bd.scrollWidth-X+this.__num(bd,j)+this.__num(bd,f);};var bf=qx.bom.element.Style.get(bd,m);if(bf==b||bf==g){ba+=bd.scrollHeight-bc+this.__num(bd,i)+this.__num(bd,a);};};switch(be){case p:bh+=this.__num(bd,e);top+=this.__num(bd,r);Y-=this.__num(bd,x);ba-=this.__num(bd,q);case b:bh-=bd.scrollLeft;top-=bd.scrollTop;Y-=bd.scrollLeft;ba-=bd.scrollTop;case A:bh+=this.__num(bd,j);top+=this.__num(bd,i);Y-=this.__num(bd,f);ba-=this.__num(bd,a);break;case w:bh-=this.__num(bd,n);top-=this.__num(bd,l);Y+=this.__num(bd,s);ba+=this.__num(bd,k);break;};};return {left:bh,top:top,right:Y,bottom:ba};},__getBodyLocation:function(bj){var top=bj.offsetTop;var bk=bj.offsetLeft;top+=this.__num(bj,l);bk+=this.__num(bj,n);if(qx.core.Environment.get(u)===d){top+=this.__num(bj,j);bk+=this.__num(bj,i);};return {left:bk,top:top};},getRelative:function(bo,bn,bm,bl){var bq=this.get(bo,bm);var bp=this.get(bn,bl);return {left:bq.left-bp.left,top:bq.top-bp.top,right:bq.right-bp.right,bottom:bq.bottom-bp.bottom};},getPosition:function(br){return this.getRelative(br,this.getOffsetParent(br));},getOffsetParent:function(bu){if(bu instanceof SVGElement){return document.body;};var bt=bu.offsetParent||document.body;var bs=qx.bom.element.Style;while(bt&&(!/^body|html$/i.test(bt.tagName)&&bs.get(bt,v)===h)){bt=bt.offsetParent;};return bt;}}});})();(function(){var a='',b="g",c="(^|\\s)",d='function',e="(\\s|$)",f="",g="\\b|\\b",h="qx.bom.element.Class",j='SVGAnimatedString',k="html.classlist",m="default",n=" ",o='object',p="$2",q="native",r="\\b",s='undefined';qx.Bootstrap.define(h,{statics:{__splitter:/\s+/g,__trim:/^\s+|\s+$/g,add:{"native":function(t,name){if(name.length>0){t.classList.add(name);};return name;},"default":function(u,name){if(!this.has(u,name)){u.className+=(u.className?n:f)+name;};return name;}}[qx.core.Environment.get(k)?q:m],addClasses:{"native":function(w,v){for(var i=0;i<v.length;i++ ){if(v[i].length>0){w.classList.add(v[i]);};};return w.className;},"default":function(y,A){var z={};var B;var x=y.className;if(x){B=x.split(this.__splitter);for(var i=0,l=B.length;i<l;i++ ){z[B[i]]=true;};for(var i=0,l=A.length;i<l;i++ ){if(!z[A[i]]){B.push(A[i]);};};}else {B=A;};return y.className=B.join(n);}}[qx.core.Environment.get(k)?q:m],get:function(D){var C=D.className;if(typeof C.split!==d){if(typeof C===o){if(qx.Bootstrap.getClass(C)==j){C=C.baseVal;}else {{};C=a;};};if(typeof C===s){{};C=a;};};return C;},has:{"native":function(E,name){return E.classList.contains(name);},"default":function(G,name){var F=new RegExp(c+name+e);return F.test(G.className);}}[qx.core.Environment.get(k)?q:m],remove:{"native":function(H,name){H.classList.remove(name);return name;},"default":function(J,name){var I=new RegExp(c+name+e);J.className=J.className.replace(I,p);return name;}}[qx.core.Environment.get(k)?q:m],removeClasses:{"native":function(L,K){for(var i=0;i<K.length;i++ ){L.classList.remove(K[i]);};return L.className;},"default":function(O,M){var N=new RegExp(r+M.join(g)+r,b);return O.className=O.className.replace(N,f).replace(this.__trim,f).replace(this.__splitter,n);}}[qx.core.Environment.get(k)?q:m],replace:function(R,Q,P){if(!this.has(R,Q)){return f;};this.remove(R,Q);return this.add(R,P);}}});})();(function(){var a="qx.module.dev.FakeServer";qx.Bootstrap.define(a,{statics:{configure:function(b){qx.dev.FakeServer.getInstance().configure(b);},removeResponse:function(d,c){qx.dev.FakeServer.getInstance().removeResponse(d,c);},addFilter:function(e){qx.dev.FakeServer.getInstance().addFilter(e);},removeFilter:function(f){qx.dev.FakeServer.getInstance().removeFilter(f);},respondWith:function(i,h,g){qx.dev.FakeServer.getInstance().respondWith(i,h,g);},getFakeServer:function(){return qx.dev.FakeServer.getInstance().getFakeServer();},restore:function(){qx.dev.FakeServer.getInstance().restore();}},defer:function(j){qxWeb.$attachStatic({"dev":{"fakeServer":{"configure":j.configure,"removeResponse":j.removeResponse,"addFilter":j.addFilter,"removeFilter":j.removeFilter,"respondWith":j.respondWith,"getFakeServer":j.getFakeServer,"restore":j.restore}}});}});})();(function(){var a=" is a singleton! It is not possible to instantiate it directly.",b="qx.dev.FakeServer",c=".*?",d="Use the static getInstance() method instead.";qx.Bootstrap.define(b,{extend:Object,construct:function(){var f=qx.dev.FakeServer;if(!f.$$allowconstruct){var e=f+a+d;throw new Error(e);};this.getFakeServer();this.__responses=[];},statics:{$$instance:null,$$allowconstruct:false,getInstance:function(){if(!this.$$instance){this.$$allowconstruct=true;this.$$instance=new this();delete this.$$allowconstruct;};return this.$$instance;}},members:{__sinon:null,__fakeServer:null,__responses:null,__filter:null,configure:function(h){h.forEach(function(n){var o=n.url instanceof RegExp?n.url:this._getRegExp(n.url);var m=[n.method,o];var k=false;for(var i=0,l=this.__responses.length;i<l;i++ ){var j=this.__responses[i];k=(j[0]==m[0]&&j[1]==m[1]);};if(!k){this.__responses.push(m);};this.respondWith(n.method,o,n.response);}.bind(this));var g=this.__filter=this.__getCombinedFilter();this.addFilter(g);},addFilter:function(p){{};this.__sinon.FakeXMLHttpRequest.addFilter(p);},removeFilter:function(q){qx.lang.Array.remove(this.__sinon.FakeXMLHttpRequest.filters,q);},removeResponse:function(t,r){qx.lang.Array.remove(this.__sinon.FakeXMLHttpRequest.filters,this.__filter);var s=r instanceof RegExp?r:this._getRegExp(r);this.__responses=this.__responses.filter(function(u){return (u[0]!=t||u[1].toString()!=s.toString());});this.__fakeServer.responses=this.__fakeServer.responses.filter(function(v){return (v.method!=t||v.url.toString()!=s.toString());});this.removeFilter(this.__filter);this.__filter=this.__getCombinedFilter();this.addFilter(this.__filter);},respondWith:function(y,x,w){this.getFakeServer().respondWith(y,x,w);},getFakeServer:function(){if(!this.__fakeServer){var z=this.__sinon=qx.dev.unit.Sinon.getSinon();z.FakeXMLHttpRequest.useFilters=true;this.__fakeServer=z.sandbox.useFakeServer();this.__fakeServer.autoRespond=true;};return this.__fakeServer;},restore:function(){this.__responses=[];this.removeFilter(this.__filter);this.__filter=null;this.__fakeServer.restore();this.__fakeServer=null;},_getRegExp:function(A){A=A.replace(/\{[^\/]*?\}/g,c);return new RegExp(A);},__getCombinedFilter:function(){var B=this.__responses;return function(I,D,F,C,H){for(var i=0,l=B.length;i<l;i++ ){var E=B[i][0];var G=B[i][1];if(I==E&&G.test(D)){return false;};};return true;};}},destruct:function(){this.restore();this.__fakeServer=this.__sinon=null;}});})();(function(){var c="html:",d="./sinon/mock",g="expected %n to always be called with match %*%C",n="truthy",q='withCredentials',r="loadstart",s="at most ",t="\r\n",u="%",v="clearInterval",w="Not Acceptable",x='create',y="Expectation met: ",z="stub#",A="INVALID_STATE_ERR - ",B="./sinon/sandbox",C="match",D="notCalled",E="stubbed",G="inherit",H='\n--------------\n',I="Requested Range Not Satisfiable",J="Date",K="expectedArguments",L='Async',M=" property ",N="expected %n to always be called with %1 as this but was called with %t",O="falsy",P="clock",Q="calledOnce",R="', but no object with such a property was passed.",S="Payment Required",T='$2',U="calledWithMatch",V="expected %n to be called thrice but was called %c%C",W="[object Date]",X="{\n  ",Y=" cannot yield since it was not yet invoked.",ba=":",bb="(",bc="sinon.testCase needs an object with test functions",bd="called",be="readystatechange",bf="Callback must be provided to timer calls",bg="loadend",bh="[object Window]",bi="object is null",bj="overrideMimeType",bk="hasOwn",bl="calledWith",bm="Matcher was not a string, a number, a ",bn=",\n  ",bo="error",bp="=\"",bq="Precondition Failed",br="progress",bs="./util/fake_timers",bt="Use Proxy",bu="any",bv="tick only understands numbers and 'h:m:s'",bw="_",bx="Forbidden",by="contenteditable",bz="./sinon/test",bA="Request Timeout",bB="Found",bC="expected %n to never be called with match %*%C",bD="expected %n to always be called with exact arguments %*%C",bE="Fake XHR onreadystatechange handler",bF=" which is already wrapped",bG="called in order but were called as ",bH="Method Not Allowed",bI=" received no arguments, expected ",bJ=", [...",bK=" and at most ",bL=']',bM=" Received [",bN="function, a boolean or an object",bO="Temporary Redirect",bP="Not Found",bQ="expected %n to be called with new",bR=" already called ",bS=";",bT="alwaysCalledWithExactly",bU="    ",bV="Content-Type",bW='withArgs',bX="undefined",bY=" cannot call arg since it was not yet invoked.",ca=" as thisValue, expected ",cb="thrice",cc="notCalledWithMatch",cd="yieldOn",ce="../sinon",cf="array",cg="Expectation Failed",ch="responseXML",ci="Gone",cj=" is not stubbed",ck="Bad Request",cl="Unauthorized",cm="open",cn="send",co=" times",cp="expected %n to be called twice but was called %c%C",cq=", expected ",cr="The constructor should be a function.",cs="{ ",ct="toString",cu="number",cv="Request Entity Too Large",cw="expected %n to be called with match %*%C",cx="INVALID_STATE_ERR",cy="[",cz="Attempted to wrap ",cA="samsam",cB="./sinon/behavior",cC="alwaysCalledOn",cD="ExpectationError",cE="Matcher expected",cF="Unsupported Media Type",cG="yield",cH="p = (function proxy(",cI="Method wrapper should be function",cJ="argument context is not an object",cK='object',cL="once",cM="Expected type of ",cN='"',cO="null",cP="' is not number",cQ="(\"",cR=", which is not a string.",cS="stub",cT="[, ...",cU=";charset=utf-8",cV="expected %n to always be called with new",cW="Multi-Status",cX="AssertError",cY='$1',da="MSXML2.XMLHTTP.3.0",db="mock",dc="util",dd="setTimeout",de="verify",df="addEventListener",dg="function",dh="\n\n",di=")",dj="Should wrap property of object",dk="Invalid time ",dl="expected value to match",dm="text/plain;charset=utf-8",dn="Moved Permanently",dp="Multiple Choice",dq=" received too many arguments (",dr="%n did not throw exception%C",ds="assert",dt="Continue",du="Unexpected call: ",dv="server",dw="' since no callback was passed.",dx="yieldTo",dy="callArgWith",dz=", didn't match ",dA="export",dB="Not Modified",dC="    expected = ",dD="'",dE='Stack Trace for original',dF=" threw exception: ",dG="called ",dH="Unprocessable Entity",dI="expected ",dJ=" is not a function",dK="threw",dL="    actual = ",dM="method is falsy",dN="Expected ",dO="Switching Protocols",dP="clearTimeout",dQ="a,b,c,d,e,f,g,h,i,j,k,l",dR=".and(",dS="boolean",dT="[object Array]",dU="restore",dV="formatio",dW="Proxy Authentication Required",dX="expected %n to be called with %1 as this but was called with %t",dY="neverCalledWith",ea="alwaysReturned",eb="calledWithExactly",ec="div",ed=" but was called %c%C",ee="), expected ",ef=", ",eg="type",eh="<",ei="setInterval",ej='number',ek="-0",el="same(",em="\n",en=") { return p.invoke(func, this, slice.call(arguments)); });",eo="./sinon/test_case",ep="OK",eq=" received too few arguments (",er="expected %n to be called once but was called %c%C",es="fake is not a spy",et="expectedThis",eu="twice",ev="sinon fake",ew="sinon.test needs to wrap a test function, got ",ex="InvalidBodyException",ey="./sinon/spy",ez="match(",eA="expected %n to be called with arguments %*%C",eB="callArgOnWith",eC="expected %n to never be called with arguments %*%C",eD="text/xml",eE="argument at index ",eF="calledOn",eG=" => ",eH="setRequestHeader",eI="Gateway Timeout",eJ="spy",eK="calledWithNew",eL='undefined',eM="[object HTMLDocument]",eN=" expected to yield to '",eO="target is null or undefined",eP="calledThrice",eQ="callCount",eR="spy#",eS="%n did not always throw exception%C",eT="Error",eU="regexp",eV="./sinon/stub",eW="typeOf(\"",eX="Request:\n",eY='progress',fa="expected %n to always be called with arguments %*%C",fb="status",fc="./sinon/call",fd="on",fe="getResponseHeader",ff="\"",fg="at least ",fh="[object global]",fi='invoke',fj="./sinon/collection",fk="anonymous mock expectation",fl=" cannot yield to '",fm="now should be milliseconds since UNIX epoch",fo="alwaysThrew",fp="Array",fq="}",fr="Bad Gateway",fs="\")",ft="clearImmediate",fu="",fv="yieldToOn",fw="]",fx="has",fy=", but was ",fz="expected %n to not have been called but was called %c%C",fA=" }",fB="Response:\n",fC="Reset Content",fD="' is not a number",fE="sandbox",fF="Call id is not a number",fG="Non-Authoritative Information",fH="Not Implemented",fI="./sinon/assert",fJ="Length Required",fK="Fake server response body should be string, but was ",fL="./match",fM='function',fN="property",fO="load",fP="Service Unavailable",fQ="//",fR=" to be ",fS="callArg",fT="test",fU="Conflict",fV=" received wrong arguments ",fW="alwaysCalledWithNew",fX="Anonymous mock",fY=" is not a function: ",ga="Refused to set unsafe header \"",gb="</",gc='Defining a stub by invoking "stub.onCall(...).withArgs(...)" is not supported. ',gd="neverCalledWithMatch",ge="match(\"",gf="string",gg="Function requires at least 1 parameter",gh="defined",gi="removeEventListener",gj="returned",gk="Object",gl="[...]",gm="Fake server request processing",gn=" called with ",go="() {}",gp="expected %n to have been called at least once but was never called",gq="instanceOf(",gr="Request-URI Too Long",gs=".or(",gt="alwaysCalledWith",gu='Use "stub.withArgs(...).onCall(...)" to define sequential behavior for calls with certain arguments.',gv="alwaysCalledWithMatch",gw=" ",gx="No Content",gy="^",gz="callOrder",gA="responseText",gB="Created",gC="argument index is not number",gD="requests",gE="statusText",gF="calledTwice",gG="abort",gH="qx.dev.unit.Sinon",gI=": ",gJ="setImmediate",gK="No headers received",gL="Custom stub should be function",gM=" (",gN=" which is already ",gO="date",gP="spied on",gQ="getAllResponseHeaders",gR='[',gS="See Other",gT="Partial Content",gU="Accepted",gV="object",gW="Cannot stub non-existent own property ",gX="[Circular]",gY="callArgOn",ha=" cannot yield since no callback was passed.",hb="] ",hc="./sinon/match",hd="Internal Server Error",he=">",hf="expected %n to be called with exact arguments %*%C",hg=" as function",hh="function ",hi="Attempted to respond to fake XMLHttpRequest with ",hj=" !",hk="Request done",hl="Microsoft.XMLHTTP",hm="false",hn="notCalledWith",ho="expected %n to be called ",hp="Microsoft.XMLDOM",hq="' since it was not yet invoked.",hr=" expected to yield, but no callback was passed.",hs="HTTP Version Not Supported",ht="never called",hu=",";qx.Bootstrap.define(gH,{statics:{getSinon:null}});(function(){this.sinon=(function(){var samsam,formatio;function define(hx,hv,hw){if(hx==cA){samsam=hv();}else {formatio=hw(samsam);};};define.amd=true;((typeof define===dg&&define.amd&&function(m){define(cA,m);})||(typeof module===gV&&function(m){module.exports=m();})||function(m){this.samsam=m();})(function(){var o=Object.prototype;var hG=typeof document!==bX&&document.createElement(ec);function isNaN(hJ){var hK=hJ;return typeof hJ===cu&&hJ!==hK;};function hz(hL){return o.toString.call(hL).split(/[ \]]/)[1];};function hI(hM){if(typeof hM!==gV||typeof hM.length!==cu||hz(hM)===fp){return false;};if(typeof hM.callee==dg){return true;};try{hM[hM.length]=6;delete hM[hM.length];}catch(e){return true;};return false;};function hF(hN){if(!hN||hN.nodeType!==1||!hG){return false;};try{hN.appendChild(hG);hN.removeChild(hG);}catch(e){return false;};return true;};function hA(hP){var hO=[],hQ;for(hQ in hP){if(o.hasOwnProperty.call(hP,hQ)){hO.push(hQ);};};return hO;};function hB(hR){return typeof hR.getTime==dg&&hR.getTime()==hR.valueOf();};function hE(hS){return hS===0&&1/hS===-Infinity;};function hy(hT,hU){if(hT===hU||(isNaN(hT)&&isNaN(hU))){return hT!==0||hE(hT)===hE(hU);};};function hD(hV,ib){var hY=[],ia=[],ie=[],ic=[],hX={};function ig(ih){if(typeof ih===cK&&ih!==null&&!(ih instanceof Boolean)&&!(ih instanceof Date)&&!(ih instanceof Number)&&!(ih instanceof RegExp)&&!(ih instanceof String)){return true;};return false;};function hW(ii,ij){var i;for(i=0;i<ii.length;i++ ){if(ii[i]===ij){return i;};};return -1;};return (function iH(iw,ix,iE,iD){var ip=typeof iw;var iq=typeof ix;if(iw===ix||isNaN(iw)||isNaN(ix)||iw==null||ix==null||ip!==gV||iq!==gV){return hy(iw,ix);};if(hF(iw)||hF(ix)){return false;};var io=hB(iw),im=hB(ix);if(io||im){if(!io||!im||iw.getTime()!==ix.getTime()){return false;};};if(iw instanceof RegExp&&ix instanceof RegExp){if(iw.toString()!==ix.toString()){return false;};};var iv=hz(iw);var iu=hz(ix);var iy=hA(iw);var iz=hA(ix);if(hI(iw)||hI(ix)){if(iw.length!==ix.length){return false;};}else {if(ip!==iq||iv!==iu||iy.length!==iz.length){return false;};};var iA,i,l,it,ir,il,ik,iC,iB,iF,iG;for(i=0,l=iy.length;i<l;i++ ){iA=iy[i];if(!o.hasOwnProperty.call(ix,iA)){return false;};it=iw[iA];ir=ix[iA];il=ig(it);ik=ig(ir);iC=il?hW(hY,it):-1;iB=ik?hW(ia,ir):-1;iF=iC!==-1?ie[iC]:iE+gR+JSON.stringify(iA)+bL;iG=iB!==-1?ic[iB]:iD+gR+JSON.stringify(iA)+bL;if(hX[iF+iG]){return true;};if(iC===-1&&il){hY.push(it);ie.push(iF);};if(iB===-1&&ik){ia.push(ir);ic.push(iG);};if(il&&ik){hX[iF+iG]=true;};if(!iH(it,ir,iF,iG)){return false;};};return true;}(hV,ib,cY,T));};var hH;function hC(iJ,iI){if(iI.length===0){return true;};var i,l,j,k;for(i=0,l=iJ.length;i<l; ++i){if(hH(iJ[i],iI[0])){for(j=0,k=iI.length;j<k; ++j){if(!hH(iJ[i+j],iI[j])){return false;};};return true;};};return false;};hH=function iN(iM,iL){if(iL&&typeof iL.test===dg){return iL.test(iM);};if(typeof iL===dg){return iL(iM)===true;};if(typeof iL===gf){iL=iL.toLowerCase();var iK=typeof iM===gf||!!iM;return iK&&(String(iM)).toLowerCase().indexOf(iL)>=0;};if(typeof iL===cu){return iL===iM;};if(typeof iL===dS){return iL===iM;};if(hz(iM)===fp&&hz(iL)===fp){return hC(iM,iL);};if(iL&&typeof iL===gV){var iO;for(iO in iL){if(!iN(iM[iO],iL[iO])){return false;};};return true;};throw new Error(bm+bN);};return {isArguments:hI,isElement:hF,isDate:hB,isNegZero:hE,identical:hy,deepEqual:hD,match:hH,keys:hA};});((typeof define===dg&&define.amd&&function(m){define(dV,[cA],m);})||(typeof module===gV&&function(m){module.exports=m(require(cA));})||function(m){this.formatio=m(this.samsam);})(function(iS){var iU={excludeConstructors:[gk,/^.$/],quoteStrings:true};var iR=[];if(typeof global!==bX){iR.push({object:global,value:fh});};if(typeof document!==bX){iR.push({object:document,value:eM});};if(typeof window!==bX){iR.push({object:window,value:bh});};function iP(iY){if(!iY){return fu;};if(iY.displayName){return iY.displayName;};if(iY.name){return iY.name;};var iX=iY.toString().match(/function\s+([^\(]+)/m);return (iX&&iX[1])||fu;};function iQ(f,jb){var name=iP(jb&&jb.constructor);var ja=f.excludeConstructors||iU.excludeConstructors||[];var i,l;for(i=0,l=ja.length;i<l; ++i){if(typeof ja[i]===gf&&ja[i]===name){return fu;}else if(ja[i].test&&ja[i].test(name)){return fu;};};return name;};function iT(jc,jd){if(typeof jc!==gV){return false;};var i,l;for(i=0,l=jd.length;i<l; ++i){if(jd[i]===jc){return true;};};return false;};function iW(f,jh,ji,jf){if(typeof jh===gf){var je=f.quoteStrings;var jg=typeof je!==dS||je;return ji||jg?cN+jh+cN:jh;};if(typeof jh===dg&&!(jh instanceof RegExp)){return iW.func(jh);};ji=ji||[];if(iT(jh,ji)){return gX;};if(Object.prototype.toString.call(jh)===dT){return iW.array.call(f,jh,ji);};if(!jh){return String((1/jh)===-Infinity?ek:jh);};if(iS.isElement(jh)){return iW.element(jh);};if(typeof jh.toString===dg&&jh.toString!==Object.prototype.toString){return jh.toString();};var i,l;for(i=0,l=iR.length;i<l;i++ ){if(jh===iR[i].object){return iR[i].value;};};return iW.object.call(f,jh,ji,jf);};iW.func=function(jj){return hh+iP(jj)+go;};iW.array=function(jk,jl){jl=jl||[];jl.push(jk);var i,l,jm=[];for(i=0,l=jk.length;i<l; ++i){jm.push(iW(this,jk[i],jl));};return cy+jm.join(ef)+fw;};iW.object=function(jq,ju,jo){ju=ju||[];ju.push(jq);jo=jo||0;var jr=[],jx=iS.keys(jq).sort();var length=3;var jw,jv,jt,i,l;for(i=0,l=jx.length;i<l; ++i){jw=jx[i];jt=jq[jw];if(iT(jt,ju)){jv=gX;}else {jv=iW(this,jt,ju,jo+2);};jv=(/\s/.test(jw)?cN+jw+cN:jw)+gI+jv;length+=jv.length;jr.push(jv);};var jn=iQ(this,jq);var js=jn?cy+jn+hb:fu;var jp=fu;for(i=0,l=jo;i<l; ++i){jp+=gw;};if(length+jo>80){return js+X+jp+jr.join(bn+jp)+em+jp+fq;};return js+cs+jr.join(ef)+fA;};iW.element=function(jD){var jG=jD.tagName.toLowerCase();var jF=jD.attributes,jA,jz=[],jy,i,l,jB;for(i=0,l=jF.length;i<l; ++i){jA=jF.item(i);jy=jA.nodeName.toLowerCase().replace(c,fu);jB=jA.nodeValue;if(jy!==by||jB!==G){if(!!jB){jz.push(jy+bp+jB+ff);};};};var jE=eh+jG+(jz.length>0?gw:fu);var content=jD.innerHTML;if(content.length>20){content=content.substr(0,20)+gl;};var jC=jE+jz.join(gw)+he+content+gb+jG+he;return jC.replace(/ contentEditable="inherit"/,fu);};function iV(jI){for(var jH in jI){this[jH]=jI[jH];};};iV.prototype={functionName:iP,configure:function(jJ){return new iV(jJ);},constructorName:function(jK){return iQ(this,jK);},ascii:function(jL,jN,jM){return iW(this,jL,jN,jM);}};return iV.prototype;});var sinon=(function(jQ){var jY=typeof document!=bX&&document.createElement(ec);var jS=Object.prototype.hasOwnProperty;function jW(kd){var ke=false;try{kd.appendChild(jY);ke=jY.parentNode==kd;}catch(e){return false;}finally{try{kd.removeChild(jY);}catch(e){};};return ke;};function jX(kf){return jY&&kf&&kf.nodeType===1&&jW(kf);};function kb(kg){return typeof kg===dg||!!(kg&&kg.constructor&&kg.call&&kg.apply);};function jP(kh){return typeof kh===ej&&isNaN(kh);};function kc(kj,ki){for(var kk in ki){if(!jS.call(kj,kk)){kj[kk]=ki[kk];};};};function jO(kl){return typeof kl===dg&&typeof kl.restore===dg&&kl.restore.sinon;};var jV={wrapMethod:function km(kn,kt,ku){if(!kn){throw new TypeError(dj);};if(typeof ku!=dg){throw new TypeError(cI);};var kq=kn[kt],kr;if(!kb(kq)){kr=new TypeError(cz+(typeof kq)+M+kt+hg);};if(kq.restore&&kq.restore.sinon){kr=new TypeError(cz+kt+bF);};if(kq.calledBefore){var kp=!!kq.returns?E:gP;kr=new TypeError(cz+kt+gN+kp);};if(kr){if(kq._stack){kr.stack+=H+kq._stack;};throw kr;};var ko=kn.hasOwnProperty?kn.hasOwnProperty(kt):jS.call(kn,kt);kn[kt]=ku;ku.displayName=kt;ku._stack=(new Error(dE)).stack;ku.restore=function(){if(!ko){delete kn[kt];};if(kn[kt]===ku){kn[kt]=kq;};};ku.restore.sinon=true;kc(ku,kq);return ku;},extend:function kx(kw){for(var i=1,l=arguments.length;i<l;i+=1){for(var kv in arguments[i]){if(arguments[i].hasOwnProperty(kv)){kw[kv]=arguments[i][kv];};if(arguments[i].hasOwnProperty(ct)&&arguments[i].toString!=kw.toString){kw.toString=arguments[i].toString;};};};return kw;},create:function ky(kz){var F=function(){};F.prototype=kz;return new F();},deepEqual:function kE(a,b){if(jV.match&&jV.match.isMatcher(a)){return a.test(b);};if(typeof a!=cK||typeof b!=cK){if(jP(a)&&jP(b)){return true;}else {return a===b;};};if(jX(a)||jX(b)){return a===b;};if(a===b){return true;};if((a===null&&b!==null)||(a!==null&&b===null)){return false;};if(a instanceof RegExp&&b instanceof RegExp){return (a.source===b.source)&&(a.global===b.global)&&(a.ignoreCase===b.ignoreCase)&&(a.multiline===b.multiline);};var kB=Object.prototype.toString.call(a);if(kB!=Object.prototype.toString.call(b)){return false;};if(kB==W){return a.valueOf()===b.valueOf();};var kC,kD=0,kA=0;if(kB==dT&&a.length!==b.length){return false;};for(kC in a){kD+=1;if(!kE(a[kC],b[kC])){return false;};};for(kC in b){kA+=1;};return kD==kA;},functionName:function kG(kH){var name=kH.displayName||kH.name;if(!name){var kF=kH.toString().match(/function ([^\s\(]+)/);name=kF&&kF[1];};return name;},functionToString:function kI(){if(this.getCall&&this.callCount){var kJ,kK,i=this.callCount;while(i-- ){kJ=this.getCall(i).thisValue;for(kK in kJ){if(kJ[kK]===this){return kK;};};};};return this.displayName||ev;},getConfig:function(kL){var kM={};kL=kL||{};var kN=jV.defaultConfig;for(var kO in kN){if(kN.hasOwnProperty(kO)){kM[kO]=kL.hasOwnProperty(kO)?kL[kO]:kN[kO];};};return kM;},format:function(kP){return fu+kP;},defaultConfig:{injectIntoThis:true,injectInto:null,properties:[eJ,cS,db,P,dv,gD],useFakeTimers:true,useFakeServer:true},timesInWords:function kR(kQ){return kQ==1&&cL||kQ==2&&eu||kQ==3&&cb||(kQ||0)+co;},calledInOrder:function(kS){for(var i=1,l=kS.length;i<l;i++ ){if(!kS[i-1].calledBefore(kS[i])||!kS[i].called){return false;};};return true;},orderByFirstCall:function(kT){return kT.sort(function(a,b){var kU=a.getCall(0);var kX=b.getCall(0);var kW=kU&&kU.callId||-1;var kV=kX&&kX.callId||-1;return kW<kV?-1:1;});},log:function(){},logError:function(lb,la){var kY=lb+dF;jV.log(kY+cy+la.name+hb+la.message);if(la.stack){jV.log(la.stack);};setTimeout(function(){la.message=kY+la.message;throw la;},0);},typeOf:function(ld){if(ld===null){return cO;}else if(ld===undefined){return bX;};var lc=Object.prototype.toString.call(ld);return lc.substring(8,lc.length-1).toLowerCase();},createStubInstance:function(le){if(typeof le!==dg){throw new TypeError(cr);};return jV.stub(jV.create(le.prototype));},restore:function(lf){if(lf!==null&&typeof lf===gV){for(var lg in lf){if(jO(lf[lg])){lf[lg].restore();};};}else if(jO(lf)){lf.restore();};}};var jR=typeof module!==bX&&module.exports;var jU=typeof define===fM&&typeof define.amd===cK&&define.amd;if(jU){define(function(){return jV;});}else if(jR){try{jQ=require(dV);}catch(e){};module.exports=jV;module.exports.spy=require(ey);module.exports.spyCall=require(fc);module.exports.behavior=require(cB);module.exports.stub=require(eV);module.exports.mock=require(d);module.exports.collection=require(fj);module.exports.assert=require(fI);module.exports.sandbox=require(B);module.exports.test=require(bz);module.exports.testCase=require(eo);module.exports.assert=require(fI);module.exports.match=require(hc);};if(jQ){var ka=jQ.configure({quoteStrings:false});jV.format=function(){return ka.ascii.apply(ka,arguments);};}else if(jR){try{var jT=require(dc);jV.format=function(lh){return typeof lh==gV&&lh.toString===Object.prototype.toString?jT.inspect(lh):lh;};}catch(e){};};return jV;}(typeof formatio==gV&&formatio));(function(ln){var li=typeof module!==eL&&module.exports;if(!ln&&li){ln=require(ce);};if(!ln){return;};function ll(ls,lq,name){var lr=ln.typeOf(ls);if(lr!==lq){throw new TypeError(cM+name+fR+lq+fy+lr);};};var lk={toString:function(){return this.message;}};function lm(lt){return lk.isPrototypeOf(lt);};function lj(lw,lv){if(lv===null||lv===undefined){return false;};for(var lx in lw){if(lw.hasOwnProperty(lx)){var lu=lw[lx];var ly=lv[lx];if(lo.isMatcher(lu)){if(!lu.test(ly)){return false;};}else if(ln.typeOf(lu)===gV){if(!lj(lu,ly)){return false;};}else if(!ln.deepEqual(lu,ly)){return false;};};};return true;};lk.or=function(lB){if(!arguments.length){throw new TypeError(cE);}else if(!lm(lB)){lB=lo(lB);};var lz=this;var lA=ln.create(lk);lA.test=function(lC){return lz.test(lC)||lB.test(lC);};lA.message=lz.message+gs+lB.message+di;return lA;};lk.and=function(lF){if(!arguments.length){throw new TypeError(cE);}else if(!lm(lF)){lF=lo(lF);};var lE=this;var lD=ln.create(lk);lD.test=function(lG){return lE.test(lG)&&lF.test(lG);};lD.message=lE.message+dR+lF.message+di;return lD;};var lo=function(lI,lK){var m=ln.create(lk);var lL=ln.typeOf(lI);switch(lL){case gV:if(typeof lI.test===dg){m.test=function(lM){return lI.test(lM)===true;};m.message=ez+ln.functionName(lI.test)+di;return m;};var lH=[];for(var lJ in lI){if(lI.hasOwnProperty(lJ)){lH.push(lJ+gI+lI[lJ]);};};m.test=function(lN){return lj(lI,lN);};m.message=ez+lH.join(ef)+di;break;case cu:m.test=function(lO){return lI==lO;};break;case gf:m.test=function(lP){if(typeof lP!==gf){return false;};return lP.indexOf(lI)!==-1;};m.message=ge+lI+fs;break;case eU:m.test=function(lQ){if(typeof lQ!==gf){return false;};return lI.test(lQ);};break;case dg:m.test=lI;if(lK){m.message=lK;}else {m.message=ez+ln.functionName(lI)+di;};break;default:m.test=function(lR){return ln.deepEqual(lI,lR);};};if(!m.message){m.message=ez+lI+di;};return m;};lo.isMatcher=lm;lo.any=lo(function(){return true;},bu);lo.defined=lo(function(lS){return lS!==null&&lS!==undefined;},gh);lo.truthy=lo(function(lT){return !!lT;},n);lo.falsy=lo(function(lU){return !lU;},O);lo.same=function(lV){return lo(function(lW){return lV===lW;},el+lV+di);};lo.typeOf=function(lX){ll(lX,gf,eg);return lo(function(lY){return ln.typeOf(lY)===lX;},eW+lX+fs);};lo.instanceOf=function(ma){ll(ma,dg,eg);return lo(function(mb){return mb instanceof ma;},gq+ln.functionName(ma)+di);};function lp(md,mc){return function(mg,mh){ll(mg,gf,fN);var me=arguments.length===1;var mf=mc+cQ+mg+ff;if(!me){mf+=ef+mh;};mf+=di;return lo(function(mi){if(mi===undefined||mi===null||!md(mi,mg)){return false;};return me||ln.deepEqual(mh,mi[mg]);},mf);};};lo.has=lp(function(mk,mj){if(typeof mk===gV){return mj in mk;};return mk[mj]!==undefined;},fx);lo.hasOwn=lp(function(mm,ml){return mm.hasOwnProperty(ml);},bk);lo.bool=lo.typeOf(dS);lo.number=lo.typeOf(cu);lo.string=lo.typeOf(gf);lo.object=lo.typeOf(gV);lo.func=lo.typeOf(dg);lo.array=lo.typeOf(cf);lo.regexp=lo.typeOf(eU);lo.date=lo.typeOf(gO);if(li){module.exports=lo;}else {ln.match=lo;};}(typeof sinon==gV&&sinon||null));(function(mr){var mn=typeof module!==eL&&module.exports;if(!mr&&mn){mr=require(ce);};if(!mr){return;};function mo(mx,mv,mw){var mu=mr.functionName(mx)+mv;if(mw.length){mu+=bM+mp.call(mw).join(ef)+fw;};throw new Error(mu);};var mp=Array.prototype.slice;var mt={calledOn:function my(mz){if(mr.match&&mr.match.isMatcher(mz)){return mz.test(this.thisValue);};return this.thisValue===mz;},calledWith:function mA(){for(var i=0,l=arguments.length;i<l;i+=1){if(!mr.deepEqual(arguments[i],this.args[i])){return false;};};return true;},calledWithMatch:function mC(){for(var i=0,l=arguments.length;i<l;i+=1){var mB=this.args[i];var mD=arguments[i];if(!mr.match||!mr.match(mD).test(mB)){return false;};};return true;},calledWithExactly:function mE(){return arguments.length==this.args.length&&this.calledWith.apply(this,arguments);},notCalledWith:function mF(){return !this.calledWith.apply(this,arguments);},notCalledWithMatch:function mG(){return !this.calledWithMatch.apply(this,arguments);},returned:function mH(mI){return mr.deepEqual(mI,this.returnValue);},threw:function mJ(mK){if(typeof mK===bX||!this.exception){return !!this.exception;};return this.exception===mK||this.exception.name===mK;},calledWithNew:function mL(){return this.proxy.prototype&&this.thisValue instanceof this.proxy;},calledBefore:function(mM){return this.callId<mM.callId;},calledAfter:function(mN){return this.callId>mN.callId;},callArg:function(mO){this.args[mO]();},callArgOn:function(mP,mQ){this.args[mP].apply(mQ);},callArgWith:function(mR){this.callArgOnWith.apply(this,[mR,null].concat(mp.call(arguments,1)));},callArgOnWith:function(mT,mU){var mS=mp.call(arguments,2);this.args[mT].apply(mU,mS);},"yield":function(){this.yieldOn.apply(this,[null].concat(mp.call(arguments,0)));},yieldOn:function(mW){var mV=this.args;for(var i=0,l=mV.length;i<l; ++i){if(typeof mV[i]===dg){mV[i].apply(mW,mp.call(arguments,1));return;};};mo(this.proxy,ha,mV);},yieldTo:function(mX){this.yieldToOn.apply(this,[mX,null].concat(mp.call(arguments,1)));},yieldToOn:function(nb,na){var mY=this.args;for(var i=0,l=mY.length;i<l; ++i){if(mY[i]&&typeof mY[i][nb]===dg){mY[i][nb].apply(na,mp.call(arguments,2));return;};};mo(this.proxy,fl+nb+dw,mY);},toString:function(){var nc=this.proxy.toString()+bb;var nd=[];for(var i=0,l=this.args.length;i<l; ++i){nd.push(mr.format(this.args[i]));};nc=nc+nd.join(ef)+di;if(typeof this.returnValue!=bX){nc+=eG+mr.format(this.returnValue);};if(this.exception){nc+=hj+this.exception.name;if(this.exception.message){nc+=bb+this.exception.message+di;};};return nc;}};mt.invokeCallback=mt.yield;function mq(ne,ng,ni,nj,nf,nk){if(typeof nk!==cu){throw new TypeError(fF);};var nh=mr.create(mt);nh.proxy=ne;nh.thisValue=ng;nh.args=ni;nh.returnValue=nj;nh.exception=nf;nh.callId=nk;return nh;};mq.toString=mt.toString;if(mn){module.exports=mq;}else {mr.spyCall=mq;};}(typeof sinon==gV&&sinon||null));(function(sinon){var commonJSModule=typeof module!==eL&&module.exports;var push=Array.prototype.push;var slice=Array.prototype.slice;var callId=0;if(!sinon&&commonJSModule){sinon=require(ce);};if(!sinon){return;};function spy(nn,nl){if(!nl&&typeof nn==dg){return spy.create(nn);};if(!nn&&!nl){return spy.create(function(){});};var nm=nn[nl];return sinon.wrapMethod(nn,nl,spy.create(nm));};function matchingFake(nq,np,no){if(!nq){return;};for(var i=0,l=nq.length;i<l;i++ ){if(nq[i].matches(np,no)){return nq[i];};};};function incrementCallCount(){this.called=true;this.callCount+=1;this.notCalled=false;this.calledOnce=this.callCount==1;this.calledTwice=this.callCount==2;this.calledThrice=this.callCount==3;};function createCallProperties(){this.firstCall=this.getCall(0);this.secondCall=this.getCall(1);this.thirdCall=this.getCall(2);this.lastCall=this.getCall(this.callCount-1);};var vars=dQ;function createProxy(func){var p;if(func.length){eval(cH+vars.substring(0,func.length*2-1)+en);}else {p=function nr(){return p.invoke(func,this,slice.call(arguments));};};return p;};var uuid=0;var spyApi={reset:function(){this.called=false;this.notCalled=true;this.calledOnce=false;this.calledTwice=false;this.calledThrice=false;this.callCount=0;this.firstCall=null;this.secondCall=null;this.thirdCall=null;this.lastCall=null;this.args=[];this.returnValues=[];this.thisValues=[];this.exceptions=[];this.callIds=[];if(this.fakes){for(var i=0;i<this.fakes.length;i++ ){this.fakes[i].reset();};};},create:function ns(nt){var name;if(typeof nt!=dg){nt=function(){};}else {name=sinon.functionName(nt);};var nu=createProxy(nt);sinon.extend(nu,spy);delete nu.create;sinon.extend(nu,nt);nu.reset();nu.prototype=nt.prototype;nu.displayName=name||eJ;nu.toString=sinon.functionToString;nu._create=sinon.spy.create;nu.id=eR+uuid++ ;return nu;},invoke:function ny(nw,nx,nz){var nC=matchingFake(this.fakes,nz);var nv,nB;incrementCallCount.call(this);push.call(this.thisValues,nx);push.call(this.args,nz);push.call(this.callIds,callId++ );try{if(nC){nB=nC.invoke(nw,nx,nz);}else {nB=(this.func||nw).apply(nx,nz);};var nA=this.getCall(this.callCount-1);if(nA.calledWithNew()&&typeof nB!==cK){nB=nx;};}catch(e){nv=e;};push.call(this.exceptions,nv);push.call(this.returnValues,nB);createCallProperties.call(this);if(nv!==undefined){throw nv;};return nB;},getCall:function nD(i){if(i<0||i>=this.callCount){return null;};return sinon.spyCall(this,this.thisValues[i],this.args[i],this.returnValues[i],this.exceptions[i],this.callIds[i]);},getCalls:function(){var nE=[];var i;for(i=0;i<this.callCount;i++ ){nE.push(this.getCall(i));};return nE;},calledBefore:function nF(nG){if(!this.called){return false;};if(!nG.called){return true;};return this.callIds[0]<nG.callIds[nG.callIds.length-1];},calledAfter:function nI(nH){if(!this.called||!nH.called){return false;};return this.callIds[this.callCount-1]>nH.callIds[nH.callCount-1];},withArgs:function(){var nJ=slice.call(arguments);if(this.fakes){var nL=matchingFake(this.fakes,nJ,true);if(nL){return nL;};}else {this.fakes=[];};var nK=this;var nM=this._create();nM.matchingAguments=nJ;nM.parent=this;push.call(this.fakes,nM);nM.withArgs=function(){return nK.withArgs.apply(nK,arguments);};for(var i=0;i<this.args.length;i++ ){if(nM.matches(this.args[i])){incrementCallCount.call(nM);push.call(nM.thisValues,this.thisValues[i]);push.call(nM.args,this.args[i]);push.call(nM.returnValues,this.returnValues[i]);push.call(nM.exceptions,this.exceptions[i]);push.call(nM.callIds,this.callIds[i]);};};createCallProperties.call(nM);return nM;},matches:function(nP,nN){var nO=this.matchingAguments;if(nO.length<=nP.length&&sinon.deepEqual(nO,nP.slice(0,nO.length))){return !nN||nO.length==nP.length;};},printf:function(nT){var nQ=this;var nR=slice.call(arguments,1);var nS;return (nT||fu).replace(/%(.)/g,function(nV,nU){nS=spyApi.formatters[nU];if(typeof nS==dg){return nS.call(null,nQ,nR);}else if(!isNaN(parseInt(nU,10))){return sinon.format(nR[nU-1]);};return u+nU;});}};function delegateToCalls(nY,nW,nX,oa){spyApi[nY]=function(){if(!this.called){if(oa){return oa.apply(this,arguments);};return false;};var oc;var ob=0;for(var i=0,l=this.callCount;i<l;i+=1){oc=this.getCall(i);if(oc[nX||nY].apply(oc,arguments)){ob+=1;if(nW){return true;};};};return ob===this.callCount;};};delegateToCalls(eF,true);delegateToCalls(cC,false,eF);delegateToCalls(bl,true);delegateToCalls(U,true);delegateToCalls(gt,false,bl);delegateToCalls(gv,false,U);delegateToCalls(eb,true);delegateToCalls(bT,false,eb);delegateToCalls(dY,false,hn,function(){return true;});delegateToCalls(gd,false,cc,function(){return true;});delegateToCalls(dK,true);delegateToCalls(fo,false,dK);delegateToCalls(gj,true);delegateToCalls(ea,false,gj);delegateToCalls(eK,true);delegateToCalls(fW,false,eK);delegateToCalls(fS,false,dy,function(){throw new Error(this.toString()+bY);});spyApi.callArgWith=spyApi.callArg;delegateToCalls(gY,false,eB,function(){throw new Error(this.toString()+bY);});spyApi.callArgOnWith=spyApi.callArgOn;delegateToCalls(cG,false,cG,function(){throw new Error(this.toString()+Y);});spyApi.invokeCallback=spyApi.yield;delegateToCalls(cd,false,cd,function(){throw new Error(this.toString()+Y);});delegateToCalls(dx,false,dx,function(od){throw new Error(this.toString()+fl+od+hq);});delegateToCalls(fv,false,fv,function(oe){throw new Error(this.toString()+fl+oe+hq);});spyApi.formatters={"c":function(of){return sinon.timesInWords(of.callCount);},"n":function(og){return og.toString();},"C":function(oh){var oi=[];for(var i=0,l=oh.callCount;i<l; ++i){var oj=bU+oh.getCall(i).toString();if(/\n/.test(oi[i-1])){oj=em+oj;};push.call(oi,oj);};return oi.length>0?em+oi.join(em):fu;},"t":function(ok){var ol=[];for(var i=0,l=ok.callCount;i<l; ++i){push.call(ol,sinon.format(ok.thisValues[i]));};return ol.join(ef);},"*":function(om,oo){var op=[];for(var i=0,l=oo.length;i<l; ++i){push.call(op,sinon.format(oo[i]));};return op.join(ef);}};sinon.extend(spy,spyApi);spy.spyCall=sinon.spyCall;if(commonJSModule){module.exports=spy;}else {sinon.spy=spy;};}(typeof sinon==gV&&sinon||null));(function(oy){var oq=typeof module!==eL&&module.exports;if(!oy&&oq){oy=require(ce);};if(!oy){return;};var ot=Array.prototype.slice;var ou=Array.prototype.join;var ow;var os=(function(){if(typeof process===gV&&typeof process.nextTick===dg){return process.nextTick;}else if(typeof setImmediate===dg){return setImmediate;}else {return function(oC){setTimeout(oC,0);};};})();function oA(oE,oD){if(typeof oE==gf){this.exception=new Error(oD||fu);this.exception.name=oE;}else if(!oE){this.exception=new Error(eT);}else {this.exception=oE;};return this;};function oz(oH,oF){var oI=oH.callArgAt;if(oI<0){var oG=oH.callArgProp;for(var i=0,l=oF.length;i<l; ++i){if(!oG&&typeof oF[i]==dg){return oF[i];};if(oG&&oF[i]&&typeof oF[i][oG]==dg){return oF[i][oG];};};return null;};return oF[oI];};function ox(oL,oM,oK){if(oL.callArgAt<0){var oJ;if(oL.callArgProp){oJ=oy.functionName(oL.stub)+eN+oL.callArgProp+R;}else {oJ=oy.functionName(oL.stub)+hr;};if(oK.length>0){oJ+=bM+ou.call(oK,ef)+fw;};return oJ;};return eE+oL.callArgAt+fY+oM;};function ov(oO,oN){if(typeof oO.callArgAt==cu){var oP=oz(oO,oN);if(typeof oP!=dg){throw new TypeError(ox(oO,oP,oN));};if(oO.callbackAsync){os(function(){oP.apply(oO.callbackContext,oO.callbackArguments);});}else {oP.apply(oO.callbackContext,oO.callbackArguments);};};};ow={create:function(oQ){var oR=oy.extend({},oy.behavior);delete oR.create;oR.stub=oQ;return oR;},isPresent:function(){return (typeof this.callArgAt==ej||this.exception||typeof this.returnArgAt==ej||this.returnThis||this.returnValueDefined);},invoke:function(oT,oS){ov(this,oS);if(this.exception){throw this.exception;}else if(typeof this.returnArgAt==ej){return oS[this.returnArgAt];}else if(this.returnThis){return oT;};return this.returnValue;},onCall:function(oU){return this.stub.onCall(oU);},onFirstCall:function(){return this.stub.onFirstCall();},onSecondCall:function(){return this.stub.onSecondCall();},onThirdCall:function(){return this.stub.onThirdCall();},withArgs:function(){throw new Error(gc+gu);},callsArg:function oV(oW){if(typeof oW!=cu){throw new TypeError(gC);};this.callArgAt=oW;this.callbackArguments=[];this.callbackContext=undefined;this.callArgProp=undefined;this.callbackAsync=false;return this;},callsArgOn:function oX(oY,pa){if(typeof oY!=cu){throw new TypeError(gC);};if(typeof pa!=gV){throw new TypeError(cJ);};this.callArgAt=oY;this.callbackArguments=[];this.callbackContext=pa;this.callArgProp=undefined;this.callbackAsync=false;return this;},callsArgWith:function pb(pc){if(typeof pc!=cu){throw new TypeError(gC);};this.callArgAt=pc;this.callbackArguments=ot.call(arguments,1);this.callbackContext=undefined;this.callArgProp=undefined;this.callbackAsync=false;return this;},callsArgOnWith:function pd(pf,pe){if(typeof pf!=cu){throw new TypeError(gC);};if(typeof pe!=gV){throw new TypeError(cJ);};this.callArgAt=pf;this.callbackArguments=ot.call(arguments,2);this.callbackContext=pe;this.callArgProp=undefined;this.callbackAsync=false;return this;},yields:function(){this.callArgAt=-1;this.callbackArguments=ot.call(arguments,0);this.callbackContext=undefined;this.callArgProp=undefined;this.callbackAsync=false;return this;},yieldsOn:function(pg){if(typeof pg!=gV){throw new TypeError(cJ);};this.callArgAt=-1;this.callbackArguments=ot.call(arguments,1);this.callbackContext=pg;this.callArgProp=undefined;this.callbackAsync=false;return this;},yieldsTo:function(ph){this.callArgAt=-1;this.callbackArguments=ot.call(arguments,1);this.callbackContext=undefined;this.callArgProp=ph;this.callbackAsync=false;return this;},yieldsToOn:function(pj,pi){if(typeof pi!=gV){throw new TypeError(cJ);};this.callArgAt=-1;this.callbackArguments=ot.call(arguments,2);this.callbackContext=pi;this.callArgProp=pj;this.callbackAsync=false;return this;},"throws":oA,throwsException:oA,returns:function pk(pl){this.returnValue=pl;this.returnValueDefined=true;return this;},returnsArg:function pm(pn){if(typeof pn!=cu){throw new TypeError(gC);};this.returnArgAt=pn;return this;},returnsThis:function po(){this.returnThis=true;return this;}};for(var oB in ow){if(ow.hasOwnProperty(oB)&&oB.match(/^(callsArg|yields)/)&&!oB.match(/Async/)){ow[oB+L]=(function(pp){return function(){var pq=this[pp].apply(this,arguments);this.callbackAsync=true;return pq;};})(oB);};};if(oq){module.exports=ow;}else {oy.behavior=ow;};}(typeof sinon==gV&&sinon||null));(function(pw){var pr=typeof module!==eL&&module.exports;if(!pw&&pr){pw=require(ce);};if(!pw){return;};function pv(pz,py,pA){if(!!pA&&typeof pA!=dg){throw new TypeError(gL);};var pB;if(pA){pB=pw.spy&&pw.spy.create?pw.spy.create(pA):pA;}else {pB=pv.create();};if(!pz&&typeof py===bX){return pw.stub.create();};if(typeof py===bX&&typeof pz==gV){for(var pC in pz){if(typeof pz[pC]===dg){pv(pz,pC);};};return pz;};return pw.wrapMethod(pz,py,pB);};function pu(pD){return pD.defaultBehavior||px(pD)||pw.behavior.create(pD);};function px(pE){return (pE.parent&&ps(pE.parent));};function ps(pF){var pG=pF.behaviors[pF.callCount-1];return pG&&pG.isPresent()?pG:pu(pF);};var pt=0;pw.extend(pv,(function(){var pI={create:function pK(){var pJ=function(){return ps(pJ).invoke(this,arguments);};pJ.id=z+pt++ ;var pL=pJ;pJ=pw.spy.create(pJ);pJ.func=pL;pw.extend(pJ,pv);pJ._create=pw.stub.create;pJ.displayName=cS;pJ.toString=pw.functionToString;pJ.defaultBehavior=null;pJ.behaviors=[];return pJ;},resetBehavior:function(){var i;this.defaultBehavior=null;this.behaviors=[];delete this.returnValue;delete this.returnArgAt;this.returnThis=false;if(this.fakes){for(i=0;i<this.fakes.length;i++ ){this.fakes[i].resetBehavior();};};},onCall:function(pM){if(!this.behaviors[pM]){this.behaviors[pM]=pw.behavior.create(this);};return this.behaviors[pM];},onFirstCall:function(){return this.onCall(0);},onSecondCall:function(){return this.onCall(1);},onThirdCall:function(){return this.onCall(2);}};for(var pH in pw.behavior){if(pw.behavior.hasOwnProperty(pH)&&!pI.hasOwnProperty(pH)&&pH!=x&&pH!=bW&&pH!=fi){pI[pH]=(function(pN){return function(){this.defaultBehavior=this.defaultBehavior||pw.behavior.create(this);this.defaultBehavior[pN].apply(this.defaultBehavior,arguments);return this;};}(pH));};};return pI;}()));if(pr){module.exports=pv;}else {pw.stub=pv;};}(typeof sinon==gV&&sinon||null));(function(pQ){var pO=typeof module!==eL&&module.exports;var pS=[].push;var pT;if(!pQ&&pO){pQ=require(ce);};if(!pQ){return;};pT=pQ.match;if(!pT&&pO){pT=require(fL);};function pR(pU){if(!pU){return pQ.expectation.create(fX);};return pR.create(pU);};pQ.mock=pR;pQ.extend(pR,(function(){function pV(pX,pW){if(!pX){return;};for(var i=0,l=pX.length;i<l;i+=1){pW(pX[i]);};};return {create:function pY(qa){if(!qa){throw new TypeError(bi);};var qb=pQ.extend({},pR);qb.object=qa;delete qb.create;return qb;},expects:function qf(qc){if(!qc){throw new TypeError(dM);};if(!this.expectations){this.expectations={};this.proxies=[];};if(!this.expectations[qc]){this.expectations[qc]=[];var qe=this;pQ.wrapMethod(this.object,qc,function(){return qe.invokeMethod(qc,this,arguments);});pS.call(this.proxies,qc);};var qd=pQ.expectation.create(qc);pS.call(this.expectations[qc],qd);return qd;},restore:function qg(){var qh=this.object;pV(this.proxies,function(qi){if(typeof qh[qi].restore==dg){qh[qi].restore();};});},verify:function qj(){var ql=this.expectations||{};var qk=[],qm=[];pV(this.proxies,function(qn){pV(ql[qn],function(qo){if(!qo.met()){pS.call(qk,qo.toString());}else {pS.call(qm,qo.toString());};});});this.restore();if(qk.length>0){pQ.expectation.fail(qk.concat(qm).join(em));}else {pQ.expectation.pass(qk.concat(qm).join(em));};return true;},invokeMethod:function qw(qy,qq,qr){var qu=this.expectations&&this.expectations[qy];var length=qu&&qu.length||0,i;for(i=0;i<length;i+=1){if(!qu[i].met()&&qu[i].allowsCall(qq,qr)){return qu[i].apply(qq,qr);};};var qt=[],qp,qv=0;for(i=0;i<length;i+=1){if(qu[i].allowsCall(qq,qr)){qp=qp||qu[i];}else {qv+=1;};pS.call(qt,bU+qu[i].toString());};if(qv===0){return qp.apply(qq,qr);};qt.unshift(du+pQ.spyCall.toString.call({proxy:qy,args:qr}));pQ.expectation.fail(qt.join(em));}};}()));var pP=pQ.timesInWords;pQ.expectation=(function(){var qB=Array.prototype.slice;var qz=pQ.spy.invoke;function qF(qG){if(qG==0){return ht;}else {return dG+pP(qG);};};function qC(qI){var qK=qI.minCalls;var qH=qI.maxCalls;if(typeof qK==cu&&typeof qH==cu){var qJ=pP(qK);if(qK!=qH){qJ=fg+qJ+bK+pP(qH);};return qJ;};if(typeof qK==cu){return fg+pP(qK);};return s+pP(qH);};function qA(qL){var qM=typeof qL.minCalls==cu;return !qM||qL.callCount>=qL.minCalls;};function qD(qN){if(typeof qN.maxCalls!=cu){return false;};return qN.callCount==qN.maxCalls;};function qE(qO,qP){if(pT&&pT.isMatcher(qO)){return qO.test(qP);}else {return true;};};return {minCalls:1,maxCalls:1,create:function qQ(qR){var qS=pQ.extend(pQ.stub.create(),pQ.expectation);delete qS.create;qS.method=qR;return qS;},invoke:function qV(qU,qW,qT){this.verifyCallAllowed(qW,qT);return qz.apply(this,arguments);},atLeast:function qY(qX){if(typeof qX!=cu){throw new TypeError(dD+qX+cP);};if(!this.limitsSet){this.maxCalls=null;this.limitsSet=true;};this.minCalls=qX;return this;},atMost:function ra(rb){if(typeof rb!=cu){throw new TypeError(dD+rb+cP);};if(!this.limitsSet){this.minCalls=null;this.limitsSet=true;};this.maxCalls=rb;return this;},never:function rc(){return this.exactly(0);},once:function rd(){return this.exactly(1);},twice:function re(){return this.exactly(2);},thrice:function rf(){return this.exactly(3);},exactly:function rg(rh){if(typeof rh!=cu){throw new TypeError(dD+rh+fD);};this.atLeast(rh);return this.atMost(rh);},met:function ri(){return !this.failed&&qA(this);},verifyCallAllowed:function rk(rl,rj){if(qD(this)){this.failed=true;pQ.expectation.fail(this.method+bR+pP(this.maxCalls));};if(et in this&&this.expectedThis!==rl){pQ.expectation.fail(this.method+gn+rl+ca+this.expectedThis);};if(!(K in this)){return;};if(!rj){pQ.expectation.fail(this.method+bI+pQ.format(this.expectedArguments));};if(rj.length<this.expectedArguments.length){pQ.expectation.fail(this.method+eq+pQ.format(rj)+ee+pQ.format(this.expectedArguments));};if(this.expectsExactArgCount&&rj.length!=this.expectedArguments.length){pQ.expectation.fail(this.method+dq+pQ.format(rj)+ee+pQ.format(this.expectedArguments));};for(var i=0,l=this.expectedArguments.length;i<l;i+=1){if(!qE(this.expectedArguments[i],rj[i])){pQ.expectation.fail(this.method+fV+pQ.format(rj)+dz+this.expectedArguments.toString());};if(!pQ.deepEqual(this.expectedArguments[i],rj[i])){pQ.expectation.fail(this.method+fV+pQ.format(rj)+cq+pQ.format(this.expectedArguments));};};},allowsCall:function rn(ro,rm){if(this.met()&&qD(this)){return false;};if(et in this&&this.expectedThis!==ro){return false;};if(!(K in this)){return true;};rm=rm||[];if(rm.length<this.expectedArguments.length){return false;};if(this.expectsExactArgCount&&rm.length!=this.expectedArguments.length){return false;};for(var i=0,l=this.expectedArguments.length;i<l;i+=1){if(!qE(this.expectedArguments[i],rm[i])){return false;};if(!pQ.deepEqual(this.expectedArguments[i],rm[i])){return false;};};return true;},withArgs:function rp(){this.expectedArguments=qB.call(arguments);return this;},withExactArgs:function rq(){this.withArgs.apply(this,arguments);this.expectsExactArgCount=true;return this;},on:function rr(rs){this.expectedThis=rs;return this;},toString:function(){var rv=(this.expectedArguments||[]).slice();if(!this.expectsExactArgCount){pS.call(rv,gl);};var rt=pQ.spyCall.toString.call({proxy:this.method||fk,args:rv});var rw=rt.replace(bJ,cT)+gw+qC(this);if(this.met()){return y+rw;};return dN+rw+gM+qF(this.callCount)+di;},verify:function rx(){if(!this.met()){pQ.expectation.fail(this.toString());}else {pQ.expectation.pass(this.toString());};return true;},pass:function(ry){pQ.assert.pass(ry);},fail:function(rz){var rA=new Error(rz);rA.name=cD;throw rA;}};}());if(pO){module.exports=pR;}else {pQ.mock=pR;};}(typeof sinon==gV&&sinon||null));(function(rF){var rB=typeof module!==eL&&module.exports;var rI=[].push;var rG=Object.prototype.hasOwnProperty;if(!rF&&rB){rF=require(ce);};if(!rF){return;};function rD(rJ){if(!rJ.fakes){rJ.fakes=[];};return rJ.fakes;};function rH(rK,rM){var rL=rD(rK);for(var i=0,l=rL.length;i<l;i+=1){if(typeof rL[i][rM]==dg){rL[i][rM]();};};};function rC(rN){var rO=rD(rN);var i=0;while(i<rO.length){rO.splice(i,1);};};var rE={verify:function rP(){rH(this,de);},restore:function rQ(){rH(this,dU);rC(this);},verifyAndRestore:function rS(){var rR;try{this.verify();}catch(e){rR=e;};this.restore();if(rR){throw rR;};},add:function rT(rU){rI.call(rD(this),rU);return rU;},spy:function rV(){return this.add(rF.spy.apply(rF,arguments));},stub:function sb(rX,sc,rY){if(sc){var sd=rX[sc];if(typeof sd!=dg){if(!rG.call(rX,sc)){throw new TypeError(gW+sc);};rX[sc]=rY;return this.add({restore:function(){rX[sc]=sd;}});};};if(!sc&&!!rX&&typeof rX==gV){var rW=rF.stub.apply(rF,arguments);for(var sa in rW){if(typeof rW[sa]===dg){this.add(rW[sa]);};};return rW;};return this.add(rF.stub.apply(rF,arguments));},mock:function se(){return this.add(rF.mock.apply(rF,arguments));},inject:function sg(sf){var sh=this;sf.spy=function(){return sh.spy.apply(sh,arguments);};sf.stub=function(){return sh.stub.apply(sh,arguments);};sf.mock=function(){return sh.mock.apply(sh,arguments);};return sf;}};if(rB){module.exports=rE;}else {rF.collection=rE;};}(typeof sinon==gV&&sinon||null));if(typeof sinon==bX){var sinon={};};(function(global){var timeoutResult=setTimeout(function(){},0);var addTimerReturnsObject=typeof timeoutResult===cK;clearTimeout(timeoutResult);var id=1;function addTimer(sk,sl){if(sk.length===0){throw new Error(gg);};if(typeof sk[0]===bX){throw new Error(bf);};var sj=id++ ;var si=sk[1]||0;if(!this.timeouts){this.timeouts={};};this.timeouts[sj]={id:sj,func:sk[0],callAt:this.now+si,invokeArgs:Array.prototype.slice.call(sk,2)};if(sl===true){this.timeouts[sj].interval=si;};if(addTimerReturnsObject){return {id:sj,ref:function(){},unref:function(){}};}else {return sj;};};function parseTime(sm){if(!sm){return 0;};var sn=sm.split(ba);var l=sn.length,i=l;var so=0,sp;if(l>3||!/^(\d\d:){0,2}\d\d?$/.test(sm)){throw new Error(bv);};while(i-- ){sp=parseInt(sn[i],10);if(sp>=60){throw new Error(dk+sm);};so+=sp*Math.pow(60,(l-i-1));};return so*1000;};function createObject(sq){var sr;if(Object.create){sr=Object.create(sq);}else {var F=function(){};F.prototype=sq;sr=new F();};sr.Date.clock=sr;return sr;};sinon.clock={now:0,create:function ss(st){var su=createObject(this);if(typeof st==cu){su.now=st;};if(!!st&&typeof st==gV){throw new TypeError(fm);};return su;},setTimeout:function setTimeout(sv,sw){return addTimer.call(this,arguments,false);},clearTimeout:function clearTimeout(sx){if(!this.timeouts){this.timeouts=[];};if(sx in this.timeouts){delete this.timeouts[sx];};},setInterval:function setInterval(sy,sz){return addTimer.call(this,arguments,true);},clearInterval:function clearInterval(sA){this.clearTimeout(sA);},setImmediate:function sD(sB){var sC=Array.prototype.slice.call(arguments,1);return addTimer.call(this,[sB,0].concat(sC),false);},clearImmediate:function sE(sF){this.clearTimeout(sF);},tick:function sL(sJ){sJ=typeof sJ==cu?sJ:parseTime(sJ);var sK=this.now,sH=this.now+sJ,sM=this.now;var sG=this.firstTimerInRange(sK,sH);var sI;while(sG&&sK<=sH){if(this.timeouts[sG.id]){sK=this.now=sG.callAt;try{this.callTimer(sG);}catch(e){sI=sI||e;};};sG=this.firstTimerInRange(sM,sH);sM=sK;};this.now=sH;if(sI){throw sI;};return this.now;},firstTimerInRange:function(sN,sO){var sP,sR=null,sQ;for(var sS in this.timeouts){if(this.timeouts.hasOwnProperty(sS)){if(this.timeouts[sS].callAt<sN||this.timeouts[sS].callAt>sO){continue;};if(sR===null||this.timeouts[sS].callAt<sR){sQ=this.timeouts[sS];sR=this.timeouts[sS].callAt;sP={func:this.timeouts[sS].func,callAt:this.timeouts[sS].callAt,interval:this.timeouts[sS].interval,id:this.timeouts[sS].id,invokeArgs:this.timeouts[sS].invokeArgs};};};};return sP||null;},callTimer:function(timer){if(typeof timer.interval==cu){this.timeouts[timer.id].callAt+=timer.interval;}else {delete this.timeouts[timer.id];};try{if(typeof timer.func==dg){timer.func.apply(null,timer.invokeArgs);}else {eval(timer.func);};}catch(e){var exception=e;};if(!this.timeouts[timer.id]){if(exception){throw exception;};return;};if(exception){throw exception;};},reset:function sT(){this.timeouts={};},Date:(function(){var sU=Date;function sV(tb,sX,tc,sW,td,sY,ta){switch(arguments.length){case 0:return new sU(sV.clock.now);case 1:return new sU(tb);case 2:return new sU(tb,sX);case 3:return new sU(tb,sX,tc);case 4:return new sU(tb,sX,tc,sW);case 5:return new sU(tb,sX,tc,sW,td);case 6:return new sU(tb,sX,tc,sW,td,sY);default:return new sU(tb,sX,tc,sW,td,sY,ta);};};return mirrorDateProperties(sV,sU);}())};function mirrorDateProperties(tf,te){if(te.now){tf.now=function th(){return tf.clock.now;};}else {delete tf.now;};if(te.toSource){tf.toSource=function ti(){return te.toSource();};}else {delete tf.toSource;};tf.toString=function tj(){return te.toString();};tf.prototype=te.prototype;tf.parse=te.parse;tf.UTC=te.UTC;tf.prototype.toUTCString=te.prototype.toUTCString;for(var tg in te){if(te.hasOwnProperty(tg)){tf[tg]=te[tg];};};return tf;};var methods=[J,dd,ei,dP,v];if(typeof global.setImmediate!==bX){methods.push(gJ);};if(typeof global.clearImmediate!==bX){methods.push(ft);};function restore(){var tk;for(var i=0,l=this.methods.length;i<l;i++ ){tk=this.methods[i];if(global[tk].hadOwnProperty){global[tk]=this[bw+tk];}else {try{delete global[tk];}catch(e){};};};this.methods=[];};function stubGlobal(tn,tm){tm[tn].hadOwnProperty=Object.prototype.hasOwnProperty.call(global,tn);tm[bw+tn]=global[tn];if(tn==J){var tl=mirrorDateProperties(tm[tn],global[tn]);global[tn]=tl;}else {global[tn]=function(){return tm[tn].apply(tm,arguments);};for(var tp in tm[tn]){if(tm[tn].hasOwnProperty(tp)){global[tn][tp]=tm[tn][tp];};};};global[tn].clock=tm;};sinon.useFakeTimers=function tr(tq){var ts=sinon.clock.create(tq);ts.restore=restore;ts.methods=Array.prototype.slice.call(arguments,typeof tq==cu?1:0);if(ts.methods.length===0){ts.methods=methods;};for(var i=0,l=ts.methods.length;i<l;i++ ){stubGlobal(ts.methods[i],ts);};return ts;};}(typeof global!=bX&&typeof global!==dg?global:this));sinon.timers={setTimeout:setTimeout,clearTimeout:clearTimeout,setImmediate:(typeof setImmediate!==bX?setImmediate:undefined),clearImmediate:(typeof clearImmediate!==bX?clearImmediate:undefined),setInterval:setInterval,clearInterval:clearInterval,Date:Date};if(typeof module!==eL&&module.exports){module.exports=sinon;};if(typeof sinon==bX){this.sinon={};};(function(){var tt=[].push;sinon.Event=function Event(tw,tv,tu,tx){this.initEvent(tw,tv,tu,tx);};sinon.Event.prototype={initEvent:function(tA,tz,ty,tB){this.type=tA;this.bubbles=tz;this.cancelable=ty;this.target=tB;},stopPropagation:function(){},preventDefault:function(){this.defaultPrevented=true;}};sinon.ProgressEvent=function tE(tC,tF,tD){this.initEvent(tC,false,false,tD);this.loaded=tF.loaded||null;this.total=tF.total||null;};sinon.ProgressEvent.prototype=new sinon.Event();sinon.ProgressEvent.prototype.constructor=sinon.ProgressEvent;sinon.CustomEvent=function CustomEvent(tH,tG,tI){this.initEvent(tH,false,false,tI);this.detail=tG.detail||null;};sinon.CustomEvent.prototype=new sinon.Event();sinon.CustomEvent.prototype.constructor=sinon.CustomEvent;sinon.EventTarget={addEventListener:function addEventListener(event,tJ){this.eventListeners=this.eventListeners||{};this.eventListeners[event]=this.eventListeners[event]||[];tt.call(this.eventListeners[event],tJ);},removeEventListener:function removeEventListener(event,tK){var tL=this.eventListeners&&this.eventListeners[event]||[];for(var i=0,l=tL.length;i<l; ++i){if(tL[i]==tK){return tL.splice(i,1);};};},dispatchEvent:function dispatchEvent(event){var tN=event.type;var tM=this.eventListeners&&this.eventListeners[tN]||[];for(var i=0;i<tM.length;i++ ){if(typeof tM[i]==dg){tM[i].call(this,event);}else {tM[i].handleEvent(event);};};return !!event.defaultPrevented;}};}());(function(tT){if(typeof sinon===bX){tT.sinon={};};var ud=typeof ProgressEvent!==bX;var tQ=typeof CustomEvent!==bX;sinon.xhr={XMLHttpRequest:tT.XMLHttpRequest};var tR=sinon.xhr;tR.GlobalXMLHttpRequest=tT.XMLHttpRequest;tR.GlobalActiveXObject=tT.ActiveXObject;tR.supportsActiveX=typeof tR.GlobalActiveXObject!=bX;tR.supportsXHR=typeof tR.GlobalXMLHttpRequest!=bX;tR.workingXHR=tR.supportsXHR?tR.GlobalXMLHttpRequest:tR.supportsActiveX?function(){return new tR.GlobalActiveXObject(da);}:false;tR.supportsCORS=q in (new sinon.xhr.GlobalXMLHttpRequest());var tO={"Accept-Charset":true,"Accept-Encoding":true,"Connection":true,"Content-Length":true,"Cookie":true,"Cookie2":true,"Content-Transfer-Encoding":true,"Date":true,"Expect":true,"Host":true,"Keep-Alive":true,"Referer":true,"TE":true,"Trailer":true,"Transfer-Encoding":true,"Upgrade":true,"User-Agent":true,"Via":true};function tW(){this.readyState=tW.UNSENT;this.requestHeaders={};this.requestBody=null;this.status=0;this.statusText=fu;this.upload=new tX();if(sinon.xhr.supportsCORS){this.withCredentials=false;};var uf=this;var ug=[r,fO,gG,bg];function addEventListener(uh){uf.addEventListener(uh,function(event){var ui=uf[fd+uh];if(ui&&typeof ui==dg){ui.call(this,event);};});};for(var i=ug.length-1;i>=0;i-- ){addEventListener(ug[i]);};if(typeof tW.onCreate==dg){tW.onCreate(this);};};function tX(){this.eventListeners={"progress":[],"load":[],"abort":[],"error":[]};};tX.prototype.addEventListener=function(event,uj){this.eventListeners[event].push(uj);};tX.prototype.removeEventListener=function(event,uk){var ul=this.eventListeners[event]||[];for(var i=0,l=ul.length;i<l; ++i){if(ul[i]==uk){return ul.splice(i,1);};};};tX.prototype.dispatchEvent=function(event){var un=this.eventListeners[event.type]||[];for(var i=0,um;(um=un[i])!=null;i++ ){um(event);};};function tY(uo){if(uo.readyState!==tW.OPENED){throw new Error(cx);};if(uo.sendFlag){throw new Error(cx);};};function uc(uq,up){if(!uq)return;for(var i=0,l=uq.length;i<l;i+=1){up(uq[i]);};};function tU(us,ur){for(var ut=0;ut<us.length;ut++ ){if(ur(us[ut])===true)return true;};return false;};var ub=function(uv,uw,uu){switch(uu.length){case 0:return uv[uw]();case 1:return uv[uw](uu[0]);case 2:return uv[uw](uu[0],uu[1]);case 3:return uv[uw](uu[0],uu[1],uu[2]);case 4:return uv[uw](uu[0],uu[1],uu[2],uu[3]);case 5:return uv[uw](uu[0],uu[1],uu[2],uu[3],uu[4]);};};tW.filters=[];tW.addFilter=function(ux){this.filters.push(ux);};var tP=/MSIE 6/;tW.defake=function(uC,uB){var uA=new sinon.xhr.workingXHR();uc([cm,eH,cn,gG,fe,gQ,df,bj,gi],function(uD){uC[uD]=function(){return ub(uA,uD,arguments);};});var uz=function(uE){uc(uE,function(uF){try{uC[uF]=uA[uF];}catch(e){if(!tP.test(navigator.userAgent))throw e;};});};var uy=function(){uC.readyState=uA.readyState;if(uA.readyState>=tW.HEADERS_RECEIVED){uz([fb,gE]);};if(uA.readyState>=tW.LOADING){uz([gA]);};if(uA.readyState===tW.DONE){uz([ch]);};if(uC.onreadystatechange)uC.onreadystatechange.call(uC,{target:uC});};if(uA.addEventListener){for(var event in uC.eventListeners){if(uC.eventListeners.hasOwnProperty(event)){uc(uC.eventListeners[event],function(uG){uA.addEventListener(event,uG);});};};uA.addEventListener(be,uy);}else {uA.onreadystatechange=uy;};ub(uA,cm,uB);};tW.useFilters=false;function tV(uH){if(uH.readyState!=tW.OPENED){throw new Error(A+uH.readyState);};};function ue(uI){if(uI.readyState==tW.DONE){throw new Error(hk);};};function ua(uJ){if(uJ.async&&uJ.readyState!=tW.HEADERS_RECEIVED){throw new Error(gK);};};function tS(uK){if(typeof uK!=gf){var uL=new Error(hi+uK+cR);uL.name=ex;throw uL;};};sinon.extend(tW.prototype,sinon.EventTarget,{async:true,open:function open(uS,uN,uQ,uM,uR){this.method=uS;this.url=uN;this.async=typeof uQ==dS?uQ:true;this.username=uM;this.password=uR;this.responseText=null;this.responseXML=null;this.requestHeaders={};this.sendFlag=false;if(sinon.FakeXMLHttpRequest.useFilters===true){var uO=arguments;var uP=tU(tW.filters,function(uT){return uT.apply(this,uO);});if(uP){return sinon.FakeXMLHttpRequest.defake(this,arguments);};};this.readyStateChange(tW.OPENED);},readyStateChange:function uU(uV){this.readyState=uV;if(typeof this.onreadystatechange==dg){try{this.onreadystatechange();}catch(e){sinon.logError(bE,e);};};this.dispatchEvent(new sinon.Event(be));switch(this.readyState){case tW.DONE:this.dispatchEvent(new sinon.Event(fO,false,false,this));this.dispatchEvent(new sinon.Event(bg,false,false,this));this.upload.dispatchEvent(new sinon.Event(fO,false,false,this));if(ud){this.upload.dispatchEvent(new sinon.ProgressEvent(eY,{loaded:100,total:100}));};break;};},setRequestHeader:function uX(uW,uY){tY(this);if(tO[uW]||/^(Sec-|Proxy-)/.test(uW)){throw new Error(ga+uW+ff);};if(this.requestHeaders[uW]){this.requestHeaders[uW]+=hu+uY;}else {this.requestHeaders[uW]=uY;};},setResponseHeaders:function vb(va){tV(this);this.responseHeaders={};for(var vc in va){if(va.hasOwnProperty(vc)){this.responseHeaders[vc]=va[vc];};};if(this.async){this.readyStateChange(tW.HEADERS_RECEIVED);}else {this.readyState=tW.HEADERS_RECEIVED;};},send:function ve(vd){tY(this);if(!/^(get|head)$/i.test(this.method)){if(this.requestHeaders[bV]){var vf=this.requestHeaders[bV].split(bS);this.requestHeaders[bV]=vf[0]+cU;}else {this.requestHeaders[bV]=dm;};this.requestBody=vd;};this.errorFlag=false;this.sendFlag=this.async;this.readyStateChange(tW.OPENED);if(typeof this.onSend==dg){this.onSend(this);};this.dispatchEvent(new sinon.Event(r,false,false,this));},abort:function vg(){this.aborted=true;this.responseText=null;this.errorFlag=true;this.requestHeaders={};if(this.readyState>sinon.FakeXMLHttpRequest.UNSENT&&this.sendFlag){this.readyStateChange(sinon.FakeXMLHttpRequest.DONE);this.sendFlag=false;};this.readyState=sinon.FakeXMLHttpRequest.UNSENT;this.dispatchEvent(new sinon.Event(gG,false,false,this));this.upload.dispatchEvent(new sinon.Event(gG,false,false,this));if(typeof this.onerror===dg){this.onerror();};},getResponseHeader:function vi(vh){if(this.readyState<tW.HEADERS_RECEIVED){return null;};if(/^Set-Cookie2?$/i.test(vh)){return null;};vh=vh.toLowerCase();for(var h in this.responseHeaders){if(h.toLowerCase()==vh){return this.responseHeaders[h];};};return null;},getAllResponseHeaders:function vk(){if(this.readyState<tW.HEADERS_RECEIVED){return fu;};var vj=fu;for(var vl in this.responseHeaders){if(this.responseHeaders.hasOwnProperty(vl)&&!/^Set-Cookie2?$/i.test(vl)){vj+=vl+gI+this.responseHeaders[vl]+t;};};return vj;},setResponseBody:function vo(vm){ue(this);ua(this);tS(vm);var vq=this.chunkSize||10;var vn=0;this.responseText=fu;do {if(this.async){this.readyStateChange(tW.LOADING);};this.responseText+=vm.substring(vn,vn+vq);vn+=vq;}while(vn<vm.length);var vp=this.getResponseHeader(bV);if(this.responseText&&(!vp||/(text\/xml)|(application\/xml)|(\+xml)/.test(vp))){try{this.responseXML=tW.parseXML(this.responseText);}catch(e){};};if(this.async){this.readyStateChange(tW.DONE);}else {this.readyState=tW.DONE;};},respond:function vr(status,vt,vs){this.status=typeof status==cu?status:200;this.statusText=tW.statusCodes[this.status];this.setResponseHeaders(vt||{});this.setResponseBody(vs||fu);},uploadProgress:function vu(vv){if(ud){this.upload.dispatchEvent(new sinon.ProgressEvent(br,vv));};},uploadError:function vw(vx){if(tQ){this.upload.dispatchEvent(new sinon.CustomEvent(bo,{"detail":vx}));};}});sinon.extend(tW,{UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4});tW.parseXML=function vA(vy){var vB;if(typeof DOMParser!=bX){var vz=new DOMParser();vB=vz.parseFromString(vy,eD);}else {vB=new ActiveXObject(hp);vB.async=hm;vB.loadXML(vy);};return vB;};tW.statusCodes={'100':dt,'101':dO,'200':ep,'201':gB,'202':gU,'203':fG,'204':gx,'205':fC,'206':gT,'300':dp,'301':dn,'302':bB,'303':gS,'304':dB,'305':bt,'307':bO,'400':ck,'401':cl,'402':S,'403':bx,'404':bP,'405':bH,'406':w,'407':dW,'408':bA,'409':fU,'410':ci,'411':fJ,'412':bq,'413':cv,'414':gr,'415':cF,'416':I,'417':cg,'422':dH,'500':hd,'501':fH,'502':fr,'503':fP,'504':eI,'505':hs};sinon.useFakeXMLHttpRequest=function(){sinon.FakeXMLHttpRequest.restore=function vC(vD){if(tR.supportsXHR){tT.XMLHttpRequest=tR.GlobalXMLHttpRequest;};if(tR.supportsActiveX){tT.ActiveXObject=tR.GlobalActiveXObject;};delete sinon.FakeXMLHttpRequest.restore;if(vD!==true){delete sinon.FakeXMLHttpRequest.onCreate;};};if(tR.supportsXHR){tT.XMLHttpRequest=sinon.FakeXMLHttpRequest;};if(tR.supportsActiveX){tT.ActiveXObject=function ActiveXObject(vE){if(vE==hl||/^Msxml2\.XMLHTTP/i.test(vE)){return new sinon.FakeXMLHttpRequest();};return new tR.GlobalActiveXObject(vE);};};return sinon.FakeXMLHttpRequest;};sinon.FakeXMLHttpRequest=tW;})(typeof global===gV?global:this);if(typeof module!==eL&&module.exports){module.exports=sinon;};if(typeof sinon==bX){var sinon={};};sinon.fakeServer=(function(){var vK=[].push;function F(){};function vI(vN){F.prototype=vN;return new F();};function vH(vO){var vP=vO;if(Object.prototype.toString.call(vO)!=dT){vP=[200,{},vO];};if(typeof vP[2]!=gf){throw new TypeError(fK+typeof vP[2]);};return vP;};var vL=typeof window!==bX?window.location:{};var vF=new RegExp(gy+vL.protocol+fQ+vL.host);function vJ(vU,vT,vW){var vS=vU.method;var vQ=!vS||vS.toLowerCase()==vT.toLowerCase();var vR=vU.url;var vV=!vR||vR==vW||(typeof vR.test==dg&&vR.test(vW));return vQ&&vV;};function vM(wc,wa){var wb=wa.url;if(!/^https?:\/\//.test(wb)||vF.test(wb)){wb=wb.replace(vF,fu);};if(vJ(wc,this.getHTTPMethod(wa),wb)){if(typeof wc.response==dg){var vX=wc.url;var vY=[wa].concat(vX&&typeof vX.exec==dg?vX.exec(wb).slice(1):[]);return wc.response.apply(wc,vY);};return true;};return false;};function vG(we,wd){var wf;wf=eX+sinon.format(wd)+dh;wf+=fB+sinon.format(we)+dh;sinon.log(wf);};return {create:function(){var wg=vI(this);this.xhr=sinon.useFakeXMLHttpRequest();wg.requests=[];this.xhr.onCreate=function(wh){wg.addRequest(wh);};return wg;},addRequest:function wj(wi){var wk=this;vK.call(this.requests,wi);wi.onSend=function(){wk.handleRequest(this);if(wk.autoRespond&&!wk.responding){setTimeout(function(){wk.responding=false;wk.respond();},wk.autoRespondAfter||10);wk.responding=true;};};},getHTTPMethod:function wm(wn){if(this.fakeHTTPMethods&&/post/i.test(wn.method)){var wl=(wn.requestBody||fu).match(/_method=([^\b;]+)/);return !!wl?wl[1]:wn.method;};return wn.method;},handleRequest:function wp(wo){if(wo.async){if(!this.queue){this.queue=[];};vK.call(this.queue,wo);}else {this.processRequest(wo);};},respondWith:function wr(wt,ws,wq){if(arguments.length==1&&typeof wt!=dg){this.response=vH(wt);return;};if(!this.responses){this.responses=[];};if(arguments.length==1){wq=wt;ws=wt=null;};if(arguments.length==2){wq=ws;ws=wt;wt=null;};vK.call(this.responses,{method:wt,url:ws,response:typeof wq==dg?wq:vH(wq)});},respond:function wu(){if(arguments.length>0)this.respondWith.apply(this,arguments);var wv=this.queue||[];var ww=wv.splice(0,wv.length);var wx;while(wx=ww.shift()){this.processRequest(wx);};},processRequest:function wy(wz){try{if(wz.aborted){return;};var wA=this.response||[404,{},fu];if(this.responses){for(var l=this.responses.length,i=l-1;i>=0;i-- ){if(vM.call(this,this.responses[i],wz)){wA=this.responses[i].response;break;};};};if(wz.readyState!=4){vG(wA,wz);wz.respond(wA[0],wA[1],wA[2]);};}catch(e){sinon.logError(gm,e);};},restore:function wB(){return this.xhr.restore&&this.xhr.restore.apply(this.xhr,arguments);}};}());if(typeof module!==eL&&module.exports){module.exports=sinon;};(function(){function wC(){};wC.prototype=sinon.fakeServer;sinon.fakeServerWithClock=new wC();sinon.fakeServerWithClock.addRequest=function wG(wD){if(wD.async){if(typeof setTimeout.clock==gV){this.clock=setTimeout.clock;}else {this.clock=sinon.useFakeTimers();this.resetClock=true;};if(!this.longestTimeout){var wE=this.clock.setTimeout;var wF=this.clock.setInterval;var wH=this;this.clock.setTimeout=function(wI,wJ){wH.longestTimeout=Math.max(wJ,wH.longestTimeout||0);return wE.apply(this,arguments);};this.clock.setInterval=function(wK,wL){wH.longestTimeout=Math.max(wL,wH.longestTimeout||0);return wF.apply(this,arguments);};};};return sinon.fakeServer.addRequest.call(this,wD);};sinon.fakeServerWithClock.respond=function wM(){var wN=sinon.fakeServer.respond.apply(this,arguments);if(this.clock){this.clock.tick(this.longestTimeout||0);this.longestTimeout=0;if(this.resetClock){this.clock.restore();this.resetClock=false;};};return wN;};sinon.fakeServerWithClock.restore=function wO(){if(this.clock){this.clock.restore();};return sinon.fakeServer.restore.apply(this,arguments);};}());if(typeof module!==eL&&module.exports){var sinon=require(ce);sinon.extend(sinon,require(bs));};(function(){var wP=[].push;function wQ(wS,wT,wU,wV){if(!wV){return;};if(wT.injectInto&&!(wU in wT.injectInto)){wT.injectInto[wU]=wV;wS.injectedKeys.push(wU);}else {wP.call(wS.args,wV);};};function wR(wX){var wW=sinon.create(sinon.sandbox);if(wX.useFakeServer){if(typeof wX.useFakeServer==gV){wW.serverPrototype=wX.useFakeServer;};wW.useFakeServer();};if(wX.useFakeTimers){if(typeof wX.useFakeTimers==gV){wW.useFakeTimers.apply(wW,wX.useFakeTimers);}else {wW.useFakeTimers();};};return wW;};sinon.sandbox=sinon.extend(sinon.create(sinon.collection),{useFakeTimers:function wY(){this.clock=sinon.useFakeTimers.apply(sinon,arguments);return this.add(this.clock);},serverPrototype:sinon.fakeServer,useFakeServer:function xa(){var xb=this.serverPrototype||sinon.fakeServer;if(!xb||!xb.create){return null;};this.server=xb.create();return this.add(this.server);},inject:function(xc){sinon.collection.inject.call(this,xc);if(this.clock){xc.clock=this.clock;};if(this.server){xc.server=this.server;xc.requests=this.server.requests;};return xc;},restore:function(){sinon.collection.restore.apply(this,arguments);this.restoreContext();},restoreContext:function(){if(this.injectedKeys){for(var i=0,j=this.injectedKeys.length;i<j;i++ ){delete this.injectInto[this.injectedKeys[i]];};this.injectedKeys=[];};},create:function(xh){if(!xh){return sinon.create(sinon.sandbox);};var xg=wR(xh);xg.args=xg.args||[];xg.injectedKeys=[];xg.injectInto=xh.injectInto;var xf,xe,xd=xg.inject({});if(xh.properties){for(var i=0,l=xh.properties.length;i<l;i++ ){xf=xh.properties[i];xe=xd[xf]||xf==fE&&xg;wQ(xg,xh,xf,xe);};}else {wQ(xg,xh,fE,xe);};return xg;}});sinon.sandbox.useFakeXMLHttpRequest=sinon.sandbox.useFakeServer;if(typeof module!==eL&&module.exports){module.exports=sinon.sandbox;};}());(function(xk){var xi=typeof module!==eL&&module.exports;if(!xk&&xi){xk=require(ce);};if(!xk){return;};function xj(xl){var xm=typeof xl;if(xm!=dg){throw new TypeError(ew+xm);};return function(){var xp=xk.getConfig(xk.config);xp.injectInto=xp.injectIntoThis&&this||xp.injectInto;var xq=xk.sandbox.create(xp);var xn,xr;var xo=Array.prototype.slice.call(arguments).concat(xq.args);try{xr=xl.apply(this,xo);}catch(e){xn=e;};if(typeof xn!==bX){xq.restore();throw xn;}else {xq.verifyAndRestore();};return xr;};};xj.config={injectIntoThis:true,injectInto:null,properties:[eJ,cS,db,P,dv,gD],useFakeTimers:true,useFakeServer:true};if(xi){module.exports=xj;}else {xk.test=xj;};}(typeof sinon==gV&&sinon||null));(function(xv){var xs=typeof module!==eL&&module.exports;if(!xv&&xs){xv=require(ce);};if(!xv||!Object.prototype.hasOwnProperty){return;};function xu(xy,xx,xw){return function(){if(xx){xx.apply(this,arguments);};var xz,xA;try{xA=xy.apply(this,arguments);}catch(e){xz=e;};if(xw){xw.apply(this,arguments);};if(xz){throw xz;};return xA;};};function xt(xB,xH){if(!xB||typeof xB!=gV){throw new TypeError(bc);};xH=xH||fT;var xE=new RegExp(gy+xH);var xC={},xG,xI,xJ;var xF=xB.setUp;var xD=xB.tearDown;for(xG in xB){if(xB.hasOwnProperty(xG)){xI=xB[xG];if(/^(setUp|tearDown)$/.test(xG)){continue;};if(typeof xI==dg&&xE.test(xG)){xJ=xI;if(xF||xD){xJ=xu(xI,xF,xD);};xC[xG]=xv.test(xJ);}else {xC[xG]=xB[xG];};};};return xC;};if(xs){module.exports=xt;}else {xv.testCase=xt;};}(typeof sinon==gV&&sinon||null));(function(xR,xO){var xK=typeof module!==bX&&module.exports;var xL=Array.prototype.slice;var xP;if(!xR&&xK){xR=require(ce);};if(!xR){return;};function xQ(){var xT;for(var i=0,l=arguments.length;i<l; ++i){xT=arguments[i];if(!xT){xP.fail(es);};if(typeof xT!=dg){xP.fail(xT+dJ);};if(typeof xT.getCall!=dg){xP.fail(xT+cj);};};};function xM(xV,xU){xV=xV||xO;var xW=xV.fail||xP.fail;xW.call(xV,xU);};function xS(name,xY,xX){if(arguments.length==2){xX=xY;xY=name;};xP[name]=function(yc){xQ(yc);var yb=xL.call(arguments,1);var ya=false;if(typeof xY==dg){ya=!xY(yc);}else {ya=typeof yc[xY]==dg?!yc[xY].apply(yc,yb):!yc[xY];};if(ya){xM(this,yc.printf.apply(yc,[xX].concat(yb)));}else {xP.pass(name);};};};function xN(yd,ye){return !yd||/^fail/.test(ye)?ye:yd+ye.slice(0,1).toUpperCase()+ye.slice(1);};xP={failException:cX,fail:function yf(yg){var yh=new Error(yg);yh.name=this.failException||xP.failException;throw yh;},pass:function yj(yi){},callOrder:function ym(){xQ.apply(null,arguments);var yk=fu,yl=fu;if(!xR.calledInOrder(arguments)){try{yk=[].join.call(arguments,ef);var yn=xL.call(arguments);var i=yn.length;while(i){if(!yn[ --i].called){yn.splice(i,1);};};yl=xR.orderByFirstCall(yn).join(ef);}catch(e){};xM(this,dI+yk+fR+bG+yl);}else {xP.pass(gz);};},callCount:function yo(yq,yp){xQ(yq);if(yq.callCount!=yp){var yr=ho+xR.timesInWords(yp)+ed;xM(this,yq.printf(yr));}else {xP.pass(eQ);};},expose:function yu(yt,yv){if(!yt){throw new TypeError(eO);};var o=yv||{};var yw=typeof o.prefix==bX&&ds||o.prefix;var ys=typeof o.includeFail==bX||!!o.includeFail;for(var yx in this){if(yx!=dA&&(ys||!/^(fail)/.test(yx))){yt[xN(yw,yx)]=this[yx];};};return yt;},match:function yB(yz,yA){var yy=xR.match(yA);if(yy.test(yz)){xP.pass(C);}else {var yC=[dl,dC+xR.format(yA),dL+xR.format(yz)];xM(this,yC.join(em));};}};xS(bd,gp);xS(D,function(yD){return !yD.called;},fz);xS(Q,er);xS(gF,cp);xS(eP,V);xS(eF,dX);xS(cC,N);xS(eK,bQ);xS(fW,cV);xS(bl,eA);xS(U,cw);xS(gt,fa);xS(gv,g);xS(eb,hf);xS(bT,bD);xS(dY,eC);xS(gd,bC);xS(dK,dr);xS(fo,eS);if(xK){module.exports=xP;}else {xR.assert=xP;};}(typeof sinon==gV&&sinon||null,typeof window!=bX?window:(typeof self!=bX)?self:global));return sinon;}.call(typeof window!=eL&&window||{}));this.sinon.assert.fail=function(yE){this.fail(yE,true);};this.sinon.FakeXMLHttpRequest.statusCodes[207]=cW;var origSinon=this.sinon;var Sinon=qx.dev.unit.Sinon;Sinon.getSinon=function(){return origSinon;};}).call(this);})();(function(){var a="tap",b="swipe",c="qx.module.event.Touch",d="longtap",e="dbltap";qx.Bootstrap.define(c,{statics:{TYPES:[a,d,b,e],normalize:function(event,g,f){if(!event){return event;};event._type=f;return event;}},defer:function(h){qxWeb.$registerEventNormalization(h.TYPES,h.normalize);}});})();(function(){var a="([^\/]+)",b="^",c="$",d="qx.event.Messaging",e="No listener found for ",f="any";qx.Bootstrap.define(d,{construct:function(){this._listener={},this.__listenerIdCount=0;this.__channelToIdMapping={};},members:{_listener:null,__listenerIdCount:null,__channelToIdMapping:null,on:function(k,j,h,g){return this._addListener(k,j,h,g);},onAny:function(n,m,l){return this._addListener(f,n,m,l);},_addListener:function(w,u,r,t){var q=this._listener[w]=this._listener[w]||{};var p=this.__listenerIdCount++ ;var s=[];var o=null;if(qx.lang.Type.isString(u)){var v=/\{([\w\d]+)\}/g;while((o=v.exec(u))!==null){s.push(o[1]);};u=new RegExp(b+u.replace(v,a)+c);};q[p]={regExp:u,params:s,handler:r,scope:t};this.__channelToIdMapping[p]=w;return p;},remove:function(y){var z=this.__channelToIdMapping[y];var x=this._listener[z];delete x[y];delete this.__channelToIdMapping[y];},emit:function(D,A,C,B){this._emit(D,A,C,B);},_emit:function(K,J,I,E){var F=false;var H=this._listener[f];F=this._emitListeners(K,J,H,I,E);var G=false;H=this._listener[K];G=this._emitListeners(K,J,H,I,E);if(!G&&!F){qx.Bootstrap.info(e+J);};},_emitListeners:function(S,Q,O,P,L){if(!O||qx.lang.Object.isEmpty(O)){return false;};var M=false;for(var R in O){var N=O[R];M|=this._emitRoute(S,Q,N,P,L);};return M;},_emitRoute:function(bb,Y,V,W,T){var ba=V.regExp.exec(Y);if(ba){var W=W||{};var U=null;var X=null;ba.shift();for(var i=0;i<ba.length;i++ ){X=ba[i];U=V.params[i];if(U){W[U]=X;}else {W[i]=X;};};V.handler.call(V.scope,{path:Y,params:W,customData:T});};return ba!=undefined;}}});})();(function(){var a="qx.module.Messaging";qx.Bootstrap.define(a,{statics:{on:null,onAny:null,remove:null,emit:null},defer:function(b){qxWeb.$attachStatic({"messaging":new qx.event.Messaging()});}});})();(function(){var a="qx.module.Attribute",b="html";qx.Bootstrap.define(a,{members:{getHtml:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Attribute.get(this[0],b);};return null;},setHtml:function(c){c=qx.bom.Html.fixEmptyTags(c);this._forEachElement(function(d){qx.bom.element.Attribute.set(d,b,c);});return this;},setAttribute:function(name,e){this._forEachElement(function(f){qx.bom.element.Attribute.set(f,name,e);});return this;},getAttribute:function(name){if(this[0]&&this[0].nodeType===1){return qx.bom.element.Attribute.get(this[0],name);};return null;},removeAttribute:function(name){this._forEachElement(function(g){qx.bom.element.Attribute.set(g,name,null);});return this;},setAttributes:function(h){for(var name in h){this.setAttribute(name,h[name]);};return this;},getAttributes:function(k){var j={};for(var i=0;i<k.length;i++ ){j[k[i]]=this.getAttribute(k[i]);};return j;},removeAttributes:function(m){for(var i=0,l=m.length;i<l;i++ ){this.removeAttribute(m[i]);};return this;},setProperty:function(name,n){for(var i=0;i<this.length;i++ ){this[i][name]=n;};return this;},getProperty:function(name){if(this[0]){return this[0][name];};return null;},setProperties:function(o){for(var name in o){this.setProperty(name,o[name]);};return this;},removeProperties:function(p){for(var i=0;i<p.length;i++ ){this.removeProperty(p[i]);};return this;},getProperties:function(q){var r={};for(var i=0;i<q.length;i++ ){r[q[i]]=this.getProperty(q[i]);};return r;},removeProperty:function(name){if(this[0]){this[0][name]=undefined;};return this;},getValue:function(){if(this[0]&&this[0].nodeType===1){return qx.bom.Input.getValue(this[0]);};return null;},setValue:function(s){this._forEachElement(function(t){qx.bom.Input.setValue(t,s);});return this;}},defer:function(u){qxWeb.$attachAll(this);}});})();(function(){var a="<fieldset>",b="<select multiple='multiple'>",c="</div>",d="</select>",e="</tr></tbody></table>",f="<col",g="div",h="<table><tbody><tr>",k="string",m=">",n="script",o="<table><tbody></tbody><colgroup>",p="<th",q="</tbody></table>",r="<td",s="</colgroup></table>",t="<opt",u="text/javascript",v="",w="<table>",x="</fieldset>",y="<table><tbody>",z="div<div>",A="<table",B="mshtml",C="engine.name",D="qx.bom.Html",E="<leg",F="tbody",G="<tr",H="</table>",I="undefined",J="></";qx.Bootstrap.define(D,{statics:{__fixNonDirectlyClosableHelper:function(L,K,M){return M.match(/^(abbr|br|col|img|input|link|meta|param|hr|area|embed)$/i)?L:K+J+M+m;},__convertMap:{opt:[1,b,d],leg:[1,a,x],table:[1,w,H],tr:[2,y,q],td:[3,h,e],col:[2,o,s],def:qx.core.Environment.select(C,{"mshtml":[1,z,c],"default":null})},fixEmptyTags:function(N){return N.replace(/(<(\w+)[^>]*?)\/>/g,this.__fixNonDirectlyClosableHelper);},__convertHtmlString:function(S,T){var V=T.createElement(g);S=qx.bom.Html.fixEmptyTags(S);var P=S.replace(/^\s+/,v).substring(0,5).toLowerCase();var U,O=this.__convertMap;if(!P.indexOf(t)){U=O.opt;}else if(!P.indexOf(E)){U=O.leg;}else if(P.match(/^<(thead|tbody|tfoot|colg|cap)/)){U=O.table;}else if(!P.indexOf(G)){U=O.tr;}else if(!P.indexOf(r)||!P.indexOf(p)){U=O.td;}else if(!P.indexOf(f)){U=O.col;}else {U=O.def;};if(U){V.innerHTML=U[1]+S+U[2];var R=U[0];while(R-- ){V=V.lastChild;};}else {V.innerHTML=S;};if((qx.core.Environment.get(C)==B)){var W=/<tbody/i.test(S);var Q=!P.indexOf(A)&&!W?V.firstChild&&V.firstChild.childNodes:U[1]==w&&!W?V.childNodes:[];for(var j=Q.length-1;j>=0; --j){if(Q[j].tagName.toLowerCase()===F&&!Q[j].childNodes.length){Q[j].parentNode.removeChild(Q[j]);};};if(/^\s/.test(S)){V.insertBefore(T.createTextNode(S.match(/^\s*/)[0]),V.firstChild);};};return qx.lang.Array.fromCollection(V.childNodes);},clean:function(X,bc,ba){bc=bc||document;if(typeof bc.createElement===I){bc=bc.ownerDocument||bc[0]&&bc[0].ownerDocument||document;};if(!ba&&X.length===1&&typeof X[0]===k){var bd=/^<(\w+)\s*\/?>$/.exec(X[0]);if(bd){return [bc.createElement(bd[1])];};};var Y,bb=[];for(var i=0,l=X.length;i<l;i++ ){Y=X[i];if(typeof Y===k){Y=this.__convertHtmlString(Y,bc);};if(Y.nodeType){bb.push(Y);}else if(Y instanceof qx.type.BaseArray||(typeof qxWeb!==I&&Y instanceof qxWeb)){bb.push.apply(bb,Array.prototype.slice.call(Y,0));}else if(Y.toElement){bb.push(Y.toElement());}else {bb.push.apply(bb,Y);};};if(ba){return qx.bom.Html.extractScripts(bb,ba);};return bb;},extractScripts:function(bh,bf){var bi=[],bg;for(var i=0;bh[i];i++ ){bg=bh[i];if(bg.nodeType==1&&bg.tagName.toLowerCase()===n&&(!bg.type||bg.type.toLowerCase()===u)){if(bg.parentNode){bg.parentNode.removeChild(bh[i]);};bi.push(bg);}else {if(bg.nodeType===1){var be=qx.lang.Array.fromCollection(bg.getElementsByTagName(n));bh.splice.apply(bh,[i+1,0].concat(be));};if(bf){bf.appendChild(bg);};};};return bi;}}});})();(function(){var a="text",b="engine.name",c="",d="mshtml",e="number",f="checkbox",g="select-one",h="option",j="value",k="select",m="radio",n="qx.bom.Input",o="textarea";qx.Bootstrap.define(n,{statics:{setValue:function(u,t){var v=u.nodeName.toLowerCase();var q=u.type;var Array=qx.lang.Array;var w=qx.lang.Type;if(typeof t===e){t+=c;};if((q===f||q===m)){if(w.isArray(t)){u.checked=Array.contains(t,u.value);}else {u.checked=u.value==t;};}else if(v===k){var p=w.isArray(t);var x=u.options;var r,s;for(var i=0,l=x.length;i<l;i++ ){r=x[i];s=r.getAttribute(j);if(s==null){s=r.text;};r.selected=p?Array.contains(t,s):t==s;};if(p&&t.length==0){u.selectedIndex=-1;};}else if((q===a||q===o)&&(qx.core.Environment.get(b)==d)){u.$$inValueSet=true;u.value=t;u.$$inValueSet=null;}else {u.value=t;};},getValue:function(F){var D=F.nodeName.toLowerCase();if(D===h){return (F.attributes.value||{}).specified?F.value:F.text;};if(D===k){var y=F.selectedIndex;if(y<0){return null;};var E=[];var H=F.options;var C=F.type==g;var G=qx.bom.Input;var B;for(var i=C?y:0,A=C?y+1:H.length;i<A;i++ ){var z=H[i];if(z.selected){B=G.getValue(z);if(C){return B;};E.push(B);};};return E;}else {return (F.value||c).replace(/\r/g,c);};}}});})();(function(){var a="qx.module.event.Rotate",b="getAngle",c="function",d="rotate";qx.Bootstrap.define(a,{statics:{TYPES:[d],BIND_METHODS:[b],getAngle:function(){return this._original.angle;},normalize:function(event,f){if(!event){return event;};var e=qx.module.event.Rotate.BIND_METHODS;for(var i=0,l=e.length;i<l;i++ ){if(typeof event[e[i]]!=c){event[e[i]]=qx.module.event.Rotate[e[i]].bind(event);};};return event;}},defer:function(g){qxWeb.$registerEventNormalization(qx.module.event.Rotate.TYPES,g.normalize);}});})();(function(){var a="function",b="getButton",c="mousedown",d="getScreenLeft",e="ie",f="mouseout",g="browser.name",h="dblclick",j="qx.module.event.Mouse",k="mousemove",m="middle",n="browser.documentmode",o="mouseover",p="mouseup",q="getDocumentLeft",r="getViewportLeft",s="right",t="click",u="getViewportTop",v="none",w="contextmenu",x="getScreenTop",y="left",z="getDocumentTop";qx.Bootstrap.define(j,{statics:{TYPES:[t,h,c,p,o,k,f],BIND_METHODS:[b,r,u,q,z,d,x],BUTTONS_DOM2:{'0':y,'2':s,'1':m},BUTTONS_MSHTML:{'1':y,'2':s,'4':m},getButton:function(){switch(this.type){case w:return s;case t:if(qxWeb.env.get(g)===e&&qxWeb.env.get(n)<9){return y;};default:if(this.target!==undefined){return qx.module.event.Mouse.BUTTONS_DOM2[this.button]||v;}else {return qx.module.event.Mouse.BUTTONS_MSHTML[this.button]||v;};};},getViewportLeft:function(){return this.clientX;},getViewportTop:function(){return this.clientY;},getDocumentLeft:function(){if(this.pageX!==undefined){return this.pageX;}else {var A=qx.dom.Node.getWindow(this.srcElement);return this.clientX+qx.bom.Viewport.getScrollLeft(A);};},getDocumentTop:function(){if(this.pageY!==undefined){return this.pageY;}else {var B=qx.dom.Node.getWindow(this.srcElement);return this.clientY+qx.bom.Viewport.getScrollTop(B);};},getScreenLeft:function(){return this.screenX;},getScreenTop:function(){return this.screenY;},normalize:function(event,D){if(!event){return event;};var C=qx.module.event.Mouse.BIND_METHODS;for(var i=0,l=C.length;i<l;i++ ){if(typeof event[C[i]]!=a){event[C[i]]=qx.module.event.Mouse[C[i]].bind(event);};};return event;}},defer:function(E){qxWeb.$registerEventNormalization(qx.module.event.Mouse.TYPES,E.normalize);}});})();(function(){var a="qx.dom.Hierarchy",b="previousSibling",c="nextSibling",d="html.element.contains",e="html.element.compareDocumentPosition";qx.Bootstrap.define(a,{statics:{getNextElementSibling:function(f){while(f&&(f=f.nextSibling)&&!qx.dom.Node.isElement(f)){continue;};return f||null;},getPreviousElementSibling:function(g){while(g&&(g=g.previousSibling)&&!qx.dom.Node.isElement(g)){continue;};return g||null;},contains:function(j,i){if(qx.core.Environment.get(d)){if(qx.dom.Node.isDocument(j)){var h=qx.dom.Node.getDocument(i);return j&&h==j;}else if(qx.dom.Node.isDocument(i)){return false;}else {return j.contains(i);};}else if(qx.core.Environment.get(e)){return !!(j.compareDocumentPosition(i)&16);}else {while(i){if(j==i){return true;};i=i.parentNode;};return false;};},isRendered:function(l){var k=l.ownerDocument||l.document;if(qx.core.Environment.get(d)){if(!l.parentNode){return false;};return k.body.contains(l);}else if(qx.core.Environment.get(e)){return !!(k.compareDocumentPosition(l)&16);}else {while(l){if(l==k.body){return true;};l=l.parentNode;};return false;};},getChildElements:function(n){n=n.firstChild;if(!n){return [];};var m=this.getNextSiblings(n);if(n.nodeType===1){m.unshift(n);};return m;},getPreviousSiblings:function(o){return this._recursivelyCollect(o,b);},getNextSiblings:function(p){return this._recursivelyCollect(p,c);},_recursivelyCollect:function(s,q){var r=[];while(s=s[q]){if(s.nodeType==1){r.push(s);};};return r;},getSiblings:function(t){return this.getPreviousSiblings(t).reverse().concat(this.getNextSiblings(t));}}});})();(function(){var a="html.node.isequalnode",b="namespaceURI",c="nodeValue",d="nodeType",e="qx.module.Traversing",f="getNextSiblings",g="prefix",h="nodeName",k="getPreviousSiblings",n="getSiblings",o="localName",p="length",q="string";qx.Bootstrap.define(e,{statics:{EQUALITY_ATTRIBUTES:[d,h,o,b,g,c],__getAncestors:function(s,t){var r=[];for(var i=0;i<this.length;i++ ){var parent=qx.dom.Element.getParentElement(this[i]);while(parent){var u=[parent];if(s&&qx.bom.Selector.matches(s,u).length>0){break;};if(t){u=qx.bom.Selector.matches(t,u);};r=r.concat(u);parent=qx.dom.Element.getParentElement(parent);};};return qxWeb.$init(r,qxWeb);},__getElementFromArgument:function(v){if(v instanceof qxWeb){return v[0];}else if(qx.Bootstrap.isString(v)){return qxWeb(v)[0];};return v;},__getNodeFromArgument:function(w){if(typeof w==q){w=qxWeb(w);};if(w instanceof Array||w instanceof qxWeb){w=w[0];};return qxWeb.isNode(w)?w:null;},__getAttributes:function(x){var y={};for(var z in x.attributes){if(z==p){continue;};var name=x.attributes[z].name;var A=x.attributes[z].value;y[name]=A;};return y;},__hierarchyHelper:function(D,G,E){var B=[];var C=qx.dom.Hierarchy;for(var i=0,l=D.length;i<l;i++ ){B.push.apply(B,C[G](D[i]));};var F=qx.lang.Array.unique(B);if(E){F=qx.bom.Selector.matches(E,F);};return F;},isElement:function(H){return qx.dom.Node.isElement(qx.module.Traversing.__getElementFromArgument(H));},isNode:function(I){return qx.dom.Node.isNode(qx.module.Traversing.__getElementFromArgument(I));},isNodeName:function(K,J){return qx.dom.Node.isNodeName(qx.module.Traversing.__getElementFromArgument(K),J);},isDocument:function(L){if(L instanceof qxWeb){L=L[0];};return qx.dom.Node.isDocument(L);},isDocumentFragment:function(M){if(M instanceof qxWeb){M=M[0];};return qx.dom.Node.isDocumentFragment(M);},getWindow:function(N){return qx.dom.Node.getWindow(qx.module.Traversing.__getElementFromArgument(N));},isTextNode:function(O){return qx.dom.Node.isText(O);},isWindow:function(P){if(P instanceof qxWeb){P=P[0];};return qx.dom.Node.isWindow(P);},getDocument:function(Q){return qx.dom.Node.getDocument(qx.module.Traversing.__getElementFromArgument(Q));},getNodeName:function(R){return qx.dom.Node.getName(qx.module.Traversing.__getElementFromArgument(R));},getNodeText:function(S){return qx.dom.Node.getText(qx.module.Traversing.__getElementFromArgument(S));},isBlockNode:function(T){return qx.dom.Node.isBlockNode(qx.module.Traversing.__getElementFromArgument(T));},equalNodes:function(bd,bf){bd=qx.module.Traversing.__getNodeFromArgument(bd);bf=qx.module.Traversing.__getNodeFromArgument(bf);if(!bd||!bf){return false;};if(qx.core.Environment.get(a)){return bd.isEqualNode(bf);}else {if(bd===bf){return true;};var bc=bd.attributes&&bf.attributes;if(bc&&bd.attributes.length!==bf.attributes.length){return false;};var bb=bd.childNodes&&bf.childNodes;if(bb&&bd.childNodes.length!==bf.childNodes.length){return false;};var ba=qx.module.Traversing.EQUALITY_ATTRIBUTES;for(var i=0,l=ba.length;i<l;i++ ){var Y=ba[i];if(bd[Y]!==bf[Y]){return false;};};if(bc){var U=qx.module.Traversing.__getAttributes(bd);var be=qx.module.Traversing.__getAttributes(bf);for(var W in U){if(U[W]!==be[W]){return false;};};};if(bb){for(var j=0,m=bd.childNodes.length;j<m;j++ ){var X=bd.childNodes[j];var V=bf.childNodes[j];if(!qx.module.Traversing.equalNodes(X,V)){return false;};};};return true;};}},members:{add:function(bg){if(bg instanceof qxWeb){bg=bg[0];};if(qx.module.Traversing.isElement(bg)||qx.module.Traversing.isDocument(bg)||qx.module.Traversing.isWindow(bg)||qx.module.Traversing.isDocumentFragment(bg)){this.push(bg);};return this;},getChildren:function(bj){var bi=[];for(var i=0;i<this.length;i++ ){var bh=qx.dom.Hierarchy.getChildElements(this[i]);if(bj){bh=qx.bom.Selector.matches(bj,bh);};bi=bi.concat(bh);};return qxWeb.$init(bi,qxWeb);},forEach:function(bl,bk){for(var i=0;i<this.length;i++ ){bl.call(bk,this[i],i,this);};return this;},getParents:function(bo){var bn=[];for(var i=0;i<this.length;i++ ){var bm=qx.dom.Element.getParentElement(this[i]);if(bo){bm=qx.bom.Selector.matches(bo,[bm]);};bn=bn.concat(bm);};return qxWeb.$init(bn,qxWeb);},isChildOf:function(parent){if(this.length==0){return false;};var bp=null,bs=qxWeb(parent),bq=false;for(var i=0,l=this.length;i<l&&!bq;i++ ){bp=qxWeb(this[i]).getAncestors();for(var j=0,br=bs.length;j<br;j++ ){if(bp.indexOf(bs[j])!=-1){bq=true;break;};};};return bq;},getAncestors:function(bt){return this.__getAncestors(null,bt);},getAncestorsUntil:function(bv,bu){return this.__getAncestors(bv,bu);},getClosest:function(by){var bx=[];var bw=function(bz){var bA=qx.bom.Selector.matches(by,bz);if(bA.length){bx.push(bA[0]);}else {bz=bz.getParents();if(bz[0]&&bz[0].parentNode){bw(bz);};};};for(var i=0;i<this.length;i++ ){bw(qxWeb(this[i]));};return qxWeb.$init(bx,qxWeb);},find:function(bC){var bB=[];for(var i=0;i<this.length;i++ ){bB=bB.concat(qx.bom.Selector.query(bC,this[i]));};return qxWeb.$init(bB,qxWeb);},getContents:function(){var bD=[];this._forEachElement(function(bE){bD=bD.concat(qx.lang.Array.fromCollection(bE.childNodes));});return qxWeb.$init(bD,qxWeb);},is:function(bF){if(qx.lang.Type.isFunction(bF)){return this.filter(bF).length>0;};return !!bF&&qx.bom.Selector.matches(bF,this).length>0;},eq:function(bG){return this.slice(bG,+bG+1);},getFirst:function(){return this.slice(0,1);},getLast:function(){return this.slice(this.length-1);},has:function(bI){var bH=[];this._forEachElement(function(bJ,bK){var bL=qx.bom.Selector.matches(bI,this.eq(bK).getContents());if(bL.length>0){bH.push(bJ);};});return qxWeb.$init(bH,this.constructor);},contains:function(bM){if(bM instanceof Array||bM instanceof qxWeb){bM=bM[0];};if(!bM){return qxWeb();};if(qx.dom.Node.isWindow(bM)){bM=bM.document;};return this.filter(function(bN){if(qx.dom.Node.isWindow(bN)){bN=bN.document;};return qx.dom.Hierarchy.contains(bN,bM);});},getNext:function(bP){var bO=this.map(qx.dom.Hierarchy.getNextElementSibling,qx.dom.Hierarchy);if(bP){bO=qxWeb.$init(qx.bom.Selector.matches(bP,bO),qxWeb);};return bO;},getNextAll:function(bR){var bQ=qx.module.Traversing.__hierarchyHelper(this,f,bR);return qxWeb.$init(bQ,qxWeb);},getNextUntil:function(bT){var bS=[];this.forEach(function(bW,bU){var bV=qx.dom.Hierarchy.getNextSiblings(bW);for(var i=0,l=bV.length;i<l;i++ ){if(qx.bom.Selector.matches(bT,[bV[i]]).length>0){break;};bS.push(bV[i]);};});return qxWeb.$init(bS,qxWeb);},getPrev:function(bY){var bX=this.map(qx.dom.Hierarchy.getPreviousElementSibling,qx.dom.Hierarchy);if(bY){bX=qxWeb.$init(qx.bom.Selector.matches(bY,bX),qxWeb);};return bX;},getPrevAll:function(cb){var ca=qx.module.Traversing.__hierarchyHelper(this,k,cb);return qxWeb.$init(ca,qxWeb);},getPrevUntil:function(cd){var cc=[];this.forEach(function(cg,ce){var cf=qx.dom.Hierarchy.getPreviousSiblings(cg);for(var i=0,l=cf.length;i<l;i++ ){if(qx.bom.Selector.matches(cd,[cf[i]]).length>0){break;};cc.push(cf[i]);};});return qxWeb.$init(cc,qxWeb);},getSiblings:function(ci){var ch=qx.module.Traversing.__hierarchyHelper(this,n,ci);return qxWeb.$init(ch,qxWeb);},not:function(ck){if(qx.lang.Type.isFunction(ck)){return this.filter(function(cl,cn,cm){return !ck(cl,cn,cm);});};var cj=qx.bom.Selector.matches(ck,this);return this.filter(function(co){return cj.indexOf(co)===-1;});},getOffsetParent:function(){return this.map(qx.bom.element.Location.getOffsetParent);},isRendered:function(){if(!this[0]){return false;};return qx.dom.Hierarchy.isRendered(this[0]);}},defer:function(cp){qxWeb.$attachAll(this);qxWeb.$attach({"__getAncestors":cp.__getAncestors});}});})();(function(){var a="scrollLeft",b="qx.module.Manipulating",c="ease-in",d="scrollTop";qx.Bootstrap.define(b,{statics:{_animationDescription:{scrollLeft:{duration:700,timing:c,keep:100,keyFrames:{'0':{},'100':{scrollLeft:1}}},scrollTop:{duration:700,timing:c,keep:100,keyFrames:{'0':{},'100':{scrollTop:1}}}},__animateScroll:function(f,g,e){var h=qx.lang.Object.clone(qx.module.Manipulating._animationDescription[f],true);h.keyFrames[100][f]=g;return this.animate(h,e);},__getCollectionFromArgument:function(o){var k;if(qx.lang.Type.isArray(o)){k=qxWeb(o);}else {var n=qx.bom.Html.clean([o]);if(n.length>0&&qx.dom.Node.isElement(n[0])){k=qxWeb(n);}else {k=qxWeb(o);};};return k;},__getInnermostElement:function(p){if(p.childNodes.length==0){return p;};for(var i=0,l=p.childNodes.length;i<l;i++ ){if(p.childNodes[i].nodeType===1){return this.__getInnermostElement(p.childNodes[i]);};};return p;},__getElementArray:function(r){if(!qx.lang.Type.isArray(r)){var q=qxWeb(r);r=q.length>0?q:[r];};return r.filter(function(s){return (s&&(s.nodeType===1||s.nodeType===11));});},create:function(t,u){return qxWeb.$init(qx.bom.Html.clean([t],u),qxWeb);}},members:{clone:function(w){var v=[];for(var i=0;i<this.length;i++ ){if(this[i]&&this[i].nodeType===1){v[i]=this[i].cloneNode(true);};};if(w===true&&this.copyEventsTo){this.copyEventsTo(v);};return qxWeb(v);},append:function(y){var x=qx.bom.Html.clean([y]);var z=qxWeb.$init(x,qxWeb);this._forEachElement(function(A,B){for(var j=0,m=z.length;j<m;j++ ){if(B==0){qx.dom.Element.insertEnd(z[j],A);}else {qx.dom.Element.insertEnd(z.eq(j).clone(true)[0],A);};};});return this;},appendTo:function(parent){parent=qx.module.Manipulating.__getElementArray(parent);for(var i=0,l=parent.length;i<l;i++ ){this._forEachElement(function(C,j){if(i==0){qx.dom.Element.insertEnd(this[j],parent[i]);}else {qx.dom.Element.insertEnd(this.eq(j).clone(true)[0],parent[i]);};});};return this;},insertBefore:function(D){D=qx.module.Manipulating.__getElementArray(D);for(var i=0,l=D.length;i<l;i++ ){this._forEachElement(function(E,F){if(i==0){qx.dom.Element.insertBefore(E,D[i]);}else {qx.dom.Element.insertBefore(this.eq(F).clone(true)[0],D[i]);};});};return this;},insertAfter:function(G){G=qx.module.Manipulating.__getElementArray(G);for(var i=0,l=G.length;i<l;i++ ){for(var j=this.length-1;j>=0;j-- ){if(!this[j]||this[j].nodeType!==1){continue;};if(i==0){qx.dom.Element.insertAfter(this[j],G[i]);}else {qx.dom.Element.insertAfter(this.eq(j).clone(true)[0],G[i]);};};};return this;},wrap:function(H){H=qx.module.Manipulating.__getCollectionFromArgument(H);if(H.length==0){return this;};this._forEachElement(function(I){var J=H.eq(0).clone(true);qx.dom.Element.insertAfter(J[0],I);var K=qx.module.Manipulating.__getInnermostElement(J[0]);qx.dom.Element.insertEnd(I,K);});return this;},remove:function(){this._forEachElement(function(L){qx.dom.Element.remove(L);});return this;},empty:function(){this._forEachElement(function(M){while(M.firstChild){M.removeChild(M.firstChild);};});return this;},before:function(content){if(!qx.lang.Type.isArray(content)){content=[content];};var N=document.createDocumentFragment();qx.bom.Html.clean(content,document,N);this._forEachElement(function(Q,O){var P=qx.lang.Array.cast(N.childNodes,Array);for(var i=0,l=P.length;i<l;i++ ){var R;if(O<this.length-1){R=P[i].cloneNode(true);}else {R=P[i];};Q.parentNode.insertBefore(R,Q);};},this);return this;},after:function(content){if(!qx.lang.Type.isArray(content)){content=[content];};var S=document.createDocumentFragment();qx.bom.Html.clean(content,document,S);this._forEachElement(function(W,T){var V=qx.lang.Array.cast(S.childNodes,Array);for(var i=V.length-1;i>=0;i-- ){var U;if(T<this.length-1){U=V[i].cloneNode(true);}else {U=V[i];};W.parentNode.insertBefore(U,W.nextSibling);};},this);return this;},getScrollLeft:function(){var X=this[0];if(!X){return null;};var Node=qx.dom.Node;if(Node.isWindow(X)||Node.isDocument(X)){return qx.bom.Viewport.getScrollLeft();};return X.scrollLeft;},getScrollTop:function(){var Y=this[0];if(!Y){return null;};var Node=qx.dom.Node;if(Node.isWindow(Y)||Node.isDocument(Y)){return qx.bom.Viewport.getScrollTop();};return Y.scrollTop;},setScrollLeft:function(bb,bc){var Node=qx.dom.Node;if(bc&&qx.bom.element&&qx.bom.element.AnimationJs){qx.module.Manipulating.__animateScroll.bind(this,a,bb,bc)();};for(var i=0,l=this.length,ba;i<l;i++ ){ba=this[i];if(Node.isElement(ba)){if(!(bc&&qx.bom.element&&qx.bom.element.AnimationJs)){ba.scrollLeft=bb;};}else if(Node.isWindow(ba)){ba.scrollTo(bb,this.getScrollTop(ba));}else if(Node.isDocument(ba)){Node.getWindow(ba).scrollTo(bb,this.getScrollTop(ba));};};return this;},setScrollTop:function(be,bf){var Node=qx.dom.Node;if(bf&&qx.bom.element&&qx.bom.element.AnimationJs){qx.module.Manipulating.__animateScroll.bind(this,d,be,bf)();};for(var i=0,l=this.length,bd;i<l;i++ ){bd=this[i];if(Node.isElement(bd)){if(!(bf&&qx.bom.element&&qx.bom.element.AnimationJs)){bd.scrollTop=be;};}else if(Node.isWindow(bd)){bd.scrollTo(this.getScrollLeft(bd),be);}else if(Node.isDocument(bd)){Node.getWindow(bd).scrollTo(this.getScrollLeft(bd),be);};};return this;},focus:function(){try{this[0].focus();}catch(bg){};return this;},blur:function(){this.forEach(function(bh,bi){try{bh.blur();}catch(bj){};});return this;}},defer:function(bk){qxWeb.$attachAll(this);}});})();(function(){var a="text",b="<label>",c="font-size",d="px",e="css.placeholder",f="font-variant",g="inline",h="css.pointerevents",j="auto",k="cursor",l="font-family",m="padding-right",n="text-align",o="placeholder",p="padding-left",q="font-weight",r="#989898",s="$qx_placeholder",t="",u="INPUT",v="TEXTAREA",w="keyup",x="display",y="padding-top",z="none",A="z-index",B="qx.module.Placeholder",C="tagName",D="hidden",E="padding-bottom",F="tap",G="absolute",H="font-style",I="input[placeholder], textarea[placeholder]";qx.Bootstrap.define(B,{statics:{PLACEHOLDER_NAME:s,update:function(){if(!qxWeb.env.get(e)){qxWeb(I).updatePlaceholder();};},__syncStyles:function(N){var O=N.getAttribute(o);var K=N.getProperty(qx.module.Placeholder.PLACEHOLDER_NAME);var M=N.getStyle(A);var J=parseInt(N.getStyle(p))+2*parseInt(N.getStyle(m));var L=parseInt(N.getStyle(y))+2*parseInt(N.getStyle(E));K.setHtml(O).setStyles({display:N.getValue()==t?g:z,zIndex:M==j?1:M+1,textAlign:N.getStyle(n),width:(N.getWidth()-J-4)+d,height:(N.getHeight()-L-4)+d,left:N.getPosition().left+d,top:N.getPosition().top+d,fontFamily:N.getStyle(l),fontStyle:N.getStyle(H),fontVariant:N.getStyle(f),fontWeight:N.getStyle(q),fontSize:N.getStyle(c),paddingTop:(parseInt(N.getStyle(y))+2)+d,paddingRight:(parseInt(N.getStyle(m))+2)+d,paddingBottom:(parseInt(N.getStyle(E))+2)+d,paddingLeft:(parseInt(N.getStyle(p))+2)+d});},__createPlaceholderElement:function(P){var Q=qxWeb.create(b).setStyles({position:G,color:r,overflow:D,pointerEvents:z});P.setProperty(qx.module.Placeholder.PLACEHOLDER_NAME,Q);P.on(w,function(R){var S=R.getProperty(qx.module.Placeholder.PLACEHOLDER_NAME);S.setStyle(x,R.getValue()==t?g:z);}.bind(this,P));if(!qxWeb.env.get(h)){Q.setStyle(k,a).on(F,function(T){T.focus();}.bind(this,P));};return Q;}},members:{updatePlaceholder:function(){if(!qxWeb.env.get(e)){for(var i=0;i<this.length;i++ ){var W=qxWeb(this[i]);var ba=W.getAttribute(o);var Y=W.getProperty(C);if(!ba||(Y!=v&&Y!=u)){continue;};var U=W.getProperty(qx.module.Placeholder.PLACEHOLDER_NAME);if(!U){U=qx.module.Placeholder.__createPlaceholderElement(W);};var V=W.isRendered();var X=U.isRendered();if(V&&!X){W.before(U);}else if(!V&&X){U.remove();return this;};qx.module.Placeholder.__syncStyles(W);};};return this;}},defer:function(bb){qxWeb.$attachAll(this,o);}});})();(function(){var a="only screen and (min-width: 90.063em)",b="xlarge-only",c="only screen and (max-width: 40em)",d="only screen and (min-width: 64.063em) and (max-width: 90em)",e="change",f="xlarge-up",g="only screen",h="only screen and (min-width: 40.063em) and (max-width: 64em)",i="small-up",j="large-only",k="large-up",l="only screen and (min-width: 120.063em)",m="small-only",n="only screen and (min-width: 64.063em)",o="only screen and (min-width: 40.063em)",p="qx.module.MatchMedia",q="xxlarge-up",r="only screen and (min-width: 90.063em) and (max-width: 120em)",s="medium-up",t="medium-only",u="html";qx.Bootstrap.define(p,{statics:{matchMedia:function(v){return new qx.bom.MediaQuery(v);},addSizeClasses:function(){qxWeb(u).mediaQueryToClass(g,i);qxWeb(u).mediaQueryToClass(c,m);qxWeb(u).mediaQueryToClass(o,s);qxWeb(u).mediaQueryToClass(h,t);qxWeb(u).mediaQueryToClass(n,k);qxWeb(u).mediaQueryToClass(d,j);qxWeb(u).mediaQueryToClass(a,f);qxWeb(u).mediaQueryToClass(r,b);qxWeb(u).mediaQueryToClass(l,q);},__applyClass:function(x,w){if(x.isMatching()){this.addClass(w);}else {this.removeClass(w);};}},members:{mediaQueryToClass:function(A,y){var B=qx.module.MatchMedia.matchMedia(A);var z=qx.module.MatchMedia.__applyClass.bind(this,B,y);z(B,y);B.on(e,z);return this;}},defer:function(C){qxWeb.$attachAll(this);}});})();(function(){var a='on',b='#mediamatchjs { position: relative; z-index: 0; }',d='px',e='max-',f='resize',g='',j='dpcm',k='mediamatchjs',l='text/css',m='landscape',n="Map",o='dppx',p='@media ',q='orientationchange',r='head',s='style',t='portrait',u="change",v="2.0.1",y='device-aspect-ratio',z=',',A='(',B='em',C='aspect-ratio',D='speech',E='use strict',F='color-index',G='tv',H="qx.bom.MediaQuery",I='tty',J='embossed',K='all',L='not',M=' } }',N='screen',O='device-height',P='handheld',Q='device-pixel-ratio',R='rem',S='print',T='device-width',U='braille',V=' { #mediamatchjs { z-index: ',W=' and ',X='projection',Y='min-';qx.Bootstrap.define(H,{extend:qx.event.Emitter,construct:function(ba){this.__mql=window.matchMedia(ba);this.query=ba;this.matches=this.__mql.matches;this.__init();},events:{"change":n},statics:{version:v},members:{__mql:null,matches:false,query:null,getQuery:function(){return this.query;},isMatching:function(){return this.matches;},__init:function(){this.__mql.addListener(this.__changed.bind(this));},__changed:function(){this.matches=this.__mql.matches;this.emit(u,{matches:this.matches,query:this.query});}},defer:function(){window.matchMedia||(window.matchMedia=function(bf){E;var bh=bf.document,bj=bh.documentElement,bk=[],bl=0,bb=g,bo={},bc=/\s*(only|not)?\s*(screen|print|[a-z\-]+)\s*(and)?\s*/i,bm=/^\s*\(\s*(-[a-z]+-)?(min-|max-)?([a-z\-]+)\s*(:?\s*([0-9]+(\.[0-9]+)?|portrait|landscape)(px|em|dppx|dpcm|rem|%|in|cm|mm|ex|pt|pc|\/([0-9]+(\.[0-9]+)?))?)?\s*\)\s*$/,be=0,bg=function(bB){var bv=(bB.indexOf(z)!==-1&&bB.split(z))||[bB],bI=bv.length-1,bu=bI,bz=null,bD=null,bE=g,bq=0,bC=false,bF=g,bt=g,bG=null,br=0,bs=0,bp=null,by=g,length=g,bH=g,bw=g,bx=g,bA=false;if(bB===g){return true;};bn();do {bz=bv[bu-bI];bC=false;bD=bz.match(bc);if(bD){bE=bD[0];bq=bD.index;};if(!bD||((bz.substring(0,bq).indexOf(A)===-1)&&(bq||(!bD[3]&&bE!==bD.input)))){bA=false;continue;};bt=bz;bC=bD[1]===L;if(!bq){bF=bD[2];bt=bz.substring(bE.length);};bA=bF===bb||bF===K||bF===g;bG=(bt.indexOf(W)!==-1&&bt.split(W))||[bt];br=bG.length-1;bs=br;if(bA&&br>=0&&bt!==g){do {bp=bG[br].match(bm);if(!bp||!bo[bp[3]]){bA=false;break;};by=bp[2];length=bp[5];bw=length;bH=bp[7];bx=bo[bp[3]];if(bH){if(bH===d){bw=Number(length);}else if(bH===B||bH===R){bw=16*length;}else if(bp[8]){bw=(length/bp[8]).toFixed(2);}else if(bH===o){bw=length*96;}else if(bH===j){bw=length*0.3937;}else {bw=Number(length);};};if(by===Y&&bw){bA=bx>=bw;}else if(by===e&&bw){bA=bx<=bw;}else if(bw){bA=bx===bw;}else {bA=!!bx;};if(!bA){break;};}while(br-- );};if(bA){break;};}while(bI-- );return bC?!bA:bA;},bn=function(){var w=bf.innerWidth||bj.clientWidth,h=bf.innerHeight||bj.clientHeight,bK=bf.screen.width,bJ=bf.screen.height,c=bf.screen.colorDepth,x=bf.devicePixelRatio;bo.width=w;bo.height=h;bo[C]=(w/h).toFixed(2);bo[T]=bK;bo[O]=bJ;bo[y]=(bK/bJ).toFixed(2);bo.color=c;bo[F]=Math.pow(2,c);bo.orientation=(h>=w?t:m);bo.resolution=(x&&x*96)||bf.screen.deviceXDPI||96;bo[Q]=x||1;},bd=function(){clearTimeout(be);be=setTimeout(function(){var bO=null,bL=bl-1,bM=bL,bP=false;if(bL>=0){bn();do {bO=bk[bM-bL];if(bO){bP=bg(bO.mql.media);if((bP&&!bO.mql.matches)||(!bP&&bO.mql.matches)){bO.mql.matches=bP;if(bO.listeners){for(var i=0,bN=bO.listeners.length;i<bN;i++ ){if(bO.listeners[i]){bO.listeners[i].call(bf,bO.mql);};};};};};}while(bL-- );};},10);},bi=function(){var bR=bh.getElementsByTagName(r)[0],bV=bh.createElement(s),bQ=null,bT=[N,S,D,X,P,G,U,J,I],bX=0,bY=bT.length,bU=b,bS=g,bW=bf.addEventListener||(bS=a)&&bf.attachEvent;bV.type=l;bV.id=k;bR.appendChild(bV);bQ=(bf.getComputedStyle&&bf.getComputedStyle(bV))||bV.currentStyle;for(;bX<bY;bX++ ){bU+=p+bT[bX]+V+bX+M;};if(bV.styleSheet){bV.styleSheet.cssText=bU;}else {bV.textContent=bU;};bb=bT[(bQ.zIndex*1)||0];bR.removeChild(bV);bn();bW(bS+f,bd);bW(bS+q,bd);};bi();return function(ca){var cb=bl,cc={matches:false,media:ca,addListener:function ce(cd){bk[cb].listeners||(bk[cb].listeners=[]);cd&&bk[cb].listeners.push(cd);},removeListener:function ch(cf){var cg=bk[cb],i=0,ci=0;if(!cg){return;};ci=cg.listeners.length;for(;i<ci;i++ ){if(cg.listeners[i]===cf){cg.listeners.splice(i,1);};};}};if(ca===g){cc.matches=true;return cc;};cc.matches=bg(ca);bl=bk.push({mql:cc,listeners:null});return cc;};}(window));}});})();(function(){var a="landscape",b="getOrientation",c="portrait",d="orientationchange",e="isLandscape",f="function",g="qx.module.event.Orientation",h="isPortrait";qx.Bootstrap.define(g,{statics:{TYPES:[d],BIND_METHODS:[b,e,h],getOrientation:function(){return this._orientation;},isLandscape:function(){return this._mode==a;},isPortrait:function(){return this._mode==c;},normalize:function(event,k,m){if(!event){return event;};event._type=m;var j=qx.module.event.Orientation.BIND_METHODS;for(var i=0,l=j.length;i<l;i++ ){if(typeof event[j[i]]!=f){event[j[i]]=qx.module.event.Orientation[j[i]].bind(event);};};return event;}},defer:function(n){qxWeb.$registerEventNormalization(n.TYPES,n.normalize);}});})();(function(){var a="start",b="animationEnd",c="",d="none",e="browser.name",f="browser.version",g="qx.module.Animation",h="animationIteration",j="end",k="animationStart",l="ease-in",m="iteration",n="ease-out",o="ie",p="display";qx.Bootstrap.define(g,{events:{"animationStart":undefined,"animationIteration":undefined,"animationEnd":undefined},statics:{_fadeOut:{duration:700,timing:n,keep:100,keyFrames:{'0':{opacity:1},'100':{opacity:0,display:d}}},_fadeIn:{duration:700,timing:l,keep:100,keyFrames:{'0':{opacity:0},'100':{opacity:1}}},_animate:function(s,q,r){this._forEachElement(function(t,i){if(t.$$animation){t.$$animation.stop();};var u;if(r){u=qx.bom.element.Animation.animateReverse(t,s,q);}else {u=qx.bom.element.Animation.animate(t,s,q);};var self=this;if(i==0){u.on(a,function(){self.emit(k);},u);u.on(m,function(){self.emit(h);},u);};u.on(j,function(){for(var i=0;i<self.length;i++ ){if(self[i].$$animation){return;};};self.emit(b);},t);});}},members:{getAnimationHandles:function(){var v=[];for(var i=0;i<this.length;i++ ){v[i]=this[i].$$animation;};return v;},animate:function(x,w){qx.module.Animation._animate.bind(this)(x,w,false);return this;},animateReverse:function(z,y){qx.module.Animation._animate.bind(this)(z,y,true);return this;},play:function(){for(var i=0;i<this.length;i++ ){var A=this[i].$$animation;if(A){A.play();};};return this;},pause:function(){for(var i=0;i<this.length;i++ ){var B=this[i].$$animation;if(B){B.pause();};};return this;},stop:function(){for(var i=0;i<this.length;i++ ){var C=this[i].$$animation;if(C){C.stop();};};return this;},isPlaying:function(){for(var i=0;i<this.length;i++ ){var D=this[i].$$animation;if(D&&D.isPlaying()){return true;};};return false;},isEnded:function(){for(var i=0;i<this.length;i++ ){var E=this[i].$$animation;if(E&&!E.isEnded()){return false;};};return true;},fadeIn:function(F){this.setStyle(p,c);return this.animate(qx.module.Animation._fadeIn,F);},fadeOut:function(G){return this.animate(qx.module.Animation._fadeOut,G);}},defer:function(H){qxWeb.$attachAll(this);if(qxWeb.env.get(e)===o&&qxWeb.env.get(f)<=9){H._fadeIn.keyFrames[100].opacity=0.99;};}});})();(function(){var a="css.animation",b="translate",c="rotate",d="skew",e="scale",f="qx.bom.element.Animation";qx.Bootstrap.define(f,{statics:{animate:function(h,k,g){var j=qx.bom.element.Animation.__hasOnlyCssKeys(h,k.keyFrames);if(qx.core.Environment.get(a)&&j){return qx.bom.element.AnimationCss.animate(h,k,g);}else {return qx.bom.element.AnimationJs.animate(h,k,g);};},animateReverse:function(m,o,l){var n=qx.bom.element.Animation.__hasOnlyCssKeys(m,o.keyFrames);if(qx.core.Environment.get(a)&&n){return qx.bom.element.AnimationCss.animateReverse(m,o,l);}else {return qx.bom.element.AnimationJs.animateReverse(m,o,l);};},__hasOnlyCssKeys:function(p,t){var r=[];for(var v in t){var s=t[v];for(var u in s){if(r.indexOf(u)==-1){r.push(u);};};};var q=[e,c,d,b];for(var i=0;i<r.length;i++ ){var u=qx.lang.String.camelCase(r[i]);if(!(u in p.style)){if(q.indexOf(r[i])!=-1){continue;};if(qx.bom.Style.getPropertyName(u)){continue;};return false;};};return true;}}});})();(function(){var a="fill-mode",b="os.name",c="repeat",d="os.version",f="timing",g="start",h="end",i="Anni",j="alternate",k="keep",l="visibilitychange",m=":",n="ios",o="} ",p="name",q="iteration-event",r="",s="origin",t="forwards",u="start-event",v="iteration",w="end-event",x="css.animation",y="ms ",z="% {",A=" ",B="linear",C=";",D="qx.bom.element.AnimationCss",E="keyframes";qx.Bootstrap.define(D,{statics:{__sheet:null,__rulePrefix:i,__id:0,__rules:{},__transitionKeys:{"scale":true,"rotate":true,"skew":true,"translate":true},__cssAnimationKeys:qx.core.Environment.get(x),animateReverse:function(G,H,F){return this._animate(G,H,F,true);},animate:function(J,K,I){return this._animate(J,K,I,false);},_animate:function(L,S,R,N){this.__normalizeDesc(S);{};var P=S.keep;if(P!=null&&(N||(S.alternate&&S.repeat%2==0))){P=100-P;};if(!this.__sheet){this.__sheet=qx.bom.Stylesheet.createElement();};var O=S.keyFrames;if(R==undefined){R=S.duration;};if(this.__cssAnimationKeys!=null){var name=this.__addKeyFrames(O,N);var M=name+A+R+y+S.timing+A+(S.delay?S.delay+y:r)+S.repeat+A+(S.alternate?j:r);qx.bom.Event.addNativeListener(L,this.__cssAnimationKeys[u],this.__onAnimationStart);qx.bom.Event.addNativeListener(L,this.__cssAnimationKeys[q],this.__onAnimationIteration);qx.bom.Event.addNativeListener(L,this.__cssAnimationKeys[w],this.__onAnimationEnd);{};L.style[qx.lang.String.camelCase(this.__cssAnimationKeys[p])]=M;if(P&&P==100&&this.__cssAnimationKeys[a]){L.style[this.__cssAnimationKeys[a]]=t;};};var Q=new qx.bom.element.AnimationHandle();Q.desc=S;Q.el=L;Q.keep=P;L.$$animation=Q;if(S.origin!=null){qx.bom.element.Transform.setOrigin(L,S.origin);};if(this.__cssAnimationKeys==null){window.setTimeout(function(){qx.bom.element.AnimationCss.__onAnimationEnd({target:L});},0);};return Q;},__onAnimationStart:function(e){if(e.target.$$animation){e.target.$$animation.emit(g,e.target);};},__onAnimationIteration:function(e){if(e.target!=null&&e.target.$$animation!=null){e.target.$$animation.emit(v,e.target);};},__onAnimationEnd:function(e){var T=e.target;var U=T.$$animation;if(!U){return;};var W=U.desc;if(qx.bom.element.AnimationCss.__cssAnimationKeys!=null){var V=qx.lang.String.camelCase(qx.bom.element.AnimationCss.__cssAnimationKeys[p]);T.style[V]=r;qx.bom.Event.removeNativeListener(T,qx.bom.element.AnimationCss.__cssAnimationKeys[p],qx.bom.element.AnimationCss.__onAnimationEnd);};if(W.origin!=null){qx.bom.element.Transform.setOrigin(T,r);};qx.bom.element.AnimationCss.__keepFrame(T,W.keyFrames[U.keep]);T.$$animation=null;U.el=null;U.ended=true;U.emit(h,T);},__keepFrame:function(X,Y){var bb;for(var ba in Y){if(ba in qx.bom.element.AnimationCss.__transitionKeys){if(!bb){bb={};};bb[ba]=Y[ba];}else {X.style[qx.lang.String.camelCase(ba)]=Y[ba];};};if(bb){qx.bom.element.Transform.transform(X,bb);};},__normalizeDesc:function(bc){if(!bc.hasOwnProperty(j)){bc.alternate=false;};if(!bc.hasOwnProperty(k)){bc.keep=null;};if(!bc.hasOwnProperty(c)){bc.repeat=1;};if(!bc.hasOwnProperty(f)){bc.timing=B;};if(!bc.hasOwnProperty(s)){bc.origin=null;};},__addKeyFrames:function(frames,be){var bh=r;for(var bl in frames){bh+=(be?-(bl-100):bl)+z;var bg=frames[bl];var bj;for(var bd in bg){if(bd in this.__transitionKeys){if(!bj){bj={};};bj[bd]=bg[bd];}else {var bk=qx.bom.Style.getPropertyName(bd);var bf=(bk!==null)?qx.bom.Style.getCssName(bk):r;bh+=(bf||bd)+m+bg[bd]+C;};};if(bj){bh+=qx.bom.element.Transform.getCss(bj);};bh+=o;};if(this.__rules[bh]){return this.__rules[bh];};var name=this.__rulePrefix+this.__id++ ;var bi=this.__cssAnimationKeys[E]+A+name;qx.bom.Stylesheet.addRule(this.__sheet,bi,bh);this.__rules[bh]=name;return name;},__clearCache:function(){this.__id=0;if(this.__sheet){this.__sheet.ownerNode.remove();this.__sheet=null;this.__rules={};};}},defer:function(bm){if(qx.core.Environment.get(b)===n&&parseInt(qx.core.Environment.get(d))>=8){document.addEventListener(l,function(){if(!document.hidden){bm.__clearCache();};},false);};}});})();(function(){var a="css.animation",b="Element",c="",d="qx.bom.element.AnimationHandle",e="play-state",f="paused",g="running";qx.Bootstrap.define(d,{extend:qx.event.Emitter,construct:function(){var h=qx.core.Environment.get(a);this.__playState=h&&h[e];this.__playing=true;},events:{"start":b,"end":b,"iteration":b},members:{__playState:null,__playing:false,__ended:false,isPlaying:function(){return this.__playing;},isEnded:function(){return this.__ended;},isPaused:function(){return this.el.style[this.__playState]==f;},pause:function(){if(this.el){this.el.style[this.__playState]=f;this.el.$$animation.__playing=false;if(this.animationId&&qx.bom.element.AnimationJs){qx.bom.element.AnimationJs.pause(this);};};},play:function(){if(this.el){this.el.style[this.__playState]=g;this.el.$$animation.__playing=true;if(this.i!=undefined&&qx.bom.element.AnimationJs){qx.bom.element.AnimationJs.play(this);};};},stop:function(){if(this.el&&qx.core.Environment.get(a)&&!this.jsAnimation){this.el.style[this.__playState]=c;this.el.style[qx.core.Environment.get(a).name]=c;this.el.$$animation.__playing=false;this.el.$$animation.__ended=true;}else if(this.jsAnimation){this.stopped=true;qx.bom.element.AnimationJs.stop(this);};}}});})();(function(){var c="cm",d="mm",e="0",f="pt",g="pc",h="",k="%",l="em",m="qx.bom.element.AnimationJs",n="infinite",o="#",p="in",q="px",r="start",s="end",t="ex",u=";",v="undefined",w="iteration",y="string",z=":";qx.Bootstrap.define(m,{statics:{__maxStepTime:30,__units:[k,p,c,d,l,t,f,g,q],__transitionKeys:{"scale":true,"rotate":true,"skew":true,"translate":true},animate:function(B,C,A){return this._animate(B,C,A,false);},animateReverse:function(E,F,D){return this._animate(E,F,D,true);},_animate:function(G,Q,P,I){if(G.$$animation){return G.$$animation;};Q=qx.lang.Object.clone(Q,true);if(P==undefined){P=Q.duration;};var L=Q.keyFrames;var J=this.__getOrderedKeys(L);var K=this.__getStepTime(P,J);var N=parseInt(P/K,10);this.__normalizeKeyFrames(L,G);var O=this.__calculateDelta(N,K,J,L,P,Q.timing);var H=new qx.bom.element.AnimationHandle();H.jsAnimation=true;if(I){O.reverse();H.reverse=true;};H.desc=Q;H.el=G;H.delta=O;H.stepTime=K;H.steps=N;G.$$animation=H;H.i=0;H.initValues={};H.repeatSteps=this.__applyRepeat(N,Q.repeat);var M=Q.delay||0;var self=this;H.delayId=window.setTimeout(function(){H.delayId=null;self.play(H);},M);return H;},__normalizeKeyFrames:function(V,R){var Y={};for(var U in V){for(var name in V[U]){var S=qx.bom.Style.getPropertyName(name);if(S&&S!=name){var X=qx.bom.Style.getCssName(S);V[U][X]=V[U][name];delete V[U][name];name=X;};if(Y[name]==undefined){var W=V[U][name];if(typeof W==y){Y[name]=this.__getUnit(W);}else {Y[name]=h;};};};};for(var U in V){var T=V[U];for(var name in Y){if(T[name]==undefined){if(name in R.style){if(window.getComputedStyle){T[name]=getComputedStyle(R,null)[name];}else {T[name]=R.style[name];};}else {T[name]=R[name];};if(T[name]===h&&this.__units.indexOf(Y[name])!=-1){T[name]=e+Y[name];};};};};},__normalizeKeyFrameTransforms:function(bb){bb=qx.lang.Object.clone(bb);var bc;for(var name in bb){if(name in this.__transitionKeys){if(!bc){bc={};};bc[name]=bb[name];delete bb[name];};};if(bc){var ba=qx.bom.element.Transform.getCss(bc).split(z);if(ba.length>1){bb[ba[0]]=ba[1].replace(u,h);};};return bb;},__calculateDelta:function(bw,bh,bo,bi,be,bq){var bp=new Array(bw);var bm=1;bp[0]=this.__normalizeKeyFrameTransforms(bi[0]);var bt=bi[0];var bj=bi[bo[bm]];var bf=Math.floor(bo[bm]/(bh/be*100));var bs=1;for(var i=1;i<bp.length;i++ ){if(i*bh/be*100>bo[bm]){bt=bj;bm++ ;bj=bi[bo[bm]];bf=Math.floor(bo[bm]/(bh/be*100))-bf;bs=1;};bp[i]={};var bd;for(var name in bj){var br=bj[name]+h;if(name in this.__transitionKeys){if(!bd){bd={};};if(qx.Bootstrap.isArray(bt[name])){if(!qx.Bootstrap.isArray(bj[name])){bj[name]=[bj[name]];};bd[name]=[];for(var j=0;j<bj[name].length;j++ ){var bu=bj[name][j]+h;var x=bs/bf;bd[name][j]=this.__getNextValue(bu,bt[name],bq,x);};}else {var x=bs/bf;bd[name]=this.__getNextValue(br,bt[name],bq,x);};}else if(br.charAt(0)==o){var bl=qx.util.ColorUtil.cssStringToRgb(bt[name]);var bk=qx.util.ColorUtil.cssStringToRgb(br);var bg=[];for(var j=0;j<bl.length;j++ ){var bv=bl[j]-bk[j];var x=bs/bf;var bn=qx.bom.AnimationFrame.calculateTiming(bq,x);bg[j]=parseInt(bl[j]-bv*bn,10);};bp[i][name]=qx.util.ColorUtil.rgbToHexString(bg);}else if(!isNaN(parseFloat(br))){var x=bs/bf;bp[i][name]=this.__getNextValue(br,bt[name],bq,x);}else {bp[i][name]=bt[name]+h;};};if(bd){var bx=qx.bom.element.Transform.getCss(bd).split(z);if(bx.length>1){bp[i][bx[0]]=bx[1].replace(u,h);};};bs++ ;};bp[bp.length-1]=this.__normalizeKeyFrameTransforms(bi[100]);return bp;},__getUnit:function(by){return by.substring((parseFloat(by)+h).length,by.length);},__getNextValue:function(bC,bB,bz,x){var bA=parseFloat(bC)-parseFloat(bB);return (parseFloat(bB)+bA*qx.bom.AnimationFrame.calculateTiming(bz,x))+this.__getUnit(bC);},play:function(bD){bD.emit(r,bD.el);var bE=window.setInterval(function(){bD.repeatSteps-- ;var bF=bD.delta[bD.i%bD.steps];if(bD.i===0){for(var name in bF){if(bD.initValues[name]===undefined){if(bD.el[name]!==undefined){bD.initValues[name]=bD.el[name];}else if(qx.bom.element.Style){bD.initValues[name]=qx.bom.element.Style.get(bD.el,qx.lang.String.camelCase(name));}else {bD.initValues[name]=bD.el.style[qx.lang.String.camelCase(name)];};};};};qx.bom.element.AnimationJs.__applyStyles(bD.el,bF);bD.i++ ;if(bD.i%bD.steps==0){bD.emit(w,bD.el);if(bD.desc.alternate){bD.delta.reverse();};};if(bD.repeatSteps<0){qx.bom.element.AnimationJs.stop(bD);};},bD.stepTime);bD.animationId=bE;return bD;},pause:function(bG){window.clearInterval(bG.animationId);bG.animationId=null;return bG;},stop:function(bK){var bJ=bK.desc;var bH=bK.el;var bI=bK.initValues;if(bK.animationId){window.clearInterval(bK.animationId);};if(bK.delayId){window.clearTimeout(bK.delayId);};if(bH==undefined){return bK;};var bL=bJ.keep;if(bL!=undefined&&!bK.stopped){if(bK.reverse||(bJ.alternate&&bJ.repeat&&bJ.repeat%2==0)){bL=100-bL;};this.__applyStyles(bH,bJ.keyFrames[bL]);}else {this.__applyStyles(bH,bI);};bH.$$animation=null;bK.el=null;bK.ended=true;bK.animationId=null;bK.emit(s,bH);return bK;},__applyRepeat:function(bN,bM){if(bM==undefined){return bN;};if(bM==n){return Number.MAX_VALUE;};return bN*bM;},__applyStyles:function(bP,bO){for(var bQ in bO){if(bO[bQ]===undefined){continue;};if(typeof bP.style[bQ]===v&&bQ in bP){bP[bQ]=bO[bQ];continue;};var name=qx.bom.Style.getPropertyName(bQ)||bQ;if(qx.bom.element.Style){qx.bom.element.Style.set(bP,name,bO[bQ]);}else {bP.style[name]=bO[bQ];};};},__getStepTime:function(bT,bR){var bU=100;for(var i=0;i<bR.length-1;i++ ){bU=Math.min(bU,bR[i+1]-bR[i]);};var bS=bT*bU/100;while(bS>this.__maxStepTime){bS=bS/2;};return Math.round(bS);},__getOrderedKeys:function(bW){var bV=Object.keys(bW);for(var i=0;i<bV.length;i++ ){bV[i]=parseInt(bV[i],10);};bV.sort(function(a,b){return a-b;});return bV;}}});})();(function(){var a="Could not convert system colors to RGB: ",b="Could not parse color: ",c="#",d="qx.util.ColorUtil";qx.Bootstrap.define(d,{statics:{REGEXP:{hex3:/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,rgb:/^rgb\(\s*([0-9]{1,3}\.{0,1}[0-9]*)\s*,\s*([0-9]{1,3}\.{0,1}[0-9]*)\s*,\s*([0-9]{1,3}\.{0,1}[0-9]*)\s*\)$/,rgba:/^rgba\(\s*([0-9]{1,3}\.{0,1}[0-9]*)\s*,\s*([0-9]{1,3}\.{0,1}[0-9]*)\s*,\s*([0-9]{1,3}\.{0,1}[0-9]*)\s*,\s*([0-9]{1,3}\.{0,1}[0-9]*)\s*\)$/},SYSTEM:{activeborder:true,activecaption:true,appworkspace:true,background:true,buttonface:true,buttonhighlight:true,buttonshadow:true,buttontext:true,captiontext:true,graytext:true,highlight:true,highlighttext:true,inactiveborder:true,inactivecaption:true,inactivecaptiontext:true,infobackground:true,infotext:true,menu:true,menutext:true,scrollbar:true,threeddarkshadow:true,threedface:true,threedhighlight:true,threedlightshadow:true,threedshadow:true,window:true,windowframe:true,windowtext:true},NAMED:{black:[0,0,0],silver:[192,192,192],gray:[128,128,128],white:[255,255,255],maroon:[128,0,0],red:[255,0,0],purple:[128,0,128],fuchsia:[255,0,255],green:[0,128,0],lime:[0,255,0],olive:[128,128,0],yellow:[255,255,0],navy:[0,0,128],blue:[0,0,255],teal:[0,128,128],aqua:[0,255,255],transparent:[-1,-1,-1],magenta:[255,0,255],orange:[255,165,0],brown:[165,42,42]},isNamedColor:function(e){return this.NAMED[e]!==undefined;},isSystemColor:function(f){return this.SYSTEM[f]!==undefined;},cssStringToRgb:function(g){if(this.isNamedColor(g)){return this.NAMED[g];}else if(this.isSystemColor(g)){throw new Error(a+g);}else if(this.isRgbString(g)){return this.__rgbStringToRgb();}else if(this.isRgbaString(g)){return this.__rgbaStringToRgb();}else if(this.isHex3String(g)){return this.__hex3StringToRgb();}else if(this.isHex6String(g)){return this.__hex6StringToRgb();};throw new Error(b+g);},rgbToHexString:function(h){return (c+qx.lang.String.pad(h[0].toString(16).toUpperCase(),2)+qx.lang.String.pad(h[1].toString(16).toUpperCase(),2)+qx.lang.String.pad(h[2].toString(16).toUpperCase(),2));},isHex3String:function(i){return this.REGEXP.hex3.test(i);},isHex6String:function(j){return this.REGEXP.hex6.test(j);},isRgbString:function(k){return this.REGEXP.rgb.test(k);},isRgbaString:function(l){return this.REGEXP.rgba.test(l);},__rgbStringToRgb:function(){var o=parseInt(RegExp.$1,10);var n=parseInt(RegExp.$2,10);var m=parseInt(RegExp.$3,10);return [o,n,m];},__rgbaStringToRgb:function(){var s=parseInt(RegExp.$1,10);var r=parseInt(RegExp.$2,10);var p=parseInt(RegExp.$3,10);var q=parseFloat(RegExp.$4,10);if(s===0&&r===0&p===0&&q===0){return [-1,-1,-1];};return [s,r,p];},__hex3StringToRgb:function(){var v=parseInt(RegExp.$1,16)*17;var u=parseInt(RegExp.$2,16)*17;var t=parseInt(RegExp.$3,16)*17;return [v,u,t];},__hex6StringToRgb:function(){var y=(parseInt(RegExp.$1,16)*16)+parseInt(RegExp.$2,16);var x=(parseInt(RegExp.$3,16)*16)+parseInt(RegExp.$4,16);var w=(parseInt(RegExp.$5,16)*16)+parseInt(RegExp.$6,16);return [y,x,w];}}});})();(function(){var a="qx.module.Storage";qx.Bootstrap.define(a,{statics:{setLocalItem:function(b,c){qx.bom.Storage.getLocal().setItem(b,c);},getLocalItem:function(d){return qx.bom.Storage.getLocal().getItem(d);},removeLocalItem:function(e){qx.bom.Storage.getLocal().removeItem(e);},getLocalLength:function(){return qx.bom.Storage.getLocal().getLength();},getLocalKey:function(f){return qx.bom.Storage.getLocal().getKey(f);},clearLocal:function(){qx.bom.Storage.getLocal().clear();},forEachLocal:function(g,h){qx.bom.Storage.getLocal().forEach(g,h);},setSessionItem:function(i,j){qx.bom.Storage.getSession().setItem(i,j);},getSessionItem:function(k){return qx.bom.Storage.getSession().getItem(k);},removeSessionItem:function(l){qx.bom.Storage.getSession().removeItem(l);},getSessionLength:function(){return qx.bom.Storage.getSession().getLength();},getSessionKey:function(m){return qx.bom.Storage.getSession().getKey(m);},clearSession:function(){qx.bom.Storage.getSession().clear();},forEachSession:function(n,o){qx.bom.Storage.getSession().forEach(n,o);}},defer:function(p){qxWeb.$attachStatic({"localStorage":{setItem:p.setLocalItem,getItem:p.getLocalItem,removeItem:p.removeLocalItem,getLength:p.getLocalLength,getKey:p.getLocalKey,clear:p.clearLocal,forEach:p.forEachLocal},"sessionStorage":{setItem:p.setSessionItem,getItem:p.getSessionItem,removeItem:p.removeSessionItem,getLength:p.getSessionLength,getKey:p.getSessionKey,clear:p.clearSession,forEach:p.forEachSession}});}});})();(function(){var a="html.storage.local",b="html.storage.userdata",c="qx.bom.Storage";qx.Bootstrap.define(c,{statics:{getLocal:function(){if(qx.core.Environment.get(a)){return qx.bom.storage.Web.getLocal();}else if(qx.core.Environment.get(b)){return qx.bom.storage.UserData.getLocal();};return qx.bom.storage.Memory.getLocal();},getSession:function(){if(qx.core.Environment.get(a)){return qx.bom.storage.Web.getSession();}else if(qx.core.Environment.get(b)){return qx.bom.storage.UserData.getSession();};return qx.bom.storage.Memory.getSession();}}});})();(function(){var a="local",b="session",c="Storage",d="qx.bom.storage.Web",f="Storage full.";qx.Bootstrap.define(d,{statics:{__local:null,__session:null,getLocal:function(){if(this.__local){return this.__local;};return this.__local=new qx.bom.storage.Web(a);},getSession:function(){if(this.__session){return this.__session;};return this.__session=new qx.bom.storage.Web(b);}},construct:function(g){this.__type=g;},members:{__type:null,getStorage:function(){return window[this.__type+c];},getLength:function(){return this.getStorage(this.__type).length;},setItem:function(h,j){j=qx.lang.Json.stringify(j);try{this.getStorage(this.__type).setItem(h,j);}catch(e){throw new Error(f);};},getItem:function(l){var k=this.getStorage(this.__type).getItem(l);if(qx.lang.Type.isString(k)){k=qx.lang.Json.parse(k);}else if(k&&k.value&&qx.lang.Type.isString(k.value)){k=qx.lang.Json.parse(k.value);};return k;},removeItem:function(m){this.getStorage(this.__type).removeItem(m);},clear:function(){var n=this.getStorage(this.__type);if(!n.clear){for(var i=n.length-1;i>=0;i-- ){n.removeItem(n.key(i));};}else {n.clear();};},getKey:function(o){return this.getStorage(this.__type).key(o);},forEach:function(p,r){var length=this.getLength();for(var i=0;i<length;i++ ){var q=this.getKey(i);p.call(r,q,this.getItem(q));};}}});})();(function(){var a="\x00\b\n\f\r\t",b="-",c="function",d="[null,null,null]",e="T",f="+",g=",\n",h="constructor",i="{\n",j='"+275760-09-13T00:00:00.000Z"',k="true",l="\\n",m="false",n='"-271821-04-20T00:00:00.000Z"',o="json",p='object',q='""',r="qx.lang.Json",s="{}",t="hasOwnProperty",u="@",v="prototype",w='hasOwnProperty',x='"',y="toLocaleString",z="0",A='function',B="",C='\\"',D="\t",E="string",F="}",G="\r",H="toJSON",I=":",J="[\n 1,\n 2\n]",K="\\f",L='"1969-12-31T23:59:59.999Z"',M="/",N="\\b",O="Z",P="\\t",Q="\b",R="[object Number]",S="isPrototypeOf",T="{",U="toString",V="0x",W="[1]",X="\\r",Y="]",bO=",",bP="null",bQ="\\u00",bK="\n",bL="json-stringify",bM="[]",bN="1",bU="000000",bV="[object Boolean]",bW="valueOf",cm="\\\\",bR="[object String]",bS="json-parse",bT="bug-string-char-index",bG="[object Array]",ca="$",bJ="[\n",cb='"-000001-01-01T00:00:00.000Z"',cc="[",bI="[null]",bX="\\",cl="[object Date]",bY='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}',cd="a",ce=" ",cf=".",ci="[object Function]",cj="01",ck='"\t"',bH="propertyIsEnumerable",cg="\f",ch="object";qx.Bootstrap.define(r,{statics:{stringify:null,parse:null}});(function(){var co;var cn;var cp;(function(window){var cr={}.toString,cG,cQ,cC;var cy=typeof cp===c&&cp.amd,cx=typeof cn==ch&&cn;if(cx||cy){if(typeof JSON==ch&&JSON){if(cx){cx.stringify=JSON.stringify;cx.parse=JSON.parse;}else {cx=JSON;};}else if(cy){cx=window.JSON={};};}else {cx=window.JSON||(window.JSON={});};var cU=new Date(-3509827334573292);try{cU=cU.getUTCFullYear()==-109252&&cU.getUTCMonth()===0&&cU.getUTCDate()===1&&cU.getUTCHours()==10&&cU.getUTCMinutes()==37&&cU.getUTCSeconds()==6&&cU.getUTCMilliseconds()==708;}catch(da){};function cJ(name){if(name==bT){return cd[0]!=cd;};var de,dd=bY,dh=name==o;if(dh||name==bL||name==bS){if(name==bL||dh){var db=cx.stringify,dg=typeof db==c&&cU;if(dg){(de=function(){return 1;}).toJSON=de;try{dg=db(0)===z&&db(new Number())===z&&db(new String())==q&&db(cr)===cC&&db(cC)===cC&&db()===cC&&db(de)===bN&&db([de])==W&&db([cC])==bI&&db(null)==bP&&db([cC,cr,null])==d&&db({"a":[de,true,false,null,a]})==dd&&db(null,de)===bN&&db([1,2],null,1)==J&&db(new Date(-8.64e15))==n&&db(new Date(8.64e15))==j&&db(new Date(-621987552e5))==cb&&db(new Date(-1))==L;}catch(di){dg=false;};};if(!dh){return dg;};};if(name==bS||dh){var df=cx.parse;if(typeof df==c){try{if(df(z)===0&&!df(false)){de=df(dd);var dc=de[cd].length==5&&de[cd][0]===1;if(dc){try{dc=!df(ck);}catch(dj){};if(dc){try{dc=df(cj)!==1;}catch(dk){};};};};}catch(dl){dc=false;};};if(!dh){return dc;};};return dg&&dc;};};if(!cJ(o)){var cV=ci;var cN=cl;var cv=R;var cY=bR;var cR=bG;var cF=bV;var cE=cJ(bT);if(!cU){var cD=Math.floor;var cM=[0,31,59,90,120,151,181,212,243,273,304,334];var cX=function(dm,dn){return cM[dn]+365*(dm-1970)+cD((dm-1969+(dn=+(dn>1)))/4)-cD((dm-1901+dn)/100)+cD((dm-1601+dn)/400);};};if(!(cG={}.hasOwnProperty)){cG=function(dp){var dq={},dr;if((dq.__proto__=null,dq.__proto__={"toString":1},dq).toString!=cr){cG=function(ds){var dt=this.__proto__,du=ds in (this.__proto__=null,this);this.__proto__=dt;return du;};}else {dr=dq.constructor;cG=function(dv){var parent=(this.constructor||dr).prototype;return dv in this&&!(dv in parent&&this[dv]===parent[dv]);};};dq=null;return cG.call(this,dp);};};var cH={'boolean':1,'number':1,'string':1,'undefined':1};var cP=function(dy,dw){var dx=typeof dy[dw];return dx==p?!!dy[dw]:!cH[dx];};cQ=function(dz,dA){var dF=0,dE,dC,dD,dB;(dE=function(){this.valueOf=0;}).prototype.valueOf=0;dC=new dE();for(dD in dC){if(cG.call(dC,dD)){dF++ ;};};dE=dC=null;if(!dF){dC=[bW,U,y,bH,S,t,h];dB=function(dH,dI){var dJ=cr.call(dH)==cV,dK,length;var dG=!dJ&&typeof dH.constructor!=A&&cP(dH,w)?dH.hasOwnProperty:cG;for(dK in dH){if(!(dJ&&dK==v)&&dG.call(dH,dK)){dI(dK);};};for(length=dC.length;dK=dC[ --length];dG.call(dH,dK)&&dI(dK));};}else if(dF==2){dB=function(dP,dL){var dO={},dM=cr.call(dP)==cV,dN;for(dN in dP){if(!(dM&&dN==v)&&!cG.call(dO,dN)&&(dO[dN]=1)&&cG.call(dP,dN)){dL(dN);};};};}else {dB=function(dT,dQ){var dR=cr.call(dT)==cV,dS,dU;for(dS in dT){if(!(dR&&dS==v)&&cG.call(dT,dS)&&!(dU=dS===h)){dQ(dS);};};if(dU||cG.call(dT,(dS=h))){dQ(dS);};};};return dB(dz,dA);};if(!cJ(bL)){var cT={'92':cm,'34':C,'8':N,'12':K,'10':l,'13':X,'9':P};var cI=bU;var cW=function(dV,dW){return (cI+(dW||0)).slice(-dV);};var cB=bQ;var cL=function(dY){var eb=x,dX=0,length=dY.length,ec=length>10&&cE,ea;if(ec){ea=dY.split(B);};for(;dX<length;dX++ ){var ed=dY.charCodeAt(dX);switch(ed){case 8:case 9:case 10:case 12:case 13:case 34:case 92:eb+=cT[ed];break;default:if(ed<32){eb+=cB+cW(2,ed.toString(16));break;};eb+=ec?ea[dX]:cE?dY.charAt(dX):dY[dX];};};return eb+x;};var cs=function(ez,eo,ew,el,ek,ex,es){var et=eo[ez],ev,ei,ef,er,ey,ep,eA,en,em,ee,eu,ej,length,eg,eq,eh;try{et=eo[ez];}catch(eB){};if(typeof et==ch&&et){ev=cr.call(et);if(ev==cN&&!cG.call(et,H)){if(et>-1/0&&et<1/0){if(cX){er=cD(et/864e5);for(ei=cD(er/365.2425)+1970-1;cX(ei+1,0)<=er;ei++ );for(ef=cD((er-cX(ei,0))/30.42);cX(ei,ef+1)<=er;ef++ );er=1+er-cX(ei,ef);ey=(et%864e5+864e5)%864e5;ep=cD(ey/36e5)%24;eA=cD(ey/6e4)%60;en=cD(ey/1e3)%60;em=ey%1e3;}else {ei=et.getUTCFullYear();ef=et.getUTCMonth();er=et.getUTCDate();ep=et.getUTCHours();eA=et.getUTCMinutes();en=et.getUTCSeconds();em=et.getUTCMilliseconds();};et=(ei<=0||ei>=1e4?(ei<0?b:f)+cW(6,ei<0?-ei:ei):cW(4,ei))+b+cW(2,ef+1)+b+cW(2,er)+e+cW(2,ep)+I+cW(2,eA)+I+cW(2,en)+cf+cW(3,em)+O;}else {et=null;};}else if(typeof et.toJSON==c&&((ev!=cv&&ev!=cY&&ev!=cR)||cG.call(et,H))){et=et.toJSON(ez);};};if(ew){et=ew.call(eo,ez,et);};if(et===null){return bP;};ev=cr.call(et);if(ev==cF){return B+et;}else if(ev==cv){return et>-1/0&&et<1/0?B+et:bP;}else if(ev==cY){return cL(B+et);};if(typeof et==ch){for(length=es.length;length-- ;){if(es[length]===et){throw TypeError();};};es.push(et);ee=[];eg=ex;ex+=ek;if(ev==cR){for(ej=0,length=et.length;ej<length;eq||(eq=true),ej++ ){eu=cs(ej,et,ew,el,ek,ex,es);ee.push(eu===cC?bP:eu);};eh=eq?(ek?bJ+ex+ee.join(g+ex)+bK+eg+Y:(cc+ee.join(bO)+Y)):bM;}else {cQ(el||et,function(eC){var eD=cs(eC,et,ew,el,ek,ex,es);if(eD!==cC){ee.push(cL(eC)+I+(ek?ce:B)+eD);};eq||(eq=true);});eh=eq?(ek?i+ex+ee.join(g+ex)+bK+eg+F:(T+ee.join(bO)+F)):s;};es.pop();return eh;};};cx.stringify=function(eK,eJ,eL){var eF,eG,eI;if(typeof eJ==c||typeof eJ==ch&&eJ){if(cr.call(eJ)==cV){eG=eJ;}else if(cr.call(eJ)==cR){eI={};for(var eE=0,length=eJ.length,eH;eE<length;eH=eJ[eE++ ],((cr.call(eH)==cY||cr.call(eH)==cv)&&(eI[eH]=1)));};};if(eL){if(cr.call(eL)==cv){if((eL-=eL%1)>0){for(eF=B,eL>10&&(eL=10);eF.length<eL;eF+=ce);};}else if(cr.call(eL)==cY){eF=eL.length<=10?eL:eL.slice(0,10);};};return cs(B,(eH={},eH[B]=eK,eH),eG,eI,eF,B,[]);};};if(!cJ(bS)){var cA=String.fromCharCode;var cz={'92':bX,'34':x,'47':M,'98':Q,'116':D,'110':bK,'102':cg,'114':G};var cq,cu;var cw=function(){cq=cu=null;throw SyntaxError();};var cS=function(){var eO=cu,length=eO.length,eN,eM,eQ,eP,eR;while(cq<length){eR=eO.charCodeAt(cq);switch(eR){case 9:case 10:case 13:case 32:cq++ ;break;case 123:case 125:case 91:case 93:case 58:case 44:eN=cE?eO.charAt(cq):eO[cq];cq++ ;return eN;case 34:for(eN=u,cq++ ;cq<length;){eR=eO.charCodeAt(cq);if(eR<32){cw();}else if(eR==92){eR=eO.charCodeAt( ++cq);switch(eR){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:eN+=cz[eR];cq++ ;break;case 117:eM= ++cq;for(eQ=cq+4;cq<eQ;cq++ ){eR=eO.charCodeAt(cq);if(!(eR>=48&&eR<=57||eR>=97&&eR<=102||eR>=65&&eR<=70)){cw();};};eN+=cA(V+eO.slice(eM,cq));break;default:cw();};}else {if(eR==34){break;};eR=eO.charCodeAt(cq);eM=cq;while(eR>=32&&eR!=92&&eR!=34){eR=eO.charCodeAt( ++cq);};eN+=eO.slice(eM,cq);};};if(eO.charCodeAt(cq)==34){cq++ ;return eN;};cw();default:eM=cq;if(eR==45){eP=true;eR=eO.charCodeAt( ++cq);};if(eR>=48&&eR<=57){if(eR==48&&((eR=eO.charCodeAt(cq+1)),eR>=48&&eR<=57)){cw();};eP=false;for(;cq<length&&((eR=eO.charCodeAt(cq)),eR>=48&&eR<=57);cq++ );if(eO.charCodeAt(cq)==46){eQ= ++cq;for(;eQ<length&&((eR=eO.charCodeAt(eQ)),eR>=48&&eR<=57);eQ++ );if(eQ==cq){cw();};cq=eQ;};eR=eO.charCodeAt(cq);if(eR==101||eR==69){eR=eO.charCodeAt( ++cq);if(eR==43||eR==45){cq++ ;};for(eQ=cq;eQ<length&&((eR=eO.charCodeAt(eQ)),eR>=48&&eR<=57);eQ++ );if(eQ==cq){cw();};cq=eQ;};return +eO.slice(eM,cq);};if(eP){cw();};if(eO.slice(cq,cq+4)==k){cq+=4;return true;}else if(eO.slice(cq,cq+5)==m){cq+=5;return false;}else if(eO.slice(cq,cq+4)==bP){cq+=4;return null;};cw();};};return ca;};var cK=function(eU){var eT,eS;if(eU==ca){cw();};if(typeof eU==E){if((cE?eU.charAt(0):eU[0])==u){return eU.slice(1);};if(eU==cc){eT=[];for(;;eS||(eS=true)){eU=cS();if(eU==Y){break;};if(eS){if(eU==bO){eU=cS();if(eU==Y){cw();};}else {cw();};};if(eU==bO){cw();};eT.push(cK(eU));};return eT;}else if(eU==T){eT={};for(;;eS||(eS=true)){eU=cS();if(eU==F){break;};if(eS){if(eU==bO){eU=cS();if(eU==F){cw();};}else {cw();};};if(eU==bO||typeof eU!=E||(cE?eU.charAt(0):eU[0])!=u||cS()!=I){cw();};eT[eU.slice(1)]=cK(cS());};return eT;};cw();};return eU;};var cO=function(eV,eW,eX){var eY=ct(eV,eW,eX);if(eY===cC){delete eV[eW];}else {eV[eW]=eY;};};var ct=function(fa,fb,fd){var fc=fa[fb],length;if(typeof fc==ch&&fc){if(cr.call(fc)==cR){for(length=fc.length;length-- ;){cO(fc,length,fd);};}else {cQ(fc,function(fe){cO(fc,fe,fd);});};};return fd.call(fa,fb,fc);};cx.parse=function(ff,fi){var fg,fh;cq=0;cu=B+ff;fg=cK(cS());if(cS()!=ca){cw();};cq=cu=null;return fi&&cr.call(fi)==cV?ct((fh={},fh[B]=fg,fh),B,fi):fg;};};};if(cy){cp(function(){return cx;});};}(this));}());qx.lang.Json.stringify=window.JSON.stringify;qx.lang.Json.parse=window.JSON.parse;})();(function(){var a="session",b="head",c="none",d="qx",e="qx.bom.storage.UserData",f="div",g="local",h="qxtest",j="#default#userdata",k="display";qx.Bootstrap.define(e,{statics:{__local:null,__session:null,__id:0,getLocal:function(){if(this.__local){return this.__local;};return this.__local=new qx.bom.storage.UserData(g);},getSession:function(){if(this.__session){return this.__session;};return this.__session=new qx.bom.storage.UserData(a);}},construct:function(m){this.__el=document.createElement(f);this.__el.style[k]=c;document.getElementsByTagName(b)[0].appendChild(this.__el);this.__el.addBehavior(j);this.__storeName=m;this.__el.load(this.__storeName);this.__storage={};this.__reference={};var l=this.__el.getAttribute(d+qx.bom.storage.UserData.__id);while(l!=undefined){l=qx.lang.Json.parse(l);this.__storage[l.key]=l.value;this.__reference[l.key]=d+qx.bom.storage.UserData.__id;qx.bom.storage.UserData.__id++ ;l=this.__el.getAttribute(d+qx.bom.storage.UserData.__id);};},members:{__el:null,__storeName:h,__storage:null,__reference:null,getLength:function(){return Object.keys(this.__storage).length;},setItem:function(n,p){if(this.__reference[n]){var o=this.__reference[n];}else {var o=d+qx.bom.storage.UserData.__id;qx.bom.storage.UserData.__id++ ;};var q=qx.lang.Json.stringify({key:n,value:p});this.__el.setAttribute(o,q);this.__el.save(this.__storeName);this.__storage[n]=p;this.__reference[n]=o;},getItem:function(r){return this.__storage[r]||null;},removeItem:function(v){var u=this.__reference[v];if(u==undefined){return;};this.__el.removeAttribute(u);qx.bom.storage.UserData.__id-- ;delete this.__storage[v];delete this.__reference[v];var t=d+qx.bom.storage.UserData.__id;if(this.__el.getAttribute(t)){var w=this.__el.getAttribute(d+qx.bom.storage.UserData.__id);this.__el.removeAttribute(t);this.__el.setAttribute(u,w);var s=qx.lang.Json.parse(w).key;this.__reference[s]=u;};this.__el.save(this.__storeName);},clear:function(){for(var x in this.__reference){this.__el.removeAttribute(this.__reference[x]);};this.__el.save(this.__storeName);this.__storage={};this.__reference={};},getKey:function(y){return Object.keys(this.__storage)[y];},forEach:function(z,B){var length=this.getLength();for(var i=0;i<length;i++ ){var A=this.getKey(i);z.call(B,A,this.getItem(A));};}}});})();(function(){var a="qx.bom.storage.Memory";qx.Bootstrap.define(a,{statics:{__local:null,__session:null,getLocal:function(){if(this.__local){return this.__local;};return this.__local=new qx.bom.storage.Memory();},getSession:function(){if(this.__session){return this.__session;};return this.__session=new qx.bom.storage.Memory();}},construct:function(){this.__storage={};},members:{__storage:null,getLength:function(){return Object.keys(this.__storage).length;},setItem:function(b,c){c=qx.lang.Json.stringify(c);this.__storage[b]=c;},getItem:function(e){var d=this.__storage[e];if(qx.lang.Type.isString(d)){d=qx.lang.Json.parse(d);};return d;},removeItem:function(f){delete this.__storage[f];},clear:function(){this.__storage={};},getKey:function(h){var g=Object.keys(this.__storage);return g[h];},forEach:function(j,l){var length=this.getLength();for(var i=0;i<length;i++ ){var k=this.getKey(i);j.call(l,k,this.getItem(k));};}}});})();(function(){var a="function",b="getScreenTop",c="getViewportTop",d="getScreenLeft",e="longtap",f="qx.module.event.Tap",g="getViewportLeft",h="tap",j="getDocumentTop",k="getDocumentLeft",m="dbltap";qx.Bootstrap.define(f,{statics:{TYPES:[h,e,m],BIND_METHODS:[g,c,k,j,d,b],getViewportLeft:function(){return this._original.getViewportLeft();},getViewportTop:function(){return this._original.getViewportTop();},getDocumentLeft:function(){return this._original.getDocumentLeft();},getDocumentTop:function(){return this._original.getDocumentTop();},getScreenLeft:function(){return this._original.getScreenLeft();},getScreenTop:function(){return this._original.getScreenTop();},normalize:function(event,o){if(!event){return event;};var n=qx.module.event.Tap.BIND_METHODS;for(var i=0,l=n.length;i<l;i++ ){if(typeof event[n[i]]!=a){event[n[i]]=qx.module.event.Tap[n[i]].bind(event);};};return event;}},defer:function(p){qxWeb.$registerEventNormalization(qx.module.event.Tap.TYPES,p.normalize);}});})();(function(){var a="qx.module.event.TouchHandler",b="touchmove",c="touchcancel",d="touchstart",e="touchend";qx.Bootstrap.define(a,{statics:{TYPES:[d,e,b,c],register:function(f){if(!f.__touchHandler){if(!f.$$emitter){f.$$emitter=new qx.event.Emitter();};f.__touchHandler=new qx.event.handler.TouchCore(f,f.$$emitter);};},unregister:function(i){if(i.__touchHandler){if(!i.$$emitter){i.__touchHandler=null;}else {var h=false;var g=i.$$emitter.getListeners();qx.module.event.TouchHandler.TYPES.forEach(function(j){if(j in g&&g[j].length>0){h=true;};});if(!h){i.__touchHandler=null;};};};}},defer:function(k){qxWeb.$registerEventHook(k.TYPES,k.register,k.unregister);}});})();(function(){var a="touchmove",b="os.name",c="MSPointerDown",d="android",e="engine.version",f="pointercancel",g="qx.event.handler.TouchCore",h="event.mspointer",j="MSPointerCancel",k="y",l="pointer-events",m="pointerup",n="touchend",o="pointerdown",p="MSPointerUp",q="right",r="engine.name",s="undefined",t="touchcancel",u="MSPointerMove",v="webkit",w="none",z="left",A="pointermove",B="down",C="x",D="up",E="touchstart";qx.Bootstrap.define(g,{extend:Object,statics:{TAP_MAX_DISTANCE:qx.core.Environment.get(b)!=d?10:40,SWIPE_DIRECTION:{x:[z,q],y:[D,B]},SWIPE_MIN_DISTANCE:qx.core.Environment.get(b)!=d?11:41,SWIPE_MIN_VELOCITY:0,LONGTAP_TIME:500},construct:function(F,G){this.__target=F;this.__emitter=G;this._initTouchObserver();this.__pointers=[];this.__touchStartPosition={};},members:{__target:null,__emitter:null,__onTouchEventWrapper:null,__originalTarget:null,__touchStartPosition:null,__startTime:null,__beginScalingDistance:null,__beginRotation:null,__pointers:null,__touchEventNames:null,_initTouchObserver:function(){this.__onTouchEventWrapper=qx.lang.Function.listener(this._onTouchEvent,this);this.__touchEventNames=[E,a,n,t];if(qx.core.Environment.get(h)){var H=parseInt(qx.core.Environment.get(e),10);if(H==10){this.__touchEventNames=[c,u,p,j];}else {this.__touchEventNames=[o,A,m,f];};};for(var i=0;i<this.__touchEventNames.length;i++ ){qx.bom.Event.addNativeListener(this.__target,this.__touchEventNames[i],this.__onTouchEventWrapper);};},_stopTouchObserver:function(){for(var i=0;i<this.__touchEventNames.length;i++ ){qx.bom.Event.removeNativeListener(this.__target,this.__touchEventNames[i],this.__onTouchEventWrapper);};},_onTouchEvent:function(I){this._commonTouchEventHandler(I);},_getScalingDistance:function(K,J){return (Math.sqrt(Math.pow(K.pageX-J.pageX,2)+Math.pow(K.pageY-J.pageY,2)));},_getRotationAngle:function(M,L){var x=M.pageX-L.pageX;var y=M.pageY-L.pageY;return (Math.atan2(y,x)*180/Math.PI);},_calcTouchesDelta:function(N){var O=[];for(var i=0;i<N.length;i++ ){O.push(this._calcSingleTouchDelta(N[i]));};return O;},_calcSingleTouchDelta:function(S){if(this.__touchStartPosition.hasOwnProperty(S.identifier)){var R=this.__touchStartPosition[S.identifier];var P=Math.floor(S.clientX-R[0]);var Q=Math.floor(S.clientY-R[1]);var T=C;if(Math.abs(P/Q)<1){T=k;};return {"x":P,"y":Q,"axis":T,"identifier":S.identifier};}else {return {"x":0,"y":0,"axis":null,"identifier":S.identifier};};},_commonTouchEventHandler:function(V,ba){var ba=ba||V.type;if(qx.core.Environment.get(h)){ba=this._mapPointerEvent(ba);var U=this._detectTouchesByPointer(V,ba);V.changedTouches=U;V.targetTouches=U;V.touches=U;};V.delta=[];if(ba==E){this.__originalTarget=this._getTarget(V);if(V.touches&&V.touches.length>1){this.__beginScalingDistance=this._getScalingDistance(V.touches[0],V.touches[1]);this.__beginRotation=this._getRotationAngle(V.touches[0],V.touches[1]);};for(var i=0;i<V.changedTouches.length;i++ ){var Y=V.changedTouches[i];this.__touchStartPosition[Y.identifier]=[Y.clientX,Y.clientY];};};if(ba==a){if(typeof V.scale==s&&V.targetTouches.length>1){var W=this._getScalingDistance(V.targetTouches[0],V.targetTouches[1]);V.scale=W/this.__beginScalingDistance;};if((typeof V.rotation==s||qx.core.Environment.get(h))&&V.targetTouches.length>1){var X=this._getRotationAngle(V.targetTouches[0],V.targetTouches[1]);V._rotation=X-this.__beginRotation;};V.delta=this._calcTouchesDelta(V.targetTouches);};this._fireEvent(V,ba,this.__originalTarget);if(qx.core.Environment.get(h)){if(ba==n||ba==t){delete this.__pointers[V.pointerId];};};if((ba==n||ba==t)&&V.changedTouches[0]){delete this.__touchStartPosition[V.changedTouches[0].identifier];};},_detectTouchesByPointer:function(bd,bf){var bc=[];if(bf==E){this.__pointers[bd.pointerId]=bd;}else if(bf==a){this.__pointers[bd.pointerId]=bd;};for(var be in this.__pointers){var bb=this.__pointers[be];bc.push(bb);};return bc;},_mapPointerEvent:function(bg){bg=bg.toLowerCase();if(bg.indexOf(o)!==-1){return E;}else if(bg.indexOf(m)!==-1){return n;}else if(bg.indexOf(A)!==-1){return a;}else if(bg.indexOf(f)!==-1){return t;};return bg;},_getTarget:function(bi){var bj=qx.bom.Event.getTarget(bi);if(qx.core.Environment.get(r)==v){if(bj&&bj.nodeType==3){bj=bj.parentNode;};}else if(qx.core.Environment.get(h)){var bh=this.__evaluateTarget(bi);if(bh){bj=bh;};};return bj;},__evaluateTarget:function(bm){var bk=null;var bl=null;if(bm&&bm.touches&&bm.touches.length!==0){bk=bm.touches[0].clientX;bl=bm.touches[0].clientY;};var bo=document.msElementsFromPoint(bk,bl);if(bo){for(var i=0;i<bo.length;i++ ){var bp=bo[i];var bn=qx.bom.element.Style.get(bp,l,3);if(bn!=w){return bp;};};};return null;},_fireEvent:function(bq,br,bs){if(!bs){bs=this._getTarget(bq);};var br=br||bq.type;if(bs&&bs.nodeType&&this.__emitter){this.__emitter.emit(br,bq);};},dispose:function(){this._stopTouchObserver();this.__originalTarget=this.__target=this.__touchEventNames=this.__pointers=this.__emitter=this.__beginScalingDistance=this.__beginRotation=null;}}});})();(function(){var a="<div class='qx-blocker' />",b="fixed",c="block",d="px",e="undefined",f="qx.module.Blocker",g="browser.version",h=':first',i="absolute",j="browser.name",k="100%",l="ie",m='undefined';qxWeb.define(f,{statics:{__attachBlocker:function(s,p,n,r){var q=qxWeb.getWindow(s);var o=qxWeb.isDocument(s);if(!o&&!qxWeb.isElement(s)){return;};if(!s.__blocker){s.__blocker={div:qxWeb.create(a)};};if(o){s.__blocker.div.insertBefore(qxWeb(q.document.body).getChildren(h));}else {s.__blocker.div.appendTo(q.document.body);};qx.module.Blocker.__styleBlocker(s,p,n,r,o);},__styleBlocker:function(z,v,t,y,u){var w=qxWeb(z);var A={"display":c};A.backgroundColor=typeof v!==m?v:null;A.zIndex=typeof y!==m?y:null;if(qxWeb.env.get(j)===l&&qxWeb.env.get(g)<=8){A.opacity=typeof t!==m?t:0;}else {A.opacity=typeof t!==m?t:null;};if(u){A.top=0+d;A.left=0+d;A.position=b;A.width=k;A.height=k;}else {var x=w.getOffset();A.top=x.top+d;A.left=x.left+d;A.position=i;A.width=w.getWidth()+d;A.height=w.getHeight()+d;};z.__blocker.div.setStyles(A);},__detachBlocker:function(B,C){if(!B.__blocker){return;};B.__blocker.div.remove();},__getBlocker:function(D){var E=qxWeb();D.forEach(function(F,G){if(typeof F.__blocker!==e){E=E.concat(F.__blocker.div);};});return E;}},members:{block:function(H,I,J){if(!this[0]){return this;};this.forEach(function(K,L){qx.module.Blocker.__attachBlocker(K,H,I,J);});return this;},unblock:function(){if(!this[0]){return this;};this.forEach(qx.module.Blocker.__detachBlocker);return this;},getBlocker:function(){if(!this[0]){return this;};var M=qx.module.Blocker.__getBlocker(this);return M;}},defer:function(N){qxWeb.$attachAll(this);}});})();(function(){var a="activex",b="No XHR support available.",c="If-None-Match",d="xhr",f="If-Modified-Since",g="engine.version",h="onunload",i="GET",j="-1",k="qx.debug.io",l="HTMLDocument",m="error",n="loadend",o="Blob",p="load",q="abort",r="String",s="browser.documentmode",t="",u="engine.name",v="Microsoft.XMLHTTP",w="Already disposed",x="browser.version",y="opera",z="qx.bom.request.Xhr",A="Not enough arguments",B="timeout",C="gecko",D="If-Match",E="mshtml",F="readystatechange",G="Microsoft.XMLDOM",H="file:",I="FormData",J="If-Range",K="Content-Type",L="io.xhr",M="on",N="ArrayBuffer",O="undefined",P="Native XHR object doesn't support overrideMimeType.";qx.Bootstrap.define(z,{extend:Object,construct:function(){var Q=qx.Bootstrap.bind(this.__onNativeReadyStateChange,this);if(qx.event&&qx.event.GlobalError&&qx.event.GlobalError.observeMethod){this.__onNativeReadyStateChangeBound=qx.event.GlobalError.observeMethod(Q);}else {this.__onNativeReadyStateChangeBound=Q;};this.__onNativeAbortBound=qx.Bootstrap.bind(this.__onNativeAbort,this);this.__onTimeoutBound=qx.Bootstrap.bind(this.__onTimeout,this);this.__initNativeXhr();this._emitter=new qx.event.Emitter();if(window.attachEvent){this.__onUnloadBound=qx.Bootstrap.bind(this.__onUnload,this);window.attachEvent(h,this.__onUnloadBound);};},statics:{UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},events:{"readystatechange":z,"error":z,"loadend":z,"timeout":z,"abort":z,"load":z},members:{readyState:0,responseText:t,responseXML:null,status:0,statusText:t,timeout:0,open:function(V,R,S,U,T){this.__checkDisposed();if(typeof R===O){throw new Error(A);}else if(typeof V===O){V=i;};this.__abort=false;this.__send=false;this.__conditional=false;this.__url=R;if(typeof S==O){S=true;};this.__async=S;if(!this.__supportsManyRequests()&&this.readyState>qx.bom.request.Xhr.UNSENT){this.dispose();this.__initNativeXhr();};this.__nativeXhr.onreadystatechange=this.__onNativeReadyStateChangeBound;try{{};this.__nativeXhr.open(V,R,S,U,T);}catch(W){if(!qx.util.Request.isCrossDomain(R)){throw W;};if(!this.__async){this.__openError=W;};if(this.__async){if(window.XDomainRequest){this.readyState=4;this.__nativeXhr=new XDomainRequest();this.__nativeXhr.onerror=qx.Bootstrap.bind(function(){this._emit(F);this._emit(m);this._emit(n);},this);{};this.__nativeXhr.open(V,R,S,U,T);return;};window.setTimeout(qx.Bootstrap.bind(function(){if(this.__disposed){return;};this.readyState=4;this._emit(F);this._emit(m);this._emit(n);},this));};};if(qx.core.Environment.get(u)===E&&qx.core.Environment.get(s)<9&&this.__nativeXhr.readyState>0){this.__nativeXhr.setRequestHeader(f,j);};if(qx.core.Environment.get(u)===C&&parseInt(qx.core.Environment.get(g),10)<2&&!this.__async){this.readyState=qx.bom.request.Xhr.OPENED;this._emit(F);};},setRequestHeader:function(X,Y){this.__checkDisposed();if(X==D||X==f||X==c||X==J){this.__conditional=true;};this.__nativeXhr.setRequestHeader(X,Y);return this;},send:function(bb){this.__checkDisposed();if(!this.__async&&this.__openError){throw this.__openError;};if(qx.core.Environment.get(u)===y&&this.timeout===0){this.timeout=10000;};if(this.timeout>0){this.__timerId=window.setTimeout(this.__onTimeoutBound,this.timeout);};bb=typeof bb==O?null:bb;var ba=qx.Bootstrap.getClass(bb);bb=(bb!==null&&this.__dataTypeWhiteList.indexOf(ba)===-1)?bb.toString():bb;try{{};this.__nativeXhr.send(bb);}catch(bd){if(!this.__async){throw bd;};if(this._getProtocol()===H){this.readyState=2;this.__readyStateChange();var bc=this;window.setTimeout(function(){if(bc.__disposed){return;};bc.readyState=3;bc.__readyStateChange();bc.readyState=4;bc.__readyStateChange();});};};if(qx.core.Environment.get(u)===C&&!this.__async){this.__onNativeReadyStateChange();};this.__send=true;return this;},abort:function(){this.__checkDisposed();this.__abort=true;this.__nativeXhr.abort();if(this.__nativeXhr){this.readyState=this.__nativeXhr.readyState;};return this;},_emit:function(event){if(this[M+event]){this[M+event]();};this._emitter.emit(event,this);},onreadystatechange:function(){},onload:function(){},onloadend:function(){},onerror:function(){},onabort:function(){},ontimeout:function(){},on:function(name,be,bf){this._emitter.on(name,be,bf);return this;},getResponseHeader:function(bg){this.__checkDisposed();if(qx.core.Environment.get(s)===9&&this.__nativeXhr.aborted){return t;};return this.__nativeXhr.getResponseHeader(bg);},getAllResponseHeaders:function(){this.__checkDisposed();if(qx.core.Environment.get(s)===9&&this.__nativeXhr.aborted){return t;};return this.__nativeXhr.getAllResponseHeaders();},overrideMimeType:function(bh){this.__checkDisposed();if(this.__nativeXhr.overrideMimeType){this.__nativeXhr.overrideMimeType(bh);}else {throw new Error(P);};return this;},getRequest:function(){return this.__nativeXhr;},dispose:function(){if(this.__disposed){return false;};window.clearTimeout(this.__timerId);if(window.detachEvent){window.detachEvent(h,this.__onUnloadBound);};try{this.__nativeXhr.onreadystatechange;}catch(bj){return false;};var bi=function(){};this.__nativeXhr.onreadystatechange=bi;this.__nativeXhr.onload=bi;this.__nativeXhr.onerror=bi;this.abort();this.__nativeXhr=null;this.__disposed=true;return true;},isDisposed:function(){return !!this.__disposed;},_createNativeXhr:function(){var bk=qx.core.Environment.get(L);if(bk===d){return new XMLHttpRequest();};if(bk==a){return new window.ActiveXObject(v);};qx.Bootstrap.error(this,b);},_getProtocol:function(){var bl=this.__url;var bm=/^(\w+:)\/\//;if(bl!==null&&bl.match){var bn=bl.match(bm);if(bn&&bn[1]){return bn[1];};};return window.location.protocol;},__nativeXhr:null,__async:null,__onNativeReadyStateChangeBound:null,__onNativeAbortBound:null,__onUnloadBound:null,__onTimeoutBound:null,__send:null,__url:null,__abort:null,__timeout:null,__disposed:null,__timerId:null,__openError:null,__conditional:null,__dataTypeWhiteList:null,__initNativeXhr:function(){this.__nativeXhr=this._createNativeXhr();this.__nativeXhr.onreadystatechange=this.__onNativeReadyStateChangeBound;if(this.__nativeXhr.onabort){this.__nativeXhr.onabort=this.__onNativeAbortBound;};this.__disposed=this.__send=this.__abort=false;this.__dataTypeWhiteList=[N,o,l,r,I];},__onNativeAbort:function(){if(!this.__abort){this.abort();};},__onNativeReadyStateChange:function(){var bo=this.__nativeXhr,bp=true;{};if(this.readyState==bo.readyState){return;};this.readyState=bo.readyState;if(this.readyState===qx.bom.request.Xhr.DONE&&this.__abort&&!this.__send){return;};if(!this.__async&&(bo.readyState==2||bo.readyState==3)){return;};this.status=0;this.statusText=this.responseText=t;this.responseXML=null;if(this.readyState>=qx.bom.request.Xhr.HEADERS_RECEIVED){try{this.status=bo.status;this.statusText=bo.statusText;this.responseText=bo.responseText;this.responseXML=bo.responseXML;}catch(bq){bp=false;};if(bp){this.__normalizeStatus();this.__normalizeResponseXML();};};this.__readyStateChange();if(this.readyState==qx.bom.request.Xhr.DONE){if(bo){bo.onreadystatechange=function(){};};};},__readyStateChange:function(){if(this.readyState===qx.bom.request.Xhr.DONE){window.clearTimeout(this.__timerId);};this._emit(F);if(this.readyState===qx.bom.request.Xhr.DONE){this.__readyStateChangeDone();};},__readyStateChangeDone:function(){if(this.__timeout){this._emit(B);if(qx.core.Environment.get(u)===y){this._emit(m);};this.__timeout=false;}else {if(this.__abort){this._emit(q);}else {if(this.__isNetworkError()){this._emit(m);}else {this._emit(p);};};};this._emit(n);},__isNetworkError:function(){var br;if(this._getProtocol()===H){br=!this.responseText;}else {br=!this.statusText&&this.status!==204;};return br;},__onTimeout:function(){var bs=this.__nativeXhr;this.readyState=qx.bom.request.Xhr.DONE;this.__timeout=true;bs.aborted=true;bs.abort();this.responseText=t;this.responseXML=null;this.__readyStateChange();},__normalizeStatus:function(){var bt=this.readyState===qx.bom.request.Xhr.DONE;if(this._getProtocol()===H&&this.status===0&&bt){if(!this.__isNetworkError()){this.status=200;};};if(this.status===1223){this.status=204;};if(qx.core.Environment.get(u)===y){if(bt&&this.__conditional&&!this.__abort&&this.status===0){this.status=304;};};},__normalizeResponseXML:function(){if(qx.core.Environment.get(u)==E&&(this.getResponseHeader(K)||t).match(/[^\/]+\/[^\+]+\+xml/)&&this.responseXML&&!this.responseXML.documentElement){var bu=new window.ActiveXObject(G);bu.async=false;bu.validateOnParse=false;bu.loadXML(this.responseText);this.responseXML=bu;};},__onUnload:function(){try{if(this){this.dispose();};}catch(e){};},__supportsManyRequests:function(){var name=qx.core.Environment.get(u);var bv=qx.core.Environment.get(x);return !(name==E&&bv<9||name==C&&bv<3.5);},__checkDisposed:function(){if(this.__disposed){throw new Error(w);};}},defer:function(){qx.core.Environment.add(k,false);}});})();(function(){var a="HEAD",b="CONNECT",c="OPTIONS",d="PUT",e="GET",f="PATCH",g="//",h="DELETE",i="POST",j="TRACE",k="qx.util.Request";qx.Bootstrap.define(k,{statics:{isCrossDomain:function(l){var n=qx.util.Uri.parseUri(l),location=window.location;if(!location){return false;};var m=location.protocol;if(!(l.indexOf(g)!==-1)){return false;};if(m.substr(0,m.length-1)==n.protocol&&location.host===n.host&&location.port===n.port){return false;};return true;},isSuccessful:function(status){return (status>=200&&status<300||status===304);},isMethod:function(p){var o=[e,i,d,h,a,c,j,b,f];return (o.indexOf(p)!==-1)?true:false;},methodAllowsRequestBody:function(q){return !((/^(GET|HEAD)$/).test(q));}}});})();(function(){var a="Microsoft.XMLHTTP",b="xhr",c="io.ssl",d="io.xhr",e="",f="file:",g="https:",h="webkit",i="gecko",j="activex",k="opera",l=".",m="io.maxrequests",n="qx.bom.client.Transport";qx.Bootstrap.define(n,{statics:{getMaxConcurrentRequestCount:function(){var p;var r=qx.bom.client.Engine.getVersion().split(l);var o=0;var s=0;var q=0;if(r[0]){o=r[0];};if(r[1]){s=r[1];};if(r[2]){q=r[2];};if(window.maxConnectionsPerServer){p=window.maxConnectionsPerServer;}else if(qx.bom.client.Engine.getName()==k){p=8;}else if(qx.bom.client.Engine.getName()==h){p=4;}else if(qx.bom.client.Engine.getName()==i&&((o>1)||((o==1)&&(s>9))||((o==1)&&(s==9)&&(q>=1)))){p=6;}else {p=2;};return p;},getSsl:function(){return window.location.protocol===g;},getXmlHttpRequest:function(){var t=window.ActiveXObject?(function(){if(window.location.protocol!==f){try{new window.XMLHttpRequest();return b;}catch(u){};};try{new window.ActiveXObject(a);return j;}catch(v){};})():(function(){try{new window.XMLHttpRequest();return b;}catch(w){};})();return t||e;}},defer:function(x){qx.core.Environment.add(m,x.getMaxConcurrentRequestCount);qx.core.Environment.add(c,x.getSsl);qx.core.Environment.add(d,x.getXmlHttpRequest);}});})();(function(){var a="io",b="qxWeb.$$",c="get",d="qx.module.Io";qx.Bootstrap.define(d,{statics:{xhr:function(e,i){if(!i){i={};};var g=new qx.bom.request.Xhr();g.open(i.method,e,i.async);if(i.header){var f=i.header;for(var h in f){g.setRequestHeader(h,f[h]);};};return g;},script:function(j){var k=new qx.bom.request.Script();k.open(c,j);return k;},jsonp:function(l,m){var n=new qx.bom.request.Jsonp();if(m&&m.callbackName){n.setCallbackName(m.callbackName);};if(m&&m.callbackParam){n.setCallbackParam(m.callbackParam);};n.setPrefix(b);n.open(c,l);return n;}},defer:function(o){qxWeb.$attachAll(this,a);}});})();(function(){var a="url: ",b="qx.debug.io",c="qx.bom.request.Script",d="Invalid state",e="head",f="error",g="loadend",h="qx.debug",i="script",j="load",k="Unknown response headers",l="browser.documentmode",m="abort",n="",o="Received native readyState: loaded",p="readystatechange",q="Response header cannot be determined for ",r="requests made with script transport.",s="opera",t="unknown",u="Open native request with ",v="Response headers cannot be determined for",w="mshtml",x="engine.name",y="Detected error",z="Send native request",A="on",B="timeout",C="Unknown environment key at this phase",D="Received native load";qx.Bootstrap.define(c,{construct:function(){this.__initXhrProperties();this.__onNativeLoadBound=qx.Bootstrap.bind(this._onNativeLoad,this);this.__onNativeErrorBound=qx.Bootstrap.bind(this._onNativeError,this);this.__onTimeoutBound=qx.Bootstrap.bind(this._onTimeout,this);this.__headElement=document.head||document.getElementsByTagName(e)[0]||document.documentElement;this._emitter=new qx.event.Emitter();this.timeout=this.__supportsErrorHandler()?0:15000;},events:{"readystatechange":c,"error":c,"loadend":c,"timeout":c,"abort":c,"load":c},members:{readyState:null,status:null,statusText:null,timeout:null,__determineSuccess:null,on:function(name,E,F){this._emitter.on(name,E,F);return this;},open:function(H,G){if(this.__disposed){return;};this.__initXhrProperties();this.__abort=null;this.__url=G;if(this.__environmentGet(b)){qx.Bootstrap.debug(qx.bom.request.Script,u+a+G);};this._readyStateChange(1);},setRequestHeader:function(I,J){if(this.__disposed){return null;};var K={};if(this.readyState!==1){throw new Error(d);};K[I]=J;this.__url=qx.util.Uri.appendParamsToUrl(this.__url,K);return this;},send:function(){if(this.__disposed){return null;};var M=this.__createScriptElement(),L=this.__headElement,N=this;if(this.timeout>0){this.__timeoutId=window.setTimeout(this.__onTimeoutBound,this.timeout);};if(this.__environmentGet(b)){qx.Bootstrap.debug(qx.bom.request.Script,z);};L.insertBefore(M,L.firstChild);window.setTimeout(function(){N._readyStateChange(2);N._readyStateChange(3);});return this;},abort:function(){if(this.__disposed){return null;};this.__abort=true;this.__disposeScriptElement();this._emit(m);return this;},_emit:function(event){this[A+event]();this._emitter.emit(event,this);},onreadystatechange:function(){},onload:function(){},onloadend:function(){},onerror:function(){},onabort:function(){},ontimeout:function(){},getResponseHeader:function(O){if(this.__disposed){return null;};if(this.__environmentGet(h)){qx.Bootstrap.debug(q+r);};return t;},getAllResponseHeaders:function(){if(this.__disposed){return null;};if(this.__environmentGet(h)){qx.Bootstrap.debug(v+r);};return k;},setDetermineSuccess:function(P){this.__determineSuccess=P;},dispose:function(){var Q=this.__scriptElement;if(!this.__disposed){if(Q){Q.onload=Q.onreadystatechange=null;this.__disposeScriptElement();};if(this.__timeoutId){window.clearTimeout(this.__timeoutId);};this.__disposed=true;};},isDisposed:function(){return !!this.__disposed;},_onTimeout:function(){this.__failure();if(!this.__supportsErrorHandler()){this._emit(f);};this._emit(B);if(!this.__supportsErrorHandler()){this._emit(g);};},_onNativeLoad:function(){var S=this.__scriptElement,R=this.__determineSuccess,T=this;if(this.__abort){return;};if(this.__environmentGet(x)===w&&this.__environmentGet(l)<9){if(!(/loaded|complete/).test(S.readyState)){return;}else {if(this.__environmentGet(b)){qx.Bootstrap.debug(qx.bom.request.Script,o);};};};if(this.__environmentGet(b)){qx.Bootstrap.debug(qx.bom.request.Script,D);};if(R){if(!this.status){this.status=R()?200:500;};};if(this.status===500){if(this.__environmentGet(b)){qx.Bootstrap.debug(qx.bom.request.Script,y);};};if(this.__timeoutId){window.clearTimeout(this.__timeoutId);};window.setTimeout(function(){T._success();T._readyStateChange(4);T._emit(j);T._emit(g);});},_onNativeError:function(){this.__failure();this._emit(f);this._emit(g);},__scriptElement:null,__headElement:null,__url:n,__onNativeLoadBound:null,__onNativeErrorBound:null,__onTimeoutBound:null,__timeoutId:null,__abort:null,__disposed:null,__initXhrProperties:function(){this.readyState=0;this.status=0;this.statusText=n;},_readyStateChange:function(U){this.readyState=U;this._emit(p);},_success:function(){this.__disposeScriptElement();this.readyState=4;if(!this.status){this.status=200;};this.statusText=n+this.status;},__failure:function(){this.__disposeScriptElement();this.readyState=4;this.status=0;this.statusText=null;},__supportsErrorHandler:function(){var W=this.__environmentGet(x)===w&&this.__environmentGet(l)<9;var V=this.__environmentGet(x)===s;return !(W||V);},__createScriptElement:function(){var X=this.__scriptElement=document.createElement(i);X.src=this.__url;X.onerror=this.__onNativeErrorBound;X.onload=this.__onNativeLoadBound;if(this.__environmentGet(x)===w&&this.__environmentGet(l)<9){X.onreadystatechange=this.__onNativeLoadBound;};return X;},__disposeScriptElement:function(){var Y=this.__scriptElement;if(Y&&Y.parentNode){this.__headElement.removeChild(Y);};},__environmentGet:function(ba){if(qx&&qx.core&&qx.core.Environment){return qx.core.Environment.get(ba);}else {if(ba===x){return qx.bom.client.Engine.getName();};if(ba===l){return qx.bom.client.Browser.getDocumentMode();};if(ba==b){return false;};throw new Error(C);};}},defer:function(){if(qx&&qx.core&&qx.core.Environment){qx.core.Environment.add(b,false);};}});})();(function(){var a="qx.bom.request.Jsonp",b="callback",c="open",d="dispose",e="",f="_onNativeLoad",g="qx",h=".callback",i="qx.bom.request.Jsonp.";qx.Bootstrap.define(a,{extend:qx.bom.request.Script,construct:function(){qx.bom.request.Script.apply(this);this.__generateId();},members:{responseJson:null,__id:null,__callbackParam:null,__callbackName:null,__callbackCalled:null,__customCallbackCreated:null,__generatedUrl:null,__disposed:null,__prefix:e,open:function(o,k){if(this.__disposed){return;};var m={},l,n,j=this;this.responseJson=null;this.__callbackCalled=false;l=this.__callbackParam||b;n=this.__callbackName||this.__prefix+i+this.__id+h;if(!this.__callbackName){this.constructor[this.__id]=this;}else {if(!window[this.__callbackName]){this.__customCallbackCreated=true;window[this.__callbackName]=function(p){j.callback(p);};}else {{};};};{};m[l]=n;this.__generatedUrl=k=qx.util.Uri.appendParamsToUrl(k,m);this.__callBase(c,[o,k]);},callback:function(q){if(this.__disposed){return;};this.__callbackCalled=true;{};this.responseJson=q;this.constructor[this.__id]=undefined;this.__deleteCustomCallback();},setCallbackParam:function(r){this.__callbackParam=r;return this;},setCallbackName:function(name){this.__callbackName=name;return this;},setPrefix:function(s){this.__prefix=s;},getGeneratedUrl:function(){return this.__generatedUrl;},dispose:function(){this.__deleteCustomCallback();this.__callBase(d);},_onNativeLoad:function(){this.status=this.__callbackCalled?200:500;this.__callBase(f);},__deleteCustomCallback:function(){if(this.__customCallbackCreated&&window[this.__callbackName]){window[this.__callbackName]=undefined;this.__customCallbackCreated=false;};},__callBase:function(u,t){qx.bom.request.Script.prototype[u].apply(this,t||[]);},__generateId:function(){this.__id=g+(new Date().valueOf())+(e+Math.random()).substring(2,5);}}});})();(function(){var a="align-start",b="align-end",c="qx.util.placement.AbstractAxis",d="edge-start",e="align-center",f="abstract method call!",g="edge-end";qx.Bootstrap.define(c,{extend:Object,statics:{computeStart:function(j,k,l,h,i){throw new Error(f);},_moveToEdgeAndAlign:function(n,o,p,m){switch(m){case d:return o.start-p.end-n;case g:return o.end+p.start;case a:return o.start+p.start;case e:return o.start+parseInt((o.end-o.start-n)/2,10)+p.start;case b:return o.end-p.end-n;};},_isInRange:function(r,s,q){return r>=0&&r+s<=q;}}});})();(function(){var a="qx.util.placement.DirectAxis";qx.Bootstrap.define(a,{statics:{_moveToEdgeAndAlign:qx.util.placement.AbstractAxis._moveToEdgeAndAlign,computeStart:function(d,e,f,b,c){return this._moveToEdgeAndAlign(d,e,f,c);}}});})();(function(){var a="qx.util.placement.KeepAlignAxis",b="edge-start",c="edge-end";qx.Bootstrap.define(a,{statics:{_moveToEdgeAndAlign:qx.util.placement.AbstractAxis._moveToEdgeAndAlign,_isInRange:qx.util.placement.AbstractAxis._isInRange,computeStart:function(k,f,g,d,j){var i=this._moveToEdgeAndAlign(k,f,g,j);var e,h;if(this._isInRange(i,k,d)){return i;};if(j==b||j==c){e=f.start-g.end;h=f.end+g.start;}else {e=f.end-g.end;h=f.start+g.start;};if(e>d-h){i=Math.max(0,e-k);}else {i=h;};return i;}}});})();(function(){var a="qx.util.placement.BestFitAxis";qx.Bootstrap.define(a,{statics:{_isInRange:qx.util.placement.AbstractAxis._isInRange,_moveToEdgeAndAlign:qx.util.placement.AbstractAxis._moveToEdgeAndAlign,computeStart:function(g,c,d,b,f){var e=this._moveToEdgeAndAlign(g,c,d,f);if(this._isInRange(e,g,b)){return e;};if(e<0){e=Math.min(0,b-g);};if(e+g>b){e=Math.max(0,b-g);};return e;}}});})();(function(){var a="-",b="best-fit",c="edge-start",d="align-end",e="bottom",f="static",g="border-left-width",h="keep-align",i="center",j="direct",k="relative",l="middle",m="px",n="align-start",o="qx.module.Placement",p="border-top-width",q="top",r="right",s="edge-end",t="block",u="position",v="align-center",w="hidden",x="left",y="absolute";qxWeb.define(o,{statics:{_getAxis:function(z){switch(z){case h:return qx.util.placement.KeepAlignAxis;case b:return qx.util.placement.BestFitAxis;case j:default:return qx.util.placement.DirectAxis;};},_computePlacement:function(C,F,D,A,B,E){var G=C.x.computeStart(F.width,{start:A.left,end:A.right},{start:B.left,end:B.right},D.width,E.x);var top=C.y.computeStart(F.height,{start:A.top,end:A.bottom},{start:B.top,end:B.bottom},D.height,E.y);return {left:G,top:top};},_getPositionX:function(I,H){if(I==x){return c;}else if(I==r){return s;}else if(H==x){return n;}else if(H==i){return v;}else if(H==r){return d;};},_getPositionY:function(K,J){if(K==q){return c;}else if(K==e){return s;}else if(J==q){return n;}else if(J==l){return v;}else if(J==e){return d;};}},members:{placeTo:function(bc,bh,U,V,W){if(!this[0]||!bc){return this;};bc=qxWeb(bc);var M=this.isRendered()&&this[0].offsetWidth>0&&this[0].offsetHeight>0;var L=null;var bb=null;if(!M){L=this[0].style.display;bb=this[0].style.visibility;this.setStyles({position:y,visibility:w,display:t});};var R={x:qx.module.Placement._getAxis(V),y:qx.module.Placement._getAxis(W)};var P={width:this.getWidth(),height:this.getHeight()};var parent=this.getParents();var Q={width:parent.getWidth(),height:parent.getHeight()};U=U||{top:0,right:0,bottom:0,left:0};var T=bh.split(a);var be=T[0];var bd=T[1];var bg={x:qx.module.Placement._getPositionX(be,bd),y:qx.module.Placement._getPositionY(be,bd)};var Y;var O=parent.getStyle(u);if(O==k||O==f){Y=bc.getOffset();}else {var bf=bc.getPosition();Y={top:bf.top,bottom:bf.top+bc.getHeight(),left:bf.left,right:bf.left+bc.getWidth()};};var S=qx.module.Placement._computePlacement(R,P,Q,Y,U,bg);while(parent.length>0){if(parent.getStyle(u)==k){var X=parent.getOffset();var ba=parseInt(parent.getStyle(p))||0;var N=parseInt(parent.getStyle(g))||0;S.left-=(X.left+N);S.top-=(X.top+ba);parent=[];}else {parent=parent.getParents();};};if(!M){this[0].style.display=L;this[0].style.visibility=bb;};this.setStyles({position:y,left:S.left+m,top:S.top+m});return this;}},defer:function(bi){qxWeb.$attachAll(this);}});})();(function(){var a="qx.module.event.OrientationHandler",b="The 'orientationchange' event is only available on window objects!",c="orientationchange";qx.Bootstrap.define(a,{statics:{TYPES:[c],register:function(d){if(!qx.dom.Node.isWindow(d)){throw new Error(b);};if(!d.__orientationHandler){if(!d.$$emitter){d.$$emitter=new qx.event.Emitter();};d.__orientationHandler=new qx.event.handler.OrientationCore(d,d.$$emitter);};},unregister:function(g){if(g.__orientationHandler){if(!g.$$emitter){g.__orientationHandler=null;}else {var f=false;var e=g.$$emitter.getListeners();qx.module.event.OrientationHandler.TYPES.forEach(function(h){if(h in e&&e[h].length>0){f=true;};});if(!f){g.__orientationHandler=null;};};};}},defer:function(i){qxWeb.$registerEventHook(i.TYPES,i.register,i.unregister);}});})();(function(){var a="qx.event.handler.OrientationCore",b="orientationchange",c="landscape",d="resize",e="portrait";qx.Bootstrap.define(a,{extend:Object,construct:function(g,f){this._window=g||window;this.__emitter=f;this._initObserver();},members:{__emitter:null,_window:null,_currentOrientation:null,__onNativeWrapper:null,__nativeEventType:null,_initObserver:function(){this.__onNativeWrapper=qx.lang.Function.listener(this._onNative,this);this.__nativeEventType=qx.bom.Event.supportsEvent(this._window,b)?b:d;qx.bom.Event.addNativeListener(this._window,this.__nativeEventType,this.__onNativeWrapper);},_stopObserver:function(){qx.bom.Event.removeNativeListener(this._window,this.__nativeEventType,this.__onNativeWrapper);},_onNative:function(h){var i=qx.bom.Viewport.getOrientation();if(this._currentOrientation!=i){this._currentOrientation=i;var j=qx.bom.Viewport.isLandscape()?c:e;h._orientation=i;h._mode=j;if(this.__emitter){this.__emitter.emit(b,h);};};}},destruct:function(){this._stopObserver();this.__manager=this.__emitter=null;}});})();(function(){var a="qx.util.Function",b="undefined";qx.Bootstrap.define(a,{statics:{debounce:function(d,c,e){var f=function(){arguments.callee.immediate=!!(e);arguments.callee.args=qx.lang.Array.fromArguments(arguments);var g=this;var i=arguments.callee.intervalId;if(typeof i===b){var h=window.setInterval((function(){if(!this.fired){window.clearInterval(this.intervalId);delete this.intervalId;if(this.immediate===false){d.apply(g,this.args);};};this.fired=false;}).bind(arguments.callee),c);arguments.callee.intervalId=h;if(arguments.callee.immediate){d.apply(g,arguments.callee.args);};};arguments.callee.fired=true;};return f;},throttle:function(n,m,q){if(typeof q===b){q={};};var k,l,o;var p=null;var r=0;var j=function(){r=q.leading===false?0:new Date();p=null;o=n.apply(k,l);};return function(){var s=new Date();if(!r&&q.leading===false){r=s;};var t=m-(s-r);k=this;l=arguments;if(t<=0){window.clearTimeout(p);p=null;r=s;o=n.apply(k,l);}else if(!p&&q.trailing!==false){p=window.setTimeout(j,t);};return o;};}}});})();(function(){var a="qx.module.util.Function",b="func";qx.Bootstrap.define(a,{statics:{debounce:qx.util.Function.debounce,throttle:qx.util.Function.throttle},defer:function(c){qxWeb.$attachAll(this,b);}});})();(function(){var a="qx.module.Core";qx.Bootstrap.define(a,{});})();(function(){var a="rest",b="qx.module.Rest";qx.Bootstrap.define(b,{statics:{resource:function(c){return new qx.bom.rest.Resource(c);}},defer:function(d){qxWeb.$attachAll(this,a);}});})();(function(){var a="function",b="=?(\\w+)?}",c="' is invalid",d="loadEnd",f="Started",g="Parameter '",h="GET",i="sent",j="Method with name of action (",k="'",l="error",m="Content-Type",n="onreadystatechange",o="get",p="{",q="Sent",r="success",s=") doesn't support other HTTP methods than 'GET'",t=") already exists",u="Error",v="",w="qx.bom.rest.Resource",x="Request (",y="Missing parameter '",z="Success",A="readystatechange",B="started",C="fail",D="undefined",E="No route for action ";qx.Bootstrap.define(w,{extend:qx.event.Emitter,construct:function(F){this.__requests={};this.__routes={};this.__pollTimers={};this.__longPollHandlers={};try{if(typeof F!==D){{};this.__mapFromDescription(F);};}catch(e){this.dispose();throw e;};},events:{"success":w,"actionSuccess":w,"error":w,"actionError":w,"sent":w,"actionSent":w,"started":w,"actionStarted":w},statics:{POLL_THROTTLE_LIMIT:100,POLL_THROTTLE_COUNT:30,REQUIRED:true,placeholdersFromUrl:function(G){var I=/\{(\w+)(=\w+)?\}/g,J,H=[];while((J=I.exec(G))){H.push(J[1]);};return H;}},members:{__requests:null,__routes:null,__baseUrl:null,__pollTimers:null,__longPollHandlers:null,__configureRequestCallback:null,__requestHandler:null,__begetRequest:null,setRequestFactory:function(K){this.__begetRequest=K;},setRequestHandler:function(L){this.__requestHandler=L;},_getRequestHandler:function(){return (this.__requestHandler===null)?{onsuccess:{callback:function(N,M){return function(){var O={"id":parseInt(N.toHashCode(),10),"response":N.getResponse(),"request":N,"action":M};this.emit(M+z,O);this.emit(r,O);};},context:this},onfail:{callback:function(Q,P){return function(){var R={"id":parseInt(Q.toHashCode(),10),"response":Q.getResponse(),"request":Q,"action":P};this.emit(P+u,R);this.emit(l,R);};},context:this},onloadend:{callback:function(T,S){return function(){window.setTimeout(function(){T.dispose();},0);};},context:this},onreadystatechange:{callback:function(V,U){return function(){if(V.getTransport().readyState===qx.bom.request.Xhr.HEADERS_RECEIVED){var W={"id":parseInt(V.toHashCode(),10),"request":V,"action":U};this.emit(U+q,W);this.emit(i,W);};if(V.getTransport().readyState===qx.bom.request.Xhr.OPENED){var X={"id":parseInt(V.toHashCode(),10),"request":V,"action":U};this.emit(U+f,X);this.emit(B,X);};};},context:this}}:this.__requestHandler;},getRequestsByAction:function(Y){var ba=(this.__requests!==null&&Y in this.__requests);return ba?this.__requests[Y]:null;},configureRequest:function(bb){this.__configureRequestCallback=bb;},_getRequest:function(){return (this.__begetRequest===null)?new qx.bom.request.SimpleXhr():this.__begetRequest();},__createRequest:function(bc){var bd=this._getRequest();if(!qx.lang.Type.isArray(this.__requests[bc])){this.__requests[bc]=[];};this.__requests[bc].push(bd);return bd;},map:function(bf,bh,be,bg){this.__routes[bf]=[bh,be,bg];this.__requests[bf]=[];if(bf==o){this[bf]=undefined;};if(typeof this[bf]!==D&&this[bf]!==null&&this[bf].action!==true){throw new Error(j+bf+t);};this.__declareEvent(bf+z);this.__declareEvent(bf+u);this[bf]=qx.lang.Function.bind(function(){Array.prototype.unshift.call(arguments,bf);return this.invoke.apply(this,arguments);},this);this[bf].action=true;},invoke:function(bm,bk,bi){var bj=this.__createRequest(bm),bk=bk==null?{}:bk,bn=this._getRequestConfig(bm,bk);this.__routes[bm].params=bk;this.__checkParameters(bk,bn.check);this.__configureRequest(bj,bn,bi);if(this.__configureRequestCallback){this.__configureRequestCallback.call(this,bj,bm,bk,bi);};this.__configureJsonRequest(bj,bn,bi);var bl=this._getRequestHandler();bj.addListenerOnce(r,bl.onsuccess.callback(bj,bm),bl.onsuccess.context);bj.addListenerOnce(C,bl.onfail.callback(bj,bm),bl.onfail.context);bj.addListenerOnce(d,bl.onloadend.callback(bj,bm),bl.onloadend.context);if(bl.hasOwnProperty(n)){bj.addListener(A,bl.onreadystatechange.callback(bj,bm),bl.onreadystatechange.context);};bj.send();return parseInt(bj.toHashCode(),10);},setBaseUrl:function(bo){this.__baseUrl=bo;},__checkParameters:function(bp,bq){if(typeof bq!==D){{};Object.keys(bq).forEach(function(br){{};if(bq[br]===qx.bom.rest.Resource.REQUIRED&&typeof bp[br]===D){throw new Error(y+br+k);};if(!(bq[br]&&typeof bq[br].test==a)){return;};if(!bq[br].test(bp[br])){throw new Error(g+br+c);};});};},__configureRequest:function(bt,bs,bu){bt.setUrl(bs.url);if(!bt.setMethod&&bs.method!==h){throw new Error(x+bt.classname+s);};if(bt.setMethod){bt.setMethod(bs.method);};if(bu){bt.setRequestData(bu);};},__configureJsonRequest:function(bx,bv,bw){if(bw){var by=bx.getRequestHeader(m);if(bx.getMethod&&qx.util.Request.methodAllowsRequestBody(bx.getMethod())){if(/application\/.*\+?json/.test(by)){bw=qx.lang.Json.stringify(bw);bx.setRequestData(bw);};};};},abort:function(bB){if(qx.lang.Type.isNumber(bB)){var bE=bB;var bD=qx.core.ObjectRegistry.getPostId();var bz=qx.core.ObjectRegistry.fromHashCode(bE+bD);if(bz){bz.abort();};}else {var bC=bB;var bA=this.__requests[bC];if(this.__requests[bC]){bA.forEach(function(bF){bF.abort();});};};},refresh:function(bG){this.invoke(bG,this.__routes[bG].params);},poll:function(bH,bJ,bK,bI){if(this.__pollTimers[bH]){this.stopPollByAction(bH);};if(typeof bK==D){bK=this.__routes[bH].params;};if(bI){this.invoke(bH,bK);};var bL=(function(bM){return function(){var bN=bM.__requests[bH][0];if(!bI&&!bN){bM.invoke(bH,bK);return;};if(bN.isDone()||bN.isDisposed()){bM.refresh(bH);};};})(this);this._startPoll(bH,bL,bJ);},_startPoll:function(bO,bP,bQ){this.__pollTimers[bO]={"id":window.setInterval(bP,bQ),"interval":bQ,"listener":bP};},stopPollByAction:function(bR){if(bR in this.__pollTimers){var bS=this.__pollTimers[bR].id;window.clearInterval(bS);};},restartPollByAction:function(bT){if(bT in this.__pollTimers){var bU=this.__pollTimers[bT];this.stopPollByAction(bT);this._startPoll(bT,bU.listener,bU.interval);};},longPoll:function(ca){var bW=this,bY,cb=0;function bV(){var cc=bY&&((new Date())-bY)<bW._getThrottleLimit();if(cc){cb+=1;if(cb>bW._getThrottleCount()){{};return true;};};if(!cc){cb=0;};return false;};var bX=this.__longPollHandlers[ca]=this.addListener(ca+z,function cd(){if(bW.isDisposed()){return;};if(!bV()){bY=new Date();bW.refresh(ca);};});this.invoke(ca);return bX;},_getRequestConfig:function(ci,ch){var cf=this.__routes[ci];var ch=qx.lang.Object.clone(ch);if(!qx.lang.Type.isArray(cf)){throw new Error(E+ci);};var cg=cf[0],ce=this.__baseUrl!==null?this.__baseUrl+cf[1]:cf[1],ck=cf[2],cj=qx.bom.rest.Resource.placeholdersFromUrl(ce);ch=ch||{};cj.forEach(function(cn){var cl=new RegExp(p+cn+b),cm=ce.match(cl)[1];if(typeof ch[cn]===D){if(cm){ch[cn]=cm;}else {ch[cn]=v;};};ce=ce.replace(cl,ch[cn]);});return {method:cg,url:ce,check:ck};},_getThrottleLimit:function(){return qx.bom.rest.Resource.POLL_THROTTLE_LIMIT;},_getThrottleCount:function(){return qx.bom.rest.Resource.POLL_THROTTLE_COUNT;},__mapFromDescription:function(co){Object.keys(co).forEach(function(cq){var cr=co[cq],ct=cr.method,cp=cr.url,cs=cr.check;{};this.map(cq,ct,cp,cs);},this);},__declareEvent:function(cu){if(!this.constructor.$$events){this.constructor.$$events={};};if(!this.constructor.$$events[cu]){this.constructor.$$events[cu]=w;};},isDisposed:function(){return this.$$disposed||false;},dispose:function(){if(this.$$disposed){return;};this.$$disposed=true;{};this.destruct();{var cw,cx,cv,cy;};},destruct:function(){var cz;for(cz in this.__requests){if(this.__requests[cz]){this.__requests[cz].forEach(function(cB){cB.dispose();});};};if(this.__pollTimers){for(cz in this.__pollTimers){this.stopPollByAction(cz);};};if(this.__longPollHandlers){for(cz in this.__longPollHandlers){var cA=this.__longPollHandlers[cz];this.removeListenerById(cA);};};this.__requests=this.__routes=this.__pollTimers=null;}}});})();(function(){var a="error",b="",c="loadEnd",d="application/x-www-form-urlencoded",f="Cache-Control",g="Content-Type",h="fail",i="GET",j="success",k="undefined",l="POST",m="timeout",n="qx.bom.request.SimpleXhr",o="abort";qx.Bootstrap.define(n,{extend:qx.event.Emitter,construct:function(p,q){if(p!==undefined){this.setUrl(p);};this.useCaching(true);this.setMethod((q!==undefined)?q:i);this._transport=this._registerTransportListener(this._createTransport());qx.core.ObjectRegistry.register(this);this.__requestHeaders={};this.__parser=this._createResponseParser();},members:{setRequestHeader:function(r,s){this.__requestHeaders[r]=s;return this;},getRequestHeader:function(t){return this.__requestHeaders[t];},getResponseHeader:function(u){return this._transport.getResponseHeader(u);},getAllResponseHeaders:function(){return this._transport.getAllResponseHeaders();},setUrl:function(v){if(qx.lang.Type.isString(v)){this.__url=v;};return this;},getUrl:function(){return this.__url;},setMethod:function(w){if(qx.util.Request.isMethod(w)){this.__method=w;};return this;},getMethod:function(){return this.__method;},setRequestData:function(x){if(qx.lang.Type.isString(x)||qx.lang.Type.isObject(x)){this.__requestData=x;};return this;},getRequestData:function(){return this.__requestData;},getResponse:function(){if(this.__response!==null){return this.__response;}else {return (this._transport.responseXML!==null)?this._transport.responseXML:this._transport.responseText;};return null;},getTransport:function(){return this._transport;},setParser:function(y){return this.__parser.setParser(y);},setTimeout:function(z){if(qx.lang.Type.isNumber(z)){this.__timeout=z;};return this;},getTimeout:function(){return this.__timeout;},useCaching:function(A){if(qx.lang.Type.isBoolean(A)){this.__cache=A;};return this;},isCaching:function(){return this.__cache;},isDone:function(){return (this._transport.readyState===qx.bom.request.Xhr.DONE);},toHashCode:function(){return this.$$hash;},isDisposed:function(){return !!this.__disposed;},send:function(){var G=this.getTimeout(),D=(this.getRequestData()!==null),E=this.__requestHeaders.hasOwnProperty(f),B=qx.util.Request.methodAllowsRequestBody(this.getMethod()),H=this.getRequestHeader(g),C=this._serializeData(this.getRequestData(),H);if(this.getMethod()===i&&D){this.setUrl(qx.util.Uri.appendParamsToUrl(this.getUrl(),C));};if(this.isCaching()===false&&!E){this.setUrl(qx.util.Uri.appendParamsToUrl(this.getUrl(),{nocache:new Date().valueOf()}));};if(G){this._transport.timeout=G;};this._transport.open(this.getMethod(),this.getUrl(),true);for(var F in this.__requestHeaders){this._transport.setRequestHeader(F,this.__requestHeaders[F]);};if(!B){this._transport.send();}else {if(typeof H===k){this._transport.setRequestHeader(g,d);};this._transport.send(C);};},abort:function(){this._transport.abort();return this;},dispose:function(){if(this._transport.dispose()){this.__parser=null;this.__disposed=true;return true;};return false;},_transport:null,_createTransport:function(){return new qx.bom.request.Xhr();},_registerTransportListener:function(I){I.onreadystatechange=qx.lang.Function.bind(this._onReadyStateChange,this);I.onloadend=qx.lang.Function.bind(this._onLoadEnd,this);I.ontimeout=qx.lang.Function.bind(this._onTimeout,this);I.onerror=qx.lang.Function.bind(this._onError,this);I.onabort=qx.lang.Function.bind(this._onAbort,this);return I;},_createResponseParser:function(){return new qx.util.ResponseParser();},_setResponse:function(J){this.__response=J;},_serializeData:function(N,M){var K=this.getMethod()===l,L=/application\/.*\+?json/.test(M);if(!N){return null;};if(qx.lang.Type.isString(N)){return N;};if(L&&(qx.lang.Type.isObject(N)||qx.lang.Type.isArray(N))){return qx.lang.Json.stringify(N);};if(qx.lang.Type.isObject(N)){return qx.util.Uri.toParameter(N,K);};return null;},__requestHeaders:null,__requestData:null,__method:b,__url:b,__response:null,__parser:null,__cache:null,__timeout:null,__disposed:null,addListenerOnce:function(name,O,P){this.once(name,O,P);return this;},addListener:function(name,Q,R){this._transport._emitter.on(name,Q,R);return this;},_onReadyStateChange:function(){{};if(this.isDone()){this.__onReadyStateDone();};},__onReadyStateDone:function(){{};var T=this._transport.responseText;var S=this._transport.getResponseHeader(g);if(qx.util.Request.isSuccessful(this._transport.status)){{};this._setResponse(this.__parser.parse(T,S));this.emit(j);}else {try{this._setResponse(this.__parser.parse(T,S));}catch(e){};if(this._transport.status!==0){this.emit(h);};};},_onLoadEnd:function(){this.emit(c);},_onAbort:function(){this.emit(o);},_onTimeout:function(){this.emit(m);this.emit(h);},_onError:function(){this.emit(a);this.emit(h);}}});})();(function(){var a="qx.core.ObjectRegistry",b="-",c="-0",d="";qx.Bootstrap.define(a,{statics:{__registry:{},__nextHash:0,__freeHashes:[],__postId:d,register:function(e){var h=this.__registry;if(!h){return;};var g=e.$$hash;if(g==null){var f=this.__freeHashes;if(f.length>0&&true){g=f.pop();}else {g=(this.__nextHash++ )+this.__postId;};e.$$hash=g;{};};{};h[g]=e;},fromHashCode:function(j){return this.__registry[j]||null;},getPostId:function(){return this.__postId;}},defer:function(k){if(window&&window.top){var frames=window.top.frames;for(var i=0;i<frames.length;i++ ){if(frames[i]===window){k.__postId=b+(i+1);return;};};};k.__postId=c;}});})();(function(){var a="function",b="plugin.silverlight.version",c="Silverlight",d="Skype.Detection",f="QuickTimeCheckObject.QuickTimeCheck.1",g="Adobe Acrobat",h="plugin.windowsmedia",k="QuickTime",l="plugin.silverlight",m="pdf",n="wmv",o="qx.bom.client.Plugin",p="application/x-skype",q=',',r="plugin.divx",s='=',t="Chrome PDF Viewer",u="divx",v="Windows Media",w="",x="mshtml",y="skype.click2call",z="plugin.skype",A="plugin.gears",B="plugin.quicktime",C="plugin.windowsmedia.version",D="quicktime",E="DivX Web Player",F="AgControl.AgControl",G="Microsoft.XMLHTTP",H="silverlight",I="plugin.pdf",J="plugin.pdf.version",K="MSXML2.DOMDocument.6.0",L="WMPlayer.OCX.7",M="AcroPDF.PDF",N="plugin.activex",O="plugin.quicktime.version",P="plugin.divx.version",Q="npdivx.DivXBrowserPlugin.1",R="object";qx.Bootstrap.define(o,{statics:{getGears:function(){return !!(window.google&&window.google.gears);},getActiveX:function(){if(typeof window.ActiveXObject===a){return true;};try{return (typeof (new window.ActiveXObject(G))===R||typeof (new window.ActiveXObject(K))===R);}catch(S){return false;};},getSkype:function(){if(qx.bom.client.Plugin.getActiveX()){try{new ActiveXObject(d);return true;}catch(e){};};var T=navigator.mimeTypes;if(T){if(p in T){return true;};for(var i=0;i<T.length;i++ ){var U=T[i];if(U.type.indexOf(y)!=-1){return true;};};};return false;},__db:{quicktime:{plugin:[k],control:f},wmv:{plugin:[v],control:L},divx:{plugin:[E],control:Q},silverlight:{plugin:[c],control:F},pdf:{plugin:[t,g],control:M}},getQuicktimeVersion:function(){var V=qx.bom.client.Plugin.__db[D];return qx.bom.client.Plugin.__getVersion(V.control,V.plugin);},getWindowsMediaVersion:function(){var W=qx.bom.client.Plugin.__db[n];return qx.bom.client.Plugin.__getVersion(W.control,W.plugin,true);},getDivXVersion:function(){var X=qx.bom.client.Plugin.__db[u];return qx.bom.client.Plugin.__getVersion(X.control,X.plugin);},getSilverlightVersion:function(){var Y=qx.bom.client.Plugin.__db[H];return qx.bom.client.Plugin.__getVersion(Y.control,Y.plugin);},getPdfVersion:function(){var ba=qx.bom.client.Plugin.__db[m];return qx.bom.client.Plugin.__getVersion(ba.control,ba.plugin);},getQuicktime:function(){var bb=qx.bom.client.Plugin.__db[D];return qx.bom.client.Plugin.__isAvailable(bb.control,bb.plugin);},getWindowsMedia:function(){var bc=qx.bom.client.Plugin.__db[n];return qx.bom.client.Plugin.__isAvailable(bc.control,bc.plugin,true);},getDivX:function(){var bd=qx.bom.client.Plugin.__db[u];return qx.bom.client.Plugin.__isAvailable(bd.control,bd.plugin);},getSilverlight:function(){var be=qx.bom.client.Plugin.__db[H];return qx.bom.client.Plugin.__isAvailable(be.control,be.plugin);},getPdf:function(){var bf=qx.bom.client.Plugin.__db[m];return qx.bom.client.Plugin.__isAvailable(bf.control,bf.plugin);},__getVersion:function(bo,bk,bj){var bg=qx.bom.client.Plugin.__isAvailable(bo,bk,bj);if(!bg){return w;};if(qx.bom.client.Engine.getName()==x&&(qx.bom.client.Browser.getDocumentMode()<11||bj)){try{var bh=new ActiveXObject(bo);var bm;if(bh.GetVersions&&bh.GetVersions()){bm=bh.GetVersions().split(q);if(bm.length>1){bm=bm[0].split(s);if(bm.length===2){return bm[1];};};};bm=bh.versionInfo;if(bm!=undefined){return bm;};bm=bh.version;if(bm!=undefined){return bm;};bm=bh.settings.version;if(bm!=undefined){return bm;};}catch(bp){return w;};return w;}else {var bn=navigator.plugins;var bl=/([0-9]\.[0-9])/g;for(var i=0;i<bn.length;i++ ){var bi=bn[i];for(var j=0;j<bk.length;j++ ){if(bi.name.indexOf(bk[j])!==-1){if(bl.test(bi.name)||bl.test(bi.description)){return RegExp.$1;};};};};return w;};},__isAvailable:function(bt,br,bq){if(qx.bom.client.Engine.getName()==x&&(qx.bom.client.Browser.getDocumentMode()<11||bq)){if(!this.getActiveX()){return false;};try{new ActiveXObject(bt);}catch(bu){return false;};return true;}else {var bs=navigator.plugins;if(!bs){return false;};var name;for(var i=0;i<bs.length;i++ ){name=bs[i].name;for(var j=0;j<br.length;j++ ){if(name.indexOf(br[j])!==-1){return true;};};};return false;};}},defer:function(bv){qx.core.Environment.add(A,bv.getGears);qx.core.Environment.add(B,bv.getQuicktime);qx.core.Environment.add(O,bv.getQuicktimeVersion);qx.core.Environment.add(h,bv.getWindowsMedia);qx.core.Environment.add(C,bv.getWindowsMediaVersion);qx.core.Environment.add(r,bv.getDivX);qx.core.Environment.add(P,bv.getDivXVersion);qx.core.Environment.add(l,bv.getSilverlight);qx.core.Environment.add(b,bv.getSilverlightVersion);qx.core.Environment.add(I,bv.getPdf);qx.core.Environment.add(J,bv.getPdfVersion);qx.core.Environment.add(N,bv.getActiveX);qx.core.Environment.add(z,bv.getSkype);}});})();(function(){var a='<\?xml version="1.0" encoding="utf-8"?>\n<',b="MSXML2.DOMDocument.3.0",c="qx.xml.Document",d="",e=" />",f="xml.domparser",g="SelectionLanguage",h="'",j="MSXML2.XMLHTTP.3.0",k="plugin.activex",m="No XML implementation available!",n="MSXML2.XMLHTTP.6.0",o="xml.implementation",p=" xmlns='",q="text/xml",r="XPath",s="MSXML2.DOMDocument.6.0",t="HTML";qx.Bootstrap.define(c,{statics:{DOMDOC:null,XMLHTTP:null,isXmlDocument:function(u){if(u.nodeType===9){return u.documentElement.nodeName!==t;}else if(u.ownerDocument){return this.isXmlDocument(u.ownerDocument);}else {return false;};},create:function(v,w){if(qx.core.Environment.get(k)){var x=new ActiveXObject(this.DOMDOC);if(this.DOMDOC==b){x.setProperty(g,r);};if(w){var y=a;y+=w;if(v){y+=p+v+h;};y+=e;x.loadXML(y);};return x;};if(qx.core.Environment.get(o)){return document.implementation.createDocument(v||d,w||d,null);};throw new Error(m);},fromString:function(A){if(qx.core.Environment.get(k)){var B=qx.xml.Document.create();B.loadXML(A);return B;};if(qx.core.Environment.get(f)){var z=new DOMParser();return z.parseFromString(A,q);};throw new Error(m);}},defer:function(D){if(qx.core.Environment.get(k)){var C=[s,b];var E=[n,j];for(var i=0,l=C.length;i<l;i++ ){try{new ActiveXObject(C[i]);new ActiveXObject(E[i]);}catch(F){continue;};D.DOMDOC=C[i];D.XMLHTTP=E[i];break;};};}});})();(function(){var a="function",b="xml.implementation",c="xml.attributens",d="xml.selectnodes",e="<a></a>",f="xml.getqualifieditem",g="SelectionLanguage",h="xml.getelementsbytagnamens",i="qx.bom.client.Xml",j="xml.domproperties",k="xml.selectsinglenode",l="1.0",m="xml.createnode",n="xml.domparser",o="getProperty",p="undefined",q="XML",r="string",s="xml.createelementns";qx.Bootstrap.define(i,{statics:{getImplementation:function(){return document.implementation&&document.implementation.hasFeature&&document.implementation.hasFeature(q,l);},getDomParser:function(){return typeof window.DOMParser!==p;},getSelectSingleNode:function(){return typeof qx.xml.Document.create().selectSingleNode!==p;},getSelectNodes:function(){return typeof qx.xml.Document.create().selectNodes!==p;},getElementsByTagNameNS:function(){return typeof qx.xml.Document.create().getElementsByTagNameNS!==p;},getDomProperties:function(){var t=qx.xml.Document.create();return (o in t&&typeof t.getProperty(g)===r);},getAttributeNS:function(){var u=qx.xml.Document.fromString(e).documentElement;return typeof u.getAttributeNS===a&&typeof u.setAttributeNS===a;},getCreateElementNS:function(){return typeof qx.xml.Document.create().createElementNS===a;},getCreateNode:function(){return typeof qx.xml.Document.create().createNode!==p;},getQualifiedItem:function(){var v=qx.xml.Document.fromString(e).documentElement;return typeof v.attributes.getQualifiedItem!==p;}},defer:function(w){qx.core.Environment.add(b,w.getImplementation);qx.core.Environment.add(n,w.getDomParser);qx.core.Environment.add(k,w.getSelectSingleNode);qx.core.Environment.add(d,w.getSelectNodes);qx.core.Environment.add(h,w.getElementsByTagNameNS);qx.core.Environment.add(j,w.getDomProperties);qx.core.Environment.add(c,w.getAttributeNS);qx.core.Environment.add(s,w.getCreateElementNS);qx.core.Environment.add(m,w.getCreateNode);qx.core.Environment.add(f,w.getQualifiedItem);}});})();(function(){var a="function",b="qx.util.ResponseParser",c="";qx.Bootstrap.define(b,{construct:function(d){if(d!==undefined){this.setParser(d);};},statics:{PARSER:{json:qx.lang.Json.parse,xml:qx.xml.Document.fromString}},members:{__parser:null,parse:function(g,f){var e=this._getParser(f);if(typeof e===a){if(g!==c){return e.call(this,g);};};return g;},setParser:function(h){if(typeof qx.util.ResponseParser.PARSER[h]===a){return this.__parser=qx.util.ResponseParser.PARSER[h];};{};return this.__parser=h;},_getParser:function(j){var i=this.__parser,l=c,k=c;if(i){return i;};l=j||c;k=l.replace(/;.*$/,c);if(/^application\/(\w|\.)*\+?json$/.test(k)){i=qx.util.ResponseParser.PARSER.json;};if(/^application\/xml$/.test(k)){i=qx.util.ResponseParser.PARSER.xml;};if(/[^\/]+\/[^\+]+\+xml$/.test(l)){i=qx.util.ResponseParser.PARSER.xml;};return i;}}});})();(function(){var a="text",b="engine.name",c="password",d="keypress",e="mshtml",f="textarea",g="function",h="input",j="gecko",k="getKeyIdentifier",m="Backspace",n="keydown",o="qx.module.event.Keyboard",p="keyup",q="browser.documentmode";qx.Bootstrap.define(o,{statics:{TYPES:[n,d,p],BIND_METHODS:[k],getKeyIdentifier:function(){if(this.type==d&&(qxWeb.env.get(b)!=j||this.charCode!==0)){return qx.event.util.Keyboard.charCodeToIdentifier(this.charCode||this.keyCode);};return qx.event.util.Keyboard.keyCodeToIdentifier(this.keyCode);},normalize:function(event,s){if(!event){return event;};var r=qx.module.event.Keyboard.BIND_METHODS;for(var i=0,l=r.length;i<l;i++ ){if(typeof event[r[i]]!=g){event[r[i]]=qx.module.event.Keyboard[r[i]].bind(event);};};return event;},registerInputFix:function(t){if(t.type===a||t.type===c||t.type===f){if(!t.__inputFix){t.__inputFix=qxWeb(t).on(p,qx.module.event.Keyboard._inputFix);};};},unregisterInputFix:function(u){if(u.__inputFix&&!qxWeb(u).hasListener(h)){qxWeb(u).off(p,qx.module.event.Keyboard._inputFix);u.__inputFix=null;};},_inputFix:function(v){if(v.getKeyIdentifier()!==m){return;};var w=v.getTarget();var x=qxWeb(w).getValue();if(!w.__oldInputValue||w.__oldInputValue!==x){w.__oldInputValue=x;v.type=v._type=h;w.$$emitter.emit(h,v);};}},defer:function(y){qxWeb.$registerEventNormalization(qx.module.event.Keyboard.TYPES,y.normalize);if(qxWeb.env.get(b)===e&&qxWeb.env.get(q)===9){qxWeb.$registerEventHook(h,y.registerInputFix,y.unregisterInputFix);};}});})();(function(){var a="-",b="PageUp",c="Escape",d="Enter",e="+",f="PrintScreen",g="os.name",h="7",i="A",j="Space",k="Left",l="5",m="F5",n="Down",o="Up",p="3",q="Meta",r="F11",s="0",t="F6",u="PageDown",v="osx",w="CapsLock",x="Insert",y="F8",z="Scroll",A="Control",B="Tab",C="Shift",D="End",E="Pause",F="Unidentified",G="/",H="8",I="Z",J="*",K="cmd",L="F1",M="F4",N="Home",O="qx.event.util.Keyboard",P="F2",Q="6",R="F7",S="Apps",T="4",U="F12",V="Alt",W="2",X="NumLock",Y="Delete",bn="1",bo="Win",bp="Backspace",bj="F9",bk="F10",bl="Right",bm="F3",bq="9",br=",";qx.Bootstrap.define(O,{statics:{specialCharCodeMap:{'8':bp,'9':B,'13':d,'27':c,'32':j},numpadToCharCode:{'96':s.charCodeAt(0),'97':bn.charCodeAt(0),'98':W.charCodeAt(0),'99':p.charCodeAt(0),'100':T.charCodeAt(0),'101':l.charCodeAt(0),'102':Q.charCodeAt(0),'103':h.charCodeAt(0),'104':H.charCodeAt(0),'105':bq.charCodeAt(0),'106':J.charCodeAt(0),'107':e.charCodeAt(0),'109':a.charCodeAt(0),'110':br.charCodeAt(0),'111':G.charCodeAt(0)},keyCodeToIdentifierMap:{'16':C,'17':A,'18':V,'20':w,'224':q,'37':k,'38':o,'39':bl,'40':n,'33':b,'34':u,'35':D,'36':N,'45':x,'46':Y,'112':L,'113':P,'114':bm,'115':M,'116':m,'117':t,'118':R,'119':y,'120':bj,'121':bk,'122':r,'123':U,'144':X,'44':f,'145':z,'19':E,'91':qx.core.Environment.get(g)==v?K:bo,'92':bo,'93':qx.core.Environment.get(g)==v?K:S},charCodeA:i.charCodeAt(0),charCodeZ:I.charCodeAt(0),charCode0:s.charCodeAt(0),charCode9:bq.charCodeAt(0),keyCodeToIdentifier:function(bs){if(this.isIdentifiableKeyCode(bs)){var bt=this.numpadToCharCode[bs];if(bt){return String.fromCharCode(bt);};return (this.keyCodeToIdentifierMap[bs]||this.specialCharCodeMap[bs]||String.fromCharCode(bs));}else {return F;};},charCodeToIdentifier:function(bu){return this.specialCharCodeMap[bu]||String.fromCharCode(bu).toUpperCase();},isIdentifiableKeyCode:function(bv){if(bv>=this.charCodeA&&bv<=this.charCodeZ){return true;};if(bv>=this.charCode0&&bv<=this.charCode9){return true;};if(this.specialCharCodeMap[bv]){return true;};if(this.numpadToCharCode[bv]){return true;};if(this.isNonPrintableKeyCode(bv)){return true;};return false;},isNonPrintableKeyCode:function(bw){return this.keyCodeToIdentifierMap[bw]?true:false;}},defer:function(bx,by){if(!bx.identifierToKeyCodeMap){bx.identifierToKeyCodeMap={};for(var bz in bx.keyCodeToIdentifierMap){bx.identifierToKeyCodeMap[bx.keyCodeToIdentifierMap[bz]]=parseInt(bz,10);};for(var bz in bx.specialCharCodeMap){bx.identifierToKeyCodeMap[bx.specialCharCodeMap[bz]]=parseInt(bz,10);};};}});})();

var exp = envinfo["qx.export"];
if (exp) {
  for (var name in exp) {
    var c = exp[name].split(".");
    var root = window;
    for (var i=0; i < c.length; i++) {
      root = root[c[i]];
    };
    window[name] = root;
  }
}

window["qx"] = undefined;
try {
  delete window.qx;
} catch(e) {}

})();