-- This file was included in Ciena MIB release MIBS-CIENA-CES-08-07-00-024
--
-- CIENA-CES-SYSLOG-COLLECTOR-MIB
--

CIENA-CES-SYSLOG-COLLECTOR-MIB DEFINITIONS ::= BEGIN


IMPORTS
    IpAddress, Integer32, OB<PERSON>ECT-TYPE, MODULE-IDENTITY
        FROM SNMPv2-SMI
    DisplayString, TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    InetAddressType,InetAddress
	    FROM INET-ADDRESS-MIB
    cienaCesConfig, cienaCesNotifications
	    FROM CIENA-SMI
    CienaGlobalState
        FROM CIENA-TC;

cienaCesSyslogCollectorMIB MODULE-IDENTITY
        LAST-UPDATED "201706070000Z"
        ORGANIZATION "Ciena Corp."
        CONTACT-INFO
        "   Mib Meister
            7035 Ridge Road
            Hanover, Maryland 21076
            USA
            Phone:  ****** 921 1144
            Email:  <EMAIL>" 
        DESCRIPTION
            "A MIB module to manage Syslog collectors on the SAOS 8700 products."

        REVISION "201706070000Z"
        DESCRIPTION
            "Updated contact info."

        REVISION "201607220000Z"
        DESCRIPTION
            "Initial creation."
        ::= { cienaCesConfig 40 }

--
-- Textual conventions
--
SyslogFacility  ::=  TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
	"This textual convention enumerates the facilities
         that originate Syslog messages.

         The value noMap(24) indicates that the appropriate
         facility will be provided by the individual applications
         on the managed entity.  If this option is not available
         on a particular entity attempt set the facillity to this
         value will fail with an error-status of wrongValue."
    SYNTAX  INTEGER {
			kernel          (0), -- kernel messages
			user            (1), -- user-level messages
			mail            (2), -- mail system
			daemon          (3), -- system daemons
			auth            (4), -- authorization messages
			syslog          (5), -- messages generated by syslogd
			lpr             (6), -- line printer subsystem
			news            (7), -- network news subsystem
			uucp            (8), -- UUCP subsystem
			cron            (9), -- clock daemon
			authPriv        (10),-- authorization messages
					    --    (private)
			ftp             (11),-- ftp daemon
			ntp             (12),-- NTP subsystem
			security        (13),-- security subsystems
					    --    (firewalling, etc.)
			console         (14),-- /dev/console output
			clockd2         (15),
			local0          (16),
			local1          (17),
			local2          (18),
			local3          (19),
			local4          (20),
			local5          (21),
			local6          (22),
			local7          (23),
			noMap           (24)
                    }
--
-- Node definitions
--
cienaCesSyslogCollMIBObjects OBJECT IDENTIFIER ::= { cienaCesSyslogCollectorMIB 1 }
cienaCesSyslogSystem  OBJECT IDENTIFIER ::= { cienaCesSyslogCollMIBObjects 1 }
cienaCesSyslogColl    OBJECT IDENTIFIER ::= { cienaCesSyslogCollMIBObjects 2 }

--
-- Notification groups
--
cienaCesSyslogCollMIBNotificationPrefix  OBJECT IDENTIFIER ::= { cienaCesNotifications 41 } 
cienaCesSyslogCollMIBNotifications       OBJECT IDENTIFIER ::= { cienaCesSyslogCollMIBNotificationPrefix 0 }

--
-- Conformance groups
--
cienaCesSyslogCollMIBConformance OBJECT IDENTIFIER ::= { cienaCesSyslogCollectorMIB 3 } 
cienaCesSyslogCollMIBCompliances OBJECT IDENTIFIER ::= { cienaCesSyslogCollMIBConformance 1 } 		
cienaCesSyslogCollMIBGroups      OBJECT IDENTIFIER ::= { cienaCesSyslogCollMIBConformance 2 }

--
-- Configuration information
--
cienaCesSyslogEnable OBJECT-TYPE
    SYNTAX      CienaGlobalState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies whether the Syslog client is enabled."
    DEFVAL { enabled }
    ::= { cienaCesSyslogSystem 1 }

-- 
-- cienaCesSyslogCollector Table
-- 
cienaCesSyslogCollectorTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CienaCesSyslogCollectorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table containing Syslog collector information."
    ::= { cienaCesSyslogColl 1 }
    
cienaCesSyslogCollectorEntry OBJECT-TYPE
    SYNTAX      CienaCesSyslogCollectorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the information pertaining to a Syslog collector
         to which Syslog messages are relayed."
    INDEX  { cienaCesSyslogIndex }
    ::= { cienaCesSyslogCollectorTable 1 }

CienaCesSyslogCollectorEntry ::= SEQUENCE {
    cienaCesSyslogIndex				Integer32,	 
    cienaCesSyslogCollectorAddr			DisplayString,
    cienaCesSyslogCollectorUDPPort		Integer32,
    cienaCesSyslogCollectorFacility		SyslogFacility,
    cienaCesSyslogCollectorSeverityList         BITS,
    cienaCesSyslogCollectorUserAdminState	CienaGlobalState,
    cienaCesSyslogCollectorDhcpAdminState	CienaGlobalState,
    cienaCesSyslogCollectorOperState		CienaGlobalState,
    cienaCesSyslogCollectorCustomPrefix		DisplayString,
    cienaCesSyslogCollectorResolvedInetAddrType	InetAddressType,
    cienaCesSyslogCollectorResolvedInetAddress	InetAddress
    }

cienaCesSyslogIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "Specifies the unique index used to represent each row in the table."
    ::= { cienaCesSyslogCollectorEntry 1 }

cienaCesSyslogCollectorAddr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "The address for the Syslog message collector.
        It can be Host Name or IP address."
    ::= { cienaCesSyslogCollectorEntry 2 }

cienaCesSyslogCollectorUDPPort OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The port number on the destination to which the
         Syslog message is forwarded over the UDP
         transport."
    DEFVAL   { 514 }
    ::= { cienaCesSyslogCollectorEntry 3 }

cienaCesSyslogCollectorFacility OBJECT-TYPE
    SYNTAX      SyslogFacility
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Syslog facility code that is added to the messages forwarded
         to this collector."
    DEFVAL   { daemon } 
    ::= { cienaCesSyslogCollectorEntry 4 }

cienaCesSyslogCollectorSeverityList     OBJECT-TYPE
    SYNTAX      BITS {
                        emergency(0),
                        alert(1),
                        critical(2),
                        error(3),
                        warning(4),
                        notice(5),
                        informational(6),
                        debug(7)
                    }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object represents all severity levels for Syslog.
         Bit positions set to 1 indicate a severity received by
         the collector while bits set to 0 indicate the severity
         is ignored."
    ::= {cienaCesSyslogCollectorEntry 5 }

cienaCesSyslogCollectorUserAdminState OBJECT-TYPE
    SYNTAX      CienaGlobalState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the administrative state of the cienaCesSyslogCollectorAddr 
         configured by the user."
    ::= { cienaCesSyslogCollectorEntry 6 }
  
cienaCesSyslogCollectorDhcpAdminState OBJECT-TYPE
    SYNTAX      CienaGlobalState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the administrative state of the cienaCesSyslogCollectorAddr 
	     configured by DHCP."
    ::= { cienaCesSyslogCollectorEntry 7 }

cienaCesSyslogCollectorOperState OBJECT-TYPE
    SYNTAX      CienaGlobalState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the operational state of the cienaCesSyslogCollectorAddr."
    ::= { cienaCesSyslogCollectorEntry 8 }

cienaCesSyslogCollectorCustomPrefix OBJECT-TYPE
    SYNTAX      DisplayString(SIZE(0..15))
    MAX-ACCESS  read-only
    STATUS      current         
    DESCRIPTION
        "Specifies the Syslog custom prefix that is prepended to all Syslog messages
         before device sends the messages to the collector."
    DEFVAL      {''H}    -- the empty string
    ::= { cienaCesSyslogCollectorEntry 9 }

cienaCesSyslogCollectorResolvedInetAddrType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the resolved IP address type. Used in conjunction with
         cienaCesSyslogCollectorResolvedInetAddress.
         When set to:
	     ipv4 : cienaCesSyslogCollectorResolvedInetAddress is compliant with
	            InetAddressIPv4
             ipv6 : cienaCesSyslogCollectorResolvedInetAddress is compliant with
	            InetAddressIPv6"
    ::= { cienaCesSyslogCollectorEntry 10 }

cienaCesSyslogCollectorResolvedInetAddress OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the resolved ip address for given cienaCesSyslogCollectorAddr.
         If cienaCesSyslogCollectorAddr is set to host name then
         cienaCesSyslogCollectorResolvedInetAddr displays the resolved IP address.
         If cienaCesSyslogCollectorAddr is set to IP address then
         cienaCesSyslogCollectorResolvedInetAddr displays the same value as the
         cienaCesSyslogCollectorAddr.  This OID should be used in conjuction
         with cienaCesSyslogCollectorResolvedInetAddrType."
    ::= { cienaCesSyslogCollectorEntry 11 }

END

--
-- CIENA-CES-COLLECTOR-MIB.my
--
