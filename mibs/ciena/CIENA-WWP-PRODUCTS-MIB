WWP-PRODUCTS-MIB DEFINITIONS ::= BEGIN

	IMPORTS
		enterprises, MODULE-IDENTITY, OBJECT-IDENTITY
			FROM SNMPv2-SMI
		wwpProducts, wwpModules
			FROM WWP-SMI;

--
-- Node definitions
--
	-- *******.4.1.6141.1.1
	wwpLe21 OBJECT IDENTIFIER ::= { wwpProducts 1 }

	-- *******.4.1.6141.1.2
	wwpLe22 OBJECT IDENTIFIER ::= { wwpProducts 2 }

	-- *******.4.1.6141.1.3
	wwpLe31 OBJECT IDENTIFIER ::= { wwpProducts 3 }

	-- *******.4.1.6141.1.4
	wwpLe32 OBJECT IDENTIFIER ::= { wwpProducts 4 }

	-- *******.4.1.6141.1.5
	wwpLe41 OBJECT IDENTIFIER ::= { wwpProducts 5 }

	-- *******.4.1.6141.1.6
	wwpLe42 OBJECT IDENTIFIER ::= { wwpProducts 6 }

	-- *******.4.1.6141.1.7
	wwpLe200 OBJECT IDENTIFIER ::= { wwpProducts 7 }

	-- *******.4.1.6141.1.8
	wwpLe210 OBJECT IDENTIFIER ::= { wwpProducts 8 }

	-- *******.4.1.6141.1.9
	wwpLe220 OBJECT IDENTIFIER ::= { wwpProducts 9 }

	-- *******.4.1.6141.1.10
	wwpLe410 OBJECT IDENTIFIER ::= { wwpProducts 10 }

	-- *******.4.1.6141.1.11
	wwpLe3700 OBJECT IDENTIFIER ::= { wwpProducts 11 }

	-- *******.4.1.6141.1.12
	wwpLe4400 OBJECT IDENTIFIER ::= { wwpProducts 12 }

	-- *******.4.1.6141.1.13
	wwpLe211 OBJECT IDENTIFIER ::= { wwpProducts 13 }

	-- *******.4.1.6141.1.14
	wwpLe211H OBJECT IDENTIFIER ::= { wwpProducts 14 }

	-- *******.4.1.6141.1.15
	wwpLe216 OBJECT IDENTIFIER ::= { wwpProducts 15 }

	-- *******.4.1.6141.1.16
	wwpLe216H OBJECT IDENTIFIER ::= { wwpProducts 16 }

	-- *******.4.1.6141.1.17
	wwpLe218 OBJECT IDENTIFIER ::= { wwpProducts 17 }

	-- *******.4.1.6141.1.18
	wwpLe218H OBJECT IDENTIFIER ::= { wwpProducts 18 }

	-- *******.4.1.6141.1.19
	wwpLe410H OBJECT IDENTIFIER ::= { wwpProducts 19 }

	-- *******.4.1.6141.1.20
	wwpLe22H OBJECT IDENTIFIER ::= { wwpProducts 20 }

	-- *******.4.1.6141.1.21
	wwpLe32H OBJECT IDENTIFIER ::= { wwpProducts 21 }

	-- *******.4.1.6141.1.22
	wwpLe36 OBJECT IDENTIFIER ::= { wwpProducts 22 }

	-- *******.4.1.6141.1.23
	wwpLe36H OBJECT IDENTIFIER ::= { wwpProducts 23 }

	-- *******.4.1.6141.1.24
	wwpLe217 OBJECT IDENTIFIER ::= { wwpProducts 24 }

	-- *******.4.1.6141.1.25
	wwpLe217H OBJECT IDENTIFIER ::= { wwpProducts 25 }

	-- *******.4.1.6141.1.26
	wwpLe217DC OBJECT IDENTIFIER ::= { wwpProducts 26 }

	-- *******.4.1.6141.1.27
	wwpLe410DC OBJECT IDENTIFIER ::= { wwpProducts 27 }

	-- *******.4.1.6141.1.28
	wwpLe317 OBJECT IDENTIFIER ::= { wwpProducts 28 }

	-- *******.4.1.6141.1.29
	wwpLe317H OBJECT IDENTIFIER ::= { wwpProducts 29 }

	-- *******.4.1.6141.1.30
	wwpLe317DC OBJECT IDENTIFIER ::= { wwpProducts 30 }

	-- *******.4.1.6141.1.31
	wwpLe38 OBJECT IDENTIFIER ::= { wwpProducts 31 }

	-- *******.4.1.6141.1.32
	wwpLe46 OBJECT IDENTIFIER ::= { wwpProducts 32 }

	-- *******.4.1.6141.1.33
	wwpLe46Voip OBJECT IDENTIFIER ::= { wwpProducts 33 }

	-- *******.4.1.6141.1.34
	wwpLe42H OBJECT IDENTIFIER ::= { wwpProducts 34 }

	-- *******.4.1.6141.1.35
	wwpLe42HVoip OBJECT IDENTIFIER ::= { wwpProducts 35 }

	-- *******.4.1.6141.1.36
	wwpLe407 OBJECT IDENTIFIER ::= { wwpProducts 36 }

	-- *******.4.1.6141.1.37
	wwpLe427 OBJECT IDENTIFIER ::= { wwpProducts 37 }

	-- *******.4.1.6141.1.38
	wwpLe307 OBJECT IDENTIFIER ::= { wwpProducts 38 }

	-- *******.4.1.6141.1.39
	wwpLe327 OBJECT IDENTIFIER ::= { wwpProducts 39 }

	-- *******.4.1.6141.1.40
	wwpLe337 OBJECT IDENTIFIER ::= { wwpProducts 40 }

	-- *******.4.1.6141.1.41
	wwpLe22P0100 OBJECT IDENTIFIER ::= { wwpProducts 41 }

	-- *******.4.1.6141.1.42
	wwpLe46H OBJECT IDENTIFIER ::= { wwpProducts 42 }

	-- *******.4.1.6141.1.43
	wwpLe46HVoip OBJECT IDENTIFIER ::= { wwpProducts 43 }

	-- *******.4.1.6141.1.44
	wwpLe42Voip OBJECT IDENTIFIER ::= { wwpProducts 44 }

	-- *******.4.1.6141.1.45
	wwpLe17 OBJECT IDENTIFIER ::= { wwpProducts 45 }

	-- *******.4.1.6141.1.46
	wwpLe17Voip OBJECT IDENTIFIER ::= { wwpProducts 46 }

	-- *******.4.1.6141.1.47
	wwpLe311 OBJECT IDENTIFIER ::= { wwpProducts 47 }

	-- *******.4.1.6141.1.48
	wwpLe38Sfp OBJECT IDENTIFIER ::= { wwpProducts 48 }

	-- *******.4.1.6141.1.49
	wwpLe38SfpVoip OBJECT IDENTIFIER ::= { wwpProducts 49 }

	-- *******.4.1.6141.1.70
	wwpLe311v OBJECT IDENTIFIER ::= { wwpProducts 70 }

	-- *******.4.1.6141.1.71
	wwpLe310 OBJECT IDENTIFIER ::= { wwpProducts 71 }

	-- *******.4.1.6141.1.72
	wwpLe135 OBJECT IDENTIFIER ::= { wwpProducts 72 }

	-- *******.4.1.6141.1.73
	wwpLe3300 OBJECT IDENTIFIER ::= { wwpProducts 73 }

	-- *******.4.1.6141.1.74
	wwpLe3300Rev2 OBJECT IDENTIFIER ::= { wwpProducts 74 }

	-- *******.4.1.6141.1.75
	wwpLe3300FanTray OBJECT IDENTIFIER ::= { wwpProducts 75 }

	-- *******.4.1.6141.1.76
	wwpLe58H OBJECT IDENTIFIER ::= { wwpProducts 76 }

	-- *******.4.1.6141.1.77
	cn3920 OBJECT IDENTIFIER ::= { wwpProducts 77 }

	-- *******.4.1.6141.1.78
	cn3911 OBJECT IDENTIFIER ::= { wwpProducts 78 }

	-- *******.4.1.6141.1.79
	cn3940 OBJECT IDENTIFIER ::= { wwpProducts 79 }

	-- *******.4.1.6141.1.80
	cn5150 OBJECT IDENTIFIER ::= { wwpProducts 80 }

	-- *******.4.1.6141.1.81
	cn3960 OBJECT IDENTIFIER ::= { wwpProducts 81 }

	-- *******.4.1.6141.1.82
	cn5140 OBJECT IDENTIFIER ::= { wwpProducts 82 }

	-- *******.4.1.6141.1.83
	cn5305 OBJECT IDENTIFIER ::= { wwpProducts 83 }

	-- *******.4.1.6141.1.84
	cn3916 OBJECT IDENTIFIER ::= { wwpProducts 84 }

	-- *******.4.1.6141.1.85
	cn3930 OBJECT IDENTIFIER ::= { wwpProducts 85 }

	-- *******.4.1.6141.1.86
	cn3931 OBJECT IDENTIFIER ::= { wwpProducts 86 }

	-- *******.4.1.6141.1.94
	cn3902 OBJECT IDENTIFIER ::= { wwpProducts 94 }

	-- *******.4.1.6141.1.95
	cn3932 OBJECT IDENTIFIER ::= { wwpProducts 95 }

	-- *******.4.1.6141.1.96
	cn5142 OBJECT IDENTIFIER ::= { wwpProducts 96 }

	-- *******.4.1.6141.1.97
	cn5160 OBJECT IDENTIFIER ::= { wwpProducts 97 }

	-- *******.4.1.6141.1.98
	cn3942 OBJECT IDENTIFIER ::= { wwpProducts 98 }

	-- *******.4.1.6141.1.99
	cn3903 OBJECT IDENTIFIER ::= { wwpProducts 99 }

	-- *******.4.1.6141.1.100
	cn3904 OBJECT IDENTIFIER ::= { wwpProducts 100 }

	-- *******.4.1.6141.1.101
	cn3905 OBJECT IDENTIFIER ::= { wwpProducts 101 }

	-- *******.4.1.6141.1.102
	cn3938 OBJECT IDENTIFIER ::= { wwpProducts 102 }

	-- *******.4.1.6141.1.103
	cn3906 OBJECT IDENTIFIER ::= { wwpProducts 103 }

	-- *******.4.1.6141.2.1
	wwpProductsMIB MODULE-IDENTITY
		LAST-UPDATED "200507280000Z"		-- July 28, 2005 at 00:00 GMT (200507280000Z)
		ORGANIZATION
			"Ciena, Inc"
		CONTACT-INFO
			"Mib Meister
			115 North Sullivan Road
			Spokane Valley, WA 99037
			   	USA		 		
			   	Phone:  ****** 242 9000
			Email:  <EMAIL>"
		DESCRIPTION
			"This module defines the object identifiers that are
			assigned to WWP products and platforms.  It is these
			OID values that are returned in sysObjectID."
		REVISION "200507280000Z"		-- July 28, 2005 at 00:00 GMT (200507280000Z)
		DESCRIPTION
			"Initial creation."
	::= { wwpModules 1 }

END

