-- This file was included in Ciena MIB release MIBS-CIENA-CES-08-07-00-024
 --
 -- CIENA-CES-PORT-XCVR-MIB.my
 --

  CIENA-CES-PORT-XCVR-MIB DEFINITIONS ::= BEGIN

	IMPORTS
		cienaGlobalMacAddress
			FROM CIENA-GLOBAL-MI<PERSON>
		cien<PERSON>, cienaCesConfig, cienaCesNotifications
			FROM CIENA-SMI
		CienaGlobalSeverity
			FROM CIENA-TC
		enterprises, Unsigned32, MODULE-IDENTITY,
		OBJECT-IDENTITY, NOTIFICATION-TYPE
			FROM SNMPv2-SMI
		DisplayString, Mac<PERSON>ddress, TEXTUAL-CONVENTION
			FROM SNMPv2-TC;

--
-- Node definitions
--
	-- *******.4.1.1271.2.1.9
	cienaCesPortXcvrMIB MODULE-IDENTITY
		LAST-UPDATED "202212070000Z"		-- December 7, 2022 at 00:00 GMT (202212070000Z)
		ORGANIZATION
			"Ciena Corp."
		CONTACT-INFO
			"   Mib Meister
			7035 Ridge Road
			Hanover, Maryland 21076
			USA
			Phone:  ****** 921 1144
			Email:  <EMAIL>"
		DESCRIPTION
			"This module defines the port XCVR related notifications."
		REVISION "202212070000Z"		-- December 7, 2022 at 00:00 GMT (202212070000Z)
		DESCRIPTION
			"Added new objects cienaCesPortXcvrPwrUsage,cienaCesPortXcvrPortNumber
			and cienaCesPortXcvrPortName to cienaCesPortXcvrTable."
		REVISION "202103240000Z"		-- March 24, 2021 at 00:00 GMT (202103240000Z)
		DESCRIPTION
			"Added a new object cienaCesPortXcvrNotifPortName."
		REVISION "201907250000Z"		-- July 25, 2019 at 00:00 GMT (201907250000Z)
		DESCRIPTION
			"Added cfp2Dco(17) in cienaCesPortXcvrIdentiferType."
		REVISION "201901030000Z"		-- January 3, 2019 at 00:00 GMT (201901030000Z)
		DESCRIPTION
			"Add Rx/Tx power High/LowWarning Notifications."
		REVISION "201809280000Z"		-- September 28, 2018 at 00:00 GMT (201809280000Z)
		DESCRIPTION
			"Added cfp, qsfpPlus and qsfp28 in cienaCesPortXcvrIdentiferType.
			Added unit information."
		REVISION "201806150000Z"		-- June 15, 2018 at 00:00 GMT (201806150000Z)
		DESCRIPTION
			"Added a new object cienaCesPortXcvrFecMode."
		REVISION "201805150000Z"		-- May 15, 2018 at 00:00 GMT (201805150000Z)
		DESCRIPTION
			"Add TxHigh/LowWarning Notifications"
		REVISION "201804200000Z"		-- April 20, 2018 at 00:00 GMT (201804200000Z)
		DESCRIPTION
			"Add RxHigh/LowWarning Notifications"
		REVISION "201706070000Z"		-- June 7, 2017 at 00:00 GMT (201706070000Z)
		DESCRIPTION
			"Updated contact info."
		REVISION "201610070000Z"		-- October 7, 2016 at 00:00 GMT (201610070000Z)
		DESCRIPTION
			"Added a new object cienaCesPortXcvrUncertifiedNotification."
		REVISION "201108230000Z"		-- August 23, 2011 at 00:00 GMT (201108230000Z)
		DESCRIPTION
			"Added a new object cienaCesPortXcvrTxOutputPower."
		REVISION "201107060000Z"		-- July 6, 2011 at 00:00 GMT (201107060000Z)
		DESCRIPTION
			"Corrected Units changed watts to uW in descriptions."
	::= { cienaCesConfig 9 }

	-- *******.4.1.1271.*******
	cienaCesPortXcvrMIBObjects OBJECT IDENTIFIER ::= { cienaCesPortXcvrMIB 1 }

	-- *******.4.1.1271.*******.1
	cienaCesPortXcvr OBJECT IDENTIFIER ::= { cienaCesPortXcvrMIBObjects 1 }

	-- *******.4.1.1271.*******.1.1
	cienaCesPortXcvrTable OBJECT-TYPE
		SYNTAX SEQUENCE OF CienaCesPortXcvrEntry
		ACCESS not-accessible
		STATUS current
		DESCRIPTION
			"This table contains descriptions and settings for each of the 
			physical transceiver devices."
	::= { cienaCesPortXcvr 1 }

	-- *******.4.1.1271.*******.1.1.1
	cienaCesPortXcvrEntry OBJECT-TYPE
		SYNTAX CienaCesPortXcvrEntry
		ACCESS not-accessible
		STATUS current
		DESCRIPTION
			"The transceiver device entry."
		INDEX { cienaCesPortXcvrId } 
	::= { cienaCesPortXcvrTable 1 }


	CienaCesPortXcvrEntry ::= SEQUENCE
		{
		cienaCesPortXcvrId INTEGER,
		cienaCesPortXcvrOperState INTEGER,
		cienaCesPortXcvrTemperature INTEGER,
		cienaCesPortXcvrVcc INTEGER,
		cienaCesPortXcvrBias INTEGER,
		cienaCesPortXcvrRxPower INTEGER,
		cienaCesPortXcvrHighTempAlarmThreshold Integer32,
		cienaCesPortXcvrLowTempAlarmThreshold Integer32,
		cienaCesPortXcvrHighVccAlarmThreshold Integer32,
		cienaCesPortXcvrLowVccAlarmThreshold Integer32,
		cienaCesPortXcvrHighBiasAlarmThreshold Integer32,
		cienaCesPortXcvrLowBiasAlarmThreshold Integer32,
		cienaCesPortXcvrHighTxPwAlarmThreshold Integer32,
		cienaCesPortXcvrLowTxPwAlarmThreshold Integer32,
		cienaCesPortXcvrHighRxPwAlarmThreshold Integer32,
		cienaCesPortXcvrLowRxPwAlarmThreshold Integer32,
		cienaCesPortXcvrNotifChassisIndex Unsigned32,
		cienaCesPortXcvrNotifShelfIndex Unsigned32,
		cienaCesPortXcvrNotifSlotIndex Unsigned32,
		cienaCesPortXcvrNotifPortNumber Unsigned32,
		cienaCesPortXcvrIdentiferType INTEGER,
		cienaCesPortXcvrExtIdentiferType INTEGER,
		cienaCesPortXcvrConnectorType INTEGER,
		cienaCesPortXcvrType INTEGER,
		cienaCesPortXcvrAdminState INTEGER,
		cienaCesPortXcvrVendorName DisplayString,
		cienaCesPortXcvrVendorOUI OCTET STRING,
		cienaCesPortXcvrVendorPartNum DisplayString,
		cienaCesPortXcvrRevNum DisplayString,
		cienaCesPortXcvrSerialNum DisplayString,
		cienaCesPortXcvrMfgDate DisplayString,
		cienaCesPortXcvrWaveLength INTEGER,
		cienaCesPortXcvrTxState INTEGER,
		cienaCesPortXcvrTxFaultStatus INTEGER,
		cienaCesPortXcvrTxOutputPower INTEGER,
		cienaCesPortXcvrFecMode INTEGER,
		cienaCesPortXcvrNotifPortName DisplayString,
		cienaCesPortXcvrPwrUsage Unsigned32,
		cienaCesPortXcvrPortNumber Unsigned32,
		cienaCesPortXcvrPortName DisplayString
		}

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrId OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		ACCESS not-accessible
		STATUS current
		DESCRIPTION
			"The ID for the transceiver."
	::= { cienaCesPortXcvrEntry 1 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrOperState OBJECT-TYPE
		SYNTAX INTEGER
		{
			disabled(1),
			enabled(2),
			loopback(3),
			notPresent(4),
			faulted(5)
		}
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The operational state of the transceiver."
	::= { cienaCesPortXcvrEntry 2 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrTemperature OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		UNITS
			"celsius"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The temperature of the transceiver.
			Units are celsius."
	::= { cienaCesPortXcvrEntry 3 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrVcc OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		UNITS
			"mV"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The voltage of the transceiver.
			Units are milli volts"
	::= { cienaCesPortXcvrEntry 4 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrBias OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		UNITS
			"mA"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The bias of the transceiver.
			Units are milli amps"
	::= { cienaCesPortXcvrEntry 5 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrRxPower OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		UNITS
			"uW"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The measured receive power of the transceiver. 
			Units are micro watts."
	::= { cienaCesPortXcvrEntry 6 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrHighTempAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"celsius"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the higher threshold for the temperature alarm.
			Units are celsius."
	::= { cienaCesPortXcvrEntry 7 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrLowTempAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"celsius"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the lower threshold for the temperature alarm.
			Units are celsius."
	::= { cienaCesPortXcvrEntry 8 }

	-- *******.4.1.1271.*******.*******
	cienaCesPortXcvrHighVccAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"mV"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the higher threshold for the voltage alarm.
			Units are milli volts"
	::= { cienaCesPortXcvrEntry 9 }

	-- *******.4.1.1271.*******.*******0
	cienaCesPortXcvrLowVccAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"mV"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the lower threshold for the voltage alarm.
			Units are milli volts"
	::= { cienaCesPortXcvrEntry 10 }

	-- *******.4.1.1271.*******.*******1
	cienaCesPortXcvrHighBiasAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"mA"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the higher threshold for the bias alarm.
			Units are milli amps"
	::= { cienaCesPortXcvrEntry 11 }

	-- *******.4.1.1271.*******.*******2
	cienaCesPortXcvrLowBiasAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"mA"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the lower threshold for the bias alarm.
			Units are milli amps"
	::= { cienaCesPortXcvrEntry 12 }

	-- *******.4.1.1271.*******.*******3
	cienaCesPortXcvrHighTxPwAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"uW"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the higher threshold for the Tx power alarm.
			Units are micro watts"
	::= { cienaCesPortXcvrEntry 13 }

	-- *******.4.1.1271.*******.*******4
	cienaCesPortXcvrLowTxPwAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"uW"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the lower threshold for the Tx power alarm.
			Units are micro watts."
	::= { cienaCesPortXcvrEntry 14 }

	-- *******.4.1.1271.*******.*******5
	cienaCesPortXcvrHighRxPwAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"uW"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the higher threshold for the Rx power alarm.
			Units are micro watts."
	::= { cienaCesPortXcvrEntry 15 }

	-- *******.4.1.1271.*******.********
	cienaCesPortXcvrLowRxPwAlarmThreshold OBJECT-TYPE
		SYNTAX Integer32
		UNITS
			"uW"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the lower threshold for the Rx power alarm.
			Units are micro watts."
	::= { cienaCesPortXcvrEntry 16 }

	-- *******.4.1.1271.*******.********
	cienaCesPortXcvrNotifChassisIndex OBJECT-TYPE
		SYNTAX Unsigned32 (1)
		ACCESS accessible-for-notify
		STATUS current
		DESCRIPTION
			"Indicates the chassis index for the port used for trap definition."
	::= { cienaCesPortXcvrEntry 17 }

	-- *******.4.1.1271.*******.********
	cienaCesPortXcvrNotifShelfIndex OBJECT-TYPE
		SYNTAX Unsigned32 (1)
		ACCESS accessible-for-notify
		STATUS current
		DESCRIPTION
			"Indicates the shelf index for the port used for trap definition."
	::= { cienaCesPortXcvrEntry 18 }

	-- *******.4.1.1271.*******.********
	cienaCesPortXcvrNotifSlotIndex OBJECT-TYPE
		SYNTAX Unsigned32 (1..7)
		ACCESS accessible-for-notify
		STATUS current
		DESCRIPTION
			"Indicates the slot index for the port used for trap definition."
	::= { cienaCesPortXcvrEntry 19 }

	-- *******.4.1.1271.*******.********
	cienaCesPortXcvrNotifPortNumber OBJECT-TYPE
		SYNTAX Unsigned32 (1..65535)
		ACCESS accessible-for-notify
		STATUS current
		DESCRIPTION
			"Indicates the port number for the corresponding PGID
			used for trap definition."
	::= { cienaCesPortXcvrEntry 20 }

	-- *******.4.1.1271.*******.*******1
	cienaCesPortXcvrIdentiferType OBJECT-TYPE
		SYNTAX INTEGER
		{
			unknown(1),
			gbic(2),
			solderedType(3),
			sfp(4),
			xbi(5),
			xenpak(6),
			xfp(7),
			xff(8),
			xfpe(9),
			xpak(10),
			x2(11),
			reserved(12),
			vendorSpecific(13),
			cfp(14),
			qsfpPlus(15),
			qsfp28(16),
			cfp2Dco(17)
		}
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Type for the transceiver."
	::= { cienaCesPortXcvrEntry 21 }

	-- *******.4.1.1271.*******.*******2
	cienaCesPortXcvrExtIdentiferType OBJECT-TYPE
		SYNTAX INTEGER
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Extended identifier type represents for this transceiver."
	::= { cienaCesPortXcvrEntry 22 }

	-- *******.4.1.1271.*******.*******3
	cienaCesPortXcvrConnectorType OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Type of connector:
			
			unknown(1)
			sc(2)
			fiberChannelStyle1(3)
			fiberChannelStyle2(4)
			bnc/tnc(5)
			coaxialHeader(6)
			fiberJack(7)
			lc(8)
			mt-rj(9)
			mu(10)
			sg(11)
			opticalPitTail(12)
			reserved(13..32)
			hssdc(33)
			copperPitTail(34)
			reserved(35..128)
			vendorSpecific(129..256)"
	::= { cienaCesPortXcvrEntry 23 }

	-- *******.4.1.1271.*******.*******4
	cienaCesPortXcvrType OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Type of transceiver."
	::= { cienaCesPortXcvrEntry 24 }

	-- *******.4.1.1271.*******.*******5
	cienaCesPortXcvrVendorName OBJECT-TYPE
		SYNTAX DisplayString
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"String containing this transceiver's vendor name."
	::= { cienaCesPortXcvrEntry 25 }

	-- *******.4.1.1271.*******.*******6
	cienaCesPortXcvrVendorOUI OBJECT-TYPE
		SYNTAX OCTET STRING (SIZE (0..255))
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"String containing this transceiver's vendor OUI."
	::= { cienaCesPortXcvrEntry 26 }

	-- *******.4.1.1271.*******.*******7
	cienaCesPortXcvrVendorPartNum OBJECT-TYPE
		SYNTAX DisplayString
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"String containing this transceiver's vendor part number."
	::= { cienaCesPortXcvrEntry 27 }

	-- *******.4.1.1271.*******.********
	cienaCesPortXcvrRevNum OBJECT-TYPE
		SYNTAX DisplayString
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"String containing this tranceiver's part revision number."
	::= { cienaCesPortXcvrEntry 28 }

	-- *******.4.1.1271.*******.*******9
	cienaCesPortXcvrSerialNum OBJECT-TYPE
		SYNTAX DisplayString
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"String containing this tranceiver's part serial number."
	::= { cienaCesPortXcvrEntry 29 }

	-- *******.4.1.1271.*******.*******0
	cienaCesPortXcvrMfgDate OBJECT-TYPE
		SYNTAX DisplayString
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"String containing this transceiver's manufactured date."
	::= { cienaCesPortXcvrEntry 30 }

	-- *******.4.1.1271.*******.*******1
	cienaCesPortXcvrWaveLength OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The wavelength of the transceiver. Units are nano meter."
	::= { cienaCesPortXcvrEntry 31 }

	-- *******.4.1.1271.*******.*******2
	cienaCesPortXcvrTxState OBJECT-TYPE
		SYNTAX INTEGER
		{
			enabled(1),
			disabled(2)
		}
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates whether this transceiver is currently set to transmit."
	::= { cienaCesPortXcvrEntry 32 }

	-- *******.4.1.1271.*******.*******3
	cienaCesPortXcvrTxFaultStatus OBJECT-TYPE
		SYNTAX INTEGER
		{
			fault(1),
			noFault(2)
		}
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the fault status of this transceiver."
	::= { cienaCesPortXcvrEntry 33 }

	-- *******.4.1.1271.*******.*******4
	cienaCesPortXcvrAdminState OBJECT-TYPE
		SYNTAX INTEGER
		{
			disabled(1),
			enabled(2),
			loopback(3)
		}
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The administrative state of the transceiver."
	::= { cienaCesPortXcvrEntry 34 }

	-- *******.4.1.1271.*******.*******5
	cienaCesPortXcvrTxOutputPower OBJECT-TYPE
		SYNTAX INTEGER (1..65535)
		UNITS
			"uW"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The measured transmitted output power of the transceiver. 
			Units are micro watts."
	::= { cienaCesPortXcvrEntry 35 }

	-- *******.4.1.1271.*******.*******6
	cienaCesPortXcvrFecMode OBJECT-TYPE
		SYNTAX INTEGER
		{
			none(1),
			gfec(2),
			efec(3)
		}
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"This represents the transceiver's FEC Mode.
			gfec means generic forward error correction,
			efec means enhanced forward error correction."
	::= { cienaCesPortXcvrEntry 36 }

	-- *******.4.1.1271.*******.*******7
	cienaCesPortXcvrNotifPortName OBJECT-TYPE
		SYNTAX DisplayString
		ACCESS accessible-for-notify
		STATUS current
		DESCRIPTION
			"Indicates the port name for the corresponding PGID
			used for trap definition."
	::= { cienaCesPortXcvrEntry 37 }

	-- *******.4.1.1271.*******.*******8
	cienaCesPortXcvrPwrUsage OBJECT-TYPE
		SYNTAX Unsigned32
		UNITS
			"uW"
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"The measured power consumed by the transceiver.
			Units are micro watts."
	::= { cienaCesPortXcvrEntry 38 }

	-- *******.4.1.1271.*******.*******9
	cienaCesPortXcvrPortNumber OBJECT-TYPE
		SYNTAX Unsigned32 (1..65535)
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the port name for the corresponding PGID"
	::= { cienaCesPortXcvrEntry 39 }

	-- *******.4.1.1271.*******.*******0
	cienaCesPortXcvrPortName OBJECT-TYPE
		SYNTAX DisplayString
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates the port name for the corresponding PGID"
	::= { cienaCesPortXcvrEntry 40 }

	-- *******.4.1.1271.*******.2
	cienaCesPortXcvrNotif OBJECT IDENTIFIER ::= { cienaCesPortXcvrMIBObjects 2 }

	-- *******.4.1.1271.*******.2.1
	cienaCesPortXcvrEventType OBJECT-TYPE
		SYNTAX INTEGER
		{
			inserted(1),
			removed(2)
		}
		ACCESS read-only
		STATUS current
		DESCRIPTION
			"Indicates if the transceiver specified by the ciena54XXPortXcvrId has come up, 
			gone down or has been selected."
	::= { cienaCesPortXcvrNotif 1 }

	-- *******.4.1.1271.*******.2.2
	cienaCesPortXcvrErrorType OBJECT-TYPE
		SYNTAX INTEGER
		{
			none(0),
			chksumFailed(1),
			opticalFault(2)
		}
		ACCESS accessible-for-notify
		STATUS current
		DESCRIPTION
			"Indicates if the transceiver specified by the cienaCesPortXcvrId is faulted because of 
			checksum failure or optical fault. This object only makes sense if the transceiver has 
			been detected faulted; otherwise it returns 'none'."
	::= { cienaCesPortXcvrNotif 2 }

	-- *******.4.1.1271.*******
	cienaCesPortXcvrMIBConformance OBJECT IDENTIFIER ::= { cienaCesPortXcvrMIB 3 }

	-- *******.4.1.1271.*******.1
	cienaCesPortXcvrMIBCompliances OBJECT IDENTIFIER ::= { cienaCesPortXcvrMIBConformance 1 }

	-- *******.4.1.1271.*******.2
	cienaCesPortXcvrMIBGroups OBJECT IDENTIFIER ::= { cienaCesPortXcvrMIBConformance 2 }

	-- *******.4.1.1271.2.2.9
	cienaCesPortXcvrMIBNotificationPrefix OBJECT IDENTIFIER ::= { cienaCesNotifications 9 }

	-- *******.4.1.1271.*******
	cienaCesPortXcvrMIBNotifications OBJECT IDENTIFIER ::= { cienaCesPortXcvrMIBNotificationPrefix 0 }

	-- *******.4.1.1271.*******.1
	cienaCesPortXcvrRemovedNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrRemovedNotification is sent if the transceiver has been removed. 
			To enable the device to send this notification: cienaCesPortXcvrLinkStateChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to 
			enabled. These objects are set to enabled by default. Variable bindings include: 
			cienaGlobalSeverity, cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, 
			cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber
			and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 1 }

	-- *******.4.1.1271.*******.2
	cienaCesPortXcvrInsertedNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrInsertedNotification is sent if the transceiver has been inserted. To 
			enable the device to send this notification: cienaCesPortXcvrLinkStateChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to 
			enabled. These objects are set to enabled by default. Variable bindings include: 
			cienaGlobalSeverity, cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, 
			cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber
			and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 2 }

	-- *******.4.1.1271.*******.5
	cienaCesPortXcvrErrorTypeNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrErrorType, cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrErrorTypeNotification is sent if the transceiver is detected to be faulted.
			The reason for the failure is specified by cienaCesPortXcvrErrorType. To enable the device to 
			send this notification: cienaCesPortXcvrErrorTrapState, cienaCesLogicalPortConfigPortAllTrapState,
			and cienaCesPortAllTrapState need to be set to enabled. These objects are enabled by default. 
			Variable bindings include: cienaGlobalSeverity, cienaGlobalMacAddress, 
			cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, 
			cienaCesPortXcvrNotifPortNumber, cienaCesPortXcvrErrorType and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 5 }

	-- *******.4.1.1271.*******.6
	cienaCesPortXcvrTempHighNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTempHighNotification is sent if the transceiver temperature exceeds the 
			threshold. To enable the device to send this notification: cienaCesPortXcvrTempChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 6 }

	-- *******.4.1.1271.*******.7
	cienaCesPortXcvrTempLowNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTempLowNotification is sent if the transceiver temperature falls below the 
			threshold. To enable the device to send this notification: cienaCesPortXcvrTempChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex,
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 7 }

	-- *******.4.1.1271.*******.8
	cienaCesPortXcvrTempNormalNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTempNormalNotification is sent when the transceiver temperature returns to 
			normal state.  To enable the device to send this notification: cienaCesPortXcvrTempChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex,
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 8 }

	-- *******.4.1.1271.*******.9
	cienaCesPortXcvrVoltageHighNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrVoltageHighNotification is sent if the transceiver voltage exceeds the 
			threshold. To enable the device to send this notification: cienaCesPortXcvrVoltageChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, cienaCesPortAllTrapState needs to be set to enabled
			     These objects are set to enabled by default. Variable bindings include: 
			     cienaGlobalSeverity, cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex,
			     cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and
			     cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 9 }

	-- *******.4.1.1271.*******.10
	cienaCesPortXcvrVoltageLowNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrVoltageLowNotification is sent if the transceiver voltage falls below the 
			threshold. To enable the device to send this notification: cienaCesPortXcvrVoltageChangeTrapState,
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex,
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 10 }

	-- *******.4.1.1271.*******.11
	cienaCesPortXcvrVoltageNormalNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrVoltageNormalNotification is sent when the transceiver voltage returns back
			to normal state. To enable the device to send this notification: cienaCesPortXcvrVoltageChangeTrapState,
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 11 }

	-- *******.4.1.1271.*******.12
	cienaCesPortXcvrBiasHighNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrBiasHighNotification is sent if the transceiver bias exceeds the 
			threshold. To enable the device to send this notification: cienaCesPortXcvrBiasChangeTrapState,
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity, 
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 12 }

	-- *******.4.1.1271.*******.13
	cienaCesPortXcvrBiasLowNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrBiasLowNotification is sent if the transceiver bias falls below the 
			threshold. To enable the device to send this notification: cienaCesPortXcvrBiasChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 13 }

	-- *******.4.1.1271.*******.14
	cienaCesPortXcvrBiasNormalNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrBiasNormalNotification is sent when the transceiver bias returns to normal 
			state. To enable the device to send this notification: cienaCesPortXcvrBiasChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 14 }

	-- *******.4.1.1271.*******.15
	cienaCesPortXcvrTxPowerHighNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTxPowerHighNotification is sent if the transceiver TxPower exceeds the 
			threshold. To enable the device to send this notification: cienaCesPortXcvrTxPowerChangeTrapState,
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 15 }

	-- *******.4.1.1271.*******.16
	cienaCesPortXcvrTxPowerLowNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTxPowerLowNotification is sent if the transceiver TxPower falls below 
			the threshold. To enable the device to send this notification: cienaCesPortXcvrTxPowerChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled.
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 16 }

	-- *******.4.1.1271.*******.17
	cienaCesPortXcvrTxPowerNormalNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTxPowerNormalNotification is sent when the transceiver TxPower returns to 
			normal state. To enable the device to send this notification: cienaCesPortXcvrTxPowerChangeTrapState,
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled. 
			These above values are set to enabled by default. Variable bindings include: cienaGlobalSeverity,
			     cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			     cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 17 }

	-- *******.4.1.1271.*******.18
	cienaCesPortXcvrRxPowerHighNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrRxPowerHighNotification is sent if the transceiver RxPower exceeds 
			the threshold. To enable the device to send this notification: cienaCesPortXcvrRxPowerChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled. 
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity, 
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex,
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 18 }

	-- *******.4.1.1271.*******.19
	cienaCesPortXcvrRxPowerLowNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrRxPowerLowNotification is sent if the transceiver RxPower falls below
			the the threshold. To enable the device to send this notification: 
			cienaCesPortXcvrRxPowerChangeTrapState, cienaCesLogicalPortConfigPortAllTrapState,
			cienaCesPortAllTrapState needs to be set to enabled. These objects are set to enabled 
			by default. Variable bindings include: cienaGlobalSeverity, cienaGlobalMacAddress, 
			cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, 
			cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 19 }

	-- *******.4.1.1271.*******.20
	cienaCesPortXcvrRxPowerNormalNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrRxPowerNormalNotification is sent when the transceiver RxPower returns 
			to normal state. To enable the device to send this notification: 
			cienaCesPortXcvrRxPowerChangeTrapState, cienaCesLogicalPortConfigPortAllTrapState, and 
			cienaCesPortAllTrapState needs to be set to enabled. These objects are set to enabled by 
			default. Variable bindings include: cienaGlobalSeverity, cienaGlobalMacAddress, 
			cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, 
			cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 20 }

	-- *******.4.1.1271.*******.21
	cienaCesPortXcvrSpeedInfoMissingNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrSpeedInfoMissingNotification is sent if the transceiver speed 
			information is not found. To enable the device to send this notification: 
			cienaCesPortXcvrSpeedInfoTrapState, cienaCesLogicalPortConfigPortAllTrapState, and 
			cienaCesPortAllTrapState need to be set to enabled. These objects are set to enabled 
			by default. Variable bindings include: cienaGlobalSeverity, cienaGlobalMacAddress, 
			cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 21 }

	-- *******.4.1.1271.*******.22
	cienaCesPortXcvrUncertifiedNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrUncertifiedNotification is sent if the transceiver is not 
			certified for use by Ciena. To enable the device to send this notification: 
			cienaCesPortXcvrUncertifiedTrapState,  cienaCesLogicalPortConfigPortAllTrapState, 
			and cesPortAllTrapState need to be set to enabled. cienaCesPortXcvrUncertifiedTrapState 
			is set to disabled by default. Variable bindings include: cienaGlobalSeverity, cienaGlobalMacAddress, 
			cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 22 }

	-- *******.4.1.1271.*******.23
	cienaCesPortXcvrRxPowerHighWarningNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrRxPowerHighWarningNotification is sent if the transceiver RxPower exceeds 
			the threshold. To enable the device to send this notification: cienaCesPortXcvrRxPowerChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled. 
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity, 
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex,
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 23 }

	-- *******.4.1.1271.*******.24
	cienaCesPortXcvrRxPowerLowWarningNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrRxPowerLowWarningNotification is sent if the transceiver RxPower falls below
			the the threshold. To enable the device to send this notification: 
			cienaCesPortXcvrRxPowerChangeTrapState, cienaCesLogicalPortConfigPortAllTrapState,
			cienaCesPortAllTrapState needs to be set to enabled. These objects are set to enabled 
			by default. Variable bindings include: cienaGlobalSeverity, cienaGlobalMacAddress, 
			cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, 
			cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 24 }

	-- *******.4.1.1271.*******.25
	cienaCesPortXcvrTxPowerHighWarningNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTxPowerHighWarningNotification is sent if the transceiver TxPower exceeds 
			the threshold. To enable the device to send this notification: cienaCesPortXcvrTxPowerChangeTrapState, 
			cienaCesLogicalPortConfigPortAllTrapState, and cienaCesPortAllTrapState need to be set to enabled. 
			These objects are set to enabled by default. Variable bindings include: cienaGlobalSeverity, 
			cienaGlobalMacAddress, cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex,
			cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 25 }

	-- *******.4.1.1271.*******.26
	cienaCesPortXcvrTxPowerLowWarningNotification NOTIFICATION-TYPE
		OBJECTS { cienaGlobalSeverity, cienaGlobalMacAddress, 
		cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, 
		cienaCesPortXcvrNotifSlotIndex, cienaCesPortXcvrNotifPortNumber, 
		cienaCesPortXcvrNotifPortName } 
		STATUS current
		DESCRIPTION
			"A cienaCesPortXcvrTxPowerLowWarningNotification is sent if the transceiver TxPower falls below
			the the threshold. To enable the device to send this notification: 
			cienaCesPortXcvrTxPowerChangeTrapState, cienaCesLogicalPortConfigPortAllTrapState,
			cienaCesPortAllTrapState needs to be set to enabled. These objects are set to enabled 
			by default. Variable bindings include: cienaGlobalSeverity, cienaGlobalMacAddress, 
			cienaCesPortXcvrNotifChassisIndex, cienaCesPortXcvrNotifShelfIndex, cienaCesPortXcvrNotifSlotIndex, 
			cienaCesPortXcvrNotifPortNumber and cienaCesPortXcvrNotifPortName."
	::= { cienaCesPortXcvrMIBNotifications 26 }

END

 --
 -- CIENA-CES-PORT-XCVR-MIB
 --
