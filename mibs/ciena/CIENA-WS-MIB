
CIENA-WS-MIB DEFINITIONS ::= BEGIN

IMPORTS 
	enterprises, MODULE-IDENTITY, OBJECT-IDENTITY			
		FROM SNMPv2-SMI;

ciena MODULE-IDENTITY
	LAST-UPDATED "201804270000Z"
	ORGANIZATION
		"Ciena Corporation"
	CONTACT-INFO
      "Web: http://www.ciena.com
      Postal:   7035 Ridge Road
                Hanover, Maryland 21076
                U.S.A
      Phone:    ******-921-1144
      Fax:      ******-694-5750"
      DESCRIPTION
		"Top-level MIB structure for Ciena's Waveserver."
       REVISION "201804270000Z" 
       DESCRIPTION
		"Added cienaWsPlatformConfig for Ai1.2."
       ::= { enterprises 1271 }
	
--
-- Node definitions
--
	
waveserver OBJECT-IDENTITY
	STATUS current
	DESCRIPTION
		"Root identifier for Ciena's Waveserver product."
	::= { ciena 3 }

cienaWsStatistics	  OBJECT-IDENTITY
	STATUS	obsolete
	DESCRIPTION
		"Statistics for Waveserver."
	::= { waveserver 3 }			                    
	                
cienaWsNotifications OBJECT-IDENTITY
	STATUS	current
	DESCRIPTION
		"Waveserver notifications."
	::= { waveserver 2 }	
	
cienaWsNotificationsControlModule	OBJECT-IDENTITY
	STATUS	current
	DESCRIPTION
		"Waveserver control module." 		                                    
	::= { cienaWsNotifications 1 }

cienaWsConfigV1 OBJECT-IDENTITY
	STATUS	current
	DESCRIPTION
		"Configuration for the Waveserver 1.0 and 1.1 releases."
	::= { waveserver 1 }			                                    

cienaWsConfig OBJECT-IDENTITY
   STATUS     current
   DESCRIPTION
      "Root object for the Waveserver API in 1.2 and beyond."
::= { waveserver 4 }

cienaWsPlatformConfig OBJECT-IDENTITY
   STATUS     current
   DESCRIPTION
      "Root object for the Waveserver Platform API in release 1.2 and beyond."
::= { waveserver 5 }

END
