NUTANIX-MIB DEFINITIONS ::= BEGIN

  IMPORTS
    enterprises, MODULE-IDENTITY, OBJECT-TYPE, Integer32, Counter64,
    NOTIFICATION-TYPE, Unsigned32
      FROM SNMPv2-SMI
    DisplayString
      FROM SNMPv2-TC;

  nutanix MODULE-IDENTITY
    LAST-UPDATED         "202111081811Z"
    ORGANIZATION         "Nutanix Inc."
    CONTACT-INFO         "<EMAIL>"
    DESCRIPTION          "Nutanix Cluster Managment Information Base"
    REVISION             "202111081811Z"
    DESCRIPTION          "SNMP MIB for Nutanix Cluster software."
    ::= { enterprises 41263 }

--
-- Cluster wide scalars starting from sub oid 501. This leaves room to add
-- more table types in sequential oid order in future.
--

  clusterName OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Name of the cluster."
    ::= { nutanix 501 }

  clusterVersion OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Current cluster version. This is the nutanix-core
                          package version expected on all the Controller VMs."
    ::= { nutanix 502 }

  clusterStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Current Status of the cluster. This will usually be
                          one of started or stopped"
    ::= { nutanix 503 }

  clusterTotalStorageCapacity OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total storage capacity of the cluster in bytes."
    ::= { nutanix 504 }

  clusterUsedStorageCapacity OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of bytes of storage used on the cluster."
    ::= { nutanix 505 }

  clusterIops OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Cluster wide average IO operations per second."
    ::= { nutanix 506 }

  clusterLatency OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Cluster wide average latency."
    ::= { nutanix 507 }

  clusterIOBandwidth OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Cluster wide IO bandwidth in KBps."
    ::= { nutanix 508 }

--
-- Nutanix SNMP table definitions.
--
-- Software version table
--

  softwareVersionTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF SvtEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Table of software versions for packages on all
                          Controller VMs."
    ::= { nutanix 1 }

  svtEntry OBJECT-TYPE
    SYNTAX               SvtEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Software version table entry containing software
                          versions on the Controller VMs."
    INDEX                { svtIndex }
    ::= { softwareVersionTable 1 }

  SvtEntry ::= SEQUENCE {
    svtIndex                  Integer32,
    svtControllerVMId         DisplayString,
    svtNutanixBootstrap       DisplayString,
    svtNutanixInfrastructure  DisplayString,
    svtNutanixCore            DisplayString,
    svtNutanixToolchain       DisplayString,
    svtNutanixServiceability  DisplayString,
    svtLinuxKernel            DisplayString
  }

  svtIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Index for software version table entries."
    ::= { svtEntry 1 }

  svtControllerVMId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Nutanix Controller VM Id."
    ::= { svtEntry 2 }

  svtNutanixBootstrap OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "The nutanix-bootstrap software package version."
    ::= { svtEntry 3 }

  svtNutanixInfrastructure OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "The nutanix-infrastructure software package version."
    ::= { svtEntry 4 }

  svtNutanixCore OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "The nutanix-core software package version."
    ::= { svtEntry 5 }

  svtNutanixToolchain OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "The nutanix-toolchain software package version."
    ::= { svtEntry 6 }

  svtNutanixServiceability OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "The nutanix-serviceability software package version."
    ::= { svtEntry 7 }

  svtLinuxKernel OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "The linux kernel version currently installed."
    ::= { svtEntry 8 }

--
-- Service status table
--

  serviceStatusTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF SstEntry
    MAX-ACCESS           not-accessible
    STATUS               obsolete
    DESCRIPTION          "Table of status of each service with one row per
                          Controller VM."
    ::= { nutanix 2 }

  sstEntry OBJECT-TYPE
    SYNTAX               SstEntry
    MAX-ACCESS           not-accessible
    STATUS               obsolete
    DESCRIPTION          "Table entry containing status of services on one
                          node."
    INDEX                { sstIndex }
    ::= { serviceStatusTable 1 }

  SstEntry ::= SEQUENCE {
    sstIndex                   Integer32,
    sstControllerVMId          DisplayString,
    sstControllerVMStatus      DisplayString,
    sstZeusStatus              DisplayString,
    sstScavengerStatus         DisplayString,
    sstMedusaStatus            DisplayString,
    sstPithosStatus            DisplayString,
    sstStargateStatus          DisplayString,
    sstChronosStatus           DisplayString,
    sstCuratorStatus           DisplayString,
    sstPrismStatus             DisplayString,
    sstAlertManagerStatus      DisplayString,
    sstStatsAggregatorStatus   DisplayString,
    sstSysStatCollectorStatus  DisplayString
  }

  sstIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "A unique index for each row in the service status
                          table."
    ::= { sstEntry 1 }

  sstControllerVMId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Nutanix Controller VM Id."
    ::= { sstEntry 2 }

  sstControllerVMStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of the node."
    ::= { sstEntry 3 }

  sstZeusStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Zeus on the node. A comma separated list
                          of pids of the zeus service."
    ::= { sstEntry 4 }

  sstScavengerStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Scavenger on the node. A comma separated
                          list of pids of the scavenger service."
    ::= { sstEntry 5 }

  sstMedusaStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Medusa on the node. A comma separated list
                          of pids of the medusa service."
    ::= { sstEntry 6 }

  sstPithosStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Pithos on the node. A comma separated list
                          of pids of the pithos service."
    ::= { sstEntry 7 }

  sstStargateStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Stargate on the node. A comma separated
                          list of pids of the stargate service."
    ::= { sstEntry 8 }

  sstChronosStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Chronos on the node. A comma separated list
                          of pids of the chronos service."
    ::= { sstEntry 9 }

  sstCuratorStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Curator on the node. A comma separated list
                          of pids of the curator service."
    ::= { sstEntry 10 }

  sstPrismStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Prism on the node. A comma separated list
                          of pids of the prism service."
    ::= { sstEntry 11 }

  sstAlertManagerStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Alert Manager on the node. A comma
                          separated list of pids of the alert manager service."
    ::= { sstEntry 12 }

  sstStatsAggregatorStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of Stats Aggregator on the node. A comma
                          separated list of pids of the stats aggregator
                          service."
    ::= { sstEntry 13 }

  sstSysStatCollectorStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Status of SysStatCollector on the node. A comma
                          separated list of pids of the sys stat collector
                          service."
    ::= { sstEntry 14 }

--
-- Disk Status Table
--

  diskStatusTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF DstEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Table provides disk status on all CVMs."
    ::= { nutanix 3 }

  dstEntry OBJECT-TYPE
    SYNTAX               DstEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Table entry containing status of a disk on a CVM."
    INDEX                { dstIndex}
    ::= { diskStatusTable 1 }

  DstEntry ::= SEQUENCE {
    dstIndex               Integer32,
    dstDiskId              DisplayString,
    dstControllerVMId      DisplayString,
    dstSerial              DisplayString,
    dstNumRawBytes         Counter64,
    dstNumTotalBytes       Counter64,
    dstNumFreeBytes        Counter64,
    dstNumTotalInodes      Counter64,
    dstNumFreeInodes       Counter64,
    dstAverageLatency      Counter64,
    dstIOBandwidth         Counter64,
    dstNumberIops          Counter64,
    dstState               INTEGER
  }

  dstIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "A unique index for each row in the disk status
                          table."
    ::= { dstEntry 1 }

  dstDiskId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "A unique disk id for each disk."
    ::= { dstEntry 2 }

  dstControllerVMId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Nutanix Controller VM unique identifier."
    ::= { dstEntry 3 }

  dstSerial OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Disk Serial Number"
    ::= { dstEntry 4 }

  dstNumRawBytes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Maximum number of raw bytes available on the device."
    ::= { dstEntry 5 }

  dstNumTotalBytes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of bytes usable on the device through
                          its file system."
    ::= { dstEntry 6 }

  dstNumFreeBytes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of bytes availabe to the non-root users
                          on the device through its file system."
    ::= { dstEntry 7 }

  dstNumTotalInodes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Maximum number of inodes usable on the device
                          through its file system."
    ::= { dstEntry 8 }

  dstNumFreeInodes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               obsolete
    DESCRIPTION          "Total number of inodes availabe to the non-root
                          users on the device through its file system."
    ::= { dstEntry 9 }

  dstAverageLatency OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Average IO latency per micro seconds for the disk."
    ::= { dstEntry 10 }

  dstIOBandwidth OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "IO bandwidth in KBps for the disk."
    ::= { dstEntry 11 }

  dstNumberIops OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number IO per seconds for the disk."
    ::= { dstEntry 12 }

  dstState OBJECT-TYPE
    SYNTAX               INTEGER {
                         online(1),
                         offline(2)
                         }
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "State of the disk."
    ::= { dstEntry 13 }
--
-- Controller VM resource table.
--

  controllerVMResourceTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF CrtEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Table of resources per Controller VM."
    ::= { nutanix 4 }

  crtEntry OBJECT-TYPE
    SYNTAX               CrtEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Table entry containing resource information of
                          one Controller VM."
    INDEX                { crtIndex }
    ::= { controllerVMResourceTable 1 }

  CrtEntry ::= SEQUENCE {
    crtIndex           Integer32,
    crtControllerVMId  DisplayString,
    crtMemory          Counter64,
    crtNumCpus         Integer32,
    crtName            DisplayString
  }

  crtIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "A unique index for each row in the CVM memory
                          table."
    ::= { crtEntry 1 }

  crtControllerVMId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Nutanix Controller VM Id."
    ::= { crtEntry 2 }

  crtMemory OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total memory available on a CVM."
    ::= { crtEntry 3 }

  crtNumCpus OBJECT-TYPE
    SYNTAX               Integer32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of CPUs allocated to a CVM."
    ::= { crtEntry 4 }

  crtName OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Name of the Nutanix COntroller VM."
    ::= { crtEntry 5 }

 --
 -- Storage pool information table.
 --

  storagePoolInformationTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF SpitEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Storage pool information in a table form."
    ::= { nutanix 7 }

  spitEntry OBJECT-TYPE
    SYNTAX               SpitEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Storage pool information table entry."
    INDEX                { spitIndex }
    ::= { storagePoolInformationTable 1 }

  SpitEntry ::= SEQUENCE {
    spitIndex            Integer32,
    spitStoragePoolId    DisplayString,
    spitStoragePoolName  DisplayString,
    spitTotalCapacity    Counter64,
    spitUsedCapacity     Counter64,
    spitIOPerSecond      Integer32,
    spitAvgLatencyUsecs  Counter64,
    spitIOBandwidth      Counter64
  }

  spitIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Unique index for storage pool information table
                          entries."
    ::= { spitEntry 1 }

  spitStoragePoolId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Storage pool id."
    ::= { spitEntry 2 }

  spitStoragePoolName OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Name of the storage pool."
    ::= { spitEntry 3 }

  spitTotalCapacity OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total capacity of the storage pool in bytes."
    ::= { spitEntry 4 }

  spitUsedCapacity OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of bytes used in the storage pool."
    ::= { spitEntry 5 }

  spitIOPerSecond OBJECT-TYPE
    SYNTAX               Integer32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of IO operations served per second from this
                          storage pool."
    ::= { spitEntry 6 }

  spitAvgLatencyUsecs OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Average IO latency for this storage pool in
                          microseconds."
    ::= { spitEntry 7 }

  spitIOBandwidth OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "IO bandwidth in KBps for the storage pool."
    ::= { spitEntry 8 }

--
-- Container information table
--

  containerInformationTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF CitEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Storage container information in a table form."
    ::= { nutanix 8 }

  citEntry OBJECT-TYPE
    SYNTAX               CitEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Storage container information table entry."
    INDEX                { citIndex }
    ::= { containerInformationTable 1 }

  CitEntry ::= SEQUENCE {
    citIndex             Integer32,
    citContainerId       DisplayString,
    citContainerName     DisplayString,
    citTotalCapacity     Counter64,
    citUsedCapacity      Counter64,
    citIOPerSecond       Integer32,
    citAvgLatencyUsecs   Counter64,
    citIOBandwidth       Counter64
  }

  citIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Unique index for storage container information table
                          entries."
    ::= { citEntry 1 }

  citContainerId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Storage container id."
    ::= { citEntry 2 }

  citContainerName OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Name of the storage container."
    ::= { citEntry 3 }

  citTotalCapacity OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total capacity of the storage container in bytes."
    ::= { citEntry 4 }

  citUsedCapacity OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of bytes used in the storage container."
    ::= { citEntry 5 }

  citIOPerSecond OBJECT-TYPE
    SYNTAX               Integer32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of IO operations served per second from this
                          storage container."
    ::= { citEntry 6 }

  citAvgLatencyUsecs OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Average IO latency for this storage container in
                          microseconds."
    ::= { citEntry 7 }

  citIOBandwidth OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "IO bandwidth in KBps for the storage container."
    ::= { citEntry 8 }

--
-- Hypervisor information table
--

  hypervisorInformationTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF HypervisorEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Hypervisor information in a table form."
    ::= { nutanix 9 }

  hypervisorEntry OBJECT-TYPE
    SYNTAX               HypervisorEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Vm information table entry."
    INDEX                { hypervisorIndex }
    ::= { hypervisorInformationTable 1 }

  HypervisorEntry ::= SEQUENCE {
    hypervisorIndex               Integer32,
    hypervisorId                  DisplayString,
    hypervisorName                DisplayString,
    hypervisorVmCount             Unsigned32,
    hypervisorCpuCount            Unsigned32,
    hypervisorCpuUsagePercent     Unsigned32,
    hypervisorMemory              Counter64,
    hypervisorMemoryUsagePercent  Unsigned32,
    hypervisorReadIOPerSecond     Unsigned32,
    hypervisorWriteIOPerSecond    Unsigned32,
    hypervisorAverageLatency      Counter64,
    hypervisorIOBandwidth         Counter64,
    hypervisorRxBytes             Counter64,
    hypervisorTxBytes             Counter64,
    hypervisorRxDropCount         Counter64,
    hypervisorTxDropCount         Counter64
  }

  hypervisorIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Unique index for hypervisor information table
                          entries."
    ::= { hypervisorEntry 1 }

  hypervisorId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Hypervisor Id for the hypervisor."
    ::= { hypervisorEntry 2 }

  hypervisorName OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Name of the Hypervisor."
    ::= { hypervisorEntry 3 }

  hypervisorVmCount OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of VM configured on the hypervisor."
    ::= { hypervisorEntry 4 }

  hypervisorCpuCount OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of CPU threads present in the
                          hypervisor."
    ::= { hypervisorEntry 5 }

  hypervisorCpuUsagePercent OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "CPU usage percentage of the hypervisor."
    ::= { hypervisorEntry 6 }

  hypervisorMemory OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total memory available on the hypervisor in bytes."
    ::= { hypervisorEntry 7 }

  hypervisorMemoryUsagePercent OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Memory usage percentage of the hypervisor."
    ::= { hypervisorEntry 8 }

  hypervisorReadIOPerSecond OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of read IO operations served per second on
                          this hypervisor."
    ::= { hypervisorEntry 9 }

  hypervisorWriteIOPerSecond OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of write IO operations served per second on
                          this hypervisor."
    ::= { hypervisorEntry 10 }

  hypervisorAverageLatency OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Average IO latency per micro seconds of the
                          hypervisor."
    ::= { hypervisorEntry 11 }

  hypervisorIOBandwidth OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "IO bandwidth in KBps of the hypervisor."
    ::= { hypervisorEntry 12 }

  hypervisorRxBytes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of bytes recieved on the hypervisor."
    ::= { hypervisorEntry 13 }

  hypervisorTxBytes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of bytes transmitted from the
                          hypervisor."
    ::= { hypervisorEntry 14 }

  hypervisorRxDropCount OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of packets dropped while receiving
                          on the hypervisor."
    ::= { hypervisorEntry 15 }

  hypervisorTxDropCount OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of packets dropped while transmiting
                          from hypervisor."
    ::= { hypervisorEntry 16 }
--
-- VM information table
--

  vmInformationTable OBJECT-TYPE
    SYNTAX               SEQUENCE OF VmEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Vm information in a table form."
    ::= { nutanix 10 }

  vmEntry OBJECT-TYPE
    SYNTAX               VmEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Vm information table entry."
    INDEX                { vmIndex }
    ::= { vmInformationTable 1 }

  VmEntry ::= SEQUENCE {
    vmIndex               Integer32,
    vmId                  DisplayString,
    vmName                DisplayString,
    vmHypervisorId        DisplayString,
    vmPowerState          DisplayString,
    vmCpuCount            Unsigned32,
    vmCpuUsagePercent     Unsigned32,
    vmMemory              Counter64,
    vmMemoryUsagePercent  Unsigned32,
    vmReadIOPerSecond     Unsigned32,
    vmWriteIOPerSecond    Unsigned32,
    vmAverageLatency      Counter64,
    vmIOBandwidth         Counter64,
    vmRxBytes             Counter64,
    vmTxBytes             Counter64,
    vmRxDropCount         Counter64,
    vmTxDropCount         Counter64
  }

  vmIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Unique index for VM information table
                          entries."
    ::= { vmEntry 1 }

  vmId OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Nutanix unique identifier for VM."
    ::= { vmEntry 2 }

  vmName OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Name of the VM."
    ::= { vmEntry 3 }

  vmHypervisorId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Hypervisor id on which the VM resides."
    ::= { vmEntry 4 }

  vmPowerState OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Power state of the VM."
    ::= { vmEntry 5 }

  vmCpuCount OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of CPUs allocated to the VM."
    ::= { vmEntry 6 }

  vmCpuUsagePercent OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "CPU usage percentage of the VM."
    ::= { vmEntry 7 }

  vmMemory OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total memory available on the VM in bytes."
    ::= { vmEntry 8 }

  vmMemoryUsagePercent OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Memory usage percentage of the VM."
    ::= { vmEntry 9 }

  vmReadIOPerSecond OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of read IO operations served per second on
                          this VM."
    ::= { vmEntry 10 }

  vmWriteIOPerSecond OBJECT-TYPE
    SYNTAX               Unsigned32
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Number of write IO operations served per second on
                          this VM."
    ::= { vmEntry 11 }

  vmAverageLatency OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Average IO latency per micro seconds of the VM."
    ::= { vmEntry 12 }

  vmIOBandwidth OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "IO bandwidth in KBps of the VM."
    ::= { vmEntry 13 }

  vmRxBytes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of bytes recieved on the VM."
    ::= { vmEntry 14 }

  vmTxBytes OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of bytes transmitted from the VM."
    ::= { vmEntry 15 }

  vmRxDropCount OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of packets dropped while receiving
                          on the VM."
    ::= { vmEntry 16 }

  vmTxDropCount OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Total number of packets dropped while transmiting
                          from VM."
    ::= { vmEntry 17 }

--
-- Controller status table
--

  controllerStatusTable  OBJECT-TYPE
    SYNTAX               SEQUENCE OF CstEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Table of status of essential services with one row
                          per Controller VM."
    ::= { nutanix 11 }

  cstEntry OBJECT-TYPE
    SYNTAX               CstEntry
    MAX-ACCESS           not-accessible
    STATUS               current
    DESCRIPTION          "Table entry containing status of essential services
                          on one node."
    INDEX                { cstIndex }
    ::= { controllerStatusTable 1 }

  CstEntry ::= SEQUENCE {
    cstIndex                   Integer32,
    cstControllerVMId          DisplayString,
    cstControllerVMStatus      DisplayString,
    cstDataServiceStatus       DisplayString,
    cstMetadataServiceStatus   DisplayString
  }

  cstIndex OBJECT-TYPE
    SYNTAX               Integer32 (1..2147483647)
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "A unique index for each row in the controller status
                          table."
    ::= { cstEntry 1 }

  cstControllerVMId OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Nutanix Controller VM Id."
    ::= { cstEntry 2 }

  cstControllerVMStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Status of the node."
    ::= { cstEntry 3 }

  cstDataServiceStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Status of core data services on the Controller VM."
    ::= { cstEntry 4 }

  cstMetadataServiceStatus OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..255))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Status of metadata services on the Controller VM."
    ::= { cstEntry 5 }


--
-- Abstract alert object.
-- All alert data to be sent in a trap is grouped within this object. This
-- allows alert objects to appear grouped together in certain graphical MIB
-- viewers.
--
  ntxAlert OBJECT IDENTIFIER ::= {nutanix 999}

  ntxAlertCreationTime    OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Alert creation Timestamp in seconds from epoch."
    ::= {ntxAlert 1}

  ntxAlertDisplayMsg    OBJECT-TYPE
    SYNTAX               DisplayString (SIZE (0..1024))
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Display message for the sent alert."
    ::= {ntxAlert 2}

  ntxAlertTitle    OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Alert Title for the sent alert."
    ::= {ntxAlert 3}

  ntxAlertSeverity    OBJECT-TYPE
    SYNTAX               INTEGER {
                           informational(1),
                           warning(2),
                           critical(3),
                           audit(4)
                         }
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Alert Severity for the sent alert."
    ::= {ntxAlert 4}

  ntxAlertUuid    OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           accessible-for-notify
    STATUS               current
    DESCRIPTION          "This object incidates the Alert Uuid"
    ::= {ntxAlert 5}

  ntxAlertResolvedTime    OBJECT-TYPE
    SYNTAX               Counter64
    MAX-ACCESS           accessible-for-notify
    STATUS               current
    DESCRIPTION          "This object indicates the time when an alert was resolved. It is the
                          timestamp in seconds from epoch."
    ::= {ntxAlert 6}

  ntxAlertClusterName    OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "The name of the cluster generating the alert"
    ::= {ntxAlert 7}

  ntxAlertSourceEntityUuid  OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "UUID of the source entity of the alert."
    ::= {ntxAlert 8}

  ntxAlertSourceEntityName    OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Name of the source entity of the alert."
    ::= {ntxAlert 9}

  ntxAlertSourceEntityType    OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           read-only
    STATUS               current
    DESCRIPTION          "Source entity type of the alert."
    ::= {ntxAlert 10}

  ntxTrapName    OBJECT-TYPE
    SYNTAX               DisplayString
    MAX-ACCESS           accessible-for-notify
    STATUS               current
    DESCRIPTION          "This object incidates the name of MIB object which is a trap and has been
                          resolved"
    ::= {nutanix 992}

  ntxTrap   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg }
     STATUS                current
     DESCRIPTION           "Nutanix enterprise trap"
     ::= { nutanix 991 }

  ntxTrapResolved   NOTIFICATION-TYPE
     OBJECTS             { ntxTrapName, ntxAlertResolvedTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "A ntxTrapResolved trap signifies that the SNMP entity, acting in an agent
                           role, has detected that a trap, which is indicated by ntxTrapName, and is
                           related to an alert identified by ntxAlertUuid,has been resovled."
     ::= { nutanix 993 }

  ntxTrapClusterRunningOutOfStorageCapacitylowRunway   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Predict high storage space usage on cluster."
     ::= { nutanix 1000 }

  ntxTrapClusterRunningOutOfCPUCapacitylowRunway   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Predict high CPU resource usage."
     ::= { nutanix 1001 }

  ntxTrapNodeRunningOutOfCPUCapacitylowRunway   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Predict high CPU resource usage."
     ::= { nutanix 1002 }

  ntxTrapClusterRunningOutOfMemoryCapacitylowRunway   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Predict high memory resource usage."
     ::= { nutanix 1003 }

  ntxTrapNodeRunningOutOfMemoryCapacitylowRunway   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Predict high memory resource usage."
     ::= { nutanix 1004 }

  ntxTrapStorageContainerRunningOutOfStorageCapacitylowRunway   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Predict high storage space usage on Storage Container."
     ::= { nutanix 1005 }

  ntxTrapTestAlertTitle   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Test Alert."
     ::= { nutanix 1006 }

  ntxTrapMetadataDriveAutoAddDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata Drive AutoAdd Disabled."
     ::= { nutanix 1007 }

  ntxTrapNodeDetachedFromMetadataRing   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Node detached from metadata ring."
     ::= { nutanix 1008 }

  ntxTrapMetadataDynamicRingChangeOperationStuck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Dynamic Ring Change operation not making progress."
     ::= { nutanix 1009 }

  ntxTrapMetadataDynamicRingChangeOperationTooSlow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Dynamic Ring Change operation too slow."
     ::= { nutanix 1010 }

  ntxTrapMetadataDriveFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata Drive Failed."
     ::= { nutanix 1011 }

  ntxTrapLargeMetadataSizeDetected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Large metadata size detected."
     ::= { nutanix 1012 }

  ntxTrapMetadataDriveMarkedToBeAutoAdded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata Drive Marked To Be AutoAdded."
     ::= { nutanix 1013 }

  ntxTrapMetadataDriveDetached   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata Drive Detached."
     ::= { nutanix 1014 }

  ntxTrapMetadataRingImbalance   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cassandra Metadata Imbalance."
     ::= { nutanix 1015 }

  ntxTrapCassandraWaitingForDiskReplacement   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata disk not replaced for Disk Replace Op."
     ::= { nutanix 1016 }

  ntxTrapCloudApplianceDeploymentFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cloud Appliance Deployment Failed."
     ::= { nutanix 1017 }

  ntxTrapUnableToRemountDatastore   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to remount datastore."
     ::= { nutanix 1018 }

  ntxTrapRemountedDatastore   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Remounted datastore."
     ::= { nutanix 1019 }

  ntxTrapFailedToAllocateSnapshotReserveOnTheRemoteSite   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to allocate snapshot reserve on the remote site."
     ::= { nutanix 1020 }

  ntxTrapFailedToAllocateSnapshotReserve   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to allocate snapshot reserve."
     ::= { nutanix 1021 }

  ntxTrapMetroTakeoverOldPrimarySiteIsHostingVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metro takeover - old primary site is hosting VMs."
     ::= { nutanix 1022 }

  ntxTrapProtectionDomainIsInDecoupledState   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection domain is in decoupled state."
     ::= { nutanix 1023 }

  ntxTrapRemoteSiteLatencyIsHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Latency to a stretch remote site is high."
     ::= { nutanix 1024 }

  ntxTrapFailedToUpdateMetroAvailabilityFailureHandling   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Error in updating failure-handling on Metro Availability protection domain."
     ::= { nutanix 1025 }

  ntxTrapStretchConnectivityIsLost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Stretch connectivity is lost."
     ::= { nutanix 1026 }

  ntxTrapVMRegistrationWarning   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Registration caused warning."
     ::= { nutanix 1027 }

  ntxTrapVMRenamedOnConversion   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Renamed On Conversion."
     ::= { nutanix 1028 }

  ntxTrapAuthenticationFailedInWitness   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Authentication failed in Witness."
     ::= { nutanix 1029 }

  ntxTrapWitnessIsNotConfigured   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Witness is not configured."
     ::= { nutanix 1030 }

  ntxTrapWitnessIsNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Witness is not reachable."
     ::= { nutanix 1031 }

  ntxTrapCuratorJobRunningTooLong   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Curator Job Running Too Long."
     ::= { nutanix 1032 }

  ntxTrapCuratorScanFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Curator Scan Failure."
     ::= { nutanix 1033 }

  ntxTrapFileServerSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Space Usage High."
     ::= { nutanix 1034 }

  ntxTrapFileServerSpaceUsageCritical   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Space Usage Critical."
     ::= { nutanix 1035 }

  ntxTrapFileServerUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server is unreachable."
     ::= { nutanix 1036 }

  ntxTrapFileServerStorageIsNotAvailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server storage is not available."
     ::= { nutanix 1037 }

  ntxTrapFileServerScaleoutFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server scale-out failed."
     ::= { nutanix 1038 }

  ntxTrapFileServerCouldNotJoinTheADDomain   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server could not join the AD Domain."
     ::= { nutanix 1039 }

  ntxTrapNodeFailedToJoinDomain   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The node could not join the domain."
     ::= { nutanix 1040 }

  ntxTrapFileServerTimeDifferenceHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The time drift between the FSVMs is beyond the acceptable range."
     ::= { nutanix 1041 }

  ntxTrapFileServerStorageCleanupFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to clean up storage for the File Server."
     ::= { nutanix 1042 }

  ntxTrapFileServerCannotConnectWithADServer   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server cannot connect with AD server with configured information."
     ::= { nutanix 1043 }

  ntxTrapFileServerPerformanceOptimizationRecommended   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server has a recommendation to optimize performance by using scale-up, scale-out or rebalance."
     ::= { nutanix 1044 }

  ntxTrapAppropriateSiteNotFoundInActiveDirectory   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to determine an appropriate site in Active Directory."
     ::= { nutanix 1045 }

  ntxTrapFileServerDNSUpdatesPending   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "DNS updates are pending after a file server operation."
     ::= { nutanix 1046 }

  ntxTrapUserQuotaAssignmentFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to assign the specified quota to the user."
     ::= { nutanix 1047 }

  ntxTrapShareUtilizationReachedConfiguredLimit   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Share is no longer writeable."
     ::= { nutanix 1048 }

  ntxTrapFileServerFailedToGetUpdatedCVMIPAddress   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server cvm ip update failed."
     ::= { nutanix 1049 }

  ntxTrapFileServerActivationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Activation Failed."
     ::= { nutanix 1050 }

  ntxTrapFailedToSetVMtoVMAntiaffinityRule   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to set VM-to-VM anti affinity rule."
     ::= { nutanix 1051 }

  ntxTrapFileServerHomeShareCreationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to create home share during file server creation."
     ::= { nutanix 1052 }

  ntxTrapDiscoveryOfISCSITargetsFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to discover iSCSI targets on the CVM during the discovery process."
     ::= { nutanix 1053 }

  ntxTrapFileServerUpgradeFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Upgrade Failed."
     ::= { nutanix 1054 }

  ntxTrapIncompatibleFileServerActivated   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Activating an incompatible File Server."
     ::= { nutanix 1055 }

  ntxTrapFileServerInHeterogeneousState   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server in heterogeneous state.Nodes do not match in their CPU or memory configuration. ."
     ::= { nutanix 1056 }

  ntxTrapFailedToCorrectFileServerDataAndMetaDataInconsistencies   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to Run File Server Metadata Fixer tool successfully."
     ::= { nutanix 1057 }

  ntxTrapFileServerShareDeletionFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to delete share."
     ::= { nutanix 1058 }

  ntxTrapFileServerCompatibilityCheckSkipped   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server compatibility check skipped."
     ::= { nutanix 1059 }

  ntxTrapSnapshotInvalidForClone   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Pre-asterix.2 snapshot is invalid for clone."
     ::= { nutanix 1060 }

  ntxTrapFileServerAntiVirusICAPServerDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "ICAP server is not responding to scan requests."
     ::= { nutanix 1061 }

  ntxTrapFileServerAntiVirusAllICAPServersDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "All configured ICAP servers are not responding to scan requests."
     ::= { nutanix 1062 }

  ntxTrapClusterInReadOnlyMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Single-node cluster is in read-only mode."
     ::= { nutanix 1063 }

  ntxTrapStorageContainerSpaceUsageExceededAOSCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check high space usage on Storage Containers."
     ::= { nutanix 1064 }

  ntxTrapDiskDiagnosticFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Drive diagnostic has failed."
     ::= { nutanix 1065 }

  ntxTrapNodeFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Node Failure."
     ::= { nutanix 1066 }

  ntxTrapNodeInMaintenanceMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Node in Maintenance Mode."
     ::= { nutanix 1067 }

  ntxTrapNonSelfEncryptionDriveDiskInserted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Non Self Encrypting Drive disk inserted."
     ::= { nutanix 1068 }

  ntxTrapPhysicalDiskAddedToSlot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Physical Disk added to slot."
     ::= { nutanix 1069 }

  ntxTrapPhysicalDiskDriveHasFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Physical drive has failed."
     ::= { nutanix 1070 }

  ntxTrapPhysicalDiskRemovedFromSlot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Physical disk removed from slot."
     ::= { nutanix 1071 }

  ntxTrapSelfEncryptingDriveOperationFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Self encrypting drive operation failure."
     ::= { nutanix 1072 }

  ntxTrapUnsupportedConfigurationForRedundancyFactor3   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unsupported Configuration For Redundancy Factor 3."
     ::= { nutanix 1073 }

  ntxTrapCannotRemovePasswordProtectedDisks   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cannot remove password protected disk(s)."
     ::= { nutanix 1074 }

  ntxTrapDiskBad   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Disk Bad."
     ::= { nutanix 1075 }

  ntxTrapDuplicateIPAddressDetected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Duplicate IP Address Detected."
     ::= { nutanix 1076 }

  ntxTrapIPAddressNotHosted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "IP Address Not Hosted."
     ::= { nutanix 1077 }

  ntxTrapSMTPError   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to send email."
     ::= { nutanix 1078 }

  ntxTrapProtectionDomainReplicationFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Replication Failure."
     ::= { nutanix 1079 }

  ntxTrapProtectionDomainSnapshotFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Snapshot Failure."
     ::= { nutanix 1080 }

  ntxTrapNutanixGuestToolsAgentIsNotReachableOnTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM not reachable."
     ::= { nutanix 1081 }

  ntxTrapMetroAvailabilityIsPromoted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metro availability is promoted."
     ::= { nutanix 1082 }

  ntxTrapEntityRestoreAborted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity restore aborted."
     ::= { nutanix 1083 }

  ntxTrapProtectionDomainReceiveSnapshotFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Receive Snapshot Failure."
     ::= { nutanix 1084 }

  ntxTrapSecureTunnelToRemoteSiteDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Secure Tunnel To Remote Site Down."
     ::= { nutanix 1085 }

  ntxTrapProtectedVolumeGroupNotFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected Volume Group not Found."
     ::= { nutanix 1086 }

  ntxTrapProtectionDomainActivation   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Activation."
     ::= { nutanix 1087 }

  ntxTrapDuplicateRemoteClusterID   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Duplicate Remote Cluster ID."
     ::= { nutanix 1088 }

  ntxTrapCloudRemoteSiteFailedToStart   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cloud remote site failed to start."
     ::= { nutanix 1089 }

  ntxTrapRemoteSiteremotenameNetworkMappingMissing   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "No Network Mapping Specified."
     ::= { nutanix 1090 }

  ntxTrapOperationForwardedToCloudRemoteFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cloud Remote Operation Failure."
     ::= { nutanix 1091 }

  ntxTrapRecoveryPointObjectiveCannotBeMet   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Point Objective Cannot Be Met."
     ::= { nutanix 1092 }

  ntxTrapVStoreSnapshotStatus   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VStore Snapshot Status."
     ::= { nutanix 1093 }

  ntxTrapProtectionDomainSnapshotOperationSkipped   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Skipped snapshot operation."
     ::= { nutanix 1094 }

  ntxTrapSkippedReplicationOfTheSnapshot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Skipped replication of the snapshot."
     ::= { nutanix 1095 }

  ntxTrapFailedToSnapshotEntities   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to snapshot entities."
     ::= { nutanix 1096 }

  ntxTrapIncorrectClusterInformationInRemoteSite   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cluster Information in Remote Site is not correct."
     ::= { nutanix 1097 }

  ntxTrapVStoreIsBeingReplicatedToBackupOnlyRemoteSite   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "vStore is being replicated to backup only remote site."
     ::= { nutanix 1098 }

  ntxTrapFailedToChangeStateOfOneOrMoreVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to change state of one or more VMs."
     ::= { nutanix 1099 }

  ntxTrapRegistrationOfOneOrMoreVMsFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Registration of one or more VMs failed."
     ::= { nutanix 1100 }

  ntxTrapSelfServiceRestoreOperationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Self service restore operation failed."
     ::= { nutanix 1101 }

  ntxTrapMetadataVolumeSnapshotPersistentFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata Volume Snapshot Persistent Failure."
     ::= { nutanix 1102 }

  ntxTrapMetroAvailabilityIsDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metro availability is disabled."
     ::= { nutanix 1103 }

  ntxTrapApplicationconsistentSnapshotNotTakenForTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Application-consistent snapshot not taken for the VM."
     ::= { nutanix 1104 }

  ntxTrapInvalidConsistencyGroup   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Invalid Consistency Group."
     ::= { nutanix 1105 }

  ntxTrapFailedToReconfigureNutanixGuestToolsForTheRecoveredVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to reconfigure Nutanix Guest Tools for a VM in protection domain."
     ::= { nutanix 1106 }

  ntxTrapNutanixGuestToolsNotInstalled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Guest Tools not installed."
     ::= { nutanix 1107 }

  ntxTrapRemoteSiteremotenameNetworkMappingInvalid   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Invalid Network Mapping Specified."
     ::= { nutanix 1108 }

  ntxTrapVSSSnapshotFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VSS snapshot failed."
     ::= { nutanix 1109 }

  ntxTrapAlertRaisedOnCloudRemoteSiteremotenamealertmessage   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Alert generated on cloud remote site."
     ::= { nutanix 1110 }

  ntxTrapProtectedVmsNotFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected Vms Not Found."
     ::= { nutanix 1111 }

  ntxTrapProtectionDomainContainsMoreThanSpecifiedVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection domain contains multiple VMs."
     ::= { nutanix 1112 }

  ntxTrapRelatedEntityProtectionStatus   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection status of a related entity."
     ::= { nutanix 1113 }

  ntxTrapNutanixGuestToolsIsNotSupportedOnRemoteSite   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Remote site does not support NGT."
     ::= { nutanix 1114 }

  ntxTrapRemoteSiteOperationModeReadOnly   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Operation Mode of Remote Site changed to kReadOnly."
     ::= { nutanix 1115 }

  ntxTrapRemoteSiteIsUnhealthy   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Remote site is unhealthy."
     ::= { nutanix 1116 }

  ntxTrapEntitiesRestoredButUnprotected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entities Restored But Unprotected."
     ::= { nutanix 1117 }

  ntxTrapProtectionDomainFullReplicationPerformed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Full Replication was done."
     ::= { nutanix 1118 }

  ntxTrapProtectedVMIsNotNutanixBackupAndRecoveryCompliant   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VM is not Nutanix backup and recovery compliant."
     ::= { nutanix 1119 }

  ntxTrapProtectedVMRenamedDuringClone   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VM renamed during clone."
     ::= { nutanix 1120 }

  ntxTrapProtectedVolumeGroupsNotFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected Volume Groups Not Found."
     ::= { nutanix 1121 }

  ntxTrapVMRegistrationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Registration Failure."
     ::= { nutanix 1122 }

  ntxTrapProtectionDomainReplicationExpired   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Replication Expired."
     ::= { nutanix 1123 }

  ntxTrapVolumeGroupAttachmentsNotRestored   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Volume Group Attachments Not Restored."
     ::= { nutanix 1124 }

  ntxTrapEntitiesSkippedDuringRestore   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entities Skipped During Restore."
     ::= { nutanix 1125 }

  ntxTrapProtectionDomainChangeModeFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Change Mode Failure."
     ::= { nutanix 1126 }

  ntxTrapVSSSnapshotAborted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VSS snapshot is aborted by the Guest VM."
     ::= { nutanix 1127 }

  ntxTrapExternalISCSIAttachmentsNotSnapshotted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "External iSCSI Attachments Not Snapshotted."
     ::= { nutanix 1128 }

  ntxTrapVMVirtualHardwareVersionNotCompatible   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Virtual Hardware Version not Compatible."
     ::= { nutanix 1129 }

  ntxTrapVolumeGroupActionError   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Volume Group Action Error."
     ::= { nutanix 1130 }

  ntxTrapMetadataVolumeSnapshotTimeout   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata Volume Snapshot Timeout Failure."
     ::= { nutanix 1131 }

  ntxTrapSnapshotPartiallyCrashConsistent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Snapshot partially crash consistent."
     ::= { nutanix 1132 }

  ntxTrapMetroAvailabilityPrechecksFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metro availability prechecks failed."
     ::= { nutanix 1133 }

  ntxTrapProtectionDomainMightHaveSymlinks   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection domain snapshot has symlinks."
     ::= { nutanix 1134 }

  ntxTrapVSSSnapshotIsNotSupportedForTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VSS snapshot is not supported for the VM."
     ::= { nutanix 1135 }

  ntxTrapSnapshotReserveOnSSDIsFull   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "LWS store has become full on the cluster."
     ::= { nutanix 1136 }

  ntxTrapMetroAvailabilityConfigurationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metro availability start failed."
     ::= { nutanix 1137 }

  ntxTrapStaleNFSMount   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Stale NFS mount."
     ::= { nutanix 1138 }

  ntxTrapVSSSoftwareOrprefreezepostthawScriptsNotInstalled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VSS software or pre_freeze/post_thaw Scripts Not Installed."
     ::= { nutanix 1139 }

  ntxTrapProtectedVMNotFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VM not Found."
     ::= { nutanix 1140 }

  ntxTrapProtectionDomainTransitioningToLowerFrequencySnapshotting   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain transitioning to lower frequency snapshot schedule."
     ::= { nutanix 1141 }

  ntxTrapSnapshotQueuedForReplicationsToRemoteSite   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection domain protection_domain_name has one or more snapshots queued for replication to remote site remote_name."
     ::= { nutanix 1142 }

  ntxTrapAgentVMRestorationFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failure to Restore Agent VM."
     ::= { nutanix 1143 }

  ntxTrapCPSDeploymentEvaluationMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the CPS deployment is running in evaluation mode."
     ::= { nutanix 1144 }

  ntxTrapHAHostEvacuationFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failure to evacuate host while entering maintenance mode or reserving host for HA."
     ::= { nutanix 1145 }

  ntxTrapFailureToRestartVMsForHAEvent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failure to restart VMs for HA event."
     ::= { nutanix 1146 }

  ntxTrapUpgradeBundleAvailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Bundle available for upgrade."
     ::= { nutanix 1147 }

  ntxTrapVMActionError   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Action Error."
     ::= { nutanix 1148 }

  ntxTrapHAHealingFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "HA healing failure."
     ::= { nutanix 1149 }

  ntxTrapVmHighAvailabilityFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "High Availability Failure."
     ::= { nutanix 1150 }

  ntxTrapKerberosClockSkewFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Kerberos Clock Skew Failure."
     ::= { nutanix 1151 }

  ntxTrapStargateTemporarilyDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Stargate Temporarily Down."
     ::= { nutanix 1152 }

  ntxTrapVMGroupSnapshotAndCurrentMismatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that snapshot to restore matches current VM group."
     ::= { nutanix 1153 }

  ntxTrapCertificateCreationError   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Certificate signing request creation failure."
     ::= { nutanix 1154 }

  ntxTrapFingerprintingDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Fingerprinting Disabled."
     ::= { nutanix 1156 }

  ntxTrapSystemDefinedFlashModeUsageLimitExceeded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that usage for flash-mode-enabled vDisks is within system limits."
     ::= { nutanix 1157 }

  ntxTrapMetadataUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata Usage High."
     ::= { nutanix 1158 }

  ntxTrapNFSMetadataSizeOvershoot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NFS Metadata Usage High."
     ::= { nutanix 1159 }

  ntxTrapOnDiskDedupDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "On-Disk Dedup Disabled."
     ::= { nutanix 1160 }

  ntxTrapSpaceReservationViolated   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Space Reservation Violated."
     ::= { nutanix 1161 }

  ntxTrapPossibleDegradedNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Services on node possibly not making progress."
     ::= { nutanix 1164 }

  ntxTrapDynamicSchedulingFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "One or more nodes have resource contention. This imbalance can cause performance bottlenecks on the node(s) affected."
     ::= { nutanix 1165 }

  ntxTrapRecoveredVMDiskConfigurationUpdateFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Disk Configuration Update Failed."
     ::= { nutanix 1166 }

  ntxTrapISCSIConfigurationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "iSCSI Configuration Failed."
     ::= { nutanix 1167 }

  ntxTrapA130104   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NGT version of the VM is incompatible with the NGT version of the cluster."
     ::= { nutanix 1168 }

  ntxTrapExecutionOfThePostThawScriptFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Execution of the PostThaw Script Failed."
     ::= { nutanix 1169 }

  ntxTrapNutanixGuestToolsMountFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Guest Tools mount failed."
     ::= { nutanix 1170 }

  ntxTrapVMForciblyPoweredOff   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Acropolis has forcibly powered off a VM that should have been powered off."
     ::= { nutanix 1171 }

  ntxTrapReportGenerationFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Report Generation failed. Database may be down or cluster may have insufficient storage."
     ::= { nutanix 1172 }

  ntxTrapSendReportThroughEmailFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Sending of report through E-mail failed. SMTP might be down or report might have been deleted."
     ::= { nutanix 1173 }

  ntxTrapReportQuotaScanFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Report Quota scan failed. Cassandra might be unreachable."
     ::= { nutanix 1174 }

  ntxTrapCassandraServiceCrashed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if Cassandra service is crashing continuously."
     ::= { nutanix 1175 }

  ntxTrapCassandraServiceIsRunningOutOfMemory   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the Cassandra service is running out of memory."
     ::= { nutanix 1176 }

  ntxTrapMultipleCassandraNodesHaveSimilarTokens   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for similar tokens in Cassandra."
     ::= { nutanix 1177 }

  ntxTrapCloudClusterDoesNotHaveRecommendedConfigurationLocally   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that cloud cluster has recommended configuration locally."
     ::= { nutanix 1178 }

  ntxTrapAWSCloudInstanceNotConfiguredProperly   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify that the AWS cloud instances have recommended configuration."
     ::= { nutanix 1179 }

  ntxTrapOldGenerationAWSInstanceConfigured   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether old generation AWS instance is configured for remote site."
     ::= { nutanix 1180 }

  ntxTrapAOSVersionOfCloudRemoteSiteIsLessThanSourceCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check AOS version for cloud remote site."
     ::= { nutanix 1181 }

  ntxTrapCloudClusterDoesNotHaveAllRecommendedGflagsSet   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if cloud remote sites have recommended gflags set."
     ::= { nutanix 1182 }

  ntxTrapEgroupCountOnCloudDiskIsHigherThanTheRecommendedThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if egroup count on cloud remote is within threshold."
     ::= { nutanix 1183 }

  ntxTrapFileServerMutipleVMsOnSingleNodeCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that multiple File server VMs are running on a single node."
     ::= { nutanix 1184 }

  ntxTrapFileServerServicesDownCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that all File server services are running."
     ::= { nutanix 1185 }

  ntxTrapFileServerUnreachableCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that File server is reachable."
     ::= { nutanix 1186 }

  ntxTrapFileServerDownCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that File server is down."
     ::= { nutanix 1187 }

  ntxTrapFileServerInvalidSnapshot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that File server is in healthy state to take snapshot."
     ::= { nutanix 1188 }

  ntxTrapFileServerEntitiesNotProtected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks all File server entities are protected."
     ::= { nutanix 1189 }

  ntxTrapMultipleFileServerVersionsArePresentInTheCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether multiple File server versions are running in the cluster."
     ::= { nutanix 1190 }

  ntxTrapFileServerUpgradeTaskHungForTooLong   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check File server task is hung for a long time."
     ::= { nutanix 1191 }

  ntxTrapFileServerPDActivatesOnMultipleSites   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that File Server PD Active On Multiple Sites."
     ::= { nutanix 1192 }

  ntxTrapFileServerPDEnabledOnNoncompatibleRemoteSite   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks File server remote sites are file server capable."
     ::= { nutanix 1193 }

  ntxTrapHardwareClockFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if Hardware clock has failed."
     ::= { nutanix 1194 }

  ntxTrapWsmanConnectivityLost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if CVM can connect to the host using WSMan."
     ::= { nutanix 1195 }

  ntxTrapVMMigrationCompromised   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether VMWare EVC configuration can cause VM migration problems."
     ::= { nutanix 1196 }

  ntxTrapCVMMemoryReservationIsIncorrectlyConfigured   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify CVM memory reservation and pinning."
     ::= { nutanix 1197 }

  ntxTrapHostMissingCriticalWindowsUpdates   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if required windows updates are installed."
     ::= { nutanix 1198 }

  ntxTrapHostdServiceNotRunning   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if hostd access is available."
     ::= { nutanix 1200 }

  ntxTrapIncorrectKerberosSetup   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if Kerberos is set up correctly."
     ::= { nutanix 1201 }

  ntxTrapUnableToConnectToVCenter   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if vCenter connection is established."
     ::= { nutanix 1202 }

  ntxTrapVMHasNonASCIIName   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any VMs have non-ASCII names."
     ::= { nutanix 1203 }

  ntxTrapFanSpeedLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that fan speed is not low."
     ::= { nutanix 1204 }

  ntxTrapFanSpeedHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that fan speed is not too high."
     ::= { nutanix 1205 }

  ntxTrapRAMFault   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if current available memory has gone below the installed size."
     ::= { nutanix 1206 }

  ntxTrapPowerSupplyDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that Power supply has no errors."
     ::= { nutanix 1207 }

  ntxTrapCPUTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that cpu temperature is not too high."
     ::= { nutanix 1208 }

  ntxTrapCPUTemperatureReadingError   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CPU temperature can be fetched."
     ::= { nutanix 1209 }

  ntxTrapCPUVoltageAbnormal   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CPU voltage is within normal range."
     ::= { nutanix 1210 }

  ntxTrapCPUVRMTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CPU-VRM temperature is not too high."
     ::= { nutanix 1211 }

  ntxTrapRAMTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that DIMM temperature is not high."
     ::= { nutanix 1212 }

  ntxTrapRAMVoltageAbnormal   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that DIMM voltage is within normal range."
     ::= { nutanix 1213 }

  ntxTrapRAMVRMTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that DIMM-VRM temperature is not high."
     ::= { nutanix 1214 }

  ntxTrapSystemTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that system temperature is not high."
     ::= { nutanix 1215 }

  ntxTrapGPUTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that gpu temperature is not high."
     ::= { nutanix 1216 }

  ntxTrapIPMIError   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether IPMI SDR has failures."
     ::= { nutanix 1217 }

  ntxTrapGPUFault   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that GPU have not faulted."
     ::= { nutanix 1218 }

  ntxTrapHighNumberOfCorrectableECCErrorsInLast1Day   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for number of correctable ECC errors for last one day in the IPMI system event log."
     ::= { nutanix 1219 }

  ntxTrapHighNumberOfCorrectableECCErrorsInLast10Days   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for number of correctable ECC errors for last 10 days in the IPMI system event log."
     ::= { nutanix 1220 }

  ntxTrapLicenseExpiry   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if license is about to expire."
     ::= { nutanix 1221 }

  ntxTrapLicenseFeatureViolation   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check license feature complaince."
     ::= { nutanix 1222 }

  ntxTrapLicenseStandbyMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check license standby mode."
     ::= { nutanix 1223 }

  ntxTrapLicenseNodeInvalid   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if all nodes have valid licenses."
     ::= { nutanix 1224 }

  ntxTrapSecondaryPDsNotInSync   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if secondary metro PD is in sync with primary."
     ::= { nutanix 1225 }

  ntxTrapNoCheckpointSnapshotsOnMetroPDInLastHour   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that Metro PDs have associated checkpoint snapshots."
     ::= { nutanix 1226 }

  ntxTrapCVMTimeDifferenceHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that the time drift between CVMs is less than time_drift_threshold_sec."
     ::= { nutanix 1227 }

  ntxTrapIPMIIPNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all ipmi ips are pingable from local SVM."
     ::= { nutanix 1228 }

  ntxTrapHostIPNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all host ips are pingable from local SVM."
     ::= { nutanix 1229 }

  ntxTrapCVMNICSpeedLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether CVM is uplinked to 10 GbE NIC."
     ::= { nutanix 1230 }

  ntxTrapCVMNotUplinkedToActive10GbpsLink   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CVM uplinked to active 10Gbps link."
     ::= { nutanix 1231 }

  ntxTrapNICErrorRateHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that each NIC has fewer than nic_error_threshold errors during span of execution (3600 seconds)."
     ::= { nutanix 1232 }

  ntxTrapCVMHostSubnetMismatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that host and cvm share the same subnet."
     ::= { nutanix 1233 }

  ntxTrapNICLinkDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether any nic is down."
     ::= { nutanix 1234 }

  ntxTrapCVMIPAddressMismatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that cvm IP address is in sync with zeus configuration."
     ::= { nutanix 1235 }

  ntxTrapZeusConfigMismatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that hypervisor IP address is in sync with zeus configuration."
     ::= { nutanix 1236 }

  ntxTrapIPMIIPAddressMismatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that IPMI IP address is in sync with zeus configuration."
     ::= { nutanix 1237 }

  ntxTrapJumboFramesEnabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check MTU of the CVM network interfaces."
     ::= { nutanix 1238 }

  ntxTrapNICFlaps   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all nics have no flapping."
     ::= { nutanix 1239 }

  ntxTrapIncorrectNTPConfiguration   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that NTP is configured properly on CVM/Hypervisor."
     ::= { nutanix 1240 }

  ntxTrapCVMIsUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all CVMs are reachable via ping."
     ::= { nutanix 1241 }

  ntxTrapNGTInstallationRequired   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Some VMs in protection domain replicating to cross-hypervisor remote site do not have NGT installed."
     ::= { nutanix 1242 }

  ntxTrapTooManyFilesInTheProtectionDomain   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if too many files are being protected by any Metro/Vstore protection domain."
     ::= { nutanix 1243 }

  ntxTrapTooManyFilesInTheConsistencyGroup   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if too many files are being protected by a single consistency group of any Metro/Vstore protection domain."
     ::= { nutanix 1244 }

  ntxTrapFoundOldClonesOnCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for any clones restored from protection domain snapshots that are too old."
     ::= { nutanix 1245 }

  ntxTrapTooManyClonesOnCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if there are too many clones restored from protection domain snapshots."
     ::= { nutanix 1246 }

  ntxTrapProtectingVMsThatAreUsingSharedVHDXDisksIsUnsupported   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if VMs with shared VHDX disks are not part of any protection domain."
     ::= { nutanix 1247 }

  ntxTrapSymlinksFoundOnMetrovstoreProtectedContainer   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for symlinks in metro/vstore protection domain."
     ::= { nutanix 1248 }

  ntxTrapAgedThirdpartyBackupSnapshotsPresent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for aged third-party backup snapshots."
     ::= { nutanix 1249 }

  ntxTrapProtectionDomainContainsMoreThanOneEntity   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if protection domains being replicated to backup site contain more than one entity."
     ::= { nutanix 1250 }

  ntxTrapRemoteSiteConnectivityNotNormal   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if connectivity to remote sites is normal."
     ::= { nutanix 1251 }

  ntxTrapCPUAverageLoadHighOnControllerVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify system load average for the past 5 minutes is below cvm_load_average_threshold."
     ::= { nutanix 1252 }

  ntxTrapCPUAverageLoadCriticallyHighOnControllerVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify system load average for the past 5 minutes is below cvm_load_average_threshold_critical."
     ::= { nutanix 1253 }

  ntxTrapControllerVMCertificateExpiring   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if node certificates are about to expire."
     ::= { nutanix 1254 }

  ntxTrapClusterCertificateExpiring   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if CA certificates are about to expire."
     ::= { nutanix 1255 }

  ntxTrapRemoteSiteInsecure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if Remote site contains encrypted drives if the local site does."
     ::= { nutanix 1256 }

  ntxTrapMixedSelfEncryptingDriveHardware   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cluster with self-encrypting drives has non-self encrypting drives installed."
     ::= { nutanix 1257 }

  ntxTrapKeyManagementServerUnavailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check Key Management Server is available."
     ::= { nutanix 1258 }

  ntxTrapNumberOfOrphanedEgroupsIsOverTheRecommendedThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that the number of orphaned egroups are below the recommended threshold."
     ::= { nutanix 1259 }

  ntxTrapCVMRAMUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CVM memory usage is not high."
     ::= { nutanix 1260 }

  ntxTrapClusterServicesAreDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that no services are down in the cluster."
     ::= { nutanix 1261 }

  ntxTrapKernelMemoryUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether CVM's kernel memory usage is higher than expected."
     ::= { nutanix 1262 }

  ntxTrapCVMServicesRestartingFrequently   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if services have restarted recently in the Controller VM."
     ::= { nutanix 1263 }

  ntxTrapClusterServiceRestartingFrequently   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if services have restarted recently across the cluster."
     ::= { nutanix 1264 }

  ntxTrapCVMConnectivityFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that SVM has passwordless connection to each other."
     ::= { nutanix 1265 }

  ntxTrapStorageContainerReplicationFactorLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that the Storage Container region replication factor is not low."
     ::= { nutanix 1266 }

  ntxTrapCVMRebooted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that Cvm is not rebooted recently."
     ::= { nutanix 1267 }

  ntxTrapRemoteSupportEnabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if remote support tunnel to Nutanix HQ is enabled on this cluster."
     ::= { nutanix 1268 }

  ntxTrapDatastoreVMCountHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks for high VM count on datastores."
     ::= { nutanix 1269 }

  ntxTrapHighVDiskCountInTheCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks for high vDisk count in the cluster."
     ::= { nutanix 1270 }

  ntxTrapAllFlashNodesMixedWithNonallflashNodes   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that all-flash nodes do not coexist with non-all-flash nodes in a cluster."
     ::= { nutanix 1271 }

  ntxTrapHaswellAndBroadwellCPUsAreInTheSameChassis   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether CPUs within a chassis are of the same type."
     ::= { nutanix 1272 }

  ntxTrapTimeSinceLastCuratorScanIsBeyondThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if time since last Curator Scan is beyond threshold."
     ::= { nutanix 1273 }

  ntxTrapSnapshotChainHeightExceedsThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that snapshot chain height is less than 25."
     ::= { nutanix 1274 }

  ntxTrapDIMMsOfDifferentTypesInOneMemoryChannel   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "DIMMs Interoperability Check."
     ::= { nutanix 1275 }

  ntxTrapZookeeperNotActiveOnAllCVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if number of active zookeepers is equal to the total number of zookeeper CVMs."
     ::= { nutanix 1276 }

  ntxTrapM60GPUConfigurationWrongOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check number of M60 GPUs on a node."
     ::= { nutanix 1277 }

  ntxTrapM10GPUConfigurationWrongOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check number of M10 GPUs on a node."
     ::= { nutanix 1278 }

  ntxTrapM10AndM60GPUsInstalledOnTheSameNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if M10 and M60 GPUs installed on the same node."
     ::= { nutanix 1279 }

  ntxTrapPCVCPUAvailabilityCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the number of vCPUs is sufficient for the number of VM entities in Prism Central."
     ::= { nutanix 1280 }

  ntxTrapPCSufficientDiskSpaceCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the amount of storage is sufficient for the number of VM entities in Prism Central."
     ::= { nutanix 1281 }

  ntxTrapPCMemoryAvailabilityCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the amount of memory is sufficient for the number of VM entities in Prism Central."
     ::= { nutanix 1282 }

  ntxTrapPCVMLimitCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the number of VM entities is within the limit."
     ::= { nutanix 1283 }

  ntxTrapStoragePoolSpaceUsageExceeded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check high space usage on storagepools."
     ::= { nutanix 1284 }

  ntxTrapDiskInodeUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that current inode usage is above inode_usage_threshold_pct."
     ::= { nutanix 1285 }

  ntxTrapDiskUnused   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that if some disk is not part of any storage pool."
     ::= { nutanix 1286 }

  ntxTrapFusionIOWearHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether fusion io drive has worn out beyond write limit."
     ::= { nutanix 1287 }

  ntxTrapFusionIOTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether temperature exceeded on fusion-io drive."
     ::= { nutanix 1288 }

  ntxTrapFusionIOReserveLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether fusion io reserves are down."
     ::= { nutanix 1289 }

  ntxTrapFusionIODiskFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether fusion-io drive has failed."
     ::= { nutanix 1290 }

  ntxTrapStorageContainerSpaceUsageExceededNCCCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check high space usage on Storage Containers."
     ::= { nutanix 1291 }

  ntxTrapDataDiskSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that current amount of disk usage is above disk_usage_threshold_pct."
     ::= { nutanix 1292 }

  ntxTrapSystemPartitionsSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that current amount of disk usage is above home_nutanix_usage_threshold_pct."
     ::= { nutanix 1293 }

  ntxTrapStorageDeviceHealthBad   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Smartctl health bad."
     ::= { nutanix 1294 }

  ntxTrapIntelSSDWearHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for wear out on Intel PCIe SSDs."
     ::= { nutanix 1295 }

  ntxTrapIntelSSDTemperatureHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the temperature on Intel PCIe SSDs."
     ::= { nutanix 1296 }

  ntxTrapCVMBootRaidDegraded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the health of boot RAID volumes."
     ::= { nutanix 1297 }

  ntxTrapAbnormalHostBootRAIDState   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the health of boot RAID volumes."
     ::= { nutanix 1298 }

  ntxTrapHypervisorDiskSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that host disk usage is not high."
     ::= { nutanix 1299 }

  ntxTrapInvalidDriveConfiguration   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether combination of SSDs and HDDs is valid."
     ::= { nutanix 1300 }

  ntxTrapSATADOMHasFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that host SATA DOM is functioning."
     ::= { nutanix 1301 }

  ntxTrapSATADOMNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that host SATA DOM is reachable."
     ::= { nutanix 1302 }

  ntxTrapSATADOMHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the wearout of SATA DOM via SMART data."
     ::= { nutanix 1303 }

  ntxTrapSATADOMSL3IE3HasHighWear   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the wearout of SATADOM-SL 3IE3 via SMART data."
     ::= { nutanix 1304 }

  ntxTrapSATADOMNeedsFirmwareVersionUpgradeToS170119   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the firmware version of SATA DOM."
     ::= { nutanix 1305 }

  ntxTrapmodelFirmwareVersionIsNotTheLatestFirmwareVersion   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the firmware version of SATA DOM."
     ::= { nutanix 1306 }

  ntxTrapSATADOMHasGuestVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that no guest VM is installed on SATA DOM."
     ::= { nutanix 1307 }

  ntxTrapSASConnectivityNotNormal   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check SAS connectivity."
     ::= { nutanix 1308 }

  ntxTrapSamsungPM1633DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the status of Samsung PM1633 drive via SMART data."
     ::= { nutanix 1309 }

  ntxTrapToshibaPM3DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the status of Toshiba PM3 drive via SMART data."
     ::= { nutanix 1310 }

  ntxTrapToshibaPM4DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of Toshiba PM4 drive."
     ::= { nutanix 1311 }

  ntxTrapSM863DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of SM863 and SM863a SSD drive."
     ::= { nutanix 1312 }

  ntxTrapMicron5100DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of Micron5100 SSD drive."
     ::= { nutanix 1313 }

  ntxTrapIntelSSDS3610OnipaddressHasConfigurationProblems   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if node has supported configuration for Intel SSD S3610."
     ::= { nutanix 1314 }

  ntxTrapOfflineDiskInACluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for the offline disks."
     ::= { nutanix 1315 }

  ntxTrapNVMeDriveHasErrors   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that host NVMe drive is functioning properly."
     ::= { nutanix 1316 }

  ntxTrapHypervisorBootDriveWearHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check Hyve boot disk status."
     ::= { nutanix 1317 }

  ntxTrapVMIsProtectedInMultiplePDs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VMs are protected in multiple Protection Domains."
     ::= { nutanix 1318 }

  ntxTrapProtectedVMsNotOnNutanixStorage   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VMs have invalid storage configuration."
     ::= { nutanix 1319 }

  ntxTrapClusterConnectivityStatus   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Tests whether the cluster connectivity is fine."
     ::= { nutanix 1320 }

  ntxTrapHighGarbageDueToErasureCoding   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if erasure coding garbage is below a safe threshold."
     ::= { nutanix 1321 }

  ntxTrapA1175   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks erasure coding is enabled on cluster with version < 4.5.2."
     ::= { nutanix 1322 }

  ntxTrapInvalidErasureCodeDelayParameter   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Validate the EC param value for erasure-code-delay."
     ::= { nutanix 1323 }

  ntxTrapFlashModeUsageLimitExceeded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the amount of usage by flash-mode-enabled vDisks with respect to the threshold limit."
     ::= { nutanix 1325 }

  ntxTrapFlashmodeenabledVMPowerStatus   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any flash-mode-enabled VMs are Powered Off."
     ::= { nutanix 1326 }

  ntxTrapStoragePoolFlashModeConfiguration   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Flash Mode is not supported when multiple storage pools are in use."
     ::= { nutanix 1327 }

  ntxTrapTestNotificationTitle   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Test Notification."
     ::= { nutanix 1328 }

  ntxTrapIncompatibleFileServer   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server version incompatible with AOS."
     ::= { nutanix 1329 }

  ntxTrapA1202   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the compatibility of Samsung PM1633 drives with NOS and Foundation versions."
     ::= { nutanix 1330 }

  ntxTrapFirmwareVersionOfSamsungPM1633DrivesIsOld   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the firmware of Samsung PM1633 drives."
     ::= { nutanix 1331 }

  ntxTrapMoreThanOneTypeOfToshibaPM4DrivesInstalledOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the Configuration of Toshiba PM4 drives."
     ::= { nutanix 1332 }

  ntxTrapA1200   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the compatibility of Toshiba PM4 drives with NOS and Foundation versions."
     ::= { nutanix 1333 }

  ntxTrapFirmwareVersionOfToshibaPM4DrivesIsOld   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the firmware of Toshiba PM4 drives."
     ::= { nutanix 1334 }

  ntxTrapFirmwareVersionOfSM863DrivesIsOld   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the firmware of SM863 or SM863a drives."
     ::= { nutanix 1335 }

  ntxTrapFewerThanTwoNonSamsungPM863aDrivesInstalledOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the types of disk drives on a node that has PM863a drives installed."
     ::= { nutanix 1336 }

  ntxTrapFirmwareVersionOfPM863aDrivesIsOld   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check firmware of PM863a drives."
     ::= { nutanix 1337 }

  ntxTrapPM863aDriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of PM863a SSD."
     ::= { nutanix 1338 }

  ntxTrapOfflineDiskInCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for offline disks."
     ::= { nutanix 1339 }

  ntxTrapMetadataDisksNotMountedOnCVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all metadata disks are mounted."
     ::= { nutanix 1340 }

  ntxTrapFileServerUpgradeTaskIsNotProgressing   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that file server task is progressing."
     ::= { nutanix 1341 }

  ntxTrapA130129   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VMs in the standby site of a Metro Availability protection domain are running at suboptimal performance."
     ::= { nutanix 1342 }

  ntxTrapA130118   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Error in updating failure handling on the remote Metro Availability protection domain."
     ::= { nutanix 1343 }

  ntxTrapFileServerCloneFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Clone Failed."
     ::= { nutanix 1344 }

  ntxTrapFileServerRenameFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Rename Failed."
     ::= { nutanix 1345 }

  ntxTrapA130097   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to mount ISO image as part of Nutanix Guest Tools reconfiguration for a VM in protection domain."
     ::= { nutanix 1346 }

  ntxTrapA130095   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to enable Nutanix Guest Tools during VMs recovery for protection domain."
     ::= { nutanix 1347 }

  ntxTrapA130131   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Snapshot contains entities from the storage container that have deduplication enabled."
     ::= { nutanix 1348 }

  ntxTrapA130137   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Replication of protection domain has not progressed."
     ::= { nutanix 1349 }

  ntxTrapA106030   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the compatibility of SM863a drives with NOS and Foundation versions."
     ::= { nutanix 1350 }

  ntxTrapA106033   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the compatibility of PM863a drives with AOS and Foundation versions."
     ::= { nutanix 1351 }

  ntxTrapA111047   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if Mellanox NICs are down or if any Mellanox NIC has speed other than 10GbE or 40GbE. Checks if both 10GbE and 40GbE Mellanox NICs are installed on one node."
     ::= { nutanix 1352 }

  ntxTrapA110219   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unsupported number of VMs in Metro/Vstore Protection Domain."
     ::= { nutanix 1353 }

  ntxTrapA110251   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Replications are scheduled on entitites from the storage containers that have deduplication enabled."
     ::= { nutanix 1354 }

  ntxTrapA111044   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that cluster virtual IP address is part of cluster external subnet."
     ::= { nutanix 1355 }

  ntxTrapMaximumConnectionsLimitReachedOnAFileServerVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Maximum connections limit reached on a file server VM."
     ::= { nutanix 1356 }

  ntxTrapFailedToAddOneOrMoreFileServerAdministratorUsersOrGroups   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to add one or more users or groups as file server administrators."
     ::= { nutanix 1357 }

  ntxTrapUserDefinedAlert   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "This trap corresponds to any of user defined alerts."
     ::= { nutanix 1358 }

  ntxTrapFileServerNetworkChangeFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Network Change Failed."
     ::= { nutanix 1359 }

  ntxTrapSnapshotCreationDelayed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection domain protection_domain_name has creation of one or more snapshots delayed."
     ::= { nutanix 1360 }

  ntxTrapA130146   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain protection_domain_name will maintain rpo_string RPO until a baseline is established to remote_site remote_name."
     ::= { nutanix 1361 }

  ntxTrapA130143   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain fallback to lower frequency replications to remote."
     ::= { nutanix 1362 }

  ntxTrapA130144   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain transitioning to higher frequency snapshot schedule."
     ::= { nutanix 1363 }

  ntxTrapFoundationVersionsInconsistent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that the Foundation version are correct and consistent."
     ::= { nutanix 1364 }

  ntxTrapMetadataDiskUsageIsHigherThanTheSpecifiedLimit   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if metadata disk usage on cloud Controller VM is within the specified limit."
     ::= { nutanix 1365 }

  ntxTrapVolumeGroupSpaceUsageExceeded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check high space usage on volume groups."
     ::= { nutanix 1366 }

  ntxTrapVSSContainersHaveHighFileCount   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check containers file count within limit."
     ::= { nutanix 1367 }

  ntxTrapCVMIpAddressIsUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all CVMs are reachable via ping."
     ::= { nutanix 1368 }

  ntxTrapIncorrectClusterInformationInTheRemoteSite   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the cluster information in remote site is correct."
     ::= { nutanix 1369 }

  ntxTrapProtectionDomainActivationMayFailAsConflictingFilesExist   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any existing file turns into a conflicting state after activating an inactive Protection Domain."
     ::= { nutanix 1370 }

  ntxTrapVNUMAVMPinningFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cluster was unable to provide requested vNUMA pinning for VM."
     ::= { nutanix 1371 }

  ntxTrapGuestPowerOperationThroughNGTFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Guest Power Operation Failed."
     ::= { nutanix 1372 }

  ntxTrapMellanoxNICNotInstalledOrWithWrongTypeOnHostMachine   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if Mellanox NICs belong to the ConnectX-3 Pro family."
     ::= { nutanix 1373 }

  ntxTrapNonComplianceWithHostAffinityPolicies   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM not in compliance with the defined affinity policies."
     ::= { nutanix 1374 }

  ntxTrapPolicyNotApplicableToAnyHost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Policy as defined does not apply to any of the Hosts."
     ::= { nutanix 1375 }

  ntxTrapTheClusterIsNotSynchronizingTimeWithAnyExternalServers   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks to ensure that the Controller VM is synchronizing time with an NTP server."
     ::= { nutanix 1376 }

  ntxTrapTheHypervisorIsNotSynchronizingTimeWithAnyExternalServers   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks to ensure that the hypervisor is synchronizing time with an NTP server."
     ::= { nutanix 1377 }

  ntxTrapProtectionDomainActivationOrMigrationFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Activation or Migration Failure."
     ::= { nutanix 1378 }

  ntxTrapProtectionDomainContainsMoreThanTheSpecifiedVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection domain contains multiple VMs."
     ::= { nutanix 1379 }

  ntxTrapSATADOMML3SEHasHighWear   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the wearout of SATADOM-ML 3SE via SMART data."
     ::= { nutanix 1380 }

  ntxTrapFileServerAntiVirusScanQueueFullOnFSVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Antivirus Scan Queue is Full on FSVM."
     ::= { nutanix 1381 }

  ntxTrapFileServerAntiVirusScanQueuePilingUpOnFSVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Antivirus Scan Queue is Piling Up on FSVM."
     ::= { nutanix 1382 }

  ntxTrapFileServerAntiVirusExcessiveQuarantinedUnquarantinedFiles   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Excessive Quarantined / Unquarantined Files on File Server."
     ::= { nutanix 1383 }

  ntxTrapA160048   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Quarantined / Unquarantined Files Limit is Reached on File Server."
     ::= { nutanix 1384 }

  ntxTrapFailedToTakeTheApplicationconsistentSnapshotForTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to take the application-consistent snapshot for the VM."
     ::= { nutanix 1385 }

  ntxTrapRemovalOfTheTemporaryHypervisorSnapshotFailedForTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Removal of the temporary hypervisor snapshot that got created while taking the host-based application-consistent snapshot has failed for the VM."
     ::= { nutanix 1386 }

  ntxTrapCloudDiskUsageIsNearingTheThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the cloud disk usage is within the threshold limit."
     ::= { nutanix 1387 }

  ntxTrapDIMMsHaveInvalidPartNumber   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "DIMMs Part Number Check."
     ::= { nutanix 1388 }

  ntxTrapAzureCloudControllerVMHasSmallerDisks   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify that the Azure cloud Controller VM has recommended configuration."
     ::= { nutanix 1389 }

  ntxTrapFirmwareVersionOfSM863OrSM863aDrivesIsOld   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the firmware of SM863 or SM863a drives."
     ::= { nutanix 1390 }

  ntxTrapM2Micron5100HostBootDriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of M.2 Micron5100 host boot drive."
     ::= { nutanix 1391 }

  ntxTrapM2IntelS3520HostBootDriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of M.2 Intel S3520 host boot drive."
     ::= { nutanix 1392 }

  ntxTrapClusterInOverrideMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Single-node cluster is in override mode."
     ::= { nutanix 1393 }

  ntxTrapMultipleVcentersDiscovered   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Multiple vCenter servers discovered."
     ::= { nutanix 1394 }

  ntxTrapProtectionRuleTestAlertTitle   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Test Alert ProtectionRule."
     ::= { nutanix 1395 }

  ntxTrapExternalClientAuthentication   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if any external client is authenticated to the REST API with the admin user."
     ::= { nutanix 1396 }

  ntxTrapTwoNodeClusterStateChangeToclusteroperationmode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Change in state of two node cluster."
     ::= { nutanix 1397 }

  ntxTrapWitnessIsUnreachableFromNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Witness is unreachable, the node may not be able to handle failure."
     ::= { nutanix 1398 }

  ntxTrapTwoNodeClusterChangedStateToStandaloneMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "One of the nodes in a two node cluster is currently unavailable, the cluster is now operating in stand-alone mode."
     ::= { nutanix 1399 }

  ntxTrapTwoNodeClusterStateChangeTostate   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Change in state of two node cluster."
     ::= { nutanix 1400 }

  ntxTrapTwoNodeClusterStateChangeToStandaloneMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Change in state of two node cluster."
     ::= { nutanix 1401 }

  ntxTrapRecoveryPlanExecutionFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery plan execution on recovery_plan_name failed."
     ::= { nutanix 1402 }

  ntxTrapXiPaymentMissed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to process payment. If this issue persists, you may experience an interruption in service."
     ::= { nutanix 1403 }

  ntxTrapFreeXiAccountExpired   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Free period using Xi expired. If this issue persists, you may experience an interruption in service."
     ::= { nutanix 1404 }

  ntxTrapXiSubscriptionExpired   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Xi subscription period expired. If this issue persists, you may experience an interruption in service."
     ::= { nutanix 1405 }

  ntxTrapEntitySyncFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Sync Failed."
     ::= { nutanix 1406 }

  ntxTrapNucalmInternalServiceHasStoppedWorking   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nucalm internal service is down."
     ::= { nutanix 1407 }

  ntxTrapEpsilonInternalServiceHasStoppedWorking   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Epsilon internal service is down."
     ::= { nutanix 1408 }

  ntxTrapProtectionRuleConflictOccurred   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to protect VM due to conflicting protection rules."
     ::= { nutanix 1409 }

  ntxTrapDomainFaultToleranceIsReducedForMetadata   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Domain fault tolerance is low for metadata."
     ::= { nutanix 1410 }

  ntxTrapVMProtectionFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM protection failed."
     ::= { nutanix 1411 }

  ntxTrapVMRecoveryPointReplicationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM recovery point replication failed."
     ::= { nutanix 1412 }

  ntxTrapVMRecoveryPointCreationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM recovery point creation failed."
     ::= { nutanix 1413 }

  ntxTrapMicrosegmentationControlPlaneFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Microsegmentation failure event."
     ::= { nutanix 1414 }

  ntxTrapMicrosegmentationRuleFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Programming a microsegmentation rule failed."
     ::= { nutanix 1415 }

  ntxTrapDriveRemovalStuck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Drive Removal Stuck."
     ::= { nutanix 1416 }

  ntxTrapFileServerNTPServersConnectivityFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server cannot connect to NTP server."
     ::= { nutanix 1417 }

  ntxTrapFileServerTimeIsOutOfSyncWithTheActiveDirectory   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server time is out of sync with the Active Directory domain controllers."
     ::= { nutanix 1418 }

  ntxTrapFileServerDNSResolverIPConnectivityFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server cannot resolve its domain name using configured DNS resolver IP addresses."
     ::= { nutanix 1419 }

  ntxTrapFileServerUserManagementConfigurationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Given user management options could not be configured for the file server."
     ::= { nutanix 1420 }

  ntxTraphomePartitionUsageOnAFileServerVMHigherThanThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Space consumption under /home partition on file server VM is higher than the set threshold."
     ::= { nutanix 1421 }

  ntxTrapFileServerDNSRecordsCannotBeRefreshed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server DNS records periodic refresh failed."
     ::= { nutanix 1422 }

  ntxTrapFileServerShareBackupDiffPathTranslationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Backup diff found inodes whose path translation failed."
     ::= { nutanix 1423 }

  ntxTrapFileServerPartnerServerConnectivityDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Partner server is not responding to file notifications."
     ::= { nutanix 1424 }

  ntxTrapFileServerPDActionToIncompatibleRemoteSiteAOS   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Remote Site AOS version is not compatible with File Server version."
     ::= { nutanix 1425 }

  ntxTrapFileServerServicesGotInterrupted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "One of the fileserver services crashed."
     ::= { nutanix 1426 }

  ntxTrapCommonPortGroupBetweenESXiHostsIsAbsent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Common port group between ESXi hosts is not present."
     ::= { nutanix 1427 }

  ntxTrapFailedToReceiveSnapshotForTheProtectionDomain   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Receive Snapshot Failure."
     ::= { nutanix 1428 }

  ntxTrapHostNetworkUplinkConfigurationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to apply host uplink configuration on the host(s)."
     ::= { nutanix 1429 }

  ntxTrapRestartVMsBeforePerformingUpgradeOrMigrateOperation   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Restart all the VMs before you perform the upgrade or migrate operation."
     ::= { nutanix 1430 }

  ntxTrapOplogEpisodeCountCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that oplog episode count is within threshold."
     ::= { nutanix 1431 }

  ntxTrapCerebroStatsCollectorFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Collect Cerebro stats and publish to CFS."
     ::= { nutanix 1432 }

  ntxTrapLatencyBetweenCVMsIsHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether latency among CVMs is high."
     ::= { nutanix 1433 }

  ntxTrapLicenseInvalid   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for license capacity."
     ::= { nutanix 1434 }

  ntxTrapRemoteSiteAOSNotCompatibleWithFileServerVersion   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks File server remote sites AOS are file server version compatible."
     ::= { nutanix 1435 }

  ntxTrapA106043   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the compatibility of Intel S4600 drives with AOS and Foundation versions."
     ::= { nutanix 1436 }

  ntxTrapFirmwareVersionOfIntelS4600DrivesIsOld   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check firmware of Intel S4600 drives."
     ::= { nutanix 1437 }

  ntxTrapIntelS4600DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of Intel S4600 SSD."
     ::= { nutanix 1438 }

  ntxTrapHostBootDiskSerialNumberHasChanged   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the change of host boot disk."
     ::= { nutanix 1439 }

  ntxTrapSataControllerStatusIsBad   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check SATA controller ports."
     ::= { nutanix 1440 }

  ntxTrapSamsung863Or863aOnipaddressHasConfigurationProblems   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if node has supported configuration for Samsung 863 or 863a."
     ::= { nutanix 1441 }

  ntxTrapHypervisorBootDriveRAIDIsInAnUnhealthyState   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the status of Dell Marvell BOSS hypervisor boot drive."
     ::= { nutanix 1442 }

  ntxTrapCVMPortGroupRenamed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CVM port group name has not changed."
     ::= { nutanix 1443 }

  ntxTrapA106453   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any orphan VSS copies are present."
     ::= { nutanix 1444 }

  ntxTrapActiveDirectoryDCsAndorDNSServersRunningOnCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if all active directory domain controllers and DNS servers are running on Nutanix cluster."
     ::= { nutanix 1445 }

  ntxTrappowersourceDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that Power supply has no errors."
     ::= { nutanix 1446 }

  ntxTrapCPUTemperatureLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that cpu temperature is not too low."
     ::= { nutanix 1447 }

  ntxTrapRAMTemperatureLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that DIMM temperature is not low."
     ::= { nutanix 1448 }

  ntxTrapSystemTemperatureLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that system temperature is not low."
     ::= { nutanix 1449 }

  ntxTrapIPMISELLogFetchFail   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check fetching IPMI SEL logs."
     ::= { nutanix 1450 }

  ntxTrapIPMISELLogPowerFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for IPMI SEL Power failure in the past 24 hours."
     ::= { nutanix 1451 }

  ntxTrapAggressiveBreakReplicationTimeout   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if break replication timeout of metro protection domain is more than the recommended limit."
     ::= { nutanix 1452 }

  ntxTrapCVMOrPCVMRAMUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CVM or PC VM  memory usage is not high."
     ::= { nutanix 1453 }

  ntxTrapCVMOrPCVMCPULoadHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CVM or PC VM CPU load is not high."
     ::= { nutanix 1454 }

  ntxTrapCVMRenamedIncorrectly   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CVM has been named properly."
     ::= { nutanix 1455 }

  ntxTrapPCVMDiskUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that disk space usage on the Prism Central VM is within limits."
     ::= { nutanix 1457 }

  ntxTrapvmtypeVirtualIPCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if virtual ip is configured and reachable."
     ::= { nutanix 1458 }

  ntxTrapvmtypeSameTimezoneCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all CVMs are in the same timezone."
     ::= { nutanix 1459 }

  ntxTrapDIMMConfigurationIsWrong   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "DIMM Configuration Check."
     ::= { nutanix 1460 }

  ntxTrapP40GPUConfigurationWrongOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check number of P40 GPUs on a node."
     ::= { nutanix 1461 }

  ntxTrapP40GPUBMCVersionIsOldOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check BMC version of P40 GPUs on a node."
     ::= { nutanix 1462 }

  ntxTrapMemoryConfigurationInconsistent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check all Controller VM or Prism Central VM memory is at the same level."
     ::= { nutanix 1463 }

  ntxTrapEntityCountExceededTheMaximumLimit   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Count Exceeded the Maximum Limit."
     ::= { nutanix 1464 }

  ntxTrapAOSUpgradesAreDisabledOncvmip   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if AOS upgrades are disabled on CVM."
     ::= { nutanix 1465 }

  ntxTrapFirmwareUpgradesAreDisabledOncvmip   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if firmware upgrades are disabled on the CVM."
     ::= { nutanix 1466 }

  ntxTrapHypervisorDiskdevnameSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that host disk usage is not high."
     ::= { nutanix 1467 }

  ntxTrapCVMPasswordlessSSHToHost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check passwordless SSH into local hypervisor."
     ::= { nutanix 1468 }

  ntxTrapCPUsOfDifferentTypesOrModelsAreInTheSameChassis   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether CPUs within a chassis are of the same type."
     ::= { nutanix 1469 }

  ntxTrapRecoveryPointReplicationSkipped   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Point Replication Skipped."
     ::= { nutanix 1470 }

  ntxTrapNetworkCreationFailureForRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Network network_cidr creation failed for the Recovery Plan recovery_plan_name."
     ::= { nutanix 1471 }

  ntxTrapVirtualIPAddressNotConfiguredOnTheRemoteCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Virtual IP address is not configured on the remote cluster."
     ::= { nutanix 1472 }

  ntxTrapEntityCountExceedDiscovered   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity count exceed discovered."
     ::= { nutanix 1473 }

  ntxTrapVMReplicationFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Replication Failure."
     ::= { nutanix 1474 }

  ntxTrapVMReplicationExpired   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Replication Expired."
     ::= { nutanix 1475 }

  ntxTrapApplicationConsistentRecoveryPointFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Application consistent Recovery Point failed."
     ::= { nutanix 1476 }

  ntxTrapReplicationTimeExceededTheRPOLimit   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Replication Time Exceeded the RPO Limit."
     ::= { nutanix 1477 }

  ntxTrapNGTOnVMvmnameWasNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NGT on VM vm_name was not reachable."
     ::= { nutanix 1478 }

  ntxTrapVMReplicationHasNotProgressed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Replication of the VM has not progressed."
     ::= { nutanix 1479 }

  ntxTrapVSSProviderOrprefreezepostthawScriptsNotInstalled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VSS provider or pre_freeze/post_thaw Scripts Not Installed."
     ::= { nutanix 1480 }

  ntxTrapPartialRecoveryPoint   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Partial Recovery Point."
     ::= { nutanix 1481 }

  ntxTrapPulseCannotConnectToRESTServerEndpoint   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if Pulse can connect to REST server endpoint."
     ::= { nutanix 1482 }

  ntxTrapJumboFramesEnabledForNICnicnameOnservicevmexternalip   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check MTU of the CVM network interfaces."
     ::= { nutanix 1483 }

  ntxTrapUnableToRetrieveTheAvailabilityZoneEndpoint   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to retrieve the endpoint information for the Availability Zone."
     ::= { nutanix 1484 }

  ntxTrapRecoveryPointReplicationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Point replication failed."
     ::= { nutanix 1485 }

  ntxTrapUnableToCommunicateWithTheDataCenterManager   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to retrieve the endpoint information for the Availability Zone from the Data Center Manager."
     ::= { nutanix 1486 }

  ntxTrapRemoteSiteInSameVCenterDatacenter   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any remote site is added under the same datacenter in vCenter."
     ::= { nutanix 1487 }

  ntxTrapV100GPUConfigurationWrongOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check number of V100 GPUs on a node."
     ::= { nutanix 1488 }

  ntxTrapApplicationsArchiveReadyForDownload   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Archive ready for download."
     ::= { nutanix 1489 }

  ntxTrapMoreThanOneTypeOfGPUsInstalledOnTheSameNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if more than one type of GPUs installed on the same node."
     ::= { nutanix 1490 }

  ntxTrapVmRegisteredWithoutNetwork   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to attach vNIC for recovered VM: vm_name."
     ::= { nutanix 1491 }

  ntxTrapAlertEmailFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to send alert emails."
     ::= { nutanix 1492 }

  ntxTrapNICRXCRCErrorRateHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether the NICs on hypervisor are experiencing rx_crc errors."
     ::= { nutanix 1493 }

  ntxTrapNICRXMissedErrorRateHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether the NICs on hypervisor are experiencing rx_missed errors."
     ::= { nutanix 1494 }

  ntxTrapEntityUnprotectionFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to unprotect VM due to some Internal Error."
     ::= { nutanix 1495 }

  ntxTrapAvailabilityZoneValidationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Availability Zone is not Valid."
     ::= { nutanix 1496 }

  ntxTrapInvalidNetworkMappingForRecoveryPlanrecoveryplanname   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Network mapping have Availability Zones that are not in accordance with Availability Zone order list of Protection Policy applied to VMs in Recovery Plan."
     ::= { nutanix 1497 }

  ntxTrapDataProtectionTasksAreNotProgressing   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cerebro Tasks got stuck."
     ::= { nutanix 1498 }

  ntxTrapIncorrectClusterInformationInTheRemoteSiteremotename   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the cluster information in remote site is correct."
     ::= { nutanix 1499 }

  ntxTrapVCenterNotRegistered   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if vCenter server is registered."
     ::= { nutanix 1500 }

  ntxTrapNGTUpdateAvailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether VMs have latest NGT version installed."
     ::= { nutanix 1501 }

  ntxTrapDuplicateCVMIPAddressDetected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for duplicate CVM IP address."
     ::= { nutanix 1502 }

  ntxTrapMTUConfigurationAcrossControllerVMsIsNotConsistent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check connectivity between Controller VMs by pinging with configured MTUs."
     ::= { nutanix 1503 }

  ntxTrapMultipleCpuunblockProcessesRunning   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that there are no stale cpu_unblock processes running."
     ::= { nutanix 1504 }

  ntxTrapRecoveryLocationOperationChangedToReadOnlyMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Operation Mode of the Recovery Location changed to Read Only."
     ::= { nutanix 1505 }

  ntxTrapA130181   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Snapshot contains entities from the storage container that have deduplication enabled."
     ::= { nutanix 1506 }

  ntxTrapAvailabilityZoneConnectionFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The remote availability zone az_url is unreachable."
     ::= { nutanix 1507 }

  ntxTrapPEPCConnectionFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The remote remote_vm_type cluster_ip is unreachable."
     ::= { nutanix 1508 }

  ntxTrapNutanixGuestToolsNotUpgraded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Guest Tools not upgraded."
     ::= { nutanix 1509 }

  ntxTrapFileServerMultipleVMsOnSingleNodeCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that multiple File server VMs are running on a single node."
     ::= { nutanix 1510 }

  ntxTrapShareUsageReachingToConfiguredLimit   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Share will be no longer writeable."
     ::= { nutanix 1511 }

  ntxTrapProtectedVMsNotRecoverable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VM(s) Not Recoverable."
     ::= { nutanix 1512 }

  ntxTrapEntitySyncFailureForProtectionRule   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Sync failed for ProtectionRule."
     ::= { nutanix 1513 }

  ntxTrapEntitySyncFailureForRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Sync failed for RecoveryPlan."
     ::= { nutanix 1514 }

  ntxTrapDataAtRestEncryptionKeyBackupWarning   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "New encryption keys not backed up."
     ::= { nutanix 1515 }

  ntxTrapLocalKeyManagerMasterKeyRotationWarning   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to rotate local key manager's master key."
     ::= { nutanix 1516 }

  ntxTrapPulseCannotConnectToRESTServerEndpointOnFileServer   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if Pulse can connect to REST server endpoint on File Server."
     ::= { nutanix 1517 }

  ntxTrapDetectedIncompatibleAHVVersion   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if hosts are installed with a compatible AHV version."
     ::= { nutanix 1518 }

  ntxTrapNucalmLicenseIsOvershooting   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nucalm license is Overshooting."
     ::= { nutanix 1519 }

  ntxTrapA300409   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan recovery_plan_name validation failed with warnings."
     ::= { nutanix 1520 }

  ntxTrapValidationFailedForRecoveryPlanrecoveryplanname   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan recovery_plan_name validation failed with errors."
     ::= { nutanix 1521 }

  ntxTrapoperationtypeFailedForRecoveryPlanrecoveryplanname   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan recovery_plan_name execution failed."
     ::= { nutanix 1522 }

  ntxTrapA300402   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan recovery_plan_name execution failed."
     ::= { nutanix 1523 }

  ntxTrapEntitySyncFailureForAvailabilityZone   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Sync failed for Availability Zone."
     ::= { nutanix 1524 }

  ntxTrapRemoteAvailabilityZoneLatencyIsHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Latency to the remote Availability Zone is high."
     ::= { nutanix 1525 }

  ntxTrapTheFrequencyOfCPUcpuidOnHosthostipIsExtremelyLow   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the frequencies of CPUs on a node."
     ::= { nutanix 1526 }

  ntxTrapCalmLicenseExpiry   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if Calm license is about to expire."
     ::= { nutanix 1527 }

  ntxTrapPCVMCPULoadHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that PC VM CPU load is not high."
     ::= { nutanix 1528 }

  ntxTrapNodeMarkedToBeAutoAddedToMetadataRing   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Node Marked To Be Auto Added To Metadata Ring."
     ::= { nutanix 1529 }

  ntxTrapAutomaticAdditionOfNodeToMetadataRingDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Automatic Addition Of Node To Metadata Ring Disabled."
     ::= { nutanix 1530 }

  ntxTrapNodeMarkedToBeDetachedFromMetadataRing   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Node Marked To Be Detached From Metadata Ring."
     ::= { nutanix 1531 }

  ntxTrapNodeForwardingMetadataRequests   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Node Forwarding Metadata Requests."
     ::= { nutanix 1532 }

  ntxTrapRecoveryPlansHaveConflictingNetworkMappings   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks network mapping conflicts in multiple Recovery Plans."
     ::= { nutanix 1533 }

  ntxTrapVMRecoveryMayFail   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Primary and Remote Locations are on different hypervisors."
     ::= { nutanix 1534 }

  ntxTrapNutanixCalmLicenseViolation   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Calm License Violation."
     ::= { nutanix 1535 }

  ntxTrapTranslatedAddressesRetrievalFailureInNGT   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NGT is unable to retrieve the XAT address translations."
     ::= { nutanix 1536 }

  ntxTrapStoragePoolUsageReachingItsLimit   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Shares on storage pool will be no longer writeable."
     ::= { nutanix 1537 }

  ntxTrapTargetCouldNotBeFoundForReplication   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Replication target could not be found."
     ::= { nutanix 1538 }

  ntxTrapClusterJoinToDomainFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to join AOS cluster to domain."
     ::= { nutanix 1539 }

  ntxTrapUnplannedFailoverAndFailbackCanCauseFullReplication   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unplanned failover and failback can cause full replication."
     ::= { nutanix 1540 }

  ntxTrapEntitySyncFailureForTheProtectionPolicy   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Sync failed for Protection Policy."
     ::= { nutanix 1541 }

  ntxTrapEntitySyncFailureForTheRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Sync failed for Recovery Plan."
     ::= { nutanix 1542 }

  ntxTrapEntitySyncFailureForTheAvailabilityZone   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity Sync failed for Availability Zone."
     ::= { nutanix 1543 }

  ntxTrapDeleteTheFailedOverVMsOnThePrimaryLocation   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Delete the failed over VMs on the Primary Location."
     ::= { nutanix 1544 }

  ntxTrapVMvmnameMemoryOverprovisioned   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "vm memory overprovisioned alert."
     ::= { nutanix 1545 }

  ntxTrapVMvmnameMemoryConstrained   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "vm memory constrained alert."
     ::= { nutanix 1546 }

  ntxTrapVMvmnameCPUOverprovisioned   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "vm cpu overprovisioned alert."
     ::= { nutanix 1547 }

  ntxTrapVMvmnameCPUConstrained   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "vm cpu constrained alert."
     ::= { nutanix 1548 }

  ntxTrapVMvmnameInactive   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "inactive vm alert."
     ::= { nutanix 1549 }

  ntxTrapVMBullyvmname   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "bully vm alert."
     ::= { nutanix 1550 }

  ntxTrapNutanixGuestToolsFailedToInitiateVMReboot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Guest Tools failed to initiate reboot on the VM."
     ::= { nutanix 1551 }

  ntxTrapMultipleRecoveryPlansHaveCategorycategory   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if same category belongs to multiple Recovery Plans."
     ::= { nutanix 1552 }

  ntxTrapFloatingIPfloatingipIsAssociatedWithMultipleVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if VMs which are part of different Recovery Plan have same Floating IPs."
     ::= { nutanix 1553 }

  ntxTrapRemoteReplicationIsLaggingForProtectionDomainSnapshot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Remote replication of the protection domain snapshot is lagging."
     ::= { nutanix 1554 }

  ntxTrapSameVMsPresentInMultipleStagesOfTheRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether a VM is part of more than one stage in Recovery Plan."
     ::= { nutanix 1555 }

  ntxTrapZookeeperAliasIsIncorrectlyConfiguredInTheCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check Zookeeper alias information configuration."
     ::= { nutanix 1556 }

  ntxTrapValidationFailedForTheRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan validation failed with warnings."
     ::= { nutanix 1557 }

  ntxTrapNetworkCreationFailureForTheRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Network creation failed for the Recovery Plan."
     ::= { nutanix 1558 }

  ntxTrapValidationFailedForTheRecoveryPlanrecoveryplanname   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Execution of the Recovery Plan failed."
     ::= { nutanix 1559 }

  ntxTrapVirtualIPAddressNotConfiguredOnTheCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Virtual IP address is not configured on the cluster."
     ::= { nutanix 1560 }

  ntxTrapNGTOnVMvmnameIsNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NGT on VM was not reachable."
     ::= { nutanix 1561 }

  ntxTrapCVMNICLinkDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether any CVM NIC is down."
     ::= { nutanix 1562 }

  ntxTrapA300417   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if VMs configured in the Recovery Plan are protected."
     ::= { nutanix 1563 }

  ntxTrapConflictingNgtPolicies   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "There are conflicting Ngt policies for the VM."
     ::= { nutanix 1564 }

  ntxTrapSystemTemperatureReadingError   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that System temperature can be fetched."
     ::= { nutanix 1565 }

  ntxTrapMultipleRecoveryPlansAreAssociatedWithCategorycategory   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if same category belongs to multiple Recovery Plans."
     ::= { nutanix 1566 }

  ntxTrapA300412   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks when last Test Failover was run on the Recovery Plan."
     ::= { nutanix 1567 }

  ntxTrapA110401   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if availability zones configured in protection policy are accessible."
     ::= { nutanix 1568 }

  ntxTrapA300418   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that VMs recovered using Recovery Plan after Test Failover have been cleaned up."
     ::= { nutanix 1569 }

  ntxTrapA300416   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if VMs which are part of different Recovery Plan have same Floating IPs."
     ::= { nutanix 1570 }

  ntxTrapVMsArePartOfMultipleStagesInTheRecoveryPlanrecoveryplan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether a VM is part of more than one stage in Recovery Plan."
     ::= { nutanix 1571 }

  ntxTrapA300414   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the VM count exceeds the threshold in Recovery Plan."
     ::= { nutanix 1572 }

  ntxTrapKaranServicesAreUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Karan service(s) are not reachable."
     ::= { nutanix 1573 }

  ntxTrapDataserviceIPIsUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Data service IP is not reachable."
     ::= { nutanix 1574 }

  ntxTrapUSBBootDeviceMissingOnNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "USB boot device connection check on Klas platform."
     ::= { nutanix 1575 }

  ntxTrapVSSSnapshotOfContainerShareFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "A VSS initiated snapshot of the container share failed due to too many files in the container."
     ::= { nutanix 1576 }

  ntxTrapPrismIsRestartingFrequently   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Tomcat is restarting frequently."
     ::= { nutanix 1577 }

  ntxTrapRecoveryPlanValidationFailedWithWarnings   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan validation failed with warnings."
     ::= { nutanix 1578 }

  ntxTrapRecoveryPlanValidationFailedWithErrors   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan validation failed with errors."
     ::= { nutanix 1579 }

  ntxTrapRecoveryPlanExecutionFailureDueToValidationErrors   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Execution of the Recovery Plan failed."
     ::= { nutanix 1580 }

  ntxTrapInvalidNetworkSettingsForTheRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Network settings have Availability Zones that are not in accordance with Availability Zone order list of Protection Policy applied to VMs in Recovery Plan."
     ::= { nutanix 1581 }

  ntxTrapRecoveryPlanExecutionFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Execution of the Recovery Plan failed."
     ::= { nutanix 1582 }

  ntxTrapSubnetsDeletionFailureForTheRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Deletion of dynamic subnets failed."
     ::= { nutanix 1583 }

  ntxTrapFloatingIPsDeallocationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Floating IPs deallocation failed."
     ::= { nutanix 1584 }

  ntxTrapSubnetCreationFailureForTheRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Subnet creation failed for the Recovery Plan."
     ::= { nutanix 1585 }

  ntxTrapFloatingIPsAllocationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Floating IPs allocation failed."
     ::= { nutanix 1586 }

  ntxTrapFailoverOrFailbackOperationsAreNotPossible   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Delete the failed over VMs on either the primary or the recovery location."
     ::= { nutanix 1587 }

  ntxTrapValidationWarningsFoundDuringRecoveryPlanExecution   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Execution of the Recovery Plan failed."
     ::= { nutanix 1588 }

  ntxTrapNodeRemovalFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to evacuate the VMs off the node."
     ::= { nutanix 1589 }

  ntxTrapConflictingNGTPolicies   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "There are conflicting NGT policies for the VM."
     ::= { nutanix 1590 }

  ntxTrapVmRestorationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Vm Restoration Failed."
     ::= { nutanix 1591 }

  ntxTrapRecoveryLocationIsInReadOnlyMode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Operation Mode of the Recovery Location changed to Read Only mode."
     ::= { nutanix 1592 }

  ntxTrapNGTOnVMIsNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NGT on the VM is not reachable."
     ::= { nutanix 1593 }

  ntxTrapNutanixGuestToolsNotInstalledOnTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Guest Tools not installed."
     ::= { nutanix 1594 }

  ntxTrapFullReplicationStartedForTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "A full replication was triggered for the VM."
     ::= { nutanix 1595 }

  ntxTrapReplicationTimeExceededTheRPO   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Replication Time Exceeded the RPO Limit."
     ::= { nutanix 1596 }

  ntxTrapNutanixVSSProviderOrprefreezepostthawScriptsNotInstalled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix VSS provider or pre_freeze/post_thaw Scripts Not Installed."
     ::= { nutanix 1597 }

  ntxTrapFailedToFindTheTargetClusterForReplication   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cannot find a target cluster for replication on the Remote Availability Zone."
     ::= { nutanix 1598 }

  ntxTrapVMVirtualHardwareVersionIncompatible   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Virtual Hardware Version Incompatible."
     ::= { nutanix 1599 }

  ntxTrapVMMigrationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Migration Failed."
     ::= { nutanix 1600 }

  ntxTrapVMRegisteredWithoutAnyNetwork   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to attach vNIC for the recovered VM."
     ::= { nutanix 1601 }

  ntxTrapProtectedVMNotRecoverable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VM Not Recoverable."
     ::= { nutanix 1602 }

  ntxTrapBackgroundEncryptionStuck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Background Encryption Stuck."
     ::= { nutanix 1603 }

  ntxTrapMaximumConnectionsLimitAboutToReachOnAFileServerVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Maximum connections limit is about to reach on a file server VM."
     ::= { nutanix 1604 }

  ntxTrapVSSSnapshotIsNotSupportedForSomeVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VSS snapshot is not supported for some VMs."
     ::= { nutanix 1605 }

  ntxTrapA110262   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any volume group with IP address in whitelist is protected in protection domain."
     ::= { nutanix 1606 }

  ntxTrapIncorrectvmtypeNTPConfiguration   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that NTP is configured properly on the CVM and hypervisor."
     ::= { nutanix 1607 }

  ntxTrapThevmtypeIsNotSynchronizingTimeWithAnyExternalServers   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks to ensure that the Controller VM is synchronizing time with an NTP server."
     ::= { nutanix 1608 }

  ntxTrapA103095   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if network switch DCBX and NIC PFC are correctly configured and enabled."
     ::= { nutanix 1609 }

  ntxTrapFirmwareVersionOfIntelS4600DrivesIsOldOrInvalid   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check firmware of Intel S4600 drives on Lenovo platform."
     ::= { nutanix 1610 }

  ntxTrapRemotenameConnectivityStatus   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Remote Connection Latency Check."
     ::= { nutanix 1611 }

  ntxTrapIncompleteMetroSetup   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metro setup for HyperV 2016 is incomplete."
     ::= { nutanix 1612 }

  ntxTrapA101047   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check storage pool has sufficient capacity to sustain replication factor rebuild capacity in the event of a node failure, based on highest replication factor container requirements."
     ::= { nutanix 1613 }

  ntxTrapP4GPUConfigurationWrongOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check number of P4 GPUs on a node."
     ::= { nutanix 1614 }

  ntxTrapPCVMTypeOrAnnotationNotSet   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the type and annotation of all PCVMs are set."
     ::= { nutanix 1615 }

  ntxTrapFailedToConfigureHostForAtlasNetworking   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to configure host for Atlas networking."
     ::= { nutanix 1616 }

  ntxTrapFailedToReserveHostMemoryForAtlasNetworking   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to reserve host memory for Atlas networking."
     ::= { nutanix 1617 }

  ntxTrapRecoveryPlanExecutionExceededTheTimeLimit   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Plan execution exceeded the time limit."
     ::= { nutanix 1618 }

  ntxTrapAHVPrismElementDetached   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "One of the existing AHV Prism Element is detached."
     ::= { nutanix 1619 }

  ntxTrapBeamIsNotReachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Beam is not reachable."
     ::= { nutanix 1620 }

  ntxTrapAHVPrismElementAttached   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "A new AHV Prism Element is attached."
     ::= { nutanix 1621 }

  ntxTrapVPNIPSECTunnelBetweenOnpremAndXiDatacenterIsDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The IPSEC tunnel between the on-prem VPN gateway and Xi VPN gateway is down."
     ::= { nutanix 1622 }

  ntxTrapEBGPSessionBetweenOnpremAndXiDatacenterIsDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The eBGP session between the on-prem VPN gateway and Xi VPN gateway is down."
     ::= { nutanix 1623 }

  ntxTrapMaximumVPNBGPRouteLimitReached   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "On-prem VPN gateway is advertising more than the maximum number of routes accepted by the Xi VPN gateway over eBGP."
     ::= { nutanix 1624 }

  ntxTrapDomainFaultToleranceIsLowForMetadata   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Domain fault tolerance is low for metadata."
     ::= { nutanix 1625 }

  ntxTrapRecoveryLocationIsNotInGoodState   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery location is not in good state."
     ::= { nutanix 1626 }

  ntxTrapA110264   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nearsync replication of the protection domain has not progressed."
     ::= { nutanix 1627 }

  ntxTrapFlowControlPlaneFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Flow failure event."
     ::= { nutanix 1628 }

  ntxTrapFlowRuleFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Programming a Flow rule failed."
     ::= { nutanix 1629 }

  ntxTrapAnalyticsVMComponentFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "One or more components of the Analytics VM avm_ip are not functioning properly or have failed."
     ::= { nutanix 1630 }

  ntxTrapAnalyticsVMHighCPUUsage   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The Analytics VM avm_ip has a high CPU usage of usage_percent%."
     ::= { nutanix 1631 }

  ntxTrapAnalyticsVMHighDiskUsage   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The Analytics VM avm_ip has a high disk usage of usage_percent%."
     ::= { nutanix 1632 }

  ntxTrapAnalyticsVMLowMemoryAvailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The memory available on AVM avm_ip is memory_available bytes which is low."
     ::= { nutanix 1633 }

  ntxTrapDuplicateIPAddressDetectedForAFileServerVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any file server VMs have IP address conflict."
     ::= { nutanix 1634 }

  ntxTrapFileServerUniqueFsidFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Assigning Unique Fsid failed."
     ::= { nutanix 1635 }

  ntxTrapUnequalDiskSizeOfPCVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if disk size of all PCVMs is same."
     ::= { nutanix 1636 }

  ntxTrapHighTimeDifferenceBetweenPCAndRegisteredPEs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks ntp sync between the PC and the registered PEs."
     ::= { nutanix 1637 }

  ntxTrapPrismCentralVersionEOL   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if Prism Central is running an EOL version."
     ::= { nutanix 1638 }

  ntxTrapPEPCIncompatibleAOSVersions   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the PE and PC AOS versions are compatible."
     ::= { nutanix 1639 }

  ntxTrapAplosGatewayIsDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if aplos gateway is down."
     ::= { nutanix 1640 }

  ntxTrapAplosEngineIsDown   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if aplos engine is down."
     ::= { nutanix 1641 }

  ntxTrapStorageContainersAreNotMountedOnAllNodes   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify that the Storage Containers are mounted on all nodes."
     ::= { nutanix 1642 }

  ntxTrapOldEntityCentricThirdPartyBackupSnapshotsPresent   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for the old entity-centric third-party backup snapshots."
     ::= { nutanix 1643 }

  ntxTrapUnsupportedSFPIsInstalledOnHostMachine   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if an unsupported SFP is plugged in."
     ::= { nutanix 1644 }

  ntxTrapMultipleRecoveryPlansAssociatedWithACategory   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if same category belongs to multiple Recovery Plans."
     ::= { nutanix 1645 }

  ntxTrapVMsArePartOfMultipleStagesInRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether a VM is part of more than one stage in Recovery Plan."
     ::= { nutanix 1646 }

  ntxTrapTestFailoverOnRecoveryPlanHasNotBeenExecutedRecently   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks when last Test Failover was run on the Recovery Plan."
     ::= { nutanix 1647 }

  ntxTrapNumberOfVMsInRecoveryPlanExceedsTheThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the VM count exceeds the threshold in Recovery Plan."
     ::= { nutanix 1648 }

  ntxTrapSATADOMNeedsFirmwareUpgrade   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the firmware version of SATA DOM SL 3IE3."
     ::= { nutanix 1649 }

  ntxTrapVCenterServerNotRegistered   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if vCenter server is registered."
     ::= { nutanix 1650 }

  ntxTrapDetectedOlderAHVVersion   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if hosts are installed with the recommended AHV version."
     ::= { nutanix 1651 }

  ntxTrapNoProtectionPolicyFoundForVMsInRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if VMs configured in the Recovery Plan are protected."
     ::= { nutanix 1652 }

  ntxTrapVMsNotCleanedUpFollowingTheTestFailoverForRecoveryPlan   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that VMs recovered using Recovery Plan after Test Failover have been cleaned up."
     ::= { nutanix 1653 }

  ntxTrapPowerSupplyStatusUnavailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that at least one power supply is available."
     ::= { nutanix 1654 }

  ntxTrapCVMTimeAndIPMISELTimeDoNotMatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for difference between CVM time and IPMI SEL time."
     ::= { nutanix 1655 }

  ntxTrapProtectionPolicyMaxVMsPerCategoryCheckFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the VM count for a category specified in Protection Policy exceeds the maximum allowed limit."
     ::= { nutanix 1656 }

  ntxTrapPulseIsDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Pulse is disabled on the paired Prism Central."
     ::= { nutanix 1657 }

  ntxTrapAOSVersionEOL   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "This cluster is running AOS Version current_AOS_version which is end of life from eol_date."
     ::= { nutanix 1658 }

  ntxTrapDIMMsOfDifferentManufacturersInOneMemoryChannel   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "DIMMs Interoperability Check."
     ::= { nutanix 1659 }

  ntxTrapPrismCentralUsingDefaultPassword   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "PC is using default password."
     ::= { nutanix 1660 }

  ntxTrapCVMUsingDefaultPassword   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "CVM is using default password."
     ::= { nutanix 1661 }

  ntxTrapHostUsingDefaultPassword   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Host is using default password."
     ::= { nutanix 1662 }

  ntxTrapIPMIUsingDefaultPassword   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "IPMI is using default password."
     ::= { nutanix 1663 }

  ntxTrapSM883DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of SM883 SSD drive."
     ::= { nutanix 1664 }

  ntxTrapFewerThanTwoNonSamsungPM883DrivesInstalledOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the types of disk drives on a node that has PM883 drives installed."
     ::= { nutanix 1665 }

  ntxTrapPM883DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of PM883 SSD."
     ::= { nutanix 1666 }

  ntxTrapCalmVersionMismatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Calm Version Mismatch."
     ::= { nutanix 1667 }

  ntxTrapEpsilonVersionMismatch   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Epsilon Version Mismatch."
     ::= { nutanix 1668 }

  ntxTrapCalmShowbackIsUnableToReachBeamService   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Beam service is unreachable. Calm showback calculations will be affected."
     ::= { nutanix 1669 }

  ntxTrapFileAnalyticsVMComponentFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "One or more components of the Analytics VM avm_ip are not functioning properly or have failed."
     ::= { nutanix 1670 }

  ntxTrapFileAnalyticsVMHighCPUUsage   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Analytics VM avm_ip has a high CPU usage of usage_percent%."
     ::= { nutanix 1671 }

  ntxTrapFileAnalyticsVMHighDiskUsage   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Analytics VM avm_ip has a high disk usage of usage_percent%."
     ::= { nutanix 1672 }

  ntxTrapFileAnalyticsVMLowMemoryAvailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The memory available on File Analytics VM avm_ip is memory_available bytes which is low."
     ::= { nutanix 1673 }

  ntxTrapFileServerVMTimeDriftFromNTPServers   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Difference between FSVM clock and NTP servers is too large."
     ::= { nutanix 1674 }

  ntxTrapFileServerServiceInCrashLoop   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server service in crash loop."
     ::= { nutanix 1675 }

  ntxTrapFileServerUpgradeTaskStuck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File Server Upgrade Task Stuck."
     ::= { nutanix 1676 }

  ntxTrapFilesClusterHATakeoverFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Files cluster HA takeover failed."
     ::= { nutanix 1677 }

  ntxTrapFileServerAlert   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Files server alert."
     ::= { nutanix 1678 }

  ntxTrapFlowModeChangeFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Flow control plane failure event."
     ::= { nutanix 1679 }

  ntxTrapOVSServiceRestart   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "OVS service has restarted on one or more AHV hosts. Rebuilt bridges and flow rules on these hosts."
     ::= { nutanix 1680 }

  ntxTrapRemoteSubnetIsNotConfiguredAppropriately   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Remote subnet is not configured appropriately."
     ::= { nutanix 1681 }

  ntxTrapA130204   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity is being transitioned to a higher frequency snapshot schedule."
     ::= { nutanix 1682 }

  ntxTrapA130205   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity is being transitioned to a lower frequency snapshot schedule."
     ::= { nutanix 1683 }

  ntxTrapHostingOfVirtualIPOfTheNetworkSegmentationDRServiceFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Hosting of virtual IP of the DR service network segmentation failed."
     ::= { nutanix 1684 }

  ntxTrapFailureToCopyImageToCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Detect if image copy to cluster is failing."
     ::= { nutanix 1685 }

  ntxTrapIDFirewallConnectivityaccessLossToAD   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "ID firewall unable to reach or access domain controller."
     ::= { nutanix 1686 }

  ntxTrapEpochSaaSConnectivityIsLost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Epoch SaaS can't be reached normally."
     ::= { nutanix 1687 }

  ntxTrapIDFirewallUnableToLocateMappedADObject   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "ID firewall unable to find a mapped object in AD."
     ::= { nutanix 1688 }

  ntxTrapSecurityPlanningIsDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Security Planning is disabled."
     ::= { nutanix 1689 }

  ntxTrapEpochDataCollectorNeedsUpgrade   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Epoch Data Collector needs to be upgraded."
     ::= { nutanix 1690 }

  ntxTrapIDFirewallUnableToRecoverStateFromADAfterDisconnect   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "ID firewall unable to recover state after disconnect from AD."
     ::= { nutanix 1691 }

  ntxTrapNGTCDROMNotUnmountedOnTheVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NGT CD-ROM not unmounted on the VM."
     ::= { nutanix 1692 }

  ntxTrapFloatingIPsDeallocationFailedAfterFailbackFromXi   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to deallocate unassociated floating IPs after failback from Xi that were earlier dynamically created."
     ::= { nutanix 1693 }

  ntxTrapTomcatIsRestartingFrequently   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Tomcat is restarting frequently."
     ::= { nutanix 1694 }

  ntxTrapExternalRepositoryAccessFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to access an NFS v3 external repository."
     ::= { nutanix 1695 }

  ntxTrapIOFailuresToADataSourceInAnExternalRepository   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "IO to an external data source within an external repository failed."
     ::= { nutanix 1697 }

  ntxTrapUnequalDiskSizeOfPrismCentralVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if disk size of /sdc1 partition on all Prism Central VMs is same."
     ::= { nutanix 1699 }

  ntxTrapPCUpgradesAreDisabledOncvmip   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if upgrades are disabled on PCVM."
     ::= { nutanix 1700 }

  ntxTrapCalmTrialLicenseExpiry   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Calm Trial License Expired."
     ::= { nutanix 1701 }

  ntxTrapSystemNonRootPartitionsSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if CVM system non-root partition usage is within threshold to ensure uninterrupted operations. Does not refer to cluster shared storage."
     ::= { nutanix 1702 }

  ntxTrapSystemRootPartitionSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if CVM system root partition usage is within threshold to ensure uninterrupted operations. Does not refer to cluster shared storage."
     ::= { nutanix 1703 }

  ntxTrapscratchLocationSpaceUsageIsHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that scratch space usage is not high."
     ::= { nutanix 1704 }

  ntxTrapFreeBlockCountOfSATADOMHasGoneBelowThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the free block count of SATA DOM SL 3IE3."
     ::= { nutanix 1705 }

  ntxTrapToshibaPM5DriveHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the status of Toshiba PM5 drive."
     ::= { nutanix 1706 }

  ntxTrapMoreThanOneTypeOfToshibaPM5DrivesInstalledOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the Configuration of Toshiba PM5 drives."
     ::= { nutanix 1707 }

  ntxTrapBootDriveIsInDegradedState   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check status of hardware raid and alert user if raid is degeraded."
     ::= { nutanix 1708 }

  ntxTrapTemperatureOfM2DiskIsOutOfRange   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks temperature of each drive and alert if it is not within 0C and 75C."
     ::= { nutanix 1709 }

  ntxTrapM2DiskReturnedUECCErrors   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if disk has returned UECC errors."
     ::= { nutanix 1710 }

  ntxTrapM2DiskHasWornOut   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks wearout level of the disk."
     ::= { nutanix 1711 }

  ntxTrapFirmwareOfRaidM2DiskNeedsToBeUpgraded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if hardware raid M2 disk firmware needs to be upgraded."
     ::= { nutanix 1712 }

  ntxTrapRAIDCardBIOSOrFirmwareOrBootLoaderNeedsToBeUpdated   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if RAID card BIOS, Firmware or boot loader needs to be updated."
     ::= { nutanix 1713 }

  ntxTrapDiskFirmwareNeedsUpgrade   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if any of the disks passthrough to CVM, requires firmware upgrade or not."
     ::= { nutanix 1714 }

  ntxTrapFlowPolicyhitLoggingDisabled   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether AHV is logging network traffic hitting Flow security policies."
     ::= { nutanix 1715 }

  ntxTrapLatencyBetweenvmtypes   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether latency among CVMs is high."
     ::= { nutanix 1716 }

  ntxTrapCVMdestipIsUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all CVMs are reachable via ping."
     ::= { nutanix 1717 }

  ntxTrapComputeonlyMinimumBandwidthCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether the total hyperconverged network bandwidth is twice the compute-only bandwidth."
     ::= { nutanix 1718 }

  ntxTrapFileServerVMDownCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if all File server VMs are reachable."
     ::= { nutanix 1719 }

  ntxTrapFileServerVMHardwareClockTimezoneNotSupported   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that file server VM hardware clock timezone is UTC."
     ::= { nutanix 1720 }

  ntxTrapNutanixFilesVersionEOL   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "File server(s) running versions which have reached end-of-life: fs_eol_msg."
     ::= { nutanix 1721 }

  ntxTrapFileServerContainerDedupCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Files container has dedup policy enabled."
     ::= { nutanix 1722 }

  ntxTrapCheckingIfTargetClusterVersionIsGreaterThan512   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the AOS verion of remote site is greater than 5.12."
     ::= { nutanix 1723 }

  ntxTrapCheckingIfPortsOfRelevantServicesAreOpenOrNot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if ports of relevant services for AHV Sync Rep are open or not."
     ::= { nutanix 1724 }

  ntxTrapA110022   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the the latency to target cluster is lesser than the maximum value allowed for AHV Sync Rep."
     ::= { nutanix 1725 }

  ntxTrapCheckingIfDRServicesAreReachableOrNot   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the DR services are reachable or not."
     ::= { nutanix 1726 }

  ntxTrapHighNumberOfCorrectableECCErrorsFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for number of CECC errors in the IPMI SEL."
     ::= { nutanix 1727 }

  ntxTrapPowerSuppliesOfDifferentTypesDetectedOnANode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for mixed power supplies on chassis."
     ::= { nutanix 1728 }

  ntxTrapPowerSupplyStatusDownUnretrievable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that Power supply has no errors."
     ::= { nutanix 1729 }

  ntxTrapProtectedVMNameTooLongOrContainsRestrictedCharacters   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the protected VM name is either too long, or contains restricted characters."
     ::= { nutanix 1730 }

  ntxTrapDetectedSnapshotsOnClusterWithHighDensityNodes   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if snapshots are present on a cluster with high density nodes."
     ::= { nutanix 1731 }

  ntxTrapDataProtectionIsConfiguredOnClusterWithHighDensityNodes   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if disaster recovery is configured on a cluster with high density nodes."
     ::= { nutanix 1732 }

  ntxTrapA110452   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if recovery points are detected on prism central managing clusters with high density nodes."
     ::= { nutanix 1733 }

  ntxTrapA110453   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if protection policies are detected on Prism Central managing clusters with high density nodes."
     ::= { nutanix 1734 }

  ntxTrapNetworkIsNotProperlyConfiguredOnTheHost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if networking is properly configured on the host."
     ::= { nutanix 1735 }

  ntxTrapDisconnectedAvailabilityZonesAreAffectingSomeEntities   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Lists all entites which will be affected due to the disconnected Availability Zones."
     ::= { nutanix 1736 }

  ntxTrapRecoveryPlanHasMultipleAvailabilityZoneOrders   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if Recovery Plan has multiple Availability Zone orders."
     ::= { nutanix 1737 }

  ntxTrapRecoveryPlanContainsVMsWithUnsupportedCHDRVMConfiguration   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if Recovery Plan contains VMs with Unsupported CHDR VM configuration."
     ::= { nutanix 1738 }

  ntxTrapA300426   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if Recovery Plan contains VMware VMs and snapshots for these VMs are replicated to a recovery Availability Zone that doesn't support recovery of VMware VMs."
     ::= { nutanix 1739 }

  ntxTrapA300428   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if VMs part of the recovery plan have an empty CDROM available to mount nutanix guest tools, so NGT reconfiguration tasks can be completed on restore."
     ::= { nutanix 1740 }

  ntxTrapvmtypeRebooted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that Cvm is not rebooted recently."
     ::= { nutanix 1741 }

  ntxTrapNodeIsInDegradedState   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any node is in degraded state."
     ::= { nutanix 1742 }

  ntxTrapComputeonlyClusterSizingHyperconvergedNodeCountCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that cluster with compute-only nodes has at least 4 hyperconverged nodes."
     ::= { nutanix 1743 }

  ntxTrapA405001   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Ratio of compute-only to hyperconverged nodes to not exceed 1:2."
     ::= { nutanix 1744 }

  ntxTrapComputeonlyClusterSizingCVMSizeCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that cluster with compute-only: Total CVM vCPUs must be greater than total cores on all compute-only nodes."
     ::= { nutanix 1745 }

  ntxTrapCalmContainersAreUnhealthy   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for Calm Container's state."
     ::= { nutanix 1746 }

  ntxTrapHealthWarningsDetectedInMetadataService   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for cassandra sstables with health warnings."
     ::= { nutanix 1747 }

  ntxTrapProtectedVMsNotCBRCapable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Some of the protected VMs are not capable of backup and recovery."
     ::= { nutanix 1748 }

  ntxTrapAutoSupportCheckFails   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for auto support configuration."
     ::= { nutanix 1749 }

  ntxTrapCoreDumpsAreEnabledOnThisCVMOrPCVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if saltstate is correctly set to default coreoff state to ensure services do not produce core dumps unnecessarily."
     ::= { nutanix 1750 }

  ntxTrapUnequalMetadataPartitionSizesAcrossPrismCentralVMs   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if metadata partition size(s) are the same among PC VMs."
     ::= { nutanix 1751 }

  ntxTrapFileServerCloneGracePeriodCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for licensing of Cloned file servers."
     ::= { nutanix 1752 }

  ntxTrapVMRecoveryStorageContainerIsNotMountedOnAllHosts   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify that the Storage Container is mounted on all the hosts."
     ::= { nutanix 1753 }

  ntxTrapOpenflowTableGettingFull   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that number of OVS rules is not reaching maximum number of rules."
     ::= { nutanix 1754 }

  ntxTrapNumberOfDatastoresConfiguredIsHigherThanESXiMaxConnPerIP   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the number of datastores configured in the ESXI host is higher than ESXi MaxConnPerIP advanced setting."
     ::= { nutanix 1755 }

  ntxTrapA110021   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the remote site configured is on AOS version >= 5.12 to support Synchronous Replication."
     ::= { nutanix 1756 }

  ntxTrapCheckingIfPortsOfRelevantServicesAreOpen   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the ports for the relevant services required for Synchronous Replication are open."
     ::= { nutanix 1757 }

  ntxTrapProtectedVMsMayNotBeRecoverable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VM(s) May Not Be Recoverable."
     ::= { nutanix 1758 }

  ntxTrapExternallyRegisteredAlert   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "This trap corresponds to any of alerts raised by external services."
     ::= { nutanix 1759 }

  ntxTrapInvalidBreakReplicationTimeout   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the break replication timeout of metro protection domain is zero or invalid."
     ::= { nutanix 1760 }

  ntxTrapBridgevSwitchConfigurationDoesNotMatchNSProtoInZookeeper   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the host bridge/vSwitch configuration matches NS proto in zookeeper."
     ::= { nutanix 1761 }

  ntxTrapActiveDirectoryDCsRunningOnCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if all active directory domain controllers are running on Nutanix cluster."
     ::= { nutanix 1762 }

  ntxTrapDNSServersRunningOnCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if all DNS servers are running on Nutanix cluster."
     ::= { nutanix 1763 }

  ntxTrapCPUUsageIsHighOnCVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether CPU usage is high on CVM."
     ::= { nutanix 1764 }

  ntxTrapSMARTParametersOfDiskAreOutOfRange   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Disk status check for SATA drives."
     ::= { nutanix 1765 }

  ntxTrapMinimumNOSAndFoundationVersionsAreNotSatisfied   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks NOS and Foundation version compatibility."
     ::= { nutanix 1766 }

  ntxTrapPCVMSystemRootPartitionSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if PC system root partition usage is within threshold to ensure uninterrupted operations. Does not refer to cluster shared storage."
     ::= { nutanix 1767 }

  ntxTrapA300430   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks for conflicting IP mappings across multiple Recovery Plans for the paired AZs."
     ::= { nutanix 1768 }

  ntxTrapCVMPythonServicesRestartingFrequently   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if python services have crashed/restarted recently in the Controller VM."
     ::= { nutanix 1769 }

  ntxTrapSnapshotReplicationToRemoteSiteIsLagging   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Replication of the snapshot to the remote cluster is lagging."
     ::= { nutanix 1770 }

  ntxTrapSMARTParametersOfDiskhostAreOutOfRange   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Disk status check for host boot disks."
     ::= { nutanix 1771 }

  ntxTrapOneOrMoreCassandraNodesHaveInvalidTokens   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for short tokens in Cassandra."
     ::= { nutanix 1772 }

  ntxTrapSecureBootFeatureStatus   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if secure boot configuration is consistent across all nodes in the cluster."
     ::= { nutanix 1773 }

  ntxTrapVMHasBeenRecoveredAtAnAlternatePath   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM has been Recovered at an Alternate Path."
     ::= { nutanix 1774 }

  ntxTrapInvalidHybridNodesConfigurationForErasureCoding   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks for valid configuration of coexisting all-flash and hybrid nodes in cluster when EC is enabled."
     ::= { nutanix 1775 }

  ntxTrapPlannedFailoverOrUnplannedFailoverOperationsWillFail   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Delete the failed over VMs on either the primary or the recovery location."
     ::= { nutanix 1776 }

  ntxTrapFileServerManagerUpgradeFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "The File Server Manager upgrade task failed on the cluster."
     ::= { nutanix 1777 }

  ntxTrapSEDKeysUnavailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check to see if the KMS server actually has the expected passwords for the SED disks."
     ::= { nutanix 1778 }

  ntxTrapSWEncryptionKeysFromkmsnameAreUnavailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check to see if the KMS server actually has the expected passwords for the container encryption keys."
     ::= { nutanix 1779 }

  ntxTrapOVAUploadInterrupted   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "OVA upload interrupted."
     ::= { nutanix 1780 }

  ntxTrapCVMOrPrismCentralVMRAMUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that CVM or Prism Central VM  memory usage is not high."
     ::= { nutanix 1781 }

  ntxTrapPrismCentralVMCPULoadHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that Prism Central VM CPU load is not high."
     ::= { nutanix 1782 }

  ntxTrapPrismCentralVMDiskUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that disk space usage on the Prism Central VM is within limits."
     ::= { nutanix 1783 }

  ntxTrapPrismCentralVMSystemRootPartitionSpaceUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if PC system root partition usage is within threshold to ensure uninterrupted operations. Does not refer to cluster shared storage."
     ::= { nutanix 1784 }

  ntxTrapPrismCentralVMLimitCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the number of VM entities is within the limit."
     ::= { nutanix 1785 }

  ntxTrapCoreDumpsAreEnabledOnThisCVMOrPrismCentralVM   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if saltstate is correctly set to default coreoff state to ensure services do not produce core dumps unnecessarily."
     ::= { nutanix 1786 }

  ntxTrapClusterDoesNotSupportSynchronousReplication   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Cluster does not support Synchronous Replication."
     ::= { nutanix 1787 }

  ntxTrapPrismCentralVMHomePartitionDiskUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if Prism Central home partition usage is within threshold to ensure uninterrupted operations. Does not refer to cluster shared storage."
     ::= { nutanix 1788 }

  ntxTrapFilesLicenseInvalid   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for Files license capacity violation."
     ::= { nutanix 1789 }

  ntxTrapSATAControllerStatusIsBad   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check SATA controller ports."
     ::= { nutanix 1790 }

  ntxTrapFileSystemInconsistenciesAreDetected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Captures EXT4-fs error messages."
     ::= { nutanix 1791 }

  ntxTrapSameTimezoneCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all CVMs are in the same timezone."
     ::= { nutanix 1792 }

  ntxTrapStoragePoolSpaceUsageExceededTheConfiguredThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks the storage pool space usage to determine if it's above the threshold."
     ::= { nutanix 1793 }

  ntxTrapHypervisorDiskUsageIsAboveTheRecommendedThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that host disk usage is not above the recommended threshold%."
     ::= { nutanix 1794 }

  ntxTrapInvalidSSDHDDDriveCombination   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether combination of SSDs and HDDs on the node is a supported configuration."
     ::= { nutanix 1795 }

  ntxTrapNodeMaintenanceModeFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VMs failed to evacuate off of the node."
     ::= { nutanix 1796 }

  ntxTrapDiskFailedMarkedOffline   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Disk Bad."
     ::= { nutanix 1797 }

  ntxTrapVirtualIPCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if virtual IP is configured and reachable."
     ::= { nutanix 1798 }

  ntxTrapInsufficientHostBandwidth   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether the total hyperconverged network bandwidth is twice the compute-only bandwidth."
     ::= { nutanix 1799 }

  ntxTrapOrphanedVSSCopiesAreDetected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if any orphan VSS copies are present."
     ::= { nutanix 1800 }

  ntxTrapStorageContainerSpaceUsageExceeded   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check high space usage on Storage Containers."
     ::= { nutanix 1801 }

  ntxTrapTwoNodeClusterStateChanged   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Change in state of two node cluster."
     ::= { nutanix 1802 }

  ntxTrapStateChangedForTwoNodeCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Change in state of two node cluster."
     ::= { nutanix 1803 }

  ntxTrapInsufficientSpaceForUVMsDeployedOnPC   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the amount of storage is sufficient for the number of VM entities in Prism Central."
     ::= { nutanix 1804 }

  ntxTrapLicensingWorkflowIsIncomplete   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check license standby mode."
     ::= { nutanix 1805 }

  ntxTrapInconsistentBridgevSwitchConfiguration   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the host bridge/vSwitch configuration matches NS config in zookeeper."
     ::= { nutanix 1806 }

  ntxTrapPowerCycleVMsBeforePerformingUpgradeOrMigrateOperation   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Power cycle all the VMs before you perform the upgrade or migrate operation."
     ::= { nutanix 1807 }

  ntxTrapStorageContainersIsareNotMountedOnAllNodes   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify that the Storage Container(s) is/are mounted on all nodes."
     ::= { nutanix 1808 }

  ntxTrapVMRecoveryStorageContainersIsareNotMountedOnAllHosts   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Verify that the Storage Container(s) is/are mounted on all the hosts."
     ::= { nutanix 1809 }

  ntxTrapFlowPolicyhitLoggingIsNotFunctional   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether AHV is logging network traffic hitting Flow security policies."
     ::= { nutanix 1810 }

  ntxTrapEpochSaaSServiceConnectivityLost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Epoch SaaS service can't be reached."
     ::= { nutanix 1811 }

  ntxTrapIDFirewallLostConnectivityToDomainController   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "ID Firewall is unable to connect to one or more domain controllers used for scraping identities."
     ::= { nutanix 1812 }

  ntxTrapEpochDataCollectorUpgradeAvailable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks whether there is an upgrade available for the Epoch Data Collector."
     ::= { nutanix 1813 }

  ntxTrapA803005   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "ID Firewall was unable to recover state after reconnecting to a temporarily unreachable domain controller."
     ::= { nutanix 1814 }

  ntxTrapIDFirewallUnableToLocateMappedActiveDirectoryObject   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "ID Firewall was unable to find a mapped object in Active Directory."
     ::= { nutanix 1815 }

  ntxTrapSkippedReplicationOfSnapshotForProtectionDomain   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Skipped replication of the snapshot."
     ::= { nutanix 1816 }

  ntxTrapInconsistentFileGroupsInTheSystem   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Inconsistent file groups possibly consuming unwarranted resources."
     ::= { nutanix 1817 }

  ntxTrapVMRestorationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Restoration Failed."
     ::= { nutanix 1818 }

  ntxTrapMetadataServiceRestartingFrequentlyDueToLongGCPauses   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metadata service restarting frequently due to long GC pauses."
     ::= { nutanix 1819 }

  ntxTrapA130206   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Subnet configured for Remote Site mis-matches with remote cluster subnet."
     ::= { nutanix 1820 }

  ntxTrapSomeOfTheVMsInTheRecoveryPlanAreUnprotected   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if VMs configured in the Recovery Plan are protected."
     ::= { nutanix 1821 }

  ntxTrapRemoteSiteIsIncompatibleForReplication   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Skipped replication of the snapshot."
     ::= { nutanix 1822 }

  ntxTrapFileServerContainerHasUnexpectedFiles   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nutanix Files container has unexpected files which is not recommended."
     ::= { nutanix 1823 }

  ntxTrapControllerVMTimeNotSynchronizedWithExternalServers   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that the Controller VM is synchronizing time with an NTP server."
     ::= { nutanix 1824 }

  ntxTrapHypervisorTimeNotSynchronisedWithAnyExternalServers   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks to ensure that the hypervisor is synchronizing time with an NTP server."
     ::= { nutanix 1825 }

  ntxTrapA110454   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the ports for the relevant services required for Synchronous Replication are open."
     ::= { nutanix 1826 }

  ntxTrapNearSyncReplicationOfProtectionDomainHasNotProgressed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Nearsync replication of the protection domain has not progressed."
     ::= { nutanix 1827 }

  ntxTrapPrismCentralVMTypeOrAnnotationNotSet   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the type and annotation of all PCVMs are set."
     ::= { nutanix 1828 }

  ntxTrapA200309   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks ntp sync between the Prism Central and the registered Prism Elements."
     ::= { nutanix 1829 }

  ntxTrapPrismCentralVCPUAvailabilityCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the number of vCPUs is sufficient for the number of VM entities in Prism Central."
     ::= { nutanix 1830 }

  ntxTrapPrismCentralMemoryAvailabilityCheck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if the amount of memory is sufficient for the number of VM entities and the services enabled in Prism Central."
     ::= { nutanix 1831 }

  ntxTrapPrismCentralUpgradesAreDisabledOncvmip   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks if upgrades are disabled on Prism Central VM."
     ::= { nutanix 1832 }

  ntxTrapHighNumberOfCorrectableUECCErrorsFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for number of UECC errors for last one day in the IPMI SEL."
     ::= { nutanix 1833 }

  ntxTrapTemperatureOfRAIDCardIsOutOfAboveThreshold   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks temperature of RAID card and alert if it is more than 60C."
     ::= { nutanix 1834 }

  ntxTrapCVMIPAddressIsUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check that all CVMs are reachable via ping."
     ::= { nutanix 1835 }

  ntxTrapA1191   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Checks that all-flash nodes do not coexist with non-all-flash nodes in a cluster."
     ::= { nutanix 1836 }

  ntxTrapDegradedRecoveryPoint   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Partial Recovery Point."
     ::= { nutanix 1837 }

  ntxTrapDIMMHPPRFailureEventFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for DIMM HPPR failure event in the IPMI SEL."
     ::= { nutanix 1838 }

  ntxTrapM60GPUConfigurationIncorrectOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check number of M60 GPUs on a node."
     ::= { nutanix 1839 }

  ntxTrapM10GPUConfigurationIncorrectOnTheNode   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check number of M10 GPUs on a node."
     ::= { nutanix 1840 }

  ntxTrapHostBootDiskRequiresAttention   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check the change of host boot disk."
     ::= { nutanix 1841 }

  ntxTrapA130177   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Availability Zone referenced in the Protection Policy is not Valid."
     ::= { nutanix 1842 }

  ntxTrapA130173   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Unable to retrieve the endpoint information for the Availability Zone."
     ::= { nutanix 1843 }

  ntxTrapProtectionDomainActivationFailed   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection Domain Activation."
     ::= { nutanix 1844 }

  ntxTrapAssociatedEntitiesAreNotProtectedTogether   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protection status of a related entity."
     ::= { nutanix 1845 }

  ntxTrapEntityConflictInConsistencyGroups   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Entity conflict in Consistency Groups."
     ::= { nutanix 1846 }

  ntxTrapVMSyncRepContainerNotFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM SyncRep container is not found at the target cluster."
     ::= { nutanix 1847 }

  ntxTrapRemoteSiteIsUnreachable   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the DR services are reachable or not."
     ::= { nutanix 1848 }

  ntxTrapNFSMetadataUsageHigh   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "NFS Metadata Usage High."
     ::= { nutanix 1849 }

  ntxTrapSSDTierSizeIsSmallOnOneOrMoreNodes   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if SSD disks are sized appropriately relative to HDD disk size."
     ::= { nutanix 1850 }

  ntxTrapClusterIsSusceptibleToCopyUpBlockMapIssue   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check whether cluster is susceptible to CopyUpBlockMap issue."
     ::= { nutanix 1851 }

  ntxTrapMemoryOvercommitFailure   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "One or more nodes have memory hotspots that cannot be solved. This imbalance can cause performance bottlenecks on the node(s) affected."
     ::= { nutanix 1852 }

  ntxTrapProtectedVMIsNotCapableOfBackupAndRecovery   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Some of the protected VMs are not capable of backup and recovery."
     ::= { nutanix 1853 }

  ntxTrapProtectedVMsNotFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Protected VM(s) Not Found."
     ::= { nutanix 1854 }

  ntxTrapDuplicateDiskIDsPresentInDifferentNodesOfTheSameCluster   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check for duplicate disk ids."
     ::= { nutanix 1855 }

  ntxTrapRecoveryPointExpiredPriorToStartOfReplication   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Recovery Point expired prior to start of replication."
     ::= { nutanix 1856 }

  ntxTrapTooManySnapshotsInTheSystem   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if the number of snapshots is approaching the supported limit."
     ::= { nutanix 1857 }

  ntxTrapA150003   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if flow visualization statistics collector service was restarted."
     ::= { nutanix 1858 }

  ntxTrapInvalidRoutesReceivedFromOnpremVPNGateway   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Invalid routes received from on-prem VPN gateway."
     ::= { nutanix 1859 }

  ntxTrapVMRenamedOnRestoration   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VM Renamed On Restoration."
     ::= { nutanix 1860 }

  ntxTrapSecondaryProtectionDomainsNotInSyncWithPrimary   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Check if secondary Metro Protection Domain is in sync with primary."
     ::= { nutanix 1861 }

  ntxTrapMetroConnectivityLost   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Metro connectivity is lost."
     ::= { nutanix 1862 }

  ntxTrapNodeRemovalStuck   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Failed to evacuate the VMs off the node."
     ::= { nutanix 1863 }

  ntxTrapVGSyncRepContainerNotFound   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "VG SyncRep container is not found on the target cluster."
     ::= { nutanix 1864 }

  ntxTrapLatestSnapshotOfProtectionDomainIsMissingEntities   NOTIFICATION-TYPE
     OBJECTS             { ntxAlertCreationTime, ntxAlertDisplayMsg, ntxAlertTitle, ntxAlertSeverity, ntxAlertClusterName, ntxAlertUuid, ntxAlertSourceEntityUuid,  ntxAlertSourceEntityName, ntxAlertSourceEntityType }
     STATUS                current
     DESCRIPTION           "Latest snapshot of protection domain is missing entities."
     ::= { nutanix 1865 }
END
