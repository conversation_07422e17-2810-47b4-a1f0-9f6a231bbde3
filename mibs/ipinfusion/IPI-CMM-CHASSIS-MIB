    IPI-CMM-CHASSIS-MIB DEFINITIONS ::= BEGIN

    --  This module provides authoritative definitions for cmm modules
    --
    --  This module will be extended, as needed.
    --

    IMPORTS
        OBJECT-TYPE, MODULE-IDENTITY,
        <PERSON><PERSON>ge32, Counter32, <PERSON>signed32,
        TimeTicks, NOTIFICATION-TYPE
            FROM SNMPv2-SMI
        DateAndTime, Di<PERSON>layString,
        MacA<PERSON>ress, RowStatus, TEXTUAL-CONVENTION
            FROM SNMPv2-TC
        MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
            FROM SNMPv2-CONF
        enterprises,OBJECT-IDENTITY
            FROM SNMPv2-SMI
        ipi FROM IPI-MODULE-MIB;


    cmm  MODULE-IDENTITY
    LAST-UPDATED "202005060000Z"   -- Jun 05, 2020 12:00:00 GMT
    ORGANIZATION "IP Infusion"
    CONTACT-INFO "<EMAIL>"

    DESCRIPTION
       "The Structure of CMM Management Information for
        the IPI enterprise."
  REVISION "202005060000Z"
  DESCRIPTION
      "Complying to level 3 of SMILINT"
    ::= { ipi 100 }


    -- ### Groups ###

    cmmChassisObject      OBJECT IDENTIFIER ::={ cmm 1 }
    cmmObjects            OBJECT IDENTIFIER ::={ cmmChassisObject 1 }
    cmmSysObjects         OBJECT IDENTIFIER ::={ cmmChassisObject 2 }
    cmmAlarmObjects       OBJECT IDENTIFIER ::={ cmmChassisObject 3 }

    -- ### Textual Convention
    LedColorCode ::= TEXTUAL-CONVENTION
       STATUS      current
       DESCRIPTION
          "The color code value for led's in the unit."
       SYNTAX  INTEGER   {
                    none(1),
                    green(2),
                    blinking-green(3),
                    solid-green(4),
                    amber(5),
                    blinking-amber(6),
                    solid-amber(7),
                    red(8),
                    blinking-red(9),
                    solid-red(10),
                    blue(11),
                    blinking-blue(12),
                    yellow(13),
                    blinking-yellow(14),
                    orange(15),
                    slow-blinking-green(16),
                    fast-blinking-green(17),
                    unknown(30),
                    unavailable(-100001),
                    not-applicable(-100002)
                    }

    SystemStatusCode ::= TEXTUAL-CONVENTION
       STATUS      current
       DESCRIPTION
          "The components for system status in the unit.
           Value '0' means there is no fault in the system."
        SYNTAX        BITS   {
                        cpu(0),
                        ram(1),
                        disk(2),
                        low-temperature(3),
                        high-temperature(4),
                        fan(5),
                        power(6),
                        software(7)
                        }
    ErrorFigures ::= TEXTUAL-CONVENTION
       STATUS         current
       DESCRIPTION
          " Frame Error Rate (FER)/Uncorrecter BER. This parameter measures the
            uncorrected/errored RS(544,514) equivalent frames per second. This
            is an uncorrected rate; i.e. the bit error rate of frames that
            cannot be corrected by the FEC.

            Pre-FEC Bit Error Ratio (BER). This is the total number of errored
            bits that were corrected by the FEC during an interval divided by
            the total number of bits received in the interval.

            Post-FEC Bit Error Ratio (BER). This is the total number of errored
            bits that were corrected by the FEC during an interval divided by
            the total number of bits received in the interval.

            Example: a mantissa value of 2.7 and an exponent value of -34
            indicates 2.7e-34"
       SYNTAX         OCTET STRING

    -- ### Chassis Information

      cmmNumStackUnits     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The total number of stack units configured
            on the chassis."
        ::= { cmmObjects 1 }



    -- ### Chassis System  ###


    -- ## StackUnit Table

    -- The Cmm chassis is a single virtual system to
    -- have the stackable units as virtual slots.
    -- In the chassis, there can be multiple physical units
    -- stacked together.

    -- The StackUnit table contains the management information
    -- of each stacked unit in the chassis.

      cmmStackUnitTable     OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmStackUnitEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A list of entries containing information
             for each stacked unit."
        ::= { cmmSysObjects 1 }

      cmmStackUnitEntry  OBJECT-TYPE
        SYNTAX        CmmStackUnitEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A list of entries containing information
             for each stacked unit."
        INDEX    { cmmStackUnitIndex }
        ::= { cmmStackUnitTable 1 }

      CmmStackUnitEntry    ::=
        SEQUENCE {
           cmmStackUnitIndex                   INTEGER,
           cmmStackUnitModelName               DisplayString,
           cmmStackUnitSerialNumber            DisplayString,
           cmmStackUnitUpTime                  TimeTicks,
           cmmStackUnitMfgDate                 DateAndTime,
           cmmStackUnitMacAddress              MacAddress,
           cmmStackUnitPartNum                 DisplayString,
           cmmStackLabelRevision               DisplayString,
           cmmStackUnitCountryCode             OCTET STRING,
           cmmStackUnitServiceTag              DisplayString,
           cmmStackPlatformName                DisplayString,
           cmmStackOnieVersion                 DisplayString,
           cmmStackMfgName                     DisplayString,
           cmmStackVendorName                  DisplayString,
           cmmStackDiagVersion                 DisplayString,
           cmmStackCrc32                       OCTET STRING,
           cmmStackUnitNumFanControllers       INTEGER,
           cmmStackUnitNumFanTrays             INTEGER,
           cmmStackUnitNumPowerSupplies        INTEGER,
           cmmStackUnitNumPluggableModules     INTEGER,
           cmmStackUnitNumFastEtherPorts       INTEGER,
           cmmStackUnitNumGigEtherPorts        INTEGER,
           cmmStackUnitNum10GigEtherPorts      INTEGER,
           cmmStackUnitNum25GigEtherPorts      INTEGER,
           cmmStackUnitNum40GigEtherPorts      INTEGER,
           cmmStackUnitNum50GigEtherPorts      INTEGER,
           cmmStackUnitNum100GigEtherPorts     INTEGER,
           cmmStackUnitSwitchChipRev           DisplayString,
           cmmStackSupportedLabelRevision      DisplayString,
           cmmStackUnitSupportedSwitchChipRev  DisplayString
        }

       cmmStackUnitIndex  OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
             "The unique stack unit number."
        ::= { cmmStackUnitEntry 1 }

       cmmStackUnitModelName  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The plugged-in model name for this unit.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 2 }

       cmmStackUnitSerialNumber OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's serial number.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 3 }

       cmmStackUnitUpTime OBJECT-TYPE
        SYNTAX      TimeTicks
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The system up time of the unit."
        ::= { cmmStackUnitEntry 4 }

       cmmStackUnitMfgDate    OBJECT-TYPE
        SYNTAX        DateAndTime
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The date the unit is manufactured.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 5 }

       cmmStackUnitMacAddress   OBJECT-TYPE
        SYNTAX          MacAddress
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "A 6-octet MAC Address assigned to this unit."
        ::= { cmmStackUnitEntry 6 }

       cmmStackUnitPartNum    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit part number.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 7 }

        cmmStackLabelRevision OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's label Revision.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 8 }

       cmmStackUnitCountryCode    OBJECT-TYPE
        SYNTAX        OCTET STRING (SIZE (2))
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit manufacturer's country code."
        ::= { cmmStackUnitEntry 9 }

       cmmStackUnitServiceTag OBJECT-TYPE
        SYNTAX        DisplayString (SIZE (0..7))
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's service tag.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 10 }

       cmmStackPlatformName OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The Platform Name for the Unit.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 11 }

       cmmStackOnieVersion OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's ONIE Version.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 12 }

       cmmStackMfgName OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's Manufacturer Name.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 13 }

       cmmStackVendorName OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's Vendor Name.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 14 }

       cmmStackDiagVersion OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's Diagnostic Version.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 15 }

        cmmStackCrc32 OBJECT-TYPE
        SYNTAX        OCTET STRING (SIZE(4))
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's Cyclic Redundancy Check 32 Bit value in hex."
        ::= { cmmStackUnitEntry 16 }

       cmmStackUnitNumFanControllers OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of fan controllers on the unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 17 }

       cmmStackUnitNumFanTrays  OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of fan trays on the unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 18 }

       cmmStackUnitNumPowerSupplies     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of power supplies available to the unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 19 }

       cmmStackUnitNumPluggableModules OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of pluggable modules in the stack.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 20 }

       cmmStackUnitNumFastEtherPorts     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of 100M Ethernet/802.3 interfaces in this unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 21 }

       cmmStackUnitNumGigEtherPorts     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of 1G Ethernet/802.3 interfaces in this unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 22 }

       cmmStackUnitNum10GigEtherPorts     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of 10G Ethernet/802.3 interfaces in this unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 23 }

       cmmStackUnitNum25GigEtherPorts     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of 25G Ethernet/802.3 interfaces in this unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 24 }

       cmmStackUnitNum40GigEtherPorts     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of 40G Ethernet/802.3 interfaces in this unit.
             '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 25 }

       cmmStackUnitNum50GigEtherPorts     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of 50G Ethernet/802.3 interfaces
            in this unit. '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 26 }

       cmmStackUnitNum100GigEtherPorts     OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of 100G Ethernet/802.3 interfaces
            in this unit. '-100001' indicates unavailable"
        ::= { cmmStackUnitEntry 27 }

       cmmStackUnitSwitchChipRev  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The Unit's Switch Chip Revision."
        ::= { cmmStackUnitEntry 28 }

        cmmStackSupportedLabelRevision OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unit's supported label Revision.
             Blank indicates unavailable"
        ::= { cmmStackUnitEntry 29 }

       cmmStackUnitSupportedSwitchChipRev  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The Unit's Switch Supported Chip Revision."
        ::= { cmmStackUnitEntry 30 }


    -- ## Transceiver EEPROM Entry
     cmmTransEEPROMTable    OBJECT-TYPE
     SYNTAX        SEQUENCE OF CmmTransEEPROMEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "A Transceiver entry containing eeprom related objects for a particular port."
     ::= { cmmSysObjects 2 }

      cmmTransEEPROMEntry      OBJECT-TYPE
        SYNTAX        CmmTransEEPROMEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A Transceiver entry containing eeprom related objects for a particular port."
        INDEX    { cmmStackUnitIndex,
                   cmmTransIndex }
        ::= { cmmTransEEPROMTable 1 }

      CmmTransEEPROMEntry::=
        SEQUENCE {
           cmmTransIndex                  INTEGER,
           cmmTransType                   INTEGER,
           cmmTransNoOfChannels           INTEGER,
           cmmTransidentifier             INTEGER,
           cmmTransSFPextendedidentifier  INTEGER,
           cmmTransQSFPextendedidentifier BITS,
           cmmTransconnectortype          INTEGER,
           cmmTransEthCompliance          INTEGER,
           cmmTransExtEthCompliance       INTEGER,
           cmmTransSonetCompliance        BITS,
           cmmTransFiberChnlLinkLen       BITS,
           cmmTransFiberChnlTransTech     BITS,
           cmmTransFiberChnlTransMedia    BITS,
           cmmTransSFPFiberChnlSpeed      BITS,
           cmmTransQSFPFiberChnlSpeed     BITS,
           cmmTransSFPInfiniBandCompliance   INTEGER,
           cmmTransSFPEsconCompliance        INTEGER,
           cmmTransSfpPlusCableTech       INTEGER,
           cmmTransEncoding               INTEGER,
           cmmTransLengthKmtrs            INTEGER,
           cmmTransLengthMtrs             INTEGER,
           cmmTransLengthOM1              INTEGER,
           cmmTransLengthOM2              INTEGER,
           cmmTransLengthOM3              INTEGER,
           cmmTransLengthOM4              INTEGER,
           cmmTransVendorName             DisplayString,
           cmmTransVendorOUI              DisplayString,
           cmmTransVendorPartNumber       DisplayString,
           cmmTransVendorRevision         DisplayString,
           cmmTransCheckCode              OCTET STRING,
           cmmTransCheckCodeExtended      OCTET STRING,
           cmmTransNominalBitRate         INTEGER,
           cmmTransBitRateMax             INTEGER,
           cmmTransBitRateMin             INTEGER,
           cmmTransVendorSerialNumber     DisplayString,
           cmmTransDateCode               DisplayString,
           cmmTransDDMSupport             INTEGER,
           cmmTransMaxCaseTemp            INTEGER,
           cmmTransSFPOptionsImp          BITS,
           cmmTransQSFPOptionsImp         BITS,
           cmmTransPresence               INTEGER,
           cmmTransFrontPanelPortNumber   INTEGER,
           cmmTransXFPextendedidentifier  BITS,
           cmmTransXFP10GEthCompliance    BITS,
           cmmTransXFP10GFiberChnCompliance BITS,
           cmmTransXFP10GCopperLinksRsvd  BITS,
           cmmTransXFPLowerSpeedLinks     BITS,
           cmmTransXFPSonetInterconnect   BITS,
           cmmTransXFPSonetShortHaul      BITS,
           cmmTransXFPSonetLongHaul       BITS,
           cmmTransXFPSonetVeryLongHaul   BITS,
           cmmTransXFPOptionsImp          BITS,
           cmmTransXFPVoltageAuxMonitor   BITS,
           cmmTransXFPEncoding            BITS,
           cmmTransWavelength             INTEGER,
           cmmTransChannelNumber          INTEGER,
           cmmTransGridSpacing            INTEGER,
           cmmTransLaserFirstFrequency    INTEGER,
           cmmTransLaserLastFrequency     INTEGER
        }

       cmmTransIndex   OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
            " A unique value, greater than zero,for each slot within the unit"
        ::= { cmmTransEEPROMEntry 1 }

       cmmTransType     OBJECT-TYPE
        SYNTAX        INTEGER {
                               sfp(1),
                               qsfp(2),
                               xfp(3),
                               pon-xfp(4),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver type"
        ::= { cmmTransEEPROMEntry 2 }

       cmmTransNoOfChannels    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Number of channels available for this transceiver.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 3 }

        cmmTransidentifier OBJECT-TYPE
        SYNTAX        INTEGER {
                             id-unknown(1),
                             gbic(2),
                             soldered-to-motherboard(3),
                             sfp-or-sfpplus-or-sfp28(4),
                             xbi-300pin(5),
                             xenpak(6),
                             xfp(7),
                             xff(8),
                             xfpe(9),
                             xpak(10),
                             x2(11),
                             dwdmsfp-or-dwdmsfpplus(12),
                             qsfp(13),
                             qsfpplus-or-later(14),
                             cxp-or-later(15),
                             shielded-mini-multilane-hd4x(16),
                             shielded-mini-multilane-hd8x(17),
                             qsfp28-or-later(18),
                             cxp2-aka-cxp28-or-later(19),
                             cdfpstyle1-or-cdfpstyle2(20),
                             shielded-mini-multilane-hd4x-fanoutcable(21),
                             shielded-mini-multilane-hd8x-fanoutcable(22),
                             cdfpstyle3(23),
                             microqsfp(24),
                             qsfp-doubledensity-8x-pluggable-transceiver(25),
                             reserved(26),
                             vendor-specific(27),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The Type of transceiver present eg:SFP/SFP+/SFP28/QSFP/QSFP+/QSFP28."
        ::= { cmmTransEEPROMEntry 4 }

        cmmTransSFPextendedidentifier OBJECT-TYPE
        SYNTAX        INTEGER {
                             gbic-notspecified-or-compliant-with-moddef(1),
                             gbic-compliant-with-moddef1(2),
                             gbic-compliant-with-moddef2(3),
                             gbic-compliant-with-moddef3(4),
                             gbic-or-sfp-definedby-twowire-interfaceid-only (5),
                             gbic-compliant-with-moddef5(6),
                             gbic-compliant-with-moddef6(7),
                             gbic-compliant-with-moddef7(8),
                             unallocated(9),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Extended identifier of SFP transceiver for additional information."
        ::= { cmmTransEEPROMEntry 5 }

        cmmTransQSFPextendedidentifier OBJECT-TYPE
        SYNTAX        BITS   {
                             powerclass1-1dot5wmax(0),
                             powerclass2-2wmax(1),
                             powerclass3-2dot5wmax(2),
                             powerclass4-3dot5wmax(3),
                             cleicode-present(4),
                             cdrpresent-in-tx(5),
                             cdrpresent-in-rx(6),
                             powerclass5-4wmax(7),
                             powerclass6-4dot5wmax(8),
                             powerclass7-5wmax(9),
                             unavailable(30),
                             not-applicable(31)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Extended identifier of QSFP transceiver for additional information."
        ::= { cmmTransEEPROMEntry 6 }

        cmmTransconnectortype   OBJECT-TYPE
        SYNTAX        INTEGER {
                             type-unknown(1),
                             subscriber-connector(2),
                             fibrechannel-style1-copperconnector(3),
                             fibrechannel-style2-copperconnector(4),
                             bayonet-or-threaded-neill-concelman(5),
                             fibrechannel-coaxheaders(6),
                             fiber-jack(7),
                             lucent-connector(8),
                             mechanical-transfer-registeredjack(9),
                             multiple-optical(10),
                             sg(11),
                             optical-pigtail(12),
                             multifiber-paralleloptic-1x12(13),
                             multifiber-paralleloptic-1x16(14),
                             hssdcii(15),
                             copper-pigtail(16),
                             rj45(17),
                             no-separable-connector(18),
                             mxc2-x16(19),
                             reserved(20),
                             vendor-specific(21),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Connector type for the transceiver."
        ::= { cmmTransEEPROMEntry 7 }

        cmmTransEthCompliance   OBJECT-TYPE
        SYNTAX        INTEGER {
                             ec-unknown(1),
                             ec-10gbase-sr(2),
                             ec-10gbase-lr(3),
                             ec-10gbase-lrm(4),
                             ec-10gbase-er(5),
                             ec-1000base-sx(6),
                             ec-1000base-lx(7),
                             ec-1000base-cx(8),
                             ec-1000base-t(9),
                             ec-100base-lx-or-lx10(10),
                             ec-100base-fx(11),
                             ec-base-bx10(12),
                             ec-base-px(13),
                             ec-40gbase-cr4(14),
                             ec-40gbase-sr4(15),
                             ec-40gbase-lr4(16),
                             ec-40g-activecable(17),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Values of Ethernet Compliance codes."
        ::= { cmmTransEEPROMEntry 8 }

        cmmTransExtEthCompliance   OBJECT-TYPE
        SYNTAX        INTEGER   {
                             eec-unspecified(0),
                             eec-100g-activeopticalcable-or-25g-auic2maoc(1),
                             eec-100gbase-sr4-or-25gbase-sr(2),
                             eec-100gbase-lr4-or-25gbase-lr(3),
                             eec-100gbase-er4-or-25gbase-er(4),
                             eec-100gbase-sr10(5),
                             eec-100g-cwdm4(6),
                             eec-100g-psm4-parallelsmf(7),
                             eec-100g-activecoppercable-or-25g-auic2macc(8),
                             eec-obsolete(9),
                             eec-reserved(10),
                             eec-100gbase-cr4-or-25gbase-crca-l(11),
                             eec-25gbase-crca-s(12),
                             eec-25gbase-crca-n(13),
                             eec-10mb-single-pair-eth(14),
                             eec-40gbase-er4(16),
                             eec-4x10gbase-sr(17),
                             eec-40g-psm4-parallelsmf(18),
                             eec-g959-dot1-profilep1-i1-2d1(19),
                             eec-g959-dot1-profilep1-s1-2d2(20),
                             eec-g959-dot1-profilep1-l1-2d2(21),
                             eec-10gbase-t-with-sfi-electricalinterface(22),
                             eec-100g-clr4(23),
                             eec-100g-aoc-or-25g-auic2maoc(24),
                             eec-100g-acc-or-25g-auic2macc(25),
                             eec-100ge-dwdm2(26),
                             eec-100g-1550nm-wdm(27),
                             eec-10gbaset-short-reach(28),
                             eec-5gbaset(29),
                             eec-2point5gbaset(30),
                             eec-40g-swdm4(31),
                             eec-100g-swdm4(32),
                             eec-100g-pam4-bidi(33),
                             eec-100g-4wdm-10msa(34),
                             eec-100g-4wdm-20msa(35),
                             eec-100g-4wdm-40msa(36),
                             eec-100gbase-dr-clause140(37),
                             eec-100g-fr-or-fr1-clause140(38),
                             eec-100g-lr-or-lr1-clause140(39),
                             eec-100gbase-sr1-p802-clause167(40),
                             eec-100gbase-sr1-200gbase-sr2-400gbase-sr4-clause167(41),
                             eec-100gbase-fr1-clause140(42),
                             eec-100gbase-lr1-clause140(43),
                             eec-100g-lr1-20msa-and-caui4(44),
                             eec-100g-er1-30msa-and-caui4(45),
                             eec-100g-er1-40msa-and-caui4(46),
                             eec-100g-lr1-20msa(47),
                             eec-acc-with-50gaui-100gaui2-200gaui4-c2m-worst-ber-10minus6(48),
                             eec-aoc-with-50gaui-100gaui2-200gaui4-c2m-worst-ber-10minus6(49),
                             eec-acc-with-50gaui-100gaui2-200gaui4-c2m-worst-ber-10minus5(50),
                             eec-aoc-with-50gaui-100gaui2-200gaui4-c2m-worst-ber-10-minus5(51),
                             eec-100g-er1-30msa(52),
                             eec-100g-er1-40msa(53),
                             eec-100gbase-vr1-200gbase-vr2-400gbase-sr4-clause167(54),
                             eec-10gbase-br(55),
                             eec-25gbase-br(56),
                             eec-50gbase-br(57),
                             eec-100gbase-vr1-p802-clause167(58),
                             -- //3b-3e reserved
                             eec-100gbase-cr1-200gbase-cr2-400gbase-cr4-clause162(63),
                             eec-50gbase-cr-100gbase-cr2-200gbase-cr4(64),
                             eec-50gbase-sr-100gbase-sr2-200gbase-sr4(65),
                             eec-50gbase-fr-200gbase-dr4(66),
                             eec-200gbase-fr4(67),
                             eec-200g-1550nm-psm4(68),
                             eec-50gbase-lr(69),
                             eec-200gbase-lr4(70),
                             eec-400gbase-dr4-clause124(71),
                             eec-400gbase-fr4-clause151(72),
                             eec-400gbase-fr46-clause151(73),
                             eec-50gbase-er-clause139(74),
                             eec-400g-lr410(75),
                             eec-400gbase-zr-clause156(76),
                             -- //4D-7E reserved
                             eec-256gfc-sw4(127),
                             eec-64gfc(128),
                             eec-128gfc(129),
                             -- //82-ff reserved
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Bitmap of Extended Ethernet Compliance codes."
        ::= { cmmTransEEPROMEntry 9 }

        cmmTransSonetCompliance   OBJECT-TYPE
        SYNTAX        BITS   {
                             oc192-shortreach(0),
                             sonet-reachspecifier-bit1(1),
                             sonet-reachspecifier-bit2(2),
                             oc48-longreach(3),
                             oc48-intermediatereach(4),
                             oc48-shortreach(5),
                             oc12-singlemode-longreach(6),
                             oc12-singlemode-intermediatereach(7),
                             oc12-singlemode-shortreach(8),
                             oc3-singlemode-longreach(9),
                             oc3-singlemode-intermediatereach(10),
                             oc3-singlemode-shortreach(11),
                             unavailable(30),
                             not-applicable(31)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Sonet Compliance codes of transceiver."
        ::= { cmmTransEEPROMEntry 10 }

        cmmTransFiberChnlLinkLen  OBJECT-TYPE
        SYNTAX        BITS {
                           short(0),
                           medium(1),
                           intermediate(2),
                           long(3),
                           verylong(4),
                           unavailable(30),
                           not-applicable(31)
                           }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Fiber Channel Link Length."
        ::= { cmmTransEEPROMEntry 11 }

        cmmTransFiberChnlTransTech  OBJECT-TYPE
        SYNTAX        BITS {
                           shortwaveLaserLinearRx(0),
                           longwaveLaserLC(1),
                           electricalInter-Enclosure(2),
                           electricalIntra-Enclosure(3),
                           shortwaveLaserWithOutOFC(4),
                           shortwaveLaserwithOFC(5),
                           longwaveLaserLL(6),
                           unavailable(30),
                           not-applicable(31)
                           }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Fiber Channel Transmitter Technology."
        ::= { cmmTransEEPROMEntry 12 }

        cmmTransFiberChnlTransMedia  OBJECT-TYPE
        SYNTAX        BITS {
                           twinaxial-pair(0),
                           twisted-pair(1),
                           miniature-coax(2),
                           video-coax(3),
                           multi-mode62dot5m(4),
                           multi-mode50m(5),
                           multi-mode50um(6),
                           single-mode(7),
                           unavailable(30),
                           not-applicable(31)
                           }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Fiber Channel Transmitter Media."
        ::= { cmmTransEEPROMEntry 13 }

        cmmTransSFPFiberChnlSpeed  OBJECT-TYPE
        SYNTAX        BITS  {
                             fcs-3200mbps(0),
                             fcs-1600mbps(1),
                             fcs-1200mbps(2),
                             fcs-800mbps(3),
                             fcs-400mbps(4),
                             fcs-200mbps(5),
                             fcs-100mbps(6),
                             unavailable(30),
                             not-applicable(31)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "SFP Fiber Channel Speed."
        ::= { cmmTransEEPROMEntry 14 }

        cmmTransQSFPFiberChnlSpeed  OBJECT-TYPE
        SYNTAX        BITS  {
                             fcs-3200mbps(0),
                             fcs-1600mbps(1),
                             fcs-1200mbps(2),
                             fcs-800mbps(3),
                             fcs-400mbps(4),
                             fcs-200mbps(5),
                             fcs-100mbps(6),
                             unavailable(30),
                             not-applicable(31)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "QSFP Fiber Channel Speed."
        ::= { cmmTransEEPROMEntry 15 }

        cmmTransSFPInfiniBandCompliance OBJECT-TYPE
        SYNTAX        INTEGER {
                           ibc-1xsx(1),
                           ibc-1xlx(2),
                           ibc-1xcopperactive(3),
                           ibc-1xcopperpassive(4),
                           unavailable(-100001),
                           not-applicable(-100002)
                           }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "SFP InfiniBand Compliance."
        ::= { cmmTransEEPROMEntry 16 }

        cmmTransSFPEsconCompliance  OBJECT-TYPE
        SYNTAX        INTEGER {
                           escon-mmf-1310nm-led(1),
                           escon-smf-1310nm-laser(2),
                           unavailable(-100001),
                           not-applicable(-100002)
                           }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "SFP Enterprise Systems Connection compliance."
        ::= { cmmTransEEPROMEntry 17 }

        cmmTransSfpPlusCableTech  OBJECT-TYPE
        SYNTAX        INTEGER {
                              active(1),
                              passive(2),
                              unavailable(-100001),
                              not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "SFP+ Cable Technology."
        ::= { cmmTransEEPROMEntry 18 }

        cmmTransEncoding  OBJECT-TYPE
        SYNTAX        INTEGER {
                              enc-unspecified(1),
                              enc-8b-or-10b(2),
                              enc-4b-or-5b(3),
                              enc-nrz(4),
                              enc-manchester(5),
                              enc-sonet-scrambled(6),
                              enc-64b-or-66b(7),
                              enc-256b-or-257b(8),
                              enc-pam4(9),
                              enc-reserved(10),
                              unavailable(-100001),
                              not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Code for SFP/QSFP high speed serial encoding algorithm."
        ::= { cmmTransEEPROMEntry 19 }

        cmmTransLengthKmtrs  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS          "km"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Link length supported for single mode fiber, units of km.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 20 }

        cmmTransLengthMtrs  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "100 m"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Link length supported for single mode fiber, units of 100 m.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 21 }

        cmmTransLengthOM1 OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "10 m"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Link length supported for 62.5 um OM1 fiber, units of 10 m.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 22 }

        cmmTransLengthOM2 OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "10 m"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Link length supported for 50 um OM2 fiber, units of 10 m.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 23 }

        cmmTransLengthOM3 OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "10 m"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Link length supported for 50 um OM3 fiber, units of 10 m.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 24 }

        cmmTransLengthOM4 OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "10 m"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Link length supported for 50 um OM4 fiber, units of 10 m.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 25 }

        cmmTransVendorName OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver vendor name.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 26 }

        cmmTransVendorOUI OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver vendor IEEE company ID.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 27 }

        cmmTransVendorPartNumber  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Part number provided by tarnsceiver vendor.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 28 }

        cmmTransVendorRevision  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Revision level for part number provided by vendor.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 29 }

        cmmTransCheckCode  OBJECT-TYPE
        SYNTAX        OCTET STRING (SIZE (1))
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Check code for Base ID Fields.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 30 }

        cmmTransCheckCodeExtended  OBJECT-TYPE
        SYNTAX        OCTET STRING (SIZE (1))
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Check code for the Extended ID Fields.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 31 }

        cmmTransNominalBitRate OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "100MBd"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Nominal signalling rate, units of 100MBd.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 32 }

        cmmTransBitRateMax  OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Upper bit rate margin, units of 1% above the Nominal Bit Rate.
            Value '0' indicates this field is not specified.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 33 }

        cmmTransBitRateMin  OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Lower bit rate margin, units of 1% below the Nominal Bit Rate.
            Value '0' indicates this field is not specified.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 34 }

        cmmTransVendorSerialNumber  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Serial number provided by vendor.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 35 }

        cmmTransDateCode  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Vendor's manufacturing date code.
             Blank indicates unavailable"
        ::= { cmmTransEEPROMEntry 36 }

        cmmTransDDMSupport  OBJECT-TYPE
        SYNTAX        INTEGER{
                             yes(1),
                             no(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Indicates whether diagnostics monitoring is
            supported or not.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 37 }

        cmmTransMaxCaseTemp   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " 0.01 C "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Maximum case temperature in Celsius.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 38 }

        cmmTransSFPOptionsImp  OBJECT-TYPE
        SYNTAX        BITS {
                           reserved(0),
                           power-level3(1),
                           paging(2),
                           internal-retimer-or-cdr(3),
                           cooled-laser-transmitter(4),
                           power-level2(5),
                           power-level1(6),
                           linear-receiver-output(7),
                           receiver-decision-threshold(8),
                           transmitter-wavelength-or-tunable-frequency(9),
                           rate-select(10),
                           tx-disable(11),
                           tx-fault(12),
                           rx-loss-of-signal(13),
                           unavailable(30),
                           not-applicable(31)
                           }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver Options Implemented for SFP Transceiver."
        ::= { cmmTransEEPROMEntry 39 }

        cmmTransQSFPOptionsImp  OBJECT-TYPE
        SYNTAX        BITS {
                           reserved(0),
                           tx-inputequalization-auto-adaptive(1),
                           tx-inputequalization-fixed-programmable(2),
                           tx-outputemphasis-fixed-programmable(3),
                           tx-outputamplitude-fixed-programmable(4),
                           tx-cdr-on-or-off-controllable(5),
                           tx-cdr-on-or-off-fixed(6),
                           rx-cdr-on-or-off-controllable(7),
                           rx-cdr-on-or-off-fixed(8),
                           tx-cdr-lossoflock(9),
                           rx-cdr-lossoflock(10),
                           rx-squelch-disable(11),
                           rx-output-disable(12),
                           tx-squelch-disable(13),
                           tx-squelch(14),
                           page2-provided(15),
                           page1-provided(16),
                           rateselect-controllable(17),
                           rateselect-fixed(18),
                           tx-disable(19),
                           tx-fault(20),
                           tx-squelch-to-reduce-pave(21),
                           tx-squelch-to-reduce-oma(22),
                           tx-loss-of-signal(23),
                           unavailable(30),
                           not-applicable(31)
                           }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver Options Implemented for QSFP Transceiver."
        ::= { cmmTransEEPROMEntry 40 }

        cmmTransPresence  OBJECT-TYPE
        SYNTAX        INTEGER{
                             present(1),
                             notpresent(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Indicates Transceiver presence status."
        ::= { cmmTransEEPROMEntry 41 }

        cmmTransFrontPanelPortNumber     OBJECT-TYPE
         SYNTAX        INTEGER
         MAX-ACCESS    read-only
         STATUS        current
         DESCRIPTION
             "Front panel port number"
         ::= { cmmTransEEPROMEntry 42 }

        cmmTransXFPextendedidentifier  OBJECT-TYPE
         SYNTAX        BITS {
                             powerlevel1-1dot5wmax(0),
                             powerlevel2-2dot5wmax(1),
                             powerlevel3-3dot5wmax(2),
                             powerlevel4-over3dot5w(3),
                             cdr-none(4),
                             tx-refclk-input-notrequired(5),
                             cleicode-present(6),
                             unavailable(30),
                             not-applicable(31)
                            }
         MAX-ACCESS    read-only
         STATUS        current
         DESCRIPTION
              "XFP transceiver extended identifier to provide extra information."
         ::= { cmmTransEEPROMEntry 43 }

        cmmTransXFP10GEthCompliance OBJECT-TYPE
          SYNTAX        BITS {
                              xec-10gbase-sr(0),
                              xec-10gbase-lr(1),
                              xec-10gbase-er(2),
                              xec-10gbase-lrm(3),
                              xec-10gbase-sw(4),
                              xec-10gbase-lw(5),
                              xec-10gbase-ew(6),
                              unavailable(30),
                              not-applicable(31)
                             }
          MAX-ACCESS    read-only
          STATUS        current
          DESCRIPTION
              "10G ethernet compliant interface supported by XFP transceiver"
          ::= { cmmTransEEPROMEntry 44 }

          cmmTransXFP10GFiberChnCompliance OBJECT-TYPE
           SYNTAX        BITS {
                                xfcc-1200-mx-sn-I(0),
                                xfcc-1200-sm-ll-l(1),
                                xfcc-exended-reach-1550nm(2),
                                xfcc-exen-reach-1300nm-fp(3),
                                unavailable(30),
                                not-applicable(31)
                              }
           MAX-ACCESS    read-only
           STATUS        current
           DESCRIPTION
                "10G fiber channel interface supported by XFP transceiver."
           ::= { cmmTransEEPROMEntry 45 }

          cmmTransXFP10GCopperLinksRsvd OBJECT-TYPE
           SYNTAX        BITS {
                                xcl-rsvd(0),
                                unavailable(30),
                                not-applicable(31)
                              }
           MAX-ACCESS    read-only
           STATUS        current
           DESCRIPTION
                "10G copper link interface supported by XFP transceiver (Not used)."
           ::= { cmmTransEEPROMEntry 46 }

          cmmTransXFPLowerSpeedLinks OBJECT-TYPE
           SYNTAX        BITS {
                              xlsl-1000base-sx(0),
                              xlsl-1000base-lx(1),
                              xlsl-2xfc-mmf(2),
                              xlsl-2xfc-smf(3),
                              xlsl-oc48-sr(4),
                              xlsl-oc48-ir(5),
                              xlsl-oc48-lr(6),
                              unavailable(30),
                              not-applicable(31)
                              }
           MAX-ACCESS    read-only
           STATUS        current
           DESCRIPTION
                "Low speed interface supported by XFP transceiver."
           ::= { cmmTransEEPROMEntry 47 }

          cmmTransXFPSonetInterconnect OBJECT-TYPE
           SYNTAX        BITS {
                              xsi-i-64-lr(0),
                              xsi-i-64-l(1),
                              xsi-i-64-2r(2),
                              xsi-i-64-2(3),
                              xsi-i-64-3(4),
                              xsi-i-64-5(5),
                              unavailable(30),
                              not-applicable(31)
                            }
           MAX-ACCESS    read-only
           STATUS        current
           DESCRIPTION
                "SONETinterconnect interface supported by XFP transceiver."
           ::= { cmmTransEEPROMEntry 48 }


          cmmTransXFPSonetShortHaul OBJECT-TYPE
           SYNTAX        BITS {
                             xssh-s-64-l(0),
                             xssh-s-64-2a(1),
                             xssh-s-64-2b(2),
                             xssh-s-64-3a(3),
                             xssh-s-64-3b(4),
                             xssh-s-64-5a(5),
                             xssh-s-64-5b(6),
                             unavailable(30),
                             not-applicable(31)
                            }
           MAX-ACCESS    read-only
           STATUS        current
           DESCRIPTION
                "SONET short haul interface supported by XFP transceiver."
           ::= { cmmTransEEPROMEntry 49 }

          cmmTransXFPSonetLongHaul OBJECT-TYPE
           SYNTAX        BITS {
                              xslh-l-64-l(0),
                              xslh-l-64-2a(1),
                              xslh-l-64-2b(2),
                              xslh-l-64-2c(3),
                              xslh-l-64-3(4),
                              xlsh-l-g959-1-p1l1-2d2(5),
                              unavailable(30),
                              not-applicable(31)
                             }
           MAX-ACCESS    read-only
           STATUS        current
           DESCRIPTION
                "SONET long haul interface supported by XFP transceiver."
           ::= { cmmTransEEPROMEntry 50 }

          cmmTransXFPSonetVeryLongHaul OBJECT-TYPE
           SYNTAX        BITS {
                              xsvh-v-64-2a(0),
                              xsvh-v-64-2b(1),
                              xsvh-v-64-3(2),
                              unavailable(30),
                              not-applicable(31)
                              }
           MAX-ACCESS    read-only
           STATUS        current
           DESCRIPTION
                "SONET very long haul interface supported by XFP transceiver."
           ::= { cmmTransEEPROMEntry 51 }


        cmmTransXFPOptionsImp OBJECT-TYPE
         SYNTAX        BITS {
                              xfp-vps(0),
                              xfp-tx-disable(1),
                              xfp-p-down(2),
                              xfp-vps-lv(3),
                              xfp-vps-bypass(4),
                              xfp-active-fec(5),
                              xfp-wavelength-tunability(6),
                              xfp-cmu(7),
                              unavailable(30),
                              not-applicable(31)
                            }
          MAX-ACCESS    read-only
          STATUS        current
          DESCRIPTION
              "Optional enhanced features implemented by XFP transceiver."
          ::= { cmmTransEEPROMEntry 52 }

        cmmTransXFPVoltageAuxMonitor OBJECT-TYPE
         SYNTAX        BITS {
                            xfp-vcc-5(0),
                            xfp-vcc-3(1),
                            xfp-vcc-2(2),
                            unavailable(30),
                            not-applicable(31)
                            }
         MAX-ACCESS    read-only
         STATUS        current
         DESCRIPTION
              "XFP Voltages monitored by auxiliary measurements."
         ::= { cmmTransEEPROMEntry 53 }

        cmmTransXFPEncoding  OBJECT-TYPE
        SYNTAX        BITS  {
                              enc-rsvd0(0),
                              enc-rsvd1(1),
                              enc-rsvd2(2),
                              enc-rz(3),
                              enc-nrz(4),
                              enc-sonet-scrambled(5),
                              enc-8b10b(6),
                              enc-64b66b(7),
                              unavailable(30),
                              not-applicable(31)
                            }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Code for XFP high speed serial encoding algorithm."
        ::= { cmmTransEEPROMEntry 54 }

        cmmTransWavelength  OBJECT-TYPE
        SYNTAX              INTEGER
        UNITS               "0.001 nm"
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "Transceiver Wavelength in 0.01 nm.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 55 }

        cmmTransChannelNumber  OBJECT-TYPE
        SYNTAX                  INTEGER
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
            "Transceiver Frequency channel number.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 56 }

        cmmTransGridSpacing  OBJECT-TYPE
        SYNTAX               INTEGER
        UNITS                "0.01 GHz"
        MAX-ACCESS           read-only
        STATUS               current
        DESCRIPTION
            "Transceiver Grid Spacing 0.01 GHz.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 57 }

        cmmTransLaserFirstFrequency   OBJECT-TYPE
        SYNTAX                        INTEGER
        UNITS                         "0.01 GHz"
        MAX-ACCESS                    read-only
        STATUS                         current
        DESCRIPTION
            "Laser first frequency in 0.01 GHz.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 58 }

        cmmTransLaserLastFrequency   OBJECT-TYPE
        SYNTAX                        INTEGER
        UNITS                         "0.01 GHz"
        MAX-ACCESS                    read-only
        STATUS                         current
        DESCRIPTION
            "Laser last frequency in 0.01 GHz.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransEEPROMEntry 59 }


    -- ## DDMChannel Entry
     cmmTransDDMTable OBJECT-TYPE
     SYNTAX        SEQUENCE OF CmmTransDDMEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "Information about Digital Optical Monitoring for each
          interface per channel."
     ::= { cmmSysObjects 3 }

      cmmTransDDMEntry OBJECT-TYPE
        SYNTAX        CmmTransDDMEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "Information about Digital Optical Monitoring for each
            interface per channel."
        INDEX    { cmmStackUnitIndex,
                   cmmTransIndex,
                   cmmTransChannelIndex
                 }
        ::= { cmmTransDDMTable 1 }

      CmmTransDDMEntry    ::=
        SEQUENCE {
           cmmTransChannelIndex                        INTEGER,
           cmmTransTemperature                         INTEGER,
           cmmTransTempAlertThresholdMin               INTEGER,
           cmmTransTempAlertThresholdMax               INTEGER,
           cmmTransTempCriticalThresholdMin            INTEGER,
           cmmTransTempCriticalThresholdMax            INTEGER,
           cmmTransVoltage                             INTEGER,
           cmmTransVoltAlertThresholdMin               INTEGER,
           cmmTransVoltAlertThresholdMax               INTEGER,
           cmmTransVoltCriticalThresholdMin            INTEGER,
           cmmTransVoltCriticalThresholdMax            INTEGER,
           cmmTransLaserBiasCurrent                    INTEGER,
           cmmTransLaserBiasCurrAlertThresholdMin      INTEGER,
           cmmTransLaserBiasCurrAlertThresholdMax      INTEGER,
           cmmTransLaserBiasCurrCriticalThresholdMin   INTEGER,
           cmmTransLaserBiasCurrCriticalThresholdMax   INTEGER,
           cmmTransTxPower                             INTEGER,
           cmmTransTxPowerAlertThresholdMin            INTEGER,
           cmmTransTxPowerAlertThresholdMax            INTEGER,
           cmmTransTxPowerCriticalThresholdMin         INTEGER,
           cmmTransTxPowerCriticalThresholdMax         INTEGER,
           cmmTransRxPower                             INTEGER,
           cmmTransRxPowerAlertThresholdMin            INTEGER,
           cmmTransRxPowerAlertThresholdMax            INTEGER,
           cmmTransRxPowerCriticalThresholdMin         INTEGER,
           cmmTransRxPowerCriticalThresholdMax         INTEGER,
           cmmTransTxPowerSupported                    INTEGER,
           cmmTransRxPowerSupported                    INTEGER,
           cmmTransDDMStatus                           INTEGER,
           cmmTransTxState                             INTEGER,
           cmmTransRxLosState                          INTEGER,
           cmmTransTxLosState                          INTEGER,
           cmmTransResetState                          INTEGER,
           cmmTransPowerMode                           INTEGER,
           cmmTransXFPVoltage2                         INTEGER,
           cmmTransXFPVolt2AlertThresholdMin           INTEGER,
           cmmTransXFPVolt2AlertThresholdMax           INTEGER,
           cmmTransXFPVolt2CriticalThresholdMin        INTEGER,
           cmmTransXFPVolt2CriticalThresholdMax        INTEGER,
           cmmTransFrequencyError                      INTEGER,
           cmmTransFrequencyErrorAlertThresholdMin     INTEGER,
           cmmTransFrequencyErrorAlertThresholdMax     INTEGER,
           cmmTransFrequencyErrorCriticalThresholdMin  INTEGER,
           cmmTransFrequencyErrorCriticalThresholdMax  INTEGER,
           cmmTransWavelengthError                     INTEGER,
           cmmTransWavelengthErrorAlertThresholdMin    INTEGER,
           cmmTransWavelengthErrorAlertThresholdMax    INTEGER,
           cmmTransWavelengthErrorCriticalThresholdMin INTEGER,
           cmmTransWavelengthErrorCriticalThresholdMax INTEGER,
           cmmTransCurrentStatus                       BITS
           }

       cmmTransChannelIndex   OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
            " A unique value, greater than zero,for each channel in the transceiver
              within the unit. For SFP transceivers, only channel '1' is applicable."
        ::= { cmmTransDDMEntry 1 }

        cmmTransTemperature  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Internally measured temperature of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 2 }

        cmmTransTempAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Alarm Threshold temperature of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 3 }

        cmmTransTempAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Alarm Threshold temperature of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 4}

        cmmTransTempCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Critical Threshold temperature of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 5}

        cmmTransTempCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Critical Threshold temperature of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 6}

        cmmTransVoltage  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Internally measured supply voltage of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 7 }

        cmmTransVoltAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Alarm Threshold voltage of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 8 }

        cmmTransVoltAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Alarm Threshold voltage of the transceiver in Volts.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 9 }

        cmmTransVoltCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Critical Threshold voltage of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 10 }

        cmmTransVoltCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Critical Threshold voltage of the transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 11 }

        cmmTransLaserBiasCurrent  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Internally measured TX Bias Current in Channel of transceiver
            '-100001' indicates unavailable.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 12 }

        cmmTransLaserBiasCurrAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Alarm Threshold of Laser Bias Current per channel.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 13 }

        cmmTransLaserBiasCurrAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Alarm Threshold of Laser Bias Current per channel.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 14 }

        cmmTransLaserBiasCurrCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Critical Threshold of Laser Bias Current per channel.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 15 }

        cmmTransLaserBiasCurrCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Critical Threshold of Laser Bias Current per channel.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 16 }

        cmmTransTxPower  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Measured TX output power in Channel of transceiver.
            '-100000' value represents no power.
            '-100001' indicates unavailable.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 17 }

        cmmTransTxPowerAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Alarm Threshold of TxOutput Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 18 }

        cmmTransTxPowerAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Alarm Threshold of TxOutput Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 19 }

        cmmTransTxPowerCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Critical Threshold of TxOutput Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 20 }

        cmmTransTxPowerCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Critical Threshold of TxOutput Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 21 }

        cmmTransRxPower  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Measured RX input power in Channel of transceiver
            '-100000' value represents no power in the transceiver.
            '-100001' indicates unavailable.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 22 }

        cmmTransRxPowerAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Alarm Threshold value of Rx Input Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 23 }

        cmmTransRxPowerAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Alarm Threshold value of Rx Input Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 24 }

        cmmTransRxPowerCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min Critical Threshold value of Rx Input Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 25 }

        cmmTransRxPowerCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dBm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max Critical Threshold value of Rx Input Power per channel.
             '-100000' value represents no power.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 26 }


        cmmTransTxPowerSupported  OBJECT-TYPE
        SYNTAX        INTEGER {
                               supported(1),
                               unsupported(2),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver TxPower Support"
        ::= { cmmTransDDMEntry 27 }

        cmmTransRxPowerSupported  OBJECT-TYPE
        SYNTAX        INTEGER {
                               supported(1),
                               unsupported(2),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver RxPower Support"
        ::= { cmmTransDDMEntry 28 }

        cmmTransDDMStatus  OBJECT-TYPE
        SYNTAX        INTEGER {
                                active(1),
                                activeunsupported(2),
                                inactive(3),
                                inactiveunsupported(4),
                                unavailable(-100001),
                                not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver DDM Status"
        ::= { cmmTransDDMEntry 29 }

        cmmTransTxState  OBJECT-TYPE
        SYNTAX        INTEGER {
                               enable(1),
                               disable(2),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver TX State"
        ::= { cmmTransDDMEntry 30 }

        cmmTransRxLosState  OBJECT-TYPE
        SYNTAX        INTEGER {
                               enable(1),
                               disable(2),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver RX Los State"
        ::= { cmmTransDDMEntry 31 }

        cmmTransTxLosState  OBJECT-TYPE
        SYNTAX        INTEGER {
                               enable(1),
                               disable(2),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver TX Los State (applicable only for QSFP)."
        ::= { cmmTransDDMEntry 32 }

       cmmTransResetState  OBJECT-TYPE
        SYNTAX        INTEGER {
                               normal(1),
                               reset(2),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver Reset State (applicable only for QSFP)."
        ::= { cmmTransDDMEntry 33 }

        cmmTransPowerMode  OBJECT-TYPE
        SYNTAX        INTEGER {
                               low(1),
                               high(2),
                               unavailable(-100001),
                               not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Transceiver Power mode (applicable only for QSFP)."
        ::= { cmmTransDDMEntry 34 }

        cmmTransXFPVoltage2  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Internally measured voltage of the second available supply of the XFP transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 35 }

        cmmTransXFPVolt2AlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The minimum warning threshold of the second available voltage of the XFP transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 36 }

        cmmTransXFPVolt2AlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The maximum warning threshold of the second available voltage of the XFP transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 37 }

        cmmTransXFPVolt2CriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The minimum critical threshold of the second available voltage of the XFP transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 38 }

        cmmTransXFPVolt2CriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The maximum critical threshold of the second available voltage of the XFP transceiver.
             This is common across channels for a transceiver.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 39 }

        cmmTransFrequencyError  OBJECT-TYPE
        SYNTAX                  INTEGER
        UNITS                   "0.01 GHZ"
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
            "Transceiver Frequency Error.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 40 }

        cmmTransFrequencyErrorAlertThresholdMin  OBJECT-TYPE
        SYNTAX                                      INTEGER
        UNITS                                       "0.01 GHz"
        MAX-ACCESS                                  read-only
        STATUS                                       current
        DESCRIPTION
            "The minimum alert threshold for transceiver frequency-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 41 }

        cmmTransFrequencyErrorAlertThresholdMax  OBJECT-TYPE
        SYNTAX                                      INTEGER
        UNITS                                       "0.01 GHz"
        MAX-ACCESS                                  read-only
        STATUS                                       current
        DESCRIPTION
            "The maximum alert threshold for transceiver frequency-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 42 }

        cmmTransFrequencyErrorCriticalThresholdMin  OBJECT-TYPE
        SYNTAX                                   INTEGER
        UNITS                                    "0.01 GHz"
        MAX-ACCESS                               read-only
        STATUS                                   current
        DESCRIPTION
            "The miniimum critical threshold for transceiver frequency-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 43 }

        cmmTransFrequencyErrorCriticalThresholdMax  OBJECT-TYPE
        SYNTAX                                   INTEGER
        UNITS                                    "0.01 GHz"
        MAX-ACCESS                               read-only
        STATUS                                   current
        DESCRIPTION
            "The maximum critical threshold for transceiver frequency-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 44 }

        cmmTransWavelengthError  OBJECT-TYPE
        SYNTAX                   INTEGER
        UNITS                    "0.00001 nm"
        MAX-ACCESS               read-only
        STATUS                   current
        DESCRIPTION
            "Transceiver Wavelength Error.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 45 }

        cmmTransWavelengthErrorAlertThresholdMin  OBJECT-TYPE
        SYNTAX                                       INTEGER
        UNITS                                        "0.00001 nm"
        MAX-ACCESS                                   read-only
        STATUS                                       current
        DESCRIPTION
            "The minimum alert threshold for transceiver wavelength-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 46 }

        cmmTransWavelengthErrorAlertThresholdMax  OBJECT-TYPE
        SYNTAX                                       INTEGER
        UNITS                                        "0.00001 nm"
        MAX-ACCESS                                   read-only
        STATUS                                       current
        DESCRIPTION
            "The maximum alert threshold for transceiver wavelength-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 47 }

        cmmTransWavelengthErrorCriticalThresholdMin  OBJECT-TYPE
        SYNTAX                                    INTEGER
        UNITS                                     "0.00001 nm"
        MAX-ACCESS                                read-only
        STATUS                                    current
        DESCRIPTION
            "The minimum critical threshold for transceiver wavelength-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 48 }

        cmmTransWavelengthErrorCriticalThresholdMax  OBJECT-TYPE
        SYNTAX                                    INTEGER
        UNITS                                     "0.00001 nm"
        MAX-ACCESS                                read-only
        STATUS                                    current
        DESCRIPTION
            "The maximum critical threshold for transceiver wavelength-error.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMEntry 49 }

        cmmTransCurrentStatus   OBJECT-TYPE
        SYNTAX                  BITS   {
                                tx-tune(0),
                                wavelength-unlocked(1),
                                tec-fault(2),
                                reserved(4)
                                }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Bitmap of supported paramaters of TSFP Current status.
             0 - not supported (values only for information)
             1 - supported (values can be used for monitoring)."
        ::= { cmmTransDDMEntry 50 }

    -- ### Ram Table

     cmmSysRamTable    OBJECT-TYPE
     SYNTAX        SEQUENCE OF CmmSysRamEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "An entry containing RAM related information of the Unit."
     ::= { cmmSysObjects 4 }

       cmmSysRamEntry OBJECT-TYPE
       SYNTAX        CmmSysRamEntry
       MAX-ACCESS    not-accessible
       STATUS        current
       DESCRIPTION
           "An entry containing RAM related information of the Unit."
       INDEX    { cmmStackUnitIndex }
       ::= { cmmSysRamTable 1 }

     CmmSysRamEntry    ::=
       SEQUENCE {
          cmmSysRamTotalMem            INTEGER,
          cmmSysRamUsedMem             INTEGER,
          cmmSysRamFreeMem             INTEGER,
          cmmSysRamAlertThreshold   INTEGER,
          cmmSysRamCriticalThreshold      INTEGER
         }

        cmmSysRamTotalMem  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " MBytes "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Total RAM available in this unit.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysRamEntry 1 }

        cmmSysRamUsedMem  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Used RAM in this unit.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysRamEntry 2 }

        cmmSysRamFreeMem  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Free RAM available in this unit.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysRamEntry 3 }

        cmmSysRamAlertThreshold OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "RAM usage threshold % for Alert level.
             '-100001' indicates unavailable"
        ::= { cmmSysRamEntry 4 }

        cmmSysRamCriticalThreshold OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "RAM usage threshold % for Critical level.
             '-100001' indicates unavailable"
        ::= { cmmSysRamEntry 5 }

    -- ### cmmStackCpuTable

       cmmStackCpuTable    OBJECT-TYPE
       SYNTAX        SEQUENCE OF CmmStackCpuEntry
       MAX-ACCESS    not-accessible
       STATUS        current
       DESCRIPTION
           "An entry containing cpu load information."
       ::= { cmmSysObjects 5 }

       cmmStackCpuEntry OBJECT-TYPE
       SYNTAX        CmmStackCpuEntry
       MAX-ACCESS    not-accessible
       STATUS        current
       DESCRIPTION
           "An entry containing cpu load information."
       INDEX    { cmmStackUnitIndex }
       ::= { cmmStackCpuTable 1 }

     CmmStackCpuEntry    ::=
       SEQUENCE {
          cmmStackUnitNumCpuProcessor               INTEGER,
          cmmStackUnitCpuLoad1Min                   INTEGER,
          cmmStackUnitCpuLoad5Min                   INTEGER,
          cmmStackUnitCpuLoad15Min                  INTEGER,
          cmmStackCpuLoad1minCriticalThreshold      INTEGER,
          cmmStackCpuLoad1minAlertThreshold         INTEGER,
          cmmStackCpuLoad5minAlertThreshold         INTEGER,
          cmmStackCpuLoad15minAlertThreshold        INTEGER,
          cmmStackUnitCpuUtilization                INTEGER,
          cmmStackUnitCpuUtilCriticalThreshold      INTEGER,
          cmmStackUnitCpuUtilAlertThreshold         INTEGER
         }

        cmmStackUnitNumCpuProcessor  OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The number of Online Processors Available.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 1 }

        cmmStackUnitCpuLoad1Min  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "CPU Load in percentage for last 1 minute.
              '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 2 }

        cmmStackUnitCpuLoad5Min  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "CPU Load in percentage for last 5 minutes.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 3 }

        cmmStackUnitCpuLoad15Min  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "CPU Load in percentage for last 15 minutes.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 4 }

        cmmStackCpuLoad1minCriticalThreshold OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The CPU Critical Threshold percentage value for 1 min load.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 5 }

        cmmStackCpuLoad1minAlertThreshold OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The CPU Alert Threshold percentage value for 1 min load.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 6 }

        cmmStackCpuLoad5minAlertThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The CPU Threshold percentage value for 5 min load.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 7 }

        cmmStackCpuLoad15minAlertThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The CPU Threshold percentage value for 15 min load.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 8 }

       cmmStackUnitCpuUtilization  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
          "CPU Utilization at this instance in percentage for this unit.
           '-100001' indicates unavailable"
        ::= {cmmStackCpuEntry 9 }

        cmmStackUnitCpuUtilCriticalThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The CPU utilization Critical Threshold %.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 10 }

        cmmStackUnitCpuUtilAlertThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The CPU utilization Alert Threshold %.
             '-100001' indicates unavailable"
        ::= { cmmStackCpuEntry 11 }

    -- ### Power Supply Table

   cmmSysPowerSupplyTable    OBJECT-TYPE
     SYNTAX        SEQUENCE OF CmmSysPowerSupplyEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
           "A power supply entry containing objects for a
           particular power supply Unit."
     ::= { cmmSysObjects 6 }

     cmmSysPowerSupplyEntry    OBJECT-TYPE
       SYNTAX        CmmSysPowerSupplyEntry
       MAX-ACCESS    not-accessible
       STATUS        current
       DESCRIPTION
           "A power supply entry containing objects for a
           particular power supply Unit."
       INDEX    { cmmStackUnitIndex,
                  cmmSysPSUIndex }
       ::= { cmmSysPowerSupplyTable 1 }

     CmmSysPowerSupplyEntry    ::=
       SEQUENCE {
          cmmSysPSUIndex                             INTEGER,
          cmmSysPowerSupplyOperStatus                INTEGER,
          cmmSysPowerSupplyType                      INTEGER,
          cmmSysHotSwapStat                          INTEGER,
          cmmSysPSConsumption                        INTEGER,
          cmmSysInputPower                           INTEGER,
          cmmSysInputVoltage                         INTEGER,
          cmmSysOutputVoltage                        INTEGER,
          cmmSysInputCurrent                         INTEGER,
          cmmSysOutputCurrent                        INTEGER,
          cmmSysPSTemperature1                       INTEGER,
          cmmSysPSTemperature2                       INTEGER,
          cmmSysPSFan1Rpm                            INTEGER,
          cmmSysPSFan2Rpm                            INTEGER,
          cmmSysPS12VPg                              INTEGER,
          cmmSysPSAcCritical                         INTEGER,
          cmmSysPSParamsSupport                      BITS,
          cmmSysPSCapacity                           INTEGER,
          cmmSysPSConsumptionAlertThresholdMin       INTEGER,
          cmmSysPSConsumptionAlertThresholdMax       INTEGER,
          cmmSysInputPowerAlertThresholdMin          INTEGER,
          cmmSysInputPowerAlertThresholdMax          INTEGER,
          cmmSysInputVoltageAlertThresholdMin        INTEGER,
          cmmSysInputVoltageAlertThresholdMax        INTEGER,
          cmmSysOutputVoltageAlertThresholdMin       INTEGER,
          cmmSysOutputVoltageAlertThresholdMax       INTEGER,
          cmmSysInputCurrentAlertThresholdMin        INTEGER,
          cmmSysInputCurrentAlertThresholdMax        INTEGER,
          cmmSysOutputCurrentAlertThresholdMin       INTEGER,
          cmmSysOutputCurrentAlertThresholdMax       INTEGER,
          cmmSysPSTemperature1AlertThresholdMin      INTEGER,
          cmmSysPSTemperature1AlertThresholdMax      INTEGER,
          cmmSysPSTemperature2AlertThresholdMin      INTEGER,
          cmmSysPSTemperature2AlertThresholdMax      INTEGER,
          cmmSysPSConsumptionCriticalThresholdMin    INTEGER,
          cmmSysPSConsumptionCriticalThresholdMax    INTEGER,
          cmmSysInputPowerCriticalThresholdMin       INTEGER,
          cmmSysInputPowerCriticalThresholdMax       INTEGER,
          cmmSysInputVoltageCriticalThresholdMin     INTEGER,
          cmmSysInputVoltageCriticalThresholdMax     INTEGER,
          cmmSysOutputVoltageCriticalThresholdMin    INTEGER,
          cmmSysOutputVoltageCriticalThresholdMax    INTEGER,
          cmmSysInputCurrentCriticalThresholdMin     INTEGER,
          cmmSysInputCurrentCriticalThresholdMax     INTEGER,
          cmmSysOutputCurrentCriticalThresholdMin    INTEGER,
          cmmSysOutputCurrentCriticalThresholdMax    INTEGER,
          cmmSysPSTemperature1CriticalThresholdMin   INTEGER,
          cmmSysPSTemperature1CriticalThresholdMax   INTEGER,
          cmmSysPSTemperature2CriticalThresholdMin   INTEGER,
          cmmSysPSTemperature2CriticalThresholdMax   INTEGER,
          cmmSysInputVoltageOverShutdown             INTEGER,
          cmmSysInputVoltageUnderShutdown            INTEGER,
          cmmSysInputVoltageOverResume               INTEGER,
          cmmSysInputVoltageUnderResume              INTEGER,
          cmmSysOutputVoltageOverShutdown            INTEGER,
          cmmSysOutputVoltageUnderShutdown           INTEGER,
          cmmSysOutputVoltageOverResume              INTEGER,
          cmmSysOutputVoltageUnderResume             INTEGER,
          cmmSysInputPowerOverShutdown               INTEGER,
          cmmSysInputPowerUnderShutdown              INTEGER,
          cmmSysInputPowerOverResume                 INTEGER,
          cmmSysInputPowerUnderResume                INTEGER,
          cmmSysOutputPowerOverShutdown              INTEGER,
          cmmSysOutputPowerUnderShutdown             INTEGER,
          cmmSysOutputPowerOverResume                INTEGER,
          cmmSysOutputPowerUnderResume               INTEGER,
          cmmSysInputCurrentOverShutdown             INTEGER,
          cmmSysInputCurrentUnderShutdown            INTEGER,
          cmmSysInputCurrentOverResume               INTEGER,
          cmmSysInputCurrentUnderResume              INTEGER,
          cmmSysOutputCurrentOverShutdown            INTEGER,
          cmmSysOutputCurrentUnderShutdown           INTEGER,
          cmmSysOutputCurrentOverResume              INTEGER,
          cmmSysOutputCurrentUnderResume             INTEGER,
          cmmSysPsuTemperature1OverShutdown          INTEGER,
          cmmSysPsuTemperature1UnderShutdown         INTEGER,
          cmmSysPsuTemperature1OverResume            INTEGER,
          cmmSysPsuTemperature1UnderResume           INTEGER,
          cmmSysPsuTemperature2OverShutdown          INTEGER,
          cmmSysPsuTemperature2UnderShutdown         INTEGER,
          cmmSysPsuTemperature2OverResume            INTEGER,
          cmmSysPsuTemperature2UnderResume           INTEGER,
          cmmSysPSTemperature3                       INTEGER,
          cmmSysPSTemperature3AlertThresholdMin      INTEGER,
          cmmSysPSTemperature3AlertThresholdMax      INTEGER,
          cmmSysPSTemperature3CriticalThresholdMin   INTEGER,
          cmmSysPSTemperature3CriticalThresholdMax   INTEGER,
          cmmSysPsuTemperature3OverShutdown          INTEGER,
          cmmSysPsuTemperature3UnderShutdown         INTEGER,
          cmmSysPsuTemperature3OverResume            INTEGER,
          cmmSysPsuTemperature3UnderResume           INTEGER,
          cmmSysPSFan3Rpm                            INTEGER,
          cmmSysPSFan4Rpm                            INTEGER
       }

        cmmSysPSUIndex    OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The unique index of the power supply."
        ::= { cmmSysPowerSupplyEntry 1 }

       cmmSysPowerSupplyOperStatus    OBJECT-TYPE
        SYNTAX        INTEGER {
                              notpresent(1),
                              running(2),
                              faulty(3),
                              not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The status of the power supply."
        ::= { cmmSysPowerSupplyEntry 2 }

       cmmSysPowerSupplyType    OBJECT-TYPE
        SYNTAX      INTEGER {
                            ac-normal(1),
                            ac-reverse(2),
                            dc-normal(3),
                            dc-reverse(4),
                            unavailable(-100001),
                            not-applicable(-100002)
                            }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The type of the power supply."
        ::= { cmmSysPowerSupplyEntry 3 }


       cmmSysHotSwapStat    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status for HOT SWAP."
        ::= { cmmSysPowerSupplyEntry 4 }

        cmmSysPSConsumption    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output Power of Power supply.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 5 }

        cmmSysInputPower    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input Power of Power supply.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 6 }

        cmmSysInputVoltage    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input Voltage of Power supply.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 7 }

        cmmSysOutputVoltage    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output Voltage of Power supply.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 8 }

        cmmSysInputCurrent    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input Current of Power supply.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 9 }

        cmmSysOutputCurrent    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output Current of Power supply.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 10 }

        cmmSysPSTemperature1    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Temperature of Power supply sensor 1.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 11 }

        cmmSysPSTemperature2    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Temperature of Power supply sensor 2.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 12 }

        cmmSysPSFan1Rpm    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Fan1 rpm of Power supply.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 13 }

        cmmSysPSFan2Rpm    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Fan2 rpm of Power supply.
            '-100001' indicates unavailable
            '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 14 }

       cmmSysPS12VPg  OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of Output Power"
        ::= { cmmSysPowerSupplyEntry 15 }

       cmmSysPSAcCritical  OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of Input Power"
        ::= { cmmSysPowerSupplyEntry 16 }

       cmmSysPSParamsSupport  OBJECT-TYPE
        SYNTAX        BITS   {
                        volt-in(0),
                        volt-out(1),
                        curr-in(2),
                        curr-out(3),
                        power-in(4),
                        power-out(5),
                        temp-1(6),
                        temp-2(7),
                        fan-1(8),
                        fan-2(9),
                        not-applicable(31)
                        }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Bitmap of supported paramaters of PSU Unit.
             0 - not supported (values only for information)
             1 - supported (values can be used for monitoring)."
        ::= { cmmSysPowerSupplyEntry 17 }

        cmmSysPSCapacity    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
        "Output Power of Power supply.
        '-100001' indicates unavailable.
        '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 18 }

    cmmSysPSConsumptionAlertThresholdMin   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU output power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 19 }

    cmmSysPSConsumptionAlertThresholdMax   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU output power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 20 }

    cmmSysInputPowerAlertThresholdMin   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU input power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 21 }

    cmmSysInputPowerAlertThresholdMax   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU input power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 22 }

    cmmSysInputVoltageAlertThresholdMin   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU input voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 23 }

    cmmSysInputVoltageAlertThresholdMax   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU input voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 24 }

    cmmSysOutputVoltageAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU output voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 25 }

    cmmSysOutputVoltageAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU output voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 26 }

    cmmSysInputCurrentAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU input current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 27 }

    cmmSysInputCurrentAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU input current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 28 }

    cmmSysOutputCurrentAlertThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU output current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 29 }

    cmmSysOutputCurrentAlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU output current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 30 }

    cmmSysPSTemperature1AlertThresholdMin OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU sensor temperature1.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 31 }

    cmmSysPSTemperature1AlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU sensor temperature1.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 32 }

    cmmSysPSTemperature2AlertThresholdMin OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU sensor temperature2.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 33 }

    cmmSysPSTemperature2AlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU sensor temperature2.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 34 }

    cmmSysPSConsumptionCriticalThresholdMin   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU output power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 35 }

    cmmSysPSConsumptionCriticalThresholdMax   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU output power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 36 }

    cmmSysInputPowerCriticalThresholdMin   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU input power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 37 }

    cmmSysInputPowerCriticalThresholdMax   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU input power.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 38 }

    cmmSysInputVoltageCriticalThresholdMin   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU input voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 39 }

    cmmSysInputVoltageCriticalThresholdMax   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU input voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 40 }

    cmmSysOutputVoltageCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU output voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 41 }

    cmmSysOutputVoltageCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU output voltage.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 42 }

    cmmSysInputCurrentCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU input current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 43 }

    cmmSysInputCurrentCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU input current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 44 }

    cmmSysOutputCurrentCriticalThresholdMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU output current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 45 }

    cmmSysOutputCurrentCriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU output current.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 46 }

    cmmSysPSTemperature1CriticalThresholdMin OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU sensor temperature1.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 47 }

    cmmSysPSTemperature1CriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU sensor temperature1.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 48 }

    cmmSysPSTemperature2CriticalThresholdMin OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU sensor temperature2.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 49 }

    cmmSysPSTemperature2CriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU sensor temperature2.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 50 }

    cmmSysInputVoltageOverShutdown  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input voltage over shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 51 }

    cmmSysInputVoltageUnderShutdown  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input voltage under shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 52 }

    cmmSysInputVoltageOverResume    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input voltage over resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 53 }

    cmmSysInputVoltageUnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input voltage under resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 54 }

    cmmSysOutputVoltageOverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output voltage over shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 55 }

    cmmSysOutputVoltageUnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output voltage under shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 56 }

    cmmSysOutputVoltageOverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output voltage over resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 57 }

    cmmSysOutputVoltageUnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 V"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output voltage under resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 58 }

    cmmSysInputPowerOverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input power over shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 59 }

    cmmSysInputPowerUnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input power under shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 60 }

    cmmSysInputPowerOverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input power over resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 61 }

    cmmSysInputPowerUnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input power under resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 62 }

    cmmSysOutputPowerOverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output power over shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 63 }

    cmmSysOutputPowerUnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output power under shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 64 }

    cmmSysOutputPowerOverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output power over resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 65 }

    cmmSysOutputPowerUnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 W"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output power under resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 66 }

    cmmSysInputCurrentOverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input current over shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 67 }

    cmmSysInputCurrentUnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input current under shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 68 }

    cmmSysInputCurrentOverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input current over resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 69 }

    cmmSysInputCurrentUnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Input current under resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 70 }

    cmmSysOutputCurrentOverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output current over shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 71 }

    cmmSysOutputCurrentUnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output current under shutdown value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 72 }

    cmmSysOutputCurrentOverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output current over resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 73 }

    cmmSysOutputCurrentUnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 A"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Output current under resume value of PSU.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 74 }

    cmmSysPsuTemperature1OverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature1 over shutdown value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 75 }

    cmmSysPsuTemperature1UnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature1 under shutdown value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 76 }

    cmmSysPsuTemperature1OverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature1 over resume value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 77 }

    cmmSysPsuTemperature1UnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature1 under resume value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 78 }

    cmmSysPsuTemperature2OverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature2 over shutdown value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 79 }

    cmmSysPsuTemperature2UnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature2 under shutdown value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 80 }

    cmmSysPsuTemperature2OverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature2 over resume value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 81 }

    cmmSysPsuTemperature2UnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature2 under resume value.
             '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 82 }

    cmmSysPSTemperature3    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Temperature of Power supply sensor 3.
            '-100001' indicates unavailable.
            '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 83 }

    cmmSysPSTemperature3AlertThresholdMin OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert min threshold of PSU sensor temperature3.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 84 }

    cmmSysPSTemperature3AlertThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents alert max threshold of PSU sensor temperature3.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 85 }

    cmmSysPSTemperature3CriticalThresholdMin OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents min critical threshold of PSU sensor temperature3.
             '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 86 }

    cmmSysPSTemperature3CriticalThresholdMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents max critical threshold of PSU sensor temperature3.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 87 }

    cmmSysPsuTemperature3OverShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature3 over shutdown value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 88 }

    cmmSysPsuTemperature3UnderShutdown   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature3 under shutdown value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 89 }

    cmmSysPsuTemperature3OverResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "PSU Temperature3 over resume value.
            '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 90 }

    cmmSysPsuTemperature3UnderResume   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
        "PSU Temperature3 under resume value.
        '-100001' indicates unavailable"
        ::= { cmmSysPowerSupplyEntry 91 }

    cmmSysPSFan3Rpm   OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
        "Fan3 rpm of Power supply.
        '-100001' indicates unavailable
        '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 92 }

    cmmSysPSFan4Rpm   OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
        "Fan4 rpm of Power supply.
        '-100001' indicates unavailable
        '-100002' indicates not-applicable."
        ::= { cmmSysPowerSupplyEntry 93 }


   -- ## Power Rail Table

   cmmSysPowerRailTable    OBJECT-TYPE
     SYNTAX        SEQUENCE OF CmmSysPowerRailEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
           "An entry containing objects for power rails in the unit."
     ::= { cmmSysObjects 7 }

     cmmSysPowerRailEntry    OBJECT-TYPE
       SYNTAX        CmmSysPowerRailEntry
       MAX-ACCESS    not-accessible
       STATUS        current
       DESCRIPTION
           "An entry containing objects for power rails in the unit."
       INDEX    { cmmStackUnitIndex }
       ::= { cmmSysPowerRailTable 1 }

      CmmSysPowerRailEntry    ::=
        SEQUENCE {
          cmmSysPOWERVDDR              INTEGER,
          cmmSysPOWERCORE              INTEGER,
          cmmSysV1P1POWERRAIL          INTEGER,
          cmmSysMAINBOARDPOWERRAIL     INTEGER,
          cmmSysV1P05POWERRAIL         INTEGER,
          cmmSysV1P5POWERRAIL          INTEGER,
          cmmSysVCCPOWERRAIL           INTEGER,
          cmmSysSBV1P5POWERRAIL        INTEGER,
          cmmSysV1P0POWERRAIL          INTEGER,
          cmmSysV3P3POWERRAIL          INTEGER,
          cmmSysV1P8POWERRAIL          INTEGER,
          cmmSysV1P35POWERRAIL         INTEGER,
          cmmSysVCC5V                  INTEGER,
          cmmSysVCC33V                 INTEGER,
          cmmSysVCCMAC1V               INTEGER,
          cmmSysVCCMACAVS1V            INTEGER,
          cmmSysVCCV1P05               INTEGER,
          cmmSysVCCV1P5                INTEGER,
          cmmSysVCCV1P8                INTEGER,
          cmmSysVCCAVS1V               INTEGER,
          cmmSysDDRVTT                 INTEGER,
          cmmSysSBV1P9POWERRAIL        INTEGER,
          cmmSysVCCMACV1P25            INTEGER,
          cmmSysMACV1P8                INTEGER,
          cmmSysPS1                    INTEGER,
          cmmSysPS2                    INTEGER,
          cmmSysPS1POWERRAIL           INTEGER,
          cmmSysPS2POWERRAIL           INTEGER,
          cmmSysPS1V12POWERRAIL        INTEGER,
          cmmSysPS2V12POWERRAIL        INTEGER,
          cmmSysPS1ACALERTPOWERRAIL    INTEGER,
          cmmSysPS2ACALERTPOWERRAIL    INTEGER,
          cmmSysPOWERVCCP              INTEGER,
          cmmSysV5APOWERRAIL           INTEGER,
          cmmSysV3P3APOWERRAIL         INTEGER,
          cmmSysXP0RV75POWERRAIL       INTEGER,
          cmmSysXP1RV07CPPOWERRAIL     INTEGER
          }

        cmmSysPOWERVDDR    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power statistics for VCC 3.3v on Main board."
        ::= { cmmSysPowerRailEntry 1 }

       cmmSysPOWERCORE    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power statistics for Mac 1v on Main board."
        ::= { cmmSysPowerRailEntry 2 }

       cmmSysV1P1POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power statistics for Mac AVS 1v on Main board."
        ::= { cmmSysPowerRailEntry 3 }

       cmmSysMAINBOARDPOWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status  for Main Board."
        ::= { cmmSysPowerRailEntry 4 }

       cmmSysV1P05POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of V1P05POWERRAIL."
        ::= { cmmSysPowerRailEntry 5 }

       cmmSysV1P5POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of V1P5POWERRAIL."
        ::= { cmmSysPowerRailEntry 6 }

       cmmSysVCCPOWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCCPOWERRAIL."
        ::= { cmmSysPowerRailEntry 7 }

       cmmSysSBV1P5POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of SBV1P5POWERRAIL."
        ::= { cmmSysPowerRailEntry 8 }

       cmmSysV1P0POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail 1.0V Status."
        ::= { cmmSysPowerRailEntry 9 }

       cmmSysV3P3POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail 3.3V Status."
        ::= { cmmSysPowerRailEntry 10 }

       cmmSysV1P8POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail 1.8V Status."
        ::= { cmmSysPowerRailEntry 11 }

       cmmSysV1P35POWERRAIL    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power Rail 1.35 Status."
        ::= { cmmSysPowerRailEntry 12 }

       cmmSysVCC5V    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCC 5V."
        ::= { cmmSysPowerRailEntry 13 }

       cmmSysVCC33V    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCC 33 V."
        ::= { cmmSysPowerRailEntry 14 }

       cmmSysVCCMAC1V    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCCMAC1V."
        ::= { cmmSysPowerRailEntry 15 }

       cmmSysVCCMACAVS1V    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCCMACAVS1V."
        ::= { cmmSysPowerRailEntry 16 }

       cmmSysVCCV1P05    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power Status of VCCV1P05."
        ::= { cmmSysPowerRailEntry 17 }

       cmmSysVCCV1P5    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power Status of VCCV1P5."
        ::= { cmmSysPowerRailEntry 18 }

       cmmSysVCCV1P8    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCCV1P8."
        ::= { cmmSysPowerRailEntry 19 }

       cmmSysVCCAVS1V    OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCCAVS1V."
        ::= { cmmSysPowerRailEntry 20 }

        cmmSysDDRVTT   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power statistics for VCC 5v on Main board."
        ::= { cmmSysPowerRailEntry 21 }

        cmmSysSBV1P9POWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail 1.9V status."
        ::= { cmmSysPowerRailEntry 22 }

        cmmSysVCCMACV1P25   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of VCC MAC 1.25 V."
        ::= { cmmSysPowerRailEntry 23 }

        cmmSysMACV1P8   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of MAC 1.8 V."
        ::= { cmmSysPowerRailEntry 24 }

        cmmSysPS1   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of Power Supply 1 on Main board."
        ::= { cmmSysPowerRailEntry 25 }

        cmmSysPS2   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of Power Supply 2 on Main board."
        ::= { cmmSysPowerRailEntry 26 }

        cmmSysPS1POWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail status of Power Supply 1 on Main board."
        ::= { cmmSysPowerRailEntry 27 }

        cmmSysPS2POWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail status of Power Supply 2 on Main board."
        ::= { cmmSysPowerRailEntry 28 }

        cmmSysPS1V12POWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail status of Power Supply 1 12V on Main board."
        ::= { cmmSysPowerRailEntry 29 }

        cmmSysPS2V12POWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail status of Power Supply 2 12V on Main board."
        ::= { cmmSysPowerRailEntry 30 }

        cmmSysPS1ACALERTPOWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail status of Power Supply 1 AC critical on Main board."
        ::= { cmmSysPowerRailEntry 31 }

        cmmSysPS2ACALERTPOWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail status of Power Supply 2 AC critical on Main board."
        ::= { cmmSysPowerRailEntry 32 }

        cmmSysPOWERVCCP   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status for VCCP on Main board."
        ::= { cmmSysPowerRailEntry 33 }

        cmmSysV5APOWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail 5VA status on Main board."
        ::= { cmmSysPowerRailEntry 34 }

        cmmSysV3P3APOWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power rail 3.3VA status on Main board."
        ::= { cmmSysPowerRailEntry 35 }

        cmmSysXP0RV75POWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of XP0R 75V on Main board."
        ::= { cmmSysPowerRailEntry 36 }

        cmmSysXP1RV07CPPOWERRAIL   OBJECT-TYPE
        SYNTAX        INTEGER{
                             good(1),
                             fail(2),
                             unavailable(-100001),
                             not-applicable(-100002)
                             }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Power status of XP1R 07V CP on Main board."
        ::= { cmmSysPowerRailEntry 37 }

    -- ## Fan Tray Table

       cmmFanTrayTable    OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmFanTrayEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A Fan Tray entry containing information of fan tray."
        ::= { cmmSysObjects 8 }

      cmmFanTrayEntry    OBJECT-TYPE
        SYNTAX        CmmFanTrayEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A Fan Tray entry containing information of fan tray."
        INDEX    { cmmStackUnitIndex,
                   cmmFanTrayNumber
                   }
        ::= { cmmFanTrayTable 1 }

      CmmFanTrayEntry    ::=
        SEQUENCE {
           cmmFanTrayNumber       INTEGER,
           cmmFanTrayStatus       INTEGER,
           cmmFanTrayLedColor     LedColorCode
        }

       cmmFanTrayNumber    OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the fan tray Number."
        ::= { cmmFanTrayEntry 1 }

        cmmFanTrayStatus    OBJECT-TYPE
        SYNTAX        INTEGER {
                              notpresent(1),
                              present(2),
                              unavailable(-100001),
                              not-applicable(-100002)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the fan tray status."
        ::= { cmmFanTrayEntry 2 }


        cmmFanTrayLedColor    OBJECT-TYPE
        SYNTAX        LedColorCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the Color of the fan led."
        ::= { cmmFanTrayEntry 3 }

    -- ## Fan Entry in FanTray Table

       cmmFanTable    OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmFanEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A fan entry containing objects for a particular fan in a fan tray."
        ::= { cmmSysObjects 9 }

      cmmFanEntry    OBJECT-TYPE
        SYNTAX        CmmFanEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A fan entry containing objects for a particular fan in a fan tray."
        INDEX    { cmmStackUnitIndex,
                   cmmFanTrayNumber,
                   cmmFanIndex }
        ::= { cmmFanTable 1 }

      CmmFanEntry    ::=
        SEQUENCE {
           cmmFanIndex            INTEGER,
           cmmFanRpm              INTEGER,
           cmmFanRpmMin           INTEGER,
           cmmFanRpmMax           INTEGER,
           cmmFanStatus           INTEGER,
           cmmFanLocation         INTEGER
        }

       cmmFanIndex    OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Index of the fan per fantray."
        ::= { cmmFanEntry 1 }

       cmmFanRpm    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the fan rpm.
             '-100001' indicates unavailable.
             '-100002' indicates not-applicable."
        ::= { cmmFanEntry 2 }

       cmmFanRpmMin    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the minimum supported RPM of fan.
            '-100001' indicates unavailable.
            '-100002' indicates not-applicable."
        ::= { cmmFanEntry 3 }

       cmmFanRpmMax    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the maximum supported RPM of fan.
            '-100001' indicates unavailable.
            '-100002' indicates not-applicable."
        ::= { cmmFanEntry 4 }

       cmmFanStatus    OBJECT-TYPE
       SYNTAX        INTEGER {
                             notpresent(1),
                             running(2),
                             faulty(3),
                             stalled(4),
                             not-applicable(-100002)
                             }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The status of the fan."
        ::= { cmmFanEntry 5 }

       cmmFanLocation    OBJECT-TYPE
       SYNTAX        INTEGER {
                             front(1),
                             rear(2),
                             unknown(3),
                             not-applicable(-100002)
                             }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The location of fan in tray"
        ::= { cmmFanEntry 6 }

-- ## Stack Temperature Table
        cmmSysTemperatureTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSysTemperatureEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A temperature entry containing objects related to particular temperature sensor"
        ::= { cmmSysObjects 10 }

      cmmSysTemperatureEntry  OBJECT-TYPE
        SYNTAX        CmmSysTemperatureEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A temperature entry containing objects related to particular temperature sensor"
        INDEX    { cmmStackUnitIndex,
                   cmmSysTemperatureSensorIndex }
        ::= { cmmSysTemperatureTable 1 }

      CmmSysTemperatureEntry    ::=
        SEQUENCE {
          cmmSysTemperatureSensorIndex       INTEGER,
          cmmSysTemperatureSensorName        DisplayString,
          cmmSysTemperatureValue             INTEGER,
          cmmSysTempEmergencyThresholdMin    INTEGER,
          cmmSysTempEmergencyThresholdMax    INTEGER,
          cmmSysTempCriticalThresholdMin     INTEGER,
          cmmSysTempCriticalThresholdMax     INTEGER,
          cmmSysTempAlertThresholdMin        INTEGER,
          cmmSysTempAlertThresholdMax        INTEGER,
          cmmSysTempMinValue                 INTEGER,
          cmmSysTempMaxValue                 INTEGER,
          cmmSysTempAverageValue             INTEGER
        }

        cmmSysTemperatureSensorIndex    OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Index"
        ::= { cmmSysTemperatureEntry 1 }

        cmmSysTemperatureSensorName    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Temperature Sensor Name.
             Blank indicates unavailable"
        ::= { cmmSysTemperatureEntry 2 }

        cmmSysTemperatureValue    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor value.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 3 }

        cmmSysTempEmergencyThresholdMin    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Min Emergency threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 4 }

        cmmSysTempEmergencyThresholdMax    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Max Emergency threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 5 }

        cmmSysTempCriticalThresholdMin    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Min Critical threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 6 }

        cmmSysTempCriticalThresholdMax    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Max Critical threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 7 }

        cmmSysTempAlertThresholdMin   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Min Alert threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 8 }

        cmmSysTempAlertThresholdMax   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Max Alert threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 9 }

        cmmSysTempMinValue               OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Min value since 72hrs.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 10 }

        cmmSysTempMaxValue               OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Max value since 72hrs.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 11 }

        cmmSysTempAverageValue               OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Sensor Average value since 72hrs.
             '-100001' indicates unavailable"
        ::= { cmmSysTemperatureEntry 12 }

-- ## Component Status Table
        cmmSysComponentStatusTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSysComponentStatusEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing each component status in the Unit."
        ::= { cmmSysObjects 11 }

      cmmSysComponentStatusEntry  OBJECT-TYPE
        SYNTAX        CmmSysComponentStatusEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing each component status in the Unit."
        INDEX    { cmmStackUnitIndex }
        ::= { cmmSysComponentStatusTable 1 }

      CmmSysComponentStatusEntry    ::=
        SEQUENCE {
          cmmSysPsu1Status           INTEGER,
          cmmSysPsu1LedColor         LedColorCode,
          cmmSysPsu2Status           INTEGER,
          cmmSysPsu2LedColor         LedColorCode,
          cmmSysLocatorLedStatus     INTEGER,
          cmmSysLocatorLedColor      LedColorCode,
          cmmSysMasterLedStatus      INTEGER,
          cmmSysMasterLedColor       LedColorCode,
          cmmSysFanStatus            INTEGER,
          cmmSysFrontFanLedColor     LedColorCode,
          cmmSysRamStatus            INTEGER,
          cmmSysCpuStatus            INTEGER,
          cmmSysDiskStatus           INTEGER,
          cmmSysTemperatureStatus    INTEGER
        }

        cmmSysPsu1Status    OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1),
                              minor-fault(2),
                              major-fault(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of PSU1."
        ::= { cmmSysComponentStatusEntry 1 }

        cmmSysPsu1LedColor    OBJECT-TYPE
        SYNTAX        LedColorCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the Color of the power led of PSU1."
        ::= { cmmSysComponentStatusEntry 2 }

        cmmSysPsu2Status    OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1),
                              minor-fault(2),
                              major-fault(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of PSU2."
        ::= { cmmSysComponentStatusEntry 3 }

        cmmSysPsu2LedColor    OBJECT-TYPE
        SYNTAX        LedColorCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the Color of the power led of PSU2."
        ::= { cmmSysComponentStatusEntry 4 }

        cmmSysLocatorLedStatus    OBJECT-TYPE
        SYNTAX        INTEGER {
                              notpresent(1),
                              on(2),
                              off(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of Locater led."
        ::= { cmmSysComponentStatusEntry 5 }

        cmmSysLocatorLedColor    OBJECT-TYPE
        SYNTAX        LedColorCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the Color of the Locator led."
        ::= { cmmSysComponentStatusEntry 6 }

        cmmSysMasterLedStatus    OBJECT-TYPE
        SYNTAX        INTEGER {
                              notpresent(1),
                              on(2),
                              off(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of Master led."
        ::= { cmmSysComponentStatusEntry 7 }

        cmmSysMasterLedColor    OBJECT-TYPE
        SYNTAX        LedColorCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the Color of the master led."
        ::= { cmmSysComponentStatusEntry 8 }

        cmmSysFanStatus    OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1),
                              minor-fault(2),
                              major-fault(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Overall Status of Fans in the Unit."
        ::= { cmmSysComponentStatusEntry 9 }

        cmmSysFrontFanLedColor    OBJECT-TYPE
        SYNTAX        LedColorCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the Color of the Front Fan led."
        ::= { cmmSysComponentStatusEntry 10 }

        cmmSysRamStatus  OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1),
                              minor-fault(2),
                              major-fault(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of RAM in the unit."
        ::= { cmmSysComponentStatusEntry 11 }

        cmmSysCpuStatus  OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1),
                              minor-fault(2),
                              major-fault(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of CPU in the unit."
        ::= { cmmSysComponentStatusEntry 12 }

        cmmSysDiskStatus  OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1),
                              minor-fault(2),
                              major-fault(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of Disk in the unit."
        ::= { cmmSysComponentStatusEntry 13 }

        cmmSysTemperatureStatus  OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1),
                              minor-fault(2),
                              major-fault(3),
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Status of Temperature sensors in the unit."
        ::= { cmmSysComponentStatusEntry 14 }

    -- ## Software Module Table

       cmmSysSwModuleTable    OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSysSwModuleEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "Software module information"
        ::= { cmmSysObjects 12 }

      cmmSysSwModuleEntry    OBJECT-TYPE
        SYNTAX        CmmSysSwModuleEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "Software module information"
        INDEX    { cmmStackUnitIndex }
        ::= { cmmSysSwModuleTable 1 }

      CmmSysSwModuleEntry    ::=
        SEQUENCE {
           cmmSysSwRuntimeImgVersion     DisplayString,
           cmmSysSwRuntimeImgDate        DateAndTime
        }

       cmmSysSwRuntimeImgVersion    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "This variable indicates the software
            module version that is currently
            running on the unit.
            The software release version is in
            the format as follow:
            <major version>.<minor version>.<minor
            version>......
            '1.2' indicate major version of 1 and
            minor release of 2.
            Blank indicates unavailable"
        ::= { cmmSysSwModuleEntry 1 }

       cmmSysSwRuntimeImgDate    OBJECT-TYPE
        SYNTAX        DateAndTime
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "The release date of this software module.
             Blank indicates unavailable"
        ::= { cmmSysSwModuleEntry 2 }

-- ## Switching Chip Temperature Table
        cmmSwitchTemperatureTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSwitchTemperatureEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A temperature entry containing objects related to particular temp monitor in switching chip"
        ::= { cmmSysObjects 13 }

      cmmSwitchTemperatureEntry  OBJECT-TYPE
        SYNTAX        CmmSwitchTemperatureEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A temperature entry containing objects related to particular temp monitor in switching chip"
        INDEX    { cmmStackUnitIndex,
                   cmmSwitchTemperatureSensorIndex }
        ::= { cmmSwitchTemperatureTable 1 }

      CmmSwitchTemperatureEntry    ::=
        SEQUENCE {
          cmmSwitchTemperatureSensorIndex  INTEGER,
          cmmSwitchTemperatureValue        INTEGER,
          cmmSwitchTempPeakValue           INTEGER
        }

        cmmSwitchTemperatureSensorIndex    OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature Monitor Index inside chip."
        ::= { cmmSwitchTemperatureEntry 1 }

        cmmSwitchTemperatureValue    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature monitor value.
            '-100001' indicates unavailable"
        ::= { cmmSwitchTemperatureEntry 2 }

        cmmSwitchTempPeakValue    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents Temperature monitor peak value since last reading.
            '-100001' indicates unavailable"
        ::= { cmmSwitchTemperatureEntry 3 }

-- ## Hard disk Table
        cmmSysHardDiskTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSysHardDiskEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing Hard disk information of the Unit."
        ::= { cmmSysObjects 14 }

      cmmSysHardDiskEntry  OBJECT-TYPE
        SYNTAX        CmmSysHardDiskEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing Hard disk information of the Unit."
        INDEX    { cmmStackUnitIndex }
        ::= { cmmSysHardDiskTable 1 }

      CmmSysHardDiskEntry    ::=
        SEQUENCE {
          cmmSysHarddiskSerialno                                 DisplayString,
          cmmSysHarddiskModelno                                  DisplayString,
          cmmSysHarddiskFirmwarerev                              DisplayString,
          cmmSysHarddiskCylinders                                INTEGER,
          cmmSysHarddiskHeads                                    INTEGER,
          cmmSysHarddiskSectors                                  INTEGER,
          cmmSysHarddiskUnformattedBytesorTrack                  INTEGER,
          cmmSysHarddiskUnformattedBytesorSector                 INTEGER,
          cmmSysHarddiskRevisionNum                              DisplayString,
          cmmSysHarddiskTotalsize                                INTEGER,
          cmmSysHarddiskUsedMem                                  INTEGER,
          cmmSysHarddiskFreeMem                                  INTEGER,
          cmmSysHarddiskUsageAlertThreshold                      INTEGER,
          cmmSysHarddiskUsageCriticalThreshold                   INTEGER,
          cmmSysHardDiskRemainLifeAlertThreshold                 INTEGER,
          cmmSysHardDiskRemainLifeCriticalThreshold              INTEGER,
          cmmSysHardDiskRemainLife                               INTEGER,
          cmmSysHardDiskAvailableReservedSpaceAlertThreshold     INTEGER,
          cmmSysHardDiskAvailableReservedSpaceCriticalThreshold  INTEGER,
          cmmSysHardDiskAvailableReservedSpace                   INTEGER,
          cmmSysHardDiskReallocSectorsCount                      INTEGER,
          cmmSysHardDiskUncorrectSectorCount                     INTEGER,
          cmmSysHardDiskActivityMonitoringInterval               INTEGER,
          cmmSysHardDiskActivityMonitoringRead                   INTEGER,
          cmmSysHardDiskActivityMonitoringWrite                  INTEGER,
          cmmSysHardDiskActivityMonitoringReadCurrent            INTEGER,
          cmmSysHardDiskActivityMonitoringWriteCurrent           INTEGER,
          cmmSysHardDiskActivityMonitoringReadThreshold          INTEGER,
          cmmSysHardDiskActivityMonitoringWriteThreshold         INTEGER,
          cmmSysHardDiskManufacturerId                           DisplayString,
          cmmSysHardDiskManufactureDate                          DisplayString,
          cmmSysHardDiskDeviceType                               DisplayString,
          cmmSysHardDiskCacheSize                                INTEGER,
          cmmSysHardDiskStorageStatus                            INTEGER
        }

        cmmSysHarddiskSerialno    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's serial number.
             Blank indicates unavailable"
        ::= { cmmSysHardDiskEntry 1 }

        cmmSysHarddiskModelno    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's model number.
             Blank indicates unavailable"
        ::= { cmmSysHardDiskEntry 2 }

        cmmSysHarddiskFirmwarerev    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's firmware revision.
             Blank indicates unavailable"
        ::= { cmmSysHardDiskEntry 3 }

        cmmSysHarddiskCylinders    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's Cylinder unit.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 4 }

        cmmSysHarddiskHeads    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's Head unit.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 5 }

        cmmSysHarddiskSectors    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's Sector unit.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 6 }

        cmmSysHarddiskUnformattedBytesorTrack    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's unformated bytes or track.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 7 }

        cmmSysHarddiskUnformattedBytesorSector    OBJECT-TYPE
        SYNTAX        INTEGER
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's unformated bytes or sectors.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 8 }

        cmmSysHarddiskRevisionNum    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's Revision number.
             Blank indicates unavailable"
        ::= { cmmSysHardDiskEntry 9 }

        cmmSysHarddiskTotalsize    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " MBytes "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Total memory of the Hard disk.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 10 }

        cmmSysHarddiskUsedMem  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        deprecated
        DESCRIPTION
            "Used Memory in the Hard disk.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 11 }

        cmmSysHarddiskFreeMem  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        deprecated
        DESCRIPTION
            "Free Memory in the Hard disk.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 12 }

        cmmSysHarddiskUsageAlertThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk usage threshold %  for Alert Level.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 13 }

        cmmSysHarddiskUsageCriticalThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk usage threshold %  for Critical Level.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 14 }

        cmmSysHardDiskRemainLifeAlertThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk Remain Life threshold % for Alert Level.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 15 }

        cmmSysHardDiskRemainLifeCriticalThreshold     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk Remain Life threshold % for Critical Level.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 16 }

        cmmSysHardDiskRemainLife                   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk Remain life %.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 17 }

        cmmSysHardDiskAvailableReservedSpaceAlertThreshold     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk Available Reserved Space threshold % for alert Level.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 18 }

        cmmSysHardDiskAvailableReservedSpaceCriticalThreshold     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk Available Reserved Space threshold % for Critical Level.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 19 }

        cmmSysHardDiskAvailableReservedSpace     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " % "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk Available Reserved Space %.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 20 }

        cmmSysHardDiskReallocSectorsCount                  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " 1 "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Harddisk Reallocated Sectors Count.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 21 }

        cmmSysHardDiskUncorrectSectorCount                   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " 1 "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk Uncorrectable Sectors Count.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 22 }

        cmmSysHardDiskActivityMonitoringInterval  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " Seconds "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk activity monitoring interval.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 23 }

        cmmSysHardDiskActivityMonitoringRead  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " kBps "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk activity monitoring average read.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 24 }

        cmmSysHardDiskActivityMonitoringWrite  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " kBps "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk activity monitoring average write.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 25 }

        cmmSysHardDiskActivityMonitoringReadCurrent  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " kBps "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk activity monitoring current read.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 26 }

        cmmSysHardDiskActivityMonitoringWriteCurrent  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " kB/s "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk activity monitoring current write.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 27 }

        cmmSysHardDiskActivityMonitoringReadThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " kBps "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk activity monitoring read threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 28 }

        cmmSysHardDiskActivityMonitoringWriteThreshold  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " kBps "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Harddisk activity monitoring write threshold.
             '-100001' indicates unavailable"
        ::= { cmmSysHardDiskEntry 29 }

        cmmSysHardDiskManufacturerId  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's manufacturer identification
            defined by JEDEC."
        ::= { cmmSysHardDiskEntry 30 }

        cmmSysHardDiskManufactureDate  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's manufacture date."
        ::= { cmmSysHardDiskEntry 31 }

        cmmSysHardDiskDeviceType  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's type. Removable or Embedded."
        ::= { cmmSysHardDiskEntry 32 }

        cmmSysHardDiskCacheSize    OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         " KBytes "
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk's cache memory size.
             '0' indicates unavailable"
        ::= { cmmSysHardDiskEntry 33 }

        cmmSysHardDiskStorageStatus    OBJECT-TYPE
        SYNTAX        INTEGER {
                            normal(1),
                            critical(2),
                            alert(3),
                            undefined(4),
                            unavailable(-100001),
                            not-applicable(-100002)
                            }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Hard disk storage status (EOL).
            '-100001' indicates unavailable,
            '-100002' indicates not-applicable."
        ::= { cmmSysHardDiskEntry 34 }

-- ## System Overall Status Table
        cmmSystemStatusTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSystemStatusEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A Table containing overall system status of the Unit."
        ::= { cmmSysObjects 15 }

      cmmSystemStatusEntry  OBJECT-TYPE
        SYNTAX        CmmSystemStatusEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing component status and overall system status of the Unit."
        INDEX    { cmmStackUnitIndex }
        ::= { cmmSystemStatusTable 1 }

      CmmSystemStatusEntry    ::=
        SEQUENCE {
               cmmSystemMinorFaultStatus SystemStatusCode,
               cmmSystemMajorFaultStatus SystemStatusCode,
               cmmSysStatus              INTEGER,
               cmmSysLedColor            LedColorCode
                }

        cmmSystemMinorFaultStatus OBJECT-TYPE
        SYNTAX        SystemStatusCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "overall Critical status caused by individual components in the unit."
        ::= { cmmSystemStatusEntry 1 }

        cmmSystemMajorFaultStatus OBJECT-TYPE
        SYNTAX        SystemStatusCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "overall Alert status caused by individual components in the unit."
        ::= { cmmSystemStatusEntry 2 }

        cmmSysStatus    OBJECT-TYPE
        SYNTAX        INTEGER {
                              normal(1), -- self diagnostics test is success
                              minor-fault(2), -- minor system fault
                              major-fault(3), -- major system fault
                              unknown(4)
                              }
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Overall Status/Health of System."
        ::= { cmmSystemStatusEntry 3 }

        cmmSysLedColor    OBJECT-TYPE
        SYNTAX        LedColorCode
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Value represents the Color of the system led."
        ::= { cmmSystemStatusEntry 4 }

-- CPU per core utilisation table

        cmmCpuCoreUtilTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmCpuCoreUtilEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing cpu utilisation information."
        ::= { cmmSysObjects 16 }

      cmmCpuCoreUtilEntry  OBJECT-TYPE
        SYNTAX        CmmCpuCoreUtilEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing cpu utilisation information."
        INDEX    { cmmStackUnitIndex, cmmCpuCoreIndex }
        ::= { cmmCpuCoreUtilTable 1 }

      CmmCpuCoreUtilEntry ::=
        SEQUENCE {
               cmmCpuCoreIndex                        INTEGER,
               cmmCpuCoreUtilization                  INTEGER,
               cmmCpuCoreModelName                    DisplayString
                }

        cmmCpuCoreIndex  OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "Cpu Core number."
        ::= { cmmCpuCoreUtilEntry 1 }

        cmmCpuCoreUtilization   OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.01 %"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Cpu Utilization per core."
        ::= { cmmCpuCoreUtilEntry 2 }

        cmmCpuCoreModelName  OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Displays CPU processor Model Name.
              Blank indicates unavailable"
        ::= { cmmCpuCoreUtilEntry 3 }

    -- ### Psu Fru Table

   cmmPsuFruTable    OBJECT-TYPE
     SYNTAX          SEQUENCE OF CmmPsuFruEntry
     MAX-ACCESS      not-accessible
     STATUS          current
     DESCRIPTION
           "A psu fru entry containing objects
            for PSU inventory information"
     ::= { cmmSysObjects 17 }

     cmmPsuFruEntry    OBJECT-TYPE
       SYNTAX          CmmPsuFruEntry
       MAX-ACCESS      not-accessible
       STATUS          current
       DESCRIPTION
           "A psu fru entry containing objects for a
           particular power supply Unit."
       INDEX    { cmmStackUnitIndex, cmmSysPSUIndex }
       ::= { cmmPsuFruTable 1 }

     CmmPsuFruEntry    ::=
       SEQUENCE {
          cmmPsuPpid                   DisplayString,
          cmmPsuCountryofOrigin        DisplayString,
          cmmPsuPpidPartNum            DisplayString,
          cmmPsuPpidPartNumRev         DisplayString,
          cmmPsuManufactureId          DisplayString,
          cmmPsuDateCode               OCTET STRING,
          cmmPsuSerialNumber           DisplayString,
          cmmPsuPartNum                DisplayString,
          cmmPsuPartNumRev             DisplayString,
          cmmPsuNumOfFanPerTray        INTEGER,
          cmmPsuType                   INTEGER,
          cmmPsuServiceTag             DisplayString,
          cmmPsuIanaNum                DisplayString,
          cmmPsuFanMaxRpm              INTEGER,
          cmmPsuAirFlowDir             DisplayString,
          cmmPsuMaxOutputWatt          INTEGER
       }

        cmmPsuPpid     OBJECT-TYPE
        SYNTAX         DisplayString
        MAX-ACCESS     read-only
        STATUS         current
        DESCRIPTION
            "The unique ppid of the power supply.
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 1 }

        cmmPsuCountryofOrigin     OBJECT-TYPE
        SYNTAX                    DisplayString
        MAX-ACCESS                read-only
        STATUS                    current
        DESCRIPTION
            "Country of Origin
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 2 }

        cmmPsuPpidPartNum     OBJECT-TYPE
        SYNTAX                DisplayString
        MAX-ACCESS            read-only
        STATUS                current
        DESCRIPTION
            "PPID Part Number
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 3 }

        cmmPsuPpidPartNumRev     OBJECT-TYPE
        SYNTAX                   DisplayString
        MAX-ACCESS               read-only
        STATUS                   current
        DESCRIPTION
            "PPID Part Number Rev
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 4 }

        cmmPsuManufactureId      OBJECT-TYPE
        SYNTAX                   DisplayString
        MAX-ACCESS               read-only
        STATUS                   current
        DESCRIPTION
            "Manufacturer ID
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 5 }

        cmmPsuDateCode     OBJECT-TYPE
        SYNTAX        OCTET STRING (SIZE (8 | 11))
        MAX-ACCESS         read-only
        STATUS             current
        DESCRIPTION
            "Date Code
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 6 }

        cmmPsuSerialNumber     OBJECT-TYPE
        SYNTAX                 DisplayString
        MAX-ACCESS             read-only
        STATUS                 current
        DESCRIPTION
            "Serial Number
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 7 }

        cmmPsuPartNum     OBJECT-TYPE
        SYNTAX            DisplayString
        MAX-ACCESS        read-only
        STATUS            current
        DESCRIPTION
            "Part Number
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 8 }

        cmmPsuPartNumRev     OBJECT-TYPE
        SYNTAX               DisplayString
        MAX-ACCESS           read-only
        STATUS               current
        DESCRIPTION
            "Part Number Revision
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 9 }

        cmmPsuNumOfFanPerTray  OBJECT-TYPE
        SYNTAX                 INTEGER
        MAX-ACCESS             read-only
        STATUS                 current
        DESCRIPTION
            "Number of Fans in the tray
            '-100001' indicates unavailable"
        ::= { cmmPsuFruEntry 10 }

        cmmPsuType     OBJECT-TYPE
        SYNTAX         INTEGER {
                           ac-normal(1),
                           ac-reverse(2),
                           dc-normal(3),
                           dc-reverse(4),
                           not-applicable(5)
                               }
        MAX-ACCESS     read-only
        STATUS         current
        DESCRIPTION
            "Type of psu"
        ::= { cmmPsuFruEntry 11 }

        cmmPsuServiceTag     OBJECT-TYPE
        SYNTAX               DisplayString
        MAX-ACCESS           read-only
        STATUS               current
        DESCRIPTION
            "Service Tag
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 12 }

        cmmPsuIanaNum     OBJECT-TYPE
        SYNTAX            DisplayString
        MAX-ACCESS        read-only
        STATUS            current
        DESCRIPTION
            "IANA Number
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 13 }

        cmmPsuFanMaxRpm     OBJECT-TYPE
        SYNTAX              INTEGER
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "Max RPM
            '-100001' indicates unavailable"
        ::= { cmmPsuFruEntry 14 }

        cmmPsuAirFlowDir     OBJECT-TYPE
        SYNTAX               DisplayString
        MAX-ACCESS           read-only
        STATUS               current
        DESCRIPTION
            "Airflow Direction
             Blank indicates unavailable"
        ::= { cmmPsuFruEntry 15 }

        cmmPsuMaxOutputWatt     OBJECT-TYPE
        SYNTAX                  INTEGER
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
            "Max Output Wattage
            '-100001' indicates unavailable"
        ::= { cmmPsuFruEntry 16 }

    -- ### Fan Fru Table

   cmmFanFruTable    OBJECT-TYPE
     SYNTAX          SEQUENCE OF CmmFanFruEntry
     MAX-ACCESS      not-accessible
     STATUS          current
     DESCRIPTION
           "A fan fru entry containing objects for a
           particular fantray."
     ::= { cmmSysObjects 18 }

     cmmFanFruEntry    OBJECT-TYPE
       SYNTAX          CmmFanFruEntry
       MAX-ACCESS      not-accessible
       STATUS          current
       DESCRIPTION
           "A fan fru entry containing objects for a
           particular fantray"
       INDEX    { cmmStackUnitIndex, cmmFanTrayNumber}
       ::= { cmmFanFruTable 1 }

     CmmFanFruEntry    ::=
       SEQUENCE {
          cmmFanPpid                   DisplayString,
          cmmFanCountryofOrigin        DisplayString,
          cmmFanPpidPartNum            DisplayString,
          cmmFanPpidPartNumRev         DisplayString,
          cmmFanManufactureId          DisplayString,
          cmmFanDateCode               DisplayString,
          cmmFanSerialNumber           DisplayString,
          cmmFanPartNum                DisplayString,
          cmmFanPartNumRev             DisplayString,
          cmmFanNumOfFanPerTray        INTEGER,
          cmmFanType                   INTEGER,
          cmmFanServiceTag             DisplayString,
          cmmFanIanaNum                DisplayString,
          cmmFanMaxRpm                 INTEGER
       }

        cmmFanPpid     OBJECT-TYPE
        SYNTAX         DisplayString
        MAX-ACCESS     read-only
        STATUS         current
        DESCRIPTION
            "The unique ppid of the fan tray
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 1 }

        cmmFanCountryofOrigin     OBJECT-TYPE
        SYNTAX                    DisplayString
        MAX-ACCESS                read-only
        STATUS                    current
        DESCRIPTION
            "Country of Origin
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 2 }

        cmmFanPpidPartNum     OBJECT-TYPE
        SYNTAX                DisplayString
        MAX-ACCESS            read-only
        STATUS                current
        DESCRIPTION
            "PPID Part Number
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 3 }

        cmmFanPpidPartNumRev     OBJECT-TYPE
        SYNTAX                   DisplayString
        MAX-ACCESS               read-only
        STATUS                   current
        DESCRIPTION
            "PPID Part Number Rev
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 4 }

        cmmFanManufactureId      OBJECT-TYPE
        SYNTAX                   DisplayString
        MAX-ACCESS               read-only
        STATUS                   current
        DESCRIPTION
            "Manufacturer ID
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 5 }

        cmmFanDateCode     OBJECT-TYPE
        SYNTAX             DisplayString
        MAX-ACCESS         read-only
        STATUS             current
        DESCRIPTION
            "Date Code
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 6 }

        cmmFanSerialNumber     OBJECT-TYPE
        SYNTAX                 DisplayString
        MAX-ACCESS             read-only
        STATUS                 current
        DESCRIPTION
            "Serial Number
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 7 }

        cmmFanPartNum     OBJECT-TYPE
        SYNTAX            DisplayString
        MAX-ACCESS        read-only
        STATUS            current
        DESCRIPTION
            "Part Number
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 8 }

        cmmFanPartNumRev     OBJECT-TYPE
        SYNTAX               DisplayString
        MAX-ACCESS           read-only
        STATUS               current
        DESCRIPTION
            "Part Number Revision
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 9 }

        cmmFanNumOfFanPerTray  OBJECT-TYPE
        SYNTAX                 INTEGER
        MAX-ACCESS             read-only
        STATUS                 current
        DESCRIPTION
            "Number of Fans in the tray
            '-100001' indicates unavailable"
        ::= { cmmFanFruEntry 10 }

        cmmFanType     OBJECT-TYPE
        SYNTAX         INTEGER {
                               blow-outfan(1),
                               blow-infan(2),
                               fan-type-unknown(3)
                               }
        MAX-ACCESS     read-only
        STATUS         current
        DESCRIPTION
            "Fan Type"
        ::= { cmmFanFruEntry 11 }

        cmmFanServiceTag     OBJECT-TYPE
        SYNTAX               DisplayString
        MAX-ACCESS           read-only
        STATUS               current
        DESCRIPTION
            "Service Tag
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 12 }

        cmmFanIanaNum     OBJECT-TYPE
        SYNTAX            DisplayString
        MAX-ACCESS        read-only
        STATUS            current
        DESCRIPTION
            "IANA Number
             Blank indicates unavailable"
        ::= { cmmFanFruEntry 13 }

        cmmFanMaxRpm     OBJECT-TYPE
        SYNTAX           INTEGER
        MAX-ACCESS       read-only
        STATUS           current
        DESCRIPTION
            "Max RPM
            '-100001' indicates unavailable"
        ::= { cmmFanFruEntry 14 }

-- ## Stack CPLD Table
        cmmSysCpldTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSysCpldEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A cpld entry containing objects related to CPLD versions"
        ::= { cmmSysObjects 19 }

      cmmSysCpldEntry  OBJECT-TYPE
        SYNTAX        CmmSysCpldEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A cpld entry containing objects related to CPLD versions"
        INDEX    { cmmStackUnitIndex,
                   cmmSysCpldIndex }
        ::= { cmmSysCpldTable 1 }

      CmmSysCpldEntry    ::=
        SEQUENCE {
          cmmSysCpldIndex              INTEGER,
          cmmSysCpldName               DisplayString,
          cmmSysCpldSupportedVer       DisplayString,
          cmmSysCpldCurrentVer         DisplayString
        }

        cmmSysCpldIndex  OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
             "Value represents CPLD Index."
        ::= { cmmSysCpldEntry 1 }

        cmmSysCpldName    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "CPLD Name.
             Blank indicates unavailable"
        ::= { cmmSysCpldEntry 2 }

        cmmSysCpldSupportedVer    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "CPLD supported version on board.
             Blank indicates unavailable"
        ::= { cmmSysCpldEntry 3 }

        cmmSysCpldCurrentVer    OBJECT-TYPE
        SYNTAX        DisplayString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "CPLD current version on board.
             Blank indicates unavailable"
        ::= { cmmSysCpldEntry 4 }

    -- ## DDMPage20h21h Entry
     cmmTransDDMPage20h21hTable OBJECT-TYPE
     SYNTAX        SEQUENCE OF CmmTransDDMPage20h21hEntry
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
         "Information about Digital Optical Monitoring for page 20 and 21."
     ::= { cmmSysObjects 20 }

      cmmTransDDMPage20h21hEntry OBJECT-TYPE
        SYNTAX        CmmTransDDMPage20h21hEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "Information about Digital Optical Monitoring for page 20 and 21."
        INDEX    { cmmStackUnitIndex,
                   cmmTransIndex,
                   cmmTransChannelIndex
                 }
        ::= { cmmTransDDMPage20h21hTable 1 }

      CmmTransDDMPage20h21hEntry    ::=
        SEQUENCE {
           cmmTransPreFecBerVal                       ErrorFigures,
           cmmTransPreFecBerCriticMin                 ErrorFigures,
           cmmTransPreFecBerCriticMax                 ErrorFigures,
           cmmTransPreFecBerCriticalMin                  ErrorFigures,
           cmmTransPreFecBerCriticalMax                  ErrorFigures,
           cmmTransUncorrectedBerVal                  ErrorFigures,
           cmmTransUncorrectedBerValCriticMin         ErrorFigures,
           cmmTransUncorrectedBerValCriticMax         ErrorFigures,
           cmmTransUncorrectedBerValCriticalMin          ErrorFigures,
           cmmTransUncorrectedBerValCriticalMax          ErrorFigures,
           cmmTransSnrVal                             INTEGER,
           cmmTransSnrCriticMin                       INTEGER,
           cmmTransSnrCriticMax                       INTEGER,
           cmmTransSnrCriticalMin                        INTEGER,
           cmmTransSnrCriticalMax                        INTEGER,
           cmmTransResIsiVal                          INTEGER,
           cmmTransResIsiCriticMin                    INTEGER,
           cmmTransResIsiCriticMax                    INTEGER,
           cmmTransResIsiCriticalMin                     INTEGER,
           cmmTransResIsiCriticalMax                     INTEGER,
           cmmTransLvlTransVal                        INTEGER,
           cmmTransLvlTransCriticMin                  INTEGER,
           cmmTransLvlTransCriticMax                  INTEGER,
           cmmTransLvlTransCriticalMin                   INTEGER,
           cmmTransLvlTransCriticalMax                   INTEGER,
           cmmTransTecCurrErrVal                      INTEGER,
           cmmTransTecCurrErrCriticMin                INTEGER,
           cmmTransTecCurrErrCriticMax                INTEGER,
           cmmTransTecCurrErrCriticalMin                 INTEGER,
           cmmTransTecCurrErrCriticalMax                 INTEGER,
           cmmTransLaserTempVal                       INTEGER,
           cmmTransLaserTempCriticMin                 INTEGER,
           cmmTransLaserTempCriticMax                 INTEGER,
           cmmTransLaserTempCriticalMin                  INTEGER,
           cmmTransLaserTempCriticalMax                  INTEGER
         }

        cmmTransPreFecBerVal  OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Forward error correction bit error rate value.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 1 }

        cmmTransPreFecBerCriticMin  OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min alert value for pre-forward error correction ber.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 2 }

        cmmTransPreFecBerCriticMax     OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max alert value for pre-forward error correction ber
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 3}

        cmmTransPreFecBerCriticalMin  OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min critical value for pre-forward error correction ber.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 4 }

        cmmTransPreFecBerCriticalMax     OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max critical value for pre-forward error correction ber.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 5 }

        cmmTransUncorrectedBerVal     OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Frame error rate value.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 6 }

        cmmTransUncorrectedBerValCriticMin     OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min alert value for Frame error rate.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 7 }

        cmmTransUncorrectedBerValCriticMax  OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max alert value for Frame error rate.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 8 }

        cmmTransUncorrectedBerValCriticalMin     OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min critical value for Frame error rate.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 9}

        cmmTransUncorrectedBerValCriticalMax  OBJECT-TYPE
        SYNTAX        ErrorFigures
        UNITS         "BER"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max critical value for Frame error rate.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 10}

        cmmTransSnrVal  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Signal to noise ratio on ingress lane.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 11 }

        cmmTransSnrCriticMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min alert value for signal-to-noise ratio on ingress lane.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 12 }

        cmmTransSnrCriticMax     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Max alert value for signal-to-noise ratio on ingress lane.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 13 }

        cmmTransSnrCriticalMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min critical value for signal-to-noise ratio on ingress lane.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 14 }

        cmmTransSnrCriticalMax     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Max critical value for signal-to-noise ratio on ingress lane.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 15 }

        cmmTransResIsiVal     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 ps/nm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Residual-isi value.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 16 }

        cmmTransResIsiCriticMin     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 ps/nm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Min alert value for residual-isi.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 17 }

        cmmTransResIsiCriticMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 ps/nm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max alert value for residual-isi.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 18 }

        cmmTransResIsiCriticalMin     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 ps/nm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min critical value for residual-isi.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 19 }

        cmmTransResIsiCriticalMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 ps/nm"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max critical value for residual-isi.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 20 }

        cmmTransLvlTransVal  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Level transition value.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 21 }

        cmmTransLvlTransCriticMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min alert value for level transition.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 22 }

        cmmTransLvlTransCriticMax     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max alert value for level transition.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 23 }

        cmmTransLvlTransCriticalMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min critical value for level transition.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 24 }

        cmmTransLvlTransCriticalMax     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 dB"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
             "Max critical value for level transition.
             '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 25 }

        cmmTransTecCurrErrVal     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Thermo-elecrical cooler current value.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 26 }

        cmmTransTecCurrErrCriticMin     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min alert value for thermo-elecrical cooler current.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 27 }

        cmmTransTecCurrErrCriticMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max alert value for thermo-elecrical cooler current.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 28 }

        cmmTransTecCurrErrCriticalMin     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min critical value for thermo-elecrical cooler current.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 29 }

        cmmTransTecCurrErrCriticalMax  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 mA"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max critical value for thermo-elecrical cooler current.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 30 }

        cmmTransLaserTempVal  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Laser Temparature value.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 31 }

        cmmTransLaserTempCriticMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min alert value for Laser Temperature.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 32 }

        cmmTransLaserTempCriticMax     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max alert value for Laser Temperature.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 33 }

        cmmTransLaserTempCriticalMin  OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Min critical value for Laser Temperature.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 34 }

        cmmTransLaserTempCriticalMax     OBJECT-TYPE
        SYNTAX        INTEGER
        UNITS         "0.001 C"
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Max critical value for Laser Temperature.
            '-100002' indicates not-applicable."
        ::= { cmmTransDDMPage20h21hEntry 35 }

-- ## Hard disk Partition Table
        cmmSysHardDiskPartitionTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF CmmSysHardDiskPartitionEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "An entry containing Hard disk information of the mounted partitions."
        ::= { cmmSysObjects 21 }

        cmmSysHardDiskPartitionEntry  OBJECT-TYPE
        SYNTAX        CmmSysHardDiskPartitionEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
           "An entry containing Hard disk mounted information of the mounted partitions."
        INDEX    { cmmStackUnitIndex, cmmSysHardDiskPartitionIndex }
        ::= { cmmSysHardDiskPartitionTable 1 }

        CmmSysHardDiskPartitionEntry    ::=
          SEQUENCE {
            cmmSysHardDiskPartitionIndex                INTEGER,
            cmmSysHardDiskPartitionName                 DisplayString,
            cmmSysHardDiskPartitionTotalMem             INTEGER,
            cmmSysHardDiskPartitionUsedMem              INTEGER,
            cmmSysHardDiskPartitionFreeMem              INTEGER
          }

        cmmSysHardDiskPartitionIndex OBJECT-TYPE
        SYNTAX        INTEGER (1..65535)
        MAX-ACCESS                read-only
        STATUS                    current
        DESCRIPTION
            "The mounted partition index."
        ::= { cmmSysHardDiskPartitionEntry 1 }

        cmmSysHardDiskPartitionName OBJECT-TYPE
        SYNTAX                    DisplayString
        MAX-ACCESS                read-only
        STATUS                    current
        DESCRIPTION
            "The mounted partition name.
             Blank indicates unavailable"
        ::= { cmmSysHardDiskPartitionEntry 2 }

        cmmSysHardDiskPartitionTotalMem OBJECT-TYPE
        SYNTAX                    INTEGER
        UNITS                     " MBytes "
        MAX-ACCESS                read-only
        STATUS                    current
        DESCRIPTION
            "The mounted partition size.
            '-100001' indicates unavailable"
        ::= { cmmSysHardDiskPartitionEntry 3 }

        cmmSysHardDiskPartitionUsedMem  OBJECT-TYPE
        SYNTAX                    INTEGER
        UNITS                     " MBytes "
        MAX-ACCESS                read-only
        STATUS                    current
        DESCRIPTION
            "The mounted partition used space.
            '-100001' indicates unavailable"
        ::= { cmmSysHardDiskPartitionEntry 4 }

        cmmSysHardDiskPartitionFreeMem   OBJECT-TYPE
        SYNTAX                   INTEGER
        UNITS                    " MBytes "
        MAX-ACCESS               read-only
        STATUS                   current
        DESCRIPTION
            "The mounted partition free space.
            '-100001' indicates unavailable"
        ::= { cmmSysHardDiskPartitionEntry 5 }


    --
    -- ## Notifications
    --

    cmmAlarmVariable             OBJECT IDENTIFIER ::= { cmmAlarmObjects 0 }
    cmmAlarmMibNotifications     OBJECT IDENTIFIER ::= { cmmAlarmObjects 1 }
    cmmTransMibNotifications     OBJECT IDENTIFIER ::= { cmmAlarmObjects 2 }

    cmmAlarmVarInteger OBJECT-TYPE
    SYNTAX       INTEGER
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "An generic INTEGER value in the TRAP object"
    ::= { cmmAlarmVariable 1 }

    cmmAlarmVarString OBJECT-TYPE
    SYNTAX       OCTET STRING
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "An generic string value in the TRAP object"
    ::= { cmmAlarmVariable 2 }

    --
    -- SYSTEM RELATED TRAPS
    --

    cmmCpuLoad15MinAlert  NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad15minAlertThreshold,
                     cmmStackUnitCpuLoad15Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for 15min alert threshold and
             CPU Utilisation"
        ::= { cmmAlarmMibNotifications 1 }

    cmmCpuLoad5MinAlert  NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad5minAlertThreshold,
                     cmmStackUnitCpuLoad5Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for 5min alert threshold and
             CPU Utilisation"
        ::= { cmmAlarmMibNotifications 2 }

    cmmCpuLoad1MinCritical  NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad1minCriticalThreshold,
                     cmmStackUnitCpuLoad1Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for 1min critical level threshold and
             CPU Utilisation"
        ::= { cmmAlarmMibNotifications 3 }

    cmmCpuLoad1MinAlert  NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad1minAlertThreshold,
                     cmmStackUnitCpuLoad1Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for 1min alert level threshold and
             CPU Utilisation"
        ::= { cmmAlarmMibNotifications 4 }

    cmmCpuLoad1MinCriticalRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad1minCriticalThreshold,
                     cmmStackUnitCpuLoad1Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU usage recovered from Critical Condition"
        ::= { cmmAlarmMibNotifications 5 }

    cmmCpuLoad15MinAlertRecovery NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad15minAlertThreshold,
                     cmmStackUnitCpuLoad15Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU usage recovered from 15 min Alert Condition"
        ::= { cmmAlarmMibNotifications 6 }

    cmmCpuLoad5MinAlertRecovery NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad5minAlertThreshold,
                     cmmStackUnitCpuLoad5Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU usage recovered from 5 min Alert Condition"
        ::= { cmmAlarmMibNotifications 7 }

    cmmCpuLoad1MinAlertRecovery NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackCpuLoad1minAlertThreshold,
                     cmmStackUnitCpuLoad1Min
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU usage recovered from 15 min Alert Condition"
        ::= { cmmAlarmMibNotifications 8 }

    cmmCpuCoreUtilHighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackUnitCpuUtilCriticalThreshold,
                     cmmStackUnitCpuUtilization
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU utilization in critical condition."
        ::= { cmmAlarmMibNotifications 9 }

    cmmCpuCoreUtilHighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackUnitCpuUtilAlertThreshold,
                     cmmStackUnitCpuUtilization
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU utilization in alert condition."
        ::= { cmmAlarmMibNotifications 10 }

    cmmCpuCoreUtilHighCriticalRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackUnitCpuUtilization
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU utilization after critical recovery."
        ::= { cmmAlarmMibNotifications 11 }

    cmmCpuCoreUtilHighAlertRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmStackUnitCpuUtilization
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send information for CPU utilization after alert recovery."
        ::= { cmmAlarmMibNotifications 12 }

    cmmRamUsageRisingCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysRamUsedMem,
                      cmmSysRamCriticalThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when RAM usage exceeds Critical Threshold"
        ::= { cmmAlarmMibNotifications 21 }

    cmmRamUsageRisingAlert   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysRamUsedMem,
                      cmmSysRamAlertThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when RAM usage exceeds Alert Threshold"
        ::= { cmmAlarmMibNotifications 22 }

    cmmRamUsageCriticalRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysRamUsedMem,
                      cmmSysRamCriticalThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when RAM usage recovers from Critical Condition"
        ::= { cmmAlarmMibNotifications 23 }

    cmmRamUsageAlertRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysRamUsedMem,
                      cmmSysRamAlertThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when RAM usage recovers under Alert threshold."
        ::= { cmmAlarmMibNotifications 24 }

    cmmHardDiskUsageRisingCritical  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHarddiskUsedMem,
                      cmmSysHarddiskUsageCriticalThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk usage exceeds critical threshold."
        ::= { cmmAlarmMibNotifications 25 }

    cmmHardDiskUsageRisingAlert  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHarddiskUsedMem,
                      cmmSysHarddiskUsageAlertThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk usage exceeds alert threshold."
        ::= { cmmAlarmMibNotifications 26 }

    cmmHardDiskUsageCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHarddiskUsedMem,
                      cmmSysHarddiskUsageCriticalThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk usage recovers under critical threshold."
        ::= { cmmAlarmMibNotifications 27 }

    cmmHardDiskUsageAlertRecovery  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHarddiskUsedMem,
                      cmmSysHarddiskUsageAlertThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk usage recovers under alert threshold."
        ::= { cmmAlarmMibNotifications 28 }

     cmmSysHardDiskRemainLifeRisingAlert  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHardDiskRemainLife,
                      cmmSysHardDiskRemainLifeAlertThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk Remain life reached below the Alert threshold."
        ::= { cmmAlarmMibNotifications 29 }

      cmmSysHardDiskRemainLifeRisingCritical  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHardDiskRemainLife,
                      cmmSysHardDiskRemainLifeCriticalThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk Remain life reached below the Critical threshold."
        ::= { cmmAlarmMibNotifications 30 }

     cmmSysHardDiskAvailableReservedSpaceAlert  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHardDiskAvailableReservedSpace,
                      cmmSysHardDiskAvailableReservedSpaceAlertThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk Available Reserved Space reached below the Alert threshold."
        ::= { cmmAlarmMibNotifications 31 }

     cmmSysHardDiskAvailableReservedSpaceCritical  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHardDiskAvailableReservedSpace,
                      cmmSysHardDiskAvailableReservedSpaceCriticalThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk Available Reserved Space reached below the Critical threshold."
        ::= { cmmAlarmMibNotifications 32 }

    cmmSysHardDiskReallocSectorsCountAlert  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHardDiskReallocSectorsCount
                      }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk Realloced sector count increments by 10."
        ::= { cmmAlarmMibNotifications 33 }

    cmmSysHardDiskUncorrectableSectorCountCritical  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHardDiskUncorrectSectorCount
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk Uncorrectable sector count increments by 1."
        ::= { cmmAlarmMibNotifications 34 }

    cmmSysHardDiskActivityMonitoringReadAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysHardDiskActivityMonitoringRead,
                      cmmSysHardDiskActivityMonitoringReadThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk activity violates read threshold."
        ::= { cmmAlarmMibNotifications 35 }

    cmmSysHardDiskActivityMonitoringReadRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysHardDiskActivityMonitoringRead,
                      cmmSysHardDiskActivityMonitoringReadThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk read activity violation recovers."
        ::= { cmmAlarmMibNotifications 36 }

    cmmSysHardDiskActivityMonitoringWriteAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysHardDiskActivityMonitoringWrite,
                      cmmSysHardDiskActivityMonitoringWriteThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk activity violates write threshold."
        ::= { cmmAlarmMibNotifications 37 }

    cmmSysHardDiskActivityMonitoringWriteRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysHardDiskActivityMonitoringWrite,
                      cmmSysHardDiskActivityMonitoringWriteThreshold
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk write activity violation recovers."
        ::= { cmmAlarmMibNotifications 38 }

     cmmSysHardDiskStorageStatusNotification  NOTIFICATION-TYPE
        OBJECTS       {
                      cmmStackUnitIndex,
                      cmmSysHardDiskStorageStatus
                     }
        STATUS       current
        DESCRIPTION
            "Trap to send when Hard Disk Storage Status (EOL) Change"
        ::= { cmmAlarmMibNotifications 39 }

    cmmTemperatureLowEmergency   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempEmergencyThresholdMin,
                      cmmSysTempEmergencyThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature will be below lower threshold for sensor"
        ::= { cmmAlarmMibNotifications 41 }

    cmmTemperatureHighEmergency   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempEmergencyThresholdMin,
                      cmmSysTempEmergencyThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature will be above threshold for sensor"
        ::= { cmmAlarmMibNotifications 42 }

    cmmTemperatureLowCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempCriticalThresholdMin,
                      cmmSysTempCriticalThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature is nearing low emergency condition for sensor"
        ::= { cmmAlarmMibNotifications 43 }

    cmmTemperatureHighCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempCriticalThresholdMin,
                      cmmSysTempCriticalThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature is nearing high emergency condition for sensor"
        ::= { cmmAlarmMibNotifications 44 }

    cmmTemperatureLowAlert   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempAlertThresholdMin,
                      cmmSysTempAlertThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature has reached low alert condition for sensor"
        ::= { cmmAlarmMibNotifications 45 }

    cmmTemperatureHighAlert   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempAlertThresholdMin,
                      cmmSysTempAlertThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature has reached high alert condition for sensor"
        ::= { cmmAlarmMibNotifications 46 }

    cmmTemperatureHighCriticalRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempCriticalThresholdMin,
                      cmmSysTempCriticalThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature is recovered to normal range from critical level high for sensor"
        ::= { cmmAlarmMibNotifications 47 }

    cmmTemperatureLowCriticalRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempCriticalThresholdMin,
                      cmmSysTempCriticalThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature is recovered to normal range from critical level low for sensor"
        ::= { cmmAlarmMibNotifications 48 }

    cmmTemperatureHighAlertRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempAlertThresholdMin,
                      cmmSysTempAlertThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature is recovered to normal range from alert level high for sensor"
        ::= { cmmAlarmMibNotifications 49 }

    cmmTemperatureLowAlertRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysTemperatureSensorIndex,
                      cmmSysTemperatureValue,
                      cmmSysTempAlertThresholdMin,
                      cmmSysTempAlertThresholdMax,
                      cmmSysTemperatureSensorName
                     }
        STATUS       current
        DESCRIPTION
            "Set when Temperature is recovered to normal range from alert level low for sensor"
        ::= { cmmAlarmMibNotifications 50 }

    cmmPsuInsertedNotify   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPowerSupplyOperStatus,
                      cmmPsuSerialNumber
                     }
        STATUS       current
        DESCRIPTION
            "Set when Power supply unit is inserted"
        ::= { cmmAlarmMibNotifications 61 }

    cmmPsuRemovedCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPowerSupplyOperStatus,
                      cmmPsuSerialNumber
                     }
        STATUS       current
        DESCRIPTION
            "Set when Power supply unit is Removed"
        ::= { cmmAlarmMibNotifications 62 }

    cmmPsuAcFailedCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex
                     }
        STATUS       current
        DESCRIPTION
            "Set when Power supply unit has no input power"
        ::= { cmmAlarmMibNotifications 63 }

    cmmPsuAcRecover  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex
                     }
        STATUS       current
        DESCRIPTION
            "Set when Power supply unit input is restored"
        ::= { cmmAlarmMibNotifications 64 }

    cmmPsu12vPgFailedCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex
                     }
        STATUS       current
        DESCRIPTION
            "Set when Power supply unit has no output power"
        ::= { cmmAlarmMibNotifications 65 }

    cmmPsu12vPgRecover  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex
                     }
        STATUS       current
        DESCRIPTION
            "Set when Power supply unit has restored output"
        ::= { cmmAlarmMibNotifications 66 }

    cmmSysPSUInputVoltageHighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageAlertThresholdMin,
                      cmmSysInputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input voltage reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 67 }

    cmmSysPSUInputVoltageLowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageAlertThresholdMin,
                      cmmSysInputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input voltage reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 68 }

    cmmSysPSUInputVoltageHighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageCriticalThresholdMin,
                      cmmSysInputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input voltage reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 69 }

    cmmSysPSUInputVoltageLowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageCriticalThresholdMin,
                      cmmSysInputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 70 }

    cmmSysPSUInputVoltageHighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageAlertThresholdMin,
                      cmmSysInputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input voltage is recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 71 }

    cmmSysPSUInputVoltageLowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageAlertThresholdMin,
                      cmmSysInputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input voltage is recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 72 }

    cmmSysPSUInputVoltageHighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageCriticalThresholdMin,
                      cmmSysInputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input voltage is recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 73 }

    cmmSysPSUInputVoltageLowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputVoltage,
                      cmmSysInputVoltageCriticalThresholdMin,
                      cmmSysInputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power is recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 74 }

    cmmSysPSUOutputVoltageHighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageAlertThresholdMin,
                      cmmSysOutputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 75 }

    cmmSysPSUOutputVoltageLowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageAlertThresholdMin,
                      cmmSysOutputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 76 }

    cmmSysPSUOutputVoltageHighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageCriticalThresholdMin,
                      cmmSysOutputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 77 }

    cmmSysPSUOutputVoltageLowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageCriticalThresholdMin,
                      cmmSysOutputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 78 }

    cmmSysPSUOutputVoltageHighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageAlertThresholdMin,
                      cmmSysOutputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 79 }

    cmmSysPSUOutputVoltageLowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageAlertThresholdMin,
                      cmmSysOutputVoltageAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 80 }

    cmmSysPSUOutputVoltageHighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageCriticalThresholdMin,
                      cmmSysOutputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 81 }

    cmmSysPSUOutputVoltageLowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputVoltage,
                      cmmSysOutputVoltageCriticalThresholdMin,
                      cmmSysOutputVoltageCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output voltage recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 82 }

    cmmSysPSUInputPowerHighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerAlertThresholdMin,
                      cmmSysInputPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 83 }

    cmmSysPSUInputPowerLowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerAlertThresholdMin,
                      cmmSysInputPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 84 }

    cmmSysPSUInputPowerHighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerCriticalThresholdMin,
                      cmmSysInputPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 85 }

    cmmSysPSUInputPowerLowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerCriticalThresholdMin,
                      cmmSysInputPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 86 }

    cmmSysPSUInputPowerHighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerAlertThresholdMin,
                      cmmSysInputPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 87 }

    cmmSysPSUInputPowerLowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerAlertThresholdMin,
                      cmmSysInputPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 88 }

    cmmSysPSUInputPowerHighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerCriticalThresholdMin,
                      cmmSysInputPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 89 }

    cmmSysPSUInputPowerLowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputPower,
                      cmmSysInputPowerCriticalThresholdMin,
                      cmmSysInputPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input power recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 90 }

    cmmSysPSUOutputPowerHighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSConsumption,
                      cmmSysPSConsumptionAlertThresholdMin,
                      cmmSysPSConsumptionAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output power reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 91 }

    cmmSysPSUOutputPowerLowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSConsumption,
                      cmmSysPSConsumptionAlertThresholdMin,
                      cmmSysPSConsumptionAlertThresholdMax
                     }
        STATUS       current
          DESCRIPTION
          "Set when PSU output power reached low alert threshold value."
          ::= { cmmAlarmMibNotifications 92 }

    cmmSysPSUOutputPowerHighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSConsumption,
                      cmmSysPSConsumptionCriticalThresholdMin,
                      cmmSysPSConsumptionCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output power reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 93 }

    cmmSysPSUOutputPowerLowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSConsumption,
                      cmmSysPSConsumptionCriticalThresholdMin,
                      cmmSysPSConsumptionCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output power reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 94 }

    cmmSysPSUOutputPowerHighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSConsumption,
                      cmmSysPSConsumptionAlertThresholdMin,
                      cmmSysPSConsumptionAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output power recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 95 }

    cmmSysPSUOutputPowerLowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSConsumption,
                      cmmSysPSConsumptionAlertThresholdMin,
                      cmmSysPSConsumptionAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output power recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 96 }

    cmmSysPSUOutputPowerHighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSConsumption,
                      cmmSysPSConsumptionCriticalThresholdMin,
                      cmmSysPSConsumptionCriticalThresholdMax
                     }
        STATUS       current
          DESCRIPTION
          "Set when PSU output power recovered from high critical threshold."
          ::= { cmmAlarmMibNotifications 97 }

    cmmSysPSUOutputPowerLowCriticalRecovery  NOTIFICATION-TYPE
          OBJECTS      {
                        cmmStackUnitIndex,
                        cmmSysPSUIndex,
                        cmmSysPSConsumption,
                        cmmSysPSConsumptionCriticalThresholdMin,
                        cmmSysPSConsumptionCriticalThresholdMax
                       }
        STATUS       current
        DESCRIPTION
            "Set when PSU output power recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 98 }

    cmmSysPSUInputCurrentHighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputCurrent,
                      cmmSysInputCurrentAlertThresholdMin,
                      cmmSysInputCurrentAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 99 }

    cmmSysPSUInputCurrentLowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysInputCurrent,
                       cmmSysInputCurrentAlertThresholdMin,
                       cmmSysInputCurrentAlertThresholdMax
                      }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 100 }

    cmmSysPSUInputCurrentHighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputCurrent,
                      cmmSysInputCurrentCriticalThresholdMin,
                      cmmSysInputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 101 }

    cmmSysPSUInputCurrentLowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputCurrent,
                      cmmSysInputCurrentCriticalThresholdMin,
                      cmmSysInputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 102 }

    cmmSysPSUInputCurrentHighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputCurrent,
                      cmmSysInputCurrentAlertThresholdMin,
                      cmmSysInputCurrentAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 103 }

    cmmSysPSUInputCurrentLowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputCurrent,
                      cmmSysInputCurrentAlertThresholdMin,
                      cmmSysInputCurrentAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 104 }

    cmmSysPSUInputCurrentHighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputCurrent,
                      cmmSysInputCurrentCriticalThresholdMin,
                      cmmSysInputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 105 }

    cmmSysPSUInputCurrentLowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysInputCurrent,
                      cmmSysInputCurrentCriticalThresholdMin,
                      cmmSysInputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU input current recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 106 }

    cmmSysPSUOutputCurrentHighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentAlertThresholdMin,
                      cmmSysOutputCurrentAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 107 }

    cmmSysPSUOutputCurrentLowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentAlertThresholdMin,
                      cmmSysOutputCurrentAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 108 }

    cmmSysPSUOutputCurrentHighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentCriticalThresholdMin,
                      cmmSysOutputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 109 }

    cmmSysPSUOutputCurrentLowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentCriticalThresholdMin,
                      cmmSysOutputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 110 }

    cmmSysPSUOutputCurrentHighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentAlertThresholdMin,
                      cmmSysOutputCurrentAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 111 }

    cmmSysPSUOutputCurrentLowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentAlertThresholdMin,
                      cmmSysOutputCurrentAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 112 }

    cmmSysPSUOutputCurrentHighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentCriticalThresholdMin,
                      cmmSysOutputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 113 }

    cmmSysPSUOutputCurrentLowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysOutputCurrent,
                      cmmSysOutputCurrentCriticalThresholdMin,
                      cmmSysOutputCurrentCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU output current recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 114 }

    cmmSysPSUTemperature1HighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature1,
                      cmmSysPSTemperature1AlertThresholdMin,
                      cmmSysPSTemperature1AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 115 }

    cmmSysPSUTemperature1LowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature1,
                      cmmSysPSTemperature1AlertThresholdMin,
                      cmmSysPSTemperature1AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 116 }

    cmmSysPSUTemperature1HighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature1,
                      cmmSysPSTemperature1CriticalThresholdMin,
                      cmmSysPSTemperature1CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 117 }

    cmmSysPSUTemperature1LowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature1,
                      cmmSysPSTemperature1CriticalThresholdMin,
                      cmmSysPSTemperature1CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 118 }

    cmmSysPSUTemperature1HighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature1,
                      cmmSysPSTemperature1AlertThresholdMin,
                      cmmSysPSTemperature1AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 119 }

    cmmSysPSUTemperature1LowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature1,
                      cmmSysPSTemperature1AlertThresholdMin,
                      cmmSysPSTemperature1AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 120 }

    cmmSysPSUTemperature1HighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                     cmmStackUnitIndex,
                     cmmSysPSUIndex,
                     cmmSysPSTemperature1,
                     cmmSysPSTemperature1CriticalThresholdMin,
                     cmmSysPSTemperature1CriticalThresholdMax
                    }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 121 }

    cmmSysPSUTemperature1LowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature1,
                      cmmSysPSTemperature1CriticalThresholdMin,
                      cmmSysPSTemperature1CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature1 recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 122 }

    cmmSysPSUTemperature2HighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2AlertThresholdMin,
                      cmmSysPSTemperature2AlertThresholdMax
                  }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 123 }

    cmmSysPSUTemperature2LowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2AlertThresholdMin,
                      cmmSysPSTemperature2AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 124 }

    cmmSysPSUTemperature2HighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2CriticalThresholdMin,
                      cmmSysPSTemperature2CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 125 }

    cmmSysPSUTemperature2LowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2CriticalThresholdMin,
                      cmmSysPSTemperature2CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 126 }

    cmmSysPSUTemperature2HighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2AlertThresholdMin,
                      cmmSysPSTemperature2AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 127 }

    cmmSysPSUTemperature2LowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2AlertThresholdMin,
                      cmmSysPSTemperature2AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 128 }

    cmmSysPSUTemperature2HighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2CriticalThresholdMin,
                      cmmSysPSTemperature2CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 129 }

    cmmSysPSUTemperature2LowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmSysPSUIndex,
                      cmmSysPSTemperature2,
                      cmmSysPSTemperature2CriticalThresholdMin,
                      cmmSysPSTemperature2CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature2 recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 130 }

    cmmSysPSUTemperature3HighAlert  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3AlertThresholdMin,
                       cmmSysPSTemperature3AlertThresholdMax
                   }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperaturer3 reached high alert threshold value."
        ::= { cmmAlarmMibNotifications 131 }

    cmmSysPSUTemperature3LowAlert  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3AlertThresholdMin,
                       cmmSysPSTemperature3AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature3 reached low alert threshold value."
        ::= { cmmAlarmMibNotifications 132 }

    cmmSysPSUTemperature3HighCritical  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3CriticalThresholdMin,
                       cmmSysPSTemperature3CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature3 reached high critical threshold value."
        ::= { cmmAlarmMibNotifications 133 }

    cmmSysPSUTemperature3LowCritical  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3CriticalThresholdMin,
                       cmmSysPSTemperature3CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature3 reached low critical threshold value."
        ::= { cmmAlarmMibNotifications 134 }

    cmmSysPSUTemperature3HighAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3AlertThresholdMin,
                       cmmSysPSTemperature3AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature3 recovered from high alert threshold."
        ::= { cmmAlarmMibNotifications 135 }

    cmmSysPSUTemperature3LowAlertRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3AlertThresholdMin,
                       cmmSysPSTemperature3AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature3 recovered from low alert threshold."
        ::= { cmmAlarmMibNotifications 136 }

    cmmSysPSUTemperature3HighCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3CriticalThresholdMin,
                       cmmSysPSTemperature3CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
        "Set when PSU temperature3 recovered from high critical threshold."
        ::= { cmmAlarmMibNotifications 137 }

    cmmSysPSUTemperature3LowCriticalRecovery  NOTIFICATION-TYPE
        OBJECTS      {
                       cmmStackUnitIndex,
                       cmmSysPSUIndex,
                       cmmSysPSTemperature3,
                       cmmSysPSTemperature3CriticalThresholdMin,
                       cmmSysPSTemperature3CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when PSU temperature3 recovered from low critical threshold."
        ::= { cmmAlarmMibNotifications 138 }


    cmmFanTrayInsertedNotify   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmFanTrayNumber,
                      cmmFanSerialNumber
                     }
        STATUS       current
        DESCRIPTION
            "Set when fan tray is inserted"
        ::= { cmmAlarmMibNotifications 141 }

    cmmFanTrayRemovedCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmFanTrayNumber,
                      cmmFanSerialNumber
                     }
        STATUS       current
        DESCRIPTION
            "Set when fan tray is Removed"
        ::= { cmmAlarmMibNotifications 142 }

    cmmFanTrayFaultyCritical  NOTIFICATION-TYPE
       OBJECTS      {
                      cmmStackUnitIndex,
                      cmmFanTrayNumber,
                      cmmFanIndex
                     }
        STATUS       current
        DESCRIPTION
            "Set when Given Fan Tray Faulty."
        ::= { cmmAlarmMibNotifications 143 }

    cmmFanTrayRecovered  NOTIFICATION-TYPE
       OBJECTS      {
                     cmmStackUnitIndex,
                     cmmFanTrayNumber,
                     cmmFanIndex
                    }
        STATUS       current
        DESCRIPTION
            "Set when Given Fan Tray is recovered."
        ::= { cmmAlarmMibNotifications 144 }

    cmmFanTrayStallCritical  NOTIFICATION-TYPE
       OBJECTS      {
                     cmmStackUnitIndex,
                     cmmFanTrayNumber,
                     cmmFanIndex
                    }
        STATUS       current
        DESCRIPTION
            "Set when Given Fan Tray Stalled."
        ::= { cmmAlarmMibNotifications 145 }

    cmmFanTrayStallRecovered  NOTIFICATION-TYPE
       OBJECTS      {
                     cmmStackUnitIndex,
                     cmmFanTrayNumber,
                     cmmFanIndex
                    }
        STATUS       current
        DESCRIPTION
            "Set when Given Fan Tray is recovered from stall."
        ::= { cmmAlarmMibNotifications 146 }

    cmmFanRPMMinNotify  NOTIFICATION-TYPE
        OBJECTS     {
                     cmmStackUnitIndex,
                     cmmFanTrayNumber,
                     cmmFanIndex,
                     cmmFanRpmMin
                    }
        STATUS       obsolete
        DESCRIPTION
            "Set when fan RPM set to its minimum value."
        ::= { cmmAlarmMibNotifications 147 }

    cmmFanRPMMaxCritical  NOTIFICATION-TYPE
        OBJECTS     {
                     cmmStackUnitIndex,
                     cmmFanTrayNumber,
                     cmmFanIndex,
                     cmmFanRpmMax
                    }
    STATUS       current
    DESCRIPTION
        "Set when fan RPM set to its maximum value."
    ::= { cmmAlarmMibNotifications 148 }

    cmmFanRPMDecreasedNotify  NOTIFICATION-TYPE
        OBJECTS     {
                     cmmStackUnitIndex,
                     cmmFanTrayNumber,
                     cmmFanIndex,
                     cmmFanRpm
                    }
        STATUS       current
        DESCRIPTION
            "Set when fan RPM decreased from previous RPM"
        ::= { cmmAlarmMibNotifications 149 }

    cmmFanRPMIncreasedNotify  NOTIFICATION-TYPE
        OBJECTS     {
                     cmmStackUnitIndex,
                     cmmFanTrayNumber,
                     cmmFanIndex,
                     cmmFanRpm
                    }
        STATUS       current
        DESCRIPTION
            "Set when fan RPM increased from previous RPM"
        ::= { cmmAlarmMibNotifications 150 }

     cmmCriticalIncompatibleTransceiverPresence   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex
                     }
        STATUS       current
        DESCRIPTION
            "Set when Incompatible Trasceiver is Present"
        ::= { cmmTransMibNotifications 54}

     cmmNotifyIncompatibleTransceiverRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex
                     }
        STATUS       current
        DESCRIPTION
            "Set when Incompatible Trasceiver is Removed"
        ::= { cmmTransMibNotifications 55}

    --
    -- TRANSCEIVER(DDM) RELATED TRAPS
    --

    cmmTransAlertTempHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransTemperature,
                      cmmTransTempAlertThresholdMin,
                      cmmTransTempAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal temperature of transceiver exceeds high alarm level."
        ::= { cmmTransMibNotifications  1}

    cmmTransAlertTempLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransTemperature,
                      cmmTransTempAlertThresholdMin,
                      cmmTransTempAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal temperature of transceiver is below low alarm level."
        ::= { cmmTransMibNotifications  2}

    cmmTransCriticalTempHigh  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransTemperature,
                      cmmTransTempCriticalThresholdMin,
                      cmmTransTempCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal temperature of transceiver exceeds high Critical level."
        ::= { cmmTransMibNotifications  3}

    cmmTransCriticalTempLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransTemperature,
                      cmmTransTempCriticalThresholdMin,
                      cmmTransTempCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal temperature of transceiver is below low Critical level"
        ::= { cmmTransMibNotifications  4}

    cmmTransNotifyTransceiverTempRecovered  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransTemperature
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is recovered from temperature fault."
        ::= { cmmTransMibNotifications 5}

    cmmTransAlertVoltageHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransVoltage,
                      cmmTransVoltAlertThresholdMin,
                      cmmTransVoltAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal supply voltage of transceiver exceeds high alarm level."
        ::= { cmmTransMibNotifications 11}

    cmmTransAlertVoltageLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransVoltage,
                      cmmTransVoltAlertThresholdMin,
                      cmmTransVoltAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal supply voltage of transceiver is below low alarm level."
        ::= { cmmTransMibNotifications 12}

    cmmTransCriticalVoltageHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransVoltage,
                      cmmTransVoltCriticalThresholdMin,
                      cmmTransVoltCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal supply voltage of transceiver exceeds high Critical level."
        ::= { cmmTransMibNotifications 13}

    cmmTransCriticalVoltageLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransVoltage,
                      cmmTransVoltCriticalThresholdMin,
                      cmmTransVoltCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when internal supply voltage of transceiver is below low Critical level"
        ::= { cmmTransMibNotifications 14}

    cmmTransNotifyTransceiverVoltRecovered  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransVoltage
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is recovered from voltage fault."
        ::= { cmmTransMibNotifications 15}

    cmmTransAlertBiasHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransLaserBiasCurrent,
                      cmmTransLaserBiasCurrAlertThresholdMin,
                      cmmTransLaserBiasCurrAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when TX Bias current of transceiver exceeds high alarm level."
        ::= { cmmTransMibNotifications 21}

    cmmTransAlertBiasLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransLaserBiasCurrent,
                      cmmTransLaserBiasCurrAlertThresholdMin,
                      cmmTransLaserBiasCurrAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when TX Bias current of transceiver is below low alarm level."
        ::= { cmmTransMibNotifications 22 }

    cmmTransCriticalBiashigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransLaserBiasCurrent,
                      cmmTransLaserBiasCurrCriticalThresholdMin,
                      cmmTransLaserBiasCurrCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when TX Bias current of transceiver exceeds high Critical level."
        ::= { cmmTransMibNotifications 23}

    cmmTransCriticalBiasLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransLaserBiasCurrent,
                      cmmTransLaserBiasCurrCriticalThresholdMin,
                      cmmTransLaserBiasCurrCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when TX Bias current of transceiver is below low Critical level."
        ::= { cmmTransMibNotifications 24}

    cmmTransNotifyTransceiverBiasRecovered  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransLaserBiasCurrent
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is recovered from Bias current fault."
        ::= { cmmTransMibNotifications 25}

    cmmTransAlertRxPowerHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransRxPower,
                      cmmTransRxPowerAlertThresholdMin,
                      cmmTransRxPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Received Power of transceiver exceeds high alarm level."
        ::= { cmmTransMibNotifications 31}

    cmmTransAlertRxPowerLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransRxPower,
                      cmmTransRxPowerAlertThresholdMin,
                      cmmTransRxPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Received Power of transceiver is below low alarm level."
        ::= { cmmTransMibNotifications 32}

    cmmTransCriticalRxPowerHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransRxPower,
                      cmmTransRxPowerCriticalThresholdMin,
                      cmmTransRxPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Received Power of transceiver exceeds high Critical level."
        ::= { cmmTransMibNotifications 33}

    cmmTransCriticalRxPowerLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransRxPower,
                      cmmTransRxPowerCriticalThresholdMin,
                      cmmTransRxPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Received Power of transceiver is below low Critical level."
        ::= { cmmTransMibNotifications 34}

    cmmTransNotifyTransceiverRxPowRecovered  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransRxPower
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is recovered from Rx power fault."
        ::= { cmmTransMibNotifications 35}

    cmmTransAlertTxPowerHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransTxPower,
                      cmmTransTxPowerAlertThresholdMin,
                      cmmTransTxPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Transferred Power of transceiver exceeds high alarm level."
        ::= { cmmTransMibNotifications 41}

    cmmTransAlertTxPowerLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransTxPower,
                      cmmTransTxPowerAlertThresholdMin,
                      cmmTransTxPowerAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Transferred Power of transceiver is below low alarm level."
        ::= { cmmTransMibNotifications 42}

    cmmTransCriticalTxPowerHigh   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransTxPower,
                      cmmTransTxPowerCriticalThresholdMin,
                      cmmTransTxPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Transferred Power of transceiver exceeds high Critical level."
        ::= { cmmTransMibNotifications 43}

    cmmTransCriticalTxPowerLow   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransTxPower,
                      cmmTransTxPowerCriticalThresholdMin,
                      cmmTransTxPowerCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Transferred Power of transceiver is below low Critical level."
        ::= { cmmTransMibNotifications 44}

    cmmTransNotifyTransceiverTxPowRecovered  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransChannelIndex,
                      cmmTransTxPower
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is recovered from Tx Power fault."
        ::= { cmmTransMibNotifications 45}

    cmmTransNotifyTransceiverInserted   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransVendorName,
                      cmmTransVendorSerialNumber,
                      cmmTransconnectortype
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is inserted."
        ::= { cmmTransMibNotifications 51}

    cmmTransCriticalTransceiverRemoved  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransVendorName,
                      cmmTransVendorSerialNumber,
                      cmmTransconnectortype
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is removed."
        ::= { cmmTransMibNotifications 52}

    cmmTransCriticalFaultyTransceiverInserted   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType
                     }
        STATUS       current
        DESCRIPTION
            "Set when Trasceiver is inserted and failed to read transceiver EEPROM information."
        ::= { cmmTransMibNotifications 53}

    cmmTransXFPAlertVoltage2High   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransXFPVoltage2,
                      cmmTransXFPVolt2AlertThresholdMin,
                      cmmTransXFPVolt2AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when the volatge of the second available supply of the XFP transceiver exceeds the high warning level."
        ::= { cmmTransMibNotifications 61}

    cmmTransXFPAlertVoltage2Low   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransXFPVoltage2,
                      cmmTransXFPVolt2AlertThresholdMin,
                      cmmTransXFPVolt2AlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when the volatge of the second available supply of the XFP transceiver is below the low warning level."
        ::= { cmmTransMibNotifications 62}

    cmmTransXFPCriticalVoltage2High   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransXFPVoltage2,
                      cmmTransXFPVolt2CriticalThresholdMin,
                      cmmTransXFPVolt2CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when the volatge of the second available supply of the XFP transceiver exceeds the high critical level."
        ::= { cmmTransMibNotifications 63}

    cmmTransXFPCriticalVoltage2Low   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransXFPVoltage2,
                      cmmTransXFPVolt2CriticalThresholdMin,
                      cmmTransXFPVolt2CriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when the volatge of the second available supply of the XFP transceiver is below the low critical level"
        ::= { cmmTransMibNotifications 64}

    cmmTransNotifyTransceiverXFPVolt2Recovered  NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransXFPVoltage2
                     }
        STATUS       current
        DESCRIPTION
            "Set when the volatge of the second available supply of the XFP transceiver is recovered from the voltage fault."
        ::= { cmmTransMibNotifications 65}


    cmmTransFrequencyErrorHighAlert   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransFrequencyError,
                      cmmTransFrequencyErrorAlertThresholdMin,
                      cmmTransFrequencyErrorAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Frequency error of transceiver exceeds high alert level."
        ::= { cmmTransMibNotifications 66 }

    cmmTransFrequencyErrorLowAlert   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransFrequencyError,
                      cmmTransFrequencyErrorAlertThresholdMin,
                      cmmTransFrequencyErrorAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Frequency error of transceiver is below low alert level."
        ::= { cmmTransMibNotifications 67 }

    cmmTransFrequencyErrorHighCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransFrequencyError,
                      cmmTransFrequencyErrorCriticalThresholdMin,
                      cmmTransFrequencyErrorCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Frequency error of transceiver exceeds high critical level."
        ::= { cmmTransMibNotifications 68 }

    cmmTransFrequencyErrorLowCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransFrequencyError,
                      cmmTransFrequencyErrorCriticalThresholdMin,
                      cmmTransFrequencyErrorCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Frequency error of transceiver is below low critical level."
        ::= { cmmTransMibNotifications 69 }

    cmmTransFrequencyErrorRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransFrequencyError
                     }
        STATUS       current
        DESCRIPTION
            "Set when Frequency error is recovered to normal range."
        ::= { cmmTransMibNotifications 70 }

    cmmTransWavelengthErrorHighAlert   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransWavelengthError,
                      cmmTransWavelengthErrorAlertThresholdMin,
                      cmmTransWavelengthErrorAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Wavelength error of transceiver exceeds high alert level."
        ::= { cmmTransMibNotifications 71 }

    cmmTransWavelengthErrorLowAlert   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransWavelengthError,
                      cmmTransWavelengthErrorAlertThresholdMin,
                      cmmTransWavelengthErrorAlertThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Wavelength error of transceiver is below low alert level."
        ::= { cmmTransMibNotifications 72 }

    cmmTransWavelengthErrorHighCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransWavelengthError,
                      cmmTransWavelengthErrorCriticalThresholdMin,
                      cmmTransWavelengthErrorCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Wavelength error of transceiver exceeds high critical level."
        ::= { cmmTransMibNotifications 73 }

    cmmTransWavelengthErrorLowCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransWavelengthError,
                      cmmTransWavelengthErrorCriticalThresholdMin,
                      cmmTransWavelengthErrorCriticalThresholdMax
                     }
        STATUS       current
        DESCRIPTION
            "Set when Wavelength error of transceiver is below low critical level."
        ::= { cmmTransMibNotifications 74 }

    cmmTransWavelengthErrorRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType,
                      cmmTransWavelengthError
                     }
        STATUS       current
        DESCRIPTION
            "Set when Wavelength error is recovered to normal range."
        ::= { cmmTransMibNotifications 75 }

    cmmTransTECFaultCritical   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType
                     }
        STATUS       current
        DESCRIPTION
            "Set when TEC Fault has occurred on the transceiver."
        ::= { cmmTransMibNotifications 76 }

    cmmTransTECFaultRecovery   NOTIFICATION-TYPE
        OBJECTS      {
                      cmmStackUnitIndex,
                      cmmTransIndex,
                      cmmTransType
                     }
        STATUS       current
        DESCRIPTION
            "Set when the transceiver has recovered from TEC Fault."
        ::= { cmmTransMibNotifications 77 }

cmmTransAlertPreFecBerErrHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransPreFecBerVal,
                  cmmTransPreFecBerCriticMin,
                  cmmTransPreFecBerCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when FEC bit error rate falls above alert max value."
    ::= { cmmTransMibNotifications 81}

cmmTransAlertPreFecBerErrLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransPreFecBerVal,
                  cmmTransPreFecBerCriticMin,
                  cmmTransPreFecBerCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when FEC bit error rate falls below alert min value."
    ::= { cmmTransMibNotifications 82}

cmmTransCriticalPreFecBerErrHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransPreFecBerVal,
                  cmmTransPreFecBerCriticalMin,
                  cmmTransPreFecBerCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when FEC bit error rate falls above critical max value."
    ::= { cmmTransMibNotifications 83}

cmmTransCriticalPreFecBerErrLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransPreFecBerVal,
                  cmmTransPreFecBerCriticalMin,
                  cmmTransPreFecBerCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when FEC bit error rate falls below critical min value."
    ::= { cmmTransMibNotifications 84}


cmmTransNotifyTransceiverPreFecBerRecovered   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransPreFecBerVal
                 }
    STATUS       current
    DESCRIPTION
        "Set when FEC bit error rate of transceiver is recovered from BER fault."
    ::= { cmmTransMibNotifications 85}


cmmTransAlertUncorrectedBerHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransUncorrectedBerVal,
                  cmmTransUncorrectedBerValCriticMin,
                  cmmTransUncorrectedBerValCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when Frame Error Rate falls above alert max value."
    ::= { cmmTransMibNotifications 91}

cmmTransAlertUncorrectedBerLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransUncorrectedBerVal,
                  cmmTransUncorrectedBerValCriticMin,
                  cmmTransUncorrectedBerValCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when Frame Error Rate falls below alert min value."
    ::= { cmmTransMibNotifications 92}


cmmTransCriticalUncorrectedBerHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransUncorrectedBerVal,
                  cmmTransUncorrectedBerValCriticalMin,
                  cmmTransUncorrectedBerValCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when Frame Error Rate falls above critical max value."
    ::= { cmmTransMibNotifications 93}

cmmTransCriticalUncorrectedBerLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransUncorrectedBerVal,
                  cmmTransUncorrectedBerValCriticalMin,
                  cmmTransUncorrectedBerValCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when Frame Error Rate falls below critical min value."
    ::= { cmmTransMibNotifications 94}


cmmTransNotifyTransceiverUncorrectedBerRecovered   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransUncorrectedBerVal
                 }
    STATUS       current
    DESCRIPTION
        "Set when Frame Error Rate transceiver is recovered from uncorrected BER fault."
    ::= { cmmTransMibNotifications 95}

cmmTransAlertSnrHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransSnrVal,
                  cmmTransSnrCriticMin,
                  cmmTransSnrCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when SNR falls above alert max value."
    ::= { cmmTransMibNotifications 101}

cmmTransAlertSnrLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransSnrVal,
                  cmmTransSnrCriticMin,
                  cmmTransSnrCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when SNR falls below alert min value."
    ::= { cmmTransMibNotifications 102}

cmmTransCriticalSnrHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransSnrVal,
                  cmmTransSnrCriticalMin,
                  cmmTransSnrCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when SNR falls above critical max value."
    ::= { cmmTransMibNotifications 103}

cmmTransCriticalSnrLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransSnrVal,
                  cmmTransSnrCriticalMin,
                  cmmTransSnrCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when SNR falls below critical min value."
    ::= { cmmTransMibNotifications 104}

cmmTransNotifyTransceiverSnrRecovered   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransSnrVal
                 }
    STATUS       current
    DESCRIPTION
        "Set when SNR of transceiver is recovered from SNR fault."
    ::= { cmmTransMibNotifications 105}

cmmTransAlertresIsiHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransResIsiVal,
                  cmmTransResIsiCriticMin,
                  cmmTransResIsiCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when residual-isi falls above alert max value."
    ::= { cmmTransMibNotifications 111}

cmmTransAlertresIsiLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransResIsiVal,
                  cmmTransResIsiCriticMin,
                  cmmTransResIsiCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when residual-isi falls below alert min value."
    ::= { cmmTransMibNotifications 112}

cmmTransCriticalResIsiHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransResIsiVal,
                  cmmTransResIsiCriticalMin,
                  cmmTransResIsiCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when residual-isi falls above critical max value."
    ::= { cmmTransMibNotifications 113}

cmmTransCriticalResIsiLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransResIsiVal,
                  cmmTransResIsiCriticalMin,
                  cmmTransResIsiCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when residual-isi falls below critical min value."
    ::= { cmmTransMibNotifications 114}

cmmTransNotifyTransceiverResIsiRecovered   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransResIsiVal
                 }
    STATUS       current
    DESCRIPTION
        "Set when residual-isi of transceiver is recovered from residual-isi fault."
    ::= { cmmTransMibNotifications 115}


cmmTransAlertLvlTranHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLvlTransVal,
                  cmmTransLvlTransCriticMin,
                  cmmTransLvlTransCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when level transition falls above alert max value."
    ::= { cmmTransMibNotifications 121}

cmmTransAlertLvlTranLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLvlTransVal,
                  cmmTransLvlTransCriticMin,
                  cmmTransLvlTransCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when level transition falls below alert min value."
    ::= { cmmTransMibNotifications 122}

cmmTransCriticalLvlTranHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLvlTransVal,
                  cmmTransLvlTransCriticalMin,
                  cmmTransLvlTransCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when level transition falls above critical max value."
    ::= { cmmTransMibNotifications 123}

cmmTransCriticalLvlTranLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLvlTransVal,
                  cmmTransLvlTransCriticalMin,
                  cmmTransLvlTransCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when level transition falls below critical min value."
    ::= { cmmTransMibNotifications 124}

cmmTransNotifyTransceiverLvlTranRecovered   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLvlTransVal
                 }
    STATUS       current
    DESCRIPTION
        "Set when level transition of transceiver is recovered from level transition fault."
    ::= { cmmTransMibNotifications 125}

cmmTransAlertTecCurrErrHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransTecCurrErrVal,
                  cmmTransTecCurrErrCriticMin,
                  cmmTransTecCurrErrCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when thermo-electric cooler current falls above alert max value."
    ::= { cmmTransMibNotifications 131}

cmmTransAlertTecCurrErrLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransTecCurrErrVal,
                  cmmTransTecCurrErrCriticMin,
                  cmmTransTecCurrErrCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when thermo-electric cooler current falls below alert min value."
    ::= { cmmTransMibNotifications 132}

cmmTransCriticalTecCurrErrHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransTecCurrErrVal,
                  cmmTransTecCurrErrCriticalMin,
                  cmmTransTecCurrErrCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when thermo-electric cooler current falls above critical max value."
    ::= { cmmTransMibNotifications 133}

cmmTransCriticalTecCurrErrLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransTecCurrErrVal,
                  cmmTransTecCurrErrCriticalMin,
                  cmmTransTecCurrErrCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when thermo-electric cooler current falls below critical min value."
    ::= { cmmTransMibNotifications 134}

cmmTransNotifyTransceiverTecCurrErrRecovered   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransTecCurrErrVal
                 }
    STATUS       current
    DESCRIPTION
        "Set when thermo-electric cooler current of transceiver is recovered thermo-electric cooler current fault."
    ::= { cmmTransMibNotifications 135}

cmmTransAlertLaserTempValHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLaserTempVal,
                  cmmTransLaserTempCriticMin,
                  cmmTransLaserTempCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when laser temperature falls above crictical max value."
    ::= { cmmTransMibNotifications 141}

cmmTransAlertLaserTempValLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLaserTempVal,
                  cmmTransLaserTempCriticMin,
                  cmmTransLaserTempCriticMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when laser temperature falls below alert min value."
    ::= { cmmTransMibNotifications 142}

cmmTransCriticalLaserTempValHigh   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLaserTempVal,
                  cmmTransLaserTempCriticalMin,
                  cmmTransLaserTempCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when laser temperature falls above critical max value."
    ::= { cmmTransMibNotifications 143}

cmmTransCriticalLaserTempValLow   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLaserTempVal,
                  cmmTransLaserTempCriticalMin,
                  cmmTransLaserTempCriticalMax
                 }
    STATUS       current
    DESCRIPTION
        "Notification sent when laser temperature falls below critical min value."
    ::= { cmmTransMibNotifications 144}

cmmTransNotifyTransceiverLaserTempRecovered   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex,
                  cmmTransLaserTempVal
                 }
    STATUS       current
    DESCRIPTION
        "Set when laser temperature of transceiver is recovered from temperature fault."
    ::= { cmmTransMibNotifications 145}

cmmTranAlertTransceiverPortRxLoss   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex
                 }
    STATUS       current
    DESCRIPTION
         "Set when RX_LOSS is detected on transceiver ports"
    ::= { cmmTransMibNotifications 146}

cmmTransNotifyTransceiverPortRxLossRecovery   NOTIFICATION-TYPE
    OBJECTS      {
                  cmmStackUnitIndex,
                  cmmTransIndex,
                  cmmTransType,
                  cmmTransChannelIndex
                }
    STATUS       current
    DESCRIPTION
         "Set when RX_LOSS is recovered on transceiver ports"
    ::= { cmmTransMibNotifications 147}

   END
