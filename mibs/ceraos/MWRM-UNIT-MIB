--MWRM-UNIT-MIB VERSION *******
--MWRM (MicroWave-Radio-MIB)
MWRM-UNIT-MIB DEFINITIONS ::= BEGIN

IMPORTS
--    Ipv6Address          FROM IPV6-TC
	enterprises, IpAddress 			FROM SNMPv2-SMI
	DisplayString, ifIndex  		FROM RFC1213-MIB
	RowStatus						FROM SNMPv2-TC
	MacAddress						FROM SNMPv2-TC
	OBJECT-TYPE          			FROM SNMPv2-SMI
	TRAP-TYPE            			FROM RFC-1215;

EnableDisable  	      ::= INTEGER { enable(2), disable(3) }
EnableDisableSMI2  	  ::= INTEGER { disable(0), enable(1) }
OffOn			      ::= INTEGER { off(0), on(1) }
MetricImperial	      ::= INTEGER { metric(0), imperial(1) }
AllowedNotAllowed     ::= INTEGER { not-allowed(0), allowed(1)}
NoYes			      ::= INTEGER { no(0), yes(1) }
DownUp			      ::= INTEGER { down(0), up(1) }
SupportedNotsupported ::= INTEGER { supported(2), not-supported(3) }

ProgressStatus ::= INTEGER{
					ready(0),
					inProgress(1),
					success(2),
					failure(3)
				}

Severity ::= INTEGER {
			indeterminate(0),
			critical(1),
			major(2),
			minor(3),
			warning(4),
			cleared(5)
			}


TrailIfType ::= INTEGER {
			unknown(-1),
			line(0),
			radio(1),
			stm-1-oc-3(2),
			sync(4)
			}

PmTableType ::= INTEGER {
			pm15mincurr(1),
			pm15min(2),
			pm24hrcurr(3),
			pm24hr(4)
			}

RateMbps		::= INTEGER {not-applicable(-1), n10(0), n100(1), n1000(2) }

HalfFull		::= INTEGER {not-applicable(-1), half(0), full(1), auto(2) }

BerLevel		::= INTEGER { n1e-3(2), n1e-4(3), n1e-5(4)}

SignalLevel		::= INTEGER { n1e-6(5), n1e-7(6), n1e-8(7), n1e-9(8)}

Exponent   		::= INTEGER {
                    n1e-2(1),
                    n1e-3(2),
                    n1e-4(3),
                    n1e-5(4),
                    n1e-6(5),
                    n1e-7(6),
                    n1e-8(7),
                    n1e-9(8),
                    n1e-10(9),
                    n1e-11(10),
                    n1e-12(11),
                    n1e-13(12),
                    n1e-14(13),
                    n1e-15(14),
                    n1e-16(15),
                    n1e-17(16),
                    n1e-18(17),
                    n1e-0(18)
        }

LoopbackType	::= INTEGER {
						off(0),
						towardsLine(1),
						towardsRadio(2)
						}

QueueName		::= INTEGER {
					first-queue(0),
					second-queue(1),
					third-queue(2),
					fourth-queue(3),
					none(4)
				}

RadioId			::= INTEGER
RfuId			::= INTEGER

SwCommand ::= INTEGER{
				noOperation(0),
				downloadUpgradeVersion(1),
				upgrade(2),
				rollback(3),
				downgrade(4),
				downloadDowngradeVersion(5)
			}

TrailProtectedType ::= INTEGER {
				none(0),
				primary(1),
				secondary(2)
			}

ClockSrc ::= INTEGER {
			local-clock(0),
			system-clock-source(1)
		}

SlotId ::= INTEGER {
			standalone(0),
			slot1(1),
			slot2(2),
			slot3(3),
			slot4(4),
			slot5(5),
			slot6(6),
			slot7(7),
			slot8(8),
			slot9(9),
			slot10(10),
			slot11(11),
			slot12(12)
        }

Integrity			::= INTEGER { integrity(0), nointegrity(1) }

GreenYellow ::= INTEGER {
					green(0),
					yellow(1)
		}

InputSeverity ::= INTEGER {
			indeterminate(0),
			critical(1),
			major(2),
			minor(3),
			warning(4)
			}

SwCommandTimer ::= INTEGER{
				noOperation(0),
				downloadUpgradeVersion(1),
				upgrade(2),
				rollback(3),
				downgrade(4),
				downloadDowngradeVersion(5),
				upgradeTimer(6),
				rollbackTimer(7),
				downgradeTimer(8),
				abortTimedInstallation(9)
			}

FileTransferStatus ::= INTEGER{
					ready(0),
					inTransfer(1),
					failure(2),
					success(3)
				}

FileRestoreStatus ::= INTEGER{
					ready(0),
					restoring-configuration(1),
					failure(2),
					success(3)
				}


RbacAccessLevel ::= INTEGER {
				none(0),
				normal(1),
				advance(2)
			}

InventoryCardType ::= INTEGER {
				cleared(0),
				nexusSc(10),
				nexusScLp(11),
				nexusDc(12),
				nexusQc(13),
				tccR(19),
				tccA(20),
				tccB(21),
				rmcA(22),
				rmcB(23),
				rmcNDc(24),
				nativeTdm16xE1T1(25),
				pwe3-16xE1T1(26),
				tdm1xStm1(27),
				tdm1xOc3(28),
				eLicEth4x1GEA(29),
				chassis1U2U(30),
				capacityBooster(31),
				pwe3-1xSTM1(32),
				pdc48v2uSingleFeed(33),
				pdc48v1uSingleFeed(34),
				pdc48v1uDualFeed(35),
				fan2U(36),
				fan1U(37),
				test-card(38),
				pdc24v2uSingleFeed(39),
				pdc24v1uSingleFeed(40),
				pdc24v1uDualFeed(41),
				unknownCard(42),
				ricE(43),
				trafficFpga(44),
				essFpga(45),
				tressFpga(46),
				ip20g(47),
				licXe4opt(48),
				tccBmc(49),
				rmcE(50),
				licStm1oc3rst(51),
				tccAmc(52),
				ip20cEband(53),
				tccA2(54),
				tccA2mc(55),
				tccB2(56)
			}


FtpProtocolType ::= INTEGER {
				ftp(0),
				sftp(1)
			}

CfgUnitInfoOper ::= INTEGER {
				invalid-operation(0),
				create-pakcge(1),
				export-pakcge(2)
			}

CfgOper ::= INTEGER {
				invalid-operation(0),
				backup(1),
				restore(2),
				delete(3),
				import(4),
				export(5)
			}

CardOccupancy ::= INTEGER {
				empty(1),
				equipped(2),
				not-operational(3)
			}

OperState ::= INTEGER {
				down(1),
				init(2),
				loading(3),
				loaded(4),
				up(5),
				up-with-alarms(6)
			}

LicenseGeneric ::= INTEGER {
						not-allowed(100000),
						allowed(100001),
						disable(100010),
						enable(100011),
						only-management(100020),
						smart-pipe(100021),
						enhanced-pipe(100022),
						edge-cet(100023),
						access-cet(100024),
						aggregation-cet(100025)
			}

RaduisAcceaaLevel ::= INTEGER {
						none(0),
						normal(1),
						advanced(2),
						root(3)
			}

VmResetType ::= INTEGER {
						no-reset(0),
						main-board-warm-reset(1),
						tcc-cold-reset(2),
						main-board-cold-reset(3),
						card-warm-reset(4),
						card-cold-reset(5),
						not-applicable-reset(6)
			}

FTStatus ::= INTEGER {
						ready(0),
						file-in-transfer(1),
						failure(2),
						success(3)
			}

CsrCertificateFTState ::= INTEGER {
						no-operation(0),
						upload(1),
						download(2)
			}

CsrFileFormat	::=	INTEGER {
						pem(1),
						der(2)
			}


microwave-radio OBJECT IDENTIFIER ::= { enterprises 2281 }
genEquip OBJECT IDENTIFIER ::= { microwave-radio 10}
genEquipUnit OBJECT IDENTIFIER ::= { genEquip 1}


--- Unit information
genEquipUnitInfo OBJECT IDENTIFIER ::= { genEquipUnit 1}

genEquipLastCfgTimeStamp OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "This parameter represents the configuration change counter.
         On every conf. change this counter is increased by 1."
        ::= { genEquipUnitInfo 1 }

genEquipRealTimeandDate OBJECT-TYPE
        SYNTAX OCTET STRING(SIZE(8))
        ACCESS  read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter specifies the real time and date
         Format 'YYYY-MM-DD,HH:MM:SS' (Hexadecimal).
         A date-time specification:

            field  octets  contents                  range
            -----  ------  --------                  -----
              1      1-2   year                      0..65536
              2       3    month                     1..12
              3       4    day                       1..31
              4       5    hour                      0..23
              5       6    minutes                   0..59
              6       7    seconds                   0..60
                           (use 60 for leap-second)
              7       8    deci-seconds              0..9

            For example, Tuesday May 26, 1992 at 1:30:15 PM EDT
            would be displayed as:        07 c8 05  1a  0d 1e 0f 00
                                         (1992  -5 -26, 13:30:15 )
         "

        ::= { genEquipUnitInfo 2 }

genEquipPMGenTime	OBJECT-TYPE
        SYNTAX 		INTEGER
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
"This value indicates the generation time of the Performance Monitor file.
The value is presented in time_t format."
::= { genEquipUnitInfo 3 }

genEquipInvGenTime	OBJECT-TYPE
        SYNTAX 		INTEGER
        ACCESS 		read-only
        STATUS 		mandatory
        DESCRIPTION
"This value indicates the generation time of the Inventory information file.
The inventory file holds the configuration information of the unit.
The value is presented in time_t format."
::= { genEquipUnitInfo 4 }

genEquipOperation	OBJECT-TYPE
		SYNTAX		INTEGER{
					noAction(0),
					idcHwReset(1)
					}
        ACCESS 		read-write
        STATUS 		mandatory
        DESCRIPTION
		"Enables to perform HW reset to the indoor unit."
::= { genEquipUnitInfo 5 }

genEquipMIBVersion	OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
"This value indicates MIB version."
::= { genEquipUnitInfo 6 }

genEquipUnitCLLI	OBJECT-TYPE
		SYNTAX		DisplayString (SIZE(0..255))
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
"This value represents a general purpose CLLI-like text field.
The CLLI field is attached to traps generated from this NE."
::= { genEquipUnitInfo 7 }

genEquipUnitMeasurementSystem	OBJECT-TYPE
        SYNTAX		MetricImperial
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
"This value represents the measurement system used by the system. (metric or imperal)."
::= { genEquipUnitInfo 8 }


genEquipUnitIduTemperature	OBJECT-TYPE
        SYNTAX		INTEGER
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
"This value indicates the IDU temperature, according to the measurement system chosen.
(genEquipUnitMeasurementSystem)"
::= { genEquipUnitInfo 9 }

genEquipUnitIduVoltageInput	OBJECT-TYPE
        SYNTAX		INTEGER
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Voltages Input."
::= { genEquipUnitInfo 10 }

genEquipUnitInfoTime OBJECT IDENTIFIER ::= { genEquipUnitInfo 11}

genEquipUnitGMTHours OBJECT-TYPE
        SYNTAX INTEGER (-12..13)
        ACCESS  read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter represent the offset in hours from GMT."
        ::= { genEquipUnitInfoTime 1 }

genEquipUnitGMTMins OBJECT-TYPE
        SYNTAX INTEGER (0..59)
        ACCESS  read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter represents the offset in minutes from GMT."
        ::= { genEquipUnitInfoTime 2 }

genEquipUnitDaylightSavingTimeStartMonth	OBJECT-TYPE
        SYNTAX		INTEGER (1..12)
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the start Month of the daylight saving time."
        ::= { genEquipUnitInfoTime 7 }

genEquipUnitDaylightSavingTimeStartDay	OBJECT-TYPE
        SYNTAX		INTEGER (1..31)
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the start Day of the daylight saving time."
        ::= { genEquipUnitInfoTime 8 }


genEquipUnitDaylightSavingTimeEndMonth	OBJECT-TYPE
        SYNTAX		INTEGER (1..12)
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the end month of the daylight saving time."
        ::= { genEquipUnitInfoTime 9 }

genEquipUnitDaylightSavingTimeEndDay	OBJECT-TYPE
        SYNTAX		INTEGER (1..31)
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the end day of the daylight saving time."
        ::= { genEquipUnitInfoTime 10 }


genEquipUnitDaylightSavingTimeOffset	OBJECT-TYPE
        SYNTAX		INTEGER (0..23)
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Daylight saving offset hours.
		for a value different then '0', at the starting date of the daylight saving time
		the time will jump forward in this value.
		at the end date of the daylight saving time, the time will jump backwards in this value."
        ::= { genEquipUnitInfoTime 11 }

--
-- Time services configuration table - time-services-config-table
--
genEquipUnitInfoTimeServicesTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitInfoTimeServicesEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table contains the time services properties."
        ::= { genEquipUnitInfoTime 12 }

genEquipUnitInfoTimeServicesEntry OBJECT-TYPE
        SYNTAX GenEquipUnitInfoTimeServicesEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table entry contains the time services properties."
        INDEX { genEquipUnitInfoTimeServicesIndex }
        ::= { genEquipUnitInfoTimeServicesTable 1 }

GenEquipUnitInfoTimeServicesEntry ::=
		SEQUENCE {
			genEquipUnitInfoTimeServicesIndex
				INTEGER,
			genEquipUnitInfoTimeServicesUtcHours
            	INTEGER,
			genEquipUnitInfoTimeServicesUtcMinutes
              	INTEGER,
			genEquipUnitInfoTimeServicesDstStartMonth
				INTEGER,
			genEquipUnitInfoTimeServicesDstStartDay
				INTEGER,
			genEquipUnitInfoTimeServicesDstEndMonth
				INTEGER,
			genEquipUnitInfoTimeServicesDstEndDay
				INTEGER,
			genEquipUnitInfoTimeServicesDstOffset
				INTEGER,
			genEquipUnitInfoTimeServicesUtcDateAndTime
				INTEGER,
			genEquipUnitInfoTimeServicesUtcCurrentDateAndTime
				INTEGER
}

genEquipUnitInfoTimeServicesIndex	OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The index of the table."
::= { genEquipUnitInfoTimeServicesEntry 1 }

genEquipUnitInfoTimeServicesUtcHours	OBJECT-TYPE
		SYNTAX 		INTEGER (-12..13)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The required hours offset relative to the UTC (Universal Time Coordinated)."
::= { genEquipUnitInfoTimeServicesEntry 2 }

genEquipUnitInfoTimeServicesUtcMinutes	OBJECT-TYPE
		SYNTAX 		INTEGER (0..59)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The required minutes offset relative to the UTC (Universal Time Coordinated)."
::= { genEquipUnitInfoTimeServicesEntry 3 }

genEquipUnitInfoTimeServicesDstStartMonth	OBJECT-TYPE
		SYNTAX 		INTEGER (1..12)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The month when the DST (Daylight Saving Time) is required to start."
::= { genEquipUnitInfoTimeServicesEntry 4 }

genEquipUnitInfoTimeServicesDstStartDay 		OBJECT-TYPE
		SYNTAX 		INTEGER (1..31)
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"The day when the DST (Daylight Saving Time) is required to start."
::= { genEquipUnitInfoTimeServicesEntry 5 }

genEquipUnitInfoTimeServicesDstEndMonth 		OBJECT-TYPE
		SYNTAX 		INTEGER (1..12)
		ACCESS 		read-write		STATUS 		mandatory
		DESCRIPTION
		"The month when the DST (Daylight Saving Time) is required to end."
::= { genEquipUnitInfoTimeServicesEntry 6 }

genEquipUnitInfoTimeServicesDstEndDay 		OBJECT-TYPE
		SYNTAX 		INTEGER (1..31)
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"The day when the DST (Daylight Saving Time) is required to end."
::= { genEquipUnitInfoTimeServicesEntry 7 }

genEquipUnitInfoTimeServicesDstOffset 		OBJECT-TYPE
		SYNTAX 		INTEGER (0..23)
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"The required offset for DST (Daylight Saving Time) in hours."
::= { genEquipUnitInfoTimeServicesEntry 8 }

genEquipUnitInfoTimeServicesUtcDateAndTime 		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"UTC (Universal Time Coordinated) date and time."
::= { genEquipUnitInfoTimeServicesEntry 9 }

genEquipUnitInfoTimeServicesUtcCurrentDateAndTime 		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS 		read-only
		STATUS 		mandatory
		DESCRIPTION
		"The calculated current local date &amp; time based on the local clock, UTC and DST (Daylight Saving Time) configurations."
::= { genEquipUnitInfoTimeServicesEntry 10 }



genEquipUnitIduPowerSupply1AlarmAdmin OBJECT-TYPE
        SYNTAX		EnableDisable
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter is relevant only for IDU with more than one power supply units.
		The user can choose to disable (ignore the alarm) of one of the power supply units."
::= { genEquipUnitInfo 12 }

genEquipUnitIduPowerSupply2AlarmAdmin OBJECT-TYPE
        SYNTAX		EnableDisable
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter is relevant only for IDU with more than two power supply units.
		The user can choose to disable (ignore the alarm) of one of the power supply units."
::= { genEquipUnitInfo 13 }

genEquipUnitInfoNG OBJECT IDENTIFIER ::= { genEquipUnitInfo 14}

genEquipUnitName OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The name of the system."
::= { genEquipUnitInfoNG 1 }

genEquipUnitDescription OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"A ahort description of the system."
::= { genEquipUnitInfoNG 2 }

genEquipUnitContactPerson OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Contact person."
::= { genEquipUnitInfoNG 3 }

genEquipUnitLocation OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The location of the system."
::= { genEquipUnitInfoNG 4 }

genEquipUnitLatitude OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The latitude of the system."
::= { genEquipUnitInfoNG 5 }

genEquipUnitLongitude OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The longitude of system."
::= { genEquipUnitInfoNG 6 }

genEquipUnitIpAddressType OBJECT-TYPE
        SYNTAX		INTEGER {
				ipv4	(0),
				ipv6	(1)
		}
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IP address Family."
::= { genEquipUnitInfoNG 7 }

genEquipUnitInfoNGTdmInterfaceStandard OBJECT-TYPE
        SYNTAX		INTEGER {
						ets1(0),
						ansi(1)
		}
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The TDM interface standard: ETSI (Erupean) or ANSI (North American)"
::= { genEquipUnitInfoNG 8 }


--- Unit NTP information
genEquipUnitInfoNTP OBJECT IDENTIFIER ::= { genEquipUnitInfoTime 6}

genEquipUnitInfoNTPAdmin	OBJECT-TYPE
        SYNTAX		EnableDisable
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the NTP Admin state"
::= { genEquipUnitInfoNTP 1 }

genEquipUnitInfoNTPServerIP	OBJECT-TYPE
        SYNTAX		IpAddress
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the IP address of the NTP Server"
::= { genEquipUnitInfoNTP 2 }

genEquipUnitInfoNTPStatus	OBJECT-TYPE
        SYNTAX		INTEGER {
						down(0),
						up(1)
		}
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the NTP Service status"
::= { genEquipUnitInfoNTP 3 }

genEquipUnitInfoNTPSync	OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter returns the IP address of the NTP server, with which the system is currently synchronized.
		Returned strings:
			(i)   - the IP address of the reference NTP server, according to IPv4/v6 format.
			(ii)  - 'LOCAL' if synchronized on local clock.
			(iii) - 'N/A' if not synchronized. Valid only when admin is 'disabled'."
::= { genEquipUnitInfoNTP 4 }

genEquipUnitInfoNTPPollInterval	OBJECT-TYPE
        SYNTAX		INTEGER (64..1024)
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter represents the NTP polling interval in minutes"
::= { genEquipUnitInfoNTP 5 }



genEquipUnitInfoNtpStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitInfoNtpStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the status of the NTP (Network Time Protocol)."
        ::= { genEquipUnitInfoNTP 6 }


genEquipUnitInfoNtpStatusEntry OBJECT-TYPE
        SYNTAX GenEquipUnitInfoNtpStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Entry containing the data of the NTP status."
        INDEX { genEquipUnitInfoNtpStatusIndex }
        ::= { genEquipUnitInfoNtpStatusTable 1 }

GenEquipUnitInfoNtpStatusEntry ::=
            SEQUENCE {
		genEquipUnitInfoNtpStatusIndex
			INTEGER,
		genEquipUnitInfoNtpStatusPollInterval
			INTEGER,
        genEquipUnitInfoNtpStatusSyncServerIP
			IpAddress,
		genEquipUnitInfoNtpStatusLockState
			INTEGER
	}

genEquipUnitInfoNtpStatusIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Index of the table."
        ::= { genEquipUnitInfoNtpStatusEntry 1 }

genEquipUnitInfoNtpStatusPollInterval OBJECT-TYPE
        SYNTAX  INTEGER (0..1024)
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Indicates the time interval in minutes in which the NTP client polls the server it is locked to."
        ::= { genEquipUnitInfoNtpStatusEntry 2 }

genEquipUnitInfoNtpStatusSyncServerIP OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "he IPv4 address of the NTP server that the client is currently locked on."
        ::= { genEquipUnitInfoNtpStatusEntry 3 }

genEquipUnitInfoNtpStatusLockState OBJECT-TYPE
        SYNTAX		INTEGER {
						none(0),
						local(1),
						locked(2)
		}
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Indicates the NTP client lock status."
        ::= { genEquipUnitInfoNtpStatusEntry 4 }




genEquipUnitInfoNtpConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitInfoNtpConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows the configuration of the NTP (Network Time Protocol)."
        ::= { genEquipUnitInfoNTP 7 }

genEquipUnitInfoNtpConfigEntry OBJECT-TYPE
        SYNTAX GenEquipUnitInfoNtpConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Entry containing the data of the NTP configuration."
        INDEX { genEquipUnitInfoNtpConfigIndex }
        ::= { genEquipUnitInfoNtpConfigTable 1 }

GenEquipUnitInfoNtpConfigEntry ::=
            SEQUENCE {
		genEquipUnitInfoNtpConfigIndex
			INTEGER,
        genEquipUnitInfoNtpConfigAdmin
			EnableDisable,
		genEquipUnitInfoNtpConfigVersion
			INTEGER,
        genEquipUnitInfoNtpConfigServerIPaddress1
			IpAddress,
		genEquipUnitInfoNtpConfigServerIPaddress2
			IpAddress
	}

genEquipUnitInfoNtpConfigIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Index of the table."
        ::= { genEquipUnitInfoNtpConfigEntry 1 }

genEquipUnitInfoNtpConfigAdmin OBJECT-TYPE
        SYNTAX  EnableDisable
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "The administrative state of the NTP (Network Time Protocol) feature."
        ::= { genEquipUnitInfoNtpConfigEntry 2 }

genEquipUnitInfoNtpConfigVersion OBJECT-TYPE
        SYNTAX		INTEGER {
						ntpv3(1),
						ntpv4(2)
		}
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "The NTP (Network Time Protocol) version that is being used."
        ::= { genEquipUnitInfoNtpConfigEntry 3 }

genEquipUnitInfoNtpConfigServerIPaddress1 OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "NTP server IPv4 address that is required in first priority."
        ::= { genEquipUnitInfoNtpConfigEntry 4 }

genEquipUnitInfoNtpConfigServerIPaddress2 OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "NTP server IPv4 address that is required in second priority."
        ::= { genEquipUnitInfoNtpConfigEntry 5 }



--- Unit Serial information
genEquipUnitInventory OBJECT IDENTIFIER ::= { genEquipUnit 2}

genEquipUnitIDUSerialNumber	OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"IDU Serial number."
::= { genEquipUnitInventory 1 }

genEquipUnitIDUPartNumber	OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"IDU Part number."
::= { genEquipUnitInventory 2 }

-- Inventory common table inventory-common-table
--
genEquipUnitInventoryNG OBJECT IDENTIFIER ::= { genEquipUnitInventory 10}

genEquipInventoryTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipInventoryEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "inventory common table."
        ::= { genEquipUnitInventoryNG 1 }

genEquipInventoryEntry OBJECT-TYPE
        SYNTAX GenEquipInventoryEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "inventory common table Entry."
        INDEX { genEquipInventorySlotIndex }
        ::= { genEquipInventoryTable 1 }

GenEquipInventoryEntry ::=
            SEQUENCE {
		genEquipInventorySlotIndex
			INTEGER,
		genEquipInventoryCardName
			DisplayString,
        genEquipInventoryCardType
			InventoryCardType,
		genEquipInventoryCardSubType
			INTEGER,
        genEquipInventoryPartNumber
			DisplayString,
		genEquipInventorySerialNumber
			DisplayString,
		genEquipInventoryNumberWorkingDays
			INTEGER
	}

genEquipInventorySlotIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "slot index."
        ::= { genEquipInventoryEntry 1 }

genEquipInventoryCardName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Card name."
        ::= { genEquipInventoryEntry 2 }

genEquipInventoryCardType OBJECT-TYPE
        SYNTAX  InventoryCardType
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Card type."
        ::= { genEquipInventoryEntry 3 }

genEquipInventoryCardSubType OBJECT-TYPE
        SYNTAX		INTEGER
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Card sub type."
        ::= { genEquipInventoryEntry 4 }

genEquipInventoryPartNumber OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Part number."
        ::= { genEquipInventoryEntry 5 }

genEquipInventorySerialNumber OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Serial number."
        ::= { genEquipInventoryEntry 6 }

genEquipInventoryNumberWorkingDays OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "Number of working days."
        ::= { genEquipInventoryEntry 7 }

--
--

genEquipUnitLicenseInfo OBJECT IDENTIFIER ::= { genEquipUnit 3}

genEquipUnitLicenseType	OBJECT-TYPE
        SYNTAX		INTEGER {
						default(0),
						normal(1),
						demo(2)
		}
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The unit license type."
::= { genEquipUnitLicenseInfo 1 }

genEquipUnitLicenseCode	OBJECT-TYPE
        SYNTAX		DisplayString
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The user license code that determines the NE license rights."
::= { genEquipUnitLicenseInfo 2 }

genEquipUnitACMLicense	OBJECT-TYPE
        SYNTAX		AllowedNotAllowed
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The ACM rights for the NE on the current license"
::= { genEquipUnitLicenseInfo 3 }

genEquipUnitSwitchAppLicense	OBJECT-TYPE
        SYNTAX		INTEGER {
						single-pipe(0),
						switch(8)
		}
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The switch application rights for the NE on the current license.
		Switch is either managed-switch or metro switch."
::= { genEquipUnitLicenseInfo 4 }

genEquipUnitCapacityLicense	OBJECT-TYPE
        SYNTAX		INTEGER (10..510)
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The radio capacity allowed for the NE on the current license"
::= { genEquipUnitLicenseInfo 5 }

genEquipUnitLicenseDemoAdmin	OBJECT-TYPE
        SYNTAX		EnableDisable
        ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The Admin state of the demo mode."
::= { genEquipUnitLicenseInfo 6 }

genEquipUnitLicenseDemoTimer	OBJECT-TYPE
        SYNTAX		INTEGER
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The Demo timer in hours"
::= { genEquipUnitLicenseInfo 7}

genEquipUnitLicenseSyncU	OBJECT-TYPE
        SYNTAX		AllowedNotAllowed
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter indicates whether the synchronization unit license is allowed.
		 This license allows configuring synchronization sources for clock distribution."
::= { genEquipUnitLicenseInfo 8}

genEquipUnitLicenseNetworkResiliency	OBJECT-TYPE
        SYNTAX		AllowedNotAllowed
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter indicates whether the network resiliency license is allowed.
		 This license allows configuring xSTP protocols and TDM path protection."
::= { genEquipUnitLicenseInfo 9}

genEquipUnitLicenseTDMCapacity	OBJECT-TYPE
        SYNTAX		AllowedNotAllowed
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter indicates the number of TDM trails per radio
		 allowed by the current license, if allowed.
		 When TDM capacity license is allowed all scripts are allowed in the radio."
::= { genEquipUnitLicenseInfo 10}

genEquipUnitLicenseTDMCapacityValue	OBJECT-TYPE
        SYNTAX		INTEGER
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter indicates the number of TDM trails per radio
		 allowed by the current license, if relevant.
		 The TDM capacity value is not relevant if the genEquipUnitLicenseTDMCapacity is not allowed."
::= { genEquipUnitLicenseInfo 11}

genEquipUnitLicensePerUsage	OBJECT-TYPE
        SYNTAX		AllowedNotAllowed
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter indicates whether the per-usage license is allowed.
		 This license allows configuring all features and causes their use to be reported to NMS.
		 If the usage license is allowed, all other licenses are not relevant, as all the licenses are open
		 for configuration."
::= { genEquipUnitLicenseInfo 12}

genEquipUnitLicenseAsymScripts	OBJECT-TYPE
        SYNTAX		AllowedNotAllowed
        ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This parameter indicates whether the asymmetrical scripts license is allowed.
		This license allows loading asymmetrical scripts."
::= { genEquipUnitLicenseInfo 13}

genEquipUnitLicenseDateCode OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION "This parameter indicates the generation date and time of the current installed license. Format is 'YYYY-MM-DD,HH:MM:SS' (Hexadecimal)."
::= { genEquipUnitLicenseInfo 14}

genEquipUnitLicenseValidationNumber OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION "This parameter indicates the validation number of the current installed license. It can be used as a proof (for LMS) that the license key was installed in order to get a downgrade refund."
::= { genEquipUnitLicenseInfo 15}

--
-- License features table -  license-features-table
--

genEquipUnitLicenseFeatureTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitLicenseFeatureEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows for each feature what is its valid license and whether the license has been violated or not."
        ::= { genEquipUnitLicenseInfo 16 }

genEquipUnitLicenseFeatureEntry OBJECT-TYPE
        SYNTAX GenEquipUnitLicenseFeatureEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
        "This table entry shows for each feature what is its valid license and whether the license has been violated or not."
        INDEX { genEquipUnitLicenseFeatureId }
        ::= { genEquipUnitLicenseFeatureTable 1 }

GenEquipUnitLicenseFeatureEntry ::=
		SEQUENCE {
			genEquipUnitLicenseFeatureId
				INTEGER,
			genEquipUnitLicenseFeatureName
				DisplayString,
			genEquipUnitLicenseFeatureDescription
				DisplayString,
			genEquipUnitLicenseFeatureIsUsed
				LicenseGeneric,
			genEquipUnitLicenseFeatureIsAllowed
				LicenseGeneric,
			genEquipUnitLicenseViolationStatus
				INTEGER
}


genEquipUnitLicenseFeatureId		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"A unique number which indicates the Id of the feature"
::= { genEquipUnitLicenseFeatureEntry 1 }

genEquipUnitLicenseFeatureName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The name of the feature."
::= { genEquipUnitLicenseFeatureEntry 2 }

genEquipUnitLicenseFeatureDescription		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"A short description of the feature."
::= { genEquipUnitLicenseFeatureEntry 3 }

genEquipUnitLicenseFeatureIsUsed		OBJECT-TYPE
		SYNTAX 		LicenseGeneric
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Indicates whether the licensed feature is actually in used or its quantity that currently is in use."
::= { genEquipUnitLicenseFeatureEntry 4 }

genEquipUnitLicenseFeatureIsAllowed		OBJECT-TYPE
		SYNTAX 		LicenseGeneric
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Indicates whether a licensed feature is allowed."
::= { genEquipUnitLicenseFeatureEntry 5 }

genEquipUnitLicenseViolationStatus		OBJECT-TYPE
		SYNTAX 		INTEGER {
						ok(0),
						violated(1)
					}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Indicates whether the actual configuration violates the feature license."
::= { genEquipUnitLicenseFeatureEntry 6 }


-- License group continue
genEquipUnitLicenseCutThrough OBJECT-TYPE
		SYNTAX 		AllowedNotAllowed
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION "This parameter indicates whether the cut-through license is allowed.
		This license allows using the cut-through frames in the TM."
::= { genEquipUnitLicenseInfo 20}

genEquipUnitLicenseTdmInterfaceStandard OBJECT-TYPE
		SYNTAX 		INTEGER {
			ets1(0),
			ansi(1)
		}
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION "The TDM interface standard: ETSI (European) or ANSI (North American)."
::= { genEquipUnitLicenseInfo 21}


--- External Alarms
genEquipUnitExternalAlarms OBJECT IDENTIFIER ::= { genEquipUnit 4}

--- External Alarms - Alarm Input
genEquipUnitAlarmInput OBJECT IDENTIFIER ::= { genEquipUnitExternalAlarms 1}

genEquipUnitAlarmInputTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitAlarmInputEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "External input alarm table.
        Using the table to manage the admin state of the external input alarm (Enable or Disable it).
         Set a description and severity."
        ::= { genEquipUnitAlarmInput 1 }

genEquipUnitAlarmInputEntry OBJECT-TYPE
        SYNTAX GenEquipUnitAlarmInputEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
         "External input alarm table.
         Using the table to manage the admin state of the external input alarm (Enable or Disable it).
         Set a description and severity."
        INDEX { genEquipUnitAlarmInputCounter }
        ::= { genEquipUnitAlarmInputTable 1 }

GenEquipUnitAlarmInputEntry ::=
		SEQUENCE {
			genEquipUnitAlarmInputCounter
				INTEGER,
			genEquipUnitAlarmInputAdmin
            	EnableDisableSMI2,
			genEquipUnitAlarmInputText
              DisplayString,
			genEquipUnitAlarmInputSeverity
				InputSeverity,
			genEquipUnitAlarmInputState
				OffOn
}

genEquipUnitAlarmInputCounter		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"External input alarm table index."
::= { genEquipUnitAlarmInputEntry 1 }

genEquipUnitAlarmInputAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisableSMI2
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The Admin state of the External input alarm  table."
::= { genEquipUnitAlarmInputEntry 2 }

genEquipUnitAlarmInputText		OBJECT-TYPE
		SYNTAX 		DisplayString (SIZE (0..99))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"External input alarm description."
::= { genEquipUnitAlarmInputEntry 3 }

genEquipUnitAlarmInputSeverity		OBJECT-TYPE
		SYNTAX 		InputSeverity
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"External input alarm severity."
::= { genEquipUnitAlarmInputEntry 4 }

genEquipUnitAlarmInputState 		OBJECT-TYPE
		SYNTAX 		OffOn
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The state, raised or cleared, of the external alarm input."
::= { genEquipUnitAlarmInputEntry 5 }


--- External Alarms - Alarm Output
genEquipUnitAlarmOutput OBJECT IDENTIFIER ::= { genEquipUnitExternalAlarms 2}

genEquipUnitAlarmOutputTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitAlarmOutputEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "External output alarm table. using the table to manage the admin state of the external input alarm
        , read the status and set an output group."
        ::= { genEquipUnitAlarmOutput 1 }

genEquipUnitAlarmOutputEntry OBJECT-TYPE
        SYNTAX GenEquipUnitAlarmOutputEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "External output alarm table. using the table to manage the admin state of the external input alarm
       	 , read the status and set an output group."
        INDEX { genEquipUnitAlarmOutputCounter }
        ::= { genEquipUnitAlarmOutputTable 1 }

GenEquipUnitAlarmOutputEntry ::=
		SEQUENCE {
			genEquipUnitAlarmOutputCounter
				INTEGER,
			genEquipUnitAlarmOutputAdmin
            	INTEGER,
			genEquipUnitAlarmOutputStatus
                INTEGER,
			genEquipUnitAlarmOutputGroup
				INTEGER
}

genEquipUnitAlarmOutputCounter		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"External output alarm table index."
::= { genEquipUnitAlarmOutputEntry 1 }

genEquipUnitAlarmOutputAdmin		OBJECT-TYPE
		SYNTAX 		INTEGER {
						disable(0),
						enable(1),
						test(2)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The Admin state of the External output alarm. When 'Enabled', the functionality will be:
		(i) If no alarms are raised, the N.O. (normally open) contact will be opened,
		    while the N.C. (normally closed) contact will be connected to the COM contact.
		(ii) When an alarm is raised, or the system power is OFF,
		the N.O will be connected to the COM contact, while the N.C. contact will remain opened.
		When 'Test' option is selected, the dry contacts behave as an alarm is raised in the system,
		as described in case (ii) above."
::= { genEquipUnitAlarmOutputEntry 2 }

genEquipUnitAlarmOutputStatus		OBJECT-TYPE
		SYNTAX 		INTEGER {
				off(0),
				on(1),
				on-test(2)
		}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The current stauts of the External output alarm table."

::= { genEquipUnitAlarmOutputEntry 3 }

genEquipUnitAlarmOutputGroup		OBJECT-TYPE
		SYNTAX 		INTEGER {
				communication(1),
				quality-of-service(2),
				processing(3),
				equipment(4),
				environmental(5),
				all-groups(6)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The description of the group to which the external output alarm belongs."
::= { genEquipUnitAlarmOutputEntry 4 }

-- Shelf Managment
genEquipUnitShelf OBJECT IDENTIFIER ::= { genEquipUnit 5}

genEquipUnitShelfInstallation		OBJECT-TYPE
		SYNTAX 		INTEGER {
						standalone (0),
						chassisModule(1)       -- xc
		}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value indicates the NE installation type (standalone or module/card within a chassis). "
::= { genEquipUnitShelf 1 }

genEquipUnitShelfModuleRole		OBJECT-TYPE
		SYNTAX 		INTEGER {
						main(0),
						extention(1)
		}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value indicates the NE module role (main unit or extention)."
::= { genEquipUnitShelf 2 }

genEquipUnitShelfSlotLabel		OBJECT-TYPE
		SYNTAX 		DisplayString (SIZE(0..255))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Slot label is a user defined description for the module in the slot."
::= { genEquipUnitShelf 3 }

genEquipUnitShelfArchivesOperationStatus OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"Shelf-wide configuration archives [backup/restore] operation status.
		Used also when creating archive for unit info.
		Relevant only for main units.
		 - ready(0)
		 - inProgress(1),
		 - success(2),
		 - failure(3)"
::= { genEquipUnitShelf 4 }

genEquipUnitShelfManagmentTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfManagmentEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table contains information about the IDUs that may be in the shelf (population, communication status, most severe alarm) and
        provides the ability to backup/restore SW for each IDU.
	    Relevant only for main units."
        ::= { genEquipUnitShelf 5 }

genEquipUnitShelfManagmentEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfManagmentEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table keeps the shelf information on all the modules in the shelf (population, status, most severe alarm) and
        provides the ability for backup/restore command or SW download.
		Relevant only for main units."
        INDEX { genEquipUnitShelfManagmentSlot }
        ::= { genEquipUnitShelfManagmentTable 1 }

GenEquipUnitShelfManagmentEntry ::=
		SEQUENCE {
			genEquipUnitShelfManagmentSlot
				SlotId,
			genEquipUnitShelfManagmentSlotPopulation
            	INTEGER,
			genEquipUnitShelfManagmentCommunicationStatus
              	INTEGER,
			genEquipUnitShelfManagmentSlotMostSevereAlarm
				Severity,
			genEquipUnitShelfManagmentMngSwCommand
				SwCommand,
			genEquipUnitShelfManagmentSlotReset
				OffOn,
			genEquipUnitShelfManagmentSlotIp
				IpAddress
}

genEquipUnitShelfManagmentSlot		OBJECT-TYPE
		SYNTAX 		SlotId
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value is the slot number used as an index for this table."
::= { genEquipUnitShelfManagmentEntry 1 }

genEquipUnitShelfManagmentSlotPopulation		OBJECT-TYPE
		SYNTAX 		INTEGER {
						not-present(0),
						present(1)
		}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Slot population state, which indicates if the slot is occupied or not.
		Relevant only for main units."
::= { genEquipUnitShelfManagmentEntry 2 }

genEquipUnitShelfManagmentCommunicationStatus		OBJECT-TYPE
		SYNTAX 		INTEGER {
						communicationDown(0),	-- not alive
						communicationUp(1)		-- alive
		}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value indicates if the slot can be reached or not.
		Relevant only for main units."
::= { genEquipUnitShelfManagmentEntry 3 }

genEquipUnitShelfManagmentSlotMostSevereAlarm		OBJECT-TYPE
		SYNTAX 		Severity
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value is the most severe alarm for the module on this slot.
		Relevant only for main unit."
::= { genEquipUnitShelfManagmentEntry 4 }

genEquipUnitShelfManagmentMngSwCommand 		OBJECT-TYPE
		SYNTAX 		SwCommand
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"The command to be executed to manage SW versions.
		Relevant only for main units.
		Possible values are:
		 - noOperation(0)
		 - downloadUpgradeVersion(1)
		 - upgrade(2)
		 - rollback(3)
		 - downgrade(4)
		 - downloadDowngradeVersion(5)"
::= { genEquipUnitShelfManagmentEntry 5 }

genEquipUnitShelfManagmentSlotReset 		OBJECT-TYPE
		SYNTAX 		OffOn
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"This parameter Allows resetting specific unit according to genEquipUnitShelfManagmentSlot table index."
::= { genEquipUnitShelfManagmentEntry 6 }

genEquipUnitShelfManagmentSlotIp 		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"Slot SNMP Management IP address."
::= { genEquipUnitShelfManagmentEntry 7 }

genEquipUnitShelfReset  		OBJECT-TYPE
		SYNTAX 		OffOn
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"This parameter allows resetting of entire shelf."
::= { genEquipUnitShelf 6 }

genEquipUnitshelfAllODUAdmin  		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"The unit is configured as all outdoor unit, enable or disable it."
::= { genEquipUnitShelf 7 }


genEquipUnitShelfSlotConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfSlotConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Slot configuration table."
        ::= { genEquipUnitShelf 8 }

genEquipUnitShelfSlotConfigEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfSlotConfigEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows configuration of card population parameters."
        INDEX { genEquipUnitShelfSlotConfigSlotID }
        ::= { genEquipUnitShelfSlotConfigTable 1 }

GenEquipUnitShelfSlotConfigEntry ::=
		SEQUENCE {
			genEquipUnitShelfSlotConfigSlotID
				SlotId,
			genEquipUnitShelfSlotConfigExpectedCardType
            	InventoryCardType,
			genEquipUnitShelfSlotConfigLabel
              	DisplayString,
			genEquipUnitShelfSlotConfigAdmin
				EnableDisable,
			genEquipUnitShelfSlotConfigSlotReset
				OffOn
}

genEquipUnitShelfSlotConfigSlotID	OBJECT-TYPE
		SYNTAX 		SlotId
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value is the slot number used as an index for this table."
::= { genEquipUnitShelfSlotConfigEntry 1 }

genEquipUnitShelfSlotConfigExpectedCardType	OBJECT-TYPE
		SYNTAX 		InventoryCardType
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Expected card type in current slot."
::= { genEquipUnitShelfSlotConfigEntry 2 }

genEquipUnitShelfSlotConfigLabel	OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Slot label."
::= { genEquipUnitShelfSlotConfigEntry 3 }

genEquipUnitShelfSlotConfigAdmin	OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Slot admin state."
::= { genEquipUnitShelfSlotConfigEntry 4 }

genEquipUnitShelfSlotConfigSlotReset 		OBJECT-TYPE
		SYNTAX 		OffOn
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"This parameter Allows resetting specific unit according to genEquipUnitShelfManagmentSlot table index."
::= { genEquipUnitShelfSlotConfigEntry 5 }



genEquipUnitShelfTccConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfTccConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "TCC configuration table."
        ::= { genEquipUnitShelf 9 }

genEquipUnitShelfTccConfigEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfTccConfigEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows configuration of TCC population parameters."
        INDEX { genEquipUnitShelfTccConfigSlotID }
        ::= { genEquipUnitShelfTccConfigTable 1 }

GenEquipUnitShelfTccConfigEntry ::=
		SEQUENCE {
			genEquipUnitShelfTccConfigSlotID
				SlotId,
			genEquipUnitShelfTccConfigExpectedCardType
            	InventoryCardType,
			genEquipUnitShelfTccConfigLabel
              	DisplayString,
			genEquipUnitShelfTccConfigAdmin
				EnableDisable,
			genEquipUnitShelfTccConfigReset
				OffOn
}

genEquipUnitShelfTccConfigSlotID	OBJECT-TYPE
		SYNTAX 		SlotId
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value is the TCC slot number used as an index for this table."
::= { genEquipUnitShelfTccConfigEntry 1 }

genEquipUnitShelfTccConfigExpectedCardType	OBJECT-TYPE
		SYNTAX 		InventoryCardType
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Expected card type in current TCC slot."
::= { genEquipUnitShelfTccConfigEntry 2 }

genEquipUnitShelfTccConfigLabel	OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"TCC label."
::= { genEquipUnitShelfTccConfigEntry 3 }

genEquipUnitShelfTccConfigAdmin	OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"TCC admin state."
::= { genEquipUnitShelfTccConfigEntry 4 }

genEquipUnitShelfTccConfigReset 		OBJECT-TYPE
		SYNTAX 		OffOn
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"This parameter Allows resetting specific unit according to genEquipUnitShelfManagmentSlot table index."
::= { genEquipUnitShelfTccConfigEntry 5 }

--
-- Slot status table - generic-cards-status-table
--

genEquipUnitShelfSlotStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfSlotStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table contains information about the status of the cards currently inserted in the system."
        ::= { genEquipUnitShelf 10 }

genEquipUnitShelfSlotStatusEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfSlotStatusEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table entry contains information about the status of the cards currently inserted in the system."
        INDEX { genEquipUnitShelfSlotStatusSlotID }
        ::= { genEquipUnitShelfSlotStatusTable 1 }

GenEquipUnitShelfSlotStatusEntry ::=
		SEQUENCE {
			genEquipUnitShelfSlotStatusSlotID
				SlotId,
			genEquipUnitShelfSlotStatusOccupancy
            	CardOccupancy,
			genEquipUnitShelfSlotStatusAllowedCardTypes
              	Counter64,
			genEquipUnitShelfSlotStatusCardType
				InventoryCardType,
			genEquipUnitShelfSlotStatusOperationalState
				OperState,
			genEquipUnitShelfSlotStatusCommunication
				DownUp
}

genEquipUnitShelfSlotStatusSlotID	OBJECT-TYPE
		SYNTAX 		SlotId
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Slot where PW-LIC is inserted."
::= { genEquipUnitShelfSlotStatusEntry 1 }

genEquipUnitShelfSlotStatusOccupancy	OBJECT-TYPE
		SYNTAX 		CardOccupancy
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Slot Occupancy."
::= { genEquipUnitShelfSlotStatusEntry 2 }

genEquipUnitShelfSlotStatusAllowedCardTypes	OBJECT-TYPE
		SYNTAX 		Counter64
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"shelf slot allowed card types."
::= { genEquipUnitShelfSlotStatusEntry 3 }

genEquipUnitShelfSlotStatusCardType	OBJECT-TYPE
		SYNTAX 		InventoryCardType
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Shelf Slot card type."
::= { genEquipUnitShelfSlotStatusEntry 4 }

genEquipUnitShelfSlotStatusOperationalState 		OBJECT-TYPE
		SYNTAX 		OperState
		ACCESS 		read-only
		STATUS 		mandatory
		DESCRIPTION
		"Operational State."
::= { genEquipUnitShelfSlotStatusEntry 5 }

genEquipUnitShelfSlotStatusCommunication 		OBJECT-TYPE
		SYNTAX 		DownUp
		ACCESS 		read-only
		STATUS 		mandatory
		DESCRIPTION
		"Slot Operational State."
::= { genEquipUnitShelfSlotStatusEntry 6 }

--
-- traffic-and-control-status-cards-table - shelf-TCC-status-cards-table
--

genEquipUnitShelfTccStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfTccStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table contains information about the status of the TCC currently inserted in the system."
        ::= { genEquipUnitShelf 11 }

genEquipUnitShelfTccStatusEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfTccStatusEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table entry contains information about the status of the TCC currently inserted in the system."
        INDEX { genEquipUnitShelfTccStatusSlotID }
        ::= { genEquipUnitShelfTccStatusTable 1 }

GenEquipUnitShelfTccStatusEntry ::=
		SEQUENCE {
			genEquipUnitShelfTccStatusSlotID
				SlotId,
			genEquipUnitShelfTccStatusOccupancy
            	CardOccupancy,
			genEquipUnitShelfTccStatusAllowedCardTypes
              	Counter64,
			genEquipUnitShelfTccStatusCardType
				InventoryCardType,
			genEquipUnitShelfTccStatusOperationalState
				OperState,
			genEquipUnitShelfTccStatusCommunication
				DownUp
}

genEquipUnitShelfTccStatusSlotID	OBJECT-TYPE
		SYNTAX 		SlotId
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Slot where PW-LIC is inserted."
::= { genEquipUnitShelfTccStatusEntry 1 }

genEquipUnitShelfTccStatusOccupancy	OBJECT-TYPE
		SYNTAX 		CardOccupancy
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Slot Occupancy."
::= { genEquipUnitShelfTccStatusEntry 2 }

genEquipUnitShelfTccStatusAllowedCardTypes	OBJECT-TYPE
		SYNTAX 		Counter64
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"shelf slot allowed card types."
::= { genEquipUnitShelfTccStatusEntry 3 }

genEquipUnitShelfTccStatusCardType	OBJECT-TYPE
		SYNTAX 		InventoryCardType
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Shelf Slot card type."
::= { genEquipUnitShelfTccStatusEntry 4 }

genEquipUnitShelfTccStatusOperationalState 		OBJECT-TYPE
		SYNTAX 		OperState
		ACCESS 		read-only
		STATUS 		mandatory
		DESCRIPTION
		"Operational State."
::= { genEquipUnitShelfTccStatusEntry 5 }

genEquipUnitShelfTccStatusCommunication 		OBJECT-TYPE
		SYNTAX 		DownUp
		ACCESS 		read-only
		STATUS 		mandatory
		DESCRIPTION
		"Slot Operational State."
::= { genEquipUnitShelfTccStatusEntry 6 }

--
-- Alarms Counter Table - alarm-services-per-slot-counter-table
--
genEquipUnitShelfManagmentSeverityTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfManagmentSeverityEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table indicates the number of current raised alarms per slot."
        ::= { genEquipUnitShelf 12 }

genEquipUnitShelfManagmentSeverityEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfManagmentSeverityEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table entry indicates the number of current raised alarms per slot."
        INDEX { genEquipUnitShelfManagmentSeveritySlot }
        ::= { genEquipUnitShelfManagmentSeverityTable 1 }

GenEquipUnitShelfManagmentSeverityEntry ::=
		SEQUENCE {
			genEquipUnitShelfManagmentSeveritySlot
				INTEGER,
			genEquipUnitShelfManagmentSeverityCritical
            	INTEGER,
			genEquipUnitShelfManagmentSeverityMajor
              	INTEGER,
			genEquipUnitShelfManagmentSeverityMinor
				INTEGER,
			genEquipUnitShelfManagmentSeverityWarning
				INTEGER,
			genEquipUnitShelfManagmentSeverityIndeterminate
				INTEGER
}

genEquipUnitShelfManagmentSeveritySlot	OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The slot ID number that identifies it."
::= { genEquipUnitShelfManagmentSeverityEntry 1 }

genEquipUnitShelfManagmentSeverityCritical	OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The number of alarms with 'Critical' severity."
::= { genEquipUnitShelfManagmentSeverityEntry 2 }

genEquipUnitShelfManagmentSeverityMajor	OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The number of alarms with 'Major' severity."
::= { genEquipUnitShelfManagmentSeverityEntry 3 }

genEquipUnitShelfManagmentSeverityMinor	OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The number of alarms with 'Minor' severity."
::= { genEquipUnitShelfManagmentSeverityEntry 4 }

genEquipUnitShelfManagmentSeverityWarning 		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS 		read-only
		STATUS 		mandatory
		DESCRIPTION
		"The number of alarms with 'Warning' severity."
::= { genEquipUnitShelfManagmentSeverityEntry 5 }

genEquipUnitShelfManagmentSeverityIndeterminate 		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS 		read-only
		STATUS 		mandatory
		DESCRIPTION
		"The number of alarms with 'Indeterminate' severity."
::= { genEquipUnitShelfManagmentSeverityEntry 6 }

--
-- pdc-and-fan-cards-table -  pdc-and-fan-cards-table
--

genEquipUnitShelfPdcFanStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfPdcFanStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows for fun card."
        ::= { genEquipUnitShelf 13 }

genEquipUnitShelfPdcFanStatusEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfPdcFanStatusEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
        "This table shows for fun card."
        INDEX { genEquipUnitShelfPdcFanStatusPdcFanId }
        ::= { genEquipUnitShelfPdcFanStatusTable 1 }

GenEquipUnitShelfPdcFanStatusEntry ::=
		SEQUENCE {
			genEquipUnitShelfPdcFanStatusPdcFanId
				INTEGER,
			genEquipUnitShelfPdcFanStatusPdcFanExMonitor
				EnableDisable,
			genEquipUnitShelfPdcFanStatusPdcFanOccupancy
				CardOccupancy,
			genEquipUnitShelfPdcFanStatusPdcFanCardType
				InventoryCardType
}


genEquipUnitShelfPdcFanStatusPdcFanId		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"pdc-fan-id"
::= { genEquipUnitShelfPdcFanStatusEntry 1 }

genEquipUnitShelfPdcFanStatusPdcFanExMonitor		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"pdc-fan-existent monitor."
::= { genEquipUnitShelfPdcFanStatusEntry 2 }

genEquipUnitShelfPdcFanStatusPdcFanOccupancy		OBJECT-TYPE
		SYNTAX 		CardOccupancy
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"card_occupancy."
::= { genEquipUnitShelfPdcFanStatusEntry 3 }

genEquipUnitShelfPdcFanStatusPdcFanCardType		OBJECT-TYPE
		SYNTAX 		InventoryCardType
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Indicates whether the licensed feature is actually in used or its quantity that currently is in use."
::= { genEquipUnitShelfPdcFanStatusEntry 4 }

--
-- ABC MUX configuration table - shelf-abc-mux-configuration-table
--

genEquipUnitShelfAbcMuxTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipUnitShelfAbcMuxEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows enabling/disabling each ABC MUX group.
		ABC groups can be defined on top of these MUX."
        ::= { genEquipUnitShelf 14 }

genEquipUnitShelfAbcMuxEntry OBJECT-TYPE
        SYNTAX GenEquipUnitShelfAbcMuxEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
        "This table entry allows enabling/disabling each ABC MUX group.
		ABC groups can be defined on top of these MUX."
        INDEX { genEquipUnitShelfAbcMuxNumber }
        ::= { genEquipUnitShelfAbcMuxTable 1 }

GenEquipUnitShelfAbcMuxEntry ::=
		SEQUENCE {
			genEquipUnitShelfAbcMuxNumber
				INTEGER,
			genEquipUnitShelfAbcMuxAdmin
				EnableDisable
}


genEquipUnitShelfAbcMuxNumber		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Identifier for each of the possible MUXs which choose between using the ABC trunks
		or the port for multi-directional radios"
::= { genEquipUnitShelfAbcMuxEntry 1 }

genEquipUnitShelfAbcMuxAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Enables the MUX (for ABC) or disables it (for multi-directional radios)."
::= { genEquipUnitShelfAbcMuxEntry 2 }

--
-- Scalars
--
genEquipUnitShelfMultiplexTrafficSource		OBJECT-TYPE
		SYNTAX 		INTEGER (1..12)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The parameter multiplex traffic between slot 1 and slot 12."
::= { genEquipUnitShelf 23 }

genEquipUnitShelfMaskUnderVoltageAlarmFirstPowerFeed		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Mask undervoltage alarm on 1st DC power feed."
::= { genEquipUnitShelf 24 }

genEquipUnitShelfMaskUnderVoltageAlarmSecondPowerFeed		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Mask undervoltage alarm on 2nd DC power feed"
::= { genEquipUnitShelf 25 }


-- Protection
genEquipProtection	OBJECT IDENTIFIER ::= {genEquipUnit 6}

genEquipProtectionAdmin OBJECT-TYPE
        SYNTAX INTEGER {
				protection1Plus1Hsb(2),
				protectionDisable(3),
				protection2Plus2Hsb(4),
				protection2Plus0Hsb(5)
		}
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The Admin mode of the protection.
         In case of protection2Plus2Hsb this parameter should be configured in
         the main and extension slots."
        ::= { genEquipProtection 1 }

genEquipProtectionMode OBJECT-TYPE
        SYNTAX INTEGER {
				active(0),
				standby(1)
		}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "In protection mode, the card can be either active or standby"
        ::= { genEquipProtection 2 }

genEquipProtectionMateIPAddr OBJECT-TYPE
        SYNTAX IpAddress
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Same parameter as genEquipNetworkMateIp.
        In case of 1+1 protection it is the protected card (Mate) IP address.
        In case of 2+2 protection it is the IP address of the protected unit."
        ::= { genEquipProtection 3 }

genEquipProtectionMateMACAddr OBJECT-TYPE
        SYNTAX MacAddress
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "In case of 1+1 protection it is protected card (Mate) MAC address
        In case of 2+2 protection it is the IP address of the protected unit."
        ::= { genEquipProtection 4 }

genEquipProtectionRadioExcessiveBERSwitch OBJECT-TYPE
        SYNTAX EnableDisable
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The Admin state of an Excessive BER threshold. When enabled, crossing the Excessive BER threshold will cause
        a protection switch."
        ::= { genEquipProtection 5 }

genEquipProtectionLockout OBJECT-TYPE
        SYNTAX OffOn
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter provides the ability to lock the mode of each card in the protection,
         meaning to force the fact that protection switch will not occur."
        ::= { genEquipProtection 6 }

genEquipProtectionForceSwitch OBJECT-TYPE
        SYNTAX OffOn
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter is a command that forces a protection switch regardless of the state of the standby card."
        ::= { genEquipProtection 7 }

genEquipProtectionManualSwitch OBJECT-TYPE
        SYNTAX OffOn
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter is a command that performs manaual protection switch."
        ::= { genEquipProtection 8 }

genEquipProtectionCopyToMateComand OBJECT-TYPE
        SYNTAX OffOn
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Setting this parameter to On will copy configured parameters from the active to the standby unit."
        ::= { genEquipProtection 9 }

genEquipProtectionCopyToMateStatus OBJECT-TYPE
        SYNTAX ProgressStatus
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "This parameter represents the status of the copy-to-mate command when protection is enabled.
   		 - ready(0)
		 - inProgress(1),
		 - success(2),
		 - failure(3)"
        ::= { genEquipProtection 10 }

genEquipProtectionMultiUnitLAGAdmin OBJECT-TYPE
        SYNTAX EnableDisable
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter enables the Multi Unit LAG feature.
		It can be activated only in Single Pipe configuration and only when protection is active (either 1+1 or 2+2)."
        ::= { genEquipProtection 11 }

genEquipProtectionRevertiveAdmin OBJECT-TYPE
        SYNTAX EnableDisable
        ACCESS read-write
        STATUS mandatory
		DESCRIPTION
		"Enable/Disable revertive protection mode. In revertive protection mode one IDU will be defined as a Primary and the other will be Secondary.
		Revertive protection will always tend to make the Primary IDU active unless it has a hardware failure."
		::= { genEquipProtection 12 }

genEquipProtectionRevertivePrimaryIDU OBJECT-TYPE
         SYNTAX		INTEGER {
			lower(0),
			upper(1)
		}
        ACCESS read-write
        STATUS mandatory
		DESCRIPTION
		"Decides whether upper or lower IDU will be primary. When placed in a shelf, the upper will be the upper slot in the shelf.
		When working in stand-alone mode, the IDU with the highest IP address will be considered as upper."
		::= { genEquipProtection 13 }

genEquipProtectionRevertiveMinTimer OBJECT-TYPE
		SYNTAX		INTEGER (1..30)
        ACCESS read-write
        STATUS mandatory
		DESCRIPTION
		"Minimal revertive protection timer period."
 		::= { genEquipProtection 14 }

genEquipProtectionRevertiveMaxNumOfTries OBJECT-TYPE
		SYNTAX		INTEGER (0..10)
        ACCESS read-write
        STATUS mandatory
		DESCRIPTION
		"Maximal number of revertive protection tries. Used to prevent instability. Revertive protection will give up trying to switch
		to the primary standby IDU path after the maximal number of tries. Setting Zero value will enable revertive protection switch only
		if the last protection switch was due to a local equipment failure."
 		::= { genEquipProtection 15 }

genEquipProtectionRevertiveTimerMultiplier OBJECT-TYPE
		SYNTAX		INTEGER (1..10)
        ACCESS read-write
        STATUS mandatory
		DESCRIPTION
		"A factor that increases the revertive timer period after each try. Setting 1 value will leave the timer period constantly at its minimal value."
 		::= { genEquipProtection 16 }

genEquipProtectionAspRevertive OBJECT-TYPE
		SYNTAX		EnableDisable
        ACCESS read-write
        STATUS mandatory
		DESCRIPTION
		"n Smart Pipe HSB mode , this parameter causes the Ethernet line port to be muted upon a complete failure of
		the radio signal (in both active and standby units)"
 		::= { genEquipProtection 17 }


--
genEquipDiversity	OBJECT IDENTIFIER ::= {genEquipUnit 7}

genEquipDiversityType OBJECT-TYPE
        SYNTAX INTEGER {
				none(1),
				space-diversity (2),
				frequency-diversity (3)
		}
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "this parameter sets the type of hitless diversity to be used.
		Notice that only if protection is enabled this has any effect."
        ::= { genEquipDiversity 1 }

genEquipDiversityRevertiveMode OBJECT-TYPE
        SYNTAX INTEGER {
        			non-revertive(1),
        			revertive(2)
        }
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "this parameter sets whether hitless switches are revertive in the event that
		the primary radio channel doesn't have any errors."
        ::= { genEquipDiversity 2 }

genEquipDiversityPrimaryRadio OBJECT-TYPE
        SYNTAX INTEGER {
				upper-radio(1),
				lower-radio(2)
		}
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "this parameter sets which IDU contains the primary radio channel to revert to."
        ::= { genEquipDiversity 3 }

genEquipDiversityRevertiveTimer OBJECT-TYPE
        SYNTAX INTEGER (1..15)
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter sets the amount of seconds that must pass without a failure in
		the primary radio before doing a revertive switch."
        ::= { genEquipDiversity 4 }

genEquipDiversityForceActive OBJECT-TYPE
        SYNTAX INTEGER {
				none(0),
				local-radio(1),
				mate-radio(2)
		}
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter allows forcing incoming traffic to be taken from either of
		the radios (or none) for testing purposes."
        ::= { genEquipDiversity 5 }

genEquipDiversitySwitchCounter OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "This parameter shows the number of hitless switches performed since last time counter was cleared."
        ::= { genEquipDiversity 6 }

genEquipDiversitySwitchCounterClear OBJECT-TYPE
        SYNTAX OffOn
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter allows clearing of switch counter."
        ::= { genEquipDiversity 7 }

genEquipDiversityReceiveRadio OBJECT-TYPE
        SYNTAX INTEGER {
				lower-idu(0),
				upper-idu(1)
		}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "This parameter shows from which radio the traffic is currently being taken."
        ::= { genEquipDiversity 8 }


-- Faults
genEquipFault OBJECT IDENTIFIER ::= { genEquip 3}

-- Current Alarms
genEquipCurrentAlarm OBJECT IDENTIFIER ::= {genEquipFault 1}

genEquipCurrentAlarmLastChangeCounter OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "This counter is initialized with a random number after resest,
        and incremented on each change in the CurrentAlarmTable"
        ::= { genEquipCurrentAlarm 1 }

genEquipCurrentAlarmTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipCurrentAlarmEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table includes the current alarm (when a RAISED trap is
          sent, an alarm is added to the table, when a CLEAR trap is sent,
          the alarm is removed)."
        ::= { genEquipCurrentAlarm 2 }

genEquipCurrentAlarmEntry OBJECT-TYPE
        SYNTAX GenEquipCurrentAlarmEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Entry containing the info of current RAISED traps."
        INDEX { genEquipCurrentAlarmCounter }
        ::= { genEquipCurrentAlarmTable 1 }

GenEquipCurrentAlarmEntry ::=
		SEQUENCE {
			genEquipCurrentAlarmCounter
				INTEGER,
			genEquipCurrentAlarmRaisedTimeT
            	INTEGER,
			genEquipCurrentAlarmId
              INTEGER,
			genEquipCurrentAlarmName
				DisplayString,
			genEquipCurrentAlarmInstance
				INTEGER,
			genEquipCurrentAlarmSeverity
				Severity,
			genEquipCurrentAlarmIfIndex
				INTEGER,
			genEquipCurrentAlarmModule
				DisplayString,
			genEquipCurrentAlarmDesc
				DisplayString,
			genEquipCurrentAlarmProbableCause
				DisplayString,
			genEquipCurrentAlarmCorrectiveActions
				DisplayString,
			genEquipCurrentAlarmState
				INTEGER,
			genEquipCurrentAlarmSlotId
				INTEGER,
			genEquipCurrentAlarmAdditionalInfo
				DisplayString,
			genEquipCurrentAlarmUserText
				DisplayString
}

genEquipCurrentAlarmCounter OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "A running counter of open alarms, incremented upon each new alarm."
        ::= { genEquipCurrentAlarmEntry 1 }


genEquipCurrentAlarmRaisedTimeT OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The rasied time of this alarm.
         Time in seconds since January 1, 1970 00:00 UTC."
        ::= { genEquipCurrentAlarmEntry 2 }

genEquipCurrentAlarmId OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Alarm ID. List of alarm IDs is available in the MIB Ref. Guide."
        ::= { genEquipCurrentAlarmEntry 3 }

genEquipCurrentAlarmName OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Alarm name (the parameter and entity in the CLI) for example
        'radio/framer/radio-lof'"
        ::= { genEquipCurrentAlarmEntry 4 }

genEquipCurrentAlarmInstance OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "In the case of alarms that may have several appereances (at interfaces, channels, VCs, etc.), this parameter indicates the instance of the alarm."
        ::= { genEquipCurrentAlarmEntry 5 }

genEquipCurrentAlarmSeverity OBJECT-TYPE
        SYNTAX  Severity
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        "The current Alarm severity
        the values can be:
         - indeterminate(0)
		 - 	critical(1)
		 - 	major(2)
		 - 	minor(3)
		 - 	warning(4)
		 - 	cleared(5)"
        DEFVAL { indeterminate }
        ::= { genEquipCurrentAlarmEntry 6 }


genEquipCurrentAlarmIfIndex OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "Interface Index where the alarm occurred, alarms that are
         not associated with specific interfaces will have the following value:
         For a non-interface alarm, the value is: 65001"
        ::= { genEquipCurrentAlarmEntry 7 }

genEquipCurrentAlarmModule OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The module of the alarm.
        Possible values are 'IDU' or 'RFU'."
        ::= {genEquipCurrentAlarmEntry 8}

genEquipCurrentAlarmDesc OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The alarm description (same as the description in the sent trap)."
        ::= { genEquipCurrentAlarmEntry 9}

genEquipCurrentAlarmProbableCause OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The alarm probable cause."
        ::= { genEquipCurrentAlarmEntry 10}

genEquipCurrentAlarmCorrectiveActions OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The alarm corrective actions."
        ::= { genEquipCurrentAlarmEntry 11 }

genEquipCurrentAlarmState OBJECT-TYPE
		SYNTAX		INTEGER{
					cleared(0),
					raised(1),
					event(2)
					}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The state of the alarm (cleared or rasied)."
        ::= { genEquipCurrentAlarmEntry 12 }

genEquipCurrentAlarmSlotId OBJECT-TYPE
		SYNTAX 		SlotId
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The ID of the slot originating the alarm."
        ::= { genEquipCurrentAlarmEntry 13 }

genEquipCurrentAlarmAdditionalInfo OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "Additional information that may further qualify the source of the alarm."
        ::= { genEquipCurrentAlarmEntry 14 }

genEquipCurrentAlarmUserText OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "An additional description of the event or fault, or any other information that user wants to attach to it."
        ::= { genEquipCurrentAlarmEntry 15 }

-- Just like current alarm
genEquipMostSevereAlarm  OBJECT-TYPE
        SYNTAX  Severity
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The severity of the most severe alarm in the system"
        ::= { genEquipCurrentAlarm 3 }

genEquipAlarmConfigDefault  OBJECT-TYPE
        SYNTAX  OffOn
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Allows to revert back to the default values of all alarm configurations"
        ::= { genEquipCurrentAlarm 4 }

--
-- Alarm Configuration - alarm-services-configuration-table
--
genEquipAlarmConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipAlarmConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows system alarms configuration."
        ::= { genEquipCurrentAlarm 5 }

genEquipAlarmConfigEntry OBJECT-TYPE
        SYNTAX GenEquipAlarmConfigEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "This table entry allows system alarms configuration."
        INDEX { genEquipAlarmConfigId }
        ::= { genEquipAlarmConfigTable 1 }

GenEquipAlarmConfigEntry ::=
		SEQUENCE {
			genEquipAlarmConfigId
				INTEGER,
			genEquipAlarmConfigSeverity
            	Severity,
			genEquipAlarmConfigDescr
				DisplayString,
			genEquipAlarmConfigAdditionalText
				DisplayString,
			genEquipAlarmServiceAffect
				OffOn
}

genEquipAlarmConfigId OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "he alarm unique Id number that identifies it."
        ::= { genEquipAlarmConfigEntry 1 }


genEquipAlarmConfigSeverity OBJECT-TYPE
        SYNTAX Severity
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The perceived severity of the raised alarm."
        ::= { genEquipAlarmConfigEntry 2 }

genEquipAlarmConfigDescr OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The description of the raised alarm."
        ::= { genEquipAlarmConfigEntry 3 }

genEquipAlarmConfigAdditionalText OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "An additional description of the fault, or any other information that user wants to attach to it."
        ::= { genEquipAlarmConfigEntry 4 }

genEquipAlarmServiceAffect OBJECT-TYPE
        SYNTAX OffOn
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Indicates whether the raised alarm affects a service."
        ::= { genEquipAlarmConfigEntry 5 }



-- Traps configuration.
genEquipTrapCfg OBJECT IDENTIFIER ::= {genEquipFault 2}

genEquipTrapCfgMgrTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipTrapCfgMgrEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table indicates the Trap manager list."
        ::= { genEquipTrapCfg 1 }

genEquipTrapCfgMgrEntry OBJECT-TYPE
        SYNTAX GenEquipTrapCfgMgrEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Entry containing the manager IP addresses and the trap filter options."
        INDEX { genEquipTrapCfgMgrId}
        ::= { genEquipTrapCfgMgrTable 1 }

GenEquipTrapCfgMgrEntry ::=
            SEQUENCE {
        genEquipTrapCfgMgrId					INTEGER,
        genEquipTrapCfgMgrAdmin					EnableDisable,
        genEquipTrapCfgMgrIP    				IpAddress,
        genEquipTrapCfgMgrPort	            	INTEGER,
        genEquipTrapCfgMgrName					DisplayString,
		genEquipTrapCfgMgrCommunity         	DisplayString,
        genEquipTrapCfgMgrSeverityFilter		INTEGER,
        genEquipTrapCfgMgrStatusChangeFilter	OffOn,
		genEquipTrapCfgMgrCLLI 					DisplayString,
		genEquipTrapCfgMgrHeartbeatPeriod		INTEGER,
		genEquipTrapCfgMgrIPv6                  OCTET STRING,
		genEquipTrapCfgMgrV3User                DisplayString
	}

genEquipTrapCfgMgrId OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS 	mandatory
        DESCRIPTION
        "This value indicates an index of a specific manager"
        ::= { genEquipTrapCfgMgrEntry 1 }

genEquipTrapCfgMgrAdmin OBJECT-TYPE
        SYNTAX  EnableDisable
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "This value indicates an administrative state of a specific manager.
        disable - will not send traps to this trap manager."
        ::= { genEquipTrapCfgMgrEntry 2 }

genEquipTrapCfgMgrIP OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "This value indicates the manager's IP address."
        ::= { genEquipTrapCfgMgrEntry 3 }

genEquipTrapCfgMgrPort        OBJECT-TYPE
        SYNTAX  INTEGER (70..65535)
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "The Port for sending the trap for each manager (possible values: 70-65535)"
        DEFVAL { 162 }
        ::= { genEquipTrapCfgMgrEntry 4 }

genEquipTrapCfgMgrName        OBJECT-TYPE
        SYNTAX  DisplayString (SIZE(0..30))
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "The name of the manager configured to receive the traps"
        ::= { genEquipTrapCfgMgrEntry 5 }

genEquipTrapCfgMgrCommunity		OBJECT-TYPE
        SYNTAX  DisplayString (SIZE(0..30))
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "The manager trap community configured."
        ::= { genEquipTrapCfgMgrEntry 6 }

 genEquipTrapCfgMgrSeverityFilter OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "This is a bit mask value for masking traps according to its severity.
         There is a bit for each severity. If the relevant bit is on (1),
         the Agent will send traps with this severity. It won't send traps if the
         bit is off (0).
         The list of bits from LSB to MSB:
			Bit 1 (LSB)	- indeterminate
			Bit 2		- critical
			Bit 3		- major
			Bit 4		- minor
			Bit 5		- warning
			Bit 6		- cleared "
        ::= { genEquipTrapCfgMgrEntry 7 }

genEquipTrapCfgMgrStatusChangeFilter OBJECT-TYPE
        SYNTAX  OffOn
        ACCESS  read-write
        STATUS 	mandatory
        DESCRIPTION
        "This value represents the mode in which the traps are sent:
        If the filter is set to ON - only traps that change the severity of the system are sent.
        If the filter is set to OFF - all traps (that are not screened by other filters) are sent."
        ::= { genEquipTrapCfgMgrEntry 8 }


genEquipTrapCfgMgrCLLI OBJECT-TYPE
        SYNTAX DisplayString (SIZE(0..100))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Common Language Location Identifier (maximum length: 100 chars)"
        ::= { genEquipTrapCfgMgrEntry 9 }

genEquipTrapCfgMgrHeartbeatPeriod OBJECT-TYPE
        SYNTAX INTEGER (0..1440)
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This item defines the interval (in minutes) between each heartbeat trap.
        A value of '0' disables this feature."
        DEFVAL { 0 }
        ::= { genEquipTrapCfgMgrEntry 10 }

genEquipTrapCfgMgrIPv6 OBJECT-TYPE
        SYNTAX OCTET STRING (SIZE(0..16))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The IP-v6 address of the trap manage)"
        ::= { genEquipTrapCfgMgrEntry 11 }

genEquipTrapCfgMgrV3User OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The manager trap V3 user name configured"
        ::= { genEquipTrapCfgMgrEntry 12 }


-- Event Log
genEquipEventLog OBJECT IDENTIFIER ::= {genEquipFault 3}

genEquipEventLogTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipEventLogEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table includes the event log."
        ::= { genEquipEventLog 1 }

genEquipEventLogEntry OBJECT-TYPE
        SYNTAX GenEquipEventLogEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Entry containing the info of the event log table."
        INDEX { genEquipEventLogCounter }
        ::= { genEquipEventLogTable 1 }

GenEquipEventLogEntry ::=
		SEQUENCE {
			genEquipEventLogCounter
				INTEGER,
			genEquipEventLogRaisedTimeT
            	INTEGER,
			genEquipEventLogSeverity
				Severity,
			genEquipEventLogModule
				DisplayString,
			genEquipEventLogDesc
				DisplayString,
			genEquipEventLogState
				INTEGER,
			genEquipEventLogProbableCause
				DisplayString,
			genEquipEventLogCorrectiveActions
			    DisplayString,
			genEquipEventLogAdditionalInfo
				DisplayString,
			genEquipEventLogUserText
				DisplayString,
			genEquipEventLogIfIndex
				INTEGER,
			genEquipEventLogId
				INTEGER
}

genEquipEventLogCounter OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The event counter."
        ::= { genEquipEventLogEntry 1 }


genEquipEventLogRaisedTimeT OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The rasied time of this event.
         Time in seconds since January 1, 1970 00:00 UTC. (time_t format)"
        ::= { genEquipEventLogEntry 2 }

genEquipEventLogSeverity OBJECT-TYPE
        SYNTAX  Severity
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        "The event severity, possible values are:
         - indeterminate(0),
		 - critical(1)
		 - major(2)
		 - minor(3)
		 - warning(4)
		 - cleared(5)"
        ::= { genEquipEventLogEntry 3 }

genEquipEventLogModule OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The module of the event. ('IDU' or 'RFU')"
        ::= { genEquipEventLogEntry 4 }

genEquipEventLogDesc OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The event description (same as the description in the sent trap)."
        ::= { genEquipEventLogEntry 5 }

genEquipEventLogState OBJECT-TYPE
		SYNTAX		INTEGER{
					cleared(0),
					raised(1),
					event(2)
					}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The state of the event or alarm.
        In case of alarm, 'cleard' or 'raised'.
        In case of event, 'event'."
        ::= { genEquipEventLogEntry 6 }

genEquipEventLogProbableCause OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The probable cause that may be the reason for the event."
        ::= { genEquipEventLogEntry 7 }

genEquipEventLogCorrectiveActions OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "Some possible suggested corrective actions in order to eliminate the raised alarm."
        ::= { genEquipEventLogEntry 8 }

genEquipEventLogAdditionalInfo OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "Additional information that may further qualify the source of the event."
        ::= { genEquipEventLogEntry 9 }

genEquipEventLogUserText OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "An additional description of the event or fault, or any other information that user wants to attach to it."
        ::= { genEquipEventLogEntry 10 }

genEquipEventLogIfIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "If Index of the originated source."
        ::= { genEquipEventLogEntry 11 }

genEquipEventLogId OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The Id of the event."
        ::= { genEquipEventLogEntry 12 }

-- Clear event log command
genEquipEventLogClear  OBJECT-TYPE
        SYNTAX  OffOn
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
        "The clear event log command. If enabled, will clear the event log."
        ::= { genEquipEventLog 2 }

genEquipEventLogLastChangeCounter OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "This counter is initialized with a random number after reset,
        and incremented on each change in the EventLogTable"
        ::= { genEquipEventLog 3 }


-- Application last error (number and description)
genEquipFaultErrno OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The application last error number. List of Errnos are located in the MIB Ref Guide."
        ::= { genEquipFault 4 }

genEquipFaultErrDescr OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
        "The application last error description. List of Errnos are located in the MIB Ref Guide."
        ::= { genEquipFault 5 }


-- Management
genEquipMng OBJECT IDENTIFIER ::= { genEquip 4}

genEquipMngSw OBJECT IDENTIFIER ::= { genEquipMng 1}

genEquipMngSwServerUrl OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
"Remote SW Update Server URL where SW updates reside"
::= { genEquipMngSw 1 }

genEquipMngSwServerLogin 		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
"Remote server login"
::= { genEquipMngSw 2 }

genEquipMngSwServerPassword 	OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
"Remote server password"
::= { genEquipMngSw 3 }

genEquipMngSwProxyUrl 			OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS 		read-only
		STATUS 		obsolete
		DESCRIPTION
"Proxy Server Url.
 This parameter is obsolete and should not be in used."
::= { genEquipMngSw 4 }

genEquipMngSwProxyLogin 		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
		STATUS		obsolete
		DESCRIPTION
"Proxy server login
This parameter is obsolete and should not be in used."
::= { genEquipMngSw 5 }

genEquipMngSwProxyPassword		OBJECT-TYPE
		SYNTAX		DisplayString
		ACCESS		read-only
		STATUS		obsolete
		DESCRIPTION
"Proxy server password
This parameter is obsolete and should not be in used."
::= { genEquipMngSw 6 }

genEquipMngSwDownloadStatus		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"Download software status.
		Possible values are:
		 - ready(0)
		 - inProgress(1)
		 - success(2)
		 - failure(3)"
::= { genEquipMngSw 7 }

genEquipMngSwInstallStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"Install software status.
		Possible values are:
		 - ready(0)
		 - inProgress(1)
		 - success(2)
		 - failure(3)"
::= { genEquipMngSw 8 }

genEquipMngSwCommand 		OBJECT-TYPE
		SYNTAX 		SwCommandTimer
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
		"The command that is executed to manage SW versions.
		Depending on the value given, a different operation will be carried out involving FTP and/or version installing.
		Notice that software will be installed in all slots in a shelf and reset will be automatically carried out
		at the end of the installation process.
		Possible values are:
		 - noOperation(0)
		 - downloadUpgradeVersion(1) - load a newer version from FTP site
		 - upgrade(2) - upgrade to newer version which has already been loaded
		 - rollback(3) - install a previously working version without downloading
		 - downgrade(4) - downgrade to an older version which has alredy been loaded
		 - downloadDowngradeVersion(5) - load an older version from FTP site
		 - upgradeTimer(6) - Starts the timer defined in genEquipMngSwInstallationTimer
			and upon expiration upgrades to a newer version which has already been loaded.
		 - rollbackTimer(7) - Starts the timer defined in genEquipMngSwInstallationTimer
			and upon expiration install a previously working version without downloading
		 - downgradeTimer(7) - Starts the timer defined in genEquipMngSwInstallationTimer
			and upon expiration downgrades to an older which has already been loaded
		 - abortTimedTnstallation(9) stops a counting timer; the installation process will not take place.
			Notice that it does not stop an installation process once started."

::= { genEquipMngSw 9 }

genEquipMngSwInstalledIduVersion	OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"This value indicates the Package Version of the installed IDU SW"
::= { genEquipMngSw 10 }

genEquipMngSwInstalledRfuVersion	OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
		"This value indicates the Package version of the installed RFU SW."
::= { genEquipMngSw 11 }

-- Versions
genEquipMngSwVersions OBJECT IDENTIFIER ::= { genEquipMngSw 13}

genEquipMngSwIDUVersionsTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwIDUVersionsEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table contains all IDU running versions."
        ::= { genEquipMngSwVersions 1 }

genEquipMngSwIDUVersionsEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwIDUVersionsEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Entry containing the info of the IDU Versions table."
        INDEX { genEquipMngSwIDUVersionsCounter }
        ::= { genEquipMngSwIDUVersionsTable 1 }

GenEquipMngSwIDUVersionsEntry ::=
		SEQUENCE {
			genEquipMngSwIDUVersionsCounter
				INTEGER,
			genEquipMngSwIDUVersionsPackageName
				DisplayString,
			genEquipMngSwIDUVersionsTargetDevice
				DisplayString,
			genEquipMngSwIDUVersionsRunningVersion
				DisplayString,
			genEquipMngSwIDUVersionsInstalledVersion
				DisplayString,
			genEquipMngSwIDUVersionsUpgradePackage
				DisplayString,
			genEquipMngSwIDUVersionsDowngradePackage
				DisplayString,
			genEquipMngSwIDUVersionsResetType
				INTEGER
}

genEquipMngSwIDUVersionsCounter OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "This is the index of this table."
        ::= { genEquipMngSwIDUVersionsEntry 1 }

genEquipMngSwIDUVersionsPackageName OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The package name"
        ::= { genEquipMngSwIDUVersionsEntry 2 }

genEquipMngSwIDUVersionsTargetDevice OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The Target device within the NE (i.e. Modem, FPGA, IDU)"
        ::= { genEquipMngSwIDUVersionsEntry 3 }

genEquipMngSwIDUVersionsRunningVersion OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The running version."
        ::= { genEquipMngSwIDUVersionsEntry 4 }

genEquipMngSwIDUVersionsInstalledVersion OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The installed version is similar to the running version, besides packages that are not currently running.
        Those packages will have in the running version 'N/A' while in the installed version there is a version number.
        For example, in equipment without daughter-board, some of the packages will be installed but not running."
        ::= { genEquipMngSwIDUVersionsEntry 5 }

genEquipMngSwIDUVersionsUpgradePackage OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "In case the user performs an upgrade, this version will be the running/installed version"
        ::= { genEquipMngSwIDUVersionsEntry 6 }

genEquipMngSwIDUVersionsDowngradePackage OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "In case the user performs downgrade, this version will be the running/installed version."
        ::= { genEquipMngSwIDUVersionsEntry 7 }

genEquipMngSwIDUVersionsResetType OBJECT-TYPE
		SYNTAX		INTEGER{
					noReset(0),
					appWarmReset(1),
					tccColdReset(2),
					mainBoardColdReset(3),
					mainBoardWarmReset(4),
					applicationRestart(5),
					cardReset(6),
					notApplicable(7)
					}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Minimum level of reset required to update the software in the relevant module."
        ::= { genEquipMngSwIDUVersionsEntry 8 }

genEquipMngSwTimerTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwTimerEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table contains all IDU running versions."
        ::= { genEquipMngSwVersions 2 }

genEquipMngSwTimerEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwTimerEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Entry containing the info of the IDU Versions table."
        INDEX { genEquipMngSwTimerSlotNumber }
        ::= { genEquipMngSwTimerTable 1 }

GenEquipMngSwTimerEntry ::=
		SEQUENCE {
			genEquipMngSwTimerSlotNumber
				INTEGER,
			genEquipMngSwTimerInstallationTimer
				INTEGER,
			genEquipMngSwTimerTimeToInstall
				INTEGER,
			genEquipMngSwTimerTimerAbort
				INTEGER
}

genEquipMngSwTimerSlotNumber OBJECT-TYPE
        SYNTAX INTEGER (1..6)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The slot ID of the unit for this timer; 0 is for stand-alone units."
        ::= { genEquipMngSwTimerEntry 1 }

genEquipMngSwTimerInstallationTimer OBJECT-TYPE
        SYNTAX INTEGER (1..1440)
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The timer (in minutes) for software installation operations to this unit."
        ::= { genEquipMngSwTimerEntry 2 }

genEquipMngSwTimerTimeToInstall OBJECT-TYPE
        SYNTAX INTEGER (1..86400)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The amount of time (in seconds) from now for the unit to perform software installation.
		Zero denotes that no operation has been requested."
        ::= { genEquipMngSwTimerEntry 3 }

genEquipMngSwTimerTimerAbort OBJECT-TYPE
        SYNTAX INTEGER {
				proceed(0),
				abort (1)
		}
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "This parameter is used to stop the count-down to the software update action."
        ::= { genEquipMngSwTimerEntry 4 }

-- Timer scalars

genEquipMngSwFTPTimer OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Time (in seconds) until file transfer is failed."
        ::= { genEquipMngSwVersions 10 }

--

genEquipMngSwInstallationTimer 	OBJECT-TYPE
        SYNTAX  INTEGER (1..1440)
        ACCESS  read-write
        STATUS mandatory
        DESCRIPTION
		"The timer (in minutes) for timed software installation operations to this unit"
::= { genEquipMngSw 14 }

genEquipMngSwTimeToInstall 	OBJECT-TYPE
        SYNTAX  INTEGER (1..1440)
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
		"The amount of time (in minutes) from now for the unit to perform software installation.
		Zero denotes that no operation has been requested"
::= { genEquipMngSw 15 }

genEquipMngSwUpgradeCommonRfuVersion	OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
		"This value indicates the Package version of the installed RFU SW."
::= { genEquipMngSw 16 }

genEquipMngSwDowngradeCommonRfuVersion	OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS mandatory
        DESCRIPTION
		"This value indicates the Package version of the installed RFU SW."
::= { genEquipMngSw 17 }




genEquipMngSwFileTransferTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwFileTransferEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows configuring the details of the external server where software versions should be located."
        ::= { genEquipMngSw 18 }

genEquipMngSwFileTransferEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwFileTransferEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Entry containing the info of software management file transfer."
        INDEX { genEquipMngSwFileTransferIndex }
        ::= { genEquipMngSwFileTransferTable 1 }

GenEquipMngSwFileTransferEntry ::=
		SEQUENCE {
			genEquipMngSwFileTransferIndex
				INTEGER,
			genEquipMngSwFileTransferProtocol
				FtpProtocolType,
			genEquipMngSwFileTransferUserName
				DisplayString,
			genEquipMngSwFileTransferPassword
				DisplayString,
			genEquipMngSwFileTransferAddress
				IpAddress,
			genEquipMngSwFileTransferPath
				DisplayString,
			genEquipMngSwFileTransferIpv6Address
				OCTET STRING
}

genEquipMngSwFileTransferIndex OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The index of the table."
        ::= { genEquipMngSwFileTransferEntry 1 }

genEquipMngSwFileTransferProtocol OBJECT-TYPE
        SYNTAX FtpProtocolType
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The protocol to use to transfer the files between the system and an external computer."
        ::= { genEquipMngSwFileTransferEntry 2 }

genEquipMngSwFileTransferUserName OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "User name for access to the configuration files location."
        ::= { genEquipMngSwFileTransferEntry 3 }

genEquipMngSwFileTransferPassword OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Password for access to the software version files location."
        ::= { genEquipMngSwFileTransferEntry 4 }

genEquipMngSwFileTransferAddress OBJECT-TYPE
        SYNTAX IpAddress
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "IP address of the computer where software version files are to be taken from."
        ::= { genEquipMngSwFileTransferEntry 5 }

genEquipMngSwFileTransferPath OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Location of the files in the external server."
        ::= { genEquipMngSwFileTransferEntry 6 }

genEquipMngSwFileTransferIpv6Address OBJECT-TYPE
        SYNTAX OCTET STRING  (SIZE (0..16))
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "IPv6 Server Address."
        ::= { genEquipMngSwFileTransferEntry 7 }


---
---
-- NG Sequrity Certificate
genEquipSecurityTrafficCrypto	OBJECT IDENTIFIER ::= {genEquipSecurity 10}

genEquipSecurityFipsAdmin OBJECT-TYPE
        SYNTAX EnableDisable
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "FIPS admin configuration parameter"
        ::= { genEquipSecurityTrafficCrypto 1 }

genEquipSecurityFipsStatus		OBJECT-TYPE
		SYNTAX 		DownUp
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"FIPS operational status."
::= { genEquipSecurityTrafficCrypto 2 }

-- NG Sequrity Traffic Crypto configuration table traffic-crypto-config-table
genEquipTrafficCryptoConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipTrafficCryptoConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Traffic Crypto configuration table."
        ::= { genEquipSecurityTrafficCrypto 10 }

genEquipTrafficCryptoConfigEntry OBJECT-TYPE
        SYNTAX GenEquipTrafficCryptoConfigEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Traffic Crypto configuration table entry"
        INDEX { genEquipTrafficCryptoConfigId }
        ::= { genEquipTrafficCryptoConfigTable 1 }

GenEquipTrafficCryptoConfigEntry ::=
		SEQUENCE {
			genEquipTrafficCryptoConfigId
				INTEGER,
			genEquipTrafficCryptoConfigConfigAdmin
            	INTEGER,
			genEquipTrafficCryptoConfigMkey
            	OCTET STRING,
			genEquipTrafficCryptoConfigBackupMkey
            	OCTET STRING,
			genEquipTrafficCryptoConfigMkeyPeriod
            	INTEGER,
			genEquipTrafficCryptoConfigRandKeyGen
            	INTEGER,
			genEquipTrafficCryptoConfigSkeyPeriod
            	INTEGER
}

genEquipTrafficCryptoConfigId		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Traffic crypto Configuration Intreface ID"
::= { genEquipTrafficCryptoConfigEntry 1 }

genEquipTrafficCryptoConfigConfigAdmin		OBJECT-TYPE
		SYNTAX 		INTEGER {
						disable(0),
						aes-256(1)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Traffic Crypto Admin Mode."
::= { genEquipTrafficCryptoConfigEntry 2 }

genEquipTrafficCryptoConfigMkey		OBJECT-TYPE
		SYNTAX 		OCTET STRING
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Traffic Crypto Master Key."
::= { genEquipTrafficCryptoConfigEntry 3 }

genEquipTrafficCryptoConfigBackupMkey		OBJECT-TYPE
		SYNTAX 		OCTET STRING
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Traffic Crypto Backup Master Key"
::= { genEquipTrafficCryptoConfigEntry 4 }

genEquipTrafficCryptoConfigMkeyPeriod		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Master Key Expire Configuration."
::= { genEquipTrafficCryptoConfigEntry 5 }

genEquipTrafficCryptoConfigRandKeyGen		OBJECT-TYPE
		SYNTAX 		INTEGER {
						random-key-generate(0),
						random-key-clear(1)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Random Key generation/clear indication."
::= { genEquipTrafficCryptoConfigEntry 6 }

genEquipTrafficCryptoConfigSkeyPeriod  OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Session Key Update Time Configuration."
::= { genEquipTrafficCryptoConfigEntry 7 }


-- NG Sequrity Traffic Crypto status table traffic-crypto-status-table
genEquipTrafficCryptoStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipTrafficCryptoStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Traffic Crypto status table."
        ::= { genEquipSecurityTrafficCrypto 11 }

genEquipTrafficCryptoStatusEntry OBJECT-TYPE
        SYNTAX GenEquipTrafficCryptoStatusEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Traffic Crypto status table Entry"
        INDEX { genEquipTrafficCryptoStatusId }
        ::= { genEquipTrafficCryptoStatusTable 1 }

GenEquipTrafficCryptoStatusEntry ::=
		SEQUENCE {
			genEquipTrafficCryptoStatusId
				INTEGER,
			genEquipTrafficCryptoStatusValid
            	INTEGER,
			genEquipTrafficCryptoStatusMkeyTimeExpire
            	INTEGER
}


genEquipTrafficCryptoStatusId		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Traffic Crypto Status Intreface ID"
::= { genEquipTrafficCryptoStatusEntry 1 }

genEquipTrafficCryptoStatusValid		OBJECT-TYPE
		SYNTAX 		INTEGER {
						not-valid(0),
						valid(1)
		}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Crypto Interface Validation State."
::= { genEquipTrafficCryptoStatusEntry 2 }

genEquipTrafficCryptoStatusMkeyTimeExpire		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Number of days Mkey to expire."
::= { genEquipTrafficCryptoStatusEntry 3 }

--
--

genEquipMngSwFileTransferStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwFileTransferStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the status of the current software version file transfer operation."
        ::= { genEquipMngSw 19 }

genEquipMngSwFileTransferStatusEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwFileTransferStatusEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Entry containing the info of software management file transfer status."
        INDEX { genEquipMngSwFileTransferStatusIndex }
        ::= { genEquipMngSwFileTransferStatusTable 1 }

GenEquipMngSwFileTransferStatusEntry ::=
		SEQUENCE {
			genEquipMngSwFileTransferStatusIndex
				INTEGER,
			genEquipMngSwFileTransferStatusResult
				INTEGER,
			genEquipMngSwFileTransferPercentageDone
				INTEGER,
			genEquipMngSwFileTransferPercentageDoneStandBy
				INTEGER,
			genEquipMngSwFileTransferStatusResultStandBy
				INTEGER
}

genEquipMngSwFileTransferStatusIndex OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The index of the table.."
        ::= { genEquipMngSwFileTransferStatusEntry 1 }


genEquipMngSwFileTransferStatusResult OBJECT-TYPE
        SYNTAX INTEGER {
				ready(0),
				downloadStarted(1),
				verifyingDownloadFiles(2),
				downloadInProgress(3),
				downloadSuccess(4),
				downloadFailure(5),
				allComponentsExist(6),
				versionIncompatibleWithSystem(7),
				incompleteFileSet(8),
				componentUnsupportedByHw(9),
				corruptSwFiles(10),
				missingDependencies(11),
				downloadCancelled(12)
		}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Status of the current file transfer operation."
        ::= { genEquipMngSwFileTransferStatusEntry 2 }

genEquipMngSwFileTransferPercentageDone OBJECT-TYPE
        SYNTAX INTEGER (0..100)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "An indication of the progress of the process of the current file transfer operation."
        ::= { genEquipMngSwFileTransferStatusEntry 3 }

genEquipMngSwFileTransferPercentageDoneStandBy OBJECT-TYPE
        SYNTAX INTEGER (0..100)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "An indication of the progress of the process of the current internal file transfer operation."
        ::= { genEquipMngSwFileTransferStatusEntry 4 }

genEquipMngSwFileTransferStatusResultStandBy OBJECT-TYPE
        SYNTAX INTEGER {
			ready(0),
			download-started(1),
			verifying-download-files(2)
		}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Status of the current internal file transfer operation."
        ::= { genEquipMngSwFileTransferStatusEntry 5 }



genEquipMngSwInstallStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwInstallStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the status of the current software version installation operation."
        ::= { genEquipMngSw 20 }

genEquipMngSwInstallStatusEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwInstallStatusEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
          "Entry containing the info of software installation status."
        INDEX { genEquipMngSwInstallStatusIndex }
        ::= { genEquipMngSwInstallStatusTable 1 }

GenEquipMngSwInstallStatusEntry ::=
		SEQUENCE {
			genEquipMngSwInstallStatusIndex
				INTEGER,
			genEquipMngSwInstallStatusResult
				INTEGER,
			genEquipMngSwInstallPercentageDone
				INTEGER,
			genEquipMngSwInstallStatusResultStandBy
				INTEGER,
			genEquipMngSwInstallPercentageDoneStandBy
				INTEGER
}

genEquipMngSwInstallStatusIndex OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The index of the table.."
        ::= { genEquipMngSwInstallStatusEntry 1 }


genEquipMngSwInstallStatusResult OBJECT-TYPE
        SYNTAX INTEGER {
				ready(0),
				installationStarted(1),
				verifyingInstallationFiles(2),
				installationInProgress(3),
				installationSuccess(4),
				installationPartialSuccess(5),
				installationFailure(6),
				incompleteSwVersion(7),
				installationCancelled(8)
		}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Status of the current installation operation."
        ::= { genEquipMngSwInstallStatusEntry 2 }

genEquipMngSwInstallPercentageDone OBJECT-TYPE
        SYNTAX INTEGER (0..100)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "An indication of the progress of the process of the current installation operation."
        ::= { genEquipMngSwInstallStatusEntry 3 }

genEquipMngSwInstallStatusResultStandBy OBJECT-TYPE
        SYNTAX INTEGER {
				ready(0),
				installationStarted(1),
				verifyingInstallationFiles(2),
				installationInProgress(3),
				installationSuccess(4),
				installationPartialSuccess(5),
				installationFailure(6),
				incompleteSwVersion(7),
				installationCancelled(8)
		}
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Status of the current file transfer operation."
        ::= { genEquipMngSwInstallStatusEntry 4 }

genEquipMngSwInstallPercentageDoneStandBy OBJECT-TYPE
        SYNTAX INTEGER (0..100)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "An indication of the progress of the process of the current installation operation."
        ::= { genEquipMngSwInstallStatusEntry 5 }


--
--
genEquipMngSwOperationTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwOperationEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows downloading and installing SW versions."
        ::= { genEquipMngSw 21 }

genEquipMngSwOperationEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwOperationEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
          "Entry containing the info of software management operation table."
        INDEX { genEquipMngSwOperationIndex }
        ::= { genEquipMngSwOperationTable 1 }

GenEquipMngSwOperationEntry ::=
		SEQUENCE {
			genEquipMngSwOperationIndex
				INTEGER,
			genEquipMngSwOperationOperation
				INTEGER,
			genEquipMngSwOperationTimedInstallation
				NoYes
}

genEquipMngSwOperationIndex OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The index of the table.."
        ::= { genEquipMngSwOperationEntry 1 }


genEquipMngSwOperationOperation OBJECT-TYPE
        SYNTAX INTEGER {
				noAction(0),
				download(1),
				install(2),
				updateBackup(3),
				swapBootSection(4),
				abortTimer(5)
		}
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "The operation to be performed: download, install, update backup, swap boot section, abort installation timer."
        ::= { genEquipMngSwOperationEntry 2 }

genEquipMngSwOperationTimedInstallation OBJECT-TYPE
        SYNTAX NoYes
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Whether to perform the installation after a configurable timer expires."
        ::= { genEquipMngSwOperationEntry 3 }

--
-- Slots running versions status table - software-mgt-slot-running-versions-status
--
genEquipMngSwSlotRunningVersionTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwSlotRunningVersionEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the versions currently running in cards installed in the system."
        ::= { genEquipMngSw 22 }

genEquipMngSwSlotRunningVersionEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwSlotRunningVersionEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
          "This table entry shows the versions currently running in cards installed in the system."
        INDEX { genEquipMngSwSlotRunningVersionSlotId }
        ::= { genEquipMngSwSlotRunningVersionTable 1 }

GenEquipMngSwSlotRunningVersionEntry ::=
		SEQUENCE {
			genEquipMngSwSlotRunningVersionSlotId
				INTEGER,
			genEquipMngSwSlotRunningVersionCardType
				InventoryCardType,
			genEquipMngSwSlotRunningVersionComponent
				DisplayString,
			genEquipMngSwSlotRunningVersionActualVersion
				DisplayString
}

genEquipMngSwSlotRunningVersionSlotId OBJECT-TYPE
        SYNTAX INTEGER (1..12)
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The index of the table.."
        ::= { genEquipMngSwSlotRunningVersionEntry 1 }


genEquipMngSwSlotRunningVersionCardType OBJECT-TYPE
        SYNTAX InventoryCardType
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Type of card inserted in slot."
        ::= { genEquipMngSwSlotRunningVersionEntry 2 }

genEquipMngSwSlotRunningVersionComponent OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Name of SW functional component."
        ::= { genEquipMngSwSlotRunningVersionEntry 3 }

genEquipMngSwSlotRunningVersionActualVersion OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Version of running SW."
        ::= { genEquipMngSwSlotRunningVersionEntry 4 }


--
-- Standby Unit software versions table - software-mgt-table-current-versions-status-standby
--
genEquipMngSwIDUVersionsStandByTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngSwIDUVersionsStandByEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the software versions residing in the standby unit."
        ::= { genEquipMngSw 23 }

genEquipMngSwIDUVersionsStandByEntry OBJECT-TYPE
        SYNTAX GenEquipMngSwIDUVersionsStandByEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION
          "This table entry shows the software versions residing in the standby unit."
        INDEX { genEquipMngSwIDUVersionsStandByIndex }
        ::= { genEquipMngSwIDUVersionsStandByTable 1 }

GenEquipMngSwIDUVersionsStandByEntry ::=
		SEQUENCE {
			genEquipMngSwIDUVersionsStandByIndex
				INTEGER,
			genEquipMngSwIDUVersionsStandByPackageName
				DisplayString,
			genEquipMngSwIDUVersionsStandByRunningVersion
				DisplayString,
			genEquipMngSwIDUVersionsStandByInstalledVersion
				DisplayString,
			genEquipMngSwIDUVersionsStandByTargetDevice
				InventoryCardType,
			genEquipMngSwIDUVersionsStandByDownloadedPackage
				DisplayString,
			genEquipMngSwIDUVersionsStandByResetType
				VmResetType
}

genEquipMngSwIDUVersionsStandByIndex OBJECT-TYPE
        SYNTAX INTEGER
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "mate rpm index."
        ::= { genEquipMngSwIDUVersionsStandByEntry 1 }


genEquipMngSwIDUVersionsStandByPackageName OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Name of the module which the software package belongs to."
        ::= { genEquipMngSwIDUVersionsStandByEntry 2 }

genEquipMngSwIDUVersionsStandByRunningVersion OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Version currently running."
        ::= { genEquipMngSwIDUVersionsStandByEntry 3 }

genEquipMngSwIDUVersionsStandByInstalledVersion OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Currently installed version; upon the appropriate reset this will become the running version."
        ::= { genEquipMngSwIDUVersionsStandByEntry 4 }

genEquipMngSwIDUVersionsStandByTargetDevice OBJECT-TYPE
        SYNTAX InventoryCardType
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "The HW board in which the SW module from the mate is running."
        ::= { genEquipMngSwIDUVersionsStandByEntry 5 }

genEquipMngSwIDUVersionsStandByDownloadedPackage OBJECT-TYPE
        SYNTAX DisplayString
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Version that was downloaded from server upon installation, it will become the installed version."
        ::= { genEquipMngSwIDUVersionsStandByEntry 6 }

genEquipMngSwIDUVersionsStandByResetType OBJECT-TYPE
        SYNTAX VmResetType
        ACCESS read-only
        STATUS mandatory
        DESCRIPTION
        "Minimum level of reset required to update the software in the relevant module."
        ::= { genEquipMngSwIDUVersionsStandByEntry 7 }

--
-- WatchDog Scalar
genEquipMngSwWatchdogAdmin OBJECT-TYPE
        SYNTAX EnableDisable
        ACCESS read-write
        STATUS mandatory
        DESCRIPTION
        "Enables/Disables the watchdog timer for the application."
        ::= { genEquipMngSw 35 }


-- Management configuration
genEquipMngCfg OBJECT IDENTIFIER ::= { genEquipMng 2}

genEquipMngCfgBackupStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"This parameter shows the current state of the configuration backup files creation in the IDU.
		Possible values are:
		 - ready(0)
		 - inProgress(1)
		 - success(2)
		 - failure(3)"
::= { genEquipMngCfg 1 }

genEquipMngCfgRestoreStatus 	OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"This parameter shows the current state of configuration restoring from downloaded backup files.
		Possible values are:
		 - ready(0)
		 - inProgress(1)
		 - success(2)
		 - failure(3)"
::= { genEquipMngCfg 2 }

genEquipMngCfgUploadStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"This parameter shows the current state of configuration backup files transfer from IDU to external FTP site.
		Possible values are:
		 - ready(0)
		 - inProgress(1)
		 - success(2)
		 - failure(3)"
::= { genEquipMngCfg 3 }


genEquipMngCfgDownloadStatus		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"This parameter shows the current state of configuration backup files transfer from external FTP site to IDU.
		Possible values are:
		 - ready(0)
		 - inProgress(1)
		 - success(2)
		 - failure(3)"
::= { genEquipMngCfg 4 }

genEquipMngCfgCommand 		OBJECT-TYPE
		SYNTAX 		INTEGER{
						no-operation(0),
						backup(1),
						restore(2),
						upload(3),
						download(4)
		}
		ACCESS 		read-write
		STATUS 		mandatory
		DESCRIPTION
	"The command to be executed to backup/restore or upload/download configuration.
	If there is more then 1 slot in the NE, it is done for all the shelf."
::= { genEquipMngCfg 5 }

genEquipMngCfgEthernetSwitchStoreConfiguration 		OBJECT-TYPE
		SYNTAX		OffOn
		ACCESS		read-write
		STATUS		mandatory
		DESCRIPTION
		"This parameter writes the current configuration in the database.
		This command should be called after any change to the following sections:
		-	Bridge configuration
		-	WaySide configuration
		-	Protection configuration."
::= {  genEquipMngCfg 6 }

genEquipMngCfgSetToDefaultKeepIp 		OBJECT-TYPE
		SYNTAX		OffOn
		ACCESS		read-write
		STATUS		mandatory
		DESCRIPTION
		"Set to Default configuration without changing the network parameters such as IP address, and subnet mask."
::= {  genEquipMngCfg 7 }

genEquipMngCfgCliScriptFileName 		OBJECT-TYPE
		SYNTAX		DisplayString
		ACCESS		read-write
		STATUS		mandatory
		DESCRIPTION
		"The CLI script file name to be downloaded to the NE."
::= {  genEquipMngCfg 8 }

--
-- Managment Configuration
--
genEquipMngCfgGeneric OBJECT IDENTIFIER ::= { genEquipMngCfg 10}

genEquipMngCfgBackupProgress 		OBJECT-TYPE
		SYNTAX		INTEGER
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"An indication of the progress of the backup operation process."
::= {  genEquipMngCfgGeneric 1 }

genEquipMngCfgTimeToInstallation 		OBJECT-TYPE
		SYNTAX		INTEGER
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"A counter indicating the time left until installation begins."
::= {  genEquipMngCfgGeneric 2 }


--
-- Configuration management file transfer - configuration-table-file-transfer-config
--
genEquipMngCfgFileTransferTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngCfgFileTransferEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows configuring the details of the external server where configuration should be located."
        ::= { genEquipMngCfg 11 }

genEquipMngCfgFileTransferEntry OBJECT-TYPE
        SYNTAX GenEquipMngCfgFileTransferEntry
        ACCESS not-accessible
        STATUS mandatory
		DESCRIPTION
        "This table entry allows configuring the details of the external server where configuration should be located."
        INDEX { genEquipMngCfgFileTransferIndex }
        ::= { genEquipMngCfgFileTransferTable 1 }

GenEquipMngCfgFileTransferEntry ::=
		SEQUENCE {
			genEquipMngCfgFileTransferIndex
				INTEGER,
			genEquipMngCfgFileTransferProtocol
            	FtpProtocolType,
			genEquipMngCfgFileTransferUserName
				DisplayString,
			genEquipMngCfgFileTransferPassword
				DisplayString,
			genEquipMngCfgFileTransferAddress
				IpAddress,
			genEquipMngCfgFileTransferPath
				DisplayString,
			genEquipMngCfgFileTransferFileName
				DisplayString,
			genEquipMngCfgFileTransferIpv6Address
				OCTET STRING
}

genEquipMngCfgFileTransferIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Index of the table."
::= { genEquipMngCfgFileTransferEntry 1 }

genEquipMngCfgFileTransferProtocol		OBJECT-TYPE
		SYNTAX 		FtpProtocolType
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The protocol to use to transfer the files between the system and an external computer."
::= { genEquipMngCfgFileTransferEntry 2 }

genEquipMngCfgFileTransferUserName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User name for access to the configuration files location."
::= { genEquipMngCfgFileTransferEntry 3 }

genEquipMngCfgFileTransferPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Password for access to the configuration files location."
::= { genEquipMngCfgFileTransferEntry 4 }

genEquipMngCfgFileTransferAddress		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IP address of the computer where configuration files are to be transferred to/from."
::= { genEquipMngCfgFileTransferEntry 5 }

genEquipMngCfgFileTransferPath		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Location of the files in the external server."
::= { genEquipMngCfgFileTransferEntry 6 }

genEquipMngCfgFileTransferFileName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Name of the file in server."
::= { genEquipMngCfgFileTransferEntry 7 }

genEquipMngCfgFileTransferIpv6Address		OBJECT-TYPE
		SYNTAX 		OCTET STRING (SIZE(0..16))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IPv6 Server's address where files will be transferred to/from."
::= { genEquipMngCfgFileTransferEntry 8 }


--
-- Configuration management file transfer status - configuration-table-file-transfer-status
--
genEquipMngCfgFileTransferStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngCfgFileTransferStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the status of the current configuration file transfer operation."
        ::= { genEquipMngCfg 12 }

genEquipMngCfgFileTransferStatusEntry OBJECT-TYPE
        SYNTAX GenEquipMngCfgFileTransferStatusEntry
        ACCESS not-accessible
        STATUS mandatory
		DESCRIPTION
        "This table entry shows the status of the current configuration file transfer operation."
        INDEX { genEquipMngCfgFileTransferStatusIndex }
        ::= { genEquipMngCfgFileTransferStatusTable 1 }

GenEquipMngCfgFileTransferStatusEntry ::=
		SEQUENCE {
			genEquipMngCfgFileTransferStatusIndex
				INTEGER,
			genEquipMngCfgFileTransferStatusPercentageDone
            	INTEGER,
			genEquipMngCfgFileTransferStatusResult
				FileTransferStatus
}

genEquipMngCfgFileTransferStatusIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Index of the table."
::= { genEquipMngCfgFileTransferStatusEntry 1 }

genEquipMngCfgFileTransferStatusPercentageDone		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"An indication of the progress of the process of the current file transfer operation."
::= { genEquipMngCfgFileTransferStatusEntry 2 }

genEquipMngCfgFileTransferStatusResult		OBJECT-TYPE
		SYNTAX 		FileTransferStatus
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Status of the current file transfer operation."
::= { genEquipMngCfgFileTransferStatusEntry 3 }

--
-- Configuration management operation table - configuration-table-operation-config
--
genEquipMngCfgOperationTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngCfgOperationEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows creating, restoring, transferring and deleting configuration files."
        ::= { genEquipMngCfg 13 }

genEquipMngCfgOperationEntry OBJECT-TYPE
        SYNTAX GenEquipMngCfgOperationEntry
        ACCESS not-accessible
        STATUS mandatory
		DESCRIPTION
        "This table entry allows creating, restoring, transferring and deleting configuration files."
        INDEX { genEquipMngCfgOperationIndex }
        ::= { genEquipMngCfgOperationTable 1 }

GenEquipMngCfgOperationEntry ::=
		SEQUENCE {
			genEquipMngCfgOperationIndex
				INTEGER,
			genEquipMngCfgOperationOperation
            	CfgOper,
			genEquipMngCfgOperationFileNumber
				INTEGER,
			genEquipMngCfgOperationTimedInstallation
				NoYes,
			genEquipMngCfgOperationTimer
				DisplayString,
			genEquipMngCfgOperationSlotNumber
				INTEGER
}

genEquipMngCfgOperationIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Index of the table."
::= { genEquipMngCfgOperationEntry 1 }

genEquipMngCfgOperationOperation		OBJECT-TYPE
		SYNTAX 		CfgOper
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The operation to be performed: backup, restore, delete, import, export."
::= { genEquipMngCfgOperationEntry 2 }

genEquipMngCfgOperationFileNumber		OBJECT-TYPE
		SYNTAX 		INTEGER (1..3)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"A number from 1 to 3 to identify different restoration files."
::= { genEquipMngCfgOperationEntry 3 }

genEquipMngCfgOperationTimedInstallation		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Whether to install immediately or following a countdown from a defined counter."
::= { genEquipMngCfgOperationEntry 4 }

genEquipMngCfgOperationTimer		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"A timer which determines the time to installation."
::= { genEquipMngCfgOperationEntry 5 }

genEquipMngCfgOperationSlotNumber		OBJECT-TYPE
		SYNTAX 		INTEGER (0..12)
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Slot number."
::= { genEquipMngCfgOperationEntry 6 }


--
-- Configuration management status table - configuration-table-backup-files-status
--
genEquipMngCfgConfigurationFilesTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngCfgConfigurationFilesEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the status of the current configuration management operation."
        ::= { genEquipMngCfg 14 }

genEquipMngCfgConfigurationFilesEntry OBJECT-TYPE
        SYNTAX GenEquipMngCfgConfigurationFilesEntry
        ACCESS not-accessible
        STATUS mandatory
		DESCRIPTION
        "This table entry shows the status of the current configuration file transfer operation."
        INDEX { genEquipMngCfgConfigurationFilesIndex }
        ::= { genEquipMngCfgConfigurationFilesTable 1 }

GenEquipMngCfgConfigurationFilesEntry ::=
		SEQUENCE {
			genEquipMngCfgConfigurationFilesIndex
				INTEGER,
			genEquipMngCfgConfigurationFilesFileNumber
            	INTEGER,
			genEquipMngCfgConfigurationFilesSystemType
				DisplayString,
			genEquipMngCfgConfigurationFilesSWVersion
				DisplayString,
			genEquipMngCfgConfigurationFilesTimeDate
				INTEGER,
			genEquipMngCfgConfigurationFilesSystemIP
				IpAddress,
			genEquipMngCfgConfigurationFilesCardsConfigured
				DisplayString,
			genEquipMngCfgConfigurationFilesSystemID
				DisplayString
}

genEquipMngCfgConfigurationFilesIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Index of the table."
::= { genEquipMngCfgConfigurationFilesEntry 1 }

genEquipMngCfgConfigurationFilesFileNumber		OBJECT-TYPE
		SYNTAX 		INTEGER (1..3)
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"A number from 1 to 3 to identify different restoration files."
::= { genEquipMngCfgConfigurationFilesEntry 2 }

genEquipMngCfgConfigurationFilesSystemType		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Type of product from which the backup file was created."
::= { genEquipMngCfgConfigurationFilesEntry 3 }

genEquipMngCfgConfigurationFilesSWVersion		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Software version of the system from which the backup file was created."
::= { genEquipMngCfgConfigurationFilesEntry 4 }

genEquipMngCfgConfigurationFilesTimeDate		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Time and date of creation of the file."
::= { genEquipMngCfgConfigurationFilesEntry 5 }

genEquipMngCfgConfigurationFilesSystemIP		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"IP address of the unit from which the backup file was created."
::= { genEquipMngCfgConfigurationFilesEntry 6 }

genEquipMngCfgConfigurationFilesCardsConfigured		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Types of cards for which configuration values exist in the file; when restoring other card types will get default values."
::= { genEquipMngCfgConfigurationFilesEntry 7 }

genEquipMngCfgConfigurationFilesSystemID		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"System ID of the unit from which the backup file was created."
::= { genEquipMngCfgConfigurationFilesEntry 8 }

--
--
genEquipMngCfgFileRestoreStatus		OBJECT-TYPE
		SYNTAX 		FileRestoreStatus
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Status of the operation of configuration restore from restore point."
::= { genEquipMngCfg 20 }

genEquipMngCfgFileTransferStatus		OBJECT-TYPE
		SYNTAX 		INTEGER {
						ready(0),
						download-started(1),
						verifying-download-files(2),
						download-in-progress(3),
						download-success(4),
						download-failure(5),
						all-components-exist(6),
						version-incompatible-with-system(7),
						incomplete-file-set(8),
						component-unsupported-by-hw(9),
						corrupt-sw-files(10),
						missing-dependencies(11),
						download-cancelled(12)
					}
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Status of the operation of configuration restore from restore point."
::= { genEquipMngCfg 21 }




-- Management File Transfer
genEquipMngFileTransfer  OBJECT IDENTIFIER ::= { genEquipMng 3}

genEquipMngFileTransferFileTypeOper 		OBJECT-TYPE
		SYNTAX		INTEGER {
						no-operation(0),
						download-configuration(1),
						download-certificate(2),
						download-warning-banner(3),
						download-cli-script(4),
						upload-configuration(5),
						upload-csr-file(6),
						upload-unit-info(7)
		}
		ACCESS		read-write
		STATUS		mandatory
		DESCRIPTION
		"Operation for the file type to be transferred between the IDU and the external server.
		The options with prefix 'download' are files to be transferred from the server to the IDU.
		The options with prefix 'upload' are files to be transferred from the IDU to the server.
		To download IDU certificate, the OIDs genEquipSecurityCfgSecurityFileName, genEquipSecurityCfgSecurityFileFormat
		and genEquipSecurityCfgSecurityFileType should be set prior the operation of download security file.
		To upload CSR file, the OID genEquipSecurityCfgSecurityFileName should be set prior the operation of upload CSR file.
		To download CLI string, the OID genEquipMngCfgCliScriptFileName should be set prior the operation of download cli-script.
		To download warning banner file, the OID genEquipSecurityCfgWarningBannerFName should be set prior the operation of download warning-banner."
::= {  genEquipMngFileTransfer 1 }

genEquipMngFileTransferDownloadConfigStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"The operation status of download configuration, which is performed using genEquipMngFileTransferFileTypeOper"
::= {  genEquipMngFileTransfer 2 }

genEquipMngFileTransferDownloadCertificateStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"The operation status of download configuration, which is performed using genEquipMngFileTransferFileTypeOper"
::= {  genEquipMngFileTransfer 3 }

genEquipMngFileTransferDownloadWarningBannerStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"The operation status of download warning banner, which is performed using genEquipMngFileTransferFileTypeOper"
::= {  genEquipMngFileTransfer 4 }

genEquipMngFileTransferDownloadCliScriptStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"TThe operation status of download cli script, which is performed using genEquipMngFileTransferFileTypeOper"
::= {  genEquipMngFileTransfer 5 }

genEquipMngFileTransferUploadConfigStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"The operation status of upload configuration, which is performed using genEquipMngFileTransferFileTypeOper"
::= {  genEquipMngFileTransfer 6 }

genEquipMngFileTransferUploadCSRStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"The operation status of upload CSR, which is performed using genEquipMngFileTransferFileTypeOper"
::= {  genEquipMngFileTransfer 7 }

genEquipMngFileTransferUploadUnitInfoStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"The operation status of upload unit info, which is performed using genEquipMngFileTransferFileTypeOper"
::= {  genEquipMngFileTransfer 8 }


-- Management Unit Information Group
genEquipMngUnitInfo  OBJECT IDENTIFIER ::= { genEquipMng 4}
genEquipMngUnitInfoGeneric  OBJECT IDENTIFIER ::= { genEquipMngUnitInfo 1}

genEquipMngUnitInfoOperation 		OBJECT-TYPE
		SYNTAX		CfgUnitInfoOper
		ACCESS		read-write
		STATUS		mandatory
		DESCRIPTION
		"Allows creating and exporting the unit information file."
::= {  genEquipMngUnitInfoGeneric 1 }

genEquipMngUnitInfoProgress 		OBJECT-TYPE
		SYNTAX		INTEGER
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"Progress in percentage of the operation of the unit info file creation."
::= {  genEquipMngUnitInfoGeneric 2 }

genEquipMngUnitInfoStatus 		OBJECT-TYPE
		SYNTAX		ProgressStatus
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"Status of the operation of the unit info file creation."
::= {  genEquipMngUnitInfoGeneric 3 }


-- CLI Group
genEquipMngCli  OBJECT IDENTIFIER ::= { genEquipMng 5}

genEquipMngCliScriptOperation 		OBJECT-TYPE
		SYNTAX		INTEGER {
						no-operation(0),
						import(1),
						delete(2),
						show(3),
						execute(4)
					}
		ACCESS		read-write
		STATUS		mandatory
		DESCRIPTION
		"Allows importing, showing, deleting and executing CLI scripts."
::= {  genEquipMngCli 1 }

genEquipMngCliScriptOperationStatus 		OBJECT-TYPE
		SYNTAX		INTEGER {
						ready(0),
						executing(1),
						failed(2),
						success(3)
					}
		ACCESS		read-only
		STATUS		mandatory
		DESCRIPTION
		"Allows importing, showing, deleting and executing CLI scripts."
::= {  genEquipMngCli 2 }


--
-- unit-info file transfer configuration table - unit-info-files-transfer-table-config
--
genEquipMngUnitInfoFileTransferTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngUnitInfoFileTransferEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table allows configuring the details of the external server which the unit info file will be expoted to."
        ::= { genEquipMngUnitInfo 2 }

genEquipMngUnitInfoFileTransferEntry OBJECT-TYPE
        SYNTAX GenEquipMngUnitInfoFileTransferEntry
        ACCESS not-accessible
        STATUS mandatory
		DESCRIPTION
        "This table entry allows configuring the details of the external server which the unit info file will be expoted to."
        INDEX { genEquipMngUnitInfoFileTransferIndex }
        ::= { genEquipMngUnitInfoFileTransferTable 1 }

GenEquipMngUnitInfoFileTransferEntry ::=
		SEQUENCE {
			genEquipMngUnitInfoFileTransferIndex
				INTEGER,
			genEquipMngUnitInfoFileTransferProtocol
            	FtpProtocolType,
			genEquipMngUnitInfoFileTransferUserName
				DisplayString,
			genEquipMngUnitInfoFileTransferPassword
				DisplayString,
			genEquipMngUnitInfoFileTransferAddress
				IpAddress,
			genEquipMngUnitInfoFileTransferPath
				DisplayString,
			genEquipMngUnitInfoFileTransferFileName
				DisplayString,
			genEquipMngUnitInfoFileTransferIpv6Address
				OCTET STRING
}

genEquipMngUnitInfoFileTransferIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Index of the table."
::= { genEquipMngUnitInfoFileTransferEntry 1 }

genEquipMngUnitInfoFileTransferProtocol		OBJECT-TYPE
		SYNTAX 		FtpProtocolType
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The protocol to use to transfer the files between the system and an external computer."
::= { genEquipMngUnitInfoFileTransferEntry 2 }

genEquipMngUnitInfoFileTransferUserName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User name for access to the  files location."
::= { genEquipMngUnitInfoFileTransferEntry 3 }

genEquipMngUnitInfoFileTransferPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Password for access to the  files location."
::= { genEquipMngUnitInfoFileTransferEntry 4 }

genEquipMngUnitInfoFileTransferAddress		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IP address of the computer where the files are to be transferred to/from."
::= { genEquipMngUnitInfoFileTransferEntry 5 }

genEquipMngUnitInfoFileTransferPath		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Desired location of the unit info file in external server."
::= { genEquipMngUnitInfoFileTransferEntry 6 }

genEquipMngUnitInfoFileTransferFileName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Name of the file in server."
::= { genEquipMngUnitInfoFileTransferEntry 7 }

genEquipMngUnitInfoFileTransferIpv6Address		OBJECT-TYPE
		SYNTAX 		OCTET STRING (SIZE(0..16))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IPv6 Server's Address to which he file will be transferred."
::= { genEquipMngUnitInfoFileTransferEntry 8 }

--
-- unit-info file transfer status table - unit-info-file-transfer-table-status
--
genEquipMngUnitInfoFileTransferStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipMngUnitInfoFileTransferStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "This table shows the status of the current configuration file transfer operation."
        ::= { genEquipMngUnitInfo 3 }

genEquipMngUnitInfoFileTransferStatusEntry OBJECT-TYPE
        SYNTAX GenEquipMngUnitInfoFileTransferStatusEntry
        ACCESS not-accessible
        STATUS mandatory
		DESCRIPTION
        "This table entry shows the status of the current configuration file transfer operation."
        INDEX { genEquipMngUnitInfoFileTransferStatusIndex }
        ::= { genEquipMngUnitInfoFileTransferStatusTable 1 }

GenEquipMngUnitInfoFileTransferStatusEntry ::=
		SEQUENCE {
			genEquipMngUnitInfoFileTransferStatusIndex
				INTEGER,
			genEquipMngUnitInfoFileTransferStatusPercentageDone
            	INTEGER,
			genEquipMngUnitInfoFileTransferStatusResult
				ProgressStatus
}

genEquipMngUnitInfoFileTransferStatusIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Index of the table."
::= { genEquipMngUnitInfoFileTransferStatusEntry 1 }

genEquipMngUnitInfoFileTransferStatusPercentageDone		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"An indication of the progress of the process of the current file transfer operation."
::= { genEquipMngUnitInfoFileTransferStatusEntry 2 }

genEquipMngUnitInfoFileTransferStatusResult		OBJECT-TYPE
		SYNTAX 		ProgressStatus
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Status of the current file transfer operation."
::= { genEquipMngUnitInfoFileTransferStatusEntry 3 }



-- Diagnostics and Maintenance
genEquipDiagAndMaintenance	OBJECT IDENTIFIER ::= {genEquip 10}

-- Radio Loopback

genEquipDiagAndMaintenanceRadioLoopbackTimeout		OBJECT-TYPE
		SYNTAX 		INTEGER (0..1440)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Radio loopback timeout, in minutes. 0 means no timer."
::= { genEquipDiagAndMaintenance 1 }


-- Line Loopback

genEquipDiagAndMaintenanceLineLoopbackTimeout		OBJECT-TYPE
		SYNTAX 		INTEGER (0..1440)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Line loopback timeout, in minutes. 0 means no timer."
::= { genEquipDiagAndMaintenance 2 }

-- STM-1/OC-3

genEquipDiagAndMaintenanceSDHLoopbackTimeout		OBJECT-TYPE
		SYNTAX 		INTEGER (0..1440)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"STM-1/OC-3 line loopback timeout, in minutes. 0 means no timer."
::= { genEquipDiagAndMaintenance 3 }




-- Security Configuration
genEquipSecurity	OBJECT IDENTIFIER ::= {genEquip 11}

-- Security Configuration
genEquipSecurityConfiguration	OBJECT IDENTIFIER ::= {genEquipSecurity 1}

genEquipSecurityCfgUploadPublicKeyStatus		OBJECT-TYPE
		SYNTAX 		ProgressStatus
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The upload status of the Public key."
::= { genEquipSecurityConfiguration 1 }

genEquipSecurityCfgDownloadSecurityStatus		OBJECT-TYPE
		SYNTAX 		ProgressStatus
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The download status of the digital certificate."
::= { genEquipSecurityConfiguration 2 }

genEquipSecurityCfgSecurityFileName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The certificate file name to be downloaded."
::= { genEquipSecurityConfiguration 3 }

genEquipSecurityCfgSecurityFileType		OBJECT-TYPE
		SYNTAX 		INTEGER {
						target-certificate(0),
						target-ca-certificate(1)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The digital certificate file type."
::= { genEquipSecurityConfiguration 4 }

genEquipSecurityCfgSecurityFileFormat		OBJECT-TYPE
		SYNTAX 		INTEGER {
						pem(0),
						der(1)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The certificate file format.
		.pem - (Privacy Enhanced Mail) Base64 encoded DER certificate,
		 enclosed between -----BEGIN CERTIFICATE----- and -----END CERTIFICATE-----

		 .der is a subset of Basic Encoding Rules (BER) providing for exactly one way to encode an ASN.1 value."
::= { genEquipSecurityConfiguration 5 }

genEquipSecurityCfgSecurityWebCertificateAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The admin state of the certificate."
::= { genEquipSecurityConfiguration 6 }

genEquipSecurityCfgWebProtocol		OBJECT-TYPE
		SYNTAX 		INTEGER {
						http(1),
						https(2)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Use unsecure http or secure https."
::= { genEquipSecurityConfiguration 7 }

genEquipSecurityCfgTelnetAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Enable or disable the Telnet server on the NE."
::= { genEquipSecurityConfiguration 8 }

genEquipSecurityCfgAutoLogOutPeriod		OBJECT-TYPE
		SYNTAX 		INTEGER (1..60)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Auto logout period for CLI or web users, (1-60 seconds)"
::= { genEquipSecurityConfiguration 9 }

genEquipSecurityXFTP	OBJECT IDENTIFIER ::= {genEquipSecurityConfiguration 10}

genEquipSecurityXFTPHostIP		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The FTP/SFTP host IP address."
::= { genEquipSecurityXFTP 1 }

genEquipSecurityXFTPHostPath		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The FTP/SFTP host directory path."
::= { genEquipSecurityXFTP 2 }

genEquipSecurityXFTPProtocol		OBJECT-TYPE
		SYNTAX 		INTEGER {
						ftp(0),
						sftp(1)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Either FTP or SFTP to be used."
::= { genEquipSecurityXFTP 3 }

genEquipSecurityXFTPUserName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"FTP User name."
::= { genEquipSecurityXFTP 4 }

genEquipSecurityXFTPPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"FTP User password."
::= { genEquipSecurityXFTP 5 }

genEquipSecurityCfgPassFirstLoginChange		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Define whether user's first login will be required to change it password or not"
::= { genEquipSecurityConfiguration 11 }

genEquipSecurityCfgCSRCreation		OBJECT-TYPE
		SYNTAX 		DisplayString (SIZE(0..512))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This command create CSR file of the element.
		The file could be uploaded to external server by using the genEquipMngFileTransferFileTypeOper OID.
		The command syntax should be:
			<country_name> <state> <locality_name> <organization_name> <organization_unit_name> <common_name> <email_addr>.
			All the fields must be referred, for skipping on any field, use 'symbol instead'."
::= { genEquipSecurityConfiguration 12 }

genEquipSecurityCfgWarningBannerFName  		OBJECT-TYPE
		SYNTAX		DisplayString
		ACCESS		read-write
		STATUS		mandatory
		DESCRIPTION
		"The Warning Banner file name to be downloaded to the NE."
::= {  genEquipSecurityConfiguration 13 }

genEquipSecurityConfigurationRadius	OBJECT IDENTIFIER ::= {genEquipSecurityConfiguration 14}

genEquipSecurityConfigurationRadiusAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"this parameter configures the admin state of the RADIUS protocol; when enabled, all users will be logged in via RADIUS server"
::= { genEquipSecurityConfigurationRadius 1 }

genEquipSecurityConfigurationRadiusServerIP		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"this parameter configures the IP address of the RADIUS server"
::= { genEquipSecurityConfigurationRadius 2 }

genEquipSecurityConfigurationRadiusSecret		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"this parameter configures the common secret between the agent and the RADIUS server"
::= { genEquipSecurityConfigurationRadius 3 }

genEquipSecurityConfigurationRadiusPort		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"this parameter configures the IP address of UDP port used for the RADIUS protocol"
::= { genEquipSecurityConfigurationRadius 4 }

genEquipSecurityConfigurationRadiusRetries		OBJECT-TYPE
		SYNTAX 		INTEGER (1..10)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"this parameter configures the number of times the agent will retry to communicate with the RADIUS server before declaring the server to be unreacheable"
::= { genEquipSecurityConfigurationRadius 5 }

genEquipSecurityConfigurationRadiusTimeout		OBJECT-TYPE
		SYNTAX 		INTEGER (3..30)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"this parameter configures the number of times the agent will retry to communicate with the RADIUS server before declaring the server to be unreacheable"
::= { genEquipSecurityConfigurationRadius 6 }


-- Users and Groups
genEquipSecurityUsersAndGroups	OBJECT IDENTIFIER ::= {genEquipSecurity 2}

genEquipSecurityUsersTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityUsersEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "User table."
        ::= { genEquipSecurityUsersAndGroups 1 }

genEquipSecurityUsersEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityUsersEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "User table."
        INDEX { genEquipSecurityUsersName }
        ::= { genEquipSecurityUsersTable 1 }

GenEquipSecurityUsersEntry ::=
		SEQUENCE {
			genEquipSecurityUsersName
            	DisplayString,
			genEquipSecurityUsersPasswd
            	DisplayString,
			genEquipSecurityUsersPriviledge
            	INTEGER,
			genEquipSecurityUsersPasswdAging
            	INTEGER,
			genEquipSecurityUsersExprDate
            	INTEGER,
			genEquipSecurityUsersLastLogin
            	INTEGER,
			genEquipSecurityUsersRowStatus
            	RowStatus
}

genEquipSecurityUsersName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the user name."
::= { genEquipSecurityUsersEntry 1 }

genEquipSecurityUsersPasswd		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the user's password. It is used only in the creation phase. To change the password,
		 you need to use the genEquipSecurityUsersAndGroupsChangePasswd command"
::= { genEquipSecurityUsersEntry 2 }

genEquipSecurityUsersPriviledge		OBJECT-TYPE
		SYNTAX 		INTEGER {
							no-privilege-lvl(-1),
							viewer-user-lvl(0),
							operator-user-lvl(1),
							admin-user-lvl(2),
							tech-user-lvl(3)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the users privilege."
::= { genEquipSecurityUsersEntry 3 }

genEquipSecurityUsersPasswdAging		OBJECT-TYPE
		SYNTAX 		INTEGER (0..90)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the user's password aging time (in days). When response is 99999 the meaning is No Aging."
::= { genEquipSecurityUsersEntry 4 }

genEquipSecurityUsersExprDate		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the user's password expiration date.
		The value is presented in time_t format. When response is -1 the meaning is Never Expired."
::= { genEquipSecurityUsersEntry 5 }

genEquipSecurityUsersLastLogin		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value is the user last login date. Time in seconds since January 1, 1970 00:00 UTC."
::= { genEquipSecurityUsersEntry 6 }

genEquipSecurityUsersRowStatus		OBJECT-TYPE
		SYNTAX 		RowStatus
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the status of the entry."
::= { genEquipSecurityUsersEntry 30 }

-- User password chnage as a single command
genEquipSecurityUsersAndGroupsChangePasswd		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This command changes the user password at the command line. It takes all arguments as the command.
		It cannot be done through the table since it needs to provide the old password as well.
		The format is: '<username> <old-password> <new-password>' seperated with spaces."
::= { genEquipSecurityUsersAndGroups 2 }

-- SNMP Security params
genEquipSecuritySNMP	OBJECT IDENTIFIER ::= {genEquipSecurity 3}

genEquipSecuritySNMPReadCommunity		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This is the SNMP Read community string"
::= { genEquipSecuritySNMP 1 }

genEquipSecuritySNMPWriteCommunity		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This is the SNMP Write community string"
::= { genEquipSecuritySNMP 2 }

-- SNMP Security V3 Authentication table
genEquipSecuritySNMPV3	OBJECT IDENTIFIER ::= {genEquipSecuritySNMP 10}
-- SNMP V3 Authentication table
genEquipSecuritySNMPV3AuthTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecuritySNMPV3AuthEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Security General File Transfer Configuration Table."
        ::= { genEquipSecuritySNMPV3 1 }

genEquipSecuritySNMPV3AuthEntry OBJECT-TYPE
        SYNTAX GenEquipSecuritySNMPV3AuthEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Security General File Transfer Configuration Table"
        INDEX { genEquipSecuritySNMPV3AuthUserName }
        ::= { genEquipSecuritySNMPV3AuthTable 1 }

GenEquipSecuritySNMPV3AuthEntry ::=
		SEQUENCE {
			genEquipSecuritySNMPV3AuthUserName
            	DisplayString,
			genEquipSecuritySNMPV3AuthPassword
            	DisplayString,
			genEquipSecuritySNMPV3AuthSecurityMode
            	INTEGER,
			genEquipSecuritySNMPV3AuthEncryptionMode
            	INTEGER,
			genEquipSecuritySNMPV3AuthAuthenticationAlgorithm
            	INTEGER,
			genEquipSecuritySNMPV3AuthAccessMode
            	INTEGER,
			genEquipSecuritySNMPV3AuthRowStatus
            	RowStatus
}

genEquipSecuritySNMPV3AuthUserName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the V3 user name and the index of file v3 Authentication table."
::= { genEquipSecuritySNMPV3AuthEntry 1 }

genEquipSecuritySNMPV3AuthPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is file V3 user authentication password."
::= { genEquipSecuritySNMPV3AuthEntry 2 }

genEquipSecuritySNMPV3AuthSecurityMode		OBJECT-TYPE
		SYNTAX 		INTEGER {
						noAuthNoPriv(1),
						authNoPriv(2),
						authPriv(3)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This is the user's security mode"
::= { genEquipSecuritySNMPV3AuthEntry 3 }

genEquipSecuritySNMPV3AuthEncryptionMode		OBJECT-TYPE
		SYNTAX 		INTEGER {
						none(1),
						des(2),
						aes(3)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This is the V3 encryption mode."
::= { genEquipSecuritySNMPV3AuthEntry 4 }

genEquipSecuritySNMPV3AuthAuthenticationAlgorithm		OBJECT-TYPE
		SYNTAX 		INTEGER {
						none(1),
						sha(2),
						md5(3)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This is the V3 authentication algorithm."
::= { genEquipSecuritySNMPV3AuthEntry 5 }

genEquipSecuritySNMPV3AuthAccessMode		OBJECT-TYPE
		SYNTAX 		INTEGER {
						readWrite(1),
						readOnly(2)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This is the V3 user access mode."
::= { genEquipSecuritySNMPV3AuthEntry 6 }

genEquipSecuritySNMPV3AuthRowStatus		OBJECT-TYPE
		SYNTAX 		RowStatus
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This is the row status."
::= { genEquipSecuritySNMPV3AuthEntry 30 }

-- NG General Security params
genEquipSecurityGen	OBJECT IDENTIFIER ::= {genEquipSecurity 4}

-- NG Security General File Transfer Configuration Table
genEquipSecurityGenFTConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityGenFTConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Security General File Transfer Configuration Table."
        ::= { genEquipSecurityGen 1 }

genEquipSecurityGenFTConfigEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityGenFTConfigEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Security General File Transfer Configuration Table"
        INDEX { genEquipSecurityGenFTConfigIndex }
        ::= { genEquipSecurityGenFTConfigTable 1 }

GenEquipSecurityGenFTConfigEntry ::=
		SEQUENCE {
			genEquipSecurityGenFTConfigIndex
            	INTEGER,
			genEquipSecurityGenFTConfigProtocol
            	INTEGER,
			genEquipSecurityGenFTConfigUsername
            	DisplayString,
			genEquipSecurityGenFTConfigPassword
            	DisplayString,
			genEquipSecurityGenFTConfigAddress
            	IpAddress,
			genEquipSecurityGenFTConfigFilePath
            	DisplayString,
			genEquipSecurityGenFTConfigFileName
            	DisplayString,
            genEquipSecurityGenFTConfigIpV6Address
                OCTET STRING

}

genEquipSecurityGenFTConfigIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the index of file transfer table."
::= { genEquipSecurityGenFTConfigEntry 1 }

genEquipSecurityGenFTConfigProtocol		OBJECT-TYPE
		SYNTAX 		INTEGER {
						ftp(0),
						sftp(1)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is file transfer protocol."
::= { genEquipSecurityGenFTConfigEntry 2 }

genEquipSecurityGenFTConfigUsername		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the user's name"
::= { genEquipSecurityGenFTConfigEntry 3 }

genEquipSecurityGenFTConfigPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the users password."
::= { genEquipSecurityGenFTConfigEntry 4 }

genEquipSecurityGenFTConfigAddress		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the server IP."
::= { genEquipSecurityGenFTConfigEntry 5 }

genEquipSecurityGenFTConfigFilePath		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is file path."
::= { genEquipSecurityGenFTConfigEntry 6 }

genEquipSecurityGenFTConfigFileName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"This value is the file name."
::= { genEquipSecurityGenFTConfigEntry 7 }

genEquipSecurityGenFTConfigIpV6Address OBJECT-TYPE
		SYNTAX 		OCTET STRING (SIZE(0..16))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IP address v6 of the server, where the file are to be transferred to/from."
::= { genEquipSecurityGenFTConfigEntry 8 }

-- NG Security General File Transfer Status Table
genEquipSecurityGenFTStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityGenFTStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Security General File Transfer Status Table."
        ::= { genEquipSecurityGen 2 }

genEquipSecurityGenFTStatusEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityGenFTStatusEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Security General File Transfer Status Table."
        INDEX { genEquipSecurityGenFTStatusIndex }
        ::= { genEquipSecurityGenFTStatusTable 1 }

GenEquipSecurityGenFTStatusEntry ::=
		SEQUENCE {
			genEquipSecurityGenFTStatusIndex
            	INTEGER,
			genEquipSecurityGenFTStatusStatus
            	INTEGER,
			genEquipSecurityGenFTStatusProgress
            	INTEGER
}

genEquipSecurityGenFTStatusIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"This value is the index of file transfer status table."
::= { genEquipSecurityGenFTStatusEntry 1 }

genEquipSecurityGenFTStatusStatus		OBJECT-TYPE
		SYNTAX 		FTStatus
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"File transfer operation status."
::= { genEquipSecurityGenFTStatusEntry 2 }

genEquipSecurityGenFTStatusProgress		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Process prcentage"
::= { genEquipSecurityGenFTStatusEntry 3 }

-- General Security Scalars
genEquipSecurityGenFTOperations		OBJECT-TYPE
		SYNTAX 		INTEGER {
						none(0),
						upload-security-log(1)
		}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"File transfer operations"
::= { genEquipSecurityGen 11 }

genEquipSecurityGenImportExportAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Admin for import or export security settings"
::= { genEquipSecurityGen 12 }

-- genEquipSecurityGenWarningBannerDel
-- genEquipSecurityGenKeysGenerate
-- genEquipSecurityGenCreateSelfCertificate
-- genEquipSecurityGenCreateCSR

-- NG Access Control Security params
genEquipSecurityAccessControl	OBJECT IDENTIFIER ::= {genEquipSecurity 5}

-- NG Security Access Control Profile Table
genEquipSecurityAccessControlProfileTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityAccessControlProfileEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Security Access Control Profile Table."
        ::= { genEquipSecurityAccessControl 1 }

genEquipSecurityAccessControlProfileEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityAccessControlProfileEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Security Access Control Profile Table Entry"
        INDEX { genEquipSecurityAccessControlProfileName }
        ::= { genEquipSecurityAccessControlProfileTable 1 }

GenEquipSecurityAccessControlProfileEntry ::=
		SEQUENCE {
			genEquipSecurityAccessControlProfileName
            	DisplayString,
			genEquipSecurityAccessControlProfileChannel
            	INTEGER,
			genEquipSecurityAccessControlProfileUsed
            	INTEGER,
			genEquipSecurityAccessControlProfileSecurityWrite
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileSecurityRead
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileMngWrite
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileMngRead
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileRadioWrite
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileRadioRead
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileTDMWrite
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileTDMRead
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileEthWrite
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileEthRead
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileSyncWrite
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileSyncRead
            	RbacAccessLevel,
			genEquipSecurityAccessControlProfileRowStatus
            	RowStatus
}

genEquipSecurityAccessControlProfileName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User profile name."
::= { genEquipSecurityAccessControlProfileEntry 1 }

genEquipSecurityAccessControlProfileChannel		OBJECT-TYPE
		SYNTAX 		INTEGER (0..127)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Permitted access channels."
::= { genEquipSecurityAccessControlProfileEntry 2 }

genEquipSecurityAccessControlProfileUsed		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Indicate whether the profile in used"
::= { genEquipSecurityAccessControlProfileEntry 3 }

genEquipSecurityAccessControlProfileSecurityWrite		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Security functional write access level."
::= { genEquipSecurityAccessControlProfileEntry 4 }

genEquipSecurityAccessControlProfileSecurityRead		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Security functional read access level."
::= { genEquipSecurityAccessControlProfileEntry 5 }

genEquipSecurityAccessControlProfileMngWrite		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Management functional write access level."
::= { genEquipSecurityAccessControlProfileEntry 6 }

genEquipSecurityAccessControlProfileMngRead		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Management functional read access level."
::= { genEquipSecurityAccessControlProfileEntry 7 }

genEquipSecurityAccessControlProfileRadioWrite		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Radio functional write access level."
::= { genEquipSecurityAccessControlProfileEntry 8 }

genEquipSecurityAccessControlProfileRadioRead		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Radio functional read access level."
::= { genEquipSecurityAccessControlProfileEntry 9 }

genEquipSecurityAccessControlProfileTDMWrite		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"TDM functional write access level."
::= { genEquipSecurityAccessControlProfileEntry 10 }

genEquipSecurityAccessControlProfileTDMRead		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"TDM functional read access level."
::= { genEquipSecurityAccessControlProfileEntry 11 }

genEquipSecurityAccessControlProfileEthWrite		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Ether functional write access level."
::= { genEquipSecurityAccessControlProfileEntry 12 }

genEquipSecurityAccessControlProfileEthRead		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Ether functional read access level."
::= { genEquipSecurityAccessControlProfileEntry 13 }

genEquipSecurityAccessControlProfileSyncWrite		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Sync functional write access level."
::= { genEquipSecurityAccessControlProfileEntry 14 }

genEquipSecurityAccessControlProfileSyncRead		OBJECT-TYPE
		SYNTAX 		RbacAccessLevel
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Sync functional read access level."
::= { genEquipSecurityAccessControlProfileEntry 15 }

genEquipSecurityAccessControlProfileRowStatus		OBJECT-TYPE
		SYNTAX 		RowStatus
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Row Status for Profile table."
::= { genEquipSecurityAccessControlProfileEntry 30 }

-- NG Security Access Control User Table
genEquipSecurityAccessControlUserTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityAccessControlUserEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Security Access Control User Table."
        ::= { genEquipSecurityAccessControl 2 }

genEquipSecurityAccessControlUserEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityAccessControlUserEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Security Access Control User Table Entry"
        INDEX { genEquipSecurityAccessControlUserName }
        ::= { genEquipSecurityAccessControlUserTable 1 }

GenEquipSecurityAccessControlUserEntry ::=
		SEQUENCE {
			genEquipSecurityAccessControlUserName
            	DisplayString,
			genEquipSecurityAccessControlUserProfile
            	DisplayString,
			genEquipSecurityAccessControlUserPassword
            	DisplayString,
			genEquipSecurityAccessControlUserExpired
            	INTEGER,
			genEquipSecurityAccessControlUserBlock
            	NoYes,
			genEquipSecurityAccessControlUserLastLogout
            	INTEGER,
			genEquipSecurityAccessControlUserLoggedIn
            	NoYes,
			genEquipSecurityAccessControlUserRowStatus
            	RowStatus
}

genEquipSecurityAccessControlUserName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User name."
::= { genEquipSecurityAccessControlUserEntry 1 }

genEquipSecurityAccessControlUserProfile		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User's profile name."
::= { genEquipSecurityAccessControlUserEntry 2 }

genEquipSecurityAccessControlUserPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User's password"
::= { genEquipSecurityAccessControlUserEntry 3 }

genEquipSecurityAccessControlUserExpired		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"expired date of user account."
::= { genEquipSecurityAccessControlUserEntry 4 }

genEquipSecurityAccessControlUserBlock		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Block user account."
::= { genEquipSecurityAccessControlUserEntry 5 }

genEquipSecurityAccessControlUserLastLogout		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Current login indication."
::= { genEquipSecurityAccessControlUserEntry 6 }

genEquipSecurityAccessControlUserLoggedIn		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Current login indication."
::= { genEquipSecurityAccessControlUserEntry 7 }

genEquipSecurityAccessControlUserRowStatus		OBJECT-TYPE
		SYNTAX 		RowStatus
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Row Status for users table."
::= { genEquipSecurityAccessControlUserEntry 30 }

-- NG Access Control Scalars
genEquipSecurityAccessControlPassEnforceStrength		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Enforce strength password."
::= { genEquipSecurityAccessControl 11 }

genEquipSecurityAccessControlPassFirstLoginChange		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Password change for first login."
::= { genEquipSecurityAccessControl 12 }

genEquipSecurityAccessControlPassAging		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Password aging."
::= { genEquipSecurityAccessControl 13 }

genEquipSecurityAccessControlFailureLoginAttempt		OBJECT-TYPE
		SYNTAX 		INTEGER (1..10)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Failure login threshold blocking user account."
::= { genEquipSecurityAccessControl 14 }

genEquipSecurityAccessControlBlockFailureLoginPeriod		OBJECT-TYPE
		SYNTAX 		INTEGER (0..60)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Blocking period due to failure login."
::= { genEquipSecurityAccessControl 15 }

genEquipSecurityAccessControlBlockunusedAccount		OBJECT-TYPE
		SYNTAX 		INTEGER (0..90)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Period time for block unused account."
::= { genEquipSecurityAccessControl 16 }

genEquipSecurityAccessControlBlockRootRemote		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Root remote access."
::= { genEquipSecurityAccessControl 17 }

-- NG Protocols Control Security params
genEquipSecurityProtocolsControl	OBJECT IDENTIFIER ::= {genEquipSecurity 6}
-- genEquipSecurityProtocolsControlSNMPTable

-- genEquipSecurityProtocolsControlHTTPMode
-- genEquipSecurityProtocolsControlHTTPSClientAuth
-- genEquipSecurityProtocolsControlTelnetAdmin

genEquipSecurityProtocolsControlAutoSessionTimeOut		OBJECT-TYPE
		SYNTAX 		INTEGER (1..60)
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Logout session after incativity timeout."
::= { genEquipSecurityProtocolsControl 1 }

genEquipSecurityProtocolsControlSNMPAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"SNMP Admin."
::= { genEquipSecurityProtocolsControl 2}

genEquipSecurityProtocolsControlSNMPOperStatus		OBJECT-TYPE
		SYNTAX 		DownUp
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"SNMP Operational Status."
::= { genEquipSecurityProtocolsControl 3 }

genEquipSecurityProtocolsControlSNMPTrapVersion		OBJECT-TYPE
		SYNTAX 		INTEGER {
						v1(1),
						v2(2),
						v3(3)
					}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"SNMP Trap version."
::= { genEquipSecurityProtocolsControl 4 }

genEquipSecurityProtocolsControlSNMPMIBVersion		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"SNMP MIB version."
::= { genEquipSecurityProtocolsControl 5 }

genEquipSecurityProtocolsControlSNMPV1V2Blocked		OBJECT-TYPE
		SYNTAX 		NoYes
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"SNMP V1V2 is blocked."
::= { genEquipSecurityProtocolsControl 6 }

genEquipSecurityProtocolsControlHTTPAdmin		OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"HTTP Admin."
::= { genEquipSecurityProtocolsControl 7}


-- NG Monitor and Logs Security params
genEquipSecurityMonitorAndLogs	OBJECT IDENTIFIER ::= {genEquipSecurity 7}

-- Security Config Log Upload Configuration table file-transfer-config-table
genEquipSecurityConfigLogUploadConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityConfigLogUploadConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Config Log Upload Configuration."
        ::= { genEquipSecurityMonitorAndLogs 1 }

genEquipSecurityConfigLogUploadConfigEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityConfigLogUploadConfigEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Config Log Upload Configuration Table Entry"
        INDEX { genEquipSecurityConfigLogUploadConfigIndex }
        ::= { genEquipSecurityConfigLogUploadConfigTable 1 }

GenEquipSecurityConfigLogUploadConfigEntry ::=
		SEQUENCE {
			genEquipSecurityConfigLogUploadConfigIndex
            	INTEGER,
			genEquipSecurityConfigLogUploadConfigProtocol
            	FtpProtocolType,
			genEquipSecurityConfigLogUploadConfigUsername
            	DisplayString,
			genEquipSecurityConfigLogUploadConfigPassword
            	DisplayString,
			genEquipSecurityConfigLogUploadConfigIpaddress
            	IpAddress,
			genEquipSecurityConfigLogUploadConfigPath
            	DisplayString,
			genEquipSecurityConfigLogUploadConfigFilename
            	DisplayString,
			genEquipSecurityConfigLogUploadConfigIpV6Address
            	OCTET STRING
}

genEquipSecurityConfigLogUploadConfigIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration index. has Fixed value of 1"
::= { genEquipSecurityConfigLogUploadConfigEntry 1 }

genEquipSecurityConfigLogUploadConfigProtocol		OBJECT-TYPE
		SYNTAX 		FtpProtocolType
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration Protocol."
::= { genEquipSecurityConfigLogUploadConfigEntry 2 }

genEquipSecurityConfigLogUploadConfigUsername		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Username"
::= { genEquipSecurityConfigLogUploadConfigEntry 3 }

genEquipSecurityConfigLogUploadConfigPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration Password."
::= { genEquipSecurityConfigLogUploadConfigEntry 4 }

genEquipSecurityConfigLogUploadConfigIpaddress		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration ip address (Ipv4)."
::= { genEquipSecurityConfigLogUploadConfigEntry 5 }

genEquipSecurityConfigLogUploadConfigPath		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration Path."
::= { genEquipSecurityConfigLogUploadConfigEntry 6 }

genEquipSecurityConfigLogUploadConfigFilename		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration Filename."
::= { genEquipSecurityConfigLogUploadConfigEntry 7 }

genEquipSecurityConfigLogUploadConfigIpV6Address OBJECT-TYPE
		SYNTAX 		OCTET STRING (SIZE(0..16))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration ip address (Ipv6)."
::= { genEquipSecurityConfigLogUploadConfigEntry 8 }

-- Security Config Log Upload Status table security-config-log-upload-status-table
genEquipSecurityConfigLogUploadStatusTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityConfigLogUploadStatusEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Config Log Upload Status."
        ::= { genEquipSecurityMonitorAndLogs 2 }

genEquipSecurityConfigLogUploadStatusEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityConfigLogUploadStatusEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Config Log Upload Status Table Entry"
        INDEX { genEquipSecurityConfigLogUploadStatusIndex }
        ::= { genEquipSecurityConfigLogUploadStatusTable 1 }

GenEquipSecurityConfigLogUploadStatusEntry ::=
		SEQUENCE {
			genEquipSecurityConfigLogUploadStatusIndex
            	INTEGER,
			genEquipSecurityConfigLogUploadStatusStatus
            	FileTransferStatus,
			genEquipSecurityConfigLogUploadStatusPrcntg
				INTEGER
}

genEquipSecurityConfigLogUploadStatusIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Status index. has Fixed value of 1"
::= { genEquipSecurityConfigLogUploadStatusEntry 1 }

genEquipSecurityConfigLogUploadStatusStatus		OBJECT-TYPE
		SYNTAX 		FileTransferStatus
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Config Log Upload Configuration Protocol."
::= { genEquipSecurityConfigLogUploadStatusEntry 2 }

genEquipSecurityConfigLogUploadStatusPrcntg		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Progress status in percentage."
::= { genEquipSecurityConfigLogUploadStatusEntry 3 }


-- Svcalars
genEquipSecurityConfigLogUpload 	OBJECT-TYPE
		SYNTAX 		INTEGER {
						none(0),
						upload-security-log(1)
					}
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Configuration Log Upload."
::= { genEquipSecurityMonitorAndLogs 10 }


-- NG Sequrity RADIUS
genEquipSecurityRadiusServer	OBJECT IDENTIFIER ::= {genEquipSecurity 8}

-- NG Sequrity RADIUS table radius-configuration-table
genEquipSecurityRadiusServerConfigurationTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityRadiusServerConfigurationEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Radius configuration table."
        ::= { genEquipSecurityRadiusServer 1 }

genEquipSecurityRadiusServerConfigurationEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityRadiusServerConfigurationEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Radius configuration table Entry"
        INDEX { genEquipSecurityAccessControlRadiusServerId }
        ::= { genEquipSecurityRadiusServerConfigurationTable 1 }

GenEquipSecurityRadiusServerConfigurationEntry ::=
		SEQUENCE {
			genEquipSecurityAccessControlRadiusServerId
            	INTEGER,
			genEquipSecurityAccessControlRadiusServerIpV4Address
            	IpAddress,
			genEquipSecurityAccessControlRadiusServerIpv6Address
            	OCTET STRING,
			genEquipSecurityAccessControlRadiusServerPort
            	INTEGER,
			genEquipSecurityAccessControlRadiusServerRetries
            	INTEGER,
			genEquipSecurityAccessControlRadiusServerTimeout
            	INTEGER,
			genEquipSecurityAccessControlRadiusServerSharedSecret
            	OCTET STRING,
			genEquipSecurityAccessControlRadiusServerConnectivityStatus
            	EnableDisable
}

genEquipSecurityAccessControlRadiusServerId		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Server id: 1 - primary, 2-secondary"
::= { genEquipSecurityRadiusServerConfigurationEntry 1 }

genEquipSecurityAccessControlRadiusServerIpV4Address		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The IP address of the RADIUS Server."
::= { genEquipSecurityRadiusServerConfigurationEntry 2 }

genEquipSecurityAccessControlRadiusServerIpv6Address		OBJECT-TYPE
		SYNTAX 		OCTET STRING (SIZE(0..16))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The IP address of the RADIUS Server"
::= { genEquipSecurityRadiusServerConfigurationEntry 3 }

genEquipSecurityAccessControlRadiusServerPort		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The port ID of the RADIUS protocol in the RADIUS Server."
::= { genEquipSecurityRadiusServerConfigurationEntry 4 }

genEquipSecurityAccessControlRadiusServerRetries		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The number of times the agent will retry to communicate with the selected RADIUS server before declaring
		the server to be not reachable."
::= { genEquipSecurityRadiusServerConfigurationEntry 5 }

genEquipSecurityAccessControlRadiusServerTimeout		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The timeout that the agent will wait in each communication with the selected RADIUS server
		before retrying if no response is received."
::= { genEquipSecurityRadiusServerConfigurationEntry 6 }

genEquipSecurityAccessControlRadiusServerSharedSecret		OBJECT-TYPE
		SYNTAX 		OCTET STRING
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The shared secret of the Radius Server."
::= { genEquipSecurityRadiusServerConfigurationEntry 7 }

genEquipSecurityAccessControlRadiusServerConnectivityStatus  OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The connectivity status of the RADIUS server in the last login tried."
::= { genEquipSecurityRadiusServerConfigurationEntry 8 }



-- NG Sequrity RADIUS table security-radius-user-access-control-table
genEquipSecurityAccessControlRadiusUsersTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityAccessControlRadiusUsersEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "Radius privilege table."
        ::= { genEquipSecurityRadiusServer 2 }

genEquipSecurityAccessControlRadiusUsersEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityAccessControlRadiusUsersEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "Radius privilege table Entry"
        INDEX { genEquipSecurityAccessControlRadiusUsersId }
        ::= { genEquipSecurityAccessControlRadiusUsersTable 1 }

GenEquipSecurityAccessControlRadiusUsersEntry ::=
		SEQUENCE {
			genEquipSecurityAccessControlRadiusUsersId
            	DisplayString,
			genEquipSecurityAccessControlRadiusUserInstances
            	INTEGER,
			genEquipSecurityAccessControlRadiusUsersAccessChannels
            	INTEGER,
			genEquipSecurityAccessControlRadiusUsersEthReadLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersEthWriteLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersMngReadLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersMngWriteLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersRadioReadLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersRadioWriteLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersSecurityReadLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersSecurityWriteLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersSyncReadLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersSyncWriteLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersTdmReadLevel
            	RaduisAcceaaLevel,
			genEquipSecurityAccessControlRadiusUsersTdmWriteLevel
            	RaduisAcceaaLevel
}

genEquipSecurityAccessControlRadiusUsersId		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"User ID"
::= { genEquipSecurityAccessControlRadiusUsersEntry 1 }

genEquipSecurityAccessControlRadiusUserInstances		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"A number of the same connected users."
::= { genEquipSecurityAccessControlRadiusUsersEntry 2 }

genEquipSecurityAccessControlRadiusUsersAccessChannels		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"A bits stands for each access channel: serial-0, telnet-1, ssh-2, web-3"
::= { genEquipSecurityAccessControlRadiusUsersEntry 3 }

genEquipSecurityAccessControlRadiusUsersEthReadLevel		OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Ethernet Read Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 4 }

genEquipSecurityAccessControlRadiusUsersEthWriteLevel		OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Ethernet Write Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 5 }

genEquipSecurityAccessControlRadiusUsersMngReadLevel		OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for MNG Read Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 6 }

genEquipSecurityAccessControlRadiusUsersMngWriteLevel		OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for MNG Write Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 7 }

genEquipSecurityAccessControlRadiusUsersRadioReadLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Radio Read Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 8 }

genEquipSecurityAccessControlRadiusUsersRadioWriteLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Radio Write Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 9 }

genEquipSecurityAccessControlRadiusUsersSecurityReadLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Security Read Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 10 }

genEquipSecurityAccessControlRadiusUsersSecurityWriteLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Security Write Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 11 }

genEquipSecurityAccessControlRadiusUsersSyncReadLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Sync Read Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 12 }

genEquipSecurityAccessControlRadiusUsersSyncWriteLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for Sync Write Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 13 }

genEquipSecurityAccessControlRadiusUsersTdmReadLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for TDM Read Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 14 }

genEquipSecurityAccessControlRadiusUsersTdmWriteLevel  OBJECT-TYPE
		SYNTAX 		RaduisAcceaaLevel
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"The RADIUS permitted access for TDM Write Level functional group."
::= { genEquipSecurityAccessControlRadiusUsersEntry 15 }

--
-- RADIUS Scalars
genEquipSecurityRadiusAdmin OBJECT-TYPE
		SYNTAX 		EnableDisable
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Control working withRadius Server."
::= { genEquipSecurityRadiusServer 10 }


-- NG Sequrity Certificate
genEquipSecurityCertificate	OBJECT IDENTIFIER ::= {genEquipSecurity 9}

genEquipSecurityCsrCertificateFileTransferSet OBJECT-TYPE
		SYNTAX 		CsrCertificateFTState
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"CSR generate and Upload."
::= { genEquipSecurityCertificate 1 }

genEquipSecurityCsrStatus OBJECT-TYPE
		SYNTAX 		FTStatus
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Status of last geneate and upload."
::= { genEquipSecurityCertificate 2 }

genEquipSecurityCsrCertificateGenerateAndUploadPercentage OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Percentage of last generate CSR and upload."
::= { genEquipSecurityCertificate 3 }

genEquipSecurityCertificateInstallSet OBJECT-TYPE
		SYNTAX 		OffOn
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Certificate Install."
::= { genEquipSecurityCertificate 4 }

genEquipSecurityCertificateDownloadStatus OBJECT-TYPE
		SYNTAX 		FTStatus
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"tatus of last certificate download."
::= { genEquipSecurityCertificate 5 }

genEquipSecurityCertificateDownloadPercentage OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Percentage of certificate download operation."
::= { genEquipSecurityCertificate 6 }


-- NG Sequrity CSR Attributes Table security-csr-attributes-table
genEquipSecurityCsrAttributesTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityCsrAttributesEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "CSR Attributes Table."
        ::= { genEquipSecurityCertificate 10 }

genEquipSecurityCsrAttributesEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityCsrAttributesEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "CSR Attributes Table Entry"
        INDEX { genEquipSecurityCsrAttributesIndex }
        ::= { genEquipSecurityCsrAttributesTable 1 }

GenEquipSecurityCsrAttributesEntry ::=
		SEQUENCE {
			genEquipSecurityCsrAttributesIndex
				INTEGER,
			genEquipSecurityCsrAttributesCountry
            	DisplayString,
			genEquipSecurityCsrAttributesLocality
            	DisplayString,
			genEquipSecurityCsrAttributesState
            	DisplayString,
			genEquipSecurityCsrAttributesOrganization
            	DisplayString,
			genEquipSecurityCsrAttributesOu
            	DisplayString,
			genEquipSecurityCsrAttributesCommonName
            	DisplayString,
			genEquipSecurityCsrAttributesEmail
            	DisplayString,
			genEquipSecurityCsrAttributesFileFormat
            	CsrFileFormat
}

genEquipSecurityCsrAttributesIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Index to table, constant 1"
::= { genEquipSecurityCsrAttributesEntry 1 }

genEquipSecurityCsrAttributesCountry		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The two-letter ISO abbreviation for your country (ISO3166-1) (example US)."
::= { genEquipSecurityCsrAttributesEntry 2 }

genEquipSecurityCsrAttributesLocality		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The city where your organization is legally located"
::= { genEquipSecurityCsrAttributesEntry 3 }

genEquipSecurityCsrAttributesState		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The state or province where your organization is legally located.
		Can not be abbreviated."
::= { genEquipSecurityCsrAttributesEntry 4 }

genEquipSecurityCsrAttributesOrganization		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The exact legal name of your organization.
		Do not abbreviate your organization name."
::= { genEquipSecurityCsrAttributesEntry 5 }

genEquipSecurityCsrAttributesOu		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Section of the organization. Example: Marketing."
::= { genEquipSecurityCsrAttributesEntry 6 }

genEquipSecurityCsrAttributesCommonName		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"The fully qualified domain name for your web server.
		This must be an exact match."
::= { genEquipSecurityCsrAttributesEntry 7 }

genEquipSecurityCsrAttributesEmail  OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"An email address used to contact your organization."
::= { genEquipSecurityCsrAttributesEntry 8 }

genEquipSecurityCsrAttributesFileFormat OBJECT-TYPE
		SYNTAX 		CsrFileFormat
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"CSR file format. PEM or DER."
::= { genEquipSecurityCsrAttributesEntry 9 }

-- NG Sequrity CSR Upload Configuration Table security-csr-upload-configuration-table
genEquipSecurityCsrUploadConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityCsrUploadConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "CSR (Certificate Request) Upload Configuration Table."
        ::= { genEquipSecurityCertificate 11 }

genEquipSecurityCsrUploadConfigEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityCsrUploadConfigEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "CSR (Certificate Request) Upload Configuration Table Entry"
        INDEX { genEquipSecurityCsrUploadConfigIndex }
        ::= { genEquipSecurityCsrUploadConfigTable 1 }

GenEquipSecurityCsrUploadConfigEntry ::=
		SEQUENCE {
			genEquipSecurityCsrUploadConfigIndex
				INTEGER,
			genEquipSecurityCsrUploadConfigIpv4Address
            	IpAddress,
			genEquipSecurityCsrUploadConfigIpV6Address
            	OCTET STRING,
			genEquipSecurityCsrUploadConfigTableUsername
            	DisplayString,
			genEquipSecurityCsrUploadConfigPassword
            	DisplayString,
			genEquipSecurityCsrUploadConfigPath
            	DisplayString,
			genEquipSecurityCsrUploadConfigFilename
            	DisplayString
}

genEquipSecurityCsrUploadConfigIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"CSR upload Configuration index. has Fixed value of 1"
::= { genEquipSecurityCsrUploadConfigEntry 1 }

genEquipSecurityCsrUploadConfigIpv4Address		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IPv4 address of the server, where the file are to be transferred to/from."
::= { genEquipSecurityCsrUploadConfigEntry 2 }

genEquipSecurityCsrUploadConfigIpV6Address		OBJECT-TYPE
		SYNTAX 		OCTET STRING (SIZE(0..16))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IPv6 address of the server, where the file are to be transferred to/from"
::= { genEquipSecurityCsrUploadConfigEntry 3 }

genEquipSecurityCsrUploadConfigTableUsername		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User name for accesssing files in the server  (User credentials to access server)."
::= { genEquipSecurityCsrUploadConfigEntry 4 }

genEquipSecurityCsrUploadConfigPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Password (User credentials to access server)."
::= { genEquipSecurityCsrUploadConfigEntry 5 }

genEquipSecurityCsrUploadConfigPath		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Location of file in the server (folder)."
::= { genEquipSecurityCsrUploadConfigEntry 6 }

genEquipSecurityCsrUploadConfigFilename  OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Filename in the server, where the file are to be transferred to/from."
::= { genEquipSecurityCsrUploadConfigEntry 7 }

-- NG Sequrity CSR (Certificate Request) Download Configuration Table security-certificate-download-configuration-table
genEquipSecurityCertificateDownloadConfigTable  OBJECT-TYPE
        SYNTAX  SEQUENCE OF GenEquipSecurityCertificateDownloadConfigEntry
        ACCESS  not-accessible
        STATUS mandatory
        DESCRIPTION
        "CSR (Certificate Request) Download Configuration Table."
        ::= { genEquipSecurityCertificate 12 }

genEquipSecurityCertificateDownloadConfigEntry OBJECT-TYPE
        SYNTAX GenEquipSecurityCertificateDownloadConfigEntry
        ACCESS not-accessible
        STATUS mandatory
          DESCRIPTION
          "CSR (Certificate Request) Download Configuration Table Entry"
        INDEX { genEquipSecurityCertificateDownloadConfigIndex }
        ::= { genEquipSecurityCertificateDownloadConfigTable 1 }

GenEquipSecurityCertificateDownloadConfigEntry ::=
		SEQUENCE {
			genEquipSecurityCertificateDownloadConfigIndex
				INTEGER,
			genEquipSecurityCertificateDownloadConfigIpv4Address
            	IpAddress,
			genEquipSecurityCertificateDownloadConfigIpV6Address
            	OCTET STRING,
			genEquipSecurityCertificateDownloadConfigUsername
            	DisplayString,
			genEquipSecurityCertificateDownloadConfigPassword
            	DisplayString,
			genEquipSecurityCertificateDownloadConfigPath
            	DisplayString,
			genEquipSecurityCertificateDownloadConfigFilename
            	DisplayString
}

genEquipSecurityCertificateDownloadConfigIndex		OBJECT-TYPE
		SYNTAX 		INTEGER
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Certificate download configuration index. has Fixed value of 1"
::= { genEquipSecurityCertificateDownloadConfigEntry 1 }

genEquipSecurityCertificateDownloadConfigIpv4Address		OBJECT-TYPE
		SYNTAX 		IpAddress
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IPv4 address of the server, where the file are to be transferred to/from."
::= { genEquipSecurityCertificateDownloadConfigEntry 2 }

genEquipSecurityCertificateDownloadConfigIpV6Address		OBJECT-TYPE
		SYNTAX 		OCTET STRING (SIZE(0..16))
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"IPv6 address of the server, where the file are to be transferred to/from"
::= { genEquipSecurityCertificateDownloadConfigEntry 3 }

genEquipSecurityCertificateDownloadConfigUsername		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"User name for accesssing files in the server  (User credentials to access server)."
::= { genEquipSecurityCertificateDownloadConfigEntry 4 }

genEquipSecurityCertificateDownloadConfigPassword		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Password (User credentials to access server)."
::= { genEquipSecurityCertificateDownloadConfigEntry 5 }

genEquipSecurityCertificateDownloadConfigPath		OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-write
        STATUS		mandatory
        DESCRIPTION
		"Location of file in the server (folder)."
::= { genEquipSecurityCertificateDownloadConfigEntry 6 }

genEquipSecurityCertificateDownloadConfigFilename  OBJECT-TYPE
		SYNTAX 		DisplayString
		ACCESS		read-only
        STATUS		mandatory
        DESCRIPTION
		"Filename in the server, where the file are to be transferred to/from."
::= { genEquipSecurityCertificateDownloadConfigEntry 7 }




-- TRAP Section moved to MWRM-NETWORK-MIB

END