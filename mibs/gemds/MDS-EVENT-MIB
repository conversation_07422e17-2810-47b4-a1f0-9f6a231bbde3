MDS-EVENT-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, 
    OBJECT-TYPE, 
    NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE,
    OBJECT-GROUP, 
    NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    mdsLogging
        FROM MDS-ORBIT-SMI-MIB;

mdsEventMIB MODULE-IDENTITY
    LAST-UPDATED "201805160000Z" -- May 16, 2018
    ORGANIZATION 
        "GE MDS LLC
        http://www.gemds.com"
    CONTACT-INFO
        "T 1-800-474-0694 (Toll Free in North America)
        T 585-242-9600
        F 585-242-9620

        175 Science Parkway
        Rochester, New York 14620
        USA"
    DESCRIPTION
            "Notifications for GE MDS products."
    REVISION    "201805160000Z"
    DESCRIPTION
        "Updated conformance statments based on smilint."
    REVISION    "201304220000Z"
    DESCRIPTION
        "Initial version."
    ::= { mdsLogging 1 }

mdsEventMIBObjects OBJECT IDENTIFIER
    ::= { mdsEventMIB 1 }

mdsEventMIBNotifications OBJECT IDENTIFIER
    ::= { mdsEventMIB 2 }

mdsEventVariables OBJECT IDENTIFIER
    ::= { mdsEventMIBObjects 1 }

-- Notification objects
mdsEventName OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION    
        "The name of the event."
    ::= { mdsEventVariables 1 }

mdsEventInfoInCee OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (0..65535))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION    
        "The detailed information about the event encoded 
        in Common Event Expression (CEE) format."
    ::= { mdsEventVariables 2 }

-- Notifications

-- This is needed to make NOTIFICATION-TYPEs reverse-mappable
traps0  OBJECT IDENTIFIER ::= { mdsEventMIBNotifications 1 }
traps   OBJECT IDENTIFIER ::= { traps0 0 }

mdsEvent NOTIFICATION-TYPE
    STATUS  current
    DESCRIPTION
        "This is the common notification sent for any events
        generated by various subsystems in the product."
    ::= { traps 1 }

-- Conformance information
mdsEventMIBConformance  OBJECT IDENTIFIER ::= { mdsEventMIB 3 }
mdsEventMIBCompliances OBJECT IDENTIFIER ::= { mdsEventMIBConformance 1 }
mdsEventMIBGroups      OBJECT IDENTIFIER ::= { mdsEventMIBConformance 2 }

-- Compliance statements
mdsEventMIBCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for SNMP entities that
        implement the MDS-EVENT-MIB."
    MODULE -- this module
        MANDATORY-GROUPS {
        	mdsEventNotificationsGroup
        }
        GROUP   mdsEventVariablesCeeGroup
        DESCRIPTION
            "This group is mandatory for SNMP entities that
            support event information encoded in CEE format."

    ::= { mdsEventMIBCompliances 2 }

-- Units of Conformance
mdsEventNotificationsGroup  NOTIFICATION-GROUP
    NOTIFICATIONS {
    	mdsEvent
    }
    STATUS  current
    DESCRIPTION
        "The common notifications."
    ::= { mdsEventMIBGroups 1 }

mdsEventVariablesCeeGroup OBJECT-GROUP
    OBJECTS {
    	mdsEventName,
      mdsEventInfoInCee
    }
    STATUS  current
    DESCRIPTION
        "Information to support events that encode event 
        information in CEE format."
    ::= { mdsEventMIBGroups 2 }

END
