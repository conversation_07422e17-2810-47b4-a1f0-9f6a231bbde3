--  *****************************************************************
--  DLINKSW-TIME-MIB.mib : D-Link Time MIB
--
--  Copyright (c) 2013 D-Link Corporation, all rights reserved.
--
--  *****************************************************************
DLINKSW-TIME-MIB DEFINITIONS ::= BEGIN

    IMPORTS
        MODULE-IDENTITY,
        OBJECT-TYPE,                
        Unsigned32
                FROM SNMPv2-SMI
        OBJECT-GROUP, MODULE-COMPLIANCE			
            FROM SNMPv2-CONF
        TEXTUAL-CONVENTION,
        DateAndTime,
        TruthValue,
        DisplayString,
        RowStatus
            FROM SNMPv2-TC      
        InterfaceIndexOrZero
            FROM IF-MIB    
        InetAddressType,
        InetAddress
            FROM INET-ADDRESS-MIB       
        dlinkIndustrialCommon		
            FROM DLINK-ID-REC-MIB;


    dlinkSwTimeMIB MODULE-IDENTITY
        LAST-UPDATED "201308200000Z"
        ORGANIZATION "D-Link Corp."
        CONTACT-INFO
            "        D-Link Corporation

             Postal: No. 289, Sinhu 3rd Rd., Neihu District,
                     Taipei City 114, Taiwan, R.O.C
             Tel:     +886-2-66000123
             E-mail: <EMAIL>
            "
        DESCRIPTION
            "This MIB module defines objects for Time and Simple Network Time
            Protocol (SNTP)."
        REVISION "201303190000Z"
        DESCRIPTION
            "This is the first version of the MIB file. 
            "
        REVISION "201308200000Z"
        DESCRIPTION
            "1.Add Year for date type of DlinkTimeSummerTimeValue .
             2.Fixed bugs of type error for dTimeSntpServerLastReceive."
    ::= { dlinkIndustrialCommon 10 }

    DlinkTimeSummerTimeValue ::= TEXTUAL-CONVENTION
        STATUS       current
        DESCRIPTION
            "Denotes Summer Time starts or ends time.
            This value is always interpreted within the context of a 
            dTimeSummerTimeAutoSwitchMode value.
            When dTimeSummerTimeAutoSwitchMode is disable(1), this value is not
            meaningful.
            When dTimeSummerTimeAutoSwitchMode is recurring(2), the format is
            defined as follows:
            octets contents range
            ------ -------- -------
            1      week     1..4, ff last = ff
            2      day      1..7  where Sunday = 1 Saturday = 7
            3      month    1..12 where January = 1 December = 12
            4      hour     0..23
            5      minute   0..59
                       
            For example, the first Feb at 13:30pm should be given as 01 02 0d 1e
            For the last Tuesday in December at 1:20am should be given as ff 03 0c 01 14
        
        When dTimeSummerTimeAutoSwitchMode is date(3), the format is defined as follows:    
        
        octets    contents    range
        ------    --------    -------
           1-2        year    0..65536 in network-byte order
             3       month    1..12 where January = 1 December = 12
             4        date    1..31,
             5        hour    0..23
             6      minute    0..59
             
             For example, the 2013-4-2 03:00 should be given as 07 dd 04 02 03 00
             For the 2013-10-31 00:30 should be given as 07 dd 0a 1f 00 1e                            "
    SYNTAX       OCTET STRING (SIZE (0 | 5 | 6))

-- -----------------------------------------------------------------------------
    dTimeMIBNotifications     OBJECT IDENTIFIER ::= { dlinkSwTimeMIB 0 }
    dTimeMIBObjects           OBJECT IDENTIFIER ::= { dlinkSwTimeMIB 1 }
    dTimeMIBConformance       OBJECT IDENTIFIER ::= { dlinkSwTimeMIB 2 }
    
-- -----------------------------------------------------------------------------
    dTimeGeneral         OBJECT IDENTIFIER ::= { dTimeMIBObjects 1 }

    dTimeSntpEnabled OBJECT-TYPE
        SYNTAX          TruthValue
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "This object indicates the state of SNTP function.
            Setting this object to 'true' will enable SNTP function.
            "
        ::= { dTimeGeneral 1 }
        
    dTimeSntpBcastClientEnabled OBJECT-TYPE
        SYNTAX          TruthValue
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "This object is used to enable/disable the broadcast client mode.
            Setting this object to 'true' indicates that the agent accepts
            broadcast SNTP traffic from servers.
            "
        ::= { dTimeGeneral 2 }
    
    dTimeSntpPollInterval OBJECT-TYPE
        SYNTAX          Unsigned32 (30..99999)
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION 
            "This object indicates the interval for the SNTP client to
            synchronize its clock with server."
        DEFVAL      { 720 }
        ::= { dTimeGeneral 3 }
    
    dTimeSntpSourceIfIndex OBJECT-TYPE
        SYNTAX         InterfaceIndexOrZero
        MAX-ACCESS     read-write
        STATUS         current
        DESCRIPTION 
            "This object indicates the ifIndex of the interface whose IP address
            will be used as the source address for sending the SNTP packet.
            A special value of 0 indicates the source interface is not specified 
            and the IP address of the closest interface will be used.            
            "       
        ::= { dTimeGeneral 4 }
            
-- -----------------------------------------------------------------------------
    dTimeClock                   OBJECT IDENTIFIER ::= { dTimeMIBObjects 2 }    

    dTimeManagedClock OBJECT-TYPE
        SYNTAX          DateAndTime
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "Use the object to manually set the system clock."
        ::= { dTimeClock 1 }

    dTimeCurrentTimeSource OBJECT-TYPE
        SYNTAX          INTEGER  { 
            sntp(1), 
            noTimeSource(2)
        }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "The object indicates the clock source."
        ::= { dTimeClock 2 }

    dTimeCurrentTime  OBJECT-TYPE
        SYNTAX          DateAndTime
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the current system time. System time can
            be disciplined or adjusted by RTC, SNTP, or configured by user.
            "
        ::= { dTimeClock 3 }

-- -----------------------------------------------------------------------------    

    dTimeSummerTime         OBJECT IDENTIFIER ::= { dTimeClock 5 }

    dTimeSummerTimeAutoSwitchMode OBJECT-TYPE
        SYNTAX          INTEGER  { 
            disable(1), 
            recurring(2), 
            date(3) 
        }
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "This object indicates the mode of the system to automatically
            switch to Summer Time (Daylight Saving Time).
            disable(1) - The Daylight Saving Time feature is disabled.
            recurring(2) - The Daylight Saving Time feature is enabled. 
                         The Summer Time should start and end on the specified
                         week day of the specified month. 
            date(3) - The Daylight Saving Time feature is enabled. 
                      The Summer Time should start and end on the specified date
                      of the specified month.  
            "
        DEFVAL { disable }
        ::= { dTimeSummerTime 1 }
     
    dTimeSummerTimeStart OBJECT-TYPE
        SYNTAX          DlinkTimeSummerTimeValue
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION 
            "This object indicates when the Summer Time starts.
            This value is always interpreted within the context of a 
            dTimeSummerTimeAutoSwitchMode value.             
            "             
        ::= { dTimeSummerTime 2 }
        
    dTimeSummerTimeEnd OBJECT-TYPE
        SYNTAX          DlinkTimeSummerTimeValue
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "This object indicates when the Summer Time ends.
            This value is always interpreted within the context of a 
            dTimeSummerTimeAutoSwitchMode value.
            "  
        ::= { dTimeSummerTime 3 }
  
    dTimeSummerTimeOffset  OBJECT-TYPE
        SYNTAX      INTEGER (30|60|90|120)
        UNITS       "Minutes"
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The value of this object indicates number of minutes to add
            or to subtract during Summer Time. This object is not meaningful
            when dTimeSummerTimeAutoSwitchMode object is 'disable'."
        DEFVAL { 60 }
        ::= { dTimeSummerTime 4 }

-- -----------------------------------------------------------------------------
    dTimeServer                  OBJECT IDENTIFIER ::= { dTimeMIBObjects 3 }
    dTimeSntpServerTableNum  OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the number of entries present in the dTimeSntpServerTable."
        ::= { dTimeServer 1 }

    dTimeSntpServerTable  OBJECT-TYPE
        SYNTAX          SEQUENCE OF DTimeSntpServerEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This table consists of a list of information for SNTP Time 
            servers, which allow the software clock to be synchronized."
        ::= { dTimeServer 2 }

    dTimeSntpServerEntry  OBJECT-TYPE
        SYNTAX          DTimeSntpServerEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "An entry contains information about an SNTP Time server."
        INDEX { 
            dTimeSntpServerAddrType,
            dTimeSntpServerAddr,
            dTimeSntpServerVrfName
        }
        ::= { dTimeSntpServerTable 1 }

    DTimeSntpServerEntry ::= SEQUENCE {
        dTimeSntpServerAddrType         InetAddressType,
        dTimeSntpServerAddr             InetAddress,
        dTimeSntpServerVrfName          DisplayString,
        dTimeSntpServerRowStatus        RowStatus,        
        dTimeSntpServerVersion          Unsigned32,
        dTimeSntpServerLastReceive      Unsigned32,
        dTimeSntpServerSynced           TruthValue,
        dTimeSntpServerBcast            TruthValue
    }

    dTimeSntpServerAddrType  OBJECT-TYPE
        SYNTAX          InetAddressType
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object indicates the address type of the SNTP server." 
        ::= { dTimeSntpServerEntry 1 }
        
    dTimeSntpServerAddr  OBJECT-TYPE
        SYNTAX          InetAddress
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "IP or IPv6 address of the time server which provides the clock
             synchronization."
        ::= { dTimeSntpServerEntry 2 }
    
    dTimeSntpServerVrfName	OBJECT-TYPE
	    SYNTAX	        DisplayString
	    MAX-ACCESS      not-accessible
	    STATUS	        current
	    DESCRIPTION
		    "This object indicates the name of the routing forwarding instance.
		    A zero length string indicates the VRF name is not specified.
            For the platform that doesn't support VRF, only a zero length string
            is allowed for this object.
		    "
	    ::= { dTimeSntpServerEntry 3 }

    dTimeSntpServerRowStatus  OBJECT-TYPE
        SYNTAX          RowStatus
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
            "The status of this entry."
        ::= { dTimeSntpServerEntry 4 }
    	    
    dTimeSntpServerVersion  OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the SNTP server version. When 
            the value of dTimeSntpServerRowStatus at the same row is notInService(2),
            this object is 0.
            "
        ::= { dTimeSntpServerEntry 5 }

    dTimeSntpServerLastReceive OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS           "seconds"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the elapsed time that the SNTP time has
            been synchronized.             
            "
        ::= { dTimeSntpServerEntry 6 }

    dTimeSntpServerSynced  OBJECT-TYPE
        SYNTAX          TruthValue
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates whether the corresponding server is the
            current synced server.
            "
        ::= { dTimeSntpServerEntry 7 }

    dTimeSntpServerBcast  OBJECT-TYPE
        SYNTAX          TruthValue
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates whether the server is a broadcast SNTP server.
            "
        ::= { dTimeSntpServerEntry 8 }


--  ***************************************************************************	
--  Conformance
--  ***************************************************************************			   		
	dTimeCompliances OBJECT IDENTIFIER ::= { dTimeMIBConformance 1 }
		
	dTimeCompliance MODULE-COMPLIANCE
		STATUS current
		DESCRIPTION 
			"The compliance statement for entities which implement the 
			DLINKSW-TIME-MIB."
		MODULE -- this module
		MANDATORY-GROUPS { 
		    dTimeSysInfoGroup	   		   
		}
				
		GROUP    dTimeClockGroup 
        DESCRIPTION
           "This group is conditionally mandatory and must be implemented by
		    the agent only if system clock can be manually configured."
		   		   
		GROUP    dTimeSntpGroup 
        DESCRIPTION
           "This group is conditionally mandatory and must be implemented by
		    the agent only if SNTP feature is supported."
		        		        
        GROUP    dTimeSummerTimeCfgGroup 
        DESCRIPTION
           "This group is conditionally mandatory and must be implemented by
		   the agent only if Daylight Saving Time feature is supported.
		   "
		::= { dTimeCompliances 1 }

-- units of conformance		
	dTimeGroups OBJECT IDENTIFIER ::= { dTimeMIBConformance 2 }
		
	dTimeSysInfoGroup OBJECT-GROUP
		OBJECTS { 		    
		    dTimeCurrentTimeSource,
		    dTimeCurrentTime	    
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides the information for system time."
		::= { dTimeGroups 1 }
	
	dTimeClockGroup OBJECT-GROUP
		OBJECTS { 		    
		    dTimeManagedClock
     	}
		STATUS          current
		DESCRIPTION 
			"A collection of objects provides configuration for system time."
		::= { dTimeGroups 2 }
			
	dTimeSntpGroup OBJECT-GROUP
		OBJECTS { 
		   	dTimeSntpEnabled,
		    dTimeSntpBcastClientEnabled,
		    dTimeSntpPollInterval,
		    dTimeSntpSourceIfIndex,		   		   
		    dTimeSntpServerTableNum,
		    dTimeSntpServerRowStatus,                       
            dTimeSntpServerVersion,
            dTimeSntpServerLastReceive,
            dTimeSntpServerSynced,
            dTimeSntpServerBcast
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides configuration for SNTP."
			::= { dTimeGroups 3 }
	
	dTimeSummerTimeCfgGroup OBJECT-GROUP
		OBJECTS { 		    		    
		    dTimeSummerTimeAutoSwitchMode,
		    dTimeSummerTimeStart,
		    dTimeSummerTimeEnd,
		    dTimeSummerTimeOffset,
		    dTimeSntpServerTableNum,
		    dTimeSntpServerRowStatus
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides configuration for Daylight
			Saving Time feature."
		::= { dTimeGroups 4 }
	
	
END
