--  *****************************************************************
--  DLINKSW-SFLOW-MIB.mib : D-Link sFlow MIB
-- 
--  Copyright (c) 2013 D-Link Corporation, all rights reserved.
--   
--  *****************************************************************
DLINKSW-SFLOW-MIB DEFINITIONS ::= BEGIN

    IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, Integer32
            FROM SNMPv2-SMI  
        TEXTUAL-CONVENTION, RowStatus,DisplayString
            FROM SNMPv2-TC
        MODULE-COMPLIANCE, OBJECT-GROUP
		        FROM SNMPv2-CONF    
        SnmpAdminString
            FROM SNMP-FRAMEWORK-MIB                        
        
        InetAddressType, <PERSON><PERSON><PERSON><PERSON><PERSON>, InetA<PERSON>ressIPv4, InetAddressIPv6
            FROM INET-ADDRESS-MIB
        dlinkIndustrialCommon
            FROM DLINK-ID-REC-MIB;

    dlinkSwSFlowMIB MODULE-IDENTITY
        LAST-UPDATED "201304100000Z"
        ORGANIZATION "D-Link Corp."
        CONTACT-INFO
            "         D-Link Corporation
            
             Postal: No. 289, Sinhu 3rd Rd., Neihu District,
                     Taipei City 114, Taiwan, R.O.C
             Tel:    +886-2-66000123
             E-mail: <EMAIL>
            "      
        DESCRIPTION
		        "The structure of sFlow for the proprietary enterprise."  
            
        REVISION "201304100000Z"
        DESCRIPTION
            "Initial version of this MIB module."
        ::= { dlinkIndustrialCommon 36 }

-- -----------------------------------------------------------------------------

    dSFlowMIBNotifications    OBJECT IDENTIFIER ::= { dlinkSwSFlowMIB 0 }
    dSFlowMIBObjects          OBJECT IDENTIFIER ::= { dlinkSwSFlowMIB 1 }
    dSFlowMIBConformance      OBJECT IDENTIFIER ::= { dlinkSwSFlowMIB 2 }

-- -----------------------------------------------------------------------------
    DSFlowDataSource ::= TEXTUAL-CONVENTION
        STATUS      current
        DESCRIPTION
	          "Identifies the ifIndex of interface as the source of sFlow data.
	
	          DSFlowDataSources of this traditional form are called
	          'port-based'. Ideally the sampling entity will perform 
	          sampling on all flows originating from or destined to 
	          the specified interface. However, if the switch architecture 
	          only allows input or output sampling then the sampling agent 
	          is permitted to only sample input flows input or output flows. 
	          Each packet must only be considered once for sampling, 
	          irrespective of the number of ports it will be forwarded to.
	         	
	          Note: Since each DSFlowDataSource operates independently a 
	                packet that crosses multiple DataSources may generate 
	                multiple flow records."
        SYNTAX      Integer32 (1..2147483647)

    DSFlowInstance ::= TEXTUAL-CONVENTION
        STATUS      current
        DESCRIPTION
	          "If more than one sFlow sampler is available for this
	           DSFlowDataSource then individual samplers are distinguished
	           using the DSFlowInstance variable. The value of 
	           DSFlowInstance ranges from 1..n where n is the number of
	           samplers associated with this DSFlowDataSource.
	
	           Note: Each sFlow sampler instance must operate
	                 independently of all other instances. Setting
	                 an attribute of one sampler must not alter the
	                 the behavior and settings of other sampler
                   instances."
        SYNTAX      Integer32 (1..65535)

    DSFlowReceiver ::= TEXTUAL-CONVENTION
        STATUS       current
        DESCRIPTION
	          "Identify the sFlow receiver associated with this resource.
	
	           A value of zero indicates that this resource is available.
	           If non-zero the value must correspond to a valid, active 
	           dSFlowRcvrIndex.
	
	           If the value is currently zero it may be set to any
	           active entry in the dSFlowRcvrTable. If the value is
	           not zero then a set to anything other than zero or
	           its current value will result in an SNMP error (bad value).
	
	           Setting to zero frees the resource and returns all the
	           values in this entry to their default values.
	
	           If an entry in the dSFlowRcvrTable expires, either
	           because the dSFlowRcvrOwner is set to the empty
	           string or because the dSFlowRcvrCurrentTimeout reaches zero, then
	           the agent must mark all associated resources as available
	           (by setting the associated DSFlowReceiver entry to zero)
	           and all values in these records must be restored to their
	           default values.
	
	           This mechanism provides no enforcement and relies on the
	           cooperation of management entities in order to ensure that
	           competition for a resource is fairly resolved. A management
	           entity should not make any changes to a resource
	           without first acquiring it by successfully writing its
	           dSFlowRcvrIndex value as the DSFlowReceiver for the resource."
        SYNTAX       Integer32
 
-- -----------------------------------------------------------------------------      
    dSFlowVersion OBJECT-TYPE
        SYNTAX      SnmpAdminString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
	          "Uniquely identifies the version and implementation of this MIB.
	           The version string must have the following structure:
	              <MIB Version>;<Organization>;<Software Revision>
	           where:
	              <MIB Version>  must be '1.3', the version of referred SFLOW-MIB, 
                                 which is defined by sFlow.org.
	              <Organization> the name of the organization responsible
	                               for the agent implementation.
	              <Revision>     the specific software build of this agent.
	
	           As an example, the string '1.3;InMon Corp.;2.1.1' indicates
	           that this agent implements version '1.2' of the SFLOW MIB, that
	           it was developed by 'InMon Corp.' and that the software build
	           is '2.1.1'.
	
	           The MIB Version will change with each revision of the SFLOW-MIB.	           
	
	           Management entities must check the MIB Version and not attempt
	           to manage agents with MIB Versions greater than that for which
	           they were designed.
	
	           Note: The sFlow Datagram Format has an independent version
	                 number which may change independently from <MIB Version>.
	                 <MIB Version> applies to the structure and semantics of
	                 the SFLOW MIB only."
        DEFVAL { "1.3;;" }
        ::= { dSFlowMIBObjects 1 }

    dSFlowAgentAddressIPv4 OBJECT-TYPE
        SYNTAX          InetAddressIPv4
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
	          "The IPv4 address associated with this agent."
        ::= { dSFlowMIBObjects 2 }

    dSFlowAgentAddressIPv6 OBJECT-TYPE
	      SYNTAX        InetAddressIPv6
	      MAX-ACCESS    read-only
	      STATUS        current
	      DESCRIPTION
		        "The IPv6 address associated with this agent. If the IPv6
		        is not supported on this agent, 0::0 will be displayed."
	     ::= { dSFlowMIBObjects 3 }   

-- -----------------------------------------------------------------------------
-- Receiver Table
-- -----------------------------------------------------------------------------
    dSFlowRcvrTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DSFlowRcvrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
        		"A table of the receivers of sFlow information."
        ::= { dSFlowMIBObjects 4 }

    dSFlowRcvrEntry OBJECT-TYPE
        SYNTAX      DSFlowRcvrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Attributes of an sFlow Receiver."
        INDEX { dSFlowRcvrIndex }
        ::= { dSFlowRcvrTable 1 }

    DSFlowRcvrEntry ::= SEQUENCE {
        dSFlowRcvrIndex                    Integer32,
        dSFlowRcvrOwner                    DisplayString,
        dSFlowRcvrAdminTimeout             Integer32,
        dSFlowRcvrCurrentTimeout           Integer32,
        dSFlowRcvrMaximumDatagramSize      Integer32,
        dSFlowRcvrAddressType              InetAddressType,
        dSFlowRcvrAddress                  InetAddress,
        dSFlowRcvrVrfName                  DisplayString,
        dSFlowRcvrPort                     Integer32,
        dSFlowRcvrDatagramVersion          Integer32,
        dSFlowRcvrReset                    INTEGER
    }

    dSFlowRcvrIndex OBJECT-TYPE
        SYNTAX          Integer32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "Index into dSFlowReceiverTable. The number of supported receivers
            is project dependent."
        ::= { dSFlowRcvrEntry 1 }

    dSFlowRcvrOwner OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
	        "The entity making use of this dSFlowRcvrTable entry. The 
	        empty string indicates that the entry is currently unclaimed. 
	        An entity wishing to claim a dSFlowRcvrTable entry must 
	        ensure that the entry is unclaimed before trying to claim it. 
	        The entry is claimed by setting the owner string. The entry 
	        must be claimed before any changes can be made to the receiver's 
	        other objects.
		
	        When a management entity is finished using the receiver, it 
	        should set the value of dSFlowRcvrOwner back to unclaimed. 
	        The agent must restore all other entities this row to their 
	        default values when the owner is set to unclaimed. It must 
	        also free all other resources associated with this 
	        dSFlowRcvrTable entry.
	
	        This mechanism provides no enforcement and relies on the
	        cooperation of management entities in order to ensure that
	        competition for a receiver entry is fairly resolved."
        DEFVAL { "" }
        ::= { dSFlowRcvrEntry 2 }

    dSFlowRcvrAdminTimeout OBJECT-TYPE
        SYNTAX          Integer32 (0..2000000)
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
	        "The expire time (in seconds) configured for the receiver.
	
	        A management entity wanting to maintain control of the receiver
	        is responsible for setting a new value before the old one
	        expires.
	
	        When the interval expires, the agent is responsible for 
	        restoring all other entities in this row to their default 
	        values. It must also free all other resources associated with
	        this dSFlowRcvrTable entry.
	         
	        When set, 0 means infinite that the receiver will never be 
	        expired." 
        DEFVAL { 0 }
        ::= { dSFlowRcvrEntry 3 } 
        
    dSFlowRcvrCurrentTimeout OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
	          "The time (in seconds) remaining before the receiver is released." 
        DEFVAL { 0 }
        ::= { dSFlowRcvrEntry 4 }

    dSFlowRcvrMaximumDatagramSize OBJECT-TYPE
        SYNTAX      Integer32 (700..1400)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The maximum number of data bytes that can be sent in a single
             sample datagram. The manager should set this value to avoid
             fragmentation of the sFlow datagrams."
        DEFVAL { 1400 }
        ::= { dSFlowRcvrEntry 5 }

    dSFlowRcvrAddressType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The address type of the sFlow collector."
        DEFVAL { ipv4 }
        ::= { dSFlowRcvrEntry 6 }

    dSFlowRcvrAddress OBJECT-TYPE
        SYNTAX      InetAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
	        "The IP address of the sFlow collector.
	        If set to 0.0.0.0 no sFlow datagrams will be sent."
        DEFVAL { '********'h }  -- 0.0.0.0
        ::= { dSFlowRcvrEntry 7 }
        
    dSFlowRcvrVrfName OBJECT-TYPE
        SYNTAX          DisplayString
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
	        "Specify the name of the routing forwarding instance."
        DEFVAL { "" }
        ::= { dSFlowRcvrEntry 8 }

    dSFlowRcvrPort OBJECT-TYPE
        SYNTAX          Integer32 (1..65535)
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
          	"The UDP port of the remote sFlow collector."
        DEFVAL { 6343 }
        ::= { dSFlowRcvrEntry 9 }

    dSFlowRcvrDatagramVersion OBJECT-TYPE
        SYNTAX          Integer32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
          	"The version of sFlow datagrams that should be sent."
        DEFVAL { 5 }
        ::= { dSFlowRcvrEntry 10 }
        
    dSFlowRcvrReset OBJECT-TYPE
        SYNTAX          INTEGER {
            reset(1),
            noOp(2)  
        }
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
          	"This object reset the receiver when set to 'reset'.
             No action is taken if this object is set to 'noOp'.
             When read, the value 'noOp' is returned."
        DEFVAL { noOp }
        ::= { dSFlowRcvrEntry 11 }                

-- -----------------------------------------------------------------------------
-- Flow Sampling Table
-- -----------------------------------------------------------------------------
    dSFlowFsTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DSFlowFsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
        		"A table of the flow samplers within a device."
        ::= { dSFlowMIBObjects 5 }

    dSFlowFsEntry OBJECT-TYPE
        SYNTAX      DSFlowFsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
          	"Attributes of a flow sampler."
        INDEX { 
            dSFlowFsDataSource, 
            dSFlowFsInstance 
        }
        ::= { dSFlowFsTable 1 }

    DSFlowFsEntry ::= SEQUENCE {
        dSFlowFsDataSource               DSFlowDataSource,
        dSFlowFsInstance                 DSFlowInstance,
        dSFlowFsReceiver                 DSFlowReceiver,
        dSFlowFsDirection                INTEGER,
        dSFlowFsAdminSamplingRate        Integer32,
        dSFlowFsActiveSamplingRate       Integer32,
        dSFlowFsMaximumHeaderSize        Integer32,
        dSFlowFsRowStatus                RowStatus
    }

    dSFlowFsDataSource OBJECT-TYPE
        SYNTAX          DSFlowDataSource
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
          	"This object indicates the source of the data for this flow 
            sampler."
        ::= { dSFlowFsEntry 1 }

    dSFlowFsInstance OBJECT-TYPE
        SYNTAX          DSFlowInstance
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
          	"The instance index for uniquely identifying the flow sampler
            when multiple flow samplers are associated with one interface."
        ::= { dSFlowFsEntry 2 }

    dSFlowFsReceiver OBJECT-TYPE
        SYNTAX          DSFlowReceiver
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
          	"The index of the sFlow receiver for this flow sampler."
        DEFVAL { 0 }
        ::= { dSFlowFsEntry 3 } 
        
    dSFlowFsDirection OBJECT-TYPE
        SYNTAX          INTEGER{
            inbound(1),
            outbound(2)  
        }
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
          	"This object indicates the flow direction which will be sampled.
            'inbound' -  sample ingress packets.
            'outbound' - sample egress packets."
        DEFVAL { inbound }
        ::= { dSFlowFsEntry 4 } 

    dSFlowFsAdminSamplingRate OBJECT-TYPE
        SYNTAX          Integer32 (0..65536)
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
	        "The configured sampling rate for packet sampling from this
	        source. 
	
	        Set to N to sample 1/Nth of the packets in the monitored flows.
	        An agent should choose its own algorithm to introduce variance
	        into the sampling so that exactly every Nth packet is not
	        counted. A sampling rate of 1 counts all packets. A sampling
	        rate of 0 disables sampling.
	           
	        The agent is permitted to have minimum and maximum allowable
	        values for the sampling rate. A minimum rate lets the agent
	        designer set an upper bound on the overhead associated with
	        sampling, and a maximum rate may be the result of hardware
	        restrictions (such as counter size). In addition not all values
	        between the maximum and minimum may be realizable as the 
	        sampling rate (again because of implementation considerations).
	
	        When the sampling rate is set the agent is free to adjust the
	        value so that it lies between the maximum and minimum values
	        and has the closest achievable value.
	
	        When read, the configured sampling rate will be returned."
        DEFVAL { 0 }
        ::= { dSFlowFsEntry 5 }
            
    dSFlowFsActiveSamplingRate OBJECT-TYPE
        SYNTAX          Integer32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
	        "The statistical sampling rate for packet sampling from this
	        source.              

	        When read, the agent must return the actual sampling rate it
	        will be using (after the adjustments previously described). The
	        sampling algorithm must converge so that over time the number
	        of packets sampled approaches 1/Nth of the total number of
	        packets in the monitored flows."   
	    DEFVAL { 0 }         
        ::= { dSFlowFsEntry 6 }            

    dSFlowFsMaximumHeaderSize OBJECT-TYPE
        SYNTAX          Integer32 (18..256)
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
	        "The maximum number of bytes that should be copied from a
	        sampled packet. The agent may have an internal maximum and
	        minimum permissible sizes. If an attempt is made to set this 
	        value outside the permissible range then the agent should 
	        adjust the value to the closest permissible value."
        DEFVAL { 128 }
        ::= { dSFlowFsEntry 7 }
            
    dSFlowFsRowStatus OBJECT-TYPE
        SYNTAX          RowStatus
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
            "This object indicates the status of this entry."
        ::= { dSFlowFsEntry 8 }            

-- -----------------------------------------------------------------------------
-- Counter Polling Table
-- -----------------------------------------------------------------------------     
    dSFlowCpTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF DSFlowCpEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
          	"A table of the counter pollers within a device."
        ::= { dSFlowMIBObjects 6 }

    dSFlowCpEntry OBJECT-TYPE
        SYNTAX          DSFlowCpEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
          	"Attributes of a counter poller."
        INDEX { 
            dSFlowCpDataSource, 
            dSFlowCpInstance 
        }
        ::= { dSFlowCpTable 1 }

    DSFlowCpEntry ::= SEQUENCE {
        dSFlowCpDataSource               DSFlowDataSource,
        dSFlowCpInstance                 DSFlowInstance,
        dSFlowCpReceiver                 DSFlowReceiver,
        dSFlowCpInterval                 Integer32,
        dSFlowCpRowStatus                RowStatus
    }

    dSFlowCpDataSource OBJECT-TYPE
        SYNTAX          DSFlowDataSource
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
          	"Identifies the source of the data for the counter poller."
        ::= { dSFlowCpEntry 1 }

    dSFlowCpInstance OBJECT-TYPE
        SYNTAX          DSFlowInstance
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
          	"The instance instance index for uniquely identifying the counter
            poller when multiple Pollers are associated with one interface."
        ::= { dSFlowCpEntry 2 }

    dSFlowCpReceiver OBJECT-TYPE
        SYNTAX          DSFlowReceiver
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
          	"The sflow receiver's index for this counter poller."
        DEFVAL { 0 }
        ::= { dSFlowCpEntry 3 }

    dSFlowCpInterval OBJECT-TYPE
        SYNTAX          Integer32 (0..120)
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
	        "The maximum number of seconds between successive samples of the
	        counters associated with this data source. A sampling interval 
	        of 0 disables counter sampling.
	
	        The agent is permitted to have minimum and maximum allowable
	        values for the counter polling interval. A minimum interval
	        lets the agent designer set an upper bound on the overhead
	        associated with polling, and a maximum interval may be the
	        result of implementation restrictions (such as counter size).
	        In addition not all values between the maximum and minimum may
	        be realizable as the sampling interval (again because of
	        implementation considerations)."
        DEFVAL { 0 }
        ::= { dSFlowCpEntry 4 }
            
    dSFlowCpRowStatus OBJECT-TYPE
        SYNTAX          RowStatus
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION
           	"This object indicates the status of this entry."
        ::= { dSFlowCpEntry 5 }        

-- -----------------------------------------------------------------------------
--  MIB Conformance statements
-- -----------------------------------------------------------------------------
    dSFlowMIBCompliances    OBJECT IDENTIFIER ::= { dSFlowMIBConformance 1 }
    dSFlowMIBGroups 				OBJECT IDENTIFIER ::= { dSFlowMIBConformance 2 }

    dSFlowCompliance MODULE-COMPLIANCE
        STATUS      current
        DESCRIPTION
          	"The compliance statement for entities which implement the 
			DLINKSW-SFLOW-MIB."

        MODULE -- this module
        MANDATORY-GROUPS { dSFlowAgentGroup }

        OBJECT     dSFlowRcvrAddressType        
        DESCRIPTION
            "It is compliant to support only a subset of values. For example if
            the platform doesn't support IPv6, the type of 'ipv6' cannot be
            configured."
        
        OBJECT dSFlowRcvrVrfName
        DESCRIPTION
            "When the agent doesn't support VRF, only zero string can be set."

        ::= { dSFlowMIBCompliances 1 }

    dSFlowAgentGroup OBJECT-GROUP
        OBJECTS { 
        	dSFlowVersion, dSFlowAgentAddressIPv4, dSFlowAgentAddressIPv6,
            dSFlowRcvrOwner,dSFlowRcvrAdminTimeout,dSFlowRcvrCurrentTimeout,
            dSFlowRcvrMaximumDatagramSize, dSFlowRcvrAddressType, 
            dSFlowRcvrAddress, dSFlowRcvrPort, dSFlowRcvrVrfName,
            dSFlowRcvrDatagramVersion, dSFlowRcvrReset,
            dSFlowFsReceiver, dSFlowFsDirection,
            dSFlowFsAdminSamplingRate, dSFlowFsActiveSamplingRate, 
            dSFlowFsMaximumHeaderSize, dSFlowFsRowStatus,
            dSFlowCpReceiver, dSFlowCpInterval, dSFlowCpRowStatus
        }
         STATUS current
         DESCRIPTION
           "A collection of objects for managing the generation and
           transportation of sFlow data records."
        ::= { dSFlowMIBGroups 1 }

END

