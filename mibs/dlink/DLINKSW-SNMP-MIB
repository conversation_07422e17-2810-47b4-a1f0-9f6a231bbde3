--  *****************************************************************
--  DLINKSW-SNMP-MIB.mib : SNMP management MIB
--
--  Copyright (c) 2013 D-Link Corporation, all rights reserved.
--
--  *****************************************************************
DLINKSW-SNMP-MIB DEFINITIONS ::= BEGIN

	IMPORTS
   		MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, Unsigned32        
       		FROM SNMPv2-SMI
   		DisplayString, TruthValue
       		FROM SNMPv2-TC
   		MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
       		FROM SNMPv2-CONF               		
        SnmpEngineID, SnmpAdminString, SnmpSecurityModel                      
            FROM SNMP-FRAMEWORK-MIB
        ifIndex, InterfaceIndexOrZero
            FROM IF-MIB                  
   		dlinkIndustrialCommon		
            FROM DLINK-ID-REC-MIB;
       
   
   dlinkSwSnmpMIB	MODULE-IDENTITY
   		LAST-UPDATED "201304150000Z"
   		ORGANIZATION "D-Link Corp."
        CONTACT-INFO
            "        D-Link Corporation

             Postal: No. 289, Sinhu 3rd Rd., Neihu District,
                     Taipei City 114, Taiwan, R.O.C
             Tel:     +886-2-66000123
             E-mail: <EMAIL>
            "
        DESCRIPTION
            "This MIB module defines objects for configuring SNMP 
            ( Simple Network Management Protocol )"
            
        REVISION "201304150000Z"
        DESCRIPTION
            "This is the first version of the MIB file for 'SNMP'
            functionality."  
       
        ::= { dlinkIndustrialCommon 2 } 
            
	dSnmpMIBNotifications 	OBJECT IDENTIFIER ::= { dlinkSwSnmpMIB 0 }
	dSnmpMIBObjects 		OBJECT IDENTIFIER ::= { dlinkSwSnmpMIB 1 }
	dSnmpMIBConformance		OBJECT IDENTIFIER ::= { dlinkSwSnmpMIB 2 }
    

-- -------------------------------------------------------------------------- 
	dSnmpGeneral			OBJECT IDENTIFIER ::= { dSnmpMIBObjects 1 }
		
	dSnmpServiceEnabled OBJECT-TYPE
   		SYNTAX          TruthValue
  		MAX-ACCESS      read-write
   		STATUS          current
   		DESCRIPTION
           "A Boolean value represents the SNMP server is Enabled or Disabled."
   		::= { dSnmpGeneral 1 }
   	
   	dSnmpServiceUdpPort OBJECT-TYPE
   		SYNTAX          Unsigned32
  		MAX-ACCESS      read-write
   		STATUS          current
   		DESCRIPTION
           "This object indicates the SNMP UDP port number.
           By default, the SNMP agent receives requests on UDP port 161."
   		::= { dSnmpGeneral 2 }
   
   	dSnmpRspBcastRequestEnabled OBJECT-TYPE
   		SYNTAX          TruthValue
  		MAX-ACCESS      read-write
   		STATUS          current
   		DESCRIPTION
           "A Boolean value represents whether response broadcast SNMP
           GetRequest packet."
   		::= { dSnmpGeneral 3 }
   	          
    dSnmpLocalEngineID OBJECT-TYPE
        SYNTAX          SnmpEngineID
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "This object indicates the agent's local engineID value.
            Changing this variable will affect the value of snmpEngineID. 
            The special value of '00 00 00 00 00'H indicates the local engineID 
            is not specified, and snmpEngineID will be automatically created
            based on the device basic MAC address.
            The SNMP engine ID is a unique string to identify the device. 
            The string is generated by default. Check RFC3411 for further
            details on the SNMP engine ID."       
        ::= { dSnmpGeneral 4 }   
        
-- -------------------------------------------------------------------------- 
	dSnmpMIBTrap				OBJECT IDENTIFIER ::= { dSnmpMIBObjects 2 }
	
	dSnmpTrapGlobalEnabled OBJECT-TYPE
       	SYNTAX          TruthValue
  		MAX-ACCESS      read-write
   		STATUS          current
   		DESCRIPTION
            "A Boolean value to represent the global state of sending of trap
            packets.
            "
   		::= { dSnmpMIBTrap 1 }

    dSnmpTrapGlobalNotifyEnable OBJECT-TYPE
        SYNTAX          BITS {         
            linkUp(0),
            linkDown(1),
            coldStart(2),
            warmStart(3),
            authentication(4)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "This object indicates global state about whether a specified
            notification is enabled or not.
            If a bit corresponding to a notification is set to 1, then 
            the specified notification can be generated.

            linkUp(0)    - sending a standard linkUp notification (defined in IF-MIB). 
            linkDown(1)  - sending a standard linkDown notification (defined in IF-MIB).  
            coldStart(2) - sending a standard coldStart notification (defined in SNMPv2-MIB).
            warmStart(3) - sending a standard warmStart notification (defined in SNMPv2-MIB).
            Note: please use snmpEnableAuthenTraps (defined in SNMPv2-MIB) to control 
            the generation of authenticationFailure traps.
            "
        ::= { dSnmpMIBTrap 2 }
      
    dSnmpTrapSourceIfIndex  OBJECT-TYPE
        SYNTAX          InterfaceIndexOrZero
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "The ifIndex of the interface whose IP address is used as the
            source IP address for outbound SNMP traps.
            A special value of zero indicates the source interface is not
            specified, the IP address of the closest interface will be used.           
            "
        ::= { dSnmpMIBTrap 3 }		   
-- -------------------------------------------------------------------------- 	
	dSnmpTrapIfCtrl			OBJECT IDENTIFIER ::= { dSnmpMIBTrap 4 }
                        
    dSnmpTrapIfCfgTable	OBJECT-TYPE
		SYNTAX          SEQUENCE OF DSnmpTrapIfCfgEntry
		MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This table contains a list of interface-specific notification
            configuration.
            "
        ::= {dSnmpTrapIfCtrl 1}
      
    dSnmpTrapIfCfgEntry	OBJECT-TYPE
		SYNTAX	        DSnmpTrapIfCfgEntry
		MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "An entry manages notification on an interface."
        INDEX  {  ifIndex }
      ::= {dSnmpTrapIfCfgTable 1}
      
    DSnmpTrapIfCfgEntry ::=
        SEQUENCE {
            dSnmpTrapIfSendTrapEnabled     TruthValue
        }  
   	
   	dSnmpTrapIfSendTrapEnabled OBJECT-TYPE
        SYNTAX          TruthValue
        MAX-ACCESS      read-create
        STATUS          current
        DESCRIPTION     
            "This object indicates whether sending trap on the interface.
            If the state is disabled, then SNMP notification traps generated
            by the system are not allowed to transmit out of the port. 
            " 
        DEFVAL { true }
        ::= { dSnmpTrapIfCfgEntry 1 }
        
   
        		
-- -------------------------------------------------------------------------- 	 
    dSnmpAccessCfg 			OBJECT IDENTIFIER ::= { dSnmpMIBObjects 3 }
         
    dSnmpCommunityTable  OBJECT-TYPE
        SYNTAX          SEQUENCE OF DSnmpCommunityEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This table consists of a list of communities.
            "       
        ::= { dSnmpAccessCfg 1 }

    dSnmpCommunityEntry  OBJECT-TYPE
        SYNTAX          DSnmpCommunityEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "An entry defines additional information which is not covered at
            snmpCommunityTable (defined in SNMP-COMMUNITY-MIB).
            An entry appears in this table when a community is created in 
            snmpCommunityTable.
            "
        INDEX     { dSnmpCommunityName }
        ::= { dSnmpCommunityTable 1 }

    DSnmpCommunityEntry ::= SEQUENCE {
        dSnmpCommunityName               SnmpAdminString,
        dSnmpCommunityAccessListName     DisplayString       
    }

    dSnmpCommunityName  OBJECT-TYPE
        SYNTAX          SnmpAdminString
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
           "This object indicates the name of the community."
        ::= { dSnmpCommunityEntry 1 }
   	
    dSnmpCommunityAccessListName  OBJECT-TYPE
        SYNTAX          DisplayString 
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
           "This object indicates the name of a standard access list to control
           the user to use this community string to access to the SNMP agent. 
           Specify the valid user in the source address field of the access 
           list entry.
           A zero length string indicates the access list is not specified.          
           "
        ::= { dSnmpCommunityEntry 3 }

-- --------------------------------------------------------------------------
    dSnmpGroupTable  OBJECT-TYPE
        SYNTAX          SEQUENCE OF DSnmpGroupEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This table consists of a list of groups.
            "       
        ::= { dSnmpAccessCfg 2 }

    dSnmpGroupEntry  OBJECT-TYPE
        SYNTAX          DSnmpGroupEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "An entry consists of additional informaiton which is not covered
            at vacmAccessTable (defined in SNMP-VIEW-BASED-ACM-MIB).
            An entry appears in this table when a group is created in 
            vacmAccessTable or vacmSecurityToGroupTable.
            "
        INDEX     { 
            dSnmpGroupName ,
        	dSnmpGroupVersion
        }
        ::= { dSnmpGroupTable 1 }

    DSnmpGroupEntry ::= SEQUENCE {
        dSnmpGroupName               SnmpAdminString,  
        dSnmpGroupVersion            SnmpSecurityModel,
        dSnmpGroupAccessListName     DisplayString       
    }

    dSnmpGroupName  OBJECT-TYPE
        SYNTAX          SnmpAdminString
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
           "This object indicates the name of the group."
        ::= { dSnmpGroupEntry 1 }
    
    dSnmpGroupVersion  OBJECT-TYPE 
      SYNTAX          SnmpSecurityModel 
      MAX-ACCESS      not-accessible 
      STATUS          current 
      DESCRIPTION 
         "This object indicates the version of the entry." 
      ::= { dSnmpGroupEntry 2 } 

       	
    dSnmpGroupAccessListName  OBJECT-TYPE
        SYNTAX          DisplayString
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
           "This object indicates the name of a standard access control list
           (ACL) to associate with the group.
           A zero length string indicates the access list is not specified.  
           "
        ::= { dSnmpGroupEntry 3 }
        
-- --------------------------------------------------------------------------
    dSnmpHostTable  OBJECT-TYPE
        SYNTAX          SEQUENCE OF DSnmpHostEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This table consists of a list of recipients for the SNMP
            notification. 
            "       
        ::= { dSnmpAccessCfg 3 }

    dSnmpHostEntry  OBJECT-TYPE
        SYNTAX          DSnmpHostEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "An entry consists of additional informaiton which is not covered
            at snmpTargetAddrTable and snmpTargetParamsTable (defined in 
            SNMP-TARGET-MIB).           
            An entry appears in this table when a target address is created in 
            snmpTargetAddrTable.
            "            
        INDEX     { dSnmpHostAddrName }
        ::= { dSnmpHostTable 1 }

    DSnmpHostEntry ::= SEQUENCE {
        dSnmpHostAddrName            SnmpAdminString,
        dSnmpHostVrfName             DisplayString,
        dSnmpHostUdpPort             Unsigned32     
    }

    dSnmpHostAddrName  OBJECT-TYPE
        SYNTAX          SnmpAdminString (SIZE(1..32))
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
           "This object indicates the address name of the recipients."
        ::= { dSnmpHostEntry 1 }
    
    dSnmpHostVrfName  OBJECT-TYPE
        SYNTAX          DisplayString 
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
           "This object indicates the name of the routing forwarding instance.
           A zero length string indicates the object is not specified.
           "
        ::= { dSnmpHostEntry 2 }
           	
    dSnmpHostUdpPort  OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
           "This object indicates trap UDP port number."
        DEFVAL { 162 }
        ::= { dSnmpHostEntry 3 }        

-- --------------------------------------------------------------------------        
    dSnmpUserTable  OBJECT-TYPE
        SYNTAX          SEQUENCE OF DSnmpUserEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This table consists of a list of recipients for the SNMP
            notification. 
            "       
        ::= { dSnmpAccessCfg 4 }

    dSnmpUserEntry  OBJECT-TYPE
        SYNTAX          DSnmpUserEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "An entry consists of additional informaiton which is not covered
            at usmUserTable (defined in SNMP-USER-BASED-SM-MIB).           
            An entry appears in this table when a user is created in 
            usmUserTable.
            "            
        INDEX     { dSnmpUserName }
        ::= { dSnmpUserTable 1 }

     DSnmpUserEntry ::= SEQUENCE {
        dSnmpUserName               SnmpAdminString,       
        dSnmpUserAccessListName     DisplayString       
    }

    dSnmpUserName  OBJECT-TYPE
        SYNTAX          SnmpAdminString (SIZE(1..32))
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
           "This object indicates the name of the user."
        ::= { dSnmpUserEntry 1 }
   	
    dSnmpUserAccessListName  OBJECT-TYPE
        SYNTAX          DisplayString
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
           "This object indicates the name of a standard IP access control
           list (ACL) to associate with the user.
           A zero length string indicates the access list is not specified.  
           "
        ::= { dSnmpUserEntry 2 }
        
--  ***************************************************************************	
--  Conformance
--  ***************************************************************************			   		
	dSnmpCompliances OBJECT IDENTIFIER ::= { dSnmpMIBConformance 1 }
		
	dSnmpCompliance MODULE-COMPLIANCE
		STATUS current
		DESCRIPTION 
			"The compliance statement for entities which implement the 
			DLINKSW-SNMP-MIB."
		MODULE -- this module
		MANDATORY-GROUPS { 
		    dSnmpSysCfgGroup,
		    dSnmpTrapCfgGroup		   		   
		}
				
		GROUP    dSnmpTargetExtGroup 
        DESCRIPTION
           "This group is conditionally mandatory and must be implemented by
		    the agent only if extensions of community is supported."
		OBJECT      dSnmpHostVrfName
		MIN-ACCESS  read-only
		DESCRIPTION
		    "This object is conditionally mandatory and must be implemented by
		    the agent only if the implementation supports VRF based reachability
            and filtering for SNMP targets."
        
        GROUP    dSnmpTrapIfCfgGroup 
        DESCRIPTION
           "This group is conditionally mandatory and must be implemented by
		    the agent only if interface-specific control of traps is supported."
		::= { dSnmpCompliances 1 }

-- units of conformance		
	dSnmpGroups OBJECT IDENTIFIER ::= { dSnmpMIBConformance 2 }
		
	dSnmpSysCfgGroup OBJECT-GROUP
		OBJECTS { 
		    dSnmpServiceEnabled,
		    dSnmpServiceUdpPort,
		    dSnmpRspBcastRequestEnabled,
		    dSnmpLocalEngineID		    
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides the information for SNMP server."
		::= { dSnmpGroups 1 }
		
	dSnmpTrapCfgGroup OBJECT-GROUP
		OBJECTS { 
		    dSnmpTrapGlobalEnabled,
		    dSnmpTrapGlobalNotifyEnable,
		    dSnmpTrapSourceIfIndex		    
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides system-wide control of traps."
			::= { dSnmpGroups 2 }
	
	dSnmpTrapIfCfgGroup OBJECT-GROUP
		OBJECTS { 		    
		    dSnmpTrapIfSendTrapEnabled
		    
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides interface-specific control of traps."
			::= { dSnmpGroups 3 }
	
	dSnmpCommunityExtGroup OBJECT-GROUP
        OBJECTS         { 
            dSnmpCommunityAccessListName
        }
        STATUS          current
        DESCRIPTION
            "The collection of objects provides configuration for community feature
            which extends SNMP community. 
            "
    ::= { dSnmpGroups 4 }
    			
    dSnmpTargetExtGroup OBJECT-GROUP
        OBJECTS         {                    
            dSnmpHostVrfName,
            dSnmpHostUdpPort     
        }
        STATUS          current
        DESCRIPTION
        
         "The collection of objects provides configuration for recipient of the
        SNMP notification."
        ::= { dSnmpGroups 5 }
    	
	dSnmpVacmExtGroup  OBJECT-GROUP
        OBJECTS        { 
            dSnmpGroupAccessListName,
            dSnmpUserAccessListName 
        }
        STATUS         current
        DESCRIPTION
           "A collection of objects providing for remote configuration of an SNMP
           engine which extends the SNMP VACM (View-based Access Control Model) and 
           USM (User-based Security Model)."
        ::= { dSnmpGroups 6 }
    
	


END

