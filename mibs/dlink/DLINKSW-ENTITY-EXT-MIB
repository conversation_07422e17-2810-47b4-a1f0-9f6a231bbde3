--  *****************************************************************
--  DLINKSW-ENTITY-EXT-MIB.mib : D-Link Entity Extensions MIB
-- 
--  Copyright (c) 2013 D-Link Corporation, all rights reserved.
--   
--  *****************************************************************

DLINKSW-ENTITY-EXT-MIB DEFINITIONS ::= BEGIN

    IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, Unsigned32, Integer32
             FROM SNMPv2-SMI
        DisplayString   			
             FROM SNMPv2-TC
        MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP           
             FROM SNMPv2-CO<PERSON>
        dlinkIndustrialCommon           
             FROM DLINK-ID-REC-MIB;
        
    dlinkSwEntityExtMIB MODULE-IDENTITY     

	    LAST-UPDATED "201309060000Z"
	    ORGANIZATION "D-Link Corp."
        CONTACT-INFO
            "        D-Link Corporation

             Postal: No. 289, Sinhu 3rd Rd., Neihu District,
                     Taipei City 114, Taiwan, R.O.C
             Tel:     +886-2-66000123
             E-mail: <EMAIL>
            "
        DESCRIPTION
            "This MIB module provides essential information about the system 
            and defines the managed objects that support the monitoring of alarms
            generated by physical entities, including temperature sensors, fans
            and power supplies.
            "            
            
        REVISION "201309060000Z"
        DESCRIPTION 
            "Add dEntityExtFactoryResetButton to support factory reset button trap."  
            
        REVISION "201303130000Z"
        DESCRIPTION 
            "This is the first version of this MIB."    
        ::= { dlinkIndustrialCommon 5 }

-- -----------------------------------------------------------------------------
    dEntityExtNotifications    OBJECT IDENTIFIER ::= { dlinkSwEntityExtMIB 0 }
    dEntityExtObjects          OBJECT IDENTIFIER ::= { dlinkSwEntityExtMIB 1 }
    dEntityExtConformance      OBJECT IDENTIFIER ::= { dlinkSwEntityExtMIB 2 }


-- ----------------------------------------------------------------------------- 
    dEntityExtEnvObjects     OBJECT IDENTIFIER ::= { dEntityExtObjects 1 }
    
	dEntityExtEnvTempTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DEntityExtEnvTempEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The table consists of a list of temperature information."
        ::= { dEntityExtEnvObjects 1 }

	dEntityExtEnvTempEntry OBJECT-TYPE
        SYNTAX          DEntityExtEnvTempEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry represents the temperature status of a testpoint.
            "
        INDEX   { 
            dEntityExtEnvTempUnitId,
            dEntityExtEnvTempIndex 
        }
        ::= { dEntityExtEnvTempTable 1 }

    DEntityExtEnvTempEntry ::= SEQUENCE {
		dEntityExtEnvTempUnitId	        Unsigned32,
		dEntityExtEnvTempIndex          Integer32,
        dEntityExtEnvTempDescr          DisplayString,
		dEntityExtEnvTempCurrent 		Integer32,
		dEntityExtEnvTempThresholdLow	Integer32,
		dEntityExtEnvTempThresholdHigh 	Integer32,
		dEntityExtEnvTempStatus 	INTEGER
    }
       
    dEntityExtEnvTempUnitId OBJECT-TYPE
        SYNTAX          Unsigned32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID at which the testpoint is located."
        ::= { dEntityExtEnvTempEntry 1 } 
	              
    dEntityExtEnvTempIndex OBJECT-TYPE
        SYNTAX          Integer32 (1..2147483647)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object is used to uniquely identify the testpoint
            within a unit/slot.
            This index is for SNMP purposes only, and has no intrinsic meaning."
        ::= { dEntityExtEnvTempEntry 2 }

    dEntityExtEnvTempDescr OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object provides human-sensible identification for the testpoint. 
            e.g. 'Inlet', 'Center', 'Outlet'. 
            "
        ::= { dEntityExtEnvTempEntry 3 }
              
	dEntityExtEnvTempCurrent OBJECT-TYPE
        SYNTAX          Integer32
        UNITS           "degrees Celsius"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the current measurement of the testpoint."            
        ::= { dEntityExtEnvTempEntry 4 }

	dEntityExtEnvTempThresholdLow OBJECT-TYPE
        SYNTAX          Integer32
        UNITS           "degrees Celsius"
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION           
            "The object indicates the minimum acceptable reading of the 
            testpoint in degrees Celsius.            
            "
        ::= { dEntityExtEnvTempEntry 5 }

	dEntityExtEnvTempThresholdHigh OBJECT-TYPE
        SYNTAX          Integer32
        UNITS           "degrees Celsius"
        MAX-ACCESS      read-write
        STATUS          current
        DESCRIPTION
            "The object indicates the maximum acceptable reading of the 
            testpoint in degrees Celsius.            
            "
        ::= { dEntityExtEnvTempEntry 6 }
        
  dEntityExtEnvTempStatus OBJECT-TYPE
        SYNTAX          INTEGER {
        			ok(1),
        			abnormal(2)
        }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "The object indicates the current temperature is over the thresholds or not.
            'abnormal' indicates the temperature is over the thresholds.
            'ok' indicates the temperature is not over the thresholds."
        ::= { dEntityExtEnvTempEntry 7 }

-- ----------------------------------------------------------------------------- 
	dEntityExtEnvFanTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DEntityExtEnvFanEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The table is used to display the status of fans."
        ::= { dEntityExtEnvObjects 2 }

	dEntityExtEnvFanEntry OBJECT-TYPE
        SYNTAX          DEntityExtEnvFanEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry represents a fan status.
            "
        INDEX   { 
            dEntityExtEnvFanUnitId, 
            dEntityExtEnvFanIndex
        }
        ::= { dEntityExtEnvFanTable 1 }

    DEntityExtEnvFanEntry ::= SEQUENCE {
        dEntityExtEnvFanUnitId	    Unsigned32,
        dEntityExtEnvFanIndex       Integer32,
        dEntityExtEnvFanDescr       DisplayString,                		
		dEntityExtEnvFanStatus 	    INTEGER
    }

    dEntityExtEnvFanUnitId OBJECT-TYPE
        SYNTAX          Unsigned32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID at which the fan is located."
        ::= { dEntityExtEnvFanEntry 1 }
	
	dEntityExtEnvFanIndex OBJECT-TYPE
        SYNTAX          Integer32 (1..2147483647)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object is used to uniquely identify the fan
            within a unit/slot.
            This index is for SNMP purposes only, and has no intrinsic meaning."
        ::= { dEntityExtEnvFanEntry 2 }

    dEntityExtEnvFanDescr OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object provides human-sensible identification for the fan. 
            e.g. 'Left 1' for the first fan on the left.
            "
        ::= { dEntityExtEnvFanEntry 3 }
        	        
	dEntityExtEnvFanStatus OBJECT-TYPE
        SYNTAX      INTEGER {        
            ok(1),
            fault(2)
    	}
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object indicates the fan status."
        ::= { dEntityExtEnvFanEntry 4 }

-- ----------------------------------------------------------------------------- 
	dEntityExtEnvPowerTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF DEntityExtEnvPowerEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "The table is used to display the status of the power modules."
        ::= { dEntityExtEnvObjects 3 }

	dEntityExtEnvPowerEntry OBJECT-TYPE
        SYNTAX          DEntityExtEnvPowerEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry contains the information of a power module."
        INDEX   { 
            dEntityExtEnvPowerUnitId,
            dEntityExtEnvPowerIndex 
        }
        ::= { dEntityExtEnvPowerTable 1 }

    DEntityExtEnvPowerEntry ::= SEQUENCE {
		dEntityExtEnvPowerUnitId	    Unsigned32,
		dEntityExtEnvPowerIndex         Unsigned32,
		dEntityExtEnvPowerDescr         DisplayString,
		dEntityExtEnvPowerUsedPower     Unsigned32,
		dEntityExtEnvPowerMaxPower      Unsigned32,		
		dEntityExtEnvPowerStatus		INTEGER
    }
 
	dEntityExtEnvPowerUnitId OBJECT-TYPE
        SYNTAX          Unsigned32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID at which the power module is located."
        ::= { dEntityExtEnvPowerEntry 1 }

	dEntityExtEnvPowerIndex OBJECT-TYPE
        SYNTAX          Unsigned32 (1..2147483647)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object indicates the ID of the power module.
            This object is used to uniquely identify the power module. 
            This index is for SNMP purposes only, and has no intrinsic meaning.
            "
        ::= { dEntityExtEnvPowerEntry 2 }
    
    dEntityExtEnvPowerDescr OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object provides human-sensible information for the power
            module. 
            e.g. '#1 (internal)', '#2 (external)'.
            "
        ::= { dEntityExtEnvPowerEntry 3 }
        		    
    dEntityExtEnvPowerUsedPower OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS           "watts"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the amount of the power that has been allocated.         
            "
        ::= { dEntityExtEnvPowerEntry 4 }
    
    dEntityExtEnvPowerMaxPower OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS           "watts"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates maximum power which the power module can supply.           
            "
        ::= { dEntityExtEnvPowerEntry 5 }    
          
    dEntityExtEnvPowerStatus OBJECT-TYPE
        SYNTAX      INTEGER {        
            inOperation(1),
            failed(2),
            empty(3)
    	}
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object indicates the status of the power."
        ::= { dEntityExtEnvPowerEntry 6 }
-- ----------------------------------------------------------------------------- 
 	dEntityExtEnvAirFlowTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF DEntityExtEnvAirFlowEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "The table is used to display the status of the air flow function."
        ::= { dEntityExtEnvObjects 4 }

	dEntityExtEnvAirFlowEntry OBJECT-TYPE
        SYNTAX          DEntityExtEnvAirFlowEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry contains the information of the air flow function."
        INDEX   { 
            dEntityExtEnvAirFlowUnitId
        }
        ::= { dEntityExtEnvAirFlowTable 1 }

    DEntityExtEnvAirFlowEntry ::= SEQUENCE {
		dEntityExtEnvAirFlowUnitId	    Unsigned32,
		dEntityExtEnvAirFlowStatus         INTEGER
    }
    
     
    dEntityExtEnvAirFlowUnitId OBJECT-TYPE
        SYNTAX          Unsigned32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID at which the air flow module is located."
        ::= { dEntityExtEnvAirFlowEntry 1 }
    
    dEntityExtEnvAirFlowStatus OBJECT-TYPE
        SYNTAX      INTEGER
           {   ok(1),
               abnormal(2)
           }
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the status of the air flow.
            
            The fans' air flow direction must match the system's air flow 
            direction to form an integrated air flow. 
            
						For example: In DXS-3600, the normal fans' air flow direction must
            match the power fan's air flow direction.  
						The power fan blow the cold air from outside to inside, all the
            normal fans must blow the hot air from inside to outside. Or the
            power fan blow the hot air from inside to outside, all the normal
            fans must blow the cold air from outside to inside.
						 
            If one or more fans' air flow direction does not match the system's
            air flow direction, it will form an abnormal air flow. Then this log
            message will be triggered.
                   
            ok(1) : A recover event indicates the air flow back to normal.
           
            abnormal(2): An alarm event indicates the air flow detects abnormal.     
            "
        ::= { dEntityExtEnvAirFlowEntry 2 }
  
-- -----------------------------------------------------------------------------
    dEntityExtEnvTrap            OBJECT IDENTIFIER ::= { dEntityExtObjects 2 }
    
    dEntityExtEnvNotifyEnable OBJECT-TYPE
        SYNTAX          BITS {
            fan(0),
            power(1),
            temperature(2)
        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "This object indicates whether a specified notification is enabled
            or not. If a bit corresponding to a notification is set to 1, then 
            the specified notification can be generated.

            fan(0)   - fan trap state for warning fan event (fan failed or fan
                       recover).
            power(1) - power trap state for warning power event (power failure 
                       or power recovery).              
            temperature(2) - temperature trap state for warning temperature event
                      (temperature exceeds the thresholds or temperature recover).
            "
    ::= { dEntityExtEnvTrap 1 }
    

-- -----------------------------------------------------------------------------   
	dEntityExtUnitTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF DEntityExtUnitEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "The table is used to display information of units."
        ::= { dEntityExtObjects 3 }
	
	dEntityExtUnitEntry OBJECT-TYPE
        SYNTAX          DEntityExtUnitEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry contains essential information of a unit."
        INDEX   { dEntityExtUnitIndex }
        ::= { dEntityExtUnitTable 1 }

    DEntityExtUnitEntry ::= SEQUENCE {
		dEntityExtUnitIndex			    Unsigned32,		
		dEntityExtUnitStatus			INTEGER,
		dEntityExtUnitUpTime			Unsigned32
    }
    
	dEntityExtUnitIndex OBJECT-TYPE
        SYNTAX         Unsigned32 (1..65535)
        MAX-ACCESS     not-accessible
        STATUS         current
        DESCRIPTION
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID.
            "
        ::= { dEntityExtUnitEntry 1 }
		         
 	dEntityExtUnitStatus OBJECT-TYPE
        SYNTAX  INTEGER {
            ok(1),
            failed(2),
            empty(3)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This object represents the status of the unit:
            ok(1) - This indicates the unit is okay.
            failed(2) - This indicates the unit fails due to some reason. 
            empty(3) - This indicates that the unit is provisioned but 
                       currently not installed. 
            "
        ::= { dEntityExtUnitEntry 2 } 
     
 	dEntityExtUnitUpTime OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS           "seconds"
        MAX-ACCESS      read-only
        STATUS          current  
        DESCRIPTION
            "This object provides the up time in units of seconds 
            for the unit since it was last re-initialized.              
            "
        ::= { dEntityExtUnitEntry 3 }   
            		
-- -----------------------------------------------------------------------------
	dEntityExtMemoryUtilTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF DEntityExtMemoryUtilEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
             "The table is used to display memory utilization information."
        ::= { dEntityExtObjects 4 }
	
	dEntityExtMemoryUtilEntry OBJECT-TYPE
        SYNTAX          DEntityExtMemoryUtilEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry contains memory utilization information.
            "
        INDEX   { 
            dEntityExtMemUtilUnitId,
            dEntityExtMemUtilType 
        }
        ::= { dEntityExtMemoryUtilTable 1 }

    DEntityExtMemoryUtilEntry ::= SEQUENCE {
		dEntityExtMemUtilUnitId     Integer32,
		dEntityExtMemUtilType		INTEGER,
		dEntityExtMemUtilTotal 		Unsigned32,
		dEntityExtMemUtilUsed 		Unsigned32,
		dEntityExtMemUtilFree 		Unsigned32
    }           
    
	dEntityExtMemUtilUnitId OBJECT-TYPE
        SYNTAX          Integer32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID.            
            "
        ::= { dEntityExtMemoryUtilEntry 1 }
	
	dEntityExtMemUtilType OBJECT-TYPE
        SYNTAX  INTEGER {
            dram(1),
            flash(2),
            nvram(3)
        }
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "The object indicates the memory type of the entry."
        ::= { dEntityExtMemoryUtilEntry 2 }
		
	dEntityExtMemUtilTotal OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS		    "KB"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the total memory size of the entry."  
       ::= { dEntityExtMemoryUtilEntry 3 }
		
	dEntityExtMemUtilUsed OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS		    "KB"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the used memory size of the entry."
        ::= { dEntityExtMemoryUtilEntry 4 }
		
	dEntityExtMemUtilFree OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS		    "KB"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the free memory size of the entry."
        ::= { dEntityExtMemoryUtilEntry 5 }          
        
-- -----------------------------------------------------------------------------
	dEntityExtCpuUtilTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF DEntityExtCpuUtilEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "The table is used to display CPU utilization information."
        ::= { dEntityExtObjects 7 }
	
	dEntityExtCpuUtilEntry OBJECT-TYPE
        SYNTAX          DEntityExtCpuUtilEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry contains CPU information."
        INDEX   { 
            dEntityExtCpuUtilUnitId,
            dEntityExtCpuUtilCpuID
        }
        ::= { dEntityExtCpuUtilTable 1 }

    DEntityExtCpuUtilEntry ::= SEQUENCE {
    	dEntityExtCpuUtilUnitId   		Unsigned32,
		dEntityExtCpuUtilCpuID 		    Unsigned32,
		dEntityExtCpuUtilFiveSeconds 	Unsigned32,
		dEntityExtCpuUtilOneMinute 	    Unsigned32,
		dEntityExtCpuUtilFiveMinutes	Unsigned32
    }        
              
    dEntityExtCpuUtilUnitId OBJECT-TYPE
        SYNTAX          Unsigned32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID.            
            "
        ::= { dEntityExtCpuUtilEntry 1 }
        
	dEntityExtCpuUtilCpuID OBJECT-TYPE
        SYNTAX          Unsigned32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object is used to uniquely identify the CPU within a unit 
            (multiprocessing system)."
        ::= { dEntityExtCpuUtilEntry 2 }
	
	dEntityExtCpuUtilFiveSeconds OBJECT-TYPE
        SYNTAX         Unsigned32
        UNITS          "percentage"
        MAX-ACCESS     read-only
        STATUS         current
        DESCRIPTION
            "This objects indicates the overall CPU busy percentage in the last
            5 second period.
            The value will be between 0% (idle) and 100% (very busy).
            "             
        ::= { dEntityExtCpuUtilEntry 3 }
		
	dEntityExtCpuUtilOneMinute OBJECT-TYPE
        SYNTAX          Unsigned32
        UNITS           "percentage"
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This objects indicates the overall CPU busy percentage in the last
            1 minute period.
            The value will be between 0% (idle) and 100% (very busy).
            "             
        ::= { dEntityExtCpuUtilEntry 4 }
		
	dEntityExtCpuUtilFiveMinutes OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This objects indicates the overall CPU busy percentage in the last
            5 minute period.
            The value will be between 0% (idle) and 100% (very busy).
            "   
        ::= { dEntityExtCpuUtilEntry 5 }
		
-- -----------------------------------------------------------------------------
    dEntityExtVersionTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DEntityExtVersionEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The table is used to display version information of units."
        ::= { dEntityExtObjects 8 }
	
	dEntityExtVersionEntry OBJECT-TYPE
        SYNTAX          DEntityExtVersionEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION            
            "An entry contains version information of a unit."   
       INDEX   { dEntityExtVersionUnitId}
       ::= { dEntityExtVersionTable 1 }

    DEntityExtVersionEntry ::= SEQUENCE {
		dEntityExtVersionUnitId 		Unsigned32,		
		dEntityExtVersionBootloader 	DisplayString,
		dEntityExtVersionRuntime 		DisplayString
    }
    
    dEntityExtVersionUnitId OBJECT-TYPE
        SYNTAX          Unsigned32 (1..65535)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This object indicates the unit (stacking device) or slot (chassis
            device) ID.            
            "
        ::= { dEntityExtVersionEntry 1 }
        							
	dEntityExtVersionBootloader OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the bootloader version of the unit. "
        ::= { dEntityExtVersionEntry 2 }
		
	dEntityExtVersionRuntime OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "This object indicates the firmware version of one unit. "
        ::= { dEntityExtVersionEntry 3 }

-- -----------------------------------------------------------------------------	
--  Notifications
-- -----------------------------------------------------------------------------        
     dEntityExtFanStatusChg NOTIFICATION-TYPE
        OBJECTS { 
            dEntityExtEnvFanUnitId,
            dEntityExtEnvFanIndex,
        		dEntityExtEnvFanStatus				                       
        }
        STATUS  current        		
        DESCRIPTION
            "This trap indicates a fan fails (dEntityExtEnvFanStatus is 'fault') or
            recovers (dEntityExtEnvFanStatus is 'ok'). "            
        ::= { dEntityExtNotifications 1 }	
     
     dEntityExtThermalStatusChg NOTIFICATION-TYPE
        OBJECTS { 
            dEntityExtEnvTempUnitId,
            dEntityExtEnvTempIndex,
        		dEntityExtEnvTempStatus				         
        }
        STATUS  current        		
        DESCRIPTION
            "This trap indicates a thermal alarms (dEntityExtEnvTempStatus
            is 'abnormal') or recovers (dEntityExtEnvTempStatus is 'ok'). "            
        ::= { dEntityExtNotifications 2 }	 
        
     dEntityExtPowerStatusChg NOTIFICATION-TYPE
        OBJECTS { 
            dEntityExtEnvPowerUnitId,
            dEntityExtEnvPowerIndex,
        		dEntityExtEnvPowerStatus				                        
        }
        STATUS  current        		
        DESCRIPTION
            "This trap indicates a power module fails, recovers or is removed.            
            "            
        ::= { dEntityExtNotifications 3 }  
        
     dEntityExtAirFlowChg NOTIFICATION-TYPE
        OBJECTS { 
            dEntityExtEnvAirFlowUnitId,
            dEntityExtEnvAirFlowStatus			                        
        }
        STATUS  current        		
        DESCRIPTION
            "This trap is triggered when the air flow status is changed from 'ok'
            to 'abnormal' and vice versa. dEntityExtEnvAirFlowStatus indicates 
            the current (new) fan air flow state.      
            "            
        ::= { dEntityExtNotifications 4 }  
   
   dEntityExtFactoryResetButton NOTIFICATION-TYPE
        OBJECTS { 
            dEntityExtUnitIndex                        
        }
        STATUS  current        		
        DESCRIPTION
            "This trap is triggered when the factory reset button was pressed.      
            "            
        ::= { dEntityExtNotifications 5 }  
              
-- -----------------------------------------------------------------------------
-- MIB Conformance statements
-- -----------------------------------------------------------------------------
    dEntityExtMIBCompliances  OBJECT IDENTIFIER    ::= { dEntityExtConformance 1 }

    dEntityExtMIBGroups  OBJECT IDENTIFIER         ::= { dEntityExtConformance 2 } 
    
    dEntityExtCompliance MODULE-COMPLIANCE
        STATUS          current
        DESCRIPTION
            "The compliance statement for entities which implement the 
			DLINKSW-ENTITY-EXT-MIB."
			
        MODULE          -- this module
        MANDATORY-GROUPS    { 
            dEntityExtSystemInfoGroup                   
        }
                      
        GROUP dEntityExtTempGroup
        DESCRIPTION
            "This group is mandatory if monitoring environment temperature 
            is supported."                   
        
        OBJECT dEntityExtEnvTempThresholdLow
	    MIN-ACCESS      read-only
	    DESCRIPTION
		    "Write access is not required."       
      
        OBJECT dEntityExtEnvTempThresholdHigh
	    MIN-ACCESS      read-only
	    DESCRIPTION
		    "Write access is not required."  
        
        GROUP dEntityExtThermalNotifGroup                 
        DESCRIPTION
            "This group is mandatory if monitoring environment temperature 
            is supported."  
            
        GROUP dEntityExtFanGroup
        DESCRIPTION
            "This group is mandatory if monitoring fan is supported."                   
       
        GROUP dEntityExtFanNotifGroup
            DESCRIPTION
            "This group is mandatory if monitoring fan is supported."   
            
        GROUP dEntityExtPowerGroup
        DESCRIPTION
            "This group is mandatory if monitoring power is supported."                   
        
        GROUP dEntityExtPowerNotifGroup
            DESCRIPTION
            "This group is mandatory if monitoring power is supported."
                    
        GROUP dEntityExtAirFlowNotifGroup
            DESCRIPTION
            "This group is mandatory if monitoring air flow is supported."

        GROUP dEntityExtGenericNotifInfoGroup               
        DESCRIPTION
            "This group is mandatory if one kind of notification is supported."
        
       
        ::= { dEntityExtMIBCompliances 1 }

-- Units of Conformance
            
     dEntityExtTempGroup OBJECT-GROUP
		OBJECTS { 
		    dEntityExtEnvTempDescr,
		    dEntityExtEnvTempCurrent, 
		    dEntityExtEnvTempThresholdLow,
		    dEntityExtEnvTempThresholdHigh, 
		    dEntityExtEnvTempStatus
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides information of the system temperature."
		::= { dEntityExtMIBGroups 1 }
		
	dEntityExtFanGroup OBJECT-GROUP
		OBJECTS { 
		    dEntityExtEnvFanDescr,
		    dEntityExtEnvFanStatus	
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects providing informations of fans."
		::= { dEntityExtMIBGroups 2 }
		
	dEntityExtPowerGroup OBJECT-GROUP
		OBJECTS { 
		    dEntityExtEnvPowerDescr,
		    dEntityExtEnvPowerUsedPower,
		    dEntityExtEnvPowerMaxPower,
		    dEntityExtEnvPowerStatus
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects providing information of power modules.
            "
		::= { dEntityExtMIBGroups 3 }

    dEntityExtSystemInfoGroup OBJECT-GROUP
		OBJECTS { 		    
            dEntityExtUnitStatus,		
            dEntityExtUnitUpTime,		
            dEntityExtMemUtilTotal,
            dEntityExtMemUtilUsed,
            dEntityExtMemUtilFree,
            dEntityExtCpuUtilFiveSeconds,
            dEntityExtCpuUtilOneMinute,
            dEntityExtCpuUtilFiveMinutes,            
            dEntityExtVersionBootloader,
            dEntityExtVersionRuntime 			    
		}
		STATUS current
		DESCRIPTION 
			"A collection of objects provides information of the system temperature."
		::= { dEntityExtMIBGroups 4 }
	
	dEntityExtGenericNotifInfoGroup OBJECT-GROUP
        OBJECTS { 
            dEntityExtEnvNotifyEnable          
        }
        STATUS  current
        DESCRIPTION
             "A collection of objects provides generic information for binding variables
             of notification.
             "
        ::= { dEntityExtMIBGroups 5 }
        	
	dEntityExtFanNotifGroup NOTIFICATION-GROUP
        NOTIFICATIONS { dEntityExtFanStatusChg }
        STATUS  current
        DESCRIPTION
             "A collection of notifications for indicating the status change of fans."
        ::= { dEntityExtMIBGroups 6 }
        
    dEntityExtThermalNotifGroup NOTIFICATION-GROUP
        NOTIFICATIONS { dEntityExtThermalStatusChg }
        STATUS  current
        DESCRIPTION
             "A collection of notifications for indicating the status change of thermals."
        ::= { dEntityExtMIBGroups 7 }
        
     dEntityExtPowerNotifGroup NOTIFICATION-GROUP
        NOTIFICATIONS { dEntityExtPowerStatusChg }
        STATUS  current
        DESCRIPTION
             "A collection of notifications for indicating the status change of power module."
        ::= { dEntityExtMIBGroups 8 }      
        
     dEntityExtAirFlowNotifGroup NOTIFICATION-GROUP
        NOTIFICATIONS { dEntityExtAirFlowChg }
        STATUS  current
        DESCRIPTION
             "A collection of notifications for indicating the status change of the air flow."
        ::= { dEntityExtMIBGroups 9 }    
        
     dEntityExtFactoryResetButtonNotifGroup NOTIFICATION-GROUP
        NOTIFICATIONS { dEntityExtFactoryResetButton }
        STATUS  current
        DESCRIPTION
             "A collection of notifications for indicating the factory reset button."
        ::= { dEntityExtMIBGroups 10 }         
        
END
