FSC-SERVERCONTROL2-MIB DEFINITIONS ::= BEGIN

--#FILENAME        "SC2.MIB"
--#DESCRIPTION     "ServerControl MIB, edition 2 - for systemboard and server hardware monitoring"
--#REVISION        "9.02.02"
--#VENDOR          "Fujitsu Technology Solutions"
--#TRAP-ENTERPRISE sc2Notifications
--#TRAP-VARIABLES  sc2NotificationsTrapInfo

-- Copyright 2019 FUJITSU LIMITED
-- All rights reserved

IMPORTS
   OBJECT-TYPE                FROM RFC-1212
   mib-2                      FROM RFC1213-MIB
   DisplayString              FROM RFC1213-MIB
   Counter, Gauge, IpAddress  FROM RFC1155-SMI;

   PhysAddress   ::= OCTET STRING

   enterprises       OBJECT IDENTIFIER   ::= { iso org(3) dod(6) internet(1) private(4) 1 }
   sni               OBJECT IDENTIFIER   ::= { enterprises    231 }
   sniProductMibs    OBJECT IDENTIFIER   ::= { sni              2 }
   sniExtensions     OBJECT IDENTIFIER   ::= { sniProductMibs  10 }
   sniServerMgmt     OBJECT IDENTIFIER   ::= { sniExtensions    2 }
   sniInventory      OBJECT IDENTIFIER   ::= { sniServerMgmt    1 }
   sniCommon         OBJECT IDENTIFIER   ::= { sniServerMgmt    2 }

   fscServerControl2 OBJECT IDENTIFIER   ::= { sniCommon 10 }
   sc2Notifications OBJECT IDENTIFIER   ::= { fscServerControl2 20 }

   --
   -- Textual conventions
   --

   TrueFalse ::= INTEGER
   {
      false(1),
      true(2)
   }

   TrueFalseUnknown ::= INTEGER
   {
      unknown(1),
      false(2),
      true(3)
   }

   UnitClass ::= INTEGER
   {
      unknown(1),
      standardServer(2),
      storageExtension(3),
      bladeServerChassis(4),
      bladeServer(5),
      clusterNode(6),
      multiNodeChassis(7),
      multiNodeServer(8),
      virtualServer(9),
      virtualPartition(10),
      systemboardInPartition(11),
      virtualServerVmware(20),      -- obsolete, virtualServer(9) used instead --
      virtualServerHyperV(21),      -- obsolete, virtualServer(9) used instead --
      virtualServerXen(22),         -- obsolete, virtualServer(9) used instead --
      virtualServerPan(23)          -- obsolete, virtualServer(9) used instead --
   }

   CompStatus ::= INTEGER
   {
      ok(1),
      warning(2),
      error(3),
      unknown(5),
      notPresent(6),
      notManageable(7)
   }

   LogSeverity ::= INTEGER
   {
      informational(1),
      minor(2),
      major(3),
      critical(4)
   }

   OsLogSeverity ::= INTEGER
   {
      ok(1),
      warning(2),
      error(3)
   }


-- **********************************************************************************************
--
-- GROUP        sc2AgentInfo
-- DESCRIPTION  "This group defines agent specific objects like agent name,
--               vendor name and versions as well as the agent's write and
--               shutdown permission by SET commands"
--
--      sc2AgentInfo group: *******.*********.*********.1
--
-- **********************************************************************************************

sc2AgentInfo OBJECT IDENTIFIER		::= { fscServerControl2 1 }

sc2AgentId OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Agent identification string"
    ::= { sc2AgentInfo 1 }

sc2AgentCompany OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Agent's company name (e.g. Fujitsu Technology Solutions)"
    ::= { sc2AgentInfo 2 }

sc2AgentVersion OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Agent's version string (e.g. 1.05)"
    ::= { sc2AgentInfo 3 }

sc2AgentBuild OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Compilation time and date of agent program"
    ::= { sc2AgentInfo 4 }

sc2AgentWriteAllowed OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        write-not-allowed(2),
        write-allowed(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Defines whether writable objects can be written
                  (except shutdown/power-off operations)"
    ::= { sc2AgentInfo 5 }

sc2AgentShutdownAllowed OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        shutdown-not-allowed(2),
        shutdown-allowed(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Defines whether shutdown/power-off objects can be written"
    ::= { sc2AgentInfo 6 }


-- **********************************************************************************************
--
-- GROUP        sc2UnitInformation
-- DESCRIPTION  "This group describes the units and their dependencies"
--
--      sc2UnitInformation group: *******.*********.*********.2
--
-- **********************************************************************************************

sc2UnitInformation OBJECT IDENTIFIER		::= { fscServerControl2 2 }

sc2LocalServerUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier number of the local server (usually is ID 1).
                  This ID can be used as index into the sc2Units table."
    ::= { sc2UnitInformation 1 }

sc2NumberUnits OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of available units (size of sc2Units table)."
    ::= { sc2UnitInformation 2 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2UnitTable
-- STATUS       mandatory
-- DESCRIPTION  "Description of all units in this MIB"
--
--      sc2UnitTable: *******.*********.*********.2.3
--
-- ----------------------------------------------------------------------------------------------

sc2UnitTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2Units
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Description of all units in this MIB"
    ::= { sc2UnitInformation 3 }

sc2Units OBJECT-TYPE
    SYNTAX       Sc2Units
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2uUnitId }
    ::= { sc2UnitTable 1 }

Sc2Units ::= SEQUENCE
{
    sc2uUnitId
        INTEGER,
    sc2UnitClass
        UnitClass,
    sc2UnitCabinetNr
        INTEGER,
    sc2UnitDesignation
        DisplayString,
    sc2UnitModelName
        DisplayString,
    sc2UnitManufacturer
        DisplayString,
    sc2UnitSerialNumber
        DisplayString,
    sc2UnitLocation
        DisplayString,
    sc2UnitContact
        DisplayString,
    sc2UnitAdminURL
        DisplayString,
    sc2FrontDoorStatus
        INTEGER,
    sc2HousingOpenStatus
        INTEGER,
    sc2MsgLogLanguages
        DisplayString,
    sc2UnitWorldWideName
        DisplayString,
    sc2RemcsId
        DisplayString,
    sc2AssetTag
        DisplayString,
    sc2MsgLogAvailable
        TrueFalse,
    sc2ManagementIpAddress
        DisplayString,
    sc2HasUefiFirmware
        TrueFalse,
    sc2ManagementIpAddressV6
        DisplayString
}

sc2uUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2Units 1 }

sc2UnitClass OBJECT-TYPE
    SYNTAX       UnitClass
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit classification (type)"
    ::= { sc2Units 2 }

sc2UnitCabinetNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit cabinet number (used for addressing storage extension cabinets
                  or server blades). The ID of the local server is usually 0."
    ::= { sc2Units 3 }

sc2UnitDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit designation / housing type (e.g. 'H200R')"
    ::= { sc2Units 4 }

sc2UnitModelName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit model name (e.g. 'PRIMERGY RX100')"
    ::= { sc2Units 5 }

sc2UnitManufacturer OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit manufacturer name (e.g. 'Fujitsu Technology Solutions')"
    ::= { sc2Units 6 }

sc2UnitSerialNumber OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit serial number (e.g. 'YBCL123456')"
    ::= { sc2Units 7 }

sc2UnitLocation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit location string (e.g. 'ServerCenter Room 17, Rack 9')"
    ::= { sc2Units 8 }

sc2UnitContact OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit contact name (e.g. 'Mr. Bond')"
    ::= { sc2Units 9 }

sc2UnitAdminURL OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "URL of administration HTML interface (e.g. 'http://MyServer/admin')"
    ::= { sc2Units 10 }

sc2FrontDoorStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        open(2),
        closed(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status describing whether the front door is open or closed"
    ::= { sc2Units 11 }

sc2HousingOpenStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        open(2),
        closed(3),
        opened-and-closed(4)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status describing whether the housing is open or closed"
    ::= { sc2Units 12 }

sc2MsgLogLanguages OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Supported languages of text message log (comma-seperated list of
                    decimal language codes, see sc2MessageTextLogTable).
                  e.g. '1031,1033' means German and English are supported.
                  English (1033) is the default language and is always available."
    ::= { sc2Units 13 }

sc2UnitWorldWideName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit World Wide Name. A 64-bit sized address for storage devices (e.g. '6661E2B0A00B88FB')"
    ::= { sc2Units 14 }

sc2RemcsId OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "REMCS system identification string (binary data):
                     Format of Fujitsu REMCS ID (40byte)
                     Format = TTMMMMMMMMMMMMmmmmmmmmmmmmCCnnnnnnnnnnnn
                     TT     :  2byte : Type ID for Company Name
                     MM...M : 12byte : Model Name
                     mm...m : 12byte : Device Name
                     CC     :  2byte : Checksum
                     nn...n : 12byte : Serial number"
    ::= { sc2Units 15 }

sc2AssetTag OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Customer specific asset tag (40 bytes)"
    ::= { sc2Units 16 }

sc2MsgLogAvailable OBJECT-TYPE
    SYNTAX       TrueFalse
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Replies whether a message log is available on this unit"
    ::= { sc2Units 17 }

sc2ManagementIpAddress OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "IPv4 address of the server for management purpose with PRIMEQUEST systems only.
                  This is the interface address for managing a server by external management clients
                  when the server has more than one LAN interfaces (represented by an IPv4 address string)."
    ::= { sc2Units 18 }

sc2HasUefiFirmware OBJECT-TYPE
    SYNTAX       TrueFalse
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Replies whether the system is equipped with UEFI firmware"
    ::= { sc2Units 19 }

sc2ManagementIpAddressV6 OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "IPv6 address of the server for management purpose with PRIMEQUEST systems only.
                  This is the interface address for managing a server by external management clients
                  when the server has more than one LAN interfaces (represented by an IPv6 address string)."
    ::= { sc2Units 20 }


sc2UnitTableUpdateCount OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Counter defining the update revision of the unit table.
                  This counter is incremented with every table contents change/update."
    ::= { sc2UnitInformation 4 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2UnitParentTable
-- STATUS       mandatory
-- DESCRIPTION  "Parent-child relationship of all units: list the parent of all available units.
--               Only units that have a parent are listed in this table."
--
--      sc2UnitParentTable: *******.*********.*********.2.5
--
-- ----------------------------------------------------------------------------------------------

sc2UnitParentTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2UnitParents
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Parent-child relationship of all units: list the parent of all available units.
                  Only units that have a parent are listed in this table."
    ::= { sc2UnitInformation 5 }

sc2UnitParents OBJECT-TYPE
    SYNTAX       Sc2UnitParents
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2pUnitId }
    ::= { sc2UnitParentTable 1 }

Sc2UnitParents ::= SEQUENCE
{
    sc2pUnitId
        INTEGER,
    sc2ParentUnit
        INTEGER,
    sc2ParentUnitClass
        UnitClass
}

sc2pUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2UnitParents 1 }

sc2ParentUnit OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier of this unit's parent"
    ::= { sc2UnitParents 2 }

sc2ParentUnitClass OBJECT-TYPE
    SYNTAX       UnitClass
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Parent unit classification (type). This is the same value as in sc2Units
                  table, but can be used for easy class detection."
    ::= { sc2UnitParents 3 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2UnitChildTable
-- STATUS       mandatory
-- DESCRIPTION  "Parent-child relationship of all units: list all children of all available units.
--               Only units that have children are listed in this table."
--
--      sc2UnitChildTable: *******.*********.*********.2.6
--
-- ----------------------------------------------------------------------------------------------

sc2UnitChildTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2UnitChilds
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Parent-child relationship of all units: list all children of all available units.
                  Only units that have children are listed in this table."
    ::= { sc2UnitInformation 6 }

sc2UnitChilds OBJECT-TYPE
    SYNTAX       Sc2UnitChilds
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2cUnitId, sc2cChildNr }
    ::= { sc2UnitChildTable 1 }

Sc2UnitChilds ::= SEQUENCE
{
    sc2cUnitId
        INTEGER,
    sc2cChildNr
        INTEGER,
    sc2ChildUnit
        INTEGER,
    sc2ChildUnitClass
        UnitClass
}

sc2cUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2UnitChilds 1 }

sc2cChildNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Child number (index, 1..n)"
    ::= { sc2UnitChilds 2 }

sc2ChildUnit OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Child unit"
    ::= { sc2UnitChilds 3 }

sc2ChildUnitClass OBJECT-TYPE
    SYNTAX       UnitClass
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit classification (type). This is the same value as in sc2Units
                  table, but can be used for easy class detection."
    ::= { sc2UnitChilds 4 }



-- **********************************************************************************************
--
-- GROUP        sc2Management
-- DESCRIPTION  "This group contains information needed for system management"
--
--      sc2Management group: *******.*********.*********.3
--
-- **********************************************************************************************

sc2Management OBJECT IDENTIFIER		::= { fscServerControl2 3 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2ManagementNodeTable
-- STATUS       mandatory
-- DESCRIPTION  "SNMP management node addresses (how to address the units)"
--
--      sc2ManagementNodeTable: *******.*********.*********.3.1
--
-- ----------------------------------------------------------------------------------------------

sc2ManagementNodeTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2ManagementNodes
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "SNMP management node addresses (how to address the units)"
    ::= { sc2Management 1 }

sc2ManagementNodes OBJECT-TYPE
    SYNTAX       Sc2ManagementNodes
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2mnUnitId, sc2mnNodeNr }
    ::= { sc2ManagementNodeTable 1 }

Sc2ManagementNodes ::= SEQUENCE
{
    sc2mnUnitId
        INTEGER,
    sc2mnNodeNr
        INTEGER,
    sc2UnitNodeIfType
        INTEGER,
    sc2UnitNodeAddress
        DisplayString,
    sc2UnitNodeIpNetmask
        DisplayString,
    sc2UnitNodeGateway
        DisplayString,
    sc2UnitNodeName
        DisplayString,
    sc2UnitNodeClass
        INTEGER,
    sc2UnitNodeMacAddress
        PhysAddress,
    sc2UnitNodeUseDHCP
        INTEGER,
    sc2UnitNodeControllerType
        DisplayString,
    sc2UnitNodeControllerModel
        DisplayString,
    sc2UnitNodeControllerFWVersion
        DisplayString,
    sc2UnitNodeControllerStorage
        INTEGER,
    sc2UnitNodeSNMPEnabled
        TrueFalseUnknown,
    sc2UnitNodeCIMEnabled
        TrueFalseUnknown,
    sc2UnitNodeRemoteIPMIEnabled
        TrueFalseUnknown
}

sc2mnUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2ManagementNodes 1 }

sc2mnNodeNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node address number (index, 1..n)"
    ::= { sc2ManagementNodes 2 }

sc2UnitNodeIfType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        ip(2),
        ipx(3),
        ip-v6(4)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node interface type"
    ::= { sc2ManagementNodes 3 }

sc2UnitNodeAddress OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node address (e.g. *************)"
    ::= { sc2ManagementNodes 4 }

sc2UnitNodeIpNetmask OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node address (e.g. ***************)"
    ::= { sc2ManagementNodes 5 }

sc2UnitNodeGateway OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node address (e.g. *************)"
    ::= { sc2ManagementNodes 6 }

sc2UnitNodeName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node name (DNS name, e.g. apollo.ts.fujitsu.com)"
    ::= { sc2ManagementNodes 7 }

sc2UnitNodeClass OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        primary(2),
        secondary(3),
        management-blade(4),
        secondary-remote(5),
        secondary-remote-backup(6),
        baseboard-controller(7)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node class:
                    primary:                 local operating system interface
                    secondary:               local management controller LAN interface
                    management-blade:        management blade interface (in a blade server chassis)
                    secondary-remote:        remote management controller (in an RSB concentrator environment)
                                      secondary-remote-backup: backup remote management controller
                                      baseboard-controller:    local baseboard management controller (BMC)"
    ::= { sc2ManagementNodes 8 }

sc2UnitNodeMacAddress OBJECT-TYPE
    SYNTAX       PhysAddress
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node hardware (MAC) address"
    ::= { sc2ManagementNodes 9 }

sc2UnitNodeUseDHCP OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        use-fixed-address(2),
        use-dhcp(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Management node uses DHCP to obtain its address"
    ::= { sc2ManagementNodes 10 }

sc2UnitNodeControllerType OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "The type of the remote management controller (for identification),
                  like 'RSB', 'RSB S2', 'RSB S2 LP'... These names will not be changed
                  if defined once."
    ::= { sc2ManagementNodes 11 }

sc2UnitNodeControllerModel OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "The model name of the remote management controller (for displaying),
                  like 'Remote Service Board', 'RemoteView Service Board S2'...
                  This name should not be used for identification, only for displaying
                  (can be changed in different firmware versions)!"
    ::= { sc2ManagementNodes 12 }

sc2UnitNodeControllerFWVersion OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "The FW version of the remote management controller"
    ::= { sc2ManagementNodes 13 }

sc2UnitNodeControllerStorage OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-available(2),
        available-sdcard(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of the controller specific external storage used for the system management."
    ::= { sc2ManagementNodes 14 }

sc2UnitNodeSNMPEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "SNMP interface is enabled at management node"
    ::= { sc2ManagementNodes 15 }

sc2UnitNodeCIMEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CIM protocol is enabled at management node"
    ::= { sc2ManagementNodes 16 }

sc2UnitNodeRemoteIPMIEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Remote IPMI is enabled at management node"
    ::= { sc2ManagementNodes 17 }


sc2NodeTableUpdateCount OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Counter defining the update revision of the management node table.
                  This counter is incremented with every table contents change/update."
    ::= { sc2Management 2 }

sc2ManagementChannelType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        primary(2),
        secondary(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "The type of the management channel currently used (this MIB instance)."
    ::= { sc2Management 3 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2ManagementProcessorTable
-- STATUS       mandatory
-- DESCRIPTION  "System management service processors"
--
--      sc2ManagementProcessorTable: *******.*********.*********.3.4
--
-- ----------------------------------------------------------------------------------------------

sc2ManagementProcessorTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2ManagementProcessors
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "System management service processors"
    ::= { sc2Management 4 }

sc2ManagementProcessors OBJECT-TYPE
    SYNTAX       Sc2ManagementProcessors
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2spUnitId, sc2spProcessorNr }
    ::= { sc2ManagementProcessorTable 1 }

Sc2ManagementProcessors ::= SEQUENCE
{
    sc2spUnitId
        INTEGER,
    sc2spProcessorNr
        INTEGER,
    sc2spModelName
        DisplayString,
    sc2spFirmwareVersion
        DisplayString,
    sc2spBatteryStatus
        INTEGER,
    sc2spBatteryDischargeTime
        INTEGER,
    sc2spTimeOnBattery
        Counter,
    sc2spDoBatteryChargeCycle
        INTEGER,
    sc2spBatteryChargeLevel
        Gauge
}

sc2spUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2ManagementProcessors 1 }

sc2spProcessorNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Service processor number (index, 1..n)"
    ::= { sc2ManagementProcessors 2 }

sc2spModelName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Service processor model name"
    ::= { sc2ManagementProcessors 3 }

sc2spFirmwareVersion OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Service processor firmware version / revision"
    ::= { sc2ManagementProcessors 4 }

sc2spBatteryStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-present(2),
        ok(3),
        on-battery(4),
        recharging(5),
        failed(6),
        discharging(7)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Service processor backup battery status"
    ::= { sc2ManagementProcessors 5 }

sc2spBatteryDischargeTime OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Maximum time (in seconds) this backup battery can discharged,
                  after that a shutdown will be performed (-1 = unknown)"
    ::= { sc2ManagementProcessors 6 }

sc2spTimeOnBattery OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Time (in seconds) the service processor is running on battery
                  since power fail (will be cleared on power recovery)"
    ::= { sc2ManagementProcessors 7 }

sc2spDoBatteryChargeCycle OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Perform a discharge/charge cycle on the backup battery by writing
                  a non-zero value. Reading this object just returns the value 0."
    ::= { sc2ManagementProcessors 8 }

sc2spBatteryChargeLevel OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Reads the current charge level of the rechargeable battery
                  (in percent of the maximum capacity)."
    ::= { sc2ManagementProcessors 9 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2ManagedUpsNodeTable
-- STATUS       mandatory
-- DESCRIPTION  "UPS nodes managed by the units"
--
--      sc2ManagedUpsNodeTable: *******.*********.*********.3.5
--
-- ----------------------------------------------------------------------------------------------

sc2ManagedUpsNodeTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2ManagedUpsNodes
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "UPS nodes managed by the units"
    ::= { sc2Management 5 }

sc2ManagedUpsNodes OBJECT-TYPE
    SYNTAX       Sc2ManagedUpsNodes
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2upsUnitId, sc2upsNr }
    ::= { sc2ManagedUpsNodeTable 1 }

Sc2ManagedUpsNodes ::= SEQUENCE
{
    sc2upsUnitId
        INTEGER,
    sc2upsNr
        INTEGER,
    sc2upsVendorName
        DisplayString,
    sc2upsModelName
        DisplayString,
    sc2upsMgmtAddress
        DisplayString,
    sc2upsMibRoot
        DisplayString,
    sc2upsSnmpCommunity
        DisplayString
}

sc2upsUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2ManagedUpsNodes 1 }

sc2upsNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "UPS number (index, 1..n)"
    ::= { sc2ManagedUpsNodes 2 }

sc2upsVendorName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "UPS vendor name"
    ::= { sc2ManagedUpsNodes 3 }

sc2upsModelName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "UPS model name"
    ::= { sc2ManagedUpsNodes 4 }

sc2upsMgmtAddress OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "UPS management interface target address (represented as string; may contain an IP V4 address, IP V6 address or a URL)"
    ::= { sc2ManagedUpsNodes 5 }

sc2upsMibRoot OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "UPS MIB root OID; specifies the root OID of the MIB to be used to
                  query UPS via SNMP (empty string if no SNMP agent available)"
    ::= { sc2ManagedUpsNodes 6 }

sc2upsSnmpCommunity OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "UPS SNMP community name (empty string if no SNMP agent available)"
    ::= { sc2ManagedUpsNodes 7 }


sc2UpsNodeTableUpdateCount OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Counter defining the update revision of the UPS node table.
                  This counter is incremented with every table contents change/update."
    ::= { sc2Management 6 }


-- **********************************************************************************************
--
-- GROUP        sc2ServerInformation
-- DESCRIPTION  "This group contains information about server units"
--
--      sc2ServerInformation group: *******.*********.*********.4
--
-- **********************************************************************************************

sc2ServerInformation OBJECT IDENTIFIER		::= { fscServerControl2 4 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2ServerTable
-- STATUS       mandatory
-- DESCRIPTION  "Table containing information about the available servers"
--
--      sc2ServerTable: *******.*********.*********.4.1
--
-- ----------------------------------------------------------------------------------------------

sc2ServerTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2Servers
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table containing information about the available servers"
    ::= { sc2ServerInformation 1 }

sc2Servers OBJECT-TYPE
    SYNTAX       Sc2Servers
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2srvUnitId }
    ::= { sc2ServerTable 1 }

Sc2Servers ::= SEQUENCE
{
    sc2srvUnitId
        INTEGER,
    sc2srvPhysicalMemory
        INTEGER,
    sc2srvLastBootResult
        INTEGER,
    sc2srvCurrentBootStatus
        INTEGER,
    sc2srvShutdownCommand
        INTEGER,
    sc2srvShutdownDelay
        INTEGER,
    sc2srvUUID
        DisplayString,
    sc2srvPhysicalMemoryOs
        INTEGER,
    sc2srvUUIDWireFormat
        DisplayString,
    sc2srvOsPlatform
        INTEGER,
    sc2srvBiosVersion
        DisplayString,
    sc2srvHasEncryptedPartitions
        TrueFalseUnknown,
    sc2srvTrustedExecutionTechnologyEnabled
        TrueFalseUnknown
}

sc2srvUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2Servers 1 }

sc2srvPhysicalMemory OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Size of physical main memory installed (MBytes)"
    ::= { sc2Servers 2 }

sc2srvLastBootResult OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        os-boot-successful(2),
        diagnostic-boot-successful(3),
        no-boot-cpu(4),
        no-bootable-media(5),
        os-failed-to-load(6),
        diagnostic-boot-failed(7),
        hardware-failure(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Result of last system boot"
    ::= { sc2Servers 3 }

sc2srvCurrentBootStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        off(2),
        no-boot-cpu(3),
        self-test(4),
        setup(5),
        os-boot(6),
        diagnostic-boot(7),
        os-running(8),
        diagnostic-running(9),
        os-shutdown(10),
        diagnostic-shutdown(11),
        reset(12)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of the current boot"
    ::= { sc2Servers 4 }

sc2srvShutdownCommand OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        power-off(2),
        power-on(3),
        power-cycle(4),
        shutdown-and-off(5),
        shutdown-and-reset(6),
        shutdown-and-power-cycle(7),
        raise-nmi(8),
        abort-pending-command(9),
        reset(10)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Shutdown the server and switch off or reset; a delay (see object below) 
                  can be defined before the command is being executed;
                  when reading that value, always unknown(1) is replied;
                  argument tells what kind of action:
                      power-on: switch power on
                      power-off: switch power off
                      power-cycle: switch power off and switch power on again.
                      shutdown-and-off: execute a shutdown then power off
                      shutdown-and-reset: execute a shutdown then reset
                      shutdown-and-power-cycle: execute a shutdown then power off and on again
                      raise-nmi: raise an NMI (normally causes debugger to start)
                      reset: execute a reset (without shutdown)
                  The value unknown(1) cannot be set."
    ::= { sc2Servers 5 }

sc2srvShutdownDelay OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Delay time (seconds) the agent waits before executing the shutdown command
                  (see previous command).
                   0 = immediate action, no delay
                  -1 = unknown (delay not supported)
                  The value unknown(-1) cannot be set."
    ::= { sc2Servers 6 }

sc2srvUUID OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Universal Unique ID (UUID) number (as specified in RFC 4122; shown in network byte order, big-endian) -
                  this is a world-wide unique ID number of the server.
                  Can be used for identifying a server. The format is like this: '*************-7766-8899-AABBCCDDEEFF'"
    ::= { sc2Servers 7 }

sc2srvPhysicalMemoryOs OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Size of physical main memory available to the operating system (MBytes).
                  This value is similar to 'sc2srvPhysicalMemory', but may be less if the system
                  has mirror or hot spare memory modules."
    ::= { sc2Servers 8 }

sc2srvUUIDWireFormat OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Universal Unique ID (UUID) number (shown in the wire format; little-endian for the first three fields,
                  as consistently used in the PC industry) - this is a world-wide unique ID number of the server.
                  Can be used for identifying a server. The format is like this: '00112233-**************-AABBCCDDEEFF'"
    ::= { sc2Servers 9 }

sc2srvOsPlatform OBJECT-TYPE
    SYNTAX       INTEGER
    {
        other(1),
        intel-x86(2),
        intel-x64(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Reports about the current operating system platform (x86, x64)"
    ::= { sc2Servers 10 }

sc2srvBiosVersion OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System BIOS version"
    ::= { sc2Servers 11 }

sc2srvHasEncryptedPartitions OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Specifies whether encrypted file systems (e.g. by Windows BitLocker) are used (may prevent from BIOS updates)"
    ::= { sc2Servers 12 }

sc2srvTrustedExecutionTechnologyEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Specifies whether Intel Trusted Execution Technology (TXT) is enabled (may prevent from BIOS updates)"
    ::= { sc2Servers 13 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2UnitPowerOnOffTable
-- STATUS       mandatory
-- DESCRIPTION  "Unit power-on and -off information"
--
--      sc2UnitPowerOnOffTable: *******.*********.*********.4.2
--
-- ----------------------------------------------------------------------------------------------

sc2UnitPowerOnOffTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2UnitPowerOnOff
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Unit power-on and -off information"
    ::= { sc2ServerInformation 2 }

sc2UnitPowerOnOff OBJECT-TYPE
    SYNTAX       Sc2UnitPowerOnOff
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2ooUnitId }
    ::= { sc2UnitPowerOnOffTable 1 }

Sc2UnitPowerOnOff ::= SEQUENCE
{
    sc2ooUnitId
        INTEGER,
    sc2PowerOnOffStatus
        INTEGER,
    sc2LastPowerOffSource
        INTEGER,
    sc2LastPowerOnSource
        INTEGER,
    sc2LastPowerOnTime
        INTEGER,
    sc2PowerOnCounts
        INTEGER,
    sc2PowerOnDuration
        INTEGER,
    sc2PowerOffDuration
        INTEGER,
    sc2PowerFailRecovery
        INTEGER,
    sc2PowerCommand
        INTEGER,
    sc2PowerSupplyRedundancy
        TrueFalseUnknown,
    sc2PowerSupplyMatchStatus
        INTEGER
}

sc2ooUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2UnitPowerOnOff 1 }

sc2PowerOnOffStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        off(2),
        on(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current power on/off status"
    ::= { sc2UnitPowerOnOff 2 }

sc2LastPowerOffSource OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        swoff-command(2),
        power-button(3),
        ac-fail(4),
        clock(5),
        fan-fail(6),
        temperature-critical(7),
        temperature-damage(8),
        power-supply-failure(9),
        watchdog(10),
        remote-off(11),
        hardware-fail(12),
        peripheral-bus-error(13),
        cpu-error(14),
        nmi(20),
        hardware-reset(23),
        warmstart(24),
        reset-button(25),
        ac-fail-reboot(26),
        keyboard(29),
        remote-manager(31),
        remote-manager-reset(32),
        power-cycle(33),
        power-limiting(35),
        mmb-continuous-operation(36),
        watchdog-power-cycle(37),
        viom-inventory-board(38),
        viom-init-boot(39),
        repeated-fan-fail(40),
        repeated-temperature-critical(41),
        firmware-restart(242),
        housing-opened(243)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Reason why the system was switched off last time"
    ::= { sc2UnitPowerOnOff 3 }

sc2LastPowerOnSource OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        swon-command(2),
        power-button(3),
        ac-fail(4),
        clock(5),
        fan-fail(6),
        temperature-critical(7),
        temperature-damage(8),
        power-supply-failure(9),
        watchdog(10),
        remote-on(11),
        hardware-fail(12),
        peripheral-bus-error(13),
        cpu-error(14),
        nmi(20),
        hardware-reset(23),
        warmstart(24),
        reset-button(25),
        ac-fail-reboot(26),
        mgmt-processor-fail(27),
        pci-pme(28),
        keyboard(29),
        chipcard-reader(30),
        remote-manager(31),
        remote-manager-reset(32),
        power-cycle(33),
        viom-inventory-board(38),
        viom-init-boot(39),
        firmware-restart(242),
        housing-closed(243)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Source the system was switched on from"
    ::= { sc2UnitPowerOnOff 4 }

sc2LastPowerOnTime OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Time and date (UTC) the system was booted (seconds since 1/1/1970; 0 = unknown or not available)"
    ::= { sc2UnitPowerOnOff 5 }

sc2PowerOnCounts OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Tells how often the server was switched on in its life (0 = unknown or not available)"
    ::= { sc2UnitPowerOnOff 6 }

sc2PowerOnDuration OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Tells how long the system has stayed ON in its life (in seconds; 0 = unknown or not available)"
    ::= { sc2UnitPowerOnOff 7 }

sc2PowerOffDuration OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Tells how long the system has stayed OFF in its life (in seconds; 0 = unknown or not available)"
    ::= { sc2UnitPowerOnOff 8 }

sc2PowerFailRecovery OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-available(2),
        as-before(3),
        remain-off(4),
        switch-on(5)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Action on AC power recovery after AC has failed.
                      not-available(2) : function not available, behavior is hardware-defined.
                      as-before(3)     : server gets the same power state as before the AC fail.
                      remain-off(4)    : server remains off on power resume.
                      switch-on(5)     : server is always switched on on power resume.
                  The value unknown(1) cannot be set, a SET will deliver 'badValue'."
    ::= { sc2UnitPowerOnOff 9 }

sc2PowerCommand OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        off(2),
        on(3),
        power-cycle(4)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Switch the unit on or off; mainly used for extension cabinets;
                  argument tells what kind of action:
                      on:          switch power on
                      off:         switch power off
                      power-cycle: switch power off and switch power on again
                  No graceful system shutdown is performed - use sc2srvShutdownCommand if
                  shutdown of a server is requested!
                  The value unknown(1) cannot be set and will be delivered if the object is read."
    ::= { sc2UnitPowerOnOff 10 }

sc2PowerSupplyRedundancy OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Specifies whether the power supply system contains redundant power supplies
                  and whether redundancy is available"
    ::= { sc2UnitPowerOnOff 11 }

sc2PowerSupplyMatchStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        ok(2),
        mismatch(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of power supply matching - 'ok' if power supplies do not mismatch"
    ::= { sc2UnitPowerOnOff 12 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2PerformanceTable
-- STATUS       mandatory
-- DESCRIPTION  "Server performance and utilisation information (Obsolete! Please use sc2PerformanceValueTable!)"
--
--      sc2PerformanceTable: *******.*********.*********.4.3
--
-- ----------------------------------------------------------------------------------------------

sc2PerformanceTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2Performance
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Server performance and utilisation information (Obsolete! Please use sc2PerformanceValueTable!)"
    ::= { sc2ServerInformation 3 }

sc2Performance OBJECT-TYPE
    SYNTAX       Sc2Performance
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2perfUnitId, sc2perfNr }
    ::= { sc2PerformanceTable 1 }

Sc2Performance ::= SEQUENCE
{
    sc2perfUnitId
        INTEGER,
    sc2perfNr
        INTEGER,
    sc2PerformanceType
        INTEGER,
    sc2PerformanceObjectNr
        INTEGER,
    sc2PerformanceName
        DisplayString,
    sc2PerformanceValue
        Gauge
}

sc2perfUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2Performance 1 }

sc2perfNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Server performance entry number (index, 1..n)"
    ::= { sc2Performance 2 }

sc2PerformanceType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        cpu(1),
        cpu-overall(2),
        pci-load(3),
        pci-efficiency(4),
        pci-transfer(5),
        memory-physical(6),
        memory-total(7),
        memory-percent(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Server performance object type (description see below)"
    ::= { sc2Performance 3 }

sc2PerformanceObjectNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Server performance object number (0..n; for types with multiple objects)"
    ::= { sc2Performance 4 }

sc2PerformanceName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Name (short description) of the performance value (e.g. 'CPU1').
                  Can be used for displaying a list of performance values in the management frontend."
    ::= { sc2Performance 5 }

sc2PerformanceValue OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Server performance value (value interpretation depending on type; 0xffffffff = unknown):
                     cpu:             Load of a single CPU in percent
                     cpu-overall:     Overall CPU load in percent
                     pci-load:        PCI bus load in percent
                     pci-efficiency:  PCI bus efficiency in percent (100% is optimum)
                     pci-transfer:    PCI bus transfer rate in MBytes/sec.
                     memory-physical: Physical memory usage in MBytes
                     memory-total:    Total memory usage (physical + virtual) in MBytes
                     memory-percent:  Physical memory usage in percent"
    ::= { sc2Performance 6 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2TimerOnOffTable
-- STATUS       mandatory
-- DESCRIPTION  "Power on/off timer table"
--
--      sc2TimerOnOffTable: *******.*********.*********.4.4
--
-- ----------------------------------------------------------------------------------------------

sc2TimerOnOffTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2TimerOnOff
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Power on/off timer table"
    ::= { sc2ServerInformation 4 }

sc2TimerOnOff OBJECT-TYPE
    SYNTAX       Sc2TimerOnOff
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2tooUnitId, sc2tooDayOfWeek }
    ::= { sc2TimerOnOffTable 1 }

Sc2TimerOnOff ::= SEQUENCE
{
    sc2tooUnitId
        INTEGER,
    sc2tooDayOfWeek
        INTEGER,
    sc2OnTime
        INTEGER,
    sc2OffTime
        INTEGER
}

sc2tooUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2TimerOnOff 1 }

sc2tooDayOfWeek OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Day of the week the times should act upon (index); 1=Sunday, 2=Monday..."
    ::= { sc2TimerOnOff 2 }

sc2OnTime OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Time the system should be switched on (by the real-time clock;
                  in minutes since midnight; -1 = disabled, no on time).
                  Only values -1 and 0..1439 are allowed."
    ::= { sc2TimerOnOff 3 }

sc2OffTime OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Time the system should be switched off (by the real-time clock of software;
                  in minutes since midnight; -1 = disabled, no on time).
                  Only values -1 and 0..1439 are allowed."
    ::= { sc2TimerOnOff 4 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2PowerMonitoringTable
-- STATUS       mandatory
-- DESCRIPTION  "Power monitoring table"
--
--      sc2PowerMonitoringTable: *******.*********.*********.4.5
--
-- ----------------------------------------------------------------------------------------------

sc2PowerMonitoringTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2PowerMonitoring
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Power monitoring table"
    ::= { sc2ServerInformation 5 }

sc2PowerMonitoring OBJECT-TYPE
    SYNTAX       Sc2PowerMonitoring
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2pmUnitId }
    ::= { sc2PowerMonitoringTable 1 }

Sc2PowerMonitoring ::= SEQUENCE
{
    sc2pmUnitId
        INTEGER,
    sc2pmCurrentPowerMonitoringAvailable
        TrueFalse,
    sc2pmCurrentPowerMonitoringEnabled
        TrueFalse,
    sc2pmNominalPowerConsumption
        INTEGER,
    sc2pmCurrentPowerConsumption
        Gauge,
    sc2pmCurrentPowerControl
        INTEGER,
    sc2pmPowerLimitStatus
        INTEGER,
    sc2pmPowerLimitThreshold
        INTEGER,
    sc2pmPowerLimitWarning
        INTEGER,
    sc2pmRedundancyCritLevel
        INTEGER,
    sc2pmPowerControlMode
        INTEGER,
    sc2pmPowerDisplayUnit
        INTEGER
}

sc2pmUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2PowerMonitoring 1 }

sc2pmCurrentPowerMonitoringAvailable OBJECT-TYPE
    SYNTAX       TrueFalse
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power monitoring and control available on this server:
                     true:  Power monitoring and power control is available
                     false: Power monitoring and power control is not available"
    ::= { sc2PowerMonitoring 2 }

sc2pmCurrentPowerMonitoringEnabled OBJECT-TYPE
    SYNTAX       TrueFalse
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power monitoring enable value:
                     true:  Power monitoring and data gathering is enabled
                     false: Power monitoring and data gathering is disabled or not available"
    ::= { sc2PowerMonitoring 3 }

sc2pmNominalPowerConsumption OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Nominal (maximum) value of the system's power consumption (in Watts; -1 if value not available)"
    ::= { sc2PowerMonitoring 4 }

sc2pmCurrentPowerConsumption OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current value of the system's power consumption (in Watts; -1 if value not available)"
    ::= { sc2PowerMonitoring 5 }

sc2pmCurrentPowerControl OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        disabled(2),
        best-performance(3),
        minimum-power(4),
        automatic(5),
        limited(7),
        low-noise(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power mode control value:
                     disabled:         The baseboard management controller does not control the power saving behaviour
                     best-performance: The baseboard management controller gives best performance to the system (power consumption may be increased)
                     minimum-power:    The baseboard management controller controls the system in way to consume minimum power (performance may be reduced)
                     automatic:        The baseboard management controller automatically controls power consumption between good performance and low power consumption
                     limited:          The baseboard management controller controls power consumption by power limit
                     low-noise:        The baseboard management controller controls the power to ensure running with lowest noise"
    ::= { sc2PowerMonitoring 6 }

sc2pmPowerLimitStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        ok(2),
        warning(3),
        error(4),
        disabled(5)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power limitation status (valid if power control = limited)
                    ok:       power consumption is within specified range
                    warning:  power consumption is above warning threshold (sc2pmPowerLimitWarning) but below limit
                    error:    power consumption is above limit (sc2pmPowerLimitThreshold)
                    disabled: power consumption limiting is disabled"
    ::= { sc2PowerMonitoring 7 }

sc2pmPowerLimitThreshold OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Maximum limit value of the system's power consumption (in Watts; -1 if value not available; only valid if limiting enabled)"
    ::= { sc2PowerMonitoring 8 }

sc2pmPowerLimitWarning OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Warning limit level (in percent of the maximum limit threshold; 0 if value not available; only valid if limiting enabled)"
    ::= { sc2PowerMonitoring 9 }

sc2pmRedundancyCritLevel OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Upper critical power consumption level (in Watts; -1 if value not available); power supply redundancy lost if level is exceeded"
    ::= { sc2PowerMonitoring 10 }

sc2pmPowerControlMode OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        disabled(2),
        best-performance(3),
        minimum-power(4),
        automatic(5),
        scheduled(6),
        limited(7),
        low-noise(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Configured power control mode value:
                     disabled:         The baseboard management controller does not control the power saving behaviour
                     best-performance: The baseboard management controller gives best performance to the system (power consumption may be increased)
                     minimum-power:    The baseboard management controller controls the system in way to consume minimum power (performance may be reduced)
                     automatic:        The baseboard management controller automatically controls power consumption between good performance and low power consumption
                     scheduled:        The baseboard management controller controls power consumption by schedule
                     limited:          The baseboard management controller controls power consumption by power limit
                     low-noise:        The baseboard management controller controls the power to ensure running with lowest noise"
    ::= { sc2PowerMonitoring 11 }

sc2pmPowerDisplayUnit OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        watt(2),
        btu(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Configurd unit for the display of power consumption values:
                     Watt:         Power consumption values are displayed in Watts
                     Btu/h: Power consumption values are displyed in Btu"
    ::= { sc2PowerMonitoring 12 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2UtilizationHistoryTable
-- STATUS       mandatory
-- DESCRIPTION  "Utilization history table (for power consumption); table only contains existing
--               values (no values for non-existing values)"
--
--      sc2UtilizationHistoryTable: *******.*********.*********.4.6
--
-- ----------------------------------------------------------------------------------------------

sc2UtilizationHistoryTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2UtilizationHistory
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Utilization history table (for power consumption); table only contains existing
                  values (no values for non-existing values)"
    ::= { sc2ServerInformation 6 }

sc2UtilizationHistory OBJECT-TYPE
    SYNTAX       Sc2UtilizationHistory
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2uthUnitId, sc2uthEntity, sc2uthTimeStamp }
    ::= { sc2UtilizationHistoryTable 1 }

Sc2UtilizationHistory ::= SEQUENCE
{
    sc2uthUnitId
        INTEGER,
    sc2uthEntity
        INTEGER,
    sc2uthTimeStamp
        INTEGER,
    sc2uthHardwareUUID
        DisplayString,
    sc2uthAverageValue
        Gauge,
    sc2uthMinValue
        Gauge,
    sc2uthMaxValue
        Gauge
}

sc2uthUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2UtilizationHistory 1 }

sc2uthEntity OBJECT-TYPE
    SYNTAX       INTEGER
    {
        powerConsumptionDay(1),
        powerConsumptionMonth(2),
        powerConsumptionYear(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Utilization value entity; describes what kind of value to be requested:
                    powerConsumptionDay:   power consumption values of the last 24 hours; one value (Watts) per minute
                    powerConsumptionMonth: power consumption values of the last month; one value (Watts) per hour
                    powerConsumptionYear:  power consumption values of the last year; one value (Watts) per day"
    ::= { sc2UtilizationHistory 2 }

sc2uthTimeStamp OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Time stamp of the currenty history entry (time_t = seconds since 1/1/1970, 0:00 AM as GMT)"
    ::= { sc2UtilizationHistory 3 }

sc2uthHardwareUUID OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "UUID of the hardware instance that delivered the value (from object sc2srvUUID);
                  may change if an installation is migrated to another hardware (e.g. in blade environments)"
    ::= { sc2UtilizationHistory 4 }

sc2uthAverageValue OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Average value (in Watts) within the monitored time period (minute, hour, day)"
    ::= { sc2UtilizationHistory 5 }

sc2uthMinValue OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Minimum (lowest) value within the monitored time period"
    ::= { sc2UtilizationHistory 6 }

sc2uthMaxValue OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Maximum (highest) value within the monitored time period"
    ::= { sc2UtilizationHistory 7 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2PowerSourceInformationTable
-- STATUS       mandatory
-- DESCRIPTION  "Power source information table"
--
--      sc2PowerSourceInformationTable: *******.*********.*********.4.7
--
-- ----------------------------------------------------------------------------------------------

sc2PowerSourceInformationTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2PowerSourceInformation
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Power source information table"
    ::= { sc2ServerInformation 7 }

sc2PowerSourceInformation OBJECT-TYPE
    SYNTAX       Sc2PowerSourceInformation
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2psiUnitId }
    ::= { sc2PowerSourceInformationTable 1 }

Sc2PowerSourceInformation ::= SEQUENCE
{
    sc2psiUnitId
        INTEGER,
    sc2psiPowerSourceType
        DisplayString,
    sc2psiPowerSourcePhase
        INTEGER,
    sc2psiPowerSourceVoltage
        INTEGER
}

sc2psiUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2PowerSourceInformation 1 }

sc2psiPowerSourceType OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System input power source type (e.g. 'AC', 'DC')"
    ::= { sc2PowerSourceInformation 2 }

sc2psiPowerSourcePhase OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        single-phase(2),
        three-phase(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System input phase of power source."
    ::= { sc2PowerSourceInformation 3 }

sc2psiPowerSourceVoltage OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System nominal input voltage of power source (in Volts; -1 if value not available)."
    ::= { sc2PowerSourceInformation 4 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2VirtualIoManagerTable
-- STATUS       mandatory
-- DESCRIPTION  "Virtual I/O Manager (VIOM) information table"
--
--      sc2VirtualIoManagerTable: *******.*********.*********.4.8
--
-- ----------------------------------------------------------------------------------------------

sc2VirtualIoManagerTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2VirtualIoManager
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Virtual I/O Manager (VIOM) information table"
    ::= { sc2ServerInformation 8 }

sc2VirtualIoManager OBJECT-TYPE
    SYNTAX       Sc2VirtualIoManager
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2viomUnitId }
    ::= { sc2VirtualIoManagerTable 1 }

Sc2VirtualIoManager ::= SEQUENCE
{
    sc2viomUnitId
        INTEGER,
    sc2viomCurrentManagerId
        DisplayString,
    sc2viomEnabled
        TrueFalseUnknown,
    sc2viomBiosSupport
        TrueFalseUnknown,
    sc2viomConnectionStatus
        INTEGER
}

sc2viomUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2VirtualIoManager 1 }

sc2viomCurrentManagerId OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Identification string of the VIOM manager that currently manages this server"
    ::= { sc2VirtualIoManager 2 }

sc2viomEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "True if the server is enabled for VIOM"
    ::= { sc2VirtualIoManager 3 }

sc2viomBiosSupport OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "True if the BIOS supports VIOM"
    ::= { sc2VirtualIoManager 4 }

sc2viomConnectionStatus OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "The connection status for VIOM - bit field:
                    Bit 0 - waiting for VIOM table
                    Bit 1 - waiting for inventory table
                    Bit 2 - waiting for external power-on event"
    ::= { sc2VirtualIoManager 5 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2PowerSupplyRedundancyConfigurationTable
-- STATUS       mandatory
-- DESCRIPTION  "Power Supply redundancy configuration table"
--
--      sc2PowerSupplyRedundancyConfigurationTable: *******.*********.*********.4.9
--
-- ----------------------------------------------------------------------------------------------

sc2PowerSupplyRedundancyConfigurationTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2PowerSupplyRedundancyConfiguration
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Power Supply redundancy configuration table"
    ::= { sc2ServerInformation 9 }

sc2PowerSupplyRedundancyConfiguration OBJECT-TYPE
    SYNTAX       Sc2PowerSupplyRedundancyConfiguration
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2PSRedUnitId }
    ::= { sc2PowerSupplyRedundancyConfigurationTable 1 }

Sc2PowerSupplyRedundancyConfiguration ::= SEQUENCE
{
    sc2PSRedUnitId
        INTEGER,
    sc2PSRedundancyMode
        INTEGER,
    sc2PSRedundancyModeConfig
        INTEGER,
    sc2PSRedundancyRequiredPowerSupplies
        INTEGER,
    sc2PSRedundancyPopulatedPowerSupplies
        INTEGER,
    sc2PSRedundancyConfigurationStatus
        CompStatus,
    sc2PSRedundancyThresholdStatus
        CompStatus,
    sc2PSRedundancyStatus
        INTEGER
}

sc2PSRedUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2PowerSupplyRedundancyConfiguration 1 }

sc2PSRedundancyMode OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-specified(2),
        no-redundancy(3),
        psu-redundancy(4),
        dual-ac-redundancy(18),
        triple-ac-redundancy(34)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Configured power supply redundancy mode; configured in Server Configuration"
    ::= { sc2PowerSupplyRedundancyConfiguration 2 }

sc2PSRedundancyModeConfig OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        no-redundancy(2),
        redundancy-1-1(19),
        redundancy-2-1(35),
        redundancy-2-2(36),
        redundancy-3-1(51)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Configuration of number required power supplies for the different
                  redundancy modes specified in sc2PSRedundancyMode:
                    no-redundancy(2):   no redundancy required
                    redundancy-1-1(19): 1 required + 1 spare PSU (psu-redundancy mode) or 1 PSU per AC phase (dual-ac-redundancy mode)
                    redundancy-2-1(35): 2 required + 1 spare PSU (psu-redundancy mode)
                    redundancy-3-1(51): 3 required + 1 spare PSU (psu-redundancy mode)
                    redundancy-2-2(36): 2 PSUs per AC phase (dual-ac-redundancy mode)"
    ::= { sc2PowerSupplyRedundancyConfiguration 3 }

sc2PSRedundancyRequiredPowerSupplies OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of power supplies required for configured redundancy mode"
    ::= { sc2PowerSupplyRedundancyConfiguration 4 }

sc2PSRedundancyPopulatedPowerSupplies OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of power supplies populated for configured redundancy mode"
    ::= { sc2PowerSupplyRedundancyConfiguration 5 }

sc2PSRedundancyConfigurationStatus OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of the redundancy configuration; reports the status about whether
                  enough power supplies are installed to operate at the configured redundancy mode"
    ::= { sc2PowerSupplyRedundancyConfiguration 6 }

sc2PSRedundancyThresholdStatus OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of redundancy threshold; reports the status about whether the
                  actual power consumption exceeds the maximum power consumption level for the configured redundancy mode."
    ::= { sc2PowerSupplyRedundancyConfiguration 7 }

sc2PSRedundancyStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-available(2),
        ok(3),
        warning(4),
        error(5)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of power supply redundancy; similar to sc2PowerSupplyRedundancy, but
                  with additional status evaluation according to the power supply redundancy configuration.
                    not-available(2): redundancy not required and not available (no error)
                    ok(3):            redundancy required and available
                    warning(4):       redundancy required but not available
                    error(5):         redundancy required but severe problem detected"
    ::= { sc2PowerSupplyRedundancyConfiguration 8 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2PerformanceValueTable
-- STATUS       mandatory
-- DESCRIPTION  "Server performance and utilisation values"
--
--      sc2PerformanceValueTable: *******.*********.*********.4.10
--
-- ----------------------------------------------------------------------------------------------

sc2PerformanceValueTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2PerformanceValues
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Server performance and utilisation values"
    ::= { sc2ServerInformation 10 }

sc2PerformanceValues OBJECT-TYPE
    SYNTAX       Sc2PerformanceValues
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2PerfValUnitId, sc2PerfValType, sc2PerfValObjectNr }
    ::= { sc2PerformanceValueTable 1 }

Sc2PerformanceValues ::= SEQUENCE
{
    sc2PerfValUnitId
        INTEGER,
    sc2PerfValType
        INTEGER,
    sc2PerfValObjectNr
        INTEGER,
    sc2PerfValName
        DisplayString,
    sc2PerfValCurrentValue
        Gauge
}

sc2PerfValUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n). For performacne values on local system use 1!"
    ::= { sc2PerformanceValues 1 }

sc2PerfValType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        physicalMemoryUsageMb(1),
        physicalMemoryUsagePercent(2),
        totalMemoryUsageMb(3),
        totalMemoryUsagePercent(4),
        overallCpuUsagePercent(5),
        logicalCpuUsagePercent(6)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Server performance object type (description see below)"
    ::= { sc2PerformanceValues 2 }

sc2PerfValObjectNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Server performance object number (1..n; for types with multiple objects)"
    ::= { sc2PerformanceValues 3 }

sc2PerfValName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Name (short description) of the performance value (e.g. 'CPU1').
                  Can be used for displaying a list of performance values in the management frontend."
    ::= { sc2PerformanceValues 4 }

sc2PerfValCurrentValue OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Server performance value (value interpretation depending on type; 0xffffffff = unknown):
                     physicalMemoryUsageMb: Physical memory usage in MBytes
                     physicalMemoryUsagePercent: Physical memory usage in percent
                     totalMemoryUsageMb: Total memory usage (physical + virtual) in MBytes
                     totalMemoryUsagePercent: Total memory usage (physical + virtual) in percent
                     overallCpuUsagePercent: Overall CPU load in percent
                     logicalCpuUsagePercent: Load of a single CPU in percent"
    ::= { sc2PerformanceValues 5 }



-- **********************************************************************************************
--
-- GROUP        sc2Environment
-- DESCRIPTION  "This group contains information about a unit's environmental status
--               (temperature, fans...)"
--
--      sc2Environment group: *******.*********.*********.5
--
-- **********************************************************************************************

sc2Environment OBJECT IDENTIFIER		::= { fscServerControl2 5 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2TemperatureSensorTable
-- STATUS       mandatory
-- DESCRIPTION  "Temperature sensors"
--
--      sc2TemperatureSensorTable: *******.*********.*********.5.1
--
-- ----------------------------------------------------------------------------------------------

sc2TemperatureSensorTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2TemperatureSensors
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Temperature sensors"
    ::= { sc2Environment 1 }

sc2TemperatureSensors OBJECT-TYPE
    SYNTAX       Sc2TemperatureSensors
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2tempUnitId, sc2tempSensorNr }
    ::= { sc2TemperatureSensorTable 1 }

Sc2TemperatureSensors ::= SEQUENCE
{
    sc2tempUnitId
        INTEGER,
    sc2tempSensorNr
        INTEGER,
    sc2tempSensorDesignation
        DisplayString,
    sc2tempSensorIdentifier
        DisplayString,
    sc2tempSensorStatus
        INTEGER,
    sc2tempCurrentTemperature
        Gauge,
    sc2tempWarningLevel
        INTEGER,
    sc2tempCriticalLevel
        INTEGER,
    sc2tempCriticalReaction
        INTEGER
}

sc2tempUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2TemperatureSensors 1 }

sc2tempSensorNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Temperature sensor number (index, 1..n)"
    ::= { sc2TemperatureSensors 2 }

sc2tempSensorDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Temperature sensor designation (e.g. TEMP-PS1)"
    ::= { sc2TemperatureSensors 3 }

sc2tempSensorIdentifier OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Temperature sensor identification string (e.g. TEMP-RSB1)"
    ::= { sc2TemperatureSensors 4 }

sc2tempSensorStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-available(2),
        ok(3),
        sensor-failed(4),
        failed(5),
        temperature-warning-toohot(6),
        temperature-critical-toohot(7),
        temperature-normal(8),
        temperature-warning(9)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of the temperature sensor"
    ::= { sc2TemperatureSensors 5 }

sc2tempCurrentTemperature OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current temperature at this sensor (degrees Celsius; 0 if unknown or sensor failed)"
    ::= { sc2TemperatureSensors 6 }

sc2tempWarningLevel OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Maximum temperature - warning level (degrees Celsius; 0 if unknown or undefined)"
    ::= { sc2TemperatureSensors 7 }

sc2tempCriticalLevel OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Maximum temperature - critical level (degrees Celsius; 0 if unknown or undefined),
                  system shutdown may occur if this level is exceeded"
    ::= { sc2TemperatureSensors 8 }

sc2tempCriticalReaction OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        continue(2),
        shutdown-and-poweroff(3)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Reaction if temperature exceeds the critical level (the value unknown(1) cannot be set)"
    ::= { sc2TemperatureSensors 9 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2FanTable
-- STATUS       mandatory
-- DESCRIPTION  "Cooling devices (fans, liquid cooling pumps)"
--
--      sc2FanTable: *******.*********.*********.5.2
--
-- ----------------------------------------------------------------------------------------------

sc2FanTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2Fans
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Cooling devices (fans, liquid cooling pumps)"
    ::= { sc2Environment 2 }

sc2Fans OBJECT-TYPE
    SYNTAX       Sc2Fans
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2fanUnitId, sc2fanNr }
    ::= { sc2FanTable 1 }

Sc2Fans ::= SEQUENCE
{
    sc2fanUnitId
        INTEGER,
    sc2fanNr
        INTEGER,
    sc2fanDesignation
        DisplayString,
    sc2fanIdentifier
        DisplayString,
    sc2fanStatus
        INTEGER,
    sc2fanCurrentSpeed
        Gauge,
    sc2fanQuality
        Gauge,
    sc2fanFailReaction
        INTEGER,
    sc2fanFailShutdownDelay
        INTEGER,
    sc2fanCoolingDeviceType
        INTEGER
}

sc2fanUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2Fans 1 }

sc2fanNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Cooling device number (index, 1..n)"
    ::= { sc2Fans 2 }

sc2fanDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Cooling device designation (e.g. FAN-PS1)"
    ::= { sc2Fans 3 }

sc2fanIdentifier OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Cooling device identification string (e.g. FAN-1-POWERSUPPLY)"
    ::= { sc2Fans 4 }

sc2fanStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        disabled(2),
        ok(3),
        failed(4),
        prefailure-predicted(5),
        redundant-fan-failed(6),
        not-manageable(7),
        not-present(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of the cooling device"
    ::= { sc2Fans 5 }

sc2fanCurrentSpeed OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current cooling device speed (revolutions per minute; 0 if unknown or failed)"
    ::= { sc2Fans 6 }

sc2fanQuality OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Quality of cooling device (in percent of nominal speed - measured at full speed)"
    ::= { sc2Fans 7 }

sc2fanFailReaction OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        continue(2),
        shutdown-and-poweroff(3)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Reaction if the cooling device fails (the value unknown(1) cannot be set)"
    ::= { sc2Fans 8 }

sc2fanFailShutdownDelay OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Delay time (in seconds) when shutdown should commit on cooling device fail
                  (if shutdown-and-off is configured; 0 = immediately,
                  -1 = unknown, cannot be set)"
    ::= { sc2Fans 9 }

sc2fanCoolingDeviceType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        fan(2),
        liquid(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Type of cooling device (whether fan or liquid cooling pump)
                    fan(2):    fan
                    liquid(3): liquid cooling pump"
    ::= { sc2Fans 10 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2AirflowTable
-- STATUS       mandatory
-- DESCRIPTION  "Exhaust airflow volume table"
--
--      sc2AirflowTable: *******.*********.*********.5.3
--
-- ----------------------------------------------------------------------------------------------

sc2AirflowTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2Airflow
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Exhaust airflow volume table"
    ::= { sc2Environment 3 }

sc2Airflow OBJECT-TYPE
    SYNTAX       Sc2Airflow
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2afUnitId }
    ::= { sc2AirflowTable 1 }

Sc2Airflow ::= SEQUENCE
{
    sc2afUnitId
        INTEGER,
    sc2afExhaustAirflowVolume
        INTEGER,
    sc2afExhaustAirflowVolumeUnit
        DisplayString
}

sc2afUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2Airflow 1 }

sc2afExhaustAirflowVolume OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System exhaust airflow volume ('-1' if value not available)"
    ::= { sc2Airflow 2 }

sc2afExhaustAirflowVolumeUnit OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System exhaust airflow volume unit (e.g. 'cbm/h', 'ccm/min', ...)"
    ::= { sc2Airflow 3 }



-- **********************************************************************************************
--
-- GROUP        sc2Hardware
-- DESCRIPTION  "This group contains information about hardware components (systemboard,
--               power supplies, voltages, ...)"
--
--      sc2Hardware group: *******.*********.*********.6
--
-- **********************************************************************************************

sc2Hardware OBJECT IDENTIFIER		::= { fscServerControl2 6 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2SystemBoardTable
-- STATUS       mandatory
-- DESCRIPTION  "System board (main board) information"
--
--      sc2SystemBoardTable: *******.*********.*********.6.1
--
-- ----------------------------------------------------------------------------------------------

sc2SystemBoardTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2SystemBoard
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "System board (main board) information"
    ::= { sc2Hardware 1 }

sc2SystemBoard OBJECT-TYPE
    SYNTAX       Sc2SystemBoard
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2sbUnitId, sc2sbBoardNr }
    ::= { sc2SystemBoardTable 1 }

Sc2SystemBoard ::= SEQUENCE
{
    sc2sbUnitId
        INTEGER,
    sc2sbBoardNr
        INTEGER,
    sc2SystemBoardModelName
        DisplayString,
    sc2SystemBoardProductNumber
        DisplayString,
    sc2SystemBoardRevision
        DisplayString,
    sc2SystemBoardSerialNumber
        DisplayString,
    sc2SystemBoardDesignation
        DisplayString,
    sc2SystemBoardSDCardSlotPresent
        TrueFalseUnknown
}

sc2sbUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2SystemBoard 1 }

sc2sbBoardNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System board number (index, 1..n)"
    ::= { sc2SystemBoard 2 }

sc2SystemBoardModelName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Model name (e.g. 'D1297')"
    ::= { sc2SystemBoard 3 }

sc2SystemBoardProductNumber OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Product number (may be identical to ModelName)"
    ::= { sc2SystemBoard 4 }

sc2SystemBoardRevision OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Revision string (e.g. 'WGS1 GS3')"
    ::= { sc2SystemBoard 5 }

sc2SystemBoardSerialNumber OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Serial number (not available for all boards)"
    ::= { sc2SystemBoard 6 }

sc2SystemBoardDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Designation (important when multiple system boards are available)"
    ::= { sc2SystemBoard 7 }

sc2SystemBoardSDCardSlotPresent OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "System board contains SD card slot"
    ::= { sc2SystemBoard 8 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2PowerSupplyTable
-- STATUS       mandatory
-- DESCRIPTION  "Power supplies"
--
--      sc2PowerSupplyTable: *******.*********.*********.6.2
--
-- ----------------------------------------------------------------------------------------------

sc2PowerSupplyTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2PowerSupply
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Power supplies"
    ::= { sc2Hardware 2 }

sc2PowerSupply OBJECT-TYPE
    SYNTAX       Sc2PowerSupply
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2psUnitId, sc2psPowerSupplyNr }
    ::= { sc2PowerSupplyTable 1 }

Sc2PowerSupply ::= SEQUENCE
{
    sc2psUnitId
        INTEGER,
    sc2psPowerSupplyNr
        INTEGER,
    sc2PowerSupplyDesignation
        DisplayString,
    sc2PowerSupplyIdentifier
        DisplayString,
    sc2PowerSupplyStatus
        INTEGER,
    sc2psPowerSupplyLoad
        INTEGER,
    sc2psPowerSupplyNominal
        INTEGER
}

sc2psUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2PowerSupply 1 }

sc2psPowerSupplyNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power supply (bay) number (index, 1..n)"
    ::= { sc2PowerSupply 2 }

sc2PowerSupplyDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power supply designation (e.g. PS1)"
    ::= { sc2PowerSupply 3 }

sc2PowerSupplyIdentifier OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power supply identification string (e.g. PS1)"
    ::= { sc2PowerSupply 4 }

sc2PowerSupplyStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-present(2),
        ok(3),
        failed(4),
        ac-fail(5),
        dc-fail(6),
        critical-temperature(7),
        not-manageable(8),
        fan-failure-predicted(9),
        fan-failure(10),
        power-safe-mode(11),
        non-redundant-dc-fail(12),
        non-redundant-ac-fail(13)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power supply status"
    ::= { sc2PowerSupply 5 }

sc2psPowerSupplyLoad OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Actual power supply output load in Watts (0 = not available)"
    ::= { sc2PowerSupply 6 }

sc2psPowerSupplyNominal OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Nominal power supply output load in Watts (0 = not available), the maximum value the power supply can provide"
    ::= { sc2PowerSupply 7 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2VoltageTable
-- STATUS       mandatory
-- DESCRIPTION  "Voltages - all voltage values are in 1/100th Volts;
--               a value of 330 means 3.30 Volts (0xffffffff = unknown)"
--
--      sc2VoltageTable: *******.*********.*********.6.3
--
-- ----------------------------------------------------------------------------------------------

sc2VoltageTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2Voltages
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Voltages - all voltage values are in 1/100th Volts;
                  a value of 330 means 3.30 Volts (0xffffffff = unknown)"
    ::= { sc2Hardware 3 }

sc2Voltages OBJECT-TYPE
    SYNTAX       Sc2Voltages
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2voUnitId, sc2voSensorNr }
    ::= { sc2VoltageTable 1 }

Sc2Voltages ::= SEQUENCE
{
    sc2voUnitId
        INTEGER,
    sc2voSensorNr
        INTEGER,
    sc2VoltageDesignation
        DisplayString,
    sc2VoltageStatus
        INTEGER,
    sc2VoltageCurrentValue
        Gauge,
    sc2VoltageNominalValue
        INTEGER,
    sc2VoltageMinimumLevel
        INTEGER,
    sc2VoltageMaximumLevel
        INTEGER,
    sc2VoltageCurrentLoad
        Gauge
}

sc2voUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2Voltages 1 }

sc2voSensorNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage sensor number (index, 1..n)"
    ::= { sc2Voltages 2 }

sc2VoltageDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage designation string (e.g. 12V, 3V3, ...)"
    ::= { sc2Voltages 3 }

sc2VoltageStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-available(2),
        ok(3),
        too-low(4),
        too-high(5),
        out-of-range(6),
        warning(7)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage status
                    not-available(2): no voltage available (e.g. CPU voltage on empty CPU socket)
                    ok(3):            voltage value is in normal range
                    too-low(4):       voltage value is below nominal range
                    too-high(5):      voltage value is above upper limit
                    out-of-range(6):  voltage value is out of normal range (cannot detect whether too low or too high)
                    warning(7):       voltage warning (e.g. battery failure predicted)"
    ::= { sc2Voltages 4 }

sc2VoltageCurrentValue OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage current value (in mV; 0xFFFFFFFF = unknown; negative voltages
                    are reported as negative values: -1770 (0xFFFFF916) means -1.77 Volts)"
    ::= { sc2Voltages 5 }

sc2VoltageNominalValue OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage nominal value (in mV; -1 = unknown; negative voltages are reported as negative values)"
    ::= { sc2Voltages 6 }

sc2VoltageMinimumLevel OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage minimum level (in mV; -1 = unknown; negative voltages are reported as negative values)"
    ::= { sc2Voltages 7 }

sc2VoltageMaximumLevel OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage maximum level (in mV; -1 = unknown; negative voltages are reported as negative values)"
    ::= { sc2Voltages 8 }

sc2VoltageCurrentLoad OBJECT-TYPE
    SYNTAX       Gauge
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current output load on this voltage line,
                  in percent of the maximum load (0xFFFFFFFF = unknown)"
    ::= { sc2Voltages 9 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2CPUTable
-- STATUS       mandatory
-- DESCRIPTION  "CPU description table"
--
--      sc2CPUTable: *******.*********.*********.6.4
--
-- ----------------------------------------------------------------------------------------------

sc2CPUTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2CPUs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "CPU description table"
    ::= { sc2Hardware 4 }

sc2CPUs OBJECT-TYPE
    SYNTAX       Sc2CPUs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2cpuUnitId, sc2cpuNr }
    ::= { sc2CPUTable 1 }

Sc2CPUs ::= SEQUENCE
{
    sc2cpuUnitId
        INTEGER,
    sc2cpuNr
        INTEGER,
    sc2cpuDesignation
        DisplayString,
    sc2cpuStatus
        INTEGER,
    sc2cpuModelName
        DisplayString,
    sc2cpuManufacturer
        DisplayString,
    sc2cpuStep
        DisplayString,
    sc2cpuCurrentSpeed
        INTEGER,
    sc2cpuNumberLogicals
        INTEGER,
    sc2cpuCacheL1Size
        INTEGER,
    sc2cpuCacheL2Size
        INTEGER,
    sc2cpuCacheL3Size
        INTEGER,
    sc2cpuNumberCores
        INTEGER,
    sc2cpuFamily
        INTEGER,
    sc2cpuEnabledCores
        INTEGER,
    sc2cpuMultithreadingEnabled
        INTEGER,
    sc2cpuConfigurationStatus
        INTEGER,
    sc2cpuMCDRAMSize
        INTEGER,
    sc2cpuMCDRAMSpeed
        INTEGER,
    sc2cpuMCDRAMMode
        INTEGER,
    sc2cpuMCDRAMCacheSize
        INTEGER,
    sc2cpuMCDRAMMemoryModel
        INTEGER
}

sc2cpuUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2CPUs 1 }

sc2cpuNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU (socket) number (index, 1..n)"
    ::= { sc2CPUs 2 }

sc2cpuDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU designation (e.g. CPU-2)"
    ::= { sc2CPUs 3 }

sc2cpuStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-present(2),
        ok(3),
        disabled(4),
        error(5),
        failed(6),
        missing-termination(7),
        prefailure-warning(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU status:
                    unknown(1):              CPU status could not be evaluated
                    not-present(2):          CPU socket is empty
                    ok(3):                   CPU is available and working properly
                    disabled(4):             CPU was manually disabled in BIOS setup
                    error(5):                CPU has encountered errors but is still in use
                    failed(6):               CPU has failed and is disabled
                    missing-termination(7):  CPU socket needs a terminator module when not populated but the terminator is missing
                    prefailure-warning(8):   CPU has encountered too many correctable errors within a certain 
                                               time period and is predicted to fail in near future"
    ::= { sc2CPUs 4 }

sc2cpuModelName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU model name (e.g. Pentium Xeon)"
    ::= { sc2CPUs 5 }

sc2cpuManufacturer OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU manufacturer name (e.g. Intel)"
    ::= { sc2CPUs 6 }

sc2cpuStep OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU step / revision"
    ::= { sc2CPUs 7 }

sc2cpuCurrentSpeed OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU speed (frequency in MHz, -1 if unknown)"
    ::= { sc2CPUs 8 }

sc2cpuNumberLogicals OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of (physically available) logical CPUs within one physical CPU (for hyper-threading and multi-core CPUs).
                  This is the sum of all cores and all logical CPUs of a hyper-threading CPU, -1 if unknown or not available."
    ::= { sc2CPUs 9 }

sc2cpuCacheL1Size OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Size of first level cache (in kBytes, 0 if unknown or not available)"
    ::= { sc2CPUs 10 }

sc2cpuCacheL2Size OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Size of second level cache (in kBytes, 0 if unknown or not available)"
    ::= { sc2CPUs 11 }

sc2cpuCacheL3Size OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Size of third level cache (in kBytes, 0 if unknown or not available)"
    ::= { sc2CPUs 12 }

sc2cpuNumberCores OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of (physically available) cores within one physical CPU (for multi-core CPUs, platform dependent), 0 if unknown or not available"
    ::= { sc2CPUs 13 }

sc2cpuFamily OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU family code. This enumerated value is specified by the DMTF SMBIOS specification (processor information, type 4)."
    ::= { sc2CPUs 14 }

sc2cpuEnabledCores OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of effectively enabled cores within one physical CPU (for multi-core CPUs; 0 if unknown)"
    ::= { sc2CPUs 15 }

sc2cpuMultithreadingEnabled OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        disabled(2),
        enabled(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Multithreading of CPU enabled"
    ::= { sc2CPUs 16 }

sc2cpuConfigurationStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        normal(2),
        disabledManually(3),
        hotSpare(4),
        mirror(5),
        notUsable(7),
        configurationError(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU configuration status:
                    unknown(1):              CPU status could not be evaluated
                    normal(2):               Regular CPU configuration
                    disabledManually(3):     CPU was manually disabled (in BIOS setup)
                    hotSpare(4):             CPU is a hot-spare CPU (for future use)
                    mirror(5):               CPU is mirrored (for future use)
                    notUsable(7):            CPU is currently not usable (due to configuration problems)
                    configurationError(8):   CPU misconfiguration"
    ::= { sc2CPUs 17 }

sc2cpuMCDRAMSize OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Size of the onboard MCDRAM (GByte; 0 if no MCDRAM is available)"
    ::= { sc2CPUs 18 }

sc2cpuMCDRAMSpeed OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Speed of the onboard MCDRAM (MT/s; 0 if no MCDRAM is available)"
    ::= { sc2CPUs 19 }

sc2cpuMCDRAMMode OBJECT-TYPE
    SYNTAX       INTEGER
    {
        undefined(1),
        flat(2),
        cache(3),
        hybrid(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Operation mode of the onboard MCDRAM:
                    undefined(1): Mode is not defined (no MCDRAM available)
                    flat(2):      MCDRAM is used as standalone memory exclusively or in addition to external RAM
                    cache(3):     MCDRAM is used as a cache for external RAM
                    hybrid(3):    Part of MCDRAM is used as a cache, rest as standalone memory (see sc2cpuMCDRAMCacheSize)"
    ::= { sc2CPUs 20 }

sc2cpuMCDRAMCacheSize OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Portion of the onboard MCDRAM used as a cache in hybrid mode (in percent; 0 if no MCDRAM is available or other mode than hybrid)"
    ::= { sc2CPUs 21 }

sc2cpuMCDRAMMemoryModel OBJECT-TYPE
    SYNTAX       INTEGER
    {
        undefined(1),
        all2all(2),
        subNumaCluster2(3),
        subNumaCluster4(4),
        hemisphere(5),
        quadrant(6)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory model of the onboard MCDRAM:
                    undefined(1):       Model is not defined (no MCDRAM available)
                    all2all(2):         All2All
                    subNumaCluster2(3): Sub NUMA Cluster - 2
                    subNumaCluster4(4): Sub NUMA Cluster - 4
                    hemisphere(5):      Hemisphere mode
                    quadrant(6):        Quadrant mode"
    ::= { sc2CPUs 22 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2MemoryModuleTable
-- STATUS       mandatory
-- DESCRIPTION  "Main memory module table"
--
--      sc2MemoryModuleTable: *******.*********.*********.6.5
--
-- ----------------------------------------------------------------------------------------------

sc2MemoryModuleTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2MemoryModules
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Main memory module table"
    ::= { sc2Hardware 5 }

sc2MemoryModules OBJECT-TYPE
    SYNTAX       Sc2MemoryModules
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2memUnitId, sc2memModuleNr }
    ::= { sc2MemoryModuleTable 1 }

Sc2MemoryModules ::= SEQUENCE
{
    sc2memUnitId
        INTEGER,
    sc2memModuleNr
        INTEGER,
    sc2memModuleDesignation
        DisplayString,
    sc2memModuleStatus
        INTEGER,
    sc2memModuleBank
        INTEGER,
    sc2memModuleCapacity
        INTEGER,
    sc2memModuleStartAddress
        INTEGER,
    sc2memModuleForm
        DisplayString,
    sc2memModuleType
        DisplayString,
    sc2memModuleCorrErrors
        Counter,
    sc2memModuleUncorrErrors
        Counter,
    sc2memModuleApproved
        INTEGER,
    sc2memModuleConfiguration
        INTEGER,
    sc2memModuleFrequency
        INTEGER,
    sc2memModuleMaxFrequency
        INTEGER,
    sc2memModuleVoltInterface
        DisplayString
}

sc2memUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2MemoryModules 1 }

sc2memModuleNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module (socket) number (index, 1..n)"
    ::= { sc2MemoryModules 2 }

sc2memModuleDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module designation (e.g. DIMM-2)"
    ::= { sc2MemoryModules 3 }

sc2memModuleStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-present(2),
        ok(3),
        disabled(4),
        error(5),
        failed(6),
        prefailure-predicted(7),
        hot-spare(8),                -- obsolete
        mirror(9),                   -- obsolete
        raid(10),                    -- obsolete
        hidden(11)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module status:
                    unknown(1):              Module status could not be evaluated
                    not-present(2):          Module socket is empty
                    ok(3):                   Module is available and working properly
                    disabled(4):             Module was manually disabled in BIOS setup
                    error(5):                Module has encountered errors but is still in use
                    failed(6):               Module has failed and was disabled
                    prefailure-predicted(7): Module has encountered too many correctable errors within a certain 
                                               time period and is predicted to fail in near future
                    hidden(11):              Module socket is not available and should be hidden"
    ::= { sc2MemoryModules 4 }

sc2memModuleBank OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module bank number (-1 if unknown)
                  Due to the fact that modern mainboards do no longer have fixed banks, this value is OBSOLETE!"
    ::= { sc2MemoryModules 5 }

sc2memModuleCapacity OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module size (capacity in MBytes; -1 if unknown)"
    ::= { sc2MemoryModules 6 }

sc2memModuleStartAddress OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module start address (in MBytes; -1 if unknown)
                  Due to the fact that modern mainboards do no longer show start addresses, this value is OBSOLETE!"
    ::= { sc2MemoryModules 7 }

sc2memModuleForm OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module form (SIMM, DIMM, ...)"
    ::= { sc2MemoryModules 8 }

sc2memModuleType OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module type (SDRAM, DDRAM, DDR2, DDR3, ...)"
    ::= { sc2MemoryModules 9 }

sc2memModuleCorrErrors OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of memory module correctable errors (-1 if unknown)"
    ::= { sc2MemoryModules 10 }

sc2memModuleUncorrErrors OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of memory module uncorrectable errors (-1 if unknown)"
    ::= { sc2MemoryModules 11 }

sc2memModuleApproved OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        no(2),
        yes(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module approved for this machine"
    ::= { sc2MemoryModules 12 }

sc2memModuleConfiguration OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        normal(2),
        disabled(3),
        hotSpare(4),
        mirror(5),
        raid(6),
        notUsable(7),
        configurationError(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module configuration:
                    unknown(1):             Module configuration could not be evaluated
                    normal(2):              Module is in normal non-redundant configuration or slot is empty
                    disabled(3):            Module is disabled (manually through BIOS setup)
                    hotSpare(4):            Module is a hot-spare (standby) module, which will be used
                                              as a run-time-replacement for failed modules
                    mirror(5):              Module is part of a mirror configuration
                    raid(6):                Module is part of a RAID configuration
                    notUsable(7):           Module is not usable in the actual configuration because of architectural restrictions
                    configurationError(8):  Module is not active due to memory misconfiguration
                  This object replaces the previous redundancy status values hot-spare and mirror in sc2memModuleStatus."
    ::= { sc2MemoryModules 13 }

sc2memModuleFrequency OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module actual front side bus frequency (in MHz; -1 if unknown). This value is based on the BIOS settings"
    ::= { sc2MemoryModules 14 }

sc2memModuleMaxFrequency OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module maximum front side bus frequency (in MHz; -1 if unknown). This value is based on memory module SPD data"
    ::= { sc2MemoryModules 15 }

sc2memModuleVoltInterface OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module interface voltage interface description"
    ::= { sc2MemoryModules 16 }


sc2cpuMultithreadEnable OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        disabled(2),
        enabled(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Multithreading of CPU enabled
                  This object is OBSOLETE for future implementations; use object 'sc2cpuMultithreadingEnabled' from 'sc2CPUTable' instead!"
    ::= { sc2Hardware 6 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2ComponentPowerConsumptionTable
-- STATUS       mandatory
-- DESCRIPTION  "Table of component power consumption data"
--
--      sc2ComponentPowerConsumptionTable: *******.*********.*********.6.7
--
-- ----------------------------------------------------------------------------------------------

sc2ComponentPowerConsumptionTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2ComponentPowerConsumption
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table of component power consumption data"
    ::= { sc2Hardware 7 }

sc2ComponentPowerConsumption OBJECT-TYPE
    SYNTAX       Sc2ComponentPowerConsumption
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2cpcUnitId, sc2cpcComponentClass, sc2cpcComponentIndex }
    ::= { sc2ComponentPowerConsumptionTable 1 }

Sc2ComponentPowerConsumption ::= SEQUENCE
{
    sc2cpcUnitId
        INTEGER,
    sc2cpcComponentClass
        INTEGER,
    sc2cpcComponentIndex
        INTEGER,
    sc2cpcDesignation
        DisplayString,
    sc2cpcCurrentValue
        INTEGER
}

sc2cpcUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2ComponentPowerConsumption 1 }

sc2cpcComponentClass OBJECT-TYPE
    SYNTAX       INTEGER
    {
        processor(3),
        disk(4),
        system-board(7),
        memory-unit(8),
        processor-module(9),
        power-supply(10),
        gpu(11),
        chassis(23),
        sub-chassis(24),
        disk-bay(26),
        cooling-device(29),
        cooling-unit(30),
        memory-device(32),
        total-power(224)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component class enumeration (based on IPMI v1.0 entity ID)"
    ::= { sc2ComponentPowerConsumption 2 }

sc2cpcComponentIndex OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component index within class (1..n)"
    ::= { sc2ComponentPowerConsumption 3 }

sc2cpcDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component designation"
    ::= { sc2ComponentPowerConsumption 4 }

sc2cpcCurrentValue OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current component power consumption in Watts"
    ::= { sc2ComponentPowerConsumption 5 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2TrustedPlatformModuleTable
-- STATUS       mandatory
-- DESCRIPTION  "Trusted Platform Module (TPM) status information (see corresponding TPM specification for detailed description of these states)"
--
--      sc2TrustedPlatformModuleTable: *******.*********.*********.6.8
--
-- ----------------------------------------------------------------------------------------------

sc2TrustedPlatformModuleTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2TrustedPlatformModule
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Trusted Platform Module (TPM) status information (see corresponding TPM specification for detailed description of these states)"
    ::= { sc2Hardware 8 }

sc2TrustedPlatformModule OBJECT-TYPE
    SYNTAX       Sc2TrustedPlatformModule
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2tpmUnitId }
    ::= { sc2TrustedPlatformModuleTable 1 }

Sc2TrustedPlatformModule ::= SEQUENCE
{
    sc2tpmUnitId
        INTEGER,
    sc2tpmHardwareAvailable
        TrueFalseUnknown,
    sc2tpmBiosEnabled
        TrueFalseUnknown,
    sc2tpmEnabled
        TrueFalseUnknown,
    sc2tpmActivated
        TrueFalseUnknown,
    sc2tpmOwnership
        TrueFalseUnknown
}

sc2tpmUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2TrustedPlatformModule 1 }

sc2tpmHardwareAvailable OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "TPM hardware is available ('unknown' if hardware availability cannot be detected; no interface found)"
    ::= { sc2TrustedPlatformModule 2 }

sc2tpmBiosEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "TPM hardware is enabled by BIOS ('unknown' if hardware is not available; a reboot is required to change this value)"
    ::= { sc2TrustedPlatformModule 3 }

sc2tpmEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "TPM is enabled (by software; cannot take ownership if disabled; 'unknown' if hardware is not available or disabled)"
    ::= { sc2TrustedPlatformModule 4 }

sc2tpmActivated OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "TPM is activated (by software; 'unknown' if hardware is not available or disabled)"
    ::= { sc2TrustedPlatformModule 5 }

sc2tpmOwnership OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "TPM ownership is established (valid endorsement key is existing; 'unknown' if hardware is not available or disabled)"
    ::= { sc2TrustedPlatformModule 6 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2PersistentMemoryModuleTable
-- STATUS       mandatory
-- DESCRIPTION  "Additional information about persistent (non-volatile) memory modules to extend the sc2MemoryModuleTable.
--               This table uses the index numbers of the previous sc2MemoryModuleTable, but only contains rows where a persistent DIMM module is detected."
--
--      sc2PersistentMemoryModuleTable: *******.*********.*********.6.9
--
-- ----------------------------------------------------------------------------------------------

sc2PersistentMemoryModuleTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2PersistentMemoryModules
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Additional information about persistent (non-volatile) memory modules to extend the sc2MemoryModuleTable.
                  This table uses the index numbers of the previous sc2MemoryModuleTable, but only contains rows where a persistent DIMM module is detected."
    ::= { sc2Hardware 9 }

sc2PersistentMemoryModules OBJECT-TYPE
    SYNTAX       Sc2PersistentMemoryModules
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2memUnitId, sc2memModuleNr }
    ::= { sc2PersistentMemoryModuleTable 1 }

Sc2PersistentMemoryModules ::= SEQUENCE
{
    sc2nvmemMemoryModeEnabled
        TrueFalseUnknown,
    sc2nvmemPersistentModeEnabled
        TrueFalseUnknown,
    sc2nvmemPackageSparingCapable
        TrueFalseUnknown,
    sc2nvmemEncryptionEnabled
        TrueFalseUnknown,
    sc2nvmemFirmwareRevision
        DisplayString,
    sc2nvmemTotalSize
        INTEGER,
    sc2nvmemRawCapacity
        INTEGER,
    sc2nvmemVolatileCapacity
        INTEGER,
    sc2nvmemPersistentCapacity
        INTEGER,
    sc2nvmemVolatilePercent
        INTEGER,
    sc2nvmemPersistentPercent
        INTEGER,
    sc2nvmemVolatileStart
        DisplayString,
    sc2nvmemPersistentStart
        DisplayString,
    sc2nvmemHealthStatusNonCritical
        TrueFalseUnknown,
    sc2nvmemHealthStatusCritical
        TrueFalseUnknown,
    sc2nvmemHealthStatusFatal
        TrueFalseUnknown,
    sc2nvmemPowerOnTime
        Counter,
    sc2nvmemUpTime
        Counter,
    sc2nvmemLiftetimeRemaining
        INTEGER,
    sc2nvmemAveragePowerBudget
        INTEGER,
    sc2nvmemPeakPowerBudget
        INTEGER
}

sc2nvmemMemoryModeEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Volatime memory mode is enabled"
    ::= { sc2PersistentMemoryModules 1 }

sc2nvmemPersistentModeEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Persistent memory mode is enabled"
    ::= { sc2PersistentMemoryModules 2 }

sc2nvmemPackageSparingCapable OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Module is capable of providing spare memory"
    ::= { sc2PersistentMemoryModules 3 }

sc2nvmemEncryptionEnabled OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory encryption is enabled"
    ::= { sc2PersistentMemoryModules 4 }

sc2nvmemFirmwareRevision OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Firmware revision"
    ::= { sc2PersistentMemoryModules 5 }

sc2nvmemTotalSize OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Total size of memory module (in GBytes; including reserved spare space)"
    ::= { sc2PersistentMemoryModules 6 }

sc2nvmemRawCapacity OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Maximum capacity that can be allocated for use in any of the supported modes (in GBytes; excluding reserved space)"
    ::= { sc2PersistentMemoryModules 7 }

sc2nvmemVolatileCapacity OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Capacity of volatile memory (in GBytes)"
    ::= { sc2PersistentMemoryModules 8 }

sc2nvmemPersistentCapacity OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Capacity of persistent memory (in GBytes)"
    ::= { sc2PersistentMemoryModules 9 }

sc2nvmemVolatilePercent OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Capacity of volatile memory (in percent of total size)"
    ::= { sc2PersistentMemoryModules 10 }

sc2nvmemPersistentPercent OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Capacity of persistent memory (in percent of total size)"
    ::= { sc2PersistentMemoryModules 11 }

sc2nvmemVolatileStart OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Start address of the volatile memory region"
    ::= { sc2PersistentMemoryModules 12 }

sc2nvmemPersistentStart OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Start address of the persistent memory region"
    ::= { sc2PersistentMemoryModules 13 }

sc2nvmemHealthStatusNonCritical OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Non-critical (warning) status"
    ::= { sc2PersistentMemoryModules 14 }

sc2nvmemHealthStatusCritical OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Critical status"
    ::= { sc2PersistentMemoryModules 15 }

sc2nvmemHealthStatusFatal OBJECT-TYPE
    SYNTAX       TrueFalseUnknown
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Fatal error status (data loss has occurred or is imminent)"
    ::= { sc2PersistentMemoryModules 16 }

sc2nvmemPowerOnTime OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Cumulated duration the DIMM has been powered on (in seconds; 0 = unknown)"
    ::= { sc2PersistentMemoryModules 17 }

sc2nvmemUpTime OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current uptime of the DIMM for the current power cycle (in seconds; 0 = unknown)"
    ::= { sc2PersistentMemoryModules 18 }

sc2nvmemLiftetimeRemaining OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Predicted remaining lifetime (in percent; -1 = unknown)"
    ::= { sc2PersistentMemoryModules 19 }

sc2nvmemAveragePowerBudget OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Average Power Budget in mW"
    ::= { sc2PersistentMemoryModules 20 }

sc2nvmemPeakPowerBudget OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Peak Power Budget in mW"
    ::= { sc2PersistentMemoryModules 21 }



-- **********************************************************************************************
--
-- GROUP        sc2Recovery
-- DESCRIPTION  "This group contains information about recovery (message log, watchdogs,
--               recovery settings...)"
--
--      sc2Recovery group: *******.*********.*********.7
--
-- **********************************************************************************************

sc2Recovery OBJECT IDENTIFIER		::= { fscServerControl2 7 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2MessageLogTable
-- STATUS       mandatory
-- DESCRIPTION  "Error and event message handling"
--
--      sc2MessageLogTable: *******.*********.*********.7.1
--
-- ----------------------------------------------------------------------------------------------

sc2MessageLogTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2MessageLogs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Error and event message handling"
    ::= { sc2Recovery 1 }

--
-- Message data of the following message log entries. Valid for the MIB table
-- "sc2MessageLogs", object "sc2msgLogEntryData". The messages are encoded as binary
-- data with following format:
--
--  --------------------------------------------------------------------------------
-- |   MessageTime    | ErrorCode  | CabinetNumber | ObjectId | AddParams (max. 15) |
-- |     (DWORD)      |  (WORD)    |    (DWORD)    |  (DWORD) |     (DWORDs)        |
--  --------------------------------------------------------------------------------
--
-- MessageTime:   Time and date (GMT) when this message occured (seconds since 1/1/1970, 0:00 AM);
--                0x00000000 if time is unknown.
-- ErrorCode:     Error message number, upper 2 bits are severity:
--                   00 = Informational, 01 = minor, 10 = major, 11 = critical
--                   LSByte: ErrorDetail, MSByte: ErrorClass and Severity.
-- CabinetNumber: Number of the cabinet (server, storage extension) where this message comes from.
-- ObjectId:      Object number that created the message (e.g. Fan 2 = object ID 2).
-- AddParams:     Additional parameters specifying additional message details (depending on the
--                error code; the number of arguments (0..15) is defined by the message size)
--
-- The log messages can be decoded by using the message file ERRMSG.INI.
-- Each MIB table entry can contain only one message entry!
--
-- Special handling for ErrorCode 3F00:
-- Messages with this error code are are not encoded, but all bytes of the
-- arguments (CabinetNumber, ObjectId and all AddParams) have to be interpreted
-- as clear text (DisplayString):
--
--  ----------------------------------------------------------
-- |   MessageTime    | ErrorCode  | Clear text message text  |
-- |     (DWORD)      |  (3F00)    |     (DisplayString)      |
--  ----------------------------------------------------------
--
-- The severity is inserted into the error code as for "normal" error codes.
--


sc2MessageLogs OBJECT-TYPE
    SYNTAX       Sc2MessageLogs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2msgUnitId, sc2msgLogEntryNr }
    ::= { sc2MessageLogTable 1 }

Sc2MessageLogs ::= SEQUENCE
{
    sc2msgUnitId
        INTEGER,
    sc2msgLogEntryNr
        INTEGER,
    sc2msgLogEntryData
        OCTET STRING
}

sc2msgUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2MessageLogs 1 }

sc2msgLogEntryNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry number (index, 1..n)"
    ::= { sc2MessageLogs 2 }

sc2msgLogEntryData OBJECT-TYPE
    SYNTAX       OCTET STRING
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry data (binary; one message per table entry)"
    ::= { sc2MessageLogs 3 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2WatchdogTable
-- STATUS       mandatory
-- DESCRIPTION  "Watchdogs"
--
--      sc2WatchdogTable: *******.*********.*********.7.2
--
-- ----------------------------------------------------------------------------------------------

sc2WatchdogTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2Watchdogs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Watchdogs"
    ::= { sc2Recovery 2 }

sc2Watchdogs OBJECT-TYPE
    SYNTAX       Sc2Watchdogs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2wdUnitId, sc2WatchdogType }
    ::= { sc2WatchdogTable 1 }

Sc2Watchdogs ::= SEQUENCE
{
    sc2wdUnitId
        INTEGER,
    sc2WatchdogType
        INTEGER,
    sc2WatchdogStatus
        INTEGER,
    sc2WatchdogTime
        INTEGER,
    sc2WatchdogAction
        INTEGER
}

sc2wdUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2Watchdogs 1 }

sc2WatchdogType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        other(1),
        hardware(2),
        software(3),
        bios(4),
        boot(5),
        management-controller(6),
        remote-management-controller(7),
        cpu(8)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Watchdog type"
    ::= { sc2Watchdogs 2 }

sc2WatchdogStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        disabled(2),
        enabled(3),
        not-available(4)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Watchdog status"
    ::= { sc2Watchdogs 3 }

sc2WatchdogTime OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Watchdog timeout period in seconds (-1 = unknown)"
    ::= { sc2Watchdogs 4 }

sc2WatchdogAction OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        continue(2),
        reset(4),
        nmi(5),
        power-cycle(6)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Watchdog timeout action; the value unknown(1) can't be written.
                  Not all watchdog type allow setting all of the values (-> BADVALUE)."
    ::= { sc2Watchdogs 5 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2RecoverySettingTable
-- STATUS       mandatory
-- DESCRIPTION  "ASR&R (Automatic server recovery and restart) settings"
--
--      sc2RecoverySettingTable: *******.*********.*********.7.3
--
-- ----------------------------------------------------------------------------------------------

sc2RecoverySettingTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2RecoverySettings
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "ASR&R (Automatic server recovery and restart) settings"
    ::= { sc2Recovery 3 }

sc2RecoverySettings OBJECT-TYPE
    SYNTAX       Sc2RecoverySettings
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2asrUnitId }
    ::= { sc2RecoverySettingTable 1 }

Sc2RecoverySettings ::= SEQUENCE
{
    sc2asrUnitId
        INTEGER,
    sc2asrNrRebootRetries
        INTEGER,
    sc2asrDefaultRebootRetries
        INTEGER,
    sc2asrNextBootSource
        INTEGER,
    sc2asrRebootFailAction
        INTEGER,
    sc2asrRestartDelay
        INTEGER,
    sc2asrPostErrorHalt
        INTEGER
}

sc2asrUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2RecoverySettings 1 }

sc2asrNrRebootRetries OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Current number of retries to reboot/restart the server when a failure shutdown
                  (and power-off) had occured. After this number of retries the action
                  defined in 'sc2asrRebootFailAction' will be executed
                  (-1 = unknown, no retries)."
    ::= { sc2RecoverySettings 2 }

sc2asrDefaultRebootRetries OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Default number of retries for server failure reboot. The above
                  object 'sc2asrNrRebootRetries' will be set to this value after the server
                  is working properly for a certain amount of time."
    ::= { sc2RecoverySettings 3 }

sc2asrNextBootSource OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        operating-system(2),
        reserved(3),
        diag-system(4),
        remote-disk(5)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Source medium to boot from (next time):
                     operating-system: boot from floppy or hard disk
                     diag-system:      boot from alternate diagnostic system (only for SCSI systems,
                                       load diagnostic system from alternate IDE disk).
                     remote-disk:      boot from remote network disk"
    ::= { sc2RecoverySettings 4 }

sc2asrRebootFailAction OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        switch-off(2),
        boot-diag-system(3),
        no-diag-system(4),
        remote-image-disk(5),
        pxe(6),
        rsb-usb(7),
        remote-storage(8),
        stop-reboot(9),
        diag-interrupt-assert(10)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Specifies the action after a specified number of failed reboot retries 
                  (the object 'sc2asrNrRebootRetries' has reached zero). The value
                  no-diag-system(4) means that a diagnostics system is not available."
    ::= { sc2RecoverySettings 5 }

sc2asrRestartDelay OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Delay (in minutes) after that the system should be automatically switched on
                  again (by real-time clock) after an error power-off.
                  Setting this value to zero means immediate restart (power-cycle only).
                  Automatic restart may be disabled by setting the 'sc2asrRebootFailAction'
                  to 'switch-off' and the 'sc2asrDefaultRebootRetries' to zero."
    ::= { sc2RecoverySettings 6 }

sc2asrPostErrorHalt OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        halt-on-any-error(2),
        no-halt-on-any-error(3),
        other(9)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Describes what to do when a POST error has occured (halt on any error, no halt on any error...)."
    ::= { sc2RecoverySettings 7 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2MessageTextLogTable
-- STATUS       mandatory
-- DESCRIPTION  "Error and event message handling, reported as text strings"
--
--      sc2MessageTextLogTable: *******.*********.*********.7.4
--
-- ----------------------------------------------------------------------------------------------

sc2MessageTextLogTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2MessageTextLogs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Error and event message handling, reported as text strings"
    ::= { sc2Recovery 4 }

--
-- The message log entries in this table are reported as clear text strings.
-- The sequence number is a unique entry number (unique in a unit) and can
--   be used to avoid duplicate entries. It may start with any number (not
--   necessarily 0).
-- The oldest entries have the lowest sequence number.
-- The log can be queried in different languages, the language code defines what
--   language to be reported. See object "sc2MsgLogLanguages" in "sc2UnitTable"
--   for the available languages.
-- Each MIB table entry can contain only one message entry!
-- The table entries may not correspond to the entries in the preceding "sc2MessageLogs" table!
--

sc2MessageTextLogs OBJECT-TYPE
    SYNTAX       Sc2MessageTextLogs
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2msgTextLogUnitId, sc2msgTextLogLanguage, sc2msgTextLogSeqNr }
    ::= { sc2MessageTextLogTable 1 }

Sc2MessageTextLogs ::= SEQUENCE
{
    sc2msgTextLogUnitId
        INTEGER,
    sc2msgTextLogLanguage
        INTEGER,
    sc2msgTextLogSeqNr
        INTEGER,
    sc2msgTextLogTimestamp
        INTEGER,
    sc2msgTextLogMessage
        DisplayString,
    sc2msgTextLogErrorCode
        INTEGER,
    sc2msgTextLogSeverity
        INTEGER,
    sc2msgTextLogCSSComponent
        TrueFalse
}

sc2msgTextLogUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2MessageTextLogs 1 }

sc2msgTextLogLanguage OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry language (index, Windows language code)
                  e.g. 1031, 1033 for German, English (default; always available)"
    ::= { sc2MessageTextLogs 2 }

sc2msgTextLogSeqNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry sequence number (index, 1..n)"
    ::= { sc2MessageTextLogs 3 }

sc2msgTextLogTimestamp OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry time stamp (time_t = seconds since 1/1/1970, 0:00 AM as GMT)"
    ::= { sc2MessageTextLogs 4 }

sc2msgTextLogMessage OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry message string (multi-byte string; multi-language; not
                  every language may be implemented)"
    ::= { sc2MessageTextLogs 5 }

sc2msgTextLogErrorCode OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry error code"
    ::= { sc2MessageTextLogs 6 }

sc2msgTextLogSeverity OBJECT-TYPE
    SYNTAX       INTEGER
    {
        informational(1),
        minor(2),
        major(3),
        critical(4)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry severity"
    ::= { sc2MessageTextLogs 7 }

sc2msgTextLogCSSComponent OBJECT-TYPE
    SYNTAX       TrueFalse
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component which caused the log message may be replaced by the customer (CSS=Customer Self Service)"
    ::= { sc2MessageTextLogs 8 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2MessageLogActionHintTable
-- STATUS       mandatory
-- DESCRIPTION  "Action text strings for error and event messages"
--
--      sc2MessageLogActionHintTable: *******.*********.*********.7.5
--
-- ----------------------------------------------------------------------------------------------

sc2MessageLogActionHintTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2MessageLogActionHints
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Action text strings for error and event messages"
    ::= { sc2Recovery 5 }

sc2MessageLogActionHints OBJECT-TYPE
    SYNTAX       Sc2MessageLogActionHints
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2mlaLanguage, sc2mlaErrorCode, sc2mlaType, sc2mlaIndex }
    ::= { sc2MessageLogActionHintTable 1 }

Sc2MessageLogActionHints ::= SEQUENCE
{
    sc2mlaLanguage
        INTEGER,
    sc2mlaErrorCode
        INTEGER,
    sc2mlaType
        INTEGER,
    sc2mlaIndex
        INTEGER,
    sc2mlaMessage
        DisplayString
}

sc2mlaLanguage OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Action text language (index, Windows language code)
                  e.g. 1031, 1033 for German, English"
    ::= { sc2MessageLogActionHints 1 }

sc2mlaErrorCode OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Action text error code (index)"
    ::= { sc2MessageLogActionHints 2 }

sc2mlaType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        summary(1),
        cause(2),
        resolution(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message log entry severity"
    ::= { sc2MessageLogActionHints 3 }

sc2mlaIndex OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Action text entry string index (index, 1..n)"
    ::= { sc2MessageLogActionHints 4 }

sc2mlaMessage OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Action message string"
    ::= { sc2MessageLogActionHints 5 }



-- **********************************************************************************************
--
-- GROUP        sc2Status
-- DESCRIPTION  "This group contains status summary information"
--
--      sc2Status group: *******.*********.*********.8
--
-- **********************************************************************************************

sc2Status OBJECT IDENTIFIER		::= { fscServerControl2 8 }

sc2AgentStatus OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Overall status of all monitored components in this agent
                  ok(1):            all components are working properly; no warnings, no errors
                  warning(2):       at least one component reports a warning status or a redundant component reports an error status
                  error(3):         at least one non-redundant component reports an error status
                  unknown(5):       none of the components has a valid status (e.g. during initialization)
                  notPresent(6):    none of the components is present
                  notManageable(7): none of the components is manageable"
    ::= { sc2Status 1 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2StatusComponentTable
-- STATUS       mandatory
-- DESCRIPTION  "Table with status values of the components in this agent. The table
--               summarizes all instances of one component to one status value."
--
--      sc2StatusComponentTable: *******.*********.*********.8.2
--
-- ----------------------------------------------------------------------------------------------

sc2StatusComponentTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2StatusComponents
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table with status values of the components in this agent. The table
                  summarizes all instances of one component to one status value."
    ::= { sc2Status 2 }

sc2StatusComponents OBJECT-TYPE
    SYNTAX       Sc2StatusComponents
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2csUnitId }
    ::= { sc2StatusComponentTable 1 }

Sc2StatusComponents ::= SEQUENCE
{
    sc2csUnitId
        INTEGER,
    sc2csStatusOverall
        CompStatus,
    sc2csStatusBoot
        CompStatus,
    sc2csStatusPowerSupply
        CompStatus,
    sc2csStatusTemperature
        CompStatus,
    sc2csStatusFans
        CompStatus,
    sc2csStatusVoltages
        CompStatus,
    sc2csStatusCpu
        CompStatus,
    sc2csStatusMemoryModule
        CompStatus
}

sc2csUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2StatusComponents 1 }

sc2csStatusOverall OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Overall status (worst status of all components) in this unit"
    ::= { sc2StatusComponents 2 }

sc2csStatusBoot OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Boot status (errors from power-on selftest and boot)"
    ::= { sc2StatusComponents 3 }

sc2csStatusPowerSupply OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Power supply status"
    ::= { sc2StatusComponents 4 }

sc2csStatusTemperature OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Temperature status"
    ::= { sc2StatusComponents 5 }

sc2csStatusFans OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Fan status"
    ::= { sc2StatusComponents 6 }

sc2csStatusVoltages OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Voltage status"
    ::= { sc2StatusComponents 7 }

sc2csStatusCpu OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "CPU status"
    ::= { sc2StatusComponents 8 }

sc2csStatusMemoryModule OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module status"
    ::= { sc2StatusComponents 9 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2ComponentStatusSensorTable
-- STATUS       mandatory
-- DESCRIPTION  "This table contains the component status sensor table of the BMC (baseboard management
--               controller). Additionally it also holds the CSS (Customer Self Service) information."
--
--      sc2ComponentStatusSensorTable: *******.*********.*********.8.3
--
-- ----------------------------------------------------------------------------------------------

sc2ComponentStatusSensorTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2ComponentStatusSensors
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "This table contains the component status sensor table of the BMC (baseboard management
                  controller). Additionally it also holds the CSS (Customer Self Service) information."
    ::= { sc2Status 3 }

sc2ComponentStatusSensors OBJECT-TYPE
    SYNTAX       Sc2ComponentStatusSensors
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2cssUnitId, sc2cssSensorNumber }
    ::= { sc2ComponentStatusSensorTable 1 }

Sc2ComponentStatusSensors ::= SEQUENCE
{
    sc2cssUnitId
        INTEGER,
    sc2cssSensorNumber
        INTEGER,
    sc2cssSensorDesignation
        DisplayString,
    sc2cssSensorDevice
        DisplayString,
    sc2cssSensorDeviceInstance
        INTEGER,
    sc2cssSensorPhysicalLed
        TrueFalse,
    sc2cssSensorCssComponent
        TrueFalse,
    sc2cssSensorStatus
        INTEGER,
    sc2cssComponentServicePartId
        DisplayString
}

sc2cssUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2ComponentStatusSensors 1 }

sc2cssSensorNumber OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "BMC component status sensor number (index, 1..n)"
    ::= { sc2ComponentStatusSensors 2 }

sc2cssSensorDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component status sensor designation string"
    ::= { sc2ComponentStatusSensors 3 }

sc2cssSensorDevice OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Name of device (entity) the component status sensor belongs to"
    ::= { sc2ComponentStatusSensors 4 }

sc2cssSensorDeviceInstance OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Instance number of device (entity) the component status sensor belongs to"
    ::= { sc2ComponentStatusSensors 5 }

sc2cssSensorPhysicalLed OBJECT-TYPE
    SYNTAX       TrueFalse
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "If set, component has a physical status LED showing the sensor status"
    ::= { sc2ComponentStatusSensors 6 }

sc2cssSensorCssComponent OBJECT-TYPE
    SYNTAX       TrueFalse
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "If set, component may be replaced by the customer (CSS 'custormer-self-service' component)"
    ::= { sc2ComponentStatusSensors 7 }

sc2cssSensorStatus OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        ok(2),
        identify(3),
        prefailure-warning(4),
        failure(5),
        not-present(6)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status returned by the component status sensor"
    ::= { sc2ComponentStatusSensors 8 }

sc2cssComponentServicePartId OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Spare part number of the replaceable component; used to create a link
                  to the spare part list on the service's spare part catalogue.
                  This usually is the component's designation string, with blanks substituted by underscores."
    ::= { sc2ComponentStatusSensors 9 }


-- ----------------------------------------------------------------------------------------------

sc2cssTableSize OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Number of entries in the component status sensor table"
    ::= { sc2Status 4 }


-- **********************************************************************************************
--
-- GROUP        sc2Maintenance
-- DESCRIPTION  "Objects for system maintenance (initiate test trap, get controller/BIOS firmware versions...)"
--
--      sc2Maintenance group: *******.*********.*********.9
--
-- **********************************************************************************************

sc2Maintenance OBJECT IDENTIFIER		::= { fscServerControl2 9 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2MaintenanceObjectTable
-- STATUS       mandatory
-- DESCRIPTION  "Table containing some other maintenance objects"
--
--      sc2MaintenanceObjectTable: *******.*********.*********.9.1
--
-- ----------------------------------------------------------------------------------------------

sc2MaintenanceObjectTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2MaintenanceObjects
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table containing some other maintenance objects"
    ::= { sc2Maintenance 1 }

sc2MaintenanceObjects OBJECT-TYPE
    SYNTAX       Sc2MaintenanceObjects
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2mtUnitId }
    ::= { sc2MaintenanceObjectTable 1 }

Sc2MaintenanceObjects ::= SEQUENCE
{
    sc2mtUnitId
        INTEGER,
    sc2ErrorCounterStartTime
        INTEGER,
    sc2SendTestTrap
        INTEGER,
    sc2AddTrapDestination
        IpAddress
}

sc2mtUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2MaintenanceObjects 1 }

sc2ErrorCounterStartTime OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Memory module error counter start time (seconds since 1/1/1970, -1 if unknown).
                  This is the time when the error counters were started or reset last time."
    ::= { sc2MaintenanceObjects 2 }

sc2SendTestTrap OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Send a test trap (test alarm) to the manager application(s) to test connection and trap
                  configuration; trap cannot be disabled.
                  Initiate test trap sending by writing a non-zero value.
                  Reading this object just returns the value 0."
    ::= { sc2MaintenanceObjects 3 }

sc2AddTrapDestination OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Add the specified IP address (of the management station) to
                  the trap destination list of this server. That client will become an alarm 
                  receiver for traps of this server."
    ::= { sc2MaintenanceObjects 4 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2FirmwareVersionTable
-- STATUS       mandatory
-- DESCRIPTION  "Controller firmware version table"
--
--      sc2FirmwareVersionTable: *******.*********.*********.9.2
--
-- ----------------------------------------------------------------------------------------------

sc2FirmwareVersionTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2FirmwareVersions
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Controller firmware version table"
    ::= { sc2Maintenance 2 }

sc2FirmwareVersions OBJECT-TYPE
    SYNTAX       Sc2FirmwareVersions
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2fwUnitId, sc2fwType }
    ::= { sc2FirmwareVersionTable 1 }

Sc2FirmwareVersions ::= SEQUENCE
{
    sc2fwUnitId
        INTEGER,
    sc2fwType
        INTEGER,
    sc2fwModelName
        DisplayString,
    sc2fwVersion
        DisplayString
}

sc2fwUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2FirmwareVersions 1 }

sc2fwType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        bios(1),
        management-controller(2),
        remote-management-controller(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Controller/firmware type"
    ::= { sc2FirmwareVersions 2 }

sc2fwModelName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Controller/firmware model name (e.g. 'Copernicus'...)"
    ::= { sc2FirmwareVersions 3 }

sc2fwVersion OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Controller/firmware version string (e.g. '3.81.02')"
    ::= { sc2FirmwareVersions 4 }



-- **********************************************************************************************
--
-- GROUP        sc2Deployment
-- DESCRIPTION  "Objects for software deployment, mainly for deploying software to blade servers."
--
--      sc2Deployment group: *******.*********.*********.10
--
-- **********************************************************************************************

sc2Deployment OBJECT IDENTIFIER		::= { fscServerControl2 10 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2DeployInfoTable
-- STATUS       mandatory
-- DESCRIPTION  "Table with data of deployment information.
--               
--               This table is OBSOLETE (no longer used for new implementations)!"
--
--      sc2DeployInfoTable: *******.*********.*********.10.1
--
-- ----------------------------------------------------------------------------------------------

sc2DeployInfoTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2DeployInfo
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table with data of deployment information.
                  
                  This table is OBSOLETE (no longer used for new implementations)!"
    ::= { sc2Deployment 1 }

sc2DeployInfo OBJECT-TYPE
    SYNTAX       Sc2DeployInfo
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2dplInfoUnitId }
    ::= { sc2DeployInfoTable 1 }

Sc2DeployInfo ::= SEQUENCE
{
    sc2dplInfoUnitId
        INTEGER,
    sc2DeployInfoChassisId
        DisplayString,
    sc2DeployInfoMacAddr1
        PhysAddress,
    sc2DeployInfoMacAddr2
        PhysAddress,
    sc2DeployInfoMacAddr3
        PhysAddress,
    sc2DeployInfoMacAddr4
        PhysAddress,
    sc2DeployInfoIpAddr1
        IpAddress,
    sc2DeployInfoIpAddr2
        IpAddress,
    sc2DeployInfoIpAddr3
        IpAddress,
    sc2DeployInfoIpAddr4
        IpAddress,
    sc2DeployInfoNetMask1
        IpAddress,
    sc2DeployInfoNetMask2
        IpAddress,
    sc2DeployInfoNetMask3
        IpAddress,
    sc2DeployInfoNetMask4
        IpAddress,
    sc2DeployInfoGateway1
        IpAddress,
    sc2DeployInfoGateway2
        IpAddress,
    sc2DeployInfoGateway3
        IpAddress,
    sc2DeployInfoGateway4
        IpAddress,
    sc2DeployInfoHostname
        DisplayString,
    sc2DeployInfoMasterImageReference
        DisplayString,
    sc2DeployInfoStatusOfBlade
        INTEGER,
    sc2DeployInfoLanStatusOfSlot
        INTEGER,
    sc2DeployInfoAutomaticRecovery
        INTEGER,
    sc2DeployInfoStatusOfCloning
        INTEGER,
    sc2DeployInfoBootMode
        INTEGER
}

sc2dplInfoUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2DeployInfo 1 }

sc2DeployInfoChassisId OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Chassis serial number"
    ::= { sc2DeployInfo 2 }

sc2DeployInfoMacAddr1 OBJECT-TYPE
    SYNTAX       PhysAddress
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "MAC address 1"
    ::= { sc2DeployInfo 3 }

sc2DeployInfoMacAddr2 OBJECT-TYPE
    SYNTAX       PhysAddress
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "MAC address 2"
    ::= { sc2DeployInfo 4 }

sc2DeployInfoMacAddr3 OBJECT-TYPE
    SYNTAX       PhysAddress
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "MAC address 3"
    ::= { sc2DeployInfo 5 }

sc2DeployInfoMacAddr4 OBJECT-TYPE
    SYNTAX       PhysAddress
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "MAC address 4"
    ::= { sc2DeployInfo 6 }

sc2DeployInfoIpAddr1 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP address 1"
    ::= { sc2DeployInfo 7 }

sc2DeployInfoIpAddr2 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP address 2"
    ::= { sc2DeployInfo 8 }

sc2DeployInfoIpAddr3 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP address 3"
    ::= { sc2DeployInfo 9 }

sc2DeployInfoIpAddr4 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP address 4"
    ::= { sc2DeployInfo 10 }

sc2DeployInfoNetMask1 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP subnet mask 1"
    ::= { sc2DeployInfo 11 }

sc2DeployInfoNetMask2 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP subnet mask 2"
    ::= { sc2DeployInfo 12 }

sc2DeployInfoNetMask3 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP subnet mask 3"
    ::= { sc2DeployInfo 13 }

sc2DeployInfoNetMask4 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP subnet mask 4"
    ::= { sc2DeployInfo 14 }

sc2DeployInfoGateway1 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP gateway 1"
    ::= { sc2DeployInfo 15 }

sc2DeployInfoGateway2 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP gateway 2"
    ::= { sc2DeployInfo 16 }

sc2DeployInfoGateway3 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP gateway 3"
    ::= { sc2DeployInfo 17 }

sc2DeployInfoGateway4 OBJECT-TYPE
    SYNTAX       IpAddress
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "IP gateway 4"
    ::= { sc2DeployInfo 18 }

sc2DeployInfoHostname OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Host name"
    ::= { sc2DeployInfo 19 }

sc2DeployInfoMasterImageReference OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "URL in UNC notification (but in ASCII) to remote image file
                  (with the extensions of *.img, *.cfg, *.txt)"
    ::= { sc2DeployInfo 20 }

sc2DeployInfoStatusOfBlade OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        power-down(2),
        standby(3),
        system-boot-failure(4),
        bios-setup(5),
        booting(6)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Status of this server:
                   power-down(2):          waiting for boot
                   standby(3):             power management mode
                   system-boot-failure(4): system boot failure
                   bios-setup(5):          BIOS setup or boot phase
                   booting(6):             ACPI OS currently booting"
    ::= { sc2DeployInfo 21 }

sc2DeployInfoLanStatusOfSlot OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        false(2),
        true(3)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Defines wether the IP-LAN definitions are valid or not."
    ::= { sc2DeployInfo 22 }

sc2DeployInfoAutomaticRecovery OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        false(2),
        true(3)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Defines if this server blade should be provided automatic recovery."
    ::= { sc2DeployInfo 23 }

sc2DeployInfoStatusOfCloning OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        not-cloned(2),
        cloning(3),
        cloned(4)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Status of current cloning process."
    ::= { sc2DeployInfo 24 }

sc2DeployInfoBootMode OBJECT-TYPE
    SYNTAX       INTEGER
    {
        unknown(1),
        normal(2),
        pxeBiosSetup(3),
        pxeLan0(4),
        pxeLan1(5),
        pxeLan2(6),
        pxeLan3(7)
    }
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Boot mode of the server for remote deployment:
                    normal(2):       boot from standard disk subsystem
                    pxeBiosSetup(3): boot from PXE according to BIOS setup settings (for OEM systems)
                    pxeLanx(y):      boot from PXE LAN interface x (0..3)"
    ::= { sc2DeployInfo 25 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2OemDeployInfoTable
-- STATUS       mandatory
-- DESCRIPTION  "Table with data of server OEM information.
--               
--               This table is OBSOLETE (no longer used for new implementations)!"
--
--      sc2OemDeployInfoTable: *******.*********.*********.10.2
--
-- ----------------------------------------------------------------------------------------------

sc2OemDeployInfoTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2OemDeployInfo
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table with data of server OEM information.
                  
                  This table is OBSOLETE (no longer used for new implementations)!"
    ::= { sc2Deployment 2 }

sc2OemDeployInfo OBJECT-TYPE
    SYNTAX       Sc2OemDeployInfo
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2odplInfoUnitId, sc2odplParamId }
    ::= { sc2OemDeployInfoTable 1 }

Sc2OemDeployInfo ::= SEQUENCE
{
    sc2odplInfoUnitId
        INTEGER,
    sc2odplParamId
        INTEGER,
    sc2OemDeployInfoParamData
        DisplayString
}

sc2odplInfoUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2OemDeployInfo 1 }

sc2odplParamId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "OEM parameter ID (index, 1..10)"
    ::= { sc2OemDeployInfo 2 }

sc2OemDeployInfoParamData OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "OEM parameter data"
    ::= { sc2OemDeployInfo 3 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2DeployLanInterfaceTable
-- STATUS       mandatory
-- DESCRIPTION  "Table with system LAN interfaces information:
--               This table lists all real LAN interfaces in same physical order
--               as the BIOS uses it for PXE boot.
--               Only on-board interfaces are listed; no virtual interfaces."
--
--      sc2DeployLanInterfaceTable: *******.*********.*********.10.3
--
-- ----------------------------------------------------------------------------------------------

sc2DeployLanInterfaceTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2DeployLanInterfaces
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table with system LAN interfaces information:
                  This table lists all real LAN interfaces in same physical order
                  as the BIOS uses it for PXE boot.
                  Only on-board interfaces are listed; no virtual interfaces."
    ::= { sc2Deployment 3 }

sc2DeployLanInterfaces OBJECT-TYPE
    SYNTAX       Sc2DeployLanInterfaces
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2dplLanUnitId, sc2dplLanInterfaceNr }
    ::= { sc2DeployLanInterfaceTable 1 }

Sc2DeployLanInterfaces ::= SEQUENCE
{
    sc2dplLanUnitId
        INTEGER,
    sc2dplLanInterfaceNr
        INTEGER,
    sc2dplLanMacAddress
        PhysAddress
}

sc2dplLanUnitId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Unit identifier (index, 1..n)"
    ::= { sc2DeployLanInterfaces 1 }

sc2dplLanInterfaceNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "LAN interface number (index, 1..n)"
    ::= { sc2DeployLanInterfaces 2 }

sc2dplLanMacAddress OBJECT-TYPE
    SYNTAX       PhysAddress
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "LAN interface hardware (MAC) address"
    ::= { sc2DeployLanInterfaces 3 }



-- **********************************************************************************************
--
-- GROUP        sc2DriverMonitoring
-- DESCRIPTION  "Objects for monitoring operating system event logs on
--               driver events or errors and generating status upon these events."
--
--      sc2DriverMonitoring group: *******.*********.*********.11
--
-- **********************************************************************************************

sc2DriverMonitoring OBJECT IDENTIFIER		::= { fscServerControl2 11 }

-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2DriverMonitorComponentTable
-- STATUS       mandatory
-- DESCRIPTION  "Table of components monitored by the driver monitor.
--               Foreign index sc2uUnitId is from sc2UnitTable."
--
--      sc2DriverMonitorComponentTable: *******.*********.*********.11.1
--
-- ----------------------------------------------------------------------------------------------

sc2DriverMonitorComponentTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2DriverMonitorComponents
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table of components monitored by the driver monitor.
                  Foreign index sc2uUnitId is from sc2UnitTable."
    ::= { sc2DriverMonitoring 1 }

sc2DriverMonitorComponents OBJECT-TYPE
    SYNTAX       Sc2DriverMonitorComponents
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2uUnitId, sc2drvmonCompClass, sc2drvmonCompIndex }
    ::= { sc2DriverMonitorComponentTable 1 }

Sc2DriverMonitorComponents ::= SEQUENCE
{
    sc2drvmonCompClass
        INTEGER,
    sc2drvmonCompIndex
        INTEGER,
    sc2drvmonCompName
        DisplayString,
    sc2drvmonCompType
        INTEGER,
    sc2drvmonCompDriverName
        DisplayString,
    sc2drvmonCompLocationDesignation
        DisplayString,
    sc2drvmonCompLocationKey
        DisplayString,
    sc2drvmonCompStatus
        CompStatus,
    sc2drvmonCompErrorAcknowledge
        INTEGER
}

sc2drvmonCompClass OBJECT-TYPE
    SYNTAX       INTEGER
    {
        other(1),
        software(2),
        network(3),
        storage(4)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component class (index)"
    ::= { sc2DriverMonitorComponents 1 }

sc2drvmonCompIndex OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component index (index, 1..n). This is an index over all component classes, but unit specific."
    ::= { sc2DriverMonitorComponents 2 }

sc2drvmonCompName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Name or designation of the component"
    ::= { sc2DriverMonitorComponents 3 }

sc2drvmonCompType OBJECT-TYPE
    SYNTAX       INTEGER
    {
        other(1),
        pci(2),
        usb(3)
    }
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Component (bus) type"
    ::= { sc2DriverMonitorComponents 4 }

sc2drvmonCompDriverName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Name of the driver that created the event record"
    ::= { sc2DriverMonitorComponents 5 }

sc2drvmonCompLocationDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Device location designation (if it is an adapter or other hardware component); e.g. 'PCI Slot #2' or 'IOB#1-FUNC#0'"
    ::= { sc2DriverMonitorComponents 6 }

sc2drvmonCompLocationKey OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Device location key, used for identifying the component in other tables (like PCI device table); e.g. 'TYPE_PCI&BUS_3&DEV_14&FUNC_0'"
    ::= { sc2DriverMonitorComponents 7 }

sc2drvmonCompStatus OBJECT-TYPE
    SYNTAX       CompStatus
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Status of the component; caused by specific error events, reset by corresponding 'component OK' events or by manual acknowledge"
    ::= { sc2DriverMonitorComponents 8 }

sc2drvmonCompErrorAcknowledge OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-write
    STATUS       mandatory
    DESCRIPTION  "Acknowledge an error condition (reset error or warning status) of the component by writing a non-zero value (reading this object just returns the value 0)"
    ::= { sc2DriverMonitorComponents 9 }


-- ----------------------------------------------------------------------------------------------
--
-- TABLE        sc2DriverMonitorMessageTable
-- STATUS       mandatory
-- DESCRIPTION  "Table of messages gathered by the driver monitor. The messages are based
--               on the monitored operating system event log and are available in translated languages.
--               Foreign indexes: sc2uUnitId is from sc2UnitTable, sc2msgTextLogLanguage from sc2MessageTextLogTable,
--               sc2drvmonCompClass, sc2drvmonCompType and sc2drvmonCompIndex from sc2DriverMonitorComponentTable.
--               When sc2drvmonCompIndex is 0, the event could not be assigned to any component."
--
--      sc2DriverMonitorMessageTable: *******.*********.*********.11.2
--
-- ----------------------------------------------------------------------------------------------

sc2DriverMonitorMessageTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF Sc2DriverMonitorMessages
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Table of messages gathered by the driver monitor. The messages are based
                  on the monitored operating system event log and are available in translated languages.
                  Foreign indexes: sc2uUnitId is from sc2UnitTable, sc2msgTextLogLanguage from sc2MessageTextLogTable,
                  sc2drvmonCompClass, sc2drvmonCompType and sc2drvmonCompIndex from sc2DriverMonitorComponentTable.
                  When sc2drvmonCompIndex is 0, the event could not be assigned to any component."
    ::= { sc2DriverMonitoring 2 }

sc2DriverMonitorMessages OBJECT-TYPE
    SYNTAX       Sc2DriverMonitorMessages
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  ""
    INDEX   { sc2uUnitId, sc2msgTextLogLanguage, sc2drvmonCompClass, sc2drvmonCompIndex, sc2msgTextLogSeqNr }
    ::= { sc2DriverMonitorMessageTable 1 }

Sc2DriverMonitorMessages ::= SEQUENCE
{
    sc2drvmonMsgSeqNr
        INTEGER,
    sc2drvmonMsgTimestamp
        INTEGER,
    sc2drvmonMsgMessage
        DisplayString,
    sc2drvmonMsgEventId
        INTEGER,
    sc2drvmonMsgSeverity
        OsLogSeverity,
    sc2drvmonMsgErrorCode
        INTEGER,
    sc2drvmonOrigLogMessage
        DisplayString,
    sc2drvmonOrigLogSource
        DisplayString,
    sc2drvmonOrigLogId
        INTEGER,
    sc2drvmonOrigLogSeverity
        OsLogSeverity
}

sc2drvmonMsgSeqNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Driver message entry sequence number (index, 1..n)"
    ::= { sc2DriverMonitorMessages 1 }

sc2drvmonMsgTimestamp OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Driver message entry time stamp (time_t = seconds since 1/1/1970, 0:00 AM as GMT)"
    ::= { sc2DriverMonitorMessages 2 }

sc2drvmonMsgMessage OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message entry string (multi-language;
                  not every language may be implemented, then English message has to be requested)"
    ::= { sc2DriverMonitorMessages 3 }

sc2drvmonMsgEventId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message entry error code (this code is a driver monitor specific error code)"
    ::= { sc2DriverMonitorMessages 4 }

sc2drvmonMsgSeverity OBJECT-TYPE
    SYNTAX       OsLogSeverity
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message entry severity (may be different from original event log severity)"
    ::= { sc2DriverMonitorMessages 5 }

sc2drvmonMsgErrorCode OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Message entry error code (must be used to get summary, causes and resolutions from sc2MessageLogActionHintTable table)"
    ::= { sc2DriverMonitorMessages 6 }

sc2drvmonOrigLogMessage OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Original operating system event log / syslog entry message (only available in the server's original language or in english,
                  depending on the driver that created that entry)"
    ::= { sc2DriverMonitorMessages 7 }

sc2drvmonOrigLogSource OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Original log entry source name"
    ::= { sc2DriverMonitorMessages 8 }

sc2drvmonOrigLogId OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Original log entry ID number (may not be available for some platforms/controllers, then ID = 0)"
    ::= { sc2DriverMonitorMessages 9 }

sc2drvmonOrigLogSeverity OBJECT-TYPE
    SYNTAX       OsLogSeverity
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Original event log entry severity"
    ::= { sc2DriverMonitorMessages 10 }


sc2drvmonTableUpdateCount OBJECT-TYPE
    SYNTAX       Counter
    ACCESS       read-only
    STATUS       mandatory
    DESCRIPTION  "Counter defining the update revision of the driver monitoring tables.
                  This counter is incremented with every change/update of the driver monitoring tables."
    ::= { sc2DriverMonitoring 3 }


-- **********************************************************************************************
--
-- GROUP        sc2NotificationsTrapInfo
-- DESCRIPTION  "This group defines the notifications/traps and their parameter variables.
--               They are not accessible directly, but sent together with traps."
--
--      sc2NotificationsTrapInfo group: *******.*********.*********.20.1
--
-- **********************************************************************************************

sc2NotificationsTrapInfo OBJECT IDENTIFIER		::= { sc2Notifications 1 }

sc2TrapInfoServerName OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Name of the server the trap comes from"
    ::= { sc2NotificationsTrapInfo 1 }

sc2TrapInfoTime OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Time when the trap occured (in seconds since 01/01/1970 00:00h)"
    ::= { sc2NotificationsTrapInfo 2 }

sc2TrapInfoCabinetNr OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Cabinet number generating the trap"
    ::= { sc2NotificationsTrapInfo 3 }

sc2TrapInfoObjectDesignation OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Designation string for object that generated the trap (fan, temperature sensor...)"
    ::= { sc2NotificationsTrapInfo 4 }

sc2TrapInfoString OBJECT-TYPE
    SYNTAX       DisplayString
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Additional string to be displayed in the trap text"
    ::= { sc2NotificationsTrapInfo 5 }

sc2TrapInfoInteger OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Additional number to be displayed in the trap text"
    ::= { sc2NotificationsTrapInfo 6 }

sc2TrapInfoInteger2 OBJECT-TYPE
    SYNTAX       INTEGER
    ACCESS       not-accessible
    STATUS       mandatory
    DESCRIPTION  "Additional number to be displayed in the trap text"
    ::= { sc2NotificationsTrapInfo 7 }


-- **********************************************************************************************
--
-- GROUP        sc2NotificationsTraps
-- DESCRIPTION  "This group defines the traps."
--
--     Trap enterprise: sc2Notifications (*******.*********.*********.20)
--
-- **********************************************************************************************

--
-- Trap number range for this MIB: 2000..2199
--

------------------------------------------------------------------------------------------
-- Trap group: Test trap, agent and controller problems
--

sc2TrapTest TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "Test trap to verify trap connection."
    --#TYPE         "Test trap"
    --#SUMMARY      "Test trap from server %s (no error)."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Note: This is no error condition."
    --#STATE        OPERATIONAL
    ::= 2000

sc2TrapCommunicationFailure TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The communication with management controller failed!"
    --#TYPE         "Communication lost"
    --#SUMMARY      "Communication with the Server Management controller in cabinet %d of server %s lost."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2001

sc2TrapCommunicationEstablished TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The communication with the management controller was reestablished"
    --#TYPE         "Communication established"
    --#SUMMARY      "Communication with the Server Management controller in cabinet %d of server %s established again."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2002

sc2TrapControllerSelftestWarning TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Controller selftest warning."
    --#TYPE         "Controller selftest warning"
    --#SUMMARY      "The Server Management controller in cabinet %d has detected an minor problem during selftest of server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2003

sc2TrapControllerSelftestError TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Controller selftest error."
    --#TYPE         "Controller selftest error"
    --#SUMMARY      "The Server Management controller in cabinet %d of server %s failed."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2004

sc2TrapBiosSelftestError TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "A critical error happend while BIOS selftest. This error needs to be acknowledged to clear the error condition."
    --#TYPE         "Critical BIOS selftest error"
    --#SUMMARY      "A critical error happend while BIOS selftest in cabinet %d of server %s. See server management message log (recovery log) for detailed information."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: See message log (recovery log) to get the error reason and fix the problem.\nPress 'Acknowledge BIOS selftest status' in ServerView to get the\nerror condition cleared when the problem is fixed."
    --#STATE        OPERATIONAL
    ::= 2005

sc2TrapSevereSystemError TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The system was restarted after a severe problem. See server management message log (recovery log) for detailed information."
    --#TYPE         "The system was restarted after a severe problem"
    --#SUMMARY      "The system was restarted after a severe problem at cabinet %d of server %s. See server management message log (recovery log) for detailed information."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: See message log (recovery log) to get the error reason and fix the problem."
    --#STATE        OPERATIONAL
    ::= 2006

------------------------------------------------------------------------------------------
-- Trap group: Cooling deviced (fans and liquid cooling pumps)
--

sc2TrapFanAdded TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated hot-plug fan or liquid pump was inserted."
    --#TYPE         "Fan added"
    --#SUMMARY      "Cooling device '%s' was inserted into cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2010

sc2TrapFanRemoved TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated hot-plug fan or liquid pump was removed."
    --#TYPE         "Fan removed"
    --#SUMMARY      "Cooling device '%s' was removed from cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2011

sc2TrapFanOk TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated fan or liquid pump is working again."
    --#TYPE         "Fan OK"
    --#SUMMARY      "Cooling device '%s' in cabinet %d of server %s is working again."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2012

sc2TrapFanCritical TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated fan or liquid pump became critical"
    --#TYPE         "Fan failure predicted"
    --#SUMMARY      "Cooling device '%s' will fail in near future in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2013

sc2TrapFanFailed TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated fan or liquid pump failed."
    --#TYPE         "Fan failed"
    --#SUMMARY      "Cooling device '%s' failed in cabinet %d of server '%s'."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Replace defect fan or liquid pump. Attention: Do not operate system with cover removed. Proper airflow will not be guaranteed!"
    --#STATE        NONOPERATIONAL
    ::= 2014

sc2TrapRedundantFanFailed TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated redundant fan or liquid pump failed"
    --#TYPE         "Redundant fan failed"
    --#SUMMARY      "The redundant cooling device '%s' failed in cabinet %d of server %s. System can become critical if another cooling device in this group fails."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2015

------------------------------------------------------------------------------------------
-- Trap group: Temperature
--

sc2TrapTempOk TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The temperature of the indicated sensor has decreased to the normal level."
    --#TYPE         "Temperature normal"
    --#SUMMARY      "Temperature at sensor '%s' in cabinet %d of server %s is within normal range."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2020

sc2TrapTempWarning TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The temperature of the indicated sensor has reached the warning level."
    --#TYPE         "Temperature warning"
    --#SUMMARY      "Temperature at sensor '%s' in cabinet %d of server %s has reached the warning level."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Action: Check fan openings.\nCheck fans.\n Reduce ambient temperature. Attention: Do not operate system with cover removed. Proper airflow will not be guaranteed!"
    --#STATE        OPERATIONAL
    ::= 2021

sc2TrapTempCritical TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The temperature of the indicated sensor is out of tolerance range. The system will shut down and power off if shutdown is enabled."
    --#TYPE         "Temperature critical"
    --#SUMMARY      "Temperature at sensor '%s' in cabinet %d of server %s has reached the critical level."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Check fan openings\nCheck fans\nReduce ambient temperature.\nLet the system cool down before restart. Attention: Do not operate system with cover removed. Proper airflow will not be guaranteed!"
    --#STATE        OPERATIONAL
    ::= 2022

sc2TrapTempSensorOk TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated broken temperature sensor is OK again."
    --#TYPE         "Temperature sensor OK"
    --#SUMMARY      "Temperature sensor '%s' in cabinet %d of server %s is working again."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2023

sc2TrapTempSensorBroken TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "The indicated temperature sensor is broken."
    --#TYPE         "Temperature sensor broken"
    --#SUMMARY      "Temperature sensor '%s' in cabinet %d of server %s is broken or not connected."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Action: Check connection or replace temperature sensor."
    --#STATE        OPERATIONAL
    ::= 2024

------------------------------------------------------------------------------------------
-- Trap group: Power supply
--

sc2TrapPowerSupplyAdded TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "One hot-replace power supply was added."
    --#TYPE         "Power supply added"
    --#SUMMARY      "Power supply '%s' in cabinet %d at server %s was added."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2030

sc2TrapPowerSupplyRemoved TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "One hot-replace power supply was removed"
    --#TYPE         "Power supply removed"
    --#SUMMARY      "Power supply '%s' in cabinet %d at server %s was removed"
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2031

sc2TrapPowerSupplyOk TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Power supply is working again."
    --#TYPE         "Power supply OK"
    --#SUMMARY      "Power supply '%s' in cabinet %d at server %s is working again."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2032

sc2TrapPowerSupplyCritical TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Power supply status has become critical."
    --#TYPE         "Power supply critical"
    --#SUMMARY      "Insufficient operating power supplies available in cabinet %d at server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Replace defective power supply.\nInstall additional power supply."
    --#STATE        OPERATIONAL
    ::= 2033

sc2TrapPowerSupplyFailed TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "One hot-replace power supply failed."
    --#TYPE         "Power supply failed"
    --#SUMMARY      "Power supply '%s' in cabinet %d at server %s failed."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Action: Replace defect power supply. Attention: Power supply redundancy is lost. To restore redundancy replace defect power supply as soon as possible!"
    --#STATE        OPERATIONAL
    ::= 2034

sc2TrapRedundantPowerSupplyFailed TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "One redundant hot-replace power supply failed"
    --#TYPE         "Redundant power supply failed"
    --#SUMMARY      "Redundant power supply '%s' in cabinet %d at server %s failed. System can become critical if another power supply fails."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2035

sc2TrapPowerSupplyRedundancyLost TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Power supply redundancy no longer available."
    --#TYPE         "Power supply redundancy lost"
    --#SUMMARY      "Power supply redundancy in cabinet %d at server %s lost. System will become critical if a power supply fails."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2036

sc2TrapPowerSupplyCriticalTemperature TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Critical temperature in power supply."
    --#TYPE         "Critical temperature at power supply"
    --#SUMMARY      "Temperature at power supply '%s' in cabinet %d of server %s has reached the critical level."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2037

sc2TrapPowerSupplyFanFailurePrediction TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Fan failure prediction in power supply."
    --#TYPE         "Fan failure prediction at power supply"
    --#SUMMARY      "Fan failure is predicted at power supply '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2038

sc2TrapPowerSupplyFanFailure TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Fan failure in power supply."
    --#TYPE         "Fan failure at power supply"
    --#SUMMARY      "Fan failure at power supply '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2039

------------------------------------------------------------------------------------------
-- Trap group: Power failure
--

sc2TrapAcFail TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Mains failed in the specified cabinet. This trap can only happen in storage extension cabinets without UPS or BBU. A server will not have time to send this trap."
    --#TYPE         "AC failed"
    --#SUMMARY      "AC failure in cabinet %d of server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Check line voltage / power supply of cabinet."
    --#STATE        OPERATIONAL
    ::= 2040

sc2TrapDcFail TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "DC power failed in the specified cabinet. This is the result of the system's power-good sensor monitoring. The system may stop when this condition occurs."
    --#TYPE         "Power failed"
    --#SUMMARY      "DC power failure in cabinet %d of server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Check power supply units; add additional power supply.\nReplace power supply unit(s); check AC power."
    --#STATE        OPERATIONAL
    ::= 2041

sc2TrapOnBattery TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoInteger,
        sc2TrapInfoInteger2
        }
    DESCRIPTION     "The server is operating on battery power - by UPS or backup battery unit (BBU)."
    --#TYPE         "AC failed - server is operating on battery"
    --#SUMMARY      "AC failure. Cabinet %d at server %s is running on battery power. The remaining battery lifetime is approximately %d minutes."
    --#ARGUMENTS    { 2, 0, 3 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Check mains line voltage."
    --#STATE        OPERATIONAL
    ::= 2042

sc2TrapOnMains TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The mains voltage returned after a power failure."
    --#TYPE         "AC OK"
    --#SUMMARY      "Mains returned after power failure in cabinet %d at server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2043

------------------------------------------------------------------------------------------
-- Trap group: Voltages
--

sc2TrapVoltageOk TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Power supply voltage is within normal range again."
    --#TYPE         "Voltage OK"
    --#SUMMARY      "Power supply voltage '%s' in cabinet %d at server %s is within normal range again."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2050

sc2TrapVoltageTooLow TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Power supply voltage is too low."
    --#TYPE         "Voltage too low"
    --#SUMMARY      "Power supply voltage '%s' in cabinet %d at server %s is too low."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2051

sc2TrapVoltageTooHigh TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Power supply voltage is too high."
    --#TYPE         "Voltage too high"
    --#SUMMARY      "Power supply voltage '%s' in cabinet %d at server %s is too high."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2052

sc2TrapVoltageFailed TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Power supply voltage is out of range."
    --#TYPE         "Voltage out of range"
    --#SUMMARY      "Power supply voltage '%s' in cabinet %d at server %s is out of range."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2053

sc2TrapBatteryVoltagePrefail TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Battery is predicted to fail"
    --#TYPE         "Battery failure predicted"
    --#SUMMARY      "Battery voltage '%s' in cabinet %d at server %s: Battery is predicted to fail in near future."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        DEGRADED
    ::= 2054

------------------------------------------------------------------------------------------
-- Trap group: Memory errors
--

sc2TrapCorrectableMemErrorAddr TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoInteger
        }
    DESCRIPTION     "A correctable memory error at specified address was detected."
    --#TYPE         "Correctable memory error"
    --#SUMMARY      "Correctable memory error at address %d in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Action: In case of a persistent error try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module."
    --#STATE        OPERATIONAL
    ::= 2060

sc2TrapUncorrectableMemErrorAddr TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoInteger
        }
    DESCRIPTION     "An uncorrectable memory error at specified address was detected."
    --#TYPE         "Uncorrectable memory error"
    --#SUMMARY      "Uncorrectable memory error at address %d in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module."
    --#STATE        NONOPERATIONAL
    ::= 2061

sc2TrapCorrectableMemErrorBank TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A correctable memory error at specified bank was detected."
    --#TYPE         "Correctable memory error"
    --#SUMMARY      "Correctable memory error at bank '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Action: In case of a persistent error try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module."
    --#STATE        OPERATIONAL
    ::= 2062

sc2TrapUncorrectableMemErrorBank TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "An uncorrectable memory error at specified bank was detected."
    --#TYPE         "Uncorrectable memory error"
    --#SUMMARY      "Uncorrectable memory error at bank '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module"
    --#STATE        NONOPERATIONAL
    ::= 2063

sc2TrapCorrectableMemErrorModule TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A correctable memory error at specified module was detected."
    --#TYPE         "Correctable memory error"
    --#SUMMARY      "Correctable memory error at module '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Action: In case of a persistent error try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module."
    --#STATE        OPERATIONAL
    ::= 2064

sc2TrapUncorrectableMemErrorModule TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "An uncorrectable memory error at specified module was detected."
    --#TYPE         "Uncorrectable memory error"
    --#SUMMARY      "Uncorrectable memory error at module '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module."
    --#STATE        NONOPERATIONAL
    ::= 2065

sc2TrapCorrectableMemError TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "A correctable memory error at unknown location was detected."
    --#TYPE         "Correctable memory error"
    --#SUMMARY      "Correctable memory error in cabinet %d of server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Action: In case of a persistent error try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module."
    --#STATE        OPERATIONAL
    ::= 2066

sc2TrapUncorrectableMemError TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "An uncorrectable memory error at unknown location was detected."
    --#TYPE         "Uncorrectable memory error"
    --#SUMMARY      "Uncorrectable memory error in cabinet %d of server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Action: Try to locate defect memory module:\nGo to window View / System Board / Memory Modules.\nReplace defect memory module."
    --#STATE        NONOPERATIONAL
    ::= 2067

sc2TrapMemErrorModulePrefail TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A memory module is predicted to fail (prefailure)."
    --#TYPE         "Memory module prefailure"
    --#SUMMARY      "Memory module failure is predicted for module '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Warning: A memory module failure is predicted. Too many errors have occured implying that the module could fail in near future. Action: Replace the failing module."
    --#STATE        OPERATIONAL
    ::= 2068

sc2TrapMemErrorModuleFailing TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A memory module is failing."
    --#TYPE         "Memory module failing"
    --#SUMMARY      "Memory module '%s' in cabinet %d of server %s is failing. Too many errors have occured."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         "Warning: A memory module is failing. Action: Replace the failing module immediately!"
    --#STATE        OPERATIONAL
    ::= 2069

sc2TrapMemErrorModuleReplaced TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A memory module had failed and was replaced by a hot-spare module."
    --#TYPE         "Memory module replaced"
    --#SUMMARY      "Memory module '%s' in cabinet %d of server %s had failed and was replaced by a hot-spare module"
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Warning: A memory module had failed. So many errors had occured that its bank was taken out of service and replaced by a hot-spare bank. Action: Replace the failed module immediately!"
    --#STATE        OPERATIONAL
    ::= 2070

sc2TrapMemErrorLoggingDisabled TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Too many correctable memory errors; logging disabled."
    --#TYPE         "Memory error logging disabled"
    --#SUMMARY      "Too many correctable memory errors in cabinet %d at server %s. Error logging was disabled."
    --#SUMMARY      "If logging was disabled and not automatically enabled again, you have to reboot your server to enable memory error logging again.\nIf logging is disabled, prefailure detection is also not active!"
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#HELP         "ATTENTION! If logging was disabled and not automatically enabled again, you have\nto reboot your server to enable memory error logging again.\nIf logging disabled, prefailure detection does not work!"
    --#STATE        OPERATIONAL
    ::= 2071

sc2TrapMemErrorLoggingEnabled TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Memory errors logging enabled again."
    --#TYPE         "Memory error logging enabled"
    --#SUMMARY      "Error logging was enabled again in cabinet %d at server %s (after being disabled because of too many errors)"
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#HELP         "Error logging was enabled again after being disabled because of too many errors."
    --#STATE        OPERATIONAL
    ::= 2072

sc2TrapMemErrorAnyModuleReplaced TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "A memory module had failed and was replaced by a hot-spare module."
    --#TYPE         "Memory module replaced"
    --#SUMMARY      "A memory module in cabinet %d of server %s had failed and was replaced by a hot-spare module"
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Warning: A memory module had failed. So many errors had occured that its bank was taken out of service and replaced by a hot-spare bank. Action: Replace the failed module immediately!"
    --#STATE        OPERATIONAL
    ::= 2073

sc2TrapMemErrorRedundancyLost TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "A memory error caused loss of memory redundancy"
    --#TYPE         "Memory redundancy lost"
    --#SUMMARY      "Memory configuration in cabinet %d of server %s has lost redundancy"
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Warning: A severe memory error occured. Memory redundancy is lost. Action: Replace failing module as soon as possible to regain redundancy!"
    --#STATE        OPERATIONAL
    ::= 2074

sc2TrapMemNVDIMMLifetime TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation,
        sc2TrapInfoInteger
        }
    DESCRIPTION     "An NVDIMM memory module has reached a certain level of lifetime"
    --#TYPE         "NVDIMM Memory module lifetime"
    --#SUMMARY      "Non-volatile memory (NVDIMM) module '%s' in cabinet %d of server %s has reached %d percent of lifetime"
    --#ARGUMENTS    { 3, 2, 0, 4 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2075

------------------------------------------------------------------------------------------
-- Trap group: CPU errors
--

sc2TrapCpuSpeedChanged TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoInteger
        }
    DESCRIPTION     "This trap will be sent if the CPU clock frequency was changed because of a temperature problem."
    --#TYPE         "CPU speed changed"
    --#SUMMARY      "CPU speed at server %s changed to %d percent of its maximum speed."
    --#ARGUMENTS    { 0, 2 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2080

sc2TrapCpuPrefail TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A CPU is predicted to fail (prefailure)."
    --#TYPE         "CPU prefailure"
    --#SUMMARY      "CPU failure is predicted for CPU '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Warning: A CPU failure is predicted. Too many errors have occured implying that the CPU could fail in near future. Action: Replace the failing CPU."
    --#STATE        OPERATIONAL
    ::= 2081

sc2TrapCpuIerr TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A CPU internal error (IERR) occurred"
    --#TYPE         "CPU IERR"
    --#SUMMARY      "Internal error (IERR) occurred on CPU '%s' in cabinet %d of server %s."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Warning: CPU internal error (IERR) occurred. Action: Replace failing CPU if error occurs repeatedly."
    --#STATE        OPERATIONAL
    ::= 2082

sc2TrapCpuDisabled TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "A CPU is disabled by BIOS"
    --#TYPE         "CPU disabled"
    --#SUMMARY      "CPU '%s' in cabinet %d of server %s is disabled."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Warning: CPU was disabled by BIOS after CPU Error occurred. Action: Reenable CPU. If error persists, replace failing CPU."
    --#STATE        OPERATIONAL
    ::= 2083

------------------------------------------------------------------------------------------
-- Trap group: Power on/off, shutdown, reboot, server suspend, server recovery, logging
--

sc2TrapCabinetSwitchedOff TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoString
        }
    DESCRIPTION     "This trap will be sent when a cabinet is switched off. For obvious reasons it cannot be sent when the main cabinet is switched off."
    --#TYPE         "Cabinet switched off"
    --#SUMMARY      "Cabinet %d was switched off because of %s (server %s)."
    --#ARGUMENTS    { 2, 3, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Note: For obvious reasons this trap cannot be sent when the main cabinet is switched off."
    --#STATE        OPERATIONAL
    ::= 2090

sc2TrapCabinetSwitchedOn TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoString
        }
    DESCRIPTION     "This trap will be sent when a cabinet is switched on."
    --#TYPE         "Cabinet switched on"
    --#SUMMARY      "Cabinet %d was switched on because of %s (server %s)."
    --#ARGUMENTS    { 2, 3, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2091

sc2TrapPowerOffTimeReached TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoInteger
        }
    DESCRIPTION     "This trap is sent when the server is going to be switched off by schedule in some minutes."
    --#TYPE         "Scheduled power off time reached"
    --#SUMMARY      "The scheduled power-off time is reached in cabinet %d on server %s! System shutdown in %d minutes."
    --#ARGUMENTS    { 2, 0, 3 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2092

sc2TrapServerShutdown TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoString
        }
    DESCRIPTION     "This trap will be sent when a server is being shut down."
    --#TYPE         "Server shutdown"
    --#SUMMARY      "The reason %s causes a shutdown at server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2093

sc2TrapShutdownCancelled TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "This trap will be sent if a pending server shutdown was cancelled by the user."
    --#TYPE         "Shutdown cancelled"
    --#SUMMARY      "Shutdown at server %s cancelled by the user."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2094

sc2TrapBootRetryCountZero TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "This trap will be sent when a boot retry counter gets zero on power up."
    --#TYPE         "Boot retry counter zero"
    --#SUMMARY      "Boot retry counter is zero on server %s."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2095

sc2TrapServerBoot TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "This trap will be sent when the server was booted"
    --#TYPE         "Server booted"
    --#SUMMARY      "Server %s was booted."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2096

sc2TrapServerStandby TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "This trap will be sent when the server entered standby mode"
    --#TYPE         "Standby mode entered"
    --#SUMMARY      "Server %s entered standby mode."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2097

sc2TrapServerSuspend TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "This trap will be sent when the server entered suspend mode"
    --#TYPE         "Suspend mode entered"
    --#SUMMARY      "Server %s entered suspend mode."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2098

sc2TrapServerResumed TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "This trap will be sent when the server resumed from standby or suspend mode"
    --#TYPE         "Resume from standby or suspend mode"
    --#SUMMARY      "Server %s resumed from standby or suspend mode."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2099

------------------------------------------------------------------------------------------
-- Trap group: Message/event logging
--

sc2TrapMessageLogFull TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The System Event Log (message log) is full. No more messages can be logged. This trap will not occur on wrap-around log types"
    --#TYPE         "Message log full"
    --#SUMMARY      "The System Event Log on server %s in cabinet %d is full. No more messages can be logged! Please clear unneeded log entries as soon as possible!"
    --#ARGUMENTS    { 0, 2 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2100

sc2TrapMessageLogWarning TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoInteger
        }
    DESCRIPTION     "The warning threshold for the number of System Event Log entries has been exceeded."
    --#TYPE         "Message log warning"
    --#SUMMARY      "The System Event Log for cabinet %d at server %s has exceeded %d percent of its capacity."
    --#ARGUMENTS    { 2, 0, 3 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2101

sc2TrapBootMessageLogEntry TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "An error message was written into the systemboard's message log.
                     This could have happened when an error occured before the server management agents were running or
                     any error without a specific trap. See server management message log
                     for detailed error description."
    --#TYPE         "Error in message log"
    --#SUMMARY      "An error was recorded on server %s before the management agents were started (boot phase)."
    --#SUMMARY      "See server management message log (Recovery log) for detailed information."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2102

------------------------------------------------------------------------------------------
-- Trap group: Intrusion detection
--

sc2TrapIntrusionAssertion TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The front door or housing was opened"
    --#TYPE         "Front door or housing opened"
    --#SUMMARY      "The front door or housing of cabinet %d was opened on server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2110

sc2TrapIntrusionDeassertion TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The front door or housing was closed"
    --#TYPE         "Front door or housing closed"
    --#SUMMARY      "The front door or housing of cabinet %d was closed on server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2111

sc2TrapIntrusionChanged TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "The front door or housing was opened or closed"
    --#TYPE         "Front door or housing opened/closed"
    --#SUMMARY      "The front door or housing of cabinet %d was opened or closed on server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2112

sc2TrapPciBusError TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "PCI bus system/parity error"
    --#TYPE         "PCI bus system/parity error"
    --#SUMMARY      "A PCI bus system or parity error happened in cabinet %d on server %s."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        NONOPERATIONAL
    ::= 2113

------------------------------------------------------------------------------------------
-- Trap group: Power on/off (continued)
--

sc2TrapPowerOnTimeReached TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoInteger
        }
    DESCRIPTION     "This trap is sent when the server is going to be switched on by schedule in some minutes. It will be sent by the Remote Management Controller."
    --#TYPE         "Scheduled power on time reached"
    --#SUMMARY      "The scheduled power-on time is reached in cabinet %d on server %s! System will be powered on in %d minutes."
    --#ARGUMENTS    { 2, 0, 3 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2114

------------------------------------------------------------------------------------------
-- Trap group: Customer self service
--

sc2TrapCssWarning TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Customer self service component changed to warning status"
    --#TYPE         "Customer self service warning"
    --#SUMMARY      "Warning status on component '%s' in cabinet %d of server '%s'. This component may be replaced by the customer."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Action: Replace affected component, may be replaced by the customer!"
    --#STATE        OPERATIONAL
    ::= 2120

sc2TrapCssFail TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Customer self service component changed to fail status"
    --#TYPE         "Customer self service fail"
    --#SUMMARY      "Fail status on component '%s' in cabinet %d of server '%s'. This component may be replaced by the customer."
    --#ARGUMENTS    { 3, 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Action: Replace affected component, may be replaced by the customer!"
    --#STATE        OPERATIONAL
    ::= 2121

sc2TrapCssWarningServer TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Customer self service component changed to warning status"
    --#TYPE         "Customer self service warning"
    --#SUMMARY      "Warning status on component '%s' at server '%s'. This component may be replaced by the customer."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Action: Replace affected component, may be replaced by the customer!"
    --#STATE        OPERATIONAL
    ::= 2122

sc2TrapCssFailServer TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoObjectDesignation
        }
    DESCRIPTION     "Customer self service component changed to fail status"
    --#TYPE         "Customer self service fail"
    --#SUMMARY      "Fail status on component '%s' at server '%s'. This component may be replaced by the customer."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         "Action: Replace affected component, may be replaced by the customer!"
    --#STATE        OPERATIONAL
    ::= 2123

------------------------------------------------------------------------------------------
-- Trap group: Power Consumption
--

sc2TrapPowerLimitOk TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Power consumption limit changed to Ok status"
    --#TYPE         "Power limit status ok"
    --#SUMMARY      "Power limit status in cabinet %d of server '%s' ok."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2130

sc2TrapPowerLimitWarning TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Power consumption limit changed to warning status"
    --#TYPE         "Power limit status warning"
    --#SUMMARY      "Power limit status in cabinet %d of server '%s' has exceeded the warning threshold."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2131

sc2TrapPowerLimitCritical TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Power consumption limit changed to critical status"
    --#TYPE         "Power limit status critical"
    --#SUMMARY      "Power limit status in cabinet %d of server '%s' has exceeded the critical threshold."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     CRITICAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2132

sc2TrapPowerLimitDisabled TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoCabinetNr
        }
    DESCRIPTION     "Power consumption limiting disabled"
    --#TYPE         "Power limiting disabled"
    --#SUMMARY      "Power limiting in cabinet %d of server '%s' disabled."
    --#ARGUMENTS    { 2, 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         ""
    --#STATE        OPERATIONAL
    ::= 2133

------------------------------------------------------------------------------------------
-- Trap group: Virtual IO Manager (VIOM)
--

sc2TrapViomInitiateCommunication TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "VIOM communication initiation request - VIOM manager action required.
                     This trap is intended to signal the Virtual IO Manager that the iRMC wants to confirm the usage of the VIOM table.
                     This trap may be repeated several times until the manager responds to the trap."
    --#TYPE         "VIOM communication initiation"
    --#SUMMARY      "VIOM communication initiation request from server %s."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Note: This is no error condition."
    --#STATE        OPERATIONAL
    ::= 2140

sc2TrapViomStatusChanged TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime
        }
    DESCRIPTION     "VIOM status has changed - VIOM manager information.
                     If the user or a server management component deactivates VIOM, the iRMC will send a VIOM status trap.
                     This trap will not be sent if VIOM was activated, because no user interface except the Virtual IO Manager shall be capable to enable VIOM."
    --#TYPE         "VIOM status changed"
    --#SUMMARY      "VIOM status has changed on server %s."
    --#ARGUMENTS    { 0 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#HELP         "Note: This is no error condition."
    --#STATE        OPERATIONAL
    ::= 2141

------------------------------------------------------------------------------------------
-- Trap group: Driver Monitoring
--

sc2TrapDrvMonEventMessage TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoString
        }
    DESCRIPTION     "Driver Monitoring detected an informational event."
    --#TYPE         "Driver Monitoring information event"
    --#SUMMARY      "Driver Monitoring information event at server %s:"
    --#SUMMARY      "%s"
    --#ARGUMENTS    { 0, 2 }
    --#SEVERITY     INFORMATIONAL
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2150

sc2TrapDrvMonEventWarning TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoString
        }
    DESCRIPTION     "Driver Monitoring detected a warning event."
    --#TYPE         "Driver Monitoring warning event"
    --#SUMMARY      "Driver Monitoring warning event at server %s:"
    --#SUMMARY      "%s"
    --#ARGUMENTS    { 0, 2 }
    --#SEVERITY     MINOR
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2151

sc2TrapDrvMonEventError TRAP-TYPE
    ENTERPRISE sc2Notifications
    VARIABLES {
        sc2TrapInfoServerName,
        sc2TrapInfoTime,
        sc2TrapInfoString
        }
    DESCRIPTION     "Driver Monitoring detected an error event."
    --#TYPE         "Driver Monitoring error event"
    --#SUMMARY      "Driver Monitoring error event at server %s:"
    --#SUMMARY      "%s"
    --#ARGUMENTS    { 0, 2 }
    --#SEVERITY     MAJOR
    --#TIMEINDEX    1
    --#STATE        OPERATIONAL
    ::= 2152


END
