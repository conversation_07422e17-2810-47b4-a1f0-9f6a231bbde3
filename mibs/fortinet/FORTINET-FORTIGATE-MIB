--
-- MODULE-IDENTITY
--  OrgName
--     Fortinet Technologies, Inc.
--  ContactInfo
--     Technical Support
--     e-mail: <EMAIL>
--     http://www.fortinet.com
--

FORTINET-FORTIGATE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    FnBoolState, FnIndex, fnAdminEntry, fnSysSerial, fortinet
        FROM FORTINET-CORE-MIB
    ifEntry, ifName, ifIndex
        FROM IF-MIB
    InetAddress, InetAddressPrefixLength, InetAddressType, InetPortNumber
        FROM INET-ADDRESS-MIB
    MODULE-COMPLIANCE, NOTIFICATION-GROUP, OBJECT-GROUP
        FROM SNMPv2-CONF
    sysName
        FROM SNMPv2-MIB
    Counter32, Counter64, Gauge32, Unsigned32, Integer32, IpAddress,
    MODULE-IDENTITY, NOTIFICATION-TYPE, OBJECT-TYPE, TimeTicks, OBJECT-IDENTITY
        FROM SNMPv2-SMI
    Ipv6Address
        FROM IPV6-TC
    CounterBasedGauge64
        FROM HCNUM-TC
    DisplayString, TEXTUAL-CONVENTION, AutonomousType, DateAndTime, PhysAddress, TimeStamp
        FROM SNMPv2-TC;

fnFortiGateMib MODULE-IDENTITY
    LAST-UPDATED "202307210000Z"
    ORGANIZATION
        "Fortinet Technologies, Inc."
    CONTACT-INFO
        "
         Technical Support
         email: <EMAIL>
         http://www.fortinet.com"
    DESCRIPTION
        "MIB module for Fortinet FortiGate devices."
    REVISION    "202307210000Z"
    DESCRIPTION
        "Add fgTrapFaz for FortiAnalyzer event."
    REVISION    "202303160000Z"
    DESCRIPTION
        "Added OID fgServiceGroupWorkerBlades for 6K7K slbc platform."
    REVISION    "202303140000Z"
    DESCRIPTION
        "Added OID for fgChassisInfo and changed the OID of fgChassis from 98 to 25 for 6K7K slbc platform in (ECO-246262)."
    REVISION    "202212190000Z"
    DESCRIPTION
        "Add fgIntfVlanID, fgIntfVlanName, fgIntfVlanPhyName in fgIntfVlanEntry."
    REVISION    "202210220000Z"
    DESCRIPTION
        "Added OIDs for fgTrapSlbc, fgDataCpuUsage and fgDataMemUsage in fgSystemInfo while merging 6K7K."
    REVISION    "202210070000Z"
    DESCRIPTION
        "Added fgVdEntChecksum in fgVdTable, fgHaStatsAllChecksum in fgHaStatsTable and fgProcessorPktTxDroppedCount in fgProcessorTable."
    REVISION    "202209230000Z"
    DESCRIPTION
        "Added fgIntfEntMeaUpBandwidth and fgIntfEntMeaDownBandwidth in fgIntfTable."
    REVISION    "202206300000Z"
    DESCRIPTION
        "Added fgProcessorFnNP6XLITE in fgProcessorTypes."
    REVISION    "202206010000Z"
    DESCRIPTION
        "Added fgFwAddresses in fgFirewall and fgFwAddrDynEmsTable in fgFwAddresses."
    REVISION    "202203110000Z"
    DESCRIPTION
        "Added fgFwAuthUserTables in fgFwUsers."
    REVISION    "202202160000Z"
    DESCRIPTION
        "Added fgVWLHealthCheckLinkUsedBandwidthIn, fgVWLHealthCheckLinkUsedBandwidthOut, fgVWLHealthCheckLinkUsedBandwidthBi, fgVWLHealthCheckLinkMOSCodec, fgVWLHealthCheckLinkMOS in fgVWLHealthCheckLinkEntry."
    REVISION    "202201260000Z"
    DESCRIPTION
        "Added fgFwIppStatsTotalPBAs, fgFwIppStatsInusePBAs, fgFwIppStatsExpiringPBAs, fgFwIppStatsFreePBAs in fgFwIppStatsEntry"
    REVISION    "202104280000Z"
    DESCRIPTION
        "Added fgSwCpu and fgSwMemory to the fgSwDevice table"
    REVISION    "202103090001Z"
    DESCRIPTION
        "Added NPU session related OIDs in fgSystemInfo."
    REVISION    "202103090000Z"
    DESCRIPTION
        "Added fgVpn2TunPhase2Index as the second index in fgVpn2TunTable"
    REVISION    "202103050000Z"
    DESCRIPTION
        "Added fgProcessorFnNP7 in fgProcessorTypes, cleaned up some obsolete ProcessorModuleTypes which should have been deleted by ECO-19013."
    REVISION    "202102250000Z"
    DESCRIPTION
        "Added fgVpnTunEntPhase2Index as the second index in fgVpnTunTable"
    REVISION    "202101260000Z"
    DESCRIPTION
        "Removed fgFwPol6StatsTable from fgFwPolTables"
    REVISION    "202101060000Z"
    DESCRIPTION
         "Added hyperscale-policy OIDs in fgFwPolTables."
    REVISION    "202012070000Z"
    DESCRIPTION
        "Added fgSysRebootReason in fgSystemInfo, fgIntfBcCfgTables in fgIntfBcs, fgLinkMonitorServer/fgLinkMonitorProtocol in fgLinkMonitorTable"
    REVISION    "202010080000Z"
    DESCRIPTION
        "Added fgSysUpTimeDetail in fgSystemInfo, fgMdmLacTac, fgMdmActBand, fgMdmCellId, fgMdmRssi OID in fgMdmInfoTable"
    REVISION    "202010020000Z"
    DESCRIPTION
        "Added fgDpdkEngToCpu in fnFortiGateMib.fgSystem.FgDpdkEngEntry"
    REVISION    "202009300000Z"
    DESCRIPTION
        "Added state standby in fgWcWtpSessionConnectionState."
    REVISION    "202009210000Z"
    DESCRIPTION 
        "Changed the HA related wording to primary/secondary."
    REVISION    "202008140000Z"
    DESCRIPTION 
        "Added fgIntfBcQTable in fgIntfBcs."
    REVISION    "202006250000Z"
    DESCRIPTION
        "Added fgVWLHealthCheckLinkIfName OID in fgVWLHealthCheckLinkTable."
    REVISION    "202005080000Z"
    DESCRIPTION
        "Added fgVpn2Tables in fgVpn and fgSIAdvLicenseDetails in fgSysInfoAdvanced."
    REVISION    "202004300000Z"
    DESCRIPTION
        "Added fgSw OIDs for FortiSwitch device and port feature."
    REVISION    "201910280000Z"
    DESCRIPTION 
        "Added fgDpdkEngs in fnFortiGateMib.fgSystem."
    REVISION    "201908160000Z"
    DESCRIPTION 
        "Added fgDhcp OIDs and fgTrapDhcp for DHCP feature."
    REVISION    "201908070000Z"
    DESCRIPTION
        "Added FgWcWlanSecurityType OIDs "
    REVISION    "201905310000Z"
    DESCRIPTION 
        "Added ingress shaping OIDs in fgIntfBcs"
    REVISION    "201905270000Z"
    DESCRIPTION
        "Added fgConfig."
    REVISION    "201901240000Z"
    DESCRIPTION
        "Added fgSysNeedLogPartitionScan to indicate whether a disk scan is needed."
    REVISION    "201812120000Z"
    DESCRIPTION
        "Added fgLog OIDs for global log device statistics."
    REVISION    "201806110000Z"
    DESCRIPTION
        "Added OIDs for fnbam stats info."
    REVISION    "201806060000Z"
    DESCRIPTION
        "Added DNS Proxy stats info OIDs."
    REVISION    "201804060000Z"
    DESCRIPTION 
        "Added fgIntfBcs OIDs for a interface"
    REVISION    "201801180000Z"
    DESCRIPTION 
        "Revert fgAvOversizedDetected and fgAvOversizedBlocked OIDs numbers."
    REVISION    "201801100000Z"
    DESCRIPTION 
        "Added fgFwGtp OIDs."
    REVISION    "201711160000Z"
    DESCRIPTION
        "Added fgSlaProbeClient OIDs for server probe feature."
    REVISION    "201710180000Z"
    DESCRIPTION
        "Added NPU counters in fgNPU."
    REVISION    "201710030000Z"
    DESCRIPTION 
        "Added fgIntfEntEstUpBandwidth and fgIntfEntEstDownBandwidth in fgIntfTable."
    REVISION    "201709070000Z"
    DESCRIPTION 
        "Added fgDisks in fnFortiGateMib.fgSystem, and created fgWebCacheDiskFailure 
        and fgWanOptDiskFailure in fnFortiGateMib.fgSystem.fgDisks."
    REVISION    "201708300000Z"
    DESCRIPTION
        "Added fgVWLHealthCheckLink OIDs for virtual-wan-link health-check per member link."
    REVISION    "201708250000Z"
    DESCRIPTION 
        "Added per-CPU high trap."
    REVISION    "201707210000Z"
    DESCRIPTION 
        "Added fgServerLoadBalanceRealServerAddress6 in fgTrapServerLoadBalanceRealServerDown."
    REVISION    "201707120000Z"
    DESCRIPTION
        "Added fgInternalLTEModemsInfo under fnFortiGateMib, Added Modem, SIM card, Signal Info,
	Traffic Info, Data Session Info OIDs."
    REVISION    "201704280000Z"
    DESCRIPTION
        "Added fgAvSMBVirusDetected and fgAvSMBVirusBlocked OIDs."
    REVISION    "201704050000Z"
    DESCRIPTION
        "Added fgAvMAPIVirusDetected and fgAvMAPIVirusBlocked OIDs."
    REVISION    "201701160000Z"
    DESCRIPTION
        "Added extended database version OIDs for AV and IPS."
    REVISION    "201609150000Z"
    DESCRIPTION
        "Added fgFwIppools OIDs."
    REVISION    "201606170000Z"
    DESCRIPTION 
        "Various non-monotonically increase Counter32 values have been changed to Gauge32."
    REVISION    "201504230000Z"
    DESCRIPTION 
        "Added fgProcessorFnNP6 in fgProcessorTypes."
    REVISION    "201503160000Z"
    DESCRIPTION 
	 "Added fgDevice OIDs"
    REVISION    "201501100000Z"
    DESCRIPTION 
	 "Added fgIntfVlanHbs OIDs for vlan interface heartbeat feature"
    REVISION    "201412040000Z"
    DESCRIPTION 
	 "Added fgLinkMonitor OIDs for link-monitor feature"
    REVISION    "201406040000Z"
    DESCRIPTION 
	 "Added fgUsbModemInfoObjects OIDs for LTE Modem"
    REVISION    "201402130000Z"
    DESCRIPTION 
	 "Added fgUsbports OIDs for external USB ports"
    REVISION    "201308120000Z"
    DESCRIPTION 
	 "Added fgIntfVrrps OIDs for VRRP"
    REVISION    "201307260000Z"
    DESCRIPTION 
	 "Added fgServerLoadBalance OIDs."
    REVISION    "201304120000Z"
    DESCRIPTION 
	 "Added fgTrapIpsFailOpen OID in fgTraps"
    REVISION    "201304060000Z"
    DESCRIPTION 
        "Added fgWc wireless controller OIDs"
    REVISION    "201211290000Z"
    DESCRIPTION 
        "Added fgVpnTrapPhase1Name OID in VPN traps"
    REVISION    "201207100000Z"
    DESCRIPTION 
        "Added OID for 64-bit sysUpTime"
    REVISION    "201205160000Z"
    DESCRIPTION 
        "Added OID for a virtual domain's HA cluster member state."
    REVISION    "201202060000Z"
    DESCRIPTION 
        "Added OIDs for advanced system info and NP4/NP2 processors."
    REVISION    "201109120000Z"
    DESCRIPTION 
        "Added OIDs for IPv6 statistics."
    REVISION    "201101100000Z"
    DESCRIPTION
        "Added OIDs for session setup rates and VPN tunnel stats"
    REVISION    "200911030000Z"
    DESCRIPTION
        "Added OIDs for Proxy information, CPU and low-memory usage"
    REVISION    "200910010000Z"
    DESCRIPTION
        "Added new FortiGate models."
    REVISION    "200907070000Z"
    DESCRIPTION 
        "Added fgTrapFazDisconnect."
    REVISION    "200811030000Z"
    DESCRIPTION 
        "Added fgVdTpTable. Re-ordered MIB tree to improve readability.
         Replaced indexes for tables that had a dependent relationship
         with another table."
    REVISION    "200809020000Z"
    DESCRIPTION 
        "Added OIDs for compatibility with FORTIOS-300-MIB."
    REVISION    "200808190000Z"
    DESCRIPTION 
        "Conformance statements."
    REVISION    "200806160000Z"
    DESCRIPTION 
        "Spelling corrections."
    REVISION    "200804140000Z"
    DESCRIPTION 
        "Initial version of FORTINET-FORTIGATE-MIB."
    ::= { fortinet 101 }


FgVdIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS      current
    DESCRIPTION 
        "data type for virtual-domain indexes"
    SYNTAX      Integer32 (1..2147483647)

FgOpMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "enumerated type for device operation modes"
    SYNTAX      INTEGER { nat(1), transparent(2) }

FgHaMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "enumerated type for HA cluster modes"
    SYNTAX      INTEGER { standalone(1), activeActive(2), 
                    activePassive(3) }

FgHaState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for HA cluster member state"
    SYNTAX      INTEGER { primary(1), secondary(2), standalone(3) }

FgSgWorkerBladeIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS      current
    DESCRIPTION
        "data type for Fortigate worker blade indexes"
    SYNTAX      Integer32 (1..2147483647)

FgSgWorkerBladeState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for worker blade state"
    SYNTAX      INTEGER { unknown(1), dead(2), standby(3), active(4) }

FgHaLBSchedule ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "enumerated type for HA load-balancing schedules"
    SYNTAX      INTEGER { none(1), hub(2), leastConnections(3), 
                    roundRobin(4), weightedRoundRobin(5), random(6), 
                    ipBased(7), ipPortBased(8) }

FgAdminPermLevel ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "enumerated type for administrative permissions"
    SYNTAX      INTEGER { readAdmin(0), writeAdmin(1), domainAdmin(15), 
                    superAdmin(255) }

FgFwUserAuthType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "enumerated type for user authentication types"
    SYNTAX      INTEGER { local(1), radiusSingle(2), radiusMultiple(3), 
                    ldap(4) }

FgFwAuthUserType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for authenticated user types"
    SYNTAX      INTEGER { fsso(0), rsso(1), ntlm(2), fw(3),
                    wsso(4), fsspCitrix(5), ssoGuest(6),
                    disclaimer(7), other(8), unauth(9), email(10) }

FgSessProto ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "data type for session protocols"
    SYNTAX      INTEGER { ip(0), icmp(1), igmp(2), ipip(4), tcp(6), 
                    egp(8), pup(12), udp(17), idp(22), ipv6(41), 
                    rsvp(46), gre(47), esp(50), ah(51), ospf(89), 
                    pim(103), comp(108), raw(255) }

FgP2PProto ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "data type for session protocols"
    SYNTAX      INTEGER { bitTorrent(0), eDonkey(1), gnutella(2), 
                    kaZaa(3), skype(4), winNY(5) }

FgScanAvDisposition ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "Scan AV Disposition for detected anomalies"
    SYNTAX      INTEGER { detected(1), blocked(2) }

FgWanOptProtocols ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "WAN optimization protocol types"
    SYNTAX      INTEGER { http(1), mapi(2), cifs(3), 
                    ftp(4), tcp(5) }

FgWanOptHistPeriods ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "WAN optimization history statistics time period types"
    SYNTAX      INTEGER { last10Min(1), lastHour(2), lastDay(3), 
                    lastMonth(4) }

FgHaStatsSyncStatusType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION 
        "Current HA Sync status types"
    SYNTAX      INTEGER { unsynchronized(0), synchronized(1) }

FgWcWlanSecurityType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for WLAN security methods"
    SYNTAX      INTEGER {
                  other(0),
                  open(1),
                  captivePortal(2),
                  wep64(3),
                  wep128(4),
                  wpaOnlyPersonal(5),
                  wpaOnlyEnterprise(6),
                  wpa2OnlyPersonal(7),
                  wpa2OnlyEnterprise(8),
                  wpaPersonal(9),
                  wpaEnterprise(10),
                  wpaOnlyPersonalCaptivePortal(11),
                  wpa2OnlyPersonalCaptivePortal(12),
                  wpaPersonalCaptivePortal(13),
                  wpa3Sae(14),
                  wpa3SaeTransition(15),
                  wpa3Enterprise(16),
                  wpa3Owe(17),
                  osen(18)  }

FgWcWlanAuthenticationType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for WLAN authentication methods"
    SYNTAX      INTEGER {
                  other(0),
                  psk(1),
                  radiusServer(2),
                  userGroup(3) }

FgWcWlanEncryptionType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for WLAN encryption methods"
    SYNTAX      INTEGER {
                  other(0),
                  none(1),
                  tkip(2),
                  aes(3),
                  tkipAes(4) }

FgWcWtpRadioId ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS      current
    DESCRIPTION
        "unique identifier of a radio on a WTP"
    SYNTAX      Unsigned32 (1..31)

FgWcWtpRadioType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Enumerated types for WTP radio.
         The following enumerated values are supported:
           other(0)             - radio type is unknown
           dot11a(1)            - 802.11a radio
           dot11b(2)            - 802.11b radio
           dot11g(3)            - 802.11g/b radio
           dot11n5g(4)          - 802.11n/a radio at 5GHz band
           dot11n2g(5)          - 802.11n/g/b radio at 2.4GHz band
           dot11ac(6)           - 802.11ac/n/a radio
           dot11ngOnly(7)       - 802.11n/g radio at 2.4GHz band
           dot11gOnly(8)        - 802.11g radio
           dot11n2GHzOnly(9)    - 802.11n radio at 2.4GHz band
           dot11n5GHzOnly(10)   - 802.11n radio at 5GHz band
           dot11acnOnly(11)     - 802.11ac/n radio
           dot11acOnly(12)      - 802.11ac radio
           dot11ax2g(13)        - 802.11ax/n/g/b radio at 2.4GHz band
           dot11ax5g(14)        - 802.11ax/ac/n/a radio at 5GHz band
           dot11ax6g(15),       - 802.11ax radio at 6GHz band
           dot11axng2gOnly(16)  - 802.11ax/n/g radio at 2.4GHz band
           dot11axn2gOnly(17)   - 802.11ax/n radio at 2.4GHz band
           dot11ax2gOnly(18)    - 802.11ax radio at 2.4GHz band
           dot11axacn5gOnly(19) - 802.11ax/ac/n radio at 5GHz band
           dot11axac5gOnly(20)  - 802.11ax/ac radio at 5GHz band
           dot11ax5gOnly(21)    - 802.11ax radio at 5GHz band"
    SYNTAX      INTEGER {
                  other(0),
                  dot11a(1),
                  dot11b(2),
                  dot11g(3),
                  dot11n5g(4),
                  dot11n2g(5),
                  dot11ac(6),
                  dot11ngOnly(7),
                  dot11gOnly(8),
                  dot11n2GHzOnly(9),
                  dot11n5GHzOnly(10),
                  dot11acnOnly(11),
                  dot11acOnly(12),
                  dot11ax2g(13),
                  dot11ax5g(14),
                  dot11ax6g(15),
                  dot11axng2gOnly(16),
                  dot11axn2gOnly(17),
                  dot11ax2gOnly(18),
                  dot11axacn5gOnly(19),
                  dot11axac5gOnly(20),
                  dot11ax5gOnly(21) }

FgWcWtpChannelWidthType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for WTP radio channel width"
    SYNTAX      INTEGER {
                  other(0),
                  width20MHz(1),
                  width40MHz(2),
                  width80MHz(3),
                  width160MHz(4) }

FgWcWtpRadioBandType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "enumerated type for WTP radio band"
    SYNTAX      INTEGER {
                  other(0),
                  band2GHz(1),
                  band5GHz(2) }

FgWcWtpRadioChannelNumber ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS      current
    DESCRIPTION
        "channel number of a WTP radio"
    SYNTAX      Integer32 (0..255)

FgWcWtpRadioMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Represents the operating mode of the WTP radio.
         The following enumerated values are supported:
           other(0)        - The radio mode is unknown.
           notExist(1)     - The radio does not physically exist.
           disabled(2)     - The radio is administratively disabled.
           ap(3)           - The radio is configured as an access point.
           monitor(4)      - The radio is configured as a dedicated rogue AP scanner.
           sniffer(5)      - The radio is configured as a wireless sniffer."
    SYNTAX      INTEGER {
                  other(0),
                  notExist(1),
                  disabled(2),
                  ap(3),
                  monitor(4),
                  sniffer(5) }

FgWcCountryString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "3a"
    STATUS      current
    DESCRIPTION
        "This attribute identifies the country or non-country
         entity in which the station is operating. If it is a
         country, the first two octets of this
         string is the two character country code as described
         in document ISO/IEC 3166-1. The third octet shall
         be one of the following:
         1. an ASCII space character, if the regulations under
         which the station is operating encompass all
         environments in the country,
         2. an ASCII 'O' character, if the regulations under
         which the station is operating are for an Outdoor
         environment only, or
         3. an ASCII 'I' character, if the regulations under
         which the station is operating are for an Indoor
         environment only.
         4. an ASCII 'X' character, if the station is operating
         under a non-country entity. The first two octets of the
         non-country entity shall be two ASCII 'XX' characters."
    SYNTAX      OCTET STRING (SIZE(3))

--
-- fortinet.fnFortiGateMib.fgModel
--

fgModel OBJECT IDENTIFIER ::= { fnFortiGateMib 1 }

-- fgModel start

ffvmbb           OBJECT IDENTIFIER ::= { fgModel 80025 }
ffvmev           OBJECT IDENTIFIER ::= { fgModel 80000 }
ffw3980E         OBJECT IDENTIFIER ::= { fgModel 39804 }
ffwVM64          OBJECT IDENTIFIER ::= { fgModel 90070 }
ffwVM64KVm       OBJECT IDENTIFIER ::= { fgModel 90071 }
fg40FI           OBJECT IDENTIFIER ::= { fgModel 443 }
fg41FI           OBJECT IDENTIFIER ::= { fgModel 444 }
fg900D           OBJECT IDENTIFIER ::= { fgModel 900 }
fgr60F           OBJECT IDENTIFIER ::= { fgModel 643 }
fgr60FI          OBJECT IDENTIFIER ::= { fgModel 648 }
fgt1000D         OBJECT IDENTIFIER ::= { fgModel 10005 }
fgt100E          OBJECT IDENTIFIER ::= { fgModel 1041 }
fgt100EF         OBJECT IDENTIFIER ::= { fgModel 1042 }
fgt100F          OBJECT IDENTIFIER ::= { fgModel 1000 }
fgt101E          OBJECT IDENTIFIER ::= { fgModel 1043 }
fgt101F          OBJECT IDENTIFIER ::= { fgModel 1001 }
fgt1100E         OBJECT IDENTIFIER ::= { fgModel 10006 }
fgt1101E         OBJECT IDENTIFIER ::= { fgModel 10007 }
fgt1200D         OBJECT IDENTIFIER ::= { fgModel 12000 }
fgt140E          OBJECT IDENTIFIER ::= { fgModel 1005 }
fgt140EP         OBJECT IDENTIFIER ::= { fgModel 1006 }
fgt1500D         OBJECT IDENTIFIER ::= { fgModel 15000 }
fgt1500DT        OBJECT IDENTIFIER ::= { fgModel 15001 }
fgt1800F         OBJECT IDENTIFIER ::= { fgModel 15003 }
fgt1801F         OBJECT IDENTIFIER ::= { fgModel 15002 }
fgt2000E         OBJECT IDENTIFIER ::= { fgModel 20000 }
fgt200E          OBJECT IDENTIFIER ::= { fgModel 2009 }
fgt200F          OBJECT IDENTIFIER ::= { fgModel 2011 }
fgt201E          OBJECT IDENTIFIER ::= { fgModel 2010 }
fgt201F          OBJECT IDENTIFIER ::= { fgModel 2012 }
fgt2200E         OBJECT IDENTIFIER ::= { fgModel 18000 }
fgt2201E         OBJECT IDENTIFIER ::= { fgModel 18001 }
fgt2500E         OBJECT IDENTIFIER ::= { fgModel 25000 }
fgt2600F         OBJECT IDENTIFIER ::= { fgModel 26000 }
fgt2601F         OBJECT IDENTIFIER ::= { fgModel 26001 }
fgt3000D         OBJECT IDENTIFIER ::= { fgModel 30000 }
fgt3000F         OBJECT IDENTIFIER ::= { fgModel 30003 }
fgt3001F         OBJECT IDENTIFIER ::= { fgModel 30004 }
fgt300E          OBJECT IDENTIFIER ::= { fgModel 3007 }
fgt301E          OBJECT IDENTIFIER ::= { fgModel 3008 }
fgt3100D         OBJECT IDENTIFIER ::= { fgModel 31000 }
fgt3200D         OBJECT IDENTIFIER ::= { fgModel 32000 }
fgt3300E         OBJECT IDENTIFIER ::= { fgModel 30001 }
fgt3301E         OBJECT IDENTIFIER ::= { fgModel 30002 }
fgt3400E         OBJECT IDENTIFIER ::= { fgModel 34001 }
fgt3401E         OBJECT IDENTIFIER ::= { fgModel 34011 }
fgt3500F         OBJECT IDENTIFIER ::= { fgModel 35001 }
fgt3501F         OBJECT IDENTIFIER ::= { fgModel 35011 }
fgt3600E         OBJECT IDENTIFIER ::= { fgModel 36001 }
fgt3601E         OBJECT IDENTIFIER ::= { fgModel 36011 }
fgt3700D         OBJECT IDENTIFIER ::= { fgModel 37000 }
fgt3800D         OBJECT IDENTIFIER ::= { fgModel 38001 }
fgt3810D         OBJECT IDENTIFIER ::= { fgModel 38101 }
fgt3815D         OBJECT IDENTIFIER ::= { fgModel 38150 }
fgt3960E         OBJECT IDENTIFIER ::= { fgModel 39601 }
fgt3980E         OBJECT IDENTIFIER ::= { fgModel 39801 }
fgt3HD           OBJECT IDENTIFIER ::= { fgModel 3006 }
fgt400D          OBJECT IDENTIFIER ::= { fgModel 4004 }
fgt400E          OBJECT IDENTIFIER ::= { fgModel 4007 }
fgt400EBP        OBJECT IDENTIFIER ::= { fgModel 4009 }
fgt400F          OBJECT IDENTIFIER ::= { fgModel 4010 }
fgt401E          OBJECT IDENTIFIER ::= { fgModel 4008 }
fgt401F          OBJECT IDENTIFIER ::= { fgModel 4011 }
fgt40F           OBJECT IDENTIFIER ::= { fgModel 441 }
fgt41F           OBJECT IDENTIFIER ::= { fgModel 442 }
fgt4200F         OBJECT IDENTIFIER ::= { fgModel 38002 }
fgt4201F         OBJECT IDENTIFIER ::= { fgModel 42002 }
fgt4400F         OBJECT IDENTIFIER ::= { fgModel 39001 }
fgt4401F         OBJECT IDENTIFIER ::= { fgModel 44001 }
fgt5001D         OBJECT IDENTIFIER ::= { fgModel 50015 }
fgt5001E         OBJECT IDENTIFIER ::= { fgModel 50016 }
fgt5001E1        OBJECT IDENTIFIER ::= { fgModel 50017 }
fgt500D          OBJECT IDENTIFIER ::= { fgModel 5004 }
fgt500E          OBJECT IDENTIFIER ::= { fgModel 5005 }
fgt501E          OBJECT IDENTIFIER ::= { fgModel 5006 }
fgt6000F         OBJECT IDENTIFIER ::= { fgModel 60001 }
fgt600D          OBJECT IDENTIFIER ::= { fgModel 6004 }
fgt600E          OBJECT IDENTIFIER ::= { fgModel 6005 }
fgt600F          OBJECT IDENTIFIER ::= { fgModel 6007 }
fgt601E          OBJECT IDENTIFIER ::= { fgModel 6006 }
fgt601F          OBJECT IDENTIFIER ::= { fgModel 6008 }
fgt60E           OBJECT IDENTIFIER ::= { fgModel 641 }
fgt60EJ          OBJECT IDENTIFIER ::= { fgModel 661 }
fgt60EPOE        OBJECT IDENTIFIER ::= { fgModel 642 }
fgt60EV          OBJECT IDENTIFIER ::= { fgModel 663 }
fgt60F           OBJECT IDENTIFIER ::= { fgModel 644 }
fgt61E           OBJECT IDENTIFIER ::= { fgModel 640 }
fgt61F           OBJECT IDENTIFIER ::= { fgModel 645 }
fgt7000E         OBJECT IDENTIFIER ::= { fgModel 70001 }
fgt7000F         OBJECT IDENTIFIER ::= { fgModel 71201 }
fgt70F           OBJECT IDENTIFIER ::= { fgModel 701 }
fgt71F           OBJECT IDENTIFIER ::= { fgModel 702 }
fgt800D          OBJECT IDENTIFIER ::= { fgModel 8004 }
fgt80E           OBJECT IDENTIFIER ::= { fgModel 842 }
fgt80EPOE        OBJECT IDENTIFIER ::= { fgModel 841 }
fgt80F           OBJECT IDENTIFIER ::= { fgModel 845 }
fgt80FBP         OBJECT IDENTIFIER ::= { fgModel 847 }
fgt80FPOE        OBJECT IDENTIFIER ::= { fgModel 850 }
fgt81E           OBJECT IDENTIFIER ::= { fgModel 843 }
fgt81EPOE        OBJECT IDENTIFIER ::= { fgModel 844 }
fgt81F           OBJECT IDENTIFIER ::= { fgModel 846 }
fgt81FPOE        OBJECT IDENTIFIER ::= { fgModel 851 }
fgt90E           OBJECT IDENTIFIER ::= { fgModel 940 }
fgt91E           OBJECT IDENTIFIER ::= { fgModel 941 }
fgtARM64AWS      OBJECT IDENTIFIER ::= { fgModel 90007 }
fgtARM64AZURE    OBJECT IDENTIFIER ::= { fgModel 90027 }
fgtARM64GCP      OBJECT IDENTIFIER ::= { fgModel 90026 }
fgtARM64KVM      OBJECT IDENTIFIER ::= { fgModel 66 }
fgtARM64OCI      OBJECT IDENTIFIER ::= { fgModel 90025 }
fgtARM64XEN      OBJECT IDENTIFIER ::= { fgModel 90008 }
fgtVM64          OBJECT IDENTIFIER ::= { fgModel 30 }
fgtVM64ALI       OBJECT IDENTIFIER ::= { fgModel 90019 }
fgtVM64AWS       OBJECT IDENTIFIER ::= { fgModel 45 }
fgtVM64AZURE     OBJECT IDENTIFIER ::= { fgModel 90081 }
fgtVM64FGCAWS    OBJECT IDENTIFIER ::= { fgModel 46 }
fgtVM64FGCKVM    OBJECT IDENTIFIER ::= { fgModel 61 }
fgtVM64GCP       OBJECT IDENTIFIER ::= { fgModel 65 }
fgtVM64HV        OBJECT IDENTIFIER ::= { fgModel 70 }
fgtVM64IBM       OBJECT IDENTIFIER ::= { fgModel 90022 }
fgtVM64KVm       OBJECT IDENTIFIER ::= { fgModel 60 }
fgtVM64OPC       OBJECT IDENTIFIER ::= { fgModel 47 }
fgtVM64RAXONDEMAND OBJECT IDENTIFIER ::= { fgModel 90021 }
fgtVM64XEN       OBJECT IDENTIFIER ::= { fgModel 40 }
fgtvmx           OBJECT IDENTIFIER ::= { fgModel 80003 }
fgv16v           OBJECT IDENTIFIER ::= { fgModel 80015 }
fgv32v           OBJECT IDENTIFIER ::= { fgModel 80016 }
fgvm00           OBJECT IDENTIFIER ::= { fgModel 80004 }
fgvm01           OBJECT IDENTIFIER ::= { fgModel 80005 }
fgvm02           OBJECT IDENTIFIER ::= { fgModel 80006 }
fgvm04           OBJECT IDENTIFIER ::= { fgModel 80007 }
fgvm08           OBJECT IDENTIFIER ::= { fgModel 80008 }
fgvm16           OBJECT IDENTIFIER ::= { fgModel 80009 }
fgvm1v           OBJECT IDENTIFIER ::= { fgModel 80011 }
fgvm2v           OBJECT IDENTIFIER ::= { fgModel 80012 }
fgvm32           OBJECT IDENTIFIER ::= { fgModel 80010 }
fgvm4v           OBJECT IDENTIFIER ::= { fgModel 80013 }
fgvm8v           OBJECT IDENTIFIER ::= { fgModel 80014 }
fgvmel           OBJECT IDENTIFIER ::= { fgModel 80023 }
fgvmev           OBJECT IDENTIFIER ::= { fgModel 80001 }
fgvmml           OBJECT IDENTIFIER ::= { fgModel 80024 }
fgvmpg           OBJECT IDENTIFIER ::= { fgModel 80030 }
fgvmsb           OBJECT IDENTIFIER ::= { fgModel 80022 }
fgvmsl           OBJECT IDENTIFIER ::= { fgModel 80021 }
fgvmul           OBJECT IDENTIFIER ::= { fgModel 80020 }
fgvmxx           OBJECT IDENTIFIER ::= { fgModel 80002 }
fgvulv           OBJECT IDENTIFIER ::= { fgModel 80019 }
fr70FB           OBJECT IDENTIFIER ::= { fgModel 704 }
fr70FM           OBJECT IDENTIFIER ::= { fgModel 705 }
fw40FI           OBJECT IDENTIFIER ::= { fgModel 447 }
fw41FI           OBJECT IDENTIFIER ::= { fgModel 448 }
fw81FD           OBJECT IDENTIFIER ::= { fgModel 852 }
fw81FP           OBJECT IDENTIFIER ::= { fgModel 853 }
fwf40F           OBJECT IDENTIFIER ::= { fgModel 445 }
fwf41F           OBJECT IDENTIFIER ::= { fgModel 446 }
fwf60E           OBJECT IDENTIFIER ::= { fgModel 639 }
fwf60EJ          OBJECT IDENTIFIER ::= { fgModel 662 }
fwf60EV          OBJECT IDENTIFIER ::= { fgModel 664 }
fwf60F           OBJECT IDENTIFIER ::= { fgModel 646 }
fwf61E           OBJECT IDENTIFIER ::= { fgModel 649 }
fwf61F           OBJECT IDENTIFIER ::= { fgModel 647 }
fwf80F           OBJECT IDENTIFIER ::= { fgModel 848 }
fwf81F           OBJECT IDENTIFIER ::= { fgModel 849 }

-- fgModel end

--
-- fortinet.fnFortiGateMib.fgTraps
--

fgTraps OBJECT IDENTIFIER
    ::= { fnFortiGateMib 2 }

fgTrapPrefix OBJECT IDENTIFIER
    ::= { fgTraps 0 }

--
-- fortinet.fnFortiGateMib.fgVirtualDomain
--

fgVirtualDomain OBJECT IDENTIFIER
    ::= { fnFortiGateMib 3 }

--
-- fortinet.fnFortiGateMib.fgVirtualDomain.fgVdInfo
--

fgVdInfo OBJECT IDENTIFIER
    ::= { fgVirtualDomain 1 }

fgVdNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of virtual domains in vdTable"
    ::= { fgVdInfo 1 }

fgVdMaxVdoms OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The maximum number of virtual domains allowed on the device as allowed by hardware and/or licensing"
    ::= { fgVdInfo 2 }

fgVdEnabled OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Whether virtual domains are enabled on this device"
    ::= { fgVdInfo 3 }

--
-- fortinet.fnFortiGateMib.fgVirtualDomain.fgVdTables
--

fgVdTables OBJECT IDENTIFIER
    ::= { fgVirtualDomain 2 }

--
-- fortinet.fnFortiGateMib.fgVirtualDomain.fgVdTables.fgVdTable
--

fgVdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of virtual domains configured on the device"
    ::= { fgVdTables 1 }

fgVdEntry OBJECT-TYPE
    SYNTAX      FgVdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to a particular virtual domain"
    INDEX       { fgVdEntIndex }
    ::= { fgVdTable 1 }

FgVdEntry ::= SEQUENCE {
    fgVdEntIndex    FgVdIndex,
    fgVdEntName     DisplayString,
    fgVdEntOpMode   FgOpMode,
    fgVdEntHaState  FgHaState,
    fgVdEntCpuUsage Gauge32,
    fgVdEntMemUsage Gauge32,
    fgVdEntSesCount Gauge32,
    fgVdEntSesRate  Gauge32,
    fgVdEntChecksum DisplayString
}

fgVdEntIndex OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Internal virtual domain index used to uniquely identify rows in this table. This index is also used by other tables referencing a virtual domain."
    ::= { fgVdEntry 1 }

fgVdEntName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The name of the virtual domain"
    ::= { fgVdEntry 2 }

fgVdEntOpMode OBJECT-TYPE
    SYNTAX      FgOpMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Operation mode of the virtual domain (NAT or Transparent)"
    ::= { fgVdEntry 3 }

fgVdEntHaState OBJECT-TYPE
    SYNTAX      FgHaState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "HA cluster member state of the virtual domain on this device
         (primary, secondary or standalone)"
    ::= { fgVdEntry 4 }

fgVdEntCpuUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "CPU usage of the virtual domain (percentage)."
    ::= { fgVdEntry 5 }

fgVdEntMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Memory usage of the virtual domain (percentage)."
    ::= { fgVdEntry 6 }

fgVdEntSesCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of active sessions on the virtual domain."
    ::= { fgVdEntry 7 }

fgVdEntSesRate OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The session setup rate on the virtual domain."
    ::= { fgVdEntry 8 }

fgVdEntChecksum OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current vdom checksum value"
    ::= { fgVdEntry 9 }

--
-- fortinet.fnFortiGateMib.fgVirtualDomain.fgVdTables.fgVdTpTable
--

fgVdTpTable OBJECT-TYPE
    SYNTAX SEQUENCE OF FgVdTpEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION 
        "A table of virtual domains in transparent operation mode.  This table has a
         sparse dependent relationship with fgVdTable."    
    ::= { fgVdTables 2 }

fgVdTpEntry OBJECT-TYPE
    SYNTAX FgVdTpEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION 
        "An entry containing information applicable
         to a particular virtual domain in transparent mode"
    INDEX { fgVdEntIndex }
    ::= { fgVdTpTable 1 }

FgVdTpEntry ::= SEQUENCE {
    fgVdTpMgmtAddrType InetAddressType,
    fgVdTpMgmtAddr InetAddress,
    fgVdTpMgmtMask InetAddressPrefixLength
}

fgVdTpMgmtAddrType OBJECT-TYPE
    SYNTAX InetAddressType
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION 
        "The type of address stored in fgVdTpMgmtAddr, in compliance with INET-ADDRESS-MIB"
    ::= { fgVdTpEntry 1 }

fgVdTpMgmtAddr OBJECT-TYPE
    SYNTAX InetAddress
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION 
        "The management IP address of the virtual domain in transparent mode,
         typically an IPv4 address. The address type/format is determined by fgVdTpMgmtAddrType."
    ::= { fgVdTpEntry 2 }

fgVdTpMgmtMask OBJECT-TYPE
    SYNTAX InetAddressPrefixLength
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION 
        "The address prefix length (or network mask) applied to the fgVdTpMgmtAddr"
    ::= { fgVdTpEntry 3 }

--
-- fortinet.fnFortiGateMib.fgSystem
--

fgSystem OBJECT IDENTIFIER
    ::= { fnFortiGateMib 4 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSystemInfo
--

fgSystemInfo OBJECT IDENTIFIER
    ::= { fgSystem 1 }

fgSysVersion OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Firmware version of the device"
    ::= { fgSystemInfo 1 }

fgSysMgmtVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index that identifies the management virtual domain. This index corresponds to the index used by fgVdTable."
    ::= { fgSystemInfo 2 }

fgSysCpuUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current CPU usage (percentage)"
    ::= { fgSystemInfo 3 }

fgSysMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current memory utilization (percentage)"
    ::= { fgSystemInfo 4 }

fgSysMemCapacity OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total physical memory (RAM) installed (KB)"
    ::= { fgSystemInfo 5 }

fgSysDiskUsage OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current hard disk usage (MB), if disk is present"
    ::= { fgSystemInfo 6 }

fgSysDiskCapacity OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total hard disk capacity (MB), if disk is present"
    ::= { fgSystemInfo 7 }

fgSysSesCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of active sessions on the device"
    ::= { fgSystemInfo 8 }

fgSysLowMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current lowmem utilization (percentage). Lowmem is memory available
         for the kernel's own data structures and kernel specific tables. The
         system can get into a bad state if it runs out of lowmem."
    ::= { fgSystemInfo 9 }

fgSysLowMemCapacity OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total lowmem capacity (KB). See fgSysLowMemUsage for the description
         of lowmem." 
    ::= { fgSystemInfo 10 }

fgSysSesRate1 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average session setup rate over the past minute."
    ::= { fgSystemInfo 11 }

fgSysSesRate10 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average session setup rate over the past 10 minutes."
    ::= { fgSystemInfo 12 }

fgSysSesRate30 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average session setup rate over the past 30 minutes."
    ::= { fgSystemInfo 13 }

fgSysSesRate60 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average session setup rate over the past 60 minutes."
    ::= { fgSystemInfo 14 }

fgSysSes6Count OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of active ipv6 sessions on the device"
    ::= { fgSystemInfo 15 }

fgSysSes6Rate1 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 session setup rate over the past minute."
    ::= { fgSystemInfo 16 }

fgSysSes6Rate10 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 session setup rate over the past 10 minutes."
    ::= { fgSystemInfo 17 }

fgSysSes6Rate30 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 session setup rate over the past 30 minutes."
    ::= { fgSystemInfo 18 }

fgSysSes6Rate60 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 session setup rate over the past 60 minutes."
    ::= { fgSystemInfo 19 }

fgSysUpTime OBJECT-TYPE
    SYNTAX      Counter64
    UNITS       "hundredths of a second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The 64bit time (in hundredths of a second) since the network management portion of the system was last re-initialized."
    ::= { fgSystemInfo 20 }

fgSysNeedLogPartitionScan OBJECT-TYPE
    SYNTAX      FnBoolState
	MAX-ACCESS  read-only
	STATUS      current
	DESCRIPTION
	    "Whether the log partition needs a scan."
	::= { fgSystemInfo 21 }

fgSysUpTimeDetail OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time detail since the network management portion of the system was last re-initialized."
    ::= { fgSystemInfo 22 }

fgSysRebootReason OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The last reboot reason of the device"
    ::= { fgSystemInfo 23 }

fgSysNpuSesCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of active NPU sessions on the device"
    ::= { fgSystemInfo 24 }

fgSysNpuSesRate1 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average NPU session setup rate over the past minute."
    ::= { fgSystemInfo 25 }

fgSysNpuSesRate10 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average NPU session setup rate over the past 10 minutes."
    ::= { fgSystemInfo 26 }

fgSysNpuSesRate30 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average NPU session setup rate over the past 30 minutes."
    ::= { fgSystemInfo 27 }

fgSysNpuSesRate60 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average NPU session setup rate over the past 60 minutes."
    ::= { fgSystemInfo 28 }

fgSysNpuSes6Count OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of active ipv6 NPU sessions on the device"
    ::= { fgSystemInfo 29 }

fgSysNpuSes6Rate1 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 NPU session setup rate over the past minute."
    ::= { fgSystemInfo 30 }

fgSysNpuSes6Rate10 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 NPU session setup rate over the past 10 minutes."
    ::= { fgSystemInfo 31 }

fgSysNpuSes6Rate30 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 NPU session setup rate over the past 30 minutes."
    ::= { fgSystemInfo 32 }

fgSysNpuSes6Rate60 OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "Sessions Per Second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average ipv6 NPU session setup rate over the past 60 minutes."
    ::= { fgSystemInfo 33 }

fgDataCpuUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current Data Plane CPU usage (percentage)"
    ::= { fgSystemInfo 34 }

fgDataMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current Data Plane memory utilization (percentage)"
    ::= { fgSystemInfo 35 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSoftware
--

fgSoftware OBJECT IDENTIFIER
    ::= { fgSystem 2 }

fgSysVersionAv OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virus signature database version installed on the device"
    ::= { fgSoftware 1 }

fgSysVersionIps OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IPS signature database version installed on the device"
    ::= { fgSoftware 2 }

fgSysVersionAvEt OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virus signature extended database version installed on the device"
    ::= { fgSoftware 3 }

fgSysVersionIpsEt OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IPS signature extended database version installed on the device"
    ::= { fgSoftware 4 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgHwSensors
--

fgHwSensors OBJECT IDENTIFIER
    ::= { fgSystem 3 }

fgHwSensorCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgHwSensorTable"
    ::= { fgHwSensors 1 }

fgHwSensorTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgHwSensorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of device specific hardware sensors and values. Because different devices have different hardware sensor capabilities, this table may or may not contain any values."
    ::= { fgHwSensors 2 }

fgHwSensorEntry OBJECT-TYPE
    SYNTAX      FgHwSensorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the name, value, and alarm status of a given hardware sensor"
    INDEX       { fgHwSensorEntIndex }
    ::= { fgHwSensorTable 1 }

FgHwSensorEntry ::= SEQUENCE {
    fgHwSensorEntIndex          FnIndex,
    fgHwSensorEntName           DisplayString,
    fgHwSensorEntValue          DisplayString,
    fgHwSensorEntAlarmStatus    INTEGER
}

fgHwSensorEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A unique identifier within the fgHwSensorTable"
    ::= { fgHwSensorEntry 1 }

fgHwSensorEntName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "A string identifying the sensor by name"
    ::= { fgHwSensorEntry 2 }

fgHwSensorEntValue OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "A string representation of the value of the sensor. Because sensors can present data in different formats, string representation is most general format. Interpretation of the value (units of measure, for example) is dependent on the individual sensor."
    ::= { fgHwSensorEntry 3 }

fgHwSensorEntAlarmStatus OBJECT-TYPE
    SYNTAX      INTEGER { false(0), true(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "If the sensor has an alarm threshold and has exceeded it, this will indicate its status. Not all sensors have alarms."
    ::= { fgHwSensorEntry 4 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgProcessors
--

fgProcessors OBJECT IDENTIFIER
    ::= { fgSystem 4 }

fgProcessorCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgProcessorTable"
    ::= { fgProcessors 1 }

fgProcessorTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgProcessorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table that lists information for each processor"
    ::= { fgProcessors 2 }

fgProcessorEntry OBJECT-TYPE
    SYNTAX      FgProcessorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information for a particular processor"
    INDEX       { fgProcessorEntIndex }
    ::= { fgProcessorTable 1 }

FgProcessorEntry ::= SEQUENCE {
    fgProcessorEntIndex    FnIndex,
    fgProcessorUsage       Gauge32,
    fgProcessorUsage5sec   Gauge32,
    fgProcessorType        AutonomousType,
    fgProcessorContainedIn FnIndex,
    fgProcessorPktRxCount  Counter64,
    fgProcessorPktTxCount  Counter64,
    fgProcessorPktDroppedCount Counter64,
    fgProcessorUserUsage   Gauge32,
    fgProcessorSysUsage    Gauge32,
    fgProcessorPktTxDroppedCount Counter64
}

fgProcessorEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A unique identifier within the fgProcessorTable"
    ::= { fgProcessorEntry 1 }

fgProcessorUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The processor's CPU usage (percentage), which is an average
        calculated over the last minute.
        (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 2 }

fgProcessorUsage5sec OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The processor's CPU usage (percentage), which is an average
        calculated over the last 5 sec.
        (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 3 }

fgProcessorType OBJECT-TYPE
    SYNTAX      AutonomousType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "An indication of the type of the processor.
        Types are defined in fgProcessorTypes."
    ::= { fgProcessorEntry 4 }

fgProcessorContainedIn OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The index to the processor module entry in the fgProcessorModuleTable
        that contains this processor."
    ::= { fgProcessorEntry 5 }

fgProcessorPktRxCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The total number of packets received by this processor
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 6 }

fgProcessorPktTxCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The total number of packets transmitted by this processor
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 7 }

fgProcessorPktDroppedCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The total number of packets dropped by this processor
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 8 }

fgProcessorUserUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The processor's CPU user space usage, which is an average 
         calculated over the last minute.
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 9 }

fgProcessorSysUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The processor's CPU system space usage, which is an average 
         calculated over the last minute.
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 10 }

fgProcessorPktTxDroppedCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of Tx packets dropped by this processor
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorEntry 11 }

--
-- Registrations for processor types, for use with fgProcessorType
--
fgProcessorTypes OBJECT IDENTIFIER
    ::= { fgProcessors 3 }

fgProcessorOther OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used when no other defined
        type is appropriate."
    ::= { fgProcessorTypes 1 }

fgProcessorIntel OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Intel CPU."
    ::= { fgProcessorTypes 2 }

fgProcessorAMD OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for AMD CPU."
    ::= { fgProcessorTypes 3 }

fgProcessorXlr OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for RMI XLR processor."
    ::= { fgProcessorTypes 4 }

fgProcessorFnSoc OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Fortinet FortiSoc processor."
    ::= { fgProcessorTypes 5 }

fgProcessorFnNP2 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Fortinet NP2 security processor."
    ::= { fgProcessorTypes 6 }

fgProcessorFnNP4 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Fortinet NP4 security processor."
    ::= { fgProcessorTypes 7 }

fgProcessorFnNP6 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Fortinet NP6 security processor."
    ::= { fgProcessorTypes 8 }

fgProcessorFnNP6LITE OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Fortinet NP6LITE security processor."
    ::= { fgProcessorTypes 9 }

fgProcessorFnNP7 OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Fortinet NP7 security processor."
    ::= { fgProcessorTypes 10 }

fgProcessorFnNP6XLITE OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor type identifier used for Fortinet NP6XLITE security processor."
    ::= { fgProcessorTypes 11 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgProcessors.fgProcessorsTrapObjects
--

fgProcessorsTrapObjects OBJECT IDENTIFIER
    ::= { fgProcessors 4 }

fgPerCpuHighDetails OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "CPUs index string that the CPU usage has exceeded the configured threshold."
    ::= { fgProcessorsTrapObjects 1 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgProcessorModules
--

fgProcessorModules OBJECT IDENTIFIER
    ::= { fgSystem 5 }

--
-- Registrations for processor module types, for use with fgProcModType
--
fgProcessorModuleTypes OBJECT IDENTIFIER
    ::= { fgProcessorModules 1 }

fgProcModOther OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor module type identifier used when no other defined
        type is appropriate."
    ::= { fgProcessorModuleTypes 1 }

fgProcModIntegrated OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor module type identifier used for the main CPUs
        built in the device."
    ::= { fgProcessorModuleTypes 2 }

fgProcModIntegratedNPU OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "The processor module type identifier used for the NPU(s)
        built in the device."
    ::= { fgProcessorModuleTypes 3 }

fgProcessorModuleCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgProcessorModuleTable"
    ::= { fgProcessorModules 2 }

fgProcessorModuleTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgProcessorModuleEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table that lists information for each processor module"
    ::= { fgProcessorModules 3 }

fgProcessorModuleEntry OBJECT-TYPE
    SYNTAX      FgProcessorModuleEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information for a particular processor module"
    INDEX       { fgProcModIndex }
    ::= { fgProcessorModuleTable 1 }


FgProcessorModuleEntry ::= SEQUENCE {
    fgProcModIndex          FnIndex,
    fgProcModType           AutonomousType,
    fgProcModName           DisplayString,
    fgProcModDescr          DisplayString,
    fgProcModProcessorCount Integer32,
    fgProcModMemCapacity    Gauge32,
    fgProcModMemUsage       Gauge32,
    fgProcModSessionCount   Gauge32,
    fgProcModSACount        Gauge32
}

fgProcModIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A unique identifier within the fgProcessorModuleTable"
    ::= { fgProcessorModuleEntry 1 }

fgProcModType OBJECT-TYPE
    SYNTAX     AutonomousType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "An indication of the type of the processor module.
        Types are defined in fgProcessorModuleTypes."
    ::= { fgProcessorModuleEntry 2 }

fgProcModName OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..32))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "A textual name of this processor module."
    ::= { fgProcessorModuleEntry 3 }

fgProcModDescr OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..64))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "A textual description of this processor module."
    ::= { fgProcessorModuleEntry 4 }

fgProcModProcessorCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total number of processors contained by this module."
    ::= { fgProcessorModuleEntry 5}

fgProcModMemCapacity OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total physical memory (RAM) installed (KB) on this processor module."
    ::= { fgProcessorModuleEntry 6 }

fgProcModMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current memory utilization (percentage) on this processor module."
    ::= { fgProcessorModuleEntry 7 }

fgProcModSessionCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of active sessions on this processor module
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorModuleEntry 8}

fgProcModSACount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of IPsec Security Associations on this processor module
         (only valid for processors types that support this statistic)."
    ::= { fgProcessorModuleEntry 9}

--
-- fortinet.fnFortiGateMib.fgSystem.fgSystemInfoAdvanced
--

fgSystemInfoAdvanced OBJECT IDENTIFIER
    ::= { fgSystem 6 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSystemInfoAdvanced.fgSysInfoAdvMem
--

fgSysInfoAdvMem OBJECT IDENTIFIER
    ::= { fgSystemInfoAdvanced 1 }

fgSIAdvMemPageCache OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The amount of physical RAM used as cache memory for files read from
         the disk (the page cache)."
    ::= { fgSysInfoAdvMem 1 }

fgSIAdvMemCacheActive OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The total amount of buffer or page cache memory that are active.
         This part of the memory is used recently and usually not reclaimed
         unless absolutely necessary."
    ::= { fgSysInfoAdvMem 2 }

fgSIAdvMemCacheInactive OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The total amount of buffer or page cache memory that are free and
         available. This is memory that has not been recently used and can be 
         reclaimed for other purposes by the paging algorithm."
    ::= { fgSysInfoAdvMem 3 }

fgSIAdvMemBuffer OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The amount of physical RAM used for filesystem buffers."
    ::= { fgSysInfoAdvMem 4 }

fgSIAdvMemEnterKerConsThrsh OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current memory threshold level to enter kernel conserve mode."
    ::= { fgSysInfoAdvMem 5 }

fgSIAdvMemLeaveKerConsThrsh OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current memory threshold level to leave kernel conserve mode."
    ::= { fgSysInfoAdvMem 6 }

fgSIAdvMemEnterProxyConsThrsh OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current memory threshold level to enter proxy conserve mode."
    ::= { fgSysInfoAdvMem 7 }

fgSIAdvMemLeaveProxyConsThrsh OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "KB"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current memory threshold level to leave proxy conserve mode."
    ::= { fgSysInfoAdvMem 8 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSystemInfoAdvanced.fgSysInfoAdvSessions
--

fgSysInfoAdvSessions OBJECT IDENTIFIER
    ::= { fgSystemInfoAdvanced 2 }

fgSIAdvSesEphemeralCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The current number of ephemeral sessions on the device."
    ::= { fgSysInfoAdvSessions 1 }

fgSIAdvSesEphemeralLimit OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The limit number of allowed ephemeral sessions on the device."
    ::= { fgSysInfoAdvSessions 2 }

fgSIAdvSesClashCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of new sessions which have collision with existing sessions.
         This generally highlights a shortage of ports or IP in ip-pool during
         source natting (PNAT)."
    ::= { fgSysInfoAdvSessions 3 }

fgSIAdvSesExpCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of current expectation sessions."
    ::= { fgSysInfoAdvSessions 4 }

fgSIAdvSesSyncQFCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The sync queue full counter, reflecting bursts on the sync queue."
    ::= { fgSysInfoAdvSessions 5 }

fgSIAdvSesAcceptQFCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The accept queue full counter, reflecting bursts on the accept queue."
    ::= { fgSysInfoAdvSessions 6 }

fgSIAdvSesNoListenerCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of direct requests to Fortigate local stack from external,
         reflecting DOS attack towards the Fortigate."
    ::= { fgSysInfoAdvSessions 7 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSystemInfoAdvanced.fgSIAdvLicenseDetails
--

fgSIAdvLicenseDetails OBJECT IDENTIFIER
    ::= { fgSystemInfoAdvanced 3 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSystemInfoAdvanced.fgSIAdvLicenseDetails.fgLicContracts
--

fgLicContracts OBJECT IDENTIFIER
    ::= { fgSIAdvLicenseDetails 1 }

fgLicContractCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgLicContractTable."
    ::= { fgLicContracts 1 }

fgLicContractTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgLicContractEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table that lists information for each contract."
    ::= { fgLicContracts 2 }

fgLicContractEntry OBJECT-TYPE
    SYNTAX      FgLicContractEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information for a particular contract."
    INDEX       { fgVdEntIndex }
    ::= { fgLicContractTable 1 }

FgLicContractEntry ::= SEQUENCE {
    fgLicContractDesc      DisplayString,
    fgLicContractExpiry    DisplayString
}

fgLicContractDesc OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The contract's description."
    ::= { fgLicContractEntry 1 }

fgLicContractExpiry OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The contract's expiry time."
    ::= { fgLicContractEntry 2 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSystemInfoAdvanced.fgSIAdvLicenseDetails.fgLicVersions
--

fgLicVersions OBJECT IDENTIFIER
    ::= { fgSIAdvLicenseDetails 2 }

fgLicVersionCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgLicVersionTable."
    ::= { fgLicVersions 1 }

fgLicVersionTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgLicVersionEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table that lists information for each version."
    ::= { fgLicVersions 2 }

fgLicVersionEntry OBJECT-TYPE
    SYNTAX      FgLicVersionEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information for a particular version."
    INDEX       { fgVdEntIndex }
    ::= { fgLicVersionTable 1 }

FgLicVersionEntry ::= SEQUENCE {
    fgLicVersionDesc      DisplayString,
    fgLicVersionExpiry    DisplayString,
    fgLicVersionNumber    DisplayString,
    fgLicVersionUpdTime   DisplayString,
    fgLicVersionUpdMethod DisplayString,
    fgLicVersionTryTime   DisplayString,
    fgLicVersionTryResult DisplayString
}

fgLicVersionDesc OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The version's description."
    ::= { fgLicVersionEntry 1 }

fgLicVersionExpiry OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The version's expiry time."
    ::= { fgLicVersionEntry 2 }

fgLicVersionNumber OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The version number."
    ::= { fgLicVersionEntry 3 }

fgLicVersionUpdTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The version's last update time."
    ::= { fgLicVersionEntry 4 }

fgLicVersionUpdMethod OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The version's last update method."
    ::= { fgLicVersionEntry 5 }

fgLicVersionTryTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The version's last attempt time."
    ::= { fgLicVersionEntry 6 }

fgLicVersionTryResult OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The version's last attempt result."
    ::= { fgLicVersionEntry 7 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgUsbports
--

fgUsbports OBJECT IDENTIFIER
    ::= { fgSystem 7 }

fgUsbportCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgUsbportTable."
    ::= { fgUsbports 1 }

fgUsbportTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgUsbportEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table that lists information for each external USB port."
    ::= { fgUsbports 2 }

fgUsbportEntry OBJECT-TYPE
    SYNTAX      FgUsbportEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information for a particular USB port."
    INDEX       { fgUsbportEntIndex }
    ::= { fgUsbportTable 1 }

FgUsbportEntry ::= SEQUENCE {
    fgUsbportEntIndex      FnIndex,
    fgUsbportPlugged       INTEGER,
    fgUsbportVersion       DisplayString,
    fgUsbportClass         INTEGER,
    fgUsbportVendId        OCTET STRING,
    fgUsbportProdId        OCTET STRING,
    fgUsbportRevision      DisplayString,
    fgUsbportManufacturer  DisplayString,
    fgUsbportProduct       DisplayString,
    fgUsbportSerial        DisplayString
}

fgUsbportEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A unique identifier within the fgUsbportTable."
    ::= { fgUsbportEntry 1 }

fgUsbportPlugged OBJECT-TYPE
    SYNTAX      INTEGER { unplugged(0), plugged(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The USB port's plugged status."
    ::= { fgUsbportEntry 2 }

fgUsbportVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The USB port's version."
    ::= { fgUsbportEntry 3 }

fgUsbportClass OBJECT-TYPE
    SYNTAX      INTEGER { ifc(0), audio(1), comm(2), hid(3), physical(5),
                  image(6), printer(7), storage(8), hub(9), cdcData(10),
                  chipSmartCard(11), contentSecurity(13), appSpec(254), vendorSpec(255)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The device class."
    ::= { fgUsbportEntry 4 }

fgUsbportVendId OBJECT-TYPE
    SYNTAX      OCTET STRING
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The Vendor ID of the device."
    ::= { fgUsbportEntry 5 }

fgUsbportProdId OBJECT-TYPE
    SYNTAX      OCTET STRING
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The Product ID of the device."
    ::= { fgUsbportEntry 6 }

fgUsbportRevision OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The release number of the device."
    ::= { fgUsbportEntry 7 }

fgUsbportManufacturer OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The manufacturer of the device."
    ::= { fgUsbportEntry 8 }

fgUsbportProduct OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The product of the device."
    ::= { fgUsbportEntry 9 }

fgUsbportSerial OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The serial number of the device."
    ::= { fgUsbportEntry 10 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgLinkMonitor
--
fgLinkMonitor OBJECT IDENTIFIER
    ::= { fgSystem 8 }

fgLinkMonitorNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of link monitor in fgLinkMonitorTable"
    ::= { fgLinkMonitor 1 }

fgLinkMonitorTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgLinkMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Link Monitor statistics table.  This table has a dependent expansion relationship with fgVdTable.
         Only virtual domains with enabled link monitor are present in this table."
    ::= { fgLinkMonitor 2 }

fgLinkMonitorEntry OBJECT-TYPE
    SYNTAX      FgLinkMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Link Monitor statistics on a virtual domain"
    INDEX       {fgLinkMonitorID}
    ::= { fgLinkMonitorTable 1 }

FgLinkMonitorEntry ::= SEQUENCE {
    fgLinkMonitorID         FnIndex,
    fgLinkMonitorName       DisplayString,
    fgLinkMonitorState      INTEGER,
    fgLinkMonitorLatency    DisplayString,
    fgLinkMonitorJitter     DisplayString,
    fgLinkMonitorPacketSend Counter64,
    fgLinkMonitorPacketRecv Counter64,
    fgLinkMonitorPacketLoss DisplayString,
    fgLinkMonitorVdom       DisplayString,
    fgLinkMonitorBandwidthIn  Counter32,
    fgLinkMonitorBandwidthOut Counter32,
    fgLinkMonitorBandwidthBi  Counter32,
    fgLinkMonitorOutofSeq     Counter64,
    fgLinkMonitorServer       DisplayString,
    fgLinkMonitorProtocol     DisplayString
}

fgLinkMonitorID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Link Monitor ID. Only enabled link monitor entries are present in this table. Link Monitor IDs are only unique within a virtual domain."
    ::= { fgLinkMonitorEntry 1 }

fgLinkMonitorName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Link Monitor name."
    ::= { fgLinkMonitorEntry 2 }

fgLinkMonitorState OBJECT-TYPE
    SYNTAX      INTEGER { alive(0), dead(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Link Monitor state."
    ::= { fgLinkMonitorEntry 3 }

fgLinkMonitorLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average latency of link monitor in float number within last 30 probes."
    ::= { fgLinkMonitorEntry 4 }

fgLinkMonitorJitter OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average jitter of link monitor in float number within last 30 probes."
    ::= { fgLinkMonitorEntry 5 }

fgLinkMonitorPacketSend OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The total number of packets sent by link monitor."
    ::= { fgLinkMonitorEntry 6 }

fgLinkMonitorPacketRecv OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The total number of packets received by link monitor."
    ::= { fgLinkMonitorEntry 7 }

fgLinkMonitorPacketLoss OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The average packet loss percentage in float number within last 30 probes."
    ::= { fgLinkMonitorEntry 8 }

fgLinkMonitorVdom OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virtual domain the link monitor entry exists in. This name corresponds to the fgVdEntName used in fgVdTable."
    ::= { fgLinkMonitorEntry 9 }

fgLinkMonitorBandwidthIn OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The available bandwidth in Mbps of incoming traffic detected by a link monitor on its interface."
    ::= { fgLinkMonitorEntry 10 }

fgLinkMonitorBandwidthOut OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The available bandwidth in Mbps of outgoing traffic detected by a link monitor on its interface."
    ::= { fgLinkMonitorEntry 11 }

fgLinkMonitorBandwidthBi OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "The available bandwidth in Mbps of bi-direction traffic detected by a link monitor on its interface."
    ::= { fgLinkMonitorEntry 12 }

fgLinkMonitorOutofSeq OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of out of sequence packets received."
    ::= { fgLinkMonitorEntry 13 }

fgLinkMonitorServer OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Link Monitor server."
    ::= { fgLinkMonitorEntry 14 }

fgLinkMonitorProtocol OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Link Monitor protocol."
    ::= { fgLinkMonitorEntry 15 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgVWLHealthCheckLink
--
fgVWLHealthCheckLink OBJECT IDENTIFIER
    ::= { fgSystem 9 }

fgVWLHealthCheckLinkNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of health check links in fgVWLHealthCheckLinkTable"
    ::= { fgVWLHealthCheckLink 1 }

fgVWLHealthCheckLinkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVWLHealthCheckLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Virtual-wan-link health check statistics table.  This table has a dependent expansion relationship with fgVdTable.
         Only health-checks with configured member link are present in this table."
    ::= { fgVWLHealthCheckLink 2 }

fgVWLHealthCheckLinkEntry OBJECT-TYPE
    SYNTAX      FgVWLHealthCheckLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Virtual-wan-link health check statistics on a virtual domain"
    INDEX       {fgVWLHealthCheckLinkID}
    ::= { fgVWLHealthCheckLinkTable 1 }

FgVWLHealthCheckLinkEntry ::= SEQUENCE {
    fgVWLHealthCheckLinkID               FnIndex,
    fgVWLHealthCheckLinkName             DisplayString,
    fgVWLHealthCheckLinkSeq              Gauge32,
    fgVWLHealthCheckLinkState            INTEGER,
    fgVWLHealthCheckLinkLatency          DisplayString,
    fgVWLHealthCheckLinkJitter           DisplayString,
    fgVWLHealthCheckLinkPacketSend       Counter64,
    fgVWLHealthCheckLinkPacketRecv       Counter64,
    fgVWLHealthCheckLinkPacketLoss       DisplayString,
    fgVWLHealthCheckLinkVdom             DisplayString,
    fgVWLHealthCheckLinkBandwidthIn      Counter32,
    fgVWLHealthCheckLinkBandwidthOut     Counter32,
    fgVWLHealthCheckLinkBandwidthBi      Counter32,
    fgVWLHealthCheckLinkIfName           DisplayString,
    fgVWLHealthCheckLinkUsedBandwidthIn  Counter32,
    fgVWLHealthCheckLinkUsedBandwidthOut Counter32,
    fgVWLHealthCheckLinkUsedBandwidthBi  Counter32,
    fgVWLHealthCheckLinkMOSCodec         DisplayString,
    fgVWLHealthCheckLinkMOS              DisplayString
}

fgVWLHealthCheckLinkID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Virtual-wan-link health-check link ID. Only health-checks with configured member link are present in this table. Virtuwal-wan-link health check link IDs are only unique within a virtual domain."
    ::= { fgVWLHealthCheckLinkEntry 1 }

fgVWLHealthCheckLinkName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Health check name."
    ::= { fgVWLHealthCheckLinkEntry 2 }

fgVWLHealthCheckLinkSeq OBJECT-TYPE
    SYNTAX      Gauge32(1..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Virtual-wan-link member link sequence."
    ::= { fgVWLHealthCheckLinkEntry 3 }

fgVWLHealthCheckLinkState OBJECT-TYPE
    SYNTAX      INTEGER { alive(0), dead(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Heatlth check state on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 4 }

fgVWLHealthCheckLinkLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average latency of a health check on a specific member link in float number within last 30 probes."
    ::= { fgVWLHealthCheckLinkEntry 5 }

fgVWLHealthCheckLinkJitter OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average jitter of a health check on a specific member link in float number within last 30 probes."
    ::= { fgVWLHealthCheckLinkEntry 6 }

fgVWLHealthCheckLinkPacketSend OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of packets sent by a health check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 7 }

fgVWLHealthCheckLinkPacketRecv OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of packets received by a health check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 8 }

fgVWLHealthCheckLinkPacketLoss OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The packet loss percentage of a health check on a specific member link in float number within last 30 probes."
    ::= { fgVWLHealthCheckLinkEntry 9 }

fgVWLHealthCheckLinkVdom OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Virtual domain the link monitor entry exists in. This name corresponds to the fgVdEntName used in fgVdTable."
    ::= { fgVWLHealthCheckLinkEntry 10 }

fgVWLHealthCheckLinkBandwidthIn OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The available bandwidth in kbps of incoming traffic detected by a health-check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 11 }

fgVWLHealthCheckLinkBandwidthOut OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The available bandwidth in kbps of outgoing traffic detected by a health-check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 12 }

fgVWLHealthCheckLinkBandwidthBi OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The available bandwidth in kbps of bi-direction traffic detected by a health-check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 13 }

fgVWLHealthCheckLinkIfName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Virtual-wan-link member interface name."
    ::= { fgVWLHealthCheckLinkEntry 14 }

fgVWLHealthCheckLinkUsedBandwidthIn OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The used bandwidth in kbps of incoming traffic detected by a health-check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 15 }

fgVWLHealthCheckLinkUsedBandwidthOut OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The used bandwidth in kbps of outgoing traffic detected by a health-check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 16 }

fgVWLHealthCheckLinkUsedBandwidthBi OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The used bandwidth in kbps of bi-direction traffic detected by a health-check on a specific member link."
    ::= { fgVWLHealthCheckLinkEntry 17 }

fgVWLHealthCheckLinkMOSCodec OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The codec used for Mean Opinion Score (MOS) calculation."
    ::= { fgVWLHealthCheckLinkEntry 18 }

fgVWLHealthCheckLinkMOS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Mean Opinion Score (MOS) in a health-check."
    ::= { fgVWLHealthCheckLinkEntry 19 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgDisks
--

fgDisks OBJECT IDENTIFIER
    ::= { fgSystem 10 }

fgDiskCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgDiskTable."
    ::= { fgDisks 1 }

fgDiskTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgDiskEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of disks."
    ::= { fgDisks 2 }

fgDiskEntry OBJECT-TYPE
    SYNTAX      FgDiskEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A disk entry containing the storage name."
    INDEX       { fgDiskIndex }
    ::= { fgDiskTable 1 }

FgDiskEntry ::= SEQUENCE {
    fgDiskIndex         FnIndex,
    fgDiskName          DisplayString
}

fgDiskIndex OBJECT-TYPE
    SYNTAX     FnIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The storage index."
    ::= { fgDiskEntry 1 }

fgDiskName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The name of the storage."
    ::= { fgDiskEntry 2 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgSlaProbeClient
--

fgSlaProbeClient OBJECT IDENTIFIER
    ::= {fgSystem 11}

fgSlaProbeClientNumber OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of sla probe clients in fgSlaProbeClientTable"
    ::= { fgSlaProbeClient 1 }

fgSlaProbeClientTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgSlaProbeClientEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Server Probe client statistics table. This table is global.
         All server probe clients are present in this table. Currently, twamp clients only."
    ::= { fgSlaProbeClient 2 }

fgSlaProbeClientEntry OBJECT-TYPE
    SYNTAX      FgSlaProbeClientEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Server Probe client statistics."
    INDEX       {fgSlaProbeClientID}
    ::= { fgSlaProbeClientTable 1 }

FgSlaProbeClientEntry ::= SEQUENCE {
    fgSlaProbeClientID            FnIndex,
    fgSlaProbeClientIP            IpAddress,
    fgSlaProbeClientState         INTEGER,
    fgSlaProbeClientAvgLatency    DisplayString,
    fgSlaProbeClientAvgLatencySD  DisplayString,
    fgSlaProbeClientAvgLatencyDS  DisplayString,
    fgSlaProbeClientMinLatency    DisplayString,
    fgSlaProbeClientMinLatencySD  DisplayString,
    fgSlaProbeClientMinLatencyDS  DisplayString,
    fgSlaProbeClientMaxLatency    DisplayString,
    fgSlaProbeClientMaxLatencySD  DisplayString,
    fgSlaProbeClientMaxLatencyDS  DisplayString,
    fgSlaProbeClientAvgJitter     DisplayString,
    fgSlaProbeClientAvgJitterSD   DisplayString,
    fgSlaProbeClientAvgJitterDS   DisplayString,
    fgSlaProbeClientMinJitter     DisplayString,
    fgSlaProbeClientMinJitterSD   DisplayString,
    fgSlaProbeClientMinJitterDS   DisplayString,
    fgSlaProbeClientMaxJitter     DisplayString,
    fgSlaProbeClientMaxJitterSD   DisplayString,
    fgSlaProbeClientMaxJitterDS   DisplayString,
    fgSlaProbeClientPktloss       DisplayString,
    fgSlaProbeClientPktlossSD     DisplayString,
    fgSlaProbeClientPktlossDS     DisplayString,
    fgSlaProbeClientOutofSeq      Counter64,
    fgSlaProbeClientOutofSeqSD    Counter64,
    fgSlaProbeClientOutofSeqDS    Counter64
}

fgSlaProbeClientID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Server Probe client ID."
    ::= { fgSlaProbeClientEntry 1 }

fgSlaProbeClientIP OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP address of server probe client."
    ::= { fgSlaProbeClientEntry 2 }

fgSlaProbeClientState OBJECT-TYPE
    SYNTAX      INTEGER { alive(0), dead(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Server Probe client state."
    ::= { fgSlaProbeClientEntry 3 }

fgSlaProbeClientAvgLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average latency of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 4 }

fgSlaProbeClientAvgLatencySD OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average latency from source to destination of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 5 }

fgSlaProbeClientAvgLatencyDS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average latency from destination to source of server probe in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 6 }

fgSlaProbeClientMinLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimal latency of server probe client in float number within last 30 probes."
    ::= {  fgSlaProbeClientEntry 7 }

fgSlaProbeClientMinLatencySD OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimal latency from source to destination of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 8 }

fgSlaProbeClientMinLatencyDS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimal latency from destination to source of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 9 }

fgSlaProbeClientMaxLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum latency of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 10 }

fgSlaProbeClientMaxLatencySD OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum latency from source to destination of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 11 }

fgSlaProbeClientMaxLatencyDS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum latency from destination to source of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 12 }

fgSlaProbeClientAvgJitter OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average jitter of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 13 }

fgSlaProbeClientAvgJitterSD OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average jitter from source to destination of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 14 }

fgSlaProbeClientAvgJitterDS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average jitter from destination to source of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 15 }

fgSlaProbeClientMinJitter OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimal jitter of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 16 }

fgSlaProbeClientMinJitterSD OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimal jitter from source to destination of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 17 }

fgSlaProbeClientMinJitterDS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimal jitter from destination to source of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 18 }

fgSlaProbeClientMaxJitter OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum jitter of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 19 }

fgSlaProbeClientMaxJitterSD OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum jitter from source to destination of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 20 }

fgSlaProbeClientMaxJitterDS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum jitter from destination to source of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 21 }

fgSlaProbeClientPktloss OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average packet loss percentage of server probe client in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 22 }

fgSlaProbeClientPktlossSD OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average packet loss percentage of server probe client from source to destination in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 23 }

fgSlaProbeClientPktlossDS OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average packet loss percentage of server probe client from destionation to source in float number within last 30 probes."
    ::= { fgSlaProbeClientEntry 24 }

fgSlaProbeClientOutofSeq OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of packets out of sequence order received by server probe client."
    ::= { fgSlaProbeClientEntry 25 }

fgSlaProbeClientOutofSeqSD OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of packets out of sequence order received by server probe client from source to destination."
    ::= { fgSlaProbeClientEntry 26 }

fgSlaProbeClientOutofSeqDS OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of packets out of sequence order received by server probe client from destination to source."
    ::= { fgSlaProbeClientEntry 27 }

--
-- fortinet.fnFortiGateMib.fgSystem.fgDpdkEngs
--

fgDpdkEngs OBJECT IDENTIFIER
    ::= {fgSystem 12}

fgDpdkEngCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of entries in fgDpdkEngTable"
    ::= { fgDpdkEngs 1 }

fgDpdkEngTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgDpdkEngEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table that lists information for each dpdk engine"
    ::= { fgDpdkEngs 2 }

fgDpdkEngEntry OBJECT-TYPE
    SYNTAX      FgDpdkEngEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry containing information for a particular dpdk engine"
    INDEX       { fgDpdkEngEntIndex }
    ::= { fgDpdkEngTable 1 }

FgDpdkEngEntry ::= SEQUENCE {
    fgDpdkEngEntIndex    FnIndex,
    fgDpdkEngRxUsage     Gauge32,
    fgDpdkEngVnpUsage    Gauge32,
    fgDpdkEngIpsUsage    Gauge32,
    fgDpdkEngTxUsage     Gauge32,
    fgDpdkEngIdle        Gauge32,
    fgDpdkEngToCpu       Integer32
}

fgDpdkEngEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A unique identifier within the fgDpdkEngTable"
    ::= { fgDpdkEngEntry 1 }

fgDpdkEngRxUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The DpdkEng's Rx usage (percentage)."
    ::= { fgDpdkEngEntry 2 }

fgDpdkEngVnpUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The DpdkEng's Vnp usage (percentage)."
    ::= { fgDpdkEngEntry 3 }

fgDpdkEngIpsUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The DpdkEng's Ips usage (percentage)."
    ::= { fgDpdkEngEntry 4 }

fgDpdkEngTxUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The DpdkEng's Tx usage (percentage)."
    ::= { fgDpdkEngEntry 5 }

fgDpdkEngIdle OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The DpdkEng's Idle usage (percentage)."
    ::= { fgDpdkEngEntry 6 }

fgDpdkEngToCpu OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Dpdk eng index to Cpu Mapping"
    ::= { fgDpdkEngEntry 7 }


--
-- fortinet.fnFortiGateMib.fgFirewall
--

fgFirewall OBJECT IDENTIFIER
    ::= { fnFortiGateMib 5 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwPolicies
--

fgFwPolicies OBJECT IDENTIFIER
    ::= { fgFirewall 1 }

fgFwPolInfo OBJECT IDENTIFIER
    ::= { fgFwPolicies 1 }

fgFwPolTables OBJECT IDENTIFIER
    ::= { fgFwPolicies 2 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwPolicies.fgFwPolTables.fgFwPolStatsTable
--

fgFwPolStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwPolStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Firewall policy statistics table.  This table has a dependent expansion relationship with fgVdTable.
         Only virtual domains with enabled policies are present in this table."
    ::= { fgFwPolTables 1 }

fgFwPolStatsEntry OBJECT-TYPE
    SYNTAX      FgFwPolStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Firewall policy statistics on a virtual domain"
    INDEX       { fgVdEntIndex, fgFwPolID }
    ::= { fgFwPolStatsTable 1 }

FgFwPolStatsEntry ::= SEQUENCE {
    fgFwPolID               FnIndex,
    fgFwPolPktCount         Counter32,
    fgFwPolByteCount        Counter32,
    fgFwPolLastUsed         DisplayString,
    fgFwPolPktCountHc       Counter64,
    fgFwPolByteCountHc      Counter64
}

fgFwPolID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Firewall policy ID. Only enabled policies are present in this table. Policy IDs are only unique within a virtual domain."
    ::= { fgFwPolStatsEntry 1 }

fgFwPolPktCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of packets matched to policy (passed or blocked, depending on policy action). Count is from the time the policy became active."
    ::= { fgFwPolStatsEntry 2 }

fgFwPolByteCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes in packets matching the policy. See fgFwPolPktCount."
    ::= { fgFwPolStatsEntry 3 }

fgFwPolLastUsed OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "How many minutes since last used."
    ::= { fgFwPolStatsEntry 4 }

fgFwPolPktCountHc OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "64-bit Number of packets matched to policy (passed or blocked, depending on policy action). Count is from the time the policy became active."
    ::= { fgFwPolStatsEntry 5 }

fgFwPolByteCountHc OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "64-bit Number of bytes in packets matching the policy. See fgFwPolPktCountHc."
    ::= { fgFwPolStatsEntry 6 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwPolicies.fgFwPolTables.fgFwHsPolStatsTable
--

fgFwHsPolStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwHsPolStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Firewall hyperscale-policy statistics table.  This table has a dependent
         expansion relationship with fgVdTable.
         Only virtual domains with enabled policies are present in this table."
    ::= { fgFwPolTables 3 }

fgFwHsPolStatsEntry OBJECT-TYPE
    SYNTAX      FgFwHsPolStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Firewall hyperscale-policy statistics on a virtual domain"
    INDEX       { fgVdEntIndex, fgFwHsPolID }
    ::= { fgFwHsPolStatsTable 1 }

FgFwHsPolStatsEntry ::= SEQUENCE {
    fgFwHsPolID         FnIndex,
    fgFwHsPolPktCount   Counter64,
    fgFwHsPolByteCount  Counter64,
    fgFwHsPolLastUsed   DisplayString
}

fgFwHsPolID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Firewall hyperscale-policy ID. Only enabled policies are present in this table.
         Policy IDs are only unique within a virtual domain."
    ::= { fgFwHsPolStatsEntry 1 }

fgFwHsPolPktCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of packets matched to policy (passed or blocked, depending
         on policy action). Count is from the time the policy became active."
    ::= { fgFwHsPolStatsEntry 2 }

fgFwHsPolByteCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes in packets matching the policy. See fgFwHsPolPktCount."
    ::= { fgFwHsPolStatsEntry 3 }

fgFwHsPolLastUsed OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "How many minutes since last used."
    ::= { fgFwHsPolStatsEntry 4 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwUsers
--

fgFwUsers OBJECT IDENTIFIER
    ::= { fgFirewall 2 }

fgFwUserInfo OBJECT IDENTIFIER
    ::= { fgFwUsers 1 }

fgFwUserNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of user accounts in fgFwUserTable"
    ::= { fgFwUserInfo 1 }

fgFwUserAuthTimeout OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Idle period after which a firewall-authentication user's session
         is automatically expired"
    ::= { fgFwUserInfo 2 }

fgFwUserTables OBJECT IDENTIFIER
    ::= { fgFwUsers 2 }

fgFwUserTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwUserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of local and proxy (Radius server)
         user accounts for use with firewall user authentication"
    ::= { fgFwUserTables 1 }

fgFwUserEntry OBJECT-TYPE
    SYNTAX      FgFwUserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to a particular user account"
    INDEX       { fgFwUserIndex }
    ::= { fgFwUserTable 1 }

FgFwUserEntry ::= SEQUENCE {
    fgFwUserIndex   FnIndex,
    fgFwUserName    DisplayString,
    fgFwUserAuth    FgFwUserAuthType,
    fgFwUserState   FnBoolState,
    fgFwUserVdom    FgVdIndex
}

fgFwUserIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index for uniquely identifying the users in fgFwUserTable"
    ::= { fgFwUserEntry 1 }

fgFwUserName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "User-name of the specified account"
    ::= { fgFwUserEntry 2 }

fgFwUserAuth OBJECT-TYPE
    SYNTAX      FgFwUserAuthType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Type of authentication the account uses (local, RADIUS, LDAP, etc.)"
    ::= { fgFwUserEntry 3 }

fgFwUserState OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Status of the user account (enable/disable)"
    ::= { fgFwUserEntry 4 }

fgFwUserVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virtual domain the user account exists in. This index corresponds to the index used in fgVdTable."
    ::= { fgFwUserEntry 5 }

fgFwAuthUserTables OBJECT IDENTIFIER
    ::= { fgFwUsers 3 }

fgFwAuthUserInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwAuthUserInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of authenticated users information for each virtual domain configured on the device."
    ::= { fgFwAuthUserTables 1 }

fgFwAuthUserInfoEntry OBJECT-TYPE
    SYNTAX      FgFwAuthUserInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry containing information applicable
         to a virtual domain that have authenticated users."
    INDEX       { fgFwAuthUserInfoVdom }
    ::= { fgFwAuthUserInfoTable 1 }

FgFwAuthUserInfoEntry ::= SEQUENCE {
    fgFwAuthUserInfoVdom        FgVdIndex,
    fgFwAuthIpv4UserNumber      Integer32,
    fgFwAuthIpv6UserNumber      Integer32
}

fgFwAuthUserInfoVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Virtual domain index that uniquely identify rows in this table."
    ::= { fgFwAuthUserInfoEntry 1 }

fgFwAuthIpv4UserNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of authenticated IPv4 users in this virtual domain."
    ::= { fgFwAuthUserInfoEntry 2 }

fgFwAuthIpv6UserNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of authenticated IPv6 users in this virtual domain."
    ::= { fgFwAuthUserInfoEntry 3 }

fgFwAuthIpv4UserTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwAuthIpv4UserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of authenticated IPv4 users."
    ::= { fgFwAuthUserTables 2 }

fgFwAuthIpv4UserEntry OBJECT-TYPE
    SYNTAX      FgFwAuthIpv4UserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry containing information applicable
         to an authenticated IPv4 user."
    INDEX       { fgFwAuthIpv4UserIndex }
    ::= { fgFwAuthIpv4UserTable 1 }

FgFwAuthIpv4UserEntry ::= SEQUENCE {
    fgFwAuthIpv4UserIndex   FnIndex,
    fgFwAuthIpv4UserVdom    FgVdIndex,
    fgFwAuthIpv4UserName    DisplayString,
    fgFwAuthIpv4UserType    FgFwAuthUserType,
    fgFwAuthIpv4UserAddr    IpAddress
}

fgFwAuthIpv4UserIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An index for uniquely identifying the users in fgFwAuthIpv4UserTable."
    ::= { fgFwAuthIpv4UserEntry 1 }

fgFwAuthIpv4UserVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Virtual domain the authenticated user exists in. This index corresponds to the index used in fgVdTable."
    ::= { fgFwAuthIpv4UserEntry 2 }

fgFwAuthIpv4UserName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User-name of the authenticated user."
    ::= { fgFwAuthIpv4UserEntry 3 }

fgFwAuthIpv4UserType OBJECT-TYPE
    SYNTAX      FgFwAuthUserType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of the authenticated user."
    ::= { fgFwAuthIpv4UserEntry 4 }

fgFwAuthIpv4UserAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv4 address of the authenticated user."
    ::= { fgFwAuthIpv4UserEntry 5 }

fgFwAuthIpv6UserTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwAuthIpv6UserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of authenticated IPv6 users."
    ::= { fgFwAuthUserTables 3 }

fgFwAuthIpv6UserEntry OBJECT-TYPE
    SYNTAX      FgFwAuthIpv6UserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry containing information applicable
         to an authenticated IPv6 user."
    INDEX       { fgFwAuthIpv6UserIndex }
    ::= { fgFwAuthIpv6UserTable 1 }

FgFwAuthIpv6UserEntry ::= SEQUENCE {
    fgFwAuthIpv6UserIndex   FnIndex,
    fgFwAuthIpv6UserVdom    FgVdIndex,
    fgFwAuthIpv6UserName    DisplayString,
    fgFwAuthIpv6UserType    FgFwAuthUserType,
    fgFwAuthIpv6UserAddr    Ipv6Address
}

fgFwAuthIpv6UserIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An index for uniquely identifying the users in fgFwAuthIpv4UserTable."
    ::= { fgFwAuthIpv6UserEntry 1 }

fgFwAuthIpv6UserVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Virtual domain the authenticated user exists in. This index corresponds to the index used in fgVdTable."
    ::= { fgFwAuthIpv6UserEntry 2 }

fgFwAuthIpv6UserName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User-name of the authenticated user."
    ::= { fgFwAuthIpv6UserEntry 3 }

fgFwAuthIpv6UserType OBJECT-TYPE
    SYNTAX      FgFwAuthUserType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of the authenticated user."
    ::= { fgFwAuthIpv6UserEntry 4 }

fgFwAuthIpv6UserAddr OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv6 address of the authenticated user."
    ::= { fgFwAuthIpv6UserEntry 5 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwIppools
--

fgFwIppools OBJECT IDENTIFIER
    ::= { fgFirewall 3 }

fgFwIppTables OBJECT IDENTIFIER
    ::= { fgFwIppools 2 }

fgFwIppStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwIppStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Firewall ippool statistics table."
    ::= { fgFwIppTables 1 }

fgFwIppStatsEntry OBJECT-TYPE
    SYNTAX      FgFwIppStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing an ippool statistics."
    INDEX       { fgFwIppStatsStartIp, fgFwIppStatsEndIp }
    ::= { fgFwIppStatsTable 1 }

FgFwIppStatsEntry ::= SEQUENCE {
    fgFwIppStatsName          DisplayString,
    fgFwIppStatsType          DisplayString,
    fgFwIppStatsStartIp       IpAddress,
    fgFwIppStatsEndIp         IpAddress,
    fgFwIppStatsTotalSessions Gauge32,
    fgFwIppStatsTcpSessions   Gauge32,
    fgFwIppStatsUdpSessions   Gauge32,
    fgFwIppStatsOtherSessions Gauge32,
    fgFwIppStatsTotalPBAs     Gauge32,
    fgFwIppStatsInusePBAs     Gauge32,
    fgFwIppStatsExpiringPBAs  Gauge32,
    fgFwIppStatsFreePBAs      Gauge32,
    fgFwIppStatsFlags         DisplayString,
    fgFwIppStatsGroupName     DisplayString,
    fgFwIppStatsBlockSize     Gauge32,
    fgFwIppStatsPortStart     InetPortNumber,
    fgFwIppStatsPortEnd       InetPortNumber,
    fgFwIppStatsStartClientIP IpAddress,
    fgFwIppStatsEndClientIP   IpAddress,
    fgFwIppStatsRscTCP        Gauge32,
    fgFwIppStatsRscUDP        Gauge32,
    fgFwIppStatsUsedRscTCP    Gauge32,
    fgFwIppStatsUsedRscUDP    Gauge32,
    fgFwIppStatsPercentageTCP Gauge32,
    fgFwIppStatsPercentageUDP Gauge32
}

fgFwIppStatsName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Name of the ippool."
    ::= { fgFwIppStatsEntry 1 }

fgFwIppStatsType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Type of the ippool."
    ::= { fgFwIppStatsEntry 2 }

fgFwIppStatsStartIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Startip of the ippool."
    ::= { fgFwIppStatsEntry 3 }

fgFwIppStatsEndIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Endip of the ippool."
    ::= { fgFwIppStatsEntry 4 }

fgFwIppStatsTotalSessions OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total session number of the ippool."
    ::= { fgFwIppStatsEntry 5 }

fgFwIppStatsTcpSessions OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tcp session number of the ippool."
    ::= { fgFwIppStatsEntry 6 }

fgFwIppStatsUdpSessions OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Udp session number of the ippool."
    ::= { fgFwIppStatsEntry 7 }

fgFwIppStatsOtherSessions OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Other session number of the ippool."
    ::= { fgFwIppStatsEntry 8 }

fgFwIppStatsTotalPBAs OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total number of the PBA."
    ::= { fgFwIppStatsEntry 9 }

fgFwIppStatsInusePBAs OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the PBA in use."
    ::= { fgFwIppStatsEntry 10 }

fgFwIppStatsExpiringPBAs OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the expiring PBA."
    ::= { fgFwIppStatsEntry 11 }

fgFwIppStatsFreePBAs OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The free PBA (percentage)."
    ::= { fgFwIppStatsEntry 12 }

fgFwIppStatsFlags OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Flags of the pool."
    ::= { fgFwIppStatsEntry 13 }

fgFwIppStatsGroupName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The pool group name if it is in an IP pool group."
    ::= { fgFwIppStatsEntry 14 }

fgFwIppStatsBlockSize OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "PBA block size."
    ::= { fgFwIppStatsEntry 15 }

fgFwIppStatsPortStart OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Start port."
    ::= { fgFwIppStatsEntry 16 }

fgFwIppStatsPortEnd OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End port."
    ::= { fgFwIppStatsEntry 17 }

fgFwIppStatsStartClientIP OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The start IP of the client address."
    ::= { fgFwIppStatsEntry 18 }

fgFwIppStatsEndClientIP OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The end IP of the client address."
    ::= { fgFwIppStatsEntry 19 }

fgFwIppStatsRscTCP OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total resource (port or block) for TCP."
    ::= { fgFwIppStatsEntry 20 }

fgFwIppStatsRscUDP OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total resource (port or block) for UDP."
    ::= { fgFwIppStatsEntry 21 }

fgFwIppStatsUsedRscTCP OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Used resource for TCP."
    ::= { fgFwIppStatsEntry 22 }

fgFwIppStatsUsedRscUDP OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Used resource for UDP."
    ::= { fgFwIppStatsEntry 23 }

fgFwIppStatsPercentageTCP OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Percentage used for TCP."
    ::= { fgFwIppStatsEntry 24 }

fgFwIppStatsPercentageUDP OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Percentage used for UDP."
    ::= { fgFwIppStatsEntry 25 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwIppools.fgFwIppTrapObjects
--

fgFwIppTrapObjects OBJECT IDENTIFIER
    ::= { fgFwIppools 3 }

fgFwIppTrapType OBJECT-TYPE
    SYNTAX      INTEGER {
                  raise(1),
                  clear(2)
                }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Pool usage trap type, there are 2 different values as below:
    	  raise
          clear "
    ::= { fgFwIppTrapObjects 1 }

fgFwTrapPoolUtilization OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Pool Usage percent."
    ::= { fgFwIppTrapObjects 2 }

fgFwIppTrapPoolProto OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Pool usage protocol udp/tcp."
    ::= { fgFwIppTrapObjects 3 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwGtp
--

fgFwGtp OBJECT IDENTIFIER
    ::= { fgFirewall 4 }

fgFwGtpStats OBJECT IDENTIFIER
    ::= { fgFwGtp 1 }

fgFwGtpStatsRequest OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of request in firewall gtp statistics."
    ::= { fgFwGtpStats 1 }

fgFwGtpStatsEchoRequest OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of echo_request in firewall gtp statistics."
    ::= { fgFwGtpStats 2 }

fgFwGtpStatsTunnel OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of tunnel in firewall gtp statistics."
    ::= { fgFwGtpStats 3 }

fgFwGtpStatsTunnelV0 OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of tunnel_v0 in firewall gtp statistics."
    ::= { fgFwGtpStats 4 }

fgFwGtpStatsPath OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of path in firewall gtp statistics."
    ::= { fgFwGtpStats 5 }

fgFwGtpStatsBearer OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bearer in firewall gtp statistics."
    ::= { fgFwGtpStats 6 }

fgFwGtpStatsFteid OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of fteid in firewall gtp statistics."
    ::= { fgFwGtpStats 7 }

fgFwGtpStatsProfile OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of profile in firewall gtp statistics."
    ::= { fgFwGtpStats 8 }

fgFwGtpStatsImsi OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of imsi in firewall gtp statistics."
    ::= { fgFwGtpStats 9 }

fgFwGtpStatsApn OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of apn in firewall gtp statistics."
    ::= { fgFwGtpStats 10 }

fgFwGtpStatsApnShaper OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of apn_shaper in firewall gtp statistics."
    ::= { fgFwGtpStats 11 }

fgFwGtpStatsTunnelLimiter OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of tunnel_limiter in firewall gtp statistics."
    ::= { fgFwGtpStats 12 }

fgFwGtpStatsAdvPolicies OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of adv_policy in firewall gtp statistics."
    ::= { fgFwGtpStats 13 }

fgFwGtpStatsIeRemovePolicies OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of ie_remove_policy in firewall gtp statistics."
    ::= { fgFwGtpStats 14 }

fgFwGtpStatsIpPolicy OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of ip_policy in firewall gtp statistics."
    ::= { fgFwGtpStats 15 }

fgFwGtpStatsNoipPolicy OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of noip_policy in firewall gtp statistics."
    ::= { fgFwGtpStats 16 }

fgFwGtpStatsIeWlEntry OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of ie_wl_entry in firewall gtp statistics."
    ::= { fgFwGtpStats 17 }

fgFwGtpStatsClash OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of clash in firewall gtp statistics."
    ::= { fgFwGtpStats 18 }

fgFwGtpStatsDrop OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of drop in firewall gtp statistics."
    ::= { fgFwGtpStats 19 }

fgFwGtpRtStats OBJECT IDENTIFIER
    ::= { fgFwGtp 2 }

fgFwGtpRtStatsCPkts OBJECT IDENTIFIER
    ::= { fgFwGtpRtStats 1 }

fgFwGtpRtStatsCForwarded OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet forwarded in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 1 }

fgFwGtpRtStatsCRejected OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet rejected in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 2 }

fgFwGtpRtStatsCDropped0 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by unknown in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 3 }

fgFwGtpRtStatsCDropped1 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by sanity in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 4 }

fgFwGtpRtStatsCDropped2 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by reserved-field in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 5 }

fgFwGtpRtStatsCDropped3 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by msg-reserved in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 6 }

fgFwGtpRtStatsCDropped4 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by msg-out-state in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 7 }

fgFwGtpRtStatsCDropped5 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by ie-reserved in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 8 }

fgFwGtpRtStatsCDropped6 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by ie-out-state in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 9 }

fgFwGtpRtStatsCDropped7 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by msg-length in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 10 }

fgFwGtpRtStatsCDropped8 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by ie-length in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 11 }

fgFwGtpRtStatsCDropped9 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by mandatory-ie in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 12 }

fgFwGtpRtStatsCDropped10 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by ip-policy in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 13 }

fgFwGtpRtStatsCDropped11 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by noip-policy in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 14 }

fgFwGtpRtStatsCDropped12 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by sgsn-auth in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 15 }

fgFwGtpRtStatsCDropped13 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by sgsn-handover in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 16 }

fgFwGtpRtStatsCDropped14 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by ggsn-auth in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 17 }

fgFwGtpRtStatsCDropped15 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by seq-num in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 18 }

fgFwGtpRtStatsCDropped16 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by msg-filter in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 19 }

fgFwGtpRtStatsCDropped17 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by apn-filter in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 20 }

fgFwGtpRtStatsCDropped18 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by adv-policy-filter in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 21 }

fgFwGtpRtStatsCDropped19 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by imsi-filter in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 22 }

fgFwGtpRtStatsCDropped20 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by rate-limited in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 23 }

fgFwGtpRtStatsCDropped21 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by tunnel-limited in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 24 }

fgFwGtpRtStatsCDropped22 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by invalid-state in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 25 }

fgFwGtpRtStatsCDropped23 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of control packet dropped by unknown-gtp-version in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsCPkts 26 }

fgFwGtpRtStatsDPkts OBJECT IDENTIFIER
    ::= { fgFwGtpRtStats 2 }

fgFwGtpRtStatsDForwarded OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet forwarded in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 1 }

fgFwGtpRtStatsDDroppedSanity OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by sanity in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 2 }

fgFwGtpRtStatsDDroppedMalMsg OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by mal-msg in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 3 }

fgFwGtpRtStatsDDroppedNoState OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by no-state in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 4 }

fgFwGtpRtStatsDDroppedMalIe OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by mal-ie in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 5 }

fgFwGtpRtStatsDDroppedGtpInGtp OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by gtp-in-gtp in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 6 }

fgFwGtpRtStatsDDroppedSpoof OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by spoof in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 7 }

fgFwGtpRtStatsDDroppedIpPol OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by ip-policy in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 8 }

fgFwGtpRtStatsDDroppedMsgFilter OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by msg-filter in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 9 }

fgFwGtpRtStatsDDroppedMsgRateLimit OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by msg-rate-limit in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 10 }

fgFwGtpRtStatsDDroppedUnknownVersion OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of data packet dropped by unknown-gtp-version in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsDPkts 11 }

fgFwGtpRtStatsBPkts OBJECT IDENTIFIER
    ::= { fgFwGtpRtStats 3 }

fgFwGtpRtStatsBForwarded OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of billing packet forwarded in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsBPkts 1 }

fgFwGtpRtStatsBDroppedSanity OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of billing packet dropped by sanity in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsBPkts 2 }

fgFwGtpRtStatsBDroppedMalMsg OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of billing packet dropped by mal-msg in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsBPkts 3 }

fgFwGtpRtStatsBDroppedMalIe OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of billing packet dropped by mal-ie in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsBPkts 4 }

fgFwGtpRtStatsBDroppedMsgFilter OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of billing packet dropped by msg-filter in firewall gtp runtime statistics."
    ::= { fgFwGtpRtStatsBPkts 5 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwAddresses
--

fgFwAddresses OBJECT IDENTIFIER
    ::= { fgFirewall 5 }

fgFwAddrTables OBJECT IDENTIFIER
    ::= { fgFwAddresses 2 }

--
-- fortinet.fnFortiGateMib.fgFirewall.fgFwAddresses.fgFwAddrTables.fgFwAddrDynEmsTable
--

fgFwAddrDynEmsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFwAddrDynEmsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Firewall dynamic address table with ems-tag."
    ::= { fgFwAddrTables 1 }

fgFwAddrDynEmsEntry OBJECT-TYPE
    SYNTAX      FgFwAddrDynEmsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Firewall dynamic address with ems-tag on a virtual domain"
    INDEX       { fgVdEntIndex, fgFwAddrDynEmsID }
    ::= { fgFwAddrDynEmsTable 1 }

FgFwAddrDynEmsEntry ::= SEQUENCE {
    fgFwAddrDynEmsID           FnIndex,
    fgFwAddrDynEmsName         DisplayString,
    fgFwAddrDynEmsAddresses    Unsigned32
}

fgFwAddrDynEmsID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "ID of firewall dynamic address."
    ::= { fgFwAddrDynEmsEntry 1 }

fgFwAddrDynEmsName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Name of firewall dynamic address."
    ::= { fgFwAddrDynEmsEntry 2 }

fgFwAddrDynEmsAddresses OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Addresses of firewall dynamic address."
    ::= { fgFwAddrDynEmsEntry 3 }

--
-- fortinet.fnFortiGateMib.fgMgmt
--

fgMgmt OBJECT IDENTIFIER
    ::= { fnFortiGateMib 6 }

fgFmTrapPrefix OBJECT IDENTIFIER
    ::= { fgMgmt 0 }

fgAdmin OBJECT IDENTIFIER
    ::= { fgMgmt 1 }

--
-- fortinet.fnFortiGateMib.fgMgmt.fgAdmin.fgAdminOptions
--

fgAdminOptions OBJECT IDENTIFIER
    ::= { fgAdmin 1 }

fgAdminIdleTimeout OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Idle period after which an administrator
         is automatically logged out of the system"
    ::= { fgAdminOptions 1 }

fgAdminLcdProtection OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Status of the LCD protection (enabled/disabled)"
    ::= { fgAdminOptions 2 }

--
-- fortinet.fnFortiGateMib.fgMgmt.fgAdmin.fgAdminTables
--

fgAdminTables OBJECT IDENTIFIER
    ::= { fgAdmin 2 }

fgAdminTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgAdminEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of administrator accounts on the device"
    ::= { fgAdminTables 1 }

fgAdminEntry OBJECT-TYPE
    SYNTAX      FgAdminEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to a particular admin account"
    AUGMENTS    { fnAdminEntry }
    ::= { fgAdminTable 1 }

FgAdminEntry ::= SEQUENCE {
    fgAdminVdom     FgVdIndex
}

fgAdminVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The virtual domain the administrator belongs to"
    ::= { fgAdminEntry 1 }

--
-- fortinet.fnFortiGateMib.fgMgmt.fgAdmin.fgMgmtTrapObjects
--

fgMgmtTrapObjects OBJECT IDENTIFIER
    ::= { fgMgmt 2 }

fgManIfIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "IP address of the interface listed in the trap"
    ::= { fgMgmtTrapObjects 1 }

fgManIfMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Mask of subnet the interface belongs to"
    ::= { fgMgmtTrapObjects 2 }

fgManIfIp6 OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "IPv6 address of the interface listed in the trap"
    ::= { fgMgmtTrapObjects 3 }

fgFazTrapType OBJECT-TYPE
    SYNTAX      INTEGER {
                  mainFailover(1),
                  altFailover(2)
                }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Fortianalyzer trap type, there are 2 different values as below:
           mainFailover(1)    - Fortianalyzer main server failover and alternate server take over.
           altFailover(2)     - Fortianalyzer alternate server failover and main server take over."
    ::= { fgMgmtTrapObjects 4 }

--
-- fortinet.fnFortiGateMib.fgIntf
--

fgIntf OBJECT IDENTIFIER
    ::= { fnFortiGateMib 7 }

fgIntfInfo OBJECT IDENTIFIER
    ::= { fgIntf 1 }

fgIntfTables OBJECT IDENTIFIER
    ::= { fgIntf 2 }

fgIntfTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Fortinet specific extensions to MIB-2 ifTable"
    ::= { fgIntfTables 1 }

fgIntfEntry OBJECT-TYPE
    SYNTAX      FgIntfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Fortinet specific information about an ifEntry. This table augments the standard ifTable, so the same indexing is used."
    AUGMENTS    { ifEntry }
    ::= { fgIntfTable 1 }

FgIntfEntry ::= SEQUENCE {
    fgIntfEntVdom             FgVdIndex,
    fgIntfEntEstUpBandwidth   Unsigned32,
    fgIntfEntEstDownBandwidth Unsigned32,
    fgIntfEntMeaUpBandwidth   Unsigned32,
    fgIntfEntMeaDownBandwidth Unsigned32
}

fgIntfEntVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The virtual domain the interface belongs to. This index corresponds to the index used by fgVdTable."
    ::= { fgIntfEntry 1 }

fgIntfEntEstUpBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Estimated maximum upstream bandwidth (Kbps). Used to estimate link utilization."
    ::= { fgIntfEntry 2 }

fgIntfEntEstDownBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Estimated maximum downstream bandwidth (Kbps). Used to estimate link utilization."
    ::= { fgIntfEntry 3 }

fgIntfEntMeaUpBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Measured upstream bandwidth (Kbps)."
    ::= { fgIntfEntry 4 }

fgIntfEntMeaDownBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Measured downstream bandwidth (Kbps)."
    ::= { fgIntfEntry 5 }

fgIntfVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of Vlans and values."
    ::= { fgIntfTables 2 }

fgIntfVlanEntry OBJECT-TYPE
    SYNTAX      FgIntfVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry containing the ID, name, physical interface name of a given vlan."
    INDEX       { ifIndex }
    ::= { fgIntfVlanTable 1 }

FgIntfVlanEntry ::= SEQUENCE {
    fgIntfVlanName      DisplayString,
    fgIntfVlanID        Unsigned32,
    fgIntfVlanPhyName   DisplayString
}

fgIntfVlanName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Vlan interface name."
    ::= { fgIntfVlanEntry 1 }

fgIntfVlanID OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Vlan interface ID."
    ::= { fgIntfVlanEntry 2 }

fgIntfVlanPhyName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The physical interface name associated with the vlan."
    ::= { fgIntfVlanEntry 3 }


fgIntfVrrps OBJECT IDENTIFIER
    ::= { fgIntf 3 }

fgIntfVrrpCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgIntfVrrpTable"
    ::= { fgIntfVrrps 1 }

fgIntfVrrpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfVrrpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of VRRPs and values."
    ::= { fgIntfVrrps 2 }

fgIntfVrrpEntry OBJECT-TYPE
    SYNTAX      FgIntfVrrpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the ID, group ID, interface name, state and IP address of a given virtual router."
    INDEX       { fgIntfVrrpEntIndex }
    ::= { fgIntfVrrpTable 1 }

FgIntfVrrpEntry ::= SEQUENCE {
    fgIntfVrrpEntIndex   FnIndex,
    fgIntfVrrpEntVrId    FnIndex,
    fgIntfVrrpEntGrpId   FnIndex,
    fgIntfVrrpEntIfName  DisplayString,
    fgIntfVrrpEntState   INTEGER,
    fgIntfVrrpEntVrIp    IpAddress
}

fgIntfVrrpEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A unique identifier within the fgIntfVrrpTable"
    ::= { fgIntfVrrpEntry 1 }

fgIntfVrrpEntVrId OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "ID of a virtual router."
    ::= { fgIntfVrrpEntry 2 }

fgIntfVrrpEntGrpId OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The group ID of a virtual router."
    ::= { fgIntfVrrpEntry 3 }

fgIntfVrrpEntIfName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The interface name of a virtual router."
    ::= { fgIntfVrrpEntry 4 }

fgIntfVrrpEntState OBJECT-TYPE
    SYNTAX      INTEGER { backup(1), master(2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "State of a virtual router."
    ::= { fgIntfVrrpEntry 5 }

fgIntfVrrpEntVrIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IP address of a virtual router."
    ::= { fgIntfVrrpEntry 6 }

fgIntfVlanHbs OBJECT IDENTIFIER
    ::= { fgIntf 4 }

fgIntfVlanHbCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of entries in fgIntfVlanHbTable"
    ::= { fgIntfVlanHbs 1 }

fgIntfVlanHbTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfVlanHbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of VlanHbs and values."
    ::= { fgIntfVlanHbs 2 }

fgIntfVlanHbEntry OBJECT-TYPE
    SYNTAX      FgIntfVlanHbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the vlan interface name, peer's serial number and state of a given vlan interface heartbeat."
    INDEX       { fgIntfVlanHbEntIndex }
    ::= { fgIntfVlanHbTable 1 }

FgIntfVlanHbEntry ::= SEQUENCE {
    fgIntfVlanHbEntIndex   FnIndex,
    fgIntfVlanHbEntIfName  DisplayString,
    fgIntfVlanHbEntSerial  DisplayString,
    fgIntfVlanHbEntState   INTEGER
}

fgIntfVlanHbEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A unique identifier within the fgIntfVlanHbTable"
    ::= { fgIntfVlanHbEntry 1 }

fgIntfVlanHbEntIfName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The vlan interface name."
    ::= { fgIntfVlanHbEntry 2 }

fgIntfVlanHbEntSerial OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Serial number of a vlan HA peer."
    ::= { fgIntfVlanHbEntry 3 }

fgIntfVlanHbEntState OBJECT-TYPE
    SYNTAX      INTEGER { active(1), inactive(2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "State of a vlan interface heartbeat."
    ::= { fgIntfVlanHbEntry 4 }

fgIntfBcs OBJECT IDENTIFIER
    ::= { fgIntf 5 }

fgIntfBcTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfBcEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of all class-levels with bandwidth usage and packet drop counter."
    ::= { fgIntfBcs 2 }

fgIntfBcEntry OBJECT-TYPE
    SYNTAX      FgIntfBcEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the Bandwidth Control statistics, packet drop counter of a given interface."
    INDEX       { ifIndex }
    ::= { fgIntfBcTable 1 }

FgIntfBcEntry ::= SEQUENCE {
    fgIntfBcAllocatedBandwidth  Integer32,
    fgIntfBcGuaranteedBandwidth Integer32,
    fgIntfBcMaxBandwidth        Integer32,
    fgIntfBcCurrentBandwidth    Integer32,
    fgIntfBcBytes               Counter64,
    fgIntfBcDrops               Counter64
}

fgIntfBcAllocatedBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Allocated Bandwidth of a given interface and class-level."
    ::= { fgIntfBcEntry 1 }

fgIntfBcGuaranteedBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Guaranteed Bandwidth of a given interface and class-level."
    ::= { fgIntfBcEntry 2 }

fgIntfBcMaxBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Max Bandwidth of a given interface and class-level."
    ::= { fgIntfBcEntry 3 }

fgIntfBcCurrentBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current Bandwidth of a given interface and class-level."
    ::= { fgIntfBcEntry 4 }

fgIntfBcBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Bytes of a given inteface and class-level."
    ::= { fgIntfBcEntry 5 }

fgIntfBcDrops OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Packet drop counter of a given interface and class-level."
    ::= { fgIntfBcEntry 6 }

fgIntfBcQTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfBcQEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of all queue type class-levels with packet sent and drop counter."
    ::= { fgIntfBcs 4 }

fgIntfBcQEntry OBJECT-TYPE
    SYNTAX      FgIntfBcQEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry containing the packet sent and drop counter of a given interface."
    INDEX       { ifIndex }
    ::= { fgIntfBcQTable 1 }

FgIntfBcQEntry ::= SEQUENCE {
    fgIntfBcQPackets    Counter64,
    fgIntfBcQBytes      Counter64,
    fgIntfBcQPDrops     Counter64,
    fgIntfBcQBDrops     Counter64
}

fgIntfBcQPackets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Packet sent counter of a given interface and class-level."
    ::= { fgIntfBcQEntry 1 }

fgIntfBcQBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Byte sent counter of a given interface and class-level."
    ::= { fgIntfBcQEntry 2 }

fgIntfBcQPDrops OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Packet drop counter of a given inteface and class-level."
    ::= { fgIntfBcQEntry 3 }

fgIntfBcQBDrops OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Byte drop counter of a given inteface and class-level."
    ::= { fgIntfBcQEntry 4 }

fgIntfBcInTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfBcInEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of all class-levels with bandwidth usage and packet drop counter."
    ::= { fgIntfBcs 3 }

fgIntfBcInEntry OBJECT-TYPE
    SYNTAX      FgIntfBcInEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the Bandwidth Control statistics, packet drop counter of a given interface."
    INDEX       { ifIndex }
    ::= { fgIntfBcInTable 1 }

FgIntfBcInEntry ::= SEQUENCE {
    fgIntfBcInAllocatedBandwidth  Integer32,
    fgIntfBcInGuaranteedBandwidth Integer32,
    fgIntfBcInMaxBandwidth        Integer32,
    fgIntfBcInCurrentBandwidth    Integer32,
    fgIntfBcInBytes               Counter64,
    fgIntfBcInDrops               Counter64
}

fgIntfBcInAllocatedBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Allocated Bandwidth of a given interface and class-level."
    ::= { fgIntfBcInEntry 1 }

fgIntfBcInGuaranteedBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Guaranteed Bandwidth of a given interface and class-level."
    ::= { fgIntfBcInEntry 2 }

fgIntfBcInMaxBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Max Bandwidth of a given interface and class-level."
    ::= { fgIntfBcInEntry 3 }

fgIntfBcInCurrentBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current Bandwidth of a given interface and class-level."
    ::= { fgIntfBcInEntry 4 }

fgIntfBcInBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Bytes of a given inteface and class-level."
    ::= { fgIntfBcInEntry 5 }

fgIntfBcInDrops OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Packet drop counter of a given interface and class-level."
    ::= { fgIntfBcInEntry 6 }

fgIntfBcCfgTables OBJECT IDENTIFIER
    ::= { fgIntfBcs 5 }

fgIntfBcCfgIfTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfBcCfgIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Configure values in the interfaces which have egress or ingress shaping profile configured."
    ::= { fgIntfBcCfgTables 1 }

fgIntfBcCfgIfEntry OBJECT-TYPE
    SYNTAX      FgIntfBcCfgIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the configure values of a given interface."
    INDEX       { ifIndex }
    ::= { fgIntfBcCfgIfTable 1 }

FgIntfBcCfgIfEntry ::= SEQUENCE {
    fgIntfBcCfgIfName             DisplayString,
    fgIntfBcCfgIfEgressSProfile   DisplayString,
    fgIntfBcCfgIfIngressSProfile  DisplayString,
    fgIntfBcCfgIfEstUpBandwidth   Unsigned32,
    fgIntfBcCfgIfEstDownBandwidth Unsigned32,
    fgIntfBcCfgIfInBandwidth      Unsigned32,
    fgIntfBcCfgIfOutBandwidth     Unsigned32
}

fgIntfBcCfgIfName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Name of the interface."
    ::= { fgIntfBcCfgIfEntry 1 }

fgIntfBcCfgIfEgressSProfile OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The egress shaping profile configured in the interface."
    ::= { fgIntfBcCfgIfEntry 2 }

fgIntfBcCfgIfIngressSProfile OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The ingress shaping profile configured in the interface."
    ::= { fgIntfBcCfgIfEntry 3 }

fgIntfBcCfgIfEstUpBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Estimated maximum upstream bandwidth."
    ::= { fgIntfBcCfgIfEntry 4 }

fgIntfBcCfgIfEstDownBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Estimated maximum downstream bandwidth."
    ::= { fgIntfBcCfgIfEntry 5 }

fgIntfBcCfgIfInBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "In bandwidth."
    ::= { fgIntfBcCfgIfEntry 6 }

fgIntfBcCfgIfOutBandwidth OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Out bandwidth."
    ::= { fgIntfBcCfgIfEntry 7 }

fgIntfBcCfgSproTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfBcCfgSproEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Configure values in shaping profiles."
    ::= { fgIntfBcCfgTables 2 }

fgIntfBcCfgSproEntry OBJECT-TYPE
    SYNTAX      FgIntfBcCfgSproEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the configure values of a shaping profile."
    INDEX       { fgVdEntIndex, fgIntfBcCfgSproID }
    ::= { fgIntfBcCfgSproTable 1 }

FgIntfBcCfgSproEntry ::= SEQUENCE {
    fgIntfBcCfgSproID              FnIndex,
    fgIntfBcCfgSproName            DisplayString,
    fgIntfBcCfgSproType            INTEGER,
    fgIntfBcCfgSproDefaultClassId  Unsigned32,
    fgIntfBcCfgSproClassNum        Unsigned32
}

fgIntfBcCfgSproID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Shaping profile ID which is created sequentially and uniquely within a virtual domain."
    ::= { fgIntfBcCfgSproEntry 1 }

fgIntfBcCfgSproName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Name of the shaping profile."
    ::= { fgIntfBcCfgSproEntry 2 }

fgIntfBcCfgSproType OBJECT-TYPE
    SYNTAX      INTEGER { policing(0), queueing(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Type of the shaping profile."
    ::= { fgIntfBcCfgSproEntry 3 }

fgIntfBcCfgSproDefaultClassId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Default class id of the shaping profile."
    ::= { fgIntfBcCfgSproEntry 4 }

fgIntfBcCfgSproClassNum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The class number of the shaping profile."
    ::= { fgIntfBcCfgSproEntry 5 }

fgIntfBcCfgSentTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfBcCfgSentEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Configure values in shaping entries of all shaping profiles."
    ::= { fgIntfBcCfgTables 3 }

fgIntfBcCfgSentEntry OBJECT-TYPE
    SYNTAX      FgIntfBcCfgSentEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the configure values of a shaping entry."
    INDEX       { fgVdEntIndex, fgIntfBcCfgSproID, fgIntfBcCfgSentClassID }
    ::= { fgIntfBcCfgSentTable 1 }

FgIntfBcCfgSentEntry ::= SEQUENCE {
    fgIntfBcCfgSentClassID             FnIndex,
    fgIntfBcCfgSentClassName           DisplayString,
    fgIntfBcCfgSentGuaranteedBandwidth Integer32,
    fgIntfBcCfgSentMaxBandwidth        Integer32
}

fgIntfBcCfgSentClassID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Class ID which is configured in firewall.traffic-class within a virtual domain."
    ::= { fgIntfBcCfgSentEntry 1 }

fgIntfBcCfgSentClassName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Class name of a given shaping entry."
    ::= { fgIntfBcCfgSentEntry 2 }

fgIntfBcCfgSentGuaranteedBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Guaranteed Bandwidth of a given shaping profile and class-level."
    ::= { fgIntfBcCfgSentEntry 3 }

fgIntfBcCfgSentMaxBandwidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Max Bandwidth of a given shaping profile and class-level."
    ::= { fgIntfBcCfgSentEntry 4 }

fgIntfBcCfgSpolTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIntfBcCfgSpolEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Configure values in shaping policies."
    ::= { fgIntfBcCfgTables 4 }

fgIntfBcCfgSpolEntry OBJECT-TYPE
    SYNTAX      FgIntfBcCfgSpolEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing the configure values of a shaping policy."
    INDEX       { fgVdEntIndex, fgIntfBcCfgSpolID }
    ::= { fgIntfBcCfgSpolTable 1 }

FgIntfBcCfgSpolEntry ::= SEQUENCE {
    fgIntfBcCfgSpolID        FnIndex,
    fgIntfBcCfgSpolSrcaddr   DisplayString,
    fgIntfBcCfgSpolDstaddr   DisplayString,
    fgIntfBcCfgSpolSvr       DisplayString,
    fgIntfBcCfgSpolComment   DisplayString,
    fgIntfBcCfgSpolClassName DisplayString
}

fgIntfBcCfgSpolID OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Shaping policy ID which is only unique within a virtual domain."
    ::= { fgIntfBcCfgSpolEntry 1 }

fgIntfBcCfgSpolSrcaddr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Src addr of a given shaping policy."
    ::= { fgIntfBcCfgSpolEntry 2 }

fgIntfBcCfgSpolDstaddr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Dst addr of a given shaping policy."
    ::= { fgIntfBcCfgSpolEntry 3 }

fgIntfBcCfgSpolSvr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Service of a given shaping policy."
    ::= { fgIntfBcCfgSpolEntry 4 }

fgIntfBcCfgSpolComment OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Comment of a given shaping policy."
    ::= { fgIntfBcCfgSpolEntry 5 }

fgIntfBcCfgSpolClassName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Class name of a given shaping policy."
    ::= { fgIntfBcCfgSpolEntry 6 }

--
-- fortinet.fnFortiGateMib.fgAntivirus
--

fgAntivirus OBJECT IDENTIFIER
    ::= { fnFortiGateMib 8 }

fgAvInfo OBJECT IDENTIFIER
    ::= { fgAntivirus 1 }

fgAvTables OBJECT IDENTIFIER
    ::= { fgAntivirus 2 }

fgAvStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgAvStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of Anti-virus statistics per virtual domain"
    ::= { fgAvTables 1 }

fgAvStatsEntry OBJECT-TYPE
    SYNTAX      FgAvStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Anti-virus statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgAvStatsTable 1 }

FgAvStatsEntry ::= SEQUENCE {
    fgAvVirusDetected       Counter32,
    fgAvVirusBlocked        Counter32,
    fgAvHTTPVirusDetected   Counter32,
    fgAvHTTPVirusBlocked    Counter32,
    fgAvSMTPVirusDetected   Counter32,
    fgAvSMTPVirusBlocked    Counter32,
    fgAvPOP3VirusDetected   Counter32,
    fgAvPOP3VirusBlocked    Counter32,
    fgAvIMAPVirusDetected   Counter32,
    fgAvIMAPVirusBlocked    Counter32,
    fgAvFTPVirusDetected    Counter32,
    fgAvFTPVirusBlocked     Counter32,
    fgAvIMVirusDetected     Counter32,
    fgAvIMVirusBlocked      Counter32,
    fgAvNNTPVirusDetected   Counter32,
    fgAvNNTPVirusBlocked    Counter32,
    fgAvOversizedDetected   Counter32,
    fgAvOversizedBlocked    Counter32,
    fgAvMAPIVirusDetected   Counter32,
    fgAvMAPIVirusBlocked    Counter32,
    fgAvSMBVirusDetected    Counter32,
    fgAvSMBVirusBlocked     Counter32
}

fgAvVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 1 }

fgAvVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 2 }

fgAvHTTPVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over HTTP detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 3 }

fgAvHTTPVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over HTTP blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 4 }

fgAvSMTPVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over SMTP detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 5 }

fgAvSMTPVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over SMTP blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 6 }

fgAvPOP3VirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over POP3 detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 7 }

fgAvPOP3VirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over POP3 blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 8 }

fgAvIMAPVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over IMAP detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 9 }

fgAvIMAPVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over IMAP blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 10 }

fgAvFTPVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over FTP detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 11 }

fgAvFTPVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over FTP blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 12 }

fgAvIMVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over IM protocols detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 13 }

fgAvIMVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over IM protocols blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 14 }

fgAvNNTPVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over NNTP detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 15 }

fgAvNNTPVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over NNTP blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 16 }

fgAvOversizedDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of over-sized file transmissions detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 17 }

fgAvOversizedBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of over-sized file transmissions blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 18 }

fgAvMAPIVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over MAPI detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 19 }

fgAvMAPIVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of virus transmissions over MAPI blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 20 }

fgAvSMBVirusDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of virus transmissions over SMB detected in the virtual domain since start-up"
    ::= { fgAvStatsEntry 21 }

fgAvSMBVirusBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of virus transmissions over SMB blocked in the virtual domain since start-up"
    ::= { fgAvStatsEntry 22 }

--
-- fortinet.fnFortiGateMib.fgAntivirus.fgAvTrapObjects
--

fgAvTrapObjects OBJECT IDENTIFIER
    ::= { fgAntivirus 3 }

fgAvTrapVirName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Virus name that triggered event"
    ::= { fgAvTrapObjects 1 }

--
-- fortinet.fnFortiGateMib.fgIps
--

fgIps OBJECT IDENTIFIER
    ::= { fnFortiGateMib 9 }

fgIpsInfo OBJECT IDENTIFIER
    ::= { fgIps 1 }

fgIpsTables OBJECT IDENTIFIER
    ::= { fgIps 2 }

fgIpsStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIpsStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of IPS/IDS statistics per virtual domain"
    ::= { fgIpsTables 1 }

fgIpsStatsEntry OBJECT-TYPE
    SYNTAX      FgIpsStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IPS/IDS statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgIpsStatsTable 1 }

FgIpsStatsEntry ::= SEQUENCE {
    fgIpsIntrusionsDetected     Counter32,
    fgIpsIntrusionsBlocked      Counter32,
    fgIpsCritSevDetections      Counter32,
    fgIpsHighSevDetections      Counter32,
    fgIpsMedSevDetections       Counter32,
    fgIpsLowSevDetections       Counter32,
    fgIpsInfoSevDetections      Counter32,
    fgIpsSignatureDetections    Counter32,
    fgIpsAnomalyDetections      Counter32
}

fgIpsIntrusionsDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of intrusions detected since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 1 }

fgIpsIntrusionsBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of intrusions blocked since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 2 }

fgIpsCritSevDetections OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of critical severity intrusions
         detected since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 3 }

fgIpsHighSevDetections OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of high severity intrusions 
         detected since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 4 }

fgIpsMedSevDetections OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of medium severity intrusions
         detected since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 5 }

fgIpsLowSevDetections OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of low severity intrusions 
         detected since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 6 }

fgIpsInfoSevDetections OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of informational severity intrusions
         detected since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 7 }

fgIpsSignatureDetections OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of intrusions detected by signature
         since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 8 }

fgIpsAnomalyDetections OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of intrusions DECed as anomalies 
         since start-up in this virtual domain"
    ::= { fgIpsStatsEntry 9 }

--
-- fortinet.fnFortiGateMib.fgIps.fgIpsTrapObjects
--

fgIpsTrapObjects OBJECT IDENTIFIER
    ::= { fgIps 3 }

fgIpsTrapSigId OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "ID of IPS signature identified in trap"
    ::= { fgIpsTrapObjects 1 }

fgIpsTrapSrcIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Source IP Address of the IPS signature trigger"
    ::= { fgIpsTrapObjects 2 }

fgIpsTrapSigMsg OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Message associated with IPS event"
    ::= { fgIpsTrapObjects 3 }

--
-- fortinet.fnFortiGateMib.fgApplications
--

fgApplications OBJECT IDENTIFIER
    ::= { fnFortiGateMib 10 }

fgWebfilter OBJECT IDENTIFIER
    ::= { fgApplications 1 }

fgWebfilterInfo OBJECT IDENTIFIER
    ::= { fgWebfilter 1 }

fgWebfilterTables OBJECT IDENTIFIER
    ::= { fgWebfilter 2 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgWebfilter.fgWebfilterTables.fgWebfilterStatsTable
--

fgWebfilterStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWebfilterStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of Web-filter statistics per virtual domain"
    ::= { fgWebfilterTables 1 }

fgWebfilterStatsEntry OBJECT-TYPE
    SYNTAX      FgWebfilterStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Web-filter statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgWebfilterStatsTable 1 }

FgWebfilterStatsEntry ::= SEQUENCE {
    fgWfHTTPBlocked         Counter32,
    fgWfHTTPSBlocked        Counter32,
    fgWfHTTPURLBlocked      Counter32,
    fgWfHTTPSURLBlocked     Counter32,
    fgWfActiveXBlocked      Counter32,
    fgWfCookieBlocked       Counter32,
    fgWfAppletBlocked       Counter32
}

fgWfHTTPBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP sessions blocked by Web-filter since start-up"
    ::= { fgWebfilterStatsEntry 1 }

fgWfHTTPSBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTPS sessions blocked by Web-filter since start-up"
    ::= { fgWebfilterStatsEntry 2 }

fgWfHTTPURLBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP URLs blocked by Web-filter since start-up"
    ::= { fgWebfilterStatsEntry 3 }

fgWfHTTPSURLBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTPS URLs blocked by Web-filter since start-up"
    ::= { fgWebfilterStatsEntry 4 }

fgWfActiveXBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of ActiveX downloads blocked by Web-filter since start-up"
    ::= { fgWebfilterStatsEntry 5 }

fgWfCookieBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP Cookies blocked by Web-filter since start-up"
    ::= { fgWebfilterStatsEntry 6 }

fgWfAppletBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of Applets blocked by Web-filter since start-up"
    ::= { fgWebfilterStatsEntry 7 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgWebfilter.fgWebfilterTables.fgFortiGuardStatsTable
--

fgFortiGuardStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgFortiGuardStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of FortiGuard statistics per virtual domain"
    ::= { fgWebfilterTables 2 }

fgFortiGuardStatsEntry OBJECT-TYPE
    SYNTAX      FgFortiGuardStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "FortiGuard statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgFortiGuardStatsTable 1 }

FgFortiGuardStatsEntry ::= SEQUENCE {
    fgFgWfHTTPExamined      Counter32,
    fgFgWfHTTPSExamined     Counter32,
    fgFgWfHTTPAllowed       Counter32,
    fgFgWfHTTPSAllowed      Counter32,
    fgFgWfHTTPBlocked       Counter32,
    fgFgWfHTTPSBlocked      Counter32,
    fgFgWfHTTPLogged        Counter32,
    fgFgWfHTTPSLogged       Counter32,
    fgFgWfHTTPOverridden    Counter32,
    fgFgWfHTTPSOverridden   Counter32
}

fgFgWfHTTPExamined OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP requests examined using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 1 }

fgFgWfHTTPSExamined OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTPS requests examined using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 2 }

fgFgWfHTTPAllowed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP requests allowed to proceed using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 3 }

fgFgWfHTTPSAllowed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTPS requests allowed to proceed using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 4 }

fgFgWfHTTPBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP requests blocked using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 5 }

fgFgWfHTTPSBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTPS requests blocked using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 6 }

fgFgWfHTTPLogged OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP requests logged using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 7 }

fgFgWfHTTPSLogged OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTPS requests logged using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 8 }

fgFgWfHTTPOverridden OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP requests overridden using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 9 }

fgFgWfHTTPSOverridden OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTPS requests overridden using FortiGuard since start-up"
    ::= { fgFortiGuardStatsEntry 10 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxyHTTP
--

fgAppProxyHTTP OBJECT IDENTIFIER
    ::= { fgApplications 100 }

fgApHTTPUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "HTTP proxy up-time (in seconds)"
    ::= { fgAppProxyHTTP 1 }

fgApHTTPMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "HTTP proxy memory usage (percentage of system total)"
    ::= { fgAppProxyHTTP 2 }

fgApHTTPConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "HTTP proxy current connections"
    ::= { fgAppProxyHTTP 4 }

fgApHTTPMaxConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum number of connections supported by HTTP proxy"
    ::= { fgAppProxyHTTP 5 }

fgApHTTPStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApHTTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of HTTP Proxy statistics per virtual domain"
    ::= { fgAppProxyHTTP 3 }

fgApHTTPStatsEntry OBJECT-TYPE
    SYNTAX      FgApHTTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "HTTP Proxy statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApHTTPStatsTable 1 }

FgApHTTPStatsEntry ::= SEQUENCE {
    fgApHTTPReqProcessed    Counter32
}

fgApHTTPReqProcessed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of HTTP requests in this virtual domain processed by the HTTP proxy since start-up"
    ::= { fgApHTTPStatsEntry 1 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxySMTP
--

fgAppProxySMTP OBJECT IDENTIFIER
    ::= { fgApplications 101 }

fgApSMTPUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "SMTP Proxy up-time, in seconds"
    ::= { fgAppProxySMTP 1 }

fgApSMTPMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "SMTP Proxy memory utilization (percentage of system total)"
    ::= { fgAppProxySMTP 2 }

fgApSMTPConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SMTP proxy current connections"
    ::= { fgAppProxySMTP 4 }

fgApSMTPMaxConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum number of connections supported by SMTP proxy"
    ::= { fgAppProxySMTP 5 }

fgApSMTPStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApSMTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of SMTP proxy statistics per virtual domain"
    ::= { fgAppProxySMTP 3 }

fgApSMTPStatsEntry OBJECT-TYPE
    SYNTAX      FgApSMTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "SMTP Proxy statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApSMTPStatsTable 1 }

FgApSMTPStatsEntry ::= SEQUENCE {
    fgApSMTPReqProcessed    Counter32,
    fgApSMTPSpamDetected    Counter32
}

fgApSMTPReqProcessed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of requests in this virtual domain processed by the SMTP proxy since start-up"
    ::= { fgApSMTPStatsEntry 1 }

fgApSMTPSpamDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of spam detected in this virtual domain by the SMTP proxy since start-up"
    ::= { fgApSMTPStatsEntry 2 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxyPOP3
--

fgAppProxyPOP3 OBJECT IDENTIFIER
    ::= { fgApplications 102 }

fgApPOP3UpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "Up-time of the POP3 proxy, in seconds"
    ::= { fgAppProxyPOP3 1 }

fgApPOP3MemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "Memory usage of the POP3 Proxy (percentage of system total)"
    ::= { fgAppProxyPOP3 2 }

fgApPOP3Connections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "POP3 proxy current connections"
    ::= { fgAppProxyPOP3 4 }

fgApPOP3MaxConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum number of connections supported by POP3 proxy"
    ::= { fgAppProxyPOP3 5 }

fgApPOP3StatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApPOP3StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of POP3 proxy statistics per virtual domain"
    ::= { fgAppProxyPOP3 3 }

fgApPOP3StatsEntry OBJECT-TYPE
    SYNTAX      FgApPOP3StatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Proxy pop3 statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApPOP3StatsTable 1 }

FgApPOP3StatsEntry ::= SEQUENCE {
    fgApPOP3ReqProcessed    Counter32,
    fgApPOP3SpamDetected    Counter32
}

fgApPOP3ReqProcessed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of requests in this virtual domain processed by the POP3 proxy since start-up"
    ::= { fgApPOP3StatsEntry 1 }

fgApPOP3SpamDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of spam detected in this virtual domain by the POP3 Proxy since start-up"
    ::= { fgApPOP3StatsEntry 2 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxyIMAP
--

fgAppProxyIMAP OBJECT IDENTIFIER
    ::= { fgApplications 103 }

fgApIMAPUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "Up-time of the IMAP proxy, in seconds"
    ::= { fgAppProxyIMAP 1 }

fgApIMAPMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "Memory utilization of the IMAP Proxy (as a percentage of the system total)"
    ::= { fgAppProxyIMAP 2 }

fgApIMAPConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "IMAP proxy current connections"
    ::= { fgAppProxyIMAP 4 }

fgApIMAPMaxConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum number of connections supported by IMAP proxy"
    ::= { fgAppProxyIMAP 5 }

fgApIMAPStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApIMAPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of IMAP proxy statistics per virtual domain"
    ::= { fgAppProxyIMAP 3 }

fgApIMAPStatsEntry OBJECT-TYPE
    SYNTAX      FgApIMAPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IMAP Proxy statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApIMAPStatsTable 1 }

FgApIMAPStatsEntry ::= SEQUENCE {
    fgApIMAPReqProcessed    Counter32,
    fgApIMAPSpamDetected    Counter32
}

fgApIMAPReqProcessed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of requests in this virtual domain processed by the IMAP proxy since start-up"
    ::= { fgApIMAPStatsEntry 1 }

fgApIMAPSpamDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of spam detected in this virtual domain by the IMAP proxy since start-up"
    ::= { fgApIMAPStatsEntry 2 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxyNNTP
--

fgAppProxyNNTP OBJECT IDENTIFIER
    ::= { fgApplications 104 }

fgApNNTPUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "Up-time of the NNTP proxy, in seconds"
    ::= { fgAppProxyNNTP 1 }

fgApNNTPMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "Memory utilization of the NNTP proxy, as a percentage of the system total"
    ::= { fgAppProxyNNTP 2 }

fgApNNTPConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "NNTP proxy current connections"
    ::= { fgAppProxyNNTP 4 }

fgApNNTPMaxConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum number of connections supported by NNTP proxy"
    ::= { fgAppProxyNNTP 5 }

fgApNNTPStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApNNTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of NNTP proxy statistics per virtual domain"
    ::= { fgAppProxyNNTP 3 }

fgApNNTPStatsEntry OBJECT-TYPE
    SYNTAX      FgApNNTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "NNTP Proxy statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApNNTPStatsTable 1 }

FgApNNTPStatsEntry ::= SEQUENCE {
    fgApNNTPReqProcessed    Counter32
}

fgApNNTPReqProcessed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of requests in the virtual domain processed by the NNTP proxy since start-up"
    ::= { fgApNNTPStatsEntry 1 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxyIM
--

fgAppProxyIM OBJECT IDENTIFIER
    ::= { fgApplications 105 }

fgApIMUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Up-time of the IM proxy, in seconds"
    ::= { fgAppProxyIM 1 }

fgApIMMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IM Proxy memory usage, as a percentage of the system total"
    ::= { fgAppProxyIM 2 }

fgApIMStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApIMStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of IM proxy statistics per virtual domain"
    ::= { fgAppProxyIM 3 }

fgApIMStatsEntry OBJECT-TYPE
    SYNTAX      FgApIMStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IM Proxy statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApIMStatsTable 1 }

FgApIMStatsEntry ::= SEQUENCE {
    fgApIMReqProcessed  Counter32
}

fgApIMReqProcessed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of requests in this virtual domain processed by the IM proxy since start-up"
    ::= { fgApIMStatsEntry 1 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxySIP
--

fgAppProxySIP OBJECT IDENTIFIER
    ::= { fgApplications 106 }

fgApSIPUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Up-time of the SIP Proxy, in seconds"
    ::= { fgAppProxySIP 1 }

fgApSIPMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "SIP Proxy memory utilization, as a percentage of the system total"
    ::= { fgAppProxySIP 2 }

fgApSIPStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApSIPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of SIP proxy statistics per virtual domain"
    ::= { fgAppProxySIP 3 }

fgApSIPStatsEntry OBJECT-TYPE
    SYNTAX      FgApSIPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "SIP Proxy statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApSIPStatsTable 1 }

FgApSIPStatsEntry ::= SEQUENCE {
    fgApSIPClientReg        Counter32,
    fgApSIPCallHandling     Counter32,
    fgApSIPServices         Counter32,
    fgApSIPOtherReq         Counter32
}

fgApSIPClientReg OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of client registration requests (Register and Options) in this virtual domain
         processed by the SIP proxy since start-up"
    ::= { fgApSIPStatsEntry 1 }

fgApSIPCallHandling OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of call handling requests (Invite, Ack, Bye, Cancel and Refer) in this virtual domain
         processed by the SIP proxy since start-up"
    ::= { fgApSIPStatsEntry 2 }

fgApSIPServices OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of service requests (Subscribe, notify and Message) in this virtual domain
         processed by the SIP proxy since start-up"
    ::= { fgApSIPStatsEntry 3 }

fgApSIPOtherReq OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of other sip requests in this virtual domain processed by the SIP proxy since start-up"
    ::= { fgApSIPStatsEntry 4 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppScanUnit
--

fgAppScanUnit OBJECT IDENTIFIER
    ::= { fgApplications 107 }

fgAppSuNumber OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of scan units in the fgAppSuStatsTable"
    ::= { fgAppScanUnit 1 }

fgAppSuStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgAppSuStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of scan unit statistics"
    ::= { fgAppScanUnit 2 }

fgAppSuStatsEntry OBJECT-TYPE
    SYNTAX      FgAppSuStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Statistics entry for a particular scan unit"
    INDEX       { fgAppSuIndex }
    ::= { fgAppSuStatsTable 1 }

FgAppSuStatsEntry ::= SEQUENCE {
    fgAppSuIndex        FnIndex,
    fgAppSuFileScanned  Counter32
}

fgAppSuIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index that uniquely identifies a scan unit in the fgAppSuStatsTable"
    ::= { fgAppSuStatsEntry 1 }

fgAppSuFileScanned OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of files scanned by this scan unit"
    ::= { fgAppSuStatsEntry 2 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppVoIP
--

fgAppVoIP OBJECT IDENTIFIER
    ::= { fgApplications 108 }

fgAppVoIPStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgAppVoIPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of VoIP related statistics per virtual domain"
    ::= { fgAppVoIP 1 }

fgAppVoIPStatsEntry OBJECT-TYPE
    SYNTAX      FgAppVoIPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "VoIP statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgAppVoIPStatsTable 1 }

FgAppVoIPStatsEntry ::= SEQUENCE {
    fgAppVoIPConn           Gauge32,
    fgAppVoIPCallBlocked    Counter32
}

fgAppVoIPConn OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The current number of VoIP connections on the virtual domain"
    ::= { fgAppVoIPStatsEntry 1 }

fgAppVoIPCallBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of VoIP calls blocked (SIP Invites blocked and
         SCCP calls blocked) in this virtual domain"
    ::= { fgAppVoIPStatsEntry 2 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppP2P
--

fgAppP2P OBJECT IDENTIFIER
    ::= { fgApplications 109 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppP2P.fgAppP2PStatsTable
--

fgAppP2PStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgAppP2PStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of P2P protocol related statistics per virtual domain"
    ::= { fgAppP2P 1 }

fgAppP2PStatsEntry OBJECT-TYPE
    SYNTAX      FgAppP2PStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "P2P statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgAppP2PStatsTable 1 }

FgAppP2PStatsEntry ::= SEQUENCE {
    fgAppP2PConnBlocked     Counter32
}

fgAppP2PConnBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of P2P connections blocked in this virtual domain"
    ::= { fgAppP2PStatsEntry 1 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppP2P.fgAppP2PProtoTable
--

fgAppP2PProtoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgAppP2PProtoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of peer to peer statistics per virtual domain per protocol.
         This table has a dependent expansion relationship with fgVdTable."
    ::= { fgAppP2P 2 }

fgAppP2PProtoEntry OBJECT-TYPE
    SYNTAX      FgAppP2PProtoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "P2P statistics for a particular virtual domain and protocol"
    INDEX       { fgVdEntIndex, fgAppP2PProtEntProto }
    ::= { fgAppP2PProtoTable 1 }

FgAppP2PProtoEntry ::= SEQUENCE {
    fgAppP2PProtEntProto        FgP2PProto,
    fgAppP2PProtEntBytes        Counter64,
    fgAppP2PProtoEntLastReset   TimeTicks
}

fgAppP2PProtEntProto OBJECT-TYPE
    SYNTAX      FgP2PProto
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "P2P protocol this row of statistics is for, within the specified virtual domain"
    ::= { fgAppP2PProtoEntry 1 }

fgAppP2PProtEntBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes transferred through this virtual domain on this P2P protocol since last reset"
    ::= { fgAppP2PProtoEntry 2 }

fgAppP2PProtoEntLastReset OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Time elapsed since the corresponding fgAppP2PProtEntBytes was
         last reset to 0."
    ::= { fgAppP2PProtoEntry 3 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppIM
--

fgAppIM OBJECT IDENTIFIER
    ::= { fgApplications 110 }

fgAppIMStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgAppIMStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of instant messaging statistics per virtual domain"
    ::= { fgAppIM 1 }

fgAppIMStatsEntry OBJECT-TYPE
    SYNTAX      FgAppIMStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IM statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgAppIMStatsTable 1 }

FgAppIMStatsEntry ::= SEQUENCE {
    fgAppIMMessages         Counter32,
    fgAppIMFileTransfered   Counter32,
    fgAppIMFileTxBlocked    Counter32,
    fgAppIMConnBlocked      Counter32
}

fgAppIMMessages OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Total number of IM messages processed in this virtual domain"
    ::= { fgAppIMStatsEntry 1 }

fgAppIMFileTransfered OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of files transferred through this virtual domain"
    ::= { fgAppIMStatsEntry 2 }

fgAppIMFileTxBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of blocked file transfers in this virtual domain"
    ::= { fgAppIMStatsEntry 3 }

fgAppIMConnBlocked OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of connections blocked in this virtual domain"
    ::= { fgAppIMStatsEntry 4 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppProxyFTP
--

fgAppProxyFTP OBJECT IDENTIFIER
    ::= { fgApplications 111 }

fgApFTPUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "Up-time of the FTP proxy, in seconds"
    ::= { fgAppProxyFTP 1 }

fgApFTPMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION 
        "FTP Proxy memory utilization, as a percentage of the system total"
    ::= { fgAppProxyFTP 2 }

fgApFTPConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "FTP proxy current connections"
    ::= { fgAppProxyFTP 4 }

fgApFTPMaxConnections OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum number of connections supported by FTP proxy"
    ::= { fgAppProxyFTP 5 }

fgApFTPStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgApFTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of FTP proxy statistics per virtual domain"
    ::= { fgAppProxyFTP 3 }

fgApFTPStatsEntry OBJECT-TYPE
    SYNTAX      FgApFTPStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "FTP Proxy statistics for a particular virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgApFTPStatsTable 1 }

FgApFTPStatsEntry ::= SEQUENCE {
    fgApFTPReqProcessed     Counter32
}

fgApFTPReqProcessed OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of requests in this virtual domain processed by the FTP proxy since start-up"
    ::= { fgApFTPStatsEntry 1 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppExplicitProxy
--

fgAppExplicitProxy OBJECT IDENTIFIER
    ::= { fgApplications 112 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppExplicitProxy.fgExplicitProxyInfo
--

fgExplicitProxyInfo OBJECT IDENTIFIER
    ::= { fgAppExplicitProxy 1 }

fgExplicitProxyUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Explicit proxy up-time (in seconds)"
    ::= { fgExplicitProxyInfo 1 }

fgExplicitProxyMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Explicit proxy memory usage (percentage of system total)"
    ::= { fgExplicitProxyInfo 2 }

fgExplicitProxyRequests OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Explicit proxy total number of requests"
    ::= { fgExplicitProxyInfo 3 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppExplicitProxy.fgExplicitProxyStatsTable
--

fgExplicitProxyStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgExplicitProxyStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of explicit proxy statistics per virtual domain"
    ::= { fgAppExplicitProxy 2 }

fgExplicitProxyStatsEntry OBJECT-TYPE
    SYNTAX      FgExplicitProxyStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Explicit proxy statistics for a particular virtual domain"
    INDEX       { fgVdEntIndex }
    ::= { fgExplicitProxyStatsTable 1 }

FgExplicitProxyStatsEntry ::= SEQUENCE {
    fgExplicitProxyUsers     Integer32,
    fgExplicitProxySessions  Integer32
}

fgExplicitProxyUsers OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of current users in this virtual domain"
    ::= { fgExplicitProxyStatsEntry 1 }

fgExplicitProxySessions OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of current sessions in this virtual domain"
    ::= { fgExplicitProxyStatsEntry 2 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppExplicitProxy.fgExplicitProxyScanStatsTable
--

fgExplicitProxyScanStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgExplicitProxyScanStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of explicit proxy scan statistics per virtual domain"
    ::= { fgAppExplicitProxy 3 }

fgExplicitProxyScanStatsEntry OBJECT-TYPE
    SYNTAX      FgExplicitProxyScanStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Explicit proxy scan statistics for a particular virtual domain"
    INDEX       { fgVdEntIndex, fgExplicitProxyScanStatsDisp }
    ::= { fgExplicitProxyScanStatsTable 1 }

FgExplicitProxyScanStatsEntry ::= SEQUENCE {
    fgExplicitProxyScanStatsDisp     FgScanAvDisposition,
    fgExplicitProxyVirus             Counter32,
    fgExplicitProxyBannedWords       Counter32,
    fgExplicitProxyPolicy            Counter32,
    fgExplicitProxyOversized         Counter32,
    fgExplicitProxyArchNest          Counter32,
    fgExplicitProxyArchSize          Counter32,
    fgExplicitProxyArchEncrypted     Counter32,
    fgExplicitProxyArchMultiPart     Counter32,
    fgExplicitProxyArchUnsupported   Counter32,
    fgExplicitProxyArchBomb          Counter32,
    fgExplicitProxyArchCorrupt       Counter32
}

fgExplicitProxyScanStatsDisp OBJECT-TYPE
    SYNTAX      FgScanAvDisposition
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Disposition of an Scan result"
   ::= { fgExplicitProxyScanStatsEntry 1 }

fgExplicitProxyVirus OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of viruses in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 2 }

fgExplicitProxyBannedWords OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of elements containing banned words in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 3 }

fgExplicitProxyPolicy OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of elements violating policy (e.g. filename or file type rules) in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 4 }

fgExplicitProxyOversized OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of oversized elements in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 5 }

fgExplicitProxyArchNest OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of too deeply nested archives in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 6 }

fgExplicitProxyArchSize OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of archives that decompress beyond size limit in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 7 }

fgExplicitProxyArchEncrypted OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of encrypted archives in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 8 }

fgExplicitProxyArchMultiPart OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of multipart archives in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 9 }

fgExplicitProxyArchUnsupported OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of archives with unsupported (but known) formats in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 10 }

fgExplicitProxyArchBomb OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of archive bombs in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 11 }

fgExplicitProxyArchCorrupt OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of corrupt archives in this virtual domain"
    ::= { fgExplicitProxyScanStatsEntry 12 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppExplicitProxy.fgExplicitProxyScriptStatsTable
--

fgExplicitProxyScriptStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgExplicitProxyScriptStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of explicit proxy script filtering statistics per virtual domain"
    ::= { fgAppExplicitProxy 4 }

fgExplicitProxyScriptStatsEntry OBJECT-TYPE
    SYNTAX      FgExplicitProxyScriptStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Explicit proxy scan statistics for a particular virtual domain"
    INDEX       { fgVdEntIndex }
    ::= { fgExplicitProxyScriptStatsTable 1 }

FgExplicitProxyScriptStatsEntry ::= SEQUENCE {
    fgExplicitProxyFilteredApplets   Counter32,
    fgExplicitProxyFilteredActiveX   Counter32,
    fgExplicitProxyFilteredJScript   Counter32,
    fgExplicitProxyFilteredJS        Counter32,
    fgExplicitProxyFilteredVBS       Counter32,
    fgExplicitProxyFilteredOthScript Counter32
}

fgExplicitProxyFilteredApplets OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of applets filtered from files in this virtual domain"
    ::= { fgExplicitProxyScriptStatsEntry 1 }

fgExplicitProxyFilteredActiveX OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of ActiveX scripts filtered from files in this virtual domain"
    ::= { fgExplicitProxyScriptStatsEntry 2 }

fgExplicitProxyFilteredJScript OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of JScript scripts filtered from files in this virtual domain"
    ::= { fgExplicitProxyScriptStatsEntry 3 }

fgExplicitProxyFilteredJS OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of JavaScript scripts filtered from files in this virtual domain"
    ::= { fgExplicitProxyScriptStatsEntry 4 }

fgExplicitProxyFilteredVBS OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of Visual Basic scripts filtered from files in this virtual domain"
    ::= { fgExplicitProxyScriptStatsEntry 5 }

fgExplicitProxyFilteredOthScript OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of other types of scripts filtered from files in this virtual domain"
    ::= { fgExplicitProxyScriptStatsEntry 6 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppExplicitProxy.fgExplicitProxyFilterStatsTable
--

fgExplicitProxyFilterStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgExplicitProxyFilterStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of explicit proxy policy enforcement statistics per virtual domain"
    ::= { fgAppExplicitProxy 5 }

fgExplicitProxyFilterStatsEntry OBJECT-TYPE
    SYNTAX      FgExplicitProxyFilterStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Explicit proxy scan statistics for a particular virtual domain"
    INDEX       { fgVdEntIndex }
    ::= { fgExplicitProxyFilterStatsTable 1 }

FgExplicitProxyFilterStatsEntry ::= SEQUENCE {
    fgExplicitProxyBlockedDLP        Counter32,
    fgExplicitProxyBlockedConType    Counter32,
    fgExplicitProxyExaminedURLs      Counter32,
    fgExplicitProxyAllowedURLs       Counter32,
    fgExplicitProxyBlockedURLs       Counter32,
    fgExplicitProxyLoggedURLs        Counter32,
    fgExplicitProxyOverriddenURLs    Counter32
}

fgExplicitProxyBlockedDLP OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of elements blocked due to Data Leak Prevention in this virtual domain"
    ::= { fgExplicitProxyFilterStatsEntry 1 }

fgExplicitProxyBlockedConType OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of elements blocked due to Content-Type filtering rules in this virtual domain"
    ::= { fgExplicitProxyFilterStatsEntry 2 }

fgExplicitProxyExaminedURLs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of URLs inspected against filtering rules in this virtual domain"
    ::= { fgExplicitProxyFilterStatsEntry 3 }

fgExplicitProxyAllowedURLs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of URLs explicitly allowed due to filtering rules in this virtual domain"
    ::= { fgExplicitProxyFilterStatsEntry 4 }

fgExplicitProxyBlockedURLs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of URLs explicitly blocked due to filtering rules in this virtual domain"
    ::= { fgExplicitProxyFilterStatsEntry 5 }

fgExplicitProxyLoggedURLs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of URLs logged due to filtering rules in this virtual domain"
    ::= { fgExplicitProxyFilterStatsEntry 6 }

fgExplicitProxyOverriddenURLs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of URLs access due to overriding filtering rules in this virtual domain"
    ::= { fgExplicitProxyFilterStatsEntry 7 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWebCache
--

fgAppWebCache OBJECT IDENTIFIER
    ::= { fgApplications 113 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWebCache.fgWebCacheInfo
--

fgWebCacheInfo OBJECT IDENTIFIER
    ::= { fgAppWebCache 1 }

fgWebCacheRAMLimit OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RAM available for web cache in bytes"
    ::= { fgWebCacheInfo 1 }

fgWebCacheRAMUsage OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RAM used by web cache in bytes"
    ::= { fgWebCacheInfo 2 }

fgWebCacheRAMHits OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of cache hits in RAM since last reset"
    ::= { fgWebCacheInfo 3 }

fgWebCacheRAMMisses OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of cache misses in RAM since last reset"
    ::= { fgWebCacheInfo 4 }

fgWebCacheRequests OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of cache requests since last reset"
    ::= { fgWebCacheInfo 5 }

fgWebCacheBypass OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of cache bypasses since last reset"
    ::= { fgWebCacheInfo 6 }

fgWebCacheUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Web Cache up-time (in seconds)"
    ::= { fgWebCacheInfo 7 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWebCache.fgWebCacheDiskStatsTable
--

fgWebCacheDiskStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWebCacheDiskStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of the Web Cache disk statistics per disk"
    ::= { fgAppWebCache 2 }

fgWebCacheDiskStatsEntry OBJECT-TYPE
    SYNTAX      FgWebCacheDiskStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "The Web Cache disk statistics for a particular disk"
    INDEX       { fgWebCacheDisk }
    ::= { fgWebCacheDiskStatsTable 1 }

FgWebCacheDiskStatsEntry ::= SEQUENCE {
    fgWebCacheDisk              Unsigned32,
    fgWebCacheDiskLimit         CounterBasedGauge64,
    fgWebCacheDiskUsage         CounterBasedGauge64,
    fgWebCacheDiskHits          Counter32,
    fgWebCacheDiskMisses        Counter32,
    fgWebCacheDiskFailure       Counter64
}

fgWebCacheDisk OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The Web Cache Disk index"
    ::= { fgWebCacheDiskStatsEntry 1 }

fgWebCacheDiskLimit OBJECT-TYPE
    SYNTAX     CounterBasedGauge64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The about of storage (in bytes) available for the Web Cache on a particular disk"
    ::= { fgWebCacheDiskStatsEntry 2 }

fgWebCacheDiskUsage OBJECT-TYPE
    SYNTAX     CounterBasedGauge64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The about of storage (in bytes) in use by the Web Cache on a paricular disk"
    ::= { fgWebCacheDiskStatsEntry 3 }

fgWebCacheDiskHits OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of cache hits on a paricular disk"
    ::= { fgWebCacheDiskStatsEntry 4 }

fgWebCacheDiskMisses OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of cache misses on a paricular disk"
    ::= { fgWebCacheDiskStatsEntry 5 }

fgWebCacheDiskFailure OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of disk failures on a paricular disk from webcache disk daemon"
    ::= { fgWebCacheDiskStatsEntry 6 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWanOpt
--

fgAppWanOpt OBJECT IDENTIFIER
    ::= { fgApplications 114 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWanOpt.fgWanOptInfo
--

fgWanOptInfo OBJECT IDENTIFIER
    ::= { fgAppWanOpt 1 }

fgMemCacheLimit OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RAM available for mem cache in bytes"
    ::= { fgWanOptInfo 1 }

fgMemCacheUsage OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RAM used by mem cache in bytes"
    ::= { fgWanOptInfo 2 }

fgMemCacheHits OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of hits in mem cache since last reset"
    ::= { fgWanOptInfo 3 }

fgMemCacheMisses OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of misses in mem cache since last reset"
    ::= { fgWanOptInfo 4 }

fgByteCacheRAMLimit OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RAM available for byte cache in bytes"
    ::= { fgWanOptInfo 5 }

fgByteCacheRAMUsage OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RAM used by byte cache in bytes"
    ::= { fgWanOptInfo 6 }

fgWanOptUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wan Optimization up-time (in seconds)"
    ::= { fgWanOptInfo 7 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWanOpt.fgWanOptStatsTable
--

fgWanOptStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWanOptStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of WAN optimization statistics per virtual domain"
    ::= { fgAppWanOpt 2 }

fgWanOptStatsEntry OBJECT-TYPE
    SYNTAX      FgWanOptStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "WAN optimization statistics for a particular virtual domain"
    INDEX       { fgVdEntIndex }
    ::= { fgWanOptStatsTable 1 }

FgWanOptStatsEntry ::= SEQUENCE {
    fgWanOptTunnels     Gauge32,
    fgWanOptLANBytesIn  Gauge32,
    fgWanOptLANBytesOut Gauge32,
    fgWanOptWANBytesIn  Gauge32,
    fgWanOptWANBytesOut Gauge32
}

fgWanOptTunnels OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of current tunnels in this virtual domain"
    ::= { fgWanOptStatsEntry 1 }

fgWanOptLANBytesIn OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes received on LAN in last 5 seconds"
    ::= { fgWanOptStatsEntry 2 }

fgWanOptLANBytesOut OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes sent on LAN in last 5 seconds"
    ::= { fgWanOptStatsEntry 3 }

fgWanOptWANBytesIn OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes received on WAN in last 5 seconds"
    ::= { fgWanOptStatsEntry 4 }

fgWanOptWANBytesOut OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes sent on WAN in last 5 seconds"
    ::= { fgWanOptStatsEntry 5 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWanOpt.fgWanOptHistoryStatsTable
--

fgWanOptHistoryStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWanOptHistoryStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of the WAN optimization history per protocol"
    ::= { fgAppWanOpt 3 }

fgWanOptHistoryStatsEntry OBJECT-TYPE
    SYNTAX      FgWanOptHistoryStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "The WAN optimization history for a particular virtual domain, period, and protocol"
    INDEX       { fgVdEntIndex, fgWanOptHistPeriod, fgWanOptProtocol }
    ::= { fgWanOptHistoryStatsTable 1 }

FgWanOptHistoryStatsEntry ::= SEQUENCE {
    fgWanOptHistPeriod          FgWanOptHistPeriods,
    fgWanOptProtocol            FgWanOptProtocols,
    fgWanOptReductionRate       Gauge32,
    fgWanOptLanTraffic          CounterBasedGauge64,
    fgWanOptWanTraffic          CounterBasedGauge64
}

fgWanOptHistPeriod OBJECT-TYPE
    SYNTAX      FgWanOptHistPeriods
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "WAN optimization table entry period"
    ::= { fgWanOptHistoryStatsEntry 1 }

fgWanOptProtocol OBJECT-TYPE
    SYNTAX      FgWanOptProtocols
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Internal WAN optimization table entry protocol"
    ::= { fgWanOptHistoryStatsEntry 2 }

fgWanOptReductionRate OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Reduction rate achieved by WAN optimization"
    ::= { fgWanOptHistoryStatsEntry 3 }

fgWanOptLanTraffic OBJECT-TYPE
    SYNTAX      CounterBasedGauge64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes transferred via LAN"
    ::= { fgWanOptHistoryStatsEntry 4 }

fgWanOptWanTraffic OBJECT-TYPE
    SYNTAX      CounterBasedGauge64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes transferred via WAN"
    ::= { fgWanOptHistoryStatsEntry 5 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWanOpt.fgWanOptTrafficStatsTable
--

fgWanOptTrafficStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWanOptTrafficStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of the WAN optimization traffic for a particular virtual domain and protocol"
    ::= { fgAppWanOpt 4 }

fgWanOptTrafficStatsEntry OBJECT-TYPE
    SYNTAX      FgWanOptTrafficStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "The WAN optimization history for a particular protocol"
    INDEX       { fgVdEntIndex, fgWanOptProtocol }
    ::= { fgWanOptTrafficStatsTable 1 }

FgWanOptTrafficStatsEntry ::= SEQUENCE {
    fgWanOptLanInTraffic        Counter64,
    fgWanOptLanOutTraffic       Counter64,
    fgWanOptWanInTraffic        Counter64,
    fgWanOptWanOutTraffic       Counter64
}

fgWanOptLanInTraffic OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Amount of traffic received from the LAN by WAN optimization"
    ::= { fgWanOptTrafficStatsEntry 1 }

fgWanOptLanOutTraffic OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Amount of traffic sent to the LAN by WAN optimization"
    ::= { fgWanOptTrafficStatsEntry 2 }

fgWanOptWanInTraffic OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Amount of traffic received from the WAN by WAN optimization"
    ::= { fgWanOptTrafficStatsEntry 3 }

fgWanOptWanOutTraffic OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Amount of traffic sent to the WAN by WAN optimization"
    ::= { fgWanOptTrafficStatsEntry 4 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppWanOpt.fgWanOptDiskStatsTable
--

fgWanOptDiskStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWanOptDiskStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of the Web Cache disk statistics per disk"
    ::= { fgAppWanOpt 5 }

fgWanOptDiskStatsEntry OBJECT-TYPE
    SYNTAX      FgWanOptDiskStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "The Web Cache disk statistics for a particular disk"
    INDEX       { fgWanOptDisk }
    ::= { fgWanOptDiskStatsTable 1 }

FgWanOptDiskStatsEntry ::= SEQUENCE {
    fgWanOptDisk              Unsigned32,
    fgWanOptDiskLimit         CounterBasedGauge64,
    fgWanOptDiskUsage         CounterBasedGauge64,
    fgWanOptDiskHits          Counter32,
    fgWanOptDiskMisses        Counter32,
    fgWanOptDiskFailure       Counter64
}

fgWanOptDisk OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The Web Cache Disk index"
    ::= { fgWanOptDiskStatsEntry 1 }

fgWanOptDiskLimit OBJECT-TYPE
    SYNTAX     CounterBasedGauge64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The about of storage (in bytes) available for the Web Cache on a particular disk"
    ::= { fgWanOptDiskStatsEntry 2 }

fgWanOptDiskUsage OBJECT-TYPE
    SYNTAX     CounterBasedGauge64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The about of storage (in bytes) in use by the Web Cache on a paricular disk"
    ::= { fgWanOptDiskStatsEntry 3 }

fgWanOptDiskHits OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of cache hits on a paricular disk"
    ::= { fgWanOptDiskStatsEntry 4 }

fgWanOptDiskMisses OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of cache misses on a paricular disk"
    ::= { fgWanOptDiskStatsEntry 5 }

fgWanOptDiskFailure OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of disk failures on a paricular disk from wanopt disk daemon"
    ::= { fgWanOptDiskStatsEntry 6 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppDNSProxy
--

fgAppDNSProxy OBJECT IDENTIFIER
    ::= { fgApplications 115 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppDNSProxy.fgDNSProxyStatsInfo
--

fgDNSProxyStatsInfo OBJECT IDENTIFIER
    ::= { fgAppDNSProxy 1 }

fgDNSProxyStatsUdpCacheHit OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of DNS cache hit"
    ::= { fgDNSProxyStatsInfo 1 }

fgDNSProxyStatsUdpRatingCacheHit OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of secure-DNS cache hit"
    ::= { fgDNSProxyStatsInfo 2 }

fgDNSProxyStatsUdpReq OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of DNS request"
    ::= { fgDNSProxyStatsInfo 3 }

fgDNSProxyStatsUdpRes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of DNS response"
    ::= { fgDNSProxyStatsInfo 4 }

fgDNSProxyStatsUdpFwd OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of DNS forwarded"
    ::= { fgDNSProxyStatsInfo 5 }

fgDNSProxyStatsUdpRetrans OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of DNS retransmission"
    ::= { fgDNSProxyStatsInfo 6 }

fgDNSProxyStatsUdpTo OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of DNS timeout"
    ::= { fgDNSProxyStatsInfo 7 }

fgDNSProxyStatsUdpFtgRes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of secure-DNS response"
    ::= { fgDNSProxyStatsInfo 8 }

fgDNSProxyStatsUdpFtgFwd OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of secure-DNS forwarded"
    ::= { fgDNSProxyStatsInfo 9 }

fgDNSProxyStatsUdpFtgRetrans OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of secure-DNS retransmission"
    ::= { fgDNSProxyStatsInfo 10 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppFnbam
--

fgAppFnbam OBJECT IDENTIFIER
    ::= { fgApplications 116 }

--
-- fortinet.fnFortiGateMib.fgApplications.fgAppFnbam.fgAppFnbamStatsInfo
--

fgAppFnbamStatsInfo OBJECT IDENTIFIER
    ::= { fgAppFnbam 1 }

fgAppFnbamStatsTotalAuthReqs OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of total authentication requests"
    ::= { fgAppFnbamStatsInfo 1 }

fgAppFnbamStatsTotalEagainErrs OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of total EAGAIN errors"
    ::= { fgAppFnbamStatsInfo 2 }

fgAppFnbamStatsTotalLdapFails OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of total LDAP connection failures"
    ::= { fgAppFnbamStatsInfo 3 }

--
-- fortinet.fnFortiGateMib.fgInetProto
--

fgInetProto OBJECT IDENTIFIER
    ::= { fnFortiGateMib 11 }

fgInetProtoInfo OBJECT IDENTIFIER
    ::= { fgInetProto 1 }

fgInetProtoTables OBJECT IDENTIFIER
    ::= { fgInetProto 2 }

--
-- fortinet.fnFortiGateMib.fgInetProto.fgInetProtoTables.fgIpSessTable
--

fgIpSessTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIpSessEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Information on the IP sessions active on the device"
    ::= { fgInetProtoTables 1 }

fgIpSessEntry OBJECT-TYPE
    SYNTAX      FgIpSessEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Information on a specific session, including source and destination"
    INDEX       { fgIpSessIndex }
    ::= { fgIpSessTable 1 }

FgIpSessEntry ::= SEQUENCE {
    fgIpSessIndex       FnIndex,
    fgIpSessProto       FgSessProto,
    fgIpSessFromAddr    IpAddress,
    fgIpSessFromPort    InetPortNumber,
    fgIpSessToAddr      IpAddress,
    fgIpSessToPort      InetPortNumber,
    fgIpSessExp         Gauge32,
    fgIpSessVdom        FgVdIndex
}

fgIpSessIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         an IP session within the fgIpSessTable"
    ::= { fgIpSessEntry 1 }

fgIpSessProto OBJECT-TYPE
    SYNTAX      FgSessProto
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The protocol the session is using (IP, TCP, UDP, etc.)"
    ::= { fgIpSessEntry 2 }

fgIpSessFromAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Source IP address (IPv4 only) of the session"
    ::= { fgIpSessEntry 3 }

fgIpSessFromPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Source port number (UDP and TCP only) of the session"
    ::= { fgIpSessEntry 4 }

fgIpSessToAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Destination IP address (IPv4 only) of the session"
    ::= { fgIpSessEntry 5 }

fgIpSessToPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Destination Port number (UDP and TCP only) of the session"
    ::= { fgIpSessEntry 6 }

fgIpSessExp OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of seconds remaining before the session expires (if idle)"
    ::= { fgIpSessEntry 7 }

fgIpSessVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virtual domain the session is part of. This index corresponds to the index used by fgVdTable."
    ::= { fgIpSessEntry 8 }

--
-- fortinet.fnFortiGateMib.fgInetProto.fgInetProtoTables.fgIpSessStatsTable
--

fgIpSessStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIpSessStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IP session statistics table"
    ::= { fgInetProtoTables 2 }

fgIpSessStatsEntry OBJECT-TYPE
    SYNTAX      FgIpSessStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IP session statistics on a virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgIpSessStatsTable 1 }

FgIpSessStatsEntry ::= SEQUENCE {
    fgIpSessNumber  Gauge32
}

fgIpSessNumber OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current number of sessions on the virtual domain"
    ::= { fgIpSessStatsEntry 1 }

--
-- fortinet.fnFortiGateMib.fgInetProto.fgInetProtoTables.fgIp6SessStatsTable
--

fgIp6SessStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgIp6SessStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IP session statistics table"
    ::= { fgInetProtoTables 3 }

fgIp6SessStatsEntry OBJECT-TYPE
    SYNTAX      FgIp6SessStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IP session statistics on a virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgIp6SessStatsTable 1 }

FgIp6SessStatsEntry ::= SEQUENCE {
    fgIp6SessNumber  Gauge32
}

fgIp6SessNumber OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current number of sessions on the virtual domain"
    ::= { fgIp6SessStatsEntry 1 }


--
-- fortinet.fnFortiGateMib.fgVpn
--

fgVpn OBJECT IDENTIFIER
    ::= { fnFortiGateMib 12 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpnInfo
--

fgVpnInfo OBJECT IDENTIFIER
    ::= { fgVpn 1 }

fgVpnTunnelUpCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of IPsec VPN tunnels with at least one SA"
    ::= { fgVpnInfo 1 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpnTables
--

fgVpnTables OBJECT IDENTIFIER
    ::= { fgVpn 2 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpnTables.fgVpnDialupTable
--

fgVpnDialupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVpnDialupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Dial-up VPN peers information"
    ::= { fgVpnTables 1 }

fgVpnDialupEntry OBJECT-TYPE
    SYNTAX      FgVpnDialupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Dial-up VPN peer info"
    INDEX       { fgVpnDialupIndex }
    ::= { fgVpnDialupTable 1 }

FgVpnDialupEntry ::= SEQUENCE {
    fgVpnDialupIndex        FnIndex,
    fgVpnDialupGateway      IpAddress,
    fgVpnDialupLifetime     Integer32,
    fgVpnDialupTimeout      Integer32,
    fgVpnDialupSrcBegin     IpAddress,
    fgVpnDialupSrcEnd       IpAddress,
    fgVpnDialupDstAddr      IpAddress,
    fgVpnDialupVdom         FgVdIndex,
    fgVpnDialupInOctets     Counter64,
    fgVpnDialupOutOctets    Counter64
}

fgVpnDialupIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         an VPN dial-up peer within the fgVpnDialupTable"
    ::= { fgVpnDialupEntry 1 }

fgVpnDialupGateway OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Remote gateway IP address of the tunnel"
    ::= { fgVpnDialupEntry 2 }

fgVpnDialupLifetime OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tunnel life time (seconds) of the tunnel"
    ::= { fgVpnDialupEntry 3 }

fgVpnDialupTimeout OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Time before the next key exchange (seconds) of the tunnel"
    ::= { fgVpnDialupEntry 4 }

fgVpnDialupSrcBegin OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Remote subnet address of the tunnel"
    ::= { fgVpnDialupEntry 5 }

fgVpnDialupSrcEnd OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Remote subnet mask of the tunnel"
    ::= { fgVpnDialupEntry 6 }

fgVpnDialupDstAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Local subnet address of the tunnel"
    ::= { fgVpnDialupEntry 7 }

fgVpnDialupVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virtual domain tunnel is part of. This index corresponds to the index used by fgVdTable."
    ::= { fgVpnDialupEntry 8 }

fgVpnDialupInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes received on tunnel since instantiation."
    ::= { fgVpnDialupEntry 9 }

fgVpnDialupOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes sent on tunnel since instantiation."
    ::= { fgVpnDialupEntry 10 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpnTables.fgVpnTunTable
--

fgVpnTunTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVpnTunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Table of non-dial-up IPsec VPN tunnels"
    ::= { fgVpnTables 2 }

fgVpnTunEntry OBJECT-TYPE
    SYNTAX      FgVpnTunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Tunnel VPN peer info"
    INDEX       { fgVpnTunEntIndex, fgVpnTunEntPhase2Index }
    ::= { fgVpnTunTable 1 }

FgVpnTunEntry ::= SEQUENCE {
    fgVpnTunEntIndex                FnIndex,
    fgVpnTunEntPhase1Name           DisplayString,
    fgVpnTunEntPhase2Name           DisplayString,
    fgVpnTunEntRemGwyIp             IpAddress,
    fgVpnTunEntRemGwyPort           InetPortNumber,
    fgVpnTunEntLocGwyIp             IpAddress,
    fgVpnTunEntLocGwyPort           InetPortNumber,
    fgVpnTunEntSelectorSrcBeginIp   IpAddress,
    fgVpnTunEntSelectorSrcEndIp     IpAddress,
    fgVpnTunEntSelectorSrcPort      InetPortNumber,
    fgVpnTunEntSelectorDstBeginIp   IpAddress,
    fgVpnTunEntSelectorDstEndIp     IpAddress,
    fgVpnTunEntSelectorDstPort      InetPortNumber,
    fgVpnTunEntSelectorProto        Integer32,
    fgVpnTunEntLifeSecs             Gauge32,
    fgVpnTunEntLifeBytes            Gauge32,
    fgVpnTunEntTimeout              Gauge32,
    fgVpnTunEntInOctets             Counter64,
    fgVpnTunEntOutOctets            Counter64,
    fgVpnTunEntStatus               INTEGER,
    fgVpnTunEntVdom                 FgVdIndex,
    fgVpnTunEntPhase2Index          FnIndex
}

fgVpnTunEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         a VPN tunnel within the fgVpnTunTable"
    ::= { fgVpnTunEntry 1 }

fgVpnTunEntPhase1Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Descriptive name of phase1 configuration for the tunnel"
    ::= { fgVpnTunEntry 2 }

fgVpnTunEntPhase2Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Descriptive name of phase2 configuration for the tunnel"
    ::= { fgVpnTunEntry 3 }

fgVpnTunEntRemGwyIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IP of remote gateway used by the tunnel"
    ::= { fgVpnTunEntry 4 }

fgVpnTunEntRemGwyPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "port of remote gateway used by tunnel, if UDP"
    ::= { fgVpnTunEntry 5 }

fgVpnTunEntLocGwyIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IP of local gateway used by the tunnel"
    ::= { fgVpnTunEntry 6 }

fgVpnTunEntLocGwyPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "port of local gateway used by tunnel, if UDP"
    ::= { fgVpnTunEntry 7 }

fgVpnTunEntSelectorSrcBeginIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning of address range of source selector"
    ::= { fgVpnTunEntry 8 }

fgVpnTunEntSelectorSrcEndIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End of address range of source selector"
    ::= { fgVpnTunEntry 9 }

fgVpnTunEntSelectorSrcPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Source selector port"
    ::= { fgVpnTunEntry 10 }

fgVpnTunEntSelectorDstBeginIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning of address range of destination
         selector"
    ::= { fgVpnTunEntry 11 }

fgVpnTunEntSelectorDstEndIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End of address range of destination selector"
    ::= { fgVpnTunEntry 12 }

fgVpnTunEntSelectorDstPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Destination selector port"
    ::= { fgVpnTunEntry 13 }

fgVpnTunEntSelectorProto OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Protocol number for selector"
    ::= { fgVpnTunEntry 14 }

fgVpnTunEntLifeSecs OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Lifetime of tunnel in seconds, if time based lifetime used"
    ::= { fgVpnTunEntry 15 }

fgVpnTunEntLifeBytes OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Lifetime of tunnel in bytes, if byte transfer based lifetime used"
    ::= { fgVpnTunEntry 16 }

fgVpnTunEntTimeout OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Timeout of tunnel in seconds"
    ::= { fgVpnTunEntry 17 }

fgVpnTunEntInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes received on tunnel"
    ::= { fgVpnTunEntry 18 }

fgVpnTunEntOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes sent out on tunnel"
    ::= { fgVpnTunEntry 19 }

fgVpnTunEntStatus OBJECT-TYPE
    SYNTAX      INTEGER { down(1), up(2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current status of tunnel (up or down)"
    ::= { fgVpnTunEntry 20 }

fgVpnTunEntVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virtual domain the tunnel is part of. This index corresponds to the index used by fgVdTable."
    ::= { fgVpnTunEntry 21 }

fgVpnTunEntPhase2Index OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         a Phase2 SA on a VPN tunnel within the fgVpnTunTable"
    ::= { fgVpnTunEntry 22 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpnTables.fgVpnSslStatsTable
--

fgVpnSslStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVpnSslStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "SSL VPN statistics table"
    ::= { fgVpnTables 3 }

fgVpnSslStatsEntry OBJECT-TYPE
    SYNTAX      FgVpnSslStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "SSL VPN statistics for a given virtual domain"
    AUGMENTS    { fgVdEntry }
    ::= { fgVpnSslStatsTable 1 }

FgVpnSslStatsEntry ::= SEQUENCE {
    fgVpnSslState                   FnBoolState,
    fgVpnSslStatsLoginUsers         Gauge32,
    fgVpnSslStatsMaxUsers           Counter32,
    fgVpnSslStatsActiveWebSessions  Gauge32,
    fgVpnSslStatsMaxWebSessions     Counter32,
    fgVpnSslStatsActiveTunnels      Gauge32,
    fgVpnSslStatsMaxTunnels         Counter32
}

fgVpnSslState OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Whether SSL-VPN is enabled on this virtual domain"
    ::= { fgVpnSslStatsEntry 1 }

fgVpnSslStatsLoginUsers OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The current number of users logged in through SSL-VPN tunnels in the virtual domain"
    ::= { fgVpnSslStatsEntry 2 }

fgVpnSslStatsMaxUsers OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The maximum number of total users that can be logged in
         at any one time on the virtual domain"
    ::= { fgVpnSslStatsEntry 3 }

fgVpnSslStatsActiveWebSessions OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The current number of active SSL web sessions in the virtual domain"
    ::= { fgVpnSslStatsEntry 4 }

fgVpnSslStatsMaxWebSessions OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The maximum number of active SSL web sessions
         at any one time within the virtual domain"
    ::= { fgVpnSslStatsEntry 5 }

fgVpnSslStatsActiveTunnels OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The current number of active SSL tunnels in the virtual domain"
    ::= { fgVpnSslStatsEntry 6 }

fgVpnSslStatsMaxTunnels OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The maximum number of active SSL tunnels
         at any one time in the virtual domain"
    ::= { fgVpnSslStatsEntry 7 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpnTables.fgVpnSslTunnelTable
--

fgVpnSslTunnelTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVpnSslTunnelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A list of active SSL VPN tunnel entries"
    ::= { fgVpnTables 4 }

fgVpnSslTunnelEntry OBJECT-TYPE
    SYNTAX      FgVpnSslTunnelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An SSL VPN tunnel entry containing connection information
         and traffic statistics"
    INDEX       { fgVpnSslTunnelIndex }
    ::= { fgVpnSslTunnelTable 1 }

FgVpnSslTunnelEntry ::= SEQUENCE {
    fgVpnSslTunnelIndex     FnIndex,
    fgVpnSslTunnelVdom      FgVdIndex,
    fgVpnSslTunnelUserName  DisplayString,
    fgVpnSslTunnelSrcIp     IpAddress,
    fgVpnSslTunnelIp        IpAddress,
    fgVpnSslTunnelUpTime    Counter32,
    fgVpnSslTunnelBytesIn   Counter64,
    fgVpnSslTunnelBytesOut  Counter64
}

fgVpnSslTunnelIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         an active SSL VPN tunnel within the fgVpnSslTunnelTable"
    ::= { fgVpnSslTunnelEntry 1 }

fgVpnSslTunnelVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The index of the virtual domain this tunnel belongs to. This index corresponds to the index used by fgVdTable."
    ::= { fgVpnSslTunnelEntry 2 }

fgVpnSslTunnelUserName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The user name used to authenticate the tunnel"
    ::= { fgVpnSslTunnelEntry 3 }

fgVpnSslTunnelSrcIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The source IP address of this tunnel"
    ::= { fgVpnSslTunnelEntry 4 }

fgVpnSslTunnelIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The connection IP address of this tunnel"
    ::= { fgVpnSslTunnelEntry 5 }

fgVpnSslTunnelUpTime OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The up-time of this tunnel in seconds"
    ::= { fgVpnSslTunnelEntry 6 }

fgVpnSslTunnelBytesIn OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of incoming bytes of L2 traffic through this tunnel since it was established"
    ::= { fgVpnSslTunnelEntry 7 }

fgVpnSslTunnelBytesOut OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of outgoing bytes of L2 traffic through this tunnel since it was established"
    ::= { fgVpnSslTunnelEntry 8 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpnTrapObjects
--

fgVpnTrapObjects OBJECT IDENTIFIER
    ::= { fgVpn 3 }

fgVpnTrapLocalGateway OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Local gateway IP address. Used in VPN related traps."
    ::= { fgVpnTrapObjects 2 }

fgVpnTrapRemoteGateway OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Remote gateway IP address. Used in VPN related traps."
    ::= { fgVpnTrapObjects 3 }

fgVpnTrapPhase1Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Name of the phase 1. Used in VPN related traps."
    ::= { fgVpnTrapObjects 4 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpn2Tables
--

fgVpn2Tables OBJECT IDENTIFIER
    ::= { fgVpn 4 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpn2Tables.fgVpn2DialupTable
--

fgVpn2DialupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVpn2DialupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Dial-up VPN peers information"
    ::= { fgVpn2Tables 1 }

fgVpn2DialupEntry OBJECT-TYPE
    SYNTAX      FgVpn2DialupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Dial-up VPN peer info"
    INDEX       { fgVpn2DialupIndex }
    ::= { fgVpn2DialupTable 1 }

FgVpn2DialupEntry ::= SEQUENCE {
    fgVpn2DialupIndex        FnIndex,
    fgVpn2DialupGatewayType  InetAddressType,
    fgVpn2DialupGateway      InetAddress,
    fgVpn2DialupLifetime     Integer32,
    fgVpn2DialupTimeout      Integer32,
    fgVpn2DialupSrcBeginType InetAddressType,
    fgVpn2DialupSrcBegin     InetAddress,
    fgVpn2DialupSrcEndType   InetAddressType,
    fgVpn2DialupSrcEnd       InetAddress,
    fgVpn2DialupDstBeginType InetAddressType,
    fgVpn2DialupDstBegin     InetAddress,
    fgVpn2DialupDstEndType   InetAddressType,
    fgVpn2DialupDstEnd       InetAddress,
    fgVpn2DialupInOctets     Counter64,
    fgVpn2DialupOutOctets    Counter64,
    fgVpn2DialupPhase1Name   DisplayString,
    fgVpn2DialupVdom         FgVdIndex
}

fgVpn2DialupIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         an VPN dial-up peer within the fgVpn2DialupTable"
    ::= { fgVpn2DialupEntry 1 }

fgVpn2DialupGatewayType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Remote gateway address type of the tunnel"
    ::= { fgVpn2DialupEntry 2 }

fgVpn2DialupGateway OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Remote gateway address of the tunnel"
    ::= { fgVpn2DialupEntry 3 }

fgVpn2DialupLifetime OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tunnel life time (seconds) of the tunnel"
    ::= { fgVpn2DialupEntry 4 }

fgVpn2DialupTimeout OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Time before the next key exchange (seconds) of the tunnel"
    ::= { fgVpn2DialupEntry 5 }

fgVpn2DialupSrcBeginType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning's IP type of remote address range of the tunnel"
    ::= { fgVpn2DialupEntry 6 }

fgVpn2DialupSrcBegin OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning of remote address range of the tunnel"
    ::= { fgVpn2DialupEntry 7 }

fgVpn2DialupSrcEndType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End's IP type of remote address range of the tunnel"
    ::= { fgVpn2DialupEntry 8 }

fgVpn2DialupSrcEnd OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End of remote address range of the tunnel"
    ::= { fgVpn2DialupEntry 9 }

fgVpn2DialupDstBeginType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning's IP type of local address range of the tunnel"
    ::= { fgVpn2DialupEntry 10 }

fgVpn2DialupDstBegin OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning of local address range of the tunnel"
    ::= { fgVpn2DialupEntry 11 }

fgVpn2DialupDstEndType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End's IP type of local address range of the tunnel"
    ::= { fgVpn2DialupEntry 12 }

fgVpn2DialupDstEnd OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End of local address range of the tunnel"
    ::= { fgVpn2DialupEntry 13 }

fgVpn2DialupInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes received on tunnel since instantiation."
    ::= { fgVpn2DialupEntry 14 }

fgVpn2DialupOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes sent on tunnel since instantiation."
    ::= { fgVpn2DialupEntry 15 }

fgVpn2DialupPhase1Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Descriptive name of phase1 configuration for the tunnel"
    ::= { fgVpn2DialupEntry 16 }

fgVpn2DialupVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virtual domain tunnel is part of. This index corresponds to the index used by fgVdTable."
    ::= { fgVpn2DialupEntry 17 }

--
-- fortinet.fnFortiGateMib.fgVpn.fgVpn2Tables.fgVpn2TunTable
--

fgVpn2TunTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgVpn2TunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Table of non-dial-up IPsec VPN tunnels"
    ::= { fgVpn2Tables 2 }

fgVpn2TunEntry OBJECT-TYPE
    SYNTAX      FgVpn2TunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "IP tunnel VPN peer info"
    INDEX       { fgVpn2TunIndex, fgVpn2TunPhase2Index }
    ::= { fgVpn2TunTable 1 }

FgVpn2TunEntry ::= SEQUENCE {
    fgVpn2TunIndex                FnIndex,
    fgVpn2TunPhase1Name           DisplayString,
    fgVpn2TunPhase2Name           DisplayString,
    fgVpn2TunRemGwyIpType         InetAddressType,
    fgVpn2TunRemGwyIp             InetAddress,
    fgVpn2TunRemGwyPort           InetPortNumber,
    fgVpn2TunLocGwyIpType         InetAddressType,
    fgVpn2TunLocGwyIp             InetAddress,
    fgVpn2TunLocGwyPort           InetPortNumber,
    fgVpn2TunSelSrcBeginIpType    InetAddressType,
    fgVpn2TunSelSrcBeginIp        InetAddress,
    fgVpn2TunSelSrcEndIpType      InetAddressType,
    fgVpn2TunSelSrcEndIp          InetAddress,
    fgVpn2TunSelSrcPort           InetPortNumber,
    fgVpn2TunSelDstBeginIpType    InetAddressType,
    fgVpn2TunSelDstBeginIp        InetAddress,
    fgVpn2TunSelDstEndIpType      InetAddressType,
    fgVpn2TunSelDstEndIp          InetAddress,
    fgVpn2TunSelDstPort           InetPortNumber,
    fgVpn2TunSelProto             Integer32,
    fgVpn2TunLifeSecs             Gauge32,
    fgVpn2TunLifeBytes            Gauge32,
    fgVpn2TunTimeout              Gauge32,
    fgVpn2TunInOctets             Counter64,
    fgVpn2TunOutOctets            Counter64,
    fgVpn2TunStatus               INTEGER,
    fgVpn2TunVdom                 FgVdIndex,
    fgVpn2TunPhase2Index          FnIndex
}

fgVpn2TunIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         a VPN tunnel within the fgVpn2TunTable"
    ::= { fgVpn2TunEntry 1 }

fgVpn2TunPhase1Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Descriptive name of phase1 configuration for the tunnel"
    ::= { fgVpn2TunEntry 2 }

fgVpn2TunPhase2Name OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Descriptive name of phase2 configuration for the tunnel"
    ::= { fgVpn2TunEntry 3 }

fgVpn2TunRemGwyIpType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IP type of remote gateway used by the tunnel"
    ::= { fgVpn2TunEntry 4 }

fgVpn2TunRemGwyIp OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IP of remote gateway used by the tunnel"
    ::= { fgVpn2TunEntry 5 }

fgVpn2TunRemGwyPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "port of remote gateway used by tunnel, if UDP"
    ::= { fgVpn2TunEntry 6 }

fgVpn2TunLocGwyIpType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IP type of local gateway used by the tunnel"
    ::= { fgVpn2TunEntry 7 }

fgVpn2TunLocGwyIp OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IP of local gateway used by the tunnel"
    ::= { fgVpn2TunEntry 8 }

fgVpn2TunLocGwyPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "port of local gateway used by tunnel, if UDP"
    ::= { fgVpn2TunEntry 9 }

fgVpn2TunSelSrcBeginIpType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning's IP type of address range of source selector"
    ::= { fgVpn2TunEntry 10 }

fgVpn2TunSelSrcBeginIp OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning of address range of source selector"
    ::= { fgVpn2TunEntry 11 }

fgVpn2TunSelSrcEndIpType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End's IP type of address range of source selector"
    ::= { fgVpn2TunEntry 12 }

fgVpn2TunSelSrcEndIp OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End of address range of source selector"
    ::= { fgVpn2TunEntry 13 }

fgVpn2TunSelSrcPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Source selector port"
    ::= { fgVpn2TunEntry 14 }

fgVpn2TunSelDstBeginIpType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning's IP type of address range of destination"
    ::= { fgVpn2TunEntry 15 }

fgVpn2TunSelDstBeginIp OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Beginning of address range of destination selector"
    ::= { fgVpn2TunEntry 16 }

fgVpn2TunSelDstEndIpType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End's IP type of address range of destination"
    ::= { fgVpn2TunEntry 17 }

fgVpn2TunSelDstEndIp OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "End of address range of destination selector"
    ::= { fgVpn2TunEntry 18 }

fgVpn2TunSelDstPort OBJECT-TYPE
    SYNTAX      InetPortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Destination selector port"
    ::= { fgVpn2TunEntry 19 }

fgVpn2TunSelProto OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Protocol number for selector"
    ::= { fgVpn2TunEntry 20 }

fgVpn2TunLifeSecs OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Lifetime of tunnel in seconds, if time based lifetime used"
    ::= { fgVpn2TunEntry 21 }

fgVpn2TunLifeBytes OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Lifetime of tunnel in bytes, if byte transfer based lifetime used"
    ::= { fgVpn2TunEntry 22 }

fgVpn2TunTimeout OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Timeout of tunnel in seconds"
    ::= { fgVpn2TunEntry 23 }

fgVpn2TunInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes received on tunnel"
    ::= { fgVpn2TunEntry 24 }

fgVpn2TunOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes sent out on tunnel"
    ::= { fgVpn2TunEntry 25 }

fgVpn2TunStatus OBJECT-TYPE
    SYNTAX      INTEGER { down(1), up(2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current status of tunnel (up or down)"
    ::= { fgVpn2TunEntry 26 }

fgVpn2TunVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Virtual domain the tunnel is part of. This index corresponds to the index used by fgVdTable."
    ::= { fgVpn2TunEntry 27 }

fgVpn2TunPhase2Index OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies
         a Phase2 SA on a VPN tunnel within the fgVpn2TunTable"
    ::= { fgVpn2TunEntry 28 }

--
-- fortinet.fnFortiGateMib.fgHighAvailability
--

fgHighAvailability OBJECT IDENTIFIER
    ::= { fnFortiGateMib 13 }

--
-- fortinet.fnFortiGateMib.fgHighAvailability.fgHaInfo
--

fgHaInfo OBJECT IDENTIFIER
    ::= { fgHighAvailability 1 }

fgHaSystemMode OBJECT-TYPE
    SYNTAX      FgHaMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "High-availability mode (Standalone, A-A or A-P)"
    ::= { fgHaInfo 1 }

fgHaGroupId OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "HA cluster group ID device is configured for"
    ::= { fgHaInfo 2 }

fgHaPriority OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "HA clustering priority of the device (default = 127)"
    ::= { fgHaInfo 3 }

fgHaOverride OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Status of a primary override flag"
    ::= { fgHaInfo 4 }

fgHaAutoSync OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Configuration of an automatic configuration
         synchronization (enabled or disabled)"
    ::= { fgHaInfo 5 }

fgHaSchedule OBJECT-TYPE
    SYNTAX      FgHaLBSchedule
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Load-balancing schedule of cluster (in A-A mode)"
    ::= { fgHaInfo 6 }

fgHaGroupName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "HA cluster group name"
    ::= { fgHaInfo 7 }

--
-- fortinet.fnFortiGateMib.fgHighAvailability.fgHaTables
--

fgHaTables OBJECT IDENTIFIER
    ::= { fgHighAvailability 2 }

fgHaStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgHaStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Some useful statistics for all members of a cluster. This table is also available in standalone mode."
    ::= { fgHaTables 1 }

fgHaStatsEntry OBJECT-TYPE
    SYNTAX      FgHaStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Statistics for a particular HA cluster's unit"
    INDEX       { fgHaStatsIndex }
    ::= { fgHaStatsTable 1 }

FgHaStatsEntry ::= SEQUENCE {
    fgHaStatsIndex      FnIndex,
    fgHaStatsSerial     DisplayString,
    fgHaStatsCpuUsage   Gauge32,
    fgHaStatsMemUsage   Gauge32,
    fgHaStatsNetUsage   Gauge32,
    fgHaStatsSesCount   Gauge32,
    fgHaStatsPktCount   Counter32,
    fgHaStatsByteCount  Counter32,
    fgHaStatsIdsCount   Counter32,
    fgHaStatsAvCount    Counter32,
    fgHaStatsHostname   DisplayString,
    fgHaStatsSyncStatus         FgHaStatsSyncStatusType,
    fgHaStatsSyncDatimeSucc     DateAndTime,
    fgHaStatsSyncDatimeUnsucc   DateAndTime,
    fgHaStatsGlobalChecksum     DisplayString,
    fgHaStatsPrimarySerial      DisplayString,
    fgHaStatsAllChecksum        DisplayString
}

fgHaStatsIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An index value that uniquely identifies an
         unit in the HA Cluster"
    ::= { fgHaStatsEntry 1 }

fgHaStatsSerial OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Serial number of the HA cluster member for this row"
    ::= { fgHaStatsEntry 2 }

fgHaStatsCpuUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "CPU usage of the specified cluster member (percentage)"
    ::= { fgHaStatsEntry 3 }

fgHaStatsMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Memory usage of the specified cluster member (percentage)"
    ::= { fgHaStatsEntry 4 }

fgHaStatsNetUsage OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Network bandwidth usage of specified cluster member (kbps)"
    ::= { fgHaStatsEntry 5 }

fgHaStatsSesCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current session count of specified cluster member"
    ::= { fgHaStatsEntry 6 }

fgHaStatsPktCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of packets processed by the specified cluster member since start-up"
    ::= { fgHaStatsEntry 7 }

fgHaStatsByteCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of bytes processed by the specified cluster member since start-up"
    ::= { fgHaStatsEntry 8 }

fgHaStatsIdsCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of IDS/IPS events triggered on the specified cluster member since start-up"
    ::= { fgHaStatsEntry 9 }

fgHaStatsAvCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of anti-virus events triggered on the specified cluster member since start-up"
    ::= { fgHaStatsEntry 10 }

fgHaStatsHostname OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Host name of the specified cluster member"
    ::= { fgHaStatsEntry 11 }

fgHaStatsSyncStatus OBJECT-TYPE
    SYNTAX      FgHaStatsSyncStatusType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current HA Sync status"
    ::= { fgHaStatsEntry 12 }

fgHaStatsSyncDatimeSucc OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Date and time of last successful sync"
    ::= { fgHaStatsEntry 13 }

fgHaStatsSyncDatimeUnsucc OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Date and time of last unsuccessful sync"
    ::= { fgHaStatsEntry 14 }

fgHaStatsGlobalChecksum OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Current HA global checksum value"
    ::= { fgHaStatsEntry 15 }

fgHaStatsPrimarySerial OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Serial number of primary during the last synch attempt (successful of not)"
    ::= { fgHaStatsEntry 16 }

fgHaStatsAllChecksum OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current HA all checksum value"
    ::= { fgHaStatsEntry 17 }

--
-- fortinet.fnFortiGateMib.fgHighAvailability.fgHaTrapObjects
--

fgHaTrapObjects OBJECT IDENTIFIER
    ::= { fgHighAvailability 3 }

fgHaTrapMemberSerial OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Serial number of an HA cluster member. Used to identify the origin of a trap when a cluster is configured."
    ::= { fgHaTrapObjects 1 }

--
-- fortinet.fnFortiGateMib.fgWc
--

fgWc OBJECT IDENTIFIER
    ::= { fnFortiGateMib 14 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcTrapObjects
--

fgWcTrapObjects OBJECT IDENTIFIER
    ::= { fgWc 1 }

fgWcApVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Virtual domain the wtp is part of"
    ::= { fgWcTrapObjects 1 }

fgWcApSerial OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Serial number of the wtp"
    ::= { fgWcTrapObjects 2 }

fgWcApName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Name of the wtp"
    ::= { fgWcTrapObjects 3 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcInfo
--

fgWcInfo OBJECT IDENTIFIER
    ::= { fgWc 2 }

fgWcInfoName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the name of an AC"
    ::= { fgWcInfo 1 }

fgWcInfoLocation OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the location of an AC"
    ::= { fgWcInfo 2 }

fgWcInfoWtpCapacity  OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the maximum number of WTPs that can be managed on the AC."
    ::= { fgWcInfo 3 }

fgWcInfoWtpManaged  OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of WTPs being managed on the AC."
    ::= { fgWcInfo 4 }

fgWcInfoWtpSessions OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of WTPs that are connecting to the AC."
    ::= { fgWcInfo 5 }

fgWcInfoStationCapacity  OBJECT-TYPE
    SYNTAX      Unsigned32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the maximum number of stations that can be supported on the AC."
    ::= { fgWcInfo 6 }

fgWcInfoStationCount  OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of stations that are accessing the wireless service
         provided by the AC."
    ::= { fgWcInfo 7 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWlanTable
--

fgWcWlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcWlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table that allows the operator to display WLAN profiles."
    ::= { fgWc 3 }

fgWcWlanEntry  OBJECT-TYPE
    SYNTAX      FgWcWlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that stores the settings of a WLAN profile."
    INDEX       { fgVdEntIndex, ifIndex }
    ::= { fgWcWlanTable 1 }

FgWcWlanEntry ::= SEQUENCE {
      fgWcWlanSsid                   OCTET STRING,
      fgWcWlanBroadcastSsid          FnBoolState,
      fgWcWlanSecurity               FgWcWlanSecurityType,
      fgWcWlanEncryption             FgWcWlanEncryptionType,
      fgWcWlanAuthentication         FgWcWlanAuthenticationType,
      fgWcWlanRadiusServer           DisplayString,
      fgWcWlanUserGroup              DisplayString,
      fgWcWlanLocalBridging          FnBoolState,
      fgWcWlanVlanId                 Integer32,
      fgWcWlanMeshBackhaul           FnBoolState,
      fgWcWlanStationCapacity        Unsigned32,
      fgWcWlanStationCount           Gauge32
    }

fgWcWlanSsid OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Service set ID of this WLAN profile."
    ::= { fgWcWlanEntry 1 }

fgWcWlanBroadcastSsid OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Whether SSID broadcast is enabled on this WLAN profile."
    ::= { fgWcWlanEntry 2 }

fgWcWlanSecurity  OBJECT-TYPE
    SYNTAX      FgWcWlanSecurityType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the security type of the WLAN profile."
    ::= { fgWcWlanEntry 3 }

fgWcWlanEncryption OBJECT-TYPE
    SYNTAX      FgWcWlanEncryptionType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the encryption method of the WLAN profile."
    ::= { fgWcWlanEntry 4 }

fgWcWlanAuthentication OBJECT-TYPE
    SYNTAX      FgWcWlanAuthenticationType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the authentication method of the WLAN profile."
    ::= { fgWcWlanEntry 5 }

fgWcWlanRadiusServer OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the specified RADIUS server of the WLAN profile."
    ::= { fgWcWlanEntry 6 }

fgWcWlanUserGroup OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the specified user group of the WLAN profile."
    ::= { fgWcWlanEntry 7 }

fgWcWlanLocalBridging OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Whether local bridging is enabled on this WLAN profile."
    ::= { fgWcWlanEntry 8 }

fgWcWlanVlanId OBJECT-TYPE
    SYNTAX      Integer32 (0 | 1..4094 | 4095)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the VLAN ID of the WLAN profile."
    ::= { fgWcWlanEntry 9 }

fgWcWlanMeshBackhaul OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Whether mesh backhaul is enabled on this WLAN profile."
    ::= { fgWcWlanEntry 10 }

fgWcWlanStationCapacity OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the maximum number of clients allowed on this WLAN profile."
    ::= { fgWcWlanEntry 11 }

fgWcWlanStationCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of clients currently connected to this WLAN profile."
    ::= { fgWcWlanEntry 12 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWtpTables
--

fgWcWtpTables OBJECT IDENTIFIER
    ::= { fgWc 4 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWtpTables.fgWcWtpProfileTable
--

fgWcWtpProfileTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcWtpProfileEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of objects that display WTP profiles for WTPs to be managed before they connect
         to the AC. A WTP could get the new configuration through the CAPWAP control channel."
    ::= { fgWcWtpTables 1 }

fgWcWtpProfileEntry OBJECT-TYPE
    SYNTAX      FgWcWtpProfileEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that display a WTP profile."
    INDEX { fgVdEntIndex, fgWcWtpProfileName }
    ::= { fgWcWtpProfileTable 1 }

FgWcWtpProfileEntry ::= SEQUENCE {
      fgWcWtpProfileName                    DisplayString,
      fgWcWtpProfilePlatform                DisplayString,
      fgWcWtpProfileDataChannelDtlsPolicy   BITS,
      fgWcWtpProfileCountryString           FgWcCountryString
    }

fgWcWtpProfileName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..36))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the name of a WTP profile."
    ::= { fgWcWtpProfileEntry 1 }

fgWcWtpProfilePlatform OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the hardware platform of a WTP profile."
    ::= { fgWcWtpProfileEntry 2 }

fgWcWtpProfileDataChannelDtlsPolicy OBJECT-TYPE
    SYNTAX      BITS { other(0), clear(1), dtls(2), ipsec(3), ipsecsn(4) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The AC communicates its policy on the use of DTLS for the CAPWAP data channel.
         The AC MAY support more than one option, represented by the bit field below:
           other(0) - Other method, for example, vendor specific
           clear(1) - Clear text
           dtls(2)  - DTLS
           ipsec(3) - IPsec
           ipsecsn(4) - IPsec-sn"
    ::= { fgWcWtpProfileEntry 3 }

fgWcWtpProfileCountryString OBJECT-TYPE
    SYNTAX      FgWcCountryString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the country setting of a WTP profile, in ISO string format."
    ::= { fgWcWtpProfileEntry 4 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWtpTables.fgWcWtpProfileRadioTable
--

fgWcWtpProfileRadioTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcWtpProfileRadioEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of objects that display WTP radio profiles for WTP radios to be managed
         before the WTPs connect to the AC. A WTP radio could get the new configuration
         through the CAPWAP control channel."
    ::= { fgWcWtpTables 2 }

fgWcWtpProfileRadioEntry  OBJECT-TYPE
    SYNTAX      FgWcWtpProfileRadioEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that display a WTP radio profile."
    INDEX { fgVdEntIndex, fgWcWtpProfileRadioProfileName, fgWcWtpProfileRadioRadioId }
    ::= { fgWcWtpProfileRadioTable 1 }

FgWcWtpProfileRadioEntry ::= SEQUENCE {
      fgWcWtpProfileRadioProfileName        DisplayString,
      fgWcWtpProfileRadioRadioId            FgWcWtpRadioId,
      fgWcWtpProfileRadioMode               FgWcWtpRadioMode,
      fgWcWtpProfileRadioApScan             FnBoolState,
      fgWcWtpProfileRadioWidsProfile        DisplayString,
      fgWcWtpProfileRadioDarrp              FnBoolState,
      fgWcWtpProfileRadioFrequencyHandoff   FnBoolState,
      fgWcWtpProfileRadioApHandoff          FnBoolState,
      fgWcWtpProfileRadioBeaconInterval     Integer32,
      fgWcWtpProfileRadioDtimPeriod         Integer32,
      fgWcWtpProfileRadioBand               FgWcWtpRadioType,
      fgWcWtpProfileRadioChannelBonding     FnBoolState,
      fgWcWtpProfileRadioChannel            DisplayString,
      fgWcWtpProfileRadioAutoTxPowerControl FnBoolState,
      fgWcWtpProfileRadioAutoTxPowerLow     Integer32,
      fgWcWtpProfileRadioAutoTxPowerHigh    Integer32,
      fgWcWtpProfileRadioTxPowerLevel       Gauge32,
      fgWcWtpProfileRadioVaps               DisplayString,
      fgWcWtpProfileRadioStationCapacity    Unsigned32,
      fgWcWtpProfileRadioChannelWidth       FgWcWtpChannelWidthType
    }

fgWcWtpProfileRadioProfileName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..36))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the name of a WTP profile."
    ::= { fgWcWtpProfileRadioEntry 1 }

fgWcWtpProfileRadioRadioId OBJECT-TYPE
    SYNTAX      FgWcWtpRadioId
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the radio Id of a WTP radio."
    ::= { fgWcWtpProfileRadioEntry 2 }

fgWcWtpProfileRadioMode OBJECT-TYPE
    SYNTAX      FgWcWtpRadioMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the operating mode of a WTP radio."
    ::= { fgWcWtpProfileRadioEntry 3 }

fgWcWtpProfileRadioApScan OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether background scan is enabled on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 4 }

fgWcWtpProfileRadioWidsProfile OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the WIDS profile configured for this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 5 }

fgWcWtpProfileRadioDarrp OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether DARRP is enabled on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 6 }

fgWcWtpProfileRadioFrequencyHandoff OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether frequency handoff is enabled on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 7 }

fgWcWtpProfileRadioApHandoff OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether AP handoff is enabled on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 8 }

fgWcWtpProfileRadioBeaconInterval OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This attribute shall specify the number of TUs that a station shall use for scheduling
         Beacon transmissions. This value is transmitted in Beacon and Probe Response frames."
    ::= { fgWcWtpProfileRadioEntry 9 }

fgWcWtpProfileRadioDtimPeriod OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This attribute shall specify the number of beacon intervals that shall elapse between
         transmission of Beacon frames containing a TIM element whose DTIM Count field is 0.
         This value is transmitted in the DTIM Period field of Beacon frames."
    ::= { fgWcWtpProfileRadioEntry 10 }

fgWcWtpProfileRadioBand OBJECT-TYPE
    SYNTAX      FgWcWtpRadioType
    MAX-ACCESS  read-only
    STATUS current
    DESCRIPTION
        "Represents the radio band setting configured for this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 11 }

fgWcWtpProfileRadioChannelBonding OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether channel bonding is enabled on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 12 }

fgWcWtpProfileRadioChannel OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents a list of channels configured for this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 13 }

fgWcWtpProfileRadioAutoTxPowerControl OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether automatic TX power control is enabled on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 14 }

fgWcWtpProfileRadioAutoTxPowerLow OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the low limit of the power level configured for this WTP radio, in dBm.
         Applicable only when auto power leveling is enabled."
    ::= { fgWcWtpProfileRadioEntry 15 }

fgWcWtpProfileRadioAutoTxPowerHigh OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the high limit of the power level configured for this WTP radio, in dBm.
         Applicable only when auto power leveling is enabled."
    ::= { fgWcWtpProfileRadioEntry 16 }

fgWcWtpProfileRadioTxPowerLevel OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the radio TX power setting configured for this WTP radio, in percentage.
         Application only when auto power leveling is disabled."
    ::= { fgWcWtpProfileRadioEntry 17 }

fgWcWtpProfileRadioVaps OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents a list of WLANs configured for this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 18 }

fgWcWtpProfileRadioStationCapacity OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the maximum number of clients allowed on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 19 }

fgWcWtpProfileRadioChannelWidth OBJECT-TYPE
    SYNTAX      FgWcWtpChannelWidthType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the channel width on this WTP radio."
    ::= { fgWcWtpProfileRadioEntry 20 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWtpTables.fgWcWtpConfigTable
--

fgWcWtpConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcWtpConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of objects that display the configuration of WTPs."
    ::= { fgWcWtpTables 3 }

fgWcWtpConfigEntry  OBJECT-TYPE
    SYNTAX      FgWcWtpConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that display the configuration of a WTP."
    INDEX { fgVdEntIndex, fgWcWtpConfigWtpId }
    ::= { fgWcWtpConfigTable 1 }

FgWcWtpConfigEntry ::= SEQUENCE {
      fgWcWtpConfigWtpId                    DisplayString,
      fgWcWtpConfigWtpAdmin                 INTEGER,
      fgWcWtpConfigWtpName                  DisplayString,
      fgWcWtpConfigWtpLocation              DisplayString,
      fgWcWtpConfigWtpProfile               DisplayString,
      fgWcWtpConfigRadioEnable              FnBoolState,
      fgWcWtpConfigRadioAutoTxPowerControl  FnBoolState,
      fgWcWtpConfigRadioAutoTxPowerLow      Integer32,
      fgWcWtpConfigRadioAutoTxPowerHigh     Integer32,
      fgWcWtpConfigRadioTxPowerLevel        Gauge32,
      fgWcWtpConfigRadioBand                FgWcWtpRadioBandType,
      fgWcWtpConfigRadioApScan              FnBoolState,
      fgWcWtpConfigVapAll                   FnBoolState,
      fgWcWtpConfigVaps                     DisplayString
    }

fgWcWtpConfigWtpId OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..36))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the unique identifier of a WTP."
    ::= { fgWcWtpConfigEntry 1 }

fgWcWtpConfigWtpAdmin OBJECT-TYPE
    SYNTAX      INTEGER {
                  other(0),
                  discovered(1),
                  disable(2),
                  enable(3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the administrative status of this WTP.
         The following enumerated values are supported:
           discovered(1)   - This WTP was discovered though discovery or join request messages.
           disable(2)      - Controller is configured to not provide service to this WTP.
           enable(3),      - Controller is configured to provide service to this WTP.
           other(0)        - The administration state of the WTP is unknown."
    ::= { fgWcWtpConfigEntry 2 }

fgWcWtpConfigWtpName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the name configured for this WTP."
    ::= { fgWcWtpConfigEntry 3 }

fgWcWtpConfigWtpLocation OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the location of this WTP."
    ::= { fgWcWtpConfigEntry 4 }

fgWcWtpConfigWtpProfile OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the WTP profile configured for this WTP."
    ::= { fgWcWtpConfigEntry 5 }

fgWcWtpConfigRadioEnable OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether radio is enabled for this WTP."
    ::= { fgWcWtpConfigEntry 6 }

fgWcWtpConfigRadioAutoTxPowerControl OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether radio automatic TX power control is enabled on this WTP."
    ::= { fgWcWtpConfigEntry 7 }

fgWcWtpConfigRadioAutoTxPowerLow OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the low limit of radio automatic TX power control configured for this WTP, in dBm."
    ::= { fgWcWtpConfigEntry 8 }

fgWcWtpConfigRadioAutoTxPowerHigh OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the high limit of radio automatic TX power control configured for this WTP, in dBm."
    ::= { fgWcWtpConfigEntry 9 }

fgWcWtpConfigRadioTxPowerLevel OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the radio TX power setting configured for this WTP, in percentage."
    ::= { fgWcWtpConfigEntry 10 }

fgWcWtpConfigRadioBand OBJECT-TYPE
    SYNTAX      FgWcWtpRadioBandType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the radio band configured for this WTP."
    ::= { fgWcWtpConfigEntry 11 }

fgWcWtpConfigRadioApScan OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether background scan is enabled on this WTP."
    ::= { fgWcWtpConfigEntry 12 }

fgWcWtpConfigVapAll OBJECT-TYPE
    SYNTAX      FnBoolState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether all VAPs are selected for this WTP."
    ::= { fgWcWtpConfigEntry 13 }

fgWcWtpConfigVaps OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents a list of VAPs configured for this WTP."
    ::= { fgWcWtpConfigEntry 14 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWtpTables.fgWcWtpSessionTable
--

fgWcWtpSessionTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcWtpSessionEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of objects that indicate the AC's CAPWAP FSM state for each WTP,
         and helps the operator to query a WTP's current status."
    ::= { fgWcWtpTables 4 }

fgWcWtpSessionEntry  OBJECT-TYPE
    SYNTAX      FgWcWtpSessionEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that displays the AC's CAPWAP FSM state for each WTP.
         Also, the operator could query the current status of a WTP by using
         the identifier of the corresponding WTP."
    INDEX { fgVdEntIndex, fgWcWtpSessionWtpId }
    ::= { fgWcWtpSessionTable 1 }

FgWcWtpSessionEntry ::= SEQUENCE {
      fgWcWtpSessionWtpId                   DisplayString,
      fgWcWtpSessionWtpIpAddressType        InetAddressType,
      fgWcWtpSessionWtpIpAddress            InetAddress,
      fgWcWtpSessionWtpLocalIpAddressType   InetAddressType,
      fgWcWtpSessionWtpLocalIpAddress       InetAddress,
      fgWcWtpSessionWtpBaseMacAddress       PhysAddress,
      fgWcWtpSessionConnectionState         INTEGER,
      fgWcWtpSessionWtpUpTime               TimeTicks,
      fgWcWtpSessionWtpDaemonUpTime         TimeTicks,
      fgWcWtpSessionWtpSessionUpTime        TimeTicks,
      fgWcWtpSessionWtpProfileName          DisplayString,
      fgWcWtpSessionWtpModelNumber          DisplayString,
      fgWcWtpSessionWtpHwVersion            DisplayString,
      fgWcWtpSessionWtpSwVersion            DisplayString,
      fgWcWtpSessionWtpBootVersion          DisplayString,
      fgWcWtpSessionWtpRegionCode           DisplayString,
      fgWcWtpSessionWtpStationCount         Gauge32,
      fgWcWtpSessionWtpByteRxCount          Counter64,
      fgWcWtpSessionWtpByteTxCount          Counter64,
      fgWcWtpSessionWtpCpuUsage             Gauge32,
      fgWcWtpSessionWtpMemoryUsage          Gauge32,
      fgWcWtpSessionWtpMemoryCapacity       Unsigned32
    }

fgWcWtpSessionWtpId OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..36))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the unique identifier of a WTP."
    ::= { fgWcWtpSessionEntry 1 }

fgWcWtpSessionWtpIpAddressType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the IP address type of a WTP.
         Only ipv4(1) and ipv6(2) are supported by the object."
    ::= { fgWcWtpSessionEntry 2 }

fgWcWtpSessionWtpIpAddress OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the IP address of a WTP that corresponds to the IP address in
         the IP packet header.
         The format of this IP address is determined by the corresponding instance of
         object fgWcWtpWtpIpAddressType."
    ::= { fgWcWtpSessionEntry 3 }

fgWcWtpSessionWtpLocalIpAddressType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the local IP address type of a WTP.
         Only ipv4(1) and ipv6(2) are supported by the object."
    ::= { fgWcWtpSessionEntry 4 }

fgWcWtpSessionWtpLocalIpAddress OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the local IP address of a WTP and models the CAPWAP Local IPv4 Address
         or CAPWAP Local IPv6 Address fields [RFC5415].
         If a Network Address Translation (NAT) device is present between WTP and AC, the value of
         fgWcWtpWtpLocalIpAddress will be different from the value of fgWcWtpWtpIpAddress.
         The format of this IP address is determined by the corresponding instance of object
         fgWcWtpSessionWtpLocalIpAddressType."
    ::= { fgWcWtpSessionEntry 5 }

fgWcWtpSessionWtpBaseMacAddress OBJECT-TYPE
    SYNTAX      PhysAddress (SIZE(6|8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the WTP's Base MAC Address, which MAY be assigned to the primary
         Ethernet interface.
         The instance of the object corresponds to the Base MAC Address sub-element
         in the CAPWAP protocol [RFC5415]."
    ::= { fgWcWtpSessionEntry 6 }

fgWcWtpSessionConnectionState OBJECT-TYPE
    SYNTAX      INTEGER {
                  other(0),
                  offLine(1),
                  onLine(2),
                  downloadingImage(3),
                  connectedImage(4),
                  standby(5)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the connection state of a WTP to AC.
         The following enumerated values are supported:
           offLine(1)           - The WTP is not connected.
           onLine(2)            - The WTP is connected.
           downloadingImage(3)  - The WTP is downloading software image from the AC on joining.
           connectedImage(4)    - The AC is pushing software image to the connected WTP.
           standby(5)           - The WTP is standby on the AC.
           other(0)             - The WTP connection state is unknown."
    ::= { fgWcWtpSessionEntry 7 }

fgWcWtpSessionWtpUpTime OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the time (in hundredths of a second) since the WTP boots."
    ::= { fgWcWtpSessionEntry 8 }

fgWcWtpSessionWtpDaemonUpTime OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the time (in hundredths of a second) since the WTP daemon has been started."
    ::= { fgWcWtpSessionEntry 9 }

fgWcWtpSessionWtpSessionUpTime OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the time (in hundredths of a second) since the WTP connects to the AC."
    ::= { fgWcWtpSessionEntry 10 }

fgWcWtpSessionWtpProfileName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the profile configured for this WTP."
    ::= { fgWcWtpSessionEntry 11 }

fgWcWtpSessionWtpModelNumber OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the model number of a WTP."
    ::= { fgWcWtpSessionEntry 12 }

fgWcWtpSessionWtpHwVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the hardware version of a WTP."
    ::= { fgWcWtpSessionEntry 13 }

fgWcWtpSessionWtpSwVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the software version of a WTP."
    ::= { fgWcWtpSessionEntry 14 }

fgWcWtpSessionWtpBootVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the boot loader version of a WTP."
    ::= { fgWcWtpSessionEntry 15 }

fgWcWtpSessionWtpRegionCode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the region code programmed for this WTP."
    ::= { fgWcWtpSessionEntry 16 }

fgWcWtpSessionWtpStationCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of clients currently connected to this WTP."
    ::= { fgWcWtpSessionEntry 17 }

fgWcWtpSessionWtpByteRxCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of bytes received by this WTP."
    ::= { fgWcWtpSessionEntry 18 }

fgWcWtpSessionWtpByteTxCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of bytes transmitted by this WTP."
    ::= { fgWcWtpSessionEntry 19 }

fgWcWtpSessionWtpCpuUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the current CPU usage of a WTP (percentage)."
    ::= { fgWcWtpSessionEntry 20 }

fgWcWtpSessionWtpMemoryUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the current memory usage of a WTP (percentage)."
    ::= { fgWcWtpSessionEntry 21 }

fgWcWtpSessionWtpMemoryCapacity OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the total physical memory (RAM) installed (KB)."
    ::= { fgWcWtpSessionEntry 22 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWtpTables.fgWcWtpSessionRadioTable
--

fgWcWtpSessionRadioTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcWtpSessionRadioEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of objects that display the status of radios in WTP sessions."
    ::= { fgWcWtpTables 5 }

fgWcWtpSessionRadioEntry  OBJECT-TYPE
    SYNTAX      FgWcWtpSessionRadioEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that display the status of a radio in a WTP session."
    INDEX { fgVdEntIndex, fgWcWtpSessionRadioWtpId, fgWcWtpSessionRadioRadioId}
    ::= { fgWcWtpSessionRadioTable 1 }

FgWcWtpSessionRadioEntry ::= SEQUENCE {
      fgWcWtpSessionRadioWtpId              DisplayString,
      fgWcWtpSessionRadioRadioId            FgWcWtpRadioId,
      fgWcWtpSessionRadioMode               FgWcWtpRadioMode,
      fgWcWtpSessionRadioBaseBssid          PhysAddress,
      fgWcWtpSessionRadioCountryString      FgWcCountryString,
      fgWcWtpSessionRadioCountryCode        Integer32,
      fgWcWtpSessionRadioOperatingChannel   FgWcWtpRadioChannelNumber,
      fgWcWtpSessionRadioOperatingPower     Integer32,
      fgWcWtpSessionRadioStationCount       Gauge32
    }

fgWcWtpSessionRadioWtpId OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..36))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the unique identifier of a WTP."
    ::= { fgWcWtpSessionRadioEntry 1 }

fgWcWtpSessionRadioRadioId OBJECT-TYPE
    SYNTAX      FgWcWtpRadioId
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the radio Id of a radio."
    ::= { fgWcWtpSessionRadioEntry 2 }

fgWcWtpSessionRadioMode  OBJECT-TYPE
    SYNTAX      FgWcWtpRadioMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the operating mode of this radio."
    ::= { fgWcWtpSessionRadioEntry 3 }

fgWcWtpSessionRadioBaseBssid  OBJECT-TYPE
    SYNTAX      PhysAddress (SIZE(6|8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents base bssid of this radio."
    ::= { fgWcWtpSessionRadioEntry 4 }

fgWcWtpSessionRadioCountryString OBJECT-TYPE
    SYNTAX      FgWcCountryString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the current operating country string, in ISO string format."
    ::= { fgWcWtpSessionRadioEntry 5 }

fgWcWtpSessionRadioCountryCode OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the current operating country code."
    ::= { fgWcWtpSessionRadioEntry 6 }

fgWcWtpSessionRadioOperatingChannel OBJECT-TYPE
    SYNTAX      FgWcWtpRadioChannelNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the current operating channel of this radio."
    ::= { fgWcWtpSessionRadioEntry 7 }

fgWcWtpSessionRadioOperatingPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the current operating power of this radio, in dBm."
    ::= { fgWcWtpSessionRadioEntry 8 }

fgWcWtpSessionRadioStationCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of clients currently connected to this radio."
    ::= { fgWcWtpSessionRadioEntry 9 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcWtpTables.fgWcWtpSessionVapTable
--

fgWcWtpSessionVapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcWtpSessionVapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of objects that display the status of VAPs in WTP sessions.
         A VAP represents an SSID that is assigned on a WTP radio."
    ::= { fgWcWtpTables 6 }

fgWcWtpSessionVapEntry  OBJECT-TYPE
    SYNTAX      FgWcWtpSessionVapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that display the status of a VAP in a WTP session."
    INDEX { fgVdEntIndex, fgWcWtpSessionVapWtpId, fgWcWtpSessionVapRadioId, ifIndex }
    ::= { fgWcWtpSessionVapTable 1 }

FgWcWtpSessionVapEntry ::= SEQUENCE {
      fgWcWtpSessionVapWtpId                DisplayString,
      fgWcWtpSessionVapRadioId              FgWcWtpRadioId,
      fgWcWtpSessionVapSsid                 OCTET STRING,
      fgWcWtpSessionVapStationCount         Gauge32,
      fgWcWtpSessionVapByteRxCount          Counter64,
      fgWcWtpSessionVapByteTxCount          Counter64
    }

fgWcWtpSessionVapWtpId OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..36))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the unique identifier of a WTP."
    ::= { fgWcWtpSessionVapEntry 1 }

fgWcWtpSessionVapRadioId OBJECT-TYPE
    SYNTAX      FgWcWtpRadioId
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the radio Id of a VAP."
    ::= { fgWcWtpSessionVapEntry 2 }

fgWcWtpSessionVapSsid OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Service set ID of this VAP interface."
    ::= { fgWcWtpSessionVapEntry 3 }

fgWcWtpSessionVapStationCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of clients currently connected to this VAP."
    ::= { fgWcWtpSessionVapEntry 4 }

fgWcWtpSessionVapByteRxCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of bytes received by this VAP."
    ::= { fgWcWtpSessionVapEntry 5 }

fgWcWtpSessionVapByteTxCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the number of bytes transmitted by this VAP."
    ::= { fgWcWtpSessionVapEntry 6 }

--
-- fortinet.fnFortiGateMib.fgWc.fgWcStaTable
--

fgWcStaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgWcStaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table that provides information of all the wireless stations that are accessing
         the wireless service provided by the AC."
    ::= { fgWc 5 }

fgWcStaEntry OBJECT-TYPE
    SYNTAX      FgWcStaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of objects that display information of a wireless station."
    INDEX       { fgVdEntIndex, ifIndex, fgWcStaMacAddress }
    ::= { fgWcStaTable 1 }

FgWcStaEntry ::= SEQUENCE {
      fgWcStaMacAddress             PhysAddress,
      fgWcStaWlan                   DisplayString,
      fgWcStaWtpId                  DisplayString,
      fgWcStaRadioId                FgWcWtpRadioId,
      fgWcStaVlanId                 Integer32,
      fgWcStaIpAddressType          InetAddressType,
      fgWcStaIpAddress              InetAddress,
      fgWcStaVci                    DisplayString,
      fgWcStaHost                   DisplayString,
      fgWcStaUser                   DisplayString,
      fgWcStaGroup                  DisplayString,
      fgWcStaSignal                 Integer32,
      fgWcStaNoise                  Integer32,
      fgWcStaIdle                   Gauge32,
      fgWcStaBandwidthTx            Gauge32,
      fgWcStaBandwidthRx            Gauge32,
      fgWcStaChannel                FgWcWtpRadioChannelNumber,
      fgWcStaRadioType              FgWcWtpRadioType,
      fgWcStaSecurity               FgWcWlanSecurityType,
      fgWcStaEncrypt                FgWcWlanEncryptionType,
      fgWcStaOnline                 INTEGER
    }

fgWcStaMacAddress OBJECT-TYPE
    SYNTAX      PhysAddress (SIZE(6|8))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Represents the MAC address of a wireless station."
    ::= { fgWcStaEntry 1 }

fgWcStaWlan OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "WLAN interface that a wireless station is connected to."
    ::= { fgWcStaEntry 2 }

fgWcStaWtpId OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Unique identifier of the WTP that a wireless station is connected to."
    ::= { fgWcStaEntry 3 }

fgWcStaRadioId OBJECT-TYPE
    SYNTAX      FgWcWtpRadioId
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the radio of the WTP that a wireless station is connected to."
    ::= { fgWcStaEntry 4 }

fgWcStaVlanId OBJECT-TYPE
    SYNTAX      Integer32 (0 | 1..4094 | 4095)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the VLAN ID that is assigned to a wireless station."
    ::= { fgWcStaEntry 5 }

fgWcStaIpAddressType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the IP address type of a wireless station.
         Only ipv4(1) and ipv6(2) are supported by the object."
    ::= { fgWcStaEntry 6 }

fgWcStaIpAddress OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the IP address of a wireless station."
    ::= { fgWcStaEntry 7 }

fgWcStaVci OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the vendor class identifier of a wireless station."
    ::= { fgWcStaEntry 8 }

fgWcStaHost OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the host name of a wireless station."
    ::= { fgWcStaEntry 9 }

fgWcStaUser OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the user name of a wireless station."
    ::= { fgWcStaEntry 10 }

fgWcStaGroup OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the user group of a wireless station."
    ::= { fgWcStaEntry 11 }

fgWcStaSignal OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the signal strengh of a wireless station, in dBm."
    ::= { fgWcStaEntry 12 }

fgWcStaNoise OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the noise level of a wireless station, in dBm."
    ::= { fgWcStaEntry 13 }

fgWcStaIdle OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates how long a wireless station in inactive, in seconds."
    ::= { fgWcStaEntry 14 }

fgWcStaBandwidthTx OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the TX bandwidth of a wireless station, in kbps."
    ::= { fgWcStaEntry 15 }

fgWcStaBandwidthRx OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the RX bandwidth of a wireless station, in kbps."
    ::= { fgWcStaEntry 16 }

fgWcStaChannel OBJECT-TYPE
    SYNTAX      FgWcWtpRadioChannelNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the frequency channel that a wireless station is using."
    ::= { fgWcStaEntry 17 }

fgWcStaRadioType OBJECT-TYPE
    SYNTAX      FgWcWtpRadioType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the radio physical type of a wireless station."
    ::= { fgWcStaEntry 18 }

fgWcStaSecurity OBJECT-TYPE
    SYNTAX      FgWcWlanSecurityType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the security type of a wireless station."
    ::= { fgWcStaEntry 19 }

fgWcStaEncrypt OBJECT-TYPE
    SYNTAX      FgWcWlanEncryptionType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represents the encryption method of a wireless station."
    ::= { fgWcStaEntry 20 }

fgWcStaOnline OBJECT-TYPE
    SYNTAX      INTEGER { yes(1), no(2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Represent online status of a wireless station."
    ::= { fgWcStaEntry 21 }

--
-- fortinet.fnFortiGateMib.fgFc
--

fgFc OBJECT IDENTIFIER
    ::= { fnFortiGateMib 15 }

--
-- fortinet.fnFortiGateMib.fgFc.fgFcTrapObjects
--

fgFcTrapObjects OBJECT IDENTIFIER
    ::= { fgFc 1 }

fgFcSwVdom OBJECT-TYPE
    SYNTAX      FgVdIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Virtual domain the switch is part of"
    ::= { fgFcTrapObjects 1 }

fgFcSwSerial OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Serial number of the switch"
    ::= { fgFcTrapObjects 2 }

fgFcSwName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Name of the switch"
    ::= { fgFcTrapObjects 3 }
    
--
-- fortinet.fnFortiGateMib.fgServerLoadBalance
--

fgServerLoadBalance OBJECT IDENTIFIER
    ::= { fnFortiGateMib 16 }

--
-- fortinet.fnFortiGateMib.fgServerLoadBalance.fgServerLoadBalanceTrapObjects
--

fgServerLoadBalanceTrapObjects OBJECT IDENTIFIER
    ::= { fgServerLoadBalance 1 }

fgServerLoadBalanceRealServerAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "IP of the real server."
    ::= { fgServerLoadBalanceTrapObjects 1 }

fgServerLoadBalanceVirtualServerName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "Name of the virual server."
    ::= { fgServerLoadBalanceTrapObjects 2 }

fgServerLoadBalanceRealServerAddress6 OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "IPv6 of the real server."
    ::= { fgServerLoadBalanceTrapObjects 3 }

--
-- fortinet.fnFortiGateMib.fgUsbModemInfo
--

fgUsbModemInfo OBJECT IDENTIFIER
    ::= { fnFortiGateMib 17 }

--
-- fortinet.fnFortiGateMib.fgUsbModemInfo.fgUsbModemInfoObjects
--

fgUsbModemInfoObjects OBJECT IDENTIFIER
    ::= { fgUsbModemInfo 1 }

fgUsbModemSignalStrength OBJECT-TYPE
    SYNTAX      INTEGER { level0(0), level1(1), level2(2), level3(3), level4(4) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "3G/4G USB Modem signal Strength."
    ::= { fgUsbModemInfoObjects 1 }

fgUsbModemStatus OBJECT-TYPE
    SYNTAX      INTEGER { disconnected(0), connected(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        " 3G/4G USB Modem status."
    ::= { fgUsbModemInfoObjects 2 }

fgUsbModemSimState OBJECT-TYPE
    SYNTAX      INTEGER { invalid(0), valid(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "3G/4G USB Modem sim card status."
    ::= { fgUsbModemInfoObjects 3 }

fgUsbModemVendor OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "3G/4G USB Modem vendor name."
    ::= { fgUsbModemInfoObjects 4 }

fgUsbModemProduct OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "3G/4G USB Modem product name."
    ::= { fgUsbModemInfoObjects 5 }

fgUsbModemNetwork OBJECT-TYPE
    SYNTAX      INTEGER { network3G(0), networkLTE(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "3G/4G USB Modem network type."
    ::= { fgUsbModemInfoObjects 6 }

fgUsbModemId OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "3G/4G USB Modem identifier."
    ::= { fgUsbModemInfoObjects 7 }

fgUsbModemSimId OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "3G/4G USB Modem sim card identifier."
    ::= { fgUsbModemInfoObjects 8 }

--
-- fortinet.fnFortiGateMib.fgDevice
--

fgDevice OBJECT IDENTIFIER
    ::= { fnFortiGateMib 18 }

--
-- fortinet.fnFortiGateMib.fgDevice.fgDeviceTrapObjects
--

fgDeviceTrapObjects OBJECT IDENTIFIER
    ::= { fgDevice 1 }

fgDeviceMacAddress OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "The MAC address pertaining the Device."
    ::= { fgDeviceTrapObjects 1 }

fgDeviceCreated OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "The created time pertaining the Device."
    ::= { fgDeviceTrapObjects 2 }

fgDeviceLastSeen OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "The last seen time pertaining the Device."
    ::= { fgDeviceTrapObjects 3 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo
--

fgInternalLTEModemsInfo OBJECT IDENTIFIER
    ::= { fnFortiGateMib 19 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgMdmInfoTable
--

fgMdmInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgMdmInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table containing information applicable
         to internal LTE modems."
    ::= { fgInternalLTEModemsInfo 1 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgMdmInfoTable.fgMdmInfoEntry
--

fgMdmInfoEntry OBJECT-TYPE
    SYNTAX      FgMdmInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to a particular LTE Modem. OIDs from fgMdmVendor are 
	 not displayed if a modem is not detected."
    INDEX       { fgMdmEntIndex }
    ::= { fgMdmInfoTable 1 }

FgMdmInfoEntry ::= SEQUENCE {
    fgMdmEntIndex       FnIndex,
    fgMdmDetected       INTEGER,
    fgMdmVendor         DisplayString,
    fgMdmModel          DisplayString,
    fgMdmRevision       DisplayString,
    fgMdmMsisdn         DisplayString,
    fgMdmEsn            DisplayString,
    fgMdmImei           DisplayString,
    fgMdmHwRevision     DisplayString,
    fgMdmMeid           DisplayString,
    fgMdmSwRev          DisplayString,
    fgMdmSku            DisplayString,
    fgMdmFsn            DisplayString,
    fgMdmPrlVer         DisplayString,
    fgMdmFwVer          DisplayString,
    fgMdmPriFwVer       DisplayString,
    fgMdmCarrierAbbr    DisplayString,
    fgMdmActState       INTEGER,
    fgMdmOpMode         INTEGER,
    fgMdmLacTac         DisplayString,
    fgMdmActBand        DisplayString,
    fgMdmCellId         DisplayString,
    fgMdmRssi           Integer32
}

fgMdmEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of an internal LTE modem."
    ::= { fgMdmInfoEntry 1 }

fgMdmDetected OBJECT-TYPE
    SYNTAX      INTEGER { no(0), yes(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        " Internal Modem Detected."
    ::= { fgMdmInfoEntry 2 }

fgMdmVendor OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Vendor Name."
    ::= { fgMdmInfoEntry 3 }

fgMdmModel OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Model Name."
    ::= { fgMdmInfoEntry 4 }

fgMdmRevision OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Modem Firmware Revision."
    ::= { fgMdmInfoEntry 5 }

fgMdmMsisdn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "MSISDN."
    ::= { fgMdmInfoEntry 6 }

fgMdmEsn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "ESN."
    ::= { fgMdmInfoEntry 7 }

fgMdmImei OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IMEI."
    ::= { fgMdmInfoEntry 8 }

fgMdmHwRevision OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Hardware Revision."
    ::= { fgMdmInfoEntry 9 }

fgMdmMeid OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "MEID."
    ::= { fgMdmInfoEntry 10 }

fgMdmSwRev OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Software Revision."
    ::= { fgMdmInfoEntry 11 }

fgMdmSku OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "SKU."
    ::= { fgMdmInfoEntry 12 }

fgMdmFsn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "FSN."
    ::= { fgMdmInfoEntry 13 }

fgMdmPrlVer OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Prl Version."
    ::= { fgMdmInfoEntry 14 }

fgMdmFwVer OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Modem Firmware Version."
    ::= { fgMdmInfoEntry 15 }

fgMdmPriFwVer OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "PRI Firmware Version."
    ::= { fgMdmInfoEntry 16 }

fgMdmCarrierAbbr OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Modem Carrier Abbriviation."
    ::= { fgMdmInfoEntry 17 }

fgMdmActState OBJECT-TYPE
    SYNTAX      INTEGER { 
			notActivated(0), 
			activated(1), 
			connecting(2),
			connected(3),
			otaspAuthenticated(4),
			otaspNamDownloaded(5),
			otaspMdnDownloaded(6),
			otaspImsiDownloaded(7),
			otaspPrlDownloaded(8),
			otaspSpcDownloaded(9),
			otaspSettingsCmted(10)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Modem Activation State.
         The following enumerated values are supported:
			notActivated(0)         - Service not activated.
			activated(1)            - Service is activated.
			connecting(2)           - Connection in progress for automatic activation.
			connected(3)            - Connection connected for automatic activation.
			otaspAuthenticated(4)   - OTASP security authenticated.
			otaspNamDownloaded(5)   - OTASP NAM downloaded.
			otaspMdnDownloaded(6)   - OTASP MDN downloaded.
			otaspImsiDownloaded(7)  - OTASP IMSI downloaded.
			otaspPrlDownloaded(8)   - OTASP PRL downloaded.
			otaspSpcDownloaded(9)   - OTASP SPC downloaded.
			otaspSettingsCmted(10)  - OTASP settings committed."
    ::= { fgMdmInfoEntry 18 }

fgMdmOpMode OBJECT-TYPE
    SYNTAX      INTEGER { 
			online(0), 
			lowPower(1), 
			factoryTest(2),
			offLine(3),
			reset(4),
			shuttingDown(5),
			persistentLowPower(6),
			modeOnlyLowPower(7),
			unknown(255)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Modem Operation Mode.
         The following enumerated values are supported:
			online(0)              - Device can acquire a system and make calls.
			lowPower(1)            - Device has temporarily disabled RF.
			factoryTest(2)         - Special mode for manufacturer tests.
			offLine(3)             - Device has deactivated RF and is partially shutdown.
			reset(4)               - Device is in the process of power cycling.
			shuttingDown(5)        - Device is in the process of shutting down.
			persistentLowPower(6)  - Device has disabled RF and state persists even after a reset.
			modeOnlyLowPower(7)    - Mode-only Low Power.
			unknown(255)           - Unknown. "
    ::= { fgMdmInfoEntry 19 }

fgMdmLacTac OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Modem LAC/TAC Code."
    ::= { fgMdmInfoEntry 20 }

fgMdmActBand OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Modem Active Band In Use."
    ::= { fgMdmInfoEntry 21 }

fgMdmCellId OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Modem Network Cell ID."
    ::= { fgMdmInfoEntry 22 }

fgMdmRssi OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Modem Current Rssi."
    ::= { fgMdmInfoEntry 23 }
--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgSimInfoTable
--

fgSimInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgSimInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table containing information applicable
         to SIM cards on internal LTE modems."
    ::= { fgInternalLTEModemsInfo 2 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgSimInfoTable.fgSimInfoEntry
--

fgSimInfoEntry OBJECT-TYPE
    SYNTAX      FgSimInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to a particular SIM card on an particular LTE Modem.
	 Although we only have one SIM slot and one internal modem now, 
	 in fact one modem supports multiple SIM slots, that's why we have
	 fgMdmEntIndex in the record. It shows nothing if the modem is not detected."
    INDEX       { fgSimEntIndex }
    ::= { fgSimInfoTable 1 }

FgSimInfoEntry ::= SEQUENCE {
    fgSimEntIndex       FnIndex,
    fgSimMdmEntIndex    FnIndex,
    fgSimState          INTEGER,
    fgSimIccid          DisplayString,
    fgSimImsi           DisplayString,
    fgSimCountry        DisplayString,
    fgSimNetwork        DisplayString
}

fgSimEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of a SIM card."
    ::= { fgSimInfoEntry 1 }

fgSimMdmEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of an internal LTE modem."
    ::= { fgSimInfoEntry 2 }

fgSimState OBJECT-TYPE
    SYNTAX      INTEGER { 
				initialized(0), 
				lockedOrFailed(1), 
				notPresent(2),
				reserved(3),
				unknown(255)
			}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        " SIM card state.
         The following enumerated values are supported:
			initialized(0)        - UIM initialization completed.
			lockedOrFailed(1)     - UIM is locked or failed.
			notPresent(2)         - No UIM in the device.
			reserved(3)           - Reserved, unknown.
			unknown(255)          - UIM state currently unavailable. "
    ::= { fgSimInfoEntry 3 }

fgSimIccid OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "ICCID."
    ::= { fgSimInfoEntry 4 }

fgSimImsi OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IMSI."
    ::= { fgSimInfoEntry 5 }

fgSimCountry OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Carrier Country."
    ::= { fgSimInfoEntry 6 }

fgSimNetwork OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Carrier Network."
    ::= { fgSimInfoEntry 7 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgSignalInfoTable
--

fgSignalInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgSignalInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table containing information applicable
         to signal strength on an internal LTE modems."
    ::= { fgInternalLTEModemsInfo 3 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgSignalInfoTable.fgSignalInfoEntry
--

fgSignalInfoEntry OBJECT-TYPE
    SYNTAX      FgSignalInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to signal strength on an internal LTE modem. If the modem with the modem index
	 is not present, it shows nothing. If the specific type of signal is not present,
	 the OID shows nothing either."
    INDEX       { fgSigMdmEntIndex }
    ::= { fgSignalInfoTable 1 }

FgSignalInfoEntry ::= SEQUENCE {
    fgSigMdmEntIndex    FnIndex,
    fgCdmaRssi          Integer32,
    fgCdmaEcio          Integer32,
    fgHdrRssi           Integer32,
    fgHdrEcio           Integer32,
    fgHdrSinr           Integer32,
    fgHdrIo             Integer32,
    fgGsm               Integer32,
    fgWcdmaRssi         Integer32,
    fgWcdmaEcio         Integer32,
    fgLteRssi           Integer32,
    fgLteRsrq           Integer32,
    fgLteRsrp           Integer32,
    fgLteSnr            Integer32,
    fgTdma              Integer32
}

fgSigMdmEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of an internal LTE modem."
    ::= { fgSignalInfoEntry 1 }

fgCdmaRssi OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RSSI for CDMA."
    ::= { fgSignalInfoEntry 2 }

fgCdmaEcio OBJECT-TYPE
    SYNTAX      Integer32(-65536..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "ECIO for CDMA."
    ::= { fgSignalInfoEntry 3 }

fgHdrRssi OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RSSI for HDR."
    ::= { fgSignalInfoEntry 4 }

fgHdrEcio OBJECT-TYPE
    SYNTAX      Integer32(-65536..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "ECIO for HDR."
    ::= { fgSignalInfoEntry 5 }

fgHdrSinr OBJECT-TYPE
    SYNTAX      Integer32(0..8)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "SINR for HDR."
    ::= { fgSignalInfoEntry 6 }

fgHdrIo OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "IO for HDR."
    ::= { fgSignalInfoEntry 7 }

fgGsm OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "GSM Signal Strength."
    ::= { fgSignalInfoEntry 8 }

fgWcdmaRssi OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RSSI for WCDMA."
    ::= { fgSignalInfoEntry 9 }

fgWcdmaEcio OBJECT-TYPE
    SYNTAX      Integer32(-65536..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "ECIO for WCDMA."
    ::= { fgSignalInfoEntry 10 }

fgLteRssi OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RSSI for LTE."
    ::= { fgSignalInfoEntry 11 }

fgLteRsrq OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RSRQ for LTE."
    ::= { fgSignalInfoEntry 12 }

fgLteRsrp OBJECT-TYPE
    SYNTAX      Integer32(-65536..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "RSRP for LTE."
    ::= { fgSignalInfoEntry 13 }

fgLteSnr OBJECT-TYPE
    SYNTAX      Integer32(-65536..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "SNR for LTE."
    ::= { fgSignalInfoEntry 14 }

fgTdma OBJECT-TYPE
    SYNTAX      Integer32(-128..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "TDMA Signal Strength."
    ::= { fgSignalInfoEntry 15 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgTrafficInfoTable
--

fgTrafficInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgTrafficInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table containing information applicable
         to data traffic information on an internal LTE modems."
    ::= { fgInternalLTEModemsInfo 4 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgTrafficInfoTable.fgTrafficInfoEntry
--

fgTrafficInfoEntry OBJECT-TYPE
    SYNTAX      FgTrafficInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to data traffic information on an internal LTE modem. If the modem with the modem index
	 is not present, it shows nothing."
    INDEX       { fgTrafMdmEntIndex }
    ::= { fgTrafficInfoTable 1 }

FgTrafficInfoEntry ::= SEQUENCE {
    fgTrafMdmEntIndex     FnIndex,
    fgTxPacksOK           Counter32,
    fgRxPacksOK           Counter32,
    fgTxPacksErr          Counter32,
    fgRxPacksErr          Counter32,
    fgTxPacksOverflow     Counter32,
    fgRxPacksOverflow     Counter32,
    fgTxBytesOK           Counter64,
    fgRxBytesOK           Counter64,
    fgLastCallTxBytesOK   Counter64,
    fgLastCallRxBytesOK   Counter64,
    fgTxPacksDrop         Counter32,
    fgRxPacksDrop         Counter32
}

fgTrafMdmEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of an internal LTE modem."
    ::= { fgTrafficInfoEntry 1 }

fgTxPacksOK OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tx Packets OK."
    ::= { fgTrafficInfoEntry 2 }

fgRxPacksOK OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Rx Packets OK."
    ::= { fgTrafficInfoEntry 3 }

fgTxPacksErr OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tx Packets Error."
    ::= { fgTrafficInfoEntry 4 }

fgRxPacksErr OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Rx Packets Error."
    ::= { fgTrafficInfoEntry 5 }

fgTxPacksOverflow OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tx Packets Overflow."
    ::= { fgTrafficInfoEntry 6 }

fgRxPacksOverflow OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Rx Packets Olverflow."
    ::= { fgTrafficInfoEntry 7 }

fgTxBytesOK OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tx Bytes OK."
    ::= { fgTrafficInfoEntry 8 }

fgRxBytesOK OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Rx Bytes OK."
    ::= { fgTrafficInfoEntry 9 }

fgLastCallTxBytesOK OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Last Call Tx Bytes OK."
    ::= { fgTrafficInfoEntry 10 }

fgLastCallRxBytesOK OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Last Call Rx Bytes OK."
    ::= { fgTrafficInfoEntry 11 }

fgTxPacksDrop OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Tx Packets Dropped."
    ::= { fgTrafficInfoEntry 12 }

fgRxPacksDrop OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Rx Packets Dropped."
    ::= { fgTrafficInfoEntry 13 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgSessInfoTable
--

fgSessInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgSessInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table containing information applicable
         to data session information on an internal LTE modems."
    ::= { fgInternalLTEModemsInfo 5 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgSessInfoTable.fgSessInfoEntry
--

fgSessInfoEntry OBJECT-TYPE
    SYNTAX      FgSessInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to data session information on an internal LTE modem. In QMI stack, each modem can have 2
	 sessions at the same time, IPv4 and IPv6. In the future if MBIM is adopted, we can have
	 multiple VLAN sessions at the same time. It shows nothing if the modem is not present."
    INDEX       { fgLteSessEntIndex }
    ::= { fgSessInfoTable 1 }

FgSessInfoEntry ::= SEQUENCE {
    fgLteSessEntIndex           FnIndex,
    fgSessMdmEntIndex           FnIndex,
    fdLteIfName                 DisplayString,
    fdLteSessConnStat           INTEGER,
    fdLteProfId                 Integer32,
    fdLteProfName               DisplayString,
    fdLteProfType               INTEGER,
    fdLtePdpType                INTEGER,
    fdLteProfApn                DisplayString,
    fdLteProfIpFamily           INTEGER,
    fdLteIpv4Addr               IpAddress,
    fdLteIpv4GwAddr             IpAddress,
    fdLteIpv4NetMask            IpAddress,
    fdLteIpv4PriDns             IpAddress,
    fdLteIpv4SecDns             IpAddress,
    fdLteIpv6Addr               Ipv6Address,
    fdLteIpv6PrefLen            Integer32,
    fdLteIpv6GwAddr             Ipv6Address,
    fdLteIpv6GwPrefLen          Integer32,
    fdLteIpv6PriDns             Ipv6Address,
    fdLteIpv6SecDns             Ipv6Address,
    fdLteMtu                    Integer32,
    fdLteAutoConn               INTEGER,
    fdLteNetType                INTEGER,
    fdLteNetTypeLas             INTEGER,
    fdLteLinkProto              INTEGER
}

fgLteSessEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of a data session on an internal LTE modem."
    ::= { fgSessInfoEntry 1 }

fgSessMdmEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of an internal LTE modem."
    ::= { fgSessInfoEntry 2 }

fdLteIfName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Interface Name of an internal LTE modem."
    ::= { fgSessInfoEntry 3 }

fdLteSessConnStat OBJECT-TYPE
    SYNTAX      INTEGER {
			unknown(0),
			disconnected(1),
			connected(2),
			suspended(3),
			authenticating(4)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Connection Status of a data session on an Internal LTE modem.
         The following enumerated values are supported:
			unknown(0)            - Unknown status.
			disconnected(1)       - Network is disconnected.
			connected(2)          - Network is connected.
			suspended(3)          - Network connection is suspended.
			authenticating(4)     -  Network authentication is ongoing."
    ::= { fgSessInfoEntry 4 }

fdLteProfId OBJECT-TYPE
    SYNTAX      Integer32(0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile index of a data session on an internal LTE modem."
    ::= { fgSessInfoEntry 5 }

fdLteProfName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile name of a data session on an internal LTE modem."
    ::= { fgSessInfoEntry 6 }

fdLteProfType OBJECT-TYPE
    SYNTAX      INTEGER {
			lpt3gpp(0),
			lpt3gpp2(1)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile type of a data session on an internal LTE modem.
         The following enumerated values are supported:
			lpt3gpp(0)   - 3GPP profile type.
			lpt3gpp2(1)  - 3gpp2 profile type. "
    ::= { fgSessInfoEntry 7 }

fdLtePdpType OBJECT-TYPE
    SYNTAX      INTEGER {
			ipv4(0),
			ppp(1),
			ipv6(2),
			ipv4v6(3)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile PDP context type of a data session on an internal LTE modem.
         The following enumerated values are supported:
			ipv4(0)   - IPv4.
			ppp(1)    - PPP.
			ipv6(2)   - IPv6.
			ipv4v6(3) - IPv4 and IPv6. "
    ::= { fgSessInfoEntry 8 }

fdLteProfApn OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile APN name of a data session on an internal LTE modem."
    ::= { fgSessInfoEntry 9 }

fdLteProfIpFamily OBJECT-TYPE
    SYNTAX      INTEGER {
			ipv4(4),
			ipv6(6),
			unspecified(8)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IP Family of a data session on an internal LTE modem.
         The following enumerated values are supported:
			ipv4(4)        - IPv4.
			ipv6(6)        - IPv6.
			unspecified(3) - None specified. "
    ::= { fgSessInfoEntry 10 }

fdLteIpv4Addr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv4 Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv4."
    ::= { fgSessInfoEntry 11 }

fdLteIpv4GwAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv4 Gateway Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv4."
    ::= { fgSessInfoEntry 12 }

fdLteIpv4NetMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv4 Net Mask of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv4."
    ::= { fgSessInfoEntry 13 }

fdLteIpv4PriDns OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv4 Primary DNS Server Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv4."
    ::= { fgSessInfoEntry 14 }

fdLteIpv4SecDns OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv4 Secondary DNS Server Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv4."
    ::= { fgSessInfoEntry 15 }

fdLteIpv6Addr OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv6 Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv6."
    ::= { fgSessInfoEntry 16 }

fdLteIpv6PrefLen OBJECT-TYPE
    SYNTAX      Integer32(0..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv6 Address Prefix of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv6."
    ::= { fgSessInfoEntry 17 }

fdLteIpv6GwAddr OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv6 Gateway Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv6."
    ::= { fgSessInfoEntry 18 }

fdLteIpv6GwPrefLen OBJECT-TYPE
    SYNTAX      Integer32(0..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile IPv6 Gateway Address Prefix of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv6."
    ::= { fgSessInfoEntry 19 }

fdLteIpv6PriDns OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile Primary DNS IPv6 Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv6."
    ::= { fgSessInfoEntry 20 }

fdLteIpv6SecDns OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile Secondary DNS IPv6 Address of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist or the data session is not
	of IP Family IPv6."
    ::= { fgSessInfoEntry 21 }

fdLteMtu OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile MTU of a data session on an internal LTE modem.
	It shows nothing if a data session does not exist."
    ::= { fgSessInfoEntry 22 }

fdLteAutoConn OBJECT-TYPE
    SYNTAX      INTEGER {
			disabled(0),
			enabled(1),
			paused(2)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile auto connection status of a data session on an internal LTE modem.
         The following enumerated values are supported:
			disabled(0)       - Disabled.
			enabled(1)        - Enabled.
			paused(2)         - Paused (resume on power cycle). "
    ::= { fgSessInfoEntry 23 }

fdLteNetType OBJECT-TYPE
    SYNTAX      INTEGER {
			unknown(-1),
			cdma1x(1),
			evdo(2),
			gsm(3),
			umts(4),
			evdoReva(5),
			edge(6),
			hsdpa(7),
			hsupa(8),
			hsdpaHsupa(9),
			lte(10),
			ehrpd(11),
			hsdpaPlus(12),
			hsdpaPlusHsupa(13),
			dchsdpaPlus(14),
			dchspdaPlusHsupa(15)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile network type of a data session on an internal LTE modem.
         The following enumerated values are supported:
			unknown(-1)            - Unknown.
			cdma1x(1)              - CDMA2000 1x.
			evdo(2)                - CDMA2000 HRPD 1xEV-DO.
			gsm(3)                 - GSM.
			umts(4)                - UMTS.
			evdoReva(5)            - CDMA2000 HRPD 1xEV-DO RevA.
			edge(6)                - EDGE.
			hsdpa(7)               - HSDPA and WCDMA.
			hsupa(8)               - WCDMA and HSUPA.
			hsdpaHsupa(9)          - HSDPA and HSUPA.
			lte(10)                - LTE.
			ehrpd(11)              - CDMA2000 eHRPD.
			hsdpaPlus(12)          - HSDPA+ and WCDMA.
			hsdpaPlusHsupa(13)     - HSDPA+ and HSUPA.
			dchsdpaPlus(14)        - DC-HSDPA+ and WCDMA.
			dchspdaPlusHsupa(15)   - DC-HSDPA+ and HSUPA."
    ::= { fgSessInfoEntry 24 }

fdLteNetTypeLas OBJECT-TYPE
    SYNTAX      INTEGER {
			unknown(-1),
			cdma1x(1),
			evdo(2),
			gsm(3),
			umts(4),
			evdoReva(5),
			edge(6),
			hsdpa(7),
			hsupa(8),
			hsdpaHsupa(9),
			lte(10),
			ehrpd(11),
			hsdpaPlus(12),
			hsdpaPlusHsupa(13),
			dchsdpaPlus(14),
			dchspdaPlusHsupa(15)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile network type of the last data session on an internal LTE modem.
         The following enumerated values are supported:
			unknown(-1)            - Unknown.
			cdma1x(1)              - CDMA2000 1x.
			evdo(2)                - CDMA2000 HRPD 1xEV-DO.
			gsm(3)                 - GSM.
			umts(4)                - UMTS.
			evdoReva(5)            - CDMA2000 HRPD 1xEV-DO RevA.
			edge(6)                - EDGE.
			hsdpa(7)               - HSDPA and WCDMA.
			hsupa(8)               - WCDMA and HSUPA.
			hsdpaHsupa(9)          - HSDPA and HSUPA.
			lte(10)                - LTE.
			ehrpd(11)              - CDMA2000 eHRPD.
			hsdpaPlus(12)          - HSDPA+ and WCDMA.
			hsdpaPlusHsupa(13)     - HSDPA+ and HSUPA.
			dchsdpaPlus(14)        - DC-HSDPA+ and WCDMA.
			dchspdaPlusHsupa(15)   - DC-HSDPA+ and HSUPA."
    ::= { fgSessInfoEntry 25 }

fdLteLinkProto OBJECT-TYPE
    SYNTAX      INTEGER {
			unknown(0),
			ieee8023(1),
			rawIp(2)
		}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Wireless profile data link protocol of a data session on an internal LTE modem.
         The following enumerated values are supported:
			unknown(0)       - Unknown.
			ieee8023(1)      - IEEE 802.3 ethernet mode.
			rawIp(2)         - Raw IP mode. "
    ::= { fgSessInfoEntry 26 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgGpsInfoTable
--

fgGpsInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgGpsInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table containing information applicable
         to GPS information on an internal LTE modems."
    ::= { fgInternalLTEModemsInfo 6 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgGpsInfoTable.fgGpsInfoEntry
--

fgGpsInfoEntry OBJECT-TYPE
    SYNTAX      FgGpsInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to GPS information on an internal LTE modem. If the modem with the modem index
	 is not present, GPS deamon is not enabled, or a GPS location is not acquired successfully,
	 it shows nothing. Also since no matter how many modems we have in a device, the location
	 information is the same, here the index is always 1."
    INDEX       { fgGpsMdmEntIndex }
    ::= { fgGpsInfoTable 1 }

FgGpsInfoEntry ::= SEQUENCE {
    fgGpsMdmEntIndex     FnIndex,
    fgGpsEnabled         INTEGER,
    fgLatitude           DisplayString,
    fgLongitude          DisplayString,
    fgUtcTime            DisplayString,
    fgLocalTime          DisplayString
}

fgGpsMdmEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of an internal LTE modem, here it is always 1."
    ::= { fgGpsInfoEntry 1 }

fgGpsEnabled OBJECT-TYPE
    SYNTAX      INTEGER { no(0), yes(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Is GPS daemon enabled."
    ::= { fgGpsInfoEntry 2 }

fgLatitude OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Latitude."
    ::= { fgGpsInfoEntry 3 }

fgLongitude OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Longitude."
    ::= { fgGpsInfoEntry 4 }

fgUtcTime OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "UTC Time."
    ::= { fgGpsInfoEntry 5 }

fgLocalTime OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Local Time."
    ::= { fgGpsInfoEntry 6 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgDatausageInfoTable
--

fgDatausageInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgDatausageInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table containing information applicable
         to data usage information on an internal LTE modems."
    ::= { fgInternalLTEModemsInfo 7 }

--
-- fortinet.fnFortiGateMib.fgInternalLTEModemsInfo.fgDatausageInfoTable.fgDatausageInfoEntry
--

fgDatausageInfoEntry OBJECT-TYPE
    SYNTAX      FgDatausageInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to data usage information on an internal LTE modem. If the modem with the modem index
	 is not present,  data usage trackingis not enabled, it shows nothing."
    INDEX       { fgDatausageMdmEntIndex }
    ::= { fgDatausageInfoTable 1 }

FgDatausageInfoEntry ::= SEQUENCE {
    fgDatausageMdmEntIndex     FnIndex,
    fgDatausageEnabled         INTEGER,
    fgDataOut                  Counter64,
    fgDataIn                   Counter64
}

fgDatausageMdmEntIndex OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Index used to identify the instance of an internal LTE modem."
    ::= { fgDatausageInfoEntry 1 }

fgDatausageEnabled OBJECT-TYPE
    SYNTAX      INTEGER{ no(0), yes(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Data usage tracking enabled."
    ::= { fgDatausageInfoEntry 2 }

fgDataOut OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Bytes Out."
    ::= { fgDatausageInfoEntry 3 }

fgDataIn OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Bytes In."
    ::= { fgDatausageInfoEntry 4 }

--
-- fortinet.fnFortiGateMib.fgNPU
--

fgNPU OBJECT IDENTIFIER
    ::= { fnFortiGateMib 20 }

--
-- fortinet.fnFortiGateMib.fgNPU.fgNPUInfo
--

FgNPUIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS      current
    DESCRIPTION
        "data type for NPU indexes"
    SYNTAX      Integer32 (0..255)

fgNPUInfo OBJECT IDENTIFIER
    ::= { fgNPU 1 }

fgNPUNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The number of NPUs in NPUTable"
    ::= { fgNPUInfo 1 }

fgNPUName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Name of the NPU"
    ::= { fgNPUInfo 2 }

fgNPUDrvDriftSum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Summation of driver session drift counters(fgNPUDrvDrift)"
    ::= { fgNPUInfo 3 }

--
-- fortinet.fnFortiGateMib.fgNPU.fgNPUTables
--

fgNPUTables OBJECT IDENTIFIER
    ::= { fgNPU 2 }

--
-- fortinet.fnFortiGateMib.fgNPU.fgNPUTables.fgNPUTable
--

fgNPUTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgNPUEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "A table of NPUs in the device"
    ::= { fgNPUTables 1 }

fgNPUEntry OBJECT-TYPE
    SYNTAX      FgNPUEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "An entry containing information applicable
         to a particular NPU"
    INDEX       { fgNPUEntIndex }
    ::= { fgNPUTable 1 }

FgNPUEntry ::= SEQUENCE {
    fgNPUEntIndex    FgNPUIndex,
    fgNPUSessionTblSize Gauge32,
    fgNPUSessionCount Gauge32,
    fgNPUDrvDrift Integer32
}

fgNPUEntIndex OBJECT-TYPE
    SYNTAX      FgNPUIndex (0..255)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "NPU index used to uniquely identify NPU in the system."
    ::= { fgNPUEntry 1 }

fgNPUSessionTblSize OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Size of session table in the NPU"
    ::= { fgNPUEntry 2 }

fgNPUSessionCount OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of active sessions in the NPU"
    ::= { fgNPUEntry 3 }

fgNPUDrvDrift OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Difference of session count between driver and hardware"
    ::= { fgNPUEntry 4 }

--
-- fortinet.fnFortiGateMib.fgLog
--

fgLog OBJECT IDENTIFIER
    ::= { fnFortiGateMib 21}

--
-- fortinet.fnFortiGateMib.fgLog.fgLogInfo
--

FgLogDeviceIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS      current
    DESCRIPTION
        "Data type for log device indexes"
    SYNTAX      Integer32 (0..255)

fgLogInfo OBJECT IDENTIFIER
    ::= { fgLog 1 }

fgLogDeviceNumber OBJECT-TYPE
    SYNTAX      Integer32
	MAX-ACCESS  read-only
	STATUS      current
	DESCRIPTION
	    "The number of log devices in LogDevices"
	::= { fgLogInfo 1 }

--
-- fortinet.fnFortiGateMib.fgLog.fgLogDevices
--

fgLogDevices OBJECT IDENTIFIER
	::= { fgLog 2 }

--
-- fortinet.fnFortiGateMib.fgLog.fgLogDevices.fgLogDeviceTable
--
fgLogDeviceTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF FgLogDeviceEntry
	MAX-ACCESS   not-accessible
	STATUS       current
	DESCRIPTION
	    "Table of log devices on the fortigate"
	::= { fgLogDevices 1 }

fgLogDeviceEntry OBJECT-TYPE
    SYNTAX      FgLogDeviceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry containing information about a specific log device"
    INDEX       { fgLogDeviceEntryIndex }
    ::= { fgLogDeviceTable 1 }

FgLogDeviceEntry ::= SEQUENCE {
	fgLogDeviceEntryIndex     FgLogDeviceIndex,
	fgLogDeviceEnabled        Integer32,
	fgLogDeviceName           DisplayString,
	fgLogDeviceSentCount      Counter32,
	fgLogDeviceRelayedCount   Counter32,
	fgLogDeviceCachedCount    Gauge32,
	fgLogDeviceFailedCount    Counter32,
	fgLogDeviceDroppedCount   Counter32
}

fgLogDeviceEntryIndex OBJECT-TYPE
    SYNTAX      FgLogDeviceIndex (0..255)
	MAX-ACCESS  not-accessible
	STATUS      current
	DESCRIPTION
	    "Log device index in the list of reported log devices"
	::= { fgLogDeviceEntry 1 }

fgLogDeviceEnabled OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
	DESCRIPTION
	    "Indicated whether the log device is enabled"
	::= { fgLogDeviceEntry 2 }

fgLogDeviceName OBJECT-TYPE
	SYNTAX      DisplayString (SIZE(0..64))
	MAX-ACCESS  read-only
	STATUS      current
    DESCRIPTION
	    "Name of the log device"
	::= { fgLogDeviceEntry 3 }

fgLogDeviceSentCount OBJECT-TYPE
    SYNTAX       Counter32
	MAX-ACCESS   read-only
	STATUS       current
	DESCRIPTION
	    "Number of logs which have been sent"
	::= { fgLogDeviceEntry 4 }

fgLogDeviceRelayedCount OBJECT-TYPE
    SYNTAX       Counter32
	MAX-ACCESS   read-only
	STATUS       current
	DESCRIPTION
	    "Number of logs which have been relayed"
	::= { fgLogDeviceEntry 5 }

fgLogDeviceCachedCount OBJECT-TYPE
    SYNTAX       Gauge32
	MAX-ACCESS   read-only
	STATUS       current
	DESCRIPTION
	    "Number of logs which are cached for later sending"
	::= { fgLogDeviceEntry 6 }

fgLogDeviceFailedCount OBJECT-TYPE
    SYNTAX       Counter32
	MAX-ACCESS   read-only
	STATUS       current
	DESCRIPTION
	    "Number of logs which have failed to send"
	::= { fgLogDeviceEntry 7 }

fgLogDeviceDroppedCount OBJECT-TYPE
    SYNTAX       Counter32
	MAX-ACCESS   read-only
	STATUS       current
	DESCRIPTION
	    "Number of logs which have been dropped"
	::= { fgLogDeviceEntry 8 }

--
-- fortinet.fnFortiGateMib.fgConfig
--

fgConfig OBJECT IDENTIFIER
    ::= { fnFortiGateMib 22 }

fgConfigStatus OBJECT IDENTIFIER
    ::= { fgConfig 1 }

fgConfigSerial OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The serial number of the current configuration, it is increased each time the configuration changes."
    ::= { fgConfigStatus 1 }

fgConfigChecksum OBJECT-TYPE
    SYNTAX      OCTET STRING
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The checksum of the current configuration."
    ::= { fgConfigStatus 2 }

fgConfigLastChangeTime OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "The value of sysUpTime when the configuration was last changed."
    ::= { fgConfigStatus 3 }

--
-- fortinet.fnFortiGateMib.fgDhcp
--

fgDhcp OBJECT IDENTIFIER
    ::= { fnFortiGateMib 23 }

--
-- fortinet.fnFortiGateMib.fgDhcp.fgDhcpInfo
--

fgDhcpInfo OBJECT IDENTIFIER
    ::= { fgDhcp 1 }

fgDhcpServerNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Number of configured DHCP servers on the FORTIGATE."
    ::= { fgDhcpInfo 1 }

--
-- fortinet.fnFortiGateMib.fgDhcp.fgDhcpTables
--

fgDhcpTables OBJECT IDENTIFIER
    ::= { fgDhcp 2 }

--
-- fortinet.fnFortiGateMib.fgDhcp.fgDhcpTables.fgDhcpTable
--

fgDhcpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgDhcpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "DHCP server table."
    ::= { fgDhcpTables 1 }

fgDhcpEntry OBJECT-TYPE
    SYNTAX      FgDhcpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "DHCP server entry."
    INDEX       { fgVdEntIndex }
    ::= { fgDhcpTable 1 }

FgDhcpEntry ::= SEQUENCE {
    fgDhcpLeaseUsage   Integer32
}

fgDhcpLeaseUsage OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
        "Lease Usage of the DHCP server."
    ::= { fgDhcpEntry 2 }

--
-- fortinet.fnFortiGateMib.fgDhcp.fgDhcpTrapObjects
--

fgDhcpTrapObjects OBJECT IDENTIFIER
    ::= { fgDhcp 3 }

fgDhcpTrapType OBJECT-TYPE
    SYNTAX      INTEGER {
                  runOutOfIPPool(1),
                  conflictIP(2),
                  receivedNAK(3)
                }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DHCP trap type, there are 3 different values as below:
           runOutOfIPPool(1)  - DHCP server runs out of ip pool.
           conflictIP(2)      - IP address is already in use.
           receivedNAK(3)     - DHCP client interface received nak."
    ::= { fgDhcpTrapObjects 1 }

fgDhcpTrapMessage OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "DHCP trap message including some optional server infos."
    ::= { fgDhcpTrapObjects 2 }

fgDhcpServerId OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION 
        "An ID that uniquely identifies a DHCP server within a vdom."
    ::= { fgDhcpTrapObjects 3 }

--
-- fortinet.fnFortiGateMib.fgSw
--

fgSw OBJECT IDENTIFIER
    ::= { fnFortiGateMib 24 }

--
-- fortinet.fnFortiGateMib.fgSw.fgSwDeviceInfo
--

fgSwDeviceInfo OBJECT IDENTIFIER
    ::= { fgSw 1 }

--
-- fortinet.fnFortiGateMib.fgSw.fgSwDeviceInfo.fgSwDeviceTable
--

fgSwDeviceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgSwDeviceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch device table."
    ::= { fgSwDeviceInfo 1 }

fgSwDeviceEntry OBJECT-TYPE
    SYNTAX      FgSwDeviceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch device entry."
    INDEX       { fgVdEntIndex, fgSwDevicePlatform, fgSwDeviceId }
    ::= { fgSwDeviceTable 1 }

FgSwDeviceEntry ::= SEQUENCE {
    fgSwDevicePlatform         FnIndex,
    fgSwDeviceId               FnIndex,
    fgSwDeviceSerialNum        DisplayString,
    fgSwDeviceName             DisplayString,
    fgSwDeviceVersion          DisplayString,
    fgSwDeviceAuthorized       INTEGER,
    fgSwDeviceStatus           INTEGER,
    fgSwDeviceJoinTime         Gauge32,
    fgSwDeviceIp               IpAddress,
    fgSwDeviceFlag             DisplayString,
    fgSwCpu                    Integer32,
    fgSwMemory                 Integer32,
    fgSwDeviceIpv6             Ipv6Address
}

fgSwDevicePlatform OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch device platform type."
    ::= { fgSwDeviceEntry 1 }

fgSwDeviceId OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch device ID."
    ::= { fgSwDeviceEntry 2 }

fgSwDeviceSerialNum OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device serial number."
    ::= { fgSwDeviceEntry 3 }

fgSwDeviceName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device name."
    ::= { fgSwDeviceEntry 4 }

fgSwDeviceVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device software version."
    ::= { fgSwDeviceEntry 5 }

fgSwDeviceAuthorized OBJECT-TYPE
    SYNTAX      INTEGER { discovered(0), disabled(1), authorized(2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device connection admin state."
    ::= { fgSwDeviceEntry 6 }

fgSwDeviceStatus OBJECT-TYPE
    SYNTAX      INTEGER { down(0), up(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device connection status."
    ::= { fgSwDeviceEntry 7 }

fgSwDeviceJoinTime OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time switch device become online."
    ::= { fgSwDeviceEntry 8 }

fgSwDeviceIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device IP address."
    ::= { fgSwDeviceEntry 9 }

fgSwDeviceFlag OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device transaction flag (C=config sync, U=upgrading, S=staged, D=delay reboot pending, E=config sync error)."
    ::= { fgSwDeviceEntry 10 }

fgSwCpu OBJECT-TYPE
    SYNTAX      Integer32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch CPU usage percentage."
    ::= { fgSwDeviceEntry 11 }

fgSwMemory OBJECT-TYPE
    SYNTAX      Integer32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch memory usage percentage."
    ::= { fgSwDeviceEntry 12 }

fgSwDeviceIpv6 OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch device IPv6 address."
    ::= { fgSwDeviceEntry 13 }

--
-- fortinet.fnFortiGateMib.fgSw.fgSwPortInfo
--

fgSwPortInfo OBJECT IDENTIFIER
    ::= { fgSw 2 }

--
-- fortinet.fnFortiGateMib.fgSw.fgSwPortInfo.fgSwPortTable
--

fgSwPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgSwPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch port table."
    ::= { fgSwPortInfo 1 }

fgSwPortEntry OBJECT-TYPE
    SYNTAX      FgSwPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch port entry."
    INDEX       { fgVdEntIndex, fgSwPortSwitchPlatform, fgSwPortSwitchId, fgSwPortNum }
    ::= { fgSwPortTable 1 }

FgSwPortEntry ::= SEQUENCE {
    fgSwPortSwitchPlatform     FnIndex,
    fgSwPortSwitchId           FnIndex,
    fgSwPortNum                FnIndex,
    fgSwPortSwitchSerialNum    DisplayString,
    fgSwPortName               DisplayString,
    fgSwPortStatus             INTEGER,
    fgSwPortSpeedDuplex        DisplayString,
    fgSwPortNativeVlan         Integer32,
    fgSwPortAllowedVlan        DisplayString,
    fgSwPortUntaggedVlan       DisplayString,
    fgSwPortPOE                INTEGER,
    fgSwPortPOEStatus          INTEGER,
    fgSwPortPOEState           DisplayString,
    fgSwPortPOEPower           DisplayString 
}

fgSwPortSwitchPlatform OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch port platform type."
    ::= { fgSwPortEntry 1 }

fgSwPortSwitchId OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch port ID."
    ::= { fgSwPortEntry 2 }

fgSwPortNum OBJECT-TYPE
    SYNTAX      FnIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Switch port logical number."
    ::= { fgSwPortEntry 3 }

fgSwPortSwitchSerialNum OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port serial number."
    ::= { fgSwPortEntry 4 }

fgSwPortName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port name."
    ::= { fgSwPortEntry 5 }

fgSwPortStatus OBJECT-TYPE
    SYNTAX      INTEGER { down(0), up(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port status."
    ::= { fgSwPortEntry 6 }

fgSwPortSpeedDuplex OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port speed."
    ::= { fgSwPortEntry 7 }

fgSwPortNativeVlan OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port native vlan."
    ::= { fgSwPortEntry 8 }

fgSwPortAllowedVlan OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port allowed vlan."
    ::= { fgSwPortEntry 9 }

fgSwPortUntaggedVlan OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port untagged vlan."
    ::= { fgSwPortEntry 10 }

fgSwPortPOE OBJECT-TYPE
    SYNTAX      INTEGER { notcapable(0), capable(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port PoE capability."
    ::= { fgSwPortEntry 11 }

fgSwPortPOEStatus OBJECT-TYPE
    SYNTAX      INTEGER { disable(0), enable(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port PoE status."
    ::= { fgSwPortEntry 12 }

fgSwPortPOEState OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port PoE state (disabled, searching, delivering-power, requesting-power, fault)."
    ::= { fgSwPortEntry 13 }

fgSwPortPOEPower OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Switch port PoE power in mW."
    ::= { fgSwPortEntry 14 }

--
-- fortinet.fnFortiGateMib.fgChassis
--

fgChassis OBJECT IDENTIFIER
    ::= { fnFortiGateMib 25 }

--
-- fortinet.fnFortiGateMib.fgChassis.fgChassisInfo
--

fgChassisInfo OBJECT IDENTIFIER
    ::= { fgChassis 1 }

fgChassisVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current version of the Fortigate Chassis."
    ::= { fgChassisInfo 1 }

--
-- fortinet.fnFortiGateMib.fgChassis.fgChassisTrapObjects
--

fgChassisTrapObjects OBJECT IDENTIFIER
    ::= { fgChassis 2 }

fgChassisSlotId OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Chassis slot Id at current chassis."
    ::= { fgChassisTrapObjects 1 }

fgChassisTrapMessage OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "SLBC trap message display."
    ::= { fgChassisTrapObjects 2 }

--
-- fortinet.fnFortiGateMib.fgServiceGroupWorkerBlades
--

fgServiceGroupWorkerBlades OBJECT IDENTIFIER
    ::= { fnFortiGateMib 26 }

--
-- fortinet.fnFortiSwitchMib.fgServiceGroupWorkerBlades.fgSgWbTables
--

fgSgWbTables OBJECT IDENTIFIER
    ::= { fgServiceGroupWorkerBlades 1 }

fgSgWorkerBladeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF FgSgWorkerBladeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of all service group worker blades."
    ::= { fgSgWbTables 1 }

fgSgWorkerBladeEntry OBJECT-TYPE
    SYNTAX      FgSgWorkerBladeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "System info for a particular worker blade."
    INDEX       { fgSgWbEntIndex }
    ::= { fgSgWorkerBladeTable 1 }

FgSgWorkerBladeEntry ::= SEQUENCE {
    fgSgWbEntIndex           FgSgWorkerBladeIndex,
    fgSgWbServiceGroupID     Integer32,
    fgSgWbChassisID          Integer32,
    fgSgWbSlotID             Integer32,
    fgSgWbState              FgSgWorkerBladeState,
    fgSgWbStatusMsg          DisplayString,
    fgSgWbMaster             INTEGER,
    fgSgWbSysVersion         DisplayString,
    fgSgWbSysSerial          DisplayString,
    fgSgWbSysUpTime          TimeTicks,
    fgSgWbSysCpuUsage        Gauge32,
    fgSgWbSysMemUsage        Gauge32,
    fgSgWbBaseLink           DisplayString,
    fgSgWbFabricLink         DisplayString,
    fgSgWbDataHb             DisplayString,
    fgSgWbMgmtHb             DisplayString
}
--
-- fortinet.fnFortiSwitchMib.fgServiceGroupWorkerBlades.fgSgWbTables.fgSgWorkerBladeTable.fgSgWorkerBladeEntry
--

fgSgWbEntIndex OBJECT-TYPE
    SYNTAX      FgSgWorkerBladeIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Index that uniquely identifies a worker blade in the fgSgWorkerBladeTable."
    ::= { fgSgWorkerBladeEntry 1 }

fgSgWbServiceGroupID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Service group identifier that this worker blade belongs to."
    ::= { fgSgWorkerBladeEntry 2 }

fgSgWbChassisID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Chassis identifier of the chassis this worker blade is installed in."
    ::= { fgSgWorkerBladeEntry 3 }

fgSgWbSlotID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Slot identifier of the slot this worker blade is installed in."
    ::= { fgSgWorkerBladeEntry 4 }

fgSgWbState OBJECT-TYPE
    SYNTAX      FgSgWorkerBladeState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "State of this worker blade."
    ::= { fgSgWorkerBladeEntry 5 }

fgSgWbStatusMsg OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Status message for this worker blade."
    ::= { fgSgWorkerBladeEntry 6 }

fgSgWbMaster OBJECT-TYPE
    SYNTAX      INTEGER { false(0), true(1) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if this worker blade is the service group primary."
    ::= { fgSgWorkerBladeEntry 7 }

fgSgWbSysVersion OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Firmware version of this worker blade"
    ::= { fgSgWorkerBladeEntry 8 }

fgSgWbSysSerial OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "fnSysSerial serial number of this worker blade."
    ::= { fgSgWorkerBladeEntry 9}

fgSgWbSysUpTime OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "MIB-2 system up time of this worker blade. The time (in hundredths
             of a second) since the network management portion of the
             system was last re-initialized."
    ::= { fgSgWorkerBladeEntry 10 }

fgSgWbSysCpuUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current CPU usage (percentage)"
    ::= { fgSgWorkerBladeEntry 11 }

fgSgWbSysMemUsage OBJECT-TYPE
    SYNTAX      Gauge32 (0..100)
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current memory utilization (percentage)"
    ::= { fgSgWorkerBladeEntry 12 }

fgSgWbBaseLink OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Status of base channel link to this worker blade."
    ::= { fgSgWorkerBladeEntry 13 }

fgSgWbFabricLink OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Status of fabric channel link to this worker blade."
    ::= { fgSgWorkerBladeEntry 14 }
fgSgWbDataHb OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Status of data heartbeat from this worker blade."
    ::= { fgSgWorkerBladeEntry 15 }

fgSgWbMgmtHb OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..128))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Status of management heartbeat from this worker blade."
    ::= { fgSgWorkerBladeEntry 16 }

--
-- fortinet.fnFortiGateMib.fgMibConformance
--

fgMibConformance OBJECT IDENTIFIER
    ::= { fnFortiGateMib 100 }

--
-- fortinet.fnFortiGateMib.fgTraps
--

fgTrapVpnTunUp NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgVpnTrapLocalGateway, 
                  fgVpnTrapRemoteGateway, fgVpnTrapPhase1Name }
    STATUS      current
    DESCRIPTION 
        "Indicates that the specified VPN tunnel has been brought up."
    ::= { fgTrapPrefix 301 }

fgTrapVpnTunDown NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgVpnTrapLocalGateway, 
                  fgVpnTrapRemoteGateway, fgVpnTrapPhase1Name }
    STATUS      current
    DESCRIPTION 
        "The specified VPN tunnel has been brought down."
    ::= { fgTrapPrefix 302 }

fgTrapHaSwitch NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "The specified cluster member has transitioned from a secondary role to a primary role."
    ::= { fgTrapPrefix 401 }

fgTrapHaStateChange NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      deprecated
    DESCRIPTION 
        "Trap being sent when the HA cluster
         member changes its state"
    ::= { fgTrapPrefix 402 }

fgTrapHaHBFail NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      current
    DESCRIPTION 
        "The heartbeat device failure count has exceeded the configured threshold."
    ::= { fgTrapPrefix 403 }

fgTrapHaMemberDown NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      current
    DESCRIPTION 
        "The specified device (by serial number) is moving to a down state."
    ::= { fgTrapPrefix 404 }

fgTrapHaMemberUp NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      current
    DESCRIPTION 
        "A new cluster member has joined the cluster."
    ::= { fgTrapPrefix 405 }

fgTrapIpsSignature NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgIpsTrapSigId, fgIpsTrapSrcIp, fgIpsTrapSigMsg }
    STATUS      current
    DESCRIPTION 
        "An IPS signature has been triggered"
    ::= { fgTrapPrefix 503 }

fgTrapIpsAnomaly NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgIpsTrapSigId, fgIpsTrapSrcIp, fgIpsTrapSigMsg }
    STATUS      current
    DESCRIPTION 
        "An IPS anomaly has been detected"
    ::= { fgTrapPrefix 504 }

fgTrapIpsPkgUpdate NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "The IPS signature database has been updated"
    ::= { fgTrapPrefix 505 }

fgTrapIpsFailOpen NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION
        "The IPS network buffer is full"
    ::= { fgTrapPrefix 506 }

fgTrapAvVirus NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgAvTrapVirName }
    STATUS      current
    DESCRIPTION 
        "A virus has been detected by the anti-virus engine"
    ::= { fgTrapPrefix 601 }

fgTrapAvOversize NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "An over-sized file has been detected by the anti-virus engine"
    ::= { fgTrapPrefix 602 }

fgTrapAvPattern NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "The anti-virus engine has blocked a file because it matched a configured pattern"
    ::= { fgTrapPrefix 603 }

fgTrapAvFragmented NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "The anti-virus engine has detected a fragmented file"
    ::= { fgTrapPrefix 604 }

fgTrapAvEnterConserve NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "The anti-virus engine has entered conservation mode due to low memory conditions"
    ::= { fgTrapPrefix 605 }

fgTrapAvBypass NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "The anti-virus engine has been bypassed due to conservation mode"
    ::= { fgTrapPrefix 606 }

fgTrapAvOversizePass NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "An over-sized file has been detected,
         but has been passed due to configuration"
    ::= { fgTrapPrefix 607 }

fgTrapAvOversizeBlock NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName }
    STATUS      current
    DESCRIPTION 
        "An over-sized file has been detected
         and has been blocked"
    ::= { fgTrapPrefix 608 }

fgTrapFazDisconnect NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      current
    DESCRIPTION 
        "The device has been disconnected from the FortiAnalyzer."
    ::= { fgTrapPrefix 701 }
    
fgTrapFaz NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgFazTrapType }
    STATUS      current
    DESCRIPTION
        "A trap for Fortianalyzer event."
    ::= { fgTrapPrefix 702}

fgTrapWcApUp NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgWcApVdom, fgWcApSerial, fgWcApName }
    STATUS      current
    DESCRIPTION 
        "Indicates that the specified AP is up."
    ::= { fgTrapPrefix 801 }
    
fgTrapWcApDown NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgWcApVdom, fgWcApSerial, fgWcApName }
    STATUS      current
    DESCRIPTION 
        "Indicates that the specified AP is down."
    ::= { fgTrapPrefix 802 }

fgTrapFcSwUp NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgFcSwVdom, fgFcSwSerial, fgFcSwName }
    STATUS      current
    DESCRIPTION 
        "Indicates that the specified switch controller session is up."
    ::= { fgTrapPrefix 901 }
    
fgTrapFcSwDown NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgFcSwVdom, fgFcSwSerial, fgFcSwName }
    STATUS      current
    DESCRIPTION 
        "Indicates that the specified switch controller session is down."
    ::= { fgTrapPrefix 902 }

fgFmTrapDeployComplete NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      current
    DESCRIPTION 
        "Indicates when deployment of a new configuration has been completed. Used for verification by FortiManager."
    ::= { fgFmTrapPrefix 1000 }

fgFmTrapDeployInProgress NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      current
    DESCRIPTION 
        "Indicates that a configuration change was not immediate and that the change is currently in progress. Used for verification by FortiManager."
    ::= { fgFmTrapPrefix 1002 }

fgFmTrapConfChange NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial }
    STATUS      current
    DESCRIPTION 
        "The device configuration has been changed by something other than the managing FortiManager device."
    ::= { fgFmTrapPrefix 1003 }

fgFmTrapIfChange NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, ifName, fgManIfIp, fgManIfMask, fgManIfIp6 }
    STATUS      current
    DESCRIPTION 
        "Trap is sent to the managing FortiManager if an interface IP is changed"
    ::= { fgFmTrapPrefix 1004 }

fgTrapServerLoadBalanceRealServerDown NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgServerLoadBalanceRealServerAddress, fgServerLoadBalanceVirtualServerName, fgServerLoadBalanceRealServerAddress6 }
    STATUS      current
    DESCRIPTION 
        "One of servers in a Server Load Balance group goes down."
    ::= { fgTrapPrefix 1101 }

fgTrapPerCpuHigh NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgPerCpuHighDetails }
    STATUS      current
    DESCRIPTION 
        "Indicates that per-CPU usage has exceeded the configured threshold."
    ::= { fgTrapPrefix 1102 }

fgTrapDeviceNew NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, ifIndex, fgVdEntIndex, fgDeviceCreated, fgDeviceLastSeen, fgDeviceMacAddress }
    STATUS      current
    DESCRIPTION 
        "A new device is found."
    ::= { fgTrapPrefix 1201 }

fgTrapDhcp NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, ifName, fgVdEntName, fgDhcpServerId, fgDhcpTrapType, fgDhcpTrapMessage }
    STATUS      current
    DESCRIPTION 
        "A trap for DHCP or DHCP6 event."
    ::= { fgTrapPrefix 1301 }

fgTrapPoolUsage NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgFwIppTrapType, fgFwIppStatsName, fgFwIppStatsGroupName, fgFwTrapPoolUtilization, fgFwIppTrapPoolProto }
    STATUS      current
    DESCRIPTION 
        "A trap for ippool."
    ::= { fgTrapPrefix 1401 }

fgTrapSlbc NOTIFICATION-TYPE
    OBJECTS     { fnSysSerial, sysName, fgChassisSlotId, fgChassisTrapMessage }
    STATUS      current
    DESCRIPTION 
        "A trap for SLBC event."
    ::= { fgTrapPrefix 1501 }

--
-- fortinet.fnFortiGateMib.fgMibConformance
--

fgFmTrapGroup NOTIFICATION-GROUP
    NOTIFICATIONS { fgFmTrapDeployComplete, fgFmTrapDeployInProgress, 
                  fgFmTrapConfChange, fgFmTrapIfChange }
    STATUS      current
    DESCRIPTION 
        "Traps are intended for use in conjunction with a FortiManager."
    ::= { fgMibConformance 1 }

fgFmTrapObjectGroup OBJECT-GROUP
    OBJECTS     { fgManIfIp, fgManIfMask,fgManIfIp6, fgFazTrapType }
    STATUS      current
    DESCRIPTION 
        "These objects support the traps in the fgFmTrapGroup."
    ::= { fgMibConformance 2 }

fgAdminObjectGroup OBJECT-GROUP
    OBJECTS     { fgAdminIdleTimeout, fgAdminLcdProtection }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to administration of the device."
    ::= { fgMibConformance 3 }

fgSystemObjectGroup OBJECT-GROUP
    OBJECTS     { fgSysVersion, fgSysCpuUsage,
                  fgSysMemUsage, fgSysMemCapacity,
                  fgSysDiskUsage, fgSysDiskCapacity,
                  fgSysSesCount, fgSysLowMemUsage,
                  fgSysLowMemCapacity, fgSysSesRate1,
                  fgSysSesRate10, fgSysSesRate30,
                  fgSysSesRate60,
                  fgSysSes6Count, fgSysSes6Rate1,
                  fgSysSes6Rate10, fgSysSes6Rate30,
                  fgSysSes6Rate60, fgSysUpTime,
                  fgSysNeedLogPartitionScan, fgSysUpTimeDetail,
                  fgSysNpuSesCount, fgSysNpuSesRate1,
                  fgSysNpuSesRate10, fgSysNpuSesRate30,
                  fgSysNpuSesRate60, fgSysNpuSes6Count,
                  fgSysNpuSes6Rate1, fgSysNpuSes6Rate10,
                  fgSysNpuSes6Rate30, fgSysNpuSes6Rate60,
                  fgSysNeedLogPartitionScan, fgSysUpTimeDetail,
                  fgSysRebootReason,
                  fgDataCpuUsage, fgDataMemUsage
                }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to the system status of the device."
    ::= { fgMibConformance 4 }

fgSoftwareObjectGroup OBJECT-GROUP
    OBJECTS     { fgSysVersionAv, fgSysVersionIps, fgSysVersionAvEt, fgSysVersionIpsEt }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to software running on the device."
    ::= { fgMibConformance 5 }

fgHwSensorsObjectGroup OBJECT-GROUP
    OBJECTS     { fgHwSensorCount, fgHwSensorEntName,
                  fgHwSensorEntValue, fgHwSensorEntAlarmStatus }
    STATUS      current
    DESCRIPTION
        "Object pertaining to hardware sensors on the device."
    ::= { fgMibConformance 6 }

fgHighAvailabilityObjectGroup OBJECT-GROUP
    OBJECTS     { fgHaSystemMode, fgHaGroupId, fgHaPriority, fgHaOverride,
                  fgHaAutoSync, fgHaSchedule, fgHaGroupName, fgHaStatsSerial, 
                  fgHaStatsCpuUsage, fgHaStatsMemUsage, 
                  fgHaStatsNetUsage, fgHaStatsSesCount, 
                  fgHaStatsPktCount, fgHaStatsByteCount, 
                  fgHaStatsIdsCount, fgHaStatsAvCount, fgHaStatsHostname, 
                  fgHaStatsSyncStatus, fgHaStatsSyncDatimeSucc, fgHaStatsSyncDatimeUnsucc, 
                  fgHaStatsGlobalChecksum, fgHaStatsPrimarySerial, fgHaStatsAllChecksum,
                  fgHaTrapMemberSerial }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to High Availability clustering of FortiGate devices."
    ::= { fgMibConformance 7 }

fgVpnObjectGroup OBJECT-GROUP
    OBJECTS     { fgVpnDialupGateway, fgVpnDialupLifetime, 
                  fgVpnDialupTimeout, fgVpnDialupSrcBegin, 
                  fgVpnDialupSrcEnd, fgVpnDialupDstAddr, fgVpnDialupVdom, 
                  fgVpnDialupInOctets, fgVpnDialupOutOctets, 
                  fgVpnTunEntPhase1Name, fgVpnTunEntPhase2Name, 
                  fgVpnTunEntRemGwyIp, fgVpnTunEntRemGwyPort, 
                  fgVpnTunEntLocGwyIp, fgVpnTunEntLocGwyPort, 
                  fgVpnTunEntSelectorSrcBeginIp, 
                  fgVpnTunEntSelectorSrcEndIp, 
                  fgVpnTunEntSelectorSrcPort, 
                  fgVpnTunEntSelectorDstBeginIp, 
                  fgVpnTunEntSelectorDstEndIp, 
                  fgVpnTunEntSelectorDstPort, fgVpnTunEntSelectorProto, 
                  fgVpnTunEntLifeSecs, fgVpnTunEntLifeBytes, 
                  fgVpnTunEntTimeout, fgVpnTunEntInOctets, 
                  fgVpnTunEntOutOctets, fgVpnTunEntStatus, 
                  fgVpnTunEntVdom, fgVpnSslState, 
                  fgVpnSslStatsLoginUsers, fgVpnSslStatsMaxUsers, 
                  fgVpnSslStatsActiveWebSessions, 
                  fgVpnSslStatsMaxWebSessions, 
                  fgVpnSslStatsActiveTunnels, fgVpnSslStatsMaxTunnels, 
                  fgVpnSslTunnelVdom, fgVpnSslTunnelUserName, 
                  fgVpnSslTunnelSrcIp, fgVpnSslTunnelIp, 
                  fgVpnSslTunnelUpTime, fgVpnSslTunnelBytesIn, 
                  fgVpnSslTunnelBytesOut, fgVpnTrapLocalGateway, 
                  fgVpnTrapRemoteGateway, fgVpnTrapPhase1Name, fgVpnTunnelUpCount, 
                  fgVpn2DialupGatewayType, fgVpn2DialupGateway, 
                  fgVpn2DialupLifetime, fgVpn2DialupTimeout, 
                  fgVpn2DialupSrcBeginType, fgVpn2DialupSrcBegin, 
                  fgVpn2DialupSrcEndType, fgVpn2DialupSrcEnd, 
                  fgVpn2DialupDstBeginType, fgVpn2DialupDstBegin, 
                  fgVpn2DialupDstEndType, fgVpn2DialupDstEnd, 
                  fgVpn2DialupInOctets, fgVpn2DialupOutOctets, 
                  fgVpn2DialupPhase1Name, fgVpn2DialupVdom, 
                  fgVpn2TunPhase1Name, fgVpn2TunPhase2Name, 
                  fgVpn2TunRemGwyIpType, fgVpn2TunRemGwyIp, fgVpn2TunRemGwyPort, 
                  fgVpn2TunLocGwyIpType, fgVpn2TunLocGwyIp, fgVpn2TunLocGwyPort, 
                  fgVpn2TunSelSrcBeginIpType, fgVpn2TunSelSrcBeginIp, 
                  fgVpn2TunSelSrcEndIpType, fgVpn2TunSelSrcEndIp, 
                  fgVpn2TunSelDstBeginIpType, fgVpn2TunSelDstBeginIp, 
                  fgVpn2TunSelDstEndIpType, fgVpn2TunSelDstEndIp, 
                  fgVpn2TunSelSrcPort, fgVpn2TunSelDstPort, fgVpn2TunSelProto, 
                  fgVpn2TunLifeSecs, fgVpn2TunLifeBytes, 
                  fgVpn2TunTimeout, fgVpn2TunInOctets, 
                  fgVpn2TunOutOctets, fgVpn2TunStatus, 
                  fgVpn2TunVdom 
                }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to Virtual Priavet Networking on FortiGate devices."
    ::= { fgMibConformance 8 }

fgFirewallObjectGroup OBJECT-GROUP
    OBJECTS     { fgFwPolPktCount, fgFwPolByteCount, fgFwUserNumber,
                  fgFwPolPktCountHc, fgFwPolByteCountHc,
                  fgFwUserAuthTimeout, fgFwUserName, fgFwUserAuth,
                  fgFwUserState, fgFwUserVdom, fgIpSessProto,
                  fgIpSessFromAddr, fgIpSessFromPort, fgIpSessToAddr,
                  fgIpSessToPort, fgIpSessExp, fgIpSessVdom,
                  fgIpSessNumber, fgIp6SessNumber, fgFwPolLastUsed,
                  fgFwIppStatsName, fgFwIppStatsType,
                  fgFwIppStatsTotalSessions, fgFwIppStatsTcpSessions,
                  fgFwIppStatsUdpSessions, fgFwIppStatsOtherSessions,
                  fgFwIppStatsTotalPBAs,fgFwIppStatsInusePBAs,
                  fgFwIppStatsExpiringPBAs, fgFwIppStatsFreePBAs,
                  fgFwIppStatsFlags, fgFwIppStatsGroupName, fgFwIppStatsBlockSize,
                  fgFwIppStatsPortStart, fgFwIppStatsPortEnd,
                  fgFwIppStatsStartClientIP, fgFwIppStatsEndClientIP,
                  fgFwIppStatsRscTCP, fgFwIppStatsRscUDP,
                  fgFwIppStatsUsedRscTCP, fgFwIppStatsUsedRscUDP,
                  fgFwIppStatsPercentageTCP, fgFwIppStatsPercentageUDP,
                  fgFwIppTrapType, fgFwIppTrapPoolProto, fgFwTrapPoolUtilization,
                  fgFwGtpStatsRequest, fgFwGtpStatsEchoRequest, fgFwGtpStatsTunnel,
                  fgFwGtpStatsTunnelV0, fgFwGtpStatsPath, fgFwGtpStatsBearer,
                  fgFwGtpStatsFteid, fgFwGtpStatsProfile, fgFwGtpStatsImsi,
                  fgFwGtpStatsApn, fgFwGtpStatsApnShaper, fgFwGtpStatsTunnelLimiter,
                  fgFwGtpStatsAdvPolicies, fgFwGtpStatsIeRemovePolicies, fgFwGtpStatsIpPolicy,
                  fgFwGtpStatsNoipPolicy, fgFwGtpStatsIeWlEntry, fgFwGtpStatsClash, fgFwGtpStatsDrop,
                  fgFwGtpRtStatsCForwarded, fgFwGtpRtStatsCRejected, fgFwGtpRtStatsCDropped0,
                  fgFwGtpRtStatsCDropped1, fgFwGtpRtStatsCDropped2, fgFwGtpRtStatsCDropped3,
                  fgFwGtpRtStatsCDropped4, fgFwGtpRtStatsCDropped5, fgFwGtpRtStatsCDropped6,
                  fgFwGtpRtStatsCDropped7, fgFwGtpRtStatsCDropped8, fgFwGtpRtStatsCDropped9,
                  fgFwGtpRtStatsCDropped10, fgFwGtpRtStatsCDropped11, fgFwGtpRtStatsCDropped12,
                  fgFwGtpRtStatsCDropped13, fgFwGtpRtStatsCDropped14, fgFwGtpRtStatsCDropped15,
                  fgFwGtpRtStatsCDropped16, fgFwGtpRtStatsCDropped17, fgFwGtpRtStatsCDropped18,
                  fgFwGtpRtStatsCDropped19, fgFwGtpRtStatsCDropped20, fgFwGtpRtStatsCDropped21,
                  fgFwGtpRtStatsCDropped22, fgFwGtpRtStatsCDropped23, fgFwGtpRtStatsDForwarded,
                  fgFwGtpRtStatsDDroppedSanity, fgFwGtpRtStatsDDroppedMalMsg, fgFwGtpRtStatsDDroppedNoState,
                  fgFwGtpRtStatsDDroppedMalIe, fgFwGtpRtStatsDDroppedGtpInGtp, fgFwGtpRtStatsDDroppedSpoof,
                  fgFwGtpRtStatsDDroppedIpPol, fgFwGtpRtStatsDDroppedMsgFilter, fgFwGtpRtStatsDDroppedMsgRateLimit,
                  fgFwGtpRtStatsDDroppedUnknownVersion, fgFwGtpRtStatsBForwarded, fgFwGtpRtStatsBDroppedSanity,
                  fgFwGtpRtStatsBDroppedMalMsg, fgFwGtpRtStatsBDroppedMalIe, fgFwGtpRtStatsBDroppedMsgFilter,
                  fgFwAddrDynEmsName, fgFwAddrDynEmsAddresses,
                  fgFwAuthIpv4UserNumber, fgFwAuthIpv6UserNumber,
                  fgFwAuthIpv4UserVdom, fgFwAuthIpv4UserName,
                  fgFwAuthIpv4UserType, fgFwAuthIpv4UserAddr,
                  fgFwAuthIpv6UserVdom, fgFwAuthIpv6UserName,
                  fgFwAuthIpv6UserType, fgFwAuthIpv6UserAddr,
                  fgFwHsPolPktCount, fgFwHsPolByteCount, fgFwHsPolLastUsed }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Firewall functionality on FortiGate devices."
    ::= { fgMibConformance 9 }

fgAppServicesObjectGroup OBJECT-GROUP
    OBJECTS     { fgApHTTPReqProcessed,
                  fgApSMTPReqProcessed, fgApSMTPSpamDetected,
                  fgApPOP3ReqProcessed, fgApPOP3SpamDetected,
                  fgApIMAPReqProcessed, fgApIMAPSpamDetected,
                  fgApNNTPReqProcessed, fgApIMUpTime, fgApIMMemUsage, 
                  fgApIMReqProcessed, fgApSIPUpTime, fgApSIPMemUsage, 
                  fgApSIPClientReg, fgApSIPCallHandling, fgApSIPServices, 
                  fgApSIPOtherReq, fgAppSuNumber, fgAppSuFileScanned, 
                  fgAppVoIPConn, fgAppVoIPCallBlocked, 
                  fgAppP2PConnBlocked, fgAppP2PProtEntBytes, 
                  fgAppP2PProtoEntLastReset, fgAppIMMessages, 
                  fgAppIMFileTransfered, fgAppIMFileTxBlocked, 
                  fgAppIMConnBlocked, fgAppFnbamStatsTotalAuthReqs,
                  fgAppFnbamStatsTotalEagainErrs, fgAppFnbamStatsTotalLdapFails, 
                  fgApFTPReqProcessed, fgApHTTPConnections, fgApFTPConnections,
                  fgApSMTPConnections, fgApPOP3Connections, fgApIMAPConnections,
                  fgApNNTPConnections, fgApHTTPMaxConnections,
                  fgApFTPMaxConnections, fgApSMTPMaxConnections,
                  fgApPOP3MaxConnections, fgApIMAPMaxConnections,
                  fgApNNTPMaxConnections }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to application proxy and filtering services on FortiGate devices."
    ::= { fgMibConformance 10 }

fgAntivirusObjectGroup OBJECT-GROUP
    OBJECTS     { fgAvVirusDetected, fgAvVirusBlocked, 
                  fgAvHTTPVirusDetected, fgAvHTTPVirusBlocked, 
                  fgAvSMTPVirusDetected, fgAvSMTPVirusBlocked, 
                  fgAvPOP3VirusDetected, fgAvPOP3VirusBlocked, 
                  fgAvIMAPVirusDetected, fgAvIMAPVirusBlocked, 
                  fgAvFTPVirusDetected, fgAvFTPVirusBlocked, 
                  fgAvIMVirusDetected, fgAvIMVirusBlocked, 
                  fgAvNNTPVirusDetected, fgAvNNTPVirusBlocked, 
                  fgAvOversizedDetected, fgAvOversizedBlocked, 
                  fgAvMAPIVirusDetected, fgAvMAPIVirusBlocked, 
                  fgAvSMBVirusDetected, fgAvSMBVirusBlocked,
                  fgAvTrapVirName }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Antivirus services on FortiGate devices."
    ::= { fgMibConformance 11 }

fgIntrusionPrevtObjectGroup OBJECT-GROUP
    OBJECTS     { fgIpsTrapSigId, fgIpsTrapSrcIp, fgIpsTrapSigMsg, 
                  fgIpsIntrusionsDetected, fgIpsIntrusionsBlocked, 
                  fgIpsCritSevDetections, fgIpsHighSevDetections, 
                  fgIpsMedSevDetections, fgIpsLowSevDetections, 
                  fgIpsInfoSevDetections, fgIpsSignatureDetections, 
                  fgIpsAnomalyDetections }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Intrusion Detection and Prevention services on FortiGate devices."
    ::= { fgMibConformance 12 }

fgWebFilterObjectGroup OBJECT-GROUP
    OBJECTS     { fgWfHTTPBlocked, fgWfHTTPSBlocked, fgWfHTTPURLBlocked, 
                  fgWfHTTPSURLBlocked, fgWfActiveXBlocked, 
                  fgWfCookieBlocked, fgWfAppletBlocked, 
                  fgFgWfHTTPExamined, fgFgWfHTTPSExamined, 
                  fgFgWfHTTPAllowed, fgFgWfHTTPSAllowed, 
                  fgFgWfHTTPBlocked, fgFgWfHTTPSBlocked, 
                  fgFgWfHTTPLogged, fgFgWfHTTPSLogged, 
                  fgFgWfHTTPOverridden, fgFgWfHTTPSOverridden }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to FortiGate and FortiGuard based Web Filtering services on FortiGate devices."
    ::= { fgMibConformance 13 }

fgVirtualDomainObjectGroup OBJECT-GROUP
    OBJECTS     { fgVdNumber, fgVdMaxVdoms, fgVdEnabled, fgVdEntName, fgVdEntIndex, 
                  fgVdEntOpMode, fgVdTpMgmtAddrType, fgVdTpMgmtAddr,
                  fgVdEntCpuUsage, fgVdEntMemUsage, fgVdEntSesCount, fgVdEntSesRate,
                  fgVdTpMgmtMask, fgVdEntHaState, fgVdEntChecksum }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Virtual Firewall Domain services on FortiGate devices."
    ::= { fgMibConformance 14 }

fgAdministrationObjectGroup OBJECT-GROUP
    OBJECTS     { fgAdminVdom }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to the administration of FortiGate device."
    ::= { fgMibConformance 15 }

fgIntfObjectGroup OBJECT-GROUP
    OBJECTS     { fgIntfEntVdom, fgIntfEntEstUpBandwidth, fgIntfEntEstDownBandwidth,
                  fgIntfEntMeaUpBandwidth, fgIntfEntMeaDownBandwidth,
                  fgIntfVlanName, fgIntfVlanID, fgIntfVlanPhyName,
                  fgIntfVrrpCount, fgIntfVrrpEntVrId, fgIntfVrrpEntGrpId,
                  fgIntfVrrpEntIfName, fgIntfVrrpEntState, fgIntfVrrpEntVrIp,
                  fgIntfVlanHbCount, fgIntfVlanHbEntIfName, fgIntfVlanHbEntSerial, fgIntfVlanHbEntState,
                  fgIntfBcCfgIfName, fgIntfBcCfgIfEgressSProfile, fgIntfBcCfgIfIngressSProfile,
                  fgIntfBcCfgIfEstUpBandwidth, fgIntfBcCfgIfEstDownBandwidth,
                  fgIntfBcCfgIfInBandwidth, fgIntfBcCfgIfOutBandwidth,
                  fgIntfBcCfgSproName, fgIntfBcCfgSproType, fgIntfBcCfgSproDefaultClassId, fgIntfBcCfgSproClassNum,
                  fgIntfBcCfgSentClassName, fgIntfBcCfgSentGuaranteedBandwidth, fgIntfBcCfgSentMaxBandwidth,
                  fgIntfBcCfgSpolSrcaddr, fgIntfBcCfgSpolDstaddr, fgIntfBcCfgSpolSvr,
                  fgIntfBcCfgSpolComment, fgIntfBcCfgSpolClassName,
                  fgIntfBcAllocatedBandwidth, fgIntfBcGuaranteedBandwidth, fgIntfBcMaxBandwidth,
                  fgIntfBcCurrentBandwidth, fgIntfBcBytes, fgIntfBcDrops,
                  fgIntfBcInAllocatedBandwidth, fgIntfBcInGuaranteedBandwidth, fgIntfBcInMaxBandwidth,
                  fgIntfBcInCurrentBandwidth, fgIntfBcInBytes, fgIntfBcInDrops,
                  fgIntfBcQPackets, fgIntfBcQBytes, fgIntfBcQPDrops, fgIntfBcQBDrops }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to the interface table of FortiGate device."
    ::= { fgMibConformance 16 }

fgProcessorsObjectGroup OBJECT-GROUP
    OBJECTS     { fgProcessorCount, fgProcessorUsage, fgProcessorUsage5sec,
                fgProcessorType, fgProcessorContainedIn, fgProcessorPktRxCount,
                fgProcessorPktTxCount, fgProcessorPktDroppedCount,
                fgProcessorUserUsage, fgProcessorSysUsage, fgProcessorPktTxDroppedCount,
                fgProcessorModuleCount, fgProcModType,
                fgProcModName, fgProcModDescr, fgProcModProcessorCount,
                fgProcModMemCapacity, fgProcModMemUsage, fgPerCpuHighDetails,
                fgProcModSessionCount, fgProcModSACount }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to the processors table of FortiGate device."
    ::= { fgMibConformance 17 }

fgNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS { fgTrapVpnTunUp, fgTrapVpnTunDown, fgTrapHaSwitch, 
                  fgTrapHaHBFail, fgTrapHaMemberDown, fgTrapHaMemberUp, 
                  fgTrapIpsSignature, fgTrapIpsAnomaly, 
                  fgTrapIpsPkgUpdate, fgTrapIpsFailOpen,
                  fgTrapAvVirus, fgTrapAvOversize,
                  fgTrapAvPattern, fgTrapAvFragmented, 
                  fgTrapAvEnterConserve, fgTrapAvBypass, 
                  fgTrapAvOversizePass, fgTrapAvOversizeBlock, 
                  fgTrapFazDisconnect, fgTrapFaz, fgTrapWcApUp, fgTrapWcApDown, fgTrapDeviceNew, 
                  fgTrapPerCpuHigh, fgTrapDhcp, fgTrapPoolUsage,
                  fgTrapFcSwUp, fgTrapFcSwDown, fgTrapServerLoadBalanceRealServerDown,
                  fgTrapSlbc }
    STATUS      current
    DESCRIPTION 
        "Notifications that can be generated from a FortiGate device."
    ::= { fgMibConformance 18 }

fgObsoleteNotificationsGroup NOTIFICATION-GROUP
    NOTIFICATIONS { fgTrapHaStateChange }
    STATUS      deprecated
    DESCRIPTION 
        "Notifications that have been deprecated, but may still be generated by older models."
    ::= { fgMibConformance 19 }

fgExplicitProxyObjectGroup OBJECT-GROUP
    OBJECTS     { fgExplicitProxyUpTime, fgExplicitProxyMemUsage, fgExplicitProxyRequests,
                fgExplicitProxyUsers, fgExplicitProxySessions,
                fgExplicitProxyVirus, fgExplicitProxyBannedWords, fgExplicitProxyPolicy,
                fgExplicitProxyOversized, fgExplicitProxyArchNest, fgExplicitProxyArchSize,
                fgExplicitProxyArchEncrypted, fgExplicitProxyArchMultiPart,
                fgExplicitProxyArchUnsupported, fgExplicitProxyArchBomb, fgExplicitProxyArchCorrupt,
                fgExplicitProxyFilteredApplets, fgExplicitProxyFilteredActiveX,
                fgExplicitProxyFilteredJScript, fgExplicitProxyFilteredJS,
                fgExplicitProxyFilteredVBS, fgExplicitProxyFilteredOthScript,
                fgExplicitProxyBlockedDLP, fgExplicitProxyBlockedConType,
                fgExplicitProxyExaminedURLs, fgExplicitProxyAllowedURLs, fgExplicitProxyBlockedURLs,
                fgExplicitProxyLoggedURLs, fgExplicitProxyOverriddenURLs }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to explicit proxy"
    ::= { fgMibConformance 20 }

fgWebCacheObjectGroup OBJECT-GROUP
    OBJECTS     { fgWebCacheUpTime, 
                fgWebCacheRAMLimit, fgWebCacheRAMUsage, fgWebCacheRAMHits,
                fgWebCacheRAMMisses, fgWebCacheRequests, fgWebCacheBypass,
                fgWebCacheDiskLimit, fgWebCacheDiskUsage, fgWebCacheDiskHits,
                fgWebCacheDiskMisses, fgWebCacheDiskFailure }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to explicit proxy, web cache and wan optimization."
    ::= { fgMibConformance 21 }

fgWanOptObjectGroup OBJECT-GROUP
    OBJECTS     { fgMemCacheLimit, fgMemCacheUsage, fgMemCacheHits, fgMemCacheMisses,
                fgByteCacheRAMLimit, fgByteCacheRAMUsage,
                fgWanOptUpTime, fgWanOptReductionRate, fgWanOptLanTraffic, fgWanOptWanTraffic,
                fgWanOptLanInTraffic, fgWanOptLanOutTraffic, fgWanOptWanInTraffic, fgWanOptWanOutTraffic,
                fgWanOptTunnels, fgWanOptLANBytesIn, fgWanOptLANBytesOut,
                fgWanOptWANBytesIn, fgWanOptWANBytesOut, fgWanOptDiskLimit, fgWanOptDiskUsage,
                fgWanOptDiskHits, fgWanOptDiskMisses, fgWanOptDiskFailure }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to explicit proxy, web cache and wan optimization."
    ::= { fgMibConformance 22 }

fgObsoleteAppServicesObjectGroup OBJECT-GROUP
    OBJECTS     { fgApHTTPUpTime, fgApHTTPMemUsage,
                  fgApSMTPUpTime, fgApSMTPMemUsage,
                  fgApPOP3UpTime, fgApPOP3MemUsage, 
                  fgApIMAPUpTime, fgApIMAPMemUsage,
                  fgApNNTPUpTime, fgApNNTPMemUsage,
                  fgApFTPUpTime, fgApFTPMemUsage }
    STATUS      deprecated
    DESCRIPTION 
        "Objects that have been deprecated, but may still be generated by older models."
    ::= { fgMibConformance 23 }

fgSystemAdvancedObjectGroup OBJECT-GROUP
    OBJECTS     { fgSIAdvMemPageCache, fgSIAdvMemCacheActive,
                  fgSIAdvMemCacheInactive, fgSIAdvMemBuffer,
                  fgSIAdvMemEnterKerConsThrsh, 
                  fgSIAdvMemLeaveKerConsThrsh,
                  fgSIAdvMemEnterProxyConsThrsh,
                  fgSIAdvMemLeaveProxyConsThrsh,
                  fgSIAdvSesEphemeralCount, fgSIAdvSesEphemeralLimit,
                  fgSIAdvSesClashCount, fgSIAdvSesExpCount,
                  fgSIAdvSesSyncQFCount, fgSIAdvSesAcceptQFCount,
                  fgSIAdvSesNoListenerCount,
                  fgLicContractCount, fgLicVersionCount,
                  fgLicContractDesc, fgLicContractExpiry,
                  fgLicVersionDesc, fgLicVersionExpiry,
                  fgLicVersionNumber, fgLicVersionUpdTime,
                  fgLicVersionUpdMethod, fgLicVersionTryTime,
                  fgLicVersionTryResult
                }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to the system advanced status of the device."
    ::= { fgMibConformance 24 }

fgWcObjectGroup OBJECT-GROUP
    OBJECTS     { fgWcApVdom, fgWcApSerial, fgWcApName,
                  fgWcInfoName, fgWcInfoLocation, fgWcInfoWtpCapacity, fgWcInfoWtpManaged,
                  fgWcInfoWtpSessions, fgWcInfoStationCapacity, fgWcInfoStationCount,
                  fgWcWlanSsid, fgWcWlanBroadcastSsid, fgWcWlanSecurity, fgWcWlanEncryption,
                  fgWcWlanAuthentication, fgWcWlanRadiusServer, fgWcWlanUserGroup,
                  fgWcWlanLocalBridging, fgWcWlanVlanId, fgWcWlanMeshBackhaul,
                  fgWcWlanStationCapacity, fgWcWlanStationCount,
                  fgWcWtpProfilePlatform, fgWcWtpProfileDataChannelDtlsPolicy,
                  fgWcWtpProfileCountryString,
                  fgWcWtpProfileRadioMode, fgWcWtpProfileRadioApScan, fgWcWtpProfileRadioWidsProfile,
                  fgWcWtpProfileRadioDarrp, fgWcWtpProfileRadioFrequencyHandoff,
                  fgWcWtpProfileRadioApHandoff, fgWcWtpProfileRadioBeaconInterval,
                  fgWcWtpProfileRadioDtimPeriod, fgWcWtpProfileRadioBand,
                  fgWcWtpProfileRadioChannelBonding, fgWcWtpProfileRadioChannel,
                  fgWcWtpProfileRadioAutoTxPowerControl, fgWcWtpProfileRadioAutoTxPowerLow,
                  fgWcWtpProfileRadioAutoTxPowerHigh, fgWcWtpProfileRadioTxPowerLevel,
                  fgWcWtpProfileRadioVaps, fgWcWtpProfileRadioStationCapacity, fgWcWtpProfileRadioChannelWidth,
                  fgWcWtpConfigWtpAdmin, fgWcWtpConfigWtpName, fgWcWtpConfigWtpLocation,
                  fgWcWtpConfigWtpProfile, fgWcWtpConfigRadioEnable, fgWcWtpConfigRadioAutoTxPowerControl,
                  fgWcWtpConfigRadioAutoTxPowerLow, fgWcWtpConfigRadioAutoTxPowerHigh,
                  fgWcWtpConfigRadioTxPowerLevel, fgWcWtpConfigRadioBand, fgWcWtpConfigRadioApScan,
                  fgWcWtpConfigVapAll, fgWcWtpConfigVaps,
                  fgWcWtpSessionWtpIpAddressType, fgWcWtpSessionWtpIpAddress, fgWcWtpSessionWtpLocalIpAddressType,
                  fgWcWtpSessionWtpLocalIpAddress, fgWcWtpSessionWtpBaseMacAddress,
                  fgWcWtpSessionConnectionState, fgWcWtpSessionWtpUpTime,
                  fgWcWtpSessionWtpDaemonUpTime, fgWcWtpSessionWtpSessionUpTime,
                  fgWcWtpSessionWtpProfileName, fgWcWtpSessionWtpModelNumber,
                  fgWcWtpSessionWtpHwVersion, fgWcWtpSessionWtpSwVersion,
                  fgWcWtpSessionWtpBootVersion, fgWcWtpSessionWtpRegionCode,
                  fgWcWtpSessionWtpStationCount, fgWcWtpSessionWtpByteRxCount,
                  fgWcWtpSessionWtpByteTxCount, fgWcWtpSessionWtpCpuUsage,
                  fgWcWtpSessionWtpMemoryUsage, fgWcWtpSessionWtpMemoryCapacity,
                  fgWcWtpSessionRadioMode, fgWcWtpSessionRadioBaseBssid,
                  fgWcWtpSessionRadioCountryString, fgWcWtpSessionRadioCountryCode,
                  fgWcWtpSessionRadioOperatingChannel, fgWcWtpSessionRadioOperatingPower,
                  fgWcWtpSessionRadioStationCount,
                  fgWcWtpSessionVapSsid, fgWcWtpSessionVapStationCount,
                  fgWcWtpSessionVapByteRxCount, fgWcWtpSessionVapByteTxCount,
                  fgWcStaWlan, fgWcStaWtpId, fgWcStaRadioId, fgWcStaVlanId, fgWcStaIpAddressType, fgWcStaIpAddress,
                  fgWcStaVci, fgWcStaHost, fgWcStaUser, fgWcStaGroup, fgWcStaSignal, fgWcStaNoise,
                  fgWcStaIdle, fgWcStaBandwidthTx, fgWcStaBandwidthRx, fgWcStaChannel,
                  fgWcStaRadioType, fgWcStaSecurity, fgWcStaEncrypt, fgWcStaOnline
                }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to wireless controller."
    ::= { fgMibConformance 25 }

fgFcObjectGroup OBJECT-GROUP
    OBJECTS     { fgFcSwVdom, fgFcSwSerial, fgFcSwName }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to switch controller."
    ::= { fgMibConformance 26 }

fgServerLoadBalanceObjectGroup OBJECT-GROUP
    OBJECTS     { fgServerLoadBalanceRealServerAddress, fgServerLoadBalanceVirtualServerName, fgServerLoadBalanceRealServerAddress6 }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Server Load Balance group."
    ::= { fgMibConformance 27 }

fgUsbportsObjectGroup OBJECT-GROUP
    OBJECTS     { fgUsbportCount, fgUsbportPlugged, fgUsbportVersion,
                  fgUsbportClass, fgUsbportVendId, fgUsbportProdId, fgUsbportRevision,
                  fgUsbportManufacturer, fgUsbportProduct, fgUsbportSerial
                }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to USB device group."
    ::= { fgMibConformance 28 }

fgUsbModemInfoGroup OBJECT-GROUP
    OBJECTS     { fgUsbModemSignalStrength, fgUsbModemStatus, fgUsbModemSimState,
               fgUsbModemVendor, fgUsbModemProduct, fgUsbModemNetwork, fgUsbModemId, fgUsbModemSimId }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to USB Modem Info group."
    ::= { fgMibConformance 29 }

fgDeviceObjectGroup OBJECT-GROUP
    OBJECTS     { fgDeviceMacAddress, fgDeviceCreated, fgDeviceLastSeen }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Device group."
    ::= { fgMibConformance 30 }

fgLinkMonitorGroup OBJECT-GROUP
    OBJECTS     { fgLinkMonitorNumber, fgLinkMonitorName, fgLinkMonitorState,
                  fgLinkMonitorLatency, fgLinkMonitorJitter, fgLinkMonitorPacketSend, fgLinkMonitorPacketRecv,
                  fgLinkMonitorPacketLoss, fgLinkMonitorVdom,
                  fgLinkMonitorBandwidthIn, fgLinkMonitorBandwidthOut, fgLinkMonitorBandwidthBi,
                  fgLinkMonitorOutofSeq, fgLinkMonitorServer, fgLinkMonitorProtocol
                }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Link Monitor group."
    ::= { fgMibConformance 31 }

fgInternalModemInfoGroup OBJECT-GROUP
    OBJECTS     { fgMdmDetected, fgMdmVendor,
               fgMdmModel, fgMdmRevision, fgMdmMsisdn, fgMdmEsn, fgMdmImei,
	       fgMdmHwRevision, fgMdmMeid, fgMdmSwRev, fgMdmSku, fgMdmFsn, fgMdmPrlVer,
	       fgMdmFwVer, fgMdmPriFwVer, fgMdmCarrierAbbr, fgMdmActState, fgMdmOpMode,
           fgMdmLacTac, fgMdmActBand, fgMdmCellId, fgMdmRssi}
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Internal Modem Info group."
    ::= { fgMibConformance 32 }

fgInternalModemSIMInfoGroup OBJECT-GROUP
    OBJECTS     { fgSimMdmEntIndex, fgSimState,
               fgSimIccid, fgSimImsi, fgSimCountry, fgSimNetwork}
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Internal Modem SIM card Info group."
    ::= { fgMibConformance 33 }

fgInternalModemSigInfoGroup OBJECT-GROUP
    OBJECTS     { fgCdmaRssi, fgCdmaEcio,
               fgHdrRssi, fgHdrEcio, fgHdrSinr, fgHdrIo,
	       fgGsm, fgWcdmaRssi, fgWcdmaEcio, fgLteRssi,
	       fgLteRsrq, fgLteRsrp, fgLteSnr, fgTdma}
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Internal Modem Signal Info group."
    ::= { fgMibConformance 34 }

fgInternalModemTrafficInfoGroup OBJECT-GROUP
    OBJECTS     { fgTxPacksOK, fgRxPacksOK,
               fgTxPacksErr, fgRxPacksErr, fgTxPacksOverflow, fgRxPacksOverflow,
	       fgTxBytesOK, fgRxBytesOK, fgLastCallTxBytesOK, fgLastCallRxBytesOK,
	       fgTxPacksDrop, fgRxPacksDrop}
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Internal Modem Data Traffic Info group."
    ::= { fgMibConformance 35 }

fgInternalModemSessInfoGroup OBJECT-GROUP
    OBJECTS     { fgSessMdmEntIndex, fdLteIfName,
               fdLteSessConnStat, fdLteProfId, fdLteProfName, fdLteProfType,
	       fdLtePdpType, fdLteProfApn, fdLteProfIpFamily, fdLteIpv4Addr,
	       fdLteIpv4GwAddr, fdLteIpv4NetMask, fdLteIpv4PriDns, fdLteIpv4SecDns,
	       fdLteIpv6Addr, fdLteIpv6PrefLen, fdLteIpv6GwAddr, fdLteIpv6GwPrefLen,
	       fdLteIpv6PriDns, fdLteIpv6SecDns, fdLteMtu, fdLteAutoConn,
	       fdLteNetType, fdLteNetTypeLas, fdLteLinkProto}
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Internal Modem Data Traffic Info group."
    ::= { fgMibConformance 36 }

fgInternalModemGpsInfoGroup OBJECT-GROUP
    OBJECTS     { fgGpsEnabled, fgLatitude,
               fgLongitude, fgUtcTime, fgLocalTime}
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Internal Modem GPS Info group."
    ::= { fgMibConformance 37 }

fgInternalModemDatausageInfoGroup OBJECT-GROUP
    OBJECTS     { fgDatausageEnabled, fgDataOut, fgDataIn}
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Internal Modem data usage Info group."
    ::= { fgMibConformance 38 }

fgVWLHealthCheckLinkGroup OBJECT-GROUP
    OBJECTS     { fgVWLHealthCheckLinkNumber, fgVWLHealthCheckLinkName, fgVWLHealthCheckLinkSeq,
                  fgVWLHealthCheckLinkState, fgVWLHealthCheckLinkLatency, fgVWLHealthCheckLinkJitter,
                  fgVWLHealthCheckLinkPacketSend, fgVWLHealthCheckLinkPacketRecv, fgVWLHealthCheckLinkPacketLoss,
                  fgVWLHealthCheckLinkVdom, fgVWLHealthCheckLinkBandwidthIn, fgVWLHealthCheckLinkBandwidthOut,
                  fgVWLHealthCheckLinkBandwidthBi, fgVWLHealthCheckLinkIfName, fgVWLHealthCheckLinkUsedBandwidthIn,
                  fgVWLHealthCheckLinkUsedBandwidthOut, fgVWLHealthCheckLinkUsedBandwidthBi, fgVWLHealthCheckLinkMOSCodec,
                  fgVWLHealthCheckLinkMOS
                }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to VWL health check group."
    ::= { fgMibConformance 39 }

fgDisksObjectGroup OBJECT-GROUP
    OBJECTS     { fgDiskCount, fgDiskName }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to disks name list."
    ::= { fgMibConformance 40 }

fgNPUGroup OBJECT-GROUP
    OBJECTS     { fgNPUNumber, fgNPUName, fgNPUDrvDriftSum, fgNPUSessionTblSize,
                       fgNPUSessionCount, fgNPUDrvDrift }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to NPU on FortiGate devices."
    ::= { fgMibConformance 41 }

fgSlaProbeClientGroup OBJECT-GROUP
    OBJECTS     { fgSlaProbeClientNumber, fgSlaProbeClientIP, fgSlaProbeClientState,
                  fgSlaProbeClientAvgLatency, fgSlaProbeClientAvgLatencySD,
                  fgSlaProbeClientAvgLatencyDS, fgSlaProbeClientMinLatency,
                  fgSlaProbeClientMinLatencySD, fgSlaProbeClientMinLatencyDS,
                  fgSlaProbeClientMaxLatency, fgSlaProbeClientMaxLatencySD,
                  fgSlaProbeClientMaxLatencyDS, fgSlaProbeClientAvgJitter,
                  fgSlaProbeClientAvgJitterSD, fgSlaProbeClientAvgJitterDS,
                  fgSlaProbeClientMinJitter, fgSlaProbeClientMinJitterSD,
                  fgSlaProbeClientMinJitterDS, fgSlaProbeClientMaxJitter,
                  fgSlaProbeClientMaxJitterSD, fgSlaProbeClientMaxJitterDS,
                  fgSlaProbeClientPktloss, fgSlaProbeClientPktlossSD,
                  fgSlaProbeClientPktlossDS, fgSlaProbeClientOutofSeq,
                  fgSlaProbeClientOutofSeqSD, fgSlaProbeClientOutofSeqDS
                }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to SLA Probe client group."
    ::= { fgMibConformance 42 }

fgDNSProxyObjectGroup OBJECT-GROUP
    OBJECTS     { fgDNSProxyStatsUdpCacheHit, fgDNSProxyStatsUdpRatingCacheHit, fgDNSProxyStatsUdpReq,
                  fgDNSProxyStatsUdpRes, fgDNSProxyStatsUdpFwd, fgDNSProxyStatsUdpRetrans, fgDNSProxyStatsUdpTo,
                  fgDNSProxyStatsUdpFtgRes, fgDNSProxyStatsUdpFtgFwd, fgDNSProxyStatsUdpFtgRetrans }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to DNS Proxy group."
    ::= { fgMibConformance 43 }

fgLogGroup OBJECT-GROUP
    OBJECTS     { fgLogDeviceNumber, fgLogDeviceName, fgLogDeviceSentCount, fgLogDeviceRelayedCount,
	              fgLogDeviceCachedCount, fgLogDeviceFailedCount, fgLogDeviceDroppedCount }
	STATUS      current
	DESCRIPTION
	    "Objects pertaining to log group."
	::= { fgMibConformance 44 }

fgConfigGroup OBJECT-GROUP
    OBJECTS     { fgConfigSerial, fgConfigChecksum, fgConfigLastChangeTime }
	STATUS      current
	DESCRIPTION
	    "Objects pertaining to log group."
	::= { fgMibConformance 45 }


fgDhcpObjectGroup OBJECT-GROUP
    OBJECTS     { fgDhcpServerNumber, fgDhcpLeaseUsage, 
                  fgDhcpServerId, fgDhcpTrapType, fgDhcpTrapMessage
                }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to DHCP services on FortiGate devices."
    ::= { fgMibConformance 46 }

fgDpdkEngsObjectGroup OBJECT-GROUP
    OBJECTS     { fgDpdkEngCount, fgDpdkEngRxUsage, fgDpdkEngVnpUsage,
                fgDpdkEngIpsUsage, fgDpdkEngTxUsage, fgDpdkEngIdle, fgDpdkEngToCpu}
    STATUS      current
    DESCRIPTION
        "Objects pertaining to the Dpdk Engine table of FortiGate device."
    ::= { fgMibConformance 47 }

fgSwitchDeviceObjectGroup OBJECT-GROUP
    OBJECTS     { fgSwDeviceSerialNum, fgSwDeviceName, fgSwDeviceVersion, fgSwDeviceAuthorized,
                  fgSwDeviceStatus, fgSwDeviceJoinTime, fgSwDeviceIp, fgSwDeviceFlag,
                  fgSwCpu, fgSwMemory, fgSwDeviceIpv6
                }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to Switch Devices on FortiGate devices."
    ::= { fgMibConformance 48 }

fgSwitchPortObjectGroup OBJECT-GROUP
    OBJECTS     { fgSwPortSwitchSerialNum, fgSwPortName, fgSwPortStatus, fgSwPortSpeedDuplex, fgSwPortNativeVlan, 
                  fgSwPortAllowedVlan, fgSwPortUntaggedVlan, fgSwPortPOE, fgSwPortPOEStatus, fgSwPortPOEState, fgSwPortPOEPower
                }
    STATUS      current
    DESCRIPTION
        "Objects pertaining to Switch Ports on FortiGate devices."
    ::= { fgMibConformance 49 }

fgChassisObjectGroup OBJECT-GROUP
    OBJECTS     { fgChassisVersion, fgChassisSlotId, fgChassisTrapMessage }
    STATUS      current
    DESCRIPTION 
        "Objects pertaining to Chassis group."
    ::= { fgMibConformance 50 }

fgServiceGroupWorkerBladesGroup OBJECT-GROUP
    OBJECTS { fgSgWbServiceGroupID, fgSgWbChassisID, fgSgWbSlotID, fgSgWbState,
              fgSgWbStatusMsg, fgSgWbMaster, fgSgWbSysVersion, fgSgWbSysSerial,
              fgSgWbSysUpTime, fgSgWbSysCpuUsage, fgSgWbSysMemUsage, fgSgWbBaseLink,
              fgSgWbFabricLink, fgSgWbDataHb, fgSgWbMgmtHb
            }
    STATUS current
    DESCRIPTION
        "Objects pertaining to Service Group Worker Blades group."
    ::= {fgMibConformance 51 }

fgMIBCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION 
        "The compliance statement for the application MIB."

    MODULE      -- this module

        GROUP   fgFmTrapGroup
        DESCRIPTION   
              "This group is mandatory for any FortiGate device being managed by a FortiManager."

        GROUP   fgNotificationGroup
        DESCRIPTION   
              "Notifications are feature dependant. See manual for feature 
               information."

        GROUP   fgFmTrapObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgAdminObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgSystemObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgSoftwareObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgHwSensorsObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgHighAvailabilityObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgVpnObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgFirewallObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgAppServicesObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgAntivirusObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgIntrusionPrevtObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgWebFilterObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgVirtualDomainObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgAdministrationObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgIntfObjectGroup
        DESCRIPTION   
              "Model and feature specific."

        GROUP   fgProcessorsObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgExplicitProxyObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgWebCacheObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgWanOptObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgSystemAdvancedObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgWcObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgFcObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgServerLoadBalanceObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgUsbportsObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgUsbModemInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgDeviceObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgLinkMonitorGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgInternalModemInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgInternalModemSIMInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgInternalModemSigInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgInternalModemTrafficInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgInternalModemSessInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgInternalModemGpsInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgInternalModemDatausageInfoGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgVWLHealthCheckLinkGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgDisksObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgNPUGroup
        DESCRIPTION
              "Model and feature specific."

	GROUP   fgSlaProbeClientGroup
        DESCRIPTION
	      "Model and feature specific."

        GROUP   fgDNSProxyObjectGroup
        DESCRIPTION
              "Model and feature specific."

	GROUP   fgLogGroup
	DESCRIPTION
	      "Model and feature specific."

	GROUP   fgConfigGroup
	DESCRIPTION
	      "Model and feature specific."

        GROUP   fgDhcpObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgDpdkEngsObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgSwitchDeviceObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgSwitchPortObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP   fgChassisObjectGroup
        DESCRIPTION
              "Model and feature specific."

        GROUP  fgServiceGroupWorkerBladesGroup 
        DESCRIPTION
              "Model and feature specific."

    ::= { fgMibConformance 100 }

fg300MibCompliance MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION 
        "The backwards compatibility compliance statement for the application MIB."

    MODULE      -- this module

        GROUP   fgObsoleteNotificationsGroup
        DESCRIPTION   
              "Model and feature specific. May be implemented by some firmwares, but should not be relied on."

    ::= { fgMibConformance 101 }

fgObsolteMIBCompliance MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION 
        "The compliance statement for the application MIB."

    MODULE      -- this module

        GROUP   fgObsoleteAppServicesObjectGroup 
        DESCRIPTION   
              "Deprecated AppService objects."

    ::= { fgMibConformance 102 }

END -- end of module FORTINET-FORTIGATE-MIB.
