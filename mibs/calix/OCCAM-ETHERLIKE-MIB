--This MIB has been extrcted from RFC 2665
OCCAM-ETHERLIKE-MIB	DEFINITIONS ::= BEGIN
	IMPORTS
        occamGenericEtherlikeModules, occamGenericInterfaceModules
			FROM OCCAM-REG-MODULE
		MODULE-COMPLIANC<PERSON>, OBJECT-GROUP
			FROM SNMPv2-CONF
		ifIndex, InterfaceIndex
			FROM IF-MIB
		MODULE-IDENTITY, OBJECT-TYPE, OBJECT-IDENTITY, Counter32, 
                mib-2, transmission, enterprises
			FROM SNMPv2-SMI;

	etherMIB	MODULE-IDENTITY
		LAST-UPDATED	"200104271051Z"
		ORGANIZATION	"Occam Networks"
		CONTACT-INFO    "email <EMAIL>"

		DESCRIPTION		
				"The MIB module to describe generic 
                                objects for Ethernet-like network 
                                interfaces.
				
				The following reference is used 
                                throughout this MIB module:
				
				[IEEE 802.3 Std] refers to:
				IEEE Std 802.3, 1998 Edition:'Information
				technology - Telecommunications and
				information exchange between systems -
				Local and metropolitan area networks -
				Specific requirements - Part 3: Carrier
				sense multiple access with collision
				detection (CSMA/CD) access method and
				physical layer specifications',
				September 1998.
				
				Of particular interest is Clause 30, 
                                '10Mb/s,100Mb/s and 1000Mb/s Management'."

                REVISION        "200104271051Z"
		DESCRIPTION	"MODULE-IDENTITY update."

                REVISION        "9908240400Z"
		DESCRIPTION	"Updated to include support for 1000 Mb/sec
				interfaces and full-duplex interfaces.
				This version published as RFC 2665."
		REVISION	"9806032150Z"
		DESCRIPTION		
				"Updated to include support for 100 Mb/sec
				interfaces.
				This version published as RFC 2358."
		REVISION	"9402030400Z"
		DESCRIPTION		
				"Initial version, published as RFC 1650."
		::=  { occamGenericEtherlikeModules 1 }

	org	OBJECT IDENTIFIER
		::=  {  iso  3  }

	dod	OBJECT IDENTIFIER
		::=  {  org  6  }

	internet	OBJECT IDENTIFIER
		::=  {  dod  1  }

	mgmt	OBJECT IDENTIFIER
		::=  {  internet  2  }


	etherMIBObjects	OBJECT IDENTIFIER
		::=  {  etherMIB  1  }

        dot3 OBJECT IDENTIFIER ::= { etherMIBObjects 7 }  


-- conformance information
-- placeholders

	etherConformance	OBJECT IDENTIFIER
		::=  {  etherMIB  2  }


-- the Ethernet-like Statistics group
	dot3StatsTable	OBJECT-TYPE
		SYNTAX		SEQUENCE  OF  Dot3StatsEntry
		MAX-ACCESS	not-accessible
		STATUS		current
		DESCRIPTION	
			"Statistics for a collection of ethernet-like
			interfaces attached to a particular system.
			There will be one row in this table for each
			ethernet-like interface in the system."
		::=  { dot3  2 }

	dot3StatsEntry	OBJECT-TYPE
		SYNTAX		Dot3StatsEntry
		MAX-ACCESS	not-accessible
		STATUS		current
		DESCRIPTION	
			"Statistics for a particular interface to an
			ethernet-like medium."
		INDEX		{  dot3StatsIndex  }
		::=  { dot3StatsTable 1 }

	Dot3StatsEntry  ::=  SEQUENCE {
		dot3StatsIndex  InterfaceIndex,
		dot3StatsSingleCollisionFrames  Counter32,
		dot3StatsFrameTooLongs  Counter32
		}


	dot3StatsIndex	OBJECT-TYPE	
		SYNTAX			InterfaceIndex
		MAX-ACCESS		read-only
		STATUS			current
		DESCRIPTION		
			"An index value that uniquely identifies
                        an interface to an ethernet-like medium.
                        The interface identified by a particular
                        value of this index is the same interface
                        as identified by the same value of 
                        ifIndex."
		REFERENCE	"RFC 2233, ifIndex"
		::=  {  dot3StatsEntry  1  }



--	dot3StatsAlignmentErrors	{  dot3StatsEntry  2  }
--	dot3StatsFCSErrors	{  dot3StatsEntry  3  }

	dot3StatsSingleCollisionFrames	OBJECT-TYPE	
		SYNTAX			Counter32
		MAX-ACCESS		read-only
		STATUS			current
		DESCRIPTION		
                        "A count of successfully transmitted frames on
                        a particular interface for which transmission
                        is inhibited by exactly one collision.
				
                        A frame that is counted by an instance of this
                        object is also counted by the corresponding
                        instance of either the ifOutUcastPkts,
                        ifOutMulticastPkts, or ifOutBroadcastPkts,
                        and is not counted by the corresponding
                        instance of the dot3StatsMultipleCollisionFrames
                        object.
				
			This counter does not increment when the
			interface is operating in full-duplex mode.
				
			Discontinuities in the value of this counter can
			occur at re-initialization of the management
			system, and at other times as indicated by the
			value of ifCounterDiscontinuityTime."
		REFERENCE		"[IEEE 802.3 Std.], ********.3,
                       aSingleCollisionFrames."
		::=  {  dot3StatsEntry  4  }



--	dot3StatsMultipleCollisionFrames {  dot3StatsEntry  5  }
--	dot3StatsSQETestErrors  {  dot3StatsEntry  6  }
--	dot3StatsDeferredTransmissions {  dot3StatsEntry  7  }
--	dot3StatsLateCollisions  {  dot3StatsEntry  8  }
--	dot3StatsExcessiveCollisions {  dot3StatsEntry  9  }
--	dot3StatsInternalMacTransmitErrors {  dot3StatsEntry  10  }
--	dot3StatsCarrierSenseErrors {  dot3StatsEntry  11  }
-- { dot3StatsEntry 12 } is not assigned

	dot3StatsFrameTooLongs	OBJECT-TYPE	
		SYNTAX			Counter32
		MAX-ACCESS		read-only
		STATUS			current
		DESCRIPTION		
			"A count of frames received on a particular
			interface that exceed the maximum permitted
			frame size.
				
			The count represented by an instance of this
			object is incremented when the frameTooLong
			status is returned by the MAC service to the
			LLC (or other MAC user). Received frames for
			which multiple error conditions obtain are,
			according to the conventions of IEEE 802.3
			Layer Management, counted exclusively according
			to the error status presented to the LLC.
				
			Discontinuities in the value of this counter can
			occur at re-initialization of the management
			system, and at other times as indicated by the
			value of ifCounterDiscontinuityTime."
		REFERENCE		"[IEEE 802.3 Std.], ********.25,
                       aFrameTooLongErrors."
		::=  {  dot3StatsEntry  13  }


-- { dot3StatsEntry 14 } is not assigned
-- { dot3StatsEntry 15 } is not assigned
--	dot3StatsInternalMacReceiveErrors {  dot3StatsEntry  16  }
--	dot3StatsEtherChipSet  {  dot3StatsEntry  17  }
--	dot3StatsSymbolErrors  {  dot3StatsEntry  18  }
--	dot3StatsDuplexStatus  {  dot3StatsEntry  19  }
--  802.3 Tests
--  serves as placeHolders

	dot3Tests	OBJECT IDENTIFIER
		::=  {  dot3  6  }

	dot3Errors	OBJECT IDENTIFIER
		::=  {  dot3  7  }

	etherGroups	OBJECT IDENTIFIER
		::=  {  etherConformance  1  }

	etherCompliances	OBJECT IDENTIFIER
		::=  {  etherConformance  2  }

END
