
-- *****************************************************************************
-- Juniper-RADIUS-CLIENT-MIB
--
-- Juniper Networks Enterprise MIB
--   RADIUS Client MIB
--
-- Copyright (c) 1999 Redstone Communications, Inc.
-- Copyright (c) 1999, 2002 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2008-2009 Juniper Networks, Inc.
--   All Rights Reserved.
-- *****************************************************************************

Juniper-RADIUS-CLIENT-MIB  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Counter32, Integer32, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ress,
    TimeTicks, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    DisplayString, RowStatus, TruthValue
        FROM SNMPv2-TC
    MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    juniMibs
        FROM Juniper-MIBs;

juniRadiusClientMIB  MODULE-IDENTITY
    LAST-UPDATED "200902261641Z"  -- 26-Feb-09 10:11 PM EST
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        Email:  <EMAIL>"
    DESCRIPTION
        "The Remote Authentication Dial In User Service (RADIUS) Client MIB for
        the Juniper enterprise."
    -- Revision History
    REVISION    "200902261641Z"  -- 26-Feb-09 10:11 PM EST  - JUNOSe 10.2
    DESCRIPTION
        "Modified the valid ranges of juniRadiusAuthClientCfgTimeoutInterval,
        juniRadiusAuthClientCfgRetries, juniRadiusAuthClientCfgDeadTime,
        juniRadiusAcctClientCfgTimeoutInterval, juniRadiusAcctClientCfgRetries,
        juniRadiusAcctClientCfgDeadTime. Modified the default values of
        juniRadiusAuthClientCfgDeadTime and juniRadiusAcctClientCfgDeadTime
        from 5 to 0.
        
        Added juniRadiusClientIncludeIpv6AccountingInAcctStop.
        
        Added juniRadiusClientIncludeDelegatedIpv6PrefixInAcctStart,
        juniRadiusClientIncludeDelegatedIpv6PrefixInAcctStop,
        juniRadiusClientIncludeFramedIpv6PoolInAcctStart,
        juniRadiusClientIncludeFramedIpv6PoolInAcctStop,
        juniRadiusClientIncludeFramedIpv6RouteInAcctStart,
        juniRadiusClientIncludeFramedIpv6RouteInAcctStop,
        juniRadiusClientIncludeIpv6LocalInterfaceInAcctStart,
        juniRadiusClientIncludeIpv6LocalInterfaceInAcctStop,
        juniRadiusClientIncludeIpv6NdRaPrefixInAcctStart,
        juniRadiusClientIncludeIpv6NdRaPrefixInAcctStop,
        juniRadiusClientIncludeIpv6PrimaryDnsInAcctStart,
        juniRadiusClientIncludeIpv6PrimaryDnsInAcctStop,
        juniRadiusClientIncludeIpv6SecondaryDnsInAcctStart,
        juniRadiusClientIncludeIpv6SecondaryDnsInAcctStop,
        juniRadiusClientIncludeIpv6VirtualRouterInAcctStart,
        juniRadiusClientIncludeIpv6VirtualRouterInAcctStop."
    REVISION    "200806181010Z"  -- 18-Jun-08 03:40 PM EST  - JUNOSe 9.3
    DESCRIPTION
        "Added juniRadiusClientIgnorePppoeMaxSession"
    REVISION    "200806110615Z"  -- 11-Jun-08 02:15 AM EDT  - JUNOSe 9.3
    DESCRIPTION
        "Modified juniRadiusClientCallingStationIdFormat of juniRadiusGeneralClient
         to include the SVLAN ID"    
    REVISION    "200712141500Z"  -- 14-Dec-07 10:00 AM EST  - JUNOSe 9.1
    DESCRIPTION
        "Added juniRadiusClientIncludeDownStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStop."    
    REVISION    "200709181822Z"  -- 18-Sep-07 02:22 PM EDT  - JUNOSe 8.2
    DESCRIPTION
        "Added juniRadiusClientIncludeInterfaceIdInAcctStart,
        juniRadiusClientIncludeIpv6PrefixInAcctStart,
        juniRadiusClientIncludeInterfaceIdInAcctStop,
        juniRadiusClientIncludeIpAddrInAcctStop,
        juniRadiusClientIncludeIpv6PrefixInAcctStop."
    REVISION    "200709162200Z"  -- 16-Sep-07 05:00 PM EST  - JUNOSe 8.1
    DESCRIPTION
        "Extended the valid range of juniRadiusAcctClientCfgMaxPendingRequests
         from 32000 to 96000."
    REVISION    "200704100103Z"  -- 09-Apr-07 09:03 PM EDT  - JUNOSe 8.1
    DESCRIPTION
	"Added juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cDslLineStateInAccessReq,
        juniRadiusClientIncludeL2cDslTypeInAccessReq,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cDslLineStateInAcctStart,
        juniRadiusClientIncludeL2cDslTypeInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cDslLineStateInAcctStop,
        juniRadiusClientIncludeL2cDslTypeInAcctStop allowing to control
        generation and format of decoded L2C Attributes."
    REVISION    "200602172200Z"  -- 17-Feb-06 05:00 PM EST  - JUNOSe 7.3
    DESCRIPTION
        "Added new objects BRAS group to allow inclusion of DSL Forum
        attributes into radius requests."
    REVISION    "200601122200Z"  -- 12-Jan-06 05:00 PM EST  - JUNOSe 7.2
    DESCRIPTION
        "Added new objects BRAS group to allow inclusion of L2C information,
        L2C up and down stream data into radius requests."
    REVISION    "200509301455Z"  -- 30-Sep-05 2:55 PM EST  - JUNOSe 7.1.1
    DESCRIPTION
        "Added new value to remote circuit id format types."
    REVISION    "200501141515Z"  -- 14-Jan-05 10:15 AM EST  - JUNOSe 7.0
    DESCRIPTION
        "Added new objects to the BRAS group to allow the widths of the fields 
    in the Nas-Port attribute (attribute number 5) to be configurable for atm
    and ethernet interfaces. Added new objects to control PPPoE Remote Circuit
    Id representation."      
    REVISION    "200412060232Z"  -- 05-Dec-04 09:32 PM EST  - JUNOSe 7.0
    DESCRIPTION
        "Added new objects BRAS group to allow inclusion of interface
        description into radius requests."
    REVISION    "200412032212Z"  -- 03-Dec-04 05:12 PM EST  - JUNOSe 6.1
    DESCRIPTION
        "Added a new object to the BRAS group to allow override of 
        nas-ip-address and nas-identifier from authentication router.
        Added new objects to the BRAS group to allow override of nas-port-id
        and calling-station-id with PPPoE Remote Circuit Id.
        Added new objects to the BRAS group to indicate which RADIUS attributes
        should be included or excluded from RADIUS packets. Added support
        for inclusion/exclusion of DHCP attributes."
    REVISION    "200409091945Z"  -- 09-Sep-04 03:45 PM EDT  - JUNOSe 5.3
    DESCRIPTION
        "Added new objects to the BRAS group to indicate which RADIUS
        attributes should be included or excluded from RADIUS packets
        (acct-multi-session-id, ascendNumInMultilink,
        profileServiceDescription, acctAuthentic, acctDelayTime,
        acctSessionId, nasIdentifier, eventTimestamp, mlpppBundleName
        and terminateCause). Added support to format nas-port, and
        connect-info attributes."
    REVISION    "200312151636Z"  -- 15-Dec-03 11:36 AM EST  - JUNOSe 5.2
    DESCRIPTION
        "Added new objects:
            juniRadiusAcctClientRejectRequests,
            juniRadiusAcctClientRejectResponses,
            juniRadiusClientVlanNasPortFormat."
    REVISION    "200303101933Z"  -- 10-Mar-03 02:33 PM EST  - JUNOSe 5.1
    DESCRIPTION
        "Added new objects:
            juniRadiusClientPppoeNasPortFormat,
            juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
            juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
            juniRadiusClientIncludeTunnelInterfaceIdInAcctStop."
    REVISION    "200301271833Z"  -- 27-Jan-03 01:33 PM EST  - JUNOSe 5.0
    DESCRIPTION
        "Replaced Unisphere names with Juniper names.
         Added objects to ignore attributes from the access-accept RADIUS
         packets.
         Added objects for RADIUS trap enable/disable control and detailed
         accounting statistics.
         Added notifications for available RADIUS servers."
    REVISION    "200211211945Z"  -- 21-Nov-02 02:45 PM EST  - JUNOSe 4.1
    DESCRIPTION
        "Added notifications for unavailable RADIUS servers."
    REVISION    "200205131754Z"  -- 13-May-02 01:54 PM EDT  - JUNOSe 4.0
    DESCRIPTION
        "Added objects (parameters) to indicate which RADIUS attributes should
         be included/excluded from RADIUS packets."
    REVISION    "200110161954Z"  -- 16-Oct-01 03:54 PM EDT  - JUNOSe 3.3
    DESCRIPTION
        "Added juniRadiusClientNasIpAddrUse."
    REVISION    "200109062108Z"  -- 06-Sep-01 05:08 PM EDT  - JUNOSe 3.2
    DESCRIPTION
        "Added juniRadiusClientRollover and
         juniRadiusClientCallingStationIdFormat."
    REVISION    "200103221520Z"  -- 22-Mar-01 10:20 AM EST  - JUNOSe 3.1
    DESCRIPTION
        "Added juniRadiusClientEthernetPortType,
         juniRadiusClientIncludeIpAddrInAcctStart, and
         juniRadiusClientIncludeAcctSessionIdInAccessReq."
    REVISION    "200012191640Z"  -- 19-Dec-00 11:40 AM EST  - JUNOSe 3.0
    DESCRIPTION
        "Added support for the RADIUS accounting backoff mechanism."
    REVISION    "200005051944Z"  --  5-May-00  3:44 PM EDT  - JUNOSe 2.0
    DESCRIPTION
        "Added support for client source address."
    REVISION      "9906010000Z"  --  1-Jun-99               - JUNOSe 1.1
    DESCRIPTION
        "Initial version of this MIB module, derived from IETF Internet Drafts
         of RADIUS Client MIBs for Authentication and Accounting."
    ::= { juniMibs 19 }

JuniRadiusClientRemoterCircuitIdFormatComponents  ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The set of configurable choices of PPPoE Remote Circtuit Id.  The
        maximum enumerated type will never be greater than 255, 'agentCircuitId'
        denotes the suboption 1 and 'remoteCircuitId' denotes the suboption 2
        of option 82 (RFC3046)."
    SYNTAX      INTEGER {
                    agentCircuitId(1),
                    agentRemoteId(2),
                    nasIdentifier(3),
                    dsl-format-1(4) }



-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Managed object groups
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniRadiusClientObjects     OBJECT IDENTIFIER ::= { juniRadiusClientMIB 1 }
juniRadiusGeneralClient     OBJECT IDENTIFIER ::= { juniRadiusClientObjects 1 }
juniRadiusAuthClient        OBJECT IDENTIFIER ::= { juniRadiusClientObjects 2 }
juniRadiusAcctClient        OBJECT IDENTIFIER ::= { juniRadiusClientObjects 3 }

-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Managed objects for RADIUS General
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniRadiusClientIdentifier OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The NAS-Identifier of the RADIUS client."
    ::= { juniRadiusGeneralClient 1 }

juniRadiusClientAlgorithm OBJECT-TYPE
    SYNTAX      INTEGER {
                    direct(0),
                    roundRobin(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The algorithm used by the client when multiple
        authentication/accounting servers are configured:
            direct      Use servers in order of precedence, each time beginning
                        with the highest precedence server and proceeding to
                        lower precedence servers if the the RADIUS request
                        fails, until the request succeeds or all servers have
                        been tried.
            roundRobin  Use servers in round-robin order, each time beginning
                        with the next round-robin-ordered server and proceeding
                        cyclically through servers if the RADIUS request fails,
                        until the request succeeds or all servers have been
                        tried."
    DEFVAL    { direct }
    ::= { juniRadiusGeneralClient 2 }

juniRadiusClientSourceAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source address used by the RADIUS client in requests to the RADIUS
        server.  The RADIUS server returns responses from this address.  Setting
        this object to 0.0.0.0 will reset the value to its default."
    DEFVAL    { 0 }
    ::= { juniRadiusGeneralClient 3 }

juniRadiusClientUdpChecksum OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the checksum calculations on RADIUS UDP packets."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 4 }

juniRadiusClientNasIdentifier OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The identifier used by the client for the value of NAS-Identifier
        attribute (number 32) in access and accounting requests.  The default is
        to use the system name."
    DEFVAL    { "" }
    ::= { juniRadiusGeneralClient 5 }

juniRadiusClientDslPortType OBJECT-TYPE
    SYNTAX      INTEGER {
                    virtual(5),
                    sdsl(11),
                    adsl-cap(12),
                    adsl-dmt(13),
                    idsl(14),
                    xdsl(16) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value to use in the NAS-Port-Type RADIUS Attribute (attribute
        number 61) for DSL interfaces in the RADIUS access and accounting
        messages:
            virtual    Used for Virtual interfaces.
            sdsl       Used for Symmetric DSL.
            adsl-cap   Used for Asymmetric DSL, Carrierless Amplitude Phase
                       Modulation.
            adsl-dmt   Used for Asymmetric DSL, Discrete Multi-Tone.
            idsl       Used for ISDN Digital Subscriber Line.
            xdsl       Used for Digital Subscriber Line of unknown type."
    DEFVAL    { xdsl }
    ::= { juniRadiusGeneralClient 6 }

juniRadiusClientTunnelAccounting OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the tunnel accounting feature, which causes the system
        to send tunnel and session accounting requests."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient  7 }

juniRadiusClientAcctSessionIdFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    decimal(0),
                    description(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the Acct-Session-ID attribute
        (attribute number 44):
            decimal      Use an ASCII decimal value only in the Acct-Session-ID
                         attribute.
            description  Use an ASCII description value which includes the
                         interface type (i.e. ATM), slot, port, and circuit
                         number (VPI and VCI for ATM), and a hexidecimal value
                         in the Acct-Session-ID attribute."
    DEFVAL    { description }
    ::= { juniRadiusGeneralClient 8 }

juniRadiusClientNasPortFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    xssssppp(0),
                    ssssxppp(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the NAS-Port attribute (attribute
        number 5):
            xssssppp    In the NAS-Port attribute (attribute 5) use format of 0
                        bit, followed by 4 bits for the slot number, 3 bits for
                        the port number, and finally, the circuit number in the
                        remaining bits.
            ssssxppp    In the NAS-Port attribute (attribute 5) use format of 4
                        bits for the slot number, followed by a 0 bit, 3 bits
                        for the port number and finally, the circuit number in
                        the remaining bits."
    DEFVAL    { ssssxppp }
    ::= { juniRadiusGeneralClient 9 }

juniRadiusClientCallingStationDelimiter OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The character use as for delimiting fields in the Calling-Station-ID
        attribute (attribute 31, from RFC 2865) sent by the client.  The default
        value is '#'."
    DEFVAL    { "#" }
    ::= { juniRadiusGeneralClient 10 }

juniRadiusClientEthernetPortType OBJECT-TYPE
    SYNTAX      INTEGER {
                    virtual(5),
                    ethernet(15) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value to use in the NAS-Port-Type RADIUS Attribute (attribute
        number 61) for Ethernet interfaces in the RADIUS access and accounting
        messages:
            ethernet   Used for Ethernet interfaces.
            virtual    Used for Virtual interfaces."
    DEFVAL    { ethernet }
    ::= { juniRadiusGeneralClient 11 }

juniRadiusClientIncludeIpAddrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Framed-IP-Address attribute in
        the RADIUS Acct-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 12 }

juniRadiusClientIncludeAcctSessionIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Acct-Session-ID attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 13 }

juniRadiusClientRollover OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the rollover to next server on receipt of
        access-reject."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 14 }

juniRadiusClientCallingStationIdFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    delimited(0),
                    fixedFormat(1),
                    fixedFormatAdapterEmbedded(2),
                    fixedFormatAdapterNewField(3),
                    fixedFormatStacked(4),
                    fixedFormatAdapterEmbeddedStacked(5),
                    fixedFormatAdapterNewFieldStacked(6) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the Calling-Station-ID attribute
        (attribute number 31):
            delimited   In the Calling-Station-ID attribute (attribute 31) use
                        the format '<delimiter><BAS name><delimiter>
                        <interface description><delimiter><layer 2 identifier>'.
            fixedFomat  In the Calling-Station-ID attribute (attribute 31) use
                        the format of 4 bytes for the host name (truncated if
                        needed), 2 digits of slot, 1 digit of port, 3 digits of
                        VPI, followed by 5 digits of VCI.
            fixedFormatAdapterEmbedded
                        In the Calling-Station-ID attribute (attribute 31) use
                        the format of 4 bytes for the host name (truncated if
                        needed), 1 digit of slot, 1 digit of adapter, 1 digit 
                        of port, 3 digits of VPI, followed by 5 digits of VCI.
            fixedFormatAdapterNewField
	                In the Calling-Station-ID attribute (attribute 31) use
                        the format of 4 bytes for the host name (truncated if
                        needed), 2 digits of slot, 1 digit of adapter, 2 digits
                        of port, 3 digits of VPI, followed by 5 digits of VCI.
            fixedFormatStacked
                        In the Calling-Station-ID attribute (attribute 31) use
                        the format of 4 bytes for the host name (truncated if
                        needed), 2 digits of slot, 1 digit of port, 4 digits of
                        SVLAN ID and 4 digits of VLAN ID only in the case of
                        Ethernet
            fixedFormatAdapterEmbeddedStacked
                        In the Calling-Station-ID attribute (attribute 31) use
                        the format of 4 bytes for the host name (truncated if
                        needed), 1 digit of slot, 1 digit of adapter, 1 digit 
                        of port, 4 digits of SVLAN ID and 4 digits of VLAN ID 
                        only in the case of Ethernet
            fixedFormatAdapterNewFieldStacked
                        In the Calling-Station-ID attribute (attribute 31) use
                        the format of 4 bytes for the host name (truncated if
                        needed), 2 digits of slot, 1 digit of adapter, 2 digits 
                        of port, 4 digits of SVLAN ID and 4 digits of VLAN ID 
                        only in the case of Ethernet"

    DEFVAL    { delimited }
    ::= { juniRadiusGeneralClient 15 }

juniRadiusClientNasIpAddrUse OBJECT-TYPE
    SYNTAX      INTEGER {
                    normal(0),
                    tunnelClientEndpoint(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value used by the client for the NAS-IP-Addr attribute (attribute
        number 4):
            normal               Use the ERX IP address value in the NAS-IP-Addr
                                 attribute (attribute 4).
            tunnelClientEndpoint Use the Tunnel Client's address value in the
                                 NAS-IP-Addr attribute (attribute 4) for tunnel
                                 users."
    DEFVAL    { normal }
    ::= { juniRadiusGeneralClient 16 }

juniRadiusClientIncludeAcctTunnelConnectionInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Acct-Tunnel-Connection attribute
        in the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 17 }

juniRadiusClientIncludeCalledStationIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Called-Station-ID attribute in
        the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 18 }

juniRadiusClientIncludeCallingStationIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Calling-Station-ID attribute in
        the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 19 }

juniRadiusClientIncludeConnectInfoInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Connect-Info attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 20 }

juniRadiusClientIncludeNasIdentifierInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Identifier attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 21 }

juniRadiusClientIncludeNasPortInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port attribute in the RADIUS
        Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 22 }

juniRadiusClientIncludeNasPortIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port-ID attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 23 }

juniRadiusClientIncludeNasPortTypeInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port-Type attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 24 }

juniRadiusClientIncludePppoeDescriptionInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the PPPoE-Description (VSA) attribute
        in the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 25 }

juniRadiusClientIncludeTunnelClientAuthIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Client-Auth-Id attribute
        in the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 26 }

juniRadiusClientIncludeTunnelClientEndpointInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Client-Endpoint attribute
        in the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 27 }

juniRadiusClientIncludeTunnelMediumTypeInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Medium attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 28 }

juniRadiusClientIncludeTunnelServerAttributesInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server attributes (Tunnel
        attributes for a PPP session terminated on the LNS) in the RADIUS
        Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 29 }

juniRadiusClientIncludeTunnelServerAuthIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server-Auth-Id attribute
        in the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 30 }

juniRadiusClientIncludeTunnelServerEndpointInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server-Endpoint attribute
        in the RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 31 }

juniRadiusClientIncludeTunnelTypeInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Type attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 32 }

juniRadiusClientIncludeAcctTunnelConnectionInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Acct-Tunnel-Connection attribute
        in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 33 }

juniRadiusClientIncludeCalledStationIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Called-Station-ID attribute in
        the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 34 }

juniRadiusClientIncludeCallingStationIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Calling-Station-ID attribute in
        the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 35 }

juniRadiusClientIncludeClassInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Class attribute in the RADIUS
        Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 36 }

juniRadiusClientIncludeConnectInfoInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Connect-Info attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 37 }

juniRadiusClientIncludeEgressPolicyNameInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Egress-Policy-Name (VSA)
        attribute in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 38 }

juniRadiusClientIncludeEventTimestampInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Event-Timestamp attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 39 }

juniRadiusClientIncludeFramedCompressionInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Framed-Compression attribute in
        the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 40 }

juniRadiusClientIncludeFramedIpNetmaskInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Framed-IP-Netmask attribute in
        the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 41 }

juniRadiusClientIncludeIngressPolicyNameInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Ingress-Policy-Name (VSA)
        attribute in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 42 }

juniRadiusClientIncludeNasIdentifierInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Identifier attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 43 }

juniRadiusClientIncludeNasPortInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port attribute in the RADIUS
        Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 44 }

juniRadiusClientIncludeNasPortIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port-ID attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 45 }

juniRadiusClientIncludeNasPortTypeInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port-Type attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 46 }

juniRadiusClientIncludePppoeDescriptionInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the PPPoE-Description (VSA) attribute
        in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 47 }

juniRadiusClientIncludeTunnelAssignmentIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Assignment-Id attribute in
        the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 48 }

juniRadiusClientIncludeTunnelClientAuthIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Client-Auth-Id attribute
        in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 49 }

juniRadiusClientIncludeTunnelClientEndpointInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Client-Endpoint attribute
        in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 50 }

juniRadiusClientIncludeTunnelMediumTypeInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Medium attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 51 }

juniRadiusClientIncludeTunnelPreferenceInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Preference attribute in
        the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 52 }

juniRadiusClientIncludeTunnelServerAttributesInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server attributes (Tunnel
        attributes for a PPP session terminated on the LNS) in the RADIUS
        Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 53 }

juniRadiusClientIncludeTunnelServerAuthIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server-Auth-Id attribute
        in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 54 }

juniRadiusClientIncludeTunnelServerEndpointInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server-Endpoint attribute
        in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 55 }

juniRadiusClientIncludeTunnelTypeInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Type attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 56 }

juniRadiusClientIncludeAcctTunnelConnectionInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Acct-Tunnel-Connection attribute
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 57 }

juniRadiusClientIncludeCalledStationIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Called-Station-ID attribute in
        the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 59 }

juniRadiusClientIncludeCallingStationIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Calling-Station-ID attribute in
        the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 60 }

juniRadiusClientIncludeClassInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Class attribute in the RADIUS
        Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 61 }

juniRadiusClientIncludeConnectInfoInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Connect-Info attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 62 }

juniRadiusClientIncludeEgressPolicyNameInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Egress-Policy-Name (VSA)
        attribute in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 63 }

juniRadiusClientIncludeEventTimestampInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Event-Timestamp attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 64 }

juniRadiusClientIncludeFramedCompressionInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Framed-Compression attribute in
        the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 65 }

juniRadiusClientIncludeFramedIpNetmaskInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Framed-IP-Netmask attribute in
        the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 66 }

juniRadiusClientIncludeIngressPolicyNameInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Ingress-Policy-Name (VSA)
        attribute in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 67 }

juniRadiusClientIncludeInputGigawordsInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Input-Gigawords attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 68 }

juniRadiusClientIncludeNasIdentifierInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Identifier attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 69 }

juniRadiusClientIncludeNasPortInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port attribute in the RADIUS
        Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 70 }

juniRadiusClientIncludeNasPortIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port-ID attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 71 }

juniRadiusClientIncludeNasPortTypeInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the NAS-Port-Type attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 72 }

juniRadiusClientIncludeOutputGigawordsInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Output-Gigawords attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 73 }

juniRadiusClientIncludePppoeDescriptionInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the PPPoE-Description (VSA) attribute
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 74 }

juniRadiusClientIncludeTunnelAssignmentIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Assignment-Id attribute in
        the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 75 }

juniRadiusClientIncludeTunnelClientAuthIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Client-Auth-Id attribute
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 76 }

juniRadiusClientIncludeTunnelClientEndpointInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Client-Endpoint attribute
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 77 }

juniRadiusClientIncludeTunnelMediumTypeInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Medium attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 78 }

juniRadiusClientIncludeTunnelPreferenceInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Preference attribute in
        the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 79 }

juniRadiusClientIncludeTunnelServerAttributesInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server attributes (Tunnel
        attributes for a PPP session terminated on the LNS) in the RADIUS
        Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 80 }

juniRadiusClientIncludeTunnelServerAuthIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server-Auth-Id attribute
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 81 }

juniRadiusClientIncludeTunnelServerEndpointInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Server-Endpoint attribute
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 82 }

juniRadiusClientIncludeTunnelTypeInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Type attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 83 }

juniRadiusClientIncludeInputGigapktsInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Input-Gigapkts attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 84 }

juniRadiusClientIncludeOutputGigapktsInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Output-Gigapkts attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 85 }

juniRadiusClientIgnoreFramedIpNetmask OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the Framed-IP-Netmask attribute in the
        RADIUS Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 86 }

juniRadiusClientIgnoreAtmCategory OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the ATM-Category (vsa) attribute in the
        RADIUS Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 87 }

juniRadiusClientIgnoreAtmMbs OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the ATM-MBS (vsa) attribute in the RADIUS
        Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 88 }

juniRadiusClientIgnoreAtmPcr OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the ATM-PCR (vsa) attribute in the RADIUS
        Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 89 }

juniRadiusClientIgnoreAtmScr OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the ATM-SCR-Or-CBR (vsa) attribute in the
        RADIUS Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 90 }

juniRadiusClientIgnoreEgressPolicyName OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the Egress-Policy-Name (vsa) attribute in the
        RADIUS Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 91 }

juniRadiusClientIgnoreIngressPolicyName OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the Ingress-Policy-Name (vsa) attribute in
        the RADIUS Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 92 }

juniRadiusClientIgnoreVirtualRouter OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the Virtual-Router (vsa) attribute in the
        RADIUS Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 93 }

juniRadiusClientTrapOnAuthServerUnavailable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables sending an SNMP trap for the condition that a specific
        RADIUS authentication server times out."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 94 }

juniRadiusClientTrapOnAcctServerUnavailable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables sending an SNMP trap for the condition that a specific
        RADIUS accounting server times out."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 95 }

juniRadiusClientTrapOnNoAuthServerAvailable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables sending an SNMP trap for the condition that all of the
        configured RADIUS authentication servers (in a virtual router context)
        time out."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 96 }

juniRadiusClientTrapOnNoAcctServerAvailable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables sending an SNMP trap for the condition that all of the
        configured RADIUS accounting servers (in a virtual router context) time
        out."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 97 }

juniRadiusClientTrapOnAuthServerAvailable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables sending an SNMP trap for the condition that a specific
        RADIUS authentication server has sent a response after being declared
        unavailable."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 98 }

juniRadiusClientTrapOnAcctServerAvailable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables sending an SNMP trap for the condition that a specific
        RADIUS accounting server has sent a response after being declared
        unavailable."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 99 }

juniRadiusClientPppoeNasPortFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    unique(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the Nas-Port attribute (attribute
        number 5) for PPPoE interfaces:
            none    Use the format specified in juniRadiusClientNasPortFormat
            unique  Use a unique value that is not related to the interface "
    DEFVAL    { none }
    ::= { juniRadiusGeneralClient 100 }

juniRadiusClientIncludeTunnelInterfaceIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Interface-Id (VSA)
        attribute in the RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 101 }

juniRadiusClientIncludeTunnelInterfaceIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Interface-Id (VSA) in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 102 }

juniRadiusClientIncludeTunnelInterfaceIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Tunnel-Interface-Id (VSA) in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 103 }

juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the L2TP PPP Disconnect Cause (VSA)
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 104 }

juniRadiusClientVlanNasPortFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    stacked(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the Nas-Port attribute (attribute
        number 5) for VLAN interfaces:
            none     Include the VLAN ID if configured
            stacked  Include both the SVLAN ID and VLAN ID if configured "
    DEFVAL    { none }
    ::= { juniRadiusGeneralClient 105 }

juniRadiusClientIncludeAcctMultiSessionIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Accounting Multilink Session ID
        in the RADIUS Access-Req packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 106 }

juniRadiusClientIncludeAcctMultiSessionIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Accounting Multilink Session ID
        in the RADIUS Accounting-Start packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 107 }

juniRadiusClientIncludeAcctMultiSessionIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Accounting Multilink Session ID
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 108 }

juniRadiusClientIncludeAscendNumInMultilinkInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Ascend Num In Multilink attribute
        in the RADIUS Access-Req packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 109 }

juniRadiusClientIncludeAscendNumInMultilinkInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Ascend Num In Multilink attribute
        in the RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 110 }

juniRadiusClientIncludeAscendNumInMultilinkInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Ascend Num In Multilink attribute
        in the RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 111 }

juniRadiusClientConnectInfoFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    default(0),
                    l2tpConnectSpeed(1),
                    l2tpConnectSpeedRxWhenEqual(2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the Connect-Info attribute
        (attribute number 77):
        
        default             The Connect-Info Attribute is generated from the
                            underlying interface id string.
        l2tpConnectSpeed    The Connect-Info Attribute is generated from
                            the received l2tp connect speed AVPs.  The format
                            is in bits per second:
                            <tx-connect-speed>[/<rx-connect-speed>]
                            The receive connect speed is included if non
                            zero and different from the transmit connect speed.
        l2tpConnectSpeedRXWhenEqual
                            The Connect-Info Attribute is generated from
                            the received l2tp connect speed AVPs.  The format
                            is in bits per second:
                            <tx-connect-speed>/<rx-connect-speed>
                            The receive connect speed is included if non-zero."

    DEFVAL    { default }
    ::= { juniRadiusGeneralClient 112 }

juniRadiusClientIncludeProfileServiceDescrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the AAA profile service description
        attribute in the RADIUS Access-Req packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 113 }

juniRadiusClientIncludeProfileServiceDescrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the AAA profile service description
        attribute in the RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 114 }

juniRadiusClientIncludeProfileServiceDescrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the AAA profile service description
        attribute in the RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 115 }

juniRadiusClientIncludeAcctAuthenticInAcctOn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the acct-authentic attribute in
        the RADIUS Accounting-On packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 116 }

juniRadiusClientIncludeAcctDelayTimeInAcctOn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the acct-delay-time attribute in
        the RADIUS Accounting-On packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 117 }

juniRadiusClientIncludeAcctSessionIdInAcctOn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the acct-session-id attribute in
        the RADIUS Accounting-On packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 118 }

juniRadiusClientIncludeNasIdentifierInAcctOn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the nas-identifier attribute in
        the RADIUS Accounting-On packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 119 }

juniRadiusClientIncludeEventTimestampInAcctOn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the event-timestamp attribute in
        the RADIUS Accounting-On packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 120 }

juniRadiusClientIncludeAcctAuthenticInAcctOff OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the acct-authentic attribute in
        the RADIUS Accounting-Off packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 121 }

juniRadiusClientIncludeAcctDelayTimeInAcctOff OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the acct-delay-time attribute in
        the RADIUS Accounting-Off packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 122 }

juniRadiusClientIncludeAcctSessionIdInAcctOff OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the acct-session-id attribute in
        the RADIUS Accounting-Off packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 123 }

juniRadiusClientIncludeAcctTerminateCauseInAcctOff OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the acct-terminate-cause attribute
        in the RADIUS Accounting-Off packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 124 }

juniRadiusClientIncludeNasIdentifierInAcctOff OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the nas-identifier attribute
        in the RADIUS Accounting-Off packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 125 }

juniRadiusClientIncludeEventTimestampInAcctOff OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the event-timestamp attribute
        in the RADIUS Accounting-Off packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 126 }

juniRadiusClientIncludeDhcpOptionsInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Options (vsa) attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 127 }

juniRadiusClientIncludeDhcpMacAddressInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Mac-Addresss (vsa) attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 128 }

juniRadiusClientIncludeDhcpGiAddressInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Gi-Address (vsa) attribute in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 129 }

juniRadiusClientIncludeDhcpOptionsInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Options (vsa) attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 130 }

juniRadiusClientIncludeDhcpMacAddressInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Mac-Addresss (vsa) attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 131 }

juniRadiusClientIncludeDhcpGiAddressInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Gi-Address (vsa) attribute in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 132 }

juniRadiusClientIncludeDhcpOptionsInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Options (vsa) attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 133 }

juniRadiusClientIncludeDhcpMacAddressInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Mac-Addresss (vsa) attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 134 }

juniRadiusClientIncludeDhcpGiAddressInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the Dhcp-Gi-Address (vsa) attribute in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 135 }

juniRadiusClientNasPortIdOverrideRemoteCircuitId OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables overriding the Nas-Port-Id with the PPPoe Remote Circuit Id."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 136 }

juniRadiusClientCallingStationIdOverrideRemoteCircuitId OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables overriding the Calling-Station-Id with the PPPoe Remote Circuit Id."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 137 }


juniRadiusClientIncludeMlpppBundleNameInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the MLPPP-Bundle-Name (VSA)
        attribute in the RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 138 }

juniRadiusClientIncludeMlpppBundleNameInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the MLPPP-Bundle-Name (VSA) in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 139 }

juniRadiusClientIncludeMlpppBundleNameInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the MLPPP-Bundle-Name (VSA) in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 140 }

juniRadiusClientOverrideNasInfo OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the overriding nas-ip-address and nas-identifier by values
        from authentication virual router. If juniRadiusClientNasIpAddrUse is not
        'normal', then nas-ip-address is not overriden."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 141 }

juniRadiusClientIncludeInterfaceDescriptionInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the interface description
        attribute in the RADIUS Access-Req packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 142 }

juniRadiusClientIncludeInterfaceDescriptionInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the interface description
        attribute in the RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 143 }

juniRadiusClientIncludeInterfaceDescriptionInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        " Enables/disables the inclusion of the interface description
        attribute in the RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 144 }


juniRadiusClientAtmNasPortFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    extended(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the Nas-Port attribute (attribute
        number 5) for ATM interfaces:
        none      Use the format specified in juniRadiusClientNasPortFormat
        extended  Use the extended format determined by atm nas-port
        field width values. "
    DEFVAL    { none }
    ::= { juniRadiusGeneralClient 145 }

juniRadiusClientNasPortFieldWidthAtmSlot OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the slot of the atm interface when the value of
        juniRadiusClientAtmNasPortFormat is extended. "
    DEFVAL    { 5 }
    ::= { juniRadiusGeneralClient 146 }

juniRadiusClientNasPortFieldWidthAtmAdapter OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the adapter of the atm interface when the value of
        juniRadiusClientAtmNasPortFormat is extended. "
    DEFVAL    { 0 }
    ::= { juniRadiusGeneralClient 147 }

juniRadiusClientNasPortFieldWidthAtmPort OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the port of the atm interface when the value of
        juniRadiusClientAtmNasPortFormat is extended. "
    DEFVAL    { 3 }
    ::= { juniRadiusGeneralClient 148 }

juniRadiusClientNasPortFieldWidthAtmVpi OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the vpi of the atm interface when the value of
        juniRadiusClientAtmNasPortFormat is extended. "
    DEFVAL    { 8 }
    ::= { juniRadiusGeneralClient 149 }

juniRadiusClientNasPortFieldWidthAtmVci OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the vci of the atm interface when the value of
        juniRadiusClientAtmNasPortFormat is extended. "
    DEFVAL    { 16 }
    ::= { juniRadiusGeneralClient 150 }

juniRadiusClientEthernetNasPortFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    extended(1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The format used by the client for the Nas-Port attribute (attribute
        number 5) for Ethernet interfaces:
        none      Use the format specified in juniRadiusClientNasPortFormat
        extended  Use the extended format determined by ethernet nas-port
        field width values. "
    DEFVAL    { none }
    ::= { juniRadiusGeneralClient 151 }

juniRadiusClientNasPortFieldWidthEthernetSlot OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the slot of the ethernet interface when the value of
        juniRadiusClientEthernetNasPortFormat is extended. "
    DEFVAL    { 5 }
    ::= { juniRadiusGeneralClient 152 }

juniRadiusClientNasPortFieldWidthEthernetAdapter OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the adapter of the ethernet interface when the value of
        juniRadiusClientEthernetNasPortFormat is extended. "
    DEFVAL    { 0 }
    ::= { juniRadiusGeneralClient 153 }

juniRadiusClientNasPortFieldWidthEthernetPort OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the port of the ethernet interface when the value of
        juniRadiusClientEthernetNasPortFormat is extended. "
    DEFVAL    { 3 }
    ::= { juniRadiusGeneralClient 154 }

juniRadiusClientNasPortFieldWidthEthernetSVlan OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the svlan of the ethernet interface when the value of
        juniRadiusClientEthernetNasPortFormat is extended. "
    DEFVAL    { 12 }
    ::= { juniRadiusGeneralClient 155 }

juniRadiusClientNasPortFieldWidthEthernetVlan OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the number of bits in the NAS-Port attribute (attribute 5)
        to be used for the vlan of the ethernet interface when the value of
        juniRadiusClientEthernetNasPortFormat is extended. "
    DEFVAL    { 12 }
    ::= { juniRadiusGeneralClient 156 }

juniRadiusClientRemoteCircuitIdFormat OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1..3))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The set of PPPoE Remote Circuit Id components configured. Each octet
        in this object contains one of the values defined in the 
        JuniRadiusClientRemoteCircuitIdFormatComponents TEXTUAL-CONVENTION. 
        Only following combinations are permited:
             agentCircuitId
             remoteCircuitId
             agentCircutiId, remoteCircuitId
             nasIdentifier, agentCircuitId
             nasIdentifier, remoteCircuitId
             nasIdentifier, agentCircutiId, remoteCircuitId
             dsl-format-1."
    DEFVAL    { '1'H }
    ::= { juniRadiusGeneralClient 157 }

juniRadiusClientRemoteCircuitIdDelimiter OBJECT-TYPE
    SYNTAX      DisplayString(SIZE(1))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The character use as for delimiting fields in the PPPoE Remote
        Circuit ID. The default value is '#'."
    DEFVAL    { "#" }
    ::= { juniRadiusGeneralClient 158 }

juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the l2c-access-loop-parameters (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 159 }

juniRadiusClientIncludeL2cDownStreamDataInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the l2c-down-stream-data (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 160 }

juniRadiusClientIncludeL2cUpStreamDataInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the l2c-up-stream-data (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 161 }

juniRadiusClientIncludeL2cDownStreamDataInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the l2c-down-stream-data (VSA) in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 162 }

juniRadiusClientIncludeL2cUpStreamDataInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the l2c-up-stream-data (VSA) in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 163 }

juniRadiusClientIncludeL2cDownStreamDataInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the l2c-down-stream-data (VSA) in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 164 }

juniRadiusClientIncludeL2cUpStreamDataInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the l2c-up-stream-data (VSA) in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 165 }

juniRadiusClientIncludeDslForumAttributesInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the DSL Forum attributes (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 166 }

juniRadiusClientIncludeDslForumAttributesInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the DSL Forum attributes (VSA) in the
        RADIUS Accounting-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 167 }

juniRadiusClientIncludeDslForumAttributesInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the DSL Forum attributes (VSA) in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 168 }

juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-loop-cir-id (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 169 }

juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-aggr-cir-id-bin (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 170 }

juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-aggr-cir-id-asc (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 171 }

juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-data-rate-up (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 172 }

juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-data-rate-dn (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 173 }

juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-data-rate-up (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 174 }

juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-in-data-rate-dn (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 175 }

juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-att-data-rate-up (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 176 }

juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-att-data-rate-dn (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 177 }

juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-data-rate-up (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 178 }

juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-data-rate-dn (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 179 }

juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-lp-data-rate-up (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 180 }

juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-lp-data-rate-dn (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 181 }

juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-interlv-delay-up (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 182 }

juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-interlv-delay-up (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 183 }

juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-interlv-delay-dn (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 184 }

juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-interlv-delay-dn (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 185 }

juniRadiusClientIncludeL2cDslLineStateInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-dsl-line-state (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 186 }

juniRadiusClientIncludeL2cDslTypeInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-dsl-type (VSA) in the
        RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 187 }

juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-loop-cir-id (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 188 }

juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-aggr-cir-id-bin (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 189 }

juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-aggr-cir-id-asc (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 190 }

juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-data-rate-up (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 191 }

juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-data-rate-dn (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 192 }

juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-data-rate-up (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 193 }

juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-in-data-rate-dn (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 194 }

juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-att-data-rate-up (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 195 }

juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-att-data-rate-dn (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 196 }

juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-data-rate-up (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 197 }

juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-data-rate-dn (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 198 }

juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-lp-data-rate-up (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 199 }

juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-lp-data-rate-dn (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 200 }

juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-interlv-delay-up (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 201 }

juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-interlv-delay-up (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 202 }

juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-interlv-delay-dn (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 203 }

juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-interlv-delay-dn (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 204 }

juniRadiusClientIncludeL2cDslLineStateInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-dsl-line-state (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 205 }

juniRadiusClientIncludeL2cDslTypeInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-dsl-type (VSA) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 206 }

juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-loop-cir-id (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 207 }

juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-aggr-cir-id-bin (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 208 }

juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-acc-aggr-cir-id-asc (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 209 }

juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-data-rate-up (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 210 }

juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-data-rate-dn (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 211 }

juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-data-rate-up (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 212 }

juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-in-data-rate-dn (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 213 }

juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-att-data-rate-up (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 214 }

juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-att-data-rate-dn (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 215 }

juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-data-rate-up (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 216 }

juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-data-rate-dn (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 217 }

juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-lp-data-rate-up (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 218 }

juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-min-lp-data-rate-dn (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 219 }

juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-interlv-delay-up (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 220 }

juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-interlv-delay-up (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 221 }

juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-max-interlv-delay-dn (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 222 }

juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-act-interlv-delay-dn (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 223 }

juniRadiusClientIncludeL2cDslLineStateInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-dsl-line-state (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 224 }

juniRadiusClientIncludeL2cDslTypeInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of l2cd-dsl-type (VSA) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 225 }

juniRadiusClientIncludeInterfaceIdInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-interface-id (96) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 226 }

juniRadiusClientIncludeIpv6PrefixInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-ipv6-prefix (97) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 227 }

juniRadiusClientIncludeInterfaceIdInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-interface-id (96) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 228 }

juniRadiusClientIncludeIpAddrInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-ip-address (8) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 229 }

juniRadiusClientIncludeIpv6PrefixInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-ipv6-prefix (97) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 230 }

juniRadiusClientIncludeDownStreamCalculatedQosRateInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of downstream-calculated-qos-rate (VSA)
		in the RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 231 }

juniRadiusClientIncludeUpStreamCalculatedQosRateInAccessReq OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of upstream-calculated-qos-rate (VSA)
		in the RADIUS Access-Request packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 232 }

juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of downstream-calculated-qos-rate (VSA)
		in the RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 233 }

juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of upstream-calculated-qos-rate (VSA)
		in the RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 234 }

juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of downstream-calculated-qos-rate (VSA)
		in the RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 235 }

juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of upstream-calculated-qos-rate (VSA)
		in the RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 236 }

juniRadiusClientIgnorePppoeMaxSession OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables ignoring the PPPoE Max Session (vsa) attribute in the
        RADIUS Access-Accept packet."
    DEFVAL    { true }
    ::= { juniRadiusGeneralClient 237 }

juniRadiusClientIncludeIpv6AccountingInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of the IPv6 Accounting (VSA) attributes in the
        RADIUS Accounting-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 238 }

juniRadiusClientIncludeDelegatedIpv6PrefixInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of delegated-ipv6-prefix (123) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 239 }

juniRadiusClientIncludeDelegatedIpv6PrefixInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of delegated-ipv6-prefix (123) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 240 }

juniRadiusClientIncludeFramedIpv6PoolInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-ipv6-pool (100) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 241 }

juniRadiusClientIncludeFramedIpv6PoolInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-ipv6-pool (100) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 242 } 

juniRadiusClientIncludeFramedIpv6RouteInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-ipv6-route (99) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 243 }

juniRadiusClientIncludeFramedIpv6RouteInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of framed-ipv6-route (99) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 244 } 

juniRadiusClientIncludeIpv6LocalInterfaceInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-local-interface (vsa) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 245 }

juniRadiusClientIncludeIpv6LocalInterfaceInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-local-interface (vsa) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 246 } 

juniRadiusClientIncludeIpv6NdRaPrefixInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-nd-ra-prefix (vsa) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 247 }

juniRadiusClientIncludeIpv6NdRaPrefixInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-nd-ra-prefix (vsa) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 248 } 

juniRadiusClientIncludeIpv6PrimaryDnsInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-primary-dns (vsa) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 249 }

juniRadiusClientIncludeIpv6PrimaryDnsInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-primary-dns (vsa) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 250 } 

juniRadiusClientIncludeIpv6SecondaryDnsInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-secondary-dns (vsa) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 251 }

juniRadiusClientIncludeIpv6SecondaryDnsInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-secondary-dns (vsa) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 252 } 

juniRadiusClientIncludeIpv6VirtualRouterInAcctStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-virtual-router (vsa) in the
        RADIUS Acct-Start packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 253 }

juniRadiusClientIncludeIpv6VirtualRouterInAcctStop OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables the inclusion of ipv6-virtual-router (vsa) in the
        RADIUS Acct-Stop packet."
    DEFVAL    { false }
    ::= { juniRadiusGeneralClient 254 } 


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Managed objects for RADIUS Authentication
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniRadiusAuthClientInvalidServerAddresses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Response packets received from unknown
        addresses."
    ::= { juniRadiusAuthClient 1 }

--
-- Statistics for RADIUS authentication servers
--
juniRadiusAuthClientServerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniRadiusAuthClientServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The (conceptual) table listing the RADIUS authentication servers with
        which the client shares a secret."
    ::= { juniRadiusAuthClient 2 }

juniRadiusAuthClientServerEntry OBJECT-TYPE
    SYNTAX      JuniRadiusAuthClientServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry (conceptual row) representing a RADIUS authentication server
        with which the client shares a secret."
    INDEX     { juniRadiusAuthClientServerAddress }
    ::= { juniRadiusAuthClientServerTable 1 }

JuniRadiusAuthClientServerEntry ::= SEQUENCE {
    juniRadiusAuthClientServerAddress            IpAddress,
    juniRadiusAuthClientServerPortNumber         Integer32,
    juniRadiusAuthClientRoundTripTime            TimeTicks,
    juniRadiusAuthClientAccessRequests           Counter32,
    juniRadiusAuthClientAccessRetransmissions    Counter32,
    juniRadiusAuthClientAccessAccepts            Counter32,
    juniRadiusAuthClientAccessRejects            Counter32,
    juniRadiusAuthClientAccessChallenges         Counter32,
    juniRadiusAuthClientMalformedAccessResponses Counter32,
    juniRadiusAuthClientBadAuthenticators        Counter32,
    juniRadiusAuthClientPendingRequests          Gauge32,
    juniRadiusAuthClientTimeouts                 Counter32,
    juniRadiusAuthClientUnknownTypes             Counter32,
    juniRadiusAuthClientPacketsDropped           Counter32 }

juniRadiusAuthClientServerAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IP address of the RADIUS authentication server referred to in this
        table entry.  A value of 0.0.0.0 indicates this entry is not in use."
    ::= { juniRadiusAuthClientServerEntry 1 }

juniRadiusAuthClientServerPortNumber  OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The UDP port the client is using to send requests to this server."
    ::= { juniRadiusAuthClientServerEntry 2 }

juniRadiusAuthClientRoundTripTime  OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time interval (in hundredths of seconds) between the most recent
        Access-Reply/Access-Challenge and the Access-Request that matched it
        from this RADIUS authentication server."
    ::= { juniRadiusAuthClientServerEntry 3 }


--
-- Request/Response statistics
--
-- TotalIncomingPackets = Accepts + Rejects + Challenges + UnknownTypes
--
-- TotalIncomingPackets - MalformedResponses - BadAuthenticators -
-- UnknownTypes - PacketsDropped = Successfully received
--
-- AccessRequests + PendingRequests + ClientTimeouts = Successfully Received
--
juniRadiusAuthClientAccessRequests OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Request packets sent to this server.  This
        does not include retransmissions."
    ::= { juniRadiusAuthClientServerEntry 4 }

juniRadiusAuthClientAccessRetransmissions OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Request packets retransmitted to this
        RADIUS authentication server."
    ::= { juniRadiusAuthClientServerEntry 5 }

juniRadiusAuthClientAccessAccepts OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Accept packets (valid or invalid) received
        from this server."
    ::= { juniRadiusAuthClientServerEntry 6 }

juniRadiusAuthClientAccessRejects OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Reject packets (valid or invalid) received
        from this server."
    ::= { juniRadiusAuthClientServerEntry  7 }

juniRadiusAuthClientAccessChallenges OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Challenge packets (valid or invalid)
        received from this server."
    ::= { juniRadiusAuthClientServerEntry 8 }


--
-- "Access-Response" includes an Access-Accept, Access-Challenge
-- or Access-Reject
--
juniRadiusAuthClientMalformedAccessResponses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of malformed RADIUS Access-Response packets received from
        this server.  Malformed packets include packets with an invalid length.
        Bad authenticators or signature attributes or unknown types are not
        included as malformed access responses."
    ::= { juniRadiusAuthClientServerEntry 9 }

juniRadiusAuthClientBadAuthenticators OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Response packets containing invalid
        authenticators or signature attributes received from this server."
    ::= { juniRadiusAuthClientServerEntry 10 }

juniRadiusAuthClientPendingRequests OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Access-Request packets destined for this server
        that have not yet timed out or received a response.  This variable is
        incremented when an Access-Request is sent and decremented due to
        receipt of an Access-Accept, Access-Reject or Access-Challenge, a
        timeout or retransmission."
    ::= { juniRadiusAuthClientServerEntry 11 }

juniRadiusAuthClientTimeouts OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of authentication timeouts to this server.  After a timeout
        the client may retry to the same server, send to a different server, or
        give up.  A retry to the same server is counted as a retransmit as well
        as a timeout.  A send to a different server is counted as a Request as
        well as a timeout."
    ::= { juniRadiusAuthClientServerEntry  12 }

juniRadiusAuthClientUnknownTypes OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS packets of unknown type which were received from
        this server on the authentication port."
    ::= { juniRadiusAuthClientServerEntry  13 }

juniRadiusAuthClientPacketsDropped OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS packets of which were received from this server on
        the authentication port and dropped for some other reason."
    ::= { juniRadiusAuthClientServerEntry  14 }


--
-- Configuration of RADIUS authentication servers
--
juniRadiusAuthClientCfgServerTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniRadiusAuthClientCfgServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The (conceptual) table listing the RADIUS authentication servers with
        which the client shares a secret."
    ::= { juniRadiusAuthClient 3 }

juniRadiusAuthClientCfgServerEntry  OBJECT-TYPE
    SYNTAX      JuniRadiusAuthClientCfgServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry (conceptual row) representing a RADIUS authentication server
        with which the client shares a secret."
    INDEX     { juniRadiusAuthClientCfgServerAddress }
    ::= { juniRadiusAuthClientCfgServerTable 1 }

JuniRadiusAuthClientCfgServerEntry ::= SEQUENCE {
    juniRadiusAuthClientCfgServerAddress         IpAddress,
    juniRadiusAuthClientCfgServerPortNumber      Integer32,
    juniRadiusAuthClientCfgKey                   DisplayString,
    juniRadiusAuthClientCfgTimeoutInterval       Integer32,
    juniRadiusAuthClientCfgRetries               Integer32,
    juniRadiusAuthClientCfgMaxPendingRequests    Integer32,
    juniRadiusAuthClientCfgRowStatus             RowStatus,
    juniRadiusAuthClientCfgPrecedence            Integer32,
    juniRadiusAuthClientCfgDeadTime              Integer32 }

juniRadiusAuthClientCfgServerAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IP address of the RADIUS authentication server referred to in this
        table entry."
    ::= { juniRadiusAuthClientCfgServerEntry 1 }

juniRadiusAuthClientCfgServerPortNumber  OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The UDP port the client is using to send requests to this server."
    DEFVAL    { 1812 }
    ::= { juniRadiusAuthClientCfgServerEntry 2 }

juniRadiusAuthClientCfgKey  OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The secret (RADIUS authenticator) used by the client during exchanges
        with this authentication server.  The default is a zero-length string,
        indicating no authenticator is used."
    DEFVAL    { ''H }
    ::= { juniRadiusAuthClientCfgServerEntry 3 }

juniRadiusAuthClientCfgTimeoutInterval  OBJECT-TYPE
    SYNTAX      Integer32 (1..1000)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The interval between retransmissions of a request to this
        authentication server."
    DEFVAL    { 3 }
    ::= { juniRadiusAuthClientCfgServerEntry 4 }

juniRadiusAuthClientCfgRetries  OBJECT-TYPE
    SYNTAX      Integer32 (0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of times to resend a request to this authentication
        server (in addition to the original request), before resorting to the
        server specified in the next entry."
    DEFVAL    { 3 }
    ::= { juniRadiusAuthClientCfgServerEntry 5 }

juniRadiusAuthClientCfgMaxPendingRequests  OBJECT-TYPE
    SYNTAX      Integer32 (10..32000)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of outstanding requests this server can support."
    DEFVAL    { 255 }
    ::= { juniRadiusAuthClientCfgServerEntry 6 }

juniRadiusAuthClientCfgRowStatus  OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Supports 'createAndGo' and 'destroy' only."
    ::= { juniRadiusAuthClientCfgServerEntry 7 }

juniRadiusAuthClientCfgPrecedence  OBJECT-TYPE
    SYNTAX      Integer32 (0..**********)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Relative precedence of this server with respect to other servers
        configured in this table.  Lower values correspond to higher precedence.
        Precedence is assigned by the device, in order of entry creation, from
        higher to lower precedence."
    ::= { juniRadiusAuthClientCfgServerEntry 8 }

juniRadiusAuthClientCfgDeadTime  OBJECT-TYPE
    SYNTAX      Integer32 (0..1440)
    UNITS       "minutes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The period of time, in minutes, to ignore this server after a request
        to the server times out (thereby avoiding additional request timeouts
        for this period, if the server failure persists)."
    DEFVAL    { 0 }
    ::= { juniRadiusAuthClientCfgServerEntry 9 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Managed objects for RADIUS Accounting
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniRadiusAcctClientInvalidServerAddresses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Response packets received from unknown
        addresses."
    ::= { juniRadiusAcctClient 1 }

--
-- Statistics for RADIUS accounting servers
--
juniRadiusAcctClientServerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniRadiusAcctClientServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The (conceptual) table listing the RADIUS accounting servers with which
        the client shares a secret."
    ::= { juniRadiusAcctClient 2 }

juniRadiusAcctClientServerEntry OBJECT-TYPE
    SYNTAX      JuniRadiusAcctClientServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry (conceptual row) representing a RADIUS accounting server with
        which the client shares a secret."
    INDEX     { juniRadiusAcctClientServerAddress }
    ::= { juniRadiusAcctClientServerTable 1 }

JuniRadiusAcctClientServerEntry ::= SEQUENCE {
    juniRadiusAcctClientServerAddress        IpAddress,
    juniRadiusAcctClientServerPortNumber     Integer32,
    juniRadiusAcctClientRoundTripTime        TimeTicks,
    juniRadiusAcctClientRequests             Counter32,
    juniRadiusAcctClientStartRequests        Counter32,
    juniRadiusAcctClientInterimRequests      Counter32,
    juniRadiusAcctClientStopRequests         Counter32,
    juniRadiusAcctClientRejectRequests       Counter32,
    juniRadiusAcctClientRetransmissions      Counter32,
    juniRadiusAcctClientResponses            Counter32,
    juniRadiusAcctClientStartResponses       Counter32,
    juniRadiusAcctClientInterimResponses     Counter32,
    juniRadiusAcctClientStopResponses        Counter32,
    juniRadiusAcctClientRejectResponses      Counter32,
    juniRadiusAcctClientMalformedResponses   Counter32,
    juniRadiusAcctClientBadAuthenticators    Counter32,
    juniRadiusAcctClientPendingRequests      Gauge32,
    juniRadiusAcctClientTimeouts             Counter32,
    juniRadiusAcctClientUnknownTypes         Counter32,
    juniRadiusAcctClientPacketsDropped       Counter32 }

juniRadiusAcctClientServerAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IP address of the RADIUS accounting server referred to in this
        table entry.  A value of 0.0.0.0 indicates this entry is not in use."
    ::= { juniRadiusAcctClientServerEntry 1 }

juniRadiusAcctClientServerPortNumber  OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The UDP port the client is using to send requests to this server."
    ::= { juniRadiusAcctClientServerEntry 2 }

juniRadiusAcctClientRoundTripTime  OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time interval between the most recent Accounting-Response and the
        Accounting-Request that matched it from this RADIUS accounting server."
    ::= { juniRadiusAcctClientServerEntry 3 }


--
-- Request/Response statistics
--
-- Requests = Responses + PendingRequests + ClientTimeouts
--
-- Responses - MalformedResponses - BadAuthenticators -
-- UnknownTypes - PacketsDropped = Successfully received
--
juniRadiusAcctClientRequests OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Request packets sent.  This does not
        include retransmissions."
    ::= { juniRadiusAcctClientServerEntry 4 }

juniRadiusAcctClientRetransmissions OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Request packets retransmitted to this
        RADIUS accounting server.  Retransmissions include retries where the
        Identifier and Acct-Delay have been updated, as well as those in which
        they remain the same."
    ::= { juniRadiusAcctClientServerEntry 5 }

juniRadiusAcctClientResponses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS packets received on the accounting port from this
        server."
    ::= { juniRadiusAcctClientServerEntry 6 }

juniRadiusAcctClientMalformedResponses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of malformed RADIUS Accounting-Response packets received
        from this server.  Malformed packets include packets with an invalid
        length.  Bad authenticators and unknown types are not included as
        malformed accounting responses."
    ::= { juniRadiusAcctClientServerEntry 7 }

juniRadiusAcctClientBadAuthenticators OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Response packets which contained
        invalid authenticators received from this server."
    ::= { juniRadiusAcctClientServerEntry 8 }

juniRadiusAcctClientPendingRequests OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Request packets sent to this server
        that have not yet timed out or received a response.  This variable is
        incremented when an Accounting-Request is sent and decremented due to
        receipt of an Accounting-Response, a timeout or a retransmission."
    ::= { juniRadiusAcctClientServerEntry 9 }

juniRadiusAcctClientTimeouts OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of accounting timeouts to this server.  After a timeout the
        client may retry to the same server, send to a different server, or give
        up.  A retry to the same server is counted as a retransmit as well as a
        timeout.  A send to a different server is counted as an
        Accounting-Request as well as a timeout."
    ::= { juniRadiusAcctClientServerEntry 10 }

juniRadiusAcctClientUnknownTypes OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS packets of unknown type which were received from
        this server on the accounting port."
    ::= { juniRadiusAcctClientServerEntry 11 }

juniRadiusAcctClientPacketsDropped OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS packets which were received from this server on
        the accounting port and dropped for some other reason."
    ::= { juniRadiusAcctClientServerEntry 12 }

juniRadiusAcctClientStartRequests OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Start request packets sent.  This does
        not include retransmissions."
    ::= { juniRadiusAcctClientServerEntry 13 }

juniRadiusAcctClientInterimRequests OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Interim request packets sent.  This
        does not include retransmissions."
    ::= { juniRadiusAcctClientServerEntry 14 }

juniRadiusAcctClientStopRequests OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS Accounting-Stop request packets sent.  This does
        not include retransmissions."
    ::= { juniRadiusAcctClientServerEntry 15 }

juniRadiusAcctClientStartResponses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS accounting-start response packets received on the
        accounting port from this server."
    ::= { juniRadiusAcctClientServerEntry 16 }

juniRadiusAcctClientInterimResponses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS accounting-interim response packets received on
        the accounting port from this server."
    ::= { juniRadiusAcctClientServerEntry 17 }

juniRadiusAcctClientStopResponses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS accounting-stop response packets received on the
        accounting port from this server."
    ::= { juniRadiusAcctClientServerEntry 18 }

juniRadiusAcctClientRejectRequests OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS accounting-reject packets sent on the accounting
        port from this server."
    ::= { juniRadiusAcctClientServerEntry 19 }

juniRadiusAcctClientRejectResponses OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RADIUS accounting-reject response packets received on the
        accounting port from this server."
    ::= { juniRadiusAcctClientServerEntry 20 }


--
-- Configuration of RADIUS accounting servers
--
juniRadiusAcctClientCfgServerTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF JuniRadiusAcctClientCfgServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The (conceptual) table listing the RADIUS accounting servers with which
        the client shares a secret."
    ::= { juniRadiusAcctClient 3 }

juniRadiusAcctClientCfgServerEntry  OBJECT-TYPE
    SYNTAX      JuniRadiusAcctClientCfgServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry (conceptual row) representing a RADIUS accounting server with
        which the client shares a secret."
    INDEX     { juniRadiusAcctClientCfgServerAddress }
    ::= { juniRadiusAcctClientCfgServerTable 1 }

JuniRadiusAcctClientCfgServerEntry ::= SEQUENCE {
    juniRadiusAcctClientCfgServerAddress         IpAddress,
    juniRadiusAcctClientCfgServerPortNumber      Integer32,
    juniRadiusAcctClientCfgKey                   DisplayString,
    juniRadiusAcctClientCfgTimeoutInterval       Integer32,
    juniRadiusAcctClientCfgRetries               Integer32,
    juniRadiusAcctClientCfgMaxPendingRequests    Integer32,
    juniRadiusAcctClientCfgRowStatus             RowStatus,
    juniRadiusAcctClientCfgPrecedence            Integer32,
    juniRadiusAcctClientCfgDeadTime              Integer32 }

juniRadiusAcctClientCfgServerAddress  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IP address of the RADIUS accounting server referred to in this
        table entry."
    ::= { juniRadiusAcctClientCfgServerEntry 1 }

juniRadiusAcctClientCfgServerPortNumber  OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The UDP port the client is using to send requests to this server."
    DEFVAL    { 1813 }
    ::= { juniRadiusAcctClientCfgServerEntry 2 }

juniRadiusAcctClientCfgKey  OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The secret (RADIUS authenticator) used by the client during exchanges
        with this accounting server.  The default is a zero-length string,
        indicating no authenticator is used."
    DEFVAL    { ''H }
    ::= { juniRadiusAcctClientCfgServerEntry 3 }

juniRadiusAcctClientCfgTimeoutInterval  OBJECT-TYPE
    SYNTAX      Integer32 (1..1000)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The interval between retransmissions of a request to this accounting
        server."
    DEFVAL    { 3 }
    ::= { juniRadiusAcctClientCfgServerEntry 4 }

juniRadiusAcctClientCfgRetries  OBJECT-TYPE
    SYNTAX      Integer32 (0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of times to resend a request to this accounting
        server (in addition to the original request), before resorting to the
        server specified in the next entry."
    DEFVAL    { 3 }
    ::= { juniRadiusAcctClientCfgServerEntry 5 }

juniRadiusAcctClientCfgMaxPendingRequests  OBJECT-TYPE
    SYNTAX      Integer32 (10..96000)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of outstanding requests this server can support."
    DEFVAL    { 255 }
    ::= { juniRadiusAcctClientCfgServerEntry 6 }

juniRadiusAcctClientCfgRowStatus  OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Supports 'createAndGo' and 'destroy' only."
    ::= { juniRadiusAcctClientCfgServerEntry 7 }

juniRadiusAcctClientCfgPrecedence  OBJECT-TYPE
    SYNTAX      Integer32 (0..**********)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Relative precedence of this server with respect to other servers
        configured in this table.  Lower values correspond to higher precedence.
        Precedence is assigned by the device, in order of entry creation, from
        higher to lower precedence."
    ::= { juniRadiusAcctClientCfgServerEntry 8 }

juniRadiusAcctClientCfgDeadTime  OBJECT-TYPE
    SYNTAX      Integer32 (0..1440)
    UNITS       "minutes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The period of time, in minutes, to ignore this server after a request
        to the server times out (thereby avoiding additional request timeouts
        for this period, if the server failure persists)."
    DEFVAL    { 0 }
    ::= { juniRadiusAcctClientCfgServerEntry 9 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Notification control objects
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniRadiusClientTrapControl  OBJECT IDENTIFIER ::= { juniRadiusClientMIB 4 }

juniRadiusAuthClientUnavailableServer  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IP address of the RADIUS client's former authentication server that
        is no longer available.  The value of this object is equivalent to the
        prior value of juniRadiusAuthClientCfgServerAddress."
    ::= { juniRadiusClientTrapControl 1 }

juniRadiusAuthClientNextAvailableServer  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The next available RADIUS authentication server, replacing the one that
        is unavailable.  The value of this object is equivalent to the current
        value of juniRadiusAuthClientCfgServerAddress."
    ::= { juniRadiusClientTrapControl 2 }

juniRadiusAcctClientUnavailableServer  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IP address of the RADIUS client's former accounting server that is
        no longer available.  The value of this object is equivalent to the
        prior value of juniRadiusAcctClientCfgServerAddress."
    ::= { juniRadiusClientTrapControl 3 }

juniRadiusAcctClientNextAvailableServer  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The next available RADIUS accounting server, replacing the one that is
        unavailable.  The value of this object is equivalent to the current
        value of juniRadiusAcctClientCfgServerAddress."
    ::= { juniRadiusClientTrapControl 4 }

juniRadiusAuthClientAvailableServer  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The re-available RADIUS authentication server after a period of time
        called dead-time.  The value of this object is equivalent to the current
        value of juniRadiusAuthClientCfgServerAddress."
    ::= { juniRadiusClientTrapControl 5 }

juniRadiusAcctClientAvailableServer  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The re-available RADIUS accounting server after a period of time called
        dead-time.  The value of this object is equivalent to the current value
        of juniRadiusAcctClientCfgServerAddress."
    ::= { juniRadiusClientTrapControl 6 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Notifications
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniRadiusClientTraps       OBJECT IDENTIFIER ::= { juniRadiusClientMIB 3 }
juniRadiusClientTrapPrefix  OBJECT IDENTIFIER ::= { juniRadiusClientTraps 0 }

--
-- RADIUS authentication server unavailable traps
--
juniRadiusAuthClientServerUnavailable  NOTIFICATION-TYPE
    OBJECTS {
        juniRadiusAuthClientUnavailableServer,
        juniRadiusAuthClientNextAvailableServer }
    STATUS      current
    DESCRIPTION
        "This trap will be generated when the requested authentication server is
        not available."
    ::= { juniRadiusClientTrapPrefix 1 }

juniRadiusAuthClientNoServerAvailable  NOTIFICATION-TYPE
    STATUS      current
    DESCRIPTION
        "This trap will be generated when all of the requested servers were not
        available."
    ::= { juniRadiusClientTrapPrefix 2 }

--
-- RADIUS accounting server unavailable traps
--
juniRadiusAcctClientServerUnavailable  NOTIFICATION-TYPE
    OBJECTS {
        juniRadiusAcctClientUnavailableServer,
        juniRadiusAcctClientNextAvailableServer }
    STATUS      current
    DESCRIPTION
        "This trap will be generated when the requested accounting server is not
        available."
    ::= { juniRadiusClientTrapPrefix 3 }

juniRadiusAcctClientNoServerAvailable  NOTIFICATION-TYPE
    STATUS      current
    DESCRIPTION
        "This trap will be generated when all of the requested servers were not
        available."
    ::= { juniRadiusClientTrapPrefix 4 }

--
-- RADIUS authentication server available trap
--
juniRadiusAuthClientServerAvailable  NOTIFICATION-TYPE
    OBJECTS {
        juniRadiusAuthClientAvailableServer }
    STATUS      current
    DESCRIPTION
        "This trap will be generated when the requested authentication server
        becomes available again after a period of time."
    ::= { juniRadiusClientTrapPrefix 5 }

--
-- RADIUS accounting server available trap
--
juniRadiusAcctClientServerAvailable  NOTIFICATION-TYPE
    OBJECTS {
        juniRadiusAcctClientAvailableServer }
    STATUS      current
    DESCRIPTION
        "This trap will be generated when the requested accounting server
        becomes available again after a period of time."
    ::= { juniRadiusClientTrapPrefix 6 }


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- Conformance information
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
juniRadiusClientMIBConformance  OBJECT IDENTIFIER
    ::= { juniRadiusClientMIB 2 }
juniRadiusClientMIBCompliances  OBJECT IDENTIFIER
    ::= { juniRadiusClientMIBConformance 1 }
juniRadiusClientMIBGroups       OBJECT IDENTIFIER
    ::= { juniRadiusClientMIBConformance 2 }

--
-- compliance statements
--
juniRadiusAuthClientCompliance  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when the juniRadiusClientSourceAddress object
        was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusGeneralClientGroup,
            juniRadiusAuthClientGroup }
    ::= { juniRadiusClientMIBCompliances 1 }                       -- JUNOSe 1.1

juniRadiusAcctClientCompliance  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for accounting clients implementing the
        Juniper RADIUS Client MIB accounting functionality.  This statement
        became obsolete when the juniRadiusClientSourceAddress object was
        added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusGeneralClientGroup,
            juniRadiusAcctClientGroup }
    ::= { juniRadiusClientMIBCompliances 2 }                       -- JUNOSe 1.1

juniRadiusAuthClientCompliance2  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when new objects were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusGeneralClientGroup2,
            juniRadiusAuthClientGroup }
    ::= { juniRadiusClientMIBCompliances 3 }                       -- JUNOSe 2.0

juniRadiusAcctClientCompliance2  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for accounting clients implementing the
        Juniper RADIUS Client MIB accounting functionality.  This statement
        became obsolete when new objects were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusGeneralClientGroup2,
            juniRadiusAcctClientGroup }
    ::= { juniRadiusClientMIBCompliances 4 }                       -- JUNOSe 2.0

juniRadiusClientCompliance  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when new B-RAS objects were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup }
        GROUP       juniRadiusAuthClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusBrasClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 5 }                       -- JUNOSe 3.0

juniRadiusClientCompliance2  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when new objects were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup }
        GROUP       juniRadiusAuthClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusBrasClientGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 6 }                       -- JUNOSe 3.1

juniRadiusClientCompliance3  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when the juniRadiusClientNasIpAddrUse object
        was added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusBrasClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 7 }                       -- JUNOSe 3.2

juniRadiusClientCompliance4  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when objects were added to indicate which
        RADIUS attributes should be included or excluded from RADIUS packets."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusBrasClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 8 }                       -- JUNOSe 3.3

juniRadiusClientCompliance5  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when notifications for unavailable RADIUS
        servers were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusBrasClientGroup5
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 9 }                       -- JUNOSe 4.0

juniRadiusClientCompliance6  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when attribute-ignore objects were added to
        the B-RAS group and accounting and authetication servers available
        notifications were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusBrasClientGroup5
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 10 }                      -- JUNOSe 4.1

juniRadiusClientCompliance7  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when authentication and accounting objects
        were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup6
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 11 }                      -- JUNOSe 5.0

juniRadiusClientCompliance8  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when accounting reject counters were added."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup7
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 12 }                      -- JUNOSe 5.1

juniRadiusClientCompliance9  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when new object was added to the BRAS group to
        indicate which RADIUS attributes should be included or excluded from
        RADIUS packets."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup8
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 13 }                      -- JUNOSe 5.2

juniRadiusClientCompliance10  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality.  This
        statement became obsolete when new objects were added to indicate
        which RADIUS attributes for DHCP VSAs should be included or excluded
        from RADIUS packets."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup9
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 14 }                      -- JUNOSe 5.3

juniRadiusClientCompliance11  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
        the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup10
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 15 }                      -- JUNOSe 6.1

juniRadiusClientCompliance12  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
	the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup11
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 16 }                      -- JUNOSe 6.1

juniRadiusClientCompliance13  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
	the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup14
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 17 }                      -- JUNOSe 7.3

juniRadiusClientCompliance14  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "The compliance statement for authentication clients implementing
	the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup15
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 18 }                      -- JUNOSe 8.1

juniRadiusClientCompliance15  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "Obsolete compliance statement for authentication clients implementing
	the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup16
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 19 }                      -- JUNOSe 8.2

juniRadiusClientCompliance16  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "The compliance statement for authentication clients implementing
	the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup18
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 20 }                      -- JUNOSe 9.3

juniRadiusClientCompliance17  MODULE-COMPLIANCE
    STATUS      obsolete
    DESCRIPTION
        "The compliance statement for authentication clients implementing
	the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup19
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 21 }                      -- JUNOSe 10.2

juniRadiusClientCompliance18  MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for authentication clients implementing
	the Juniper RADIUS Client MIB authentication functionality."
    MODULE   -- this module
        MANDATORY-GROUPS {
            juniRadiusBasicClientGroup2 }
        GROUP       juniRadiusAuthClientGroup3
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAuthNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication support."
        GROUP       juniRadiusAcctClientGroup4
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement accounting support."
        GROUP       juniRadiusAcctNotificationGroup2
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement authentication or accounting support."
        GROUP       juniRadiusBrasClientGroup20
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement B-RAS support."
        GROUP       juniRadiusTunnelClientGroup
            DESCRIPTION
                "This group is mandatory only for those E-series agents that
                implement tunneling support."
    ::= { juniRadiusClientMIBCompliances 22 }                      -- JUNOSe 10.2

--
-- units of conformance
--
juniRadiusGeneralClientGroup  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientIdentifier,
        juniRadiusClientAlgorithm }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete basic collection of objects providing management of RADIUS
        Clients.  This group became obsolete when juniRadiusClientSourceAddress
        was added."
    ::= { juniRadiusClientMIBGroups 1 }                            -- JUNOSe 1.1

juniRadiusAuthClientGroup  OBJECT-GROUP
    OBJECTS {
        juniRadiusAuthClientInvalidServerAddresses,

        juniRadiusAuthClientServerPortNumber,
        juniRadiusAuthClientRoundTripTime,
        juniRadiusAuthClientAccessRequests,
        juniRadiusAuthClientAccessRetransmissions,
        juniRadiusAuthClientAccessAccepts,
        juniRadiusAuthClientAccessRejects,
        juniRadiusAuthClientAccessChallenges,
        juniRadiusAuthClientMalformedAccessResponses,
        juniRadiusAuthClientBadAuthenticators,
        juniRadiusAuthClientPendingRequests,
        juniRadiusAuthClientTimeouts,
        juniRadiusAuthClientUnknownTypes,
        juniRadiusAuthClientPacketsDropped,

        juniRadiusAuthClientCfgServerPortNumber,
        juniRadiusAuthClientCfgKey,
        juniRadiusAuthClientCfgTimeoutInterval,
        juniRadiusAuthClientCfgRetries,
        juniRadiusAuthClientCfgMaxPendingRequests,
        juniRadiusAuthClientCfgRowStatus,
        juniRadiusAuthClientCfgPrecedence,
        juniRadiusAuthClientCfgDeadTime }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of RADIUS
        Authentication Clients.  This group became obsolete when notification
        objects for an unavailable authentication server were added."
    ::= { juniRadiusClientMIBGroups 2 }                            -- JUNOSe 1.1

juniRadiusAcctClientGroup  OBJECT-GROUP
    OBJECTS {
        juniRadiusAcctClientInvalidServerAddresses,

        juniRadiusAcctClientServerPortNumber,
        juniRadiusAcctClientRoundTripTime,
        juniRadiusAcctClientRequests,
        juniRadiusAcctClientRetransmissions,
        juniRadiusAcctClientResponses,
        juniRadiusAcctClientMalformedResponses,
        juniRadiusAcctClientBadAuthenticators,
        juniRadiusAcctClientPendingRequests,
        juniRadiusAcctClientTimeouts,
        juniRadiusAcctClientUnknownTypes,
        juniRadiusAcctClientPacketsDropped,

        juniRadiusAcctClientCfgServerPortNumber,
        juniRadiusAcctClientCfgKey,
        juniRadiusAcctClientCfgTimeoutInterval,
        juniRadiusAcctClientCfgRetries,
        juniRadiusAcctClientCfgMaxPendingRequests,
        juniRadiusAcctClientCfgRowStatus,
        juniRadiusAcctClientCfgPrecedence,
        juniRadiusAcctClientCfgDeadTime }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of RADIUS
        Accounting Clients.  This group became obsolete when notification
        objects for an unavailable accounting server were added."
    ::= { juniRadiusClientMIBGroups 3 }                            -- JUNOSe 1.1

juniRadiusGeneralClientGroup2 OBJECT-GROUP
    OBJECTS {
        juniRadiusClientIdentifier,
        juniRadiusClientAlgorithm,
        juniRadiusClientSourceAddress }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete basic collection of objects providing management of RADIUS
        Clients.  This group became obsolete when new objects were added."
    ::= { juniRadiusClientMIBGroups 4 }                            -- JUNOSe 2.0

juniRadiusBasicClientGroup  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientIdentifier,
        juniRadiusClientAlgorithm,
        juniRadiusClientSourceAddress,
        juniRadiusClientUdpChecksum,
        juniRadiusClientNasIdentifier }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing basic management of RADIUS
        Clients.  This group became obsolete when the juniRadiusClientRollover
        object was added."
    ::= { juniRadiusClientMIBGroups 5 }                            -- JUNOSe 3.0

juniRadiusBrasClientGroup  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when new
        objects were added."
    ::= { juniRadiusClientMIBGroups 6 }                            -- JUNOSe 3.0

juniRadiusTunnelClientGroup  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientTunnelAccounting }
    STATUS      current
    DESCRIPTION
        "An object providing management of tunneling functions for RADIUS
        Clients."
    ::= { juniRadiusClientMIBGroups 7 }                            -- JUNOSe 3.0

juniRadiusBrasClientGroup2  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when the
        juniRadiusClientCallingStationIdFormat object was added."
    ::= { juniRadiusClientMIBGroups 8 }                            -- JUNOSe 3.1

juniRadiusBasicClientGroup2  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientIdentifier,
        juniRadiusClientAlgorithm,
        juniRadiusClientSourceAddress,
        juniRadiusClientUdpChecksum,
        juniRadiusClientNasIdentifier,
        juniRadiusClientRollover }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing basic management of RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 9 }                            -- JUNOSe 3.2

juniRadiusBrasClientGroup3  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when the
        juniRadiusClientNasIpAddrUse object was added."
    ::= { juniRadiusClientMIBGroups 10 }                           -- JUNOSe 3.2

juniRadiusBrasClientGroup4  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when objects
        were added to indicate which RADIUS attributes should be included or
        excluded from RADIUS packets."
    ::= { juniRadiusClientMIBGroups 11 }                           -- JUNOSe 3.3

juniRadiusBrasClientGroup5  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when objects
        to ignore attributes and enable/disable traps were added."
    ::= { juniRadiusClientMIBGroups 12 }                           -- JUNOSe 4.0

juniRadiusAuthClientGroup2  OBJECT-GROUP
    OBJECTS {
        juniRadiusAuthClientInvalidServerAddresses,

        juniRadiusAuthClientServerPortNumber,
        juniRadiusAuthClientRoundTripTime,
        juniRadiusAuthClientAccessRequests,
        juniRadiusAuthClientAccessRetransmissions,
        juniRadiusAuthClientAccessAccepts,
        juniRadiusAuthClientAccessRejects,
        juniRadiusAuthClientAccessChallenges,
        juniRadiusAuthClientMalformedAccessResponses,
        juniRadiusAuthClientBadAuthenticators,
        juniRadiusAuthClientPendingRequests,
        juniRadiusAuthClientTimeouts,
        juniRadiusAuthClientUnknownTypes,
        juniRadiusAuthClientPacketsDropped,

        juniRadiusAuthClientCfgServerPortNumber,
        juniRadiusAuthClientCfgKey,
        juniRadiusAuthClientCfgTimeoutInterval,
        juniRadiusAuthClientCfgRetries,
        juniRadiusAuthClientCfgMaxPendingRequests,
        juniRadiusAuthClientCfgRowStatus,
        juniRadiusAuthClientCfgPrecedence,
        juniRadiusAuthClientCfgDeadTime,

        juniRadiusAuthClientUnavailableServer,
        juniRadiusAuthClientNextAvailableServer }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of RADIUS
        Authentication Clients.  This group became obsolete when authentication
        server available notification support was added."
    ::= { juniRadiusClientMIBGroups 13 }                           -- JUNOSe 4.1

juniRadiusAcctClientGroup2  OBJECT-GROUP
    OBJECTS {
        juniRadiusAcctClientInvalidServerAddresses,

        juniRadiusAcctClientServerPortNumber,
        juniRadiusAcctClientRoundTripTime,
        juniRadiusAcctClientRequests,
        juniRadiusAcctClientRetransmissions,
        juniRadiusAcctClientResponses,
        juniRadiusAcctClientMalformedResponses,
        juniRadiusAcctClientBadAuthenticators,
        juniRadiusAcctClientPendingRequests,
        juniRadiusAcctClientTimeouts,
        juniRadiusAcctClientUnknownTypes,
        juniRadiusAcctClientPacketsDropped,

        juniRadiusAcctClientCfgServerPortNumber,
        juniRadiusAcctClientCfgKey,
        juniRadiusAcctClientCfgTimeoutInterval,
        juniRadiusAcctClientCfgRetries,
        juniRadiusAcctClientCfgMaxPendingRequests,
        juniRadiusAcctClientCfgRowStatus,
        juniRadiusAcctClientCfgPrecedence,
        juniRadiusAcctClientCfgDeadTime,

        juniRadiusAcctClientUnavailableServer,
        juniRadiusAcctClientNextAvailableServer }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of RADIUS
        Accounting Clients.  This group became obsolete when detailed accounting
        statistics and accounting server available notification support were
        added."
    ::= { juniRadiusClientMIBGroups 14 }                           -- JUNOSe 4.1

juniRadiusAuthNotificationGroup  NOTIFICATION-GROUP
    NOTIFICATIONS {
        juniRadiusAuthClientServerUnavailable,
        juniRadiusAuthClientNoServerAvailable }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of management notifications for RADUIS
        authentication events.  This group became obsolete when authentication
        server available notification was added."
    ::= { juniRadiusClientMIBGroups 15 }                           -- JUNOSe 4.1

juniRadiusAcctNotificationGroup  NOTIFICATION-GROUP
    NOTIFICATIONS {
        juniRadiusAcctClientServerUnavailable,
        juniRadiusAcctClientNoServerAvailable }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of management notifications for RADUIS accounting
        events.  This group became obsolete when accounting server available
        notification was added."
    ::= { juniRadiusClientMIBGroups 16 }                           -- JUNOSe 4.1

juniRadiusBrasClientGroup6  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when objects
        for PPPoE Nas-Port format were added."
    ::= { juniRadiusClientMIBGroups 17 }                           -- JUNOSe 5.0

juniRadiusAuthClientGroup3  OBJECT-GROUP
    OBJECTS {
        juniRadiusAuthClientInvalidServerAddresses,

        juniRadiusAuthClientServerPortNumber,
        juniRadiusAuthClientRoundTripTime,
        juniRadiusAuthClientAccessRequests,
        juniRadiusAuthClientAccessRetransmissions,
        juniRadiusAuthClientAccessAccepts,
        juniRadiusAuthClientAccessRejects,
        juniRadiusAuthClientAccessChallenges,
        juniRadiusAuthClientMalformedAccessResponses,
        juniRadiusAuthClientBadAuthenticators,
        juniRadiusAuthClientPendingRequests,
        juniRadiusAuthClientTimeouts,
        juniRadiusAuthClientUnknownTypes,
        juniRadiusAuthClientPacketsDropped,

        juniRadiusAuthClientCfgServerPortNumber,
        juniRadiusAuthClientCfgKey,
        juniRadiusAuthClientCfgTimeoutInterval,
        juniRadiusAuthClientCfgRetries,
        juniRadiusAuthClientCfgMaxPendingRequests,
        juniRadiusAuthClientCfgRowStatus,
        juniRadiusAuthClientCfgPrecedence,
        juniRadiusAuthClientCfgDeadTime,

        juniRadiusAuthClientUnavailableServer,
        juniRadiusAuthClientNextAvailableServer,
        juniRadiusAuthClientAvailableServer }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of RADIUS Authentication
        Clients."
    ::= { juniRadiusClientMIBGroups 18 }                           -- JUNOSe 5.0

juniRadiusAcctClientGroup3  OBJECT-GROUP
    OBJECTS {
        juniRadiusAcctClientInvalidServerAddresses,

        juniRadiusAcctClientServerPortNumber,
        juniRadiusAcctClientRoundTripTime,
        juniRadiusAcctClientRequests,
        juniRadiusAcctClientStartRequests,
        juniRadiusAcctClientInterimRequests,
        juniRadiusAcctClientStopRequests,
        juniRadiusAcctClientRetransmissions,
        juniRadiusAcctClientResponses,
        juniRadiusAcctClientStartResponses,
        juniRadiusAcctClientInterimResponses,
        juniRadiusAcctClientStopResponses,
        juniRadiusAcctClientMalformedResponses,
        juniRadiusAcctClientBadAuthenticators,
        juniRadiusAcctClientPendingRequests,
        juniRadiusAcctClientTimeouts,
        juniRadiusAcctClientUnknownTypes,
        juniRadiusAcctClientPacketsDropped,

        juniRadiusAcctClientCfgServerPortNumber,
        juniRadiusAcctClientCfgKey,
        juniRadiusAcctClientCfgTimeoutInterval,
        juniRadiusAcctClientCfgRetries,
        juniRadiusAcctClientCfgMaxPendingRequests,
        juniRadiusAcctClientCfgRowStatus,
        juniRadiusAcctClientCfgPrecedence,
        juniRadiusAcctClientCfgDeadTime,

        juniRadiusAcctClientUnavailableServer,
        juniRadiusAcctClientNextAvailableServer,
        juniRadiusAcctClientAvailableServer }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of RADIUS
        Accounting Clients.  This group became obsolete when reject counters
        were added."
    ::= { juniRadiusClientMIBGroups 19 }                           -- JUNOSe 5.0

juniRadiusAuthNotificationGroup2  NOTIFICATION-GROUP
    NOTIFICATIONS {
        juniRadiusAuthClientServerUnavailable,
        juniRadiusAuthClientNoServerAvailable,
        juniRadiusAuthClientServerAvailable }
    STATUS      current
    DESCRIPTION
        "Management notifications for RADUIS authentication events."
    ::= { juniRadiusClientMIBGroups 20 }                           -- JUNOSe 5.0

juniRadiusAcctNotificationGroup2  NOTIFICATION-GROUP
    NOTIFICATIONS {
        juniRadiusAcctClientServerUnavailable,
        juniRadiusAcctClientNoServerAvailable,
        juniRadiusAcctClientServerAvailable }
    STATUS      current
    DESCRIPTION
        "Management notifications for RADUIS accounting events."
    ::= { juniRadiusClientMIBGroups 21 }                           -- JUNOSe 5.0

juniRadiusBrasClientGroup7  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when an object
        for VLAN Nas-Port format was added."
    ::= { juniRadiusClientMIBGroups 22 }                           -- JUNOSe 5.1

juniRadiusAcctClientGroup4  OBJECT-GROUP
    OBJECTS {
        juniRadiusAcctClientInvalidServerAddresses,

        juniRadiusAcctClientServerPortNumber,
        juniRadiusAcctClientRoundTripTime,
        juniRadiusAcctClientRequests,
        juniRadiusAcctClientStartRequests,
        juniRadiusAcctClientInterimRequests,
        juniRadiusAcctClientStopRequests,
        juniRadiusAcctClientRejectRequests,
        juniRadiusAcctClientRetransmissions,
        juniRadiusAcctClientResponses,
        juniRadiusAcctClientStartResponses,
        juniRadiusAcctClientInterimResponses,
        juniRadiusAcctClientStopResponses,
        juniRadiusAcctClientRejectResponses,
        juniRadiusAcctClientMalformedResponses,
        juniRadiusAcctClientBadAuthenticators,
        juniRadiusAcctClientPendingRequests,
        juniRadiusAcctClientTimeouts,
        juniRadiusAcctClientUnknownTypes,
        juniRadiusAcctClientPacketsDropped,

        juniRadiusAcctClientCfgServerPortNumber,
        juniRadiusAcctClientCfgKey,
        juniRadiusAcctClientCfgTimeoutInterval,
        juniRadiusAcctClientCfgRetries,
        juniRadiusAcctClientCfgMaxPendingRequests,
        juniRadiusAcctClientCfgRowStatus,
        juniRadiusAcctClientCfgPrecedence,
        juniRadiusAcctClientCfgDeadTime,

        juniRadiusAcctClientUnavailableServer,
        juniRadiusAcctClientNextAvailableServer,
        juniRadiusAcctClientAvailableServer }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of RADIUS Accounting
        Clients."
    ::= { juniRadiusClientMIBGroups 23 }                           -- JUNOSe 5.2

juniRadiusBrasClientGroup8  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientVlanNasPortFormat }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS
        functions for RADIUS Clients.  This group became obsolete when new
        objects were added to indicate which RADIUS attributes should be
        included or excluded from RADIUS packets."
    ::= { juniRadiusClientMIBGroups 24 }                           -- JUNOSe 5.2

juniRadiusBrasClientGroup9  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 25 }                           -- JUNOSe 5.3

juniRadiusBrasClientGroup10  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients.  This group became obsolete when new objects were
        added to enable/disable the overriding of the nas-port-id and/or
        calling-station-id values with the PPPoE Remote Circuit Id."
    ::= { juniRadiusClientMIBGroups 26 }                           -- JUNOSe 6.1

juniRadiusBrasClientGroup11  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo }
    STATUS      obsolete
    DESCRIPTION
        "Obsolete collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 27 }                           -- JUNOSe 6.1

juniRadiusBrasClientGroup12  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 28 }                           -- JUNOSe 7.0

juniRadiusBrasClientGroup13  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 29 }                           -- JUNOSe 7.2

juniRadiusBrasClientGroup14  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop,
        juniRadiusClientIncludeDslForumAttributesInAccessReq,
        juniRadiusClientIncludeDslForumAttributesInAcctStart,
        juniRadiusClientIncludeDslForumAttributesInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 30 }                           -- JUNOSe 7.3

juniRadiusBrasClientGroup15  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop,
        juniRadiusClientIncludeDslForumAttributesInAccessReq,
        juniRadiusClientIncludeDslForumAttributesInAcctStart,
	juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cDslLineStateInAccessReq,
        juniRadiusClientIncludeL2cDslTypeInAccessReq,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cDslLineStateInAcctStart,
        juniRadiusClientIncludeL2cDslTypeInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cDslLineStateInAcctStop,
        juniRadiusClientIncludeL2cDslTypeInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 31 }                           -- JUNOSe 8.1

juniRadiusBrasClientGroup16  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop,
        juniRadiusClientIncludeDslForumAttributesInAccessReq,
        juniRadiusClientIncludeDslForumAttributesInAcctStart,
	juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cDslLineStateInAccessReq,
        juniRadiusClientIncludeL2cDslTypeInAccessReq,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cDslLineStateInAcctStart,
        juniRadiusClientIncludeL2cDslTypeInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cDslLineStateInAcctStop,
        juniRadiusClientIncludeL2cDslTypeInAcctStop,
        juniRadiusClientIncludeInterfaceIdInAcctStart,
        juniRadiusClientIncludeIpv6PrefixInAcctStart,
        juniRadiusClientIncludeInterfaceIdInAcctStop,
        juniRadiusClientIncludeIpAddrInAcctStop,
        juniRadiusClientIncludeIpv6PrefixInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 32 }                           -- JUNOSe 8.2

juniRadiusBrasClientGroup17  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop,
        juniRadiusClientIncludeDslForumAttributesInAccessReq,
        juniRadiusClientIncludeDslForumAttributesInAcctStart,
    	juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cDslLineStateInAccessReq,
        juniRadiusClientIncludeL2cDslTypeInAccessReq,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cDslLineStateInAcctStart,
        juniRadiusClientIncludeL2cDslTypeInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cDslLineStateInAcctStop,
        juniRadiusClientIncludeL2cDslTypeInAcctStop,
        juniRadiusClientIncludeInterfaceIdInAcctStart,
        juniRadiusClientIncludeIpv6PrefixInAcctStart,
        juniRadiusClientIncludeInterfaceIdInAcctStop,
        juniRadiusClientIncludeIpAddrInAcctStop,
        juniRadiusClientIncludeIpv6PrefixInAcctStop,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 33 }                           -- JUNOSe 9.1

juniRadiusBrasClientGroup18  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop,
        juniRadiusClientIncludeDslForumAttributesInAccessReq,
        juniRadiusClientIncludeDslForumAttributesInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cDslLineStateInAccessReq,
        juniRadiusClientIncludeL2cDslTypeInAccessReq,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cDslLineStateInAcctStart,
        juniRadiusClientIncludeL2cDslTypeInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cDslLineStateInAcctStop,
        juniRadiusClientIncludeL2cDslTypeInAcctStop,
        juniRadiusClientIncludeInterfaceIdInAcctStart,
        juniRadiusClientIncludeIpv6PrefixInAcctStart,
        juniRadiusClientIncludeInterfaceIdInAcctStop,
        juniRadiusClientIncludeIpAddrInAcctStop,
        juniRadiusClientIncludeIpv6PrefixInAcctStop,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIgnorePppoeMaxSession }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 34 }                           -- JUNOSe 9.3

juniRadiusBrasClientGroup19  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop,
        juniRadiusClientIncludeDslForumAttributesInAccessReq,
        juniRadiusClientIncludeDslForumAttributesInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cDslLineStateInAccessReq,
        juniRadiusClientIncludeL2cDslTypeInAccessReq,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cDslLineStateInAcctStart,
        juniRadiusClientIncludeL2cDslTypeInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cDslLineStateInAcctStop,
        juniRadiusClientIncludeL2cDslTypeInAcctStop,
        juniRadiusClientIncludeInterfaceIdInAcctStart,
        juniRadiusClientIncludeIpv6PrefixInAcctStart,
        juniRadiusClientIncludeInterfaceIdInAcctStop,
        juniRadiusClientIncludeIpAddrInAcctStop,
        juniRadiusClientIncludeIpv6PrefixInAcctStop,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIgnorePppoeMaxSession,
        juniRadiusClientIncludeIpv6AccountingInAcctStop }
    STATUS      obsolete
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 35 }                           -- JUNOSe 10.2

juniRadiusBrasClientGroup20  OBJECT-GROUP
    OBJECTS {
        juniRadiusClientDslPortType,
        juniRadiusClientAcctSessionIdFormat,
        juniRadiusClientNasPortFormat,
        juniRadiusClientCallingStationDelimiter,
        juniRadiusClientEthernetPortType,
        juniRadiusClientIncludeIpAddrInAcctStart,
        juniRadiusClientIncludeAcctSessionIdInAccessReq,
        juniRadiusClientCallingStationIdFormat,
        juniRadiusClientNasIpAddrUse,
        juniRadiusClientIncludeAcctTunnelConnectionInAccessReq,
        juniRadiusClientIncludeCalledStationIdInAccessReq,
        juniRadiusClientIncludeCallingStationIdInAccessReq,
        juniRadiusClientIncludeConnectInfoInAccessReq,
        juniRadiusClientIncludeNasIdentifierInAccessReq,
        juniRadiusClientIncludeNasPortInAccessReq,
        juniRadiusClientIncludeNasPortIdInAccessReq,
        juniRadiusClientIncludeNasPortTypeInAccessReq,
        juniRadiusClientIncludePppoeDescriptionInAccessReq,
        juniRadiusClientIncludeTunnelClientAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelClientEndpointInAccessReq,
        juniRadiusClientIncludeTunnelMediumTypeInAccessReq,
        juniRadiusClientIncludeTunnelServerAttributesInAccessReq,
        juniRadiusClientIncludeTunnelServerAuthIdInAccessReq,
        juniRadiusClientIncludeTunnelServerEndpointInAccessReq,
        juniRadiusClientIncludeTunnelTypeInAccessReq,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStart,
        juniRadiusClientIncludeCalledStationIdInAcctStart,
        juniRadiusClientIncludeCallingStationIdInAcctStart,
        juniRadiusClientIncludeClassInAcctStart,
        juniRadiusClientIncludeConnectInfoInAcctStart,
        juniRadiusClientIncludeEgressPolicyNameInAcctStart,
        juniRadiusClientIncludeEventTimestampInAcctStart,
        juniRadiusClientIncludeFramedCompressionInAcctStart,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStart,
        juniRadiusClientIncludeIngressPolicyNameInAcctStart,
        juniRadiusClientIncludeNasIdentifierInAcctStart,
        juniRadiusClientIncludeNasPortInAcctStart,
        juniRadiusClientIncludeNasPortIdInAcctStart,
        juniRadiusClientIncludeNasPortTypeInAcctStart,
        juniRadiusClientIncludePppoeDescriptionInAcctStart,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStart,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStart,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStart,
        juniRadiusClientIncludeTunnelPreferenceInAcctStart,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStart,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStart,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStart,
        juniRadiusClientIncludeTunnelTypeInAcctStart,
        juniRadiusClientIncludeAcctTunnelConnectionInAcctStop,
        juniRadiusClientIncludeCalledStationIdInAcctStop,
        juniRadiusClientIncludeCallingStationIdInAcctStop,
        juniRadiusClientIncludeClassInAcctStop,
        juniRadiusClientIncludeConnectInfoInAcctStop,
        juniRadiusClientIncludeEgressPolicyNameInAcctStop,
        juniRadiusClientIncludeEventTimestampInAcctStop,
        juniRadiusClientIncludeFramedCompressionInAcctStop,
        juniRadiusClientIncludeFramedIpNetmaskInAcctStop,
        juniRadiusClientIncludeIngressPolicyNameInAcctStop,
        juniRadiusClientIncludeInputGigawordsInAcctStop,
        juniRadiusClientIncludeNasIdentifierInAcctStop,
        juniRadiusClientIncludeNasPortInAcctStop,
        juniRadiusClientIncludeNasPortIdInAcctStop,
        juniRadiusClientIncludeNasPortTypeInAcctStop,
        juniRadiusClientIncludeOutputGigawordsInAcctStop,
        juniRadiusClientIncludePppoeDescriptionInAcctStop,
        juniRadiusClientIncludeTunnelAssignmentIdInAcctStop,
        juniRadiusClientIncludeTunnelClientAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelClientEndpointInAcctStop,
        juniRadiusClientIncludeTunnelMediumTypeInAcctStop,
        juniRadiusClientIncludeTunnelPreferenceInAcctStop,
        juniRadiusClientIncludeTunnelServerAttributesInAcctStop,
        juniRadiusClientIncludeTunnelServerAuthIdInAcctStop,
        juniRadiusClientIncludeTunnelServerEndpointInAcctStop,
        juniRadiusClientIncludeTunnelTypeInAcctStop,
        juniRadiusClientIncludeInputGigapktsInAcctStop,
        juniRadiusClientIncludeOutputGigapktsInAcctStop,
        juniRadiusClientIgnoreFramedIpNetmask,
        juniRadiusClientIgnoreAtmCategory,
        juniRadiusClientIgnoreAtmMbs,
        juniRadiusClientIgnoreAtmPcr,
        juniRadiusClientIgnoreAtmScr,
        juniRadiusClientIgnoreEgressPolicyName,
        juniRadiusClientIgnoreIngressPolicyName,
        juniRadiusClientIgnoreVirtualRouter,
        juniRadiusClientTrapOnAuthServerUnavailable,
        juniRadiusClientTrapOnAcctServerUnavailable,
        juniRadiusClientTrapOnNoAuthServerAvailable,
        juniRadiusClientTrapOnNoAcctServerAvailable,
        juniRadiusClientTrapOnAuthServerAvailable,
        juniRadiusClientTrapOnAcctServerAvailable,
        juniRadiusClientPppoeNasPortFormat,
        juniRadiusClientIncludeTunnelInterfaceIdInAccessReq,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStart,
        juniRadiusClientIncludeTunnelInterfaceIdInAcctStop,
        juniRadiusClientIncludeL2tpPppDisconnectCauseInAcctStop,
        juniRadiusClientVlanNasPortFormat,
        juniRadiusClientIncludeAcctMultiSessionIdInAccessReq,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStart,
        juniRadiusClientIncludeAcctMultiSessionIdInAcctStop,
        juniRadiusClientIncludeAscendNumInMultilinkInAccessReq,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStart,
        juniRadiusClientIncludeAscendNumInMultilinkInAcctStop,
        juniRadiusClientConnectInfoFormat,
        juniRadiusClientIncludeProfileServiceDescrInAccessReq,
        juniRadiusClientIncludeProfileServiceDescrInAcctStart,
        juniRadiusClientIncludeProfileServiceDescrInAcctStop,
        juniRadiusClientIncludeAcctAuthenticInAcctOn,
        juniRadiusClientIncludeAcctDelayTimeInAcctOn,
        juniRadiusClientIncludeAcctSessionIdInAcctOn,
        juniRadiusClientIncludeAcctAuthenticInAcctOff,
        juniRadiusClientIncludeAcctDelayTimeInAcctOff,
        juniRadiusClientIncludeAcctSessionIdInAcctOff,
        juniRadiusClientIncludeAcctTerminateCauseInAcctOff,
        juniRadiusClientIncludeMlpppBundleNameInAccessReq,
        juniRadiusClientIncludeMlpppBundleNameInAcctStart,
        juniRadiusClientIncludeMlpppBundleNameInAcctStop,
        juniRadiusClientIncludeDhcpOptionsInAccessReq,
        juniRadiusClientIncludeDhcpMacAddressInAccessReq,
        juniRadiusClientIncludeDhcpGiAddressInAccessReq,
        juniRadiusClientIncludeDhcpOptionsInAcctStart,
        juniRadiusClientIncludeDhcpMacAddressInAcctStart,
        juniRadiusClientIncludeDhcpGiAddressInAcctStart,
        juniRadiusClientIncludeDhcpOptionsInAcctStop,
        juniRadiusClientIncludeDhcpMacAddressInAcctStop,
        juniRadiusClientIncludeDhcpGiAddressInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientOverrideNasInfo,
        juniRadiusClientIncludeInterfaceDescriptionInAccessReq,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStart,
        juniRadiusClientIncludeInterfaceDescriptionInAcctStop,
        juniRadiusClientNasPortIdOverrideRemoteCircuitId,
        juniRadiusClientCallingStationIdOverrideRemoteCircuitId,
        juniRadiusClientNasPortFieldWidthAtmSlot,
        juniRadiusClientNasPortFieldWidthAtmAdapter,
        juniRadiusClientNasPortFieldWidthAtmPort,
        juniRadiusClientNasPortFieldWidthAtmVpi,
        juniRadiusClientNasPortFieldWidthAtmVci,
        juniRadiusClientNasPortFieldWidthEthernetSlot,
        juniRadiusClientNasPortFieldWidthEthernetAdapter,
        juniRadiusClientNasPortFieldWidthEthernetPort,
        juniRadiusClientNasPortFieldWidthEthernetSVlan,
        juniRadiusClientNasPortFieldWidthEthernetVlan,
        juniRadiusClientRemoteCircuitIdFormat,
        juniRadiusClientRemoteCircuitIdDelimiter,
        juniRadiusClientIncludeL2cAccessLoopParametersInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAccessReq,
        juniRadiusClientIncludeL2cUpStreamDataInAccessReq,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStart,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStart,
        juniRadiusClientIncludeL2cDownStreamDataInAcctStop,
        juniRadiusClientIncludeL2cUpStreamDataInAcctStop,
        juniRadiusClientIncludeDslForumAttributesInAccessReq,
        juniRadiusClientIncludeDslForumAttributesInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAccessReq,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cActualDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAccessReq,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAccessReq,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAccessReq,
        juniRadiusClientIncludeL2cDslLineStateInAccessReq,
        juniRadiusClientIncludeL2cDslTypeInAccessReq,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStart,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStart,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStart,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStart,
        juniRadiusClientIncludeL2cDslLineStateInAcctStart,
        juniRadiusClientIncludeL2cDslTypeInAcctStart,
        juniRadiusClientIncludeL2cAccessLoopCircuitIdInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdBinaryInAcctStop,
        juniRadiusClientIncludeL2cAccessAggrCircuitIdAsciiInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cActualDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinimumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cAttainDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMaximumDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateUstrInAcctStop,
        juniRadiusClientIncludeL2cMinLowPowerDataRateDstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayUstrInAcctStop,
        juniRadiusClientIncludeL2cMaxInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cActInterleavingDelayDstrInAcctStop,
        juniRadiusClientIncludeL2cDslLineStateInAcctStop,
        juniRadiusClientIncludeL2cDslTypeInAcctStop,
        juniRadiusClientIncludeInterfaceIdInAcctStart,
        juniRadiusClientIncludeIpv6PrefixInAcctStart,
        juniRadiusClientIncludeInterfaceIdInAcctStop,
        juniRadiusClientIncludeIpAddrInAcctStop,
        juniRadiusClientIncludeIpv6PrefixInAcctStop,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAccessReq,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStart,
        juniRadiusClientIncludeDownStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIncludeUpStreamCalculatedQosRateInAcctStop,
        juniRadiusClientIgnorePppoeMaxSession,
        juniRadiusClientIncludeIpv6AccountingInAcctStop,
        juniRadiusClientIncludeDelegatedIpv6PrefixInAcctStart,
        juniRadiusClientIncludeDelegatedIpv6PrefixInAcctStop,
        juniRadiusClientIncludeFramedIpv6PoolInAcctStart,
        juniRadiusClientIncludeFramedIpv6PoolInAcctStop,
        juniRadiusClientIncludeFramedIpv6RouteInAcctStart,
        juniRadiusClientIncludeFramedIpv6RouteInAcctStop,
        juniRadiusClientIncludeIpv6LocalInterfaceInAcctStart,
        juniRadiusClientIncludeIpv6LocalInterfaceInAcctStop,
        juniRadiusClientIncludeIpv6NdRaPrefixInAcctStart,
        juniRadiusClientIncludeIpv6NdRaPrefixInAcctStop,
        juniRadiusClientIncludeIpv6PrimaryDnsInAcctStart,
        juniRadiusClientIncludeIpv6PrimaryDnsInAcctStop,
        juniRadiusClientIncludeIpv6SecondaryDnsInAcctStart,
        juniRadiusClientIncludeIpv6SecondaryDnsInAcctStop,
        juniRadiusClientIncludeIpv6VirtualRouterInAcctStart,
        juniRadiusClientIncludeIpv6VirtualRouterInAcctStop}
    STATUS      current
    DESCRIPTION
        "A collection of objects providing management of general B-RAS functions
        for RADIUS Clients."
    ::= { juniRadiusClientMIBGroups 36 }                           -- JUNOSe 10.2
END
