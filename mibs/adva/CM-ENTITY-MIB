CM-ENTITY-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-COMPLIANCE, OBJECT-GROUP 
             FROM SNMPv2-CONF
    MODULE-IDENTITY, OBJECT-TYPE, <PERSON>teger32, <PERSON><PERSON><PERSON><PERSON><PERSON>
             FROM SNMPv2-SMI
    DisplayString, TruthValue, RowStatus, StorageType, VariablePointer, 
    DateAndTime, TEXTUAL-CONVENTION
             FROM SNMPv2-TC
    AdminState, OperationalState, SecondaryState, RestartType
             FROM  CM-COMMON-MIB
    fsp150cm
             FROM  ADVA-MIB 
    VlanId, CmPmIntervalType, TDMFrequencySourceType ,
    UsbOperationalMode
             FROM  CM-COMMON-MIB
    PhysicalIndex 
             FROM ENTITY-MIB
    CmAutoProvMode 
             FROM CM-SYSTEM-MIB;

cmEntityMIB MODULE-IDENTITY
    LAST-UPDATED    "202101270000Z"
    ORGANIZATION    "ADVA Optical Networking SE"
    CONTACT-INFO
        "Web URL: http://adva.com/
        E-mail:  <EMAIL>
        Postal:  ADVA Optical Networking SE
             Campus Martinsried
             Fraunhoferstrasse 9a
             82152 Martinsried/Munich
             Germany
        Phone: +49 089 89 06 65 0
        Fax:  +49 089 89 06 65 199 "
    DESCRIPTION
            "This module defines the Entity MIB definitions used by 
             the F3 (FSP150CM/CC) product lines.  These definitions are 
             vendor specific extensions to the standard ENTITY MIB (RFC2737). 
             Copyright (C) ADVA."
    REVISION        "202101270000Z"
    DESCRIPTION
            "  
               Notes from release 202002170000Z
               (1) Added new table f3StorageDeviceTable

               Notes from release 202002060000Z
               (1) Added new attribute to serverCardTable
               
               Notes from release 201911050000Z
               (1) Added new literal CompositeClock to CardType
               (2) New compositeClockCardTable, with columns:
                    compositeClockCardEntityIndex, 
                    compositeClockCardAdminState,
                    compositeClockCardOperationalState, 
                    compositeClockCardSecondaryState,
                    compositeClockCardRowStatus,    
                    compositeClockCardAlias
               (3) New Conformance Group: compositeClockCardGroup 

               Notes from release 201909150000Z
               (1) Added new literal MbGnss to CardType
               (2) New mbGnssCardTable, with columns:
                    mbGnssCardEntityIndex, 
                    mbGnssCardAdminState,
                    mbGnssCardOperationalState, 
                    mbGnssCardSecondaryState,
                    mbGnssCardRowStatus,    
                    mbGnssCardAlias
               (3) New Conformance Group: mbGnssCardGroup 

               Notes from release 201909130000Z
               (1) Added new literal irig to CardType
               (2) New f3IrigCardTable, with columns:
                  f3IrigCardEntityIndex, f3IrigCardAlias,
                  f3IrigCardAdminState, ff3IrigCardOperationalState,
                  f3IrigCardSecondaryState, f3IrigCardTemperature,
                  f3IrigCardStorageType, f3IrigCardRowStatus
               (3) New Conformance Group: f3IrigCardGroup 
	       
               Notes from release 201903070000Z
               (1) Added new literal eth-xg-118proac-sh to CardType
               (2) Added new literal onerackunit-xg118proacSH to ShelfType
               (3) Added new literal eth-xg-118proac-sh to CardType
               (4) Added ethernetNTEXG118PROACSHCardTable
               (5) Added nteXg118ProacSHCardGroup

               Notes from release 201901300000Z
               (1) Added auxPortCardTemperature column
                   in auxPortCardTable entry
               (2) Added ge4PortCardTemperature column
                   in ge4PortCardTable entry
               (3) Added bits16PortCardTemperature column
                   in bits16PortCardTable entry
           
               Notes from release 201901070000Z
               (1) Added ethernetNTEOSA5401CardTableRestartAction 
               (2) Added ethernetNTEOSA5405CardTableRestartAction

               Notes from release 201809240000Z
               (1) Added new literal ccxg118proSH to NetworkElementType
               (2) Added new literal onerackunit-xg118proSH to ShelfType
               (3) Added new literal eth-xg-118pro-sh to CardType
               (4) Added ethernetNTEXG118PROSHCardTable
               (5) Added nteXg118ProSHCardGroup
               
               Notes from release 201804190000Z
               (1) Added new literal ccxg116proH to NetworkElementType
               (2) Added new literal onerackunit-xg116proH to ShelfType
               (3) Added new literal eth-xg-116pro-h to CardType
               (4) Added ethernetNTEXG116PROHCardTable
               (5) Added nteXg116ProHCardGroup

               Notes from release 201802210000Z
               (1) Added table: ethernetOsa3350MgntCardTable
               (2) New textual convention: ResyncType

               Notes from release 201802020000Z
               (1) Added ethernetNTEGE102ProHCardTable and ethernetNTEGE102ProEFMHCardTable
               (2) Added new literals to NetworkElementType, ShelfType and CardType.
               
               Notes from release 201709050000Z
               (1) add auxPortCardTable

               Notes from release 201708220000Z
               (1) add ccosa5430, ccosa5440 to NetworkElementType
               (2) add onerackunit-osa5430, threerackunit-osa5440 to ShelfType
               (3) add ethernetCSMCardTable

               Notes from release 201705290000Z
               (1) add osa5401, osa5405 to ShelfType
               (2) add eth-osa5401, eth-osa5405 to CardType
               (3) add ethernetNTEOSA5401CardTable
               (4) add ethernetNTEOSA5405CardTable

               Notes from release 201603170000Z
               (1) add ethGe112ProVm to CardType
               (2) add ethGe112ProVmTable

               Notes from release 201603170000Z
               (1) add ethGe114ProVmH, ethGe114ProVmCH, ethGe114ProVmCSH, serverCard to CardType
               (2) add serverCardTable

               Notes from release 201603060000Z
               (1) add bits-x16 bits-x16 to CardType
               (2) add bits16PortCardTable

           Notes from release 20150519000Z
               1) change the OID of ethernetNTEXG210CCardTable to 55 
               2) change the OID of ethernetGE8SCryptoConnectorCardTable to 56

               Notes from release 201502040000Z
               (1) add ccxg210c to NetworkElementType
               (2) add eth-ge-8sc-cc to CardType
               (3) add onerackunit-xg210c to ShelfType
               (4) add ethernetNTEXG210CardTable
               (5) add ethernetGE8SCryptoConnectorCardTable

             Notes from release 201501270000Z,
             (1) Added new NetworkElementType literal:
                 - ccosa5411.

             (2) Added new ShelfType literal:
                 - onerackunit-osa5411.

             (3) Added new CardType literal:
                 - eth-osa5411.

             Notes from release 201407180000Z,
             (1) Added new NetworkElementType literal:
                 - sh1pcs.

             (2) Added new ShelfType literal:
                 - onerackunit-sh1pcs.

             (3) Added new CardType literal:
                 - eth-sh1pcs.

             Notes from release 201209180000Z,
             (1) New table ethernetGE8SCCCardTable  

             Notes from release 201207190000Z,
             (1) New textual convention: PSNEncapsulationMode
             (2) New pseudoWireOcnStmCardTable object: pseudoWireOcnStmCardPSNEncapsulation
             (3) New pseudoWireE1T1CardTable object: pseudoWireE1T1CardPSNEncapsulation

             Notes from release 201201090000Z
             (1)Added the following new objects,
                  scuFlashModelNum, scuFlashFirmwareRev, scuFlashSerialNum, 
                  nemiFlashModelNum, nemiFlashFirmwareRev, nemiFlashSerialNum

             Notes from release 201002120000Z
             (1)Updated NetworkElementType TC with ccge201 and ccge201se
             (2)Updated ShelfType TC with onerackunit-ge201, onerackunit-ge201se
             (3)Updated CardType TC with eth-ge-201, eth-ge-201se, 
                   eth-10-100-1000-nte and scu-t
             (4)New tables for scuTTable, ethernetNTECardTable, 
                ethernetNTEGE201CardTable and ethernetNTEGE201SyncECardTable  
             (5)Added lag as SlotType

             Notes from release 200903160000Z
             This release is applicable to the FSP150CC Release 4.1 
             devices GE101 and GE206.
             (1)Textual Convention NetworkElementType is updated 
                with additional enumeration literals, 
                  ccge101, ccge206 
             (2)Textual Convention ShelfType is updated 
                with additional enumeration literals, 
                  onerackunit-ge101, onerackunit-ge206 
             (3)Textual Convention CardType is updated 
                with additional enumeration literals, 
                  eth-ge-101, eth-ge-206
             (4)ethernetNTEGE101CardTable is the new table that 
                  represents the GE101. 
             (5)ethernetNTEGE206CardTable is the new table that 
                  represents the GE206. 
             (6)pseudoWireE3CardTable is the new table that 
                  represents the Pseudo Wire E3 card.
             (7)slotTable has new attribute slotCardPhysicalAddress 
             (8)ethernet1x10GCardTable is the new table that 
                  represents the 1x10GCard.
             (9)ethernet10x1GCardTable is the new table that 
                  represents the 10x1GCard.
             (10)ethernetSWFCardTable is the new table that 
                  represents the SWF Card.
                  
             (11)stuTable is the new table that 
                  represents the stu Card.
             (12)amiTable is the new table that 
                  represents the ami Card.
             (13)stiTable is the new table that 
                  represents the sti Card.
                            
             Notes from release 200803030000Z,
             (1)MIB version ready for release FSP150CM 3.1." 
    ::= {fsp150cm 3}    

-- 
-- OID definitions
-- 
cmEntityObjects      OBJECT IDENTIFIER ::= {cmEntityMIB 1}
cmEntityConformance  OBJECT IDENTIFIER ::= {cmEntityMIB 2}

-- 
-- Textual Conventions 
-- 
NeProvAction ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Network Element Action to accept or decline the Network Element."
    SYNTAX       INTEGER {
                   accept (1),
                   decline(2)
                 }

NetworkElementType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Network Element Type."
    SYNTAX       INTEGER {
                  onerackunit (1),
                  hubshelf (2),
                  cle (3),
                  aggregation (4),
                  cpmr (5),
                  --
                  ccge101(6),
                  ccge206(7),
                  ccge201(8), -- GE201
                  ccge201se(9), -- GE201 with SyncE
                  --
                  ccge206f(10), -- GE206 with 6 SFP capable Access Ports
                  ccge112(11),
                  ccge114(12),
                  --
                  ccge206v(13), -- GE206 with 6 SFP capable Access Ports and 2 
                               --     auxiliary slots
                  ccxg210(14),  -- XG210 with 2 auxiliary slots

                  --
                  cct1804(15),
                  cct3204(16),

                  --
                  ccsyncprobe(17),  -- SyncProbe 
                  ccge114h(18),
                  ccge114ph(19),
                  ccge114sh(20),
                  ccge114s(21),
                  sh1pcs(22),
                  ccosa5411(23),  -- OSA5411 
                  ccge112pro(24),
                  ccge112proM(25),
                  ccge114pro(26),
                  ccge114proC(27),
                  ccge114proSH(28),
                  ccge114proCSH(29),
                  ccge114proHE(30),
                  ccge112proH(31),
                  ccxg210c(32),
                  ccosa5420(33),  -- OSA5420
                  ccosa5421(34),   -- OSA5421         
                  ccge114g(35),
                  ccge114proVmH(36),
                  ccge114proVmCH(37),
                  ccge114proVmCSH(38),
                  ccge101pro(39),
                  ccgo102ProS(40),
                  ccgo102ProSP(41),
                  cccx101Pro30A(42),
                  cccx102Pro30A(43),
                  ccxg116pro(44),
                  ccxg120pro(45),
                  ccge112proVm(46),
                  ccosa5430(47),  -- OSA5430
                  ccosa5440(48),   -- OSA5440
                  ge102proh(49),
                  ge102proefmh(50),
                  ccxg116proH(51),
                  ccgo102ProSM(52),
                  ccxg118proSH(53),
                  ccxg118proacSH(54),
                  ccge114proVmSH(55),
                  ccge104(56),
                  ccxg120proSH(57)
                 }

SlotType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Slot Type."
    SYNTAX       INTEGER {
                  scu (1),
                  psu (2),
                  fan (3),
                  generic (4),
                  lag (5),
                  nemi (6),
                  stu (7),
                  swf-140g (8),
                  ami (9),
                  sti (10)
                 }

ShelfType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Shelf Type."
    SYNTAX       INTEGER {
                  onerackunit (1),
                  hubshelf (2),
                  cle (3),
                  aggregation (4),
                  cpmr (5),
                  --
                  onerackunit-ge101(6),
                  onerackunit-ge206(7),
                  onerackunit-ge201(8),
                  onerackunit-ge201se(9),
                  --
                  onerackunit-ge206f(10),
                  --
                  onerackunit-ge112(11),
                  onerackunit-ge114(12),
                  --
                  onerackunit-ge206v(13),
                  onerackunit-xg210(14),

                  --
                  onerackunit-t1804(15),
                  onerackunit-t3204(16),

                  --
                  onerackunit-syncprobe(17),
                  onerackunit-ge114h(18),
                  onerackunit-ge114ph(19),
                  onerackunit-ge114sh(20),
                  onerackunit-ge114s(21),
                  onerackunit-sh1pcs(22),
                  onerackunit-osa5411(23),
                  onerackunitGe112Pro(24),
                  ge112ProM(25),
                  onerackunitGe114Pro(26),
                  onerackunitGe114ProC(27),
                  onerackunitGe114ProSH(28),
                  onerackunitGe114ProCSH(29),
                  onerackunitGe114ProHE(30),
                  onerackunitGe112ProH(31),
                  onerackunit-xg210c(32),
                  onerackunit-osa5420(33),
                  onerackunit-osa5421(34),
                  onerackunit-ge114g(35),
                  onerackunitGe114ProVmH(36),
                  onerackunitGe114ProVmCH(37),
                  onerackunitGe114ProVmCSH(38),
                  ge101pro(39),
                  go102proS(40),
                  go102proSP(41),
                  onerackunit-cx101pro30A(42),
                  onerackunit-cx102pro30A(43),
                  onerackunit-xg116pro(44),
                  onerackunit-xg120pro(45),
                  onerackunitGe112ProVm(46),
                  osa5401(47),
                  osa5405(48),
                  onerackunit-osa5430(49),
                  threerackunit-osa5440(50),
                  ge102proh(51),
                  ge102proefmh(52),
                  onerackunit-xg116proH(53),
                  go102proSM(54),
                  onerackunit-xg118proSH(55),
                  onerackunit-xg118proacSH(56),
                  onerackunitGe114ProVmSH(57),
                  onerackunitGe104(58),
                  onerackunit-xg120proSH(59)
                 }

ShelfAction ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Shelf Action."
    SYNTAX       INTEGER {
                  initiateLampTest (1),
                  initiateACO(2),
                  coldRestart(3)
                 }

PsuType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Power Supply Type."
    SYNTAX       INTEGER {
                  unknown (0),
                  ac (1),
                  dc (2)
                 }

CardType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Card Type."
    SYNTAX       INTEGER {
                  none (1),
                  psu (2),  -- Power Supply
                  fan (3),  -- FAN 
                  nemi (4), -- NEMI Card on Hub Shelf
                  scu  (5), -- SCU Card on Hub Shelf
                  eth-10-100-1000-ntu (6), -- GigE NTU Card on Hub Shelf
                  eth-cpmr (7),
                  --
                  eth-ge-101(8),  -- GE101 NID
                  eth-ge-206(9),  -- GE206 NID
                  eth-ge-201(10), -- GE201 NID
                  eth-ge-201se(11), -- GE201 NID with SyncE
                  eth-10-100-1000-nte(12), -- GigE NTE Card on Hub Shelf
                  scu-t(13), -- SCU-T Card on Hub Shelf
                  --
                  eth-ge-206f(14),
                  
                  --added for cm5.1
                  eth-xg-1x(15),
                  swf-140g(16),
                  stu(17),
                  eth-ge-10s(18),
                  ami(19),
                  sti(20),

                  eth-ge-112(21),
                  eth-ge-114(22),
                  -- 
                  eth-ge-206v(23),
                  eth-ge-4e-cc(24),
                  eth-ge-4s-cc(25),
                  
                  eth-xg-210(26),
                  eth-xg-1x-cc(27),
                  eth-xg-1s-cc(28),

                  --
                  stm1-4-et(29),
                  --
                  pwe3-ocnstm(30),   -- PWE3 OCN STM card
                  pwe3-e1t1(31),     -- PWE3 E1 T1 card
                  eth-xg-1x-h(32),
                  eth-ge-10s-h(33),

                  --
                  eth-t1804(34),
                  eth-t3204(35),

                  --
                  eth-ge-syncprobe(36),
                  eth-ge-8s-cc(37),
                  eth-ge-114h(38),
                  eth-ge-114ph(39),
                  eth-fe-36e(40),
                  eth-ge-114sh(41),
                  eth-ge-114s(42),
                  sti-h(43),
                  stu-h(44),
                  eth-ge-8e-cc(45),
                  eth-sh1pcs(46),
                  eth-osa5411(47),
                  ethGe112Pro(48),
                  ethGe112ProM(49),
                  ethGe114Pro(50),
                  ethGe114ProC(51),
                  ethGe114ProSH(52),
                  ethGe114ProCSH(53),
                  ethGe114ProHE(54),
                  ethGe112ProH(55),
                  eth-xg-210c(56),
                  eth-ge-8sc-cc(57),
                  eth-osa5420(58),
                  eth-osa5421(59),
                  bits-x16(60),
                  eth-ge-114g(61),
                  ethGe114ProVmH(62),
                  ethGe114ProVmCH(63),
                  ethGe114ProVmCSH(64),
                  serverCard(65),
                  eth-ptpv2-osa(66),
                  gnss-osa(67),
                  thc-osa(68),
                  sgc-osa(69),
                  pps-x16(70),
                  clk-x16(71),
                  todAndPps-x16(72),
                  eth-ge-101pro(73),                  
                  ethgo102proS(74),
                  ethgo102proSP(75),
                  ethcx101pro30A(76),
                  ethcx102pro30A(77),
                  osa-ge-4s(78),
                  eth-xg-116pro(79),
                  eth-xg-120pro(80),
                  ethGe112ProVm(81),
                  eth-osa5401(82),
                  eth-osa5405(83),
                  eth-csm(84),
                  aux-osa(85),            
                  bits-x16-enhanced(86),  
                  osa-ge-4s-protected(87),
                  eth-ge-102pro-h(88),    
                  eth-ge-102pro-efmh(89), 
                  eth-xg-116pro-h(90),    
                  ethgo102proSM(91),      
                  eth-xg-118pro-sh(92),  
		  eth-xg-118proac-sh(93),
                  ethGe114ProVmSH(94),
                  ethGe104(95),
                  eth-xg-120pro-sh(96),
                  irig(97),                   
                  mb-gnss(98),
                  composite-clock(99)                   
                 }                        
                                          
CmCPMRLinkLossFwdMode ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "Link Loss Forwarding Mode on the CPMR."
    SYNTAX     INTEGER {
                 llfmode-none(1),
                 llfmode-acc2acc(2),
                 llfmode-net2acc(3),
                 llfmode-both(4)
               }

PWE3OCNSTMCardMode ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "OCN/STM Card Mode."
    SYNTAX  INTEGER {
                stm4(1),
                oc12(2),
                stm1(3),
                oc3(4)
            }

PWE3E1T1CardMode ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "OCN/STM Card Mode."
    SYNTAX  INTEGER {
                t1-16(1),
                e1-16(2)
            }

PSNEncapsulationMode ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "OCN/STM Card Mode."
    SYNTAX  INTEGER {
                ethernet(1),
                mpls(2)
            }

LLDPEnableAction ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "Enable (TxAndRx, Tx-Only, Rx-Only) LLDP or Disable LLDP."
    SYNTAX  INTEGER {
                notApplicable(0),
                enableLLDP(1),       -- set the admin status as TxAndRx
                disableLLDP(2),      -- set the admin status as disabled
                enableLLDPTxOnly(3), -- set the admin status as Tx Only
                enableLLDPRxOnly(4)  -- set the admin status as Rx Only
            }

LedControlType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "."
    SYNTAX  INTEGER {
                notApplicable(0),
                normal(1),
                status-led-only(2),
                all-disabled(3)
            }

ResyncType ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Enumerations for Resync Type."
    SYNTAX INTEGER    {
        no-action(1),
        resync-to-pps(2)
        }

StorageStatus ::= TEXTUAL-CONVENTION
    STATUS         current
    DESCRIPTION    "Enumerations for Storage Status."
    SYNTAX INTEGER    {
        formatting(1),
        empty(2),
        ready(3),
        unformatted(4),
        unmounted(5)
        }
--
--NetworkElement
--
networkElementTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF NetworkElementEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Entries may be auto discovered, or can be explicitly created by 
          SNMP Manager.  Each remotely discovered shelf is represented as 
          a row in this table." 
    ::= { cmEntityObjects 1 }

networkElementEntry  OBJECT-TYPE
    SYNTAX      NetworkElementEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the networkElementTable."
    INDEX { neIndex }
    ::= { networkElementTable 1 }

NetworkElementEntry ::= SEQUENCE {
    neIndex              Integer32,
    neName               DisplayString,
    neType               NetworkElementType,
    neContact            DisplayString,
    neLocation           DisplayString,
    neDescription        DisplayString,
    neCmdPromptPrefix    DisplayString,
    neAccepted           TruthValue,
    neFromPort           VariablePointer,
    neProvAction         NeProvAction,
    neStorageType        StorageType,
    neRowStatus          RowStatus,
    neAutoProvMode       CmAutoProvMode, 
    neFineGrainedPmInterval   CmPmIntervalType
}

neIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "An arbitrary integer index value used to uniquely identify
            a NetworkElement."
    ::= { networkElementEntry 1 }

neName OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..256))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "An administratively-assigned name for the
              Network Element.  By convention, this is the node's
              fully-qualified domain name."
    ::= { networkElementEntry 2 }

neType OBJECT-TYPE
    SYNTAX  NetworkElementType 
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Type of Network Element."
    ::= { networkElementEntry 3 }

neContact OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..256))
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
              "The textual identification of the contact person
              for the Network Element, together with information
              on how to contact this person."
    ::= { networkElementEntry 4 }

neLocation OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..256))
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
              "The physical location of this node (e.g.,
              `telephone closet, 3rd floor')."
    ::= { networkElementEntry 5 }

neDescription OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..256))
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
              "A textual description of the Network Element.  This value
              should include the full name and version
              identification of the system's hardware type,
              software operating-system, and networking
              software.  It is mandatory that this only contain
              printable ASCII characters."
    ::= { networkElementEntry 6 }

neCmdPromptPrefix OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..64))
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
         "User specified command prompt prefix, used by the CLI, 
          at the Network Element level."
    ::= { networkElementEntry 7 }

neAccepted OBJECT-TYPE
    SYNTAX  TruthValue 
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
              "Indicates whether or not the Network Element is accepted or not.
               When the system's autoProvMode(CM-SYSTEM-MIB) is 'confirm',
               NetworkElement's are discovered with this object as 'false'.
               User must invoke the 'accept' action on neProvAction object
               to accept the NetworkElement."
    ::= { networkElementEntry 8 }

neFromPort OBJECT-TYPE
    SYNTAX     VariablePointer 
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
         "The Network Element port to which this 
          Network Element is connected."
     ::= { networkElementEntry 9 }

neProvAction OBJECT-TYPE
    SYNTAX  NeProvAction 
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
          "When the system's autoProvMode(CM-SYSTEM-MIB) is 'confirm',
           NetworkElement's are discovered with 'neAccepted' 
           object as 'false'.
           User must invoke the 'accept' action on this object
           to accept the NetworkElement.  User can invoke the 'decline'
           action on this object to decline the NetworkElement."
    ::= { networkElementEntry 10 }

neStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { networkElementEntry 11 }

neRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of neRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            neRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The neRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { networkElementEntry 12 }

neAutoProvMode OBJECT-TYPE
    SYNTAX     CmAutoProvMode  
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "Autoprovisioning mode which controls automatic creation of cards plugged
             into the slots of Network Element.
             Supported values are off(1) and auto(3). When neAutoProvMode is auto then
             the cards inserted into the slots of Network Element will be automatically
             provisioned. When neAutoProvMode is off no auto-provisioning will occur."
    ::= { networkElementEntry 13 }

neFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the Network Element level.
              Value of this objec is propagated to all the cards beloning to the Network Element.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { networkElementEntry 14 }

--
--Shelf
--
shelfTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF ShelfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on shelf within the NetworkElement.
          In the case of FSP150CM, there is a single shelf for each
          NetworkElement." 
    ::= { cmEntityObjects 2 }

shelfEntry  OBJECT-TYPE
    SYNTAX      ShelfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the shelfTable."
    INDEX { neIndex, shelfIndex }
    ::= { shelfTable 1 }

ShelfEntry ::= SEQUENCE {
    shelfIndex               Integer32,
    shelfEntityIndex         PhysicalIndex,
    shelfType                ShelfType,
    shelfbackplaneRev        DisplayString,
    shelfbackplaneDOM        DateAndTime,
    shelfbackplaneSerialNo   DisplayString,
    shelfAction              ShelfAction,
    shelfAdminState          AdminState,
    shelfOperationalState    OperationalState,
    shelfSecondaryState      SecondaryState,
    shelfMfgSite             DisplayString,
    shelfOscillatorType      DisplayString,
    shelfLedControl          LedControlType
}

shelfIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "An integer index value used to uniquely identify a Shelf 
            within a NetworkElement."
    ::= { shelfEntry 1 }

shelfEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this shelf."
    ::= { shelfEntry 2 }

shelfType OBJECT-TYPE
    SYNTAX  ShelfType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Shelf Type of the shelf."
    ::= { shelfEntry 3 }

shelfbackplaneRev OBJECT-TYPE
    SYNTAX   DisplayString (SIZE (0..16))
    MAX-ACCESS   read-only
    STATUS   current
    DESCRIPTION
              "The backplane revision number."
    ::= { shelfEntry 4 }

shelfbackplaneDOM OBJECT-TYPE
    SYNTAX  DateAndTime 
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
              "Backplane Date of Manufacture."
    ::= { shelfEntry 5 }

shelfbackplaneSerialNo OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..32)) 
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
              "Backplane Serial Number."
    ::= { shelfEntry 6 }

shelfAction OBJECT-TYPE
    SYNTAX  ShelfAction 
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
          "Invoke action on the shelf, such as lamp test."
    ::= { shelfEntry 7 }

shelfAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Shelf."
    ::= { shelfEntry 8 }

shelfOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Shelf."
    ::= { shelfEntry 9 }

shelfSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Shelf."
    ::= { shelfEntry 10 }

shelfMfgSite OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..16)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the shelf's Manufacturer site."
    ::= { shelfEntry 11 }

shelfOscillatorType OBJECT-TYPE
    SYNTAX   DisplayString (SIZE (0..16))
    MAX-ACCESS   read-only
    STATUS   current
    DESCRIPTION
              "The mOscillatorType."
    ::= { shelfEntry 12 }

shelfLedControl OBJECT-TYPE
    SYNTAX   LedControlType
    MAX-ACCESS   read-write
    STATUS   current
    DESCRIPTION
              "Led Control of the shelf."
    ::= { shelfEntry 13 }

--
--Slot
--
slotTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF SlotEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on slot within the Shelf." 
    ::= { cmEntityObjects 3 }

slotEntry  OBJECT-TYPE
    SYNTAX      SlotEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the slotTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { slotTable 1 }

SlotEntry ::= SEQUENCE {
    slotIndex                Integer32,
    slotEntityIndex          PhysicalIndex,
    slotType                 SlotType,
    slotCardType             CardType,
    slotCardUnitName         DisplayString,
    slotCardFormatVersion    DisplayString,
    slotCardCLEICode         DisplayString,
    slotCardPartNumber       DisplayString,
    slotCardHwRev            DisplayString,
    slotCardSwRev            DisplayString,
    slotCardSerialNum        DisplayString,
    slotCardMfgName          DisplayString,
    slotCardMfgDate          DateAndTime,
    slotCardMfgSite          DisplayString,
    slotSecondaryState       SecondaryState,
    slotCardPhysicalAddress  DisplayString
}

slotIndex OBJECT-TYPE
    SYNTAX      Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "The actual physical slot index that uniquely identify 
            a Slot within a shelf. Slot number 255 is used to indicate system level entities."
    ::= { slotEntry 1 }

slotEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this slot."
    ::= { slotEntry 2 }

slotType OBJECT-TYPE
    SYNTAX  SlotType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Slot Type of the slot.  'generic' slots can hold NEMI card
              as well as line cards."
    ::= { slotEntry 3 }

slotCardType OBJECT-TYPE
    SYNTAX  CardType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Card Type of the slot."
    ::= { slotEntry 4 }

slotCardUnitName OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..16)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's unit name."
    ::= { slotEntry 5 }

slotCardFormatVersion OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..8)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's format version."
    ::= { slotEntry 6 }

slotCardCLEICode OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..16)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's CLEI code."
    ::= { slotEntry 7 }

slotCardPartNumber OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..32)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's Part Number."
    ::= { slotEntry 8 }

slotCardHwRev OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..16)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's Hardware Revision."
    ::= { slotEntry 9 }

slotCardSwRev OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..16)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's Software Revision."
    ::= { slotEntry 10 }

slotCardSerialNum OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..32)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's Serial Number."
    ::= { slotEntry 11 }

slotCardMfgName OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..16)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's Manufacturer Name."
    ::= { slotEntry 12 }

slotCardMfgDate OBJECT-TYPE
    SYNTAX  DateAndTime 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's Manufacturer date."
    ::= { slotEntry 13 }

slotCardMfgSite OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..16)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's Manufacturer site."
    ::= { slotEntry 14 }

slotSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Slot."
    ::= { slotEntry 15 }

slotCardPhysicalAddress OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..32)) 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "This is the inserted card's MAC Address, if applicable."
    ::= { slotEntry 16 }

--
--Card - Power Supply
--
psuTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF PsuEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Power Supply Units within the Shelf." 
    ::= { cmEntityObjects 4 }

psuEntry  OBJECT-TYPE
    SYNTAX      PsuEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the psuTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { psuTable 1 }

PsuEntry ::= SEQUENCE {
    psuEntityIndex       PhysicalIndex,
    psuType              PsuType,
    psuAdminState        AdminState,
    psuOperationalState  OperationalState,
    psuSecondaryState    SecondaryState,
    psuOutputVoltage     Integer32,
    psuTemperature       Integer32,
    psuOutputCurrent     Integer32,
    psuStorageType       StorageType,
    psuRowStatus         RowStatus
}

psuEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this PSU."
    ::= { psuEntry 1 }

psuType OBJECT-TYPE
    SYNTAX  PsuType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "PSU Type of the PSU."
    ::= { psuEntry 2 }

psuAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the PSU."
    ::= { psuEntry 3 }

psuOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the PSU."
    ::= { psuEntry 4 }

psuSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the PSU."
    ::= { psuEntry 5 }

psuOutputVoltage OBJECT-TYPE
    SYNTAX  Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Output Voltage of the PSU."
    ::= { psuEntry 6 }

psuTemperature OBJECT-TYPE
    SYNTAX  Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the PSU."
    ::= { psuEntry 7 }

psuOutputCurrent OBJECT-TYPE
    SYNTAX  Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Output Current of the PSU."
    ::= { psuEntry 8 }

psuStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { psuEntry 9 }

psuRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of psuRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            psuRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The psuRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { psuEntry 10 }


--
--Card - FAN 
--
fanTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF FanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on FANs within the Shelf." 
    ::= { cmEntityObjects 5 }

fanEntry  OBJECT-TYPE
    SYNTAX      FanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the fanTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { fanTable 1 }

FanEntry ::= SEQUENCE {
    fanEntityIndex       PhysicalIndex,
    fanAdminState        AdminState,
    fanOperationalState  OperationalState,
    fanSecondaryState    SecondaryState,
    fanStorageType       StorageType,
    fanRowStatus         RowStatus
}

fanEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this FAN."
    ::= { fanEntry 1 }

fanAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the FAN."
    ::= { fanEntry 2 }

fanOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the FAN."
    ::= { fanEntry 3 }

fanSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the FAN."
    ::= { fanEntry 4 }

fanStorageType OBJECT-TYPE
    SYNTAX  StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
             "Storage Type of the FAN."
    ::= { fanEntry 5 }

fanRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The status of this row.  An entry MUST NOT exist in the
            active state unless all objects in the entry have an
            appropriate value, as described in the description
            clause for each writable object.

            The values of fanRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            fanRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The fanRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."

    ::= { fanEntry 6 }

--
--Card - SCU - Shelf Control Unit 
--
scuTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF ScuEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on SCU within the Shelf." 
    ::= { cmEntityObjects 6 }

scuEntry  OBJECT-TYPE
    SYNTAX      ScuEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the scuTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { scuTable 1 }

ScuEntry ::= SEQUENCE {
    scuEntityIndex          PhysicalIndex,
    scuAdminState           AdminState,
    scuOperationalState     OperationalState,
    scuSecondaryState       SecondaryState,
    scuVoltage              Integer32,
    scuTemperature          Integer32,
    scuRestartAction        RestartType,
    scuStorageType          StorageType,
    scuRowStatus            RowStatus,
    scuFlashModelNum        DisplayString,    
    scuFlashFirmwareRev     DisplayString,    
    scuFlashSerialNum       DisplayString
}

scuEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the SCU card."
    ::= { scuEntry 1 }

scuAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the SCU."
    ::= { scuEntry 2 }

scuOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the SCU."
    ::= { scuEntry 3 }

scuSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the SCU."
    ::= { scuEntry 4 }

scuVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the SCU."
    ::= { scuEntry 5 }

scuTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the SCU."
    ::= { scuEntry 6 }

scuRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the SCU card."
    ::= { scuEntry 7 }

scuStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { scuEntry 8 }

scuRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of scuRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            scuRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The scuRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { scuEntry 9 }

scuFlashModelNum OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This provides the Compact Flash Model Number on the SCU card."
    ::= { scuEntry 10 }

scuFlashFirmwareRev OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This provides the Compact Flash Firmware Revision on the SCU card."
    ::= { scuEntry 11 }

scuFlashSerialNum OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This provides the Compact Flash Serial Number on the SCU card."
    ::= { scuEntry 12 }

--
--Card - NEMI 
--
nemiTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF NemiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on NEMI within the Shelf." 
    ::= { cmEntityObjects 7 }

nemiEntry  OBJECT-TYPE
    SYNTAX      NemiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the nemiTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { nemiTable 1 }

NemiEntry ::= SEQUENCE {
    nemiEntityIndex         PhysicalIndex,
    nemiAdminState          AdminState,
    nemiOperationalState    OperationalState,
    nemiSecondaryState      SecondaryState,
    nemiVoltage             Integer32,
    nemiTemperature         Integer32,
    nemiRestartAction       RestartType,
    nemiStorageType         StorageType,
    nemiRowStatus           RowStatus,
    nemiForceOffLineAction  TruthValue,
    nemiFlashModelNum       DisplayString,    
    nemiFlashFirmwareRev    DisplayString,    
    nemiFlashSerialNum      DisplayString
}

nemiEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the NEMI card."
    ::= { nemiEntry 1 }

nemiAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the NEMI."
    ::= { nemiEntry 2 }

nemiOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the NEMI."
    ::= { nemiEntry 3 }

nemiSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the NEMI."
    ::= { nemiEntry 4 }

nemiVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the NEMI."
    ::= { nemiEntry 5 }

nemiTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the NEMI."
    ::= { nemiEntry 6 }

nemiRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the NEMI card."
    ::= { nemiEntry 7 }

nemiStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { nemiEntry 8 }

nemiRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of nemiRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            nemiRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The nemiRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { nemiEntry 9 }

nemiForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { nemiEntry 10 }

nemiFlashModelNum OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This provides the Flash Model Number on the NEMI card."
    ::= { nemiEntry 11 }

nemiFlashFirmwareRev OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This provides the Flash Firmware Revision on the NEMI card."
    ::= { nemiEntry 12 }

nemiFlashSerialNum OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This provides the Flash Serial Number on the NEMI card."
    ::= { nemiEntry 13 }
--
--Card - Ethernet NTU Card 
--
ethernetNTUCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTUCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTU Cards within the Shelf.
          These cards support 10M, 100M and 1G Ethernet Interfaces." 
    ::= { cmEntityObjects 8 }

ethernetNTUCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTUCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTUCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTUCardTable 1 }

EthernetNTUCardEntry ::= SEQUENCE {
    ethernetNTUCardEntityIndex             PhysicalIndex,
    ethernetNTUCardAdminState              AdminState,
    ethernetNTUCardOperationalState        OperationalState,
    ethernetNTUCardSecondaryState          SecondaryState,
    ethernetNTUCardVoltage                 Integer32,
    ethernetNTUCardTemperature             Integer32,
    ethernetNTUCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTUCardRestartAction           RestartType,
    ethernetNTUCardStorageType             StorageType,
    ethernetNTUCardRowStatus               RowStatus
}

ethernetNTUCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTU Card."
    ::= { ethernetNTUCardEntry 1 }

ethernetNTUCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTU Card."
    ::= { ethernetNTUCardEntry 2 }

ethernetNTUCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTU Card."
    ::= { ethernetNTUCardEntry 3 }

ethernetNTUCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTU Card."
    ::= { ethernetNTUCardEntry 4 }

ethernetNTUCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTU Card."
    ::= { ethernetNTUCardEntry 5 }

ethernetNTUCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTU Card."
    ::= { ethernetNTUCardEntry 6 }

ethernetNTUCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTU Card."
    ::= { ethernetNTUCardEntry 7 }

ethernetNTUCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTU card."
    ::= { ethernetNTUCardEntry 8 }

ethernetNTUCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetNTUCardEntry 9 }

ethernetNTUCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetNTUCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetNTUCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetNTUCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetNTUCardEntry 10 }


--
--Card - Ethernet CPMR Card 
--
ethernetCPMRCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetCPMRCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet CPMR Cards within the Shelf." 
    ::= { cmEntityObjects 9 }

ethernetCPMRCardEntry  OBJECT-TYPE
    SYNTAX      EthernetCPMRCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetCPMRCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetCPMRCardTable 1 }

EthernetCPMRCardEntry ::= SEQUENCE {
    ethernetCPMRCardEntityIndex             PhysicalIndex,
    ethernetCPMRCardAdminState              AdminState,
    ethernetCPMRCardOperationalState        OperationalState,
    ethernetCPMRCardSecondaryState          SecondaryState,
    ethernetCPMRCardVoltage                 Integer32,
    ethernetCPMRCardTemperature             Integer32,
    ethernetCPMRCardRestartAction           RestartType,
    ethernetCPMRCardPSU1State               OperationalState,
    ethernetCPMRCardPSU2State               OperationalState,
    ethernetCPMRCardFAN1State               OperationalState,
    ethernetCPMRCardFAN2State               OperationalState,
    ethernetCPMRCardPsuType                 PsuType,
    ethernetCPMRCardLLFMode                 CmCPMRLinkLossFwdMode,
    ethernetCPMRCardLLFModeAction           CmCPMRLinkLossFwdMode
}

ethernetCPMRCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet CPMR Card."
    ::= { ethernetCPMRCardEntry 1 }

ethernetCPMRCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet CPMR Card."
    ::= { ethernetCPMRCardEntry 2 }

ethernetCPMRCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet CPMR Card."
    ::= { ethernetCPMRCardEntry 3 }

ethernetCPMRCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet CPMR Card."
    ::= { ethernetCPMRCardEntry 4 }

ethernetCPMRCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet CPMR Card."
    ::= { ethernetCPMRCardEntry 5 }

ethernetCPMRCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet CPMR Card."
    ::= { ethernetCPMRCardEntry 6 }

ethernetCPMRCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet CPMR card."
    ::= { ethernetCPMRCardEntry 7 }

ethernetCPMRCardPSU1State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of Power Supply Unit 1 on Ethernet CPMR chassis."
    ::= { ethernetCPMRCardEntry 8 }

ethernetCPMRCardPSU2State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of Power Supply Unit 2 on Ethernet CPMR chassis."
    ::= { ethernetCPMRCardEntry 9 }

ethernetCPMRCardFAN1State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of FAN 1 on Ethernet CPMR chassis."
    ::= { ethernetCPMRCardEntry 10 }

ethernetCPMRCardFAN2State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of FAN 2 on Ethernet CPMR chassis."
    ::= { ethernetCPMRCardEntry 11 }

ethernetCPMRCardPsuType OBJECT-TYPE
    SYNTAX  PsuType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "PSU Type on Ethernet CPMR chassis."
    ::= { ethernetCPMRCardEntry 12 }

ethernetCPMRCardLLFMode OBJECT-TYPE
    SYNTAX      CmCPMRLinkLossFwdMode 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Current Link Loss Forwarding Mode on CPMR."
    ::= { ethernetCPMRCardEntry 13 }

ethernetCPMRCardLLFModeAction OBJECT-TYPE
    SYNTAX      CmCPMRLinkLossFwdMode 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "Operation to set the appropriate mode on CPMR."
    ::= { ethernetCPMRCardEntry 14 }


--
--Card - Ethernet NTE - GE 101 Card 
--
ethernetNTEGE101CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE101CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE101 Cards.
          These are supported on the FSP150CC GE101 product." 
    ::= { cmEntityObjects 10 }

ethernetNTEGE101CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE101CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE101CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE101CardTable 1 }

EthernetNTEGE101CardEntry ::= SEQUENCE {
    ethernetNTEGE101CardEntityIndex             PhysicalIndex,
    ethernetNTEGE101CardAdminState              AdminState,
    ethernetNTEGE101CardOperationalState        OperationalState,
    ethernetNTEGE101CardSecondaryState          SecondaryState,
    ethernetNTEGE101CardVoltage                 Integer32,
    ethernetNTEGE101CardTemperature             Integer32,
    ethernetNTEGE101CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE101CardRestartAction           RestartType
}

ethernetNTEGE101CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE101 Card."
    ::= { ethernetNTEGE101CardEntry 1 }

ethernetNTEGE101CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE101 Card."
    ::= { ethernetNTEGE101CardEntry 2 }

ethernetNTEGE101CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE101 Card."
    ::= { ethernetNTEGE101CardEntry 3 }

ethernetNTEGE101CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE101 Card."
    ::= { ethernetNTEGE101CardEntry 4 }

ethernetNTEGE101CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE101 Card."
    ::= { ethernetNTEGE101CardEntry 5 }

ethernetNTEGE101CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE101 Card."
    ::= { ethernetNTEGE101CardEntry 6 }

ethernetNTEGE101CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE101 Card."
    ::= { ethernetNTEGE101CardEntry 7 }

ethernetNTEGE101CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE101 card."
    ::= { ethernetNTEGE101CardEntry 8 }


--
--Card - Ethernet NTE - GE 206 Card 
--
ethernetNTEGE206CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE206CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE206 Cards.
          These are supported on the FSP150CC GE206 product." 
    ::= { cmEntityObjects 11 }

ethernetNTEGE206CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE206CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE206CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE206CardTable 1 }

EthernetNTEGE206CardEntry ::= SEQUENCE {
    ethernetNTEGE206CardEntityIndex             PhysicalIndex,
    ethernetNTEGE206CardAdminState              AdminState,
    ethernetNTEGE206CardOperationalState        OperationalState,
    ethernetNTEGE206CardSecondaryState          SecondaryState,
    ethernetNTEGE206CardVoltage                 Integer32,
    ethernetNTEGE206CardTemperature             Integer32,
    ethernetNTEGE206CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE206CardRestartAction           RestartType,
    ethernetNTEGE206CardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEGE206CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206CardEntry 1 }

ethernetNTEGE206CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206CardEntry 2 }

ethernetNTEGE206CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206CardEntry 3 }

ethernetNTEGE206CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206CardEntry 4 }

ethernetNTEGE206CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206CardEntry 5 }

ethernetNTEGE206CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206CardEntry 6 }

ethernetNTEGE206CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206CardEntry 7 }

ethernetNTEGE206CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE206 card."
    ::= { ethernetNTEGE206CardEntry 8 }

ethernetNTEGE206CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE206CardEntry 9 }


--
--Card - PWE3 Card 
--
pseudoWireE3CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF PseudoWireE3CardEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
         "Contains information on Pseudo Wire E3 (Emulation Edge to Edge) Cards.
          These are supported on the FSP150CC GE206 product." 
    ::= { cmEntityObjects 12 }

pseudoWireE3CardEntry OBJECT-TYPE
    SYNTAX      PseudoWireE3CardEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
            "A conceptual row in the pseudoWireE3CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { pseudoWireE3CardTable 1 }

PseudoWireE3CardEntry ::= SEQUENCE {
    pseudoWireE3CardEntityIndex          PhysicalIndex,
    pseudoWireE3CardAdminState           AdminState,
    pseudoWireE3CardOperationalState     OperationalState,
    pseudoWireE3CardSecondaryState       SecondaryState,
    pseudoWireE3CardIpAddress            IpAddress,
    pseudoWireE3CardIpNetmask            IpAddress,
    pseudoWireE3CardIpGateway            IpAddress,
    pseudoWireE3CardDhcpEnabled          TruthValue,
    pseudoWireE3CardMgmtVlanId           VlanId,
    pseudoWireE3CardTimeOfDay            DateAndTime,
    pseudoWireE3CardRestartAction        RestartType
}

pseudoWireE3CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 1 }

pseudoWireE3CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
             "Administrative State of the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 2 }

pseudoWireE3CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
             "Operational State of the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 3 }

pseudoWireE3CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
             "Secondary State of the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 4 }

pseudoWireE3CardIpAddress OBJECT-TYPE
    SYNTAX  IpAddress 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
             "External management IP Address of the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 5 }

pseudoWireE3CardIpNetmask OBJECT-TYPE
    SYNTAX  IpAddress 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
             "External management IP Net mask of the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 6 }

pseudoWireE3CardIpGateway OBJECT-TYPE
    SYNTAX  IpAddress 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
             "External management IP Gateway of the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 7 }

pseudoWireE3CardDhcpEnabled OBJECT-TYPE
    SYNTAX  TruthValue 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
             "External management - whether DHCP is enabled."
    ::= { pseudoWireE3CardEntry 8 }

pseudoWireE3CardMgmtVlanId OBJECT-TYPE
    SYNTAX  VlanId 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
             "External management VLAN Id for the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 9 }

pseudoWireE3CardTimeOfDay OBJECT-TYPE
    SYNTAX  DateAndTime 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
             "Time of day for the Pseudo Wire E3 Card."
    ::= { pseudoWireE3CardEntry 10 }

pseudoWireE3CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Allows to perform restart action on the Pseudo Wire E3 card."
    ::= { pseudoWireE3CardEntry 11 }

--
--Card - SCU-T - Shelf Control Unit with Timing
--
scuTTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF ScuTEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on SCU-T within the Shelf." 
    ::= { cmEntityObjects 13 }

scuTEntry  OBJECT-TYPE
    SYNTAX      ScuTEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the scuTTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { scuTTable 1 }

ScuTEntry ::= SEQUENCE {
    scuTEntityIndex       PhysicalIndex,
    scuTAdminState        AdminState,
    scuTOperationalState  OperationalState,
    scuTSecondaryState    SecondaryState,
    scuTVoltage           Integer32,
    scuTTemperature       Integer32,
    scuTRestartAction     RestartType,
    scuTStorageType       StorageType,
    scuTRowStatus         RowStatus
}

scuTEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the SCU-T card."
    ::= { scuTEntry 1 }

scuTAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the SCU-T."
    ::= { scuTEntry 2 }

scuTOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the SCU-T."
    ::= { scuTEntry 3 }

scuTSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the SCU-T."
    ::= { scuTEntry 4 }

scuTVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the SCU-T."
    ::= { scuTEntry 5 }

scuTTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the SCU-T."
    ::= { scuTEntry 6 }

scuTRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the SCU-T card."
    ::= { scuTEntry 7 }

scuTStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { scuTEntry 8 }

scuTRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of scuTRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            neRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The scuTRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { scuTEntry 9 }

--
--Card - GigE Ethernet NTE Card 
--
ethernetNTECardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE Cards within the Shelf.
          These cards support 10M, 100M and 1G Ethernet Interfaces." 
    ::= { cmEntityObjects 14 }

ethernetNTECardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTECardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTECardTable 1 }

EthernetNTECardEntry ::= SEQUENCE {
    ethernetNTECardEntityIndex             PhysicalIndex,
    ethernetNTECardAdminState              AdminState,
    ethernetNTECardOperationalState        OperationalState,
    ethernetNTECardSecondaryState          SecondaryState,
    ethernetNTECardVoltage                 Integer32,
    ethernetNTECardTemperature             Integer32,
    ethernetNTECardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTECardRestartAction           RestartType,
    ethernetNTECardStorageType             StorageType,
    ethernetNTECardRowStatus               RowStatus
}

ethernetNTECardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE Card."
    ::= { ethernetNTECardEntry 1 }

ethernetNTECardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE Card."
    ::= { ethernetNTECardEntry 2 }

ethernetNTECardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE Card."
    ::= { ethernetNTECardEntry 3 }

ethernetNTECardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE Card."
    ::= { ethernetNTECardEntry 4 }

ethernetNTECardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE Card."
    ::= { ethernetNTECardEntry 5 }

ethernetNTECardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE Card."
    ::= { ethernetNTECardEntry 6 }

ethernetNTECardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE Card."
    ::= { ethernetNTECardEntry 7 }

ethernetNTECardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE card."
    ::= { ethernetNTECardEntry 8 }

ethernetNTECardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetNTECardEntry 9 }

ethernetNTECardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetNTECardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetNTECardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetNTECardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetNTECardEntry 10 }


--
--Card - Ethernet NTE - GE 201 Card 
--
ethernetNTEGE201CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE201CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE201 Cards.
          These are supported on the FSP150CC GE201 product." 
    ::= { cmEntityObjects 15 }

ethernetNTEGE201CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE201CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE201CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE201CardTable 1 }

EthernetNTEGE201CardEntry ::= SEQUENCE {
    ethernetNTEGE201CardEntityIndex             PhysicalIndex,
    ethernetNTEGE201CardAdminState              AdminState,
    ethernetNTEGE201CardOperationalState        OperationalState,
    ethernetNTEGE201CardSecondaryState          SecondaryState,
    ethernetNTEGE201CardVoltage                 Integer32,
    ethernetNTEGE201CardTemperature             Integer32,
    ethernetNTEGE201CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE201CardRestartAction           RestartType,
    ethernetNTEGE201CardFineGrainedPmInterval   CmPmIntervalType 
}

ethernetNTEGE201CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE201 Card."
    ::= { ethernetNTEGE201CardEntry 1 }

ethernetNTEGE201CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE201 Card."
    ::= { ethernetNTEGE201CardEntry 2 }

ethernetNTEGE201CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE201 Card."
    ::= { ethernetNTEGE201CardEntry 3 }

ethernetNTEGE201CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE201 Card."
    ::= { ethernetNTEGE201CardEntry 4 }

ethernetNTEGE201CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE201 Card."
    ::= { ethernetNTEGE201CardEntry 5 }

ethernetNTEGE201CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE201 Card."
    ::= { ethernetNTEGE201CardEntry 6 }

ethernetNTEGE201CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE201 Card."
    ::= { ethernetNTEGE201CardEntry 7 }

ethernetNTEGE201CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE201 card."
    ::= { ethernetNTEGE201CardEntry 8 }

ethernetNTEGE201CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE201CardEntry 9 }


--
--Card - Ethernet NTE - GE 201 SyncE Card 
--
ethernetNTEGE201SyncECardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE201SyncECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE201SyncE Cards.
          These are supported on the FSP150CC GE201SyncE product." 
    ::= { cmEntityObjects 16 }

ethernetNTEGE201SyncECardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE201SyncECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE201SyncECardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE201SyncECardTable 1 }

EthernetNTEGE201SyncECardEntry ::= SEQUENCE {
    ethernetNTEGE201SyncECardEntityIndex             PhysicalIndex,
    ethernetNTEGE201SyncECardAdminState              AdminState,
    ethernetNTEGE201SyncECardOperationalState        OperationalState,
    ethernetNTEGE201SyncECardSecondaryState          SecondaryState,
    ethernetNTEGE201SyncECardVoltage                 Integer32,
    ethernetNTEGE201SyncECardTemperature             Integer32,
    ethernetNTEGE201SyncECardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE201SyncECardRestartAction           RestartType,
    ethernetNTEGE201SyncECardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEGE201SyncECardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE201SyncE Card."
    ::= { ethernetNTEGE201SyncECardEntry 1 }

ethernetNTEGE201SyncECardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE201SyncE Card."
    ::= { ethernetNTEGE201SyncECardEntry 2 }

ethernetNTEGE201SyncECardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE201SyncE Card."
    ::= { ethernetNTEGE201SyncECardEntry 3 }

ethernetNTEGE201SyncECardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE201SyncE Card."
    ::= { ethernetNTEGE201SyncECardEntry 4 }

ethernetNTEGE201SyncECardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE201SyncE Card."
    ::= { ethernetNTEGE201SyncECardEntry 5 }

ethernetNTEGE201SyncECardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE201SyncE Card."
    ::= { ethernetNTEGE201SyncECardEntry 6 }

ethernetNTEGE201SyncECardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE201SyncE Card."
    ::= { ethernetNTEGE201SyncECardEntry 7 }

ethernetNTEGE201SyncECardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE201SyncE card."
    ::= { ethernetNTEGE201SyncECardEntry 8 }

ethernetNTEGE201SyncECardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE201SyncECardEntry 9 }

--
--Card - Ethernet NTE - GE 206F Card 
--
ethernetNTEGE206FCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE206FCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE206F Cards.
          These are supported on the FSP150CC GE206 product." 
    ::= { cmEntityObjects 17 }

ethernetNTEGE206FCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE206FCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE206FCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE206FCardTable 1 }

EthernetNTEGE206FCardEntry ::= SEQUENCE {
    ethernetNTEGE206FCardEntityIndex             PhysicalIndex,
    ethernetNTEGE206FCardAdminState              AdminState,
    ethernetNTEGE206FCardOperationalState        OperationalState,
    ethernetNTEGE206FCardSecondaryState          SecondaryState,
    ethernetNTEGE206FCardVoltage                 Integer32,
    ethernetNTEGE206FCardTemperature             Integer32,
    ethernetNTEGE206FCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE206FCardRestartAction           RestartType,
    ethernetNTEGE206FCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEGE206FCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206FCardEntry 1 }

ethernetNTEGE206FCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206FCardEntry 2 }

ethernetNTEGE206FCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206FCardEntry 3 }

ethernetNTEGE206FCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206FCardEntry 4 }

ethernetNTEGE206FCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206FCardEntry 5 }

ethernetNTEGE206FCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206FCardEntry 6 }

ethernetNTEGE206FCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE206 Card."
    ::= { ethernetNTEGE206FCardEntry 7 }

ethernetNTEGE206FCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE206 card."
    ::= { ethernetNTEGE206FCardEntry 8 }

ethernetNTEGE206FCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE206FCardEntry 9 }


--
--Card - Ethernet  - 1x10G Card 
--
ethernet1x10GCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF Ethernet1x10GCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet  1x10G Cards." 
    ::= { cmEntityObjects 18 }

ethernet1x10GCardEntry  OBJECT-TYPE
    SYNTAX      Ethernet1x10GCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernet1x10GCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernet1x10GCardTable 1 }

Ethernet1x10GCardEntry ::= SEQUENCE {
    ethernet1x10GCardEntityIndex             PhysicalIndex,
    ethernet1x10GCardAdminState              AdminState,
    ethernet1x10GCardOperationalState        OperationalState,
    ethernet1x10GCardSecondaryState          SecondaryState,
    ethernet1x10GCardTemperature             Integer32,
    ethernet1x10GCardSnmpDyingGaspEnabled    TruthValue,
    ethernet1x10GCardRestartAction           RestartType,
    ethernet1x10GCardStorageType             StorageType,
    ethernet1x10GCardRowStatus               RowStatus,
    ethernet1x10GCardForceOffLineAction      TruthValue
}

ethernet1x10GCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet 1x10G Card."
    ::= { ethernet1x10GCardEntry 1 }

ethernet1x10GCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet 1x10G Card."
    ::= { ethernet1x10GCardEntry 2 }

ethernet1x10GCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet 1x10G Card."
    ::= { ethernet1x10GCardEntry 3 }

ethernet1x10GCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet 1x10G Card."
    ::= { ethernet1x10GCardEntry 4 }

ethernet1x10GCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet 1x10G Card."
    ::= { ethernet1x10GCardEntry 5 }

ethernet1x10GCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet 1x10G Card."
    ::= { ethernet1x10GCardEntry 6 }

ethernet1x10GCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet 1x10G card."
    ::= { ethernet1x10GCardEntry 7 }
    
ethernet1x10GCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernet1x10GCardEntry 8 }

ethernet1x10GCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernet1x10GCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetNTUCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernet1x10GCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernet1x10GCardEntry 9 }

ethernet1x10GCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { ethernet1x10GCardEntry 10 }

--
--Card - Ethernet  - 10x1G Card 
--
ethernet10x1GCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF Ethernet10x1GCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet  10x1G Cards." 
    ::= { cmEntityObjects 19 }

ethernet10x1GCardEntry  OBJECT-TYPE
    SYNTAX      Ethernet10x1GCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernet10x1GCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernet10x1GCardTable 1 }

Ethernet10x1GCardEntry ::= SEQUENCE {
    ethernet10x1GCardEntityIndex             PhysicalIndex,
    ethernet10x1GCardAdminState              AdminState,
    ethernet10x1GCardOperationalState        OperationalState,
    ethernet10x1GCardSecondaryState          SecondaryState,
    ethernet10x1GCardTemperature             Integer32,
    ethernet10x1GCardSnmpDyingGaspEnabled    TruthValue,
    ethernet10x1GCardRestartAction           RestartType,
    ethernet10x1GCardStorageType             StorageType,
    ethernet10x1GCardRowStatus               RowStatus,
    ethernet10x1GCardForceOffLineAction      TruthValue
}

ethernet10x1GCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet 10x1G Card."
    ::= { ethernet10x1GCardEntry 1 }

ethernet10x1GCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet 10x1G Card."
    ::= { ethernet10x1GCardEntry 2 }

ethernet10x1GCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet 10x1G Card."
    ::= { ethernet10x1GCardEntry 3 }

ethernet10x1GCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet 10x1G Card."
    ::= { ethernet10x1GCardEntry 4 }

ethernet10x1GCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet 10x1G Card."
    ::= { ethernet10x1GCardEntry 5 }

ethernet10x1GCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet 10x1G Card."
    ::= { ethernet10x1GCardEntry 6 }

ethernet10x1GCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet 10x1G card."
    ::= { ethernet10x1GCardEntry 7 }
    
ethernet10x1GCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernet10x1GCardEntry 8 }

ethernet10x1GCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernet10x1GCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernet10x1GCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernet10x1GCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernet10x1GCardEntry 9 }

ethernet10x1GCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { ethernet10x1GCardEntry 10 }


--
--Card - Ethernet  - SWF Card 
--
ethernetSWFCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetSWFCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet SWF Cards." 
    ::= { cmEntityObjects 20 }

ethernetSWFCardEntry  OBJECT-TYPE
    SYNTAX      EthernetSWFCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetSWFCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetSWFCardTable 1 }

EthernetSWFCardEntry ::= SEQUENCE {
    ethernetSWFCardEntityIndex             PhysicalIndex,
    ethernetSWFCardAdminState              AdminState,
    ethernetSWFCardOperationalState        OperationalState,
    ethernetSWFCardSecondaryState          SecondaryState,
    ethernetSWFCardTemperature             Integer32,
    ethernetSWFCardRestartAction           RestartType,
    ethernetSWFCardStorageType             StorageType,
    ethernetSWFCardRowStatus               RowStatus,
    ethernetSWFCardForceOffLineAction      TruthValue
}

ethernetSWFCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet SWF Card."
    ::= { ethernetSWFCardEntry 1 }

ethernetSWFCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet SWF Card."
    ::= { ethernetSWFCardEntry 2 }

ethernetSWFCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet SWF Card."
    ::= { ethernetSWFCardEntry 3 }

ethernetSWFCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet SWF Card."
    ::= { ethernetSWFCardEntry 4 }

ethernetSWFCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet SWF Card."
    ::= { ethernetSWFCardEntry 5 }

ethernetSWFCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet SWF card."
    ::= { ethernetSWFCardEntry 6 }
    
ethernetSWFCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetSWFCardEntry 7 }

ethernetSWFCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetSWFCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetNTUCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetSWFCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetSWFCardEntry 8 }

ethernetSWFCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { ethernetSWFCardEntry 9 }

--
--Card - STI Card 
--
stuCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF StuCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on STU Cards." 
    ::= { cmEntityObjects 21 }

stuCardEntry  OBJECT-TYPE
    SYNTAX      StuCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the stuCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { stuCardTable 1 }

StuCardEntry ::= SEQUENCE {
    stuCardEntityIndex             PhysicalIndex,
    stuCardAdminState              AdminState,
    stuCardOperationalState        OperationalState,
    stuCardSecondaryState          SecondaryState,
    stuCardTemperature             Integer32,
    stuCardRestartAction           RestartType,
    stuCardStorageType             StorageType,
    stuCardRowStatus               RowStatus,
    stuCardForceOffLineAction      TruthValue
}

stuCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the STU Card."
    ::= { stuCardEntry 1 }

stuCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Stu Card."
    ::= { stuCardEntry 2 }

stuCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Stu Card."
    ::= { stuCardEntry 3 }

stuCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Stu Card."
    ::= { stuCardEntry 4 }

stuCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Stu Card."
    ::= { stuCardEntry 5 }

stuCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Stu card."
    ::= { stuCardEntry 6 }
    
stuCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { stuCardEntry 7 }

stuCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of stuCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            stuRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The stuCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { stuCardEntry 8 }

stuCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { stuCardEntry 9 }


--
--Card - AMI - Shelf Control Unit 
--
amiTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF AmiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on AMI within the Shelf." 
    ::= { cmEntityObjects 22 }

amiEntry  OBJECT-TYPE
    SYNTAX      AmiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the amiTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { amiTable 1 }

AmiEntry ::= SEQUENCE {
    amiEntityIndex       PhysicalIndex,
    amiAdminState        AdminState,
    amiOperationalState  OperationalState,
    amiSecondaryState    SecondaryState,
    amiTemperature       Integer32,
    amiRestartAction     RestartType
}

amiEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the AMI card."
    ::= { amiEntry 1 }

amiAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the AMI."
    ::= { amiEntry 2 }

amiOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the AMI."
    ::= { amiEntry 3 }

amiSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the AMI."
    ::= { amiEntry 4 }

amiTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the AMI."
    ::= { amiEntry 5 }

amiRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the AMI card."
    ::= { amiEntry 6 }
--
--Card - STI Card
--
stiTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF StiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on STI within the Shelf." 
    ::= { cmEntityObjects 23 }

stiEntry  OBJECT-TYPE
    SYNTAX      StiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the stiTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { stiTable 1 }

StiEntry ::= SEQUENCE {
    stiEntityIndex       PhysicalIndex,
    stiAdminState        AdminState,
    stiOperationalState  OperationalState,
    stiSecondaryState    SecondaryState,
    stiTemperature       Integer32,
    stiStorageType       StorageType,
    stiRowStatus         RowStatus
}

stiEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the STI card."
    ::= { stiEntry 1 }

stiAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the STI."
    ::= { stiEntry 2 }

stiOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the STI."
    ::= { stiEntry 3 }

stiSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the STI."
    ::= { stiEntry 4 }

stiTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the STI."
    ::= { stiEntry 5 }

stiStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { stiEntry 6 }

stiRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of stiRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            stiRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The stiRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { stiEntry 7 }


--
-- usb host
--
f3UsbHostTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3UsbHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Usb Host." 
    ::= { cmEntityObjects 24 }

f3UsbHostEntry  OBJECT-TYPE
    SYNTAX      F3UsbHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the f3UsbHostTable."
    INDEX { neIndex, shelfIndex, slotIndex, f3UsbHostIndex }
    ::= { f3UsbHostTable 1 }

F3UsbHostEntry ::= SEQUENCE {
    f3UsbHostIndex               PhysicalIndex,
    f3UsbHostEntityIndex         PhysicalIndex,
    f3UsbHostUnitName            DisplayString,
    f3UsbHostFormatVersion       DisplayString,
    f3UsbHostCLEICode            DisplayString,
    f3UsbHostPartNumber          DisplayString,
    f3UsbHostHwRev               DisplayString,
    f3UsbHostSwRev               DisplayString,
    f3UsbHostSerialNum           DisplayString,
    f3UsbHostMfgName             DisplayString,
    f3UsbHostMfgDate             DateAndTime,
    f3UsbHostMfgSite             DisplayString,
    f3UsbHostSecondaryState      SecondaryState,
    f3UsbHostPhysicalAddress     DisplayString,
    f3UsbHostMuxOperationalMode  UsbOperationalMode
}

f3UsbHostIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "The actual physical usb host index that uniquely identify 
            a usb host."
    ::= { f3UsbHostEntry 1 }

f3UsbHostEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the usb host."
    ::= { f3UsbHostEntry 2 }

f3UsbHostUnitName OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's unit name."
    ::= { f3UsbHostEntry 3 }

f3UsbHostFormatVersion OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's format version."
    ::= { f3UsbHostEntry 4 }

f3UsbHostCLEICode OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's CLEI code."
    ::= { f3UsbHostEntry 5 }

f3UsbHostPartNumber OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's Part Number."
    ::= { f3UsbHostEntry 6 }

f3UsbHostHwRev OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's Hardware Revision."
    ::= { f3UsbHostEntry 7 }

f3UsbHostSwRev OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's Software Revision."
    ::= { f3UsbHostEntry 8 }

f3UsbHostSerialNum OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's Serial Number."
    ::= { f3UsbHostEntry 9 }

f3UsbHostMfgName OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's Manufacturer Name."
    ::= { f3UsbHostEntry 10 }

f3UsbHostMfgDate OBJECT-TYPE
    SYNTAX  DateAndTime 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's Manufacturer date."
    ::= { f3UsbHostEntry 11 }

f3UsbHostMfgSite OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's Manufacturer site."
    ::= { f3UsbHostEntry 12 }

f3UsbHostSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Secondary State of the usb device."
    ::= { f3UsbHostEntry 13 }

f3UsbHostPhysicalAddress OBJECT-TYPE
    SYNTAX  DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "This is the inserted usb device's MAC Address, if applicable."
    ::= { f3UsbHostEntry 14 }

f3UsbHostMuxOperationalMode OBJECT-TYPE
    SYNTAX     UsbOperationalMode
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
         "This object describe the USB operatioan mode."
     ::= { f3UsbHostEntry 15 }

--
--Card - Ethernet NTE - GE 112 Card 
--
ethernetNTEGE112CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE112CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Cards.
          These are supported on the FSP150CC GE112 product." 
    ::= { cmEntityObjects 25 }

ethernetNTEGE112CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE112CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE112CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE112CardTable 1 }


SwitchPortAction ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Switch port action."
    SYNTAX       INTEGER {
                   not-applicable (0),
                   switch (1)
                 }


EthernetNTEGE112CardEntry ::= SEQUENCE {
    ethernetNTEGE112CardEntityIndex             PhysicalIndex,
    ethernetNTEGE112CardAdminState              AdminState,
    ethernetNTEGE112CardOperationalState        OperationalState,
    ethernetNTEGE112CardSecondaryState          SecondaryState,
    ethernetNTEGE112CardVoltage                 Integer32,
    ethernetNTEGE112CardTemperature             Integer32,
    ethernetNTEGE112CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE112CardRestartAction           RestartType,
    ethernetNTEGE112CardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE112CardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE112CardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE112CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE112CardEntry 1 }

ethernetNTEGE112CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE112CardEntry 2 }

ethernetNTEGE112CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE112CardEntry 3 }

ethernetNTEGE112CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE112CardEntry 4 }

ethernetNTEGE112CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE112CardEntry 5 }

ethernetNTEGE112CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE112CardEntry 6 }

ethernetNTEGE112CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE112CardEntry 7 }

ethernetNTEGE112CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112 card."
    ::= { ethernetNTEGE112CardEntry 8 }

ethernetNTEGE112CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE112CardEntry 9 }

ethernetNTEGE112CardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE112 card."
    ::= { ethernetNTEGE112CardEntry 10 }

ethernetNTEGE112CardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112 card."
    ::= { ethernetNTEGE112CardEntry 11 }

--
--Card - Ethernet NTE - GE 114 Card 
--
ethernetNTEGE114CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114 Cards.
          These are supported on the FSP150CC GE114 product." 
    ::= { cmEntityObjects 26 }

ethernetNTEGE114CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114CardTable 1 }

EthernetNTEGE114CardEntry ::= SEQUENCE {
    ethernetNTEGE114CardEntityIndex             PhysicalIndex,
    ethernetNTEGE114CardAdminState              AdminState,
    ethernetNTEGE114CardOperationalState        OperationalState,
    ethernetNTEGE114CardSecondaryState          SecondaryState,
    ethernetNTEGE114CardVoltage                 Integer32,
    ethernetNTEGE114CardTemperature             Integer32,
    ethernetNTEGE114CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114CardRestartAction           RestartType,
    ethernetNTEGE114CardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114CardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114CardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114 Card."
    ::= { ethernetNTEGE114CardEntry 1 }

ethernetNTEGE114CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114 Card."
    ::= { ethernetNTEGE114CardEntry 2 }

ethernetNTEGE114CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114 Card."
    ::= { ethernetNTEGE114CardEntry 3 }

ethernetNTEGE114CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114 Card."
    ::= { ethernetNTEGE114CardEntry 4 }

ethernetNTEGE114CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE114CardEntry 5 }

ethernetNTEGE114CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114 Card."
    ::= { ethernetNTEGE114CardEntry 6 }

ethernetNTEGE114CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114 Card."
    ::= { ethernetNTEGE114CardEntry 7 }

ethernetNTEGE114CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114 card."
    ::= { ethernetNTEGE114CardEntry 8 }

ethernetNTEGE114CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114CardEntry 9 }

ethernetNTEGE114CardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114 card."
    ::= { ethernetNTEGE114CardEntry 10 }

ethernetNTEGE114CardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114 card."
    ::= { ethernetNTEGE114CardEntry 11 }

--
--Card - Ethernet NTE - GE 206V Card 
--
ethernetNTEGE206VCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE206VCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE206V Cards.
          These are supported on the FSP150CC GE206V product." 
    ::= { cmEntityObjects 27 }

ethernetNTEGE206VCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE206VCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE206VCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE206VCardTable 1 }

EthernetNTEGE206VCardEntry ::= SEQUENCE {
    ethernetNTEGE206VCardEntityIndex             PhysicalIndex,
    ethernetNTEGE206VCardAdminState              AdminState,
    ethernetNTEGE206VCardOperationalState        OperationalState,
    ethernetNTEGE206VCardSecondaryState          SecondaryState,
    ethernetNTEGE206VCardVoltage                 Integer32,
    ethernetNTEGE206VCardTemperature             Integer32,
    ethernetNTEGE206VCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE206VCardRestartAction           RestartType,
    ethernetNTEGE206VCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEGE206VCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE206V Card."
    ::= { ethernetNTEGE206VCardEntry 1 }

ethernetNTEGE206VCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE206V Card."
    ::= { ethernetNTEGE206VCardEntry 2 }

ethernetNTEGE206VCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE206V Card."
    ::= { ethernetNTEGE206VCardEntry 3 }

ethernetNTEGE206VCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE206V Card."
    ::= { ethernetNTEGE206VCardEntry 4 }

ethernetNTEGE206VCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE206V Card."
    ::= { ethernetNTEGE206VCardEntry 5 }

ethernetNTEGE206VCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE206V Card."
    ::= { ethernetNTEGE206VCardEntry 6 }

ethernetNTEGE206VCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE206V Card."
    ::= { ethernetNTEGE206VCardEntry 7 }

ethernetNTEGE206VCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
           "Allows to perform specified action on the Ethernet NTE GE206V card."
    ::= { ethernetNTEGE206VCardEntry 8 }

ethernetNTEGE206VCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEGE206VCardEntry 9 }


--
--Card - Ethernet GE 206V Optical Port Expander Card 
--
ethernetGE4SCCCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetGE4SCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet GE206V Optical Port Expander Cards.
          These are supported on the FSP150CC GE206V product." 
    ::= { cmEntityObjects 28 }

ethernetGE4SCCCardEntry  OBJECT-TYPE
    SYNTAX      EthernetGE4SCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetGE4SCCCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetGE4SCCCardTable 1 }

EthernetGE4SCCCardEntry ::= SEQUENCE {
    ethernetGE4SCCCardEntityIndex             PhysicalIndex,
    ethernetGE4SCCCardAdminState              AdminState,
    ethernetGE4SCCCardOperationalState        OperationalState,
    ethernetGE4SCCCardSecondaryState          SecondaryState,
    ethernetGE4SCCCardVoltage                 Integer32,
    ethernetGE4SCCCardTemperature             Integer32,
    ethernetGE4SCCCardRestartAction           RestartType,
    ethernetGE4SCCCardStorageType             StorageType,
    ethernetGE4SCCCardRowStatus               RowStatus
}

ethernetGE4SCCCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Entity Index from ENTITY-MIB for the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4SCCCardEntry 1 }

ethernetGE4SCCCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4SCCCardEntry 2 }

ethernetGE4SCCCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4SCCCardEntry 3 }

ethernetGE4SCCCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4SCCCardEntry 4 }

ethernetGE4SCCCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4SCCCardEntry 5 }

ethernetGE4SCCCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4SCCCardEntry 6 }

ethernetGE4SCCCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet GE-4S Connector card."
    ::= { ethernetGE4SCCCardEntry 7 }

ethernetGE4SCCCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetGE4SCCCardEntry 8 }

ethernetGE4SCCCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetGE4SCCCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetGE4SCCCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetGE4SCCCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetGE4SCCCardEntry 9 }

--
--Card - Ethernet GE 206V Electrical Port Expander Card 
--
ethernetGE4ECCCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetGE4ECCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet GE206V Optical Port Expander Cards.
          These are supported on the FSP150CC GE206V product." 
    ::= { cmEntityObjects 29 }

ethernetGE4ECCCardEntry  OBJECT-TYPE
    SYNTAX      EthernetGE4ECCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetGE4ECCCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetGE4ECCCardTable 1 }

EthernetGE4ECCCardEntry ::= SEQUENCE {
    ethernetGE4ECCCardEntityIndex             PhysicalIndex,
    ethernetGE4ECCCardAdminState              AdminState,
    ethernetGE4ECCCardOperationalState        OperationalState,
    ethernetGE4ECCCardSecondaryState          SecondaryState,
    ethernetGE4ECCCardVoltage                 Integer32,
    ethernetGE4ECCCardTemperature             Integer32,
    ethernetGE4ECCCardRestartAction           RestartType,
    ethernetGE4ECCCardStorageType             StorageType,
    ethernetGE4ECCCardRowStatus               RowStatus
}

ethernetGE4ECCCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Entity Index from ENTITY-MIB for the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4ECCCardEntry 1 }

ethernetGE4ECCCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4ECCCardEntry 2 }

ethernetGE4ECCCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4ECCCardEntry 3 }

ethernetGE4ECCCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4ECCCardEntry 4 }

ethernetGE4ECCCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4ECCCardEntry 5 }

ethernetGE4ECCCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet GE-4S Connector Card."
    ::= { ethernetGE4ECCCardEntry 6 }

ethernetGE4ECCCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet GE-4S Connector card."
    ::= { ethernetGE4ECCCardEntry 7 }

ethernetGE4ECCCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetGE4ECCCardEntry 8 }

ethernetGE4ECCCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetGE4ECCCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetGE4ECCCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetGE4ECCCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetGE4ECCCardEntry 9 }


--
--Card - Ethernet NTE - XG210 Card 
--
ethernetNTEXG210CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG210CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG210 Cards.
          These are supported on the FSP150CC XG210 product." 
    ::= { cmEntityObjects 30 }

ethernetNTEXG210CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG210CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG210CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG210CardTable 1 }

EthernetNTEXG210CardEntry ::= SEQUENCE {
    ethernetNTEXG210CardEntityIndex             PhysicalIndex,
    ethernetNTEXG210CardAdminState              AdminState,
    ethernetNTEXG210CardOperationalState        OperationalState,
    ethernetNTEXG210CardSecondaryState          SecondaryState,
    ethernetNTEXG210CardVoltage                 Integer32,
    ethernetNTEXG210CardTemperature             Integer32,
    ethernetNTEXG210CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG210CardRestartAction           RestartType,
    ethernetNTEXG210CardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG210CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG210 Card."
    ::= { ethernetNTEXG210CardEntry 1 }

ethernetNTEXG210CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG210 Card."
    ::= { ethernetNTEXG210CardEntry 2 }

ethernetNTEXG210CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG210 Card."
    ::= { ethernetNTEXG210CardEntry 3 }

ethernetNTEXG210CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG210 Card."
    ::= { ethernetNTEXG210CardEntry 4 }

ethernetNTEXG210CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG210 Card."
    ::= { ethernetNTEXG210CardEntry 5 }

ethernetNTEXG210CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG210 Card."
    ::= { ethernetNTEXG210CardEntry 6 }

ethernetNTEXG210CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG210 Card."
    ::= { ethernetNTEXG210CardEntry 7 }

ethernetNTEXG210CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG210 card."
    ::= { ethernetNTEXG210CardEntry 8 }

ethernetNTEXG210CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG210CardEntry 9 }


--
--Card - Ethernet XG210 XFP Port Expander Card 
--
ethernetXG1XCCCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetXG1XCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet XG210/SH1PCS XFP Port Expander Cards.
          These are supported on the FSP150CC XG210, SH1PCS products." 
    ::= { cmEntityObjects 31 }

ethernetXG1XCCCardEntry  OBJECT-TYPE
    SYNTAX      EthernetXG1XCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetXG1XCCCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetXG1XCCCardTable 1 }

EthernetXG1XCCCardEntry ::= SEQUENCE {
    ethernetXG1XCCCardEntityIndex             PhysicalIndex,
    ethernetXG1XCCCardAdminState              AdminState,
    ethernetXG1XCCCardOperationalState        OperationalState,
    ethernetXG1XCCCardSecondaryState          SecondaryState,
    ethernetXG1XCCCardVoltage                 Integer32,
    ethernetXG1XCCCardTemperature             Integer32,
    ethernetXG1XCCCardRestartAction           RestartType,
    ethernetXG1XCCCardStorageType             StorageType,
    ethernetXG1XCCCardRowStatus               RowStatus
}

ethernetXG1XCCCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet XG-1-XFP 
            Connector Card."
    ::= { ethernetXG1XCCCardEntry 1 }

ethernetXG1XCCCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet XG-1-XFP Connector Card."
    ::= { ethernetXG1XCCCardEntry 2 }

ethernetXG1XCCCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet XG-1-XFP Connector Card."
    ::= { ethernetXG1XCCCardEntry 3 }

ethernetXG1XCCCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet XG-1-XFP Connector Card."
    ::= { ethernetXG1XCCCardEntry 4 }

ethernetXG1XCCCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet XG-1-XFP Connector Card."
    ::= { ethernetXG1XCCCardEntry 5 }

ethernetXG1XCCCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet XG-1-XFP Connector Card."
    ::= { ethernetXG1XCCCardEntry 6 }

ethernetXG1XCCCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet XG-1-XFP Connector card."
    ::= { ethernetXG1XCCCardEntry 7 }

ethernetXG1XCCCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetXG1XCCCardEntry 8 }

ethernetXG1XCCCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetXG1XCCCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetXG1XCCCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetXG1XCCCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetXG1XCCCardEntry 9 }


--
--Card - Ethernet XG210 SFPPlus Port Expander Card 
--
ethernetXG1SCCCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetXG1SCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet XG210/SH1PCS SFP+ Port Expander Cards.
          These are supported on the FSP150CC XG210, SH1PCS products." 
    ::= { cmEntityObjects 32 }

ethernetXG1SCCCardEntry  OBJECT-TYPE
    SYNTAX      EthernetXG1SCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetXG1SCCCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetXG1SCCCardTable 1 }

EthernetXG1SCCCardEntry ::= SEQUENCE {
    ethernetXG1SCCCardEntityIndex             PhysicalIndex,
    ethernetXG1SCCCardAdminState              AdminState,
    ethernetXG1SCCCardOperationalState        OperationalState,
    ethernetXG1SCCCardSecondaryState          SecondaryState,
    ethernetXG1SCCCardVoltage                 Integer32,
    ethernetXG1SCCCardTemperature             Integer32,
    ethernetXG1SCCCardRestartAction           RestartType,
    ethernetXG1SCCCardStorageType             StorageType,
    ethernetXG1SCCCardRowStatus               RowStatus
}

ethernetXG1SCCCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet XG-1-SFP+ 
            Connector Card."
    ::= { ethernetXG1SCCCardEntry 1 }

ethernetXG1SCCCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet XG-1-SFP+ Connector Card."
    ::= { ethernetXG1SCCCardEntry 2 }

ethernetXG1SCCCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet XG-1-SFP+ Connector Card."
    ::= { ethernetXG1SCCCardEntry 3 }

ethernetXG1SCCCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet XG-1-SFP+ Connector Card."
    ::= { ethernetXG1SCCCardEntry 4 }

ethernetXG1SCCCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet XG-1-SFP+ Connector Card."
    ::= { ethernetXG1SCCCardEntry 5 }

ethernetXG1SCCCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet XG-1-SFP+ Connector Card."
    ::= { ethernetXG1SCCCardEntry 6 }

ethernetXG1SCCCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet XG-1-SFP+ Connector card."
    ::= { ethernetXG1SCCCardEntry 7 }

ethernetXG1SCCCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetXG1SCCCardEntry 8 }

ethernetXG1SCCCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetXG1SCCCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetXG1SCCCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetXG1SCCCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetXG1SCCCardEntry 9 }


--
--Card - Ethernet  - STM1-4-ET Card
--
PortCarrierType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This attribute is to show/set the SONET/SDH mode and OC3/OC12/STM1/STM4 speed for SONET/SDH."
    SYNTAX       INTEGER {
                   not-applicable (0),
                   t1 (1),
                   e1 (2),
                   t3 (3),
                   e3 (4),
                   oc3 (5),
                   oc12 (6),
                   stm1 (7),
                   stm4 (8)
                 }

ethernetOverOCSTMCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetOverOCSTMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on STM1-4-ET Cards."
    ::= { cmEntityObjects 33 }

ethernetOverOCSTMCardEntry  OBJECT-TYPE
    SYNTAX      EthernetOverOCSTMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetOverOCSTMCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetOverOCSTMCardTable 1 }

EthernetOverOCSTMCardEntry ::= SEQUENCE {
    ethernetOverOCSTMCardEntityIndex             PhysicalIndex,
    ethernetOverOCSTMCardAdminState              AdminState,
    ethernetOverOCSTMCardOperationalState        OperationalState,
    ethernetOverOCSTMCardSecondaryState          SecondaryState,
    ethernetOverOCSTMCardTemperature             Integer32,
    ethernetOverOCSTMCardSnmpDyingGaspEnabled    TruthValue,
    ethernetOverOCSTMCardRestartAction           RestartType,
    ethernetOverOCSTMCardStorageType             StorageType,
    ethernetOverOCSTMCardRowStatus               RowStatus,
    ethernetOverOCSTMCardForceOffLineAction      TruthValue,
    ethernetOverOCSTMCardMode                    PortCarrierType
}

ethernetOverOCSTMCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the STM1-4-ET Card."
    ::= { ethernetOverOCSTMCardEntry 1 }

ethernetOverOCSTMCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the STM1-4-ET Card."
    ::= { ethernetOverOCSTMCardEntry 2 }

ethernetOverOCSTMCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the STM1-4-ET Card."
    ::= { ethernetOverOCSTMCardEntry 3 }

ethernetOverOCSTMCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the STM1-4-ET Card."
    ::= { ethernetOverOCSTMCardEntry 4 }

ethernetOverOCSTMCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the STM1-4-ET Card."
    ::= { ethernetOverOCSTMCardEntry 5 }

ethernetOverOCSTMCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the STM1-4-ET Card."
    ::= { ethernetOverOCSTMCardEntry 6 }

ethernetOverOCSTMCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the STM1-4-ET card."
    ::= { ethernetOverOCSTMCardEntry 7 }

ethernetOverOCSTMCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetOverOCSTMCardEntry 8 }

ethernetOverOCSTMCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetOverOCSTMCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernet10x1GCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetOverOCSTMCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetOverOCSTMCardEntry 9 }

ethernetOverOCSTMCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { ethernetOverOCSTMCardEntry 10 }

ethernetOverOCSTMCardMode OBJECT-TYPE
    SYNTAX      PortCarrierType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "This attribute is to show/set the SONET/SDH mode and OC3/OC12/STM1/STM4 speed for SONET/SDH.."
    ::= { ethernetOverOCSTMCardEntry 11 }


--
--Pseudo Wire Card - OCN/STM  
--
pseudoWireOcnStmCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF PseudoWireOcnStmCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Pseudo Wire OCN STM Cards. 
          These are supported on the FSP150CC GE206v product." 
    ::= { cmEntityObjects 34 }

pseudoWireOcnStmCardEntry  OBJECT-TYPE
    SYNTAX      PseudoWireOcnStmCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the pseudoWireOcnStmCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { pseudoWireOcnStmCardTable 1 }

PseudoWireOcnStmCardEntry ::= SEQUENCE {
    pseudoWireOcnStmCardEntityIndex             PhysicalIndex,
    pseudoWireOcnStmCardAdminState              AdminState,
    pseudoWireOcnStmCardOperationalState        OperationalState,
    pseudoWireOcnStmCardSecondaryState          SecondaryState,
    pseudoWireOcnStmCardIpAddress               IpAddress,
    pseudoWireOcnStmCardMode                    PWE3OCNSTMCardMode,
    pseudoWireOcnStmCardVoltage                 Integer32,
    pseudoWireOcnStmCardTemperature             Integer32,
    pseudoWireOcnStmCardRestartAction           RestartType,
    pseudoWireOcnStmCardStorageType             StorageType,
    pseudoWireOcnStmCardRowStatus               RowStatus,
    pseudoWireOcnStmCardPSNEncapsulation        PSNEncapsulationMode,
    pseudoWireOcnStmCardFreqSourceType          TDMFrequencySourceType, 
    pseudoWireOcnStmCardFreqSource              VariablePointer,
    pseudoWireOcnStmCardForceOffLineAction      TruthValue
}

pseudoWireOcnStmCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 1 }

pseudoWireOcnStmCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 2 }

pseudoWireOcnStmCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 3 }

pseudoWireOcnStmCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 4 }

pseudoWireOcnStmCardIpAddress OBJECT-TYPE
    SYNTAX  IpAddress 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "IP Address associated with the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 5 }

pseudoWireOcnStmCardMode OBJECT-TYPE
    SYNTAX  PWE3OCNSTMCardMode 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Card Mode of with the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 6 }

pseudoWireOcnStmCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 7 }

pseudoWireOcnStmCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Pseudo Wire OCN/STM Card."
    ::= { pseudoWireOcnStmCardEntry 8 }

pseudoWireOcnStmCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Pseudo Wire OCN/STM card."
    ::= { pseudoWireOcnStmCardEntry 9 }

pseudoWireOcnStmCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { pseudoWireOcnStmCardEntry 10 }

pseudoWireOcnStmCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of pseudoWireOcnStmCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            pseudoWireOcnStmCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The pseudoWireOcnStmCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { pseudoWireOcnStmCardEntry 11 }

pseudoWireOcnStmCardPSNEncapsulation OBJECT-TYPE
    SYNTAX PSNEncapsulationMode 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "This object specifies Packet Switched Network demultiplexing layer 
              header type of the pseudo-wire packets."
    ::= { pseudoWireOcnStmCardEntry 12 }

pseudoWireOcnStmCardFreqSourceType OBJECT-TYPE
    SYNTAX TDMFrequencySourceType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the TDM Frequency source type of OC/STM card."
    ::= { pseudoWireOcnStmCardEntry 13 }

pseudoWireOcnStmCardFreqSource OBJECT-TYPE
    SYNTAX VariablePointer 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the TDM Frequency source of OC/STM card."
    ::= { pseudoWireOcnStmCardEntry 14 }

pseudoWireOcnStmCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { pseudoWireOcnStmCardEntry 15 }

--
--Pseudo Wire Card - T1/E1 
--
pseudoWireE1T1CardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF PseudoWireE1T1CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Pseudo Wire T1/E1 Cards. 
          These are supported on the FSP150CC GE206v product." 
    ::= { cmEntityObjects 35 }

pseudoWireE1T1CardEntry  OBJECT-TYPE
    SYNTAX      PseudoWireE1T1CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the pseudoWireE1T1CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { pseudoWireE1T1CardTable 1 }

PseudoWireE1T1CardEntry ::= SEQUENCE {
    pseudoWireE1T1CardEntityIndex             PhysicalIndex,
    pseudoWireE1T1CardAdminState              AdminState,
    pseudoWireE1T1CardOperationalState        OperationalState,
    pseudoWireE1T1CardSecondaryState          SecondaryState,
    pseudoWireE1T1CardIpAddress               IpAddress,
    pseudoWireE1T1CardMode                    PWE3E1T1CardMode,
    pseudoWireE1T1CardVoltage                 Integer32,
    pseudoWireE1T1CardTemperature             Integer32,
    pseudoWireE1T1CardRestartAction           RestartType,
    pseudoWireE1T1CardStorageType             StorageType,
    pseudoWireE1T1CardRowStatus               RowStatus,
    pseudoWireE1T1CardPSNEncapsulation        PSNEncapsulationMode
}

pseudoWireE1T1CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 1 }

pseudoWireE1T1CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 2 }

pseudoWireE1T1CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 3 }

pseudoWireE1T1CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 4 }

pseudoWireE1T1CardIpAddress OBJECT-TYPE
    SYNTAX  IpAddress 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "IP Address associated with the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 5 }

pseudoWireE1T1CardMode OBJECT-TYPE
    SYNTAX  PWE3E1T1CardMode 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Card Mode of with the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 6 }

pseudoWireE1T1CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 7 }

pseudoWireE1T1CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Pseudo Wire T1/E1 Card."
    ::= { pseudoWireE1T1CardEntry 8 }

pseudoWireE1T1CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Pseudo Wire T1/E1 card."
    ::= { pseudoWireE1T1CardEntry 9 }

pseudoWireE1T1CardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { pseudoWireE1T1CardEntry 10 }

pseudoWireE1T1CardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of pseudoWireE1T1CardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            pseudoWireE1T1CardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The pseudoWireE1T1CardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { pseudoWireE1T1CardEntry 11 }

pseudoWireE1T1CardPSNEncapsulation OBJECT-TYPE
    SYNTAX PSNEncapsulationMode 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "This object specifies Packet Switched Network demultiplexing layer 
              header type of the pseudo-wire packets."
    ::= { pseudoWireE1T1CardEntry 12 }

--
--Card - Ethernet  - 1x10G High Performance Card 
--
ethernet1x10GHighPerCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF Ethernet1x10GHighPerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet 1x10G High Performance Cards." 
    ::= { cmEntityObjects 36 }

ethernet1x10GHighPerCardEntry  OBJECT-TYPE
    SYNTAX      Ethernet1x10GHighPerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernet1x10GHighPerCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernet1x10GHighPerCardTable 1 }

Ethernet1x10GHighPerCardEntry ::= SEQUENCE {
    ethernet1x10GHighPerCardEntityIndex             PhysicalIndex,
    ethernet1x10GHighPerCardAdminState              AdminState,
    ethernet1x10GHighPerCardOperationalState        OperationalState,
    ethernet1x10GHighPerCardSecondaryState          SecondaryState,
    ethernet1x10GHighPerCardTemperature             Integer32,
    ethernet1x10GHighPerCardSnmpDyingGaspEnabled    TruthValue,
    ethernet1x10GHighPerCardRestartAction           RestartType,
    ethernet1x10GHighPerCardStorageType             StorageType,
    ethernet1x10GHighPerCardRowStatus               RowStatus,
    ethernet1x10GHighPerCardForceOffLineAction      TruthValue
}

ethernet1x10GHighPerCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet 1x10G High Performance Card."
    ::= { ethernet1x10GHighPerCardEntry 1 }

ethernet1x10GHighPerCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet 1x10G High Performance Card."
    ::= { ethernet1x10GHighPerCardEntry 2 }

ethernet1x10GHighPerCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet 1x10G High Performance Card."
    ::= { ethernet1x10GHighPerCardEntry 3 }

ethernet1x10GHighPerCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet 1x10G High Performance Card."
    ::= { ethernet1x10GHighPerCardEntry 4 }

ethernet1x10GHighPerCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet 1x10G High Performance Card."
    ::= { ethernet1x10GHighPerCardEntry 5 }

ethernet1x10GHighPerCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet 1x10G High Performance Card."
    ::= { ethernet1x10GHighPerCardEntry 6 }

ethernet1x10GHighPerCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet 1x10G High Performance card."
    ::= { ethernet1x10GHighPerCardEntry 7 }
    
ethernet1x10GHighPerCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernet1x10GHighPerCardEntry 8 }

ethernet1x10GHighPerCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernet1x10GHighPerCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetNTUCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernet1x10GHighPerCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernet1x10GHighPerCardEntry 9 }

ethernet1x10GHighPerCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { ethernet1x10GHighPerCardEntry 10 }


--
--Card - Ethernet  - 10x1G High Performance Card 
--
ethernet10x1GHighPerCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF Ethernet10x1GHighPerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet 10x1G High Performance Cards." 
    ::= { cmEntityObjects 37 }

ethernet10x1GHighPerCardEntry  OBJECT-TYPE
    SYNTAX      Ethernet10x1GHighPerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernet10x1GHighPerCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernet10x1GHighPerCardTable 1 }

Ethernet10x1GHighPerCardEntry ::= SEQUENCE {
    ethernet10x1GHighPerCardEntityIndex             PhysicalIndex,
    ethernet10x1GHighPerCardAdminState              AdminState,
    ethernet10x1GHighPerCardOperationalState        OperationalState,
    ethernet10x1GHighPerCardSecondaryState          SecondaryState,
    ethernet10x1GHighPerCardTemperature             Integer32,
    ethernet10x1GHighPerCardSnmpDyingGaspEnabled    TruthValue,
    ethernet10x1GHighPerCardRestartAction           RestartType,
    ethernet10x1GHighPerCardStorageType             StorageType,
    ethernet10x1GHighPerCardRowStatus               RowStatus,
    ethernet10x1GHighPerCardForceOffLineAction      TruthValue
}

ethernet10x1GHighPerCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet 10x1G High Performance Card."
    ::= { ethernet10x1GHighPerCardEntry 1 }

ethernet10x1GHighPerCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet 10x1G High Performance Card."
    ::= { ethernet10x1GHighPerCardEntry 2 }

ethernet10x1GHighPerCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet 10x1G High Performance Card."
    ::= { ethernet10x1GHighPerCardEntry 3 }

ethernet10x1GHighPerCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet 10x1G High Performance Card."
    ::= { ethernet10x1GHighPerCardEntry 4 }

ethernet10x1GHighPerCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet 10x1G High Performance Card."
    ::= { ethernet10x1GHighPerCardEntry 5 }

ethernet10x1GHighPerCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet 10x1G High Performance Card."
    ::= { ethernet10x1GHighPerCardEntry 6 }

ethernet10x1GHighPerCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet 10x1G High Performance card."
    ::= { ethernet10x1GHighPerCardEntry 7 }
    
ethernet10x1GHighPerCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernet10x1GHighPerCardEntry 8 }

ethernet10x1GHighPerCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernet10x1GHighPerCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernet10x1GHighPerCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernet10x1GHighPerCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernet10x1GHighPerCardEntry 9 }

ethernet10x1GHighPerCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { ethernet10x1GHighPerCardEntry 10 }


--
--Card - Ethernet NTE - T1804 Card 
--
ethernetNTET1804CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTET1804CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE T1804 Cards.
          These are supported on the FSP150CC T1804 product." 
    ::= { cmEntityObjects 38 }

ethernetNTET1804CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTET1804CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTET1804CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTET1804CardTable 1 }

EthernetNTET1804CardEntry ::= SEQUENCE {
    ethernetNTET1804CardEntityIndex             PhysicalIndex,
    ethernetNTET1804CardAdminState              AdminState,
    ethernetNTET1804CardOperationalState        OperationalState,
    ethernetNTET1804CardSecondaryState          SecondaryState,
    ethernetNTET1804CardVoltage                 Integer32,
    ethernetNTET1804CardTemperature             Integer32,
    ethernetNTET1804CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTET1804CardRestartAction           RestartType,
    ethernetNTET1804CardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTET1804CardMode                    PortCarrierType
}

ethernetNTET1804CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE T1804 Card."
    ::= { ethernetNTET1804CardEntry 1 }

ethernetNTET1804CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE T1804 Card."
    ::= { ethernetNTET1804CardEntry 2 }

ethernetNTET1804CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE T1804 Card."
    ::= { ethernetNTET1804CardEntry 3 }

ethernetNTET1804CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE T1804 Card."
    ::= { ethernetNTET1804CardEntry 4 }

ethernetNTET1804CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112S Card."
    ::= { ethernetNTET1804CardEntry 5 }

ethernetNTET1804CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE T1804 Card."
    ::= { ethernetNTET1804CardEntry 6 }

ethernetNTET1804CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE T1804 Card."
    ::= { ethernetNTET1804CardEntry 7 }

ethernetNTET1804CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE T1804 card."
    ::= { ethernetNTET1804CardEntry 8 }

ethernetNTET1804CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTET1804CardEntry 9 }

ethernetNTET1804CardMode OBJECT-TYPE
    SYNTAX      PortCarrierType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "This attribute is to show/set E1/T1 mode of the NET T1804 card."
    ::= { ethernetNTET1804CardEntry 10 }


--
--Card - Ethernet NTE - T3204 Card 
--
ethernetNTET3204CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTET3204CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE T3204 Cards.
          These are supported on the FSP150CC T3204 product." 
    ::= { cmEntityObjects 39 }

ethernetNTET3204CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTET3204CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTET3204CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTET3204CardTable 1 }

EthernetNTET3204CardEntry ::= SEQUENCE {
    ethernetNTET3204CardEntityIndex             PhysicalIndex,
    ethernetNTET3204CardAdminState              AdminState,
    ethernetNTET3204CardOperationalState        OperationalState,
    ethernetNTET3204CardSecondaryState          SecondaryState,
    ethernetNTET3204CardVoltage                 Integer32,
    ethernetNTET3204CardTemperature             Integer32,
    ethernetNTET3204CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTET3204CardRestartAction           RestartType,
    ethernetNTET3204CardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTET3204CardMode                    PortCarrierType
}

ethernetNTET3204CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE T3204 Card."
    ::= { ethernetNTET3204CardEntry 1 }

ethernetNTET3204CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE T3204 Card."
    ::= { ethernetNTET3204CardEntry 2 }

ethernetNTET3204CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE T3204 Card."
    ::= { ethernetNTET3204CardEntry 3 }

ethernetNTET3204CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE T3204 Card."
    ::= { ethernetNTET3204CardEntry 4 }

ethernetNTET3204CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112S Card."
    ::= { ethernetNTET3204CardEntry 5 }

ethernetNTET3204CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE T3204 Card."
    ::= { ethernetNTET3204CardEntry 6 }

ethernetNTET3204CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE T3204 Card."
    ::= { ethernetNTET3204CardEntry 7 }

ethernetNTET3204CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE T3204 card."
    ::= { ethernetNTET3204CardEntry 8 }

ethernetNTET3204CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTET3204CardEntry 9 }

ethernetNTET3204CardMode OBJECT-TYPE
    SYNTAX      PortCarrierType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "This attribute is to show/set E3/T3 mode of the NET T3204 card."
    ::= { ethernetNTET3204CardEntry 10 }


--
--Card - Ethernet NTE - SyncProbe 
--
ethernetNTEGESyncProbeCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGESyncProbeCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE-GE SyncProbe Card.
          These are supported on the FSP150CC GE SyncProbe product." 
    ::= { cmEntityObjects 40 }

ethernetNTEGESyncProbeCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGESyncProbeCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGESyncProbeCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGESyncProbeCardTable 1 }

EthernetNTEGESyncProbeCardEntry ::= SEQUENCE {
    ethernetNTEGESyncProbeCardEntityIndex             PhysicalIndex,
    ethernetNTEGESyncProbeCardAdminState              AdminState,
    ethernetNTEGESyncProbeCardOperationalState        OperationalState,
    ethernetNTEGESyncProbeCardSecondaryState          SecondaryState,
    ethernetNTEGESyncProbeCardVoltage                 Integer32,
    ethernetNTEGESyncProbeCardTemperature             Integer32,
    ethernetNTEGESyncProbeCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGESyncProbeCardRestartAction           RestartType,
    ethernetNTEGESyncProbeCardFineGrainedPmInterval   CmPmIntervalType 
}

ethernetNTEGESyncProbeCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE SyncProbe Card."
    ::= { ethernetNTEGESyncProbeCardEntry 1 }

ethernetNTEGESyncProbeCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE SyncProbe Card."
    ::= { ethernetNTEGESyncProbeCardEntry 2 }

ethernetNTEGESyncProbeCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE SyncProbe Card."
    ::= { ethernetNTEGESyncProbeCardEntry 3 }

ethernetNTEGESyncProbeCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE SyncProbe Card."
    ::= { ethernetNTEGESyncProbeCardEntry 4 }

ethernetNTEGESyncProbeCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE SyncProbe Card."
    ::= { ethernetNTEGESyncProbeCardEntry 5 }

ethernetNTEGESyncProbeCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE SyncProbe Card."
    ::= { ethernetNTEGESyncProbeCardEntry 6 }

ethernetNTEGESyncProbeCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE SyncProbe Card."
    ::= { ethernetNTEGESyncProbeCardEntry 7 }

ethernetNTEGESyncProbeCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE SyncProbe card."
    ::= { ethernetNTEGESyncProbeCardEntry 8 }

ethernetNTEGESyncProbeCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
      "Allows specification of the fine-grained PM interval at the card level.
       This applies to all monitored PM entities. The default value of this
       attribute is interval-15min.  Valid values are interval-5min and interval-15min.
       interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGESyncProbeCardEntry 9 }

--
--Card - Ethernet XG210 Optical Port Expander Card 
--
ethernetGE8SCCCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetGE8SCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet XG210/SH1PCS Optical Port Expander Cards.
          These are supported on the FSP150CC XG210, SH1PCS product." 
    ::= { cmEntityObjects 41 }

ethernetGE8SCCCardEntry  OBJECT-TYPE
    SYNTAX      EthernetGE8SCCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetGE8SCCCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetGE8SCCCardTable 1 }

EthernetGE8SCCCardEntry ::= SEQUENCE {
    ethernetGE8SCCCardEntityIndex             PhysicalIndex,
    ethernetGE8SCCCardAdminState              AdminState,
    ethernetGE8SCCCardOperationalState        OperationalState,
    ethernetGE8SCCCardSecondaryState          SecondaryState,
    ethernetGE8SCCCardVoltage                 Integer32,
    ethernetGE8SCCCardTemperature             Integer32,
    ethernetGE8SCCCardRestartAction           RestartType,
    ethernetGE8SCCCardStorageType             StorageType,
    ethernetGE8SCCCardRowStatus               RowStatus
}

ethernetGE8SCCCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Entity Index from ENTITY-MIB for the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8SCCCardEntry 1 }

ethernetGE8SCCCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8SCCCardEntry 2 }

ethernetGE8SCCCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8SCCCardEntry 3 }

ethernetGE8SCCCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8SCCCardEntry 4 }

ethernetGE8SCCCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8SCCCardEntry 5 }

ethernetGE8SCCCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8SCCCardEntry 6 }

ethernetGE8SCCCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet GE-8S Connector card."
    ::= { ethernetGE8SCCCardEntry 7 }

ethernetGE8SCCCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetGE8SCCCardEntry 8 }

ethernetGE8SCCCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetGE8SCCCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetGE8SCCCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetGE8SCCCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetGE8SCCCardEntry 9 }

--
--Card - Ethernet NTE - GE 114H Card 
--
ethernetNTEGE114HCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114HCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114H Cards.
          These are supported on the FSP150CC GE114H product." 
    ::= { cmEntityObjects 42 }

ethernetNTEGE114HCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114HCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114HCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114HCardTable 1 }

EthernetNTEGE114HCardEntry ::= SEQUENCE {
    ethernetNTEGE114HCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114HCardAdminState              AdminState,
    ethernetNTEGE114HCardOperationalState        OperationalState,
    ethernetNTEGE114HCardSecondaryState          SecondaryState,
    ethernetNTEGE114HCardVoltage                 Integer32,
    ethernetNTEGE114HCardTemperature             Integer32,
    ethernetNTEGE114HCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114HCardRestartAction           RestartType,
    ethernetNTEGE114HCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114HCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114HCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114HCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114H Card."
    ::= { ethernetNTEGE114HCardEntry 1 }

ethernetNTEGE114HCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114H Card."
    ::= { ethernetNTEGE114HCardEntry 2 }

ethernetNTEGE114HCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114H Card."
    ::= { ethernetNTEGE114HCardEntry 3 }

ethernetNTEGE114HCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114H Card."
    ::= { ethernetNTEGE114HCardEntry 4 }

ethernetNTEGE114HCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE114HCardEntry 5 }

ethernetNTEGE114HCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114H Card."
    ::= { ethernetNTEGE114HCardEntry 6 }

ethernetNTEGE114HCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114H Card."
    ::= { ethernetNTEGE114HCardEntry 7 }

ethernetNTEGE114HCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114H card."
    ::= { ethernetNTEGE114HCardEntry 8 }

ethernetNTEGE114HCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114HCardEntry 9 }

ethernetNTEGE114HCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114H card."
    ::= { ethernetNTEGE114HCardEntry 10 }

ethernetNTEGE114HCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114H card."
    ::= { ethernetNTEGE114HCardEntry 11 }
    
--
--Card - Ethernet NTE - GE 114PH Card 
--
ethernetNTEGE114PHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114PHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114PH Cards.
          These are supported on the FSP150CC GE114PH product." 
    ::= { cmEntityObjects 43 }

ethernetNTEGE114PHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114PHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114PHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114PHCardTable 1 }

EthernetNTEGE114PHCardEntry ::= SEQUENCE {
    ethernetNTEGE114PHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114PHCardAdminState              AdminState,
    ethernetNTEGE114PHCardOperationalState        OperationalState,
    ethernetNTEGE114PHCardSecondaryState          SecondaryState,
    ethernetNTEGE114PHCardVoltage                 Integer32,
    ethernetNTEGE114PHCardTemperature             Integer32,
    ethernetNTEGE114PHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114PHCardRestartAction           RestartType,
    ethernetNTEGE114PHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114PHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114PHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114PHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114PH Card."
    ::= { ethernetNTEGE114PHCardEntry 1 }

ethernetNTEGE114PHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114PH Card."
    ::= { ethernetNTEGE114PHCardEntry 2 }

ethernetNTEGE114PHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114PH Card."
    ::= { ethernetNTEGE114PHCardEntry 3 }

ethernetNTEGE114PHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114PH Card."
    ::= { ethernetNTEGE114PHCardEntry 4 }

ethernetNTEGE114PHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE114PHCardEntry 5 }

ethernetNTEGE114PHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114PH Card."
    ::= { ethernetNTEGE114PHCardEntry 6 }

ethernetNTEGE114PHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114PH Card."
    ::= { ethernetNTEGE114PHCardEntry 7 }

ethernetNTEGE114PHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114PH card."
    ::= { ethernetNTEGE114PHCardEntry 8 }

ethernetNTEGE114PHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114PHCardEntry 9 }

ethernetNTEGE114PHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114PH card."
    ::= { ethernetNTEGE114PHCardEntry 10 }

ethernetNTEGE114PHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114PH card."
    ::= { ethernetNTEGE114PHCardEntry 11 }

--
--Card - Ethernet  - 36x100M Card 
--
ethernetFE36ECardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetFE36ECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet  36x100M Cards." 
    ::= { cmEntityObjects 44 }

ethernetFE36ECardEntry  OBJECT-TYPE
    SYNTAX      EthernetFE36ECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetFE36ECardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetFE36ECardTable 1 }

EthernetFE36ECardEntry ::= SEQUENCE {
    ethernetFE36ECardEntityIndex             PhysicalIndex,
    ethernetFE36ECardAdminState              AdminState,
    ethernetFE36ECardOperationalState        OperationalState,
    ethernetFE36ECardSecondaryState          SecondaryState,
    ethernetFE36ECardTemperature             Integer32,
    ethernetFE36ECardRestartAction           RestartType,
    ethernetFE36ECardStorageType             StorageType,
    ethernetFE36ECardRowStatus               RowStatus,
    ethernetFE36ECardForceOffLineAction      TruthValue,
    ethernetFE36ECard8023azEnabled           TruthValue
}

ethernetFE36ECardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet FE36E Card."
    ::= { ethernetFE36ECardEntry 1 }

ethernetFE36ECardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet FE36E Card."
    ::= { ethernetFE36ECardEntry 2 }

ethernetFE36ECardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet FE36E Card."
    ::= { ethernetFE36ECardEntry 3 }

ethernetFE36ECardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet FE36E Card."
    ::= { ethernetFE36ECardEntry 4 }

ethernetFE36ECardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet FE36E Card."
    ::= { ethernetFE36ECardEntry 5 }

ethernetFE36ECardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet FE36E card."
    ::= { ethernetFE36ECardEntry 6 }
    
ethernetFE36ECardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetFE36ECardEntry 7 }

ethernetFE36ECardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetFE36ECardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetNTUCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetFE36ECardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetFE36ECardEntry 8 }

ethernetFE36ECardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { ethernetFE36ECardEntry 9 }

ethernetFE36ECard8023azEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "This object describes whether enable 802.3az energy efficient."
    ::= { ethernetFE36ECardEntry 10 }

--
--Card - Ethernet NTE - GE 114SH Card 
--
ethernetNTEGE114SHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114SHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114SH Cards.
          These are supported on the FSP150CC GE114SH product." 
    ::= { cmEntityObjects 45 }

ethernetNTEGE114SHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114SHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114SHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114SHCardTable 1 }

EthernetNTEGE114SHCardEntry ::= SEQUENCE {
    ethernetNTEGE114SHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114SHCardAdminState              AdminState,
    ethernetNTEGE114SHCardOperationalState        OperationalState,
    ethernetNTEGE114SHCardSecondaryState          SecondaryState,
    ethernetNTEGE114SHCardVoltage                 Integer32,
    ethernetNTEGE114SHCardTemperature             Integer32,
    ethernetNTEGE114SHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114SHCardRestartAction           RestartType,
    ethernetNTEGE114SHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114SHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114SHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114SHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114SH Card."
    ::= { ethernetNTEGE114SHCardEntry 1 }

ethernetNTEGE114SHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114SH Card."
    ::= { ethernetNTEGE114SHCardEntry 2 }

ethernetNTEGE114SHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114SH Card."
    ::= { ethernetNTEGE114SHCardEntry 3 }

ethernetNTEGE114SHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114SH Card."
    ::= { ethernetNTEGE114SHCardEntry 4 }

ethernetNTEGE114SHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112 Card."
    ::= { ethernetNTEGE114SHCardEntry 5 }

ethernetNTEGE114SHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114SH Card."
    ::= { ethernetNTEGE114SHCardEntry 6 }

ethernetNTEGE114SHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114SH Card."
    ::= { ethernetNTEGE114SHCardEntry 7 }

ethernetNTEGE114SHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114SH card."
    ::= { ethernetNTEGE114SHCardEntry 8 }

ethernetNTEGE114SHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114SHCardEntry 9 }

ethernetNTEGE114SHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114SH card."
    ::= { ethernetNTEGE114SHCardEntry 10 }

ethernetNTEGE114SHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114SH card."
    ::= { ethernetNTEGE114SHCardEntry 11 }


--
--Card - Ethernet NTE - GE 114S Card 
--
ethernetNTEGE114SCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114SCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114S Cards.
          These are supported on the FSP150CC GE114S product." 
    ::= { cmEntityObjects 46 }

ethernetNTEGE114SCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114SCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114SCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114SCardTable 1 }

EthernetNTEGE114SCardEntry ::= SEQUENCE {
    ethernetNTEGE114SCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114SCardAdminState              AdminState,
    ethernetNTEGE114SCardOperationalState        OperationalState,
    ethernetNTEGE114SCardSecondaryState          SecondaryState,
    ethernetNTEGE114SCardVoltage                 Integer32,
    ethernetNTEGE114SCardTemperature             Integer32,
    ethernetNTEGE114SCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114SCardRestartAction           RestartType,
    ethernetNTEGE114SCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114SCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114SCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114SCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114S Card."
    ::= { ethernetNTEGE114SCardEntry 1 }

ethernetNTEGE114SCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114S Card."
    ::= { ethernetNTEGE114SCardEntry 2 }

ethernetNTEGE114SCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114S Card."
    ::= { ethernetNTEGE114SCardEntry 3 }

ethernetNTEGE114SCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114S Card."
    ::= { ethernetNTEGE114SCardEntry 4 }

ethernetNTEGE114SCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112S Card."
    ::= { ethernetNTEGE114SCardEntry 5 }

ethernetNTEGE114SCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114S Card."
    ::= { ethernetNTEGE114SCardEntry 6 }

ethernetNTEGE114SCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114S Card."
    ::= { ethernetNTEGE114SCardEntry 7 }

ethernetNTEGE114SCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114S card."
    ::= { ethernetNTEGE114SCardEntry 8 }

ethernetNTEGE114SCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114SCardEntry 9 }

ethernetNTEGE114SCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114S card."
    ::= { ethernetNTEGE114SCardEntry 10 }

ethernetNTEGE114SCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114S card."
    ::= { ethernetNTEGE114SCardEntry 11 }

--
--Card - STU High performance Card 
--
stuHighPerCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF StuHighPerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on STU Cards." 
    ::= { cmEntityObjects 47 }

stuHighPerCardEntry  OBJECT-TYPE
    SYNTAX      StuHighPerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the stuHighPerCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { stuHighPerCardTable 1 }

StuHighPerCardEntry ::= SEQUENCE {
    stuHighPerCardEntityIndex             PhysicalIndex,
    stuHighPerCardAdminState              AdminState,
    stuHighPerCardOperationalState        OperationalState,
    stuHighPerCardSecondaryState          SecondaryState,
    stuHighPerCardTemperature             Integer32,
    stuHighPerCardRestartAction           RestartType,
    stuHighPerCardStorageType             StorageType,
    stuHighPerCardRowStatus               RowStatus,
    stuHighPerCardForceOffLineAction      TruthValue
}

stuHighPerCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the STU High Performance Card."
    ::= { stuHighPerCardEntry 1 }

stuHighPerCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Stu High Performance Card."
    ::= { stuHighPerCardEntry 2 }

stuHighPerCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Stu High Performance Card."
    ::= { stuHighPerCardEntry 3 }

stuHighPerCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Stu High Performance Card."
    ::= { stuHighPerCardEntry 4 }

stuHighPerCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Stu High Performance Card."
    ::= { stuHighPerCardEntry 5 }

stuHighPerCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Stu High Performance card."
    ::= { stuHighPerCardEntry 6 }
    
stuHighPerCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { stuHighPerCardEntry 7 }

stuHighPerCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of stuHighPerCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            stuRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The stuHighPerCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { stuHighPerCardEntry 8 }

stuHighPerCardForceOffLineAction OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "On some condition, user wishes to make one card out of service 
         and hold the reset signal of the card via AMI. When force offline is enabled, 
         reset signal is held; When force offline is disabled, reset signal is released."
    ::= { stuHighPerCardEntry 9 }

--
--Card - STI HighPer Card
--
stiHighPerTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF StiHighPerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on STI within the Shelf." 
    ::= { cmEntityObjects 48 }

stiHighPerEntry  OBJECT-TYPE
    SYNTAX      StiHighPerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the stiTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { stiHighPerTable 1 }

StiHighPerEntry ::= SEQUENCE {
    stiHighPerEntityIndex       PhysicalIndex,
    stiHighPerAdminState        AdminState,
    stiHighPerOperationalState  OperationalState,
    stiHighPerSecondaryState    SecondaryState,
    stiHighPerTemperature       Integer32,
    stiHighPerStorageType       StorageType,
    stiHighPerRowStatus         RowStatus
}

stiHighPerEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the STI High Per card."
    ::= { stiHighPerEntry 1 }

stiHighPerAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the STI."
    ::= { stiHighPerEntry 2 }

stiHighPerOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the STI."
    ::= { stiHighPerEntry 3 }

stiHighPerSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the STI."
    ::= { stiHighPerEntry 4 }

stiHighPerTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the STI."
    ::= { stiHighPerEntry 5 }

stiHighPerStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { stiHighPerEntry 6 }

stiHighPerRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of stiRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            stiRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The stiRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { stiHighPerEntry 7 }

--
--Card - Ethernet XG210 Electrical Port Expander Card 
--
ethernetGE8ECCCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetGE8ECCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet XG210 Optical Port Expander Cards.
          These are supported on the FSP150CC XG210, SH1PCS products." 
    ::= { cmEntityObjects 49 }

ethernetGE8ECCCardEntry  OBJECT-TYPE
    SYNTAX      EthernetGE8ECCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetGE8ECCCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetGE8ECCCardTable 1 }

EthernetGE8ECCCardEntry ::= SEQUENCE {
    ethernetGE8ECCCardEntityIndex             PhysicalIndex,
    ethernetGE8ECCCardAdminState              AdminState,
    ethernetGE8ECCCardOperationalState        OperationalState,
    ethernetGE8ECCCardSecondaryState          SecondaryState,
    ethernetGE8ECCCardVoltage                 Integer32,
    ethernetGE8ECCCardTemperature             Integer32,
    ethernetGE8ECCCardRestartAction           RestartType,
    ethernetGE8ECCCardStorageType             StorageType,
    ethernetGE8ECCCardRowStatus               RowStatus
}

ethernetGE8ECCCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Entity Index from ENTITY-MIB for the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8ECCCardEntry 1 }

ethernetGE8ECCCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8ECCCardEntry 2 }

ethernetGE8ECCCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8ECCCardEntry 3 }

ethernetGE8ECCCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8ECCCardEntry 4 }

ethernetGE8ECCCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8ECCCardEntry 5 }

ethernetGE8ECCCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet GE-8S Connector Card."
    ::= { ethernetGE8ECCCardEntry 6 }

ethernetGE8ECCCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet GE-8S Connector card."
    ::= { ethernetGE8ECCCardEntry 7 }

ethernetGE8ECCCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetGE8ECCCardEntry 8 }

ethernetGE8ECCCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetGE8ECCCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetGE8ECCCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetGE8ECCCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetGE8ECCCardEntry 9 }

networkElementLLDPParamsTable  OBJECT-TYPE
    SYNTAX          SEQUENCE OF NetworkElementLLDPParamsEntry
    MAX-ACCESS  not-accessible
    STATUS          current
    DESCRIPTION
         "Entries may be auto discovered, or can be explicitly created by 
          SNMP Manager.  Each remotely discovered shelf is represented as 
          a row in this table." 
    ::= { cmEntityObjects 50 }

networkElementLLDPParamsEntry  OBJECT-TYPE
    SYNTAX          NetworkElementLLDPParamsEntry
    MAX-ACCESS  not-accessible
    STATUS          current
    DESCRIPTION
            "A conceptual row in the networkElementLLDPParamsTable."
    AUGMENTS { networkElementEntry }
    ::= { networkElementLLDPParamsTable 1 }


NetworkElementLLDPParamsEntry ::= SEQUENCE {
    neLLDPParamsLLDPEnableAction                LLDPEnableAction
}

neLLDPParamsLLDPEnableAction OBJECT-TYPE
    SYNTAX          LLDPEnableAction  
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "This object allows user enable or disable LLDP on all the ports in the NE."
    ::= { networkElementLLDPParamsEntry 1 }

--
--Card - Ethernet NTE - SH1PCS Card 
--
ethernetNTESH1PCSCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTESH1PCSCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE SH1PCS Cards.
          These are supported on the FSP150CC SH1PCS product." 
    ::= { cmEntityObjects 51 }

ethernetNTESH1PCSCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTESH1PCSCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTESH1PCSCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTESH1PCSCardTable 1 }

EthernetNTESH1PCSCardEntry ::= SEQUENCE {
    ethernetNTESH1PCSCardEntityIndex             PhysicalIndex,
    ethernetNTESH1PCSCardAdminState              AdminState,
    ethernetNTESH1PCSCardOperationalState        OperationalState,
    ethernetNTESH1PCSCardSecondaryState          SecondaryState,
    ethernetNTESH1PCSCardVoltage                 Integer32,
    ethernetNTESH1PCSCardTemperature             Integer32,
    ethernetNTESH1PCSCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTESH1PCSCardRestartAction           RestartType,
    ethernetNTESH1PCSCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTESH1PCSCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE SH1PCS Card."
    ::= { ethernetNTESH1PCSCardEntry 1 }

ethernetNTESH1PCSCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE SH1PCS Card."
    ::= { ethernetNTESH1PCSCardEntry 2 }

ethernetNTESH1PCSCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE SH1PCS Card."
    ::= { ethernetNTESH1PCSCardEntry 3 }

ethernetNTESH1PCSCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE SH1PCS Card."
    ::= { ethernetNTESH1PCSCardEntry 4 }

ethernetNTESH1PCSCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE SH1PCS Card."
    ::= { ethernetNTESH1PCSCardEntry 5 }

ethernetNTESH1PCSCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE SH1PCS Card."
    ::= { ethernetNTESH1PCSCardEntry 6 }

ethernetNTESH1PCSCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE SH1PCS Card."
    ::= { ethernetNTESH1PCSCardEntry 7 }

ethernetNTESH1PCSCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE SH1PCS card."
    ::= { ethernetNTESH1PCSCardEntry 8 }

ethernetNTESH1PCSCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTESH1PCSCardEntry 9 }


--
--Card - Ethernet NTE - OSA5411 
--
ethernetNTEOSA5411CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEOSA5411CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE OSA5411 Card.
          These are supported on the FSP150CC OSA5411 product." 
    ::= { cmEntityObjects 52 }

ethernetNTEOSA5411CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEOSA5411CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEOSA5411CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEOSA5411CardTable 1 }

EthernetNTEOSA5411CardEntry ::= SEQUENCE {
    ethernetNTEOSA5411CardEntityIndex             PhysicalIndex,
    ethernetNTEOSA5411CardAdminState              AdminState,
    ethernetNTEOSA5411CardOperationalState        OperationalState,
    ethernetNTEOSA5411CardSecondaryState          SecondaryState,
    ethernetNTEOSA5411CardVoltage                 Integer32,
    ethernetNTEOSA5411CardTemperature             Integer32,
    ethernetNTEOSA5411CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEOSA5411CardRestartAction           RestartType,
    ethernetNTEOSA5411CardFineGrainedPmInterval   CmPmIntervalType 
}

ethernetNTEOSA5411CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE OSA5411 Card."
    ::= { ethernetNTEOSA5411CardEntry 1 }

ethernetNTEOSA5411CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE OSA5411 Card."
    ::= { ethernetNTEOSA5411CardEntry 2 }

ethernetNTEOSA5411CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE OSA5411 Card."
    ::= { ethernetNTEOSA5411CardEntry 3 }

ethernetNTEOSA5411CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE OSA5411 Card."
    ::= { ethernetNTEOSA5411CardEntry 4 }

ethernetNTEOSA5411CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE OSA5411 Card."
    ::= { ethernetNTEOSA5411CardEntry 5 }

ethernetNTEOSA5411CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE OSA5411 Card."
    ::= { ethernetNTEOSA5411CardEntry 6 }

ethernetNTEOSA5411CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE OSA5411 Card."
    ::= { ethernetNTEOSA5411CardEntry 7 }

ethernetNTEOSA5411CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE OSA5411 card."
    ::= { ethernetNTEOSA5411CardEntry 8 }

ethernetNTEOSA5411CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
      "Allows specification of the fine-grained PM interval at the card level.
       This applies to all monitored PM entities. The default value of this
       attribute is interval-15min.  Valid values are interval-5min and interval-15min.
       interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEOSA5411CardEntry 9 }

--
--Card - Ethernet NTE - GE 112Pro Card 
--
ethernetNTEGE112ProCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE112ProCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 53 }

ethernetNTEGE112ProCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE112ProCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE112ProCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE112ProCardTable 1 }

EthernetNTEGE112ProCardEntry ::= SEQUENCE {
    ethernetNTEGE112ProCardEntityIndex             PhysicalIndex,
    ethernetNTEGE112ProCardAdminState              AdminState,
    ethernetNTEGE112ProCardOperationalState        OperationalState,
    ethernetNTEGE112ProCardSecondaryState          SecondaryState,
    ethernetNTEGE112ProCardVoltage                 Integer32,
    ethernetNTEGE112ProCardTemperature             Integer32,
    ethernetNTEGE112ProCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE112ProCardRestartAction           RestartType,
    ethernetNTEGE112ProCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE112ProCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE112ProCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE112ProCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE112ProCardEntry 1 }

ethernetNTEGE112ProCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE112ProCardEntry 2 }

ethernetNTEGE112ProCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE112Pro Card."
    ::= { ethernetNTEGE112ProCardEntry 3 }

ethernetNTEGE112ProCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE112Pro Card."
    ::= { ethernetNTEGE112ProCardEntry 4 }

ethernetNTEGE112ProCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112Pro Card."
    ::= { ethernetNTEGE112ProCardEntry 5 }

ethernetNTEGE112ProCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE112Pro Card."
    ::= { ethernetNTEGE112ProCardEntry 6 }

ethernetNTEGE112ProCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE112Pro Card."
    ::= { ethernetNTEGE112ProCardEntry 7 }

ethernetNTEGE112ProCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112Pro card."
    ::= { ethernetNTEGE112ProCardEntry 8 }

ethernetNTEGE112ProCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE112ProCardEntry 9 }

ethernetNTEGE112ProCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE112Pro card."
    ::= { ethernetNTEGE112ProCardEntry 10 }

ethernetNTEGE112ProCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112Pro card."
    ::= { ethernetNTEGE112ProCardEntry 11 }

--
--Card - Ethernet NTE - GE 112ProM Card 
--
ethernetNTEGE112ProMCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE112ProMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 54 }

ethernetNTEGE112ProMCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE112ProMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE112ProMCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE112ProMCardTable 1 }

EthernetNTEGE112ProMCardEntry ::= SEQUENCE {
    ethernetNTEGE112ProMCardEntityIndex             PhysicalIndex,
    ethernetNTEGE112ProMCardAdminState              AdminState,
    ethernetNTEGE112ProMCardOperationalState        OperationalState,
    ethernetNTEGE112ProMCardSecondaryState          SecondaryState,
    ethernetNTEGE112ProMCardVoltage                 Integer32,
    ethernetNTEGE112ProMCardTemperature             Integer32,
    ethernetNTEGE112ProMCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE112ProMCardRestartAction           RestartType,
    ethernetNTEGE112ProMCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE112ProMCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE112ProMCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE112ProMCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE112ProMCardEntry 1 }

ethernetNTEGE112ProMCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE112ProMCardEntry 2 }

ethernetNTEGE112ProMCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE112ProM Card."
    ::= { ethernetNTEGE112ProMCardEntry 3 }

ethernetNTEGE112ProMCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE112ProM Card."
    ::= { ethernetNTEGE112ProMCardEntry 4 }

ethernetNTEGE112ProMCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112ProM Card."
    ::= { ethernetNTEGE112ProMCardEntry 5 }

ethernetNTEGE112ProMCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE112ProM Card."
    ::= { ethernetNTEGE112ProMCardEntry 6 }

ethernetNTEGE112ProMCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE112ProM Card."
    ::= { ethernetNTEGE112ProMCardEntry 7 }

ethernetNTEGE112ProMCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112ProM card."
    ::= { ethernetNTEGE112ProMCardEntry 8 }

ethernetNTEGE112ProMCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE112ProMCardEntry 9 }

ethernetNTEGE112ProMCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE112ProM card."
    ::= { ethernetNTEGE112ProMCardEntry 10 }

ethernetNTEGE112ProMCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112ProM card."
    ::= { ethernetNTEGE112ProMCardEntry 11 }


--
--Card - Ethernet NTE - XG210C (Crypto) Card 
--
ethernetNTEXG210CCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG210CCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG210 Cards.
          These are supported on the FSP150CC XG210 product." 
    ::= { cmEntityObjects 55 }

ethernetNTEXG210CCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG210CCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG210CCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG210CCardTable 1 }

EthernetNTEXG210CCardEntry ::= SEQUENCE {
    ethernetNTEXG210CCardEntityIndex             PhysicalIndex,
    ethernetNTEXG210CCardAdminState              AdminState,
    ethernetNTEXG210CCardOperationalState        OperationalState,
    ethernetNTEXG210CCardSecondaryState          SecondaryState,
    ethernetNTEXG210CCardVoltage                 Integer32,
    ethernetNTEXG210CCardTemperature             Integer32,
    ethernetNTEXG210CCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG210CCardRestartAction           RestartType,
    ethernetNTEXG210CCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG210CCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG210C Card."
    ::= { ethernetNTEXG210CCardEntry 1 }

ethernetNTEXG210CCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG210C Card."
    ::= { ethernetNTEXG210CCardEntry 2 }

ethernetNTEXG210CCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG210C Card."
    ::= { ethernetNTEXG210CCardEntry 3 }

ethernetNTEXG210CCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG210C Card."
    ::= { ethernetNTEXG210CCardEntry 4 }

ethernetNTEXG210CCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG210C Card."
    ::= { ethernetNTEXG210CCardEntry 5 }

ethernetNTEXG210CCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG210C Card."
    ::= { ethernetNTEXG210CCardEntry 6 }

ethernetNTEXG210CCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG210C Card."
    ::= { ethernetNTEXG210CCardEntry 7 }

ethernetNTEXG210CCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG210 card."
    ::= { ethernetNTEXG210CCardEntry 8 }

ethernetNTEXG210CCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG210CCardEntry 9 }



--
--Card - Ethernet XG210 Optical Port Expander Card for MACSEC
--
ethernetGE8SCryptoConnectorCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetGE8SCryptoConnectorCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet XG210C Optical Port Expander Cards.
          These are supported on the XG210C MACSEC board." 
    ::= { cmEntityObjects 56 }

ethernetGE8SCryptoConnectorCardEntry  OBJECT-TYPE
    SYNTAX      EthernetGE8SCryptoConnectorCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetGE8SCryptoConnectorCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetGE8SCryptoConnectorCardTable 1 }

EthernetGE8SCryptoConnectorCardEntry ::= SEQUENCE {
    ethernetGE8SCryptoConnectorCardEntityIndex             PhysicalIndex,
    ethernetGE8SCryptoConnectorCardAdminState              AdminState,
    ethernetGE8SCryptoConnectorCardOperationalState        OperationalState,
    ethernetGE8SCryptoConnectorCardSecondaryState          SecondaryState,
    ethernetGE8SCryptoConnectorCardVoltage                 Integer32,
    ethernetGE8SCryptoConnectorCardTemperature             Integer32,
    ethernetGE8SCryptoConnectorCardRestartAction           RestartType,
    ethernetGE8SCryptoConnectorCardStorageType             StorageType,
    ethernetGE8SCryptoConnectorCardRowStatus               RowStatus
}

ethernetGE8SCryptoConnectorCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Entity Index from ENTITY-MIB for the Ethernet GE-8SC Connector Card."
    ::= { ethernetGE8SCryptoConnectorCardEntry 1 }

ethernetGE8SCryptoConnectorCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet GE-8SC Connector Card."
    ::= { ethernetGE8SCryptoConnectorCardEntry 2 }

ethernetGE8SCryptoConnectorCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet GE-8SC Connector Card."
    ::= { ethernetGE8SCryptoConnectorCardEntry 3 }

ethernetGE8SCryptoConnectorCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet GE-8SC Connector Card."
    ::= { ethernetGE8SCryptoConnectorCardEntry 4 }

ethernetGE8SCryptoConnectorCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet GE-8SC Connector Card."
    ::= { ethernetGE8SCryptoConnectorCardEntry 5 }

ethernetGE8SCryptoConnectorCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet GE-8SC Connector Card."
    ::= { ethernetGE8SCryptoConnectorCardEntry 6 }

ethernetGE8SCryptoConnectorCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet GE-8SC Connector card."
    ::= { ethernetGE8SCryptoConnectorCardEntry 7 }

ethernetGE8SCryptoConnectorCardStorageType OBJECT-TYPE
    SYNTAX     StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The type of storage configured for this entry."
    ::= { ethernetGE8SCryptoConnectorCardEntry 8 }

ethernetGE8SCryptoConnectorCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ethernetGE8SCryptoConnectorCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ethernetGE8SCryptoConnectorCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ethernetGE8SCryptoConnectorCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ethernetGE8SCryptoConnectorCardEntry 9 }

--
--Card - Ethernet NTE - GE 114Pro Card 
--
ethernetNTEGE114ProCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 57 }

ethernetNTEGE114ProCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProCardTable 1 }

EthernetNTEGE114ProCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProCardAdminState              AdminState,
    ethernetNTEGE114ProCardOperationalState        OperationalState,
    ethernetNTEGE114ProCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProCardVoltage                 Integer32,
    ethernetNTEGE114ProCardTemperature             Integer32,
    ethernetNTEGE114ProCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProCardRestartAction           RestartType,
    ethernetNTEGE114ProCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProCardEntry 1 }

ethernetNTEGE114ProCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProCardEntry 2 }

ethernetNTEGE114ProCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114Pro Card."
    ::= { ethernetNTEGE114ProCardEntry 3 }

ethernetNTEGE114ProCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114Pro Card."
    ::= { ethernetNTEGE114ProCardEntry 4 }

ethernetNTEGE114ProCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114Pro Card."
    ::= { ethernetNTEGE114ProCardEntry 5 }

ethernetNTEGE114ProCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114Pro Card."
    ::= { ethernetNTEGE114ProCardEntry 6 }

ethernetNTEGE114ProCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114Pro Card."
    ::= { ethernetNTEGE114ProCardEntry 7 }

ethernetNTEGE114ProCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114Pro card."
    ::= { ethernetNTEGE114ProCardEntry 8 }

ethernetNTEGE114ProCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProCardEntry 9 }

ethernetNTEGE114ProCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114Pro card."
    ::= { ethernetNTEGE114ProCardEntry 10 }

ethernetNTEGE114ProCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114Pro card."
    ::= { ethernetNTEGE114ProCardEntry 11 }

--
--Card - Ethernet NTE - GE 114ProC Card 
--
ethernetNTEGE114ProCCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 58 }

ethernetNTEGE114ProCCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProCCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProCCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProCCardTable 1 }

EthernetNTEGE114ProCCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProCCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProCCardAdminState              AdminState,
    ethernetNTEGE114ProCCardOperationalState        OperationalState,
    ethernetNTEGE114ProCCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProCCardVoltage                 Integer32,
    ethernetNTEGE114ProCCardTemperature             Integer32,
    ethernetNTEGE114ProCCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProCCardRestartAction           RestartType,
    ethernetNTEGE114ProCCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProCCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProCCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProCCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProCCardEntry 1 }

ethernetNTEGE114ProCCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProCCardEntry 2 }

ethernetNTEGE114ProCCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProC Card."
    ::= { ethernetNTEGE114ProCCardEntry 3 }

ethernetNTEGE114ProCCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProC Card."
    ::= { ethernetNTEGE114ProCCardEntry 4 }

ethernetNTEGE114ProCCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProC Card."
    ::= { ethernetNTEGE114ProCCardEntry 5 }

ethernetNTEGE114ProCCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProC Card."
    ::= { ethernetNTEGE114ProCCardEntry 6 }

ethernetNTEGE114ProCCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProC Card."
    ::= { ethernetNTEGE114ProCCardEntry 7 }

ethernetNTEGE114ProCCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProC card."
    ::= { ethernetNTEGE114ProCCardEntry 8 }

ethernetNTEGE114ProCCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProCCardEntry 9 }

ethernetNTEGE114ProCCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProC card."
    ::= { ethernetNTEGE114ProCCardEntry 10 }

ethernetNTEGE114ProCCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProC card."
    ::= { ethernetNTEGE114ProCCardEntry 11 }


--
--Card - Ethernet NTE - GE 114ProS Card 
--
ethernetNTEGE114ProSHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 59 }

ethernetNTEGE114ProSHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProSHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProSHCardTable 1 }

EthernetNTEGE114ProSHCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProSHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProSHCardAdminState              AdminState,
    ethernetNTEGE114ProSHCardOperationalState        OperationalState,
    ethernetNTEGE114ProSHCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProSHCardVoltage                 Integer32,
    ethernetNTEGE114ProSHCardTemperature             Integer32,
    ethernetNTEGE114ProSHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProSHCardRestartAction           RestartType,
    ethernetNTEGE114ProSHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProSHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProSHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProSHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProSHCardEntry 1 }

ethernetNTEGE114ProSHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProSHCardEntry 2 }

ethernetNTEGE114ProSHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProS Card."
    ::= { ethernetNTEGE114ProSHCardEntry 3 }

ethernetNTEGE114ProSHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProS Card."
    ::= { ethernetNTEGE114ProSHCardEntry 4 }

ethernetNTEGE114ProSHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProS Card."
    ::= { ethernetNTEGE114ProSHCardEntry 5 }

ethernetNTEGE114ProSHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProS Card."
    ::= { ethernetNTEGE114ProSHCardEntry 6 }

ethernetNTEGE114ProSHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProS Card."
    ::= { ethernetNTEGE114ProSHCardEntry 7 }

ethernetNTEGE114ProSHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProS card."
    ::= { ethernetNTEGE114ProSHCardEntry 8 }

ethernetNTEGE114ProSHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProSHCardEntry 9 }

ethernetNTEGE114ProSHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProSH card."
    ::= { ethernetNTEGE114ProSHCardEntry 10 }

ethernetNTEGE114ProSHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProSH card."
    ::= { ethernetNTEGE114ProSHCardEntry 11 }

--
--Card - Ethernet NTE - GE 114ProCSH Card 
--
ethernetNTEGE114ProCSHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProCSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 60 }

ethernetNTEGE114ProCSHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProCSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProCSHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProCSHCardTable 1 }

EthernetNTEGE114ProCSHCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProCSHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProCSHCardAdminState              AdminState,
    ethernetNTEGE114ProCSHCardOperationalState        OperationalState,
    ethernetNTEGE114ProCSHCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProCSHCardVoltage                 Integer32,
    ethernetNTEGE114ProCSHCardTemperature             Integer32,
    ethernetNTEGE114ProCSHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProCSHCardRestartAction           RestartType,
    ethernetNTEGE114ProCSHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProCSHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProCSHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProCSHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProCSHCardEntry 1 }

ethernetNTEGE114ProCSHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProCSHCardEntry 2 }

ethernetNTEGE114ProCSHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProCS Card."
    ::= { ethernetNTEGE114ProCSHCardEntry 3 }

ethernetNTEGE114ProCSHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProCS Card."
    ::= { ethernetNTEGE114ProCSHCardEntry 4 }

ethernetNTEGE114ProCSHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProCS Card."
    ::= { ethernetNTEGE114ProCSHCardEntry 5 }

ethernetNTEGE114ProCSHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProCS Card."
    ::= { ethernetNTEGE114ProCSHCardEntry 6 }

ethernetNTEGE114ProCSHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProCS Card."
    ::= { ethernetNTEGE114ProCSHCardEntry 7 }

ethernetNTEGE114ProCSHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProCS card."
    ::= { ethernetNTEGE114ProCSHCardEntry 8 }

ethernetNTEGE114ProCSHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProCSHCardEntry 9 }

ethernetNTEGE114ProCSHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProCS card."
    ::= { ethernetNTEGE114ProCSHCardEntry 10 }

ethernetNTEGE114ProCSHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProCS card."
    ::= { ethernetNTEGE114ProCSHCardEntry 11 }    

--
--Card - Ethernet NTE - GE 114ProHE Card 
--
ethernetNTEGE114ProHECardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProHECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 61 }

ethernetNTEGE114ProHECardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProHECardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProHECardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProHECardTable 1 }

EthernetNTEGE114ProHECardEntry ::= SEQUENCE {
    ethernetNTEGE114ProHECardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProHECardAdminState              AdminState,
    ethernetNTEGE114ProHECardOperationalState        OperationalState,
    ethernetNTEGE114ProHECardSecondaryState          SecondaryState,
    ethernetNTEGE114ProHECardVoltage                 Integer32,
    ethernetNTEGE114ProHECardTemperature             Integer32,
    ethernetNTEGE114ProHECardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProHECardRestartAction           RestartType,
    ethernetNTEGE114ProHECardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProHECardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProHECardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProHECardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProHECardEntry 1 }

ethernetNTEGE114ProHECardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE114ProHECardEntry 2 }

ethernetNTEGE114ProHECardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProHE Card."
    ::= { ethernetNTEGE114ProHECardEntry 3 }

ethernetNTEGE114ProHECardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProHE Card."
    ::= { ethernetNTEGE114ProHECardEntry 4 }

ethernetNTEGE114ProHECardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProHE Card."
    ::= { ethernetNTEGE114ProHECardEntry 5 }

ethernetNTEGE114ProHECardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProHE Card."
    ::= { ethernetNTEGE114ProHECardEntry 6 }

ethernetNTEGE114ProHECardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProHE Card."
    ::= { ethernetNTEGE114ProHECardEntry 7 }

ethernetNTEGE114ProHECardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProHE card."
    ::= { ethernetNTEGE114ProHECardEntry 8 }

ethernetNTEGE114ProHECardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProHECardEntry 9 }

ethernetNTEGE114ProHECardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProHE card."
    ::= { ethernetNTEGE114ProHECardEntry 10 }

ethernetNTEGE114ProHECardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProHE card."
    ::= { ethernetNTEGE114ProHECardEntry 11 }    

--
--Card - Ethernet NTE - GE 112ProH Card 
--
ethernetNTEGE112ProHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE112ProHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro Cards.
          These are supported on the FSP150CC GE112 Pro product." 
    ::= { cmEntityObjects 62 }

ethernetNTEGE112ProHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE112ProHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE112ProHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE112ProHCardTable 1 }

EthernetNTEGE112ProHCardEntry ::= SEQUENCE {
    ethernetNTEGE112ProHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE112ProHCardAdminState              AdminState,
    ethernetNTEGE112ProHCardOperationalState        OperationalState,
    ethernetNTEGE112ProHCardSecondaryState          SecondaryState,
    ethernetNTEGE112ProHCardVoltage                 Integer32,
    ethernetNTEGE112ProHCardTemperature             Integer32,
    ethernetNTEGE112ProHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE112ProHCardRestartAction           RestartType,
    ethernetNTEGE112ProHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE112ProHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE112ProHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE112ProHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE112ProHCardEntry 1 }

ethernetNTEGE112ProHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE112 Pro Card."
    ::= { ethernetNTEGE112ProHCardEntry 2 }

ethernetNTEGE112ProHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE112ProH Card."
    ::= { ethernetNTEGE112ProHCardEntry 3 }

ethernetNTEGE112ProHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE112ProH Card."
    ::= { ethernetNTEGE112ProHCardEntry 4 }

ethernetNTEGE112ProHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112ProH Card."
    ::= { ethernetNTEGE112ProHCardEntry 5 }

ethernetNTEGE112ProHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE112ProH Card."
    ::= { ethernetNTEGE112ProHCardEntry 6 }

ethernetNTEGE112ProHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE112ProH Card."
    ::= { ethernetNTEGE112ProHCardEntry 7 }

ethernetNTEGE112ProHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112ProH card."
    ::= { ethernetNTEGE112ProHCardEntry 8 }

ethernetNTEGE112ProHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE112ProHCardEntry 9 }

ethernetNTEGE112ProHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE112ProH card."
    ::= { ethernetNTEGE112ProHCardEntry 10 }

ethernetNTEGE112ProHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112ProH card."
    ::= { ethernetNTEGE112ProHCardEntry 11 }

--
--Card - Ethernet NTE - OSA5420 
--
ethernetNTEOSA5420CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEOSA5420CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE OSA5420 Card.
          These are supported on the FSP150CC OSA5420 product." 
    ::= { cmEntityObjects 63 }

ethernetNTEOSA5420CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEOSA5420CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEOSA5420CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEOSA5420CardTable 1 }

EthernetNTEOSA5420CardEntry ::= SEQUENCE {
    ethernetNTEOSA5420CardEntityIndex             PhysicalIndex,
    ethernetNTEOSA5420CardAdminState              AdminState,
    ethernetNTEOSA5420CardOperationalState        OperationalState,
    ethernetNTEOSA5420CardSecondaryState          SecondaryState,
    ethernetNTEOSA5420CardVoltage                 Integer32,
    ethernetNTEOSA5420CardTemperature             Integer32,
    ethernetNTEOSA5420CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEOSA5420CardRestartAction           RestartType,
    ethernetNTEOSA5420CardFineGrainedPmInterval   CmPmIntervalType 
}

ethernetNTEOSA5420CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE OSA5420 Card."
    ::= { ethernetNTEOSA5420CardEntry 1 }

ethernetNTEOSA5420CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE OSA5420 Card."
    ::= { ethernetNTEOSA5420CardEntry 2 }

ethernetNTEOSA5420CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE OSA5420 Card."
    ::= { ethernetNTEOSA5420CardEntry 3 }

ethernetNTEOSA5420CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE OSA5420 Card."
    ::= { ethernetNTEOSA5420CardEntry 4 }

ethernetNTEOSA5420CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE OSA5420 Card."
    ::= { ethernetNTEOSA5420CardEntry 5 }

ethernetNTEOSA5420CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE OSA5420 Card."
    ::= { ethernetNTEOSA5420CardEntry 6 }

ethernetNTEOSA5420CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE OSA5420 Card."
    ::= { ethernetNTEOSA5420CardEntry 7 }

ethernetNTEOSA5420CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE OSA5420 card."
    ::= { ethernetNTEOSA5420CardEntry 8 }

ethernetNTEOSA5420CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
      "Allows specification of the fine-grained PM interval at the card level.
       This applies to all monitored PM entities. The default value of this
       attribute is interval-15min.  Valid values are interval-5min and interval-15min.
       interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEOSA5420CardEntry 9 }

--
--Card - Ethernet NTE - OSA5421
--
ethernetNTEOSA5421CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEOSA5421CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE OSA5421 Card.
          These are supported on the FSP150CC OSA5421 product." 
    ::= { cmEntityObjects 64 }

ethernetNTEOSA5421CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEOSA5421CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEOSA5421CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEOSA5421CardTable 1 }

EthernetNTEOSA5421CardEntry ::= SEQUENCE {
    ethernetNTEOSA5421CardEntityIndex             PhysicalIndex,
    ethernetNTEOSA5421CardAdminState              AdminState,
    ethernetNTEOSA5421CardOperationalState        OperationalState,
    ethernetNTEOSA5421CardSecondaryState          SecondaryState,
    ethernetNTEOSA5421CardVoltage                 Integer32,
    ethernetNTEOSA5421CardTemperature             Integer32,
    ethernetNTEOSA5421CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEOSA5421CardRestartAction           RestartType,
    ethernetNTEOSA5421CardFineGrainedPmInterval   CmPmIntervalType 
}

ethernetNTEOSA5421CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE OSA5421 Card."
    ::= { ethernetNTEOSA5421CardEntry 1 }

ethernetNTEOSA5421CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE OSA5421 Card."
    ::= { ethernetNTEOSA5421CardEntry 2 }

ethernetNTEOSA5421CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE OSA5421 Card."
    ::= { ethernetNTEOSA5421CardEntry 3 }

ethernetNTEOSA5421CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE OSA5421 Card."
    ::= { ethernetNTEOSA5421CardEntry 4 }

ethernetNTEOSA5421CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE OSA5421 Card."
    ::= { ethernetNTEOSA5421CardEntry 5 }

ethernetNTEOSA5421CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE OSA5421 Card."
    ::= { ethernetNTEOSA5421CardEntry 6 }

ethernetNTEOSA5421CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE OSA5421 Card."
    ::= { ethernetNTEOSA5421CardEntry 7 }

ethernetNTEOSA5421CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE OSA5421 card."
    ::= { ethernetNTEOSA5421CardEntry 8 }

ethernetNTEOSA5421CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
      "Allows specification of the fine-grained PM interval at the card level.
       This applies to all monitored PM entities. The default value of this
       attribute is interval-15min.  Valid values are interval-5min and interval-15min.
       interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEOSA5421CardEntry 9 }

--
--Card - Ethernet NTE - GE 114G Card 
--
ethernetNTEGE114GCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114GCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114G Cards.
          These are supported on the FSP150CC GE114G product." 
    ::= { cmEntityObjects 65 }

ethernetNTEGE114GCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114GCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114GCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114GCardTable 1 }

EthernetNTEGE114GCardEntry ::= SEQUENCE {
    ethernetNTEGE114GCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114GCardAdminState              AdminState,
    ethernetNTEGE114GCardOperationalState        OperationalState,
    ethernetNTEGE114GCardSecondaryState          SecondaryState,
    ethernetNTEGE114GCardVoltage                 Integer32,
    ethernetNTEGE114GCardTemperature             Integer32,
    ethernetNTEGE114GCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114GCardRestartAction           RestartType,
    ethernetNTEGE114GCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114GCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114GCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114GCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114G Card."
    ::= { ethernetNTEGE114GCardEntry 1 }

ethernetNTEGE114GCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114G Card."
    ::= { ethernetNTEGE114GCardEntry 2 }

ethernetNTEGE114GCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114G Card."
    ::= { ethernetNTEGE114GCardEntry 3 }

ethernetNTEGE114GCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114G Card."
    ::= { ethernetNTEGE114GCardEntry 4 }

ethernetNTEGE114GCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114G Card."
    ::= { ethernetNTEGE114GCardEntry 5 }

ethernetNTEGE114GCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114G Card."
    ::= { ethernetNTEGE114GCardEntry 6 }

ethernetNTEGE114GCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114G Card."
    ::= { ethernetNTEGE114GCardEntry 7 }

ethernetNTEGE114GCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114G card."
    ::= { ethernetNTEGE114GCardEntry 8 }

ethernetNTEGE114GCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114GCardEntry 9 }

ethernetNTEGE114GCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114G card."
    ::= { ethernetNTEGE114GCardEntry 10 }

ethernetNTEGE114GCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114G card."
    ::= { ethernetNTEGE114GCardEntry 11 }




--
--  BITS 16 Port Expansion Card (Building Integrated Timing System) 
--
bits16PortCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF Bits16PortCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to BITS 16 Port Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 66 }

bits16PortCardEntry OBJECT-TYPE
    SYNTAX      Bits16PortCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the bits16PortCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { bits16PortCardTable 1 }

Bits16PortCardEntry ::= SEQUENCE {
    -- Port Indices 
    bits16PortCardEntityIndex                PhysicalIndex,

    -- State Management params
    bits16PortCardAdminState                 AdminState,
    bits16PortCardOperationalState           OperationalState,
    bits16PortCardSecondaryState             SecondaryState,
    bits16PortCardRowStatus                  RowStatus,
    bits16PortCardAlias                      DisplayString,
    bits16PortCardTemperature                Integer32
}
    
-- bits16PortCard Indices 

bits16PortCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { bits16PortCardEntry 1 }

-- State Management params
bits16PortCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the BITS 16 Port Expansion Card."
    ::= { bits16PortCardEntry 2 }

bits16PortCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the BITS 16 Port Expansion Card."
    ::= { bits16PortCardEntry 3 }

bits16PortCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the BITS 16 Port Expansion Card."
    ::= { bits16PortCardEntry 4 }

bits16PortCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of bits16PortCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            bits16PortCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The bits16PortCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { bits16PortCardEntry 5 }

bits16PortCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the BITS 16 Port Expansion Card."
    ::= { bits16PortCardEntry 6 }

bits16PortCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the BITS 16 Port Expansion Card"
    ::= { bits16PortCardEntry 7 }


--
--Card - Ethernet NTE - GE 114ProVM-H Card 
--
ethernetNTEGE114ProVmHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProVmHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114 Pro VM-H Cards.
          These are supported on the FSP150CC GE114 Pro VM-H product." 
    ::= { cmEntityObjects 67 }

ethernetNTEGE114ProVmHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProVmHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProVmHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProVmHCardTable 1 }

EthernetNTEGE114ProVmHCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProVmHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProVmHCardAdminState              AdminState,
    ethernetNTEGE114ProVmHCardOperationalState        OperationalState,
    ethernetNTEGE114ProVmHCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProVmHCardVoltage                 Integer32,
    ethernetNTEGE114ProVmHCardTemperature             Integer32,
    ethernetNTEGE114ProVmHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProVmHCardRestartAction           RestartType,
    ethernetNTEGE114ProVmHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProVmHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProVmHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProVmHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114 Pro VM-H Card."
    ::= { ethernetNTEGE114ProVmHCardEntry 1 }

ethernetNTEGE114ProVmHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114 Pro VM-H Card."
    ::= { ethernetNTEGE114ProVmHCardEntry 2 }

ethernetNTEGE114ProVmHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProVmH Card."
    ::= { ethernetNTEGE114ProVmHCardEntry 3 }

ethernetNTEGE114ProVmHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProVmH Card."
    ::= { ethernetNTEGE114ProVmHCardEntry 4 }

ethernetNTEGE114ProVmHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProVmH Card."
    ::= { ethernetNTEGE114ProVmHCardEntry 5 }

ethernetNTEGE114ProVmHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProVmH Card."
    ::= { ethernetNTEGE114ProVmHCardEntry 6 }

ethernetNTEGE114ProVmHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProVmH Card."
    ::= { ethernetNTEGE114ProVmHCardEntry 7 }

ethernetNTEGE114ProVmHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmH card."
    ::= { ethernetNTEGE114ProVmHCardEntry 8 }

ethernetNTEGE114ProVmHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProVmHCardEntry 9 }

ethernetNTEGE114ProVmHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProVmH card."
    ::= { ethernetNTEGE114ProVmHCardEntry 10 }

ethernetNTEGE114ProVmHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmH card."
    ::= { ethernetNTEGE114ProVmHCardEntry 11 }


--
--Card - Ethernet NTE - GE 114ProVM-CH Card 
--
ethernetNTEGE114ProVmCHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProVmCHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114 Pro VM-CH Cards.
          These are supported on the FSP150CC GE114 Pro VM-CH product." 
    ::= { cmEntityObjects 68 }

ethernetNTEGE114ProVmCHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProVmCHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProVmCHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProVmCHCardTable 1 }

EthernetNTEGE114ProVmCHCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProVmCHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProVmCHCardAdminState              AdminState,
    ethernetNTEGE114ProVmCHCardOperationalState        OperationalState,
    ethernetNTEGE114ProVmCHCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProVmCHCardVoltage                 Integer32,
    ethernetNTEGE114ProVmCHCardTemperature             Integer32,
    ethernetNTEGE114ProVmCHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProVmCHCardRestartAction           RestartType,
    ethernetNTEGE114ProVmCHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProVmCHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProVmCHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProVmCHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114 Pro VM-CH Card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 1 }

ethernetNTEGE114ProVmCHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114 Pro VM-CH Card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 2 }

ethernetNTEGE114ProVmCHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProVmCH Card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 3 }

ethernetNTEGE114ProVmCHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProVmCH Card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 4 }

ethernetNTEGE114ProVmCHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProVmCH Card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 5 }

ethernetNTEGE114ProVmCHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProVmCH Card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 6 }

ethernetNTEGE114ProVmCHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProVmCH Card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 7 }

ethernetNTEGE114ProVmCHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmCH card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 8 }

ethernetNTEGE114ProVmCHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProVmCHCardEntry 9 }

ethernetNTEGE114ProVmCHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProVmCH card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 10 }

ethernetNTEGE114ProVmCHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmCH card."
    ::= { ethernetNTEGE114ProVmCHCardEntry 11 }

--
--Card - Ethernet NTE - GE 114ProVM-CSH Card 
--
ethernetNTEGE114ProVmCSHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProVmCSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114 Pro VM-CSH Cards.
          These are supported on the FSP150CC GE114 Pro VM-CSH product." 
    ::= { cmEntityObjects 69 }

ethernetNTEGE114ProVmCSHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProVmCSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProVmCSHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProVmCSHCardTable 1 }

EthernetNTEGE114ProVmCSHCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProVmCSHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProVmCSHCardAdminState              AdminState,
    ethernetNTEGE114ProVmCSHCardOperationalState        OperationalState,
    ethernetNTEGE114ProVmCSHCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProVmCSHCardVoltage                 Integer32,
    ethernetNTEGE114ProVmCSHCardTemperature             Integer32,
    ethernetNTEGE114ProVmCSHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProVmCSHCardRestartAction           RestartType,
    ethernetNTEGE114ProVmCSHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProVmCSHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProVmCSHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProVmCSHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114 Pro VM-CSH Card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 1 }

ethernetNTEGE114ProVmCSHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114 Pro VM-CSH Card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 2 }

ethernetNTEGE114ProVmCSHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProVmCSH Card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 3 }

ethernetNTEGE114ProVmCSHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProVmCSH Card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 4 }

ethernetNTEGE114ProVmCSHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProVmCSH Card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 5 }

ethernetNTEGE114ProVmCSHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProVmCSH Card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 6 }

ethernetNTEGE114ProVmCSHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProVmCSH Card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 7 }

ethernetNTEGE114ProVmCSHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmCSH card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 8 }

ethernetNTEGE114ProVmCSHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 9 }

ethernetNTEGE114ProVmCSHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProVmCSH card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 10 }

ethernetNTEGE114ProVmCSHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmCSH card."
    ::= { ethernetNTEGE114ProVmCSHCardEntry 11 }

--
--Card - ServerCard 
--
serverCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF ServerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on ServerCard within the Shelf." 
    ::= { cmEntityObjects 70 }

serverCardEntry  OBJECT-TYPE
    SYNTAX      ServerCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the serverCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { serverCardTable 1 }

ServerCardEntry ::= SEQUENCE {
    serverCardEntityIndex           PhysicalIndex,
    serverCardAdminState            AdminState,
    serverCardOperationalState      OperationalState,
    serverCardSecondaryState        SecondaryState,
    serverCardStorageType           StorageType,
    serverCardVoltage               Integer32,
    serverCardTemperature           Integer32,
    serverCardUpTime                Integer32,
    serverCardVmNumber              Integer32,
    serverCardVirtualCpuTotal       Integer32,
    serverCardVirtualCpuAvailiable  Integer32,
    serverCardMemoryTotal           Integer32,
    serverCardMemoryAvailiable      Integer32,
    serverCardStorageTotal          Integer32,
    serverCardStorageAvailiable     Integer32,
    serverCardHvVersion             DisplayString,
    serverCardHostName              DisplayString,
    serverCardRestartAction         RestartType,
    serverCardRowStatus             RowStatus,
    serverCardIgnoreWatchdog        TruthValue

}

serverCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this Server Card."
    ::= { serverCardEntry 1 }

serverCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Server Card."
    ::= { serverCardEntry 2 }

serverCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Server Card."
    ::= { serverCardEntry 3 }

serverCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Server Card."
    ::= { serverCardEntry 4 }

serverCardStorageType OBJECT-TYPE
    SYNTAX  StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
             "Storage Type of the Server Card."
    ::= { serverCardEntry 5 }

serverCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Server Card."
    ::= { serverCardEntry 6 }

serverCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Server Card."
    ::= { serverCardEntry 7 }

serverCardUpTime OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Up time of the Server Card."
    ::= { serverCardEntry 8 }

serverCardVmNumber OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "VM number of the Server Card."
    ::= { serverCardEntry 9 }

serverCardVirtualCpuTotal OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Total virtual CPU of the Server Card."
    ::= { serverCardEntry 10 }

serverCardVirtualCpuAvailiable OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Availiable virtual CPU of the Server Card."
    ::= { serverCardEntry 11 }

serverCardMemoryTotal OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Total memory of the Server Card."
    ::= { serverCardEntry 12 }

serverCardMemoryAvailiable OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Availiable memory of the Server Card."
    ::= { serverCardEntry 13 }

serverCardStorageTotal OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Total storage of the Server Card."
    ::= { serverCardEntry 14 }

serverCardStorageAvailiable OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Availiable storage of the Server Card."
    ::= { serverCardEntry 15 }

serverCardHvVersion OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "HV version of the Server Card."
    ::= { serverCardEntry 16 }

serverCardHostName OBJECT-TYPE
    SYNTAX  DisplayString (SIZE (0..64))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Hostname of the Server Card."
    ::= { serverCardEntry 17 }

serverCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the VM server card."
    ::= { serverCardEntry 18 }

serverCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of serverCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            serverCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The serverCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { serverCardEntry 19 }

serverCardIgnoreWatchdog OBJECT-TYPE
    SYNTAX  TruthValue 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Ignore keep-alive watchdog."
    ::= { serverCardEntry 20 }
--
--  PPS 16 Port Expansion Card (Building Integrated Timing System) 
--
pps16PortCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF Pps16PortCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to BITS 16 Port Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 71 }

pps16PortCardEntry OBJECT-TYPE
    SYNTAX      Pps16PortCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the pps16PortCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { pps16PortCardTable 1 }

Pps16PortCardEntry ::= SEQUENCE {
    -- Port Indices 
    pps16PortCardEntityIndex                PhysicalIndex,

    -- State Management params
    pps16PortCardAdminState                 AdminState,
    pps16PortCardOperationalState           OperationalState,
    pps16PortCardSecondaryState             SecondaryState,
    pps16PortCardRowStatus                  RowStatus,
    pps16PortCardAlias                      DisplayString

}
    
-- pps16PortCard Indices 



pps16PortCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { pps16PortCardEntry 1 }

-- State Management params
pps16PortCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the BITS 16 Port Expansion Card."
    ::= { pps16PortCardEntry 2 }

pps16PortCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the BITS 16 Port Expansion Card."
    ::= { pps16PortCardEntry 3 }

pps16PortCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the BITS 16 Port Expansion Card."
    ::= { pps16PortCardEntry 4 }

pps16PortCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of pps16PortCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            pps16PortCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The pps16PortCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { pps16PortCardEntry 5 }

pps16PortCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the BITS 16 Port Expansion Card."
    ::= { pps16PortCardEntry 6 }


--
--  CLK 16 Port Expansion Card (Building Integrated Timing System) 
--
clk16PortCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF Clk16PortCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to BITS 16 Port Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 72 }

clk16PortCardEntry OBJECT-TYPE
    SYNTAX      Clk16PortCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the clk16PortCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { clk16PortCardTable 1 }

Clk16PortCardEntry ::= SEQUENCE {
    -- Port Indices 
    clk16PortCardEntityIndex                PhysicalIndex,

    -- State Management params
    clk16PortCardAdminState                 AdminState,
    clk16PortCardOperationalState           OperationalState,
    clk16PortCardSecondaryState             SecondaryState,
    clk16PortCardRowStatus                  RowStatus,
    clk16PortCardAlias                      DisplayString

}
    
-- clk16PortCard Indices 


clk16PortCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { clk16PortCardEntry 1 }

-- State Management params
clk16PortCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the BITS 16 Port Expansion Card."
    ::= { clk16PortCardEntry 2 }

clk16PortCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the BITS 16 Port Expansion Card."
    ::= { clk16PortCardEntry 3 }

clk16PortCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the BITS 16 Port Expansion Card."
    ::= { clk16PortCardEntry 4 }

clk16PortCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of clk16PortCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            clk16PortCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The clk16PortCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { clk16PortCardEntry 5 }

clk16PortCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the BITS 16 Port Expansion Card."
    ::= { clk16PortCardEntry 6 }






--
--  TodPps 16 Port Expansion Card (Building Integrated Timing System) 
--
todPps16PortCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TodPps16PortCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to BITS 16 Port Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 73 }

todPps16PortCardEntry OBJECT-TYPE
    SYNTAX      TodPps16PortCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the todPps16PortCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { todPps16PortCardTable 1 }

TodPps16PortCardEntry ::= SEQUENCE {
    -- Port Indices 
    todPps16PortCardEntityIndex                PhysicalIndex,

    -- State Management params
    todPps16PortCardAdminState                 AdminState,
    todPps16PortCardOperationalState           OperationalState,
    todPps16PortCardSecondaryState             SecondaryState,
    todPps16PortCardRowStatus                  RowStatus,
    todPps16PortCardAlias                      DisplayString

}
    
-- todPps16PortCard Indices 



todPps16PortCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { todPps16PortCardEntry 1 }

-- State Management params
todPps16PortCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the BITS 16 Port Expansion Card."
    ::= { todPps16PortCardEntry 2 }

todPps16PortCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the BITS 16 Port Expansion Card."
    ::= { todPps16PortCardEntry 3 }

todPps16PortCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the BITS 16 Port Expansion Card."
    ::= { todPps16PortCardEntry 4 }

todPps16PortCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of todPps16PortCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            todPps16PortCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The todPps16PortCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { todPps16PortCardEntry 5 }

todPps16PortCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the BITS 16 Port Expansion Card."
    ::= { todPps16PortCardEntry 6 }



--
--Card - Ethernet NTE - GE 101Pro Card 
--
ethernetNTEGE101ProCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE101ProCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE101 Pro Cards.
          These are supported on the FSP150CC GE101 Pro product." 
    ::= { cmEntityObjects 74 }

ethernetNTEGE101ProCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE101ProCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE101ProCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE101ProCardTable 1 }

EthernetNTEGE101ProCardEntry ::= SEQUENCE {
    ethernetNTEGE101ProCardEntityIndex             PhysicalIndex,
    ethernetNTEGE101ProCardAdminState              AdminState,
    ethernetNTEGE101ProCardOperationalState        OperationalState,
    ethernetNTEGE101ProCardSecondaryState          SecondaryState,
    ethernetNTEGE101ProCardVoltage                 Integer32,
    ethernetNTEGE101ProCardTemperature             Integer32,
    ethernetNTEGE101ProCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE101ProCardRestartAction           RestartType,
    ethernetNTEGE101ProCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE101ProCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE101ProCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE101ProCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE101 Pro Card."
    ::= { ethernetNTEGE101ProCardEntry 1 }

ethernetNTEGE101ProCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE101 Pro Card."
    ::= { ethernetNTEGE101ProCardEntry 2 }

ethernetNTEGE101ProCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE101Pro Card."
    ::= { ethernetNTEGE101ProCardEntry 3 }

ethernetNTEGE101ProCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE101Pro Card."
    ::= { ethernetNTEGE101ProCardEntry 4 }

ethernetNTEGE101ProCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE101Pro Card."
    ::= { ethernetNTEGE101ProCardEntry 5 }

ethernetNTEGE101ProCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE101Pro Card."
    ::= { ethernetNTEGE101ProCardEntry 6 }

ethernetNTEGE101ProCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE101Pro Card."
    ::= { ethernetNTEGE101ProCardEntry 7 }

ethernetNTEGE101ProCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE101Pro card."
    ::= { ethernetNTEGE101ProCardEntry 8 }

ethernetNTEGE101ProCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE101ProCardEntry 9 }

ethernetNTEGE101ProCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE101Pro card."
    ::= { ethernetNTEGE101ProCardEntry 10 }

ethernetNTEGE101ProCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE101Pro card."
    ::= { ethernetNTEGE101ProCardEntry 11 }


--
--Card - Ethernet NTE - Pro Rugged S Card 
--
ethernetNTEGO102ProSCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGO102ProSCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE Pro GO102Pro S Cards.
          These are supported on the FSP150CC GO102ProS product." 
    ::= { cmEntityObjects 75 }

ethernetNTEGO102ProSCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGO102ProSCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGO102ProSCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGO102ProSCardTable 1 }

EthernetNTEGO102ProSCardEntry ::= SEQUENCE {
    ethernetNTEGO102ProSCardEntityIndex             PhysicalIndex,
    ethernetNTEGO102ProSCardAdminState              AdminState,
    ethernetNTEGO102ProSCardOperationalState        OperationalState,
    ethernetNTEGO102ProSCardSecondaryState          SecondaryState,
    ethernetNTEGO102ProSCardVoltage                 Integer32,
    ethernetNTEGO102ProSCardTemperature             Integer32,
    ethernetNTEGO102ProSCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGO102ProSCardRestartAction           RestartType,
    ethernetNTEGO102ProSCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGO102ProSCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGO102ProSCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGO102ProSCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GO102ProS Card."
    ::= { ethernetNTEGO102ProSCardEntry 1 }

ethernetNTEGO102ProSCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GO102ProS Card."
    ::= { ethernetNTEGO102ProSCardEntry 2 }

ethernetNTEGO102ProSCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GO102ProS Card."
    ::= { ethernetNTEGO102ProSCardEntry 3 }

ethernetNTEGO102ProSCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GO102ProS Card."
    ::= { ethernetNTEGO102ProSCardEntry 4 }

ethernetNTEGO102ProSCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GO102ProS Card."
    ::= { ethernetNTEGO102ProSCardEntry 5 }

ethernetNTEGO102ProSCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GO102ProS Card."
    ::= { ethernetNTEGO102ProSCardEntry 6 }

ethernetNTEGO102ProSCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GO102ProS Card."
    ::= { ethernetNTEGO102ProSCardEntry 7 }

ethernetNTEGO102ProSCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GO102ProS card."
    ::= { ethernetNTEGO102ProSCardEntry 8 }

ethernetNTEGO102ProSCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGO102ProSCardEntry 9 }

ethernetNTEGO102ProSCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GO102ProS card."
    ::= { ethernetNTEGO102ProSCardEntry 10 }

ethernetNTEGO102ProSCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GO102ProS card."
    ::= { ethernetNTEGO102ProSCardEntry 11 }

--
--Card - Ethernet NTE - Pro Rugged S Card 
--
ethernetNTEGO102ProSPCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGO102ProSPCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE Pro Rugged S Cards.
          These are supported on the FSP150CC GO102ProSP product." 
    ::= { cmEntityObjects 76 }

ethernetNTEGO102ProSPCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGO102ProSPCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGO102ProSPCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGO102ProSPCardTable 1 }

EthernetNTEGO102ProSPCardEntry ::= SEQUENCE {
    ethernetNTEGO102ProSPCardEntityIndex             PhysicalIndex,
    ethernetNTEGO102ProSPCardAdminState              AdminState,
    ethernetNTEGO102ProSPCardOperationalState        OperationalState,
    ethernetNTEGO102ProSPCardSecondaryState          SecondaryState,
    ethernetNTEGO102ProSPCardVoltage                 Integer32,
    ethernetNTEGO102ProSPCardTemperature             Integer32,
    ethernetNTEGO102ProSPCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGO102ProSPCardRestartAction           RestartType,
    ethernetNTEGO102ProSPCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGO102ProSPCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGO102ProSPCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGO102ProSPCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GO102ProSP Card."
    ::= { ethernetNTEGO102ProSPCardEntry 1 }

ethernetNTEGO102ProSPCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GO102ProSP Card."
    ::= { ethernetNTEGO102ProSPCardEntry 2 }

ethernetNTEGO102ProSPCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GO102ProSP Card."
    ::= { ethernetNTEGO102ProSPCardEntry 3 }

ethernetNTEGO102ProSPCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GO102ProSP Card."
    ::= { ethernetNTEGO102ProSPCardEntry 4 }

ethernetNTEGO102ProSPCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GO102ProSP Card."
    ::= { ethernetNTEGO102ProSPCardEntry 5 }

ethernetNTEGO102ProSPCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GO102ProSP Card."
    ::= { ethernetNTEGO102ProSPCardEntry 6 }

ethernetNTEGO102ProSPCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GO102ProSP Card."
    ::= { ethernetNTEGO102ProSPCardEntry 7 }

ethernetNTEGO102ProSPCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GO102ProSP card."
    ::= { ethernetNTEGO102ProSPCardEntry 8 }

ethernetNTEGO102ProSPCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGO102ProSPCardEntry 9 }

ethernetNTEGO102ProSPCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GO102ProSP card."
    ::= { ethernetNTEGO102ProSPCardEntry 10 }

ethernetNTEGO102ProSPCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GO102ProSP card."
    ::= { ethernetNTEGO102ProSPCardEntry 11 }


--
--Card - Ethernet NTE - Pro Rugged S Card 
--
ethernetNTECX101Pro30ACardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTECX101Pro30ACardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE Pro Rugged S Cards.
          These are supported on the FSP150CC GE101 Pro product." 
    ::= { cmEntityObjects 77 }

ethernetNTECX101Pro30ACardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTECX101Pro30ACardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTECX101Pro30ACardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTECX101Pro30ACardTable 1 }

EthernetNTECX101Pro30ACardEntry ::= SEQUENCE {
    ethernetNTECX101Pro30ACardEntityIndex             PhysicalIndex,
    ethernetNTECX101Pro30ACardAdminState              AdminState,
    ethernetNTECX101Pro30ACardOperationalState        OperationalState,
    ethernetNTECX101Pro30ACardSecondaryState          SecondaryState,
    ethernetNTECX101Pro30ACardVoltage                 Integer32,
    ethernetNTECX101Pro30ACardTemperature             Integer32,
    ethernetNTECX101Pro30ACardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTECX101Pro30ACardRestartAction           RestartType,
    ethernetNTECX101Pro30ACardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTECX101Pro30ACardSwitchPortActionPort    VariablePointer,
    ethernetNTECX101Pro30ACardSwitchPortAction        SwitchPortAction
}

ethernetNTECX101Pro30ACardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE CX101PRO 30A Card."
    ::= { ethernetNTECX101Pro30ACardEntry 1 }

ethernetNTECX101Pro30ACardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE CX101PRO 30A Card."
    ::= { ethernetNTECX101Pro30ACardEntry 2 }

ethernetNTECX101Pro30ACardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE CX101Pro30A Card."
    ::= { ethernetNTECX101Pro30ACardEntry 3 }

ethernetNTECX101Pro30ACardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE CX101Pro30A Card."
    ::= { ethernetNTECX101Pro30ACardEntry 4 }

ethernetNTECX101Pro30ACardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE CX101Pro30A Card."
    ::= { ethernetNTECX101Pro30ACardEntry 5 }

ethernetNTECX101Pro30ACardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE CX101Pro30A Card."
    ::= { ethernetNTECX101Pro30ACardEntry 6 }

ethernetNTECX101Pro30ACardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE CX101Pro30A Card."
    ::= { ethernetNTECX101Pro30ACardEntry 7 }

ethernetNTECX101Pro30ACardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE CX101Pro30A card."
    ::= { ethernetNTECX101Pro30ACardEntry 8 }

ethernetNTECX101Pro30ACardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTECX101Pro30ACardEntry 9 }

ethernetNTECX101Pro30ACardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE CX101Pro30A card."
    ::= { ethernetNTECX101Pro30ACardEntry 10 }

ethernetNTECX101Pro30ACardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE CX101Pro30A card."
    ::= { ethernetNTECX101Pro30ACardEntry 11 }

--
--Card - Ethernet NTE - Pro Rugged S Card 
--
ethernetNTECX102Pro30ACardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTECX102Pro30ACardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE Pro Rugged S Cards.
          These are supported on the FSP150CC GE101 Pro product." 
    ::= { cmEntityObjects 78 }

ethernetNTECX102Pro30ACardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTECX102Pro30ACardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTECX102Pro30ACardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTECX102Pro30ACardTable 1 }

EthernetNTECX102Pro30ACardEntry ::= SEQUENCE {
    ethernetNTECX102Pro30ACardEntityIndex             PhysicalIndex,
    ethernetNTECX102Pro30ACardAdminState              AdminState,
    ethernetNTECX102Pro30ACardOperationalState        OperationalState,
    ethernetNTECX102Pro30ACardSecondaryState          SecondaryState,
    ethernetNTECX102Pro30ACardVoltage                 Integer32,
    ethernetNTECX102Pro30ACardTemperature             Integer32,
    ethernetNTECX102Pro30ACardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTECX102Pro30ACardRestartAction           RestartType,
    ethernetNTECX102Pro30ACardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTECX102Pro30ACardSwitchPortActionPort    VariablePointer,
    ethernetNTECX102Pro30ACardSwitchPortAction        SwitchPortAction
}

ethernetNTECX102Pro30ACardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE101 Pro Card."
    ::= { ethernetNTECX102Pro30ACardEntry 1 }

ethernetNTECX102Pro30ACardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE101 Pro Card."
    ::= { ethernetNTECX102Pro30ACardEntry 2 }

ethernetNTECX102Pro30ACardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE CX102Pro30A Card."
    ::= { ethernetNTECX102Pro30ACardEntry 3 }

ethernetNTECX102Pro30ACardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE CX102Pro30A Card."
    ::= { ethernetNTECX102Pro30ACardEntry 4 }

ethernetNTECX102Pro30ACardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE CX102Pro30A Card."
    ::= { ethernetNTECX102Pro30ACardEntry 5 }

ethernetNTECX102Pro30ACardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE CX102Pro30A Card."
    ::= { ethernetNTECX102Pro30ACardEntry 6 }

ethernetNTECX102Pro30ACardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE CX102Pro30A Card."
    ::= { ethernetNTECX102Pro30ACardEntry 7 }

ethernetNTECX102Pro30ACardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE CX102Pro30A card."
    ::= { ethernetNTECX102Pro30ACardEntry 8 }

ethernetNTECX102Pro30ACardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTECX102Pro30ACardEntry 9 }

ethernetNTECX102Pro30ACardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE CX102Pro30A card."
    ::= { ethernetNTECX102Pro30ACardEntry 10 }

ethernetNTECX102Pro30ACardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE CX102Pro30A card."
    ::= { ethernetNTECX102Pro30ACardEntry 11 }
    
    
--  GE 4 Port Expansion Card (Building Integrated Timing System) 
--
ge4PortCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF Ge4PortCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to GE 4 Port Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 79 }

ge4PortCardEntry OBJECT-TYPE
    SYNTAX      Ge4PortCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ge4PortCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ge4PortCardTable 1 }

Ge4PortCardEntry ::= SEQUENCE {
    -- Port Indices 
    ge4PortCardEntityIndex                PhysicalIndex,

    -- State Management params
    ge4PortCardAdminState                 AdminState,
    ge4PortCardOperationalState           OperationalState,
    ge4PortCardSecondaryState             SecondaryState,
    ge4PortCardRowStatus                  RowStatus,
    ge4PortCardAlias                      DisplayString,
    ge4PortCardTemperature                Integer32
}
    
-- ge4PortCard Indices 

ge4PortCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { ge4PortCardEntry 1 }

-- State Management params
ge4PortCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the GE 4 Port Expansion Card."
    ::= { ge4PortCardEntry 2 }

ge4PortCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the GE 4 Port Expansion Card."
    ::= { ge4PortCardEntry 3 }

ge4PortCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the GE 4 Port Expansion Card."
    ::= { ge4PortCardEntry 4 }

ge4PortCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of ge4PortCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            ge4PortCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The ge4PortCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { ge4PortCardEntry 5 }

ge4PortCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the GE 4 Port Expansion Card."
    ::= { ge4PortCardEntry 6 }

ge4PortCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the GE 4 Port Expansion Card."
    ::= { ge4PortCardEntry 7 }

--
-- Card - Ethernet NTE - XG116PRO Card 
--
ethernetNTEXG116PROCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG116PROCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG116PRO Card.
          These are supported on the FSP150CC XG116PRO product." 
    ::= { cmEntityObjects 80 }

ethernetNTEXG116PROCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG116PROCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG116PROCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG116PROCardTable 1 }

EthernetNTEXG116PROCardEntry ::= SEQUENCE {
    ethernetNTEXG116PROCardEntityIndex             PhysicalIndex,
    ethernetNTEXG116PROCardAdminState              AdminState,
    ethernetNTEXG116PROCardOperationalState        OperationalState,
    ethernetNTEXG116PROCardSecondaryState          SecondaryState,
    ethernetNTEXG116PROCardVoltage                 Integer32,
    ethernetNTEXG116PROCardTemperature             Integer32,
    ethernetNTEXG116PROCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG116PROCardRestartAction           RestartType,
    ethernetNTEXG116PROCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG116PROCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG116PRO Card."
    ::= { ethernetNTEXG116PROCardEntry 1 }

ethernetNTEXG116PROCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG116PRO Card."
    ::= { ethernetNTEXG116PROCardEntry 2 }

ethernetNTEXG116PROCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG116PRO Card."
    ::= { ethernetNTEXG116PROCardEntry 3 }

ethernetNTEXG116PROCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG116PRO Card."
    ::= { ethernetNTEXG116PROCardEntry 4 }

ethernetNTEXG116PROCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG116PRO Card."
    ::= { ethernetNTEXG116PROCardEntry 5 }

ethernetNTEXG116PROCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG116PRO Card."
    ::= { ethernetNTEXG116PROCardEntry 6 }

ethernetNTEXG116PROCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG116PRO Card."
    ::= { ethernetNTEXG116PROCardEntry 7 }

ethernetNTEXG116PROCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG116PRO card."
    ::= { ethernetNTEXG116PROCardEntry 8 }

ethernetNTEXG116PROCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG116PROCardEntry 9 }

--
-- Card - Ethernet NTE - XG120PRO Card 
--
ethernetNTEXG120PROCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG120PROCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG120PRO Card.
          These are supported on the FSP150CC XG120PRO product." 
    ::= { cmEntityObjects 81 }

ethernetNTEXG120PROCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG120PROCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG120PROCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG120PROCardTable 1 }

EthernetNTEXG120PROCardEntry ::= SEQUENCE {
    ethernetNTEXG120PROCardEntityIndex             PhysicalIndex,
    ethernetNTEXG120PROCardAdminState              AdminState,
    ethernetNTEXG120PROCardOperationalState        OperationalState,
    ethernetNTEXG120PROCardSecondaryState          SecondaryState,
    ethernetNTEXG120PROCardVoltage                 Integer32,
    ethernetNTEXG120PROCardTemperature             Integer32,
    ethernetNTEXG120PROCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG120PROCardRestartAction           RestartType,
    ethernetNTEXG120PROCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG120PROCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG120PRO Card."
    ::= { ethernetNTEXG120PROCardEntry 1 }

ethernetNTEXG120PROCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG120PRO Card."
    ::= { ethernetNTEXG120PROCardEntry 2 }

ethernetNTEXG120PROCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG120PRO Card."
    ::= { ethernetNTEXG120PROCardEntry 3 }

ethernetNTEXG120PROCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG120PRO Card."
    ::= { ethernetNTEXG120PROCardEntry 4 }

ethernetNTEXG120PROCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG120PRO Card."
    ::= { ethernetNTEXG120PROCardEntry 5 }

ethernetNTEXG120PROCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG120PRO Card."
    ::= { ethernetNTEXG120PROCardEntry 6 }

ethernetNTEXG120PROCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG120PRO Card."
    ::= { ethernetNTEXG120PROCardEntry 7 }

ethernetNTEXG120PROCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG120PRO card."
    ::= { ethernetNTEXG120PROCardEntry 8 }

ethernetNTEXG120PROCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG120PROCardEntry 9 }

--
--Card - Ethernet NTE - GE 112ProVM Card 
--
ethernetNTEGE112ProVmCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE112ProVmCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE112 Pro VM Cards.
          These are supported on the FSP150CC GE112 Pro VM product." 
    ::= { cmEntityObjects 82 }

ethernetNTEGE112ProVmCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE112ProVmCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE112ProVmCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE112ProVmCardTable 1 }

EthernetNTEGE112ProVmCardEntry ::= SEQUENCE {
    ethernetNTEGE112ProVmCardEntityIndex             PhysicalIndex,
    ethernetNTEGE112ProVmCardAdminState              AdminState,
    ethernetNTEGE112ProVmCardOperationalState        OperationalState,
    ethernetNTEGE112ProVmCardSecondaryState          SecondaryState,
    ethernetNTEGE112ProVmCardVoltage                 Integer32,
    ethernetNTEGE112ProVmCardTemperature             Integer32,
    ethernetNTEGE112ProVmCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE112ProVmCardRestartAction           RestartType,
    ethernetNTEGE112ProVmCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE112ProVmCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE112ProVmCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE112ProVmCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114 Pro VM-CSH Card."
    ::= { ethernetNTEGE112ProVmCardEntry 1 }

ethernetNTEGE112ProVmCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114 Pro VM-CSH Card."
    ::= { ethernetNTEGE112ProVmCardEntry 2 }

ethernetNTEGE112ProVmCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE112ProVm Card."
    ::= { ethernetNTEGE112ProVmCardEntry 3 }

ethernetNTEGE112ProVmCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE112ProVm Card."
    ::= { ethernetNTEGE112ProVmCardEntry 4 }

ethernetNTEGE112ProVmCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE112ProVm Card."
    ::= { ethernetNTEGE112ProVmCardEntry 5 }

ethernetNTEGE112ProVmCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE112ProVm Card."
    ::= { ethernetNTEGE112ProVmCardEntry 6 }

ethernetNTEGE112ProVmCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE112ProVm Card."
    ::= { ethernetNTEGE112ProVmCardEntry 7 }

ethernetNTEGE112ProVmCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112ProVm card."
    ::= { ethernetNTEGE112ProVmCardEntry 8 }

ethernetNTEGE112ProVmCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE112ProVmCardEntry 9 }

ethernetNTEGE112ProVmCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE112ProVm card."
    ::= { ethernetNTEGE112ProVmCardEntry 10 }

ethernetNTEGE112ProVmCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE112ProVm card."
    ::= { ethernetNTEGE112ProVmCardEntry 11 }

--
--Card - Ethernet NTE - OSA5401 
--
ethernetNTEOSA5401CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEOSA5401CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE OSA5401 Card.
          These are supported on the OSA5401 product." 
    ::= { cmEntityObjects 83 }

ethernetNTEOSA5401CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEOSA5401CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEOSA5401CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEOSA5401CardTable 1 }

EthernetNTEOSA5401CardEntry ::= SEQUENCE {
    ethernetNTEOSA5401CardEntityIndex             PhysicalIndex,
    ethernetNTEOSA5401CardAdminState              AdminState,
    ethernetNTEOSA5401CardOperationalState        OperationalState,
    ethernetNTEOSA5401CardTableRestartAction      RestartType
}

ethernetNTEOSA5401CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE OSA5401 Card."
    ::= { ethernetNTEOSA5401CardEntry 1 }

ethernetNTEOSA5401CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE OSA5401 Card."
    ::= { ethernetNTEOSA5401CardEntry 2 }

ethernetNTEOSA5401CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE OSA5401 Card."
    ::= { ethernetNTEOSA5401CardEntry 3 }

ethernetNTEOSA5401CardTableRestartAction OBJECT-TYPE
    SYNTAX      RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform restart action on NTE OSA5401 Card."
    ::= { ethernetNTEOSA5401CardEntry 4 }


--
--Card - Ethernet NTE - OSA5405
--
ethernetNTEOSA5405CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEOSA5405CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE OSA5405 Card.
          These are supported on the OSA5405 product." 
    ::= { cmEntityObjects 84 }

ethernetNTEOSA5405CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEOSA5405CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEOSA5405CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEOSA5405CardTable 1 }

EthernetNTEOSA5405CardEntry ::= SEQUENCE {
    ethernetNTEOSA5405CardEntityIndex             PhysicalIndex,
    ethernetNTEOSA5405CardAdminState              AdminState,
    ethernetNTEOSA5405CardOperationalState        OperationalState,
    ethernetNTEOSA5405CardVoltage                 Integer32,
    ethernetNTEOSA5405CardTemperature             Integer32,
    ethernetNTEOSA5405CardTableRestartAction      RestartType
}

ethernetNTEOSA5405CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE OSA5405 Card."
    ::= { ethernetNTEOSA5405CardEntry 1 }

ethernetNTEOSA5405CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE OSA5405 Card."
    ::= { ethernetNTEOSA5405CardEntry 2 }

ethernetNTEOSA5405CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE OSA5405 Card."
    ::= { ethernetNTEOSA5405CardEntry 3 }

ethernetNTEOSA5405CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE OSA5405 Card in mV units."
    ::= { ethernetNTEOSA5405CardEntry 4 }

ethernetNTEOSA5405CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE OSA5405 Card."
    ::= { ethernetNTEOSA5405CardEntry 5 }

ethernetNTEOSA5405CardTableRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform restart action on NTE OSA5405 Card."
    ::= { ethernetNTEOSA5405CardEntry 6 }

--
--Card - Ethernet NTE - CSM
--
ethernetCSMCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetCSMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet CSM Card.
          These are supported on the OSA5430 and OSA5440 product." 
    ::= { cmEntityObjects 85 }

ethernetCSMCardEntry  OBJECT-TYPE
    SYNTAX      EthernetCSMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetCSMCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetCSMCardTable 1 }

EthernetCSMCardEntry ::= SEQUENCE {
    ethernetCSMCardEntityIndex             PhysicalIndex,
    ethernetCSMCardAdminState              AdminState,
    ethernetCSMCardOperationalState        OperationalState,
    ethernetCSMCardSecondaryState          SecondaryState,
    ethernetCSMCardVoltage                 Integer32,
    ethernetCSMCardTemperature             Integer32,
    ethernetCSMCardSnmpDyingGaspEnabled    TruthValue,
    ethernetCSMCardRestartAction           RestartType,
    ethernetCSMCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetCSMCardOscillatorType          DisplayString

}

ethernetCSMCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE CSM Card."
    ::= { ethernetCSMCardEntry 1 }

ethernetCSMCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE CSM Card."
    ::= { ethernetCSMCardEntry 2 }

ethernetCSMCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE CSM Card."
    ::= { ethernetCSMCardEntry 3 }

ethernetCSMCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE CSM Card."
    ::= { ethernetCSMCardEntry 4 }

ethernetCSMCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE CSM Card."
    ::= { ethernetCSMCardEntry 5 }

ethernetCSMCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE CSM Card."
    ::= { ethernetCSMCardEntry 6 }

ethernetCSMCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE CSM Card."
    ::= { ethernetCSMCardEntry 7 }

ethernetCSMCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE CSM card."
    ::= { ethernetCSMCardEntry 8 }

ethernetCSMCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
      "Allows specification of the fine-grained PM interval at the card level.
       This applies to all monitored PM entities. The default value of this
       attribute is interval-15min.  Valid values are interval-5min and interval-15min.
       interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetCSMCardEntry 9 }

ethernetCSMCardOscillatorType OBJECT-TYPE
    SYNTAX   DisplayString (SIZE (0..16))
    MAX-ACCESS   read-only
    STATUS   current
    DESCRIPTION
              "The mOscillatorType."
    ::= { ethernetCSMCardEntry 10 }



--
--  Auxiliary Port Expansion Card (Building Integrated Timing System) 
--
auxPortCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AuxPortCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to Auxiliary Port Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 87 }

auxPortCardEntry OBJECT-TYPE
    SYNTAX      AuxPortCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the auxPortCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { auxPortCardTable 1 }

AuxPortCardEntry ::= SEQUENCE {
    -- Port Indices 
    auxPortCardEntityIndex                PhysicalIndex,

    -- State Management params
    auxPortCardAdminState                 AdminState,
    auxPortCardOperationalState           OperationalState,
    auxPortCardSecondaryState             SecondaryState,
    auxPortCardRowStatus                  RowStatus,
    auxPortCardAlias                      DisplayString,
    auxPortCardTemperature                Integer32

}
    
-- auxPortCard Indices 

auxPortCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { auxPortCardEntry 1 }

-- State Management params
auxPortCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the Auxiliary Port Expansion Card."
    ::= { auxPortCardEntry 2 }

auxPortCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the Auxiliary Port Expansion Card."
    ::= { auxPortCardEntry 3 }

auxPortCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the Auxiliary Port Expansion Card."
    ::= { auxPortCardEntry 4 }

auxPortCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of auxPortCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            auxPortCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The auxPortCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { auxPortCardEntry 5 }

auxPortCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the Auxiliary Port Expansion Card."
    ::= { auxPortCardEntry 6 }

auxPortCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Auxiliary Port Expansion Card."
    ::= { auxPortCardEntry 7 }


--
--Card - Ethernet NTE - GE 102ProH Card 
--
ethernetNTEGE102ProHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE102ProHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE102 Pro H Cards.
          These are supported on the FSP150CC GE102 Pro H product." 
    ::= { cmEntityObjects 88 }

ethernetNTEGE102ProHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE102ProHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE102ProHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE102ProHCardTable 1 }

EthernetNTEGE102ProHCardEntry ::= SEQUENCE {
    ethernetNTEGE102ProHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE102ProHCardAdminState              AdminState,
    ethernetNTEGE102ProHCardOperationalState        OperationalState,
    ethernetNTEGE102ProHCardSecondaryState          SecondaryState,
    ethernetNTEGE102ProHCardVoltage                 Integer32,
    ethernetNTEGE102ProHCardTemperature             Integer32,
    ethernetNTEGE102ProHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE102ProHCardRestartAction           RestartType,
    ethernetNTEGE102ProHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE102ProHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE102ProHCardSwitchPortAction        SwitchPortAction,
    ethernetNTEGE102ProHCardPSU1State               OperationalState,
    ethernetNTEGE102ProHCardPSU2State               OperationalState,
    ethernetNTEGE102ProHCardFAN1State               OperationalState,
    ethernetNTEGE102ProHCardFAN2State               OperationalState,
    ethernetNTEGE102ProHCardPsuType                 PsuType
}

ethernetNTEGE102ProHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE102 Pro H Card."
    ::= { ethernetNTEGE102ProHCardEntry 1 }

ethernetNTEGE102ProHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE102 Pro H Card."
    ::= { ethernetNTEGE102ProHCardEntry 2 }

ethernetNTEGE102ProHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE102 Pro H Card."
    ::= { ethernetNTEGE102ProHCardEntry 3 }

ethernetNTEGE102ProHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE102 Pro H Card."
    ::= { ethernetNTEGE102ProHCardEntry 4 }

ethernetNTEGE102ProHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE102 Pro H Card."
    ::= { ethernetNTEGE102ProHCardEntry 5 }

ethernetNTEGE102ProHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE102 Pro H Card."
    ::= { ethernetNTEGE102ProHCardEntry 6 }

ethernetNTEGE102ProHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE102 Pro H Card."
    ::= { ethernetNTEGE102ProHCardEntry 7 }

ethernetNTEGE102ProHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE102 Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 8 }

ethernetNTEGE102ProHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE102ProHCardEntry 9 }

ethernetNTEGE102ProHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE102 Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 10 }

ethernetNTEGE102ProHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE102 Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 11 }

ethernetNTEGE102ProHCardPSU1State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of Power Supply Unit 1 on Ethernet NTE GE102Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 12 }

ethernetNTEGE102ProHCardPSU2State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of Power Supply Unit 2 on Ethernet NTE GE102Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 13 }

ethernetNTEGE102ProHCardFAN1State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of FAN 1 on Ethernet NTE GE102Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 14 }

ethernetNTEGE102ProHCardFAN2State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of FAN 2 on Ethernet NTE GE102Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 15 }

ethernetNTEGE102ProHCardPsuType OBJECT-TYPE
    SYNTAX  PsuType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "PSU Type on Ethernet NTE GE102Pro H card."
    ::= { ethernetNTEGE102ProHCardEntry 16 }


--
--Card - Ethernet NTE - GE 102ProEFMH Card 
--
ethernetNTEGE102ProEFMHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE102ProEFMHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE102 Pro EFMH Cards.
          These are supported on the FSP150CC GE102 Pro EFMH product." 
    ::= { cmEntityObjects 89 }

ethernetNTEGE102ProEFMHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE102ProEFMHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE102ProEFMHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE102ProEFMHCardTable 1 }

EthernetNTEGE102ProEFMHCardEntry ::= SEQUENCE {
    ethernetNTEGE102ProEFMHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE102ProEFMHCardAdminState              AdminState,
    ethernetNTEGE102ProEFMHCardOperationalState        OperationalState,
    ethernetNTEGE102ProEFMHCardSecondaryState          SecondaryState,
    ethernetNTEGE102ProEFMHCardVoltage                 Integer32,
    ethernetNTEGE102ProEFMHCardTemperature             Integer32,
    ethernetNTEGE102ProEFMHCardRestartAction           RestartType,
    ethernetNTEGE102ProEFMHCardPSU1State               OperationalState,
    ethernetNTEGE102ProEFMHCardPSU2State               OperationalState,
    ethernetNTEGE102ProEFMHCardFAN1State               OperationalState,
    ethernetNTEGE102ProEFMHCardFAN2State               OperationalState,
    ethernetNTEGE102ProEFMHCardPsuType                 PsuType,
    ethernetNTEGE102ProEFMHCardLLFMode                 CmCPMRLinkLossFwdMode,
    ethernetNTEGE102ProEFMHCardLLFModeAction           CmCPMRLinkLossFwdMode
}

ethernetNTEGE102ProEFMHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE102 Pro EFMH Card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 1 }

ethernetNTEGE102ProEFMHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE102 Pro EFMH Card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 2 }

ethernetNTEGE102ProEFMHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE102 Pro EFMH Card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 3 }

ethernetNTEGE102ProEFMHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE102 Pro EFMHCard."
    ::= { ethernetNTEGE102ProEFMHCardEntry 4 }

ethernetNTEGE102ProEFMHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE102 Pro EFMH Card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 5 }

ethernetNTEGE102ProEFMHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE102 Pro EFMH Card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 6 }

ethernetNTEGE102ProEFMHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE102Pro EFMH card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 7 }

ethernetNTEGE102ProEFMHCardPSU1State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of Power Supply Unit 1 on Ethernet NTE GE102Pro EFMH card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 8 }

ethernetNTEGE102ProEFMHCardPSU2State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of Power Supply Unit 2 on Ethernet NTE GE102Pro EFMH card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 9 }

ethernetNTEGE102ProEFMHCardFAN1State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of FAN 1 on Ethernet NTE GE102Pro EFMH card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 10 }

ethernetNTEGE102ProEFMHCardFAN2State OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Operational State of FAN 2 on Ethernet NTE GE102Pro EFMH card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 11 }

ethernetNTEGE102ProEFMHCardPsuType OBJECT-TYPE
    SYNTAX  PsuType 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "PSU Type on Ethernet NTE GE102Pro EFMH card."
    ::= { ethernetNTEGE102ProEFMHCardEntry 12 }

ethernetNTEGE102ProEFMHCardLLFMode OBJECT-TYPE
    SYNTAX      CmCPMRLinkLossFwdMode 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "Current Link Loss Forwarding Mode on CPMR."
    ::= { ethernetNTEGE102ProEFMHCardEntry 13 }

ethernetNTEGE102ProEFMHCardLLFModeAction OBJECT-TYPE
    SYNTAX      CmCPMRLinkLossFwdMode 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
         "Operation to set the appropriate mode on CPMR."
    ::= { ethernetNTEGE102ProEFMHCardEntry 14 }

--  Management Card for OSA3350

ethernetOsa3350MgntCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetOsa3350MgntCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet Osa3350 Mgnt Cards." 
    ::= { cmEntityObjects 90 }

ethernetOsa3350MgntCardEntry  OBJECT-TYPE
    SYNTAX      EthernetOsa3350MgntCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetOsa3350MgntCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetOsa3350MgntCardTable 1 }

EthernetOsa3350MgntCardEntry ::= SEQUENCE {
    ethernetOsa3350MgntCardEntityIndex             PhysicalIndex,
    ethernetOsa3350MgntCardAdminState              AdminState,
    ethernetOsa3350MgntCardOperationalState        OperationalState,
    ethernetOsa3350MgntCardSecondaryState          SecondaryState,
    ethernetOsa3350MgntCardRestartAction           RestartType,
    ethernetOsa3350MgntCardResyncAction            ResyncType
}

ethernetOsa3350MgntCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet Osa3350 Mgnt Card."
    ::= { ethernetOsa3350MgntCardEntry 1 }

ethernetOsa3350MgntCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet Osa3350 Mgnt Card."
    ::= { ethernetOsa3350MgntCardEntry 2 }

ethernetOsa3350MgntCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet Osa3350 Mgnt Card."
    ::= { ethernetOsa3350MgntCardEntry 3 }

ethernetOsa3350MgntCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet Osa3350 Mgnt Card."
    ::= { ethernetOsa3350MgntCardEntry 4 }

ethernetOsa3350MgntCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified restart action on the 
              Ethernet Osa3350 Mgnt card."
    ::= { ethernetOsa3350MgntCardEntry 5 }

ethernetOsa3350MgntCardResyncAction OBJECT-TYPE
    SYNTAX  ResyncType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified resync action on the 
              Ethernet Osa3350 Mgnt card."
    ::= { ethernetOsa3350MgntCardEntry 6 }

--
-- Card - Ethernet NTE - XG116PRO (H) Card 
--
ethernetNTEXG116PROHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG116PROHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG116PRO (H) Card.
          These are supported on the FSP150CC XG116PRO (H) product." 
    ::= { cmEntityObjects 91 }

ethernetNTEXG116PROHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG116PROHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG116PROHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG116PROHCardTable 1 }

EthernetNTEXG116PROHCardEntry ::= SEQUENCE {
    ethernetNTEXG116PROHCardEntityIndex             PhysicalIndex,
    ethernetNTEXG116PROHCardAdminState              AdminState,
    ethernetNTEXG116PROHCardOperationalState        OperationalState,
    ethernetNTEXG116PROHCardSecondaryState          SecondaryState,
    ethernetNTEXG116PROHCardVoltage                 Integer32,
    ethernetNTEXG116PROHCardTemperature             Integer32,
    ethernetNTEXG116PROHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG116PROHCardRestartAction           RestartType,
    ethernetNTEXG116PROHCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG116PROHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG116PRO (H) Card."
    ::= { ethernetNTEXG116PROHCardEntry 1 }

ethernetNTEXG116PROHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG116PRO (H) Card."
    ::= { ethernetNTEXG116PROHCardEntry 2 }

ethernetNTEXG116PROHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG116PRO (H) Card."
    ::= { ethernetNTEXG116PROHCardEntry 3 }

ethernetNTEXG116PROHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG116PRO (H) Card."
    ::= { ethernetNTEXG116PROHCardEntry 4 }

ethernetNTEXG116PROHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG116PRO (H) Card."
    ::= { ethernetNTEXG116PROHCardEntry 5 }

ethernetNTEXG116PROHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG116PRO (H) Card."
    ::= { ethernetNTEXG116PROHCardEntry 6 }

ethernetNTEXG116PROHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG116PRO (H) Card."
    ::= { ethernetNTEXG116PROHCardEntry 7 }

ethernetNTEXG116PROHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG116PRO (H) card."
    ::= { ethernetNTEXG116PROHCardEntry 8 }

ethernetNTEXG116PROHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG116PROHCardEntry 9 }


--
--Card - Ethernet NTE - GO102Pro-S Mini Card 
--
ethernetNTEGO102ProSMCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGO102ProSMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE Pro GO102Pro-S Mini Cards.
          These are supported on the FSP150CC GO102Pro-S Mini product." 
    ::= { cmEntityObjects 92 }

ethernetNTEGO102ProSMCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGO102ProSMCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGO102ProSMCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGO102ProSMCardTable 1 }

EthernetNTEGO102ProSMCardEntry ::= SEQUENCE {
    ethernetNTEGO102ProSMCardEntityIndex             PhysicalIndex,
    ethernetNTEGO102ProSMCardAdminState              AdminState,
    ethernetNTEGO102ProSMCardOperationalState        OperationalState,
    ethernetNTEGO102ProSMCardSecondaryState          SecondaryState,
    ethernetNTEGO102ProSMCardVoltage                 Integer32,
    ethernetNTEGO102ProSMCardTemperature             Integer32,
    ethernetNTEGO102ProSMCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGO102ProSMCardRestartAction           RestartType,
    ethernetNTEGO102ProSMCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGO102ProSMCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGO102ProSMCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGO102ProSMCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GO102Pro-SM Card."
    ::= { ethernetNTEGO102ProSMCardEntry 1 }

ethernetNTEGO102ProSMCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GO102Pro-SM Card."
    ::= { ethernetNTEGO102ProSMCardEntry 2 }

ethernetNTEGO102ProSMCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GO102Pro-SM Card."
    ::= { ethernetNTEGO102ProSMCardEntry 3 }

ethernetNTEGO102ProSMCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GO102Pro-SM Card."
    ::= { ethernetNTEGO102ProSMCardEntry 4 }

ethernetNTEGO102ProSMCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GO102Pro-SM Card."
    ::= { ethernetNTEGO102ProSMCardEntry 5 }

ethernetNTEGO102ProSMCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GO102Pro-SM Card."
    ::= { ethernetNTEGO102ProSMCardEntry 6 }

ethernetNTEGO102ProSMCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GO102Pro-SM Card."
    ::= { ethernetNTEGO102ProSMCardEntry 7 }

ethernetNTEGO102ProSMCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GO102Pro-SM card."
    ::= { ethernetNTEGO102ProSMCardEntry 8 }

ethernetNTEGO102ProSMCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGO102ProSMCardEntry 9 }

ethernetNTEGO102ProSMCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GO102Pro-SM card."
    ::= { ethernetNTEGO102ProSMCardEntry 10 }

ethernetNTEGO102ProSMCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GO102Pro-SM card."
    ::= { ethernetNTEGO102ProSMCardEntry 11 }


--
-- Card - Ethernet NTE - XG118PRO (SH) Card 
--
ethernetNTEXG118PROSHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG118PROSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG118PRO (SH) Card.
          These are supported on the FSP150CC XG118PRO (SH) product." 
    ::= { cmEntityObjects 93 }

ethernetNTEXG118PROSHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG118PROSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG118PROSHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG118PROSHCardTable 1 }

EthernetNTEXG118PROSHCardEntry ::= SEQUENCE {
    ethernetNTEXG118PROSHCardEntityIndex             PhysicalIndex,
    ethernetNTEXG118PROSHCardAdminState              AdminState,
    ethernetNTEXG118PROSHCardOperationalState        OperationalState,
    ethernetNTEXG118PROSHCardSecondaryState          SecondaryState,
    ethernetNTEXG118PROSHCardVoltage                 Integer32,
    ethernetNTEXG118PROSHCardTemperature             Integer32,
    ethernetNTEXG118PROSHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG118PROSHCardRestartAction           RestartType,
    ethernetNTEXG118PROSHCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG118PROSHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG118PRO (SH) Card."
    ::= { ethernetNTEXG118PROSHCardEntry 1 }

ethernetNTEXG118PROSHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG118PRO (SH) Card."
    ::= { ethernetNTEXG118PROSHCardEntry 2 }

ethernetNTEXG118PROSHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG118PRO (SH) Card."
    ::= { ethernetNTEXG118PROSHCardEntry 3 }

ethernetNTEXG118PROSHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG118PRO (SH) Card."
    ::= { ethernetNTEXG118PROSHCardEntry 4 }

ethernetNTEXG118PROSHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG118PRO (SH) Card."
    ::= { ethernetNTEXG118PROSHCardEntry 5 }

ethernetNTEXG118PROSHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG118PRO (SH) Card."
    ::= { ethernetNTEXG118PROSHCardEntry 6 }

ethernetNTEXG118PROSHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG118PRO (SH) Card."
    ::= { ethernetNTEXG118PROSHCardEntry 7 }

ethernetNTEXG118PROSHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG118PRO (SH) card."
    ::= { ethernetNTEXG118PROSHCardEntry 8 }

ethernetNTEXG118PROSHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG118PROSHCardEntry 9 }

--
-- Card - Ethernet NTE - XG118PROAC (SH) Card 
--
ethernetNTEXG118PROACSHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG118PROACSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG118PROAC (SH) Card.
          These are supported on the FSP150CC XG118PROAC (SH) product." 
    ::= { cmEntityObjects 94 }

ethernetNTEXG118PROACSHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG118PROACSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG118PROACSHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG118PROACSHCardTable 1 }

EthernetNTEXG118PROACSHCardEntry ::= SEQUENCE {
    ethernetNTEXG118PROACSHCardEntityIndex             PhysicalIndex,
    ethernetNTEXG118PROACSHCardAdminState              AdminState,
    ethernetNTEXG118PROACSHCardOperationalState        OperationalState,
    ethernetNTEXG118PROACSHCardSecondaryState          SecondaryState,
    ethernetNTEXG118PROACSHCardVoltage                 Integer32,
    ethernetNTEXG118PROACSHCardTemperature             Integer32,
    ethernetNTEXG118PROACSHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG118PROACSHCardRestartAction           RestartType,
    ethernetNTEXG118PROACSHCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG118PROACSHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION 
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG118PROAC (SH) Card."
    ::= { ethernetNTEXG118PROACSHCardEntry 1 }

ethernetNTEXG118PROACSHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG118PROAC (SH) Card."
    ::= { ethernetNTEXG118PROACSHCardEntry 2 }

ethernetNTEXG118PROACSHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG118PROAC (SH) Card."
    ::= { ethernetNTEXG118PROACSHCardEntry 3 }

ethernetNTEXG118PROACSHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG118PROAC (SH) Card."
    ::= { ethernetNTEXG118PROACSHCardEntry 4 }

ethernetNTEXG118PROACSHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG118PROAC (SH) Card."
    ::= { ethernetNTEXG118PROACSHCardEntry 5 }

ethernetNTEXG118PROACSHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG118PROAC (SH) Card."
    ::= { ethernetNTEXG118PROACSHCardEntry 6 }

ethernetNTEXG118PROACSHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG118PROAC (SH) Card."
    ::= { ethernetNTEXG118PROACSHCardEntry 7 }

ethernetNTEXG118PROACSHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG118PROAC (SH) card."
    ::= { ethernetNTEXG118PROACSHCardEntry 8 }

ethernetNTEXG118PROACSHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG118PROACSHCardEntry 9 }

--
--Card - Ethernet NTE - GE 114ProVM-CH Card 
--
ethernetNTEGE114ProVmSHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE114ProVmSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE114 Pro VM-CH Cards.
          These are supported on the FSP150CC GE114 Pro VM-CH product." 
    ::= { cmEntityObjects 95 }

ethernetNTEGE114ProVmSHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE114ProVmSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE114ProVmSHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE114ProVmSHCardTable 1 }

EthernetNTEGE114ProVmSHCardEntry ::= SEQUENCE {
    ethernetNTEGE114ProVmSHCardEntityIndex             PhysicalIndex,
    ethernetNTEGE114ProVmSHCardAdminState              AdminState,
    ethernetNTEGE114ProVmSHCardOperationalState        OperationalState,
    ethernetNTEGE114ProVmSHCardSecondaryState          SecondaryState,
    ethernetNTEGE114ProVmSHCardVoltage                 Integer32,
    ethernetNTEGE114ProVmSHCardTemperature             Integer32,
    ethernetNTEGE114ProVmSHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE114ProVmSHCardRestartAction           RestartType,
    ethernetNTEGE114ProVmSHCardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE114ProVmSHCardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE114ProVmSHCardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE114ProVmSHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE114 Pro VM-CH Card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 1 }

ethernetNTEGE114ProVmSHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE114 Pro VM-CH Card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 2 }

ethernetNTEGE114ProVmSHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE114ProVmSH Card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 3 }

ethernetNTEGE114ProVmSHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE114ProVmSH Card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 4 }

ethernetNTEGE114ProVmSHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE114ProVmSH Card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 5 }

ethernetNTEGE114ProVmSHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE114ProVmSH Card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 6 }

ethernetNTEGE114ProVmSHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE114ProVmSH Card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 7 }

ethernetNTEGE114ProVmSHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmSH card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 8 }

ethernetNTEGE114ProVmSHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE114ProVmSHCardEntry 9 }

ethernetNTEGE114ProVmSHCardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE114ProVmSH card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 10 }

ethernetNTEGE114ProVmSHCardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE114ProVmSH card."
    ::= { ethernetNTEGE114ProVmSHCardEntry 11 }


--
--Card - Ethernet NTE - GE 104 Card 
--
ethernetNTEGE104CardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEGE104CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE GE104 Cards.
          These are supported on the FSP150CC GE104 product." 
    ::= { cmEntityObjects 96 }

ethernetNTEGE104CardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEGE104CardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEGE104CardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEGE104CardTable 1 }

EthernetNTEGE104CardEntry ::= SEQUENCE {
    ethernetNTEGE104CardEntityIndex             PhysicalIndex,
    ethernetNTEGE104CardAdminState              AdminState,
    ethernetNTEGE104CardOperationalState        OperationalState,
    ethernetNTEGE104CardSecondaryState          SecondaryState,
    ethernetNTEGE104CardVoltage                 Integer32,
    ethernetNTEGE104CardTemperature             Integer32,
    ethernetNTEGE104CardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEGE104CardRestartAction           RestartType,
    ethernetNTEGE104CardFineGrainedPmInterval   CmPmIntervalType,
    ethernetNTEGE104CardSwitchPortActionPort    VariablePointer,
    ethernetNTEGE104CardSwitchPortAction        SwitchPortAction
}

ethernetNTEGE104CardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE GE104 Card."
    ::= { ethernetNTEGE104CardEntry 1 }

ethernetNTEGE104CardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE GE104 Card."
    ::= { ethernetNTEGE104CardEntry 2 }

ethernetNTEGE104CardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE GE104 Card."
    ::= { ethernetNTEGE104CardEntry 3 }

ethernetNTEGE104CardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE GE104 Card."
    ::= { ethernetNTEGE104CardEntry 4 }

ethernetNTEGE104CardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE GE102 Card."
    ::= { ethernetNTEGE104CardEntry 5 }

ethernetNTEGE104CardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE GE104 Card."
    ::= { ethernetNTEGE104CardEntry 6 }

ethernetNTEGE104CardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE GE104 Card."
    ::= { ethernetNTEGE104CardEntry 7 }

ethernetNTEGE104CardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE104 card."
    ::= { ethernetNTEGE104CardEntry 8 }

ethernetNTEGE104CardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the card level.
              This applies to all monitored PM entities. The default value of this
              attribute is interval-15min.  Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered fine-grained intervals."
    ::= { ethernetNTEGE104CardEntry 9 }

ethernetNTEGE104CardSwitchPortActionPort OBJECT-TYPE
    SYNTAX  VariablePointer
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
             "Which port to perform specified switch action on the Ethernet NTE GE104 card."
    ::= { ethernetNTEGE104CardEntry 10 }

ethernetNTEGE104CardSwitchPortAction OBJECT-TYPE
    SYNTAX  SwitchPortAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the Ethernet NTE GE104 card."
    ::= { ethernetNTEGE104CardEntry 11 }



--
-- Card - Ethernet NTE - XG120PROSH Card 
--
ethernetNTEXG120PROSHCardTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF EthernetNTEXG120PROSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "Contains information on Ethernet NTE XG120PROSH Card.
          These are supported on the FSP150CC XG120PROSH product." 
    ::= { cmEntityObjects 97 }

ethernetNTEXG120PROSHCardEntry  OBJECT-TYPE
    SYNTAX      EthernetNTEXG120PROSHCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the ethernetNTEXG120PROSHCardTable."
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { ethernetNTEXG120PROSHCardTable 1 }

EthernetNTEXG120PROSHCardEntry ::= SEQUENCE {
    ethernetNTEXG120PROSHCardEntityIndex             PhysicalIndex,
    ethernetNTEXG120PROSHCardAdminState              AdminState,
    ethernetNTEXG120PROSHCardOperationalState        OperationalState,
    ethernetNTEXG120PROSHCardSecondaryState          SecondaryState,
    ethernetNTEXG120PROSHCardVoltage                 Integer32,
    ethernetNTEXG120PROSHCardTemperature             Integer32,
    ethernetNTEXG120PROSHCardSnmpDyingGaspEnabled    TruthValue,
    ethernetNTEXG120PROSHCardRestartAction           RestartType,
    ethernetNTEXG120PROSHCardFineGrainedPmInterval   CmPmIntervalType
}

ethernetNTEXG120PROSHCardEntityIndex OBJECT-TYPE
    SYNTAX  PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for the Ethernet NTE XG120PROSH Card."
    ::= { ethernetNTEXG120PROSHCardEntry 1 }

ethernetNTEXG120PROSHCardAdminState OBJECT-TYPE
    SYNTAX  AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Administrative State of the Ethernet NTE XG120PROSH Card."
    ::= { ethernetNTEXG120PROSHCardEntry 2 }

ethernetNTEXG120PROSHCardOperationalState OBJECT-TYPE
    SYNTAX  OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Operational State of the Ethernet NTE XG120PROSH Card."
    ::= { ethernetNTEXG120PROSHCardEntry 3 }

ethernetNTEXG120PROSHCardSecondaryState OBJECT-TYPE
    SYNTAX  SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Secondary State of the Ethernet NTE XG120PROSH Card."
    ::= { ethernetNTEXG120PROSHCardEntry 4 }

ethernetNTEXG120PROSHCardVoltage OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Voltage of the Ethernet NTE XG120PROSH Card."
    ::= { ethernetNTEXG120PROSHCardEntry 5 }

ethernetNTEXG120PROSHCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the Ethernet NTE XG120PROSH Card."
    ::= { ethernetNTEXG120PROSHCardEntry 6 }

ethernetNTEXG120PROSHCardSnmpDyingGaspEnabled OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
          "Whether or not SNMP dying gasp is enabled 
           on the Ethernet NTE XG120PROSH Card."
    ::= { ethernetNTEXG120PROSHCardEntry 7 }

ethernetNTEXG120PROSHCardRestartAction OBJECT-TYPE
    SYNTAX  RestartType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows to perform specified action on the 
              Ethernet NTE XG120PROSH card."
    ::= { ethernetNTEXG120PROSHCardEntry 8 }

ethernetNTEXG120PROSHCardFineGrainedPmInterval   OBJECT-TYPE 
    SYNTAX  CmPmIntervalType 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
             "Allows specification of the fine-grained PM interval at the 
              card level.  This applies to all monitored PM entities. 
              The default value of this attribute is interval-15min.  
              Valid values are interval-5min and interval-15min.
              interval-1day and rollover are not considered 
              fine-grained intervals."
    ::= { ethernetNTEXG120PROSHCardEntry 9 }



--
--  Mb Gnss Expansion Card (Building Integrated Timing System) 
--
mbGnssCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MbGnssCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to MB GNSS  Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 98 }

mbGnssCardEntry OBJECT-TYPE
    SYNTAX      MbGnssCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the mbGnssCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { mbGnssCardTable 1 }

MbGnssCardEntry ::= SEQUENCE {
    -- Port Indices 
    mbGnssCardEntityIndex                PhysicalIndex,

    -- State Management params
    mbGnssCardAdminState                 AdminState,
    mbGnssCardOperationalState           OperationalState,
    mbGnssCardSecondaryState             SecondaryState,
    mbGnssCardRowStatus                  RowStatus,
    mbGnssCardAlias                      DisplayString

}
    
-- mbGnssCard Indices 



mbGnssCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { mbGnssCardEntry 1 }

-- State Management params
mbGnssCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the BITS 16 Port Expansion Card."
    ::= { mbGnssCardEntry 2 }

mbGnssCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the BITS 16 Port Expansion Card."
    ::= { mbGnssCardEntry 3 }

mbGnssCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the BITS 16 Port Expansion Card."
    ::= { mbGnssCardEntry 4 }

mbGnssCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of mbGnssCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            mbGnssCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The mbGnssCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { mbGnssCardEntry 5 }

mbGnssCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the MB GNSS Expansion Card."
    ::= { mbGnssCardEntry 6 }

--
--  IRIG Card
--
f3IrigCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3IrigCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to IRIG Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 99 }

f3IrigCardEntry OBJECT-TYPE
    SYNTAX      F3IrigCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the f3IrigCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { f3IrigCardTable 1 }

F3IrigCardEntry ::= SEQUENCE {
    -- Port indices 
    f3IrigCardEntityIndex                PhysicalIndex,

    -- Management params
    f3IrigCardAlias                      DisplayString,
    f3IrigCardAdminState                 AdminState,
    f3IrigCardOperationalState           OperationalState,
    f3IrigCardSecondaryState             SecondaryState,
    f3IrigCardTemperature                Integer32,
    f3IrigCardStorageType                StorageType,
    f3IrigCardRowStatus                  RowStatus
}
    
-- Card indices 
f3IrigCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { f3IrigCardEntry 1 }

-- Management params
f3IrigCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the GE 4 Port Expansion Card."
    ::= { f3IrigCardEntry 2 }

f3IrigCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the GE 4 Port Expansion Card."
    ::= { f3IrigCardEntry 3 }

f3IrigCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the GE 4 Port Expansion Card."
    ::= { f3IrigCardEntry 4 }

f3IrigCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the GE 4 Port Expansion Card."
    ::= { f3IrigCardEntry 5 }

f3IrigCardTemperature OBJECT-TYPE
    SYNTAX  Integer32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Temperature of the GE 4 Port Expansion Card."
    ::= { f3IrigCardEntry 6 }

f3IrigCardStorageType OBJECT-TYPE
    SYNTAX  StorageType
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
             "Storage Type of the IRIG Card."
    ::= { f3IrigCardEntry 7 }

f3IrigCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of f3IrigCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            f3IrigCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The f3IrigCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { f3IrigCardEntry 8 }

--
--  Composite Clock Expansion Card (Building Integrated Timing System) 
--
compositeClockCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CompositeClockCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A list of entries corresponding to Composite Clock Expansion Card 
             Facilities for configuration purposes."
    ::= { cmEntityObjects 100 }

compositeClockCardEntry OBJECT-TYPE
    SYNTAX      CompositeClockCardEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the compositeClockCardTable"
    INDEX { neIndex, shelfIndex, slotIndex }
    ::= { compositeClockCardTable 1 }

CompositeClockCardEntry ::= SEQUENCE {
    -- Port Indices 
    compositeClockCardEntityIndex                PhysicalIndex,

    -- State Management params
    compositeClockCardAdminState                 AdminState,
    compositeClockCardOperationalState           OperationalState,
    compositeClockCardSecondaryState             SecondaryState,
    compositeClockCardRowStatus                  RowStatus,
    compositeClockCardAlias                      DisplayString

}
    
-- compositeClockCard Indices 



compositeClockCardEntityIndex OBJECT-TYPE
    SYNTAX      PhysicalIndex 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
           "Entity Index from ENTITY-MIB for this card"
    ::= { compositeClockCardEntry 1 }

-- State Management params
compositeClockCardAdminState OBJECT-TYPE
    SYNTAX      AdminState 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object represents the Administrative State of the BITS 16 Port Expansion Card."
    ::= { compositeClockCardEntry 2 }

compositeClockCardOperationalState OBJECT-TYPE
    SYNTAX      OperationalState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Operational State of the BITS 16 Port Expansion Card."
    ::= { compositeClockCardEntry 3 }

compositeClockCardSecondaryState OBJECT-TYPE
    SYNTAX      SecondaryState 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "This object represents the Secondary State of the BITS 16 Port Expansion Card."
    ::= { compositeClockCardEntry 4 }

compositeClockCardRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
           "The status of this row.  An entry MUST NOT exist in the 
            active state unless all objects in the entry have an 
            appropriate value, as described
            in the description clause for each writable object.

            The values of compositeClockCardRowStatus supported are
            createAndGo(4) and destroy(6).  All mandatory attributes
            must be specified in a single SNMP SET request with
            compositeClockCardRowStatus value as createAndGo(4).
            Upon successful row creation, this object has a
            value of active(1).

            The compositeClockCardRowStatus object may be modified if
            the associated instance of this object is equal to active(1)."
    ::= { compositeClockCardEntry 5 }

compositeClockCardAlias OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..256))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "This object allows SNMP management entities to provide an 
             alias to the COMPOSITE CLOCK Expansion Card."
    ::= { compositeClockCardEntry 6 }

--
--Card - StorageDevice
--
f3StorageDeviceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF F3StorageDeviceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
     "Contains information on storage devices within the server card."
::= { cmEntityObjects 101 }

f3StorageDeviceEntry  OBJECT-TYPE
    SYNTAX      F3StorageDeviceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A conceptual row in the f3StorageDeviceTable."
    AUGMENTS { serverCardEntry }
    ::= { f3StorageDeviceTable 1 }

F3StorageDeviceEntry ::= SEQUENCE {
    f3StorageDeviceInternalSsdHealth         TruthValue,
    f3StorageDeviceExternalSsdStatus         StorageStatus,
    f3StorageDeviceWearoutLevel              Integer32
}

f3StorageDeviceInternalSsdHealth OBJECT-TYPE
    SYNTAX  TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Indicates health of internal SSD on server card."
    ::= { f3StorageDeviceEntry 1 }

f3StorageDeviceExternalSsdStatus OBJECT-TYPE
    SYNTAX  StorageStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Indicates status of external SSD on server card."
    ::= { f3StorageDeviceEntry 2 }

f3StorageDeviceWearoutLevel OBJECT-TYPE
    SYNTAX  Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
             "Indicated wearout level of internal SSD on server card."
    ::= { f3StorageDeviceEntry 3 }

--
-- Conformance
--
cmEntityCompliances OBJECT IDENTIFIER ::= {cmEntityConformance 1}
cmEntityGroups      OBJECT IDENTIFIER ::= {cmEntityConformance 2}

cmEntityCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "Describes the requirements for conformance to the CM Entity
             group."
    MODULE  -- this module
        MANDATORY-GROUPS {
              cmEntityObjectGroup, commonEntityGroup, psuGroup, fanGroup, hubshelfGroup,
              nteGe206CardGroup, nteGe201SyncECardGroup, nteGe201NonSyncECardGroup,
              nteGe206FCardGroup, nteGe206VCardGroup, nteXg210CardGroup, neLLDPParamsGroup,
              nteSh1PcsCardGroup, nteXg210CCardGroup, geGE8SCryptoConnectorCardGroup,
              nteOsa5420CardGroup, nteOsa5421CardGroup, bits16PortCardGroup, pps16PortCardGroup,
              clk16PortCardGroup, todPps16PortCardGroup,ge4PortCardGroup, nteXg116ProCardGroup, 
              nteXg120ProCardGroup, nteOsa5401CardGroup, nteOsa5405CardGroup, csmCardGroup,
              auxPortCardGroup, nteGe102ProHCardGroup, nteGe102ProEFMHCardGroup,
              nteXg116ProHCardGroup, nteXg118ProSHCardGroup, nteXg118ProacSHCardGroup,
              nteXg120ProSHCardGroup
        }
    ::= { cmEntityCompliances 1 }

cmEntityObjectGroup OBJECT-GROUP
    OBJECTS {
        neIndex, neName, neType, neContact, neLocation, neDescription,
        neCmdPromptPrefix, neAccepted, neFromPort, neProvAction,
        neStorageType, neRowStatus,
        shelfIndex, shelfEntityIndex, shelfType, shelfbackplaneRev,
        shelfbackplaneDOM, shelfbackplaneSerialNo, shelfAction,
        shelfAdminState, shelfOperationalState, shelfSecondaryState,
        shelfMfgSite, shelfOscillatorType, shelfLedControl,
        slotIndex, slotEntityIndex, slotType, slotCardType, slotCardUnitName,
        slotCardFormatVersion, slotCardCLEICode, slotCardPartNumber,
        slotCardHwRev, slotCardSwRev, slotCardSerialNum,
        slotCardMfgName, slotCardMfgDate, slotCardMfgSite,
        slotSecondaryState, slotCardPhysicalAddress,
        psuEntityIndex, psuType, psuAdminState, psuOperationalState,
        psuSecondaryState,psuOutputVoltage,psuTemperature,psuOutputCurrent,psuStorageType,psuRowStatus,
        fanEntityIndex, fanAdminState, fanOperationalState,
        fanSecondaryState,
        scuEntityIndex, scuAdminState, scuOperationalState,
        scuSecondaryState, scuVoltage, scuTemperature,
        scuRestartAction, scuStorageType, scuRowStatus,
        nemiEntityIndex, nemiAdminState, nemiOperationalState,
        nemiSecondaryState, nemiVoltage, nemiTemperature,
        nemiRestartAction, nemiStorageType, nemiRowStatus, nemiForceOffLineAction,
        ethernetNTUCardEntityIndex, ethernetNTUCardAdminState,
        ethernetNTUCardOperationalState, ethernetNTUCardSecondaryState,
        ethernetNTUCardVoltage, ethernetNTUCardTemperature,
        ethernetNTUCardStorageType, ethernetNTUCardRowStatus,
        ethernetCPMRCardEntityIndex, ethernetCPMRCardAdminState,
        ethernetCPMRCardOperationalState, ethernetCPMRCardSecondaryState,
        ethernetCPMRCardVoltage, ethernetCPMRCardTemperature, 
        ethernetCPMRCardRestartAction, ethernetCPMRCardPSU1State,
        ethernetCPMRCardPSU2State, ethernetCPMRCardFAN1State,
        ethernetCPMRCardFAN2State, ethernetCPMRCardPsuType,
        ethernetCPMRCardLLFMode, ethernetCPMRCardLLFModeAction,

        ethernetNTEGE101CardEntityIndex, ethernetNTEGE101CardAdminState,
        ethernetNTEGE101CardOperationalState, ethernetNTEGE101CardSecondaryState,
        ethernetNTEGE101CardVoltage, ethernetNTEGE101CardTemperature,
        ethernetNTEGE101CardSnmpDyingGaspEnabled, ethernetNTEGE101CardRestartAction,

        ethernetNTEGE206CardEntityIndex, ethernetNTEGE206CardAdminState,
        ethernetNTEGE206CardOperationalState, ethernetNTEGE206CardSecondaryState,
        ethernetNTEGE206CardVoltage, ethernetNTEGE206CardTemperature,
        ethernetNTEGE206CardSnmpDyingGaspEnabled, ethernetNTEGE206CardRestartAction,

        pseudoWireE3CardEntityIndex, pseudoWireE3CardAdminState,
        pseudoWireE3CardOperationalState, pseudoWireE3CardSecondaryState,
        pseudoWireE3CardIpAddress, pseudoWireE3CardIpNetmask,
        pseudoWireE3CardIpGateway, pseudoWireE3CardDhcpEnabled,
        pseudoWireE3CardMgmtVlanId, pseudoWireE3CardTimeOfDay,
        pseudoWireE3CardRestartAction,

        ethernetNTEGE201SyncECardEntityIndex, ethernetNTEGE201SyncECardAdminState,
        ethernetNTEGE201SyncECardOperationalState, ethernetNTEGE201SyncECardSecondaryState,
        ethernetNTEGE201SyncECardVoltage, ethernetNTEGE201SyncECardTemperature,
        ethernetNTEGE201SyncECardSnmpDyingGaspEnabled, ethernetNTEGE201SyncECardRestartAction,
    
        ethernetNTEGE201CardEntityIndex, ethernetNTEGE201CardAdminState,
        ethernetNTEGE201CardOperationalState, ethernetNTEGE201CardSecondaryState,
        ethernetNTEGE201CardVoltage, ethernetNTEGE201CardTemperature,
        ethernetNTEGE201CardSnmpDyingGaspEnabled, ethernetNTEGE201CardRestartAction,

        ethernetNTEGE206FCardEntityIndex, ethernetNTEGE206FCardAdminState,
        ethernetNTEGE206FCardOperationalState, ethernetNTEGE206FCardSecondaryState,
        ethernetNTEGE206FCardVoltage, ethernetNTEGE206FCardTemperature,
        ethernetNTEGE206FCardSnmpDyingGaspEnabled, ethernetNTEGE206FCardRestartAction,
        
        ethernet1x10GCardEntityIndex,ethernet1x10GCardAdminState,
        ethernet1x10GCardOperationalState,ethernet1x10GCardSecondaryState,
        ethernet1x10GCardTemperature,
        ethernet1x10GCardSnmpDyingGaspEnabled,ethernet1x10GCardRestartAction,
        ethernet1x10GCardStorageType,ethernet1x10GCardRowStatus, ethernet1x10GCardForceOffLineAction,
        
        ethernet10x1GCardEntityIndex,ethernet10x1GCardAdminState,
        ethernet10x1GCardOperationalState,ethernet10x1GCardSecondaryState,
        ethernet10x1GCardTemperature,
        ethernet10x1GCardSnmpDyingGaspEnabled,ethernet10x1GCardRestartAction,
        ethernet10x1GCardStorageType,ethernet10x1GCardRowStatus, ethernet10x1GCardForceOffLineAction,
        
        ethernetSWFCardEntityIndex,ethernetSWFCardAdminState,
        ethernetSWFCardOperationalState,ethernetSWFCardSecondaryState,
        ethernetSWFCardTemperature,
        ethernetSWFCardRestartAction,
        ethernetSWFCardStorageType,ethernetSWFCardRowStatus, ethernetSWFCardForceOffLineAction,
        
        stuCardEntityIndex,stuCardAdminState,
        stuCardOperationalState,stuCardSecondaryState,
        stuCardTemperature,
        stuCardRestartAction,
        stuCardStorageType,stuCardRowStatus, stuCardForceOffLineAction,
        
        amiEntityIndex, amiAdminState, amiOperationalState,
        amiSecondaryState, amiTemperature, amiRestartAction,
        
        stiEntityIndex, stiAdminState, stiOperationalState,
        stiSecondaryState, stiTemperature,stiStorageType,
        stiRowStatus,
        
        f3UsbHostIndex, f3UsbHostEntityIndex,
        f3UsbHostUnitName, f3UsbHostFormatVersion,
        f3UsbHostCLEICode, f3UsbHostPartNumber,
        f3UsbHostHwRev, f3UsbHostSwRev,
        f3UsbHostSerialNum, f3UsbHostMfgName,
        f3UsbHostMfgDate, f3UsbHostMfgSite,
        f3UsbHostSecondaryState, f3UsbHostPhysicalAddress,
        f3UsbHostMuxOperationalMode,

        ethernetNTEGE112CardEntityIndex, ethernetNTEGE112CardAdminState,
        ethernetNTEGE112CardOperationalState, ethernetNTEGE112CardSecondaryState,
        ethernetNTEGE112CardVoltage, ethernetNTEGE112CardTemperature,
        ethernetNTEGE112CardSnmpDyingGaspEnabled, ethernetNTEGE112CardRestartAction,
        ethernetNTEGE112CardFineGrainedPmInterval, ethernetNTEGE112CardSwitchPortActionPort, 
        ethernetNTEGE112CardSwitchPortAction,
        
        ethernetNTEGE114CardEntityIndex, ethernetNTEGE114CardAdminState,
        ethernetNTEGE114CardOperationalState, ethernetNTEGE114CardSecondaryState,
        ethernetNTEGE114CardVoltage, ethernetNTEGE114CardTemperature,
        ethernetNTEGE114CardSnmpDyingGaspEnabled, ethernetNTEGE114CardRestartAction,
        ethernetNTEGE114CardFineGrainedPmInterval, ethernetNTEGE114CardSwitchPortActionPort, 
        ethernetNTEGE114CardSwitchPortAction, 
        
        ethernetOverOCSTMCardEntityIndex, ethernetOverOCSTMCardAdminState,
        ethernetOverOCSTMCardOperationalState, ethernetOverOCSTMCardSecondaryState,
        ethernetOverOCSTMCardTemperature, ethernetOverOCSTMCardSnmpDyingGaspEnabled,
        ethernetOverOCSTMCardRestartAction, ethernetOverOCSTMCardStorageType,
        ethernetOverOCSTMCardRowStatus, ethernetOverOCSTMCardForceOffLineAction,
        ethernetOverOCSTMCardMode,
        
        ethernet1x10GHighPerCardEntityIndex, ethernet1x10GHighPerCardAdminState,
        ethernet1x10GHighPerCardOperationalState, ethernet1x10GHighPerCardSecondaryState,
        ethernet1x10GHighPerCardTemperature, ethernet1x10GHighPerCardSnmpDyingGaspEnabled,
        ethernet1x10GHighPerCardRestartAction, ethernet1x10GHighPerCardStorageType,
        ethernet1x10GHighPerCardRowStatus, ethernet1x10GHighPerCardForceOffLineAction,
        
        ethernet10x1GHighPerCardEntityIndex, ethernet10x1GHighPerCardAdminState,
        ethernet10x1GHighPerCardOperationalState, ethernet10x1GHighPerCardSecondaryState,
        ethernet10x1GHighPerCardTemperature, ethernet10x1GHighPerCardSnmpDyingGaspEnabled,
        ethernet10x1GHighPerCardRestartAction, ethernet10x1GHighPerCardStorageType,
        ethernet10x1GHighPerCardRowStatus, ethernet10x1GHighPerCardForceOffLineAction
    }
    STATUS  deprecated
    DESCRIPTION
            "*************** THIS OBJECT GROUP IS DEPRECATED ******************
             A collection of objects used to manage the CM Entity group."
    ::= { cmEntityGroups 1 }

commonEntityGroup OBJECT-GROUP
    OBJECTS {
        neIndex, neName, neType, neContact, neLocation, neDescription,
        neCmdPromptPrefix, neAccepted, neFromPort, neProvAction,
        neStorageType, neRowStatus, neAutoProvMode, neFineGrainedPmInterval,
        shelfIndex, shelfEntityIndex, shelfType, shelfbackplaneRev,
        shelfbackplaneDOM, shelfbackplaneSerialNo, shelfAction,
        shelfAdminState, shelfOperationalState, shelfSecondaryState,
        shelfMfgSite, shelfOscillatorType, shelfLedControl,
        slotIndex, slotEntityIndex, slotType, slotCardType, slotCardUnitName,
        slotCardFormatVersion, slotCardCLEICode, slotCardPartNumber,
        slotCardHwRev, slotCardSwRev, slotCardSerialNum,
        slotCardMfgName, slotCardMfgDate, slotCardMfgSite,
        slotSecondaryState, slotCardPhysicalAddress
    } 
    STATUS  current
    DESCRIPTION
            "A collection of common objects used to manage any F3 product 
             family product."
    ::= { cmEntityGroups 2 }

psuGroup OBJECT-GROUP
    OBJECTS {
        psuEntityIndex, psuType, psuAdminState, psuOperationalState,
        psuSecondaryState
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the F3 Power Supply 
             units."
    ::= { cmEntityGroups 3 }

fanGroup OBJECT-GROUP
    OBJECTS {
        fanEntityIndex, fanAdminState, fanOperationalState,
        fanSecondaryState, fanStorageType, fanRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the F3 Fan units." 
    ::= { cmEntityGroups 4 }

hubshelfGroup OBJECT-GROUP
    OBJECTS {
        scuEntityIndex, scuAdminState, scuOperationalState,
        scuSecondaryState, scuVoltage, scuTemperature,
        scuRestartAction, scuStorageType, scuRowStatus,
        scuFlashModelNum, scuFlashFirmwareRev, scuFlashSerialNum, 
        nemiEntityIndex, nemiAdminState, nemiOperationalState,
        nemiSecondaryState, nemiVoltage, nemiTemperature,
        nemiRestartAction, nemiStorageType, nemiRowStatus,
        nemiFlashModelNum, nemiFlashFirmwareRev, nemiFlashSerialNum,
        ethernetNTUCardEntityIndex, ethernetNTUCardAdminState,
        ethernetNTUCardOperationalState, ethernetNTUCardSecondaryState,
        ethernetNTUCardVoltage, ethernetNTUCardTemperature,
        ethernetNTUCardSnmpDyingGaspEnabled,ethernetNTUCardRestartAction,
        ethernetNTUCardStorageType, ethernetNTUCardRowStatus,
        ethernetCPMRCardEntityIndex, ethernetCPMRCardAdminState,
        ethernetCPMRCardOperationalState, ethernetCPMRCardSecondaryState,
        ethernetCPMRCardVoltage, ethernetCPMRCardTemperature, 
        ethernetCPMRCardRestartAction, ethernetCPMRCardPSU1State,
        ethernetCPMRCardPSU2State, ethernetCPMRCardFAN1State,
        ethernetCPMRCardFAN2State, ethernetCPMRCardPsuType,
        ethernetCPMRCardLLFMode, ethernetCPMRCardLLFModeAction,
        scuTEntityIndex, scuTAdminState, scuTOperationalState, 
        scuTSecondaryState, scuTVoltage, scuTTemperature,
        scuTRestartAction, scuTStorageType, scuTRowStatus,
        ethernetNTECardEntityIndex, ethernetNTECardAdminState,
        ethernetNTECardOperationalState, ethernetNTECardSecondaryState,
        ethernetNTECardVoltage, ethernetNTECardTemperature,
        ethernetNTECardSnmpDyingGaspEnabled, ethernetNTECardRestartAction,
        ethernetNTECardStorageType, ethernetNTECardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the CM Hub Shelf 
             entities."
    ::= { cmEntityGroups 5 }

nteGe206CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE206CardEntityIndex, ethernetNTEGE206CardAdminState,
        ethernetNTEGE206CardOperationalState, ethernetNTEGE206CardSecondaryState,
        ethernetNTEGE206CardVoltage, ethernetNTEGE206CardTemperature,
        ethernetNTEGE206CardSnmpDyingGaspEnabled, ethernetNTEGE206CardRestartAction,
        ethernetNTEGE206CardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE206 Card."
    ::= { cmEntityGroups 6 }

nteGe201SyncECardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE201SyncECardEntityIndex, ethernetNTEGE201SyncECardAdminState,
        ethernetNTEGE201SyncECardOperationalState, ethernetNTEGE201SyncECardSecondaryState,
        ethernetNTEGE201SyncECardVoltage, ethernetNTEGE201SyncECardTemperature,
        ethernetNTEGE201SyncECardSnmpDyingGaspEnabled, 
        ethernetNTEGE201SyncECardRestartAction,
        ethernetNTEGE201SyncECardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE201 Sync Ethernet Card." 
    ::= { cmEntityGroups 7 }

nteGe201NonSyncECardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE201CardEntityIndex, ethernetNTEGE201CardAdminState,
        ethernetNTEGE201CardOperationalState, ethernetNTEGE201CardSecondaryState,
        ethernetNTEGE201CardVoltage, ethernetNTEGE201CardTemperature,
        ethernetNTEGE201CardSnmpDyingGaspEnabled, ethernetNTEGE201CardRestartAction,
        ethernetNTEGE201CardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE201 Non SyncE Card."
    ::= { cmEntityGroups 8 }

nteGe206FCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE206FCardEntityIndex, ethernetNTEGE206FCardAdminState,
        ethernetNTEGE206FCardOperationalState, ethernetNTEGE206FCardSecondaryState,
        ethernetNTEGE206FCardVoltage, ethernetNTEGE206FCardTemperature,
        ethernetNTEGE206FCardSnmpDyingGaspEnabled, ethernetNTEGE206FCardRestartAction,
        ethernetNTEGE206FCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE206F Card."
    ::= { cmEntityGroups 9 }

nteGe112CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE112CardEntityIndex, ethernetNTEGE112CardAdminState,
        ethernetNTEGE112CardOperationalState, ethernetNTEGE112CardSecondaryState,
        ethernetNTEGE112CardVoltage, ethernetNTEGE112CardTemperature,
        ethernetNTEGE112CardSnmpDyingGaspEnabled, ethernetNTEGE112CardRestartAction,
        ethernetNTEGE112CardFineGrainedPmInterval, ethernetNTEGE112CardSwitchPortActionPort,
        ethernetNTEGE112CardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE112 Card."
    ::= { cmEntityGroups 10 }

nteGe114CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114CardEntityIndex, ethernetNTEGE114CardAdminState,
        ethernetNTEGE114CardOperationalState, ethernetNTEGE114CardSecondaryState,
        ethernetNTEGE114CardVoltage, ethernetNTEGE114CardTemperature,
        ethernetNTEGE114CardSnmpDyingGaspEnabled, ethernetNTEGE114CardRestartAction,
        ethernetNTEGE114CardFineGrainedPmInterval, ethernetNTEGE114CardSwitchPortActionPort,
        ethernetNTEGE114CardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114 Card."
    ::= { cmEntityGroups 11 }

nteGe206VCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE206VCardEntityIndex,
        ethernetNTEGE206VCardAdminState,
        ethernetNTEGE206VCardOperationalState,
        ethernetNTEGE206VCardSecondaryState,
        ethernetNTEGE206VCardVoltage,
        ethernetNTEGE206VCardTemperature,
        ethernetNTEGE206VCardSnmpDyingGaspEnabled,
        ethernetNTEGE206VCardRestartAction,
        ethernetNTEGE206VCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE206V Card."
    ::= { cmEntityGroups 12 }

nteXg210CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG210CardEntityIndex,
        ethernetNTEXG210CardAdminState,
        ethernetNTEXG210CardOperationalState,
        ethernetNTEXG210CardSecondaryState,
        ethernetNTEXG210CardVoltage,
        ethernetNTEXG210CardTemperature,
        ethernetNTEXG210CardSnmpDyingGaspEnabled,
        ethernetNTEXG210CardRestartAction,
        ethernetNTEXG210CardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG210 Card."
    ::= { cmEntityGroups 13 }

pseudoWireCardOcnStmCardGroup OBJECT-GROUP
    OBJECTS {
        pseudoWireOcnStmCardEntityIndex, pseudoWireOcnStmCardAdminState,
        pseudoWireOcnStmCardOperationalState, pseudoWireOcnStmCardSecondaryState,
        pseudoWireOcnStmCardIpAddress, pseudoWireOcnStmCardMode,
        pseudoWireOcnStmCardVoltage, pseudoWireOcnStmCardTemperature,
        pseudoWireOcnStmCardRestartAction, pseudoWireOcnStmCardStorageType,
        pseudoWireOcnStmCardRowStatus, pseudoWireOcnStmCardPSNEncapsulation,
        pseudoWireOcnStmCardFreqSourceType, pseudoWireOcnStmCardFreqSource              
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the Pseudo Wire OCN/STM Card."
    ::= { cmEntityGroups 14 }

pseudoWireCardE1T1CardGroup OBJECT-GROUP
    OBJECTS {
        pseudoWireE1T1CardEntityIndex, pseudoWireE1T1CardAdminState,
        pseudoWireE1T1CardOperationalState, pseudoWireE1T1CardSecondaryState,
        pseudoWireE1T1CardIpAddress, pseudoWireE1T1CardMode,
        pseudoWireE1T1CardVoltage, pseudoWireE1T1CardTemperature,
        pseudoWireE1T1CardRestartAction, pseudoWireE1T1CardStorageType,
        pseudoWireE1T1CardRowStatus, pseudoWireE1T1CardPSNEncapsulation
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the Pseudo Wire E1/T1 Card."
    ::= { cmEntityGroups 15 }

nteT1804CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTET1804CardEntityIndex, ethernetNTET1804CardAdminState,
        ethernetNTET1804CardOperationalState, ethernetNTET1804CardSecondaryState,
        ethernetNTET1804CardVoltage, ethernetNTET1804CardTemperature,
        ethernetNTET1804CardSnmpDyingGaspEnabled, ethernetNTET1804CardRestartAction,
        ethernetNTET1804CardFineGrainedPmInterval, ethernetNTET1804CardMode
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the T1804 Card."
    ::= { cmEntityGroups 16 }

nteT3204CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTET3204CardEntityIndex, ethernetNTET3204CardAdminState,
        ethernetNTET3204CardOperationalState, ethernetNTET3204CardSecondaryState,
        ethernetNTET3204CardVoltage, ethernetNTET3204CardTemperature,
        ethernetNTET3204CardSnmpDyingGaspEnabled, ethernetNTET3204CardRestartAction,
        ethernetNTET3204CardFineGrainedPmInterval, ethernetNTET3204CardMode,
        ethernetGE4ECCCardTemperature,
        ethernetGE4ECCCardRestartAction,
        ethernetGE4ECCCardStorageType,
        ethernetGE4ECCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the T3204 Card."
    ::= { cmEntityGroups 17 }


nteGeSyncProbeCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGESyncProbeCardEntityIndex, ethernetNTEGESyncProbeCardAdminState,
        ethernetNTEGESyncProbeCardOperationalState, ethernetNTEGESyncProbeCardSecondaryState,
        ethernetNTEGESyncProbeCardVoltage, ethernetNTEGESyncProbeCardTemperature,
        ethernetNTEGESyncProbeCardSnmpDyingGaspEnabled, ethernetNTEGESyncProbeCardRestartAction,
        ethernetNTEGESyncProbeCardFineGrainedPmInterval,
        ethernetGE4SCCCardTemperature,
        ethernetGE4SCCCardRestartAction,
        ethernetGE4SCCCardStorageType,
        ethernetGE4SCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE SyncProbe Card."
    ::= { cmEntityGroups 18 }

xg1XCCCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetXG1XCCCardEntityIndex,
        ethernetXG1XCCCardAdminState,
        ethernetXG1XCCCardOperationalState,
        ethernetXG1XCCCardSecondaryState,
        ethernetXG1XCCCardVoltage,
        ethernetXG1XCCCardTemperature,
        ethernetXG1XCCCardRestartAction,
        ethernetXG1XCCCardStorageType,
        ethernetXG1XCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG 1X CC Card."
    ::= { cmEntityGroups 19 }

xg1SCCCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetXG1SCCCardEntityIndex,
        ethernetXG1SCCCardAdminState,
        ethernetXG1SCCCardOperationalState,
        ethernetXG1SCCCardSecondaryState,
        ethernetXG1SCCCardVoltage,
        ethernetXG1SCCCardTemperature,
        ethernetXG1SCCCardRestartAction,
        ethernetXG1SCCCardStorageType,
        ethernetXG1SCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG 1S CC Card."
    ::= { cmEntityGroups 20 }
    
ge4ECCCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetGE4ECCCardEntityIndex,
        ethernetGE4ECCCardAdminState,
        ethernetGE4ECCCardOperationalState,
        ethernetGE4ECCCardSecondaryState,
        ethernetGE4ECCCardVoltage,
        ethernetGE4ECCCardTemperature,
        ethernetGE4ECCCardRestartAction,
        ethernetGE4ECCCardStorageType,
        ethernetGE4ECCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE 4E CC Card."
    ::= { cmEntityGroups 21 }

ge4SCCCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetGE4SCCCardEntityIndex,
        ethernetGE4SCCCardAdminState,
        ethernetGE4SCCCardOperationalState,
        ethernetGE4SCCCardSecondaryState,
        ethernetGE4SCCCardVoltage,
        ethernetGE4SCCCardTemperature,
        ethernetGE4SCCCardRestartAction,
        ethernetGE4SCCCardStorageType,
        ethernetGE4SCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE 4S CC Card."
    ::= { cmEntityGroups 22 }


ge8SCCCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetGE8SCCCardEntityIndex,
        ethernetGE8SCCCardAdminState,
        ethernetGE8SCCCardOperationalState,
        ethernetGE8SCCCardSecondaryState,
        ethernetGE8SCCCardVoltage,
        ethernetGE8SCCCardTemperature,
        ethernetGE8SCCCardRestartAction,
        ethernetGE8SCCCardStorageType,
        ethernetGE8SCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE 8S CC Card."
    ::= { cmEntityGroups 23 }

nteGe114HCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114HCardEntityIndex, ethernetNTEGE114HCardAdminState,
        ethernetNTEGE114HCardOperationalState, ethernetNTEGE114HCardSecondaryState,
        ethernetNTEGE114HCardVoltage, ethernetNTEGE114HCardTemperature,
        ethernetNTEGE114HCardSnmpDyingGaspEnabled, ethernetNTEGE114HCardRestartAction,
        ethernetNTEGE114HCardFineGrainedPmInterval, ethernetNTEGE114HCardSwitchPortActionPort,
        ethernetNTEGE114HCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114H Card."
    ::= { cmEntityGroups 24 }
    
nteGe114PHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114PHCardEntityIndex, ethernetNTEGE114PHCardAdminState,
        ethernetNTEGE114PHCardOperationalState, ethernetNTEGE114PHCardSecondaryState,
        ethernetNTEGE114PHCardVoltage, ethernetNTEGE114PHCardTemperature,
        ethernetNTEGE114PHCardSnmpDyingGaspEnabled, ethernetNTEGE114PHCardRestartAction,
        ethernetNTEGE114PHCardFineGrainedPmInterval, ethernetNTEGE114PHCardSwitchPortActionPort,
        ethernetNTEGE114PHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114PH Card."
    ::= { cmEntityGroups 25 }

ethernetOverOCSTMCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetOverOCSTMCardEntityIndex, ethernetOverOCSTMCardAdminState,
        ethernetOverOCSTMCardOperationalState, ethernetOverOCSTMCardSecondaryState,
        ethernetOverOCSTMCardTemperature, ethernetOverOCSTMCardSnmpDyingGaspEnabled,
        ethernetOverOCSTMCardRestartAction, ethernetOverOCSTMCardStorageType,
        ethernetOverOCSTMCardRowStatus, ethernetOverOCSTMCardForceOffLineAction,
        ethernetOverOCSTMCardMode
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the cards."
    ::= { cmEntityGroups 26 }

ethernet1x10GHighPerCardGroup OBJECT-GROUP
    OBJECTS {
        ethernet1x10GHighPerCardEntityIndex, ethernet1x10GHighPerCardAdminState,
        ethernet1x10GHighPerCardOperationalState, ethernet1x10GHighPerCardSecondaryState,
        ethernet1x10GHighPerCardTemperature, ethernet1x10GHighPerCardSnmpDyingGaspEnabled,
        ethernet1x10GHighPerCardRestartAction, ethernet1x10GHighPerCardStorageType,
        ethernet1x10GHighPerCardRowStatus, ethernet1x10GHighPerCardForceOffLineAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the cards."
    ::= { cmEntityGroups 27 }

ethernetFE36ECardGroup OBJECT-GROUP
    OBJECTS {
        ethernetFE36ECardEntityIndex, ethernetFE36ECardAdminState,
        ethernetFE36ECardOperationalState, ethernetFE36ECardSecondaryState,
        ethernetFE36ECardTemperature, ethernetFE36ECardRestartAction,
        ethernetFE36ECardStorageType, ethernetFE36ECardRowStatus,
        ethernetFE36ECardForceOffLineAction, ethernetFE36ECard8023azEnabled
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the cards."
    ::= { cmEntityGroups 28 }

nteGe114SHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114SHCardEntityIndex, ethernetNTEGE114SHCardAdminState,
        ethernetNTEGE114SHCardOperationalState, ethernetNTEGE114SHCardSecondaryState,
        ethernetNTEGE114SHCardVoltage, ethernetNTEGE114SHCardTemperature,
        ethernetNTEGE114SHCardSnmpDyingGaspEnabled, ethernetNTEGE114SHCardRestartAction,
        ethernetNTEGE114SHCardFineGrainedPmInterval, ethernetNTEGE114SHCardSwitchPortActionPort,
        ethernetNTEGE114SHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114SH Card."
    ::= { cmEntityGroups 29 }
    
nteGe114SCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114SCardEntityIndex, ethernetNTEGE114SCardAdminState,
        ethernetNTEGE114SCardOperationalState, ethernetNTEGE114SCardSecondaryState,
        ethernetNTEGE114SCardVoltage, ethernetNTEGE114SCardTemperature,
        ethernetNTEGE114SCardSnmpDyingGaspEnabled, ethernetNTEGE114SCardRestartAction,
        ethernetNTEGE114SCardFineGrainedPmInterval, ethernetNTEGE114SCardSwitchPortActionPort,
        ethernetNTEGE114SCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114S Card."
    ::= { cmEntityGroups 30 }

ge8ECCCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetGE8ECCCardEntityIndex,
        ethernetGE8ECCCardAdminState,
        ethernetGE8ECCCardOperationalState,
        ethernetGE8ECCCardSecondaryState,
        ethernetGE8ECCCardVoltage,
        ethernetGE8ECCCardTemperature,
        ethernetGE8ECCCardRestartAction,
        ethernetGE8ECCCardStorageType,
        ethernetGE8ECCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE 8E CC Card."
    ::= { cmEntityGroups 31 }

neLLDPParamsGroup OBJECT-GROUP
    OBJECTS {
        neLLDPParamsLLDPEnableAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the LLDP params on NE."
    ::= { cmEntityGroups 32 }
    
nteSh1PcsCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTESH1PCSCardEntityIndex,
        ethernetNTESH1PCSCardAdminState,
        ethernetNTESH1PCSCardOperationalState,
        ethernetNTESH1PCSCardSecondaryState,
        ethernetNTESH1PCSCardVoltage,
        ethernetNTESH1PCSCardTemperature,
        ethernetNTESH1PCSCardSnmpDyingGaspEnabled,
        ethernetNTESH1PCSCardRestartAction,
        ethernetNTESH1PCSCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the SH1PCS Card."
    ::= { cmEntityGroups 33 }

nteOsa5411CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEOSA5411CardEntityIndex, ethernetNTEOSA5411CardAdminState,
        ethernetNTEOSA5411CardOperationalState, ethernetNTEOSA5411CardSecondaryState,
        ethernetNTEOSA5411CardVoltage, ethernetNTEOSA5411CardTemperature,
        ethernetNTEOSA5411CardSnmpDyingGaspEnabled, ethernetNTEOSA5411CardRestartAction,
        ethernetNTEOSA5411CardFineGrainedPmInterval,
        ethernetGE4SCCCardTemperature,
        ethernetGE4SCCCardRestartAction,
        ethernetGE4SCCCardStorageType,
        ethernetGE4SCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the OSA5411 Card."
    ::= { cmEntityGroups 34 }

nteGe112ProCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE112ProCardEntityIndex, ethernetNTEGE112ProCardAdminState,
        ethernetNTEGE112ProCardOperationalState, ethernetNTEGE112ProCardSecondaryState,
        ethernetNTEGE112ProCardVoltage, ethernetNTEGE112ProCardTemperature,
        ethernetNTEGE112ProCardSnmpDyingGaspEnabled, ethernetNTEGE112ProCardRestartAction,
        ethernetNTEGE112ProCardFineGrainedPmInterval, ethernetNTEGE112ProCardSwitchPortActionPort,
        ethernetNTEGE112ProCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE112Pro Card."
    ::= { cmEntityGroups 35 }

nteGe112ProMCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE112ProMCardEntityIndex, ethernetNTEGE112ProMCardAdminState,
        ethernetNTEGE112ProMCardOperationalState, ethernetNTEGE112ProMCardSecondaryState,
        ethernetNTEGE112ProMCardVoltage, ethernetNTEGE112ProMCardTemperature,
        ethernetNTEGE112ProMCardSnmpDyingGaspEnabled, ethernetNTEGE112ProMCardRestartAction,
        ethernetNTEGE112ProMCardFineGrainedPmInterval, ethernetNTEGE112ProMCardSwitchPortActionPort,
        ethernetNTEGE112ProMCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE112ProM Card."
    ::= { cmEntityGroups 36 }

nteGe114ProCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProCardEntityIndex, ethernetNTEGE114ProCardAdminState,
        ethernetNTEGE114ProCardOperationalState, ethernetNTEGE114ProCardSecondaryState,
        ethernetNTEGE114ProCardVoltage, ethernetNTEGE114ProCardTemperature,
        ethernetNTEGE114ProCardSnmpDyingGaspEnabled, ethernetNTEGE114ProCardRestartAction,
        ethernetNTEGE114ProCardFineGrainedPmInterval, ethernetNTEGE114ProCardSwitchPortActionPort,
        ethernetNTEGE114ProCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114Pro Card."
    ::= { cmEntityGroups 37 }

nteGe114ProCCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProCCardEntityIndex, ethernetNTEGE114ProCCardAdminState,
        ethernetNTEGE114ProCCardOperationalState, ethernetNTEGE114ProCCardSecondaryState,
        ethernetNTEGE114ProCCardVoltage, ethernetNTEGE114ProCCardTemperature,
        ethernetNTEGE114ProCCardSnmpDyingGaspEnabled, ethernetNTEGE114ProCCardRestartAction,
        ethernetNTEGE114ProCCardFineGrainedPmInterval, ethernetNTEGE114ProCCardSwitchPortActionPort,
        ethernetNTEGE114ProCCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProC Card."
    ::= { cmEntityGroups 38 }

nteGe114ProSHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProSHCardEntityIndex, ethernetNTEGE114ProSHCardAdminState,
        ethernetNTEGE114ProSHCardOperationalState, ethernetNTEGE114ProSHCardSecondaryState,
        ethernetNTEGE114ProSHCardVoltage, ethernetNTEGE114ProSHCardTemperature,
        ethernetNTEGE114ProSHCardSnmpDyingGaspEnabled, ethernetNTEGE114ProSHCardRestartAction,
        ethernetNTEGE114ProSHCardFineGrainedPmInterval, ethernetNTEGE114ProSHCardSwitchPortActionPort,
        ethernetNTEGE114ProSHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProSH Card."
    ::= { cmEntityGroups 39 }

nteGe114ProCSHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProCSHCardEntityIndex, ethernetNTEGE114ProCSHCardAdminState,
        ethernetNTEGE114ProCSHCardOperationalState, ethernetNTEGE114ProCSHCardSecondaryState,
        ethernetNTEGE114ProCSHCardVoltage, ethernetNTEGE114ProCSHCardTemperature,
        ethernetNTEGE114ProCSHCardSnmpDyingGaspEnabled, ethernetNTEGE114ProCSHCardRestartAction,
        ethernetNTEGE114ProCSHCardFineGrainedPmInterval, ethernetNTEGE114ProCSHCardSwitchPortActionPort,
        ethernetNTEGE114ProCSHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProCSH Card."
    ::= { cmEntityGroups 40 }

nteGe114ProHECardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProHECardEntityIndex, ethernetNTEGE114ProHECardAdminState,
        ethernetNTEGE114ProHECardOperationalState, ethernetNTEGE114ProHECardSecondaryState,
        ethernetNTEGE114ProHECardVoltage, ethernetNTEGE114ProHECardTemperature,
        ethernetNTEGE114ProHECardSnmpDyingGaspEnabled, ethernetNTEGE114ProHECardRestartAction,
        ethernetNTEGE114ProHECardFineGrainedPmInterval, ethernetNTEGE114ProHECardSwitchPortActionPort,
        ethernetNTEGE114ProHECardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProHE Card."
    ::= { cmEntityGroups 41 }

nteGe112ProHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE112ProHCardEntityIndex, ethernetNTEGE112ProHCardAdminState,
        ethernetNTEGE112ProHCardOperationalState, ethernetNTEGE112ProHCardSecondaryState,
        ethernetNTEGE112ProHCardVoltage, ethernetNTEGE112ProHCardTemperature,
        ethernetNTEGE112ProHCardSnmpDyingGaspEnabled, ethernetNTEGE112ProHCardRestartAction,
        ethernetNTEGE112ProHCardFineGrainedPmInterval, ethernetNTEGE112ProHCardSwitchPortActionPort,
        ethernetNTEGE112ProHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE112ProH Card."
    ::= { cmEntityGroups 42 }


nteXg210CCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG210CCardEntityIndex,
        ethernetNTEXG210CCardAdminState,
        ethernetNTEXG210CCardOperationalState,
        ethernetNTEXG210CCardSecondaryState,
        ethernetNTEXG210CCardVoltage,
        ethernetNTEXG210CCardTemperature,
        ethernetNTEXG210CCardSnmpDyingGaspEnabled,
        ethernetNTEXG210CCardRestartAction,
        ethernetNTEXG210CCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG210 Card."
    ::= { cmEntityGroups 43 }


geGE8SCryptoConnectorCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetGE8SCryptoConnectorCardEntityIndex,
        ethernetGE8SCryptoConnectorCardAdminState,
        ethernetGE8SCryptoConnectorCardOperationalState,
        ethernetGE8SCryptoConnectorCardSecondaryState,
        ethernetGE8SCryptoConnectorCardVoltage,
        ethernetGE8SCryptoConnectorCardTemperature,
        ethernetGE8SCryptoConnectorCardRestartAction,
        ethernetGE8SCryptoConnectorCardStorageType,
        ethernetGE8SCryptoConnectorCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE 8S CC Card."
    ::= { cmEntityGroups 44 }
    
nteOsa5420CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEOSA5420CardEntityIndex, ethernetNTEOSA5420CardAdminState,
        ethernetNTEOSA5420CardOperationalState, ethernetNTEOSA5420CardSecondaryState,
        ethernetNTEOSA5420CardVoltage, ethernetNTEOSA5420CardTemperature,
        ethernetNTEOSA5420CardSnmpDyingGaspEnabled, ethernetNTEOSA5420CardRestartAction,
        ethernetNTEOSA5420CardFineGrainedPmInterval,
        ethernetGE4SCCCardTemperature,
        ethernetGE4SCCCardRestartAction,
        ethernetGE4SCCCardStorageType,
        ethernetGE4SCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the OSA5420 Card."
    ::= { cmEntityGroups 45 }

nteOsa5421CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEOSA5421CardEntityIndex, ethernetNTEOSA5421CardAdminState,
        ethernetNTEOSA5421CardOperationalState, ethernetNTEOSA5421CardSecondaryState,
        ethernetNTEOSA5421CardVoltage, ethernetNTEOSA5421CardTemperature,
        ethernetNTEOSA5421CardSnmpDyingGaspEnabled, ethernetNTEOSA5421CardRestartAction,
        ethernetNTEOSA5421CardFineGrainedPmInterval,
        ethernetGE4SCCCardTemperature,
        ethernetGE4SCCCardRestartAction,
        ethernetGE4SCCCardStorageType,
        ethernetGE4SCCCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the OSA5421 Card."
    ::= { cmEntityGroups 46 }

nteGe114GCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114GCardEntityIndex, ethernetNTEGE114GCardAdminState,
        ethernetNTEGE114GCardOperationalState, ethernetNTEGE114GCardSecondaryState,
        ethernetNTEGE114GCardVoltage, ethernetNTEGE114GCardTemperature,
        ethernetNTEGE114GCardSnmpDyingGaspEnabled, ethernetNTEGE114GCardRestartAction,
        ethernetNTEGE114GCardFineGrainedPmInterval, ethernetNTEGE114GCardSwitchPortActionPort,
        ethernetNTEGE114GCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114G Card."
    ::= { cmEntityGroups 47 }

bits16PortCardGroup OBJECT-GROUP
    OBJECTS {
        bits16PortCardEntityIndex, 
        bits16PortCardAdminState,
        bits16PortCardOperationalState, 
        bits16PortCardSecondaryState,
        bits16PortCardRowStatus,    
        bits16PortCardAlias,
        bits16PortCardTemperature
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the BITS 16 PORT EXP Card."
    ::= { cmEntityGroups 48 }

nteGE114ProVmHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProVmHCardEntityIndex, ethernetNTEGE114ProVmHCardAdminState,
        ethernetNTEGE114ProVmHCardOperationalState, ethernetNTEGE114ProVmHCardSecondaryState,
        ethernetNTEGE114ProVmHCardVoltage, ethernetNTEGE114ProVmHCardTemperature,
        ethernetNTEGE114ProVmHCardSnmpDyingGaspEnabled, ethernetNTEGE114ProVmHCardRestartAction,
        ethernetNTEGE114ProVmHCardFineGrainedPmInterval, ethernetNTEGE114ProVmHCardSwitchPortActionPort,
        ethernetNTEGE114ProVmHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProVmH Card."
    ::= { cmEntityGroups 49 }

nteGE114ProVmCHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProVmCHCardEntityIndex, ethernetNTEGE114ProVmCHCardAdminState,
        ethernetNTEGE114ProVmCHCardOperationalState, ethernetNTEGE114ProVmCHCardSecondaryState,
        ethernetNTEGE114ProVmCHCardVoltage, ethernetNTEGE114ProVmCHCardTemperature,
        ethernetNTEGE114ProVmCHCardSnmpDyingGaspEnabled, ethernetNTEGE114ProVmCHCardRestartAction,
        ethernetNTEGE114ProVmCHCardFineGrainedPmInterval, ethernetNTEGE114ProVmCHCardSwitchPortActionPort,
        ethernetNTEGE114ProVmCHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProVmCH Card."
    ::= { cmEntityGroups 50 }

nteGE114ProVmCSHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProVmCSHCardEntityIndex, ethernetNTEGE114ProVmCSHCardAdminState,
        ethernetNTEGE114ProVmCSHCardOperationalState, ethernetNTEGE114ProVmCSHCardSecondaryState,
        ethernetNTEGE114ProVmCSHCardVoltage, ethernetNTEGE114ProVmCSHCardTemperature,
        ethernetNTEGE114ProVmCSHCardSnmpDyingGaspEnabled, ethernetNTEGE114ProVmCSHCardRestartAction,
        ethernetNTEGE114ProVmCSHCardFineGrainedPmInterval, ethernetNTEGE114ProVmCSHCardSwitchPortActionPort,
        ethernetNTEGE114ProVmCSHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProVmCSH Card."
    ::= { cmEntityGroups 51 }

serverCardGroup OBJECT-GROUP
    OBJECTS {
        serverCardEntityIndex, serverCardAdminState, serverCardOperationalState,
        serverCardSecondaryState, serverCardStorageType, serverCardVoltage,
        serverCardTemperature, serverCardUpTime, serverCardVmNumber,
        serverCardVirtualCpuTotal, serverCardVirtualCpuAvailiable,
        serverCardMemoryTotal, serverCardMemoryAvailiable,
        serverCardStorageTotal, serverCardStorageAvailiable, serverCardHvVersion,
        serverCardHostName, serverCardRestartAction, serverCardRowStatus, serverCardIgnoreWatchdog,
        f3StorageDeviceInternalSsdHealth, f3StorageDeviceExternalSsdStatus, f3StorageDeviceWearoutLevel
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the Server Card." 
    ::= { cmEntityGroups 52 }

pps16PortCardGroup OBJECT-GROUP
    OBJECTS {
        pps16PortCardEntityIndex, 
        pps16PortCardAdminState,
        pps16PortCardOperationalState, 
        pps16PortCardSecondaryState,
        pps16PortCardRowStatus,    
    pps16PortCardAlias
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the PPS 16 PORT EXP Card."
    ::= { cmEntityGroups 53 }


clk16PortCardGroup OBJECT-GROUP
    OBJECTS {
        clk16PortCardEntityIndex, 
        clk16PortCardAdminState,
        clk16PortCardOperationalState, 
        clk16PortCardSecondaryState,
        clk16PortCardRowStatus,    
        clk16PortCardAlias
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the PPS 16 PORT EXP Card."
    ::= { cmEntityGroups 54 }


todPps16PortCardGroup OBJECT-GROUP
    OBJECTS {
        todPps16PortCardEntityIndex, 
        todPps16PortCardAdminState,
        todPps16PortCardOperationalState, 
        todPps16PortCardSecondaryState,
        todPps16PortCardRowStatus,    
        todPps16PortCardAlias
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the PPS 16 PORT EXP Card."
    ::= { cmEntityGroups 55 }



nteGe101ProCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE101ProCardEntityIndex, ethernetNTEGE101ProCardAdminState,
        ethernetNTEGE101ProCardOperationalState, ethernetNTEGE101ProCardSecondaryState,
        ethernetNTEGE101ProCardVoltage, ethernetNTEGE101ProCardTemperature,
        ethernetNTEGE101ProCardSnmpDyingGaspEnabled, ethernetNTEGE101ProCardRestartAction,
        ethernetNTEGE101ProCardFineGrainedPmInterval, ethernetNTEGE101ProCardSwitchPortActionPort,
        ethernetNTEGE101ProCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE101Pro Card."
    ::= { cmEntityGroups 56 }

nteGo102ProSCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGO102ProSCardEntityIndex, ethernetNTEGO102ProSCardAdminState,
        ethernetNTEGO102ProSCardOperationalState, ethernetNTEGO102ProSCardSecondaryState,
        ethernetNTEGO102ProSCardVoltage, ethernetNTEGO102ProSCardTemperature,
        ethernetNTEGO102ProSCardSnmpDyingGaspEnabled, ethernetNTEGO102ProSCardRestartAction,
        ethernetNTEGO102ProSCardFineGrainedPmInterval, ethernetNTEGO102ProSCardSwitchPortActionPort,
        ethernetNTEGO102ProSCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GO102ProS Card."
    ::= { cmEntityGroups 57 }

nteGo102ProSPCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGO102ProSPCardEntityIndex, ethernetNTEGO102ProSPCardAdminState,
        ethernetNTEGO102ProSPCardOperationalState, ethernetNTEGO102ProSPCardSecondaryState,
        ethernetNTEGO102ProSPCardVoltage, ethernetNTEGO102ProSPCardTemperature,
        ethernetNTEGO102ProSPCardSnmpDyingGaspEnabled, ethernetNTEGO102ProSPCardRestartAction,
        ethernetNTEGO102ProSPCardFineGrainedPmInterval, ethernetNTEGO102ProSPCardSwitchPortActionPort,
        ethernetNTEGO102ProSPCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GO102ProSP Card."
    ::= { cmEntityGroups 58 }

nteCx101Pro30ACardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTECX101Pro30ACardEntityIndex, ethernetNTECX101Pro30ACardAdminState,
        ethernetNTECX101Pro30ACardOperationalState, ethernetNTECX101Pro30ACardSecondaryState,
        ethernetNTECX101Pro30ACardVoltage, ethernetNTECX101Pro30ACardTemperature,
        ethernetNTECX101Pro30ACardSnmpDyingGaspEnabled, ethernetNTECX101Pro30ACardRestartAction,
        ethernetNTECX101Pro30ACardFineGrainedPmInterval, ethernetNTECX101Pro30ACardSwitchPortActionPort,
        ethernetNTECX101Pro30ACardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the CX101Pro30A Card."
    ::= { cmEntityGroups 59 }

nteCx102Pro30ACardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTECX102Pro30ACardEntityIndex, ethernetNTECX102Pro30ACardAdminState,
        ethernetNTECX102Pro30ACardOperationalState, ethernetNTECX102Pro30ACardSecondaryState,
        ethernetNTECX102Pro30ACardVoltage, ethernetNTECX102Pro30ACardTemperature,
        ethernetNTECX102Pro30ACardSnmpDyingGaspEnabled, ethernetNTECX102Pro30ACardRestartAction,
        ethernetNTECX102Pro30ACardFineGrainedPmInterval, ethernetNTECX102Pro30ACardSwitchPortActionPort,
        ethernetNTECX102Pro30ACardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the CX102Pro30A Card."
    ::= { cmEntityGroups 60 }


ge4PortCardGroup OBJECT-GROUP
    OBJECTS {
        ge4PortCardEntityIndex, 
        ge4PortCardAdminState,
        ge4PortCardOperationalState, 
        ge4PortCardSecondaryState,
        ge4PortCardRowStatus,    
        ge4PortCardAlias,
        ge4PortCardTemperature
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE 4 PORT EXP Card."
    ::= { cmEntityGroups 61 }

nteXg116ProCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG116PROCardEntityIndex,
        ethernetNTEXG116PROCardAdminState,
        ethernetNTEXG116PROCardOperationalState,
        ethernetNTEXG116PROCardSecondaryState,
        ethernetNTEXG116PROCardVoltage,
        ethernetNTEXG116PROCardTemperature,
        ethernetNTEXG116PROCardSnmpDyingGaspEnabled,
        ethernetNTEXG116PROCardRestartAction,
        ethernetNTEXG116PROCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG116PRO Card."
    ::= { cmEntityGroups 62 }

nteXg120ProCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG120PROCardEntityIndex,
        ethernetNTEXG120PROCardAdminState,
        ethernetNTEXG120PROCardOperationalState,
        ethernetNTEXG120PROCardSecondaryState,
        ethernetNTEXG120PROCardVoltage,
        ethernetNTEXG120PROCardTemperature,
        ethernetNTEXG120PROCardSnmpDyingGaspEnabled,
        ethernetNTEXG120PROCardRestartAction,
        ethernetNTEXG120PROCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG120PRO Card."
    ::= { cmEntityGroups 63 }
    
nteGE112ProVmCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE112ProVmCardEntityIndex, ethernetNTEGE112ProVmCardAdminState,
        ethernetNTEGE112ProVmCardOperationalState, ethernetNTEGE112ProVmCardSecondaryState,
        ethernetNTEGE112ProVmCardVoltage, ethernetNTEGE112ProVmCardTemperature,
        ethernetNTEGE112ProVmCardSnmpDyingGaspEnabled, ethernetNTEGE112ProVmCardRestartAction,
        ethernetNTEGE112ProVmCardFineGrainedPmInterval, ethernetNTEGE112ProVmCardSwitchPortActionPort,
        ethernetNTEGE112ProVmCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE112ProVm Card."
    ::= { cmEntityGroups 64 }
        
nteOsa5401CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEOSA5401CardEntityIndex, ethernetNTEOSA5401CardAdminState,
        ethernetNTEOSA5401CardOperationalState, ethernetNTEOSA5401CardTableRestartAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the OSA5401 Card."
    ::= { cmEntityGroups 65 }

nteOsa5405CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEOSA5405CardEntityIndex, ethernetNTEOSA5405CardAdminState,
        ethernetNTEOSA5405CardOperationalState, ethernetNTEOSA5405CardVoltage,
        ethernetNTEOSA5405CardTemperature, ethernetNTEOSA5405CardTableRestartAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the OSA5405 Card."
    ::= { cmEntityGroups 66 }

csmCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetCSMCardEntityIndex, ethernetCSMCardAdminState,
        ethernetCSMCardOperationalState, ethernetCSMCardSecondaryState,
        ethernetCSMCardVoltage, ethernetCSMCardTemperature,
        ethernetCSMCardSnmpDyingGaspEnabled, ethernetCSMCardRestartAction,
        ethernetCSMCardFineGrainedPmInterval,ethernetCSMCardOscillatorType
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the CSM Card."
    ::= { cmEntityGroups 67 }

auxPortCardGroup OBJECT-GROUP
    OBJECTS {
        auxPortCardEntityIndex, 
        auxPortCardAdminState,
        auxPortCardOperationalState, 
        auxPortCardSecondaryState,
        auxPortCardRowStatus,    
        auxPortCardAlias,
        auxPortCardTemperature
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the Auxiliary Port Expansion Card."
    ::= { cmEntityGroups 68 }

nteGe102ProHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE102ProHCardEntityIndex, ethernetNTEGE102ProHCardAdminState,
        ethernetNTEGE102ProHCardOperationalState, ethernetNTEGE102ProHCardSecondaryState,
        ethernetNTEGE102ProHCardVoltage, ethernetNTEGE102ProHCardTemperature,
        ethernetNTEGE102ProHCardSnmpDyingGaspEnabled, ethernetNTEGE102ProHCardRestartAction,
        ethernetNTEGE102ProHCardFineGrainedPmInterval, ethernetNTEGE102ProHCardSwitchPortActionPort,
        ethernetNTEGE102ProHCardSwitchPortAction, ethernetNTEGE102ProHCardPSU1State,
        ethernetNTEGE102ProHCardPSU2State, ethernetNTEGE102ProHCardFAN1State,
        ethernetNTEGE102ProHCardFAN2State, ethernetNTEGE102ProHCardPsuType
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE102Pro H Card."
    ::= { cmEntityGroups 69 }

nteGe102ProEFMHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE102ProEFMHCardEntityIndex, ethernetNTEGE102ProEFMHCardAdminState,
        ethernetNTEGE102ProEFMHCardOperationalState, ethernetNTEGE102ProEFMHCardSecondaryState,
        ethernetNTEGE102ProEFMHCardVoltage, ethernetNTEGE102ProEFMHCardTemperature,
        ethernetNTEGE102ProEFMHCardRestartAction, ethernetNTEGE102ProEFMHCardPSU1State,
        ethernetNTEGE102ProEFMHCardPSU2State, ethernetNTEGE102ProEFMHCardFAN1State,
        ethernetNTEGE102ProEFMHCardFAN2State, ethernetNTEGE102ProEFMHCardPsuType,
        ethernetNTEGE102ProEFMHCardLLFMode, ethernetNTEGE102ProEFMHCardLLFModeAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE102Pro EFMH Card."
    ::= { cmEntityGroups 70 }

ethernetOsa3350MgntCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetOsa3350MgntCardEntityIndex,
        ethernetOsa3350MgntCardAdminState,
        ethernetOsa3350MgntCardOperationalState,
        ethernetOsa3350MgntCardSecondaryState,
        ethernetOsa3350MgntCardRestartAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the OSA3350 Mgnt Card."
    ::= { cmEntityGroups 71 }

nteXg116ProHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG116PROHCardEntityIndex,
        ethernetNTEXG116PROHCardAdminState,
        ethernetNTEXG116PROHCardOperationalState,
        ethernetNTEXG116PROHCardSecondaryState,
        ethernetNTEXG116PROHCardVoltage,
        ethernetNTEXG116PROHCardTemperature,
        ethernetNTEXG116PROHCardSnmpDyingGaspEnabled,
        ethernetNTEXG116PROHCardRestartAction,
        ethernetNTEXG116PROHCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG116PRO (H) Card."
    ::= { cmEntityGroups 72 }

nteGo102ProSMCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGO102ProSMCardEntityIndex, ethernetNTEGO102ProSMCardAdminState,
        ethernetNTEGO102ProSMCardOperationalState, ethernetNTEGO102ProSMCardSecondaryState,
        ethernetNTEGO102ProSMCardVoltage, ethernetNTEGO102ProSMCardTemperature,
        ethernetNTEGO102ProSMCardSnmpDyingGaspEnabled, ethernetNTEGO102ProSMCardRestartAction,
        ethernetNTEGO102ProSMCardFineGrainedPmInterval, ethernetNTEGO102ProSMCardSwitchPortActionPort,
        ethernetNTEGO102ProSMCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GO102ProSM Card."
    ::= { cmEntityGroups 73 }

nteXg118ProSHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG118PROSHCardEntityIndex,
        ethernetNTEXG118PROSHCardAdminState,
        ethernetNTEXG118PROSHCardOperationalState,
        ethernetNTEXG118PROSHCardSecondaryState,
        ethernetNTEXG118PROSHCardVoltage,
        ethernetNTEXG118PROSHCardTemperature,
        ethernetNTEXG118PROSHCardSnmpDyingGaspEnabled,
        ethernetNTEXG118PROSHCardRestartAction,
        ethernetNTEXG118PROSHCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG118PRO (SH) Card."
    ::= { cmEntityGroups 74 }

nteXg118ProacSHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG118PROACSHCardEntityIndex,
        ethernetNTEXG118PROACSHCardAdminState,
        ethernetNTEXG118PROACSHCardOperationalState,
        ethernetNTEXG118PROACSHCardSecondaryState,
        ethernetNTEXG118PROACSHCardVoltage,
        ethernetNTEXG118PROACSHCardTemperature,
        ethernetNTEXG118PROACSHCardSnmpDyingGaspEnabled,
        ethernetNTEXG118PROACSHCardRestartAction,
        ethernetNTEXG118PROACSHCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG118PROAC (SH) Card."
    ::= { cmEntityGroups 75 }

nteGE114ProVmSHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE114ProVmSHCardEntityIndex, ethernetNTEGE114ProVmSHCardAdminState,
        ethernetNTEGE114ProVmSHCardOperationalState, ethernetNTEGE114ProVmSHCardSecondaryState,
        ethernetNTEGE114ProVmSHCardVoltage, ethernetNTEGE114ProVmSHCardTemperature,
        ethernetNTEGE114ProVmSHCardSnmpDyingGaspEnabled, ethernetNTEGE114ProVmSHCardRestartAction,
        ethernetNTEGE114ProVmSHCardFineGrainedPmInterval, ethernetNTEGE114ProVmSHCardSwitchPortActionPort,
        ethernetNTEGE114ProVmSHCardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE114ProVmSH Card."
    ::= { cmEntityGroups 76 }

nteGE104CardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEGE104CardEntityIndex, ethernetNTEGE104CardAdminState,
        ethernetNTEGE104CardOperationalState, ethernetNTEGE104CardSecondaryState,
        ethernetNTEGE104CardVoltage, ethernetNTEGE104CardTemperature,
        ethernetNTEGE104CardSnmpDyingGaspEnabled, ethernetNTEGE104CardRestartAction,
        ethernetNTEGE104CardFineGrainedPmInterval, ethernetNTEGE104CardSwitchPortActionPort,
        ethernetNTEGE104CardSwitchPortAction
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the GE104 Card."
    ::= { cmEntityGroups 77 }

nteXg120ProSHCardGroup OBJECT-GROUP
    OBJECTS {
        ethernetNTEXG120PROSHCardEntityIndex,
        ethernetNTEXG120PROSHCardAdminState,
        ethernetNTEXG120PROSHCardOperationalState,
        ethernetNTEXG120PROSHCardSecondaryState,
        ethernetNTEXG120PROSHCardVoltage,
        ethernetNTEXG120PROSHCardTemperature,
        ethernetNTEXG120PROSHCardSnmpDyingGaspEnabled,
        ethernetNTEXG120PROSHCardRestartAction,
        ethernetNTEXG120PROSHCardFineGrainedPmInterval
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the XG120PROSH Card."
    ::= { cmEntityGroups 78 }

mbGnssCardGroup OBJECT-GROUP
    OBJECTS {
        mbGnssCardEntityIndex, 
        mbGnssCardAdminState,
        mbGnssCardOperationalState, 
        mbGnssCardSecondaryState,
        mbGnssCardRowStatus,    
        mbGnssCardAlias
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the MB GNSS EXP Card."
    ::= { cmEntityGroups 79 }

f3IrigCardGroup OBJECT-GROUP
    OBJECTS {
        f3IrigCardEntityIndex, 
        f3IrigCardAlias,
        f3IrigCardAdminState,
        f3IrigCardOperationalState, 
        f3IrigCardSecondaryState,
        f3IrigCardTemperature,
        f3IrigCardStorageType,
        f3IrigCardRowStatus
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the IRIG Card."
    ::= { cmEntityGroups 80 }

compositeClockCardGroup OBJECT-GROUP
    OBJECTS {
        compositeClockCardEntityIndex, 
        compositeClockCardAdminState,
        compositeClockCardOperationalState, 
        compositeClockCardSecondaryState,
        compositeClockCardRowStatus,    
        compositeClockCardAlias
    }
    STATUS  current
    DESCRIPTION
            "A collection of objects used to manage the COMPOSITE CLOCK EXP Card."
    ::= { cmEntityGroups 81 }

END
