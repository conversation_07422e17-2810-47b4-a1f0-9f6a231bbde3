ALCATEL-IND1-INTERSWITCH-PROTOCOL-MIB DEFINITIONS ::= BEGIN

     	IMPORTS
    		NOTIFICATION-TYPE,
		MODULE-IDENTITY,
		OBJECT-TYP<PERSON>,
		<PERSON><PERSON><PERSON><PERSON><PERSON>, Unsigned32
        		FROM SNMPv2-<PERSON><PERSON>
          	DisplayString, <PERSON><PERSON><PERSON><PERSON>,TruthValue
			FROM SNMPv2-TC
                softentIND1Aip, aipAMAPTraps, aipGMAPTraps
                        FROM ALCATEL-IND1-BASE
		MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
			FROM SNMPv2-CONF
		InterfaceIndex
			FROM IF-MIB

			;

        alcatelIND1InterswitchProtocolMIB MODULE-IDENTITY
                LAST-UPDATED "200704030000Z"
                ORGANIZATION "Alcatel-Lucent"
                CONTACT-INFO
            "Please consult with Customer Service to ensure the most appropriate
             version of this document is used with the products in question:

                        Alcatel-Lucent, Enterprise Solutions Division
                       (Formerly Alcatel Internetworking, Incorporated)
                               26801 West Agoura Road
                            Agoura Hills, CA  91301-5122
                              United States Of America

            Telephone:               North America  ****** 995 2696
                                     Latin America  ****** 919 9526
                                     Europe         +31 23 556 0100
                                     Asia           +65 394 7933
                                     All Other      +1 ************

            Electronic Mail:         <EMAIL>
            World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
            File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

                DESCRIPTION
                        "This module describes an authoritative enterprise-specific Simple
             Network Management Protocol (SNMP) Management Information Base (MIB):

                 For the Birds Of Prey Product Line
                 Health Monitoring for dissemination of resource consumption information.

             The right to make changes in specification and other information
             contained in this document without prior notice is reserved.

             No liability shall be assumed for any incidental, indirect, special, or
             consequential damages whatsoever arising from or related to this
             document or the information contained herein.

             Vendors, end-users, and other interested parties are granted
             non-exclusive license to use this specification in connection with
             management of the products for which it is intended to be used.

                         Copyright (C) 1995-2007 Alcatel-Lucent
                             ALL RIGHTS RESERVED WORLDWIDE"

                REVISION      "200704030000Z"
        DESCRIPTION
            "Addressing discrepancies with Alcatel Standard."
                ::= { softentIND1Aip 1 }

alcatelIND1InterswitchProtocolMIBObjects OBJECT IDENTIFIER ::= { alcatelIND1InterswitchProtocolMIB 1 }
aipGMAPconfig OBJECT IDENTIFIER ::= { alcatelIND1InterswitchProtocolMIBObjects 1 }
aipAMAPconfig OBJECT IDENTIFIER ::= { alcatelIND1InterswitchProtocolMIBObjects 2 }
aipLLDPConfig OBJECT IDENTIFIER ::= { alcatelIND1InterswitchProtocolMIBObjects 3 }


alcatelIND1InterswitchProtocolMIBConformance OBJECT IDENTIFIER ::= { alcatelIND1InterswitchProtocolMIB 2 }


-- GMAP Configuration *********************

aipGMAPstate OBJECT-TYPE
	SYNTAX  INTEGER {
		active   (1),
		inactive (2)
	}
	MAX-ACCESS  read-write
	STATUS  current
	DESCRIPTION "Current state of the GMAP functionality."
	::= { aipGMAPconfig 1 }

aipGMAPgaptime OBJECT-TYPE
	SYNTAX INTEGER (1..65535)
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION "The time in milliseconds for gaps between successive GMAP
		     protocol packets."
	::= { aipGMAPconfig 2 }

aipGMAPupdatetime OBJECT-TYPE
	SYNTAX INTEGER (1..65535)
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION "The time in seconds between GMAP update packet generation."
	::= { aipGMAPconfig 3 }

aipGMAPholdtime OBJECT-TYPE
	SYNTAX INTEGER (1..65535)
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION "The time in minutes that the switch will remember
		     information received in GMAP update packets."
	::= { aipGMAPconfig 4 }


-- GMAP Trap elements *********************

aipGMAPLastTrapReason OBJECT-TYPE
	SYNTAX  INTEGER {
		authenticatedVlan (1),
		conflictingBindingRule (2),
		sameProtoDifferentVlansConflict (3),
		sameVlanDifferentProtocolsConflict (4),
		nonMobileVlan (5),
		none (6)
	}
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "Reason for last GMAP update to not be applied.  Valid
                     reasons are:

		       1 - Target Vlan is an authenticated vlan
		       2 - Update would conflict with a binding rule
		       3 - Update would create two different vlan entries
		           for the same protocol
		       4 - Update would create two different protocol entries
		           for the same vlan
		       5 - Target vlan is not mobile
		       6 - No trap has been sent"

	::= { aipGMAPconfig 5 }

aipGMAPLastTrapPort OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The ifindex number of the last port on which the
		     GMAP change was not applied because of a conflict."
	::= { aipGMAPconfig 6 }

aipGMAPLastTrapMac OBJECT-TYPE
	SYNTAX  MacAddress
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The last MAC address for which a GMAP change was not
		     applied because of a conflict."
	::= { aipGMAPconfig 7 }

aipGMAPLastTrapProtocol OBJECT-TYPE
	SYNTAX INTEGER (1..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The protocol identifier of the last GMAP change that
		     was not applied because of a conflict."
	::= { aipGMAPconfig 8 }

aipGMAPLastTrapVlan OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The Vlan identifier of the last GMAP change that was
		     not applied because of a conflict."
	::= { aipGMAPconfig 9 }

-- GMAP Trap  *****************************

aipGMAPConflictTrap NOTIFICATION-TYPE
    OBJECTS    {
		  aipGMAPLastTrapReason,
		  aipGMAPLastTrapPort,
		  aipGMAPLastTrapMac,
		  aipGMAPLastTrapProtocol,
		  aipGMAPLastTrapVlan
               }
    STATUS  current
    DESCRIPTION
           "A GMAP update conflict."
    ::= { aipGMAPTraps 0 1 }


-- GMAP Table *****************************

aipGMAPTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF AipGMAPTableEntry
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION "GMAP MAC address list."
	::= { aipGMAPconfig 10 }

aipGMAPTableEntry OBJECT-TYPE
	SYNTAX  AipGMAPTableEntry
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION "GMAP MAC address list."
	INDEX { aipGMAPMacAddr, aipGMAPProtocol }
	::= { aipGMAPTable 1 }

AipGMAPTableEntry ::= SEQUENCE {
	aipGMAPMacAddr MacAddress,
	aipGMAPProtocol Unsigned32,
	aipGMAPVlan INTEGER,
	aipGMAPSrcSwitch MacAddress,
	aipGMAPFlags OCTET STRING (SIZE(4)),
	aipGMAPTimeout INTEGER
	}

aipGMAPMacAddr OBJECT-TYPE
	SYNTAX  MacAddress
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The MAC address of a GMAP entry."
	::= { aipGMAPTableEntry 1 }

aipGMAPProtocol OBJECT-TYPE
	SYNTAX Unsigned32
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The protocol identifier for a GMAP entry."
	::= { aipGMAPTableEntry 2 }

aipGMAPVlan OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The Vlan identifier for a GMAP entry."
	::= { aipGMAPTableEntry 3 }

aipGMAPSrcSwitch OBJECT-TYPE
	SYNTAX  MacAddress
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The MAC address of the switch that provided the
	             information for a GMAP entry."
	::= { aipGMAPTableEntry 4 }

aipGMAPFlags OBJECT-TYPE
	SYNTAX  OCTET STRING (SIZE(4))
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The flags for a GMAP entry.  The least significant byte
	             holds the router flags.  The next byte contains the flags
	             from auto-tracker.  The most significant 2 bytes are not
	             currently used."
	::= { aipGMAPTableEntry 5 }

aipGMAPTimeout OBJECT-TYPE
	SYNTAX INTEGER (1..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "Seconds remaining before this entry will age out."
	::= { aipGMAPTableEntry 6 }

-- GMAP Configuration end *********************



-- AMAP Configuration *********************

aipAMAPstate OBJECT-TYPE
	SYNTAX  INTEGER {
		active   (1),
		inactive (2)
	}
	MAX-ACCESS  read-write
	STATUS  current
	DESCRIPTION "Current state of the AMAP agent."
	DEFVAL	{ active }
	::= { aipAMAPconfig 1 }

aipAMAPdisctime OBJECT-TYPE
	SYNTAX INTEGER (1..65535)
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION "The time in seconds for interval betweeen hello
		     packets in the initial discovery phase
		     of the AMAP protocol."
	DEFVAL	{ 30 }
	::= { aipAMAPconfig 2 }

aipAMAPcommontime OBJECT-TYPE
	SYNTAX INTEGER (1..65535)
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION "The time in seconds for the interval between hello
		     packets at other than the discovery phase."
	DEFVAL	{ 300 }
	::= { aipAMAPconfig 3 }

-- AMAP Trap elements *********************

aipAMAPLastTrapReason OBJECT-TYPE
	SYNTAX  INTEGER {
		add (1),
		change (2),
		remove (3),
		none (4)
	}
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "Reason for last change of port status.  Valid
                     reasons are:
		       1 - port added
		       2 - change of information on existing port
		       3 - port deleted
		       4 - no trap has been sent"
	::= { aipAMAPconfig 4 }

aipAMAPLastTrapPort OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The ifindex number of the port that most recently changed."
	::= { aipAMAPconfig 5 }

-- AMAP Trap *********************

aipAMAPStatusTrap NOTIFICATION-TYPE
    OBJECTS    {
		  aipAMAPLastTrapReason,
		  aipAMAPLastTrapPort
               }
    STATUS   current
    DESCRIPTION
           "An AMAP port status change and reason."
    ::= { aipAMAPTraps 0 1 }


-- AMAP Adjacency Tables *********************

aipAMAPportConnectionTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF AipAMAPportConnectionEntry
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION "AMAP adjacent switch, local and remote connection index, and Vlan information."
	::= { aipAMAPconfig 6 }

aipAMAPportConnectionentry OBJECT-TYPE
	SYNTAX  AipAMAPportConnectionEntry
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION "AMAP adjacent switch, local and remote connection index, and Vlan information."
	INDEX { aipAMAPLocalConnectionIndex, aipAMAPRemMac,
		aipAMAPRemConnectionIndex }
	::= { aipAMAPportConnectionTable 1 }

AipAMAPportConnectionEntry ::= SEQUENCE {
	aipAMAPLocalConnectionIndex INTEGER,
	aipAMAPRemMac MacAddress,
	aipAMAPRemConnectionIndex INTEGER,
	aipAMAPRemVlan INTEGER,
	aipAMAPRemHostname DisplayString (SIZE(0..32)),
	aipAMAPLocalIfindex InterfaceIndex,
	aipAMAPLocalSlot INTEGER,
	aipAMAPLocalPort INTEGER,
	aipAMAPRemSlot INTEGER,
	aipAMAPRemPort INTEGER,
        aipAMAPRemDeviceType INTEGER,
	aipAMAPRemDevModelName DisplayString (SIZE(0..32)),
	aipAMAPRemProductType INTEGER
	}

aipAMAPLocalConnectionIndex OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The local index number of a connection in the local switch which has an
			adjacent switch connected. This index is assigned to a connection by
			the AMAP application running on the local switch."
	::= { aipAMAPportConnectionentry 1 }

aipAMAPRemMac OBJECT-TYPE
	SYNTAX  MacAddress
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The MAC Address of the CMM in the adjacent switch."
	::= { aipAMAPportConnectionentry 2 }

aipAMAPRemConnectionIndex OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The remote index number of the connection in the adjacent switch to which
			the local switch has a connection connected.  This index is assigned
			to a connection by the AMAP application running on the adjacent switch."
	::= { aipAMAPportConnectionentry 3 }

aipAMAPRemVlan OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The remote Vlan to which the connection is connected in the adjacent switch."
	::= { aipAMAPportConnectionentry 4 }

aipAMAPRemHostname OBJECT-TYPE
	SYNTAX  DisplayString (SIZE(0..32))
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The remote hostname for the adjacent switch."
	::= { aipAMAPportConnectionentry 5 }

aipAMAPLocalIfindex OBJECT-TYPE
	SYNTAX  InterfaceIndex
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The local ifindex of the port to which the connection is connected."
	::= { aipAMAPportConnectionentry 6 }

aipAMAPLocalSlot OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The local slot of the port to which the connection is connected."
	::= { aipAMAPportConnectionentry 7 }

aipAMAPLocalPort OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The local interface port of the port to which the connection is
			connected."
	::= { aipAMAPportConnectionentry 8 }

aipAMAPRemSlot OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The remote slot of the port to which the connection is connected."
	::= { aipAMAPportConnectionentry 9 }

aipAMAPRemPort OBJECT-TYPE
	SYNTAX INTEGER (0..65535)
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The remote interface port of the port to which the connection is
			connected."
	::= { aipAMAPportConnectionentry 10 }


aipAMAPRemDeviceType OBJECT-TYPE
         SYNTAX INTEGER {
	unknownDevice       (1),
	omniSwitch7700      (2),
	omniSwitch7800      (3),
	omniSwitch9600      (4),
	omniSwitch9700	    (5),
	omniSwitch9800	    (6),
	omniSwitch9600E     (7),
	omniSwitch9700E     (8),
	omniSwitch9800E     (9),
	omniSwitch8800      (10),
	omniSwitch6624      (20),
	omniSwitch6648      (21),
	omniSwitch6624Fiber (22),
	omniSwitch6624Ver2  (23),
	omniSwitch6648Ver2  (24),
	omniSwitch6824      (30),
	omniSwitch6848      (31),
	omniSwitch6824Fiber (32),
	omniSwitch6850-24   (34),
	omniSwitch6850-48   (35),
	omniSwitch6850-24Fiber (36),
	omniSwitch5slotXOS  (40),
	omniSwitch9slotXOS  (41),
	omniSwitchRouterXOS (42),
	omniAccess408       (43),
	omniAccess512       (44),
	omniStack2032       (50),
	omniStack4024       (51),
	omniStack5024       (52),
	omniStack6024       (53),
	omniStack6048       (54),
	omniStack6124       (55),
	omniStack6148       (56),
	omniStack8008       (57),
	omniAccess210       (60),
	omniAccess250       (61),
	omniAccess280       (62),
	ipPhone             (70),
	omniPCX4400         (71),
    omniSwitch6855-14   (72),
	omniSwitch6855-U10  (73),
	omniSwitch6855-24   (74),
	omniSwitch6855-U24  (75),
    omniSwitch6424      (76),
    omniSwitch6448      (77),
    omniSwitch6424Fiber (78),
	omniSwitch6855-U24X	(79),
        omniSwitch62508ME   (80),
        omniSwitch625024ME  (81),
        omniSwitch625024SMB (82),
	omniSwitch6200-MIXED-STACK    (214),
	omniSwitch6224     (215),
	omniSwitch6224P    (216),
	omniSwitch6248     (217),
	omniSwitch6248P    (218),
	omniSwitch6224-DC  (219),
	omniSwitch6248-DC  (220),
	omniSwitch6212	   (221),
	omniSwitch6212P	   (222),
	omniSwitch6224U	   (223),
	omniSwitch6324      (227),
	omniAccess5000      (249),
	omniAccess4324      (250),
	omniAccess4308      (251),
	omniAccess6000      (252),
	omniAccessAP60      (253),
	omniAccessAP61      (254),
	omniAccessAP70      (255)
                     }
                     MAX-ACCESS read-only
                     STATUS current
                     DESCRIPTION "The type of device seen on the port. "
               ::= { aipAMAPportConnectionentry 11 }

aipAMAPRemDevModelName OBJECT-TYPE
        SYNTAX  DisplayString (SIZE(0..32))
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "The remote device's model name (marketing name) for the adjacent switch."
        ::= { aipAMAPportConnectionentry 12 }

aipAMAPRemProductType OBJECT-TYPE
        SYNTAX INTEGER  {
		unknown		(0),
		chassis		(1),
		stack		(2),
		accessPoint	(3),
		pcx		(4),
		ipPhone		(5),
		standAlone      (6)
	}

        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION "Type of product connected to remote end. e.g. chassis, stack, wireless acess point etc"
        ::= { aipAMAPportConnectionentry 13 }

aipAMAPhostsTable OBJECT-TYPE
	SYNTAX  SEQUENCE OF AipAMAPHostEntry
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION "AMAP adjacent switch IP address information."
	::= { aipAMAPconfig 7 }

aipAMAPHostentry OBJECT-TYPE
	SYNTAX  AipAMAPHostEntry
	MAX-ACCESS  not-accessible
	STATUS current
	DESCRIPTION "AMAP adjacent switch IP address information."
	INDEX { aipAMAPHostMac,
		aipAMAPIpAddr }
	::= { aipAMAPhostsTable 1 }

AipAMAPHostEntry ::= SEQUENCE {
	aipAMAPHostMac  MacAddress,
	aipAMAPIpAddr   IpAddress
	}

aipAMAPHostMac OBJECT-TYPE
	SYNTAX  MacAddress
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION "The MAC Address of the CMM in the adjacent switch."
	::= { aipAMAPHostentry 1 }

aipAMAPIpAddr OBJECT-TYPE
	SYNTAX  IpAddress
	MAX-ACCESS  read-only
	STATUS  current
       	DESCRIPTION "An IP address of the adjacent switch."
	::= { aipAMAPHostentry 2 }

aipAMAPVoiceVlan OBJECT-TYPE
	SYNTAX INTEGER (0..4095)
	MAX-ACCESS read-write
	STATUS current
	DESCRIPTION	"The user configured VLAN value to be passed in AMAP pkt to neighbouring
			bridges as voice vlan used for IP phones. If the value is 0 (zero) then
			there is no voice vlan configured on the switch."
	DEFVAL	{ 1 }
	::= { aipAMAPconfig 8 }

-- AMAP configuration end *********************

aipLLDPConfigManAddrTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AipLLDPConfigManAddrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The table that controls selection of LLDP management address
            TLV instances to be transmitted on individual ports."
    ::= { aipLLDPConfig 1 }

aipLLDPConfigManAddrEntry  OBJECT-TYPE
    SYNTAX      AipLLDPConfigManAddrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "LLDP configuration information for a particular port
             on which the local system management address instance will be transmitted."

    INDEX { aipLLDPConfigManAddrPortNum }
    ::= { aipLLDPConfigManAddrTable 1 }

AipLLDPConfigManAddrEntry  ::= SEQUENCE {
    aipLLDPConfigManAddrPortNum   InterfaceIndex,
    aipLLDPConfigManAddrTlvTxEnable TruthValue
}

aipLLDPConfigManAddrPortNum   OBJECT-TYPE
    SYNTAX      InterfaceIndex 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The  port ifindex of the port associated with this entry."
    ::= { aipLLDPConfigManAddrEntry 1 }

aipLLDPConfigManAddrTlvTxEnable	OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
	    " This object controls, on a per port basis, whether or not 
	      management address TLV instances are transmitted. The value 
	      true(1) means that management address TLVs are transmitted ;
	      the value false(2) means that they are not.The default value 
	      for this object is false(2). "
            
    DEFVAL  { false }
    ::= { aipLLDPConfigManAddrEntry 2 }


-- AIP Conformance

alcatelIND1InterswitchProtocolMIBGroups OBJECT IDENTIFIER      ::=
{ alcatelIND1InterswitchProtocolMIBConformance 1 }

alcatelIND1InterswitchProtocolMIBCompliances OBJECT IDENTIFIER ::=
{ alcatelIND1InterswitchProtocolMIBConformance 2 }

aipGMAPConfGroup OBJECT-GROUP
    OBJECTS {
	aipGMAPstate,
	aipGMAPgaptime,
	aipGMAPupdatetime,
	aipGMAPholdtime,
	aipGMAPLastTrapReason,
	aipGMAPLastTrapPort,
	aipGMAPLastTrapMac,
	aipGMAPLastTrapProtocol,
	aipGMAPLastTrapVlan,
	aipGMAPMacAddr,
	aipGMAPProtocol,
	aipGMAPVlan,
	aipGMAPSrcSwitch,
	aipGMAPFlags,
	aipGMAPTimeout
    }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing information about GMAP."
    ::= { alcatelIND1InterswitchProtocolMIBGroups 1 }

aipAMAPConfGroup OBJECT-GROUP
    OBJECTS {
	aipAMAPstate,
	aipAMAPdisctime,
	aipAMAPcommontime,
	aipAMAPLastTrapReason,
	aipAMAPLastTrapPort,
	aipAMAPLocalConnectionIndex,
	aipAMAPRemMac,
	aipAMAPRemConnectionIndex,
	aipAMAPRemVlan,
	aipAMAPRemHostname,
	aipAMAPVoiceVlan,
	aipAMAPLocalIfindex,
	aipAMAPLocalSlot,
	aipAMAPLocalPort,
	aipAMAPRemSlot,
	aipAMAPRemPort,
	aipAMAPHostMac,
	aipAMAPIpAddr
    }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing information about AMAP."
    ::= { alcatelIND1InterswitchProtocolMIBGroups 2 }

aipNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS { aipAMAPStatusTrap,
                    aipGMAPConflictTrap}
    STATUS  current
    DESCRIPTION
            "A collection of notifications for signaling AIP events."
    ::= { alcatelIND1InterswitchProtocolMIBGroups 3 }



alcatelIND1InterswitchProtocolMIBCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for device support of AIP."


    MODULE
        MANDATORY-GROUPS {
            aipGMAPConfGroup,
            aipAMAPConfGroup,
	    aipNotificationGroup
        }

    ::= { alcatelIND1InterswitchProtocolMIBCompliances 1 }



END

