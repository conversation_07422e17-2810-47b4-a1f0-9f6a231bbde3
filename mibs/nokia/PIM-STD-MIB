PIM-STD-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, mib-2,
    NOTIFICATION-TYPE, Unsigned<PERSON>,
    <PERSON>32, <PERSON>64, <PERSON><PERSON><PERSON>32,
    TimeTicks                        FROM SNMPv2-SMI        -- [RFC2578]
    TEXTUAL-CONVENTION,
    RowStatus, TruthValue,
    StorageType                      FROM SNMPv2-TC         -- [RFC2579]
    MODULE-COMPLIANCE, OBJECT-G<PERSON><PERSON>,
    NOTIFICATION-GROUP               FROM SNMPv2-CONF       -- [RFC2580]
    InterfaceIndexOrZero,
    InterfaceIndex                   FROM IF-MIB            -- [RFC2863]
    InetAddressType,
    InetAddressPrefixLength,
    InetAddress, InetVersion         FROM INET-ADDRESS-MIB  -- [RFC4001]
    IANAipRouteProtocol              FROM IANA-RTPROTO-MIB; -- [R<PERSON><PERSON><PERSON>]

pimStdMIB MODULE-IDENTITY
    LAST-UPDATED "200711020000Z" -- 2 November 2007
    ORGANIZATION
            "IETF Protocol Independent Multicast (PIM) Working Group"
    CONTACT-INFO
            "Email: <EMAIL>
            WG charter:
            http://www.ietf.org/html.charters/pim-charter.html"
    DESCRIPTION
            "The MIB module for management of PIM routers.

            Copyright (C) The IETF Trust (2007).  This version of this
            MIB module is part of RFC 5060; see the RFC itself for full
            legal notices."
    REVISION     "200711020000Z" -- 2 November 2007
    DESCRIPTION  "Initial version, published as RFC 5060."
    ::= { mib-2 157 }

--
-- Textual Conventions
--

PimMode ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION
            "The PIM mode in which a group is operating.

            none(1)      The group is not using PIM, which may be the
                         case if, for example, it is a link-local or
                         unroutable group address.

            ssm(2)       Source-Specific Multicast (SSM) with PIM Sparse
                         Mode.

            asm(3)       Any Source Multicast (ASM) with PIM Sparse
                         Mode.

            bidir(4)     Bidirectional PIM.

            dm(5)        PIM Dense Mode.

            other(6)     Any other PIM mode."

    SYNTAX     INTEGER {
                  none(1),
                  ssm(2),
                  asm(3),
                  bidir(4),
                  dm(5),
                  other(6)
               }

PimGroupMappingOriginType ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION
            "The mechanism by which a PIM group mapping was learned.

            fixed(1)     Link-local or unroutable group mappings.

            configRp(2)  Local static RP configuration.

            configSsm(3) Local SSM Group configuration.

            bsr(4)       The PIM Bootstrap Router (BSR) mechanism.

            autoRP(5)    Cisco's Auto-RP mechanism.

            embedded(6)  The Embedded-RP mechanism where the RP address
                         is embedded in the multicast group address.

            other(7)     Any other mechanism."

    REFERENCE "RFC 3569, RFC 3956, and RFC 5059"
    SYNTAX     INTEGER {
                  fixed(1),
                  configRp(2),
                  configSsm(3),
                  bsr(4),
                  autoRP(5),
                  embedded(6),
                  other(7)
               }

--
-- Top-level structure
--

pimNotifications OBJECT IDENTIFIER ::= { pimStdMIB 0 }
pim              OBJECT IDENTIFIER ::= { pimStdMIB 1 }

pimKeepalivePeriod OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The duration of the Keepalive Timer.  This is the period
            during which the PIM router will maintain (S,G) state in the
            absence of explicit (S,G) local membership or (S,G) join
            messages received to maintain it.  This timer period is
            called the Keepalive_Period in the PIM-SM specification.  It
            is called the SourceLifetime in the PIM-DM specification.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    REFERENCE "RFC 4601 section 4.11"
    DEFVAL { 210 }
    ::= { pim 14 }

pimRegisterSuppressionTime OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The duration of the Register Suppression Timer.  This is
            the period during which a PIM Designated Router (DR) stops
            sending Register-encapsulated data to the Rendezvous Point
            (RP) after receiving a Register-Stop message.  This object
            is used to run timers both at the DR and at the RP.  This
            timer period is called the Register_Suppression_Time in the
            PIM-SM specification.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    REFERENCE "RFC 4601 section 4.11"
    DEFVAL { 60 }
    ::= { pim 15 }

pimStarGEntries OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of entries in the pimStarGTable."
    ::= { pim 16 }

pimStarGIEntries OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of entries in the pimStarGITable."
    ::= { pim 17 }

pimSGEntries OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of entries in the pimSGTable."
    ::= { pim 18 }

pimSGIEntries OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of entries in the pimSGITable."
    ::= { pim 19 }

pimSGRptEntries OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of entries in the pimSGRptTable."
    ::= { pim 20 }

pimSGRptIEntries OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of entries in the pimSGRptITable."
    ::= { pim 21 }

pimOutAsserts OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of Asserts sent by this router.

            Discontinuities in the value of this counter can occur at
            re-initialization of the management system, for example,
            when the device is rebooted."
    REFERENCE "RFC 4601 section 4.6"
    ::= { pim 22 }

pimInAsserts OBJECT-TYPE
    SYNTAX     Counter64
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of Asserts received by this router.  Asserts
            are multicast to all routers on a network.  This counter is
            incremented by all routers that receive an assert, not only
            those routers that are contesting the assert.

            Discontinuities in the value of this counter can occur at
            re-initialization of the management system, for example,
            when the device is rebooted."
    REFERENCE "RFC 4601 section 4.6"
    ::= { pim 23 }

pimLastAssertInterface OBJECT-TYPE
    SYNTAX     InterfaceIndexOrZero
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The interface on which this router most recently sent or
            received an assert, or zero if this router has not sent or
            received an assert."
    REFERENCE "RFC 4601 section 4.6"
    ::= { pim 24 }

pimLastAssertGroupAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type of the multicast group address in the most
            recently sent or received assert.  If this router has not
            sent or received an assert, then this object is set to
            unknown(0)."
    ::= { pim 25 }

pimLastAssertGroupAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The multicast group address in the most recently sent or
            received assert.  The InetAddressType is given by the
            pimLastAssertGroupAddressType object."
    ::= { pim 26 }

pimLastAssertSourceAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type of the source address in the most recently
            sent or received assert.  If the most recent assert was
            (*,G), or if this router has not sent or received an assert,
            then this object is set to unknown(0)."
    ::= { pim 27 }

pimLastAssertSourceAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The source address in the most recently sent or received
            assert.  The InetAddressType is given by the
            pimLastAssertSourceAddressType object."
    ::= { pim 28 }

pimNeighborLossNotificationPeriod OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The minimum time that must elapse between pimNeighborLoss
            notifications originated by this router.  The maximum value
            65535 represents an 'infinite' time, in which case, no
            pimNeighborLoss notifications are ever sent.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    DEFVAL { 0 }
    ::= { pim 29 }

pimNeighborLossCount OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of neighbor loss events that have occurred.

            This counter is incremented when the neighbor timer expires,
            and the router has no other neighbors on the same interface
            with the same IP version and a lower IP address than itself.

            This counter is incremented whenever a pimNeighborLoss
            notification would be generated.

            Discontinuities in the value of this counter can occur at
            re-initialization of the management system, for example,
            when the device is rebooted."
    REFERENCE "RFC 4601 section 4.3.2"
    ::= { pim 30 }

pimInvalidRegisterNotificationPeriod OBJECT-TYPE
    SYNTAX     Unsigned32 (10..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The minimum time that must elapse between
            pimInvalidRegister notifications originated by this router.
            The default value of 65535 represents an 'infinite' time, in
            which case, no pimInvalidRegister notifications are ever
            sent.

            The non-zero minimum allowed value provides resilience
            against propagation of denial-of-service attacks from the
            data and control planes to the network management plane.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    DEFVAL { 65535 }
    ::= { pim 31 }

pimInvalidRegisterMsgsRcvd OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of invalid PIM Register messages that have been
            received by this device.

            A PIM Register message is invalid if either

            o the destination address of the Register message does not
              match the Group to RP mapping on this device, or

            o this device believes the group address to be within an
              SSM address range, but this Register implies ASM usage.

            These conditions can occur transiently while RP mapping
            changes propagate through the network.  If this counter is
            incremented repeatedly over several minutes, then there is a
            persisting configuration error that requires correction.

            The active Group to RP mapping on this device is specified
            by the object pimGroupMappingPimMode.  If there is no such
            mapping, then the object pimGroupMappingPimMode is absent.
            The RP address contained in the invalid Register is
            pimInvalidRegisterRp.

            Multicast data carried by invalid Register messages is
            discarded.  The discarded data is from a source directly
            connected to pimInvalidRegisterOrigin, and is addressed to
            pimInvalidRegisterGroup.

            Discontinuities in the value of this counter can occur at
            re-initialization of the management system, for example,
            when the device is rebooted."
    REFERENCE "RFC 4601 section 4.4.2, RFC 3569, and
               'IP Multicast MIB' (August 2007) ipMcastSsmRangeTable"
    ::= { pim 32 }

pimInvalidRegisterAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type stored in pimInvalidRegisterOrigin,
            pimInvalidRegisterGroup, and pimInvalidRegisterRp.

            If no invalid Register messages have been received, then
            this object is set to unknown(0)."
    ::= { pim 33 }

pimInvalidRegisterOrigin OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The source address of the last invalid Register message
            received by this device."
    ::= { pim 34 }

pimInvalidRegisterGroup OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The IP multicast group address to which the last invalid
            Register message received by this device was addressed."
    ::= { pim 35 }

pimInvalidRegisterRp OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The RP address to which the last invalid Register message
            received by this device was delivered."
    ::= { pim 36 }

pimInvalidJoinPruneNotificationPeriod OBJECT-TYPE
    SYNTAX     Unsigned32 (10..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The minimum time that must elapse between
            pimInvalidJoinPrune notifications originated by this router.
            The default value of 65535 represents an 'infinite' time, in
            which case, no pimInvalidJoinPrune notifications are ever
            sent.

            The non-zero minimum allowed value provides resilience
            against propagation of denial-of-service attacks from the
            control plane to the network management plane.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    DEFVAL { 65535 }
    ::= { pim 37 }

pimInvalidJoinPruneMsgsRcvd OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of invalid PIM Join/Prune messages that have
            been received by this device.

            A PIM Join/Prune message is invalid if either

            o the Group to RP mapping specified by this message does not
              match the Group to RP mapping on this device, or

            o this device believes the group address to be within an
              SSM address range, but this Join/Prune (*,G) or (S,G,rpt)
              implies ASM usage.

            These conditions can occur transiently while RP mapping
            changes propagate through the network.  If this counter is
            incremented repeatedly over several minutes, then there is a
            persisting configuration error that requires correction.

            The active Group to RP mapping on this device is specified
            by the object pimGroupMappingPimMode.  If there is no such
            mapping, then the object pimGroupMappingPimMode is absent.
            The RP address contained in the invalid Join/Prune is
            pimInvalidJoinPruneRp.

            Invalid Join/Prune messages are discarded.  This may result
            in loss of multicast data affecting listeners downstream of
            pimInvalidJoinPruneOrigin, for multicast data addressed to
            pimInvalidJoinPruneGroup.

            Discontinuities in the value of this counter can occur at
            re-initialization of the management system, for example,
            when the device is rebooted."
    REFERENCE "RFC 4601 section 4.5.2, RFC 3569, and
               'IP Multicast MIB' (August 2007) ipMcastSsmRangeTable"
    ::= { pim 38 }

pimInvalidJoinPruneAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type stored in pimInvalidJoinPruneOrigin,
            pimInvalidJoinPruneGroup, and pimInvalidJoinPruneRp.

            If no invalid Join/Prune messages have been received, this
            object is set to unknown(0)."
    ::= { pim 39 }

pimInvalidJoinPruneOrigin OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The source address of the last invalid Join/Prune message
            received by this device."
    ::= { pim 40 }

pimInvalidJoinPruneGroup OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The IP multicast group address carried in the last
            invalid Join/Prune message received by this device."
    ::= { pim 41 }

pimInvalidJoinPruneRp OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The RP address carried in the last invalid Join/Prune
            message received by this device."
    ::= { pim 42 }

pimRPMappingNotificationPeriod OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The minimum time that must elapse between
            pimRPMappingChange notifications originated by this router.
            The default value of 65535 represents an 'infinite' time, in
            which case, no pimRPMappingChange notifications are ever
            sent.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    DEFVAL { 65535 }
    ::= { pim 43 }

pimRPMappingChangeCount OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of changes to active RP mappings on this device.

            Information about active RP mappings is available in
            pimGroupMappingTable.  Only changes to active mappings cause
            this counter to be incremented.  That is, changes that
            modify the pimGroupMappingEntry with the highest precedence
            for a group (lowest value of pimGroupMappingPrecedence).

            Such changes may result from manual configuration of this
            device, or from automatic RP mapping discovery methods
            including the PIM Bootstrap Router (BSR) mechanism.

            Discontinuities in the value of this counter can occur at
            re-initialization of the management system, for example,
            when the device is rebooted."
    REFERENCE "RFC 5059"
    ::= { pim 44 }

pimInterfaceElectionNotificationPeriod OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The minimum time that must elapse between
            pimInterfaceElection notifications originated by this
            router.  The default value of 65535 represents an 'infinite'
            time, in which case, no pimInterfaceElection notifications
            are ever sent.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    DEFVAL { 65535 }
    ::= { pim 45 }

pimInterfaceElectionWinCount OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The number of times this device has been elected DR or DF
            on any interface.

            Elections occur frequently on newly-active interfaces, as
            triggered Hellos establish adjacencies.  This counter is not
            incremented for elections on an interface until the first
            periodic Hello has been sent.  If this router is the DR or
            DF at the time of sending the first periodic Hello after
            interface activation, then this counter is incremented
            (once) at that time.

            Discontinuities in the value of this counter can occur at
            re-initialization of the management system, for example,
            when the device is rebooted."
    REFERENCE "RFC 4601 section 4.3.2 and RFC 5015 section 3.5.2"
    ::= { pim 46 }

pimRefreshInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "The interval between successive State Refresh messages sent
            by an Originator.  This timer period is called the
            RefreshInterval in the PIM-DM specification.  This object is
            used only by PIM-DM.

            The storage type of this object is determined by
            pimDeviceConfigStorageType."
    REFERENCE "RFC 3973 section 4.8"
    DEFVAL { 60 }
    ::= { pim 47 }

pimDeviceConfigStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The storage type used for the global PIM configuration of
            this device, comprised of the objects listed below.  If this
            storage type takes the value 'permanent', write-access to
            the listed objects need not be allowed.

            The objects described by this storage type are:
            pimKeepalivePeriod, pimRegisterSuppressionTime,
            pimNeighborLossNotificationPeriod,
            pimInvalidRegisterNotificationPeriod,
            pimInvalidJoinPruneNotificationPeriod,
            pimRPMappingNotificationPeriod,
            pimInterfaceElectionNotificationPeriod, and
            pimRefreshInterval."
    DEFVAL { nonVolatile }
    ::= { pim 48 }

--
-- The PIM Interface Table
--

pimInterfaceTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimInterfaceEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the router's PIM interfaces.
            PIM is enabled on all interfaces listed in this table."
    ::= { pim 1 }

pimInterfaceEntry OBJECT-TYPE
    SYNTAX     PimInterfaceEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimInterfaceTable.  This
            entry is preserved on agent restart."
    INDEX      { pimInterfaceIfIndex,
                 pimInterfaceIPVersion }
    ::= { pimInterfaceTable 1 }

PimInterfaceEntry ::= SEQUENCE {
    pimInterfaceIfIndex              InterfaceIndex,
    pimInterfaceIPVersion            InetVersion,
    pimInterfaceAddressType          InetAddressType,
    pimInterfaceAddress              InetAddress,
    pimInterfaceGenerationIDValue    Unsigned32,
    pimInterfaceDR                   InetAddress,
    pimInterfaceDRPriority           Unsigned32,
    pimInterfaceDRPriorityEnabled    TruthValue,
    pimInterfaceHelloInterval        Unsigned32,
    pimInterfaceTrigHelloInterval    Unsigned32,
    pimInterfaceHelloHoldtime        Unsigned32,
    pimInterfaceJoinPruneInterval    Unsigned32,
    pimInterfaceJoinPruneHoldtime    Unsigned32,
    pimInterfaceDFElectionRobustness Unsigned32,
    pimInterfaceLanDelayEnabled      TruthValue,
    pimInterfacePropagationDelay     Unsigned32,
    pimInterfaceOverrideInterval     Unsigned32,
    pimInterfaceEffectPropagDelay    Unsigned32,
    pimInterfaceEffectOverrideIvl    Unsigned32,
    pimInterfaceSuppressionEnabled   TruthValue,
    pimInterfaceBidirCapable         TruthValue,
    pimInterfaceDomainBorder         TruthValue,
    pimInterfaceStubInterface        TruthValue,
    pimInterfacePruneLimitInterval   Unsigned32,
    pimInterfaceGraftRetryInterval   Unsigned32,
    pimInterfaceSRPriorityEnabled    TruthValue,
    pimInterfaceStatus               RowStatus,
    pimInterfaceStorageType          StorageType
}

pimInterfaceIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The ifIndex value of this PIM interface."
    ::= { pimInterfaceEntry 1 }

pimInterfaceIPVersion OBJECT-TYPE
    SYNTAX     InetVersion
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The IP version of this PIM interface.  A physical interface
            may be configured in multiple modes concurrently, e.g., IPv4
            and IPv6; however, the traffic is considered to be logically
            separate."
    ::= { pimInterfaceEntry 2 }

pimInterfaceAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type of this PIM interface."
    ::= { pimInterfaceEntry 3 }

pimInterfaceAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The primary IP address of this router on this PIM
            interface.  The InetAddressType is given by the
            pimInterfaceAddressType object."
    REFERENCE "RFC 4601 sections 4.1.6, 4.3.1-4.3.4, and 4.5.1"
    ::= { pimInterfaceEntry 4 }

pimInterfaceGenerationIDValue OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of the Generation ID this router inserted in the
            last PIM Hello message it sent on this interface."
    REFERENCE "RFC 4601 section 4.3.1"
    ::= { pimInterfaceEntry 5 }

pimInterfaceDR OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The primary IP address of the Designated Router on this PIM
            interface.  The InetAddressType is given by the
            pimInterfaceAddressType object."
    REFERENCE "RFC 4601 section 4.3"
    ::= { pimInterfaceEntry 6 }

pimInterfaceDRPriority OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The Designated Router Priority value inserted into the DR
            Priority option in PIM Hello messages transmitted on this
            interface.  Numerically higher values for this object
            indicate higher priorities."
    REFERENCE "RFC 4601 section 4.3.2"
    DEFVAL { 1 }
    ::= { pimInterfaceEntry 7 }

pimInterfaceDRPriorityEnabled OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if all routers on this interface are
            using the DR Priority option."
    REFERENCE "RFC 4601 section 4.3.2"
    ::= { pimInterfaceEntry 8 }

pimInterfaceHelloInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..18000)
    UNITS      "seconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The frequency at which PIM Hello messages are transmitted
            on this interface.  This object corresponds to the
            'Hello_Period' timer value defined in the PIM-SM
            specification.  A value of zero represents an 'infinite'
            interval, and indicates that periodic PIM Hello messages
            should not be sent on this interface."
    REFERENCE "RFC 4601 section 9"
    DEFVAL { 30 }
    ::= { pimInterfaceEntry 9 }

pimInterfaceTrigHelloInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..60)
    UNITS      "seconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The maximum time before this router sends a triggered PIM
            Hello message on this interface.  This object corresponds to
            the 'Trigered_Hello_Delay' timer value defined in the PIM-SM
            specification.  A value of zero has no special meaning and
            indicates that triggered PIM Hello messages should always be
            sent immediately."
    REFERENCE "RFC 4601 section 4.11"
    DEFVAL { 5 }
    ::= { pimInterfaceEntry 10 }

pimInterfaceHelloHoldtime OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The value set in the Holdtime field of PIM Hello messages
            transmitted on this interface.  A value of 65535 represents
            an 'infinite' holdtime.  Implementations are recommended
            to use a holdtime that is 3.5 times the value of
            pimInterfaceHelloInterval, or 65535 if
            pimInterfaceHelloInterval is set to zero."
    REFERENCE "RFC 4601 sections 4.3.2 and 4.9.2"
    DEFVAL { 105 }
    ::= { pimInterfaceEntry 11 }

pimInterfaceJoinPruneInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..18000)
    UNITS      "seconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The frequency at which this router sends PIM Join/Prune
            messages on this PIM interface.  This object corresponds to
            the 't_periodic' timer value defined in the PIM-SM
            specification.  A value of zero represents an 'infinite'
            interval, and indicates that periodic PIM Join/Prune
            messages should not be sent on this interface."
    REFERENCE "RFC 4601 section 4.11"
    DEFVAL { 60 }
    ::= { pimInterfaceEntry 12 }

pimInterfaceJoinPruneHoldtime OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The value inserted into the Holdtime field of a PIM
            Join/Prune message sent on this interface.  A value of 65535
            represents an 'infinite' holdtime.  Implementations are
            recommended to use a holdtime that is 3.5 times the value of
            pimInterfaceJoinPruneInterval, or 65535 if
            pimInterfaceJoinPruneInterval is set to zero.  PIM-DM
            implementations are recommended to use the value of
            pimInterfacePruneLimitInterval."
    REFERENCE "RFC 4601 sections 4.5.3 and 4.9.5"
    DEFVAL { 210 }
    ::= { pimInterfaceEntry 13 }

pimInterfaceDFElectionRobustness OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The minimum number of PIM DF-Election messages that must be
            lost in order for DF election on this interface to fail."
    DEFVAL { 3 }
    ::= { pimInterfaceEntry 14 }

pimInterfaceLanDelayEnabled OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if all routers on this interface are
            using the LAN Prune Delay option."
    REFERENCE "RFC 4601 sections 4.3.3 and 4.9.2"
    ::= { pimInterfaceEntry 15 }

pimInterfacePropagationDelay OBJECT-TYPE
    SYNTAX     Unsigned32 (0..32767)
    UNITS      "milliseconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The expected propagation delay between PIM routers on this
            network or link.

            This router inserts this value into the Propagation_Delay
            field of the LAN Prune Delay option in the PIM Hello
            messages sent on this interface.  Implementations SHOULD
            enforce a lower bound on the permitted values for this
            object to allow for scheduling and processing delays within
            the local router."
    DEFVAL { 500 }
    ::= { pimInterfaceEntry 16 }

pimInterfaceOverrideInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "milliseconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The value this router inserts into the Override_Interval
            field of the LAN Prune Delay option in the PIM Hello
            messages it sends on this interface.

            When overriding a prune, PIM routers pick a random timer
            duration up to the value of this object.  The more PIM
            routers that are active on a network, the more likely it is
            that the prune will be overridden after a small proportion
            of this time has elapsed.

            The more PIM routers are active on this network, the larger
            this object should be to obtain an optimal spread of prune
            override latencies."
    REFERENCE "RFC 4601 section 4.3.3"
    DEFVAL { 2500 }
    ::= { pimInterfaceEntry 17 }

pimInterfaceEffectPropagDelay OBJECT-TYPE
    SYNTAX     Unsigned32 (0..32767)
    UNITS      "milliseconds"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The Effective Propagation Delay on this interface.  This
            object is always 500 if pimInterfaceLanDelayEnabled is
            FALSE."
    REFERENCE "RFC 4601 section 4.3.3"
    ::= { pimInterfaceEntry 18 }

pimInterfaceEffectOverrideIvl OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "milliseconds"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The Effective Override Interval on this interface.  This
            object is always 2500 if pimInterfaceLanDelayEnabled is
            FALSE."
    REFERENCE "RFC 4601 section 4.3.3"
    ::= { pimInterfaceEntry 19 }

pimInterfaceSuppressionEnabled OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether join suppression is enabled on this interface.
            This object is always TRUE if pimInterfaceLanDelayEnabled is
            FALSE."
    REFERENCE "RFC 4601 section 4.3.3"
    ::= { pimInterfaceEntry 20 }

pimInterfaceBidirCapable OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if all routers on this interface are
            using the Bidirectional-PIM Capable option."
    REFERENCE "RFC 5015 section 3.2 and 3.7.4"
    ::= { pimInterfaceEntry 21 }

pimInterfaceDomainBorder OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "Whether or not this interface is a PIM domain border.  This
            includes acting as a border for PIM Bootstrap Router (BSR)
            messages, if the BSR mechanism is in use."
    DEFVAL { false }
    ::= { pimInterfaceEntry 22 }

pimInterfaceStubInterface OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "Whether this interface is a 'stub interface'.  If this
            object is set to TRUE, then no PIM packets are sent out this
            interface, and any received PIM packets are ignored.

            Setting this object to TRUE is a security measure for
            interfaces towards untrusted hosts.  This allows an
            interface to be configured for use with IGMP (Internet Group
            Management Protocol) or MLD (Multicast Listener Discovery)
            only, which protects the PIM router from forged PIM messages
            on the interface.

            To communicate with other PIM routers using this interface,
            this object must remain set to FALSE.

            Changing the value of this object while the interface is
            operational causes PIM to be disabled and then re-enabled on
            this interface."
    REFERENCE "RFC 3376, RFC 3810"
    DEFVAL { false }
    ::= { pimInterfaceEntry 23 }

pimInterfacePruneLimitInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The minimum interval that must transpire between two
            successive Prunes sent by a router.  This object corresponds
            to the 't_limit' timer value defined in the PIM-DM
            specification.  This object is used only by PIM-DM."
    REFERENCE "RFC 3973 section 4.8"
    DEFVAL { 60 }
    ::= { pimInterfaceEntry 24 }

pimInterfaceGraftRetryInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The minimum interval that must transpire between two
            successive Grafts sent by a router.  This object corresponds
            to the 'Graft_Retry_Period' timer value defined in the
            PIM-DM specification.  This object is used only by PIM-DM."
    REFERENCE "RFC 3973 section 4.8"
    DEFVAL { 3 }
    ::= { pimInterfaceEntry 25 }

pimInterfaceSRPriorityEnabled OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if all routers on this interface are
            using the State Refresh option.  This object is used only by
            PIM-DM."
    ::= { pimInterfaceEntry 26 }

pimInterfaceStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The status of this entry.  Creating the entry enables PIM
            on the interface; destroying the entry disables PIM on the
            interface.

            This status object can be set to active(1) without setting
            any other columnar objects in this entry.

            All writeable objects in this entry can be modified when the
            status of this entry is active(1)."
    ::= { pimInterfaceEntry 27 }

pimInterfaceStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The storage type for this row.  Rows having the value
            'permanent' need not allow write-access to any columnar
            objects in the row."
    DEFVAL { nonVolatile }
    ::= { pimInterfaceEntry 28 }

--
-- The PIM Neighbor Table
--

pimNeighborTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimNeighborEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the router's PIM neighbors."
    ::= { pim 2 }

pimNeighborEntry OBJECT-TYPE
    SYNTAX     PimNeighborEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimNeighborTable."
    INDEX      { pimNeighborIfIndex,
                 pimNeighborAddressType,
                 pimNeighborAddress }
    ::= { pimNeighborTable 1 }

PimNeighborEntry ::= SEQUENCE {
    pimNeighborIfIndex              InterfaceIndex,
    pimNeighborAddressType          InetAddressType,
    pimNeighborAddress              InetAddress,
    pimNeighborGenerationIDPresent  TruthValue,
    pimNeighborGenerationIDValue    Unsigned32,
    pimNeighborUpTime               TimeTicks,
    pimNeighborExpiryTime           TimeTicks,
    pimNeighborDRPriorityPresent    TruthValue,
    pimNeighborDRPriority           Unsigned32,
    pimNeighborLanPruneDelayPresent TruthValue,
    pimNeighborTBit                 TruthValue,
    pimNeighborPropagationDelay     Unsigned32,
    pimNeighborOverrideInterval     Unsigned32,
    pimNeighborBidirCapable         TruthValue,
    pimNeighborSRCapable            TruthValue
}

pimNeighborIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The value of ifIndex for the interface used to reach this
            PIM neighbor."
    ::= { pimNeighborEntry 1 }

pimNeighborAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of this PIM neighbor."
    ::= { pimNeighborEntry 2 }

pimNeighborAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The primary IP address of this PIM neighbor.  The
            InetAddressType is given by the pimNeighborAddressType
            object."
    ::= { pimNeighborEntry 3 }

pimNeighborGenerationIDPresent OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if this neighbor is using the Generation
            ID option."
    REFERENCE "RFC 4601 section 4.3.1"
    ::= { pimNeighborEntry 4 }

pimNeighborGenerationIDValue OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of the Generation ID from the last PIM Hello
            message received from this neighbor.  This object is always
            zero if pimNeighborGenerationIDPresent is FALSE."
    REFERENCE "RFC 4601 section 4.3.1"
    ::= { pimNeighborEntry 5 }

pimNeighborUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since this PIM neighbor (last) became a neighbor
            of the local router."
    ::= { pimNeighborEntry 6 }

pimNeighborExpiryTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The minimum time remaining before this PIM neighbor will
            time out.  The value zero indicates that this PIM neighbor
            will never time out."
    ::= { pimNeighborEntry 7 }

pimNeighborDRPriorityPresent OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if this neighbor is using the DR Priority
            option."
    REFERENCE "RFC 4601 section 4.3.2"
    ::= { pimNeighborEntry 8 }

pimNeighborDRPriority OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of the Designated Router Priority from the last
            PIM Hello message received from this neighbor.  This object
            is always zero if pimNeighborDRPriorityPresent is FALSE."
    REFERENCE "RFC 4601 section 4.3.2"
    ::= { pimNeighborEntry 9 }

pimNeighborLanPruneDelayPresent OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if this neighbor is using the LAN Prune
            Delay option."
    REFERENCE "RFC 4601 section 4.3.3"
    ::= { pimNeighborEntry 10 }

pimNeighborTBit OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the T bit was set in the LAN Prune Delay option
            received from this neighbor.  The T bit specifies the
            ability of the neighbor to disable join suppression.  This
            object is always TRUE if pimNeighborLanPruneDelayPresent is
            FALSE."
    REFERENCE "RFC 4601 section 4.3.3"
    ::= { pimNeighborEntry 11 }

pimNeighborPropagationDelay OBJECT-TYPE
    SYNTAX     Unsigned32 (0..32767)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of the Propagation_Delay field of the LAN Prune
            Delay option received from this neighbor.  This object is
            always zero if pimNeighborLanPruneDelayPresent is FALSE."
    REFERENCE "RFC 4601 section 4.3.3"
    ::= { pimNeighborEntry 12 }

pimNeighborOverrideInterval OBJECT-TYPE
    SYNTAX     Unsigned32 (0..65535)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of the Override_Interval field of the LAN Prune
            Delay option received from this neighbor.  This object is
            always zero if pimNeighborLanPruneDelayPresent is FALSE."
    REFERENCE "RFC 4601 section 4.3.3"
    ::= { pimNeighborEntry 13 }

pimNeighborBidirCapable OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if this neighbor is using the
            Bidirectional-PIM Capable option."
    REFERENCE "RFC 5015 section 3.2 and 3.7.4"
    ::= { pimNeighborEntry 14 }

pimNeighborSRCapable OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Evaluates to TRUE if this neighbor is using the State
            Refresh Capable option.  This object is used only by
            PIM-DM."
    REFERENCE "RFC 3973 section 4.3.4"
    ::= { pimNeighborEntry 15 }

--
-- The PIM Neighbor Secondary Address Table
--

pimNbrSecAddressTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimNbrSecAddressEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the secondary addresses
            advertised by each PIM neighbor (on a subset of the rows of
            the pimNeighborTable defined above)."
    REFERENCE "RFC 4601 section 4.3.4"
    ::= { pim 3 }

pimNbrSecAddressEntry OBJECT-TYPE
    SYNTAX     PimNbrSecAddressEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimNbrSecAddressTable."
    INDEX      { pimNbrSecAddressIfIndex,
                 pimNbrSecAddressType,
                 pimNbrSecAddressPrimary,
                 pimNbrSecAddress }
    ::= { pimNbrSecAddressTable 1 }

PimNbrSecAddressEntry ::= SEQUENCE {
    pimNbrSecAddressIfIndex InterfaceIndex,
    pimNbrSecAddressType    InetAddressType,
    pimNbrSecAddressPrimary InetAddress,
    pimNbrSecAddress        InetAddress
}

pimNbrSecAddressIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The value of ifIndex for the interface used to reach this
            PIM neighbor."
    ::= { pimNbrSecAddressEntry 1 }

pimNbrSecAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of this PIM neighbor."
    ::= { pimNbrSecAddressEntry 2 }

pimNbrSecAddressPrimary OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The primary IP address of this PIM neighbor.  The
            InetAddressType is given by the pimNbrSecAddressType
            object."
    ::= { pimNbrSecAddressEntry 3 }

pimNbrSecAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The secondary IP address of this PIM neighbor.  The
            InetAddressType is given by the pimNbrSecAddressType
            object."
    ::= { pimNbrSecAddressEntry 4 }

--
-- The PIM (*,G) State Table
--

pimStarGTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimStarGEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the non-interface specific
            (*,G) state that PIM has."
    REFERENCE "RFC 4601 section 4.1.3"
    ::= { pim 4 }

pimStarGEntry OBJECT-TYPE
    SYNTAX     PimStarGEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimStarGTable."
    INDEX      { pimStarGAddressType,
                 pimStarGGrpAddress }
    ::= { pimStarGTable 1 }

PimStarGEntry ::= SEQUENCE {
    pimStarGAddressType             InetAddressType,
    pimStarGGrpAddress              InetAddress,
    pimStarGUpTime                  TimeTicks,
    pimStarGPimMode                 PimMode,
    pimStarGRPAddressType           InetAddressType,
    pimStarGRPAddress               InetAddress,
    pimStarGPimModeOrigin           PimGroupMappingOriginType,
    pimStarGRPIsLocal               TruthValue,
    pimStarGUpstreamJoinState       INTEGER,
    pimStarGUpstreamJoinTimer       TimeTicks,
    pimStarGUpstreamNeighborType    InetAddressType,
    pimStarGUpstreamNeighbor        InetAddress,
    pimStarGRPFIfIndex              InterfaceIndexOrZero,
    pimStarGRPFNextHopType          InetAddressType,
    pimStarGRPFNextHop              InetAddress,
    pimStarGRPFRouteProtocol        IANAipRouteProtocol,
    pimStarGRPFRouteAddress         InetAddress,
    pimStarGRPFRoutePrefixLength    InetAddressPrefixLength,
    pimStarGRPFRouteMetricPref      Unsigned32,
    pimStarGRPFRouteMetric          Unsigned32
}

pimStarGAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of this multicast group."
    ::= { pimStarGEntry 1 }

pimStarGGrpAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The multicast group address.  The InetAddressType is given
            by the pimStarGAddressType object."
    ::= { pimStarGEntry 2 }

pimStarGUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since this entry was created by the local router."
    ::= { pimStarGEntry 3 }

pimStarGPimMode OBJECT-TYPE
    SYNTAX     PimMode 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether this entry represents an ASM (Any Source Multicast,
            used with PIM-SM) or BIDIR-PIM group."
    ::= { pimStarGEntry 4 }

pimStarGRPAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type of the Rendezvous Point (RP), or
            unknown(0) if the RP address is unknown."
    ::= { pimStarGEntry 5 }

pimStarGRPAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address of the Rendezvous Point (RP) for the group.
            The InetAddressType is given by the pimStarGRPAddressType."
    ::= { pimStarGEntry 6 }

pimStarGPimModeOrigin OBJECT-TYPE
    SYNTAX     PimGroupMappingOriginType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The mechanism by which the PIM mode and RP for the group
            were learned."
    ::= { pimStarGEntry 7 }

pimStarGRPIsLocal OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router is the RP for the group."
    ::= { pimStarGEntry 8 }

pimStarGUpstreamJoinState OBJECT-TYPE
    SYNTAX     INTEGER {
                  notJoined (1),
                  joined (2)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router should join the RP tree for the
            group.  This corresponds to the state of the upstream (*,G)
            state machine in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.5.6"
    ::= { pimStarGEntry 9 }

pimStarGUpstreamJoinTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before the local router next sends a
            periodic (*,G) Join message on pimStarGRPFIfIndex.  This
            timer is called the (*,G) Upstream Join Timer in the PIM-SM
            specification.  This object is zero if the timer is not
            running."
    REFERENCE "RFC 4601 section 4.10"
    ::= { pimStarGEntry 10 }

pimStarGUpstreamNeighborType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The primary address type of the upstream neighbor, or
            unknown(0) if the upstream neighbor address is unknown or is
            not a PIM neighbor."
    ::= { pimStarGEntry 11 }

pimStarGUpstreamNeighbor OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The primary address of the neighbor on pimStarGRPFIfIndex
            that the local router is sending periodic (*,G) Join
            messages to.  The InetAddressType is given by the
            pimStarGUpstreamNeighborType object.  This address is called
            RPF'(*,G) in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.1.6"
    ::= { pimStarGEntry 12 }

pimStarGRPFIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndexOrZero
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of ifIndex for the Reverse Path Forwarding
            (RPF) interface towards the RP, or zero if the RPF
            interface is unknown."
    ::= { pimStarGEntry 13 }

pimStarGRPFNextHopType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type of the RPF next hop towards the RP, or
            unknown(0) if the RPF next hop is unknown."
    ::= { pimStarGEntry 14 }

pimStarGRPFNextHop OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address of the RPF next hop towards the RP.  The
            InetAddressType is given by the pimStarGRPFNextHopType
            object.  This address is called MRIB.next_hop(RP(G))
            in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.5.5"
    ::= { pimStarGEntry 15 }

pimStarGRPFRouteProtocol OBJECT-TYPE
    SYNTAX     IANAipRouteProtocol
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The routing mechanism via which the route used to find the
            RPF interface towards the RP was learned."
    ::= { pimStarGEntry 16 }

pimStarGRPFRouteAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The IP address that, when combined with the corresponding
            value of pimStarGRPFRoutePrefixLength, identifies the route
            used to find the RPF interface towards the RP.  The
            InetAddressType is given by the pimStarGRPFNextHopType
            object.

            This address object is only significant up to
            pimStarGRPFRoutePrefixLength bits.  The remainder of the
            address bits are zero."
    ::= { pimStarGEntry 17 }

pimStarGRPFRoutePrefixLength OBJECT-TYPE
    SYNTAX     InetAddressPrefixLength
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The prefix length that, when combined with the
            corresponding value of pimStarGRPFRouteAddress, identifies
            the route used to find the RPF interface towards the RP.
            The InetAddressType is given by the pimStarGRPFNextHopType
            object."
    ::= { pimStarGEntry 18 }

pimStarGRPFRouteMetricPref OBJECT-TYPE
    SYNTAX     Unsigned32 (0..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The metric preference of the route used to find the RPF
            interface towards the RP."
    ::= { pimStarGEntry 19 }

pimStarGRPFRouteMetric OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The routing metric of the route used to find the RPF
            interface towards the RP."
    ::= { pimStarGEntry 20 }

--
-- The PIM (*,G,I) State Table
--

pimStarGITable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimStarGIEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the interface-specific (*,G)
            state that PIM has."
    REFERENCE "RFC 4601 section 4.1.3"
    ::= { pim 5 }

pimStarGIEntry OBJECT-TYPE
    SYNTAX     PimStarGIEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimStarGITable."
    INDEX      { pimStarGAddressType,
                 pimStarGGrpAddress,
                 pimStarGIIfIndex }
    ::= { pimStarGITable 1 }

PimStarGIEntry ::= SEQUENCE {
    pimStarGIIfIndex                 InterfaceIndex,
    pimStarGIUpTime                  TimeTicks,
    pimStarGILocalMembership         TruthValue,
    pimStarGIJoinPruneState          INTEGER,
    pimStarGIPrunePendingTimer       TimeTicks,
    pimStarGIJoinExpiryTimer         TimeTicks,
    pimStarGIAssertState             INTEGER,
    pimStarGIAssertTimer             TimeTicks,
    pimStarGIAssertWinnerAddressType InetAddressType,
    pimStarGIAssertWinnerAddress     InetAddress,
    pimStarGIAssertWinnerMetricPref  Unsigned32,
    pimStarGIAssertWinnerMetric      Unsigned32
}

pimStarGIIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The ifIndex of the interface that this entry corresponds
            to."
    ::= { pimStarGIEntry 1 }

pimStarGIUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since this entry was created by the local router."
    ::= { pimStarGIEntry 2 }

pimStarGILocalMembership OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router has (*,G) local membership on this
            interface (resulting from a mechanism such as IGMP or MLD).
            This corresponds to local_receiver_include(*,G,I) in the
            PIM-SM specification."
    REFERENCE "RFC 3376, RFC 3810, and RFC 4601 section 4.1.6"
    ::= { pimStarGIEntry 3 }

pimStarGIJoinPruneState OBJECT-TYPE
    SYNTAX     INTEGER {
                  noInfo (1),
                  join (2),
                  prunePending (3)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The state resulting from (*,G) Join/Prune messages
            received on this interface.  This corresponds to the state
            of the downstream per-interface (*,G) state machine in the
            PIM-SM specification."
    REFERENCE "RFC 4601 section 4.5.2"
    ::= { pimStarGIEntry 4 }

pimStarGIPrunePendingTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before the local router acts on a (*,G)
            Prune message received on this interface, during which the
            router is waiting to see whether another downstream router
            will override the Prune message.  This timer is called the
            (*,G) Prune-Pending Timer in the PIM-SM specification.  This
            object is zero if the timer is not running."
    REFERENCE "RFC 4601 section 4.5.1"
    ::= { pimStarGIEntry 5 }

pimStarGIJoinExpiryTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before (*,G) Join state for this
            interface expires.  This timer is called the (*,G) Join
            Expiry Timer in the PIM-SM specification.  This object is
            zero if the timer is not running.  A value of 'FFFFFFFF'h
            indicates an infinite expiry time."
    REFERENCE "RFC 4601 section 4.10"
    ::= { pimStarGIEntry 6 }

pimStarGIAssertState OBJECT-TYPE
    SYNTAX     INTEGER {
                  noInfo (1),
                  iAmAssertWinner (2),
                  iAmAssertLoser (3)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The (*,G) Assert state for this interface.  This
            corresponds to the state of the per-interface (*,G) Assert
            state machine in the PIM-SM specification.  If
            pimStarGPimMode is 'bidir', this object must be 'noInfo'."
    REFERENCE "RFC 4601 section 4.6.2"
    ::= { pimStarGIEntry 7 }

pimStarGIAssertTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimStarGIAssertState is 'iAmAssertWinner', this is the
            time remaining before the local router next sends a (*,G)
            Assert message on this interface.  If pimStarGIAssertState
            is 'iAmAssertLoser', this is the time remaining before the
            (*,G) Assert state expires.  If pimStarGIAssertState is
            'noInfo', this is zero.  This timer is called the (*,G)
            Assert Timer in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.6.2"
    ::= { pimStarGIEntry 8 }

pimStarGIAssertWinnerAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimStarGIAssertState is 'iAmAssertLoser', this is the
            address type of the assert winner; otherwise, this object is
            unknown(0)."
    ::= { pimStarGIEntry 9 }

pimStarGIAssertWinnerAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimStarGIAssertState is 'iAmAssertLoser', this is the
            address of the assert winner.  The InetAddressType is given
            by the pimStarGIAssertWinnerAddressType object."
    ::= { pimStarGIEntry 10 }

pimStarGIAssertWinnerMetricPref OBJECT-TYPE
    SYNTAX     Unsigned32 (0..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimStarGIAssertState is 'iAmAssertLoser', this is the
            metric preference of the route to the RP advertised by the
            assert winner; otherwise, this object is zero."
    ::= { pimStarGIEntry 11 }

pimStarGIAssertWinnerMetric OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimStarGIAssertState is 'iAmAssertLoser', this is the
            routing metric of the route to the RP advertised by the
            assert winner; otherwise, this object is zero."
    ::= { pimStarGIEntry 12 }

--
-- The PIM (S,G) State Table
--

pimSGTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimSGEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the non-interface specific
            (S,G) state that PIM has."
    REFERENCE "RFC 4601 section 4.1.4"
    ::= { pim 6 }

pimSGEntry OBJECT-TYPE
    SYNTAX     PimSGEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimSGTable."
    INDEX      { pimSGAddressType,
                 pimSGGrpAddress,
                 pimSGSrcAddress }
    ::= { pimSGTable 1 }

PimSGEntry ::= SEQUENCE {
    pimSGAddressType               InetAddressType,
    pimSGGrpAddress                InetAddress,
    pimSGSrcAddress                InetAddress,
    pimSGUpTime                    TimeTicks,
    pimSGPimMode                   PimMode,
    pimSGUpstreamJoinState         INTEGER,
    pimSGUpstreamJoinTimer         TimeTicks,
    pimSGUpstreamNeighbor          InetAddress,
    pimSGRPFIfIndex                InterfaceIndexOrZero,
    pimSGRPFNextHopType            InetAddressType,
    pimSGRPFNextHop                InetAddress,
    pimSGRPFRouteProtocol          IANAipRouteProtocol,
    pimSGRPFRouteAddress           InetAddress,
    pimSGRPFRoutePrefixLength      InetAddressPrefixLength,
    pimSGRPFRouteMetricPref        Unsigned32,
    pimSGRPFRouteMetric            Unsigned32,
    pimSGSPTBit                    TruthValue,
    pimSGKeepaliveTimer            TimeTicks,
    pimSGDRRegisterState           INTEGER,
    pimSGDRRegisterStopTimer       TimeTicks,
    pimSGRPRegisterPMBRAddressType InetAddressType,
    pimSGRPRegisterPMBRAddress     InetAddress,
    pimSGUpstreamPruneState        INTEGER,
    pimSGUpstreamPruneLimitTimer   TimeTicks,
    pimSGOriginatorState           INTEGER,
    pimSGSourceActiveTimer         TimeTicks,
    pimSGStateRefreshTimer         TimeTicks
}

pimSGAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of the source and multicast group for this
            entry."
    ::= { pimSGEntry 1 }

pimSGGrpAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The multicast group address for this entry.  The
            InetAddressType is given by the pimSGAddressType object."
    ::= { pimSGEntry 2 }

pimSGSrcAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The source address for this entry.  The InetAddressType is
            given by the pimSGAddressType object."
    ::= { pimSGEntry 3 }

pimSGUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since this entry was created by the local router."
    ::= { pimSGEntry 4 }

pimSGPimMode OBJECT-TYPE
    SYNTAX     PimMode 
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether pimSGGrpAddress is an SSM (Source Specific
            Multicast, used with PIM-SM) or ASM (Any Source Multicast,
            used with PIM-SM), or DM (Dense Mode)  group."
    REFERENCE "RFC 4601 section 4.5.2, RFC 3569, and
               'IP Multicast MIB' (August 2007) ipMcastSsmRangeTable"
    ::= { pimSGEntry 5 }

pimSGUpstreamJoinState OBJECT-TYPE
    SYNTAX     INTEGER {
                  notApplicable (0),
                  notJoined (1),
                  joined (2)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router should join the shortest-path tree
            for the source and group represented by this entry.  This
            corresponds to the state of the upstream (S,G) state machine
            in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.5.7"
    ::= { pimSGEntry 6 }

pimSGUpstreamJoinTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before the local router next sends a
            periodic (S,G) Join message on pimSGRPFIfIndex.  This timer
            is called the (S,G) Upstream Join Timer in the PIM-SM
            specification.  This object is zero if the timer is not
            running."
    REFERENCE "RFC 4601 sections 4.10 and 4.11"
    ::= { pimSGEntry 7 }

pimSGUpstreamNeighbor OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The primary address of the neighbor on pimSGRPFIfIndex that
            the local router is sending periodic (S,G) Join messages to.
            This is zero if the RPF next hop is unknown or is not a
            PIM neighbor.  The InetAddressType is given by the
            pimSGAddressType object.  This address is called RPF'(S,G)
            in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.1.6"
    ::= { pimSGEntry 8 }

pimSGRPFIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndexOrZero
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The value of ifIndex for the RPF interface towards the
            source, or zero if the RPF interface is unknown."
    ::= { pimSGEntry 9 }

pimSGRPFNextHopType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type of the RPF next hop towards the source, or
            unknown(0) if the RPF next hop is unknown."
    ::= { pimSGEntry 10 }

pimSGRPFNextHop OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address of the RPF next hop towards the source.  The
            InetAddressType is given by the pimSGRPFNextHopType.  This
            address is called MRIB.next_hop(S) in the PIM-SM
            specification."
    REFERENCE "RFC 4601 section 4.5.5"
    ::= { pimSGEntry 11 }

pimSGRPFRouteProtocol OBJECT-TYPE
    SYNTAX     IANAipRouteProtocol
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The routing mechanism via which the route used to find the
            RPF interface towards the source was learned."
    ::= { pimSGEntry 12 }

pimSGRPFRouteAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The IP address that, when combined with the corresponding
            value of pimSGRPFRoutePrefixLength, identifies the route
            used to find the RPF interface towards the source.  The
            InetAddressType is given by the pimSGRPFNextHopType object.

            This address object is only significant up to
            pimSGRPFRoutePrefixLength bits.  The remainder of the
            address bits are zero."
    ::= { pimSGEntry 13 }

pimSGRPFRoutePrefixLength OBJECT-TYPE
    SYNTAX     InetAddressPrefixLength
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The prefix length that, when combined with the
            corresponding value of pimSGRPFRouteAddress, identifies the
            route used to find the RPF interface towards the source.
            The InetAddressType is given by the pimSGRPFNextHopType
            object."
    ::= { pimSGEntry 14 }

pimSGRPFRouteMetricPref OBJECT-TYPE
    SYNTAX     Unsigned32 (0..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The metric preference of the route used to find the RPF
            interface towards the source."
    ::= { pimSGEntry 15 }

pimSGRPFRouteMetric OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The routing metric of the route used to find the RPF
            interface towards the source."
    ::= { pimSGEntry 16 }

pimSGSPTBit OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the SPT bit is set; and therefore whether
            forwarding is taking place on the shortest-path tree."
    ::= { pimSGEntry 17 }

pimSGKeepaliveTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before this (S,G) state expires, in
            the absence of explicit (S,G) local membership or (S,G)
            Join messages received to maintain it.  This timer is
            called the (S,G) Keepalive Timer in the PIM-SM
            specification."
    REFERENCE "RFC 4601 section 4.1.4"
    ::= { pimSGEntry 18 }

pimSGDRRegisterState OBJECT-TYPE
    SYNTAX     INTEGER {
                  noInfo (1),
                  join (2),
                  joinPending (3),
                  prune (4)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router should encapsulate (S,G) data
            packets in Register messages and send them to the RP.  This
            corresponds to the state of the per-(S,G) Register state
            machine in the PIM-SM specification.  This object is always
            'noInfo' unless pimSGPimMode is 'asm'."
    REFERENCE "RFC 4601 section 4.4.1"
    ::= { pimSGEntry 19 }

pimSGDRRegisterStopTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGDRRegisterState is 'prune', this is the time
            remaining before the local router sends a Null-Register
            message to the RP.  If pimSGDRRegisterState is
            'joinPending', this is the time remaining before the local
            router resumes encapsulating data packets and sending them
            to the RP.  Otherwise, this is zero.  This timer is called
            the Register-Stop Timer in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.4"
    ::= { pimSGEntry 20 }

pimSGRPRegisterPMBRAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The address type of the first PIM Multicast Border Router
            to send a Register message with the Border bit set.  This
            object is unknown(0) if the local router is not the RP for
            the group."
    ::= { pimSGEntry 21 }

pimSGRPRegisterPMBRAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The IP address of the first PIM Multicast Border Router to
            send a Register message with the Border bit set.  The
            InetAddressType is given by the
            pimSGRPRegisterPMBRAddressType object."
    ::= { pimSGEntry 22 }

pimSGUpstreamPruneState OBJECT-TYPE
    SYNTAX     INTEGER {
                  notApplicable (0),
                  forwarding (1),
                  ackpending (2),
                  pruned (3)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router has pruned itself from the tree.
            This corresponds to the state of the upstream prune (S,G)
            state machine in the PIM-DM specification.  This object is
            used only by PIM-DM."
    REFERENCE "RFC 3973 section 4.4.1"
    ::= { pimSGEntry 23 }

pimSGUpstreamPruneLimitTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before the local router may send a (S,G)
            Prune message on pimSGRPFIfIndex.  This timer is called the
            (S,G) Prune Limit Timer in the PIM-DM specification.  This
            object is zero if the timer is not running.  This object is
            used only by PIM-DM."
    REFERENCE "RFC 2973 section 4.8"
    ::= { pimSGEntry 24 }

pimSGOriginatorState OBJECT-TYPE
    SYNTAX     INTEGER {
                  notApplicable (0),
                  notOriginator (1),
                  originator (2)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the router is an originator for an (S,G) message
            flow.  This corresponds to the state of the per-(S,G)
            Originator state machine in the PIM-DM specification.  This
            object is used only by PIM-DM."
    REFERENCE "RFC 3973 section 4.5.2"
    ::= { pimSGEntry 25 }

pimSGSourceActiveTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGOriginatorState is 'originator', this is the time
            remaining before the local router reverts to a notOriginator
            state.  Otherwise, this is zero.  This timer is called the
            Source Active Timer in the PIM-DM specification.  This
            object is used only by PIM-DM."
    REFERENCE "RFC 3973 section 4.8"
    ::= { pimSGEntry 26 }

pimSGStateRefreshTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGOriginatorState is 'originator', this is the time
            remaining before the local router sends a State Refresh
            message.  Otherwise, this is zero.  This timer is called the
            State Refresh Timer in the PIM-DM specification.  This
            object is used only by PIM-DM."
    REFERENCE "RFC 3973 section 4.8"
    ::= { pimSGEntry 27 }

--
-- The PIM (S,G,I) State Table
--

pimSGITable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimSGIEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the interface-specific (S,G)
            state that PIM has."
    REFERENCE "RFC 4601 section 4.1.4"
    ::= { pim 7 }

pimSGIEntry OBJECT-TYPE
    SYNTAX     PimSGIEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimSGITable."
    INDEX      { pimSGAddressType,
                 pimSGGrpAddress,
                 pimSGSrcAddress,
                 pimSGIIfIndex }
    ::= { pimSGITable 1 }

PimSGIEntry ::= SEQUENCE {
    pimSGIIfIndex                 InterfaceIndex,
    pimSGIUpTime                  TimeTicks,
    pimSGILocalMembership         TruthValue,
    pimSGIJoinPruneState          INTEGER,
    pimSGIPrunePendingTimer       TimeTicks,
    pimSGIJoinExpiryTimer         TimeTicks,
    pimSGIAssertState             INTEGER,
    pimSGIAssertTimer             TimeTicks,
    pimSGIAssertWinnerAddressType InetAddressType,
    pimSGIAssertWinnerAddress     InetAddress,
    pimSGIAssertWinnerMetricPref  Unsigned32,
    pimSGIAssertWinnerMetric      Unsigned32
}

pimSGIIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The ifIndex of the interface that this entry corresponds
            to."
    ::= { pimSGIEntry 1 }

pimSGIUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since this entry was created by the local router."
    ::= { pimSGIEntry 2 }

pimSGILocalMembership OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router has (S,G) local membership on this
            interface (resulting from a mechanism such as IGMP or MLD).
            This corresponds to local_receiver_include(S,G,I) in the
            PIM-SM specification."
    REFERENCE "RFC 3376, RFC 3810, RFC 4601 sections 4.1.6, 4.6.1, and
              4.6.2"
    ::= { pimSGIEntry 3 }

pimSGIJoinPruneState OBJECT-TYPE
    SYNTAX     INTEGER {
                  noInfo (1),
                  join (2),
                  prunePending (3),
                  pruned (4)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The state resulting from (S,G) Join/Prune messages
            received on this interface.  This corresponds to the state
            of the downstream per-interface (S,G) state machine in the
            PIM-SM and PIM-DM specification."
    REFERENCE "RFC 4601 section 4.5.3 and RFC 3973 section 4.4.2"
    ::= { pimSGIEntry 4 }

pimSGIPrunePendingTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before the local router acts on an (S,G)
            Prune message received on this interface, during which the
            router is waiting to see whether another downstream router
            will override the Prune message.  This timer is called the
            (S,G) Prune-Pending Timer in the PIM-SM specification.  This
            object is zero if the timer is not running."
    REFERENCE "RFC 4601 sections 4.5.3 and 4.5.4"
    ::= { pimSGIEntry 5 }

pimSGIJoinExpiryTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before (S,G) Join state for this
            interface expires.  This timer is called the (S,G) Join
            Expiry Timer in the PIM-SM specification.  This object is
            zero if the timer is not running.  A value of 'FFFFFFFF'h
            indicates an infinite expiry time.  This timer is called the
            (S,G) Prune Timer in the PIM-DM specification."
    REFERENCE "RFC 4601 section 4.10 and RFC 3973 section 4.8"
    ::= { pimSGIEntry 6 }

pimSGIAssertState OBJECT-TYPE
    SYNTAX     INTEGER {
                  noInfo (1),
                  iAmAssertWinner (2),
                  iAmAssertLoser (3)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The (S,G) Assert state for this interface.  This
            corresponds to the state of the per-interface (S,G) Assert
            state machine in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.6.1"
    ::= { pimSGIEntry 7 }

pimSGIAssertTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGIAssertState is 'iAmAssertWinner', this is the time
            remaining before the local router next sends a (S,G) Assert
            message on this interface.  If pimSGIAssertState is
            'iAmAssertLoser', this is the time remaining before the
            (S,G) Assert state expires.  If pimSGIAssertState is
            'noInfo', this is zero.  This timer is called the (S,G)
            Assert Timer in the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.6.1"
    ::= { pimSGIEntry 8 }

pimSGIAssertWinnerAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGIAssertState is 'iAmAssertLoser', this is the
            address type of the assert winner; otherwise, this object is
            unknown(0)."
    ::= { pimSGIEntry 9 }

pimSGIAssertWinnerAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGIAssertState is 'iAmAssertLoser', this is the
            address of the assert winner.  The InetAddressType is given
            by the pimSGIAssertWinnerAddressType object."
    ::= { pimSGIEntry 10 }

pimSGIAssertWinnerMetricPref OBJECT-TYPE
    SYNTAX     Unsigned32 (0..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGIAssertState is 'iAmAssertLoser', this is the
            metric preference of the route to the source advertised by
            the assert winner; otherwise, this object is zero."
    ::= { pimSGIEntry 11 }

pimSGIAssertWinnerMetric OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "If pimSGIAssertState is 'iAmAssertLoser', this is the
            routing metric of the route to the source advertised by the
            assert winner; otherwise, this object is zero."
    ::= { pimSGIEntry 12 }

--
-- The PIM (S,G,rpt) State Table
--

pimSGRptTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimSGRptEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the non-interface specific
            (S,G,rpt) state that PIM has."
    REFERENCE "RFC 4601 section 4.1.5"
    ::= { pim 8 }

pimSGRptEntry OBJECT-TYPE
    SYNTAX     PimSGRptEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimSGRptTable."
    INDEX      { pimStarGAddressType,
                 pimStarGGrpAddress,
                 pimSGRptSrcAddress }
    ::= { pimSGRptTable 1 }

PimSGRptEntry ::= SEQUENCE {
    pimSGRptSrcAddress             InetAddress,
    pimSGRptUpTime                 TimeTicks,
    pimSGRptUpstreamPruneState     INTEGER,
    pimSGRptUpstreamOverrideTimer  TimeTicks
}

pimSGRptSrcAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The source address for this entry.  The InetAddressType is
            given by the pimStarGAddressType object."
    ::= { pimSGRptEntry 1 }

pimSGRptUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since this entry was created by the local router."
    ::= { pimSGRptEntry 2 }

pimSGRptUpstreamPruneState OBJECT-TYPE
    SYNTAX     INTEGER {
                  rptNotJoined (1),
                  pruned (2),
                  notPruned (3)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router should prune the source off the RP
            tree.  This corresponds to the state of the upstream
            (S,G,rpt) state machine for triggered messages in the PIM-SM
            specification."
    REFERENCE "RFC 4601 section 4.5.9"
    ::= { pimSGRptEntry 3 }

pimSGRptUpstreamOverrideTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before the local router sends a
            triggered (S,G,rpt) Join message on pimStarGRPFIfIndex.
            This timer is called the (S,G,rpt) Upstream Override Timer
            in the PIM-SM specification.  This object is zero if the
            timer is not running."
    REFERENCE "RFC 4601 section 4.5.9"
    ::= { pimSGRptEntry 4 }

--
-- The PIM (S,G,rpt,I) State Table
--

pimSGRptITable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimSGRptIEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the interface-specific
            (S,G,rpt) state that PIM has."
    REFERENCE "RFC 4601 section 4.1.5"
    ::= { pim 9 }

pimSGRptIEntry OBJECT-TYPE
    SYNTAX     PimSGRptIEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimSGRptITable."
    INDEX      { pimStarGAddressType,
                 pimStarGGrpAddress,
                 pimSGRptSrcAddress,
                 pimSGRptIIfIndex }
    ::= { pimSGRptITable 1 }

PimSGRptIEntry ::= SEQUENCE {
    pimSGRptIIfIndex            InterfaceIndex,
    pimSGRptIUpTime             TimeTicks,
    pimSGRptILocalMembership    TruthValue,
    pimSGRptIJoinPruneState     INTEGER,
    pimSGRptIPrunePendingTimer  TimeTicks,
    pimSGRptIPruneExpiryTimer   TimeTicks
}

pimSGRptIIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The ifIndex of the interface that this entry corresponds
            to."
    ::= { pimSGRptIEntry 1 }

pimSGRptIUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since this entry was created by the local router."
    ::= { pimSGRptIEntry 2 }

pimSGRptILocalMembership OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether the local router has both (*,G) include local
            membership and (S,G) exclude local membership on this
            interface (resulting from a mechanism such as IGMP or MLD).
            This corresponds to local_receiver_exclude(S,G,I) in the
            PIM-SM specification."
    REFERENCE "RFC 3376, RFC 3810, RFC 4601 section 4.1.6"
    ::= { pimSGRptIEntry 3 }

pimSGRptIJoinPruneState OBJECT-TYPE
    SYNTAX     INTEGER {
                  noInfo (1),
                  prune (2),
                  prunePending (3)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The state resulting from (S,G,rpt) Join/Prune messages
            received on this interface.  This corresponds to the state
            of the downstream per-interface (S,G,rpt) state machine in
            the PIM-SM specification."
    REFERENCE "RFC 4601 section 4.5.4"
    ::= { pimSGRptIEntry 4 }

pimSGRptIPrunePendingTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before the local router starts pruning
            this source off the RP tree.  This timer is called the
            (S,G,rpt) Prune-Pending Timer in the PIM-SM specification.
            This object is zero if the timer is not running."
    REFERENCE "RFC 4601 section 4.5.4"
    ::= { pimSGRptIEntry 5 }

pimSGRptIPruneExpiryTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time remaining before (S,G,rpt) Prune state for this
            interface expires.  This timer is called the (S,G,rpt)
            Prune Expiry Timer in the PIM-SM specification.  This object
            is zero if the timer is not running.  A value of 'FFFFFFFF'h
            indicates an infinite expiry time."
    REFERENCE "RFC 4601 section 4.5.4"
    ::= { pimSGRptIEntry 6 }

--
-- The PIM Bidir DF-Election Table
--

pimBidirDFElectionTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimBidirDFElectionEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing the per-RP Designated
            Forwarder (DF) Election state for each interface for all the
            RPs in BIDIR mode."
    REFERENCE "RFC 5015 section 3.5"
    ::= { pim 10 }

pimBidirDFElectionEntry OBJECT-TYPE
    SYNTAX     PimBidirDFElectionEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimBidirDFElectionTable."
    INDEX      { pimBidirDFElectionAddressType,
                 pimBidirDFElectionRPAddress,
                 pimBidirDFElectionIfIndex }
    ::= { pimBidirDFElectionTable 1 }

PimBidirDFElectionEntry ::= SEQUENCE {
    pimBidirDFElectionAddressType       InetAddressType,
    pimBidirDFElectionRPAddress         InetAddress,
    pimBidirDFElectionIfIndex           InterfaceIndex,
    pimBidirDFElectionWinnerAddressType InetAddressType,
    pimBidirDFElectionWinnerAddress     InetAddress,
    pimBidirDFElectionWinnerUpTime      TimeTicks,
    pimBidirDFElectionWinnerMetricPref  Unsigned32,
    pimBidirDFElectionWinnerMetric      Unsigned32,
    pimBidirDFElectionState             INTEGER,
    pimBidirDFElectionStateTimer        TimeTicks
}

pimBidirDFElectionAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of the RP for which the DF state is being
            maintained."
    ::= { pimBidirDFElectionEntry 1 }

pimBidirDFElectionRPAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The IP address of the RP for which the DF state is being
            maintained.  The InetAddressType is given by the
            pimBidirDFElectionAddressType object."
    ::= { pimBidirDFElectionEntry 2 }

pimBidirDFElectionIfIndex OBJECT-TYPE
    SYNTAX     InterfaceIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The value of ifIndex for the interface for which the DF
            state is being maintained."
    ::= { pimBidirDFElectionEntry 3 }

pimBidirDFElectionWinnerAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The primary address type of the winner of the DF Election
            process.  A value of unknown(0) indicates there is currently
            no DF."
    ::= { pimBidirDFElectionEntry 4 }

pimBidirDFElectionWinnerAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The primary IP address of the winner of the DF Election
            process.  The InetAddressType is given by the
            pimBidirDFElectionWinnerAddressType object."
    ::= { pimBidirDFElectionEntry 5 }

pimBidirDFElectionWinnerUpTime OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The time since the current winner (last) became elected as
            the DF for this RP."
    ::= { pimBidirDFElectionEntry 6 }

pimBidirDFElectionWinnerMetricPref OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The metric preference advertised by the DF Winner, or zero
            if there is currently no DF."
    ::= { pimBidirDFElectionEntry 7 }

pimBidirDFElectionWinnerMetric OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The metric advertised by the DF Winner, or zero if there is
            currently no DF."
    ::= { pimBidirDFElectionEntry 8 }

pimBidirDFElectionState OBJECT-TYPE
    SYNTAX     INTEGER {
                  dfOffer(1),
                  dfLose(2),
                  dfWinner(3),
                  dfBackoff(4)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The state of this interface with respect to DF-Election for
            this RP.  The states correspond to the ones defined in the
            BIDIR-PIM specification."
    REFERENCE "RFC 5015 section *******"
    ::= { pimBidirDFElectionEntry 9 }

pimBidirDFElectionStateTimer OBJECT-TYPE
    SYNTAX     TimeTicks
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The minimum time remaining after which the local router
            will expire the current DF state represented by
            pimBidirDFElectionState."
    ::= { pimBidirDFElectionEntry 10 }

--
-- The PIM Static RP Table
--

pimStaticRPTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimStaticRPEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "This table is used to manage static configuration of RPs.

            If the group prefixes configured for two or more rows in
            this table overlap, the row with the greatest value of
            pimStaticRPGrpPrefixLength is used for the overlapping
            range."
    REFERENCE "RFC 4601 section 3.7"
    ::= { pim 11 }

pimStaticRPEntry OBJECT-TYPE
    SYNTAX     PimStaticRPEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimStaticRPTable.  This
            entry is preserved on agent restart."
    INDEX      { pimStaticRPAddressType,
                 pimStaticRPGrpAddress,
                 pimStaticRPGrpPrefixLength }
    ::= { pimStaticRPTable 1 }

PimStaticRPEntry ::= SEQUENCE {
    pimStaticRPAddressType      InetAddressType,
    pimStaticRPGrpAddress       InetAddress,
    pimStaticRPGrpPrefixLength  InetAddressPrefixLength,
    pimStaticRPRPAddress        InetAddress,
    pimStaticRPPimMode          PimMode,
    pimStaticRPOverrideDynamic  TruthValue,
    pimStaticRPPrecedence       Unsigned32,
    pimStaticRPRowStatus        RowStatus,
    pimStaticRPStorageType      StorageType
}

pimStaticRPAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of this entry."
    ::= { pimStaticRPEntry 1 }

pimStaticRPGrpAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The multicast group address that, when combined with
            pimStaticRPGrpPrefixLength, gives the group prefix for this
            entry.  The InetAddressType is given by the
            pimStaticRPAddressType object.

            This address object is only significant up to
            pimStaticRPGrpPrefixLength bits.  The remainder of the
            address bits are zero.  This is especially important for
            this index field, which is part of the index of this entry.
            Any non-zero bits would signify an entirely different
            entry."
    ::= { pimStaticRPEntry 2 }

pimStaticRPGrpPrefixLength OBJECT-TYPE
    SYNTAX     InetAddressPrefixLength (4..128)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The multicast group prefix length that, when combined
            with pimStaticRPGrpAddress, gives the group prefix for this
            entry.  The InetAddressType is given by the
            pimStaticRPAddressType object.  If pimStaticRPAddressType is
            'ipv4' or 'ipv4z', this object must be in the range 4..32.
            If pimStaticRPGrpAddressType is 'ipv6' or 'ipv6z', this
            object must be in the range 8..128."
    ::= { pimStaticRPEntry 3 }

pimStaticRPRPAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The IP address of the RP to be used for groups within this
            group prefix.  The InetAddressType is given by the
            pimStaticRPAddressType object."
    ::= { pimStaticRPEntry 4 }

pimStaticRPPimMode OBJECT-TYPE
    SYNTAX     PimMode 
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The PIM mode to be used for groups in this group prefix.

            If this object is set to ssm(2) or dm(5), then 
            pimStaticRPRPAddress must be set to zero.  No RP 
            operations are ever possible for PIM Mode SSM or DM."
    REFERENCE "RFC 4601 section 3.7, RFC 3569, and
               'IP Multicast MIB' (August 2007) ipMcastSsmRangeTable"
    DEFVAL { asm }
    ::= { pimStaticRPEntry 5 }

pimStaticRPOverrideDynamic OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "Whether this static RP configuration will override other
            group mappings in this group prefix.  If this object is
            TRUE, then it will override:

            -  RP information learned dynamically for groups in this
            group prefix.

            -  RP information configured in pimStaticRPTable with
            pimStaticRPOverrideDynamic set to FALSE.

            See pimGroupMappingTable for details."
    DEFVAL { false }
    ::= { pimStaticRPEntry 6 }

pimStaticRPPrecedence OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The value for pimGroupMappingPrecedence to be used for this
            static RP configuration.  This allows fine control over
            which configuration is overridden by this static
            configuration.

            If pimStaticRPOverrideDynamic is set to TRUE, all dynamic RP
            configuration is overridden by this static configuration,
            whatever the value of this object.

            The absolute values of this object have a significance only
            on the local router and do not need to be coordinated with
            other routers.  A setting of this object may have different
            effects when applied to other routers.

            Do not use this object unless fine control of static RP
            behavior on the local router is required.

            A value of 65535 is used to represent an 'undefined' value
            indicating that this object is not being used."
    ::= { pimStaticRPEntry 7 }

pimStaticRPRowStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The status of this row, by which rows in this table can
            be created and destroyed.

            This status object cannot be set to active(1) before a valid
            value has been written to pimStaticRPRPAddress.

            All writeable objects in this entry can be modified when the
            status of this entry is active(1)."
    ::= { pimStaticRPEntry 8 }

pimStaticRPStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The storage type for this row.  Rows having the value
            'permanent' need not allow write-access to any columnar
            objects in the row."
    DEFVAL { nonVolatile }
    ::= { pimStaticRPEntry 9 }

--
-- The PIM Anycast-RP Set Table
--

pimAnycastRPSetTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimAnycastRPSetEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "This table is used to manage Anycast-RP via PIM Register
            messages, as opposed to via other protocols such as MSDP
            (Multicast Source Discovery Protocol).

            Entries must be configured in this table if and only if the
            local router is a member of one or more Anycast-RP sets,
            that is, one or more Anycast-RP addresses are assigned to
            the local router.  Note that if using static RP
            configuration, this is in addition to, not instead of, the
            pimStaticRPTable entries that must be configured for the
            Anycast-RPs.

            The set of rows with the same values of both
            pimAnycastRPSetAddressType and pimAnycastRPSetAnycastAddress
            corresponds to the Anycast-RP set for that Anycast-RP
            address.

            When an Anycast-RP set configuration is active, one entry
            per pimAnycastRPSetAnycastAddress corresponds to the local
            router.  The local router is identified by the
            pimAnycastRpSetLocalRouter object.  That entry determines
            the source address used by the local router when forwarding
            PIM Register messages within the Anycast-RP set."
    REFERENCE "RFC 4610, RFC 3618"
    ::= { pim 12 }

pimAnycastRPSetEntry OBJECT-TYPE
    SYNTAX     PimAnycastRPSetEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry corresponds to a single router within a particular
            Anycast-RP set.  This entry is preserved on agent restart."
    INDEX      { pimAnycastRPSetAddressType,
                 pimAnycastRPSetAnycastAddress,
                 pimAnycastRPSetRouterAddress }
    ::= { pimAnycastRPSetTable 1 }

PimAnycastRPSetEntry ::= SEQUENCE {
    pimAnycastRPSetAddressType     InetAddressType,
    pimAnycastRPSetAnycastAddress  InetAddress,
    pimAnycastRPSetRouterAddress   InetAddress,
    pimAnycastRPSetLocalRouter     TruthValue,
    pimAnycastRPSetRowStatus       RowStatus,
    pimAnycastRPSetStorageType     StorageType
}

pimAnycastRPSetAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of the Anycast-RP address and router
            address."
    ::= { pimAnycastRPSetEntry 1 }

pimAnycastRPSetAnycastAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The Anycast-RP address.  The InetAddressType is given by
            the pimAnycastRPSetAddressType object."
    ::= { pimAnycastRPSetEntry 2 }

pimAnycastRPSetRouterAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address of a router that is a member of the Anycast-RP
            set.  The InetAddressType is given by the
            pimAnycastRPSetAddressType object.

            This address differs from pimAnycastRPSetAnycastAddress.
            Equal values for these two addresses in a single entry are
            not permitted.  That would cause a Register loop."
    ::= { pimAnycastRPSetEntry 3 }

pimAnycastRPSetLocalRouter OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "Whether this entry corresponds to the local router."
    ::= { pimAnycastRPSetEntry 4 }

pimAnycastRPSetRowStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The status of this row, by which rows in this table can
            be created and destroyed.

            This status object can be set to active(1) without setting
            any other columnar objects in this entry.

            All writeable objects in this entry can be modified when the
            status of this entry is active(1)."
    ::= { pimAnycastRPSetEntry 5 }

pimAnycastRPSetStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
            "The storage type for this row.  Rows having the value
            'permanent' need not allow write-access to any columnar
            objects in the row."
    DEFVAL { nonVolatile }
    ::= { pimAnycastRPSetEntry 6 }

--
-- The PIM Group Mapping Table
--

pimGroupMappingTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF PimGroupMappingEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The (conceptual) table listing mappings from multicast
            group prefixes to the PIM mode and RP address to use for
            groups within that group prefix.

            Rows in this table are created for a variety of reasons,
            indicated by the value of the pimGroupMappingOrigin object.

            -  Rows with a pimGroupMappingOrigin value of 'fixed' are
               created automatically by the router at startup, to
               correspond to the well-defined prefixes of link-local and
               unroutable group addresses.  These rows are never
               destroyed.

            -  Rows with a pimGroupMappingOrigin value of 'embedded' are
               created by the router to correspond to group prefixes
               that are to be treated as being in Embedded-RP format.

            -  Rows with a pimGroupMappingOrigin value of 'configRp' are
               created and destroyed as a result of rows in the
               pimStaticRPTable being created and destroyed.

            -  Rows with a pimGroupMappingOrigin value of 'configSsm'
               are created and destroyed as a result of configuration of
               SSM address ranges to the local router.

            -  Rows with a pimGroupMappingOrigin value of 'bsr' are
               created as a result of running the PIM Bootstrap Router
               (BSR) mechanism.  If the local router is not the elected
               BSR, these rows are created to correspond to group
               prefixes in the PIM Bootstrap messages received from the
               elected BSR.  If the local router is the elected BSR,
               these rows are created to correspond to group prefixes in
               the PIM Bootstrap messages that the local router sends.
               In either case, these rows are destroyed when the group
               prefixes are timed out by the BSR mechanism.

            -  Rows with a pimGroupMappingOrigin value of 'other' are
               created and destroyed according to some other mechanism
               not specified here.

            Given the collection of rows in this table at any point in
            time, the PIM mode and RP address to use for a particular
            group is determined using the following algorithm.

            1. From the set of all rows, the subset whose group prefix
               contains the group in question are selected.

            2. If there are no such rows, then the group mapping is
               undefined.

            3. If there are multiple selected rows, and a subset is
               defined by pimStaticRPTable (pimGroupMappingOrigin value
               of 'configRp') with pimStaticRPOverrideDynamic set to
               TRUE, then this subset is selected.

            4. From the selected subset of rows, the subset that have
               the greatest value of pimGroupMappingGrpPrefixLength are
               selected.

            5. If there are still multiple selected rows, the subset
               that has the highest precedence (the lowest numerical
               value for pimGroupMappingPrecedence) is selected.

            6. If there are still multiple selected rows, the row
               selected is implementation dependent; the implementation
               might or might not apply the PIM hash function to select
               the row.

            7. The group mode to use is given by the value of
               pimGroupMappingPimMode from the single selected row; the
               RP to use is given by the value of
               pimGroupMappingRPAddress, unless pimGroupMappingOrigin is
               'embedded', in which case, the RP is extracted from the
               group address in question."
    REFERENCE "RFC 4601 section 3.7, RFC 3956, and RFC 4610"
    ::= { pim 13 }

pimGroupMappingEntry OBJECT-TYPE
    SYNTAX     PimGroupMappingEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "An entry (conceptual row) in the pimGroupMappingTable."
    INDEX      { pimGroupMappingOrigin,
                 pimGroupMappingAddressType,
                 pimGroupMappingGrpAddress,
                 pimGroupMappingGrpPrefixLength,
                 pimGroupMappingRPAddressType,
                 pimGroupMappingRPAddress }
    ::= { pimGroupMappingTable 1 }

PimGroupMappingEntry ::= SEQUENCE {
    pimGroupMappingOrigin           PimGroupMappingOriginType,
    pimGroupMappingAddressType      InetAddressType,
    pimGroupMappingGrpAddress       InetAddress,
    pimGroupMappingGrpPrefixLength  InetAddressPrefixLength,
    pimGroupMappingRPAddressType    InetAddressType,
    pimGroupMappingRPAddress        InetAddress,
    pimGroupMappingPimMode          PimMode,
    pimGroupMappingPrecedence       Unsigned32
}

pimGroupMappingOrigin OBJECT-TYPE
    SYNTAX     PimGroupMappingOriginType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The mechanism by which this group mapping was learned."
    ::= { pimGroupMappingEntry 1 }

pimGroupMappingAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of the IP multicast group prefix."
    ::= { pimGroupMappingEntry 2 }

pimGroupMappingGrpAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The IP multicast group address that, when combined with
            pimGroupMappingGrpPrefixLength, gives the group prefix for
            this mapping.  The InetAddressType is given by the
            pimGroupMappingAddressType object.

            This address object is only significant up to
            pimGroupMappingGrpPrefixLength bits.  The remainder of the
            address bits are zero.  This is especially important for
            this index field, which is part of the index of this entry.
            Any non-zero bits would signify an entirely different
            entry."
    ::= { pimGroupMappingEntry 3 }

pimGroupMappingGrpPrefixLength OBJECT-TYPE
    SYNTAX     InetAddressPrefixLength (4..128)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The multicast group prefix length that, when combined
            with pimGroupMappingGrpAddress, gives the group prefix for
            this mapping.  The InetAddressType is given by the
            pimGroupMappingAddressType object.  If
            pimGroupMappingAddressType is 'ipv4' or 'ipv4z', this
            object must be in the range 4..32.  If
            pimGroupMappingAddressType is 'ipv6' or 'ipv6z', this object
            must be in the range 8..128."
    ::= { pimGroupMappingEntry 4 }

pimGroupMappingRPAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The address type of the RP to be used for groups within
            this group prefix, or unknown(0) if no RP is to be used or
            if the RP address is unknown.  This object must be
            unknown(0) if pimGroupMappingPimMode is ssm(2), or if
            pimGroupMappingOrigin is embedded(6)."
    ::= { pimGroupMappingEntry 5 }

pimGroupMappingRPAddress OBJECT-TYPE
    SYNTAX     InetAddress (SIZE (0|4|8|16|20))
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "The IP address of the RP to be used for groups within this
            group prefix.  The InetAddressType is given by the
            pimGroupMappingRPAddressType object."
    ::= { pimGroupMappingEntry 6 }

pimGroupMappingPimMode OBJECT-TYPE
    SYNTAX     PimMode
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The PIM mode to be used for groups in this group prefix."
    ::= { pimGroupMappingEntry 7 }

pimGroupMappingPrecedence OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "The precedence of this row, used in the algorithm that
            determines which row applies to a given group address
            (described above).  Numerically higher values for this
            object indicate lower precedences, with the value zero
            denoting the highest precedence.

            The absolute values of this object have a significance only
            on the local router and do not need to be coordinated with
            other routers."
    ::= { pimGroupMappingEntry 8 }

--
-- PIM Notifications
--

pimNeighborLoss NOTIFICATION-TYPE
    OBJECTS { pimNeighborUpTime }
    STATUS     current
    DESCRIPTION
            "A pimNeighborLoss notification signifies the loss of an
            adjacency with a neighbor.  This notification should be
            generated when the neighbor timer expires, and the router
            has no other neighbors on the same interface with the same
            IP version and a lower IP address than itself.

            This notification is generated whenever the counter
            pimNeighborLossCount is incremented, subject
            to the rate limit specified by
            pimNeighborLossNotificationPeriod."
    REFERENCE "RFC 4601 section 4.3.2"
    ::= { pimNotifications 1 }

pimInvalidRegister NOTIFICATION-TYPE
    OBJECTS { pimGroupMappingPimMode,
              pimInvalidRegisterAddressType,
              pimInvalidRegisterOrigin,
              pimInvalidRegisterGroup,
              pimInvalidRegisterRp
            }
    STATUS      current
    DESCRIPTION
            "A pimInvalidRegister notification signifies that an invalid
            PIM Register message was received by this device.

            This notification is generated whenever the counter
            pimInvalidRegisterMsgsRcvd is incremented, subject to the
            rate limit specified by
            pimInvalidRegisterNotificationPeriod."
    REFERENCE "RFC 4601 section 4.4.2"
    ::= { pimNotifications 2 }

pimInvalidJoinPrune NOTIFICATION-TYPE
    OBJECTS { pimGroupMappingPimMode,
              pimInvalidJoinPruneAddressType,
              pimInvalidJoinPruneOrigin,
              pimInvalidJoinPruneGroup,
              pimInvalidJoinPruneRp,
              pimNeighborUpTime
            }
    STATUS      current
    DESCRIPTION
            "A pimInvalidJoinPrune notification signifies that an
            invalid PIM Join/Prune message was received by this device.

            This notification is generated whenever the counter
            pimInvalidJoinPruneMsgsRcvd is incremented, subject to the
            rate limit specified by
            pimInvalidJoinPruneNotificationPeriod."
    REFERENCE "RFC 4601 section 4.5.2"
    ::= { pimNotifications 3 }

pimRPMappingChange NOTIFICATION-TYPE
    OBJECTS { pimGroupMappingPimMode,
              pimGroupMappingPrecedence
            }
    STATUS      current
    DESCRIPTION
            "A pimRPMappingChange notification signifies a change to the
            active RP mapping on this device.

            This notification is generated whenever the counter
            pimRPMappingChangeCount is incremented, subject to the
            rate limit specified by
            pimRPMappingChangeNotificationPeriod."
    ::= { pimNotifications 4 }

pimInterfaceElection NOTIFICATION-TYPE
    OBJECTS { pimInterfaceAddressType,
              pimInterfaceAddress }
    STATUS      current
    DESCRIPTION
            "A pimInterfaceElection notification signifies that a new DR
            or DF has been elected on a network.

            This notification is generated whenever the counter
            pimInterfaceElectionWinCount is incremented, subject to the
            rate limit specified by
            pimInterfaceElectionNotificationPeriod."
    REFERENCE "RFC 4601 section 4.3.2 and RFC 5015 section 3.5.2"
    ::= { pimNotifications 5 }

--
-- Conformance Information
--

pimMIBConformance OBJECT IDENTIFIER ::= { pimStdMIB 2 }
pimMIBCompliances OBJECT IDENTIFIER ::= { pimMIBConformance 1 }
pimMIBGroups      OBJECT IDENTIFIER ::= { pimMIBConformance 2 }

--
-- Compliance Statements
--

pimMIBComplianceAsm MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
             "The compliance statement for routers which are running
             PIM-SM (Sparse Mode)."
    MODULE  -- this module
    MANDATORY-GROUPS { pimTopologyGroup,
                       pimSsmGroup,
                       pimRPConfigGroup,
                       pimSmGroup }

      GROUP   pimNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimTuningParametersGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimRouterStatisticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimAnycastRpGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimStaticRPPrecedenceGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationObjects
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDiagnosticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDeviceStorageGroup
      DESCRIPTION
          "This group is optional."

    ::= { pimMIBCompliances 1 }

pimMIBComplianceBidir MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers which are running
            Bidir-PIM."
    MODULE  -- this module
    MANDATORY-GROUPS { pimTopologyGroup,
                       pimRPConfigGroup,
                       pimSmGroup,
                       pimBidirGroup }

      GROUP   pimNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimTuningParametersGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimRouterStatisticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimAnycastRpGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimStaticRPPrecedenceGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationObjects
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDiagnosticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDeviceStorageGroup
      DESCRIPTION
          "This group is optional."

    ::= { pimMIBCompliances 2 }

pimMIBComplianceSsm MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers which are running
             PIM SSM (Source Specific Multicast)."
    MODULE  -- this module
    MANDATORY-GROUPS { pimTopologyGroup,
                       pimSsmGroup }

      GROUP   pimNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimTuningParametersGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimRouterStatisticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationObjects
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDiagnosticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDeviceStorageGroup
      DESCRIPTION
          "This group is optional."

    ::= { pimMIBCompliances 3 }

pimMIBComplianceDm MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers which are running
            PIM-DM (Dense Mode)."
    MODULE  -- this module
    MANDATORY-GROUPS { pimTopologyGroup,
                       pimSsmGroup,
                       pimRPConfigGroup,
                       pimSmGroup,
                       pimDmGroup }

      GROUP   pimNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimTuningParametersGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimRouterStatisticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimAnycastRpGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimStaticRPPrecedenceGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationObjects
      DESCRIPTION
          "This group is optional."

      GROUP   pimNetMgmtNotificationGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDiagnosticsGroup
      DESCRIPTION
          "This group is optional."

      GROUP   pimDeviceStorageGroup
      DESCRIPTION
          "This group is optional."

    ::= { pimMIBCompliances 4 }

--
-- Units of Conformance
--

pimTopologyGroup OBJECT-GROUP
    OBJECTS { pimInterfaceAddressType,
              pimInterfaceAddress,
              pimInterfaceGenerationIDValue,
              pimInterfaceDR,
              pimInterfaceDRPriorityEnabled,
              pimInterfaceHelloHoldtime,
              pimInterfaceJoinPruneHoldtime,
              pimInterfaceLanDelayEnabled,
              pimInterfaceEffectPropagDelay,
              pimInterfaceEffectOverrideIvl,
              pimInterfaceSuppressionEnabled,
              pimInterfaceBidirCapable,
              pimNeighborGenerationIDPresent,
              pimNeighborGenerationIDValue,
              pimNeighborUpTime,
              pimNeighborExpiryTime,
              pimNeighborDRPriorityPresent,
              pimNeighborDRPriority,
              pimNeighborLanPruneDelayPresent,
              pimNeighborTBit,
              pimNeighborPropagationDelay,
              pimNeighborOverrideInterval,
              pimNeighborBidirCapable,
              pimNbrSecAddress
            }
    STATUS  current
    DESCRIPTION
            "A collection of read-only objects used to report local PIM
            topology."
    ::= { pimMIBGroups 1 }

pimNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS { pimNeighborLoss }
    STATUS  current
    DESCRIPTION
            "A collection of notifications for signaling important PIM
            events."
    ::= { pimMIBGroups 2 }

pimTuningParametersGroup OBJECT-GROUP
    OBJECTS { pimKeepalivePeriod,
              pimRegisterSuppressionTime,
              pimInterfaceDRPriority,
              pimInterfaceHelloInterval,
              pimInterfaceTrigHelloInterval,
              pimInterfaceJoinPruneInterval,
              pimInterfacePropagationDelay,
              pimInterfaceOverrideInterval,
              pimInterfaceDomainBorder,
              pimInterfaceStubInterface,
              pimInterfaceStatus,
              pimInterfaceStorageType
            }
    STATUS  current
    DESCRIPTION
            "A collection of writeable objects used to configure PIM
            behavior and to tune performance."
    ::= { pimMIBGroups 3 }

pimRouterStatisticsGroup OBJECT-GROUP
    OBJECTS { pimStarGEntries,
              pimStarGIEntries,
              pimSGEntries,
              pimSGIEntries,
              pimSGRptEntries,
              pimSGRptIEntries
            }
    STATUS  current
    DESCRIPTION
            "A collection of statistics global to the PIM router."
    ::= { pimMIBGroups 4 }

pimSsmGroup OBJECT-GROUP
    OBJECTS { pimSGUpTime,
              pimSGPimMode,
              pimSGUpstreamJoinState,
              pimSGUpstreamJoinTimer,
              pimSGUpstreamNeighbor,
              pimSGRPFIfIndex,
              pimSGRPFNextHopType,
              pimSGRPFNextHop,
              pimSGRPFRouteProtocol,
              pimSGRPFRouteAddress,
              pimSGRPFRoutePrefixLength,
              pimSGRPFRouteMetricPref,
              pimSGRPFRouteMetric,
              pimSGSPTBit,
              pimSGKeepaliveTimer,
              pimSGDRRegisterState,
              pimSGDRRegisterStopTimer,
              pimSGRPRegisterPMBRAddressType,
              pimSGRPRegisterPMBRAddress,
              pimSGIUpTime,
              pimSGILocalMembership,
              pimSGIJoinPruneState,
              pimSGIPrunePendingTimer,
              pimSGIJoinExpiryTimer,
              pimSGIAssertState,
              pimSGIAssertTimer,
              pimSGIAssertWinnerAddressType,
              pimSGIAssertWinnerAddress,
              pimSGIAssertWinnerMetricPref,
              pimSGIAssertWinnerMetric
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of PIM
            routers running the PIM SSM (Source Specific Multicast)
            protocol, in PIM mode SM (Sparse Mode)."
    ::= { pimMIBGroups 5 }

pimRPConfigGroup OBJECT-GROUP
    OBJECTS { pimStaticRPRPAddress,
              pimStaticRPPimMode,
              pimStaticRPOverrideDynamic,
              pimStaticRPRowStatus,
              pimStaticRPStorageType,
              pimGroupMappingPimMode,
              pimGroupMappingPrecedence
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support configuration of RPs
            (Rendezvous Points) and Group Mappings."
    ::= { pimMIBGroups 6 }

pimSmGroup OBJECT-GROUP
    OBJECTS { pimStarGUpTime,
              pimStarGPimMode,
              pimStarGRPAddressType,
              pimStarGRPAddress,
              pimStarGPimModeOrigin,
              pimStarGRPIsLocal,
              pimStarGUpstreamJoinState,
              pimStarGUpstreamJoinTimer,
              pimStarGUpstreamNeighborType,
              pimStarGUpstreamNeighbor,
              pimStarGRPFIfIndex,
              pimStarGRPFNextHopType,
              pimStarGRPFNextHop,
              pimStarGRPFRouteProtocol,
              pimStarGRPFRouteAddress,
              pimStarGRPFRoutePrefixLength,
              pimStarGRPFRouteMetricPref,
              pimStarGRPFRouteMetric,
              pimStarGIUpTime,
              pimStarGILocalMembership,
              pimStarGIJoinPruneState,
              pimStarGIPrunePendingTimer,
              pimStarGIJoinExpiryTimer,
              pimStarGIAssertState,
              pimStarGIAssertTimer,
              pimStarGIAssertWinnerAddressType,
              pimStarGIAssertWinnerAddress,
              pimStarGIAssertWinnerMetricPref,
              pimStarGIAssertWinnerMetric,
              pimSGRptUpTime,
              pimSGRptUpstreamPruneState,
              pimSGRptUpstreamOverrideTimer,
              pimSGRptIUpTime,
              pimSGRptILocalMembership,
              pimSGRptIJoinPruneState,
              pimSGRptIPrunePendingTimer,
              pimSGRptIPruneExpiryTimer
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of PIM
            routers running PIM-SM (Sparse Mode).  The groups
            pimSsmGroup and pimRPConfigGroup are also required."
    ::= { pimMIBGroups 7 }

pimBidirGroup OBJECT-GROUP
    OBJECTS { pimInterfaceDFElectionRobustness,
              pimBidirDFElectionWinnerAddressType,
              pimBidirDFElectionWinnerAddress,
              pimBidirDFElectionWinnerUpTime,
              pimBidirDFElectionWinnerMetricPref,
              pimBidirDFElectionWinnerMetric,
              pimBidirDFElectionState,
              pimBidirDFElectionStateTimer
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of PIM
            routers running BIDIR mode.  The groups pimSsmGroup,
            pimSmGroup and pimRPConfigGroup are also required."
    ::= { pimMIBGroups 8 }

pimAnycastRpGroup OBJECT-GROUP
    OBJECTS { pimAnycastRPSetLocalRouter,
              pimAnycastRPSetRowStatus,
              pimAnycastRPSetStorageType
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of the PIM
            Anycast-RP mechanism."
    ::= { pimMIBGroups 9 }

pimStaticRPPrecedenceGroup OBJECT-GROUP
    OBJECTS { pimStaticRPPrecedence }
    STATUS  current
    DESCRIPTION
            "A collection of objects to allow fine control of
            interactions between static RP configuration and
            dynamically acquired group to RP mappings."
    ::= { pimMIBGroups 10 }

pimNetMgmtNotificationObjects OBJECT-GROUP
    OBJECTS { pimInvalidRegisterNotificationPeriod,
              pimInvalidRegisterMsgsRcvd,
              pimInvalidRegisterAddressType,
              pimInvalidRegisterOrigin,
              pimInvalidRegisterGroup,
              pimInvalidRegisterRp,
              pimInvalidJoinPruneNotificationPeriod,
              pimInvalidJoinPruneMsgsRcvd,
              pimInvalidJoinPruneAddressType,
              pimInvalidJoinPruneOrigin,
              pimInvalidJoinPruneGroup,
              pimInvalidJoinPruneRp,
              pimRPMappingNotificationPeriod,
              pimRPMappingChangeCount,
              pimInterfaceElectionNotificationPeriod,
              pimInterfaceElectionWinCount
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support notification of PIM
            network management events."
    ::= { pimMIBGroups 11 }

pimNetMgmtNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS { pimInvalidRegister,
                    pimInvalidJoinPrune,
                    pimRPMappingChange,
                    pimInterfaceElection
                  }
    STATUS  current
    DESCRIPTION
            "A collection of notifications for signaling PIM network
            management events."
    ::= { pimMIBGroups 12 }

pimDiagnosticsGroup OBJECT-GROUP
    OBJECTS { pimInAsserts,
              pimOutAsserts,
              pimLastAssertInterface,
              pimLastAssertGroupAddressType,
              pimLastAssertGroupAddress,
              pimLastAssertSourceAddressType,
              pimLastAssertSourceAddress,
              pimNeighborLossNotificationPeriod,
              pimNeighborLossCount
            }
    STATUS  current
    DESCRIPTION
            "Objects providing additional diagnostics related to a PIM
            router."
    ::= { pimMIBGroups 13 }

pimDmGroup OBJECT-GROUP
    OBJECTS {
              pimRefreshInterval,
              pimInterfacePruneLimitInterval,
              pimInterfaceGraftRetryInterval,
              pimInterfaceSRPriorityEnabled,
              pimNeighborSRCapable,
              pimSGUpstreamPruneState,
              pimSGUpstreamPruneLimitTimer,
              pimSGOriginatorState,
              pimSGSourceActiveTimer,
              pimSGStateRefreshTimer
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects required for management of PIM
            Dense Mode (PIM-DM) function.  The groups pimSsmGroup and
            pimSmGroup are also required."
    REFERENCE "RFC 3973"
    ::= { pimMIBGroups 14 }

pimDeviceStorageGroup OBJECT-GROUP
    OBJECTS { pimDeviceConfigStorageType
            }
    STATUS  current
    DESCRIPTION
            "An object that specifies the volatility of global PIM
            configuration settings on this device."
    ::= { pimMIBGroups 15 }

END
