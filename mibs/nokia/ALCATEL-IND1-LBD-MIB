ALCATEL-IND1-LBD-<PERSON>B DEFINITIONS ::= BEGIN

        IMPORTS
                Unsigned32, Counter32, MODULE-IDENTITY,
                NOTIFICATION-TYPE, OBJECT-TYPE,
                OBJECT-IDENTITY         
					FROM SNMPv2-<PERSON><PERSON>
                MODULE-COMPLIANCE, OBJECT-GROUP
                                        FROM SNMPv2-CONF
                InterfaceIndex          FROM IF-MIB
                softentIND1Lbd, alaLbdTraps
			                FROM ALCATEL-IND1-BASE;


                alcatelIND1LBDMIB MODULE-IDENTITY
                LAST-UPDATED "200812100000Z"
                ORGANIZATION "Alcatel - Architects Of An Internet World"
                CONTACT-INFO
                "Please consult with Customer Service to insure the most appropriate
                version of this document is used with the products in question:

                        Alcatel Internetworking, Incorporated
                        (Division 1, Formerly XYLAN Corporation)
                        26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                        United States Of America

                        Telephone:      North America  ****** 995 2696
                                        Latin America  ****** 919 9526
                                        Europe         +31 23 556 0100
                                        Asia           +65 394 7933
                                        All Other      ****** 878 4507

                Electronic Mail:         <EMAIL>
                World Wide Web:          http://www.ind.alcatel.com
                File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

                DESCRIPTION
                        "This module describes an authoritative enterprise-specific Simple
                        Network Management Protocol (SNMP) Management Information Base (MIB):

                        For the Birds Of Prey Product Line
                        LBD for detection loopback.

                        The right to make changes in specification and other information
                        contained in this document without prior notice is reserved.

                        No liability shall be assumed for any incidental, indirect, special, or
                        consequential damages whatsoever arising from or related to this
                        document or the information contained herein.

                        Vendors, end-users, and other interested parties are granted
                        non-exclusive license to use this specification in connection with
                        management of the products for which it is intended to be used.

                        Copyright (C) 1995-2002 Alcatel Internetworking, Incorporated
                        ALL RIGHTS RESERVED WORLDWIDE"

                REVISION      "200812100000Z"
                DESCRIPTION
                	"The LBD mib defines a set of LBD related management objects for ports
			that support Loop Back Detection(LBD) mechanism. 
			LBD mechanism monitors the physical configuration of cables and detect when 
			a loopback detection exists due to any physical or logical problem. In case 
			such problem, it shall take appropriate measures for the port based upon the 
			configured mode of operation and raise a trap to the user."
                ::= { softentIND1Lbd 1}

-- --------------------------------------------------------------
		alcatelIND1LBDMIBObjects OBJECT-IDENTITY
                STATUS current
                DESCRIPTION
                    	"Branch For Loopback-Detection Subsystem Managed Objects."
		::= { alcatelIND1LBDMIB 1 }
	
		alcatelIND1LBDMIBConformance OBJECT-IDENTITY
                STATUS current
                DESCRIPTION
			"Branch for LBD Module MIB Subsystem Conformance Information."
		::= { alcatelIND1LBDMIB 2 }

		alcatelIND1LBDMIBGroups OBJECT-IDENTITY
                STATUS current
                DESCRIPTION
			"Branch for LBD Module MIB Subsystem Units of Conformance."
		::= { alcatelIND1LBDMIBConformance 1 }  

	 	alcatelIND1LBDMIBCompliances OBJECT-IDENTITY
                STATUS current
                DESCRIPTION
		 	"Branch for LBD Module MIB Subsystem Compliance Statements."
	 	::= { alcatelIND1LBDMIBConformance 2 }

-- --------------------------------------------------------------
-- Texual Convention
-- --------------------------------------------------------------
		AlaLbdPortConfigLbdOperStatus ::= TEXTUAL-CONVENTION
       		STATUS  current
        	DESCRIPTION 
			"Specifies the current operational state of this port."
		SYNTAX  INTEGER {
             	       	inactive (0),  -- Specifies that the operational state is inactive.
                       	normal (1),    -- Specifies that the operational state is normal.
                       	shutdown (2)   -- Specifies that the port is shutdown in state.
                       	}

		AlaLbdCurrentStateCVAorAR ::= TEXTUAL-CONVENTION
        	STATUS  current
        	DESCRIPTION
			"Specifies the state of the port, where LBD was running, after
                	auto-recovery or issuing clear-violation-all."
		SYNTAX  INTEGER {
		 	inactive (0), -- Specifies that the operational state is inactive. 	
			normal (1)    -- Specifies that the state is normal.
                        }
	
-- --------------------------------------------------------------
-- Global LBD Objects
-- LBD - Loop Back Detection
-- --------------------------------------------------------------

	        alaLbdGlobalConfigStatus      OBJECT-TYPE
	        SYNTAX          INTEGER {
        	                enable(1),
                	        disable(2)
                        	}
	        MAX-ACCESS      read-write
        	STATUS          current
	        DESCRIPTION
        	        "This object is used to enable or disable LBD on the
                	switch. The value enable (1) indicates that LBD should be
	                enabled on the switch. The value disable (2) is used to
        	        disable LBD on the switch."
	        DEFVAL  { disable }
        	::= { alcatelIND1LBDMIBObjects 1 }

	        alaLbdGlobalConfigTransmissionTimer     OBJECT-TYPE
        	SYNTAX  Unsigned32 ( 5 .. 600 )
	        UNITS   "seconds"
        	MAX-ACCESS      read-write
	        STATUS  current
        	DESCRIPTION
	            "This object is used to set the transmission timer for LBD
        	    feature globally. It is allowed to modify the timer at any
	            instance but the new timer value will be effective from next
        	    time the timer restarts."
	        DEFVAL  { 30 }
        	::= { alcatelIND1LBDMIBObjects 2 }

	        alaLbdGlobalClearPortStat OBJECT-TYPE
        	SYNTAX  INTEGER {
                	default(0),
	                reset(1)
        	        }
	        MAX-ACCESS      read-write
        	STATUS  current
	        DESCRIPTION
        	        "Defines the global clear statistics control for port for
                	LBD.
	                default(0) - default value for this object,
        	        reset(1)   - indicates that LBD should clear all statistic
	                             counters related to all ports in the system."
	        DEFVAL  { default }
	        ::= { alcatelIND1LBDMIBObjects 3 }

	        alaLbdGlobalConfigAutorecoveryTimer OBJECT-TYPE
        	SYNTAX  Unsigned32 ( 30 .. 86400 )
		UNITS   "seconds"
	        MAX-ACCESS      read-write
		STATUS  current
        	DESCRIPTION
                	"This object is used to make up all the disabled ports which 
			was caused due to loop-back detection." 
	        DEFVAL { 300 }
	        ::= { alcatelIND1LBDMIBObjects 4 }
		
-- -------------------------------------------------------------
-- LBD Config Table on Physical Port 
-- -------------------------------------------------------------
	
		alaLbdPortConfig  OBJECT IDENTIFIER ::= { alcatelIND1LBDMIBObjects 5 }
	      	alaLbdPortConfigTable  OBJECT-TYPE
       	        SYNTAX                SEQUENCE OF AlaLbdPortConfigEntry
                MAX-ACCESS            not-accessible
                STATUS                current
                DESCRIPTION
                	"A table containing LBD user port configuration information."
                ::= { alaLbdPortConfig 1 }

	        alaLbdPortConfigEntry  OBJECT-TYPE
                SYNTAX                AlaLbdPortConfigEntry 
                MAX-ACCESS            not-accessible
                STATUS                current
                DESCRIPTION
                	"A LBD user port configuration entry."
                INDEX { alaLbdPortConfigIfIndex }
                ::= { alaLbdPortConfigTable 1 }

      		AlaLbdPortConfigEntry ::= SEQUENCE {
                	alaLbdPortConfigIfIndex              InterfaceIndex,
                	alaLbdPortConfigLbdAdminStatus       INTEGER,
		  	alaLbdPortConfigLbdOperStatus	     AlaLbdPortConfigLbdOperStatus		
               	}

                alaLbdPortConfigIfIndex       OBJECT-TYPE
                SYNTAX  InterfaceIndex
                MAX-ACCESS  not-accessible
                STATUS  current
            	DESCRIPTION
                	"The physical identification number for this port."
                ::= { alaLbdPortConfigEntry 1 }

	        alaLbdPortConfigLbdAdminStatus OBJECT-TYPE
            	SYNTAX          INTEGER {
                        enable(1),
                        disable(2)
                        }
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "This object is used to enable or disable loopback-detection per port. 
			The value enable(1) indicates that loopback-detection is 
			enabled on this port. The value disable(2) indicates that 
			loopback-detection is disabled on this port.
			The LBD configuration per port can be applied and retained, 
			irrespective of value of alaLbdGlobalConfigStatus, but the same shall 
			be functionally unavailable when alaLbdGlobalConfigStatus is set to
			disable(2)."
		DEFVAL  { disable }
                ::= { alaLbdPortConfigEntry 2 }

		alaLbdPortConfigLbdOperStatus OBJECT-TYPE
                SYNTAX          AlaLbdPortConfigLbdOperStatus
		MAX-ACCESS     read-only
		STATUS          current
                DESCRIPTION
			"This object specifies the current operational state of the corresponding 
			port."
		::= { alaLbdPortConfigEntry 3 }

-- --------------------------------------------------------------
-- Loop Back Detection Statistics Table
-- -- --------------------------------------------------------------
		alaLbdPortStat OBJECT IDENTIFIER ::= { alcatelIND1LBDMIBObjects 6 }
		
		alaLbdPortStatsTable OBJECT-TYPE
       	        SYNTAX  SEQUENCE OF AlaLbdPortStatsEntry
                MAX-ACCESS  not-accessible
                STATUS  current
                DESCRIPTION
			"A table containing Loop Back Detection information."
		::= { alaLbdPortStat 1 }

		alaLbdPortStatsEntry  OBJECT-TYPE
                SYNTAX  AlaLbdPortStatsEntry
                MAX-ACCESS  not-accessible
                STATUS  current
                DESCRIPTION
			"A LBD Statistics entry on per port."
		INDEX { alaLbdPortStatsIfIndex }
		::= { alaLbdPortStatsTable 1 }

		AlaLbdPortStatsEntry ::= SEQUENCE {
			alaLbdPortStatsIfIndex         InterfaceIndex,
			alaLbdPortNumLbdInvalidRcvd	   Counter32,
			alaLbdPortLbdSent		       Counter32,
			alaLbdPortStatsClear           INTEGER
		}

		alaLbdPortStatsIfIndex OBJECT-TYPE
		SYNTAX  InterfaceIndex
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"The ifindex of the port on which LBD is running."
		::= { alaLbdPortStatsEntry 1 }

		alaLbdPortNumLbdInvalidRcvd OBJECT-TYPE
		SYNTAX Counter32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Number of Invalid packets received by a port."
		::= { alaLbdPortStatsEntry 2 }

		alaLbdPortLbdSent OBJECT-TYPE
		SYNTAX Counter32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Number of LBD packets sent by a port."
		::= { alaLbdPortStatsEntry 3 }

		alaLbdPortStatsClear OBJECT-TYPE
                SYNTAX INTEGER {
			default(0),
			reset(1)
			} 
                MAX-ACCESS  read-write
		STATUS  current
                DESCRIPTION
			"When this object is set to reset(1), all statistics parameters 
			corresponding to this port gets cleared (reset)." 
		DEFVAL  { default }
		::= { alaLbdPortStatsEntry 4 }
 
-- --------------------------------------------------------------
-- Trap Description 
-- --------------------------------------------------------------
		alaLbdTrapsDesc  OBJECT IDENTIFIER ::= { alaLbdTraps 1 }
		alaLbdTrapsObj OBJECT IDENTIFIER ::= { alaLbdTraps 2 }

		alaLbdTrapsRoot OBJECT IDENTIFIER ::= { alaLbdTrapsDesc  0 }

                alaLbdStateChangeToShutdown NOTIFICATION-TYPE
                OBJECTS  {
                        alaLbdPortIfIndex,
              		alaLbdPreviousState,
                        alaLbdCurrentState
                        }
                STATUS   current
                DESCRIPTION
                        "When the port state becomes shutdown, a notification is sent
                        to the Management Entity with the LBD-state
                        information."
                ::= { alaLbdTrapsRoot 1 }

	 	alaLbdStateChangeForClearViolationAll NOTIFICATION-TYPE
                OBJECTS  {
			alaLbdPortIfIndex,
			alaLbdPreviousStateClearViolationAll,
		 	alaLbdCurrentStateClearViolationAll
			} 			
		STATUS   current
                DESCRIPTION
			"When the the port state changes from shutdown due to 
			clear-violation-all, a notification is sent to the Management 
			Entity, with the LBD-state information."
		::= { alaLbdTrapsRoot 2 }

		alaLbdStateChangeForAutoRecovery NOTIFICATION-TYPE
                OBJECTS  {
                        alaLbdPortIfIndex,
			alaLbdPreviousStateAutoRecovery,
			alaLbdCurrentStateAutoRecovery
			}
		STATUS   current
		DESCRIPTION
			"When the port state changes from shutdown due to auto-recovery
			mechanism, a notification is sent to the Management Entity
			with the LBD-state information."
		::= { alaLbdTrapsRoot 3 }

-- ----------------------------------------------------------------------
-- Trap Objects
-- -- ----------------------------------------------------------------------
                alaLbdPortIfIndex  OBJECT-TYPE
                SYNTAX  InterfaceIndex
                MAX-ACCESS  accessible-for-notify
                STATUS  current
                DESCRIPTION
                        "The ifIndex of the port on which LBD trap is raised"
                ::= { alaLbdTrapsObj 1 }

		alaLbdPreviousState OBJECT-TYPE
                SYNTAX  INTEGER {
				normal(1)   --  Specifies that the operational state is normal.
				}
		MAX-ACCESS  accessible-for-notify
                STATUS  current
		DESCRIPTION
		 	"The previous state of the port on which LBD was running."
		::= { alaLbdTrapsObj 2 }

		alaLbdCurrentState OBJECT-TYPE
		SYNTAX  INTEGER {
			shutdown (1)	  -- State is shutdown
        		}
		MAX-ACCESS  accessible-for-notify
		STATUS  current
		DESCRIPTION
   			"The current state of the port on which LBD was running."
		::= { alaLbdTrapsObj 3 }

		alaLbdPreviousStateClearViolationAll OBJECT-TYPE  
		SYNTAX  INTEGER {
			shutdown (1)	   -- State is shutdown 
			}
		MAX-ACCESS  accessible-for-notify
                STATUS  current
                DESCRIPTION
			"The state of the the port where LBD was running 
			before clear-violation-all applied."
		::= { alaLbdTrapsObj 4 }

		alaLbdCurrentStateClearViolationAll OBJECT-TYPE
                SYNTAX  AlaLbdCurrentStateCVAorAR
		MAX-ACCESS  accessible-for-notify
                STATUS  current
                DESCRIPTION
                        "The state of the the port where LBD was running
			after clear-violation-all applied."
                ::= { alaLbdTrapsObj 5 }

		alaLbdPreviousStateAutoRecovery OBJECT-TYPE
                SYNTAX  INTEGER {
			shutdown (1)       -- State is shutdown
                        }
                MAX-ACCESS  accessible-for-notify
                STATUS  current
                DESCRIPTION
			"The state of the the port where LBD was running
                        before auto-recovery."
		::= { alaLbdTrapsObj 6 }

		alaLbdCurrentStateAutoRecovery OBJECT-TYPE
                SYNTAX  AlaLbdCurrentStateCVAorAR
		MAX-ACCESS  accessible-for-notify
                STATUS  current
                DESCRIPTION
                        "The state of the the port where LBD was running
			after auto-recovery."
                ::= { alaLbdTrapsObj 7 }

-- -------------------------------------------------------------
-- COMPLIANCE
-- -------------------------------------------------------------
		alcatelIND1LBDMIBCompliance MODULE-COMPLIANCE
   		STATUS    current
		DESCRIPTION
			"Compliance statement for LBD."
   		MODULE
		MANDATORY-GROUPS
		{
			alaLbdGlobalConfigGroup,
			alaLbdIntfConfigGroup,
			alaLbdPortStatusGroup,
			alaLbdStateTrapToShutdownGroup,
			alaLbdStateTrapForClearViolationAllGroup,
			alaLbdStateTrapForAutoRecoveryGroup
		}
   		::= { alcatelIND1LBDMIBCompliances 1 }

-- -------------------------------------------------------------
-- UNITS OF CONFORMANCE
-- -------------------------------------------------------------

		alaLbdGlobalConfigGroup   OBJECT-GROUP
   		OBJECTS
   		{
			alaLbdGlobalConfigStatus,
      			alaLbdGlobalConfigTransmissionTimer,
			alaLbdGlobalClearPortStat,
			alaLbdGlobalConfigAutorecoveryTimer
   		}
  		STATUS  current
  		DESCRIPTION
  			"Collection of objects for management of LBD Global Configuration."
  		::= { alcatelIND1LBDMIBGroups 1 }

		alaLbdIntfConfigGroup   OBJECT-GROUP
   		OBJECTS
   		{
			alaLbdPortConfigLbdAdminStatus,
			alaLbdPortConfigLbdOperStatus	
   		}
  		STATUS  current
  		DESCRIPTION
  		"Collection of objects for management of LBD per port Configuration."
  		::= { alcatelIND1LBDMIBGroups 2 }

		alaLbdPortStatusGroup OBJECT-GROUP
                OBJECTS
                {
			alaLbdPortNumLbdInvalidRcvd,
			alaLbdPortLbdSent,
			alaLbdPortStatsClear
		}
		STATUS  current
		DESCRIPTION
			"Collection of objects for LBD Statistics information."
		::= { alcatelIND1LBDMIBGroups 3 }
	
		alaLbdStateTrapToShutdownGroup OBJECT-GROUP
   		OBJECTS
   		{
			alaLbdPreviousState,
			alaLbdCurrentState
   		}
  		STATUS  current
  		DESCRIPTION
  			"Collection of objects for LBD State change to shutdown Traps ."
  		::= { alcatelIND1LBDMIBGroups 4 }

		alaLbdStateTrapForClearViolationAllGroup OBJECT-GROUP
                OBJECTS
                {
			alaLbdPreviousStateClearViolationAll,
			alaLbdCurrentStateClearViolationAll
		}
		STATUS  current
                DESCRIPTION
                        "Collection of objects for LBD State Traps due to
			issuing clear-violation-all."
		::= { alcatelIND1LBDMIBGroups 5 }

		alaLbdStateTrapForAutoRecoveryGroup OBJECT-GROUP
                OBJECTS
		{
			alaLbdPreviousStateAutoRecovery,
			alaLbdCurrentStateAutoRecovery
		}
		STATUS  current
                DESCRIPTION
                        "Collection of objects for LBD State Traps due to
			auto-recovery mechanism."
		::= { alcatelIND1LBDMIBGroups 6 }
			
-- -------------------------------------------------------------

END
 
