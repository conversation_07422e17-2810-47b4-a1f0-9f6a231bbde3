ALCATEL-IND1-INLINE-POWER-MIB DEFINITIONS ::= BEGIN

IMPORTS
	MODULE-IDENTITY, OBJECT-TYPE, Integer32, NOTIFICATION-TYPE
		FROM SNMPv2-SMI
	pethPsePortEntry, pethMainPseEntry
		FROM POWER-ETHERNET-MIB
        MODULE-COMPLIANCE, OBJECT-GROUP
		FROM SNMPv2-CONF
	softentIND1InLinePower, pethTraps
		FROM ALCATEL-IND1-BASE;

alcatelIND1INLINEPOWERMIB MODULE-IDENTITY
    LAST-UPDATED "200704030000Z" 
    ORGANIZATION "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:
         
                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America
        
        Telephone:               North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507
        
        Electronic Mail:         <EMAIL>
        World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
        File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):
         
             Propietary InLinePower Extensions MIB definitions
         
         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.
         
         No liability shall be assumed for any incidental, indirect, special,
	 or consequential damages whatsoever arising from or related to this
         document or the information contained herein.
         
         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.
         
                     Copyright (C) 1995-2007 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "200704030000Z"
    DESCRIPTION
        "The latest version of this MIB Module."

    ::= { softentIND1InLinePower 1 }

   alaPethObjects       OBJECT IDENTIFIER ::= { alcatelIND1INLINEPOWERMIB 1 }
   alaPethConformance   OBJECT IDENTIFIER ::= { alcatelIND1INLINEPOWERMIB 2 }
   alaPethMain       	OBJECT IDENTIFIER ::= { alcatelIND1INLINEPOWERMIB 3 }

   -- PSE Objects

     alaPethPsePortTable OBJECT-TYPE
          SYNTAX      SEQUENCE OF AlaPethPsePortEntry
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
              "A table of objects that augments the standard pethPsePortTable
              entry by adding the alaPethPsePortPowerMaximum object."
          ::= { alaPethObjects 1 }

      alaPethPsePortEntry OBJECT-TYPE
          SYNTAX      AlaPethPsePortEntry
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
                  "A set of objects that display and control the power
                  characteristics of a power Ethernet PSE port."
          AUGMENTS  { pethPsePortEntry }  
          ::= { alaPethPsePortTable 1 }


      AlaPethPsePortEntry ::= SEQUENCE {
           alaPethPsePortPowerMaximum
                INTEGER,
           alaPethPsePortPowerActual
                INTEGER,
           alaPethPsePortPowerStatus
                INTEGER,
	   alaPethPsePortPowerClass
		INTEGER
      }

       alaPethPsePortPowerMaximum OBJECT-TYPE
       SYNTAX INTEGER ( 3000 .. 20000 )   
       MAX-ACCESS read-write
       STATUS current
       DESCRIPTION
           "This object controls maximum amount of power per port." 
       ::= { alaPethPsePortEntry 1 }

       alaPethPsePortPowerActual OBJECT-TYPE
       SYNTAX INTEGER ( 0 .. 30000 ) 
       MAX-ACCESS read-only
       STATUS current
       DESCRIPTION
           "This object contains the actual amount of power used by a port" 
       ::= { alaPethPsePortEntry 2 }

       alaPethPsePortPowerStatus OBJECT-TYPE
       SYNTAX INTEGER   {
                  powerOn(1),
                  powerOff(2)
       	   }
       MAX-ACCESS read-only
       STATUS current
       DESCRIPTION
           "This object contains the power status of a port" 
       ::= { alaPethPsePortEntry 3 }

       alaPethPsePortPowerClass OBJECT-TYPE
       SYNTAX INTEGER  {
                class0 (0),
                class1 (1),
                class2 (2),
                class3 (3),
                class4 (4),
                class5 (5)
                }
       MAX-ACCESS read-only
       STATUS current
       DESCRIPTION
           "This object controls class of poe device attached on a port.
           class0 - The device can draw PD power from 0.44W to 12.95W and
                    current less than 5.0 mA.
           class1 - The device can draw PD power from 0.44W to 3.84W and
                    current 10.5 mA.
           class2 - The device can draw PD power from 3.84W to 6.49W and
                    current 12.95 mA.
           class3 - The device can draw PD power from 6.49W to 12.95W and
                    current 28 mA.
           class4 - This class is reserved for future use.
           class5 - No Powered Device is connected to this POE port, or the
                                port is powered down."
       ::= { alaPethPsePortEntry 4 }

   -- Main PSE Objects

   alaPethMainPseTable OBJECT-TYPE
          SYNTAX      SEQUENCE OF AlaPethMainPseEntry
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
              "This table augments the standard table PethMainPseTable by allowing the 
              admin status for a group to be set to on/off as well as
              showing the values for the max power for that group."
          ::= { alaPethObjects 2 }

      alaPethMainPseEntry OBJECT-TYPE
          SYNTAX      AlaPethMainPseEntry
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
               "A set of objects that display and control the Main power
                of a PSE. "
          AUGMENTS  { pethMainPseEntry }  
          ::= { alaPethMainPseTable 1 }

      AlaPethMainPseEntry ::= SEQUENCE {
          alaPethMainPseAdminStatus
              INTEGER,
          alaPethMainPseMaxPower 
              Integer32,
          alaPethMainPsePriorityDisconnect
              INTEGER,
          alaPethMainPseCapacitorDetect
              INTEGER,
          alaPethMainPsePriority
              INTEGER,
          alaPethMainPseComboPort
              INTEGER
      }

        alaPethMainPseAdminStatus OBJECT-TYPE
          SYNTAX INTEGER   {
                  on(1),
                  off(2)
             }
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
                  "The object is used to set the status of the main PSE to ON or OFF."
          ::= { alaPethMainPseEntry 1 }

        alaPethMainPseMaxPower OBJECT-TYPE
          SYNTAX      Integer32  (36..800)
          UNITS      "Watts"
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
                  "The maximum amount of power allowed for main PSE." 
          ::= { alaPethMainPseEntry 2 }
          
        alaPethMainPsePriorityDisconnect OBJECT-TYPE
          SYNTAX INTEGER   {
                  enable(1),
                  disable(2)
             }
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
                  "The object is used to set the priority disconnect of the
				   main PSE to Enabled or Disable."
          ::= { alaPethMainPseEntry 3 }
          
        alaPethMainPseCapacitorDetect OBJECT-TYPE
          SYNTAX INTEGER   {
                  enable(1),
                  disable(2)
             }
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
                  "The object is used to enable or disable the PowerDsine 
				   Capacitor detection method." 
          ::= { alaPethMainPseEntry 4 }
          
        alaPethMainPsePriority OBJECT-TYPE
          SYNTAX INTEGER   {
                  critical(1),
                  high(2),
                  low(3)
             }
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
                  "The object is set the power down priority for the slot"
          ::= { alaPethMainPseEntry 5 }

        alaPethMainPseComboPort OBJECT-TYPE
          SYNTAX INTEGER   {
                  enable(1),
                  disable(2)
             }
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
                  "The object is used to set the combo port option of the
                  main PSE to Enable or Disable."
          ::= { alaPethMainPseEntry 6 }
          
   --
   -- Conformance Section
   --
   alaPethCompliances OBJECT IDENTIFIER ::= { alaPethConformance 1 }
   alaPethGroups      OBJECT IDENTIFIER ::= { alaPethConformance 2 }

   alaPethCompliance MODULE-COMPLIANCE
       STATUS  current
       DESCRIPTION
               "Describes the requirements for conformance to the
               Alcatel Power Ethernet MIB."
       MODULE  -- this module
           GROUP   alaPethPsePortGroup
           DESCRIPTION
               "The alaPethPsePortGroup is mandatory for systems which
               implement PSE ports."
           GROUP   alaPethMainPseGroup
           DESCRIPTION
               "The pethMainPseGroup is mandatory for systems which
               implement main power supply within a PSE Device."
   ::= { alaPethCompliances 1 }

   alaPethPseCompliance MODULE-COMPLIANCE
       STATUS  current
       DESCRIPTION
               "Describes the requirements for conformance to the PSE"
       MODULE  -- this module
       MANDATORY-GROUPS {alaPethPsePortGroup, alaPethMainPseGroup}
        ::= { alaPethCompliances 2 }

   alaPethPsePortGroup OBJECT-GROUP
       OBJECTS {
          alaPethPsePortPowerMaximum,
	  alaPethPsePortPowerActual,
          alaPethPsePortPowerStatus,
          alaPethPsePortPowerClass
       }
       STATUS  current
       DESCRIPTION
             "The pethPsePortGroup is mandatory for systems which
             implement PSE ports."
       ::= { alaPethGroups 1 }

   alaPethMainPseGroup OBJECT-GROUP
       OBJECTS {
		alaPethMainPseAdminStatus,
          	alaPethMainPseMaxPower,
                alaPethMainPsePriorityDisconnect,
        	alaPethMainPseCapacitorDetect,
        	alaPethMainPsePriority,
		alaPethMainPseComboPort
       }
       STATUS  current
       DESCRIPTION
               "Main PSE Objects. "
       ::= { alaPethGroups 2 }

   -- Peth Main

     alaPethMainTable OBJECT-TYPE
          SYNTAX      SEQUENCE OF AlaPethMainEntry
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
              "A table of objects that augments the standard pethPsePortTable
              entry by adding the alaPethPsePortPowerMaximum object."
          ::= { alaPethMain 1 }

      alaPethMainEntry OBJECT-TYPE
          SYNTAX      AlaPethMainEntry
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
                  "A set of objects that display and control the power
                  characteristics of a power Ethernet PSE port."
	      INDEX { alaPethMainIndex }
          ::= { alaPethMainTable 1 }

      AlaPethMainEntry ::= SEQUENCE {
           alaPethMainIndex
             Integer32,
           alaPethMainPowerRedundancy
                INTEGER
      }
        alaPethMainIndex OBJECT-TYPE
          SYNTAX       Integer32 (1..2147483647)
          MAX-ACCESS  not-accessible
          STATUS      current
          DESCRIPTION
              "This variable uniquely identifies the group to which
              power Ethernet PSE is connected.Group means (box in the stack,
              module in a rack) and  the value 1 MUST be  used for non-modular
              devices "
          ::= { alaPethMainEntry  1 }

       alaPethMainPowerRedundancy OBJECT-TYPE
	   SYNTAX INTEGER   {
                  enable(1),
                  disable(2)
       }
       MAX-ACCESS read-write
       STATUS current
       DESCRIPTION
           "This object controls power supply redundancy." 
       ::= { alaPethMainEntry  2 }


   -- Notification Objects

   alaPethNotificationObjects OBJECT IDENTIFIER ::= { alaPethObjects 3 }

	pethSourceSlot  OBJECT-TYPE
	    SYNTAX  INTEGER  (1..64)
	    MAX-ACCESS  read-only
	    STATUS  current
	    DESCRIPTION
		    "Slot number of generating entity."
          ::= { alaPethNotificationObjects 1 }

	pethSourcePort  OBJECT-TYPE
	    SYNTAX  INTEGER  (1..48)
	    MAX-ACCESS  read-only
	    STATUS  current
	    DESCRIPTION
		    "Port number of generating entity."
          ::= { alaPethNotificationObjects 2 }

   --  NOTIFICATIONS

	pethPwrSupplyConflict  NOTIFICATION-TYPE
	   OBJECTS {
	       pethSourceSlot
                   }
	   STATUS   current
	   DESCRIPTION 
	       "Power supply type conflict trap."
	    ::= { pethTraps 0 1 }

	pethPwrSupplyNotSupported  NOTIFICATION-TYPE
	   OBJECTS {
	       pethSourceSlot
                   }
	   STATUS   current
	   DESCRIPTION 
	       "Power supply not supported trap."
	    ::= { pethTraps 0 2 }


END
