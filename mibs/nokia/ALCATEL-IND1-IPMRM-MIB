ALCATEL-IND1-IPMRM-MIB DEFINITIONS ::= BEGIN

IMPORTS
	MODULE-IDENTITY, OBJECT-TYPE,
	Integer32
		FROM SNMPv2-SMI
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM SNMPv2-CONF
	routingIND1Ipmrm
		FROM ALCATEL-IND1-BASE;

alcatelIND1IPMRMMIB MODULE-IDENTITY

    LAST-UPDATED  "200704030000Z"
    ORGANIZATION  "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:

                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America

        Telephone:               North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507

        Electronic Mail:         <EMAIL>
        World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
        File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):

             This proprietary MIB contains management information for
             the configuration of IPMRM (IP Multicast Route Manager) 
             global configuration parameters.

         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.

         No liability shall be assumed for any incidental, indirect, special, or
         consequential damages whatsoever arising from or related to this
         document or the information contained herein.

         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.

                     Copyright (C) 1995-2007 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "200704030000Z"
    DESCRIPTION
        "The latest version of this MIB Module."

    ::= { routingIND1Ipmrm 1 }


alcatelIND1IPMRMMIBObjects OBJECT IDENTIFIER ::= { alcatelIND1IPMRMMIB 1 }

alaIpmrmDebugConfig  OBJECT IDENTIFIER ::= { alcatelIND1IPMRMMIBObjects 1 }

-- ************************************************************************
--  IPMRM Debug Configuration
-- ************************************************************************

alaIpmrmDebugLevel     OBJECT-TYPE
    SYNTAX     Integer32 (0..255)
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { 0 }
    ::= {alaIpmrmDebugConfig 1}

alaIpmrmDebugError     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugError
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 2}

alaIpmrmDebugFib     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugFib
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 3}

alaIpmrmDebugAging     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugAging
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable } 
    ::= {alaIpmrmDebugConfig 4}

alaIpmrmDebugProtos     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugProtos
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 5}

alaIpmrmDebugIpms     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugIpms
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 6}

alaIpmrmDebugMip     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugMip
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 7}

alaIpmrmDebugInit     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugInit
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 8}

alaIpmrmDebugTm     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugTm
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 9}

alaIpmrmDebugMisc     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugMisc
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 10}

alaIpmrmDebugAll     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
               "This Object is deprecated in favour of alaDrcTmIpmrmDebugAll
                MIB Object of alaDrcTmIpmrmDebug Configuration"
    DEFVAL     { disable }
    ::= {alaIpmrmDebugConfig 11}

-- conformance information

alcatelIND1IPMRMMIBConformance OBJECT IDENTIFIER ::= { alcatelIND1IPMRMMIB 2 }
alcatelIND1IPMRMMIBCompliances OBJECT IDENTIFIER ::=
                                          { alcatelIND1IPMRMMIBConformance 1 }
alcatelIND1IPMRMMIBGroups      OBJECT IDENTIFIER ::=
                                          { alcatelIND1IPMRMMIBConformance 2 }

-- units of conformance

alaIpmrmCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers running IP Multicast
            Routing and implementing the ALCATEL-IND1-IPMRM MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { alaIpmrmDebugMIBGroup }

    ::= { alcatelIND1IPMRMMIBCompliances 1 }

alaIpmrmDebugMIBGroup OBJECT-GROUP
    OBJECTS { alaIpmrmDebugLevel, alaIpmrmDebugError,
              alaIpmrmDebugFib, alaIpmrmDebugAging,
              alaIpmrmDebugProtos, alaIpmrmDebugIpms,
              alaIpmrmDebugMip, alaIpmrmDebugInit,
              alaIpmrmDebugTm, alaIpmrmDebugMisc,
              alaIpmrmDebugAll
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to provide debugging
             support of the IP Multicast Route Manager."
    ::= { alcatelIND1IPMRMMIBGroups 2 }



END
