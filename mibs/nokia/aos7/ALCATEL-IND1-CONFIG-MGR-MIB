ALCATEL-IND1-CONFIG-MGR-MIB DEFINITIONS ::= BEGIN


        IMPORTS
                OBJECT-TYPE,
                MODULE-IDENTITY,
                OBJECT-IDENTITY,
                Integer32               FROM SNMPv2-SMI
                SnmpAdminString         FROM SNMP-FRAMEWORK-<PERSON><PERSON>
                MODULE-COMPLIANCE,
                OBJECT-GROUP            FROM SNMPv2-CONF
                softentIND1Confmgr      FROM ALCATEL-IND1-BASE
                VirtualOperChassisId    FROM ALCATEL-IND1-VIRTUAL-CHASSIS-MIB
                ;



        alcatelIND1ConfigMgrMIB   MODULE-IDENTITY
                LAST-UPDATED     "200704030000Z"
                ORGANIZATION     "Alcatel-Lucent"
                CONTACT-INFO
                        "Please consult with Customer Service to ensure the most appropriate
                         version of this document is used with the products in question:

                         Alcatel-Lucent, Enterprise Solutions Division
                         (Formerly Alcatel Internetworking, Incorporated)
                               26801 West Agoura Road
                              Agoura Hills, CA  91301-5122
                              United States Of America

                         Telephone:        North America  ****** 995 2696
                                           Latin America  ****** 919 9526
                                           Europe         +31 23 556 0100
                                           Asia           +65 394 7933
                                           All Other      ****** 878 4507

                         Electronic Mail:         <EMAIL>
                         World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
                         File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

        DESCRIPTION
            "This module describes an authoritative enterprise-specific Simple
             Network Management Protocol (SNMP) Management Information Base (MIB):

                 For the Birds Of Prey Product Line
                 Configuration Manager Subsystem.

             The right to make changes in specification and other information
             contained in this document without prior notice is reserved.

             No liability shall be assumed for any incidental, indirect, special, or
             consequential damages whatsoever arising from or related to this
             document or the information contained herein.

             Vendors, end-users, and other interested parties are granted
             non-exclusive license to use this specification in connection with
             management of the products for which it is intended to be used.

                         Copyright (C) 1995-2007 Alcatel-Lucent
                             ALL RIGHTS RESERVED WORLDWIDE"

        REVISION         "200704030000Z"
        DESCRIPTION
                "The latest version of this MIB Module."

        ::= { softentIND1Confmgr 1}


        alcatelIND1ConfigMgrMIBObjects  OBJECT-IDENTITY
                STATUS  current
                DESCRIPTION
                    "Branch For Configuration Manager Subsystem Managed Objects."
        ::= { alcatelIND1ConfigMgrMIB 1 }


        alcatelIND1ConfigMgrMIBConformance  OBJECT-IDENTITY
                STATUS  current
                DESCRIPTION
                    "Branch For Configuration Manager Subsystem Conformance Information."
        ::= { alcatelIND1ConfigMgrMIB 2 }


        alcatelIND1ConfigMgrMIBGroups  OBJECT-IDENTITY
                STATUS  current
                DESCRIPTION
                    "Branch For Configuration Manager Subsystem Units Of Conformance."
        ::= { alcatelIND1ConfigMgrMIBConformance 1 }


        alcatelIND1ConfigMgrMIBCompliances  OBJECT-IDENTITY
                STATUS  current
                DESCRIPTION
                    "Branch For Configuration Manager Subsystem Compliance Statements."
        ::= { alcatelIND1ConfigMgrMIBConformance 2 }


--
-- Configuration Manager
--

        configManager   OBJECT IDENTIFIER ::= { alcatelIND1ConfigMgrMIBObjects 1 }


        configFileName          OBJECT-TYPE
                SYNTAX          SnmpAdminString (SIZE (0..45))
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "The name of the configuration file to be
                         read by the device. The file specified
                         should be present on the device."
                DEFVAL  { "" }
                ::= { configManager 1 }


        configFileAction        OBJECT-TYPE
                SYNTAX          INTEGER {
                                none(1),
                                checkSyntaxOnly(2),
                                apply(3)
                                }
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "Triggers application of the configuration
                         file specified by the configInputFileName.
                         A value of checkSyntax causes configInputFile
                         to be checked for syntax, with no changes to
                         device configuration. A value of
                         checkSyntaxAndApply causes the configInputFile
                         to be applied to the device configuration.A value
                         of none indicates no action has been triggered."
                DEFVAL  { none }
                ::= { configManager 2 }


        configErrorFileName     OBJECT-TYPE
                SYNTAX          SnmpAdminString (SIZE (0..255))
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The name of the file containing the error
                         messages generated by the device during file
                         configuration."
                DEFVAL  { "" }
                ::= { configManager 3 }


        configFileStatus        OBJECT-TYPE
                SYNTAX          INTEGER {
                                        noneAvail(1),
                                        inProgress(2),
                                        completeNoErrors(3),
                                        completeErrors(4)
                                        }
                MAX-ACCESS              read-only
                STATUS          current
                DESCRIPTION
                        "Status of most recent application of configuration
                         file to the device."
                DEFVAL  { noneAvail }
                ::= { configManager 4 }


        configFileMode          OBJECT-TYPE
                SYNTAX          INTEGER {
                                none(1),
                                verbose(2)
                                }
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "The verbose mode displays all configuration commands
                         and errors when the file is applied on the console
                         screen.  Ignored unless set from command line interface."
                DEFVAL  { none }
                ::= { configManager 5 }


        configTimerFileName     OBJECT-TYPE
                SYNTAX          SnmpAdminString (SIZE (0..45))
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "The name of the configuration file to be read later
                         by the device."
                DEFVAL  { "" }
                ::= { configManager 6 }


        configTimerFileTime     OBJECT-TYPE
                SYNTAX          SnmpAdminString (SIZE (0..16))
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "The local time when the configuration file
                         will be read by the device. This object is
                         only set if the configInputFile must be applied
                         at a later time.
                         The following formats are valid:

                          MM/DD/YYYY hh:mm

                          MM/DD/YY hh:mm

                          MM/DD hh:mm -  uses current year if
                                         after current date and time,
                                         next year otherwise.

                          hh:mm -  uses today's date if later
                                   than current time,
                                   tommorow's date otherwise.
                      where
                        YY - year   (0-38,99)
                        MM - month  (1-12)
                        DD - day    (1-31)
                        hh - hour   (0-23)
                        mm - minute (0-59)"
                DEFVAL  { "" }
                ::= { configManager 7 }


        configTimerFileStatus   OBJECT-TYPE
                SYNTAX          INTEGER {
                                idle(1),
                                pending(2),
                                inProgress(3)
                                }
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The current status of the ASCII configuration
                         timer."
                DEFVAL  { idle }
                ::= { configManager 8 }


        configTimerClear        OBJECT-TYPE
                SYNTAX          Integer32 (0..1)
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "A set of this object causes the ASCII
                         configuration timer to be cleared."
                DEFVAL  { 0 }
                ::= { configManager 9 }


        configSnapshotFileName  OBJECT-TYPE
                SYNTAX          SnmpAdminString (SIZE (0..45))
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "The name of the configuration snap-shot file
                         to be generated by the device."
                DEFVAL  { "" }
                ::= { configManager 10 }


        configSnapshotAction    OBJECT-TYPE
                SYNTAX          Integer32 (0..1)
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "A set of this object causes the configuration
                         snap-shot to be generated by the device."
                DEFVAL  { 0 }
                ::= { configManager 11 }


        configSnapshotAllSelect                 OBJECT-TYPE
                SYNTAX                          Integer32 (0..1)
                MAX-ACCESS                      read-write
                STATUS                          current
                DESCRIPTION
                        "A set of this object causes
                         all application configuration data to be
                         included in the snap-shot file generated by
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 12 }


        configSnapshotVlanSelect                OBJECT-TYPE
                SYNTAX                          Integer32 (0..1)
                MAX-ACCESS                      read-write
                STATUS                          current
                DESCRIPTION
                        "A set of this object causes
                         Vlan configuration data to be
                         included in the snap-shot file generated by
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 13 }


        configSnapshotSpanningTreeSelect        OBJECT-TYPE
                SYNTAX                          Integer32 (0..1)
                MAX-ACCESS                      read-write
                STATUS                          current
                DESCRIPTION
                        "A set of this object causes
                         Spanning Tree configuration data to be
                         included in the snap-shot file generated by
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 14 }


        configSnapshotQOSSelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         QOS configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 15 }


        configSnapshotIPSelect          OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         IP configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 16 }


        configSnapshotIPXSelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         IPX configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 17 }


        configSnapshotIPMSSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         IPMS configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 18 }

        configSnapshotAAASelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         AAA configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 19 }


        configSnapshotSNMPSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         SNMP configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 20 }


        configSnapshot8021QSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         802.1Q configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 21 }


        configSnapshotLinkAggregateSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         link aggregation configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 22 }


        configSnapshotPortMirrorSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         port mirroring configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 23 }


        configSnapshotXIPSelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         proprietary inter-switch protocol configuration
                         data to be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 24 }


        configSnapshotHealthMonitorSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         health monitoring configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 25 }


        configSnapshotBootPSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         bootp configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 26 }


        configSnapshotBridgeSelect      OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         source-learning configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 27 }


        configSnapshotChassisSelect     OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         the chassis configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 28 }


        configSnapshotInterfaceSelect   OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         ethernet interface configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 29 }


        configSnapshotPolicySelect      OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         policy manager configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 30 }


        configSnapshotSessionSelect     OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         the user-session configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 31 }


        configSnapshotServerLoadBalanceSelect   OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         server load balancing configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 32 }


        configSnapshotSystemServiceSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         system services configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 33 }


        configSnapshotVRRPSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         VRRP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 34 }


        configSnapshotWebSelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         web manager configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 35 }


        configSnapshotRIPSelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         RIP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 36 }


        configSnapshotOSPFSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         OSPF configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 37 }


        configSnapshotBGPSelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         BGP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 38 }


        configSnapshotIPRMSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         IPRM routing configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 39 }


        configSnapshotIPMRSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         IP multicast routing configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 40 }


        configSnapshotModuleSelect      OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         module configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 41 }

        configTechSupportLogAction      OBJECT-TYPE
                SYNTAX                  INTEGER {
                                           notSignificant(0),
                                           techSupportBasic(1),
                                           techSupportL2(2),
                                           techSupportL3(3),
                                           techSupportL3Rip(4),
                                           techSupportL3Ipx(5),
                                           techSupportL3Ospf(6),
                                           techSupportL3Bgp(7),
                                           techSupportL3Pimsm(8),
                                           techSupportL3Mroute(9),
                                           techSupportL3Dvmrp(10),
                                           techSupportL3IPv6(11),
                                           techSupportL3RIPng(12),
                                           techSupportL3OSPF3(13),
                                           techSupportL3Isis(14),
                                           techSupportL3Pim6(15),
                                           techSupportL3IPsec(16),
                                           techSupportL3Bfd(17)

                                }
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         a tech_support.log file to be created
                         containing switch information useful to
                         a technical support group."
                DEFVAL  { techSupportBasic }
                ::= { configManager 42 }

        configWriteMemory               OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         the entire switch snap-shot configuration to
                         be stored in the boot.cfg file in the
                         current running directory."
                DEFVAL  { 0 }
                ::= { configManager 43 }

        configErrorFileMaximum          OBJECT-TYPE
                SYNTAX                  Integer32 (1..25)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "The maximum number of error files which may be
                         present on a switch.  Each time a new error file
                         is created, all but the newest configErrorFileMaxNum
                         error files are deleted."
                DEFVAL  { 1 }
                ::= { configManager 44 }

        configChangeStatus      OBJECT-TYPE
                SYNTAX          INTEGER {
                                identical(1),
                                different(2)
                                }
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The current state of the running configuration with respect
                         to the saved configuration."
                DEFVAL  { identical }
                ::= { configManager 45 }

        configSnapshotRDPSelect         OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         RDP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 46 }

        configSnapshotIPv6Select        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         IPv6 configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 47 }

        configSnapshotRIPngSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         RIPng configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 48 }

        configSnapshotAtmSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         ATM configuration data to
                         be included in the snap-shot file generated
                         by the device. NOT SUPPORTED."
                DEFVAL  { 0 }
                ::= { configManager 49 }


        configSnapshotSonetSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Sonet configuration data to
                         be included in the snap-shot file generated
                         by the device. NOT SUPPORTED."
                DEFVAL  { 0 }
                ::= { configManager 50 }

        configSnapshotNTPSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         NTP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 51 }


        configSnapshotPortMappingSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Port Mapping configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 52 }


        configSnapshotOSPF3Select       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         OSPF3 configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 53 }


        configWriteMemoryStatus OBJECT-TYPE
                SYNTAX          INTEGER {
                                        noneAvail(1),
                                        inProgress(2),
                                        completeNoErrors(3),
                                        completeErrors(4)
                                        }
                MAX-ACCESS              read-only
                STATUS          current
                DESCRIPTION
                        "Status of most recent wwrite memory action."
                DEFVAL  { noneAvail }
                ::= { configManager 54 }


        configSnapshotStackSelect       OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         STack Manager configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 55 }

       configSnapshotISISSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         ISIS configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 56 }


        configSnapshotEOAMSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Ethernet OAM configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 57 }


        configSnapshotUDLDSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         UDLD configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 58 }

        configSnapshotNETSECSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         NETSEC configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 59 }

        configSnapshotIPsecSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         IP Security configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 60 }

        configSnapshotBFDSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         BFD configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 61 }

        configSnapshotMultiChassisSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         MCM configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 62 }

        configSnapshotErpSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         ERP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 63 }

        configSnapshotMPLSSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         MPLS configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 64 }

        configSnapshotEFMOAMSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         EFMOAM configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 65}

        configSnapshotCapabilitySelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         capability manager configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 66 }

        configSnapshotVfcSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         VFC configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 67 }

        configSnapshotHaVlanSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         HA VLAN configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 68 }

        configSnapshotDaUnpSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         DA UNP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 69 }

        configSnapshotDHLSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         DHL configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 70 }

        configSnapshotMVRPSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         MVRP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 71 }

        configSnapshotSAASelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         SAA configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 72 }

        configSnapshotSPBSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         SPB configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 73 }

        configSnapshotSPBIsisSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         SPB ISIS configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 74 }

        configSnapshotVirtualChassisSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes VCM configuration
                         data to be included in the snap-shot file
                         generated by the device.  A set of this object
                         is only valid in standalone mode"
                DEFVAL  { 0 }
                ::= { configManager 75 }

        configSnapshotMplsLdpSelect        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         MPLS LDP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 76 }               

        configSnapshotVCMSpecific        OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         VCM configuration data for the chassis 
                         specified by configSnapshotChassisId to
                         be included in the snap-shot file generated
                         by the device  A set of this object is only
                         valid in virtual chassis mode."
                DEFVAL  { 0 }
                ::= { configManager 77 }

        configSnapshotChassisId        OBJECT-TYPE
                SYNTAX                  VirtualOperChassisId
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object in combination with
                         configSnapshotVCMSpecific causes
                         VCM configuration data for the specified
                         chassis to be included in the snap-shot
                         file generated by the device.  A set of
                         this object is only valid in virtual
                         chassis mode."
                DEFVAL  { 0 }
                ::= { configManager 78 }

        configSnapshotEvbSelect            OBJECT-TYPE
                  SYNTAX                  Integer32 (0..1)
                  MAX-ACCESS              read-write
                  STATUS                  current
                  DESCRIPTION
                          "A set of this object causes
                           EVB configuration data to
                           be included in the snap-shot file generated
                           by the device."
                  DEFVAL  { 0 }
                  ::= { configManager 79 }

        configConvertConfiguration        OBJECT-TYPE
                SYNTAX          INTEGER {
                                notSignificant(0),
                                virtualChassis(1)
                                }
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "A set of this object to virtualChassis is only valid in
                         standalone mode and causes a virtual chassis snapshot to be
                         generated from the running configuration.  The resulting
                         vcsetup.cfg and vcboot.cfg files are stored in the directory
                         specified by configConvertDestinationDirectory. If no directory
                         is specified the running directory will be used provided the
                         running directory is not the certified directory."
                DEFVAL  { notSignificant }
                ::= { configManager 80 }

        configConvertConfigurationStatus        OBJECT-TYPE
                SYNTAX          INTEGER {
                                        noneAvail(1),
                                        inProgress(2),
                                        completeNoErrors(3),
                                        completeErrors(4)
                                        }
                MAX-ACCESS              read-only
                STATUS          current
                DESCRIPTION
                        "Status of most recent convert configuration action."
                DEFVAL  { noneAvail }
                ::= { configManager 81 }

        configConvertDestinationDirectory        OBJECT-TYPE
                SYNTAX          SnmpAdminString (SIZE (0..45))
                MAX-ACCESS      read-write
                STATUS          current
                DESCRIPTION
                        "The destination directory for the files created by the action  
                         of setting the configConvertConfiguration object."
                DEFVAL  { "" }
                ::= { configManager 82 }

        configConvertReload        OBJECT-TYPE
                  SYNTAX          Integer32 (0..1)
                  MAX-ACCESS      read-write
                  STATUS          current
                  DESCRIPTION
                          "A set of this object will initiate a reboot of the
                           switch as part of the configConvertConfiguration
                           action."
                  DEFVAL  { 0 }
                  ::= { configManager 83 }

        configSnapshotAppfpSelect            OBJECT-TYPE
                  SYNTAX                  Integer32 (0..1)
                  MAX-ACCESS              read-write
                  STATUS                  current
                  DESCRIPTION
                          "A set of this object causes
                           Application Finger Print configuration data to
                           be included in the snap-shot file generated
                           by the device."
                  DEFVAL  { 0 }
                  ::= { configManager 84 }

        configSnapshotFipsSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         FIP Snooping configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 85 }

        configSnapshotLFPSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         LFP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 86 }

        configSnapshotPmInterfaceSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Port Manager  Interfaceconfiguration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 87 }

        configSnapshotAutofabricSelect	OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Autofabric configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 88 }

    configSnapshotDhcpv6RelaySelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         DHCPv6 Relay configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 89 }

     configSnapshotSIPSelect	OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Autofabric configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 90 }

    configSnapshotOpenflowSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Openflow configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 91 }

    configSnapshotWlanSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Wlan configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 92 }

     configSnapshotDhcpSrvSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         DhcpSrv configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 93 }

    configSnapshotDPISelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Dpi configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 94 }

    configSnapshotMsgSrvSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         MsgSrv configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 95 }

    configSnapshotAlSrvSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         ActiveLeaseSrv configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 96 }

    configSnapshotDhcpv6SrvSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Dhcp6Srv configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 97 }

	configSnapshotAGSelect OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         AG configuration data to be included
                         in the snap-shot file generated by the
                         the device."
                DEFVAL  { 0 }
                ::= { configManager 98 }

		configSnapshotQMRSelect OBJECT-TYPE 


                SYNTAX                  Integer32 (0..1) 
	        MAX-ACCESS              read-write 
	        STATUS                  current 
                DESCRIPTION
	               "A set of this object causes 
	                QMR configuration data to be included    
	                in the snap-shot file generated by the    
	                the device."    
	        DEFVAL  { 0 } 
	        ::= { configManager 99 } 

        configSnapshotVCSPSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         VCSP configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 100 }

        configSnapshotDhcpSnoopingSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         DHCP SNOOPING configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 101 }

        configSnapshotAppMonSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Application Monitoring configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 102 }

	configSnapshotLbdSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Loopback Detection configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 103 }

        configSnapshotVMSnoopSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         VM Snooping configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 104 }

	configSnapshotPppoeIaSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         PPPoE-IA configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 105}

	configSnapshotPmPortViolationSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Port Violation configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 106}

	configSnapshotLanPowerSelect    OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Lan Power configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 107}

        configSnapshotPVLANSelect  OBJECT-TYPE
                SYNTAX                  Integer32 (0..1)
                MAX-ACCESS              read-write
                STATUS                  current
                DESCRIPTION
                        "A set of this object causes
                         Private VLAN(PVLAN) configuration data to
                         be included in the snap-shot file generated
                         by the device."
                DEFVAL  { 0 }
                ::= { configManager 108 }

--
-- Compliance Statements
--

        alcatelIND1ConfigMgrMIBCompliance  MODULE-COMPLIANCE
                STATUS  current
                DESCRIPTION
                    "Compliance statement for Configuration Manager Subsystem."
                MODULE  -- this module

                    MANDATORY-GROUPS
                    {
                        configFileGroup,
                        configTimerFileGroup,
                        configSnapshotGroup,
                        configTechSupportLogGroup,
                        configWriteMemoryGroup,
                        configConvertConfigurationGroup
                    }

                ::= { alcatelIND1ConfigMgrMIBCompliances  1 }


--
-- Units of Conformance
--

        configFileGroup  OBJECT-GROUP
                OBJECTS
                {
                  configFileName,
                  configFileAction,
                  configErrorFileName,
                  configFileStatus,
                  configFileMode,
                  configErrorFileMaximum,
                  configChangeStatus
                }
                STATUS  current
                DESCRIPTION
                    "Collection of objects for file configuration."
                ::= { alcatelIND1ConfigMgrMIBGroups  1}


        configTimerFileGroup  OBJECT-GROUP
                OBJECTS
                {
                  configTimerFileName,
                  configTimerFileTime,
                  configTimerFileStatus,
                  configTimerClear,
                  configSnapshotFileName
                }
                STATUS  current
                DESCRIPTION
                    "Collection of objects for timer file configuration."
                ::= { alcatelIND1ConfigMgrMIBGroups  2}


        configSnapshotGroup  OBJECT-GROUP
                OBJECTS
                {
                  configSnapshotAction,
                  configSnapshotAllSelect,
                  configSnapshotVCMSpecific,
                  configSnapshotChassisId,
                  configSnapshotVlanSelect,
                  configSnapshotSpanningTreeSelect,
                  configSnapshotQOSSelect,
                  configSnapshotIPSelect,
                  configSnapshotIPXSelect,
                  configSnapshotIPMSSelect,
                  configSnapshotAAASelect,
                  configSnapshotSNMPSelect,
                  configSnapshot8021QSelect,
                  configSnapshotLinkAggregateSelect,
                  configSnapshotPortMirrorSelect,
                  configSnapshotXIPSelect,
                  configSnapshotHealthMonitorSelect,
                  configSnapshotBootPSelect,
                  configSnapshotBridgeSelect,
                  configSnapshotChassisSelect,
                  configSnapshotInterfaceSelect,
                  configSnapshotPolicySelect,
                  configSnapshotSessionSelect,
                  configSnapshotServerLoadBalanceSelect,
                  configSnapshotSystemServiceSelect,
                  configSnapshotVRRPSelect,
                  configSnapshotWebSelect,
                  configSnapshotRIPSelect,
                  configSnapshotOSPFSelect,
                  configSnapshotBGPSelect,
                  configSnapshotIPRMSelect,
                  configSnapshotIPMRSelect,
                  configSnapshotModuleSelect,
                  configSnapshotRDPSelect,
                  configSnapshotIPv6Select,
                  configSnapshotRIPngSelect,
                  configSnapshotAtmSelect,
                  configSnapshotSonetSelect,
                  configSnapshotNTPSelect,
                  configSnapshotPortMappingSelect,
                  configSnapshotOSPF3Select,
                  configSnapshotStackSelect,
                  configWriteMemoryStatus,
                  configSnapshotISISSelect,
                  configSnapshotEOAMSelect,
                  configSnapshotUDLDSelect,
                  configSnapshotNETSECSelect,
                  configSnapshotIPsecSelect,
                  configSnapshotBFDSelect,
                  configSnapshotMultiChassisSelect,
                  configSnapshotEFMOAMSelect,
                  configSnapshotMPLSSelect,
                  configSnapshotErpSelect,
                  configSnapshotCapabilitySelect,
                  configSnapshotVfcSelect,
                  configSnapshotHaVlanSelect,
                  configSnapshotDaUnpSelect,
                  configSnapshotDHLSelect,
                  configSnapshotMVRPSelect,
                  configSnapshotSAASelect,
                  configSnapshotSPBSelect,
                  configSnapshotSPBIsisSelect,
                  configSnapshotVirtualChassisSelect,
                  configSnapshotMplsLdpSelect,
                  configSnapshotEvbSelect,
                  configSnapshotAppfpSelect,
                  configSnapshotFipsSelect,
                  configSnapshotLFPSelect,
                  configSnapshotPmInterfaceSelect,
                  configSnapshotAutofabricSelect, 
                  configSnapshotDhcpv6RelaySelect, 
                  configSnapshotSIPSelect,
                  configSnapshotOpenflowSelect,
                  configSnapshotWlanSelect,
                  configSnapshotDhcpSrvSelect,
		  configSnapshotDPISelect,
		  configSnapshotMsgSrvSelect,
		  configSnapshotAlSrvSelect,
		  configSnapshotDhcpv6SrvSelect,
                  configSnapshotAGSelect,
                  configSnapshotQMRSelect,
                  configSnapshotVCSPSelect,
                  configSnapshotDhcpSnoopingSelect,
		  configSnapshotAppMonSelect,
		  configSnapshotVMSnoopSelect,
		  configSnapshotLbdSelect,
		  configSnapshotPppoeIaSelect,
		  configSnapshotPmPortViolationSelect,
                  configSnapshotLanPowerSelect,
                  configSnapshotPVLANSelect
                }
                STATUS  current
                DESCRIPTION
                    "Collection of objects for snapshot configuration."
                ::=  { alcatelIND1ConfigMgrMIBGroups  3}


        configTechSupportLogGroup  OBJECT-GROUP
                OBJECTS
                {
                  configTechSupportLogAction
                }
                STATUS  current
                DESCRIPTION
                    "Collection of objects for technical support log file."
                ::= { alcatelIND1ConfigMgrMIBGroups  4}


        configWriteMemoryGroup  OBJECT-GROUP
                OBJECTS
                {
                  configWriteMemory
                }
                STATUS  current
                DESCRIPTION
                    "Collection of objects for write memory."
                ::= { alcatelIND1ConfigMgrMIBGroups  5}


        configConvertConfigurationGroup  OBJECT-GROUP
                OBJECTS
                {
                  configConvertConfiguration,
                  configConvertConfigurationStatus,
                  configConvertDestinationDirectory,
                  configConvertReload
                }
                STATUS  current
                DESCRIPTION
                    "Collection of objects for write memory."
                ::= { alcatelIND1ConfigMgrMIBGroups  6}



END
