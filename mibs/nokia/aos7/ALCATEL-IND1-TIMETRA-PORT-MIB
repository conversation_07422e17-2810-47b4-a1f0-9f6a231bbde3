ALCATEL-IND1-TIMETRA-PORT-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, 
        NOTIFICATION-TYPE, Unsigned32, 
        <PERSON>32, <PERSON><PERSON><PERSON><PERSON><PERSON>, Counter64, 
        <PERSON>te<PERSON>32, <PERSON><PERSON><PERSON>32
                                                FROM SNMPv2-SM<PERSON>

        MODULE-COMPLIANCE, OBJECT-<PERSON><PERSON><PERSON>, 
        NOTIFICATION-GROUP                      FROM SNMPv2-CONF

        TEXTUAL-CONVENTION, DateAndTime, 
        RowStatus, TimeStamp, TimeInterval,
        TruthValue, MacAddress, RowPointer,
        DisplayString                           FROM SNMPv2-TC

        TmnxActionType, TmnxPortID,
        TItemDescription, TNamedItemOrEmpty,
        TNamedItem, TFCName, TQueueId, 
        TmnxOperState, TPortSchedulerPIR, 
        TPortSchedulerCIR, TItemLongDescription,
        TSecondaryShaper10GPIRRate,
        TMlpppQoSProfileId                      FROM ALCATEL-IND1-TIMETRA-TC-MIB

        timetraSRMIBModules                     FROM ALCATEL-IND1-TIMETRA-GLOBAL-MIB                                           
        
        tmnxHwObjs, tmnxHwNotification,
        tmnxHwConformance, tmnxChassisIndex, 
        tmnxChassisNotifyChassisId,
        TmnxAlarmState, TmnxPortAdminStatus,
        TmnxMDAChanType                         FROM ALCATEL-IND1-TIMETRA-CHASSIS-MIB
        ;


tmnxPortMIBModule MODULE-IDENTITY
        LAST-UPDATED    "0801010000Z"
        ORGANIZATION    "Alcatel"
        CONTACT-INFO    
            "Alcatel 7x50 Support
             Web: http://www.alcatel.com/comps/pages/carrier_support.jhtml"
        DESCRIPTION
        "This document is the SNMP MIB module to manage and provision the 
        hardware components of the Alcatel 7x50 device.
        
        Copyright 2003-2008 Alcatel-Lucent.  All rights reserved.
        Reproduction of this document is authorized on the condition that
        the foregoing copyright notice is included.

        This SNMP MIB module (Specification) embodies Alcatel's
        proprietary intellectual property.  Alcatel retains 
        all title and ownership in the Specification, including any 
        revisions.

        Alcatel grants all interested parties a non-exclusive 
        license to use and distribute an unmodified copy of this 
        Specification in connection with management of Alcatel 
        products, and without fee, provided this copyright notice and 
        license appear on all copies.

        This Specification is supplied 'as is', and Alcatel 
        makes no warranty, either express or implied, as to the use, 
        operation, condition, or performance of the Specification."

--
--  Revision History
--
        REVISION        "0801010000Z"
        DESCRIPTION     "Rev 6.0                01 Jan 2008 00:00
                         6.0 release of the TIMETRA-PORT-MIB."

        REVISION        "0701010000Z"
        DESCRIPTION     "Rev 5.0                01 Jan 2007 00:00
                         5.0 release of the TIMETRA-PORT-MIB."

        REVISION        "0603160000Z"   
        DESCRIPTION     "Rev 4.0                16 Mar 2006 00:00
                         4.0 release of the TIMETRA-PORT-MIB."

        REVISION        "0508310000Z"   
        DESCRIPTION     "Rev 3.0                31 Aug 2005 00:00
                         3.0 release of the TIMETRA-PORT-MIB."

        REVISION        "0501240000Z"   
        DESCRIPTION     "Rev 2.1                24 Jan 2005 00:00
                         2.1 release of the TIMETRA-PORT-MIB."

        REVISION        "0403010000Z"
        DESCRIPTION     "Rev 2.0                01 Mar 2004 00:00 
                         2.0 release of the TIMETRA-PORT-MIB."

        ::= { timetraSRMIBModules 25 }


tmnxPortObjs                    OBJECT IDENTIFIER ::= { tmnxHwObjs 4 }
tmnxPortNotificationObjects     OBJECT IDENTIFIER ::= { tmnxHwObjs 7 }
tmnxFRObjs                      OBJECT IDENTIFIER ::= { tmnxHwObjs 9 }
tmnxQosAppObjs                  OBJECT IDENTIFIER ::= { tmnxHwObjs 10 }
tmnxATMObjs                     OBJECT IDENTIFIER ::= { tmnxHwObjs 11 }
tmnxPortStatsObjs               OBJECT IDENTIFIER ::= { tmnxHwObjs 12 }

tmnxPortNotifyPrefix OBJECT IDENTIFIER ::= { tmnxHwNotification 2 } 
  tmnxPortNotification OBJECT IDENTIFIER ::= { tmnxPortNotifyPrefix 0 } 

tmnxPortConformance     OBJECT IDENTIFIER ::= { tmnxHwConformance 2 }


--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      ALCATEL-IND1-TIMETRA-PORT-MIB textual conventions
--

--
--      TmnxPortOperStatus
--
TmnxPortOperStatus ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The current operational status of this port."
    SYNTAX  INTEGER {
                unknown (1),
                inService (2),
                outOfService (3),
                diagnosing (4),
                failed (5)
            }

--
--      TmnxPortEtherReportValue
--
TmnxPortEtherReportValue ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxPortEtherReportValue is an enumerated integer that describes
        the values used to identify the reason an alarm has been raised on an
        Ethernet Port."
    SYNTAX  INTEGER {
                notUsed      (0),  -- not applicable
                signalFailure(1),  -- Ethernet signal lost alarm
                remoteFault  (2),  -- Remote Fault
                localFault   (3),  -- Local Fault
                noFrameLock  (4),  -- Not locked on the Ethernet framing sequence
                highBer      (5)   -- High Bit Error Rate
            }

--
--      TmnxPortEtherReportStatus
--
TmnxPortEtherReportStatus ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxPortEtherReportStatus is a bitmask that describes alarms that
        can be raised/cleared on an Ethernet Port."
    SYNTAX  BITS {
                notUsed      (0),  -- not applicable
                signalFailure(1),  -- Ethernet signal lost alarm
                remoteFault  (2),  -- Remote Fault
                localFault   (3),  -- Local Fault
                noFrameLock  (4),  -- Not locked on the Ethernet framing sequence
                highBer      (5)   -- High Bit Error Rate
            }

--
--      TmnxPortClass
--
TmnxPortClass ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxPortClass data type is an enumerated integer that 
         describes the values used to identify the class of interface 
         provided by this port."
    SYNTAX  INTEGER {
                none  (1),      -- no port installed
                faste (2),      -- Fast Ethernet (10/100MBS)
                gige  (3),      -- Gigabit Ethernet
                xgige (4),      -- Ten-Gigabit Ethernet
                sonet (5),      -- SONET
                vport (6),      -- Virtual port
                unused(7),      -- Unused
                xcme  (8),      -- 10/100/1000 Ethernet
                tdm   (9)       -- TDM
            }

--
--      TmnxPortConnectorType
--
TmnxPortConnectorType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxPortConnectorType data type is an enumerated integer that 
         describes the values used to identify the connector type used on 
         a port.  A TmnxPortConnectorType value specifies the index value 
         for the entry in the tmnxPortConnectTypeTable used to identify a 
         specific type of port connector.  Some example port connector types 
         are: 'unknown', 'rj45', 'bncCoax', 'mtrj', 'scDuplex', 'sffp',
         'lcDuplex'." 
    SYNTAX  Unsigned32

--
--      TmnxPortState
--
TmnxPortState ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The state level of this port.  A value of 'none' indicates the 
         port is either in its initial creation state or is just about to 
         be deleted.  A value of 'ghost' represents a port that is not 
         physically present.  This state may represent a pre-provisioned 
         port.  A value of 'linkDown' represents a port that is physically 
         present but does not have a link.  The 'linkUp' state represents a 
         port that is physically present and has physical link present.
         A port in 'up' state is ready to pass some kinds of traffic.
         The tmnxPortUpProtocols variable indicates the actual type(s) of 
         traffic that can be passed on this 'up' link. The 'diagnose' state
         represents the port undergoing diagnostic test."
    SYNTAX  INTEGER {
                none (1),
                ghost (2),
                linkDown (3),
                linkUp (4),
                up (5),
                diagnose (6)
            }

--
--      TmnxPortType
--
TmnxPortType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxPortType data type is an enumerated integer that describes 
         the values used to identify the specific type of hardware port.
         A TmnxPortType value specifies the index value for the entry in the 
         tmnxPortTypeTable used to identify a specific type of port.
         Some example port types are: 
            -- unknown            - no port installed or unidentified
            -- portType100Tx      - 10/100 base copper
            -- portType100F       - 10/100 base fiber
            -- portType1000Tx     - 1000 base copper
            -- portType1000F      - 1000 base fiber
            -- portType1000dF     - 1000 base dual fiber
            -- portTypeOC3sonet   - OC-3
            -- portTypeOC12sonet  - OC-12
            -- portTypeOC48sonet  - OC-48
            -- portTypeOC192sonet - OC-192
            -- portType10000F     - 10 Gig Ethernet LAN
            -- portTypeXgigWan    - 10 Gig Ethernet WAN
            -- portTypeVport      - Virtual port
            -- portTypeDS3E3      - DS3/E3 port
            -- portTypeDS1E1      - DS1/E1 port
         "
    SYNTAX  Unsigned32

--
--      TmnxDs0ChannelList
--
TmnxDs0ChannelList ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
            "A list of ds0 timeslots on a DS1 line.

            The list is specified as an OCTET STRING in which each ds0
            timeslot is represented by a single bit, where  timeslots 1
            through 8 are represented by the bits in the first octet,
            timeslots 9 through 16 by the bits in the second octet,
            etc.  In each octet, the lowest numbered timeslot is
            represented by the most significant bit, and the highest
            numbered timeslot by the least significant bit.  A timeslot
            is present in the list when its bit is 1, and absent
            when its bit is 0. 

            If the OCTET STRING value has more bits than required to 
            represent the timeslots on a DS1, then the extra bits 
            are ignored."
    SYNTAX       OCTET STRING (SIZE(0..4))

--
--      TmnxBundleID
--
TmnxBundleID ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "A TmnxBundleID is an unique 32 bit number encoded as shown below.

            |32 30| 29 26 | 25 22 | 21 16 | 15     13 | 12   1|
            +-----+-------+-------+-------+-----------+-------+
            |001  |  slot |  mda  |   0   | bndl type | bndlid|
            +-----+-------+-------+-------+-----------+-------+

         The bundle id is unique for a MDA.  The bndlid must fall in one of
         the following ranges depending on MDA type:

           mda-type             range
           m4-choc3-as-sfp      1-256 
           m1-choc12-as-sfp     1-256 
           m12-chds3-as         1-256 
           m4-chds3-as          1-112
           m4-chds3             1-56
           m12-chds3            1-56
           m1-choc12-sfp        1-56
           m4-choc3-sfp         1-56
           c8-chds1             1-56
           c8-atmds1            1-8
         
         A slot value of 15 (Invalid slot) and mda value of 0 (invalid mda)
         represents a bundle protection group. 
         Otherwise, the bundle is considered associated with a valid slot 
         and mda.
         
         The following bundle types are supported:
         
         bndl type     value
           MLPPP        000
           IMA          001"
    SYNTAX  Unsigned32

--
--      TmnxDSXBertPattern
--
TmnxDSXBertPattern ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxDSXBertPattern data type is an enumerated integer that 
         indicates the pattern used for the Bit Error Rate Test (BERT)."
    SYNTAX  INTEGER {
                none        (0),
                ones        (1),
                zeros       (2),
                alternating (3),
                twoexp3     (4),
                twoexp9     (5),
                twoexp15    (6),
                twoexp20    (7),
                twoexp11    (8),
                twoexp20q   (9),
                twoexp23   (10)
            }

--
--      TmnxDSXBertOperStatus
--
TmnxDSXBertOperStatus ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxDSXBertOperStatus data type is an enumerated integer that 
         indicates the status of Bit Error Rate Test (BERT).
         When BERT is activated, the status will be 'active'. Otherwise
         the status will be 'idle' or 'noMdaResources' if the last BERT test
         could not be executed because of an MDA concurrent BERT test limit."
    SYNTAX  INTEGER {
                none (0),
                active (1),
                idle (2),
                noMdaResources (3)
            }

--
--      TmnxDSXIdleCycleFlags
--
TmnxDSXIdleCycleFlags ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxDSXIdleCycleFlags data type is an enumerated integer that 
         indicates the value that the DS3/DS1 port/channel or a DS0 channel
         group transmits during idle cycle.

         The value of 'none' is applicable to ATM and CEM ports/channels only,
         which transmits idle cells and not octets defined by the below values.
 
         If the value is set to 'flags', a value of 0x7E is used.
         If the value is set to 'ones', a value of 0xFF is used."
    SYNTAX  INTEGER {
                none  (0),
                flags (1),
                ones (2)
            }

--
--      TmnxDSXIdleFillType
--
TmnxDSXIdleFillType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxDSXIdleFillType data type is an enumerated integer that 
         indicates the type of data fill that a CEM DS3/DS1 channel or a DS0
         channel group plays out when the channel experiences underun.
         
         - The value of 'notApplicable' indicates that this object is not
           applicable to the channel.
         - The value of 'allOnes' indicates that all 1's will be played out.
         - The value of 'userDefinedPattern' indicates that a user defined
           pattern will be played out."
    SYNTAX  INTEGER {
                notApplicable (0),
                allOnes (1),
                userDefinedPattern (2)
            }

--
--      TmnxDSXLoopback
--
TmnxDSXLoopback ::= TEXTUAL-CONVENTION
    STATUS  obsolete
    DESCRIPTION
        "The TmnxDSXLoopback data type is an enumerated integer that 
         indicates the type of loopback the DS3/DS1 port/channel 
         currently is in."
    SYNTAX  INTEGER {
                none (0),
                line (1),
                internal (2),
                remote (3)
            }

--
--      TmnxDSXReportAlarm
--
TmnxDSXReportAlarm ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxDSXReportAlarm data type indicates the type of TDM alarm:
         ais - alarm indication signal errors. 
         los - loss of signal errors.
         oof - out-of-frame errors. 
         rai - resource availability indicator events.
         looped - far end wants the read end to loopback
         berSd - DSX bit error signal degradation
         berSf - DSX bit error signal failure"
    SYNTAX      BITS {
                    notUsed (0),
                    ais (1),
                    los (2),
                    oof (3),
                    rai (4),
                    looped (5),
                    berSd (6),
                    berSf (7)
                }

--
--      TmnxDSXClockSource
--
TmnxDSXClockSource ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxDSXClockSource data type is an enumerated type
         that indicates the clock for transmitted data on a DS3 or DS1 channel.
         loopTimed(1)     - The clock is recovered from the line's receive
                            data stream
         nodeTimed(2)     - The clock is internal
         adaptive(3)      - The clock is adaptively recovered from the rate at
                            which data is received and not from the physical
                            layer.
         differential(4)  - The clock is recovered from differential RTP
                            timestamp header."
    SYNTAX      INTEGER {
                    loopTimed    (1),
                    nodeTimed    (2),
                    adaptive     (3),
                    differential (4)
                }

--
--      TmnxDSXClockSyncState
--
TmnxDSXClockSyncState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxDSXClockSyncState data type is an enumeration type that
         indicates the clock synchronization state on a DS3 or DS1 channel.
         unknown(0)       - Unsupported or unknown state
         normal(1)        - Normal (locked) state
         holdOver(2)      - Synchronization with reference has been lost
         freeRun(3)       - Synchronized with internal reference"
    SYNTAX      INTEGER {
                    unknown  (0),
                    normal   (1),
                    holdOver (2),
                    freeRun  (3)
                }

--
--      TmnxDS1Loopback
--
TmnxDS1Loopback ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxDS1Loopback data type is an enumerated integer that 
         indicates the type of loopback the DS1 port/channel 
         currently is in.
         A 'line' loopback loops frames received on this port back to the 
         remote system.
         A 'internal' loopback loops frames from the local system back at
         the framer.        
         A 'fdlAnsi' requests a line loopback of type FDL ANSI T1.403.
         A 'fdlBellcore' requests a line loopback of type FDL Bellcore 
         TR-TSY-000312.        
         A 'payloadAnsi' requests a payload loopback of type FDL ANSI T1.403.
         A 'inbandAnsi' requests a line loopback of type inband ANSI T1.403.
         A 'inbandBellcore' requests a line loopback of type inband Bellcore 
         TR-TSY-000312."
    SYNTAX  INTEGER {
                none (0),
                line (1),
                internal (2),
                fdlAnsi (3),
                fdlBellcore (4),
                payloadAnsi (5),
                inbandAnsi  (6),
                inbandBellcore (7)
            }

--
--      TmnxDS3Loopback
--
TmnxDS3Loopback ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxDS3Loopback data type is an enumerated integer that 
         indicates the type of loopback the DS3 port/channel 
         currently is in.
         A 'line' loopback loops frames received on this port back to the 
         remote system.
         A 'internal' loopback loops the frames from the local system back at
         the framer.
         When the value is set to 'remote', a signal is sent to the remote 
         system to provide a line loopback."
    SYNTAX  INTEGER {
                none (0),
                line (1),
                internal (2),
                remote (3)
            }

--
--      TmnxImaGrpState
--
TmnxImaGrpState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxImaGrpState indicates the current state 
         of the IMA Group State Machine."
    SYNTAX      INTEGER {
                    invalid                          (0),
                    notConfigured                    (1),
                    startUp                          (2),
                    startUpAck                       (3),
                    configAbortUnsupportedM          (4),
                    configAbortIncompatibleSymmetry  (5),
                    configAbortOther                 (6),
                    insufficientLinks                (7),
                    blocked                          (8),
                    operational                      (9),
                    configAbortUnsupportedImaVersion (10) 
                }

TmnxImaGrpFailState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxImaGrpFailState indcates the failure 
         reason of an IMA group."
    SYNTAX      INTEGER {
                    noFailure(1), -- unit is up
                    startUpNe(2),
                    startUpFe(3),
                    invalidMValueNe(4),
                    invalidMValueFe(5),
                    failedAssymetricNe(6),
                    failedAssymetricFe(7),
                    insufficientLinksNe(8),
                    insufficientLinksFe(9),
                    blockedNe(10),
                    blockedFe(11),
                    otherFailure(12),
                    invalidImaVersionNe(13),
                    invalidImaVersionFe(14) 
                }

TmnxImaLnkState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxImaLnkState indicates the current 
         state of an IMA link."
    SYNTAX      INTEGER {
                    notInGroup(1),
                    unusableNoGivenReason(2),
                    unusableFault(3),
                    unusableMisconnected(4),
                    unusableInhibited(5),
                    unusableFailed(6),
                    usable(7),
                    active(8) 
                }

TmnxImaLnkFailState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxImaLnkFailState indicates the local failure 
         status of a link belonging to an IMA group."
    SYNTAX      INTEGER {
      noFailure(1),
      imaLinkFailure(2),
      lifFailure(3),
      lodsFailure(4),
      misConnected(5),
      blocked(6),
      fault(7),
      farEndTxLinkUnusable(8),
      farEndRxLinkUnusable(9) }

TmnxImaTestState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxImaTestState indicates the current state 
         of the test pattern procedure."
    SYNTAX      INTEGER {
      disabled(1),
      operating(2),
      failed(3) }
        
TmnxImaGrpClockModes ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxImaGrpClockModes lists the types of 
         clock modes available to IMA Groups."
    SYNTAX      INTEGER {
      ctc(1),
      itc(2) }
        
TmnxImaGrpVersion ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxImaGrpVersion lists the types of 
         Ima versions available per IMA Groups."
    SYNTAX      INTEGER {
      oneDotZero(1),
      oneDotOne(2) }

TmnxMcMlpppClassIndex ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxMcMlpppClassIndex indicates the class
         of a Multiclass MLPPP bundle.
         
         For Multiclass MLPPP bundles with a non-zero 
         tmnxBundleMlpppClassCount of classes, class index takes 
         valid values from 0 to (tmnxBundleMlpppClassCount - 1) 
         inclusive. For example a 4-class MLPPP bundle has 4 classes 
         with indices 0, 1, 2, and 3."
    SYNTAX      INTEGER (0..15)

TmnxMlpppEndpointIdClass ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "TmnxMlpppEndpointIdClass indicates the MLPPP Endpoint
         Discriminator Class Field Type."
    SYNTAX      INTEGER {
        nullClass (0),
        localAddress (1),
        ipAddress (2),
        ieee802dot1GlobalMacAddress (3),
        pppMagicNumberBlock (4),
        publicSwitchedNetworkDirNumber (5)}
            
--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      ALCATEL-IND1-TIMETRA-PORT-MIB at a glance
-- 
--  timetra (enterprises 6527)
--    timetraProducts (3)
--      tmnxSRMIB (1)
--                tmnxSRConfs (1)
--                tmnxSRObjs (2)
--                      tmnxHwObjs (tmnxSRObjs 2)
--                tmnxSRNotifyPrefix (3)
--
--  tmnxPortObjs (tmnxHwObjs 4)
--        tmnxPortTableLastChange (1)
--        tmnxPortTable (2)
--        tmnxPortTestTable (3)
--        tmnxPortEtherTable (4)
--        tmnxSonetTable (5)
--        tmnxSonetPathTable (6)
--        tmnxPortTypeTable (7)
--        tmnxPortConnectTypeTable (8)
--        tmnxPortFCStatsTable (9)
--        tmnxDS3Table (10)
--        tmnxDS3ChannelTable (11)
--        tmnxDS1Table (12)
--        tmnxDS0ChanGroupTable (13)
--        tmnxBundleTable (14)
--        tmnxBundleMemberTable (15)
--        tmnxPortToChannelTable (16)
--        tmnxPortIngrMdaQosStatTable (17)
--        tmnxSonetGroupTable (18)
--        tmnxPortScalarObjs (19)
--        tmnxCiscoHDLCTable (20)
--        tmnxBundleImaGrpTable (21)
--        tmnxBundleMemberImaTable (22)
--        tmnxDS1PortTable (23)
--        tmnxPortSchedOverrideTable (24)  
--        tmnxBPGrpAssocTable (25)
--        tmnxBundleMlpppTable (26)
--        tmnxDigitalDiagMonitorTable (29)
--
--  tmnxPortNotificationObjects (7)
--        tmnxPortNotifyPortId (1)
--        tmnxPortNotifySonetAlarmReason (2)
--        tmnxPortNotifySonetPathAlarmReason (3)
--        tmnxPortNotifyError (4)
--        tmnxPortNotifyDS3AlarmReason (5)
--        tmnxPortNotifyDS1AlarmReason (6)
--        tmnxPortNotifyBundleId (7)
--        tmnxPortNotifyEtherAlarmReason (8)
--        
--  tmnxFRObjs (9)
--        tmnxFRDlcmiTable (1)
--
--  tmnxQosAppObjs (10)
--        tmnxQosPoolAppTable (2)
--
--  tmnxATMObjs (11)
--        tmnxATMIntfTable (1)
--
--  tmnxPortStatsObjs (12)
--        tmnxPortNetIngressStatsTable    (1)
--        tmnxPortNetEgressStatsTable     (2)
--        tmnxCiscoHDLCStatsTable         (3)
--        tmnxMcMlpppStatsTable           (4)
--

--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      IOM Port tables
--
tmnxPortTableLastChange   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime when the tmnxPortTable was last changed."
    ::= { tmnxPortObjs 1 }
        
tmnxPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The port table has an entry for each port on each IOM card in each 
         chassis in the TMNX system."
    ::= { tmnxPortObjs 2 }

tmnxPortEntry       OBJECT-TYPE
    SYNTAX      TmnxPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a port on a IOM card in a chassis in the 
         system.  Entries cannot be created and deleted via SNMP SET 
         operations.  Before an IOM tmnxMDAEntry can be deleted from the 
         tmnxMDATable, its supported tmnxPortEntry rows must be in the proper 
         state for removal."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxPortTable 1 }

TmnxPortEntry ::=
    SEQUENCE {
        tmnxPortPortID                    TmnxPortID,
        tmnxPortLastChangeTime            TimeStamp,
        tmnxPortType                      TmnxPortType,
        tmnxPortClass                     TmnxPortClass,
        tmnxPortDescription               TItemLongDescription,
        tmnxPortName                      TNamedItemOrEmpty,
        tmnxPortAlias                     TNamedItemOrEmpty,
        tmnxPortUserAssignedMac           TruthValue,
        tmnxPortMacAddress                MacAddress,
        tmnxPortHwMacAddress              MacAddress,
        tmnxPortMode                      INTEGER,
        tmnxPortEncapType                 INTEGER,
        tmnxPortLagId                     Unsigned32,
        tmnxPortHoldTimeUp                Unsigned32,
        tmnxPortHoldTimeDown              Unsigned32,
        tmnxPortUpProtocols               BITS,
        tmnxPortConnectorType             TmnxPortConnectorType,
        tmnxPortTransceiverType           INTEGER,
        tmnxPortTransceiverCode           BITS,
        tmnxPortTransceiverLaserWaveLen   Unsigned32,
        tmnxPortTransceiverDiagCapable    INTEGER,
        tmnxPortTransceiverModelNumber    TNamedItemOrEmpty,
        tmnxPortSFPConnectorCode          INTEGER,
        tmnxPortSFPVendorOUI              Unsigned32,
        tmnxPortSFPVendorManufactureDate  DateAndTime, 
        tmnxPortSFPMedia                  INTEGER,
        tmnxPortSFPEquipped               TruthValue,
        tmnxPortEquipped                  TruthValue,
        tmnxPortLinkStatus                TruthValue,
        tmnxPortAdminStatus               TmnxPortAdminStatus,
        tmnxPortOperStatus                TmnxPortOperStatus,
        tmnxPortState                     TmnxPortState,
        tmnxPortPrevState                 TmnxPortState,
        tmnxPortNumAlarms                 Unsigned32,
        tmnxPortAlarmState                TmnxAlarmState,
        tmnxPortLastAlarmEvent            RowPointer,
        tmnxPortClearAlarms               TmnxActionType,
        tmnxPortSFPVendorSerialNum        TNamedItemOrEmpty,
        tmnxPortSFPVendorPartNum          TNamedItemOrEmpty,
        tmnxPortLastStateChanged          TimeStamp,
        tmnxPortNumChannels               Unsigned32,
        tmnxPortNetworkEgrQueues          TNamedItemOrEmpty,
        tmnxPortBundleNumber              INTEGER,
        tmnxPortIsLeaf                    TruthValue,
        tmnxPortChanType                  TmnxMDAChanType,
        tmnxPortParentPortID              TmnxPortID,
        tmnxPortOpticalCompliance         OCTET STRING,
        tmnxPortLoadBalanceAlgorithm      INTEGER,
        tmnxPortEgrPortSchedPlcy          TNamedItemOrEmpty,
        tmnxPortLastClearedTime           TimeStamp,
        tmnxPortIngNamedPoolPlcy          TNamedItemOrEmpty,
        tmnxPortEgrNamedPoolPlcy          TNamedItemOrEmpty,
        tmnxPortIngPoolPercentRate        Unsigned32,
        tmnxPortEgrPoolPercentRate        Unsigned32,
        tmnxPortDDMEventSuppression       TruthValue,
        tmnxPortSFPStatus                 INTEGER

    }

tmnxPortPortID          OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxPortPortID is an index into this table. It maps this port to its 
         entry in the mib-2 interfaces table."
    ::= { tmnxPortEntry 1 }

tmnxPortLastChangeTime    OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortLastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxPortEntry row for this port."
    ::= { tmnxPortEntry 2 }

tmnxPortType        OBJECT-TYPE
    SYNTAX      TmnxPortType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The type of port or optical pack installed."
    ::= { tmnxPortEntry 3 }

tmnxPortClass        OBJECT-TYPE
    SYNTAX      TmnxPortClass
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The class of port or optical pack installed.  This can be
         derived from tmnxPortType."
    ::= { tmnxPortEntry 4 }

tmnxPortDescription     OBJECT-TYPE
    SYNTAX      TItemLongDescription
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A textual string containing user supplied information about the
         interface."
    DEFVAL { ''H }
    ::= { tmnxPortEntry 5 }

tmnxPortName        OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The textual name of the interface.  The value of this
         object should be the name of the interface as assigned by
         the local device and should be suitable for use in commands
         entered at the device's `console'.  This might be a text
         name, such as `le0' or 'sys171-2/1." 
    ::= { tmnxPortEntry 6 }

tmnxPortAlias        OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This object is an 'alias' name for the interface as
         specified by a network manager, and provides a non-volatile
         'handle' for the interface.

         On the first instantiation of an interface, the value of
         tmnxPortAlias associated with that interface is the zero-length
         string.  As and when a value is written into an instance of
         tmnxPortAlias through a network management set operation, then the
         agent must retain the supplied value in the tmnxPortAlias instance
         associated with the same interface for as long as that
         interface remains instantiated, including across all re-
         initializations/reboots of the network management system."
    DEFVAL { ''H }
    ::= { tmnxPortEntry 7 }

tmnxPortUserAssignedMac OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxPortUserAssignedMac has a value of 'true', the value of
         tmnxPortMacAddress has been explicitly assigned by a SNMP SET
         request.  When tmnxPortUserAssignedMac is set to 'false',
         tmnxPortMacAddress returns the system assigned MAC address."
    DEFVAL { false }
    ::= { tmnxPortEntry 8 }

tmnxPortMacAddress  OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When tmnxPortUserAssignedMac has a value of 'true', 
         tmnxPortMacAddress has the value that has been explicitly
         assigned by a SNMP SET request.  When tmnxPortUserAssignedMac
         has a value of 'false', tmnxPortMacAddress returns the same
         value as tmnxPortMacAddress, the hardware or system assigned
         MAC address.  When tmnxPortMacAddress is modified by a SET 
         request, the agent sets tmnxPortUserAssignedMac to 'true'.
             
         Setting tmnxPortMacAddress to all zeros causes the agent to
         revert to using the default tmnxPortHwMacAddress and also to
         return tmnxPortUserAssignedMac as 'false'."
    DEFVAL { '000000000000'h }
    ::= { tmnxPortEntry 9 }

tmnxPortHwMacAddress  OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface's hardware or system assigned MAC address at its 
         protocol sub-layer.  When tmnxPortUserAssignedMac has a value of 
         'true', the value of tmnxPortMacAddress is used instead of 
         tmnxPortHwMacAddress.
             
         In the case of a pre-provisioned port that is not yet physically
         in the system, the MAC address may not be known.  In this case
         a MAC address of all zeros is returned."
    ::= { tmnxPortEntry 10 }

tmnxPortMode        OBJECT-TYPE
    SYNTAX      INTEGER {
                    undefined (0),
                    access (1),
                    network (2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortMode variable indicates if the interface on this port
         is configured as a service access port or a network access
         port.  

         If the port mode equals 'access', and the tmnxMDAAssignedType
         is of ATM MDA family, then tmnxPortEncapType is automatically 
         set to 'atmEncap'.

         If port mode equals 'access', tmnxPortEncapType must also
         be configured to 'qEncap' (For Ethernet MDAs) or to
         'bcpNullEncap' for Sonet/SDH MDAs.  

         Once the port's operation mode has been configured 
         for 'access' mode, multiple services may be configured 
         on the port.  

         If the port mode equals 'network', tmnxPortEncapType must also
         be configured to 'nullEncap' (Ethernet MDAs), or 'pppAutoEncap'
         (Sonet/SDH MDAs) and tmnxPortEncapType is defaulted to those
         values on the mode change."
    ::= { tmnxPortEntry 11 }

tmnxPortEncapType   OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (0),
                    nullEncap (1),
                    qEncap (2),
                    mplsEncap (3),
                    bcpNullEncap (4),
                    bcpDot1qEncap (5),
                    ipcpEncap (6),
                    frEncap (7),
                    pppAutoEncap (8),
                    atmEncap (9),
                    qinqEncap (10),
                    wanMirrorEncap (11),
                    ciscoHDLCEncap (12),
                    cemEncap (13)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The type of encapsulation protocol configured on this
         port's network interface.  If tmnxPortEncapType has a value
         of 'nullEncap', frames received will not carry any tags
         or labels and as a result only one service can be
         configured on this port.  Sometimes this is referred to
         as raw Ethernet packets. 
                 
         If tmnxPortEncapType has a value of 'qEncap', ingress frames 
         carry 802.1q tags where each different tag can signify a 
         different service. This is not a valid value if
         tmnxPortClass has a value of 'sonetPath'. 
                 
         If tmnxPortEncapType has a value of 'bcpNullEncap',
         Bcp is used on the sonet path as the NCP control protocol.
         The BCP IEEE 802 Tagged Frame Configuration Option (type 8)
         is negotiated to 'enabled'.  VLAN tagged frames are
         allowed into the sonet path.  Only a single SAP can
         be associated with the sonet path.  'bcpNullEncap' is 
         valid only if tmnxPortClass has a value of 'sonetPath'.
                 
         If tmnxPortEncapType has a value of 'bcpDot1qEncap',
         BCP is used as the NCP control protocol.  The BCP IEEE 802
         Tagged Frame Configuration Option (type 8) is negotiated to
         'enabled'.  VLAN tagged frames are allowed on the sonet path.
         This encapsulation type is required when multiple SAPs are
         defined on the sonet path where each one is service
         delimited by a provisioned Dot1q tag.  When 'bcpDot1qEncap'
         is specified, BCP does not enter the 'open' state unless
         the far end peer also supports 'bcpDot1qEncap'.  This allows
         a LCP negotiation to transmit configuration request and
         confirmation messages to enable this feature.  'bcpDot1qEncap'
         is a valid value only if tmnxPortClass has a value of 'sonetPath'.
 
         If tmnxPortEncapType has a value of 'ipcpEncap',
         BCP will not be used on this sonet path.  IPCP NCP is used
         instead.  'ipcpEncap' is a valid only if tmnxPortClass has
         a value of 'sonetPath'.
         
         If tmnxPortEncapType has a value of 'frEncap', Frame Relay is
         the expected encapsulation.

         If tmnxPortEncapType has a value of 'pppAutoEncap', IPCP is 
         automatically enabled. This encap type is only valid on 
         ports/paths in network mode.

         If tmnxPortEncapType has a value of 'atmEncap', the encapsulation
         on the port is ATM. The 'atmEncap' is also used when mirroring
         ATM ports.

         If tmnxPortEncapType has a value of 'wanMirrorEncap', the 
         port is used for mirroring of frame-relay and POS ports. On such 
         ports no link management protocol would run.

         If tmnxPortEncapType has a value of 'ciscoHDLCEncap', the Cisco HDLC
         encapsulation is applied. This encap type is only valid if 
         TmnxPortClass has a value of 'tdm' or 'sonet'.
         
         If tmnxPortEncapType has a value of 'cemEncap', encapsulation of
         frames will be circuit emulation.  This is used to support transparent
         transmission of frames.  This encap type is only valid
         if TmnxPortClass has a value of 'tdm' or 'sonet' in access mode.

         tmnxPortEncapType is set to 'unknown' for physical SONET/SDH and
         TDM ports.
         "
    ::= { tmnxPortEntry 12 }

tmnxPortLagId OBJECT-TYPE
    SYNTAX      Unsigned32 (0..200)  
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION     
        "The value of tmnxPortLagId indicates which LAG or multi-link trunk
         (MLT) this port is assigned to. If this port is not associated
         with any LAG, this value will be set to zero(0).

         The maximum value of this object is '64', when the value of
         ALCATEL-IND1-TIMETRA-CHASSIS-MIB::tmnxChassisType is '5' (ESS-1/SR-1)."
    DEFVAL { 0 }
    ::= { tmnxPortEntry 13 }

tmnxPortHoldTimeUp  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..50)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortHoldTimeUp is used to configure the 
         hold-timer for link up event dampening. This guards against 
         reporting excessive interface transitions. This is implemented 
         by not advertising subsequent transitions of the interface to 
         upper layer protocols until the configured timer has expired. 
         A value of zero (0) indicates that an up transition is reported
         immediately.  The value of tmnxPortHoldTimeUp is not applicable 
         when tmnxPortClass has a value of 'sonet (5)'. In that case,
         tmnxSonetHoldTimeUp is used instead."
    DEFVAL { 0 }
    ::= { tmnxPortEntry 14 }

tmnxPortHoldTimeDown    OBJECT-TYPE
    SYNTAX      Unsigned32 (0..50)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortHoldTimeDown is used to configure the 
         hold-timer for link down event dampening. This guards against 
         reporting excessive interface transitions. This is implemented 
         by not advertising subsequent transitions of the interface to 
         upper layer protocols until the configured timer has expired. 
         A value of zero (0) indicates that a down transition is reported
         immediately. The value of tmnxPortHoldTimeDown is not applicable 
         when tmnxPortClass has a value of 'sonet (5)'. In that case,
         tmnxSonetHoldTimeDown is used instead."
    DEFVAL { 0 }
    ::= { tmnxPortEntry 15 }

tmnxPortUpProtocols     OBJECT-TYPE
    SYNTAX      BITS {
                    portUpIpv4  (0),
                    portUpMpls  (1),
                    portUpBcp   (2),
                    portUpIso   (3),
                    portUpFr    (4),
                    portUpAtm   (5),
                    portUpChdlc (6),
                    portUpIma   (7),
                    portUpIpv6  (8)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortUpProtocols variable is a bitmap that indicates what
         protocols can be used on this port type."
    ::= { tmnxPortEntry 16 }

tmnxPortConnectorType       OBJECT-TYPE
    SYNTAX      TmnxPortConnectorType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The type of connector installed on this port."
    ::= { tmnxPortEntry 17 }
    
tmnxPortTransceiverType       OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown(0),
                    gbic(1),
                    moduleConnectorSolderedToMotherboard(2),
                    sfpTransceiver(3),
                    xbiTransceiver(4), 
                    xenpakTransceiver(5), 
                    xfpTransceiver(6), 
                    xffTransceiver(7), 
                    xfpeTransceiver(8), 
                    xpakTransceiver(9), 
                    x2Transceiver(10) 
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of the transceiver."
    ::= { tmnxPortEntry 25 }

tmnxPortTransceiverCode       OBJECT-TYPE
    SYNTAX      BITS{
                    unknown(0),
                    oc48-longreach(1),
                    oc48-intermediatereach(2),
                    oc48-shortreach(3),
                    oc12-singlemodelongreach(4),
                    oc12-singlemodeinterreach(5),
                    oc12-multimodeshortreach(6),
                    oc3-singlemodelongreach(7),
                    oc3-singlemodeinterreach(8),
                    oc3-multi-modeshortreach(9),
                    gige-1000base-t(10),
                    gige-1000base-cx(11),
                    gige-1000base-lx(12),
                    gige-1000base-sx(13),
                    faste-100base-mm-fx(14),
                    faste-100base-sm-fx(15),
                    xgige-10gbase-sr(16), 
                    xgige-10gbase-lr(17), 
                    xgige-10gbase-er(18),
                    xgige-10gbase-sw(19), 
                    xgige-10gbase-lw(20), 
                    xgige-10gbase-ew(21) 
                }
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The code for the transmission media.

         tmnxPortOpticalCompliance is used instead of 
         tmnxPortTransceiverCode.tmnxPortOpticalCompliance  contains the 
         same information and is defined by an industry standard."
    ::= { tmnxPortEntry 26 }

tmnxPortTransceiverLaserWaveLen       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The light wavelength transmitted by the transceiver's laser.
         A value of zero indicates that the port is not equipped with
         the transceiver."
    ::= { tmnxPortEntry 27 }

tmnxPortTransceiverDiagCapable    OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable(0),
                    true(1),
                    false(2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the transceiver is capable of doing diagnostics."
    ::= { tmnxPortEntry 28 }

tmnxPortTransceiverModelNumber OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Model number of the transceiver."
    ::= { tmnxPortEntry 29 }

tmnxPortSFPConnectorCode       OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown(0),
                    sc(1),
                    fiberChannel-Style1-CopperConnector(2),
                    fiberChannel-Style2-CopperConnector(3),
                    bncortnc(4),
                    fiberChannelCoaxialHeaders(5),
                    fiberJack(6),
                    lc(7),
                    mt-rj(8),
                    mu(9),
                    sg(10),
                    opticalPigtail(11),
                    hssdcII(32),
                    copperPigtail(33),
                    copperGigE(128)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The external connector provided on the interface."
    ::= { tmnxPortEntry 30 }

tmnxPortSFPVendorOUI       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The vendor organizationally unique identifier field (OUI) 
         contains the IEEE company identifier for the vendor. 
         A value of all zero indicates that the vendor OUI 
         is unspecified."
    ::= { tmnxPortEntry 31 }
    
tmnxPortSFPVendorManufactureDate       OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The vendor's date code."
    ::= { tmnxPortEntry 32 }

tmnxPortSFPMedia       OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable(0),
                    ethernet(1),
                    sonetsdh(2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The media supported for the SFP."
    ::= { tmnxPortEntry 33 }

tmnxPortSFPEquipped    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the SFP is equipped."
    ::= { tmnxPortEntry 34 }

tmnxPortEquipped    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if there is an optics pack installed in this
         port or not."
    ::= { tmnxPortEntry 35 }

tmnxPortLinkStatus  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether or not the port has a physical link."
    ::= { tmnxPortEntry 36 }    

tmnxPortAdminStatus  OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The desired administrative status of this port."
    DEFVAL { inService }
    ::= { tmnxPortEntry 37 }

tmnxPortOperStatus   OBJECT-TYPE
    SYNTAX      TmnxPortOperStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current operational status of this port."
    ::= { tmnxPortEntry 38 }

tmnxPortState   OBJECT-TYPE
    SYNTAX  TmnxPortState
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The state level of this port.  A value of 'none' indicates
         the port is either in its initial creation state or is
         just about to be deleted.  A value of 'ghost' represents
         a port that is not physically present.  This state may
         represent a pre-provisioned port.  A value of 'linkDown'
         represents a port that is physically present but does
         not have a link.  The 'linkUp' state represents a port
         that is physically present and has physical link present.
         A port in 'up' state is ready to pass some kinds of traffic.
         The tmnxPortUpProtocols variable indicates the actual type(s)
         of traffic can be passed on this 'up' link."
    ::= { tmnxPortEntry 39 }
        
tmnxPortPrevState   OBJECT-TYPE
    SYNTAX      TmnxPortState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortPrevState indicates the state level that the port
         transitioned from into the current tmnxPortState level."
    ::= { tmnxPortEntry 40 }  

tmnxPortNumAlarms   OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of alarms currently outstanding on this port."
    ::= { tmnxPortEntry 41 }

tmnxPortAlarmState    OBJECT-TYPE
    SYNTAX  TmnxAlarmState
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The value of tmnxPortAlarmState indicates the current alarm
         state for this port."
    ::= { tmnxPortEntry 42 }

tmnxPortLastAlarmEvent    OBJECT-TYPE
    SYNTAX  RowPointer
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The value of tmnxPortLastAlarmEvent is an object identifier whose
         object name and instance values point to the row entry in the 
         ALARM-MIB that contains the most recent alarm event associated with 
         this port.  If the tmnxPortAlarmState has a value of 
         'alarmCleared', the most recent alarm event will be in the 
         nlmAlarmClearedTable.  If it has a value of 'alarmActive', the 
         entry pointed to is in the nlmAlarmActiveTable.  If the value of 
         tmnxPortLastAlarmEvent is '0.0', then either there have not been any 
         alarm events associated with this chassis since the system was
         last booted, or the last alarm event has aged out and its entry is 
         no longer available in the ALARM-MIB tables."
    ::= { tmnxPortEntry 43 }

tmnxPortClearAlarms OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting this action variable causes all the alarms
         associated with this port to be moved from the current
         alarm log to the history alarm log. Primarily meant
         for use in development.  This object will most likely
         be removed from the MIB before product release."
    DEFVAL { notApplicable }
    ::= { tmnxPortEntry 44 }

tmnxPortSFPVendorSerialNum       OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortSFPVendorSerialNum contains ASCII characters, 
         defining the vendor serial number.
         A value of all zero indicates that the vendor SN 
         is unspecified."
    ::= { tmnxPortEntry 45 }

tmnxPortSFPVendorPartNum       OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The vendor part number contains ASCII characters, 
         defining the vendor part number or product name.
         A value of all zero indicates that the vendor PN 
         is unspecified. "
    ::= { tmnxPortEntry 46 }
    
tmnxPortLastStateChanged       OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortLastStateChanged variable contains the 
         value of the sysUpTime the last time the operational status 
         of the port changed state."
    ::= { tmnxPortEntry 48 }

tmnxPortNumChannels       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "For SONET/SDH paths or TDM channels, tmnxPortNumChannels
         contains the number of possible immediate children. For
         leaf paths or channels, the value of this object will be zero.
         For SONET/SDH or TDM physical ports, tmnxPortNumChannels
         contains the total number of channels on the physical port.
         For all other entries in the tmnxPortTable, the value of
         this object will be zero."
    ::= { tmnxPortEntry 49 }

tmnxPortNetworkEgrQueues       OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "tmnxPortNetworkEgrQueues contains the network egress queue policy 
        if the tmnxPortMode is network. Otherwise this object has no 
        significance."
    ::= { tmnxPortEntry 50 }

tmnxPortBundleNumber OBJECT-TYPE
    SYNTAX      INTEGER (0..1280)  
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION     
        "tmnxPortBundleNumber identifies the bundle for which this port is
         a member. If this port is not a member of any bundle, the value of
         tmnxPortBundleNumber will be zero."
    ::= { tmnxPortEntry 51 }

tmnxPortIsLeaf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION     
        "tmnxPortIsLeaf is applicable only when the 'isSonet' and/or 'isTDM'
         bits in ALCATEL-IND1-TIMETRA-CHASSIS-MIB:tmnxMDACapabilities are set.
         When the value of this object is 'true', it indicates that the channel 
         is capable of passing traffic.

         The value of this object is 'false' for:
         1. Physical ports on SONET/TDM MDA's
         2. Intermediate channels on a channelized MDA
         3. Leaf channels which belong to a bundle

         For ports on Ethernet MDA's or for LAG ports, the value of this object
         will be 'false'."
    ::= { tmnxPortEntry 52 }

tmnxPortChanType      OBJECT-TYPE
    SYNTAX      TmnxMDAChanType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortChanType is applicable only when the 'isSonet' and/or 'isTDM'
         bits in ALCATEL-IND1-TIMETRA-CHASSIS-MIB:tmnxMDACapabilities are set. It indicates
         the type of the channel.
         For ports on Ethernet MDA's, the value of this object will 
         be 'unknown'."
    ::= { tmnxPortEntry 53 }

tmnxPortParentPortID      OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortParentPortID  is applicable only when the 'isSonet' 
         and/or 'isTDM' bits in ALCATEL-IND1-TIMETRA-CHASSIS-MIB:tmnxMDACapabilities are set.
         For SONET or TDM channel, it indicates the port ID of its parent
         channel or port."
    ::= { tmnxPortEntry 54 }

tmnxPortOpticalCompliance   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortOpticalCompliance is applicable only when the 
         tmnxPortSFPEquipped is set to 'true'. The value of
         tmnxPortOpticalCompliance indicates the 8 bytes of optical compliance 
         bits stored in SFP and XFP eeproms."
    ::= { tmnxPortEntry 55 }


tmnxPortLoadBalanceAlgorithm  OBJECT-TYPE 
    SYNTAX      INTEGER {
                    notApplicable (0),
                    default (1),
                    includeL4 (2),
                    excludeL4 (3)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortLoadBalanceAlgorithm specifies the load
         balancing algorithm to be used on this port. When the
         value is 'includeL4', the src and dst port are used in the 
         hashing algorithm. When it's 'excludeL4', they are not included.
         When the value is 'default', the port inherits the global settings
         in tmnxL4LoadBalancing. The value is 'notApplicable' for ports that
         do not pass any traffic. An attempt to modify this object when it is
         set to 'notApplicable' will return an inconsistentValue error."
    DEFVAL { default }
    ::= { tmnxPortEntry 56 }

tmnxPortEgrPortSchedPlcy  OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortEgrPortSchedPlcy specifies the egress QoS 
         port-scheduler policy for this port. This object must correspond
         to the index of a row in ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyTable."
    DEFVAL { "" }
    ::= { tmnxPortEntry 57 }

tmnxPortLastClearedTime  OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortLastClearedTime indicates the sysUpTime
         when the counters in the IF-MIB:ifTable were last cleared."
    ::= { tmnxPortEntry 58 }

tmnxPortIngNamedPoolPlcy OBJECT-TYPE   
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortIngNamedPoolPlcy specifies a named pool 
         policy associated with an port ingress context. The policy 
         governs the way named pools are created at the port level."
    DEFVAL { ''H }    
    ::= { tmnxPortEntry 60 }

tmnxPortEgrNamedPoolPlcy OBJECT-TYPE   
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortEgrNamedPoolPlcy specifies a named pool 
         policy associated with an port egress context. The policy 
         governs the way named pools are created at the port level."
    DEFVAL { ''H }    
    ::= { tmnxPortEntry 61 }

tmnxPortIngPoolPercentRate OBJECT-TYPE   
    SYNTAX      Unsigned32 (1..1000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortIngPoolPercentRate specifies increment or 
         decrement of the active bandwidth associated with the ingress port.
         This active bandwidth affects the amount of ingress buffer space 
         managed by the port."
    DEFVAL { 100 }    
    ::= { tmnxPortEntry 62 }

tmnxPortEgrPoolPercentRate OBJECT-TYPE   
    SYNTAX      Unsigned32 (1..1000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortEgrPoolPercentRate specifies increment or 
         decrement of the active bandwidth associated with the egress port.
         This active bandwidth affects the amount of egress buffer space 
         managed by the port."
    DEFVAL { 100 }    
    ::= { tmnxPortEntry 63 }

tmnxPortDDMEventSuppression OBJECT-TYPE   
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortDDMEventSuppression specifies whether or not an
         inserted SFP/XFP that supports Digital Diagnostic Monitoring
         raises traps and events (false) or suppresses all notifications
         (true)."
    DEFVAL { false }    
    ::= { tmnxPortEntry 64 }

tmnxPortSFPStatus           OBJECT-TYPE   
    SYNTAX      INTEGER {
                     not-equipped (0),
                     operational  (1),
                     read-error   (2),
                     data-corrupt (3),
                     ddm-corrupt  (4),
                     unsupported  (5)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSFPStatus indicates the operational status of 
         the inserted SFP/XFP. If tmnxPortSFPEquipped is false, the
         tmnxPortSFPStatus will be 'not-equipped (0)', otherwise
         'operational (1)' if no failure is detected.
         
         A failure to read the SFP data will result in a 'read-error (2)',
         while corrupted information on the SFP will result in either
         'data-corrupt (3)', or 'ddm-corrupt (4)' if an SFP read results in
         invalid data or DDM information respectively.  An insertion of an
         unsupported SFP will result in 'unsupported (5)'.

         A trap will be raised if the value of tmnxPortSFPStatus indicates a
         failure; that is the tmnxPortSFPStatus is neither 'not-equipped (0)',
         nor 'operational (1)'."
    DEFVAL { not-equipped }    
    ::= { tmnxPortEntry 65 }

--
--  Port Diagnostic Table
--
tmnxPortTestTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxPortTestEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
         "The tmnxPortTestTable has an entry for each port on each IOM
         card in each chassis in the TMNX system."
    ::= { tmnxPortObjs 3 }

tmnxPortTestEntry       OBJECT-TYPE
    SYNTAX      TmnxPortTestEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a port on a IOM card in a
         chassis in the system.  Entries cannot be created and
         deleted via SNMP SET operations."
    AUGMENTS { tmnxPortEntry }
    ::= { tmnxPortTestTable 1 }

TmnxPortTestEntry ::=
    SEQUENCE {
        tmnxPortTestState           INTEGER,
        tmnxPortTestMode            INTEGER,
        tmnxPortTestParameter       Unsigned32,
        tmnxPortTestLastResult      INTEGER,
        tmnxPortTestStartTime       DateAndTime,
        tmnxPortTestEndTime         DateAndTime,
        tmnxPortTestDuration        INTEGER,
        tmnxPortTestAction          INTEGER
    }

tmnxPortTestState   OBJECT-TYPE
    SYNTAX      INTEGER {
                    notInTest (1),
                    inTest (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current testing status of this port."
    ::= { tmnxPortTestEntry 1 }

tmnxPortTestMode    OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable(0),
                    loopback1 (1),
                    loopback2 (2),
                    loopback3 (3),
                    singalInsertion (4)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The type of test to be executed on this port."
    ::= { tmnxPortTestEntry 2 }

tmnxPortTestParameter       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A parameter to be passed to the test program."
    ::= { tmnxPortTestEntry 3 }

tmnxPortTestLastResult      OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable(0),
                    success (1),
                    failure (2),
                    timeout (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The status of the last test executed on this port."
    ::= { tmnxPortTestEntry 4 }

tmnxPortTestStartTime       OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time the test started on this port.
         Returns 0 if tmnxPortTestState is notInTest."
    ::= { tmnxPortTestEntry 5 }

tmnxPortTestEndTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time the last test completed.  Returns 0
         if a test is in progress or no tests have yet to be run on
         this port."
    ::= { tmnxPortTestEntry 6 }

tmnxPortTestDuration        OBJECT-TYPE
    SYNTAX      INTEGER (0..256)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of seconds the port test is anticipated to last."
    ::= { tmnxPortTestEntry 7 }

tmnxPortTestAction  OBJECT-TYPE
    SYNTAX      INTEGER {
                    none (1),
                    startTest (2),
                    stopTest (3)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The operator sets this variable to perform the appropriate
         type of testing."
    ::= { tmnxPortTestEntry 8 }

--
--  Ethernet Ports Table
--
tmnxPortEtherTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxPortEtherEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherTable has an entry for each Ethernet 
         (faste, gige, xcme or xgige) port on each IOM card in each chassis 
         in the TMNX system."
    ::= { tmnxPortObjs 4 }

tmnxPortEtherEntry       OBJECT-TYPE
    SYNTAX  TmnxPortEtherEntry
    MAX-ACCESS      not-accessible
    STATUS  current
    DESCRIPTION
        "Each row entry represents an Ethernet port on a IOM card in a
         chassis in the system.  Entries cannot be created and
         deleted via SNMP SET operations.  Before an IOM card entry
         can be deleted from the tmnxMDATable, its supported
         tmnxPortEntry and tmnxPortEtherEntry rows must be in the proper
         state for removal.  The tmnxPortEtherEntry contains attributes
         that are unique to the Ethernet TmnxPortType."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxPortEtherTable 1 }

TmnxPortEtherEntry ::=
    SEQUENCE {
        tmnxPortEtherMTU                            Unsigned32,
        tmnxPortEtherDuplex                         INTEGER,
        tmnxPortEtherSpeed                          INTEGER,
        tmnxPortEtherAutoNegotiate                  INTEGER,
        tmnxPortEtherOperDuplex                     INTEGER,
        tmnxPortEtherOperSpeed                      Unsigned32,
        tmnxPortEtherAcctPolicyId                   Unsigned32,
        tmnxPortEtherCollectStats                   TruthValue,        
        tmnxPortEtherMDIMDIX                        INTEGER,
        tmnxPortEtherXGigMode                       INTEGER,
        tmnxPortEtherEgressRate                     Integer32,
        tmnxPortEtherDot1qEtype                     Unsigned32,
        tmnxPortEtherQinqEtype                      Unsigned32,
        tmnxPortEtherIngressRate                    Integer32,
        tmnxPortEtherReportAlarm                    TmnxPortEtherReportStatus,
        tmnxPortEtherReportAlarmStatus              TmnxPortEtherReportStatus,
        tmnxPortEtherPkts1519toMax                  Counter32,
        tmnxPortEtherHCOverPkts1519toMax            Counter32,
        tmnxPortEtherHCPkts1519toMax                Counter64,
        tmnxPortEtherLacpTunnel                     TruthValue,
        tmnxPortEtherDownWhenLoopedEnabled          TruthValue,
        tmnxPortEtherDownWhenLoopedKeepAlive        Unsigned32,
        tmnxPortEtherDownWhenLoopedRetry            Unsigned32,
        tmnxPortEtherDownWhenLoopedState            INTEGER,
        tmnxPortEtherPBBEtype                       Unsigned32,
        tmnxPortEtherReasonDownFlags                BITS

    }

tmnxPortEtherMTU  OBJECT-TYPE
    SYNTAX      Unsigned32 (0|512..9212)
    UNITS       "bytes"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The size of the largest packet which can be sent/received
         on the Ethernet physical interface, specified in octets.  For 
         interfaces that are used for transmitting network datagrams, 
         this is the size of the largest network datagram that can be 
         sent on the interface.  
             
         Setting tmnxPortEtherMTU to a value of zero (0), causes the agent 
         to recalculate the default MTU size which can vary based on the 
         current setting of tmnxPortMode and tmnxPortEncapType variables.  
         Some typical default values are:
                1514 with mode access and encap-type null
                1518 with mode access and encap-type dot1q
                1518 with mode access and encap-type mpls
                9198 with mode network
        "
    ::= { tmnxPortEtherEntry 1 }

tmnxPortEtherDuplex OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable (0),
                    fullDuplex (1),
                    halfDuplex (2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherDuplex variable specifies the duplex mode used by
         the Ethernet interface if tmnxPortEtherAutoNegotiate has a value of
         'false' or 'limited'. If tmnxPortEtherAutoNegotiate has a value of
         'true', the link parameters are negotiated with the far end and
         the tmnxPortEtherDuplex variable is ignored. A value of 'fullDuplex'
         sets the link to full duplex mode.  A value of 'halfDuplex' sets
         the link to half duplex mode. tmnxPortEtherDuplex is only valid on
         Ethernet interfaces that support multiple duplex modes."
    DEFVAL { fullDuplex }
    ::= { tmnxPortEtherEntry 2 }

tmnxPortEtherSpeed  OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable (0),
                    speed10 (1),
                    speed100 (2),
                    speed1000 (3),
                    speed10000 (4)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherSpeed variable specifies the link speed of the
         Ethernet interface if tmnxPortEtherAutoNegotiate has a value of
         'false' or 'limited'.  If tmnxPortEtherAutoNegotiate has a value of
         'true', the link parameters are negotiated with the far end and
         the tmnxPortEtherSpeed variable is ignored. A value of 'speed10' 
         sets the link to 10 mbps.  A value of 'speed100' sets the link to
         100 mbps.  A value of 'speed1000' sets the link to 1000 mbps (1 gbps).
         tmnxPortEtherSpeed is only valid on Ethernet interfaces that support
         multiple link speeds."
    ::= { tmnxPortEtherEntry 3 }
        
tmnxPortEtherAutoNegotiate   OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable (0),
                    true (1),
                    false (2),
                    limited (3)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value of tmnxPortEtherAutoNegotiate is 'true', the Ethernet
         interface will automatically negotiate link parameters with the far
         end (including speed and duplex), and will advertise all speeds and
         duplex modes supported by the interface. When the value of 
         tmnxPortEtherAutoNegotiate is 'limited', the Ethernet interface will
         automatically negotiate link parameters with the far end, but will
         only advertise the speed and duplex mode specified by tmnxPortEtherSpeed
         and tmnxPortEtherDuplex. If tmnxPortEtherAutoNegotiate is 'false',
         the Ethernet interface won't negotiate link parameters with the far
         end and will instead force the speed and duplex mode to the values specified
         by tmnxPortEtherSpeed and tmnxPortEtherDuplex respectively."
    DEFVAL { true }
    ::= { tmnxPortEtherEntry 4 }

tmnxPortEtherOperDuplex OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable (0),
                    fullDuplex (1),
                    halfDuplex (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherOperDuplex variable indicates the operational duplex
         mode of the Ethernet interface. A value of 'fullDuplex' indicates that
         the link is in full duplex mode.  A value of 'halfDuplex' indicates
         that the link is in half duplex mode. tmnxPortEtherOperDuplex is only
         valid if tmnxPortClass is 'faste' or 'xcme'."
    ::= { tmnxPortEtherEntry 5 }

tmnxPortEtherOperSpeed  OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "mega-bits per second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherOperSpeed variable indicates the operational speed
         of the Ethernet interface in mega-bits per second (mbps).  If the value of 
         tmnxPortEtherAutoNegotiate is 'true', the link autonegotiates the link 
         parameters with the far end side.  The value of tmnxPortEtherOperSpeed is 
         only valid if tmnxPortClass is one of the Ethernet classes."
    ::= { tmnxPortEtherEntry 6 }
        
tmnxPortEtherAcctPolicyId  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..99)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of the accounting policy must be defined prior to associating
         the policy with the ethernet port. Accounting policies can only be 
         associated with network ports.
         A non-zero value indicates the tmnxLogApPolicyId index identifying the
         policy entry in the tmnxLogApTable from the TIMETRA-LOG-MIB which is
         associated with this port.  A zero value indicates that there is no
         accounting policy associated with this port"
    DEFVAL { 0 }
    ::= { tmnxPortEtherEntry 7 }

tmnxPortEtherCollectStats        OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/disable the collection of accounting and statistical data
         for the network ethernet port. When applying accounting policies the 
         data by default will be collected in the appropriate records and 
         written to the designated billing file. 
         When the value is set to false, the statistics are still accumulated 
         by the IOM cards, however, the CPU will not obtain the results and
         write them to the billing file."
    DEFVAL { true }
    ::= { tmnxPortEtherEntry 8 }                

tmnxPortEtherMDIMDIX        OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (0),
                    mdi (1),
                    mdix (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherMDIMDIX variable indicates whether the Ethernet 
         interface is of type 'mdi' (Media Dependent Interface) or 
         'mdix' (Media Dependent Interface with crossover). If the agent 
         cannot identify the type of Ethernet interface the value 
         'unknown (0)' is indicated. tmnxPortEtherMDIMDIX is only valid if
         tmnxPortClass is 'faste' or 'xcme'."
    ::= { tmnxPortEtherEntry 9 }

tmnxPortEtherXGigMode        OBJECT-TYPE
    SYNTAX      INTEGER {
                    notApplicable (0),
                    lan (1),
                    wan (2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherXGigMode variable specifies whether the Ethernet 
         interface is in LAN or WAN mode. tmnxPortEtherXGigMode must be
         'notApplicable' if tmnxPortClass is not 'xgige'."
    DEFVAL { lan }
    ::= { tmnxPortEtherEntry 10 }
    
tmnxPortEtherEgressRate        OBJECT-TYPE
    SYNTAX      Integer32 (-1 | 1..10000000) 
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherEgressRate variable specifies the maximum egress
         bandwidth (in kilobits per second) that the Ethernet interface
         can generate. A value of -1 means that the limit is the actual 
         physical limit. If the provisioned sub-rate bandwidth is larger than 
         the actual physical bandwidth of the interface, the latter applies."
    DEFVAL { -1 }
    ::= { tmnxPortEtherEntry 11 }

tmnxPortEtherDot1qEtype        OBJECT-TYPE
    SYNTAX      Unsigned32 ('600'H..'ffff'H)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherDot1qEtype variable indicates the ethertype expected
         when the port's encapsulation type is dot1qEncap."
    DEFVAL { '8100'H }
    ::= { tmnxPortEtherEntry 12 }

tmnxPortEtherQinqEtype         OBJECT-TYPE
    SYNTAX      Unsigned32 ('600'H..'ffff'H)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherQinqEtype variable indicates the ethertype expected
         when the port's encapsulation type is qinqEncap."
    DEFVAL { '8100'H }
    ::= { tmnxPortEtherEntry 13 }

tmnxPortEtherIngressRate        OBJECT-TYPE
    SYNTAX      Integer32 (-1 | 1..10000) 
    UNITS       "mega-bits per second"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherIngressRate variable specifies the maximum ingress
         bandwidth (in mega-bits per second) that the Ethernet interface
         can receive. A value of -1 means that the limit is the actual
         physical limit. If the provisioned sub-rate bandwidth is larger than
         the actual physical bandwidth of the interface, the latter applies."
    DEFVAL { -1 }
    ::= { tmnxPortEtherEntry 14 }

tmnxPortEtherReportAlarm  OBJECT-TYPE
    SYNTAX          TmnxPortEtherReportStatus
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The value of tmnxPortEtherReportAlarm determines when and if to
         generate tmnxEqPortEtherAlarm and tmnxEqPortEtherAlarmClear."
    DEFVAL { { remoteFault, localFault } }
    ::= { tmnxPortEtherEntry 15 }

tmnxPortEtherReportAlarmStatus  OBJECT-TYPE
    SYNTAX          TmnxPortEtherReportStatus
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of tmnxPortEtherReportAlarmStatus indicates the alarms on 
         this port."
    ::= { tmnxPortEtherEntry 16 }

tmnxPortEtherPkts1519toMax   OBJECT-TYPE
    SYNTAX     Counter32
    UNITS      "Packets"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The total number of packets received that were longer than 1518 octets 
         but less than the maximum frame size for the particular medium, usually
         12287 octets (excluding framing bits, but including FCS octets) and were
         otherwise well formed."
    ::= { tmnxPortEtherEntry 17 }

tmnxPortEtherHCOverPkts1519toMax OBJECT-TYPE
    SYNTAX     Counter32
    UNITS      "Packets"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times the associated tmnxPortEtherPkts1519toMax 
        counter has overflowed."
    ::= { tmnxPortEtherEntry 18 }

tmnxPortEtherHCPkts1519toMax OBJECT-TYPE
    SYNTAX     Counter64
    UNITS      "Packets"
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The total number of packets (including bad packets) received that were 
         between 1519 octets in length and the maximum frame size, usually 12287
         octets inclusive (excluding framing bits but including FCS octets).

         The lower 32-bits of this 64-bit counter will equal the value of 
         tmnxPortEtherHCPkts1519toMax.  The high 32-bits of this counter will
         equal the value of tmnxPortEtherHCOverPkts1519toMax."
    ::= { tmnxPortEtherEntry 19 }

tmnxPortEtherLacpTunnel          OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortEtherLacpTunnel specifies whether the LACP
         packet tunneling for the ethernet port is enabled or disabled.
         When tunneling is enabled, the port will not process any LACP 
         packets but will tunnel them through instead. Also, the port 
         cannot be added as a member to a LAG group, and vice versa."
    DEFVAL { false }
    ::= { tmnxPortEtherEntry 20 }

tmnxPortEtherDownWhenLoopedEnabled          OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortEtherDownWhenLoopedEnabled specifies whether 
         physical loop detection for the Ethernet port is enabled or
         disabled.  When enabled, the port will periodically send out
         keep-alive PDUs with an EtherType of 0x9000.  If the port
         receives a keep-alive that it transmitted, tmnxPortState will be
         set to 'linkUp' if it was previously 'up'. The port will not move 
         back to tmnxPortState 'up' for a period of time defined by 
         tmnxPortEtherDownWhenLoopedRetry, but will continue to periodically
         send out keep-alive PDUs.  Every time the port receives a keep alive
         it sent while a loop has been detected, it will reset the time
         period that it will remain down as defined by 
         tmnxPortEtherDownWhenLoopedRetry."
    DEFVAL { false }
    ::= { tmnxPortEtherEntry 21 }

tmnxPortEtherDownWhenLoopedKeepAlive         OBJECT-TYPE
    SYNTAX      Unsigned32 (1..120)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherDownWhenLoopedKeepAlive variable specifies the
         number of seconds between each keep alive PDU transmission."
    DEFVAL { 10 }
    ::= { tmnxPortEtherEntry 22 }

tmnxPortEtherDownWhenLoopedRetry         OBJECT-TYPE
    SYNTAX      Unsigned32 (0|10..160)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherDownWhenLoopedRetry variable specifies the minimum
         number of seconds the port should wait after detecting a loop before 
         tmnxPortState can be set to 'up'.  A value of 0 specifies the the 
         port should not set tmnxPortState to 'up' until the user 
         administratively disables and re-enables the port by setting 
         tmnxPortAdminStatus to 'outOfService' and then to 'inService'."
    DEFVAL { 120 }
    ::= { tmnxPortEtherEntry 23 }

tmnxPortEtherDownWhenLoopedState          OBJECT-TYPE
    SYNTAX      INTEGER {
                    noLoopDetected (1),
                    loopDetected (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortEtherDownWhenLoopedState indicates whether 
         a physical loop has been detected on the port or not.  If
         tmnxPortEtherDownWhenLoopedEnabled is set to 'false', this value
         will be 'noLoopDetected'."
    ::= { tmnxPortEtherEntry 24 }

tmnxPortEtherPBBEtype        OBJECT-TYPE
    SYNTAX      Unsigned32 ('600'H..'ffff'H)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherPBBEtype variable indicates the Ethertype expected
        for Provider Backbone Bridging Frames."
    DEFVAL { '88E7'H }
    ::= { tmnxPortEtherEntry 25 }

tmnxPortEtherReasonDownFlags OBJECT-TYPE
    SYNTAX      BITS {
                   unknown     (0),
                   linklossFwd (1)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxPortEtherReasonDownFlags indicates why an ethernet port may
        be in the operationally 'down' state. The following reasons are
        detected through this object:
                unknown(0)     - Unspecified (or unknown)
                linklossFwd(1) - A sap using this port has the object 
                                 'sapEthernetLLFOperStatus' set to 'fault'"
    ::= { tmnxPortEtherEntry 26 }

--
--  Sonet Ports Table
--
tmnxSonetTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxSonetEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxSonetTable has an entry for each packet over Sonet
         port on each IOM card in each chassis in the TMNX system."
    ::= { tmnxPortObjs 5 }

tmnxSonetEntry       OBJECT-TYPE
    SYNTAX      TmnxSonetEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a packet over Sonet port on a 
         IOM card in a chassis in the system.  Entries cannot be 
         created and deleted via SNMP SET operations.  Before an IOM 
         tmnxMDAEntry can be deleted from the tmnxMDATable, its 
         supported tmnxPortEntry rows must be in the proper state for 
         removal.  The tmnxSonetEntry contains attributes that are 
         unique to the 'sonet' TmnxPortType.  It also contains attributes
         that are common to all sonet paths configured on a sonet port.
         The tmnxPortPortID for a sonet port includes a sonet path number 
         in the lower bits.   If the sonet path number is zero, '0', it 
         represents a single (clear) sonet path on the port that uses 
         the full bandwidth of the sonet port.  All entries in this table 
         have the sonet path number part of their tmnxPortPortID index 
         value set to zero."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxSonetTable 1 }

TmnxSonetEntry ::=
    SEQUENCE {
        tmnxSonetSpeed                INTEGER,
        tmnxSonetClockSource          INTEGER,
        tmnxSonetFraming              INTEGER,
        tmnxSonetReportAlarm          BITS,
        tmnxSonetBerSdThreshold       Unsigned32,
        tmnxSonetBerSfThreshold       Unsigned32,
        tmnxSonetAps                  TruthValue,
        tmnxSonetApsAdminStatus       TmnxPortAdminStatus,
        tmnxSonetApsOperStatus        TmnxPortOperStatus,
        tmnxSonetApsAuthKey           OCTET STRING,
        tmnxSonetApsNeighborAddr      IpAddress,
        tmnxSonetApsAdvertiseInterval TimeInterval,
        tmnxSonetApsAdvertiseTimeLeft TimeInterval,
        tmnxSonetApsHoldTime          TimeInterval,
        tmnxSonetApsHoldTimeLeft      TimeInterval,
        tmnxSonetLoopback             INTEGER,
        tmnxSonetReportAlarmStatus    BITS,
        tmnxSonetSectionTraceMode     INTEGER,
        tmnxSonetJ0String             OCTET STRING,
        tmnxSonetMonS1Byte            Unsigned32,
        tmnxSonetMonJ0String          OCTET STRING,
        tmnxSonetMonK1Byte            Unsigned32,
        tmnxSonetMonK2Byte            Unsigned32,
        tmnxSonetSingleFiber          TruthValue,
        tmnxSonetHoldTimeUp           Unsigned32,
        tmnxSonetHoldTimeDown         Unsigned32

    }

tmnxSonetSpeed    OBJECT-TYPE
    SYNTAX      INTEGER {
                    oc3 (1),
                    oc12 (2),
                    oc48 (3),
                    oc192 (4),
                    oc768 (5),
                    oc1 (6)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The rate of this optical facility."
    ::= { tmnxSonetEntry 1 }

tmnxSonetClockSource  OBJECT-TYPE
    SYNTAX      INTEGER {
                    loopTimed (1),
                    nodeTimed (2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "tmnxSonetClockSource configures the clock for transmitted data from 
         either the internal clock ('nodeTimed') or from a clock recovered 
         from the line's receive data stream ('loopTimed')."
    DEFVAL { loopTimed }
    ::= { tmnxSonetEntry 2 }

tmnxSonetFraming      OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (1),
                    sonet (2),
                    sdh (3)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxSonetFraming indicates the type of framing used
         on this interface."
    DEFVAL { sonet }
    ::= { tmnxSonetEntry 3 }

tmnxSonetReportAlarm  OBJECT-TYPE
    SYNTAX      BITS {
                    notUsed (0),
                    loc(1),
                    lais(2),
                    lrdi(3),
                    ss1f(4),
                    sb1err(5),
                    lb2erSd(6),
                    lb2erSf(7),
                    slof(8),
                    slos(9),
                    stxptr(10),
                    srxptr(11),
                    lrei(12)
                }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION
        "The value of tmnxSonetReportAlarm determines when and if to
         generate tmnxEqPortSonetAlarm and tmnxEqPortSonetAlarmClear 
         notifications for this port:
            loc(1)      Reports a loss of clock which causes the operational 
                        state of the port to be downed.  Set by default. 
            lais(2)     Reports line alarm indication signal errors. Not set
                        by default. 
            lrdi(3)     Reports line remote defect initiation errors.  LRDIs
                        are caused by remote LOF, LOC, and LOS.  Set by default.
            ss1f(4)     Reports section synchronization failure as reported by
                        the S1 byte.  Not set by default.
            sb1err(5)   Reports section B1 errors.  Not set by default.
            lb2erSd(6)  Reports line signal degradation BER errors.  Not set
                        by default.
            lb2erSf(7)  Reports line signal failure BER errors.  Set by default.
            slof(8)     Reports section loss of frame errors.  Set by default
            slos(9)     Reports section loss of signal errors. Set by default.
            stxptr(10)  Reports a section synchronization error on the transmit
                        side.  Indicates if there is a positive or negative
                        justification count per channel.  Not set by default.
            srxptr(11)  Reports a section synchronization error on the receive
                        side.  Indicates if there is a positive or negative
                        justification count per path.  Not set by default.
            lrei(12)    Reports a line error condition raised by the remote
                        as a result of B1 errors received from this node.
                        Not set by default.
        "
    DEFVAL { {loc, lrdi, lb2erSf, slof, slos} }
    ::= { tmnxSonetEntry 4 }

tmnxSonetBerSdThreshold       OBJECT-TYPE
    SYNTAX      Unsigned32 (3..9)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxSonetBerSdThreshold specifies a bit error rate (BER)
         threshold used to determine when to send a tmnxEqPortSonetAlarm 
         notification for a BER SD failure and tmnxEqPortSonetAlarmClear
         notification for a BER SD failure clear.  tmnxSonetBerSdThreshold is 
         the absolute value of the exponent of the rate expressed as 10e-n."
    DEFVAL { 6 }
    ::= { tmnxSonetEntry 5 } 

tmnxSonetBerSfThreshold       OBJECT-TYPE
    SYNTAX      Unsigned32 (3..6)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxSonetBerSfThreshold specifies a bit error rate (BER)
         threshold used to determine when to send a tmnxEqPortSonetAlarm 
         notification for a BER SF failure and tmnxEqPortSonetAlarmClear
         notification for a BER SF failure clear.  tmnxSonetBerSdThreshold is 
         the absolute value of the exponent of the rate expressed as 10e-n."
    DEFVAL { 3 }
    ::= { tmnxSonetEntry 6 } 

tmnxSonetAps      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    DEFVAL { false }
    ::= { tmnxSonetEntry 7 }

tmnxSonetApsAdminStatus   OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    DEFVAL { inService }
    ::= { tmnxSonetEntry 8 }

tmnxSonetApsOperStatus    OBJECT-TYPE
    SYNTAX      TmnxPortOperStatus
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    ::= { tmnxSonetEntry 9 }

tmnxSonetApsAuthKey OBJECT-TYPE
    SYNTAX       OCTET STRING (SIZE (0..256))
    MAX-ACCESS   read-write
    STATUS       obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    DEFVAL { '0000000000000000'H }
    ::= { tmnxSonetEntry 10 }

tmnxSonetApsNeighborAddr      OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    DEFVAL { '00000000'H }
    ::= { tmnxSonetEntry 11 }

tmnxSonetApsAdvertiseInterval     OBJECT-TYPE
    SYNTAX      TimeInterval
    UNITS       "milliseconds"
    MAX-ACCESS  read-write
    STATUS      obsolete
     DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    DEFVAL { 1000 }
    ::= { tmnxSonetEntry 12 }
        
tmnxSonetApsAdvertiseTimeLeft  OBJECT-TYPE
    SYNTAX      TimeInterval
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    ::= { tmnxSonetEntry 13 }

tmnxSonetApsHoldTime      OBJECT-TYPE
    SYNTAX      TimeInterval
    UNITS       "milliseconds"
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
    DEFVAL { 3000 }
    ::= { tmnxSonetEntry 14 }
        
tmnxSonetApsHoldTimeLeft   OBJECT-TYPE
    SYNTAX      TimeInterval
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Automatic Protection Switching (APS) is supported on 7x50 systems
         by the APS-MIB and TIMETRA-APS-MIB."
   ::= { tmnxSonetEntry 15 }

tmnxSonetLoopback   OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(0),
                    line(1),
                    internal(2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Activate a loopback on the SONET port. The SONET port must be in a
        shutdown state to activate any type of loopback."
   ::= { tmnxSonetEntry 16 }

tmnxSonetReportAlarmStatus  OBJECT-TYPE
    SYNTAX      BITS {
                    notUsed (0),
                    loc(1),
                    lais(2),
                    lrdi(3),
                    ss1f(4),
                    sb1err(5),
                    lb2erSd(6),
                    lb2erSf(7),
                    slof(8),
                    slos(9),
                    stxptr(10),
                    srxptr(11),
                    lrei(12)
                }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The bits in this object are used for indicating the alarms. The bits
         are explained as follows:
            loc(1)      Indicates a loss of clock which causes the operational 
                        state of the port to be downed. 
            lais(2)     Indicates line alarm indication signal errors. 
            lrdi(3)     Indicates line remote defect indication errors.  LRDIs
                        are caused by remote LOF, LOC, and LOS.
            ss1f(4)     Indicates section synchronization failure as reported by
                        the S1 byte.
            sb1err(5)   Indicates section B1 errors.
            lb2erSd(6)  Indicates line signal degradation BER errors.
            lb2erSf(7)  Indicates line signal failure BER errors.
            slof(8)     Indicates section loss of frame errors.
            slos(9)     Indicates section loss of signal errors.
            stxptr(10)  Indicates a section synchronization error on the 
                        transmit side. Indicates if there is a positive or 
                        negative justification count per channel.
            srxptr(11)  Indicates a section synchronization error on the receive
                        side.  Indicates if there is a positive or negative
                        justification count per sonet path.
            lrei(12)    Indicates a line error condition raised by the remote
                        as a result of B1 errors received from this node.
        "
    ::= { tmnxSonetEntry 17 }

tmnxSonetSectionTraceMode  OBJECT-TYPE
    SYNTAX      INTEGER {
                    increment-z0(1),
                    byte(2),
                    string(3)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxSonetSectionTraceMode variable along with the
        tmnxSonetJ0String object determines the contents of
        the section trace bytes (j0/z0) in the SONET Section Header. If the
        mode is set to 'increment-z0', then the j0 byte is 0x01, and the
        z0 byte increments. If the mode is set to 'byte', then the j0 byte is
        the first octet of the tmnxSonetJ0String object and the z0 byte
        is 0xcc. If the mode is set to 'string', then the J0 byte is set to
        the 16 values in the tmnxSonetJ0String object and the z0 byte is
        0xcc."
    DEFVAL { byte }
    ::= { tmnxSonetEntry 18 }

tmnxSonetJ0String   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The tmnxSonetJ0String is used with the tmnxSonetSectionTraceMode
        variable to determine the contents of the j0 byte in the SONET Section
        Header. If the tmnxSonetSectionTraceMode is increment-z0, this object
        is ignored. If the mode is byte, then the first octet of this object is
        used for the J0 byte. If the mode is string, then the 16 bytes in this
        object are used for the J0 byte. If set to less than 16 bytes it, will
        be padded out to 16 with zeros. The default value is 0x01 and 15 NULLs"
    DEFVAL { '01'H }
    ::= { tmnxSonetEntry 19 }

tmnxSonetMonS1Byte     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxSonetMonS1Byte variable reports the Synchronization Status
        Message from the S1 byte in the Line Overhead Header. A value of 0x00 
        indicates unknown quality. A value of 0xF0 indicates 
        `Do not use for synchronization`. Other values are documented in 
        Tellcordia GR253 and ITU G.707"
    DEFVAL { 'cc'H }
    ::= { tmnxSonetEntry 20 }        

tmnxSonetMonJ0String   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxSonetMonJ0String variable reports the contents of the j0 byte 
         in the SONET Section Header."
    ::= { tmnxSonetEntry 21 }

tmnxSonetMonK1Byte     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxSonetMonK1Byte variable reports the Automatic Protection 
         Switching(APS) signalling status from the K1 byte in the SONET
         Line Overhead Header."
    ::= { tmnxSonetEntry 22 }        

tmnxSonetMonK2Byte     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxSonetMonK1Byte variable reports the Automatic Protection 
         Switching(APS) signalling status from the K2 byte in the SONET
         Line Overhead Header."
    ::= { tmnxSonetEntry 23 }        

tmnxSonetSingleFiber     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "tmnxSonetSingleFiber is used to enable/disable packet gathering and 
         redirection of IP packets from a single fiber on RX port of the 
         SONET interface and redistribute packets to other interfaces 
         through either state routes or policy-based forwarding."
    DEFVAL { false }
    ::= { tmnxSonetEntry 24 }        

tmnxSonetHoldTimeUp  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    UNITS       "100s of milliseconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxSonetHoldTimeUp is used to configure the 
         hold-timer for link up event dampening. This guards against 
         reporting excessive interface transitions. This is implemented 
         by not advertising subsequent transitions of the interface to 
         upper layer protocols until the configured timer has expired. 
         A value of zero (0) indicates that an up transition is reported
         immediately."
    DEFVAL { 5 }
    ::= { tmnxSonetEntry 25 }

tmnxSonetHoldTimeDown    OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    UNITS       "100s of milliseconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxSonetHoldTimeDown is used to configure the 
         hold-timer for link down event dampening. This guards against 
         reporting excessive interface transitions. This is implemented 
         by not advertising subsequent transitions of the interface to 
         upper layer protocols until the configured timer has expired. 
         A value of zero (0) indicates that a down transition is reported
         immediately."
    DEFVAL { 0 }
    ::= { tmnxSonetEntry 26 }

--
--  Sonet Path Table
--
tmnxSonetPathTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxSonetPathEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxSonetPathTable has an entry for each sonet path
         configured in each packet over Sonet port on each IOM card 
         in each chassis in the TMNX system."
    ::= { tmnxPortObjs 6 }

tmnxSonetPathEntry       OBJECT-TYPE
    SYNTAX      TmnxSonetPathEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a configured sonet path in a packet 
         over Sonet port on a IOM card in a chassis in the system.
         The tmnxSonetPathEntry contains attributes that are 
         unique to a sonet path of a 'sonet' TmnxPortType. The 
         tmnxPortPortID for a sonet port includes a sonet path number 
         in the lower bits.  If the sonet path number is zero, '0', it 
         represents a single (clear) sonet path on the port that uses 
         the full bandwidth of the sonet port.  Entries in this table 
         that have the sonet path number part of their tmnxPortPortID 
         index value set to zero cannot be created and deleted via 
         SNMP SET operations.  They are created by the SNMP agent 
         when the corresponding entry in the tmnxSonetTable is 
         created.  
                 
         Entries with non-zero sonet path numbers can be created and 
         deleted via SNMP SET operations using tmnxSonetPathRowStatus.
         When a row is created in the tmnxSonetPathTable, the
         agent also creates a row with the same index values in the
         tmnxPortTable.  In order to delete an entry, 
         tmnxPortAdminStatus must first be set to 'outOfService'.  When
         the tmnxSonetPathEntry is deleted, the agent also deletes
         the corresponding row in the tmnxPortTable."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxSonetPathTable 1 }

TmnxSonetPathEntry ::=
    SEQUENCE {
        tmnxSonetPathRowStatus          RowStatus,
        tmnxSonetPathLastChangeTime     TimeStamp,
        tmnxSonetPathMTU                Unsigned32,
        tmnxSonetPathScramble           TruthValue,
        tmnxSonetPathC2Byte             Unsigned32,
        tmnxSonetPathJ1String           OCTET STRING,
        tmnxSonetPathCRC                INTEGER,
        tmnxSonetPathOperMTU            Unsigned32,
        tmnxSonetPathOperMRU            Unsigned32,
        tmnxSonetPathReportAlarm        BITS,
        tmnxSonetPathAcctPolicyId       Unsigned32,
        tmnxSonetPathCollectStats       TruthValue,
        tmnxSonetPathReportAlarmStatus  BITS,
        tmnxSonetPathMonC2Byte          Unsigned32,
        tmnxSonetPathMonJ1String        OCTET STRING,
        tmnxSonetPathType                INTEGER,
        tmnxSonetPathChildType          TmnxMDAChanType
    }

tmnxSonetPathRowStatus        OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxSonetPathRowStatus controls the creation and deletion of
         row entries in the tmnxSonetPathTable. The tmnxPortPortID 
         for a sonet port includes a sonet path number in the lower bits.  
         If the sonet path number is zero, '0', it represents a single 
         (clear) sonet path on the port that uses the full bandwidth of 
         the sonet port.  Entries in this table that have the sonet path 
         number part of their tmnxPortPortID index value set to zero 
         cannot be created and deleted via SNMP SET operations. They
         are created by the SNMP agent when the corresponding entry in
         the tmnxSonetTable is created. 
             
         Entries with non-zero sonet path numbers can be created and deleted 
         via SNMP SET operations.  When a row is created in the 
         tmnxSonetPathTable, the agent also creates a row with the 
         same index values in the tmnxPortTable.  In order to delete an 
         entry, tmnxPortAdminStatus must first be set to 'outOfService'.  
         When the tmnxSonetPathEntry is deleted, the agent also 
         deletes the corresponding row in the tmnxPortTable for this
         sonet path port."
    ::= { tmnxSonetPathEntry 1 }

tmnxSonetPathLastChangeTime    OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxSonetPathLastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxSonetPathEntry row for this sonet path."
    ::= { tmnxSonetPathEntry 2 }
                        
tmnxSonetPathMTU  OBJECT-TYPE
    SYNTAX      Unsigned32 (0|512..9208)
    UNITS       "bytes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The size of the largest packet which can be sent/received
         on the sonet path, specified in octets.  For paths that
         are used for transmitting network datagrams, this is the
         size of the largest network datagram that can be sent on the
         sonet path.
            
         Setting tmnxSonetPathMTU to a value of zero (0), causes the agent 
         to recalculate the default MTU size which can vary based on the 
         current setting of tmnxPortMode and tmnxPortEncapType variables.  
         Some typical default values are:
                1522 with mode access and encap-type bcp-null
                1526 with mode access and encap-type bcp-dot1q
                1502 with mode access and encap-type ipcp
                1506 with mode access and encap-type mplscp
                1524 with mode access and encap-type atm
                9208 with mode network
        "
    ::= { tmnxSonetPathEntry 3 }

tmnxSonetPathScramble     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "If the tmnxSonetPathScramble variable is set to 'true', 
         SONET (SDH) payload scrambling is enabled on this sonet path.  
         Both ends of the connection must use the same scrambling 
         algorithm.  If tmnxSonetPathScramble is set to 'false', 
         scrambling is disabled.
         The default value for non-ATM paths is 'false'.
         The default value for ATM paths is 'true'

         If the value of tmnxSonetPathC2Byte is set to the default for
         the currently configured tmnxSonetPathScramble, then changing 
         the value of tmnxSonetPathScramble  for non-ATM paths causes a change
         to tmnxSonetPathC2Byte to a default value for a new scrambling 
         option. The default values are as follows:
         scrambled non-ATM sonet/sdh path - C2 Byte value is 0x16
         unscrambled non-ATM sonet/sdh path - C2 Byte value is 0xCF"
    ::= { tmnxSonetPathEntry 4 }

tmnxSonetPathC2Byte     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..254)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The tmnxSonetPathC2Byte variable is used configure the value
         used in the SONET/SDH C2 header byte. 
         
         For paths on an MDA that does not support channelization:
         Setting C2 equal to 0x16 indicates scrambled sonet.

         Setting C2 equal to 0xCF indicates unscrambled sonet.

         Setting C2 equal to 0x13 indicates ATM.

         For paths on an MDA that supports channelization, the default
         value is based on the value of tmnxMDAMaxChannelization. 
         For 'pdhDs3' or 'pdhE3' channelization, the default value is 0x04.

         Setting tmnxSonetPathC2Byte to a value of zero(0) causes the agent
         to assign the default value to this object."
    ::= { tmnxSonetPathEntry 5 }
            
tmnxSonetPathJ1String   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..62))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The tmnxSonetPathJ1String variable is used to configure the sonet path
        trace string to be sent in the J1 Path Overhead bytes. The bytes
        must be printable ASCII. If the string is less than 62 bytes, it
        will be padded with NULLs. For SONET framing, an additional CR (0x0D)
        / LF (0x0A) pair will be added by the driver. For SDH framing only
        16 bytes are sent in the J1 bytes. The driver will set the MSB of 
        the first byte, and put the first 15 bytes of the user configured string 
        as the following bytes. The driver will set the CRC-7 of the 16 bytes 
        into the other 7 bits of the first byte. 

        The default value is `Alcatel 7x50 SR`. 
        Setting a 1-byte long string containing 0xFF will restore the default. 
        Setting the string 'zeros' will send all-zeros (ASCII NULL characters 
        without CR/LF or CRC-7)in the J1 bytes."
    ::= { tmnxSonetPathEntry 6 } 
            
tmnxSonetPathCRC  OBJECT-TYPE
    SYNTAX      INTEGER {
                    crc16 (1),
                    crc32 (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxSonetPathCRC variable indicates the precision of
         the cyclic redundancy check.  A value of 'crc16' is a 16-bit
         CRC calculation.  A value of 'crc32' is a 32-bit CRC
         calculation.  32-bit CRC increases the error detection ability,
         but it also adds some performance overhead.

         For paths with encapsulation set to ATM the default and only 
         supported value is crc32 and applies to AAL5 CRC calculations
         on that path."
    DEFVAL { crc32 }
    ::= { tmnxSonetPathEntry 7 }

tmnxSonetPathOperMTU        OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The negotiated size of the largest packet which can be sent on 
         the sonet path, specified in octets.  For sonet paths that are used 
         for transmitting network datagrams, this is the size of the largest
         network datagram that can be sent on the sonet path."
    ::= { tmnxSonetPathEntry 8 }

tmnxSonetPathOperMRU        OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The negotiated size of the largest packet that can be received
         on the sonet path, specified in octets."
    ::= { tmnxSonetPathEntry 9 }

tmnxSonetPathReportAlarm        OBJECT-TYPE
    SYNTAX      BITS {
                    notUsed (0),
                    pais(1),
                    plop(2),
                    prdi(3),
                    pb3err(4),
                    pplm(5),
                    prei(6),
                    puneq(7),
                    plcd(8)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxSonetPathReportAlarm determines when and if to
         generate tmnxEqPortSonetPathAlarm and tmnxEqPortSonetPathAlarmClear 
         notifications for this port:
            pais(1)     Reports path alarm indication signal errors. Not set by
                        default.
            plop(2)     Reports path loss of pointer (per tributary) errors.
                        Set by default.
            prdi(3)     Reports path remote defect indication errors.  Not set
                        by default.
            pb3err(4)   Reports path B3 errors.  Not set by default.
            pplm(5)     Reports a path payload mismatch.  As a result the
                        path will be operationally downed.  Set by default.
            prei(6)     Reports a path error condition raised by the remote as
                        a result of B3 errors received from this node.  Not
                        set by default.
            puneq(7)    Reports unequipped path errors. Set by default.
            plcd(8)     Reports path loss of codegroup delineation error. 
                        It is applicable only when the value of 
                        tmnxPortEtherXGigMode is set to 'wan'. 
                        Not set by default.
        "
    DEFVAL { {plop, pplm, puneq} }
    ::= { tmnxSonetPathEntry 10 }

tmnxSonetPathAcctPolicyId  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..99)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the accounting policy must be defined prior to associating
         the policy with the sonet path. Accounting policies can only be 
         associated with network sonet path.
         A non-zero value indicates the tmnxLogApPolicyId index identifying the
         policy entry in the tmnxLogApTable from the TIMETRA-LOG-MIB which is
         associated with this port.  A zero value indicates that there is no
         accounting policy associated with this port"
    DEFVAL { 0 }
    ::= { tmnxSonetPathEntry 11 }

tmnxSonetPathCollectStats        OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Enable/disable the collection of accounting and statistical data
         for the network sonet path. When applying accounting policies the 
         data by default will be collected in the appropriate records and 
         written to the designated billing file. 
         When the value is set to false, the statistics are still accumulated 
         by the IOM cards, however, the CPU will not obtain the results and
         write them to the billing file."
    DEFVAL { true }
    ::= { tmnxSonetPathEntry 12 }

tmnxSonetPathReportAlarmStatus        OBJECT-TYPE
    SYNTAX      BITS {
                    notUsed (0),
                    pais(1),
                    plop(2),
                    prdi(3),
                    pb3err(4),
                    pplm(5),
                    prei(6),
                    puneq(7),
                    plcd(8)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The bits in this object are used for indicating the alarms. The bits
         are explained as follows:
            pais(1)     Indicates path alarm indication signal errors.
            plop(2)     Indicates path loss of pointer (per tributary) errors.
            prdi(3)     Indicates path remote defect indication errors.
            pb3err(4)   Indicates path B3 errors.
            pplm(5)     Indicates a path payload mismatch.
            prei(6)     Indicates a path error condition raised by the remote as
                        a result of B3 errors received from this node.
            puneq(7)    Indicates a far-end unequipped error.
            plcd(8)     Indicates a path loss of codegroup delineation error.
                        It is applicable only when the value of 
                        tmnxPortEtherXGigMode is set to 'wan'. 
                        Not set by default.
        "
    ::= { tmnxSonetPathEntry 13 }

tmnxSonetPathMonC2Byte     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxSonetPathC2Byte variable reports the value in the SONET/SDH 
         C2 header byte."
    ::= { tmnxSonetPathEntry 14 }

tmnxSonetPathMonJ1String   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxSonetPathJ1String variable reports the sonet path
         trace string received in the J1 Path Overhead bytes."
    ::= { tmnxSonetPathEntry 15 } 

tmnxSonetPathType       OBJECT-TYPE
    SYNTAX      INTEGER {
                    ds3 (1),
                    e3  (2),
                    vtg (3),
                    tug-2 (4),
                    tug-3 (5)
                }
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "tmnxSonetPathType specifies if the associated SONET/SDH Path 
         is an asynchronous circuit, a virtual tributary group or 
         a tributary unit group.
            ds3   - Configures the port or channel as service access(ds3)
            e3    - Configures the port or channel as service access(e3)
            vtg   - Configures the path as a virtual tributary group.
            tug-2 - Configures the path as a tributary unit group.
            tug-3 - Configures the port or channel for transport network use."
    DEFVAL       { ds3 }
    ::= { tmnxSonetPathEntry 16 }

tmnxSonetPathChildType      OBJECT-TYPE
    SYNTAX      TmnxMDAChanType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxSonetPathChildType is used to configure the type of this path.
         Some typical default values are:
           pdhDs3 for STS-1 paths
           pdhE3 for TU3 paths
           pdhE1 for VT2 paths
           pdhDs1 for VT15 paths
         Examples of valid child path types are sonetSts3, sdhTug3,
         pdhDs3, pdhE3, sonetVt15, sonetVt2, pdhDs1 and pdhE1."
    ::= { tmnxSonetPathEntry 17 }
--
--  Alcatel 7x50 SR series Port Type Definition Table
--
tmnxPortTypeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Port type table has an entry for each Alcatel 7x50 SR series
         port model."
    ::= { tmnxPortObjs 7 }

tmnxPortTypeEntry    OBJECT-TYPE
    SYNTAX      TmnxPortTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7x50 SR series port model.  
         Rows in this table are created by the agent at initialization and
         cannot be created or destroyed by SNMP Get or Set requests."
    INDEX   { tmnxPortTypeIndex }
    ::= { tmnxPortTypeTable 1 }

TmnxPortTypeEntry ::=
    SEQUENCE {
        tmnxPortTypeIndex        TmnxPortType,
        tmnxPortTypeName         TNamedItemOrEmpty,
        tmnxPortTypeDescription  TItemDescription,
        tmnxPortTypeStatus       TruthValue
    }

tmnxPortTypeIndex    OBJECT-TYPE
    SYNTAX      TmnxPortType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique index value which identifies this type of Alcatel 7x50
         SR series port model."
    ::= { tmnxPortTypeEntry 1 }
        
tmnxPortTypeName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The administrative name that identifies this type of Alcatel
         7x50 SR series port model.  This name string may be used in CLI 
         commands to specify a particular Port model type."
    ::= { tmnxPortTypeEntry 2 }

tmnxPortTypeDescription  OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A detailed description of this Alcatel 7x50 SR series port model."
    ::= { tmnxPortTypeEntry 3 }         

tmnxPortTypeStatus       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxPortTypeStatus has a value of 'true' it indicates that
         this port model is supported in this revision of the management
         software.  When it has a value of 'false' there is no support."
    ::= { tmnxPortTypeEntry 4 }

--
--  Alcatel 7x50 SR series Port Connector Type Definition Table
--
tmnxPortConnectTypeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortConnectTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Port Connector type table has an entry for each Alcatel 
         7x50 SR series port connector model."
    ::= { tmnxPortObjs 8 }

tmnxPortConnectTypeEntry    OBJECT-TYPE
    SYNTAX      TmnxPortConnectTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7x50 SR series port 
         Connector model.  Rows in this table are created by the agent 
         at initialization and cannot be created or destroyed by SNMP 
         Get or Set requests."
    INDEX   { tmnxPortConnectTypeIndex }
    ::= { tmnxPortConnectTypeTable 1 }

TmnxPortConnectTypeEntry ::=
    SEQUENCE {
        tmnxPortConnectTypeIndex        TmnxPortConnectorType,
        tmnxPortConnectTypeName         TNamedItemOrEmpty,
        tmnxPortConnectTypeDescription  TItemDescription,
        tmnxPortConnectTypeStatus       TruthValue
    }

tmnxPortConnectTypeIndex    OBJECT-TYPE
    SYNTAX      TmnxPortConnectorType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique index value which identifies this type of Alcatel
         7x50 SR series port connector model."
    ::= { tmnxPortConnectTypeEntry 1 }
        
tmnxPortConnectTypeName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The administrative name that identifies this type of Alcatel
         7x50 SR series port connector model.  This name string may be 
         used in CLI commands to specify a particular port connector 
         model type."
    ::= { tmnxPortConnectTypeEntry 2 }

tmnxPortConnectTypeDescription  OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A detailed description of this Alcatel 7x50 SR series port 
         connector model."
    ::= { tmnxPortConnectTypeEntry 3 }         

tmnxPortConnectTypeStatus       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxPortConnectTypeStatus has a value of 'true' it indicates that
         this port connector model is supported in this revision of the 
         chassis management software.  When it has a value of 'false' there 
         is no support."
    ::= { tmnxPortConnectTypeEntry 4 }
        
--
--  Network Port FC Stats Table
--
tmnxPortFCStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortNetworkFCStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The Network Port FC Stats table has an entry for each forwarding 
         class defined on each network port."
    ::= { tmnxPortObjs 9 }

tmnxPortFCStatsEntry    OBJECT-TYPE
    SYNTAX      TmnxPortNetworkFCStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "Each row entry represents a set of ingress and egress packet and
         octet statistics for the given network port and forwarding class.
         Rows in this table are created by the agent when the network port
         is provisioned and cannot be created or destroyed by SNMP Get or
         Set requests."
    INDEX   { tmnxChassisIndex, tmnxPortPortID, tmnxPortFCStatsIndex }
    ::= { tmnxPortFCStatsTable 1 }

TmnxPortNetworkFCStatsEntry ::=
    SEQUENCE {
        tmnxPortFCStatsIndex             TFCName,
        tmnxPortFCStatsIngFwdInProfPkts  Counter64,
        tmnxPortFCStatsIngFwdOutProfPkts Counter64,
        tmnxPortFCStatsIngFwdInProfOcts  Counter64,
        tmnxPortFCStatsIngFwdOutProfOcts Counter64,
        tmnxPortFCStatsIngDroInProfPkts  Counter64,
        tmnxPortFCStatsIngDroOutProfPkts Counter64,
        tmnxPortFCStatsIngDroInProfOcts  Counter64,
        tmnxPortFCStatsIngDroOutProfOcts Counter64,
        tmnxPortFCStatsEgrFwdInProfPkts  Counter64,
        tmnxPortFCStatsEgrFwdOutProfPkts Counter64,
        tmnxPortFCStatsEgrFwdInProfOcts  Counter64,
        tmnxPortFCStatsEgrFwdOutProfOcts Counter64,
        tmnxPortFCStatsEgrDroInProfPkts  Counter64,
        tmnxPortFCStatsEgrDroOutProfPkts Counter64,
        tmnxPortFCStatsEgrDroInProfOcts  Counter64,
        tmnxPortFCStatsEgrDroOutProfOcts Counter64
    }

tmnxPortFCStatsIndex OBJECT-TYPE
    SYNTAX      TFCName
    MAX-ACCESS  not-accessible
    STATUS      obsolete
    DESCRIPTION
        "The forwarding class associated with these stats
         on this network port."
    ::= { tmnxPortFCStatsEntry 1 }

tmnxPortFCStatsIngFwdInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of in-profile packets (rate below CIR)
         forwarded by the ingress Qchip."
    ::= { tmnxPortFCStatsEntry 2 }

tmnxPortFCStatsIngFwdOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile packets (rate above CIR)
         forwarded by the ingress Qchip."
    ::= { tmnxPortFCStatsEntry 3 }

tmnxPortFCStatsIngFwdInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of in-profile octets (rate below CIR)
         forwarded by the ingress Qchip."
    ::= { tmnxPortFCStatsEntry 4 }

tmnxPortFCStatsIngFwdOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile octets (rate above CIR)
         forwarded by the ingress Qchip."
    ::= { tmnxPortFCStatsEntry 5 }

tmnxPortFCStatsIngDroInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION     
        "The number of in-profile packets (rate below CIR)
         dropped by the ingress Qchip due to: MBS exceeded,
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 6 }

tmnxPortFCStatsIngDroOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile packets (rate above CIR)
         dropped by the ingress Qchip due to: MBS exceeded, 
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 7 }

tmnxPortFCStatsIngDroInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of in-profile octets (rate below CIR)
         dropped by the ingress Qchip due to: MBS exceeded,
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 8 }

tmnxPortFCStatsIngDroOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile octets (rate above CIR)
         dropped by the ingress Qchip due to: MBS exceeded,
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 9 }

tmnxPortFCStatsEgrFwdInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of in-profile packets (rate below CIR)
         forwarded by the egress Qchip."
    ::= { tmnxPortFCStatsEntry 10 }

tmnxPortFCStatsEgrFwdOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile packets (rate above CIR)
         forwarded by the egress Qchip."
    ::= { tmnxPortFCStatsEntry 11 }

tmnxPortFCStatsEgrFwdInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of in-profile octets (rate below CIR)
         forwarded by the egress Qchip."
    ::= { tmnxPortFCStatsEntry 12 }

tmnxPortFCStatsEgrFwdOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile octets (rate above CIR)
         forwarded by the egress Qchip."
    ::= { tmnxPortFCStatsEntry 13 }

tmnxPortFCStatsEgrDroInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of in-profile packets (rate below CIR)
         dropped by the egress Qchip due to: MBS exceeded,
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 14 }

tmnxPortFCStatsEgrDroOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile packets (rate above CIR)
         dropped by the egress Qchip due to: MBS exceeded, 
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 15 }

tmnxPortFCStatsEgrDroInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of in-profile octets (rate below CIR)
         dropped by the egress Qchip due to: MBS exceeded,
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 16 }

tmnxPortFCStatsEgrDroOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The number of out-of-profile octets (rate above CIR)
         dropped by the egress Qchip due to: MBS exceeded,
         buffer pool limit exceeded, etc."
    ::= { tmnxPortFCStatsEntry 17 }

--
-- DS3 table
--
tmnxDS3Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxDS3Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxDS3Table has an entry for a DS3 physical port."
    ::= { tmnxPortObjs 10 }

tmnxDS3Entry       OBJECT-TYPE
    SYNTAX      TmnxDS3Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a physical DS3 port on a 
         IOM card in a chassis in the system.  Entries cannot be created 
         and deleted via SNMP SET operations.  Entries in this table will 
         be created automatically when the tmnxMDAAssignedType object is 
         set to the DS3 MDA type The tmnxDS3Entry contains attributes that are 
         unique to the 'ds3e3' TmnxPortType. The tmnxPortPortID contains
         the slot, mda and port numbers encoded into it.

         For each tmnxDS3Entry, there will be a corresponding entry 
         in the tmnxPortTable and the ifTable."
    INDEX       { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxDS3Table 1 }

TmnxDS3Entry ::=
    SEQUENCE {
        tmnxDS3Buildout                 INTEGER,
        tmnxDS3LastChangeTime           TimeStamp,
        tmnxDS3Type                     INTEGER
    }

tmnxDS3Buildout    OBJECT-TYPE
    SYNTAX      INTEGER {
                    short (1),
                    long  (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3Buildout configures the T3 line buildout. A T3 port has two 
         settings for the T3 line buildout: a short setting, which is less 
         than 225 feet, and a long setting, which is greater than 225 feet. 
         This object applies to copper-cable-based T3 ports only."
    DEFVAL      { short }
    ::= { tmnxDS3Entry 1 }

tmnxDS3LastChangeTime    OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxDS3LastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxDS3Entry row for this port."
    ::= { tmnxDS3Entry 2 }

tmnxDS3Type     OBJECT-TYPE
    SYNTAX      INTEGER {
                    ds3(1),
                    e3(2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3Type configures the type of the physical port to 'ds3' or 'e3'."
    DEFVAL      { ds3 }
    ::= { tmnxDS3Entry 3 }


--
-- DS3 Channel table
--
tmnxDS3ChannelTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxDS3ChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxDS3ChannelTable has an entry for a DS3 channel."
    ::= { tmnxPortObjs 11 }

tmnxDS3ChannelEntry       OBJECT-TYPE
    SYNTAX      TmnxDS3ChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a DS3 channel. Entries can be created 
         and deleted via SNMP SET operations.  The tmnxDS3ChannelEntry 
         contains attributes that are applicable to a DS3 channel which
         can belong to the DS3 physical port or belong to a container 
         on a sonet path. The tmnxPortPortID contains the slot, mda and 
         port numbers encoded into it.

         For each tmnxDS3ChannelEntry, there will be a corresponding entry 
         in the tmnxPortTable and the ifTable."
    INDEX       { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxDS3ChannelTable 1 }

TmnxDS3ChannelEntry ::=
    SEQUENCE {
        tmnxDS3ChannelRowStatus                RowStatus,
        tmnxDS3ChannelType                     INTEGER,
        tmnxDS3ChannelFraming                  INTEGER,
        tmnxDS3ChannelClockSource              TmnxDSXClockSource,
        tmnxDS3ChannelChannelized              INTEGER,
        tmnxDS3ChannelSubrateCSUMode           INTEGER,
        tmnxDS3ChannelSubrate                  Unsigned32,
        tmnxDS3ChannelIdleCycleFlags           TmnxDSXIdleCycleFlags,
        tmnxDS3ChannelLoopback                 TmnxDS3Loopback,
        tmnxDS3ChannelBitErrorInsertionRate    Integer32,
        tmnxDS3ChannelBERTPattern              TmnxDSXBertPattern,
        tmnxDS3ChannelBERTDuration             Unsigned32,
        tmnxDS3ChannelMDLEicString             DisplayString,
        tmnxDS3ChannelMDLLicString             DisplayString,
        tmnxDS3ChannelMDLFicString             DisplayString,
        tmnxDS3ChannelMDLUnitString            DisplayString,
        tmnxDS3ChannelMDLPfiString             DisplayString,
        tmnxDS3ChannelMDLPortString            DisplayString,
        tmnxDS3ChannelMDLGenString             DisplayString,
        tmnxDS3ChannelMDLMessageType           BITS,
        tmnxDS3ChannelFEACLoopRespond          TruthValue,
        tmnxDS3ChannelCRC                      INTEGER,
        tmnxDS3ChannelMTU                      Unsigned32,
        tmnxDS3ChannelOperMTU                  Unsigned32,
        tmnxDS3ChannelReportAlarm              TmnxDSXReportAlarm,
        tmnxDS3ChannelReportAlarmStatus        TmnxDSXReportAlarm,
        tmnxDS3ChannelLastChangeTime           TimeStamp,
        tmnxDS3ChannelInFEACLoop               TruthValue,
        tmnxDS3ChannelMDLMonPortString         DisplayString,
        tmnxDS3ChannelMDLMonGenString          DisplayString,
        tmnxDS3ChannelBERTOperStatus           TmnxDSXBertOperStatus,
        tmnxDS3ChannelBERTSynched              Unsigned32,
        tmnxDS3ChannelBERTErrors               Counter64,
        tmnxDS3ChannelBERTTotalBits            Counter64,
        tmnxDS3ChannelScramble                 TruthValue,
        tmnxDS3ChannelAcctPolicyId             Unsigned32,
        tmnxDS3ChannelCollectStats             TruthValue,
        tmnxDS3ChannelClockSyncState           TmnxDSXClockSyncState,
        tmnxDS3ChannelClockMasterPortId        TmnxPortID
    }

tmnxDS3ChannelRowStatus    OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelRowStatus controls the creation and deletion of
         row entries in the tmnxDS3ChannelTable.
         For row creation, the manager has to first calculate the 
         tmnxPortPortID based on the TiMOS encoding scheme."
    ::= { tmnxDS3ChannelEntry 1 }

tmnxDS3ChannelType    OBJECT-TYPE
    SYNTAX      INTEGER {
                    ds3 (1),
                    e3  (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelType indicates whether the channel is
         in DS3 or E3 mode."
    ::= { tmnxDS3ChannelEntry 2 }

tmnxDS3ChannelFraming    OBJECT-TYPE
    SYNTAX      INTEGER {
                    cbit (1),
                    m23  (2),
                    g751 (3),
                    g832 (4)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelFraming indicates the type of framing 
         associated with the DS3 channel.
         If tmnxDS3ChannelType is set to 'ds3', the default framing is 'cbit'.
         If tmnxDS3ChannelType is set to 'e3', the default framing is 'g751'."
    DEFVAL      { cbit }
    ::= { tmnxDS3ChannelEntry 3 }

tmnxDS3ChannelClockSource    OBJECT-TYPE
    SYNTAX      TmnxDSXClockSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelClockSource configures the clock for transmitted data 
         from either the internal clock ('nodeTimed'), or from a clock
         recovered from the line's receive data stream ('loopTimed')."
    DEFVAL      { loopTimed }
    ::= { tmnxDS3ChannelEntry 4 }

tmnxDS3ChannelChannelized    OBJECT-TYPE
    SYNTAX      INTEGER {
                    none(1),
                    ds1 (2),
                    e1  (3),
                    j1  (4)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelChannelized is used to create sub-channels of type
         'ds1' or 'e1' or 'j1' on the interface. Setting the value to 'none'
         removes the sub-channels on the interface."
    DEFVAL      { none }
    ::= { tmnxDS3ChannelEntry 5 }

tmnxDS3ChannelSubrateCSUMode    OBJECT-TYPE
    SYNTAX      INTEGER {
                    notUsed (0),
                    digital-link (1)               
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelSubrateCSUMode configures the channel service unit (CSU)
         compatibility mode to interoperate with existing DS3 subrate 
         standards. 
         This configuration applies only for a non-channelized DS3.
         
         Changing the value of tmnxDS3ChannelSubrateCSUMode to notUsed resets
         the value of tmnxDS3ChannelScramble to 'false'. 
         
         Changing the value of tmnxDS3ChannelSubrateCSUMode 
         resets the value of tmnxDS3ChannelSubrate to default for the mode if
         tmnxDS3ChannelSubrate is not provided."
    DEFVAL      { notUsed }
    ::= { tmnxDS3ChannelEntry 6 }

tmnxDS3ChannelSubrate    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelSubrate configures the subrate step. 

         tmnxDS3ChannelSubrate multiplied by the granularity for a 
         particular mode as defined by tmnxDS3ChannelSubrateCSUMode gives 
         the absoulte subrate in kbps.

         -----------------------------------------------------------------------
          Subrate CSU Mode|Granularity|Default |Range     |Subrate speed range
         ----------------------------------------------------------`-------------
          notUsed         | N/A       | 0      | 0        |N/A
          digital-link    | 301 kbps  | 1      | 1 to 147 |301 kbps to 44.2 mbps
         -----------------------------------------------------------------------" 
    ::= { tmnxDS3ChannelEntry 7 }

tmnxDS3ChannelIdleCycleFlags    OBJECT-TYPE
    SYNTAX      TmnxDSXIdleCycleFlags
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelIdleCycleFlags configures the value that the 
         DS3 port transmits during idle cycle.
 
         The default value applies to HDLC channels only. For ATM
         channels the object does not really apply so a default value
         of 'none' is used and cannot be changed."
    DEFVAL      { flags }
    ::= { tmnxDS3ChannelEntry 8 }

tmnxDS3ChannelLoopback    OBJECT-TYPE
    SYNTAX      TmnxDS3Loopback
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelLoopback is used to put the channel into a loopback mode.
         The value of tmnxPortAdminStatus should be set to 'outOfService' 
         in order for the loopback to be enabled.
         A 'line' loopback loops frames received on this port back to the 
         remote system.
         A 'internal' loopback loops the frames from the local system back at
         the framer.
         When the value is set to 'remote', a signal is sent to the remote 
         system to provide a line loopback."
    DEFVAL      { none }
    ::= { tmnxDS3ChannelEntry 9 }

tmnxDS3ChannelBitErrorInsertionRate    OBJECT-TYPE
    SYNTAX      Integer32 (0 | 2..7)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelBitErrorInsertionRate is used to insert bit errors for a 
         BERT test. The number of error corresponds to 10^(-rate). A rate of 7
         will cause an error rate of 10^(-7), or 1 error in every 10
         million bits transmitted.
         If the value is set to 0, it disables the insertion of bit errors 
         into the BERT.
         Change in this value while the test is running is accepted but
         does not take effect until the test gets restarted."
    DEFVAL      { 0 }
    ::= { tmnxDS3ChannelEntry 10 }

tmnxDS3ChannelBERTPattern    OBJECT-TYPE
    SYNTAX      TmnxDSXBertPattern
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelBERTPattern indicates the pattern used for the 
         Bit Error Rate Test (BERT). The value of tmnxDS3ChannelBERTDuration
         indicates the duration of the test.
         Setting the value of this object to 'none' terminates the test."
    DEFVAL      { none }
    ::= { tmnxDS3ChannelEntry 11 }

tmnxDS3ChannelBERTDuration    OBJECT-TYPE
    SYNTAX      Unsigned32 (0..86400)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelBERTDuration is used to set the duration of the 
         BERT test."
    ::= { tmnxDS3ChannelEntry 12 }

tmnxDS3ChannelMDLEicString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..10))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLEicString configures the Equipment ID Code(EIC) of the 
         Message Data Link (MDL)."
    ::= { tmnxDS3ChannelEntry 13 }

tmnxDS3ChannelMDLLicString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..11))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLLicString configures the Location ID Code(LIC) of the 
         Message Data Link (MDL)."
    ::= { tmnxDS3ChannelEntry 14 }

tmnxDS3ChannelMDLFicString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..10))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLFicString configures the Frame ID Code(FIC) of the 
         Message Data Link (MDL)."
    ::= { tmnxDS3ChannelEntry 15 }

tmnxDS3ChannelMDLUnitString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..6))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLUnitString configures the Unit ID Code(UIC) of the 
         Message Data Link (MDL)."
    ::= { tmnxDS3ChannelEntry 16 }

tmnxDS3ChannelMDLPfiString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..38))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLPfiString configures the Facility ID Code sent in
         the Message Data Link (MDL) Path message."
    ::= { tmnxDS3ChannelEntry 17 }

tmnxDS3ChannelMDLPortString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..38))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLPortString configures the port number string sent in
         the Message Data Link (MDL) idle signal message."
    ::= { tmnxDS3ChannelEntry 18 }

tmnxDS3ChannelMDLGenString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..38))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLGenString configures the generator number string 
         sent in the Message Data Link (MDL) test signal message."
    ::= { tmnxDS3ChannelEntry 19 }

tmnxDS3ChannelMDLMessageType    OBJECT-TYPE
    SYNTAX      BITS {
                    none (0),
                    ds3Path (1),
                    idleSignal (2),
                    testSignal (3)       
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLMessageType configures the type of MDL message that
         is transmitted over the DS3 interface. If the value of this object is
         set to 'none', MDL messages are not transmitted.
         ds3Path(0)   - Enables transmission of the MDL path message.
                        An MDL path message, as defined by ANSI T1.107, is
                        distinguished from idle and test signal messages in 
                        that it contains a facility identification code as its 
                        final data element.
        idleSignal(1) - Enables transmission of the MDL idle signal message.
                        An MDL idle signal message, as defined by ANSI T1.107,
                        is distinguished from path and test signal messages in
                        that it contains a port number as its final data 
                        element.
        testSignal(2) - Enables transmission of the MDL test signal message.
                        An MDL test signal message, as defined by ANSI T1.107, 
                        is distinguished from path and idle signal messages in 
                        that it contains a generator number as its final data 
                        element."
    DEFVAL      { { none } }
    ::= { tmnxDS3ChannelEntry 20 }

tmnxDS3ChannelFEACLoopRespond    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When tmnxDS3ChannelFEACLoopRespond has a value of 'true', the channel 
        is enabled to respond to remote loop signals.  When it has 
        a value of 'false' the port will not respond."
    DEFVAL      { false }
    ::= { tmnxDS3ChannelEntry 21 }

tmnxDS3ChannelCRC    OBJECT-TYPE
    SYNTAX      INTEGER {
                    crc16 (1),
                    crc32 (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelCRC variable indicates the precision of
         the cyclic redundancy check.  A value of 'crc16' is a 16-bit
         CRC calculation.  A value of 'crc32' is a 32-bit CRC
         calculation.  32-bit CRC increases the error detection ability,
         but it also adds some performance overhead.

         For ATM channels, crc32 is the default and the only value supported
         and applies to AAL5 CRC calculation on that channel."
    DEFVAL      { crc16 }
    ::= { tmnxDS3ChannelEntry 22 }

tmnxDS3ChannelMTU  OBJECT-TYPE
    SYNTAX      Unsigned32 (0|512..9208)
    UNITS       "bytes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The size of the largest packet which can be sent/received
         on the port, specified in octets.  For ports that
         are used for transmitting network datagrams, this is the
         size of the largest network datagram that can be sent on the
         sonet path.
            
         Setting tmnxDS3ChannelMTU to a value of zero (0), causes the agent 
         to recalculate the default MTU size which can vary based on the 
         current setting of tmnxPortMode and tmnxPortEncapType variables.  
         Some typical default values are:
                1522 with mode access and encap-type bcp-null
                1526 with mode access and encap-type bcp-dot1q
                1502 with mode access and encap-type ipcp
                4474 with mode access and encap-type frame-relay
                1524 with mode access and encap-type atm
                2092 with mode access and encap-type cem
        "
    ::= { tmnxDS3ChannelEntry 23 }

tmnxDS3ChannelOperMTU    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The negotiated size of the largest packet which can be sent on 
         the channel, specified in octets.  For channels that are used 
         for transmitting network datagrams, this is the size of the largest
         network datagram that can be sent."
    ::= { tmnxDS3ChannelEntry 24 }

tmnxDS3ChannelReportAlarm    OBJECT-TYPE
    SYNTAX      TmnxDSXReportAlarm
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelReportAlarm determines when and if to
         generate notifications for this channel:
         ais - Reports alarm indication signal errors. 
               ais alarms are issued by default.
         los - Reports loss of signal errors.
               los alarms are issued by default.
         oof - Reports out-of-frame errors. 
               oof alarms are not issued by default.
         rai - Reports resource availability indicator events. 
               rai alarms are not issued by default.
         looped - Reports if the far end has forced the near end to loopback.
               looped alarms are not issued by default.
         berSd - Reports DS3/E3 signal degradation bit errors.
                 berSd alarms are not issued by default.
         berSf - Reports DS3/E3 signal failure bit errors.
                 berSf alarms are not issued by default"
    DEFVAL      { {ais, los} }
    ::= { tmnxDS3ChannelEntry 25 }

tmnxDS3ChannelReportAlarmStatus    OBJECT-TYPE
    SYNTAX      TmnxDSXReportAlarm
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelReportAlarmStatus indicates the current 
         alarms on this port."
    ::= { tmnxDS3ChannelEntry 26 }

tmnxDS3ChannelLastChangeTime    OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxDS3ChannelLastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxDS3ChannelEntry row for this port."
    ::= { tmnxDS3ChannelEntry 27 }

tmnxDS3ChannelInFEACLoop    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelInFEACLoop indicates whether the remote end has put 
         this channel in FEAC(Far End Alarm Control) loopback."
    ::= { tmnxDS3ChannelEntry 28 }

tmnxDS3ChannelMDLMonPortString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..38))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLMonPortString indicates the port number string 
         received in the Message Data Link (MDL) idle signal message."
    ::= { tmnxDS3ChannelEntry 29 }

tmnxDS3ChannelMDLMonGenString    OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..38))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelMDLMonGenString indicates the generator number string 
         received in the Message Data Link (MDL) test signal message."
    ::= { tmnxDS3ChannelEntry 30 }

tmnxDS3ChannelBERTOperStatus    OBJECT-TYPE
    SYNTAX      TmnxDSXBertOperStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelBERTOperStatus indicates the status of the BERT test
         as specified by TmnxDSXBertOperStatus."
    ::= { tmnxDS3ChannelEntry 31 }

tmnxDS3ChannelBERTSynched    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelBERTSynched is the number of seconds for which the 
         BERT hardware was synchronized. This will be less than or equal to 
         the duration of the last BERT test. It is valid only after 
         tmnxDS3ChannelBERTOperStatus transitioned from 'active' to 'idle'
         the last time BERT was activated."
    ::= { tmnxDS3ChannelEntry 32 }

tmnxDS3ChannelBERTErrors    OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelBERTErrors is the number of bit errors detected during 
         the last BERT test. It is valid only after tmnxDS3ChannelBERTOperStatus
         transitioned from 'active' to 'idle' the last time BERT 
         was activated."
    ::= { tmnxDS3ChannelEntry 33 }

tmnxDS3ChannelBERTTotalBits    OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS3ChannelBERTTotalBits is the total number of bits received 
         during the last BERT test. Bits are only counted when the BERT 
         hardware is synchronized. It is valid only after 
         tmnxDS3ChannelBERTOperStatus transitioned from 'active' to 'idle'
         the last time BERT was activated."
    ::= { tmnxDS3ChannelEntry 34 }

tmnxDS3ChannelScramble     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When tmnxDS3ChannelScramble indicates whether scrambling
         is enabled on this channel.  Both ends of the connection
         must use the same scrambling algorithm.  The default value
         is 'true' if the tmnxPortEncapType is atmEncap for this
         channel and indicates ATM payload scrambling; otherwise 
         (tmnxPortEncapType is not atmEncap) the default is 'false'
         and can only be changed to true if the value of 
         tmnxDS3ChannelSubrateCSUMode is digital-link (1)"
    ::= { tmnxDS3ChannelEntry 35 }
    
tmnxDS3ChannelAcctPolicyId     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..99)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelAcctPolicyId specifies an existing accounting 
         policy to use for the ds3 channel. Accounting policies can only be 
         associated with network ports or channels.

         A non-zero value indicates the tmnxLogApPolicyId index identifying the
         policy entry in the tmnxLogApTable from the TIMETRA-LOG-MIB which is
         associated with this channel.  A zero value indicates that there is no
         accounting policy associated with this channel"
    DEFVAL { 0 }
    ::= { tmnxDS3ChannelEntry 36 }

tmnxDS3ChannelCollectStats     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelCollectStats specifies whether or not the 
         collection of accounting and statistical data for the network ds3 
         channel is collected.

         When the value is set to false, the statistics are still accumulated 
         by the IOM cards, however, the CPM will not obtain the results and
         write them to the billing file.

         When applying accounting policies the data by default will be collected 
         in the appropriate records and written to the designated billing file."
    DEFVAL { true }
    ::= { tmnxDS3ChannelEntry 37 }

tmnxDS3ChannelClockSyncState    OBJECT-TYPE
    SYNTAX      TmnxDSXClockSyncState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelClockSyncState indicates the current clock
         synchronization state if tmnxDS3ChannelClockSource is 'adaptive' or
         'differential'."
    ::= { tmnxDS3ChannelEntry 38 }

tmnxDS3ChannelClockMasterPortId    OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS3ChannelClockMasterPortId indicates the current
         clock synchronization master port id if tmnxDS3ChannelClockSource
         is 'adaptive' or 'differential'."
    ::= { tmnxDS3ChannelEntry 39 }


--
-- DS1 table
--
tmnxDS1Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxDS1Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxDS1Table has an entry for each DS1 channel."
    ::= { tmnxPortObjs 12 }

tmnxDS1Entry       OBJECT-TYPE
    SYNTAX      TmnxDS1Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a DS1 channel on a IOM card 
         in a chassis in the system.  DS1 channel entries for DS3
         ports cannot be created and deleted via SNMP SET operations.
         Entries in this table will be created automatically when
         tmnxDS3ChannelChannelized is set to 'true'. DS1 channel entries
         for DS1 ports can be created and deleted via SNMP SET operations.
         For each tmnxDS1Entry, there will be a corresponding entry 
         in the tmnxPortTable and the ifTable."
    INDEX       { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxDS1Table 1 }

TmnxDS1Entry ::=
    SEQUENCE {
        tmnxDS1RowStatus                RowStatus,
        tmnxDS1Type                     INTEGER,
        tmnxDS1Framing                  INTEGER,
        tmnxDS1IdleCycleFlags           TmnxDSXIdleCycleFlags,
        tmnxDS1Loopback                 TmnxDS1Loopback,
        tmnxDS1InvertData               TruthValue,
        tmnxDS1BitErrorInsertionRate    Integer32,
        tmnxDS1BERTPattern              TmnxDSXBertPattern,
        tmnxDS1BERTDuration             Unsigned32,
        tmnxDS1ReportAlarm              TmnxDSXReportAlarm,
        tmnxDS1ReportAlarmStatus        TmnxDSXReportAlarm,
        tmnxDS1LastChangeTime           TimeStamp,
        tmnxDS1ClockSource              TmnxDSXClockSource,
        tmnxDS1BERTOperStatus           TmnxDSXBertOperStatus,
        tmnxDS1BERTSynched              Unsigned32,
        tmnxDS1BERTErrors               Counter64,
        tmnxDS1BERTTotalBits            Counter64,        
        tmnxDS1RemoteLoopRespond        TruthValue,
        tmnxDS1InRemoteLoop             TruthValue,
        tmnxDS1InsertSingleBitError     TmnxActionType,
        tmnxDS1SignalMode               INTEGER,
        tmnxDS1ClockSyncState           TmnxDSXClockSyncState,
        tmnxDS1ClockMasterPortId        TmnxPortID,
        tmnxDS1BerSdThreshold           Unsigned32,
        tmnxDS1BerSfThreshold           Unsigned32
    }

tmnxDS1RowStatus    OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1RowStatus controls the creation and deletion of
         row entries in the tmnxDS1Table.
         For row creation, the manager has to first calculate the 
         tmnxPortPortID based on the TiMOS encoding scheme."
    ::= { tmnxDS1Entry 1 }

tmnxDS1Type    OBJECT-TYPE
    SYNTAX      INTEGER {
                    ds1(1),
                    e1 (2),
                    j1 (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1Type indicates whether the DS1 channel is
         in DS1, E1 or J1 mode."
    ::= { tmnxDS1Entry 2 }

tmnxDS1Framing    OBJECT-TYPE
    SYNTAX      INTEGER {
                    esf (1),
                    sf  (2),
                    g704-no-crc (3),
                    g704 (4),
                    e1-unframed (5), 
                    ds1-unframed (6)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1Framing indicates the type of framing associated
         with the channel:
         esf            ESF (extended super frame) mode for T1 interfaces.
         sf             SF (super frame) mode for T1 interfaces.
         g704           G.704 framing format for E1 interfaces.
         g704-no-crc4   G.704 framing with no CRC4 for E1 interfaces.
         e1-unframed    E1 Unframed (G.703) mode for E1 interfaces.
         ds1-unframed   Unframed mode for T1 interfaces.

         If tmnxDS1Type is set to 'ds1', the default framing is 'esf'.
         If tmnxDS1Type is set to 'e1', the default framing is 'g704'. 
         
         Changing the value of tmnxDS1Framing resets the values of
         tmnxDS1BerSdThreshold and tmnxDS1BerSfThreshold to defaults
         and turns off reporting of berSd and berSf alarms 
         in tmnxDS1ReportAlarm."
    DEFVAL      { esf }
    ::= { tmnxDS1Entry 3 }

tmnxDS1IdleCycleFlags    OBJECT-TYPE
    SYNTAX      TmnxDSXIdleCycleFlags
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "tmnxDS1IdleCycleFlags configures the value that the DS1 
         channel transmits during idle cycle."
    DEFVAL      { flags }
    ::= { tmnxDS1Entry 4 }

tmnxDS1Loopback    OBJECT-TYPE
    SYNTAX      TmnxDS1Loopback
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1Loopback is used to put the channel into a loopback mode. The 
         value of tmnxPortAdminStatus should be set to 'outOfService' in order
         for the loopback to be enabled.
         A 'line' loopback loops frames received on this port back to the 
         remote system.
         A 'internal' loopback loops frames from the local system back at
         the framer.        
         A 'fdlAnsi' requests loopback of type FDL ANSI T1.403. This is valid
         with tmnxDS1Framing set to 'esf' framing.
         A 'fdlBellcore' requests loopback of type FDL Bellcore TR-TSY-000312.
         This is valid with tmnxDS1Framing set to 'esf'.
         A 'inbandAnsi' requests loopback of type inband ANSI T1.403. This is 
         valid with tmnxDS1Framing set to 'sf'."
    DEFVAL      { none }
    ::= { tmnxDS1Entry 5 }

tmnxDS1InvertData    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Setting tmnxDS1InvertData to 'true' causes all data bits to 
         be inverted, to guarantee ones density. It is typically used with
         AMI line encoding."
    DEFVAL      { false }
    ::= { tmnxDS1Entry 6 }

tmnxDS1BitErrorInsertionRate    OBJECT-TYPE
    SYNTAX      Integer32 (0 | 2..7)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1BitErrorInsertionRate is used to insert bit errors for a 
         BERT test. The number of error corresponds to 10^(-rate). A rate of 7
         will cause an error rate of 10^(-7), or 1 error in every 10
         million bits transmitted.
         If the value is set to 0, disables the insertion of bit errors into 
         the BERT.
         Change in this value while the test is running is accepted but
         does not take effect until the test gets restarted."
    DEFVAL      { 0 }
    ::= { tmnxDS1Entry 7 }

tmnxDS1BERTPattern    OBJECT-TYPE
    SYNTAX      TmnxDSXBertPattern
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1BERTPattern object indicates the pattern used for the 
         Bit Error Rate Test (BERT). The value of tmnxDS1BERTDuration
         indicates the duration of the test.
         Setting the value to 'none' terminates the test."
    DEFVAL      { none }
    ::= { tmnxDS1Entry 8 }

tmnxDS1BERTDuration    OBJECT-TYPE
    SYNTAX      Unsigned32 (0..86400)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1BERTDuration is used to set the duration of the BERT test."
    ::= { tmnxDS1Entry 9 }

tmnxDS1ReportAlarm    OBJECT-TYPE
    SYNTAX      TmnxDSXReportAlarm
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1ReportAlarm determines when and if to
         generate notifications for this channel:
         ais - Reports alarm indication signal errors. 
               ais alarms are issued by default.
         los - Reports loss of signal errors.
               los alarms are issued by default.
         oof - Reports out-of-frame errors.
               oof alarms are not issued by default.
         rai - Reports resource availability indicator events. 
               rai alarms are not issued by default.
         looped - Reports if the far end has forced the near end to loopback.
               looped alarms are not issued by default.
         berSd - Reports DS1/E1/J1 signal degradation bit errors.
                 berSd alarms are not issued by default.
         berSf - Reports DS1/E1/J1 signal failure bit errors.
                 berSf alarms are not issued by default"
    DEFVAL      { {ais, los} }
    ::= { tmnxDS1Entry 10 }

tmnxDS1ReportAlarmStatus    OBJECT-TYPE
    SYNTAX      TmnxDSXReportAlarm
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1ReportAlarmStatus indicates the current alarms
         on this channel."
    ::= { tmnxDS1Entry 11 }

tmnxDS1LastChangeTime    OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxDS1LastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxDS1Entry row for this channel."
    ::= { tmnxDS1Entry 12 }

tmnxDS1ClockSource    OBJECT-TYPE
    SYNTAX      TmnxDSXClockSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1ClockSource configures the clock for transmitted data from
         either the internal clock ('nodeTimed'), or from a clock recovered
         from the line's receive data stream ('loopTimed'), or recovered
         adaptively from the rate at which the data that is received and not
         from the physical layer ('adaptive'), or recovered from the
         differential timestamp in the RTP header ('differential')."
    DEFVAL      { loopTimed }
    ::= { tmnxDS1Entry 13 }

tmnxDS1BERTOperStatus    OBJECT-TYPE
    SYNTAX      TmnxDSXBertOperStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS1BERTOperStatus indicates the status of the BERT test as 
         specified by TmnxDSXBertOperStatus."
    ::= { tmnxDS1Entry 14 }

tmnxDS1BERTSynched    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS1BERTSynched is the number of seconds for which the 
         BERT hardware was synchronized. This will be less than or equal to 
         the duration of the last BERT test. It is valid only after 
         tmnxDS1BERTOperStatus transitioned from 'active' to 'idle'
         the last time BERT was activated."
    ::= { tmnxDS1Entry 15 }

tmnxDS1BERTErrors    OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS1BERTErrors is the number of bit errors detected during 
         the last BERT test. It is valid only after tmnxDS1BERTOperStatus
         transitioned from 'active' to 'idle' the last time BERT 
         was activated."
    ::= { tmnxDS1Entry 16 }

tmnxDS1BERTTotalBits    OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS1BERTTotalBits is the total number of bits received 
         during the last BERT test. Bits are only counted when the BERT 
         hardware is synchronized. It is valid only after 
         tmnxDS1BERTOperStatus transitioned from 'active' to 'idle'
         the last time BERT was activated."
    ::= { tmnxDS1Entry 17 }
    
tmnxDS1RemoteLoopRespond    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When tmnxDS1RemoteLoopRespond has a value of 'true', the channel 
        is enabled to respond to remote loop signals.  When it has 
        a value of 'false' the port will not respond."
    DEFVAL      { false }
    ::= { tmnxDS1Entry 18 }

tmnxDS1InRemoteLoop    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS1InRemoteLoop indicates whether the remote end has put 
         this channel in remote loopback."
    ::= { tmnxDS1Entry 19 }

tmnxDS1InsertSingleBitError    OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1InsertSingleBitError is used to insert a single bit error for a
         BERT test."
    ::= { tmnxDS1Entry 20 }

tmnxDS1SignalMode    OBJECT-TYPE
    SYNTAX      INTEGER {
                    none (1),
                    cas  (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1SignalMode indicates the type of signalling
         associated with the channel:
         none  - Indicates no signalling on this channel.
         cas   - Indicates Channel Associated Signalling (CAS) on this channel.
         The default signalling is 'none'. "
    DEFVAL      { none }
    ::= { tmnxDS1Entry 21 }

tmnxDS1ClockSyncState    OBJECT-TYPE
    SYNTAX      TmnxDSXClockSyncState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1ClockSyncState indicates the current clock
         synchronization state if tmnxDS1ClockSource is 'adaptive' or
         'differential'."
    ::= { tmnxDS1Entry 22 }

tmnxDS1ClockMasterPortId    OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1ClockMasterPortId indicates the current clock
         synchronization master port id if tmnxDS1ClockSource is 'adaptive'
         or 'differential'."
    ::= { tmnxDS1Entry 23 }

tmnxDS1BerSdThreshold       OBJECT-TYPE
    SYNTAX      Unsigned32 (1|5|10|50|100)
    UNITS       "error bits in million bits received"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1BerSdThreshold specifies a bit error rate (BER)
         threshold used to determine when to send a tmnxEqPortDS1Alarm 
         notification for a BER SD failure and a tmnxEqPortDS1AlarmClear
         notification for a BER SD failure clear. Bit error computed must 
         reach/exceed threshold value over a pre-defined period of time
         for the tmnxEqPortSonetAlarm notification to be sent.
         
         The value of tmnxDS1Framing defines how the bit error is
         computed and what values of tmnxDS1BerSdThreshold are allowed.
         The following table defines this dependency:

         tmnxDS1Framing   Bit error calculation and value supported
         --------------   -----------------------------------------
           esf  (1)       each CRC error is counted as one bit error
           g704 (4)       values supported are 1,5,10,50,100
            
           other values   bit error alarms not supported, value
                          of tmnxDS1BerSdThreshold set to default and 
                          cannot be changed
         
         tmnxDS1BerSdThreshold value must not exceed the value of
         tmnxDS1BerSfThreshold."
    DEFVAL { 5 }
    ::= { tmnxDS1Entry 24 } 

tmnxDS1BerSfThreshold       OBJECT-TYPE
    SYNTAX      Unsigned32 (1|5|10|50|100)
    UNITS       "error bits in million bits received"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS1BerSfThreshold specifies a bit error rate (BER)
         threshold used to determine when to send a tmnxEqPortDS1Alarm 
         notification for a BER SF failure and a tmnxEqPortDS1AlarmClear
         notification for a BER SF failure clear. Bit error computed must 
         reach/exceed threshold value over a pre-defined period of time
         for the tmnxEqPortSonetAlarm notification to be sent.
         
         The value of tmnxDS1Framing defines how the bit errors are
         computed and what values of tmnxDS1BerSfThreshold are allowed.
         The following table defines this dependency:

         tmnxDS1Framing   Bit error calculation and value supported
         --------------   -----------------------------------------
           esf  (1)       each CRC error is counted as one bit error
           g704 (4)       values supported are 1,5,10,50,100
            
           other values   bit error alarms not supported, value
                          of tmnxDS1BerSfThreshold set to default and 
                          cannot be changed
        
         tmnxDS1BerSfThreshold value must not be smaller than 
         tmnxDS1BerSdThreshold value"
    DEFVAL { 50 }
    ::= { tmnxDS1Entry 25 } 

--
-- DS0 Channel Group Table 
-- 
tmnxDS0ChanGroupTable   OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxDS0ChanGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxDS0ChanGroupTable has an entry for DS0 channels 
         grouped together and belonging to a particular DS1 channel."
    ::= { tmnxPortObjs 13 }

tmnxDS0ChanGroupEntry       OBJECT-TYPE
    SYNTAX      TmnxDS0ChanGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents one or more DS0 channels on a 
         DS1 interface.  tmnxDS0ChanGroupTimeSlots object is used to add or
         remove DS0 channels from the group.
         
         Entries can be created and deleted via SNMP SET operations 
         using the tmnxDS0ChanGroupRowStatus object. 

         For each tmnxDS0ChanGroupEntry, there will be a corresponding entry 
         in the tmnxPortTable and the ifTable."
    INDEX       { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxDS0ChanGroupTable 1 }

TmnxDS0ChanGroupEntry ::=
    SEQUENCE {
        tmnxDS0ChanGroupRowStatus           RowStatus,
        tmnxDS0ChanGroupTimeSlots           TmnxDs0ChannelList,
        tmnxDS0ChanGroupSpeed               INTEGER,
        tmnxDS0ChanGroupCRC                 INTEGER,
        tmnxDS0ChanGroupMTU                 Unsigned32,
        tmnxDS0ChanGroupOperMTU             Unsigned32,
        tmnxDS0ChanGroupLastChangeTime      TimeStamp,
        tmnxDS0ChanGroupIdleCycleFlags      TmnxDSXIdleCycleFlags,
        tmnxDS0ChanGroupScramble            TruthValue,
        tmnxDS0ChanGroupAcctPolicyId        Unsigned32,
        tmnxDS0ChanGroupCollectStats        TruthValue,
        tmnxDS0ChanGroupPayloadFillType     TmnxDSXIdleFillType,
        tmnxDS0ChanGroupPayloadPattern      Unsigned32,
        tmnxDS0ChanGroupSignalFillType      TmnxDSXIdleFillType,
        tmnxDS0ChanGroupSignalPattern       Unsigned32
    }

tmnxDS0ChanGroupRowStatus    OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS0ChanGroupRowStatus controls the creation and deletion of
         row entries in the tmnxDS0ChanGroupTable. The tmnxPortPortID 
         for a channel group includes a group ID in the lower bits.
         The manager has to first calculate the tmnxPortPortID based on the
         TiMOS encoding scheme."
    ::= { tmnxDS0ChanGroupEntry 1 }

tmnxDS0ChanGroupTimeSlots    OBJECT-TYPE
    SYNTAX      TmnxDs0ChannelList
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS0ChanGroupTimeSlots is a bitmap which is used to 
         add/remove DS0 timeslots from a channel group. 
         The description for TmnxDs0ChannelList contains details of the
         bitmap organization.

         When a channel group is created on unframed DS1/E1, all timeslots
         as applicable to a given speed are auto-allocated and cannot be 
         changed.

         When a channel group is created on a framed DS1/E1 or
         existing channel group's encapsulation is changed from ATM,
         the default of no timeslots is auto-assigned to the group.

         When a channel group  on a framed DS1/E1 is configured for 
         ATM encapsulation, the ATM defaults of 0x00FFFFFF (DS1) 
         and 0XFFFEFFFE (E1) are auto-assigned and cannot be changed."
    ::= { tmnxDS0ChanGroupEntry 2 }

tmnxDS0ChanGroupSpeed    OBJECT-TYPE
    SYNTAX      INTEGER {
                    speed-56 (1),
                    speed-64 (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS0ChanGroupSpeed configures the required link speed 
         for all the time slots included in this channel group."
    ::= { tmnxDS0ChanGroupEntry 3 }

tmnxDS0ChanGroupCRC    OBJECT-TYPE
    SYNTAX      INTEGER {
                    crc16 (1),
                    crc32 (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS0ChanGroupCRC variable indicates the precision of
         the cyclic redundancy check.  A value of 'crc16' is a 16-bit
         CRC calculation.  A value of 'crc32' is a 32-bit CRC
         calculation.  32-bit CRC increases the error detection ability,
         but it also adds some performance overhead.

         For channel groups with encapsulation set to ATM, the default and
         only supported value is crc32 and applies to AAL5 CRC calculations
         on that channel group."
    DEFVAL      { crc16 }
    ::= { tmnxDS0ChanGroupEntry 4 }

tmnxDS0ChanGroupMTU  OBJECT-TYPE
    SYNTAX      Unsigned32 (0|512..9208)
    UNITS       "bytes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The size of the largest packet which can be sent/received
         on the channel group, specified in octets.  For groups that
         are used for transmitting network datagrams, this is the
         size of the largest network datagram that can be sent.
            
         Setting tmnxDS0ChanGroupMTU to a value of zero (0), causes the agent 
         to recalculate the default MTU size which can vary based on the 
         current setting of tmnxPortMode and tmnxPortEncapType variables.  
         Some typical default values are:
                1522 with mode access and encap-type bcp-null
                1526 with mode access and encap-type bcp-dot1q
                1502 with mode access and encap-type ipcp
                4474 with mode access and encap-type frame-relay
                1524 with mode access and encap-type atm
                2092 with mode access and encap-type cem
        "
    ::= { tmnxDS0ChanGroupEntry 5 }

tmnxDS0ChanGroupOperMTU    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The negotiated size of the largest packet which can be sent on 
         the channel group, specified in octets.  For channel groups that 
         are used for transmitting network datagrams, this is the size of 
         the largest network datagram that can be sent."
    ::= { tmnxDS0ChanGroupEntry 6 }

tmnxDS0ChanGroupLastChangeTime    OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxDS0ChanGroupLastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxDS0ChanGroupEntry row for this channel group."
    ::= { tmnxDS0ChanGroupEntry 7 }

tmnxDS0ChanGroupIdleCycleFlags    OBJECT-TYPE
    SYNTAX      TmnxDSXIdleCycleFlags
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS0ChanGroupIdleCycleFlags configures the value that the
         channel group transmits during idle cycle.
 
         The default value applies to HDLC channels only. For ATM and CEM
         channels the object does not really apply so a default value of
         'none' is used and cannot be changed."
    DEFVAL      { flags }
    ::= { tmnxDS0ChanGroupEntry 8 }

tmnxDS0ChanGroupScramble     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS0ChanGroupScramble indicates whether payload scrambling
         is enabled on this ATM channel.  Both ends of the connection
         must use the same scrambling algorithm.  The default value
         is 'true' if the tmnxPortEncapType is atmEncap for this
         channel, otherwise the default is 'false' and not allowed to 
         change."
    ::= { tmnxDS0ChanGroupEntry 9 }
    
tmnxDS0ChanGroupAcctPolicyId OBJECT-TYPE
    SYNTAX      Unsigned32 (0..99)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS0ChanGroupAcctPolicyId specifies an existing accounting 
         policy to use for the ds0 channel-group. Accounting policies can only 
         be associated with network ports or channels.

         A non-zero value indicates the tmnxLogApPolicyId index identifying the
         policy entry in the tmnxLogApTable from the TIMETRA-LOG-MIB which is
         associated with this channel-group.  A zero value indicates that there is 
         no accounting policy associated with this channel"
    DEFVAL { 0 }
    ::= { tmnxDS0ChanGroupEntry 10 }

tmnxDS0ChanGroupCollectStats     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS0ChanGroupCollectStats specifies whether or not the 
         collection of accounting and statistical data for the network ds0 
         channel-group is collected.

         When the value is set to false, the statistics are still accumulated 
         by the IOM cards, however, the CPM will not obtain the results and
         write them to the billing file.

         When applying accounting policies the data by default will be collected 
         in the appropriate records and written to the designated billing file."
    DEFVAL { true }
    ::= { tmnxDS0ChanGroupEntry 11 }

tmnxDS0ChanGroupPayloadFillType  OBJECT-TYPE
    SYNTAX      TmnxDSXIdleFillType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS0ChanGroupPayloadFillType specifies the type of
         idle payload fill to be sent when the CEM channel group experiences
         an underrun.

         If the type is set to 'userDefinedPattern' then the value of
         tmnxDS0ChanGroupPayloadPattern is used to define the payload pattern.

         The default value depends on the encap type of the channel group:
         For CEM encap           - 'allOnes'
         For all other encaps    - 'notApplicable'
         
         For a ds1-unframed and e1-unframed CEM encap DS0 channel group the
         only supported value is 'allOnes'.

         For non-CEM encap channel groups the value cannot be changed."
    DEFVAL { notApplicable }
    ::= { tmnxDS0ChanGroupEntry 12 }

tmnxDS0ChanGroupPayloadPattern   OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS0ChanGroupPayloadPattern specifies the idle payload
         fill pattern when the CEM channel group experiences an underrun and
         tmnxDS0ChanGroupPayloadFillType is set to 'userDefinedPattern'.

         The default value depends on the encap type of the channel group:
         For CEM encap channel group             - 255 (Valid range 0..255)
         For non-CEM encap channel group         -   0 (Valid range 0..0)."
    DEFVAL { 0 }
    ::= { tmnxDS0ChanGroupEntry 13 }

tmnxDS0ChanGroupSignalFillType   OBJECT-TYPE
    SYNTAX      TmnxDSXIdleFillType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS0ChanGroupSignalFillType specifies the type of
         idle signal fill to be sent when the CAS CEM channel group experiences
         an underrun.

         If the type is set to 'userDefinedPattern' then the value of
         tmnxDS0ChanGroupSignalPattern is used to define the signal pattern.
         
         The default value depends on the encap of the DS0 channel group and
         the signal-mode of the DS1/E1:
         For CEM encap on DS1/E1 with CAS signal mode    - 'allOnes'
         For all other encaps or non-CAS CEM DS1/E1      - 'notApplicable'

         For CEM channel groups on DS1/E1 with no CAS signalling and non-CEM
         encap channel groups the value cannot be changed."
    DEFVAL {  notApplicable }
    ::= { tmnxDS0ChanGroupEntry 14 }

tmnxDS0ChanGroupSignalPattern   OBJECT-TYPE
    SYNTAX      Unsigned32 (0..15)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxDS0ChanGroupSignalPattern specifies the idle signal
         fill pattern when the CAS CEM channel group experiences an underrun
         and tmnxDS0ChanGroupSignalFillType is set to 'userDefinedPattern'.

         CAS ESF DS1 and CAS E1 support 4 signalling bits per timeslot (ABCD)
         CAS SF DS1 supports 2 signalling bits per timeslot (AB)
         
         The default value depends on the type of channel group:
         For CAS DS1 ESF or CAS E1 channel group -  15 (Valid range 0..15)
         For CAS DS1 SF channel group            -   3 (Valid range 0..3)
         For non-CAS or non-CEM channel group    -   0 (Valid range 0..0)"
    DEFVAL { 0 }
    ::= { tmnxDS0ChanGroupEntry 15 }
    
 
--
-- Bundle Table
--
tmnxBundleTable   OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxBundleEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxBundleTable has an entry for a bundle created on the system."
    ::= { tmnxPortObjs 14 }

tmnxBundleEntry       OBJECT-TYPE
    SYNTAX      TmnxBundleEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a multilink bundle on a MDA.
         Entries can be created and deleted via SNMP SET operations 
         using the tmnxBundleRowStatus object. The tmnxBundleBundleID will 
         contain the bundle number encoded in it. The bundle number is 
         unique for a MDA.
         For each tmnxBundleEntry, there will be a corresponding entry 
         in the tmnxPortTable and the ifTable."
    INDEX       { tmnxChassisIndex, tmnxBundleBundleID }
    ::= { tmnxBundleTable 1 }

TmnxBundleEntry ::=
    SEQUENCE {
        tmnxBundleBundleID            TmnxBundleID,
        tmnxBundleRowStatus           RowStatus,
        tmnxBundleType                INTEGER,
        tmnxBundleMinimumLinks        Unsigned32,
        tmnxBundleNumLinks            Unsigned32,
        tmnxBundleNumActiveLinks      Unsigned32,
        tmnxBundleMRRU                Unsigned32,
        tmnxBundleOperMRRU            Unsigned32,
        tmnxBundlePeerMRRU            Unsigned32,
        tmnxBundleOperMTU             Unsigned32,
        tmnxBundleRedDiffDelay        Unsigned32,
        tmnxBundleRedDiffDelayAction  INTEGER,
        tmnxBundleYellowDiffDelay     Unsigned32,
        tmnxBundleShortSequence       TruthValue,
        tmnxBundleLastChangeTime      TimeStamp,
        tmnxBundleFragmentThreshold   Unsigned32,
        tmnxBundleUpTime              Unsigned32,
        tmnxBundleInputDiscards       Counter32,
        tmnxBundlePrimaryMemberPortID TmnxPortID,
        tmnxBundleLFI                 TruthValue,
        tmnxBundleProtectedType       INTEGER,
        tmnxBundleParentBundle        TmnxBundleID
   }

tmnxBundleBundleID    OBJECT-TYPE
    SYNTAX      TmnxBundleID
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxBundleBundleID identifies this multilink bundle. The value of
         this object is calculated using the TiMOS encoding scheme described
         in TmnxBundleID.
         tmnxBundleBundleID is used as an index into the ifTable and the 
         tmnxPortTable to access an entry corresponding to this entry."
    ::= { tmnxBundleEntry 1 }

tmnxBundleRowStatus    OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleRowStatus controls the creation and deletion of
         row entries in the tmnxBundleTable. 

         The manager has to first calculate the tmnxBundleBundleID 
         based on the TiMOS encoding."
    ::= { tmnxBundleEntry 2 }

tmnxBundleType    OBJECT-TYPE
    SYNTAX      INTEGER {
                    mlppp (1),
                    mlfr  (2),
                    imagrp (3)                 
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleType specifies the type of this multilink bundle.
         It provides a reference as to the type of bundle this row
         refers to but does not map to the direct value of 
         bndle type defined in TmnxBundleID."
    DEFVAL      { mlppp }
    ::= { tmnxBundleEntry 3 }

tmnxBundleMinimumLinks    OBJECT-TYPE
    SYNTAX      Unsigned32 (1..8)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleMinimumLinks is the minimum number of links that must be
         active for a bundle to be active. If the number of links drop below
         the given minimum then the multilink bundle will transition to an
         operation down state."
    ::= { tmnxBundleEntry 4 }

tmnxBundleNumLinks    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleNumLinks indicates the total number of links configured 
         for this bundle."
    ::= { tmnxBundleEntry 5 }

tmnxBundleNumActiveLinks    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleNumActiveLinks indicates the current number of active links 
         belonging to this bundle."
    ::= { tmnxBundleEntry 6 }

tmnxBundleMRRU    OBJECT-TYPE
    SYNTAX      Unsigned32 (0|1500..9206)
    UNITS       "bytes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleMRRU is used to configure the 
         Max Received Reconstructed Unit (MRRU). This is the maximum frame that
         can be reconstructed from multilink fragments.
         This object is only applicable to tmnxBundleType values of mlppp or
         mlfr. All non applicable types have a value of zero."
    DEFVAL      { 1524 }
    ::= { tmnxBundleEntry 7 }

tmnxBundleOperMRRU    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleOperMRRU indicates the operational value of 
         Max Received Reconstructed Unit (MRRU).
         This object is only applicable to tmnxBundleType values of mlppp or
         mlfr. All non applicable types have a value of zero."
    ::= { tmnxBundleEntry 8 }

tmnxBundlePeerMRRU    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundlePeerMRRU indicates the MRRU value sent by the peer
         during negotiation.
         This object is only applicable to tmnxBundleType values of mlppp or
         mlfr. All non applicable types have a value of zero."
    ::= { tmnxBundleEntry 9 }

tmnxBundleOperMTU    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleOperMTU indicates the operational MTU of the bundle."
    ::= { tmnxBundleEntry 10 }

tmnxBundleRedDiffDelay    OBJECT-TYPE
    SYNTAX      Unsigned32 (0..25|0..50)
    UNITS       "milliseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleRedDiffDelay configures the maximum acceptable differential
         delay for individual circuits within this multilink bundle. If
         the delay exceeds this threshold, a tmnxEqPortBndlRedDiffExceeded
         trap is issued.
         
         For a tmnxBundleType value of 'imagrp' a range of 0 to 50 is supported,
         0 to 25 otherwise."
    ::= { tmnxBundleEntry 11 }

tmnxBundleRedDiffDelayAction    OBJECT-TYPE
    SYNTAX      INTEGER {
                    none (0),
                    down (1)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleRedDiffDelayAction specifies the action to be taken
         when the differential delay exceeds the threshold configured in
         tmnxBundleRedDiffDelay.
         For a tmnxBundleType value of 'imagrp', only the down action is 
         supported."
    DEFVAL      { none }
    ::= { tmnxBundleEntry 12 }

tmnxBundleYellowDiffDelay    OBJECT-TYPE
    SYNTAX      Unsigned32 (0..25)
    UNITS       "milliseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleYellowDiffDelay configures the yellow warning 
         threshold for the differential delay for the circuits 
         within a given multilink bundle. If the delay exceeds this 
         threshold, a tmnxEqPortBndlYellowDiffExceeded trap is issued.
         This object is only applicable to tmnxBundleType values of 
         'mlppp' or 'mlfr'. All non applicable types have a value 
         of zero."
    ::= { tmnxBundleEntry 13 }

tmnxBundleShortSequence    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleShortSequence command specifies that the MLPPP bundle 
         should use short (12 bit) sequence numbers. Instead of the 
         standard long (24 bits) sequence number.
         This object is only applicable to tmnxBundleType values of mlppp."
    DEFVAL      { false }
    ::= { tmnxBundleEntry 14 }

tmnxBundleLastChangeTime    OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxBundleLastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxBundleEntry row for this channel group."
    ::= { tmnxBundleEntry 15 }

tmnxBundleFragmentThreshold     OBJECT-TYPE
    SYNTAX      Unsigned32(0|128..512)
    UNITS       "bytes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxBundleFragmentThreshold specifies the maximum length 
         of a fragment transmitted across the multilink bundle. Values supported
         within each bundle type are H/W dependent. The value of 0 specifies
         no fragmentation.
         
         Currently, the following values are supported depending upon the 
         tmnxBundleType value:
         
             tmnxBundleType         tmnxBundleFragmentThreshold values supported
              mlppp                   0; 128 to 512 inclusive
              imagrp                  128"
    DEFVAL      { 128 }
    ::= { tmnxBundleEntry 16 }

tmnxBundleUpTime    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleUpTime indicates the time since the bundle is operationally 
         'inService'."
    ::= { tmnxBundleEntry 17 }

tmnxBundleInputDiscards    OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleInputDiscards indicates the number of LCP packets that
         were discarded. This object is only supported for a 
         tmnxBundleType value of mlppp."
    ::= { tmnxBundleEntry 18 }

tmnxBundlePrimaryMemberPortID    OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundlePrimaryMemberPortID indicates the TmnxPortID of the primary
         member of this bundle. The value of this object will be zero if
         there are no members configured in this bundle."
    ::= { tmnxBundleEntry 19 } 

tmnxBundleLFI    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleLFI specifies if Link Fragmentation and 
         Interleaving is enabled on the multilink bundle. A value of 'false'
         indicates that Link Fragmentation and Interleaving is disabled on the 
         multilink bundle while a value of 'true' indicates that Link 
         Fragmentation and Interleaving is enabled on the multilink bundle.

         This object is only supported for tmnxBundleType value of mlppp.
         
         The value of tmnxBundleLFI may be modified only when no members are 
         part of the bundle.
         
         This object cannot be set to true when tmnxBundleMlpppClassCount object
         value is set to enable Multiclass MLPPP on the bundle."
    DEFVAL      { false }
    ::= { tmnxBundleEntry 20 }

tmnxBundleProtectedType OBJECT-TYPE
    SYNTAX      INTEGER {
                    none       (0),
                    working    (1),
                    protection (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleProtectedType identifies how this bundle is part 
        of a tmnxBundleParentBundle. A value of 'none' identifies no protection,
        'working' identifies it to be a working bundle of a tmnxBundleParentBundle and
        'protection' identifies it to be a protection bundle of a tmnxBundleParentBundle.
        tmnxBundleProtectedType can only be set at row creation."
    DEFVAL { none }
    ::= { tmnxBundleEntry 21 }

tmnxBundleParentBundle OBJECT-TYPE
    SYNTAX      TmnxBundleID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleParentBundle specifies the parent to which this bundle belongs.
        A row for the given non zero walue of tmnxBundleParentBundle must exist for 
        this row to be successfully created. If a bundle has no parent, its value
        is zero. tmnxBundleParentBundle can only be set at row creation."
    DEFVAL { 0 }
    ::= { tmnxBundleEntry 22 }

--
-- Bundle Link Table
--
tmnxBundleMemberTable   OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxBundleMemberEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxBundleMemberTable has an entry for a member port 
         associated with a multilink bundle."
    ::= { tmnxPortObjs 15 }

tmnxBundleMemberEntry       OBJECT-TYPE
    SYNTAX      TmnxBundleMemberEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row represents a port associated with a bundle. Entries
         can be added or removed from this table using SNMP SET operation."
    INDEX   { tmnxBundleBundleID, tmnxPortPortID }
    ::= { tmnxBundleMemberTable 1 }

TmnxBundleMemberEntry ::=
    SEQUENCE {
        tmnxBundleMemberRowStatus           RowStatus,
        tmnxBundleMemberActive              TruthValue,
        tmnxBundleMemberDownReason          INTEGER,
        tmnxBundleMemberUpTime              Unsigned32
    }

tmnxBundleMemberRowStatus    OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberRowStatus controls the creation and deletion of
         row entries in the tmnxBundleMemberTable.
         tmnxPortPortID identifies the port to be associated with the bundle.
         A bundle entry with tmnxBundleBundleID has to exist in the 
         tmnxBundleTable before creating an entry in this table."
    ::= { tmnxBundleMemberEntry 1 }

tmnxBundleMemberActive    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberActive is set to 'false' for one of reasons in 
         tmnxBundleMemberDownReason."
    ::= { tmnxBundleMemberEntry 2 }

tmnxBundleMemberDownReason    OBJECT-TYPE
    SYNTAX      INTEGER {
                    none (0),
                    outOfService (1),
                    redDiffDelayExceeded (2),
                    mismatchEndPtDiscriminator (3),
                    peerNotBundleMember (4),
                    underNegotiation (5),
                    peerInvalidMlHdrFmt(6)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberDownReason contains the reason code for marking
         the member link of the bundle to down (i.e. tmnxBundleMemberActive
         will be set to false):
             none - The link is active and the value of tmnxBundleMemberActive
                    is 'true'.
             outOfService - The link operational status is down.
             redDiffDelayExceeded - The differential delay of the link
                    exceeded the configured value of red differential delay
                    and tmnxBundleRedDiffDelayAction was configured as 'down'.
             mismatchEndPtDiscriminator - Detected inconsistent peer endpoint 
                    discriminator for the bundle.
             peerNotBundleMember - When the peer is either not added to the
                    bundle or is removed from the bundle.
             underNegotiation - When the local end is trying to negotiate with
                    the peer.
             peerInvalidMlHdrFmt - The peer does not support the required ML-PPP
                    header format. The remote peer must support the same short/long
                    sequence code, and at least as many classes as configured for the
                    local bundle.
        "
    ::= { tmnxBundleMemberEntry 3 }

tmnxBundleMemberUpTime    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberUpTime indicates the time since the bundle member is
         active as indicated by tmnxBundleMemberActive."
    ::= { tmnxBundleMemberEntry 4 }

--
-- Physical Port to Channel Mapping Table
--
tmnxPortToChannelTable   OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortToChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxPortToChannelTable is a mapping table. Given a chassis index,
         physical port index and channel string, it maps it to the index
         of the channel. This channel index can then be used as an index
         into the ifTable or the tmnxPortTable."
    ::= { tmnxPortObjs 16 }

tmnxPortToChannelEntry       OBJECT-TYPE
    SYNTAX      TmnxPortToChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row represents a mapping of the physical port index and the
         channel index string to the index of the channel."
    INDEX   { tmnxChassisIndex, tmnxPortPortID, tmnxChannelIdxString }
    ::= { tmnxPortToChannelTable 1 }

TmnxPortToChannelEntry ::=
    SEQUENCE {
        tmnxChannelIdxString     DisplayString,
        tmnxChannelPortID        TmnxPortID
    }

tmnxChannelIdxString    OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxChannelIdxString is an index into the table. It contains 
         a string identifying a channel on the physical port."
    ::= { tmnxPortToChannelEntry 1 }

tmnxChannelPortID    OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxChannelPortID contains the port ID of the channel. This value
         is used to identify the entry for this channel in the ifTable or 
         the tmnxPortTable."
    ::= { tmnxPortToChannelEntry 2 }

--
--  Network Port ingress MDA QoS queue drop Stats Table
--
tmnxPortIngrMdaQosStatTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortIngrMdaQosStatEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table tmnxPortIngrMdaQosStatTable contains a row for each ethernet
         port located on an oversubscribed MDA."
    ::= { tmnxPortObjs 17 }

tmnxPortIngrMdaQosStatEntry    OBJECT-TYPE
    SYNTAX      TmnxPortIngrMdaQosStatEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry indicates the MDA QoS discard statistics for an
         ethernet port located on an oversubscribed MDA.
         tmnxPortEtherIngressRate object in the tmnxPortEtherTable indicates
         the ingress rate configured for the port." 
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxPortIngrMdaQosStatTable 1 }

TmnxPortIngrMdaQosStatEntry ::=
    SEQUENCE {
        tmnxPortIngrMdaQos00StatDropPkts   Counter64,
        tmnxPortIngrMdaQos00StatDropOcts   Counter64,
        tmnxPortIngrMdaQos01StatDropPkts   Counter64,
        tmnxPortIngrMdaQos01StatDropOcts   Counter64,
        tmnxPortIngrMdaQos02StatDropPkts   Counter64,
        tmnxPortIngrMdaQos02StatDropOcts   Counter64,
        tmnxPortIngrMdaQos03StatDropPkts   Counter64,
        tmnxPortIngrMdaQos03StatDropOcts   Counter64,
        tmnxPortIngrMdaQos04StatDropPkts   Counter64,
        tmnxPortIngrMdaQos04StatDropOcts   Counter64,
        tmnxPortIngrMdaQos05StatDropPkts   Counter64,
        tmnxPortIngrMdaQos05StatDropOcts   Counter64,
        tmnxPortIngrMdaQos06StatDropPkts   Counter64,
        tmnxPortIngrMdaQos06StatDropOcts   Counter64,
        tmnxPortIngrMdaQos07StatDropPkts   Counter64,
        tmnxPortIngrMdaQos07StatDropOcts   Counter64,
        tmnxPortIngrMdaQos08StatDropPkts   Counter64,
        tmnxPortIngrMdaQos08StatDropOcts   Counter64,
        tmnxPortIngrMdaQos09StatDropPkts   Counter64,
        tmnxPortIngrMdaQos09StatDropOcts   Counter64,
        tmnxPortIngrMdaQos10StatDropPkts   Counter64,
        tmnxPortIngrMdaQos10StatDropOcts   Counter64,
        tmnxPortIngrMdaQos11StatDropPkts   Counter64,
        tmnxPortIngrMdaQos11StatDropOcts   Counter64,
        tmnxPortIngrMdaQos12StatDropPkts   Counter64,
        tmnxPortIngrMdaQos12StatDropOcts   Counter64,
        tmnxPortIngrMdaQos13StatDropPkts   Counter64,
        tmnxPortIngrMdaQos13StatDropOcts   Counter64,
        tmnxPortIngrMdaQos14StatDropPkts   Counter64,
        tmnxPortIngrMdaQos14StatDropOcts   Counter64,
        tmnxPortIngrMdaQos15StatDropPkts   Counter64,
        tmnxPortIngrMdaQos15StatDropOcts   Counter64
    }

tmnxPortIngrMdaQos00StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos00StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 1 }

tmnxPortIngrMdaQos00StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos00StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 2 }

tmnxPortIngrMdaQos01StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos01StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 3 }

tmnxPortIngrMdaQos01StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos01StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 4 }

tmnxPortIngrMdaQos02StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos02StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 5 }

tmnxPortIngrMdaQos02StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos02StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 6 }

tmnxPortIngrMdaQos03StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos03StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 7 }

tmnxPortIngrMdaQos03StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos03StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 8 }

tmnxPortIngrMdaQos04StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos04StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 9 }

tmnxPortIngrMdaQos04StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos04StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 10 }

tmnxPortIngrMdaQos05StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos05StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 11 }

tmnxPortIngrMdaQos05StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos05StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 12 }

tmnxPortIngrMdaQos06StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos06StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 13 }

tmnxPortIngrMdaQos06StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos06StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 14 }

tmnxPortIngrMdaQos07StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos07StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 15 }

tmnxPortIngrMdaQos07StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos07StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 16 }

tmnxPortIngrMdaQos08StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos08StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 17 }

tmnxPortIngrMdaQos08StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos08StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 18 }

tmnxPortIngrMdaQos09StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos09StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 19 }

tmnxPortIngrMdaQos09StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos09StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 20 }

tmnxPortIngrMdaQos10StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos10StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 21 }

tmnxPortIngrMdaQos10StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos10StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 22 }

tmnxPortIngrMdaQos11StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos11StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 23 }

tmnxPortIngrMdaQos11StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos11StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 24 }

tmnxPortIngrMdaQos12StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos12StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 25 }

tmnxPortIngrMdaQos12StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos12StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 26 }

tmnxPortIngrMdaQos13StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos13StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 27 }

tmnxPortIngrMdaQos13StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos13StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 28 }

tmnxPortIngrMdaQos14StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos14StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 29 }

tmnxPortIngrMdaQos14StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos14StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 30 }

tmnxPortIngrMdaQos15StatDropPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos15StatDropPkts indicates the number of
         packets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 31 }

tmnxPortIngrMdaQos15StatDropOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortIngrMdaQos15StatDropOcts indicates the number of
         octets dropped on the oversubscribed MDA for given Qos
         classifier result because of an overload condition on the MDA."
    ::= { tmnxPortIngrMdaQosStatEntry 32 }

--
--  Sonet Group Table
--

tmnxSonetGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxSonetGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxSonetGroupTable has an entry for each 
         Tributary Unit Group(TUG3) on a path. On a port which supports
         unchannelized STS-3's, rows in this table are created when a
         channelized STS-3 has its tmnxSonetPathChildType set to sdhTug3.
         On a port which does not support unchannelized STS-3's, the rows
         in this table are implicitly created when a STS-3 is created
         in the tmnxSonetPathTable.  Entries in this table are implicitly
         deleted when the parent entry of this group is deleted from the
         tmnxSonetPathTable or if the tmnxSonetPathChildType of the parent
         is set to something other than sdhTug3."
    ::= { tmnxPortObjs 18 }

tmnxSonetGroupEntry       OBJECT-TYPE
    SYNTAX      TmnxSonetGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a Tributary Unit Group(TUG3) on a path."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxSonetGroupTable 1 }

TmnxSonetGroupEntry ::=
    SEQUENCE {
        tmnxSonetGroupType              TmnxMDAChanType,
        tmnxSonetGroupParentPortID      TmnxPortID,
        tmnxSonetGroupChildType         TmnxMDAChanType,
        tmnxSonetGroupName              TNamedItemOrEmpty
    }

tmnxSonetGroupType     OBJECT-TYPE
    SYNTAX      TmnxMDAChanType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxSonetGroupType indicates the sonet group type of this entry.
         For STS-3/STM-1 channelization, the value of this object will 
         be 'sdhTug3'."
    ::= { tmnxSonetGroupEntry 1 }

tmnxSonetGroupParentPortID     OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxSonetGroupParentPortID indicates the TmnxPortID of the parent of
         this group."
    ::= { tmnxSonetGroupEntry 2 }

tmnxSonetGroupChildType     OBJECT-TYPE
    SYNTAX      TmnxMDAChanType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "tmnxSonetGroupChildType is used to configure the type of the children
         of this group. Based on the value of this object, entries of
         a certain channel type can be created in the tmnxSonetPathTable.
         For example, if the value of this object is set to 'sonetVt15', 
         then 28 entries of type 'sonetVt15' can be created in 
         the tmnxSonetPathTable."
    ::= { tmnxSonetGroupEntry 3 }

tmnxSonetGroupName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxSonetGroupName is the textual name for this sonet group." 
    ::= { tmnxSonetGroupEntry 4 }

--
--  Cisco HDLC Table
--

tmnxCiscoHDLCTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCiscoHDLCEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCiscoHDLCTable has an entry for each port in the system 
         that is configured for Cisco HDLC. It contains the parameters related 
         to Cisco HDLC protocol on this port."
    ::= { tmnxPortObjs 20 }

tmnxCiscoHDLCEntry       OBJECT-TYPE
    SYNTAX      TmnxCiscoHDLCEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCiscoHDLCEntry specifies parameters for a particular 
         interfaces running Cisco HDLC encapsulation. Entries cannot be created 
         and deleted via SNMP SET operations."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxCiscoHDLCTable 1 }

TmnxCiscoHDLCEntry ::=
    SEQUENCE {
        tmnxCiscoHDLCKeepAliveInt        Unsigned32,
        tmnxCiscoHDLCUpCount             Unsigned32,
        tmnxCiscoHDLCDownCount           Unsigned32,
        tmnxCiscoHDLCOperState           TmnxOperState
    }

tmnxCiscoHDLCKeepAliveInt      OBJECT-TYPE
    SYNTAX      Unsigned32 (0..300)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxCiscoHDLCKeepAliveInt specifies the interval in seconds
         used for sending periodic keepalive packets. The default value is set
         to 10 seconds. A value of 0 specifies that the keepalive function is
         turned off.

         It is expected that the nodes at the two endpoints of 
         the Cisco HDLC link are provisioned with the same values."
    DEFVAL      {10}
    ::= { tmnxCiscoHDLCEntry 1 }

tmnxCiscoHDLCUpCount      OBJECT-TYPE
    SYNTAX        Unsigned32 (1..3)
    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCUpCount specifies the number of continual
         keepalive packets that has to be received to declare the link up.

         It is expected that the nodes at the two endpoints of 
         the Cisco HDLC link are provisioned with the same values."
    DEFVAL      {1}
    ::= { tmnxCiscoHDLCEntry 2 }

tmnxCiscoHDLCDownCount        OBJECT-TYPE
    SYNTAX        Unsigned32 (3..16)
    MAX-ACCESS    read-create
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCDownCount specifies the number of 
         'tmnxCiscoHDLCKeepAliveInt' intervals that must
         pass without receiving a keepalive packet before the 
         link is declared down.
    
         It is expected that the nodes at the two endpoints of 
         the Cisco HDLC link are provisioned with the same values."
    DEFVAL      {3}
    ::= { tmnxCiscoHDLCEntry 3 }

tmnxCiscoHDLCOperState     OBJECT-TYPE
    SYNTAX      TmnxOperState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCiscoHDLCOperState indicates the operational
         status of the Cisco HDLC protocol for this port."
    ::= { tmnxCiscoHDLCEntry 4 } 


--
--  Cisco HDLC statisctics Table
--

tmnxCiscoHDLCStatsTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxCiscoHDLCStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCiscoHDLCStatsTable has an entry for each port in the
         system that is configured for Cisco HDLC encapsulation. It contains 
         Cisco HDLC protocol statistics for the particular port."
    ::= { tmnxPortStatsObjs 3 }

tmnxCiscoHDLCStatsEntry OBJECT-TYPE
    SYNTAX     TmnxCiscoHDLCStatsEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An entry in the tmnxCiscoHDLCStatsTable."
    AUGMENTS      { tmnxCiscoHDLCEntry }
    ::= { tmnxCiscoHDLCStatsTable 1 }

TmnxCiscoHDLCStatsEntry ::= SEQUENCE {
    tmnxCiscoHDLCDiscardStatInPkts          Unsigned32,
    tmnxCiscoHDLCDiscardStatOutPkts         Unsigned32,
    tmnxCiscoHDLCStatInPkts                 Unsigned32,
    tmnxCiscoHDLCStatOutPkts                Unsigned32,
    tmnxCiscoHDLCStatInOctets               Unsigned32,
    tmnxCiscoHDLCStatOutOctets              Unsigned32
}

tmnxCiscoHDLCDiscardStatInPkts      OBJECT-TYPE
    SYNTAX        Unsigned32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCDiscardStatInPkts indicates the number of 
         inbound Cisco HDLC packets discarded."
    ::= { tmnxCiscoHDLCStatsEntry 1 }

tmnxCiscoHDLCDiscardStatOutPkts     OBJECT-TYPE
    SYNTAX        Unsigned32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCDiscardStatOutPkts indicates the number of 
         outbound Cisco HDLC packets discarded."
    ::= { tmnxCiscoHDLCStatsEntry 2 }

tmnxCiscoHDLCStatInPkts         OBJECT-TYPE
    SYNTAX        Unsigned32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCStatInPkts indicates the number of inbound Cisco 
         HDLC packets."
    ::= { tmnxCiscoHDLCStatsEntry 3 }

tmnxCiscoHDLCStatOutPkts        OBJECT-TYPE
    SYNTAX        Unsigned32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCStatOutPkts indicates the number of outbound Cisco 
         HDLC packets."
    ::= { tmnxCiscoHDLCStatsEntry 4 }

tmnxCiscoHDLCStatInOctets     OBJECT-TYPE
    SYNTAX        Unsigned32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCStatInOctets indicates the number of inbound Cisco
         HDLC octets."
    ::= { tmnxCiscoHDLCStatsEntry 5 }

tmnxCiscoHDLCStatOutOctets     OBJECT-TYPE
    SYNTAX        Unsigned32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "tmnxCiscoHDLCStatOutOctets indicates the number of outbound 
         Cisco HDLC octets."
    ::= { tmnxCiscoHDLCStatsEntry 6 }

--
--  Frame Relay Protocol Table
--
tmnxFRDlcmiTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxFRDlcmiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxFRDlcmiTable has an entry for each port in the
         system that is configured for Frame Relay. It contains 
         the parameters for the Data Link Connection Management
         Interface (DLCMI) for the frame relay service on this port."
    ::= { tmnxFRObjs 1 }

tmnxFRDlcmiEntry       OBJECT-TYPE
    SYNTAX      TmnxFRDlcmiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Parameters for a particular Data Link Connection
         Management Interface. Entries cannot be created and 
         deleted via SNMP SET operations."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxFRDlcmiTable 1 }

TmnxFRDlcmiEntry ::=
    SEQUENCE {
        tmnxFRDlcmiMode                 INTEGER,
        tmnxFRDlcmiN392Dce              INTEGER,
        tmnxFRDlcmiN393Dce              INTEGER,
        tmnxFRDlcmiT392Dce              INTEGER,
        tmnxFRDlcmiTxStatusEnqMsgs      Counter32,
        tmnxFRDlcmiRxStatusEnqMsgs      Counter32,
        tmnxFRDlcmiStatusEnqMsgTimeouts Counter32,
        tmnxFRDlcmiTxStatusMsgs         Counter32,
        tmnxFRDlcmiRxStatusMsgs         Counter32,
        tmnxFRDlcmiStatusMsgTimeouts    Counter32,
        tmnxFRDlcmiDiscardedMsgs        Counter32,
        tmnxFRDlcmiInvRxSeqNumMsgs      Counter32
    }

tmnxFRDlcmiMode OBJECT-TYPE
        SYNTAX      INTEGER {
                        dte (1),
                        dce (2),
                        bidir(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiMode is used to set the mode of the interface.
             It can be set as Data terminal equipment (dte),  
             Data circuit-terminating equipment (dce) or both as Data 
             terminal equipment and Data circuit-terminating 
             equipment (bidir). "
        DEFVAL { dte }
        ::= { tmnxFRDlcmiEntry 1 }

tmnxFRDlcmiN392Dce OBJECT-TYPE
        SYNTAX      INTEGER (1..10)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiN392Dce is used to configure the LMI error 
             threshold for DCE interface."
        DEFVAL { 3 }
        ::= { tmnxFRDlcmiEntry 2 }

tmnxFRDlcmiN393Dce OBJECT-TYPE
        SYNTAX      INTEGER (1..10)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiN393Dce is used to configure the LMI monitored 
             event count for DCE interface."
        DEFVAL { 4 }
        ::= { tmnxFRDlcmiEntry 3 }

tmnxFRDlcmiT392Dce OBJECT-TYPE
        SYNTAX      INTEGER (5..30)
        UNITS       "seconds"
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiT392Dce is used to configure the polling verification 
             timer for the Frame Relay DCE interface."
        DEFVAL { 15 }
        ::= { tmnxFRDlcmiEntry 4 }

tmnxFRDlcmiTxStatusEnqMsgs OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiTxStatusEnqMsgs indicates the number of LMI Status
             Enquiry messages transmitted on this Frame Relay interface."
        ::= { tmnxFRDlcmiEntry 5 }

tmnxFRDlcmiRxStatusEnqMsgs OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiRxStatusEnqMsgs indicates the number of LMI Status
             Enquiry messages received on this Frame Relay interface."
        ::= { tmnxFRDlcmiEntry 6 }

tmnxFRDlcmiStatusEnqMsgTimeouts OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiStatusEnqMsgTimeouts indicates the number of times 
             the LMI agent did not receive a Status Enquiry message within
             the allotted time."
        ::= { tmnxFRDlcmiEntry 7 }

tmnxFRDlcmiTxStatusMsgs OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiTxStatusMsgs indicates the number of LMI Status
             messages transmitted on this Frame Relay interface."
        ::= { tmnxFRDlcmiEntry 8 }

tmnxFRDlcmiRxStatusMsgs OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiRxStatusMsgs indicates the number of LMI Status
             messages received on this Frame Relay interface."
        ::= { tmnxFRDlcmiEntry 9 }

tmnxFRDlcmiStatusMsgTimeouts OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiStatusMsgTimeouts indicates the number of times 
             the LMI agent did not receive a Status message within the
             allotted time."
        ::= { tmnxFRDlcmiEntry 10 }

tmnxFRDlcmiDiscardedMsgs OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiDiscardedMsgs indicates the number of times 
             the LMI agent discarded a received message because it
             wasn't expecting it, the type of message was incorrect,
             or the contents of the message were invalid."
        ::= { tmnxFRDlcmiEntry 11 }

tmnxFRDlcmiInvRxSeqNumMsgs OBJECT-TYPE
        SYNTAX      Counter32
        UNITS       "messages"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "tmnxFRDlcmiInvRxSeqNumMsgs indicates the number of times 
             the LMI agent received a message with an invalid receive
             sequence number: i.e. a sequence number that does not
             match the last transmitted sequence number of the agent."
        ::= { tmnxFRDlcmiEntry 12 }

--
-- QoS Policy Application Table
-- 
tmnxQosPoolAppTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxQosPoolAppEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table maintains associations of the buffer pools with the
        individual objects."
    ::= { tmnxQosAppObjs 2 }

tmnxQosPoolAppEntry OBJECT-TYPE
    SYNTAX      TmnxQosPoolAppEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This entry maintains the specifics about the association of a 
        buffer pool and an object."
    INDEX { 
              tmnxChassisIndex, 
              tmnxObjectType,
              tmnxObjectId, 
              tmnxObjectAppType, 
              tmnxObjectAppPool 
          }
    ::= { tmnxQosPoolAppTable 1 }

TmnxQosPoolAppEntry ::=
    SEQUENCE {
        tmnxObjectType               INTEGER,
        tmnxObjectId                 TmnxPortID,
        tmnxObjectAppType            INTEGER,
        tmnxObjectAppPool            TNamedItem,
        tmnxObjectAppPoolRowStatus   RowStatus,
        tmnxObjectAppResvCbs         Integer32,
        tmnxObjectAppSlopePolicy     TNamedItem,
        tmnxObjectAppPoolSize        Integer32
    }

tmnxObjectType OBJECT-TYPE
    SYNTAX      INTEGER {
                    mda (1),
                    port (2),
                    channel (3),
                    bundle (4)
                }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The object tmnxObjectType defines the type of object to which the 
        pool is being setup for."
    ::= { tmnxQosPoolAppEntry 1 }

tmnxObjectId          OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxObjectId is used to identify the specific object. If 
        tmnxObjectType is 'mda', then the tmnxObjectId is encoded as a
        physical port with the port being '0'. For all other types the 
        encoding is done as per the description of TmnxPortID."
    ::= { tmnxQosPoolAppEntry 2 }

tmnxObjectAppType OBJECT-TYPE
    SYNTAX      INTEGER {
                    accessIngress (1),
                    accessEgress (2),
                    networkIngress (3),
                    networkEgress (4)
                }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The object tmnxObjectAppType specifies the nature of usage the pool 
        would be used for. The pools could be used for access or network
        traffic at either ingress or egress."
    ::= { tmnxQosPoolAppEntry 3 }

tmnxObjectAppPool OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxObjectAppPool specifies the name of the pool to be used/created."
    ::= { tmnxQosPoolAppEntry 4 }

tmnxObjectAppPoolRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The object tmnxObjectAppPoolRowStatus is used to create or remove a 
        pool application to the object."
    ::= { tmnxQosPoolAppEntry 5 }

tmnxObjectAppResvCbs OBJECT-TYPE
    SYNTAX      Integer32 (-1|0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The percentage of pool size reserved for CBS. The value '-1'
        implies that the reserved CBS should be computed as the sum of 
        the CBS requested by the entities using this pool if the application
        point is 'network'. For 'access' application points the value '-1'
        means a default of 30%."
    DEFVAL { -1 }
    ::= { tmnxQosPoolAppEntry 6 }

tmnxObjectAppSlopePolicy OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxObjectAppSlopePolicy specifies the slope policy being used for 
        this pool. The Slope policies define the nature of the RED Slopes for 
        the high and the low priority traffic."
    DEFVAL { "default" }   
    ::= { tmnxQosPoolAppEntry 7 }

tmnxObjectAppPoolSize OBJECT-TYPE
    SYNTAX      Integer32 (-1|0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Size in percentage of buffer space. The value '-1' implies that 
        the pool size should be computed as per fair weighing between
        all other pools."
    DEFVAL { -1 }
    ::= { tmnxQosPoolAppEntry 8 }

--
--  ATM Interface Table
--

tmnxATMIntfTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxATMIntfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxATMIntfTable has an entry for each port/channel in the
         system that is configured for ATM. It contains ATM cell layer 
         configuration parameters."
    ::= { tmnxATMObjs 1 }

tmnxATMIntfEntry       OBJECT-TYPE
    SYNTAX      TmnxATMIntfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Parameters for a particular ATM Cell 
         Management Interface. Entries cannot be created or 
         deleted via SNMP SET operations.
         Entries will exist in this table when ATM encapsulation
         is configured on the port/channel."
    INDEX   { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxATMIntfTable 1 }

TmnxATMIntfEntry ::=
    SEQUENCE {
        tmnxATMIntfCellFormat        INTEGER,
        tmnxATMIntfMinVpValue        Integer32,
        tmnxATMIntfMapping           INTEGER
    }

tmnxATMIntfCellFormat OBJECT-TYPE
    SYNTAX      INTEGER {
                    uni (1),
                    nni (2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "tmnxATMIntfCellFormat is used to set the cell format 
         (UNI or NNI) that is to be used on the ATM interface."
    DEFVAL { uni }
    ::= { tmnxATMIntfEntry 1 }

tmnxATMIntfMinVpValue OBJECT-TYPE
        SYNTAX      Integer32 (0..4095)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "tmnxATMIntfMinVpValue is used to set the minimum 
             allowable VPI value that can be used on the  
             ATM interface for a VPC."
        DEFVAL { 0 }
        ::= { tmnxATMIntfEntry 2 }

tmnxATMIntfMapping OBJECT-TYPE
        SYNTAX      INTEGER {
                        direct (1),
                        plcp (2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The value of tmnxATMIntfMapping specifies the cell
             mapping that is to be used on this ATM interface.  When
             tmnxPortChanType does not have a value of 'pdhDs3 (14)',
             'inconsistentValue' error will be returned to SNMP SET
             requests.  When tmnxPortChanType has a value of 'pdhDs3 (14)', 
             tmnxATMIntfMapping can be set to 'plcp (2)'."
        DEFVAL { direct }
        ::= { tmnxATMIntfEntry 3 }


--
--  Network Port Ingress Stats Table
--
tmnxPortNetIngressStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortNetIngressStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR series network port ingress 
         statistics table for providing, via SNMP, the capability of
         retrieving the traffic statistics for the physical queues
         being used for the ports to forward the network ingress
         traffic."
    ::= { tmnxPortStatsObjs 1 }

tmnxPortNetIngressStatsEntry    OBJECT-TYPE
    SYNTAX      TmnxPortNetIngressStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in tmnxPortNetIngressStatsTable. Entries are 
         created and deleted by the system depending on the queue policy 
         being used at the specific port."
    INDEX   { tmnxChassisIndex, tmnxPortPortID, tmnxPortNetIngressQueueIndex }
    ::= { tmnxPortNetIngressStatsTable 1 }

TmnxPortNetIngressStatsEntry ::=
    SEQUENCE {
        tmnxPortNetIngressQueueIndex      TQueueId,
        tmnxPortNetIngressFwdInProfPkts   Counter64,
        tmnxPortNetIngressFwdOutProfPkts  Counter64,
        tmnxPortNetIngressFwdInProfOcts   Counter64,
        tmnxPortNetIngressFwdOutProfOcts  Counter64,
        tmnxPortNetIngressDroInProfPkts   Counter64,
        tmnxPortNetIngressDroOutProfPkts  Counter64,
        tmnxPortNetIngressDroInProfOcts   Counter64,
        tmnxPortNetIngressDroOutProfOcts  Counter64
    }

tmnxPortNetIngressQueueIndex OBJECT-TYPE
    SYNTAX      TQueueId (1..16)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressQueueIndex serves as the tertiary index. When used
         in conjunction with tmnxChassisIndex and tmnxPortPortID, it uniquely 
         identifies a network ingress queue for the specified port in the
         managed system."
    ::= { tmnxPortNetIngressStatsEntry 1 }

tmnxPortNetIngressFwdInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressFwdInProfPkts indicates the number of conforming
         network ingress packets forwarded on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 2 }

tmnxPortNetIngressFwdOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressFwdOutProfPkts indicates the number of exceeding
         network ingress packets forwarded on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 3 }

tmnxPortNetIngressFwdInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressFwdInProfOcts indicates the number of conforming
         network ingress octets forwarded on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 4 }

tmnxPortNetIngressFwdOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressFwdOutProfOcts indicates the number of exceeding
         network ingress octets forwarded on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 5 }

tmnxPortNetIngressDroInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressDroInProfPkts indicates the number of conforming
         network ingress packets dropped on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 6 }

tmnxPortNetIngressDroOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressDroOutProfPkts indicates the number of exceeding
         network ingress packets dropped on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 7 }

tmnxPortNetIngressDroInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressDroInProfOcts indicates the number of conforming
         network ingress octets dropped on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 8 }

tmnxPortNetIngressDroOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetIngressDroOutProfOcts indicates the number of exceeding
         network ingress octets dropped on this port using this queue."
    ::= { tmnxPortNetIngressStatsEntry 9 }

--
--  Network Port Egress Stats Table
--
tmnxPortNetEgressStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortNetEgressStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR series network port egress 
         statistics table for providing, via SNMP, the capability of
         retrieving the traffic statistics for the physical queues
         being used for the ports to forward the network egress
         traffic."
    ::= { tmnxPortStatsObjs 2 }

tmnxPortNetEgressStatsEntry    OBJECT-TYPE
    SYNTAX      TmnxPortNetEgressStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in tmnxPortNetEgressStatsTable. Entries are 
         created and deleted by the system depending on the queue policy 
         being used at the specific port."
    INDEX   { tmnxChassisIndex, tmnxPortPortID, tmnxPortNetEgressQueueIndex }
    ::= { tmnxPortNetEgressStatsTable 1 }

TmnxPortNetEgressStatsEntry ::=
    SEQUENCE {
        tmnxPortNetEgressQueueIndex      TQueueId,
        tmnxPortNetEgressFwdInProfPkts   Counter64,
        tmnxPortNetEgressFwdOutProfPkts  Counter64,
        tmnxPortNetEgressFwdInProfOcts   Counter64,
        tmnxPortNetEgressFwdOutProfOcts  Counter64,
        tmnxPortNetEgressDroInProfPkts   Counter64,
        tmnxPortNetEgressDroOutProfPkts  Counter64,
        tmnxPortNetEgressDroInProfOcts   Counter64,
        tmnxPortNetEgressDroOutProfOcts  Counter64
    }

tmnxPortNetEgressQueueIndex OBJECT-TYPE
    SYNTAX      TQueueId (1..8)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressQueueIndex serves as the tertiary index. When used
         in conjunction with tmnxChassisIndex and tmnxPortPortID, it uniquely 
         identifies a network egress queue for the specified port in the
         managed system."
    ::= { tmnxPortNetEgressStatsEntry 1 }

tmnxPortNetEgressFwdInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressFwdInProfPkts indicates the number of conforming
         network egress packets forwarded on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 2 }

tmnxPortNetEgressFwdOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressFwdOutProfPkts indicates the number of exceeding
         network egress packets forwarded on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 3 }

tmnxPortNetEgressFwdInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressFwdInProfOcts indicates the number of conforming
         network egress octets forwarded on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 4 }

tmnxPortNetEgressFwdOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressFwdOutProfOcts indicates the number of exceeding
         network egress octets forwarded on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 5 }

tmnxPortNetEgressDroInProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressDroInProfPkts indicates the number of conforming
         network egress packets dropped on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 6 }

tmnxPortNetEgressDroOutProfPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressDroOutProfPkts indicates the number of exceeding
         network egress packets dropped on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 7 }

tmnxPortNetEgressDroInProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressDroInProfOcts indicates the number of conforming
         network egress octets dropped on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 8 }

tmnxPortNetEgressDroOutProfOcts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxPortNetEgressDroOutProfOcts indicates the number of exceeding
         network egress octets dropped on this port using this queue."
    ::= { tmnxPortNetEgressStatsEntry 9 }

--
--  Multiclass MLPPP Bundle Stats Table
--
--  This table provides per MClass MLPPP Bundle traffic statistics
--  The rows are created when tmnxBundleMlpppEntry has tmnxBundleMlpppClassCount
--  value set to enable Mclass MLPPP on a given bundle.
--

tmnxMcMlpppStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxMcMlpppStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR series Multiclass MLPPP
         statistics table for providing the capability of
         retrieving the traffic statistics for the physical queues
         being used for a class of a multiclass MLPPP bundle to 
         forward the traffic."
    ::= { tmnxPortStatsObjs 4 }

tmnxMcMlpppStatsEntry    OBJECT-TYPE
    SYNTAX      TmnxMcMlpppStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in tmnxMcMlpppStatsTable. Entries are 
         created and deleted by the system depending on the number of
         classes being used by a given MLPPP bundle."
    INDEX   { tmnxChassisIndex, tmnxBundleBundleID, tmnxMcMlpppClassIndex }
    ::= { tmnxMcMlpppStatsTable 1 }

TmnxMcMlpppStatsEntry ::=
    SEQUENCE {
        tmnxMcMlpppClassIndex          TmnxMcMlpppClassIndex,
        tmnxMcMlpppStatsIngressOct     Counter32,
        tmnxMcMlpppStatsIngressPkt     Counter32,
        tmnxMcMlpppStatsIngressErrPkt  Counter32,
        tmnxMcMlpppStatsEgressOct      Counter32,
        tmnxMcMlpppStatsEgressPkt      Counter32,
        tmnxMcMlpppStatsEgressErrPkt   Counter32
    }

tmnxMcMlpppClassIndex  OBJECT-TYPE
    SYNTAX      TmnxMcMlpppClassIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxMcMlpppClassIndex serves as the tertiary index. When used
         in conjunction with tmnxChassisIndex and tmnxBundleBundleID, it 
         uniquely identifies a class of a multiclass MLPPP bundle in the
         managed system."
    ::= { tmnxMcMlpppStatsEntry 1 }


tmnxMcMlpppStatsIngressOct OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMcMlpppStatsIngressOct indicates the total 
         number of octets in all packets received on the bundle
         for the given class on ingress before reassembly."
    ::= { tmnxMcMlpppStatsEntry 2 }

tmnxMcMlpppStatsIngressPkt OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMcMlpppStatsIngressPkt indicates the total 
         number of packets forwarded on the bundle for the given
         class on ingress towards higher layer protocols."
    ::= { tmnxMcMlpppStatsEntry 3 }

tmnxMcMlpppStatsIngressErrPkt OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMcMlpppStatsIngressErrPkt indicates the total 
         number of packets discarded due to reassembly errors 
         on the bundle for the given class on ingress."
    ::= { tmnxMcMlpppStatsEntry 4 }

tmnxMcMlpppStatsEgressOct OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMcMlpppStatsEgressOct indicates the total 
         number of octets in all packets received on the bundle
         for the given class on egress before segmentation."
    ::= { tmnxMcMlpppStatsEntry 5 }

tmnxMcMlpppStatsEgressPkt OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMcMlpppStatsEgressPkt indicates the total 
         number of packets forwarded on the bundle for the given
         class on egress towards the line."
    ::= { tmnxMcMlpppStatsEntry 6 }

tmnxMcMlpppStatsEgressErrPkt OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMcMlpppStatsEgressErrPkt indicates the total 
         number of packets discarded due to segmentation errors
         on the bundle for the given class on egress."
    ::= { tmnxMcMlpppStatsEntry 7 }

--
-- IMA Group Bundle Table
--
--  Sparse Dependent Extension of the tmnxBundleTable.
--
--  The same indexes are used for both the base table tmnxBundleTable, 
--  and the sparse dependent table, tmnxBundleImaGrpTable. 
--
--  This in effect extends the tmnxBundleTable with additional columns.
--  Rows are created in the tmnxBundleImaGrpTable only for those entries 
--  in the tmnxBundleTable for a value of 'imagrp' for the bundle type.  
--  
--  Deletion of a row in the tmnxBundleTable results in the 
--  same fate for the row in the tmnxBundleImaGrpTable.
--
tmnxBundleImaGrpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxBundleImaGrpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxBundleImaGrpTable contains IMA Group data"
    ::= { tmnxPortObjs 21 }

tmnxBundleImaGrpEntry OBJECT-TYPE
    SYNTAX      TmnxBundleImaGrpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry contains data on an IMA Group."
    INDEX       { tmnxChassisIndex, tmnxBundleBundleID }
    ::= { tmnxBundleImaGrpTable 1 }

TmnxBundleImaGrpEntry ::=
    SEQUENCE {
        tmnxBundleImaGrpLnkActTimer     Unsigned32,
        tmnxBundleImaGrpLnkDeactTimer   Unsigned32,
        tmnxBundleImaGrpSymmetryMode    INTEGER,
        tmnxBundleImaGrpTxId            INTEGER,
        tmnxBundleImaGrpRxId            INTEGER,
        tmnxBundleImaGrpTxRefLnk        TmnxPortID,
        tmnxBundleImaGrpRxRefLnk        TmnxPortID,
        tmnxBundleImaGrpSmNeState       TmnxImaGrpState,
        tmnxBundleImaGrpSmFeState       TmnxImaGrpState,
        tmnxBundleImaGrpSmFailState     TmnxImaGrpFailState,
        tmnxBundleImaGrpSmDownSecs      Counter32,
        tmnxBundleImaGrpSmOperSecs      Counter32,
        tmnxBundleImaGrpAvailTxCR       Gauge32,
        tmnxBundleImaGrpAvailRxCR       Gauge32,
        tmnxBundleImaGrpNeFails         Counter32,
        tmnxBundleImaGrpFeFails         Counter32,
        tmnxBundleImaGrpTxIcpCells      Counter32,
        tmnxBundleImaGrpRxIcpCells      Counter32,
        tmnxBundleImaGrpErrorIcpCells   Counter32,
        tmnxBundleImaGrpLostRxIcpCells  Counter32,
        tmnxBundleImaGrpTxOamLablVal    INTEGER,
        tmnxBundleImaGrpRxOamLablVal    INTEGER,
        tmnxBundleImaGrpAlphaValue      INTEGER,
        tmnxBundleImaGrpBetaValue       INTEGER,
        tmnxBundleImaGrpGammaValue      INTEGER,
        tmnxBundleImaGrpNeClockMode     TmnxImaGrpClockModes,
        tmnxBundleImaGrpFeClockMode     TmnxImaGrpClockModes,
        tmnxBundleImaGrpVersion         TmnxImaGrpVersion,   
        tmnxBundleImaGrpMaxConfBw       Unsigned32,
        tmnxBundleImaGrpTestState       TmnxImaTestState,
        tmnxBundleImaGrpTestMember      TmnxPortID,
        tmnxBundleImaGrpTestPattern     INTEGER,
        tmnxBundleImaGrpDiffDelayMaxObs Unsigned32,
        tmnxBundleImaGrpLeastDelayLink  Unsigned32
    }
 
tmnxBundleImaGrpLnkActTimer OBJECT-TYPE
    SYNTAX      Unsigned32 (1..30000)
    UNITS       "milliseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpLnkActTimer specifies the Link Activation Timer
         used to clear an existing LIF, LODS and FRI-IMA alarm. The time
         specified determines how long is needed for member links to 
         stabilize before being activated."
    DEFVAL { 10000 }
    ::= { tmnxBundleImaGrpEntry 1 }

tmnxBundleImaGrpLnkDeactTimer OBJECT-TYPE
    SYNTAX      Unsigned32 (1..30000)
    UNITS       "milliseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpLnkDeactTimer specifies the Link Deactivation Timer
         used to raise an LIF, LODS and FRI-IMA alarm. The time
         specified determines how long before a member link is declared in
         error and is deactivated."
    DEFVAL { 2000 }
    ::= { tmnxBundleImaGrpEntry 2 }
    
tmnxBundleImaGrpSymmetryMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    symmetric (1)
                }    
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpSymmetryMode specifies the type of cell rate
         transfer over the virtual link." 
    DEFVAL { symmetric }
    ::= { tmnxBundleImaGrpEntry 3 }

tmnxBundleImaGrpTxId OBJECT-TYPE
    SYNTAX      INTEGER    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpTxId specifies the IMA Id generated at group
         creation time used by the near-end." 
    ::= { tmnxBundleImaGrpEntry 4 }

tmnxBundleImaGrpRxId OBJECT-TYPE
    SYNTAX      INTEGER    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpRxId specifies the IMA ID generated at group
         creation time used by the far-end." 
    ::= { tmnxBundleImaGrpEntry 5 }

tmnxBundleImaGrpTxRefLnk OBJECT-TYPE
    SYNTAX      TmnxPortID    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpTxRefLnk specifies the IMA bundle member
         that was selected to be the transmit timing reference link.
         If no member has been selected as the reference link a value
         of zero is returned. "
    ::= { tmnxBundleImaGrpEntry 6 }

tmnxBundleImaGrpRxRefLnk OBJECT-TYPE
    SYNTAX      TmnxPortID    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpRxRefLnk specifies the IMA bundle member
         that was detected to be the receive timing reference link.
         If no member has been detected as the reference link a value
         of zero is returned. "
    ::= { tmnxBundleImaGrpEntry 7 }
    
tmnxBundleImaGrpSmNeState OBJECT-TYPE
    SYNTAX      TmnxImaGrpState    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpSmNeState specifies the current state 
         of the IMA Group for the near-end."
    ::= { tmnxBundleImaGrpEntry 8 }

tmnxBundleImaGrpSmFeState OBJECT-TYPE
    SYNTAX      TmnxImaGrpState    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpSmFeState specifies the current state 
         of the IMA Group for the far-end."
    ::= { tmnxBundleImaGrpEntry 9 }

tmnxBundleImaGrpSmFailState OBJECT-TYPE
    SYNTAX      TmnxImaGrpFailState    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpSmFailState specifies the current state 
         of the IMA Group."
    ::= { tmnxBundleImaGrpEntry 10 }
        
tmnxBundleImaGrpSmDownSecs OBJECT-TYPE
    SYNTAX      Counter32    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpSmDownSecs specifies the number of seconds
         that the IMA Group State Machine is not Operational." 
    ::= { tmnxBundleImaGrpEntry 11 }

tmnxBundleImaGrpSmOperSecs OBJECT-TYPE
    SYNTAX      Counter32   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpSmOperSecs specifies the number of seconds
         that the IMA Group State Machine has been in the Operational
         State." 
    ::= { tmnxBundleImaGrpEntry 12 }

tmnxBundleImaGrpAvailTxCR OBJECT-TYPE
    SYNTAX      Gauge32   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpAvailTxCR specifies the available transmit 
         bandwidth on this IMA group. It only considers members that
         are in the Active State." 
    ::= { tmnxBundleImaGrpEntry 13 }

tmnxBundleImaGrpAvailRxCR OBJECT-TYPE
    SYNTAX      Gauge32   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpAvailRxCR specifies the available bandwidth 
         on this IMA group in the receive direction. It only considers
         members that are in the Active State." 
    ::= { tmnxBundleImaGrpEntry 14 }

tmnxBundleImaGrpNeFails OBJECT-TYPE
    SYNTAX      Counter32   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpNeFails specifies the number of reported
         IMA Group failures since boot time." 
    ::= { tmnxBundleImaGrpEntry 15 }

tmnxBundleImaGrpFeFails OBJECT-TYPE
    SYNTAX      Counter32    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpFeFails specifies the number of reported
         IMA Group far-end failures since boot time." 
    ::= { tmnxBundleImaGrpEntry 16 }

tmnxBundleImaGrpTxIcpCells OBJECT-TYPE
    SYNTAX      Counter32    
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpTxIcpCells specifies number of transmitted
         ICP cells for this IMA Group."
    ::= { tmnxBundleImaGrpEntry 17 }

tmnxBundleImaGrpRxIcpCells OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpRxIcpCells specifies number of received
         ICP cells for this IMA Group."
    ::= { tmnxBundleImaGrpEntry 18 }
    
tmnxBundleImaGrpErrorIcpCells OBJECT-TYPE
    SYNTAX      Counter32  
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpErrorIcpCells specifies number of 
         ICP cells with HEC or CRC-10 errors." 
    ::= { tmnxBundleImaGrpEntry 19 }

tmnxBundleImaGrpLostRxIcpCells OBJECT-TYPE
    SYNTAX      Counter32   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpLostRxIcpCells specifies the number of
         missing ICP cells at the expected offset."
    ::= { tmnxBundleImaGrpEntry 20 }

tmnxBundleImaGrpTxOamLablVal OBJECT-TYPE
    SYNTAX      INTEGER   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpTxOamLablVal specifies near-end value
         to be transmitted."
    ::= { tmnxBundleImaGrpEntry 21 }
    
tmnxBundleImaGrpRxOamLablVal OBJECT-TYPE
    SYNTAX      INTEGER   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpRxOamLablVal specifies far-end value
         received."
    ::= { tmnxBundleImaGrpEntry 22 }

tmnxBundleImaGrpAlphaValue OBJECT-TYPE
    SYNTAX      INTEGER    
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpAlphaValue specifies the number of
         consecutive ICP cells that need to be detected
         before moving from the IMA Sync State to the IMA Hunt 
         State."
    DEFVAL { 2 }
    ::= { tmnxBundleImaGrpEntry 23 }

tmnxBundleImaGrpBetaValue OBJECT-TYPE
    SYNTAX      INTEGER   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpBetaValue specifies the number of
         consecutive errored ICP cells that need to be detected
         before moving from the IMA Sync State to the IMA Hunt 
         State"
    DEFVAL { 2 }
    ::= { tmnxBundleImaGrpEntry 24 }

tmnxBundleImaGrpGammaValue OBJECT-TYPE
    SYNTAX      INTEGER       
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpGammaValue specifies the number of
         consecutive valid ICP cells that need to be detected
         before moving from the IMA pre-Sync State to the 
         IMA Hunt State."
    DEFVAL { 1 }
    ::= { tmnxBundleImaGrpEntry 25 }

tmnxBundleImaGrpNeClockMode OBJECT-TYPE
    SYNTAX      TmnxImaGrpClockModes   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpNeClockMode specifies near-end transmit
         clock mode."
    DEFVAL { ctc }
    ::= { tmnxBundleImaGrpEntry 26 }

tmnxBundleImaGrpFeClockMode OBJECT-TYPE
    SYNTAX      TmnxImaGrpClockModes   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpFeClockMode specifies far-end transmit
         clock mode."
    DEFVAL { ctc }
    ::= { tmnxBundleImaGrpEntry 27 }

tmnxBundleImaGrpVersion OBJECT-TYPE
    SYNTAX      TmnxImaGrpVersion   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpVersion specifies the IMA Version for
         this group."
    DEFVAL { oneDotOne }
    ::= { tmnxBundleImaGrpEntry 28 }

tmnxBundleImaGrpMaxConfBw OBJECT-TYPE
    SYNTAX      Unsigned32 (1..8)   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpMaxConfBw specifies the number of links
         that is used to determine the maximum configurable 
         bandwidth that is allowed to be used for this IMA group."
    DEFVAL { 8 }
    ::= { tmnxBundleImaGrpEntry 29 }

tmnxBundleImaGrpTestState OBJECT-TYPE
    SYNTAX      TmnxImaTestState   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpTestState specifies the current state of
         the test pattern procedure. A value of 'disabled' specifies  
         that the group is currently not running. A value of 
         'operating' enables the test pattern procedure. The test
         pattern procedure continues until it is 'disabled'."
    DEFVAL { disabled }
    ::= { tmnxBundleImaGrpEntry 30 }

tmnxBundleImaGrpTestMember OBJECT-TYPE
    SYNTAX      TmnxPortID   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpTestMember specifies a member link to 
         be used for the test pattern procedure. A value of zero
         indicates that no member link has been selected. 
         
         When tmnxBundleImaGrpTestState does not have the value
         'disabled', an attempt to set this object will be  
         rejected with an inconsistentValue error."
    DEFVAL { 0 }
    ::= { tmnxBundleImaGrpEntry 31 }

tmnxBundleImaGrpTestPattern OBJECT-TYPE
    SYNTAX      INTEGER (0..255)   
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpTestPattern specifies the Transmit Test 
         Pattern in an IMA group loopback operation. This value 
         may only be changed when tmnxBundleImaGrpTestState is 
         'disabled'."
    DEFVAL { 0 }
    ::= { tmnxBundleImaGrpEntry 32 }

tmnxBundleImaGrpDiffDelayMaxObs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"   
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpDiffDelayMaxObs indicates the latest 
         maximum differential delay observed between links 
         having the least and most link propagation delay 
         among the received links configured in this IMA group."
    ::= { tmnxBundleImaGrpEntry 33 }

tmnxBundleImaGrpLeastDelayLink OBJECT-TYPE
    SYNTAX      Unsigned32 
    UNITS       "milliseconds"  
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleImaGrpLeastDelayLink indicates a member link 
         which has the smallest link propagation delay. If this
         delay has yet to be determined or no member link has 
         been configured, a value of zero is returned."
    ::= { tmnxBundleImaGrpEntry 34 }
    
--
-- IMA Group Member Table
--
--  Sparse Dependent Extension of the tmnxBundleMemberTable.
--
--  The same indexes are used for both the base table tmnxBundleMemberTable, 
--  and the sparse dependent table, tmnxBundleMemberImaTable. 
--
--  This in effect extends the tmnxBundleMemberTable with additional columns.
--  Rows are created in the tmnxBundleMemberImaTable only for those entries 
--  in the tmnxBundleMemberTable that were created and have a value of 'imagrp' 
--  for the bundle type.  
--  
--  Deletion of a row in the tmnxBundleMemberTable results in the 
--  same fate for the row in the tmnxBundleMemberImaTable.
--

tmnxBundleMemberImaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxBundleMemberImaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxBundleMemberImaTable has an entry for an
         IMA link associated with an IMA Group."
    ::= { tmnxPortObjs 22 }

tmnxBundleMemberImaEntry  OBJECT-TYPE
    SYNTAX      TmnxBundleMemberImaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an IMA link associated with 
         an IMA Group."
    INDEX   { tmnxBundleBundleID, tmnxPortPortID }
    ::= { tmnxBundleMemberImaTable 1 }

TmnxBundleMemberImaEntry ::=
    SEQUENCE {
        tmnxBundleMemberImaNeTxState     TmnxImaLnkState,
        tmnxBundleMemberImaNeRxState     TmnxImaLnkState,
        tmnxBundleMemberImaFeTxState     TmnxImaLnkState,
        tmnxBundleMemberImaFeRxState     TmnxImaLnkState,
        tmnxBundleMemberImaNeRxFailState TmnxImaLnkFailState,
        tmnxBundleMemberImaFeRxFailState TmnxImaLnkFailState,
        tmnxBundleMemberImaTxLid         INTEGER,
        tmnxBundleMemberImaRxLid         INTEGER,
        tmnxBundleMemberImaViolations    Counter32,
        tmnxBundleMemberImaNeSevErrSecs  Counter32,
        tmnxBundleMemberImaFeSevErrSecs  Counter32,
        tmnxBundleMemberImaNeUnavailSecs Counter32,
        tmnxBundleMemberImaFeUnavailSecs Counter32,
        tmnxBundleMemberImaNeTxUnuseSecs Counter32,
        tmnxBundleMemberImaNeRxUnuseSecs Counter32,
        tmnxBundleMemberImaFeTxUnuseSecs Counter32,
        tmnxBundleMemberImaFeRxUnuseSecs Counter32,
        tmnxBundleMemberImaNeTxNumFails  Counter32,
        tmnxBundleMemberImaNeRxNumFails  Counter32,
        tmnxBundleMemberImaFeTxNumFails  Counter32,
        tmnxBundleMemberImaFeRxNumFails  Counter32,
        tmnxBundleMemberImaTxIcpCells    Counter32,
        tmnxBundleMemberImaRxIcpCells    Counter32,
        tmnxBundleMemberImaErrorIcpCells Counter32,
        tmnxBundleMemberImaLstRxIcpCells Counter32,
        tmnxBundleMemberImaOifAnomalies  Counter32,
        tmnxBundleMemberImaRxTestState   TmnxImaTestState,
        tmnxBundleMemberImaRxTestPattern INTEGER,
        tmnxBundleMemberImaRelDelay      Unsigned32
        
    }

tmnxBundleMemberImaNeTxState    OBJECT-TYPE
    SYNTAX      TmnxImaLnkState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeTxState indicates the state of near-end
         transmit IMA link."
    ::= { tmnxBundleMemberImaEntry 1 }

tmnxBundleMemberImaNeRxState    OBJECT-TYPE
    SYNTAX      TmnxImaLnkState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeRxState indicates the state of near-end
         receive IMA link."
    ::= { tmnxBundleMemberImaEntry 2 }
    
tmnxBundleMemberImaFeTxState    OBJECT-TYPE
    SYNTAX      TmnxImaLnkState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeTxState indicates the state of far-end
         transmit IMA link."
    ::= { tmnxBundleMemberImaEntry 3 }                

tmnxBundleMemberImaFeRxState    OBJECT-TYPE
    SYNTAX      TmnxImaLnkState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeRxState indicates the state of far-end
         receive IMA link."
    ::= { tmnxBundleMemberImaEntry 4 }

tmnxBundleMemberImaNeRxFailState    OBJECT-TYPE
    SYNTAX      TmnxImaLnkFailState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeRxFailState indicates the IMA link failure
         reason for the near-end."
    ::= { tmnxBundleMemberImaEntry 5 }

tmnxBundleMemberImaFeRxFailState    OBJECT-TYPE
    SYNTAX      TmnxImaLnkFailState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeRxFailState indicates the IMA link failure
         reason for the far-end."
    ::= { tmnxBundleMemberImaEntry 6 }
        
tmnxBundleMemberImaTxLid OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaTxLid indicates the Link Identifier
         assigned to the transmit IMA link."
    ::= { tmnxBundleMemberImaEntry 7 }

tmnxBundleMemberImaRxLid OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaRxLid indicates the Link Identifier
         used on the receive IMA link."
    ::= { tmnxBundleMemberImaEntry 8 }

tmnxBundleMemberImaViolations OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaViolations indicates the number of
         ICP violations including errored, invalid or missing ICP 
         cells."
    ::= { tmnxBundleMemberImaEntry 9 }

tmnxBundleMemberImaNeSevErrSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeSevErrSecs indicates the number 
         of one second intervals in which thirty percent or 
         more of the near-end ICP cells are in violation, or
         link defects have occurred."
    ::= { tmnxBundleMemberImaEntry 10 }

tmnxBundleMemberImaFeSevErrSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeSevErrSecs indicates the number
         of one second intervals in which the far-end contains
         IMA-RDI defects."
    ::= { tmnxBundleMemberImaEntry 11 }

tmnxBundleMemberImaNeUnavailSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeUnavailSecs indicates the number
         of unavailable seconds at the near-end."
    ::= { tmnxBundleMemberImaEntry 12 }

tmnxBundleMemberImaFeUnavailSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeUnavailSecs indicates the number
         of unavailable seconds at the near-end."
    ::= { tmnxBundleMemberImaEntry 13 }

tmnxBundleMemberImaNeTxUnuseSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeTxUnuseSecs indicates the number
         of unavailable seconds at the near-end transmit link
         state machine."
    ::= { tmnxBundleMemberImaEntry 14 }

tmnxBundleMemberImaNeRxUnuseSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeRxUnuseSecs indicates the number
         of unavailable seconds at the near-end receive link
         state machine."
    ::= { tmnxBundleMemberImaEntry 15 }

tmnxBundleMemberImaFeTxUnuseSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeTxUnuseSecs indicates the number
         of unavailable seconds at the far-end transmit link
         state machine."
    ::= { tmnxBundleMemberImaEntry 16 }

tmnxBundleMemberImaFeRxUnuseSecs OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeRxUnuseSecs indicates the number
         of unavailable seconds at the far-end receive link
         state machine."
    ::= { tmnxBundleMemberImaEntry 17 }

tmnxBundleMemberImaNeTxNumFails OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeTxNumFails indicates the number
         of times that a near-end transmit alarm is set on the
         IMA link."
    ::= { tmnxBundleMemberImaEntry 18 }

tmnxBundleMemberImaNeRxNumFails OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaNeRxNumFails indicates the number
         of times that a near-end receive alarm is set on the
         IMA link."
    ::= { tmnxBundleMemberImaEntry 19 }

tmnxBundleMemberImaFeTxNumFails OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeTxNumFails indicates the number
         of times that a far-end transmit alarm is set on the
         IMA link."
    ::= { tmnxBundleMemberImaEntry 20 }

tmnxBundleMemberImaFeRxNumFails OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaFeRxNumFails indicates the number
         of times that a far-end receive alarm is set on the
         IMA link."
    ::= { tmnxBundleMemberImaEntry 21 }        

tmnxBundleMemberImaTxIcpCells OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaTxIcpCells indicates the number
         of ICP cells that have been transmitted on the IMA
         link."
    ::= { tmnxBundleMemberImaEntry 22 } 
    
tmnxBundleMemberImaRxIcpCells OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaRxIcpCells indicates the number
         of ICP cells that have been received on the IMA
         link."
    ::= { tmnxBundleMemberImaEntry 23 } 

tmnxBundleMemberImaErrorIcpCells OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaErrorIcpCells indicates the number
         of ICP cells with HEC or CRC-10 errors."
    ::= { tmnxBundleMemberImaEntry 24 } 

tmnxBundleMemberImaLstRxIcpCells OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaLstRxIcpCells indicates the number
         of lost ICP cells at the expected offset."
    ::= { tmnxBundleMemberImaEntry 25 } 

tmnxBundleMemberImaOifAnomalies OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaOifAnomalies indicates the number
         of OIF anomalies at the near-end."
    ::= { tmnxBundleMemberImaEntry 26 } 

tmnxBundleMemberImaRxTestState OBJECT-TYPE
    SYNTAX      TmnxImaTestState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaRxTestState indicates the current
         state of the test pattern on this link."
    ::= { tmnxBundleMemberImaEntry 27 } 

tmnxBundleMemberImaRxTestPattern OBJECT-TYPE
    SYNTAX      INTEGER (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaRxTestPattern indicates the received
        test pattern value on this link."
    ::= { tmnxBundleMemberImaEntry 28 } 

tmnxBundleMemberImaRelDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBundleMemberImaRelDelay indicates the latest measured
        delay on this member link relative to the member link with
        the least delay within the same IMA group"
    ::= { tmnxBundleMemberImaEntry 29 }
    
--
--      L4 Load Balancing
--

tmnxPortScalarObjs OBJECT IDENTIFIER ::= { tmnxPortObjs 19 }

tmnxL4LoadBalancing OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxL4LoadBalancing specifies whether layer 4 information,
         src and dst ports, should be used in the LAG and ECMP hashing 
         algorithm. This is the global system setting that all ports will
         inherit. When set to 'true', src and dst ports are used in the hashing
         algorithm. The per port setting of tmnxPortLoadBalanceAlgorithm can 
         override tmnxL4LoadBalancing."
    DEFVAL { false }
    ::= { tmnxPortScalarObjs 1}


--
-- DS1 Port table
--
tmnxDS1PortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxDS1PortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxDS1PortTable has an entry for a DS1 physical port."
    ::= { tmnxPortObjs 23 }

tmnxDS1PortEntry       OBJECT-TYPE
    SYNTAX      TmnxDS1PortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a physical DS1 port on a 
         IOM card in a chassis in the system.  Entries cannot be created 
         and deleted via SNMP SET operations.  Entries in this table will 
         be created automatically when the tmnxMDAAssignedType object is 
         set to the DS1 MDA type The tmnxDS1PortEntry contains attributes
         that are unique to the 'ds1e1' TmnxPortType. The tmnxPortPortID
         contains the slot, mda and port numbers encoded into it.

         For each tmnxDS1PortEntry, there will be a corresponding entry 
         in the tmnxPortTable and the ifTable."
    INDEX       { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxDS1PortTable 1 }

TmnxDS1PortEntry ::=
    SEQUENCE {
        tmnxDS1PortBuildout             INTEGER,
        tmnxDS1PortLastChangeTime       TimeStamp,
        tmnxDS1PortType                 INTEGER,
        tmnxDS1PortLineLength           INTEGER,
        tmnxDS1PortLbo                  INTEGER,
        tmnxDS1PortDbGain               Integer32
    }

tmnxDS1PortBuildout       OBJECT-TYPE
    SYNTAX      INTEGER {
                    short (1),
                    long  (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1PortBuildout configures the cable buildout length.  A ds1 port
         has two settings for the DS1 cable buildout: a 'short' setting, for
         cable lengths of less than or equal to 655 feet, and a 'long' setting
         for cable lengths of greater than 655 feet.
         This object applies to copper-cable-based DS1 ports only."
    DEFVAL      { short }
    ::= { tmnxDS1PortEntry 1 }

tmnxDS1PortLastChangeTime OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxDS1PortLastChangeTime variable contains the sysUpTime
         value of the most recently modified writable variable in the
         tmnxDS1PortEntry row for this port."
    ::= { tmnxDS1PortEntry 2 }

tmnxDS1PortType           OBJECT-TYPE
    SYNTAX      INTEGER {
                    ds1(1),
                    e1 (2),
                    j1 (3)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1PortType configures the type of the physical port to 'ds1',
         'e1' or 'j1'."
    DEFVAL      { ds1 }
    ::= { tmnxDS1PortEntry 3 }

tmnxDS1PortLineLength           OBJECT-TYPE
    SYNTAX      INTEGER {
                    lengthNotApplicable (1),
                    length0To133        (2),
                    length134To266      (3),
                    length267To399      (4),
                    length400To533      (5),
                    length534To655      (6)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1PortLineLength configuration only applies to a ds1 port
         configured with a 'short' buildout.  tmnxDS1PortLineLength configures
         the length of the line (in feet).  For line lengths longer than 655
         feet configure the ds1 port buildout as 'long'.
         For 'long' buildout the following values are valid:
             lengthNotApplicable - Not applicable
         For 'short' buildout the following values are valid:
             length0To133        - For line length from 0 to 133 feet
             length134To266      - For line length from 134 to 266 feet
             length267To399      - For line length from 267 to 399 feet
             length400To533      - For line length from 400 to 533 feet
             length534To655      - For line length from 534 to 655 feet
         The default for 'long' buildout is 'lengthNotApplicable' while the
         default for 'short' buildout is 'length0To133'."
    ::= { tmnxDS1PortEntry 4 }

tmnxDS1PortLbo       OBJECT-TYPE
    SYNTAX      INTEGER {
                    lboNotApplicable (1),
                    lbo0dB           (2),
                    lboNeg7p5dB      (3),
                    lboNeg15p0dB     (4),
                    lboNeg22p5dB     (5)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxDS1PortLbo configuration only applies to a ds1 port
         configured with a 'long' buildout.  tmnxDS1PortLbo configures the
         number of decibels(dB) the transmission signal decreases over the line.
         For 'short' buildout the following values are valid:
             lboNotApplicable - Not applicable
         For 'long' buildout the following values are valid:
             lbo0dB           - For 0 dB
             lboNeg7p5dB      - For -7.5 dB
             lboNeg15p0dB     - For -15.0 dB
             lboNeg22p5dB     - For -22.5 dB
         The default for 'short' build out is 'lboNotApplicable' while the
         default for 'long' buildout is 'lbo0dB'"
    ::= { tmnxDS1PortEntry 5 }
 
tmnxDS1PortDbGain       OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "db"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxDS1PortDbGain only applies to a ds1 port configured with a
         'long' buildout.  tmnxDS1PortDbGain shows the number of decibels
         the received signal is increased to compensate for loss."
    ::= { tmnxDS1PortEntry 6 }

--
--      Port scheduler policy overrides
-- 

tmnxPortSchedOverrideTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxPortSchedOverrideEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxPortSchedOverrideTable has an entry for each port 
         scheduler override configured on this system."
    ::= { tmnxPortObjs 24 }

tmnxPortSchedOverrideEntry    OBJECT-TYPE
    SYNTAX      TmnxPortSchedOverrideEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row represents a particular port-scheduler override. Entries
         are created and deleted by the user."
    INDEX { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxPortSchedOverrideTable 1}

TmnxPortSchedOverrideEntry ::= SEQUENCE
    {
        tmnxPortSchedOverrideRowStatus          RowStatus,
        tmnxPortSchedOverrideSchedName          DisplayString,
        tmnxPortSchedOverrideLastChanged        TimeStamp,
        tmnxPortSchedOverrideMaxRate            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl1PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl1CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideLvl2PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl2CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideLvl3PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl3CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideLvl4PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl4CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideLvl5PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl5CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideLvl6PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl6CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideLvl7PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl7CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideLvl8PIR            TPortSchedulerPIR,
        tmnxPortSchedOverrideLvl8CIR            TPortSchedulerCIR,
        tmnxPortSchedOverrideFlags              BITS
    }

tmnxPortSchedOverrideRowStatus        OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideRowStatus is used for the 
         creation and deletion of port-scheduler overrides. When
         tmnxPortEgrPortSchedPlcy of the tmnxPortEntry indexed by the 
         same indices of this table is an empty string, creation
         will fail."
    ::= { tmnxPortSchedOverrideEntry 1 }

tmnxPortSchedOverrideSchedName        OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideSchedName indicates the name
         of the port scheduler policy that this row entry overrides."
    ::= { tmnxPortSchedOverrideEntry 2 }

tmnxPortSchedOverrideLastChanged        OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLastChanged indicates the value
         of sysUpTime at the time of the most recent management change to 
         this row."
    ::= { tmnxPortSchedOverrideEntry 3 }

tmnxPortSchedOverrideMaxRate        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideMaxRate specifies the explicit
         maximum frame based bandwidth limit. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyMaxRate."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 4 }

tmnxPortSchedOverrideLvl1PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl1PIR specifies the total 
         bandwidth limit, PIR, for priority level 1. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl1PIR."
    DEFVAL      { -1 } 
    ::= { tmnxPortSchedOverrideEntry 5 }

tmnxPortSchedOverrideLvl1CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl1CIR specifies the within-cir
         bandwidth limit for priority level 1. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl1CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 6 }

tmnxPortSchedOverrideLvl2PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl2PIR specifies the total 
         bandwidth limit, PIR, for priority level 2. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl2PIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 7 }

tmnxPortSchedOverrideLvl2CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl2CIR specifies the within-cir
         bandwidth limit for priority level 2. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl2CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 8 }

tmnxPortSchedOverrideLvl3PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl3PIR specifies the total 
         bandwidth limit, PIR, for priority level 3. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl3PIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 9 }

tmnxPortSchedOverrideLvl3CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl3CIR specifies the within-cir
         bandwidth limit for priority level 3. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl3CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 10 }

tmnxPortSchedOverrideLvl4PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl4PIR specifies the total 
         bandwidth limit, PIR, for priority level 4. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl4PIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 11 }

tmnxPortSchedOverrideLvl4CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl4CIR specifies the within-cir
         bandwidth limit for priority level 4. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl4CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 12 }

tmnxPortSchedOverrideLvl5PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl5PIR specifies the total 
         bandwidth limit, PIR, for priority level 5. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl5PIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 13 }

tmnxPortSchedOverrideLvl5CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl5CIR specifies the within-cir
         bandwidth limit for priority level 5. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl5CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 14 }

tmnxPortSchedOverrideLvl6PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl6PIR specifies the total 
         bandwidth limit, PIR, for priority level 6. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl6PIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 15 }

tmnxPortSchedOverrideLvl6CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl6CIR specifies the within-cir
         bandwidth limit for priority level 6. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl6CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 16 }

tmnxPortSchedOverrideLvl7PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl7PIR specifies the total 
         bandwidth limit, PIR, for priority level 7. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl7PIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 17 }

tmnxPortSchedOverrideLvl7CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl7CIR specifies the within-cir
         bandwidth limit for priority level 7. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl7CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 18 }

tmnxPortSchedOverrideLvl8PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl8PIR specifies the total 
         bandwidth limit, PIR, for priority level 8. This object 
         overrides ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl8PIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 19 }

tmnxPortSchedOverrideLvl8CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideLvl8CIR specifies the within-cir
         bandwidth limit for priority level 8. This object overrides
         ALCATEL-IND1-TIMETRA-QOS-MIB::tPortSchedulerPlcyLvl8CIR."
    DEFVAL      { -1 }
    ::= { tmnxPortSchedOverrideEntry 20 }

tmnxPortSchedOverrideFlags        OBJECT-TYPE
    SYNTAX      BITS {
                    maxRate(0),
                    lvl1PIR(1),
                    lvl1CIR(2),
                    lvl2PIR(3),
                    lvl2CIR(4),
                    lvl3PIR(5),
                    lvl3CIR(6),
                    lvl4PIR(7),
                    lvl4CIR(8),
                    lvl5PIR(9),
                    lvl5CIR(10),
                    lvl6PIR(11),
                    lvl6CIR(12),
                    lvl7PIR(13),
                    lvl7CIR(14),
                    lvl8PIR(15),
                    lvl8CIR(16)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxPortSchedOverrideFlags specifies the set of 
         attributes whose values have been overridden via management 
         on this port scheduler. Clearing a given flag will return the 
         corresponding overridden attribute to the value defined in 
         the port's port-scheduler policy."
    ::= { tmnxPortSchedOverrideEntry 21 }

--
-- Bundle Protection Group Association Table
--
tmnxBPGrpAssocTable   OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxBPGrpAssocEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxBPGrpAssocTable has an entry for each Bundle Protection Group created 
         on the system."
    ::= { tmnxPortObjs 25 }

tmnxBPGrpAssocEntry       OBJECT-TYPE
    SYNTAX      TmnxBPGrpAssocEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row displays the relationship between a Bundle Protection 
         group and its working and protection bundle."
    INDEX       { tmnxChassisIndex, tmnxBundleBundleID }
    ::= { tmnxBPGrpAssocTable 1 }

TmnxBPGrpAssocEntry ::=
    SEQUENCE {
        tmnxBPGrpAssocWorkingBundleID  TmnxBundleID,
        tmnxBPGrpAssocProtectBundleID  TmnxBundleID,
        tmnxBPGrpAssocActiveBundleID   TmnxBundleID
    }

tmnxBPGrpAssocWorkingBundleID    OBJECT-TYPE
    SYNTAX      TmnxBundleID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBPGrpAssocWorkingBundleID identifies the working bundle. A value
         of zero is given if no working bundle is associated with the given
         tmnxBundleBundleID."
    ::= { tmnxBPGrpAssocEntry 1 }

tmnxBPGrpAssocProtectBundleID    OBJECT-TYPE
    SYNTAX      TmnxBundleID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBPGrpAssocProtectBundleID identifies the protection bundle. A value
         of zero is given if no protection bundle is associated with the given 
         tmnxBundleBundleID."
    ::= { tmnxBPGrpAssocEntry 2 }

tmnxBPGrpAssocActiveBundleID    OBJECT-TYPE
    SYNTAX      TmnxBundleID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxBPGrpAssocActiveBundleID identifies the bundle that is considered
         the active bundle. This bundle contains the members that are part of the
         active aps port. A value of zero is given if no bundle is considered active." 
    ::= { tmnxBPGrpAssocEntry 3 }

--
-- MLPPP Bundle Table
--
--  Sparse Dependent Extension of the tmnxBundleTable.
--
--  The same indexes are used for both the base table tmnxBundleTable, 
--  and the sparse dependent table, tmnxBundleMlpppTable. 
--
--  This in effect extends the tmnxBundleTable with additional columns.
--  Rows are created in the tmnxBundleMlpppTable only for those entries 
--  in the tmnxBundleTable for a value of 'mlppp' for the bundle type.  
--  
--  Deletion of a row in the tmnxBundleTable results in the 
--  same fate for the row in the tmnxBundleMlpppTable.
--
tmnxBundleMlpppTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxBundleMlpppEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxBundleMlpppTable contains MLPPP specific data."
    ::= { tmnxPortObjs 26 }

tmnxBundleMlpppEntry OBJECT-TYPE
    SYNTAX      TmnxBundleMlpppEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry contains data on an MLPPP Bundle."
    INDEX       { tmnxChassisIndex, tmnxBundleBundleID }
    ::= { tmnxBundleMlpppTable 1 }

TmnxBundleMlpppEntry ::=
    SEQUENCE {
        tmnxBundleMlpppEndpointID         OCTET STRING,
        tmnxBundleMlpppEndpointIDClass    TmnxMlpppEndpointIdClass,
        tmnxBundleMlpppClassCount         INTEGER,
        tmnxBundleMlpppIngQoSProfId       TMlpppQoSProfileId,
        tmnxBundleMlpppEgrQoSProfId       TMlpppQoSProfileId
    }
 
tmnxBundleMlpppEndpointID OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (0..20))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleMlpppEndpointID indentifies the Endpoint Discriminator
         identifier value within the specified tmnxBundleMlpppEndpointIDClass.
         The object tmnxPortAdminStatus must be set to 'outOfService' to 
         change this value."
    ::= { tmnxBundleMlpppEntry 1 }

tmnxBundleMlpppEndpointIDClass OBJECT-TYPE
    SYNTAX      TmnxMlpppEndpointIdClass
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleMlpppEndpointIDClass specifies the Link Control
         Protocol Endpoint Descriminator Class field type of the 
         tmnxBundleMlpppEndpointID. The object tmnxPortAdminStatus 
         must be set to 'outOfService' to change this value.
         
                    Bundle                     DEFVAL
            Physical MLPPP Bundle           ieee802dot1GlobalMacAddress (3)
            MLPPP Bundle Protection Group   ipAddress (2)
         "
    ::= { tmnxBundleMlpppEntry 2 }

tmnxBundleMlpppClassCount OBJECT-TYPE
    SYNTAX      INTEGER (0..16)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxBundleMlpppClassCount specifies
         whether Multiclass MLPPP is enabled on
         a bundle with a tmnxBundleType of 'mlppp' and when enabled what is the
         number of classes to be negotiated/supported over the MLPPP bundle. 
        
         The value of 0 disables multiclass MLPPP including negotiation in
         the PPP protocol.
         All other values specify the number of classes to be supported
         on a given multiclass bundle and enable Multiclass MLPPP negotiation 
         on that bundle.
        
         Non-zero values supported are platform/MDA specific.
        
         The value of tmnxBundleMlpppClassCount must be set to 0 and cannot be 
         changed when LFI is enabled on the bundle (see tmnxBundleLFI object 
         in tmnxBundleTable).
        
         The value of tmnxBundleMlpppClassCount may be modified only when 
         tmnxBundleNumLinks is zero.
    
         To set the value of tmnxBundleMlpppClassCount to a value greater
         than 4, the long sequence format must be enabled on the bundle (see 
         tmnxBundleShortSequence object in tmnxBundleTable).
         
         Changing the value of tmnxBundleMlpppClassCount resets the values of
         tmnxBundleMlpppIngQoSProfId and tmnxBundleMlpppEgrQoSProfId to their
         defaults"
    DEFVAL { 0 }
    
    ::= { tmnxBundleMlpppEntry 3 }   

tmnxBundleMlpppIngQoSProfId OBJECT-TYPE
    SYNTAX      TMlpppQoSProfileId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleMlpppIngQoSProfId specifies ingress QoS profile
         to be used for the incoming traffic over this MLPPP
         bundle.
         
         The value of tmnxBundleMlpppIngQoSProfId may be modified only 
         when the value of tmnxBundleNumLinks is 0.

         The value of tmnxBundleMlpppIngQoSProfId of 0 indicates
         a default QoS profile is used as applicable to a given H/W and
         the configured value of tmnxBundleMlpppClassCount."
    DEFVAL { 0 }
 
    ::= { tmnxBundleMlpppEntry 4 }   
    
tmnxBundleMlpppEgrQoSProfId OBJECT-TYPE
    SYNTAX      TMlpppQoSProfileId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxBundleMlpppEgrQoSProfId specifies egress QoS profile
         to be used for the outgoing traffic over this MLPPP
         bundle.
         
         The value of tmnxBundleMlpppEgrQoSProfId may be modified only 
         when tmnxBundleNumLinks is zero.

         The value of tmnxBundleMlpppEgrQoSProfId of 0 indicates
         a default profile is used as applicable to a given H/W and
         the configured value of tmnxBundleMlpppClassCount."
    DEFVAL { 0 }
    
    ::= { tmnxBundleMlpppEntry 5 } 

--
--  Digital Diagnostic Monitor Table
--
tmnxDigitalDiagMonitorTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxDigitalDiagMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxDigitalDiagMonitorTable has an entry for each SFP/XFP in the
         system that supports Digital Diagnostic Monitoring (DDM). The table is
         indexed by TmnxPortID. Each row in this table is dynamically added
         and removed internally by the system based on the presence or absence
         of DDM capable SFP/XFP components."
    ::= { tmnxPortObjs 31 }

tmnxDigitalDiagMonitorEntry OBJECT-TYPE
    SYNTAX      TmnxDigitalDiagMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row represents a particular SFP/XFP that supports Digital
         Diagnostic Monitoring.
         Entries are created and deleted internally by the system."
    INDEX { tmnxChassisIndex, tmnxPortPortID }
    ::= { tmnxDigitalDiagMonitorTable 1}

TmnxDigitalDiagMonitorEntry ::= SEQUENCE
    {
        tmnxDDMTemperature                   Integer32,
        tmnxDDMTempLowWarning                Integer32,
        tmnxDDMTempLowAlarm                  Integer32,
        tmnxDDMTempHiWarning                 Integer32,
        tmnxDDMTempHiAlarm                   Integer32,
        tmnxDDMSupplyVoltage                 Integer32,
        tmnxDDMSupplyVoltageLowWarning       Integer32,
        tmnxDDMSupplyVoltageLowAlarm         Integer32,
        tmnxDDMSupplyVoltageHiWarning        Integer32,
        tmnxDDMSupplyVoltageHiAlarm          Integer32,
        tmnxDDMTxBiasCurrent                 Integer32,
        tmnxDDMTxBiasCurrentLowWarning       Integer32,
        tmnxDDMTxBiasCurrentLowAlarm         Integer32,
        tmnxDDMTxBiasCurrentHiWarning        Integer32,
        tmnxDDMTxBiasCurrentHiAlarm          Integer32,
        tmnxDDMTxOutputPower                 Integer32,
        tmnxDDMTxOutputPowerLowWarning       Integer32,
        tmnxDDMTxOutputPowerLowAlarm         Integer32,
        tmnxDDMTxOutputPowerHiWarning        Integer32,
        tmnxDDMTxOutputPowerHiAlarm          Integer32,
        tmnxDDMRxOpticalPower                Integer32,
        tmnxDDMRxOpticalPowerLowWarning      Integer32,
        tmnxDDMRxOpticalPowerLowAlarm        Integer32,
        tmnxDDMRxOpticalPowerHiWarning       Integer32,
        tmnxDDMRxOpticalPowerHiAlarm         Integer32,
        tmnxDDMRxOpticalPowerType            INTEGER,
        tmnxDDMAux1                          Integer32,
        tmnxDDMAux1LowWarning                Integer32,
        tmnxDDMAux1LowAlarm                  Integer32,
        tmnxDDMAux1HiWarning                 Integer32,
        tmnxDDMAux1HiAlarm                   Integer32,
        tmnxDDMAux1Type                      INTEGER,
        tmnxDDMAux2                          Integer32,
        tmnxDDMAux2LowWarning                Integer32,
        tmnxDDMAux2LowAlarm                  Integer32,
        tmnxDDMAux2HiWarning                 Integer32,
        tmnxDDMAux2HiAlarm                   Integer32,
        tmnxDDMAux2Type                      INTEGER,
        tmnxDDMFailedThresholds              BITS,
        tmnxDDMExternallyCalibrated          TruthValue,
        tmnxDDMExtCalRxPower4                Unsigned32,
        tmnxDDMExtCalRxPower3                Unsigned32,
        tmnxDDMExtCalRxPower2                Unsigned32,
        tmnxDDMExtCalRxPower1                Unsigned32,
        tmnxDDMExtCalRxPower0                Unsigned32,
        tmnxDDMExtCalTxLaserBiasSlope        Unsigned32,
        tmnxDDMExtCalTxLaserBiasOffset       Integer32,
        tmnxDDMExtCalTxPowerSlope            Unsigned32,
        tmnxDDMExtCalTxPowerOffset           Integer32,
        tmnxDDMExtCalTemperatureSlope        Unsigned32,
        tmnxDDMExtCalTemperatureOffset       Integer32,
        tmnxDDMExtCalVoltageSlope            Unsigned32,
        tmnxDDMExtCalVoltageOffset           Integer32
    }

tmnxDDMTemperature                 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTemperature indicates the current temperature
         of the SFP/XFP in degrees Celsius."
    ::= { tmnxDigitalDiagMonitorEntry 1 }
    
tmnxDDMTempLowWarning              OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTempLowWarning indicates the temperature
         of the SFP/XFP in degrees Celsius that triggers a low-warning."
    ::= { tmnxDigitalDiagMonitorEntry 2 }

tmnxDDMTempLowAlarm                OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTempLowAlarm indicates the temperature
         of the SFP/XFP in degrees Celsius that triggers a low-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 3 }

tmnxDDMTempHiWarning               OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTempHiWarning indicates the temperature
         of the SFP/XFP in degrees Celsius that triggers a hi-warning."
    ::= { tmnxDigitalDiagMonitorEntry 4 }

tmnxDDMTempHiAlarm                 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTempHiAlarm indicates the temperature
         of the SFP/XFP in degrees Celsius that triggers a hi-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 5 }

tmnxDDMSupplyVoltage               OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMSupplyVoltage indicates the current supply
         voltage of the SFP/XFP in micro-Volts (uV)."
    ::= { tmnxDigitalDiagMonitorEntry 6 }

tmnxDDMSupplyVoltageLowWarning     OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMSupplyVoltageLowWarning indicates the supply
         voltage of the SFP/XFP in micro-Volts (uV) that triggers a low-warning."
    ::= { tmnxDigitalDiagMonitorEntry 7 }

tmnxDDMSupplyVoltageLowAlarm       OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMSupplyVoltageLowAlarm indicates the supply
         voltage of the SFP/XFP in micro-Volts (uV) that triggers a low-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 8 }

tmnxDDMSupplyVoltageHiWarning      OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMSupplyVoltageHiWarning indicates the supply
         voltage of the SFP/XFP in micro-Volts (uV) that triggers a hi-warning."
    ::= { tmnxDigitalDiagMonitorEntry 9 }

tmnxDDMSupplyVoltageHiAlarm        OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMSupplyVoltageHiAlarm indicates the supply
         voltage of the SFP/XFP in micro-Volts (uV) that triggers a hi-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 10 }

tmnxDDMTxBiasCurrent               OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxBiasCurrent indicates the current Transmit
         Bias Current of the SFP/XFP in micro-Amperes (uA)."
    ::= { tmnxDigitalDiagMonitorEntry 11 }

tmnxDDMTxBiasCurrentLowWarning     OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxBiasCurrentLowWarning indicates the Transmit
         Bias Current of the SFP/XFP in micro-Amperes (uA) that triggers a
         low-warning."
    ::= { tmnxDigitalDiagMonitorEntry 12 }

tmnxDDMTxBiasCurrentLowAlarm       OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxBiasCurrentLowAlarm indicates the Transmit
         Bias Current of the SFP/XFP in micro-Amperes (uA) that triggers a
         low-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 13 }

tmnxDDMTxBiasCurrentHiWarning      OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxBiasCurrentHiWarning indicates the Transmit
         Bias Current of the SFP/XFP in micro-Amperes (uA) that triggers a
         hi-warning."
    ::= { tmnxDigitalDiagMonitorEntry 14 }

tmnxDDMTxBiasCurrentHiAlarm        OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxBiasCurrentHiAlarm indicates the Transmit
         Bias Current of the SFP/XFP in micro-Amperes (uA) that triggers a
         hi-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 15 }

tmnxDDMTxOutputPower               OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxOutputPower indicates the current Output
         Power of the SFP/XFP in micro-Watts (uW)."
    ::= { tmnxDigitalDiagMonitorEntry 16 }

tmnxDDMTxOutputPowerLowWarning     OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxOutputPowerLowWarning indicates the Output Power
         of the SFP/XFP in micro-Watts (uW) that triggers a low-warning."
    ::= { tmnxDigitalDiagMonitorEntry 17 }

tmnxDDMTxOutputPowerLowAlarm       OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxOutputPowerLowAlarm indicates the Output Power
         of the SFP/XFP in micro-Watts (uW) that triggers a low-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 18 }

tmnxDDMTxOutputPowerHiWarning      OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxOutputPowerHiWarning indicates the Output Power
         of the SFP/XFP in micro-Watts (uW) that triggers a hi-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 19 }

tmnxDDMTxOutputPowerHiAlarm        OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMTxOutputPowerHiAlarm indicates the Output Power
         of the SFP/XFP in micro-Watts (uW) that triggers a hi-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 20 }

tmnxDDMRxOpticalPower              OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMRxOpticalPower indicates the current Received
         Optical Power of the SFP/XFP in micro-Watts (uW)."
    ::= { tmnxDigitalDiagMonitorEntry 21 }

tmnxDDMRxOpticalPowerLowWarning    OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMRxOpticalPowerLowWarning indicates the Received
         Optical Power of the SFP/XFP in micro-Watts (uW) that triggers a
         low-warning."
    ::= { tmnxDigitalDiagMonitorEntry 22 }

tmnxDDMRxOpticalPowerLowAlarm      OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMRxOpticalPowerLowAlarm indicates the Received
         Optical Power of the SFP/XFP in micro-Watts (uW) that triggers a
         low-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 23 }

tmnxDDMRxOpticalPowerHiWarning     OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMRxOpticalPowerHiWarning indicates the Received
         Optical Power of the SFP/XFP in micro-Watts (uW) that triggers a
         hi-warning."
    ::= { tmnxDigitalDiagMonitorEntry 24 }

tmnxDDMRxOpticalPowerHiAlarm       OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMRxOpticalPowerHiAlarm indicates the Received
         Optical Power of the SFP/XFP in micro-Watts (uW) that triggers a
         hi-alarm."
    ::= { tmnxDigitalDiagMonitorEntry 25 }

tmnxDDMRxOpticalPowerType          OBJECT-TYPE
    SYNTAX      INTEGER {
                    oma      (0),   -- Optical Modulation Amplitude
                    average  (1)    -- Average rx optical power
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMRxOpticalPowerType indicates whether the
         tmnxDDMRxOpticalPower was taken as an average, or as an Optical
         Modulation Amplitude (OMA)."
    ::= { tmnxDigitalDiagMonitorEntry 26 }

tmnxDDMAux1                        OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux1 indicates the Manufacturer specific
         Auxiliary 1 information of the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 27 }

tmnxDDMAux1LowWarning              OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux1LowWarning indicates the Manufacturer specific
         Auxiliary 1 low-warning threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 28 }

tmnxDDMAux1LowAlarm                OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux1LowAlarm indicates the Manufacturer specific
         Auxiliary 1 low-alarm threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 29 }

tmnxDDMAux1HiWarning               OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux1HiWarning indicates the Manufacturer specific
         Auxiliary 1 hi-warning threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 30 }

tmnxDDMAux1HiAlarm                 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux1HiAlarm indicates the Manufacturer specific
         Auxiliary 1 hi-alarm threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 31 }

tmnxDDMAux1Type                    OBJECT-TYPE
    SYNTAX      INTEGER {
                    none             (0),
                    adp-bias-voltage (1),
                    reserved-2       (2),
                    tec-current      (3),
                    laser-temp       (4),
                    laser-wavelength (5),
                    voltage-50       (6),
                    voltage-33       (7),
                    voltage-18       (8),
                    voltage-52       (9),
                    current-50       (10),
                    reserved-11      (11),
                    reserved-12      (12),
                    current-33       (13),
                    current-18       (14),
                    current-52       (15)
                } 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux1Type indicates the measurement type of
         the Manufacturer specific Auxiliary 1 information of the XFP:
            none             (0)  - not specified.
            adp-bias-voltage (1)  - LSB is 10mV.
            reserved-2       (2)  - reserved for future use.
            tec-current      (3)  - LSB is 100uA.
            laser-temp       (4)  - degrees Celsius(C).
            laser-wavelength (5)  - LSB is 0.05 nm.
            voltage-50       (6)  - 5.0 supply voltage, LSB=100uV.
            voltage-33       (7)  - 3.3 supply voltage, LSB=100uV.
            voltage-18       (8)  - 1.8 supply voltage, LSB=100uV,
            voltage-52       (9)  - -5.2 supply voltage, LSB=100uV.
            current-50      (10)  - 5.0V supply current, LSB=100uA.
            reserved-11     (11)  - reserved for future use.
            reserved-12     (12)  - reserved for future use.
            current-33      (13)  - 3.3V supply current, LSB=100uA.
            current-18      (14)  - 1.8V supply current, LSB=100uA.
            current-52      (15)  - -5.2V supply current, LSB=100uA."
    ::= { tmnxDigitalDiagMonitorEntry 32 }

tmnxDDMAux2                        OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux2 indicates the Manufacturer specific
         Auxiliary 2 information of the SFP/XFP."
    ::= { tmnxDigitalDiagMonitorEntry 33 }

tmnxDDMAux2LowWarning              OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux2LowWarning indicates the Manufacturer specific
         Auxiliary 2 low-warning threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 34 }

tmnxDDMAux2LowAlarm                OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux2LowAlarm indicates the Manufacturer specific
         Auxiliary 2 low-alarm threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 35 }

tmnxDDMAux2HiWarning               OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux2HiWarning indicates the Manufacturer specific
         Auxiliary 2 hi-warning threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 36 }

tmnxDDMAux2HiAlarm                 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux2HiAlarm indicates the Manufacturer specific
         Auxiliary 2 hi-alarm threshold for the XFP."
    ::= { tmnxDigitalDiagMonitorEntry 37 }

tmnxDDMAux2Type                    OBJECT-TYPE
    SYNTAX      INTEGER {
                    none             (0),
                    adp-bias-voltage (1),
                    reserved-2       (2),
                    tec-current      (3),
                    laser-temp       (4),
                    laser-wavelength (5),
                    voltage-50       (6),
                    voltage-33       (7),
                    voltage-18       (8),
                    voltage-52       (9),
                    current-50       (10),
                    reserved-11      (11),
                    reserved-12      (12),
                    current-33       (13),
                    current-18       (14),
                    current-52       (15)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMAux2Type indicates the measurement type of
         the Manufacturer specific Auxiliary 2 information of the XFP:
            none             (0)  - not specified.
            adp-bias-voltage (1)  - LSB is 10mV.
            reserved-2       (2)  - reserved for future use.
            tec-current      (3)  - LSB is 100uA.
            laser-temp       (4)  - degrees Celsius(C).
            laser-wavelength (5)  - LSB is 0.05 nm.
            voltage-50       (6)  - 5.0 supply voltage, LSB=100uV.
            voltage-33       (7)  - 3.3 supply voltage, LSB=100uV.
            voltage-18       (8)  - 1.8 supply voltage, LSB=100uV,
            voltage-52       (9)  - -5.2 supply voltage, LSB=100uV.
            current-50      (10)  - 5.0V supply current, LSB=100uA.
            reserved-11     (11)  - reserved for future use.
            reserved-12     (12)  - reserved for future use.
            current-33      (13)  - 3.3V supply current, LSB=100uA.
            current-18      (14)  - 1.8V supply current, LSB=100uA.
            current-52      (15)  - -5.2V supply current, LSB=100uA."
    ::= { tmnxDigitalDiagMonitorEntry 38 }

tmnxDDMFailedThresholds            OBJECT-TYPE
    SYNTAX      BITS {
                    unknown                      (0),
                    temperature-low-warning      (1),
                    temperature-low-alarm        (2),
                    temperature-high-warning     (3),
                    temperature-high-alarm       (4),
                    supplyVoltage-low-warning    (5),
                    supplyVoltage-low-alarm      (6),
                    supplyVoltage-high-warning   (7),
                    supplyVoltage-high-alarm     (8),
                    txBiasCurrent-low-warning    (9),
                    txBiasCurrent-low-alarm     (10),
                    txBiasCurrent-high-warning  (11),
                    txBiasCurrent-high-alarm    (12),
                    txOutputPower-low-warning   (13),
                    txOutputPower-low-alarm     (14),
                    txOutputPower-high-warning  (15),
                    txOutputPower-high-alarm    (16),
                    rxOpticalPower-low-warning  (17),
                    rxOpticalPower-low-alarm    (18),
                    rxOpticalPower-high-warning (19),
                    rxOpticalPower-high-alarm   (20),
                    aux1-low-warning            (21),
                    aux1-low-alarm              (22),
                    aux1-high-warning           (23),
                    aux1-high-alarm             (24),
                    aux2-low-warning            (25),
                    aux2-low-alarm              (26),
                    aux2-high-warning           (27),
                    aux2-high-alarm             (28)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMFailedThresholds indicates which objects
        of the monitored SFP/XFP is in a failed or cleared threshold state.
        A set bit indicates that the specified threshold has been exceeded."
    ::= { tmnxDigitalDiagMonitorEntry 39 }

tmnxDDMExternallyCalibrated        OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExternallyCalibrated indicates whether the
         SFP was externally calibrated (true) or internally calibrated (false)."
    ::= { tmnxDigitalDiagMonitorEntry 40 }

tmnxDDMExtCalRxPower4              OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalRxPower4 indicates the Rx_PWR(4)
         value as specified in Table 3.16 in the SFF Committee Standard's
         document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 41 }

tmnxDDMExtCalRxPower3              OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalRxPower3 indicates the Rx_PWR(3)
         value as specified in Table 3.16 in the SFF Committee Standard's
         document SFF-8462 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 42 }

tmnxDDMExtCalRxPower2              OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalRxPower2 indicates the Rx_PWR(2)
         value as specified in Table 3.16 in the SFF Committee Standard's
         document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 43 }

tmnxDDMExtCalRxPower1              OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalRxPower1 indicates the Rx_PWR(1)
         value as specified in Table 3.16 in the SFF Committee Standard's
         document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 44 }

tmnxDDMExtCalRxPower0              OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalRxPower0 indicates the Rx_PWR(0)
         value as specified in Table 3.16 in the SFF Committee Standard's
         document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 45 }

tmnxDDMExtCalTxLaserBiasSlope      OBJECT-TYPE
    SYNTAX      Unsigned32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalTxLaserBiasSlope indicates the
         Tx_l(Slope) value as specified in Table 3.16 in the SFF
         Committee Standard's document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 46 }

tmnxDDMExtCalTxLaserBiasOffset     OBJECT-TYPE
    SYNTAX      Integer32 (-32767..32768)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalTxLaserBiasOffset indicates the
         Tx_l(Offset) value as specified in Table 3.16 in the SFF
         Committee Standard's document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 47 }

tmnxDDMExtCalTxPowerSlope          OBJECT-TYPE
    SYNTAX      Unsigned32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalTxPowerSlope indicates the
         Tx_PWR(Slope) value as specified in Table 3.16 in the
         SFF Committee Standard's document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 48 }

tmnxDDMExtCalTxPowerOffset         OBJECT-TYPE
    SYNTAX      Integer32 (-32767..32768)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalTxPowerOffset indicates the
         Tx_PWR(Offset) value as specified in Table 3.16 in the
         SFF Committee Standard's document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 49 }

tmnxDDMExtCalTemperatureSlope      OBJECT-TYPE
    SYNTAX      Unsigned32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalTemperatureSlope indicates the
         T(Slope) value as specified in Table 3.16 in the SFF
         Committee Standard's document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 50 }

tmnxDDMExtCalTemperatureOffset     OBJECT-TYPE
    SYNTAX      Integer32 (-32767..32768)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalTemperatureOffset indicates the
         T(Offset) value as specified in Table 3.16 in the SFF
         Committee Standard's document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 51 }

tmnxDDMExtCalVoltageSlope          OBJECT-TYPE
    SYNTAX      Unsigned32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalVoltageSlope indicates the V(Slope)
        value as specified in Table 3.16 in the SFF Committee Standard's
        document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 52 }

tmnxDDMExtCalVoltageOffset         OBJECT-TYPE
    SYNTAX      Integer32 (-32767..32768)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxDDMExtCalVoltageOffset indicates the V(Slope)
        value as specified in Table 3.16 in the SFF Committee Standard's
        document SFF-8472 Rev 10.2."
    REFERENCE "SFF-8472 Rev 10.2"
    ::= { tmnxDigitalDiagMonitorEntry 53 }

--
--
--  Port Notification Objects
--
 tmnxPortNotifyPortId  OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnx port Notifications, the OID indicates the
         port or sonet path associated with the alarm."
    ::= { tmnxPortNotificationObjects 1 }

 tmnxPortNotifySonetAlarmReason  OBJECT-TYPE
    SYNTAX      INTEGER {
                    notUsed (0),
                    loc(1),
                    lais(2),
                    lrdi(3),
                    ss1f(4),
                    sb1err(5),
                    lb2erSd(6),
                    lb2erSf(7),
                    slof(8),
                    slos(9),
                    stxptr(10),
                    srxptr(11),
                    lrei(12)
                }
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "Used by tmnx sonet Port Notifications, the value of
         tmnxPortNotifySonetAlarmReason indicates the reason a sonet port
         alarm has been raised."
    ::= { tmnxPortNotificationObjects 2 }

 tmnxPortNotifySonetPathAlarmReason  OBJECT-TYPE
    SYNTAX      INTEGER {
                    notUsed (0),
                    pais(1),
                    plop(2),
                    prdi(3),
                    pb3err(4),
                    pplm(5),
                    prei(6),
                    puneq(7),
                    plcd(8)
                }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnx sonet path Notifications, the value of
         tmnxPortNotifySonetPathAlarmReason indicates the reason a sonet path
         alarm has been raised."
    ::= { tmnxPortNotificationObjects 3 }

tmnxPortNotifyError  OBJECT-TYPE
    SYNTAX      INTEGER {
                    txClockError (1),
                    rxClockError(2),
                    txFifoError(3),
                    laserError(4),
                    miscError(5)
                }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnxEqPortError notification, the value of 
         tmnxPortNotifyError indicates the reason a port has
         an error."
    ::= { tmnxPortNotificationObjects 4 }

 tmnxPortNotifyDS3AlarmReason  OBJECT-TYPE
    SYNTAX      INTEGER {
                    notUsed (0),
                    ais (1),
                    los (2),
                    oof (3),
                    rai (4),
                    looped (5)
                }
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "Used by tmnx DS3 Port Notifications, the value of
         tmnxPortNotifyDS3AlarmReason indicates the reason a DS3 interface
         alarm has been raised."
    ::= { tmnxPortNotificationObjects 5 }

 tmnxPortNotifyDS1AlarmReason  OBJECT-TYPE
    SYNTAX      INTEGER {
                    notUsed (0),
                    ais (1),
                    los (2),
                    oof (3),
                    rai (4),
                    looped (5),
                    berSd (6),
                    berSf (7)
                }
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "Used by tmnx DS1 Port Notifications, the value of
         tmnxPortNotifyDS1AlarmReason indicates the reason a DS1 interface
         alarm has been raised."
    ::= { tmnxPortNotificationObjects 6 }

 tmnxPortNotifyBundleId  OBJECT-TYPE
    SYNTAX      TmnxBundleID
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnx bundle Notifications, the value indicates the
         bundle associated with the alarm."
    ::= { tmnxPortNotificationObjects 7 }

 tmnxPortNotifyEtherAlarmReason  OBJECT-TYPE
    SYNTAX       TmnxPortEtherReportValue
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "Used by tmnx ethernet Port Notifications, the value of
         tmnxPortNotifyEtherAlarmReason indicates the reason a ethernet port
         alarm has been raised."
    ::= { tmnxPortNotificationObjects 8 }

 tmnxDDMFailedObject OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown                      (0),
                    temperature-low-warning      (1),
                    temperature-low-alarm        (2),
                    temperature-high-warning     (3),
                    temperature-high-alarm       (4),
                    supplyVoltage-low-warning    (5),
                    supplyVoltage-low-alarm      (6),
                    supplyVoltage-high-warning   (7),
                    supplyVoltage-high-alarm     (8),
                    txBiasCurrent-low-warning    (9),
                    txBiasCurrent-low-alarm     (10),
                    txBiasCurrent-high-warning  (11),
                    txBiasCurrent-high-alarm    (12),
                    txOutputPower-low-warning   (13),
                    txOutputPower-low-alarm     (14),
                    txOutputPower-high-warning  (15),
                    txOutputPower-high-alarm    (16),
                    rxOpticalPower-low-warning  (17),
                    rxOpticalPower-low-alarm    (18),
                    rxOpticalPower-high-warning (19),
                    rxOpticalPower-high-alarm   (20),
                    aux1-low-warning            (21),
                    aux1-low-alarm              (22),
                    aux1-high-warning           (23),
                    aux1-high-alarm             (24),
                    aux2-low-warning            (25),
                    aux2-low-alarm              (26),
                    aux2-high-warning           (27),
                    aux2-high-alarm             (28)
                }
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "Used by Digital Diagnostic Monitoring (DDM) Notifications, the
         value of tmnxDDMFailedObject indicates which object of the
         monitored SFP/XFP is in a failed or cleared threshold state."
    ::= { tmnxPortNotificationObjects 9 }

-- 
--  ALCATEL-IND1-TIMETRA-PORT-MIB Notifications
--

--
--  Equipment Alarms
--
 tmnxEqOobPortFailure   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyChassisId,
        tmnxPortNotifyPortId
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when the out-of-band Ethernet port has failed.  On the
         Alcatel 7x50 SR system, the out-of-band port is the CPM management 
         interface.
         
         This notification was made obsolete in the 2.1 release.
                    
         The Out-of-band, OOB, port is what id refered to as the 
         management port or cpm port.  It has an entry in ifTable 
         and linkup, linkDown and tmnxEqPortError notifications 
         are sent for the management port just as for any other 
         port in the system."
    ::= { tmnxPortNotification 1 }

 tmnxEqPortFailure  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyChassisId,
        tmnxPortNotifyPortId
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a port has failed.
         
         This notification was made obsolete in the 2.1 release.

         tmnxEqPortError is used instead of tmnxEqPortFailure.
                     
         Standard IETF linkDown notification is sent when a 
         physical port failure is detected.  tmnxEqPortError is 
         sent with a ASCII reason message when port errors are 
         detected.

         It was originally intended that tmnxEqPortError be used 
         to report errors that do not cause port state change to 
         'down'.

         tmnxEqPortFailure was intended to report errors that 
         do cause the port state change to 'down'.

         However, in the implementation tmnxEqPortError is used 
         for both fatal and non-fatal errors."
    ::= { tmnxPortNotification 2 }

 tmnxEqPortSonetAlarm     NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifySonetAlarmReason    
    }
    STATUS  current
    DESCRIPTION
        "Generated when a SONET/SDH port alarm condition is detected.
         It is generated only when the type of alarm being raised is enabled
         in tmnxSonetReportAlarm."
    ::= { tmnxPortNotification 4 }
                 
 tmnxEqPortSonetAlarmClear        NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifySonetAlarmReason    
    }
    STATUS  current
    DESCRIPTION
        "Generated when a SONET/SDH port alarm condition is cleared.
         It is generated only when the type of alarm being cleared is enabled
         in tmnxSonetReportAlarm."
    ::= { tmnxPortNotification 5 }
                 
 tmnxEqPortSonetPathAlarm   NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifySonetPathAlarmReason  
    }
    STATUS  current
    DESCRIPTION
        "Generated when a SONET/SDH path alarm condition is detected.
         It is generated only when the type of alarm being raised is enabled
         in tmnxSonetPathReportAlarm."
    ::= { tmnxPortNotification 6 }
                 
 tmnxEqPortSonetPathAlarmClear      NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifySonetPathAlarmReason  
    }
    STATUS  current
    DESCRIPTION
        "Generated when a SONET/SDH path alarm condition is cleared.
         It is generated only when the type of alarm being cleared is enabled
         in tmnxSonetPathReportAlarm."
    ::= { tmnxPortNotification 7 }

 tmnxEqPortSFPInserted NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId
    }
    STATUS  current
    DESCRIPTION
        "Generated when a SFP is inserted in the port."
    ::= { tmnxPortNotification 8 }

 tmnxEqPortSFPRemoved NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId
    }
    STATUS  current
    DESCRIPTION
        "Generated when a SFP is removed from the port."
    ::= { tmnxPortNotification 9 }

 tmnxEqPortWrongSFP NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a wrong type of SFP optics module 
         is plugged into a 1 Gig MDA, SONET MDA, or 100FX MDA.

         Because of the large variety of SFPs now supported and 
         their different encoding schemes for compliance bits, the
         check for wrong SFP has been removed. As a result,                     
         tmnxEqPortWrongSFP notification is no longer generated.
 
         However, wrong SFP can be inferred from port statistics."
    ::= { tmnxPortNotification 10 }

tmnxEqPortSFPCorrupted  NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId
    }
    STATUS  obsolete 
    DESCRIPTION
        "Generated when SFP information stored in the EPROM 
         on the SFP is corrupt. This notification was made
         obsolete for revision 6.0 on Alcatel 7x50 SR series
         systems; it was replaced by tmnxEqPortSFPStatusFailure."
    ::= { tmnxPortNotification 11 }

 tmnxPortNotifyBerSdTca NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxSonetBerSdThreshold
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a SONET/SDH port's signal degradation bit error
         rate is greater than the configured error rate threshold.
         
         This notification was made obsolete in the 2.1 release."
        ::= { tmnxPortNotification 12 }

 tmnxPortNotifyBerSfTca NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxSonetBerSfThreshold
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a SONET/SDH port's signal failure bit error
         rate is greater than the configured error rate threshold.
         
         This notification was made obsolete in the 2.1 release."
    ::= { tmnxPortNotification 13 }

tmnxEqPortError NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifyError
    }
    STATUS  current
    DESCRIPTION
        "Generated when an error listed in tmnxPortNotifyError 
         is detected on the port."
    ::= { tmnxPortNotification 14 }

 tmnxEqPortDS3Alarm   NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifyDS3AlarmReason  
    }
    STATUS  current
    DESCRIPTION
        "Generated when a DS3 interface alarm condition is detected.
         It is generated only when the type of alarm being raised is enabled
         in tmnxDS3ChannelReportAlarm."
    ::= { tmnxPortNotification 15 }
                 
 tmnxEqPortDS3AlarmClear      NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifyDS3AlarmReason  
    }
    STATUS  current
    DESCRIPTION
        "Generated when a DS3 interface alarm condition is cleared.
         It is generated only when the type of alarm being cleared is enabled
         in tmnxDS3ChannelReportAlarm."
    ::= { tmnxPortNotification 16 }

 tmnxEqPortDS1Alarm   NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifyDS1AlarmReason  
    }
    STATUS  current
    DESCRIPTION
        "Generated when a DS1 interface alarm condition is detected.
         It is generated only when the type of alarm being raised is enabled
         in tmnxDS1ReportAlarm."
    ::= { tmnxPortNotification 17 }
                 
 tmnxEqPortDS1AlarmClear      NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifyDS1AlarmReason  
    }
    STATUS  current
    DESCRIPTION
        "Generated when a DS1 interface alarm condition is cleared.
         It is generated only when the type of alarm being cleared is enabled
         in tmnxDS1ReportAlarm."
    ::= { tmnxPortNotification 18 }

 tmnxEqPortBndlYellowDiffExceeded   NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxBundleYellowDiffDelay
    }
    STATUS  current
    DESCRIPTION
        "Generated when the differential delay of a port in the bundle 
         exceeds the configured value in tmnxBundleYellowDiffDelay."
    ::= { tmnxPortNotification 19 }

 tmnxEqPortBndlRedDiffExceeded   NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxBundleRedDiffDelay
    }
    STATUS  current
    DESCRIPTION
        "Generated when the differential delay of a port in the bundle 
         exceeds the configured value in tmnxBundleRedDiffDelay."
    ::= { tmnxPortNotification 20 }

 tmnxEqPortBndlBadEndPtDiscr  NOTIFICATION-TYPE
    OBJECTS {
        tmnxBundleMemberDownReason
    }
    STATUS  current
    DESCRIPTION
        "Generated when the port detected mismatched peer endpoint 
         discriminator for the bundle."
    ::= { tmnxPortNotification 21 }

 tmnxEqPortEtherAlarm     NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifyEtherAlarmReason    
    }
    STATUS  current
    DESCRIPTION
        "tmnxEqPortEtherAlarm is generated when a ethernet port alarm 
         condition is detected. It is generated only when the type of 
         alarm being raised is enabled in tmnxPortEtherReportAlarm."
    ::= { tmnxPortNotification 22 }
                 
 tmnxEqPortEtherAlarmClear        NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortNotifyEtherAlarmReason    
    }
    STATUS  current
    DESCRIPTION
        "tmnxEqPortEtherAlarmClear is generated when a ethernet port alarm 
         condition is cleared. It is generated only when the type of alarm 
         being cleared is enabled in tmnxPortEtherReportAlarm."
    ::= { tmnxPortNotification 23 }

 tmnxDS1E1LoopbackStarted        NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxDS1Loopback
    }
    STATUS          current
    DESCRIPTION
        "The tmnxDS1E1LoopbackStarted notification is generated when a 
         loopback is provisioned on a DS1/E1 port."
    ::= { tmnxPortNotification 24 }
 
 tmnxDS1E1LoopbackStopped NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxDS1Loopback
    }
    STATUS          current
    DESCRIPTION
        "The tmnxDS1E1LoopbackStopped notification is generated when a 
         loopback is removed on a DS1/E1 port. The value of 
         tmnxSonetLoopback specifies the type of loopback that was 
         configured and has now been removed."
    ::= { tmnxPortNotification 25 }
 
 tmnxDS3E3LoopbackStarted NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxDS3ChannelLoopback
    }
    STATUS          current
    DESCRIPTION
        "The tmnxDS3E3LoopbackStarted notification is generated when a
         loopback is provisioned on a DS3/E3 port."
    ::= { tmnxPortNotification 26 }
 
 tmnxDS3E3LoopbackStopped NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxDS3ChannelLoopback
    }
    STATUS          current
    DESCRIPTION
        "The tmnxDS3E3LoopbackStopped notification is generated when a 
         loopback is removed on a DS3/E3 port. The value of 
         tmnxDS3ChannelLoopback specifies the type of loopback that was 
         configured and has now been removed."
    ::= { tmnxPortNotification 27 }
 
 tmnxSonetSDHLoopbackStarted NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxSonetLoopback
    }
    STATUS          current
    DESCRIPTION
        "The tmnxSonetSDHLoopbackStarted notification is generated when a
         loopback is provisioned on a Sonet-SDH port."
    ::= { tmnxPortNotification 28 }
 
 tmnxSonetSDHLoopbackStopped NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxSonetLoopback
    }
    STATUS          current
    DESCRIPTION
        "The tmnxSonetSDHLoopbackStopped notification is generated when a
         loopback test is removed on a Sonet-SDH port. The value of 
         tmnxDS1Loopback specifies the type of loopback that was 
         configured and has now been removed."
    ::= { tmnxPortNotification 29 }

--
--  Down-when-looped Alarm
--
 tmnxEqPortEtherLoopDetected  NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId
    }
    STATUS  current
    DESCRIPTION
        "The tmnxEqPortEtherLoopDetected notification is genereated when
         down-when-looped detects an Ethernet port is receiving PDUs that
         it transmitted and tmnxPortEtherDownWhenLoopedEnabled is set to
         'true'."
    ::= { tmnxPortNotification 30 }

 tmnxEqPortEtherLoopCleared  NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId
    }
    STATUS  current
    DESCRIPTION
        "The tmnxEqPortEtherLoopCleared notification is generated when
         down-when-looped detects an Ethernet port has stopped receiving
         PDUs that it transmitted and tmnxPortEtherDownWhenLoopedEnabled
         is set to 'true'.  Setting tmnxPortEtherDownWhenLoopedEnabled to
         'false' will also cause this notification to be generated if 
         tmnxEqPortEtherLoopDetected had previously been raised."
    ::= { tmnxPortNotification 31 }

 tmnxEqPortSpeedCfgNotCompatible  NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortEtherSpeed
    }
    STATUS  current
    DESCRIPTION
        "Generated when a supported MDA is inserted into a slot of an
         IOM, the MDA is compatible with the currently provisioned MDA, 
         but the currently configured speed on an MDA port is not 
         compatible with the inserted MDA."
    ::= { tmnxPortNotification 32 }

 tmnxEqPortDuplexCfgNotCompatible  NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortEtherDuplex
    }
    STATUS  current
    DESCRIPTION
        "Generated when a supported MDA is inserted into a slot of an
         IOM, the MDA is compatible with the currently provisioned MDA,
         but the currently configured duplex on an MDA port is not 
         compatible with the inserted MDA."
    ::= { tmnxPortNotification 33 }

 tmnxEqPortIngressRateCfgNotCompatible  NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId
    }
    STATUS  current
    DESCRIPTION
        "Generated when a supported MDA is inserted into a slot of an
         IOM, the MDA is compatible with the currently provisioned MDA,
         but the currently configured ingress rate on an MDA port is not 
         compatible with the inserted MDA."
    ::= { tmnxPortNotification 34 }

tmnxEqDigitalDiagMonitorFailure NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxDDMFailedObject
    }
    STATUS  current
    DESCRIPTION
        "Generated when an SFP/XFP that supports Digital Diagnostic
         Monitoring (DDM) enters a failed state."
    ::= { tmnxPortNotification 35 }

tmnxEqPortSFPStatusFailure NOTIFICATION-TYPE
    OBJECTS {
        tmnxPortNotifyPortId,
        tmnxPortSFPStatus
    }
    STATUS  current
    DESCRIPTION
        "Generated when the tmnxPortSFPStatus of an SFP/XFP results in 
         a value other than 'not-equipped (0)', or 'operational (1)'.
         tmnxEqPortSFPStatusFailure obsoleted tmnxEqPortSFPCorrupted for
         revision 6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortNotification 36 }

--
--  Quality of Service Alarm
--
tmnxQosServiceDegraded     NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyChassisId,
        tmnxPortNotifyPortId
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when the port is unable to provided the specified
         quality of service level."
    ::= { tmnxPortNotification 3 }


--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      The compliance specifications.
--

tmnxPortCompliances     OBJECT IDENTIFIER ::= { tmnxPortConformance 1 }
tmnxPortGroups            OBJECT IDENTIFIER ::= { tmnxPortConformance 2 }

tmnxPortComp7750        OBJECT IDENTIFIER ::= { tmnxPortCompliances 3 }
--  tmnxPortComp7750V3v0  OBJECT IDENTIFIER ::= { tmnxPortComp7750 1}

tmnxPortComp7450        OBJECT IDENTIFIER ::= { tmnxPortCompliances 4 }
--  tmnxPortComp7450V3v0  OBJECT IDENTIFIER ::= { tmnxPortComp7450 1 }

tmnxPortComp7710        OBJECT IDENTIFIER ::= { tmnxPortCompliances 5 }
--  tmnxPortComp7710V3v0  OBJECT IDENTIFIER ::= { tmnxPortComp7710 1 }

-- tmnxPortCompliance  MODULE-COMPLIANCE
--    ::= { tmnxPortCompliances 1 }

-- tmnxPortR2r1Compliance  MODULE-COMPLIANCE
--    ::= { tmnxPortCompliances 2 }

-- tmnxPortComp7750V3v0  MODULE-COMPLIANCE
--    ::= { tmnxPortComp7750 1 }

tmnxPortComp7750V4v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for revision 4.0 of ALCATEL-IND1-TIMETRA-PORT-MIB on 
             the Alcatel 7750 SR series systems.
             
             tmnxMlImaBundleGroup was added as of R4."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV4v0,
            tmnxPortEthernetV3v0Group,
            tmnxPortSonetV3v0Group,
            tmnxPortTDMGroupV4v0,
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV3v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortATMGroupV4v0,
            tmnxPortStatsR2r1Group,
            tmnxCiscoHDLCGroup,
            tmnxScalarPortV3v0Group,
            tmnxMlBundleGroupV4v0,
            tmnxMlImaBundleGroup
        }
    ::= { tmnxPortComp7750 2 }

tmnxPortComp7750V5v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for revision 5.0 of ALCATEL-IND1-TIMETRA-PORT-MIB on 
             the Alcatel 7750 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV5v0,
            tmnxPortSonetV3v0Group,
            tmnxPortTDMGroupV5v0,
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV5v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortATMGroupV4v0,
            tmnxPortStatsR2r1Group,
            tmnxCiscoHDLCGroup,
            tmnxScalarPortV3v0Group,
            tmnxMlBundleGroupV5v0,
            tmnxMlImaBundleGroup,
            tmnxPortSchedV5v0Group,
            tmnxPortEthernetV5v0Group
        }
    ::= { tmnxPortComp7750 3 }

tmnxPortComp7750V6v0  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for revision 6.0 of ALCATEL-IND1-TIMETRA-PORT-MIB on 
             the Alcatel 7750 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV6v0,
            tmnxPortSonetV3v0Group,
            tmnxPortTDMGroupV6v0,
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV6v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortATMGroupV4v0,
            tmnxPortStatsR2r1Group,
            tmnxCiscoHDLCGroup,
            tmnxScalarPortV3v0Group,
            tmnxMlBundleGroupV6v0,
            tmnxMlImaBundleGroup,
            tmnxPortSchedV5v0Group,
            tmnxPortEthernetV6v0Group,
            tmnxPortCemGroupV6v0,
            tmnxMcMlpppBundleGroup,
            tmnxMlpppBundleGroup,
            tmnxNamedPoolGroupV6v0,
            tmnxDigitalDiagMonitorGroup 
        }
    ::= { tmnxPortComp7750 4 }

-- tmnxPortComp7450V3v0  MODULE-COMPLIANCE
--    ::= { tmnxPortComp7450 1 }

tmnxPortComp7450V4v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for revision 4.0 of ALCATEL-IND1-TIMETRA-PORT-MIB on 
             the Alcatel 7450 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV4v0,
            tmnxPortEthernetV3v0Group,
            tmnxPortSonetV3v0Group,
            -- tmnxPortTDMGroupV4v0,
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV3v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortStatsR2r1Group,
            tmnxScalarPortV3v0Group
            -- tmnxPortATMGroupV4v0,         No ATM on 7450
            -- tmnxCiscoHDLCGroup,           No Cisco HDLC on 7450
            -- tmnxMlBundleGroupV4v0         No ML Bundles on 7450
            -- tmnxMlImaBundleGroup          No IMA Bundles on 7450
        }
    ::= { tmnxPortComp7450 2 }

tmnxPortComp7450V5v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for revision 5.0 of ALCATEL-IND1-TIMETRA-PORT-MIB on 
             the Alcatel 7450 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV5v0,
            tmnxPortSonetV3v0Group,
            --tmnxPortTDMGroupV5v0,         No TDM interfaces
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV5v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortStatsR2r1Group,
            tmnxScalarPortV3v0Group,
            -- tmnxPortATMGroupV4v0,         No ATM on 7450
            -- tmnxCiscoHDLCGroup,           No Cisco HDLC on 7450
            -- tmnxMlBundleGroupV5v0         No ML Bundles on 7450
            -- tmnxMlImaBundleGroup          No IMA Bundles on 7450
            -- tmnxPortSchedV5v0Group,
            tmnxPortEthernetV5v0Group
        }
    ::= { tmnxPortComp7450 3 }

tmnxPortComp7450V6v0  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for revision 5.0 of ALCATEL-IND1-TIMETRA-PORT-MIB on 
             the Alcatel 7450 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV6v0,
            tmnxPortSonetV3v0Group,
            -- tmnxPortTDMGroupV6v0,         No TDM interfaces
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV6v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortStatsR2r1Group,
            tmnxScalarPortV3v0Group,
            -- tmnxPortATMGroupV4v0,         No ATM on 7450
            -- tmnxCiscoHDLCGroup,           No Cisco HDLC on 7450
            -- tmnxMlBundleGroupV5v0         No ML Bundles on 7450
            -- tmnxMlImaBundleGroup          No IMA Bundles on 7450
            -- tmnxPortSchedV5v0Group,
            tmnxPortEthernetV6v0Group,
            -- tmnxPortCemGroupV6v0          No CEM on 7450
            -- tmnxMcMlpppBundleGroup        No Multiclass MLPPP Bundles on 7450
            -- tmnxMlpppBundleGroup          No MLPPP Bundles on 7450
            tmnxNamedPoolGroupV6v0,
            tmnxDigitalDiagMonitorGroup 
        }
    ::= { tmnxPortComp7450 4 }

tmnxPortComp7710V3v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for revision 3.0 of ALCATEL-IND1-TIMETRA-PORT-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortV3v0Group,
            tmnxPortEthernetV3v0Group,
            tmnxPortSonetV3v0Group,
            tmnxPortTDMV3v0Group,
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            tmnxPortTestGroup,
            tmnxPortNotificationGroupR2r1,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortATMV3v0Group,
            tmnxPortStatsR2r1Group,
            tmnxCiscoHDLCGroup,
            tmnxScalarPortV3v0Group,
            tmnxMlBundleV3v0Group,
            tmnx7710PortTDMGroupV3v0
        }
    ::= { tmnxPortComp7710 1 }

tmnxPortComp7710V5v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for revision 5.0 of ALCATEL-IND1-TIMETRA-PORT-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV5v0,
            tmnxPortEthernetV5v0Group,
            tmnxPortSonetV3v0Group,
            tmnxPortTDMGroupV5v0,
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV5v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortATMGroupV4v0,
            tmnxPortStatsR2r1Group,
            tmnxCiscoHDLCGroup,
            tmnxScalarPortV3v0Group,
            tmnxMlBundleGroupV5v0,
            tmnxPortSchedV5v0Group,
            tmnx7710PortTDMGroupV5v0
        }
    ::= { tmnxPortComp7710 2 }
    
tmnxPortComp7710V6v0  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for revision 6.0 of ALCATEL-IND1-TIMETRA-PORT-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxPortGroupV6v0,
            tmnxPortEthernetV6v0Group,
            tmnxPortSonetV3v0Group,
            tmnxPortTDMGroupV6v0,
            tmnxPortFRGroup,
            tmnxQosAppObjsGroup,
            -- tmnxPortTestGroup,
            tmnxPortNotificationGroupV6v0,
            tmnxPortIngrMdaQosStatR2r1Group,
            tmnxPortATMGroupV4v0,
            tmnxPortStatsR2r1Group,
            tmnxCiscoHDLCGroup,
            tmnxScalarPortV3v0Group,
            tmnxMlBundleGroupV6v0,
            tmnxPortSchedV5v0Group,
            tmnx7710PortTDMGroupV5v0,
            tmnxPortCemGroupV6v0,
            tmnxMcMlpppBundleGroup,
            tmnxMlpppBundleGroup,
            -- tmnxNamedPoolGroupV6v0,
            tmnxDigitalDiagMonitorGroup 
        }
    ::= { tmnxPortComp7710 3 }

-- units of conformance

-- tmnxPortGroup   OBJECT-GROUP
--    ::= { tmnxPortGroups 1 }

-- tmnxPortEthernetGroup       OBJECT-GROUP
--    ::= { tmnxPortGroups 2 }

-- tmnxPortSONETGroup      OBJECT-GROUP
--    ::= { tmnxPortGroups 3 }

-- tmnxPortTDMGroup    OBJECT-GROUP
--    ::= { tmnxPortGroups 4 }

tmnxPortFRGroup     OBJECT-GROUP
    OBJECTS {   tmnxFRDlcmiMode,
                tmnxFRDlcmiN392Dce,
                tmnxFRDlcmiN393Dce,
                tmnxFRDlcmiT392Dce,
                tmnxFRDlcmiTxStatusEnqMsgs,
                tmnxFRDlcmiRxStatusEnqMsgs,
                tmnxFRDlcmiStatusEnqMsgTimeouts,
                tmnxFRDlcmiTxStatusMsgs,
                tmnxFRDlcmiRxStatusMsgs,
                tmnxFRDlcmiStatusMsgTimeouts,
                tmnxFRDlcmiDiscardedMsgs,
                tmnxFRDlcmiInvRxSeqNumMsgs
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Frame Relay DLCMI
         on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 5 }

tmnxQosAppObjsGroup     OBJECT-GROUP
    OBJECTS {   tmnxObjectAppPoolRowStatus,
                tmnxObjectAppResvCbs,
                tmnxObjectAppSlopePolicy,
                tmnxObjectAppPoolSize
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Qos associations of 
         the buffer pools to mdas, ports, channels and bundles on 
         Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 6 }

tmnxPortTestGroup       OBJECT-GROUP
    OBJECTS {   tmnxPortTestState,
                tmnxPortTestMode,
                tmnxPortTestParameter,
                tmnxPortTestLastResult,
                tmnxPortTestStartTime,
                tmnxPortTestEndTime,
                tmnxPortTestDuration,
                tmnxPortTestAction
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of port testing
         on Alcatel 7x50 SR series system."
    ::= { tmnxPortGroups 7 }

-- tmnxPortNotifyObjsGroup   OBJECT-GROUP
--    ::= { tmnxPortGroups 8 }

-- tmnxPortNotificationGroup NOTIFICATION-GROUP
--    ::= { tmnxPortGroups 9 }

-- tmnxPortATMGroup     OBJECT-GROUP
--    ::= { tmnxPortGroups 10 }

tmnxPortObsoleteGroup    OBJECT-GROUP
    OBJECTS {   
                tmnxDS1IdleCycleFlags,
                tmnxSonetPathType,
                tmnxPortFCStatsIngFwdInProfPkts,
                tmnxPortFCStatsIngFwdOutProfPkts,
                tmnxPortFCStatsIngFwdInProfOcts,
                tmnxPortFCStatsIngFwdOutProfOcts,
                tmnxPortFCStatsIngDroInProfPkts,
                tmnxPortFCStatsIngDroOutProfPkts,
                tmnxPortFCStatsIngDroInProfOcts,
                tmnxPortFCStatsIngDroOutProfOcts,
                tmnxPortFCStatsEgrFwdInProfPkts,
                tmnxPortFCStatsEgrFwdOutProfPkts,
                tmnxPortFCStatsEgrFwdInProfOcts,
                tmnxPortFCStatsEgrFwdOutProfOcts,
                tmnxPortFCStatsEgrDroInProfPkts,
                tmnxPortFCStatsEgrDroOutProfPkts,
                tmnxPortFCStatsEgrDroInProfOcts,
                tmnxPortFCStatsEgrDroOutProfOcts
            }
    STATUS      current
    DESCRIPTION
        "The group of objects in ALCATEL-IND1-TIMETRA-PORT-MIB which are obsoleted."
    ::= { tmnxPortGroups 11 }

-- tmnxPortR2r1Group   OBJECT-GROUP
--    ::= { tmnxPortGroups 12 }

-- tmnxPortEthernetR2r1Group       OBJECT-GROUP
--    ::= { tmnxPortGroups 13 }

tmnxPortIngrMdaQosStatR2r1Group OBJECT-GROUP
    OBJECTS {  
             tmnxPortIngrMdaQos00StatDropPkts,
             tmnxPortIngrMdaQos00StatDropOcts,
             tmnxPortIngrMdaQos01StatDropPkts,
             tmnxPortIngrMdaQos01StatDropOcts,
             tmnxPortIngrMdaQos02StatDropPkts,
             tmnxPortIngrMdaQos02StatDropOcts,
             tmnxPortIngrMdaQos03StatDropPkts,
             tmnxPortIngrMdaQos03StatDropOcts,
             tmnxPortIngrMdaQos04StatDropPkts,
             tmnxPortIngrMdaQos04StatDropOcts,
             tmnxPortIngrMdaQos05StatDropPkts,
             tmnxPortIngrMdaQos05StatDropOcts,
             tmnxPortIngrMdaQos06StatDropPkts,
             tmnxPortIngrMdaQos06StatDropOcts,
             tmnxPortIngrMdaQos07StatDropPkts,
             tmnxPortIngrMdaQos07StatDropOcts,
             tmnxPortIngrMdaQos08StatDropPkts,
             tmnxPortIngrMdaQos08StatDropOcts,
             tmnxPortIngrMdaQos09StatDropPkts,
             tmnxPortIngrMdaQos09StatDropOcts,
             tmnxPortIngrMdaQos10StatDropPkts,
             tmnxPortIngrMdaQos10StatDropOcts,
             tmnxPortIngrMdaQos11StatDropPkts,
             tmnxPortIngrMdaQos11StatDropOcts,
             tmnxPortIngrMdaQos12StatDropPkts,
             tmnxPortIngrMdaQos12StatDropOcts,
             tmnxPortIngrMdaQos13StatDropPkts,
             tmnxPortIngrMdaQos13StatDropOcts,
             tmnxPortIngrMdaQos14StatDropPkts,
             tmnxPortIngrMdaQos14StatDropOcts,
             tmnxPortIngrMdaQos15StatDropPkts,
             tmnxPortIngrMdaQos15StatDropOcts
            }
    STATUS        current
    DESCRIPTION
        "The group of objects that describe the MDA QoS discard
         statistics for an ethernet port located on an oversubscribed MDA
         for revision 2.1 on Alcatel 7x50 SR series systems." 
    ::= { tmnxPortGroups 14 }

-- tmnxPortSONETR2r1Group      OBJECT-GROUP
--    ::= { tmnxPortGroups 15 }

tmnxPortStatsR2r1Group      OBJECT-GROUP
    OBJECTS {   
                tmnxPortNetIngressFwdInProfPkts,
                tmnxPortNetIngressFwdOutProfPkts,
                tmnxPortNetIngressFwdInProfOcts,
                tmnxPortNetIngressFwdOutProfOcts,
                tmnxPortNetIngressDroInProfPkts,
                tmnxPortNetIngressDroOutProfPkts,
                tmnxPortNetIngressDroInProfOcts,
                tmnxPortNetIngressDroOutProfOcts,
                tmnxPortNetEgressFwdInProfPkts,
                tmnxPortNetEgressFwdOutProfPkts,
                tmnxPortNetEgressFwdInProfOcts,
                tmnxPortNetEgressFwdOutProfOcts,
                tmnxPortNetEgressDroInProfPkts,
                tmnxPortNetEgressDroOutProfPkts,
                tmnxPortNetEgressDroInProfOcts,
                tmnxPortNetEgressDroOutProfOcts
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting port statistics
         for revision 2.1 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 16 }

tmnxPortNotificationGroupR2r1 NOTIFICATION-GROUP
    NOTIFICATIONS   {   tmnxEqPortSonetAlarm,
                        tmnxEqPortSonetAlarmClear,
                        tmnxEqPortSonetPathAlarm,
                        tmnxEqPortSonetPathAlarmClear,
                        tmnxEqPortSFPInserted,
                        tmnxEqPortSFPRemoved,
                        tmnxEqPortSFPCorrupted,
                        tmnxEqPortError,
                        tmnxEqPortDS3Alarm,
                        tmnxEqPortDS3AlarmClear,
                        tmnxEqPortDS1Alarm,
                        tmnxEqPortDS1AlarmClear,
                        tmnxEqPortBndlYellowDiffExceeded,
                        tmnxEqPortBndlRedDiffExceeded,
                        tmnxEqPortBndlBadEndPtDiscr,
                        tmnxEqPortEtherAlarm,
                        tmnxEqPortEtherAlarmClear
                    }
    STATUS        obsolete
    DESCRIPTION
        "The group of notifications supporting the management of physical
         ports for revision 2.1 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 17 }

tmnxPortNotifyObjsGroupR2r1   OBJECT-GROUP
    OBJECTS {   tmnxPortNotifyPortId,
                tmnxPortNotifySonetAlarmReason,
                tmnxPortNotifySonetPathAlarmReason,
                tmnxPortNotifyError,
                tmnxPortNotifyDS3AlarmReason,
                tmnxPortNotifyDS1AlarmReason,
                tmnxPortNotifyBundleId,
                tmnxPortNotifyEtherAlarmReason
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting physical port notifications 
          for revision 2.1 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 18 }

-- tmnxPortTDMGroupR2r1    OBJECT-GROUP
--    ::= { tmnxPortGroups 19 }

tmnxPortNotifyObsoleteGroup NOTIFICATION-GROUP
    NOTIFICATIONS   {   tmnxEqOobPortFailure,
                        tmnxEqPortFailure,
                        tmnxQosServiceDegraded,
                        tmnxPortNotifyBerSdTca,
                        tmnxPortNotifyBerSfTca,
                        tmnxEqPortWrongSFP,
                        tmnxEqPortSFPCorrupted
                         
                    }
    STATUS        current
    DESCRIPTION
        "The group of notifications supporting the management of physical
         ports made obsolete for revision 2.1 on Alcatel 7x50 SR series 
         systems. tmnxEqPortSFPCorrupted was made obsolete for revision 6.0
         on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 20 }

tmnxPortSonetV3v0Group      OBJECT-GROUP
    OBJECTS {   tmnxSonetSpeed,
                tmnxSonetClockSource,
                tmnxSonetFraming,
                tmnxSonetReportAlarm,
                tmnxSonetBerSdThreshold,
                tmnxSonetBerSfThreshold,
                tmnxSonetLoopback,
                tmnxSonetReportAlarmStatus,
                tmnxSonetSectionTraceMode,
                tmnxSonetJ0String,
                tmnxSonetMonS1Byte,
                tmnxSonetMonJ0String,
                tmnxSonetMonK1Byte,
                tmnxSonetMonK2Byte,
                tmnxSonetSingleFiber,
                tmnxSonetHoldTimeUp,
                tmnxSonetHoldTimeDown,
                tmnxSonetPathRowStatus,
                tmnxSonetPathLastChangeTime,
                tmnxSonetPathMTU,
                tmnxSonetPathScramble,
                tmnxSonetPathC2Byte,
                tmnxSonetPathJ1String,
                tmnxSonetPathCRC,
                tmnxSonetPathOperMTU,
                tmnxSonetPathOperMRU,
                tmnxSonetPathReportAlarm,
                tmnxSonetPathAcctPolicyId,
                tmnxSonetPathCollectStats,
                tmnxSonetPathReportAlarmStatus,
                tmnxSonetPathMonC2Byte,
                tmnxSonetPathMonJ1String,
                tmnxSonetPathChildType,
                tmnxSonetGroupType,
                tmnxSonetGroupParentPortID,
                tmnxSonetGroupChildType,
                tmnxSonetGroupName
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of SONET type ports
         for revision 3.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 21 }

tmnxPortTDMV3v0Group    OBJECT-GROUP
    OBJECTS {   tmnxDS3Buildout,
                tmnxDS3Type,
                tmnxDS3LastChangeTime,
                tmnxDS3ChannelRowStatus,
                tmnxDS3ChannelType,
                tmnxDS3ChannelFraming,
                tmnxDS3ChannelClockSource,
                tmnxDS3ChannelChannelized,
                tmnxDS3ChannelSubrateCSUMode,
                tmnxDS3ChannelSubrate,
                tmnxDS3ChannelIdleCycleFlags,
                tmnxDS3ChannelLoopback,
                tmnxDS3ChannelBitErrorInsertionRate,
                tmnxDS3ChannelBERTPattern,
                tmnxDS3ChannelBERTDuration,
                tmnxDS3ChannelMDLEicString,
                tmnxDS3ChannelMDLLicString,
                tmnxDS3ChannelMDLFicString,
                tmnxDS3ChannelMDLUnitString,
                tmnxDS3ChannelMDLPfiString,
                tmnxDS3ChannelMDLPortString,
                tmnxDS3ChannelMDLGenString,
                tmnxDS3ChannelMDLMessageType,
                tmnxDS3ChannelFEACLoopRespond,
                tmnxDS3ChannelCRC,
                tmnxDS3ChannelMTU,
                tmnxDS3ChannelOperMTU,
                tmnxDS3ChannelReportAlarm,
                tmnxDS3ChannelReportAlarmStatus,
                tmnxDS3ChannelLastChangeTime,
                tmnxDS3ChannelInFEACLoop,
                tmnxDS3ChannelMDLMonPortString,
                tmnxDS3ChannelMDLMonGenString,
                tmnxDS3ChannelBERTOperStatus,
                tmnxDS3ChannelBERTSynched,
                tmnxDS3ChannelBERTErrors,
                tmnxDS3ChannelBERTTotalBits,
                tmnxDS1RowStatus,
                tmnxDS1Type,
                tmnxDS1Framing,
                tmnxDS1Loopback,
                tmnxDS1InvertData,
                tmnxDS1BitErrorInsertionRate,
                tmnxDS1BERTPattern,
                tmnxDS1BERTDuration,
                tmnxDS1ReportAlarm,
                tmnxDS1ReportAlarmStatus,
                tmnxDS1LastChangeTime,
                tmnxDS1ClockSource,
                tmnxDS1BERTOperStatus,
                tmnxDS1BERTSynched,
                tmnxDS1BERTErrors,
                tmnxDS1BERTTotalBits,
                tmnxDS1RemoteLoopRespond,
                tmnxDS1InRemoteLoop,
                tmnxDS0ChanGroupRowStatus,
                tmnxDS0ChanGroupTimeSlots,
                tmnxDS0ChanGroupSpeed,
                tmnxDS0ChanGroupCRC,
                tmnxDS0ChanGroupMTU,
                tmnxDS0ChanGroupOperMTU,
                tmnxDS0ChanGroupLastChangeTime,
                tmnxDS0ChanGroupIdleCycleFlags
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of TDM type ports
         for revision 3.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 22 }

tmnxPortATMV3v0Group     OBJECT-GROUP
    OBJECTS {   
                tmnxATMIntfCellFormat,
                tmnxATMIntfMinVpValue
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of ATM interfaces
         for version 3.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 23 }

tmnxScalarPortV3v0Group         OBJECT-GROUP
    OBJECTS { tmnxL4LoadBalancing }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management for general port
         settings for revision 3.0 on Alcatel 7x50 series systems."
    ::= { tmnxPortGroups 24 }

tmnxPortV3v0Group   OBJECT-GROUP
    OBJECTS {   tmnxPortTableLastChange,
                tmnxPortLastChangeTime,
                tmnxPortType,
                tmnxPortClass,
                tmnxPortDescription,
                tmnxPortName,
                tmnxPortAlias,
                tmnxPortUserAssignedMac,
                tmnxPortMacAddress,
                tmnxPortHwMacAddress,
                tmnxPortMode,
                tmnxPortEncapType,
                tmnxPortLagId,
                tmnxPortHoldTimeUp,
                tmnxPortHoldTimeDown,
                tmnxPortUpProtocols,
                tmnxPortConnectorType,
                tmnxPortTransceiverType,
                tmnxPortTransceiverCode,
                tmnxPortTransceiverLaserWaveLen,
                tmnxPortTransceiverDiagCapable,
                tmnxPortTransceiverModelNumber,
                tmnxPortSFPConnectorCode,
                tmnxPortSFPVendorOUI,
                tmnxPortSFPVendorManufactureDate, 
                tmnxPortSFPMedia,
                tmnxPortSFPEquipped,
                tmnxPortSFPVendorSerialNum,
                tmnxPortSFPVendorPartNum,
                tmnxPortEquipped,
                tmnxPortLinkStatus,
                tmnxPortAdminStatus,
                tmnxPortOperStatus,
                tmnxPortState,
                tmnxPortPrevState,
                tmnxPortNumAlarms,
                tmnxPortAlarmState,
                tmnxPortLastAlarmEvent,
                tmnxPortClearAlarms,
                tmnxPortLastStateChanged,
                tmnxPortNumChannels,
                tmnxPortNetworkEgrQueues,
                tmnxPortIsLeaf,
                tmnxPortChanType,
                tmnxPortParentPortID,
                tmnxPortLoadBalanceAlgorithm,
                tmnxPortTypeName,
                tmnxPortTypeDescription,
                tmnxPortTypeStatus,
                tmnxPortConnectTypeName,
                tmnxPortConnectTypeDescription,
                tmnxPortConnectTypeStatus,
                tmnxChannelPortID,
                tmnxPortOpticalCompliance,
                tmnxL4LoadBalancing
            }
    STATUS        obsolete
    DESCRIPTION
        "The group of objects supporting management of physical port 
         capabilities for revision 3.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 25 }

tmnxCiscoHDLCGroup      OBJECT-GROUP
    OBJECTS {   tmnxCiscoHDLCKeepAliveInt,
                tmnxCiscoHDLCUpCount,
                tmnxCiscoHDLCDownCount,
                tmnxCiscoHDLCOperState,
                tmnxCiscoHDLCDiscardStatInPkts,
                tmnxCiscoHDLCDiscardStatOutPkts,
                tmnxCiscoHDLCStatInPkts,
                tmnxCiscoHDLCStatOutPkts,
                tmnxCiscoHDLCStatInOctets,
                tmnxCiscoHDLCStatOutOctets
           }
    STATUS     current
    DESCRIPTION
        "The group of objects supporting Cisco HDLC encapsulation on Alcatel 
         7750 SR series systems."
    ::= { tmnxPortGroups 26 }

tmnxMlBundleV3v0Group    OBJECT-GROUP
    OBJECTS {   tmnxBundleRowStatus,
                tmnxBundleType,
                tmnxBundleMinimumLinks,
                tmnxBundleNumLinks,
                tmnxBundleNumActiveLinks,
                tmnxBundleMRRU,
                tmnxBundleOperMRRU,
                tmnxBundlePeerMRRU,
                tmnxBundleOperMTU,
                tmnxBundleRedDiffDelay,
                tmnxBundleRedDiffDelayAction,
                tmnxBundleYellowDiffDelay,
                tmnxBundleShortSequence,
                tmnxBundleLastChangeTime,
                tmnxBundleFragmentThreshold,
                tmnxBundleUpTime,
                tmnxBundleMemberRowStatus,
                tmnxBundleMemberActive,
                tmnxBundleMemberDownReason,
                tmnxBundleMemberUpTime,
                tmnxBundleInputDiscards,
                tmnxBundlePrimaryMemberPortID,
                tmnxBundleLFI,
                tmnxPortBundleNumber
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management MLBUNDLES
         for revision 3.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 27 }

tmnxObsoleteGroupV3v0    OBJECT-GROUP
    OBJECTS {   tmnxSonetAps,
                tmnxSonetApsAdminStatus,
                tmnxSonetApsOperStatus,
                tmnxSonetApsAuthKey,
                tmnxSonetApsNeighborAddr,
                tmnxSonetApsAdvertiseInterval,
                tmnxSonetApsAdvertiseTimeLeft,
                tmnxSonetApsHoldTime,
                tmnxSonetApsHoldTimeLeft
            }
    STATUS      current
    DESCRIPTION
        "The group of objects obsoleted in the 7x50 SR series 3.0 release"
    ::= { tmnxPortGroups 28 }

tmnxPortEthernetV3v0Group       OBJECT-GROUP
    OBJECTS {  tmnxPortEtherMTU,
                tmnxPortEtherDuplex,
                tmnxPortEtherSpeed,
                tmnxPortEtherAutoNegotiate,
                tmnxPortEtherOperDuplex,
                tmnxPortEtherOperSpeed,
                tmnxPortEtherAcctPolicyId,
                tmnxPortEtherCollectStats,        
                tmnxPortEtherMDIMDIX,
                tmnxPortEtherXGigMode,
                tmnxPortEtherEgressRate,
                tmnxPortEtherDot1qEtype,
                tmnxPortEtherQinqEtype,
                tmnxPortEtherIngressRate,
                tmnxPortEtherReportAlarm,
                tmnxPortEtherReportAlarmStatus,
                tmnxPortEtherPkts1519toMax,
                tmnxPortEtherHCOverPkts1519toMax,
                tmnxPortEtherHCPkts1519toMax
            }
    STATUS      obsolete 
    DESCRIPTION
        "The group of objects supporting management of Ethernet ports
         for revision 3.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 29 }

tmnxPortTDMGroupV4v0    OBJECT-GROUP
    OBJECTS {   tmnxDS3Buildout,
                tmnxDS3Type,
                tmnxDS3LastChangeTime,
                tmnxDS3ChannelRowStatus,
                tmnxDS3ChannelType,
                tmnxDS3ChannelFraming,
                tmnxDS3ChannelClockSource,
                tmnxDS3ChannelChannelized,
                tmnxDS3ChannelSubrateCSUMode,
                tmnxDS3ChannelSubrate,
                tmnxDS3ChannelIdleCycleFlags,
                tmnxDS3ChannelLoopback,
                tmnxDS3ChannelBitErrorInsertionRate,
                tmnxDS3ChannelBERTPattern,
                tmnxDS3ChannelBERTDuration,
                tmnxDS3ChannelMDLEicString,
                tmnxDS3ChannelMDLLicString,
                tmnxDS3ChannelMDLFicString,
                tmnxDS3ChannelMDLUnitString,
                tmnxDS3ChannelMDLPfiString,
                tmnxDS3ChannelMDLPortString,
                tmnxDS3ChannelMDLGenString,
                tmnxDS3ChannelMDLMessageType,
                tmnxDS3ChannelFEACLoopRespond,
                tmnxDS3ChannelCRC,
                tmnxDS3ChannelMTU,
                tmnxDS3ChannelOperMTU,
                tmnxDS3ChannelReportAlarm,
                tmnxDS3ChannelReportAlarmStatus,
                tmnxDS3ChannelLastChangeTime,
                tmnxDS3ChannelInFEACLoop,
                tmnxDS3ChannelMDLMonPortString,
                tmnxDS3ChannelMDLMonGenString,
                tmnxDS3ChannelBERTOperStatus,
                tmnxDS3ChannelBERTSynched,
                tmnxDS3ChannelBERTErrors,
                tmnxDS3ChannelBERTTotalBits,
                tmnxDS3ChannelScramble,
                tmnxDS1RowStatus,
                tmnxDS1Type,
                tmnxDS1Framing,
                tmnxDS1Loopback,
                tmnxDS1InvertData,
                tmnxDS1BitErrorInsertionRate,
                tmnxDS1BERTPattern,
                tmnxDS1BERTDuration,
                tmnxDS1ReportAlarm,
                tmnxDS1ReportAlarmStatus,
                tmnxDS1LastChangeTime,
                tmnxDS1ClockSource,
                tmnxDS1BERTOperStatus,
                tmnxDS1BERTSynched,
                tmnxDS1BERTErrors,
                tmnxDS1BERTTotalBits,
                tmnxDS1RemoteLoopRespond,
                tmnxDS1InRemoteLoop,
                tmnxDS0ChanGroupRowStatus,
                tmnxDS0ChanGroupTimeSlots,
                tmnxDS0ChanGroupSpeed,
                tmnxDS0ChanGroupCRC,
                tmnxDS0ChanGroupMTU,
                tmnxDS0ChanGroupOperMTU,
                tmnxDS0ChanGroupLastChangeTime,
                tmnxDS0ChanGroupIdleCycleFlags,
                tmnxDS0ChanGroupScramble
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of TDM type ports
         for version 4.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 30 }

tmnxPortATMGroupV4v0     OBJECT-GROUP
    OBJECTS {   
                tmnxATMIntfCellFormat,
                tmnxATMIntfMinVpValue,
                tmnxATMIntfMapping
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of ATM interfaces
         for version 4.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 31 }

tmnxMlBundleGroupV4v0    OBJECT-GROUP
    OBJECTS {   tmnxBundleRowStatus,
                tmnxBundleType,
                tmnxBundleMinimumLinks,
                tmnxBundleNumLinks,
                tmnxBundleNumActiveLinks,
                tmnxBundleMRRU,
                tmnxBundleOperMRRU,
                tmnxBundlePeerMRRU,
                tmnxBundleOperMTU,
                tmnxBundleRedDiffDelay,
                tmnxBundleRedDiffDelayAction,
                tmnxBundleYellowDiffDelay,
                tmnxBundleShortSequence,
                tmnxBundleLastChangeTime,
                tmnxBundleFragmentThreshold,
                tmnxBundleUpTime,
                tmnxBundleMemberRowStatus,
                tmnxBundleMemberActive,
                tmnxBundleMemberDownReason,
                tmnxBundleMemberUpTime,
                tmnxBundleInputDiscards,
                tmnxBundlePrimaryMemberPortID,
                tmnxBundleLFI,
                tmnxPortBundleNumber
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management MLBUNDLES
         for revision 4.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 32 }

tmnxMlImaBundleGroup    OBJECT-GROUP
    OBJECTS {  
                tmnxBundleImaGrpLnkActTimer,
                tmnxBundleImaGrpLnkDeactTimer,
                tmnxBundleImaGrpSymmetryMode,
                tmnxBundleImaGrpTxId,          
                tmnxBundleImaGrpRxId,          
                tmnxBundleImaGrpTxRefLnk,      
                tmnxBundleImaGrpRxRefLnk,      
                tmnxBundleImaGrpSmNeState,     
                tmnxBundleImaGrpSmFeState,     
                tmnxBundleImaGrpSmFailState,   
                tmnxBundleImaGrpSmDownSecs,    
                tmnxBundleImaGrpSmOperSecs,    
                tmnxBundleImaGrpAvailTxCR,     
                tmnxBundleImaGrpAvailRxCR,     
                tmnxBundleImaGrpNeFails,       
                tmnxBundleImaGrpFeFails,       
                tmnxBundleImaGrpTxIcpCells,    
                tmnxBundleImaGrpRxIcpCells,    
                tmnxBundleImaGrpErrorIcpCells, 
                tmnxBundleImaGrpLostRxIcpCells,
                tmnxBundleImaGrpTxOamLablVal,  
                tmnxBundleImaGrpRxOamLablVal,
                tmnxBundleImaGrpAlphaValue, 
                tmnxBundleImaGrpBetaValue,  
                tmnxBundleImaGrpGammaValue, 
                tmnxBundleImaGrpNeClockMode,
                tmnxBundleImaGrpFeClockMode,
                tmnxBundleImaGrpVersion,
                tmnxBundleImaGrpMaxConfBw,
                tmnxBundleImaGrpTestState,  
                tmnxBundleImaGrpTestMember, 
                tmnxBundleImaGrpTestPattern,
                tmnxBundleImaGrpDiffDelayMaxObs,
                tmnxBundleImaGrpLeastDelayLink,
                tmnxBundleMemberImaNeTxState,    
                tmnxBundleMemberImaNeRxState,    
                tmnxBundleMemberImaFeTxState,    
                tmnxBundleMemberImaFeRxState,    
                tmnxBundleMemberImaNeRxFailState,
                tmnxBundleMemberImaFeRxFailState,
                tmnxBundleMemberImaTxLid,        
                tmnxBundleMemberImaRxLid,        
                tmnxBundleMemberImaViolations,   
                tmnxBundleMemberImaNeSevErrSecs, 
                tmnxBundleMemberImaFeSevErrSecs, 
                tmnxBundleMemberImaNeUnavailSecs,
                tmnxBundleMemberImaFeUnavailSecs,
                tmnxBundleMemberImaNeTxUnuseSecs,
                tmnxBundleMemberImaNeRxUnuseSecs,
                tmnxBundleMemberImaFeTxUnuseSecs,
                tmnxBundleMemberImaFeRxUnuseSecs,
                tmnxBundleMemberImaNeTxNumFails, 
                tmnxBundleMemberImaNeRxNumFails, 
                tmnxBundleMemberImaFeTxNumFails, 
                tmnxBundleMemberImaFeRxNumFails, 
                tmnxBundleMemberImaTxIcpCells,  
                tmnxBundleMemberImaRxIcpCells,   
                tmnxBundleMemberImaErrorIcpCells,
                tmnxBundleMemberImaLstRxIcpCells,
                tmnxBundleMemberImaOifAnomalies,
                tmnxBundleMemberImaRxTestState,
                tmnxBundleMemberImaRxTestPattern,
                tmnxBundleMemberImaRelDelay
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Ima Groups
         and Members on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 33 }

tmnx7710PortTDMGroupV3v0   OBJECT-GROUP
    OBJECTS {
                tmnxDS3ChannelAcctPolicyId,
                tmnxDS3ChannelCollectStats,
                tmnxDS1PortBuildout,
                tmnxDS1PortLastChangeTime,
                tmnxDS1PortType,
                tmnxDS1PortLineLength,
                tmnxDS1PortLbo,
                tmnxDS1PortDbGain,
                tmnxDS1InsertSingleBitError,
                tmnxDS0ChanGroupAcctPolicyId,
                tmnxDS0ChanGroupCollectStats
           }
    STATUS     obsolete
    DESCRIPTION
        "The group of objects supporting management of TDM type ports
         specific to the Alcatel 7710 SR series systems for revision 3.0."
    ::= { tmnxPortGroups 34 }

tmnxPortGroupV4v0   OBJECT-GROUP
    OBJECTS {   tmnxPortTableLastChange,
                tmnxPortLastChangeTime,
                tmnxPortType,
                tmnxPortClass,
                tmnxPortDescription,
                tmnxPortName,
                tmnxPortAlias,
                tmnxPortUserAssignedMac,
                tmnxPortMacAddress,
                tmnxPortHwMacAddress,
                tmnxPortMode,
                tmnxPortEncapType,
                tmnxPortLagId,
                tmnxPortHoldTimeUp,
                tmnxPortHoldTimeDown,
                tmnxPortUpProtocols,
                tmnxPortConnectorType,
                tmnxPortTransceiverType,
                tmnxPortTransceiverCode,
                tmnxPortTransceiverLaserWaveLen,
                tmnxPortTransceiverDiagCapable,
                tmnxPortTransceiverModelNumber,
                tmnxPortSFPConnectorCode,
                tmnxPortSFPVendorOUI,
                tmnxPortSFPVendorManufactureDate, 
                tmnxPortSFPMedia,
                tmnxPortSFPEquipped,
                tmnxPortSFPVendorSerialNum,
                tmnxPortSFPVendorPartNum,
                tmnxPortEquipped,
                tmnxPortLinkStatus,
                tmnxPortAdminStatus,
                tmnxPortOperStatus,
                tmnxPortState,
                tmnxPortPrevState,
                tmnxPortNumAlarms,
                tmnxPortAlarmState,
                tmnxPortLastAlarmEvent,
                tmnxPortClearAlarms,
                tmnxPortLastStateChanged,
                tmnxPortNumChannels,
                tmnxPortNetworkEgrQueues,
                tmnxPortIsLeaf,
                tmnxPortChanType,
                tmnxPortParentPortID,
                tmnxPortLoadBalanceAlgorithm,
                tmnxPortTypeName,
                tmnxPortTypeDescription,
                tmnxPortTypeStatus,
                tmnxPortConnectTypeName,
                tmnxPortConnectTypeDescription,
                tmnxPortConnectTypeStatus,
                tmnxChannelPortID,
                tmnxPortOpticalCompliance,
                tmnxL4LoadBalancing
            }
    STATUS        obsolete
    DESCRIPTION
        "The group of objects supporting management of physical port 
         capabilities for revision 4.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 35 }

tmnxObsoleteGroupV5v0    OBJECT-GROUP
    OBJECTS {   
                tmnxPortTransceiverCode
            }
    STATUS      current
    DESCRIPTION
        "The group of objects obsoleted in the 7x50 SR series 5.0 release"
    ::= { tmnxPortGroups 36 }

tmnxPortSchedV5v0Group  OBJECT-GROUP
    OBJECTS {
                tmnxPortEgrPortSchedPlcy,
                tmnxPortSchedOverrideRowStatus,
                tmnxPortSchedOverrideSchedName,
                tmnxPortSchedOverrideLastChanged,
                tmnxPortSchedOverrideMaxRate,
                tmnxPortSchedOverrideLvl1PIR,
                tmnxPortSchedOverrideLvl1CIR,
                tmnxPortSchedOverrideLvl2PIR,
                tmnxPortSchedOverrideLvl2CIR,
                tmnxPortSchedOverrideLvl3PIR,
                tmnxPortSchedOverrideLvl3CIR,
                tmnxPortSchedOverrideLvl4PIR,
                tmnxPortSchedOverrideLvl4CIR,
                tmnxPortSchedOverrideLvl5PIR,
                tmnxPortSchedOverrideLvl5CIR,
                tmnxPortSchedOverrideLvl6PIR,
                tmnxPortSchedOverrideLvl6CIR,
                tmnxPortSchedOverrideLvl7PIR,
                tmnxPortSchedOverrideLvl7CIR,
                tmnxPortSchedOverrideLvl8PIR,
                tmnxPortSchedOverrideLvl8CIR,
                tmnxPortSchedOverrideFlags
            }     
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of physical port 
         virtual scheduler capabilities for revision 5.0 on Alcatel 
         7x50 SR series systems."
    ::= { tmnxPortGroups 37 }

tmnxPortEthernetV5v0Group       OBJECT-GROUP
    OBJECTS {  tmnxPortEtherMTU,
                tmnxPortEtherDuplex,
                tmnxPortEtherSpeed,
                tmnxPortEtherAutoNegotiate,
                tmnxPortEtherOperDuplex,
                tmnxPortEtherOperSpeed,
                tmnxPortEtherAcctPolicyId,
                tmnxPortEtherCollectStats,        
                tmnxPortEtherMDIMDIX,
                tmnxPortEtherXGigMode,
                tmnxPortEtherEgressRate,
                tmnxPortEtherDot1qEtype,
                tmnxPortEtherQinqEtype,
                tmnxPortEtherIngressRate,
                tmnxPortEtherReportAlarm,
                tmnxPortEtherReportAlarmStatus,
                tmnxPortEtherPkts1519toMax,
                tmnxPortEtherHCOverPkts1519toMax,
                tmnxPortEtherHCPkts1519toMax,
                tmnxPortEtherLacpTunnel
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of Ethernet ports
         for revision 5.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 38 }

tmnxPortGroupV5v0   OBJECT-GROUP
    OBJECTS {   tmnxPortTableLastChange,
                tmnxPortLastChangeTime,
                tmnxPortType,
                tmnxPortClass,
                tmnxPortDescription,
                tmnxPortName,
                tmnxPortAlias,
                tmnxPortUserAssignedMac,
                tmnxPortMacAddress,
                tmnxPortHwMacAddress,
                tmnxPortMode,
                tmnxPortEncapType,
                tmnxPortLagId,
                tmnxPortHoldTimeUp,
                tmnxPortHoldTimeDown,
                tmnxPortUpProtocols,
                tmnxPortConnectorType,
                tmnxPortTransceiverType,
                tmnxPortTransceiverCode,
                tmnxPortTransceiverLaserWaveLen,
                tmnxPortTransceiverDiagCapable,
                tmnxPortTransceiverModelNumber,
                tmnxPortSFPConnectorCode,
                tmnxPortSFPVendorOUI,
                tmnxPortSFPVendorManufactureDate, 
                tmnxPortSFPMedia,
                tmnxPortSFPEquipped,
                tmnxPortSFPVendorSerialNum,
                tmnxPortSFPVendorPartNum,
                tmnxPortEquipped,
                tmnxPortLinkStatus,
                tmnxPortAdminStatus,
                tmnxPortOperStatus,
                tmnxPortState,
                tmnxPortPrevState,
                tmnxPortNumAlarms,
                tmnxPortAlarmState,
                tmnxPortLastAlarmEvent,
                tmnxPortClearAlarms,
                tmnxPortLastStateChanged,
                tmnxPortNumChannels,
                tmnxPortNetworkEgrQueues,
                tmnxPortIsLeaf,
                tmnxPortChanType,
                tmnxPortParentPortID,
                tmnxPortLoadBalanceAlgorithm,
                tmnxPortTypeName,
                tmnxPortTypeDescription,
                tmnxPortTypeStatus,
                tmnxPortConnectTypeName,
                tmnxPortConnectTypeDescription,
                tmnxPortConnectTypeStatus,
                tmnxChannelPortID,
                tmnxPortOpticalCompliance,
                tmnxL4LoadBalancing,
                tmnxPortLastClearedTime
            }
    STATUS        obsolete 
    DESCRIPTION
        "The group of objects supporting management of physical port 
         capabilities for revision 5.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 39 }

tmnxMlBundleGroupV5v0    OBJECT-GROUP
    OBJECTS {   tmnxBundleRowStatus,
                tmnxBundleType,
                tmnxBundleMinimumLinks,
                tmnxBundleNumLinks,
                tmnxBundleNumActiveLinks,
                tmnxBundleMRRU,
                tmnxBundleOperMRRU,
                tmnxBundlePeerMRRU,
                tmnxBundleOperMTU,
                tmnxBundleRedDiffDelay,
                tmnxBundleRedDiffDelayAction,
                tmnxBundleYellowDiffDelay,
                tmnxBundleShortSequence,
                tmnxBundleLastChangeTime,
                tmnxBundleFragmentThreshold,
                tmnxBundleUpTime,
                tmnxBundleMemberRowStatus,
                tmnxBundleMemberActive,
                tmnxBundleMemberDownReason,
                tmnxBundleMemberUpTime,
                tmnxBundleInputDiscards,
                tmnxBundlePrimaryMemberPortID,
                tmnxBundleLFI,
                tmnxPortBundleNumber,
                tmnxBundleProtectedType,      
                tmnxBundleParentBundle,
                tmnxBPGrpAssocWorkingBundleID,
                tmnxBPGrpAssocProtectBundleID,
                tmnxBPGrpAssocActiveBundleID
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management MLBUNDLES
         for revision 5.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 40 }

tmnxPortNotificationGroupV5v0 NOTIFICATION-GROUP
    NOTIFICATIONS   {   tmnxEqPortSonetAlarm,
                        tmnxEqPortSonetAlarmClear,
                        tmnxEqPortSonetPathAlarm,
                        tmnxEqPortSonetPathAlarmClear,
                        tmnxEqPortSFPInserted,
                        tmnxEqPortSFPRemoved,
                        tmnxEqPortSFPCorrupted,
                        tmnxEqPortError,
                        tmnxEqPortDS3Alarm,
                        tmnxEqPortDS3AlarmClear,
                        tmnxEqPortDS1Alarm,
                        tmnxEqPortDS1AlarmClear,
                        tmnxEqPortBndlYellowDiffExceeded,
                        tmnxEqPortBndlRedDiffExceeded,
                        tmnxEqPortBndlBadEndPtDiscr,
                        tmnxEqPortEtherAlarm,
                        tmnxEqPortEtherAlarmClear,
                        tmnxDS1E1LoopbackStarted,
                        tmnxDS1E1LoopbackStopped,
                        tmnxDS3E3LoopbackStarted,  
                        tmnxDS3E3LoopbackStopped,
                        tmnxSonetSDHLoopbackStarted,
                        tmnxSonetSDHLoopbackStopped,
                        tmnxEqPortSpeedCfgNotCompatible,
                        tmnxEqPortDuplexCfgNotCompatible,
                        tmnxEqPortIngressRateCfgNotCompatible
                    }
    STATUS        obsolete
    DESCRIPTION
        "The group of notifications supporting the management of physical
         ports for revision 5.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 41 }

tmnxPortTDMGroupV5v0    OBJECT-GROUP
    OBJECTS {   tmnxDS3ChannelAcctPolicyId,
                tmnxDS3ChannelCollectStats,
                tmnxDS3Buildout,
                tmnxDS3Type,
                tmnxDS3LastChangeTime,
                tmnxDS3ChannelRowStatus,
                tmnxDS3ChannelType,
                tmnxDS3ChannelFraming,
                tmnxDS3ChannelClockSource,
                tmnxDS3ChannelChannelized,
                tmnxDS3ChannelSubrateCSUMode,
                tmnxDS3ChannelSubrate,
                tmnxDS3ChannelIdleCycleFlags,
                tmnxDS3ChannelLoopback,
                tmnxDS3ChannelBitErrorInsertionRate,
                tmnxDS3ChannelBERTPattern,
                tmnxDS3ChannelBERTDuration,
                tmnxDS3ChannelMDLEicString,
                tmnxDS3ChannelMDLLicString,
                tmnxDS3ChannelMDLFicString,
                tmnxDS3ChannelMDLUnitString,
                tmnxDS3ChannelMDLPfiString,
                tmnxDS3ChannelMDLPortString,
                tmnxDS3ChannelMDLGenString,
                tmnxDS3ChannelMDLMessageType,
                tmnxDS3ChannelFEACLoopRespond,
                tmnxDS3ChannelCRC,
                tmnxDS3ChannelMTU,
                tmnxDS3ChannelOperMTU,
                tmnxDS3ChannelReportAlarm,
                tmnxDS3ChannelReportAlarmStatus,
                tmnxDS3ChannelLastChangeTime,
                tmnxDS3ChannelInFEACLoop,
                tmnxDS3ChannelMDLMonPortString,
                tmnxDS3ChannelMDLMonGenString,
                tmnxDS3ChannelBERTOperStatus,
                tmnxDS3ChannelBERTSynched,
                tmnxDS3ChannelBERTErrors,
                tmnxDS3ChannelBERTTotalBits,
                tmnxDS3ChannelScramble,
                tmnxDS1RowStatus,
                tmnxDS1Type,
                tmnxDS1Framing,
                tmnxDS1Loopback,
                tmnxDS1InvertData,
                tmnxDS1BitErrorInsertionRate,
                tmnxDS1BERTPattern,
                tmnxDS1BERTDuration,
                tmnxDS1ReportAlarm,
                tmnxDS1ReportAlarmStatus,
                tmnxDS1LastChangeTime,
                tmnxDS1ClockSource,
                tmnxDS1BERTOperStatus,
                tmnxDS1BERTSynched,
                tmnxDS1BERTErrors,
                tmnxDS1BERTTotalBits,
                tmnxDS1RemoteLoopRespond,
                tmnxDS1InRemoteLoop,
                tmnxDS0ChanGroupRowStatus,
                tmnxDS0ChanGroupTimeSlots,
                tmnxDS0ChanGroupSpeed,
                tmnxDS0ChanGroupCRC,
                tmnxDS0ChanGroupMTU,
                tmnxDS0ChanGroupOperMTU,
                tmnxDS0ChanGroupLastChangeTime,
                tmnxDS0ChanGroupIdleCycleFlags,
                tmnxDS0ChanGroupScramble
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of TDM type ports
         for version 5.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 42 }

tmnx7710PortTDMGroupV5v0   OBJECT-GROUP
    OBJECTS {
                tmnxDS1PortBuildout,
                tmnxDS1PortLastChangeTime,
                tmnxDS1PortType,
                tmnxDS1PortLineLength,
                tmnxDS1PortLbo,
                tmnxDS1PortDbGain,
                tmnxDS1InsertSingleBitError,
                tmnxDS0ChanGroupAcctPolicyId,
                tmnxDS0ChanGroupCollectStats
            }
    STATUS     current
    DESCRIPTION
        "The group of objects supporting management of TDM type ports
         specific to the revision 5.0 on Alcatel 7710 SR series systems."
    ::= { tmnxPortGroups 43 }
    
tmnxPortCemGroupV6v0   OBJECT-GROUP
    OBJECTS {
                tmnxDS3ChannelClockSyncState,
                tmnxDS3ChannelClockMasterPortId,
                tmnxDS1SignalMode,
                tmnxDS1ClockSyncState,
                tmnxDS1ClockMasterPortId,
                tmnxDS0ChanGroupPayloadFillType,
                tmnxDS0ChanGroupPayloadPattern,
                tmnxDS0ChanGroupSignalFillType,
                tmnxDS0ChanGroupSignalPattern
            }
    STATUS     current
    DESCRIPTION
        "The group of objects supporting management of CEM encapsulation TDM
         type ports specific to the Alcatel 7750 and 7710 SR series systems
         for revision 6.0."
    ::= { tmnxPortGroups 44 }

tmnxMcMlpppBundleGroup    OBJECT-GROUP
    OBJECTS {   tmnxBundleMlpppClassCount,
                tmnxBundleMlpppIngQoSProfId,
                tmnxBundleMlpppEgrQoSProfId,
                tmnxMcMlpppStatsIngressOct,
                tmnxMcMlpppStatsIngressPkt,
                tmnxMcMlpppStatsIngressErrPkt,
                tmnxMcMlpppStatsEgressOct,
                tmnxMcMlpppStatsEgressPkt,
                tmnxMcMlpppStatsEgressErrPkt
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of MClass MLPPP BUNDLES
         for revision 6.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 45 }

tmnxPortNotificationGroupV6v0 NOTIFICATION-GROUP
    NOTIFICATIONS   {   tmnxEqPortSonetAlarm,
                        tmnxEqPortSonetAlarmClear,
                        tmnxEqPortSonetPathAlarm,
                        tmnxEqPortSonetPathAlarmClear,
                        tmnxEqPortSFPInserted,
                        tmnxEqPortSFPRemoved,
                        tmnxEqPortError,
                        tmnxEqPortDS3Alarm,
                        tmnxEqPortDS3AlarmClear,
                        tmnxEqPortDS1Alarm,
                        tmnxEqPortDS1AlarmClear,
                        tmnxEqPortBndlYellowDiffExceeded,
                        tmnxEqPortBndlRedDiffExceeded,
                        tmnxEqPortBndlBadEndPtDiscr,
                        tmnxEqPortEtherAlarm,
                        tmnxEqPortEtherAlarmClear,
                        tmnxDS1E1LoopbackStarted,
                        tmnxDS1E1LoopbackStopped,
                        tmnxDS3E3LoopbackStarted,  
                        tmnxDS3E3LoopbackStopped,
                        tmnxSonetSDHLoopbackStarted,
                        tmnxSonetSDHLoopbackStopped,
                        tmnxEqPortEtherLoopDetected,
                        tmnxEqPortEtherLoopCleared,
                        tmnxEqPortSpeedCfgNotCompatible,
                        tmnxEqPortDuplexCfgNotCompatible,
                        tmnxEqPortIngressRateCfgNotCompatible,
                        tmnxEqDigitalDiagMonitorFailure,
                        tmnxEqPortSFPStatusFailure
                    }
    STATUS        current
    DESCRIPTION
        "The group of notifications supporting the management of physical
         ports for revision 6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 46 }
    
tmnxPortEthernetV6v0Group       OBJECT-GROUP
    OBJECTS {  tmnxPortEtherMTU,
                tmnxPortEtherDuplex,
                tmnxPortEtherSpeed,
                tmnxPortEtherAutoNegotiate,
                tmnxPortEtherOperDuplex,
                tmnxPortEtherOperSpeed,
                tmnxPortEtherAcctPolicyId,
                tmnxPortEtherCollectStats,        
                tmnxPortEtherMDIMDIX,
                tmnxPortEtherXGigMode,
                tmnxPortEtherEgressRate,
                tmnxPortEtherDot1qEtype,
                tmnxPortEtherQinqEtype,
                tmnxPortEtherIngressRate,
                tmnxPortEtherReportAlarm,
                tmnxPortEtherReportAlarmStatus,
                tmnxPortEtherPkts1519toMax,
                tmnxPortEtherHCOverPkts1519toMax,
                tmnxPortEtherHCPkts1519toMax,
                tmnxPortEtherLacpTunnel,
                tmnxPortEtherDownWhenLoopedEnabled,
                tmnxPortEtherDownWhenLoopedKeepAlive,
                tmnxPortEtherDownWhenLoopedRetry,
                tmnxPortEtherDownWhenLoopedState,
                tmnxPortEtherPBBEtype,
                tmnxPortEtherReasonDownFlags
            }
    STATUS      current 
    DESCRIPTION
        "The group of objects supporting management of Ethernet ports
         for revision 6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 47 }

tmnxMlBundleGroupV6v0    OBJECT-GROUP
    OBJECTS {   tmnxBundleRowStatus,
                tmnxBundleType,
                tmnxBundleMinimumLinks,
                tmnxBundleNumLinks,
                tmnxBundleNumActiveLinks,
                tmnxBundleRedDiffDelay,
                tmnxBundleRedDiffDelayAction,
                tmnxBundleLastChangeTime,
                tmnxBundleFragmentThreshold,
                tmnxBundleUpTime,
                tmnxBundleMemberRowStatus,
                tmnxBundleMemberActive,
                tmnxBundleMemberDownReason,
                tmnxBundleMemberUpTime,
                tmnxBundleInputDiscards,
                tmnxBundlePrimaryMemberPortID,
                tmnxPortBundleNumber,
                tmnxBundleProtectedType,      
                tmnxBundleParentBundle,
                tmnxBPGrpAssocWorkingBundleID,
                tmnxBPGrpAssocProtectBundleID,
                tmnxBPGrpAssocActiveBundleID
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management MLBUNDLES
         for revision 6.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 48 }
    
tmnxMlpppBundleGroup    OBJECT-GROUP
    OBJECTS {  
                tmnxBundleMlpppEndpointID,
                tmnxBundleMlpppEndpointIDClass,
                tmnxBundleYellowDiffDelay,
                tmnxBundleShortSequence,
                tmnxBundleMRRU,
                tmnxBundleOperMRRU,
                tmnxBundlePeerMRRU,
                tmnxBundleOperMTU,
                tmnxBundleLFI
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of MLPPP Bundles
         on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 49 }

tmnxPortNotificationGroupV3v0 NOTIFICATION-GROUP
    NOTIFICATIONS   {   tmnxEqPortSonetAlarm,
                        tmnxEqPortSonetAlarmClear,
                        tmnxEqPortSonetPathAlarm,
                        tmnxEqPortSonetPathAlarmClear,
                        tmnxEqPortSFPInserted,
                        tmnxEqPortSFPRemoved,
                        tmnxEqPortSFPCorrupted,
                        tmnxEqPortError,
                        tmnxEqPortDS3Alarm,
                        tmnxEqPortDS3AlarmClear,
                        tmnxEqPortDS1Alarm,
                        tmnxEqPortDS1AlarmClear,
                        tmnxEqPortBndlYellowDiffExceeded,
                        tmnxEqPortBndlRedDiffExceeded,
                        tmnxEqPortBndlBadEndPtDiscr,
                        tmnxEqPortEtherAlarm,
                        tmnxEqPortEtherAlarmClear,
                        tmnxEqPortSpeedCfgNotCompatible,
                        tmnxEqPortDuplexCfgNotCompatible,
                        tmnxEqPortIngressRateCfgNotCompatible
                    }
    STATUS        obsolete
    DESCRIPTION
        "The group of notifications supporting the management of physical
         ports for revision 3.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 50 }

tmnxPortTDMGroupV6v0    OBJECT-GROUP
    OBJECTS {   tmnxDS3ChannelAcctPolicyId,
                tmnxDS3ChannelCollectStats,
                tmnxDS3Buildout,
                tmnxDS3Type,
                tmnxDS3LastChangeTime,
                tmnxDS3ChannelRowStatus,
                tmnxDS3ChannelType,
                tmnxDS3ChannelFraming,
                tmnxDS3ChannelClockSource,
                tmnxDS3ChannelChannelized,
                tmnxDS3ChannelSubrateCSUMode,
                tmnxDS3ChannelSubrate,
                tmnxDS3ChannelIdleCycleFlags,
                tmnxDS3ChannelLoopback,
                tmnxDS3ChannelBitErrorInsertionRate,
                tmnxDS3ChannelBERTPattern,
                tmnxDS3ChannelBERTDuration,
                tmnxDS3ChannelMDLEicString,
                tmnxDS3ChannelMDLLicString,
                tmnxDS3ChannelMDLFicString,
                tmnxDS3ChannelMDLUnitString,
                tmnxDS3ChannelMDLPfiString,
                tmnxDS3ChannelMDLPortString,
                tmnxDS3ChannelMDLGenString,
                tmnxDS3ChannelMDLMessageType,
                tmnxDS3ChannelFEACLoopRespond,
                tmnxDS3ChannelCRC,
                tmnxDS3ChannelMTU,
                tmnxDS3ChannelOperMTU,
                tmnxDS3ChannelReportAlarm,
                tmnxDS3ChannelReportAlarmStatus,
                tmnxDS3ChannelLastChangeTime,
                tmnxDS3ChannelInFEACLoop,
                tmnxDS3ChannelMDLMonPortString,
                tmnxDS3ChannelMDLMonGenString,
                tmnxDS3ChannelBERTOperStatus,
                tmnxDS3ChannelBERTSynched,
                tmnxDS3ChannelBERTErrors,
                tmnxDS3ChannelBERTTotalBits,
                tmnxDS3ChannelScramble,
                tmnxDS1RowStatus,
                tmnxDS1Type,
                tmnxDS1Framing,
                tmnxDS1Loopback,
                tmnxDS1InvertData,
                tmnxDS1BitErrorInsertionRate,
                tmnxDS1BERTPattern,
                tmnxDS1BERTDuration,
                tmnxDS1ReportAlarm,
                tmnxDS1ReportAlarmStatus,
                tmnxDS1LastChangeTime,
                tmnxDS1ClockSource,
                tmnxDS1BERTOperStatus,
                tmnxDS1BERTSynched,
                tmnxDS1BERTErrors,
                tmnxDS1BERTTotalBits,
                tmnxDS1RemoteLoopRespond,
                tmnxDS1InRemoteLoop,
                tmnxDS1BerSdThreshold,
                tmnxDS1BerSfThreshold,
                tmnxDS0ChanGroupRowStatus,
                tmnxDS0ChanGroupTimeSlots,
                tmnxDS0ChanGroupSpeed,
                tmnxDS0ChanGroupCRC,
                tmnxDS0ChanGroupMTU,
                tmnxDS0ChanGroupOperMTU,
                tmnxDS0ChanGroupLastChangeTime,
                tmnxDS0ChanGroupIdleCycleFlags,
                tmnxDS0ChanGroupScramble
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of TDM type ports
         for version 6.0 on the Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 52 }

tmnxDigitalDiagMonitorGroup OBJECT-GROUP
    OBJECTS   {   
                  tmnxDDMTemperature,
                  tmnxDDMTempLowWarning,
                  tmnxDDMTempLowAlarm,
                  tmnxDDMTempHiWarning,
                  tmnxDDMTempHiAlarm,
                  tmnxDDMSupplyVoltage,
                  tmnxDDMSupplyVoltageLowWarning,
                  tmnxDDMSupplyVoltageLowAlarm,
                  tmnxDDMSupplyVoltageHiWarning,
                  tmnxDDMSupplyVoltageHiAlarm,
                  tmnxDDMTxBiasCurrent,
                  tmnxDDMTxBiasCurrentLowWarning,
                  tmnxDDMTxBiasCurrentLowAlarm,
                  tmnxDDMTxBiasCurrentHiWarning,
                  tmnxDDMTxBiasCurrentHiAlarm,
                  tmnxDDMTxOutputPower,
                  tmnxDDMTxOutputPowerLowWarning,
                  tmnxDDMTxOutputPowerLowAlarm,
                  tmnxDDMTxOutputPowerHiWarning,
                  tmnxDDMTxOutputPowerHiAlarm,
                  tmnxDDMRxOpticalPower,
                  tmnxDDMRxOpticalPowerLowWarning,
                  tmnxDDMRxOpticalPowerLowAlarm,
                  tmnxDDMRxOpticalPowerHiWarning,
                  tmnxDDMRxOpticalPowerHiAlarm,
                  tmnxDDMRxOpticalPowerType,
                  tmnxDDMAux1,
                  tmnxDDMAux1LowWarning,
                  tmnxDDMAux1LowAlarm,
                  tmnxDDMAux1HiWarning,
                  tmnxDDMAux1HiAlarm,
                  tmnxDDMAux1Type,
                  tmnxDDMAux2,
                  tmnxDDMAux2LowWarning,
                  tmnxDDMAux2LowAlarm,
                  tmnxDDMAux2HiWarning,
                  tmnxDDMAux2HiAlarm,
                  tmnxDDMAux2Type,
                  tmnxDDMFailedThresholds,
                  tmnxDDMExternallyCalibrated,
                  tmnxDDMExtCalRxPower4,
                  tmnxDDMExtCalRxPower3,
                  tmnxDDMExtCalRxPower2,
                  tmnxDDMExtCalRxPower1,
                  tmnxDDMExtCalRxPower0,
                  tmnxDDMExtCalTxLaserBiasSlope,
                  tmnxDDMExtCalTxLaserBiasOffset,
                  tmnxDDMExtCalTxPowerSlope,
                  tmnxDDMExtCalTxPowerOffset,
                  tmnxDDMExtCalTemperatureSlope,
                  tmnxDDMExtCalTemperatureOffset,
                  tmnxDDMExtCalVoltageSlope,
                  tmnxDDMExtCalVoltageOffset
              }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting the management of 
         Digital Diagnostic Monitoring SFP/XFPs for revision 6.0 on 
         Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 53 }
    
tmnxPortGroupV6v0   OBJECT-GROUP
    OBJECTS {   tmnxPortTableLastChange,
                tmnxPortLastChangeTime,
                tmnxPortType,
                tmnxPortClass,
                tmnxPortDescription,
                tmnxPortName,
                tmnxPortAlias,
                tmnxPortUserAssignedMac,
                tmnxPortMacAddress,
                tmnxPortHwMacAddress,
                tmnxPortMode,
                tmnxPortEncapType,
                tmnxPortLagId,
                tmnxPortHoldTimeUp,
                tmnxPortHoldTimeDown,
                tmnxPortUpProtocols,
                tmnxPortConnectorType,
                tmnxPortTransceiverType,
                tmnxPortTransceiverLaserWaveLen,
                tmnxPortTransceiverDiagCapable,
                tmnxPortTransceiverModelNumber,
                tmnxPortSFPConnectorCode,
                tmnxPortSFPVendorOUI,
                tmnxPortSFPVendorManufactureDate, 
                tmnxPortSFPMedia,
                tmnxPortSFPEquipped,
                tmnxPortSFPVendorSerialNum,
                tmnxPortSFPVendorPartNum,
                tmnxPortEquipped,
                tmnxPortLinkStatus,
                tmnxPortAdminStatus,
                tmnxPortOperStatus,
                tmnxPortState,
                tmnxPortPrevState,
                tmnxPortNumAlarms,
                tmnxPortAlarmState,
                tmnxPortLastAlarmEvent,
                tmnxPortClearAlarms,
                tmnxPortLastStateChanged,
                tmnxPortNumChannels,
                tmnxPortNetworkEgrQueues,
                tmnxPortIsLeaf,
                tmnxPortChanType,
                tmnxPortParentPortID,
                tmnxPortLoadBalanceAlgorithm,
                tmnxPortTypeName,
                tmnxPortTypeDescription,
                tmnxPortTypeStatus,
                tmnxPortConnectTypeName,
                tmnxPortConnectTypeDescription,
                tmnxPortConnectTypeStatus,
                tmnxChannelPortID,
                tmnxPortOpticalCompliance,
                tmnxL4LoadBalancing,
                tmnxPortLastClearedTime,
                tmnxPortDDMEventSuppression,
                tmnxPortSFPStatus
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of physical port 
         capabilities for revision 6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 54 }

tmnxNamedPoolGroupV6v0 OBJECT-GROUP
    OBJECTS   {   
                  tmnxPortIngNamedPoolPlcy,
                  tmnxPortEgrNamedPoolPlcy,
                  tmnxPortIngPoolPercentRate,
                  tmnxPortEgrPoolPercentRate
              }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting the Named Pool feature
         for revision 6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 55 }

tmnxPortNotifyObjsGroupV6v0     OBJECT-GROUP
    OBJECTS {
                tmnxDDMFailedObject
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting physical port notifications
         for revision 6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxPortGroups 57 }

END
