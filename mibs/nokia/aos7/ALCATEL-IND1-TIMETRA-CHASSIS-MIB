ALCATEL-IND1-TIMETRA-CHASSIS-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, 
        NOTIFICATION-TYPE, Unsigned32,
        <PERSON>teger32, <PERSON><PERSON>ge32                      FROM SNMPv2-<PERSON><PERSON>

        MODULE-COMPLIANCE, OBJECT-<PERSON><PERSON><PERSON>, 
        NOTIFICATION-GROUP                      FROM SNMPv2-CONF

        TEXTUAL-CONVENTION, <PERSON>AndT<PERSON>, 
        RowStatus, TimeStamp, TimeInterval,
        TruthValue, MacAddress, RowPointer,
        DisplayString                           FROM SNMPv2-TC

        SnmpAdminString                         FROM SNMP-FRAMEWORK-MIB

        TmnxActionType, TmnxPortID,
        TItemDescription, TNamedItemOrEmpty,
        TNamedItem, TmnxOperState,
        TmnxAdminState                          FROM ALCATEL-IND1-TIMETRA-TC-MIB

        timetraSRMIBModules, tmnxSRObjs,
        tmnxSRNotifyPrefix, tmnxSRConfs         FROM ALCATEL-IND1-TIMETRA-GLOBAL-MIB
        ;
        
tmnxChassisMIBModule MODULE-IDENTITY
        LAST-UPDATED    "0801010000Z"
        ORGANIZATION    "Alcatel"
        CONTACT-INFO    
            "Alcatel 7x50 Support
             Web: http://www.alcatel.com/comps/pages/carrier_support.jhtml"
        DESCRIPTION
        "This document is the SNMP MIB module to manage and provision the 
        hardware components of the Alcatel 7x50 device.
        
        Copyright 2003-2008 Alcatel-Lucent.  All rights reserved.
        Reproduction of this document is authorized on the condition that
        the foregoing copyright notice is included.

        This SNMP MIB module (Specification) embodies Alcatel's
        proprietary intellectual property.  Alcatel retains 
        all title and ownership in the Specification, including any 
        revisions.

        Alcatel grants all interested parties a non-exclusive 
        license to use and distribute an unmodified copy of this 
        Specification in connection with management of Alcatel 
        products, and without fee, provided this copyright notice and 
        license appear on all copies.

        This Specification is supplied 'as is', and Alcatel 
        makes no warranty, either express or implied, as to the use, 
        operation, condition, or performance of the Specification."

--
--  Revision History
--
        REVISION        "0801010000Z"   
        DESCRIPTION     "Rev 6.0                01 Jan 2008 00:00
                         6.0 release of the TIMETRA-CHASSIS-MIB."

        REVISION        "0701010000Z"   
        DESCRIPTION     "Rev 5.0                01 Jan 2007 00:00
                         5.0 release of the TIMETRA-CHASSIS-MIB."

        REVISION        "0603160000Z"   
        DESCRIPTION     "Rev 4.0                16 Mar 2006 00:00
                         4.0 release of the TIMETRA-CHASSIS-MIB."

        REVISION        "0508310000Z"   
        DESCRIPTION     "Rev 3.0                31 Aug 2005 00:00
                         3.0 release of the TIMETRA-CHASSIS-MIB."

        REVISION        "0501240000Z"   
        DESCRIPTION     "Rev 2.1                24 Jan 2005 00:00
                        2.1 release of the TIMETRA-CHASSIS-MIB."

        REVISION        "0401150000Z"
        DESCRIPTION     "Rev 2.0                15 Jan 2004 00:00 
                         2.0 release of the TIMETRA-CHASSIS-MIB."

        REVISION        "0308150000Z"
        DESCRIPTION     "Rev 1.2                15 Aug 2003 00:00 
                         1.2 release of the TIMETRA-CHASSIS-MIB."

        REVISION        "0301200000Z"
        DESCRIPTION     "Rev 1.0                20 Jan 2003 00:00 
                         Release 1.0 of the TIMETRA-HW-MIB."

        REVISION        "0008140000Z"
        DESCRIPTION     "Rev 0.1                14 Aug 2000 00:00 
                         Initial version of the TIMETRA-HW-MIB."

        ::= { timetraSRMIBModules 2 }


--  sub-tree for managed objects, and for each functional area
tmnxHwObjs            OBJECT IDENTIFIER ::= { tmnxSRObjs 2 }
        tmnxChassisObjs         OBJECT IDENTIFIER ::= { tmnxHwObjs 1 }
        tmnxSlotObjs            OBJECT IDENTIFIER ::= { tmnxHwObjs 2 }
        tmnxCardObjs            OBJECT IDENTIFIER ::= { tmnxHwObjs 3 }
--      tmnxPortObjs            OBJECT IDENTIFIER ::= { tmnxHwObjs 4 }
--      tmnxPppObjs             OBJECT IDENTIFIER ::= { tmnxHwObjs 5 }

tmnxChassisNotificationObjects  OBJECT IDENTIFIER ::= { tmnxHwObjs 6 }
-- tmnxPortNotificationObjects     OBJECT IDENTIFIER ::= { tmnxHwObjs 7 }
tmnxChassisAdminObjects         OBJECT IDENTIFIER ::= { tmnxHwObjs 8 }
-- tmnxFRObjs                      OBJECT IDENTIFIER ::= { tmnxHwObjs 9 }
-- tmnxQosAppObjs                  OBJECT IDENTIFIER ::= { tmnxHwObjs 10 }
-- tmnxATMObjs                     OBJECT IDENTIFIER ::= { tmnxHwObjs 11 }

tmnxHwNotification          OBJECT IDENTIFIER ::= { tmnxSRNotifyPrefix 2 }
        tmnxChassisNotifyPrefix      OBJECT IDENTIFIER ::= { tmnxHwNotification 1} 
               tmnxChassisNotification      OBJECT IDENTIFIER ::= { tmnxChassisNotifyPrefix 0 } 
        --  tmnxPortNotifyPrefix   OBJECT IDENTIFIER ::= { tmnxHwNotification 2 }
                --  tmnxPortNotification    OBJECT IDENTIFIER ::= { tmnxPortNotifyPrefix 0 }
        --  tmnxPppNotifyPrefix     OBJECT IDENTIFIER ::= { tmnxHwNotification 3 }
                --  tmnxPppNotification     OBJECT IDENTIFIER ::= { tmnxPppNotifyPrefix 0 }
        --  tAtmNotifyPrefix       OBJECT IDENTIFIER ::= { tmnxSrNotifyPrefix 27 } 
                --  tAtmNotifications       OBJECT IDENTIFIER ::= { tAtmNotifyPrefix 0 } 

    
tmnxHwConformance           OBJECT IDENTIFIER ::= { tmnxSRConfs 2 }
    tmnxChassisConformance      OBJECT IDENTIFIER ::= { tmnxHwConformance 1 }
--  tmnxPortConformance         OBJECT IDENTIFIER ::= { tmnxHwConformance 2 }
--  tmnxPppConformance          OBJECT IDENTIFIER ::= { tmnxHwConformance 3 }

--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      ALCATEL-IND1-TIMETRA-CHASSIS-MIB textual conventions
--

--
--      TmnxAlarmState
--
TmnxAlarmState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxAlarmState is an enumerated integer whose value indicates
         the current alarm state of a physical or logical component in the
         Alcatel 7x50 SR series system."
    SYNTAX  INTEGER {
                unknown (0),
                alarmActive (1),
                alarmCleared (2)
            }

--
--      TmnxChassisIndex
--
TmnxChassisIndex ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxChassisIndex is a unique index that identifies a chassis
         within an Alcatel 7x50 system.  Note that initial releases will 
         support only one chassis in a system."
    SYNTAX      INTEGER (1..32)

--
--      TmnxHwIndex
--
TmnxHwIndex ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxHwIndex is a unique integer index that identifies an
         Alcatel 7x50 SR series manufactured hardware component, such as 
         an IOM, CPM, Fabric or MDA card."
    SYNTAX      Integer32 (1..2147483647)

TmnxHwIndexOrZero ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxHwIndexOrZero is a unique integer index that identifies an
         Alcatel 7x50 SR series manufactured hardware component, such as an
         IOM, CPM, Fabric or MDA card. Also TmnxHwIndexOrZero can be zero."
    SYNTAX      Integer32 (0..2147483647)

--
--      TmnxHwClass
--
TmnxHwClass ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "TmnxHwClass is an enumerated integer that identifies the general
         hardware type of a component in the tmnxHwTable."
    SYNTAX  INTEGER {
                other (1),
                unknown (2),
                chassis (3),
                container (4),
                powerSupply (5),
                fan (6),
                sensor (7),
                ioModule (8),
                cpmModule (9),
                fabricModule (10),
                mdaModule (11),
                flashDiskModule (12),
                port (13),
                mcm (14),
                ccm (15)
            }   

--
--      TmnxCardType
--
TmnxCardType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxCardType data type is a bit-mask field that describes the 
         various Alcatel 7x50 SR series card types.  A TmnxCardType bit 
         value specifies the index value for the entry in the 
         tmnxCardTypeTable used to identify a specific type of card 
         manufactured by Alcatel.   
         
         When multiple bits are set, it can be used to identify a set or 
         list of card types used in the tmnxCardTable and tmnxCpmCardTable to
         indicate supported or allowed cards within a specific chassis slot.   
         Some example card types might be:

                    sfm-400g  -- 400g CPM/SF module
                    sfm-200g  -- 200g CPM/SF module
                    sfm-100g  -- 100g CPM/SF module
                    iom-20g   -- 2 x 10-Gig MDA IOM Card
        "
    SYNTAX  Unsigned32

--
--      TmnxChassisType
--
TmnxChassisType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxChassisType data type is an integer that specifies 
         the index value for the entry in the tmnxChassisTypeTable used to 
         identify a specific type of chassis backplane manufactured 
         by Alcatel."
    SYNTAX  Unsigned32
 
--
--      TmnxDeviceState
--
TmnxDeviceState ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxDeviceState data type is an enumerated integer that 
         describes the values used to identify states of chassis 
         components such as fans and power supplies."
    SYNTAX  INTEGER {
                deviceStateUnknown (1),
                deviceNotEquipped (2),
                deviceStateOk (3),
                deviceStateFailed (4),
                deviceStateOutOfService (5)
            }

--
--      TmnxLEDState
--
TmnxLEDState ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxLEDState data type is an enumerated integer that 
         describes the values used to identify state LEDs on Alcatel
         7x50 SR series cards."
    SYNTAX  INTEGER {
                ledOff          (1),
                ledRed          (2),
                ledAmber        (3),
                ledYellow       (4),
                ledGreen        (5),
                ledAmberBlink   (6),
                ledYellowBlink  (7),
                ledGreenBlink   (8)
            }

--
--      TmnxMdaType
--
TmnxMdaType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxMdaType data type is an integer that used to identify the 
         kind of Media Dependent Adapter (MDA) installed on a card.  

         The value of TmnxMdaType corresponds to the bit number indicated by
         TmnxMDASuppType.

         A TmnxMdaType value specifies the index value for the entry in the 
         tmnxMdaTypeTable used to identify a specific type of MDA 
         manufactured by Alcatel."
    SYNTAX      Unsigned32

--
--      TmnxMDASuppType
--
TmnxMDASuppType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxMDASuppType data type is a SNMP BIT that is used to identify
         the kind of Media Dependent Adapter (MDA) supported on a card.
         
         When multiple bits are set, it can be used to identify a set or list 
         of supported MDAs within a specific card slot. The MDA types are defined
         in the tmnxMdaTypeTable."
    SYNTAX      BITS {
                    invalid-MDA-type (0),
                    unassigned (1),
                    supp-MDA-type-2 (2),
                    supp-MDA-type-3 (3),
                    supp-MDA-type-4 (4),
                    supp-MDA-type-5 (5),
                    supp-MDA-type-6 (6),
                    supp-MDA-type-7 (7),
                    supp-MDA-type-8 (8),
                    supp-MDA-type-9 (9),
                    supp-MDA-type-10 (10),
                    supp-MDA-type-11 (11),
                    supp-MDA-type-12 (12),
                    supp-MDA-type-13 (13),
                    supp-MDA-type-14 (14),
                    supp-MDA-type-15 (15),
                    supp-MDA-type-16 (16),
                    supp-MDA-type-17 (17),
                    supp-MDA-type-18 (18),
                    supp-MDA-type-19 (19),
                    supp-MDA-type-20 (20),
                    supp-MDA-type-21 (21),
                    supp-MDA-type-22 (22),
                    supp-MDA-type-23 (23),
                    supp-MDA-type-24 (24),
                    supp-MDA-type-25 (25),
                    supp-MDA-type-26 (26),
                    supp-MDA-type-27 (27),
                    supp-MDA-type-28 (28),
                    supp-MDA-type-29 (29),
                    supp-MDA-type-30 (30),
                    supp-MDA-type-31 (31),
                    supp-MDA-type-32 (32),
                    supp-MDA-type-33 (33),
                    supp-MDA-type-34 (34),
                    supp-MDA-type-35 (35),
                    supp-MDA-type-36 (36),
                    supp-MDA-type-37 (37),
                    supp-MDA-type-38 (38),
                    supp-MDA-type-39 (39),
                    supp-MDA-type-40 (40),
                    supp-MDA-type-41 (41),
                    supp-MDA-type-42 (42),
                    supp-MDA-type-43 (43),
                    supp-MDA-type-44 (44),
                    supp-MDA-type-45 (45),
                    supp-MDA-type-46 (46),
                    supp-MDA-type-47 (47)
                }
--
--      TmnxMDAChanType
--
TmnxMDAChanType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxMDAChanType data type indicates the type of channel that
         can be created on an MDA."
    SYNTAX  INTEGER {
                unknown (0),
                sonetSts768(1),
                sonetSts192(2),
                sonetSts48(3),
                sonetSts12(4),
                sonetSts3(5),
                sonetSts1(6),
                sdhTug3(7),
                sonetVtg(8),
                sonetVt15(9),
                sonetVt2(10),
                sonetVt3(11),
                sonetVt6(12),
                pdhTu3(13),
                pdhDs3(14),
                pdhE3(15),
                pdhDs1(16),
                pdhE1(17),
                pdhDs0Grp(18)
            }

--
--      TmnxCcmType
--
TmnxCcmType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxCcmType data type is bit-mask field that describes
         the values used to identify the kind of Chassis Control 
         module (CCM) installed on the chassis.  A TmnxCcmType bit
         value specifies the index value for the entry in the
         tmnxCcmTypeTable used to identify a specific type of CCM
         manufactured by Alcatel.  When multiple bits are set, it can
         be used to identify a set or list of CCM types used in the
         tmnxCcmTable to indicate supported CCMs within a specific
         chassis slot.  Some example CCM types are:

                    unknown            -- unknown/uninstalled
                    ccm-v1             -- Chassis Control Module version 1
                    
        "
    SYNTAX      Unsigned32
    
--
--      TmnxMcmType
--
TmnxMcmType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The TmnxMcmType data type is bit-mask field that describes
         the values used to identify the kind of MDA Carrier 
         module (MCM) installed on the chassis.  A TmnxMcmType bit
         value specifies the index value for the entry in the
         tmnxMcmTypeTable used to identify a specific type of MCM
         manufactured by Alcatel.  When multiple bits are set, it can
         be used to identify a set or list of MCM types used in the
         tmnxMcmTable to indicate supported MCMs within a specific
         card slot.  Some example MCM types are:

                    unknown            -- unknown/uninstalled
                    mcm-v1             -- MDA Carrier Module version 1
                    
        "
    SYNTAX      Unsigned32

--
--      TmnxSlotNum
--
TmnxSlotNum ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxSlotNum data type is an integer that specifies a slot in 
         an Alcatel 7x50 SR series chassis."
    SYNTAX  INTEGER (1..128)

TmnxSlotNumOrZero ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxSlotNumOrZero data type is an integer that specifies a
         slot in an Alcatel 7x50 SR series chassis or zero."
    SYNTAX  INTEGER (0..128)

--
--      TmnxPortAdminStatus
--
TmnxPortAdminStatus ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The desired administrative status of this port."
    SYNTAX      INTEGER {
                    noop (1),
                    inService (2),
                    outOfService (3),
                    diagnose (4)
                }

--
--      TmnxChassisMode
--
TmnxChassisMode ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxChassisMode data type is an enumerated integer that 
         specifies the values used to identify which set of scaling numbers
         and features are effective for an Alcatel 7x50 SR series chassis.
         'modeA' corresponds to the scaling and feature set on the existing
         iom-20g. 'modeB' corresponds to the scaling and features that come
         with iom-20g-b. 'modeC' corresponds to the scaling and features that
         come with iom2-20g."
    SYNTAX  INTEGER {
                modeA   (1),
                modeB   (2),
                modeC   (3)
            }

--
--      TmnxSETSRefSource
--
TmnxSETSRefSource ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxSETSRefSource data type is an enumerated integer that
         describes the values used to identify the Synchronous Equipment
         Timing Subsystem (SETS) timing reference source."
    SYNTAX  INTEGER {
                reference1 (1),
                reference2 (2),
                bits (3)
            }

--
--      TmnxSETSRefQualified
--
TmnxSETSRefQualified ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxSETSRefQualified data type is an enumerated integer that
         describes the values used to identify whether the reference is
         'qualified' or 'not-qualified' for use by SETS."
    SYNTAX  INTEGER {
                qualified (1),
                not-qualified (2)
            }

--
--      TmnxSETSRefAlarm
--
TmnxSETSRefAlarm ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxSETSRefAlarm data type is a bitmap that describes the values
         used to identify the alarms on the SETS timing reference source if
         the source is 'not-qualified'.
         
         'los'    - loss of signal
         'oof'    - out of frequency range
         'oopir'  - out of pull in range."
    SYNTAX  BITS {
                los (0),
                oof (1),
                oopir (2)
            }

--
--      TmnxBITSIfType
--
TmnxBITSIfType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxBITSIfType data type is an enumerated integer that describes
         the values used to identify the interface and framing types of a BITS
         (Building Integrated Timing Supply) interface." 
    SYNTAX  INTEGER {
                    none (0),
                    t1-esf (1),
                    t1-sf (2),
                    e1-pcm30crc (3),
                    e1-pcm31crc (4)
            }

--
--      TmnxCcagId
--
TmnxCcagId ::= TEXTUAL-CONVENTION
    STATUS               current
    DESCRIPTION
        "TmnxCcagId is an integer specifying the cross connect aggregation
         group. The value '0' is used when a ccag is not defined and is not
         a valid value when TmnxCcagId is used as an index."
    SYNTAX Integer32 (0|1..8)

--
--      TmnxCcagRate
--
TmnxCcagRate ::= TEXTUAL-CONVENTION
    STATUS              current
    DESCRIPTION         
        "TmnxCcagRate is an integer specifying the rate for a CCAG member in Kbps.
         The range of TmnxCcagRate is from 0 Kbps to 100Gbps. The value '-1' is used
         for maximum rate available."
    SYNTAX  Integer32 (-1|0..*********)

--
--      TmnxCcagRateOption
--
TmnxCcagRateOption ::= TEXTUAL-CONVENTION
    STATUS              current
    DESCRIPTION
        "TmnxCcagRateOption specifies how the defined rate is
         applied to active Cross Connect Adaptors (CCAs).
         aggregate (1) -  the defined rate is equally divided among the CCAs in
                          the CCAG member list based on the number of active 
                          CCAs.
         cca       (2) -  the defined rate is applied to all CCAs in the CCAG 
                          member list."
    SYNTAX  INTEGER {
                aggregate (1),
                cca       (2)
            }


--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      ALCATEL-IND1-TIMETRA-CHASSIS-MIB at a glance
-- 
--  timetra (enterprises 6527)
--    timetraProducts (3)
--      tmnxSRMIB (1)
--                tmnxSRConfs (1)
--                tmnxSRObjs (2)
--                      tmnxHwObjs (tmnxSRObjs 2)
--                tmnxSRNotifyPrefix (3)
--
--      
--  tmnxChassisObjs (tmnxHwObjs 1)
--        tmnxChassisTotalNumber (1)
--        tmnxChassisLastChange (2)
--        tmnxChassisTable (3)
--        tmnxChassisFanTable (4)
--        tmnxChassisPowerSupplyTable (5)
--        tmnxChassisTypeTable (6)
--        tmnxChassisHwLastChange (7)
--        tmnxHwTable (8)
--        tmnxHwContainsTable (9)
--        tmnxCcmTable (10)
--        tmnxCcmTypeTable (11)
--
--  tmnxSlotObjs (2)    - not used
--
--  tmnxCardObjs (3)
--        tmnxCardLastChange (1)
--        tmnxCardTable (2)
--        tmnxCpmCardLastChange (3)
--        tmnxCpmCardTable (4)
--        tmnxFabricLastChange (5)
--        tmnxFabricTable (6)
--        tmnxCpmFlashTable (7)
--        tmnxMDATable (8)
--        tmnxCardTypeTable (9)
--        tmnxMdaTypeTable (10)
--        tmnxSyncIfTimingTable (11)
--        tmnxCcagTable (12)
--        tmnxCcagPathTable (13)
--        tmnxCcagPathCcTable (14)
--        tmnxMcmTable (15)
--        tmnxMcmTypeTable (16)
--        tmnxMdaClockDomainTable (17)
--
--  tmnxPortObjs (4)
--  tmnxPppObjs (5)
--  tmnxChassisNotificationObjects (6)
--  tmnxPortNotificationObjects (7)
--  tmnxChassisAdminObjects (8)
--  tmnxFRObjs (9)
--  tmnxQosAppObjs (10)
--

--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      The Chassis Group
--


tmnxChassisTotalNumber OBJECT-TYPE
    SYNTAX      INTEGER (1..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of chassis installed in this system.  For the first 
         release of the Alcatel 7x50 series product, there is only 
         1 chassis per system.  A multi-chassis system model is supported 
         to allow for future product expansion."
    ::= { tmnxChassisObjs 1 }
        
tmnxChassisLastChange   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime when the tmnxChassisTable was last changed."
    ::= { tmnxChassisObjs 2 }
        
tmnxChassisTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxChassisEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The chassis table has an entry for each chassis in the system."
    ::= { tmnxChassisObjs 3 }

tmnxChassisEntry    OBJECT-TYPE
    SYNTAX      TmnxChassisEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a chassis in the system.  The agent 
         creates the row for the first chassis in the system, with 
         tmnxChassisIndex = 1, which is auto-discovered by the active CPM 
         card.  Additional chassis entries can be created and deleted via 
         SNMP SET operations.  Creation requires a SET request containing 
         at least tmnxChassisAdminMode and tmnxChassisRowStatus.  Note 
         that the first Alcatel 7x50 series product release does not 
         support multiple chassis, therefore there will not be more 
         than one row entry in this table; attempts to create additional 
         rows in this table will be denied."
    INDEX       { tmnxChassisIndex }
    ::= { tmnxChassisTable 1 }

TmnxChassisEntry ::=
    SEQUENCE {
        tmnxChassisIndex               TmnxChassisIndex,
        tmnxChassisRowStatus           RowStatus,
        tmnxChassisName                TNamedItemOrEmpty,
        tmnxChassisType                TmnxChassisType,
        tmnxChassisLocation            TItemDescription,
        tmnxChassisCoordinates         TItemDescription,
        tmnxChassisNumSlots            Unsigned32,
        tmnxChassisNumPorts            Unsigned32,
        tmnxChassisNumPwrSupplies      Unsigned32,
        tmnxChassisNumFanTrays         Unsigned32,
        tmnxChassisNumFans             Unsigned32,
        tmnxChassisCriticalLEDState    TmnxLEDState,
        tmnxChassisMajorLEDState       TmnxLEDState,
        tmnxChassisMinorLEDState       TmnxLEDState,
        tmnxChassisBaseMacAddress      MacAddress,
        tmnxChassisCLLICode            DisplayString,
        tmnxChassisReboot              TmnxActionType,
        tmnxChassisUpgrade             TmnxActionType,
        tmnxChassisAdminMode           TmnxChassisMode,
        tmnxChassisOperMode            TmnxChassisMode,        
        tmnxChassisModeForce           TmnxActionType,
        tmnxChassisUpdateWaitTime      Unsigned32,
        tmnxChassisUpdateTimeLeft      Unsigned32,
        tmnxChassisOverTempState       INTEGER
    }

tmnxChassisIndex    OBJECT-TYPE
    SYNTAX      TmnxChassisIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this chassis in the system. 
         The first release of the product only supports a single chassis 
         in the system."
    ::= { tmnxChassisEntry 1 }

tmnxChassisRowStatus        OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The row status.  The creation or deletion of a chassis entry causes 
         creation or deletion of corresponding entries in the tmnxCardTable with 
         the same tmnxChassisIndex value.  Note, the agent will disallow 
         chassis deletion if its entries in the card table have not first been 
         put into the proper state for removal.  The row entry for 
         tmnxChassisIndex equal 1 cannot be deleted."
    ::= { tmnxChassisEntry 2 }

tmnxChassisName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The administrative name assigned this chassis.  Setting 
         tmnxChassisName to the empty string, ''H, resets tmnxChassisName
         to the TiMOS default value."
    DEFVAL      { ''H }
    ::= { tmnxChassisEntry 3 }


tmnxChassisType     OBJECT-TYPE
    SYNTAX      TmnxChassisType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The type of chassis used in this Alcatel 7x50 system.  The value of
         tmnxChassisType is the tmnxChassisTypeIndex for the entry in the 
         tmnxChassisTypeTable that represents the Alcatel 7x50 SR series
         chassis model for this system.  Chassis types are distinguished 
         by their backplane type."
    ::= { tmnxChassisEntry 4 }

tmnxChassisLocation OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "A user supplied string that indicates the on site location of this 
         chassis.  This could used for a Common Language Location Identifier,
         CLLI, code string if desired. 
         
         A CLLI code is an 11-character standardized geographic identifier that
         uniquely identifies the geographic location of places and certain 
         functional categories of equipment unique to the telecommunications 
         industry.

         All valid CLLI codes are created, updated and maintained in the 
         Central Location Online Entry System (CLONES) database."
    DEFVAL      { ''H }
    ::= { tmnxChassisEntry 5 }

tmnxChassisCoordinates   OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "A user supplied string that indicates the Global Positioning
         System (GPS) coordinates for the location of this chassis.

            N 45 58 23, W 34 56 12
            N37 37' 00 latitude, W122 22' 00 longitude
            N36*39.246'  W121*40.121'
            
         Two-dimensional GPS positioning offers latitude and longitude
         information as a four dimensional vector:

            <Direction, hours, minutes, seconds>

         where Direction is one of the four basic values: N, S, W, E; hours
         ranges from 0 to 180 (for latitude) and 0 to 90 for longitude, and,
         finally, minutes and seconds range from 0 to 60.

         Thus <W, 122, 56, 89> is an example of longitude and <N, 85, 66, 43>
         is an example of latitude.

         Four bytes of addressing space (one byte for each of the four
         dimensions) are necessary to store latitude and four bytes are also
         sufficient to store longitude. Thus eight bytes total are necessary
         to address the whole surface of earth with precision down to 0.1
         mile!  Notice that if we desired precision down to 0.001 mile (1.8
         meters) then we would need just five bytes for each component, or ten
         bytes together for the full address (as military versions provide)."
        DEFVAL      { ''H }
    ::= { tmnxChassisEntry 6 }

tmnxChassisNumSlots OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of slots in this chassis that are available for plug-in 
         cards.  This includes both fabric and IOM cards"
    ::= { tmnxChassisEntry 7 }

tmnxChassisNumPorts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of ports currently installed in this chassis.  
         This count does not include the Ethernet ports on the CPM cards 
         that are used for management access."
    ::= { tmnxChassisEntry  8 }

tmnxChassisNumPwrSupplies   OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of power supplies installed in this chassis."
    ::= { tmnxChassisEntry 9 }
        
tmnxChassisNumFanTrays  OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of fan trays installed in this chassis."
    ::= { tmnxChassisEntry 10 }

tmnxChassisNumFans  OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total number of fans installed in this chassis."
    ::= { tmnxChassisEntry 11 }
       
tmnxChassisCriticalLEDState    OBJECT-TYPE
    SYNTAX      TmnxLEDState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current state of the Critical LED in this chassis."
    ::= { tmnxChassisEntry 12 }

tmnxChassisMajorLEDState    OBJECT-TYPE
    SYNTAX      TmnxLEDState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current state of the Major LED in this chassis."
    ::= { tmnxChassisEntry 13 }

tmnxChassisMinorLEDState    OBJECT-TYPE
    SYNTAX      TmnxLEDState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current state of the Minor LED in this chassis."
    ::= { tmnxChassisEntry 14 }

tmnxChassisBaseMacAddress   OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The base chassis ethernet MAC address.  Special purpose MAC
         addresses used by the system software are constructed as
         offsets from this base address."
    ::= { tmnxChassisEntry 15 }

tmnxChassisCLLICode   OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "A Common Language Location Identifier (CLLI) code is an 11-character 
         standardized geographic identifier that uniquely identifies the 
         geographic location of places and certain functional categories of 
         equipment unique to the telecommunications industry.
         If the set on this object specifies a non-null string, the string will 
         automatically be truncated or padded(with spaces) to 11 characters."
    ::= { tmnxChassisEntry 16 }

tmnxChassisReboot      OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Setting this tmnxChassisReboot to 'doAction' causes a soft-reboot 
         of the entire chassis including all the CPM and IOM cards.
         
         Note that the reboot action is likely to occur before the SNMP
         SET response can be transmitted."
    DEFVAL { notApplicable }
    ::= { tmnxChassisEntry 17 }

tmnxChassisUpgrade      OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Setting this tmnxChassisUpgrade to 'doAction' causes an upgrade 
         of all firmware and a reboot of the entire chassis including all 
         the CPM and IOM cards.  
         
         CAUTION: This upgrade and reboot may take several minutes to 
         complete.  The chassis MUST NOT be reset or powered down, 
         nor cards inserted or removed, during this process.  Any of
         these prohibited actions may cause the cards to be rendered
         inoperable.

         tmnxChassisUpgrade and tmnxChassisReboot must be set
         together in the same SNMP SET request PDU or else the SET request
         will fail with an inconsistentValue error.
         
         Note that the reboot action is likely to occur before the SNMP
         SET response can be transmitted."
    DEFVAL { notApplicable }
    ::= { tmnxChassisEntry 18 }

tmnxChassisAdminMode     OBJECT-TYPE
    SYNTAX      TmnxChassisMode
    MAX-ACCESS  read-create
    STATUS      current
    
    DESCRIPTION
        "The value of tmnxChassisAdminMode specifies the scaling and feature 
         set for the IOM cards in the chassis. Setting this variable to
         'modeA' causes all IOM cards in the chassis to use the scaling 
         and feature sets supported on the iom-20g card type. Setting
         tmnxChassisAdminMode to 'modeB' corresponds to the scaling and feature
         sets supported on the iom-20g-b card type. 'modeC' corresponds to the
         scaling and feature sets supported on the iom2-20g card type.
 
         An attempt to change the value of tmnxChassisAdminMode from 'modeA'
         to 'modeB' (upgrade) or 'modeC' (upgrade) without also setting 
         tmnxChassisModeForce to a value of 'doAction' in the same SNMP SET 
         request, will fail with an inconsistentValue error if there are any IOM 
         cards in the chassis with a value of 'iom-20g' for tmnxCardAssignedType.      
        
         An attempt to change the value of tmnxChassisAdminMode from 'modeB'
         to 'modeC' (upgrade) without also setting tmnxChassisModeForce to
         a value of 'doAction' in the same SNMP SET request, will fail with an
         inconsistentValue error if there are any IOM cards in the chassis with
         a value of 'iom-20g-b' for tmnxCardAssignedType.
 
         'modeB' scaling and feature sets cannot be supported on the iom-20g
         card. 'modeC' scaling feature set cannot be supported on either on
         the iom-20g or the iom-20g-b."
                       
    DEFVAL { modeA }
    ::= { tmnxChassisEntry 19 }

tmnxChassisOperMode OBJECT-TYPE
    SYNTAX      TmnxChassisMode
    MAX-ACCESS  read-only
    STATUS      current
    
    DESCRIPTION
        "The value of tmnxChassisOperMode indicates the operational scaling 
         and feature set for the IOM cards in the chassis. Changing the value
         of tmnxChassisAdminMode from 'modeB' to 'modeA' (downgrade) will 
         result in tmnxChassisAdminMode indicating 'modeA' while 
         tmnxChassisOperMode indicates 'modeB' untill the configuration is 
         saved and the system rebooted, at which point, the actual downgrade 
         will take effect.

         Changing the value of tmnxChassisAdminMode from 'modeC' to either
         'modeB' (downgrade) or 'modeA' (downgrade) will result in
         tmnxChassisAdminMode indicating 'modeB' or 'modeA' respectively while
         tmnxChassisOperMode indicates 'modeC' untill the configuration is
         saved and the system rebooted, at which point, the actual downgrade
         will take effect.

         'modeB' scaling and feature sets cannot be supported on the iom-20g
         card. 'modeC' scaling feature set cannot be supported on either on
         the iom-20g or the iom-20g-b."
    ::= { tmnxChassisEntry 20 } 

tmnxChassisModeForce     OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Setting tmnxChassisModeForce to a value of 'doAction' in the
         same SNMP SET request where tmnxChassisAdminMode is set to 'modeB'
         allows the chassis to be upgraded to 'modeB' even if there are 
         IOM cards in the chassis with a value of 'iom-20g' for
         tmnxCardAssignedType.
        
         Setting tmnxChassisModeForce to a value of 'doAction' in the
         same SNMP SET request where tmnxChassisAdminMode is set to 'modeC'
         allows the chassis to be upgraded to 'modeC' even if there are
         IOM cards in the chassis with a value of 'iom2-20g' for
         tmnxCardAssignedType.           
 
         An attempt to set tmnxChassisModeForce to 'doAction' without
         also setting tmnxChassisAdminMode, in the same SNMP SET request 
         will fail with an inconsistentValue error.
       
         -----------------------------------------------------------------------
               Mode change   | Assigned card | Force   |  Result
         -----------------------------------------------------------------------
         a to b (upgrade)  |    iom-20g    | not-set |  error
         a to b (upgrade)  |    iom-20g    |   set   |  mode b with warnings
         a to b (upgrade)  |    iom-20g-b  | not-set |  mode b
         a to b (upgrade)  |    iom-20g-b  |   set   |  mode b
         a to c (upgrade)  |    iom-20g    | not-set |  error
         a to c (upgrade)  |    iom-20g    |   set   |  mode c with warnings
         a to c (upgrade)  |    iom2-20g   | not-set |  mode c
         a to c (upgrade)  |    iom2-20g   |   set   |  mode c
         b to c (upgrade)  |    iom-20g-b  | not-set |  error
         b to c (upgrade)  |    iom-20g-b  |   set   |  mode c with warnings
         b to c (upgrade)  |    iom2-20g   | not-set |  mode c
         b to c (upgrade)  |    iom2-20g   |   set   |  mode c
         b to a (downgrade)|    iom-20g    | not-set |  mode a on save and reboot
         b to a (downgrade)|    iom-20g    |   set   |  mode a on save and reboot
         b to a (downgrade)|    iom-20g-b  | not-set |  mode a on save and reboot
         b to a (downgrade)|    iom-20g-b  |   set   |  mode a on save and reboot
         c to a (downgrade)|    iom-20g    | not-set |  mode a on save and reboot
         c to a (downgrade)|    iom-20g    |   set   |  mode a on save and reboot
         c to a (downgrade)|    iom2-20g   | not-set |  mode a on save and reboot
         c to a (downgrade)|    iom2-20g   |   set   |  mode a on save and reboot
         c to b (downgrade)|    iom-20g-b  | not-set |  mode b on save and reboot
         c to b (downgrade)|    iom-20g-b  |   set   |  mode b on save and reboot
         c to b (downgrade)|    iom2-20g   | not-set |  mode b on save and reboot
         c to b (downgrade)|    iom2-20g   |   set   |  mode b on save and reboot
         -----------------------------------------------------------------------"
    DEFVAL { notApplicable }
    ::= { tmnxChassisEntry 21 }

tmnxChassisUpdateWaitTime      OBJECT-TYPE
    SYNTAX      Unsigned32 (15..600)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "The value of tmnxChassisUpdateWaitTime specifies the time to wait
         before rebooting IOM cards running older software versions following 
         a software upgrade or downgrade activity switchover. This object
         was obsoleted in release 5.0."
    DEFVAL { 15 }
    ::= { tmnxChassisEntry 22 }

tmnxChassisUpdateTimeLeft      OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Following a software upgrade or downgrade activity switchover,
         the value of tmnxChassisUpdateTimeLeft indicates the time remaining
         before IOM cards or MDAs running older software versions will be
         rebooted."
    ::= { tmnxChassisEntry 23 }

tmnxChassisOverTempState    OBJECT-TYPE
    SYNTAX      INTEGER {
                    stateOk       (1),
                    stateOverTemp (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current Over Temperature state of this chassis.
        
         stateOk         Indicates chassis is below the temperature threshold.
         stateOverTemp   Indicates chassis is above the temperature threshold.
         "
    ::= { tmnxChassisEntry 24 }

--
--  Fan Table
--

tmnxChassisFanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxChassisFanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION   
        "This table contains information about fan trays."
    ::= { tmnxChassisObjs 4 }

tmnxChassisFanEntry OBJECT-TYPE
    SYNTAX      TmnxChassisFanEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION   
        "Contains information regarding a fan tray."
    INDEX       { tmnxChassisIndex, tmnxChassisFanIndex }
    ::= { tmnxChassisFanTable 1 }

TmnxChassisFanEntry ::=
     SEQUENCE {
        tmnxChassisFanIndex         Unsigned32,
        tmnxChassisFanOperStatus    TmnxDeviceState,
        tmnxChassisFanSpeed         INTEGER
    }

tmnxChassisFanIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..31)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies a specific fan tray in the
         chassis."
    ::= { tmnxChassisFanEntry 1 }

tmnxChassisFanOperStatus OBJECT-TYPE
    SYNTAX      TmnxDeviceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION   
        "Current status of the Fan tray."
    ::= { tmnxChassisFanEntry 2 }

tmnxChassisFanSpeed     OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (1),
                    halfSpeed (2),
                    fullSpeed (3)
                }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The value of tmnxChassisFanSpeed indicates if the fans in this
         fan tray are running at 'halfSpeed' or 'fullSpeed'."
    ::= { tmnxChassisFanEntry 3 }

--
--  Power Supply table
--

tmnxChassisPowerSupplyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxChassisPowerSupplyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION     
        "This table contains information about power supply trays."
    ::= { tmnxChassisObjs 5 }

tmnxChassisPowerSupplyEntry OBJECT-TYPE
    SYNTAX      TmnxChassisPowerSupplyEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION     
        "Contains information regarding a power supply tray."
    INDEX  { tmnxChassisIndex, tmnxChassisPowerSupplyId }
    ::= { tmnxChassisPowerSupplyTable 1 }

TmnxChassisPowerSupplyEntry ::=
    SEQUENCE {
        tmnxChassisPowerSupplyId            Unsigned32,
        tmnxChassisPowerSupplyACStatus      TmnxDeviceState,
        tmnxChassisPowerSupplyDCStatus      TmnxDeviceState,
        tmnxChassisPowerSupplyTempStatus    TmnxDeviceState,
        tmnxChassisPowerSupplyTempThreshold Integer32,
        tmnxChassisPowerSupply1Status       TmnxDeviceState,
        tmnxChassisPowerSupply2Status       TmnxDeviceState,
        tmnxChassisPowerSupplyAssignedType  INTEGER,
        tmnxChassisPowerSupplyInputStatus   TmnxDeviceState,
        tmnxChassisPowerSupplyOutputStatus  TmnxDeviceState
    }

tmnxChassisPowerSupplyId OBJECT-TYPE
    SYNTAX      Unsigned32 (1..31)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique identifier index for a power supply tray in the chassis."
    ::= { tmnxChassisPowerSupplyEntry 1 }

tmnxChassisPowerSupplyACStatus OBJECT-TYPE
    SYNTAX      TmnxDeviceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION   
        "If the value of tmnxChassisPowerSupplyACStatus is 'deviceStateOk', 
         the input AC voltage is within range.  If the value is 
         'deviceStateFailed', an AC voltage out of range condition has been 
         detected. A value of 'deviceNotEquipped' indicates that the AC
         power supply is not present."
    ::= { tmnxChassisPowerSupplyEntry 2 }
            
tmnxChassisPowerSupplyDCStatus OBJECT-TYPE
    SYNTAX      TmnxDeviceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION   
        "If the value of tmnxChassisPowerSupplyDCStatus is 'deviceStateOk', 
         the input DC voltage is within range.  If the value is 
         'deviceStateFailed', an DC voltage out of range condition has been 
         detected. A value of 'deviceNotEquipped' indicates that the DC
         power supply is not present."
    ::= { tmnxChassisPowerSupplyEntry 3 }

tmnxChassisPowerSupplyTempStatus OBJECT-TYPE
    SYNTAX      TmnxDeviceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION   
        "If the value of tmnxChassisPowerSupplyTempStatus is 'deviceStateOk',
         the current temperature is within acceptable range. If the value is
         'deviceStateFailed', a temperature too high condition has been 
         detected."
    ::= { tmnxChassisPowerSupplyEntry 4 }

tmnxChassisPowerSupplyTempThreshold   OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "degrees celsius"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The temperature threshold for this power supply tray in degrees 
         celsius.  When the temperature raises above 
         tmnxChassisPowerSupplyTempThreshold, a 'temperature too high'
         event will be generated."
    ::= { tmnxChassisPowerSupplyEntry 5 }

tmnxChassisPowerSupply1Status OBJECT-TYPE
    SYNTAX      TmnxDeviceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION   
        "The overall status of an equipped power supply. For AC multiple power
         supplies, this represents the overall status of the first power supply
         in the tray (or shelf). For any other type, this represents the overall
         status of the power supply. If tmnxChassisPowerSupply1Status is
         'deviceStateOk', then all monitored statuses are 'deviceStateOk'. A
         value of 'deviceStateFailed' represents a condition where at least one
         monitored status is in a failed state."
    ::= { tmnxChassisPowerSupplyEntry 6 }

tmnxChassisPowerSupply2Status OBJECT-TYPE
    SYNTAX         TmnxDeviceState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION   
        "The overall status of an equipped power supply. For AC multiple power
         supplies, this represents the overall status of the second power supply
         in the tray (or shelf). For any other type, this field is unused and 
         set to 'deviceNotEquipped'. If tmnxChassisPowerSupply2Status is 
         'deviceStateOk', then all monitored statuses are 'deviceStateOk'. A 
         value of 'deviceStateFailed' represents a condition where at least one 
         monitored status is in a failed state."
    ::= { tmnxChassisPowerSupplyEntry 7 }

tmnxChassisPowerSupplyAssignedType OBJECT-TYPE
    SYNTAX         INTEGER {
                       none (0),
                       dc (1),
                       acSingle (2),
                       acMultiple (3)
                   }
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION   
        "tmnxChassisPowerSupplyAssignedType configures the type of power supply 
         for a platform. Based on the value assigned to this object, various 
         power supply monitoring signals will be interpreted. For example, if 
         a platform is provisioned to use DC power supplies, then the signal 
         that indicates an AC power supply is missing can be ignored.
         This is required for proper generation of traps and LED management."
    ::= { tmnxChassisPowerSupplyEntry 8 }

tmnxChassisPowerSupplyInputStatus OBJECT-TYPE
    SYNTAX         TmnxDeviceState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION   
        "If the value of tmnxChassisPowerSupplyInputStatus is 'deviceStateOk', 
         the input voltage of the power supply is within range.  If the value 
         is 'deviceStateFailed', an input voltage out of range condition has 
         been detected. A value of 'deviceNotEquipped' indicates that the power 
         supply is not present."
    ::= { tmnxChassisPowerSupplyEntry 9 }

tmnxChassisPowerSupplyOutputStatus OBJECT-TYPE
    SYNTAX         TmnxDeviceState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION   
        "If the value of tmnxChassisPowerSupplyOutputStatus is 'deviceStateOk', 
         the output voltage of the power supply is within range.  If the value 
         is 'deviceStateFailed', an output voltage out of range condition has 
         been detected. A value of 'deviceNotEquipped' indicates that the power 
         supply is not present."
    ::= { tmnxChassisPowerSupplyEntry 10 }

--
--  Alcatel 7x50 SR series Chassis Type Defintion Table
--
tmnxChassisTypeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxChassisTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The chassis type table has an entry for each Alcatel 7x50 SR series
         chassis model."
    ::= { tmnxChassisObjs 6 }

tmnxChassisTypeEntry    OBJECT-TYPE
    SYNTAX      TmnxChassisTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7x50 SR series Chassis model.
         Rows in this table are created by the agent at initialization and 
         cannot be created or destroyed by SNMP Get or Set requests."
    INDEX   { tmnxChassisTypeIndex }
    ::= { tmnxChassisTypeTable 1 }

TmnxChassisTypeEntry ::=
    SEQUENCE {
        tmnxChassisTypeIndex        TmnxChassisType,
        tmnxChassisTypeName         TNamedItemOrEmpty,
        tmnxChassisTypeDescription  TItemDescription,
        tmnxChassisTypeStatus       TruthValue
    }

tmnxChassisTypeIndex    OBJECT-TYPE
    SYNTAX      TmnxChassisType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique index value which identifies this type of Alcatel 
         7x50 SR series chassis model."
    ::= { tmnxChassisTypeEntry 1 }
        
tmnxChassisTypeName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The administrative name that identifies this type of Alcatel
         7x50 SR series chassis model.  This name string may be used in 
         CLI commands to specify a particular chassis model type."
    ::= { tmnxChassisTypeEntry 2 }

tmnxChassisTypeDescription  OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A detailed description of this Alcatel 7x50 SR series chassis model."
    ::= { tmnxChassisTypeEntry 3 }         

tmnxChassisTypeStatus       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxChassisTypeStatus has a value of 'true' it indicates that
         this chassis model is supported in this revision of the management
         software.  When it has a value of 'false' there is no support."
    ::= { tmnxChassisTypeEntry 4 }


--
--  Alcatel 7x50 SR series Hardware Components Table
--
tmnxHwLastChange   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime when the tmnxHwTable was last changed."
    ::= { tmnxChassisObjs 7 }
        
tmnxHwTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxHwEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxHwTable has an entry for each managed hardware component 
         in the Alcatel 7x50 SR series system's chassis.  Examples of 
         these hardware component types are IOM, Fabric, and CPM cards,
         MCM and CCM, and MDA modules.  Similar information for physical ports
         is in the tmnxPortObjs."
    ::= { tmnxChassisObjs 8 }

tmnxHwEntry       OBJECT-TYPE
    SYNTAX      TmnxHwEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7x50 SR series manufactured 
         hardware component.  Entries cannot be created and deleted via 
         SNMP SET operations.  When an entry is created in one of the 
         card tables, IOM, CPM, Fabric or MDA, a tmnxHwEntry is created 
         for the common hardware management information for that card 
         in that chassis.  When the card  is removed from the chassis, 
         its corresponding tmnxHwEntry is deleted.
         
         The tmnxHwIndex is bitmapped:
         
         | 32       25 | 24       17 | 16        9 | 8         1 |
         +-------------+-------------+-------------+-------------+
         | TmnxHwClass |   00000000  |     Slot    |     number  |
         +-------------+-------------+-------------+-------------+

         The Slot field is only used for components on cards in
         slots. It is zero for all others.

         The number field starts from 1 and indicates which component.
         E.g. Power supply 1 or 2."
         
         
    INDEX   { tmnxChassisIndex, tmnxHwIndex }
    ::= { tmnxHwTable 1 }

TmnxHwEntry ::=
    SEQUENCE {
        tmnxHwIndex                     TmnxHwIndex,
        tmnxHwID                        RowPointer,
        tmnxHwMfgString                 SnmpAdminString,
        tmnxHwMfgBoardNumber            OCTET STRING,
        tmnxHwSerialNumber              SnmpAdminString,
        tmnxHwManufactureDate           SnmpAdminString,
        tmnxHwClass                     TmnxHwClass,
        tmnxHwName                      TNamedItemOrEmpty,
        tmnxHwAlias                     TNamedItemOrEmpty,
        tmnxHwAssetID                   SnmpAdminString,
        tmnxHwCLEI                      SnmpAdminString,
        tmnxHwIsFRU                     TruthValue,
        tmnxHwContainedIn               Integer32,
        tmnxHwParentRelPos              Integer32,
        tmnxHwAdminState                INTEGER,
        tmnxHwOperState                 INTEGER,
        tmnxHwTempSensor                TruthValue,
        tmnxHwTemperature               Integer32,
        tmnxHwTempThreshold             Integer32,
        tmnxHwBootCodeVersion           DisplayString,
        tmnxHwSoftwareCodeVersion       DisplayString,
        tmnxHwSwLastBoot                DateAndTime,
        tmnxHwSwState                   INTEGER,
        tmnxHwAlarmState                TmnxAlarmState,
        tmnxHwLastAlarmEvent            RowPointer,
        tmnxHwClearAlarms               TmnxActionType,
        tmnxHwSwImageSource             INTEGER,
        tmnxHwMfgDeviations             SnmpAdminString,
        tmnxHwBaseMacAddress            MacAddress,
        tmnxHwFailureReason             DisplayString
    }

tmnxHwIndex     OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwIndex is a unique index that identifies
         common management information for Alcatel 7x50 SR series
         manufactured hardware components within the specified chassis."
    ::= { tmnxHwEntry 1 }

tmnxHwID        OBJECT-TYPE
    SYNTAX      RowPointer
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwID is an object identifier that points to
         the table and row entry with additional management information
         specific to this hardware component's class."
    ::= { tmnxHwEntry 2 }

tmnxHwMfgString     OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(0..253))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxHwMfgString contains unspecified Alcatel 7x50 SR series
         manufacturing information and includes the Alcatel vendor information."
    ::= { tmnxHwEntry 3 }

tmnxHwMfgBoardNumber    OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tmnxHwMfgBoardNumber contains the part number information."
    ::= { tmnxHwEntry 4 }

tmnxHwSerialNumber        OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The unique Alcatel 7x50 SR series serial number of the hardware 
         component."
    ::= { tmnxHwEntry 5 }

tmnxHwManufactureDate     OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The manufacturing date of the hardware component in 'mmddyyyy' 
         ascii format."
    ::= { tmnxHwEntry 6 }

tmnxHwClass     OBJECT-TYPE
    SYNTAX      TmnxHwClass
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwClass  indicates the general hardware type of this
         component.  If no appropriate enumeration exists for this hardware 
         component then the value 'other (1)' is used.  If the agent cannot 
         identify this hardware component then the value 'unknown (2)' is 
         used."
    ::= { tmnxHwEntry 7 }

tmnxHwName      OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwName is the name of the component as assigned
         by the system software itself and is suitable for use in CLI commands.
         This may be a text name such as 'console' or a port ID such as '2/2'.

         If there is no predefined name then a zero length string is returned.

         Note that the value of tmnxHwName for two component entries will
         be the same if the CLI does not distinguish between them, e.g.  the
         chassis slot-1 and the card in slot-1."
    ::= { tmnxHwEntry 8 }

tmnxHwAlias        OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwAlias is the administrative name assigned to this
         hardware component by the CLI user or network manager.  It is saved
         across re-initializations and reboots of the system."
    DEFVAL      { ''H }
    ::= { tmnxHwEntry 9 }

tmnxHwAssetID   OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwAssetID is an administratively assigned asset
         tracking identifier for this hardware component.  It is saved across
         re-initializations and reboots of the system.  If no asset tracking
         information is associated with this hardware component, a zero-length
         string is returned to an SNMP get request.

         Some hardware components do not have asset tracking identifiers.
         Components for which tmnxHwIsFRU has a value of 'false' do not
         need their own unique asset tracking identifier.  In this case, the
         agent denies write access to this object and returns a zero-length
         string to an SNMP get request."
    DEFVAL      { ''H }
    ::= { tmnxHwEntry 10 }

tmnxHwCLEI          OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(10))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Common Language Equipment Identifier, CLEI, code is a unique 
         10-character identifier, that is fixed by the manufacturer.  It 
         consists of ten alphanumeric characters.  The first seven characters 
         present a concise summary of an equipment entity's circuit or 
         transport capabilities, e.g., functional, electrical, bandwidth, etc. 
         CLEI codes for plug-in or portable equipment with the same first 
         seven characters (CLEI-7) are considered bidirectionally 
         interchangeable and group under a G level record.  Most licensees 
         plug-in inventories and records are controlled at the group level. 
         The eighth character denotes the reference source used for coding 
         the item, and the last two characters denote manufacturing vintage 
         or version, and other complemental information.

         A ten-character CLEI code that is developed for a specific piece of
         equipment is unique within the CLEI code universe and is used in A 
         level records; the code is not assigned to any other equipment piece. 
         Equipment is coded to a first or major application. When the same 
         equipment is usable in another application or system, it is not 
         recorded nor are additional codes developed for that purpose."
    REFERENCE
        "Bellcore (Telcordia Technologies) GR-485."
    ::= { tmnxHwEntry 11 }

tmnxHwIsFRU     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwIsFRU indicates whether or not this hardware
         component is a Field Replaceable Unit (FRU) or not.  Those components
         that are permanently contained within a FRU have a value of 'false'."
    ::= { tmnxHwEntry 12 }

tmnxHwContainedIn   OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwContainedIn is the tmnxHwIndex value for the
         row entry of the hardware component that contains this component.  
         A value of zero indicates that this component is not contained in any 
         other component."
    ::= { tmnxHwEntry 13 }

tmnxHwParentRelPos      OBJECT-TYPE
    SYNTAX      Integer32 (-1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwParentRelPos indicates the relative position of
         this hardware component among all its 'sibling' components.  A sibling
         component shares the same instance values for tmnxHwContainedIn and
         tmnxHwClass objects.

         If the associated value of tmnxHwContainedIn is zero, then the value -1
         is returned."
    ::= { tmnxHwEntry 14 }

tmnxHwAdminState  OBJECT-TYPE
    SYNTAX      INTEGER {
                    noop (1),
                    inService (2),
                    outOfService (3),
                    diagnose (4),
                    operateSwitch (5)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The desired administrative status of this hardware component.  Write 
         access will be denied for those components that do not have 
         administrative status.  An attempt to set tmnxHwAdminState to 
         'operateSwitch (5)' will fail if the hardware component is not part 
         of a redundant pair.  Some examples of redundant hardware are the
         CPM cards and fabric cards."
    DEFVAL { noop }
    ::= { tmnxHwEntry 15 }

tmnxHwOperState   OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (1),
                    inService (2),
                    outOfService (3),
                    diagnosing (4),
                    failed (5),
                    booting (6),
                    empty (7),
                    provisioned (8),
                    unprovisioned (9),
                    upgrade (10),
                    downgrade (11),
                    inServiceUpgrade (12),
                    inServiceDowngrade (13),
                    resetPending (14)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual operational status of this hardware component.

         unknown (1)                Status cannot be determined

         inService (2)              Online - If tmnxHwClass has a value of
                                    'ioModule (8)' or 'cpmModule (9), the 
                                    card is present, booted, configured, 
                                    and running.

         outOfService (3)           Ready - The hardware component is OK 
                                    but is down because tmnxHwAdminState has 
                                    a value of 'outOfService (3)'.

         diagnosing (4)             Not implemented.

         failed (5)                 This hardware component has failed.  The 
                                    value of tmnxHwFailureReason indicates 
                                    the type of failure.  If tmnxHwClass has 
                                    a value of 'ioModule(8)' or 'cpmModule(9)',
                                    there is a card in the slot but it has 
                                    failed.

         booting (6)                A card is in the transitional startup state.

         empty (7)                  There is no card in the slot and it has 
                                    not been pre-configured.

         provisioned (8)            There is no card in the slot but it has 
                                    been pre-configured.

         unprovisioned (9)          There is a card in the slot but it is not 
                                    configured.

         upgrade (10)               Card software version is compatible with 
                                    and newer than that running on the current 
                                    active CPM.

         downgrade (11)             Card software version is compatible with 
                                    and older than that running on the current 
                                    active CPM.

         inServiceUpgrade (12)      Card is inService and the card software 
                                    version is compatible with and newer than 
                                    that running on the current active CPM.  
                                    This state applies only to a standby CPM 
                                    card. This enumeration is no longer
                                    supported as of release 5.0.

         inServiceDowngrade (13)    Card is inService and the card software 
                                    is compatible with and older than that 
                                    running on the current active CPM.  This 
                                    state applies only to a standby CPM card.
                                    This enumeration is no longer supported
                                    as of release 5.0.

         resetPending (14)          Card is awaiting reset following an
                                    upgrade or downgrade activity switch. 
                                    The card software version is upgrade 
                                    or downgrade compatible but will be reset
                                    in order to update it to match the active 
                                    CPM software.  The value of 
                                    tmnxChassisUpdateWaitTime indicates the
                                    how long the system will wait following
                                    an upgrade or downgrade activity switch
                                    before it resets IOM cards.  This state 
                                    applies only to IOM cards. This
                                    enumeration is no longer supported as of
                                    release 5.0.
        "
    ::= { tmnxHwEntry 16 }

tmnxHwTempSensor    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwTempSensor indicates whether or not this
         hardware component contains a temperature sensor."
    ::= { tmnxHwEntry 17 }

tmnxHwTemperature OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "degrees celsius"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current temperature reading in degrees celsius from this hardware 
         component's temperature sensor.  If this component does not contain
         a temperature sensor, then the value -1 is returned."
    ::= { tmnxHwEntry 18 }

tmnxHwTempThreshold   OBJECT-TYPE
    SYNTAX     Integer32
    UNITS       "degrees celsius"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The temperature threshold for this hardware component in degrees 
         celsius.  When the value of tmnxHwTemperature raises above 
         tmnxHwTempThreshold, a 'temperature too high' event will 
         be generated."
    ::= { tmnxHwEntry 19 }

tmnxHwBootCodeVersion     OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The version number of boot eprom on the card in this slot.
         
         If no specific software program is associated with this hardware
         component then this object will contain a zero length string."
    ::= { tmnxHwEntry 20 }

tmnxHwSoftwareCodeVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The software product release version number for the software image 
         currently running on this IOM or CPM card.
         
         If no specific software program is associated with this hardware
         component then this object will contain a zero length string."
    ::= { tmnxHwEntry 21 }

tmnxHwSwLastBoot  OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time the software running on this IOM or CPM card was
         last rebooted.  
         
         If this row entry represents a standby CPM card, the date and time 
         indicated is when the standby completed its initial synchronization 
         process and became ready to take over in case the active card fails
         or a manual switchover command is issued."
    ::= { tmnxHwEntry 22 }

tmnxHwSwState     OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (0),
                    hwFailure (1),
                    swFailure (2),
                    hwInitting (3),
                    swDownloading (4),
                    swInitting (5),
                    swInitted (6),
                    swRunning (7)
                }
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The state of the software running on this IOM or CPM card.
         
         The tmnxHwSwState object is obsolete.  The Alcatel 7x50 platform
         cannot distinguish software status separate from the hardware
         status.  Instead of using this object, additional operational
         states have been added to tmnxHwOperState.

         If no specific software program is associated with this hardware
         component then this object will contain a zero."
    ::= { tmnxHwEntry 23 }

tmnxHwAlarmState    OBJECT-TYPE
    SYNTAX  TmnxAlarmState
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The value of tmnxHwAlarmState indicates the current alarm
         state for this hardware component."
    ::= { tmnxHwEntry 24 }

tmnxHwLastAlarmEvent    OBJECT-TYPE
    SYNTAX  RowPointer
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The value of tmnxHwLastAlarmEvent is an object identifier whose
         object name and instance values point to the row entry in the 
         ALARM-MIB that contains the most recent alarm event associated with 
         this hardware component.  If the tmnxHwAlarmState has a value of 
         'alarmCleared', the most recent alarm event will be in the 
         nlmAlarmClearedTable.  If it has a value of 'alarmActive', the 
         entry pointed to is in the nlmAlarmActiveTable.  If the value of 
         tmnxHwLastAlarmEvent is '0.0', then either there have not been any 
         alarm events associated with this chassis since the system was
         last booted, or the last alarm event has aged out and its entry is 
         no longer available in the ALARM-MIB tables."
    ::= { tmnxHwEntry 25 }

tmnxHwClearAlarms OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting this action variable causes all the active alarms associated 
         with this hardware component to be moved from the ALARM-MIB
         nlmActiveAlarmTable to the nlmClearedAlarmTable.  This action button 
         is primarily meant for use as a code development aid.  This object may 
         be removed from the ALCATEL-IND1-TIMETRA-CHASSIS-MIB before product release."
    DEFVAL { notApplicable }
    ::= { tmnxHwEntry 26 }

tmnxHwSwImageSource       OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (0),
                    primary (1),
                    secondary (2),
                    tertiary (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwSwImageSource indicates the location in the 
         Boot Options File (BOF) where the software image file was found 
         when the system last rebooted."
    ::= { tmnxHwEntry 27 }

tmnxHwMfgDeviations        OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxHwMfgDeviations contains a record of changes done by the 
         manufacturing to the hardware or software and which is outside the 
         normal revision control process."
    ::= { tmnxHwEntry 28 }

tmnxHwBaseMacAddress        OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxHwBaseMacAddress contains the base MAC address of the hardware
         component. It is applicable only if tmnxHwClass is of type 'chassis',
         'ioModule', 'cpmModule' or 'mdaModule'."
    ::= { tmnxHwEntry 29 }

tmnxHwFailureReason OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxHwFailureReason indicates the reason why a hardware component
         'failed' as indicated in tmnxHwOperState."
    ::= { tmnxHwEntry 30 }

--
--  Alcatel 7x50 SR series Hardware Components Containment Table
--
tmnxHwContainsTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxHwContainsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxHwContainsTable shows the container/containee relationship
         between entries in the tmnxHwTable.  The hardware component
         containment tree can be constructed from information in the
         tmnxHwTable, but this table provides the information in a more
         convenient format for the manager system to use."
    ::= { tmnxChassisObjs 9 }

tmnxHwContainsEntry       OBJECT-TYPE
    SYNTAX      TmnxHwContainsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a single container/containee relationship.  
         Entries cannot be created and deleted via SNMP SET operations."
    INDEX   { tmnxHwIndex, tmnxHwContainedIndex }
    ::= { tmnxHwContainsTable 1 }

TmnxHwContainsEntry ::=
    SEQUENCE {
        tmnxHwContainedIndex    TmnxHwIndex
    }

tmnxHwContainedIndex    OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwIndex for the contained hardware component."
    ::= { tmnxHwContainsEntry 1 } 

--
--  Alcatel 7710 SR series Chassis Control Module (CCM) Table
--

tmnxCcmTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCcmEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION   
        "This table contains information about CCM."
    ::= { tmnxChassisObjs 10 }

tmnxCcmEntry OBJECT-TYPE
    SYNTAX      TmnxCcmEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION   
        "Contains information regarding a CCM."
    INDEX       { tmnxChassisIndex, tmnxCcmIndex }
    ::= { tmnxCcmTable 1 }

TmnxCcmEntry ::=
     SEQUENCE {
        tmnxCcmIndex            Unsigned32,
        tmnxCcmOperStatus       TmnxDeviceState,
        tmnxCcmHwIndex          TmnxHwIndex,
        tmnxCcmEquippedType     TmnxCcmType
    }

tmnxCcmIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..8)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies a specific CCM instance in the
         chassis."
    ::= { tmnxCcmEntry 1 }

tmnxCcmOperStatus OBJECT-TYPE
    SYNTAX      TmnxDeviceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION   
        "Current status of the CCM."
    ::= { tmnxCcmEntry 2 }

tmnxCcmHwIndex     OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The value of tmnxCcmHwIndex is the index into the tmnxHwTable 
         for the row entry that represents the hardware component information 
         for this CCM."
    ::= { tmnxCcmEntry 3 }

tmnxCcmEquippedType        OBJECT-TYPE
    SYNTAX      TmnxCcmType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bit-mask that identifies the CCM type that is physically
         inserted into this chassis.  There will not be more than one
         bit set at a time in tmnxCcmEquippedType."
    ::= { tmnxCcmEntry 4 }

--
--  Chassis Control Module Type (CCM) Definition Table
--
tmnxCcmTypeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCcmTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The card type table has an entry for each Alcatel 7710 series
         Chassis Control Module (CCM) model."
    ::= { tmnxChassisObjs 11 }

tmnxCcmTypeEntry    OBJECT-TYPE
    SYNTAX      TmnxCcmTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7710 series CCM model.  
         Rows in this table are created by the agent at initialization and
         cannot be created or destroyed by SNMP Get or Set requests."
    INDEX   { tmnxCcmTypeIndex }
    ::= { tmnxCcmTypeTable 1 }

TmnxCcmTypeEntry ::=
    SEQUENCE {
        tmnxCcmTypeIndex        TmnxCcmType,
        tmnxCcmTypeName         TNamedItemOrEmpty,
        tmnxCcmTypeDescription  TItemDescription,
        tmnxCcmTypeStatus       TruthValue
    }

tmnxCcmTypeIndex    OBJECT-TYPE
    SYNTAX      TmnxCcmType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique index value which identifies this type of Alcatel
         7710 series CCM model."
    ::= { tmnxCcmTypeEntry 1 }
        
tmnxCcmTypeName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The administrative name that identifies this type of Alcatel
         7710 series CCM model.  This name string may be used in CLI
         commands to specify a particular card model type."
    ::= { tmnxCcmTypeEntry 2 }

tmnxCcmTypeDescription  OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A detailed description of this Alcatel 7710 series CCM model."
    ::= { tmnxCcmTypeEntry 3 }         

tmnxCcmTypeStatus       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxCcmTypeStatus has a value of 'true' it
        indicates that this CCM is supported in this revision of the
        management software.  When it has a value of 'false' there is no
        support."
    ::= { tmnxCcmTypeEntry 4 }

--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      Alcatel 7x50 SR series Card Objects
--

--
--      IOM Card Table - The tmnxCardTable contains information 
--                       about the IOM cards in a chassis.
--

tmnxCardLastChange   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
         "The value of sysUpTime when the tmnxCardTable was last changed."
    ::= { tmnxCardObjs 1 }
        
tmnxCardTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCardTable has an entry for each IOM card slot in each 
         chassis in the TMNX system."
    ::= { tmnxCardObjs 2 }

tmnxCardEntry       OBJECT-TYPE
    SYNTAX      TmnxCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an IOM card slot in a chassis in the 
         system.  Entries cannot be created and deleted via SNMP SET 
         operations.  When a tmnxChassisEntry is created, a tmnxCardEntry 
         is created for each IOM card slot in that chassis.  Before a 
         tmnxChassisEntry can be deleted, each tmnxCardEntry for that 
         chassis must be in the proper state for removal."
    INDEX   { tmnxChassisIndex, tmnxCardSlotNum }
    ::= { tmnxCardTable 1 }

TmnxCardEntry ::=
    SEQUENCE {
        tmnxCardSlotNum                     TmnxSlotNum,
        tmnxCardSupportedTypes              TmnxCardType,
        tmnxCardAllowedTypes                TmnxCardType,
        tmnxCardAssignedType                TmnxCardType,
        tmnxCardEquippedType                TmnxCardType,
        tmnxCardHwIndex                     TmnxHwIndex,
        tmnxCardClockSource                 TItemDescription,
        tmnxCardNumMdaSlots                 Unsigned32,
        tmnxCardNumMdas                     Unsigned32,
        tmnxCardReboot                      TmnxActionType,
        tmnxCardMemorySize                  Unsigned32,
        tmnxCardNamedPoolAdminMode          TmnxAdminState,
        tmnxCardNamedPoolOperMode           TmnxAdminState
    }

tmnxCardSlotNum     OBJECT-TYPE
    SYNTAX      TmnxSlotNum
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this IOM slot within a chassis 
         in the system."
    ::= { tmnxCardEntry 1 }

tmnxCardSupportedTypes  OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bit-mask that specifies what card types can be physically 
         supported in this IOM slot in this chassis."
    ::= { tmnxCardEntry 2 }

tmnxCardAllowedTypes    OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "A bit-mask that specifies what IOM card types the administrator
         has designated be allowed to be inserted into this slot.
         If the slot has not-been pre-provisioned and a card that
         does not match one of the allowed types is inserted into
         this slot, a mis-match alarm will be raised.  If a specific
         value has not yet been SET by the manager, tmnxCardAllowedTypes 
         will return the same value to a GET request as 
         tmnxCardSupportedTypes. 

         The object was made obsolete in the 3.0 release."
    ::= { tmnxCardEntry 3 }

tmnxCardAssignedType        OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A bit-mask that identifies the administratively assigned 
         (pre-provisioned) IOM card type that should occupy this slot 
         in this chassis.  If tmnxCardAssignedType has a value of 
         'unassigned', this slot has not yet been pre-provisioned.  
         There must not be more than one bit set at a time in 
         tmnxCardAssignedType."
    DEFVAL { 1 }
    ::= { tmnxCardEntry 4 }

tmnxCardEquippedType        OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bit-mask that identifies the IOM card type that is physically 
         inserted into this slot in this chassis.  If the slot has been 
         pre-provisioned, tmnxCardAssignedType is not equal 'unassigned', 
         and the value of tmnxCardEquippedType is not the same as 
         tmnxCardAssignedType, a mis-match alarm will be raised.  If the 
         slot has not been pre-provisioned, and the value of 
         tmnxCardEquippedType is not one of the allowed types as specified 
         by tmnxCardAllowedTypes, a mis-match alarm will be raised.  There 
         will not be more than one bit set at a time in tmnxCardEquippedType.
         A value of 0 indicates the IOM card type is not recognized by the
         software."
    ::= { tmnxCardEntry 5 }

tmnxCardHwIndex      OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCardHwIndex is the index into the tmnxHwTable 
         for the row entry that represents the hardware component information 
         for this IOM card."
    ::= { tmnxCardEntry 6 }

tmnxCardClockSource OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The clock source used by the IOM card in this slot."
     ::= { tmnxCardEntry 10 }

tmnxCardNumMdaSlots             OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The number of Media Dependent Adapter (MDA) slots available on
         this IOM card."
    ::= { tmnxCardEntry 11 }

tmnxCardNumMdas     OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of Media Dependent Adapters installed on this IOM card."
    ::= { tmnxCardEntry 12 }
        
tmnxCardReboot      OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting this variable to 'doAction' causes the IOM card to execute 
         a soft-reboot."
    DEFVAL { notApplicable }
    ::= { tmnxCardEntry 13 }
 
tmnxCardMemorySize   OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "Mega-bytes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCardMemorySize indicates the amount of 
         memory, in mega-bytes, populated on this IOM card."
    ::= { tmnxCardEntry 14 }

tmnxCardNamedPoolAdminMode OBJECT-TYPE
    SYNTAX      TmnxAdminState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxCardNamedPoolAdminMode specifies whether or 
         not an IOM is provisioned for the configuration of named pools. If
         the value of tmnxCardNamedPoolAdminMode is 'inService(2)', the
         system will change the way default pools are created and allow for
         the creation of MDA and port level named buffer pools. If the value
         of tmnxCardNamedPoolAdminMode is 'outOfService(3)', the system will
         not create per port pools, instead a default network and access pool
         is created for ingress and egress and is shared by queues on all
         ports. This object is used in conjunction with
         tmnxCardNamedPoolOperMode."
    DEFVAL  { outOfService }
    ::= { tmnxCardEntry 15 }

tmnxCardNamedPoolOperMode OBJECT-TYPE
    SYNTAX      TmnxAdminState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCardNamedPoolOperMode specifies whether or 
         not an IOM provisioned with tmnxCardNamedPoolAdminMode to a 
         value of 'inService(2)' will activly use named pools or not.
         A value of 'outOfService(3) means that the named pool configurations
         will not be downloaded to the IOM until after a reset of the IOM
         is performed. A value of 'inService(2)' means that the named pool
         configurations are programmed by the IOM. On systems using a
         separate CPM and IOM combination the value of tmnxCardNamedPoolOperMode
         and tmnxCardNamedPoolAdminMode will always be in sync due to a 
         mandatory reboot of the IOM. On systems using a combined image (CFM)
         these values will be out-of-sync until the chassis is rebooted."
    DEFVAL  { outOfService }
    ::= { tmnxCardEntry 16 }


--
--      CPM Card Table - The Chassis Process Manager card table contains
--      the information about CPM cards or modules in a chassis.
--

tmnxCpmCardLastChange   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime when the tmnxCpmCardTable was last changed."
    ::= { tmnxCardObjs 3 }

tmnxCpmCardTable    OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxCpmCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCpmCardTable has an entry for each CPM card or module in 
         each chassis in the TMNX system."
    ::= { tmnxCardObjs 4 }

tmnxCpmCardEntry       OBJECT-TYPE
    SYNTAX      TmnxCpmCardEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a CPM card or module in a chassis in the 
         system.  Entries cannot be created and deleted via SNMP SET 
         operations.  When a tmnxChassisEntry is created, a tmnxCpmCardEntry 
         is created for each CPM card or module in that chassis.  Before a 
         tmnxChassisEntry can be deleted, each tmnxCpmCardEntry for that 
         chassis must be in the proper state for removal."
    INDEX   { tmnxChassisIndex, tmnxCpmCardSlotNum, tmnxCpmCardNum }
    ::= { tmnxCpmCardTable 1 }

TmnxCpmCardEntry ::=
    SEQUENCE {
        tmnxCpmCardSlotNum                        TmnxSlotNum,
        tmnxCpmCardNum                            Unsigned32,
        tmnxCpmCardSupportedTypes                 TmnxCardType,
        tmnxCpmCardAllowedTypes                   TmnxCardType,
        tmnxCpmCardAssignedType                   TmnxCardType,
        tmnxCpmCardEquippedType                   TmnxCardType,
        tmnxCpmCardHwIndex                        TmnxHwIndex,
        tmnxCpmCardBootOptionVersion              TItemDescription,
        tmnxCpmCardBootOptionLastModified         DateAndTime,
        tmnxCpmCardConfigBootedVersion            TItemDescription,
        tmnxCpmCardIndexBootedVersion             TItemDescription,
        tmnxCpmCardConfigLastModified             DateAndTime,
        tmnxCpmCardConfigLastSaved                DateAndTime,
        tmnxCpmCardRedundant                      INTEGER,
        tmnxCpmCardClockSource                    TItemDescription,
        tmnxCpmCardNumCpus                        Unsigned32,
        tmnxCpmCardCpuType                        INTEGER,
        tmnxCpmCardMemorySize                     Unsigned32,
        tmnxCpmCardSwitchToRedundantCard          TmnxActionType,
        tmnxCpmCardReboot                         TmnxActionType,
        tmnxCpmCardRereadBootOptions              TmnxActionType,
        tmnxCpmCardConfigFileLastBooted           DisplayString,
        tmnxCpmCardConfigFileLastSaved            DisplayString,
        tmnxCpmCardConfigFileLastBootedHeader     OCTET STRING,
        tmnxCpmCardIndexFileLastBootedHeader      OCTET STRING,
        tmnxCpmCardBootOptionSource               DisplayString,
        tmnxCpmCardConfigSource                   INTEGER,
        tmnxCpmCardBootOptionLastSaved            DateAndTime,
        tmnxCpmCardMasterSlaveRefState            INTEGER
    }

tmnxCpmCardSlotNum     OBJECT-TYPE
    SYNTAX      TmnxSlotNum
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this slot within a chassis in 
         the system.  Depending upon the value of tmnxChassisType, this may 
         represent a fabric slot or a regular card slot.  If this CPM module 
         resides on a fabric card, tmnxCpmCardSlotNum has the value the
         corresponding tmnxFabricSlotNum.  If this is a CPM module on a
         fabric card, tmnxCpmCardSlotNum is the fabric slot number in the 
         chassis where this CPM module is located.  Else if this is a
         CPM card, tmnxCpmCardSlotNum is a regular card slot number."
    ::= { tmnxCpmCardEntry 1 }

tmnxCpmCardNum      OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this CPM module within a
         specific card slot within a chassis in the system."
    ::= { tmnxCpmCardEntry 2 }

tmnxCpmCardSupportedTypes  OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bit-mask that specifies what CPM card types can be physically
         supported in this slot in this chassis."
    ::= { tmnxCpmCardEntry 3 }

tmnxCpmCardAllowedTypes    OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "A bit-mask that specifies what CPM card types the administrator
         has designated be allowed to be inserted into this slot.  If the 
         slot has not-been pre-provisioned and a card that does not match 
         one of the allowed types is inserted into this slot, a mis-match 
         alarm will be raised.  If a specific value has not yet been SET by 
         the manager, tmnxCpmCardAllowedTypes will return the same value to 
         a GET request as tmnxCpmCardSupportedTypes.

         The object was made obsolete in the 3.0 release."
    ::= { tmnxCpmCardEntry 4 }

tmnxCpmCardAssignedType        OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A bit-mask that identifies the administratively assigned 
         (pre-provisioned) CPM card type that should occupy this slot 
         in this chassis.  If tmnxCpmCardAssignedType has a value of 
         'unassigned', this slot has not yet been pre-provisioned.  
         There must not be more than one bit set at a time in 
         tmnxCpmCardAssignedType."
    DEFVAL { 1 }
    ::= { tmnxCpmCardEntry 5 }

tmnxCpmCardEquippedType        OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-only
    STATUS      current
     DESCRIPTION
        "A bit-mask that identifies the CPM card type that is physically 
         inserted into this slot in this chassis.  If the slot has been 
         pre-provisioned, tmnxCpmCardAssignedType is not equal 'unassigned', 
         and the value of tmnxCpmCardEquippedType is not the same as 
         tmnxCpmCardAssignedType, a mis-match alarm will be raised.  
         
         If the slot has not been pre-provisioned, and the value  of 
         tmnxCpmCardEquippedType is not one of the allowed types as specified 
         by tmnxCpmCardAllowedTypes, a mis-match alarm will be raised.  
         There will not be more than one bit set at a time in 
         tmnxCpmCardEquippedType."
    ::= { tmnxCpmCardEntry 6 }

tmnxCpmCardHwIndex       OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCpmCardHwIndex is the index into the tmnxHwTable 
         for the row entry that represents the hardware component information 
         for this CPM card or module."
    ::= { tmnxCpmCardEntry 7 }

tmnxCpmCardBootOptionVersion     OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The version number of boot option file (BOF) read by the CPM card in 
         this slot."
    ::= { tmnxCpmCardEntry 8 }

tmnxCpmCardBootOptionLastModified  OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time the boot options file (BOF) for this card was last 
         modified.  If tmnxCpmCardBootOptionLastModified is more recent than 
         tmnxHwSwLastBoot, the boot options file has been edited since 
         the software was booted and different software images or configuration
         will likely be used when this card is next rebooted."
    ::= { tmnxCpmCardEntry 9 }

tmnxCpmCardConfigBootedVersion OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The version of the configuration file read when this CPM card was
         last rebooted."
    ::= { tmnxCpmCardEntry 10 }

tmnxCpmCardIndexBootedVersion OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The version of the index file read when this CPM card was
         last rebooted."
    ::= { tmnxCpmCardEntry 11 }

tmnxCpmCardConfigLastModified  OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time the running configuration was last modified.
         If tmnxCpmCardConfigLastModified is more recent than 
         tmnxHwSwLastBoot, the current configuration may be different
         than that in the configuration file read upon system initialization."
    ::= { tmnxCpmCardEntry 12 }

tmnxCpmCardConfigLastSaved OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time the current configuration was last saved.
         If tmnxCpmCardConfigLastSaved is more recent the value of 
         tmnxHwSwLastBoot, the initial configuration is likely to 
         be different the next time the system is rebooted."
    ::= { tmnxCpmCardEntry 13 }

tmnxCpmCardRedundant   OBJECT-TYPE
    SYNTAX      INTEGER {
                    singleton (1),
                    redundantActive (2),
                    redundantStandby (3),
                    redundantSplit (4),
                    redundantDisabled (5),
                    redundantSynching (6)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This variable indicates if the CPM card is standalone or part
         of a pair of redundant cards.  If 'redundantDisabled',
         tmnxHwOperState indicates the specific reason why this 
         redundant CPM card is not available.
         
         Note that the 'redudantSplit' option is not implemented."
    ::= { tmnxCpmCardEntry 14 }

tmnxCpmCardClockSource OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The clock source used by the CPM card in this slot."
    ::= { tmnxCpmCardEntry 15 }

tmnxCpmCardNumCpus      OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCpmCardNumCpus indicates the number of CPU chips 
         populated on this CPM module."
    ::= { tmnxCpmCardEntry 16 }

tmnxCpmCardCpuType      OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (1),
                    mips (2),
                    pentium-pc (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCpmCardCpuType indicates the type of CPU chips 
         populated on this CPM module."
    ::= { tmnxCpmCardEntry 17 }

tmnxCpmCardMemorySize   OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "Mega-bytes"
    MAX-ACCESS  read-only
        STATUS          current
    DESCRIPTION
        "The value of tmnxCpmCardMemorySize indicates the amount of 
         memory, in mega-bytes, populated on this CPM module."
    ::= { tmnxCpmCardEntry 18 }

tmnxCpmCardSwitchToRedundantCard   OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting this variable to doAction causes the switchover to the 
         redundant CPM card."
    DEFVAL { notApplicable }
    ::= { tmnxCpmCardEntry 19 }

tmnxCpmCardReboot      OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting this variable to 'doAction' causes the CPM card to execute 
         a soft-reboot."
    DEFVAL { notApplicable }
    ::= { tmnxCpmCardEntry 20 }

tmnxCpmCardRereadBootOptions    OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting this variable to 'doAction' causes the Boot Options File
         (BOF) to be reread and applied."
    DEFVAL { notApplicable }
    ::= { tmnxCpmCardEntry 21 }

tmnxCpmCardConfigFileLastBooted      OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxCpmCardConfigFileLastBooted indicates the location and name of 
         the configuration file from which the system last rebooted."
    ::= { tmnxCpmCardEntry 22 }

tmnxCpmCardConfigFileLastSaved      OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxCpmCardConfigFileLastSaved indicates the location and name of the 
         file to which the configuration was last saved."
    ::= { tmnxCpmCardEntry 23 }

tmnxCpmCardConfigFileLastBootedHeader       OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (0..512))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxCpmCardConfigFileLastBootedHeader contains the header 
         of the configuration file from which the system last rebooted."
    ::= { tmnxCpmCardEntry 24 }     

tmnxCpmCardIndexFileLastBootedHeader       OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (0..512))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxCpmCardIndexFileLastBootedHeader contains the header 
         of the index file from which the system last rebooted."
    ::= { tmnxCpmCardEntry 25 }

tmnxCpmCardBootOptionSource       OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxCpmCardBootOptionSource indicates the compact flash slot where the
         Boot Options File (BOF) file was found when the system last rebooted.
         For example, if the BOF file was found on compact flash slot 1, the
         value of this variable will be 'cf1:'"
    ::= { tmnxCpmCardEntry 26 }

tmnxCpmCardConfigSource       OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (0),
                    primary (1),
                    secondary (2),
                    tertiary (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCpmCardConfigSource indicates the location
         in the Boot Options File(BOF) where the configuration file was 
         found when the system last rebooted."
    ::= { tmnxCpmCardEntry 27 }

tmnxCpmCardBootOptionLastSaved OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time the boot options file (BOF) was last saved.
         If tmnxCpmCardBootOptionLastSaved is more recent than the value
         of tmnxHwSwLastBoot, the boot options file has been edited
         since the software was booted and different software images or
         configuration will likely be used when this card is next rebooted."
    ::= { tmnxCpmCardEntry 28 }

tmnxCpmCardMasterSlaveRefState    OBJECT-TYPE
    SYNTAX      INTEGER {
                    primaryRef          (1),
                    secondaryRef        (2),
                    notInitialized      (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current Master/Slave clocking reference designation.
        
         primaryRef       Indicates this card is designated as the primary
                          clocking reference in a redundant system.
         secondaryRef     Indicates this card is designated as the secondary
                          clocking reference in a redundant system.
         notInitialized   Indicates the clock is not initialized.
         "
    ::= { tmnxCpmCardEntry 30 }
    
--
--      Fabric Card Table - The fabric card table contains information about 
--      the fabric cards in a chassis.
--

tmnxFabricLastChange   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime when the tmnxFabricTable was last changed."
    ::= { tmnxCardObjs 5 }
        
tmnxFabricTable         OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxFabricEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxFabricTable has an entry for each fabric card slot in 
         each chassis in the TMNX system."
    ::= { tmnxCardObjs 6 }

tmnxFabricEntry         OBJECT-TYPE
    SYNTAX      TmnxFabricEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a fabric card slot in a chassis in 
         the system.  Entries cannot be created and deleted via
         SNMP SET operations.  When a tmnxChassisEntry is created,
         a tmnxFabricEntry is created for each fabric card slot in that
         chassis.  Before a tmnxChassisEntry can be deleted, each 
         tmnxFabricEntry for that chassis must be in the proper state 
         for removal."
    INDEX   { tmnxChassisIndex, tmnxFabricSlotNum }
    ::= { tmnxFabricTable 1 }

TmnxFabricEntry ::=
    SEQUENCE {
        tmnxFabricSlotNum           Unsigned32,
        tmnxFabricAssignedType      TmnxCardType,
        tmnxFabricEquippedType      TmnxCardType,
        tmnxFabricHwIndex           TmnxHwIndex
    }

tmnxFabricSlotNum     OBJECT-TYPE
    SYNTAX      Unsigned32 (1..16)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this fabric slot within a
         chassis in the system. The CPM cards and IOM cards cannot be
         physically inserted into the switch fabric card slots.  In
         some models, the CPM is not a separate card, but rather a
         module on a Fabric card."
    ::= { tmnxFabricEntry 1 }

tmnxFabricAssignedType        OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administratively assigned switch fabric card type that 
         should occupy this fabric slot in this chassis."
    DEFVAL { 2 }
    ::= { tmnxFabricEntry 2 }

tmnxFabricEquippedType        OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The switch fabric card type that is physically inserted into
         this slot in this chassis."
    ::= { tmnxFabricEntry 3 }

tmnxFabricHwIndex        OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwIndex is the index into the tmnxHwTable to 
         locate the row entry that represents the hardware component 
         information for this fabric card."
    ::= { tmnxFabricEntry 4 }

 
--
--  Flash Drive Table
--

tmnxCpmFlashTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF TmnxCpmFlashEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     
        "This table contains information about Flash devices on a CPM card."
    ::= { tmnxCardObjs 7 }

tmnxCpmFlashEntry OBJECT-TYPE
    SYNTAX          TmnxCpmFlashEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     
        "Contains information regarding a CPM card's flash unit."
    INDEX   { tmnxChassisIndex, tmnxCardSlotNum, tmnxCpmFlashId }
    ::= { tmnxCpmFlashTable 1 }

TmnxCpmFlashEntry ::=
    SEQUENCE {
        tmnxCpmFlashId                  Unsigned32,
        tmnxCpmFlashOperStatus          TmnxDeviceState,
        tmnxCpmFlashSerialNumber        TItemDescription,
        tmnxCpmFlashFirmwareRevision    TItemDescription,
        tmnxCpmFlashModelNumber         TItemDescription,
        tmnxCpmFlashCapacity            Unsigned32,
        tmnxCpmFlashUsed                Unsigned32,
        tmnxCpmFlashHwIndex             TmnxHwIndex
    }

tmnxCpmFlashId OBJECT-TYPE
    SYNTAX          Unsigned32 (1..32)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "The unique identifier index for a flash device on a CPM card."
    ::= { tmnxCpmFlashEntry 1 }

tmnxCpmFlashOperStatus OBJECT-TYPE
    SYNTAX         TmnxDeviceState
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION   
        "Current status of this flash unit on this CPM card."
    ::= { tmnxCpmFlashEntry 2 }

tmnxCpmFlashSerialNumber OBJECT-TYPE
    SYNTAX        TItemDescription
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The serial number for this flash unit on this CPM card."
        ::= { tmnxCpmFlashEntry 3 }

tmnxCpmFlashFirmwareRevision OBJECT-TYPE
    SYNTAX        TItemDescription
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The firmware revision number for this flash unit on this CPM card."
    ::= { tmnxCpmFlashEntry 4 }

tmnxCpmFlashModelNumber OBJECT-TYPE
    SYNTAX        TItemDescription
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The model number for this flash unit on this CPM card."
    ::= { tmnxCpmFlashEntry 5 }

tmnxCpmFlashCapacity    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "sectors"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCpmFlashCapacity indicates the maximum size
         of this flash unit in 512-byte sectors."
    ::= { tmnxCpmFlashEntry 6 }

tmnxCpmFlashUsed    OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "sectors"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCpmFlashUsed indicates the amount used, in 
         512-byte sectors, of this flash unit's total capacity."
    ::= { tmnxCpmFlashEntry 7 }

tmnxCpmFlashHwIndex   OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwIndex is the index into the tmnxHwTable for
         the row entry that represents the hardware component information 
         for this flash unit."
    ::= { tmnxCpmFlashEntry 8 }


--
--      MDA table
--
tmnxMDATable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxMDAEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxMDATable has an entry for each MDA slot in each IOM
         card in this chassis in the TMNX system."
    ::= { tmnxCardObjs 8 }

tmnxMDAEntry       OBJECT-TYPE
    SYNTAX      TmnxMDAEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a MDA slot in an IOM card in a
         chassis in the system.  Entries cannot be created and 
         deleted via SNMP SET operations.  When a tmnxCardEntry 
         has tmnxCardAssignedType or tmnxCardEquippedType equal to
         an IOM card type that supports MDA slots, a tmnxMDAEntry is 
         created by the agent for each MDA slot on that IOM card. 
         Before a tmnxCardEntry can be deleted, each tmnxMDAEntry for 
         that card must be in the proper state for removal."
    INDEX   { tmnxChassisIndex, tmnxCardSlotNum, tmnxMDASlotNum }
    ::= { tmnxMDATable 1 }

TmnxMDAEntry ::=
    SEQUENCE {
        tmnxMDASlotNum                  Unsigned32,
        tmnxMDASupportedTypes           TmnxMDASuppType,
        tmnxMDAAllowedTypes             TmnxMdaType,
        tmnxMDAAssignedType             TmnxMdaType,
        tmnxMDAEquippedType             TmnxMdaType,
        tmnxMDAHwIndex                  TmnxHwIndex,
        tmnxMDAMaxPorts                 INTEGER,
        tmnxMDAEquippedPorts            Unsigned32,
        tmnxMDATxTimingSelected         INTEGER,
        tmnxMDASyncIfTimingStatus       INTEGER,
        tmnxMDANetworkIngQueues         TNamedItem,
        tmnxMDACapabilities             BITS,
        tmnxMDAMinChannelization        TmnxMDAChanType,
        tmnxMDAMaxChannelization        TmnxMDAChanType,
        tmnxMDAMaxChannels              Unsigned32,
        tmnxMDAChannelsInUse            Unsigned32,
        tmnxMDACcagId                   TmnxCcagId,
        tmnxMDAReboot                   TmnxActionType,
        tmnxMDAHiBwMcastSource          TruthValue,
        tmnxMDAHiBwMcastAlarm           TruthValue,
        tmnxMDAHiBwMcastTapCount        Gauge32,
        tmnxMDAHiBwMcastGroup           Unsigned32,
        tmnxMDAClockMode                INTEGER,
        tmnxMDADiffTimestampFreq        Unsigned32,
        tmnxMDAMcPathMgmtBwPlcyName     TNamedItem,   
        tmnxMDAMcPathMgmtPriPathLimit   Unsigned32,
        tmnxMDAMcPathMgmtSecPathLimit   Unsigned32,
        tmnxMDAMcPathMgmtAncPathLimit   Unsigned32,
        tmnxMDAMcPathMgmtAdminState     TmnxAdminState,
        tmnxMDAIngNamedPoolPolicy       TNamedItemOrEmpty,
        tmnxMDAEgrNamedPoolPolicy       TNamedItemOrEmpty,
        tmnxMDAMcPathMgmtPriInUseBw     Gauge32,
        tmnxMDAMcPathMgmtSecInUseBw     Gauge32,
        tmnxMDAMcPathMgmtAncInUseBw     Gauge32,
        tmnxMDAMcPathMgmtBlkHoleInUseBw Gauge32            
    }

tmnxMDASlotNum     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this MDA slot within a
         specific IOM card in the system.  Rows with a tmnxMDASlotNum
         value of zero (0) represent the special case of an IOM card
         without MDA slots but that instead has its ports directly
         on the IOM card itself.  In that case, there should be only
         that one row entry in the tmnxMDATable for that IOM card."
    ::= { tmnxMDAEntry 1 }

tmnxMDASupportedTypes   OBJECT-TYPE
    SYNTAX      TmnxMDASuppType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bit-mask that specifies what MDA card types can be physically
         supported in this slot in this chassis."
    ::= { tmnxMDAEntry 2 }

tmnxMDAAllowedTypes OBJECT-TYPE
    SYNTAX      TmnxMdaType
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "An integer that specified what MDA card types the administrator
         has designated be allowed to be inserted into this slot.
         If the slot has not-been pre-provisioned and a MDA card that
         does not match one of the allowed types is inserted into
         this slot, a mis-match alarm will be raised. 

         The object was made obsolete in the 3.0 release."
    ::= { tmnxMDAEntry 3 }

tmnxMDAAssignedType        OBJECT-TYPE
    SYNTAX      TmnxMdaType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "An integer that indicates the administratively assigned 
         (pre-provisioned) MDA card type that should occupy this slot in 
         this chassis.  If tmnxMDAAssignedType has a value of 
         'unassigned', this slot has not yet been pre-provisioned."
    DEFVAL { 1 }
    ::= { tmnxMDAEntry 4 }

tmnxMDAEquippedType        OBJECT-TYPE
    SYNTAX      TmnxMdaType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An integer that indicates the MDA card type that is physically 
         inserted into this slot in this chassis.  If the slot has been 
         pre-provisioned, tmnxMDAAssignedType is not equal 'unassigned', 
         and the value of tmnxMDAEquippedType is not the same as
         tmnxMDAAssignedType, a mis-match alarm will be raised.
         A value of 0 indicates the equipped MDA is not supported by
         this software release."
    ::= { tmnxMDAEntry 5 }

tmnxMDAHwIndex       OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxHwIndex is the index into the tmnxHwTable for
         the row entry that represents the hardware component information 
         for this MDA card."
    ::= { tmnxMDAEntry 6 }

tmnxMDAMaxPorts    OBJECT-TYPE
    SYNTAX      INTEGER (0..127)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum number of ports that can be equipped on this MDA card."
    ::= { tmnxMDAEntry 7 }

tmnxMDAEquippedPorts       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAEquippedPorts indicates the number of ports
         equipped on this MDA card."
    ::= { tmnxMDAEntry 8 }

tmnxMDATxTimingSelected       OBJECT-TYPE
    SYNTAX      INTEGER
                {
                    cpm-card-A(1),
                    cpm-card-B(2),
                    local(3),
                    holdover(4),
                    not-applicable(5)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The transmit timing method which is presently selected and being
         used by this MDA.
         tmnxMDATxTimingSelected will be set to 'not-applicable' if this MDA
         does not use the transmit timing subsystem."
    ::= { tmnxMDAEntry 10 }

tmnxMDASyncIfTimingStatus       OBJECT-TYPE
    SYNTAX      INTEGER
                {
                    qualified(1),
                    not-qualified(2),
                    not-applicable(3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the status of the synchronous equipment timing subsystem.
         If the tmnxSyncIfTimingRef1Qualified and tmnxSyncIfTimingRef2Qualified
         are both set to 'not-qualified, then tmnxMDASyncIfTimingStatus is set 
         to 'not-qualified'. If any of the timing references is in use, then 
         tmnxMDASyncIfTimingStatus is set to 'qualified'.
         tmnxMDASyncIfTimingStatus will be set to 'not-applicable' if this MDA
         does not use the transmit timing subsystem."
    ::= { tmnxMDAEntry 11 }

tmnxMDANetworkIngQueues      OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the network queue policy being used for this object to 
        define the queueing structure for this object."
    DEFVAL { "default" }
    ::= { tmnxMDAEntry 12 }

tmnxMDACapabilities      OBJECT-TYPE
    SYNTAX      BITS {
                    isEthernet(0),
                    isSonet(1),
                    isTDM(2),
                    supportsPPP(3),
                    supportsFR(4),
                    supportsATM(5),
                    supportscHDLC(6),
                    isCMA(7),
                    supportsCEM(8)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxMDACapabilities indicates the capabilities of this MDA.
         It identifies the type of MDA and the protocols that can run on it."
    ::= { tmnxMDAEntry 13 }

tmnxMDAMinChannelization      OBJECT-TYPE
    SYNTAX      TmnxMDAChanType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxMDAMinChannelization indicates the minimum size of the channel that
         can exist on this MDA."
    ::= { tmnxMDAEntry 14 }

tmnxMDAMaxChannelization      OBJECT-TYPE
    SYNTAX      TmnxMDAChanType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxMDAMaxChannelization indicates the maximum size of the channel that
         can exist on this MDA."
    ::= { tmnxMDAEntry 15 }

tmnxMDAMaxChannels      OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxMDAMaxChannels is applicable for SONET and TDM MDAs only. It
         indicates the total number of leaf SONET paths, TDM channels 
         and bundles on the MDA that may be configured to pass traffic."
    ::= { tmnxMDAEntry 16 }

tmnxMDAChannelsInUse      OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxMDAChannelsInUse is applicable for SONET and TDM MDAs only.  It
         indicates the total number of leaf SONET paths, TDM channels and
         bundles on the MDA which are in use.  A leaf SONET path or TDM
         channel which is currently capable of passing traffic is considered
         to be in use.  Also, a SONET path or TDM channel which is
         channelized and has no subchannels capable of passing traffic
         is considered to be in use.  A SONET path or TDM channel which is
         channelized and has one or more subchannels capable of passing
         traffic is not considered to be in use, although the subchannels
         themselves are considered to be in use.  A bundle is considered to
         be a channel in use as are each of its members since they are TDM
         channels capable of passing traffic."
    ::= { tmnxMDAEntry 17 }

tmnxMDACcagId        OBJECT-TYPE
    SYNTAX      TmnxCcagId
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When tmnxMDAAssignedType has a value of 'cca' , the value of 
         tmnxMDACcagId specifies the Cross Connect Aggregation Group (CCAG) 
         entry this MDA is provisioned on.  If this entry does not represent 
         a 'cca' MDA or is not associated with a CCAG, tmnxMDACcagId 
         has a value of zero. "
    DEFVAL { 0 }
    ::= { tmnxMDAEntry 18 }

tmnxMDAReboot       OBJECT-TYPE
    SYNTAX      TmnxActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting this variable to 'doAction' causes the MDA to execute 
         a soft-reboot."
    DEFVAL { notApplicable }
    ::= { tmnxMDAEntry 19 }

tmnxMDAHiBwMcastSource   OBJECT-TYPE 
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAHiBwMcastSource specifies if this MDA should
         attempt to allocate separate fabric planes to allocate high bandwidth 
         multicast traffic taps.

         tmnxMDAHiBwMcastGroup must be set in the same SNMP request PDU with 
         tmnxMDAHiBwMcastSource or an 'inconsistentValue' error will be
         returned."
    DEFVAL { false }
    ::= { tmnxMDAEntry 20 }

tmnxMDAHiBwMcastAlarm     OBJECT-TYPE   
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAHiBwMcastAlarm specifies if a
         tmnxChassisHiBwMcastAlarm alarm is raised if there are more than 
         one high bandwidth multicast traffic taps sharing a plane."
    DEFVAL { true }
    ::= { tmnxMDAEntry 21 }

tmnxMDAHiBwMcastTapCount     OBJECT-TYPE   
    SYNTAX      Gauge32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAHiBwMcastTapCount indicates the number of high
         bandwidth multicast traffic taps on this MDA."    
    ::= { tmnxMDAEntry 22 }    
 
tmnxMDAHiBwMcastGroup  OBJECT-TYPE   
    SYNTAX      Unsigned32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAHiBwMcastGroup specifies the group of high
         bandwidth multicast traffic taps to which this tap belongs.

         A value of '0' specifies that this tap is not a member of any High
         Bandwidth Multicast group. 

         On an IOM of type 'iom-10g', the value of tmnxMDAHiBwMcastGroup
         should be the same as the value of tmnxMDAHiBwMcastGroup set on the 
         other MDA residing on the IOM if the tmnxMDAHiBwMcastSource is set
         to 'true'. Attempt to set to different values will result in an
         'inconsistentValue' error.

         tmnxMDAHiBwMcastGroup must be set in the same SNMP request PDU with 
         tmnxMDAHiBwMcastSource or an 'inconsistentValue' error will be
         returned."
    DEFVAL { 0 }    
    ::= { tmnxMDAEntry 23 }    

tmnxMDAClockMode  OBJECT-TYPE   
    SYNTAX      INTEGER {
                    notApplicable (0),
                    adaptive (1),
                    differential (2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAClockMode specifies the clock mode
         of the MDA.

         notApplicable - The MDA does not support any clock modes or domains.
         adaptive      - The MDA is in 'adaptive' clock mode.  This allows
                         adaptive clock domains to be created.
         differential  - The MDA is in 'differential clock mode.  This allows
                         differential clock domains to be created.

         The value of tmnxMDAClockMode can be changed when there are no ports
         created on the MDA.  If there are ports created, a shutdown of the
         MDA is required in order to change the value."
    DEFVAL { notApplicable }
    ::= { tmnxMDAEntry 24 }

tmnxMDADiffTimestampFreq  OBJECT-TYPE   
    SYNTAX      Unsigned32 (0|19440|77760|103680)
    UNITS       "kilohertz"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDADiffTimestampFreq specifies the differential
         timestamp frequency of the differential clock on the MDA.

         The value must be a multiple of 8 KHz.

         This value can only be changed if the value of tmnxMDAClockMode is
         'differential (2)' and there are no ports created on the MDA.  If
         there are ports created, a shutdown of the MDA is required in order
         to change the value.

         If the value of tmnxMDAClockMode is 'differential (2) then the default
         is 103,680 KHz.
         If the value of tmnxMDAClockMode is not 'differential (2)' then
         this value is 0 KHz and cannot be changed."
    DEFVAL { 0 }
    ::= { tmnxMDAEntry 25 }

tmnxMDAMcPathMgmtBwPlcyName     OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtBwPlcyName specifies the multicast policy 
         name configured on the MDA."
    DEFVAL { "default" }
    ::= { tmnxMDAEntry 27 }

tmnxMDAMcPathMgmtPriPathLimit   OBJECT-TYPE
    SYNTAX      Unsigned32 (0|1..2000)
    UNITS       "mega-bits-per-second"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtPriPathLimit specifies the primary path 
         limit for the MDA."
    DEFVAL { 0 } 
    ::= { tmnxMDAEntry 28 }

tmnxMDAMcPathMgmtSecPathLimit OBJECT-TYPE
    SYNTAX      Unsigned32 (0|1..2000)
    UNITS       "mega-bits-per-second"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtSecPathLimit specifies the secondary path
         limit for the MDA."
    DEFVAL { 0 } 
    ::= { tmnxMDAEntry 29 }

tmnxMDAMcPathMgmtAncPathLimit        OBJECT-TYPE
    SYNTAX      Unsigned32 (0|1..5000)
    UNITS       "mega-bits-per-second"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtAncPathLimit specifies the ancillary path
         limit for the MDA."
    DEFVAL { 0 } 
    ::= { tmnxMDAEntry 30 }

tmnxMDAMcPathMgmtAdminState        OBJECT-TYPE
    SYNTAX      TmnxAdminState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtAdminState specifies administrative state
         of this multicast path on the MDA."
    DEFVAL { outOfService }
    ::= { tmnxMDAEntry 31 }

tmnxMDAIngNamedPoolPolicy OBJECT-TYPE   
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAIngNamedPoolPolicy specifies a named pool 
         policy associated with an MDA ingress context. The policy 
         governs the way named pools are created at the MDA level."
    DEFVAL { ''H }    
    ::= { tmnxMDAEntry 32 }

tmnxMDAEgrNamedPoolPolicy OBJECT-TYPE   
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAEgrNamedPoolPolicy specifies a named pool 
         policy associated with an MDA egress context. The policy 
         governs the way named pools are created at the MDA level."
    DEFVAL { ''H }    
    ::= { tmnxMDAEntry 33 }

tmnxMDAMcPathMgmtPriInUseBw   OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtPriInUseBw indicates the in use 
         ingress multicast bandwidth for the primary forwarding path."
    ::= { tmnxMDAEntry 36 }

tmnxMDAMcPathMgmtSecInUseBw OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtSecInUseBw indicates the in use 
         ingress multicast bandwidth for the secondary forwarding path."
    ::= { tmnxMDAEntry 37 }

tmnxMDAMcPathMgmtAncInUseBw        OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtAncInUseBw indicates the in use 
         ingress multicast bandwidth for the ancillary forwarding path."
    ::= { tmnxMDAEntry 38 }

tmnxMDAMcPathMgmtBlkHoleInUseBw        OBJECT-TYPE
    SYNTAX      Gauge32 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMDAMcPathMgmtBlkHoleInUseBw indicates the bandwidth of
         the ingress multicast traffic that is being black holed on the MDA."
    ::= { tmnxMDAEntry 39 }

--
--  Card Type Definition Table
--
tmnxCardTypeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCardTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The card type table has an entry for each Alcatel 7x50 SR series
         card model."
    ::= { tmnxCardObjs 9 }

tmnxCardTypeEntry    OBJECT-TYPE
    SYNTAX      TmnxCardTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7x50 SR series Card model.  
         Rows in this table are created by the agent at initialization and
         cannot be created or destroyed by SNMP Get or Set requests."
    INDEX   { tmnxCardTypeIndex }
    ::= { tmnxCardTypeTable 1 }

TmnxCardTypeEntry ::=
    SEQUENCE {
        tmnxCardTypeIndex        TmnxCardType,
        tmnxCardTypeName         TNamedItemOrEmpty,
        tmnxCardTypeDescription  TItemDescription,
        tmnxCardTypeStatus       TruthValue
    }

tmnxCardTypeIndex    OBJECT-TYPE
    SYNTAX      TmnxCardType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique index value which identifies this type of Alcatel
         7x50 SR series card model."
    ::= { tmnxCardTypeEntry 1 }
        
tmnxCardTypeName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The administrative name that identifies this type of Alcatel
         7x50 SR series card model.  This name string may be used in 
         CLI commands to specify a particular card model type."
    ::= { tmnxCardTypeEntry 2 }

tmnxCardTypeDescription  OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A detailed description of this Alcatel 7x50 SR series card model."
    ::= { tmnxCardTypeEntry 3 }         

tmnxCardTypeStatus       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxCardTypeStatus has a value of 'true' it indicates that
         this card model is supported in this revision of the management
         software.  When it has a value of 'false' there is no support."
    ::= { tmnxCardTypeEntry 4 }
                                                              

--
--  MDA Type Defintion Table
--
tmnxMdaTypeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxMdaTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The MDA type table has an entry for each Alcatel 7x50 SR series
         MDA card model."
    ::= { tmnxCardObjs 10 }

tmnxMdaTypeEntry    OBJECT-TYPE
    SYNTAX      TmnxMdaTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7x50 SR series MDA card model.
         Rows in this table are created by the agent at initialization and
         cannot be created or destroyed by SNMP Get or Set requests."
    INDEX   { tmnxMdaTypeIndex }
    ::= { tmnxMdaTypeTable 1 }

TmnxMdaTypeEntry ::=
    SEQUENCE {
        tmnxMdaTypeIndex        TmnxMdaType,
        tmnxMdaTypeName         TNamedItemOrEmpty,
        tmnxMdaTypeDescription  TItemDescription,
        tmnxMdaTypeStatus       TruthValue
    }

tmnxMdaTypeIndex    OBJECT-TYPE
    SYNTAX      TmnxMdaType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique index value which identifies this type of Alcatel
         7x50 SR series MDA card model."
    ::= { tmnxMdaTypeEntry 1 }
        
tmnxMdaTypeName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The administrative name that identifies this type of Alcatel
         7x50 SR series MDA card model.  This name string may be used 
         in CLI commands to specify a particular MDA card model type."
    ::= { tmnxMdaTypeEntry 2 }

tmnxMdaTypeDescription  OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A detailed description of this Alcatel 7x50 SR series MDA card 
         model."
    ::= { tmnxMdaTypeEntry 3 }         

tmnxMdaTypeStatus       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxMdaTypeStatus has a value of 'true' it indicates that
         this MDA card model is supported in this revision of the management
         software.  When it has a value of 'false' there is no support."
    ::= { tmnxMdaTypeEntry 4 }

--
-- Synchronous interface timing information for the CPM card
--

tmnxSyncIfTimingTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxSyncIfTimingEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The synchronous interface timing table has an entry for each cpm
         card in the system."
    ::= { tmnxCardObjs 11 }

tmnxSyncIfTimingEntry    OBJECT-TYPE
    SYNTAX      TmnxSyncIfTimingEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "A row represents the configuration of synchronous equipment timing 
        subsystem (SETS) of the system. Entries cannot be created and deleted 
        via SNMP SET operations.  When a tmnxCpmCardEntry is created, a 
        tmnxSyncIfTimingEntry is created. Note that the first Alcatel
        7x50 SR series product release does not support configuration of 
        synchronous equipment on the secondary CPM.  All SNMP operations 
        with tmnxCpmCardSlotNum of the secondary CPM will be denied.
        
        If the value of the reference source port is a valid Port ID then the
        reference is a port.  If the value of the source hardware is a valid 
        non-zero HWIndex then the source is the hardware specified by the
        HWIndex."
    AUGMENTS    { tmnxCpmCardEntry }
    ::= { tmnxSyncIfTimingTable 1 }

TmnxSyncIfTimingEntry ::=
    SEQUENCE {
        tmnxSyncIfTimingRevert              TruthValue,
        tmnxSyncIfTimingRefOrder1           TmnxSETSRefSource,
        tmnxSyncIfTimingRefOrder2           TmnxSETSRefSource,
        tmnxSyncIfTimingRef1SrcPort         TmnxPortID,
        tmnxSyncIfTimingRef1AdminStatus     TmnxPortAdminStatus,
        tmnxSyncIfTimingRef1InUse           TruthValue,
        tmnxSyncIfTimingRef1Qualified       TmnxSETSRefQualified,
        tmnxSyncIfTimingRef1Alarm           TmnxSETSRefAlarm,
        tmnxSyncIfTimingRef2SrcPort         TmnxPortID,
        tmnxSyncIfTimingRef2AdminStatus     TmnxPortAdminStatus,
        tmnxSyncIfTimingRef2InUse           TruthValue,
        tmnxSyncIfTimingRef2Qualified       TmnxSETSRefQualified,
        tmnxSyncIfTimingRef2Alarm           TmnxSETSRefAlarm,
        tmnxSyncIfTimingFreqOffset          Integer32,
        tmnxSyncIfTimingStatus              INTEGER,
        tmnxSyncIfTimingRefOrder3           TmnxSETSRefSource,
        tmnxSyncIfTimingBITSIfType          TmnxBITSIfType,
        tmnxSyncIfTimingBITSAdminStatus     TmnxPortAdminStatus,
        tmnxSyncIfTimingBITSInUse           TruthValue,
        tmnxSyncIfTimingBITSQualified       TmnxSETSRefQualified,
        tmnxSyncIfTimingBITSAlarm           TmnxSETSRefAlarm,
        tmnxSyncIfTimingRef1SrcHw           TmnxHwIndexOrZero,
        tmnxSyncIfTimingRef1BITSIfType      TmnxBITSIfType,
        tmnxSyncIfTimingRef2SrcHw           TmnxHwIndexOrZero,
        tmnxSyncIfTimingRef2BITSIfType      TmnxBITSIfType
    }

tmnxSyncIfTimingRevert      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRevert indicates whether the reference
         source will revert to a higher priority source that has been
         re-validated or newly validated.  
         
         The synchronous interface timing subsystem is by default non-revertive
         ('false')."
    ::= { tmnxSyncIfTimingEntry 1 }

tmnxSyncIfTimingRefOrder1      OBJECT-TYPE
    SYNTAX      TmnxSETSRefSource
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRefOrder1 indicates the most preferred
         timing reference source.
         
         The synchronous equipment timing subsystem can lock to three
         different timing reference inputs, reference1, reference2 and bits.
         The subsystem chooses a reference based on priority."
    ::= { tmnxSyncIfTimingEntry 2 }

tmnxSyncIfTimingRefOrder2      OBJECT-TYPE
    SYNTAX      TmnxSETSRefSource
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRefOrder2 indicates the second most
          preferred timing reference for the synchronous equipment timing
          subsystem."
    ::= { tmnxSyncIfTimingEntry 3 }

tmnxSyncIfTimingRef1SrcPort      OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef1SrcPort indicates the source port of
         the first timing reference.
        
         A value of '1e000000'H indicates that there is no source port for this
         reference."
    ::= { tmnxSyncIfTimingEntry 4 }

tmnxSyncIfTimingRef1AdminStatus      OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef1AdminStatus indicates the
         administrative status of the first timing reference."
    ::= { tmnxSyncIfTimingEntry 5 }

tmnxSyncIfTimingRef1InUse      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef1InUse indicates whether the first
         timing reference is presently being used by the synchronous timing
         subsystem.  If it is in use, tmnxSyncIfTimingFreqOffset indicates
         the frequency offset for this reference."
    ::= { tmnxSyncIfTimingEntry 6 }

tmnxSyncIfTimingRef1Qualified      OBJECT-TYPE
    SYNTAX      TmnxSETSRefQualified
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef1Qualified indicates whether the first
         timing reference is qualified for use by the synchronous timing
         subsystem.  If tmnxSyncIfTimingRef1Qualified is set to 'not-qualified',
         then the object tmnxSyncIfTimingRef1Alarm gives the reason for 
         disqualification."
    ::= { tmnxSyncIfTimingEntry 7 }

tmnxSyncIfTimingRef1Alarm      OBJECT-TYPE
    SYNTAX      TmnxSETSRefAlarm
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef1Alarm indicates the alarms on the
         first reference.  If any of the bits is set to '1', then the first
         reference is disqualified by the timing subsystem and the value of 
         tmnxSyncIfTimingRef1Qualified is set to 'not-qualified'.
         los    - loss of signal
         oof    - out of frequency range
         oopir  - out of pull in range
        "
    ::= { tmnxSyncIfTimingEntry 8 }

tmnxSyncIfTimingRef2SrcPort      OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef2SrcPort indicates the source port of
         the second timing reference.
         
         A value of '1e000000'H indicates that there is no source port for this
         reference."
    ::= { tmnxSyncIfTimingEntry 9 }

tmnxSyncIfTimingRef2AdminStatus      OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef2AdminStatus indicates the
         administrative status of the second timing reference."
    ::= { tmnxSyncIfTimingEntry 10 }      

tmnxSyncIfTimingRef2InUse      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef2InUse indicates whether the second
         timing reference is presently being used by the synchronous timing
         subsystem."
    ::= { tmnxSyncIfTimingEntry 11 }

tmnxSyncIfTimingRef2Qualified      OBJECT-TYPE
    SYNTAX      TmnxSETSRefQualified
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef2Qualified indicates whether the
         second timing reference is qualified for use by the synchronous
         timing subsystem.  If tmnxSyncIfTimingRef2Qualified is 'not-qualified'
         then the object tmnxSyncIfTimingRef2Alarm gives the reason for 
         disqualification."
    ::= { tmnxSyncIfTimingEntry 12 }

tmnxSyncIfTimingRef2Alarm      OBJECT-TYPE
    SYNTAX      TmnxSETSRefAlarm
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef2Alarm indicates the alarms on the
         second reference.  If any of the bits is set to '1', then the second
         reference is disqualified by the timing subsystem and the value of 
         tmnxSyncIfTimingRef2Qualified is set to 'not-qualified'.
         los    - loss of signal
         oof    - out of frequency range
         oopir  - out of pull in range
        "
    ::= { tmnxSyncIfTimingEntry 13 }

tmnxSyncIfTimingFreqOffset      OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "parts-per-million"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingFreqOffset indicates the frequency offset
         of the current selected timing reference in parts per million (ppm)."
    ::= { tmnxSyncIfTimingEntry 14 }

tmnxSyncIfTimingStatus      OBJECT-TYPE
    SYNTAX      INTEGER
                {
                    not-present (1),
                    master-freerun (2),
                    master-holdover (3),
                    master-locked (4),
                    slave (5),
                    acquiring (6)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingStatus indicates the present status of
         the synchronous timing equipment subsystem (SETS)."
    ::= { tmnxSyncIfTimingEntry 15 }

tmnxSyncIfTimingRefOrder3      OBJECT-TYPE
    SYNTAX      TmnxSETSRefSource
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRefOrder3 is the third most preferred
         timing reference for the synchronous equipment timing subsystem."
    ::= { tmnxSyncIfTimingEntry 16 }

tmnxSyncIfTimingBITSIfType      OBJECT-TYPE
    SYNTAX      TmnxBITSIfType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingBITSIfType indicates the interface type
         of the BITS (Building Integrated Timing Supply) timing reference. It
         also indicates the framing type of the interface."
    ::= { tmnxSyncIfTimingEntry 17 }

tmnxSyncIfTimingBITSAdminStatus      OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingBITSAdminStatus indicates administrative 
         status of the BITS (Building Integrated Timing Supply) timing 
         reference."
    ::= { tmnxSyncIfTimingEntry 18 }

tmnxSyncIfTimingBITSInUse      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingBITSInUse indicates whether the BITS
         timing reference is presently being used by the synchronous timing
         subsystem.  If it is in use, tmnxSyncIfTimingFreqOffset indicates
         the frequency offset for this reference."
    ::= { tmnxSyncIfTimingEntry 19 }

tmnxSyncIfTimingBITSQualified      OBJECT-TYPE
    SYNTAX      TmnxSETSRefQualified
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingBITSQualified indicates whether the BITS
         timing reference is qualified for use by the synchronous timing
         subsystem.  If tmnxSyncIfTimingBITSQualified is 'not-qualified', then
         the object tmnxSyncIfTimingBITSAlarm gives the reason for 
         disqualification."
    ::= { tmnxSyncIfTimingEntry 20 }

tmnxSyncIfTimingBITSAlarm      OBJECT-TYPE
    SYNTAX      TmnxSETSRefAlarm
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingBITSAlarm indicates the alarms on the
         BITS reference.  If any of the bits is set to '1', then the BITS
         reference is disqualified by the timing subsystem and the value of 
         tmnxSyncIfTimingBITSQualified is set to 'not-qualified'."
    ::= { tmnxSyncIfTimingEntry 21 }

tmnxSyncIfTimingRef1SrcHw      OBJECT-TYPE
    SYNTAX      TmnxHwIndexOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef1SrcHw indicates the source HW
         index of the first timing reference if source is not a port."
    ::= { tmnxSyncIfTimingEntry 22 }

tmnxSyncIfTimingRef1BITSIfType      OBJECT-TYPE
    SYNTAX      TmnxBITSIfType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef1BITSIfType indicates the interface
         type of the first timing reference if the source is BITS.  It also
         indicates the framing type of the interface."
    ::= { tmnxSyncIfTimingEntry 23 }

tmnxSyncIfTimingRef2SrcHw      OBJECT-TYPE
    SYNTAX      TmnxHwIndexOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef2SrcHw indicates the source HW
         index of the second timing reference if source is not a port."
    ::= { tmnxSyncIfTimingEntry 24 }

tmnxSyncIfTimingRef2BITSIfType      OBJECT-TYPE
    SYNTAX      TmnxBITSIfType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxSyncIfTimingRef2BITSIfType indicates the interface
         type of the second timing reference if the source is BITS. It also
         indicates the framing type of the interface."
    ::= { tmnxSyncIfTimingEntry 25 }


--
-- Administrative value objects
--
tmnxChassisAdminCtrlObjs  OBJECT IDENTIFIER ::= { tmnxChassisAdminObjects 1 }
tmnxChassisAdminValueObjs OBJECT IDENTIFIER ::= { tmnxChassisAdminObjects 2 }

--
-- Admin Synchoronous Interface Timing table
--

tSyncIfTimingAdmTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF TSyncIfTimingAdmEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Values for the synchronous interface timing for the chassis."
    ::= { tmnxChassisAdminValueObjs 1 }

tSyncIfTimingAdmEntry    OBJECT-TYPE
    SYNTAX      TSyncIfTimingAdmEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "Information about the synchronous interface timing.
        Entries are created by user.
        Entries are deleted by user.
        Note that the first Alcatel 7x50 SR series product release does 
        not support configuration of synchronous timing equipment on the
        secondary CPM. All SNMP operations with tmnxCpmCardSlotNum of the
        secondary CPM will be denied.
        
        The 7x50 systems supports 3 timing references (reference1, reference2
        and bits).
        
        The 7710 system only supports 2 timing references (reference1 and
        reference2).  On 7710 system, references can be a source port
        or a BITS input on a CES CMA.  If the value of the reference source
        port is a valid Port ID then the reference is a source port.  If the
        value of the source hardware is a valid HWIndex of a CES CMA then the
        source is a BITS on the CES CMA."        
    INDEX       { tmnxChassisIndex, tmnxCpmCardSlotNum, tmnxCpmCardNum }
    ::= { tSyncIfTimingAdmTable 1 }

TSyncIfTimingAdmEntry ::=
    SEQUENCE {
        tSyncIfTimingAdmRevert              TruthValue,
        tSyncIfTimingAdmRefOrder1           TmnxSETSRefSource,
        tSyncIfTimingAdmRefOrder2           TmnxSETSRefSource,
        tSyncIfTimingAdmRef1SrcPort         TmnxPortID,
        tSyncIfTimingAdmRef1AdminStatus     TmnxPortAdminStatus,
        tSyncIfTimingAdmRef2SrcPort         TmnxPortID,
        tSyncIfTimingAdmRef2AdminStatus     TmnxPortAdminStatus,
        tSyncIfTimingAdmChanged             Unsigned32,
        tSyncIfTimingAdmRefOrder3           TmnxSETSRefSource,
        tSyncIfTimingAdmBITSIfType          TmnxBITSIfType,
        tSyncIfTimingAdmBITSAdminStatus     TmnxPortAdminStatus,
        tSyncIfTimingAdmRef1SrcHw           TmnxHwIndexOrZero,
        tSyncIfTimingAdmRef1BITSIfType      TmnxBITSIfType,
        tSyncIfTimingAdmRef2SrcHw           TmnxHwIndexOrZero,
        tSyncIfTimingAdmRef2BITSIfType      TmnxBITSIfType
    }

tSyncIfTimingAdmRevert      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRevert specifies whether the reference
         source will revert to a higher priority source that has been
         re-validated or newly validated.
         
         The synchronous interface timing subsystem is by default non-revertive
         ('false')."
    DEFVAL { false }
    ::= { tSyncIfTimingAdmEntry 1 }

tSyncIfTimingAdmRefOrder1      OBJECT-TYPE
    SYNTAX      TmnxSETSRefSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRefOrder1 specifies the most preferred
         timing reference source.
         
         The synchronous equipment timing subsystem can lock to three
         different timing reference inputs, reference1, reference2 and bits.
         The subsystem chooses a reference based on priority. 
         tSyncIfTimingAdmRefOrder1 is used to configure the most preferred 
         timing reference."
    DEFVAL { bits }
    ::= { tSyncIfTimingAdmEntry 2 }

tSyncIfTimingAdmRefOrder2      OBJECT-TYPE
    SYNTAX      TmnxSETSRefSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRefOrder2 specifies the second most 
         preferred timing reference for the synchronous equipment timing 
         subsystem."
    DEFVAL { reference1 }
    ::= { tSyncIfTimingAdmEntry 3 }

tSyncIfTimingAdmRef1SrcPort      OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef1SrcPort specifies the source port 
         of the first timing reference.

         This can only be set to a valid TmnxPortID if the value of
         tSyncIfTimingAdmRef1SrcHw is 0."
    DEFVAL { '1e000000'H }
    ::= { tSyncIfTimingAdmEntry 4 }

tSyncIfTimingAdmRef1AdminStatus      OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef1AdminStatus specifies the desired
         administrative status of the first timing reference."
    DEFVAL { outOfService }
    ::= { tSyncIfTimingAdmEntry 5 }      

tSyncIfTimingAdmRef2SrcPort      OBJECT-TYPE
    SYNTAX      TmnxPortID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef2SrcPort specifies the source port 
         of the second timing reference.

         This can only be set to a valid TmnxPortID if the value of
         tSyncIfTimingAdmRef2SrcHw is 0."
    DEFVAL { '1e000000'H }
    ::= { tSyncIfTimingAdmEntry 6 }

tSyncIfTimingAdmRef2AdminStatus      OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef2AdminStatus specifies the desired 
         administrative status of the second timing reference."
    DEFVAL { outOfService }
    ::= { tSyncIfTimingAdmEntry 7 }      

tSyncIfTimingAdmChanged      OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmChanged is a bitmask that indicates which
         objects have been set, but not committed.  bit values used here are:
        
         0x0001:    tSyncIfTimingAdmRevert
         0x0002:    tSyncIfTimingAdmRefOrder1
         0x0004:    tSyncIfTimingAdmRefOrder2
         0x0008:    tSyncIfTimingAdmRef1SrcPort
         0x0010:    tSyncIfTimingAdmRef1AdminStatus
         0x0020:    tSyncIfTimingAdmRef2SrcPort
         0x0040:    tSyncIfTimingAdmRef2AdminStatus
         0x0080:    tSyncIfTimingAdmRefOrder3
         0x0100:    tSyncIfTimingAdmBITSIfType
         0x0200:    tSyncIfTimingAdmBITSAdminStatus
         0x0400:    tSyncIfTimingAdmRef1SrcHw
         0x0800:    tSyncIfTimingAdmRef1BITSIfType
         0x1000:    tSyncIfTimingAdmRef2SrcHw
         0x2000:    tSyncIfTimingAdmRef2BITSIfType
        
         The agent sets these bits when an object in the row
         is set.  This object is cleared to zero by setting
         tmnxChassisAdminControlApply to initialize(2) or commit(3).
        "
     ::= { tSyncIfTimingAdmEntry 8 }      

tSyncIfTimingAdmRefOrder3      OBJECT-TYPE
    SYNTAX      TmnxSETSRefSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRefOrder3 indicates the third most 
         preferred timing reference for the synchronous equipment timing 
         subsystem."
    DEFVAL { reference2 }
    ::= { tSyncIfTimingAdmEntry 9 }

tSyncIfTimingAdmBITSIfType      OBJECT-TYPE
    SYNTAX      TmnxBITSIfType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmBITSIfType specifies the interface type
         of the BITS (Building Integrated Timing Supply) timing reference."
    DEFVAL { t1-esf }
    ::= { tSyncIfTimingAdmEntry 10 }

tSyncIfTimingAdmBITSAdminStatus      OBJECT-TYPE
    SYNTAX      TmnxPortAdminStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmBITSAdminStatus specifies the desired 
         administrative status of the BITS (Building Integrated Timing Supply)
         timing reference."
    DEFVAL { outOfService }
    ::= { tSyncIfTimingAdmEntry 11 }

tSyncIfTimingAdmRef1SrcHw      OBJECT-TYPE
    SYNTAX      TmnxHwIndexOrZero
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef1SrcHw specifies the source HW 
         Index of the first timing reference if the source is not a port.

         This can only be set to a valid HW Index if the value of
         tSyncIfTimingAdmRef1SrcPort is '1e000000'H."
    DEFVAL { 0 }
    ::= { tSyncIfTimingAdmEntry 12 }

tSyncIfTimingAdmRef1BITSIfType      OBJECT-TYPE
    SYNTAX      TmnxBITSIfType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef1BITSIfType specifies the interface
         type of the first timing reference if the source is BITS."
    DEFVAL { t1-esf }
    ::= { tSyncIfTimingAdmEntry 13 }

tSyncIfTimingAdmRef2SrcHw      OBJECT-TYPE
    SYNTAX      TmnxHwIndexOrZero
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef2SrcHw specifies the source HW 
         Index of the second timing reference if the source is not a port.

         This can only be set to a valid HW Index if the value of
         tSyncIfTimingAdmRef2SrcPort is '1e000000'H."
    DEFVAL { 0 }
    ::= { tSyncIfTimingAdmEntry 14 }

tSyncIfTimingAdmRef2BITSIfType      OBJECT-TYPE
    SYNTAX      TmnxBITSIfType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSyncIfTimingAdmRef2BITSIfType specifies the interface
         type of the second timing reference if the source is BITS."
    DEFVAL { t1-esf }
    ::= { tSyncIfTimingAdmEntry 15 }


--
-- Administrative value control objects
--

tmnxChassisAdminOwner  OBJECT-TYPE
    SYNTAX       SnmpAdminString
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "Who has last initialized the chassis administrative table, 
        who is making all the changes, and who is expected to 
        either commit or re-initialize (ABORT-TRANSACTION).
         
        tmnxChassisAdminOwner is advisory only. Before beginning a transaction,
        read tmnxChassisAdminOwner.  if it is empty then proceed with the 
        configuration.
        
        Set tmnxChassisAdminOwner after setting tmnxChassisAdminControlApply so
        that other users will be advised to not make changes to the Admin 
        tables.
        
        Agent sets tmnxChassisAdminOwner to empty string after 
        tmnxChassisAdminControlApply is set - either by user initializing or 
        committing, or by agent timing out the uncommitted transactions 
        (tmnxChassisAdminLastSetTimer).
        "
    ::= { tmnxChassisAdminCtrlObjs 1 }

tmnxChassisAdminControlApply  OBJECT-TYPE
    SYNTAX       INTEGER 
                 { 
                     none(1), 
                     initialize(2), 
                     commit(3) 
                 }
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "This object controls the use of tmnxChassisAdminTable.
        
        when set to initialize(2), the objects in tmnxChassisAdminTable
        are set to the current Operational values, from the tmnxChassisTable. 
        Any uncommitted changes are lost, so setting this value corresponds 
        to both BEGIN-TRANSACTION and ABORT-TRANSACTION.
        
        when set to commit(3) (END-TRANSACTION), all of the objects from 
        tmnxChassisAdminTable are copied to the corresponding tmnxChassisTable 
        table objects.        
        "
    ::= { tmnxChassisAdminCtrlObjs 2 }

tmnxChassisAdminLastSetTimer  OBJECT-TYPE
    SYNTAX       TimeInterval
    UNITS        "centiseconds"
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The time remaining until the agent re-initializes the 
        administrative tables.
       
        If tmnxChassisAdminControlApply is not set to commit(3) within 
        tmnxChassisAdminLastSetTimeout centiseconds, the agent will set it 
        to initialize(2) and all uncommitted changes will be lost.

        This way, uncommitted changes from failed (uncompleted) change sets 
        will eventually be removed, and another transaction can safely begin.
        
        this object is reset to tmnxChassisAdminLastSetTimeout after SNMP SET 
        operation to any of the tmnxChassisAdminValue tables.
        "
    ::= { tmnxChassisAdminCtrlObjs 3 }

tmnxChassisAdminLastSetTimeout  OBJECT-TYPE
    SYNTAX       TimeInterval
    UNITS        "centiseconds"
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "Timeout for tmnxChassisAdminLastSetTimer.
         The value zero is not allowed.
        "
    DEFVAL { 180000 }
    ::= { tmnxChassisAdminCtrlObjs 4 }


--
--      Cross Connect Aggregation Group Table
--

tmnxCcagTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCcagEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCcagTable has an entry for each Cross Connect Aggregation
         Group,CCAG, configured on this system."
    ::= { tmnxCardObjs 12 }

tmnxCcagEntry    OBJECT-TYPE
    SYNTAX      TmnxCcagEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a particular CCAG. 
         Entries are created/deleted by the user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tmnxCcagId }
    ::= { tmnxCcagTable 1}

TmnxCcagEntry ::= SEQUENCE 
{
    tmnxCcagId                  TmnxCcagId,
    tmnxCcagRowStatus           RowStatus,
    tmnxCcagLastChanged         TimeStamp,
    tmnxCcagDescription         DisplayString,
    tmnxCcagAdminStatus         TmnxAdminState,
    tmnxCcagOperStatus          TmnxOperState,
    tmnxCcagCcaRate             TmnxCcagRate,
    tmnxCcagAccessAdaptQos      INTEGER
}

tmnxCcagId         OBJECT-TYPE
    SYNTAX      TmnxCcagId
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagId is used to index into the 
         tmnxCcagTable. It uniquely identifies a CCAG entry
         as configured on this system."
    ::= { tmnxCcagEntry 1 }

tmnxCcagRowStatus        OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagRowStatus specifies the row status. It
         allows entries to be created and deleted in the tmnxCcagTable.
         tmnxCcagRowStatus does not support createAndWait. The status 
         can only be active or notInService."
    ::= { tmnxCcagEntry 2 }

tmnxCcagLastChanged     OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagLastChanged indicates the time this row
         was last changed."
    ::= { tmnxCcagEntry 3 }

tmnxCcagDescription        OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagDescription specifies a user provided
         description string of this CCAG entry."
    DEFVAL      { ''H }
    ::= { tmnxCcagEntry 4 }

tmnxCcagAdminStatus     OBJECT-TYPE
    SYNTAX      TmnxAdminState
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagAdminStatus specifies the desired state of this
         CCAG."
    DEFVAL      { inService }
    ::= { tmnxCcagEntry 5 }

tmnxCcagOperStatus      OBJECT-TYPE
    SYNTAX      TmnxOperState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagOperStatus indicates the operational state of this
         CCAG."
    ::= { tmnxCcagEntry 6 }

tmnxCcagCcaRate        OBJECT-TYPE
    SYNTAX      TmnxCcagRate
    UNITS       "kilobits per second"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagCcaRate specifies the maximum forwarding rate
         for each CCA member within the CCAG."
    DEFVAL { -1 }
    ::= { tmnxCcagEntry 7 }

tmnxCcagAccessAdaptQos  OBJECT-TYPE
    SYNTAX      INTEGER
                {
                   link (1),
                   distribute (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagAccessAdaptQos specifies how the CCAG SAP queue
         and virtual scheduler buffering and rate parameters are adapted over
         multiple active CCAs.
         link           (1) - The CCAG will create the SAP queues and virtual
                              schedulers on each CCA with the actual parameters
                              specified in the tmnxCcagPathCcTable.
         distribute     (2) - Each CCA will receive a portion of the parameters
                              specified in the tmnxCcagPathCcTable."
    DEFVAL { distribute }
    ::= { tmnxCcagEntry 8 }

--
--      Cross Connect Aggregation Group Path Table
--

tmnxCcagPathTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCcagPathEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCcagPathTable has an entry for each Cross Connect
         Aggregation Group, CCAG, path configured on this system."
    ::= { tmnxCardObjs 13 }

tmnxCcagPathEntry    OBJECT-TYPE
    SYNTAX      TmnxCcagPathEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a particular CCAG Path.
         Entries are created/deleted by the user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tmnxCcagId, tmnxCcagPathId }
    ::= { tmnxCcagPathTable 1}

TmnxCcagPathEntry ::= SEQUENCE 
{
    tmnxCcagPathId              INTEGER,
    tmnxCcagPathLastChanged     TimeStamp,
    tmnxCcagPathRate            TmnxCcagRate,
    tmnxCcagPathRateOption      TmnxCcagRateOption,
    tmnxCcagPathWeight          Unsigned32
}

tmnxCcagPathId        OBJECT-TYPE
    SYNTAX      INTEGER {
                    alpha (1),
                    beta  (2)
                }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathId is used as the secondary index into
         the tmnxCcagPathTable. Along with tmnxCcagId, it uniquely identifies
         a specific path, alpha or beta, on a CCAG."
    ::= { tmnxCcagPathEntry 1 }

tmnxCcagPathLastChanged       OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathLastChanged indicates the time this row
         was last changed."
    ::= { tmnxCcagPathEntry 2 }

tmnxCcagPathRate        OBJECT-TYPE
    SYNTAX      TmnxCcagRate
    UNITS       "kilobits per second"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathRate specifies the bandwidth rate
         limitation for this path on each member cross connect
         adaptor, CCA, in the CCAG."
    DEFVAL { -1 } 
    ::= { tmnxCcagPathEntry 3 }

tmnxCcagPathRateOption        OBJECT-TYPE
    SYNTAX      TmnxCcagRateOption
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathRateOption specifies whether the 
         rate in tmnxCcagPathRate is defined as an aggregate path
         rate for all CCAs in the CCAG or as a per CCA path
         rate."
    DEFVAL { aggregate } 
    ::= { tmnxCcagPathEntry 4 }

tmnxCcagPathWeight        OBJECT-TYPE
    SYNTAX      Unsigned32 (1..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathWeight specifies the scheduling
         percentage for this path. It is applied to all CCAs in
         the CCAG membership list for this path."
    DEFVAL { 50 }
    ::= { tmnxCcagPathEntry 5 }

--
--      CCAG Path Cross-Connect Table
--

tmnxCcagPathCcTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxCcagPathCcEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxCcagPathCcTable has an entry for each type of Cross
         Connection on a Cross Connect Aggregation Group Path
         configured on this system."
    ::= { tmnxCardObjs 14 }

tmnxCcagPathCcEntry    OBJECT-TYPE
    SYNTAX      TmnxCcagPathCcEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a particular CCAG Path Cross Connect.
         Entries are created/deleted by the user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tmnxCcagId, tmnxCcagPathId, tmnxCcagPathCcType }
    ::= { tmnxCcagPathCcTable 1}

TmnxCcagPathCcEntry ::= SEQUENCE 
{
    tmnxCcagPathCcType                  INTEGER,
    tmnxCcagPathCcLastChanged           TimeStamp,
    tmnxCcagPathCcEgrPoolResvCbs        INTEGER,
    tmnxCcagPathCcEgrPoolSlpPlcy        TNamedItem,
    tmnxCcagPathCcIngPoolResvCbs        INTEGER,
    tmnxCcagPathCcIngPoolSlpPlcy        TNamedItem,
    tmnxCcagPathCcAcctPolicyId          Unsigned32,
    tmnxCcagPathCcCollectStats          TruthValue,
    tmnxCcagPathCcQueuePlcy             TNamedItem,
    tmnxCcagPathCcMac                   MacAddress,
    tmnxCcagPathCcMtu                   Unsigned32,
    tmnxCcagPathCcUserAssignedMac       TruthValue,
    tmnxCcagPathCcHwMac                 MacAddress
}

tmnxCcagPathCcType        OBJECT-TYPE
    SYNTAX      INTEGER {
                    sapsap (1),
                    sapnet (2),
                    netsap (3)
                }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcType is used as a third index into
         the tmnxCcagPathCcTable. Along with tmnxCcagId and tmnxCcagPathId,
         it uniquely identifies a cross connection type on a specific path
         in a particular CCAG. The types are:
         sapsap (1): the cross connection is between two saps, where both 
                     services are access.
         sapnet (2): the cross connection is between a sap and a network 
                     service.
         netsap (3): the cross connection is between a network and a sap 
                     service."
    ::= { tmnxCcagPathCcEntry 1 }

tmnxCcagPathCcLastChanged       OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcLastChanged indicates the time this row
         was last changed."
    ::= { tmnxCcagPathCcEntry 2 }

tmnxCcagPathCcEgrPoolResvCbs        OBJECT-TYPE
    SYNTAX      INTEGER (-1|0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcEgrPoolResvCbs specifies the percentage 
         of pool size reserved for the committed burst size, CBS. The value '-1'
         implies that the reserved CBS should be computed as the sum of 
         the CBS requested by the entities using this pool if the application
         point is 'network'. For 'access' application points the value '-1'
         means a default of 30%."
    DEFVAL { -1 }
    ::= { tmnxCcagPathCcEntry 3 }

tmnxCcagPathCcEgrPoolSlpPlcy        OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcEgrPoolSlpPlcy specifies the slope 
         policy being used for the egress pool. The Slope policies define the 
         nature of the RED Slopes for the high and the low priority traffic."
    DEFVAL { "default" }  
    ::= { tmnxCcagPathCcEntry 4 }

tmnxCcagPathCcIngPoolResvCbs        OBJECT-TYPE
    SYNTAX      INTEGER (-1|0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcIngPoolResvCbs specifies the percentage 
         of pool size reserved for the committed burst size, CBS. The value '-1'
         implies that the reserved CBS should be computed as the sum of 
         the CBS requested by the entities using this pool if the application
         point is 'network'. For 'access' application points the value '-1'
         means a default of 30%. tmnxCcagPathCcIngPoolResvCbs does not apply 
         to tmnxCcagPathCcType 'netsap'."
    DEFVAL { -1 }
    ::= { tmnxCcagPathCcEntry 5 }

tmnxCcagPathCcIngPoolSlpPlcy        OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcIngPoolSlpPlcy specifies the slope policy 
         being used for the ingress pool. The Slope policies define the nature 
         of the RED Slopes for the high and the low priority traffic. 
         tmnxCcagPathCcIngPoolSlpPlcy does not apply to tmnxCcagPathCcType 
         'netsap'."
    DEFVAL { "default" }  
    ::= { tmnxCcagPathCcEntry 6 }

tmnxCcagPathCcAcctPolicyId        OBJECT-TYPE
    SYNTAX      Unsigned32 (0..99)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcAcctPolicyId specifies the accounting 
         policy which must be defined prior to associating it with the port. 
         A non-zero value indicates the tmnxLogApPolicyId index identifying the
         policy entry in the tmnxLogApTable from the TIMETRA-LOG-MIB which is
         associated with this port.  A zero value indicates that there is no
         accounting policy associated with this port. It is only meaningful 
         when the tmnxCcagPathCcType is 'netsap'."
    DEFVAL { 0 }
    ::= { tmnxCcagPathCcEntry 7 }

tmnxCcagPathCcCollectStats        OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcCollectStats specifies whether the collection 
         of accounting and statistical data for the network port is 
         enabled/disabled, 'true'/'false'. When applying accounting policies the 
         data by default will be collected in the appropriate records and 
         written to the designated billing file. 
         When the value is set to false, the statistics are still accumulated 
         by the IOM cards, however, the CPU will not obtain the results and
         write them to the billing file. If the value of tmnxCcagPathCcType is 
         not 'netsap', the value of this object is meaningless and an attempt 
         to set it will result in an inconsistentValue error."
    DEFVAL { false }
    ::= { tmnxCcagPathCcEntry 8 }

tmnxCcagPathCcQueuePlcy        OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcQueuePlcy specifies the network egress
         queue policy. If the value of tmnxCcagPathCcType is 
         not 'netsap', the value of this object is meaningless and an attempt 
         to set it will result in an inconsistentValue error."
    DEFVAL { "default" }
    ::= { tmnxCcagPathCcEntry 9 }

tmnxCcagPathCcMac       OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcMac specifies the MAC address of 
         the virtual LAG that maps to tmnxCcagPathId and tmnxCcagPathCcType.
         The default value of this object is derived from the chassis MAC
         address pool."
    DEFVAL {'000000000000'h }
    ::= { tmnxCcagPathCcEntry 10 }

tmnxCcagPathCcMtu       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcMtu specifies the MTU of the path
         indexed by tmnxCcagId, tmnxCcagPathId, and tmnxCcagPathCcType.
         When the value is '0', the real MTU is calculated internally."
    DEFVAL { 0 }
    ::= { tmnxCcagPathCcEntry 11 }

tmnxCcagPathCcUserAssignedMac OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcUserAssignedMac indicates whether
         the value of tmnxCcagPathCcMac has been explicitly assigned
         or inherited from tmnxCcagPathCcHwMac, 'true' and 'false',
         respectively."
    DEFVAL { false }
    ::= { tmnxCcagPathCcEntry 12 }

tmnxCcagPathCcHwMac     OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxCcagPathCcHwMac is the system assigned MAC 
         address of the virtual LAG that maps to tmnxCcagPathId and 
         tmnxCcagPathCcType. When tmnxCcagPathCcUserAssignedMac is 
         'false', tmnxCcagPathCcMac inherits its value from this object." 
    ::= { tmnxCcagPathCcEntry 13 }


--
--  Alcatel 7710 SR series Mda Carrier Module (MCM) Table
--

tmnxMcmTable    OBJECT-TYPE
    SYNTAX  SEQUENCE OF TmnxMcmEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxMcmTable has an entry for each Mda Carrier module
         (MCM) on the 7710 system."
    ::= { tmnxCardObjs 15 }

tmnxMcmEntry       OBJECT-TYPE
    SYNTAX      TmnxMcmEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents a MCM in a chassis in the 7710 system.
         Entries in the table cannot be created and deleted via SNMP SET
         operations.  When a tmnxChassisEntry is created, a
         tmnxMcmEntry is created in the chassis.  Before a
         tmnxChassisEntry can be deleted, each tmnxMcmEntry
         for the chassis must be in the proper state for removal."
    INDEX   { tmnxChassisIndex, tmnxCardSlotNum, tmnxMcmSlotNum }
    ::= { tmnxMcmTable 1 }

TmnxMcmEntry ::=
    SEQUENCE {
        tmnxMcmSlotNum              Unsigned32,
        tmnxMcmSupportedTypes       TmnxMcmType,
        tmnxMcmAssignedType         TmnxMcmType,
        tmnxMcmEquippedType         TmnxMcmType,
        tmnxMcmHwIndex              TmnxHwIndex
    }

tmnxMcmSlotNum     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this MDA slot within a specific
         IOM card in the system.  Since the MCM occupies two MDA slots in
         the chassis this value can only be an odd number."
    ::= { tmnxMcmEntry 1 }
    
tmnxMcmSupportedTypes  OBJECT-TYPE
    SYNTAX      TmnxMcmType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bit-mask that specifies what MCM types can be physically supported
         in this chassis."
    ::= { tmnxMcmEntry 2 }

tmnxMcmAssignedType        OBJECT-TYPE
    SYNTAX      TmnxMcmType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A bit-mask that identifies the administratively assigned 
         (pre-provisioned) MCM type that should occupy this chassis.
         If tmnxMcmAssignedType has a value of 'unassigned',
         this slot has not yet been pre-provisioned.  There must not be more
         than one bit set at a time in tmnxMcmAssignedType."
    DEFVAL { 1 }
    ::= { tmnxMcmEntry 3 }

tmnxMcmEquippedType        OBJECT-TYPE
    SYNTAX      TmnxMcmType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bit-mask that identifies the MCM type that is physically
         inserted into this chassis.  If the chassis has been pre-provisioned,
         tmnxMcmAssignedType is not equal 'unassigned', and the
         value of tmnxMcmEquippedType is not the same as
         tmnxMcmAssignedType, a mis-match alarm will be raised.
         If the chassis has not been pre-provisioned, and the value of
         tmnxMcmEquippedType is not one of the supported types as
         specified by tmnxMcmSupportedTypes, a mis-match alarm will
         be raised.  There will not be more than one bit set at a time in
         tmnxMcmEquippedType."
    ::= { tmnxMcmEntry 4 }

tmnxMcmHwIndex      OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxMcmHwIndex is the index into the
         tmnxHwTable for the row entry that represents the hardware component
         information for this MCM."
    ::= { tmnxMcmEntry 5 }

--
--  Mda Carrier Module Type (MCM) Definition Table
--
tmnxMcmTypeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxMcmTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The card type table has an entry for each Alcatel 7710 series
         Mda Carrier Module (MCM) model."
    ::= { tmnxCardObjs 16 }

tmnxMcmTypeEntry    OBJECT-TYPE
    SYNTAX      TmnxMcmTypeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row entry represents an Alcatel 7710 series MCM model.  
         Rows in this table are created by the agent at initialization and
         cannot be created or destroyed by SNMP Get or Set requests."
    INDEX   { tmnxMcmTypeIndex }
    ::= { tmnxMcmTypeTable 1 }

TmnxMcmTypeEntry ::=
    SEQUENCE {
        tmnxMcmTypeIndex        TmnxMcmType,
        tmnxMcmTypeName         TNamedItemOrEmpty,
        tmnxMcmTypeDescription  TItemDescription,
        tmnxMcmTypeStatus       TruthValue
    }

tmnxMcmTypeIndex    OBJECT-TYPE
    SYNTAX      TmnxMcmType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The unique index value which identifies this type of Alcatel
         7710 series MCM model."
    ::= { tmnxMcmTypeEntry 1 }
        
tmnxMcmTypeName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The administrative name that identifies this type of Alcatel
         7710 series MCM model.  This name string may be used in CLI
         commands to specify a particular card model type."
    ::= { tmnxMcmTypeEntry 2 }

tmnxMcmTypeDescription  OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A detailed description of this Alcatel 7710 series MCM model."
    ::= { tmnxMcmTypeEntry 3 }         

tmnxMcmTypeStatus       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxMcmTypeStatus has a value of 'true' it
        indicates that this MCM is supported in this revision of the
        management software.  When it has a value of 'false' there is no
        support."
    ::= { tmnxMcmTypeEntry 4 }


--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
 --
 --     Notification Definition section
 --
 --                     Notification Objects
 --
 
 tmnxEqNotificationRow  OBJECT-TYPE
    SYNTAX       RowPointer
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "used by tmnx chassis Notifications, the OID
        indicates the table and entry."
    ::= { tmnxChassisNotificationObjects 1 }

 tmnxEqTypeNotificationRow  OBJECT-TYPE
    SYNTAX      RowPointer
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "used by tmnx chassis notifications, the OID indicates the
         table and entry with the equipment model type information."
    ::= { tmnxChassisNotificationObjects 2 }

 tmnxChassisNotifyChassisId  OBJECT-TYPE
    SYNTAX      TmnxChassisIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnx chassis and port Notifications, indicates the chassis
         associated with the alarm."
    ::= { tmnxChassisNotificationObjects 3 }

 tmnxChassisNotifyHwIndex       OBJECT-TYPE
    SYNTAX      TmnxHwIndex
    MAX-ACCESS  accessible-for-notify
    STATUS  current
    DESCRIPTION
        "Used by tmnx chassis and port Notifications, indicates the entry
         in the tmnxHwTable for the hardware component associated with an
         alarm."
    ::= { tmnxChassisNotificationObjects 4 }

tmnxRedSecondaryCPMStatus       OBJECT-TYPE
    SYNTAX      INTEGER {
                    online (1),
                    offline (2),
                    fail (3)
                }
    MAX-ACCESS  accessible-for-notify
    STATUS  current
    DESCRIPTION
        "Used by the tmnxRedSecondaryCPMStatusChange Notification, indicates 
         the status of the secondary CPM."
    ::= { tmnxChassisNotificationObjects 5 }

tmnxChassisNotifyOID  OBJECT-TYPE
    SYNTAX       OBJECT IDENTIFIER
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "Used by the tmnxChassisNotificationClear trap, the OID 
         identifies the trap which is getting cleared."
    ::= { tmnxChassisNotificationObjects 6 }

tmnxSyncIfTimingNotifyAlarm  OBJECT-TYPE
    SYNTAX      INTEGER {
                    notUsed (0),
                    los (1),
                    oof (2),
                    oopir (3)
                }
    MAX-ACCESS   accessible-for-notify
    STATUS       current
    DESCRIPTION
        "Used by tmnx Synchronous interface timing notifications, the value of
         tmnxSyncIfTimingNotifyAlarm indicates the reason a timing reference
         alarm has been raised."
    ::= { tmnxChassisNotificationObjects 7 }

tmnxChassisNotifyMismatchedVer OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnxPeSoftwareVersionMismatch, the value of 
         tmnxChassisNotifyMismatchedVer indicates the software version of the
         mismatched CPM/IOM card."
    ::= { tmnxChassisNotificationObjects 8 }

tmnxChassisNotifySoftwareLocation OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnxPeSoftwareLoadFailed, the value of 
         tmnxChassisNotifySoftwareLocation contains the location of the 
         software."
    ::= { tmnxChassisNotificationObjects 9 }

tmnxChassisNotifyCardFailureReason OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnxEqCardFailure, the value of 
         tmnxChassisNotifyCardFailureReason contains the 
         reason for card failure."
    ::= { tmnxChassisNotificationObjects 10 }

tmnxChassisNotifyCardName   OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(1..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Used by tmnxEqCardInserted and tmnxEqCardRemoved, the value
         of tmnxChassisNotifyCardName specifies the name of the affected
         card."
    ::= { tmnxChassisNotificationObjects 11 }

--
--              ALCATEL-IND1-TIMETRA-CHASSIS-MIB Notifications
--

--
--              Hardware Configuration Change Alarm
--
 tmnxHwConfigChange NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "A tmnxHwConfigChange notification is generated when the value of
         tmnxHwLastChange is updated.  It can be used by the NMS to trigger
         maintenance polls of the hardware configuration information.
         
         Only one tmnxHwConfigChange notification event will be generated
         in a 5 second throttling time period.  A notification event is
         the transmission of a single trap to a list of notification
         destinations configured in the SNMP-TARGET-MIB.
         
         If additional hardware configuration change occurs within the
         throttling period, the notification events for these changes are
         suppressed until the throttling period expires.  At the end of
         the throttling period, one notification event is generated if
         any addition configuration changes occurred within the just
         completed throttling period and another throttling period is
         started.
         
         The NMS should periodically check the value of tmnxHwConfigChange
         to detect any missed tmnxHwConfigChange traps.

         This notification was made obsolete in the 2.1 release.
                        
         The tmnxHwConfigChange notification has been replaced 
         with the generic change notifications from the 
         TIMETRA-SYSTEM-MIB: tmnxConfigModify, tmnxConfigCreate, 
         tmnxConfigDelete, tmnxStateChange."
    ::= { tmnxChassisNotification 1 }


--
--  Environmental Alarms
--
 tmnxEnvTempTooHigh NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxHwTemperature,
        tmnxHwTempThreshold
    }
    STATUS  current
    DESCRIPTION
        "Generated when the temperature sensor reading on an equipment
         object is greater than its configured threshold."
    ::= { tmnxChassisNotification 2 }

--
--  Equipment Alarms
--

 tmnxEqPowerSupplyFailure   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisPowerSupplyACStatus,
        tmnxChassisPowerSupplyDCStatus,
        tmnxChassisPowerSupplyTempStatus,
        tmnxChassisPowerSupplyTempThreshold,
        tmnxChassisPowerSupply1Status,
        tmnxChassisPowerSupply2Status,
        tmnxChassisPowerSupplyInputStatus,
        tmnxChassisPowerSupplyOutputStatus
    }
    STATUS  current
    DESCRIPTION
        "Generated when one of the chassis's power supplies fails."
    ::= { tmnxChassisNotification 3 }

 tmnxEqPowerSupplyInserted   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "Generated when one of the chassis's power supplies is inserted."
    ::= { tmnxChassisNotification 4 }

 tmnxEqPowerSupplyRemoved   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "Generated when one of the chassis's power supplies is removed."
    ::= { tmnxChassisNotification 5 }

 tmnxEqFanFailure   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisFanOperStatus,
        tmnxChassisFanSpeed
    }
    STATUS  current
    DESCRIPTION
        "Generated when one of the fans in a fan tray has failed."
    ::= { tmnxChassisNotification 6 }

 tmnxEqCardFailure  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxHwOperState,
        tmnxChassisNotifyCardFailureReason
    }
    STATUS  current
    DESCRIPTION
        "Generated when one of the cards in a chassis has failed.  The card
         type may be IOM, Fabric, MDA, MCM, CCM, CPM module, compact flash
         module, etc. tmnxChassisNotifyCardFailureReason contains the reason
         for card failure."
    ::= { tmnxChassisNotification 7 }

 tmnxEqCardInserted NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifyCardName
    }
    STATUS  current
    DESCRIPTION
        "Generated when a card is inserted into the chassis.  The card type
         may be IOM, Fabric, MDA, MCM, CCM CPM module, compact flash module,
         etc."
    ::= { tmnxChassisNotification 8 }

 tmnxEqCardRemoved NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifyCardName
    }
    STATUS  current
    DESCRIPTION
        "Generated when a card is removed from the chassis.  The card type
         may be IOM, Fabric, MDA, MCM, CCM, CPM module, compact flash module,
         etc."
    ::= { tmnxChassisNotification 9 }

 tmnxEqWrongCard    NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "Generated when the wrong type of card is inserted into a slot of
         the chassis.  Even though a card may be physically supported by
         the slot, it may have been administratively configured to allow
         only certain card types in a particular slot location.  The card 
         type may be IOM, Fabric, MDA, MCM, CPM module, etc."
    ::= { tmnxChassisNotification 10 }

 tmnxEqCpuFailure   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a failure is detected for a CPU on an IOM card or
         CPM module.

         This notification was made obsolete in the 2.1 release.
                                            
         A cpu failure on a CPM card is detected by the hardware 
         bootup and is indicated by the boot diagnostic display.  
         If there is no working redundant CPM card, the system 
         does not come up.

         A failure of an IOM card or standby redundant CPM card 
         causes the tmnxEqCardFailure notification to be sent."
    ::= { tmnxChassisNotification 11 }

 tmnxEqMemoryFailure    NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a memory module failure is detected for an IOM card or
         CPM module.

         This notification was made obsolete in the 2.1 release.
                                            
         A failure of the memory device is detected by the 
         hardware bootup and is indicated by the boot diagnostic 
         display.  If there is no working redundant CPM card, 
         the system does not come up.

         A failure of the memory device during run-time causes 
         the system to fail and the 'admin tech-support' 
         information to be saved.

         A failure of an IOM card or standby redundant CPM card 
         causes the tmnxEqCardFailure notification to be sent."
    ::= { tmnxChassisNotification 12 }

 tmnxEqBackdoorBusFailure   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyChassisId
    }
    STATUS obsolete
    DESCRIPTION
        "Generated when the backdoor bus has failed.

         This notification was made obsolete in the 2.1 release."
    ::= { tmnxChassisNotification 13 }

--
--  Processing Error Alarms
--

 tmnxPeSoftwareError    NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a software error has been detected.

         This notification was made obsolete in the 2.1 release.
                                            
         Many of the other notifications more specifically 
         indicate detection of some type of software error.  

         The 'admin tech-support' information helps developers 
         diagnose a failure of the software in the field."
    ::= { tmnxChassisNotification 14 }
    
 tmnxPeSoftwareAbnormalHalt     NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when the software has abnormally terminated.

        This notification was made obsolete in the 2.1 release.
                        
         Many of the other notifications more specifically 
         indicate detection of some type of software error.  

         The 'admin tech-support' information helps developers 
         diagnose a failure of the software in the field."
    ::= { tmnxChassisNotification 15 }

 tmnxPeSoftwareVersionMismatch  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifyMismatchedVer,
        tmnxHwSoftwareCodeVersion
    }
    STATUS  current
    DESCRIPTION
        "Generated when there is a mismatch between software versions of the
         active CPM and standby CPM or the CPM and IOM.
         tmnxChassisNotifyHwIndex identifies the mismatched CPM/IOM card and
         tmnxChassisNotifyMismatchedVer will contain the version of the 
         mismatched card. The tmnxHwSoftwareCodeVersion object will contain
         the expected version."
    ::= { tmnxChassisNotification 16 }
                
 tmnxPeOutOfMemory  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when there is an out of memory error detected.

         This notification was made obsolete in the 2.1 release.
                                            
         The tmnxPeOutOfMemory notification has been replaced 
         with the module specific notification from the 
         TIMETRA-SYSTEM-MIB: tmnxModuleMallocFailed."
    ::= { tmnxChassisNotification 17 }
                
 tmnxPeConfigurationError   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a configuration error has been detected.

         This notification was made obsolete in the 2.1 release.
                                            
         Many other notifications more specifically indicate 
         detection of a configuration error.  In most cases the 
         SNMP SET request that tries to make an invalid 
         configuration results in an error response.

         In some cases the configuration parameters are valid 
         and the SNMP SET request succeeds but the system cannot 
         successfully apply the new parameters.  The affected 
         object may then put into an operational 'down' state.  
         A state change notification such as tmnxStateChange or 
         a more specific notification is sent to alert about the 
         problem.  

         For example, an attempt to create an event log with a 
         file-type destination when the specified cflash media is 
         full or not present results in TIMETRA-LOG-MIB 
         notifications tmnxLogSpaceContention, tmnxLogAdminLocFailed, 
         or tmnxLogBackupLocFailed."
    ::= { tmnxChassisNotification 18 }
                
 tmnxPeStorageProblem   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when there is a storage capacity problem.

         This notification was made obsolete in the 2.1 release.
                                            
         The only 'storage' devices on the SR7750 are the cflash 
         drives.  Cflash write errors cause a tmnxEqFlashDataLoss 
         notification to be sent.  The tmnxEqFlashDiskFull 
         notification is sent when the driver detects that the 
         cflash device is full."
    ::= { tmnxChassisNotification 19 }
                
 tmnxPeCpuCyclesExceeded    NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when the CPU cycle usage limit has been exceeded.

         This notification was made obsolete in the 2.1 release.
                                            
         It does not apply. The SR7750 software architecture does 
         not restrict CPU cycles used by a specific code module."
    ::= { tmnxChassisNotification 20 }

--
--  Redundancy notifications
--

tmnxRedPrimaryCPMFail   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS current
    DESCRIPTION
        "Generated when the primary CPM fails."
    ::= { tmnxChassisNotification 21 }

tmnxRedSecondaryCPMStatusChange   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxRedSecondaryCPMStatus
    }
    STATUS obsolete
    DESCRIPTION
        "Generated when there is a change in the secondary CPM status.

         This notification was made obsolete in the 2.1 release.
                                            
         There is no way to administratively enable or disable
         CPM cards so there is no need for a status change event
         for administrative state changes.
                     
         Operational changes detected about the standby CPM 
         card are indicated by more specific notifications such 
         as tmnxEqCardFailure, tmnxEqCardRemoved, tmnxEqCardInserted
         TIMETRA-SYSTEM-MIB::ssiRedStandbyReady, 
         TIMETRA-SYSTEM-MIB::ssiRedStandbySyncLost, and
         TIMETRA-SYSTEM-MIB::ssiRedStandbySyncLost."
    ::= { tmnxChassisNotification 22 }

tmnxRedRestoreSuccess   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS obsolete
    DESCRIPTION
        "Generated when the secondary CPM successfully restores 
         the config and state.

         This notification was made obsolete in the 2.1 release.
                                            
         It does not apply. This event was originally created
         for an early redundancy mechanism that was never
         released."
    ::= { tmnxChassisNotification 23 }

tmnxRedRestoreFail   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS obsolete
    DESCRIPTION
        "Generated when the secondary CPM fails to 
         restore the config and state.

         This notification was made obsolete in the 2.1 release.
                                            
         It does not apply. This event was originally created
         for an early redundancy mechanism that was never
         released."
    ::= { tmnxChassisNotification 24 }

--
--  Chassis Clear Alarm
--

tmnxChassisNotificationClear   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifyOID
    }
    STATUS current
    DESCRIPTION
        "A trap indicating the clear of a chassis notification
         identified by tmnxChassisNotifyOID."
    ::= { tmnxChassisNotification 25 }

--
-- Synchronous timing alarms
--

tmnxEqSyncIfTimingHoldover   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS current
    DESCRIPTION
        "Generated when the synchronous equipment timing subsystem 
         transitions into a holdover state.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 26 }

tmnxEqSyncIfTimingHoldoverClear   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS current
    DESCRIPTION
        "Generated when the synchronous equipment timing subsystem 
         transitions out of the holdover state.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 27 }

tmnxEqSyncIfTimingRef1Alarm   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxSyncIfTimingNotifyAlarm
    }
    STATUS current
    DESCRIPTION
        "Generated when an alarm condition on the first timing 
         reference is detected.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 28 }

tmnxEqSyncIfTimingRef1AlarmClear   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxSyncIfTimingNotifyAlarm
    }
    STATUS current
    DESCRIPTION
        "Generated when an alarm condition on the first timing 
         reference is cleared.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 29 }

tmnxEqSyncIfTimingRef2Alarm   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxSyncIfTimingNotifyAlarm
    }
    STATUS current
    DESCRIPTION
        "Generated when an alarm condition on the second timing 
         reference is detected.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 30 }

tmnxEqSyncIfTimingRef2AlarmClear   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxSyncIfTimingNotifyAlarm
    }
    STATUS current
    DESCRIPTION
        "Generated when an alarm condition on the second timing 
         reference is cleared.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 31 }

tmnxEqFlashDataLoss   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxHwOperState
    }
    STATUS current
    DESCRIPTION
        "tmnxEqFlashDataLoss is generated when there was an error 
         while data was getting written on to the compact flash. This 
         notification indicates a probable data loss."
    ::= { tmnxChassisNotification 32 }

tmnxEqFlashDiskFull   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxHwOperState
    }
    STATUS current
    DESCRIPTION
        "tmnxEqFlashDiskFull is generated when there is no space
         left on the compact flash. No more data can be written to it."
    ::= { tmnxChassisNotification 33 }

tmnxPeSoftwareLoadFailed   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifySoftwareLocation
    }
    STATUS current
    DESCRIPTION
        "Generated when the CPM fails to load the software from a specified 
         location.
         tmnxChassisNotifyHwIndex identifies the card for which the software
         load failed and tmnxChassisNotifySoftwareLocation contains the
         location from where the software load was attempted."
    ::= { tmnxChassisNotification 34 }

 tmnxPeBootloaderVersionMismatch  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifyMismatchedVer,
        tmnxHwSoftwareCodeVersion
    }
    STATUS  current
    DESCRIPTION
        "Generated when there is a mismatch between the CPM and boot loader 
         versions. tmnxChassisNotifyHwIndex identifies the CPM card.
         tmnxChassisNotifyMismatchedVer contains the mismatched version of
         bootloader and tmnxHwSoftwareCodeVersion contains the 
         expected version of the bootloader."
    ::= { tmnxChassisNotification 35 }

 tmnxPeBootromVersionMismatch  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifyMismatchedVer,
        tmnxHwSoftwareCodeVersion
    }
    STATUS  current
    DESCRIPTION
        "Generated when there is a mismatch between the boot rom versions. 
         tmnxChassisNotifyHwIndex identifies the IOM card.
         tmnxChassisNotifyMismatchedVer contains the mismatched version of
         bootrom and tmnxHwSoftwareCodeVersion contains the expected version 
         of the bootrom."
    ::= { tmnxChassisNotification 36 }

 tmnxPeFPGAVersionMismatch  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxChassisNotifyMismatchedVer,
        tmnxHwSoftwareCodeVersion
    }
    STATUS  current
    DESCRIPTION
        "Generated when there is a mismatch between the FPGA versions. 
         tmnxChassisNotifyHwIndex identifies the IOM card.
         tmnxChassisNotifyMismatchedVer contains the mismatched version of
         FPGA and tmnxHwSoftwareCodeVersion contains the expected version 
         of the FPGA."
    ::= { tmnxChassisNotification 37 }

tmnxEqSyncIfTimingBITSAlarm   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxSyncIfTimingNotifyAlarm
    }
    STATUS current
    DESCRIPTION
        "Generated when an alarm condition on the BITS timing 
         reference is detected.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 38 }

tmnxEqSyncIfTimingBITSAlarmClear   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass,
        tmnxSyncIfTimingNotifyAlarm
    }
    STATUS current
    DESCRIPTION
        "Generated when an alarm condition on the BITS timing 
         reference is cleared.
         This notification will have the same indices as those of 
         the tmnxCpmCardTable."
    ::= { tmnxChassisNotification 39 }

tmnxEqCardFirmwareUpgraded NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "Generated when a card is hot-inserted into the chassis and its 
         firmware is automatically upgraded.  The card type may be IOM or 
         CPM module."
    ::= { tmnxChassisNotification 40 }

tmnxChassisUpgradeInProgress  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "The tmnxChassisUpgradeInProgress notification is generated only
         after a CPM switchover occurs and the new active CPM is running new
         software, while the IOMs are still running old software. This is the
         start of the upgrade process. The tmnxChassisUpgradeInProgress
         notification will continue to be generated every 30 minutes while at
         least one IOM is still running older software." 
    ::= { tmnxChassisNotification 41 }

tmnxChassisUpgradeComplete  NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "The tmnxChassisUpgradeComplete notification is generated to 
         indicate that all the IOMs are running matching software version in
         reference to the active CPM software version changed as part of the
         upgrade process." 
    ::= { tmnxChassisNotification 42 }

tmnxChassisHiBwMcastAlarm   NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "The tmnxChassisHiBwMcastAlarm notification is generated when a plane
         is  shared by more than one high bandwidth multicast tap." 
    ::= { tmnxChassisNotification 43 }

 tmnxEqMdaCfgNotCompatible    NOTIFICATION-TYPE
    OBJECTS {
        tmnxChassisNotifyHwIndex,
        tmnxHwID,
        tmnxHwClass
    }
    STATUS  current
    DESCRIPTION
        "Generated when a supported MDA is inserted into a slot of an
         IOM, the MDA is compatible with the currently provisioned
         MDA, but the current configuration on the MDA's ports is not 
         compatible with the inserted MDA."
    ::= { tmnxChassisNotification 44 }

--
--
--
         
--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      TMNX-HW-MIB Object Groups
--



--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
--
--      The compliance specifications.
--

tmnxChassisCompliances  OBJECT IDENTIFIER ::= { tmnxChassisConformance 1 }
tmnxChassisGroups       OBJECT IDENTIFIER ::= { tmnxChassisConformance 2 }

-- compliance statements

-- tmnxChassisCompliance  MODULE-COMPLIANCE
--    ::= { tmnxChassisCompliances 1 }

-- tmnxChassisR2r1Compliance  MODULE-COMPLIANCE
--    ::= { tmnxChassisCompliances 2 }

-- tmnxChassisV3v0Compliance  MODULE-COMPLIANCE
--    ::= { tmnxChassisCompliances 3 }

tmnxChassisV4v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for management of chassis features
             in the ALCATEL-IND1-TIMETRA-CHASSIS-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxChassisV3v0Group,
            tmnxCardV3v0Group,
            tmnxMDAV4v0Group,
            tmnxChassisNotificationV4v0Group           
        }
    ::= { tmnxChassisCompliances 4 }

tmnxChassisV5v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for management of chassis features
             in the ALCATEL-IND1-TIMETRA-CHASSIS-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxChassisV5v0Group,
            tmnxCardV3v0Group,
            tmnxMDAV4v0Group,
            tmnxChassisNotificationV4v0Group           
        }
    ::= { tmnxChassisCompliances 6 }

tmnxChassis7750V6v0Compliance  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for management of chassis features
             on the 7750 in the ALCATEL-IND1-TIMETRA-CHASSIS-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxChassisV5v0Group,
            tmnxCardV3v0Group,
            tmnxMDAV4v0Group,
            tmnxChassisNotificationV6v0Group,
            tmnx77x0CESMDAV6v0Group,
            tmnxCardV6v0NamedPoolPlcyGroup,
            -- tmnx7710HwV3v0Group
            -- tmnx7710SETSRefSrcHwV6v0Group
            tmnxMDAMcPathMgmtV6v0Group
        }
    ::= { tmnxChassisCompliances 7 }

tmnxChassis7450V6v0Compliance  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for management of chassis features
             on the 7450 in the ALCATEL-IND1-TIMETRA-CHASSIS-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxChassisV5v0Group,
            tmnxCardV3v0Group,
            tmnxMDAV4v0Group,
            tmnxCardV6v0NamedPoolPlcyGroup, 
            tmnxChassisNotificationV6v0Group,
            -- tmnx77x0CESMDAV6v0Group
            -- tmnx7710HwV3v0Group
            -- tmnx7710SETSRefSrcHwV6v0Group
            tmnxMDAMcPathMgmtV6v0Group
        }
    ::= { tmnxChassisCompliances 8 }

tmnxChassisComp7710     OBJECT IDENTIFIER ::= { tmnxChassisCompliances 5 }

tmnxChassisComp7710V3v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for management of chassis features
             for the 7710 in the ALCATEL-IND1-TIMETRA-CHASSIS-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxChassisV3v0Group,
            tmnxCardV3v0Group,
            tmnxMDAV3v0Group,
            tmnxChassisNotificationV3v0Group,
            tmnx7710HwV3v0Group
        }
    ::= { tmnxChassisComp7710 1 }
    
tmnxChassisComp7710V5v0  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
            "The compliance statement for management of chassis features
             for the 7710 in the ALCATEL-IND1-TIMETRA-CHASSIS-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxChassisV5v0Group,
            tmnxCardV3v0Group,
            tmnxMDAV4v0Group,
            tmnxChassisNotificationV4v0Group,
            tmnx7710HwV3v0Group
        }
    ::= { tmnxChassisComp7710 2 }

tmnxChassisComp7710V6v0  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for management of chassis features
             for the 7710 in the ALCATEL-IND1-TIMETRA-CHASSIS-MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { 
            tmnxChassisV5v0Group,
            tmnxCardV3v0Group,
            tmnxMDAV4v0Group,
            tmnxChassisNotificationV6v0Group,
            tmnx7710HwV3v0Group,
            tmnx77x0CESMDAV6v0Group,
            tmnx7710SETSRefSrcHwV6v0Group,
            tmnxCardV6v0NamedPoolPlcyGroup,
            tmnxMDAMcPathMgmtV6v0Group
        }
    ::= { tmnxChassisComp7710 3 }

-- units of conformance

-- tmnxChassisGroup   OBJECT-GROUP
--    ::= { tmnxChassisGroups 1 }

-- tmnxCardGroup   OBJECT-GROUP
--    ::= { tmnxChassisGroups 2 }

-- tmnxMDAGroup        OBJECT-GROUP
--    ::= { tmnxChassisGroups 3 }

tmnxChassisNotifyObjsGroup   OBJECT-GROUP
    OBJECTS {   tmnxEqNotificationRow,
                tmnxEqTypeNotificationRow,
                tmnxChassisNotifyChassisId,
                tmnxChassisNotifyHwIndex,
                tmnxRedSecondaryCPMStatus,
                tmnxChassisNotifyOID,
                tmnxSyncIfTimingNotifyAlarm,
                tmnxChassisNotifyMismatchedVer,
                tmnxChassisNotifySoftwareLocation,
                tmnxChassisNotifyCardFailureReason
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting chassis hardware notifications 
         on Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 4 }

-- tmnxChassisNotificationGroup NOTIFICATION-GROUP
--    ::= { tmnxChassisGroups 5 }

-- tmnxChassisNotificationR2r1Group NOTIFICATION-GROUP
--    ::= { tmnxChassisGroups 6 }

tmnxChassisNotifyObsoleteGroup NOTIFICATION-GROUP
    NOTIFICATIONS   { tmnxHwConfigChange,
                      tmnxEqCpuFailure,
                      tmnxEqMemoryFailure,
                      tmnxEqBackdoorBusFailure,
                      tmnxPeSoftwareError,
                      tmnxPeSoftwareAbnormalHalt,
                      tmnxPeOutOfMemory,
                      tmnxPeConfigurationError,
                      tmnxPeStorageProblem,
                      tmnxPeCpuCyclesExceeded,
                      tmnxRedSecondaryCPMStatusChange,
                      tmnxRedRestoreSuccess,
                      tmnxRedRestoreFail
                    }
    STATUS        current
    DESCRIPTION
        "The group of notifications supporting the management of chassis
         hardware made obsolete for revision 2.1 on Alcatel 7x50 SR series 
         systems."
    ::= { tmnxChassisGroups 7 }

-- tmnxChassisR2r1Group   OBJECT-GROUP
--    ::= { tmnxChassisGroups 8 }

tmnxChassisV3v0Group   OBJECT-GROUP
    OBJECTS {   tmnxChassisTotalNumber,
                tmnxChassisLastChange,
                tmnxChassisRowStatus,
                tmnxChassisName,
                tmnxChassisType,
                tmnxChassisLocation,
                tmnxChassisCoordinates,
                tmnxChassisNumSlots,
                tmnxChassisNumPorts,
                tmnxChassisNumPwrSupplies,
                tmnxChassisNumFanTrays,
                tmnxChassisNumFans,
                tmnxChassisCriticalLEDState,
                tmnxChassisMajorLEDState,
                tmnxChassisMinorLEDState,
                tmnxChassisBaseMacAddress,
                tmnxChassisCLLICode,
                tmnxChassisReboot,
                tmnxChassisUpgrade,
                tmnxChassisAdminMode,
                tmnxChassisOperMode,
                tmnxChassisModeForce,
                tmnxChassisUpdateWaitTime,
                tmnxChassisUpdateTimeLeft,
                tmnxChassisFanOperStatus,
                tmnxChassisFanSpeed,
                tmnxChassisPowerSupplyACStatus,
                tmnxChassisPowerSupplyDCStatus,
                tmnxChassisPowerSupplyTempStatus,
                tmnxChassisPowerSupplyTempThreshold,
                tmnxChassisPowerSupply1Status,
                tmnxChassisPowerSupply2Status,
                tmnxChassisPowerSupplyAssignedType,
                tmnxChassisTypeName,
                tmnxChassisTypeDescription,
                tmnxChassisTypeStatus,
                tmnxHwLastChange,
                tmnxHwID,
                tmnxHwMfgString,
                tmnxHwMfgBoardNumber,
                tmnxHwSerialNumber,
                tmnxHwManufactureDate,
                tmnxHwClass,
                tmnxHwName,
                tmnxHwAlias,
                tmnxHwAssetID,
                tmnxHwCLEI,
                tmnxHwIsFRU,
                tmnxHwContainedIn,
                tmnxHwParentRelPos,
                tmnxHwAdminState,
                tmnxHwOperState,
                tmnxHwTempSensor,
                tmnxHwTemperature,
                tmnxHwTempThreshold,
                tmnxHwBootCodeVersion,
                tmnxHwSoftwareCodeVersion,
                tmnxHwSwLastBoot,
                tmnxHwAlarmState,
                tmnxHwLastAlarmEvent,
                tmnxHwClearAlarms,
                tmnxHwSwImageSource,
                tmnxHwMfgDeviations,
                tmnxHwBaseMacAddress,
                tmnxHwFailureReason,
                tmnxHwContainedIndex
            }
    STATUS        obsolete
    DESCRIPTION
        "The group of objects supporting management of chassis hardware 
         capabilities on release 3.0 of  Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 9 }

tmnxMDAV3v0Group        OBJECT-GROUP
    OBJECTS {   tmnxMDASupportedTypes,
                tmnxMDAAssignedType,
                tmnxMDAEquippedType,
                tmnxMDAHwIndex,
                tmnxMDAMaxPorts,
                tmnxMDAEquippedPorts,
                tmnxMDATxTimingSelected,
                tmnxMDASyncIfTimingStatus,
                tmnxMDANetworkIngQueues,
                tmnxMDACapabilities,
                tmnxMDAMinChannelization,
                tmnxMDAMaxChannelization,
                tmnxMDAMaxChannels,
                tmnxMDAChannelsInUse,
                tmnxMDACcagId,
                tmnxMdaTypeName,
                tmnxMdaTypeDescription,
                tmnxMdaTypeStatus,
                tmnxCcagRowStatus,
                tmnxCcagDescription,
                tmnxCcagAdminStatus,
                tmnxCcagOperStatus,
                tmnxCcagCcaRate,
                tmnxCcagLastChanged,
                tmnxCcagAccessAdaptQos,
                tmnxCcagPathLastChanged,
                tmnxCcagPathRate,
                tmnxCcagPathRateOption,
                tmnxCcagPathWeight,
                tmnxCcagPathCcLastChanged,
                tmnxCcagPathCcEgrPoolResvCbs,
                tmnxCcagPathCcEgrPoolSlpPlcy,
                tmnxCcagPathCcIngPoolResvCbs,
                tmnxCcagPathCcIngPoolSlpPlcy,
                tmnxCcagPathCcAcctPolicyId,
                tmnxCcagPathCcCollectStats,
                tmnxCcagPathCcQueuePlcy,
                tmnxCcagPathCcMac,
                tmnxCcagPathCcMtu,
                tmnxCcagPathCcHwMac,
                tmnxCcagPathCcUserAssignedMac
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of MDAs on
         Alcatel 7x50 SR series systems."

    ::= { tmnxChassisGroups 10 }

tmnxChassisObsoleteGroup OBJECT-GROUP
    OBJECTS {   tmnxHwSwState,
                tmnxCardAllowedTypes,
                tmnxCpmCardAllowedTypes,
                tmnxMDAAllowedTypes
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting the allowed types of CPM cards, IOM 
         cards and MDA made obsolete for revision 3.0 on Alcatel 7x50 SR series 
         systems."
    ::= { tmnxChassisGroups 11 }

tmnxCardV3v0Group   OBJECT-GROUP
    OBJECTS {   tmnxCardLastChange,
                tmnxCardTypeName,
                tmnxCardTypeDescription,
                tmnxCardTypeStatus,
                tmnxCardSupportedTypes, 
                tmnxCardAssignedType,
                tmnxCardEquippedType,
                tmnxCardHwIndex,
                tmnxCardClockSource,
                tmnxCardNumMdaSlots,
                tmnxCardNumMdas,
                tmnxCardReboot,
                tmnxCardMemorySize,
                tmnxCpmCardLastChange,
                tmnxCpmCardSupportedTypes,
                tmnxCpmCardAssignedType,
                tmnxCpmCardEquippedType,
                tmnxCpmCardHwIndex,
                tmnxCpmCardBootOptionVersion,
                tmnxCpmCardBootOptionLastModified,
                tmnxCpmCardConfigBootedVersion,
                tmnxCpmCardIndexBootedVersion,
                tmnxCpmCardConfigLastModified,
                tmnxCpmCardConfigLastSaved,
                tmnxCpmCardRedundant,
                tmnxCpmCardClockSource,
                tmnxCpmCardNumCpus,
                tmnxCpmCardCpuType,
                tmnxCpmCardMemorySize,
                tmnxCpmCardSwitchToRedundantCard,
                tmnxCpmCardReboot,
                tmnxCpmCardRereadBootOptions,
                tmnxCpmCardConfigFileLastBooted,
                tmnxCpmCardConfigFileLastSaved,
                tmnxCpmCardConfigFileLastBootedHeader,
                tmnxCpmCardIndexFileLastBootedHeader,
                tmnxCpmCardBootOptionSource,
                tmnxCpmCardConfigSource,
                tmnxCpmCardBootOptionLastSaved,
                tmnxFabricLastChange,
                tmnxFabricAssignedType,
                tmnxFabricEquippedType,
                tmnxFabricHwIndex,
                tmnxCpmFlashOperStatus,
                tmnxCpmFlashSerialNumber,
                tmnxCpmFlashFirmwareRevision,
                tmnxCpmFlashModelNumber,
                tmnxCpmFlashCapacity,
                tmnxCpmFlashUsed,
                tmnxCpmFlashHwIndex,
                tmnxSyncIfTimingRevert,
                tmnxSyncIfTimingRefOrder1,
                tmnxSyncIfTimingRefOrder2,
                tmnxSyncIfTimingRef1SrcPort,
                tmnxSyncIfTimingRef1AdminStatus,
                tmnxSyncIfTimingRef1InUse,
                tmnxSyncIfTimingRef1Qualified,
                tmnxSyncIfTimingRef1Alarm,
                tmnxSyncIfTimingRef2SrcPort,
                tmnxSyncIfTimingRef2AdminStatus,
                tmnxSyncIfTimingRef2InUse,
                tmnxSyncIfTimingRef2Qualified,
                tmnxSyncIfTimingRef2Alarm,
                tmnxSyncIfTimingFreqOffset,
                tmnxSyncIfTimingStatus,
                tmnxSyncIfTimingRefOrder3,
                tmnxSyncIfTimingBITSIfType,
                tmnxSyncIfTimingBITSAdminStatus,
                tmnxSyncIfTimingBITSInUse,
                tmnxSyncIfTimingBITSQualified,
                tmnxSyncIfTimingBITSAlarm,
                tSyncIfTimingAdmRevert,
                tSyncIfTimingAdmRefOrder1,
                tSyncIfTimingAdmRefOrder2,
                tSyncIfTimingAdmRef1SrcPort,
                tSyncIfTimingAdmRef1AdminStatus,
                tSyncIfTimingAdmRef2SrcPort,
                tSyncIfTimingAdmRef2AdminStatus,
                tSyncIfTimingAdmChanged,
                tSyncIfTimingAdmRefOrder3,
                tSyncIfTimingAdmBITSIfType,
                tSyncIfTimingAdmBITSAdminStatus,
                tmnxChassisAdminOwner,
                tmnxChassisAdminControlApply,
                tmnxChassisAdminLastSetTimer,
                tmnxChassisAdminLastSetTimeout
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of hardware cards
         on Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 12 }

tmnxMDAV4v0Group        OBJECT-GROUP
    OBJECTS {   tmnxMDASupportedTypes,           
                tmnxMDAAssignedType,
                tmnxMDAEquippedType,
                tmnxMDAHwIndex,
                tmnxMDAMaxPorts,
                tmnxMDAEquippedPorts,
                tmnxMDATxTimingSelected,
                tmnxMDASyncIfTimingStatus,
                tmnxMDANetworkIngQueues,
                tmnxMDACapabilities,
                tmnxMDAMinChannelization,
                tmnxMDAMaxChannelization,
                tmnxMDAMaxChannels,
                tmnxMDAChannelsInUse,
                tmnxMDACcagId,
                tmnxMdaTypeName,
                tmnxMdaTypeDescription,
                tmnxMdaTypeStatus,
                tmnxMDAReboot,
                tmnxCcagRowStatus,
                tmnxCcagDescription,
                tmnxCcagAdminStatus,
                tmnxCcagOperStatus,
                tmnxCcagCcaRate,
                tmnxCcagLastChanged,
                tmnxCcagAccessAdaptQos,
                tmnxCcagPathLastChanged,
                tmnxCcagPathRate,
                tmnxCcagPathRateOption,
                tmnxCcagPathWeight,
                tmnxCcagPathCcLastChanged,
                tmnxCcagPathCcEgrPoolResvCbs,
                tmnxCcagPathCcEgrPoolSlpPlcy,
                tmnxCcagPathCcIngPoolResvCbs,
                tmnxCcagPathCcIngPoolSlpPlcy,
                tmnxCcagPathCcAcctPolicyId,
                tmnxCcagPathCcCollectStats,
                tmnxCcagPathCcQueuePlcy,
                tmnxCcagPathCcMac,
                tmnxCcagPathCcMtu,
                tmnxCcagPathCcHwMac,  
                tmnxCcagPathCcUserAssignedMac,
                tmnxMDAHiBwMcastSource,
                tmnxMDAHiBwMcastAlarm,
                tmnxMDAHiBwMcastTapCount,
                tmnxMDAHiBwMcastGroup
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of MDAs for release 4.0 on
         Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 13 }

tmnxChassisNotificationV4v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   { tmnxEnvTempTooHigh,
                      tmnxEqPowerSupplyFailure,
                      tmnxEqPowerSupplyInserted,
                      tmnxEqPowerSupplyRemoved,
                      tmnxEqFanFailure,
                      tmnxEqCardFailure,
                      tmnxEqCardInserted,
                      tmnxEqCardRemoved,
                      tmnxEqWrongCard,
                      tmnxPeSoftwareVersionMismatch,
                      tmnxRedPrimaryCPMFail,
                      tmnxChassisNotificationClear,
                      tmnxEqSyncIfTimingHoldover,
                      tmnxEqSyncIfTimingHoldoverClear,
                      tmnxEqSyncIfTimingRef1Alarm,
                      tmnxEqSyncIfTimingRef1AlarmClear,
                      tmnxEqSyncIfTimingRef2Alarm,
                      tmnxEqSyncIfTimingRef2AlarmClear,
                      tmnxEqFlashDataLoss,
                      tmnxEqFlashDiskFull,
                      tmnxPeSoftwareLoadFailed,
                      tmnxPeBootloaderVersionMismatch,
                      tmnxPeBootromVersionMismatch,
                      tmnxPeFPGAVersionMismatch,
                      tmnxEqSyncIfTimingBITSAlarm,
                      tmnxEqSyncIfTimingBITSAlarmClear,
                      tmnxEqCardFirmwareUpgraded,
                      tmnxChassisUpgradeInProgress,  
                      tmnxChassisUpgradeComplete,
                      tmnxChassisHiBwMcastAlarm,
                      tmnxEqMdaCfgNotCompatible
                    }
    STATUS        obsolete
    DESCRIPTION
        "The group of notifications supporting the management of chassis
         hardware for release 4.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 14 }

tmnx7710HwV3v0Group  OBJECT-GROUP
    OBJECTS {   tmnxChassisOverTempState,
                tmnxCpmCardMasterSlaveRefState,
                tmnxCcmOperStatus,
                tmnxCcmHwIndex,
                tmnxCcmEquippedType,
                tmnxCcmTypeName,
                tmnxCcmTypeDescription,
                tmnxCcmTypeStatus,
                tmnxMcmSupportedTypes,
                tmnxMcmAssignedType,
                tmnxMcmEquippedType,
                tmnxMcmHwIndex,
                tmnxMcmTypeName,
                tmnxMcmTypeDescription,
                tmnxMcmTypeStatus,
                tmnxChassisPowerSupplyInputStatus,
                tmnxChassisPowerSupplyOutputStatus,
                tmnxMDAReboot
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of hardware specific to
         the Alcatel 7710 SR series systems."
    ::= { tmnxChassisGroups 15 }

tmnxChassisV5v0Group   OBJECT-GROUP
    OBJECTS {   tmnxChassisTotalNumber,
                tmnxChassisLastChange,
                tmnxChassisRowStatus,
                tmnxChassisName,
                tmnxChassisType,
                tmnxChassisLocation,
                tmnxChassisCoordinates,
                tmnxChassisNumSlots,
                tmnxChassisNumPorts,
                tmnxChassisNumPwrSupplies,
                tmnxChassisNumFanTrays,
                tmnxChassisNumFans,
                tmnxChassisCriticalLEDState,
                tmnxChassisMajorLEDState,
                tmnxChassisMinorLEDState,
                tmnxChassisBaseMacAddress,
                tmnxChassisCLLICode,
                tmnxChassisReboot,
                tmnxChassisUpgrade,
                tmnxChassisAdminMode,
                tmnxChassisOperMode,
                tmnxChassisModeForce,
                tmnxChassisUpdateTimeLeft,
                tmnxChassisFanOperStatus,
                tmnxChassisFanSpeed,
                tmnxChassisPowerSupplyACStatus,
                tmnxChassisPowerSupplyDCStatus,
                tmnxChassisPowerSupplyTempStatus,
                tmnxChassisPowerSupplyTempThreshold,
                tmnxChassisPowerSupply1Status,
                tmnxChassisPowerSupply2Status,
                tmnxChassisPowerSupplyAssignedType,
                tmnxChassisTypeName,
                tmnxChassisTypeDescription,
                tmnxChassisTypeStatus,
                tmnxHwLastChange,
                tmnxHwID,
                tmnxHwMfgString,
                tmnxHwMfgBoardNumber,
                tmnxHwSerialNumber,
                tmnxHwManufactureDate,
                tmnxHwClass,
                tmnxHwName,
                tmnxHwAlias,
                tmnxHwAssetID,
                tmnxHwCLEI,
                tmnxHwIsFRU,
                tmnxHwContainedIn,
                tmnxHwParentRelPos,
                tmnxHwAdminState,
                tmnxHwOperState,
                tmnxHwTempSensor,
                tmnxHwTemperature,
                tmnxHwTempThreshold,
                tmnxHwBootCodeVersion,
                tmnxHwSoftwareCodeVersion,
                tmnxHwSwLastBoot,
                tmnxHwAlarmState,
                tmnxHwLastAlarmEvent,
                tmnxHwClearAlarms,
                tmnxHwSwImageSource,
                tmnxHwMfgDeviations,
                tmnxHwBaseMacAddress,
                tmnxHwFailureReason,
                tmnxHwContainedIndex
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of chassis hardware 
         capabilities on release 5.0 of  Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 16 }

tmnxChassisV5v0ObsoleteGroup   OBJECT-GROUP
    OBJECTS { tmnxChassisUpdateWaitTime
            }
    STATUS        current
    DESCRIPTION
        "The group of onbsolete objects for managing the chassis hardware 
         capabilities on release 5.0 of  Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 17 }

tmnx77x0CESMDAV6v0Group        OBJECT-GROUP
    OBJECTS {   tmnxMDAClockMode,
                tmnxMDADiffTimestampFreq,
                tmnxMDAIngNamedPoolPolicy,
                tmnxMDAEgrNamedPoolPolicy
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of CES MDAs for release 6.0
         on Alcatel 77x0 SR series systems."
    ::= { tmnxChassisGroups 18 }

tmnxChassisNotificationV3v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   { tmnxEnvTempTooHigh,
                      tmnxEqPowerSupplyFailure,
                      tmnxEqPowerSupplyInserted,
                      tmnxEqPowerSupplyRemoved,
                      tmnxEqFanFailure,
                      tmnxEqCardFailure,
                      tmnxEqCardInserted,
                      tmnxEqCardRemoved,
                      tmnxEqWrongCard,
                      tmnxPeSoftwareVersionMismatch,
                      tmnxRedPrimaryCPMFail,
                      tmnxChassisNotificationClear,
                      tmnxEqSyncIfTimingHoldover,
                      tmnxEqSyncIfTimingHoldoverClear,
                      tmnxEqSyncIfTimingRef1Alarm,
                      tmnxEqSyncIfTimingRef1AlarmClear,
                      tmnxEqSyncIfTimingRef2Alarm,
                      tmnxEqSyncIfTimingRef2AlarmClear,
                      tmnxEqFlashDataLoss,
                      tmnxEqFlashDiskFull,
                      tmnxPeSoftwareLoadFailed,
                      tmnxPeBootloaderVersionMismatch,
                      tmnxPeBootromVersionMismatch,
                      tmnxPeFPGAVersionMismatch,
                      tmnxEqSyncIfTimingBITSAlarm,
                      tmnxEqSyncIfTimingBITSAlarmClear,
                      tmnxEqCardFirmwareUpgraded,
                      tmnxEqMdaCfgNotCompatible
                    }
    STATUS        obsolete
    DESCRIPTION
        "The group of notifications supporting the management of chassis
         hardware for release 3.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 20 }

tmnxChassisNotificationV6v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   { tmnxEnvTempTooHigh,
                      tmnxEqPowerSupplyFailure,
                      tmnxEqPowerSupplyInserted,
                      tmnxEqPowerSupplyRemoved,
                      tmnxEqFanFailure,
                      tmnxEqCardFailure,
                      tmnxEqCardInserted,
                      tmnxEqCardRemoved,
                      tmnxEqWrongCard,
                      tmnxPeSoftwareVersionMismatch,
                      tmnxRedPrimaryCPMFail,
                      tmnxChassisNotificationClear,
                      tmnxEqSyncIfTimingHoldover,
                      tmnxEqSyncIfTimingHoldoverClear,
                      tmnxEqSyncIfTimingRef1Alarm,
                      tmnxEqSyncIfTimingRef1AlarmClear,
                      tmnxEqSyncIfTimingRef2Alarm,
                      tmnxEqSyncIfTimingRef2AlarmClear,
                      tmnxEqFlashDataLoss,
                      tmnxEqFlashDiskFull,
                      tmnxPeSoftwareLoadFailed,
                      tmnxPeBootloaderVersionMismatch,
                      tmnxPeBootromVersionMismatch,
                      tmnxPeFPGAVersionMismatch,
                      tmnxEqSyncIfTimingBITSAlarm,
                      tmnxEqSyncIfTimingBITSAlarmClear,
                      tmnxEqCardFirmwareUpgraded,
                      tmnxChassisUpgradeInProgress,  
                      tmnxChassisUpgradeComplete,
                      tmnxChassisHiBwMcastAlarm,
                      tmnxEqMdaCfgNotCompatible                        
                    }
    STATUS        current
    DESCRIPTION
        "The group of notifications supporting the management of chassis
         hardware for release 6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 21 }

tmnx7710SETSRefSrcHwV6v0Group  OBJECT-GROUP
    OBJECTS {
                tmnxSyncIfTimingRef1SrcHw,
                tmnxSyncIfTimingRef1BITSIfType,
                tmnxSyncIfTimingRef2SrcHw,
                tmnxSyncIfTimingRef2BITSIfType,
                tSyncIfTimingAdmRef1SrcHw,
                tSyncIfTimingAdmRef1BITSIfType,
                tSyncIfTimingAdmRef2SrcHw,
                tSyncIfTimingAdmRef2BITSIfType
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of 'Synchronous Equipment
         Timing' (SETS) when the references are of type 'Building Integrated
         Timing Supply' (BITS) for release 6.0 on Alcatel 7710 SR series
         systems."
    ::= { tmnxChassisGroups 22 }

tmnxMDAMcPathMgmtV6v0Group        OBJECT-GROUP
     OBJECTS {
                tmnxMDAMcPathMgmtBwPlcyName,   
                tmnxMDAMcPathMgmtPriPathLimit,
                tmnxMDAMcPathMgmtSecPathLimit,
                tmnxMDAMcPathMgmtAncPathLimit,
                tmnxMDAMcPathMgmtAdminState,
                tmnxMDAMcPathMgmtPriInUseBw,
                tmnxMDAMcPathMgmtSecInUseBw,
                tmnxMDAMcPathMgmtAncInUseBw,
                tmnxMDAMcPathMgmtBlkHoleInUseBw
             }
     STATUS      current
     DESCRIPTION
         "The group of objects supporting management of Multicast Path
          Management feature for release 6.0 on Alcatel 7x50 SR series systems."
     ::= { tmnxChassisGroups 24 }

tmnxCardV6v0NamedPoolPlcyGroup   OBJECT-GROUP
    OBJECTS {   
                tmnxCardNamedPoolAdminMode,
                tmnxCardNamedPoolOperMode
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting named buffer pools for release 
         6.0 on Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 25 }

tmnxChassisNotifyObjsV6v0Group   OBJECT-GROUP
    OBJECTS {   tmnxChassisNotifyCardName
            }
    STATUS        current
    DESCRIPTION
        "The group of objects added in 6.0 release to support chassis 
         hardware notifications on Alcatel 7x50 SR series systems."
    ::= { tmnxChassisGroups 26 }

END
