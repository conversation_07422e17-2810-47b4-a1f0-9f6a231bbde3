ALCATEL-IND1-TIMETRA-SDP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE,
    NOTIFICATION-TYPE, <PERSON><PERSON><PERSON>32,
    <PERSON>te<PERSON>32, <PERSON>signed<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>64, Counter32                        FROM SNMPv2-<PERSON>I

    OBJECT-<PERSON><PERSON><PERSON>, NOTIFICATION-<PERSON><PERSON><PERSON>, MODULE-COMP<PERSON><PERSON>NCE
                                                FROM SNMPv2-CO<PERSON>

    RowStatus, Mac<PERSON>dd<PERSON>, TimeStamp, DisplayString,
    TruthValue FROM SNMPv2-TC

    InterfaceIndexOrZero                        FROM IF-MIB

    InetAddressType, InetAddress                FROM INET-ADDRESS-MIB

    ServiceAdminStatus, 
    TmnxServId, TmnxCustId, TNamedItem, SdpBindId, TNamedItemOrEmpty,
    TmnxVRtrMplsLspID, TmnxOperState, TmnxIgmpVersion,
    TmnxEnabledDisabled, TItemDescription, TPolicyStatementNameOrEmpty,
    TmnxVPNRouteDistinguisher                   FROM ALCATEL-IND1-TIMETRA-TC-MIB

    tmnxServObjs, tmnxServConformance, tmnxServNotifications, tmnxSvcObjs,
    custId, svcId, svcVpnId, tstpTraps,
    tmnxOtherBridgeId, tmnxCustomerBridgeId, tmnxCustomerRootBridgeId,
    tmnxOldSdpBindTlsStpPortState, svcTlsStpDesignatedRoot,
    tlsDhcpPacketProblem, svcDhcpLseStateNewCiAddr,
    svcDhcpLseStateNewChAddr, svcDhcpLseStateOldCiAddr,
    svcDhcpLseStateOldChAddr, svcDhcpClientLease, svcDhcpLseStatePopulateError,
    svcTlsMacMoveMaxRate, svcDhcpProxyError, svcDhcpCoAError,
    svcDhcpPacketProblem, svcDhcpSubAuthError,
    ServObjName, ServObjDesc,
    VpnId, SdpId, PWTemplateId, SdpBindTlsBpduTranslation,
    TlsLimitMacMoveLevel, TlsLimitMacMove, SdpBindVcType,
    StpExceptionCondition, LspIdList, BridgeId, TStpPortState,
    StpPortRole, StpProtocol, MvplsPruneState, TdmOptionsSigPkts,
    TdmOptionsCasTrunkFraming, SdpBFHundredthsOfPercent, SdpBindBandwidth,
    L2ptProtocols, L2RouteOrigin, ConfigStatus  FROM ALCATEL-IND1-TIMETRA-SERV-MIB

    timetraSRMIBModules                         FROM ALCATEL-IND1-TIMETRA-GLOBAL-MIB

    TFilterID                                   FROM ALCATEL-IND1-TIMETRA-FILTER-MIB
    tmnxChassisIndex, tmnxCardSlotNum,
    tmnxMDASlotNum                              FROM ALCATEL-IND1-TIMETRA-CHASSIS-MIB;

timetraServicesSdpMIBModule   MODULE-IDENTITY
    LAST-UPDATED    "0710010000Z"
    ORGANIZATION    "Alcatel"
    CONTACT-INFO
        "Alcatel 7x50 Support
         Web: http://www.alcatel.com/comps/pages/carrier_support.jhtml"
    DESCRIPTION
        "This  document  is the SNMP MIB module to manage and provision
        the various services of the Alcatel 7x50 SR system.

        Copyright  2003-2008 Alcatel-Lucent. All rights reserved. Reproduction
        of  this  document  is  authorized  on  the  condition that the
        foregoing copyright notice is included.

        This   SNMP   MIB  module  (Specification)  embodies  Alcatel's
        proprietary  intellectual  property.  Alcatel retains all title
        and ownership in the Specification, including any revisions.

        Alcatel  grants  all interested parties a non-exclusive license
        to  use and distribute an unmodified copy of this Specification
        in  connection with management of Alcatel products, and without
        fee,  provided  this copyright notice and license appear on all
        copies.

        This  Specification  is  supplied `as is', and Alcatel makes no
        warranty,  either express or implied, as to the use, operation,
        condition, or performance of the Specification."

--
--  Revision History
--
    REVISION        "0710010000Z"
    DESCRIPTION     "Rev 1.0                01 Oct 2007 00:00
                     1.0 release of the TIMETRA-SDP-MIB from TIMETRA-SERV-MIB."

    ::= { timetraSRMIBModules 56 }

-- --------------------------------------------------------------------
--      ALCATEL-IND1-TIMETRA-SERV-MIB organisation
-- --------------------------------------------------------------------
tmnxSdpObjs             OBJECT IDENTIFIER ::= { tmnxServObjs 4 }
    tmnxSdpNotifyObjs       OBJECT IDENTIFIER ::= { tmnxSdpObjs 100 }

tmnxSdpConformance      OBJECT IDENTIFIER ::= { tmnxServConformance 4 }

sdpTrapsPrefix          OBJECT IDENTIFIER ::= { tmnxServNotifications 4 }
    sdpTraps                OBJECT IDENTIFIER ::= { sdpTrapsPrefix 0 }

-- --------------------------------------------------------------------
-- tmnxSdpObjs group
-- --------------------------------------------------------------------
sdpNumEntries OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The current number of SDPs configured in this
                     device."
    ::= { tmnxSdpObjs 1 }

sdpNextFreeId OBJECT-TYPE
    SYNTAX          SdpId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The next available value for sdpId."
    ::= { tmnxSdpObjs 2 }

-- ----------------------------
-- SDP Table
-- ----------------------------
sdpInfoTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains SDP information."
    ::= { tmnxSdpObjs 3 }

sdpInfoEntry OBJECT-TYPE
    SYNTAX          SdpInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Information about a specific SDP."
    INDEX           { sdpId }
    ::= { sdpInfoTable 1 }

SdpInfoEntry ::=
    SEQUENCE {
        sdpId                                    SdpId,
        sdpRowStatus                             RowStatus,
        sdpDelivery                              INTEGER,
        sdpFarEndIpAddress                       IpAddress,
        sdpLspList                               LspIdList,
        sdpDescription                           ServObjDesc,
        sdpLabelSignaling                        INTEGER,
        sdpAdminStatus                           ServiceAdminStatus,
        sdpOperStatus                            INTEGER,
        sdpAdminPathMtu                          INTEGER,
        sdpOperPathMtu                           Integer32,
        sdpKeepAliveAdminStatus                  INTEGER,
        sdpKeepAliveOperStatus                   INTEGER,
        sdpKeepAliveHelloTime                    INTEGER,
        sdpKeepAliveMaxDropCount                 INTEGER,
        sdpKeepAliveHoldDownTime                 INTEGER,
        sdpLastMgmtChange                        TimeStamp,
        sdpKeepAliveHelloMessageLength           INTEGER,
        sdpKeepAliveNumHelloRequestMessages      Unsigned32,
        sdpKeepAliveNumHelloResponseMessages     Unsigned32,
        sdpKeepAliveNumLateHelloResponseMessages Unsigned32,
        sdpKeepAliveHelloRequestTimeout          INTEGER,
        sdpLdpEnabled                            TruthValue,
        sdpVlanVcEtype                           Unsigned32,
        sdpAdvertisedVllMtuOverride              TruthValue,
        sdpOperFlags                             BITS,
        sdpLastStatusChange                      TimeStamp,
        sdpMvplsMgmtService                      TmnxServId,
        sdpMvplsMgmtSdpBndId                     SdpBindId,
        sdpCollectAcctStats                      TruthValue,
        sdpAccountingPolicyId                    Unsigned32,
        sdpClassFwdingEnabled                    TruthValue,
        sdpClassFwdingDefaultLsp                 TmnxVRtrMplsLspID,
        sdpClassFwdingMcLsp                      TmnxVRtrMplsLspID,
        sdpMetric                                Unsigned32,
        sdpAutoSdp                               TruthValue,
        sdpSnmpAllowed                           TruthValue,
        sdpPBBEtype                              Unsigned32,
        sdpBandwidthBookingFactor                Unsigned32,
        sdpOperBandwidth                         Unsigned32,
        sdpAvailableBandwidth                    Unsigned32,
        sdpMaxBookableBandwidth                  Unsigned32,
        sdpBookedBandwidth                       Unsigned32,
        sdpCreationOrigin                        L2RouteOrigin
    }

sdpId OBJECT-TYPE
    SYNTAX          SdpId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "SDP identifier."
    ::= { sdpInfoEntry 1 }

sdpRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object indicates the status of this row. The
                     only values supported during a set operation are
                     'createAndGo' and 'destroy'. To delete an entry
                     from this table, the corresponding SDP must be
                     administratively down, not bound to any service,
                     and not in use as a mirror destination."
    ::= { sdpInfoEntry 2 }

sdpDelivery OBJECT-TYPE
    SYNTAX          INTEGER {
                        gre  (1),
                        mpls (2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the type of delivery used
                     by this SDP: e.g. GRE or MPLS. The value of this
                     object must be specified when the row is created
                     and cannot be changed while the row status is
                     'active'."
    ::= { sdpInfoEntry 3 }

sdpFarEndIpAddress OBJECT-TYPE
    SYNTAX          IpAddress
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the IP address of the
                     remote end of the GRE or MPLS tunnel defined
                     by this SDP. The value of this object must
                     be set for the row to become 'active', and
                     can only be changed while the admin status
                     of the SDP is 'down'."
    ::= { sdpInfoEntry 4 }

sdpLspList OBJECT-TYPE
    SYNTAX          LspIdList
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "When the SDP delivery specified by sdpDelivery
                     is 'mpls', this object specifies the list of
                     LSPs used to reach the far-end ESR device.
                     All the LSPs in this list must terminate at the
                     IP address specified by sdpFarEndIpAddress. This
                     object is otherwise insignificant and should
                     contain a value of 0.

                     When this list has more than one element, the
                     Alcatel 7x50 SR router will use all of the LSPs for
                     load balancing purposes. Each LSP ID in the list
                     corresponds to the vRtrMplsLspIndex of the given
                     MPLS LSP."
    ::= { sdpInfoEntry 5 }

sdpDescription OBJECT-TYPE
    SYNTAX          ServObjDesc
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "Generic information about this SDP."
    DEFVAL          { "" }
    ::= { sdpInfoEntry 6 }

sdpLabelSignaling OBJECT-TYPE
    SYNTAX          INTEGER {
                        none (1),
                        tldp (2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the signaling protocol
                     used to obtain the ingress and egress labels
                     used in frames transmitted and received on
                     this SDP. When the value of this object is
                     'none' then the labels are manually assigned
                     at the time the SDP is bound to a service. The
                     value of this object can only be changed while
                     the admin status of the SDP is 'down'."
    DEFVAL          { tldp }
    ::= { sdpInfoEntry 7 }

sdpAdminStatus OBJECT-TYPE
    SYNTAX          ServiceAdminStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The desired state of this SDP."
    DEFVAL          { down }
    ::= { sdpInfoEntry 8 }

sdpOperStatus OBJECT-TYPE
    SYNTAX          INTEGER {
                        up                     (1),
                        notAlive               (2),
                        notReady               (3),
                        invalidEgressInterface (4),
                        transportTunnelDown    (5),
                        down                   (6)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The operating state of this SDP. The value
                     'notAlive' is valid only when keep-alive is
                     enabled, and it means that the keep-alive
                     operating status is not alive. The value
                     'notReady' is valid only when this SDP uses a
                     label signaling protocol (e.g. TLDP) and it means
                     that the signaling session with the far-end peer
                     has not been established. The value
                     'invalidEgressInterface' indicates that the
                     IOM's have detected that the egress interface
                     towards the far-end device is not a network
                     port."
    ::= { sdpInfoEntry 9 }

sdpAdminPathMtu OBJECT-TYPE
    SYNTAX          INTEGER (0|576..9194)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the desired largest service
                     frame size (in octets) that can be transmitted
                     through this SDP to the far-end ESR, without
                     requiring the packet to be fragmented. The default
                     value of zero indicates that the path MTU should
                     be computed dynamically from the corresponding
                     MTU of the tunnel."
    DEFVAL          { 0 }
    ::= { sdpInfoEntry 10 }

sdpOperPathMtu OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object specifies the actual largest service
                     frame size (in octets) that can be transmitted
                     through this SDP to the far-end ESR, without
                     requiring the packet to be fragmented. In order
                     to be able to bind this SDP to a given service,
                     the value of this object must be equal to or
                     larger than the MTU of the service, as defined
                     by its svcMtu."
    ::= { sdpInfoEntry 11 }

sdpKeepAliveAdminStatus OBJECT-TYPE
    SYNTAX          INTEGER {
                        enabled  (1),
                        disabled (2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object is used to enable or disable the
                     keep-alive protocol used to determine the
                     operating status of this SDP."
    DEFVAL          { disabled }
    ::= { sdpInfoEntry 12 }

sdpKeepAliveOperStatus OBJECT-TYPE
    SYNTAX          INTEGER {
                        alive            (1),
                        noResponse       (2),
                        senderIdInvalid  (3),
                        responderIdError (4),
                        disabled         (5)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The current status of the keep-alive protocol.
                     The value 'alive' indicates that the far-end
                     ESR is replying the SDP Echo Requests messages
                     sent by this device indicating no error condition.
                     The value 'noResponse' indicates that the number
                     of consecutive SDP Echo Request messages unack-
                     nowledged by the far-end ESR exceeded the limit
                     defined by sdpKeepAliveMaxDropCount. The values
                     'senderIdInvalid' and 'responderIdError' are
                     two error conditions detected by the far-end ESR.
                     The value 'disabled' indicates that the keep-alive
                     protocol is not enabled."
    ::= { sdpInfoEntry 13 }

sdpKeepAliveHelloTime OBJECT-TYPE
    SYNTAX          INTEGER (1..3600)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies how often the SDP Echo
                     Request messages are transmitted on this SDP."
    DEFVAL          { 10 }
    ::= { sdpInfoEntry 14 }

sdpKeepAliveMaxDropCount OBJECT-TYPE
    SYNTAX          INTEGER (1..5)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the maximum number of
                     consecutive SDP Echo Request messages that can
                     be unacknowledged before the keep-alive
                     protocol reports a fault."
    DEFVAL          { 3 }
    ::= { sdpInfoEntry 15 }

sdpKeepAliveHoldDownTime OBJECT-TYPE
    SYNTAX          INTEGER (0..3600)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the amount of time to
                     wait before the keep-alive operating status
                     is eligible to enter the 'alive' state."
    DEFVAL          { 10 }
    ::= { sdpInfoEntry 16 }

sdpLastMgmtChange OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sysUpTime at the time of the
                     most recent management-initiated change to
                     this SDP."
    ::= { sdpInfoEntry 17 }

sdpKeepAliveHelloMessageLength OBJECT-TYPE
    SYNTAX          INTEGER (0 | 40..9198)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the length of the
                     SDP Echo Request messages transmitted on
                     this SDP. The default value of zero
                     indicates that the message length should
                     be equal to the SDP's operating path MTU,
                     as specified by sdpOperPathMtu. When the
                     default value is overridden, the message
                     length is sdpKeepAliveHelloMessageLength."
    DEFVAL          { 0 }
    ::= { sdpInfoEntry 18 }

sdpKeepAliveNumHelloRequestMessages OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The number of SDP Echo Request messages
                     transmitted since the keep-alive was
                     administratively enabled or the counter
                     was cleared."
    ::= { sdpInfoEntry 19 }

sdpKeepAliveNumHelloResponseMessages OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The number of SDP Echo Response messages
                     received since the keep-alive was
                     administratively enabled or the counter
                     was cleared."
    ::= { sdpInfoEntry 20 }

sdpKeepAliveNumLateHelloResponseMessages OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The number of SDP Echo Response messages
                     received after the corresponding Request
                     timeout timer expired."
    ::= { sdpInfoEntry 21 }

sdpKeepAliveHelloRequestTimeout OBJECT-TYPE
    SYNTAX          INTEGER (1..10)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The number of seconds to wait for an SDP
                     Echo Response message before declaring
                     a timeout."
    DEFVAL { 5 }
    ::= { sdpInfoEntry 22 }

sdpLdpEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "When the value of this object is 'true'
                     the transport LSP's are signalled by LDP,
                     as opposed to being provisioned static or
                     RSVP-signalled LSP's. This object applies
                     only to MPLS SDP's."
    DEFVAL { false }
    ::= { sdpInfoEntry 23 }

sdpVlanVcEtype OBJECT-TYPE
    SYNTAX          Unsigned32 ('600'H..'ffff'H)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the Ethertype used in
                     frames sent out this SDP, when the VC type
                     is vlan."
    DEFVAL          { '8100'H }
    ::= { sdpInfoEntry 24 }

sdpAdvertisedVllMtuOverride OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "When the value of this object is 'true'
                     the advertised MTU of a VLL spoke SDP bind
                     includes the 14-byte L2 header, so that it is
                     backward compatible with pre-2.0 software."
    DEFVAL { false }
    ::= { sdpInfoEntry 25 }

sdpOperFlags OBJECT-TYPE
    SYNTAX          BITS {
                        sdpAdminDown            (0),
                        signalingSessionDown    (1),
                        transportTunnelDown     (2),
                        keepaliveFailure        (3),
                        invalidEgressInterface  (4),
                        noSystemIpAddress       (5),
                        transportTunnelUnstable (6)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object specifies all the conditions that
                     affect the operating status of this SDP."
    ::= { sdpInfoEntry 26 }

sdpLastStatusChange OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sysUpTime at the time of the
                     most recent operating status change to this
                     SDP."
    ::= { sdpInfoEntry 27 }

sdpMvplsMgmtService OBJECT-TYPE
    SYNTAX          TmnxServId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpMvplsMgmtService indicates
                     the service Id of the service where the STP instance
                     is running that is managing this SDP. This object is
                     only valid if sdpMvplsMgmtService is different from
                     0."
    ::= { sdpInfoEntry 28 }

sdpMvplsMgmtSdpBndId OBJECT-TYPE
    SYNTAX          SdpBindId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpMvplsMgmtSdpBndId indicates
                     which SDP bind in the mVPLS instance specified in
                     sdpMvplsMgmtService is controlling this SDP. This
                     object is only valid if sdpMvplsMgmtService is
                     different from 0."
    ::= { sdpInfoEntry 29 }

sdpCollectAcctStats OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of the object sdpCollectAcctStats specifies
                     whether the agent collects accounting statistics for this
                     SDP. When the value is 'true' the agent
                     collects accounting statistics on this SDP."
    DEFVAL          { false }
    ::= { sdpInfoEntry 30 }

sdpAccountingPolicyId OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpAccountingPolicyId specifies the
                     policy to use to collect accounting statistics on
                     this SDP. The value zero indicates that the
                     agent should use the default accounting policy,
                     if one exists."
    DEFVAL          { 0 }
    ::= { sdpInfoEntry 31 }

sdpClassFwdingEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpClassFwdingEnabled specifies the
                     admin state of class-based forwarding on this SDP. When
                     the value is 'true', class-based forwarding is enabled."
    DEFVAL          { false } 
    ::= { sdpInfoEntry 32 }

sdpClassFwdingDefaultLsp OBJECT-TYPE
    SYNTAX          TmnxVRtrMplsLspID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpClassFwdingDefaultLsp specifies the
                     LSP ID that is used as a default when class-based
                     forwarding is enabled on this SDP. This object
                     must be set when enabling class-based forwarding."
    DEFVAL          { 0 }
    ::= { sdpInfoEntry 33 }

sdpClassFwdingMcLsp OBJECT-TYPE
    SYNTAX          TmnxVRtrMplsLspID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpClassFwdingMcLsp specifies the LSP ID that
                     all multicast traffic will be forwarded on when class-based
                     forwarding is enabled on this SDP. When this object has its
                     default value, multicast traffic will be forwarded 
                     on an LSP according to its forwarding class mapping."
    DEFVAL          { 0 }
    ::= { sdpInfoEntry 34 }

sdpMetric       OBJECT-TYPE
    SYNTAX          Unsigned32 (0..65535)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpMetric specifies the metric to be used 
                     within the Tunnel Table Manager for decision making 
                     purposes. When multiple SDPs going to the same destination 
                     exist, this value is used as a tie-breaker by Tunnel Table 
                     Manager users like MP-BGP to select route with lower 
                     value."
    DEFVAL          { 0 }
    ::= { sdpInfoEntry 35 }

sdpAutoSdp      OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpAutoSdp indicates whether this is an 
                     Auto generated SDP from RADIUS discovery or BGP
                     auto-discovery."
    ::= { sdpInfoEntry 36 }

sdpSnmpAllowed OBJECT-TYPE
    SYNTAX           TruthValue
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      "The value of sdpSnmpAllowed indicates if SNMP sets 
                      are allowed on this SDP."
    ::= { sdpInfoEntry 37 }

sdpPBBEtype OBJECT-TYPE
    SYNTAX          Unsigned32 ('600'H..'ffff'H)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object specifies the Ethertype used in frames sent
                     out on this SDP when sdpBindVcType is 'vlan' for
                     Provider Backbone Bridging frames."
    DEFVAL          { '88E7'H }
    ::= { sdpInfoEntry 38 }

sdpBandwidthBookingFactor OBJECT-TYPE
    SYNTAX           Unsigned32 (0..1000)
    MAX-ACCESS       read-create
    STATUS           current
    DESCRIPTION      "sdpBandwidthBookingFactor is used to calculate the max
                     SDP available bandwidth.  The value of
                     sdpBandwidthBookingFactor specifies the percentage of the
                     SDP max available bandwidth for VLL call admission. When
                     the value of sdpBandwidthBookingFactor is set to zero (0),
                     no new VLL spoke-sdp bindings with non-zero bandwidth are
                     permitted with this SDP.  Overbooking, >100% is allowed."
    DEFVAL          { 100 }
    ::= { sdpInfoEntry 39 }

sdpOperBandwidth OBJECT-TYPE
    SYNTAX           Unsigned32
    UNITS            "kilo-bits per second"
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      "The value of sdpOperBandwidth indicates the operational 
                      Bandwidth in kilo-bits per seconds (Kbps) available for 
                      this SDP. The value sdpOperBandwidth is determined by the 
                      sum of the bandwidth of all the RSVP LSPs used by the 
                      SDP."
    ::= { sdpInfoEntry 40 }

sdpAvailableBandwidth OBJECT-TYPE
    SYNTAX           Unsigned32 
    UNITS            "kilo-bits per second"
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      "The value of sdpAvailableBandwidth indicates the Bandwidth
                      that is still free for booking by the SDP bindings on the
                      SDP."
    ::= { sdpInfoEntry 41 }

sdpMaxBookableBandwidth OBJECT-TYPE
    SYNTAX           Unsigned32
    UNITS            "kilo-bits per second"
    MAX-ACCESS       accessible-for-notify
    STATUS           current
    DESCRIPTION      "The value of sdpMaxBookableBandwidth indicates the max 
                      Bandwidth that the SDP has for booking by the SDP 
                      bindings. The value of sdpMaxBookableBandwidth is 
                      calculated as follow:
  
                      sdpMaxBookableBandwidth = sdpOperBandwidth * 
                                                sdpBandwidthBookingFactor
                      "
    ::= { sdpInfoEntry 42 }

sdpBookedBandwidth OBJECT-TYPE
    SYNTAX           Unsigned32
    UNITS            "kilo-bits per second"
    MAX-ACCESS       accessible-for-notify
    STATUS           current
    DESCRIPTION      "The value of sdpBookedBandwidth indicates the 
                      SDP Bandwidth that has been booked by the SDP 
                      bindings."
    ::= { sdpInfoEntry 43 }

sdpCreationOrigin   OBJECT-TYPE
    SYNTAX          L2RouteOrigin
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpCreationOrigin indicates the protocol or 
                     mechanism which created this SDP."
    ::= { sdpInfoEntry 44 }

-- -------------------------
-- SDP Bind Table
-- -------------------------
sdpBindTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains SDP binding information."
    ::= { tmnxSdpObjs 4 }

sdpBindEntry OBJECT-TYPE
    SYNTAX          SdpBindEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Information about a specific SDP binding."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindTable 1 }

SdpBindEntry ::=
    SEQUENCE {
        sdpBindId                           SdpBindId,
        sdpBindRowStatus                    RowStatus,
        sdpBindAdminIngressLabel            Unsigned32,
        sdpBindAdminEgressLabel             Unsigned32,
        sdpBindOperIngressLabel             Unsigned32,
        sdpBindOperEgressLabel              Unsigned32,
        sdpBindAdminStatus                  ServiceAdminStatus,
        sdpBindOperStatus                   INTEGER,
        sdpBindLastMgmtChange               TimeStamp,
        sdpBindType                         INTEGER,
        sdpBindIngressMacFilterId           TFilterID,
        sdpBindIngressIpFilterId            TFilterID,
        sdpBindEgressMacFilterId            TFilterID,
        sdpBindEgressIpFilterId             TFilterID,
        sdpBindVpnId                        VpnId,
        sdpBindCustId                       TmnxCustId,
        sdpBindVcType                       SdpBindVcType,
        sdpBindVlanVcTag                    Unsigned32,
        sdpBindSplitHorizonGrp              ServObjName,
        sdpBindOperFlags                    BITS,
        sdpBindLastStatusChange             TimeStamp,
        sdpBindIesIfIndex                   InterfaceIndexOrZero,
        sdpBindMacPinning                   TmnxEnabledDisabled,
        sdpBindIngressIpv6FilterId          TFilterID,
        sdpBindEgressIpv6FilterId           TFilterID,
        sdpBindCollectAcctStats             TruthValue,
        sdpBindAccountingPolicyId           Unsigned32,
        sdpBindPwPeerStatusBits             BITS,
        sdpBindPeerVccvCvBits               BITS,
        sdpBindPeerVccvCcBits               BITS,
        sdpBindControlWordBit               TruthValue,
        sdpBindOperControlWord              TruthValue,
        sdpBindEndPoint                     ServObjName,
        sdpBindEndPointPrecedence           Unsigned32,
        sdpBindIsICB                        TruthValue,
        sdpBindPwFaultInetAddressType       InetAddressType,
        sdpBindPwFaultInetAddress           InetAddress,
        sdpBindClassFwdingOperState         TmnxOperState,
        sdpBindForceVlanVcForwarding        TruthValue,
        sdpBindAdminBandwidth               SdpBindBandwidth,
        sdpBindOperBandwidth                SdpBindBandwidth
    }

sdpBindId OBJECT-TYPE
    SYNTAX          SdpBindId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "SDP Binding identifier."
    ::= { sdpBindEntry 1 }

sdpBindRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object indicates the status of this row. The
                     only values supported during a set operation are
                     'createAndGo' and 'destroy'."
    ::= { sdpBindEntry 2 }

sdpBindAdminIngressLabel OBJECT-TYPE
    SYNTAX          Unsigned32 (0 | 1 | 2048..18431)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The static MPLS VC label used by the far-end device
                     to send packets to this device in this service via
                     this SDP. The value of sdpBindAdminIngressLabel is
                     1 when it is used by a mirror service. All mirror SDPs
                     use this label to avoid the unnecessary use of
                     additional labels."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 3 }

sdpBindAdminEgressLabel OBJECT-TYPE
    SYNTAX          Unsigned32 (0 | 16..1048575)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The static MPLS VC label used by this device to send
                     packets to the far-end device in this service via
                     this SDP."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 4 }

sdpBindOperIngressLabel OBJECT-TYPE
    SYNTAX          Unsigned32 (0 | 1..1048575)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The MPLS label used by the far-end device to send
                     packets to this device in this service via this SDP.
                     This label is either sdpBindAdminIngressLabel, if
                     not null, or the one obtained via the SDP's signaling
                     protocol."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 5 }

sdpBindOperEgressLabel OBJECT-TYPE
    SYNTAX          Unsigned32 (0 | 1..1048575)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The MPLS label used by this device to send packets
                     to the far-end device in this service via this SDP.
                     This label is either sdpBindAdminEgressLabel, if
                     not null, or the one obtained via the SDP's signaling
                     protocol."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 6 }

sdpBindAdminStatus OBJECT-TYPE
    SYNTAX          ServiceAdminStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The desired state of this Service-SDP binding."
    DEFVAL          { up }
    ::= { sdpBindEntry 7 }

sdpBindOperStatus OBJECT-TYPE
    SYNTAX          INTEGER {
                        up                 (1),
                        noEgressLabel      (2),
                        noIngressLabel     (3),
                        noLabels           (4),
                        down               (5),
                        svcMtuMismatch     (6),
                        sdpPathMtuTooSmall (7),
                        sdpNotReady        (8),
                        sdpDown            (9),
                        sapDown            (10)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of sdpBindOperStatus indicates the operating status of
         this Service-SDP binding.
            'up'               The Service-SDP binding is operational.

            'noEgressLabel'    The ingress label is available but the
                               egress one is missing.

            'noIngressLabel'   The egress label is available but the
                               ingress one is not.

            'noLabels'         Both the ingress and the egress labels
                               are missing.

            'down'             The binding is administratively down.

            'svcMtuMismatch'   Both labels are available, but a service
                               MTU mismatch was detected between the local
                               and the far-end devices.

            'sdpPathMtuTooSmall' The operating path MTU of the corresponding
                                 SDP is smaller than the service MTU.

            'sdpNotReady'      The SDP's signaling session is down.

            'sdpDown'          The SDP is not operationally up.

            'sapDown'          The SAP associated with the service is down."
    ::= { sdpBindEntry 8 }

sdpBindLastMgmtChange OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sysUpTime at the time of the
                     most recent management-initiated change to
                     this Service-SDP binding."
    ::= { sdpBindEntry 9 }

sdpBindType OBJECT-TYPE
    SYNTAX          INTEGER {
                        spoke (1),
                        mesh  (2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This object indicates whether this Service SDP
                     binding is a spoke or a mesh.  The value of this
                     object must be specified when the row is created
                     and cannot be changed while the row status is
                     'active'."
    DEFVAL          { mesh }
    ::= { sdpBindEntry 10 }

sdpBindIngressMacFilterId OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The row index in the tMacFilterTable
                     corresponding to this ingress filter,
                     or zero if no filter is specified."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 11 }

sdpBindIngressIpFilterId OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The row index in the tIPFilterTable
                     corresponding to this ingress filter,
                     or zero if no filter is specified."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 12 }

sdpBindEgressMacFilterId OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The row index in the tMacFilterTable
                     corresponding to this egress filter,
                     or zero if no filter is specified."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 13 }

sdpBindEgressIpFilterId OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The row index in the tIPFilterTable
                     corresponding to this egress filter,
                     or zero if no filter is specified."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 14 }

sdpBindVpnId OBJECT-TYPE
    SYNTAX          VpnId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The VPN ID of the associated service."
    ::= { sdpBindEntry 15 }

sdpBindCustId OBJECT-TYPE
    SYNTAX          TmnxCustId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The Customer ID of the associated service."
    ::= { sdpBindEntry 16 }

sdpBindVcType OBJECT-TYPE
    SYNTAX          SdpBindVcType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The value of sdpBindVcType is an enumerated integer that specifies
         the type of virtual circuit (VC) associated with the SDP binding.

         The value 'vpls' is no longer supported."
    ::= { sdpBindEntry 17 }

sdpBindVlanVcTag OBJECT-TYPE
    SYNTAX          Unsigned32 ('0000'H..'0fff'H)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     ""
    DEFVAL          { '0fff'H }
    ::= { sdpBindEntry 18 }

sdpBindSplitHorizonGrp OBJECT-TYPE
    SYNTAX          ServObjName
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "This value of the object sdpBindSplitHorizonGrp specifies
                     the name of the split-horizon group where the spoke SDP
                     Bind belongs to. This object can be set only at the time
                     the row is created. Per default a spoke SDP Bind does not
                     belong to any split-horizon group. The name specified must
                     correspond to an existing split-horizon group in the TLS
                     service where this spoke SDP Bind is defined."
    DEFVAL          { "" }
    ::= { sdpBindEntry 19 }

sdpBindOperFlags OBJECT-TYPE
    SYNTAX          BITS {
                        sdpBindAdminDown       (0), -- SDP Bind is admin down
                        svcAdminDown           (1), -- Service is admin down
                        sapOperDown            (2), -- SAP is oper down (VLL's only)
                        sdpOperDown            (3), -- SDP is oper down
                        sdpPathMtuTooSmall     (4), -- SDP's path MTU is less than Service MTU
                        noIngressVcLabel       (5), -- No ingress VC label
                        noEgressVcLabel        (6), -- No egress VC label
                        svcMtuMismatch         (7), -- Service MTU mismatch with the remote PE
                        vcTypeMismatch         (8), -- VC type mismatch with the remote PE
                        relearnLimitExceeded   (9), -- MAC relearn limit was exceeded (TLS only)
                        iesIfAdminDown         (10),-- IP interface is admin down (IES and VPRN only)
                        releasedIngressVcLabel (11),-- Peer released our ingress VC label
                        labelsExhausted        (12),-- Label Manager has ran out of labels
                        svcParamMismatch       (13),-- Service-specific parameter mismatch
                        insufficientBandwidth  (14),-- Insufficient bandwidth to allocate to SDP binding
                        pwPeerFaultStatusBits  (15),-- Received PW fault status bits from peer      
                        meshSdpDown            (16) -- Mesh SDP Down
                        
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object specifies all the conditions that
                     affect the operating status of this SDP Bind."
    ::= { sdpBindEntry 20 }

sdpBindLastStatusChange OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindLastStatusChange specifies
                     the value of sysUpTime at the time of the most recent
                     operating status change to this SDP Bind."
    ::= { sdpBindEntry 21 }

sdpBindIesIfIndex OBJECT-TYPE
    SYNTAX          InterfaceIndexOrZero
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "When this SDP Bind is defined on an IES service
                     and the value of sdpBindType is 'spoke', this
                     object specifies the index of the associated IES
                     interface. The value of this object can be set
                     only when the row is created and cannot be changed
                     while the row status is 'active'. This object is
                     otherwise not significant and should have
                     the value zero."
    ::= { sdpBindEntry 22 }

sdpBindMacPinning OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindMacPinning specifies
                     whether or not MAC address pinning is active on this SDP
                     bind (mesh or spoke). Setting the value to enable disables
                     re-learning of MAC addresses on other SAPs or SDPs
                     within the same VPLS; the MAC address will hence
                     remain attached to the SDP Bind for the duration of
                     its age-timer. This object has effect only for MAC
                     addresses learned via the normal MAC learning
                     process, and not for entries learned via DHCP. The
                     value will be set by default to disabled. However for
                     a spoke SDP that belongs to a residential SHG, the
                     value is set to enabled by the system, and cannot be
                     altered by the operator. This object applies to spoke-SDP
                     associated with the service with svcType set to 
                     'tls'."
    ::= { sdpBindEntry 23 }

sdpBindIngressIpv6FilterId OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindIngressIpv6FilterId
                     specifies the row index in the tIPv6FilterTable
                     corresponding to this ingress ipv6 filter,
                     or zero if no ipv6 filter is specified."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 24 }

sdpBindEgressIpv6FilterId OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindEgressIpv6FilterId
                     specifies the row index in the tIPv6FilterTable
                     corresponding to this egress ipv6 filter,
                     or zero if no ipv6 filter is specified."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 25 }

sdpBindCollectAcctStats OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindCollectAcctStats specifies
                     whether the agent collects accounting statistics for this
                     SDP bind. When the value is 'true' the agent
                     collects accounting statistics on this SDP bind."
    DEFVAL          { false }
    ::= { sdpBindEntry 26 }

sdpBindAccountingPolicyId OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpBindAccountingPolicyId specifies the
                     policy to use to collect accounting statistics on
                     this SDP bind. The value zero indicates that the
                     agent should use the default accounting policy,
                     if one exists."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 27 }

sdpBindPwPeerStatusBits OBJECT-TYPE
    SYNTAX          BITS {
                        pwNotForwarding (0),     -- Pseudo Wire Not Forwarding
                        lacIngressFault (1),     -- Local Attachment Circuit Rx 
                                                 -- Fault
                        lacEgresssFault (2),     -- Local Attachment Circuit Tx 
                                                 -- Fault
                        psnIngressFault (3),     -- Local PSN-facing PW Rx Fault
                        psnEgressFault  (4),     -- Local PSN-facing PW Tx Fault
                        pwFwdingStandby (5)      -- Pseudo Wire in Standby mode
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "sdpBindPwPeerStatusBits indicates the bits set by the LDP
                     peer when there is a fault on its side of the pseudowire.
                     LAC failures occur on the SAP that has been configured on
                     the PIPE service, PSN bits are set by SDP-binding failures
                     on the PIPE service.  The pwNotForwarding bit is set when
                     none of the above failures apply, such as an MTU mismatch
                     failure.  This value is only applicable if the peer is
                     using the pseudowire status signalling method to indicate
                     faults."
    ::= { sdpBindEntry 28 }

sdpBindPeerVccvCvBits OBJECT-TYPE
    SYNTAX          BITS {
                        icmpPing                       (0),
                        lspPing                        (1),
                        bfdFaultDetection              (2),
                        bfdFaultDetectionAndSignalling (3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "sdpBindPeerVccvCvBits indicates the CV type bits set by the
                     LDP peer if it supports VCCV (Virtual Circuit Connection
                     Verification) on a pseudowire.  If the peer does not send
                     VCCV information, or does not support it, the bits will
                     be set to 0."
    ::= { sdpBindEntry 29 }

sdpBindPeerVccvCcBits OBJECT-TYPE
    SYNTAX          BITS {
                        pwe3ControlWord          (0),
                        mplsRouterAlertLabel     (1),
                        mplsPwDemultiplexorLabel (2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "sdpBindPeerVccvCcBits indicates the CC type bits set by the
                     LDP peer if it supports VCCV (Virtual Circuit Connection
                     Verification) on a pseudowire.  If the peer does not send
                     VCCV information, or does not support it, the bits will
                     all be 0."
    ::= { sdpBindEntry 30 }

sdpBindControlWordBit  OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION     "sdpBindControlWordBit specifies whether the use of the
                     'ControlWord' is preferred or not. The value of 
                     sdpBindControlWordBit is exchanged with LDP peer during
                     pseudowire negotiation time. The default value is
                     determined by sdpBindVcType. sdpBindVcType of atmSdu and 
                     frDlci must have default value of 'true'. Other values of 
                     sdpBindVcType must have default value of 'false'."
    ::= { sdpBindEntry 31 }

sdpBindOperControlWord  OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION     "sdpBindOperControlWord indicates whether the 'ControlWord'
                     is used or not. The value of sdpBindOperControlWord is 
                     negotiated with the LDP peer.  When both the local and the
                     peer prefer the use of the 'ControlWord', sdpBindOperControlWord
                     has the value of 'true'."
    ::= { sdpBindEntry 32 }

sdpBindEndPoint         OBJECT-TYPE
    SYNTAX      ServObjName
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION     "sdpBindEndPoint specifies the service endpoint to which
                     this SDP bind is attached. The svcId of the SDP bind MUST
                     match the svcId of the service endpoint."
    DEFVAL      { "" }
    ::= { sdpBindEntry 33 }

sdpBindEndPointPrecedence       OBJECT-TYPE
    SYNTAX      Unsigned32 (0..4)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION     "sdpBindEndPointPrecedence specifies the precedence of this
                     SDP bind when there are multiple SDP binds attached to one
                     service endpoint. The value 0 can only be assigned to one
                     SDP bind, making it the primary SDP bind. When an SDP bind
                     goes down, the next highest precedence SDP bind begins 
                     forwarding traffic."
    DEFVAL      { 4 }
    ::= { sdpBindEntry 34 } 

sdpBindIsICB    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION     "sdpBindIsICB specifies whether this sdpBind is an 
                     inter-chassis backup SDP bind."
    DEFVAL      { false }
    ::= { sdpBindEntry 35 } 

sdpBindPwFaultInetAddressType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindPwFaultInetAddressType
                     indicates the address type of sdpBindPwFaultInetAddress."
    ::= { sdpBindEntry 36 }

sdpBindPwFaultInetAddress    OBJECT-TYPE
    SYNTAX          InetAddress  (SIZE(0|4|16))
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindPwFaultInetAddress indicates the IP
                     address that was included in the pseudowire status 
                     message sent by the LDP peer.  This value is only 
                     applicable if the peer is using the pseudowire status 
                     signalling method to indicate faults."
    ::= { sdpBindEntry 37 } 

sdpBindClassFwdingOperState     OBJECT-TYPE
    SYNTAX          TmnxOperState
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindClassFwdingOperState indicates the
                     operational state of forwarding-class based forwarding
                     on this sdpBind. 

                     When the SDP this sdpBind is bound to has 
                     sdpClassFwdingEnabled set to 'false', the value of 
                     sdpBindClassFwdingOperState is 'outOfService'.

                     When the SDP this sdpBind is bound to has 
                     sdpClassFwdingEnabled set to 'true' and the svcType
                     of the service this sdpBind is defined on is 'tls',
                     'vprn', or 'ies', the value of 
                     sdpBindClassFwdingOperState is 'inService'. If the
                     service has svcVcSwitching set to 'true', the value
                     of sdpBindClassFwdingOperState is 'inService'

                     When the SDP this sdpBind is bound to has 
                     sdpClassFwdingEnabled set to 'true' and the svcType
                     of the service this sdpBind is defined on is 'epipe',
                     'apipe', 'fpipe', or 'ipipe' with no SAP 
                     defined on the service, the value of 
                     sdpBindClassFwdingOperState is 'unknown'. If the 
                     service has a SAP with a NULL 
                     sapIngressSharedQueuePolicy, the value of 
                     sdpBindClassFwdingOperState is 'outOfService'. If the
                     service has a SAP with a non-NULL 
                     sapIngressSharedQueuePolicy, the value of 
                     sdpBindClassFwdingOperState is 'inService'."
     ::= { sdpBindEntry 38 } 

sdpBindForceVlanVcForwarding OBJECT-TYPE
    SYNTAX           TruthValue
    MAX-ACCESS       read-create
    STATUS           current
    DESCRIPTION
        "The value of sdpBindForceVlanVcForwarding specifies whether or not
        vc-vlan-type forwarding is forced in the data-path for the sdp which
        have sdpBindVcType set to 'ether'.  When set to 'true'
        vc-vlan-type forwarding is forced.

        An 'inconsistentValue' error is returned when an attempt is made to set
        the value of sdpBindForceVlanVcForwarding to 'true' and sdpBindVcType is
        not set to 'ether'."
    DEFVAL          { false }
    ::= { sdpBindEntry 39 }

sdpBindAdminBandwidth    OBJECT-TYPE
    SYNTAX      SdpBindBandwidth
    UNITS       "kilo-bits per second"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION     "The value of the object sdpBindAdminBandwidth specifies the
                     bandwidth that needs to be reserved for this SDP binding in
                     kilo-bits per second. The SdpBindBandwidth object only applies 
                     to the SDP bindings under the epipe(1), apipe(7), fpipe(8), 
                     ipipe(9) and cpipe(10) services."
    DEFVAL          { 0 }
    ::= { sdpBindEntry 40 } 

sdpBindOperBandwidth    OBJECT-TYPE
    SYNTAX      SdpBindBandwidth
    UNITS       "kilo-bits per second"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION     "The value of the object sdpBindOperBandwidth indicates the
                     bandwidth that has been reserved for this SDP binding in 
                     kilo-bits per second. The value 0 indicates that SDP doesn't 
                     have bandwidth to satisfy the bandwidth requirement of this 
                     SDP binding. The sdpBindOperBandwidth object only applies 
                     to the SDP bindings under the epipe(1), apipe(7), fpipe(8), 
                     ipipe(9) and cpipe(10) services."
    ::= { sdpBindEntry 41 } 
    

-- ----------------------------------
-- Base SDP Binding Statistics Table
-- ----------------------------------
sdpBindBaseStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindBaseStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains basic SDP Binding
                     statistics."
    ::= { tmnxSdpObjs 5 }

sdpBindBaseStatsEntry OBJECT-TYPE
    SYNTAX          SdpBindBaseStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Basic statistics about a specific SDP
                     Binding."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindBaseStatsTable 1 }

SdpBindBaseStatsEntry ::=
    SEQUENCE {
        sdpBindBaseStatsIngressForwardedPackets  Counter64,
        sdpBindBaseStatsIngressDroppedPackets    Counter64,
        sdpBindBaseStatsEgressForwardedPackets   Counter64,
        sdpBindBaseStatsEgressForwardedOctets    Counter64,
        sdpBindBaseStatsCustId                   TmnxCustId,
        sdpBindBaseStatsIngFwdOctets             Counter64,
        sdpBindBaseStatsIngDropOctets            Counter64
    }

sdpBindBaseStatsIngressForwardedPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     ""
    ::= { sdpBindBaseStatsEntry 1 }

sdpBindBaseStatsIngressDroppedPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     ""
    ::= { sdpBindBaseStatsEntry 2 }

sdpBindBaseStatsEgressForwardedPackets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     ""
    ::= { sdpBindBaseStatsEntry 3 }

sdpBindBaseStatsEgressForwardedOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     ""
    ::= { sdpBindBaseStatsEntry 4 }

sdpBindBaseStatsCustId OBJECT-TYPE
    SYNTAX          TmnxCustId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The Customer ID of the associated service."
    ::= { sdpBindBaseStatsEntry 5 }

sdpBindBaseStatsIngFwdOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     ""
    ::= { sdpBindBaseStatsEntry 6 }

sdpBindBaseStatsIngDropOctets OBJECT-TYPE
    SYNTAX          Counter64
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     ""
    ::= { sdpBindBaseStatsEntry 7 }

-- ------------------------------------------
-- TLS SDP Bind Table
-- ------------------------------------------
sdpBindTlsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindTlsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains TLS spoke SDP Bind
                     information."
    ::= { tmnxSdpObjs 6 }

sdpBindTlsEntry OBJECT-TYPE
    SYNTAX          SdpBindTlsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "TLS specific information about an SDP Bind."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindTlsTable 1 }

SdpBindTlsEntry ::=
    SEQUENCE {
        sdpBindTlsStpAdminStatus        TmnxEnabledDisabled,
        sdpBindTlsStpPriority           INTEGER,
        sdpBindTlsStpPortNum            INTEGER,
        sdpBindTlsStpPathCost           INTEGER,
        sdpBindTlsStpRapidStart         TmnxEnabledDisabled,
        sdpBindTlsStpBpduEncap          INTEGER,
        sdpBindTlsStpPortState          TStpPortState,
        sdpBindTlsStpDesignatedBridge   BridgeId,
        sdpBindTlsStpDesignatedPort     Integer32,
        sdpBindTlsStpForwardTransitions Gauge32,
        sdpBindTlsStpInConfigBpdus      Gauge32,
        sdpBindTlsStpInTcnBpdus         Gauge32,
        sdpBindTlsStpInBadBpdus         Gauge32,
        sdpBindTlsStpOutConfigBpdus     Gauge32,
        sdpBindTlsStpOutTcnBpdus        Gauge32,
        sdpBindTlsStpOperBpduEncap      INTEGER,
        sdpBindTlsStpVpnId              VpnId,
        sdpBindTlsStpCustId             TmnxCustId,
        sdpBindTlsMacAddressLimit       Integer32,
        sdpBindTlsNumMacAddresses       Integer32,
        sdpBindTlsNumStaticMacAddresses Integer32,
        sdpBindTlsMacLearning           TmnxEnabledDisabled,
        sdpBindTlsMacAgeing             TmnxEnabledDisabled,
        sdpBindTlsStpOperEdge           TruthValue,
        sdpBindTlsStpAdminPointToPoint  INTEGER,
        sdpBindTlsStpPortRole           StpPortRole,
        sdpBindTlsStpAutoEdge           TmnxEnabledDisabled,
        sdpBindTlsStpOperProtocol       StpProtocol,
        sdpBindTlsStpInRstBpdus         Gauge32,
        sdpBindTlsStpOutRstBpdus        Gauge32,
        sdpBindTlsLimitMacMove          TlsLimitMacMove,
        sdpBindTlsDiscardUnknownSource  TmnxEnabledDisabled,
        sdpBindTlsMvplsPruneState       MvplsPruneState,
        sdpBindTlsMvplsMgmtService      TmnxServId,
        sdpBindTlsMvplsMgmtSdpBndId     SdpBindId,
        sdpBindTlsStpException          StpExceptionCondition,
        sdpBindTlsL2ptTermination       TmnxEnabledDisabled,
        sdpBindTlsBpduTranslation       SdpBindTlsBpduTranslation,
        sdpBindTlsStpRootGuard          TruthValue,
        sdpBindTlsStpInMstBpdus         Gauge32,
        sdpBindTlsStpOutMstBpdus        Gauge32,
        sdpBindTlsStpRxdDesigBridge     BridgeId,
        sdpBindTlsMacMoveNextUpTime     Unsigned32,
        sdpBindTlsMacMoveRateExcdLeft   Unsigned32,
        sdpBindTlsLimitMacMoveLevel     TlsLimitMacMoveLevel,
        sdpBindTlsBpduTransOper         INTEGER,
        sdpBindTlsL2ptProtocols         L2ptProtocols,
        sdpBindTlsIgnoreStandbySig      TruthValue,
        sdpBindTlsBlockOnMeshFail       TruthValue
    }

sdpBindTlsStpAdminStatus OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpAdminStatus specifies
                     whether this SDP Bind participates in the TLS's Spanning
                     Tree Protocol."
    DEFVAL          { enabled }
    ::= { sdpBindTlsEntry 1 }

sdpBindTlsStpPriority OBJECT-TYPE
    SYNTAX          INTEGER (0..255)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpPriority specifies
                     the value of the port priority field which is contained
                     in the most significant 4 bits of the 16-bit Port ID
                     associated with this SDP Bind. As only the most
                     significant 4 bits of the value are used, the
                     actual value of this object is limited to
                     multiples of 16: e.g. the agent rounds down
                     the value to one of: 0, 16, 32, .. , 224, 240."
    DEFVAL          { 128 }
    ::= { sdpBindTlsEntry 2 }

sdpBindTlsStpPortNum OBJECT-TYPE
    SYNTAX          INTEGER (0..4094)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpPortNum specifies
                     the value of the port number field which is contained in
                     the least significant 12 bits of the 16-bit Port ID
                     associated with this SDP Bind.
                     Values in the range 2048..4094 are automatically
                     assigned by the agent when the SDP Bind is created or
                     when the value of this object is set to zero via
                     management. Values in the range 1..2047 can be set
                     via management, to allow this object to have a
                     deterministic value across system reboots."
    ::= { sdpBindTlsEntry 3 }

sdpBindTlsStpPathCost OBJECT-TYPE
    SYNTAX          INTEGER (1..*********)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpPathCost specifies
                     the contribution of this port to the path cost of paths
                     towards the spanning tree root which include this port."
    DEFVAL          { 10 }
    ::= { sdpBindTlsEntry 4 }

sdpBindTlsStpRapidStart OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpRapidStart
                     specifies whether Rapid Start is enabled on this SDP Bind.
                     When the value is 'enabled' the Spanning Tree Protocol
                     state transitions on this SDP Bind are driven by the value
                     of the 'HelloTime', instead of the value of 'ForwardDelay',
                     thus allowing a faster transition into the forwarding
                     state."
    DEFVAL          { disabled }
    ::= { sdpBindTlsEntry 5 }

sdpBindTlsStpBpduEncap OBJECT-TYPE
    SYNTAX          INTEGER {
                        dynamic (1),
                        dot1d   (2),
                        pvst    (3)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpBpduEncap
                     specifies the type of encapsulation used on BPDUs sent out
                     and received on this SDP Bind."
    DEFVAL          { dynamic }
    ::= { sdpBindTlsEntry 6 }

sdpBindTlsStpPortState OBJECT-TYPE
    SYNTAX          TStpPortState
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpPortState indicates
                     the port's current state as defined by application of the
                     Spanning Tree Protocol. This state controls what action a
                     port takes on reception of a frame. If the bridge has
                     detected a port that is malfunctioning it will
                     place that port into the 'broken' state. All possible
                     states are: learning, forwarding, broken, and discarding."
    ::= { sdpBindTlsEntry 7 }

sdpBindTlsStpDesignatedBridge OBJECT-TYPE
    SYNTAX          BridgeId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpDesignatedBridge
                     indicates the Bridge Identifier of the bridge which this
                     port considers to be the Designated Bridge for this port's
                     segment."
    ::= { sdpBindTlsEntry 8 }

sdpBindTlsStpDesignatedPort OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpDesignatedPort
                     indicates the Port Identifier of the port on the
                     Designated Bridge for this port's segment."
    ::= { sdpBindTlsEntry 9 }

sdpBindTlsStpForwardTransitions OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpForwardTransitions
                     indicates the number of times this port has transitioned
                     from the Learning state to the Forwarding state."
    ::= { sdpBindTlsEntry 10 }

sdpBindTlsStpInConfigBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpInConfigBpdus
                     indicates the number of Configuration BPDUs received on
                     this SDP Bind."
    ::= { sdpBindTlsEntry 11 }

sdpBindTlsStpInTcnBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpInTcnBpdus
                     indicates the number of Topology
                     Change Notification BPDUs received on this SDP Bind."
    ::= { sdpBindTlsEntry 12 }

sdpBindTlsStpInBadBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpInBadBpdus indicates
                     the number of bad BPDUs received on this SDP Bind."
    ::= { sdpBindTlsEntry 13 }

sdpBindTlsStpOutConfigBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpOutConfigBpdus
                     indicates the number of Configuration BPDUs sent out this
                     SDP Bind."
    ::= { sdpBindTlsEntry 14 }

sdpBindTlsStpOutTcnBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpOutTcnBpdus
                     indicates the number of Topology Change Notification BPDUs
                     sent out this SDP Bind."
    ::= { sdpBindTlsEntry 15 }

sdpBindTlsStpOperBpduEncap OBJECT-TYPE
    SYNTAX          INTEGER {
                        dot1d (2),
                        pvst  (3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpOperBpduEncap
                     indicates the operating encapsulation type used on BPDUs
                     sent out and received on this SDP Bind."
    ::= { sdpBindTlsEntry 16 }

sdpBindTlsStpVpnId OBJECT-TYPE
    SYNTAX          VpnId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpVpnId indicates the
                      VPN ID of the associated service."
    ::= { sdpBindTlsEntry 17 }

sdpBindTlsStpCustId OBJECT-TYPE
    SYNTAX          TmnxCustId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpCustId indicates the
                     Customer ID of the associated service."
    ::= { sdpBindTlsEntry 18 }

sdpBindTlsMacAddressLimit OBJECT-TYPE
    SYNTAX          Integer32(0..196607)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsMacAddressLimit
                     specifies the maximum number of learned and static entries
                     allowed in the FDB for this  SDP  Bind.  The value 0
                     means: no limit for this SDP Bind. The command is valid
                     only for spoke SDPs. When the value of
                     ALCATEL-IND1-TIMETRA-CHASSIS-MIB::tmnxChassisOperMode is not 'c', the
                     maximum value of sdpBindTlsMacAddressLimit is '131071'."
    DEFVAL          { 0 }
    ::= { sdpBindTlsEntry 19 }

sdpBindTlsNumMacAddresses OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsNumMacAddresses
                     indicates the number of MAC addresses  currently present
                     in the FDB that belong to this  SDP  Bind (Both learned
                     and static MAC addresses are counted)."
    ::= { sdpBindTlsEntry 20 }

sdpBindTlsNumStaticMacAddresses OBJECT-TYPE
    SYNTAX          Integer32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsNumStaticMacAddresses
                     indicates the number of static MAC addresses currently
                     present in the FDB that belong to this SDP Bind."
    ::= { sdpBindTlsEntry 21 }

sdpBindTlsMacLearning OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsMacLearning specifies
                     whether the MAC learning process is enabled for this SDP
                     Bind. The value is ignored if MAC learning is disabled on
                     service level."
    DEFVAL          { enabled }
    ::= { sdpBindTlsEntry 22 }

sdpBindTlsMacAgeing OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsMacAgeing specifies
                     whether the MAC aging process  is  enabled  for  this
                     SDP Bind. the value is ignored if MAC aging is disabled
                     on service level."
    DEFVAL          { enabled }
    ::= { sdpBindTlsEntry 23 }

sdpBindTlsStpOperEdge OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpOperEdge indicates
                     the operational value of the Edge Port parameter.
                     The object is initialized to the value of
                     sdpBindTlsStpRapidStart and is set FALSE on reception of a
                     BPDU."
    REFERENCE
                    "IEEE 802.1t clause 14.8.2, 18.3.4"
    ::= { sdpBindTlsEntry 24 }

sdpBindTlsStpAdminPointToPoint OBJECT-TYPE
    SYNTAX          INTEGER {
                        forceTrue  (0),
                        forceFalse (1)
                    }
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object xx sdpBindTlsStpAdminPointToPoint
                     specifies the administrative   point-to-point   status  of
                     the  LAN segment  attached to this sdp.
                     A value of 'forceTrue' indicates  that  this port should
                     always be treated as if it is connected to a
                     point-to-point link.
                     A value of 'forceFalse' indicates that this port should
                     be treated as having a shared media connection."
    REFERENCE
                    "IEEE 802.1w clause 6.4.3, 6.5, 14.8.2"
    DEFVAL          { forceTrue }
    ::= { sdpBindTlsEntry 25 }

sdpBindTlsStpPortRole OBJECT-TYPE
    SYNTAX          StpPortRole
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpPortRole indicates
                     the current role of the sdp as defined by the Rapid
                     Spanning Tree Protocol."
    ::= { sdpBindTlsEntry 26 }

sdpBindTlsStpAutoEdge OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpAutoEdge specifies
                     whether this SDP is enabled for auto-edge detection as
                     defined by Rapid Spanning Tree Protocol."
    DEFVAL          { enabled }
    ::= { sdpBindTlsEntry 27 }

sdpBindTlsStpOperProtocol OBJECT-TYPE
    SYNTAX          StpProtocol
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpOperProtocol
                     indicates whether stp, rstp or mstp is running on this
                     spoke sdp. If the protocol is not enabled on this
                     spoke-sdp the value notApplicable is returned."
    ::= { sdpBindTlsEntry 28 }

sdpBindTlsStpInRstBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpInRstBpdus indicates
                     the number of Rapid Spanning Tree (Rst) BPDUs received on
                     this SDP."
    ::= { sdpBindTlsEntry 29 }

sdpBindTlsStpOutRstBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpOutRstBpdus indicates
                     the number of Rapid Spanning Tree (Rstp) BPDUs sent out on
                     this SDP."
    ::= { sdpBindTlsEntry 30 }

sdpBindTlsLimitMacMove OBJECT-TYPE
    SYNTAX          TlsLimitMacMove
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "When sdpBindTlsLimitMacMove value is set to  blockable
                     (1) the agent will monitor the MAC relearn rate on this
                     SDP  Bind,  and it will block it when the re-learn rate
                     specified by svcTlsMacMoveMaxRate is exceeded. When the
                     value  is  'nonBlockable' this SDP binding will not be
                     blocked,  and  another  blockable  SDP  binding will be
                     blocked instead."
    DEFVAL          { blockable }
    ::= { sdpBindTlsEntry 31 }

sdpBindTlsDiscardUnknownSource OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "With the object sdpBindTlsMacAddressLimit a limit  can
                     be configured for the max number of MAC addresses that
                     will  be  learned  on  this  SDP  Bind (only for spoke
                     SDPs).  When  this  limit  is  reached,  packets  with
                     unknown  source  MAC address are forwarded by default.
                     By  setting sdpBindTlsDiscardUnknownSource to enabled,
                     packets  with  unknown  source  MAC will be dropped in
                     stead."
    DEFVAL          { disabled }
    ::= { sdpBindTlsEntry 32 }

sdpBindTlsMvplsPruneState OBJECT-TYPE
    SYNTAX          MvplsPruneState
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsMvplsPruneState
                     indicates the mVPLS prune state of the spoke SDP. The
                     object will be set to notApplicable if the spoke SDP is
                     not managed by a mVPLS. If the SDP is managed the state
                     reflects whether or not it is pruned by the STP instance
                     running in the mVPLS instance."
    ::= { sdpBindTlsEntry 33 }

sdpBindTlsMvplsMgmtService OBJECT-TYPE
    SYNTAX          TmnxServId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsMvplsMgmtService
                     indicates the service Id of the service where the STP
                     instance is running that is managing this spoke SDP. This
                     object is only valid if sdpBindTlsMvplsPruneState is
                     different from notApplicable."
    ::= { sdpBindTlsEntry 34 }

sdpBindTlsMvplsMgmtSdpBndId OBJECT-TYPE
    SYNTAX          SdpBindId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsMvplsMgmtSdpBndId
                     indicates the SDP bind id in the mVPLS instance specified
                     in sdpBindTlsMvplsMgmtService that is controlling this
                     SDP. This object is only valid if
                     sdpBindTlsMvplsPruneState is different from
                     notApplicable."
    ::= { sdpBindTlsEntry 35 }

sdpBindTlsStpException OBJECT-TYPE
    SYNTAX          StpExceptionCondition
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpException indicates
                     whether an STP exception condition is present on this
                     Spoke Sdp.
                     - none : no exception condition found.
                     - oneWayCommuniation : The neighbor RSTP peer on this link
                                            is not able to detect our presence.
                     - downstreamLoopDetected :A loop is detected on this link."
    ::= { sdpBindTlsEntry 36 }

sdpBindTlsL2ptTermination OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsL2ptTermination
                     specifies whether received L2 Protocol Tunnel pdu's are
                     terminated on this port or sdp"
    DEFVAL          { disabled }
    ::= { sdpBindTlsEntry 37 }

sdpBindTlsBpduTranslation OBJECT-TYPE
    SYNTAX          SdpBindTlsBpduTranslation
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsBpduTranslation
                     specifies whether received L2 Protocol Tunnel pdu's are
                     translated before being sent out on this port or sap"
    DEFVAL          { disabled }
    ::= { sdpBindTlsEntry 38 }

sdpBindTlsStpRootGuard OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpRootGuard specifies
                     whether this port is allowed to become STP root port.
                     It corresponds to the parameter 'restrictedRole' in 802.1Q.
                     If set, it can cause lack of spanning tree connectivity."
    DEFVAL { false }
    ::= { sdpBindTlsEntry 39 }

sdpBindTlsStpInMstBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpInMstBpdus indicates
                     the number of Multiple Spanning Tree (Mst) BPDUs received
                     on this SDP."
    ::= { sdpBindTlsEntry 40 }

sdpBindTlsStpOutMstBpdus OBJECT-TYPE
    SYNTAX          Gauge32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpOutMstBpdus indicates
                     the number of Multiple Spanning Tree (Mst) BPDUs sent out
                     on this SDP."
    ::= { sdpBindTlsEntry 41 }

sdpBindTlsStpRxdDesigBridge OBJECT-TYPE
    SYNTAX          BridgeId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsStpRxdDesigBridge
                     indicates  the designated Bridge Identifier in the last
                     BPDU which was received on this SDP."
    ::= { sdpBindTlsEntry 42 }

sdpBindTlsMacMoveNextUpTime OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "seconds"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsMacMoveNextUpTime 
                     counts down the time in seconds until a SDP bind that 
                     has been brought down due to exceeding the TLS 
                     svcTlsMacMoveMaxRate, sdpBindOperFlags 
                     'relearnLimitExceeded', is automatically brought up again. 
                     When this value is 0xffff, the SDP bind will never be 
                     automatically brought up. The value is zero when 
                     sdpBindOperStatus is 'up'."
    ::= { sdpBindTlsEntry 43 }

sdpBindTlsMacMoveRateExcdLeft   OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sapTlsMacMoveRateExcdLeft
                     counts down the number of times this SDP bind can exceed 
                     the TLS svcTlsMacMoveMaxRate and still be automatically
                     brought up."
    ::= { sdpBindTlsEntry 44 }

sdpBindTlsLimitMacMoveLevel   OBJECT-TYPE
    SYNTAX          TlsLimitMacMoveLevel
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsLimitMacMoveLevel
                     specifies the hierarchy in which spoke-SDPs are
                     blocked when a MAC-move limit is exceeded. When a MAC is
                     moving among multiple SAPs or spoke-SDPs, the SAP bind
                     or spoke-SDP bind with the lower level is blocked first.
                     (tertiary is the lowest)"
    DEFVAL { tertiary }
    ::= { sdpBindTlsEntry 45 }

sdpBindTlsBpduTransOper OBJECT-TYPE
    SYNTAX          INTEGER {
                        undefined (1),
                        disabled  (2),
                        pvst      (3),
                        stp       (4)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsBpduTransOper indicates
                     the operational BPDU encapsulation used for BPDU 
                     translated frames."
    ::= { sdpBindTlsEntry 46 }

sdpBindTlsL2ptProtocols OBJECT-TYPE
    SYNTAX          L2ptProtocols
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindTlsL2ptTermination
                     specifies which L2 Protocol Tunnel pdu's are
                     terminated on this port or sdp"
    DEFVAL          { { stp }  }
    ::= { sdpBindTlsEntry 47 }

sdpBindTlsIgnoreStandbySig     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION     "The value of sdpBindTlsIgnoreStandbySig specifies whether
                     the local internal tasks will take into account the 
                     'pseudo-wire forwarding standby' bit received from the LDP
                     peer which is normally ignored. 

                     When set to 'true', this bit is not considered in the
                     internal tasks.

                     A similar object svcEndPointIgnoreStandbySig is present at
                     the endpoint level.  If this spoke-SDP is part of that
                     explicit endpoint, this object will be set to the value of
                     svcEndPointIgnoreStandbySig and its value will not allowed
                     to be changed.
        
                     This spoke-SDP can be made part of an explicit-endpoint
                     only if the setting of this object is not conflicting with
                     the setting of svcEndPointIgnoreStandbySig object."
    DEFVAL      { false }
    ::= { sdpBindTlsEntry 48 } 

sdpBindTlsBlockOnMeshFail     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION     "The value of sdpBindTlsBlockOnMeshFail specifies that the
                     operational status of this spoke SDP will consider
                     operational status of associated mesh SDPs in this service.
                     
                     If there are no mesh SDPs in the service, value of this
                     object is ignored.
                     
                     When this object is set to 'true', then the operational
                     status of this spoke SDP will be 'down' until the
                     operational status of atleast one mesh SDP in this service
                     is 'up'.

                     When set to 'false', the operational status of this spoke
                     SDP does not consider the operational status of any mesh
                     SDPs in the service."
    DEFVAL      { false }
    ::= { sdpBindTlsEntry 49 } 

-- ------------------------------------------
-- TLS Mesh SDP Bind Table
-- ------------------------------------------
sdpBindMeshTlsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindMeshTlsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains TLS Mesh SDP Bind
                     information."
    ::= { tmnxSdpObjs 7 }

sdpBindMeshTlsEntry OBJECT-TYPE
    SYNTAX          SdpBindMeshTlsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "TLS specific information about an Mesh SDP Bind."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindMeshTlsTable 1 }

SdpBindMeshTlsEntry ::=
    SEQUENCE {
        sdpBindMeshTlsPortState         TStpPortState,
        sdpBindMeshTlsHoldDownTimer     INTEGER,
        sdpBindMeshTlsTransitionState   INTEGER,
        sdpBindMeshTlsNotInMstRegion    TruthValue

    }

sdpBindMeshTlsPortState OBJECT-TYPE
    SYNTAX          TStpPortState
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the actual state of the Mesh SDP. If
                     the sdp is operationally down, the port will be in the
                     'disabled' state. If the sdp is operationally up, the
                     state will be 'forwarding' unless the hold-down timer is
                     active in which case the state will be 'discarding'."
    ::= { sdpBindMeshTlsEntry 1 }

sdpBindMeshTlsHoldDownTimer OBJECT-TYPE
    SYNTAX          INTEGER {
                        not-active (1),
                        active     (2)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "When the hold-down timer is active, all traffic coming
                     from this mesh sdp will be blocked. This timer will be
                     activated for any of the following cases:
                     1. when a mesh SDP becomes operational;
                     2. when a 'disputed' BPDU is received over this mesh sdp;
                        This is typically a symptom of one way communication
                        (the peer at the other side of the mesh sdp does not
                        receive our BPDUs).
                     3. when a MSTP BPDU from outside the region is received
                        over this mesh SDP."
    ::= { sdpBindMeshTlsEntry 2 }

sdpBindMeshTlsTransitionState OBJECT-TYPE
    SYNTAX          INTEGER {
                        not-applicable        (1),
                        waiting-for-agreement (2),
                        agreement-received    (3)
                    }
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates whether we already received an
                     agreement from the peer connected via this mesh sdp. RSTP
                     expects an agreement from every peer after sending a
                     proposal over the VCP when it wants to transition the latter
                     to the forwarding state. This object is only relevant when
                     the role of the VCP is 'designated'. Not receiving an
                     agreement is typically caused by an improperly configured
                     sdp or by a non rstp enabled peer."
    ::= { sdpBindMeshTlsEntry 3 }

sdpBindMeshTlsNotInMstRegion OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object sdpBindMeshTlsNotInMstRegion indicates whether
                     we received a BPDU from another MST-region on this mesh
                     SDP.

                     If set to 'true' then the object sdpBindMeshTlsHoldDownTimer
                     will have the value 'active'.

                     It is up to the operator to make sure bridges connected
                     via mesh SDPs are in the same MST-region. If not the mesh
                     will NOT become operational."
    ::= { sdpBindMeshTlsEntry 4 }

-- ------------------------------------
-- APIPE SDP Bind Table
-- ------------------------------------
sdpBindApipeTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindApipeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The sdpBindApipeTable has an entry for each apipe sdpBind
                     configured on this system."
    ::= { tmnxSdpObjs 8 }

sdpBindApipeEntry OBJECT-TYPE
    SYNTAX          SdpBindApipeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry represents a particular sdpBind related to a
                     particular Apipe service entry. Entries are created/deleted
                     by the user."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindApipeTable 1 }

SdpBindApipeEntry ::=
    SEQUENCE {
        sdpBindApipeAdminConcatCellCount        Integer32,
        sdpBindApipeSigConcatCellCount          Integer32,
        sdpBindApipeOperConcatCellCount         Integer32,
        sdpBindApipeConcatMaxDelay              Integer32,
        sdpBindApipeConcatCellClp               TruthValue,
        sdpBindApipeConcatCellAal5Fr            TruthValue
    }

sdpBindApipeAdminConcatCellCount OBJECT-TYPE
    SYNTAX          Integer32 (1..128)
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindApipeAdminConcatCellCount specifies
                     the maximum number of ATM cells to accumulate
                     into an MPLS packet.  The remote peer will also signal the
                     maximum number of concatenated cells it is willing to
                     accept in an MPLS packet.  When the lesser of (the
                     configured value and the signaled value) number of cells
                     is reached, the MPLS packet is queued for transmission
                     onto the pseudowire."
    DEFVAL { 1 }
    ::= { sdpBindApipeEntry 1 }

sdpBindApipeSigConcatCellCount OBJECT-TYPE
    SYNTAX          Integer32 (0..128)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindApipeSigConcatCellCount indicates the
                     maximum number of concatenated ATM cells the remote peer
                     is willing to accept.  If there is no remote peer, or if
                     the label mapping has not been received, this object will
                     be zero (0)."
    ::= { sdpBindApipeEntry 2 }

sdpBindApipeOperConcatCellCount OBJECT-TYPE
    SYNTAX          Integer32 (1..128)
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindApipeOperConcatCellCount indicates the
                     maximum number of concatenated ATM cells that will be sent
                     on this SDP binding."
    ::= { sdpBindApipeEntry 3 }

sdpBindApipeConcatMaxDelay OBJECT-TYPE
    SYNTAX          Integer32 (1..400)
    UNITS           "hundreds of microseconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindApipeConcatMaxDelay object specifies
                     the maximum amount of time to wait while
                     performing ATM cell concatenation into an MPLS packet
                     before transmitting the MPLS packet.  This places an upper
                     bound on the amount of delay introduced by the
                     concatenation process.
                     When this amount of time is reached from when the first
                     ATM cell for this MPLS packet was received, the MPLS
                     packet is queued for transmission onto the pseudowire."
    DEFVAL { 400 }
    ::= { sdpBindApipeEntry 4 }

sdpBindApipeConcatCellClp OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindApipeConcatCellClp specifies whether
                     a CLP change should be used as an indication to complete
                     the cell concatenation operation. When the value is 'true',
                     CLP is used to indicate that cell concatenation should
                     be completed."
    DEFVAL { false }
    ::= { sdpBindApipeEntry 5 }

sdpBindApipeConcatCellAal5Fr OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindApipeConcatCellAal5Fr specifies
                     whether the AAL5 EOP (end of packet) should be used as an
                     indication to complete the cell concatenation operation.
                     When the value is 'true', EOP is used to indicate that
                     cell concatenation should be completed."
    DEFVAL { false }
    ::= { sdpBindApipeEntry 6 }

-- ------------------------------------
-- SDP Bind DHCP Information Table
-- ------------------------------------
sdpBindDhcpInfoTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindDhcpInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains DHCP information related to a
                     SDP Bind.

                     A row will exist in this table for each spoke or
                     mesh SDP in a  Tls Service. Rows are created and deleted
                     automatically by the system."
    ::= { tmnxSdpObjs 9 }

sdpBindDhcpInfoEntry OBJECT-TYPE
    SYNTAX          SdpBindDhcpInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "DHCP specific information about an SDP Bind."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindDhcpInfoTable 1 }

SdpBindDhcpInfoEntry ::=
    SEQUENCE {
        sdpBindDhcpDescription  ServObjDesc,
        sdpBindDhcpSnoop        TmnxEnabledDisabled
    }

sdpBindDhcpDescription OBJECT-TYPE
    SYNTAX          ServObjDesc
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpDescription specifies
                     a user provided description for DHCP on this Sdp Bind."
    DEFVAL          { ''H }
    ::= { sdpBindDhcpInfoEntry 1 }

sdpBindDhcpSnoop OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpSnoop specifies
                     whether or not DHCP snooping is enabled on the Sdp Bind."
    DEFVAL          { disabled }
    ::= { sdpBindDhcpInfoEntry 2 }

-- ------------------------------------
-- SDP Bind DHCP Stats Table
-- ------------------------------------
sdpBindDhcpStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindDhcpStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "sdpBindDhcpStatsTable contains DHCP statistics related
                     to  a TLS SDP Bind. A row will exist in this table for
                     each  spoke  or  mesh  SDP  in a Tls Service. Rows are
                     created and deleted automatically by the system."
    ::= { tmnxSdpObjs 10 }

sdpBindDhcpStatsEntry OBJECT-TYPE
    SYNTAX          SdpBindDhcpStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "DHCP statistics for a TLS spoke SDP or mesh SDP."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindDhcpStatsTable 1 }

SdpBindDhcpStatsEntry ::=
    SEQUENCE {
        sdpBindDhcpStatsClntSnoopdPckts    Counter32,
        sdpBindDhcpStatsSrvrSnoopdPckts    Counter32,
        sdpBindDhcpStatsClntForwdPckts     Counter32,
        sdpBindDhcpStatsSrvrForwdPckts     Counter32,
        sdpBindDhcpStatsClntDropdPckts     Counter32,
        sdpBindDhcpStatsSrvrDropdPckts     Counter32,
        sdpBindDhcpStatsClntProxRadPckts   Counter32,
        sdpBindDhcpStatsClntProxLSPckts    Counter32,
        sdpBindDhcpStatsGenReleasePckts    Counter32,
        sdpBindDhcpStatsGenForceRenPckts   Counter32
    }

sdpBindDhcpStatsClntSnoopdPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsClntSnoopdPckts
                     indicates the number of  DHCP client packets that have
                     been snooped on this SDP bind."
    ::= { sdpBindDhcpStatsEntry 1 }

sdpBindDhcpStatsSrvrSnoopdPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsSrvrSnoopdPckts
                     indicates the number of  DHCP server packets that have
                     been snooped on this SDP bind."
    ::= { sdpBindDhcpStatsEntry 2 }

sdpBindDhcpStatsClntForwdPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsClntForwdPckts
                     indicates the number of  DHCP  client  packets  that have
                     been forwarded on this SDP bind."
    ::= { sdpBindDhcpStatsEntry 3 }

sdpBindDhcpStatsSrvrForwdPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsSrvrForwdPckts
                     indicates the number of  DHCP  server  packets  that have
                     been forwarded on this SDP bind."
    ::= { sdpBindDhcpStatsEntry 4 }

sdpBindDhcpStatsClntDropdPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsClntDropdPckts
                     indicates the number of  DHCP client packets that have
                     been dropped on this SDP bind."
    ::= { sdpBindDhcpStatsEntry 5 }

sdpBindDhcpStatsSrvrDropdPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsSrvrDropdPckts
                     indicates the number of  DHCP server packets that have
                     been dropped on this SDP bind."
    ::= { sdpBindDhcpStatsEntry 6 }

sdpBindDhcpStatsClntProxRadPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsClntProxRadPckts
                     indicates the number of DHCP client packets that have
                     been proxied on this SDP bind based on data received from
                     a RADIUS server."
    ::= { sdpBindDhcpStatsEntry 7 }

sdpBindDhcpStatsClntProxLSPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsClntProxLSPckts
                     indicates the number of DHCP client packets that have
                     been proxied on this SDP bind based on a lease state. The
                     lease itself can have been obtained from a DHCP or RADIUS
                     server. This is the so called lease split functionality."
    ::= { sdpBindDhcpStatsEntry 8 }

sdpBindDhcpStatsGenReleasePckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsGenReleasePckts
                     indicates the number of DHCP RELEASE messages spoofed on
                     this SDP bind to the DHCP server."
    ::= { sdpBindDhcpStatsEntry 9 }

sdpBindDhcpStatsGenForceRenPckts OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindDhcpStatsGenForceRenPckts
                     indicates the number of DHCP FORCERENEW messages spoofed
                     on this SDP bind to the DHCP clients."
    ::= { sdpBindDhcpStatsEntry 10 }

-- ------------------------------------
-- IPIPE SDP Bind Table
-- ------------------------------------
sdpBindIpipeTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindIpipeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The sdpBindIpipeTable has an entry for each IPIPE sdpBind
                     configured on this system."
    ::= { tmnxSdpObjs 11 }

sdpBindIpipeEntry OBJECT-TYPE
    SYNTAX          SdpBindIpipeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry represents a particular sdpBind related to a
                     particular IPIPE service entry. Entries are created/deleted
                     by the user."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindIpipeTable 1 }

SdpBindIpipeEntry ::=
    SEQUENCE {
        sdpBindIpipeCeInetAddressType               InetAddressType,
        sdpBindIpipeCeInetAddress                   InetAddress
    }

sdpBindIpipeCeInetAddressType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of the object sdpBindIpipeCeInetAddressType
                     specifies the addresstype of the IP address of the CE
                     device reachable throught this IPIPE SDP binding."
    ::= { sdpBindIpipeEntry 1 }

sdpBindIpipeCeInetAddress OBJECT-TYPE
    SYNTAX          InetAddress (SIZE(0|4))
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The type of this address is determined by the value of
                     the sdpBindIpipeCeInetAddressType object.

                     This object specifies the IPv4 address of the
                     CE device reachable through this SDP binding."
    ::= { sdpBindIpipeEntry 2 }

-- --------------------------------------------
-- SDP Egress forwarding-class mapping table
-- --------------------------------------------
sdpFCMappingTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpFCMappingEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The sdpFCMappingTable has an entry for each FC mapping
                     on an SDP configured on this system."
    ::= { tmnxSdpObjs 12 }

sdpFCMappingEntry OBJECT-TYPE
    SYNTAX          SdpFCMappingEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry represents a particular FC to LSP ID
                     mapping on an SDP. Entries are created/deleted by 
                     the user."
    INDEX           { sdpId, sdpFCMappingFCName }
    ::= { sdpFCMappingTable 1 }

SdpFCMappingEntry ::=
    SEQUENCE {
        sdpFCMappingFCName               TNamedItem,
        sdpFCMappingRowStatus            RowStatus,
        sdpFCMappingLspId                TmnxVRtrMplsLspID
    }

sdpFCMappingFCName OBJECT-TYPE
    SYNTAX          TNamedItem
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The value of sdpFCMappingFCName specifies the forwarding
                     class for which this mapping is defined, in the SDP
                     indexed by 'sdpId'."
    ::= { sdpFCMappingEntry 1 }

sdpFCMappingRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpFCMappingRowStatus is used for the 
                     creation and deletion of forwarding class to LSP
                     mappings."
    ::= { sdpFCMappingEntry 2 }


sdpFCMappingLspId OBJECT-TYPE
    SYNTAX          TmnxVRtrMplsLspID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpFCMappingLspId specifies the LSP ID that
                     traffic corresponding to the class specified in 
                     sdpFCMappingFCName will be forwarded on. This object MUST
                     be specified at row creation time."
    ::= { sdpFCMappingEntry 3 }

-- ------------------------------------
-- CPIPE SDP Bind Table
-- ------------------------------------
sdpBindCpipeTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindCpipeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The sdpBindCpipeTable has an entry for each cpipe sdpBind
                     configured on this system."
    ::= { tmnxSdpObjs 15 }

sdpBindCpipeEntry OBJECT-TYPE
    SYNTAX          SdpBindCpipeEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry represents a particular sdpBind related to a
                     particular Cpipe service entry. Entries are created/deleted
                     by the user."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindCpipeTable 1 }

SdpBindCpipeEntry ::=
    SEQUENCE {
        sdpBindCpipeLocalPayloadSize        Unsigned32,
        sdpBindCpipePeerPayloadSize         Unsigned32,
        sdpBindCpipeLocalBitrate            Unsigned32,
        sdpBindCpipePeerBitrate             Unsigned32,
        sdpBindCpipeLocalSigPkts            TdmOptionsSigPkts,
        sdpBindCpipePeerSigPkts             TdmOptionsSigPkts,
        sdpBindCpipeLocalCasTrunkFraming    TdmOptionsCasTrunkFraming,
        sdpBindCpipePeerCasTrunkFraming     TdmOptionsCasTrunkFraming,
        sdpBindCpipeLocalUseRtpHeader       TruthValue,
        sdpBindCpipePeerUseRtpHeader        TruthValue,
        sdpBindCpipeLocalDifferential       TruthValue,
        sdpBindCpipePeerDifferential        TruthValue,
        sdpBindCpipeLocalTimestampFreq      Unsigned32,
        sdpBindCpipePeerTimestampFreq       Unsigned32
    }

sdpBindCpipeLocalPayloadSize OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the local payload size (in bytes)."
    ::= { sdpBindCpipeEntry 1 }

sdpBindCpipePeerPayloadSize OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "bytes"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the remote payload size (in bytes).
                     If there is no remote peer, or if the label mapping has
                     not been received, or if this value has not been received
                     from the remote peer then this object will be zero (0)."
    ::= { sdpBindCpipeEntry 2 }

sdpBindCpipeLocalBitrate OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "64 Kbits/s"    
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the local bit-rate in multiples of
                     64 Kbit/s."
    ::= { sdpBindCpipeEntry 3 }

sdpBindCpipePeerBitrate OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "64 Kbits/s"    
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the remote bit-rate in multiples of
                     64 Kbit/s.
                     If there is no remote peer, or if the label mapping has
                     not been received, or if this value has not been received
                     from the remote peer then this object will be zero (0)."
    ::= { sdpBindCpipeEntry 4 }

sdpBindCpipeLocalSigPkts OBJECT-TYPE
    SYNTAX          TdmOptionsSigPkts
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the local CE application signalling
                     packets mode."
    ::= { sdpBindCpipeEntry 5 }

sdpBindCpipePeerSigPkts OBJECT-TYPE
    SYNTAX          TdmOptionsSigPkts
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the remote CE application signalling
                     packets mode.
                     If there is no remote peer, or if the label mapping has
                     not been received, or if the remote peer does not support
                     signalling packets then this object will be zero (0)."
    ::= { sdpBindCpipeEntry 6 }

sdpBindCpipeLocalCasTrunkFraming OBJECT-TYPE
    SYNTAX          TdmOptionsCasTrunkFraming
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the local CAS trunk framing mode."
    ::= { sdpBindCpipeEntry 7 }

sdpBindCpipePeerCasTrunkFraming OBJECT-TYPE
    SYNTAX          TdmOptionsCasTrunkFraming
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the remote CAS trunk framing mode.
                     If there is no remote peer, or if the label mapping has
                     not been received, or if the remote peer does not support
                     CAS trunk framing then this object will be zero (0)."
    ::= { sdpBindCpipeEntry 8 }

sdpBindCpipeLocalUseRtpHeader OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates whether a RTP header is used
                     when packets are transmitted to the remote peer, and
                     the local peer expects a RTP header when packets are
                     received from the remote peer." 
    ::= { sdpBindCpipeEntry 9 }

sdpBindCpipePeerUseRtpHeader OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates whether a RTP header is used
                     when packets are transmitted by the remote peer, and
                     the remote peer expects a RTP header when packets are
                     received from the local peer. 
                     If there is no remote peer, or if the label mapping has
                     not been received, or if the remote peer does not support
                     RTP headers then this object will be 'false'."
    ::= { sdpBindCpipeEntry 10 }
    
sdpBindCpipeLocalDifferential OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates whether differential timestamp
                     mode is used in the RTP header when packets are
                     transmitted to the remote peer, and the local peer expects
                     differential timestamps in the RTP header when packets are
                     received from the remote peer."
    ::= { sdpBindCpipeEntry 11 }

sdpBindCpipePeerDifferential OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates whether differential timestamp mode
                     is used in the RTP header when packets are transmitted by
                     the remote peer, and the remote peer expects differential
                     timestamps in the RTP header when packets are received
                     from the local peer.
                     If there is no remote peer, or if the label mapping has
                     not been received, or if the remote peer does not support
                     differential timestamp mode then this object will be
                     'false'."
    ::= { sdpBindCpipeEntry 12 }

sdpBindCpipeLocalTimestampFreq OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "8 KHz"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the timestamp frequency used
                     in the RTP header when packets are transmitted to the
                     remote peer, and the local peer expects same timestamp
                     frequency in the RTP header when packets are received
                     from the remote peer.
                     
                     This value is in multiples of 8 KHz." 
    ::= { sdpBindCpipeEntry 13 }

sdpBindCpipePeerTimestampFreq OBJECT-TYPE
    SYNTAX          Unsigned32
    UNITS           "8 KHz"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "This object indicates the timestamp frequency used in
                     the RTP header when packets are transmitted by the remote
                     peer, and the remote peer expects the same timestamp
                     frequency in the RTP header when packets are received
                     from the local peer.
                     If there is no remote peer, or if the label mapping has
                     not been received, or if the remote peer does not support
                     support RTP headers then this object will be zero (0).

                     This value is in multiples of 8 KHz."
    ::= { sdpBindCpipeEntry 14 }

-- --------------------------------------------
-- SDP Bind TLS Egress MFIB Allowed MDA Destinations Table
-- --------------------------------------------
sdpBindTlsMfibAllowedMdaTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindTlsMfibAllowedMdaEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The sdpBindTlsMfibAllowedMdaTable has an entry for each
                     MFIB allowed MDA destination for an SDP Binding configured
                     in the system."
    ::= { tmnxSdpObjs 13 }

sdpBindTlsMfibAllowedMdaEntry OBJECT-TYPE
    SYNTAX          SdpBindTlsMfibAllowedMdaEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry represents an MFIB allowed MDA destination
                     for an SDP Binding configured in the system. Entries can
                     be created and deleted via SNMP SET operations on the
                     object sdpBindTlsMfibMdaRowStatus."
    INDEX           { svcId,
                      sdpBindId,
                      tmnxChassisIndex,
                      tmnxCardSlotNum,
                      tmnxMDASlotNum }
    ::= { sdpBindTlsMfibAllowedMdaTable 1 }

SdpBindTlsMfibAllowedMdaEntry ::=
    SEQUENCE {
        sdpBindTlsMfibMdaRowStatus RowStatus
    }

sdpBindTlsMfibMdaRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMfibMdaRowStatus controls the
                     creation and deletion of rows in this table."
    ::= { sdpBindTlsMfibAllowedMdaEntry 1 }

-- ------------------------------------------
-- SDP Bind TLS L2PT Statistics Table
-- ------------------------------------------
sdpBindTlsL2ptStatsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindTlsL2ptStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains TLS spoke SDP Bind 
                     Layer 2 Protocol Tunneling Statistics.
                     This table complements the sdpBindTlsTable. Rows in this
                     table  are  created  and  deleted automatically by the
                     system."
    ::= { tmnxSdpObjs 16 }

sdpBindTlsL2ptStatsEntry OBJECT-TYPE
    SYNTAX          SdpBindTlsL2ptStatsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "TLS specific information about an SDP Bind."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindTlsL2ptStatsTable 1 }

SdpBindTlsL2ptStatsEntry ::=
    SEQUENCE {
        sdpBindTlsL2ptStatsLastClearedTime               TimeStamp,
        sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusRx     Counter32,
        sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusTx     Counter32,
        sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusRx        Counter32,
        sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusTx        Counter32,
        sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusRx        Counter32,
        sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusTx        Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusRx    Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusTx    Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusRx       Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusTx       Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusRx       Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusTx       Counter32,
        sdpBindTlsL2ptStatsStpConfigBpdusRx              Counter32,
        sdpBindTlsL2ptStatsStpConfigBpdusTx              Counter32,
        sdpBindTlsL2ptStatsStpRstBpdusRx                 Counter32,
        sdpBindTlsL2ptStatsStpRstBpdusTx                 Counter32,
        sdpBindTlsL2ptStatsStpTcnBpdusRx                 Counter32,
        sdpBindTlsL2ptStatsStpTcnBpdusTx                 Counter32,
        sdpBindTlsL2ptStatsPvstConfigBpdusRx             Counter32,
        sdpBindTlsL2ptStatsPvstConfigBpdusTx             Counter32,
        sdpBindTlsL2ptStatsPvstRstBpdusRx                Counter32,
        sdpBindTlsL2ptStatsPvstRstBpdusTx                Counter32,
        sdpBindTlsL2ptStatsPvstTcnBpdusRx                Counter32,
        sdpBindTlsL2ptStatsPvstTcnBpdusTx                Counter32,
        sdpBindTlsL2ptStatsOtherBpdusRx                  Counter32,
        sdpBindTlsL2ptStatsOtherBpdusTx                  Counter32,
        sdpBindTlsL2ptStatsOtherL2ptBpdusRx              Counter32,
        sdpBindTlsL2ptStatsOtherL2ptBpdusTx              Counter32,
        sdpBindTlsL2ptStatsOtherInvalidBpdusRx           Counter32,
        sdpBindTlsL2ptStatsOtherInvalidBpdusTx           Counter32,
        sdpBindTlsL2ptStatsL2ptEncapCdpBpdusRx           Counter32,
        sdpBindTlsL2ptStatsL2ptEncapCdpBpdusTx           Counter32,
        sdpBindTlsL2ptStatsL2ptEncapVtpBpdusRx           Counter32,
        sdpBindTlsL2ptStatsL2ptEncapVtpBpdusTx           Counter32,
        sdpBindTlsL2ptStatsL2ptEncapDtpBpdusRx           Counter32,
        sdpBindTlsL2ptStatsL2ptEncapDtpBpdusTx           Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPagpBpdusRx          Counter32,
        sdpBindTlsL2ptStatsL2ptEncapPagpBpdusTx          Counter32,
        sdpBindTlsL2ptStatsL2ptEncapUdldBpdusRx          Counter32,
        sdpBindTlsL2ptStatsL2ptEncapUdldBpdusTx          Counter32,
        sdpBindTlsL2ptStatsCdpBpdusRx                    Counter32,
        sdpBindTlsL2ptStatsCdpBpdusTx                    Counter32,
        sdpBindTlsL2ptStatsVtpBpdusRx                    Counter32,
        sdpBindTlsL2ptStatsVtpBpdusTx                    Counter32,
        sdpBindTlsL2ptStatsDtpBpdusRx                    Counter32,
        sdpBindTlsL2ptStatsDtpBpdusTx                    Counter32,
        sdpBindTlsL2ptStatsPagpBpdusRx                   Counter32,
        sdpBindTlsL2ptStatsPagpBpdusTx                   Counter32,
        sdpBindTlsL2ptStatsUdldBpdusRx                   Counter32,
        sdpBindTlsL2ptStatsUdldBpdusTx                   Counter32
    }

sdpBindTlsL2ptStatsLastClearedTime OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsLastClearedTime indicates 
                     the last time that these stats were cleared. The value
                     zero indicates that they have not been cleared yet."
    ::= { sdpBindTlsL2ptStatsEntry 1 }
                                                                                                                                                  
sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusRx indicates the
                     number of L2PT encapsulated STP config bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 2 }

sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusTx indicates the
                     number of L2PT encapsulated STP config bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 3 }

sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusRx indicates the
                     number of L2PT encapsulated STP rst bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 4 }

sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusTx indicates the
                     number of L2PT encapsulated STP rst bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 5 }

sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusRx indicates the
                     number of L2PT encapsulated STP tcn bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 6 }

sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusTx indicates the
                     number of L2PT encapsulated STP tcn bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 7 }

sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusRx indicates the
                     number of L2PT encapsulated PVST config bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 8 }

sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusTx indicates the
                     number of L2PT encapsulated PVST config bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 9 }

sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusRx indicates the
                     number of L2PT encapsulated PVST rst bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 10 }

sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusTx indicates the
                     number of L2PT encapsulated PVST rst bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 11 }

sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusRx indicates the
                     number of L2PT encapsulated PVST tcn bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 12 }

sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusTx indicates the
                     number of L2PT encapsulated PVST tcn bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 13 }

sdpBindTlsL2ptStatsStpConfigBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsStpConfigBpdusRx indicates the
                     number of STP config bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 14 }

sdpBindTlsL2ptStatsStpConfigBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsStpConfigBpdusTx indicates the
                     number of STP config bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 15 }

sdpBindTlsL2ptStatsStpRstBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsStpRstBpdusRx indicates the
                     number of STP rst bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 16 }

sdpBindTlsL2ptStatsStpRstBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsStpRstBpdusTx indicates the
                     number of STP rst bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 17 }

sdpBindTlsL2ptStatsStpTcnBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsStpTcnBpdusRx indicates the
                     number of STP tcn bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 18 }

sdpBindTlsL2ptStatsStpTcnBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsStpTcnBpdusTx indicates the
                     number of STP tcn bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 19 }

sdpBindTlsL2ptStatsPvstConfigBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPvstConfigBpdusRx indicates the
                     number of PVST config bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 20 }

sdpBindTlsL2ptStatsPvstConfigBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPvstConfigBpdusTx indicates the
                     number of PVST config bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 21 }

sdpBindTlsL2ptStatsPvstRstBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPvstRstBpdusRx indicates the
                     number of PVST rst bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 22 }

sdpBindTlsL2ptStatsPvstRstBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPvstRstBpdusTx indicates the
                     number of PVST rst bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 23 }

sdpBindTlsL2ptStatsPvstTcnBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPvstTcnBpdusRx indicates the
                     number of PVST tcn bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 24 }

sdpBindTlsL2ptStatsPvstTcnBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPvstTcnBpdusTx indicates the
                     number of PVST tcn bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 25 }

sdpBindTlsL2ptStatsOtherBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsOtherBpdusRx indicates the
                     number of other bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 26 }

sdpBindTlsL2ptStatsOtherBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsOtherBpdusTx indicates the
                     number of other bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 27 }

sdpBindTlsL2ptStatsOtherL2ptBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsOtherL2ptBpdusRx indicates the
                     number of other L2PT bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 28 }

sdpBindTlsL2ptStatsOtherL2ptBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsOtherL2ptBpdusTx indicates the
                     number of other L2PT bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 29 }

sdpBindTlsL2ptStatsOtherInvalidBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsOtherInvalidBpdusRx indicates the
                     number of other invalid bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 30 }

sdpBindTlsL2ptStatsOtherInvalidBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsOtherInvalidBpdusTx indicates the
                     number of other invalid bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 31 }

sdpBindTlsL2ptStatsL2ptEncapCdpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapCdpBpdusRx indicates the
                     number of L2PT encapsulated CDP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 32 }

sdpBindTlsL2ptStatsL2ptEncapCdpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapCdpBpdusTx indicates the
                     number of L2PT encapsulated CDP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 33 }

sdpBindTlsL2ptStatsL2ptEncapVtpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapVtpBpdusRx indicates the
                     number of L2PT encapsulated VTP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 34 }

sdpBindTlsL2ptStatsL2ptEncapVtpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapVtpBpdusTx indicates the
                     number of L2PT encapsulated VTP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 35 }

sdpBindTlsL2ptStatsL2ptEncapDtpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapDtpBpdusRx indicates the
                     number of L2PT encapsulated DTP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 36 }

sdpBindTlsL2ptStatsL2ptEncapDtpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapDtpBpdusTx indicates the
                     number of L2PT encapsulated DTP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 37 }

sdpBindTlsL2ptStatsL2ptEncapPagpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPagpBpdusRx indicates the
                     number of L2PT encapsulated PAGP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 38 }

sdpBindTlsL2ptStatsL2ptEncapPagpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapPagpBpdusTx indicates the
                     number of L2PT encapsulated PAGP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 39 }


sdpBindTlsL2ptStatsL2ptEncapUdldBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapUdldBpdusRx indicates the
                     number of L2PT encapsulated UDLD bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 40 }

sdpBindTlsL2ptStatsL2ptEncapUdldBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsL2ptEncapUdldBpdusTx indicates the
                     number of L2PT encapsulated UDLD bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 41 }

sdpBindTlsL2ptStatsCdpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsCdpBpdusRx indicates the
                     number of CDP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 42 }

sdpBindTlsL2ptStatsCdpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsCdpBpdusTx indicates the
                     number of CDP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 43 }

sdpBindTlsL2ptStatsVtpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsVtpBpdusRx indicates the
                     number of VTP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 44 }

sdpBindTlsL2ptStatsVtpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsVtpBpdusTx indicates the
                     number of VTP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 45 }


sdpBindTlsL2ptStatsDtpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsDtpBpdusRx indicates the
                     number of DTP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 46 }

sdpBindTlsL2ptStatsDtpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsDtpBpdusTx indicates the
                     number of DTP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 47 }

sdpBindTlsL2ptStatsPagpBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPagpBpdusRx indicates the
                     number of PAGP bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 48 }

sdpBindTlsL2ptStatsPagpBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsPagpBpdusTx indicates the
                     number of PAGP bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 49 }


sdpBindTlsL2ptStatsUdldBpdusRx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsUdldBpdusRx indicates the
                     number of UDLD bpdus received on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 50 }

sdpBindTlsL2ptStatsUdldBpdusTx OBJECT-TYPE
    SYNTAX          Counter32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsL2ptStatsUdldBpdusTx indicates the
                     number of UDLD bpdus transmitted on this spoke SDP."
    ::= { sdpBindTlsL2ptStatsEntry 51 }

-- -------------------------
-- PW Template Table
-- -------------------------
pwTemplateTableLastChanged  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of pwTemplateTableLastChanged indicates the
                     sysUpTime at the time of the last modification
                     of pwTemplateTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management subsystem,
                     then this object contains a zero value."
    ::= { tmnxSdpObjs 17 }

pwTemplateTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF PwTemplateEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains entries for pseudowire (PW) templates
                     specifying SDP auto-binding."
    ::= { tmnxSdpObjs 18 }

pwTemplateEntry OBJECT-TYPE
    SYNTAX          PwTemplateEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Information about a specific PW template."
    INDEX           { pwTemplateId }
    ::= { pwTemplateTable 1 }

PwTemplateEntry ::=
    SEQUENCE {
        pwTemplateId                           PWTemplateId,
        pwTemplateRowStatus                    RowStatus,
        pwTemplateLastChanged                  TimeStamp,
        pwTemplateUseProvisionedSdp            TruthValue,
        pwTemplateVcType                       SdpBindVcType,
        pwTemplateAccountingPolicyId           Unsigned32,
        pwTemplateCollectAcctStats             TruthValue,
        pwTemplateMacLearning                  TmnxEnabledDisabled,
        pwTemplateMacAgeing                    TmnxEnabledDisabled,
        pwTemplateDiscardUnknownSource         TmnxEnabledDisabled,
        pwTemplateLimitMacMove                 TlsLimitMacMove,
        pwTemplateMacPinning                   TmnxEnabledDisabled,
        pwTemplateVlanVcTag                    Unsigned32,
        pwTemplateMacAddressLimit              Unsigned32,
        pwTemplateShgName                      TNamedItemOrEmpty,  
        pwTemplateShgDescription               TItemDescription,
        pwTemplateShgRestProtSrcMac            TruthValue,
        pwTemplateShgRestUnprotDstMac          TruthValue,
        pwTemplateEgressMacFilterId            TFilterID,
        pwTemplateEgressIpFilterId             TFilterID,
        pwTemplateEgressIpv6FilterId           TFilterID,
        pwTemplateIngressMacFilterId           TFilterID,
        pwTemplateIngressIpFilterId            TFilterID,
        pwTemplateIngressIpv6FilterId          TFilterID,
        pwTemplateIgmpFastLeave                TmnxEnabledDisabled,
        pwTemplateIgmpImportPlcy               TNamedItemOrEmpty,
        pwTemplateIgmpLastMembIntvl            Unsigned32,
        pwTemplateIgmpMaxNbrGrps               Unsigned32,
        pwTemplateIgmpGenQueryIntvl            Unsigned32,
        pwTemplateIgmpQueryRespIntvl           Unsigned32,
        pwTemplateIgmpRobustCount              Unsigned32,
        pwTemplateIgmpSendQueries              TmnxEnabledDisabled,
        pwTemplateIgmpMcacPolicyName           TPolicyStatementNameOrEmpty,
        pwTemplateIgmpMcacUnconstBW            Integer32,
        pwTemplateIgmpMcacPrRsvMndBW           Integer32,
        pwTemplateIgmpVersion                  TmnxIgmpVersion
    }

pwTemplateId        OBJECT-TYPE
    SYNTAX          PWTemplateId
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The PW template identifier."
    ::= { pwTemplateEntry 1 }

pwTemplateRowStatus        OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateRowStatus is used for the
                     creation and deletion of PW templates."
    ::= { pwTemplateEntry 2 }

pwTemplateLastChanged        OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of pwTemplateLastChanged indicates the 
                     sysUpTime at the time of the last modification of this
                     entry.
                     
                     If no changes were made to the entry since the last 
                     re-initialization of the local network management 
                     subsystem, then this object contains a zero value."
    ::= { pwTemplateEntry 3 }

pwTemplateUseProvisionedSdp    OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateUseProvisionedSdp specifies
                     whether the to use an already provisioned SDP.
                     A value of 'true' specifies that the tunnel manager
                     will be consulted for an existing active SDP.
                     Otherwise, a value of 'false' specifies that
                     the default SDP template will be used to use for
                     instantiation of the SDP."
    DEFVAL          { false }
    ::= { pwTemplateEntry 4 }

pwTemplateVcType        OBJECT-TYPE
    SYNTAX          SdpBindVcType
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateVcType specifies the type of
                     virtual circuit (VC) associated with the SDP Bind."
    DEFVAL          { ether }
    ::= { pwTemplateEntry 5 }

pwTemplateAccountingPolicyId        OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateAccountingPolicyId specifies the
                     policy to use to collect accounting statistics on
                     the SDP Bind. The value zero indicates that the
                     agent should use the default accounting policy,
                     if one exists."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 6 }

pwTemplateCollectAcctStats        OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateCollectAcctStats specifies
                     whether the agent collects accounting statistics for
                     the SDP Bind. When the value is 'true' the agent
                     collects accounting statistics on the SDP Bind."
    DEFVAL          { false }
    ::= { pwTemplateEntry 7 }

pwTemplateMacLearning        OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateMacLearning specifies whether
                     the MAC learning process is enabled for the SDP Bind.
                     The value is ignored if MAC learning is disabled at
                     service level."
    DEFVAL          { enabled }
    ::= { pwTemplateEntry 8 }

pwTemplateMacAgeing        OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateMacAgeing specifies whether
                     the MAC aging process is enabled for the SDP Bind.
                     The value is ignored if MAC aging is disabled
                     at the service level."
    DEFVAL          { enabled }
    ::= { pwTemplateEntry 9 }

pwTemplateDiscardUnknownSource        OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "With the object pwTemplateMacAddressLimit a limit can
                     be configured for the max number of MAC addresses that
                     will be learned on the SDP Bind (only for spoke SDPs).
                     When the limit is reached, packets with unknown source
                     MAC address are forwarded by default. By setting
                     sdpBindTlsDiscardUnknownSource to 'enabled', packets with
                     unknown source MAC will be dropped instead."
    DEFVAL          { disabled }
    ::= { pwTemplateEntry 10 }

pwTemplateLimitMacMove        OBJECT-TYPE
    SYNTAX          TlsLimitMacMove
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateLimitMacMove specifies the
                     behavior for when the re-learn rate specified by
                     svcTlsMacMoveMaxRate is exceeded.

                     When pwTemplateLimitMacMove value is set to 'blockable'
                     the agent will monitor the MAC relearn rate on the
                     SDP  Bind, and it will block it when the re-learn rate
                     specified by svcTlsMacMoveMaxRate is exceeded. When the
                     value is 'nonBlockable' the SDP Bind will not be
                     blocked,  and  another  blockable  SDP Bind will be
                     blocked instead."
    DEFVAL          { blockable }
    ::= { pwTemplateEntry 11 }

pwTemplateMacPinning        OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateMacPinning specifies
                     whether or not MAC address pinning is active on the
                     SDP Bind (mesh or spoke). Setting the value to 'enabled'
                     disables re-learning of MAC addresses on other SAPs or
                     SDPs within the same VPLS; the MAC address will hence
                     remain attached to the SDP Bind for the duration of
                     its age-timer. This object has effect only for MAC
                     addresses learned via the normal MAC learning
                     process, and not for entries learned via DHCP. The
                     value will be set by default to 'disabled'. However for
                     a spoke SDP that belongs to a residential SHG, the
                     value is set to enabled by the system, and cannot be
                     altered by the operator."
    DEFVAL { disabled }
    ::= { pwTemplateEntry 12 }

pwTemplateVlanVcTag        OBJECT-TYPE
    SYNTAX          Unsigned32 ('0000'H..'0fff'H)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateVlanVcTag specifies the VLAN VC tag
                     for the SDP Bind."
    DEFVAL          { '0fff'H }
    ::= { pwTemplateEntry 13 }

pwTemplateMacAddressLimit        OBJECT-TYPE
    SYNTAX          Unsigned32 (0..196607)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateMacAddressLimit specifies
                     the maximum number of learned and static entries
                     allowed in the FDB for the SDP Bind. The value 0
                     specifies no limit for the SDP Bind. The command is
                     valid only for spoke SDPs. When the value of
                     ALCATEL-IND1-TIMETRA-CHASSIS-MIB::tmnxChassisOperMode is not 'c', the
                     maximum value of pwTemplateMacAddressLimit is '131071'."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 14 }

pwTemplateShgName        OBJECT-TYPE
    SYNTAX          TNamedItemOrEmpty
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateShgName specifies the name of the
                     split-horizon group where the spoke SDP Bind belongs to.
                     By default a spoke SDP Bind does not belong to any
                     split-horizon group. The name specified must
                     correspond to an existing split-horizon group in the TLS
                     service where the spoke SDP Bind is defined."
    DEFVAL          { "" }
    ::= { pwTemplateEntry 15 }

pwTemplateShgDescription        OBJECT-TYPE
    SYNTAX          TItemDescription
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateShgDescription specifies a 
                     user-provided description for split-horizon group on
                     the SDP Bind."
    DEFVAL          { "" }
    ::= { pwTemplateEntry 16 }

pwTemplateShgRestProtSrcMac        OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateShgRestProtSrcMac specifies
                     how the agent will handle relearn requests for protected
                     MAC addresses. When the value of this object is 'true'
                     requests to relearn a protected MAC address will be
                     ignored."
    DEFVAL          { false }
    ::= { pwTemplateEntry 17 }

pwTemplateShgRestUnprotDstMac        OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateShgRestUnprotDstMac specifies
                     how the system will forward packets destined to an
                     unprotected MAC address. When the value of this object is
                     'true' packets destined to an unprotected MAC address
                     will be dropped."
    DEFVAL          { false }
    ::= { pwTemplateEntry 18 }

pwTemplateEgressMacFilterId        OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateEgressMacFilterId specifies
                     the tMacFilterId which indexes an egress filter entry
                     in ALCATEL-IND1-TIMETRA-FILTER-MIB::tMacFilterTable, or zero if no
                     filter is specified."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 19 }

pwTemplateEgressIpFilterId        OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateEgressIpFilterId specifies
                     the tIPFilterId which indexes an egress filter entry
                     in ALCATEL-IND1-TIMETRA-FILTER-MIB::tIPFilterTable, or zero if no
                     filter is specified."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 20 }

pwTemplateEgressIpv6FilterId        OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateEgressIpv6FilterId specifies
                     the tIPv6FilterId which indexes an egress filter entry
                     in ALCATEL-IND1-TIMETRA-FILTER-MIB::tIPv6FilterTable, or zero if no
                     filter is specified."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 21 }

pwTemplateIngressMacFilterId        OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIngressMacFilterId specifies
                     the tMacFilterId which indexes an ingress filter entry
                     in ALCATEL-IND1-TIMETRA-FILTER-MIB::tMacFilterTable, or zero if no
                     filter is specified."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 22 }

pwTemplateIngressIpFilterId        OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIngressIpFilterId specifies
                     the tIPFilterId which indexes an ingress filter entry
                     in ALCATEL-IND1-TIMETRA-FILTER-MIB::tIPFilterTable, or zero if no
                     filter is specified."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 23 }

pwTemplateIngressIpv6FilterId        OBJECT-TYPE
    SYNTAX          TFilterID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIngressIpv6FilterId specifies
                     the tIPv6FilterId which indexes an ingress filter entry
                     in ALCATEL-IND1-TIMETRA-FILTER-MIB::tIPv6FilterTable, or zero if no
                     filter is specified."
    DEFVAL          { 0 }
    ::= { pwTemplateEntry 24 }

pwTemplateIgmpFastLeave        OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpFastLeave specifies
                     whether or not fastleave is allowed on the SDP Bind.

                     If set to 'enabled', the system prunes the port on which an IGMP
                     'leave' message has been received without waiting for the Group
                     Specific Query to timeout."
    DEFVAL { disabled }
    ::= { pwTemplateEntry 25 }

pwTemplateIgmpImportPlcy        OBJECT-TYPE
    SYNTAX          TNamedItemOrEmpty
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpImportPlcy specifies
                     a policy statement that must be applied to all
                     incoming IGMP messages on the SDP Bind."
    DEFVAL { "" }
    ::= { pwTemplateEntry 26 }

pwTemplateIgmpLastMembIntvl        OBJECT-TYPE
    SYNTAX          Unsigned32 (1..50)
    UNITS           "deci-seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpLastMembIntvl specifies
                     the Max Response Time (in tenths of a second) used in
                     Group-Specific and Group-Source-Specific Queries sent
                     in response to 'leave' messages. This is also the
                     amount of time between Group-Specific Query messages.

                     This value may be tuned to modify the leave latency of
                     the network. A reduced value results in reduced time to
                     detect the loss of the last member of a group."
    DEFVAL { 10 }
    ::= { pwTemplateEntry 27 }

pwTemplateIgmpMaxNbrGrps        OBJECT-TYPE
    SYNTAX          Unsigned32 (0..1000)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpMaxNbrGrps specifies how many
                     group addresses are allowed for the SDP Bind. The value 0
                     means that no limit is imposed."
    DEFVAL { 0 }
    ::= { pwTemplateEntry 28 }

pwTemplateIgmpGenQueryIntvl        OBJECT-TYPE
    SYNTAX          Unsigned32 (2..1024)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpGenQueryIntvl specifies
                     the interval (in seconds) between two consecutive general
                     queries sent by the system on the SDP.

                     The value of this object is only meaningful when the value of
                     pwTemplateIgmpSendQueries is 'enabled'."
    DEFVAL { 125 }
    ::= { pwTemplateEntry 29 }

pwTemplateIgmpQueryRespIntvl        OBJECT-TYPE
    SYNTAX          Unsigned32 (1..1023)
    UNITS           "seconds"
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpQueryRespIntvl specifies the
                     maximum response time (in seconds) advertised in
                     IGMPv2/v3 queries.

                     The value of this object is only meaningful when the value of
                     pwTemplateIgmpSendQueries is 'enabled'."
    DEFVAL { 10 }
    ::= { pwTemplateEntry 30 }

pwTemplateIgmpRobustCount        OBJECT-TYPE
    SYNTAX          Unsigned32 (2..7)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpRobustCount specifies the
                     value of the Robust count.

                     This object allows tuning for the expected packet loss on
                     the SDP. If an SDP is expected to be lossy, the Robustness
                     Variable may be increased. IGMP snooping is robust to
                     (Robustness Variable-1) packet losses.

                     The value of this object is only meaningful when the
                     value of pwTemplateIgmpSendQueries is 'enabled'."
    DEFVAL { 2 }
    ::= { pwTemplateEntry 31 }

pwTemplateIgmpSendQueries        OBJECT-TYPE
    SYNTAX          TmnxEnabledDisabled
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpSendQueries specifies whether
                     the system generates General Queries by itself on the SDP."
    DEFVAL { disabled }
    ::= { pwTemplateEntry 32 }

pwTemplateIgmpMcacPolicyName OBJECT-TYPE
    SYNTAX     TPolicyStatementNameOrEmpty
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The value of pwTemplateIgmpMcacPolicyName indicates the name
         of the multicast CAC policy."
    DEFVAL { "" }
    ::= { pwTemplateEntry 33 }

pwTemplateIgmpMcacUnconstBW OBJECT-TYPE
    SYNTAX      Integer32 (-1|0..2147483647)
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of pwTemplateIgmpMcacUnconstBW specifies the bandwidth
         assigned for interface's multicast CAC policy traffic in kilo-bits per
         second(kbps). 

         If the default value of '-1' is set, there is no constraint on
         bandwidth allocated at the interface. 

         If the value of pwTemplateIgmpMcacUnconstBW is set to '0' and if 
         a multicast CAC policy is assigned on the interface, then 
         no group (channel) from that policy is allowed on that interface."
    DEFVAL { -1 }
    ::= { pwTemplateEntry 34 }

pwTemplateIgmpMcacPrRsvMndBW OBJECT-TYPE
    SYNTAX      Integer32 (-1|0..2147483647)
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of pwTemplateIgmpMcacPrRsvMndBW specifies the bandwidth
         pre-reserved for all the mandatory channels on a given interface
         in kilo-bits per second(kbps). 
              
         If the value of pwTemplateIgmpMcacUnconstBW is '0', no mandatory
         channels are allowed. If the value of pwTemplateIgmpMcacUnconstBW 
         is '-1', then all mandatory and optional channels are allowed.

         If the value of pwTemplateIgmpMcacPrRsvMndBW is equal to the 
         value of pwTemplateIgmpMcacUnconstBW, then all the unconstrained 
         bandwidth on a given interface is allocated to mandatory channels 
         configured through multicast CAC policy on that interface and no 
         optional groups (channels) are allowed.

         The value of pwTemplateIgmpMcacPrRsvMndBW should always be less 
         than or equal to that of pwTemplateIgmpMcacUnconstBW. An attempt 
         to set the value of pwTemplateIgmpMcacPrRsvMndBW greater than 
         that of pwTemplateIgmpMcacUnconstBW will result in 
         'inconsistentValue' error."
    DEFVAL { -1 }
    ::= { pwTemplateEntry 35 }

pwTemplateIgmpVersion OBJECT-TYPE
    SYNTAX      TmnxIgmpVersion
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of pwTemplateIgmpVersion specifies the version
         of IGMP for the PW template."
    DEFVAL { version3 }
    ::= { pwTemplateEntry 36 }


-- ----------------------------------------------
-- PW Template IGMP Snooping Group Source Table
-- ----------------------------------------------
pwTemplateIgmpSnpgGrpSrcTblLC  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpSnpgGrpSrcTblLC indicates
                     the sysUpTime at the time of the last modification
                     of pwTemplateIgmpSnpgGrpSrcTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management subsystem,
                     then this object contains a zero value."
    ::= { tmnxSdpObjs 19 }

pwTemplateIgmpSnpgGrpSrcTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF PwTemplateIgmpSnpgGrpSrcEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "A table that contains entries for static IGMP Snooping
                     groups."
    ::= { tmnxSdpObjs 20 }

pwTemplateIgmpSnpgGrpSrcEntry    OBJECT-TYPE
    SYNTAX          PwTemplateIgmpSnpgGrpSrcEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Information about a specific static IGMP Snooping groups."
    INDEX { pwTemplateId, 
            pwTemplateIgmpSnpgGrpAddrType,
            pwTemplateIgmpSnpgGrpAddr,
            pwTemplateIgmpSnpgSrcAddrType,
            pwTemplateIgmpSnpgSrcAddr }
    ::= { pwTemplateIgmpSnpgGrpSrcTable 1}

PwTemplateIgmpSnpgGrpSrcEntry ::=    SEQUENCE {
    pwTemplateIgmpSnpgGrpAddrType  InetAddressType,
    pwTemplateIgmpSnpgGrpAddr      InetAddress,
    pwTemplateIgmpSnpgSrcAddrType  InetAddressType,
    pwTemplateIgmpSnpgSrcAddr      InetAddress,
    pwTemplateIgmpSnpgRowStatus    RowStatus,
    pwTemplateIgmpSnpgLastChngd    TimeStamp
}

pwTemplateIgmpSnpgGrpAddrType    OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The IP multicast group address type for this entry."
    ::= { pwTemplateIgmpSnpgGrpSrcEntry 1 }

pwTemplateIgmpSnpgGrpAddr        OBJECT-TYPE
    SYNTAX          InetAddress (SIZE(0|4))
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The IP multicast group address for this entry."
    ::= { pwTemplateIgmpSnpgGrpSrcEntry 2 }

pwTemplateIgmpSnpgSrcAddrType        OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The source address type for this entry."
    ::= { pwTemplateIgmpSnpgGrpSrcEntry 3 }

pwTemplateIgmpSnpgSrcAddr        OBJECT-TYPE
    SYNTAX          InetAddress (SIZE(0|4))
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The source address for this entry."
    ::= { pwTemplateIgmpSnpgGrpSrcEntry 4 }

pwTemplateIgmpSnpgRowStatus        OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpSnpgRowStatus is used for
                     the creation and deletion of static IGMP snooping entries."
    ::= { pwTemplateIgmpSnpgGrpSrcEntry 5 }

pwTemplateIgmpSnpgLastChngd        OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of pwTemplateIgmpSnpgLastChngd indicates the 
                     sysUpTime at the time of the last modification of this
                     entry.
                     
                     If no changes were made to the entry since the last 
                     re-initialization of the local network management 
                     subsystem, then this object contains a zero value."
    ::= { pwTemplateIgmpSnpgGrpSrcEntry 6 }

-- --------------------------------------------
-- SDP Bind TLS Egress MFIB Allowed MDA Destinations Table
-- --------------------------------------------
pwTemplateMfibAllowedMdaTblLC  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of pwTemplateMfibAllowedMdaTblLC indicates
                     the sysUpTime at the time of the last modification
                     of pwTemplateMfibAllowedMdaTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management subsystem,
                     then this object contains a zero value."
    ::= { tmnxSdpObjs 21 }

pwTemplateMfibAllowedMdaTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF PwTemplateMfibAllowedMdaEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The pwTemplateMfibAllowedMdaTable has an entry for each
                     MFIB allowed MDA destination for an PW template."
    ::= { tmnxSdpObjs 22 }

pwTemplateMfibAllowedMdaEntry OBJECT-TYPE
    SYNTAX          PwTemplateMfibAllowedMdaEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry represents an MFIB allowed MDA destination
                     for an PW template configured in the system. Entries
                     can be created and deleted via SNMP SET operations on the
                     object pwTemplateMfibMdaRowStatus."
    INDEX           { pwTemplateId,
                      tmnxChassisIndex,
                      tmnxCardSlotNum,
                      tmnxMDASlotNum }
    ::= { pwTemplateMfibAllowedMdaTable 1 }

PwTemplateMfibAllowedMdaEntry ::=
    SEQUENCE {
        pwTemplateMfibMdaRowStatus RowStatus
    }

pwTemplateMfibMdaRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "The value of pwTemplateMfibMdaRowStatus controls the
                     creation and deletion of rows in this table."
    ::= { pwTemplateMfibAllowedMdaEntry 1 }

-- ----------------------------------------
-- SDP BIND TLS MRP Information Table
-- ----------------------------------------
sdpBindTlsMrpTableLastChanged  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMrpTableLastChanged indicates the
                     sysUpTime at the time of the last modification
                     of sdpBindTlsMrpTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management subsystem,
                     then this object contains a zero value."
    ::= { tmnxSdpObjs 23 }

sdpBindTlsMrpTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindTlsMrpEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The sdpBindTlsMrpTable allows the operator to modify
                     attributes of the Multiple Registration Protocol (MRP)
                     feature for the TLS SDP Bind.

                     This table contains an entry for each TLS SDP Bind created
                     by the user using either sdpBindTlsTable or
                     sdpBindMeshTlsTable.

                     Rows in this table are created and deleted automatically
                     by the system."
    ::= { tmnxSdpObjs 24 }

sdpBindTlsMrpEntry OBJECT-TYPE
    SYNTAX          SdpBindTlsMrpEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry contains objects that allows the
                     modification of the Multiple Registration Protocol feature
                     for a specific SDP-Binding in a TLS service."
    INDEX           { svcId, sdpBindId }
    ::= { sdpBindTlsMrpTable 1 }

SdpBindTlsMrpEntry ::=
    SEQUENCE {
        sdpBindTlsMrpLastChngd          TimeStamp,
        sdpBindTlsMrpJoinTime           Unsigned32,
        sdpBindTlsMrpLeaveTime          Unsigned32,
        sdpBindTlsMrpLeaveAllTime       Unsigned32,
        sdpBindTlsMrpPeriodicTime       Unsigned32,
        sdpBindTlsMrpPeriodicEnabled    TruthValue,
        sdpBindTlsMrpRxPdus             Counter32,
        sdpBindTlsMrpDroppedPdus        Counter32,
        sdpBindTlsMrpTxPdus             Counter32,
        sdpBindTlsMrpRxNewEvent         Counter32,
        sdpBindTlsMrpRxJoinInEvent      Counter32,
        sdpBindTlsMrpRxInEvent          Counter32,
        sdpBindTlsMrpRxJoinEmptyEvent   Counter32,
        sdpBindTlsMrpRxEmptyEvent       Counter32,
        sdpBindTlsMrpRxLeaveEvent       Counter32,
        sdpBindTlsMrpTxNewEvent         Counter32,
        sdpBindTlsMrpTxJoinInEvent      Counter32,
        sdpBindTlsMrpTxInEvent          Counter32,
        sdpBindTlsMrpTxJoinEmptyEvent   Counter32,
        sdpBindTlsMrpTxEmptyEvent       Counter32,
        sdpBindTlsMrpTxLeaveEvent       Counter32
    }

sdpBindTlsMrpLastChngd        OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMrpLastChngd indicates the 
                     sysUpTime at the time of the last modification of this
                     entry.
                     
                     If no changes were made to the entry since the last 
                     re-initialization of the local network management 
                     subsystem, then this object contains a zero value."
    ::= { sdpBindTlsMrpEntry 1 }

sdpBindTlsMrpJoinTime OBJECT-TYPE
    SYNTAX          Unsigned32 (1..10)
    UNITS           "deci-seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMrpJoinTime specifies a timer
                    value in 10ths of seconds which determines the maximum rate
                    at which attribute join messages can be sent on the SDP."
    DEFVAL         { 2 }
    ::= { sdpBindTlsMrpEntry 2 }

sdpBindTlsMrpLeaveTime OBJECT-TYPE
    SYNTAX          Unsigned32 (30..60)
    UNITS           "deci-seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMrpLeaveTime specifies a timer
                    value in 10ths of seconds which determines the amount of
                    time a registered attribute is held in leave state before
                    the registration is removed."
    DEFVAL         { 30 }
    ::= { sdpBindTlsMrpEntry 3 }
    
sdpBindTlsMrpLeaveAllTime OBJECT-TYPE
    SYNTAX          Unsigned32 (60..300)
    UNITS           "deci-seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMrpLeaveAllTime specifies a timer
                    value in 10ths of seconds which determines the frequency
                    where all attribute declarations on the SDP are all
                    refreshed."
    DEFVAL         { 100 }
    ::= { sdpBindTlsMrpEntry 4 }
    
sdpBindTlsMrpPeriodicTime OBJECT-TYPE
    SYNTAX          Unsigned32 (10..100)
    UNITS           "deci-seconds"
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMrpPeriodicTime specifies a timer
                    value in 10ths of seconds which determines the frequency of
                    re-transmission of attribute declarations."
    DEFVAL         { 10 }
    ::= { sdpBindTlsMrpEntry 5 }
    
sdpBindTlsMrpPeriodicEnabled OBJECT-TYPE
    SYNTAX          TruthValue
    MAX-ACCESS      read-write
    STATUS          current
    DESCRIPTION     "The value of sdpBindTlsMrpPeriodicEnabled specifies whether
                    re-transmission of attribute declarations is enabled."
    DEFVAL         { false }
    ::= { sdpBindTlsMrpEntry 6 }

sdpBindTlsMrpRxPdus OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpRxPdus indicates the number of MRP packets
        received on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 7 }

sdpBindTlsMrpDroppedPdus OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpDroppedPdus indicates the number of dropped
        MRP packets on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 8 }

sdpBindTlsMrpTxPdus OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpTxPdus indicates the number of MRP packets
        transmitted on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 9 }

sdpBindTlsMrpRxNewEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpRxNewEvent indicates the number of 'New' MRP
        events received on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 10 }

sdpBindTlsMrpRxJoinInEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpRxJoinInEvent indicates the number of
        'Join-In' MRP events received on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 11 }

sdpBindTlsMrpRxInEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpRxInEvent indicates the number of 'In' MRP
        events received on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 12 }

sdpBindTlsMrpRxJoinEmptyEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpRxJoinEmptyEvent indicates the number of
        'Join Empty' MRP events received on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 13 }
    
sdpBindTlsMrpRxEmptyEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpRxEmptyEvent indicates the number of 'Empty'
        MRP events received on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 14 }
    
sdpBindTlsMrpRxLeaveEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpRxLeaveEvent indicates the number of 'Leave'
        MRP events received on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 15 }
    
sdpBindTlsMrpTxNewEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpTxNewEvent indicates the number of 'New' MRP
        events transmitted on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 16 }
    
sdpBindTlsMrpTxJoinInEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpTxJoinInEvent indicates the number of
        'Join-In' MRP events transmitted on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 17 }
    
sdpBindTlsMrpTxInEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpTxInEvent indicates the number of 'In' MRP
        events transmitted on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 18 }
    
sdpBindTlsMrpTxJoinEmptyEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpTxJoinEmptyEvent indicates the number of
        'Join Empty' MRP events transmitted on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 19 }

sdpBindTlsMrpTxEmptyEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpTxEmptyEvent indicates the number of 'Empty'
        MRP events transmitted on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 20 }

sdpBindTlsMrpTxLeaveEvent OBJECT-TYPE
    SYNTAX           Counter32
    MAX-ACCESS       read-only
    STATUS           current
    DESCRIPTION      
        "The value of sdpBindTlsMrpTxLeaveEvent indicates the number of 'Leave'
        MRP events transmitted on this SDP Bind."
    ::= { sdpBindTlsMrpEntry 21 }

-- -------------------------
-- SDP Bind TLS MMRP Table
-- -------------------------
sdpBindTlsMmrpTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpBindTlsMmrpEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "This table contains an entry for each MAC address managed
                    by Multiple MAC Registration Protocol (MMRP) on the SDP
                    Bind for the TLS.  Entries are dynamically created and
                    destroyed by the system as the MAC Addresses are registered
                    or declared in MMRP."
    ::= { tmnxSdpObjs 25 }

sdpBindTlsMmrpEntry OBJECT-TYPE
    SYNTAX          SdpBindTlsMmrpEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "MMRP specific information about a MAC address managed by
                    MMRP on a SDP Bind in a TLS."
    INDEX           { svcId, sdpBindId, sdpBindTlsMmrpMacAddr }
    ::= { sdpBindTlsMmrpTable 1 }

SdpBindTlsMmrpEntry ::=
    SEQUENCE {
        sdpBindTlsMmrpMacAddr              MacAddress,
        sdpBindTlsMmrpDeclared             TruthValue,
        sdpBindTlsMmrpRegistered           TruthValue
    }

sdpBindTlsMmrpMacAddr OBJECT-TYPE
    SYNTAX        MacAddress
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "The value of sdpBindTlsMmrpMacAddr indicates an ethernet MAC address which
        is being managed by MMRP on this SAP."
    ::= { sdpBindTlsMmrpEntry 1 }

sdpBindTlsMmrpDeclared OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The value of sdpBindTlsMmrpDeclared indicates whether the MRP applicant
        on this SAP is declaring this MAC address on behalf of MMRP."
    ::= { sdpBindTlsMmrpEntry 2 }

sdpBindTlsMmrpRegistered OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The value of sdpBindTlsMmrpRegistered indicates whether the MRP
        registrant on this SAP has notified MMRP of a registration of this MAC
        address."
    ::= { sdpBindTlsMmrpEntry 3 }

-- ---------------------------------------------------------------------
--  SDP Auto Bind Bgp Auto-Discovery Info
-- 
--  Sparse Dependent Extention of the sdpBindTable.
--
--  The same indexes are used for both the base table, sdpBindTable,
--  and the sparse dependent table, sdpAutoBindBgpInfoTable.
--
--  This in effect extends the sdpBindTable with additional columns.
--  Rows are created in the sdpAutoBindBgpInfoTable only for those entries
--  in the sdpBindTable that are created as a result of BGP Auto-discovery.
--
--  Deletion of a row in the sdpBindTable results in the
--  same fate for the row in the sdpAutoBindBgpInfoTable.
-- ---------------------------------------------------------------------
sdpAutoBindBgpInfoTableLC  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpAutoBindBgpInfoTableLC indicates
                     the sysUpTime at the time of the last modification
                     of sdpAutoBindBgpInfoTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management 
                     subsystem, then this object contains a zero value."
    ::= { tmnxSdpObjs 26 }

sdpAutoBindBgpInfoTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SdpAutoBindBgpInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The sdpAutoBindBgpInfoTable has an entry for each
                     SDP Bind entry from sdpBindTable which was
                     created as a result of BGP Auto-discovery."
    ::= { tmnxSdpObjs 27 }

sdpAutoBindBgpInfoEntry OBJECT-TYPE
    SYNTAX          SdpAutoBindBgpInfoEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "Each row entry contains BGP-related information for an
                     SDP Bind entry created as a result of BGP Auto-discovery."
    INDEX           { svcId, sdpBindId }
    ::= { sdpAutoBindBgpInfoTable 1 }

SdpAutoBindBgpInfoEntry ::=
    SEQUENCE {
        sdpAutoBindBgpInfoTemplateId   PWTemplateId,
        sdpAutoBindBgpInfoAGI          TmnxVPNRouteDistinguisher,
        sdpAutoBindBgpInfoSAII         Unsigned32,
        sdpAutoBindBgpInfoTAII         Unsigned32
    }

sdpAutoBindBgpInfoTemplateId OBJECT-TYPE
    SYNTAX          PWTemplateId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpAutoBindBgpInfoTemplateId indicates the
                     the value of the pwTemplateId object for the
                     PW template entry used to create this
                     SDP Bind."
    ::= { sdpAutoBindBgpInfoEntry 1 }

sdpAutoBindBgpInfoAGI  OBJECT-TYPE
    SYNTAX          TmnxVPNRouteDistinguisher
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpAutoBindBgpInfoAGI indicates the
                     Attachment Group Indentifier (AGI) portion of the
                     Generalized Id FEC element from the pseudowire
                     setup for this SDP Bind."
    ::= { sdpAutoBindBgpInfoEntry 2 }

sdpAutoBindBgpInfoSAII   OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpAutoBindBgpInfoSAII indicates the
                     Source Attachment Individual Indentifier (SAII) portion
                     of the Generalized Id FEC element from the pseudowire
                     setup for this SDP Bind."
    ::= { sdpAutoBindBgpInfoEntry 3 }

sdpAutoBindBgpInfoTAII   OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of sdpAutoBindBgpInfoTAII indicates the
                     Target Attachment Individual Indentifier (TAII) portion
                     of the Generalized Id FEC element from the pseudowire
                     setup for this SDP Bind."
    ::= { sdpAutoBindBgpInfoEntry 4 }

-- -------------------------------------
-- BGP Auto-Discovery SDP Auto Policy Table
-- -------------------------------------

svcTlsBgpADPWTempBindTblLC  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of svcTlsBgpADPWTempBindTblLC indicates the
                     sysUpTime at the time of the last modification
                     of svcTlsBgpADPWTempBindTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management subsystem,
                     then this object contains a zero value."
    ::= { tmnxSvcObjs 32 }

svcTlsBgpADPWTempBindTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SvcTlsBgpADAutoBindPlcyEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION    "svcTlsBgpADPWTempBindTable contains entries for the
                    associations between SDP Auto-Bind policies and a
                    BGP Auto-Discovery context for a VPLS service."
    ::= { tmnxSvcObjs 33 }

svcTlsBgpADPWTempBindEntry    OBJECT-TYPE
    SYNTAX          SvcTlsBgpADAutoBindPlcyEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION    "A SDP Auto-Bind Policy entry in the
                    svcTlsBgpADPWTempBindTable."
    INDEX { svcId, pwTemplateId }
    ::= { svcTlsBgpADPWTempBindTable 1 }

SvcTlsBgpADAutoBindPlcyEntry ::=    SEQUENCE {
    svcTlsBgpADPWTempBindRowStatus  RowStatus,
    svcTlsBgpADPWTempBindLastChngd  TimeStamp,
    svcTlsBgpADPWTempBindSHG        TNamedItemOrEmpty
}

svcTlsBgpADPWTempBindRowStatus OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION    "The value of svcTlsBgpADPWTempBindRowStatus is used
                    for the creation and deletion of associations between
                    SDP Auto-Bind policies and a BGP Auto-Discovery context
                    for a VPLS service."
    ::= { svcTlsBgpADPWTempBindEntry 1 }

svcTlsBgpADPWTempBindLastChngd        OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of svcTlsBgpADPWTempBindLastChngd indicates
                     the sysUpTime at the time of the last modification of
                     this entry.
                     
                     If no changes were made to the entry since the last 
                     re-initialization of the local network management 
                     subsystem, then this object contains a zero value."
    ::= { svcTlsBgpADPWTempBindEntry 2 }


svcTlsBgpADPWTempBindSHG       OBJECT-TYPE
    SYNTAX          TNamedItemOrEmpty
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION    "The value of svcTlsBgpADPWTempBindSHG specifies the
                    split-horizon group to associate with the SDP Auto-Bind
                    policy in this BGP Auto-Discovery context in a VPLS
                    service.

                    When this Auto-Bind policy is used to create an SDP,
                    this split-horizon group will be associated with the
                    SDP.

                    The name specified must correspond to an
                    existing split-horizon group in the VPLS service,
                    otherwise an 'inconsistentValue' error will be
                    returned."
     DEFVAL       { "" }
    ::= { svcTlsBgpADPWTempBindEntry 3 }

-- -----------------------------------------
-- BGP Auto-Discovery SDP Auto Policy Route Target Table
-- -----------------------------------------

svcTlsBgpADPWTempBindRTTblLC  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of svcTlsBgpADPWTempBindRTTblLC indicates the
                     sysUpTime at the time of the last modification
                     of svcTlsBgpADPWTempBindRTTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management subsystem,
                     then this object contains a zero value."
    ::= { tmnxSvcObjs 34 }

svcTlsBgpADPWTempBindRTTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF SvcTlsBgpADAutoBindPlcyRTEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION    "svcTlsBgpADPWTempBindTable contains entries for Route
                    Targets associated with a SDP Auto-Bind policy and a
                    BGP Auto-Discovery context for a VPLS service."
    ::= { tmnxSvcObjs 35 }

svcTlsBgpADPWTempBindRTEntry    OBJECT-TYPE
    SYNTAX          SvcTlsBgpADAutoBindPlcyRTEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION    "A SDP Auto-Bind Policy Route Target entry in the
                    svcTlsBgpADPWTempBindRTTable."
    INDEX { svcId, pwTemplateId, IMPLIED svcTlsBgpADPWTempBindRT }
    ::= { svcTlsBgpADPWTempBindRTTable 1 }

SvcTlsBgpADAutoBindPlcyRTEntry ::=    SEQUENCE {
    svcTlsBgpADPWTempBindRT         TNamedItem,
    svcTlsBgpADPWTempBindRTRowStat  RowStatus
}

svcTlsBgpADPWTempBindRT OBJECT-TYPE
    SYNTAX          TNamedItem
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION    "The value of svcTlsBgpADPWTempBindRT is the Route
                    Target associated with a PW template and a
                    BGP Auto-Discovery context for a VPLS service.

                    When advertisements are received with this Route Target,
                    the PW template specified by the index, pwTemplateId,
                    will be used to create the SDP."
    ::= { svcTlsBgpADPWTempBindRTEntry 1 }

svcTlsBgpADPWTempBindRTRowStat OBJECT-TYPE
    SYNTAX          RowStatus
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION    "The value of svcTlsBgpADPWTempBindRTRowStat is used
                    for the association of Route Targets with a SDP Auto-Bind
                    policy and a BGP Auto-Discovery context for a VPLS
                    service."
    ::= { svcTlsBgpADPWTempBindRTEntry 2 }

-- -------------------------
-- L2 Route Table
-- -------------------------

svcL2RteTableLastChanged  OBJECT-TYPE
    SYNTAX          TimeStamp
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of svcL2RteTableLastChanged indicates the
                     sysUpTime at the time of the last modification of
                     svcL2RteTable.

                     If no changes were made to the entry since the last
                     re-initialization of the local network management subsystem,
                     then this object contains a zero value."
    ::= { tmnxSvcObjs 38 }

svcL2RteTable       OBJECT-TYPE
    SYNTAX          SEQUENCE OF SvcL2RteEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "svcL2RteTable contains entries for L2 routes."
    ::= { tmnxSvcObjs 39 }

svcL2RteEntry       OBJECT-TYPE
    SYNTAX          SvcL2RteEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "An L2 route entry in the svcL2RteTable."
    INDEX           { svcId,
                      svcL2RteVsiPrefix,
                      svcL2RteRouteDistinguisher,
                      svcL2RteNextHopType,
                      svcL2RteNextHop }
    ::= { svcL2RteTable 1}

SvcL2RteEntry ::= SEQUENCE {
    svcL2RteVsiPrefix           Unsigned32,
    svcL2RteRouteDistinguisher  TmnxVPNRouteDistinguisher,
    svcL2RteNextHopType         InetAddressType,
    svcL2RteNextHop             InetAddress,
    svcL2RteSdpBindId           SdpBindId,
    svcL2RtePwTemplateId        PWTemplateId
}

svcL2RteVsiPrefix   OBJECT-TYPE
    SYNTAX          Unsigned32
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The value of svcL2RteVsiPrefix is the low-order 4 bytes
                     of the Virtual Switch Instance idendifier (VSI-id) of the
                     remote VSI for this L2 route."
    ::= { svcL2RteEntry 1 }

svcL2RteRouteDistinguisher  OBJECT-TYPE
    SYNTAX          TmnxVPNRouteDistinguisher
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The value of svcL2RteRouteDistinguisher is the high-order
                     6 bytes of the Virtual Switch Instance idendifier (VSI-id)
                     of the remote VSI for this L2 route."
    ::= { svcL2RteEntry 2 }

svcL2RteNextHopType OBJECT-TYPE
    SYNTAX          InetAddressType
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The value of svcL2RteNextHopType indicates the address
                     type of svcL2RteNextHop."
    ::= { svcL2RteEntry 3 }

svcL2RteNextHop     OBJECT-TYPE
    SYNTAX          InetAddress
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION     "The value of svcL2RteNextHop indicates the IP next hop
                     for this L2 route. This value is equivilant to the
                     IP address of the Far End of this L2 route."
    ::= { svcL2RteEntry 4 }

svcL2RteSdpBindId     OBJECT-TYPE
    SYNTAX          SdpBindId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of svcL2RteSdpBindId indicates the SDP bind
                     ID of the SDP bind that binds this VPLS context to
                     the VSI indicated by svcL2RteRouteDistinguisher,
                     svcL2RteVsiPrefix, and svcL2RteNextHop."
    ::= { svcL2RteEntry 5 }

svcL2RtePwTemplateId  OBJECT-TYPE
    SYNTAX          PWTemplateId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     "The value of svcL2RtePwTemplateId indicates the PW
                     template associated with the SDP bind that binds
                     this VPLS context to the VSI indicated by
                     svcL2RteRouteDistinguisher, svcL2RteVsiPrefix,
                     and svcL2RteNextHop."
    ::= { svcL2RteEntry 6 }

-- --------------------------------------
-- SDP Notification Objects
-- --------------------------------------
-- tmnxSdpNotifyObjs       OBJECT IDENTIFIER ::= ( tmnxSdpObjs 100 }

sdpNotifySdpId      OBJECT-TYPE
    SYNTAX          SdpId
    MAX-ACCESS      accessible-for-notify
    STATUS          current
    DESCRIPTION     "The ID of the SDP where SDP Bindings are associated.
                     This object is used by the sdpBindSdpStateChangeProcessed
                     notification to indicate the SDP that changed
                     state and that resulted in having the associated
                     sdpBindStatusChanged events suppressed for all SDP
                     Bindings on that SDP."
    ::= { tmnxSdpNotifyObjs 1 }

dynamicSdpStatus OBJECT-TYPE
    SYNTAX          ConfigStatus
    MAX-ACCESS      accessible-for-notify
    STATUS          current
    DESCRIPTION     "The value of dynamicSdpStatus indicates the status of the
                     dynamic SDP which is used by the dynamicSdpConfigChanged
                     and dynamicSdpBindConfigChanged notifications to indicate
                     what state the dynamic SDP or SDP Bind 
                     has entered: 'created', 'modified', or 'deleted'."
    ::= { tmnxSdpNotifyObjs 2 }

dynamicSdpOrigin    OBJECT-TYPE
    SYNTAX          L2RouteOrigin
    MAX-ACCESS      accessible-for-notify
    STATUS          current
    DESCRIPTION     "The value of dynamicSdpOrigin indicates the origin of the
                     dynamic SDP. The origin indicates the protocol or mechanism
                     that created the dynamic SDP."
    ::= { tmnxSdpNotifyObjs 3 }

dynamicSdpCreationError OBJECT-TYPE
    SYNTAX         DisplayString
    MAX-ACCESS     accessible-for-notify
    STATUS         current
    DESCRIPTION    "The value of the object dynamicSdpCreationError
                    indicates the reason why the system was unable to create
                    the dynamic SDP."
    ::= { tmnxSdpNotifyObjs 4 }

dynamicSdpBindCreationError OBJECT-TYPE
    SYNTAX         DisplayString
    MAX-ACCESS     accessible-for-notify
    STATUS         current
    DESCRIPTION    "The value of the object dynamicSdpBindCreationError
                   indicates the reason why the system was unable to create
                   the dynamic SDP Binding."
    ::= { tmnxSdpNotifyObjs 5 }

-- --------------------------------------------
-- SDP traps
-- --------------------------------------------
sdpCreated NOTIFICATION-TYPE
    OBJECTS {
        sdpId
    }
    STATUS          obsolete
    DESCRIPTION     "The sdpCreated notification is sent when a new row is
                     created in the sdpInfoTable."
    ::= { sdpTraps 1 }

sdpDeleted NOTIFICATION-TYPE
    OBJECTS {
        sdpId
    }
    STATUS          obsolete
    DESCRIPTION     "The sdpDeleted notification is sent when an existing row
                     is deleted from the ng row is deleted from the
                     sdpInfoTable."
    ::= { sdpTraps 2 }

sdpStatusChanged NOTIFICATION-TYPE
    OBJECTS {
        sdpId,
        sdpAdminStatus,
        sdpOperStatus
    }
    STATUS          current
    DESCRIPTION     "The sdpStatusChanged notification is generated
                     when there is a change in the administrative or
                     operating status of an SDP."
    ::= { sdpTraps 3 }

sdpBindCreated NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId
    }
    STATUS          obsolete
    DESCRIPTION     "The sdpBindCreated notification is sent when a new row
                     is created in the sdpBindTable."
    ::= { sdpTraps 4 }

sdpBindDeleted NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId
    }
    STATUS          obsolete
    DESCRIPTION     "The sdpBindDeleted notification is sent when an existing
                     row is deleted from the sdpBindTable."
    ::= { sdpTraps 5 }

sdpBindStatusChanged NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        sdpBindAdminStatus,
        sdpBindOperStatus,
        sdpBindOperFlags
    }
    STATUS          current
    DESCRIPTION     "The sdpBindStatusChanged notification is generated
                     when there is a change in the administrative or
                     operating status of an SDP Binding.

                     Notice that this trap is not generated whenever
                     the SDP Binding operating status change is caused by
                     an operating status change on the associated SDP."
    ::= { sdpTraps 6 }

sdpTlsMacAddrLimitAlarmRaised  NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpId
    }
    STATUS          current
    DESCRIPTION     "The sdpTlsMacAddrLimitAlarmRaised notification is sent
                     whenever the number of MAC addresses stored in the FDB
                     for this spoke sdp increases to reach the watermark
                     specified by the object svcTlsFdbTableFullHighWatermark."
    ::= { sdpTraps 7 }

sdpTlsMacAddrLimitAlarmCleared  NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpId
    }
    STATUS          current
    DESCRIPTION     "The sdpTlsMacAddrLimitAlarmCleared notification is sent
                     whenever the number of MAC addresses stored in the FDB for
                     this spoke SDP drops to the watermark specified by the
                     object svcTlsFdbTableFullLowWatermark."
    ::= { sdpTraps 8 }

sdpTlsDHCPSuspiciousPcktRcvd NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpId,
        tlsDhcpPacketProblem
    }
    STATUS          obsolete
    DESCRIPTION     "The sdpTlsDHCPSuspiciousPcktRcvd notification is
                     generated when a DHCP packet is received with suspicious
                     content."
    ::= { sdpTraps 9 }

sdpBindDHCPLeaseEntriesExceeded NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpLseStateNewCiAddr,
        svcDhcpLseStateNewChAddr,
        svcDhcpClientLease
    }
    STATUS          current
    DESCRIPTION     "The sdpBindDHCPLeaseEntriesExceeded notification is
                     generated when the number of DHCP lease state entries on a
                     given IES or VRPN spoke-SDP reaches the user configurable
                     upper limit given by
                     ALCATEL-IND1-TIMETRA-VRTR-MIB::vRtrIfDHCPLeasePopulate."
    ::= { sdpTraps 10 }

sdpBindDHCPLseStateOverride NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpLseStateNewCiAddr,
        svcDhcpLseStateNewChAddr,
        svcDhcpLseStateOldCiAddr,
        svcDhcpLseStateOldChAddr
    }
    STATUS          current
    DESCRIPTION     "The sdpBindDHCPLseStateOverride notification is generated
                     when an existing DHCP lease state is overridden by a new
                     lease state which has the same IP address but a different
                     MAC address. This trap is only applicable for IES and VPRN
                     spoke-SDPs."
    ::= { sdpTraps 11 }

sdpBindDHCPSuspiciousPcktRcvd NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpPacketProblem
    }
    STATUS          current
    DESCRIPTION     "The sdpBindDHCPSuspiciousPcktRcvd notification is
                     generated when a DHCP packet is received with suspicious
                     content."
    ::= { sdpTraps 12 }

sdpBindDHCPLseStatePopulateErr NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpLseStatePopulateError
    }
    STATUS          current
    DESCRIPTION     "The sdpBindDHCPLseStatePopulateErr notification indicates
                     that the system was unable to update the DHCP Lease State
                     table with the information contained in the DHCP ACK
                     message. The DHCP ACK message has been discarded. This
                     trap is only applicable for IES and VPRN spoke-SDPs."
    ::= { sdpTraps 13 }

sdpBindPwPeerStatusBitsChanged NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        sdpBindPwPeerStatusBits
    }
    STATUS          current
    DESCRIPTION     "The sdpBindPwPeerStatusBitsChanged notification is 
                     generated when there is a change in the PW status
                     bits received from the peer."
    ::= { sdpTraps 14 }

sdpBindTlsMacMoveExceeded NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        sdpBindAdminStatus,
        sdpBindOperStatus,
        sdpBindTlsMacMoveRateExcdLeft,
        sdpBindTlsMacMoveNextUpTime,
        svcTlsMacMoveMaxRate
    }
    STATUS          current
    DESCRIPTION     "The sdpBindTlsMacMoveExceeded notification is generated
                     when the SDP exceeds the TLS svcTlsMacMoveMaxRate."
    ::= { sdpTraps 15 }

sdpBindPwPeerFaultAddrChanged NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        sdpBindPwFaultInetAddressType,
        sdpBindPwFaultInetAddress
    }
    STATUS          current
    DESCRIPTION     "The sdpBindPwPeerFaultAddrChanged notification is 
                     generated when there is a change in the IP address
                     included in the PW status message sent by the peer.
                     This notification is only generated if the IP address
                     is the only information in the notification that 
                     changed.  If the status bits changed as well, then
                     the sdpBindPwPeerStatusBitsChanged notification will 
                     be generated instead."
    ::= { sdpTraps 16 }

sdpBindDHCPProxyServerError NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpProxyError
    }
    STATUS          current
    DESCRIPTION     "The sdpBindDHCPProxyServerError notification indicates
                     that the system was unable to proxy DHCP requests."
    ::= { sdpTraps 17 }

sdpBindDHCPCoAError NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpCoAError
    }
    STATUS          obsolete
    DESCRIPTION     "The sdpBindDHCPCoAError notification indicates that
                     the system was unable to process a Change of Authorization
                     (CoA) request from a Radius server."
    ::= { sdpTraps 18 }

sdpBindDHCPSubAuthError NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpSubAuthError
    }
    STATUS          obsolete
    DESCRIPTION     "The sdpBindDHCPSubAuthError notification indicates that
                     the system encountered a problem while trying to
                     authenticate a subscriber."
    ::= { sdpTraps 19 }

sdpBindSdpStateChangeProcessed NOTIFICATION-TYPE
    OBJECTS {
        sdpNotifySdpId
    }
    STATUS          current
    DESCRIPTION     "The sdpBindSdpStateChangeProcessed notification 
                     indicates that the agent has finished processing an 
                     SDP state change event, and that the operating status
                     of all the affected SDP Bindings has been updated 
                     accordingly. The value of the sdpNotifySdpId object
                     indicates the SDP that experienced the state change."
    ::= { sdpTraps 20 }

sdpBindDHCPLseStateMobilityErr NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        svcVpnId,
        sdpBindId,
        svcDhcpLseStatePopulateError
    }
    STATUS          current
    DESCRIPTION     "The sdpBindDHCPLseStateMobilityErr notification indicates
                     that the system was unable to perform a mobility check
                     for this lease state."
    ::= { sdpTraps 21 }

sdpBandwidthOverbooked NOTIFICATION-TYPE
    OBJECTS {
        sdpId,
        sdpMaxBookableBandwidth,
        sdpBookedBandwidth
    }
    STATUS          current
    DESCRIPTION     "The sdpBandwidthOverbooked notification indicates
                     that the bandwidth that has been allocated to the SDP 
                     bindings indicated by sdpBookedBandwidth exceeds 
                     sdpMaxBookableBandwidth."
    ::= { sdpTraps 22 }

sdpBindInsufficientBandwidth NOTIFICATION-TYPE
    OBJECTS {
        svcId,
        sdpId,
        sdpBindId,
        sdpAvailableBandwidth,
        sdpBindAdminBandwidth
    }
    STATUS          current
    DESCRIPTION     "The sdpBindInsufficientBandwidth notification indicates
                     that the available bandwidth of the SDP is insufficient 
                     to satisfy the bandwidth requirement specified by 
                     sdpBindAdminBandwidth of this SDP binding."
    ::= { sdpTraps 23 }

dynamicSdpConfigChanged NOTIFICATION-TYPE
   OBJECTS {
       dynamicSdpOrigin,
       sdpId,
       svcL2RteSdpBindId,
       dynamicSdpStatus
   }
   STATUS          current
   DESCRIPTION     "The dynamicSdpConfigChanged notification is generated when a
                    dynamic SDP is 'created', 'modified', or 'deleted', with the
                    value of dynamicSdpStatus indicated which state it has entered."
   ::= { sdpTraps 24 }

dynamicSdpBindConfigChanged NOTIFICATION-TYPE
   OBJECTS {
       dynamicSdpOrigin,
       sdpId,
       svcL2RteSdpBindId,
       dynamicSdpStatus
   }
   STATUS          current
   DESCRIPTION     "The dynamicSdpBindConfigChanged notification is generated when a
                    dynamic SDP Bind is 'created', 'modified', or 'deleted', with the
                    value of dynamicSdpStatus indicated which state it has entered."
   ::= { sdpTraps 25 }

dynamicSdpCreationFailed NOTIFICATION-TYPE
   OBJECTS {
       svcL2RteSdpBindId,
       dynamicSdpOrigin,
       dynamicSdpCreationError
   }
   STATUS          current
   DESCRIPTION     "The dynamicSdpCreationFailed notification is generated
                    when the system fails to create a dynamic SDP."
   ::= { sdpTraps 26 }

dynamicSdpBindCreationFailed NOTIFICATION-TYPE
   OBJECTS {
       svcL2RteSdpBindId,
       dynamicSdpOrigin,
       sdpId,
       pwTemplateLastChanged,       
       dynamicSdpBindCreationError
   }
   STATUS          current
   DESCRIPTION     "The dynamicSdpBindCreationFailed notification is generated
                    when the system fails to create a dynamic SDP Bind."
   ::= { sdpTraps 27 }

-- ------------------------------------
-- TLS STP traps
-- ------------------------------------

unacknowledgedTCN NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpId
    }
    STATUS          current
    DESCRIPTION     "The unacknowledgedTCN notification is generated when a
                     TCN sent towards the root bridge on the root port (SAP
                     or  SDP  binding) has not been acknowledged within the
                     allowed  time. A portion of the spanning tree topology
                     may  not have been notified that a topology change has
                     taken  place.  FDB  tables  on  some  devices may take
                     significantly longer to represent the new distribution
                     of  layer-2 addresses. Examine this device and devices
                     towards the root bridge for STP issues."
    ::= { tstpTraps 8 }

tmnxSvcTopoChgSdpBindMajorState NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        sdpBindTlsStpPortState,
        tmnxOldSdpBindTlsStpPortState
    }
    STATUS          current
    DESCRIPTION     "The tmnxSvcTopoChgSdpBindMajorState  notification   is
                     generated  when  a  SDP  binding  has transitioned its
                     state  from  learning to forwarding or from forwarding
                     to  blocking or broken. The spanning tree topology has
                     been  modified.  It may denote loss of customer access
                     or  redundancy.  Check  the  new  topology against the
                     provisioned  topology  to  determine  the  severity of
                     connectivity loss."
    ::= { tstpTraps 14 }

tmnxSvcNewRootSdpBind NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        svcTlsStpDesignatedRoot
    }
    STATUS          current
    DESCRIPTION     "The  tmnxSvcNewRootSdpBind notification  is  generated
                     when  the previous root bridge has been aged out and a
                     new  root bridge has been elected. The new root bridge
                     creates  a  new  spanning tree topology. It may denote
                     loss  of  customer access or redundancy. Check the new
                     topology against the provisioned topology to determine
                     the severity of connectivity loss."
    ::= { tstpTraps 15 }

tmnxSvcTopoChgSdpBindState NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        sdpBindTlsStpPortState,
        tmnxOldSdpBindTlsStpPortState
    }
    STATUS          current
    DESCRIPTION     "The    tmnxSvcTopoChgSdpBindState    notification   is
                     generated when a SDP binding has transitioned state to
                     blocking  or  broken  from  learning state. This event
                     complements     what     is     not     covered     by
                     tmnxSvcTopoChgSdpBindMajorState.   The  spanning  tree
                     topology  has  been  modified.  It  may denote loss of
                     customer  access or redundancy. Check the new topology
                     against  the  provisioned  topology  to  determine the
                     severity of connectivity loss."
    ::= { tstpTraps 16 }

tmnxSvcSdpBindRcvdTCN NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId
    }
    STATUS          current
    DESCRIPTION     "The tmnxSvcSdpBindRcvdTCN  notification  is  generated
                     when  a  SDP  binding  has  received  TCN from another
                     bridge.  This  bridge  will either have its configured
                     BPDU with the topology change flag set if it is a root
                     bridge,  or  it  will  pass  TCN  to  its root bridge.
                     Eventually  the address aging timer for the forwarding
                     database  will  be  made shorter for a short period of
                     time. No recovery is needed."
    ::= { tstpTraps 17 }

tmnxSvcSdpBindRcvdHigherBriPrio NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        tmnxCustomerBridgeId,
        tmnxCustomerRootBridgeId
    }
    STATUS          current
    DESCRIPTION     "The tmnxSvcSdpBindRcvdHigherBriPrio  notification   is
                     generated when a customer's device has been configured
                     with  a bridge priority equal to zero. The SDP binding
                     that  the  customer's device is connected through will
                     be   blocked.   Remove   the   customer's   device  or
                     reconfigure  the customer's bridge priority with value
                     greater than zero."
    ::= { tstpTraps 18 }

tmnxSvcSdpBindEncapPVST NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        tmnxOtherBridgeId
    }
    STATUS          current
    DESCRIPTION     "The tmnxSvcSdpBindEncapPVST notification is  generated
                     when an SDP bindings STP received a BPDU that was PVST
                     encapsulated. The SDP binding STP's BPDUs will be PVST
                     encapsulated. No recovery is needed."
    ::= { tstpTraps 19 }

tmnxSvcSdpBindEncapDot1d NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        tmnxOtherBridgeId
    }
    STATUS          current
    DESCRIPTION     "The tmnxSvcSdpBindEncapDot1d notification is generated
                     when  a  SDP  binding  received a BPDU that was 802.1d
                     encapsulated.  The  SDP  binding  BPDUs  will  also be
                     802.1d encapsulated. No recovery is needed."
    ::= { tstpTraps 20 }

tmnxSvcSdpActiveProtocolChange NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        sdpBindTlsStpOperProtocol
    }
    STATUS          current
    DESCRIPTION     "The tmnxSvcSdpActiveProtocolChange  notification    is
                     generated  when the spanning tree protocol on this SDP
                     changes from rstp to stp or vise versa. No recovery is
                     needed."
    ::= { tstpTraps 31 }

tmnxStpMeshNotInMstRegion NOTIFICATION-TYPE
    OBJECTS {
        svcId,
        sdpBindId
    }
    STATUS          current
    DESCRIPTION     "The tmnxStpMeshNotInMstRegion notification is
                     generated when a MSTP BPDU from outside the MST region
                     is received on the indicated mesh SDP.

                     It is up to the operator to make sure bridges connected
                     via mesh SDPs are in the same MST-region. If not the mesh
                     will NOT become operational."
    ::= { tstpTraps 36 }

tmnxSdpBndStpExcepCondStateChng NOTIFICATION-TYPE
    OBJECTS {
        custId,
        svcId,
        sdpBindId,
        sdpBindTlsStpException
    }
    STATUS          current
    DESCRIPTION     "The tmnxSdpBndStpExcepCondStateChng notification is
                     generated when the value of the object sdpBindTlsStpException
                     has changed, i.e. when the exception condition 
                     changes on the indicated SDP Bind."
    ::= { tstpTraps 38 }

-- ----------------------------------------------------------------------------
-- Conformance Information
-- ----------------------------------------------------------------------------
tmnxSdpCompliances  OBJECT IDENTIFIER ::= { tmnxSdpConformance 1 }
tmnxSdpGroups       OBJECT IDENTIFIER ::= { tmnxSdpConformance 2 }

-- ----------------------------------------------
-- Compliance Statements
-- ----------------------------------------------

tmnxSdp77x0V6v0Compliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for management of services SDPs
         on Alcatel 7750 SR and 7710 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS
        {
             tmnxSdpV6v0Group,
             tmnxSdpBindV6v0Group,
             tmnxSdpBindTlsV6v0Group,
             tmnxSdpBindMeshV6v0Group,
             tmnxSdpApipeV6v0Group,
             tmnxSdpBindDhcpV6v0Group,
             tmnxSdpBindIpipeV6v0Group,
             tmnxSdpBindTlsL2ptV6v0Group,
             tmnxSdpAutoBindV6v0Group,
             tmnxSdpBindTlsMrpV6v0Group,
             tmnxSdpTlsBgpV6v0Group,
             tmnxSdpNotifyV6v0Group,
             tmnxSdpL2V6v0Group,
             tmnxSdpFCV6v0Group,
             tmnxSdpBindCpipeV6v0Group
        }
    ::= { tmnxSdpCompliances 8 }
             
tmnxSdp7450V6v0Compliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for management of services SDPs
         on Alcatel 7450 ESS series systems."
    MODULE  -- this module
        MANDATORY-GROUPS
        {
             tmnxSdpV6v0Group,
             tmnxSdpBindV6v0Group,
             tmnxSdpBindTlsV6v0Group,
             tmnxSdpBindMeshV6v0Group,
             -- tmnxSdpApipeV6v0Group,
             tmnxSdpBindDhcpV6v0Group,
             tmnxSdpBindIpipeV6v0Group,
             tmnxSdpBindTlsL2ptV6v0Group,
             tmnxSdpAutoBindV6v0Group,
             tmnxSdpBindTlsMrpV6v0Group,
             tmnxSdpTlsBgpV6v0Group,
             tmnxSdpNotifyV6v0Group,
             tmnxSdpL2V6v0Group,
             tmnxSdpFCV6v0Group
             -- tmnxSdpBindCpipeV6v0Group
        }
    ::= { tmnxSdpCompliances 9 }

-- Object groups

tmnxSdpV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpNumEntries,
      sdpNextFreeId,
      sdpId,
      sdpRowStatus,
      sdpDelivery,
      sdpFarEndIpAddress,
      sdpLspList,
      sdpDescription,
      sdpLabelSignaling,
      sdpAdminStatus,
      sdpOperStatus,
      sdpOperPathMtu,
      sdpKeepAliveAdminStatus,
      sdpKeepAliveOperStatus,
      sdpKeepAliveHelloTime,
      sdpKeepAliveMaxDropCount,
      sdpKeepAliveHoldDownTime,
      sdpLastMgmtChange,
      sdpKeepAliveNumHelloRequestMessages,
      sdpKeepAliveNumHelloResponseMessages,
      sdpKeepAliveNumLateHelloResponseMessages,
      sdpKeepAliveHelloRequestTimeout,
      sdpLdpEnabled,
      sdpVlanVcEtype,
      sdpAdvertisedVllMtuOverride,
      sdpOperFlags,
      sdpLastStatusChange,
      sdpMvplsMgmtService,
      sdpMvplsMgmtSdpBndId,
      sdpCollectAcctStats,
      sdpAccountingPolicyId,
      sdpClassFwdingEnabled,
      sdpClassFwdingDefaultLsp,
      sdpClassFwdingMcLsp,
      sdpMetric,
      sdpAutoSdp,
      sdpSnmpAllowed,
      sdpPBBEtype,
      sdpBandwidthBookingFactor,
      sdpOperBandwidth,
      sdpAvailableBandwidth,
      sdpAdminPathMtu,
      sdpKeepAliveHelloMessageLength
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP base feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 100 }

tmnxSdpBindV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindId,
      sdpBindRowStatus,
      sdpBindAdminStatus,
      sdpBindOperStatus,
      sdpBindLastMgmtChange,
      sdpBindType,
      sdpBindIngressMacFilterId,
      sdpBindIngressIpFilterId,
      sdpBindEgressMacFilterId,
      sdpBindEgressIpFilterId,
      sdpBindVpnId,
      sdpBindCustId,
      sdpBindVcType,
      sdpBindVlanVcTag,
      sdpBindSplitHorizonGrp,
      sdpBindOperFlags,
      sdpBindLastStatusChange,
      sdpBindIesIfIndex,
      sdpBindMacPinning,
      sdpBindIngressIpv6FilterId,
      sdpBindEgressIpv6FilterId,
      sdpBindCollectAcctStats,
      sdpBindAccountingPolicyId,
      sdpBindPwPeerStatusBits,
      sdpBindPeerVccvCvBits,
      sdpBindPeerVccvCcBits,
      sdpBindControlWordBit,
      sdpBindOperControlWord,
      sdpBindEndPoint,
      sdpBindEndPointPrecedence,
      sdpBindIsICB,
      sdpBindPwFaultInetAddressType,
      sdpBindClassFwdingOperState,
      sdpBindForceVlanVcForwarding,
      sdpBindAdminBandwidth,
      sdpBindOperBandwidth,
      sdpBindBaseStatsIngressForwardedPackets,
      sdpBindBaseStatsIngressDroppedPackets,
      sdpBindBaseStatsEgressForwardedPackets,
      sdpBindBaseStatsEgressForwardedOctets,
      sdpBindBaseStatsCustId,
      sdpBindBaseStatsIngFwdOctets,
      sdpBindBaseStatsIngDropOctets,
      sdpBindAdminIngressLabel,
      sdpBindAdminEgressLabel,
      sdpBindOperIngressLabel,
      sdpBindOperEgressLabel,
      sdpBindPwFaultInetAddress,
      sdpBindIpipeCeInetAddress
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP Bind feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 101 }

tmnxSdpBindTlsV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindTlsStpAdminStatus,
      sdpBindTlsStpPriority,
      sdpBindTlsStpPortNum,
      sdpBindTlsStpPathCost,
      sdpBindTlsStpRapidStart,
      sdpBindTlsStpBpduEncap,
      sdpBindTlsStpPortState,
      sdpBindTlsStpDesignatedBridge,
      sdpBindTlsStpDesignatedPort,
      sdpBindTlsStpForwardTransitions,
      sdpBindTlsStpInConfigBpdus,
      sdpBindTlsStpInTcnBpdus,
      sdpBindTlsStpInBadBpdus,
      sdpBindTlsStpOutConfigBpdus,
      sdpBindTlsStpOutTcnBpdus,
      sdpBindTlsStpOperBpduEncap,
      sdpBindTlsStpVpnId,
      sdpBindTlsStpCustId,
      sdpBindTlsMacAddressLimit,
      sdpBindTlsNumMacAddresses,
      sdpBindTlsNumStaticMacAddresses,
      sdpBindTlsMacLearning,
      sdpBindTlsMacAgeing,
      sdpBindTlsStpOperEdge,
      sdpBindTlsStpAdminPointToPoint,
      sdpBindTlsStpPortRole,
      sdpBindTlsStpAutoEdge,
      sdpBindTlsStpOperProtocol,
      sdpBindTlsStpInRstBpdus,
      sdpBindTlsStpOutRstBpdus,
      sdpBindTlsLimitMacMove,
      sdpBindTlsDiscardUnknownSource,
      sdpBindTlsMvplsPruneState,
      sdpBindTlsMvplsMgmtService,
      sdpBindTlsMvplsMgmtSdpBndId,
      sdpBindTlsStpException,
      sdpBindTlsL2ptTermination,
      sdpBindTlsBpduTranslation,
      sdpBindTlsStpRootGuard,
      sdpBindTlsStpInMstBpdus,
      sdpBindTlsStpOutMstBpdus,
      sdpBindTlsStpRxdDesigBridge,
      sdpBindTlsMacMoveNextUpTime,
      sdpBindTlsMacMoveRateExcdLeft,
      sdpBindTlsLimitMacMoveLevel,
      sdpBindTlsBpduTransOper,
      sdpBindTlsL2ptProtocols,
      sdpBindTlsIgnoreStandbySig,
      sdpBindTlsBlockOnMeshFail
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP Bind TLS feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 102 }

tmnxSdpBindMeshV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindMeshTlsPortState,
      sdpBindMeshTlsNotInMstRegion,
      sdpBindMeshTlsHoldDownTimer,
      sdpBindMeshTlsTransitionState
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting mesh SDP bind feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 103 }

tmnxSdpApipeV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindApipeAdminConcatCellCount,
      sdpBindApipeSigConcatCellCount,
      sdpBindApipeOperConcatCellCount,
      sdpBindApipeConcatMaxDelay,
      sdpBindApipeConcatCellClp,
      sdpBindApipeConcatCellAal5Fr
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP A-Pipe feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 104 }

tmnxSdpBindDhcpV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindDhcpDescription,
      sdpBindDhcpSnoop,
      sdpBindDhcpStatsClntSnoopdPckts,
      sdpBindDhcpStatsSrvrSnoopdPckts,
      sdpBindDhcpStatsClntForwdPckts,
      sdpBindDhcpStatsSrvrForwdPckts,
      sdpBindDhcpStatsClntDropdPckts,
      sdpBindDhcpStatsSrvrDropdPckts,
      sdpBindDhcpStatsClntProxRadPckts,
      sdpBindDhcpStatsClntProxLSPckts,
      sdpBindDhcpStatsGenReleasePckts,
      sdpBindDhcpStatsGenForceRenPckts
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP Bind DHCP feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 105 }

tmnxSdpBindIpipeV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindIpipeCeInetAddressType
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP Bind I-Pipe feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 106 }

tmnxSdpFCV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpFCMappingRowStatus,
      sdpFCMappingLspId
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP FC feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 107 }

tmnxSdpBindCpipeV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindCpipeLocalPayloadSize,
      sdpBindCpipePeerPayloadSize,
      sdpBindCpipeLocalBitrate,
      sdpBindCpipePeerBitrate,
      sdpBindCpipeLocalSigPkts,
      sdpBindCpipePeerSigPkts,
      sdpBindCpipeLocalCasTrunkFraming,
      sdpBindCpipePeerCasTrunkFraming,
      sdpBindCpipeLocalUseRtpHeader,
      sdpBindCpipePeerUseRtpHeader,
      sdpBindCpipeLocalDifferential,
      sdpBindCpipePeerDifferential,
      sdpBindCpipeLocalTimestampFreq,
      sdpBindCpipePeerTimestampFreq
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP bind C-Pipe feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 108 }

tmnxSdpBindTlsL2ptV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindTlsMfibMdaRowStatus,
      sdpBindTlsL2ptStatsLastClearedTime,
      sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapStpConfigBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapStpRstBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapStpTcnBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapPvstConfigBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapPvstRstBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapPvstTcnBpdusTx,
      sdpBindTlsL2ptStatsStpConfigBpdusRx,
      sdpBindTlsL2ptStatsStpConfigBpdusTx,
      sdpBindTlsL2ptStatsStpRstBpdusRx,
      sdpBindTlsL2ptStatsStpRstBpdusTx,
      sdpBindTlsL2ptStatsStpTcnBpdusRx,
      sdpBindTlsL2ptStatsStpTcnBpdusTx,
      sdpBindTlsL2ptStatsPvstConfigBpdusRx,
      sdpBindTlsL2ptStatsPvstConfigBpdusTx,
      sdpBindTlsL2ptStatsPvstRstBpdusRx,
      sdpBindTlsL2ptStatsPvstRstBpdusTx,
      sdpBindTlsL2ptStatsPvstTcnBpdusRx,
      sdpBindTlsL2ptStatsPvstTcnBpdusTx,
      sdpBindTlsL2ptStatsOtherBpdusRx,
      sdpBindTlsL2ptStatsOtherBpdusTx,
      sdpBindTlsL2ptStatsOtherL2ptBpdusRx,
      sdpBindTlsL2ptStatsOtherL2ptBpdusTx,
      sdpBindTlsL2ptStatsOtherInvalidBpdusRx,
      sdpBindTlsL2ptStatsOtherInvalidBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapCdpBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapCdpBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapVtpBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapVtpBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapDtpBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapDtpBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapPagpBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapPagpBpdusTx,
      sdpBindTlsL2ptStatsL2ptEncapUdldBpdusRx,
      sdpBindTlsL2ptStatsL2ptEncapUdldBpdusTx,
      sdpBindTlsL2ptStatsCdpBpdusRx,
      sdpBindTlsL2ptStatsCdpBpdusTx,
      sdpBindTlsL2ptStatsVtpBpdusRx,
      sdpBindTlsL2ptStatsVtpBpdusTx,
      sdpBindTlsL2ptStatsDtpBpdusRx,
      sdpBindTlsL2ptStatsDtpBpdusTx,
      sdpBindTlsL2ptStatsPagpBpdusRx,
      sdpBindTlsL2ptStatsPagpBpdusTx,
      sdpBindTlsL2ptStatsUdldBpdusRx,
      sdpBindTlsL2ptStatsUdldBpdusTx
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP bind L2pt feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 109 }
      
tmnxSdpAutoBindV6v0Group OBJECT-GROUP
    OBJECTS {
      pwTemplateTableLastChanged,
      pwTemplateRowStatus,
      pwTemplateLastChanged,
      pwTemplateVcType,
      pwTemplateAccountingPolicyId,
      pwTemplateCollectAcctStats,
      pwTemplateMacLearning,
      pwTemplateMacAgeing,
      pwTemplateDiscardUnknownSource,
      pwTemplateLimitMacMove,
      pwTemplateMacPinning,
      pwTemplateMacAddressLimit,
      pwTemplateShgName,
      pwTemplateShgDescription,
      pwTemplateShgRestProtSrcMac,
      pwTemplateShgRestUnprotDstMac,
      pwTemplateEgressMacFilterId,
      pwTemplateEgressIpFilterId,
      pwTemplateEgressIpv6FilterId,
      pwTemplateIngressMacFilterId,
      pwTemplateIngressIpFilterId,
      pwTemplateIngressIpv6FilterId,
      pwTemplateIgmpFastLeave,
      pwTemplateIgmpImportPlcy,
      pwTemplateIgmpLastMembIntvl,
      pwTemplateIgmpMaxNbrGrps,
      pwTemplateIgmpGenQueryIntvl,
      pwTemplateIgmpQueryRespIntvl,
      pwTemplateIgmpRobustCount,
      pwTemplateIgmpSendQueries,
      pwTemplateIgmpMcacPolicyName,
      pwTemplateIgmpMcacPrRsvMndBW,
      pwTemplateIgmpMcacUnconstBW,
      pwTemplateIgmpVersion,
      pwTemplateIgmpSnpgGrpSrcTblLC,
      pwTemplateIgmpSnpgRowStatus,
      pwTemplateIgmpSnpgLastChngd,
      pwTemplateMfibAllowedMdaTblLC,
      pwTemplateMfibMdaRowStatus,
      pwTemplateUseProvisionedSdp,
      pwTemplateVlanVcTag,
      sdpAutoBindBgpInfoTableLC,
      sdpAutoBindBgpInfoTemplateId,
      sdpAutoBindBgpInfoAGI,
      sdpAutoBindBgpInfoSAII,
      sdpAutoBindBgpInfoTAII
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP auto-bind feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 112 }

tmnxSdpBindTlsMrpV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpBindTlsMrpTableLastChanged,
      sdpBindTlsMrpLastChngd,
      sdpBindTlsMrpJoinTime,
      sdpBindTlsMrpLeaveTime,
      sdpBindTlsMrpLeaveAllTime,
      sdpBindTlsMrpPeriodicTime,
      sdpBindTlsMrpPeriodicEnabled,
      sdpBindTlsMrpRxPdus,
      sdpBindTlsMrpDroppedPdus,
      sdpBindTlsMrpTxPdus,
      sdpBindTlsMrpRxNewEvent,
      sdpBindTlsMrpRxJoinInEvent,
      sdpBindTlsMrpRxInEvent,
      sdpBindTlsMrpRxJoinEmptyEvent,
      sdpBindTlsMrpRxEmptyEvent,
      sdpBindTlsMrpRxLeaveEvent,
      sdpBindTlsMrpTxNewEvent,
      sdpBindTlsMrpTxJoinInEvent,
      sdpBindTlsMrpTxInEvent,
      sdpBindTlsMrpTxJoinEmptyEvent,
      sdpBindTlsMrpTxEmptyEvent,
      sdpBindTlsMrpTxLeaveEvent,
      sdpBindTlsMmrpDeclared,
      sdpBindTlsMmrpRegistered
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP MRP feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 113 }

tmnxSdpTlsBgpV6v0Group OBJECT-GROUP
    OBJECTS {
      svcTlsBgpADPWTempBindTblLC,
      svcTlsBgpADPWTempBindRowStatus,
      svcTlsBgpADPWTempBindLastChngd,
      svcTlsBgpADPWTempBindSHG,
      svcTlsBgpADPWTempBindRTTblLC,
      svcTlsBgpADPWTempBindRTRowStat
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP BGP feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 114 }

tmnxSdpL2V6v0Group OBJECT-GROUP
    OBJECTS {
      sdpCreationOrigin,
      svcL2RteTableLastChanged,
      svcL2RteSdpBindId,
      svcL2RtePwTemplateId
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP L2 Route feature
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 115 }

-- Notification objects
tmnxSdpNotifyObjsV6v0Group OBJECT-GROUP
    OBJECTS {
      sdpNotifySdpId,
      sdpMaxBookableBandwidth,
      sdpBookedBandwidth,
      dynamicSdpStatus,
      dynamicSdpOrigin,
      dynamicSdpCreationError,
      dynamicSdpBindCreationError
    }
    STATUS      current
    DESCRIPTION
        "The  group  of objects supporting SDP notification objects
         on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 200 }

-- Obsoleted Group (300) 

-- Notification group
tmnxSdpNotifyV6v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {
      unacknowledgedTCN,
      tmnxSvcTopoChgSdpBindMajorState,
      tmnxSvcNewRootSdpBind,
      tmnxSvcTopoChgSdpBindState,
      tmnxSvcSdpBindRcvdTCN,
      tmnxSvcSdpBindRcvdHigherBriPrio,
      tmnxSvcSdpBindEncapPVST,
      tmnxSvcSdpBindEncapDot1d,
      tmnxSvcSdpActiveProtocolChange,
      tmnxStpMeshNotInMstRegion,
      tmnxSdpBndStpExcepCondStateChng,
      sdpStatusChanged,
      sdpBindStatusChanged,
      sdpTlsMacAddrLimitAlarmRaised,
      sdpTlsMacAddrLimitAlarmCleared,
      sdpBindDHCPLeaseEntriesExceeded,
      sdpBindDHCPLseStateOverride,
      sdpBindDHCPLseStatePopulateErr,
      sdpBindDHCPSuspiciousPcktRcvd,
      sdpBindPwPeerStatusBitsChanged,
      sdpBindTlsMacMoveExceeded,
      sdpBindPwPeerFaultAddrChanged,
      sdpBindDHCPProxyServerError,
      sdpBindSdpStateChangeProcessed,
      sdpBindDHCPLseStateMobilityErr,
      sdpBandwidthOverbooked,
      sdpBindInsufficientBandwidth,
      dynamicSdpConfigChanged,
      dynamicSdpBindConfigChanged,
      dynamicSdpCreationFailed,
      dynamicSdpBindCreationFailed
    }
    STATUS      current
    DESCRIPTION
        "The  group  of SDP notifications on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 400 }

tmnxSdpObsoletedNotifyV6v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {
      sdpCreated,
      sdpDeleted,
      sdpBindCreated,
      sdpBindDeleted,
      sdpTlsDHCPSuspiciousPcktRcvd,
      sdpBindDHCPCoAError,
      sdpBindDHCPSubAuthError
      }
    STATUS      current
    DESCRIPTION
        "The  group  of obsoleted SDP objects on Alcatel 7x50 SR series systems."
    ::= { tmnxSdpGroups 401 }

END
