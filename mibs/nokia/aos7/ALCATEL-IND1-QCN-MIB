ALCATEL-IND1-QCN-MIB DEFINITIONS ::= BEGIN

IMPORTS
    Integer32,
    MODULE-IDENTITY,
    OBJECT-TYPE,
    OBJECT-IDENTITY,
    Unsigned32
        FROM SNMPv2-SMI
    TruthValue
        FROM SNMPv2-TC
    OBJECT-GROUP,
    MODULE-COMPLIANCE
        FROM SNMPv2-CONF
    softentIND1QcnMib
        FROM ALCATEL-IND1-BASE;

alcatelIND1QcnMIB MODULE-IDENTITY
    LAST-UPDATED "201109010000Z"
    ORGANIZATION "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:

                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America

         Telephone:              North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507

         Electronic Mail:         <EMAIL>
             World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
             File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
        Network Management Protocol (SNMP) Management Information Base (MIB):

        For the Birds Of Prey Product Line
        System Capability Manager, to allow for system control and limitation setting, of
        different, features through out the system.
        Capability manager is a centralized process which provides hardware information and
        capability to other processes. To optimize the system performance , certain features
        may be configured to a lower than the hardware limit through capability manager.

        The right to make changes in specification and other information
        contained in this document without prior notice is reserved.

        No liability shall be assumed for any incidental, indirect, special, or
        consequential damages whatsoever arising from or related to this
        document or the information contained herein.

        Vendors, end-users, and other interested parties are granted
        non-exclusive license to use this specification in connection with
        management of the products for which it is intended to be used.

                     Copyright (C) 1995-2011 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "201109010000Z"
    DESCRIPTION
        "Capability Manager is used to set system wide limitation."
    ::= { softentIND1QcnMib 1 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

    alcatelIND1QcnMIBObjects OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Branch For QCN Subsystem Managed Objects."
    ::= { alcatelIND1QcnMIB 1 }

    alcatelIND1QcnMIBConformance OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Branch For QCN Subsystem Conformance Information."
    ::= { alcatelIND1QcnMIB 2 }

    alcatelIND1QcnMIBGroups OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Branch For QCN Subsystem Units Of Conformance."
    ::= { alcatelIND1QcnMIBConformance 1 }

    alcatelIND1QcnMIBCompliances OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Branch For QCN Subsystem Compliance Statements."
    ::= { alcatelIND1QcnMIBConformance 2 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

    alaQcnConfig  OBJECT IDENTIFIER ::= { alcatelIND1QcnMIBObjects 1 }
    alaQcnConformance  OBJECT IDENTIFIER ::= { alcatelIND1QcnMIBObjects 2 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

-- xxxxxxxxxxxxxxxxxxxx
-- QCN Global Controls
-- xxxxxxxxxxxxxxxxxxxx

alaQcnGlobalTable  OBJECT-TYPE
    SYNTAX  SEQUENCE OF AlaQcnGlobalEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "A table of QCN global configuration."
    ::= { alaQcnConfig 1 }

alaQcnGlobalEntry  OBJECT-TYPE
    SYNTAX  AlaQcnGlobalEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "A QCN global Entry."
    INDEX { alaQcnGlobalCompId }
    ::= { alaQcnGlobalTable 1 }

AlaQcnGlobalEntry ::= SEQUENCE {
    alaQcnGlobalCompId
        Integer32,
    alaQcnGlobalCNMVlanTag
        Integer32,
    alaQcnGlobalCID
        Integer32
    }

alaQcnGlobalCompId  OBJECT-TYPE
    SYNTAX  Integer32 (1)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Comp Id is the index to this table."
    ::= { alaQcnGlobalEntry 1 }

alaQcnGlobalCNMVlanTag OBJECT-TYPE
    SYNTAX        Integer32 (0..4094)
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION 
        "When set, the vlan tag overrides the incoming vlan id and forces
         CNMs to be transmitted in that vlan."
    DEFVAL         { 0 }
    ::= { alaQcnGlobalEntry 2 }

alaQcnGlobalCID OBJECT-TYPE
    SYNTAX        Integer32 (0..16777215)
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION 
        "Sets the Congestion Point ID sent as part of the Congestion
         Notification Message."
    DEFVAL         { 0 }
    ::= { alaQcnGlobalEntry 3 }

-- xxxxxxxxxxxxxxxxxxxxxxxx
-- QCN Port Instance Table
-- xxxxxxxxxxxxxxxxxxxxxxxx

alaQcnPortInstanceTable  OBJECT-TYPE
    SYNTAX  SEQUENCE OF AlaQcnPortInstanceEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "A table of DCP Port Instances."
    ::= { alaQcnConfig 2 }

alaQcnPortInstanceEntry  OBJECT-TYPE
    SYNTAX  AlaQcnPortInstanceEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "A DCP Port Instance Entry."
    INDEX { alaQcnPIIfIndex, alaQcnPIPriority }
    ::= { alaQcnPortInstanceTable 1 }

AlaQcnPortInstanceEntry ::= SEQUENCE {
    alaQcnPIIfIndex
        Unsigned32,
    alaQcnPIPriority
        Unsigned32,
    alaQcnPIPriorityReset
        TruthValue,
    alaQcnPICncpStatsClear
        TruthValue,
    alaQcnPICncpReset
        TruthValue
    }

alaQcnPIIfIndex  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "IfIndex of this port."
    ::= { alaQcnPortInstanceEntry 1 }

alaQcnPIPriority  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Priority identifier of this port."
    ::= { alaQcnPortInstanceEntry 2 }

alaQcnPIPriorityReset OBJECT-TYPE
    SYNTAX        TruthValue { true(1) }
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Set to true to reset the priority configuration to the global default."
    ::= { alaQcnPortInstanceEntry 3 }

alaQcnPICncpStatsClear OBJECT-TYPE
    SYNTAX        TruthValue { true(1) }
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Set to true to clear the cncp statistics for this port
         priority.  Will do nothing if stats are disabled."
    ::= { alaQcnPortInstanceEntry 4 }

alaQcnPICncpReset OBJECT-TYPE
    SYNTAX        TruthValue { true(1) }
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Set to true to reset the cncp configuration to the global default for
         this port priority."
    ::= { alaQcnPortInstanceEntry 5 }

-- -------------------------------------------------------------
-- Compliance Statements
-- -------------------------------------------------------------

alcatelIND1QcnMIBCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "Compliance statement for QCN Subsystem."
    MODULE  -- this module
        MANDATORY-GROUPS
        {
            alaQcnGlobalGroup,
            alaQcnPortInstanceGroup
        }
    ::= { alcatelIND1QcnMIBCompliances 1 }

-- -------------------------------------------------------------
-- Units Of Conformance
-- -------------------------------------------------------------

alaQcnGlobalGroup OBJECT-GROUP
    OBJECTS
    {
        alaQcnGlobalCNMVlanTag,
        alaQcnGlobalCID
    }
    STATUS current
    DESCRIPTION
        "Collection of Global QCN Configuration for management of QCN."
    ::= { alcatelIND1QcnMIBGroups 1 }

alaQcnPortInstanceGroup OBJECT-GROUP
    OBJECTS
    {
        alaQcnPIPriorityReset,
        alaQcnPICncpStatsClear,
        alaQcnPICncpReset
    }
    STATUS current
    DESCRIPTION
        "Collection of QCN Port level Configuration for management of QCN."
    ::= { alcatelIND1QcnMIBGroups 2 }

END
