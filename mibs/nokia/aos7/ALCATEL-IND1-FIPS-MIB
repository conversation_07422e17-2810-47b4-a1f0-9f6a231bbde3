ALCATEL-IND1-FIPS-MIB DEFINITIONS ::= BEGIN

IMPORTS
        OBJECT-TYPE,
        OBJECT-IDENTITY,
        MODULE-IDENTITY,
        NOTIFICATION-TYPE,
	Unsigned32,
	Counter32,
        Integer32                       FROM SNMPv2-<PERSON><PERSON>
        ifIndex,                       
        InterfaceIndex,
        InterfaceIndexOrZero           FROM IF-<PERSON><PERSON>,
        RowStatus,
        TimeStamp,
	DateAndTime,
        TEXTUAL-CONVENTION              FROM SNMPv2-TC
        InetAddressType,
        InetAddress                     FROM INET-ADDRESS-MIB 
        SnmpAdminString                 FROM SNMP-FRAMEWORK-MIB
        MODULE-COMPLIANCE,
        OBJECT-GRO<PERSON>,
        NOTIFICATION-GROUP              FROM SNMPv2-CONF
        MultiChassisId                  FROM ALCATEL-IND1-MULTI-CHASSIS-MIB
        softentIND1Fips                 FROM ALCATEL-IND1-BASE
;


alcatelIND1FipsMIB MODULE-IDENTITY
    LAST-UPDATED "201210160000Z"
    ORGANIZATION "Alcatel-Lucent, Enterprise Solutions Division"
    CONTACT-INFO
     "Please consult with Customer Service to ensure the most appropriate
      version of this document is used with the products in question:

                 Alcatel-Lucent, Enterprise Solutions Division
                (Formerly Alcatel Internetworking, Incorporated)
                        26801 West Agoura Road
                     Agoura Hills, CA  91301-5122
                       United States Of America

     Telephone:               North America  ****** 995 2696
                              Latin America  ****** 919 9526
                              Europe         +31 23 556 0100
                              Asia           +65 394 7933
                              All Other      ****** 878 4507

     Electronic Mail:         <EMAIL>
     World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
     File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"
    DESCRIPTION
              "This module describes an authoritative enterprise-specific Simple
        Network Management Protocol (SNMP) Management Information Base (MIB):

        For the Birds Of Prey Product Line, this is the MIB module for
              address learning mac addresses entity.

        The right to make changes in specification and other information
        contained in this document without prior notice is reserved.

        No liability shall be assumed for any incidental, indirect, special, or
        consequential damages whatsoever arising from or related to this
        document or the information contained herein.

        Vendors, end-users, and other interested parties are granted
        non-exclusive license to use this specification in connection with
        management of the products for which it is intended to be used.

                   Copyright (C) 1995-2007 Alcatel-Lucent
                       ALL RIGHTS RESERVED WORLDWIDE"

    ::= { softentIND1Fips 1}

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

-- Textual Conventions

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

FipsFCMAP ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "1x:"
    STATUS       current
    DESCRIPTION
            "In a Fabric Provided MAC Address, the required value
            for the upper 24 bits of a MAC address assigned to a
            VN_Port. - FC-BB-5-09-056v5.pdf; Section 3.5.5"
    SYNTAX       OCTET STRING (SIZE (3))

Fcid ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "1x:"
    STATUS       current
    DESCRIPTION
            "A 3 byte N_PORT_ID obtained when a Fibre Channel port successfully login to a Fabric."
    SYNTAX       OCTET STRING (SIZE (3))

Wwpn ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "1x:"
    STATUS       current
    DESCRIPTION
            "An 8 byte name assigned to each Fibre Channel ports.  First 2 bytes are 1000H and remainng 6 bytes are MAC address of the port."
    SYNTAX       OCTET STRING (SIZE (8))

Wwnn ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "1x:"
    STATUS       current
    DESCRIPTION
            "An 8 byte name assigned to the switch.  First 2 bytes are 1000H and remainng 6 bytes are switch MAC address of AOS."
    SYNTAX       OCTET STRING (SIZE (8))


-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- Hook into the Alcatel Tree
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

    alcatelIND1FipsMIBNotifications OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For FIP Snooping Module MIB Subsystem Notifications."
        ::= { alcatelIND1FipsMIB 0 }

    alcatelIND1FipsMIBObjects OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch For FIP Snooping Module MIB Subsystem Managed Objects."
        ::= { alcatelIND1FipsMIB 1 }

    alcatelIND1FipsMIBConformance OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch for FIP Snooping Module MIB Subsystem Conformance Information."
        ::= { alcatelIND1FipsMIB 2 }

    alcatelIND1FipsMIBGroups OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch for FIP Snooping Module MIB Subsystem Units of Conformance."
        ::= { alcatelIND1FipsMIBConformance 1 }

    alcatelIND1FipsMIBCompliances OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch for FIP Snooping Module MIB Subsystem Compliance Statements."
        ::= { alcatelIND1FipsMIBConformance 2 }

--
-- FIP Snooping Common Definitions
--

--
--alaFipsConfig
--
   alaFipsInfo OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 1 }
alaFipsConfigFilterResourceLimit  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 100)
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "Allowed maximum percentage of filter resources to be configured from the allocated FIPS resources."
	DEFVAL {80}
    ::= { alaFipsInfo 1 }


alaFipsConfigFIPSAdmin  OBJECT-TYPE
    SYNTAX  INTEGER
		{
			enable(1),
			disable(2)
		}
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
       "Fips FIP Snooping Admin State, With the Value set as Enable FIP Snooping is enabled in the System "
	DEFVAL {disable}
    ::= { alaFipsInfo 2 }

alaFipsConfigAddressMode  OBJECT-TYPE
    SYNTAX  INTEGER
		{
			fpma(1),
			spma(2)
		}
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
       "Fips Address Mode Supported on the Switch "
	DEFVAL {fpma}
    ::= { alaFipsInfo 3 }

alaFipsConfigPriorityOne  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 7)
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
       "First fips lossless Priority "
	DEFVAL {3}
    ::= { alaFipsInfo 4 }

alaFipsConfigPriorityTwo  OBJECT-TYPE
    SYNTAX  Integer32 (-1 .. 7)
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
       "Second fips lossless Priority.  Valid priorities are 0 - 7;  Lower numbers represent higher priority; 1 indicates that this object is not configured."
    ::= { alaFipsInfo 5 }

alaFipsTotalNumFilterResource  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
       "Fips Total Num of Available Filter Resources "
	DEFVAL {256}
    ::= { alaFipsInfo 6 }
	
alaFipsUsedNumFilterResource  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
       "Fips Total Num of Used Filter Resources "
	DEFVAL {0}
    ::= { alaFipsInfo 7}

alaFipsConfigStatsClear OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Global Fips Statistics Clear Object, 0 is Default Value 1 is is Clear all the Fips Statistics in the VLAN as well as Port Level"
    ::= { alaFipsInfo 8 }

alaFipsConfigPrioProtection OBJECT-TYPE
    SYNTAX  INTEGER
		{
			enable(1),
			disable(2)
		}
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "This enables/disables Fips priority protection globally, 'disable(0)' is Default Value."
	DEFVAL {disable}
    ::= { alaFipsInfo 9 }

alaFipsConfigPriorityProtectionAction OBJECT-TYPE
    SYNTAX  INTEGER
		{
			drop(1),
			remark(2)
		}
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "When Fips priority protection is enabled, the action on non-protected priorities can be 'drop' or 'remark'. Selecting 'drop', discards
         all frames with different priority and/or non-FCoE ethertype.  Selecting 'remark', changes the priority and forwards."
	DEFVAL {drop}
    ::= { alaFipsInfo 10 }

alaFipsConfigPriorityProtectionRemarkVal  OBJECT-TYPE
    SYNTAX  Integer32 (-1 .. 7)
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
       "When Fips priority protection is 'enabled' and configured as 'remark', this represents the remarked priority.
        Valid priorities are 0 - 7; Lower numbers represent higher priority; -1 indicates that this object is not configured."
    ::= { alaFipsInfo 11 }

alaFipsConfigHouseKeepingTimePeriod OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "This sets the time peroid (in seconds) for FIP Snooping house keeping activity."
	DEFVAL {300}
    ::= { alaFipsInfo 12 }

alaFipsConfigSWReinsertStatus  OBJECT-TYPE
    SYNTAX  INTEGER
		{
			enable(1),
			disable(2)
		}
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
       "Fips FIP Snooping Software Reinsert Status ,With the Value set as Enable FIP Control Packet will be forwarded from software instead of hardware "
	DEFVAL {enable}
    ::= { alaFipsInfo 13 }

alaFipsConfigSessClear OBJECT-TYPE
    SYNTAX  INTEGER
		{
			all(1),
			npiv(2),
			reverseNpiv(3),
			eTunnel(4),
			fips(5),
                        npivPending(6),
                        none(7)
		}
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Clear sessions on FCoE ports; clear - ALL sessions / only NPIV sessions / only R-NPIV sessions / only E-TUNNEL sessions / FIPS sessions. "
	DEFVAL {none}
    ::= { alaFipsInfo 14 }



--
-- alaFipsVlanTable
--
   alaFipsVlan OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 2 }
alaFipsVlanTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsVlanEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of Fips VLAN "
    ::= { alaFipsVlan 1}

 alaFipsVlanEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVlanEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips/FIPS global Entry."
    INDEX { alaFipsVlanId }
    ::= { alaFipsVlanTable 1 }

AlaFipsVlanEntry ::= SEQUENCE {
		alaFipsVlanId		Integer32,
		alaFipsVlanFCMap	FipsFCMAP,
		alaFipsVlanStatsClear	Unsigned32,
		alaFipsVlanStatsFnreClear  INTEGER
}

alaFipsVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled "
    ::= { alaFipsVlanEntry 1 }

alaFipsVlanFCMap  OBJECT-TYPE
    SYNTAX  FipsFCMAP
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "Fips FC-MAP on this VLAN.  Valid values are 0EFC00h to 0EFCFFh.  000000h is used to clear FC-MAP on this VLAN."
	DEFVAL {'000000'H}
    ::= { alaFipsVlanEntry 2 }
alaFipsVlanStatsClear OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "FCoE VLAN Statistics Clear Object, 0 is Default Value 1 is is Clear all the Fips Statistics in the VLAN"
    ::= { alaFipsVlanEntry 3}

alaFipsVlanStatsFnreClear OBJECT-TYPE
    SYNTAX  INTEGER
                {
                        all(1),
                        fips(2),
                        npiv(3),
                        reverseNpiv(4),
                        eTunnel(5),
                        none(6)
                }
    MAX-ACCESS read-create 
    STATUS  current
    DESCRIPTION
        "Global FCoE Statistics per feature Clear Object, 1 is Clear all the Fips Statistics at the VLAN Level"
    DEFVAL { none }
    ::= { alaFipsVlanEntry 4 }

--
-- alaFipsVlanEnodeStatsTable
--
   alaFipsVlanEnodeStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 3 }
alaFipsVlanEnodeStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsVlanEnodeStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of Fips VLAN Enode Statistics"
    ::= { alaFipsVlanEnodeStats 1}

 alaFipsVlanEnodeStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVlanEnodeStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per VLan/Enode Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFipsVlanEnodeStatsVlanId }
    ::= { alaFipsVlanEnodeStatsTable 1 }

AlaFipsVlanEnodeStatsEntry ::= SEQUENCE {
		alaFipsVlanEnodeStatsVlanId	Integer32,
		alaFipsVlanEnodeStatsSessions	Unsigned32,
		alaFipsVlanEnodeStatsMds	Counter32,
		alaFipsVlanEnodeStatsUds	Counter32,
		alaFipsVlanEnodeStatsFlogi  	Counter32,
		alaFipsVlanEnodeStatsFdisc	Counter32,
		alaFipsVlanEnodeStatsLogo	Counter32,
		alaFipsVlanEnodeStatsEka	Counter32,
		alaFipsVlanEnodeStatsVnka	Counter32,
		alaFipsVlanEnodeStatsClear	Unsigned32
}

alaFipsVlanEnodeStatsVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled "
    ::= { alaFipsVlanEnodeStatsEntry 1 }

alaFipsVlanEnodeStatsSessions  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FCoE Sessions on this Vlan Id"
    DEFVAL {0}
    ::= { alaFipsVlanEnodeStatsEntry 2 }

alaFipsVlanEnodeStatsMds  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Solicitation (MDS) Packets on this Vlan"
    ::= { alaFipsVlanEnodeStatsEntry 3 }
	
alaFipsVlanEnodeStatsUds  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Solicitation (MDS) Packets on this Vlan"
    ::= { alaFipsVlanEnodeStatsEntry 4 }

alaFipsVlanEnodeStatsFlogi  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FLOGI) Packets on this Vlan"
    ::= { alaFipsVlanEnodeStatsEntry 5 }

alaFipsVlanEnodeStatsFdisc  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Discovery (FDISC) Packets on this Vlan"
    ::= { alaFipsVlanEnodeStatsEntry 6 }
	
alaFipsVlanEnodeStatsLogo  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logouts (LOGO) Packets on this Vlan"
    ::= { alaFipsVlanEnodeStatsEntry 7 }	

alaFipsVlanEnodeStatsEka  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Enode-keep-alive Packets on this Vlan"
    ::= { alaFipsVlanEnodeStatsEntry 8 }	
	
alaFipsVlanEnodeStatsVnka  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  VNPort-keep-alive Packets on this Vlan"
    ::= { alaFipsVlanEnodeStatsEntry 9 }	

alaFipsVlanEnodeStatsClear OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "FCoE VLAN Statistics Clear Object, 0 is Default Value 1 is is Clear all the Fips Statistics in the VLAN"
    ::= { alaFipsVlanEnodeStatsEntry 10}

--
--alaFipsVlanFcfStatsTable
--
   alaFipsVlanFcfStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 4 }
alaFipsVlanFcfStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsVlanFcfStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of Fips VLAN FCF Statistics"
    ::= { alaFipsVlanFcfStats 1}

 alaFipsVlanFcfStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVlanFcfStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per VLan/FCF Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFipsVlanFcfStatsVlanId }
    ::= { alaFipsVlanFcfStatsTable 1 }

AlaFipsVlanFcfStatsEntry ::= SEQUENCE {
		alaFipsVlanFcfStatsVlanId	Integer32,
		alaFipsVlanFcfStatsSessions	Unsigned32,
		alaFipsVlanFcfStatsMda		Counter32,
		alaFipsVlanFcfStatsUda		Counter32,
		alaFipsVlanFcfStatsFlogiAcc	Counter32,
		alaFipsVlanFcfStatsFlogiRjt	Counter32,
		alaFipsVlanFcfStatsFdiscRjt	Counter32,
		alaFipsVlanFcfStatsLogoAcc	Counter32,
		alaFipsVlanFcfStatsLogoRjt	Counter32,
		alaFipsVlanFcfStatsCvl		Counter32,
		alaFipsVlanFcfStatsClear	Unsigned32,
		alaFipsVlanFcfStatsFdiscAcc	Counter32
}

alaFipsVlanFcfStatsVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled "
    ::= { alaFipsVlanFcfStatsEntry 1 }

alaFipsVlanFcfStatsSessions  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FCoE Sessions on this Vlan Id"
    DEFVAL {0}
    ::= { alaFipsVlanFcfStatsEntry 2 }

alaFipsVlanFcfStatsMda  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Advertisement (MDA) Packets on this Vlan"
    ::= { alaFipsVlanFcfStatsEntry 3 }		
	
alaFipsVlanFcfStatsUda  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Advertisement (UDA) Packets on this Vlan"
    ::= { alaFipsVlanFcfStatsEntry 4 }		
	
alaFipsVlanFcfStatsFlogiAcc  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Login Accept (FLOGI_ACC) Packets on this Vlan"
    ::= { alaFipsVlanFcfStatsEntry 5 }	
	
alaFipsVlanFcfStatsFlogiRjt  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Login Reject (FLOGI_RJT) Packets on this Vlan"
    ::= { alaFipsVlanFcfStatsEntry 6 }		

alaFipsVlanFcfStatsFdiscRjt  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Login Reject (FDISC_RJT) Packets on this Vlan"
    ::= { alaFipsVlanFcfStatsEntry 7 }		

alaFipsVlanFcfStatsLogoAcc  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logout Accept (LOGO_ACC) Packets on this Vlan"
    ::= { alaFipsVlanFcfStatsEntry 8 }	
	
alaFipsVlanFcfStatsLogoRjt  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logout Reject (LOGO_RJT) Packets on this Vlan"
    ::= { alaFipsVlanFcfStatsEntry 9 }	

 alaFipsVlanFcfStatsCvl OBJECT-TYPE
     SYNTAX  Counter32
     MAX-ACCESS  read-only
     STATUS  current
     DESCRIPTION
         "Number of  Clear Virtual Link (CVL)  Packets on this Vlan"
     ::= { alaFipsVlanFcfStatsEntry 10 }		


 alaFipsVlanFcfStatsClear OBJECT-TYPE
     SYNTAX  Unsigned32
     MAX-ACCESS read-write 
     STATUS  current
     DESCRIPTION
         "FCoE VLAN Statistics Clear Object, 0 is Default Value 1 is is Clear all the Fips Statistics in the VLAN"
     ::= { alaFipsVlanFcfStatsEntry 11}

 alaFipsVlanFcfStatsFdiscAcc OBJECT-TYPE
     SYNTAX  Counter32
     MAX-ACCESS  read-only
     STATUS  current
     DESCRIPTION
         "Number of Fabric Disc Acc (FDISC_ACC)  Packets on this Vlan"
     ::= { alaFipsVlanFcfStatsEntry 12 }		


--
--AlaFipsIntfTable
--

   alaFipsIntf OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 5 }

alaFipsIntfTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsIntfEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of Fips Interfaces "
        ::= { alaFipsIntf 1}
		
 alaFipsIntfEntry  OBJECT-TYPE
    SYNTAX  AlaFipsIntfEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of Fips Interfaces."
    INDEX { alaFipsIntfIfIndex }
    ::= { alaFipsIntfTable 1 }
	
AlaFipsIntfEntry ::= SEQUENCE {
		alaFipsIntfIfIndex		InterfaceIndexOrZero,
		alaFipsIntfOperStatus		INTEGER,
		alaFipsIntfPortRole		INTEGER,
		alaFipsIntfRowStatus		RowStatus,
		alaFipsIntfStatsClear		Unsigned32,
		alaFipsIntfStatsFnreClear	INTEGER
}

alaFipsIntfIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Interface where Fips is enabled, it can be indivisual Port /Linkagg  "
    ::= { alaFipsIntfEntry 1 }

alaFipsIntfOperStatus OBJECT-TYPE
    SYNTAX  INTEGER
		{
			enable(1),
			disable(2)
		}

    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " Interface oper statusof  individual Port /Linkagg  "
	DEFVAL {disable}
    ::= { alaFipsIntfEntry 2 }

alaFipsIntfPortRole OBJECT-TYPE
    SYNTAX  INTEGER
		{
			edge(1),
			enode(2),
			fcf(3),
			mixed(4),
			trusted(5),
			ve(6)
		}
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Fips Port roles are assigned.
          VE stands for Virtual Expansion.  A VE port on one FCF is
          connected to a VE port on another FCF.  This link carries
          ISL (inter-switch link) traffic (includes Fabric control
          traffic and data traffic) between connected devices.
          FIP ELP Exchanges are allowed on ports whose role declared
          as VE."
    ::= { alaFipsIntfEntry 3 }

alaFipsIntfRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Row status controls the creation and deletion of rows in the table Fips alaFipsIntfTable"
	DEFVAL {notReady}
    ::= { alaFipsIntfEntry 4 }

alaFipsIntfStatsClear OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "Fips Interface Statistics Clear Object, 0 is Default Value 1 is is Clear all the Fips Statistics on the Interface"
    ::= { alaFipsIntfEntry 5}

alaFipsIntfStatsFnreClear OBJECT-TYPE
    SYNTAX  INTEGER
                {
                        all(1),
                        fips(2),
                        npiv(3),
                        reverseNpiv(4),
                        eTunnel(5),
                        none(6)
                }
    MAX-ACCESS read-create
    STATUS  current
    DESCRIPTION
        "Global FCoE Statistics per feature Clear Object, 1 is Clear all the Fips Statistics at the Port Level"
    DEFVAL { none }
    ::= { alaFipsIntfEntry 6 }


--
--AlaFipsIntfEnodeStatsTable
--
   alaFipsIntfEnodeStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 6 }
alaFipsIntfEnodeStatsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsIntfEnodeStatsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of Fips Interfaces "
        ::= { alaFipsIntfEnodeStats 1}
		
 alaFipsIntfEnodeStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsIntfEnodeStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per Intf/Enode Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFipsIntfEnodeStatsIfIndex }
    ::= { alaFipsIntfEnodeStatsTable 1 }
	
AlaFipsIntfEnodeStatsEntry ::= SEQUENCE {
		alaFipsIntfEnodeStatsIfIndex	InterfaceIndexOrZero,
		alaFipsIntfEnodeStatsSessions	Unsigned32,
		alaFipsIntfEnodeStatsMds	Counter32,
		alaFipsIntfEnodeStatsUds	Counter32,
		alaFipsIntfEnodeStatsFlogi  	Counter32,
		alaFipsIntfEnodeStatsFdisc	Counter32,
		alaFipsIntfEnodeStatsLogo	Counter32,
		alaFipsIntfEnodeStatsEka	Counter32,
		alaFipsIntfEnodeStatsVnka	Counter32,
		alaFipsIntfEnodeStatsClear	Unsigned32
}
alaFipsIntfEnodeStatsIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Interface where Fips is enabled, it can be indivisual Port /Linkagg  "
    ::= { alaFipsIntfEnodeStatsEntry 1 }

alaFipsIntfEnodeStatsSessions  OBJECT-TYPE
   SYNTAX  Unsigned32
   MAX-ACCESS  read-only
   STATUS  current
   DESCRIPTION
       "Number of FCoE Sessions on this Intf"
   DEFVAL {0}
   ::= { alaFipsIntfEnodeStatsEntry 2 }

alaFipsIntfEnodeStatsMds  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Solicitation (MDS) Packets on this Intf"
    ::= { alaFipsIntfEnodeStatsEntry 3 }
	
alaFipsIntfEnodeStatsUds  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Solicitation (MDS) Packets on this Intf"
    ::= { alaFipsIntfEnodeStatsEntry 4 }

alaFipsIntfEnodeStatsFlogi  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFipsIntfEnodeStatsEntry 5 }

alaFipsIntfEnodeStatsFdisc  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Discovery (FDISC) Packets on this Intf"
    ::= { alaFipsIntfEnodeStatsEntry 6 }
	
alaFipsIntfEnodeStatsLogo  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logouts (LOGO) Packets on this Intf"
    ::= { alaFipsIntfEnodeStatsEntry 7 }	

alaFipsIntfEnodeStatsEka  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Enode-keep-alive  Packets on this Intf"
    ::= { alaFipsIntfEnodeStatsEntry 8 }	
	
alaFipsIntfEnodeStatsVnka  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  VNPort-keep-alive   Packets on this Intf"
    ::= { alaFipsIntfEnodeStatsEntry 9 }	

alaFipsIntfEnodeStatsClear OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Fips Interface Statistics Clear Object, 0 is Default Value 1 is is Clear all the Fips Statistics on the Interface"
    ::= { alaFipsIntfEnodeStatsEntry 10 }

--
--AlaFipsIntfFcfStatsTable
--

   alaFipsIntfFcfStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 7 }

alaFipsIntfFcfStatsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsIntfFcfStatsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of Fips Interfaces "
        ::= { alaFipsIntfFcfStats 1}
		
 alaFipsIntfFcfStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsIntfFcfStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per Intf/FCF Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFipsIntfFcfStatsIfIndex }
    ::= { alaFipsIntfFcfStatsTable 1 }
	
AlaFipsIntfFcfStatsEntry ::= SEQUENCE {
		alaFipsIntfFcfStatsIfIndex	InterfaceIndexOrZero,
		alaFipsIntfFcfStatsSessions	Unsigned32,
		alaFipsIntfFcfStatsMda		Counter32,
		alaFipsIntfFcfStatsUda		Counter32,
		alaFipsIntfFcfStatsFlogiAcc	Counter32,
		alaFipsIntfFcfStatsFdiscRjt	Counter32,
		alaFipsIntfFcfStatsFlogiRjt	Counter32,
		alaFipsIntfFcfStatsLogoAcc	Counter32,
		alaFipsIntfFcfStatsLogoRjt	Counter32,
		alaFipsIntfFcfStatsCvl		Counter32,
		alaFipsIntfFcfStatsClear	Unsigned32,
		alaFipsIntfFcfStatsFdiscAcc	Counter32
}
alaFipsIntfFcfStatsIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Interface where Fips is enabled, it can be indivisual Port /Linkagg  "
    ::= { alaFipsIntfFcfStatsEntry 1 }

alaFipsIntfFcfStatsSessions  OBJECT-TYPE
   SYNTAX  Unsigned32
   MAX-ACCESS  read-only
   STATUS  current
   DESCRIPTION
       "Number of FCoE Sessions on this Intf"
   DEFVAL {0}
   ::= { alaFipsIntfFcfStatsEntry 2 }

alaFipsIntfFcfStatsMda  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Advertisement (MDA)   Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 3 }		
	
alaFipsIntfFcfStatsUda  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Advertisement (UDA)   Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 4 }		
	
alaFipsIntfFcfStatsFlogiAcc  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Login Accept (FLOGI_ACC) Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 5 }	
	
alaFipsIntfFcfStatsFdiscRjt  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Discovery (FDISC-RJT) Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 6 }	
	
alaFipsIntfFcfStatsFlogiRjt  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Login Reject (FLOGI_RJT) Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 7 }		

alaFipsIntfFcfStatsLogoAcc  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logout Accept (LOGO_ACC) Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 8 }	
	
alaFipsIntfFcfStatsLogoRjt  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logout Reject (LOGO_RJT) Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 9 }	

alaFipsIntfFcfStatsCvl OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Clear Virtual Link (CVL)  Packets on this Intf"
    ::= { alaFipsIntfFcfStatsEntry 10 }		

alaFipsIntfFcfStatsClear OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Fips Interface Statistics Clear Object, 0 is Default Value 1 is is Clear all the Fips Statistics on the Interface"
    ::= { alaFipsIntfFcfStatsEntry 11}

 alaFipsIntfFcfStatsFdiscAcc OBJECT-TYPE
     SYNTAX  Counter32
     MAX-ACCESS  read-only
     STATUS  current
     DESCRIPTION
         "Number of Fabric Disc Acc (FDISC_ACC)  Packets on this Vlan"
     ::= { alaFipsIntfFcfStatsEntry 12 }		


--
--alaFipsFcfTable
--
alaFipsFcf OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 8 }
alaFipsFcfTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsFcfEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of FCoE Interfaces "
        ::= { alaFipsFcf 1}
		
 alaFipsFcfEntry  OBJECT-TYPE
    SYNTAX  AlaFipsFcfEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of Fips FCFs."
    INDEX { alaFipsFcfMAC,alaFipsFcfVlan }
    ::= { alaFipsFcfTable 1 }
	
AlaFipsFcfEntry ::= SEQUENCE {
	alaFipsFcfMAC		MacAddress,
	alaFipsFcfVlan		Integer32,
	alaFipsFcfIntf		InterfaceIndex,
	alaFipsFcfSessions	Unsigned32,
	alaFipsFcfConfigType	INTEGER,
	alaFipsFcfRowStatus	RowStatus,
        alaFipsFcfAvailForLogin INTEGER,
        alaFipsFcfMaxFcoeFrmSizeVerified INTEGER,
        alaFipsFcfPriority      Unsigned32,
        alaFipsFcfType          INTEGER
}


alaFipsFcfMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FCF MAC configured/Learned in the System"
    ::= { alaFipsFcfEntry 1 }
	

alaFipsFcfVlan OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Vlan on which the "
    ::= { alaFipsFcfEntry 2 }	

alaFipsFcfIntf OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
         " Interface Fips session exists, it can be indivisual Port /Linkagg."
    ::= { alaFipsFcfEntry 3 }	
	
alaFipsFcfSessions OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " Number of Fips Sessions on this FCF MAC in the given VLAN"
    ::= { alaFipsFcfEntry 4 }	
	
alaFipsFcfConfigType OBJECT-TYPE
    SYNTAX  INTEGER
		{
			fpma(1),
			spma(2)
		}
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " The FCF MAC is SPMA or FPMA"
	DEFVAL {fpma}
    ::= { alaFipsFcfEntry 5 }	
		 
alaFipsFcfRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Row status controls the creation and deletion of rows in the table Fips alaFipsFcfTable"
	DEFVAL {notReady}
    ::= { alaFipsFcfEntry 6 }	

	
alaFipsFcfAvailForLogin OBJECT-TYPE
    SYNTAX  INTEGER
		{
			yes(1),
			no(2)
		}
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " The FCF's availability for login-in"
    ::= { alaFipsFcfEntry 7 }	
		 
	
alaFipsFcfMaxFcoeFrmSizeVerified OBJECT-TYPE
    SYNTAX  INTEGER
		{
			yes(1),
			no(2)
		}
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " The FCF's max frame size verified"
    ::= { alaFipsFcfEntry 8 }	

alaFipsFcfPriority OBJECT-TYPE
    SYNTAX  Unsigned32 (0..255)
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
       "Value of priority in FIP Discovery Advertisement packets received/generated on this VLAN for the FCF."
    ::= { alaFipsFcfEntry 9 }	


alaFipsFcfType OBJECT-TYPE
    SYNTAX  INTEGER
		{
			dynamic(1),
			static(2),
			npiv(3)
		}
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " The FCF MAC is learned from FIP Snooping or Administrator Configuration or NPIV serviced"
    ::= { alaFipsFcfEntry 10 }	
--
--AlaFipsSessionTable
--
   alaFipsSession OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 9 }
alaFipsSessionTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsSessionEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the list of Session in the system "
        ::= { alaFipsSession 1}
		
 alaFipsSessionEntry  OBJECT-TYPE
    SYNTAX  AlaFipsSessionEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of Fips Session."
    INDEX { alaFipsSessionEnodeMAC , alaFipsSessionVNMAC,   alaFipsSessionVlanId }
    ::= { alaFipsSessionTable 1 }

AlaFipsSessionEntry ::= SEQUENCE {
	alaFipsSessionEnodeMAC		MacAddress,
	alaFipsSessionVNMAC		MacAddress,
	alaFipsSessionVlanId		Integer32,
	alaFipsSessionIfIndex		InterfaceIndex,
	alaFipsSessionFCFMAC		MacAddress,
	alaFipsSessionStatus		INTEGER,
	alaFipsSessionLoginTime		TimeStamp,
	alaFipsSessionLoginTimeDate	DateAndTime
}
alaFipsSessionEnodeMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Enode MAC where for which this session exists"
    ::= { alaFipsSessionEntry 1 }

alaFipsSessionVNMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " VN MAC address of the Session"
    ::= { alaFipsSessionEntry 2 }

alaFipsSessionVlanId OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FCF VLAN ID of the Session"
    ::= { alaFipsSessionEntry 3 }



alaFipsSessionIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " Interface Fips session exists, it can be indivisual Port /Linkagg."
    ::= { alaFipsSessionEntry 4 }
	
	
alaFipsSessionFCFMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " FCF MAC address of the Session"
	DEFVAL {'000000000000'H}
    ::= { alaFipsSessionEntry 5 }

	
alaFipsSessionStatus OBJECT-TYPE
    SYNTAX  INTEGER
		{
			pending(1),
			created(2)
		}

    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCF Session Pending or Active"
	DEFVAL {pending}
    ::= { alaFipsSessionEntry 6 }

alaFipsSessionLoginTime OBJECT-TYPE
    SYNTAX  TimeStamp
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "This is the value of the 'sysUpTime' object when this
        FCoE session established"
    ::= { alaFipsSessionEntry 7 }

alaFipsSessionLoginTimeDate OBJECT-TYPE
    SYNTAX  DateAndTime
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Date and time of this session login"
    ::= { alaFipsSessionEntry 8 }



-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- NOTIFICATIONS (TRAPS)
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
alaFipsResourceThresholdReached NOTIFICATION-TYPE
    OBJECTS  {
		alaFipsFilterResourceUsage
             }
    STATUS   current
    DESCRIPTION
            "The trap shall be raised when FIPS configuration results in utilization of more filter resources than configured % of resources for FIPS."
    ::= { alcatelIND1FipsMIBNotifications 1 }

-- Notification Objects
   alaFipsNotificationObj OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 10 }

   alaFipsFilterResourceUsage OBJECT-TYPE
   SYNTAX  Integer32 (0 .. 100)
   MAX-ACCESS accessible-for-notify
   STATUS  current
   DESCRIPTION
      "The % of allocated filter resources used by current FIP Snooping configuration."
        ::= { alaFipsNotificationObj 1 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

-- NPIV, R-NPIV, E-TUNNEL

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

--AlaVsanTable

   alaFcVsan OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 11 }

alaFcVsanTable  OBJECT-TYPE
    SYNTAX  SEQUENCE OF AlaFcVsanEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
    "A list of Virtual SAN instances in this device."
    ::= { alaFcVsan 1 }

 alaFcVsanEntry  OBJECT-TYPE
     SYNTAX  AlaFcVsanEntry
     MAX-ACCESS  not-accessible
     STATUS  current
     DESCRIPTION
      "A VSAN entry."
     INDEX { alaFcVsanNumber }
     ::= { alaFcVsanTable 1 }

AlaFcVsanEntry ::= SEQUENCE {
    alaFcVsanNumber         Integer32,
    alaFcVsanDescription    SnmpAdminString,
    alaFcVsanAdmStatus      INTEGER,
    alaFcVsanOperStatus     INTEGER,
    alaFcVsanRowStatus      RowStatus
}

alaFcVsanNumber  OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "The VSAN number identifying this instance. Valid
         range from 1 to 4094."
    ::= { alaFcVsanEntry 1 }

alaFcVsanDescription  OBJECT-TYPE
    SYNTAX  SnmpAdminString (SIZE (0..32))
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "Textual description of the VSAN instance."
    ::= { alaFcVsanEntry 2 }

alaFcVsanAdmStatus  OBJECT-TYPE
    SYNTAX  INTEGER {
        enabled(1),
        disabled(2)
    }
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "The administrative status which can be set by
         configuration. Disable(1) will disable ALL FC ports
         that are connected to this VSAN, enable(2) will enable
         ALL ports on this VSAN."
    DEFVAL { enabled }
    ::= { alaFcVsanEntry 3 }

alaFcVsanOperStatus  OBJECT-TYPE
    SYNTAX  INTEGER {
        active(1),
        inactive(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Provide operating status of a particular VSAN environment."
    DEFVAL { inactive }
    ::= { alaFcVsanEntry 4 }

alaFcVsanRowStatus  OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "Row status to control creation/deletion of Vsans."
    ::= { alaFcVsanEntry 5 }


-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

   alaFcVfpa OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 12 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

alaFcVfpaTable  OBJECT-TYPE
    SYNTAX  SEQUENCE OF AlaFcVfpaEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "A list of Virtual SAN instances in this device."
    ::= { alaFcVfpa 1 }

 alaFcVfpaEntry  OBJECT-TYPE
     SYNTAX  AlaFcVfpaEntry
     MAX-ACCESS  not-accessible
     STATUS  current
     DESCRIPTION
         "A VSAN-FC Port Association (VPA) entry."
     INDEX { alaFcVfpaVsanNumber, alaFcVfpaIfIndex }
     ::= { alaFcVfpaTable 1 }

AlaFcVfpaEntry ::= SEQUENCE {
     alaFcVfpaVsanNumber    Integer32,
     alaFcVfpaIfIndex       Unsigned32,
     alaFcVfpaState         INTEGER,
     alaFcVfpaRowStatus     RowStatus
}

alaFcVfpaVsanNumber  OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "The VSAN number component of this instance. Valid
         range from 1 to 4094."
    ::= { alaFcVfpaEntry 1 }

alaFcVfpaIfIndex  OBJECT-TYPE
    SYNTAX  Unsigned32 (1001..4294967295)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "The ifIndex component of this instance."
    ::= {alaFcVfpaEntry 2 }

alaFcVfpaState  OBJECT-TYPE
    SYNTAX  INTEGER {
        enabled(0),
        disabled(1),
        inactive(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The state of this vfpa"
    DEFVAL { inactive }
    ::= { alaFcVfpaEntry 3 }

alaFcVfpaRowStatus  OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "This is used to create or delete staticPrimary VFPAs (default vsans
         on a fibre port ).  Creation replaces the existing staticPrimary VPA
         while deletion replaces the existing VFPA so that the vsan number
         is 1 for the given fibre port.  Of course the corresponding vsan and
         fiber port must exist."
    ::= { alaFcVfpaEntry 4 }


--
--AlaFcIntfTable
--

   alaFcIntf OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 13 }

alaFcIntfTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcIntfEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of FC Interfaces "
        ::= { alaFcIntf 1}
		
 alaFcIntfEntry  OBJECT-TYPE
    SYNTAX  AlaFcIntfEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FC Interfaces."
    INDEX { alaFcIntfIfIndex }
    ::= { alaFcIntfTable 1 }
	
AlaFcIntfEntry ::= SEQUENCE {
    alaFcIntfIfIndex           InterfaceIndexOrZero,
    alaFcIntfOperStatus        INTEGER,
    alaFcIntfMode              INTEGER,
    alaFcIntfBbScN             Integer32,
    alaFcIntfClassOfService    INTEGER,
    alaFcIntfFcid              Fcid,	
    alaFcIntfWwpn              Wwpn,
    alaFcIntfLoginState        INTEGER,
    alaFcIntfRowStatus         RowStatus,
    alaFcIntfStatsClear        INTEGER
}

alaFcIntfIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Interface where ports are configured in Fibre Channel mode."
    ::= { alaFcIntfEntry 1 }

alaFcIntfOperStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        up(1),
        down(2)
    }

    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " Interface oper status of  FC Port."
    ::= { alaFcIntfEntry 2 }

alaFcIntfMode OBJECT-TYPE
    SYNTAX  INTEGER {
        np(1),
        f(2),
        te(3)
    }
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " FC Port mode of operation
            1. np - Proxy N_Port
            2. f - Fabric Port connected to N_port
            3. te - Tunnel E-Port"
    ::= { alaFcIntfEntry 3 }

alaFcIntfBbScN OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 15)
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Buffer-to-Buffer State Change Number - Fibre Channels ports exchange Buffer-to-Buffer State Change (BB_SC)
          primitives after 2^BB_SC_N frames.  BB_SC primities are used to re-calculate buffer crdits."
    ::= { alaFcIntfEntry 4 }

alaFcIntfClassOfService OBJECT-TYPE
    SYNTAX  INTEGER {
        class2(1),
        class3(2),
        classF(3),
        class3andF(4)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " Classes of service are distinguished primarily by the level of delivery integrity required for an application.  Section 4.7 of FC-FC-4.
          1. class-2 - Class 2 is a frame delivery service multiplexing frames at frame boundaries with frame acknowledgement
          2. class-3 - Class 3 is a frame delivery service with the Fabric multiplexing frames at frame boundaries without frame acknowledgement
          3. class-f - Class F is a frame delivery service used only for communication between switches in a Fabric"
    ::= { alaFcIntfEntry 5 }

alaFcIntfFcid OBJECT-TYPE
    SYNTAX  Fcid
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Port ID received after succesful intial Fabric login.  It is a 3 byte value."
    ::= { alaFcIntfEntry 6 }

alaFcIntfWwpn OBJECT-TYPE
    SYNTAX  Wwpn
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Port name assigned to ports that are in FC mode by AOS.  It is an 8 byte value whose first 2 bytes are 1000h and remaing bytes  of operation."
    ::= { alaFcIntfEntry 7 }

alaFcIntfLoginState OBJECT-TYPE
    SYNTAX  INTEGER {
        up(1),
        flogiSent(2),
        elpSent(3),
        sessionClearing(4),
        down(5)
    }

    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " Interface FLOGI status of  FC Port."
    ::= { alaFcIntfEntry 8 }

alaFcIntfRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Row status controls the creation and deletion of rows in the table FC alaFcIntfTable."
	DEFVAL {notReady}
    ::= { alaFcIntfEntry 9 }

alaFcIntfStatsClear OBJECT-TYPE
    SYNTAX  INTEGER {
        clear(1),
        none(2)
    }
     MAX-ACCESS  read-create
     STATUS  current
     DESCRIPTION
     "FC Interface Statistics Clear Objectr; Clears all the FC Statistics on the Interface"
     DEFVAL { none }
     ::= { alaFcIntfEntry 10}
--
--AlaFcNpivStaticLoadBalanceTable
--

   alaFcNpivStaticLoadBalance OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 14 }

alaFcNpivStaticLoadBalanceTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcNpivStaticLoadBalanceEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of FC Interfaces and FCoE interface mapping for static loadbalancing.
             Load balancing algorithm is for the creation of FC sessions when there is more than
             one FC port is connected within a VSAN.  Default is - new session is created on the
             port which has least number of sessions.
             dynamic-reorder - When new FC ports become active within the VSAN, some of the sessions are
                       logged out and relogged in to load balace the sessions across the FC ports.
             enode-based - FC port on which new session is established is chosen based on ENODE MAC.
             static - This algorithm allows administrator to map sessions on specific FCoE port to a
                      specific FC port(s)."
        ::= { alaFcNpivStaticLoadBalance 1}
		
 alaFcNpivStaticLoadBalanceEntry  OBJECT-TYPE
    SYNTAX  AlaFcNpivStaticLoadBalanceEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FC Interface FCoE interface mappings."
    INDEX { alaFcNpivStaticLoadBalanceFcIfIndex, alaFcNpivStaticLoadBalanceEthIfIndex }
    ::= { alaFcNpivStaticLoadBalanceTable 1 }
	
AlaFcNpivStaticLoadBalanceEntry ::= SEQUENCE {
    alaFcNpivStaticLoadBalanceFcIfIndex    InterfaceIndexOrZero,
    alaFcNpivStaticLoadBalanceEthIfIndex   InterfaceIndexOrZero,
    alaFcNpivStaticLoadBalanceRowStatus    RowStatus
}

alaFcNpivStaticLoadBalanceFcIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Fibre Channel Interface of the mapping."
    ::= { alaFcNpivStaticLoadBalanceEntry 1 }

alaFcNpivStaticLoadBalanceEthIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Ethernet Interface of the mapping.  It can be a linkagg."
    ::= { alaFcNpivStaticLoadBalanceEntry 2 }

alaFcNpivStaticLoadBalanceRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Row status controls the creation and deletion of rows in the table FC alaFcNpivStaticLoadBalanceTable."
	DEFVAL {notReady}
    ::= { alaFcNpivStaticLoadBalanceEntry 3 }



--
--AlaFcNodeTable
--

   alaFcNode OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 15 }

alaFcNodeTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcNodeEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of FC Interfaces "
        ::= { alaFcNode 1}
		
 alaFcNodeEntry  OBJECT-TYPE
    SYNTAX  AlaFcNodeEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FC nodes."
    INDEX { alaFcNodeIfIndex, alaFcNodeWwpn }
    ::= { alaFcNodeTable 1 }
	
AlaFcNodeEntry ::= SEQUENCE {
    alaFcNodeIfIndex       InterfaceIndex,
    alaFcNodeWwpn          Wwpn,
    alaFcNodeVsanNumber    Integer32,
    alaFcNodeVlanNumber    Integer32,
    alaFcNodeFcid          Fcid,	
    alaFcNodeWwnn          Wwnn
}

alaFcNodeIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Interface where FC node is connected."
    ::= { alaFcNodeEntry 1 }

alaFcNodeWwpn OBJECT-TYPE
    SYNTAX  Wwpn
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FC Port port name.  It is a 8 byte value."
    ::= { alaFcNodeEntry 2 }

alaFcNodeVsanNumber  OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The VSAN number on which FC node is connected."
    ::= { alaFcNodeEntry 3 }

alaFcNodeVlanNumber  OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The VLAN number on which this FC node services are provided/used."
    ::= { alaFcNodeEntry 4 }

alaFcNodeFcid OBJECT-TYPE
    SYNTAX  Fcid
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Port ID optained for F Node after succesful Node login.  It is a 3 byte value."
    ::= { alaFcNodeEntry 5 }

alaFcNodeWwnn OBJECT-TYPE
    SYNTAX  Wwnn
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Node (AOS) name.  It is a 8 byte value."
    ::= { alaFcNodeEntry 6 }

--
--AlaFcSessTable
--

   alaFcSess OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 16 }

alaFcSessTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcSessEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of FC Interfaces "
        ::= { alaFcSess 1}
		
 alaFcSessEntry  OBJECT-TYPE
    SYNTAX  AlaFcSessEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FC Interfaces."
    INDEX { alaFcSessIfIndex, alaFcSessWwpn}
    ::= { alaFcSessTable 1 }
	
AlaFcSessEntry ::= SEQUENCE {
    alaFcSessIfIndex       InterfaceIndex,
    alaFcSessWwpn          Wwpn,
    alaFcSessVsanNumber    Integer32,
    alaFcSessStatus        INTEGER,
    alaFcSessIntfMode      INTEGER,
    alaFcSessFcid          Fcid,	
    alaFcSessType          INTEGER,
    alaFcSessTunnelId      Unsigned32

}

alaFcSessIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Interface where FC node is connected."
    ::= { alaFcSessEntry 1 }

alaFcSessWwpn OBJECT-TYPE
    SYNTAX  Wwpn
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FC Port port name.  It is a 8 byte value."
    ::= { alaFcSessEntry 2 }

alaFcSessVsanNumber  OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The VSAN number on which FC node is connected."
    ::= { alaFcSessEntry 3 }


alaFcSessStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        pending(1),
        success(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Port session status"
    ::= { alaFcSessEntry 4 }


alaFcSessIntfMode OBJECT-TYPE
    SYNTAX  INTEGER {
        np(1),
        f(2),
        te(3)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Port mode of operation
            1. np - Proxy N_Port
            2. f - Fabric Port connected to N_port
            3. te - Tunnel E-Port"
    ::= { alaFcSessEntry 5 }

alaFcSessFcid OBJECT-TYPE
    SYNTAX  Fcid
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Port ID received after succesful Fabric login.  It is a 3 byte value."
    ::= { alaFcSessEntry 6 }

alaFcSessType OBJECT-TYPE
    SYNTAX  INTEGER {
        fdisc(1),
        flogi(2),
        elp(3)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Port session login type."
    ::= { alaFcSessEntry 7 }

alaFcSessTunnelId  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "The tunnel Id associated with the FC session, if applicable (E-Tunnels)."
    ::= { alaFcSessEntry 8 }

--
--AlaFcIntfNpivStatsTable
--
   alaFcIntfNpivStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 17 }
alaFcIntfNpivStatsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcIntfNpivStatsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of FC Interfaces for statistics of NPIV. "
        ::= { alaFcIntfNpivStats 1}
		
 alaFcIntfNpivStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFcIntfNpivStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "NPIV per Intf Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFcIntfNpivStatsIfIndex }
    ::= { alaFcIntfNpivStatsTable 1 }
	
AlaFcIntfNpivStatsEntry ::= SEQUENCE {
    alaFcIntfNpivStatsIfIndex         InterfaceIndex,
    alaFcIntfNpivStatsTxFlogis        Counter32,
    alaFcIntfNpivStatsTxFdiscs        Counter32,
    alaFcIntfNpivStatsRxLsAccs        Counter32,
    alaFcIntfNpivStatsRxFlogos        Counter32,
    alaFcIntfNpivStatsRxFlogiLsRjts   Counter32,
    alaFcIntfNpivStatsRxFdiscLsRjts   Counter32,
    alaFcIntfNpivStatsTxFlogos        Counter32,
    alaFcIntfNpivStatsClear           INTEGER
}
alaFcIntfNpivStatsIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " NPIV statistics on this FC port. "
    ::= { alaFcIntfNpivStatsEntry 1 }

alaFcIntfNpivStatsTxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfNpivStatsEntry 2 }

alaFcIntfNpivStatsTxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FDISC) Packets on this Intf"
    ::= { alaFcIntfNpivStatsEntry 3 }

alaFcIntfNpivStatsRxLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_ACC Packets on this Intf"
    ::= { alaFcIntfNpivStatsEntry 4 }

alaFcIntfNpivStatsRxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Rx Packets on this Intf"
    ::= { alaFcIntfNpivStatsEntry 5 }

alaFcIntfNpivStatsRxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfNpivStatsEntry 6 }

alaFcIntfNpivStatsRxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfNpivStatsEntry 7 }

alaFcIntfNpivStatsTxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Tx Packets on this Intf"
    ::= { alaFcIntfNpivStatsEntry 8 }

alaFcIntfNpivStatsClear  OBJECT-TYPE
    SYNTAX  INTEGER {
        clear(1),
        none(2)
    }
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "Clear Npiv statistics on  this Intf"
        DEFVAL { none }
    ::= { alaFcIntfNpivStatsEntry 9 }


--
--AlaFcVsanNpivStatsTable
--
   alaFcVsanNpivStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 18 }
alaFcVsanNpivStatsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcVsanNpivStatsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of VSANS for statistics of NPIV. "
        ::= { alaFcVsanNpivStats 1}
		
 alaFcVsanNpivStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFcVsanNpivStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "NPIV per Vsan Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFcVsanNpivStatsVsan }
    ::= { alaFcVsanNpivStatsTable 1 }
	
AlaFcVsanNpivStatsEntry ::= SEQUENCE {
    alaFcVsanNpivStatsVsan            Integer32,
    alaFcVsanNpivStatsTxFlogis        Counter32,
    alaFcVsanNpivStatsTxFdiscs        Counter32,
    alaFcVsanNpivStatsRxLsAccs        Counter32,
    alaFcVsanNpivStatsRxFlogos        Counter32,
    alaFcVsanNpivStatsRxFlogiLsRjts   Counter32,
    alaFcVsanNpivStatsRxFdiscLsRjts   Counter32,
    alaFcVsanNpivStatsTxFlogos        Counter32
}

alaFcVsanNpivStatsVsan OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "NPIV statistics on this VSAN number."
    ::= { alaFcVsanNpivStatsEntry 1 }

alaFcVsanNpivStatsTxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanNpivStatsEntry 2 }

alaFcVsanNpivStatsTxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FDISC) Packets on this Vsan"
    ::= { alaFcVsanNpivStatsEntry 3 }

alaFcVsanNpivStatsRxLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_ACC Packets on this Vsan"
    ::= { alaFcVsanNpivStatsEntry 4 }

alaFcVsanNpivStatsRxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Rx Packets on this Vsan"
    ::= { alaFcVsanNpivStatsEntry 5 }

alaFcVsanNpivStatsRxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanNpivStatsEntry 6 }

alaFcVsanNpivStatsRxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanNpivStatsEntry 7 }

alaFcVsanNpivStatsTxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Tx Packets on this Vsan"
    ::= { alaFcVsanNpivStatsEntry 8 }

--
--AlaFcIntfRnpivStatsTable
--
   alaFcIntfRnpivStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 19 }
alaFcIntfRnpivStatsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcIntfRnpivStatsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of FC Interfaces for statistics of R-NPIV. "
        ::= { alaFcIntfRnpivStats 1}
		
 alaFcIntfRnpivStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFcIntfRnpivStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "R-NPIV per Intf Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFcIntfRnpivStatsIfIndex }
    ::= { alaFcIntfRnpivStatsTable 1 }
	
AlaFcIntfRnpivStatsEntry ::= SEQUENCE {
    alaFcIntfRnpivStatsIfIndex         InterfaceIndex,
    alaFcIntfRnpivStatsRxFlogis        Counter32,
    alaFcIntfRnpivStatsRxFdiscs        Counter32,
    alaFcIntfRnpivStatsTxFlogiLsAccs   Counter32,
    alaFcIntfRnpivStatsTxFdiscLsAccs   Counter32,
    alaFcIntfRnpivStatsTxFlogos        Counter32,
    alaFcIntfRnpivStatsTxFlogiLsRjts   Counter32,
    alaFcIntfRnpivStatsTxFdiscLsRjts   Counter32,
    alaFcIntfRnpivStatsRxFlogos        Counter32,
    alaFcIntfRnpivStatsClear           INTEGER
}

alaFcIntfRnpivStatsIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " NPIV statistics on this FC port. "
    ::= { alaFcIntfRnpivStatsEntry 1 }

alaFcIntfRnpivStatsRxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 2 }

alaFcIntfRnpivStatsRxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FDISC) Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 3 }

alaFcIntfRnpivStatsTxFlogiLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_ACCs for Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 4 }

alaFcIntfRnpivStatsTxFdiscLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_ACCs for Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 5 }

alaFcIntfRnpivStatsTxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Tx Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 6 }

alaFcIntfRnpivStatsTxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 7 }

alaFcIntfRnpivStatsTxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 8 }

alaFcIntfRnpivStatsRxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Rx Packets on this Intf"
    ::= { alaFcIntfRnpivStatsEntry 9 }

alaFcIntfRnpivStatsClear  OBJECT-TYPE
    SYNTAX  INTEGER {
        clear(1),
        none(2)
    }
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Clear Rnpiv statistics on  this Intf"
        DEFVAL { none }
    ::= { alaFcIntfRnpivStatsEntry 10 }


--
--AlaFcVsanRnpivStatsTable
--
   alaFcVsanRnpivStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 20 }
alaFcVsanRnpivStatsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcVsanRnpivStatsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of VSANS for statistics of R-NPIV. "
        ::= { alaFcVsanRnpivStats 1}
		
 alaFcVsanRnpivStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFcVsanRnpivStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "R-NPIV per Vsan Statistics Entry. All Counters starts from 0 when they rollover"
    INDEX { alaFcVsanRnpivStatsVsan }
    ::= { alaFcVsanRnpivStatsTable 1 }
	
AlaFcVsanRnpivStatsEntry ::= SEQUENCE {
    alaFcVsanRnpivStatsVsan            Integer32,
    alaFcVsanRnpivStatsRxFlogis        Counter32,
    alaFcVsanRnpivStatsRxFdiscs        Counter32,
    alaFcVsanRnpivStatsTxFlogiLsAccs   Counter32,
    alaFcVsanRnpivStatsTxFdiscLsAccs   Counter32,
    alaFcVsanRnpivStatsTxFlogos        Counter32,
    alaFcVsanRnpivStatsTxFlogiLsRjts   Counter32,
    alaFcVsanRnpivStatsTxFdiscLsRjts   Counter32,
    alaFcVsanRnpivStatsRxFlogos        Counter32
}

alaFcVsanRnpivStatsVsan OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "R-NPIV statistics on this VSAN number."
    ::= { alaFcVsanRnpivStatsEntry 1 }

alaFcVsanRnpivStatsRxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 2 }

alaFcVsanRnpivStatsRxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Logins (FDISC) Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 3 }

alaFcVsanRnpivStatsTxFlogiLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_ACCs for Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 4 }

alaFcVsanRnpivStatsTxFdiscLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_ACCs for Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 5 }

alaFcVsanRnpivStatsTxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Tx Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 6 }

alaFcVsanRnpivStatsTxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 7 }

alaFcVsanRnpivStatsTxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of LS_RJTs for Fabric Logins (FLOGI) Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 8 }

alaFcVsanRnpivStatsRxFlogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGOs for Fabric Logins (FLOGI) Rx Packets on this Vsan"
    ::= { alaFcVsanRnpivStatsEntry 9 }

--
-- FC feature Common Definitions
--

--
--alaFcConfig
--
   alaFcInfo OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 21 }
alaFcConfigSessClear OBJECT-TYPE
    SYNTAX  INTEGER {
        all(1),
        npiv(2),
        reverseNpiv(3),
        eTunnel(4),
        none(5)
    }
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Clear sessions on Fibre Channel ports; clear - ALL sessions / only NPIV sessions / only R-NPIV sessions / only E-TUNNEL sessions."
        DEFVAL { none }
    ::= { alaFcInfo 1 }

alaFcConfigStatsClear OBJECT-TYPE
    SYNTAX  INTEGER {
        all(1),
        npiv(2),
        reverseNpiv(3),
        eTunnel(4),
        none(5)
    }
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Global FC Statistics Clear Object, Value 1 to Clear all the FC Statistics, 2 for NPIV, 3 for Reverse-NPIV, 4 for E-Tunnel in the VSAN/Tunnel as well as Port Level"
        DEFVAL { none }
    ::= { alaFcInfo 2 }

alaFcConfigNpivLoadBalance OBJECT-TYPE
    SYNTAX  INTEGER {
        default(1),
       	dynamicReorder(2),
        enodeBased(3)
    }
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
            "Load balancing algorithm is for the creation of FC sessions when there is more than
             one FC port is connected within a VSAN.  The value configured here takes effect when
             'static load balancing' is not configured on a pair of ports.
             Default is - new session is created on the port (within the VSAN) which has least number of sessions.
             dynamic-reorder - When new FC ports become active within the VSAN, some of the sessions are
                               logged out and relogged in to load balace the sessions across the FC ports.
             enode-based - FC port on which new session is established is chosen based on ENODE MAC."
	DEFVAL {default}
    ::= { alaFcInfo 3 }

alaFcConfigWwnn OBJECT-TYPE
    SYNTAX  Wwnn 
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
            "World Wide Node Name of this device"
    ::= { alaFcInfo 4 }
 
--
-- AlaFipsVlanNpivDiscStatsTable
--
   alaFipsVlanNpivDiscStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 22 }
alaFipsVlanNpivDiscStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsVlanNpivDiscStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE VLAN NPIV ENODE Discovery Statistics."
    ::= { alaFipsVlanNpivDiscStats 1}

 alaFipsVlanNpivDiscStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVlanNpivDiscStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per VLan/NPIV ENODE Discovery Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsVlanNpivDiscStatsVlanId }
    ::= { alaFipsVlanNpivDiscStatsTable 1 }

AlaFipsVlanNpivDiscStatsEntry ::= SEQUENCE {
    alaFipsVlanNpivDiscStatsVlanId             Integer32,
    alaFipsVlanNpivDiscStatsRxVlanDiscRqs      Counter32,
    alaFipsVlanNpivDiscStatsTxVlanDiscResps    Counter32,
    alaFipsVlanNpivDiscStatsRxMdss             Counter32,
    alaFipsVlanNpivDiscStatsRxUdss             Counter32,
    alaFipsVlanNpivDiscStatsTxMdas             Counter32,
    alaFipsVlanNpivDiscStatsTxUdas             Counter32
}

alaFipsVlanNpivDiscStatsVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled."
    ::= { alaFipsVlanNpivDiscStatsEntry 1 }

alaFipsVlanNpivDiscStatsRxVlanDiscRqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of VLAN discovery requests on this Vlan."
    ::= { alaFipsVlanNpivDiscStatsEntry 2 }

alaFipsVlanNpivDiscStatsTxVlanDiscResps  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of responses to VLAN discovery requests on this Vlan."
    ::= { alaFipsVlanNpivDiscStatsEntry 3 }

alaFipsVlanNpivDiscStatsRxMdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of unicast discovery solicitations of NPIV proxy on this Vlan."
    ::= { alaFipsVlanNpivDiscStatsEntry 4 }

alaFipsVlanNpivDiscStatsRxUdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of multicast discovery solicitations of NPIV proxy on this Vlan."
    ::= { alaFipsVlanNpivDiscStatsEntry 5 }

alaFipsVlanNpivDiscStatsTxMdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of unicast discovery advertisements of NPIV proxy on this Vlan."
    ::= { alaFipsVlanNpivDiscStatsEntry 6 }

alaFipsVlanNpivDiscStatsTxUdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Multicast discovery advertisements of NPIV proxy on this Vlan."
    ::= { alaFipsVlanNpivDiscStatsEntry 7 }

--
-- AlaFipsIntfNpivDiscStatsTable
--
   alaFipsIntfNpivDiscStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 23 }
alaFipsIntfNpivDiscStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsIntfNpivDiscStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE Port NPIV ENODE Discovery Statistics."
    ::= { alaFipsIntfNpivDiscStats 1}

 alaFipsIntfNpivDiscStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsIntfNpivDiscStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per Port/NPIV ENODE Discovery Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsIntfNpivDiscStatsIfIndex }
    ::= { alaFipsIntfNpivDiscStatsTable 1 }

AlaFipsIntfNpivDiscStatsEntry ::= SEQUENCE {
    alaFipsIntfNpivDiscStatsIfIndex            InterfaceIndex,
    alaFipsIntfNpivDiscStatsRxVlanDiscRqs      Counter32,
    alaFipsIntfNpivDiscStatsTxVlanDiscResps    Counter32,
    alaFipsIntfNpivDiscStatsRxMdss             Counter32,
    alaFipsIntfNpivDiscStatsRxUdss             Counter32,
    alaFipsIntfNpivDiscStatsTxMdas             Counter32,
    alaFipsIntfNpivDiscStatsTxUdas             Counter32
}

alaFipsIntfNpivDiscStatsIfIndex  OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Port Id, the Port on which FCoE is enabled."
    ::= { alaFipsIntfNpivDiscStatsEntry 1 }

alaFipsIntfNpivDiscStatsRxVlanDiscRqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of VLAN discovery requests on this Port."
    ::= { alaFipsIntfNpivDiscStatsEntry 2 }

alaFipsIntfNpivDiscStatsTxVlanDiscResps  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of responses to VLAN discovery requests on this Port."
    ::= { alaFipsIntfNpivDiscStatsEntry 3 }

alaFipsIntfNpivDiscStatsRxMdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of unicast discovery solicitations of NPIV proxy on this Port."
    ::= { alaFipsIntfNpivDiscStatsEntry 4 }

alaFipsIntfNpivDiscStatsRxUdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of multicast discovery solicitations of NPIV proxy on this Port."
    ::= { alaFipsIntfNpivDiscStatsEntry 5 }

alaFipsIntfNpivDiscStatsTxMdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of unicast discovery advertisements of NPIV proxy on this Port."
    ::= { alaFipsIntfNpivDiscStatsEntry 6 }

alaFipsIntfNpivDiscStatsTxUdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Multicast discovery advertisements of NPIV proxy on this Port."
    ::= { alaFipsIntfNpivDiscStatsEntry 7 }


--
-- AlaFipsVlanNpivLoginStatsTable
--
   alaFipsVlanNpivLoginStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 24 }
alaFipsVlanNpivLoginStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsVlanNpivLoginStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE VLAN NPIV Login Statistics."
    ::= { alaFipsVlanNpivLoginStats 1}

 alaFipsVlanNpivLoginStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVlanNpivLoginStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per VLan/NPIV Login Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsVlanNpivLoginStatsVlanId }
    ::= { alaFipsVlanNpivLoginStatsTable 1 }

AlaFipsVlanNpivLoginStatsEntry ::= SEQUENCE {
    alaFipsVlanNpivLoginStatsVlanId         Integer32,
    alaFipsVlanNpivLoginStatsRxFlogis       Counter32,
    alaFipsVlanNpivLoginStatsRxFdiscs       Counter32,
    alaFipsVlanNpivLoginStatsTxFlogiAccs    Counter32,
    alaFipsVlanNpivLoginStatsTxFlogiLsRjts  Counter32,
    alaFipsVlanNpivLoginStatsTxFdiscLsRjts  Counter32,
    alaFipsVlanNpivLoginStatsRxLogos        Counter32,
    alaFipsVlanNpivLoginStatsTxCvls         Counter32,
    alaFipsVlanNpivLoginStatsRxEkas         Counter32,
    alaFipsVlanNpivLoginStatsRxVnkas        Counter32,
    alaFipsVlanNpivLoginStatsTxFDiscAccs    Counter32,
    alaFipsVlanNpivLoginStatsTxFlogoAccs    Counter32,
    alaFipsVlanNpivLoginStatsTxFLogoLsRjts  Counter32
}

alaFipsVlanNpivLoginStatsVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled."
    ::= { alaFipsVlanNpivLoginStatsEntry 1 }

alaFipsVlanNpivLoginStatsRxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Login (FDISC) Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 2 }

alaFipsVlanNpivLoginStatsRxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGI Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 3 }

alaFipsVlanNpivLoginStatsTxFlogiAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGI ACC Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 4 }

alaFipsVlanNpivLoginStatsTxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS RJT Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 5 }

alaFipsVlanNpivLoginStatsTxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FDISC RJT Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 6 }

alaFipsVlanNpivLoginStatsRxLogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGO Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 7 }

alaFipsVlanNpivLoginStatsTxCvls  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Clear Virtual Link Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 8 }

alaFipsVlanNpivLoginStatsRxEkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  ENODE Keep Alive Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 9 }

alaFipsVlanNpivLoginStatsRxVnkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  VN Keep Alive Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 10 }

alaFipsVlanNpivLoginStatsTxFDiscAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FDISC ACC Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 11 }

alaFipsVlanNpivLoginStatsTxFlogoAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGO ACC Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 12 }

alaFipsVlanNpivLoginStatsTxFLogoLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGO RJT Packets on this VLAN."
    ::= { alaFipsVlanNpivLoginStatsEntry 13 }

--
-- AlaFipsIntfNpivLoginStatsTable
--
   alaFipsIntfNpivLoginStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 25 }
alaFipsIntfNpivLoginStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsIntfNpivLoginStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE Port NPIV Login Statistics."
    ::= { alaFipsIntfNpivLoginStats 1}

 alaFipsIntfNpivLoginStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsIntfNpivLoginStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per Port/NPIV Login Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsIntfNpivLoginStatsIfIndex }
    ::= { alaFipsIntfNpivLoginStatsTable 1 }

AlaFipsIntfNpivLoginStatsEntry ::= SEQUENCE {
    alaFipsIntfNpivLoginStatsIfIndex        InterfaceIndex,
    alaFipsIntfNpivLoginStatsRxFlogis       Counter32,
    alaFipsIntfNpivLoginStatsRxFdiscs       Counter32,
    alaFipsIntfNpivLoginStatsTxFlogiAccs    Counter32,
    alaFipsIntfNpivLoginStatsTxFlogiLsRjts  Counter32,
    alaFipsIntfNpivLoginStatsTxFdiscLsRjts  Counter32,
    alaFipsIntfNpivLoginStatsRxLogos        Counter32,
    alaFipsIntfNpivLoginStatsTxCvls         Counter32,
    alaFipsIntfNpivLoginStatsRxEkas         Counter32,
    alaFipsIntfNpivLoginStatsRxVnkas        Counter32,
    alaFipsIntfNpivLoginStatsTxFDiscAccs    Counter32,
    alaFipsIntfNpivLoginStatsTxFlogoAccs    Counter32,
    alaFipsIntfNpivLoginStatsTxFLogoLsRjts  Counter32
}

alaFipsIntfNpivLoginStatsIfIndex  OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Port Id, the Port on which FCoE is enabled."
    ::= { alaFipsIntfNpivLoginStatsEntry 1 }

alaFipsIntfNpivLoginStatsRxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Fabric Login (FDISC) Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 2 }

alaFipsIntfNpivLoginStatsRxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGI Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 3 }

alaFipsIntfNpivLoginStatsTxFlogiAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGI LS ACC Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 4 }

alaFipsIntfNpivLoginStatsTxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS RJT Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 5 }

alaFipsIntfNpivLoginStatsTxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FDISC RJT Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 6 }

alaFipsIntfNpivLoginStatsRxLogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGO Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 7 }

alaFipsIntfNpivLoginStatsTxCvls  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Clear Virtual Link Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 8 }

alaFipsIntfNpivLoginStatsRxEkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  ENODE Keep Alive Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 9 }

alaFipsIntfNpivLoginStatsRxVnkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  VN Keep Alive Packets on this Port."
    ::= { alaFipsIntfNpivLoginStatsEntry 10 }

alaFipsIntfNpivLoginStatsTxFDiscAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FDISC ACC Packets on this VLAN."
    ::= { alaFipsIntfNpivLoginStatsEntry 11 }

alaFipsIntfNpivLoginStatsTxFlogoAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGO ACC Packets on this VLAN."
    ::= { alaFipsIntfNpivLoginStatsEntry 12 }

alaFipsIntfNpivLoginStatsTxFLogoLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FLOGO RJT Packets on this VLAN."
    ::= { alaFipsIntfNpivLoginStatsEntry 13 }

--
-- AlaFipsVlanRnpivDiscStatsTable
--
   alaFipsVlanRnpivDiscStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 26 }
alaFipsVlanRnpivDiscStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsVlanRnpivDiscStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE VLAN R-NPIV FCF Discovery Statistics."
    ::= { alaFipsVlanRnpivDiscStats 1}

 alaFipsVlanRnpivDiscStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVlanRnpivDiscStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per VLan/R-NPIV FCF Discovery Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsVlanRnpivDiscStatsVlanId }
    ::= { alaFipsVlanRnpivDiscStatsTable 1 }

AlaFipsVlanRnpivDiscStatsEntry ::= SEQUENCE {
    alaFipsVlanRnpivDiscStatsVlanId      Integer32,
    alaFipsVlanRnpivDiscStatsRxMdas      Counter32,
    alaFipsVlanRnpivDiscStatsRxUdas      Counter32,
    alaFipsVlanRnpivDiscStatsTxMdss      Counter32,
    alaFipsVlanRnpivDiscStatsTxUdss      Counter32
}

alaFipsVlanRnpivDiscStatsVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled."
    ::= { alaFipsVlanRnpivDiscStatsEntry 1 }

alaFipsVlanRnpivDiscStatsRxMdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Advertisement Packets on this VLAN."
    ::= { alaFipsVlanRnpivDiscStatsEntry 2 }

alaFipsVlanRnpivDiscStatsRxUdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Advertisement Packets on this VLAN."
    ::= { alaFipsVlanRnpivDiscStatsEntry 3 }
alaFipsVlanRnpivDiscStatsTxMdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Solicitation Packets on this VLAN."
    ::= { alaFipsVlanRnpivDiscStatsEntry 4 }

alaFipsVlanRnpivDiscStatsTxUdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Solicitation Packets on this VLAN."
    ::= { alaFipsVlanRnpivDiscStatsEntry 5 }

--
-- AlaFipsIntfRnpivDiscStatsTable
--
   alaFipsIntfRnpivDiscStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 27 }
alaFipsIntfRnpivDiscStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsIntfRnpivDiscStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE Port R-NPIV FCF Discovery Statistics."
    ::= { alaFipsIntfRnpivDiscStats 1}

 alaFipsIntfRnpivDiscStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsIntfRnpivDiscStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per Port/R-NPIV FCF Discovery Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsIntfRnpivDiscStatsIfIndex }
    ::= { alaFipsIntfRnpivDiscStatsTable 1 }

AlaFipsIntfRnpivDiscStatsEntry ::= SEQUENCE {
    alaFipsIntfRnpivDiscStatsIfIndex    InterfaceIndex,
    alaFipsIntfRnpivDiscStatsRxMdas       Counter32,
    alaFipsIntfRnpivDiscStatsRxUdas       Counter32,
    alaFipsIntfRnpivDiscStatsTxMdss       Counter32,
    alaFipsIntfRnpivDiscStatsTxUdss       Counter32
}

alaFipsIntfRnpivDiscStatsIfIndex  OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled."
    ::= { alaFipsIntfRnpivDiscStatsEntry 1 }

alaFipsIntfRnpivDiscStatsRxMdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Advertisement Packets on this Port."
    ::= { alaFipsIntfRnpivDiscStatsEntry 2 }

alaFipsIntfRnpivDiscStatsRxUdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Advertisement Packets on this Port."
    ::= { alaFipsIntfRnpivDiscStatsEntry 3 }
alaFipsIntfRnpivDiscStatsTxMdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Multicast Discovery Solicitation Packets on this Port."
    ::= { alaFipsIntfRnpivDiscStatsEntry 4 }

alaFipsIntfRnpivDiscStatsTxUdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Unicast Discovery Solicitation Packets on this Port."
    ::= { alaFipsIntfRnpivDiscStatsEntry 5 }

--
-- AlaFipsVlanRnpivLoginStatsTable
--
   alaFipsVlanRnpivLoginStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 28 }
alaFipsVlanRnpivLoginStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsVlanRnpivLoginStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE VLAN R-NPIV Node Login Statistics."
    ::= { alaFipsVlanRnpivLoginStats 1}

 alaFipsVlanRnpivLoginStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVlanRnpivLoginStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per VLan/R-NPIV Node Login Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsVlanRnpivLoginStatsVlanId }
    ::= { alaFipsVlanRnpivLoginStatsTable 1 }

AlaFipsVlanRnpivLoginStatsEntry ::= SEQUENCE {
    alaFipsVlanRnpivLoginStatsVlanId          Integer32,
    alaFipsVlanRnpivLoginStatsTxFlogis        Counter32,
    alaFipsVlanRnpivLoginStatsTxFdiscs        Counter32,
    alaFipsVlanRnpivLoginStatsRxLsAccs        Counter32,
    alaFipsVlanRnpivLoginStatsRxFlogiLsRjts   Counter32,
    alaFipsVlanRnpivLoginStatsRxFdiscLsRjts   Counter32,
    alaFipsVlanRnpivLoginStatsRxCvls          Counter32,
    alaFipsVlanRnpivLoginStatsTxLogos         Counter32,
    alaFipsVlanRnpivLoginStatsTxVnkas         Counter32,
    alaFipsVlanRnpivLoginStatsTxEkas          Counter32
}

alaFipsVlanRnpivLoginStatsVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled."
    ::= { alaFipsVlanRnpivLoginStatsEntry 1 }

alaFipsVlanRnpivLoginStatsTxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGI Packets on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 2 }

alaFipsVlanRnpivLoginStatsTxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FDISC Packets on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 3 }

alaFipsVlanRnpivLoginStatsRxLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS ACC Packets on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 4 }

alaFipsVlanRnpivLoginStatsRxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS Rejects Packets for FLOGI on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 5 }

alaFipsVlanRnpivLoginStatsRxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS Rejects Packets for FDISC on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 6 }

alaFipsVlanRnpivLoginStatsRxCvls  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Clear Virtual Link packets on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 7 }

alaFipsVlanRnpivLoginStatsTxLogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Clear Virtual Link packets on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 8 }

alaFipsVlanRnpivLoginStatsTxVnkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  VN Keep Alive Packets on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 9 }

alaFipsVlanRnpivLoginStatsTxEkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  ENODE Keep Alive Packets on this VLAN."
    ::= { alaFipsVlanRnpivLoginStatsEntry 10 }

--
-- AlaFipsIntfRnpivLoginStatsTable
--
   alaFipsIntfRnpivLoginStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 29 }
alaFipsIntfRnpivLoginStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsIntfRnpivLoginStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE Port R-NPIV Node Login Statistics."
    ::= { alaFipsIntfRnpivLoginStats 1}

 alaFipsIntfRnpivLoginStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsIntfRnpivLoginStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per Port/R-NPIV Node Login Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsIntfRnpivLoginStatsIfIndex }
    ::= { alaFipsIntfRnpivLoginStatsTable 1 }

AlaFipsIntfRnpivLoginStatsEntry ::= SEQUENCE {
    alaFipsIntfRnpivLoginStatsIfIndex         InterfaceIndex,
    alaFipsIntfRnpivLoginStatsTxFlogis        Counter32,
    alaFipsIntfRnpivLoginStatsTxFdiscs        Counter32,
    alaFipsIntfRnpivLoginStatsRxLsAccs        Counter32,
    alaFipsIntfRnpivLoginStatsRxFlogiLsRjts   Counter32,
    alaFipsIntfRnpivLoginStatsRxFdiscLsRjts   Counter32,
    alaFipsIntfRnpivLoginStatsRxCvls          Counter32,
    alaFipsIntfRnpivLoginStatsTxLogos         Counter32,
    alaFipsIntfRnpivLoginStatsTxVnkas         Counter32,
    alaFipsIntfRnpivLoginStatsTxEkas          Counter32
}

alaFipsIntfRnpivLoginStatsIfIndex  OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Port Id, the Port on which FCoE is enabled."
    ::= { alaFipsIntfRnpivLoginStatsEntry 1 }

alaFipsIntfRnpivLoginStatsTxFlogis  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of FLOGI Packets on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 2 }

alaFipsIntfRnpivLoginStatsTxFdiscs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  FDISC Packets on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 3 }

alaFipsIntfRnpivLoginStatsRxLsAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS ACC Packets on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 4 }

alaFipsIntfRnpivLoginStatsRxFlogiLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS Rejects Packets for FLOGI on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 5 }

alaFipsIntfRnpivLoginStatsRxFdiscLsRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  LS Rejects Packets for FDISC on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 6 }

alaFipsIntfRnpivLoginStatsRxCvls  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Clear Virtual Link packets on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 7 }

alaFipsIntfRnpivLoginStatsTxLogos  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  Clear Virtual Link packets on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 8 }

alaFipsIntfRnpivLoginStatsTxVnkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  VN Keep Alive Packets on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 9 }

alaFipsIntfRnpivLoginStatsTxEkas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of  ENODE Keep Alive Packets on this Port."
    ::= { alaFipsIntfRnpivLoginStatsEntry 10 }

--
-- AlaFipsEtunnelVePortStatsTable
--
   alaFipsEtunnelVePortStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 30 }
alaFipsEtunnelVePortStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsEtunnelVePortStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE E-Tunnel E-Port Statistics."
    ::= { alaFipsEtunnelVePortStats 1}

 alaFipsEtunnelVePortStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsEtunnelVePortStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per E-Tunnel E-Port Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsEtunnelVePortStatsTunnelId }
    ::= { alaFipsEtunnelVePortStatsTable 1 }

AlaFipsEtunnelVePortStatsEntry ::= SEQUENCE {
    alaFipsEtunnelVePortStatsTunnelId      Unsigned32,
    alaFipsEtunnelVePortStatsRxMdss        Counter32,
    alaFipsEtunnelVePortStatsRxUdss        Counter32,
    alaFipsEtunnelVePortStatsRxMdas        Counter32,
    alaFipsEtunnelVePortStatsRxUdas        Counter32,
    alaFipsEtunnelVePortStatsRxElpReqs     Counter32,
    alaFipsEtunnelVePortStatsRxSwAccs      Counter32,
    alaFipsEtunnelVePortStatsRxSwRjts      Counter32,
    alaFipsEtunnelVePortStatsRxCvls        Counter32,
    alaFipsEtunnelVePortStatsTxMdss        Counter32,
    alaFipsEtunnelVePortStatsTxUdss        Counter32,
    alaFipsEtunnelVePortStatsTxMdas        Counter32,
    alaFipsEtunnelVePortStatsTxUdas        Counter32,
    alaFipsEtunnelVePortStatsTxElpReqs     Counter32,
    alaFipsEtunnelVePortStatsTxSwAccs      Counter32,
    alaFipsEtunnelVePortStatsTxSwRjts      Counter32,
    alaFipsEtunnelVePortStatsTxCvls        Counter32,
    alaFipsEtunnelVePortStatsClear         INTEGER
}

alaFipsEtunnelVePortStatsTunnelId  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE E-Tunnel Id."
    ::= { alaFipsEtunnelVePortStatsEntry 1 }

alaFipsEtunnelVePortStatsRxMdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Multicast Discovery Solicitation Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 2 }

alaFipsEtunnelVePortStatsRxUdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Unicast Discovery Solicitation Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 3 }

alaFipsEtunnelVePortStatsRxMdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Multicast Discovery Advertisements Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 4 }

alaFipsEtunnelVePortStatsRxUdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Unicast Discovery Advertisement Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 5 }

alaFipsEtunnelVePortStatsRxElpReqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of ELP Request Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 6 }

alaFipsEtunnelVePortStatsRxSwAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Accept Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 7 }

alaFipsEtunnelVePortStatsRxSwRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Reject Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 8 }

alaFipsEtunnelVePortStatsRxCvls  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Clear Virtual Link Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 9 }


alaFipsEtunnelVePortStatsTxMdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Multicast Discovery Solicitation Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 10 }

alaFipsEtunnelVePortStatsTxUdss  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Unicast Discovery Solicitation Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 11 }

alaFipsEtunnelVePortStatsTxMdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Multicast Discovery Advertisements Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 12 }

alaFipsEtunnelVePortStatsTxUdas  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Unicast Discovery Advertisement Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 13 }

alaFipsEtunnelVePortStatsTxElpReqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of ELP Request Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 14 }

alaFipsEtunnelVePortStatsTxSwAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Accept Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 15 }

alaFipsEtunnelVePortStatsTxSwRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Reject Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 16 }

alaFipsEtunnelVePortStatsTxCvls  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Clear Virtual Link Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelVePortStatsEntry 17 }


alaFipsEtunnelVePortStatsClear  OBJECT-TYPE
    SYNTAX  INTEGER {
        clear(1),
        none(2)
    }
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Clear VE-Port statistics of this E-Tunnel."
        DEFVAL { none }
    ::= { alaFipsEtunnelVePortStatsEntry 18 }

--
-- AlaFipsEtunnelTePortStatsTable
--
   alaFipsEtunnelTePortStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 31 }
alaFipsEtunnelTePortStatsTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsEtunnelTePortStatsEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of FCoE E-Tunnel E-Port Statistics."
    ::= { alaFipsEtunnelTePortStats 1}

 alaFipsEtunnelTePortStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFipsEtunnelTePortStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "Fips per E-Tunnel E-Port Statistics Entry. All Counters starts from 0 when they rollover."
    INDEX { alaFipsEtunnelTePortStatsTunnelId }
    ::= { alaFipsEtunnelTePortStatsTable 1 }

AlaFipsEtunnelTePortStatsEntry ::= SEQUENCE {
    alaFipsEtunnelTePortStatsTunnelId      Unsigned32,
    alaFipsEtunnelTePortStatsRxElpReqs     Counter32,
    alaFipsEtunnelTePortStatsRxSwAccs      Counter32,
    alaFipsEtunnelTePortStatsRxSwRjts      Counter32,
    alaFipsEtunnelTePortStatsTxElpReqs     Counter32,
    alaFipsEtunnelTePortStatsTxSwAccs      Counter32,
    alaFipsEtunnelTePortStatsTxSwRjts      Counter32,
    alaFipsEtunnelTePortStatsClear         INTEGER
}

alaFipsEtunnelTePortStatsTunnelId  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE E-Tunnel Id."
    ::= { alaFipsEtunnelTePortStatsEntry 1 }


alaFipsEtunnelTePortStatsRxElpReqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of ELP Request Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelTePortStatsEntry 2 }

alaFipsEtunnelTePortStatsRxSwAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Accept Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelTePortStatsEntry 3 }

alaFipsEtunnelTePortStatsRxSwRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Clear Virtual Link Rx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelTePortStatsEntry 4 }

alaFipsEtunnelTePortStatsTxElpReqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of ELP Request Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelTePortStatsEntry 5 }

alaFipsEtunnelTePortStatsTxSwAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Accept Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelTePortStatsEntry 6 }

alaFipsEtunnelTePortStatsTxSwRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of Clear Virtual Link Tx Packets on this E-Tunnel."
    ::= { alaFipsEtunnelTePortStatsEntry 7 }

alaFipsEtunnelTePortStatsClear  OBJECT-TYPE
    SYNTAX  INTEGER {
        clear(1),
        none(2)
    }
    MAX-ACCESS read-write 
    STATUS  current
    DESCRIPTION
        "Clear TE-Port statistics of this E-Tunnel."
        DEFVAL { none }
    ::= { alaFipsEtunnelTePortStatsEntry 8 }

--
--AlaFipsVsanVlanMapTable
--

   alaFipsVsanVlanMap OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 32 }

alaFipsVsanVlanMapTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsVsanVlanMapEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the Rows of VSAN and FCoE VLAN mapping."
        ::= { alaFipsVsanVlanMap 1}
		
 alaFipsVsanVlanMapEntry  OBJECT-TYPE
    SYNTAX  AlaFipsVsanVlanMapEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of VSAN and FCoE VLAN mappings."
    INDEX { alaFipsVsanVlanMapVsanNumber }
    ::= { alaFipsVsanVlanMapTable 1 }
	
AlaFipsVsanVlanMapEntry ::= SEQUENCE {
    alaFipsVsanVlanMapVsanNumber    Integer32,
    alaFipsVsanVlanMapVlanNumber    Integer32,
    alaFipsVsanVlanMapRowStatus     RowStatus
}

alaFipsVsanVlanMapVsanNumber OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "The VSAN number identifying this instance. Valid
         range from 1 to 4094."
    ::= { alaFipsVsanVlanMapEntry 1 }

alaFipsVsanVlanMapVlanNumber OBJECT-TYPE
    SYNTAX  Integer32 (0..4094)
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "The VLAN number."
    ::= { alaFipsVsanVlanMapEntry 2 }

alaFipsVsanVlanMapRowStatus OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Row status controls the creation and deletion of rows in the table FC alaFipsVsanVlanMapTable."
    ::= { alaFipsVsanVlanMapEntry 3 }

--
-- AlaFipsDiscAdvtTable
--
   alaFipsDiscAdvt OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 33 }
alaFipsDiscAdvtTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsDiscAdvtEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of Discovery Advertisement configuration per VLAN."
    ::= { alaFipsDiscAdvt 1}

 alaFipsDiscAdvtEntry  OBJECT-TYPE
    SYNTAX  AlaFipsDiscAdvtEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Discovery Advertisement configuration Entry."
    INDEX { alaFipsDiscAdvtVlanId }
    ::= { alaFipsDiscAdvtTable 1 }

AlaFipsDiscAdvtEntry ::= SEQUENCE {
    alaFipsDiscAdvtVlanId          Integer32,
    alaFipsDiscAdvtAbit            INTEGER,
    alaFipsDiscAdvtFkaAdvPeriod    Integer32,
    alaFipsDiscAdvtPriority        Unsigned32,
    alaFipsDiscAdvtUdsRetries      Unsigned32,
    alaFipsDiscAdvtRowStatus       RowStatus
}

alaFipsDiscAdvtVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE is enabled."
    ::= { alaFipsDiscAdvtEntry 1 }

alaFipsDiscAdvtAbit  OBJECT-TYPE
    SYNTAX  INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
       "A-Bit value of FIP Discovery Advertisement packets transmitted on this VLAN."
    ::= { alaFipsDiscAdvtEntry 2 }

alaFipsDiscAdvtFkaAdvPeriod  OBJECT-TYPE
    SYNTAX  Integer32 (1 .. 90)
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
       "Period (in seconds) of FIP Discovery Advertisement packets transmitted and FIP ENODE Keep Alive
        packes expected on this VLAN."
	DEFVAL {8}
    ::= { alaFipsDiscAdvtEntry 3 }

alaFipsDiscAdvtPriority  OBJECT-TYPE
    SYNTAX  Unsigned32 (0..255)
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
       "Value of priority in FIP Discovery Advertisement packets transmitted on this VLAN."
	DEFVAL {128}
    ::= { alaFipsDiscAdvtEntry 4 }

alaFipsDiscAdvtUdsRetries  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
       "Number of times an unicast discovery solicitation is transmitted after
        a port, on which an FCF MAC is learnt, goes down.  This helps to
        find, if the same FCF is reachable on some other port of the same vlan."
    ::= { alaFipsDiscAdvtEntry 5 }

alaFipsDiscAdvtRowStatus  OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Row status controls the creation and deletion of rows in the table Fips alaFipsDiscAdvtTable"
    ::= { alaFipsDiscAdvtEntry 6 }

--
-- AlaFipsEtunnelTable
--
   alaFipsEtunnel OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 34 }
alaFipsEtunnelTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF AlaFipsEtunnelEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table has the Rows of E-Tunnels."
    ::= { alaFipsEtunnel 1}

 alaFipsEtunnelEntry  OBJECT-TYPE
    SYNTAX  AlaFipsEtunnelEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE E-Tunnel configuration Entry."
    INDEX { alaFipsEtunnelId }
    ::= { alaFipsEtunnelTable 1 }

AlaFipsEtunnelEntry ::= SEQUENCE {
    alaFipsEtunnelId            Unsigned32,
    alaFipsEtunnelVlanId        Integer32,
    alaFipsEtunnelIfIndexOne    InterfaceIndexOrZero,
    alaFipsEtunnelIfIndexTwo    InterfaceIndexOrZero,
    alaFipsEtunnelRowStatus     RowStatus,
    alaFipsEtunnelStatsClear    INTEGER
}

alaFipsEtunnelId  OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "FCoE E-Tunnel Id."
    ::= { alaFipsEtunnelEntry 1 }

alaFipsEtunnelVlanId  OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "FCoE Vlan Id, the Vlan on which FCoE E-Tunnel is defined."
    ::= { alaFipsEtunnelEntry 2 }

alaFipsEtunnelIfIndexOne  OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
       "IfIndex of the one of the end points of the E-Tunnel."
    ::= { alaFipsEtunnelEntry 3 }

alaFipsEtunnelIfIndexTwo  OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
       "IfIndex of the one of the end points of the E-Tunnel."
    ::= { alaFipsEtunnelEntry 4 }

alaFipsEtunnelRowStatus  OBJECT-TYPE
    SYNTAX  RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " Row status controls the creation and deletion of rows in the table alaFipsEtunnelTable."
    ::= { alaFipsEtunnelEntry 5 }

alaFipsEtunnelStatsClear  OBJECT-TYPE
    SYNTAX  INTEGER {
        clear(1),
        none(2)
    }
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "FCoE E-Tunnel Statistics Clear Object."
        DEFVAL { none }
    ::= { alaFipsEtunnelEntry 6}



--
--AlaFipsNpivSessionTable
--
   alaFipsNpivSession OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 35 }
alaFipsNpivSessionTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsNpivSessionEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the list of Session in the system."
        ::= { alaFipsNpivSession 1}
		
 alaFipsNpivSessionEntry  OBJECT-TYPE
    SYNTAX  AlaFipsNpivSessionEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FCoE NPIV Sessions."
    INDEX { alaFipsNpivSessionEnodeMAC, alaFipsNpivSessionVNMAC,   alaFipsNpivSessionVlanId }
    ::= { alaFipsNpivSessionTable 1 }

AlaFipsNpivSessionEntry ::= SEQUENCE {
    alaFipsNpivSessionEnodeMAC      MacAddress,
    alaFipsNpivSessionVNMAC         MacAddress,
    alaFipsNpivSessionVlanId        Integer32,
    alaFipsNpivSessionInIfIndex     InterfaceIndex,
    alaFipsNpivSessionOutIfIndex    InterfaceIndex,
    alaFipsNpivSessionFCFMAC        MacAddress,
    alaFipsNpivSessionStatus        INTEGER,
    alaFipsNpivSessionLoginTimeDate DateAndTime
}
alaFipsNpivSessionEnodeMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " Enode MAC where this NPIV session exists."
    ::= { alaFipsNpivSessionEntry 1 }

alaFipsNpivSessionVNMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " VN MAC address of the Session."
    ::= { alaFipsNpivSessionEntry 2 }

alaFipsNpivSessionVlanId OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FCF VLAN ID of the Session."
    ::= { alaFipsNpivSessionEntry 3 }

alaFipsNpivSessionInIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCoE interface on which FCoE NPIV session exists, it can be indivisual Port /Linkagg."
    ::= { alaFipsNpivSessionEntry 4 }
	
alaFipsNpivSessionOutIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Interface on which FCoE NPIV session exists."
    ::= { alaFipsNpivSessionEntry 5 }
	
	
alaFipsNpivSessionFCFMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " FCF MAC address of the Session."
	DEFVAL {'000000000000'H}
    ::= { alaFipsNpivSessionEntry 6 }

	
alaFipsNpivSessionStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        pending(1),
        created(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCoE NPIV Session Pending or Active."
	DEFVAL {pending}
    ::= { alaFipsNpivSessionEntry 7 }

alaFipsNpivSessionLoginTimeDate OBJECT-TYPE
    SYNTAX  DateAndTime
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Date and time of this session login"
    ::= { alaFipsNpivSessionEntry 8 }


--
--AlaFipsRnpivSessionTable
--
   alaFipsRnpivSession OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 36 }
alaFipsRnpivSessionTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsRnpivSessionEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the list of Session in the system."
        ::= { alaFipsRnpivSession 1}
		
 alaFipsRnpivSessionEntry  OBJECT-TYPE
    SYNTAX  AlaFipsRnpivSessionEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FCoE R-NPIV Sessions."
    INDEX { alaFipsRnpivSessionFcid, alaFipsRnpivSessionVsanId }
    ::= { alaFipsRnpivSessionTable 1 }

AlaFipsRnpivSessionEntry ::= SEQUENCE {
    alaFipsRnpivSessionFcid          Fcid,
    alaFipsRnpivSessionVsanId        Integer32,
    alaFipsRnpivSessionVNMAC         MacAddress,
    alaFipsRnpivSessionVlanId        Integer32,
    alaFipsRnpivSessionInIfIndex     InterfaceIndex,
    alaFipsRnpivSessionOutIfIndex    InterfaceIndex,
    alaFipsRnpivSessionFCFMAC        MacAddress,
    alaFipsRnpivSessionStatus        INTEGER,
    alaFipsRnpivSessionLoginTimeDate DateAndTime
}

alaFipsRnpivSessionFcid OBJECT-TYPE
    SYNTAX  Fcid
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FC Port ID received after succesful Fabric login.  It is a 3 byte value."
    ::= { alaFipsRnpivSessionEntry 1 }

alaFipsRnpivSessionVsanId OBJECT-TYPE
    SYNTAX  Integer32 (1..4094)
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "The VSAN number identifying this instance. Valid
         range from 1 to 4094."
    ::= { alaFipsRnpivSessionEntry 2 }

alaFipsRnpivSessionVNMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " VN MAC address of the Session."
    ::= { alaFipsRnpivSessionEntry 3 }

alaFipsRnpivSessionVlanId OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCF VLAN ID of the Session."
    ::= { alaFipsRnpivSessionEntry 4 }

alaFipsRnpivSessionInIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCoE interface on which FCoE R-NPIV session exists, it can be indivisual Port /Linkagg.  This
          port can be a Fibre port when the FC Switch is dirctly conected to the AOS."
    ::= { alaFipsRnpivSessionEntry 5 }
	
alaFipsRnpivSessionOutIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Interface on which FCoE R-NPIV session exists."
    ::= { alaFipsRnpivSessionEntry 6 }
	
	
alaFipsRnpivSessionFCFMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCF MAC address of the Session."
	DEFVAL {'000000000000'H}
    ::= { alaFipsRnpivSessionEntry 7 }

	
alaFipsRnpivSessionStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        pending(1),
        created(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCoE R-NPIV Session Pending or Active."
	DEFVAL {pending}
    ::= { alaFipsRnpivSessionEntry 8 }

alaFipsRnpivSessionLoginTimeDate OBJECT-TYPE
    SYNTAX  DateAndTime
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Date and time of this session login"
    ::= { alaFipsRnpivSessionEntry 9 }

--
--AlaFipsEtunnelSessionTable
--
   alaFipsEtunnelSession OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 37 }
alaFipsEtunnelSessionTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFipsEtunnelSessionEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table has the list of FCoE E-Tunnel Session in the system."
        ::= { alaFipsEtunnelSession 1}
		
 alaFipsEtunnelSessionEntry  OBJECT-TYPE
    SYNTAX  AlaFipsEtunnelSessionEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FCoE E-Tunnel Sessions."
    INDEX { alaFipsEtunnelSessionTunnelId }
    ::= { alaFipsEtunnelSessionTable 1 }

AlaFipsEtunnelSessionEntry ::= SEQUENCE {
    alaFipsEtunnelSessionTunnelId      Unsigned32,
    alaFipsEtunnelSessionVlanId        Integer32,
    alaFipsEtunnelSessionInIfIndex     InterfaceIndex,
    alaFipsEtunnelSessionOutIfIndex    InterfaceIndex,
    alaFipsEtunnelSessionFCFMAC        MacAddress,
    alaFipsEtunnelSessionStatus        INTEGER,
    alaFipsEtunnelSessionLoginTimeDate DateAndTime,
    alaFipsEtunnelSessionPairMode      INTEGER
}

alaFipsEtunnelSessionTunnelId OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FCoE E-Tunnel ID of the Session."
    ::= { alaFipsEtunnelSessionEntry 1 }

alaFipsEtunnelSessionVlanId OBJECT-TYPE
    SYNTAX  Integer32 (0 .. 4094)
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCF VLAN ID of the Session."
    ::= { alaFipsEtunnelSessionEntry 2 }

alaFipsEtunnelSessionInIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCoE interface on which FCoE E-Tunnel session exists, it can be indivisual Port /Linkagg.  This
          port can be a Fibre port when the FC Switch is dirctly conected to the AOS."
    ::= { alaFipsEtunnelSessionEntry 3 }
	
alaFipsEtunnelSessionOutIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FC Interface on which FCoE E-Tunnel session exists."
    ::= { alaFipsEtunnelSessionEntry 4 }
	
	
alaFipsEtunnelSessionFCFMAC OBJECT-TYPE
    SYNTAX  MacAddress
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        " FCF MAC address of the Session."
	DEFVAL {'000000000000'H}
    ::= { alaFipsEtunnelSessionEntry 5 }

	
alaFipsEtunnelSessionStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        pending(1),
        created(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " FCoE E-Tunnel Session Pending or Active."
	DEFVAL {pending}
    ::= { alaFipsEtunnelSessionEntry 6 }

alaFipsEtunnelSessionLoginTimeDate OBJECT-TYPE
    SYNTAX  DateAndTime
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Date and time of this session login"
    ::= { alaFipsEtunnelSessionEntry 7 }

alaFipsEtunnelSessionPairMode OBJECT-TYPE
    SYNTAX  INTEGER {
        teToVe(1),
        te-to-te(2)
    }
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Type of the E-Tunnel."
    ::= { alaFipsEtunnelSessionEntry 8 }

--
--AlaFcNpivLoadBalSessTable
--

   alaFcNpivLoadBalSess OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 38 }

alaFcNpivLoadBalSessTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcNpivLoadBalSessEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table maintains count of sessions opened on each FC port of type 'NP'."
        ::= { alaFcNpivLoadBalSess 1}
		
 alaFcNpivLoadBalSessEntry  OBJECT-TYPE
    SYNTAX  AlaFcNpivLoadBalSessEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of FC Interface FCoE interface mappings."
    INDEX { alaFcNpivLoadBalSessIfIndex }
    ::= { alaFcNpivLoadBalSessTable 1 }
	
AlaFcNpivLoadBalSessEntry ::= SEQUENCE {
    alaFcNpivLoadBalSessIfIndex    InterfaceIndex,
    alaFcNpivLoadBalSessCount      Unsigned32
}

alaFcNpivLoadBalSessIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FC interface of type NP type."
    ::= { alaFcNpivLoadBalSessEntry 1 }

alaFcNpivLoadBalSessCount OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " Number of sessions per FC interface of type NP type."
    ::= { alaFcNpivLoadBalSessEntry 2 }

--
--AlaFcIntfEtunnelStatsTable
--

   alaFcIntfEtunnelStats OBJECT IDENTIFIER ::= { alcatelIND1FipsMIBObjects 39 }

alaFcIntfEtunnelStatsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF AlaFcIntfEtunnelStatsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table maintains statistics of E-Tunnel per port Id on FC side."
        ::= { alaFcIntfEtunnelStats 1}
		
 alaFcIntfEtunnelStatsEntry  OBJECT-TYPE
    SYNTAX  AlaFcIntfEtunnelStatsEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "List of objects of FC side E-Tunnel statistics per port."
    INDEX { alaFcIntfEtunnelStatsIfIndex }
    ::= { alaFcIntfEtunnelStatsTable 1 }
	
AlaFcIntfEtunnelStatsEntry ::= SEQUENCE {
    alaFcIntfEtunnelStatsIfIndex    InterfaceIndex,
    alaFcIntfEtunnelStatsTunnelId   Unsigned32,
    alaFcIntfEtunnelStatsRxElpReqs  Counter32,
    alaFcIntfEtunnelStatsRxSwAccs   Counter32,
    alaFcIntfEtunnelStatsRxSwRjts   Counter32,
    alaFcIntfEtunnelStatsTxElpReqs  Counter32,
    alaFcIntfEtunnelStatsTxSwAccs   Counter32,
    alaFcIntfEtunnelStatsTxSwRjts   Counter32,
    alaFcIntfEtunnelStatsClear      INTEGER
}

alaFcIntfEtunnelStatsIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndex
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        " FC Port."
    ::= { alaFcIntfEtunnelStatsEntry 1 }

alaFcIntfEtunnelStatsTunnelId OBJECT-TYPE
    SYNTAX  Unsigned32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        " E-Tunnel ID."
    ::= { alaFcIntfEtunnelStatsEntry 2 }

alaFcIntfEtunnelStatsRxElpReqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of ELP Request Rx Packets on this port."
    ::= { alaFcIntfEtunnelStatsEntry 3 }

alaFcIntfEtunnelStatsRxSwAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Accept Rx Packets on this port."
    ::= { alaFcIntfEtunnelStatsEntry 4 }

alaFcIntfEtunnelStatsRxSwRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Reject Rx Packets on this port."
    ::= { alaFcIntfEtunnelStatsEntry 5 }

alaFcIntfEtunnelStatsTxElpReqs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of ELP Request Tx Packets on this port."
    ::= { alaFcIntfEtunnelStatsEntry 6 }

alaFcIntfEtunnelStatsTxSwAccs  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Accept Tx Packets on this port."
    ::= { alaFcIntfEtunnelStatsEntry 7 }

alaFcIntfEtunnelStatsTxSwRjts  OBJECT-TYPE
    SYNTAX  Counter32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
        "Number of SW Reject Tx Packets on this port."
    ::= { alaFcIntfEtunnelStatsEntry 8 }

alaFcIntfEtunnelStatsClear  OBJECT-TYPE
    SYNTAX  INTEGER {
        clear(1),
        none(2)
    }
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "Clear ETunnel statistics on  this Intf"
        DEFVAL { none }
    ::= { alaFcIntfEtunnelStatsEntry 9 }


-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- COMPLIANCE
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

   alcatelIND1FipsMIBCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
            "Compliance statement for FIP Snooping."
        MODULE
            MANDATORY-GROUPS
            {
                alaFipsInfoGroup,
                alaFipsVlanGroup,
                alaFipsVlanEnodeStatsGroup,
                alaFipsVlanFcfStatsGroup,
                alaFipsIntfGroup,
                alaFipsIntfEnodeStatsGroup,
                alaFipsIntfFcfStatsGroup,
		alaFipsFcfGroup,
		alaFipsSessionGroup,
		alaFipsNotificationGroup,
		alaFipsNotificationObjectGroup,
		alaFcVsanGroup,
		alaFcVfpaGroup,
		alaFcIntfGroup,
		alaFcNpivStaticLoadBalanceGroup,
		alaFcNodeGroup,
		alaFcSessGroup,
		alaFcIntfNpivStatsGroup,
		alaFcVsanNpivStatsGroup,
		alaFcIntfRnpivStatsGroup,
		alaFcVsanRnpivStatsGroup,
		alaFcInfoGroup,
		alaFipsVlanNpivDiscStatsGroup,
		alaFipsIntfNpivDiscStatsGroup,
		alaFipsVlanNpivLoginStatsGroup,
		alaFipsIntfNpivLoginStatsGroup,
		alaFipsVlanRnpivDiscStatsGroup,
		alaFipsIntfRnpivDiscStatsGroup,
		alaFipsVlanRnpivLoginStatsGroup,
		alaFipsIntfRnpivLoginStatsGroup,
		alaFipsEtunnelVePortStatsGroup,
		alaFipsEtunnelTePortStatsGroup,
		alaFipsVsanVlanMapGroup,
		alaFipsDiscAdvtGroup,
		alaFipsEtunnelGroup,
		alaFipsNpivSessionGroup,
		alaFipsRnpivSessionGroup,
		alaFipsEtunnelSessionGroup,
		alaFcNpivLoadBalSessGroup,
                alaFcIntfEtunnelStatsGroup 
            }

        ::= { alcatelIND1FipsMIBCompliances 1 }
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- UNITS OF CONFORMANCE
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

    alaFipsInfoGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsConfigFilterResourceLimit,
		alaFipsConfigFIPSAdmin,
		alaFipsConfigAddressMode,
		alaFipsConfigPriorityOne,
		alaFipsConfigPriorityTwo,
		alaFipsTotalNumFilterResource,
		alaFipsUsedNumFilterResource,
		alaFipsConfigStatsClear,
		alaFipsConfigPrioProtection,
		alaFipsConfigPriorityProtectionAction,
		alaFipsConfigPriorityProtectionRemarkVal,
		alaFipsConfigHouseKeepingTimePeriod,
		alaFipsConfigSWReinsertStatus,
		alaFipsConfigSessClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping Global setup."
        ::= { alcatelIND1FipsMIBGroups 1 }



    alaFipsVlanGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVlanFCMap,
		alaFipsVlanStatsClear,
		alaFipsVlanStatsFnreClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping VLANs."
        ::= { alcatelIND1FipsMIBGroups 2 }

    alaFipsVlanEnodeStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVlanEnodeStatsSessions,
		alaFipsVlanEnodeStatsMds,
		alaFipsVlanEnodeStatsUds,
		alaFipsVlanEnodeStatsFlogi,
		alaFipsVlanEnodeStatsFdisc,
		alaFipsVlanEnodeStatsLogo,
		alaFipsVlanEnodeStatsEka,
		alaFipsVlanEnodeStatsVnka,
		alaFipsVlanEnodeStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping VLANs Enode statistics."
        ::= { alcatelIND1FipsMIBGroups 3 }

    alaFipsVlanFcfStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVlanFcfStatsSessions,
		alaFipsVlanFcfStatsMda,
		alaFipsVlanFcfStatsUda,
		alaFipsVlanFcfStatsFlogiAcc,
		alaFipsVlanFcfStatsFlogiRjt,
		alaFipsVlanFcfStatsFdiscRjt,
		alaFipsVlanFcfStatsLogoAcc,
		alaFipsVlanFcfStatsLogoRjt,
		alaFipsVlanFcfStatsCvl,
		alaFipsVlanFcfStatsClear,
		alaFipsVlanFcfStatsFdiscAcc
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping VLANs FCF statistics."
        ::= { alcatelIND1FipsMIBGroups 4 }


    alaFipsIntfGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsIntfOperStatus,
		alaFipsIntfPortRole,
		alaFipsIntfRowStatus,
		alaFipsIntfStatsClear,
		alaFipsIntfStatsFnreClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping interfaces."
        ::= { alcatelIND1FipsMIBGroups 5 }


    alaFipsIntfEnodeStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsIntfEnodeStatsSessions,
		alaFipsIntfEnodeStatsMds,
		alaFipsIntfEnodeStatsUds,
		alaFipsIntfEnodeStatsFlogi,
		alaFipsIntfEnodeStatsFdisc,
		alaFipsIntfEnodeStatsLogo,
		alaFipsIntfEnodeStatsEka,
		alaFipsIntfEnodeStatsVnka,
		alaFipsIntfEnodeStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping interfaces for Enode statistics."
        ::= { alcatelIND1FipsMIBGroups 6 }


    alaFipsIntfFcfStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsIntfFcfStatsSessions,
		alaFipsIntfFcfStatsMda,
		alaFipsIntfFcfStatsUda,
		alaFipsIntfFcfStatsFlogiAcc,
		alaFipsIntfFcfStatsFlogiRjt,
		alaFipsIntfFcfStatsFdiscRjt,
		alaFipsIntfFcfStatsLogoAcc,
		alaFipsIntfFcfStatsLogoRjt,
		alaFipsIntfFcfStatsCvl,
		alaFipsIntfFcfStatsClear,
		alaFipsIntfFcfStatsFdiscAcc
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping interfaces for FCF statistics."
        ::= { alcatelIND1FipsMIBGroups 7 }

    alaFipsFcfGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsFcfIntf,
		alaFipsFcfSessions,
		alaFipsFcfConfigType,
		alaFipsFcfRowStatus,
		alaFipsFcfAvailForLogin,
		alaFipsFcfMaxFcoeFrmSizeVerified,
		alaFipsFcfPriority,
		alaFipsFcfType
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping FCF information."
        ::= { alcatelIND1FipsMIBGroups 8 }

    alaFipsSessionGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsSessionIfIndex,
		alaFipsSessionFCFMAC,
		alaFipsSessionStatus,
		alaFipsSessionLoginTime,
		alaFipsSessionLoginTimeDate
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FIP Snooping session."
        ::= { alcatelIND1FipsMIBGroups 9 }

   alaFipsNotificationObjectGroup OBJECT-GROUP
        OBJECTS
        {
                alaFipsFilterResourceUsage
        }
        STATUS current
        DESCRIPTION
                "Collection of trap objects for management of FIP Snooping."
        ::= {  alcatelIND1FipsMIBGroups 10}


   alaFipsNotificationGroup NOTIFICATION-GROUP
        NOTIFICATIONS
        {
                  alaFipsResourceThresholdReached
        }
        STATUS  current
        DESCRIPTION
        "Collection of notifications for FIP Snooping."
        ::= { alcatelIND1FipsMIBGroups 11 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

-- NPIV, R-NPIV, E-TUNNEL

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    alaFcVsanGroup OBJECT-GROUP
        OBJECTS
        {
                alaFcVsanDescription,
                alaFcVsanAdmStatus,
                alaFcVsanOperStatus,
                alaFcVsanRowStatus
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of VSANs."
        ::= { alcatelIND1FipsMIBGroups 12 }

    alaFcVfpaGroup OBJECT-GROUP
        OBJECTS
        {
                alaFcVfpaState,
                alaFcVfpaRowStatus
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of VSAN VFPAs."
        ::= { alcatelIND1FipsMIBGroups 13 }

    alaFcIntfGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcIntfOperStatus,
		alaFcIntfMode,
		alaFcIntfBbScN,
		alaFcIntfClassOfService,
		alaFcIntfFcid,
		alaFcIntfWwpn,
		alaFcIntfLoginState,
		alaFcIntfRowStatus,
		alaFcIntfStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of Fibre Channel ports."
        ::= { alcatelIND1FipsMIBGroups 14 }

    alaFcNpivStaticLoadBalanceGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcNpivStaticLoadBalanceRowStatus
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of Fibre Channel ports."
        ::= { alcatelIND1FipsMIBGroups 15 }



    alaFcNodeGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcNodeVsanNumber,
		alaFcNodeVlanNumber,
		alaFcNodeFcid,	
		alaFcNodeWwnn
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of connected Fibre Channel nodes."
        ::= { alcatelIND1FipsMIBGroups 16 }

    alaFcSessGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcSessVsanNumber,
		alaFcSessStatus,
		alaFcSessIntfMode,
		alaFcSessFcid,	
		alaFcSessType,
		alaFcSessTunnelId
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of connected Fibre Channel nodes."
        ::= { alcatelIND1FipsMIBGroups 17 }

    alaFcIntfNpivStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcIntfNpivStatsTxFlogis,
		alaFcIntfNpivStatsTxFdiscs,
		alaFcIntfNpivStatsRxLsAccs,
		alaFcIntfNpivStatsRxFlogos,
		alaFcIntfNpivStatsRxFlogiLsRjts,
		alaFcIntfNpivStatsRxFdiscLsRjts,
		alaFcIntfNpivStatsTxFlogos,
		alaFcIntfNpivStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FC NPIV statistics on FC Ports."
        ::= { alcatelIND1FipsMIBGroups 18 }

    alaFcVsanNpivStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcVsanNpivStatsTxFlogis,
		alaFcVsanNpivStatsTxFdiscs,
		alaFcVsanNpivStatsRxLsAccs,
		alaFcVsanNpivStatsRxFlogos,
		alaFcVsanNpivStatsRxFlogiLsRjts,
		alaFcVsanNpivStatsRxFdiscLsRjts,
		alaFcVsanNpivStatsTxFlogos
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FC NPIV statistics on VSANS." 
        ::= { alcatelIND1FipsMIBGroups 19 }


    alaFcIntfRnpivStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcIntfRnpivStatsRxFlogis,
		alaFcIntfRnpivStatsRxFdiscs,
		alaFcIntfRnpivStatsTxFlogiLsAccs,
		alaFcIntfRnpivStatsTxFdiscLsAccs,
		alaFcIntfRnpivStatsTxFlogos,
		alaFcIntfRnpivStatsTxFlogiLsRjts,
		alaFcIntfRnpivStatsTxFdiscLsRjts,
		alaFcIntfRnpivStatsRxFlogos,
		alaFcIntfRnpivStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FC R-NPIV statistics on FC Ports."
        ::= { alcatelIND1FipsMIBGroups 20 }

    alaFcVsanRnpivStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcVsanRnpivStatsRxFlogis,
		alaFcVsanRnpivStatsRxFdiscs,
		alaFcVsanRnpivStatsTxFlogiLsAccs,
		alaFcVsanRnpivStatsTxFdiscLsAccs,
		alaFcVsanRnpivStatsTxFlogos,
		alaFcVsanRnpivStatsTxFlogiLsRjts,
		alaFcVsanRnpivStatsTxFdiscLsRjts,
		alaFcVsanRnpivStatsRxFlogos
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FC R-NPIV statistics on VSANS."
        ::= { alcatelIND1FipsMIBGroups 21 }

    alaFcInfoGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcConfigSessClear,
		alaFcConfigStatsClear,
		alaFcConfigNpivLoadBalance,
		alaFcConfigWwnn
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of Fibre Channel features' Global setup."
        ::= { alcatelIND1FipsMIBGroups 22 }

    alaFipsVlanNpivDiscStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVlanNpivDiscStatsRxVlanDiscRqs,
		alaFipsVlanNpivDiscStatsTxVlanDiscResps,
		alaFipsVlanNpivDiscStatsRxMdss,
		alaFipsVlanNpivDiscStatsRxUdss,
		alaFipsVlanNpivDiscStatsTxMdas,
		alaFipsVlanNpivDiscStatsTxUdas
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE NPIV ENODE Discovery statistics on FCoE VLANS."
        ::= { alcatelIND1FipsMIBGroups 23 }

    alaFipsIntfNpivDiscStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsIntfNpivDiscStatsRxVlanDiscRqs,
		alaFipsIntfNpivDiscStatsTxVlanDiscResps,
		alaFipsIntfNpivDiscStatsRxMdss,
		alaFipsIntfNpivDiscStatsRxUdss,
		alaFipsIntfNpivDiscStatsTxMdas,
		alaFipsIntfNpivDiscStatsTxUdas
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE NPIV ENODE Discovery statistics on FCoE Ports."
        ::= { alcatelIND1FipsMIBGroups 24 }

    alaFipsVlanNpivLoginStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVlanNpivLoginStatsRxFlogis,
		alaFipsVlanNpivLoginStatsRxFdiscs,
		alaFipsVlanNpivLoginStatsTxFlogiAccs,
		alaFipsVlanNpivLoginStatsTxFlogiLsRjts,
		alaFipsVlanNpivLoginStatsTxFdiscLsRjts,
		alaFipsVlanNpivLoginStatsRxLogos,
		alaFipsVlanNpivLoginStatsTxCvls,
		alaFipsVlanNpivLoginStatsRxEkas,
		alaFipsVlanNpivLoginStatsRxVnkas,
		alaFipsVlanNpivLoginStatsTxFDiscAccs,
		alaFipsVlanNpivLoginStatsTxFlogoAccs,
		alaFipsVlanNpivLoginStatsTxFLogoLsRjts
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE NPIV ENODE Login statistics on FCoE VLANs."
        ::= { alcatelIND1FipsMIBGroups 25 }

    alaFipsIntfNpivLoginStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsIntfNpivLoginStatsRxFlogis,
		alaFipsIntfNpivLoginStatsRxFdiscs,
		alaFipsIntfNpivLoginStatsTxFlogiAccs,
		alaFipsIntfNpivLoginStatsTxFlogiLsRjts,
		alaFipsIntfNpivLoginStatsTxFdiscLsRjts,
		alaFipsIntfNpivLoginStatsRxLogos,
		alaFipsIntfNpivLoginStatsTxCvls,
		alaFipsIntfNpivLoginStatsRxEkas,
		alaFipsIntfNpivLoginStatsRxVnkas,
		alaFipsIntfNpivLoginStatsTxFDiscAccs,
		alaFipsIntfNpivLoginStatsTxFlogoAccs,
		alaFipsIntfNpivLoginStatsTxFLogoLsRjts
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE NPIV ENODE Login statistics on FCoE VLANs."
        ::= { alcatelIND1FipsMIBGroups 26 }

    alaFipsVlanRnpivDiscStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVlanRnpivDiscStatsRxMdas,
		alaFipsVlanRnpivDiscStatsRxUdas,
		alaFipsVlanRnpivDiscStatsTxMdss,
		alaFipsVlanRnpivDiscStatsTxUdss
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE R-NPIV FCF Discovery statistics on FCoE VLANs."
        ::= { alcatelIND1FipsMIBGroups 27 }

    alaFipsIntfRnpivDiscStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsIntfRnpivDiscStatsRxMdas,
		alaFipsIntfRnpivDiscStatsRxUdas,
		alaFipsIntfRnpivDiscStatsTxMdss,
		alaFipsIntfRnpivDiscStatsTxUdss
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE R-NPIV FCF Discovery statistics on FCoE Ports."
        ::= { alcatelIND1FipsMIBGroups 28 }

    alaFipsVlanRnpivLoginStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVlanRnpivLoginStatsTxFlogis,
		alaFipsVlanRnpivLoginStatsTxFdiscs,
		alaFipsVlanRnpivLoginStatsRxLsAccs,
		alaFipsVlanRnpivLoginStatsRxFlogiLsRjts,
		alaFipsVlanRnpivLoginStatsRxFdiscLsRjts,
		alaFipsVlanRnpivLoginStatsRxCvls,
		alaFipsVlanRnpivLoginStatsTxLogos,
		alaFipsVlanRnpivLoginStatsTxVnkas,
		alaFipsVlanRnpivLoginStatsTxEkas
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE R-NPIV Node Login statistics on FCoE VLANs."
        ::= { alcatelIND1FipsMIBGroups 29 }

    alaFipsIntfRnpivLoginStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsIntfRnpivLoginStatsTxFlogis,
		alaFipsIntfRnpivLoginStatsTxFdiscs,
		alaFipsIntfRnpivLoginStatsRxLsAccs,
		alaFipsIntfRnpivLoginStatsRxFlogiLsRjts,
		alaFipsIntfRnpivLoginStatsRxFdiscLsRjts,
		alaFipsIntfRnpivLoginStatsRxCvls,
		alaFipsIntfRnpivLoginStatsTxLogos,
		alaFipsIntfRnpivLoginStatsTxVnkas,
		alaFipsIntfRnpivLoginStatsTxEkas
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE R-NPIV Node Login statistics on FCoE Ports."
        ::= { alcatelIND1FipsMIBGroups 30 }

    alaFipsEtunnelVePortStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsEtunnelVePortStatsRxMdss,
		alaFipsEtunnelVePortStatsRxUdss,
		alaFipsEtunnelVePortStatsRxMdas,
		alaFipsEtunnelVePortStatsRxUdas,
		alaFipsEtunnelVePortStatsRxElpReqs,
		alaFipsEtunnelVePortStatsRxSwAccs,
		alaFipsEtunnelVePortStatsRxSwRjts,
		alaFipsEtunnelVePortStatsRxCvls,
		alaFipsEtunnelVePortStatsTxMdss,
		alaFipsEtunnelVePortStatsTxUdss,
		alaFipsEtunnelVePortStatsTxMdas,
		alaFipsEtunnelVePortStatsTxUdas,
		alaFipsEtunnelVePortStatsTxElpReqs,
		alaFipsEtunnelVePortStatsTxSwAccs,
		alaFipsEtunnelVePortStatsTxSwRjts,
		alaFipsEtunnelVePortStatsTxCvls,
		alaFipsEtunnelVePortStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE VE-Port statistics on E-Tunnels."
        ::= { alcatelIND1FipsMIBGroups 31 }

    alaFipsEtunnelTePortStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsEtunnelTePortStatsRxElpReqs,
		alaFipsEtunnelTePortStatsRxSwAccs,
		alaFipsEtunnelTePortStatsRxSwRjts,
		alaFipsEtunnelTePortStatsTxElpReqs,
		alaFipsEtunnelTePortStatsTxSwAccs,
		alaFipsEtunnelTePortStatsTxSwRjts,
		alaFipsEtunnelTePortStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE TE-Port statistics on E-Tunnels."
        ::= { alcatelIND1FipsMIBGroups 32 }

    alaFipsVsanVlanMapGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsVsanVlanMapVlanNumber,
		alaFipsVsanVlanMapRowStatus
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of VSAN and VLAN mapping."
        ::= { alcatelIND1FipsMIBGroups 33 }


    alaFipsDiscAdvtGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsDiscAdvtAbit,
		alaFipsDiscAdvtFkaAdvPeriod,
		alaFipsDiscAdvtPriority,
		alaFipsDiscAdvtUdsRetries,
		alaFipsDiscAdvtRowStatus
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of Discovery Advertisement Configuration."
        ::= { alcatelIND1FipsMIBGroups 34 }

    alaFipsEtunnelGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsEtunnelVlanId,
		alaFipsEtunnelIfIndexOne,
		alaFipsEtunnelIfIndexTwo,
		alaFipsEtunnelRowStatus,
		alaFipsEtunnelStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of E-Tunnel Configuration."
        ::= { alcatelIND1FipsMIBGroups 35 }

    alaFipsNpivSessionGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsNpivSessionInIfIndex,
		alaFipsNpivSessionOutIfIndex,
		alaFipsNpivSessionFCFMAC,
		alaFipsNpivSessionStatus,
		alaFipsNpivSessionLoginTimeDate
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE NPIV session statistics."
        ::= { alcatelIND1FipsMIBGroups 36 }

    alaFipsRnpivSessionGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsRnpivSessionVNMAC,
		alaFipsRnpivSessionVlanId,
		alaFipsRnpivSessionInIfIndex,
		alaFipsRnpivSessionOutIfIndex,
		alaFipsRnpivSessionFCFMAC,
		alaFipsRnpivSessionStatus,
		alaFipsRnpivSessionLoginTimeDate
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE R-NPIV session statistics."
        ::= { alcatelIND1FipsMIBGroups 37 }

    alaFipsEtunnelSessionGroup OBJECT-GROUP
        OBJECTS
        {
		alaFipsEtunnelSessionVlanId,
		alaFipsEtunnelSessionInIfIndex,
		alaFipsEtunnelSessionOutIfIndex,
		alaFipsEtunnelSessionFCFMAC,
		alaFipsEtunnelSessionStatus,
		alaFipsEtunnelSessionLoginTimeDate,
		alaFipsEtunnelSessionPairMode
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FCoE E-Tunnel session statistics."
        ::= { alcatelIND1FipsMIBGroups 38 }

    alaFcNpivLoadBalSessGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcNpivLoadBalSessCount
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of session created on FC port for NPIV feature."
        ::= { alcatelIND1FipsMIBGroups 39 }

    alaFcIntfEtunnelStatsGroup OBJECT-GROUP
        OBJECTS
        {
		alaFcIntfEtunnelStatsTunnelId,
		alaFcIntfEtunnelStatsRxElpReqs,
		alaFcIntfEtunnelStatsRxSwAccs,
		alaFcIntfEtunnelStatsRxSwRjts,
		alaFcIntfEtunnelStatsTxElpReqs,
		alaFcIntfEtunnelStatsTxSwAccs,
		alaFcIntfEtunnelStatsTxSwRjts,
		alaFcIntfEtunnelStatsClear
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of FC E-Tunnel statistics per port."
        ::= { alcatelIND1FipsMIBGroups 40 }

--  END ***********************



END
