ALCATEL-IND1-MULTI-CHASSIS-MIB DEFINITIONS ::= BEGIN

IMPORTS
         OBJECT-TYPE,
         OBJECT-IDENTITY,
         MODULE-IDENTITY,
         NOTIFICATION-TYPE,
         Integer32, Unsigned32,
         Counter32              FROM SNMPv2-SMI
         softentIND1MultiChassisManager FROM ALCATEL-IND1-<PERSON>SE
         TruthValue,
         MacAddress,
         TEXTUAL-CONVENTION,
         RowStatus              FROM SNMPv2-TC
         SnmpAdminString        FROM SNMP-FRAMEWORK-MIB
         MODULE-COMPLIANCE,
         OBJECT-GROUP,
         NOTIFICATION-GROUP     FROM SNMPv2-CONF
         InterfaceIndex         FROM IF-MIB;


alcatelIND1MultiChassisMIB MODULE-IDENTITY
    LAST-UPDATED "200911100000Z"
    ORGANIZATION "Alcatel-Lucent, Enterprise Solutions Division"
    CONTACT-INFO
     "Please consult with Customer Service to ensure the most appropriate
      version of this document is used with the products in question:

                 Alcatel-Lucent, Enterprise Solutions Division
                (Formerly Alcatel Internetworking, Incorporated)
                        26801 West Agoura Road
                     Agoura Hills, CA  91301-5122
                       United States Of America

     Telephone:               North America  ****** 995 2696
                              Latin America  ****** 919 9526
                              Europe         +31 23 556 0100
                              Asia           +65 394 7933
                              All Other      ****** 878 4507

     Electronic Mail:         <EMAIL>
     World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
     File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"
    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
        etwork Management Protocol (SNMP) Management Information Base (MIB):

        For the Birds Of Prey Product Line, this is the Chassis Supervision
        Chassis MIB
        for managing physical chassis objects not covered in the IETF
        Entity MIB (rfc 2737).

        The right to make changes in specification and other information
        contained in this document without prior notice is reserved.

        No liability shall be assumed for any incidental, indirect, special, or
        consequential damages whatsoever arising from or related to this
        document or the information contained herein.

        Vendors, end-users, and other interested parties are granted
        non-exclusive license to use this specification in connection with
        management of the products for which it is intended to be used.

                   Copyright (C) 1995-2007 Alcatel-Lucent
                       ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "200911100000Z"

  DESCRIPTION
     "Addressing discrepancies with Alcatel Standard."
     ::= { softentIND1MultiChassisManager 1 }

    alcatelIND1MultiChassisMIBNotifications OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For Multi-Chassis manager MIB
            Subsystem Managed Objects."
        ::= { alcatelIND1MultiChassisMIB 0 }

    alcatelIND1MultiChassisMIBObjects OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For Multi-Chassis manager MIB
            Subsystem Managed Objects."
        ::= { alcatelIND1MultiChassisMIB 1 }


    alcatelIND1MultiChassisMIBConformance OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For Chassis Supervision Multiple Chassis MIB
            Subsystem Conformance Information."
        ::= { alcatelIND1MultiChassisMIB 2}


    alcatelIND1MultiChassisMIBGroups OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For Chassis Supervision Multiple Chassis MIB
            Subsystem Units Of Conformance."
        ::= { alcatelIND1MultiChassisMIBConformance 1 }


    alcatelIND1MultiChassisMIBCompliances OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For Chassis Supervision Multiple Chassis MIB
            Subsystem Compliance Statements."
        ::= { alcatelIND1MultiChassisMIBConformance 2 }



-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- Multi-Chassis MIB
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

MultiChassisId ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "Multiple Chassis identifier."
                SYNTAX Integer32 (0..2)

MultiChassisLinkIfIndex ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "Virtual Fabric Link Id."
                SYNTAX INTEGER {
                        link0(40000128)
                    }

MultiChassisConsistency ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "Multi-chassis parameter consistency status. 
                         inconsis(0): able to compare but not consistent 
                         consis(1): able to compare and consistent
                         na(2): not able to compare since peer chassis is not connected yet  
                         disabeled(3):  not able to compare because of standalone mode"
                SYNTAX INTEGER {
                        inconsistent(0),
                        consistent(1),
                        na(2),
                        disabled(3)
                    }

MultiChassisLocaleType ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "Multiple Chassis locale type identifier."
                SYNTAX INTEGER {
                        local(1),
                        peer(2)
                    }

MultiChassisGroup ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "Multiple Chassis group."
                SYNTAX Integer32 (0..255)

MultiChassisType ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "Virtual-chassis chassis type: 
                        invalid(0): Only support Rushmore and Tor for now
                        rushmore(1): OS10k
                        tor(2): OS6900
                         "
                SYNTAX INTEGER {
                        invalid(0),
                        rushmore(1),
                        tor(2)
                    }

multiChassisConfig   OBJECT IDENTIFIER ::= { alcatelIND1MultiChassisMIBObjects 1 }

multiChassisConfigChassisId OBJECT-TYPE
        SYNTAX MultiChassisId
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
        "Chassis identifier globally unique within a multi-chassis  domain,
         which is a set of two chassis configured to operate together
         providing multi-chassis services. When the value of this object
         is equal to 0, the chassis operates in stand-alone mode, whereas
         when the value of this object is equal to 1 or 2 the chassis is
         capable of operating in a multi-chassis system."
    DEFVAL { 0}
::= { multiChassisConfig 1 }


multiChassisConfigHelloInterval OBJECT-TYPE
        SYNTAX Integer32 (1..10)
        UNITS           "seconds"
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
        "Time interval, in seconds, at which hello messages will be sent to the peer."
DEFVAL { 1}
::= { multiChassisConfig 2 }

multiChassisConfigIpcVlan OBJECT-TYPE
        SYNTAX Integer32 (1..4094)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
        "VLAN used for IPC communication"
DEFVAL { 4094}
::= { multiChassisConfig 3 }

multiChassisConfigChassisGroup OBJECT-TYPE
        SYNTAX MultiChassisGroup
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
        "Multi-chassis chassis group configuration"
DEFVAL { 0}
::= { multiChassisConfig 4 }

multiChassisOperation   OBJECT IDENTIFIER ::= { alcatelIND1MultiChassisMIBObjects 2 }

multiChassisOperChassisId OBJECT-TYPE
        SYNTAX Integer32 (0..2)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Chassis identifier globally unique within a multi-chassis  domain,
         which is a set of two chassis configured to operate together
         providing multi-chassis services. When the value of this object
         is equal to 0, the chassis operates in stand-alone mode, whereas
         when the value of this object is equal to 1 or 2 the chassis is
         capable of operating in a multi-chassis system."
::= { multiChassisOperation 1 }

multiChassisOperChassisRole OBJECT-TYPE
        SYNTAX INTEGER
                    {
                        unassigned(0),
                        primary(1),
                        secondary(2),
                        inconsistent(3)
                    }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Multi-Chassis chassis role"
::= { multiChassisOperation 2 }

multiChassisOperStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        down(0),
                        up(1),
                        inconsistent(2),
                        standalone (3)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Operational status of the multi-chassis feature as follows:
                - up:   the chassis is running in multi-chassis mode and
                        this feature is operational.
                - down: the chassis is running in multi-chassis mode, but
                        this feature is not operational. By default,
                        a chassis running in multi-chassis mode will be
                        in this state initially.
                - inconsistent: the chassis is running in multi-chassis
                        mode, but this feature is in a suspended state due
                        to inconsistencies in one or more of the global
                        mandatory consistency parameters.
                - standalone: the chassis is running in stand-alone mode
                        wherein the multi-chassis feature is not operational."
::= { multiChassisOperation 3 }

multiChassisOperHelloInterval OBJECT-TYPE
        SYNTAX Integer32 (1..10)
        UNITS           "seconds"
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Time interval, in seconds, at which hello messages will be sent to the peer."
::= { multiChassisOperation 4 }

multiChassisOperIpcVlan OBJECT-TYPE
        SYNTAX Integer32 (1..4094)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "VLAN used for IPC communication"
::= { multiChassisOperation 5 }

multiChassisOperChassisGroup OBJECT-TYPE
        SYNTAX MultiChassisGroup
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Multi-chassis operational chassis group"
::= { multiChassisOperation 6 }

multiChassisOperChassisType OBJECT-TYPE
        SYNTAX MultiChassisType
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Multi-chassis operational chassis type"
::= { multiChassisOperation 7 }


multiChassisLinkTable OBJECT-TYPE
        SYNTAX SEQUENCE OF MultiChassisLinkEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Multiple Chassis Virtual Fabric Link Table"
::= { alcatelIND1MultiChassisMIBObjects 3 }

multiChassisLinkEntry OBJECT-TYPE
        SYNTAX MultiChassisLinkEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Multiple Chassis Virtual Fabric Link Table Entry"
        INDEX { multiChassisLinkIfIndex }
::= { multiChassisLinkTable 1 }


MultiChassisLinkEntry ::= SEQUENCE
        {
                multiChassisLinkIfIndex         MultiChassisLinkIfIndex,
                multiChassisLinkAdminStatus     INTEGER,
                multiChassisLinkOperDefaultVlan Integer32,
                multiChassisLinkOperStatus      INTEGER,
                multiChassisLinkPrimaryPort     InterfaceIndex,
                multiChassisLinkActivePortNum   Integer32,
                multiChassisLinkConfigPortNum   Integer32,
                multiChassisLinkRowStatus       RowStatus
        }


multiChassisLinkIfIndex OBJECT-TYPE
        SYNTAX MultiChassisLinkIfIndex
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Virtual Fabric Link Interface IfIndex"
::= { multiChassisLinkEntry 1 }

multiChassisLinkAdminStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
        "Virtual Fabric Link Admin Status"
DEFVAL { enabled }
::= { multiChassisLinkEntry 2 }

multiChassisLinkOperDefaultVlan OBJECT-TYPE
        SYNTAX Integer32 (1..4094)
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
        "Virtual Fabric Link default vlan"
::= { multiChassisLinkEntry 3 }

multiChassisLinkOperStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        disabled(0),
                        up(1),
                        down(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Virtual Fabric Link Operational Status"
::= { multiChassisLinkEntry 4 }

multiChassisLinkPrimaryPort OBJECT-TYPE
        SYNTAX InterfaceIndex
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Virtual Fabric Link primary Port ifindex"
::= { multiChassisLinkEntry 5 }

multiChassisLinkActivePortNum OBJECT-TYPE
        SYNTAX Integer32 (0..8)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of active member ports of participating on the Virtual Fabric Link."
::= { multiChassisLinkEntry 6 }

multiChassisLinkConfigPortNum OBJECT-TYPE
        SYNTAX Integer32 (0..8)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of ports configured as members of the Virtual Fabric Link."
::= { multiChassisLinkEntry 7 }

multiChassisLinkRowStatus OBJECT-TYPE
        SYNTAX RowStatus
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
        "Virtual Fabric Link RowStatus for creationh and deletion"
::= { multiChassisLinkEntry 8 }

multiChassisLinkMemberPortTable OBJECT-TYPE
        SYNTAX SEQUENCE OF MultiChassisLinkMemberPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Virtual Fabric Link Member Port Table."
::= { alcatelIND1MultiChassisMIBObjects 4 }


multiChassisLinkMemberPortEntry OBJECT-TYPE
        SYNTAX MultiChassisLinkMemberPortEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Virtual Fabric Link Member Port Table Entry."
        INDEX { multiChassisLinkMemberPortLinkIfIndex, multiChassisLinkMemberPortIfindex }
::= { multiChassisLinkMemberPortTable 1 }

MultiChassisLinkMemberPortEntry ::= SEQUENCE
        {
                multiChassisLinkMemberPortLinkIfIndex   MultiChassisLinkIfIndex,
                multiChassisLinkMemberPortIfindex       InterfaceIndex,
                multiChassisLinkMemberPortIsPrimay      TruthValue,
                multiChassisLinkMemberOperStatus        INTEGER,
                multiChassisLinkMemberPortRowStatus     RowStatus
        }


multiChassisLinkMemberPortLinkIfIndex OBJECT-TYPE
        SYNTAX MultiChassisLinkIfIndex
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Virtual Fabric Link Interface IfIndex"
::= { multiChassisLinkMemberPortEntry 1 }

multiChassisLinkMemberPortIfindex OBJECT-TYPE
        SYNTAX InterfaceIndex
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
        "Virtual Fabric Link Member Port ifIndex."
::= { multiChassisLinkMemberPortEntry 2 }


multiChassisLinkMemberPortIsPrimay OBJECT-TYPE
                SYNTAX TruthValue
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
        "To determine if this Virtual Fabric Link Member Port is primary or not"
::= { multiChassisLinkMemberPortEntry 3 }

multiChassisLinkMemberOperStatus OBJECT-TYPE
                SYNTAX INTEGER
                    {

                        disabled(0),
                        up(1),
                        down(2)
                    }
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
        "Virtual Fabric Link Member Port operational status"
::= { multiChassisLinkMemberPortEntry 4 }

multiChassisLinkMemberPortRowStatus OBJECT-TYPE
                SYNTAX RowStatus
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
        "Virtual Fabric Link Member Port RowStatus for creation and deletion"
::= { multiChassisLinkMemberPortEntry 5 }


multiChassisLoopDetection   OBJECT IDENTIFIER ::= { alcatelIND1MultiChassisMIBObjects 5 }

multiChassisLoopDetectionAdminStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
        "Enable/disable multi-chassis loop detection"
DEFVAL { enabled }
::= { multiChassisLoopDetection 1 }


multiChassisLoopDetectionTransmitInterval OBJECT-TYPE
        SYNTAX Integer32 (1..60)
        UNITS           "seconds"
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
        "multi-chassis loop detection transmit-interval "
DEFVAL { 1}
::= { multiChassisLoopDetection 2 }

multiChassisLoopDetectionTransmitCount OBJECT-TYPE
        SYNTAX Integer32 (0..65535)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "multi-chassis loop detection transmit count "
DEFVAL { 0}
::= { multiChassisLoopDetection 3 }

multiChassisLoopDetectionCount OBJECT-TYPE
        SYNTAX Integer32 (0..65535)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "multi-chassis loop detection count "
DEFVAL { 0}
::= { multiChassisLoopDetection 4 }

multiChassisLoopDetectionPortDownList OBJECT-TYPE
        SYNTAX      SnmpAdminString(SIZE (0..128))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "multi-chassis loop detection port down ifIndex list. Each Ifindex
        will be displayed in a seperated by comas. (maximum 16 port) "
::= { multiChassisLoopDetection 5 }

multiChassisLoopDetectionClear OBJECT-TYPE
        SYNTAX      INTEGER
                    { 
                        nonClear(0),
                        clear(1)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
        "Clear multi-chassis loop detection statistics"
DEFVAL { nonClear }
::= { multiChassisLoopDetection 6 }

multiChassisGlobalConsistency   OBJECT IDENTIFIER ::= { alcatelIND1MultiChassisMIBObjects 6 }

multiChassisLocalChassisId OBJECT-TYPE
        SYNTAX MultiChassisId
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Chassis identifier globally unique within a multi-chassis  domain,
         which is a set of two chassis configured to operate together
         providing multi-chassis services"
::= { multiChassisGlobalConsistency 1 }

multiChassisPeerChassisId OBJECT-TYPE
        SYNTAX MultiChassisId
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Chassis identifier globally unique within a multi-chassis  domain,
         which is a set of two chassis configured to operate together
         providing multi-chassis services"
::= { multiChassisGlobalConsistency 2 }

multiChassisIdConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate chassis id consistency within multi-chassis"
::= { multiChassisGlobalConsistency 3 }

multiChassisLocalHelloInterval OBJECT-TYPE
        SYNTAX Integer32 (1..10)
        UNITS           "seconds"
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Time interval, in seconds, local time interval at which hello messages will be sent to the peer."
::= { multiChassisGlobalConsistency 4 }

multiChassisPeerHelloInterval OBJECT-TYPE
        SYNTAX Integer32 (0..10)
        UNITS           "seconds"
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Time interval, in seconds, peer time interval at which hello messages will be sent to the local."
::= { multiChassisGlobalConsistency 5 }

multiChassisHelloIntervalConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate Hello Interval consistency within multi-chassis"
::= { multiChassisGlobalConsistency 6 }

multiChassisLocalIpcVlan OBJECT-TYPE
        SYNTAX Integer32 (1..4094)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Local VLAN used for IPC communication."
::= { multiChassisGlobalConsistency 7 }

multiChassisPeerIpcVlan OBJECT-TYPE
        SYNTAX Integer32 (1..4094)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Peer VLAN used for IPC communication."
::= { multiChassisGlobalConsistency 8 }

multiChassisIpcVlanConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate Ipc Vlan consistency within multi-chassis"
::= { multiChassisGlobalConsistency 9 }

multiChassisLocalStpPathCostMode OBJECT-TYPE
        SYNTAX    INTEGER {
        thrityTwoBit(1),
        auto(2)}
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Local stp path cost mode within multi-chassis"
::= { multiChassisGlobalConsistency 10 }

multiChassisPeerStpPathCostMode OBJECT-TYPE
        SYNTAX    INTEGER {
        invalid(0),
        thrityTwoBit(1),
        auto(2)}
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Peer stp path cost mode within multi-chassis"
::= { multiChassisGlobalConsistency 11 }

multiChassisStpPathCostModeConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate stp path cost mode consistency within multi-chassis"
::= { multiChassisGlobalConsistency 12 }

multiChassisLocalStpMode OBJECT-TYPE
        SYNTAX  INTEGER {
                flat(1),
                onePerVlan(2)
                }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Local stp mode within multi-chassis"
::= { multiChassisGlobalConsistency 13 }

multiChassisPeerStpMode OBJECT-TYPE
        SYNTAX  INTEGER {
                invalid(0),
                flat(1),
                onePerVlan(2)
                }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Peer stp mode within multi-chassis"
::= { multiChassisGlobalConsistency 14 }

multiChassisStpModeConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate stp mode consistency within multi-chassis"
::= { multiChassisGlobalConsistency 15 }

multiChassisLocalChassisGroup OBJECT-TYPE
        SYNTAX MultiChassisGroup
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Local chassis group"
::= { multiChassisGlobalConsistency 16 }

multiChassisPeerChassisGroup OBJECT-TYPE
        SYNTAX MultiChassisGroup
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Peer chassis group"
::= { multiChassisGlobalConsistency 17 }

multiChassisGroupConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate chassis group consistency within multi-chassis"
::= { multiChassisGlobalConsistency 18 }

multiChassisLocalChassisType OBJECT-TYPE
        SYNTAX MultiChassisType
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Local chassis type"
::= { multiChassisGlobalConsistency 19 }

multiChassisPeerChassisType OBJECT-TYPE
        SYNTAX MultiChassisType
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Peer chassis type"
::= { multiChassisGlobalConsistency 20 }

multiChassisTypeConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate chassis type consistency within multi-chassis"
::= { multiChassisGlobalConsistency 21 }

multiChassisLinkaggConsistencyTable OBJECT-TYPE
        SYNTAX SEQUENCE OF MultiChassisLinkaggConsistencyEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Multiple Chassis linkagg consistency table"
::= { alcatelIND1MultiChassisMIBObjects 7 }


multiChassisLinkaggConsistencyEntry OBJECT-TYPE
        SYNTAX MultiChassisLinkaggConsistencyEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Multiple Chassis linkagg consistency table Entry"
        INDEX { multiChassisLinkaggConsistencyAggIndex }
::= { multiChassisLinkaggConsistencyTable 1 }

MultiChassisLinkaggConsistencyEntry ::= SEQUENCE
        {
                multiChassisLinkaggConsistencyAggIndex                  InterfaceIndex,
                multiChassisLinkaggConsistency                          MultiChassisConsistency,
                multiChassisLinkaggLocalAggType                         INTEGER,
                multiChassisLinkaggPeerAggType                          INTEGER,
                multiChassisLinkaggAggTypeConsistency                   MultiChassisConsistency,
                multiChassisLinkaggLocalDefaultVlan                     Integer32,
                multiChassisLinkaggPeerDefaultVlan                      Integer32,
                multiChassisLinkaggDefaultVlanConsistency               MultiChassisConsistency,
                multiChassisLinkaggLocalVlanListConfigured              INTEGER,
                multiChassisLinkaggPeerVlanListConfigured               INTEGER,
                multiChassisLinkaggVlanListConfiguredConsistency        MultiChassisConsistency,
                multiChassisLinkaggLocalAggActorSystemID                MacAddress,
                multiChassisLinkaggPeerAggActorSystemID                 MacAddress,
                multiChassisLinkaggAggActorSystemIDConsistency          MultiChassisConsistency,
                multiChassisLinkaggLocalAggActorSystemPriority          Integer32,
                multiChassisLinkaggPeerAggActorSystemPriority           Integer32,
                multiChassisLinkaggAggActorSystemPriorityConsistency    MultiChassisConsistency,
                multiChassisLinkaggLocalExist                           TruthValue,
                multiChassisLinkaggPeerExist                            TruthValue,
                multiChassisLinkaggAggAllConsistency                    MultiChassisConsistency,
                multiChassisLinkaggLocalListVlanSize                    Integer32,
                multiChassisLinkaggPeerListVlanSize                     Integer32

        }


multiChassisLinkaggConsistencyAggIndex OBJECT-TYPE
        SYNTAX InterfaceIndex
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
                "Multiple Chassis linkagg consistency table Aggregated Interface IfIndex"
::= { multiChassisLinkaggConsistencyEntry 1 }

multiChassisLinkaggConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate linkagg consistency within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 2 }

multiChassisLinkaggLocalAggType OBJECT-TYPE
        SYNTAX  INTEGER {
                static(1),
                lacp(2),
                mcStatic(3),
                mcLacp(4)
                }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Local linkagg type within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 3 }

multiChassisLinkaggPeerAggType OBJECT-TYPE
        SYNTAX  INTEGER {
                invalid(0),
                static(1),
                lacp(2),
                mcStatic(3),
                mcLacp(4)
                }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Peer linkagg type within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 4 }

multiChassisLinkaggAggTypeConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Indicate linkagg agg type consistency within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 5 }


multiChassisLinkaggLocalDefaultVlan OBJECT-TYPE
       SYNTAX Integer32 (1..4094)
       MAX-ACCESS  read-only
       STATUS  current
       DESCRIPTION
                "Local linkagg default vlan within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 6 }

multiChassisLinkaggPeerDefaultVlan OBJECT-TYPE
       SYNTAX Integer32 (1..4094)
       MAX-ACCESS  read-only
       STATUS  current
       DESCRIPTION
                "Peer linkagg default vlan within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 7 }

multiChassisLinkaggDefaultVlanConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Indicate linkagg default vlan consistency within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 8 }

multiChassisLinkaggLocalVlanListConfigured  OBJECT-TYPE
       SYNTAX  INTEGER {
                 yes(1),
                 no(2)
               }
       MAX-ACCESS  read-only
       STATUS  current
       DESCRIPTION
                "Indicated whether local linkagg vlan list is configured within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 9 }

multiChassisLinkaggPeerVlanListConfigured  OBJECT-TYPE
       SYNTAX  INTEGER {
                 invalid(0),
                 yes(1),
                 no(2)
               }
       MAX-ACCESS  read-only
       STATUS  current
       DESCRIPTION
                "Indicated whether Peer linkagg vlan list is configured within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 10 }

multiChassisLinkaggVlanListConfiguredConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Indicate linkagg vlan list configured consistency within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 11 }

multiChassisLinkaggLocalAggActorSystemID OBJECT-TYPE
        SYNTAX MacAddress
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Local aggregated actor system ID within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 12 }

multiChassisLinkaggPeerAggActorSystemID OBJECT-TYPE
        SYNTAX MacAddress
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Peer aggregated actor system ID within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 13 }

multiChassisLinkaggAggActorSystemIDConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate aggregated actor system ID consistency within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 14 }

multiChassisLinkaggLocalAggActorSystemPriority OBJECT-TYPE
        SYNTAX Integer32 (0..65535)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "local aggregated actor system priority within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 15 }

multiChassisLinkaggPeerAggActorSystemPriority OBJECT-TYPE
        SYNTAX Integer32 (0..65535)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Peer aggregated actor system priority within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 16 }

multiChassisLinkaggAggActorSystemPriorityConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate aggregated actor system priority consistency within multi-chassis"
::= { multiChassisLinkaggConsistencyEntry 17 }

multiChassisLinkaggLocalExist OBJECT-TYPE
                SYNTAX TruthValue
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
        "To determine if this local mclag does exist"
::= { multiChassisLinkaggConsistencyEntry 18 }

multiChassisLinkaggPeerExist OBJECT-TYPE
                SYNTAX TruthValue
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
        "To determine if this peer mclag does exist"
::= { multiChassisLinkaggConsistencyEntry 19 }

multiChassisLinkaggAggAllConsistency OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate all consistencies within multi-chassis linkagg"
::= { multiChassisLinkaggConsistencyEntry 20 }

multiChassisLinkaggLocalListVlanSize OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Size of multi-chassis linkagg local vlan list"
::= { multiChassisLinkaggConsistencyEntry 21 }

multiChassisLinkaggPeerListVlanSize OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Size of multi-chassis linkagg Peer vlan list"
::= { multiChassisLinkaggConsistencyEntry 22 }

multiChassisTrapInfo   OBJECT IDENTIFIER ::= { alcatelIND1MultiChassisMIBObjects 8 }


multiChassisTrapIpcVlan OBJECT-TYPE
        SYNTAX Integer32 (0..2147483647)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate multi-chassis ipc-vlan"
::= { multiChassisTrapInfo 1 }

multiChassisTrapStpBlockingVlanList OBJECT-TYPE
        SYNTAX      SnmpAdminString(SIZE (0..128))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "The STP status for some Vlans on the VFLink is in
        blocking state. This shows the first 16 vlan list. And Each Vlan
        will be displayed in a seperated by comas. (maximum 16 vlan)"
::= { multiChassisTrapInfo 2 }


multiChassisTrapFailure OBJECT-TYPE
        SYNTAX INTEGER {
                 failure(1)
               }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate multi-chassis failure"
::= { multiChassisTrapInfo 3 }

multiChassisTrapVFL OBJECT-TYPE
        SYNTAX MultiChassisLinkIfIndex
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate multi-chassis VFL ifIndex"
::= { multiChassisTrapInfo 4 }

multiChassisTrapVFLMemberPort OBJECT-TYPE
        SYNTAX InterfaceIndex
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate multi-chassis VFL member port ifIndex"
::= { multiChassisTrapInfo 5 }

multiChassisTrapDiagnostic OBJECT-TYPE
        SYNTAX INTEGER {
                duplexMode (1),
                speed (2)
               }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicates why a port configured as virtual-fabric member is unable to join the virtual-fabric link"
::= { multiChassisTrapInfo 6 }

multiChassisStpStatus OBJECT-TYPE
        SYNTAX INTEGER {
                forwarding (0),
                blocking (1)}
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicates STP stauts for VFL link"
::= { multiChassisTrapInfo 7 }

multiChassisTrapRecovered OBJECT-TYPE
        SYNTAX INTEGER {
                 recovered(1)
               }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicates that the system has recovered from a multi-chassis failure"
::= { multiChassisTrapInfo 8 }

multiChassisLinkaggConsistencyVlanTable OBJECT-TYPE
        SYNTAX SEQUENCE OF MultiChassisLinkaggConsistencyVlanEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Multiple Chassis linkagg consistency vlan table"
::= { alcatelIND1MultiChassisMIBObjects 9 }


multiChassisLinkaggConsistencyVlanEntry OBJECT-TYPE
        SYNTAX MultiChassisLinkaggConsistencyVlanEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Multiple Chassis linkagg consistency table vlan Entry"
        INDEX { multiChassisLinkaggConsistencyVlanAggIndex, multiChassisLinkaggConsistencyVlanId, multiChassisLinkaggConsistencyVlanLocaleType}
::= { multiChassisLinkaggConsistencyVlanTable 1 }

MultiChassisLinkaggConsistencyVlanEntry ::= SEQUENCE
        {
                multiChassisLinkaggConsistencyVlanAggIndex              InterfaceIndex,
                multiChassisLinkaggConsistencyVlanId                    Integer32,
                multiChassisLinkaggConsistencyVlanLocaleType            MultiChassisLocaleType,
                multiChassisLinkaggConsistencyVlanType                  INTEGER,
                multiChassisLinkaggConsistencyVlanAdminStatus           INTEGER,
                multiChassisLinkaggConsistencyVlanOperStatus            INTEGER,
                multiChassisLinkaggConsistencyVlanIpEnable              INTEGER,
                multiChassisLinkaggConsistencyVlanMtu                   Integer32,
                multiChassisLinkaggConsistencyVlanSrcLearningStatus     INTEGER,
                multiChassisLinkaggConsistencyVlanVpaType               INTEGER,
                multiChassisLinkaggConsistencyVlanVpaState              INTEGER,
                multiChassisLinkaggConsistencyVlanVRF                   Integer32,
                multiChassisLinkaggConsistencyVlanIcmpRedirectStatus    INTEGER,
                multiChassisLinkaggConsistencyVlanStatus                MultiChassisConsistency
        }


multiChassisLinkaggConsistencyVlanAggIndex OBJECT-TYPE
        SYNTAX InterfaceIndex
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Multiple Chassis linkagg vlan consistency table Aggregated Interface IfIndex."
::= { multiChassisLinkaggConsistencyVlanEntry 1 }

multiChassisLinkaggConsistencyVlanId OBJECT-TYPE
        SYNTAX Integer32 (1..4094)
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Indicate vlan entry within this Multiple Chassis linkagg consistency"
::= { multiChassisLinkaggConsistencyVlanEntry 2 }

multiChassisLinkaggConsistencyVlanLocaleType OBJECT-TYPE
        SYNTAX MultiChassisLocaleType
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "Indicate localeType: local or peer for this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 3 }

multiChassisLinkaggConsistencyVlanType  OBJECT-TYPE
        SYNTAX  INTEGER {
            invalid(0),
            service(1),
            multicastEnt(2),
            multicastService(3),
            dynamic(4),
            standard(5),
            ipc(6),
            vipVlan(7),
            erpVlan(8),
            mtpVlan(9)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicate vlan type for this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 4 }

multiChassisLinkaggConsistencyVlanAdminStatus  OBJECT-TYPE
        SYNTAX  INTEGER {
            invalid(0),
            enabled(1),
            disabled(2)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicate vlan administrative status for this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 5 }

multiChassisLinkaggConsistencyVlanOperStatus  OBJECT-TYPE
        SYNTAX  INTEGER {
            invalid(0),
            enabled(1),
            disabled(2)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicate vlan operating status for this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 6 }

multiChassisLinkaggConsistencyVlanIpEnable  OBJECT-TYPE
        SYNTAX  INTEGER {
                invalid(0),
                enabled(1),
                disabled(2)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
         "Indicates if any IP routers are configured on this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 7 }


multiChassisLinkaggConsistencyVlanMtu  OBJECT-TYPE
        SYNTAX  Integer32 (512..10222)
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicates whether the mac learning is enabled or disabled on this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 8 }

multiChassisLinkaggConsistencyVlanSrcLearningStatus  OBJECT-TYPE
        SYNTAX  INTEGER {
            invalid(0),
            enabled(1),
            disabled(2)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicates whether the mac learning is enabled or disabled on this Multiple Chassis linkagg consistency vlan entry."
        DEFVAL { enabled }
::= { multiChassisLinkaggConsistencyVlanEntry 9 }

multiChassisLinkaggConsistencyVlanVpaType  OBJECT-TYPE
        SYNTAX  INTEGER {
            invalid(0),
            cfgDefault(1),
            qTagged(2),
            dynamic(3),
            vstkDoubleTag(4),
            vstkTranslate(5),
            forbidden(6)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicates vpa type for this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 10 }

multiChassisLinkaggConsistencyVlanVpaState  OBJECT-TYPE
        SYNTAX  INTEGER {
            forwarding(0),
            blocking(1),
            inactive(2),
            invalid(3)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicates vpa state for this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 11 }

multiChassisLinkaggConsistencyVlanVRF OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION
    "Indicates VRF num for this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 12 }

multiChassisLinkaggConsistencyVlanIcmpRedirectStatus  OBJECT-TYPE
        SYNTAX  INTEGER {
            invalid(0),
            enabled(1),
            disabled(2)
        }
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Indicates icmp redirect status within this Multiple Chassis linkagg consistency vlan entry."
::= { multiChassisLinkaggConsistencyVlanEntry 13 }

multiChassisLinkaggConsistencyVlanStatus OBJECT-TYPE
        SYNTAX MultiChassisConsistency
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
        "Indicate consistency status of this mult-chassis linkagg: local vlan entry vs peer vlan entry"
::= { multiChassisLinkaggConsistencyVlanEntry 14 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
--  NOTIFICATIONS
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

        multiChassisIpcVlanDown NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapIpcVlan
           }
           STATUS   deprecated
           DESCRIPTION
               "Trap indicating the the operational status for the multi-chassis
                communication vlan: up => down. (This trap is no longer supported.)"
           ::= { alcatelIND1MultiChassisMIBNotifications 1 }

        multiChassisIpcVlanUp NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapIpcVlan
           }
           STATUS   deprecated
           DESCRIPTION
               "Trap indicating the the operational status for the multi-chassis
                communication vlan: down => up. (This trap is no longer supported.)"
           ::= { alcatelIND1MultiChassisMIBNotifications 2 }

        multiChassisMisconfigurationFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Multi-chassis misconfiguration possibly due to inconsistent chassis Id, hello-interval or ipc vlan."
           ::= { alcatelIND1MultiChassisMIBNotifications 3 }

        multiChassisHelloIntervalConsisFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating inconsistency between local and peer hello interval."
           ::= { alcatelIND1MultiChassisMIBNotifications 4 }

        multiChassisStpModeConsisFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating inconsistency between local and peer spanning tree path cost mode."
           ::= { alcatelIND1MultiChassisMIBNotifications 5 }

        multiChassisStpPathCostModeConsisFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating the STP path cost mode consistency Falure."
           ::= { alcatelIND1MultiChassisMIBNotifications 6 }

        multiChassisVflinkStatusConsisFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   deprecated
           DESCRIPTION
               "Trap indicating the MCM vflink status consistency Falure. (This trap is no longer supported.)"
           ::= { alcatelIND1MultiChassisMIBNotifications 7}

        multiChassisStpBlockingStatus NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapStpBlockingVlanList,
                multiChassisTrapVFL,
                multiChassisStpStatus

           }
           STATUS   current
           DESCRIPTION
               "Trap indicating the MCM The STP status on the VFLink is in blocking state or back to forwarding. 
               (Only VFL ifindex and Stp status will be provided. Vlan list info is not supported and will be retured as null string)"
           ::= { alcatelIND1MultiChassisMIBNotifications 8}

        multiChassisLoopDetected NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating the a loop has been detected over the multi-chassis aggregates."
           ::= { alcatelIND1MultiChassisMIBNotifications 9 }

        multiChassisHelloTimeout NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating that hello timeout has been detected."
           ::= { alcatelIND1MultiChassisMIBNotifications 10 }

        multiChassisVflinkDown NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating that the VFL link is down."
           ::= { alcatelIND1MultiChassisMIBNotifications 11 }

        multiChassisVFLMemberJoinFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapVFL, multiChassisTrapVFLMemberPort, multiChassisTrapDiagnostic
           }
           STATUS   current
           DESCRIPTION
               "Indicates a port configured as virtual-fabric member is unable to join the virtual-fabric link."
           ::= { alcatelIND1MultiChassisMIBNotifications 12 }

        multiChassisGroupConsisFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating inconsistency between local and peer chassis group."
           ::= { alcatelIND1MultiChassisMIBNotifications 13 }

        multiChassisTypeConsisFailure NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapFailure
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating inconsistency between local and peer chassis type."
           ::= { alcatelIND1MultiChassisMIBNotifications 14 }

        multiChassisConsisFailureRecovered NOTIFICATION-TYPE
           OBJECTS {
                multiChassisTrapRecovered
           }
           STATUS   current
           DESCRIPTION
               "Trap indicating that the system has recovered from a multi-chassis inconsistency between the local and peer switches."
           ::= { alcatelIND1MultiChassisMIBNotifications 15 }


-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- COMPLIANCE
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

    alcatelIND1MultiChassisMIBCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
            "Compliance statement for Multi-Chassis Supervision."
        MODULE
            MANDATORY-GROUPS
            {

                multiChassisConfigGroup,
                multiChassisOperationGroup,
                multiChassisLinkGroup,
                multiChassisLinkMemberPortGroup,
                multiChassisLoopDetectionGroup,
                multiChassisGlobalConsistencyGroup,
                multiChassisLinkaggConsistencyGroup
            }

        ::= { alcatelIND1MultiChassisMIBCompliances 1 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- UNITS OF CONFORMANCE
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    multiChassisConfigGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisConfigChassisId,
                multiChassisConfigHelloInterval,
                multiChassisConfigIpcVlan,
                multiChassisConfigChassisGroup
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis Config Group."

        ::= { alcatelIND1MultiChassisMIBGroups 1 }


    multiChassisOperationGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisOperChassisId,
                multiChassisOperChassisRole,
                multiChassisOperStatus,
                multiChassisOperHelloInterval,
                multiChassisOperIpcVlan,
                multiChassisOperChassisGroup,
                multiChassisOperChassisType
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis Operation Group."
        ::= { alcatelIND1MultiChassisMIBGroups 2 }

    multiChassisLinkGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisLinkAdminStatus,
                multiChassisLinkOperDefaultVlan,
                multiChassisLinkOperStatus,
                multiChassisLinkPrimaryPort,
                multiChassisLinkActivePortNum,
                multiChassisLinkConfigPortNum,
                multiChassisLinkRowStatus
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis Link Group."
        ::= { alcatelIND1MultiChassisMIBGroups 3 }
    multiChassisLinkMemberPortGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisLinkMemberPortIsPrimay,
                multiChassisLinkMemberOperStatus,
                multiChassisLinkMemberPortRowStatus
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis Link Member Port Group."
        ::= { alcatelIND1MultiChassisMIBGroups 4 }
   multiChassisLoopDetectionGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisLoopDetectionAdminStatus,
                multiChassisLoopDetectionTransmitInterval,
                multiChassisLoopDetectionTransmitCount,
                multiChassisLoopDetectionCount,
                multiChassisLoopDetectionPortDownList,
                multiChassisLoopDetectionClear


        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis loop detection Group."
        ::= { alcatelIND1MultiChassisMIBGroups 5 }

   multiChassisGlobalConsistencyGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisLocalChassisId,
                multiChassisPeerChassisId,
                multiChassisIdConsistency,
                multiChassisLocalHelloInterval,
                multiChassisPeerHelloInterval,
                multiChassisHelloIntervalConsistency,
                multiChassisLocalIpcVlan,
                multiChassisPeerIpcVlan,
                multiChassisIpcVlanConsistency,
                multiChassisLocalStpPathCostMode,
                multiChassisPeerStpPathCostMode,
                multiChassisStpPathCostModeConsistency,
                multiChassisLocalStpMode,
                multiChassisPeerStpMode,
                multiChassisStpModeConsistency,
                multiChassisLocalChassisGroup, 
                multiChassisPeerChassisGroup,
                multiChassisGroupConsistency,
                multiChassisLocalChassisType, 
                multiChassisPeerChassisType,
                multiChassisTypeConsistency
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis glocal consistency Group."
        ::= { alcatelIND1MultiChassisMIBGroups 6 }

 multiChassisLinkaggConsistencyGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisLinkaggConsistency,
                multiChassisLinkaggLocalAggType,
                multiChassisLinkaggPeerAggType,
                multiChassisLinkaggAggTypeConsistency,
                multiChassisLinkaggLocalDefaultVlan,
                multiChassisLinkaggPeerDefaultVlan,
                multiChassisLinkaggDefaultVlanConsistency,
                multiChassisLinkaggLocalVlanListConfigured,
                multiChassisLinkaggPeerVlanListConfigured,
                multiChassisLinkaggVlanListConfiguredConsistency,
                multiChassisLinkaggLocalAggActorSystemID,
                multiChassisLinkaggPeerAggActorSystemID,
                multiChassisLinkaggAggActorSystemIDConsistency,
                multiChassisLinkaggLocalAggActorSystemPriority,
                multiChassisLinkaggPeerAggActorSystemPriority,
                multiChassisLinkaggAggActorSystemPriorityConsistency,
                multiChassisLinkaggLocalExist,
                multiChassisLinkaggPeerExist,
                multiChassisLinkaggAggAllConsistency,
                multiChassisLinkaggLocalListVlanSize,
                multiChassisLinkaggPeerListVlanSize


        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis linkagg consistency Group."
        ::= { alcatelIND1MultiChassisMIBGroups 7 }

  multiChassisTrapInfoGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisTrapIpcVlan,
                multiChassisTrapStpBlockingVlanList,
                multiChassisTrapFailure,
                multiChassisTrapVFL,
                multiChassisTrapVFLMemberPort,
                multiChassisTrapDiagnostic,
                multiChassisStpStatus,
                multiChassisTrapRecovered
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis trap info Group."
        ::= { alcatelIND1MultiChassisMIBGroups 8 }

  multiChassisTrapOBJGroup NOTIFICATION-GROUP
        NOTIFICATIONS
        {
                multiChassisIpcVlanDown,
                multiChassisIpcVlanUp,
                multiChassisMisconfigurationFailure,
                multiChassisHelloIntervalConsisFailure,
                multiChassisStpModeConsisFailure,
                multiChassisStpPathCostModeConsisFailure,
                multiChassisVflinkStatusConsisFailure,
                multiChassisStpBlockingStatus,
                multiChassisLoopDetected,
                multiChassisHelloTimeout,
                multiChassisVflinkDown,
                multiChassisVFLMemberJoinFailure,
                multiChassisGroupConsisFailure,
                multiChassisTypeConsisFailure,
                multiChassisConsisFailureRecovered
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis trap object Group."
        ::= { alcatelIND1MultiChassisMIBGroups 9 }

 multiChassisLinkaggConsistencyVlanGroup OBJECT-GROUP
        OBJECTS
        {
                multiChassisLinkaggConsistencyVlanType,
                multiChassisLinkaggConsistencyVlanAdminStatus,
                multiChassisLinkaggConsistencyVlanOperStatus,
                multiChassisLinkaggConsistencyVlanIpEnable,
                multiChassisLinkaggConsistencyVlanMtu,
                multiChassisLinkaggConsistencyVlanSrcLearningStatus,
                multiChassisLinkaggConsistencyVlanVpaType,
                multiChassisLinkaggConsistencyVlanVpaState,
                multiChassisLinkaggConsistencyVlanVRF,
                multiChassisLinkaggConsistencyVlanIcmpRedirectStatus,
                multiChassisLinkaggConsistencyVlanStatus
        }
        STATUS  current
        DESCRIPTION
            "Chassis Supervision Multi-Chassis linkagg consistency vlan Group."
        ::= { alcatelIND1MultiChassisMIBGroups 10 }

END





