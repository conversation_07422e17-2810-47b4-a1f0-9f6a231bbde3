
        ALCATEL-IND1-LAG-MIB DEFINITIONS ::= BEGIN

        IMPORTS
        MODULE-IDENTITY, OBJECT-IDENTITY, OBJECT-TYPE,
        NOTIFICATION-TYPE, <PERSON>32, <PERSON>64, <PERSON>te<PERSON>32, TimeTicks
                        FROM SNMPv2-<PERSON><PERSON>
                MacAddress, TEXTUAL-CONVENTION, RowStatus
                        FROM SNMPv2-TC
                SnmpAdminString
                        FROM SNMP-FRAMEWORK-MIB
                MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
                        FROM SNMPv2-CONF
                InterfaceIndex
                        FROM IF-MIB
                PortList
                        FROM Q-BRIDGE-MIB
                <PERSON>,LacpState,ChurnState
                        FROM IEEE8023-LAG-MIB

                softentIND1LnkAgg
                        FROM ALCATEL-IND1-BASE;

        alcatelIND1LAGMIB MODULE-IDENTITY
                LAST-UPDATED "201005130000Z"
                ORGANIZATION "Alcatel-Lucent"
                CONTACT-INFO
                    "Please consult with Customer Service to ensure the most appropriate
                     version of this document is used with the products in question:

                                Alcatel-Lucent, Enterprise Solutions Division
                               (Formerly Alcatel Internetworking, Incorporated)
                                       26801 West Agoura Road
                                   Agoura Hills, CA  91301-5122
                                        United States Of America

                     Telephone:              North America  ****** 995 2696
                                             Latin America  ****** 919 9526
                                             Europe         +31 23 556 0100
                                             Asia           +65 394 7933
                                             All Other      ****** 878 4507

                     Electronic Mail:         <EMAIL>
                     World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
                     File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

        DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):

                           The Link Aggregation module for managing  ALCATEL FALCON product
                           This MIB is an image of the  802.3ad standard.
                 The aim of this proprietary MIB is to allow the creation of
         aggregate objects with their size.

         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.

         No liability shall be assumed for any incidental, indirect, special, or
         consequential damages whatsoever arising from or related to this
         document or the information contained herein.

         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.

                     Copyright (C) 1995-2007 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

        REVISION      "201005130000Z"
        DESCRIPTION
            "Fixed the Notifications to use MIB Module OID.0 as Notifications root."

                ::= { softentIND1LnkAgg 1 }


        alcatelIND1LAGMIBNotifications OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For LAG MIB Subsystem Notifications."
        ::= { alcatelIND1LAGMIB 0 }

        alcatelIND1LAGMIBObjects OBJECT IDENTIFIER ::= { alcatelIND1LAGMIB 1 }


        -- -------------------------------------------------------------
        -- Textual Conventions
        -- -------------------------------------------------------------





--      - Local Declares

   LacpType ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "The object supports or not (Omnichannel) the LACP protocol ."
                SYNTAX INTEGER {
                        lacpOff(0),
                        lacpOn(1)
                                }

   McLagType ::= TEXTUAL-CONVENTION
                STATUS current
                DESCRIPTION
                        "The object supports or not the Multi Chassis Link aggregation  ."
                SYNTAX INTEGER {
                        mcLagOff(0),
                        mcLagOn(1)
                                }


        -- -------------------------------------------------------------


        -- -------------------------------------------------------------
        -- groups in the LAG MIB
        -- -------------------------------------------------------------


        alclnkaggAgg OBJECT IDENTIFIER ::= { alcatelIND1LAGMIBObjects 1 }
        alclnkaggAggPort OBJECT IDENTIFIER ::= { alcatelIND1LAGMIBObjects 2 }


        -- -------------------------------------------------------------

        -- -------------------------------------------------------------
        -- The Tables Last Changed Object
        -- -------------------------------------------------------------



        alclnkaggTablesLastChanged OBJECT-TYPE
                SYNTAX TimeTicks
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "This object indicates the time of the
                        most recent change to the alclnkaggAggTable,
                        alclnkaggAggPortListTable, or
                        alclnkaggAggPortTable."
        ::= { alcatelIND1LAGMIBObjects 3 }





        -- -------------------------------------------------------------
        -- The Aggregator Configuration Table
        -- -------------------------------------------------------------


        alclnkaggAggTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A table that contains information about every
                        Aggregator that is associated with this System.
                        To create specify alclnkaggAggIndex,alclnkaggAggSize
                        and alclnkaggAggLacpType. "

                REFERENCE
                        "IEEE 802.3 Subclause 30.7.1"
                ::= { alclnkaggAgg 1 }


        alclnkaggAggEntry OBJECT-TYPE
                SYNTAX AlclnkaggAggEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A list of the Aggregator parameters. This is indexed
                        by the ifIndex of the Aggregator."
                INDEX { alclnkaggAggIndex }
                ::= { alclnkaggAggTable 1 }


        AlclnkaggAggEntry ::=
                SEQUENCE {
                alclnkaggAggIndex
                        InterfaceIndex,
                alclnkaggAggMACAddress
                        MacAddress,
                alclnkaggAggActorSystemPriority
                        Integer32,
                alclnkaggAggActorSystemID
                        MacAddress,
                alclnkaggAggPartnerAdminKey
                         LacpKey,
                alclnkaggAggActorAdminKey
                        LacpKey,
                alclnkaggAggActorOperKey
                        LacpKey,
                alclnkaggAggPartnerSystemID
                        MacAddress,
                alclnkaggAggPartnerSystemPriority
                        Integer32,
                alclnkaggAggPartnerOperKey
                        LacpKey,
--------------------------------
--   Specific Proprietary Part
------------------------------
                alclnkaggAggSize
                         Integer32,
                alclnkaggAggNumber
                         Integer32,
                alclnkaggAggDescr
                         SnmpAdminString,
                alclnkaggAggName
                         SnmpAdminString,
                alclnkaggAggLacpType
                         LacpType,
                alclnkaggAggAdminState
--           Adminstate,
                         INTEGER,
                alclnkaggAggOperState
                         INTEGER,
                alclnkaggAggNbrSelectedPorts
                         Integer32,
                alclnkaggAggNbrAttachedPorts
                         Integer32,
                alclnkaggAggPrimaryPortIndex
                        InterfaceIndex,
                alclnkaggAggPrimaryPortPosition
                        Integer32,
                alclnkaggAggRowStatus
                         RowStatus,
                alclnkaggAggMcLagType
                         McLagType ,
                alclnkaggAggPortSelectionHash
                    INTEGER,
                alclnkaggAggWTRTimer
                   Integer32 
                }


        alclnkaggAggIndex OBJECT-TYPE
                SYNTAX InterfaceIndex
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "The unique identifier allocated to this Aggregator by the local
                        System.
                        This attribute identifies an Aggregator instance among the subordinate
                        managed objects of the containing object. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.1"
                ::= { alclnkaggAggEntry 1 }


        alclnkaggAggMACAddress OBJECT-TYPE
                SYNTAX MacAddress
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "A 6-octet read-only value carrying the individual
                        MAC address assigned to the Aggregator."
                REFERENCE
                        "IEEE 802.3 Subclause ********.9"
                ::= { alclnkaggAggEntry 2 }


        alclnkaggAggActorSystemPriority OBJECT-TYPE
                SYNTAX Integer32 (0..65535)
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 2-octet read-write value indicating the priority value
                        associated with the Actor's System ID."
                REFERENCE
                        "IEEE 802.3 Subclause ********.5"
                ::= { alclnkaggAggEntry 3 }


        alclnkaggAggActorSystemID OBJECT-TYPE
                SYNTAX MacAddress
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 6-octet read-write MAC address value used as a unique
                        identifier for the System that contains this Aggregator.
                        NOTE-From the perspective of the Link Aggregation mechanisms
                        described in Clause , only a single combination of
                        Actor's System ID and System Priority are considered, and no
                        distinction is made between the values of these parameters
                        for an Aggregator and the port(s) that are associated with
                        it; i.e., the protocol is described in terms of the operation
                        of aggregation within a single System. However, the managed.
                        objects provided for the Aggregator and the port both allow
                        management of these parameters. The result of this is to
                        permit a single piece of equipment to be configured by
                        management to contain more than one System from the point of
                        view of the operation of Link Aggregation. This may be of
                        particular use in the configuration of equipment that has
                        limited aggregation capability (see )."
                REFERENCE
                        "IEEE 802.3 Subclause ********.4"
                ::= { alclnkaggAggEntry 4 }

        alclnkaggAggPartnerAdminKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The current Administrative value of the Key accepted for the
                        Aggregator's current protocol Partner. Optional this is
                        a 16-bit read-write value. If the aggregation is manually
                        configured, this Key value will be a value assigned by the
                        local System. The administrative Key value may differ from
                                   the operational "
                REFERENCE
                        " specific to ALCATEL "
                ::= { alclnkaggAggEntry 5 }


        alclnkaggAggActorAdminKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The current administrative value of the Key for the Aggregator.
                        The administrative Key value may differ from the operational
                        Key value for the reasons discussed in . This is a 16-bit,
                        read-write value. The meaning of particular Key values
                        is of local significance."
                REFERENCE
                        "IEEE 802.3 Subclause ********.7"
                ::= { alclnkaggAggEntry 6 }


        alclnkaggAggActorOperKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The current operational value of the Key for the Aggregator.
                        The administrative Key value may differ from the operational
                        Key value for the reasons discussed in .
                        This is a 16-bit read-only value. The meaning of particular Key
                        values is of local significance."
                REFERENCE
                        "IEEE 802.3 Subclause ********.8"
                ::= { alclnkaggAggEntry 7 }


        alclnkaggAggPartnerSystemID OBJECT-TYPE
                SYNTAX MacAddress
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 6-octet read-write MAC address value consisting
                        of the unique identifier for the current protocol Partner of
                        this Aggregator. A value of zero indicates that there is no
                        known Partner. If the aggregation is manually configured, this
                        System ID value will be a value assigned by the local System."
                REFERENCE
                        "IEEE 802.3 Subclause ********.10"
                ::= { alclnkaggAggEntry 8 }


        alclnkaggAggPartnerSystemPriority OBJECT-TYPE
                SYNTAX Integer32 (0..65535)
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 2-octet read-only value that indicates the priority
                        value associated with the Partner's System ID. If the
                        aggregation is manually configured, this System Priority value
                        will be a value assigned by the local System."
                REFERENCE
                        "IEEE 802.3 Subclause ********.11"
                ::= { alclnkaggAggEntry 9 }


        alclnkaggAggPartnerOperKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The current operational value of the Key for the
                        Aggregator's current protocol Partner. This is
                        a 16-bit read- write value. If the aggregation is manually
                        configured, this Key value will be a value assigned by the
                        local System."
                REFERENCE
                        "IEEE 802.3 Subclause ********.12"
                ::= { alclnkaggAggEntry 10 }


------------------------------------------------------------
-- Specific Proprietary Part
-----------------------------------------------------------


        alclnkaggAggSize OBJECT-TYPE
                SYNTAX Integer32 (1..16)
                MAX-ACCESS read-create
                STATUS  current
                DESCRIPTION
                        "Maximum number of links that could be attached to this
                         aggregator, This nominator is mandatory and is a required
                         field in order to create a row in this table"
                REFERENCE
                        "specific to ALCATEL"
                ::= { alclnkaggAggEntry 11 }

        alclnkaggAggNumber OBJECT-TYPE
                SYNTAX Integer32(0..127)
--              MAX-ACCESS read-create
                MAX-ACCESS read-only
                 STATUS  current
                DESCRIPTION
--                      "Aggreagtor number to assign a reference  number at the creation of
--                       an aggregator mandatory for the Omnichhannel object creation"
                        "Aggreagtor number reference the aggregate dedicated from the
                         ifindex at the creation"
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggEntry 12 }

           -- alclnkaggAggDescr is equivalent to ifDescr in ifTable

        alclnkaggAggDescr OBJECT-TYPE
                SYNTAX SnmpAdminString (SIZE (0..255))
                MAX-ACCESS read-only
                 STATUS  current
                DESCRIPTION
                        "Description is a human readable string which describes of
             the type of aggregator object OMNICHANNEL / LACP  etc "
                REFERENCE
                        " IEEE 802.3 Subclause ********.2"
                ::= { alclnkaggAggEntry 13 }

            -- alclnkaggAggDescr is equivalent to ifDescr in ifXTable

        alclnkaggAggName OBJECT-TYPE
                SYNTAX SnmpAdminString (SIZE (0..255))
                MAX-ACCESS read-create
                 STATUS current
                DESCRIPTION
                        "Name of the aggregator is human readable string manually given by
                                      the operator"
                REFERENCE
                        " IEEE 802.3 Subclause ********.3"
                ::= { alclnkaggAggEntry 14 }


        alclnkaggAggLacpType OBJECT-TYPE
                SYNTAX LacpType
                MAX-ACCESS read-create
                 STATUS current
                DESCRIPTION
                        "Aggreagtor set with LACP protocol or not (case of Omnichannel)
                         mandatory at the creation of the aggregator, cannot be modified
                         later "
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggEntry 15 }


           -- alclnkaggAggAdminState is equivalent to ifAdminStatus in ifTable

        alclnkaggAggAdminState OBJECT-TYPE
--              SYNTAX AdminState
                SYNTAX  INTEGER {
                            enable(1),       -- ready to pass packets
                            disable(2)
                      }
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "status assigned by the admnistrator. The activation of the aggregator
                                     is managed by this nominator"
                REFERENCE
                        " IEEE 802.3 Subclause ********.13"
                ::= { alclnkaggAggEntry 16 }

           -- alclnkaggAggOperState is similar  to ifOperStatus in ifTable but the nominator
           -- does not take exactly the same values.

        alclnkaggAggOperState OBJECT-TYPE
                  SYNTAX  INTEGER {
                                           up(1),
                                           down(2),
                                           logicPortCreatFailed(3),
                                           qReservationFailed(4)
                                                  }
                MAX-ACCESS read-only
                 STATUS current
                DESCRIPTION
                        "Operational status of the aggregator with proprietary values
                         operational state from iftable corresponds to UP state "
                REFERENCE
                        " IEEE 802.3 Subclause ********.14"
                ::= { alclnkaggAggEntry 17 }

         alclnkaggAggNbrSelectedPorts OBJECT-TYPE
                SYNTAX Integer32 (0..16)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of ports selected at the momemt ."
                REFERENCE
                        "Specific to ALCATEL"
                ::= { alclnkaggAggEntry 18 }

          alclnkaggAggNbrAttachedPorts OBJECT-TYPE
                SYNTAX Integer32 (0..16)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of ports attached at the momemt ."
                REFERENCE
                        "Specific to ALCATEL"
                ::= { alclnkaggAggEntry 19 }

          alclnkaggAggPrimaryPortIndex OBJECT-TYPE
                SYNTAX InterfaceIndex
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "the ifindex of the primary port in the aggregator used to send
                         BPDUs and flooding frames Zeo indicates no primary port is
                         available ."
                REFERENCE
                        "Specific to ALCATEL"
                ::= { alclnkaggAggEntry 20 }

          alclnkaggAggPrimaryPortPosition OBJECT-TYPE
                SYNTAX Integer32 (-1..15)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "the port index number in the aggregator used to send
                         BPDUs and flooding frames ."
                REFERENCE
                        "Specific to ALCATEL"
                ::= { alclnkaggAggEntry 21 }

        alclnkaggAggRowStatus OBJECT-TYPE
                SYNTAX RowStatus
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "This object is the RowStatus (locking flag) for creating or
                        deleting aggregator objects. alclnkaggAggSize, alclnkaggAggLacpType
                        and  must be supplied in the request to create a row, it is a
                        required field."
                REFERENCE
                        " specific to ALCATEL "
                ::= { alclnkaggAggEntry 22 }

        alclnkaggAggMcLagType OBJECT-TYPE
                SYNTAX McLagType
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "Aggregator set with MultiChassis  mode or not
                         mandatory at the creation of the aggregator, cannot be modified
                         later "
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggEntry 23 }

        alclnkaggAggPortSelectionHash OBJECT-TYPE
                SYNTAX  INTEGER {
                                           sourceMac (1),
                                           destinationMac(2),
                                           sourceDestinationMac (3),
                                           sourceIp (4),
                                           destinationIp(5),
                                           sourceDestinationIp (6),
                                           tunnelProtocol (7)
                                                  }
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "Port Selection Criteria used to hash one port of the aggregate
                         for a given traffic flow "
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggEntry 24 }

         alclnkaggAggWTRTimer OBJECT-TYPE
                SYNTAX Integer32(0..12)
                UNITS  "minutes"
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "This sets the WTR  timer value in minutes for an aggregate. The default value is 0 minute.
                         The Default means the WTR is Disabled.
                         When an link comes up, 
                         If there is no WTR enabled for this linkagg, bring up the link.
                         If there is a WTR configured for this linkagg,
                         1.if there are no other links attached to the same linkagg, bypass the WTR and bring up the link immediately.
                         2. If there are links attached to the same linkagg, start the WTR when the WTR expires,bring up the link."
                REFERENCE
                    "specific to ALCATEL"
                DEFVAL        { 0 }
                ::={ alclnkaggAggEntry 25}



        -- -------------------------------------------------------------
        -- The Aggregation Port List Table
        -- -------------------------------------------------------------


        alclnkaggAggPortListTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggPortListEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A table that contains a list of all the ports
                        associated with each Aggregator."
                REFERENCE
                        "IEEE 802.3 Subclause ********.31"
                ::= { alclnkaggAgg 2 }


        alclnkaggAggPortListEntry OBJECT-TYPE
                SYNTAX AlclnkaggAggPortListEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A list of the ports associated with a given Aggregator.
                        This is indexed by the ifIndex of the Aggregator."
                INDEX { alclnkaggAggIndex }
                ::= { alclnkaggAggPortListTable 1 }


        AlclnkaggAggPortListEntry ::=
                SEQUENCE {
                        alclnkaggAggPortListPorts
                                PortList
                }


        alclnkaggAggPortListPorts OBJECT-TYPE
                SYNTAX PortList
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The complete set of ports currently associated with
                        this Aggregator. Each bit set in this list represents
                        an Actor Port member of this Link Aggregation."
                REFERENCE
                        "IEEE 802.3 Subclause ********.31"
                ::= { alclnkaggAggPortListEntry 1 }


        -- -------------------------------------------------------------
        -- The Aggregation Port Table
        -- -------------------------------------------------------------


        alclnkaggAggPortTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggPortEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A table that contains Link Aggregation Control
                        configuration information about every
                        Aggregation Port associated with this device.
                        A row appears in this table for each physical port."
                REFERENCE
                        "IEEE 802.3 Subclause 30.7.2"
                ::= { alclnkaggAggPort 1 }

        alclnkaggAggPortEntry OBJECT-TYPE
                SYNTAX AlclnkaggAggPortEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A list of Link Aggregation Control configuration
                        parameters for each Aggregation Port on this device."
                INDEX { alclnkaggAggPortIndex }
                ::= { alclnkaggAggPortTable 1 }


        AlclnkaggAggPortEntry ::=
                SEQUENCE {
                        alclnkaggAggPortIndex
                                InterfaceIndex,
                        alclnkaggAggPortActorSystemPriority
                                Integer32,
                        alclnkaggAggPortActorSystemID
                                MacAddress,
                        alclnkaggAggPortActorAdminKey
                                LacpKey,
                        alclnkaggAggPortActorOperKey
                                LacpKey,
                        alclnkaggAggPortPartnerAdminSystemPriority
                                Integer32,
                        alclnkaggAggPortPartnerOperSystemPriority
                                Integer32,
                        alclnkaggAggPortPartnerAdminSystemID
                                MacAddress,
                        alclnkaggAggPortPartnerOperSystemID
                                MacAddress,
                        alclnkaggAggPortPartnerAdminKey
                                LacpKey,
                        alclnkaggAggPortPartnerOperKey
                                LacpKey,
                        alclnkaggAggPortSelectedAggID
                                InterfaceIndex,
                        alclnkaggAggPortAttachedAggID
                                InterfaceIndex,
                        alclnkaggAggPortActorPort
                                Integer32,
                        alclnkaggAggPortActorPortPriority
                                Integer32,
                        alclnkaggAggPortPartnerAdminPort
                                Integer32,
                        alclnkaggAggPortPartnerOperPort
                                Integer32,
                        alclnkaggAggPortPartnerAdminPortPriority
                                Integer32,
                        alclnkaggAggPortPartnerOperPortPriority
                                Integer32,
                        alclnkaggAggPortActorAdminState
                                LacpState,
                        alclnkaggAggPortActorOperState
                                LacpState,
                        alclnkaggAggPortPartnerAdminState
                                LacpState,
                        alclnkaggAggPortPartnerOperState
                                LacpState,

------------------------------------------------------------
-- Specific Proprietary Part
-----------------------------------------------------------

            alclnkaggAggPortSelectedAggNumber
                                Integer32,
            alclnkaggAggPortGlobalPortNumber
                                 Integer32,
            alclnkaggAggPortAdminState
--                              AdminState,
                                INTEGER,
                    alclnkaggAggPortOperState
--                              OperState,
                                INTEGER,
                    alclnkaggAggPortState
                                INTEGER,
                        alclnkaggAggPortLinkState
                                INTEGER,
                        alclnkaggAggPortPrimary
                                INTEGER,
                        alclnkaggAggPortLacpType
                                 LacpType,
                        alclnkaggAggPortRowStatus
                                 RowStatus,
                        alclnkaggAggPortMcLagType
                                 McLagType
                }


        alclnkaggAggPortIndex OBJECT-TYPE
                SYNTAX InterfaceIndex
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "The ifIndex of the port"
                REFERENCE
                        "IEEE 802.3 Subclause ********.1"
                ::= { alclnkaggAggPortEntry 1 }


        alclnkaggAggPortActorSystemPriority OBJECT-TYPE
                SYNTAX Integer32 (0..255)
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 2-octet read-write value used to define the priority
                        value associated with the Actor's System ID."
                REFERENCE
                        "IEEE 802.3 Subclause ********.2"
                ::= { alclnkaggAggPortEntry 2 }

        alclnkaggAggPortActorSystemID OBJECT-TYPE
                SYNTAX MacAddress
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 6-octet read-only MAC address value that defines
                        the value of the System ID for the System that contains this
                        Aggregation Port."
                REFERENCE
                        "IEEE 802.3 Subclause ********.3"
                ::= { alclnkaggAggPortEntry 3 }

        alclnkaggAggPortActorAdminKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The current administrative value of the Key for the
                        Aggregation Port. This is a 16-bit read-write value.
                        The meaning of particular Key values is of local significance."
                REFERENCE
                        "IEEE 802.3 Subclause ********.4"
                ::= { alclnkaggAggPortEntry 4 }


        alclnkaggAggPortActorOperKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The current operational value of the Key for the
                        Aggregation Port. This is a 16-bit read-only value.
                        The meaning of particular Key values is of local significance."
                REFERENCE
                        "IEEE 802.3 Subclause ********.5"
                ::= { alclnkaggAggPortEntry 5 }


        alclnkaggAggPortPartnerAdminSystemPriority OBJECT-TYPE
                SYNTAX Integer32 (0..255)
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 2-octet read-write value used to define the administrative
                        value of priority associated with the Partner's System ID. The
                        assigned value is used, along with the value of
                        aAggPortPartnerAdminSystemID, aAggPortPartnerAdminKey,
                        aAggPortPartnerAdminPort, and aAggPortPartnerAdminPortPriority,
                        in order to achieve manually configured aggregation."
                REFERENCE
                        "IEEE 802.3 Subclause ********.6"
                ::= { alclnkaggAggPortEntry 6 }

        alclnkaggAggPortPartnerOperSystemPriority OBJECT-TYPE
                SYNTAX Integer32 (0..255)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "A 2-octet read-only value indicating the operational value
                        of priority associated with the Partner's System ID. The
                        value of this attribute may contain the manually configured value
                        carried in aAggPortPartnerAdminSystemPriority
                        if there is no protocol Partner."
                REFERENCE
                        "IEEE 802.3 Subclause ********.7"
                ::= { alclnkaggAggPortEntry 7 }

        alclnkaggAggPortPartnerAdminSystemID OBJECT-TYPE
                SYNTAX MacAddress
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A 6-octet read-write MACAddress value representing
                        the administrative value of the Aggregation Port's protocol
                        Partner's System ID. The assigned value is used, along with
                        the value of aAggPortPartnerAdminSystemPriority,
                        aAggPortPartnerAdminKey, aAggPortPartnerAdminPort,
                        and aAggPortPartnerAdminPortPriority, in order to
                        achieve manually configured aggregation."
                REFERENCE
                        "IEEE 802.3 Subclause ********.8"
                ::= { alclnkaggAggPortEntry 8 }

        alclnkaggAggPortPartnerOperSystemID OBJECT-TYPE
                SYNTAX MacAddress
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "A 6-octet read-only MACAddress value representing
                        the current value of the Aggregation Port's protocol Partner's
                        System ID. A value of zero indicates that there is no known
                        protocol Partner. The value of this attribute may contain the
                        manually configured value carried in
                        aAggPortPartnerAdminSystemID if there is no protocol Partner."
                REFERENCE
                        "IEEE 802.3 Subclause ********.9"
                ::= { alclnkaggAggPortEntry 9 }


        alclnkaggAggPortPartnerAdminKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The current administrative value of the Key for the
                        protocol Partner. This is a 16-bit read-write value.
                        The assigned value is used, along with the value of
                        aAggPortPartnerAdminSystemPriority, aAggPortPartnerAdminSystemID,
                        aAggPortPartnerAdminPort, and aAggPortPartnerAdminPortPriority,
                        in order to achieve manually configured aggregation."
                REFERENCE
                        "IEEE 802.3 Subclause ********.10"
                ::= { alclnkaggAggPortEntry 10 }


        alclnkaggAggPortPartnerOperKey OBJECT-TYPE
                SYNTAX LacpKey
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The current operational value of the Key for the
                        protocol Partner. The value of this attribute may contain
                        the manually configured value carried in
                        aAggPortPartnerAdminKey if there is no protocol Partner.
                        This is a 16-bit read-only value."
                REFERENCE
                        "IEEE 802.3 Subclause ********.11"
                ::= { alclnkaggAggPortEntry 11 }


        alclnkaggAggPortSelectedAggID OBJECT-TYPE
                SYNTAX InterfaceIndex
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The identifier value of the Aggregator that this Aggregation
                        Port has currently selected. Zero indicates that the Aggregation
                        Port has not selected an Aggregator, either because it is in the
                        process of detaching from an Aggregator or because there is no
                        suitable Aggregator available for it to select. This value is
                        read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.12"
                ::= { alclnkaggAggPortEntry 12 }


        alclnkaggAggPortAttachedAggID OBJECT-TYPE
                SYNTAX InterfaceIndex
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The identifier value of the Aggregator that this Aggregation
                        Port is currently attached to. Zero indicates that the Aggregation
                        Port is not currently attached to an Aggregator. This value is
                        read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.13"
                ::= { alclnkaggAggPortEntry 13 }


        alclnkaggAggPortActorPort OBJECT-TYPE
                SYNTAX Integer32 (0..65535)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The port number locally assigned to the Aggregation Port.
                        The port number is communicated in LACPDUs as the
                        Actor_Port. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.14"
                ::= { alclnkaggAggPortEntry 14 }


        alclnkaggAggPortActorPortPriority OBJECT-TYPE
                SYNTAX Integer32 (0..255)
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The priority value assigned to this Aggregation Port.
                        This 16-bit value is read-write."
                REFERENCE
                        "IEEE 802.3 Subclause ********.15"
                ::= { alclnkaggAggPortEntry 15 }


        alclnkaggAggPortPartnerAdminPort OBJECT-TYPE
                SYNTAX Integer32 (0..65535)
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The current administrative value of the port number
                        for the protocol Partner. This is a 16-bit read-write value.
                        The assigned value is used, along with the value of
                        aAggPortPartnerAdminSystemPriority,
                        aAggPortPartnerAdminSystemID, aAggPortPartnerAdminKey,
                        and aAggPortPartnerAdminPortPriority,
                        in order to achieve manually configured aggregation."
                REFERENCE
                        "IEEE 802.3 Subclause ********.16"
                ::= { alclnkaggAggPortEntry 16 }


        alclnkaggAggPortPartnerOperPort OBJECT-TYPE
                SYNTAX Integer32 (0..65535)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The operational port number assigned to this Aggregation
                        Port by the Aggregation Port's protocol Partner. The value
                        of this attribute may contain the manually configured value
                        carried in aAggPortPartnerAdminPort if there is no protocol
                        Partner. This 16-bit value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.17"
                ::= { alclnkaggAggPortEntry 17 }


        alclnkaggAggPortPartnerAdminPortPriority OBJECT-TYPE
                SYNTAX Integer32 (0..255)
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "The current administrative value of the port priority
                        for the protocol Partner. This is a 16-bit read-write value.
                        The assigned value is used, along with the value of
                        aAggPortPartnerAdminSystemPriority, aAggPortPartnerAdminSystemID,
                        aAggPortPartnerAdminKey, and aAggPortPartnerAdminPort,
                        in order to achieve manually configured aggregation."
                REFERENCE
                        "IEEE 802.3 Subclause ********.18"
                ::= { alclnkaggAggPortEntry 18 }


        alclnkaggAggPortPartnerOperPortPriority OBJECT-TYPE
                SYNTAX Integer32 (0..255)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The priority value assigned to this Aggregation Port
                        by the Partner. The value of this attribute may contain the
                        manually configured value carried in
                        aAggPortPartnerAdminPortPriority if there is no
                        protocol Partner. This 16-bit value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.19"
                ::= { alclnkaggAggPortEntry 19 }


        alclnkaggAggPortActorAdminState OBJECT-TYPE
                SYNTAX LacpState
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A string of 8 bits, corresponding to the administrative values
                        of Actor_State () as transmitted by the Actor in LACPDUs.
                        The first bit corresponds to bit 0 of Actor_State (LACP_Activity),
                        the second bit corresponds to bit 1 (LACP_Timeout), the third bit
                        corresponds to bit 2 (Aggregation), the fourth bit corresponds to
                        bit 3 (Synchronization), the fifth bit corresponds to bit 4
                        (Collecting), the sixth bit corresponds to bit 5 (Distributing),
                        the seventh bit corresponds to bit 6 (Defaulted), and the eighth
                        bit corresponds to bit 7 (Expired). These values allow
                        administrative control over the values of LACP_Activity,
                        LACP_Timeout and Aggregation. This attribute value is read-write."
                REFERENCE
                        "IEEE 802.3 Subclause ********.20"
                ::= { alclnkaggAggPortEntry 20 }


        alclnkaggAggPortActorOperState OBJECT-TYPE
                SYNTAX LacpState
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "A string of 8 bits, corresponding to the current
                        operational values of Actor_State as transmitted by the
                        Actor in LACPDUs. The bit allocations are as defined in
                        . This attribute value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.21"
                ::= { alclnkaggAggPortEntry 21 }


        alclnkaggAggPortPartnerAdminState OBJECT-TYPE
                SYNTAX LacpState
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "A string of 8 bits, corresponding to the current administrative
                        value of Actor_State for the protocol Partner. The bit
                        allocations are as defined in . This attribute value is
                        read-write. The assigned value is used in order to achieve
                        manually configured aggregation."
                REFERENCE
                        "IEEE 802.3 Subclause ********.22"
                ::= { alclnkaggAggPortEntry 22 }


        alclnkaggAggPortPartnerOperState OBJECT-TYPE
                SYNTAX LacpState
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "A string of 8 bits, corresponding to the current values of
                        Actor_State in the most recently received LACPDU transmitted
                        by the protocol Partner. The bit allocations are as defined in
                        . In the absence of an active protocol Partner, this
                        value may reflect the manually configured value
                        aAggPortPartnerAdminState. This attribute value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.23"
                ::= { alclnkaggAggPortEntry 23 }


------------------------------------------------------------
-- Specific Proprietary Part
-----------------------------------------------------------


        alclnkaggAggPortSelectedAggNumber OBJECT-TYPE
                SYNTAX Integer32(-1..127)
                MAX-ACCESS read-create
                 STATUS current
                DESCRIPTION
                        "Aggreagtor number to attach a  port at its creation
                         mandatory and only used for the
                         Omnichhannel not allowed for LACP
                         (-1) value displayed when it is not significant"
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 24 }

        alclnkaggAggPortGlobalPortNumber OBJECT-TYPE
                SYNTAX Integer32 (0..65535)
                MAX-ACCESS read-only
                 STATUS current
                DESCRIPTION
                        "Physical reference of the port "
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 25 }


           -- alclnkaggAggPortAdminState is equivalent to ifAdminStatus in ifTable

        alclnkaggAggPortAdminState OBJECT-TYPE
                SYNTAX  INTEGER {
                            enable(1),       -- ready to pass packets
                            disable(2)
                      }
                MAX-ACCESS read-only
                 STATUS current
                DESCRIPTION
                        "status assigned by the admnistrator. port activation done by iftable
                         this nominator is equivalent to ifAdminStatus in ifTable  "
                REFERENCE
                        " IEEE 802.3 Subclause ********.13"
                ::= { alclnkaggAggPortEntry 26 }


        alclnkaggAggPortOperState OBJECT-TYPE
                  SYNTAX  INTEGER {
                                    up(1),
                                    down(2),
                                    notAttached(3),
                                    notAggregable(4)
                                   }
                MAX-ACCESS read-only
                 STATUS current
                DESCRIPTION
                        "Operational status of the port regarding the traffic"
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 27 }

        alclnkaggAggPortState OBJECT-TYPE
                  SYNTAX  INTEGER {
                                    created(1),
                                    configurable(2),
                                    configured(3),
                                    selected(4),
                                    reserved(5),
                                    attached(6)
                                   }
                MAX-ACCESS read-only
                 STATUS current
                DESCRIPTION
                        "aggregation status of the port"
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 28 }

        alclnkaggAggPortLinkState OBJECT-TYPE
                  SYNTAX  INTEGER {
                                    up(1),
                                    down(2)
                                   }
                MAX-ACCESS read-only
                 STATUS current
                DESCRIPTION
                        "Operational status of the link"
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 29 }

         -- alclnkaggAggPortLinkState is equivalent to ifOperStatus

        alclnkaggAggPortPrimary OBJECT-TYPE
                  SYNTAX  INTEGER {
                                    yes(1),
                                    no(2),
                                    notSignificant(3)
                                   }
                MAX-ACCESS read-only
                 STATUS current
                DESCRIPTION
                        "port primary or not"
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 30 }


        alclnkaggAggPortLacpType OBJECT-TYPE
                SYNTAX LacpType
                MAX-ACCESS read-create
--               STATUS mandatory
                 STATUS current
                DESCRIPTION
                        "Port set with LACP protocol or not (case of Omnichannel)
                          mandatory at the creation of the port"
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 31 }


        alclnkaggAggPortRowStatus OBJECT-TYPE
        SYNTAX RowStatus
                MAX-ACCESS read-create
                STATUS current
                DESCRIPTION
                        "This object is the RowStatus (locking flag) for creating or
                         deleting aggregatable port objects. alclnkaggAggPortActorAdminKey
                         and alclnkaggAggPortSlotSlice must be supplied
                         in the request to create a row, it is a required field."
                REFERENCE
                        " specific to ALCATEL "
                ::= { alclnkaggAggPortEntry 32 }

        alclnkaggAggPortMcLagType OBJECT-TYPE
                SYNTAX McLagType
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "Port set with McLag mode or not  "
                REFERENCE
                        " specific to ALCATEL"
                ::= { alclnkaggAggPortEntry 33 }

        -- -------------------------------------------------------------
        -- LACP Statistics Table
        -- -------------------------------------------------------------


        alclnkaggAggPortStatsTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggPortStatsEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A table that contains Link Aggregation information
                         about every port that is associated with this device.
                         A row appears in this table for each physical port."
                REFERENCE
                        "IEEE 802.3 Subclause 30.7.3"
                ::= { alclnkaggAggPort 2 }


        alclnkaggAggPortStatsEntry OBJECT-TYPE
                SYNTAX AlclnkaggAggPortStatsEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A list of Link Aggregation Control Protocol statistics
                        for each port on this device."
                INDEX { alclnkaggAggPortIndex }
                ::= { alclnkaggAggPortStatsTable 1 }


        AlclnkaggAggPortStatsEntry ::=
                SEQUENCE {
                        alclnkaggAggPortStatsLACPDUsRx
                                Counter32,
                        alclnkaggAggPortStatsMarkerPDUsRx
                                Counter32,
                        alclnkaggAggPortStatsMarkerResponsePDUsRx
                                Counter32,
                        alclnkaggAggPortStatsUnknownRx
                                Counter32,
                        alclnkaggAggPortStatsIllegalRx
                                Counter32,
                        alclnkaggAggPortStatsLACPDUsTx
                                Counter32,
                        alclnkaggAggPortStatsMarkerPDUsTx
                                Counter32,
                        alclnkaggAggPortStatsMarkerResponsePDUsTx
                                Counter32
                }


        alclnkaggAggPortStatsLACPDUsRx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of valid LACPDUs received on this
                        Aggregation Port. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.2"
                ::= { alclnkaggAggPortStatsEntry 1 }


        alclnkaggAggPortStatsMarkerPDUsRx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of valid Marker PDUs received on this
                        Aggregation Port. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.3"
                ::= { alclnkaggAggPortStatsEntry 2 }


        alclnkaggAggPortStatsMarkerResponsePDUsRx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of valid Marker Response PDUs received on this
                         Aggregation Port. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.4"
                ::= { alclnkaggAggPortStatsEntry 3 }


        alclnkaggAggPortStatsUnknownRx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of frames received that either:
                        - carry the Slow Protocols Ethernet Type value (),
                        but contain an unknown PDU, or:
                        - are addressed to the Slow Protocols group MAC
                        Address (), but do not carry the Slow Protocols Ethernet Type.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.5"
                ::= { alclnkaggAggPortStatsEntry 4 }


        alclnkaggAggPortStatsIllegalRx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of frames received that carry the Slow
                        Protocols Ethernet Type value (), but contain a badly formed
                        PDU or an illegal value of Protocol Subtype ().
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.6"
                ::= { alclnkaggAggPortStatsEntry 5 }


        alclnkaggAggPortStatsLACPDUsTx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of LACPDUs transmitted on this
                        Aggregation Port. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.7"
                ::= { alclnkaggAggPortStatsEntry 6 }


        alclnkaggAggPortStatsMarkerPDUsTx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of Marker PDUs transmitted on this
                        Aggregation Port. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.8"
                ::= { alclnkaggAggPortStatsEntry 7 }


        alclnkaggAggPortStatsMarkerResponsePDUsTx OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The number of Marker Response PDUs transmitted
                         on this Aggregation Port. This value is read-only."
                REFERENCE
                "IEEE 802.3 Subclause ********.9"
                ::= { alclnkaggAggPortStatsEntry 8 }


        -- -------------------------------------------------------------
        -- LACP Debug Table
        -- -------------------------------------------------------------



        alclnkaggAggPortDebugTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggPortDebugEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A table that contains Link Aggregation debug
                        information about every port that is associated with
                        this device. A row appears in this table for each
                        physical port."
                REFERENCE
                        "IEEE 802.3 Subclause 30.7.4"
                ::= { alclnkaggAggPort 3 }


        alclnkaggAggPortDebugEntry OBJECT-TYPE
                SYNTAX AlclnkaggAggPortDebugEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "A list of the debug parameters for a port."
                INDEX { alclnkaggAggPortIndex }
                ::= { alclnkaggAggPortDebugTable 1 }

        AlclnkaggAggPortDebugEntry ::=
                SEQUENCE {
                        alclnkaggAggPortDebugRxState
                                INTEGER,
                        alclnkaggAggPortDebugLastRxTime
                                TimeTicks,
                        alclnkaggAggPortDebugMuxState
                                INTEGER,
                        alclnkaggAggPortDebugMuxReason
                                SnmpAdminString,
                        alclnkaggAggPortDebugActorChurnState
                                ChurnState,
                        alclnkaggAggPortDebugPartnerChurnState
                                ChurnState,
                        alclnkaggAggPortDebugActorChurnCount
                                Counter32,
                        alclnkaggAggPortDebugPartnerChurnCount
                                Counter32,
                        alclnkaggAggPortDebugActorSyncTransitionCount
                                Counter32,
                        alclnkaggAggPortDebugPartnerSyncTransitionCount
                                Counter32,
                        alclnkaggAggPortDebugActorChangeCount
                                Counter32,
                        alclnkaggAggPortDebugPartnerChangeCount
                                Counter32
                }


        alclnkaggAggPortDebugRxState OBJECT-TYPE
                SYNTAX INTEGER {
                                        current(1),
                                        expired(2),
                                        defaulted(3),
                                        initialize(4),
                                        lacpDisabled(5),
                                        portDisabled(6)
                        }
        MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "This attribute holds the value 'current' if the Receive
                        state machine for the Aggregation Port is in the
                        CURRENT state, 'expired' if the Receive state machine.
                        is in the EXPIRED state, 'defaulted' if the Receive state
                        machine is in the DEFAULTED state, 'initialize' if the
                        Receive state machine is in the INITIALIZE state,
                        'lacpDisabled' if the Receive state machine is in the
                        LACP_DISABLED state, or 'portDisabled' if the Receive
                        state machine is in the PORT_DISABLED state.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.2"
                ::= { alclnkaggAggPortDebugEntry 1 }


        alclnkaggAggPortDebugLastRxTime OBJECT-TYPE
                SYNTAX TimeTicks
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The value of aTimeSinceSystemReset (F.2.1) when
                        the last LACPDU was received by this Aggregation Port.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.3"
                ::= { alclnkaggAggPortDebugEntry 2 }

       alclnkaggAggPortDebugMuxState OBJECT-TYPE
                SYNTAX       INTEGER {
                    detached(1),
                    waiting(2),
                    attached(3),
                    collecting(4),
                    distributing(5),
                    collectingDistributing(6)
                }
                MAX-ACCESS   read-only
                STATUS       current
                DESCRIPTION
                        "This attribute holds the value `detached' if the Mux
                        state machine (43.4.14) for the Aggregation Port is in
                        the DETACHED state, `waiting' if the Mux state machine
                        is in the WAITING state, `attached' if the Mux state
                        machine for the Aggregation Port is in the ATTACHED
                        state, `collecting' if the Mux state machine for the
                        Aggregation Port is in the COLLECTING state,
                        `distributing' if the Mux state machine for the
                        Aggregation Port is in the DISTRIBUTING state, and
                        `collectingDistributing' if the Mux state machine for
                        the Aggregation Port is in the COLLECTING_DISTRIBUTING
                        state. This value is read-only."
                REFERENCE
                "IEEE 802.3 Subclause ********.4"
                ::= { alclnkaggAggPortDebugEntry 3 }


        alclnkaggAggPortDebugMuxReason OBJECT-TYPE
                SYNTAX       SnmpAdminString
                MAX-ACCESS   read-only
                STATUS       current
                DESCRIPTION
                        "A human-readable text string indicating the reason
                        for the most recent change of Mux machine state.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.5"
                ::= { alclnkaggAggPortDebugEntry 4 }


        alclnkaggAggPortDebugActorChurnState OBJECT-TYPE
                SYNTAX ChurnState
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The state of the Actor Churn Detection machine
                        () for the Aggregation Port. A value of 'noChurn'
                        indicates that the state machine is in either the
                        NO_ACTOR_CHURN or the ACTOR_CHURN_MONITOR
                        state, and 'churn' indicates that the state machine is in the
                        ACTOR_CHURN state. This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.6"
                ::= { alclnkaggAggPortDebugEntry 5 }


        alclnkaggAggPortDebugPartnerChurnState OBJECT-TYPE
                SYNTAX ChurnState
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The state of the Partner Churn Detection machine
                        () for the Aggregation Port. A value of 'noChurn'
                        indicates that the state machine is in either the
                        NO_PARTNER_CHURN or the PARTNER_CHURN_MONITOR
                        state, and 'churn' indicates that the state machine is
                        in the PARTNER_CHURN state.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.7"
                ::= { alclnkaggAggPortDebugEntry 6 }



        alclnkaggAggPortDebugActorChurnCount OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "Count of the number of times the Actor Churn state
                        machine has entered the ACTOR_CHURN state.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.8"
                ::= { alclnkaggAggPortDebugEntry 7 }


        alclnkaggAggPortDebugPartnerChurnCount OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "Count of the number of times the Partner Churn
                        state machine has entered the PARTNER_CHURN state.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.9"
                ::= { alclnkaggAggPortDebugEntry 8 }


        alclnkaggAggPortDebugActorSyncTransitionCount OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "Count of the number of times the Actor's Mux state
                        machine () has entered the IN_SYNC state.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.10"
                ::= { alclnkaggAggPortDebugEntry 9 }


        alclnkaggAggPortDebugPartnerSyncTransitionCount OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "Count of the number of times the Partner's Mux
                        state machine () has entered the IN_SYNC state.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.11"
                ::= { alclnkaggAggPortDebugEntry 10 }


        alclnkaggAggPortDebugActorChangeCount OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "Count of the number of times the Actor's perception of
                        the LAG ID for this Aggregation Port has changed.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.12"
                ::= { alclnkaggAggPortDebugEntry 11 }


        alclnkaggAggPortDebugPartnerChangeCount OBJECT-TYPE
                SYNTAX Counter32
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "Count of the number of times the Partner's perception of
                        the LAG ID (see ) for this Aggregation Port has changed.
                        This value is read-only."
                REFERENCE
                        "IEEE 802.3 Subclause ********.13"
                ::= { alclnkaggAggPortDebugEntry 12 }



        -- -------------------------------------------------------------
        -- Link Aggregation additional parameters
        -- -------------------------------------------------------------



        ----------------------------------------------------------------
        -- Table for mapping LAG Id to the ifIndex of this aggregate
        ---------------------------------------------------------------

        alclnkaggAggIdIfIndexTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggIdIfIndexEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "Each row in this table represents the link aggregation
                        aggregate id's corresponding ifIndex. "
                REFERENCE
                        "specific to Alcatel"
                ::= { alclnkaggAgg 3 }


        alclnkaggAggIdIfIndexEntry OBJECT-TYPE
                SYNTAX AlclnkaggAggIdIfIndexEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "Parameters for link aggregation aggregate id relationship to ifIndex."
                INDEX { alclnkaggIfIndex }
                ::= { alclnkaggAggIdIfIndexTable 1 }


        AlclnkaggAggIdIfIndexEntry ::=
                SEQUENCE {

                alclnkaggIfIndex
                        Integer32,
                alclnkaggAggId
                        Integer32
                }

        alclnkaggIfIndex OBJECT-TYPE
                SYNTAX Integer32  (1..2147483647)
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "IfIndex for the given Aggregate ID"
                ::= { alclnkaggAggIdIfIndexEntry 1 }

        alclnkaggAggId OBJECT-TYPE
                SYNTAX Integer32 (0..127)
                MAX-ACCESS read-only
                STATUS current
                DESCRIPTION
                        "The aggregate id number."
                REFERENCE
                        "specific to Alcatel"
                ::= { alclnkaggAggIdIfIndexEntry 2 }



        ----------------------------------------------------------------                                                    
        -- Table for LAS  data for this aggregate                                                        
        ---------------------------------------------------------------                                                     
                                                                                                                            
        alclnkaggAggIdAccountTable OBJECT-TYPE                                                                              
                SYNTAX SEQUENCE OF AlclnkaggAggIdAccountEntry
                MAX-ACCESS not-accessible
                STATUS current

                DESCRIPTION
                        "Rows in this table represents account statistics of one aggregation."             

                REFERENCE
                        "specific to Alcatel"                                                                               
                ::= { alclnkaggAgg 4 }                                                                                      
         alclnkaggAggIdAccountEntry OBJECT-TYPE                                                                              
                SYNTAX AlclnkaggAggIdAccountEntry
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "Link Aggregate statistics."                             
                INDEX { alclnkaggAggIndex }                                                                                  
                ::= { alclnkaggAggIdAccountTable 1 }                                                                        
        AlclnkaggAggIdAccountEntry ::=                                                                                      
                SEQUENCE {
                alcRxUndersize
                        Counter64,
		alcTxUndersize	
                        Counter64,
		alcRxOversize
                        Counter64,
		alcTxOversize
                        Counter64,
		alcRxPackets64
                        Counter64,
		alcRxPackets127
                        Counter64,
		alcRxPackets255
                        Counter64,
		alcRxPackets511
                        Counter64,
		alcRxPackets1023
                        Counter64,
		alcRxPackets1518
                        Counter64,
		alcRxPackets4095
                        Counter64,
		alcRxPackets9216
                        Counter64,
		alcRxJabberFrames
                        Counter64
                }

     	alcRxUndersize OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "Undersize packet received"                                                                
                ::= { alclnkaggAggIdAccountEntry 1 }                                                                                                                          

     	alcTxUndersize OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "Undersize packet sent"                                                                
                ::= { alclnkaggAggIdAccountEntry 2 }                                                                                                                          

     	alcRxOversize OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "oversize packet received"                                                                
                ::= { alclnkaggAggIdAccountEntry 3 }                                                                                                                          

     	alcTxOversize OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "oversize packet sent"                                                                
                ::= { alclnkaggAggIdAccountEntry 4 }                                                                                                                          

     	alcRxPackets64 OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "packet size 64 received"                                                                
                ::= { alclnkaggAggIdAccountEntry 5 }                                                                                                                          

     	alcRxPackets127 OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "packet size 64-127 received"                                                                
                ::= { alclnkaggAggIdAccountEntry 6 }                                                                                                                          

     	alcRxPackets255 OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "packet size 128-255 received"                                                                
                ::= { alclnkaggAggIdAccountEntry 7 }                                                                                                                          

     	alcRxPackets511 OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "packet size 256-511 received"                                                                
                ::= { alclnkaggAggIdAccountEntry 8 }                                                                                                                          

     	alcRxPackets1023 OBJECT-TYPE
		SYNTAX  Counter64                                                                          
                MAX-ACCESS  read-only
                STATUS current                                                                                              
                DESCRIPTION
                        "packet size 512-1023 received"                                                                
                ::= { alclnkaggAggIdAccountEntry 9 }                                                                                                                          

	alcRxPackets1518 OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "packet size 1024-1518 received"
                ::= { alclnkaggAggIdAccountEntry 10 } 

        alcRxPackets4095 OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "packet size 1518-4095 received"
                ::= { alclnkaggAggIdAccountEntry 11 }   

        alcRxPackets9216 OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "packet size 4096-Max received"
                ::= { alclnkaggAggIdAccountEntry 12 }  

        alcRxJabberFrames OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "Jabber frames received"
                ::= { alclnkaggAggIdAccountEntry 13 }  


	alclnkaggAggIdCounterTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggIdCounterEntry
                MAX-ACCESS not-accessible
                STATUS current
                             
                DESCRIPTION 
                        "Rows in this table represents counter statistics of one aggregation."
                                                                
                REFERENCE
                        "specific to Alcatel"
                ::= { alclnkaggAgg 5 }                                                                                                                                                                                                              
         alclnkaggAggIdCounterEntry OBJECT-TYPE
		SYNTAX AlclnkaggAggIdCounterEntry
		MAX-ACCESS not-accessible      
                STATUS current
		DESCRIPTION
                        "Link Aggregate statistics."                                                                      
                INDEX { alclnkaggAggIndex }
                ::= { alclnkaggAggIdCounterTable 1 }

	AlclnkaggAggIdCounterEntry ::=
                SEQUENCE {
                alcInOctets
                        Counter64,
                alcOutOctets
                        Counter64,
                alcInUcastPkts
                        Counter64,
                alcOutUcastPkts
                        Counter64,
                alcInMcastPkts
                        Counter64,
                alcOutMcastPkts
                        Counter64,
                alcInBcastPkts
                        Counter64,
                alcOutBcastPkts
                        Counter64,
                alcInPauseFrames
                        Counter64,
                alcOutPauseFrames
                        Counter64

		}


	alcInOctets OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "Octets received" 
                ::= { alclnkaggAggIdCounterEntry 1 }        

	alcOutOctets OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "Octets sent" 
                ::= { alclnkaggAggIdCounterEntry 2 }        

	alcInUcastPkts OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "unicast packet received" 
                ::= { alclnkaggAggIdCounterEntry 3 }        

	alcOutUcastPkts OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "unicast packet sent" 
                ::= { alclnkaggAggIdCounterEntry 4 }        

	alcInMcastPkts OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "multicast packet received" 
                ::= { alclnkaggAggIdCounterEntry 5 }        


	alcOutMcastPkts OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "multicast packet sent" 
                ::= { alclnkaggAggIdCounterEntry 6 }        

	alcInBcastPkts OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "broadcast packet received" 
                ::= { alclnkaggAggIdCounterEntry 7 }        

	alcOutBcastPkts OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "broadcast packet sent" 
                ::= { alclnkaggAggIdCounterEntry 8 }        

	alcInPauseFrames OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "pause frames received" 
                ::= { alclnkaggAggIdCounterEntry 9 }        

	alcOutPauseFrames OBJECT-TYPE 
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "pause frames sent" 
                ::= { alclnkaggAggIdCounterEntry 10 }        


	alclnkaggAggIdCounterErrTable OBJECT-TYPE
                SYNTAX SEQUENCE OF AlclnkaggAggIdCounterErrEntry
                MAX-ACCESS not-accessible
                STATUS current
                             
                DESCRIPTION
                        "Rows in this table represents counter error statistics of one aggregation."
                                                
                REFERENCE
                        "specific to Alcatel"
                ::= { alclnkaggAgg 6 }                                                                                                                                                                                                                
         alclnkaggAggIdCounterErrEntry OBJECT-TYPE
		SYNTAX AlclnkaggAggIdCounterErrEntry 
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "Link Aggregate statistics."
                INDEX { alclnkaggAggIndex }
                ::= { alclnkaggAggIdCounterErrTable 1 }
                                                                                                                           
        AlclnkaggAggIdCounterErrEntry ::=
                SEQUENCE {
                alcAlignmentsErrors
                        Counter64,
		alcFCSErrors
                        Counter64,
		alcIfInErrors
                        Counter64,
		alcIfOutErrors
                        Counter64
		}

	alcAlignmentsErrors OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "Alignments error"
                ::= { alclnkaggAggIdCounterErrEntry 1 }     


	alcFCSErrors OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "Alignments error"
                ::= { alclnkaggAggIdCounterErrEntry 2 }     


	alcIfInErrors OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "interface rceive error"
                ::= { alclnkaggAggIdCounterErrEntry 3 }     

	alcIfOutErrors OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "interface send error"
                ::= { alclnkaggAggIdCounterErrEntry 4 }     


	alclnkaggAggIdTrafficTable OBJECT-TYPE                                                                          
                SYNTAX SEQUENCE OF AlclnkaggAggIdTrafficEntry
                MAX-ACCESS not-accessible
                STATUS current
                           
                DESCRIPTION
                        "Rows in this table represents traffic count statistics of one aggregation."
                          
                REFERENCE
                        "specific to Alcatel"
                ::= { alclnkaggAgg 7 }

         alclnkaggAggIdTrafficEntry OBJECT-TYPE
		SYNTAX AlclnkaggAggIdTrafficEntry 
                MAX-ACCESS not-accessible
                STATUS current
                DESCRIPTION
                        "Link Aggregate statistics."
                INDEX { alclnkaggAggIndex }
                ::= { alclnkaggAggIdTrafficTable 1 }
                                 
        AlclnkaggAggIdTrafficEntry ::=
		SEQUENCE {
                alcInputPackets
                        Counter64,
                alcInputBytes
                        Counter64,
                alcOutputPackets
                        Counter64,
                alcOutputBytes
                        Counter64
                }                                                                                                          

        alcInputPackets OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "InputPacket "                                                                                 
                ::= { alclnkaggAggIdTrafficEntry 1 }	

        alcInputBytes OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "Input bytes "                                                                                 
                ::= { alclnkaggAggIdTrafficEntry 2 }	

        alcOutputPackets OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "InputPacket "                                                                                 
                ::= { alclnkaggAggIdTrafficEntry 3 }	

        alcOutputBytes OBJECT-TYPE
                SYNTAX  Counter64
                MAX-ACCESS  read-only
                STATUS current
                DESCRIPTION
                        "output bytes "                                                                                 
                ::= { alclnkaggAggIdTrafficEntry 4 }	


        -- -------------------------------------------------------------
        -- IEEE 802.3ad MIB - Conformance Information
        -- -------------------------------------------------------------


        alcatelIND1LAGMIBConformance OBJECT-IDENTITY
                STATUS current
                DESCRIPTION
                        "Branch For Link Aggregation Subsystem Conformance Information."
        ::= { alcatelIND1LAGMIB 2 }

        alcatelIND1LAGMIBGroups OBJECT-IDENTITY
                STATUS current
                DESCRIPTION
                        "Branch For Link Aggregation Subsystem Units Of Conformance."
                ::= { alcatelIND1LAGMIBConformance 1 }


        alcatelIND1LAGMIBCompliances OBJECT-IDENTITY
                STATUS current
                DESCRIPTION
                        "Branch For Link Aggregation Subsystem Compliance Statements."
                ::= { alcatelIND1LAGMIBConformance 2 }


        -- -------------------------------------------------------------
        -- units of conformance
        -- -------------------------------------------------------------


        alclnkaggAggGroup OBJECT-GROUP
                OBJECTS {
                        alclnkaggAggActorSystemID,
                        alclnkaggAggPartnerAdminKey,
                        alclnkaggAggActorSystemPriority,
                        alclnkaggAggActorAdminKey,
                        alclnkaggAggMACAddress,
                        alclnkaggAggActorOperKey,
                        alclnkaggAggPartnerSystemID,
                        alclnkaggAggPartnerSystemPriority,
                        alclnkaggAggPartnerOperKey,
--------------------------------
--   Specific Proprietary Part
------------------------------
                        alclnkaggAggSize,
                        alclnkaggAggNumber,
                        alclnkaggAggDescr,
                        alclnkaggAggName,
                        alclnkaggAggLacpType,
                        alclnkaggAggAdminState,
                        alclnkaggAggOperState,
                        alclnkaggAggNbrSelectedPorts,
                        alclnkaggAggNbrAttachedPorts,
                        alclnkaggAggPrimaryPortIndex,
                        alclnkaggAggPrimaryPortPosition,
                        alclnkaggAggRowStatus,
                        alclnkaggAggMcLagType ,
                        alclnkaggAggId,
                        alclnkaggAggPortSelectionHash,
                        alclnkaggAggWTRTimer
                }
                STATUS current
                DESCRIPTION
                        "A collection of objects providing information about an
                        aggregation."
                ::= { alcatelIND1LAGMIBGroups 1 }


        alclnkaggAggPortListGroup OBJECT-GROUP
                OBJECTS {
                        alclnkaggAggPortListPorts
                }
                STATUS current
                DESCRIPTION
                        "A collection of objects providing information about every
                        port in an aggregation."
                ::= { alcatelIND1LAGMIBGroups 2 }


        alclnkaggAggPortGroup OBJECT-GROUP
                OBJECTS {
                        alclnkaggAggPortActorSystemPriority,
                        alclnkaggAggPortActorSystemID,
                        alclnkaggAggPortActorAdminKey,
                        alclnkaggAggPortActorOperKey,
                        alclnkaggAggPortPartnerAdminSystemPriority,
                        alclnkaggAggPortPartnerOperSystemPriority,
                        alclnkaggAggPortPartnerAdminSystemID,
                        alclnkaggAggPortPartnerOperSystemID,
                        alclnkaggAggPortPartnerAdminKey,
                        alclnkaggAggPortPartnerOperKey,
                        alclnkaggAggPortSelectedAggID,
                        alclnkaggAggPortAttachedAggID,
                        alclnkaggAggPortActorPort,
                        alclnkaggAggPortActorPortPriority,
                        alclnkaggAggPortPartnerAdminPort,
                        alclnkaggAggPortPartnerOperPort,
                        alclnkaggAggPortPartnerAdminPortPriority,
                        alclnkaggAggPortPartnerOperPortPriority,
                        alclnkaggAggPortActorAdminState,
                        alclnkaggAggPortActorOperState,
                        alclnkaggAggPortPartnerAdminState,
                        alclnkaggAggPortPartnerOperState,
                        alclnkaggAggPortSelectedAggNumber,
                        alclnkaggAggPortGlobalPortNumber,
                        alclnkaggAggPortAdminState,
                        alclnkaggAggPortOperState,
                        alclnkaggAggPortState,
                        alclnkaggAggPortLinkState,
                        alclnkaggAggPortPrimary,
                        alclnkaggAggPortLacpType,
                        alclnkaggAggPortRowStatus,
                        alclnkaggAggPortMcLagType
                        }
                STATUS current
                DESCRIPTION
                        "A collection of objects providing information about every
                        port in an aggregation."
                ::= { alcatelIND1LAGMIBGroups 3 }


        alclnkaggAggPortStatsGroup OBJECT-GROUP
                OBJECTS {
                        alclnkaggAggPortStatsLACPDUsRx,
                        alclnkaggAggPortStatsMarkerPDUsRx,
                        alclnkaggAggPortStatsMarkerResponsePDUsRx,
                        alclnkaggAggPortStatsUnknownRx,
                        alclnkaggAggPortStatsIllegalRx,
                        alclnkaggAggPortStatsLACPDUsTx,
                        alclnkaggAggPortStatsMarkerPDUsTx,
                        alclnkaggAggPortStatsMarkerResponsePDUsTx
                }
                STATUS current
                DESCRIPTION
                        "A collection of objects providing information about every
                        port in an aggregation."
                ::= { alcatelIND1LAGMIBGroups 4 }


        alclnkaggAggPortDebugGroup OBJECT-GROUP
                OBJECTS {
                        alclnkaggAggPortDebugRxState,
                        alclnkaggAggPortDebugLastRxTime,
                        alclnkaggAggPortDebugMuxState,
                        alclnkaggAggPortDebugMuxReason,
                        alclnkaggAggPortDebugActorChurnState,
                        alclnkaggAggPortDebugPartnerChurnState,
                        alclnkaggAggPortDebugActorChurnCount,
                        alclnkaggAggPortDebugPartnerChurnCount,
                        alclnkaggAggPortDebugActorSyncTransitionCount,
                        alclnkaggAggPortDebugPartnerSyncTransitionCount,
                        alclnkaggAggPortDebugActorChangeCount,
                        alclnkaggAggPortDebugPartnerChangeCount
                }
                STATUS current
                DESCRIPTION
                        "A collection of objects providing debug information about.
                        every aggregated port."
                ::= { alcatelIND1LAGMIBGroups 5 }


        alclnkaggTablesLastChangedGroup OBJECT-GROUP
                OBJECTS {
                        alclnkaggTablesLastChanged
                }
                STATUS current
                DESCRIPTION
                        "A collection of objects providing information about the time
                        of changes to the configuration of aggregations and their ports."
                ::= { alcatelIND1LAGMIBGroups 6 }


        -- -------------------------------------------------------------
        -- compliance statements
        -- -------------------------------------------------------------


        alclnkaggAggCompliance MODULE-COMPLIANCE
                STATUS current
                DESCRIPTION
                        "The compliance statement for device support of
                        Link Aggregation."


                MODULE
                        MANDATORY-GROUPS {
                                alclnkaggAggGroup,
                                alclnkaggAggPortGroup,
                                alclnkaggTablesLastChangedGroup,
                                lnkaggNotificationVarsGroup
                        }


                        GROUP alclnkaggAggPortListGroup
                        DESCRIPTION
                                "This group is optional."


                        GROUP alclnkaggAggPortStatsGroup
                        DESCRIPTION
                                "This group is optional."


                        GROUP alclnkaggAggPortDebugGroup
                        DESCRIPTION
                                "This group is optional."

                        GROUP alclnkaggAggIdAccountGroup
                        DESCRIPTION
                                "This group is optional."

                        GROUP alclnkaggAggIdCounterGroup
                        DESCRIPTION
                                "This group is optional."

                        GROUP alclnkaggAggIdCounterErrGroup
                        DESCRIPTION
                                "This group is optional."

                        GROUP alclnkaggAggIdTrafficGroup
                        DESCRIPTION
                                "This group is optional."

                ::= { alcatelIND1LAGMIBCompliances 1 }



lnkaggNotificationVarsGroup OBJECT-GROUP
        OBJECTS
        {
                       traplnkaggAggId,
                       traplnkaggPortIfIndex
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects which appear only in notifications."
        ::= {alcatelIND1LAGMIBGroups  7 }

lnkaggTrapsGroup NOTIFICATION-GROUP
        NOTIFICATIONS {
            lnkaggAggUp,
            lnkaggAggDown,
            lnkaggPortJoin,
            lnkaggPortLeave,
            lnkaggPortRemove
        }
        STATUS  current
        DESCRIPTION
            "Collection of Traps for Link Aggregation ."
        ::= { alcatelIND1LAGMIBGroups 8 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

        lnkaggNotificationVars  OBJECT IDENTIFIER ::= { alcatelIND1LAGMIBObjects 4 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

        traplnkaggAggId OBJECT-TYPE
                SYNTAX  Integer32  (0..32)
                MAX-ACCESS  accessible-for-notify
                STATUS  current
                DESCRIPTION
                        "Index Value of Link Aggregation group  ."
           ::= {lnkaggNotificationVars  1 }

        traplnkaggPortIfIndex OBJECT-TYPE
                SYNTAX  Integer32  (1..2147483647)
                MAX-ACCESS  accessible-for-notify
                STATUS  current
                DESCRIPTION
                        "Port of  Link Aggregate group ."
           ::= {lnkaggNotificationVars   2 }







-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
--  NOTIFICATIONS
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
lnkaggAggUp NOTIFICATION-TYPE
        OBJECTS {
                traplnkaggAggId,
                traplnkaggPortIfIndex
                }
        STATUS current
        DESCRIPTION
                "The Link Aggregation  is  active .
                This trap is sent when any one port of the link aggregation
                goes to attached state."
        ::= { alcatelIND1LAGMIBNotifications 1 }



lnkaggAggDown NOTIFICATION-TYPE
        OBJECTS {
                traplnkaggAggId,
                traplnkaggPortIfIndex
                }
        STATUS current
        DESCRIPTION
                "The Link Aggregation  is  not active .
                This trap is sent when all  ports of the link aggregation
                goes out of attached state."
        ::= { alcatelIND1LAGMIBNotifications 2 }




lnkaggPortJoin NOTIFICATION-TYPE
        OBJECTS {
                traplnkaggAggId,
                traplnkaggPortIfIndex
                }
        STATUS current
        DESCRIPTION
                "The Link Aggregation port  joins  the aggregate .
                This trap is sent when given  port of the link aggregation
                goes to attached state."
        ::= { alcatelIND1LAGMIBNotifications 3 }

lnkaggPortLeave NOTIFICATION-TYPE
        OBJECTS {
                traplnkaggAggId,
                traplnkaggPortIfIndex
                }
        STATUS current
        DESCRIPTION
                "The Link Aggregation port  leaves  the aggregate .
                This trap is sent when given  port of the link aggregation
                goes out of  attached state."
        ::= { alcatelIND1LAGMIBNotifications 4 }

lnkaggPortRemove NOTIFICATION-TYPE
        OBJECTS {
                traplnkaggAggId,
                traplnkaggPortIfIndex
                }
        STATUS deprecated
        DESCRIPTION
                "The Link Aggregation port removed from the aggregate  .
                This trap is sent when given  port of the link aggregation
                removed due to invalid configuration ."
        ::= { alcatelIND1LAGMIBNotifications 5 }


-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

        alclnkaggAggConfig  OBJECT IDENTIFIER ::= { alcatelIND1LAGMIBObjects 5 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

--
--    Link Aggregation configuration group
--

    alclnkAggLocalRangeOperMin OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Minimum value of aggregate currently used  for
             local aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known.
             Valid linkagg range is from 0 to 127. Value -1 indicated none."
          DEFVAL     { 0 }
        ::= { alclnkaggAggConfig 1}


    alclnkAggLocalRangeOperMax OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Maximum value of aggregate currently used  for
             local aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. Value -1 indicated none."
          DEFVAL     { 127 }
        ::= { alclnkaggAggConfig 2}


    alclnkAggLocalRangeConfiguredMin OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-write
        STATUS        current
        DESCRIPTION
            "Minimum value of aggregate configured to be used for
             local aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. Value -1 indicated none."
          DEFVAL     { 0 }
        ::= { alclnkaggAggConfig 3}


    alclnkAggLocalRangeConfiguredMax OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-write
        STATUS        current
        DESCRIPTION
            "Maximum value of aggregate configured to be used for
             local aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. Value -1 indicated none."
          DEFVAL     { 127 }
        ::= { alclnkaggAggConfig 4}


    alclnkAggPeerRangeOperMin OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Minimum value of aggregate currently used  for
             peer aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 5}


    alclnkAggPeerRangeOperMax OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Maximum value of aggregate currently used  for
             peer aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 6}


    alclnkAggPeerRangeConfiguredMin OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-write
        STATUS        current
        DESCRIPTION
            "Minimum value of aggregate configured to be used for
             peer aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 7}


    alclnkAggPeerRangeConfiguredMax OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-write
        STATUS        current
        DESCRIPTION
            "Maximum value of aggregate configured to be used for
             peer aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 8}


    alclnkAggMcLagRangeOperMin OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Minimum value of aggregate currently used  for
             Multi Chassis aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 9}


    alclnkAggMcLagRangeOperMax OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Maximum value of aggregate currently used  for
             Multi Chassis aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 10}


    alclnkAggMcLagRangeConfiguredMin OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-write
        STATUS        current
        DESCRIPTION
            "Minimum value of aggregate configured to be used for
             Multi Chassis aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 11}


    alclnkAggMcLagRangeConfiguredMax OBJECT-TYPE
        SYNTAX        Integer32 ( -1 .. 127 )
        MAX-ACCESS    read-write
        STATUS        current
        DESCRIPTION
            "Maximum value of aggregate configured to be used for
             Multi Chassis aggregates. Used only when
             multi-chassis aggregation enabled and chassis id is known. 
	     Valid linkagg range is from 0 to 127. 
	     Value -1 indicated none or multi-chassis feature is not enabled."
          DEFVAL     { -1 }
        ::= { alclnkaggAggConfig 12}


    alclnkaggAggConfigGroup OBJECT-GROUP
                OBJECTS {
                        alclnkAggLocalRangeOperMin,
                        alclnkAggLocalRangeOperMax ,
                        alclnkAggLocalRangeConfiguredMin,
                        alclnkAggLocalRangeConfiguredMax,
                        alclnkAggPeerRangeOperMin,
                        alclnkAggPeerRangeOperMax ,
                        alclnkAggPeerRangeConfiguredMin,
                        alclnkAggPeerRangeConfiguredMax,
                        alclnkAggMcLagRangeOperMin,
                        alclnkAggMcLagRangeOperMax,
                        alclnkAggMcLagRangeConfiguredMin,
                        alclnkAggMcLagRangeConfiguredMax

                }
                STATUS current
                DESCRIPTION
                        "A collection of objects providing information about ranges
                        allowed aggregate number values."
                ::= { alcatelIND1LAGMIBGroups 9 }


         alclnkaggAggIdAccountGroup  OBJECT-GROUP
                OBJECTS {
                        alcRxUndersize,
			alcTxUndersize,
			alcRxOversize,
			alcTxOversize,
			alcRxPackets64,
			alcRxPackets127,
			alcRxPackets255,
			alcRxPackets511,
			alcRxPackets1023,
			alcRxPackets1518,
			alcRxPackets4095,
			alcRxPackets9216,
			alcRxJabberFrames
		}                                                                                                              
                STATUS current                                                                                                 
                DESCRIPTION                                                                                                    
                        "A collection of objects providing information about aggregate account info"

                ::= { alcatelIND1LAGMIBGroups 10 }


	  alclnkaggAggIdCounterGroup  OBJECT-GROUP                                                                        
                OBJECTS {                                                                                                
			alcInOctets,
			alcOutOctets,
			alcInUcastPkts,
			alcOutUcastPkts,
			alcInMcastPkts,
			alcOutMcastPkts,
			alcInBcastPkts,
			alcOutBcastPkts,
			alcInPauseFrames,
			alcOutPauseFrames
                }
                STATUS current 
                DESCRIPTION
                        "A collection of objects providing information about aggregate counter info"
                                                                                                                         
                ::= { alcatelIND1LAGMIBGroups 11 }

	   alclnkaggAggIdCounterErrGroup  OBJECT-GROUP                                                                                                                                                                                                
                OBJECTS {                                                                                                
			alcAlignmentsErrors,
			alcFCSErrors,
			alcIfInErrors,
			alcIfOutErrors
                }                                                                                                        
                STATUS current                                                                                           
                DESCRIPTION                                                                                              
                        "A collection of objects providing information about aggregate counter error info"                     
                                                                                                                         
                ::= { alcatelIND1LAGMIBGroups 12 } 


	   alclnkaggAggIdTrafficGroup  OBJECT-GROUP
                OBJECTS {     
			alcInputPackets,
			alcInputBytes,
			alcOutputPackets,
			alcOutputBytes	
                }                                                                                                        
                STATUS current                                                                                           
                DESCRIPTION                                                                                              
                        "A collection of objects providing information about aggregate traffic info"                                                                                                                                        
                                                                                                                         
                ::= { alcatelIND1LAGMIBGroups 13 }



        END
