ALCATEL-IND1-IP-MIB DEFINITIONS ::= <PERSON><PERSON><PERSON>

IMPORTS
        NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-IDENTITY, OBJECT-TYP<PERSON>,
        Integer32, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Unsigned32
                FROM SNMPv2-SMI
        PhysAddress, <PERSON>splayString, RowStatus, MacAddress, TimeStamp,
        TEXTUAL-CONVENTION
                FROM SNMPv2-TC
        SnmpAdminString
                FROM SNMP-FRAMEWORK-MIB
        ipNetToMediaIfIndex, ipNetToMediaNetAddress, ipNetToMediaEntry
                FROM IP-MIB
        ipCidrRouteEntry
                FROM IP-FORWARD-MIB
        MODULE-COMPLIANCE, OBJECT-GROUP,
        NOTIFICATION-GROUP
                FROM SNMPv2-CONF
        softentIND1Ip
                FROM ALCATEL-IND1-BASE
        InetAddressType, InetAddress
                FROM INET-ADDRESS-<PERSON><PERSON>
        ifIndex, InterfaceIndex, InterfaceIndexOrZero
                FROM IF-MIB;

alcatelIND1IPMIB MODULE-<PERSON><PERSON><PERSON><PERSON>
    LAST-UPDATED "201602250000Z"
    ORGANIZATION "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:

                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America

        Telephone:               North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507

        Electronic Mail:         <EMAIL>
        World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
        File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):

             Propietary IP MIB definitions

         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.

         No liability shall be assumed for any incidental, indirect, special,
         or consequential damages whatsoever arising from or related to this
         document or the information contained herein.

         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.

                     Copyright (C) 1995-2006 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "201602250000Z"
    DESCRIPTION
        "Mark as obsolete the old chassis/slot/port values"

    REVISION      "201509090000Z"
    DESCRIPTION
        "The alaIpPortServiceTable is deprecated."

    REVISION      "201410170000Z"
    DESCRIPTION
        "added alaIpServiceSourceIpTable"

    REVISION      "201203230000Z"
    DESCRIPTION
        "added ipNetToMediaChassisId, alaDoSChassisId"

    REVISION      "201103070000Z"
    DESCRIPTION
        "Increased size of alaIpInterfaceName and alaIpItfConfigName to 32"

    REVISION      "201101250000Z"
    DESCRIPTION
        "Changed alaDoSStatus to read-write."

    REVISION      "201005130000Z"
    DESCRIPTION
        "Fixed the Notifications to use MIB Module OID.0 as Notifications root."

    REVISION      "200905190000Z"
    DESCRIPTION
        "The latest version of this MIB Module."

    ::= { softentIND1Ip 1 }

alcatelIND1IPMIBNotifications OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For IP MIB Subsystem Notifications."
        ::= { alcatelIND1IPMIB 0 }

alcatelIND1IPMIBObjects OBJECT IDENTIFIER ::= { alcatelIND1IPMIB 1 }

AlaIpServiceSourceIpAppIndex ::= TEXTUAL-CONVENTION
    STATUS   current
    DESCRIPTION
        "The application index for the service source IP table entry."
    SYNTAX   INTEGER
    {
        all(1),
        dns(2),
        ftp(3),
        ldap(4),
        ntp(5),
        radius(6),
        sflow(7),
        snmp(8),
        ssh(9),
        swlog(10),
        tacacs(11),
        telnet(12),
        tftp(13)
    }

--
-- Alcatel IP stack configuration
--

alaIpConfig OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 1 }

alaIpClearArpCache OBJECT-TYPE
    SYNTAX     INTEGER {
                    clear(1)
               }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "Clear dynamic ARP entries from ARP cache."
    DEFVAL     { clear }
    ::= { alaIpConfig 1 }

alaIpDirectedBroadcast OBJECT-TYPE
    SYNTAX      INTEGER {
                    on(1), -- Forward IP directed broadcasts
                    off(0) -- NOT forward IP directed broadcasts
               }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The indication of whether this router is forwarding IP
             directed broadcasts"
    DEFVAL     { off }
    ::= { alaIpConfig 2 }

alaIpClearArpFilter OBJECT-TYPE
    SYNTAX     INTEGER {
                    clear(1)
               }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "Clear ARP filters entries."
    DEFVAL     { clear }
    ::= { alaIpConfig 3 }

alaIpDistributedArp OBJECT-TYPE
    SYNTAX      INTEGER {
                    on(1), -- Distributed ARP
                    off(0) -- NOT distributed ARP
               }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
            "Enables/Disables the distributed ARP feature.  The
            feature creates designated NIs for ip interfaces.
            This allows more hardware ARP entries on a switch/VC."
    DEFVAL     { off }
    ::= { alaIpConfig 4 }

--
-- proprietary alcatel ipNetToMediaTable, only ARP entries
-- with special flag (e.g.  proxy, vrrp, avlan) appears in this MIB.
-- since proxy and vrrp can coexist, each flag is a columnar object.
--
alaIpNetToMediaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIpNetToMediaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of ARP flags, expansion to ipNetToMediaTable."
    ::= { alcatelIND1IPMIBObjects 2 }

alaIpNetToMediaEntry OBJECT-TYPE
    SYNTAX      AlaIpNetToMediaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in alaIpToMediaTable."
    INDEX  { ipNetToMediaIfIndex, ipNetToMediaNetAddress }
    ::= { alaIpNetToMediaTable 1 }

AlaIpNetToMediaEntry ::= SEQUENCE {
        alaIpNetToMediaPhysAddress      PhysAddress,
        alaIpNetToMediaProxy            INTEGER,
        alaIpNetToMediaVrrp             INTEGER,
        alaIpNetToMediaAuth             INTEGER,
        alaIpNetToMediaName             SnmpAdminString,
        alaIpNetToMediaStaticIntfRt     INTEGER
        }

alaIpNetToMediaPhysAddress OBJECT-TYPE
    SYNTAX      PhysAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "The media-dependent `physical' address."
    ::= { alaIpNetToMediaEntry 1 }

alaIpNetToMediaProxy OBJECT-TYPE
    SYNTAX      INTEGER {
                no(0),  -- not proxy ARP
                yes(1)  -- proxy ARP
               }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "Proxy ARP flag"
    ::= { alaIpNetToMediaEntry 2  }

alaIpNetToMediaVrrp OBJECT-TYPE
    SYNTAX      INTEGER {
                no(0),  -- not VRRP ARP
                yes(1)  -- VRRP ARP
               }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "VRRP ARP flag"
    ::= { alaIpNetToMediaEntry 3  }

alaIpNetToMediaAuth OBJECT-TYPE
    SYNTAX      INTEGER {
                no(0),  -- not AVLAN ARP
                yes(1)  -- AVLAN ARP
               }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Authentication VLAN ARP flags"
    ::= { alaIpNetToMediaEntry 4  }

alaIpNetToMediaName OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE (0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of the entry."
    ::= { alaIpNetToMediaEntry 5  }

alaIpNetToMediaStaticIntfRt OBJECT-TYPE
    SYNTAX      INTEGER {
                no(0),  -- not associated with static-intf-route
                yes(1)  -- ARP is associated with static-intf-route
               }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Static-Interface-Route ARP flag"
    ::= { alaIpNetToMediaEntry 6  }


--
-- DoS stuff
--
alaDoSConfig OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 3 }

alaDoSTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaDoSEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of DoS (denial of service) attacks detected"
    ::= { alaDoSConfig 1 }

alaDoSEntry OBJECT-TYPE
    SYNTAX      AlaDoSEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the alaDoStable"
    INDEX { alaDoSType }
    ::= { alaDoSTable 1 }

AlaDoSEntry ::= SEQUENCE {
        alaDoSType              INTEGER,
        alaDoSDetected          Counter32,
        alaDoSIp                IpAddress,
        alaDoSMac               MacAddress,
        alaDoSSlot              Integer32,
        alaDoSPort              Integer32,
        alaDoSStatus            INTEGER,
        alaDoSChassisId         Integer32,
        alaDoSPortIfindex       InterfaceIndexOrZero
        }

alaDoSType OBJECT-TYPE
    SYNTAX      INTEGER {
                portscan(0),
                tcpsyn(1),
                pingofdeath(2),
                smurf(3),
                pepsi(4),
                land(5),
                teardropBonkBoink(6),
                loopbacksrcip(7),
                invalidip(8),
                mcastmismatch(9),
                ucastipmcastmac(10),
                pingattack(11),
                arpattack(12),
                arppoison(13),
                antispoof(14)
                }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "index field for the alaDoSTable.  Note: tcpsyn, smurf, pepsi and
		teardropBonkBoink are deprecated."
    ::= { alaDoSEntry 1 }

alaDoSDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Number of attacks detected"
    ::= { alaDoSEntry 2 }

alaDoSIp OBJECT-TYPE
        SYNTAX IpAddress
        MAX-ACCESS accessible-for-notify
        STATUS current
        DESCRIPTION
                "Source IP address from recent suspect packet."
        ::= { alaDoSEntry 3 }

alaDoSMac OBJECT-TYPE
        SYNTAX MacAddress
        MAX-ACCESS accessible-for-notify
        STATUS current
        DESCRIPTION
                "Source MAC address from recent suspect packet."
        ::= { alaDoSEntry 4 }

alaDoSSlot OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS accessible-for-notify
        STATUS obsolete
        DESCRIPTION
                "Slot on which recent suspect packet was received.
                 This object is deprecated in favor of alaDoSPortIfindex."
        ::= { alaDoSEntry 5 }

alaDoSPort OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS accessible-for-notify
        STATUS obsolete
        DESCRIPTION
                "Port on which recent suspect packet was received.
                 This object is deprecated in favor of alaDoSPortIfindex."
        ::= { alaDoSEntry 6 }

alaDoSStatus OBJECT-TYPE
        SYNTAX  INTEGER {
                enabled (1),
                disabled(2)
    }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
            "enable/disable DoS attack Detection"
    DEFVAL { enabled }
    ::= { alaDoSEntry 7 }

alaDoSChassisId OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS accessible-for-notify
        STATUS obsolete
        DESCRIPTION
                "Chassis on which recent suspect packet was received.
                 This object is deprecated in favor of alaDoSPortIfindex."
        ::= { alaDoSEntry 8 }

alaDoSPortIfindex OBJECT-TYPE
        SYNTAX InterfaceIndexOrZero
        MAX-ACCESS accessible-for-notify
        STATUS current
        DESCRIPTION
                "Ifindex of port on which suspect packet was received."
        ::= { alaDoSEntry 9 }

--
-- Port scan parameters
--
alaDoSPortScanClosePortPenalty OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Penalty points added to the total port scan penalty value
         when a TCP/UDP packet destinated to a closed port is
         received."
    DEFVAL { 10 }
    ::= { alaDoSConfig 2 }

alaDoSPortScanTcpOpenPortPenalty OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Penalty points added to the total port scan penalty value
         when a TCP SYN/FIN packet destinated to a open TCP port is
         received. No difference between legal TCP packet
         and port scan packet."
    DEFVAL { 0 }
    ::= { alaDoSConfig 3 }

alaDoSPortScanUdpOpenPortPenalty OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Penalty points added to the total port scan penalty value
         when a UDP packet destinated to a open UDP port is
         received. No difference between legal UDP packet
         and port scan packet."
    DEFVAL { 0 }
    ::= { alaDoSConfig 4 }

alaDoSPortScanTotalPenalty OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total port scan penalty value accumulated at present time"
    ::= { alaDoSConfig 5 }

alaDoSPortScanThreshold OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Threshold port scan penalty value. If the total port scan penalty value
         exceeds this value, a port scan attack is recorded"
    DEFVAL { 1000 }
    ::= { alaDoSConfig 6 }

alaDoSPortScanDecay OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Decay speed for the total port scan penalty value. The penalty value
         is reduced by a factor of the decay value every minute"
    DEFVAL { 2 }
    ::= { alaDoSConfig 7 }

alaDoSTrapCntl  OBJECT-TYPE
     SYNTAX       INTEGER {
         enabled     (1),
         disabled    (2)
     }
     MAX-ACCESS   read-write
     STATUS       current
     DESCRIPTION
        "Indicates whether the switch will generate
         SNMP traps for alaDoSTraps. 'Enabled'
         results in SNMP traps; 'disabled', no traps are sent."
     DEFVAL { enabled }
     ::= { alaDoSConfig 8 }

--
-- ARP/Ping Rates for Dos detection
--
alaDoSARPRate OBJECT-TYPE
    SYNTAX      Integer32 (1..500)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
      "Rate of ARP Requests, which if exceeded, an ARP Flood
       attack is detected."
          DEFVAL { 500 }
          ::= { alaDoSConfig 9 }

alaDoSPingRate OBJECT-TYPE
    SYNTAX      Integer32 (1..10000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
      "Rate of ICMP Requests, which if exceeded, a Ping Overload
       attack is detected."
          DEFVAL { 100 }
          ::= { alaDoSConfig 10 }

--
-- proprietary alcatel AlaIpArpPoisonTable, the switch will periodically generate
-- ARP Requests for these addresses, to detect an ARP Poisoning Attack if a reply is received
--
alaDoSArpPoisonTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AlaDoSArpPoisonEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        "A list of IP addresses for which periodic ARP Requests are to be generated,
         and from which if an ARP Packet is received, an ARP Poison Attack is detected."
        ::= { alaDoSConfig 11 }

alaDoSArpPoisonEntry OBJECT-TYPE
        SYNTAX AlaDoSArpPoisonEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        " An entry in the alaDoSArpPoisonTable."
        INDEX { alaDoSArpPoisonIpAddr }
        ::= { alaDoSArpPoisonTable 1 }

AlaDoSArpPoisonEntry ::= SEQUENCE {
        alaDoSArpPoisonIpAddr      IpAddress,
        alaDoSArpPoisonDetected    Counter32,
        alaDoSArpPoisonRowStatus   RowStatus
        }

alaDoSArpPoisonIpAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
    "The IP address from which an ARP packet is considered an ARP Poison attack."
        ::= { alaDoSArpPoisonEntry 1 }

alaDoSArpPoisonDetected OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of ARP Poison attacks detected."
    ::= { alaDoSArpPoisonEntry 2 }

alaDoSArpPoisonRowStatus  OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
          "enable/disable ARP Poisoning for this IP"
    ::= { alaDoSArpPoisonEntry 3 }

--
-- ANTI SPOOF 
--
alaDoSAntiSpoofGlobalCountReset OBJECT-TYPE
    SYNTAX      INTEGER {
                default(1),
                reset(2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Reset Anti-spoof counter value for each IP/VRRP Interface."
    DEFVAL      { default }
    ::= { alaDoSConfig 12 }

alaDoSAntiSpoofTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaDoSAntiSpoofEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of IP addresses for which Anti Spoofing needs to be configured "
    ::= { alaDoSConfig 13 }

alaDoSAntiSpoofEntry OBJECT-TYPE
    SYNTAX      AlaDoSAntiSpoofEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        " An entry in the alaDoSAntiSpoofTable."
    INDEX { alaDoSAntiSpoofIPAddressType, alaDoSAntiSpoofIPAddress }
    ::= { alaDoSAntiSpoofTable 1 }

AlaDoSAntiSpoofEntry ::= SEQUENCE {
        alaDoSAntiSpoofIPAddressType       InetAddressType,
        alaDoSAntiSpoofIPAddress           InetAddress,
        alaDoSAntiSpoofStatus              INTEGER,
        alaDoSAntiSpoofMacAddress          MacAddress,
        alaDoSAntiSpoofIfIndex             InterfaceIndexOrZero,
        alaDoSAntiSpoofVlan                Integer32,
        alaDoSAntiSpoofCount               Counter32,
        alaDoSAntiSpoofCountReset          INTEGER,
        alaDoSAntiSpoofRowStatus           RowStatus
        }

alaDoSAntiSpoofIPAddressType OBJECT-TYPE
    SYNTAX      InetAddressType {
                ipv4(1),
                ipv6(2)
                }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IP address type of the IP address for which spoofing needs to be configured."
    ::= { alaDoSAntiSpoofEntry 1 }

alaDoSAntiSpoofIPAddress OBJECT-TYPE
    SYNTAX      InetAddress (SIZE(4|16))
    MAX-ACCESS  not-accessible 
    STATUS      current
    DESCRIPTION
        "The IP address for which spoofing needs to be configured."
    ::= { alaDoSAntiSpoofEntry 2 }

alaDoSAntiSpoofStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                enable (1),
                disable (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Enable or disable IP Anti Spoofing for each IP/VRRP Interface."
    DEFVAL { enable }
    ::= { alaDoSAntiSpoofEntry 3 }

alaDoSAntiSpoofMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        " MAC address of the attacked IP Address."
    ::= { alaDoSAntiSpoofEntry 4 }

alaDoSAntiSpoofIfIndex OBJECT-TYPE
    SYNTAX      InterfaceIndexOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IfIndex of the Interface on which the suspect packet was received."
    ::= { alaDoSAntiSpoofEntry 5 }

alaDoSAntiSpoofVlan OBJECT-TYPE
    SYNTAX      Integer32 (0..4094)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The VLAN to which the interface is bound; 0 if unbound."
    DEFVAL { 0 }
    ::= { alaDoSAntiSpoofEntry 6 }

alaDoSAntiSpoofCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total number of attacks eachIP/VRRP Interface address"
    ::= { alaDoSAntiSpoofEntry 7}   

alaDoSAntiSpoofCountReset OBJECT-TYPE
    SYNTAX      INTEGER {
                default(1),
                reset(2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "To reset the counter values for each IP/VRRP Interface."
    ::= { alaDoSAntiSpoofEntry 8 }

alaDoSAntiSpoofRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        " The row status of this table entry."
    ::= { alaDoSAntiSpoofEntry 9}

--
-- Expansion of ipNetToMedia table
--
ipNetToMediaAugTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF IpNetToMediaAugEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        " slot/port expansion to ipNetToMediaTable."
    ::= { alcatelIND1IPMIBObjects 4 }

ipNetToMediaAugEntry OBJECT-TYPE
    SYNTAX      IpNetToMediaAugEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in ipNetToMediaAugTable."
    AUGMENTS  { ipNetToMediaEntry }
    ::= { ipNetToMediaAugTable 1 }

IpNetToMediaAugEntry ::= SEQUENCE {
        ipNetToMediaSlot                Integer32,
        ipNetToMediaPort                Integer32,
        ipNetToMediaName                SnmpAdminString,
        ipNetToMediaChassisId           Integer32,
        ipNetToMediaPortIfindex         InterfaceIndexOrZero,
        ipNetToMediaSubType             INTEGER,
        ipNetToMediaSubId               Integer32 
        }

ipNetToMediaSlot OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Slot associated with address translation. An address translation
         not associated with a slot/port pair will have ipNetToMediaSlot
         equal to zero and ipNetToMediaPort equal to zero. An address
         associated with an Ethernet Management Port (EMP) will have
         ipNetToMediaSlot equal to zero and ipNetToMediaPort equal to -1.
         An address associated with a link agg port will have ipNetToMediaSlot
         equal to zero and ipNetToMediaPort equal to the link agg id.
         This object is deprecated in favor of ipNetToMediaPortIfindex to
         specify the port the address translation was learned on."
    ::= { ipNetToMediaAugEntry 1  }

ipNetToMediaPort OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Port associated with address translation. An address translation
         not associated with a slot/port pair will have ipNetToMediaSlot
         equal to zero and ipNetToMediaPort equal to zero. An address
         associated with an Ethernet Management Port(EMP) will have
         ipNetToMediaSlot equal to zero and ipNetToMediaPort equal to -1.
         An address associated with a link agg port will have ipNetToMediaSlot
         equal to zero and ipNetToMediaPort equal to the link agg id.
         This object is deprecated in favor of ipNetToMediaPortIfindex to
         specify the port the address translation was learned on."
    ::= { ipNetToMediaAugEntry 2  }

ipNetToMediaName OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE (0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of the entry."
    ::= { ipNetToMediaAugEntry 3  }

ipNetToMediaChassisId OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Chassis Id associated with address translation.
         This object is deprecated in favor of ipNetToMediaPortIfindex to
         specify the port the address translation was learned on."
    ::= { ipNetToMediaAugEntry 4  }

ipNetToMediaPortIfindex OBJECT-TYPE
    SYNTAX      InterfaceIndexOrZero
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
    "Ifindex of the port for this address translation. 
    This object carries different values for address translations on 
    physical ports and virtual ports (SAPs and SDPs)
    This field should be set to one of the following
      1. ifIndex of the physical port for translations on Vlan domain (physical port)
      2. ifIndex of the SAP for the translations on service (VPLS, SPBM & VxLAN) domain SAPs or
      3. SDP_ID of a SDP for translations on service domain SDPs"
    ::= { ipNetToMediaAugEntry 5  }

ipNetToMediaSubType OBJECT-TYPE
    SYNTAX     INTEGER {
                  default(0),
                  sap(1),
                  sBind(2)
            }
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This object is used for network to media entries learnt on the
      virtual ports (SAPs & SDPs), the virtual port could be created by
      SPB-M,VPLS or VxLAN services. this is invalid for the entries
      learnt on the physical ports and always set to 0 on physical ports"
   DEFVAL      { default }
   ::= { ipNetToMediaAugEntry 6 }

ipNetToMediaSubId OBJECT-TYPE
   SYNTAX        Integer32  (0 .. 2147483647)
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
     "This object is used for the entries learnt on the virtual ports (SAPs & SDPs).
     This is invalid for the entries lernt on the physical ports and the value
     will always be set to 0 on physical ports (virtual ports are created by SPB-M,VPLS or 
     VxLAN services). This field should be the EncapId for SAP, and service id for SBIND.
      
     If this entry is learnt on a SAP, this object holds  vlan id (1 .. 4096)
     this entry is leant in SDP, this object holds service id (1 .. 32767)"
   ::= { ipNetToMediaAugEntry 7 }



--
-- DoS traps
--

alaDoSTrap NOTIFICATION-TYPE
     OBJECTS      { alaDoSType,
                    alaDoSDetected,
                    alaDoSIp,
                    alaDoSMac,
                    alaDoSSlot,
                    alaDoSPort,
                    alaDoSChassisId,
                    alaDoSPortIfindex
                  }
     STATUS       current
     DESCRIPTION
         "The DoS trap indicates that the sending agent
         has received DoS attack"
     ::= { alcatelIND1IPMIBNotifications 1 }

--
-- Traps for traffic events
--

trafficEventTrapObjs OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 5 }

pktDrop NOTIFICATION-TYPE
     OBJECTS      { pktDropType,
                    pktDropIfIndex,
                    pktDropCount,
                    pktDropFrag
                  }
     STATUS       current
     DESCRIPTION
         "The pktDrop trap indicates that the sending agent
         has dropped certain packets (to blocked IP ports,
         from spoofed addresses, etc)."
     ::= { alcatelIND1IPMIBNotifications 2 }

--
-- Traffic Event Traps' objects
--

pktDropType OBJECT-TYPE
        SYNTAX  INTEGER {
                spoofedIp(0),
                toBlockedPort(1),
                rulematchTriggeredPortDisable(2),
                spoofTriggeredUserPortDisable(3),
                bpduTriggeredUserPortDisable(4),
                bgpTriggeredUserPortDisable(5),
                ospfTriggeredUserPortDisable(6),
                ripTriggeredUserPortDisable(7),
                vrrpTriggeredUserPortDisable(8)
        }
        MAX-ACCESS      accessible-for-notify
        STATUS  current
        DESCRIPTION "Reason index for why this packet was dropped"
        ::= { trafficEventTrapObjs 1 }

pktDropIfIndex OBJECT-TYPE
        SYNTAX  InterfaceIndexOrZero
        MAX-ACCESS      accessible-for-notify
        STATUS  current
        DESCRIPTION "Interface index (if_index) of ingress port of this dropped pkt"
        ::= { trafficEventTrapObjs 2 }

pktDropCount OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS      accessible-for-notify
        STATUS  current
        DESCRIPTION "The # of pkt drops (within a configured time interval)
of this pktDropType that triggered this particular trap instance"
        ::= { trafficEventTrapObjs 3 }

pktDropFrag OBJECT-TYPE
        SYNTAX  OCTET STRING (SIZE(0..512))
        MAX-ACCESS      accessible-for-notify
        STATUS  current
        DESCRIPTION "<= 512 bytes of the dropped pkt (dsmac[12], tag[4], etype[2], payload[..512] (0 if DropCount only)"
        ::= { trafficEventTrapObjs 4 }

--
-- Expansion to ipCidrRouteTable, add flag to show NI route
-- or EMP route
--

ipCidrRouteAugTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF IpCidrRouteAugEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "NI/EMP flag expansion to ipCidrRouteTable."
    ::= { alcatelIND1IPMIBObjects 6 }

ipCidrRouteAugEntry OBJECT-TYPE
    SYNTAX      IpCidrRouteAugEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in ipCidrRouteAugTable."
    AUGMENTS  { ipCidrRouteEntry }
    ::= { ipCidrRouteAugTable 1 }

IpCidrRouteAugEntry ::= SEQUENCE {
        ipCidrRouteScope INTEGER
        }

ipCidrRouteScope OBJECT-TYPE
    SYNTAX      INTEGER {
                        niroute(1),  -- routes on NI
                        emproute(2)  -- routes on EMP
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Indicate the route is NI route or EMP route"
    ::= { ipCidrRouteAugEntry 1  }

--
-- ICMP control
--
alaIcmpCtrlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIcmpCtrlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table to control ICMP message generation from the switch."
    ::= { alcatelIND1IPMIBObjects 7 }

alaIcmpCtrlEntry OBJECT-TYPE
    SYNTAX      AlaIcmpCtrlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the alaIcmpCtrlTable"
    INDEX { alaIcmpCtrlType,
            alaIcmpCtrlCode }
    ::= { alaIcmpCtrlTable 1 }

AlaIcmpCtrlEntry ::= SEQUENCE {
        alaIcmpCtrlType         Integer32,
        alaIcmpCtrlCode         Integer32,
        alaIcmpCtrlStatus       INTEGER,
        alaIcmpCtrlPktGap       Integer32
        }

alaIcmpCtrlType OBJECT-TYPE
    SYNTAX      Integer32 (0..18)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "ICMP message type"
    ::= { alaIcmpCtrlEntry 1 }

alaIcmpCtrlCode  OBJECT-TYPE
    SYNTAX      Integer32 (0..15)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "ICMP message code"
    ::= { alaIcmpCtrlEntry 2 }

alaIcmpCtrlStatus  OBJECT-TYPE
    SYNTAX      INTEGER {
                enabled (1),
                disabled(2)
    }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "enable/disable ICMP message generation"
    DEFVAL { enabled }
    ::= { alaIcmpCtrlEntry 3 }

alaIcmpCtrlPktGap OBJECT-TYPE
     SYNTAX       Integer32
     MAX-ACCESS   read-write
     STATUS       current
     DESCRIPTION
        "Minimum packet gap in microseconds between the two successive
         ICMP messages with the same type and code"
     DEFVAL { 0 }
     ::= { alaIcmpCtrlEntry 4 }


--
-- IP route summary
--
alaIpRouteSumTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIpRouteSumEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "IP route summary table"
    ::= { alcatelIND1IPMIBObjects 9 }

alaIpRouteSumEntry OBJECT-TYPE
    SYNTAX      AlaIpRouteSumEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the alaIpRouteSumTable"
    INDEX { alaIpRouteProtocol }
    ::= { alaIpRouteSumTable 1 }

AlaIpRouteSumEntry ::= SEQUENCE {
        alaIpRouteProtocol      INTEGER,
        alaIpRouteCount         Integer32
        }

alaIpRouteProtocol  OBJECT-TYPE
    SYNTAX      INTEGER {
                  total         (1), -- total number
                  local         (2), -- local interface
                  netmgmt       (3), -- static route
                  rip           (4), -- RIP or RIP-II
                  isis          (5), -- ISIS
                  ospf          (6), -- Open Shortest Path First
                  bgp           (7), -- Border Gateway Protocol
                  other         (8)  -- other not specified protocols
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP route protocol"
    ::= { alaIpRouteSumEntry 1 }

alaIpRouteCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP route count"
    ::= { alaIpRouteSumEntry 2 }

--
-- Object to turn on/off all ICMP control messages
--
alaIcmpCtrl OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 10 }

alaIcmpAllMsgStatus  OBJECT-TYPE
    SYNTAX      INTEGER {
                enabled (1),
                disabled(2),
                other(3)
    }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/disable all ICMP messages on the switch. On set operation,
         only enabled(1)/disabled(2) is allowed. Other(3) will be returned
         in get operation, if not all ICMP messages are enabled and not all
         ICMP messages are disabled."
    DEFVAL { enabled }
    ::= { alaIcmpCtrl 1 }

--
-- proprietary alcatel AlaIpArpFilterTable, the switch will not respond to ARPs
-- received who have an IP entry in the block list.
--
alaIpArpFilterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIpArpFilterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of IP addresses for ARP to ignore."
    ::= { alcatelIND1IPMIBObjects 11 }

alaIpArpFilterEntry OBJECT-TYPE
    SYNTAX      AlaIpArpFilterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in alaIpArpFilterTable."
    INDEX  { alaIpArpFilterIpAddr, alaIpArpFilterIpMask, alaIpArpFilterVlan,
             alaIpArpFilterType }
    ::= { alaIpArpFilterTable 1 }

AlaIpArpFilterEntry ::= SEQUENCE {
        alaIpArpFilterIpAddr    IpAddress,
        alaIpArpFilterIpMask    IpAddress,
        alaIpArpFilterVlan      Integer32,
        alaIpArpFilterType      INTEGER,
        alaIpArpFilterMode      INTEGER,
        alaIpArpFilterRowStatus RowStatus
        }

alaIpArpFilterIpAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The IP address to block ARP responses."
    ::= { alaIpArpFilterEntry 1 }


alaIpArpFilterIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "The IP address mask to block ARP responses."
    ::= { alaIpArpFilterEntry 2 }

alaIpArpFilterVlan  OBJECT-TYPE
    SYNTAX      Integer32 (0..4094)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Vlan for filter. 0 disablesP"
    DEFVAL { 0 }
    ::= { alaIpArpFilterEntry 3 }

alaIpArpFilterType  OBJECT-TYPE
    SYNTAX      INTEGER {
                target(1),
                sender(2)
    }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "This indicates which parameter of the ARP packet to filter on."
    DEFVAL { target }
    ::= { alaIpArpFilterEntry 4 }

alaIpArpFilterMode  OBJECT-TYPE
    SYNTAX      INTEGER {
                allow   (1),
                block (2)
    }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "allow/block ARP for this filter entry"
    DEFVAL { allow }
    ::= { alaIpArpFilterEntry 5 }

alaIpArpFilterRowStatus  OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "enable/disable ARP block for this IP"
    ::= { alaIpArpFilterEntry 6 }

--
-- IP service table
--

alaIpServiceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIpServiceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table to enable/disable IP services on switch"
    ::= { alcatelIND1IPMIBObjects 12 }

alaIpServiceEntry OBJECT-TYPE
    SYNTAX      AlaIpServiceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in alaIpServiceTable"
    INDEX { alaIpServiceType }
    ::= { alaIpServiceTable 1 }

AlaIpServiceEntry ::= SEQUENCE {
        alaIpServiceType        INTEGER,
        alaIpServicePort        Integer32,
        alaIpServiceStatus      INTEGER
        }

alaIpServiceType OBJECT-TYPE
    SYNTAX      INTEGER {
                ftp(1),
                ssh(2),
                telnet(3),
                http(4),
                ntp(5),
                snmp(6),
                https(7),
                radius(8)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP service type"
    ::= { alaIpServiceEntry 1 }

alaIpServicePort OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP service port. To modify the default port, use corresponding MIB attribute below:
         - ftp : alaIpFtpPort from alaIpFtpConfig in ALCATEL-IND1-IP-MIB.mib
         - ssh : alaIpSshPort from alaIpSshConfig in ALCATEL-IND1-IP-MIB.mib
         - telnet : alaIpTelnetPort from alaIpTelnetConfig in ALCATEL-IND1-IP-MIB.mib
         - http : alaIND1WebMgtHttpPort from alcatelIND1WebMgtMIBObjects in ALCATEL-IND1-WEBMGT-MIB.mib
         - ntp : can't be modified
         - snmp : can't be modified
         - https : alaIND1WebMgtHttpsPort from alcatelIND1WebMgtMIBObjects in ALCATEL-IND1-WEBMGT-MIB.mib
         - radius : can't be modified"

    ::= { alaIpServiceEntry 2 }

alaIpServiceStatus  OBJECT-TYPE
    SYNTAX      INTEGER {
                enabled (1),
                disabled(2),
                other(3)
    }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When read, returns enabled if service and port are enabled, and returns disabled otherwise.
         When written, enables/disables service and port 
         (although snmp is never fully shut down; if disabled in some VRF, it will run in the default VRF).
         Note that that http, https, and ntp here are read-only; use alternatives described below to enable/disable service.
         If you want to block packet and shut down service, use corresponding MIB attribute below:
         - ftp : alaIpFtpAdminStatus from alaIpFtpConfig in ALCATEL-IND1-IP-MIB.mib
         - ssh : alaIpSshAdminStatus from alaIpSshConfig in ALCATEL-IND1-IP-MIB.mib
         - telnet : alaIpTelnetAdminStatus from alaIpTelnetConfig in ALCATEL-IND1-IP-MIB.mib
         - http enable : alaIND1WebMgtAdminStatus from alcatelIND1WebMgtMIBObjects in ALCATEL-IND1-WEBMGT-MIB.mib
         - http disable : alaIND1WebMgtServerStatus from alcatelIND1WebMgtMIBObjects in ALCATEL-IND1-WEBMGT-MIB.mib
         - ntp : alaIpNtpVrfName (in this mib), and alaNtpEnable and alaNtpBroadcastEnable from alaNtpConfig in ALCATEL-IND1-NTP-MIB.mib
         - snmp : [no alternative -- use this MIB object]
         - https : same as http
         - radius: [no alternative -- use this MIB object]"

    DEFVAL { enabled }
    ::= { alaIpServiceEntry 3 }

--
-- The deprecated IP port service table
--

alaIpPortServiceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIpPortServiceEntry
    MAX-ACCESS  not-accessible
    STATUS      deprecated
    DESCRIPTION
        "Table to enable/disable IP services by port on switch.
         This table has been deprecated in favor of the
         alaIpServiceTable."
    ::= { alcatelIND1IPMIBObjects 13 }

alaIpPortServiceEntry OBJECT-TYPE
    SYNTAX      AlaIpPortServiceEntry
    MAX-ACCESS  not-accessible
    STATUS      deprecated
    DESCRIPTION
        "An entry in alaIpPortServiceTable"
    INDEX { alaIpPortServicePort }
    ::= { alaIpPortServiceTable 1 }

AlaIpPortServiceEntry ::= SEQUENCE {
        alaIpPortServicePort     Integer32,
        alaIpPortServiceStatus   INTEGER
        }

alaIpPortServicePort OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "IP service port"
    ::= { alaIpPortServiceEntry 1 }

alaIpPortServiceStatus  OBJECT-TYPE
    SYNTAX      INTEGER {
                enabled (1),
                disabled(2)
    }
    MAX-ACCESS  read-write
    STATUS      deprecated
    DESCRIPTION
        "enable/disable IP service"
    DEFVAL { enabled }
    ::= { alaIpPortServiceEntry 2 }

--
-- IP Interface Configuration
--
alaIpInterfaceTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AlaIpInterfaceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                "A list of IP interfaces in this switch."
        ::= { alcatelIND1IPMIBObjects 14  }

alaIpInterfaceEntry OBJECT-TYPE
        SYNTAX  AlaIpInterfaceEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                "An IP interface entry."
        INDEX { ifIndex }
        ::= { alaIpInterfaceTable 1 }

AlaIpInterfaceEntry ::= SEQUENCE {
        alaIpInterfaceName SnmpAdminString,
        alaIpInterfaceAddress IpAddress,
        alaIpInterfaceMask IpAddress,
        alaIpInterfaceAdminState INTEGER,
        alaIpInterfaceDeviceType INTEGER,
        alaIpInterfaceVlanID Integer32,
        alaIpInterfaceIpForward INTEGER,
        alaIpInterfaceEncap INTEGER,
        alaIpInterfaceMtu Unsigned32,
        alaIpInterfaceLocalProxyArp INTEGER,
        alaIpInterfacePrimCfg INTEGER,
        alaIpInterfaceOperState INTEGER,
        alaIpInterfaceOperReason INTEGER,
        alaIpInterfaceRouterMac MacAddress,
        alaIpInterfaceBcastAddr IpAddress,
        alaIpInterfacePrimAct INTEGER,
        alaIpInterfaceRemoteAddr IpAddress,
        alaIpInterfaceTunnelSrcAddressType InetAddressType,
        alaIpInterfaceTunnelSrc InetAddress,
        alaIpInterfaceTunnelDstAddressType InetAddressType,
		  alaIpInterfaceTunnelDst InetAddress,
		  alaIpInterfaceVipAddress IpAddress,
		  alaIpInterfaceDhcpStatus INTEGER,
		  alaIpInterfaceDhcpIpRelease INTEGER,
		  alaIpInterfaceDhcpIpRenew INTEGER,
		  alaIpInterfaceDhcpOption60String SnmpAdminString,
		  alaIpInterfaceChassisId  Integer32,
		  alaIpInterfaceSlot  Integer32,
		  alaIpInterfacePort  Integer32,
		  alaIpInterfaceTag   INTEGER,
		  alaIpInterfaceArpCount Counter32,
		  alaIpInterfaceArpNiChassis Integer32,
		  alaIpInterfaceArpNiSlot Integer32,
		  alaIpInterfaceArpNiDevice Integer32,
		  alaIpInterfacePortIfindex InterfaceIndexOrZero,
		  alaIpInterfaceDhcpVsiAcceptFilterString SnmpAdminString,
		  alaIpInterfaceServiceID	Unsigned32
}

alaIpInterfaceName  OBJECT-TYPE
        SYNTAX  SnmpAdminString (SIZE (1..32))
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "IP interface name."
        ::= { alaIpInterfaceEntry 1 }

alaIpInterfaceAddress OBJECT-TYPE
        SYNTAX  IpAddress
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "IP address for this interface."
        DEFVAL { '00000000'h }
        ::= { alaIpInterfaceEntry 2 }

alaIpInterfaceMask OBJECT-TYPE
        SYNTAX  IpAddress
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "IP mask for this IP interface."
        DEFVAL { '00000000'h }
        ::= { alaIpInterfaceEntry 3 }

alaIpInterfaceAdminState OBJECT-TYPE
        SYNTAX INTEGER {
                enable(1),
                disable(2)
        }
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
                "Administrative state of the IP interface, independent of the
                 state of the underlying device."
        DEFVAL { enable }
        ::= { alaIpInterfaceEntry 4 }

alaIpInterfaceDeviceType  OBJECT-TYPE
        SYNTAX INTEGER {
                unbound (0),
                vlan (1),
                emp (2),
                loopback (3),
                greTunnel (4),
                ipipTunnel (5),
                service (6)
        }
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
                "The type of device to which the IP interface is bound."
        DEFVAL { unbound }
        ::= { alaIpInterfaceEntry 5}

alaIpInterfaceVlanID  OBJECT-TYPE
        SYNTAX Integer32 (0..4094)
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "The VLAN to which the interface is bound; 0 if unbound."
        DEFVAL { 0 }
        ::= { alaIpInterfaceEntry 6}

alaIpInterfaceIpForward OBJECT-TYPE
        SYNTAX INTEGER {
                enable(1),
                disable(2)
        }
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
                "IP interface forwarding status(disabled = ip host only)."
        DEFVAL { enable }
        ::= { alaIpInterfaceEntry 7 }

alaIpInterfaceEncap OBJECT-TYPE
        SYNTAX INTEGER {
                ethernet2(1),
                snap(2),
                service(3)
        }
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
                "IP router port encapsulation."
        DEFVAL { ethernet2 }
        ::= { alaIpInterfaceEntry 8}

alaIpInterfaceMtu OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
                "IP interface's maximum transmission unit (MTU)."
        DEFVAL { 0 }
        ::= { alaIpInterfaceEntry 9}

alaIpInterfaceLocalProxyArp OBJECT-TYPE
        SYNTAX INTEGER {
                enable(1),
                disable(2)
        }
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
                "Local Proxy ARP status on this interface."
        DEFVAL { enable }
        ::= { alaIpInterfaceEntry 10 }

alaIpInterfacePrimCfg OBJECT-TYPE
        SYNTAX INTEGER {
                false(0),
                true(1)
        }
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
                "Primary interface configuration status for
                 this interface.  If set to true, this interface
                 will be used as the default interface for the
                 VLAN on which it resides when possible."
        DEFVAL { false }
        ::= { alaIpInterfaceEntry 11 }

alaIpInterfaceOperState OBJECT-TYPE
        SYNTAX INTEGER {
                up(1),
                down(2)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Operational state of the IP interface."
        ::= { alaIpInterfaceEntry 12 }

alaIpInterfaceOperReason OBJECT-TYPE
        SYNTAX INTEGER {
                interfaceUp(0),
                adminDown(1),
                unbound(2),
                deviceDown(3),
                noSuchDevice(4),
                noRouterMac(5),
                tunnelSrcInvalid(6),
                tunnelDstUnreachable(7),
                noVipAddress(8)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The reason the operational state is down."
        ::= { alaIpInterfaceEntry 13 }

alaIpInterfaceRouterMac OBJECT-TYPE
        SYNTAX MacAddress
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The router MAC for this IP interface."
        ::= { alaIpInterfaceEntry 14 }

alaIpInterfaceBcastAddr OBJECT-TYPE
        SYNTAX  IpAddress
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Broadcast address for this IP interface."
        ::= { alaIpInterfaceEntry 15 }

alaIpInterfacePrimAct OBJECT-TYPE
        SYNTAX INTEGER {
                false(0),
                true(1)
        }
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "Actual primary interface status for this interface."
        ::= { alaIpInterfaceEntry 16 }

alaIpInterfaceRemoteAddr OBJECT-TYPE
        SYNTAX  IpAddress
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "Peer address for this IP interface."
        ::= { alaIpInterfaceEntry 17 }

alaIpInterfaceTunnelSrcAddressType OBJECT-TYPE
        SYNTAX  InetAddressType
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "The address type of the tunnel source network address.
                 Only type InetAddressIPv4 (1) is supported."
        ::= { alaIpInterfaceEntry 18 }

alaIpInterfaceTunnelSrc OBJECT-TYPE
        SYNTAX  InetAddress
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "Source network address for tunnel."
        ::= { alaIpInterfaceEntry 19 }

alaIpInterfaceTunnelDstAddressType OBJECT-TYPE
        SYNTAX  InetAddressType
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "The address type of the tunnel destination network address.
                 Only type InetAddressIPv4 (1) is supported."
        ::= { alaIpInterfaceEntry 20 }

alaIpInterfaceTunnelDst OBJECT-TYPE
        SYNTAX  InetAddress
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                "Destination network address for tunnel."
        ::= { alaIpInterfaceEntry 21 }

alaIpInterfaceVipAddress OBJECT-TYPE
        SYNTAX  IpAddress
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Virtual IP address for this interface when part of a
                 multi-chass configuration.
                 This object is deprecated and can no longer be set."
        DEFVAL { '00000000'h }
        ::= { alaIpInterfaceEntry 22 }

alaIpInterfaceDhcpStatus OBJECT-TYPE
        SYNTAX  INTEGER {
                discovery (1),
                active (2),
                timeout (3)
        }
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
                "DHCP status of the DHCP Client IP interface"
        DEFVAL { discovery }
        ::= { alaIpInterfaceEntry 23 }

alaIpInterfaceDhcpIpRelease OBJECT-TYPE
        SYNTAX  INTEGER {
                enabled (1),
                disabled(2)
        }
        MAX-ACCESS read-write
        STATUS      current
        DESCRIPTION
                "To do an IP address release if dynamically assigned address"
        DEFVAL { disabled }
        ::= { alaIpInterfaceEntry 24 }

alaIpInterfaceDhcpIpRenew OBJECT-TYPE
        SYNTAX  INTEGER {
                enabled (1),
                disabled(2)
        }
        MAX-ACCESS read-write
        STATUS      current
        DESCRIPTION
                "To do an IP address renew if dynamically assigned address"
        DEFVAL { disabled }
        ::= { alaIpInterfaceEntry 25 }

alaIpInterfaceDhcpOption60String OBJECT-TYPE
        SYNTAX      SnmpAdminString (SIZE (0..63))
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
               "The value of option-60 filed that should be inserted in
               DHCP discover/request packet if configured."
        ::= { alaIpInterfaceEntry 26 }

alaIpInterfaceChassisId OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      obsolete
        DESCRIPTION
               "ChassisId associated with the physical port bound 
                directly with the IP Interface. An Ip interface not bound
                directly with a physical port will have alaIpInterfaceChassisId
                equal to zero.
                This object is deprecated in favor of
                ipAlaIpInterfacePortIfindex."
        ::= { alaIpInterfaceEntry 27 }

alaIpInterfaceSlot OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      obsolete
        DESCRIPTION
               "Slot associated with the physical port bound directly 
                with the IP Interface. An Ip interface not bound directly 
                with a physical port will have alaIpInterfaceSlot equal to zero.
                This object is deprecated in favor of
                ipAlaIpInterfacePortIfindex."
        ::= { alaIpInterfaceEntry 28 }

alaIpInterfacePort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      obsolete
        DESCRIPTION
               "Port associated with the physical port bound directly with the 
                IP Interface. An Ip interface not bound directly with a physical 
                port will have alaIpInterfacePort equal to zero.
                This object is deprecated in favor of
                ipAlaIpInterfacePortIfindex."
        ::= { alaIpInterfaceEntry 29 }

alaIpInterfaceTag OBJECT-TYPE
        SYNTAX  INTEGER {
                unbound(0),
                untagged(1),
                tagged(2)
                }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
               "If a physical-port or linkagg is directly bound to the IP Interface, then
                alaIpInterfaceTag indicates whether it is tagged or untagged. 1 for untagged,
                and 2 for tagged. If neither a linkagg nor a physical port is bound directly,
                alaIpInterfaceTag will be equal to 0"
        ::= { alaIpInterfaceEntry 30 }

alaIpInterfaceArpCount OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
               "The number of ARPs associated with an interface."              
        ::= { alaIpInterfaceEntry 31 }

alaIpInterfaceArpNiChassis OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
               "The distributed ARP designated NI chassis ID." 
        ::= { alaIpInterfaceEntry 32 }

alaIpInterfaceArpNiSlot OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
               "The distributed ARP designated NI slot."             
        ::= { alaIpInterfaceEntry 33 }

alaIpInterfaceArpNiDevice OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
               "The distributed ARP designated NI device." 
        ::= { alaIpInterfaceEntry 34 }

alaIpInterfacePortIfindex OBJECT-TYPE
        SYNTAX      InterfaceIndexOrZero
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
               "Specifies the physical port bound directly with the IP
                Interface.  An IP interface not bound directly with a physical
                port will have alaIpInterfacePortIfindex equal to zero.  Note
                this object replaces alaIpInterfaceChassisId, alaIpInterfaceSlot
                and alaIpInterfacePort."
        ::= { alaIpInterfaceEntry 35 }

alaIpInterfaceDhcpVsiAcceptFilterString OBJECT-TYPE
        SYNTAX      SnmpAdminString (SIZE (0..63))
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
               "The string value to prefer an OV-Server.
               On the basis of this value DHCP-Ack will be accepted."
        ::= { alaIpInterfaceEntry 36 }

alaIpInterfaceServiceID  OBJECT-TYPE
      SYNTAX Unsigned32(0..32767)
      MAX-ACCESS  read-write
      STATUS  current
      DESCRIPTION
             "The service to which the L3 VPN interface is bound; 0 if unbound."
      DEFVAL { 0 }
      ::= { alaIpInterfaceEntry 37}

alaIpItfConfigTable OBJECT-TYPE
        SYNTAX SEQUENCE OF AlaIpItfConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                "Table used to configure a new IP interface with an IP address."
        ::= { alcatelIND1IPMIBObjects 15  }

alaIpItfConfigEntry OBJECT-TYPE
        SYNTAX  AlaIpItfConfigEntry
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                "An IP interface entry."
        INDEX { alaIpItfConfigName }
        ::= { alaIpItfConfigTable 1 }

AlaIpItfConfigEntry ::= SEQUENCE {
        alaIpItfConfigName SnmpAdminString,
        alaIpItfConfigIfIndex InterfaceIndexOrZero,
        alaIpItfConfigRowStatus RowStatus
}

alaIpItfConfigName OBJECT-TYPE
        SYNTAX  SnmpAdminString (SIZE (1..32))
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "The name for this interface."
        ::= { alaIpItfConfigEntry 1 }

alaIpItfConfigIfIndex OBJECT-TYPE
        SYNTAX  InterfaceIndexOrZero
        MAX-ACCESS  read-create
        STATUS  current
        DESCRIPTION
                "The ifIndex for this interface.  It is only a valid
                 ifIndex value when the value for alaIpInterfaceRowStatus
                 is active."
        ::= { alaIpItfConfigEntry 2 }

alaIpItfConfigRowStatus OBJECT-TYPE
        SYNTAX RowStatus
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
                "This object is used to create a new IP interface or
                 delete an existing interface.  It should be set
                 to createAndGo to create an entry, or destroy to delete
                 an existing entry.
                 A valid ifIndex value for an IP interface is allocated
                 when a row is created in this table.  The successful
                 allocation of the ifIndex value is noted by the state
                 of the new row becoming active."
        ::= { alaIpItfConfigEntry 3 }


--    FTP configuration MIB

alaIpFtpConfig    OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 16 }

--
--    FTP configuration group
--

alaIpFtpAdminStatus OBJECT-TYPE
	SYNTAX  INTEGER {
		enabled(1),
		disabled(2) }
	MAX-ACCESS  read-write
	STATUS  current
	DESCRIPTION
		 "Indicates whether the FTP service is enabled on the switch."
	DEFVAL { enabled }
	::= { alaIpFtpConfig 1 }

alaIpFtpPort OBJECT-TYPE
        SYNTAX  Integer32 (21 | 1024..65535)
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                 "The user configurable TCP port for switch's
                 ftp access. Default is 21."
        DEFVAL { 21 }
        ::= { alaIpFtpConfig 2 }


--    SSH configuration MIB

alaIpSshConfig    OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 17 }

--
--    SSH configuration group
--

alaIpSshAdminStatus OBJECT-TYPE
	SYNTAX  INTEGER {
		enabled(1),
		disabled(2) }
	MAX-ACCESS  read-write
	STATUS  current
	DESCRIPTION
		 "Indicates whether the SSH service is enabled on the switch."
	DEFVAL { enabled }
	::= { alaIpSshConfig 1 }

alaIpSshPort OBJECT-TYPE
        SYNTAX  Integer32 (22 | 1024..65535)
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                 "The user configurable TCP port for switch's
                 ssh access. Default is 22."
        DEFVAL { 22 }
        ::= { alaIpSshConfig 2 }

alaIpSshPubKeyEnforceAdminStatus OBJECT-TYPE
	SYNTAX  INTEGER {
		enabled(1),
		disabled(2) }
	MAX-ACCESS  read-write
	STATUS  current
	DESCRIPTION
		 "Indicates whether the Public Key Authentication is enforced
		  on the switch."
	DEFVAL { disabled }
	::= { alaIpSshConfig 3 }

alaIpSshStrongCiphersAdminStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        enabled(1),
        disabled(2) }
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "Indicates whether the ssh Strong-Ciphers are enforced
        on the switch."
    DEFVAL { disabled }
    ::= { alaIpSshConfig 4 }
    
alaIpSshStrongHmacsAdminStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        enabled(1),
        disabled(2) }
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "Indicates whether the ssh Strong-Hmacs are enforced
        on the switch."
    DEFVAL { disabled }
    ::= { alaIpSshConfig 5 }


--    TELNET configuration MIB

alaIpTelnetConfig    OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 18 }

--
--    TELNET configuration group
--

alaIpTelnetAdminStatus OBJECT-TYPE
	SYNTAX  INTEGER {
		enabled(1),
		disabled(2) }
	MAX-ACCESS  read-write
	STATUS  current
	DESCRIPTION
		 "Indicates whether the TELNET service is enabled on the switch."
	DEFVAL { enabled }
	::= { alaIpTelnetConfig 1 }

alaIpTelnetPort OBJECT-TYPE
        SYNTAX  Integer32 (23 | 1024..65535)
        MAX-ACCESS  read-write
        STATUS  current
        DESCRIPTION
                 "The user configurable TCP port for switch's
                 telnet access. Default is 23."
        DEFVAL { 23 }
        ::= { alaIpTelnetConfig 2 }


alaIpDhcpHostIdentifierObjects OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 19 }

alaIpDhcpServerAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "DHCP Server IP address Type. Currently ipv4(1) is supported."
    ::= { alaIpDhcpHostIdentifierObjects 1 }

alaIpDhcpServerAddress OBJECT-TYPE
    SYNTAX     InetAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "DHCP Server IP address."
    ::= { alaIpDhcpHostIdentifierObjects 2 }

alaIpDhcpRouterAddressType OBJECT-TYPE
    SYNTAX     InetAddressType
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "DHCP Router IP address Type. Currently ipv4(1) is supported."
    ::= { alaIpDhcpHostIdentifierObjects 3 }

alaIpDhcpRouterAddress OBJECT-TYPE
    SYNTAX     InetAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "DHCP Router IP address."
    ::= { alaIpDhcpHostIdentifierObjects 4 }

alaIpDhcpHostName OBJECT-TYPE
    SYNTAX     SnmpAdminString (SIZE (1..255))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "DHCP Server assigned host name."
    ::= { alaIpDhcpHostIdentifierObjects 5 }

alaIpDhcpClientLeaseObtained OBJECT-TYPE
    SYNTAX    TimeStamp
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
 "DHCP IP address lease obtained time. If no DHCP client exists
             this object contains a zero value"
    ::= { alaIpDhcpHostIdentifierObjects 6 }

alaIpDhcpClientLeaseExpires OBJECT-TYPE
    SYNTAX    TimeStamp
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
            "DHCP IP address lease expiry time.If no DHCP client exists
             this object contains a zero value"
    ::= { alaIpDhcpHostIdentifierObjects 7 }


--    NTP configuration MIB

alaIpNtpConfig    OBJECT IDENTIFIER ::= { alcatelIND1IPMIBObjects 20 }

--
--    NTP configuration group
--

alaIpNtpVrfName OBJECT-TYPE
    SYNTAX        SnmpAdminString ( SIZE( 0 .. 20 ) )
    MAX-ACCESS    read-write
    STATUS        current
    DESCRIPTION
        "Name of the VRF used by the NTP daemon."
	DEFVAL { "default" }
	::= { alaIpNtpConfig 1 }

--
-- proprietary alcatel ipNetToMediaDpaTable, only DPA entries
--

alaIpNetToMediaDpaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIpNetToMediaDpaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "A table of dynamic proxy ARPs for the router IPs"
    ::= { alcatelIND1IPMIBObjects 24 }

alaIpNetToMediaDpaEntry OBJECT-TYPE
    SYNTAX      AlaIpNetToMediaDpaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "An entry in alaIpToMediaDpaTable."
    INDEX  { alaIpNetToMediaDpaVlan}
    ::= { alaIpNetToMediaDpaTable 1 }

AlaIpNetToMediaDpaEntry ::= SEQUENCE {
        alaIpNetToMediaDpaVlan          Unsigned32,
        alaIpNetToMediaDpaPhysAddress   PhysAddress,
        alaIpNetToMediaDpaIpType        InetAddressType,
        alaIpNetToMediaDpaIp            InetAddress,
        alaIpNetToMediaDpaIfIndex       InterfaceIndexOrZero
        }

alaIpNetToMediaDpaVlan OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "Vlan to which the router IP belongs to."
    ::= { alaIpNetToMediaDpaEntry 1  }

alaIpNetToMediaDpaPhysAddress OBJECT-TYPE
    SYNTAX      PhysAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The media-dependent `physical' address of the head end router."
    ::= { alaIpNetToMediaDpaEntry 2 }

alaIpNetToMediaDpaIpType OBJECT-TYPE
    SYNTAX      InetAddressType(1)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Router IP address type(IPV4) of the VLAN."
    ::= { alaIpNetToMediaDpaEntry 3  }

alaIpNetToMediaDpaIp OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Router IP address of the VLAN"
    ::= { alaIpNetToMediaDpaEntry 4  }

alaIpNetToMediaDpaIfIndex OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The IfIndex subindex identifying this instance.It is the
            the interface where the RouterIp was learnt"
    ::= { alaIpNetToMediaDpaEntry 5  }
--
-- conformance information
--
alcatelIND1IPMIBConformance OBJECT IDENTIFIER ::= { alcatelIND1IPMIB 2 }
alcatelIND1IPMIBCompliances OBJECT IDENTIFIER ::= { alcatelIND1IPMIBConformance 1 }
alcatelIND1IPMIBGroups OBJECT IDENTIFIER ::= { alcatelIND1IPMIBConformance 2 }

alaIpCompliance MODULE-COMPLIANCE
        STATUS     current
    DESCRIPTION
            "The compliance statement for switches with Alcatel IP stack and
            implementing ALCATEL-IND1-IP-MIB."
    MODULE
    MANDATORY-GROUPS { alaIpConfigGroup,
                       alaIpNetToMediaGroup,
                       alaDoSGroup,
                       alaPortScanGroup,
                       alaArpPingGroup,
                       alaArpPoisonGroup,
                       alaIpNetToMediaAugGroup,
                       alaPktDropGroup,
                       alaIpCidrAugGroup,
                       alaIcmpCtrlGroup,
                       alaIpRouteSumGroup,
                       alaIpArpFilterGroup,
                       alaIpServiceTypeGroup,
                       alaIpPortServiceTypeGroup,
                       alaIpInterfaceGroup,
                       alaIpItfGroup,
                       alaIpNotificationGroup,
                       alaIpDhcpHostIdentifierGroup,
                       alaIpServiceSourceIpGroup,
                       alaIpNetToMediaDpGroup,
                       alaAntiSpoofGlobalGroup,
                       alaAntiSpoofGroup
                     }
    ::= { alcatelIND1IPMIBCompliances 1 }
--
-- units of conformance
--
alaIpConfigGroup OBJECT-GROUP
    OBJECTS  { alaIpClearArpCache, alaIpDirectedBroadcast,
               alaIpClearArpFilter, alaIpDistributedArp }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel IP stack."
    ::= { alcatelIND1IPMIBGroups 1 }

alaIpNetToMediaGroup OBJECT-GROUP
    OBJECTS  { alaIpNetToMediaPhysAddress,
               alaIpNetToMediaProxy,
               alaIpNetToMediaVrrp,
               alaIpNetToMediaAuth,
               alaIpNetToMediaName,
               alaIpNetToMediaStaticIntfRt
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel IP Net to Media stack."
    ::= { alcatelIND1IPMIBGroups 2 }

alaDoSGroup OBJECT-GROUP
    OBJECTS  { alaDoSType,
               alaDoSDetected,
               alaDoSIp,
               alaDoSMac,
               alaDoSSlot,
               alaDoSPort,
               alaDoSStatus,
               alaDoSChassisId,
               alaDoSPortIfindex
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support Notification for DoS attack."
    ::= { alcatelIND1IPMIBGroups 3 }

alaPortScanGroup OBJECT-GROUP
    OBJECTS  { alaDoSPortScanClosePortPenalty,
               alaDoSPortScanTcpOpenPortPenalty,
               alaDoSPortScanUdpOpenPortPenalty,
               alaDoSPortScanTotalPenalty,
               alaDoSPortScanThreshold,
               alaDoSPortScanDecay,
               alaDoSTrapCntl
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support Notification for Port Scan DoS attack."
    ::= { alcatelIND1IPMIBGroups 4 }


alaArpPingGroup OBJECT-GROUP
    OBJECTS  { alaDoSARPRate,
               alaDoSPingRate
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support Notification for Arp or Ping DoS attack."
    ::= { alcatelIND1IPMIBGroups 5 }

alaArpPoisonGroup OBJECT-GROUP
    OBJECTS  { alaDoSArpPoisonDetected,
               alaDoSArpPoisonRowStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support DoS Arp Poison Table."
    ::= { alcatelIND1IPMIBGroups 6 }

alaIpNetToMediaAugGroup OBJECT-GROUP
    OBJECTS  { ipNetToMediaSlot,
               ipNetToMediaPort,
               ipNetToMediaName,
               ipNetToMediaChassisId,
               ipNetToMediaPortIfindex,
               ipNetToMediaSubId,
               ipNetToMediaSubType
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel IP Net to Media Augmented stack."
    ::= { alcatelIND1IPMIBGroups 7 }

alaPktDropGroup OBJECT-GROUP
    OBJECTS  { pktDropType,
               pktDropIfIndex,
               pktDropCount,
               pktDropFrag

             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support Packet Drop Notification objects."
    ::= { alcatelIND1IPMIBGroups 8 }


alaIpCidrAugGroup OBJECT-GROUP
    OBJECTS  { ipCidrRouteScope
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel IP Cidr Augmented stack."
    ::= { alcatelIND1IPMIBGroups 9 }

alaIcmpCtrlGroup OBJECT-GROUP
    OBJECTS  { alaIcmpCtrlType,
               alaIcmpCtrlCode,
               alaIcmpCtrlStatus,
               alaIcmpCtrlPktGap,
               alaIcmpAllMsgStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel Icmp Ctrl stack."
    ::= { alcatelIND1IPMIBGroups 10 }

alaIpRouteSumGroup OBJECT-GROUP
    OBJECTS  { alaIpRouteProtocol,
               alaIpRouteCount
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel Ip Route Sum stack."
    ::= { alcatelIND1IPMIBGroups 11 }

alaIpArpFilterGroup OBJECT-GROUP
    OBJECTS  { alaIpArpFilterMode,
               alaIpArpFilterRowStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel Ip Arp Filter stack."
    ::= { alcatelIND1IPMIBGroups 12 }

alaIpServiceTypeGroup OBJECT-GROUP
    OBJECTS  { alaIpServiceType,
               alaIpServicePort,
               alaIpServiceStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel Ip Service Type  stack."
    ::= { alcatelIND1IPMIBGroups 13 }

alaIpPortServiceTypeGroup OBJECT-GROUP
    OBJECTS  { alaIpPortServicePort,
               alaIpPortServiceStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel Ip Port Service Type  stack."
    ::= { alcatelIND1IPMIBGroups 14 }

alaIpInterfaceGroup OBJECT-GROUP
    OBJECTS  { alaIpInterfaceName,
               alaIpInterfaceAddress,
               alaIpInterfaceMask,
               alaIpInterfaceAdminState,
               alaIpInterfaceDeviceType,
               alaIpInterfaceVlanID,
               alaIpInterfaceIpForward,
               alaIpInterfaceEncap,
               alaIpInterfaceMtu,
               alaIpInterfaceLocalProxyArp,
               alaIpInterfacePrimCfg,
					alaIpInterfaceOperState,
					alaIpInterfaceOperReason,
					alaIpInterfaceRouterMac,
					alaIpInterfaceBcastAddr,
					alaIpInterfacePrimAct,
					alaIpInterfaceRemoteAddr,
					alaIpInterfaceTunnelSrcAddressType,
					alaIpInterfaceTunnelSrc,
					alaIpInterfaceTunnelDstAddressType,
					alaIpInterfaceTunnelDst,
					alaIpInterfaceVipAddress,
					alaIpInterfaceDhcpStatus,
					alaIpInterfaceDhcpIpRelease,
					alaIpInterfaceDhcpIpRenew,
					alaIpInterfaceDhcpOption60String,
					alaIpInterfaceChassisId,
					alaIpInterfaceSlot,
					alaIpInterfacePort,
					alaIpInterfaceTag,
					alaIpInterfaceArpCount,
					alaIpInterfaceArpNiChassis,
					alaIpInterfaceArpNiSlot,
					alaIpInterfaceArpNiDevice,
					alaIpInterfacePortIfindex,
					alaIpInterfaceDhcpVsiAcceptFilterString,
					alaIpInterfaceServiceID
	 }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel IP Interface stack."
    ::= { alcatelIND1IPMIBGroups 15 }

alaIpItfGroup OBJECT-GROUP
    OBJECTS  { alaIpItfConfigName,
               alaIpItfConfigIfIndex,
               alaIpItfConfigRowStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel Ip Itf stack."
    ::= { alcatelIND1IPMIBGroups 16 }

alaIpFtpGroup OBJECT-GROUP
    OBJECTS  { alaIpFtpAdminStatus,
               alaIpFtpPort
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of ftp configuration."
    ::= { alcatelIND1IPMIBGroups 17 }

alaIpSshGroup OBJECT-GROUP
    OBJECTS  { alaIpSshAdminStatus,
               alaIpSshPort,
               alaIpSshPubKeyEnforceAdminStatus,
               alaIpSshStrongCiphersAdminStatus,
               alaIpSshStrongHmacsAdminStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of ssh configuration."
    ::= { alcatelIND1IPMIBGroups 18 }

alaIpTelnetGroup OBJECT-GROUP
    OBJECTS  { alaIpTelnetAdminStatus,
               alaIpTelnetPort
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of telnet configuration."
    ::= { alcatelIND1IPMIBGroups 19 }

alaIpNotificationGroup NOTIFICATION-GROUP
   NOTIFICATIONS
   {
        alaDoSTrap,
        pktDrop,
        alaDistArpItfChange,
        alaDistArpNiThreshold

   }
   STATUS  current
   DESCRIPTION
        "Collection of notifications for IP MIB."
   ::= { alcatelIND1IPMIBGroups 20 }


alaIpDhcpHostIdentifierGroup OBJECT-GROUP
   OBJECTS  { alaIpDhcpServerAddressType,
	      alaIpDhcpServerAddress,
              alaIpDhcpRouterAddressType,
	      alaIpDhcpRouterAddress,
              alaIpDhcpHostName,
              alaIpDhcpClientLeaseObtained,
              alaIpDhcpClientLeaseExpires }
   STATUS     current
   DESCRIPTION
            "A collection of objects to support display of
            DHCP Client details."
    ::= { alcatelIND1IPMIBGroups 21 }

alaIpNtpGroup OBJECT-GROUP
    OBJECTS  { alaIpNtpVrfName
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of ntp configuration."
    ::= { alcatelIND1IPMIBGroups 22 }

alaIpNetToMediaDpGroup OBJECT-GROUP
    OBJECTS  { alaIpNetToMediaDpaPhysAddress,
               alaIpNetToMediaDpaIpType,
               alaIpNetToMediaDpaIp,
               alaIpNetToMediaDpaIfIndex }
    STATUS     current
    DESCRIPTION
             "A collection of objects to support display of
              ARPs learnt for dynamic router IPs."
    ::= { alcatelIND1IPMIBGroups 26 }

alaAntiSpoofGlobalGroup OBJECT-GROUP
    OBJECTS  { alaDoSAntiSpoofGlobalCountReset
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of anti-spoof global level."
    ::= { alcatelIND1IPMIBGroups 27 }

alaAntiSpoofGroup OBJECT-GROUP
    OBJECTS  { alaDoSAntiSpoofStatus,
               alaDoSAntiSpoofMacAddress,
               alaDoSAntiSpoofIfIndex,
               alaDoSAntiSpoofVlan,
               alaDoSAntiSpoofCount,
               alaDoSAntiSpoofCountReset,
               alaDoSAntiSpoofRowStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of anti-spoof"
    ::= { alcatelIND1IPMIBGroups 28 }

--
-- proprietary Alcatel ipDistArpNiTable. Includes the Slot stats
-- for distributed ARP designated NIs.
--
alaDistArpNiTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaDistArpNiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of distributed ARP slots."
    ::= { alcatelIND1IPMIBObjects 21 }

alaDistArpNiEntry OBJECT-TYPE
    SYNTAX      AlaDistArpNiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in alaDistArpNiTable."
    INDEX  { alaDistArpNiChassis, 
            alaDistArpNiSlot,
             alaDistArpNiDevice }
    ::= { alaDistArpNiTable 1 }

AlaDistArpNiEntry ::= SEQUENCE {
        alaDistArpNiChassis               Unsigned32,
        alaDistArpNiSlot                  Unsigned32,
        alaDistArpNiDevice                Unsigned32,
        alaDistArpNiCount                 Counter32,
        alaDistArpNiMaxCount              Counter32
        }

alaDistArpNiChassis OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Chassis number of the NI."
    ::= { alaDistArpNiEntry 1 }

alaDistArpNiSlot OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Slot number of the NI."
    ::= { alaDistArpNiEntry 2 }

alaDistArpNiDevice OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Device number of the NI."
    ::= { alaDistArpNiEntry 3 }

alaDistArpNiCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Count of the active hardware ARPs in hardware on the NI."
    ::= { alaDistArpNiEntry 4 }

alaDistArpNiMaxCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Count of the maximum number of hardware ARPs on the NI."
    ::= { alaDistArpNiEntry 5 }

alaDistArpNiGroup OBJECT-GROUP
    OBJECTS  { alaDistArpNiChassis,
               alaDistArpNiSlot,
               alaDistArpNiDevice,
               alaDistArpNiMaxCount,
               alaDistArpNiCount
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of Distributed ARP NIs"
    ::= { alcatelIND1IPMIBGroups 23 }

alaIpServiceSourceIpGroup OBJECT-GROUP
    OBJECTS  { alaIpServiceSourceIpName,
               alaIpServiceSourceIpRowStatus
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of configuration
            parameters of Alcatel Source Ip addresses"
    ::= { alcatelIND1IPMIBGroups 24 }

--
--    IP Service Source Ip Table
--

alaIpServiceSourceIpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIpServiceSourceIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of service source IP interfaces for various applications"
    ::= { alcatelIND1IPMIBObjects 22 }

alaIpServiceSourceIpEntry OBJECT-TYPE
    SYNTAX      AlaIpServiceSourceIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in alaIpServiceSourceIpTable."
    INDEX  { alaIpServiceSourceIpAppIndex }
    ::= { alaIpServiceSourceIpTable  1 }

AlaIpServiceSourceIpEntry ::= SEQUENCE {
        alaIpServiceSourceIpAppIndex    AlaIpServiceSourceIpAppIndex,
        alaIpServiceSourceIpName        SnmpAdminString,
        alaIpServiceSourceIpRowStatus   RowStatus
    }

alaIpServiceSourceIpAppIndex  OBJECT-TYPE
    SYNTAX      AlaIpServiceSourceIpAppIndex
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Service Source IP index for a specified application.
         When setting values for the all(1) row, additional
         application rows are affected as follows:
         create - Set alaIpServiceSourceIpRowStatus to createAndGo(4).
                  Creates a matching row for each application
                  which does not have an instance.
         modify - Set alaIpServiceSourceIpName for active row.
                  Modifies all application rows for which
                  alaIpServiceSourceIpName matches the all(1)
                  row being modified, creates a row for each
                  application which does not have an instance.
         delete - Set alaIpServiceSourceIpRowStatus to destroy(6).
                  Deletes the all(1) row and any other application
                  rows for which alaIpServiceSourceIpName matches
                  the all(1) row."

    ::= { alaIpServiceSourceIpEntry 1 }

alaIpServiceSourceIpName    OBJECT-TYPE
    SYNTAX      SnmpAdminString(SIZE(1..32))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Service Source IP interface name for the specified application"
    ::= { alaIpServiceSourceIpEntry 2 }

alaIpServiceSourceIpRowStatus   OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Row Status for the specified entry"
    ::= { alaIpServiceSourceIpEntry 3 }

--
-- proprietary Alcatel ipDistArpNiTable. Includes the Slot stats
-- for distributed ARP designated NIs.
--
alaDistArpItfTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaDistArpItfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of distributed ARP slots."
    ::= { alcatelIND1IPMIBObjects 23 }

alaDistArpItfEntry OBJECT-TYPE
    SYNTAX      AlaDistArpItfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in alaDistArpItfTable."
    INDEX  { alaDistArpNiChassis, 
             alaDistArpNiSlot,
             alaDistArpNiDevice,
             alaDistArpItfIfIndex}
    ::= { alaDistArpItfTable 1 }

AlaDistArpItfEntry ::= SEQUENCE {
        alaDistArpItfIfIndex               InterfaceIndexOrZero,
        alaDistArpItfCount                 Counter32
        }

alaDistArpItfIfIndex    OBJECT-TYPE
    SYNTAX  InterfaceIndexOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP ifindex of the interface for the ARP stats"
    ::= { alaDistArpItfEntry 1 }

alaDistArpItfCount OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Count of the active hardware ARPs on the NI for the interface."
    ::= { alaDistArpItfEntry 2 }

alaDistArpItfGroup OBJECT-GROUP
    OBJECTS  { alaDistArpItfIfIndex,
               alaDistArpItfCount
             }
    STATUS     current
    DESCRIPTION
            "A collection of objects to support management of Distributed ARP Interfacess"
    ::= { alcatelIND1IPMIBGroups 25 }

alaDistArpItfChange NOTIFICATION-TYPE
     OBJECTS      { alaDistArpItfIfIndex,
                        alaDistArpNiChassis,
                        alaDistArpNiSlot,
                        alaDistArpNiDevice
                  }
     STATUS       current
     DESCRIPTION
         "The interface has been reasigned to a new designated NI."
     ::= { alcatelIND1IPMIBNotifications 3 }

alaDistArpNiThreshold NOTIFICATION-TYPE
     OBJECTS      { alaDistArpNiChassis,
                        alaDistArpNiSlot,
                        alaDistArpNiDevice
                  }
     STATUS       current
     DESCRIPTION
         "The number of ARPs in hardware has reached the 
         reassignment threshold." 
     ::= { alcatelIND1IPMIBNotifications 4 }

END


