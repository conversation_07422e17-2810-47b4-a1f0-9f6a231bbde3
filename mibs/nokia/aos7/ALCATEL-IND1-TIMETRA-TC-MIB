ALCATEL-IND1-TIMETRA-TC-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY,
        Unsigned32, Integer32           FROM SNMPv2-SMI

        TEXTUAL-CONVENTION              FROM SNMPv2-TC

        timetraModules                  FROM ALCATEL-IND1-TIMETRA-GLOBAL-MIB
        ;

timetraTCMIBModule   MODULE-IDENTITY
        LAST-UPDATED    "0801010000Z"
        ORGANIZATION    "Alcatel"
        CONTACT-INFO    
            "Alcatel 7x50 Support
             Web: http://www.alcatel.com/comps/pages/carrier_support.jhtml"
        DESCRIPTION
        "This document is the SNMP MIB module for the SNMP Textual
        Conventions (TCs) used in the Alcatel 7x50 manageability
        instrumentation.

        Copyright 2003-2008 Alcatel-Lucent. All rights reserved.
        Reproduction of this document is authorized on the condition
        that the foregoing copyright notice is included.

        This SNMP MIB module (Specification) embodies Alcatel's
        proprietary intellectual property.  Alcatel retains
        all title and ownership in the Specification, including any
        revisions.

        Alcatel grants all interested parties a non-exclusive
        license to use and distribute an unmodified copy of this
        Specification in connection with management of Alcatel
        products, and without fee, provided this copyright notice and
        license appear on all copies.

        This Specification is supplied `as is', and Alcatel
        makes no warranty, either express or implied, as to the use,
        operation, condition, or performance of the Specification."

--
--  Revision History
--
        REVISION        "0801010000Z"
        DESCRIPTION     "Rev 6.0                01 Jan 2008 00:00
                         6.0 release of the TIMETRA-TC-MIB."

        REVISION        "0701010000Z"
        DESCRIPTION     "Rev 5.0                01 Jan 2007 00:00
                         5.0 release of the TIMETRA-TC-MIB."

        REVISION        "0603230000Z"   
        DESCRIPTION     "Rev 4.0                23 Mar 2006 00:00
                         4.0 release of the TIMETRA-TC-MIB."

        REVISION        "0508310000Z"   
        DESCRIPTION     "Rev 3.0                31 Aug 2005 00:00
                         3.0 release of the TIMETRA-TC-MIB."

        REVISION        "0501240000Z"   
        DESCRIPTION     "Rev 2.1                24 Jan 2005 00:00
                         2.1 release of the TIMETRA-TC-MIB."

        REVISION        "0401150000Z"
        DESCRIPTION     "Rev 2.0                15 Jan 2004 00:00
                         2.0 release of the TIMETRA-TC-MIB."

        REVISION        "0308150000Z"
        DESCRIPTION     "Rev 1.2                15 Aug 2003 00:00
                         1.2 release of the TIMETRA-TC-MIB."

        REVISION        "0301200000Z"
        DESCRIPTION     "Rev 1.0                20 Jan 2003 00:00
                         1.0 Release of the TIMETRA-TC-MIB."

        REVISION        "0105290000Z"
        DESCRIPTION     "Rev 0.1                14 Aug 2000 00:00
                         First version of the TIMETRA-TC-MIB."

        ::= { timetraModules 2 }

-- InterfaceIndex TC is defined here to work about problems with
-- importing from IF-MIB, rfc1573.mib

   -- InterfaceIndex contains the semantics of ifIndex and
   -- should be used for any objects defined on other mib
   -- modules that need these semantics.

InterfaceIndex ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "d"
    STATUS       current
    DESCRIPTION
       "A unique value, greater than zero, for each interface
        or interface sub-layer in the managed system.  It is
        recommended that values are assigned contiguously
        starting from 1.  The value for each interface sub-
        layer must remain constant at least from one re-
        initialization of the entity's network management
        system to the next re-initialization."
    SYNTAX  Integer32

TmnxPortID ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "A portid is an unique 32 bit number encoded as shown below.
            
            32 30 | 29 26 | 25 22 | 21 16 | 15  1 |
            +-----+-------+-------+-------+-------+
            |000  |  slot |  mda  | port  |  zero | Physical Port
            +-----+-------+-------+-------+-------+

            32 30 | 29 26 | 25 22 | 21 16 | 15  1 |
            +-----+-------+-------+-------+-------+
            |001  |  slot |  mda  | port  |channel| Channel
            +-----+-------+-------+-------+-------+

         Slots, mdas (if present), ports, and channels are numbered
         starting with 1.

            32     29 | 28             10 | 9   1 |
            +---------+-------------------+-------+
            | 0 1 0 0 |   zeros           |   ID  | Virtual Port
            +---------+-------------------+-------+

            32     29 | 28                9 | 8 1 |
            +---------+---------------------+-----+
            | 0 1 0 1 |   zeros             | ID  | LAG Port
            +---------+---------------------+-----+

        A card port number (cpn) has significance within the context
        of the card on which it resides(ie., cpn 2 may exist in one or
        more cards in the chassis).  Whereas, portid is an
        unique/absolute port number (apn) within a given chassis.
        
        An 'invalid portid' is a TmnxPortID with a value of 0x1e000000 as 
        represented below.
        
            32 30 | 29 26 | 25 22 | 21 16 | 15  1 |
            +-----+-------+-------+-------+-------+
            |zero | ones  | zero  |  zero |  zero | Invalid Port
            +-----+-------+-------+-------+-------+"
        
    SYNTAX  Unsigned32

TmnxEncapVal ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
       "The value of the label used to identify the entity using the
       specified encapsulation value on a specific port.

       The format of this object depends on the encapsulation type
       defined on this port.

       When the encapsulation is nullEncap the value of this object
       must be zero.

       31                                   0
       +--------+--------+--------+--------+
       |00000000 00000000 00000000 00000000|
       +--------+--------+--------+--------+

       When the encapsulation is dot1qEncap the value of this object
       is equal to the 12-bit IEEE 802.1Q VLAN ID.

       31                                   0
       +--------+--------+--------+--------+
       |00000000 00000000 0000XXXX XXXXXXXX|
       +--------+--------+--------+--------+

       When the encapsulation is mplsEncap the value of this object
       is equal to the 20-bit LSP ID.

       31                                   0
       +--------+--------+--------+--------+
       |00000000 0000XXXX XXXXXXXX XXXXXXXX|
       +--------+--------+--------+--------+

       When the encapsulation is frEncap, the value of this object is
       equal to the 10-bit Frame Relay DLCI.

       31                                   0
       +--------+--------+--------+--------+
       |00000000 00000000 000000XX XXXXXXXX|
       +--------+--------+--------+--------+

       When the encapsulation is qinqEncap, the value of the outer
       802.1Q VLAN ID is encoded in the least significant 16 bits,
       and the value of the inner VLAN ID is encoded in the most
       significant 16 bits.

       31                                   0
       +--------+--------+--------+--------+
       |0000YYYY YYYYYYYY 0000XXXX XXXXXXXX|
       +--------+--------+--------+--------+

       When the encapsulation is atmEncap, the value
       of the ATM VCI is encoded in the least significant 
       16 bits, and the value of the ATM VPI is encoded 
       in the next 12 bits.

       For ATM VCs, the top 2 bits are 00.  The value of
       the ATM VCI is encoded in the least significant 16 
       bits, and the value of the ATM VPI is encoded in the next
       12 bits.
                     
       31                                   0
       +--------+--------+--------+--------+
       |0000YYYY YYYYYYYY XXXXXXXX XXXXXXXX|
       +--------+--------+--------+--------+   

       For ATM VPs, the top 2 bits are 01.  The value of
       the ATM VPI is encoded in the least significant 12 bits.

       31                                   0
       +--------+--------+--------+--------+
       |01000000 00000000 0000XXXX XXXXXXXX|
       +--------+--------+--------+--------+          

       For ATM VP ranges, the top 2 bits are 10.  The value of
       the start of the ATM VPI range is encoded in the least significant 
       12 bits, and the value of the end of the ATM VP range is encoded
       in the next 12 bits.

       31                                   0
       +--------+--------+--------+--------+
       |10000000 YYYYYYYY YYYYXXXX XXXXXXXX|
       +--------+--------+--------+--------+         

       For ATM ports, the top 2 bits are 11, and the rest of the bits 
       must be zero.  

       31                                   0
       +--------+--------+--------+--------+
       |11000000 00000000 00000000 00000000|
       +--------+--------+--------+--------+         

       When the encapsulation is wanMirrorEncap the value of this 
       object is equal to the 12-bit value.
                     
       31                                   0
       +--------+--------+--------+--------+
       |00000000 00000000 0000XXXX XXXXXXXX|
       +--------+--------+--------+--------+      

       Some ports have a restrictions to the encapsulation types that
       they can support and hence impose restrictions on the respective
       formats defined above."       
    SYNTAX  Unsigned32

QTag ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The QTag data type is a 12-bit integer tag used to identify
        a service.  The values 0 and 4095 are not allowed."
    SYNTAX  INTEGER (1..4094)

QTagOrZero ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type QTagOrZero represents a VLAN tag. 
        
         The value '0' indicates that no VLAN tag is provisioned, or that its value
         is unknown."
    SYNTAX  Unsigned32 (0..4094)

TmnxStrSapId ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
       "The value of TmnxStrSapId is a printable string which
        contains the owner SAP Id or equivalent on a remote system.

        The string should contain the printable string equivalent of the
        textual-conventions TmnxPortID and TmnxEncapVal in the format
        specified as TmnxPortID[:TmnxEncapVal]"
    SYNTAX OCTET STRING (SIZE (0..32))

IpAddressPrefixLength ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "the number of bits to match in an IP address mask."
    REFERENCE ""
    SYNTAX INTEGER (0..32)

--
--      TmnxActionType
--
TmnxActionType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxActionType data type is an enumerated integer
        that describes the values used to support action or
        operation style commands.  Setting a variable of this
        type to 'doAction' causes the action to occur.  GETs and
        GETNEXTs on this variable return 'not-applicable'."
    SYNTAX  INTEGER {
                doAction (1),
                notApplicable (2)
            }

TmnxAdminState ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxAdminState data type is an enumerated integer that describes
        the values used to identify the administratively desired state of
        functional modules."
    SYNTAX  INTEGER {
                noop (1),
                inService (2),
                outOfService (3)
                }

TmnxOperState ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxOperState data type is an enumerated integer that describes
        the values used to identify the current operational state of functional
        modules."
    SYNTAX  INTEGER {
                unknown (1),
                inService (2),
                outOfService (3),
                transition (4)
                }

TmnxStatus ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxStatus data type is an enumerated integer that describes the
        values used to identify the current status of functional modules in the
        system such as OSPF and MPLS protocols. Setting this variable to
        'create' causes instantiation of the feature in the system.  Setting it
        to 'delete' removes the instance and all associated configuration
        information."
    SYNTAX  INTEGER {
                create (1),
                delete (2)
                }

TmnxEnabledDisabled ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The TmnxEnabledDisabled data type is an enumerated integer that
         describes the values used to identify whether an entity is
         'enabled' or 'disabled'."
    SYNTAX  INTEGER {
                enabled  (1),
                disabled (2)
                }

TNamedItem ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS       current
    DESCRIPTION
        "The name of an item.  When used as an index to a table, the item
         name uniquely identifies the instance.  When used in a reference
         (TNamedItemOrEmpty) the item name entry must exist in the table.

         Note, use only NVT ASCII displayable characters
         here, no control characters, no UTF-8, etc."
    SYNTAX OCTET STRING (SIZE (1..32))

TNamedItemOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS       current
    DESCRIPTION
        "The name of an item, or an empty string.  When used in a reference
         (TNamedItemOrEmpty) the item name entry must exist in the table.

         Note, use only NVT ASCII displayable characters
         here, no control characters, no UTF-8, etc."
    SYNTAX OCTET STRING (SIZE (0 | 1..32))

TItemDescription ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS       current
    DESCRIPTION
        "Description for an item.  Note, use only NVT ASCII displayable characters
        here, no control characters, no UTF-8, etc."
    SYNTAX OCTET STRING (SIZE(0..80))

TItemLongDescription ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS       current
    DESCRIPTION
        "Longer description for an item.  Note, use only NVT ASCII displayable
        characters here, no control characters, no UTF-8, etc."
    SYNTAX OCTET STRING (SIZE(0..160))

TmnxVRtrID ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "A number used to identify a virtual router instance in the system."
    SYNTAX       Integer32 (1..4096)

TmnxVRtrIDOrZero ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "A number used to identify a virtual router instance in the system.
         The number 0 will have special significance in the context the TC 
         is used."
    SYNTAX       Integer32 (0..4096)

TmnxBgpAutonomousSystem ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION  "an autonomous system (AS) number."
    REFERENCE    "BGP4-MIB.bgpPeerRemoteAs"
    SYNTAX       INTEGER (0..65535)

TmnxBgpLocalPreference ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION  "a local route preference value."
    REFERENCE    "RFC 1771 section 4.3 Path Attributes e)"
    SYNTAX       Unsigned32

TmnxBgpPreference ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION  "a route preference value."
    REFERENCE    "RFC 1771 section 4.3 Path Attributes e)"
    SYNTAX       Unsigned32 (0..255)

TmnxCustId ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     "A number used to identify a Customer or
                     Subscriber. This ID must be unique within
                     the Service Domain. The value 0 is used as
                     the null ID."
    SYNTAX          Unsigned32 (0|1..2147483647)

TmnxServId ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     "A number used to identify a Service. This ID
                     must be unique within the Service Domain.
                     The value 0 is used as the null ID."
    SYNTAX          Unsigned32 (0|1..2147483647)

ServiceAdminStatus ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION  "ServiceAdminStatus data type is an enumerated integer that
                  describes the values used to identify the administrative
                  state of a service."
    REFERENCE    ""
    SYNTAX       INTEGER {
                     up(1),
                     down(2)
                 }

ServiceOperStatus ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION  "ServiceOperStatus data type is an enumerated integer that
                  describes the values used to identify the current operational
                  state of a service."
    REFERENCE    ""
    SYNTAX       INTEGER {
                     up(1),
                     down(2)
                 }

TPolicyID ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The identification number of a policy."
    SYNTAX Unsigned32 (0..65535)

TSapIngressPolicyID ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The identification number of a SAP ingress policy."
    SYNTAX Unsigned32 (0..65535)

TSapEgressPolicyID ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The identification number of a SAP egress policy."
    SYNTAX Unsigned32 (1..65535)

TPolicyStatementNameOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS       current
    DESCRIPTION
        "The name of a policy statement, when an object refers to it."
    SYNTAX OCTET STRING (SIZE (0 | 1..32))

TmnxVcType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION     "The value of TmnxVcType is an enumerated integer that
                     indicates a Virtual Circuit (VC) type. 'frDlciMartini(1)'
                     replaces the old 'frDlci' when used over martini tunnels."
    SYNTAX      INTEGER {
                        frDlciMartini(1),
                        atmSdu(2),
                        atmCell(3),
                        ethernetVlan(4),
                        ethernet(5),
                        atmVccCell(9),
                        atmVpcCell(10),
                        ipipe(11),
                        satopE1(17),
                        satopT1(18),
                        satopE3(19),
                        satopT3(20),
                        cesopsn(21),
                        cesopsnCas(23),
                        frDlci(25),
                        mirrorDest(4096)
                    }

TmnxVcId ::=  TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     "A 32 bit number is used to identify a VC(Virtual Circuit).
                     The VC ID cannot be 0."
    SYNTAX          Unsigned32 (1..4294967295)

TmnxVcIdOrNone ::=  TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     "A 32 bit number is used to identify a VC(Virtual Circuit).
                     A value of 0 indicates no VC ID is configured or 
                     available."
    SYNTAX          Unsigned32 (0|1..4294967295)


-- generic TEXTUAL-CONVENTIONs for common technology (IEEE, IETF)


Dot1PPriority ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "IEEE 802.1p priority.  zero is lowest, seven is highest.
         -1 means not set"
    REFERENCE ""
    SYNTAX      INTEGER (-1 | 0..7)

ServiceAccessPoint ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "802.2 LLC SAP value, Source and Destination."
    REFERENCE
        "assigned numbers:  http://www.iana.org/assignments/ieee-802-numbers"
    SYNTAX      INTEGER (-1 | 0..255)

TLspExpValue ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION
        "MPLS Experimental bits. -1 means not set."
    REFERENCE ""
    SYNTAX      INTEGER (-1 | 0..7)

TIpProtocol ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "IP protocol number.  well known protocol numbers include ICMP(1),
         TCP(6), UDP(17).

         -1 means value not set.
         -2 indicates protocol wildcard for UDP and TCP."
    REFERENCE "http://www.iana.org/assignments/protocol-numbers"
    SYNTAX          INTEGER (-2 | -1 | 0..255)

TIpOption ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "IP packet options octet.  explanation of the octet bits:

     IP OPTION NUMBERS

     The Internet Protocol (IP) has provision for optional header fields
     identified by an option type field.  Options 0 and 1 are exactly one
     octet which is their type field.  All other options have their one
     octet type field, followed by a one octet length field, followed by
     length-2 octets of option data.  The option type field is sub-divided
     into a one bit copied flag, a two bit class field, and a five bit
     option number.  These taken together form an eight bit value for the
     option type field.  IP options are commonly refered to by this value.


     Copy Class Number Value Name                Reference
     ---- ----- ------ ----- ------------------------------- ---------
        0     0      0     0 EOOL   - End of Options List    [RFC791,JBP]
        0     0      1     1 NOP    - No Operation           [RFC791,JBP]
        1     0      2   130 SEC    - Security                  [RFC1108]
        1     0      3   131 LSR    - Loose Source Route     [RFC791,JBP]
        0     2      4    68 TS     - Time Stamp             [RFC791,JBP]
        1     0      5   133 E-SEC  - Extended Security         [RFC1108]
        1     0      6   134 CIPSO  - Commercial Security           [???]
        0     0      7     7 RR     - Record Route           [RFC791,JBP]
        1     0      8   136 SID    - Stream ID              [RFC791,JBP]
        1     0      9   137 SSR    - Strict Source Route    [RFC791,JBP]
        0     0     10    10 ZSU    - Experimental Measurement      [ZSu]
        0     0     11    11 MTUP   - MTU Probe                 [RFC1191]*
        0     0     12    12 MTUR   - MTU Reply                 [RFC1191]*
        1     2     13   205 FINN   - Experimental Flow Control    [Finn]
        1     0     14   142 VISA   - Expermental Access Control [Estrin]
        0     0     15    15 ENCODE - ???                      [VerSteeg]
        1     0     16   144 IMITD  - IMI Traffic Descriptor        [Lee]
        1     0     17   145 EIP    - Extended Internet Protocol[RFC1385]
        0     2     18    82 TR     - Traceroute        [RFC1393]
        1     0     19   147 ADDEXT - Address Extension    [Ullmann IPv7]
        1     0     20   148 RTRALT - Router Alert              [RFC2113]
        1     0     21   149 SDB    - Selective Directed Broadcast[Graff]
        1     0     22   150 NSAPA  - NSAP Addresses          [Carpenter]
        1     0     23   151 DPS    - Dynamic Packet State        [Malis]
        1     0     24   152 UMP    - Upstream Multicast Pkt. [Farinacci]

     [Note, an asterisk (*) denotes an obsoleted IP Option Number.]
        "
    REFERENCE "http://www.iana.org/assignments/ip-parameters"
    SYNTAX          INTEGER (0..255)

TTcpUdpPort ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The number of a TCP or UDP port.
         Well known port numbers include
         ftp-data(20), ftp(21), telnet(23), smtp(25), http(80),
         pop3(110), nntp(119), snmp(161), snmptrap(162), etc."
    REFERENCE "http://www.iana.org/assignments/port-numbers"
    SYNTAX INTEGER (0 | 1..65535)

TTcpUdpPortOperator ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
       "The operator specifies the manner in which a couple of other
        MIB objects in the table are supposed to be used.

        Operator        Value1               Value2
        ----------------------------------------------------
        none(0)         Any(0)               Any(0)
        eq(1)           Specified Value      Any(0)
        range(2)        Starting Value       Ending Value
        lt(3)           Specified Value      Any(0)
        gt(4)           Specified Value      Any(0)

        'Any(0)' specifies that, this object can accept any values
         but would default to 0. "
    SYNTAX       INTEGER {
                    none(0),
                    eq(1),
                    range(2),
                    lt(3),
                    gt(4)
                 }

TFrameType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
       "The type of the frame for which this mac filter match criteria is
        defined."
    SYNTAX       INTEGER {
                    e802dot3(0),
                    e802dot2LLC(1),
                    e802dot2SNAP(2),
                    ethernetII(3)
                 }

TQueueId ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The value of TQueueId specifies the identification number of a
         queue.  A value of zero (0) indicates that no specific queue 
         identification has been assigned for this object. When an object
         of type TQueueId is an SNMP table index, an index value of zero 
         (0) is not allowed and a noCreation error will be returned."
    SYNTAX INTEGER (0 | 1..32)

TIngressQueueId ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The value of TIngressQueueId specifies the identification number 
         of an ingress queue.  A value of zero (0) indicates that no 
         specific queue identification has been assigned for this object. 
         When an object of type TIngressQueueId is an SNMP table index,
         an index value of zero (0) is not allowed and a noCreation error
         will be returned."
    SYNTAX INTEGER (0 | 1..32)

TEgressQueueId ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The value of TEgressQueueId specifies the identification number 
         of an egress queue.  A value of zero (0) indicates that no 
         specific queue identification has been assigned for this object. 
         When an object of type TEgressQueueId is an SNMP table index,
         an index value of zero (0) is not allowed and a noCreation error
         will be returned."
    SYNTAX INTEGER (0 | 1..8)

--
-- TEXTUAL-CONVENTIONs for Alcatel 7x50 SR series common items
--

TDSCPName ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS       current
    DESCRIPTION
        "The name of a Differential Services Code Point value."
    SYNTAX OCTET STRING (SIZE (1..32))

TDSCPNameOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS       current
    DESCRIPTION
        "The name of a Differential Services Code Point value."
    SYNTAX OCTET STRING (SIZE (0 | 1..32))

TDSCPValue ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The value of a Differential Services Code Point."
    SYNTAX INTEGER (0..63)

TDSCPValueOrNone ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The value of a Differential Services Code Point (DSCP). A value
         of -1 means that no DSCP value is configured or available."
    SYNTAX INTEGER (-1 | 0..63)

TDSCPFilterActionValue ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The value of a Differential Services Code Point. -1 means not set."
    SYNTAX INTEGER (-1 | 0..255)

TFCName ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS       current
    DESCRIPTION
        "The name of a Forwarding Class entry."
    SYNTAX OCTET STRING (SIZE (1..32))

TFCNameOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT    "255a"
    STATUS       current
    DESCRIPTION
        "The name of a Forwarding Class entry."
    SYNTAX OCTET STRING (SIZE (0 | 1..32))

TFCSet ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This data type describes a set of Forwarding Classes."
    SYNTAX      BITS {
                       be (0),
                       l2 (1),
                       af (2),
                       l1 (3),
                       h2 (4),
                       ef (5),
                       h1 (6),
                       nc (7)
                }

TFCType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This data type enumerates the Forwarding Classes."
    SYNTAX       INTEGER {
                       be (0),
                       l2 (1),
                       af (2),
                       l1 (3),
                       h2 (4),
                       ef (5),
                       h1 (6),
                       nc (7)
                }

TmnxTunnelType ::= TEXTUAL-CONVENTION
    STATUS   current
    DESCRIPTION
       "The type of this tunnel entity."
    SYNTAX   INTEGER {
                 sdp (1),
                 ldp (2),
                 rsvp (3),
                 gre (4),
                 bypass (5),
                 invalid (6)
                 }

TmnxTunnelID ::= TEXTUAL-CONVENTION
    STATUS   current
    DESCRIPTION
       "The identifying value for a BGP-VPRN tunnel.  Depending on the
        tunnel type the associated tunnel-id may be an sdp-id, an lsp-id
        or zero(0)."
    SYNTAX   Unsigned32

TmnxBgpRouteTarget ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION
        "TmnxBgpRouteTarget is an readable string that specifies the
         extended community name to be accepted by a Route Reflector Server
         or advertised by the router when reflecting any routes. I.e, it
         does not apply to routes that are not reflected by the router."
    SYNTAX     OCTET STRING (SIZE(1..32))

TmnxVPNRouteDistinguisher ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The VPRN route distinguisher is a 8-octet object. It contains a
         2-octet type field followed by a 6-octet value field. The type
         field specify how to interpret the value field.

         Type 0 specifies two subfields as a 2-octet administrative field
         and a 4-octet assigned number subfield.

         Type 1 specifies two subfields as a 4-octet administrative field
         which must contain an IP address and a 2-octet assigned number
         subfield.

         Type 2 specifies two subfields as a 4-octet administrative field
         which contains a 4-octet AS number and a 2-octet assigned number
         subfield."
    SYNTAX      OCTET STRING (SIZE (8))

SdpBindId ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     "The value used to uniquely identify an SDP Binding.
                     The first four octets correspond to the zero-extended
                     16-bit SDP ID, while the remaining four octets
                     correspond to the 32-bit VC ID, both encoded in network
                     byte order."
    SYNTAX          OCTET STRING (SIZE (8))

TmnxVRtrMplsLspID ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     "A unique value, greater than zero, for each Label
                     Switched Path in the managed system."
    SYNTAX          Unsigned32 (0..65535)

TPortSchedulerPIR ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The Peak Information Rate (PIR) rate to be used in kbps.
         The value -1 means maximum rate."
    SYNTAX      Integer32 (-1 | 1..40000000)

TPortSchedulerCIR ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The Committed Information Rate (CIR) rate to be used in kbps.
         The value -1 means maximum rate."
    SYNTAX      Integer32 (-1 | 0..40000000)

TWeight ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The weight of the specified entity while feeding into the parent."
    SYNTAX       Integer32 (0..100)

TCIRRate ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The CIR rate to be used in kbps. The value -1 means maximum rate."
    SYNTAX       Integer32 (-1 | 0..*********)

TPIRRate ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The PIR rate to be used in kbps. The value -1 means maximum rate."
    SYNTAX      Integer32 (-1 | 1..*********)

TSecondaryShaper10GPIRRate ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The secondary shaper PIR rate to be used in Mbps.
         The value -1 means maximum rate."
    SYNTAX      Integer32 (-1 | 1..10000)

TPIRRateOverride ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The PIR rate to be used in kbps. The value -1 means maximum rate.
         A value of -2 specifies no override."
    SYNTAX      Integer32 (-2 | -1 | 1..*********)

TPIRRateOrZero ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The PIR rate to be used in kbps. The value -1 means maximum rate.
         The value 0 means undefined rate."
    SYNTAX      Integer32 (-1 | 0..*********)

TmnxDHCP6MsgType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The DHCP6 messagetype."
    SYNTAX INTEGER {
               dhcp6MsgTypeSolicit      (1),
               dhcp6MsgTypeAdvertise    (2),
               dhcp6MsgTypeRequest      (3),
               dhcp6MsgTypeConfirm      (4),
               dhcp6MsgTypeRenew        (5),
               dhcp6MsgTypeRebind       (6),
               dhcp6MsgTypeReply        (7),
               dhcp6MsgTypeRelease      (8),
               dhcp6MsgTypeDecline      (9),
               dhcp6MsgTypeReconfigure  (10),
               dhcp6MsgTypeInfoRequest  (11),
               dhcp6MsgTypeRelayForw    (12),
               dhcp6MsgTypeRelayReply   (13),
               dhcp6MsgTypeMaxValue     (14)
           }

TmnxOspfInstance ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     "A number used to identify an instance of OSPF." 
    SYNTAX          Unsigned32 (0..31)

TmnxBGPFamilyType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The value of TmnxBGPFamilyType specifies the AFI-SAFI family for
         BGP peer."
    SYNTAX      BITS {
                    ipv4Unicast (0),
                    ipv4Multicast (1),
                    ipv4UastMcast (2),
                    ipv4MplsLabel (3),
                    ipv4Vpn (4),
                    ipv6Unicast (5),
                    ipv6Multicast (6),
                    ipv6UcastMcast (7),
                    ipv6MplsLabel (8),
                    ipv6Vpn (9)
                }

TmnxIgmpGroupFilterMode ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxIgmpGroupFilterMode describes the filter-mode of
         a group.

         In 'include(1)' mode, reception of packets sent to the specified
         multicast address is requested only from those IPv4 Source addresses
         listed in the corresponding source-list.

         In 'exclude(2)' mode, reception of packets sent to the given multicast
         address is requested from all IPv4 Source addresses, except those
         listed in the corresponding source-list (if any)."
    SYNTAX INTEGER {
                include (1),
                exclude (2)
            }

TmnxIgmpGroupType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxIgmpGroupType describes how a multicast group is
         learned."
    SYNTAX  INTEGER {
                static(1),
                dynamic(2)
            }

TmnxIgmpVersion ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxIgmpVersion denotes the version of the IGMP protocol:
         - 'version1(1)': means version 1 of the IGMP protocol
         - 'version2(2)': means version 2 of the IGMP protocol
         - 'version3(3)': means version 3 of the IGMP protocol."
    SYNTAX  INTEGER {
                version1(1),
                version2(2),
                version3(3)
            }

TmnxMldGroupFilterMode ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxMldGroupFilterMode describes the filter-mode of a
         group.

         In 'include(1)' mode, reception of packets sent to the specified
         multicast address is requested only from those IPv6 source addresses
         listed in the corresponding source-list.

         In 'exclude(2)' mode, reception of packets sent to the given multicast
         address is requested from all IPv6 source addresses, except those
         listed in the corresponding source-list (if any)."
    SYNTAX INTEGER {
                include (1),
                exclude (2)
            }

TmnxMldGroupType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxMldGroupType describes how a multicast group is
         learned."
    SYNTAX  INTEGER {
                static(1),
                dynamic(2)
            }

TmnxMldVersion ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxMldVersion denotes the version of the MLD protocol:
         - 'version1(1)': means version 1 of the MLD protocol
         - 'version2(2)': means version 2 of the MLD protocol"
    SYNTAX  INTEGER {
                version1(1),
                version2(2)
            }

TmnxManagedRouteStatus ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxManagedRouteStatus denotes the status of a Managed Route."
    SYNTAX
           INTEGER {
                installed           (0),
                notYetInstalled     (1),
                wrongAntiSpoofType  (2),
                outOfMemory         (3),
                shadowed            (4),
                routeTableFull      (5),
                parentInterfaceDown (6)
           }

-- 
-- Textual conventions for GSMP
--
TmnxAncpString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The TmnxAncpString data type contains a valid ancp string."
    SYNTAX OCTET STRING (SIZE (1..63))

TmnxAncpStringOrZero ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The TmnxAncpStringOrZero data type contains a valid ancp string.
        An empty string indicates that no ANCP string is defined."
    SYNTAX OCTET STRING (SIZE (0..63))

-- 
-- Textual conventions for Multicast Address Family
--

TmnxMulticastAddrFamily ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The data type TmnxMulticastAddrFamily denotes the family for
         multicast protocol."
    SYNTAX      INTEGER {
                    ipv4Multicast (0),
                    ipv6Multicast (1)
                }
--
-- Textual conventions for subscriber mgmt
-- 

TmnxSubIdentString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSubIdentString denotes the subscriber
         identification string."
    SYNTAX OCTET STRING (SIZE (1..32))

TmnxSubIdentStringOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSubIdentStringOrEmpty denotes the subscriber
         identification string. The empty string denotes the absence of a
         subscriber identification string."
    SYNTAX OCTET STRING (SIZE (0..32))

TmnxSubProfileString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSubProfileString denotes the subscriber
         profile string."
    SYNTAX OCTET STRING (SIZE (1..16))

TmnxSubProfileStringOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSubProfileStringOrEmpty denotes the subscriber
         profile string. The empty string denotes the absence of a
         subscriber profile."
    SYNTAX OCTET STRING (SIZE (0..16))

TmnxSlaProfileString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSlaProfileString denotes the SLA 
         profile string."
    SYNTAX OCTET STRING (SIZE (1..16))

TmnxSlaProfileStringOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSlaProfileStringOrEmpty denotes the SLA
         profile string. The empty string denotes the absence of a
         SLA profile."
    SYNTAX OCTET STRING (SIZE (0..16))

TmnxAppProfileString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxAppProfileString denotes the application
         profile string."
    SYNTAX OCTET STRING (SIZE (1..16))

TmnxAppProfileStringOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxAppProfileStringOrEmpty denotes the application
         profile string. The empty string denotes the absence of a
         application profile."
    SYNTAX OCTET STRING (SIZE (0..16))

TmnxSubMgtIntDestIdOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSubMgtIntDestIdOrEmpty denotes the intermediate
         destination id. The empty string denotes the absence of an
         intermediate destination id."
    SYNTAX OCTET STRING (SIZE (0..32))

TmnxSubMgtIntDestId ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxSubMgtIntDestId denotes the intermediate 
         destination id."
    SYNTAX OCTET STRING (SIZE (1..32))

--
-- Textual conventions for DHCP server
--
TmnxDhcpOptionType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The data type TmnxDhcpOptionType represents how the value
         of this option is encoded:
         - 'ipv4 (1)': this option contains an IPv4 address (4 octets)
         - 'ascii(2)': this option contains seven-bit ASCII characters
         - 'hex  (3)': this option contains octets. It must be displayed in
                       hexadecimal format because it contains non-printable
                       characters."
    SYNTAX  INTEGER {
                ipv4(1),
                ascii(2),
                hex(3)
            }

TmnxDhcpVendorOption ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This value specifies what is encoded in the Alcatel vendor specific
         sub-option of option 82."
    SYNTAX  BITS {
        systemId  (0),
        clientMac (1),
        serviceId (2),
        sapId     (3)
    }

TmnxPppoeUserNameOrEmpty ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS  current
    DESCRIPTION
        "The data type TmnxPppoeUserNameOrEmpty denotes the PPPoE username."
    SYNTAX OCTET STRING (SIZE (0..128))

--
-- Textual conventions for Security
--
TCpmProtPolicyID ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The data type TCpmProtPolicyID represents the identification number 
         of a CPM Protection policy.
        
         The value '0' indicates that no CPM Protection policy is provisioned."
    SYNTAX Unsigned32 (0..255)

--
-- Textual conventions for MLPPP QoS
--
TMlpppQoSProfileId ::=  TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This textual-convention uniquely identifies MLPPP Bundle QoS
        profile in the ingress and egress MLPPP QoS profile tables. 
        The value 0 indicates default MLPPP QoS Profile as applicable to
        a given H/W"
    SYNTAX Unsigned32 (0..65535)

END
