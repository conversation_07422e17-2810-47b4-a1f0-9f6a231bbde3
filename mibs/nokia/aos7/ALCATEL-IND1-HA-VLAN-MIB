ALCATEL-IND1-HA-VLAN-MIB DEFINITIONS ::= BEGIN

IMPORTS
        OBJECT-TYPE,
        OBJECT-IDENTITY,
        MODULE-IDENTITY,
        NOTIFICATION-TYPE,
        Integer32                       FROM SNMPv2-<PERSON><PERSON>
        ifIndex,                       
        InterfaceIndex                 FROM IF-<PERSON><PERSON>
        MacAddress,
        RowStatus,
        TEXTUAL-CONVENTION              FROM SNMPv2-TC
        InetAddressType,
        InetAddress                     FROM INET-ADDRESS-MIB 
        SnmpAdminString                 FROM SNMP-FRAMEWORK-MIB
        MODULE-COMPLIANCE,
        OBJECT-GRO<PERSON>,
        NOTIFICATION-GROUP              FROM SNMPv2-CO<PERSON>
        MultiChassisId                  FROM ALCATEL-IND1-MULTI-CHASSIS-MI<PERSON>
        softentIND1HAVlan               FROM ALCATEL-IND1-BASE;


alcatelIND1HAVlanMIB MODULE-IDENTITY
    LAST-UPDATED "201005130000Z"
    ORGANIZATION "Alcatel-Lucent, Enterprise Solutions Division"
    CONTACT-INFO
     "Please consult with Customer Service to ensure the most appropriate
      version of this document is used with the products in question:

                 Alcatel-Lucent, Enterprise Solutions Division
                (Formerly Alcatel Internetworking, Incorporated)
                        26801 West Agoura Road
                     Agoura Hills, CA  91301-5122
                       United States Of America

     Telephone:               North America  ****** 995 2696
                              Latin America  ****** 919 9526
                              Europe         +31 23 556 0100
                              Asia           +65 394 7933
                              All Other      ****** 878 4507

     Electronic Mail:         <EMAIL>
     World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
     File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"
    DESCRIPTION
              "This module describes an authoritative enterprise-specific Simple
        Network Management Protocol (SNMP) Management Information Base (MIB):

        For the Birds Of Prey Product Line, this is the MIB module for
              address learning mac addresses entity.

        The right to make changes in specification and other information
        contained in this document without prior notice is reserved.

        No liability shall be assumed for any incidental, indirect, special, or
        consequential damages whatsoever arising from or related to this
        document or the information contained herein.

        Vendors, end-users, and other interested parties are granted
        non-exclusive license to use this specification in connection with
        management of the products for which it is intended to be used.

                   Copyright (C) 1995-2007 Alcatel-Lucent
                       ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "201005130000Z"
    DESCRIPTION
        "Fixed the Notifications to use MIB Module OID.0 as Notifications root."

    REVISION      "200704030000Z"

    DESCRIPTION
        "The MIB module for High Availability Vlan entity."
    ::= { softentIND1HAVlan 1}


-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- Hook into the Alcatel Tree
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

    alcatelIND1HAVlanMIBNotifications OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For High Availability Vlan Module MIB Subsystem Notifications."
        ::= { alcatelIND1HAVlanMIB 0 }

    alcatelIND1HAVlanMIBObjects OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch For High Availability Vlan Module MIB Subsystem Managed Objects."
        ::= { alcatelIND1HAVlanMIB 1 }

    alcatelIND1HAVlanMIBConformance OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch for High Availability Vlan Module MIB Subsystem Conformance Information."
        ::= { alcatelIND1HAVlanMIB 2 }

    alcatelIND1HAVlanMIBGroups OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch for High Availability Vlan Module MIB Subsystem Units of Conformance."
        ::= { alcatelIND1HAVlanMIBConformance 1 }

    alcatelIND1HAVlanMIBCompliances OBJECT-IDENTITY
        STATUS  current
        DESCRIPTION
            "Branch for High Availability Vlan Module MIB Subsystem Compliance Statements."
        ::= { alcatelIND1HAVlanMIBConformance 2 }

--
-- High Availability Vlan Common Definitions
--

-- HA Vlan Cluster Mib Table

   alaHAVlanCluster OBJECT IDENTIFIER ::= { alcatelIND1HAVlanMIBObjects 1 }

    alaHAVlanClusterTable  OBJECT-TYPE
	    SYNTAX  SEQUENCE OF AlaHAVlanClusterEntry
	    MAX-ACCESS  not-accessible
	    STATUS  current
	    DESCRIPTION
		 "A list of HA VLAN clusters in the system."
	    ::= { alaHAVlanCluster 1 }

    alaHAVlanClusterEntry  OBJECT-TYPE
	    SYNTAX  AlaHAVlanClusterEntry
	    MAX-ACCESS  not-accessible
	    STATUS  current
	    DESCRIPTION
		 "A HA VLAN Cluster entry."
	    INDEX { alaHAVlanClusterId }
	    ::= { alaHAVlanClusterTable 1 }

    AlaHAVlanClusterEntry ::= 
        SEQUENCE {
		alaHAVlanClusterId
			Integer32,
	    	alaHAVlanClusterName
			SnmpAdminString,
                alaHAVlanClusterAdminStatus
                        INTEGER,
                alaHAVlanClusterOperStatus
                        INTEGER,
                alaHAVlanClusterOperStatusFlag
                        INTEGER,
		alaHAVlanClusterMode
			INTEGER,
                alaHAVlanClusterVlan
			Integer32,
                alaHAVlanClusterMacAddressType
                        INTEGER,
	    	alaHAVlanClusterMacAddress
			MacAddress,
                alaHAVlanClusterInetAddressType
			InetAddressType,
                alaHAVlanClusterInetAddress
                        InetAddress,
                alaHAVlanClusterMulticastStatus
                        INTEGER,	    	
                alaHAVlanClusterMulticastInetAddressType
			InetAddressType,
	    	alaHAVlanClusterMulticastInetAddress
			InetAddress,
		alaHAVlanClusterRowStatus
			RowStatus,
            alaHAVlanClusterMcmStatus
                INTEGER,
            alaHAVlanClusterMcmStatusFlag
                INTEGER,
            alaHAVlanClusterVflStatus
                INTEGER,
            alaHAVlanClusterLoopback
                INTEGER
	}


    alaHAVlanClusterId  OBJECT-TYPE
	    SYNTAX  Integer32 (1..32)
	    MAX-ACCESS  accessible-for-notify
	    STATUS  current
	    DESCRIPTION
		 "The number identifying a cluster."
	    ::= { alaHAVlanClusterEntry 1 }

    alaHAVlanClusterName  OBJECT-TYPE
	    SYNTAX   SnmpAdminString (SIZE (0..32))
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "Textual description of the cluster."
	    ::= { alaHAVlanClusterEntry 2 }

    alaHAVlanClusterAdminStatus  OBJECT-TYPE
	    SYNTAX   INTEGER
			  {
				enable(1),
				disable(2)
			  }
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "Admin control to enable/disable a cluster"
             DEFVAL      { enable }

	    ::= { alaHAVlanClusterEntry 3 }
      
    alaHAVlanClusterOperStatus  OBJECT-TYPE
	    SYNTAX   INTEGER
			  {
				enable(1),
				disable(2)
			  }
	    MAX-ACCESS  read-only
	    STATUS  current
	    DESCRIPTION
	        "Operational status of a cluster"
	    ::= { alaHAVlanClusterEntry 4 }

    alaHAVlanClusterOperStatusFlag  OBJECT-TYPE
	    SYNTAX   INTEGER 
                  {
                   invalid(0),
                   novlan(1),
                   vlandown(2),
                   vpanotforwarding(3),
                   ipinterfacedown(4),
                   noigmpmembers(5),
                   nomacaddress(6),
                   nomulticastip(7)
                  }

	    MAX-ACCESS  read-only
	    STATUS  current
	    DESCRIPTION
	        "Operational status flag describing reason of the cluster 
               Operational status."
	    ::= { alaHAVlanClusterEntry 5 }

    alaHAVlanClusterMode  OBJECT-TYPE
	    SYNTAX   INTEGER
				{
				l2mode(1),
				l3mode(2)
				}
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "This object specifies the mode of the cluster."
       DEFVAL      { l2mode }
	    ::= { alaHAVlanClusterEntry 6 }

    alaHAVlanClusterVlan OBJECT-TYPE
	    SYNTAX   Integer32 (0 .. 4094)
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "The Vlan associated with the cluster.In case of L3 cluster
                  it is invalid (value 0)"
            DEFVAL  {0}
	    ::= { alaHAVlanClusterEntry 7 }
   
    alaHAVlanClusterMacAddressType OBJECT-TYPE
	    SYNTAX   INTEGER
		{
                     invalid(1),
	             static(2),
		     dynamic(3)
		}
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "The type of ARP resolution used in L3 cluster"
            DEFVAL        { invalid }
	    ::= { alaHAVlanClusterEntry 8 }

 
    alaHAVlanClusterMacAddress OBJECT-TYPE
	    SYNTAX   MacAddress
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "The Mac-address associated with the L2 cluster
                  or the ARP entry associated with L3 cluster"
          DEFVAL { '000000000000'H }
	    ::= { alaHAVlanClusterEntry 9 }

    alaHAVlanClusterInetAddressType OBJECT-TYPE
	    SYNTAX   InetAddressType
		{
            ipv4(1)
		}
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "The type of IP address associated with the L3 cluster"
            DEFVAL  {ipv4}
	    ::= { alaHAVlanClusterEntry 10 }

    alaHAVlanClusterInetAddress OBJECT-TYPE
	    SYNTAX   InetAddress (SIZE (0..4))
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "The IP address based on 
               alaHAVlanClusterInetAddressType associated with the L3 
               cluster."
          DEFVAL { '00000000'H }
	    ::= { alaHAVlanClusterEntry 11 }

    alaHAVlanClusterMulticastStatus  OBJECT-TYPE
	    SYNTAX   INTEGER
			  {
				enable(1),
				disable(2)
			  }
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "Admin control to enable/disable IGMP on a cluster"
          DEFVAL {disable}
	    ::= { alaHAVlanClusterEntry 12 }

 
    alaHAVlanClusterMulticastInetAddressType OBJECT-TYPE
	    SYNTAX   InetAddressType (1)
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "The type of Multicast address associated with the L3 cluster"
	    ::= { alaHAVlanClusterEntry 13 }

    alaHAVlanClusterMulticastInetAddress OBJECT-TYPE
	    SYNTAX   InetAddress (SIZE (0..4))
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "The IP multicast addess of the cluster"
          DEFVAL { '00000000'H }
	    ::= { alaHAVlanClusterEntry 14 }

    alaHAVlanClusterRowStatus  OBJECT-TYPE
	    SYNTAX   RowStatus
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "Row status to control creation/deletion of the clusters"
	    ::= { alaHAVlanClusterEntry 15 }

    alaHAVlanClusterMcmStatus  OBJECT-TYPE
	    SYNTAX   INTEGER
			  {
				inSync(1),
				outofSync(2)
			  }
	    MAX-ACCESS  read-only
	    STATUS  deprecated
	    DESCRIPTION
	        "Multi-chassis status of a cluster"
	    ::= { alaHAVlanClusterEntry 16 }

    alaHAVlanClusterMcmStatusFlag  OBJECT-TYPE
	    SYNTAX   INTEGER 
                  {
                   mcdown(1),
                   operationaldown(2),
                   allportmodenotsupported(3),
                   modemismatch(4),
                   vlanmismatch(5),
                   macmismatch(6),
                   ipmismatch(7),
                   arptypemismatch(8),
                   igmpstatusmismatch(9),
                   mcastipmismatch(10),
                   syncinprogress(11),
                   invalidmac(12),
                   nonvipvlannotsupportedinl3mode(13),
                   noflag(14)
                  }

	    MAX-ACCESS  read-only
	    STATUS  deprecated
	    DESCRIPTION
	        "Multi-chassis status flag describing reason of the cluster 
               Multi-chassis status."
	    ::= { alaHAVlanClusterEntry 17 }

    alaHAVlanClusterVflStatus  OBJECT-TYPE
	    SYNTAX   INTEGER
			  {
				enable(1),
				disable(2)
			  }
	    MAX-ACCESS  read-only
	    STATUS  deprecated
	    DESCRIPTION
	        "VFL status of a cluster"
	    ::= { alaHAVlanClusterEntry 18 }

    alaHAVlanClusterLoopback OBJECT-TYPE
        SYNTAX   INTEGER
              {
                enable(1),
                disable(2)
              }
        MAX-ACCESS  read-create
        STATUS  current
        DESCRIPTION
            "Admin control to enable/disable LOOPBACK for a cluster"
             DEFVAL      { disable }

        ::= { alaHAVlanClusterEntry 19 }

-- High Availability Vlan CLuster Port Table

   alaHAVlanClusterPort OBJECT IDENTIFIER ::= { alcatelIND1HAVlanMIBObjects 2 }

   alaHAVlanClusterPortTable  OBJECT-TYPE
	    SYNTAX  SEQUENCE OF AlaHAVlanClusterPortEntry
	    MAX-ACCESS  not-accessible
	    STATUS  current
	    DESCRIPTION
		 "The  port members of a cluster."
	    ::= { alaHAVlanClusterPort 1 }

   alaHAVlanClusterPortEntry  OBJECT-TYPE
	    SYNTAX  AlaHAVlanClusterPortEntry 
	    MAX-ACCESS  not-accessible
	    STATUS  current
	    DESCRIPTION
		 "A HA VLAN cluster ports entry."
	    INDEX { alaHAVlanClusterId, alaHAVlanClusterPortIfIndex }
	    ::= { alaHAVlanClusterPortTable 1 }

   AlaHAVlanClusterPortEntry ::= SEQUENCE {
            alaHAVlanClusterPortIfIndex
			InterfaceIndex,
            alaHAVlanClusterPortRowStatus
                        RowStatus,
            alaHAVlanClusterPortType
                        INTEGER,
            alaHAVlanClusterPortValid
                        INTEGER
	}

   alaHAVlanClusterPortIfIndex OBJECT-TYPE
	    SYNTAX   InterfaceIndex
	    MAX-ACCESS  accessible-for-notify
	    STATUS  current
	    DESCRIPTION
	        "The ifindex identifying the cluster port.
                 An ifindex of 1 shall be used for all port"
	    ::= { alaHAVlanClusterPortEntry 1 }


   alaHAVlanClusterPortRowStatus  OBJECT-TYPE
	    SYNTAX   RowStatus 
	    MAX-ACCESS  read-create
	    STATUS  current
	    DESCRIPTION
	        "Row status to control creation/deletion of ports to
               the clusters"
	    ::= { alaHAVlanClusterPortEntry 2 }

   alaHAVlanClusterPortType  OBJECT-TYPE
	    SYNTAX   INTEGER
			  {
				static(1),
				dynamic(2)
			  }
	    MAX-ACCESS  read-only
	    STATUS  current
	    DESCRIPTION
	        "Port type of the cluster; whether
                 dynamically (IGMP enabled) assigned to
                 cluster or statically assigned"
	    ::= { alaHAVlanClusterPortEntry 3 }


    alaHAVlanClusterPortValid  OBJECT-TYPE
	    SYNTAX   INTEGER
			  {
				valid(1),
				invalid(2)
			  }
	    MAX-ACCESS  read-only
	    STATUS  current
	    DESCRIPTION
	        "Port validity of the cluster; whether port
                 is currently active/inactive"
	    ::= { alaHAVlanClusterPortEntry 4 }

-- ******************************************************************
-- NOTIFICATIONS (TRAPS)
-- ******************************************************************
        alaHAVlanClusterPeerMismatch NOTIFICATION-TYPE
        OBJECTS  {
                        alaHAVlanClusterId
                 }
        STATUS   current
        DESCRIPTION
                "The trap shall be raised when parameteras configured for this cluster ID
                 (Level 1 check) does not match accross the MCLAG peers."
        ::= { alcatelIND1HAVlanMIBNotifications 1 }

        alaHAVlanMCPeerMismatch NOTIFICATION-TYPE
        OBJECTS {
                        alaHAVlanClusterId,
                        alaHAVlanMultiChassisId,
                        alaHAVlanClusterPortIfIndex
                }
        STATUS  current
        DESCRIPTION
                "The trap shall be raised when the cluster parameters are matching on the
                 peers but MCLAG is not configured or clusters are not in operational
                 state."
        ::= { alcatelIND1HAVlanMIBNotifications 2}


        alaHAVlanDynamicMAC NOTIFICATION-TYPE
        OBJECTS {
                        alaHAVlanClusterId,
                        alaHAVlanClusterInetAddress,
                        alaHAVlanClusterMacAddress,
                        alaHAVlanClusterPortIfIndex
                }
        STATUS  current
        DESCRIPTION
                "The trap shall be raised when the dynamic MAC learnt on
                 non server-cluster port" 
        ::= { alcatelIND1HAVlanMIBNotifications 3}

-- Notification Objects
   alaHAVlanNotificationObj OBJECT IDENTIFIER ::= { alcatelIND1HAVlanMIBObjects 3 }

        alaHAVlanMultiChassisId OBJECT-TYPE
            SYNTAX MultiChassisId
            MAX-ACCESS accessible-for-notify
            STATUS  current
            DESCRIPTION
                "The Multi Chassis ID identifying the Multi Chassis Peer."
        ::= { alaHAVlanNotificationObj 1 }


--
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- COMPLIANCE
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

   alcatelIND1HAVlanMIBCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
            "Compliance statement for HA VLAN."
        MODULE
            MANDATORY-GROUPS
            {
                alaHAVlanClusterGroup,
                alaHAVlanClusterPortGroup,
                alaHAVlanNotificationGroup
            }

        ::= { alcatelIND1HAVlanMIBCompliances 1 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
-- UNITS OF CONFORMANCE
-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

    alaHAVlanClusterGroup OBJECT-GROUP
        OBJECTS
        {
                  alaHAVlanClusterId,
                  alaHAVlanClusterName,
                  alaHAVlanClusterAdminStatus,
                  alaHAVlanClusterOperStatus,
                  alaHAVlanClusterOperStatusFlag,
                  alaHAVlanClusterMode,
                  alaHAVlanClusterVlan,
                  alaHAVlanClusterMacAddressType,
                  alaHAVlanClusterMacAddress,
                  alaHAVlanClusterInetAddressType,
                  alaHAVlanClusterInetAddress,
                  alaHAVlanClusterMulticastStatus,
                  alaHAVlanClusterMulticastInetAddressType,
                  alaHAVlanClusterMulticastInetAddress,
                  alaHAVlanClusterRowStatus,
                  alaHAVlanClusterMcmStatus,
                  alaHAVlanClusterMcmStatusFlag,
                  alaHAVlanClusterVflStatus,
                  alaHAVlanClusterLoopback
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of HA Vlan Clusters."
        ::= { alcatelIND1HAVlanMIBGroups 1 }

   alaHAVlanClusterPortGroup OBJECT-GROUP
        OBJECTS
        {
                  alaHAVlanClusterPortIfIndex,
                  alaHAVlanClusterPortRowStatus,
                  alaHAVlanClusterPortType,
                  alaHAVlanClusterPortValid
        }
        STATUS  current
        DESCRIPTION
            "Collection of objects for management of HA Vlan Cluster ports."
        ::= { alcatelIND1HAVlanMIBGroups 2 }

   alaHAVlanNotificationObjectGroup OBJECT-GROUP
        OBJECTS
        {
                alaHAVlanMultiChassisId
        }
        STATUS current
        DESCRIPTION
                "Collection of trap objects for management of HAVLAN."
        ::= {  alcatelIND1HAVlanMIBGroups 3}

   alaHAVlanNotificationGroup NOTIFICATION-GROUP
        NOTIFICATIONS
        {
                  alaHAVlanClusterPeerMismatch,
                  alaHAVlanMCPeerMismatch,
                  alaHAVlanDynamicMAC
        }
        STATUS  current
        DESCRIPTION
        "Collection of notifications for HAVLAN."
        ::= { alcatelIND1HAVlanMIBGroups 4 }


--  END ***********************



END


