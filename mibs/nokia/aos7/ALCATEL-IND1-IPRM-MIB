ALCATEL-IND1-IPRM-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYP<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Integer32, Unsigned32
        FROM SNMPv2-SM<PERSON>
    RowStatus,TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM SNMPv2-CONF
    IANAipRouteProtocol
        FROM IANA-RTPROTO-MIB
    routingIND1Iprm
        FROM ALCATEL-IND1-BASE ;

alcatelIND1IPRMMIB MODULE-IDENTITY

    LAST-UPDATED  "201212010000Z"
    ORGANIZATION  "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:

                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America

        Telephone:               North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507

        Electronic Mail:         <EMAIL>
        World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
        File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):

             This proprietary MIB contains management information for
             the configuration of IPRM global configuration parameters.

         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.

         No liability shall be assumed for any incidental, indirect, special, or
         consequential damages whatsoever arising from or related to this
         document or the information contained herein.

         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.

                     Copyright (C) 1995-2010 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "201002220000Z"
    DESCRIPTION
        "The latest version of this MIB Module."

    ::= { routingIND1Iprm 1 }

alcatelIND1IPRMMIBObjects  OBJECT IDENTIFIER ::= { alcatelIND1IPRMMIB 1 }

alaIprmConfig  OBJECT IDENTIFIER ::= { alcatelIND1IPRMMIBObjects 1 }


--  Textual Conventions

AlaIprmAdminStatus ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION
            " Admin status"
    SYNTAX  INTEGER { enabled(1),
                      disabled(2)
                    }

AlaIprmStaticRouteTypes ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION
            "A mechanism for distinguishing the type of static route"
    SYNTAX  INTEGER {
                     regular(1),
                     recursive(2),
                     bfdEnabled(3),
                     interface(4)
                    }


AlaMplsL3VpnRouteType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Used to define the type of a route target usage.
         Route targets can be specified to be imported,
         exported, or both. For a complete definition of a
         route target, see [RFC4364]."
    REFERENCE
        "[RFC4364]"
    SYNTAX  INTEGER { import(1), export(2), both(3) }

AlaIprmRtPrefType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Used to define the various types of routes along with their
         route preference values"
    SYNTAX  INTEGER {
                     local(1),
                     static(2),
                     ospf(3),
                     rip(4),
                     bgpExternal(5),
                     bgpInternal(6),
                     isisl1(7),
                     isisl2(8),
                     import(9)
                    }


-- Route Table

alaIprmRouteTable OBJECT-TYPE
    SYNTAX   SEQUENCE OF AlaIprmRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "IPRM's Routing table."
    ::= { alaIprmConfig 1 }

alaIprmRouteEntry OBJECT-TYPE
    SYNTAX   AlaIprmRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "A particular route to  a  particular  destination"
    INDEX {
        alaIprmRouteDest,
        alaIprmRouteMask,
        alaIprmRouteTos,
        alaIprmRouteNextHop
        }
    ::= { alaIprmRouteTable 1 }

AlaIprmRouteEntry ::=
    SEQUENCE {
        alaIprmRouteDest
            IpAddress,
        alaIprmRouteMask
            IpAddress,
        alaIprmRouteTos
             Integer32,
        alaIprmRouteNextHop
            IpAddress,
        alaIprmRouteProto
            IANAipRouteProtocol,
        alaIprmRouteMetric
            Integer32,
        alaIprmRoutePriority
            Integer32
    }

alaIprmRouteDest OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The destination IP address of this route."
    ::= { alaIprmRouteEntry 1 }

alaIprmRouteMask OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The mask of this route"
    ::= { alaIprmRouteEntry 2 }

alaIprmRouteTos OBJECT-TYPE
    SYNTAX   Integer32 (0..16)
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The TOS for this route"
    ::= { alaIprmRouteEntry 3 }

alaIprmRouteNextHop OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The Gateway for this route"
    ::= { alaIprmRouteEntry 4 }

alaIprmRouteProto OBJECT-TYPE
    SYNTAX   IANAipRouteProtocol
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The protocol that this route was learned from"
    ::= { alaIprmRouteEntry 5 }

alaIprmRouteMetric OBJECT-TYPE
    SYNTAX   Integer32
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The metric for this route"
    ::= { alaIprmRouteEntry 6 }

alaIprmRoutePriority OBJECT-TYPE
    SYNTAX   Integer32
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The priority for this route"
    ::= { alaIprmRouteEntry 7 }


-- Static Route Table

alaIprmStaticRouteTable OBJECT-TYPE
    SYNTAX   SEQUENCE OF AlaIprmStaticRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "IPRM's Static Route table."
    ::= { alaIprmConfig 2 }

alaIprmStaticRouteEntry OBJECT-TYPE
    SYNTAX   AlaIprmStaticRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "A route entered by the user"
    INDEX {
        alaIprmStaticRouteDest,
        alaIprmStaticRouteMask,
        alaIprmStaticRouteNextHop
        }
    ::= { alaIprmStaticRouteTable 1 }

AlaIprmStaticRouteEntry ::=
    SEQUENCE {
        alaIprmStaticRouteDest
            IpAddress,
        alaIprmStaticRouteMask
            IpAddress,
        alaIprmStaticRouteNextHop
            IpAddress,
        alaIprmStaticRouteMetric
            Integer32,
        alaIprmStaticRouteStatus
            RowStatus,
        alaIprmStaticRouteBfdStatus
            AlaIprmAdminStatus,
        alaIprmStaticRouteType
            AlaIprmStaticRouteTypes,
        alaIprmStaticRouteTag
            Unsigned32,
        alaIprmStaticRouteName
            SnmpAdminString
    }

alaIprmStaticRouteDest OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The destination IP address of this route."
    ::= { alaIprmStaticRouteEntry 1 }

alaIprmStaticRouteMask OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The mask of this route"
    ::= { alaIprmStaticRouteEntry 2 }

alaIprmStaticRouteNextHop OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The Gateway for this route"
    ::= { alaIprmStaticRouteEntry 3 }

alaIprmStaticRouteMetric OBJECT-TYPE
    SYNTAX   Integer32
    MAX-ACCESS read-create
    STATUS   current
    DESCRIPTION
        "The metric for this route"
    ::= { alaIprmStaticRouteEntry 4 }

alaIprmStaticRouteStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "The status of this entry.  Enabling causes a new
                static route to be created, disabled causes that
                static route to be deleted."
    ::= { alaIprmStaticRouteEntry 5 }

alaIprmStaticRouteBfdStatus OBJECT-TYPE
    SYNTAX     AlaIprmAdminStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "For enabling or disabling the BFD status on this static route."
    ::= { alaIprmStaticRouteEntry 6 }

alaIprmStaticRouteType OBJECT-TYPE
    SYNTAX     AlaIprmStaticRouteTypes
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "Type of static route."
    ::= { alaIprmStaticRouteEntry 7 }

alaIprmStaticRouteTag OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "Tag associated with static route."
    ::= { alaIprmStaticRouteEntry 8 }

alaIprmStaticRouteName OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(0..32))
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "A comment associated with the static route."
    ::= { alaIprmStaticRouteEntry 9 }

-- BFD status for all static routes

alaIprmStaticAllBfd   OBJECT-TYPE
   SYNTAX   AlaIprmAdminStatus
    MAX-ACCESS read-write
    STATUS   current
    DESCRIPTION
        "For attempting to enable/disable the BFD state on all static routes."
    DEFVAL      { disabled }
    ::= { alaIprmConfig 3 }



-- Primary Address

alaIprmPrimaryAddress    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines the Primary IP Address of this Router."
    ::= { alaIprmConfig 4 }



-- Router ID

alaIprmRouterId    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines the Router-id for this Router."
    ::= {alaIprmConfig 5 }


-- Route Distinguisher

alaIprmRouteDistinguisher OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE (3..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Associates a route distinguisher to a VRF. Syntax for a route distinguisher and route target
         as defined in [RFC4364]."
    ::= { alaIprmConfig 6 }


-- Route Target Table

alaIprmRouteTargetTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIprmRouteTargetEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table specifies per-VRF route target association.
         Each entry identifies a connectivity policy supported
         as part of a VPN."
    ::= { alaIprmConfig 7 }

alaIprmRouteTargetEntry OBJECT-TYPE
    SYNTAX      AlaIprmRouteTargetEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created by an LSR for
         each route target configured for a VRF supporting
         a MPLS L3VPN instance. See [RFC4364] for a
         complete definition of a route target."
    INDEX {
        alaIprmRouteTarget
       }
    ::= { alaIprmRouteTargetTable 1 }

AlaIprmRouteTargetEntry ::= SEQUENCE {
    alaIprmRouteTarget                   SnmpAdminString,
    alaIprmRouteTargetType               AlaMplsL3VpnRouteType,
    alaIprmRouteTargetRowStatus          RowStatus
   }

alaIprmRouteTarget OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE (3..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A Virtual Router Route Target. Syntax for a route distinguisher and route target
         as defined in [RFC4364]."
    ::= { alaIprmRouteTargetEntry 1 }

alaIprmRouteTargetType OBJECT-TYPE
    SYNTAX      AlaMplsL3VpnRouteType
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "The route target distribution type."
    ::= { alaIprmRouteTargetEntry 2 }

alaIprmRouteTargetRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "This variable is used to create, modify, and/or
         delete a row in this table."
    ::= { alaIprmRouteTargetEntry 3 }


-- Route Preference Table

alaIprmRtPrefTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIprmRtPrefTableEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table specifies the route preference values for
         various types of routes."
    ::= { alaIprmConfig 8 }

alaIprmRtPrefTableEntry OBJECT-TYPE
    SYNTAX      AlaIprmRtPrefTableEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry is in this table for each route preference type."
    INDEX {
        alaIprmRtPrefEntryType
       }
    ::= { alaIprmRtPrefTable 1 }

AlaIprmRtPrefTableEntry ::= SEQUENCE {
    alaIprmRtPrefEntryType      AlaIprmRtPrefType,
    alaIprmRtPrefEntryValue     Integer32
   }

alaIprmRtPrefEntryType OBJECT-TYPE
    SYNTAX      AlaIprmRtPrefType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The type of route (associated with a route preference value)"
    ::= { alaIprmRtPrefTableEntry 1 }

alaIprmRtPrefEntryValue OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "The route preference value used for this type of route."
    ::= { alaIprmRtPrefTableEntry 2 }

-- Route Leaking Export route-map

alaIprmExportRouteMap   OBJECT-TYPE
   SYNTAX   Integer32
    MAX-ACCESS read-write
    STATUS   current
    DESCRIPTION
        "A route-map index representing the route-map used to filter
         routes exported to the Global Route Table. A value of -1 indicates 
         no routes should be exported. A value of 0 indicates all routes 
         should be exported. Otherwise, the value is the route-map index to 
         use for filtering."         
    ::= { alaIprmConfig 9 }

-- Route Leaking Import Vrf Table

alaIprmImportVrfTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIprmImportVrfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table specifies VRFs to import routes from." 
    ::= { alaIprmConfig 10 }

alaIprmImportVrfEntry OBJECT-TYPE
    SYNTAX      AlaIprmImportVrfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created for each VRF
         to import routes from."
    INDEX {
        alaIprmImportVrfName
       }
    ::= { alaIprmImportVrfTable 1 }

AlaIprmImportVrfEntry ::= SEQUENCE {
    alaIprmImportVrfName              SnmpAdminString,
    alaIprmImportVrfRouteMap          Integer32,
    alaIprmImportVrfRowStatus         RowStatus
   }

alaIprmImportVrfName OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE (0..20))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VRF that routes should be imported from. An
         empty string indicates the default VRF."
    ::= { alaIprmImportVrfEntry 1 }

alaIprmImportVrfRouteMap OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "A route-map index representing the route-map used to filter
         imported routes."
    ::= { alaIprmImportVrfEntry 2 }

alaIprmImportVrfRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "This variable is used to create, modify, and/or
         delete a row in this table."
    ::= { alaIprmImportVrfEntry 3 }

-- Route Leaking Import Isid Table

alaIprmImportIsidTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIprmImportIsidEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table specifies ISIS L3VPN ISIDs to import routes from." 
    ::= { alaIprmConfig 11 }

alaIprmImportIsidEntry OBJECT-TYPE
    SYNTAX      AlaIprmImportIsidEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created for each ISID
         to import routes from."
    INDEX {
        alaIprmImportIsid
       }
    ::= { alaIprmImportIsidTable 1 }

AlaIprmImportIsidEntry ::= SEQUENCE {
    alaIprmImportIsid              Unsigned32,
    alaIprmImportIsidRouteMap      Integer32,
    alaIprmImportIsidRowStatus     RowStatus
   }

alaIprmImportIsid OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The ISIS SPB L3VPN ISID that routes should be imported from."
    ::= { alaIprmImportIsidEntry 1 }

alaIprmImportIsidRouteMap OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "A route-map index representing the route-map used to filter
         imported routes."
    ::= { alaIprmImportIsidEntry 2 }

alaIprmImportIsidRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "This variable is used to create, modify, and/or
         delete a row in this table."
    ::= { alaIprmImportIsidEntry 3 }

-- Route Leaking Export All route-map

alaIprmExportToAllVrfsRouteMap   OBJECT-TYPE
   SYNTAX   Integer32
    MAX-ACCESS read-write
    STATUS   current
    DESCRIPTION
        "Used to export routes to all other VRFs that don't already have
         an entry in the Import Vrf Table. The object is a route-map index 
         representing the route-map used to filter the exported routes.
         A value of -1 indicates no routes should be exported. A value of 0 
         indicates all routes should be exported. Otherwise, the value is 
         the route-map index to use for filtering."         
    ::= { alaIprmConfig 12 }


-- conformance information

alcatelIND1IPRMMIBConformance OBJECT IDENTIFIER ::= { alcatelIND1IPRMMIB 2 }
alcatelIND1IPRMMIBCompliances OBJECT IDENTIFIER ::=
                                          { alcatelIND1IPRMMIBConformance 1 }
alcatelIND1IPRMMIBGroups      OBJECT IDENTIFIER ::=
                                          { alcatelIND1IPRMMIBConformance 2 }

-- compliance statements

alaIprmCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers running IPRM
            and implementing the ALCATEL-IND1-IPRM MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { alaIprmConfigMIBGroup }

    ::= { alcatelIND1IPRMMIBCompliances 1 }

-- units of conformance

alaIprmConfigMIBGroup OBJECT-GROUP
    OBJECTS { alaIprmRouteProto, alaIprmRouteMetric, alaIprmRoutePriority,
                alaIprmStaticRouteMetric, alaIprmStaticRouteStatus,
                alaIprmStaticRouteBfdStatus, alaIprmStaticRouteType,
                alaIprmStaticRouteTag, alaIprmStaticRouteName, 
		alaIprmStaticAllBfd,
                alaIprmPrimaryAddress, alaIprmRouterId,
                alaIprmRouteDistinguisher, alaIprmRouteTargetType,
                alaIprmRouteTargetRowStatus, alaIprmRtPrefEntryValue,
                alaIprmExportRouteMap, alaIprmImportVrfRouteMap,
                alaIprmImportVrfRowStatus, alaIprmImportIsidRouteMap, 
                alaIprmImportIsidRowStatus, alaIprmExportToAllVrfsRouteMap
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of global
            configuration parameters of the IPRM Module."
    ::= { alcatelIND1IPRMMIBGroups 1 }


END

