ALCATEL-IND1-IPRMV6-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE,
    Integer32, Unsigned32
        FROM SNMPv2-SM<PERSON>
    RowStatus, TruthValue, TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM SNMPv2-CONF
    IANAipRouteProtocol
        FROM IANA-RTPROTO-MIB
    Ipv6Address, Ipv6IfIndex, Ipv6IfIndexOrZero
        FROM IPV6-TC
    routingIND1Iprm
        FROM ALCATEL-IND1-BASE
    AlaIprmStaticRouteTypes, AlaIprmAdminStatus
        FROM ALCATEL-IND1-IPRM-MIB;

alcatelIND1IPRMV6MIB MODULE-IDENTITY

    LAST-UPDATED  "201309230000Z"
    ORGANIZATION  "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:

                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America

        Telephone:               North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507

        Electronic Mail:         <EMAIL>
        World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
        File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):

             This proprietary MIB contains management information for
             the configuration of IPRMv6 global configuration parameters.

         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.

         No liability shall be assumed for any incidental, indirect, special, or
         consequential damages whatsoever arising from or related to this
         document or the information contained herein.

         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.

                     Copyright (C) 1995-2010 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "201002220000Z"
    DESCRIPTION
        "The latest version of this MIB Module."

    ::= { routingIND1Iprm 2 }

alcatelIND1IPRMV6MIBObjects  OBJECT IDENTIFIER ::= { alcatelIND1IPRMV6MIB 1 }

alaIprmV6Config  OBJECT IDENTIFIER ::= { alcatelIND1IPRMV6MIBObjects 1 }

--  Textual Conventions

AlaIprmV6RtPrefType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Used to define the various types of IPv6 routes along with their
         route preference values"
    SYNTAX  INTEGER {
                     local(1),
                     static(2),
                     ospf(3),
                     rip(4),
                     bgpExternal(5),
                     bgpInternal(6),
                     isisl1(7),
                     isisl2(8),
                     import(9) 
                    }


-- IPv6 Route Table

alaIprmV6RouteTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF AlaIprmV6RouteEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
       "IPRM's IPv6 Routing table. This table contains
       an entry for each valid IPv6 unicast route that
       can be used for packet forwarding determination.
       It is for display purposes only."
    ::= { alaIprmV6Config 1 }

alaIprmV6RouteEntry OBJECT-TYPE
    SYNTAX     AlaIprmV6RouteEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
       "A routing entry."
    INDEX {
        alaIprmV6RouteDest,
        alaIprmV6RoutePfxLength,
        alaIprmV6RouteNextHop,
        alaIprmV6RouteProtocol,
        alaIprmV6RouteIfIndex
        }
    ::= { alaIprmV6RouteTable 1 }

AlaIprmV6RouteEntry ::=
    SEQUENCE {
        alaIprmV6RouteDest      Ipv6Address,
        alaIprmV6RoutePfxLength Integer32,
        alaIprmV6RouteNextHop   Ipv6Address,
        alaIprmV6RouteProtocol  IANAipRouteProtocol,
        alaIprmV6RouteIfIndex   Ipv6IfIndex,
        alaIprmV6RouteMetric    Unsigned32,
        alaIprmV6RouteValid     TruthValue
    }

alaIprmV6RouteDest OBJECT-TYPE
    SYNTAX     Ipv6Address
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
       "The destination IPv6 address of this route."
    ::= { alaIprmV6RouteEntry 1 }

alaIprmV6RoutePfxLength OBJECT-TYPE
    SYNTAX     Integer32(0..128)
    UNITS      "bits"
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Indicates the prefix length of the destination
        address."
    ::= { alaIprmV6RouteEntry 2 }

alaIprmV6RouteNextHop OBJECT-TYPE
    SYNTAX     Ipv6Address
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "On remote routes, the address of the next
        system en route;  otherwise, ::0
        ('00000000000000000000000000000000'H in ASN.1
        string representation)."
    ::= { alaIprmV6RouteEntry 3 }

alaIprmV6RouteProtocol OBJECT-TYPE
    SYNTAX     IANAipRouteProtocol
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The protocol that this route was learned from"
    ::= { alaIprmV6RouteEntry 4 }

alaIprmV6RouteIfIndex OBJECT-TYPE
    SYNTAX     Ipv6IfIndex
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "The index value which uniquely identifies the local
        interface through which the next hop of this
        route should be reached."
    ::= { alaIprmV6RouteEntry 5 }

alaIprmV6RouteMetric OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The routing metric for this route. The
        semantics of this metric are determined by the
        routing protocol specified in the route's
        ipv6RouteProtocol value."
    ::= { alaIprmV6RouteEntry 6 }

alaIprmV6RouteValid OBJECT-TYPE
    SYNTAX     TruthValue
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "If this value is true(1) IPRM believes the
        route is being used.  If this value is false(2),
        the route is currently not being used and is
        considered a backup route."
    ::= { alaIprmV6RouteEntry 7 }


-- IPRM's IPv6 Static Routes Table

alaIprmV6StaticRouteTable OBJECT-TYPE
    SYNTAX   SEQUENCE OF AlaIprmV6StaticRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "Table allowing the creation and removal of IPv6 Static Routes."
    ::= { alaIprmV6Config 2 }

alaIprmV6StaticRouteEntry OBJECT-TYPE
    SYNTAX   AlaIprmV6StaticRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "An IPv6 static route entered by the user"
    INDEX {
        alaIprmV6StaticRouteDest,
        alaIprmV6StaticRoutePfxLength,
        alaIprmV6StaticRouteNextHop,
        alaIprmV6StaticRouteIfIndex
        }
    ::= { alaIprmV6StaticRouteTable 1 }

AlaIprmV6StaticRouteEntry ::=
    SEQUENCE {
        alaIprmV6StaticRouteDest      Ipv6Address,
        alaIprmV6StaticRoutePfxLength Integer32,
        alaIprmV6StaticRouteNextHop   Ipv6Address,
        alaIprmV6StaticRouteIfIndex   Ipv6IfIndexOrZero,
        alaIprmV6StaticRouteMetric    Unsigned32,
        alaIprmV6StaticRouteStatus    RowStatus,
        alaIprmV6StaticRouteTag       Unsigned32,
        alaIprmV6StaticRouteName      SnmpAdminString,
        alaIprmV6StaticRouteBfdStatus AlaIprmAdminStatus,
        alaIprmV6StaticRouteType      AlaIprmStaticRouteTypes
    }

alaIprmV6StaticRouteDest OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "The destination IPv6 address of this static route.
       This object may not take a multicast address value."
    ::= { alaIprmV6StaticRouteEntry 1 }

alaIprmV6StaticRoutePfxLength OBJECT-TYPE
    SYNTAX      Integer32(0..128)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Indicates the prefix length of the destination
        address."
    ::= { alaIprmV6StaticRouteEntry 2 }

alaIprmV6StaticRouteNextHop OBJECT-TYPE
    SYNTAX      Ipv6Address
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The IPv6 address of the next hop towards the
        destination."
    ::= { alaIprmV6StaticRouteEntry 3 }

alaIprmV6StaticRouteIfIndex OBJECT-TYPE
    SYNTAX      Ipv6IfIndexOrZero
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The index value which uniquely identifies the local
        interface through which the next hop of this
        route should be reached."
    ::= { alaIprmV6StaticRouteEntry 4 }

alaIprmV6StaticRouteMetric OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The routing metric for this route. The lower the
        value, the higher the priority for the static
        route."
    DEFVAL      { 1 }
    ::= { alaIprmV6StaticRouteEntry 5 }

alaIprmV6StaticRouteStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "Used to control the addition and removal of static
        routes."
    ::= { alaIprmV6StaticRouteEntry 6 }

alaIprmV6StaticRouteTag OBJECT-TYPE
    SYNTAX     Unsigned32
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "Tag associated with route."
    ::= { alaIprmV6StaticRouteEntry 7 }

alaIprmV6StaticRouteName OBJECT-TYPE
    SYNTAX     SnmpAdminString (SIZE(0..32))
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "A comment associated with the static route."
    ::= { alaIprmV6StaticRouteEntry 8 }

alaIprmV6StaticRouteBfdStatus OBJECT-TYPE
    SYNTAX     AlaIprmAdminStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "For enabling or disabling the BFD status on this static route."
    ::= { alaIprmV6StaticRouteEntry 9 }

alaIprmV6StaticRouteType OBJECT-TYPE
    SYNTAX     AlaIprmStaticRouteTypes
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
            "Type of static route."
    ::= { alaIprmV6StaticRouteEntry 10 }



-- IPRM's IPV6 Route Preference Table

alaIprmV6RtPrefTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIprmV6RtPrefTableEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table specifies the route preference values for
         various types of IPv6 routes handled by IPRM."
    ::= { alaIprmV6Config 3 }

alaIprmV6RtPrefTableEntry OBJECT-TYPE
    SYNTAX      AlaIprmV6RtPrefTableEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry is in this table for each IPv6 route preference type."
    INDEX {
        alaIprmV6RtPrefEntryType
       }
    ::= { alaIprmV6RtPrefTable 1 }

AlaIprmV6RtPrefTableEntry ::= SEQUENCE {
    alaIprmV6RtPrefEntryType      AlaIprmV6RtPrefType,
    alaIprmV6RtPrefEntryValue     Integer32
   }

alaIprmV6RtPrefEntryType OBJECT-TYPE
    SYNTAX      AlaIprmV6RtPrefType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The type of route (associated with a route preference value)"
    ::= { alaIprmV6RtPrefTableEntry 1 }

alaIprmV6RtPrefEntryValue OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-write
    STATUS  current
    DESCRIPTION
        "The route preference value used for this type of route."
    ::= { alaIprmV6RtPrefTableEntry 2 }

-- Route Leaking Export route-map

alaIprmV6ExportRouteMap   OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "A route-map index representing the route-map used to filter
         IPv6 routes exported to the Global Route Table. A value of -1 
         indicates no routes should be exported. A value of 0 indicates 
         all routes should be exported. Otherwise, the value is the 
         route-map index to use for filtering."
    ::= { alaIprmV6Config 4 }

-- Route Leaking Export All route-map

alaIprmV6ExportToAllVrfsRouteMap   OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Used to export IPv6 routes to all other VRFs that don't already have
         an entry in the Import Vrf Table. The object is a route-map index 
         representing the route-map used to filter the exported routes.
         A value of -1 indicates no routes should be exported. A value of 0 
         indicates all routes should be exported. Otherwise, the value is 
         the route-map index to use for filtering."
    ::= { alaIprmV6Config 5 }


-- Route Leaking Import Vrf Table

alaIprmV6ImportVrfTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlaIprmV6ImportVrfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table specifies VRFs to import IPv6 routes from."
    ::= { alaIprmV6Config 6 }

alaIprmV6ImportVrfEntry OBJECT-TYPE
    SYNTAX      AlaIprmV6ImportVrfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created for each VRF
         to import IPv6 routes from."
    INDEX {
        alaIprmV6ImportVrfName
       }
    ::= { alaIprmV6ImportVrfTable 1 }

AlaIprmV6ImportVrfEntry ::= SEQUENCE {
    alaIprmV6ImportVrfName              SnmpAdminString,
    alaIprmV6ImportVrfRouteMap          Integer32,
    alaIprmV6ImportVrfRowStatus         RowStatus
   }

alaIprmV6ImportVrfName OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE (0..20))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the VRF that IPv6 routes should be imported from. An
         empty string indicates the default VRF."
    ::= { alaIprmV6ImportVrfEntry 1 }

alaIprmV6ImportVrfRouteMap OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "A route-map index representing the route-map used to filter
         IPv6 imported routes.  A value of 0 indicates all IPv6 routes 
         that have been exported by the specified VRF should be imported."
    ::= { alaIprmV6ImportVrfEntry 2 }

alaIprmV6ImportVrfRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS  current
    DESCRIPTION
        "This variable is used to create, modify, and/or
         delete a row in this table."
    ::= { alaIprmV6ImportVrfEntry 3 }


-- BFD status for all static routes

alaIprmV6StaticAllBfd   OBJECT-TYPE
   SYNTAX   AlaIprmAdminStatus
    MAX-ACCESS read-write
    STATUS   current
    DESCRIPTION
        "To enable/disable the BFD state on all IPv6 static routes."
    DEFVAL      { disabled }
    ::= { alaIprmV6Config 7 }


-- conformance information

alcatelIND1IPRMV6MIBConformance OBJECT IDENTIFIER ::= { alcatelIND1IPRMV6MIB 2 }
alcatelIND1IPRMV6MIBCompliances OBJECT IDENTIFIER ::=
                                          { alcatelIND1IPRMV6MIBConformance 1 }
alcatelIND1IPRMV6MIBGroups      OBJECT IDENTIFIER ::=
                                          { alcatelIND1IPRMV6MIBConformance 2 }

-- compliance statements

alaIprmV6Compliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers running IPRM
            and implementing the ALCATEL-IND1-IPRMV6 MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { alaIprmV6ConfigMIBGroup }

    ::= { alcatelIND1IPRMV6MIBCompliances 1 }

-- units of conformance

alaIprmV6ConfigMIBGroup OBJECT-GROUP
    OBJECTS {   alaIprmV6RouteMetric, alaIprmV6RouteValid,
                alaIprmV6StaticRouteMetric, alaIprmV6StaticRouteStatus,
                alaIprmV6StaticRouteTag, alaIprmV6StaticRouteName, 
                alaIprmV6StaticRouteBfdStatus, alaIprmV6StaticRouteType,
                alaIprmV6RtPrefEntryValue,  alaIprmV6ExportRouteMap, 
                alaIprmV6ExportToAllVrfsRouteMap, alaIprmV6ImportVrfRouteMap,
                alaIprmV6ImportVrfRowStatus, alaIprmV6StaticAllBfd
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of global
            configuration parameters of the IPRM Module supporting IPv6 routes."
    ::= { alcatelIND1IPRMV6MIBGroups 1 }


END

