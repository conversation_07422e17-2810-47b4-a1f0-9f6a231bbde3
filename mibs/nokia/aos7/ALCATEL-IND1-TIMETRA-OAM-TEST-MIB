ALCATEL-IND1-TIMETRA-OAM-TEST-MIB DEFINITIONS ::= BEGIN
                                                                        
IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, <PERSON><PERSON><PERSON><PERSON><PERSON>, 
        <PERSON>teger32, <PERSON>signed32, <PERSON><PERSON><PERSON>32, <PERSON>32
            FROM SNMPv2-<PERSON><PERSON>

        MODULE-COMPLIANCE, OBJECT-<PERSON><PERSON><PERSON>, NOTIFICATION-GROUP
            FROM SNMPv2-CONF

        TEXTUAL-CONVENTION, RowStatus, StorageType, TruthValue,
        DateAndTime, MacAddress, TimeStamp, DisplayString
            FROM SNMPv2-TC
        
        SnmpAdminString
            FROM SNMP-FRAMEWORK-MI<PERSON>

        Inet<PERSON>ressType, InetAddress, InetAddressPrefixLength
            FROM INET-ADDRESS-MIB

        InterfaceIndex, InterfaceIndexOrZero
            FROM IF-MIB

        RouterID
            FROM OSPF-MIB

        AtmVpIdentifier, AtmVcIdentifier  
                FROM ATM-TC-<PERSON><PERSON>      

        M<PERSON>abel
            FROM MPLS-LDP-<PERSON><PERSON>mpt<PERSON>, TmnxVcType, TmnxVcId, TmnxVcIdOrNone, TFCName, 
        TmnxPortID, TmnxEncapVal, TmnxStrSapId, TmnxServId, TmnxTunnelType, 
        TmnxTunnelID, TmnxBgpRouteTarget, TmnxVPNRouteDistinguisher, 
        TmnxVRtrID, IpAddressPrefixLength, TmnxAdminState, SdpBindId,
        TItemDescription, TPolicyStatementNameOrEmpty
            FROM ALCATEL-IND1-TIMETRA-TC-MIB

        timetraSRMIBModules, tmnxSRObjs, 
        tmnxSRNotifyPrefix, tmnxSRConfs                              
            FROM ALCATEL-IND1-TIMETRA-GLOBAL-MIB

        SdpId, SdpBindVcType
            FROM ALCATEL-IND1-TIMETRA-SERV-MIB
            
        TProfile
            FROM ALCATEL-IND1-TIMETRA-QOS-MIB

        vRtrID
            FROM ALCATEL-IND1-TIMETRA-VRTR-MIB
        ;

timetraOamTestMIBModule MODULE-IDENTITY
        LAST-UPDATED    "0801010000Z"
        ORGANIZATION    "Alcatel"
        CONTACT-INFO    "Alcatel 7x50 Support
                Web: http://www.alcatel.com/comps/pages/carrier_support.jhtml"
                              
        DESCRIPTION
        "This document is the SNMP MIB module to manage and provision the 
        Alcatel 7x50 OAM tests.

        Copyright 2003-2008 Alcatel-Lucent.  All rights reserved.
        Reproduction of this document is authorized on the condition that
        the foregoing copyright notice is included.

        This SNMP MIB module (Specification) embodies Alcatel's
        proprietary intellectual property.  Alcatel retains 
        all title and ownership in the Specification, including any 
        revisions.

        Alcatel grants all interested parties a non-exclusive 
        license to use and distribute an unmodified copy of this 
        Specification in connection with management of Alcatel 
        products, and without fee, provided this copyright notice and 
        license appear on all copies.

        This Specification is supplied 'as is', and Alcatel 
        makes no warranty, either express or implied, as to the use, 
        operation, condition, or performance of the Specification."
--
--  Revision History
--
        REVISION        "0801010000Z"
        DESCRIPTION     "Rev 6.0                01 Jan 2008 00:00
                         6.0 release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0701010000Z"
        DESCRIPTION     "Rev 5.0                01 Jan 2007 00:00
                         5.0 release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0603090000Z"   
        DESCRIPTION     "Rev 4.0                09 Mar 2006 00:00
                         4.0 release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0508310000Z"   
        DESCRIPTION     "Rev 3.0                31 Aug 2005 00:00
                         3.0 release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0501240000Z"   
        DESCRIPTION     "Rev 2.1                24 Jan 2005 00:00
                         2.1 release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0401150000Z"
        DESCRIPTION     "Rev 2.0                15 Jan 2004 00:00
                         2.0 release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0308150000Z"
        DESCRIPTION     "Rev 1.2                15 Aug 2003 00:00
                         1.2 release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0301200000Z"
        DESCRIPTION     "Rev 1.0                20 Jan 2003 00:00
                         1.0 Release of the TIMETRA-OAM-TEST-MIB."

        REVISION        "0111150000Z"
        DESCRIPTION     "Rev 0.1                15 Nov 2001 00:00
                         Initial version of the TIMETRA-OAM-TEST-MIB."

        ::= { timetraSRMIBModules 11 }


tmnxOamTestObjs         OBJECT IDENTIFIER ::= { tmnxSRObjs 11 }

    tmnxOamPingObjs                     OBJECT IDENTIFIER 
        ::= { tmnxOamTestObjs 1 }
    tmnxOamPingNotificationObjects      OBJECT IDENTIFIER 
        ::= { tmnxOamPingObjs 1 }

    tmnxOamTraceRouteObjs               OBJECT IDENTIFIER 
        ::= { tmnxOamTestObjs 2 }
    tmnxOamTraceRouteNotifyObjects      OBJECT IDENTIFIER 
        ::= { tmnxOamTraceRouteObjs 1 }

    tmnxOamSaaObjs                      OBJECT IDENTIFIER 
        ::= { tmnxOamTestObjs 3 }
    tmnxOamSaaNotifyObjects             OBJECT IDENTIFIER 
        ::= { tmnxOamSaaObjs 1 }

tmnxOamTestNotifications        OBJECT IDENTIFIER ::= { tmnxSRNotifyPrefix 11 }
    tmnxOamPingNotifyPrefix            OBJECT IDENTIFIER 
        ::= { tmnxOamTestNotifications 1 }
        tmnxOamPingNotifications            OBJECT IDENTIFIER 
           ::= { tmnxOamPingNotifyPrefix 0}
    tmnxOamTraceRouteNotifyPrefix      OBJECT IDENTIFIER 
        ::= { tmnxOamTestNotifications 2 }
        tmnxOamTraceRouteNotifications      OBJECT IDENTIFIER 
            ::= { tmnxOamTraceRouteNotifyPrefix 0}
    tmnxOamSaaNotifyPrefix             OBJECT IDENTIFIER 
        ::= { tmnxOamTestNotifications 3}
        tmnxOamSaaNotifications             OBJECT IDENTIFIER 
            ::= { tmnxOamSaaNotifyPrefix 0}
 
tmnxOamTestConformance          OBJECT IDENTIFIER ::= { tmnxSRConfs 11 }
    tmnxOamPingConformance              OBJECT IDENTIFIER 
        ::= { tmnxOamTestConformance 1 }
    tmnxOamTraceRouteConformance        OBJECT IDENTIFIER 
        ::= { tmnxOamTestConformance 2 }
    tmnxOamSaaConformance        OBJECT IDENTIFIER 
        ::= { tmnxOamTestConformance 3 }
 
--
-- Textual Conventions
--

TmnxOamTestMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The value of TmnxOamTestMode is an enumerated integer
         that indicates the type of OAM test."
    SYNTAX      INTEGER {
                    notConfigured (0),
                    ping (1),
                    traceroute (2)
                }


TmnxOamPingRtnCode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The value of TmnxOamPingRtnCode is an enumerated integer
         that indicates the return code received in the OAM ping response
         common header.
            notApplicable       The return code has no significance in the 
                                context of the test being conducted.
            fecEgress           Replying router is an egress for the FEC.
            fecNoMap            Replying router has no mapping for the FEC.
            notDownstream       Replying router is not one of the 
                                downstream routers.
            downstream          Replying router is one of the downstream
                                routers and its mapping for this FEC on the
                                received interface is the given label.
            downstreamNotLabel  Replying router is one of the downstream 
                                routers but its mapping for this FEC is not
                                the given label.
            downstreamNotMac    Replying router is one of the downstream
                                routers but it does not have the given MAC
                                address
            downstreamNotMacFlood   Replying router is one of the downstream
                                routers but it does not have the given MAC
                                address and is unable to flood the request.
            malformedEchoRequest    A malformed echo request was received.
            tlvNotUnderstood    One or more of the TLVs was not understood.
            downstreamNotInMfib Replying router is one of the downstream
                                routers but it does not have an MFIB entry
                                for the given source-group combination.
            downstreamMismatched    Downstream mapping mismatched.
            upstreamIfIdUnkn    Upstream interface index unknown.
            noMplsFwd           Label switched but no MPLS forwarding at
                                stack-depth.
            noLabelAtStackDepth No label entry at stack-depth.
            protoIntfMismatched Protocol not associated with interface at 
                                FEC stack-depth.
            terminatedByOneLabel    Premature termination of ping due to 
                                label stack shrinking to a single label.
         "
    SYNTAX INTEGER {
                notApplicable(0),
                fecEgress (1),
                fecNoMap (2),
                notDownstream (3),
                downstream (4),
                downstreamNotLabel (5),
                downstreamNotMac (6),
                downstreamNotMacFlood (7),
                malformedEchoRequest (8),
                tlvNotUnderstood (9),
                downstreamNotInMfib(10),
                downstreamMismatched(11),
                upstreamIfIdUnkn(12),
                noMplsFwd(13),
                noLabelAtStackDepth(14),
                protoIntfMismatched(15),
                terminatedByOneLabel(16)
            }
  
TmnxOamAddressType ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The value of TmnxOamAddressType is an enumerated integer that 
         indicates the type of address used in OAM test requests and
         responses."
    SYNTAX  INTEGER {
                unknown (0),
                ipv4Address (1),
                ipv6Address (2),
                macAddress (3),
                sapId (4),
                sdpId (5),
                localCpu (6),
                ipv4Unnumbered (7),
                ipv6Unnumbered (8)
            }

TmnxOamResponseStatus ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "Used to report the result of an OAM Echo probe operation:
  
         responseReceived(1) - Operation completes successfully.
         unknown(2) - Operation failed due to unknown error.
         internalError(3) - An implementation detected an error
              in its own processing that caused an operation to fail.
         maxConcurrentLimitReached(4) - The maximum number of
              concurrent active operations would have been exceeded
              if the corresponding operation was allowed.
         requestTimedOut(5) - Operation failed to receive a
              valid reply within the time limit imposed on it.
         unknownOrigSdpId(6) - Invalid or non-existent originating 
              SDP-ID.
         downOrigSdpId(7) - The originating SDP-ID is operationaly 
              down.
         requestTerminated(8) - The OAM ping test was terminated
              by the manager before reply or timeout.
         invalidOriginatorId(9) - The far-end replied with an invalid
              originator-ID error.
         invalidResponderId(10) - The far-end replied with an invalid
              responder-ID error.
         unknownRespSdpId(11) - The far-end replied with an invalid
              response SDP-ID error.
         downRespSdpId(12) - The far-end replied with down (admin or
              oper) response SDP-ID.
         invalidServiceId(13) - Invalid or non-existent Service-ID
              (svc-ping).
         invalidSdp(14) - Invalid or non-existent SDP for Service
              (svc-ping).
         downServiceSdp(15) - SDP for the Service is down.
              (svc-ping).
         noServiceEgressLabel(16) - Non-existent Service egress label.
              (svc-ping).         
         invalidHostAddress(17) - The IP address for a host
              has been determined to be invalid.  Examples of this
              are broadcast or multicast addresses. (svc-ping).
         invalidMacAddress(18) - The MAC address specified has been
              determined to be invalid. (macPing).
         invalidLspName(19) - The LSP name specified has been determined
              to be invalid. (lspPing).
         macIsLocal(20) - MAC Ping or Trace route not sent because the mac
              address is on a local SAP or CPU. (MAC-Ping/MAC-TR).
         farEndUnreachable(21) - no route to the far end of the GRE SDP
              tunnel.
         downOriginatorId(22) - The ping originator is operationally down.
         downResponderId(23) - The ping responder is operationally down.
         changedResponderId(24) - The ping responder-ID is changed.
         downOrigSvcId(25) - Service on the originator side is 
              operationally down. (svc-ping). 
         downRespSvcId(26) - Service on the responder side is
              operationally down. (svc-ping).
         noServiceIngressLabel(27) - Non-existent Service ingress label.
              (svc-ping).
         mismatchCustId(28) - Service customer ID mismatch between 
              originator and responder. (svc-ping).
         mismatchSvcType(29) - Service type mismatch between originator
              and responder. (svc-ping).
         mismatchSvcMtu(30) - Service MTU mismatch between originator
              and responder. (svc-ping).
         mismatchSvcLabel(31) - Service label mismatch between originator
              and responder. (svc-ping).
         noSdpBoundToSvc(32) - No SDP bound to the Service. (svc-ping).
         downOrigSdpBinding(33) - SDP binding is down on the Originator
              side. (sdp-ping).
         invalidLspPathName(34) - The LSP path name specified is invalid.
              (lsp-ping).
         noLspEndpointAddr(35) - No LSP Endpoint address. (lsp-ping).
         invalidLspId(36) - No active LSP path found. (lsp-ping).
         downLspPath(37) - LSP path is operationally down. (lsp-ping).
         invalidLspProtocol(38) - LSP Protocol is not supported or is 
              invalid. (lsp-ping).
         invalidLspLabel(39) - LSP label is invalid. (lsp-ping).     
         routeIsLocal(40) - The route is a local route. (vprn-ping).
         noRouteToDest(41) - There is no route to the destination.
              (vprn-ping).
         localExtranetRoute(42) - The route is a local extranet route.
              (vprn-ping).
         srcIpInBgpVpnRoute(43) - The source IP belongs to a BGP-VPN route.
              (vprn-ping).
         srcIpInvalid(44) - The source IP is invalid or there is no route
              to the source. (vprn-ping)
         bgpDaemonBusy(45) - The BGP routing daemon is busy; vprn route target
              information is not retrievable. (vprn-trace)
         mcastNotEnabled(46) - Multicast is not enabled. Multicast trace cannot
              be initiated. (mtrace)
         mTraceNoSGFlow(47) - No (*,G)/(S,G) flow on the router. Multicast 
              trace cannot be initiated. (mtrace)
         mTraceSysIpNotCfg(48) - System ip address not configured. It is used
              as the response address in the multicast trace query. (mtrace)
         noFwdEntryInMfib(49) - No forwarding entry could be found for the
              specified source and destination address in the MFIB.
              (mfib-ping)
         dnsNameNotFound(50) -  the domain name specified in the dns query 
              does not exist.
         noSocket(51) - unable to get socket. (icmp-ping).
         socketOptVprnIdFail(52) - unable to set SO_VPRNID for socket. 
              (icmp-ping).
         socketOptIfInexFail(53) -  unable to set IP_IFINDEX for socket. 
              (icmp-ping).
         socketOptNextHopFail(54) - unable to set IP_NEXT_HOP for socket. 
              (icmp-ping).
         socketOptMtuDiscFail(55) - unable to set IP_MTU_DISC for socket. 
              (icmp-ping).
         socketOptSndbufFail(56) - unable to set SO_SNDBUF for socket. 
              (icmp-ping).
         socketOptHdrincFail(57) - unable to set IP_HDRINCL for socket. 
              (icmp-ping).
         socketOptTosFail(58) - unable to set IP_TOS for socket. (icmp-ping).
         socketOptTtlFail(59) - unable to set IP_TTL for socket. (icmp-ping).
         bindSocketFail(60) - unable to bind socket. (icmp-ping).
         noRouteByIntf(61) - no route to destination via the specified 
              interface. (icmp-ping).
         noIntf(62) - no interface specified. (icmp-ping).
         noLocalIp(63) - unable to find local ip address. (icmp-ping).
         sendtoFail(64) - sendto function failed. (icmp-ping).
         rcvdWrongType(65) - received packet of wrong icmp type. (icmp-ping).
         noDirectInterface(66) - no direct interface to reach destination. 
              (icmp-ping).
         nexthopUnreachable (67) - unable to reach the next-hop. (icmp-ping).
         socketOptHwTimeStampFail (68) - unable to set IP_TIM_TIME for socket.  
              (icmp-ping).
         noSpokeSdpInVll (69) - unable to find spoke-sdp given SdpId:vc-id
                                (vccv-ping).
         farEndVccvNotSupported (70) - far end does not support the VCCV 
                                       options (vccv-ping).
         noVcEgressLabel (71) - no Vc egress label to send vccv-ping.
         socketOptIpSessionFail (72) - unable to set IP_SESSION for socket.
              (icmp-ping).
         rcvdWrongSize(73) - received packet of wrong size. (icmp-ping).
         dnsLookupFail (74) - dns lookup failed. (icmp-ping).
         noIpv6SrcAddrOnIntf (75) - no ipv6 source on the interface (icmp-ping).
         multipathNotSupported (76) - downstream node does not support 
                                      multipath (lsp-trace).
         nhIntfNameNotFound (77) - Given next-hop interface name not found
                                      (lsp-ping/trace).
         msPwInvalidReplyMode (78) - MS-PW switching node supports ip-routed
                                     reply mode only (vccv-ping).
         ancpNoAncpString (79) - ANCP string unknown to the system
         ancpNoSubscriber (80) - subscriber unknown to the system
         ancpNoAncpStringForSubscriber (81) - subscriber has no associated
                                              ANCP string.
         ancpNoAccessNodeforAncpString (82) - no access node is found for the
                                              given ANCP string
         ancpNoAncpCapabilityNegotiated (83) - ANCP capability not 
                                           negotiated with the involved
                                           DSLAM.
         ancpOtherTestInProgress (84) - another ANCP test is running
                                        for this ANCP string.           
         ancpMaxNbrAncpTestsInProgress (85) - max number of concurrent
                                                  ANCP tests reached.
         spokeSdpOperDown (86) - Spoke-sdp is operationally down (vccv-ping).
         noMsPwVccvInReplyDir (87) - Switching node in MS-PW with no vccv 
                                    support in echo reply direction.
         "
    SYNTAX INTEGER {
                 responseReceived(1),
                 unknown(2),
                 internalError(3),
                 maxConcurrentLimitReached(4),
                 requestTimedOut(5),
                 unknownOrigSdpId(6),
                 downOrigSdpId(7),
                 requestTerminated(8),
                 invalidOriginatorId(9),
                 invalidResponderId(10),
                 unknownRespSdpId(11),
                 downRespSdpId(12),
                 invalidServiceId(13),
                 invalidSdp(14),
                 downServiceSdp(15),
                 noServiceEgressLabel(16),
                 invalidHostAddress(17),
                 invalidMacAddress(18),
                 invalidLspName(19),
                 macIsLocal(20),
                 farEndUnreachable(21),
                 downOriginatorId(22),
                 downResponderId(23),
                 changedResponderId(24),
                 downOrigSvcId(25),
                 downRespSvcId(26),
                 noServiceIngressLabel(27),
                 mismatchCustId(28),
                 mismatchSvcType(29),
                 mismatchSvcMtu(30),
                 mismatchSvcLabel(31),
                 noSdpBoundToSvc(32),
                 downOrigSdpBinding(33),
                 invalidLspPathName(34),
                 noLspEndpointAddr(35),
                 invalidLspId(36),
                 downLspPath(37),
                 invalidLspProtocol(38),
                 invalidLspLabel(39),
                 routeIsLocal(40),
                 noRouteToDest(41),
                 localExtranetRoute(42),
                 srcIpInBgpVpnRoute(43),
                 srcIpInvalid(44),
                 bgpDaemonBusy(45),
                 mcastNotEnabled(46),
                 mTraceNoSGFlow(47),
                 mTraceSysIpNotCfg(48),
                 noFwdEntryInMfib(49),
                 dnsNameNotFound(50),
                 noSocket(51),
                 socketOptVprnIdFail(52),
                 socketOptIfInexFail(53),
                 socketOptNextHopFail(54),
                 socketOptMtuDiscFail(55),
                 socketOptSndbufFail(56),
                 socketOptHdrincFail(57),
                 socketOptTosFail(58),
                 socketOptTtlFail(59),
                 bindSocketFail(60),
                 noRouteByIntf(61),
                 noIntf(62),
                 noLocalIp(63),
                 sendtoFail(64),
                 rcvdWrongType(65),
                 noDirectInterface(66),
                 nexthopUnreachable (67),
                 socketOptHwTimeStampFail(68),
                 noSpokeSdpInVll(69),
                 farEndVccvNotSupported(70),
                 noVcEgressLabel(71),
                 socketOptIpSessionFail(72),
                 rcvdWrongSize(73),
                 dnsLookupFail (74),
                 noIpv6SrcAddrOnIntf (75),
                 multipathNotSupported (76),
                 nhIntfNameNotFound (77),
                 msPwInvalidReplyMode (78),
                 ancpNoAncpString (79),
                 ancpNoSubscriber (80),
                 ancpNoAncpStringForSubscriber (81),
                 ancpNoAccessNodeforAncpString (82),
                 ancpNoAncpCapabilityNegotiated (83),
                 ancpOtherTestInProgress (84),
                 ancpMaxNbrAncpTestsInProgress (85),
                 spokeSdpOperDown (86),
                 noMsPwVccvInReplyDir (87)
           }                                            

TmnxOamSignalProtocol ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The value of TmnxOamSignalProtocol is an enumerated integer that 
         indicates the type of label signaling protocol used by a
         router in a specific L2 mapping entry."
    SYNTAX  INTEGER {
                unknown (0),
                static  (1),
                bgp     (2),
                ldp     (3),
                rsvpTe  (4),
                crLdp   (5)
            }
  
TmnxOamTestResponsePlane ::= TEXTUAL-CONVENTION
    STATUS  current
    DESCRIPTION
        "The value of TmnxOamTestResponsePlane is an enumerated integer
         that indicates the respone plane from which the OAM ping or
         traceroute response was received."
    SYNTAX  INTEGER {
                controlPlane (1),
                dataPlane    (2),
                none         (3)
            }            
  
TmnxOamSaaThreshold ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The value of TmnxOamSaaThreshold is a enumerated integer that 
         indicates which type of thresholds should be monitored upon 
         completion of an OAM ping test run. When the corresponding
         threshold is crossed, a tmnxOamSaaThreshold notification will be 
         generated.

         noThreshold(0) No threshold type is configured.
         inJitter   (1) Monitor the value of jitter calculated for the 
                        inbound, one-way, OAM ping responses received 
                        for an OAM ping test run.
         outJitter  (2) Monitor the value of jitter calculated for the 
                        outbound, one-way, OAM ping requests sent for an 
                        OAM ping test run.
         rtJitter   (3) Monitor the value of jitter calculated for the
                        round trip, two-way, OAM ping requests and replies 
                        for an OAM ping test run.
         inLoss     (4) Monitor the number of inbound OAM ping responses not
                        received for an OAM ping test run.
         outLoss    (5) Monitor the number of outbound OAM ping requests that
                        could not be sent for an OAM ping test run.
         rtLoss     (6) Monitor the amount of packet loss for a round-trip
                        OAM ping test run.
         inLatency  (7) Monitor the average amount of latency of inbound 
                        OAM ping responses for an OAM ping test run.
         outLatency (8) Monitor the average amount of latency of outbound
                        OAM ping requests for an OAM ping test run.
         rtLatency  (9) Monitor the average amount of round-trip latency
                        for an OAM ping test run.
        "
    SYNTAX      INTEGER {
                    noThreshold (0),
                    inJitter    (1),
                    outJitter   (2),
                    rtJitter    (3),
                    inLoss      (4),
                    outLoss     (5),
                    rtLoss      (6),
                    inLatency   (7),
                    outLatency  (8),
                    rtLatency   (9)
                }


TmnxOamVcType ::= TEXTUAL-CONVENTION
    STATUS          current
    DESCRIPTION     
        "The value of TmnxOamVcType is an enumerated integer that
         indicates the type of SDP binding, mesh or spoke,  associated 
         with a VC ID.
         
         NOTE: In releases of this mib prior to R4.0, the textual convention
         TmnxVcType was used to indicate either a mesh, 'ethernet (5)',
         or spoke, 'vpls (11)', type of SDP binding for a VC ID.  In 
         release 4.0, the TmnxVcType enumeration 'vpls (11)' is changed to 
         'ipipe (11)' and can no longer can be used to represent a spoke SDP 
         binding.  This new enumerated textual convention, TmnxOamVcType, has 
         been created for use in this mib.  The same enumerated values used in
         previous releases are still used to indicate a mesh or spoke VC ID."
    SYNTAX          INTEGER {
                        meshSdp (5),
                        spokeSdp (11)
                    }


TmnxOamLTtraceDisStatusBits ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The value of TmnxOamLTtraceDisStatusBits is an enumerated integer 
         that describes the LDP tree trace discovery status in BITS. This 
         indicates different reason values when the tree discovery 
         of an IP Address FEC is not completely successful.
            timeout (0)        One or more trace requests for an IP Address  
                              FEC were timedout.
            maxPath (1)        reached the maximum allowed path limit 
                              for an IP Address FEC.
            maxHop (2)         reached the maximum allowed hop limit.
            unexploredPath (3) could not discover all possible paths.
            noResource (4)     no more internal resource to complete
                              the discovery.  
         "
    SYNTAX BITS {
                timeout (0),
                maxPath (1),
                maxHop (2),
                unexploredPath (3),
                noResource (4)
            }
--
--  Alcatel 7x50 SR series OAM Ping General Objects
--

tmnxOamPingMaxConcurrentTests   OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "tests"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The maximum number of concurrent active OAM ping tests
         that are allowed within an agent implementation.  A value of
         zero (0) for this object implies that there is no limit for
         the number of concurrent active tests in effect."
    DEFVAL { 0 }
    ::= { tmnxOamPingObjs 2 }
    
--
--  Alcatel 7x50 SR series OAM Ping Control Table
--
tmnxOamPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM ping Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7x50 SR OAM ping test 
         operations.  The results of these tests are stored in the 
         tmnxOamPingResultsTable and the tmnxOamPingHistoryTable."
   ::= { tmnxOamPingObjs 3 }

tmnxOamPingCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamPingCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamPingCtlTable 1 }

TmnxOamPingCtlEntry ::=
    SEQUENCE {
        tmnxOamPingCtlOwnerIndex             SnmpAdminString,
        tmnxOamPingCtlTestIndex              SnmpAdminString,
        tmnxOamPingCtlRowStatus              RowStatus,
        tmnxOamPingCtlStorageType            StorageType,
        tmnxOamPingCtlDescr                  SnmpAdminString,
        tmnxOamPingCtlTestMode               INTEGER,
        tmnxOamPingCtlAdminStatus            INTEGER,
        tmnxOamPingCtlOrigSdpId              SdpId,
        tmnxOamPingCtlRespSdpId              SdpId,
        tmnxOamPingCtlFcName                 TFCName,
        tmnxOamPingCtlProfile                TProfile,
        tmnxOamPingCtlMtuStartSize           Unsigned32,
        tmnxOamPingCtlMtuEndSize             Unsigned32,
        tmnxOamPingCtlMtuStepSize            Unsigned32,       
--        tmnxOamPingCtlTargetAddressType      InetAddressType,
--        tmnxOamPingCtlTargetAddress          InetAddress,
        tmnxOamPingCtlTargetIpAddress        IpAddress,
        tmnxOamPingCtlServiceId              TmnxServId,
        tmnxOamPingCtlLocalSdp               TruthValue,
        tmnxOamPingCtlRemoteSdp              TruthValue,
        tmnxOamPingCtlSize                   Unsigned32,
        tmnxOamPingCtlTimeOut                Unsigned32,
        tmnxOamPingCtlProbeCount             Unsigned32,
        tmnxOamPingCtlInterval               Unsigned32,
        tmnxOamPingCtlMaxRows                Unsigned32,
        tmnxOamPingCtlTrapGeneration         BITS,
        tmnxOamPingCtlTrapProbeFailureFilter Unsigned32,
        tmnxOamPingCtlTrapTestFailureFilter  Unsigned32,
        tmnxOamPingCtlSAA                    TruthValue,
        tmnxOamPingCtlRuns                   Counter32,
        tmnxOamPingCtlFailures               Counter32,
        tmnxOamPingCtlLastRunResult          INTEGER,
        tmnxOamPingCtlLastChanged            TimeStamp,
        tmnxOamPingCtlVRtrID                 TmnxVRtrID,
        tmnxOamPingCtlTgtAddrType            InetAddressType,
        tmnxOamPingCtlTgtAddress             InetAddress,
        tmnxOamPingCtlSrcAddrType            InetAddressType,
        tmnxOamPingCtlSrcAddress             InetAddress,
        tmnxOamPingCtlDnsName                OCTET STRING,
        tmnxOamPingCtlDNSRecord              INTEGER
    }

tmnxOamPingCtlOwnerIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(1..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "To facilitate the provisioning of access control by a
        security administrator using the View-Based Access
        Control Model (RFC 2575, VACM) for tables in which
        multiple users may need to independently create or
        modify entries, the initial index is used as an 'owner
        index'.  Such an initial index has a syntax of
        SnmpAdminString, and can thus be trivially mapped to a
        security name or group name as defined in VACM, in
        accordance with a security policy.
 
        When used in conjunction with such a security policy all
        entries in the table belonging to a particular user (or
        group) will have the same value for this initial index.
        For a given user's entries in a particular table, the
        object identifiers for the information in these entries
        will have the same subidentifiers (except for the 'column'
        subidentifier) up to the end of the encoded owner index.
        To configure VACM to permit access to this portion of the
        table, one would create vacmViewTreeFamilyTable entries
        with the value of vacmViewTreeFamilySubtree including
        the owner index portion, and vacmViewTreeFamilyMask
        'wildcarding' the column subidentifier.  More elaborate
        configurations are possible."
    ::= { tmnxOamPingCtlEntry 1 }
 
tmnxOamPingCtlTestIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(1..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The test name index of the Alcatel 7x50 SR OAM ping test.  
         This is locally unique, within the scope of an 
         tmnxOamPingCtlOwnerIndex."
    ::= { tmnxOamPingCtlEntry 2 }

tmnxOamPingCtlRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object allows entries to be created and deleted
         in the tmnxOamPingCtlTable.  Deletion of an entry in this
         table results in all corresponding (same
         tmnxOamPingCtlOwnerIndex and tmnxOamPingCtlTestIndex index 
         values) tmnxOamPingResultsTable and tmnxOamPingHistoryTable 
         entries being deleted.
 
         Activation of a Alcatel 7x50 SR OAM ping operation is controlled
         via tmnxOamPingCtlAdminStatus and not by changing
         this object's value to active(1).
 
         The values for configuration objects required for the type of
         test specified in tmnxOamPingCtlTestMode MUST be specified
         prior to a transition to active(1) state being
         accepted.
 
         Transitions in and out of active(1) state are not allowed while 
         an entry's tmnxOamPingResultsOperStatus is active(1) with the 
         exception that deletion of an entry in this table by setting 
         its RowStatus object to destroy(6) will stop an active Alcatel 
         7x50 SR OAM ping operation.
 
         The operational state of an Alcatel 7x50 SR OAM ping operation
         can be determined by examination of its
         tmnxOamPingResultsOperStatus object."
    REFERENCE
        "See definition of RowStatus in RFC 2579, 'Textual
        Conventions for SMIv2.'"
    ::= { tmnxOamPingCtlEntry 3 }
 
tmnxOamPingCtlStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The storage type for this conceptual row.
         Conceptual rows having the value 'permanent' need not
         allow write-access to any columnar objects in the row."
    DEFVAL { volatile }
    ::= { tmnxOamPingCtlEntry 4 }

tmnxOamPingCtlDescr OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The purpose of this object is to provide a
         descriptive name of the OAM ping test."
    DEFVAL      { ''H }   -- the empty string
    ::= { tmnxOamPingCtlEntry 5 }

tmnxOamPingCtlTestMode     OBJECT-TYPE
    SYNTAX      INTEGER {
                    sdpPing (1),
                    mtuPing (2),
                    svcPing (3),
                    macQuery (4),
                    macPing (5),
                    macPopulate (6),
                    macPurge (7),
                    lspPing (8),
                    vprnPing (9),
                    atmPing (10),
                    mfibPing (11),
                    cpePing (12),
                    mrInfo (13),
                    vccvPing (14),
                    icmpPing (15),
                    dnsPing (16),
                    ancpLoopback (17)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the type of OAM ping test defined by this entry.
         The configuration parameters unique to a specific test type are to 
         be found in an sparsely dependent table extension for that test type.
           
         sdpPing -  tests SDP connectivity and round trip time.
         
         mtuPing -  tests MTU path size.
         
         svcPing -  determines the existence and operative state of the 
                    service ID on the far end ESR.
                    NOTE: This is a one-shot test.
                    
         macQuery - determines a specific address mapping for the service ID.
                    See tmnxOamMacPingCtlTable for additional parameters.
                    NOTE: This is a one-shot test.
         
         macPing -  determines address mappings for the service ID.
                    See tmnxOamMacPingCtlTable for additional parameters.
 
         macPopulate - populates an OAM MAC address into the FIBs.
                    See tmnxOamMacPingCtlTable for additional parameters.
                    NOTE: This is a one-shot test.  
 
         macPurge - deletes an OAM MAC address from the FIBs.
                    See tmnxOamMacPingCtlTable for additional parameters.
                    NOTE: This is a one-shot test.  
 
         lspPing -  tests LSP path connectivity and round trip time.    
                    See tmnxOamLspPingCtlTable for additional parameters.
                    
         vprnPing - tests IP path connectivity and round trip time within 
                    a specified VPRN service.  See tmnxOamVprnPingCtlTable
                    for additional parameters.
                    
         atmPing  - tests ATM path connectivity and round trip time on an 
                    ATM VCC.  See tmnxOamAtmPingCtlTable for additional
                    parameters.
 
         mfibPing - tests IP multicast connectivity and round trip time
                    within a specified VPLS service.  
                    See tmnxOamMfibPingCtlTable for additional parameters.
 
         cpePing  - determines IP connectivity to a CPE
                    within a specified VPLS service.  
                    See tmnxOamCpePingCtlTable for additional parameters.
 
         mrInfo   - Get multicast router's capabilities and the list of
                    interfaces with neighbors.

        vccvPing  - tests pseudowire connectivity and round trip time.
                    See tmnxOamVccvPingCtlTable for additional parameters.

        icmpPing  - tests IP connectivity and round trip time.
                    See tmnxOamIcmpPingCtlTable for additional parameters.
                    
        dnsPing   - tests DNS name resolution connectivity and round trip time.

        ancpLoopback - send an OAM loopback test request to the access node.
                       see tmnxOamAncpTestCtlTable for additional parameters.
        "
    DEFVAL { sdpPing }
    ::= { tmnxOamPingCtlEntry 6 }
 
tmnxOamPingCtlAdminStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                          enabled(1), -- test should be started
                          disabled(2) -- test should be stopped
                        }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Reflects the desired state that a tmnxOamPingCtlEntry should be
         in:
 
           enabled(1)  - Attempt to activate the test as defined by
                         this tmnxOamPingCtlEntry.
           disabled(2) - Deactivate the test as defined by this
                         tmnxOamPingCtlEntry.
 
         An attempt to set tmnxOamPingCtlAdminStatus to 'enabled' will fail
         if the parameters required by a particular OAM ping test mode have
         not been previously set.  Upon completion of an OAM ping test,
         the agent will reset the value of this object to 'disabled'.
         When this object has the value 'enabled' an attempt to modify
         any of the test parameters will fail with an inconsistentValue
         error.  
         
         If tmnxOamPingCtlTestMode equals either 
         'sdpPing' or 'mtuPing', tmnxOamPingCtlOrigSdpId must have already
         been set.  
         
         If tmnxOamPingCtlTestMode equals 'mtuPing',
         tmnxOamPingCtlMtuStartSize and tmnxOamPingCtlMtuEndSize must also
         have already been set.  
         
         If tmnxOamPingCtlTestMode equals 'svcPing',
         tmnxOamPingCtlTgtAddress and tmnxOamPingCtlServiceId must have
         already been set.

         If tmnxOamPingCtlTestMode equals 'cpePing',
         tmnxOamPingCtlTgtAddress, and tmnxOamCpePingCtlSourceIpAddr
         are required.

         If tmnxOamPingCtlTestMode equals 'icmpPing', tmnxOamPingCtlTgtAddress 
         must have already been set.

         If tmnxOamPingCtlTestMode equals 'dnsPing', tmnxOamPingCtlTgtAddress 
         must have already been set.

         If tmnxOamPingCtlSAA has the value 'true' and tmnxOamSaaCtlAdminStatus
         has a value 'outOfService', an attempt to set this object to 'enabled'
         will fail with an inconsistentValue error.

         Refer to the corresponding tmnxOamPingResultsOperStatus to determine 
         the operational state of the test defined by this entry."
    DEFVAL { disabled }
    ::= { tmnxOamPingCtlEntry 7 }
 
tmnxOamPingCtlOrigSdpId    OBJECT-TYPE
    SYNTAX      SdpId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the originating SDP-ID to be used for performing
         a spd-ping or mtu-ping operation.  This parameter is required
         only if tmnxOamPingCtlTestMode has a value of either 'sdpPing'
         or 'mtuPing'.
                  
         The far-end address of the specified SPD-ID is the expected
         responder-id within each OAM reply message received.  The
         specified SPD-ID defines the encapsulation of the SDP tunnel
         encapsulation used to reach the far-end.  This can be IP/GRE
         or MPLS.  The value of tmnxOamPingCtlFcName is used to define
         the outgoing forwarding class used for the SDP encapsulation.
         
         If the value of tmnxOamPingCtlOrigSdpId is invalid, or the SDP
         is administratively down, or unavailable, the OAM Echo request
         message probe is not sent and an appropriate error value is
         written to tmnxOamPingHistoryStatus for that probe entry.  Once
         the interval timer expires, the next probe attempt will be made
         if required."
    DEFVAL { 0 } -- invalid Sdp-ID
    ::= { tmnxOamPingCtlEntry 8 }
 
tmnxOamPingCtlRespSdpId    OBJECT-TYPE
    SYNTAX      SdpId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the return SDP-ID to be used by the far-end node for
         its message reply.  This parameter is optional and is valid only if
         tmnxOamPingCtlTestMode has a value of 'sdpPing'.  
         
         If this SDP-ID does not exist on the far-end, terminates on another 
         node different than the originating node, or some other issue 
         prevents the far-end from using the specified SDP-ID, the OAM Echo 
         message reply is sent using generic IP/GRE OAM encapsulation.  The 
         received forwarding class (as mapped on the ingress network interface 
         for the far-end) defines the forwarding class encapsulation for the 
         reply message."
    DEFVAL { 0 }    -- invalid SDP-ID
    ::= { tmnxOamPingCtlEntry 9 }
 
tmnxOamPingCtlFcName       OBJECT-TYPE
    SYNTAX      TFCName
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlFcName specifies the forwarding class.
         This parameter is optional and is valid only if tmnxOamPingCtlTestMode 
         has a value of 'sdpPing', 'macPing', 'lspPing' or 'vccvPing.   
         For 'sdpPing' or 'macPing' this is the forwarding class of the 
         SDP encapsulation. For 'lspPing' this is the forwarding class of the 
         LSP tunnel. For 'vccvPing' this is the forwarding class of the 
         pseudowire.
        
         The forwarding class name must be one of those defined in the
         tFCNameTable in ALCATEL-IND1-TIMETRA-QOS-MIB.  The agent creates predefined
         entries in the tFCNameTable for 'premium', 'assured', and 'be'
         (for best-effort) forwarding classes.  The actual forwarding
         class encoding is controlled by the network egress DSCP or
         LSP-EXP mappings."  
    DEFVAL { "be" }
    ::= { tmnxOamPingCtlEntry 10 }
 
tmnxOamPingCtlProfile      OBJECT-TYPE
    SYNTAX      TProfile
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the profile value to be used with the forwarding
         class specified in tmnxOamPingCtlFcName.  This parameter is optional 
         and is valid only if tmnxOamPingCtlTestMode has a value of 
         'vccvPing', 'sdpPing', 'macPing' or 'lspPing'.
         
         The profile value must be consistent with the specified forwarding 
         class:
            'assured' = 'in' or 'out'
            'premium' = 'in'
            'be' = 'out' "
     DEFVAL { out }
     ::= { tmnxOamPingCtlEntry 11 }
 
tmnxOamPingCtlMtuStartSize     OBJECT-TYPE
    SYNTAX      Unsigned32 (0|40..9197)
    UNITS       "Octets"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the size of the first OAM Echo message sent
         when tmnxOamPingCtlTestMode is set equal to 'mtuPing'.
         This parameter is required for 'mtuPing'.  An attempt to
         set tmnxOamPingCtlAdminStatus to 'enabled' to start an 
         'mtuPing' test will fail if this object has not been 
         explicitly set.
         
         A value of 0 is returned for this object if it has not
         been explicitly set.  An attempt to set this object to a
         value of 0 will fail with a wrongValue error."
    ::= { tmnxOamPingCtlEntry 12 }
 
tmnxOamPingCtlMtuEndSize       OBJECT-TYPE
    SYNTAX      Unsigned32 (0|41..9198)
    UNITS       "Octets"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the size of the last OAM Echo message sent
         when tmnxOamPingCtlTestMode is set equal to 'mtuPing'.
         Its value must be greater than the value of
         tmnxOamPingCtlMtuStartSize.  This parameter is required
         for 'mtuPing'.  An attempt to set tmnxOamPingCtlAdminStatus
         to 'enabled to start an 'mtuPing' will fail if this object 
         has not been explicitly set.
         
         A value of 0 is returned for this object if it has not
         been explicitly set.  An attempt to set this object to a
         value of 0 will fail with a wrongValue error."
    ::= { tmnxOamPingCtlEntry 13 }
    
tmnxOamPingCtlMtuStepSize      OBJECT-TYPE
    SYNTAX      Unsigned32 (1..512)
    UNITS       "Octets"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the number of octets by which to increment the
         OAM Echo message request size for each message request sent
         when tmnxOamPingCtlTestMode is set equal to 'mtuPing'.  If the
         next incremented message size is greater than 
         tmnxOamPingCtlMtuEndSize, the last message has already been
         sent.  The next size message is not sent until a reply is
         received or three messages have timed out at the current
         size.

         This parameter is optional."
    DEFVAL { 32 }
    ::= { tmnxOamPingCtlEntry 14 } 
 
tmnxOamPingCtlTargetIpAddress  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 address to be used as the destination for
         performing an OAM ping operation when tmnxOamPingCtlTestMode has
         a value of 'svcPing', 'macQuery' or 'vprnPing'.   This parameter 
         is required for 'svcPing', 'macQuery', 'vprnPing', 'cpePing' and 
         'mrinfo'. 
         
         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamPingCtlTgtAddrType and 
         tmnxOamPingCtlTgtAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamPingCtlEntry 16 }
 
tmnxOamPingCtlServiceId        OBJECT-TYPE
    SYNTAX      TmnxServId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the Service-ID of the service being tested when the value
         of tmnxOamPingCtlTestMode is equal to 'svcPing', 'macPopulate', 
         'macPurge', 'macQuery', 'macPing', 'vprnPing', 'mfibPing', or
         'cpePing'.
         
         Except for 'vprnPing' the Service-ID need not exist on the local 
         node in order to receive a reply message if the far-end target 
         IP address is specified in tmnxOamPingCtlTgtAddress.  
         
         This parameter is required for 'svcPing' , 'macPopulate', 
         'macPurge', 'macQuery', 'macPing', 'vprnPing', 'mfibPing'
         and 'cpePing'."
    DEFVAL { 0 } -- invalid Service-ID
    ::= { tmnxOamPingCtlEntry 17 }

tmnxOamPingCtlLocalSdp     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamPingCtlLocalSdp is 'true', the 'svcPing' test
         determines an egress SDP-ID that is bound to the service that has
         the far-end IP address specified in tmnxOamPingCtlTgtAddress
         assigned to it.  The far-end address of the specified SPD-ID is
         the expected responder-id within the OAM Echo reply message.  The
         SDP-ID defines the encapsulation of the SDP tunnel encapsulation
         used to reach the far-end.  This can be IP/GRE or MPLS.  On the
         originator egress, the Service-ID must have an associated VC-Label
         to reach the far-end address of the SDP-ID and the SDP-ID must be
         operational for the message to be sent.
         This object is optional and valid only when tmnxOamPingCtlTestMode is 
         equal to 'svcPing'."
    DEFVAL { false }
    ::= { tmnxOamPingCtlEntry 18 }

tmnxOamPingCtlRemoteSdp        OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamPingCtlRemoteSdp is 'false', the far-end
         node must use the generic IP/GRE OAM encapsulation as the return
         path in a 'svcPing' test.
         
         This object is optional and valid only when tmnxOamPingCtlTestMode is 
         equal to 'svcPing'."
    DEFVAL { false }
    ::= { tmnxOamPingCtlEntry 19 }

 tmnxOamPingCtlSize OBJECT-TYPE
--    SYNTAX      Unsigned32 (40..9198)
    SYNTAX      Unsigned32 (0..16384)
    UNITS       "octets"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlSize specifies the size of the OAM Echo 
         message.  
        
         When tmnxOamPingCtlTestMode has a value of 'mtuPing', changing the
         message size is a method of checking the ability of an SDP
         to support a particular path-mtu value.  The size of the message
         does not include the SDP encapsulation, VC-Lable (if applied)
         or any DLC headers or trailers.
         
         When the OAM message is encapsulated in an IP/GRE SDP, the
         IP 'DF' (Don't Fragment) bit is set.  If any segment of the path
         between the sender and receiver cannot handle the message size,
         the message is discarded.  MPLS LSPs are not expected to fragment
         the message either, as the message contained in the LSP is not an
         IP packet.         

         In the case of 'lspPing' the minimum size allowed is 84 octets.  
         In the case of 'vccvPing' the minimum size allowed is 88 octets.
         In the case of 'sdpPing' the minimum size allowed is 72 octets. 
         Before 6.0 release, the minimum size in 'sdpPing' is 40 octets. 
         Such shorter packet has no timestamp information but should still 
         be accepted for interoperability purpose. However, new 'sdpPing' 
         should include the timestamp information.
         In the case of 'icmpPing' the minimum size allowed is 0 octet.
         In the case of 'macPing', 'mfibPing' and 'vprnPing' the minimum
         size allowed is 1 octet.

         In the case of all the test types except 'icmpPing' the maximum
         size allowed is 9198 octets. In the case of 'icmpPing' the maximum
         size allowed is 16384 octets.

         When tmnxOamPingCtlTestMode has a value of 'icmpPing', the value
         of this object specifies the payload size following the ICMP
         header.  The default size for 'icmpPing' is 56 octets.

         This parameter is optional and is valid only if tmnxOamPingCtlTestMode
         has a value of either 'sdpPing' ,'mtuPing', 'macPing', 'lspPing',
         'vprnPing', 'mfibPing', 'vccvPing' or 'icmpPing'."
    DEFVAL { 72 }
    ::= { tmnxOamPingCtlEntry 20 }

tmnxOamPingCtlTimeOut OBJECT-TYPE
    SYNTAX      Unsigned32 (1..10)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlTimeOut specifies the time-out value, 
         in seconds, to wait for an OAM Echo message reply.  Upon expiration 
         of the timeout period, the agent assumes that the message response 
         will not be received.  An appropriate error value is written to 
         tmnxOamPingHistoryStatus for the timed out probe entry.  Any response 
         received after the timeout period has expired is silently discarded.

         This parameter is optional and is valid only if tmnxOamPingCtlTestMode
         has a value of either 'sdpPing', 'mtuPing', 'macPing', 'lspPing', 
         'vprnPing', 'atmPing', 'mfibPing', 'vccvPing', 'icmpPing' or 
         'dnsPing'."
    DEFVAL { 5 }
    ::= { tmnxOamPingCtlEntry 21 }

-- Note that 'mtuPing' does not use a probe count
tmnxOamPingCtlProbeCount OBJECT-TYPE
    SYNTAX      Unsigned32 (1..100000)
    UNITS       "probes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the number of times to perform an OAM ping probe
         operation.  Each OAM Echo message request must either timeout 
         or receive a reply before the next message request is sent.
        
         This parameter is optional and is valid only if tmnxOamPingCtlTestMode
         does not have a value of 'mtuPing'. The maximum value for all ping
         test modes other than icmp-ping is 100. The default probe count value
         for 'icmpPing' is 5."
    DEFVAL { 1 }
    ::= { tmnxOamPingCtlEntry 22 }

tmnxOamPingCtlInterval  OBJECT-TYPE
--    SYNTAX      Unsigned32 (1..10)
    SYNTAX      Unsigned32 (1..10000)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The number of seconds to wait before sending the next OAM 
         Echo request message.  If tmnxOamPingCtlInterval has a
         value of 1 and tmnxOamPingCtlTimeOut has a value of 10 seconds,
         then the maximum time between message requests is 10 seconds and
         the minimum is 1 second.
         
         For test types other than 'icmpPing', the maximum interval is
         10 seconds.
         
         For 'icmpPing' when tmnxOamIcmpPingCtlRapid has a value of 'true',
         the UNITS for tmnxOamPingCtlInterval is '10 milliseconds' in order
         to allow rapid intervals less than 1 second to be specified."
    DEFVAL { 1 }
    ::= { tmnxOamPingCtlEntry 23 }

tmnxOamPingCtlMaxRows OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "rows"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of entries allowed in the tmnxOamPingHistoryTable
         for this OAM ping test.  The agent will remove the oldest entry in 
         the tmnxOamPingHistoryTable to allow the addition of an new entry 
         once the number of rows in the tmnxOamPingHistoryTable reaches this 
         value.

         Old entries are not removed when a new test is started.  Entries are 
         added to the tmnxOamPingHistoryTable until tmnxOamPingCtlMaxRows is 
         reached before entries begin to be removed.

         A value of 0 for this object disables creation of 
         tmnxOamPingHistoryTable entries."
    DEFVAL { 300 }
    ::= { tmnxOamPingCtlEntry 24 }

tmnxOamPingCtlTrapGeneration OBJECT-TYPE
    SYNTAX      BITS {
                   probeFailure(0),
                   testFailure(1),
                   testCompletion(2)
                  }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of this object determines when and if
         to generate a notification for this entry:

         probeFailure(0)   - Generate a tmnxOamPingProbeFailed
            notification subject to the value of
            tmnxOamPingCtlTrapProbeFailureFilter.  The object
            tmnxOamPingCtlTrapProbeFailureFilter can be used
            to specify the number of successive probe failures
            that are required before a tmnxOamPingProbeFailed
            notification can be generated.
         testFailure(1)    - Generate a tmnxOamPingTestFailed
            notification. In this instance the object
            tmnxOamPingCtlTrapTestFailureFilter can be used to
            determine the number of probe failures that
            signal when a test fails.
         testCompletion(2) - Generate a tmnxOamPingTestCompleted
            notification.

         The value of this object defaults to zero, indicating
         that none of the above options have been selected."
    DEFVAL { {} }
    ::= { tmnxOamPingCtlEntry 25 }

tmnxOamPingCtlTrapProbeFailureFilter OBJECT-TYPE
    SYNTAX      Unsigned32 (0..15)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of this object is used to determine when
         to generate a tmnxOamPingProbeFailed NOTIFICATION.

         Setting tmnxOamPingCtlTrapGeneration
         to probeFailure(0) implies that a tmnxOamPingProbeFailed
         NOTIFICATION is generated only when the number of
         successive probe failures, as indicated by the
         value of tmnxOamPingCtlTrapProbeFailureFilter, fail within
         a given ping test."
    DEFVAL { 1 }
    ::= { tmnxOamPingCtlEntry 26 }

tmnxOamPingCtlTrapTestFailureFilter OBJECT-TYPE
    SYNTAX      Unsigned32 (0..15)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of this object is used to determine when
         to generate a tmnxOamPingTestFailed NOTIFICATION.

         Setting tmnxOamPingCtlTrapGeneration to testFailure(1)
         implies that a tmnxOamPingTestFailed NOTIFICATION is
         generated only when the number of ping failures
         within a test exceed the value of
         tmnxOamPingCtlTrapTestFailureFilter."
    DEFVAL { 1 }
    ::= { tmnxOamPingCtlEntry 27 }

tmnxOamPingCtlSAA OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlSAA specifies whether or not to
         collect Service Assurance Agent, SAA, metrics such as loss,
         jitter and latency.
         
         When tmnxOamPingCtlSAA has a value of 'true', SAA metrics
         are collected.

         This parameter is optional."
    DEFVAL { false }
    ::= { tmnxOamPingCtlEntry 28 }

tmnxOamPingCtlRuns      OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlRuns indicates the number of times
         this OAM ping test has been executed."
    ::= { tmnxOamPingCtlEntry 29 }

tmnxOamPingCtlFailures  OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlFailures indicates the number of times
         this OAM ping test has failed."
    ::= { tmnxOamPingCtlEntry 30 }

tmnxOamPingCtlLastRunResult  OBJECT-TYPE
    SYNTAX      INTEGER {
                    undetermined (0),
                    success (1),
                    failed (2),
                    aborted (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlLastRunResult indicates the completion
         status the last time this test was executed.  If this OAM test is
         currently in progress, this object indicates the result of the
         previous test run, if any."
    ::= { tmnxOamPingCtlEntry 31 }

tmnxOamPingCtlLastChanged   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlLastChanged indicates the time the
         value of a settable object in this row was last changed."
    ::= { tmnxOamPingCtlEntry 32 }

tmnxOamPingCtlVRtrID  OBJECT-TYPE
    SYNTAX      TmnxVRtrID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this virtual router in the 
         Alcatel 7x50 SR system. The vRtrID value for each virtual router 
         must remain constant at least from one re-initialization of the 
         system management processor (CPM) to the next.  There will always 
         be at least one router entry defined by the agent with vRtrID=1 
         which represents the base transport router.

         This parameter is optional and is valid only if tmnxOamPingCtlTestMode
         is equal to 'mrInfo' or 'icmpPing'. If no value is specified the 
         base router ID is used."
    DEFVAL { 1 }
    ::= { tmnxOamPingCtlEntry 33 }

tmnxOamPingCtlTgtAddrType   OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the type of host address to be used as the destination
         for performing a OAM ping operation when tmnxOamPingCtlTestMode
         has a value of 'svcPing', 'macQuery', 'vprnPing', 'cpePing',
         'mrinfo', 'icmpPing' or 'vccvPing'.
 
         This object indicates the type of address stored in the 
         corresponding tmnxOamPingCtlTgtAddress object.
         
         Only 'ipv4', 'ipv6' and 'dns' address types are supported.
         The 'dns' address type is valid only for 'icmpPing'."
    DEFVAL { unknown }
    ::= { tmnxOamPingCtlEntry 34 }

tmnxOamPingCtlTgtAddress OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the IP host address to be used as the destination for
         performing an OAM ping operation and is required when 
         tmnxOamPingCtlTestMode has a value of 'svcPing', 'macQuery', 'vprnPing', 
         'cpePing', 'mrinfo', 'icmpPing', 'dnsPing' or 'vccvPing'.
         
         For 'dnsPing' this is the address of the DNS server host that
         will be asked to resolve a dns name specified by 
         tmnxOamPingCtlDnsName.  
 
         For 'vccvPing', this object must be accompanied by a valid 
         tmnxOamPingCtlSrcAddress and a valid tmnxOamVccvPingCtlPwId.
 
         The IP host address type is determined by the value of the 
         corresponding tmnxOamPingCtlTgtAddrType object."
    DEFVAL { ''H }
    ::= { tmnxOamPingCtlEntry 35 }

tmnxOamPingCtlSrcAddrType   OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the type of host address to be used as the source
         for performing a OAM ping operation when tmnxOamPingCtlTestMode
         has a value of 'svcPing', 'macQuery', 'vprnPing', 'cpePing',
         'mrinfo', 'icmpPing' or 'vccvPing'.
 
         This object indicates the type of address stored in the 
         corresponding tmnxOamPingCtlSrcAddress object.
         
         Only 'ipv4' and 'ipv6' address types are supported."
    DEFVAL { unknown }
    ::= { tmnxOamPingCtlEntry 36 }

tmnxOamPingCtlSrcAddress OBJECT-TYPE
    SYNTAX      InetAddress  (SIZE(0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the IP host address to be used as the source for
         performing an OAM ping operation when tmnxOamPingCtlTestMode has a 
         value of 'svcPing', 'macQuery', 'vprnPing', 'cpePing', 'mrinfo', 
         'icmpPing' or 'vccvPing'.  
         
         This is an optional parameter.
         
         For 'vccvPing', this object must be accompanied by a valid 
         tmnxOamPingCtlTgtAddress and a valid tmnxOamVccvPingCtlPwId.
        
         The host address type is determined by the value of the 
         corresponding tmnxOamPingCtlSrcAddrType object."
    DEFVAL { ''H }
    ::= { tmnxOamPingCtlEntry 37 }

tmnxOamPingCtlDnsName   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..255))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlDnsName specifies the DNS name to
         be resolved to an IP address.

         This object is required for 'dnsPing'."
    DEFVAL { ''H }
    ::= { tmnxOamPingCtlEntry 38 }

tmnxOamPingCtlDNSRecord  OBJECT-TYPE
    SYNTAX      INTEGER {
                    ipv4Arecord    (1),
                    ipv6AAAArecord (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingCtlDNSRecord specifies which DNS record
         is to be retrieved in this test."
    DEFVAL {ipv4Arecord }
    ::= { tmnxOamPingCtlEntry 39 }

--
-- Alcatel 7x50 SR serier OAM Ping Results Table
--
tmnxOamPingResultsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamPingResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Service Ping Results Table for providing
         the capability of performing OAM ping operations.  The 
         results of these operations are stored in the 
         tmnxOamPingResultsTable and the tmnxOamPingHistoryTable.

         An entry is added to the tmnxOamPingResultsTable when an
         tmnxOamPingCtlEntry is started by successful transition
         of its tmnxOamPingCtlAdminStatus object to enabled(1).
        
         An entry is removed from the tmnxOamPingResultsTable when
         its corresponding tmnxOamPingCtlEntry is deleted."
    ::= { tmnxOamPingObjs 4 }

tmnxOamPingResultsEntry OBJECT-TYPE
    SYNTAX      TmnxOamPingResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamPingResultsTable.  The
         tmnxOamPingResultsTable has the same indexing as the
         tmnxOamPingCtlTable in order for a tmnxOamPingResultsEntry to
         correspond to the tmnxOamPingCtlEntry that caused it to
         be created.
        
         An entry in this table summarizes results from multiple
         invocations of the test configured by the corresponding
         entry in tmnxOamPingCtlTable."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex,
             tmnxOamPingResultsTestRunIndex
          }
    ::= { tmnxOamPingResultsTable 1 }

TmnxOamPingResultsEntry ::=
    SEQUENCE {
        tmnxOamPingResultsOperStatus          INTEGER,
        tmnxOamPingResultsMinRtt              Unsigned32,
        tmnxOamPingResultsMaxRtt              Unsigned32,
        tmnxOamPingResultsAverageRtt          Unsigned32,
        tmnxOamPingResultsRttSumOfSquares     Unsigned32,
        tmnxOamPingResultsMtuResponseSize     Unsigned32,
        tmnxOamPingResultsSvcPing             INTEGER,
        tmnxOamPingResultsProbeResponses      Unsigned32,
        tmnxOamPingResultsSentProbes          Unsigned32,
        tmnxOamPingResultsLastGoodProbe       DateAndTime,
        tmnxOamPingResultsLastRespHeader      OCTET STRING,
        tmnxOamPingResultsMinTt               Integer32,
        tmnxOamPingResultsMaxTt               Integer32,
        tmnxOamPingResultsAverageTt           Integer32,
        tmnxOamPingResultsTtSumOfSquares      Integer32,
        tmnxOamPingResultsMinInTt             Integer32,
        tmnxOamPingResultsMaxInTt             Integer32,
        tmnxOamPingResultsAverageInTt         Integer32,
        tmnxOamPingResultsInTtSumOfSqrs       Integer32,
        tmnxOamPingResultsOutJitter           Integer32,
        tmnxOamPingResultsInJitter            Integer32,
        tmnxOamPingResultsRtJitter            Integer32,
        tmnxOamPingResultsProbeTimeouts       Unsigned32,
        tmnxOamPingResultsProbeFailures       Unsigned32,
        tmnxOamPingResultsTestRunIndex        Unsigned32
    }

tmnxOamPingResultsOperStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                          enabled(1),  -- test is in progress
                          disabled(2)  -- test has stopped
                        }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflects the operational state of a tmnxOamPingCtlEntry:
           enabled(1)   - Test is active.
           disabled(2)  - Test has stopped."
    ::= { tmnxOamPingResultsEntry 1 }

tmnxOamPingResultsMinRtt OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum OAM ping round-trip-time (RTT) received.  
         
         A value of 0 for this object implies that no RTT has been received.
         When tmnxOamPingCtlTestMode does not have a value of 'sdpPing', this 
         object is not relevant and should contain a value of 0."
    ::= { tmnxOamPingResultsEntry 4 }

tmnxOamPingResultsMaxRtt OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum OAM ping round-trip-time (RTT) received.  
         A value of 0 for this object implies that no RTT has been received.
         
         When tmnxOamPingCtlTestMode does not have a value of 'sdpPing', this 
         object is not relevant and should contain a value of 0."
    ::= { tmnxOamPingResultsEntry 5 }

tmnxOamPingResultsAverageRtt OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current average OAM ping round-trip-time (RTT).
         When tmnxOamPingCtlTestMode does not have a value of 'sdpPing', this 
         object is not relevant and should contain a value of 0."
    ::= { tmnxOamPingResultsEntry 6 }

tmnxOamPingResultsRttSumOfSquares OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the sum of the squares for all ping
         responses received.  Its purpose is to enable standard
         deviation calculation.  
        
         The value of this object MUST be reported as 0 when no ping responses 
         have been received.  When tmnxOamPingCtlTestMode does not have a 
         value of 'sdpPing', this object is not relevant and should contain 
         a value of 0."
    ::= { tmnxOamPingResultsEntry 7 }

tmnxOamPingResultsMtuResponseSize      OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "Octets"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the largest size OAM Echo request message
         that received a valid reply.
         The value of this object MUST be reported as 0 when no ping responses 
         have been received or an active test has not yet timed out.  When 
         tmnxOamPingCtlTestMode does not have a value of 'mtuPing', this object 
         is not relevant and should contain a value of 0."
    ::= { tmnxOamPingResultsEntry 8 }

tmnxOamPingResultsSvcPing         OBJECT-TYPE
    SYNTAX      INTEGER {
                    undetermined (0),
                    failed (1),
                    success (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The object contains the final results of an OAM 'svcPing' test.
         The value of this object MUST be reported as 0 when no ping responses 
         have been received or an active test has not yet timed out.  When 
         tmnxOamPingCtlTestMode does not have a value of 'svcPing', this object 
         is not relevant and should contain a value of 0."
    ::= { tmnxOamPingResultsEntry 9 }
 
tmnxOamPingResultsProbeResponses OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "responses"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of responses received for the corresponding
         tmnxOamPingCtlEntry and tmnxOamPingResultsEntry.  The value of 
         this object MUST be reported as 0 when no probe responses have 
         been received."
    ::= { tmnxOamPingResultsEntry 10 }

tmnxOamPingResultsSentProbes OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "probes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of this object reflects the number of probes sent
         for the corresponding tmnxOamPingCtlEntry and tmnxOamPingResultsEntry.
         The value of this object MUST be reported as 0 when no probes
         have been sent."
    ::= { tmnxOamPingResultsEntry 11 }

tmnxOamPingResultsLastGoodProbe OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Date and time when the last response was received for
         an OAM probe."
    ::= { tmnxOamPingResultsEntry 12 }

tmnxOamPingResultsLastRespHeader   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (100))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A copy of the OAM header of the last response packet received for
         this OAM ping test.  The value of this object MUST be reported
         as 0 when no probes have been sent."
    ::= { tmnxOamPingResultsEntry 13 }

tmnxOamPingResultsMinTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum OAM ping outbound one-way-trip-time received.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 14 }

tmnxOamPingResultsMaxTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum OAM ping outbound one-way-trip-time received.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 15 }

tmnxOamPingResultsAverageTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current average OAM ping outbound one-way-trip-time.
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 16 }

tmnxOamPingResultsTtSumOfSquares OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the sum of the squares for the outbound
         one-way-trip time of all ping responses received.  Its purpose 
         is to enable standard deviation calculation.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 17 }

tmnxOamPingResultsMinInTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum OAM ping inbound one-way-trip-time received.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 18 }

tmnxOamPingResultsMaxInTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum OAM ping inbound one-way-trip-time received.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 19 }

tmnxOamPingResultsAverageInTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current average OAM ping inbound one-way-trip-time.
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 20 }

tmnxOamPingResultsInTtSumOfSqrs OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the sum of the squares for the inbound
         one-way-trip time of all ping responses received.  Its purpose 
         is to enable standard deviation calculation.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 21 }

tmnxOamPingResultsOutJitter      OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingResultsOutJitter indicates the
         amount of one-way trip jitter, expressed in milliseconds, for a
         ping probe request packet sent for this OAM test.
         A value of 0 for this object implies that no one-way-trip jitter 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 22 }
    
tmnxOamPingResultsInJitter      OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingResultsInJitter indicates the
         amount of one-way-trip jitter, expressed in milliseconds, for a
         ping probe response packet received for this OAM test.
         A value of 0 for this object implies that no one-way-trip jitter 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 23 }
    
tmnxOamPingResultsRtJitter      OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingResultsRtJitter indicates the
         amount of two-way trip jitter, expressed in milliseconds, for a
         ping probe sent for this OAM test.
         A value of 0 for this object implies that no two-way-trip jitter 
         measurement is available."
    ::= { tmnxOamPingResultsEntry 24 }

tmnxOamPingResultsProbeTimeouts     OBJECT-TYPE       
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingResultsProbeTimeouts indicates the number
         of probes timed out without receiving a response."
    ::= { tmnxOamPingResultsEntry 25 }

tmnxOamPingResultsProbeFailures     OBJECT-TYPE       
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingResultsProbeFailures indicates the total
         number of probes that failed to be transmitted plus the number of
         probes that timed out without receiving a response."
    ::= { tmnxOamPingResultsEntry 26 }
 
tmnxOamPingResultsTestRunIndex      OBJECT-TYPE
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingResultsTestRunIndex identifies the
         row entry that reports results for a single OAM test run.

         The agent starts assigning tmnxOamPingResultsTestRunIndex values
         at 1 and wraps after exceeding the maximum possible value as
         defined by the limit of this object {'ffffffff'h}."
    ::= { tmnxOamPingResultsEntry 27 }
    

 --
 -- Alcatel 7x50 SR series OAM Ping History Table
 --
tmnxOamPingHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamPingHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM ping
         probe operation. The probe results for all OAM ping test types
         are saved in this table except for 'macPing'.  The 'macPing'
         results are saved in the tmnxOamMacPingHistoryTable.  
         
         The number of entries in this table for
         a configured test are limited by the value of the 
         corresponding tmnxOamPingCtlMaxRows object.

         An entry in this table is created when the result of an OAM ping 
         probe is determined.  An entry is removed from this table when 
         its corresponding tmnxOamPingCtlEntry is deleted.

         The agent removes the oldest entry for a test in the 
         tmnxOamPingHistoryTable to allow the addition of an new 
         entry for that test once the number of rows in the 
         tmnxOamPingHistoryTable reaches the value specified by 
         tmnxOamPingCtlMaxRows."
    ::= { tmnxOamPingObjs 5 }

tmnxOamPingHistoryEntry OBJECT-TYPE
    SYNTAX      TmnxOamPingHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamPingHistoryTable.
         The first two index elements identify the tmnxOamPingCtlEntry 
         that a tmnxOamPingHistoryEntry belongs to.  The third index
         identifies a specific OAM test run.  The fourth index 
         element selects a single OAM probe result."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex,
             tmnxOamPingResultsTestRunIndex,
             tmnxOamPingHistoryIndex
           }
    ::= { tmnxOamPingHistoryTable 1 }

TmnxOamPingHistoryEntry ::=
    SEQUENCE {
        tmnxOamPingHistoryIndex         Unsigned32,
        tmnxOamPingHistoryResponse      Unsigned32,
        tmnxOamPingHistoryOneWayTime    Integer32,
        tmnxOamPingHistorySize          Unsigned32,
        tmnxOamPingHistoryStatus        TmnxOamResponseStatus,
        tmnxOamPingHistoryTime          DateAndTime,
        tmnxOamPingHistoryReturnCode    TmnxOamPingRtnCode,
        tmnxOamPingHistorySrcIpAddress  IpAddress,
        tmnxOamPingHistAddressType      TmnxOamAddressType,
        tmnxOamPingHistSapId            TmnxStrSapId,
        tmnxOamPingHistoryVersion       Unsigned32,
        tmnxOamPingHistoryCpeMacAddr    MacAddress,
        tmnxOamPingHistoryRespSvcId     TmnxServId,
        tmnxOamPingHistorySequence      Unsigned32,
        tmnxOamPingHistoryIfIndex       InterfaceIndexOrZero,
        tmnxOamPingHistoryDataLen       Unsigned32,
        tmnxOamPingHistoryRespPlane     TmnxOamTestResponsePlane,
        tmnxOamPingHistoryReqHdr        OCTET STRING,
        tmnxOamPingHistoryRespHdr       OCTET STRING,
        tmnxOamPingHistoryDnsAddrType   InetAddressType,
        tmnxOamPingHistoryDnsAddress    InetAddress,
        tmnxOamPingHistorySrcAddrType   InetAddressType,
        tmnxOamPingHistorySrcAddress    InetAddress,
        tmnxOamPingHistoryInOneWayTime  Integer32
    }

tmnxOamPingHistoryIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created when the result of
         a OAM ping probe is determined.  The initial 2 instance
         identifier index values identify the tmnxOamPingCtlEntry
         that a OAM probe result (tmnxOamPingHistoryEntry) belongs
         to.  The tmnxOamPingHistoryIndex element selects a single OAM 
         probe result.
         The agent starts assigning tmnxOamPingHistoryIndex values at 1 
         and wraps after exceeding the maximum possible value as defined by
         the limit of this object ('ffffffff'h)."
    ::= { tmnxOamPingHistoryEntry 1 }

tmnxOamPingHistoryResponse OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when
         a OAM probe was sent to when its response was received or
         when it timed out.  The value of this object is reported
         as 0 when it is not possible to transmit an OAM probe."
    ::= { tmnxOamPingHistoryEntry 2 }

tmnxOamPingHistoryOneWayTime OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when
         a OAM probe was sent to when it was received by the replier.  
         The value of this object is reported as 0 when it is not possible 
         to transmit an OAM probe or the information is not available."
    ::= { tmnxOamPingHistoryEntry 3 }

tmnxOamPingHistorySize OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "octets"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OAM Echo Request message size sent with this OAM
         probe when the value of tmnxOamPingCtlTestMode has a value
         of 'mtuPing'; otherwise, the value of this object is
         reported as 0."
    ::= { tmnxOamPingHistoryEntry 4 }

tmnxOamPingHistoryStatus OBJECT-TYPE
    SYNTAX      TmnxOamResponseStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The result of a particular OAM test probe."
    ::= { tmnxOamPingHistoryEntry 5 }

tmnxOamPingHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Timestamp for when this OAM probe result was determined."
    ::= { tmnxOamPingHistoryEntry 6 }

tmnxOamPingHistoryReturnCode    OBJECT-TYPE
    SYNTAX      TmnxOamPingRtnCode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryReturnCode is an enumerated integer
         that indicates the OAM return code received in the OAM ping response."
    ::= { tmnxOamPingHistoryEntry 7 }

tmnxOamPingHistorySrcIpAddress  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 address of the remote node that generated this 
        reply to a OAM probe.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamPingHistorySrcAddrType and 
         tmnxOamPingHistorySrcAddress." 
    ::= { tmnxOamPingHistoryEntry 8 }

tmnxOamPingHistAddressType     OBJECT-TYPE
    SYNTAX      TmnxOamAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistAddressType specifies the type of
         binding address information returned in response to a 'vprnPing' 
         'mfibPing', or cpePing' test."
    ::= { tmnxOamPingHistoryEntry 9 }

tmnxOamPingHistSapId OBJECT-TYPE
    SYNTAX          TmnxStrSapId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamPingHistSapId is the name of the access 
         port of the SAP supporting the requests returned in
         response to a 'vprnPing', 'mfibPing' or 'cpePing' probe.
         
         If the value of tmnxOamPingHistAddressType is not 'sapId',
         this object is not relevant and MUST have a null string ''."
    ::= { tmnxOamPingHistoryEntry 10 }    

tmnxOamPingHistoryVersion OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryVersion indicates the protocol
         version for this OAM ping probe reply."
    ::= { tmnxOamPingHistoryEntry 12 }

tmnxOamPingHistoryCpeMacAddr OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamPingHistoryCpeMacAddr is set to 0 except in case of
         a 'cpePing', where it contains the mac address of the replying
         CPE for this OAM ping probe reply."
    ::= { tmnxOamPingHistoryEntry 13 }

tmnxOamPingHistoryRespSvcId OBJECT-TYPE
    SYNTAX      TmnxServId
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value tmnxOamPingHistoryRespSvcId indicates for an 'mfibPing'
         the service id to which the SAP responding to the 'mfibPing' probe 
         belongs. If the indicated SAP is part of the same service originating 
         the probe, this value is set to 0.
          
         For other types of probes, this value is insignificant and is also
         set to 0."
    ::= { tmnxOamPingHistoryEntry 14 }

tmnxOamPingHistorySequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value tmnxOamPingHistorySequence indicates this probe's 
         sequence number.  If a probe response was received, this object
         indicates the sequence number of the response."
    ::= { tmnxOamPingHistoryEntry 15 }

tmnxOamPingHistoryIfIndex   OBJECT-TYPE
    SYNTAX      InterfaceIndexOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryIfIndex indicates for an 'lspPing'
         the ifIndex value of the interface that this probe was transmitted 
         from. 

         For other types of probes, this value is not significant and is
         set to 0."
    ::= { tmnxOamPingHistoryEntry 16 }

tmnxOamPingHistoryDataLen   OBJECT-TYPE       
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryDataLen indicates for an 'lspPing'
         the UPD data length of the echo reply, and for 'icmpPing' normally
         the length starting after the IP header of the echo reply. 

         For other types of probes, this value is not significant and is
         set to 0."
    ::= { tmnxOamPingHistoryEntry 17 }

tmnxOamPingHistoryRespPlane OBJECT-TYPE    
    SYNTAX      TmnxOamTestResponsePlane
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryRespPlane indicates the 
         response plane from which this ping response was received."
    ::= { tmnxOamPingHistoryEntry 18 }

tmnxOamPingHistoryReqHdr   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (0|1..150))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryReqHdr is a copy of the OAM 
         header of the request packet sent for this OAM ping test probe.
         
         The request header is reported only when tmnxOamPingCtlTestMode 
         has a value of 'svcPing', 'sdpPing' or 'mtuPing'; otherwise,
         a zero length OCTET STRING is returned."  
    ::= { tmnxOamPingHistoryEntry 19 }

tmnxOamPingHistoryRespHdr   OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (0|1..150))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryRespHdr is a copy of the OAM 
         header of the response packet received for this OAM ping test probe.
          
         The response header is reported only when tmnxOamPingCtlTestMode 
         has a value of 'svcPing', 'sdpPing', 'mtuPing' and 'icmpPing'; 
         For icmpPing it reports the received icmp message starting from
         IP header. Otherwise, a zero length OCTET STRING is returned.
           
         The value of this object MUST be reported as a zero length
         OCTET STRING when no probe response has been received."
    ::= { tmnxOamPingHistoryEntry 20 }

tmnxOamPingHistoryDnsAddrType   OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryDnsAddrType indicates the
         Internet address type stored in tmnxOamPingHistoryDnsAddress.

         If this was not a 'dnsPing' test or the dns name specified
         in tmnxOamPingCtlDnsName could not be resolved, the value of
         this object will be reported as 'unknown'."
    ::= { tmnxOamPingHistoryEntry 21 }

tmnxOamPingHistoryDnsAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryDnsAddress indicates the
         Internet address that was resolved from the name specified
         in tmnxOamPingCtlDnsName by a 'dnsPing' test.

         If this was not a 'dnsPing' test or the dns name specified
         in tmnxOamPingCtlDnsName could not be resolved, the value of
         this object will be reported as ''H (empty string)."
    ::= { tmnxOamPingHistoryEntry 22 }

tmnxOamPingHistorySrcAddrType  OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistorySrcAddrType indicates the Internet
         address type of the address stored in tmnxOamPingHistorySrcAddress."
    ::= { tmnxOamPingHistoryEntry 23 }

tmnxOamPingHistorySrcAddress  OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16|20))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistorySrcAddress indicates the Internet 
        address of the remote node that generated this reply to a OAM probe."
    ::= { tmnxOamPingHistoryEntry 24 }

tmnxOamPingHistoryInOneWayTime OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamPingHistoryInOneWayTime indicates the amount 
         of time measured in milliseconds from when an OAM probe reply was 
         sent to when it was received by this host (in-bound one-way time).  
         The value of this object is reported as 0 when the information is 
         not available."
    ::= { tmnxOamPingHistoryEntry 25 }

--
--  Alcatel 7x50 SR series OAM MAC Ping Control Table
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable, 
--  and the sparse dependent table, tmnxOamMacPingCtlTable. 
--
--  This in effect extends the tmnxOamPingCtlTable with additional columns.
--  Rows are created in the tmnxOamMacPingCtlTable only for those entries 
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of 
--  'macQuery', 'macPing', 'macPopulate', or 'macPurge'.  
--  
--  Deletion of a row in the tmnxOamPingCtlTable results in the 
--  same fate for the row in the tmnxOamMacPingCtlTable.
--

tmnxOamMacPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMacPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM MAC Ping Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7x50 SR OAM 'macQuery',
         'macPing', 'macPopulate', or 'macPurge' test operations.  
         The results of these tests are stored in the tmnxOamPingResultsTable 
         and the tmnxOamMacPingHistoryTable.  There will be no entries for
         these test in the tmnxOamPingHistoryTable."
   ::= { tmnxOamPingObjs 6 }

tmnxOamMacPingCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamMacPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMacPingCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamMacPingCtlTable 1 }

TmnxOamMacPingCtlEntry ::=
    SEQUENCE {
        tmnxOamMacPingCtlTargetMacAddr      MacAddress,
        tmnxOamMacPingCtlSourceMacAddr      MacAddress,
        tmnxOamMacPingCtlSendControl        TruthValue,
        tmnxOamMacPingCtlReplyControl       TruthValue,
        tmnxOamMacPingCtlTtl                Unsigned32,
        tmnxOamMacPingCtlRegister           TruthValue,
        tmnxOamMacPingCtlFlood              TruthValue,
        tmnxOamMacPingCtlForce              TruthValue,
        tmnxOamMacPingCtlAge                Unsigned32,
        tmnxOamMacPingCtlSapPortId          TmnxPortID,
        tmnxOamMacPingCtlSapEncapValue      TmnxEncapVal,
        tmnxOamMacPingCtlFibEntryName       TNamedItemOrEmpty
    }

tmnxOamMacPingCtlTargetMacAddr OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingCtlTargetMacAddr is a 6-octet value 
         that specifies the target MAC address to be used in the query 
         request for performing an OAM ping operation.
         
         This parameter is required."
    REFERENCE
        "IEEE 802.3 Subclause ********.9"
    DEFVAL { '000000000000'H }
    ::= { tmnxOamMacPingCtlEntry 1 }

tmnxOamMacPingCtlSourceMacAddr OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingCtlSourceMacAddr is a 6-octet value 
         that  specifies  the  MAC  address to be used as the source in the
         query request for performing an OAM ping operation.
         This    object    is   optional   and   is   not   relevant   when
         tmnxOamPingCtlTestMode has a value other than 'macPing'."
    REFERENCE
        "IEEE 802.3 Subclause ********.9"
    DEFVAL { '000000000000'H }
    ::= { tmnxOamMacPingCtlEntry 2 }

tmnxOamMacPingCtlSendControl       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMacPingCtlSendControl is 'true', the OAM
         ping packet is sent directly to the destination IP address via the
         control plane. If its value is 'false', the packet is sent via the
         data plane.
         This object is optional and is not relevant when
         tmnxOamPingCtlTestMode has a value other than 'macPopulate' or
         'macPurge'."
    DEFVAL { false }
    ::= { tmnxOamMacPingCtlEntry 3 }

tmnxOamMacPingCtlReplyControl      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMacPingCtlReplyControl is 'true', the
         OAM ping response is returned using the control plane. If its
         value is 'false', the packet is sent via the data plane. This
         object is optional and is not relevant when tmnxOamPingCtlTestMode
         has a value other than 'macPing'."
    DEFVAL { false }
    ::= { tmnxOamMacPingCtlEntry 4 }

tmnxOamMacPingCtlTtl       OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the vc-label time-to-live value.

         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPing'.
         
         While performing the test with a ttl of 1 no responses should ever be 
         expected."
    DEFVAL { 5 }    
    ::= { tmnxOamMacPingCtlEntry 5 }

tmnxOamMacPingCtlRegister      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMacPingCtlRegister is 'true', the
         OAM ping request is transmitted that causes each upstream node
         to delete the MAC address only from its forwarding plane but
         keep it in its control plane in order to inhibit dynamic learning. 
         If its value is 'false', the MAC address is deleted from both
         the forwarding and control planes.
        
         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPurge'."
    DEFVAL { false }
    ::= { tmnxOamMacPingCtlEntry 6 }

tmnxOamMacPingCtlFlood     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMacPingCtlFlood is 'true', the
         OAM ping request is transmitted that causes each upstream node
         to add or delete the MAC address. If its value is 'false', the 
         operation is performed locally.
        
         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPopulate' or 
         'macPurge'."
    DEFVAL { false }
    ::= { tmnxOamMacPingCtlEntry 7 }

tmnxOamMacPingCtlForce     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMacPingCtlForce is 'true', the
         MAC type in the FIB is forced to be labeled OAM type if it already
         exised as dynamic or static.
        
         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPopulate'."
    DEFVAL { false }
    ::= { tmnxOamMacPingCtlEntry 8 }

tmnxOamMacPingCtlAge       OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingCtlAge specifies the number of seconds
         to age this OAM MAC address in the FIB.

         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPopulate'."
    DEFVAL { 3600 }     -- 1 hour
    ::= { tmnxOamMacPingCtlEntry 9 }

tmnxOamMacPingCtlSapPortId OBJECT-TYPE
    SYNTAX          TmnxPortID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamMacPingCtlSapPortId is the ID of the access 
         port of the target SAP.
         
         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPopulate'."
    DEFVAL { 0 }
    ::= { tmnxOamMacPingCtlEntry 10 }    
    
tmnxOamMacPingCtlSapEncapValue     OBJECT-TYPE
    SYNTAX          TmnxEncapVal
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamMacPingCtlSapEncapValue is the label used to
         identify the target SAP on the port specified in 
         tmnxOamMacPingCtlSapPortId. 
         
         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPopulate'."
    DEFVAL { 0 }
    ::= { tmnxOamMacPingCtlEntry 11 }

tmnxOamMacPingCtlFibEntryName      OBJECT-TYPE
    SYNTAX          TNamedItemOrEmpty
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamMacPingCtlFibEntryName is the fib entry name
         to associate with the target MAC address.
         
         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'macPopulate'."
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamMacPingCtlEntry 12 }


 --
 -- Alcatel 7x50 SR series OAM MAC Ping History Table
 --
tmnxOamMacPingHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMacPingHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'macQuery'
         or 'macPing' probe operation.  The number of entries in this table 
         are limited by the value of the corresponding tmnxOamPingCtlMaxRows
         object.

         An entry in this table is created when the result of an OAM 
         'macQuery' or 'macPing' probe is determined.  An entry 
         is removed from this table when its corresponding tmnxOamPingCtlEntry 
         is deleted.

         The agent removes the oldest entry for a test in the 
         tmnxOamMacPingHistoryTable to allow the addition of an new 
         entry for that test once the number of rows in the 
         tmnxOamMacPingHistoryTable reaches the value specified by 
         tmnxOamPingCtlMaxRows."
   ::= { tmnxOamPingObjs 7 }

tmnxOamMacPingHistoryEntry OBJECT-TYPE
    SYNTAX      TmnxOamMacPingHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamPingHistoryTable.  The first two 
         index elements identify the tmnxOamPingCtlEntry that a 
         tmnxOamMacPingHistoryEntry belongs to.  The third index identifies
         a specific run of the OAM test.  The fourth index element 
         selects the group of responses associated with a specific probe
         attempt.  The fifth index selects a single OAM 'macQuery' or
         'macPing' reply.  Note that in the case of 'macQuery' there will
         be only one row entry created per test run."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex,
             tmnxOamPingResultsTestRunIndex,
             tmnxOamMacPingHistoryIndex,
             tmnxOamMacPingReplyIndex
           }
    ::= { tmnxOamMacPingHistoryTable 1 }

TmnxOamMacPingHistoryEntry ::=
    SEQUENCE {
        tmnxOamMacPingHistoryIndex              Unsigned32,
        tmnxOamMacPingReplyIndex                Unsigned32,
        tmnxOamMacPingHistoryResponse           Unsigned32,
        tmnxOamMacPingHistoryOneWayTime         Integer32,
        tmnxOamMacPingHistoryStatus             TmnxOamResponseStatus,
        tmnxOamMacPingHistoryTime               DateAndTime,
        tmnxOamMacPingHistoryReturnCode         TmnxOamPingRtnCode,        
        tmnxOamMacPingHistorySrcIpAddress       IpAddress,
        tmnxOamMacPingHistoryAddressType        TmnxOamAddressType,
        tmnxOamMacPingHistorySapId              TmnxStrSapId,
        tmnxOamMacPingHistorySdpId              SdpId,
        tmnxOamMacPingHistoryAdminStatus        TruthValue,
        tmnxOamMacPingHistoryOperStatus         TruthValue,
        tmnxOamMacPingHistoryResponsePlane      TmnxOamTestResponsePlane,
        tmnxOamMacPingHistorySize               Unsigned32,
        tmnxOamMacPingHistoryInOneWayTime       Integer32,
        tmnxOamMacPingHistorySrcAddrType        InetAddressType,
        tmnxOamMacPingHistorySrcAddress         InetAddress
    }

tmnxOamMacPingHistoryIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created when the result of a OAM 
         'macQuery' or 'macPing' probe is determined.  The initial 2 
         instance identifier index values identify the tmnxOamPingCtlEntry
         that a OAM probe result (tmnxOamMacPingHistoryEntry) belongs
         to.  The tmnxOamMacPingHistoryIndex has the value of the
         sequence number of the request probe and identifies a group of 
         replies received in response to a specific probe transmission.

         The agent starts assigning tmnxOamMacPingHistoryIndex values at 1 
         and wraps after exceeding the maximum possible value as defined by
         the limit of this object ('ffffffff'h)."
    ::= { tmnxOamMacPingHistoryEntry 1 }

tmnxOamMacPingReplyIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxOamMacPingReplyIndex is unique within a group of responses
         received as the result of a OAM 'macQuery' or 'macPing' probe as
         specified by having the save value for tmnxOamMacPingHistoryIndex.

         The agent starts assigning tmnxOamMacPingReplyIndex values at 1 
         and wraps after exceeding the maximum possible value as defined by
         the limit of this object ('ffffffff'h)."
    ::= { tmnxOamMacPingHistoryEntry 2 }

tmnxOamMacPingHistoryResponse OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when
         a OAM probe was sent to when its response was received or
         when it timed out.  The value of this object is reported
         as 0 when it is not possible to transmit an OAM probe."
    ::= { tmnxOamMacPingHistoryEntry 3 }

tmnxOamMacPingHistoryOneWayTime OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when an 
         OAM probe was sent to when it was received by the replier
         (out-bound one-way time).  The value of this object is 
         reported as 0 when it is not possible to transmit an OAM 
         probe or the information is not available."
    ::= { tmnxOamMacPingHistoryEntry 4 }

tmnxOamMacPingHistoryStatus OBJECT-TYPE
    SYNTAX      TmnxOamResponseStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistoryStatus is an enumberate integer
         that indicates the result of a particular OAM probe."
    ::= { tmnxOamMacPingHistoryEntry 5 }

tmnxOamMacPingHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Timestamp for when this OAM probe result was determined."
    ::= { tmnxOamMacPingHistoryEntry 6 }

tmnxOamMacPingHistoryReturnCode    OBJECT-TYPE
    SYNTAX      TmnxOamPingRtnCode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistoryReturnCode is an enumerated integer
         that indicates the return code received in the OAM ping response."
    ::= { tmnxOamMacPingHistoryEntry 7 }

tmnxOamMacPingHistorySrcIpAddress  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of tmnxOamMacPingHistorySrcIpAddress specifies the Ipv4 
         address of the remote node that generated this reply to a OAM probe.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMacPingHistorySrcAddrType and 
         tmnxOamMacPingHistorySrcAddress." 
    ::= { tmnxOamMacPingHistoryEntry 8 }

tmnxOamMacPingHistoryAddressType       OBJECT-TYPE
    SYNTAX      TmnxOamAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistoryAddressType specifies the type of
         binding address information returned in response to a 'macQuery' 
         or 'macPing' test."
    ::= { tmnxOamMacPingHistoryEntry 9 }

tmnxOamMacPingHistorySapId OBJECT-TYPE
    SYNTAX          TmnxStrSapId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamMacPingHistorySapId is the name of the access 
         port of the SAP supporting the requested MAC address returned in
         response to a 'macQuery' or 'macPing' probe.
         
         If the value of tmnxOamMacPingHistoryAddressType is not 'sapId',
         this object is not relevant and MUST have a null string ''."
    ::= { tmnxOamMacPingHistoryEntry 10 }    

tmnxOamMacPingHistorySdpId     OBJECT-TYPE
    SYNTAX      SdpId
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistorySdpId is the ID of the SDP
         supporting the requested MAC address returned in response to a
         'macQuery' or 'macPing' probe.

         If the value of tmnxOamMacPingHistoryAddressType is not 'sdpId',
         this object is not relevant and MUST return a value of '0'."
    ::= { tmnxOamMacPingHistoryEntry 12 }

tmnxOamMacPingHistoryAdminStatus   OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistoryAdminStatus indicates the current
         administrative state of the SAP or SDP ID returned in response to a
         'macQuery' or 'macPing' test.  A value of 'true' indicates the SAP 
         or SDP is administratively 'up'.

         The value of this object MUST be reported as 'false' when no ping 
         responses have been received or an active test has not yet timed out." 
    ::= { tmnxOamMacPingHistoryEntry 13 }

tmnxOamMacPingHistoryOperStatus    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistoryOperStatus indicates the current
         operational state of the SAP or SDP ID returned in response to a
         'macQuery' or 'macPing' test.  A value of 'true' indicates the SAP 
         or SDP is operationally 'up'.

         The value of this object MUST be reported as 'false' when no ping 
         responses have been received or an active test has not yet timed out."
    ::= { tmnxOamMacPingHistoryEntry 14 }

tmnxOamMacPingHistoryResponsePlane     OBJECT-TYPE
    SYNTAX      TmnxOamTestResponsePlane
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistoryResponsePlane indicates the 
         response plane from which this ping response was received."
    ::= { tmnxOamMacPingHistoryEntry 15 }
    
tmnxOamMacPingHistorySize     OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "octets"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistorySize indicates the size in octets
         of the user payload in ping request packet.  It does not include 
         the service encapsulation."
    ::= { tmnxOamMacPingHistoryEntry 16 }
    
tmnxOamMacPingHistoryInOneWayTime OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when an OAM 
         probe reply was sent to when it was received by this host
         (in-bound one-way time).  The value of this object is reported 
         as 0 when the information is not available."
    ::= { tmnxOamMacPingHistoryEntry 17 }

tmnxOamMacPingHistorySrcAddrType  OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistorySrcAddrType indicates the Internet 
         address type stored in tmnxOamMacPingHistorySrcAddress."
    ::= { tmnxOamMacPingHistoryEntry 18 }

tmnxOamMacPingHistorySrcAddress  OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingHistorySrcAddress indicates the Internet 
         address of the remote node that generated this reply to a OAM probe."
    ::= { tmnxOamMacPingHistoryEntry 19 }


 --
 -- Alcatel 7x50 SR series OAM MAC Ping Label Mapping Table
 --
tmnxOamMacPingL2MapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMacPingL2MapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'macQuery'
         probe operation where a L2 mapping TLV is returned.  Only one
         downstream and one upstream L2 mapping entry is returned if the 
         'macQuery' replier has no egress mapping for the requested MAC 
         address but it does have an SDP binding.  Multiple downstream L2 
         mappings that specify the replier's flooding domain may be returned 
         if the replier has no bindings for the MAC address.
       
         An entry in this table is created when the result of an OAM 
         'macQuery'probe is determined. 
        
         An entry is removed from this table when its corresponding 
         tmnxOamPingCtlEntry is deleted."
   ::= { tmnxOamPingObjs 8 }

tmnxOamMacPingL2MapEntry OBJECT-TYPE
    SYNTAX      TmnxOamMacPingL2MapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMacPingL2MapTable.  The first two 
         index elements identify the tmnxOamPingCtlEntry that a 
         tmnxOamMacPingL2MapEntry belongs to.  The third index element
         identifies a specific OAM test run.  The fourth index element 
         selects the group of responses associated with a specific probe
         attempt.  The fifth index selects a single OAM 'macQuery' reply.  
         Note that in the case of a successful 'macQuery' reply there will 
         be only two row entries created per test run.  However there may 
         also be one or more error replies.  The sixth index selects a 
         single L2 mapping entry within a specific probe reply."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex,
             tmnxOamPingResultsTestRunIndex,
             tmnxOamMacPingHistoryIndex,
             tmnxOamMacPingReplyIndex,
             tmnxOamMacPingL2MapIndex
           }
    ::= { tmnxOamMacPingL2MapTable 1 }

TmnxOamMacPingL2MapEntry ::=
    SEQUENCE {
        tmnxOamMacPingL2MapIndex        Unsigned32,
        tmnxOamMacPingL2MapRouterID     IpAddress,
        tmnxOamMacPingL2MapLabel        MplsLabel,
        tmnxOamMacPingL2MapProtocol     TmnxOamSignalProtocol,
        tmnxOamMacPingL2MapVCType       TmnxOamVcType,        
        tmnxOamMacPingL2MapVCID         TmnxVcId,
        tmnxOamMacPingL2MapDirection    INTEGER
    }

tmnxOamMacPingL2MapIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created when the result of a OAM 
         'macQuery' probe is determined.  The initial 2 instance identifier 
         index values identify the tmnxOamPingCtlEntry that a OAM probe result 
         (tmnxOamMacPingHistoryEntry) belongs to. The tmnxOamMacPingHistoryIndex
         identifies a group of replies received in response to a specific 
         probe transmission.  The tmnxOamMacPingReplyIndex selects a single 
         OAM 'macQuery' reply.    
        
         The tmnxOamMacPingL2MapIndex selects a single L2 mapping entry
         within a specific 'macQuery' probe reply.

         The agent starts assigning tmnxOamMacPingL2MapIndex values at 1." 
    ::= { tmnxOamMacPingL2MapEntry 1 }

tmnxOamMacPingL2MapRouterID    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingL2MapRouterID is the router ID for this
         L2 mapping entry."
    ::= { tmnxOamMacPingL2MapEntry 2 }
    
tmnxOamMacPingL2MapLabel       OBJECT-TYPE
    SYNTAX      MplsLabel
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingL2MapLabel is the label used by the
         router for the L2FEC or VC ID specified by this L2 mapping entry."
    ::= { tmnxOamMacPingL2MapEntry 3 }
    
tmnxOamMacPingL2MapProtocol    OBJECT-TYPE
    SYNTAX      TmnxOamSignalProtocol
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingL2MapProtocol is the signaling protocol
         used by the router for the L2FEC or VC ID specified by this L2
         mapping entry."
    ::= { tmnxOamMacPingL2MapEntry 4 }
    
tmnxOamMacPingL2MapVCType       OBJECT-TYPE
    SYNTAX      TmnxOamVcType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingL2MapVCType specifies the class of
         VC ID given in tmnxOamMacPingL2MapVCID."
    ::= { tmnxOamMacPingL2MapEntry 5 }
    
tmnxOamMacPingL2MapVCID        OBJECT-TYPE
    SYNTAX      TmnxVcId
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingL2MapVCID is the VC ID associated with
         the label used by the L2FEC specified by this L2 mapping entry."
    ::= { tmnxOamMacPingL2MapEntry 6 }

tmnxOamMacPingL2MapDirection       OBJECT-TYPE
    SYNTAX      INTEGER {
                    upstream (1),
                    downstream (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacPingL2MapDirection specifies the direction
         that this L2 mapping entry is in relation to the node that returned
         the 'macQuery' reply."
    ::= { tmnxOamMacPingL2MapEntry 7 }



--
--  Alcatel 7x50 SR series OAM LSP Ping Control Table
--
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable, 
--  and the sparse dependent table, tmnxOamLspPingCtlTable. 
--
--  This in effect extends the tmnxOamPingCtlTable with additional columns.
--  Rows are created in the tmnxOamLspPingCtlTable only for those entries 
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of 
--  'lspPing'.  
--  
--  Deletion of a row in the tmnxOamPingCtlTable results in the 
--  same fate for the row in the tmnxOamLspPingCtlTable.
--
tmnxOamLspPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamLspPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM Lsp ping Control Table for 
         providing, via SNMP, the capability of performing Alcatel
         7x50 SR OAM 'lspPing' test operations.  The results of these 
         tests are stored in the tmnxOamPingResultsTable and the 
         tmnxOamPingHistoryTable."
    ::= { tmnxOamPingObjs 9 }

tmnxOamLspPingCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamLspPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamLspPingCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamLspPingCtlTable 1 }

TmnxOamLspPingCtlEntry ::=
    SEQUENCE {
        tmnxOamLspPingCtlVRtrID          TmnxVRtrID,
        tmnxOamLspPingCtlLspName         TNamedItemOrEmpty,
        tmnxOamLspPingCtlReturnLsp       TNamedItemOrEmpty,
        tmnxOamLspPingCtlTtl             Unsigned32,
        tmnxOamLspPingCtlPathName        TNamedItemOrEmpty,
        tmnxOamLspPingCtlLdpIpPrefix     IpAddress,
        tmnxOamLspPingCtlLdpIpPrefixLen  IpAddressPrefixLength,
        tmnxOamLspPingCtlLdpPrefixType   InetAddressType,
        tmnxOamLspPingCtlLdpPrefix       InetAddress,
        tmnxOamLspPingCtlLdpPrefixLen    InetAddressPrefixLength,
        tmnxOamLspPingCtlPathDestType    InetAddressType,
        tmnxOamLspPingCtlPathDest        InetAddress,
        tmnxOamLspPingCtlNhIntfName      TNamedItemOrEmpty,
        tmnxOamLspPingCtlNhAddressType   InetAddressType,
        tmnxOamLspPingCtlNhAddress       InetAddress
    }

tmnxOamLspPingCtlVRtrID  OBJECT-TYPE
    SYNTAX      TmnxVRtrID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this virtual router in the 
         Alcatel 7x50 SR system. The vRtrID value for each virtual router 
         must remain constant at least from one re-initialization of the 
         system management processor (CPM) to the next.  There will always 
         be at least one router entry defined by the agent with vRtrID=1 
         which represents the transport router."
    DEFVAL { 1 } -- Base router
    ::= { tmnxOamLspPingCtlEntry 1 }

tmnxOamLspPingCtlLspName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Administrative name of the target Labeled Switch Path (LSP) for this
         OAM LSP Ping test.  The LSP name must be unique within the virtual
         router instance specified by tmnxOamLspPingCtlVRtrID.
         
         This parameter is mutually exclusive with the IP prefix parameter 
         associated with an LDP based LSP (tmnxOamLspPingCtlLdpIpPrefix). 
         Either the LSP name or the LDP IP Prefix must be specified but not
         both."
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamLspPingCtlEntry 2 }

tmnxOamLspPingCtlReturnLsp     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Administrative name of the Labeled Switch Path (LSP) to use to
         return the response to this OAM LSP Ping test.  The LSP name must 
         be unique within the virtual router instance specified by 
         tmnxOamLspPingCtlVRtrID.
         This is an optional parameter."
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamLspPingCtlEntry 3 }

tmnxOamLspPingCtlTtl       OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the outermost label time-to-live value.  This is an optional
         parameter."
    DEFVAL { 255 }
    ::= { tmnxOamLspPingCtlEntry 4 }

tmnxOamLspPingCtlPathName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Administrative name of the Path (LSP) for this OAM LSP Ping test. 
         The Path name must be unique within the virtual router 
         instance specified by tmnxOamLspPingCtlVRtrID.
         
         This is an optional parameter which can be specified only if the
         LSP Name parameter is specified."
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamLspPingCtlEntry 5 }

tmnxOamLspPingCtlLdpIpPrefix     OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "The IP prefix for the LDP based LSP for this OAM LSP Ping test. 
         
         This parameter is mutually exclusive with the LSP name parameter 
         (tmnxOamLspPingCtlLspName). Either the LDP IP Prefix or the LSP 
         name must be specified but not both.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamLspPingCtlLdpPrefixType and 
         tmnxOamLspPingCtlLdpPrefix." 
    DEFVAL { '00000000'H } -- 0.0.0.0
    ::= { tmnxOamLspPingCtlEntry 6 }

tmnxOamLspPingCtlLdpIpPrefixLen     OBJECT-TYPE
    SYNTAX      IpAddressPrefixLength
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "The IP prefix length for the LDP based LSP for this OAM LSP
         Ping test.

         The value of this parameter is valid only when LDP IP Prefix 
         has been specified.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress object tmnxOamLspPingCtlLdpPrefixLen." 
    DEFVAL { 32 } 
    ::= { tmnxOamLspPingCtlEntry 7 }

tmnxOamLspPingCtlLdpPrefixType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspPingCtlLdpPrefixType specifies the type
         of Internet address stored in tmnxOamLspPingCtlLdpPrefix."
    DEFVAL { unknown }
    ::= { tmnxOamLspPingCtlEntry 8 }

tmnxOamLspPingCtlLdpPrefix     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspPingCtlLdpPrefix specifies the Internet
         prefix for the LDP based LSP for this OAM LSP Ping test. 
         
         This parameter is mutually exclusive with the LSP name parameter 
         (tmnxOamLspPingCtlLspName).  Either the LDP IP Prefix or the LSP 
         name must be specified but not both."
    DEFVAL { ''H }
    ::= { tmnxOamLspPingCtlEntry 9 }

tmnxOamLspPingCtlLdpPrefixLen     OBJECT-TYPE
    SYNTAX      InetAddressPrefixLength
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspPingCtlLdpPrefixLen specifies the Internet
         address prefix length for the LDP based LSP for this OAM LSP
         Ping test.

         The value of this parameter is valid only when LDP IP Prefix 
         has been specified."
    DEFVAL { 32 } 
    ::= { tmnxOamLspPingCtlEntry 10 }

tmnxOamLspPingCtlPathDestType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspPingCtlPathDestType specifies the type
         of Internet address stored in tmnxOamLspPingCtlPathDest."
    DEFVAL { unknown }
    ::= { tmnxOamLspPingCtlEntry 11 }

tmnxOamLspPingCtlPathDest     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspPingCtlPathDest specifies a unique path 
         for this OAM Lsp Ping to traverse. This address is used as part of a
         hash key to select the appropriate ECMP path to the destination of 
         an OAM LSP Ping test. 
         
         This is an optional parameter. "
    DEFVAL { ''H }
    ::= { tmnxOamLspPingCtlEntry 12 }    

tmnxOamLspPingCtlNhIntfName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Administrative name of the next hop interface for this OAM LSP 
         Ping test to be sent. The interface name must be unique within
         the virtual router instance specified by tmnxOamLspPingCtlVRtrID.
         
         This is an optional parameter which can be specified only if the
         tmnxOamLspPingCtlPathDest parameter is specified. This parameter 
         is mutually exclusive with the tmnxOamLspPingCtlNhAddress
         parameter. Either the next-hop interface name or next-hop address
         can be specified but not both. An attempt to set this object to a
         non-default value when tmnxOamLspPingCtlNhAddress has a 
         non-default value will result in an 'inconsistentValue' error."
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamLspPingCtlEntry 13 }

tmnxOamLspPingCtlNhAddressType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspPingCtlNhAddressType specifies the type
         of Internet address stored in tmnxOamLspPingCtlNhAddress."
    DEFVAL { unknown }
    ::= { tmnxOamLspPingCtlEntry 14 }

tmnxOamLspPingCtlNhAddress     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspPingCtlNhAddress specifies the Interface
         address to the next hop in which this OAM LSP ping test will be
         transmitted.  
         
         This is an optional parameter which can be specified only if the
         tmnxOamLspPingCtlPathDest parameter is specified. This parameter
         is mutually exclusive with tmnxOamLspPingCtlNhIntfName parameter.
         Either the next-hop interface name or next-hop address
         can be specified but not both. An attempt to set this object to a
         non-default value when tmnxOamLspPingCtlNhIntfName has a
         non-default value  will result in an 'inconsistentValue' error."
    DEFVAL { ''H }
    ::= { tmnxOamLspPingCtlEntry 15 }    

--
--  Alcatel 7x50 SR series OAM VPRN Ping Control Table
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable, 
--  and the sparse dependent table, tmnxOamVprnPingCtlTable. 
--
--  This in effect extends the tmnxOamPingCtlTable with additional columns.
--  Rows are created in the tmnxOamVprnPingCtlTable only for those entries 
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of 
--  'vprnPing'.  
--  
--  Deletion of a row in the tmnxOamPingCtlTable results in the 
--  same fate for the row in the tmnxOamVprnPingCtlTable.
--

tmnxOamVprnPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVprnPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM VPRN Ping Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7x50 SR OAM 'vprnPing'
         test operations.  
        
         The results of these tests are stored in the tmnxOamPingResultsTable 
         and the tmnxOamTrProbeHistoryTable.  There will be no entries for
         these test in the tmnxOamPingHistoryTable."
   ::= { tmnxOamPingObjs 10 }

tmnxOamVprnPingCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamVprnPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVprnPingCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamVprnPingCtlTable 1 }

TmnxOamVprnPingCtlEntry ::=
    SEQUENCE {
        tmnxOamVprnPingCtlSourceIpAddr      IpAddress,
        tmnxOamVprnPingCtlReplyControl      TruthValue,
        tmnxOamVprnPingCtlTtl               Unsigned32,
        tmnxOamVprnPingCtlSrcAddrType       InetAddressType,
        tmnxOamVprnPingCtlSrcAddress        InetAddress
    }

tmnxOamVprnPingCtlSourceIpAddr     OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 address to be used as the source for
         performing an OAM ping operation when tmnxOamPingCtlTestMode has
         a value of 'vprnPing'.   This parameter is required.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamVprnPingCtlSrcAddrType and 
         tmnxOamVprnPingCtlSrcAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamVprnPingCtlEntry 1 }

tmnxOamVprnPingCtlReplyControl     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamVprnPingCtlReplyControl is 'true', the
         OAM ping response is returned using the control plane.  If its value
         is 'false', the packet is sent via the data plane.

         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'vprnPing'."
    DEFVAL { false }
    ::= { tmnxOamVprnPingCtlEntry 2 }

tmnxOamVprnPingCtlTtl      OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the vc-label time-to-live value.
         
         This object is optional and is not relevant when 
         tmnxOamPingCtlTestMode has a value other than 'vprnPing'.

         While performing the test with a ttl of 1 no responses should ever be 
         expected."
    DEFVAL { 5 }    
    ::= { tmnxOamVprnPingCtlEntry 3 }

tmnxOamVprnPingCtlSrcAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnPingCtlSrcAddrType specifies the Internet
         address type stored in tmnxOamVprnPingCtlSrcAddress."
    DEFVAL { unknown }
    ::= { tmnxOamVprnPingCtlEntry 4 }

tmnxOamVprnPingCtlSrcAddress     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnPingCtlSrcAddress specifies the Internet
         address to be used as the source for performing an OAM ping 
         operation when tmnxOamPingCtlTestMode has a value of 'vprnPing'.   
         
         This parameter is required."
    DEFVAL { ''H }
    ::= { tmnxOamVprnPingCtlEntry 5 }


--
--  Alcatel 7750 SR series OAM ATM Ping Control Table
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable, 
--  and the sparse dependent table, tmnxOamAtmPingCtlTable. 
--
--  This in effect extends the tmnxOamPingCtlTable with additional columns.
--  Rows are created in the tmnxOamAtmPingCtlTable only for those entries 
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of 
--  'atmPing'.
--  
--  Deletion of a row in the tmnxOamPingCtlTable results in the 
--  same fate for the row in the tmnxOamAtmPingCtlTable.
--
tmnxOamAtmPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamAtmPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7750 SR OAM ATM Ping Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7750 SR OAM 'atmPing'
         test operations.  
         The results of these tests are stored in the tmnxOamPingResultsTable 
         and the tmnxOamPingHistoryTable."
   ::= { tmnxOamPingObjs 11 }

tmnxOamAtmPingCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamAtmPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamAtmPingCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamAtmPingCtlTable 1 }

TmnxOamAtmPingCtlEntry ::=
    SEQUENCE {
        tmnxOamAtmPingCtlPortId           TmnxPortID,
        tmnxOamAtmPingCtlVpi              AtmVpIdentifier,
        tmnxOamAtmPingCtlVci              AtmVcIdentifier,
        tmnxOamAtmPingCtlLpbkLocation     OCTET STRING,
        tmnxOamAtmPingCtlSegment          INTEGER
    }

tmnxOamAtmPingCtlPortId OBJECT-TYPE
    SYNTAX          TmnxPortID
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamAtmPingCtlPortId is the ID of the access 
         port of the target VC. This parameter is required."
    DEFVAL { 0 }
    ::= { tmnxOamAtmPingCtlEntry 1 }    
    
tmnxOamAtmPingCtlVpi OBJECT-TYPE
    SYNTAX          AtmVpIdentifier
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamAtmPingCtlVpi is the VPI of the VC used to
         send the OAM ATM ping.  This is a required parameter."
    DEFVAL { 0 }
    ::= { tmnxOamAtmPingCtlEntry 2 }
    
tmnxOamAtmPingCtlVci OBJECT-TYPE
    SYNTAX          AtmVcIdentifier
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamAtmPingCtlVci is the VCI of the VC used to
         send the OAM ATM ping.  This is a required parameter."
    DEFVAL { 0 }
    ::= { tmnxOamAtmPingCtlEntry 3 }
    
tmnxOamAtmPingCtlLpbkLocation OBJECT-TYPE
    SYNTAX          OCTET STRING (SIZE(16))
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamAtmPingCtlLpbkLocation is the Loopback 
         Location ID used in the ATM OAM loopback cell.  If all bits in the 
         Loopback Location ID are '1', the destination of the OAM ATM ping 
         is the far-end destination of the VC.  Otherwise it is destined to 
         a specific ATM node in the ATM network.

         This is a required parameter."
    DEFVAL { '00000000000000000000000000000000'H }  -- the empty string
    ::= { tmnxOamAtmPingCtlEntry 4 }
    
tmnxOamAtmPingCtlSegment OBJECT-TYPE
    SYNTAX          INTEGER {
                          endToEnd (1),
                          segment (2)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamAtmPingCtlSegment determines if the 
         ATM OAM loopback cell is destined to the first segment poing in 
         the line direction or the PVCC's connection endpoint."
    DEFVAL { endToEnd }     
    ::= { tmnxOamAtmPingCtlEntry 5 }


--
--  Alcatel 7x50 SR series OAM MFIB Ping Control Table
--
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable,
--  and the sparse dependent table, tmnxOamMfibPingCtlTable.
--
--  Rows are created in the tmnxOamMfibPingCtlTable only for those entries
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of
--  'mfibPing'.
--
--  Deletion of a row in the tmnxOamPingCtlTable results in the
--  same fate for the row in the tmnxOamMfibPingCtlTable.
--
tmnxOamMfibPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMfibPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel  7x50  SR  OAM  MFIB  Ping Control Table for
         providing, via SNMP, the capability of performing Alcatel 7x50 SR
         OAM 'mfibPing' test operations.

         The results of these tests are stored in the
         tmnxOamPingResultsTable and the tmnxOamPingHistoryTable. Rows are
         created in the tmnxOamMfibPingCtlTable only for those entries in
         the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of
         'mfibPing'."
   ::= { tmnxOamPingObjs 12 }

tmnxOamMfibPingCtlEntry     OBJECT-TYPE
    SYNTAX      TmnxOamMfibPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMfibPingCtlTable.  The
         first index element, tmnxOamPingCtlOwnerIndex, is of
         type SnmpAdminString, a textual convention that allows
         for use of the SNMPv3 View-Based Access Control Model
         (RFC 2575 [11], VACM) and allows a management
         application to identify its entries. The second index,
         tmnxOamPingCtlTestIndex, enables the same management
         application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamMfibPingCtlTable 1 }

TmnxOamMfibPingCtlEntry ::=
    SEQUENCE {
        tmnxOamMfibPingCtlSourceIpAddr    IpAddress,
        tmnxOamMfibPingCtlDestIpAddr      IpAddress,
        tmnxOamMfibPingCtlReplyControl    TruthValue,
        tmnxOamMfibPingCtlTtl             Unsigned32,
        tmnxOamMfibPingCtlSrcAddrType     InetAddressType,
        tmnxOamMfibPingCtlSrcAddress      InetAddress,
        tmnxOamMfibPingCtlDestAddrType    InetAddressType,
        tmnxOamMfibPingCtlDestAddress     InetAddress,
        tmnxOamMfibPingCtlDestMacAddr     MacAddress
    }

tmnxOamMfibPingCtlSourceIpAddr     OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 address to be used as the source for performing
         an OAM ping operation when tmnxOamPingCtlTestMode has a value of
         'mfibPing' in which case this parameter is required.  If
         tmnxOamPingCtlTestMode is different from 'mfibPing' this field is
         ignored.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMfibPingCtlSrcAddrType and 
         tmnxOamMfibPingCtlSrcAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamMfibPingCtlEntry 1 }

tmnxOamMfibPingCtlDestIpAddr       OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 multicast address to be used as the destination
         for performing an OAM ping operation when tmnxOamPingCtlTestMode has a
         value of 'mfibPing' in which case this parameter is required. If
         tmnxOamPingCtlTestMode is different from 'mfibPing' this field is
         ignored.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMfibPingCtlDestAddrType and 
         tmnxOamMfibPingCtlDestAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamMfibPingCtlEntry 2 }

tmnxOamMfibPingCtlReplyControl     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMfibPingCtlReplyControl is 'true', the
         OAM ping response is returned using the control plane. If its value
         is 'false', the packet is sent via the data plane.

         This object is optional and is not relevant when tmnxOamPingCtlTestMode
         has a value other than 'mfibPing'."
    DEFVAL { false }
    ::= { tmnxOamMfibPingCtlEntry 3 }

tmnxOamMfibPingCtlTtl              OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the vc-label time-to-live value.
         This object is optional and is not relevant when
         tmnxOamPingCtlTestMode has a value other than 'mfibPing'.
         While performing the test with a ttl of 1 no responses should ever
         be expected."
    DEFVAL { 255 }
    ::= { tmnxOamMfibPingCtlEntry 4 }

tmnxOamMfibPingCtlSrcAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMfibPingCtlSrcAddrType specifies the Internet
         address type stored in tmnxOamMfibPingCtlSrcAddress."
    DEFVAL { unknown }
    ::= { tmnxOamMfibPingCtlEntry 5 }

tmnxOamMfibPingCtlSrcAddress     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMfibPingCtlSrcAddress specifies the Internet
         address to be used as the source for performing an OAM ping 
         operation when tmnxOamPingCtlTestMode has a value of 'mfibPing'
         and the value of tmnxOamMfibPingCtlDestMacAddr is all zeros, in 
         which case this parameter is required.  If tmnxOamPingCtlTestMode is 
         different from 'mfibPing' this field is ignored."
    DEFVAL { ''H }
    ::= { tmnxOamMfibPingCtlEntry 6 }

tmnxOamMfibPingCtlDestAddrType       OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMfibPingCtlDestAddrType specifies the Internet
         multicast address type stored in tmnxOamMfibPingCtlDestAddress."
    DEFVAL { unknown }
    ::= { tmnxOamMfibPingCtlEntry 7 }

tmnxOamMfibPingCtlDestAddress       OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMfibPingCtlDestAddress specifies the Internet
         multicast address to be used as the destination for performing an 
         OAM ping operation when tmnxOamPingCtlTestMode has a value of 'mfibPing' 
         and the value of tmnxOamMfibPingCtlDestMacAddr is all zeros,
         in which case this parameter is required.  If tmnxOamPingCtlTestMode is 
         different from 'mfibPing' this field is ignored."
    DEFVAL { ''H }
    ::= { tmnxOamMfibPingCtlEntry 8 }

tmnxOamMfibPingCtlDestMacAddr       OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMfibPingCtlDestMacAddr specifies the IPv6
         multicast MAC address to be used as the destination for performing an
         OAM ping operation when tmnxOamPingCtlTestMode has a value of 'mfibPing'
         and objects tmnxOamMfibPingCtlSrcAddrType and
         tmnxOamMfibPingCtlDestAddrType have a value of 'unknown (0)',
         in which case this parameter is required.  If tmnxOamPingCtlTestMode is 
         different from 'mfibPing' this field is ignored."
    DEFVAL { '000000000000'H }
    ::= { tmnxOamMfibPingCtlEntry 9 }

--
--  Alcatel 7x50 SR series OAM CPE Ping Control Table
--
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable,
--  and the sparse dependent table, tmnxOamCpePingCtlTable.
--
--  Rows are created in the tmnxOamCpePingCtlTable only for those entries
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of
--  'cpePing'.
--
--  Deletion of a row in the tmnxOamPingCtlTable results in the
--  same fate for the row in the tmnxOamCpePingCtlTable.
--
tmnxOamCpePingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamCpePingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines  the  Alcatel  7x50  SR  OAM  CPE  Ping   Control Table for
         providing,  via  SNMP, the capability of performing Alcatel 7x50 SR
         OAM 'cpePing' test operations.

         The results of these tests are stored in the
         tmnxOamPingResultsTable  and  the tmnxOamPingHistoryTable. Rows are
         created in the tmnxOamCpePingCtlTable only for those entries in the
         tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of
        'cpePing'."
   ::= { tmnxOamPingObjs 13 }

tmnxOamCpePingCtlEntry     OBJECT-TYPE
    SYNTAX      TmnxOamCpePingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamCpePingCtlTable. The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString, a
         textual convention that allows for use of the SNMPv3 View-Based
         Access Control Model (RFC 2575 [11], VACM) and allows a management
         application to identify its entries.  The second index,
         tmnxOamPingCtlTestIndex, enables the same management application to
         have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamCpePingCtlTable 1 }

TmnxOamCpePingCtlEntry ::=
    SEQUENCE {
        tmnxOamCpePingCtlSourceIpAddr    IpAddress,
        tmnxOamCpePingCtlSendControl     TruthValue,
        tmnxOamCpePingCtlReplyControl    TruthValue,
        tmnxOamCpePingCtlTtl             Unsigned32,
        tmnxOamCpePingCtlSrceMacAddr     MacAddress,
        tmnxOamCpePingCtlSrcAddrType     InetAddressType,
        tmnxOamCpePingCtlSrcAddress      InetAddress
    }

tmnxOamCpePingCtlSourceIpAddr     OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 address to be used as the source for performing
         an CPE ping operation when tmnxOamPingCtlTestMode has a value of
         'cpePing'.  This parameter is required for 'cpePing' and ignored in
         all other cases.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamCpePingCtlSrcAddrType and 
         tmnxOamCpePingCtlSrcAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamCpePingCtlEntry 1 }

tmnxOamCpePingCtlSendControl      OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamCpePingCtlSendControl is 'true', the OAM
         ping packet is sent via the control plane.  If its value is 'false',
         the packet is sent via the data plane.
         This object is optional and is not relevant when tmnxOamPingCtlTestMode
         has a value other than 'cpePing'."
    DEFVAL { false }
    ::= { tmnxOamCpePingCtlEntry 2 }

tmnxOamCpePingCtlReplyControl     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamCpePingCtlReplyControl is 'true', the OAM
         ping response is returned using the control plane. If its value is
         'false', the packet is sent via the data plane.
         This object is optional and is not relevant when
         tmnxOamPingCtlTestMode has a value other than 'cpePing'."
    DEFVAL { false }
    ::= { tmnxOamCpePingCtlEntry 3 }

 tmnxOamCpePingCtlTtl              OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the vc-label time-to-live value. This object is optional
         and is not relevant when tmnxOamPingCtlTestMode has a value other
         than 'cpePing'. When performing the test with a ttl=1, the cpe-ping
         is only done to the local SAP(s)."
    DEFVAL { 255 }
    ::= { tmnxOamCpePingCtlEntry 4 }

tmnxOamCpePingCtlSrceMacAddr      OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When set to a non zero value, the system will use the value as
         source mac address in the ARP request that will be sent to the CPE.
         If set to 0, the MAC address configured for the CPM is used. This
         object is optional and is not relevant when tmnxOamPingCtlTestMode
         has a value other than 'cpePing'."
    DEFVAL { '000000000000'H }
    ::= { tmnxOamCpePingCtlEntry 5 }

tmnxOamCpePingCtlSrcAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamCpePingCtlSrcAddrType specifies the Internet
         address type stored in tmnxOamCpePingCtlSrcAddress."
    DEFVAL { unknown }
    ::= { tmnxOamCpePingCtlEntry 6 }

tmnxOamCpePingCtlSrcAddress     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamCpePingCtlSrcAddress specifies the Internet
         address to be used as the source for performing a CPE ping operation 
         when tmnxOamPingCtlTestMode has a value of 'cpePing'.  This parameter 
         is required for 'cpePing' and ignored in all other cases."
    DEFVAL { ''H }
    ::= { tmnxOamCpePingCtlEntry 7 }


--
-- Alcatel 7x50 SR series OAM Multicast Router Information Response Table
--
tmnxOamMRInfoRespTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMRInfoRespEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM Multicast Router Information Response
         Table for providing, via SNMP, the capability of performing 
         Alcatel 7x50 SR OAM 'mrinfo' test operations. The results of these 
         tests are stored in the tmnxOamPingResultsTable, the 
         tmnxOamPingHistoryTable and the tmnxOamMRInfoRespTable."
   ::= { tmnxOamPingObjs 14 }

tmnxOamMRInfoRespEntry OBJECT-TYPE
    SYNTAX      TmnxOamMRInfoRespEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMRInfoRespTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex,
             tmnxOamPingResultsTestRunIndex,
             tmnxOamPingHistoryIndex
          }
    ::= { tmnxOamMRInfoRespTable 1 }

TmnxOamMRInfoRespEntry ::=
    SEQUENCE {
        tmnxOamMRInfoRespCapabilities   BITS,
        tmnxOamMRInfoRespMinorVersion   Unsigned32,
        tmnxOamMRInfoRespMajorVersion   Unsigned32,
        tmnxOamMRInfoRespNumInterfaces  Unsigned32
    }

tmnxOamMRInfoRespCapabilities    OBJECT-TYPE
    SYNTAX      BITS {
                    leaf(0),
                    prune(1),
                    genid(2),
                    mtrace(3),
                    snmp(4)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespCapabilities indicates the capabilities of the router
         responding to the mrinfo request:
            leaf(0)     This is a leaf router
            prune(1)    This router understands pruning
            genid(2)    This router sends Generation Id's
            mtrace(3)   This router handles Mtrace requests
            snmp(4)     This router supports the DVMRP MIB
        "
    ::= { tmnxOamMRInfoRespEntry 1 }

tmnxOamMRInfoRespMinorVersion    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespMinorVersion indicates the minor software version
         on the router responding to the mrinfo request."
    ::= { tmnxOamMRInfoRespEntry 2 }

tmnxOamMRInfoRespMajorVersion    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespMajorVersion indicates the major software version
         on the router responding to the mrinfo request."
    ::= { tmnxOamMRInfoRespEntry 3 }

tmnxOamMRInfoRespNumInterfaces    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespNumInterfaces indicates the number of interfaces
         in the mrinfo response packet. These interfaces are listed in the 
         tmnxOamMRInfoRespIfTable."
    ::= { tmnxOamMRInfoRespEntry 4 }


--
-- Alcatel 7x50 SR series OAM Multicast Router Information Interface Table
--
tmnxOamMRInfoRespIfTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMRInfoRespIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM Multicast Router Information Interface
         Table for providing, via SNMP, the capability of performing Alcatel 
         7x50 SR OAM 'mrinfo' test operation. The results of these tests 
         are stored in the tmnxOamPingResultsTable, the 
         tmnxOamPingHistoryTable and the tmnxOamMRInfoRespIfTable."
   ::= { tmnxOamPingObjs 15 }

tmnxOamMRInfoRespIfEntry OBJECT-TYPE
    SYNTAX      TmnxOamMRInfoRespIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMRInfoRespIfTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex,
             tmnxOamPingResultsTestRunIndex,
             tmnxOamPingHistoryIndex,
             tmnxOamMRInfoRespIfIndex
          }
    ::= { tmnxOamMRInfoRespIfTable 1 }

TmnxOamMRInfoRespIfEntry ::=
    SEQUENCE {
        tmnxOamMRInfoRespIfIndex        Unsigned32,
        tmnxOamMRInfoRespIfAddress      IpAddress,
        tmnxOamMRInfoRespIfMetric       Unsigned32,
        tmnxOamMRInfoRespIfThreshold    Unsigned32,
        tmnxOamMRInfoRespIfFlags        BITS,
        tmnxOamMRInfoRespIfNbrCount     Unsigned32,
        tmnxOamMRInfoRespIfAddrType     InetAddressType,
        tmnxOamMRInfoRespIfAddr         InetAddress
    }

tmnxOamMRInfoRespIfIndex    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfIndex is the index into the 
         tmnxOamMRInfoRespIfTable.  The mrinfo response packet has 
         the router's interfaces on which multicast is enabled. 
         tmnxOamMRInfoRespIfIndex is used to identify those interfaces."
    ::= { tmnxOamMRInfoRespIfEntry 1 }

tmnxOamMRInfoRespIfAddress    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMRInfoRespIfAddress indicates the interface address on the
         router responding to the mrinfo request.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMRInfoRespIfAddrType and 
         tmnxOamMRInfoRespIfAddr." 
    ::= { tmnxOamMRInfoRespIfEntry 2 }

tmnxOamMRInfoRespIfMetric    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfMetric indicates the metric on the interface."
    ::= { tmnxOamMRInfoRespIfEntry 3 }

tmnxOamMRInfoRespIfThreshold    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfThreshold indicates the threshold on the 
         interface."
    ::= { tmnxOamMRInfoRespIfEntry 4 }

tmnxOamMRInfoRespIfFlags    OBJECT-TYPE
    SYNTAX      BITS {
                    tunnel(0),
                    srcrt(1),
                    reserved1(2),
                    reserved2(3),
                    down(4),
                    disabled(5),
                    querier(6),
                    leaf(7)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfFlags indicates the flags associated with 
         an interface:

         tunnel(0)         Neighbor reached via tunnel
         srcrt(1)          Tunnel uses IP source routing
         reserved1(2)      No longer used
         reserved2(3)      No longer used
         down(4)           Operational status down
         disabled(5)       Administrative status down
         querier(6)        Querier for interface
         leaf(7)           No downstream neighbors on interface        
        "
    ::= { tmnxOamMRInfoRespIfEntry 5 }

tmnxOamMRInfoRespIfNbrCount    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfNbrCount indicates the number of multicast neighbors
         on the interface. The neighbors are listed in the 
         tmnxOamMRInfoRespIfNbrTable."
    ::= { tmnxOamMRInfoRespIfEntry 6 }

tmnxOamMRInfoRespIfAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfAddrType indicates the Internet address type
         stored in tmnxOamMRInfoRespIfAddr."
    ::= { tmnxOamMRInfoRespIfEntry 7 }

tmnxOamMRInfoRespIfAddr    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfAddr indicates the Internet address of the 
         interface on the router responding to the mrinfo request."
    ::= { tmnxOamMRInfoRespIfEntry 8 }


--
-- Alcatel 7x50 SR series OAM Multicast Router Information If Neighbor Table
--

tmnxOamMRInfoRespIfNbrTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMRInfoRespIfNbrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM Multicast Router Information Interface
         Neighbor Table for providing, via SNMP, the capability of performing 
         Alcatel 7x50 SR OAM 'mrinfo' test operation. This table contains
         entries for neighbors on an interface. The results of the mrinfo test
         are stored in the tmnxOamPingResultsTable, the 
         tmnxOamPingHistoryTable, the tmnxOamMRInfoRespTable, 
         the tmnxOamMRInfoRespIfTable and the tmnxOamMRInfoRespIfNbrTable."
   ::= { tmnxOamPingObjs 16 }

tmnxOamMRInfoRespIfNbrEntry OBJECT-TYPE
    SYNTAX      TmnxOamMRInfoRespIfNbrEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMRInfoRespIfNbrTable.  The first index
        element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
        a textual convention that allows for use of the SNMPv3
        View-Based Access Control Model (RFC 2575 [11], VACM)
        and allows a management application to identify its entries.
        The second index, tmnxOamPingCtlTestIndex, enables the same 
        management application to have multiple outstanding requests."
    INDEX {
            tmnxOamPingCtlOwnerIndex,
            tmnxOamPingCtlTestIndex,
            tmnxOamPingResultsTestRunIndex,
            tmnxOamPingHistoryIndex,
            tmnxOamMRInfoRespIfIndex,
            tmnxOamMRInfoRespIfNbrIndex
          }
    ::= { tmnxOamMRInfoRespIfNbrTable 1 }

TmnxOamMRInfoRespIfNbrEntry ::=
    SEQUENCE {
       tmnxOamMRInfoRespIfNbrIndex     Unsigned32,
       tmnxOamMRInfoRespIfNbrAddress   IpAddress,
       tmnxOamMRInfoRespIfNbrAddrType  InetAddressType,
       tmnxOamMRInfoRespIfNbrAddr      InetAddress
    }

tmnxOamMRInfoRespIfNbrIndex    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfNbrIndex is used to identify the multicast neighbor
         on the interface tmnxOamMRInfoRespIfIndex."
    ::= { tmnxOamMRInfoRespIfNbrEntry 1 }

tmnxOamMRInfoRespIfNbrAddress    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMRInfoRespIfNbrAddress indicates the address of the neighbor 
         on the interface.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMRInfoRespIfNbrAddrType and 
         tmnxOamMRInfoRespIfNbrAddr." 
    ::= { tmnxOamMRInfoRespIfNbrEntry 2 }

tmnxOamMRInfoRespIfNbrAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfNbrAddrType indicates the Internet address type 
         stored in tmnxOamMRInfoRespIfNbrAddr."
    ::= { tmnxOamMRInfoRespIfNbrEntry 3 }

tmnxOamMRInfoRespIfNbrAddr    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMRInfoRespIfNbrAddr indicates the Internet address of the 
         neighbor on the interface."
    ::= { tmnxOamMRInfoRespIfNbrEntry 4 }


--
--  Alcatel 7750 SR series OAM VCCV Ping Control Table
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable, 
--  and the sparse dependent table, tmnxOamVccvPingCtlTable. 
--
--  This in effect extends the tmnxOamPingCtlTable with additional columns.
--  Rows are created in the tmnxOamVccvPingCtlTable only for those entries 
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of 
--  'vccvPing'.
--  
--  Deletion of a row in the tmnxOamPingCtlTable results in the 
--  deletion of the row in the tmnxOamVccvPingCtlTable.
--
tmnxOamVccvPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVccvPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7750 SR OAM VCCV Ping Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7750 SR OAM 'vccvPing'
         test operations.  
         The results of these tests are stored in the tmnxOamPingResultsTable 
         and the tmnxOamPingHistoryTable."
   ::= { tmnxOamPingObjs 17 }

tmnxOamVccvPingCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamVccvPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVccvPingCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamVccvPingCtlTable 1 }

TmnxOamVccvPingCtlEntry ::=
    SEQUENCE {
        tmnxOamVccvPingCtlSdpIdVcId       SdpBindId,
        tmnxOamVccvPingCtlReplyMode       INTEGER,
        tmnxOamVccvPingCtlPwId            TmnxVcIdOrNone,
        tmnxOamVccvPingCtlTtl             Unsigned32
    }

tmnxOamVccvPingCtlSdpIdVcId    OBJECT-TYPE
    SYNTAX      SdpBindId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvPingCtlSdpIdVcId specifies the SDP ID and
         the VC ID of the pseudowire to be used for performing a vccv-ping
         operation. This parameter is required only if tmnxOamPingCtlTestMode 
         has a value of 'vccvPing'.
             SDP ID: first 4 octets  
             VC ID:  remaining 4 octets 
         If the value of tmnxOamVccvPingCtlSdpIdVcId is invalid, or the
         pseudowire is administratively down, or unavailable, the OAM Echo
         request message probe is not sent and an appropriate error value is
         written to tmnxOamPingHistoryStatus for that probe entry. Once
         the interval timer expires, the next probe attempt will be made
         if required."
    DEFVAL { '0000000000000000'h }      -- invalid SdpId vc-id
    ::= { tmnxOamVccvPingCtlEntry 1 }
   
tmnxOamVccvPingCtlReplyMode OBJECT-TYPE
    SYNTAX          INTEGER {
                        ip(2),
                        controlChannel(4)                        
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamVccvPingCtlReplyMode as specified in 
         draft-ietf-mpls-lsp-ping-10.txt sets the method of reply
         due to the vccv-ping request message.

             ip(2)                 out-of-band reply 
             controlChannel(4)     inband reply
             
         This parameter is optional for vccv-ping."         
    DEFVAL { controlChannel }
    ::= { tmnxOamVccvPingCtlEntry 2 }

tmnxOamVccvPingCtlPwId    OBJECT-TYPE
    SYNTAX          TmnxVcIdOrNone 
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION
        "The value of tmnxOamVccvPingCtlPwId specifies the pseudowire Id 
         to be used for performing a vccv-ping operation. The pseudowire
         Id is a non-zero 32-bit connection ID required by the FEC 128, as
         defined in RFE 4379. This object is only valid when used in 
         conjunction with valid tmnxOamPingCtlTgtAddress and 
         tmnxOamPingCtlSrcAddress.  A value of 0 indicates that no VC ID
         is configured or available."
    DEFVAL { 0 }
    ::= { tmnxOamVccvPingCtlEntry 3 }

tmnxOamVccvPingCtlTtl    OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvPingCtlTtl specifies the time-to-live value 
         for the vc-label of the echo request message. The outer label TTL is 
         still set to the default of 255 regardless of this value."
    DEFVAL { 1 }
    ::= { tmnxOamVccvPingCtlEntry 4 }

--
--  Alcatel 7750 SR series OAM ICMP Ping Control Table
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable, 
--  and the sparse dependent table, tmnxOamIcmpPingCtlTable. 
--
--  This in effect extends the tmnxOamPingCtlTable with additional columns.
--  Rows are created in the tmnxOamIcmpPingCtlTable only for those entries 
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of 
--  'icmpPing'.
--  
--  Deletion of a row in the tmnxOamPingCtlTable results in the 
--  deletion of the row in the tmnxOamIcmpPingCtlTable.
--
tmnxOamIcmpPingCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamIcmpPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7750 SR OAM ICMP Ping Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7750 SR OAM 'icmpPing'
         test operations.  
         The results of these tests are stored in the tmnxOamPingResultsTable 
         and the tmnxOamPingHistoryTable."
   ::= { tmnxOamPingObjs 18 }

tmnxOamIcmpPingCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamIcmpPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamIcmpPingCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamIcmpPingCtlTable 1 }

TmnxOamIcmpPingCtlEntry ::=
    SEQUENCE {
        tmnxOamIcmpPingCtlRapid             TruthValue,
        tmnxOamIcmpPingCtlTtl               Unsigned32,
        tmnxOamIcmpPingCtlDSField           Unsigned32,
        tmnxOamIcmpPingCtlPattern           Integer32,
        tmnxOamIcmpPingCtlNhAddrType        InetAddressType,
        tmnxOamIcmpPingCtlNhAddress         InetAddress,
        tmnxOamIcmpPingCtlEgrIfIndex        InterfaceIndexOrZero,
        tmnxOamIcmpPingCtlBypassRouting     TruthValue,
        tmnxOamIcmpPingCtlDoNotFragment     TruthValue
    }

tmnxOamIcmpPingCtlRapid    OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamIcmpPingCtlRapid specifies whether or
         not to send ICMP ping probes in rapid sequence.
         
         When tmnxOamIcmpPingCtlRapid has the value 'true', the UNITS
         value for tmnxOamPingCtlInterval is changed from 'seconds' to
         '10 milliseconds'."
    DEFVAL { false }
    ::= { tmnxOamIcmpPingCtlEntry 1 }
   
tmnxOamIcmpPingCtlTtl   OBJECT-TYPE
    SYNTAX          Unsigned32 (1..128)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamIcmpPingCtlTtl specifies the initial
         time-to-live value for the ICMP ping packets."         
    DEFVAL { 64 }
    ::= { tmnxOamIcmpPingCtlEntry 2 }

tmnxOamIcmpPingCtlDSField   OBJECT-TYPE
    SYNTAX          Unsigned32 (0..255)
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamIcmpPingCtlDSField specifies the value to store
         in the Differentiated Services (DS) Field in the IP packet used 
         to encapsulate the Alcatel 7x50 SR OAM ping probe.  The DS Field 
         is defined as the Type of Service (TOS) octet in a IPv4 header or 
         as the Traffic Class octet in a IPv6 header.
    
         The value of this object must be a decimal integer in the range 
         from 0 to 255.  This option can be used to determine what effect 
         an explicit DS Field setting has on a OAM ping response.  Not 
         all values are legal or meaningful.  DS Field usage is often not 
         supported by IP implementations.  A value of 0 means that the
         function represented by this option is not supported.  Well known 
         TOS octet values are '16' (low delay) and '8' (high throughput)."
    REFERENCE
        "Refer to RFC 2474 for the definition of the Differentiated Services 
         Field and to RFC 1812 Section 5.3.2 for Type of Service (TOS)."
    DEFVAL { 0 }
    ::= { tmnxOamIcmpPingCtlEntry 3 }

tmnxOamIcmpPingCtlPattern   OBJECT-TYPE
    SYNTAX      Integer32 (-1|0..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamIcmpPingCtlPattern specifies a pattern to
         be repeated to fill the data field of the ICMP ping packet.
         When the value -1 is specified, the data field will be filled
         with positional values."
    DEFVAL { -1 }
    ::= { tmnxOamIcmpPingCtlEntry 4 }

tmnxOamIcmpPingCtlNhAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamIcmpPingCtlNhAddrType specifies the address type 
         of the tmnxOamIcmpPingCtlNhAddress Internet address."
    DEFVAL { unknown }
    ::= { tmnxOamIcmpPingCtlEntry 5 }

tmnxOamIcmpPingCtlNhAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamIcmpPingCtlNhAddress specifies the Internet
         address of the next-hop.
         
         When tmnxOamIcmpPingCtlBypassRouting has a value of 'true'
         or tmnxOamIcmpPingCtlEgrIfIndex has a value not equal 0,
         an attempt to set tmnxOamIcmpPingCtlNhAddress to a value
         other than ''H will fail with an inconsistentValue error.
         
         Only one of tmnxOamIcmpPingCtlNhAddress, tmnxOamIcmpPingCtlEgrIfIndex,
         or tmnxOamIcmpPingCtlBypassRouting may be set to a non-default
         value."
    DEFVAL { ''H }
    ::= { tmnxOamIcmpPingCtlEntry 6 }

tmnxOamIcmpPingCtlEgrIfIndex    OBJECT-TYPE
    SYNTAX      InterfaceIndexOrZero
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamIcmpPingCtlEgrIfIndex specifies the 
         ifIndex of the interface to use to transmit the ICMP ping 
         packets.
         
         When tmnxOamIcmpPingCtlBypassRouting has a value of 'true',
         an attempt to set tmnxOamIcmpPingCtlEgrIfIndex to a value
         other than 0 will fail with an inconsistentValue error.

         Only one of tmnxOamIcmpPingCtlNhAddress, tmnxOamIcmpPingCtlEgrIfIndex,
         or tmnxOamIcmpPingCtlBypassRouting may be set to a non-default
         value."
    DEFVAL { 0 }
    ::= { tmnxOamIcmpPingCtlEntry 7 }

tmnxOamIcmpPingCtlBypassRouting     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamIcmpPingCtlBypassRouting specifies whether 
         to send the ping request to a host on a directly attached network 
         bypassing the routing table.
        
         An attempt to set tmnxOamIcmpPingCtlBypassRouting to 'true'
         will fail with an inconsistentValue error if 
         tmnxOamIcmpPingCtlNhAddress does not have the value ''H or
         tmnxOamIcmpPingCtlEgrIfIndex is not equal 0.

         Only one of tmnxOamIcmpPingCtlNhAddress, tmnxOamIcmpPingCtlEgrIfIndex,
         or tmnxOamIcmpPingCtlBypassRouting may be set to a non-default
         value."
    DEFVAL { false }
    ::= { tmnxOamIcmpPingCtlEntry 8 }

tmnxOamIcmpPingCtlDoNotFragment     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamIcmpPingCtlDoNotFragment specifies whether or
         not the 'DF' (Don't Fragment) bit is set in the ICMP ping packet.
         
         When tmnxOamIcmpPingCtlDoNotFragment has the value 'false',
         the 'DF' bit is not set."
    DEFVAL { false }
    ::= { tmnxOamIcmpPingCtlEntry 9 }


--  Alcatel 7750 SR series OAM ANCP Control Table
--
--  Sparse Dependent Extension of the tmnxOamPingCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamPingCtlTable,
--  and the sparse dependent table, tmnxOamAncpTestCtlTable.
--
--  This in effect extends the tmnxOamPingCtlTable with additional columns.
--  Rows are created in the tmnxOamAncpTestCtlTable only for those entries
--  in the tmnxOamPingCtlTable where tmnxOamPingCtlTestMode has a value of
--  'ancpLoopback'.
--
--  Deletion of a row in the tmnxOamPingCtlTable results in the
--  deletion of the row in the tmnxOamAncpTestCtlTable.
--
tmnxOamAncpTestCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamAncpTestCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7750 SR OAM ANCP Control Table for providing,
         via SNMP, the capability of performing Alcatel 7750 SR OAM
         'ANCP loopback' test operations.
         The results of these tests are stored in the tmnxOamPingResultsTable
         and the tmnxOamAncpTestHistoryTable.  There will be no entries for
         these test in the tmnxOamPingHistoryTable."
   ::= { tmnxOamPingObjs 19 }

tmnxOamAncpTestCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamAncpTestCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamAncpTestCtlTable.  The first index
         element, tmnxOamPingCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamPingCtlTestIndex, enables the same
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex
          }
    ::= { tmnxOamAncpTestCtlTable 1 }

TmnxOamAncpTestCtlEntry ::=
    SEQUENCE {
        tmnxOamAncpTestTarget     INTEGER,
        tmnxOamAncpTestTargetId   DisplayString,
        tmnxOamAncpTestcount      INTEGER,
        tmnxOamAncpTestTimeout    INTEGER
    }

tmnxOamAncpTestTarget  OBJECT-TYPE
    SYNTAX      INTEGER {
                    none         (0),
                    subscriberId (1),
                    ancpString   (2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The object tmnxOamAncpTestTarget specifies how to interprete the
         value of the object tmnxOamAncpTestTargetId.
         If set to 'subscriberId', the object tmnxOamAncpTestTargetId
         is a printable character string which contains the subscriber-id.
         If set to 'ancpString', the object tmnxOamAncpTestTargetId
         is a printable character string which contains the ancp-string.
         If set to 'none', no value is specified, the object
         tmnxOamAncpTestTargetId is an empty string, and no ancp-loopback test
         can be performed."
    DEFVAL { none }
    ::= { tmnxOamAncpTestCtlEntry 1 }

tmnxOamAncpTestTargetId  OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..63))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The object tmnxOamAncpTestTargetId contains the ID of the subscriber
         for which the ANCP loopback test must be performed. The content of the
         field is interpreted as being a subscriber-id of max 32 chars
         (in case the object tmnxOamAncpTestTarget is set to 'subscriberId'),
         or as being an acnp-string of max 63 chars (in case the object
         tmnxOamAncpTestTarget is set to 'ancpString')."
    DEFVAL { "" }
    ::= { tmnxOamAncpTestCtlEntry 2 }

tmnxOamAncpTestcount  OBJECT-TYPE
    SYNTAX      INTEGER (0 .. 32)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamAncpTestcount specifies the number of messages the
         access node will use to test the circuit.
         If set to 0, the number of messages will be determined by the
         access node. Following settings are allowed:
         tmnxOamAncpTestcount   tmnxOamAncpTestTimeout
                0                   0
                1..32               0
                1..32               1..255
        "
    DEFVAL { 0 }
    ::= { tmnxOamAncpTestCtlEntry 3 }

tmnxOamAncpTestTimeout  OBJECT-TYPE
    SYNTAX      INTEGER (0 .. 255)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamAncpTestTimeout specifies the number of seconds
          the controlling node will wait for a reply. This timeout value
          is also sent to the access node. If set to 0, the access node will
          pick a default value, while the control node will assume a value
          of 255 seconds.

          Following settings are allowed:
          tmnxOamAncpTestcount   tmnxOamAncpTestTimeout
                0                   0
                1..32               0
                1..32               1..255
        "
    DEFVAL { 0 }
    ::= { tmnxOamAncpTestCtlEntry 4 }

 --
 -- Alcatel 7x50 SR series OAM ANCP History Table
 --
tmnxOamAncpTestHistoryTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamAncpTestHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM ANCP test.
         The number of entries in this table is limited by the value of the
         corresponding tmnxOamPingCtlMaxRows object.

         An entry in this table is created when the result of an OAM
         ANCP test is determined.  An entry  is removed from this table when
         its corresponding tmnxOamPingCtlEntry is deleted.

         The agent removes the oldest entry for a test in the
         tmnxOamAncpTestHistoryTable to allow the addition of an new
         entry for that test once the number of rows in the
         tmnxOamAncpTestHistoryTable reaches the value specified by
         tmnxOamPingCtlMaxRows."
   ::= { tmnxOamPingObjs 20 }

tmnxOamAncpTestHistoryEntry  OBJECT-TYPE
    SYNTAX      TmnxOamAncpTestHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamAncpTestHistoryTable. The first two
         index elements identify the tmnxOamPingCtlEntry that a
         tmnxOamAncpTestHistoryTable belongs to. The third index identifies
         a specific run of the OAM test. The fourth index element selects a
         single OAM ANCP test result."
    INDEX {
             tmnxOamPingCtlOwnerIndex,
             tmnxOamPingCtlTestIndex,
             tmnxOamPingResultsTestRunIndex,
             tmnxOamAncpHistoryIndex
           }
    ::= { tmnxOamAncpTestHistoryTable 1 }

TmnxOamAncpTestHistoryEntry ::=
    SEQUENCE {
        tmnxOamAncpHistoryIndex         Unsigned32,
        tmnxOamAncpHistoryAncpString    DisplayString,
        tmnxOamAncpHistoryAccNodeCode   Unsigned32,
        tmnxOamAncpHistoryAccNodeResult Unsigned32,
        tmnxOamAncpHistoryAccNodeRspStr DisplayString
    }

tmnxOamAncpHistoryIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created when the result of a OAM
         ANCP test is determined. The initial 2 instance
         identifier index values identify the tmnxOamPingCtlEntry
         that This ANCP test result belongs
         to.  The tmnxOamAncpHistoryIndex element selects a single OAM
         probe result.

         The agent starts assigning tmnxOamAncpHistoryIndex values at 1
         and wraps after exceeding the maximum possible value as defined by
         the limit of this object ('ffffffff'h)."
    ::= { tmnxOamAncpTestHistoryEntry 1 }

tmnxOamAncpHistoryAncpString  OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..63))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The object tmnxOamAncpHistoryAncpString indicates the value of the
         ancp-string used while running this ANCP test."
    ::= { tmnxOamAncpTestHistoryEntry 2 }

tmnxOamAncpHistoryAccNodeCode  OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The object tmnxOamAncpHistoryAccNodeCode indicates, if different from 0,
         the return code received from the ANCP access node.
         The value of this field corresponds to the errorcodes defined in the
         draft-wadhwa-gsmp-l2control-configuration-01:
         - 0x500: specified access line doe not exist
         - 0x501 Loopback test timed out
         - 0x502 Reserved
         - 0x503 DSL line status showtime
         - 0x504 DSL line status idle
         - 0x505 DSL line status silent
         - 0x506 DSL line status training
         - 0x507 DSL line integrity error
         - 0x508 DSLAM resource not available
         - 0x509 Invalid test parameter"
    ::= { tmnxOamAncpTestHistoryEntry 3 }

tmnxOamAncpHistoryAccNodeResult  OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The object tmnxOamAncpHistoryAccNodeResult indicates, if different
         from 0, the return result received from the ANCP access node.
         The value of this field corresponds to the result codes defined in the
         draft-wadhwa-gsmp-l2control-configuration-01:
         {
         - 1: NoSuccessAck
         - 2: AckAll
         - 3: Sucecss
         - 4: Failure
         - 5: More
         - 6: ReturnReceipt
         }"
    ::= { tmnxOamAncpTestHistoryEntry 4 }

tmnxOamAncpHistoryAccNodeRspStr  OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The object tmnxOamAncpHistoryAccNodeRspStr indicates, if different
         from the empty string, the OAM-loopback-Test-Response-String received
         from the access node, as reply to the requested test."
    ::= { tmnxOamAncpTestHistoryEntry 5 }

 --
 -- Notification Definition section
 --
 
tmnxOamPingProbeFailed NOTIFICATION-TYPE
    OBJECTS {
--       tmnxOamPingCtlTargetAddressType,
--       tmnxOamPingCtlTargetAddress,
       tmnxOamPingCtlTargetIpAddress,
       tmnxOamPingResultsOperStatus,
       tmnxOamPingResultsMinRtt,
       tmnxOamPingResultsMaxRtt,
       tmnxOamPingResultsAverageRtt,
       tmnxOamPingResultsRttSumOfSquares,
       tmnxOamPingResultsMtuResponseSize,
       tmnxOamPingResultsSvcPing,
       tmnxOamPingResultsProbeResponses,
       tmnxOamPingResultsSentProbes,
       tmnxOamPingResultsLastGoodProbe
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a probe failure is detected when the
         corresponding tmnxOamPingCtlTrapGeneration object is set to
         probeFailure(0) subject to the value of
         tmnxOamPingCtlTrapProbeFailureFilter.  The object
         tmnxOamPingCtlTrapProbeFailureFilter can be used to specify the
         number of successive probe failures that are required
         before this notification can be generated."
    ::= { tmnxOamPingNotifications 1 }

tmnxOamPingTestFailed NOTIFICATION-TYPE
    OBJECTS {
--       tmnxOamPingCtlTargetAddressType,
--       tmnxOamPingCtlTargetAddress,
        tmnxOamPingCtlTargetIpAddress,
        tmnxOamPingResultsOperStatus,
        tmnxOamPingResultsMinRtt,
        tmnxOamPingResultsMaxRtt,
        tmnxOamPingResultsAverageRtt,
        tmnxOamPingResultsRttSumOfSquares,
        tmnxOamPingResultsMtuResponseSize,
        tmnxOamPingResultsSvcPing,
        tmnxOamPingResultsProbeResponses,
        tmnxOamPingResultsSentProbes,
        tmnxOamPingResultsLastGoodProbe
      }
    STATUS  obsolete
    DESCRIPTION
        "Generated when a ping test is determined to have failed
         when the corresponding tmnxOamPingCtlTrapGeneration object is
         set to testFailure(1).  In this instance
         tmnxOamPingCtlTrapTestFailureFilter should specify the number of
         probes in a test required to have failed in order to
         consider the test as failed."
    ::= { tmnxOamPingNotifications 2 }

tmnxOamPingTestCompleted NOTIFICATION-TYPE
    OBJECTS {
--       tmnxOamPingCtlTargetAddressType,
--       tmnxOamPingCtlTargetAddress,
     tmnxOamPingCtlTargetIpAddress,
     tmnxOamPingResultsOperStatus,
     tmnxOamPingResultsMinRtt,
     tmnxOamPingResultsMaxRtt,
     tmnxOamPingResultsAverageRtt,
     tmnxOamPingResultsRttSumOfSquares,
     tmnxOamPingResultsMtuResponseSize,
     tmnxOamPingResultsSvcPing,
     tmnxOamPingResultsProbeResponses,
     tmnxOamPingResultsSentProbes,
     tmnxOamPingResultsLastGoodProbe
    }
    STATUS  obsolete
    DESCRIPTION
        "Generated at the completion of a ping test when the
         corresponding tmnxOamPingCtlTrapGeneration object is set to
         testCompletion(2)."
    ::= { tmnxOamPingNotifications 3 }

tmnxOamPingProbeFailedV2 NOTIFICATION-TYPE
    OBJECTS {
       tmnxOamPingCtlTgtAddrType,
       tmnxOamPingCtlTgtAddress,
       tmnxOamPingResultsOperStatus,
       tmnxOamPingResultsMinRtt,
       tmnxOamPingResultsMaxRtt,
       tmnxOamPingResultsAverageRtt,
       tmnxOamPingResultsRttSumOfSquares,
       tmnxOamPingResultsMtuResponseSize,
       tmnxOamPingResultsSvcPing,
       tmnxOamPingResultsProbeResponses,
       tmnxOamPingResultsSentProbes,
       tmnxOamPingResultsLastGoodProbe
    }
    STATUS  current
    DESCRIPTION
        "Generated when a probe failure is detected when the
         corresponding tmnxOamPingCtlTrapGeneration object is set to
         probeFailure(0) subject to the value of
         tmnxOamPingCtlTrapProbeFailureFilter.  The object
         tmnxOamPingCtlTrapProbeFailureFilter can be used to specify the
         number of successive probe failures that are required
         before this notification can be generated."
    ::= { tmnxOamPingNotifications 4 }

tmnxOamPingTestFailedV2 NOTIFICATION-TYPE
    OBJECTS {
        tmnxOamPingCtlTgtAddrType,
        tmnxOamPingCtlTgtAddress,
        tmnxOamPingResultsOperStatus,
        tmnxOamPingResultsMinRtt,
        tmnxOamPingResultsMaxRtt,
        tmnxOamPingResultsAverageRtt,
        tmnxOamPingResultsRttSumOfSquares,
        tmnxOamPingResultsMtuResponseSize,
        tmnxOamPingResultsSvcPing,
        tmnxOamPingResultsProbeResponses,
        tmnxOamPingResultsSentProbes,
        tmnxOamPingResultsLastGoodProbe
      }
    STATUS  current
    DESCRIPTION
        "Generated when a ping test is determined to have failed
         when the corresponding tmnxOamPingCtlTrapGeneration object is
         set to testFailure(1).  In this instance
         tmnxOamPingCtlTrapTestFailureFilter should specify the number of
         probes in a test required to have failed in order to
         consider the test as failed."
    ::= { tmnxOamPingNotifications 5 }

tmnxOamPingTestCompletedV2 NOTIFICATION-TYPE
    OBJECTS {
     tmnxOamPingCtlTgtAddrType,
     tmnxOamPingCtlTgtAddress,
     tmnxOamPingResultsOperStatus,
     tmnxOamPingResultsMinRtt,
     tmnxOamPingResultsMaxRtt,
     tmnxOamPingResultsAverageRtt,
     tmnxOamPingResultsRttSumOfSquares,
     tmnxOamPingResultsMtuResponseSize,
     tmnxOamPingResultsSvcPing,
     tmnxOamPingResultsProbeResponses,
     tmnxOamPingResultsSentProbes,
     tmnxOamPingResultsLastGoodProbe
    }
    STATUS  current
    DESCRIPTION
        "Generated at the completion of a ping test when the
         corresponding tmnxOamPingCtlTrapGeneration object is set to
         testCompletion(2)."
    ::= { tmnxOamPingNotifications 6 }

tmnxAncpLoopbackTestCompleted NOTIFICATION-TYPE
   OBJECTS {
       tmnxOamAncpHistoryAncpString
   }
   STATUS  current
   DESCRIPTION
       "This Notification is sent whenever a ANCP loopback
        is finished for which a notification was explictly requested."
   ::= { tmnxOamPingNotifications 7 }

 --
 -- Alcatel 7x50 SR series OAM Trace Route Simple Object Definitions
 --
tmnxOamTrMaxConcurrentRequests OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "requests"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
       "The maximum number of concurrent active trace route requests
        that are allowed within an agent implementation.  A value
        of 0 for this object implies that there is no limit for
        the number of concurrent active requests in effect."
    DEFVAL { 0 }
    ::= { tmnxOamTraceRouteObjs 2 }

--
-- Alcatel 7x50 SR series OAM Traceroute Control Table
--
  
tmnxOamTrCtlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR OAM Trace Route Control Table for
         providing the capability of invoking OAM trace route tests from 
         via SNMP.  The results of trace route operations can be stored in
         the tmnxOamTrResultsTable, tmnxOamTrProbeHistoryTable, and
         the tmnxOamTrHopsTable."
   ::= { tmnxOamTraceRouteObjs 3 }

tmnxOamTrCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamTrCtlTable.  The first
         index element, tmnxOamTrCtlOwnerIndex, is of type
         SnmpAdminString, a textual convention that allows for
         use of the SNMPv3 View-Based Access Control Model
         (RFC 2575 [11], VACM) and allows an management
         application to identify its entries.  The second index,
         tmnxOamTrCtlTestIndex (also an SnmpAdminString),
         enables the same management application to have
         multiple requests outstanding."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamTrCtlTable 1 }

TmnxOamTrCtlEntry ::=
    SEQUENCE {
     tmnxOamTrCtlOwnerIndex         SnmpAdminString,
     tmnxOamTrCtlTestIndex          SnmpAdminString,
     tmnxOamTrCtlRowStatus          RowStatus,
     tmnxOamTrCtlStorageType        StorageType,
     tmnxOamTrCtlDescr              SnmpAdminString,
     tmnxOamTrCtlTestMode           INTEGER,
     tmnxOamTrCtlAdminStatus        INTEGER,
     tmnxOamTrCtlFcName             TFCName,
     tmnxOamTrCtlProfile            TProfile,
     tmnxOamTrCtlTargetIpAddress    IpAddress,
     tmnxOamTrCtlServiceId          TmnxServId,
     tmnxOamTrCtlDataSize           Unsigned32,
     tmnxOamTrCtlTimeOut            Unsigned32,
     tmnxOamTrCtlProbesPerHop       Unsigned32,
     tmnxOamTrCtlMaxTtl             Unsigned32,
     tmnxOamTrCtlInitialTtl         Unsigned32,
     tmnxOamTrCtlDSField            Unsigned32,
     tmnxOamTrCtlMaxFailures        Unsigned32,
     tmnxOamTrCtlInterval           Unsigned32,
     tmnxOamTrCtlMaxRows            Unsigned32,
     tmnxOamTrCtlTrapGeneration     BITS,
     tmnxOamTrCtlCreateHopsEntries  TruthValue,
     tmnxOamTrCtlSAA                TruthValue,
     tmnxOamTrCtlRuns               Counter32,
     tmnxOamTrCtlFailures           Counter32,
     tmnxOamTrCtlLastRunResult      INTEGER,
     tmnxOamTrCtlLastChanged        TimeStamp,
     tmnxOamTrCtlVRtrID             TmnxVRtrID,
     tmnxOamTrCtlTgtAddrType        InetAddressType,
     tmnxOamTrCtlTgtAddress         InetAddress,
     tmnxOamTrCtlSrcAddrType        InetAddressType,
     tmnxOamTrCtlSrcAddress         InetAddress,
     tmnxOamTrCtlWaitMilliSec       Unsigned32
    }

tmnxOamTrCtlOwnerIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(1..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "To facilitate the provisioning of access control by a
        security administrator using the View-Based Access
        Control Model (RFC 2575, VACM) for tables in which
        multiple users may need to independently create or
        modify entries, the initial index is used as an 'owner
        index'.  Such an initial index has a syntax of
        SnmpAdminString, and can thus be trivially mapped to a
        security name or group name as defined in VACM, in
        accordance with a security policy.
 
        When used in conjunction with such a security policy
        all entries in the table belonging to a particular user
        (or group) will have the same value for this initial
        index.  For a given user's entries in a particular
        table, the object identifiers for the information in
        these entries will have the same subidentifiers (except
        for the 'column' subidentifier) up to the end of the
        encoded owner index. To configure VACM to permit access
        to this portion of the table, one would create
        vacmViewTreeFamilyTable entries with the value of
        vacmViewTreeFamilySubtree including the owner index
        portion, and vacmViewTreeFamilyMask 'wildcarding' the
        column subidentifier.  More elaborate configurations
        are possible."
    ::= { tmnxOamTrCtlEntry 1 }

tmnxOamTrCtlTestIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(1..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The test name index of a Alcatel 7x50 SR OAM Trace Route test.  
         This is locally unique, within the scope of a tmnxOamTrCtlOwnerIndex."
    ::= { tmnxOamTrCtlEntry 2 }

tmnxOamTrCtlRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object allows entries to be created and deleted
         in the tmnxOamTrCtlTable.  Deletion of an entry in
         this table results in all corresponding (same
         tmnxOamTrCtlOwnerIndex and tmnxOamTrCtlTestIndex
         index values) tmnxOamTrResultsTable,
         tmnxOamTrProbeHistoryTable, and tmnxOamTrHopsTable
         entries being deleted.

         The values for configuration objects required for the type of
         test specified in tmnxOamTrCtlTestMode MUST be specified
         prior to a transition to active(1) state being
         accepted.

         Activation of an Alcatel 7x50 SR OAM Trace Route operation is
         controlled via tmnxOamTrCtlAdminStatus and not
         by transitioning of this object's value to active(1).
  
         Transitions in and out of active(1) state are not
         allowed while an entry's tmnxOamTrResultsOperStatus
         is active(1) with the exception that deletion of
         an entry in this table by setting its RowStatus
         object to destroy(6) will stop an active Alcatel
         7x50 SR OAM Trace Route operation.
  
         The operational state of an Alcatel 7x50 SR OAM Trace Route
         operation can be determined by examination of the corresponding
         tmnxOamTrResultsOperStatus object."
    REFERENCE
        "See definition of RowStatus in RFC 2579, 'Textual
        Conventions for SMIv2.'"
    ::= { tmnxOamTrCtlEntry 3 }

tmnxOamTrCtlStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The storage type for this conceptual row.
         Conceptual rows having the value 'permanent' need not
         allow write-access to any columnar objects in the row."
    DEFVAL { volatile }
    ::= { tmnxOamTrCtlEntry 4 }

tmnxOamTrCtlDescr OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The purpose of this object is to provide a descriptive name of 
         the Alcatel 7x50 SR OAM Trace Route test."
    DEFVAL { '00'H }
    ::= { tmnxOamTrCtlEntry 5 }

tmnxOamTrCtlTestMode     OBJECT-TYPE
    SYNTAX      INTEGER {
                    macTraceRoute (1),
                    lspTraceRoute (2),
                    vprnTraceRoute (3),
                    mcastTraceRoute (4),
                    icmpTraceRoute (5),
                    ldpTreeTrace (6),
                    vccvTraceRoute (7)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the type of Alcatel 7x50 SR OAM Trace Route test defined 
         by this entry.  The configuration parameters unique to a specific
         test type are to be found in a sparsely dependent table extension 
         for that test type.

         This is a required parameter."
    DEFVAL { macTraceRoute }
    ::= { tmnxOamTrCtlEntry 6 }

tmnxOamTrCtlAdminStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                          enabled(1), -- operation should be started
                          disabled(2) -- operation should be stopped
                        }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Reflects the desired state that a tmnxOamTrCtlEntry
         should be in:
           enabled(1)  - Attempt to activate the test as defined by
                         this tmnxOamTrCtlEntry.
           disabled(2) - Deactivate the test as defined by this
                         tmnxOamTrCtlEntry.

         If tmnxOamTrCtlSAA has the value 'true' and tmnxOamSaaCtlAdminStatus
         has the value 'outOfService', an attempt to set this object to 
         'enabled' will fail with an inconsistentValue error.

         Refer to the corresponding tmnxOamTrResultsOperStatus to
         determine the operational state of the test defined by
         this entry."
     DEFVAL { disabled }
    ::= { tmnxOamTrCtlEntry 7 }

tmnxOamTrCtlFcName       OBJECT-TYPE
    SYNTAX      TFCName
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlFcName specifies the forwarding class.
         This parameter is optional.  For 'macTraceRoute this is the 
         forwarding class of the SDP encapsulation.  For 'lspTraceRoute' 
         this is the forwarding class of the LSP tunnel.  

         The forwarding class name must be one of those defined in the
         tFCNameTable in ALCATEL-IND1-TIMETRA-QOS-MIB.  The agent creates predefined
         entries in the tFCNameTable for 'premium', 'assured', and 'be'
         (for best-effort) forwarding classes.  The actual forwarding
         class encoding is controlled by the network egress DSCP or
         LSP-EXP mappings.
         
         This parameter is not defined for 'vprnTraceRoute', 
         'mcastTraceRoute' and 'icmpTraceRoute'."  
    DEFVAL { "be" }
    ::= { tmnxOamTrCtlEntry 8 }

tmnxOamTrCtlProfile      OBJECT-TYPE
    SYNTAX      TProfile
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the profile value to be used with the forwarding
         class specified in tmnxOamTrCtlFcName.  This parameter is optional.
         
         The profile value must be consistent with the specified forwarding 
         class:
            'assured' = 'in' or 'out'
            'premium' = 'in'
            'be' = 'out'
             
         This parameter is not used by 'vprnTraceRoute, 'mcastTraceRoute',
         or 'icmpTraceRoute'."  
     DEFVAL { out }
     ::= { tmnxOamTrCtlEntry 9 }

tmnxOamTrCtlTargetIpAddress  OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 address to be used as the destination for
         performing an OAM Trace Route operation.  This parameter is
         not used by 'macTraceRoute' or 'lspTraceRoute' or 'mcastTraceRoute'.
         This parameter is required by 'vprnTraceRoute'.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamTrCtlTgtAddrType and 
         tmnxOamTrCtlTgtAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamTrCtlEntry 10 }

tmnxOamTrCtlServiceId        OBJECT-TYPE
    SYNTAX      TmnxServId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the Service-ID of the service being tested.  This parameter
         is required when the value of tmnxOamTrCtlTestMode is equal to 
         'macTraceRoute' or 'vprnTraceRoute'.
         
         In the case of 'macTraceRoute' the Service-ID need not exist on 
         the local node in order to receive a reply message if the far-end 
         target IP address is specified in tmnxOamTrCtlTgtAddress."
    DEFVAL { 0 } -- invalid Service-ID
    ::= { tmnxOamTrCtlEntry 11 }

tmnxOamTrCtlDataSize OBJECT-TYPE
    SYNTAX      Unsigned32 (1..9198)
    UNITS       "octets"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlDataSize specifies the size of the 
         data portion of an Alcatel 7x50 SR OAM Trace Route request in octets.  
         The size of the message does not include the SDP encapsulation, 
         VC-Lable (if applied) or any DLC headers or trailers.
         
         When the OAM message is encapsulated in an IP/GRE SDP, the
         IP 'DF' (Don't Fragment) bit is set.  If any segment of the path
         between the sender and receiver cannot handle the message size,
         the message is discarded.  MPLS LSPs are not expected to fragment
         the message either, as the message contained in the LSP is not an
         IP packet. This parameter is optional.

         In the case of 'lspTraceRoute' the minimum size is 104 octets.
         In the case of 'lspTraceRoute' with multipath DSMap the minimum size
         is 140 octets. 

         In the case of 'macTraceRoute' the minimum size is 1 octet.
         In the case of 'vprnTraceRoute' the minimum size is 1 octet.
         In the case of 'vccvTraceRoute' the minimum size is 88 octets.
    
         This parameter is not used by  'mcastTraceRoute' or 'icmpTraceRoute'."
    DEFVAL { 1 }
    ::= { tmnxOamTrCtlEntry 12 }

tmnxOamTrCtlTimeOut OBJECT-TYPE
    SYNTAX      Unsigned32 (1..60)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the time-out value, in seconds, for an Alcatel 7x50 SR
         OAM Trace Route request.  This parameter is optional.
         
         This parameter is not used by 'icmpTraceRoute'."
    DEFVAL { 3 }
    ::= { tmnxOamTrCtlEntry 13 }

tmnxOamTrCtlProbesPerHop OBJECT-TYPE
    SYNTAX      Unsigned32 (1..10)
    UNITS       "probes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Specifies the number of times to reissue an Alcatel 7x50 SR OAM 
         Trace Route request with the same time-to-live (TTL) value.  
         This parameter is optional.
         
         This parameter is not configurable for 'icmpTraceRoute';
         a default value of 3 is used."
    DEFVAL { 1 }
    ::= { tmnxOamTrCtlEntry 14 }

tmnxOamTrCtlMaxTtl OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlMaxTtl specifies the maximum time-to-live
        value.  This parameter is optional.  
        
        When tmnxOamTrCtlMaxTtl is not explicitly specified, the agent 
        defaults the value based on the 'tmnxOamTrCtlTestMode'.

        In the case of 'lspTraceRoute' the default is set to 30.
        In the case of 'icmpTraceRoute' the default is set to 30.
        In the case of 'ldpTreeTrace' the default is set to 30.
        In the case of 'vccvTraceRoute' the default is set to 8.
        Otherwise the default is set to 4."
    DEFVAL { 4 }
    ::= { tmnxOamTrCtlEntry 15 }

tmnxOamTrCtlInitialTtl OBJECT-TYPE
    SYNTAX         Unsigned32 (0..255)
    MAX-ACCESS     read-create
    STATUS         current
    DESCRIPTION
        "The value of tmnxOamTrCtlInitialTtl specifies the initial TTL 
        value to use.  This enables bypassing the initial (often well known)
        portion of a path.  This parameter is optional.
        
        This parameter is not configurable for 'icmpTraceRoute'; the
        default value 1 is used."
    DEFVAL { 1 }
    ::= { tmnxOamTrCtlEntry 16 }

tmnxOamTrCtlDSField OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlDSField specifies the value to store 
         in the Differentiated Services (DS) Field in the IP packet used 
         to encapsulate the Alcatel 7x50 SR OAM Trace Route probe.  
         The DS Field is defined as the Type of Service (TOS) octet 
         in a IPv4 header or as the Traffic Class octet in a IPv6 
         header.
    
         The value of this object must be a decimal integer in the range 
         from 0 to 255.  This option can be used to determine what effect 
         an explicit DS Field setting has on a oam traceroute response.
         Not all values are legal or meaningful.  DS Field usage is often
         not supported by IP implementations.  A value of 0 means that the
         function represented by this option is not supported.  Well known
         TOS octet values are '16' (low delay) and '8' (high throughput).

         This parameter is optional.
         
         This parameter is not used by 'macTraceRoute', 'lspTraceRoute', 
         'mcastTraceRoute' or 'vccvTraceRoute'."
    REFERENCE
        "Refer to RFC 2474 for the definition of the Differentiated Services 
         Field and to RFC 1812 Section 5.3.2 for Type of Service (TOS)."
    DEFVAL { 0 }
    ::= { tmnxOamTrCtlEntry 17 }

tmnxOamTrCtlMaxFailures OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    UNITS       "timeouts"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlMaxFailures specifies the maximum number
         of consecutive timeouts allowed before terminating an Alcatel
         7x50 SR OAM Trace Route request.  This parameter is optional.
         
         A value of either 255 (maximum hop count/possible TTL value) or 
         a 0 indicates that the function of terminating an Alcatel
         7x50 SR OAM Trace Route request when a specific number of 
         successive timeouts are detected is disabled.
         
         This parameter is optional.  This parameter is not used by
         'icmpTraceRoute'."
    DEFVAL { 5 }
    ::= { tmnxOamTrCtlEntry 18 }

tmnxOamTrCtlInterval  OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlInterval specifies the number of seconds
         to wait before repeating an Alcatel 7x50 SR OAM Trace Route test as
         defined by the value of the various objects in the corresponding row.
         This parameter is optional.

         The number of hops in a single Alcatel 7x50 SR OAM Trace Route test
         is determined by the value of the corresponding
         tmnxOamTrCtlProbesPerHop object.  After a single test completes,
         the number of seconds as defined by the value of
         tmnxOamTrCtlInterval MUST elapse before the next Alcatel 7x50 SR
         OAM Trace Route test is started.

         A value of 0 for this object implies that the test as defined by the 
         corresponding entry will not be repeated.

         This parameter is not used by 'icmpTraceRoute'."
    DEFVAL { 1 }
    ::= { tmnxOamTrCtlEntry 19 }

tmnxOamTrCtlMaxRows OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "rows"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlMaxRows specifies the maximum number of
         entries allowed in the tmnxOamTrProbeHistoryTable.  The oldest entry
         in the tmnxOamTrProbeHistoryTable is removed to allow the addition
         of an new entry once the number of rows in the
         tmnxOamTrProbeHistoryTable reaches this value.

         Old entries are not removed when a new test is started.  Entries are
         added to the tmnxOamTrProbeHistoryTable until tmnxOamTrCtlMaxRows
         is reached before entries begin to be removed.

         A value of 0 for this object disables creation of 
         tmnxOamTrProbeHistoryTable entries."
    DEFVAL      { 300 }
    ::= { tmnxOamTrCtlEntry 20 }

tmnxOamTrCtlTrapGeneration OBJECT-TYPE
    SYNTAX      BITS {
                  pathChange(0),
                  testFailure(1),
                  testCompletion(2)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlTrapGeneration specifies when and if to
         generate a notification for this entry:
         pathChange(0)     - Generate a tmnxOamTrPathChange
             notification when the current path varies from a previously
             determined path.
         testFailure(1)    - Generate a tmnxOamTrTestFailed notification when
             the full path to a target can't be determined.
         testCompletion(2) - Generate a tmnxOamTrTestCompleted notification
             when the path to a target has been determined.
       
         The value of this object defaults to zero, indicating that none of
         the above options have been selected."
    ::= { tmnxOamTrCtlEntry 21 }

tmnxOamTrCtlCreateHopsEntries OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlCreateHopsEntries specifies whether or not
         the current path for an Alcatel 7x50 SR OAM Trace Route test is kept
         in the tmnxOamTrHopsTable on a per hop basis.
         
         tmnxOamTrHopsTable provides a current path topology based on the 
         results of the OAM Trace Route tests.  If this feature is not 
         supported tmnxOamTrCtlCreateHopsEntries will always be set to false(2)
         and any attempt to change its value to true(1) will be denied."
    DEFVAL { false }
    ::= { tmnxOamTrCtlEntry 22 }

tmnxOamTrCtlSAA OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlSAA specifies whether or not to
         collect Service Assurance Agent, SAA, metrics such as loss,
         jitter and latency.
         
         When tmnxOamTrCtlSAA has a value of 'true', SAA metrics
         are collected.

         This parameter is optional."
    DEFVAL { false }
    ::= { tmnxOamTrCtlEntry 23 }

tmnxOamTrCtlRuns      OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlRuns indicates the number of times
         this OAM trace route test has been executed."
    ::= { tmnxOamTrCtlEntry 24 }

tmnxOamTrCtlFailures  OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlFailures indicates the number of times
         this OAM trace route test has failed."
    ::= { tmnxOamTrCtlEntry 25 }

tmnxOamTrCtlLastRunResult  OBJECT-TYPE
    SYNTAX      INTEGER {
                    undetermined (0),
                    success (1),
                    failed (2),
                    aborted (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlLastRunResult indicates the completion
         status the last time this test was executed.  If this OAM test is
         currently in progress, this object indicates the result of the
         previous test run, if any."
    ::= { tmnxOamTrCtlEntry 26 }

tmnxOamTrCtlLastChanged   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlLastChanged indicates the time the
         value of a settable object in this row was last changed."
    ::= { tmnxOamTrCtlEntry 27 }

tmnxOamTrCtlVRtrID  OBJECT-TYPE
    SYNTAX      TmnxVRtrID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this virtual router in the 
         Alcatel 7x50 SR system. The vRtrID value for each virtual router 
         must remain constant at least from one re-initialization of the 
         system management processor (CPM) to the next.  There will always 
         be at least one router entry defined by the agent with vRtrID=1 
         which represents the base transport router.

         This parameter is optional and is valid only if tmnxOamTrCtlTestMode
         is equal to 'icmpTrace'. If no value is specified the base router ID 
         is used."
    DEFVAL { 1 }
    ::= { tmnxOamTrCtlEntry 28 }

tmnxOamTrCtlTgtAddrType   OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlTgtAddrType specifies the type of host
         address to be used as the destination for performing a OAM Trace
         Route operation. Only 'ipv4', 'ipv6' and 'dns' address types are
         supported.
 
         This object indicates the type of address stored in the 
         corresponding tmnxOamTrCtlTgtAddress object.
         
         The 'dns' address type is valid only for 'icmpTraceRoute'."
    DEFVAL { unknown }
    ::= { tmnxOamTrCtlEntry 29 }

tmnxOamTrCtlTgtAddress OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlTgtAddress specifies the IP host address to 
         be used as the destination for performing an OAM Trace Route 
         operation. The host address type is determined by the value of the 
         corresponding tmnxOamTrCtlTgtAddrType object.

         This parameter is required by 'vprnTraceRoute' and 'icmpTraceRoute'.
         This parameter is not used by 'macTraceRoute',  'lspTraceRoute', 
         'mcastTraceRoute' or 'vccvTraceRoute'. "
    DEFVAL { ''H }
    ::= { tmnxOamTrCtlEntry 30 }

tmnxOamTrCtlSrcAddrType   OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlSrcAddrType specifies the type of host 
         address to be used as the source for performing an OAM traceroute 
         operation. This object indicates the type of address stored in the 
         corresponding tmnxOamTrCtlSrcAddress object. Only 'ipv4' and 'ipv6' 
         address types are supported.

         This object is valid only when the tmnxOamTrCtlTestMode has a 
         value of 'icmpTraceRoute'."
    DEFVAL { unknown }
    ::= { tmnxOamTrCtlEntry 31 }

tmnxOamTrCtlSrcAddress OBJECT-TYPE
    SYNTAX      InetAddress  (SIZE(0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlSrcAddress specifies the IP host address to 
         be used as the source for performing an OAM Trace Route operation.
         The host address type is determined by the value of the corresponding 
         tmnxOamTrCtlSrcAddrType object. This is an optional parameter.
         
         This object is valid only when the tmnxOamTrCtlTestMode has a 
         value of 'icmpTraceRoute'."  
    DEFVAL { ''H }
    ::= { tmnxOamTrCtlEntry 32 }

tmnxOamTrCtlWaitMilliSec OBJECT-TYPE
    SYNTAX      Unsigned32 (10..60000)
    UNITS       "milliseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrCtlWaitMilliSec specifies the time in
         milliseconds to wait for a response before sending the next
         probe.
         
         This object is valid only for 'icmpTraceRoute' tests."
    DEFVAL { 5000 }  -- 5 seconds
    ::= { tmnxOamTrCtlEntry 33 }

--
-- Alcatel 7x50 SR series OAM Trace Route Test Results Table
--
tmnxOamTrResultsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamTrResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR OAM Trace Route Test Results 
         Table for keeping track of the status of a tmnxOamTrCtlEntry.

         An entry is added to the tmnxOamTrResultsTable when an
         tmnxOamTrCtlEntry is started by successful transition
         of its tmnxOamTrCtlAdminStatus object to enabled(1).
         An entry is removed from the tmnxOamTrResultsTable when
         its corresponding tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 4 }

tmnxOamTrResultsEntry OBJECT-TYPE
    SYNTAX      TmnxOamTrResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamTrResultsTable.  The
         first two indexes of the tmnxOamTrResultsTable are the 
         same as the tmnxOamTrCtlTable in order for a tmnxOamTrResultsEntry
         to correspond to the tmnxOamTrCtlEntry that caused it to
         be created.  The third index allows a history of test runs
         to be stored."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex,
           tmnxOamTrResultsTestRunIndex
          }
    ::= { tmnxOamTrResultsTable 1 }

 TmnxOamTrResultsEntry ::=
    SEQUENCE {
     tmnxOamTrResultsOperStatus       INTEGER,
     tmnxOamTrResultsCurHopCount      Gauge32,
     tmnxOamTrResultsCurProbeCount    Gauge32,
     tmnxOamTrResultsIpTgtAddr        IpAddress,
     tmnxOamTrResultsTestAttempts     Unsigned32,
     tmnxOamTrResultsTestSuccesses    Unsigned32,
     tmnxOamTrResultsLastGoodPath     DateAndTime,
     tmnxOamTrResultsTestRunIndex     Unsigned32,
     tmnxOamTrResultsTgtAddrType      InetAddressType,
     tmnxOamTrResultsTgtAddress       InetAddress
    }

tmnxOamTrResultsOperStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                          enabled(1), -- test is in progress
                          disabled(2) -- test has stopped
                        }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflects the operational state of a tmnxOamTrCtlEntry:

           enabled(1)  - Test is active.
           disabled(2) - Test has stopped."
    ::= { tmnxOamTrResultsEntry 1 }

tmnxOamTrResultsCurHopCount OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "hops"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflects the current TTL value (range from 1 to
         255) for an Alcatel 7x50 SR OAM Trace Route operation.
         Maximum TTL value is determined by tmnxOamTrCtlMaxTtl."
    ::= { tmnxOamTrResultsEntry 2 }

tmnxOamTrResultsCurProbeCount OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "probes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Reflects the current probe count (1..10) for an Alcatel
         7x50 SR OAM Trace Route operation. The maximum probe count is 
         determined by tmnxOamTrCtlProbesPerHop."
    ::= { tmnxOamTrResultsEntry 3 }

tmnxOamTrResultsIpTgtAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "This objects reports the IP address associated
         with a tmnxOamTrCtlTargetIpAddress value when the
         destination address is specified as a DNS name.
         The value of this object should be a zero length
         octet string when a DNS name is not specified or
         when a specified DNS name fails to resolve.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamTrResultsTgtAddrType and 
         tmnxOamTrResultsTgtAddress." 
    ::= { tmnxOamTrResultsEntry 4 }

tmnxOamTrResultsTestAttempts OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "tests"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The current number of attempts to determine a path
         to a target.  The value of this object MUST be started
         at 0.
         
         This object was made obsolete in the 3.0 release.  Originally
         this table was used to report a summary of the results of all
         traceroute test for the configured test.  An additional index,
         tmnxOamTrResultsTestRunIndex, has been added to this table and
         a row in this table now represents the results of a single
         traceroute test run.  The new object tmnxOamTrCtlRuns in the
         tmnxOamTrCtlTable indicates the number of times this traceroute
         test has been run."
    ::= { tmnxOamTrResultsEntry 5 }

tmnxOamTrResultsTestSuccesses OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "tests"
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The current number of attempts to determine a path
         to a target that have succeeded.  The value of this
         object MUST be reported as 0 when no attempts have
         succeeded.
         
         This object was made obsolete in the 3.0 release.  Originally
         this table was used to report a summary of the results of all
         traceroute test for the configured test.  An additional index,
         tmnxOamTrResultsTestRunIndex, has been added to this table and
         a row in this table now represents the results of a single
         traceroute test run.  The new object tmnxOamTrCtlFailures in the
         tmnxOamTrCtlTable indicates the number of times an attempt to run
         this traceroute test has failed.  The number of successful runs
         can be calclated as (tmnxOamTrCtlRuns - tmnxOamTrCtlFailures)."
    ::= { tmnxOamTrResultsEntry 6 }

tmnxOamTrResultsLastGoodPath OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time when the last complete path
         was determined."
    ::= { tmnxOamTrResultsEntry 7 }

tmnxOamTrResultsTestRunIndex      OBJECT-TYPE
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrResultsTestRunIndex identifies the
         row entry that reports results for a single OAM trace
         route test run.

         The agent starts assigning tmnxOamTrResultsTestRunIndex values
         at 1 and wraps after exceeding the maximum possible value as
         defined by the limit of this object {'ffffffff'h}."
    ::= { tmnxOamTrResultsEntry 8 }
    
tmnxOamTrResultsTgtAddrType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrResultsTgtAddrType indicates the Internet
         address type stored in tmnxOamTrResultsTgtAddress."
    ::= { tmnxOamTrResultsEntry 9 }

tmnxOamTrResultsTgtAddress OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrResultsTgtAddress indicates the Internet
         address associated with a tmnxOamTrCtlTgtAddress value when the
         destination address is specified as a DNS name.  The value of this 
         object should be a zero length octet string when a DNS name is not 
         specified or when a specified DNS name fails to resolve."
    ::= { tmnxOamTrResultsEntry 10 }


--
-- Trace Route Probe History Table
--
tmnxOamTrProbeHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamTrProbeHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR OAM Trace Route Test Results Table 
         for storing the replies to an Alcatel 7x50 SR OAM Trace Route probe.

         The agent removes the oldest entry for a test in the 
         tmnxOamTrProbeHistoryTable to allow the addition of a new 
         entry for that test once the number of rows in the 
         tmnxOamTrProbeHistoryTable reaches the value specified by 
         tmnxOamTrCtlMaxRows."
   ::= { tmnxOamTraceRouteObjs 5 }

tmnxOamTrProbeHistoryEntry OBJECT-TYPE
    SYNTAX      TmnxOamTrProbeHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an Alcatel 7x50 SR OAM
         Trace Route probe operation.  Entries in this table for a
         configured test are limited by the value of the corresponding 
         tmnxOamTrCtlMaxRows object.

         The first two index elements identify the tmnxOamTrCtlEntry that 
         a tmnxOamTrProbeHistoryEntry belongs to.  The third index element
         selects an OAM trace route test run.  The fourth index element 
         selects a single Alcatel 7x50 SR OAM Trace Route operation result.  
         The fifth and sixth indexes select the hop and the probe at that 
         hop for a particular Alcatel 7x50 SR OAM Trace Route operation."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex
          }
    ::= { tmnxOamTrProbeHistoryTable 1 }

 TmnxOamTrProbeHistoryEntry ::=
    SEQUENCE {
     tmnxOamTrProbeHistoryIndex         Unsigned32,
     tmnxOamTrProbeHistoryHopIndex      Unsigned32,
     tmnxOamTrProbeHistoryProbeIndex    Unsigned32,
     tmnxOamTrProbeHistoryIpAddr        IpAddress,
     tmnxOamTrProbeHistoryResponse      Unsigned32,
     tmnxOamTrProbeHistoryOneWayTime    Integer32,
     tmnxOamTrProbeHistoryStatus        TmnxOamResponseStatus,
     tmnxOamTrProbeHistoryLastRC        Integer32,
     tmnxOamTrProbeHistoryTime          DateAndTime,
     tmnxOamTrProbeHistoryResponsePlane TmnxOamTestResponsePlane,
     tmnxOamTrProbeHistoryAddressType   TmnxOamAddressType,
     tmnxOamTrProbeHistorySapId         TmnxStrSapId,
     tmnxOamTrProbeHistoryVersion       Unsigned32,
     tmnxOamTrProbeHistoryRouterID      RouterID,
     tmnxOamTrProbeHistoryIfIndex       InterfaceIndexOrZero,
     tmnxOamTrProbeHistoryDataLen       Unsigned32,
     tmnxOamTrProbeHistorySize          Unsigned32,
     tmnxOamTrProbeHistoryInOneWayTime  Integer32,
     tmnxOamTrProbeHistoryAddrType      InetAddressType,
     tmnxOamTrProbeHistoryAddress       InetAddress
    }

tmnxOamTrProbeHistoryIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..'ffffffff'h)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created when the result of an Alcatel
         7x50 SR OAM Trace Route probe is determined.  The initial 2 
         instance identifier index values identify the tmnxOamTrCtlEntry
         that a probe result (tmnxOamTrProbeHistoryEntry) belongs
         to.  An entry is removed from this table when its corresponding 
         tmnxOamTrCtlEntry is deleted.
       
         The value of tmnxOamTrProbeHistoryIndex selects the entries belonging
         to a single OAM Trace Route test instance.
          
         The tmnxOamTrProbeHistoryIndex values are assigned starting at 1 
         and wrap after exceeding the maximum possible value as defined by 
         the limit of this object ('ffffffff'h)."
    ::= { tmnxOamTrProbeHistoryEntry 1 }

tmnxOamTrProbeHistoryHopIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "Indicates which hop in an Alcatel 7x50 SR OAM Trace Route path that
        the probe's results are for.  The value of this object is initially
        determined by the value of tmnxOamTrCtlInitialTtl."
    ::= { tmnxOamTrProbeHistoryEntry 2 }

tmnxOamTrProbeHistoryProbeIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..10)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "Indicates the index of a probe for a particular hop in an Alcatel
        7x50 SR OAM Trace Route path.  The number of probes per hop is 
        determined by the value of the corresponding tmnxOamTrCtlProbesPerHop 
        object."
    ::= { tmnxOamTrProbeHistoryEntry 3 }

tmnxOamTrProbeHistoryIpAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The IP address of a hop in an Alcatel 7x50 SR OAM Trace Route path.
         The value of tmnxOamTrProbeHistoryIpAddr specifies the Ipv4 
         address of the remote node that generated this reply to a OAM 
         Trace Route probe.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamTrProbeHistoryAddrType and 
         tmnxOamTrProbeHistoryAddress." 
    ::= { tmnxOamTrProbeHistoryEntry 4 }

tmnxOamTrProbeHistoryResponse OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when
         a probe was sent to when its response was received, two-way
         time, or when it timed out.  
         
         The value of this object is reported as 0 when it is not 
         possible to transmit a probe."
    ::= { tmnxOamTrProbeHistoryEntry 5 }

tmnxOamTrProbeHistoryOneWayTime OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when
         a OAM probe was sent to when it was received by the replier,
         out-bound one-way time.  
         
         The value of this object is reported as 0 when it is not possible 
         to transmit an OAM probe or the information is not available."
    ::= { tmnxOamTrProbeHistoryEntry 6 }

tmnxOamTrProbeHistoryStatus OBJECT-TYPE
    SYNTAX      TmnxOamResponseStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The result of an Alcatel 7x50 SR OAM Trace Route operation made 
         by a remote node to a particular probe."
    ::= { tmnxOamTrProbeHistoryEntry 7 }

tmnxOamTrProbeHistoryLastRC OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The last implementation method specific reply code received.

         The Alcatel 7x50 SR OAM Trace Route is usually implemented by 
         transmitting a series of probe packets with increasing time-to-live
         values.  A probe packet is a UDP datagram encapsulated into an 
         IP packet.  Each hop in a path to the target (destination) host 
         rejects the probe packets (probe's TTL too small, ICMP reply) until
         either the maximum TTL is exceeded or the target host is
         received. For icmpTraceRoute, both icmp packet type and code 
         are included."
    ::= { tmnxOamTrProbeHistoryEntry 8 }

tmnxOamTrProbeHistoryTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Timestamp for when this probe results were determined."
    ::= { tmnxOamTrProbeHistoryEntry 9 }

tmnxOamTrProbeHistoryResponsePlane OBJECT-TYPE
    SYNTAX      TmnxOamTestResponsePlane
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrProbeHistoryResponsePlane indicates the
         type of response plane from with this traceroute response was
         received."
   ::= { tmnxOamTrProbeHistoryEntry 10 }

tmnxOamTrProbeHistoryAddressType       OBJECT-TYPE
    SYNTAX      TmnxOamAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrProbeHistoryAddressType specifies the type of
         binding address information returned in response to a 'vprnTraceRoute' 
         test."
    ::= { tmnxOamTrProbeHistoryEntry 11 }

tmnxOamTrProbeHistorySapId OBJECT-TYPE
    SYNTAX          TmnxStrSapId
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamTrProbeHistorySapId is the name of the access 
         port of the SAP supporting the requested IP address returned in
         response to a 'vprnTraceRoute' probe.
         
         If the value of tmnxOamTrProbeHistoryAddressType is not 'sapId',
         this object is not relevant and MUST have a null string ''."
    ::= { tmnxOamTrProbeHistoryEntry 12 }    
    
tmnxOamTrProbeHistoryVersion       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrProbeHistoryVersion indicates the protocol
         version for this OAM ping reply."
    ::= { tmnxOamTrProbeHistoryEntry 14 }

tmnxOamTrProbeHistoryRouterID       OBJECT-TYPE
    SYNTAX      RouterID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value tmnxOamTrProbeHistoryRouterID indicates the downstream
         router ID of the node that provided this ping reply.  This object
         is valid only when tmnxOamTrCtlTestMode has a value of 
         'lspTraceRoute'."
    ::= { tmnxOamTrProbeHistoryEntry 15 }

tmnxOamTrProbeHistoryIfIndex   OBJECT-TYPE
    SYNTAX      InterfaceIndexOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrProbeHistoryIfIndex indicates for a 
         'lspTraceRoute' probe the ifIndex value of the interface that 
         this probe was transmitted from. 

         For other types of probes, this value is not significant and is
         set to 0."
    ::= { tmnxOamTrProbeHistoryEntry 16 }

tmnxOamTrProbeHistoryDataLen   OBJECT-TYPE       
    SYNTAX      Unsigned32
    UNITS       "octets"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrProbeHistoryDataLen indicates for a 
         'lspTraceRoute' probe the UPD data length of the echo reply. 

         For other types of probes, this value is not significant and is
         set to 0."
    ::= { tmnxOamTrProbeHistoryEntry 17 }

tmnxOamTrProbeHistorySize   OBJECT-TYPE       
    SYNTAX      Unsigned32
    UNITS       "octets"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrProbeHistorySize indicates the size in octets
         of the user payload in the probe request packet.  It does not
         include the service encapsulation."
    ::= { tmnxOamTrProbeHistoryEntry 18 }

tmnxOamTrProbeHistoryInOneWayTime OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in milliseconds from when
         a OAM probe reply was sent to when it was received,
         in-bound one-way time.  
         
         The value of this object is reported as 0 when the information 
         is not available."
    ::= { tmnxOamTrProbeHistoryEntry 19 }

tmnxOamTrProbeHistoryAddrType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "The value of tmnxOamTrProbeHistoryAddrType indicates the Internet
        address type stored in tmnxOamTrProbeHistoryAddress."
    ::= { tmnxOamTrProbeHistoryEntry 20 }

tmnxOamTrProbeHistoryAddress OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "The value of tmnxOamTrProbeHistoryAddress indicates the Internet
        address of a hop in an Alcatel 7x50 SR OAM Trace Route path."
    ::= { tmnxOamTrProbeHistoryEntry 21 }


--
-- Alcatel 7x50 SR series OAM Trace Route Test Hop Results Table
--
-- NOTE: This table provides a current path topology based on the results 
--       of the OAM Trace Route tests.  If this feature is not supported, 
--       tmnxOamTrCtlCreateHopsEntries will always be set to false(2) and 
--       any attempt to change its value to true(1) will be denied.
--       
tmnxOamTrHopsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamTrHopsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR OAM Trace Route Hop Table for keeping 
         track of the results of an Alcatel 7x50 SR OAM Trace Route test 
         on a per hop basis."
    ::= { tmnxOamTraceRouteObjs 6 }

tmnxOamTrHopsEntry OBJECT-TYPE
    SYNTAX      TmnxOamTrHopsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamTrHopsTable.
         The first two index elements identify the tmnxOamTrCtlEntry 
         that a tmnxOamTrHopsEntry belongs to.  The third index element
         identifies a OAM trace route test run.  The fourth index element,
         tmnxOamTrHopsHopIndex, selects a hop in an Alcatel 7x50 SR OAM 
         Trace Route path."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex,
           tmnxOamTrResultsTestRunIndex,
           tmnxOamTrHopsHopIndex
          }
    ::= { tmnxOamTrHopsTable 1 }

 TmnxOamTrHopsEntry ::=
    SEQUENCE {
       tmnxOamTrHopsHopIndex         Unsigned32,
       tmnxOamTrHopsIpTgtAddress     IpAddress,
       tmnxOamTrHopsMinRtt           Unsigned32,
       tmnxOamTrHopsMaxRtt           Unsigned32,
       tmnxOamTrHopsAverageRtt       Unsigned32,
       tmnxOamTrHopsRttSumOfSquares  Unsigned32,
       tmnxOamTrHopsMinTt            Integer32,
       tmnxOamTrHopsMaxTt            Integer32,
       tmnxOamTrHopsAverageTt        Integer32,
       tmnxOamTrHopsTtSumOfSquares   Integer32,
       tmnxOamTrHopsSentProbes       Unsigned32,
       tmnxOamTrHopsProbeResponses   Unsigned32,
       tmnxOamTrHopsLastGoodProbe    DateAndTime,
       tmnxOamTrHopsMinInTt          Integer32,
       tmnxOamTrHopsMaxInTt          Integer32,
       tmnxOamTrHopsAverageInTt      Integer32,
       tmnxOamTrHopsInTtSumOfSqrs    Integer32,
       tmnxOamTrHopsOutJitter        Integer32,
       tmnxOamTrHopsInJitter         Integer32,
       tmnxOamTrHopsRtJitter         Integer32,
       tmnxOamTrHopsProbeTimeouts    Unsigned32,
       tmnxOamTrHopsProbeFailures    Unsigned32,
       tmnxOamTrHopsTgtAddrType      InetAddressType,
       tmnxOamTrHopsTgtAddress       InetAddress
      }

tmnxOamTrHopsHopIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Specifies the hop index for an Alcatel 7x50 SR OAM Trace Route 
         hop.  Values for this object with respect to the same 
         tmnxOamTrCtlOwnerIndex and tmnxOamTrCtlTestIndex MUST start at 1 
         and increase monotonically.
    
         The tmnxOamTrHopsTable keeps the current Alcatel 7x50 SR OAM Trace 
         Route path per tmnxOamTrCtlEntry if enabled by setting the 
         corresponding tmnxOamTrCtlCreateHopsEntries to true(1).
    
         All hops (tmnxOamTrHopsTable entries) in an Alcatel 7x50 SR OAM 
         Trace Route path MUST be updated at the same time when an Alcatel
         7x50 SR OAM Trace Route operation completes.  Care needs to be 
         applied when either a path changes or can't be determined.  The 
         initial portion of the path, up to the first hop change, MUST 
         retain the same tmnxOamTrHopsHopIndex values.  The remaining portion
         of the path SHOULD be assigned new tmnxOamTrHopsHopIndex values."
    ::= { tmnxOamTrHopsEntry 1 }

tmnxOamTrHopsIpTgtAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "This object reports the IP address associated with
         the hop.  A value for this object should be reported
         as a numeric IP address and not as a DNS name.
         
         This value of this object is not significant when
         tmnxOamTrCtlTestMode has a value of 'macTraceRoute'.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamTrHopsTgtAddrType and 
         tmnxOamTrHopsTgtAddrType." 
    ::= { tmnxOamTrHopsEntry 2 }

tmnxOamTrHopsMinRtt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum Alcatel 7x50 SR OAM Trace Route round-trip-time (RTT) 
         received for this hop.  A value of 0 for this object implies that no
         RTT has been received."
    ::= { tmnxOamTrHopsEntry 3 }

tmnxOamTrHopsMaxRtt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum Alcatel 7x50 SR OAM Trace Route round-trip-time (RTT) 
         received for this hop.  A value of 0 for this object implies that no
         RTT has been received."
    ::= { tmnxOamTrHopsEntry 4 }

tmnxOamTrHopsAverageRtt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current average Alcatel 7x50 SR OAM Trace Route round-trip-time 
        (RTT) for this hop."
    ::= { tmnxOamTrHopsEntry 5 }

tmnxOamTrHopsRttSumOfSquares OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the sum of all Alcatel 7x50 SR OAM Trace Route 
         responses received for this hop.  Its purpose is to enable standard
         deviation calculation."
    ::= { tmnxOamTrHopsEntry 6 }

tmnxOamTrHopsMinTt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum Alcatel 7x50 SR OAM Trace Route outbound one-way-trip-time 
         received for this hop.  A value of 0 for this object implies that no
         one-way-trip-time has been received."
    ::= { tmnxOamTrHopsEntry 7 }

tmnxOamTrHopsMaxTt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum Alcatel 7x50 SR OAM Trace Route outbound one-way-trip-time 
         received for this hop.  A value of 0 for this object implies that no
         one-way-trip-time has been received."
    ::= { tmnxOamTrHopsEntry 8 }

tmnxOamTrHopsAverageTt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current average Alcatel 7x50 SR OAM Trace Route outbound 
         one-way-trip-time for this hop."
    ::= { tmnxOamTrHopsEntry 9 }

tmnxOamTrHopsTtSumOfSquares OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the sum of all outbound one-way-trip-time 
         responses received for this hop.  Its purpose is to enable standard
         deviation calculation."
    ::= { tmnxOamTrHopsEntry 10 }

tmnxOamTrHopsSentProbes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of this object reflects the number of probes sent
         for this hop during this Alcatel 7x50 SR OAM Trace Route test.  
         The value of this object should start at 0."
    ::= { tmnxOamTrHopsEntry 11 }

tmnxOamTrHopsProbeResponses OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of responses received for this hop during this Alcatel 
         7x50 SR OAM Trace Route test.  This value of this object should 
         start at 0."
    ::= { tmnxOamTrHopsEntry 12 }

tmnxOamTrHopsLastGoodProbe OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Date and time was the last response was received for a probe
         for this hop during this Alcatel 7x50 SR OAM Trace Route test."
    ::= { tmnxOamTrHopsEntry 13 }

tmnxOamTrHopsMinInTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum OAM trace route inbound one-way-trip-time received.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamTrHopsEntry 18 }

tmnxOamTrHopsMaxInTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum OAM trace route inbound one-way-trip-time received.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamTrHopsEntry 19 }

tmnxOamTrHopsAverageInTt OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current average OAM inbound inbound one-way-trip-time.
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamTrHopsEntry 20 }

tmnxOamTrHopsInTtSumOfSqrs OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the sum of the squares for the inbound
         one-way-trip time of all trace route probe responses received.  
         Its purpose is to enable standard deviation calculation.  
         A value of 0 for this object implies that no one-way-trip-time 
         measurement is available."
    ::= { tmnxOamTrHopsEntry 21 }

tmnxOamTrHopsOutJitter      OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrHopsOutJitter indicates the
         amount of outbound one-way trip jitter, expressed in milliseconds, 
         for a trace route probe request packet sent for this OAM test.
         A value of 0 for this object implies that no one-way-trip jitter 
         measurement is available."
    ::= { tmnxOamTrHopsEntry 22 }
    
tmnxOamTrHopsInJitter      OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrHopsInJitter indicates the
         amount of inbound one-way-trip jitter, expressed in milliseconds, 
         for a trace route probe response packet received for this OAM test.
         A value of 0 for this object implies that no one-way-trip jitter 
         measurement is available."
    ::= { tmnxOamTrHopsEntry 23 }
    
tmnxOamTrHopsRtJitter      OBJECT-TYPE
    SYNTAX      Integer32
    UNITS       "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrHopsRtJitter indicates the
         amount of two-way trip jitter, expressed in milliseconds, for a
         trace route probe sent for this OAM test.
         A value of 0 for this object implies that no two-way-trip jitter 
         measurement is available."
    ::= { tmnxOamTrHopsEntry 24 }

tmnxOamTrHopsProbeTimeouts     OBJECT-TYPE       
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrHopsProbeTimeouts indicates the number
         of probes that timed out without receiving a response."
    ::= { tmnxOamTrHopsEntry 25 }

tmnxOamTrHopsProbeFailures     OBJECT-TYPE       
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrHopsProbeFailures indicates the total
         number of probes that failed to be transmitted plus the number of
         probes that timed out without receiving a response."
    ::= { tmnxOamTrHopsEntry 26 }
 
tmnxOamTrHopsTgtAddrType OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrHopsTgtAddrType indicates the Internet
         address type stored in tmnxOamTrHopsTgtAddress."
    ::= { tmnxOamTrHopsEntry 27 }

tmnxOamTrHopsTgtAddress OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamTrHopsTgtAddress indicates the Internet
         address associated with the hop.  A value for this object should 
         be reported as a numeric IP address and not as a DNS name.
         
         This value of this object is not significant when
         tmnxOamTrCtlTestMode has a value of 'macTraceRoute'."
    ::= { tmnxOamTrHopsEntry 28 }


--
--  Alcatel 7x50 SR OAM MAC Trace Route Control Table
--
--
--  Sparsely dependent extension of the tmnxOamTrCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamTrCtlTable, 
--  and the sparse dependent table, tmnxOamMacTrCtlTable. 
--
--  This in effect extends the tmnxOamTrCtlTable with additional columns.
--  Rows are created in the tmnxOamMacTrCtlTable only for those entries 
--  in the tmnxOamTrCtlTable where tmnxOamTrCtlTestMode has a value of 
--  'macTraceRoute'.  
--  
--  Deletion of a row in the tmnxOamTrCtlTable results in the 
--  same fate for the row in the tmnxOamMacTrCtlTable.
--
tmnxOamMacTrCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMacTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM MAC Trace Route Control Table for 
         providing, via SNMP, the capability of performing Alcatel 7x50 SR
         OAM 'macTraceRoute' test operations.  The results of these tests 
         are stored in the tmnxOamTrResultsTable, tmnxOamTrProbeHistoryTable 
         and the tmnxOamMacTrL2MapTable."
   ::= { tmnxOamTraceRouteObjs 7 }

tmnxOamMacTrCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamMacTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMacTrCtlTable.  The first index
         element, tmnxOamTrCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamTrCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamMacTrCtlTable 1 }

 TmnxOamMacTrCtlEntry ::=
    SEQUENCE {
       tmnxOamMacTrCtlTargetMacAddr    MacAddress,
       tmnxOamMacTrCtlSourceMacAddr    MacAddress,
       tmnxOamMacTrCtlSendControl      TruthValue,
       tmnxOamMacTrCtlReplyControl     TruthValue
    }

tmnxOamMacTrCtlTargetMacAddr   OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrCtlTargetMacAddr is a 6-octet value 
         that specifies the target MAC address to be used in the query 
         request for performing an OAM Mac Trace Route operation when 
         tmnxOamTrCtlTestMode has a value of 'macTraceRoute'. "
    REFERENCE
        "IEEE 802.3 Subclause ********.9"
    DEFVAL { '000000000000'H }
    ::= { tmnxOamMacTrCtlEntry 1 }

tmnxOamMacTrCtlSourceMacAddr   OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrCtlSourceMacAddr is a 6-octet value 
         that specifies the MAC address to be used as the source in the 
         query request for performing an OAM MAC Trace Route operation when 
         tmnxOamTrCtlTestMode has a value of 'macTraceRoute'. "
    REFERENCE
        "IEEE 802.3 Subclause ********.9"
    DEFVAL { '000000000000'H }
    ::= { tmnxOamMacTrCtlEntry 2 }

tmnxOamMacTrCtlSendControl     OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMacTrCtlSendControl is 'true', the OAM
        MAC Trace Route packet is sent directly to the destination IP address 
        via the control plane.  If its value is 'false', the packet is sent
        via the data plane."
    DEFVAL { false }
    ::= { tmnxOamMacTrCtlEntry 3 }

tmnxOamMacTrCtlReplyControl        OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamMacTrCtlReplyControl is 'true', the
        OAM Mac Trace Route response is returned using the control plane.  
        If its value is 'false', the packet is sent via the data plane."
    DEFVAL { false }
    ::= { tmnxOamMacTrCtlEntry 4 }

--
-- Alcatel 7x50 SR series OAM MAC Trace Route Label Mapping Table
--
tmnxOamMacTrL2MapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMacTrL2MapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'macTraceRoute'
         probe operation when a L2 Mapping TLV was returned.  Each row
         represents a single L2FEC TLV within the L2 Mapping TLV. A single
         L2 mapping entry is returned if the 'macTraceRoute' replier knows
         the requested VPN ID and has a binding for the requested MAC
         address.  Multiple downstream L2 mappings that specify
         the replier's flooding domain may be returned if the replier has 
         no bindings for the MAC address.

         An entry in this table is created when the result of an OAM 
         'macTraceRoute' probe is determined.  An entry is removed from 
         this table when its corresponding tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 8 }

tmnxOamMacTrL2MapEntry OBJECT-TYPE
    SYNTAX      TmnxOamMacTrL2MapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMacTrL2MapTable.  The first two 
         index elements identify the tmnxOamTrCtlEntry that a 
         tmnxOamMacTrL2MapEntry belongs to.  The third index element
         identifies a particular OAM trace route test run.  The fourth 
         index element selects the group of responses associated with a 
         specific probe attempt.  The fifth and sixth indexes select the 
         hop and the probe at that hop for a particular Alcatel 7x50 SR 
         OAM Trace Route operation.  Note that in the case of a successful 
         'macTraceRoute' reply there will be only one row entry created.  
         However there may also be one or more error replies.  The seventh 
         index selects a single L2 mapping entry withing a specific 
         probe reply."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex,
            tmnxOamMacTrL2MapIndex
           }
    ::= { tmnxOamMacTrL2MapTable 1 }

 TmnxOamMacTrL2MapEntry ::=
    SEQUENCE {
       tmnxOamMacTrL2MapIndex        Unsigned32,
       tmnxOamMacTrL2MapRouterID     IpAddress,
       tmnxOamMacTrL2MapLabel        MplsLabel,
       tmnxOamMacTrL2MapProtocol     TmnxOamSignalProtocol,
       tmnxOamMacTrL2MapVCType       TmnxOamVcType,        
       tmnxOamMacTrL2MapVCID         TmnxVcId,
       tmnxOamMacTrL2MapDirection    INTEGER,
       tmnxOamMacTrL2MapSdpId        SdpId,
       tmnxOamMacTrL2MapSapName      TNamedItemOrEmpty
    }

tmnxOamMacTrL2MapIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxOamMacTrL2MapIndex selects a single L2 mapping entry
         within a specific 'macTraceRoute' probe reply.

         The agent starts assigning tmnxOamMacTrL2MapIndex values at 1." 
    ::= { tmnxOamMacTrL2MapEntry 1 }

tmnxOamMacTrL2MapRouterID    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapRouterID is the router ID for this
         L2 mapping entry."
    ::= { tmnxOamMacTrL2MapEntry 2 }
    
tmnxOamMacTrL2MapLabel       OBJECT-TYPE
    SYNTAX      MplsLabel
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapLabel is the label used by the
         router for the L2FEC or VC ID specified by this L2 mapping entry."
    ::= { tmnxOamMacTrL2MapEntry 3 }
    
tmnxOamMacTrL2MapProtocol    OBJECT-TYPE
    SYNTAX      TmnxOamSignalProtocol
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapProtocol is the signaling protocol
         used by the router for the L2FEC or VC ID specified by this L2
         mapping entry."
    ::= { tmnxOamMacTrL2MapEntry 4 }
    
tmnxOamMacTrL2MapVCType       OBJECT-TYPE
    SYNTAX      TmnxOamVcType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapVCType specifies the class of
         VC ID given in tmnxOamMacTrL2MapVCID."
    ::= { tmnxOamMacTrL2MapEntry 5 }
    
tmnxOamMacTrL2MapVCID        OBJECT-TYPE
    SYNTAX      TmnxVcId
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapVCID is the VC ID associated with
         the label used by the L2FEC specified by this L2 mapping entry."
    ::= { tmnxOamMacTrL2MapEntry 6 }

tmnxOamMacTrL2MapDirection       OBJECT-TYPE
    SYNTAX      INTEGER {
                    upstream (1),
                    downstream (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapDirection specifies the direction
         that this L2 mapping entry is in relation to the node that returned
         the 'macQuery' reply."
    ::= { tmnxOamMacTrL2MapEntry 7 }

tmnxOamMacTrL2MapSdpId     OBJECT-TYPE
    SYNTAX      SdpId
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapSdpId specifies the ID of the SDP
         supporting the L2 mapping entry that returned the reply.

         If this mapping is not a SDP, this object is not relevant and 
         MUST return a value of '0'."
    ::= { tmnxOamMacTrL2MapEntry 8 }

tmnxOamMacTrL2MapSapName    OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamMacTrL2MapSapName specifies the SAP name 
         when the downstream of the responder is a SAP.

         If the downstream responder is not a SAP, this object is not 
         relevant and MUST return a zero length empty string."
    ::= { tmnxOamMacTrL2MapEntry 9 }


--
--  Alcatel 7x50 SR series OAM LSP Trace Route Control Table
--
--
--  Sparsely dependent extension of the tmnxOamTrCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamTrCtlTable, 
--  and the sparse dependent table, tmnxOamLspTrCtlTable. 
--
--  This in effect extends the tmnxOamTrCtlTable with additional columns.
--  Rows are created in the tmnxOamLspTrCtlTable only for those entries 
--  in the tmnxOamTrCtlTable where tmnxOamTrCtlTestMode has a value of 
--  'lspTraceRoute'.  
--  
--  Deletion of a row in the tmnxOamTrCtlTable results in the 
--  same fate for the row in the tmnxOamLspTrCtlTable.
--

tmnxOamLspTrCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamLspTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM Lsp Trace Route Control Table 
         for providing, via SNMP, the capability of performing Alcatel
         7x50 SR OAM 'lspTraceRoute' test operations.  The results of 
         these tests are stored in the tmnxOamTrResultsTable and the 
         tmnxOamTrProbeHistoryTable."
   ::= { tmnxOamTraceRouteObjs 9 }

tmnxOamLspTrCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamLspTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamLspTrCtlTable.  The first index
         element, tmnxOamTrCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamTrCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamLspTrCtlTable 1 }

 TmnxOamLspTrCtlEntry ::=
    SEQUENCE {
       tmnxOamLspTrCtlVRtrID           TmnxVRtrID,
       tmnxOamLspTrCtlLspName          TNamedItemOrEmpty,
       tmnxOamLspTrCtlPathName         TNamedItemOrEmpty,
       tmnxOamLspTrCtlLdpIpPrefix      IpAddress,
       tmnxOamLspTrCtlLdpIpPrefixLen   IpAddressPrefixLength,
       tmnxOamLspTrCtlLdpPrefixType    InetAddressType,
       tmnxOamLspTrCtlLdpPrefix        InetAddress,
       tmnxOamLspTrCtlLdpPrefixLen     InetAddressPrefixLength,
       tmnxOamLspTrCtlPathDestType     InetAddressType,
       tmnxOamLspTrCtlPathDest         InetAddress,
       tmnxOamLspTrCtlNhIntfName       TNamedItemOrEmpty,
       tmnxOamLspTrCtlNhAddressType    InetAddressType,
       tmnxOamLspTrCtlNhAddress        InetAddress
    }

tmnxOamLspTrCtlVRtrID  OBJECT-TYPE
    SYNTAX      TmnxVRtrID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this virtual router in the 
         Alcatel 7x50 SR system. The vRtrID value for each virtual router 
         must remain constant at least from one re-initialization of the 
         system management processor (CPM) to the next.  There will always 
         be at least one router entry defined by the agent with vRtrID=1 
         which represents the base transport router."
    DEFVAL { 1 }
    ::= { tmnxOamLspTrCtlEntry 1 }

tmnxOamLspTrCtlLspName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Administrative name of the target Labeled Switch Path (LSP) for this
         OAM LSP Trace Route test.  The LSP name must be unique within the 
         virtual router instance specified by tmnxOamLspTrCtlVRtrID.

         This parameter is mutually exclusive with the IP prefix parameter 
         associated with an LDP based LSP (tmnxOamLspTrCtlLdpIpPrefix). Either
         the LSP name or the LDP IP Prefix must be specified but not both."
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamLspTrCtlEntry 2 }

tmnxOamLspTrCtlPathName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Administrative name of the Path (LSP) for this OAM LSP Trace Route 
         test. The Path name must be unique within the virtual router 
         instance specified by tmnxOamLspTrCtlVRtrID.
         
         This is an optional parameter which can be specified only if the
         LSP Name parameter is specified."
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamLspTrCtlEntry 3 }

tmnxOamLspTrCtlLdpIpPrefix     OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "The IP prefix for the LDP based LSP for this OAM LSP Trace Route 
         test.

         This parameter is mutually exclusive with the LSP name parameter 
         (tmnxOamLspTrCtlLspName). Either the LDP IP Prefix or the LSP name 
         must be specified but not both.
         
         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamLspTrCtlLdpPrefixType and 
         tmnxOamLspTrCtlLdpPrefix." 
    DEFVAL { '00000000'H } -- 0.0.0.0
    ::= { tmnxOamLspTrCtlEntry 4 }

tmnxOamLspTrCtlLdpIpPrefixLen     OBJECT-TYPE
    SYNTAX      IpAddressPrefixLength
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "The IP prefix length for the LDP based LSP for this OAM LSP Trace 
         Route test. 
         
         The value of this parameter is valid only when LDP IP Prefix 
         has been specified.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress object tmnxOamLspTrCtlLdpPrefixLen." 
    DEFVAL { 32 } 
    ::= { tmnxOamLspTrCtlEntry 5 }

tmnxOamLspTrCtlLdpPrefixType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrCtlLdpPrefixType specifies the type of
         Internet address stored in tmnxOamLspTrCtlLdpPrefix."
    DEFVAL { unknown }
    ::= { tmnxOamLspTrCtlEntry 6 }

tmnxOamLspTrCtlLdpPrefix     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrCtlLdpPrefix specifies the Internet address
         prefix for the LDP based LSP for this OAM LSP Trace Route 
         test.

         This parameter is mutually exclusive with the LSP name parameter 
         (tmnxOamLspTrCtlLspName). Either the LDP IP Prefix or the LSP name 
         must be specified but not both."
    DEFVAL { ''H }
    ::= { tmnxOamLspTrCtlEntry 7 }

tmnxOamLspTrCtlLdpPrefixLen     OBJECT-TYPE
    SYNTAX      InetAddressPrefixLength
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrCtlLdpPrefixLen specifies the Internet
         address prefix length for the LDP based LSP for this OAM LSP Trace 
         Route test. 
         
         The value of this parameter is valid only when LDP IP Prefix 
         has been specified."
    DEFVAL { 32 } 
    ::= { tmnxOamLspTrCtlEntry 8 }

tmnxOamLspTrCtlPathDestType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrCtlPathDestType specifies the type
         of Internet address stored in tmnxOamLspTrCtlPathDest."
    DEFVAL { unknown }
    ::= { tmnxOamLspTrCtlEntry 9 }

tmnxOamLspTrCtlPathDest     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrCtlPathDest specifies a unique path 
         in a multipath setup for this OAM Lsp Trace Route to traverse. 
         This address is used as part of a hash key to select the 
         appropriate ECMP to the destination of an OAM LSP Trace test. 
         
         This is an optional parameter. "
    DEFVAL { ''H }
    ::= { tmnxOamLspTrCtlEntry 10 }    

tmnxOamLspTrCtlNhIntfName     OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Administrative name of the next hop interface for this OAM LSP 
         Trace Route test to be sent. The interface name must be unique 
         within the virtual router instance specified by 
         tmnxOamLspTrCtlVRtrID.
         
         This is an optional parameter which can be specified only if the
         tmnxOamLspTrCtlPathDest parameter is specified. This parameter 
         is mutually exclusive with the tmnxOamLspTrCtlNhAddress
         parameter. Either the next-hop interface name or next-hop address
         can be specified but not both. An attempt to set this object to a
         non-default value when tmnxOamLspTrCtlNhAddress has a
         non-default value will result in an 'inconsistentValue' error."
         
    DEFVAL { ''H }  -- the empty string
    ::= { tmnxOamLspTrCtlEntry 11 }

tmnxOamLspTrCtlNhAddressType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrCtlNhAddressType specifies the type
         of Internet address stored in tmnxOamLspTrCtlNhAddress."
    DEFVAL { unknown }
    ::= { tmnxOamLspTrCtlEntry 12 }

tmnxOamLspTrCtlNhAddress     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrCtlNhAddress specifies the Interface
         address to the next hop through which this OAM LSP Trace Route 
         test is to be transmitted.  
         
         This is an optional parameter which can be specified only if the
         tmnxOamLspTrCtlPathDest parameter is specified. This parameter
         is mutually exclusive with tmnxOamLspTrCtlNhIntfName parameter.
         Either the next-hop interface name or next-hop address
         can be specified but not both. An attempt to set this object to a
         non-default value when tmnxOamLspTrCtlNhIntfName has a
         non-default value will result in an 'inconsistentValue' error."
    DEFVAL { ''H }
    ::= { tmnxOamLspTrCtlEntry 13 }    

 --
 -- Alcatel 7x50 SR series OAM Lsp Trace Route Downstream L2 Mapping Table
 --
 
tmnxOamLspTrMapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamLspTrMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'lspTraceRoute'
         probe operation when a Downstream Mapping TLV was returned.  Each row
         represents a single label map within the returned downstream mapping
         TLV. 

         An entry in this table is created when the result of an OAM 
         'lspTraceRoute' probe is determined.
                
         An entry is removed from this table when its corresponding 
         tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 10 }

tmnxOamLspTrMapEntry OBJECT-TYPE
    SYNTAX      TmnxOamLspTrMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamLspTrMapTable.  The first two 
         index elements identify the tmnxOamTrCtlEntry that a 
         tmnxOamLspTrMapEntry belongs to.  The third index element
         identifies an OAM trace route test run.  The fourth index element
         selects the group of responses associated with a specific
         probe attempt. The fifth and sixth indexes select the hop and 
         the probe at that hop for a particular Alcatel 7x50 SR OAM 
         Trace Route operation. The seventh index selects a single downstream 
         L2 map entry within the specific probe reply."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex,
            tmnxOamLspTrMapIndex
           }
    ::= { tmnxOamLspTrMapTable 1 }

 TmnxOamLspTrMapEntry ::=
    SEQUENCE {
       tmnxOamLspTrMapIndex         Unsigned32,
       tmnxOamLspTrMapDSIPv4Addr    IpAddress,
       tmnxOamLspTrMapAddrType      TmnxOamAddressType,
       tmnxOamLspTrMapDSIfAddr      Unsigned32,
       tmnxOamLspTrMapMTU           Unsigned32,
       tmnxOamLspTrMapDSIndex       Unsigned32
    }

tmnxOamLspTrMapIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in this table is created when the result of a OAM 
         'lspTraceRoute' probe is determined.  The initial 2 
         instance identifier index values identify the tmnxOamTrCtlEntry
         that a OAM probe result (tmnxOamTrProbeHistoryEntry) belongs
         to.  The tmnxOamTrProbeHistoryIndex identifies a group of replies
         received in response to a specific probe transmission. The fourth 
         and fifth indexes select the hop and the probe at that hop for a 
         particular Alcatel 7x50 SR OAM Trace Route operation.
        
         The tmnxOamLspTrMapIndex selects a single L2 mapping entry
         within a specific 'lspTraceRoute' probe reply.

         The agent starts assigning tmnxOamLspTrMapIndex values at 1." 
    ::= { tmnxOamLspTrMapEntry 1 }

tmnxOamLspTrMapDSIPv4Addr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrMapDSIPv4Addr indicates the IPv4 address
         of the downstream LSR that this entry in the tmnxOamLspTrMapTable
         describes.  If the interface to the downstream LSR is numbered,
         then the tmnxOamLspTrMapDSIPv4Addr can either be the downstream 
         LSR's Router ID or the interface address of the downstream LSR. 
         
         If the interface to the downstream LSR is unnumbered, the value
         of tmnxOamLspTrMapDSIPv4Addr indicates the downstream LSR's Router 
         ID."
    ::= { tmnxOamLspTrMapEntry 2 }
    
tmnxOamLspTrMapAddrType        OBJECT-TYPE
    SYNTAX      TmnxOamAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrMapAddrType indicates the type of
         downstream interface address specified by tmnxOamLspTrMapDSIfAddr.
         The only valid values for tmnxOamLspTrMapAddrType are 
         'ipv4Address', 'ipv4Unnumbered', 'ipv6Address' and 'ipv6Unnumbered'."
    ::= { tmnxOamLspTrMapEntry 3 }

tmnxOamLspTrMapDSIfAddr        OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When tmnxOamLspTrMapAddrType has a value of 'ipv4Address' 
         tmnxOamLspTrMapDSIfAddr indicates the downstream LSR's
         interface address.  When tmnxOamLspTrMapAddrType has a value
         of 'unnumbered', tmnxOamLspTrMapDSIfAddr indicates the index 
         assigned by the upstream LSR to the interface."
    ::= { tmnxOamLspTrMapEntry 4 }
 
tmnxOamLspTrMapMTU     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrMapMTU indicates the largest MPLS frame
         (including the label stack) that fits on the interface to the
         downstream LSR."
    ::= { tmnxOamLspTrMapEntry 5 }
         
tmnxOamLspTrMapDSIndex     OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of tmnxOamLspTrMapDSIndex indicates the unique index
         assigned to the pair of downstream router and interface addresses
         represented by this row in the tmnxOamLspTrMapTable.
         
         tmnxOamLspTrMapDSIndex object was obsolete in release 5.0" 
    ::= { tmnxOamLspTrMapEntry 6 }

 --
 -- Alcatel 7x50 SR series OAM Lsp Trace Route Downstream Label Mapping Table
 --
 
tmnxOamLspTrDSLabelTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamLspTrDSLabelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'lspTraceRoute'
         probe operation when a Downstream Mapping TLV was returned.  Each row
         represents a single downstream label within a returned downstream 
         L2 mapping TLV. 

         An entry in this table is created when the result of an OAM 
         'lspTraceRoute' probe is determined. 
         An entry is removed from this table when its corresponding 
         tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 15 }

tmnxOamLspTrDSLabelEntry OBJECT-TYPE
    SYNTAX      TmnxOamLspTrDSLabelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamLspTrDSLabelTable.  The first 
         two index elements identify the tmnxOamTrCtlEntry that a 
         tmnxOamLspTrMapEntry belongs to.  The third index element
         identifies a OAM trace route test run. The fourth index element
         selects the group of responses associated with a specific
         probe attempt. The fifth and sixth indexes select the hop and 
         the probe at that hop for a particular Alcatel 7x50 SR OAM 
         Trace Route operation. The seventh index selects a single downstream 
         L2 map entry within the specific probe reply.  The eigth index 
         selects seccessive downstream L2 label entries contained in a 
         specific probe reply."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex,
            tmnxOamLspTrMapIndex,
            tmnxOamLspTrDSLabelIndex
           }
    ::= { tmnxOamLspTrDSLabelTable 1 }

 TmnxOamLspTrDSLabelEntry ::=
    SEQUENCE {
       tmnxOamLspTrDSLabelIndex        Unsigned32,
       tmnxOamLspTrDSLabelLabel        MplsLabel,
       tmnxOamLspTrDSLabelProtocol     TmnxOamSignalProtocol
    }

tmnxOamLspTrDSLabelIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxOamLspTrDSLabelIndex selects a single downstream L2 label
         entry within a specific L2 mapping entry reply.

         The agent starts assigning tmnxOamLspTrDSLabelIndex values at 1." 
    ::= { tmnxOamLspTrDSLabelEntry 1 }

tmnxOamLspTrDSLabelLabel       OBJECT-TYPE
    SYNTAX      MplsLabel
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrDSLabelLabel is the label used by the
         downstream router specified by this downstream L2 label entry."
    ::= { tmnxOamLspTrDSLabelEntry 2 }
    
tmnxOamLspTrDSLabelProtocol    OBJECT-TYPE
    SYNTAX      TmnxOamSignalProtocol
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLspTrDSLabelProtocol is the signaling protocol
         used by the router for the label specified by this downstream 
         L2 label entry."
    ::= { tmnxOamLspTrDSLabelEntry 3 }
    

--
--  Alcatel 7x50 SR series OAM VPRN Trace Route Control Table
--
--
--  Sparse Dependent Extention of the tmnxOamTrCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamTrCtlTable, 
--  and the sparse dependent table, tmnxOamVprnTrCtlTable. 
--
--  This in effect extends the tmnxOamTrCtlTable with additional columns.
--  Rows are created in the tmnxOamVprnTrCtlTable only for those entries 
--  in the tmnxOamTrCtlTable where tmnxOamTrCtlTestMode has a value of 
--  'vprnTraceRoute'.  
--  
--  Deletion of a row in the tmnxOamTrCtlTable results in the 
--  same fate for the row in the tmnxOamVprnTrCtlTable.
--
tmnxOamVprnTrCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVprnTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM VPRN Trace Route Control Table for 
         providing, via SNMP, the capability of performing Alcatel 7x50 SR
         OAM 'vprnTraceRoute' test operations.  The results of these tests 
         are stored in the tmnxOamTrResultsTable, the 
         tmnxOamTrProbeHistoryTable, the tmnxOamVprnTrRTTable and the 
         tmnxOamVprnTrNextHopTable."
   ::= { tmnxOamTraceRouteObjs 11 }

tmnxOamVprnTrCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamVprnTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVprnTrCtlTable.  The first index
         element, tmnxOamTrCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamTrCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamVprnTrCtlTable 1 }

 TmnxOamVprnTrCtlEntry ::=
    SEQUENCE {
       tmnxOamVprnTrCtlSourceIpAddr        IpAddress,
       tmnxOamVprnTrCtlReplyControl        TruthValue,
       tmnxOamVprnTrCtlSrcAddrType         InetAddressType,
       tmnxOamVprnTrCtlSrcAddress          InetAddress
    }

tmnxOamVprnTrCtlSourceIpAddr       OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "Specifies the Ipv4 address to be used as the source for
         performing an OAM 'vprnTraceRoute' operation when tmnxOamTrCtlTestMode 
         has a value of 'vprnTraceRoute'.   This parameter is required.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamVprnTrCtlSrcAddrType and 
         tmnxOamVprnTrCtlSrcAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamVprnTrCtlEntry 1 }

tmnxOamVprnTrCtlReplyControl       OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamVprnTrCtlReplyControl is 'true', the
         OAM 'vprnTraceRoute' response is returned using the control plane.  
         If its value is 'false', the packet is sent via the data plane.
        
         This object is optional and is not relevant when tmnxOamTrCtlTestMode 
         has a value other than 'vprnTraceRoute'."
    DEFVAL { false }
    ::= { tmnxOamVprnTrCtlEntry 2 }

tmnxOamVprnTrCtlSrcAddrType       OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrCtlSrcAddrType specifies the type of
         Internet address stored in tmnxOamVprnTrCtlSrcAddress."
    DEFVAL { unknown }
    ::= { tmnxOamVprnTrCtlEntry 3 }

tmnxOamVprnTrCtlSrcAddress       OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrCtlSrcAddress specifies the Internet
         address to be used as the source for performing an OAM 
         'vprnTraceRoute' operation when tmnxOamTrCtlTestMode has a value of 
         'vprnTraceRoute'.   This parameter is required."
    DEFVAL { ''H }
    ::= { tmnxOamVprnTrCtlEntry 4 }



 --
 -- Alcatel 7x50 SR series OAM VPRN Trace Route L3 Map Table
 --
tmnxOamVprnTrL3MapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVprnTrL3MapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'vprnTraceRoute' 
         probe operation.  The number of entries in this table are limited 
         by the value of the corresponding tmnxOamTrCtlMaxRows object.

         An entry in this table is created when the result of an OAM 
         'vprnTraceRoute' probe is determined.  An entry is removed from 
         this table when its corresponding tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 12 }

tmnxOamVprnTrL3MapEntry OBJECT-TYPE
    SYNTAX      TmnxOamVprnTrL3MapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVprnTrL3MapTable.  The first 
         two index elements identify the tmnxOamTrCtlEntry that a 
         tmnxOamVprnTrL3MapEntry belongs to.  The third index element
         identifies an OAM trace route test run.  The fourth index element 
         selects a single Alcatel 7x50 SR OAM 'vprnTraceRoute' operation 
         result.  The fifth and sixth indexes select the hop and the 
         probe at that hop for a particular Alcatel 7x50 SR OAM 
         'vprnTraceRoute' operation.  The seventh index selects either the 
         requestor's or responder's L3 route information."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex,
            tmnxOamVprnTrL3MapReporter
          }
    ::= { tmnxOamVprnTrL3MapTable 1 }

 TmnxOamVprnTrL3MapEntry ::=
    SEQUENCE {
       tmnxOamVprnTrL3MapReporter          INTEGER,
       tmnxOamVprnTrL3MapRouterID          RouterID,
       tmnxOamVprnTrL3MapRteDestAddr       IpAddress,
       tmnxOamVprnTrL3MapRteDestMask       Unsigned32,
       tmnxOamVprnTrL3MapRteVprnLabel      MplsLabel,
       tmnxOamVprnTrL3MapRteMetrics        Unsigned32,
       tmnxOamVprnTrL3MapRteLastUp         DateAndTime,
       tmnxOamVprnTrL3MapRteOwner          INTEGER,
       tmnxOamVprnTrL3MapRtePref           Unsigned32,
       tmnxOamVprnTrL3MapRteDist           TmnxVPNRouteDistinguisher,
       tmnxOamVprnTrL3MapNumNextHops       Unsigned32,
       tmnxOamVprnTrL3MapNumRteTargets     Unsigned32,
       tmnxOamVprnTrL3MapDestAddrType      InetAddressType,
       tmnxOamVprnTrL3MapDestAddress       InetAddress,
       tmnxOamVprnTrL3MapDestMaskLen       InetAddressPrefixLength
    }

tmnxOamVprnTrL3MapReporter        OBJECT-TYPE
    SYNTAX      INTEGER {
                    requestor (1),
                    responder (2)
                }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapReporter specifies who reported 
         the route information in this row entry.  It may have come from
         the 'vprnTraceRoute' requestor's local information or it may be
         from the remote 'vprnTraceRoute' responder."
    ::= { tmnxOamVprnTrL3MapEntry 1 }

tmnxOamVprnTrL3MapRouterID     OBJECT-TYPE
    SYNTAX      RouterID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRouterID indicates the router ID
         of the node, requestor or responder, that provided this route
         information."
    ::= { tmnxOamVprnTrL3MapEntry 2 }

tmnxOamVprnTrL3MapRteDestAddr      OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRteDestAddr indicates the IP 
         address to be advertised for the route information returned by
         this 'vprnTraceRoute' reply.  When used together with the values
         in tmnxOamVprnTrL3MapRteDestMask and tmnxOamVprnTrL3MapRteVprnLabel
         a customer route can be identified.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamVprnTrL3MapDestAddrType and 
         tmnxOamVprnTrL3MapDestAddrType." 
    ::= { tmnxOamVprnTrL3MapEntry 3 }

tmnxOamVprnTrL3MapRteDestMask      OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRteDestMask indicates the number
         of prefix bits (prefix width) to be &-ed with IP address in
         tmnxOamVprnTrL3MapRteDestAddr.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress object tmnxOamVprnTrL3MapDestMaskLen." 
    ::= { tmnxOamVprnTrL3MapEntry 4 }

tmnxOamVprnTrL3MapRteVprnLabel OBJECT-TYPE
    SYNTAX      MplsLabel
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRteVprnLabel indicates the VPRN
         label associated with the route information returned in this
         'vprnTraceRoute' reply."
    ::= { tmnxOamVprnTrL3MapEntry 5 }

tmnxOamVprnTrL3MapRteMetrics       OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRteMetrics indicates the metrics
         to be used with this vprnTraceRoute route."
    ::= { tmnxOamVprnTrL3MapEntry 6 }

tmnxOamVprnTrL3MapRteLastUp        OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRteLastUp indicates the time
         this route was last added or modified by the protocol."
    ::= { tmnxOamVprnTrL3MapEntry 7 }

tmnxOamVprnTrL3MapRteOwner         OBJECT-TYPE
    SYNTAX      INTEGER {
                    invalid (0),
                    local   (1),
                    static  (2),
                    rip     (3),
                    ospf    (4),
                    isis    (5),
                    bgp     (6),
                    bgpVpn  (7),
                    ldp     (8),
                    aggregate (9),
                    any     (10)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRteOwner indicates the protocol
         that owns this route.  It may be a local or remote route."
    ::= { tmnxOamVprnTrL3MapEntry 8 }

tmnxOamVprnTrL3MapRtePref          OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRtePref indicates the route
         preference value for this route."
    ::= { tmnxOamVprnTrL3MapEntry 9 }

tmnxOamVprnTrL3MapRteDist          OBJECT-TYPE
    SYNTAX      TmnxVPNRouteDistinguisher
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapRteDist is the route distinguisher
         associated with the route information returned in this 'vprnTraceRoute'
         reply."
    ::= { tmnxOamVprnTrL3MapEntry 10 }

tmnxOamVprnTrL3MapNumNextHops      OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapNumNextHops indicates the number
         of entries in the tmnxOamVprnTrNextHopTable for this route."
    ::= { tmnxOamVprnTrL3MapEntry 11 }

tmnxOamVprnTrL3MapNumRteTargets    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapNumRteTargets indicates the number
         of entries in the tmnxOamVprnTrRTTable for this route."
    ::= { tmnxOamVprnTrL3MapEntry 12 }

tmnxOamVprnTrL3MapDestAddrType      OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapDestAddrType indicates the type
         of internet address stored in tmnxOamVprnTrL3MapDestAddress." 
    ::= { tmnxOamVprnTrL3MapEntry 13 }

tmnxOamVprnTrL3MapDestAddress      OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapDestAddress indicates the Internet 
         address to be advertised for the route information returned by
         this 'vprnTraceRoute' reply.  When used together with the values
         in tmnxOamVprnTrL3MapRteDestMask and tmnxOamVprnTrL3MapRteVprnLabel
         a customer route can be identified."
    ::= { tmnxOamVprnTrL3MapEntry 14 }

tmnxOamVprnTrL3MapDestMaskLen      OBJECT-TYPE
    SYNTAX      InetAddressPrefixLength
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrL3MapDestMaskLen indicates the number
         of prefix bits (prefix width) to be &-ed with the IP address in
         tmnxOamVprnTrL3MapDestAddress."
    ::= { tmnxOamVprnTrL3MapEntry 15 }


--
-- Alcatel 7x50 SR OAM VPRN TraceRoute L3 Route Information Next Hop Table
--
tmnxOamVprnTrNextHopTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVprnTrNextHopEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'vprnTraceRoute'
         probe operation where a L3 route next hop list is returned.

         An entry in this table is created when the result of an OAM 
         'vprnTraceRoute' probe is determined.  
         An entry is removed from this table when its corresponding 
         tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 13 }

tmnxOamVprnTrNextHopEntry OBJECT-TYPE
    SYNTAX      TmnxOamVprnTrNextHopEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVprnTrNextHopTable.  The first two 
         index elements identify the tmnxOamTrCtlEntry that a 
         tmnxOamVprnTrNextHopEntry belongs to.  The third index element
         identifies an OAM trace route test run.  The fourth, fifth and sixth
         index elements select a single OAM 'vprnTraceRoute' reply.  The
         seventh index selects either the requestor's or responder's route
         information. The eighth index selects a single L3 next hop entry 
         within a specific 'vprnTraceRoute' probe reply route information."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex,
            tmnxOamVprnTrL3MapReporter,
            tmnxOamVprnTrNextHopIndex
           }
    ::= { tmnxOamVprnTrNextHopTable 1 }

 TmnxOamVprnTrNextHopEntry ::=
    SEQUENCE {
       tmnxOamVprnTrNextHopIndex      Unsigned32,
       tmnxOamVprnTrNextHopRtrID      RouterID,
       tmnxOamVprnTrNextHopType       INTEGER,
       tmnxOamVprnTrNextHopTunnelID   TmnxTunnelID,
       tmnxOamVprnTrNextHopTunnelType TmnxTunnelType,
       tmnxOamVprnTrNextHopIfIndex    InterfaceIndex,
       tmnxOamVprnTrNextHopAddrType   InetAddressType,
       tmnxOamVprnTrNextHopAddress    InetAddress
    }

tmnxOamVprnTrNextHopIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxOamVprnTrNextHopIndex selects a single L3 next hop entry
         associated with the router information returned within a specific 
         'vprnTraceRoute' probe reply.

         The agent starts assigning tmnxOamVprnTrNextHopIndex values at 1." 
    ::= { tmnxOamVprnTrNextHopEntry 1 }

tmnxOamVprnTrNextHopRtrID    OBJECT-TYPE
    SYNTAX      RouterID
    MAX-ACCESS  read-only
    STATUS      obsolete 
    DESCRIPTION
        "The value of tmnxOamVprnTrNextHopRtrID is the router ID for this
         L3 next hop entry.
         
         This object was obsoleted in release 6.0 and replaced with
         InetAddress objects tmnxOamVprnTrNextHopAddrType and 
         tmnxOamVprnTrNextHopAddress." 
    ::= { tmnxOamVprnTrNextHopEntry 2 }

tmnxOamVprnTrNextHopType       OBJECT-TYPE
    SYNTAX      INTEGER {
                    local (1),
                    remote (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrNextHopType indicates whether the
         route owner is a local route or a remote, BGP-VPRN, route."
    ::= { tmnxOamVprnTrNextHopEntry 3 }
 
tmnxOamVprnTrNextHopTunnelID       OBJECT-TYPE
    SYNTAX      TmnxTunnelID
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamVprnTrNextHopType is 'remote' the
         value of tmnxOamVprnTrNextHopTunnelID indicates the tunnel ID
         used to reach this BGP next hop."
    ::= { tmnxOamVprnTrNextHopEntry 4 }

tmnxOamVprnTrNextHopTunnelType     OBJECT-TYPE
    SYNTAX      TmnxTunnelType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamVprnTrNextHopType is 'remote' the
         value of tmnxOamVprnTrNextHopTunnelType indicates the type of
         tunnel used to reach this BGP next hop."
    ::= { tmnxOamVprnTrNextHopEntry 5 }

tmnxOamVprnTrNextHopIfIndex        OBJECT-TYPE
    SYNTAX      InterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "When the value of tmnxOamVprnTrNextHopType is 'local' the
         value of tmnxOamVprnTrNextHopIfIndex indicates the interface
         used to reach this ECMP next hop."
    ::= { tmnxOamVprnTrNextHopEntry 6 }

tmnxOamVprnTrNextHopAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamVprnTrNextHopAddrType indicates the type of Internet
         address stored in tmnxOamVprnTrNextHopAddress."
    ::= { tmnxOamVprnTrNextHopEntry 7 }

tmnxOamVprnTrNextHopAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrNextHopAddress is the Internet address 
         for this L3 next hop entry."
    ::= { tmnxOamVprnTrNextHopEntry 8 }

--
--  Alcatel 7x50 SR series OAM VPRN Trace Route Returned Route 
--  Information Target Table
--

tmnxOamVprnTrRTTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVprnTrRTEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'vprnTraceRoute'
         probe operation where a L3 route target list is returned.

         An entry in this table is created when the result of an OAM 
         'vprnTraceRoute' probe is determined.
         An entry is removed from this table when its corresponding 
         tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 14 }

tmnxOamVprnTrRTEntry OBJECT-TYPE
    SYNTAX      TmnxOamVprnTrRTEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVprnTrRTTable.  The first two 
         index elements identify the tmnxOamTrCtlEntry that a 
         tmnxOamVprnTrRTEntry belongs to.  The third index element
         identifies an OAM trace route test run.  The fouth, fifth and sixth 
         index elements select a single OAM 'vprnTraceRoute' reply.  
         The seventh index selects either the probe requestor's or
         probe replier's route information.  The eighth index selects 
         successive L3 route target entries contained in a specific 
         'vprnTraceRoute' requestor or probe reply route information."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex,
            tmnxOamVprnTrL3MapReporter,
            tmnxOamVprnTrRTIndex
           }
    ::= { tmnxOamVprnTrRTTable 1 }

 TmnxOamVprnTrRTEntry ::=
    SEQUENCE {
       tmnxOamVprnTrRTIndex        Unsigned32,
       tmnxOamVprnTrRouteTarget    TmnxBgpRouteTarget
    }

tmnxOamVprnTrRTIndex OBJECT-TYPE
--  NOTE: The UCD snmpget utility program does not parse this correctly
--    SYNTAX      Unsigned32 (1..'ffffffff'h)
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tmnxOamVprnTrRTIndex selects a single L3 route target entry
         associated with the router information returned within a specific 
         'vprnTraceRoute' probe reply.

         The agent starts assigning tmnxOamVprnTrRTIndex values at 1." 
    ::= { tmnxOamVprnTrRTEntry 1 }

tmnxOamVprnTrRouteTarget    OBJECT-TYPE
    SYNTAX      TmnxBgpRouteTarget
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVprnTrRouteTarget is the route target ID 
         for this L3 route target entry."
    ::= { tmnxOamVprnTrRTEntry 2 }

--
--  Alcatel 7x50 SR series OAM Multicast Trace Route Control Table
--
--
--  Sparse Dependent Extention of the tmnxOamTrCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamTrCtlTable, 
--  and the sparse dependent table, tmnxOamMcastTrCtlTable. 
--
--  This in effect extends the tmnxOamTrCtlTable with additional columns.
--  Rows are created in the tmnxOamMcastTrCtlTable only for those entries 
--  in the tmnxOamTrCtlTable where tmnxOamTrCtlTestMode has a value of 
--  'mcastTraceRoute'.  
--  
--  Deletion of a row in the tmnxOamTrCtlTable results in the 
--  same fate for the row in the tmnxOamMcastTrCtlTable.
--
tmnxOamMcastTrCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMcastTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM Multicast Trace Route Control Table 
         for providing, via SNMP, the capability of performing Alcatel 7x50 SR
         OAM 'mcastTraceRoute' test operations.  The results of these tests 
         are stored in the tmnxOamTrResultsTable, the 
         tmnxOamTrProbeHistoryTable and the tmnxOamMcastTrRespTable."
   ::= { tmnxOamTraceRouteObjs 16 }

tmnxOamMcastTrCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamMcastTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMcastTrCtlTable.  The first index
         element, tmnxOamTrCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamTrCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamMcastTrCtlTable 1 }

 TmnxOamMcastTrCtlEntry ::=
    SEQUENCE {
       tmnxOamMcastTrCtlVRtrID              TmnxVRtrID,
       tmnxOamMcastTrCtlSrcIpAddr           IpAddress,
       tmnxOamMcastTrCtlDestIpAddr          IpAddress,
       tmnxOamMcastTrCtlRespIpAddr          IpAddress,
       tmnxOamMcastTrCtlGrpIpAddr           IpAddress,
       tmnxOamMcastTrCtlHops                Unsigned32,
       tmnxOamMcastTrQueryId                Unsigned32,
       tmnxOamMcastTrCtlSrcAddrType         InetAddressType,
       tmnxOamMcastTrCtlSrcAddress          InetAddress,
       tmnxOamMcastTrCtlDestAddrType        InetAddressType,
       tmnxOamMcastTrCtlDestAddress         InetAddress,
       tmnxOamMcastTrCtlRespAddrType        InetAddressType,
       tmnxOamMcastTrCtlRespAddress         InetAddress,
       tmnxOamMcastTrCtlGrpAddrType         InetAddressType,
       tmnxOamMcastTrCtlGrpAddress          InetAddress
    }

tmnxOamMcastTrCtlVRtrID  OBJECT-TYPE
    SYNTAX      TmnxVRtrID
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The unique value which identifies this virtual router in the 
         Alcatel 7x50 SR system. The vRtrID value for each virtual router 
         must remain constant at least from one re-initialization of the 
         system management processor (CPM) to the next.  There will always 
         be at least one router entry defined by the agent with vRtrID=1 
         which represents the base transport router."
    DEFVAL { 1 }
    ::= { tmnxOamMcastTrCtlEntry 1 }

tmnxOamMcastTrCtlSrcIpAddr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMcastTrCtlSrcIpAddr is used to specify the Ipv4 address to 
         be used as the source for performing an OAM 'mcastTraceRoute' 
         operation when tmnxOamTrCtlTestMode has a value of 'mcastTraceRoute'.
         This is a required parameter to initiate a multicast trace.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMcastTrCtlSrcAddrType and 
         tmnxOamMcastTrCtlSrcAddress." 
    ::= { tmnxOamMcastTrCtlEntry 2 }

tmnxOamMcastTrCtlDestIpAddr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMcastTrCtlDestIpAddr is used to specify the Ipv4 address to 
         be used as the destination address for performing an OAM 
         'mcastTraceRoute' operation when tmnxOamTrCtlTestMode has a value 
         of 'mcastTraceRoute'. The multicast trace query is sent to
         this destination address. If this object is not set, the multicast 
         trace starts from this router. This is not a required parameter.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMcastTrCtlDestAddrType and 
         tmnxOamMcastTrCtlDestAddress." 
    DEFVAL { '00000000'h }      -- 0.0.0.0
    ::= { tmnxOamMcastTrCtlEntry 3 }
    
tmnxOamMcastTrCtlRespIpAddr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMcastTrCtlRespIpAddr is used to specify the Ipv4 address 
         to be used as the response address for performing an OAM 
         'mcastTraceRoute' operation. If this object is not set, then 
         the system ip address is used as the response address. This is 
         not a required parameter.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMcastTrCtlRespAddrType and 
         tmnxOamMcastTrCtlRespAddress." 
    ::= { tmnxOamMcastTrCtlEntry 4 }

tmnxOamMcastTrCtlGrpIpAddr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMcastTrCtlGrpIpAddr is used to specify the Ipv4 multicast
         group address for doing a 'mcastTraceRoute'  operation. This is 
         a required parameter to initiate a multicast trace.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMcastTrCtlGrpAddrType and 
         tmnxOamMcastTrCtlGrpAddress." 
    ::= { tmnxOamMcastTrCtlEntry 5 }

tmnxOamMcastTrCtlHops    OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlHops is used to specify the maximum number of hops
         that will be traced along the path to the source. This is not a 
         required parameter."
    DEFVAL { 32 }
    ::= { tmnxOamMcastTrCtlEntry 6 }

tmnxOamMcastTrQueryId    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrQueryId is the 24 bit random number that uniquely
         identifies the multicast trace query. When the router receives the
         response back from the first hop router, it maps the response to the
         query by looking at the query id in the packet."
    ::= { tmnxOamMcastTrCtlEntry 7 }

tmnxOamMcastTrCtlSrcAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlSrcAddrType specifies the type of Internet
         address stored in tmnxOamMcastTrCtlSrcAddress."
    DEFVAL { unknown }
    ::= { tmnxOamMcastTrCtlEntry 8 }

tmnxOamMcastTrCtlSrcAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlSrcAddress specifies the Internet address to 
         be used as the source for performing an OAM 'mcastTraceRoute' 
         operation when tmnxOamTrCtlTestMode has a value of 'mcastTraceRoute'.

         This is a required parameter to initiate a multicast trace."
    DEFVAL { ''H }
    ::= { tmnxOamMcastTrCtlEntry 9 }

tmnxOamMcastTrCtlDestAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlDestAddrType specifies the type of Internet
         address stored in tmnxOamMcastTrCtlDestAddress."
    DEFVAL { unknown }
    ::= { tmnxOamMcastTrCtlEntry 10 }
    
tmnxOamMcastTrCtlDestAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlDestAddress specifies the Internet address to 
         be used as the destination address for performing an OAM 
         'mcastTraceRoute' operation when tmnxOamTrCtlTestMode has a value 
         of 'mcastTraceRoute'. The multicast trace query is sent to
         this destination address. If this object is not set, the multicast 
         trace starts from this router. This is not a required parameter."
    DEFVAL { ''H }
    ::= { tmnxOamMcastTrCtlEntry 11 }
    
tmnxOamMcastTrCtlRespAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlRespAddrType specifies the type of Internet
         address stored in tmnxOamMcastTrCtlRespAddress."
    DEFVAL { unknown }
    ::= { tmnxOamMcastTrCtlEntry 12 }

tmnxOamMcastTrCtlRespAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlRespAddress is used to specify the Internet 
         address to be used as the response address for performing an OAM 
         'mcastTraceRoute' operation. If this object is not set, then 
         the system ip address is used as the response address. This is 
         not a required parameter."
    DEFVAL { ''H }
    ::= { tmnxOamMcastTrCtlEntry 13 }

tmnxOamMcastTrCtlGrpAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlGrpAddrType specifies the type of Internet
         address stored in tmnxOamMcastTrCtlGrpAddress."
    DEFVAL { unknown }
    ::= { tmnxOamMcastTrCtlEntry 14 }

tmnxOamMcastTrCtlGrpAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrCtlGrpAddress specifies the Internet multicast
         group address for doing a 'mcastTraceRoute'  operation. This is 
         a required parameter to initiate a multicast trace."
    DEFVAL { ''H }
    ::= { tmnxOamMcastTrCtlEntry 15 }



-- Multicast Trace Response Table

tmnxOamMcastTrRespTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamMcastTrRespEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM Multicast Trace Route Response Table 
         for providing, via SNMP, the result of OAM 'mcastTraceRoute' test 
         operation.  The results of these tests are stored in the 
         tmnxOamTrResultsTable, the tmnxOamTrProbeHistoryTable and 
         the tmnxOamMcastTrRespTable."
   ::= { tmnxOamTraceRouteObjs 17 }

tmnxOamMcastTrRespEntry OBJECT-TYPE
    SYNTAX      TmnxOamMcastTrRespEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamMcastTrRespTable.  
         The first two index elements identify the tmnxOamTrCtlEntry that 
         a tmnxOamMcastTrRespEntry belongs to.  The third index element
         selects an OAM trace route test run.  The fourth index element 
         selects a single Alcatel 7x50 SR OAM Trace Route operation result.  
         The fifth and sixth indexes select the hop and the probe at that 
         hop for a particular Alcatel 7x50 SR OAM Trace Route operation."
    INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex
          }
    ::= { tmnxOamMcastTrRespTable 1 }

 TmnxOamMcastTrRespEntry ::=
    SEQUENCE {
       tmnxOamMcastTrRespQueryArrivalTime     Unsigned32,
       tmnxOamMcastTrRespInIfAddr             IpAddress,
       tmnxOamMcastTrRespOutIfAddr            IpAddress,
       tmnxOamMcastTrRespPrevHopRtrAddr       IpAddress,
       tmnxOamMcastTrRespInPktCount           Counter32,
       tmnxOamMcastTrRespOutPktCount          Counter32,
       tmnxOamMcastTrRespSGPktCount           Counter32,
       tmnxOamMcastTrRespRtgProtocol          INTEGER,
       tmnxOamMcastTrRespFwdTtl               Unsigned32,
       tmnxOamMcastTrRespMBZBit               Unsigned32,        
       tmnxOamMcastTrRespSrcBit               Unsigned32,
       tmnxOamMcastTrRespSrcMask              Unsigned32,
       tmnxOamMcastTrRespFwdCode              INTEGER,
       tmnxOamMcastTrRespInIfAddrType         InetAddressType,
       tmnxOamMcastTrRespInIfAddress          InetAddress,
       tmnxOamMcastTrRespOutIfAddrType        InetAddressType,
       tmnxOamMcastTrRespOutIfAddress         InetAddress,
       tmnxOamMcastTrRespPhRtrAddrType        InetAddressType,
       tmnxOamMcastTrRespPhRtrAddress         InetAddress
    }

tmnxOamMcastTrRespQueryArrivalTime    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespQueryArrivalTime specifies the 32 bit NTP timestamp.
         It is the time at which a particular hop received the Multicast
         Trace query/request."
    ::= { tmnxOamMcastTrRespEntry 1 }

tmnxOamMcastTrRespInIfAddr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMcastTrRespInIfAddr specifies the address of the interface on 
         which packets from this source and group are expected to arrive.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMcastTrRespInIfAddrType and 
         tmnxOamMcastTrRespInIfAddress." 
    ::= { tmnxOamMcastTrRespEntry 2 }

tmnxOamMcastTrRespOutIfAddr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMcastTrRespOutIfAddr specifies the address of the interface on 
         which packets from this source and group flow to the specified 
         destination.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMcastTrRespOutIfAddrType and 
         tmnxOamMcastTrRespOutIfAddress." 
    ::= { tmnxOamMcastTrRespEntry 3 }

tmnxOamMcastTrRespPrevHopRtrAddr    OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
        "tmnxOamMcastTrRespPrevHopRtrAddr specifies the router from which this 
         router expects packets from this source.  This may be a multicast 
         group (e.g. ALL-[protocol]-ROUTERS.MCAST.NET) if the previous hop is 
         not known because of the workings of the multicast routing protocol.

         This object was obsoleted in release 4.0 and replaced with
         InetAddress objects tmnxOamMcastTrRespPhRtrAddrType and 
         tmnxOamMcastTrRespPhRtrAddress." 
    ::= { tmnxOamMcastTrRespEntry 4 }

tmnxOamMcastTrRespInPktCount    OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespInPktCount contains the number of multicast packets 
         received for all groups and sources on the incoming interface, or 
         0xffffffff if no count can be reported.  This counter should have the 
         same value as ifInMulticastPkts from the IF-MIB for this interface."
    ::= { tmnxOamMcastTrRespEntry 5 }

tmnxOamMcastTrRespOutPktCount    OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespOutPktCount contains the number of multicast packets
         that have been transmitted or queued for transmission for all groups 
         and sources on the outgoing interface, or 0xffffffff if no count can 
         be reported.  This counter should have the same value as 
         ifOutMulticastPkts from the IF-MIB for this interface."
    ::= { tmnxOamMcastTrRespEntry 6 }

tmnxOamMcastTrRespSGPktCount    OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespSGPktCount counts the number of packets from the 
         specified source forwarded by this router to the specified group, 
         or 0xffffffff if no count can be reported. If the value of 
         tmnxOamMcastTrRespSrcBit is set to one, the count is for the source 
         network, as specified by tmnxOamMcastTrRespSrcMask.  
         If the S bit is set and the Src Mask field is 63, indicating no 
         source-specific state, the count is for all sources sending to 
         this group."
    ::= { tmnxOamMcastTrRespEntry 7 }

tmnxOamMcastTrRespRtgProtocol    OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown(0),
                    dvmrp(1),
                    mospf(2),
                    pim(3),
                    cbt(4),
                    pimSpecial(5),
                    pimStatic(6),
                    dvmrpStatic(7),
                    bgp4Plus(8),
                    cbtSpecial(9),
                    cbtStatic(10),
                    pimAssert(11)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespRtgProtocol describes the routing protocol in use 
         between this router and the previous-hop router."
    ::= { tmnxOamMcastTrRespEntry 8 }

tmnxOamMcastTrRespFwdTtl    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespFwdTtl contains the TTL that a packet is required 
         to have before it will be forwarded over the outgoing interface."
    ::= { tmnxOamMcastTrRespEntry 9 }

tmnxOamMcastTrRespMBZBit    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespMBZBit is zeroed on transmission and ignored on 
         reception."
    ::= { tmnxOamMcastTrRespEntry 10 }

tmnxOamMcastTrRespSrcBit    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If the value of tmnxOamMcastTrRespSrcBit is 1, it indicates that the 
         packet count for the source-group pair is for the source network, as 
         determined by masking the source address with 
         the tmnxOamMcastTrRespSrcMask field."
    ::= { tmnxOamMcastTrRespEntry 11 }

tmnxOamMcastTrRespSrcMask    OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespSrcMask contains the number of 1's in the netmask 
         this router has for the source (i.e. a value of 24 means the netmask is
         0xffffff00).  If the router is forwarding solely on group state, 
         this field is set to 63 (0x3f)."
    ::= { tmnxOamMcastTrRespEntry 12 }

tmnxOamMcastTrRespFwdCode    OBJECT-TYPE
    SYNTAX      INTEGER {
                    noError(0),
                    wrongIf(1),
                    pruneSent(2),
                    pruneRecvd(3),
                    scoped(4),
                    noRoute(5),
                    wrongLastHop(6),
                    notForwarding(7),
                    reachedRP(8),
                    rpfIf(9),
                    noMulticast(10),
                    infoHidden(11),
                    fatalError(128),
                    noSpace(129),
                    oldRouter(130),
                    adminProhib(131),
                    unknown(132)                  
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespFwdCode contains a forwarding information/error 
         code:
         noError(0)    - No error
         wrongIf(1)    - Traceroute request arrived on an interface to
                         which this router would not forward for this
                         source,group,destination.
         pruneSent(2)  - This router has sent a prune upstream which
                         applies to the source and group in the traceroute 
                         request.
         pruneRecvd(3) - This router has stopped forwarding for this
                         source and group in response to a request from
                         the next hop router.
         scoped(4)     - The group is subject to administrative scoping
                          at this hop.
         noRoute(5)    - This router has no route for the source or
                         group and no way to determine a potential route.
         wrongLastHop(6)  - This router is not the proper last-hop router.
         notForwarding(7) - This router is not forwarding this
                            source,group out the outgoing interface for an
                            unspecified reason.
         reachedRP(8) - Reached Rendez-vous Point or Core
         rpfIf(9)     - Traceroute request arrived on the expected RPF
                          interface for this source,group.
         noMulticast(10) - Traceroute request arrived on an interface
                          which is not enabled for multicast.
         infoHidden(11)  - One or more hops have been hidden from this trace.
         fatalError(12)  - Router may know the previous hop but cannot forward 
                           the message to it.
         noSpace(129)    - There was not enough room to insert another
                          response data block in the packet.
         oldRouter(130)  - The previous hop router does not understand
                           traceroute requests
         adminProhib(131) - Traceroute is administratively prohibited.
         unknown(132)     - Unknown forwarding code received."       
    ::= { tmnxOamMcastTrRespEntry 13 }

tmnxOamMcastTrRespInIfAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespInIfAddrType specifies the type of Internet
         address stored at tmnxOamMcastTrRespInIfAddress."
    ::= { tmnxOamMcastTrRespEntry 14 }

tmnxOamMcastTrRespInIfAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespInIfAddress specifies the Internet address of 
         the interface on which packets from this source and group are 
         expected to arrive."
    ::= { tmnxOamMcastTrRespEntry 15 }

tmnxOamMcastTrRespOutIfAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespOutIfAddrType specifies the type of Internet 
         address stored in tmnxOamMcastTrRespOutIfAddress." 
    ::= { tmnxOamMcastTrRespEntry 16 }

tmnxOamMcastTrRespOutIfAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespOutIfAddress specifies the Inetenet address 
         of the interface on which packets from this source and group 
         flow to the specified  destination."
    ::= { tmnxOamMcastTrRespEntry 17 }

tmnxOamMcastTrRespPhRtrAddrType    OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespPhRtrAddrType specifies the type of internet
         address stored in tmnxOamMcastTrRespPhRtrAddress."
    ::= { tmnxOamMcastTrRespEntry 18 }

tmnxOamMcastTrRespPhRtrAddress    OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "tmnxOamMcastTrRespPhRtrAddress specifies the Internet address of
         the router from which this router expects packets for this source.  
         This may be a multicast group (e.g. ALL-[protocol]-ROUTERS.MCAST.NET)
         if the previous hop is not known because of the workings of the 
         multicast routing protocol."
    ::= { tmnxOamMcastTrRespEntry 19 }


--
-- Notification Definition section
--
tmnxOamTrPathChange NOTIFICATION-TYPE
      OBJECTS {
          tmnxOamTrCtlTestMode,
          tmnxOamTrCtlLastRunResult,
          tmnxOamTrResultsOperStatus
      }
      STATUS  current
      DESCRIPTION
          "The path to a target has changed."
      ::= { tmnxOamTraceRouteNotifications 1 }

tmnxOamTrTestFailed NOTIFICATION-TYPE
      OBJECTS {
          tmnxOamTrCtlTestMode,
          tmnxOamTrCtlLastRunResult,
          tmnxOamTrResultsOperStatus
      }
      STATUS  current
      DESCRIPTION
          "The OAM trace route test failed to complete successfully."
      ::= { tmnxOamTraceRouteNotifications 2 }

tmnxOamTrTestCompleted NOTIFICATION-TYPE
      OBJECTS {
          tmnxOamTrCtlTestMode,
          tmnxOamTrCtlLastRunResult,
          tmnxOamTrResultsOperStatus
      }
      STATUS  current
      DESCRIPTION
          "The OAM trace route test has just been completed."
      ::= { tmnxOamTraceRouteNotifications 3 }

--
--  Alcatel 7x50 SR series OAM Service Assurance Agent Test Control Table
--
tmnxOamSaaCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamSaaCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM SAA Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7x50 SR OAM SAA test 
         operations.  The results of these tests are stored in the 
         ping or trace route results tables."
   ::= { tmnxOamSaaObjs 2 }

tmnxOamSaaCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamSaaCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamSaaCtlTable.  The first index
         element, tmnxOamSaaCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamSaaCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamSaaCtlOwnerIndex,
             tmnxOamSaaCtlTestIndex
          }
    ::= { tmnxOamSaaCtlTable 1 }

TmnxOamSaaCtlEntry ::=
    SEQUENCE {
        tmnxOamSaaCtlOwnerIndex         SnmpAdminString,
        tmnxOamSaaCtlTestIndex          SnmpAdminString,
        tmnxOamSaaCtlRowStatus          RowStatus,
        tmnxOamSaaCtlStorageType        StorageType,
        tmnxOamSaaCtlLastChanged        TimeStamp,
        tmnxOamSaaCtlAdminStatus        TmnxAdminState,
        tmnxOamSaaCtlDescr              TItemDescription,
        tmnxOamSaaCtlTestMode           TmnxOamTestMode,
        tmnxOamSaaCtlRuns               Counter32,
        tmnxOamSaaCtlFailures           Counter32,
        tmnxOamSaaCtlLastRunResult      INTEGER
    }

tmnxOamSaaCtlOwnerIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(1..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "To facilitate the provisioning of access control by a
        security administrator using the View-Based Access
        Control Model (RFC 2575, VACM) for tables in which
        multiple users may need to independently create or
        modify entries, the initial index is used as an 'owner
        index'.  Such an initial index has a syntax of
        SnmpAdminString, and can thus be trivially mapped to a
        security name or group name as defined in VACM, in
        accordance with a security policy.
 
        When used in conjunction with such a security policy all
        entries in the table belonging to a particular user (or
        group) will have the same value for this initial index.
        For a given user's entries in a particular table, the
        object identifiers for the information in these entries
        will have the same subidentifiers (except for the 'column'
        subidentifier) up to the end of the encoded owner index.
        To configure VACM to permit access to this portion of the
        table, one would create vacmViewTreeFamilyTable entries
        with the value of vacmViewTreeFamilySubtree including
        the owner index portion, and vacmViewTreeFamilyMask
        'wildcarding' the column subidentifier.  More elaborate
        configurations are possible."
    ::= { tmnxOamSaaCtlEntry 1 }
 
tmnxOamSaaCtlTestIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(1..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The test name index of the Alcatel 7x50 SR OAM Service Assurance
         Agent, SAA, test.  This is locally unique, within the scope of an 
         tmnxOamSaaCtlOwnerIndex."
    ::= { tmnxOamSaaCtlEntry 2 }

tmnxOamSaaCtlRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object allows entries to be created and deleted
         in the tmnxOamSaaCtlTable.  Deletion of an entry in this
         table results in all corresponding control and results entries
         in either the ping or traceroute tables.
 
         Activation of a Alcatel 7x50 SR OAM ping or trace route operation 
         is controlled via tmnxOamPingCtlAdminStatus or 
         tmnxOamTrCtlAdminStatus and not by changing this object's value 
         to 'active (1)'.
 
         An attempt to delete an entry in this table by setting this object's
         value to 'destroy (6)' will fail with an inconsistentValue error
         if the associated ping or traceroute test is in progress.
 
         An attempt to delete an entry in this table by setting this object's
         value to 'destroy (6)' will fail with an inconsistentValue error
         if the value of tmnxOamSaaCtlAdminStatus is not 'outOfService'.
 
         The operational state of an Alcatel 7x50 SR OAM SAA test operation
         can be determined by examination of its associated
         tmnxOamPingResultsOperStatus or tmnxOamTrResultsOperStatus object."
    REFERENCE
        "See definition of RowStatus in RFC 2579, 'Textual
        Conventions for SMIv2.'"
    ::= { tmnxOamSaaCtlEntry 3 }
 
tmnxOamSaaCtlStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The storage type for this conceptual row.
         Conceptual rows having the value 'permanent' need not
         allow write-access to any columnar objects in the row."
    DEFVAL { nonVolatile }
    ::= { tmnxOamSaaCtlEntry 4 }

tmnxOamSaaCtlLastChanged   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaCtlLastChanged indicates the time the
         value of a settable object in this row was last changed."
    ::= { tmnxOamSaaCtlEntry 5 }

tmnxOamSaaCtlAdminStatus OBJECT-TYPE
    SYNTAX      TmnxAdminState
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaCtlAdminStatus indicates the desired
         administrative state of the Service Assurance Agent, SAA,
         test.
         
         When the value of tmnxOamSaaCtlAdminStatus is 'outOfService',
         an attempt to start this SAA test by setting its associated 
         tmnxOamPingCtlAdminStatus or tmnxOamTrCtlAdminStatus to 
         'enabled (1)' will fail with an inconsistentValue error.
         
         When the value of tmnxOamSaaCtlAdminStatus is 'inService' attempts
         to modify any other object in the associated tmnxOamPingCtlTable, 
         tmnxOamTrCtlTable or the control table for the specific OAM test 
         type will fail with an inconsistentValue error."
    DEFVAL { outOfService }
    ::= { tmnxOamSaaCtlEntry 6 }

tmnxOamSaaCtlDescr OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaCtlDescr specifies a user provided description
         string for this SAA test.  It can consist of any printable, 
         seven-bit ASCII characters up to 80 characters in length."
    DEFVAL { ''H }  -- empty string
    ::= { tmnxOamSaaCtlEntry 7 }

tmnxOamSaaCtlTestMode OBJECT-TYPE
    SYNTAX      TmnxOamTestMode 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaCtlTestMode indicates the type of
         Service Assurance Agent, SAA, test to be performed.

         When the value of this object is 'notConfigured', no associated
         entry exists in either the tmnxOamPingCtlTable or tmnxOamTrCtlTable.

         When the value of this object is 'ping', an associated test entry
         is configured in the tmnxOamPingCtlTable.

         When the value of this object is 'traceroute', an associated test
         entry is configured in the tmnxOamTrCtlTable."
    DEFVAL { notConfigured }
    ::= { tmnxOamSaaCtlEntry 8 }

tmnxOamSaaCtlRuns      OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaCtlRuns indicates the number of times
         this OAM SAAtest has been executed."
    ::= { tmnxOamSaaCtlEntry 9 }

tmnxOamSaaCtlFailures  OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaCtlFailures indicates the number of times
         this OAM SAA test has failed."
    ::= { tmnxOamSaaCtlEntry 10 }

tmnxOamSaaCtlLastRunResult  OBJECT-TYPE
    SYNTAX      INTEGER {
                    undetermined (0),
                    success (1),
                    failed (2),
                    aborted (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaCtlLastRunResult indicates the completion
         status the last time this test was executed.  If this OAM test is
         currently in progress, this object indicates the result of the
         previous test run, if any."
    ::= { tmnxOamSaaCtlEntry 11 }

--
--  Alcatel 7x50 SR series OAM SAA Test Thresholds Table
--

tmnxOamSaaThresholdTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamSaaThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7x50 SR OAM ping thresholds table for providing, 
         via SNMP, the capability of setting rising and falling thresholds
         on metrics resulting from Alcatel 7x50 SR SAA OAM trace route test 
         operations.  The results of the ping tests are stored in the 
         tmnxOamTrResultsTable and the tmnxOamTrProbeHistoryTable."
   ::= { tmnxOamSaaObjs 3 }

tmnxOamSaaThresholdEntry OBJECT-TYPE
    SYNTAX      TmnxOamSaaThresholdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamSaaThresholdTable.  The first index
         element, tmnxOamSaaCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamSaaCtlTestIndex, enables the same 
         management application to have multiple outstanding requests.
         The third index element identifies the type of statistic to be
         monitored for threshold crossing.  The fourth index element
         identifies which direction, rising or falling, to monitor."
    INDEX { tmnxOamSaaCtlOwnerIndex, 
            tmnxOamSaaCtlTestIndex, 
            tmnxOamSaaTType, 
            tmnxOamSaaTDirection
          }
    ::= { tmnxOamSaaThresholdTable 1 }

TmnxOamSaaThresholdEntry ::=
    SEQUENCE {
        tmnxOamSaaTType                    TmnxOamSaaThreshold,
        tmnxOamSaaTDirection               INTEGER,
        tmnxOamSaaTRowStatus               RowStatus,
        tmnxOamSaaTLastChanged             TimeStamp,
        tmnxOamSaaTThreshold               Integer32,
        tmnxOamSaaTValue                   Integer32,
        tmnxOamSaaTLastSent                TimeStamp,
        tmnxOamSaaTTestMode                TmnxOamTestMode,
        tmnxOamSaaTTestRunIndex            Unsigned32
    }
 
tmnxOamSaaTType        OBJECT-TYPE
    SYNTAX      TmnxOamSaaThreshold
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTType specifies the OAM trace route test
         statistic to be monitored to determine if a tmnxOamSaaThreshold
         notification should be generated at the completion of an
         SAA test run." 
    ::= { tmnxOamSaaThresholdEntry 1 }

tmnxOamSaaTDirection   OBJECT-TYPE
    SYNTAX      INTEGER {
                    rising (1),
                    falling (2)
                }
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTDirection specifies whether to
         monitor this threshold for crossing in the rising or falling
         direction."
    ::= { tmnxOamSaaThresholdEntry 2 }

tmnxOamSaaTRowStatus   OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTRowStatus controls creation and deletion
         of row entries in the tmnxOamSaaThresholdTable.
         
         When a row in this table is created, tmnxOamSaaTThreshold must
         also be set or the row creation will fail with an inconsistentValue
         error."
    ::= { tmnxOamSaaThresholdEntry 3 }

tmnxOamSaaTLastChanged OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTLastChanged indicates the last
         time the value of tmnxOamSaaTThreshold was modified."
    ::= { tmnxOamSaaThresholdEntry 4 }

tmnxOamSaaTThreshold   OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTThreshold specifies the threshold
         value to monitor to determine when to generate a tmnxOamSaaThreshold
         notification.
         
         A threshold value must be set when the row entry is created."
    ::= { tmnxOamSaaThresholdEntry 5 }

tmnxOamSaaTValue       OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTValue indicates the value of the
         statistic that last caused a tmnxOamSaaThreshold notification
         to be generated."    
    ::= { tmnxOamSaaThresholdEntry 6 }

tmnxOamSaaTLastSent OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTLastSent indicates the last time
         a tmnxOamSaaThreshold notification was generated as a result
         of crossing the threshold specified by the value of 
         tmnxOamSaaTThreshold."
    ::= { tmnxOamSaaThresholdEntry 7 }

tmnxOamSaaTTestMode OBJECT-TYPE
    SYNTAX      TmnxOamTestMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTTestMode indicates the type of
         Service Assurance Agent, SAA, test for which a tmnxOamSaaThreshold
         notification was generated as a result of crossing the threshold
         specified by the value of tmnxOamSaaTThreshold.

         When the value of this object is 'notConfigured', no associated
         entry exists in either the tmnxOamPingCtlTable or tmnxOamTrCtlTable.

         When the value of this object is 'ping', an associated test entry
         is configured in the tmnxOamPingCtlTable.

         When the value of this object is 'traceroute', an associated test
         entry is configured in the tmnxOamTrCtlTable."
    ::= { tmnxOamSaaThresholdEntry 8 }

tmnxOamSaaTTestRunIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamSaaTTestRunIndex indicates the test run index
         of the Service Assurance Agent, SAA, test for which the last 
         tmnxOamSaaThreshold notification was generated as a result of 
         crossing the threshold specified by the value of tmnxOamSaaTThreshold.

         When the value of tmnxOamSaaTTestMode is 'ping', the value of
         this object is the value of the tmnxOamPingResultsTestRunIndex 
         that identifies entries for the ping test results of the test run
         that generated the last tmnxOamSaaThreshold notification.

         When the value of tmnxOamSaaTTestMode is 'traceroute', the value of
         this object is the value of the tmnxOamTrResultsTestRunIndex 
         that identifies entries for the traceroute test results of the 
         test run that generated the last tmnxOamSaaThreshold notification.

         When the value of tmnxOamSaaTTestMode is 'notConfigured', the value of
         this object is zero."
    ::= { tmnxOamSaaThresholdEntry 9 }

--
-- Notification Definition section
--
tmnxOamSaaThreshold NOTIFICATION-TYPE
    OBJECTS {
        tmnxOamSaaTThreshold,
        tmnxOamSaaTValue,
        tmnxOamSaaCtlTestMode,
        tmnxOamSaaCtlLastRunResult,
        tmnxOamSaaTTestRunIndex
    }
    STATUS  current
    DESCRIPTION
        "Generated at the completion of an SAA OAM trace route 
         test when a threshold has been crossed for a results 
         statistic."
    ::= { tmnxOamSaaNotifications 1 }

--
--  Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Discovery Control Table
--
--
--  Sparsely dependent extension of the tmnxOamTrCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamTrCtlTable, 
--  and the sparsely dependent table, tmnxOamLTtraceCtlTable. 
--
--  This in effect extends the tmnxOamTrCtlTable with additional columns.
--  Rows are created in the tmnxOamLTtraceCtlTable only for those entries 
--  in the tmnxOamTrCtlTable where tmnxOamTrCtlTestMode has a value of 
--  'ldpTreeTrace'.  
--  
--  Deletion of a row in the tmnxOamTrCtlTable results in the 
--  same fate for the row in the tmnxOamLTtraceCtlTable.
--

tmnxOamLTtraceCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamLTtraceCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "tmnxOamLTtraceCtlTable defines the Alcatel 7x50 SR OAM LDP 
         Tree Trace Control Table for providing, via SNMP, the 
         capability of performing Alcatel 7x50 SR OAM 'ldpTreeTrace' 
         manual test operations. The results of these tests are stored 
         in the tmnxOamLTtraceResultsTable and the 
         tmnxOamLTtraceHopInfoTable."
   ::= { tmnxOamTraceRouteObjs 18 }

tmnxOamLTtraceCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamLTtraceCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxOamLTtraceCtlEntry represents a row in the 
         tmnxOamLTtraceCtlTable.  The first index element, 
         tmnxOamTrCtlOwnerIndex, is of type SnmpAdminString, 
         a textual convention that allows for use of the 
         SNMPv3 View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamTrCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamLTtraceCtlTable 1 }

TmnxOamLTtraceCtlEntry ::=
    SEQUENCE {
        tmnxOamLTtraceCtlLdpPrefixType      InetAddressType,
        tmnxOamLTtraceCtlLdpPrefix          InetAddress,
        tmnxOamLTtraceCtlLdpPrefixLen       InetAddressPrefixLength,
        tmnxOamLTtraceCtlMaxPath            Unsigned32
    }

tmnxOamLTtraceCtlLdpPrefixType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceCtlLdpPrefixType specifies the 
         type of Internet address stored in tmnxOamLTtraceCtlLdpPrefix."
    DEFVAL { unknown }
    ::= { tmnxOamLTtraceCtlEntry 2 }

tmnxOamLTtraceCtlLdpPrefix     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceCtlLdpPrefix specifies the 
         Internet address prefix for the LDP based LSP for this 
         OAM LDP Tree discovery test."
    DEFVAL { ''H }
    ::= { tmnxOamLTtraceCtlEntry 3 }

tmnxOamLTtraceCtlLdpPrefixLen     OBJECT-TYPE
    SYNTAX      InetAddressPrefixLength
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceCtlLdpPrefixLen specifies the Internet
         address prefix length for the LDP based LSP for this OAM LDP Tree 
         discovery test. 
         
         The value of this parameter is valid only when LDP IP Prefix 
         has been specified."
    DEFVAL { 32 } 
    ::= { tmnxOamLTtraceCtlEntry 4 }

tmnxOamLTtraceCtlMaxPath OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceCtlMaxPath specifies the maximum number
         of paths that can be discovered for the given LDP Prefix 
         (tmnxOamLTtraceCtlLdpPrefix)."
    DEFVAL { 128 }
    ::= { tmnxOamLTtraceCtlEntry 5 }

--
-- Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Manual Discovery Test
-- Simple Object Definitions
--
tmnxOamLTtraceMaxConRequests OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "requests"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
       "The value of tmnxOamLTtraceMaxConRequests indicates the 
        maximum number of concurrent Ldp Tree Trace manual discovery
        sessions that are allowed within an agent implementation."
    ::= { tmnxOamTraceRouteObjs 19 }

--
-- Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Manual Discovery Test
-- Results Table
--
tmnxOamLTtraceResultsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamLTtraceResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxOamLTtraceResultsTable defines the Alcatel 7x50 SR OAM 
         Trace Route Test Results Table for keeping track of the status 
         of a tmnxOamTrCtlEntry having tmnxOamTrCtlTestMode as ldpTreeTrace.

         An entry is added to the tmnxOamLTtraceResultsTable when an
         tmnxOamTrCtlEntry is created with  tmnxOamTrCtlTestMode having 
         value 'ldpTreeTrace'.

         An entry is removed from the tmnxOamTrResultsTable when
         such a corresponding entry, tmnxOamTrCtlEntry, is deleted."
   ::= { tmnxOamTraceRouteObjs 20 }

tmnxOamLTtraceResultsEntry OBJECT-TYPE
    SYNTAX      TmnxOamLTtraceResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxOamLTtraceResultsEntry represents a row in the 
         tmnxOamLTtraceResultsTable.  The two indices of the 
         tmnxOamLTtraceResultsTable are the same as that in the 
         tmnxOamTrCtlTable in order for a tmnxOamLTtraceResultsEntry to
         correspond to the tmnxOamTrCtlEntry that caused it to be
         created."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamLTtraceResultsTable 1 }

TmnxOamLTtraceResultsEntry ::=
    SEQUENCE {
        tmnxOamLTtraceResultsDisPaths         Unsigned32,
        tmnxOamLTtraceResultsFailedHops       Unsigned32,
        tmnxOamLTtraceResultsDisState         INTEGER,
        tmnxOamLTtraceResultsDisStatus        TmnxOamLTtraceDisStatusBits
    }

tmnxOamLTtraceResultsDisPaths OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceResultsDisPaths indicates the number of 
         discovered ECMP paths in this OAM LDP Tree discovery test."
    ::= { tmnxOamLTtraceResultsEntry 2 }

tmnxOamLTtraceResultsFailedHops OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceResultsFailedHops indicates the number 
         of hops from which no successful response was received during
         this OAM LDP Tree discovery test."
    ::= { tmnxOamLTtraceResultsEntry 3 }

tmnxOamLTtraceResultsDisState       OBJECT-TYPE
    SYNTAX      INTEGER {
                    initial (0),
                    inProgress (1),
                    done (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceResultsDisState indicates the current
         state of the discovery process for the LDP IP prefix
        (tmnxOamLTtraceCtlLdpPrefix) in this OAM LDP Tree discovery test."
    ::= { tmnxOamLTtraceResultsEntry  4 }

tmnxOamLTtraceResultsDisStatus OBJECT-TYPE
    SYNTAX      TmnxOamLTtraceDisStatusBits 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceResultsDisStatus indicates the current 
         discovery status bits for the LDP IP prefix
         (tmnxOamLTtraceCtlLdpPrefix) in this OAM LDP Tree discovery test."
    ::= { tmnxOamLTtraceResultsEntry 5 }
--
-- Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Manual Discovery Test
-- Hops Table
--
tmnxOamLTtraceHopInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamLTtraceHopInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxOamLTtraceHopInfoTable defines the Alcatel 7x50 SR OAM LDP Trace 
         Hops table for keeping track of the results of an LDP Tree Trace
         test on a per hop basis.

         An entry is added to the tmnxOamLTtraceHopInfoTable when an
         hop is discovered after setting the tmnxOamTrCtlAdminStatus  
         object of the tmnxOamTrCtlEntry (having tmnxOamTrCtlTestMode 
         as ldpTreeTrace) to 'enabled(1)'.  An entry is removed from the 
         tmnxOamLTtraceHopInfoTable when its corresponding tmnxOamTrCtlEntry
         is deleted."
   ::= { tmnxOamTraceRouteObjs 21 }

tmnxOamLTtraceHopInfoEntry OBJECT-TYPE
    SYNTAX      TmnxOamLTtraceHopInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "tmnxOamLTtraceHopInfoEntry represents a row in the
         tmnxOamLTtraceHopInfoTable. The first two indices of the
         tmnxOamLTtraceHopInfoTable are the same as the tmnxOamTrCtlTable in
         order for a tmnxOamLTtraceHopInfoEntry to correspond to the 
         tmnxOamTrCtlEntry that caused it to be created. The third index 
         element, tmnxOamLTtraceHopIndex, selects a hop in an Alcatel 
         7x50 SR Ldp Tree Trace discovered path."
    INDEX {
           tmnxOamTrCtlOwnerIndex,
           tmnxOamTrCtlTestIndex,
           tmnxOamLTtraceHopIndex
          }
    ::= { tmnxOamLTtraceHopInfoTable 1 }

TmnxOamLTtraceHopInfoEntry ::=
    SEQUENCE {
        tmnxOamLTtraceHopIndex             Unsigned32,
        tmnxOamLTtraceUpStreamHopIndex     Unsigned32,
        tmnxOamLTtraceHopAddrType          InetAddressType,
        tmnxOamLTtraceHopAddr              InetAddress,
        tmnxOamLTtraceHopDstAddrType       InetAddressType,
        tmnxOamLTtraceHopDstAddr           InetAddress,
        tmnxOamLTtraceHopEgrNhAddrType     InetAddressType,
        tmnxOamLTtraceHopEgrNhAddr         InetAddress,
        tmnxOamLTtraceHopDisTtl            Unsigned32,
        tmnxOamLTtraceHopLastRc            TmnxOamPingRtnCode,
        tmnxOamLTtraceHopDiscoveryState    INTEGER,
        tmnxOamLTtraceHopDiscoveryTime     TimeStamp
    }

tmnxOamLTtraceHopIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopIndex indicates the hop index 
         for an Alcatel 7x50 SR OAM Trace hop for an LDP Tree Trace test. 
         The hop index values are assigned starting at 1."
    ::= { tmnxOamLTtraceHopInfoEntry 1 }

tmnxOamLTtraceUpStreamHopIndex OBJECT-TYPE 
    SYNTAX      Unsigned32 
    MAX-ACCESS  read-only 
    STATUS      current 
    DESCRIPTION 
        "The value of tmnxOamLTtraceUpStreamHopIndex indicates the 
         hop index (tmnxOamLTtraceHopIndex) of its upstream hop 
         discovered during the LDP Tree Trace test.  The value of 
         tmnxOamLTtraceUpStreamHopIndex is 0 for the hops which are 
         the root of the different discovered paths for the LDP 
         Tree Trace test." 
    ::= { tmnxOamLTtraceHopInfoEntry 2 } 

tmnxOamLTtraceHopAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopAddrType indicates the type of
         Internet address stored in tmnxOamLTtraceHopAddr."
    ::= { tmnxOamLTtraceHopInfoEntry 3 }

tmnxOamLTtraceHopAddr     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopAddr indicates the IP address   
         of a hop for a given LDP based LSP associated with the
         OAM LDP Tree trace test."
    ::= { tmnxOamLTtraceHopInfoEntry 4 }

tmnxOamLTtraceHopDstAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopDstAddrType indicates the type of
         Internet address stored in tmnxOamLTtraceHopDstAddr."
    ::= { tmnxOamLTtraceHopInfoEntry 5 }

tmnxOamLTtraceHopDstAddr     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopDstAddr indicates the destination 
         IP address (127 Address) of a path associated with the hop."
    ::= { tmnxOamLTtraceHopInfoEntry 6 }

tmnxOamLTtraceHopEgrNhAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopEgrNhAddrType indicates the type of
         Internet address stored in tmnxOamLTtraceHopEgrNhAddr."
    ::= { tmnxOamLTtraceHopInfoEntry 7 }

tmnxOamLTtraceHopEgrNhAddr     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopEgrNhAddr indicates the  
         next hop  IP address (wrt the ingress router), which is used 
         to reach this Hop (tmnxOamLTtraceHopAddr)."
    ::= { tmnxOamLTtraceHopInfoEntry 8 }

tmnxOamLTtraceHopDisTtl OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopDisTtl indicates the label time-to-live 
         value used to discover this hop."
    ::= { tmnxOamLTtraceHopInfoEntry 9 }

tmnxOamLTtraceHopLastRc    OBJECT-TYPE
    SYNTAX      TmnxOamPingRtnCode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopLastRc indicates the OAM return code 
         received in the OAM trace response."
    ::= { tmnxOamLTtraceHopInfoEntry 10 }

tmnxOamLTtraceHopDiscoveryState       OBJECT-TYPE
    SYNTAX      INTEGER {
                    inProgress (0),
                    doneOk (1),
                    doneTimeout (2),
                    doneLoopDetected (3),
                    doneExpiredTtl (4)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopDiscoveryState indicates the current
         discovery state of this hop."
    ::= { tmnxOamLTtraceHopInfoEntry 11 }

tmnxOamLTtraceHopDiscoveryTime   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceHopDiscoveryTime indicates the sysUpTime 
         when the hop was discovered."
    ::= { tmnxOamLTtraceHopInfoEntry 12 }

--
--  Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Auto Config Table
--

tmnxOamLTtraceAutoConfigTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF TmnxOamLTtraceAutoConfigEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtraceAutoConfigTable contains an entry for configuration 
         information about each instance of Automatic (background) LDP Tree
         Trace."
    ::= { tmnxOamTraceRouteObjs 22 }

tmnxOamLTtraceAutoConfigEntry OBJECT-TYPE
    SYNTAX     TmnxOamLTtraceAutoConfigEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtraceAutoConfigEntry represents a row in the 
         tmnxOamLTtraceAutoConfigTable.  Each entry represents the 
         Automatic LDP Tree Trace instance running on a virtual router.

          A row entry can be created and deleted through the
          tmnxOamLTtraceAutoRowStatus object."
    INDEX      { vRtrID }
    ::= { tmnxOamLTtraceAutoConfigTable 1 }

TmnxOamLTtraceAutoConfigEntry ::= 
    SEQUENCE {
        tmnxOamLTtraceAutoRowStatus         RowStatus,
        tmnxOamLTtraceAutoLastChanged       TimeStamp,
        tmnxOamLTtraceAutoStorageType       StorageType,
        tmnxOamLTtraceAutoAdminState        TmnxAdminState,
        tmnxOamLTtraceAutoFcName            TFCName,
        tmnxOamLTtraceAutoProfile           TProfile,
        tmnxOamLTtraceAutoDiscIntvl         Unsigned32,
        tmnxOamLTtraceAutoMaxPath           Unsigned32,
        tmnxOamLTtraceAutoTrMaxTtl          Unsigned32,
        tmnxOamLTtraceAutoTrTimeOut         Unsigned32,
        tmnxOamLTtraceAutoTrMaxFailures     Unsigned32,
        tmnxOamLTtraceAutoPolicy1           TPolicyStatementNameOrEmpty,
        tmnxOamLTtraceAutoPolicy2           TPolicyStatementNameOrEmpty,
        tmnxOamLTtraceAutoPolicy3           TPolicyStatementNameOrEmpty,
        tmnxOamLTtraceAutoPolicy4           TPolicyStatementNameOrEmpty,
        tmnxOamLTtraceAutoPolicy5           TPolicyStatementNameOrEmpty,
        tmnxOamLTtraceAutoProbeIntvl        Unsigned32,
        tmnxOamLTtraceAutoPrTimeOut         Unsigned32,
        tmnxOamLTtraceAutoPrMaxFailures     Unsigned32
   }

tmnxOamLTtraceAutoRowStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoRowStatus specifies the row status of
          the Auto LDP Tree Trace instance for the virtual router.

         A row with default attribute values is created by setting 
         tmnxOamLTtraceAutoRowStatus to 'createAndGo'. A row entry can be 
         destroyed by setting tmnxOamLTtraceAutoRowStatus to 'destroy'. An 
         attempt to destroy a row will fail if tmnxOamLTtraceAutoAdminState 
         is not set to 'outOfService'.
         
         Deletion of an entry in this table results in deletion 
         of all the corresponding tables: tmnxOamLTtraceAutoStatusTable,
         tmnxOamLTtraceFecInfoTable, and tmnxOamLTtracePathInfoTable."
    ::= { tmnxOamLTtraceAutoConfigEntry 1 }

tmnxOamLTtraceAutoLastChanged     OBJECT-TYPE 
    SYNTAX      TimeStamp 
    MAX-ACCESS  read-only 
    STATUS      current 
    DESCRIPTION 
        "The value of tmnxOamLTtraceAutoLastChanged indicates the last time
        the value of a writable object in this row was modified."
    ::= { tmnxOamLTtraceAutoConfigEntry 2 } 

tmnxOamLTtraceAutoStorageType OBJECT-TYPE
    SYNTAX      StorageType
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoStorageType specifies the storage type 
         for this conceptual row. Conceptual rows having the value 'permanent' 
         need not allow write-access to any columnar objects in the row."
    DEFVAL { volatile }
    ::= { tmnxOamLTtraceAutoConfigEntry 3 }

tmnxOamLTtraceAutoAdminState OBJECT-TYPE
    SYNTAX     TmnxAdminState
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoAdminState specifies the state based on 
         which the Auto LDP Tree Discovery process starts or stops. 

         When the tmnxOamLTtraceAutoAdminState becomes 'inService', 
         the Auto LDP Tree Discovery process starts, and periodic 
         path-probing on the previously discovered paths, if any, continues.
         When the tmnxOamLTtraceAutoAdminState becomes 'outOfService', 
         the Auto LDP Tree Discovery process and periodic path-probing
         on the discovered paths stop.

         At the starting of the Auto discovery process, it selects the 
         address FECs imported from LDP peers (ALCATEL-IND1-TIMETRA-LDP-MIB::
         vRtrLdpAddrFecTable) provided the FECs pass the configured policies
         (tmnxOamLTtraceAutoPolicy1..tmnxOamLTtraceAutoPolicy5). For all the
         FECs (tmnxOamLTtraceAutoTotalFecs), it automatically 
         creates entries from the tmnxOamLTtraceFecInfoTable. As a part of
         the auto discovery process, it creates entries from the  
         tmnxOamLTtracePathInfoTable for each discovered path."
    DEFVAL { outOfService }
    ::= { tmnxOamLTtraceAutoConfigEntry 4 }    

tmnxOamLTtraceAutoFcName       OBJECT-TYPE
    SYNTAX      TFCName
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoFcName specifies the forwarding class
         of the MPLS Echo request packets.
 
         The forwarding class name must be one of those defined in the
         tFCNameTable in ALCATEL-IND1-TIMETRA-QOS-MIB.  The agent creates predefined
         entries in the tFCNameTable for 'premium', 'assured', and 'be'
         (for best-effort) forwarding classes.  The actual forwarding
         class encoding is controlled by the network egress LSP-EXP
         mappings."  
    DEFVAL { "be" }
    ::= { tmnxOamLTtraceAutoConfigEntry 5 }

tmnxOamLTtraceAutoProfile      OBJECT-TYPE
    SYNTAX      TProfile
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoProfile specifies the profile 
         value to be used with the forwarding class specified in
         tmnxOamLTtraceAutoFcName.

         The profile value must be consistent with the specified forwarding 
         class:
            'assured' = 'in' or 'out'
            'premium' = 'in'
            'be' = 'out'
        "  
     DEFVAL { out }
     ::= { tmnxOamLTtraceAutoConfigEntry 6 }

tmnxOamLTtraceAutoDiscIntvl  OBJECT-TYPE
    SYNTAX      Unsigned32 (60..1440)
    UNITS       "minutes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoDiscIntvl specifies number of 
         minutes to wait before repeating LDP Tree Auto Discovery process."
    DEFVAL { 60 }
    ::= { tmnxOamLTtraceAutoConfigEntry 7 }

tmnxOamLTtraceAutoMaxPath OBJECT-TYPE
    SYNTAX      Unsigned32 (1..128)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoMaxPath specifies the maximum
          number of paths that can be discovered for a selected IP Address 
          FEC (tmnxOamLTtraceAutoTotalFecs)."
    DEFVAL { 128 }
    ::= { tmnxOamLTtraceAutoConfigEntry 8 }

tmnxOamLTtraceAutoTrMaxTtl OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    UNITS       "time-to-live value"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoTrMaxTtl specifies the maximum 
         label time-to-live value for an Lsp trace request during the tree 
         discovery."
    DEFVAL { 30 }
    ::= { tmnxOamLTtraceAutoConfigEntry 9 }

tmnxOamLTtraceAutoTrTimeOut OBJECT-TYPE
    SYNTAX      Unsigned32 (1..60)
    UNITS       "seconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoTrTimeOut specifies the time-out
         value, in seconds, for an lsp trace request during the tree discovery."
    DEFVAL { 30 }
    ::= { tmnxOamLTtraceAutoConfigEntry 10 }

tmnxOamLTtraceAutoTrMaxFailures OBJECT-TYPE
    SYNTAX      Unsigned32 (1..10)
    UNITS       "timeouts"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoTrMaxFailures specifies the maximum 
         number of consecutive timeouts allowed before terminating an lsp trace
         request to a hop."
    DEFVAL { 3 }
    ::= { tmnxOamLTtraceAutoConfigEntry 11 }

tmnxOamLTtraceAutoPolicy1 OBJECT-TYPE
    SYNTAX       TPolicyStatementNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoPolicy1 specifies the first 
         policy used to filter LDP imported Address FECs."
    DEFVAL { ''H }
    ::= { tmnxOamLTtraceAutoConfigEntry 12 }

tmnxOamLTtraceAutoPolicy2 OBJECT-TYPE
    SYNTAX       TPolicyStatementNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoPolicy2 specifies the second 
         policy used to filter LDP imported Address FECs."
    DEFVAL { ''H }
    ::= { tmnxOamLTtraceAutoConfigEntry 13 }

tmnxOamLTtraceAutoPolicy3 OBJECT-TYPE
    SYNTAX       TPolicyStatementNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoPolicy3 specifies the third 
         policy used to filter LDP imported Address FECs."
    DEFVAL { ''H }
    ::= { tmnxOamLTtraceAutoConfigEntry 14 }

tmnxOamLTtraceAutoPolicy4 OBJECT-TYPE
    SYNTAX       TPolicyStatementNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoPolicy4 specifies the fourth 
         policy used to filter LDP imported Address FECs."
    DEFVAL { ''H }
    ::= { tmnxOamLTtraceAutoConfigEntry 15 }

tmnxOamLTtraceAutoPolicy5 OBJECT-TYPE
    SYNTAX       TPolicyStatementNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoPolicy5 specifies the fifth 
         policy used to filter LDP imported Address FECs."
    DEFVAL { ''H }
    ::= { tmnxOamLTtraceAutoConfigEntry 16 }

tmnxOamLTtraceAutoProbeIntvl  OBJECT-TYPE
    SYNTAX      Unsigned32 (1..60)
    UNITS       "minutes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoProbeIntvl specifies the number 
         of minutes to wait before repeating probing (pinging) a discovered
         path."

    DEFVAL { 1 }
    ::= { tmnxOamLTtraceAutoConfigEntry 17 }

tmnxOamLTtraceAutoPrTimeOut OBJECT-TYPE
    SYNTAX      Unsigned32 (1..3)
    UNITS       "minutes"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoPrTimeOut specifies the time-out
         value, in minutes, for a ping request during probing. 
         tmnxOamLTtraceAutoPrTimeOut cannot be greater than  
         tmnxOamLTtraceAutoProbeIntvl."
    DEFVAL { 1 }
    ::= { tmnxOamLTtraceAutoConfigEntry 18 }

tmnxOamLTtraceAutoPrMaxFailures OBJECT-TYPE
    SYNTAX      Unsigned32 (1..10)
    UNITS       "timeouts"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoPrMaxFailures specifies the 
         maximum number of consecutive timeouts allowed before failing a path 
         probe (ping)."
    DEFVAL { 3 }
    ::= { tmnxOamLTtraceAutoConfigEntry 19 }


--
--  Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Auto Status Table
--
tmnxOamLTtraceAutoStatusTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF TmnxOamLTtraceAutoStatusEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtraceAutoStatusTable contains an entry for operational 
         information about each instance of Automatic (background) LDP 
         Tree Trace."
    ::= { tmnxOamTraceRouteObjs 23 }

tmnxOamLTtraceAutoStatusEntry OBJECT-TYPE
    SYNTAX     TmnxOamLTtraceAutoStatusEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtraceAutoStatusEntry represents a row in the 
         tmnxOamLTtraceAutoStatusTable.

         Entries cannot be created or deleted via snmp SET operations.  
         An entry is created whenever a row is created in
         tmnxOamLTtraceAutoConfigTable."
    INDEX      { vRtrID }
    ::= { tmnxOamLTtraceAutoStatusTable 1 }

TmnxOamLTtraceAutoStatusEntry ::= 
    SEQUENCE {
        tmnxOamLTtraceAutoDiscoveryState    INTEGER,
        tmnxOamLTtraceAutoTotalFecs         Unsigned32,
        tmnxOamLTtraceAutoDisFecs           Unsigned32,
        tmnxOamLTtraceAutoLastDisStart      TimeStamp,
        tmnxOamLTtraceAutoLastDisEnd        TimeStamp,
        tmnxOamLTtraceAutoLastDisDur        Unsigned32
   }

tmnxOamLTtraceAutoDiscoveryState       OBJECT-TYPE
    SYNTAX      INTEGER {
                    initial (0),
                    inProgress (1),
                    done (2),
                    halt (3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoDiscoveryState indicates the current
         state of the discovery process."
    ::= { tmnxOamLTtraceAutoStatusEntry 1 }

tmnxOamLTtraceAutoTotalFecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoTotalFecs indicates the number of 
         Address FECs that have been selected for discovery during the 
         current discovery process.

         An Address FEC imported from an LDP peer 
         (ALCATEL-IND1-TIMETRA-LDP-MIB:: vRtrLdpAddrFecTable)is selected if it passes 
         the associated policies (tmnxOamLTtraceAutoPolicy1..
         tmnxOamLTtraceAutoPolicy5)."
    ::= { tmnxOamLTtraceAutoStatusEntry 2 }

tmnxOamLTtraceAutoDisFecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoDisFecs indicates the number of 
         selected Address FECs that have been discovered successfully 
         during the current discovery process."
    ::= { tmnxOamLTtraceAutoStatusEntry 3 }

tmnxOamLTtraceAutoLastDisStart   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoLastDisStart indicates the 
         sysUpTime when the last Auto discovery process started.  If 
         no discovery process has started, the value will be 0."
    ::= { tmnxOamLTtraceAutoStatusEntry 4 }

tmnxOamLTtraceAutoLastDisEnd   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoLastDisEnd indicates the 
         sysUpTime when the last Auto discovery process ended. Before 
         the first discovery process ends, it would be 0. At the end of a 
         discovery this value is set. This value is not reset during 
         starting/restarting."
    ::= { tmnxOamLTtraceAutoStatusEntry 5 }

tmnxOamLTtraceAutoLastDisDur   OBJECT-TYPE
    SYNTAX      Unsigned32 
    UNITS       "seconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceAutoLastDisDur indicates the time 
         (in seconds) it took to finish the last discovery process.If no test 
         has been finished, the value is 0.  This value is updated only when 
         a discovery ends."
    ::= { tmnxOamLTtraceAutoStatusEntry 6 }

--
--  Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Auto  FEC Info Table
--
tmnxOamLTtraceFecInfoTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF TmnxOamLTtraceFecInfoEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtraceFecInfoTable contains entries for the operational 
         information about the automatically selected FECs."
    ::= { tmnxOamTraceRouteObjs 24 }

tmnxOamLTtraceFecInfoEntry OBJECT-TYPE
    SYNTAX     TmnxOamLTtraceFecInfoEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtraceFecInfoEntry represents a row in the 
         tmnxOamLTtraceFecInfoTable.

         Entries cannot be created and deleted via SNMP SET operations."

    INDEX {
             vRtrID,
             tmnxOamLTtraceFecPrefixType,
             tmnxOamLTtraceFecPrefix,
             tmnxOamLTtraceFecPrefLen
          }
    ::= { tmnxOamLTtraceFecInfoTable 1 }

TmnxOamLTtraceFecInfoEntry ::= 
    SEQUENCE {
        tmnxOamLTtraceFecPrefixType         InetAddressType,
        tmnxOamLTtraceFecPrefix             InetAddress,
        tmnxOamLTtraceFecPrefLen            InetAddressPrefixLength,
        tmnxOamLTtraceFecDiscoveryState     INTEGER,
        tmnxOamLTtraceFecDisStatusBits      TmnxOamLTtraceDisStatusBits,
        tmnxOamLTtraceFecDisPaths           Unsigned32,
        tmnxOamLTtraceFecFailedHops         Unsigned32,
        tmnxOamLTtraceFecLastDisEnd         TimeStamp,
        tmnxOamLTtraceFecFailedProbes       Unsigned32,
        tmnxOamLTtraceFecProbeState         INTEGER
   }

tmnxOamLTtraceFecPrefixType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecPrefixType specifies the type of
         remote IP address stored in tmnxOamLTtraceFecPrefix.  Currently
         only ipv4 type is supported."
    ::= { tmnxOamLTtraceFecInfoEntry 1 }

tmnxOamLTtraceFecPrefix     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecPrefix indicates the Internet address
         prefix for an LDP based LSP associated with the OAM LDP Tree trace
         test."
    ::= { tmnxOamLTtraceFecInfoEntry 2 }

tmnxOamLTtraceFecPrefLen     OBJECT-TYPE
    SYNTAX      InetAddressPrefixLength
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecPrefLen indicates the Internet
         address prefix length for the LDP based LSP associated with the OAM 
         LDP Tree trace test."
    ::= { tmnxOamLTtraceFecInfoEntry 3 }

tmnxOamLTtraceFecDiscoveryState       OBJECT-TYPE
    SYNTAX      INTEGER {
                    initial (0),
                    inProgress (1),
                    done (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecDiscoveryState indicates the current
         state of the discovery process for the Address FEC."
    ::= { tmnxOamLTtraceFecInfoEntry 4 }


tmnxOamLTtraceFecDisStatusBits OBJECT-TYPE
    SYNTAX      TmnxOamLTtraceDisStatusBits 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecDisStatusBits indicates the current
         discovery status bits for the Address FEC."
    ::= { tmnxOamLTtraceFecInfoEntry 5 }

tmnxOamLTtraceFecDisPaths OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecDisPaths indicates the number of 
         discovered ECMP paths for the Address FEC."
    ::= { tmnxOamLTtraceFecInfoEntry 6 }

tmnxOamLTtraceFecFailedHops OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecFailedHops indicates the number of 
         hops from which no successful response was received."
    ::= { tmnxOamLTtraceFecInfoEntry 7 }

tmnxOamLTtraceFecLastDisEnd   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecLastDisEnd indicates the sysUpTime 
         when the FEC was last discovered."
    ::= { tmnxOamLTtraceFecInfoEntry 8 }

tmnxOamLTtraceFecFailedProbes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecFailedProbes indicates the number of 
         discovered paths which are in failed probing state."
    ::= { tmnxOamLTtraceFecInfoEntry 9 }

tmnxOamLTtraceFecProbeState       OBJECT-TYPE
    SYNTAX      INTEGER {
                    oK (0),
                    partiallyFailed (1),
                    failed (2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtraceFecProbeState indicates the current
         overall probing state for the discovered paths of the Address FEC.
         The tmnxOamLTtraceFecProbeState is considered as 'oK' when 
         probing on all the associated discovered paths is OK. 
         It is considered as 'failed' when probing on all the associated 
         discovered paths failed. It is considered as partially failed 
         when probing on one or more, but not all, discovered paths failed."
    ::= { tmnxOamLTtraceFecInfoEntry 10 }

--
--  Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Auto Path Info Table
--
tmnxOamLTtracePathInfoTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF TmnxOamLTtracePathInfoEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtracePathInfoTable contains entries with operational 
         information about the automatically discovered ECMP paths."
    ::= { tmnxOamTraceRouteObjs 25 }

tmnxOamLTtracePathInfoEntry OBJECT-TYPE
    SYNTAX     TmnxOamLTtracePathInfoEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "tmnxOamLTtracePathInfoEntry represents a row in the 
         tmnxOamLTtracePathInfoTable.
         
         Entries cannot be created and deleted via SNMP SET operations."
    INDEX {
             vRtrID,
             tmnxOamLTtraceFecPrefixType,
             tmnxOamLTtraceFecPrefix,
             tmnxOamLTtraceFecPrefLen,
             tmnxOamLTtracePathDstAddrType,
             tmnxOamLTtracePathDstAddr
          }
    ::= { tmnxOamLTtracePathInfoTable 1 }

TmnxOamLTtracePathInfoEntry ::= 
    SEQUENCE {
        tmnxOamLTtracePathDstAddrType       InetAddressType,
        tmnxOamLTtracePathDstAddr           InetAddress,
        tmnxOamLTtracePathRemAddrType       InetAddressType,
        tmnxOamLTtracePathRemoteAddr        InetAddress,
        tmnxOamLTtracePathEgrNhAddrType     InetAddressType,
        tmnxOamLTtracePathEgrNhAddr         InetAddress,
        tmnxOamLTtracePathDisTtl            Unsigned32,
        tmnxOamLTtracePathLastDisTime       TimeStamp,
        tmnxOamLTtracePathLastRc            TmnxOamPingRtnCode,
        tmnxOamLTtracePathProbeState        INTEGER,
        tmnxOamLTtracePathProbeTmOutCnt     Unsigned32
   }
tmnxOamLTtracePathDstAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathDstAddrType indicates the type of
         Internet address stored in tmnxOamLTtracePathDstAddr."
    ::= { tmnxOamLTtracePathInfoEntry 1 }

tmnxOamLTtracePathDstAddr     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathDstAddr indicates the destination 
         IP address (127 Address) of a path for a given LDP based LSP 
         associated with the OAM LDP Tree trace test."
    ::= { tmnxOamLTtracePathInfoEntry 2 }

tmnxOamLTtracePathRemAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathRemAddrType indicates the type of
         Internet address stored in tmnxOamLTtracePathRemoteAddr."
    ::= { tmnxOamLTtracePathInfoEntry 3 }

tmnxOamLTtracePathRemoteAddr     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathRemoteAddr indicates the associated 
         interface IP address or the router ID of the egress router."
    ::= { tmnxOamLTtracePathInfoEntry 4 }

tmnxOamLTtracePathEgrNhAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathEgrNhAddrType indicates the type of
         Internet address stored in tmnxOamLTtracePathEgrNhAddr."
    ::= { tmnxOamLTtracePathInfoEntry 5 }

tmnxOamLTtracePathEgrNhAddr     OBJECT-TYPE
    SYNTAX      InetAddress (SIZE (0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathEgrNhAddr indicates the  
         next hop IP address (wrt the ingress router) used to reach the
         associated ECMP path endpoint."
    ::= { tmnxOamLTtracePathInfoEntry 6 }

tmnxOamLTtracePathDisTtl OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathDisTtl indicates the label 
         time-to-live value used to discover the egress router for 
         the path."
    ::= { tmnxOamLTtracePathInfoEntry 7 }

tmnxOamLTtracePathLastDisTime   OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathLastDisTime indicates the sysUpTime 
         when the path was last discovered."
    ::= { tmnxOamLTtracePathInfoEntry 8 }

tmnxOamLTtracePathLastRc    OBJECT-TYPE
    SYNTAX      TmnxOamPingRtnCode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathLastRc indicates the OAM return code 
         received in the OAM ping response."
    ::= { tmnxOamLTtracePathInfoEntry 9 }

tmnxOamLTtracePathProbeState       OBJECT-TYPE
    SYNTAX      INTEGER {
                    oK (0),
                    failed (1)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathProbeState indicates the current
         probing (ping) state for the discovered ECMP path. 
         The tmnxOamLTtracePathProbeState is considered as 'failed' when 
         number of consecutive timeouts for the ping request reached its 
         maximum allowed limit (tmnxOamLTtraceAutoPrMaxFailures). 
         The tmnxOamLTtracePathProbeState is considered as 'oK' after receiving
         a successful ping response for the associated path."
    ::= { tmnxOamLTtracePathInfoEntry 10 }

tmnxOamLTtracePathProbeTmOutCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamLTtracePathProbeTmOutCnt indicates the number of 
         consecutive timeouts for the associated ping request.
         This tmnxOamLTtracePathProbeTmOutCnt is reset to 0
         after receiving a successful ping response from the path 
         destination."
    ::= { tmnxOamLTtracePathInfoEntry 11 }

--
-- Alcatel 7x50 SR series LDP ECMP OAM (TREE TRACE) Notification 
-- Definition section 
--

tmnxOamLdpTtraceAutoDiscState NOTIFICATION-TYPE
    OBJECTS {
        tmnxOamLTtraceAutoDiscoveryState
    }
    STATUS  current
    DESCRIPTION
        "The tmnxOamLdpTtraceAutoDiscState notification is generated when
         the discovery state of the 'Auto Ldp Tree Trace entity' represented by
         tmnxOamLTtraceAutoDiscoveryState has been changed."
    ::= { tmnxOamTraceRouteNotifications 4 }

tmnxOamLdpTtraceFecProbeState NOTIFICATION-TYPE
    OBJECTS {
        tmnxOamLTtraceFecProbeState,
        tmnxOamLTtraceFecDisPaths,
        tmnxOamLTtraceFecFailedProbes
    }
    STATUS  current
    DESCRIPTION
        "The tmnxOamLdpTtraceFecProbeState notification is generated when
          the probe state of the 'auto discovered FEC' has been changed."
    ::= { tmnxOamTraceRouteNotifications 5 }

tmnxOamLdpTtraceFecDisStatus NOTIFICATION-TYPE
    OBJECTS {
        tmnxOamLTtraceFecDisStatusBits,
        tmnxOamLTtraceFecDisPaths
    }
    STATUS  current
    DESCRIPTION
        "The tmnxOamLdpTtraceFecDisStatus notification is generated when
         the discovery status BITS or the number of discovered paths of the 
        'auto discovered FEC' has been changed.  Note that the changes are 
        evaluated at the end of a FEC discovery."
    ::= { tmnxOamTraceRouteNotifications 6 }

--
--  Alcatel 7xx0 SR series OAM VCCV Trace Control Table
--
--  Sparse Dependent Extension of the tmnxOamTrCtlTable.
--
--  The same indexes are used for both the base table, tmnxOamTrCtlTable, 
--  and the sparse dependent table, tmnxOamVccvTrCtlTable. 
--
--  This in effect extends the tmnxOamTrCtlTable with additional columns.
--  Rows are created in the tmnxOamVccvTrCtlTable only for those entries 
--  in the tmnxOamTrCtlTable where tmnxOamTrCtlTestMode has a value of 
--  'vccvTraceRoute'.
--  
--  Deletion of a row in the tmnxOamTrCtlTable results in the 
--  deletion of the row in the tmnxOamVccvTrCtlTable.
--
tmnxOamVccvTrCtlTable     OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVccvTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION 
        "Defines the Alcatel 7xx0 SR OAM VCCV Trace Control Table for providing, 
         via SNMP, the capability of performing Alcatel 7xx0 SR OAM 
         'vccvTraceRoute' test operations. The results of these tests are stored
         in the tmnxOamTrResultsTable, the tmnxOamTrProbeHistoryTable and the
         tmnxOamVccvTrNextPwSegmentTable."
   ::= { tmnxOamTraceRouteObjs 26 }

tmnxOamVccvTrCtlEntry OBJECT-TYPE
    SYNTAX      TmnxOamVccvTrCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVccvTrCtlTable.  The first index
         element, tmnxOamTrCtlOwnerIndex, is of type SnmpAdminString,
         a textual convention that allows for use of the SNMPv3
         View-Based Access Control Model (RFC 2575 [11], VACM)
         and allows a management application to identify its entries.
         The second index, tmnxOamTrCtlTestIndex, enables the same 
         management application to have multiple outstanding requests."
    INDEX {
             tmnxOamTrCtlOwnerIndex,
             tmnxOamTrCtlTestIndex
          }
    ::= { tmnxOamVccvTrCtlTable 1 }

TmnxOamVccvTrCtlEntry ::=
    SEQUENCE {
        tmnxOamVccvTrCtlSdpIdVcId       SdpBindId,
        tmnxOamVccvTrCtlReplyMode       INTEGER
    }

tmnxOamVccvTrCtlSdpIdVcId    OBJECT-TYPE
    SYNTAX      SdpBindId
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvTrCtlSdpIdVcId specifies the SDP ID and the
        VC ID of the pseudowire to be used for performing a VCC trace route 
        operation.  This parameter is only required if tmnxOamTrCtlTestMode 
        has a value of 'vccvTraceRoute'. tmnxOamVccvTrCtlSdpIdVcId is defined
        using the following format:
             SDP ID: first 4 octets  
             VC ID:  remaining 4 octets 
        If the value of tmnxOamVccvTrCtlSdpIdVcId is invalid, or the pseudowire
        is administratively down, or unavailable, the OAM Trace request
        message probe is not sent and an appropriate error value is
        written to tmnxOamTrProbeHistoryStatus for that probe entry.  Once
        the interval timer expires, the next probe attempt will be made
        if required."
    DEFVAL { '0000000000000000'h }      -- invalid SdpId vc-Id
    ::= { tmnxOamVccvTrCtlEntry 1 }
   
tmnxOamVccvTrCtlReplyMode OBJECT-TYPE
    SYNTAX          INTEGER {
                        ip (2),
                        controlChannel (4)
                    }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     
        "The value of tmnxOamVccvTrCtlReplyMode specifies the method of reply
         due to the vccv-traceRoute request message.
             ip (2)                 out-of-band reply 
             controlChannel (4)     inband reply

         This parameter is optional for vccv-traceRoute."
    REFERENCE   "RFC 4379, Section 7.1"
    DEFVAL { controlChannel }
    ::= { tmnxOamVccvTrCtlEntry 2 }

--
-- Alcatel 7xx0 SR OAM VCCV TraceRoute Route Information Next Hop Table
--
tmnxOamVccvTrNextPwSegmentTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF TmnxOamVccvTrNextPwSegmentEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines a table for storing the results of an OAM 'vccvTraceRoute'
         probe operation where a Vccv next hop list is returned.

         An entry in this table is created when the result of an OAM 
         'vccvTraceRoute' probe is determined. An entry is removed from this 
         table when its corresponding tmnxOamTrCtlEntry is deleted."
   ::= { tmnxOamTraceRouteObjs 27 }

tmnxOamVccvTrNextPwSegmentEntry OBJECT-TYPE
    SYNTAX      TmnxOamVccvTrNextPwSegmentEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the tmnxOamVccvTrNextPwSegmentTable. The first two
         index elements identify the tmnxOamTrCtlEntry that a 
         tmnxOamVccvTrNextPwSegmentEntry belongs to.  The third index element
         identifies an OAM trace route test run.  The fourth, fifth and sixth
         index elements select a single OAM 'vccvTraceRoute' reply."
         INDEX {
            tmnxOamTrCtlOwnerIndex,
            tmnxOamTrCtlTestIndex,
            tmnxOamTrResultsTestRunIndex,
            tmnxOamTrProbeHistoryIndex,
            tmnxOamTrProbeHistoryHopIndex,
            tmnxOamTrProbeHistoryProbeIndex
           }
    ::= { tmnxOamVccvTrNextPwSegmentTable 1 }

 TmnxOamVccvTrNextPwSegmentEntry ::=
    SEQUENCE {
       tmnxOamVccvTrNextPwID            TmnxVcIdOrNone,
       tmnxOamVccvTrNextPwType          SdpBindVcType,
       tmnxOamVccvTrNextSenderAddrType  InetAddressType,
       tmnxOamVccvTrNextSenderAddr      InetAddress,
       tmnxOamVccvTrNextRemoteAddrType  InetAddressType,
       tmnxOamVccvTrNextRemoteAddr      InetAddress
    }

tmnxOamVccvTrNextPwID OBJECT-TYPE
    SYNTAX      TmnxVcIdOrNone
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvTrNextPwID indicates the next-hop pseudo-wire
         in the trace route operation for a specific 'vccvTraceRoute' probe 
         reply. A value of '0' indicates that there are no further next-hops for
         a specific trace route operation."
    ::= { tmnxOamVccvTrNextPwSegmentEntry 1 }

tmnxOamVccvTrNextPwType    OBJECT-TYPE
    SYNTAX      SdpBindVcType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvTrNextPwType indicates the next-hop pseudo-wire
         type in the trace route operation for a specific 'vccvTraceRoute' probe
         reply."
    ::= { tmnxOamVccvTrNextPwSegmentEntry 2 }
    
tmnxOamVccvTrNextSenderAddrType     OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvTrNextSenderAddrType indicates the next-hop
         sender IP address type in the trace route operation for a specific
         'vccvTraceRoute' probe reply."
    ::= { tmnxOamVccvTrNextPwSegmentEntry 3 }

tmnxOamVccvTrNextSenderAddr       OBJECT-TYPE
    SYNTAX      InetAddress   (SIZE(0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvTrNextSenderAddr indicates the next-hop sender
         IP address in the trace route oepration for a specific 'vccvTraceRoute'
         probe reply."
    ::= { tmnxOamVccvTrNextPwSegmentEntry 4 }
 
tmnxOamVccvTrNextRemoteAddrType   OBJECT-TYPE
    SYNTAX      InetAddressType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvTrNextRemoteAddrType indicates the next-hop
         remote IP address type in the trace route operation for a specific
         'vccvTraceRoute' probe reply."
    ::= { tmnxOamVccvTrNextPwSegmentEntry 5 }

tmnxOamVccvTrNextRemoteAddr       OBJECT-TYPE
    SYNTAX      InetAddress   (SIZE(0|4|16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tmnxOamVccvTrNextRemoteAddr indicates the next-hop
         remote IP address in the trace route operation for a specific
         'vccvTraceRoute' probe reply."
    ::= { tmnxOamVccvTrNextPwSegmentEntry 6 }

--
-- Conformance Information
--
tmnxOamPingCompliances  OBJECT IDENTIFIER ::= { tmnxOamPingConformance 1 }
tmnxOamPingGroups       OBJECT IDENTIFIER ::= { tmnxOamPingConformance 2 }

tmnxOamTrCompliances    OBJECT IDENTIFIER ::= { tmnxOamTraceRouteConformance 1 }
tmnxOamTrGroups         OBJECT IDENTIFIER ::= { tmnxOamTraceRouteConformance 2 }

tmnxOamSaaCompliances   OBJECT IDENTIFIER ::= { tmnxOamSaaConformance 1 }
tmnxOamSaaGroups        OBJECT IDENTIFIER ::= { tmnxOamSaaConformance 2 }

-- compliance statements

-- tmnxOamPingCompliance  MODULE-COMPLIANCE
--    ::= { tmnxOamPingCompliances 1 }

-- tmnxOamPingR2r1Compliance  MODULE-COMPLIANCE
--    ::= { tmnxOamPingCompliances 2 }

-- tmnxOamPingV3v0Compliance  MODULE-COMPLIANCE
--    ::= { tmnxOamPingCompliances 3 }

tmnxOamPing7450V4v0Compliance  MODULE-COMPLIANCE
    STATUS obsolete 
    DESCRIPTION
        "The compliance statement for management of OAM ping tests on
         Alcatel 7450 ESS series systems release 4.0."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamPingGeneralV4v0Group,
           --tmnxOamMacPingL2MapGroup,
           tmnxOamMacPingV4v0Group,
           tmnxOamLspPingV4v0Group,
           --tmnxOamVprnPingV4v0Group,
           tmnxOamMfibPingV4v0Group,
           tmnxOamCpePingV4v0Group,
           --tmnxOamMRInfoV4v0Group,
           --tmnxOamAtmPingR2r1Group,
           tmnxOamVccvPingGroup,
           tmnxOamIcmpPingGroup,
           tmnxOamPingNotificationV4v0Group
        }
    ::= { tmnxOamPingCompliances 4 }

tmnxOamPing7750V4v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
        "The compliance statement for management of OAM ping tests on
         Alcatel 7750 SR series systems release 4.0."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamPingGeneralV4v0Group,
           --tmnxOamMacPingL2MapGroup,
           tmnxOamMacPingV4v0Group,
           tmnxOamLspPingV4v0Group,
           tmnxOamVprnPingV4v0Group,
           tmnxOamMfibPingV4v0Group,
           tmnxOamCpePingV4v0Group,
           tmnxOamMRInfoV4v0Group,
           tmnxOamAtmPingR2r1Group,
           tmnxOamVccvPingGroup,
           tmnxOamIcmpPingGroup,
           tmnxOamPingNotificationV4v0Group
        }
    ::= { tmnxOamPingCompliances 5 }

tmnxOamPing7450V5v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
        "The compliance statement for management of OAM ping tests on
         Alcatel 7450 ESS series systems release 5.0."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamPingGeneralV4v0Group,
           --tmnxOamMacPingL2MapGroup,
           tmnxOamMacPingV4v0Group,
           tmnxOamLspPingV5v0Group,
           --tmnxOamVprnPingV4v0Group,
           tmnxOamMfibPingV4v0Group,
           tmnxOamCpePingV4v0Group,
           --tmnxOamMRInfoV4v0Group,
           --tmnxOamAtmPingR2r1Group,
           tmnxOamVccvPingV5v0Group,
           tmnxOamIcmpPingGroup,
           tmnxOamAncpTestV5v0Group,
           tmnxOamPingNotificationV5v0Group
        }
    ::= { tmnxOamPingCompliances 6 }

tmnxOamPing7750V5v0Compliance  MODULE-COMPLIANCE 
    STATUS  obsolete
      DESCRIPTION 
          "The compliance statement for management of OAM ping tests on 
           Alcatel 7750 SR series systems release 5.0." 
      MODULE  -- this module 
          MANDATORY-GROUPS { 
             tmnxOamPingGeneralV4v0Group, 
             --tmnxOamMacPingL2MapGroup, 
             tmnxOamMacPingV4v0Group, 
             tmnxOamLspPingV5v0Group,
             tmnxOamVprnPingV4v0Group, 
             tmnxOamMfibPingV4v0Group, 
             tmnxOamCpePingV4v0Group, 
             tmnxOamMRInfoV4v0Group, 
             tmnxOamAtmPingR2r1Group, 
             tmnxOamVccvPingV5v0Group, 
             tmnxOamIcmpPingGroup, 
             tmnxOamAncpTestV5v0Group,
             tmnxOamPingNotificationV5v0Group 
          } 
      ::= { tmnxOamPingCompliances 7 }

tmnxOamPing7450V6v0Compliance  MODULE-COMPLIANCE
    STATUS current 
    DESCRIPTION
        "The compliance statement for management of OAM ping tests on
         Alcatel 7450 ESS series systems release 6.0."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamPingGeneralV6v0Group,
           --tmnxOamMacPingL2MapGroup,
           tmnxOamMacPingV4v0Group,
           tmnxOamLspPingV5v0Group,
           --tmnxOamVprnPingV4v0Group,
           tmnxOamMfibPingV6v0Group,
           tmnxOamCpePingV4v0Group,
           --tmnxOamMRInfoV4v0Group,
           --tmnxOamAtmPingR2r1Group,
           tmnxOamVccvPingV5v0Group,
           tmnxOamIcmpPingGroup,
           tmnxOamAncpTestV5v0Group,
           tmnxOamPingNotificationV5v0Group
        }
    ::= { tmnxOamPingCompliances 8 }

tmnxOamPing7750V6v0Compliance  MODULE-COMPLIANCE 
      STATUS  current 
      DESCRIPTION 
          "The compliance statement for management of OAM ping tests on 
           Alcatel 7750 SR series systems release 6.0." 
      MODULE  -- this module 
          MANDATORY-GROUPS { 
             tmnxOamPingGeneralV6v0Group, 
             --tmnxOamMacPingL2MapGroup, 
             tmnxOamMacPingV4v0Group, 
             tmnxOamLspPingV5v0Group,
             tmnxOamVprnPingV4v0Group, 
             tmnxOamMfibPingV6v0Group, 
             tmnxOamCpePingV4v0Group, 
             tmnxOamMRInfoV4v0Group, 
             tmnxOamAtmPingR2r1Group, 
             tmnxOamVccvPingV5v0Group, 
             tmnxOamIcmpPingGroup, 
             tmnxOamAncpTestV5v0Group,
             tmnxOamPingNotificationV5v0Group 
          } 
      ::= { tmnxOamPingCompliances 9 }

-- tmnxOamTrCompliance  MODULE-COMPLIANCE
--    ::= { tmnxOamTrCompliances 1 }

-- tmnxOamTrV3v0Compliance  MODULE-COMPLIANCE
--    ::= { tmnxOamTrCompliances 2 }

tmnxOamTr7450V4v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
        "The compliance statement for management of OAM Trace Route 
         tests on Alcatel 7450 ESS series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamTrGeneralV4v0Group,
           tmnxOamTrHopsV4v0Group,
           tmnxOamMacTrV3v0Group,
           tmnxOamLspTrV4v0Group,
           --tmnxOamVprnTrV4v0Group,
           tmnxOamMcastTrV4v0Group,
           tmnxOamTrNotificationV4v0Group
        }
    ::= { tmnxOamTrCompliances 3 }

tmnxOamTr7750V4v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
        "The compliance statement for management of OAM Trace Route 
         tests on Alcatel 7750 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamTrGeneralV4v0Group,
           tmnxOamTrHopsV4v0Group,
           tmnxOamMacTrV3v0Group,
           tmnxOamLspTrV4v0Group,
           tmnxOamVprnTrV4v0Group,
           tmnxOamMcastTrV4v0Group,
           tmnxOamTrNotificationV4v0Group
         }
    ::= { tmnxOamTrCompliances 4 }

tmnxOamTr7450V5v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
        "The compliance statement for management of OAM Trace Route 
         tests on Alcatel 7450 ESS series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamTrGeneralV5v0Group,
           tmnxOamTrHopsV4v0Group,
           tmnxOamMacTrV3v0Group,
           tmnxOamLspTrV5v0Group,
           --tmnxOamVprnTrV4v0Group,
           tmnxOamMcastTrV4v0Group,
           tmnxOamTrNotificationV5v0Group           
        }
    ::= { tmnxOamTrCompliances 5 }

tmnxOamTr7750V5v0Compliance  MODULE-COMPLIANCE
    STATUS  obsolete
    DESCRIPTION
        "The compliance statement for management of OAM Trace Route 
         tests on Alcatel 7750 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamTrGeneralV5v0Group,
           tmnxOamTrHopsV4v0Group,
           tmnxOamMacTrV3v0Group,
           tmnxOamLspTrV5v0Group,
           tmnxOamVprnTrV4v0Group,
           tmnxOamMcastTrV4v0Group,
           tmnxOamTrNotificationV5v0Group           
         }
    ::= { tmnxOamTrCompliances 6 }

tmnxOamTr7450V6v0Compliance  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for management of OAM Trace Route 
         tests on Alcatel 7450 ESS series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamTrGeneralV5v0Group,
           tmnxOamTrHopsV4v0Group,
           tmnxOamMacTrV3v0Group,
           tmnxOamLspTrV5v0Group,
           --tmnxOamVprnTrV4v0Group,
           tmnxOamMcastTrV4v0Group,
           tmnxOamVccvTrV6v0Group,
           tmnxOamTrNotificationV5v0Group
        }
    ::= { tmnxOamTrCompliances 7 }

tmnxOamTr77x0V6v0Compliance  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for management of OAM Trace Route 
         tests on Alcatel 7710/7750 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamTrGeneralV5v0Group,
           tmnxOamTrHopsV4v0Group,
           tmnxOamMacTrV3v0Group,
           tmnxOamLspTrV5v0Group,
           tmnxOamVprnTrV6v0Group,
           tmnxOamMcastTrV4v0Group,
           tmnxOamVccvTrV6v0Group,
           tmnxOamTrNotificationV5v0Group
         }
    ::= { tmnxOamTrCompliances 8 }

tmnxOamSaaV3v0Compliance  MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "The compliance statement for management of OAM SAA 
         tests on Alcatel 7x50 SR series systems."
    MODULE  -- this module
        MANDATORY-GROUPS { 
           tmnxOamSaaGeneralV3v0Group,
           tmnxOamSaaThresholdV3v0Group,
           tmnxOamSaaNotificationV3v0Group
        }
    ::= { tmnxOamSaaCompliances 1 }


-- units of conformance

--
--  OAM Ping Groups
--

-- tmnxOamPingGeneralGroup   OBJECT-GROUP
--    ::= { tmnxOamPingGroups 1 }

-- tmnxOamMacPingGroup     OBJECT-GROUP
--    ::= { tmnxOamPingGroups 2 }

tmnxOamMacPingL2MapGroup     OBJECT-GROUP
    OBJECTS {  tmnxOamMacPingL2MapRouterID,
               tmnxOamMacPingL2MapLabel,
               tmnxOamMacPingL2MapProtocol,
               tmnxOamMacPingL2MapVCType,        
               tmnxOamMacPingL2MapVCID,
               tmnxOamMacPingL2MapDirection
            }
    STATUS      current
    DESCRIPTION
        "The group of optional objects to report OAM Mac Ping layer-2
         mapping information returned in response to OAM Mac Ping 
         tests on Alcatel 7x50 SR series systems."
    ::= { tmnxOamPingGroups 3 }

-- tmnxOamLspPingGroup     OBJECT-GROUP
--    ::= { tmnxOamPingGroups 4 }

-- tmnxOamVprnPingGroup     OBJECT-GROUP
--    ::= { tmnxOamPingGroups 5 }

-- tmnxOamPingNotifyObjsGroup   OBJECT-GROUP
--    ::= { tmnxOamPingGroups 6 }

-- tmnxOamPingNotificationGroup NOTIFICATION-GROUP
--    ::= { tmnxOamPingGroups 7 }

tmnxOamAtmPingR2r1Group OBJECT-GROUP
    OBJECTS {  tmnxOamAtmPingCtlPortId,
               tmnxOamAtmPingCtlVpi,
               tmnxOamAtmPingCtlVci,
               tmnxOamAtmPingCtlLpbkLocation,
               tmnxOamAtmPingCtlSegment         
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM ATM Ping tests
         on Alcatel 7x50 SR series systems."
    ::= { tmnxOamPingGroups 8}

-- tmnxOamMRInfoR2r1Group OBJECT-GROUP
--    ::= { tmnxOamPingGroups 9}

-- tmnxOamMfibPingV3v0Group    OBJECT-GROUP
--    ::= { tmnxOamPingGroups 10 }

-- tmnxOamCpePingV3v0Group     OBJECT-GROUP
--    ::= { tmnxOamPingGroups 11 }

-- tmnxOamPingGeneralV3v0Group   OBJECT-GROUP
--    ::= { tmnxOamPingGroups 12 }

-- tmnxOamMacPingV3v0Group     OBJECT-GROUP
--    ::= { tmnxOamPingGroups 13 }

tmnxOamMacPingV4v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamMacPingCtlTargetMacAddr,
               tmnxOamMacPingCtlSourceMacAddr,
               tmnxOamMacPingCtlSendControl,
               tmnxOamMacPingCtlReplyControl,
               tmnxOamMacPingCtlTtl,
               tmnxOamMacPingCtlRegister,
               tmnxOamMacPingCtlFlood,
               tmnxOamMacPingCtlForce,
               tmnxOamMacPingCtlAge,
               tmnxOamMacPingCtlSapPortId,
               tmnxOamMacPingCtlSapEncapValue,
               tmnxOamMacPingCtlFibEntryName,
               tmnxOamMacPingHistoryResponse,
               tmnxOamMacPingHistoryOneWayTime,
               tmnxOamMacPingHistoryStatus,
               tmnxOamMacPingHistoryTime,
               tmnxOamMacPingHistoryReturnCode,       
               tmnxOamMacPingHistoryAddressType,
               tmnxOamMacPingHistorySapId,
               tmnxOamMacPingHistorySdpId,
               tmnxOamMacPingHistoryAdminStatus,
               tmnxOamMacPingHistoryOperStatus,
               tmnxOamMacPingHistoryResponsePlane,
               tmnxOamMacPingHistorySize,
               tmnxOamMacPingHistoryInOneWayTime,
               tmnxOamMacPingHistorySrcAddrType,
               tmnxOamMacPingHistorySrcAddress
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM Mac Ping tests 
         on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamPingGroups 14 }

tmnxOamVccvPingGroup OBJECT-GROUP
    OBJECTS {  
               tmnxOamVccvPingCtlSdpIdVcId,
               tmnxOamVccvPingCtlReplyMode
            }
    STATUS     obsolete 
    DESCRIPTION
        "The group of objects supporting management of OAM VCCV Ping tests
         on Alcatel 7x50 SR series systems."
    ::= { tmnxOamPingGroups 15 }

tmnxOamPingGeneralV4v0Group   OBJECT-GROUP
    OBJECTS {  tmnxOamPingMaxConcurrentTests,
               tmnxOamPingCtlRowStatus,
               tmnxOamPingCtlStorageType,
               tmnxOamPingCtlDescr,
               tmnxOamPingCtlTestMode,
               tmnxOamPingCtlAdminStatus,
               tmnxOamPingCtlOrigSdpId,
               tmnxOamPingCtlRespSdpId,
               tmnxOamPingCtlFcName,
               tmnxOamPingCtlProfile,
               tmnxOamPingCtlMtuStartSize,
               tmnxOamPingCtlMtuEndSize,
               tmnxOamPingCtlMtuStepSize,       
               tmnxOamPingCtlServiceId,
               tmnxOamPingCtlLocalSdp,
               tmnxOamPingCtlRemoteSdp,
               tmnxOamPingCtlSize,
               tmnxOamPingCtlTimeOut,
               tmnxOamPingCtlProbeCount,
               tmnxOamPingCtlInterval,
               tmnxOamPingCtlMaxRows,
               tmnxOamPingCtlTrapGeneration,
               tmnxOamPingCtlTrapProbeFailureFilter,
               tmnxOamPingCtlTrapTestFailureFilter,
               tmnxOamPingCtlSAA,
               tmnxOamPingCtlRuns,
               tmnxOamPingCtlFailures,
               tmnxOamPingCtlLastRunResult,
               tmnxOamPingCtlLastChanged,
               tmnxOamPingCtlVRtrID,
               tmnxOamPingCtlTgtAddrType,
               tmnxOamPingCtlTgtAddress,
               tmnxOamPingCtlSrcAddrType,
               tmnxOamPingCtlSrcAddress,
               tmnxOamPingCtlDnsName,
               tmnxOamPingResultsOperStatus,
               tmnxOamPingResultsMinRtt,
               tmnxOamPingResultsMaxRtt,
               tmnxOamPingResultsAverageRtt,
               tmnxOamPingResultsRttSumOfSquares,
               tmnxOamPingResultsMtuResponseSize,
               tmnxOamPingResultsSvcPing,
               tmnxOamPingResultsProbeResponses,
               tmnxOamPingResultsSentProbes,
               tmnxOamPingResultsLastGoodProbe,
               tmnxOamPingResultsLastRespHeader,
               tmnxOamPingResultsMinTt,
               tmnxOamPingResultsMaxTt,
               tmnxOamPingResultsAverageTt,
               tmnxOamPingResultsTtSumOfSquares,
               tmnxOamPingResultsMinInTt,
               tmnxOamPingResultsMaxInTt,
               tmnxOamPingResultsAverageInTt,
               tmnxOamPingResultsInTtSumOfSqrs,
               tmnxOamPingResultsOutJitter,
               tmnxOamPingResultsInJitter,
               tmnxOamPingResultsRtJitter,
               tmnxOamPingResultsProbeTimeouts,
               tmnxOamPingResultsProbeFailures,
               tmnxOamPingHistoryResponse,
               tmnxOamPingHistoryOneWayTime,
               tmnxOamPingHistorySize,
               tmnxOamPingHistoryStatus,
               tmnxOamPingHistoryTime,
               tmnxOamPingHistoryReturnCode,
               tmnxOamPingHistAddressType,
               tmnxOamPingHistoryVersion,
               tmnxOamPingHistSapId,
               tmnxOamPingHistoryCpeMacAddr, 
               tmnxOamPingHistoryRespSvcId,  
               tmnxOamPingHistorySequence,
               tmnxOamPingHistoryIfIndex,
               tmnxOamPingHistoryDataLen,
               tmnxOamPingHistoryRespPlane,
               tmnxOamPingHistoryReqHdr,
               tmnxOamPingHistoryRespHdr,
               tmnxOamPingHistoryDnsAddrType,
               tmnxOamPingHistoryDnsAddress,
               tmnxOamPingHistorySrcAddrType,
               tmnxOamPingHistorySrcAddress,
               tmnxOamPingHistoryInOneWayTime
            }
    STATUS        obsolete
    DESCRIPTION
        "The group of objects supporting management of OAM ping tests general 
         capabilities on Alcatel 7x50 SR series systems 4.0 release"
    ::= { tmnxOamPingGroups 16 }

tmnxOamLspPingV4v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamLspPingCtlVRtrID,
               tmnxOamLspPingCtlLspName,
               tmnxOamLspPingCtlReturnLsp,
               tmnxOamLspPingCtlTtl,
               tmnxOamLspPingCtlPathName,
               tmnxOamLspPingCtlLdpPrefixType,
               tmnxOamLspPingCtlLdpPrefix,
               tmnxOamLspPingCtlLdpPrefixLen
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of OAM LSP Ping tests 
         on Alcatel 7x50 SR series systems 4.0 release."
    ::= { tmnxOamPingGroups 17 }

tmnxOamVprnPingV4v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamVprnPingCtlReplyControl,
               tmnxOamVprnPingCtlTtl,
               tmnxOamVprnPingCtlSrcAddrType,
               tmnxOamVprnPingCtlSrcAddress
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM VPRN Ping tests 
         on Alcatel 7x50 SR series systems 4.0 release."
    ::= { tmnxOamPingGroups 19 }

tmnxOamMfibPingV4v0Group    OBJECT-GROUP
    OBJECTS {  tmnxOamMfibPingCtlReplyControl,
               tmnxOamMfibPingCtlTtl,
               tmnxOamMfibPingCtlSrcAddrType,
               tmnxOamMfibPingCtlSrcAddress,
               tmnxOamMfibPingCtlDestAddrType,
               tmnxOamMfibPingCtlDestAddress,
               tmnxOamPingHistoryRespSvcId
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of OAM MFIB Ping tests
         on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamPingGroups 20 }

tmnxOamCpePingV4v0Group     OBJECT-GROUP
    OBJECTS { tmnxOamCpePingCtlSendControl,
              tmnxOamCpePingCtlReplyControl,
              tmnxOamCpePingCtlTtl,
              tmnxOamCpePingCtlSrceMacAddr,
              tmnxOamCpePingCtlSrcAddrType,
              tmnxOamCpePingCtlSrcAddress,
              tmnxOamPingHistoryCpeMacAddr
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM CPE Ping tests
         on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamPingGroups 21 }

tmnxOamMRInfoV4v0Group OBJECT-GROUP
    OBJECTS {   
               tmnxOamMRInfoRespCapabilities,
               tmnxOamMRInfoRespMinorVersion,
               tmnxOamMRInfoRespMajorVersion,
               tmnxOamMRInfoRespNumInterfaces,
               tmnxOamMRInfoRespIfMetric,
               tmnxOamMRInfoRespIfThreshold,
               tmnxOamMRInfoRespIfFlags,
               tmnxOamMRInfoRespIfNbrCount,
               tmnxOamMRInfoRespIfAddrType,
               tmnxOamMRInfoRespIfAddr,
               tmnxOamMRInfoRespIfNbrAddrType,
               tmnxOamMRInfoRespIfNbrAddr
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM Multicast Router
         Information(mrinfo) tests on Alcatel 7x50 SR series systems
         release 4.0."
    ::= { tmnxOamPingGroups 22 }

tmnxOamIcmpPingGroup OBJECT-GROUP
    OBJECTS {   
                tmnxOamIcmpPingCtlRapid,
                tmnxOamIcmpPingCtlTtl,
                tmnxOamIcmpPingCtlDSField,
                tmnxOamIcmpPingCtlPattern,
                tmnxOamIcmpPingCtlNhAddrType,
                tmnxOamIcmpPingCtlNhAddress,
                tmnxOamIcmpPingCtlEgrIfIndex,
                tmnxOamIcmpPingCtlBypassRouting,
                tmnxOamIcmpPingCtlDoNotFragment
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM ICMP Ping
         tests on Alcatel 7x50 SR series systems."
    ::= { tmnxOamPingGroups 23 }

tmnxOamPingObsoleteV4v0Group OBJECT-GROUP
    OBJECTS {
                tmnxOamPingCtlTargetIpAddress,
                tmnxOamPingHistorySrcIpAddress,
                tmnxOamMacPingHistorySrcIpAddress,
                tmnxOamLspPingCtlLdpIpPrefix,
                tmnxOamLspPingCtlLdpIpPrefixLen,
                tmnxOamVprnPingCtlSourceIpAddr,
                tmnxOamMfibPingCtlSourceIpAddr,
                tmnxOamMfibPingCtlDestIpAddr,
                tmnxOamCpePingCtlSourceIpAddr,
                tmnxOamMRInfoRespIfAddress,
                tmnxOamMRInfoRespIfNbrAddress
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM ICMP Ping
         tests on Alcatel 7x50 SR series systems that were made
         obsolete in release 4.0."
    ::= { tmnxOamPingGroups 24 }

tmnxOamPingNotificationV4v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {  tmnxOamPingProbeFailedV2,
                       tmnxOamPingTestFailedV2,
                       tmnxOamPingTestCompletedV2
                    }
    STATUS      obsolete
    DESCRIPTION
        "The group of notifications supporting the OAM ping feature
         on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamPingGroups 25 }

tmnxOamPingNotificationObsoleteV4v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {  tmnxOamPingProbeFailed,
                       tmnxOamPingTestFailed,
                       tmnxOamPingTestCompleted
                    }
    STATUS      current
    DESCRIPTION
        "The group of notifications supporting the OAM ping feature
         on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamPingGroups 26 }

tmnxOamLspPingV5v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamLspPingCtlVRtrID,
               tmnxOamLspPingCtlLspName,
               tmnxOamLspPingCtlReturnLsp,
               tmnxOamLspPingCtlTtl,
               tmnxOamLspPingCtlPathName,
               tmnxOamLspPingCtlLdpPrefixType,
               tmnxOamLspPingCtlLdpPrefix,
               tmnxOamLspPingCtlLdpPrefixLen,
               tmnxOamLspPingCtlPathDestType,
               tmnxOamLspPingCtlPathDest,
               tmnxOamLspPingCtlNhIntfName,
               tmnxOamLspPingCtlNhAddressType,
               tmnxOamLspPingCtlNhAddress
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM LSP Ping tests 
         on Alcatel 7x50 SR series systems 5.0 release."
    ::= { tmnxOamPingGroups 27 }

tmnxOamVccvPingV5v0Group OBJECT-GROUP
    OBJECTS {  
               tmnxOamVccvPingCtlSdpIdVcId,
               tmnxOamVccvPingCtlReplyMode,
               tmnxOamVccvPingCtlPwId,
               tmnxOamVccvPingCtlTtl
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM VCCV Ping tests
         on Alcatel 7x50 SR series systems 5.0 release."
    ::= { tmnxOamPingGroups 28 }

tmnxOamAncpTestV5v0Group OBJECT-GROUP
    OBJECTS { 
               tmnxOamAncpTestTarget,
               tmnxOamAncpTestTargetId,
               tmnxOamAncpTestcount,
               tmnxOamAncpTestTimeout,
               tmnxOamAncpHistoryAncpString,
               tmnxOamAncpHistoryAccNodeResult,
               tmnxOamAncpHistoryAccNodeCode,
               tmnxOamAncpHistoryAccNodeRspStr,
               tmnxOamVccvPingCtlTtl
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM ANCP tests
         on Alcatel 7x50 SR series systems 5.0 release."
    ::= { tmnxOamPingGroups 29 }

tmnxOamPingNotificationV5v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {  tmnxOamPingProbeFailedV2,
                       tmnxOamPingTestFailedV2,
                       tmnxOamPingTestCompletedV2,
                       tmnxAncpLoopbackTestCompleted
                    }
    STATUS      current
    DESCRIPTION
        "The group of notifications supporting the OAM ping feature
         on Alcatel 7x50 SR series systems release 5.0."
    ::= { tmnxOamPingGroups 30 }

tmnxOamMfibPingV6v0Group    OBJECT-GROUP
    OBJECTS {  tmnxOamMfibPingCtlReplyControl,
               tmnxOamMfibPingCtlTtl,
               tmnxOamMfibPingCtlSrcAddrType,
               tmnxOamMfibPingCtlSrcAddress,
               tmnxOamMfibPingCtlDestAddrType,
               tmnxOamMfibPingCtlDestAddress,
               tmnxOamPingHistoryRespSvcId,
               tmnxOamMfibPingCtlDestMacAddr
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM MFIB Ping tests
         on Alcatel 7x50 SR series systems release 6.0."
    ::= { tmnxOamPingGroups 31 }

tmnxOamPingGeneralV6v0Group   OBJECT-GROUP
    OBJECTS {  tmnxOamPingMaxConcurrentTests,
               tmnxOamPingCtlRowStatus,
               tmnxOamPingCtlStorageType,
               tmnxOamPingCtlDescr,
               tmnxOamPingCtlTestMode,
               tmnxOamPingCtlAdminStatus,
               tmnxOamPingCtlOrigSdpId,
               tmnxOamPingCtlRespSdpId,
               tmnxOamPingCtlFcName,
               tmnxOamPingCtlProfile,
               tmnxOamPingCtlMtuStartSize,
               tmnxOamPingCtlMtuEndSize,
               tmnxOamPingCtlMtuStepSize,       
               tmnxOamPingCtlServiceId,
               tmnxOamPingCtlLocalSdp,
               tmnxOamPingCtlRemoteSdp,
               tmnxOamPingCtlSize,
               tmnxOamPingCtlTimeOut,
               tmnxOamPingCtlProbeCount,
               tmnxOamPingCtlInterval,
               tmnxOamPingCtlMaxRows,
               tmnxOamPingCtlTrapGeneration,
               tmnxOamPingCtlTrapProbeFailureFilter,
               tmnxOamPingCtlTrapTestFailureFilter,
               tmnxOamPingCtlSAA,
               tmnxOamPingCtlRuns,
               tmnxOamPingCtlFailures,
               tmnxOamPingCtlLastRunResult,
               tmnxOamPingCtlLastChanged,
               tmnxOamPingCtlVRtrID,
               tmnxOamPingCtlTgtAddrType,
               tmnxOamPingCtlTgtAddress,
               tmnxOamPingCtlSrcAddrType,
               tmnxOamPingCtlSrcAddress,
               tmnxOamPingCtlDnsName,
               tmnxOamPingResultsOperStatus,
               tmnxOamPingResultsMinRtt,
               tmnxOamPingResultsMaxRtt,
               tmnxOamPingResultsAverageRtt,
               tmnxOamPingResultsRttSumOfSquares,
               tmnxOamPingResultsMtuResponseSize,
               tmnxOamPingResultsSvcPing,
               tmnxOamPingResultsProbeResponses,
               tmnxOamPingResultsSentProbes,
               tmnxOamPingResultsLastGoodProbe,
               tmnxOamPingResultsLastRespHeader,
               tmnxOamPingResultsMinTt,
               tmnxOamPingResultsMaxTt,
               tmnxOamPingResultsAverageTt,
               tmnxOamPingResultsTtSumOfSquares,
               tmnxOamPingResultsMinInTt,
               tmnxOamPingResultsMaxInTt,
               tmnxOamPingResultsAverageInTt,
               tmnxOamPingResultsInTtSumOfSqrs,
               tmnxOamPingResultsOutJitter,
               tmnxOamPingResultsInJitter,
               tmnxOamPingResultsRtJitter,
               tmnxOamPingResultsProbeTimeouts,
               tmnxOamPingResultsProbeFailures,
               tmnxOamPingHistoryResponse,
               tmnxOamPingHistoryOneWayTime,
               tmnxOamPingHistorySize,
               tmnxOamPingHistoryStatus,
               tmnxOamPingHistoryTime,
               tmnxOamPingHistoryReturnCode,
               tmnxOamPingHistAddressType,
               tmnxOamPingHistoryVersion,
               tmnxOamPingHistSapId,
               tmnxOamPingHistoryCpeMacAddr, 
               tmnxOamPingHistoryRespSvcId,  
               tmnxOamPingHistorySequence,
               tmnxOamPingHistoryIfIndex,
               tmnxOamPingHistoryDataLen,
               tmnxOamPingHistoryRespPlane,
               tmnxOamPingHistoryReqHdr,
               tmnxOamPingHistoryRespHdr,
               tmnxOamPingHistoryDnsAddrType,
               tmnxOamPingHistoryDnsAddress,
               tmnxOamPingHistorySrcAddrType,
               tmnxOamPingHistorySrcAddress,
               tmnxOamPingHistoryInOneWayTime,
               tmnxOamPingCtlDNSRecord
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of OAM ping tests general 
         capabilities on Alcatel 7x50 SR series systems 6.0 release"
    ::= { tmnxOamPingGroups 32 }
-- 
--      OAM Trace Route Groups
--

-- tmnxOamTrGeneralGroup   OBJECT-GROUP
--    ::= { tmnxOamTrGroups 1 }

-- tmnxOamTrHopsGroup     OBJECT-GROUP
--    ::= { tmnxOamTrGroups 2 }

-- tmnxOamMacTrGroup     OBJECT-GROUP
--    ::= { tmnxOamTrGroups 3 }

-- tmnxOamLspTrGroup     OBJECT-GROUP
--    ::= { tmnxOamTrGroups 4 }

-- tmnxOamVprnTrGroup     OBJECT-GROUP
--    ::= { tmnxOamTrGroups 5 }

--tmnxOamTrNotifyObjsGroup   OBJECT-GROUP
--    ::= { tmnxOamTrGroups 5 }

-- tmnxOamTrNotificationGroup NOTIFICATION-GROUP
--    ::= { tmnxOamTrGroups 6 }
   
-- tmnxOamMcastTrGroup     OBJECT-GROUP
--    ::= { tmnxOamTrGroups 7 }

-- tmnxOamTrGeneralV3v0Group   OBJECT-GROUP
--    ::= { tmnxOamTrGroups 8 }

-- tmnxOamTrHopsV3v0Group     OBJECT-GROUP
--    ::= { tmnxOamTrGroups 9 }

tmnxOamMacTrV3v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamMacTrCtlTargetMacAddr,
               tmnxOamMacTrCtlSourceMacAddr,
               tmnxOamMacTrCtlSendControl,
               tmnxOamMacTrCtlReplyControl,
               tmnxOamMacTrL2MapRouterID,
               tmnxOamMacTrL2MapLabel,
               tmnxOamMacTrL2MapProtocol,
               tmnxOamMacTrL2MapVCType,        
               tmnxOamMacTrL2MapVCID,
               tmnxOamMacTrL2MapDirection,
               tmnxOamMacTrL2MapSdpId,
               tmnxOamMacTrL2MapSapName
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of OAM MAC Trace Route
         tests on Alcatel 7x50 SR series systems."
    ::= { tmnxOamTrGroups 10 }

tmnxOamTrObsoleteV3v0Group  OBJECT-GROUP
    OBJECTS {  tmnxOamTrResultsTestAttempts,
               tmnxOamTrResultsTestSuccesses
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM Trace Route
         tests that became obsolete in the 3.0 release."
    ::= { tmnxOamTrGroups 11 }

tmnxOamTrGeneralV4v0Group   OBJECT-GROUP
    OBJECTS {  tmnxOamTrMaxConcurrentRequests,
               tmnxOamTrCtlRowStatus,
               tmnxOamTrCtlStorageType,
               tmnxOamTrCtlDescr,
               tmnxOamTrCtlTestMode,
               tmnxOamTrCtlAdminStatus,
               tmnxOamTrCtlFcName,
               tmnxOamTrCtlProfile,
               tmnxOamTrCtlServiceId,
               tmnxOamTrCtlDataSize,
               tmnxOamTrCtlTimeOut,
               tmnxOamTrCtlProbesPerHop,
               tmnxOamTrCtlMaxTtl,
               tmnxOamTrCtlInitialTtl,
               tmnxOamTrCtlDSField,
               tmnxOamTrCtlMaxFailures,
               tmnxOamTrCtlInterval,
               tmnxOamTrCtlMaxRows,
               tmnxOamTrCtlTrapGeneration,
               tmnxOamTrCtlCreateHopsEntries,
               tmnxOamTrCtlSAA,
               tmnxOamTrCtlRuns,
               tmnxOamTrCtlFailures,
               tmnxOamTrCtlLastRunResult,
               tmnxOamTrCtlLastChanged,
               tmnxOamTrCtlVRtrID,
               tmnxOamTrCtlTgtAddrType,
               tmnxOamTrCtlTgtAddress,
               tmnxOamTrCtlSrcAddrType,
               tmnxOamTrCtlSrcAddress,
               tmnxOamTrCtlWaitMilliSec,
               tmnxOamTrResultsOperStatus,
               tmnxOamTrResultsCurHopCount,
               tmnxOamTrResultsCurProbeCount,
               tmnxOamTrResultsLastGoodPath,
               tmnxOamTrResultsTgtAddrType,
               tmnxOamTrResultsTgtAddress,
               tmnxOamTrProbeHistoryResponse,
               tmnxOamTrProbeHistoryOneWayTime,
               tmnxOamTrProbeHistoryStatus,
               tmnxOamTrProbeHistoryLastRC,
               tmnxOamTrProbeHistoryTime,
               tmnxOamTrProbeHistoryResponsePlane,
               tmnxOamTrProbeHistoryAddressType,
               tmnxOamTrProbeHistorySapId,
               tmnxOamTrProbeHistoryVersion,
               tmnxOamTrProbeHistoryRouterID,
               tmnxOamTrProbeHistoryIfIndex,
               tmnxOamTrProbeHistoryDataLen,
               tmnxOamTrProbeHistorySize,
               tmnxOamTrProbeHistoryInOneWayTime,
               tmnxOamTrProbeHistoryAddrType,
               tmnxOamTrProbeHistoryAddress
            }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of OAM Trace Route test 
         general capabilities on Alcatel 7x50 SR series systems 4.0R1 release."
    ::= { tmnxOamTrGroups 12 }

tmnxOamTrHopsV4v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamTrHopsMinRtt,
               tmnxOamTrHopsMaxRtt,
               tmnxOamTrHopsAverageRtt,
               tmnxOamTrHopsRttSumOfSquares,
               tmnxOamTrHopsMinTt,
               tmnxOamTrHopsMaxTt,
               tmnxOamTrHopsAverageTt,
               tmnxOamTrHopsTtSumOfSquares,
               tmnxOamTrHopsSentProbes,
               tmnxOamTrHopsProbeResponses,
               tmnxOamTrHopsLastGoodProbe,
               tmnxOamTrHopsMinInTt,
               tmnxOamTrHopsMaxInTt,
               tmnxOamTrHopsAverageInTt,
               tmnxOamTrHopsInTtSumOfSqrs,
               tmnxOamTrHopsOutJitter,
               tmnxOamTrHopsInJitter,
               tmnxOamTrHopsRtJitter,
               tmnxOamTrHopsProbeTimeouts,
               tmnxOamTrHopsProbeFailures,
               tmnxOamTrHopsTgtAddrType,
               tmnxOamTrHopsTgtAddress
            }
    STATUS      current
    DESCRIPTION
        "The group of optional objects to report OAM Trace Route Hops
         returned in response to OAM Trace Route tests on Alcatel 
         7x50 SR series systems 4.0R1."
    ::= { tmnxOamTrGroups 13 }

tmnxOamLspTrV4v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamLspTrCtlVRtrID,
               tmnxOamLspTrCtlLspName,
               tmnxOamLspTrCtlPathName,
               tmnxOamLspTrCtlLdpPrefixType,
               tmnxOamLspTrCtlLdpPrefix,
               tmnxOamLspTrCtlLdpPrefixLen,
               tmnxOamLspTrMapAddrType,
               tmnxOamLspTrMapDSIPv4Addr,
               tmnxOamLspTrMapDSIfAddr,
               tmnxOamLspTrMapMTU,
               tmnxOamLspTrMapDSIndex,
               tmnxOamLspTrDSLabelLabel,
               tmnxOamLspTrDSLabelProtocol
            }
    STATUS       obsolete 
    DESCRIPTION
        "The group of objects supporting management of OAM LSP Trace Route
         tests on Alcatel 7x50 SR series systems 4.0 release."
    ::= { tmnxOamTrGroups 14 }

tmnxOamVprnTrV4v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamVprnTrCtlReplyControl,
               tmnxOamVprnTrCtlSrcAddrType,
               tmnxOamVprnTrCtlSrcAddress,
               tmnxOamVprnTrL3MapRouterID,
               tmnxOamVprnTrL3MapRteVprnLabel,
               tmnxOamVprnTrL3MapRteMetrics,
               tmnxOamVprnTrL3MapRteLastUp,
               tmnxOamVprnTrL3MapRteOwner,
               tmnxOamVprnTrL3MapRtePref,
               tmnxOamVprnTrL3MapRteDist,
               tmnxOamVprnTrL3MapNumNextHops,
               tmnxOamVprnTrL3MapNumRteTargets,
               tmnxOamVprnTrL3MapDestAddrType,
               tmnxOamVprnTrL3MapDestAddress,
               tmnxOamVprnTrL3MapDestMaskLen,
               tmnxOamVprnTrNextHopRtrID,
               tmnxOamVprnTrNextHopType,
               tmnxOamVprnTrNextHopTunnelID,
               tmnxOamVprnTrNextHopTunnelType,
               tmnxOamVprnTrNextHopIfIndex,        
               tmnxOamVprnTrRouteTarget
            }
    STATUS        obsolete 
    DESCRIPTION
        "The group of objects supporting management of OAM VPRN Trace Route
         tests on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamTrGroups 15 }

tmnxOamMcastTrV4v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamMcastTrCtlVRtrID,
               tmnxOamMcastTrCtlHops,
               tmnxOamMcastTrQueryId,
               tmnxOamMcastTrCtlSrcAddrType,
               tmnxOamMcastTrCtlSrcAddress,
               tmnxOamMcastTrCtlDestAddrType,
               tmnxOamMcastTrCtlDestAddress,
               tmnxOamMcastTrCtlRespAddrType,
               tmnxOamMcastTrCtlRespAddress,
               tmnxOamMcastTrCtlGrpAddrType,
               tmnxOamMcastTrCtlGrpAddress,
               tmnxOamMcastTrRespQueryArrivalTime,
               tmnxOamMcastTrRespInPktCount,
               tmnxOamMcastTrRespOutPktCount,
               tmnxOamMcastTrRespSGPktCount,
               tmnxOamMcastTrRespRtgProtocol,
               tmnxOamMcastTrRespFwdTtl,
               tmnxOamMcastTrRespMBZBit,        
               tmnxOamMcastTrRespSrcBit,
               tmnxOamMcastTrRespSrcMask,
               tmnxOamMcastTrRespFwdCode,
               tmnxOamMcastTrRespInIfAddrType,
               tmnxOamMcastTrRespInIfAddress,
               tmnxOamMcastTrRespOutIfAddrType,
               tmnxOamMcastTrRespOutIfAddress,
               tmnxOamMcastTrRespPhRtrAddrType,
               tmnxOamMcastTrRespPhRtrAddress
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of OAM Multicast Trace Route
         tests on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamTrGroups 16 }

tmnxOamTrObsoleteV4v0Group OBJECT-GROUP
    OBJECTS {
                tmnxOamTrCtlTargetIpAddress,
                tmnxOamTrResultsIpTgtAddr,
                tmnxOamTrProbeHistoryIpAddr,
                tmnxOamTrHopsIpTgtAddress,
                tmnxOamLspTrCtlLdpIpPrefix,
                tmnxOamLspTrCtlLdpIpPrefixLen,
                tmnxOamVprnTrCtlSourceIpAddr,
                tmnxOamVprnTrL3MapRteDestAddr,
                tmnxOamVprnTrL3MapRteDestMask,
                tmnxOamMcastTrCtlSrcIpAddr,
                tmnxOamMcastTrCtlDestIpAddr,
                tmnxOamMcastTrCtlRespIpAddr,
                tmnxOamMcastTrCtlGrpIpAddr,
                tmnxOamMcastTrRespPrevHopRtrAddr,
                tmnxOamMcastTrRespInIfAddr,
                tmnxOamMcastTrRespOutIfAddr
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM ICMP Ping
         tests on Alcatel 7x50 SR series systems that were made
         obsolete in release 4.0."
    ::= { tmnxOamTrGroups 17 }

tmnxOamTrNotificationV4v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {  tmnxOamTrPathChange,
                       tmnxOamTrTestFailed,
                       tmnxOamTrTestCompleted
                    }
    STATUS        obsolete
    DESCRIPTION
        "The group of notifications supporting the OAM Trace Route test
         feature on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxOamTrGroups 18 }

tmnxOamLspTrV5v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamLspTrCtlVRtrID,
               tmnxOamLspTrCtlLspName,
               tmnxOamLspTrCtlPathName,
               tmnxOamLspTrCtlLdpPrefixType,
               tmnxOamLspTrCtlLdpPrefix,
               tmnxOamLspTrCtlLdpPrefixLen,               
               tmnxOamLspTrCtlPathDestType,
               tmnxOamLspTrCtlPathDest,
               tmnxOamLspTrCtlNhIntfName,
               tmnxOamLspTrCtlNhAddressType,
               tmnxOamLspTrCtlNhAddress,
               tmnxOamLspTrMapAddrType,
               tmnxOamLspTrMapDSIPv4Addr,
               tmnxOamLspTrMapDSIfAddr,
               tmnxOamLspTrMapMTU,
               tmnxOamLspTrDSLabelLabel,
               tmnxOamLspTrDSLabelProtocol
            }
    STATUS       current 
    DESCRIPTION
        "The group of objects supporting management of OAM LSP Trace Route
         tests on Alcatel 7x50 SR series systems 5.0 release."
    ::= { tmnxOamTrGroups 19 }
    
tmnxOamTrObsoleteV5v0Group OBJECT-GROUP
    OBJECTS {
              tmnxOamLspTrMapDSIndex
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM Trace
         tests on Alcatel 7x50 SR series systems that were made
         obsolete in release 5.0."
    ::={ tmnxOamTrGroups 20 }

tmnxOamTrGeneralV5v0Group   OBJECT-GROUP
    OBJECTS {  tmnxOamTrMaxConcurrentRequests,
               tmnxOamTrCtlRowStatus,
               tmnxOamTrCtlStorageType,
               tmnxOamTrCtlDescr,
               tmnxOamTrCtlTestMode,
               tmnxOamTrCtlAdminStatus,
               tmnxOamTrCtlFcName,
               tmnxOamTrCtlProfile,
               tmnxOamTrCtlServiceId,
               tmnxOamTrCtlDataSize,
               tmnxOamTrCtlTimeOut,
               tmnxOamTrCtlProbesPerHop,
               tmnxOamTrCtlMaxTtl,
               tmnxOamTrCtlInitialTtl,
               tmnxOamTrCtlDSField,
               tmnxOamTrCtlMaxFailures,
               tmnxOamTrCtlInterval,
               tmnxOamTrCtlMaxRows,
               tmnxOamTrCtlTrapGeneration,
               tmnxOamTrCtlCreateHopsEntries,
               tmnxOamTrCtlSAA,
               tmnxOamTrCtlRuns,
               tmnxOamTrCtlFailures,
               tmnxOamTrCtlLastRunResult,
               tmnxOamTrCtlLastChanged,
               tmnxOamTrCtlVRtrID,
               tmnxOamTrCtlTgtAddrType,
               tmnxOamTrCtlTgtAddress,
               tmnxOamTrCtlSrcAddrType,
               tmnxOamTrCtlSrcAddress,
               tmnxOamTrCtlWaitMilliSec,
               tmnxOamTrResultsOperStatus,
               tmnxOamTrResultsCurHopCount,
               tmnxOamTrResultsCurProbeCount,
               tmnxOamTrResultsLastGoodPath,
               tmnxOamTrResultsTgtAddrType,
               tmnxOamTrResultsTgtAddress,
               tmnxOamTrProbeHistoryResponse,
               tmnxOamTrProbeHistoryOneWayTime,
               tmnxOamTrProbeHistoryStatus,
               tmnxOamTrProbeHistoryLastRC,
               tmnxOamTrProbeHistoryTime,
               tmnxOamTrProbeHistoryResponsePlane,
               tmnxOamTrProbeHistoryAddressType,
               tmnxOamTrProbeHistorySapId,
               tmnxOamTrProbeHistoryVersion,
               tmnxOamTrProbeHistoryRouterID,
               tmnxOamTrProbeHistoryIfIndex,
               tmnxOamTrProbeHistoryDataLen,
               tmnxOamTrProbeHistorySize,
               tmnxOamTrProbeHistoryInOneWayTime,
               tmnxOamTrProbeHistoryAddrType,
               tmnxOamTrProbeHistoryAddress,
               tmnxOamLTtraceFecDiscoveryState,
               tmnxOamLTtraceFecDisStatusBits,
               tmnxOamLTtraceFecDisPaths,
               tmnxOamLTtraceFecFailedHops,
               tmnxOamLTtraceFecLastDisEnd,
               tmnxOamLTtraceFecFailedProbes,
               tmnxOamLTtraceFecProbeState,   
               tmnxOamLTtracePathRemAddrType,
               tmnxOamLTtracePathRemoteAddr,
               tmnxOamLTtracePathEgrNhAddrType,
               tmnxOamLTtracePathEgrNhAddr,
               tmnxOamLTtracePathDisTtl,
               tmnxOamLTtracePathLastDisTime,
               tmnxOamLTtracePathLastRc,
               tmnxOamLTtraceCtlLdpPrefixType,
               tmnxOamLTtraceCtlLdpPrefix,
               tmnxOamLTtraceCtlLdpPrefixLen,
               tmnxOamLTtraceCtlMaxPath,  
               tmnxOamLTtraceResultsDisPaths,
               tmnxOamLTtraceResultsFailedHops,
               tmnxOamLTtraceResultsDisState,
               tmnxOamLTtraceResultsDisStatus,
               tmnxOamLTtraceUpStreamHopIndex,     
               tmnxOamLTtraceHopAddrType,
               tmnxOamLTtraceHopAddr,
               tmnxOamLTtraceHopDstAddrType,
               tmnxOamLTtraceHopDstAddr,
               tmnxOamLTtraceHopEgrNhAddrType,
               tmnxOamLTtraceHopEgrNhAddr,
               tmnxOamLTtraceHopDisTtl,
               tmnxOamLTtraceHopLastRc,
               tmnxOamLTtraceHopDiscoveryState,
               tmnxOamLTtraceHopDiscoveryTime, 
               tmnxOamLTtraceAutoRowStatus,
               tmnxOamLTtraceAutoLastChanged,
               tmnxOamLTtraceAutoStorageType,
               tmnxOamLTtraceAutoAdminState,
               tmnxOamLTtraceAutoFcName,
               tmnxOamLTtraceAutoProfile,
               tmnxOamLTtraceAutoDiscIntvl,
               tmnxOamLTtraceAutoMaxPath,
               tmnxOamLTtraceAutoTrMaxTtl,
               tmnxOamLTtraceAutoTrTimeOut,
               tmnxOamLTtraceAutoTrMaxFailures,
               tmnxOamLTtraceAutoPolicy1,
               tmnxOamLTtraceAutoPolicy2,
               tmnxOamLTtraceAutoPolicy3,
               tmnxOamLTtraceAutoPolicy4,
               tmnxOamLTtraceAutoPolicy5,
               tmnxOamLTtraceAutoProbeIntvl,
               tmnxOamLTtraceAutoPrTimeOut,
               tmnxOamLTtraceAutoPrMaxFailures,
               tmnxOamLTtraceAutoDiscoveryState,
               tmnxOamLTtraceAutoTotalFecs,
               tmnxOamLTtraceAutoDisFecs,
               tmnxOamLTtraceAutoLastDisStart,
               tmnxOamLTtraceAutoLastDisEnd,
               tmnxOamLTtraceAutoLastDisDur,
               tmnxOamLTtracePathProbeState,
               tmnxOamLTtracePathProbeTmOutCnt,
               tmnxOamLTtraceMaxConRequests
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM Trace Route test 
         general capabilities on Alcatel 7x50 SR series systems 5.0 release."
    ::= { tmnxOamTrGroups 21 }

tmnxOamTrNotificationV5v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {  tmnxOamTrPathChange,
                       tmnxOamTrTestFailed,
                       tmnxOamTrTestCompleted,
                       tmnxOamLdpTtraceAutoDiscState,
                       tmnxOamLdpTtraceFecProbeState,
                       tmnxOamLdpTtraceFecDisStatus
                    }
    STATUS        current
    DESCRIPTION
        "The group of notifications supporting the OAM Trace Route test
         feature on Alcatel 7x50 SR series systems release 5.0."
    ::= { tmnxOamTrGroups 22 }   

tmnxOamVccvTrV6v0Group    OBJECT-GROUP
    OBJECTS {   tmnxOamVccvTrCtlSdpIdVcId,
                tmnxOamVccvTrCtlReplyMode,
                tmnxOamVccvTrNextPwID,
                tmnxOamVccvTrNextPwType,
                tmnxOamVccvTrNextSenderAddrType,
                tmnxOamVccvTrNextSenderAddr,
                tmnxOamVccvTrNextRemoteAddrType,
                tmnxOamVccvTrNextRemoteAddr      
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM VCCV Trace Route
         test general capabilities on Alcatel 7xx0 SR series systems 6.0 
        release."
    ::= { tmnxOamTrGroups 23 }

tmnxOamVprnTrObsoleteV6v0Group OBJECT-GROUP
    OBJECTS {
              tmnxOamVprnTrNextHopRtrID 
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM VPRN Trace Route
         tests on Alcatel 7x50 SR series systems that were made
         obsolete in release 6.0."
    ::={ tmnxOamTrGroups 24 }

tmnxOamVprnTrV6v0Group     OBJECT-GROUP
    OBJECTS {  tmnxOamVprnTrCtlReplyControl,
               tmnxOamVprnTrCtlSrcAddrType,
               tmnxOamVprnTrCtlSrcAddress,
               tmnxOamVprnTrL3MapRouterID,
               tmnxOamVprnTrL3MapRteVprnLabel,
               tmnxOamVprnTrL3MapRteMetrics,
               tmnxOamVprnTrL3MapRteLastUp,
               tmnxOamVprnTrL3MapRteOwner,
               tmnxOamVprnTrL3MapRtePref,
               tmnxOamVprnTrL3MapRteDist,
               tmnxOamVprnTrL3MapNumNextHops,
               tmnxOamVprnTrL3MapNumRteTargets,
               tmnxOamVprnTrL3MapDestAddrType,
               tmnxOamVprnTrL3MapDestAddress,
               tmnxOamVprnTrL3MapDestMaskLen,
               tmnxOamVprnTrNextHopType,
               tmnxOamVprnTrNextHopTunnelID,
               tmnxOamVprnTrNextHopTunnelType,
               tmnxOamVprnTrNextHopIfIndex,        
               tmnxOamVprnTrNextHopAddrType,
               tmnxOamVprnTrNextHopAddress,
               tmnxOamVprnTrRouteTarget
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of OAM VPRN Trace Route
         tests on Alcatel 7x50 SR series systems release 6.0."
    ::= { tmnxOamTrGroups 25 }

-- 
--      OAM SAA Test Groups
--
tmnxOamSaaGeneralV3v0Group   OBJECT-GROUP
    OBJECTS {   tmnxOamSaaCtlRowStatus,
                tmnxOamSaaCtlStorageType,
                tmnxOamSaaCtlLastChanged,
                tmnxOamSaaCtlAdminStatus,
                tmnxOamSaaCtlTestMode,
                tmnxOamSaaCtlDescr,
                tmnxOamSaaCtlRuns,
                tmnxOamSaaCtlFailures,
                tmnxOamSaaCtlLastRunResult
            }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of OAM SAA test 
         general capabilities on Alcatel 7x50 SR series systems."
    ::= { tmnxOamSaaGroups 1 }

tmnxOamSaaThresholdV3v0Group     OBJECT-GROUP
    OBJECTS {   tmnxOamSaaTRowStatus,
                tmnxOamSaaTLastChanged,
                tmnxOamSaaTThreshold,
                tmnxOamSaaTValue,
                tmnxOamSaaTLastSent,
                tmnxOamSaaTTestMode,
                tmnxOamSaaTTestRunIndex
            }
    STATUS        current
    DESCRIPTION
        "The group of objects supporting management of OAM Trace Route
         SAA tests thresholds on Alcatel 7x50 SR series systems 3.0R1."
    ::= { tmnxOamSaaGroups 2 }

tmnxOamSaaNotificationV3v0Group NOTIFICATION-GROUP
    NOTIFICATIONS   {  tmnxOamSaaThreshold
                    }
    STATUS        current
    DESCRIPTION
        "The group of notifications supporting the OAM Trace Route test
         feature on Alcatel 7x50 SR series systems."
    ::= { tmnxOamSaaGroups 3 }

END        
