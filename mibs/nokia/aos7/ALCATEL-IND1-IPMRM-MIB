ALCATEL-IND1-IPMRM-MIB DEFINITIONS ::= BEGIN

IMPORTS
	MODULE-IDENTITY, OBJECT-TYPE,
	Integer32
		FROM SNMPv2-SMI
    MODULE-COMPLIANCE, OBJECT-GROUP
    	FROM SNMPv2-CONF
	routingIND1Ipmrm
		FROM ALCATEL-IND1-BASE;

alcatelIND1IPMRMMIB MODULE-IDENTITY

    LAST-UPDATED  "201412040000Z"
    ORGANIZATION  "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:

                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America

        Telephone:               North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507

        Electronic Mail:         <EMAIL>
        World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
        File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):

             This proprietary MIB contains management information for 
             the configuration of IPMRM (IP Multicast Route Manager)
             global configuration parameters.

         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.

         No liability shall be assumed for any incidental, indirect, special, or
         consequential damages whatsoever arising from or related to this
         document or the information contained herein.

         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.

                     Copyright (C) 1995-2007 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "201204030000Z"
    DESCRIPTION
        "The latest version of this MIB Module."
    REVISION      "201412040000Z"
    DESCRIPTION
        "Add failover holddown object.."

    ::= { routingIND1Ipmrm 1 }

alcatelIND1IPMRMMIBObjects OBJECT IDENTIFIER ::= { alcatelIND1IPMRMMIB 1 }

alaIpmrmGlobalConfig OBJECT IDENTIFIER ::= { alcatelIND1IPMRMMIBObjects 1 }

-- ************************************************************************
--  IPMRM Global Configuration
-- ************************************************************************

alaIpmrmMbrStatus     OBJECT-TYPE
    SYNTAX     INTEGER {
                   enable(1),
                   disable(2)
               }
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
               "Administratively enables/disables the Multicast
                Border Router (MBR) functionality on this router."
    DEFVAL     { disable }
    ::= {alaIpmrmGlobalConfig 1}

alaIpmrmMbrProtocolApps  OBJECT-TYPE
    SYNTAX     BITS {
                   dvmrp(0),
                   pim(1)
               }
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
               "Bit map object to reflect the multicast protocols that
                are currently registered with IPMRM.  Bits 0 - 1 are 
                currently in use, and if set, indicate that the respective 
                application is currently active:
                     bit 0 - DVMRP
                     bit 1 - PIM "
    ::= {alaIpmrmGlobalConfig 2}

alaIpmrmFailoverHolddown    OBJECT-TYPE
    SYNTAX     Integer32 (0..65535)
    UNITS      "seconds"
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
               "This is the period (in seconds) during a failover
                in which the multicast routing tables will be kept in
                holddown (or frozen) to allow for the unicast and multicast 
                routing protocols to converge before updating the 
                forwarding tables.

                A value of 0 indicates that multicast routing will not
                not be frozen during a failover."
    DEFVAL     { 80 }
    ::= { alaIpmrmGlobalConfig 3 }

alaIpmrmExtendedBoundaryStatus     OBJECT-TYPE
     SYNTAX     INTEGER {
                    enable(1),
                    disable(2)
               }
     MAX-ACCESS read-write
     STATUS     current
     DESCRIPTION
                "Administratively enables/disables the Multicast
                 Route Boundary Expand functionality on this router."
     DEFVAL     { disable }
     ::= {alaIpmrmGlobalConfig 4}

-- conformance information

alcatelIND1IPMRMMIBConformance OBJECT IDENTIFIER ::= { alcatelIND1IPMRMMIB 2 }
alcatelIND1IPMRMMIBCompliances OBJECT IDENTIFIER ::= 
                                          { alcatelIND1IPMRMMIBConformance 1 }
alcatelIND1IPMRMMIBGroups      OBJECT IDENTIFIER ::= 
                                          { alcatelIND1IPMRMMIBConformance 2 }


-- compliance statements

alaIpmrmCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers running IP Multicast
             and implementing the ALCATEL-IND1-IPMRM MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { alaIpmrmConfigMIBGroup }

    ::= { alcatelIND1IPMRMMIBCompliances 1 }

-- units of conformance

alaIpmrmConfigMIBGroup OBJECT-GROUP
    OBJECTS { alaIpmrmMbrStatus, alaIpmrmMbrProtocolApps, alaIpmrmFailoverHolddown, alaIpmrmExtendedBoundaryStatus
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of global
            configuration parameters for IP Multicast Routing"
    ::= { alcatelIND1IPMRMMIBGroups 1 }

END
