ALCATEL-IND1-<PERSON><PERSON><PERSON><PERSON><PERSON>OUTETABLE-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE,
    <PERSON>p<PERSON><PERSON><PERSON>, Integer32, Unsigned32
        FROM SNMPv2-SMI
    RowStatus,TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM SNMPv2-CONF
    routingIND1GlobalRouteTable
        FROM ALCATEL-IND1-BASE
    Ipv6Address, Ipv6IfIndex
        FROM IPV6-TC ;


alcatelIND1GRTMIB MODULE-IDENTITY

    LAST-UPDATED  "201402070000Z"
    ORGANIZATION  "Alcatel-Lucent"
    CONTACT-INFO
        "Please consult with Customer Service to ensure the most appropriate
         version of this document is used with the products in question:

                    Alcatel-Lucent, Enterprise Solutions Division
                   (Formerly Alcatel Internetworking, Incorporated)
                           26801 West Agoura Road
                        Agoura Hills, CA  91301-5122
                          United States Of America

        Telephone:               North America  ****** 995 2696
                                 Latin America  ****** 919 9526
                                 Europe         +31 23 556 0100
                                 Asia           +65 394 7933
                                 All Other      ****** 878 4507

        Electronic Mail:         <EMAIL>
        World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
        File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

    DESCRIPTION
        "This module describes an authoritative enterprise-specific Simple
         Network Management Protocol (SNMP) Management Information Base (MIB):

             This proprietary MIB contains management information for
             the configuration of the Global Route Table parameters.

         The right to make changes in specification and other information
         contained in this document without prior notice is reserved.

         No liability shall be assumed for any incidental, indirect, special, or
         consequential damages whatsoever arising from or related to this
         document or the information contained herein.

         Vendors, end-users, and other interested parties are granted
         non-exclusive license to use this specification in connection with
         management of the products for which it is intended to be used.

                     Copyright (C) 1995-2011 Alcatel-Lucent
                         ALL RIGHTS RESERVED WORLDWIDE"

    REVISION      "201402070000Z"
    DESCRIPTION
        "Add IPv6 Global Route Table"

    ::= { routingIND1GlobalRouteTable 1 }

alcatelIND1GRTMIBObjects  OBJECT IDENTIFIER ::= { alcatelIND1GRTMIB 1 }

alaGrtConfig  OBJECT IDENTIFIER ::= { alcatelIND1GRTMIBObjects 1 }


--  Textual Conventions

AlaGrtRouteDistinguisher ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Syntax for a route distinguisher and route target
         as defined in [RFC4346]."
    SYNTAX  OCTET STRING(SIZE (0..256))


--  Global IPv4 Route Table

alaGrtRouteTable OBJECT-TYPE
    SYNTAX   SEQUENCE OF AlaGrtRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The Global IPv4 Routing Table."
    ::= { alaGrtConfig 1 }

alaGrtRouteEntry OBJECT-TYPE
    SYNTAX   AlaGrtRouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "A particular route in the Global Route Table"
    INDEX {
        alaGrtRouteDistinguisher,
        alaGrtRouteDest,
        alaGrtRouteMaskLen,
        alaGrtRouteNextHop
        }
    ::= { alaGrtRouteTable 1 }

AlaGrtRouteEntry ::=
    SEQUENCE {
        alaGrtRouteDistinguisher AlaGrtRouteDistinguisher,
        alaGrtRouteDest          IpAddress,
        alaGrtRouteMaskLen       Unsigned32,
        alaGrtRouteNextHop       IpAddress,
        alaGrtRouteMetric        Unsigned32,
        alaGrtRouteTag           Unsigned32,
        alaGrtRouteVrfName       OCTET STRING,    
        alaGrtRouteIsid          Unsigned32
    }

alaGrtRouteDistinguisher OBJECT-TYPE
    SYNTAX   AlaGrtRouteDistinguisher
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The route distinguisher of a global route."
    ::= { alaGrtRouteEntry 1 }

alaGrtRouteDest OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The destination IP address of this route."
    ::= { alaGrtRouteEntry 2 }

alaGrtRouteMaskLen OBJECT-TYPE
    SYNTAX   Unsigned32
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The destination mask length of this route"
    ::= { alaGrtRouteEntry 3 }

alaGrtRouteNextHop OBJECT-TYPE
    SYNTAX   IpAddress
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The Gateway for this route"
    ::= { alaGrtRouteEntry 4 }

alaGrtRouteMetric OBJECT-TYPE
    SYNTAX   Unsigned32
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The metric for this route"
    ::= { alaGrtRouteEntry 5 }

alaGrtRouteTag OBJECT-TYPE
    SYNTAX   Unsigned32
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The tag for this route"
    ::= { alaGrtRouteEntry 6 }

alaGrtRouteVrfName OBJECT-TYPE
    SYNTAX  OCTET STRING(SIZE (0..20))
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The name of the VRF this route came from"
    ::= { alaGrtRouteEntry 7 }

alaGrtRouteIsid OBJECT-TYPE
    SYNTAX   Unsigned32
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The isid number this route came from"
    ::= { alaGrtRouteEntry 8 }


--  Global IPv6 Route Table

alaGrt6RouteTable OBJECT-TYPE
    SYNTAX   SEQUENCE OF AlaGrt6RouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The IPv6 Global Routing Table."
    ::= { alaGrtConfig 2 }

alaGrt6RouteEntry OBJECT-TYPE
    SYNTAX   AlaGrt6RouteEntry
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "A particular route in the IPv6 Global Route Table"
    INDEX {
        alaGrt6RouteDistinguisher,
        alaGrt6RouteDest,
        alaGrt6RouteMaskLen,
        alaGrt6RouteNextHop,
        alaGrt6RouteIfIndex
        }
    ::= { alaGrt6RouteTable 1 }

AlaGrt6RouteEntry ::=
    SEQUENCE {
        alaGrt6RouteDistinguisher AlaGrtRouteDistinguisher,
        alaGrt6RouteDest          Ipv6Address,
        alaGrt6RouteMaskLen       Unsigned32,
        alaGrt6RouteNextHop       Ipv6Address,
        alaGrt6RouteIfIndex       Ipv6IfIndex,
        alaGrt6RouteMetric        Unsigned32,
        alaGrt6RouteTag           Unsigned32,
        alaGrt6RouteVrfName       OCTET STRING    
    }

alaGrt6RouteDistinguisher OBJECT-TYPE
    SYNTAX   AlaGrtRouteDistinguisher
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The route distinguisher of an IPv6 global route."
    ::= { alaGrt6RouteEntry 1 }

alaGrt6RouteDest OBJECT-TYPE
    SYNTAX   Ipv6Address
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The destination address of this route."
    ::= { alaGrt6RouteEntry 2 }

alaGrt6RouteMaskLen OBJECT-TYPE
    SYNTAX   Unsigned32
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The destination mask length of this route"
    ::= { alaGrt6RouteEntry 3 }

alaGrt6RouteNextHop OBJECT-TYPE
    SYNTAX   Ipv6Address
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
        "The gateway for this route"
    ::= { alaGrt6RouteEntry 4 }

alaGrt6RouteIfIndex OBJECT-TYPE
    SYNTAX   Ipv6IfIndex
    MAX-ACCESS not-accessible
    STATUS   current
    DESCRIPTION
       "The index value which uniquely identifies the local
        interface through which the next hop of this
        route should be reached."
    ::= { alaGrt6RouteEntry 5 }

alaGrt6RouteMetric OBJECT-TYPE
    SYNTAX   Unsigned32
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The metric for this route"
    ::= { alaGrt6RouteEntry 6 }

alaGrt6RouteTag OBJECT-TYPE
    SYNTAX   Unsigned32
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The tag for this route"
    ::= { alaGrt6RouteEntry 7 }

alaGrt6RouteVrfName OBJECT-TYPE
    SYNTAX  OCTET STRING(SIZE (0..20))
    MAX-ACCESS read-only
    STATUS   current
    DESCRIPTION
        "The name of the VRF this route came from"
    ::= { alaGrt6RouteEntry 8 }


-- conformance information

alcatelIND1GRTMIBConformance OBJECT IDENTIFIER ::= { alcatelIND1GRTMIB 2 }
alcatelIND1GRTMIBCompliances OBJECT IDENTIFIER ::=
                                          { alcatelIND1GRTMIBConformance 1 }
alcatelIND1GRTMIBGroups      OBJECT IDENTIFIER ::=
                                          { alcatelIND1GRTMIBConformance 2 }

-- compliance statements

alaGrtCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
            "The compliance statement for routers using the 
            Global Route Manager and implementing the 
            ALCATEL-IND1-GlobalRouteTable MIB."
    MODULE  -- this module
        MANDATORY-GROUPS { alaGrtConfigMIBGroup }

    ::= { alcatelIND1GRTMIBCompliances 1 }

-- units of conformance

alaGrtConfigMIBGroup OBJECT-GROUP
    OBJECTS { alaGrtRouteMetric, alaGrtRouteTag,
              alaGrtRouteVrfName, alaGrtRouteIsid,
              alaGrt6RouteMetric, alaGrt6RouteTag,
              alaGrt6RouteVrfName
            }
    STATUS  current
    DESCRIPTION
            "A collection of objects to support management of the Global
            Route Table configuration parameters."
    ::= { alcatelIND1GRTMIBGroups 1 }


END

