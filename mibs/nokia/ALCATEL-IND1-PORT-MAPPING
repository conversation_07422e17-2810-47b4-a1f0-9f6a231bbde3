ALCATEL-IND1-PORT-MAPPING DEFINITIONS ::= BEGIN

IMPORTS
	OBJECT-TYPE,
	MODULE-IDENTITY,
	OBJECT-IDENTITY                 FROM SNMPv2-SMI
    	RowStatus                   FROM SNMPv2-TC
	InterfaceIndexOrZero            FROM IF-MIB
    	softentIND1PortMapping      FROM ALCATEL-IND1-BASE;

 
	alcatelIND1PortMappingMIB MODULE-IDENTITY
		LAST-UPDATED "200704030000Z"
		ORGANIZATION "Alcatel-Lucent"
		CONTACT-INFO
            "Please consult with Customer Service to ensure the most appropriate
             version of this document is used with the products in question:
         
                        Alcatel-Lucent, Enterprise Solutions Division
                       (Formerly Alcatel Internetworking, Incorporated)
                               26801 West Agoura Road
                            Agoura Hills, CA  91301-5122
                              United States Of America
        
            Telephone:               North America  ****** 995 2696
                                     Latin America  ****** 919 9526
                                     Europe         +31 23 556 0100
                                     Asia           +65 394 7933
                                     All Other      ****** 878 4507
        
            Electronic Mail:         <EMAIL>
            World Wide Web:          http://alcatel-lucent.com/wps/portal/enterprise
            File Transfer Protocol:  ftp://ftp.ind.alcatel.com/pub/products/mibs"

		DESCRIPTION
			"This module describes an authoritative enterprise-specific Simple
             Network Management Protocol (SNMP) Management Information Base (MIB):
         
             For the Birds Of Prey Product Line
		     Port Mapping session control
         
             The right to make changes in specification and other information
             contained in this document without prior notice is reserved.
         
             No liability shall be assumed for any incidental, indirect, special, or
             consequential damages whatsoever arising from or related to this
             document or the information contained herein.
         
             Vendors, end-users, and other interested parties are granted
             non-exclusive license to use this specification in connection with
             management of the products for which it is intended to be used.
         
                         Copyright (C) 1995-2007 Alcatel-Lucent
                             ALL RIGHTS RESERVED WORLDWIDE"

		REVISION      "200704030000Z"
        DESCRIPTION
            "Addressing discrepancies with Alcatel Standard."
     	        ::= { softentIND1PortMapping 1}

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

	alcatelIND1PortMappingMIBObjects OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Branch For Port Mapping
            Subsystem Managed Objects."
            ::= { alcatelIND1PortMappingMIB 1 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

	portMapping  OBJECT IDENTIFIER ::= { alcatelIND1PortMappingMIBObjects 1 }

-- xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

-- xxxxxxxxxxxxxxxxxxx
-- Port Mapping Status Table 
-- xxxxxxxxxxxxxxxxxxx

portMappingSessionTable  OBJECT-TYPE
	    SYNTAX  SEQUENCE OF PMapSessionEntry
	    MAX-ACCESS  not-accessible
	    STATUS  current
	    DESCRIPTION
		"A list of port mapping session instances."
            ::= { portMapping 1 }


pmapSessionEntry  OBJECT-TYPE
	SYNTAX  PMapSessionEntry
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION
		"A port  mapping entry."
	INDEX { pmapSessionNumber }
	    ::= { portMappingSessionTable 1 }


PMapSessionEntry ::= SEQUENCE {
	pmapSessionNumber
		INTEGER,
	pmapSessionStatus
		INTEGER,
	pmapSessionDirection
		INTEGER,
	pmapSessionRowStatus
		RowStatus,
	pmapSessionUnknownUnicastFloodStatus
		INTEGER,
	pmapSessionDynProxyARP
		INTEGER
	}

pmapSessionNumber OBJECT-TYPE
	SYNTAX  INTEGER  (1..2147483647)
	MAX-ACCESS not-accessible
	STATUS current
	DESCRIPTION
		"Identifies a specific port mapping instance."
        DEFVAL  { 1 }
    ::= { pmapSessionEntry 1 }	

pmapSessionStatus  OBJECT-TYPE
	SYNTAX  INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS  read-create
	STATUS  current
	DESCRIPTION
		"Whether port mapping is configured with or without user/network ports.
         the session is disabled by default. The switch administrator has to
         explicity enable a session once configured. Also a session can only
         be enabled if there are 2 or more user/network ports in that session."
        DEFVAL  { 1 }
	::= { pmapSessionEntry 2 }

pmapSessionDirection  OBJECT-TYPE
	SYNTAX  INTEGER {
		uni (1),
		bi (2)
	}
	MAX-ACCESS  read-create
	STATUS  current
	DESCRIPTION
		"The session direction (uni/ bi) for this sessions network ports."
        DEFVAL  { 2 }
	::= { pmapSessionEntry 3 }

pmapSessionRowStatus OBJECT-TYPE
	 	SYNTAX  RowStatus
		MAX-ACCESS  read-create
		STATUS  current
		DESCRIPTION
		"The status of this table entry.  
		         "
    ::= { pmapSessionEntry 4 }	

pmapSessionUnknownUnicastFloodStatus  OBJECT-TYPE
	SYNTAX  INTEGER {
		disable (1),
		enable (2)
	}
	MAX-ACCESS  read-create
	STATUS  current
	DESCRIPTION
		"To enable or disable unknown unicast flooding from network port to user ports for this session."
        DEFVAL  { enable }
	::= { pmapSessionEntry 5 }

pmapSessionDynProxyARP OBJECT-TYPE
  SYNTAX        INTEGER {
                  disable(1),
		  enable(2)
                }
  MAX-ACCESS    read-create
  STATUS        current
  DESCRIPTION   "Enables/disables Dynamic proxy ARP on the port mapping session."
  DEFVAL		{ disable }
  ::= { pmapSessionEntry 6}


-- xxxxxxxxxxxxxxxxxxx
-- Port Mapping Table 
-- xxxxxxxxxxxxxxxxxxx

portMappingTable  OBJECT-TYPE
	    SYNTAX  SEQUENCE OF PMapEntry
	    MAX-ACCESS  not-accessible
	    STATUS  current
	    DESCRIPTION
		"A list of port mapping table instances."
            ::= { portMapping 2 }


pmapEntry  OBJECT-TYPE
	SYNTAX  PMapEntry
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION
		"A port  mapping entry."
	INDEX { pmapSessionNumber, pmapPortIfIndex }
	    ::= { portMappingTable 1 }


PMapEntry ::= SEQUENCE {
	pmapPortIfIndex
		InterfaceIndexOrZero,
	pmapPortType
		INTEGER,
    pmapRowStatus
		RowStatus
	}


pmapPortIfIndex  OBJECT-TYPE
	SYNTAX  InterfaceIndexOrZero
	MAX-ACCESS  not-accessible
	STATUS  current
	DESCRIPTION
		"The physical identification number for this port (user/network port)."
        DEFVAL  { 0 }
	::= { pmapEntry 1 }

pmapPortType  OBJECT-TYPE
	SYNTAX  INTEGER {
		user (1),
		network (2)
    }
	MAX-ACCESS  read-create
	STATUS  current
	DESCRIPTION
		"The logical group (user/network) number for this port instance."
        DEFVAL  { 1 }
	::= { pmapEntry 2 }
 
pmapRowStatus OBJECT-TYPE
	 	SYNTAX  RowStatus
		MAX-ACCESS  read-create
		STATUS  current
		DESCRIPTION
		"The status of this table entry.  
		         "
    ::= { pmapEntry 3 }	

-- --------------------------------------------------------------
-- Port Mapping MIB - Conformance Information
-- --------------------------------------------------------------
pmapConformance	OBJECT IDENTIFIER ::= { alcatelIND1PortMappingMIB 2 }
pmapGroups	OBJECT IDENTIFIER ::= { pmapConformance 1 }
pmapCompliances	OBJECT IDENTIFIER ::= { pmapConformance 2 }
-- --------------------------------------------------------------

-- --------------------------------------------------------------
-- Units of conformance
-- --------------------------------------------------------------
pmapSessionTableGroup OBJECT-GROUP
   OBJECTS {
      pmapSessionStatus,
      pmapSessionDirection,
      pmapSessionRowStatus,
      pmapSessionUnknownUnicastFloodStatus,
      pmapSessionDynProxyARP 
   }
   STATUS      current
   DESCRIPTION
      "Mandatory objects for the Port Mapping Session Table group"
   ::= { pmapGroups 1 }

pmapTableGroup OBJECT-GROUP
   OBJECTS {
      pmapPortType,
      pmapRowStatus
  }
   STATUS      current
   DESCRIPTION
      "Mandatory objects for the Port Mapping Table group"
   ::= { pmapGroups 2 }          
   
-- --------------------------------------------------------------
-- Compliance statements
-- --------------------------------------------------------------
pmapCompliance MODULE-COMPLIANCE
   STATUS      current
   DESCRIPTION
      "The compliance statement for support of Port Mapping."
   MODULE
      MANDATORY-GROUPS {
         pmapSessionTableGroup,
         pmapTableGroup
      }
   ::= { pmapCompliances 1 }
-- --------------------------------------------------------------

	END
