--
-- at-dos.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 285
-- Wednesday, May 07, 2008 at 15:39:48
--

	AT-DOS-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			modules			
				FROM AT-SMI-MIB			
			IpAddress, Counter32, BITS, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TruthValue			
				FROM SNMPv2-TC;
	
	
-- ============================================================================
-- AT-DOS.MIB, Allied Telesis enterprise MIB: Denial of Service defense
-- 
-- Copyright (c) 2008 by Allied Telesis, Inc.
-- All rights reserved.
-- 
-- ============================================================================
		-- *******.*********.*********
		dosDefense MODULE-IDENTITY 
			LAST-UPDATED "200804291125Z"		-- April 29, 2008 at 11:25 GMT
			ORGANIZATION 
				"Allied Telesis, Inc"
			CONTACT-INFO 
				"http://www.alliedtelesis.com"
			DESCRIPTION 
				"The Denial of Service defense MIB for managing
				defenses against denial of service attacks.
				"
			::= { modules 143 }
-- 
-- 
-- -- -----------------------------------
-- -- Global Settings
-- -- -----------------------------------
		
	
	
--
-- Node definitions
--
	
		-- *******.*********.*********.1
		dosDefenseStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enabled(1),
				disabled(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Whether or not the DoS defense module is
				currently enabled"
			::= { dosDefense 1 }

		
		-- *******.*********.*********.2
		dosDefenseDebugMode OBJECT-TYPE
			SYNTAX BITS
				{
				none(0),
				packet(1),
				attack(2),
				packet/attack(3),
				diagnostics(4),
				packet/diagnostics(5),
				attack/diagnostics(6),
				packet/attack/diagnostics(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The debugging options enabled for DoS defense.  Output goes
				to the asynchronous port or telnet session that enabled
				debugging.
				
				The bit 'None(0)' indicates that no debugging is enabled.
				
				The bit 'Attack(1)' indicates that information about the
				start and finish of attacks is displayed.
				
				The bit 'Packet(2)' indicates that a hexadecimal dump of
				the IP header of all suspect packets is displayed.
				
				The bit 'Diagnostics(3)' indicates that additional
				debugging and diagnostic messages may be displayed."
			::= { dosDefense 2 }

		
		-- *******.*********.*********.3
		dosDefenseNumDebugPackets OBJECT-TYPE
			SYNTAX INTEGER { continuous(0) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"When packet debugging is enabled, this is the maximum
				number of packets that will be displayed before debugging
				is automatically disabled.  A value of 0 means no limit
				(i.e. continuous)."
			::= { dosDefense 3 }

		
-- ----------------------------------------------------------
-- The DoS Defense Table
-- 
-- Each row of the table contains the configuration for the
-- defense against one attack type on one port.
-- ----------------------------------------------------------
		-- *******.*********.*********.4
		dosDefenseTable OBJECT-TYPE
			SYNTAX SEQUENCE OF DosDefenseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"A table of configuration and status information for
				each defense configured on a port."
			::= { dosDefense 4 }

		
		-- *******.*********.*********.4.1
		dosDefenseEntry OBJECT-TYPE
			SYNTAX DosDefenseEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The configuration and status of the defense against 
				a single attack type on a single port."
			INDEX { dosDefensePort, dosDefenseAttackType }
			::= { dosDefenseTable 1 }

		
		DosDefenseEntry ::=
			SEQUENCE { 
				dosDefensePort
					INTEGER,
				dosDefenseAttackType
					INTEGER,
				dosDefenseDefenseStatus
					INTEGER,
				dosDefenseThreshold
					INTEGER,
				dosDefenseBlockTime
					INTEGER,
				dosDefenseMirroring
					TruthValue,
				dosDefensePortType
					INTEGER,
				dosDefenseSubnetAddress
					IpAddress,
				dosDefenseSubnetMask
					IpAddress,
				dosDefenseAttackState
					INTEGER,
				dosDefenseAttackCount
					Counter32,
				dosDefenseRemainingBlockTime
					INTEGER
			 }

		-- *******.*********.*********.4.1.1
		dosDefensePort OBJECT-TYPE
			SYNTAX INTEGER (1..1023)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The port index on which the defense is configured."
			::= { dosDefenseEntry 1 }

		
		-- *******.*********.*********.4.1.2
		dosDefenseAttackType OBJECT-TYPE
			SYNTAX INTEGER
				{
				synFlood(1),
				pingOfDeath(2),
				smurf(3),
				ipOptions(4),
				land(5),
				teardrop(6),
				none(7)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of attack this defense protects against."
			::= { dosDefenseEntry 2 }

		
		-- *******.*********.*********.4.1.3
		dosDefenseDefenseStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				enabled(1),
				disabled(2),
				set(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Whether or not this attack is currently enabled
				on this port."
			::= { dosDefenseEntry 3 }

		
		-- *******.*********.*********.4.1.4
		dosDefenseThreshold OBJECT-TYPE
			SYNTAX INTEGER (0..1023)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The threshold, in packets per second, at which an
				attack is deemed to be in progress.
				
				If dosDefenseAttackType is SYNFlood(1), a value of 0 means
				no threshold has been set and the default thresholds apply.
				An attack is suspected when the SYN:ACK ratio exceeds 2:1
				above 20 packets per second, in any one-second interval.
				An attack is in progress when the SYN:ACK ratio exceeds 3:1
				above 20 packets per second, in any one-second interval, or
				an attack is suspected more than once within a 
				dosDefenseBlockTime interval.
				
				If dosDefenseAttackType is Smurf(3), a value of 0 means
				the filter will block all broadcast ICMP requests.
				A threshold greater than 0 will block after that number of
				ICMP requests are received in a 1 second interval."
			::= { dosDefenseEntry 4 }

		
		-- *******.*********.*********.4.1.5
		dosDefenseBlockTime OBJECT-TYPE
			SYNTAX INTEGER (1..65535)
			UNITS "seconds"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time, in seconds, that must elapse after the last 
				malicious packet is seen, before an attack is deemed
				to have finished and the port stops blocking traffic.
				
				If dosDefenseAttackType is SYNFlood(1), it is also
				the maximum time an attack is suspected before it
				returns to a state of no attack."
			::= { dosDefenseEntry 5 }

		
		-- *******.*********.*********.4.1.6
		dosDefenseMirroring OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Whether or not suspect traffic received by this port
				is copied to the pre-configured mirror port."
			::= { dosDefenseEntry 6 }

		
		-- *******.*********.*********.4.1.7
		dosDefensePortType OBJECT-TYPE
			SYNTAX INTEGER
				{
				notApplicable(0),
				client(1),
				gateway(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If dosDefenseAttackType is Land(6), the type of port.
				For other values of dosDefenseAttackType, this object
				returns notapplicable(0).
				
				A device connected to a client(1) port should have an IP
				address in the local subnet, and be the original source or
				ultimate destination of packets transiting the network.
				Incoming packets should have a source address in the local
				subnet.  Outgoing packets should have a destination address
				in the local subnet.
				
				A gateway(2) port is connected directly to a gateway device
				attached to external networks.  Apart from a small number of
				packets from the gateway device itself, all packets arriving
				at the gateway port should be from other subnets.  Incoming
				packets should have a source address not in the local
				subnet. Outgoing packets should have a destination address
				not in the local subnet."
			::= { dosDefenseEntry 7 }

		
		-- *******.*********.*********.4.1.8
		dosDefenseSubnetAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If dosDefenseAttackType is Smurf(3), the subnet address
				is used to determine the local broadcast address.
				
				If dosDefenseAttackType is Land(6), the subnet address
				used to determine which addresses are local or remote.
				
				For other values of dosDefenseAttackType, this object
				returns 0.0.0.0."
			::= { dosDefenseEntry 8 }

		
		-- *******.*********.*********.4.1.9
		dosDefenseSubnetMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"If dosDefenseAttackType is Smurf(3), the subnet mask
				is used to determine the local broadcast address.
				
				If dosDefenseAttackType is Land(6), the subnet mask
				used to determine which addresses are local or remote.
				
				For other values of dosDefenseAttackType, this object
				returns 0.0.0.0."
			::= { dosDefenseEntry 9 }

		
		-- *******.*********.*********.4.1.10
		dosDefenseAttackState OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				suspected(1),
				inProgress(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Whether or not an attack is currently in progress on the
				port.
				
				None(0) means no attack is in progress.
				
				If dosDefenseAttackType is SYNFlood(1), Suspected(1)
				means a SYN Flood attack is suspected. A threshold has
				not been set, and the default threshold of a SYN:ACK
				ratio of 2:1 above 20 packets per second has been
				reached.
				
				If dosDefenseAttackType is PingOfDeath(2), Teardrop(5)
				or Land(6), Suspected means that some suspect packets
				have been received but have not yet been analysed to
				determine if an attack exists.
				
				InProgress(2) means an attack is in progress."
			::= { dosDefenseEntry 10 }

		
		-- *******.*********.*********.4.1.11
		dosDefenseAttackCount OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of attacks (attacked seconds) detected
				on this port."
			::= { dosDefenseEntry 11 }

		
		-- *******.*********.*********.4.1.12
		dosDefenseRemainingBlockTime OBJECT-TYPE
			SYNTAX INTEGER (0..65535)
			UNITS "seconds"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time remaining"
			::= { dosDefenseEntry 12 }

		
-- -------------------------------------------
-- DoS Attack Start and End traps
-- -------------------------------------------
-- 
		-- *******.*********.*********.5
		dosDefenseTraps OBJECT IDENTIFIER::= { dosDefense 5 }

		
		-- *******.*********.*********.5.1
		dosDefenseAttackStart NOTIFICATION-TYPE
			OBJECTS { dosDefensePort, dosDefenseAttackType }
			STATUS current
			DESCRIPTION 
				"Triggered when an attack is detected on a port."
			::= { dosDefenseTraps 1 }

		
		-- *******.*********.*********.5.2
		dosDefenseAttackEnd NOTIFICATION-TYPE
			OBJECTS { dosDefensePort, dosDefenseAttackType }
			STATUS current
			DESCRIPTION 
				"Triggered when an attack is finished on a port.
				
				This occurs after an attack packet has not been
				seen for a complete BlockTime period."
			::= { dosDefenseTraps 2 }

		
	
	END

--
-- at-dos.mib
--
