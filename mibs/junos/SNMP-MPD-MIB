   SNMP-MPD-MIB DEFINITIONS ::= BEGIN

   IMPORTS
       MODULE-COMPLIANCE, OBJECT-GROUP         FROM SNMPv2-CONF
       MODULE-IDENTITY, OBJECT-TYPE,
       snmpModules, Counter32                  FROM SNMPv2-SMI;

   snmpMPDMIB MODULE-IDENTITY
       LAST-UPDATED "9905041636Z"              -- 4 April 1999
       ORGANIZATION "SNMPv3 Working Group"
       CONTACT-INFO "WG-EMail:   <EMAIL>
                     Subscribe:  <EMAIL>
                                 In message body:  subscribe snmpv3

                     Chair:      <PERSON>
                                 TIS Labs at Network Associates
                     postal:     3060 Washington Road
                                 Glenwood, MD 21738
                                 USA
                     EMail:      <EMAIL>
                     phone:      ******-854-6889

                     Co-editor:  <PERSON>, Inc.
                     postal:     3001 Kimberlin Heights Road
                                 Knoxville, TN 37920-9716
                                 USA
                     EMail:      <EMAIL>
                     phone:      ******-573-1434

                     Co-editor   <PERSON> Systems, Inc.
                     postal:     Post Office Box 5005
                                 MailStop: Durham
                                 35 Industrial Way
                                 Rochester, NH 03867-5005
                                 USA
                     EMail:      <EMAIL>
                     phone:      ******-337-7357

                     Co-editor:  <PERSON> Software, Inc.
                     postal:     965 <PERSON>
                                 Sunnyvale, CA 94086
                                 USA
                     EMail:      <EMAIL>
                     phone:      ******-616-3100

                     Co-editor:  Bert Wijnen
                                 IBM T. J. Watson Research
                     postal:     Schagen 33
                                 3461 GL Linschoten
                                 Netherlands
                     EMail:      <EMAIL>
                     phone:      +31 348-432-794

                    "
       DESCRIPTION  "The MIB for Message Processing and Dispatching"
       REVISION     "9905041636Z"              -- 4 April 1999
       DESCRIPTION  "Updated addresses, published as RFC 2572."
       REVISION     "9709300000Z"              -- 30 September 1997
       DESCRIPTION  "Original version, published as RFC 2272."
       ::= { snmpModules 11 }

   -- Administrative assignments ***************************************

   snmpMPDAdmin           OBJECT IDENTIFIER ::= { snmpMPDMIB 1 }
   snmpMPDMIBObjects      OBJECT IDENTIFIER ::= { snmpMPDMIB 2 }
   snmpMPDMIBConformance  OBJECT IDENTIFIER ::= { snmpMPDMIB 3 }

   -- Statistics for SNMP Messages *************************************

   snmpMPDStats           OBJECT IDENTIFIER ::= { snmpMPDMIBObjects 1 }

   snmpUnknownSecurityModels OBJECT-TYPE
       SYNTAX       Counter32
       MAX-ACCESS   read-only
       STATUS       current
       DESCRIPTION "The total number of packets received by the SNMP
                    engine which were dropped because they referenced a
                    securityModel that was not known to or supported by
                    the SNMP engine.
                   "
       ::= { snmpMPDStats 1 }

   snmpInvalidMsgs OBJECT-TYPE
       SYNTAX       Counter32
       MAX-ACCESS   read-only
       STATUS       current
       DESCRIPTION "The total number of packets received by the SNMP
                    engine which were dropped because there were invalid
                    or inconsistent components in the SNMP message.
                   "
       ::= { snmpMPDStats 2 }

   snmpUnknownPDUHandlers OBJECT-TYPE
       SYNTAX       Counter32
       MAX-ACCESS   read-only
       STATUS       current
       DESCRIPTION "The total number of packets received by the SNMP
                    engine which were dropped because the PDU contained
                    in the packet could not be passed to an application
                    responsible for handling the pduType, e.g. no SNMP
                    application had registered for the proper
                    combination of the contextEngineID and the pduType.
                   "
       ::= { snmpMPDStats 3 }

   -- Conformance information ******************************************

   snmpMPDMIBCompliances OBJECT IDENTIFIER ::= {snmpMPDMIBConformance 1}
   snmpMPDMIBGroups      OBJECT IDENTIFIER ::= {snmpMPDMIBConformance 2}

   -- Compliance statements

   snmpMPDCompliance MODULE-COMPLIANCE
       STATUS       current
       DESCRIPTION "The compliance statement for SNMP entities which
                    implement the SNMP-MPD-MIB.
                   "

       MODULE    -- this module
           MANDATORY-GROUPS { snmpMPDGroup }

       ::= { snmpMPDMIBCompliances 1 }

   snmpMPDGroup OBJECT-GROUP
       OBJECTS {
                 snmpUnknownSecurityModels,
                 snmpInvalidMsgs,
                 snmpUnknownPDUHandlers
               }
       STATUS       current
       DESCRIPTION "A collection of objects providing for remote
                    monitoring of the SNMP Message Processing and
                    Dispatching process.
                   "
       ::= { snmpMPDMIBGroups 1 }

   END
