-- *******************************************************************
-- Juniper enterprise specific syslog MIB. 
--
-- Copyright (c) 2002-2004, 2006, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
-- *******************************************************************

JUNIPER-SYSLOG-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-IDENTITY, OBJECT-TYPE, 
    NOTIFICATION-TYPE, Unsigned32
        FROM SNMPv2-SMI
    DisplayString, DateAndTime, TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    jnxMibs, jnxSyslogNotifications 
        FROM JUNIPER-SMI;

jnxSyslog MODULE-IDENTITY
    LAST-UPDATED "200603202153Z" -- Mon Mar 20 21:53:48 2005 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089
             E-mail: <EMAIL>"

    DESCRIPTION
            "This is Juniper Networks implementation of enterprise
             specific MIB for syslogs generated by JUNOS."
    ::= { jnxMibs 35 }


-- Textual Conventions
JnxSyslogSeverity ::= TEXTUAL-CONVENTION
        STATUS	current
        DESCRIPTION
            "The severity of the generated syslog message. The enumeration
             values are equal to the values that syslog uses + 1.
             For example, with syslog, emergency=0."
        SYNTAX	INTEGER {
                    emergency(1),
                    alert(2),
                    critical(3),
                    error(4),
                    warning(5),
                    notice(6),
                    info(7),
                    debug(8)
                }

JnxSyslogFacility  ::= TEXTUAL-CONVENTION
        STATUS	current
        DESCRIPTION
            "The facility of the generated syslog message.The enumeration
             values are equal to the values that syslog uses + 1.
             For example, with syslog, kernel=0."
        SYNTAX	INTEGER {
		    kernel(1), -- kernel messages
		    user(2), -- user-level messages
		    mail(3), -- mail system
		    daemon(4), -- system daemons
		    auth(5), -- authorization messages
		    syslog(6), -- messages generated by syslogd
		    lpr(7), -- line printer subsystem
		    news(8), -- network news subsystem
		    uucp(9), -- UUCP subsystem
		    cron(10), -- clock daemon
		    authPriv(11),-- authorization messages
		    ftp(12),-- ftp daemon
		    ntp(13),-- NTP subsystem
		    security(14),-- security subsystems
                                     --    (firewalling, etc.)
		    console(15),-- /dev/console output
                         -- 16 Reserved for system use
		    local0(17),
		    dfc(18), -- JUNOS Names.
		    local2(19),
		    firewall(20), -- JUNOS Names.
		    pfe(21), -- JUNOS Names.
		    conflict(22), -- JUNOS Names.
		    change(23), -- JUNOS Names.
		    interact(24) -- JUNOS Names.
                }

--
-- Syslog Notification objects
--

    jnxSyslogNotifyVars OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Notification object definitions."
        ::= { jnxSyslog 1 }

    jnxSyslogTable   OBJECT-TYPE
      SYNTAX      SEQUENCE OF JnxSyslogEntry 
      MAX-ACCESS  not-accessible
      STATUS      current 
      DESCRIPTION
            "A table of syslog messages generated by the device."
      ::= { jnxSyslogNotifyVars 1 }
 
    jnxSyslogEntry    OBJECT-TYPE
      SYNTAX      JnxSyslogEntry 
      MAX-ACCESS  not-accessible
      STATUS      current
      DESCRIPTION 
            "An entry of syslog table."
      INDEX     { jnxSyslogId }
      ::= { jnxSyslogTable 1 }

    JnxSyslogEntry ::=
        SEQUENCE {
          jnxSyslogId             Unsigned32,
          jnxSyslogEventName      DisplayString,
          jnxSyslogTimestamp      DateAndTime,
          jnxSyslogSeverity       JnxSyslogSeverity,
          jnxSyslogFacility       JnxSyslogFacility,
          jnxSyslogProcessId      Unsigned32,
          jnxSyslogProcessName    DisplayString,
          jnxSyslogHostName       DisplayString,
          jnxSyslogMessage        OCTET STRING
        }

    jnxSyslogId OBJECT-TYPE
        SYNTAX      Unsigned32 
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Syslog message identifier. This is also used as
             primary index in jnxSyslogAttrValTable"
        ::= { jnxSyslogEntry 1 }

    jnxSyslogEventName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "An octet string containing syslog event name."
        ::= { jnxSyslogEntry 2 }

    jnxSyslogTimestamp OBJECT-TYPE
        SYNTAX      DateAndTime
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Date and Time of syslog message generation."
        ::= { jnxSyslogEntry 3 }

    jnxSyslogSeverity OBJECT-TYPE
        SYNTAX      JnxSyslogSeverity 
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Identifies the severity of this syslog message."
        ::= { jnxSyslogEntry 4 }

    jnxSyslogFacility OBJECT-TYPE
        SYNTAX      JnxSyslogFacility
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Identified the facility of this syslog message."
        ::= { jnxSyslogEntry 5 }

    jnxSyslogProcessId OBJECT-TYPE
        SYNTAX      Unsigned32 
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Process-Id of the process that generated this syslog message."
        ::= { jnxSyslogEntry 6 }

    jnxSyslogProcessName OBJECT-TYPE
        SYNTAX      DisplayString 
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Name of the process that generated this syslog message."
        ::= { jnxSyslogEntry 7 }

    jnxSyslogHostName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Hostname of host on which this syslog message is generated." 
        ::= { jnxSyslogEntry 8 }

    jnxSyslogMessage OBJECT-TYPE 
        SYNTAX      OCTET STRING 
        MAX-ACCESS  accessible-for-notify
        STATUS      current 
        DESCRIPTION
            "The syslog message string."
        ::= { jnxSyslogEntry 9 }

    jnxSyslogAvTable  OBJECT-TYPE
      SYNTAX      SEQUENCE OF JnxSyslogAvEntry
      MAX-ACCESS  not-accessible
      STATUS      current
      DESCRIPTION
            "A table of attribute value pairs of the syslog 
             messages generated by the device."
      ::= { jnxSyslogNotifyVars 2 }

    jnxSyslogAvEntry   OBJECT-TYPE
      SYNTAX      JnxSyslogAvEntry 
      MAX-ACCESS  not-accessible
      STATUS      current
      DESCRIPTION
            "An entry of attribute value pair."
      INDEX     { jnxSyslogId, jnxSyslogAvIndex }
      ::= { jnxSyslogAvTable 1 }

    JnxSyslogAvEntry::=
        SEQUENCE {
          jnxSyslogAvIndex       Unsigned32,
          jnxSyslogAvAttribute   DisplayString,
          jnxSyslogAvValue       DisplayString
        }

    jnxSyslogAvIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Identifies the sequence number of attribute-value
             pair in the syslog message."
        ::= { jnxSyslogAvEntry 1 }

    jnxSyslogAvAttribute OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Attribute of the syslog message identified by jnxSyslogId."
        ::= { jnxSyslogAvEntry 2 }

    jnxSyslogAvValue OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Value of the attribute identified by jnxSyslogAvAttribute."
        ::= { jnxSyslogAvEntry 3 }

    jnxUserDefinedTrapOID OBJECT-TYPE
        SYNTAX      OBJECT IDENTIFIER
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Value will be a OID to be set and send to NMS using spoof traps."
        ::= { jnxSyslogNotifyVars 3 }

--
-- Syslog Notification definitions
--
    jnxSyslogNotificationPrefix OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "All Syslog notifications are registered under 
             this branch."
        ::= { jnxSyslogNotifications 0 }

    jnxSyslogTrap NOTIFICATION-TYPE
        OBJECTS { jnxSyslogEventName, jnxSyslogTimestamp,
                  jnxSyslogSeverity, jnxSyslogFacility, 
                  jnxSyslogProcessId, jnxSyslogProcessName,
                  jnxSyslogHostName, jnxSyslogMessage
                }
        STATUS  current
        DESCRIPTION
            "Notification of a generated syslog message. Apart from
             the jnxSyslogTrap objects, this notification can include 
	     one or more attribute-value pairs. The attribute-value 
	     pairs shall be identified by objects jnxSyslogAvAttribute 
	     and jnxSyslogAvValue."
        ::= { jnxSyslogNotificationPrefix 1 }

    jnxUserDefinedNotification NOTIFICATION-TYPE
        OBJECTS  { 
                       jnxUserDefinedTrapOID, 
                       jnxSyslogMessage 
               }
        STATUS   current
        DESCRIPTION
            "The SNMP trap that will be used only for sending spoofed
             traps from customer scripts running from JUNOS."
        ::= { jnxSyslogNotificationPrefix 2 } 

END
