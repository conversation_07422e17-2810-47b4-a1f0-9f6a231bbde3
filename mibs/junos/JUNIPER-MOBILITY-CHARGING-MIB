-- JUNIPER-MOBILITY-CHARGING-MIB 
-- Copyright (c) 2011-2013, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-MOBILITY-CHARGING-MIB DEFINITIONS ::= <PERSON><PERSON><PERSON>

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE,
     <PERSON>32,  <PERSON>64, <PERSON><PERSON><PERSON>32, <PERSON>teger32, Unsigned32, IpA<PERSON>ress
        FROM SNMPv2-SMI
    TruthValue
        FROM SNMPv2-TC
    TEXTUAL-CONVENTION, DisplayString, RowStatus, TruthValue
        FROM SNMPv2-TC
    MODULE-COMPLIANCE, OBJECT-GRO<PERSON>, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    jnxMbgGwIndex, jnxMbgGwName
        FROM JUNIPER-MOBILE-GATEWAYS

    jnxMobileGatewayPgwGgsn
        FROM JUNIPER-MBG-SMI;

jnxMbgPgwChargingMib MODULE-<PERSON>EN<PERSON>TY
    LAST-UPDATED        "201006151430Z" -- <PERSON><PERSON> Jun 15 14:30:00 2010 UTC
    ORGANIZATION        "Juniper Networks, Inc."
    CONTACT-INFO
                        "Juniper Technical Assistance Center
                        Juniper Networks, Inc.
                        1133 Innovation Way
                        Sunnyvale, CA 94089
                        E-mail: <EMAIL>"

    DESCRIPTION
         "This is Juniper Networks implementation of Mobility Charging MIB for 
    PGW (Packet Data Networks Gateway ) in 3GPP LTE network  and the 
    Gateway GPRS Support Node (GGSN) in the 3GPP 3G Network."
    -- revision history --
        REVISION "201006151430Z"    -- 15 June, 2010
        DESCRIPTION
                "Initial version."

        REVISION "201110101430Z"    -- 10 Oct, 2011
        DESCRIPTION
                "CGF group and CGF tables index keys has changed to 
                gateway id and profile id. Gateway id and gateway name has 
                added to all the traps."
    
    REVISION "201203161430Z"    -- 16 March, 2012
    DESCRIPTION
                "GGSN/PGW Charging global statistics table has added."
	::= { jnxMobileGatewayPgwGgsn 3 }

jnxMbgPgwCgNotifications      OBJECT IDENTIFIER ::= { jnxMbgPgwChargingMib 0 }
jnxMbgPgwChargingObjects      OBJECT IDENTIFIER ::= { jnxMbgPgwChargingMib 1 }
jnxMbgPgwCgLcStorageStats     OBJECT IDENTIFIER ::= { 
                                              jnxMbgPgwChargingObjects 1 }
jnxMbgPgwCgCgfGroupsStatsTable OBJECT-TYPE
   SYNTAX        SEQUENCE OF JnxMbgPgwCgCgfGrpStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
       "A table listing the stats for all (Charging Gateway Function) CGF 
        Groups configured on the PGW."
   ::= { jnxMbgPgwChargingObjects 2 }

jnxMbgPgwCgNotificationVars   OBJECT IDENTIFIER ::= { 
                                             jnxMbgPgwChargingObjects 3 }
jnxMbgPgwCgCgfStatsTable OBJECT-TYPE
   SYNTAX        SEQUENCE OF JnxMbgPgwCgCgfStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION
         "A table listing the statistics for all CGF configured on the PGW."
   ::= { jnxMbgPgwChargingObjects 4 }


jnxMbgPgwCgLpsStatsTable OBJECT-TYPE
   SYNTAX        SEQUENCE OF  JnxMbgPgwCgLpsStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
       "A table listing the stats for all Local persistent storage stats 
        configured on the PGW."
   ::= { jnxMbgPgwChargingObjects 5 }

jnxMbgPgwCgTspStatsTable OBJECT-TYPE
   SYNTAX        SEQUENCE OF JnxMbgPgwCgTspStatsEntry
   MAX-ACCESS    not-accessible

   STATUS        current
   DESCRIPTION 
       "A table listing the stats for all (Charging Gateway Function) CGF 
        Groups configured on the PGW."
   ::= { jnxMbgPgwChargingObjects 6 }

jnxMbgPgwCgPeerStatsTable OBJECT-TYPE
   SYNTAX        SEQUENCE OF JnxMbgPgwCgPeerStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A table listing the statistics for all CGF configured on the PGW."
   ::= { jnxMbgPgwChargingObjects 7 }

jnxMbgPgwCgGlobalStatsTable OBJECT-TYPE
   SYNTAX        SEQUENCE OF JnxMbgPgwCgGlobalStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A table listing the charging global statistics of the PGW."
   ::= { jnxMbgPgwChargingObjects 8 }

--
-- Local Storage Stats
--
jnxMbgPgwCgFilesOnLcStorage OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only 
   STATUS        obsolete
   DESCRIPTION
         "The number of Files containing Charging Data Records (CDRs) present 
          on the Local Storage Device.Incremented when a file containing CDRs 
          is closed on the Local storage device Decremented when sftp is done 
          and a file is removed from the Local storage device"
   ::= { jnxMbgPgwCgLcStorageStats 1 }

jnxMbgPgwCgLcStorageAvailSpace OBJECT-TYPE
   SYNTAX        Counter64 
   UNITS         "MBytes"
   MAX-ACCESS    read-only
   STATUS        obsolete
   DESCRIPTION
         "The space available on the Local Storage Device in MB."
   ::= { jnxMbgPgwCgLcStorageStats 2 }

--
-- CG LPS Stats
--

jnxMbgPgwCgLpsStatsEntry OBJECT-TYPE
   SYNTAX        JnxMbgPgwCgLpsStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A conceptual row listing the statistics for each
          LPS configured on the PGW."
   INDEX         { jnxMbgGwIndex }
   ::= { jnxMbgPgwCgLpsStatsTable 1 }

JnxMbgPgwCgLpsStatsEntry ::= SEQUENCE {
   jnxMbgPgwCgLpsFilesOnLcStorage      Gauge32,
   jnxMbgPgwCgLpsStorageAvailSpace     Gauge32
}

jnxMbgPgwCgLpsFilesOnLcStorage OBJECT-TYPE
   SYNTAX        Gauge32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
         "The number of Files containing Charging Data Records (CDRs) present 
          on the Local Storage Device.Incremented when a file containing CDRs 
          is closed on the Local storage device Decremented when sftp is done 
          and a file is removed from the Local storage device"
   ::= { jnxMbgPgwCgLpsStatsEntry 1 }

jnxMbgPgwCgLpsStorageAvailSpace OBJECT-TYPE
   SYNTAX        Gauge32 
   UNITS         "MBytes"
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
         "The space available on the Local Storage Device in MB."
   ::= { jnxMbgPgwCgLpsStatsEntry 2 }

--
-- CGF Group Stats
--
jnxMbgPgwCgCgfGroupStatsEntry OBJECT-TYPE
   SYNTAX        JnxMbgPgwCgCgfGrpStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        obsolete 
   DESCRIPTION 
         "A conceptual row listing the statistics for each
          CGF Server configured on the PGW."
   INDEX         { jnxMbgPgwCgCgfGrpProfName }
   ::= { jnxMbgPgwCgCgfGroupsStatsTable 1 }

JnxMbgPgwCgCgfGrpStatsEntry ::= SEQUENCE {
   jnxMbgPgwCgCgfGrpProfName          DisplayString,
   jnxMbgPgwCgCgfGrpDRTReqTx          Counter32,
   jnxMbgPgwCgCgfGrpDRTReqRx          Counter32,
   jnxMbgPgwCgCgfGrpDRTReqTmout       Counter32,
   jnxMbgPgwCgCgfGrpDRTSucRspRx       Counter32,
   jnxMbgPgwCgCgfGrpDRTErrRspRx       Counter32,
   jnxMbgPgwCgCgfGrpRediReqRx         Counter32,
   jnxMbgPgwCgCgfGrpRediRspTx         Counter32,
   jnxMbgPgwCgCgfGrpSwitchovers       Counter32,
   jnxMbgPgwCgCgfGrpBatchReqTx        Counter32,
   jnxMbgPgwCgCgfGrpBatchRspErrors    Counter32,
   jnxMbgPgwCgCgfGrpBatchCDRsTx       Counter32,
   jnxMbgPgwCgCgfGroupTotalWFA        Counter32
}

jnxMbgPgwCgCgfGrpProfName OBJECT-TYPE
    SYNTAX      DisplayString  (SIZE (0..127)) 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A string that uniquely identifies the CGF group profile."
    ::= { jnxMbgPgwCgCgfGroupStatsEntry 1 }

jnxMbgPgwCgCgfGrpDRTReqTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the DRT (Detailed Record Time) request transmitted 
         for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 2 }

jnxMbgPgwCgCgfGrpDRTReqRx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the DRT request received for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 3 }

jnxMbgPgwCgCgfGrpDRTReqTmout OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the DRT request timeouts happend for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 4 }

jnxMbgPgwCgCgfGrpDRTSucRspRx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   
        " Total number of the DRT success responses received"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 5 }

jnxMbgPgwCgCgfGrpDRTErrRspRx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the DRT error responses received for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 6 }

jnxMbgPgwCgCgfGrpRediReqRx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   
        "Total number of the redirection responses received for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 7 }

jnxMbgPgwCgCgfGrpRediRspTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the redirection responses transmitted for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 8 }

jnxMbgPgwCgCgfGrpSwitchovers OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Total number of the switch overs on the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 9 }

jnxMbgPgwCgCgfGrpBatchReqTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Total number of the batch req transmitted for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 10 }

jnxMbgPgwCgCgfGrpBatchRspErrors OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Tatal number of the batch response errors for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 11 }

jnxMbgPgwCgCgfGrpBatchCDRsTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   
        " Total number of the batch CDRs transmitted for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 12 }

jnxMbgPgwCgCgfGroupTotalWFA OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   
        " Total WFA available for the CGF group"
   ::= { jnxMbgPgwCgCgfGroupStatsEntry 13 }

-- CGF Group Stats
--
jnxMbgPgwCgTspStatsEntry OBJECT-TYPE
   SYNTAX        JnxMbgPgwCgTspStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A conceptual row listing the statistics for each
          CGF Server configured on the PGW."
   INDEX         { jnxMbgGwIndex, jnxMbgPgwCgTspProfId }
   ::= { jnxMbgPgwCgTspStatsTable 1 }

JnxMbgPgwCgTspStatsEntry ::= SEQUENCE {
   jnxMbgPgwCgTspProfId            Unsigned32,
   jnxMbgPgwCgTspDRTReqTx          Counter32,
   jnxMbgPgwCgTspDRTReqTmout       Counter32,
   jnxMbgPgwCgTspDRTSucRspRx       Counter32,
   jnxMbgPgwCgTspDRTErrRspRx       Counter32,
   jnxMbgPgwCgTspRediReqRx         Counter32,
   jnxMbgPgwCgTspRediRspTx         Counter32,
   jnxMbgPgwCgTspSwitchovers       Counter32,
   jnxMbgPgwCgTspBatchReqTx        Counter32,
   jnxMbgPgwCgTspBatchRspErrors    Counter32,
   jnxMbgPgwCgTspBatchCDRsTx       Counter32,
   jnxMbgPgwCgTspTotalWFA          Counter32,
   jnxMbgPgwCgTspProfName          DisplayString
}

jnxMbgPgwCgTspProfId OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION
        "This will identify the CGF Group profile id uniquely and used as 
         secondary key for CGF group table"
   ::= { jnxMbgPgwCgTspStatsEntry 1 }

jnxMbgPgwCgTspDRTReqTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the DRT (Detailed Record Time) request transmitted 
         for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 2}

jnxMbgPgwCgTspDRTReqTmout OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the DRT request timeouts happend for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 3 }

jnxMbgPgwCgTspDRTSucRspRx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Total number of the DRT success responses received"
   ::= { jnxMbgPgwCgTspStatsEntry 4 }

jnxMbgPgwCgTspDRTErrRspRx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the DRT error responses received for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 5 }

jnxMbgPgwCgTspRediReqRx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the redirection responses received for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 6 }

jnxMbgPgwCgTspRediRspTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Total number of the redirection responses transmitted 
         for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 7}

jnxMbgPgwCgTspSwitchovers OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Total number of the switch overs on the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 8 }

jnxMbgPgwCgTspBatchReqTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Total number of the batch req transmitted for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 9 }

jnxMbgPgwCgTspBatchRspErrors OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Tatal number of the batch response errors for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 10 }

jnxMbgPgwCgTspBatchCDRsTx OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   
        " Total number of the batch CDRs transmitted for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 11 }

jnxMbgPgwCgTspTotalWFA OBJECT-TYPE
   SYNTAX        Counter32
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        " Total WFA available for the CGF group"
   ::= { jnxMbgPgwCgTspStatsEntry 12 }

jnxMbgPgwCgTspProfName OBJECT-TYPE
    SYNTAX      DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A string that uniquely identifies the TSP Profile."
    ::= { jnxMbgPgwCgTspStatsEntry 13 }

--
-- CGF Stats
--

jnxMbgPgwCgCgfStatsEntry OBJECT-TYPE
   SYNTAX        JnxMbgPgwCgCgfStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        obsolete 
   DESCRIPTION 
         "A conceptual row listing the statistics for each
          CGF Server configured on the PGW."
   INDEX         { jnxMbgPgwCgCgfIndex }
   ::= { jnxMbgPgwCgCgfStatsTable 1 }

JnxMbgPgwCgCgfStatsEntry ::= SEQUENCE {
   jnxMbgPgwCgCgfProfName          DisplayString,
   jnxMbgPgwCgCgfIndex             Integer32,
   jnxMbgPgwCgCgfIpAddress         IpAddress,
   jnxMbgPgwCgCgfStatus            INTEGER,
   jnxMbgPgwCgCgfUpDuration        Counter64,
   jnxMbgPgwCgCgfDownDuration      Counter64,
   jnxMbgPgwCgCgfEchoReqTx         Counter64, 
   jnxMbgPgwCgCgfEchoReqRx         Counter64,
   jnxMbgPgwCgCgfEchoReqTmout      Counter64, 
   jnxMbgPgwCgCgfEchoRespTx        Counter64,
   jnxMbgPgwCgCgfEchoRespRx        Counter64,
   jnxMbgPgwCgCgfVerUnsuppTx       Counter64,
   jnxMbgPgwCgCgfVerUnsuppRx       Counter64,
   jnxMbgPgwCgCgfNodeAliveReqTx    Counter64,
   jnxMbgPgwCgCgfNodeAliveReqRx    Counter64,
   jnxMbgPgwCgCgfNodeAliveReqTmout Counter64,
   jnxMbgPgwCgCgfNodeAliveRespTx   Counter64,
   jnxMbgPgwCgCgfNodeAliveRespRx   Counter64,
   jnxMbgPgwCgCgfRedirectReqRx     Counter64,
   jnxMbgPgwCgCgfRedirectRespTx    Counter64, 
   jnxMbgPgwCgCgfDRTReqTx          Counter64,
   jnxMbgPgwCgCgfDRTReqTmout       Counter64,
   jnxMbgPgwCgCgfDRTSuccRespRx     Counter64,
   jnxMbgPgwCgCgfDRTErrRespRx      Counter64,
   jnxMbgPgwCgCgfCdrTx             Counter64,
   jnxMbgPgwCgCgfDRTRTTMean        Counter64,
   jnxMbgPgwCgCgfDRTRTTMin         Counter64,
   jnxMbgPgwCgCgfDRTRTTMax         Counter64,
   jnxMbgPgwCgCgfTransToDownState  Counter64,
   jnxMbgPgwCgCgfContainers        Counter64
}

jnxMbgPgwCgCgfProfName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..127)) 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A string that uniquely identifies the CGF Profile."
    ::= { jnxMbgPgwCgCgfStatsEntry 1 }

jnxMbgPgwCgCgfIndex OBJECT-TYPE
   SYNTAX        Integer32 (1..48)
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A number representing each CGF Server whose statistics
          is being generated."
   ::= { jnxMbgPgwCgCgfStatsEntry 2 }

jnxMbgPgwCgCgfIpAddress OBJECT-TYPE
   SYNTAX        IpAddress
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "CGF Server IP-address."
   ::= { jnxMbgPgwCgCgfStatsEntry 3 }

jnxMbgPgwCgCgfStatus OBJECT-TYPE
   SYNTAX        INTEGER {
        up(1),     -- server is up
        down(2)     -- server is not reachable or unconfigured
   }
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION 
         "This indicates the state of the CGF Server i.e UP or DOWN."
   ::= { jnxMbgPgwCgCgfStatsEntry 4 }

jnxMbgPgwCgCgfUpDuration OBJECT-TYPE
   SYNTAX        Counter64
   UNITS         "minutes"
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total duration in minutes for which the CGF Server
                  was in UP State."
   ::= { jnxMbgPgwCgCgfStatsEntry 5 }

jnxMbgPgwCgCgfDownDuration OBJECT-TYPE
   SYNTAX        Counter64
   UNITS         "minutes"
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total duration in minutes for which the CGF Server 
                  was in DOWN State."
   ::= { jnxMbgPgwCgCgfStatsEntry 6 }

jnxMbgPgwCgCgfEchoReqTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Requests transmitted to the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 7 }

jnxMbgPgwCgCgfEchoReqRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Requests received from the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 8 }

jnxMbgPgwCgCgfEchoReqTmout OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Requests to the CGF Server that 
                  timed out."
   ::= { jnxMbgPgwCgCgfStatsEntry 9 }

jnxMbgPgwCgCgfEchoRespTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Responses transmitted to the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 10 }

jnxMbgPgwCgCgfEchoRespRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Responses received from the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 11 }

jnxMbgPgwCgCgfVerUnsuppTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Version Unsupported messages transmitted to 
                 the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 12 }

jnxMbgPgwCgCgfVerUnsuppRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Version Unsupported messages received 
                 from the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 13 }

jnxMbgPgwCgCgfNodeAliveReqTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Node Alive Requests transmitted to the
                  CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 14 }

jnxMbgPgwCgCgfNodeAliveReqRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Node Alive Requests received from 
                  the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 15 }

jnxMbgPgwCgCgfNodeAliveReqTmout OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Node Alive Requests to the CGF Server 
                  that timed out."
   ::= { jnxMbgPgwCgCgfStatsEntry 16 }

jnxMbgPgwCgCgfNodeAliveRespTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Node Alive Responses transmitted 
                  to the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 17 }

jnxMbgPgwCgCgfNodeAliveRespRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Node Alive Responses received from 
                  the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 18 }

jnxMbgPgwCgCgfRedirectReqRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Redirect Requests received from 
                  the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 19 }

jnxMbgPgwCgCgfRedirectRespTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Redirect Responses transmitted 
                  to the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 20 }

jnxMbgPgwCgCgfDRTReqTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Data Record Transfer Requests transmitted to 
                  the CGF Server.This includes the retransmission counts also."
   ::= { jnxMbgPgwCgCgfStatsEntry 21 }

jnxMbgPgwCgCgfDRTReqTmout OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Data Record Transfer Requests to the CGF  
                 Server that timed out after the configured number of retries."
   ::= { jnxMbgPgwCgCgfStatsEntry 22 }

jnxMbgPgwCgCgfDRTSuccRespRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Data Record Transfer Responses indicating 
                  success received from the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 23 }

jnxMbgPgwCgCgfDRTErrRespRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Data Record Transfer Responses indicating 
                  error received from the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 24 }


jnxMbgPgwCgCgfCdrTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Call Data Records (CDRs) transmitted 
                  to the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 25 }

jnxMbgPgwCgCgfDRTRTTMean OBJECT-TYPE
   SYNTAX        Counter64
   UNITS         "seconds"
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Mean Round Trip Time of the Data Record Transfer Request and Response 
         to and from the CGF Server in seconds. This is calculated from the 
         average of the minimum and maximum round trip times of the Data Record 
         Transfer Request. This is applicable for CGF Servers which are 
         connected via UDP protocol."
   ::= { jnxMbgPgwCgCgfStatsEntry 26 }

jnxMbgPgwCgCgfDRTRTTMin OBJECT-TYPE
   SYNTAX        Counter64
   UNITS         "seconds"
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Minimum Round Trip Time of the Data Record Transfer Request and 
        Response to and from the CGF Server in seconds. This is 
        applicable for CGF Servers which are connected via UDP protocol."
   ::= { jnxMbgPgwCgCgfStatsEntry 27 }

jnxMbgPgwCgCgfDRTRTTMax OBJECT-TYPE
   SYNTAX        Counter64
   UNITS         "seconds"
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION
        "Maximum Round Trip Time of the Data Record Transfer Request and 
         Response to and from the CGF Server in seconds.This is 
         applicable for CGF Servers which are connected via UDP protocol."
   ::= { jnxMbgPgwCgCgfStatsEntry 28 }

jnxMbgPgwCgCgfTransToDownState OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of transitions of the CGF Server to 
                  the DOWN state."
   ::= { jnxMbgPgwCgCgfStatsEntry 29 }

jnxMbgPgwCgCgfContainers OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of closed containers to the CGF Server."
   ::= { jnxMbgPgwCgCgfStatsEntry 30 }

--
-- CGF Stats
--

jnxMbgPgwCgPeerStatsEntry OBJECT-TYPE
   SYNTAX        JnxMbgPgwCgPeerStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A conceptual row listing the statistics for each
          CGF Server configured on the PGW."
   INDEX         { jnxMbgGwIndex, jnxMbgPgwCgPeerIndex }
   ::= { jnxMbgPgwCgPeerStatsTable 1 }

JnxMbgPgwCgPeerStatsEntry ::= SEQUENCE {
   jnxMbgPgwCgPeerIndex             Unsigned32,
   jnxMbgPgwCgPeerIpAddress         IpAddress,
   jnxMbgPgwCgPeerStatus            INTEGER,
   jnxMbgPgwCgPeerEchoReqTx         Counter64, 
   jnxMbgPgwCgPeerEchoReqRx         Counter64,
   jnxMbgPgwCgPeerEchoReqTmout      Counter64, 
   jnxMbgPgwCgPeerEchoRespTx        Counter64,
   jnxMbgPgwCgPeerEchoRespRx        Counter64,
   jnxMbgPgwCgPeerVerUnsuppTx       Counter64,
   jnxMbgPgwCgPeerVerUnsuppRx       Counter64,
   jnxMbgPgwCgPeerNodeAliveReqRx    Counter64,
   jnxMbgPgwCgPeerNodeAliveRespTx   Counter64,
   jnxMbgPgwCgPeerRedirectReqRx     Counter64,
   jnxMbgPgwCgPeerRedirectRespTx    Counter64, 
   jnxMbgPgwCgPeerDRTReqTx          Counter64,
   jnxMbgPgwCgPeerDRTSuccRespRx     Counter64,
   jnxMbgPgwCgPeerDRTErrRespRx      Counter64,
   jnxMbgPgwCgPeerProfileName       DisplayString 
}

jnxMbgPgwCgPeerIndex OBJECT-TYPE
   SYNTAX        Unsigned32
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A number representing each CGF Server whose statistics
          is being generated."
   ::= { jnxMbgPgwCgPeerStatsEntry 1 }

jnxMbgPgwCgPeerIpAddress OBJECT-TYPE
   SYNTAX        IpAddress
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "CGF Server IP-address."
   ::= { jnxMbgPgwCgPeerStatsEntry 2 }

jnxMbgPgwCgPeerStatus OBJECT-TYPE
   SYNTAX        INTEGER {
        up(1),     -- server is up
        down(2)     -- server is not reachable or unconfigured
   }
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION 
         "This indicates the state of the CGF Server i.e UP or DOWN."
   ::= { jnxMbgPgwCgPeerStatsEntry 3 }

jnxMbgPgwCgPeerEchoReqTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Requests transmitted to the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 4 }

jnxMbgPgwCgPeerEchoReqRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Requests received from the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 5 }

jnxMbgPgwCgPeerEchoReqTmout OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Requests to the CGF Server that 
                  timed out."
   ::= { jnxMbgPgwCgPeerStatsEntry 6 }

jnxMbgPgwCgPeerEchoRespTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Responses transmitted to the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 7 }

jnxMbgPgwCgPeerEchoRespRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Echo Responses received from the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 8 }

jnxMbgPgwCgPeerVerUnsuppTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Version Unsupported messages transmitted to 
                 the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 9 }

jnxMbgPgwCgPeerVerUnsuppRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Version Unsupported messages received 
                 from the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 10 }

jnxMbgPgwCgPeerNodeAliveReqRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Node Alive Requests received from 
                  the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 11 }

jnxMbgPgwCgPeerNodeAliveRespTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Node Alive Responses transmitted 
                  to the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 12 }

jnxMbgPgwCgPeerRedirectReqRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Redirect Requests received from 
                  the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 13 }

jnxMbgPgwCgPeerRedirectRespTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Redirect Responses transmitted 
                  to the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 14 }

jnxMbgPgwCgPeerDRTReqTx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Data Record Transfer Requests transmitted to 
                  the CGF Server.This includes the retransmission counts also."
   ::= { jnxMbgPgwCgPeerStatsEntry 15 }

jnxMbgPgwCgPeerDRTSuccRespRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Data Record Transfer Responses indicating 
                  success received from the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 16 }

jnxMbgPgwCgPeerDRTErrRespRx OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of Data Record Transfer Responses indicating 
                  error received from the CGF Server."
   ::= { jnxMbgPgwCgPeerStatsEntry 17 }

jnxMbgPgwCgPeerProfileName OBJECT-TYPE
    SYNTAX      DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A string that uniquely identifies the CGF Peer Profile."
    ::= { jnxMbgPgwCgPeerStatsEntry 18 }

 jnxMbgPgwCgGlobalStatsEntry OBJECT-TYPE
   SYNTAX       JnxMbgPgwCgGlobalStatsEntry
   MAX-ACCESS    not-accessible
   STATUS        current
   DESCRIPTION 
         "A conceptual row listing the statistics for each
          PGW charging global statistics."
   INDEX         { jnxMbgGwIndex }
   ::= { jnxMbgPgwCgGlobalStatsTable 1 }

JnxMbgPgwCgGlobalStatsEntry ::= SEQUENCE {
   jnxMbgPgwCgCdrSendErrors           Counter64,
   jnxMbgPgwCgCdrEncodeErrors         Counter64,
   jnxMbgPgwCgCdrAllocFailures        Counter64,
   jnxMbgPgwCgContFailures            Counter64,
   jnxMbgPgwCgCmBearersCreated        Counter64,
   jnxMbgPgwCgCmBearersDeleted        Counter64
}

--Charging Global Stats*/
jnxMbgPgwCgCdrSendErrors OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of CDR send errors to charging module" 
   ::= { jnxMbgPgwCgGlobalStatsEntry 1 }

jnxMbgPgwCgCdrEncodeErrors OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of CDR (charging data record) encoding errors." 
   ::= { jnxMbgPgwCgGlobalStatsEntry 2 }

jnxMbgPgwCgCdrAllocFailures OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of CDR memory allocation failures."
   ::= { jnxMbgPgwCgGlobalStatsEntry 3 }

jnxMbgPgwCgContFailures OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of container failures."
   ::= { jnxMbgPgwCgGlobalStatsEntry 4 }

jnxMbgPgwCgCmBearersCreated OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number bearers created."
   ::= { jnxMbgPgwCgGlobalStatsEntry 5 }

jnxMbgPgwCgCmBearersDeleted OBJECT-TYPE
   SYNTAX        Counter64
   MAX-ACCESS    read-only
   STATUS        current
   DESCRIPTION   "Total number of bearers deleted."
   ::= { jnxMbgPgwCgGlobalStatsEntry 6 }

jnxMbgPgwCgServerName OBJECT-TYPE
    SYNTAX      DisplayString  (SIZE (0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
    "A string that uniquely identifies the CGF server name."
   ::= { jnxMbgPgwCgNotificationVars 1 }

jnxMbgPgwCgServicePicName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
    "This identifies the session-pic, in the form ms-a/b/0, 
     where <a> is the slot and <b> could be either 0 or 1."
   ::= { jnxMbgPgwCgNotificationVars 2 }

jnxMbgPgwCgCDRDest OBJECT-TYPE
    SYNTAX        INTEGER {
                      cdrcgf       (1),    
                      cdrbackup    (2),    
                      cdrnobackup  (3) }
    MAX-ACCESS    accessible-for-notify
    STATUS        current
    DESCRIPTION 
"This indicates any transisitions in the state of the CGF.
Value 1 indicates one of the CGF for the Group came up. Redirecting CDRs to the Active CGF.
Value 2 indicates last active CGF for the Group went down. CDRs being written to backup Local storage device.
Value 3 indicates last active CGF for the Group went down. Backup Local storage device not configured."
   ::= { jnxMbgPgwCgNotificationVars 3 }

jnxMbgPgwCgActiveCgfIpAddr OBJECT-TYPE
   SYNTAX        IpAddress
   MAX-ACCESS    accessible-for-notify
   STATUS        current
   DESCRIPTION   "CGF Server IP-address."
   ::= { jnxMbgPgwCgNotificationVars 4 }

jnxMbgPgwCgTSPName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (0..31)) 
    MAX-ACCESS  accessible-for-notify
    STATUS       deprecated
    DESCRIPTION
        "A string that uniquely identifies the Transport Profile."
    ::= { jnxMbgPgwCgNotificationVars 5 }

jnxMbgPgwCgMemLimit OBJECT-TYPE
    SYNTAX        INTEGER {
                      memfulldisconnectnew          (1),
                      memfulldisconnectnewrslvd     (2),
                      memfulldisconnectexistnew     (3),
                      memfulldisconnectexistnewrslvd(4)
    }
    MAX-ACCESS    accessible-for-notify
    STATUS        current
    DESCRIPTION 
"This indicates any transisitions in the state of the CGF.
Value 1 indicates System has reached Level 1 critical memory threshold. 
Action - Check the CGF server connections. If local storage is enabled,
         please ftp the charging records immediately.
         If local storage is not enabled, please enable it so the
         charging records can be stored in local persistent storage. 
Risk -  No new sessions will be allowed.
Value 2 indicates System reaching Level 1 critical memory threshold
        condition has been resolved.
Value 3 indicates System has reached Level 2 critical memory threshold.
Action - Check the CGF server connections. If local storage is enabled,
         please ftp the charging records immediately.
         If local storage is not enabled, please enable it so the
         charging records can be stored in local persistent storage.
Risk -  New and existing sessions will be not be allowed.
Value 4 indicates System reaching Level 2 critical memory threshold
        condition has been resolved."
    ::= { jnxMbgPgwCgNotificationVars 6 }

jnxMbgPgwCgLcsSpace OBJECT-TYPE
    SYNTAX        INTEGER {
                      localstoragememlevel1     (1),
                      localstoragememlevel2     (2),
                      localstoragememlevel3     (3)
    }
    MAX-ACCESS    accessible-for-notify
    STATUS        current 
    DESCRIPTION
    "Water marking for the local storage levels in charged of RE."
   ::= { jnxMbgPgwCgNotificationVars 7 }

jnxMbgPgwCgLcsUtil OBJECT-TYPE
    SYNTAX      Gauge32
    UNITS       "percent"
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The percentage of the total of Local Storage
         Space by one the Charged on RE"
        ::= { jnxMbgPgwCgNotificationVars 8 }

jnxMbgPgwCgAlarmStatus OBJECT-TYPE
    SYNTAX        INTEGER {
                      raised	    (1),
                      cleared	    (2)
    }
    MAX-ACCESS    accessible-for-notify
    STATUS        current 
    DESCRIPTION
    "Value 1 indicates that the Alarm for a particular condition is present.
    Value 2 indicates that the Alarm for a particular condition is absent."
   ::= { jnxMbgPgwCgNotificationVars 9 }

jnxMbgPgwCgProfileName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      deprecated
    DESCRIPTION
        "A string that identifies a charging profile ."
    ::= { jnxMbgPgwCgNotificationVars 10 }

jnxMbgPgwCgPrevMMState OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "A string that indicates the maintenance-mode state ."
    ::= { jnxMbgPgwCgNotificationVars 11 }

jnxMbgPgwCgNewMMState OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "A string that indicates the maintenance-mode state ."
    ::= { jnxMbgPgwCgNotificationVars 12 }

jnxMbgPgwCgTProfileName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      deprecated
    DESCRIPTION
        "A string that identifies a charging profile ."
    ::= { jnxMbgPgwCgNotificationVars 13 }

jnxMbgPgwCgTPrevMMState OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      deprecated
    DESCRIPTION
        "A string that indicates the maintenance-mode state ."
    ::= { jnxMbgPgwCgNotificationVars 14 }

jnxMbgPgwCgTNewMMState OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      deprecated
    DESCRIPTION
        "A string that indicates the maintenance-mode state ."
    ::= { jnxMbgPgwCgNotificationVars 15 }

jnxMbgPgwCgPeerProfName OBJECT-TYPE
  SYNTAX      DisplayString  
  MAX-ACCESS  accessible-for-notify
  STATUS      current
  DESCRIPTION
    "A string that uniquely identifies the CGF Profile."
  ::= { jnxMbgPgwCgNotificationVars 16 }

jnxMbgPgwCgGtpGWUpNotif NOTIFICATION-TYPE
     OBJECTS          { jnxMbgPgwCgServerName, 
                        jnxMbgPgwCgServicePicName }
     STATUS             deprecated 
     DESCRIPTION
        "This notification signifies that the specified server has been
        marked alive.  The ServerName identifies the server and the
        SPIdentfier identifies the session-pic which originated this
        notification."
    ::= { jnxMbgPgwCgNotifications 1 }

jnxMbgPgwCgGtpGWDownNotif NOTIFICATION-TYPE
    OBJECTS     { jnxMbgPgwCgServerName, 
                  jnxMbgPgwCgServicePicName }
    STATUS        deprecated
    DESCRIPTION
        "This notification signifies that the specified server has been
        marked dead.  The ServerName identifies the server and the
        SPIdentfier identifies the session-pic which originated this
        notification."
    ::= { jnxMbgPgwCgNotifications 2 }

jnxMbgPgwCgCDRDestNotif NOTIFICATION-TYPE
    OBJECTS	    { jnxMbgPgwCgCDRDest,
		      jnxMbgPgwCgTSPName,
		      jnxMbgPgwCgActiveCgfIpAddr }
    STATUS            deprecated
    DESCRIPTION
          "This signifies change in the destination of the CDRs 
           (Charging Data Record)"        
    ::= { jnxMbgPgwCgNotifications 3 } 

jnxMbgPgwCgMemThresNotif NOTIFICATION-TYPE
    OBJECTS	{   jnxMbgPgwCgAlarmStatus,
                    jnxMbgPgwCgMemLimit,
		    jnxMbgPgwCgTSPName,
		    jnxMbgPgwCgServicePicName }
    STATUS          deprecated
    DESCRIPTION
        "This signifies the internal memory unavalability in the system."
      ::= { jnxMbgPgwCgNotifications 4 } 

jnxMbgPgwCgLcsThresNotif NOTIFICATION-TYPE
    OBJECTS      { jnxMbgPgwCgLcsSpace, 
                   jnxMbgPgwCgLcsUtil  }
    STATUS          deprecated
    DESCRIPTION
          "This signifies the memory unavailability in the local storage in 
            the system."
    ::= { jnxMbgPgwCgNotifications 5 }

jnxMbgPgwCgServiceUpNotif NOTIFICATION-TYPE
    OBJECTS	{  jnxMbgPgwCgServicePicName } 
    STATUS         deprecated
    DESCRIPTION
        "This signifies the Charging daemon is UP on the SP."      
    ::= { jnxMbgPgwCgNotifications 6 } 

jnxMbgPgwCgMMStateChange NOTIFICATION-TYPE
    OBJECTS     {  jnxMbgPgwCgProfileName,
                   jnxMbgPgwCgPrevMMState,
                   jnxMbgPgwCgNewMMState   }
    STATUS         deprecated
    DESCRIPTION
        "This indicates that the given charging profile underwent a change
         in the maintenance-mode."
    ::= { jnxMbgPgwCgNotifications 7 }

jnxMbgPgwCgTMMStateChange NOTIFICATION-TYPE
    OBJECTS     {  jnxMbgPgwCgTProfileName,
                   jnxMbgPgwCgTPrevMMState,
                   jnxMbgPgwCgTNewMMState   }
    STATUS         deprecated
    DESCRIPTION
        "This indicates that the given transport profile underwent a change
         in the maintenance-mode."
    ::= { jnxMbgPgwCgNotifications 8 }

jnxMbgPgwCgGtpGWUpNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgServerName, 
                   jnxMbgPgwCgServicePicName }
     STATUS        current 
     DESCRIPTION
        "This notification signifies that the specified server has been
        marked alive.  The ServerName identifies the server and the
        SPIdentfier identifies the session-pic which originated this
        notification."
    ::= { jnxMbgPgwCgNotifications 9 }

jnxMbgPgwCgGtpGWDownNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgServerName, 
                   jnxMbgPgwCgServicePicName }
    STATUS         current
    DESCRIPTION
        "This notification signifies that the specified server has been
        marked dead.  The ServerName identifies the server and the
        SPIdentfier identifies the session-pic which originated this
        notification."
    ::= { jnxMbgPgwCgNotifications 10 }


jnxMbgPgwCgCDRDestNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgCDRDest,
                   jnxMbgPgwCgPeerProfName,
                   jnxMbgPgwCgActiveCgfIpAddr }
    STATUS         current 
    DESCRIPTION
          "This signifies change in the destination of the CDRs 
           (Charging Data Record)"        
    ::= { jnxMbgPgwCgNotifications 11 } 

jnxMbgPgwCgServiceUpNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgServicePicName } 
    STATUS         current
    DESCRIPTION
        "This signifies the Charging daemon is UP on the SP."
    ::= { jnxMbgPgwCgNotifications 12 } 

jnxMbgPgwCgMMStateChangeNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgPeerProfName,
                   jnxMbgPgwCgPrevMMState,
                   jnxMbgPgwCgNewMMState   }
    STATUS         current
    DESCRIPTION
        "This indicates that the given charging profile underwent a change
         in the maintenance-mode."
    ::= { jnxMbgPgwCgNotifications 13 }

jnxMbgPgwCgTMMStateChangeNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgPeerProfName,
                   jnxMbgPgwCgPrevMMState,
                   jnxMbgPgwCgNewMMState   }
    STATUS         current
    DESCRIPTION
        "This indicates that the given transport profile underwent a change
         in the maintenance-mode."
    ::= { jnxMbgPgwCgNotifications 14 }

jnxMbgPgwCgMemHighThresNotify NOTIFICATION-TYPE
    OBJECTS        { jnxMbgGwName,
                     jnxMbgPgwCgPeerProfName,
                     jnxMbgPgwCgServicePicName,
                     jnxMbgPgwCgMemLimit,
                     jnxMbgPgwCgAlarmStatus }
    STATUS           current
    DESCRIPTION
          "This trap indicates the alarm status on the node associated with the utilization of inernal memory space for charging records. This alarm is sent outwhen the utilization exceeds or falls below configured high threshold value. Thealarm status (Active/Clear)is indicated by the jnxMbgPgwCgAlarmStatus variable."
      ::= { jnxMbgPgwCgNotifications 15 } 

jnxMbgPgwCgMemMediumThresNotify NOTIFICATION-TYPE
    OBJECTS        { jnxMbgGwName,
                     jnxMbgPgwCgPeerProfName,
                     jnxMbgPgwCgServicePicName,
                     jnxMbgPgwCgMemLimit,
                     jnxMbgPgwCgAlarmStatus }
    STATUS           current
    DESCRIPTION
          "This trap indicates the alarm status on the node associated with the utilization of inernal memory space for charging records. This alarm is sent outwhen the utilization exceeds or falls below configured medium threshold value.  The alarm status (Active/Clear)is indicated by the jnxMbgPgwCgAlarmStatus variable."
      ::= { jnxMbgPgwCgNotifications 16 } 

jnxMbgPgwCgMemLowThresNotify NOTIFICATION-TYPE
    OBJECTS        { jnxMbgGwName,
                     jnxMbgPgwCgPeerProfName,
                     jnxMbgPgwCgServicePicName,
                     jnxMbgPgwCgMemLimit,
                     jnxMbgPgwCgAlarmStatus }
    STATUS           current
    DESCRIPTION
          "This trap indicates the alarm status on the node associated with the utilization of inernal memory space for charging records. This alarm is sent outwhen the utilization exceeds or falls below configured low threshold value. The alarm status (Active/Clear)is indicated by the jnxMbgPgwCgAlarmStatus variable."
      ::= { jnxMbgPgwCgNotifications 17 } 

jnxMbgPgwCgLcsThresHighNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgAlarmStatus,
                   jnxMbgPgwCgLcsUtil  }
    STATUS         current
    DESCRIPTION
          "This trap indicates the alarm status on the node associated with the utilization of local storage space for charging records. This alarm is sent out when the utilization exceeds or falls below configured high threshold of available disk space. The alarm status (Active/Clear)is indicated by the jnxMbgPgwCgAlarmStatus variable."
    ::= { jnxMbgPgwCgNotifications 18 }

jnxMbgPgwCgLcsThresMediumNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgAlarmStatus,
                   jnxMbgPgwCgLcsUtil }
    STATUS         current
    DESCRIPTION
          "This trap indicates the alarm status on the node associated with the utilization of local storage space for charging records. This alarm is sent out when the utilization exceeds or falls below configured medium threshold of available disk space. The alarm status (Active/Clear)is indicated by  the jnxMbgPgwCgAlarmStatus variable."
    ::= { jnxMbgPgwCgNotifications 19 }

jnxMbgPgwCgLcsThresLowNotify NOTIFICATION-TYPE
    OBJECTS      { jnxMbgGwName,
                   jnxMbgPgwCgAlarmStatus,  
                   jnxMbgPgwCgLcsUtil }
    STATUS         current
    DESCRIPTION
          "This trap indicates the alarm status on the node associated with the utilization of local storage space for charging records. This alarm is sent out when the utilization exceeds or falls below configured low threshold of available disk space. The alarm status (Active/Clear)is indicated by  the jnxMbgPgwCgAlarmStatus variable."
    ::= { jnxMbgPgwCgNotifications 20 }


END
