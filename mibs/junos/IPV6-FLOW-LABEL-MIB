
-- WinAgents MIB Extraction Wizard
-- Extracted from rfc3595.txt 16.03.2005 20:22:00

IPV6-FLOW-LABEL-MIB DEFINITIONS ::= BEGIN

IMPORTS

    MODULE-IDENTITY, mib-2, Integer32           FROM SNMPv2-SMI
    TEXTUAL-CONVENTION                          FROM SNMPv2-TC;

ipv6FlowLabelMIB   MODULE-IDENTITY

    LAST-UPDATED  "200308280000Z"  -- 28 August 2003
    ORGANIZATION  "IETF Operations and Management Area"
    CONTACT-INFO  "<PERSON> (Editor)
                   Lucent Technologies
                   Schagen 33
                   3461 GL Linschoten
                   Netherlands



                   Phone: +31 348-407-775
                   EMail: <EMAIL>

                   Send comments to <<EMAIL>>.
                  "
    DESCRIPTION   "This MIB module provides commonly used textual
                   conventions for IPv6 Flow Labels.

                   Copyright (C) The Internet Society (2003).  This
                   version of this MIB module is part of RFC 3595,
                   see the RFC itself for full legal notices.
                  "
    -- Revision History

    REVISION      "200308280000Z"  -- 28 August 2003
    DESCRIPTION   "Initial version, published as RFC 3595."

    ::= { mib-2 103 }

IPv6FlowLabel      ::= TEXTUAL-CONVENTION
    DISPLAY-HINT  "d"
    STATUS         current
    DESCRIPTION   "The flow identifier or Flow Label in an IPv6
                   packet header that may be used to discriminate
                   traffic flows.
                  "
    REFERENCE     "Internet Protocol, Version 6 (IPv6) specification,
                   section 6.  RFC 2460.
                  "
    SYNTAX         Integer32 (0..1048575)

IPv6FlowLabelOrAny ::= TEXTUAL-CONVENTION
    DISPLAY-HINT  "d"
    STATUS         current
    DESCRIPTION   "The flow identifier or Flow Label in an IPv6
                   packet header that may be used to discriminate
                   traffic flows.  The value of -1 is used to
                   indicate a wildcard, i.e. any value.
                  "
    SYNTAX         Integer32 (-1 | 0..1048575)

END

