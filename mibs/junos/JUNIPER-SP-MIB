--
-- Juniper Enterprise Specific MIB: Service PIC MIB
-- 
-- Copyright (c) 2003-2013, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-SP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Counter32, <PERSON><PERSON><PERSON>32, <PERSON><PERSON>ger32,
    NOTIFICATION-TYPE, OBJECT-IDENTITY
        FROM SNMPv2-SMI
    DisplayString
        FROM SNMPv2-TC
    ifIndex, InterfaceIndex
        FROM IF-MIB
    CounterBasedGauge64
        FROM HCNUM-TC
    jnxMibs, jnxSpNotifications
        FROM JUNIPER-SMI;

jnxSpMIB  MODULE-IDENTITY
    LAST-UPDATED "201909190000Z"     -- Thu Sept 19 00:00:00 2019 UTC
    ORGANIZATION "Juniper Networks, Inc." 
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"

    DESCRIPTION
            "Provides data about each of the AS Pics on a router."

    -- revision history
    REVISION  "200504020000Z"
    DESCRIPTION
            "Initial revision."
    REVISION  "201302230000Z"    --Feb  23, 2013
    DESCRIPTION
            "Added OID jnxSpSvcSetFlowLimitUtilised under jnxSpNotificationPrefix 
            Depreated OID jnxSpSvcSetFlowLimitUtilized ."

    REVISION    "201605310000Z" -- 31-May-16
    DESCRIPTION
            "Removed duplicate entries"

    REVISION    "201810220000Z" -- 22-Oct-18
    DESCRIPTION
            "Added OID jnxSpTcpLoggingHostStatus under jnxSpNotificationPrefix"

    REVISION    "201908100000Z" -- 10-Aug-19
    DESCRIPTION
            "Added OIDs jnxSpSvcSetMemoryUsage64, jnxSpSvcSetMemLimitPktDrops64,
             jnxSpSvcSetCpuLimitPktDrops64, jnxSpSvcSetFlowLimitPktDrops64
             under JnxSpSvcSetEntry. "

    REVISION    "201909190000Z" -- 19-Sept-19
    DESCRIPTION
            "Added OID jnxSpSvcSetSessCount under JnxSpSvcSetEntry.
             Added new OIDs under JnxSpSvcSetIfEntry."

    ::= { jnxMibs 32 }

    jnxFlowLimitTrapVars   OBJECT IDENTIFIER ::= { jnxSpMIB 2 }
    jnxTcpLoggingTrapVars  OBJECT IDENTIFIER ::= { jnxSpMIB 3 }
    
--
-- Per Service Set information
--

    jnxSpSvcSet OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Information about Service PIC Service Sets."
        ::= { jnxSpMIB 1 }

    jnxSpSvcSetTable OBJECT-TYPE
        SYNTAX       SEQUENCE OF JnxSpSvcSetEntry
        MAX-ACCESS   not-accessible
        STATUS       current
        DESCRIPTION
            "Data about each service set on each Service PIC on the router."
        ::= { jnxSpSvcSet 1 }

    jnxSpSvcSetEntry OBJECT-TYPE
        SYNTAX       JnxSpSvcSetEntry
        MAX-ACCESS   not-accessible
        STATUS       current
        DESCRIPTION
            "Each entry provides information about a single Service Set.
            The Service Set is identified by its name and the Service PIC the
            Service Set is configured on is identified by jnxSpSvcSetIfName."
        INDEX   { jnxSpSvcSetName }
        ::= { jnxSpSvcSetTable 1 }

    JnxSpSvcSetEntry ::=
        SEQUENCE {
            jnxSpSvcSetName                 DisplayString,
            jnxSpSvcSetSvcType              DisplayString,
            jnxSpSvcSetTypeIndex            Integer32,
            jnxSpSvcSetIfName               DisplayString,
            jnxSpSvcSetIfIndex              InterfaceIndex,
            jnxSpSvcSetMemoryUsage          Gauge32,
            jnxSpSvcSetCpuUtil              Gauge32,
            jnxSpSvcSetSvcStyle             INTEGER, 
            jnxSpSvcSetMemLimitPktDrops     Counter32,
            jnxSpSvcSetCpuLimitPktDrops     Counter32,
            jnxSpSvcSetFlowLimitPktDrops    Counter32,
            jnxSpSvcSetMemoryUsage64        CounterBasedGauge64,
            jnxSpSvcSetMemLimitPktDrops64   CounterBasedGauge64,
            jnxSpSvcSetCpuLimitPktDrops64   CounterBasedGauge64,
            jnxSpSvcSetFlowLimitPktDrops64  CounterBasedGauge64,
            jnxSpSvcSetSessCount            Counter32
        }

    jnxSpSvcSetName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..96))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The Service Set name."
        ::= { jnxSpSvcSetEntry 1 }

    jnxSpSvcSetSvcType OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The name of the Service Type associated with this table entry."
        ::= { jnxSpSvcSetEntry 2 }

    jnxSpSvcSetTypeIndex OBJECT-TYPE
        SYNTAX      Integer32 (0..255)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "A generic integer used to identify the Service Type for this
            entry."
        ::= { jnxSpSvcSetEntry 3 }

    jnxSpSvcSetIfName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The ifName of the interface identifying the Service PIC.  
            If more than one interface is associated with the Service PIC, 
            the name associated with the lowest layer interface is used for 
            this object."
        ::= { jnxSpSvcSetEntry 4 }

    jnxSpSvcSetIfIndex OBJECT-TYPE
        SYNTAX      InterfaceIndex
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The ifIndex corresponding to jnxSpSvcSetIfName."
        ::= { jnxSpSvcSetEntry 5 }

    jnxSpSvcSetMemoryUsage OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service Set, expressed in bytes."
        ::= { jnxSpSvcSetEntry 6 }

    jnxSpSvcSetCpuUtil OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "percent"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of CPU used by this Service Set, expressed as a
            percentage of the total."
        ::= { jnxSpSvcSetEntry 7 }

    jnxSpSvcSetSvcStyle OBJECT-TYPE
        SYNTAX      INTEGER {
                        unknown           (1),
                        interface-service (2),
                        next-hop-service  (3)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The type of this Service Set. The definitions of each style 
            being:
            
             Unknown            - Service style is not known.
             Interface-service  - Service style is interface based.
             Next-hop-service   - Service style is next-hop based."
        ::= { jnxSpSvcSetEntry 8 }

    jnxSpSvcSetMemLimitPktDrops OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets dropped due to the Service Set exceeding
            its memory limits (when in Red Zone)."
        ::= { jnxSpSvcSetEntry 9 }

    jnxSpSvcSetCpuLimitPktDrops OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets dropped due to the Service Set exceeding
            the average cpu limits (when total exceeds 85%)."
        ::= { jnxSpSvcSetEntry 10 }

    jnxSpSvcSetFlowLimitPktDrops OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets dropped due to the Service Set exceeding
            the flow limit."
        ::= { jnxSpSvcSetEntry 11 }

    jnxSpSvcSetMemoryUsage64 OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service Set, expressed in
            bytes, represented by 64 bit integer."
        ::= { jnxSpSvcSetEntry 12 }

    jnxSpSvcSetMemLimitPktDrops64 OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets dropped due to the Service Set exceeding
            its memory limits (when in Red Zone),
            represented by 64 bit integer."
        ::= { jnxSpSvcSetEntry 13 }

    jnxSpSvcSetCpuLimitPktDrops64 OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets dropped due to the Service Set exceeding
            the average cpu limits (when total exceeds 85%),
            represented by 64 bit integer."
        ::= { jnxSpSvcSetEntry 14 }

    jnxSpSvcSetFlowLimitPktDrops64 OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets dropped due to the Service Set exceeding
            the flow limit, represented by 64 bit integer."
        ::= { jnxSpSvcSetEntry 15 }

    jnxSpSvcSetSessCount OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of sessions active in the Service Set."
        ::= { jnxSpSvcSetEntry 16 }


--
-- Per Service information
--

    jnxSpSvcSetSvcTypeTable OBJECT-TYPE
        SYNTAX       SEQUENCE OF JnxSpSvcSetSvcTypeEntry
        MAX-ACCESS   not-accessible
        STATUS       current
        DESCRIPTION
            "Data about each service on each Service PIC on the router."
        ::= { jnxSpSvcSet 2 }

    jnxSpSvcSetSvcTypeEntry OBJECT-TYPE
        SYNTAX       JnxSpSvcSetSvcTypeEntry
        MAX-ACCESS   not-accessible
        STATUS       current
        DESCRIPTION
            "Each entry provides information about a single Service on
            each Service PIC.  Each Service PIC is identified by its 
            corresponding ifIndex, while each Service is identified by 
            jnxSpSvcSetSvcTypeIndex.  The Service Type associated with 
            this index is provided by jnxSpSvcSetSvcTypeName."
        INDEX   { ifIndex, jnxSpSvcSetSvcTypeIndex }
        ::= { jnxSpSvcSetSvcTypeTable 1 }

    JnxSpSvcSetSvcTypeEntry ::=
        SEQUENCE {
            jnxSpSvcSetSvcTypeIndex             Integer32,
            jnxSpSvcSetSvcTypeIfName            DisplayString,
            jnxSpSvcSetSvcTypeName              DisplayString,
            jnxSpSvcSetSvcTypeSvcSets           Gauge32,
            jnxSpSvcSetSvcTypeMemoryUsage       Gauge32,
            jnxSpSvcSetSvcTypePctMemoryUsage    Gauge32,
            jnxSpSvcSetSvcTypeCpuUtil           Gauge32,
            jnxSpSvcSetSvcTypeMemoryUsage64     CounterBasedGauge64
        }

    jnxSpSvcSetSvcTypeIndex OBJECT-TYPE
        SYNTAX      Integer32 (0..255)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A generic integer used to identify the Service Type for this
            entry."
        ::= { jnxSpSvcSetSvcTypeEntry 1 }

    jnxSpSvcSetSvcTypeIfName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The ifName of the interface identifying the Service PIC.  If 
            more than one interface is associated with the Service PIC, the 
            name associated with the lowest layer interface is used for this 
            object."
        ::= { jnxSpSvcSetSvcTypeEntry 2 }

    jnxSpSvcSetSvcTypeName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The name of the Service Type associated with this table entry."
        ::= { jnxSpSvcSetSvcTypeEntry 3 }

    jnxSpSvcSetSvcTypeSvcSets OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of Service Sets configured on this Service PIC that 
            use this service type."
        ::= { jnxSpSvcSetSvcTypeEntry 4 }

    jnxSpSvcSetSvcTypeMemoryUsage OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service Type, expressed in
            bytes."
        ::= { jnxSpSvcSetSvcTypeEntry 5 }

    jnxSpSvcSetSvcTypePctMemoryUsage OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "percent"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service Type, expressed as a
            percentage of the total."
        ::= { jnxSpSvcSetSvcTypeEntry 6 }

    jnxSpSvcSetSvcTypeCpuUtil OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "percent"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of CPU used by this Service Type, expressed as a
            percentage of the total."
        ::= { jnxSpSvcSetSvcTypeEntry 7 }

    jnxSpSvcSetSvcTypeMemoryUsage64 OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service Type, expressed in
            bytes, represented by 64 bit integer."
        ::= { jnxSpSvcSetSvcTypeEntry 8 }

--
-- Per Interface information
--

    jnxSpSvcSetIfTable OBJECT-TYPE
        SYNTAX       SEQUENCE OF JnxSpSvcSetIfEntry
        MAX-ACCESS   not-accessible
        STATUS       current
        DESCRIPTION
            "Service Set data about each Service PIC on the router."
        ::= { jnxSpSvcSet 3 }

    jnxSpSvcSetIfEntry OBJECT-TYPE
        SYNTAX       JnxSpSvcSetIfEntry
        MAX-ACCESS   not-accessible
        STATUS       current
        DESCRIPTION
            "Each entry provides Service Set information about a single 
            Service PIC.  Each Service PIC is identified by its corresponding 
            ifIndex."
        INDEX   { ifIndex }
        ::= { jnxSpSvcSetIfTable 1 }

    JnxSpSvcSetIfEntry ::=
        SEQUENCE {
            jnxSpSvcSetIfTableName                   DisplayString,
            jnxSpSvcSetIfSvcSets                     Gauge32,
            jnxSpSvcSetIfMemoryUsage                 Gauge32,
            jnxSpSvcSetIfPctMemoryUsage              Gauge32,
            jnxSpSvcSetIfPolMemoryUsage              Gauge32,
            jnxSpSvcSetIfPctPolMemoryUsage           Gauge32,
            jnxSpSvcSetIfMemoryZone                  INTEGER,
            jnxSpSvcSetIfCpuUtil                     Gauge32,
            jnxSpSvcSetIfMemoryUsage64               CounterBasedGauge64,
            jnxSpSvcSetIfPolMemoryUsage64            CounterBasedGauge64,
            jnxSpSvcSetIfNumTotalSessActive          Integer32,
            jnxSpSvcSetIfPeakTotalSessActive         Integer32,
            jnxSpSvcSetIfNumCreatedSessPerSec        Integer32,
            jnxSpSvcSetIfNumDeletedSessPerSec        Integer32,
            jnxSpSvcSetIfNumTotalTcpSessActive       Integer32,
            jnxSpSvcSetIfNumTotalUdpSessActive       Integer32,
            jnxSpSvcSetIfNumTotalOtherSessActive     Integer32,
            jnxSpSvcSetIfPeakTotalTcpSessActive      Integer32,
            jnxSpSvcSetIfPeakTotalUdpSessActive      Integer32,
            jnxSpSvcSetIfPeakTotalOtherSessActive    Integer32,
            jnxSpSvcSetIfPeakCreatedSessPerSec       Integer32,
            jnxSpSvcSetIfPeakDeletedSessPerSec       Integer32,
            jnxSpSvcSetIfNumTotalTcpIpv4SessActive   Integer32,
            jnxSpSvcSetIfNumTotalTcpIpv6SessActive   Integer32,
            jnxSpSvcSetIfNumTotalUdpIpv4SessActive   Integer32,
            jnxSpSvcSetIfNumTotalUdpIpv6SessActive   Integer32,
            jnxSpSvcSetIfNumTotalOtherIpv4SessActive Integer32,
            jnxSpSvcSetIfNumTotalOtherIpv6SessActive Integer32,
            jnxSpSvcSetIfNumTotalTcpGatedSessActive  Integer32,
            jnxSpSvcSetIfNumTotalUdpGatedSessActive  Integer32,
            jnxSpSvcSetIfNumTotalTcpRegSessActive    Integer32,
            jnxSpSvcSetIfNumTotalUdpRegSessActive    Integer32,
            jnxSpSvcSetIfNumTotalTcpTunSessActive    Integer32,
            jnxSpSvcSetIfNumTotalUdpTunSessActive    Integer32,
            jnxSpSvcSetIfSessPktRecv                 CounterBasedGauge64,
            jnxSpSvcSetIfSessPktXmit                 CounterBasedGauge64,
            jnxSpSvcSetIfSessSlowPathDiscard         CounterBasedGauge64,
            jnxSpSvcSetIfSessSlowPathForward         CounterBasedGauge64,
            jnxSpSvcSetIfMspNumCreatedSubsPerSec     Integer32,
            jnxSpSvcSetIfMspNumDeletedSubsPerSec     Integer32,
            jnxSpSvcSetIfMspNumTotalSubsActive       Integer32,
            jnxSpSvcSetIfMspPeakCreatedSubsPerSec    Integer32,
            jnxSpSvcSetIfMspPeakDeletedSubsPerSec    Integer32,
            jnxSpSvcSetIfMspPeakTotalSubsActive      Integer32
        }

    jnxSpSvcSetIfTableName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The ifName of the interface identifying the Service PIC.  If more 
            than one interface is associated with the Service PIC, the name 
            associated with the lowest layer interface is used for this object."
        ::= { jnxSpSvcSetIfEntry 1 }

    jnxSpSvcSetIfSvcSets OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of Service Sets configured on this Service PIC."
        ::= { jnxSpSvcSetIfEntry 2 }

    jnxSpSvcSetIfMemoryUsage OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service PIC, expressed in
            bytes."
        ::= { jnxSpSvcSetIfEntry 3 }

    jnxSpSvcSetIfPctMemoryUsage OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "percent"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service PIC, expressed as a
            percentage of the total."
        ::= { jnxSpSvcSetIfEntry 4 }

    jnxSpSvcSetIfPolMemoryUsage OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of policy memory used by this Service PIC, expressed in
            bytes."
        ::= { jnxSpSvcSetIfEntry 5 }

    jnxSpSvcSetIfPctPolMemoryUsage OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "percent"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of policy memory used by this Service PIC, expressed
            as a percentage of the total."
        ::= { jnxSpSvcSetIfEntry 6 }

    jnxSpSvcSetIfMemoryZone OBJECT-TYPE
        SYNTAX      INTEGER {
                        green  (1),
                        yellow (2),
                        orange (3),
                        red    (4)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The memory-usage zone currently occupied by this Service PIC.  The 
            definitions of each zone are:

              Green  - All new flows are allowed.
              Yellow - Unused memory is reclaimed. All new flows are allowed.
              Orange - New flows are only allowed for service sets that are 
                       using less than their equal share of memory.
              Red    - No new flows are allowed."
        ::= { jnxSpSvcSetIfEntry 7 }


    jnxSpSvcSetIfCpuUtil OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "percent"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of CPU used by this Service PIC, expressed as a
            percentage of the total."
        ::= { jnxSpSvcSetIfEntry 8 }

    jnxSpSvcSetIfMemoryUsage64 OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of memory used by this Service PIC, expressed in
            bytes, represented by 64 bit integer."
        ::= { jnxSpSvcSetIfEntry 9 }

    jnxSpSvcSetIfPolMemoryUsage64 OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        UNITS       "bytes"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The amount of policy memory used by this Service PIC, expressed in
            bytes, represented by 64 bit integer."
        ::= { jnxSpSvcSetIfEntry 10 }

    jnxSpSvcSetIfNumTotalSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 11 }

    jnxSpSvcSetIfPeakTotalSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The Peak number of sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 12 }

    jnxSpSvcSetIfNumCreatedSessPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of sessions created per second in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 13 }

    jnxSpSvcSetIfNumDeletedSessPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of sessions deleted per second in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 14 }

    jnxSpSvcSetIfNumTotalTcpSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of TCP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 15 }

    jnxSpSvcSetIfNumTotalUdpSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of UDP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 16 }

    jnxSpSvcSetIfNumTotalOtherSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of other (not TCP or UDP) sessions active
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 17 }

    jnxSpSvcSetIfPeakTotalTcpSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak number of TCP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 18 }

    jnxSpSvcSetIfPeakTotalUdpSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak number of UDP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 19 }

    jnxSpSvcSetIfPeakTotalOtherSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak number of other (not TCP or UDP) sessions active
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 20 }

    jnxSpSvcSetIfPeakCreatedSessPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak number of sessions created per second in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 21 }

    jnxSpSvcSetIfPeakDeletedSessPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak number of sessions deleted per second in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 22 }

    jnxSpSvcSetIfNumTotalTcpIpv4SessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of IPv4 TCP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 23 }

    jnxSpSvcSetIfNumTotalTcpIpv6SessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of IPv6 TCP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 24 }

    jnxSpSvcSetIfNumTotalUdpIpv4SessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of IPv4 UDP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 25 }

    jnxSpSvcSetIfNumTotalUdpIpv6SessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of IPv6 UDP sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 26 }

    jnxSpSvcSetIfNumTotalOtherIpv4SessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of other IPv4 sessions (not TCP or UDP) active
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 27 }

    jnxSpSvcSetIfNumTotalOtherIpv6SessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of other IPv6 sessions (not TCP or UDP) active
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 28 }

    jnxSpSvcSetIfNumTotalTcpGatedSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of TCP gated sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 29 }

    jnxSpSvcSetIfNumTotalUdpGatedSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of UDP gated sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 30 }

    jnxSpSvcSetIfNumTotalTcpRegSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of TCP regular sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 31 }

    jnxSpSvcSetIfNumTotalUdpRegSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of UDP regular sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 32 }

    jnxSpSvcSetIfNumTotalTcpTunSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of TCP tunneled sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 33 }

    jnxSpSvcSetIfNumTotalUdpTunSessActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of UDP tunneled sessions active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 34 }

    jnxSpSvcSetIfSessPktRecv OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of session packets received in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 35 }


    jnxSpSvcSetIfSessPktXmit OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of session packets transmitted in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 36 }

    jnxSpSvcSetIfSessSlowPathDiscard OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of session packets discarded in the slow path
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 37 }

    jnxSpSvcSetIfSessSlowPathForward OBJECT-TYPE
        SYNTAX      CounterBasedGauge64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of session packets forwarded in the slow path
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 38 }

    jnxSpSvcSetIfMspNumCreatedSubsPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of subscribers created per second
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 39 }

    jnxSpSvcSetIfMspNumDeletedSubsPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of subscribers deleted per second
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 40 }

    jnxSpSvcSetIfMspNumTotalSubsActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The total number of subscribers active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 41 }

    jnxSpSvcSetIfMspPeakCreatedSubsPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak number of subscribers created per second
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 42 }

    jnxSpSvcSetIfMspPeakDeletedSubsPerSec OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak number of subscribers deleted per second
             in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 43 }

    jnxSpSvcSetIfMspPeakTotalSubsActive OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The peak total number of subscribers active in the Service PIC."
        ::= { jnxSpSvcSetIfEntry 44 }


--
--
-- Service PIC Notification definitions
--
    jnxSpNotificationPrefix OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "All collector notifications are registered under this branch."
        ::= { jnxSpNotifications 0 }


    jnxSpSvcSetZoneEntered NOTIFICATION-TYPE
        OBJECTS { jnxSpSvcSetIfMemoryZone,
                  jnxSpSvcSetIfTableName }
        STATUS  current
        DESCRIPTION 
            "This indicates a Service PIC has entered a more severe memory-usage
            zone from a less severe memory usage zone.  The zone entered
            is identified by jnxSpSvcSetIfMemoryZone."
        ::= { jnxSpNotificationPrefix 1 }

    jnxSpSvcSetZoneExited NOTIFICATION-TYPE
        OBJECTS { jnxSpSvcSetIfMemoryZone,
                  jnxSpSvcSetIfTableName }
        STATUS  current
        DESCRIPTION
            "This indicates a Service Pic has exited a more severe memory-usage
            zone to a less severe memory usage zone.  The zone exited is
            identified by jnxSpSvcSetIfMemoryZone."
        ::= { jnxSpNotificationPrefix 2 }

    jnxSpSvcSetCpuExceeded NOTIFICATION-TYPE
        OBJECTS { jnxSpSvcSetIfCpuUtil,
                  jnxSpSvcSetIfTableName }
        STATUS  current
        DESCRIPTION
            "This indicates a Service Pic has exceeded its internal threshold
            for CPU utilization (85%)."
        ::= { jnxSpNotificationPrefix 3 }

    jnxSpSvcSetCpuOk NOTIFICATION-TYPE
        OBJECTS { jnxSpSvcSetIfCpuUtil,
                  jnxSpSvcSetIfTableName }
        STATUS  current
        DESCRIPTION
            "This indicates a Service Pic has crossed below its internal 
            threshold for CPU utilization (85%)."
        ::= { jnxSpNotificationPrefix 4 }
      
    jnxSpSvcSetFlowLimitUtil OBJECT-TYPE
        SYNTAX      Integer32 (0..100)
        MAX-ACCESS  accessible-for-notify
       STATUS      current
        DESCRIPTION
            "The Total no of flows present in this Service Set, expressed as a
            percentage of the total maximum flows."
        ::= { jnxFlowLimitTrapVars 1 }

   jnxSpSvcSetNameUtil OBJECT-TYPE
        SYNTAX        DisplayString (SIZE(1..96))
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
            "The Service Set name."
        ::= { jnxFlowLimitTrapVars 2 }

   jnxSpTcpLoggingHostIpaddr OBJECT-TYPE
        SYNTAX        DisplayString (SIZE(1..64))
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
            "The TCP logging remote host server IP address."
        ::= { jnxTcpLoggingTrapVars 1 }

   jnxSpTcpLoggingHostPort OBJECT-TYPE
        SYNTAX        Integer32 (0..65535)
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
            "The TCP logging remote host server port number."
        ::= { jnxTcpLoggingTrapVars 2 }

   jnxSpTcpLoggingHostConnStatus OBJECT-TYPE
        SYNTAX        DisplayString (SIZE(1..64))
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
            "The TCP logging remote host server connection status.
             Status can be either <connection established> or 
             <connection dropped>"
        ::= { jnxTcpLoggingTrapVars 3 }

   jnxSpTcpLoggingHostRoutingInstance OBJECT-TYPE
        SYNTAX        DisplayString (SIZE(1..64))
        MAX-ACCESS    accessible-for-notify
        STATUS        current
        DESCRIPTION
            "The routing instance name under which TCP logging 
            remote host server is configured."
        ::= { jnxTcpLoggingTrapVars 4 }

   jnxSpSvcSetFlowLimitUtilized NOTIFICATION-TYPE
        OBJECTS {jnxSpSvcSetFlowLimitUtil,
                 jnxSpSvcSetNameUtil}
        STATUS  deprecated
        DESCRIPTION
            "This indicates a Service Set has reached its upper limit of flows
            threshold of a maximun flows allowed for a service set."
        ::= { jnxSpNotifications 1 }

   jnxSpSvcSetFlowLimitUtilised NOTIFICATION-TYPE
        OBJECTS {jnxSpSvcSetFlowLimitUtil,
                 jnxSpSvcSetNameUtil}
        STATUS  current
        DESCRIPTION
            "This indicates a Service Set has reached its upper limit of flows
            threshold of a maximun flows allowed for a service set."
        ::= { jnxSpNotificationPrefix 5 }

   jnxSpTcpLoggingHostStatus NOTIFICATION-TYPE
        OBJECTS {jnxSpTcpLoggingHostIpaddr,
                 jnxSpTcpLoggingHostPort,
                 jnxSpTcpLoggingHostConnStatus,
                 jnxSpTcpLoggingHostRoutingInstance}
        STATUS  current
        DESCRIPTION
            "This provides TCP logging host connection status for Service Set."
        ::= { jnxSpNotificationPrefix 6 }

END
