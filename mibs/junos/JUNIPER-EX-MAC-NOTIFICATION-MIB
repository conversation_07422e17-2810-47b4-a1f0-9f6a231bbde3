-- *****************************************************************
-- Juniper Enterprise Specific MIB: EX-MAC-NOTIFICATION 
--
-- Copyright (c) 2008-2010, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
-- *****************************************************************

JUNIPER-EX-MAC-NOTIFICATION-MIB DEFINITIONS ::= BEGIN

IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, Counter64, NOTIFICATION-TYPE,
        Unsigned32, TimeTicks, OBJECT-IDENTITY
                FROM SNMPv2-SMI
        TruthValue
                FROM SNMPv2-TC
        jnxMacNotificationRoot
                FROM JUNIPER-EX-SMI;

jnxMacNotificationMIB MODULE-IDENTITY
    LAST-UPDATED "201004280000Z"
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "Juniper Technical Assistance Center
         Juniper Networks, Inc.
         1133 Innovation Way
         Sunnyvale, CA 94089
         E-mail: <EMAIL>"

    DESCRIPTION
        "This MIB module is for configuration of the MAC notification
        feature. MAC notification is a mechanism to inform monitoring
        devices when there are MAC addresses learnt or removed from
        the forwarding database of the monitored devices." 

    --revision history
    REVISION  "200901200000Z"
    DESCRIPTION
            "Initial revision."    

    REVISION  "200905270000Z"
    DESCRIPTION
            "Updated the description of the Objects."
    
    REVISION  "201002090000Z"
    DESCRIPTION
            "Updated the description of jnxHistMacChangedMsg. When next hop gets
            changed the port information gets updated and is logged."

    REVISION  "201004280000Z"
    DESCRIPTION
            "Added new scalar jnxMacAddressesUpdated which increments for each
            mac address updated."

    ::= { jnxMacNotificationRoot 1 }

-- Juniper MAC Notification MIB object definitions

jnxMacNotificationMIBObjects OBJECT IDENTIFIER
    ::= { jnxMacNotificationMIB 1 }

jnxMacNotificationMIBGlobalObjects     OBJECT IDENTIFIER
    ::= { jnxMacNotificationMIBObjects 1 }

--**********************************************************************
-- Juniper Mac Notification Global Group
--**********************************************************************

jnxMacGlobalFeatureEnabled  OBJECT-TYPE
    SYNTAX        TruthValue
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Indicates whether the MAC notification feature is currently 
        running in the device.

        Setting this object to false(2) disables the MAC notification
        feature globally.

        Setting this object to true(1) will start the MAC notification
        feature running in the device. If the feature is already
        running, setting to true(1) has no effect. "
    ::= { jnxMacNotificationMIBGlobalObjects 1 }

jnxMacNotificationInterval  OBJECT-TYPE
    SYNTAX        Unsigned32 (0..4294967295)
    UNITS         "seconds"
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This object specifies the maximum interval of time between
        jnxMacChangedNotifications being generated by the device.
        If the value of jnxNotificationsEnabled is true(1), the
        device will send out the generated jnxMacChangedNotifications
        and archive the MAC change notification events in the
        jnxMacHistoryTable. If the value of jnxNotificationEnabled is
        false(2), the device will not send out the generated
        jnxMacChangedNotifications but it will archive these events
        in the jnxMacHistoryTable.   
        
        If the value of this object is equal to 0, the device will 
        generate jnxMacChangedNotifications and archive the MAC 
        change notification events in the jnxMacHistoryTable as soon as
        there is MAC address learnt or removed by the device.

        If the value of this object is greater than 0, the device will
        wait for a period of time equal to the value of this object
        before generate the jnxMacChangedNotifications and archive
        the MAC change notification events in the jnxMacHistoryTable."
    DEFVAL { 30 }
    ::= { jnxMacNotificationMIBGlobalObjects 2 }

jnxMacAddressesLearnt OBJECT-TYPE
    SYNTAX        Counter64 
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Indicates the number of MAC addresses learnt by the
        device." 
    ::= { jnxMacNotificationMIBGlobalObjects 3 }

jnxMacAddressesRemoved OBJECT-TYPE
    SYNTAX        Counter64 
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Indicates the number of MAC addresses removed from the
        forwarding database." 
    ::= { jnxMacNotificationMIBGlobalObjects 4 }

jnxMacNotificationsEnabled OBJECT-TYPE
     SYNTAX          TruthValue
     MAX-ACCESS      read-only
     STATUS          current
     DESCRIPTION
         "Indicates whether jnxMacChangedNotification notifications
          will or will not be sent when there are MAC addresses
          learnt or removed from the device's forwarding database. 
          Disabling notifications does not prevent the MAC address
          info from being added to the jnxMacHistoryTable."
     DEFVAL { false }
     ::= { jnxMacNotificationMIBGlobalObjects 5 }

jnxMacNotificationsSent OBJECT-TYPE
    SYNTAX         Counter64
    MAX-ACCESS     read-only
    STATUS         current
    DESCRIPTION
        "Indicates the number of jnxMacChangedNotifications sent out
        by the device."
    ::= { jnxMacNotificationMIBGlobalObjects 6 }

jnxMacHistTableMaxLength OBJECT-TYPE
    SYNTAX          Unsigned32 (0..500)
    UNITS           "entries"
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The upper limit on the number of entries that the
        jnxMacHistoryTable may contain.  A value of 0 will
        prevent any history from being retained. When this
        table is full, the oldest entry will be deleted and
        a new one will be created."
    DEFVAL  { 256 }
    ::= { jnxMacNotificationMIBGlobalObjects 7 }

jnxMacHistoryTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF JnxMacHistoryEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "This table will archive the MAC change notification events 
        generated by this device. The MAC change notification
        events are archived here even if jnxMacChangesNotifications 
        are not actually sent."
    ::= { jnxMacNotificationMIBGlobalObjects 8 }

jnxMacHistoryEntry OBJECT-TYPE
    SYNTAX     JnxMacHistoryEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "A MAC change notification message that was previously
        generated by this device.  Each entry is indexed by a message
        index."
    INDEX   { jnxHistIndex }
    ::= { jnxMacHistoryTable 1 }

JnxMacHistoryEntry ::=
    SEQUENCE {
        jnxHistIndex
                Unsigned32,
        jnxHistMacChangedMsg
                OCTET STRING,
        jnxHistTimestamp
                TimeTicks
    }

jnxHistIndex OBJECT-TYPE
    SYNTAX          Unsigned32 (1..4294967295)
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "An index that uniquely identifies a MAC change notification
        event previously generated by the device. This index starts at
        1 and increases by one when a MAC change notification is 
        generated.  When it reaches the maximum value, the agent wraps
        the value back to 1."
    ::= { jnxMacHistoryEntry 1 }

jnxHistMacChangedMsg OBJECT-TYPE
    SYNTAX        OCTET STRING (SIZE(1..512))
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This object contains the information of a MAC change
        notification event. It consists of several tuples packed
        together in the format of '<tuple1><tuple2>...'.
        
        Each tuple consist of 13 octets in the format of
        '<operation><VLAN><MAC><dot1dBasePort>' where 

        <operation> is of size 1 octet and supports the following values
          0 - End of MIB object.
          1 - MAC learnt.
          2 - MAC removed.
          3 - MAC updated.

        <VLAN> is VLAN number of the VLAN which the MAC address is
        belonged to and has size of 2 octet.

        <MAC> is the Layer2 Mac Address and has size of 6 octets.

        <dot1dBasePort> is the value of dot1dBasePort for the
        interface from which the MAC address is learnt and has size
        of 4 octets."
    ::= { jnxMacHistoryEntry 2 }

jnxHistTimestamp  OBJECT-TYPE
    SYNTAX          TimeTicks
    MAX-ACCESS      read-only
    STATUS          current
    DESCRIPTION
        "The value of sysUpTime when the jnxMacChangedNotification
        containing the information denoted by the jnxHistMacChangedMsg
        object in this entry was generated."
    ::= { jnxMacHistoryEntry 3 }

jnxMacAddressesUpdated OBJECT-TYPE
    SYNTAX        Counter64
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Indicates the number of MAC addresses updated by the
        device."
    ::= { jnxMacNotificationMIBGlobalObjects 9 }

-- ***********************************************************

-- Mac NOTIFICATIONS

-- ***********************************************************

jnxMacNotifications  OBJECT IDENTIFIER  ::= { jnxMacNotificationRoot 2 }

jnxMacNotificationsPrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "All Mac Notifications are registered
        under this branch."
    ::= { jnxMacNotifications 0 }

jnxMacChangedNotification NOTIFICATION-TYPE
    OBJECTS { jnxHistMacChangedMsg, jnxHistTimestamp } 
    STATUS  current
    DESCRIPTION
        "This notification is generated when there is enough MAC
        address information to fully occupy a maximum size SNMP trap
        message. This notification is also generated when there
        is at least one MAC address changed or removed and the amount
        of time elapsed from the previous notification is greater
        than the maximum wait time denoted by
        jnxNotificationInterval object.

        If there are more MAC addresses information than can fit into
        one cmmHistTrapContent object, then multiple notifications
        will be generated."

    ::= { jnxMacNotificationsPrefix 1 }

END
