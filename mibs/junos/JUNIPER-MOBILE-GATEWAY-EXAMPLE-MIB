--
-- Juniper Mobile Gateway EXAMPLE objects MIB.
--
-- Copyright (c) 2010-2013, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-MOBILE-GATEWAY-EXAMPLE-MIB DEFINITIONS ::= <PERSON><PERSON>IN

IMPORTS
    Counter64, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ger32, Counter<PERSON>, Unsigned32,
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE
        FROM SNMPv2-SMI

    TEXTUAL-CONVENTION, DisplayString, RowStatus, TruthValue
        FROM SNMPv2-TC

    Ipv6AddressPrefix, Ipv6AddressIfIdentifier, Ipv6Address
        FROM IPV6-TC

    InetAddressType, InetAddress, InetPortNumber, InetAddressPrefixLength
        FROM INET-ADDRESS-MIB

    EnabledStatus
        FROM JUNIPER-MIMSTP-MIB

    jnxExampleMibRoot
        FROM JUNIPER-EXPERIMENT-MIB;

jnxMobileGatewayExampleMib MODULE-<PERSON>ENTITY
    LAST-UPDATED "201011221200Z" -- Nov 22, 2010, 12:00:00 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "Juniper Technical Assistance Center
         Juniper Networks, Inc.
         1133 Innovation Way
         Sunnyvale, CA 94089
         E-mail: <EMAIL>"
    DESCRIPTION
        "This module defines some sample objects pertaining to Mobile-Edge Services."
    REVISION "201011221200Z" -- Nov 22, 2010, 12:00:00
    DESCRIPTION "Initial version"

    ::= { jnxExampleMibRoot 2 }


jnxMobileGatewayExampleObjects       OBJECT IDENTIFIER ::= 
                                                { jnxMobileGatewayExampleMib 1 }
jnxMobileGatewayExampleNotifications OBJECT IDENTIFIER ::=
                                                { jnxMobileGatewayExampleMib 2 }

jnxMobileGatewayExampleSyncStats OBJECT IDENTIFIER ::= 
                                            { jnxMobileGatewayExampleObjects 1 }
jnxMobileGatewayExampleAsyncStats OBJECT IDENTIFIER ::= 
                                            { jnxMobileGatewayExampleObjects 2 }

jnxMobileGatewayProfileTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF JnxMobileGatewayProfileEntry
    MAX-ACCESS  not-accessible 
    STATUS      current
    DESCRIPTION
        "The table listing Mobile Gateway Test Profiles, key is Profile Name."
    ::= { jnxMobileGatewayExampleObjects 3 }

jnxMobileGatewayExampleNotificationVars OBJECT IDENTIFIER ::=
                                            { jnxMobileGatewayExampleObjects 4 }


--
-- Global counters that are returned by mobiled test module in a synchronous manner
--

jnxMobileGatewayTotalRequests OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total requests made."
    ::= { jnxMobileGatewayExampleSyncStats 1 }

jnxMobileGatewayTotalAccepts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total requests that were accepted."
    ::= { jnxMobileGatewayExampleSyncStats 2 }

jnxMobileGatewayTotalRejects OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total requests that were rejected."
    ::= { jnxMobileGatewayExampleSyncStats 3 }

jnxMobileGatewayTotalChallenges OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total challenges received."
    ::= { jnxMobileGatewayExampleSyncStats 4 }


--
-- Global counters that are returned by mobiled test module in an async manner.
-- test module queries Service PICs and aggregates their responses before sending
-- data to snmp.
--

jnxMobileGatewayTotalRequestTimeouts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total  requests that timed out."
    ::= { jnxMobileGatewayExampleAsyncStats 1 }

jnxMobileGatewayTotalRequestTxErrors OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total  requests transmit errors."
    ::= { jnxMobileGatewayExampleAsyncStats 2 }

jnxMobileGatewayTotalResponseErrors OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total  response errors."
    ::= { jnxMobileGatewayExampleAsyncStats 3 }

jnxMobileGatewayTotalPendingRequests OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total pending requests."
    ::= { jnxMobileGatewayExampleAsyncStats 4 }

--
-- Profile Table
-- This table contains the attributes of mobile-gateway test profiles.
-- mobiled test module queries the service PICS for this information and 
-- conveys this data to snmp in an asynchronous manner
--
jnxMobileGatewayProfileEntry OBJECT-TYPE
    SYNTAX      JnxMobileGatewayProfileEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry representing a Mobile Gateway Test Profile."
    INDEX       { jnxMobileGatewayProfileName }
    ::= { jnxMobileGatewayProfileTable 1 }

JnxMobileGatewayProfileEntry ::= SEQUENCE {
    jnxMobileGatewayProfileName              DisplayString,
    jnxMobileGatewayProfileDescription       DisplayString,
    jnxMobileGatewayProfileType              Integer32 
}


jnxMobileGatewayProfileName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A string that uniquely identifies the Test Profile."
    ::= { jnxMobileGatewayProfileEntry 1 }

jnxMobileGatewayProfileDescription OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A string that describes the Test Profile."
    ::= { jnxMobileGatewayProfileEntry 2 }

jnxMobileGatewayProfileType OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Test Profile Type."
    ::= { jnxMobileGatewayProfileEntry 3 }

--
-- Objects used in EXAMPLE Notifications
--
jnxMobileGatewayExampleServerName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name identifies an external server (charging,AAA,etc) on mobile-gateway."
    ::= { jnxMobileGatewayExampleNotificationVars 1 }

jnxMobileGatewayExampleServicePicName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This identifies the session-pic, in the form ms-a/b/0, where
        <a> is the slot and <b> could be either 0 or 1."
    ::= { jnxMobileGatewayExampleNotificationVars 2 }

jnxMobileGatewayExampleServerState OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This indicates whether the server status is Up or Down"
    ::= { jnxMobileGatewayExampleNotificationVars 3 }

--
-- EXAMPLE MIB Notifications
--

jnxMobileGatewayExampleServerStatus NOTIFICATION-TYPE
    OBJECTS     { jnxMobileGatewayExampleServerName, jnxMobileGatewayExampleServicePicName, jnxMobileGatewayExampleServerState }
    STATUS      current
    DESCRIPTION
        "This notification signifies that the specified server has 
        changed state. The ServerName identifies the server, the 
        ServicePicName identifies the session-pic that originated this
        notification and ServerState indicates whether server came up or went down." 
    ::= { jnxMobileGatewayExampleNotifications 1 }

-- End of JUNIPER Mobile Gateway EXAMPLE MIB

END
