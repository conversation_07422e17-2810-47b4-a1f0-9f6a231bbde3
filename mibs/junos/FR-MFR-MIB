FR-MFR-MIB DEFINITIONS ::= BEGIN

   IMPORTS
      MODULE-IDENTITY, OBJECT-TYPE, Integer32, Counter32,
         NOTIFICATION-TYPE, transmission
         FROM SNMPv2-SMI
      TEXTUAL-CONVENTION, TestAndIncr, RowStatus
         FROM SNMPv2-TC
      MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
         FROM SNMPv2-CONF
      SnmpAdminString
         FROM SNMP-FRAMEWORK-MIB
      InterfaceIndex, ifIndex
         FROM IF-MIB;

   mfrMib MODULE-IDENTITY
      LAST-UPDATED "200011300000Z"
      ORGANIZATION "IETF Frame Relay Service MIB (frnetmib)
                    Working Group"
      CONTACT-INFO
        "WG Charter:
             http://www.ietf.org/html.charters/frnetmib-charter.html
         WG-email:      <EMAIL>
         Subscribe:     <EMAIL>
         Email Archive: ftp://ftp.ietf.org/ietf-mail-archive/frnetmib

         Chair:      <PERSON> Networks
         Email:      Andy.<PERSON>@vivacenetworks.com

         WG editor:  Prayson Pate
                     Overture Networks
         Email:      <EMAIL>

         Co-author:  Bob Lynch
                     Overture Networks
         EMail:      <EMAIL>

         Co-author:  Kenneth Rehbehn
                     Megisto Systems, Inc.
         EMail:      <EMAIL>"

      DESCRIPTION
         "This is the MIB used to control and monitor the multilink
          frame relay (MFR) function described in FRF.16."

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Revision History
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

      REVISION "200011300000Z"
      DESCRIPTION
          "Published as RFC 3020."

      ::= { transmission 47 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Textual Conventions
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

   MfrBundleLinkState ::= TEXTUAL-CONVENTION
      STATUS      current
      DESCRIPTION
         "The possible states for a bundle link, as defined in
          Annex A of FRF.16."
      REFERENCE "FRF.16 Annex A"
      SYNTAX  INTEGER {
         mfrBundleLinkStateAddSent       (1),
         mfrBundleLinkStateAddRx         (2),
         mfrBundleLinkStateAddAckRx      (3),
         mfrBundleLinkStateUp            (4),
         mfrBundleLinkStateIdlePending   (5),
         mfrBundleLinkStateIdle          (6),
         mfrBundleLinkStateDown          (7),
         mfrBundleLinkStateDownIdle      (8)
         }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Object Identifiers
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

   mfrMibScalarObjects     OBJECT IDENTIFIER ::= { mfrMib 1 }
   mfrMibBundleObjects     OBJECT IDENTIFIER ::= { mfrMib 2 }
   mfrMibBundleLinkObjects OBJECT IDENTIFIER ::= { mfrMib 3 }
   mfrMibTraps             OBJECT IDENTIFIER ::= { mfrMib 4 }
   mfrMibConformance       OBJECT IDENTIFIER ::= { mfrMib 5 }

   mfrMibTrapsPrefix       OBJECT IDENTIFIER ::= { mfrMibTraps 0 }

   mfrMibGroups      OBJECT IDENTIFIER ::= { mfrMibConformance 1 }
   mfrMibCompliances OBJECT IDENTIFIER ::= { mfrMibConformance 2 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Scalars
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

   mfrBundleMaxNumBundles OBJECT-TYPE
      SYNTAX  Integer32
      MAX-ACCESS read-only
      STATUS  current
      DESCRIPTION
         "This object is used to inform the manager of the
          maximum number of bundles supported by this device."
      ::= { mfrMibScalarObjects 1 }

   mfrBundleNextIndex OBJECT-TYPE
      SYNTAX  TestAndIncr
      MAX-ACCESS read-write
      STATUS  current
      DESCRIPTION
         "This object is used to assist the manager in
          selecting a value for mfrBundleIndex during row creation
          in the mfrBundleTable.  It can also be used to avoid race
          conditions with multiple managers trying to create
          rows in the table (see RFC 2494 [RFC2494] for one such
          alogrithm)."
      REFERENCE "RFC 2494"
      ::= { mfrMibScalarObjects 2 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Bundle Table
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   mfrBundleTable OBJECT-TYPE
      SYNTAX  SEQUENCE OF MfrBundleEntry
      MAX-ACCESS  not-accessible
      STATUS  current
      DESCRIPTION
         "The bundle configuration and status table.  There
          is a one-to-one correspondence between a bundle
          and an interface represented in the ifTable.

          The following objects of the ifTable have specific
          meaning for an MFR bundle:
             ifAdminStatus  - the bundle admin status
             ifOperStatus   - the bundle operational status
             ifSpeed        - the current bandwidth of the bundle
             ifInUcastPkts  - the number of frames received
                              on the bundle
             ifOutUcastPkts - the number of frames transmitted
                              on the bundle
             ifInErrors     - frame (not fragment) errors
             ifOutErrors    - frame (not fragment) errors
             "
      ::= { mfrMibBundleObjects 3 }

   mfrBundleEntry OBJECT-TYPE
      SYNTAX  MfrBundleEntry
      MAX-ACCESS  not-accessible
      STATUS  current
      DESCRIPTION
         "An entry in the bundle table."
      INDEX   { mfrBundleIndex }
      ::= { mfrBundleTable 1 }

   MfrBundleEntry ::=
      SEQUENCE {
         mfrBundleIndex
            Integer32,
         mfrBundleIfIndex
            InterfaceIndex,
         mfrBundleRowStatus
            RowStatus,
         mfrBundleNearEndName
            SnmpAdminString,
         mfrBundleFragmentation
            INTEGER,
         mfrBundleMaxFragSize
            Integer32,
         mfrBundleTimerHello
            INTEGER,
         mfrBundleTimerAck
            INTEGER,
         mfrBundleCountMaxRetry
            INTEGER,
         mfrBundleActivationClass
            INTEGER,
         mfrBundleThreshold
            Integer32,
         mfrBundleMaxDiffDelay
            Integer32,
         mfrBundleSeqNumSize
            INTEGER,
         mfrBundleMaxBundleLinks
            Integer32,
         mfrBundleLinksConfigured
            Integer32,
         mfrBundleLinksActive
            Integer32,
         mfrBundleBandwidth
            Integer32,
         mfrBundleFarEndName
            SnmpAdminString,
         mfrBundleResequencingErrors
            Counter32
         }

   mfrBundleIndex OBJECT-TYPE
      SYNTAX  Integer32 (1..2147483647)
      MAX-ACCESS  not-accessible
      STATUS  current
      DESCRIPTION
         "The index into the table.  While this corresponds
          to an entry in the ifTable, the value of mfrBundleIndex
          need not match that of the ifIndex in the ifTable.
          A manager can use mfrBundleNextIndex to select a unique
          mfrBundleIndex for creating a new row."
      ::= { mfrBundleEntry 1 }

   mfrBundleIfIndex OBJECT-TYPE
      SYNTAX  InterfaceIndex
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The value must match an entry in the interface
          table whose ifType must be set to frf16MfrBundle(163).

          For example: if the value of mfrBundleIfIndex is 10,
          then a corresponding entry should be present in
          the ifTable with an index of 10 and an ifType of 163."
      ::= { mfrBundleEntry 2 }

   mfrBundleRowStatus OBJECT-TYPE
      SYNTAX  RowStatus
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
          "The mfrBundleRowStatus object allows create, change,
           and delete operations on bundle entries."
      REFERENCE "RFC 1903"
      ::= { mfrBundleEntry 3 }

   mfrBundleNearEndName OBJECT-TYPE
      SYNTAX  SnmpAdminString
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The configured name of the bundle."
      REFERENCE "FRF.16 section 3.4.1"
      ::= { mfrBundleEntry 4 }

   mfrBundleFragmentation OBJECT-TYPE
      SYNTAX  INTEGER {
         enable  (1),
         disable (2)
         }
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "Controls whether the bundle performs/accepts
          fragmentation and re-assembly.  The possible
          values are:

          enable(1) - Bundle links will fragment frames

          disable(2) - Bundle links will not fragment
                      frames."
      DEFVAL { disable }
      ::= { mfrBundleEntry 5 }

   mfrBundleMaxFragSize OBJECT-TYPE
      SYNTAX  Integer32 (-1..8184)
      UNITS "Octets"
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The maximum fragment size supported.  Note that this
          is only valid if mfrBundleFragmentation is set to enable(1).

          Zero is not a valid fragment size.

          A bundle that does not support fragmentation must return
          this object with a value of -1."
      DEFVAL { -1 }
      ::= { mfrBundleEntry 6 }

   mfrBundleTimerHello OBJECT-TYPE
      SYNTAX  INTEGER (1..180)
      UNITS "Seconds"
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The configured MFR Hello Timer value."
      REFERENCE "FRF.16 section *******"
      DEFVAL { 10 }
      ::= { mfrBundleEntry 7 }

   mfrBundleTimerAck OBJECT-TYPE
      SYNTAX  INTEGER (1..10)
      UNITS "Seconds"
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The configured MFR T_ACK value."
      REFERENCE "FRF.16 section *******"
      DEFVAL { 4 }
      ::= { mfrBundleEntry 8 }

   mfrBundleCountMaxRetry OBJECT-TYPE
      SYNTAX  INTEGER (1..5)
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The MFR N_MAX_RETRY value."
      REFERENCE "FRF.16 section *******"
      DEFVAL { 2 }
      ::= { mfrBundleEntry 9 }

   mfrBundleActivationClass OBJECT-TYPE
      SYNTAX  INTEGER {
         mfrBundleActivationClassA (1),
         mfrBundleActivationClassB (2),
         mfrBundleActivationClassC (3),
         mfrBundleActivationClassD (4)
         }

      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "Controls the conditions under which the bundle is activated.
          The following settings are available:

             mfrBundleActivationClassA(1) - at least one must link up
             mfrBundleActivationClassB(2) - all links must be up
             mfrBundleActivationClassC(3) - a certain number must be
                                            up.  Refer to
                                            mfrBundleThreshold for
                                            the required number.
             mfrBundleActivationClassD(4) - custom (implementation
                                            specific)."
      REFERENCE "FRF.16 section *******"
      DEFVAL { mfrBundleActivationClassA }
      ::= { mfrBundleEntry 10 }

   mfrBundleThreshold OBJECT-TYPE
      SYNTAX  Integer32 (-1..2147483647)
      UNITS "Bundle Links"
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "Specifies the number of links that must be in operational
          'up' state before the bundle will transition to an
          operational up/active state.  If the number of
          operational 'up' links falls below this value,
          then the bundle will transition to an inactive
          state.

          Note - this is only valid when mfrBundleActivationClass
          is set to mfrBundleActivationClassC or, depending upon the
          implementation, to mfrBundleActivationClassD.  A bundle that
          is not set to one of these must return this object with a
          value of -1."
      REFERENCE "FRF.16 section *******"
      DEFVAL { -1 }
      ::= { mfrBundleEntry 11 }

   mfrBundleMaxDiffDelay OBJECT-TYPE
      SYNTAX  Integer32 (-1..2147483647)
      UNITS "Milliseconds"
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The maximum delay difference between the bundle
          links.

          A value of -1 indicates that this object does not contain
          a valid value"
      DEFVAL { -1 }
      ::= { mfrBundleEntry 12 }

   mfrBundleSeqNumSize OBJECT-TYPE
      SYNTAX  INTEGER {
          seqNumSize12bit (1),
          seqNumSize24bit (2)
          }
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "Controls whether the standard FRF.12 12-bit
          sequence number is used or the optional 24-bit
          sequence number."
      REFERENCE "FRFTC/99-194"
      DEFVAL { seqNumSize12bit }
      ::= { mfrBundleEntry 13 }

   mfrBundleMaxBundleLinks OBJECT-TYPE
      SYNTAX  Integer32 (1..2147483647)
      UNITS "Bundle Links"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The maximum number of bundle links supported for
          this bundle."
      ::= { mfrBundleEntry 14 }

   mfrBundleLinksConfigured OBJECT-TYPE
      SYNTAX  Integer32 (1..2147483647)
      UNITS "Bundle Links"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The number of links configured for the bundle."
      ::= {  mfrBundleEntry 15 }

   mfrBundleLinksActive OBJECT-TYPE
      SYNTAX  Integer32 (-1..2147483647)
      UNITS "Bundle Links"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The number of links that are active."
      ::= {  mfrBundleEntry 16 }

   mfrBundleBandwidth OBJECT-TYPE
       SYNTAX  Integer32
       UNITS "Bits/Sec"
       MAX-ACCESS  read-only
       STATUS  current
       DESCRIPTION
          "The amount of available bandwidth on the bundle"
       ::= {  mfrBundleEntry 17 }

   mfrBundleFarEndName OBJECT-TYPE
      SYNTAX  SnmpAdminString
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Name of the bundle received from the far end."
      REFERENCE "FRF.16 section 3.4.1"
      ::= { mfrBundleEntry 18 }

   mfrBundleResequencingErrors OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Error Events"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "A count of the number of resequencing errors.  Each event
          may correspond to multiple lost frames.  Example:
          Say sequence number 56, 59 and 60 is received for DLCI 100.
          It is decided by some means that sequence 57 and 58 is lost.
          This counter should then be incremented by ONE, even though
          two frames were lost."
      ::= { mfrBundleEntry 19 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- ifIndex Mapping to Bundle Index Table
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

   mfrBundleIfIndexMappingTable OBJECT-TYPE
      SYNTAX  SEQUENCE OF MfrBundleIfIndexMappingEntry
      MAX-ACCESS  not-accessible
      STATUS  current
      DESCRIPTION
          "A table mapping the values of ifIndex to the
           mfrBundleIndex.  This is required in order to find
           the mfrBundleIndex given an ifIndex.  The mapping of
           mfrBundleIndex to ifIndex is provided by the
           mfrBundleIfIndex entry in the mfrBundleTable."
      ::= { mfrMibBundleObjects 4 }

   mfrBundleIfIndexMappingEntry OBJECT-TYPE
      SYNTAX  MfrBundleIfIndexMappingEntry
      MAX-ACCESS  not-accessible
      STATUS  current
      DESCRIPTION
           "Each row describes one ifIndex to mfrBundleIndex mapping."
    INDEX   { ifIndex }
    ::= { mfrBundleIfIndexMappingTable 1 }

   MfrBundleIfIndexMappingEntry ::=
      SEQUENCE {
        mfrBundleIfIndexMappingIndex
           Integer32
        }

   mfrBundleIfIndexMappingIndex OBJECT-TYPE
      SYNTAX  Integer32 (1..2147483647)
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
           "The mfrBundleIndex of the given ifIndex."
    ::= { mfrBundleIfIndexMappingEntry 2 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Bundle Link Table
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

   mfrBundleLinkTable OBJECT-TYPE
      SYNTAX  SEQUENCE OF MfrBundleLinkEntry
      MAX-ACCESS  not-accessible
      STATUS  current
      DESCRIPTION
         "The bundle link configuration and status table.  There
          is a one-to-one correspondence between a bundle link
          and a physical interface represented in the ifTable.  The
          ifIndex of the physical interface is used to index the
          bundle link table, and to create rows.

          The following objects of the ifTable have specific
          meaning for an MFR bundle link:

             ifAdminStatus  - the bundle link admin status
             ifOperStatus   - the bundle link operational
                              status
             ifSpeed        - the bandwidth of the bundle
                              link interface
             ifInUcastPkts  - the number of frames received
                              on the bundle link
             ifOutUcastPkts - the number of frames transmitted
                              on the bundle link
             ifInErrors     - frame and fragment errors
             ifOutErrors    - frame and fragment errors"
      ::= { mfrMibBundleLinkObjects 1 }

   mfrBundleLinkEntry OBJECT-TYPE
      SYNTAX  MfrBundleLinkEntry
      MAX-ACCESS  not-accessible
      STATUS  current
      DESCRIPTION
         "An entry in the bundle link table."
      INDEX   { ifIndex }
      ::= { mfrBundleLinkTable 1 }

   MfrBundleLinkEntry ::=
      SEQUENCE {
         mfrBundleLinkRowStatus
            RowStatus,
         mfrBundleLinkConfigBundleIndex
            Integer32,
         mfrBundleLinkNearEndName
            SnmpAdminString,
         mfrBundleLinkState
            MfrBundleLinkState,
         mfrBundleLinkFarEndName
            SnmpAdminString,
         mfrBundleLinkFarEndBundleName
            SnmpAdminString,
         mfrBundleLinkDelay
            Integer32,
         mfrBundleLinkFramesControlTx
            Counter32,
         mfrBundleLinkFramesControlRx
            Counter32,
         mfrBundleLinkFramesControlInvalid
            Counter32,
         mfrBundleLinkTimerExpiredCount
            Counter32,
         mfrBundleLinkLoopbackSuspected
            Counter32,
         mfrBundleLinkUnexpectedSequence
            Counter32,
         mfrBundleLinkMismatch
            Counter32
         }

   mfrBundleLinkRowStatus OBJECT-TYPE
      SYNTAX  RowStatus
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The mfrBundleLinkRowStatus object allows create, change,
          and delete operations on mfrBundleLink entries.

          The create operation must fail if no physical interface
          is associated with the bundle link."
      ::= { mfrBundleLinkEntry 1 }

   mfrBundleLinkConfigBundleIndex OBJECT-TYPE
      SYNTAX  Integer32 (1..2147483647)
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The mfrBundleLinkConfigBundleIndex object allows
          the manager to control the bundle to which the bundle
          link is assigned.  If no value were in this field, then
          the bundle would remain in NOT_READY rowStatus and be
          unable to go to active.  With an appropriate mfrBundleIndex
          in this field, then we could put the mfrBundleLink row in
          NOT_IN_SERVICE or ACTIVE rowStatus."
      ::= { mfrBundleLinkEntry 2 }

   mfrBundleLinkNearEndName OBJECT-TYPE
      SYNTAX  SnmpAdminString
      MAX-ACCESS  read-create
      STATUS  current
      DESCRIPTION
         "The configured bundle link name that is sent to the far end."
      ::= { mfrBundleLinkEntry 3 }

   mfrBundleLinkState OBJECT-TYPE
      SYNTAX  MfrBundleLinkState
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Current bundle link state as defined by the MFR protocol
          described in Annex A of FRF.16."
      REFERENCE "FRF.16 Annex A"
      ::= { mfrBundleLinkEntry 4 }

   mfrBundleLinkFarEndName OBJECT-TYPE
      SYNTAX  SnmpAdminString
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Name of bundle link received from far end."
      REFERENCE "FRF.16 section 3.4.2"
      ::= { mfrBundleLinkEntry 5 }

   mfrBundleLinkFarEndBundleName OBJECT-TYPE
      SYNTAX  SnmpAdminString
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Name of far end bundle for this link received from far end."
      REFERENCE "FRF.16 section 3.4.1"
      ::= { mfrBundleLinkEntry 6 }

   mfrBundleLinkDelay OBJECT-TYPE
      SYNTAX  Integer32 (-1..2147483647)
      UNITS "Milliseconds"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Current round-trip delay for this bundle link.  The
          value -1 is returned when an implementation does not
          support measurement of the bundle link delay."
      REFERENCE "FRF.16 section 3.4.4"
      ::= { mfrBundleLinkEntry 7 }

   mfrBundleLinkFramesControlTx OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Frames"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Number of MFR control frames sent."
      REFERENCE "FRF.16 section 3.2"
      ::= { mfrBundleLinkEntry 8 }

   mfrBundleLinkFramesControlRx OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Frames"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Number of valid MFR control frames received."
      REFERENCE "FRF.16 section 3.2"
      ::= { mfrBundleLinkEntry 9 }

   mfrBundleLinkFramesControlInvalid OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Frames"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The number of invalid MFR control frames received."
      REFERENCE "FRF.16 section 3.2"
      ::= { mfrBundleLinkEntry 10 }

   mfrBundleLinkTimerExpiredCount OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Timer Expiration Events"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "Number of times the T_HELLO or T_ACK timers expired."
      REFERENCE "FRF.16 section ******* and *******"
      ::= { mfrBundleLinkEntry 11 }

   mfrBundleLinkLoopbackSuspected OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Loopback Suspected Events"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The number of times a loopback has been suspected
          (based upon the use of magic numbers)."
      REFERENCE "FRF.16 section 4.3.7"
      ::= { mfrBundleLinkEntry 12 }

   mfrBundleLinkUnexpectedSequence OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Frames"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The number of data MFR frames discarded because the sequence
          number of the frame for a DLCI was less than (delayed frame)
          or equal to (duplicate frame) the one expected for that DLCI.

          Example:
          Say frames with sequence numbers 56, 58, 59 is received for
          DLCI 100.  While waiting for sequence number 57 another frame
          with sequence number 58 arrives.  Frame 58 is discarded and
          the counter is incremented."
      REFERENCE "FRF.16 section *******"
      ::= { mfrBundleLinkEntry 13 }

   mfrBundleLinkMismatch OBJECT-TYPE
      SYNTAX  Counter32
      UNITS "Bundle Name Mismatch Events"
      MAX-ACCESS  read-only
      STATUS  current
      DESCRIPTION
         "The number of times that the unit has been notified by the
          remote peer that the bundle name is inconsistent with other
          bundle links attached to the far-end bundle."
      REFERENCE "FRF.16 section *******"
      ::= { mfrBundleLinkEntry 14 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Notifications/Traps
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

   mfrMibTrapBundleLinkMismatch NOTIFICATION-TYPE
      OBJECTS {
         mfrBundleNearEndName,
         mfrBundleFarEndName,
         mfrBundleLinkNearEndName,
         mfrBundleLinkFarEndName,
         mfrBundleLinkFarEndBundleName
         }
      STATUS  current
      DESCRIPTION
         "This trap indicates that a bundle link mismatch has
          been detected.  The following objects are reported:

          mfrBundleNearEndName:    configured name of near end bundle

          mfrBundleFarEndName:     previously reported name of
                                far end bundle

          mfrBundleLinkNearEndName: configured name of near end bundle

          mfrBundleLinkFarEndName: reported name of far end bundle

          mfrBundleLinkFarEndBundleName: currently reported name of
                                far end bundle

          Note: that the configured items may have been configured
                automatically.

          Note: The mfrBundleLinkMismatch counter is incremented when
                the trap is sent."
      REFERENCE "FRF.16 section *******"
       ::= { mfrMibTrapsPrefix 1 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Conformance/Compliance
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------

   mfrMibCompliance MODULE-COMPLIANCE
      STATUS      current
      DESCRIPTION
         "The compliance statement for equipment that implements
          the FRF16 MIB.  All of the current groups are mandatory,
          but a number of objects may be read-only if the
          implementation does not allow configuration."
      MODULE -- this module
      MANDATORY-GROUPS {
         mfrMibBundleGroup,
         mfrMibBundleLinkGroup,
         mfrMibTrapGroup
         }

      OBJECT     mfrBundleFragmentation
      MIN-ACCESS read-only
      DESCRIPTION
          "Write access is not required, but the value used must be
           reported."

      OBJECT     mfrBundleMaxFragSize
      MIN-ACCESS read-only
      DESCRIPTION
          "Write access is not required, but the value used must be
           reported.
           A value of -1 indicates that the value is not applicable."

      OBJECT     mfrBundleThreshold
      MIN-ACCESS read-only
      DESCRIPTION
          "Write access is not required, but the value used must be
           reported.
           A value of -1 indicates that the value is not applicable."

      OBJECT     mfrBundleMaxDiffDelay
      MIN-ACCESS read-only
      DESCRIPTION
          "Write access is not required, but the value used must be
           reported."
      OBJECT     mfrBundleSeqNumSize
      MIN-ACCESS read-only
      DESCRIPTION
          "Write access is not required, but the value used must be
           reported.
           A value of -1 indicates that the value is not applicable."

      ::= { mfrMibCompliances 1 }

   -- ---------------------------------------------------------
   -- ---------------------------------------------------------
   -- Units of Conformance
   -- ---------------------------------------------------------
   -- ---------------------------------------------------------


   mfrMibBundleGroup OBJECT-GROUP
      OBJECTS {
         mfrBundleMaxNumBundles,
         mfrBundleNextIndex,
         mfrBundleIfIndex,
         mfrBundleRowStatus,
         mfrBundleNearEndName,
         mfrBundleFragmentation,
         mfrBundleMaxFragSize,
         mfrBundleTimerHello,
         mfrBundleTimerAck,
         mfrBundleCountMaxRetry,
         mfrBundleActivationClass,
         mfrBundleThreshold,
         mfrBundleMaxDiffDelay,
         mfrBundleMaxBundleLinks,
         mfrBundleLinksConfigured,
         mfrBundleLinksActive,
         mfrBundleBandwidth,
         mfrBundleSeqNumSize,
         mfrBundleFarEndName,
         mfrBundleResequencingErrors,
         mfrBundleIfIndexMappingIndex
         }
      STATUS current
      DESCRIPTION
          "Group of objects describing bundles."
      ::= { mfrMibGroups 1 }

   mfrMibBundleLinkGroup   OBJECT-GROUP
      OBJECTS {
         mfrBundleLinkRowStatus,
         mfrBundleLinkConfigBundleIndex,
         mfrBundleLinkNearEndName,
         mfrBundleLinkState,
         mfrBundleLinkFarEndName,
         mfrBundleLinkFarEndBundleName,
         mfrBundleLinkDelay,
         mfrBundleLinkFramesControlTx,
         mfrBundleLinkFramesControlRx,
         mfrBundleLinkFramesControlInvalid,
         mfrBundleLinkTimerExpiredCount,
         mfrBundleLinkLoopbackSuspected,
         mfrBundleLinkUnexpectedSequence,
         mfrBundleLinkMismatch
         }
      STATUS current
      DESCRIPTION
          "Group of objects describing bundle links."
      ::= { mfrMibGroups 2 }

   mfrMibTrapGroup NOTIFICATION-GROUP
      NOTIFICATIONS {
         mfrMibTrapBundleLinkMismatch
         }
      STATUS current
      DESCRIPTION
          "Group of objects describing notifications (traps)."
      ::= { mfrMibGroups 3 }
END
