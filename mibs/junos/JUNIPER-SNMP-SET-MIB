--
-- Juniper Enterprise Specific MIB: General  traps and information 
--    to support SET 
--
-- Copyright (c) 2012-2013, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-SNMP-SET-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    DisplayString
        FROM SNMPv2-TC
    jnxSnmpSetMibRoot, jnxSnmpSetTraps
        FROM JUNIPER-SMI;

jnxSnmpSetMib MODULE-IDENTITY
    LAST-UPDATED "201201271000Z" -- Thu Jan 27 10:00:00 PST 2012
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"

    DESCRIPTION
            "This MIB module defines objects used for managing 
             SNMP sets for Juniper products."
    REVISION      "201311260000Z"
    DESCRIPTION
               "Added General  trap for SNMP set failure."
    ::= { jnxSnmpSetMibRoot 1 }

jnxSnmpSet              OBJECT IDENTIFIER ::=     { jnxSnmpSetMib 1 }
jnxSnmpSetNotifications OBJECT IDENTIFIER ::=     {  jnxSnmpSetTraps 1 }

--
-- Error returned for the commit failure
--
jnxCommitSetFailureReason OBJECT-TYPE
    SYNTAX          DisplayString 
    MAX-ACCESS      accessible-for-notify
    STATUS          current
    DESCRIPTION
            " This is the reason for the failure ... Text information
              about the failure "

    ::= { jnxSnmpSet 1 }

--
-- Traps
--
jnxSnmpSetFailure NOTIFICATION-TYPE
        OBJECTS {
                  jnxCommitSetFailureReason
                }
        STATUS  current
        DESCRIPTION
                "Notification for a snmp set commit error."
        ::= { jnxSnmpSetNotifications 1 }


END
