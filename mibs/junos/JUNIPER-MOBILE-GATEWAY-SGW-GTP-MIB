
--
-- Juniper Mobile SGateway GTP objects MIB.
--
-- Copyright (c) 2011-2013, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-MOBILE-GATEWAY-SGW-GTP-MIB DEFINITIONS ::= B<PERSON>IN

IMPORTS
    Counter64, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>teger32, Counter32, Unsigned32,
    NOTIFICATION-TYPE, MODULE-IDENTITY,OBJECT-TYPE
        FROM SNMPv2-SMI

    TEXTUAL-CONVENTION, DisplayString, RowStatus, TruthValue
        FROM SNMPv2-TC

    Ipv6AddressPrefix, Ipv6AddressIfIdentifier, Ipv6Address
        FROM IPV6-TC

    InetAddressType, InetAddress, InetPortNumber, InetAddressPrefixLength
        FROM INET-ADDRESS-MIB

    EnabledStatus
        FROM JUNIPER-MIMSTP-MIB

    jnxMobileGatewaySgw
        FROM JUNIPER-MBG-<PERSON><PERSON>

    jnxMbgGwIndex, jnxMbgGwName
        FROM JUNIPER-MOBILE-GATEWAYS;

--
-- Module Identity for GPRS Tunneling Protocol
-- GTPC generally refers to Control Path of the GTP protocol and
-- GTPU generally refers to the Data Path of the GTP Protocol
--

jnxMbgSgwGtpMib MODULE-IDENTITY
    LAST-UPDATED "201109211200Z" -- Sep 21, 2011, 12:00:00 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "Juniper Technical Assistance Center
         Juniper Networks, Inc.
         1133 Innovation Way
         Sunnyvale, CA 94089
         E-mail: <EMAIL>"
    DESCRIPTION
        "This module defines some sample objects pertaining to GTP protocol."
    REVISION "201109211200Z" -- Sep 21, 2011, 12:00:00 UTC
    DESCRIPTION "Initial version"

    ::= { jnxMobileGatewaySgw 2 }

jnxMbgSgwGtpNotifications  OBJECT IDENTIFIER ::=
                                    { jnxMbgSgwGtpMib 0 }
jnxMbgSgwGtpObjects     OBJECT IDENTIFIER ::=
                                    { jnxMbgSgwGtpMib 1 }

--
-- GTP Object for showing GTP Global Statistics
--
jnxMbgSgwGtpCGlbStatsTable  OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxMbgSgwGtpGlbStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry corresponds to a gateway level GTP Control statistic."
    ::= { jnxMbgSgwGtpObjects 2 }

jnxMbgSgwGtpGlbStatsEntry OBJECT-TYPE
    SYNTAX      JnxMbgSgwGtpGlbStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A specification of the GTP gateway level control Statistics."
    INDEX     { jnxMbgGwIndex }
    ::= { jnxMbgSgwGtpCGlbStatsTable 1}

JnxMbgSgwGtpGlbStatsEntry ::= SEQUENCE {
    jnxMbgSgwRxPacketsDropped       Counter64,
    jnxMbgSgwPacketAllocFail        Counter64,
    jnxMbgSgwPacketSendFail         Counter64,
    jnxMbgSgwIPVerErrRx             Counter64,
    jnxMbgSgwIPProtoErrRx           Counter64,
    jnxMbgSgwGTPPortErrRx           Counter64,
    jnxMbgSgwGTPUnknVerRx           Counter64,
    jnxMbgSgwPcktLenErrRx           Counter64,
    jnxMbgSgwUnknMsgRx              Counter64,
    jnxMbgSgwProtocolErrRx          Counter64,
    jnxMbgSgwUnSupportedMsgRx       Counter64,
    jnxMbgSgwT3RespTmrExpRx         Counter64,
    jnxMbgSgwV2NumMsgRx             Counter64,
    jnxMbgSgwV2NumMsgTx             Counter64,
    jnxMbgSgwV2NumBytesRx           Counter64,
    jnxMbgSgwV2NumBytesTx           Counter64,
    jnxMbgSgwV2EchoReqRx            Counter64,
    jnxMbgSgwV2EchoReqTx            Counter64,
    jnxMbgSgwV2EchoRespRx           Counter64,
    jnxMbgSgwV2EchoRespTx           Counter64,
    jnxMbgSgwV2VerNotSupRx          Counter64,
    jnxMbgSgwV2VerNotSupTx          Counter64,
    jnxMbgSgwCreateSessReqRx        Counter64,
    jnxMbgSgwCreateSessReqTx        Counter64,
    jnxMbgSgwCreateSessRspRx        Counter64,
    jnxMbgSgwCreateSessRspTx        Counter64,
    jnxMbgSgwModBrReqRx             Counter64,
    jnxMbgSgwModBrReqTx             Counter64,
    jnxMbgSgwModBrRspRx             Counter64,
    jnxMbgSgwModBrRspTx             Counter64,
    jnxMbgSgwDelSessReqRx           Counter64,
    jnxMbgSgwDelSessReqTx           Counter64,
    jnxMbgSgwDelSessRspRx           Counter64,
    jnxMbgSgwDelSessRspTx           Counter64,
    jnxMbgSgwCrtBrReqRx             Counter64,
    jnxMbgSgwCrtBrReqTx             Counter64,
    jnxMbgSgwCrtBrRspRx             Counter64,
    jnxMbgSgwCrtBrRspTx             Counter64,
    jnxMbgSgwUpdBrReqRx             Counter64,
    jnxMbgSgwUpdBrReqTx             Counter64,
    jnxMbgSgwUpdBrRspRx             Counter64,
    jnxMbgSgwUpdBrRspTx             Counter64,
    jnxMbgSgwDelBrReqRx             Counter64,
    jnxMbgSgwDelBrReqTx             Counter64,
    jnxMbgSgwDelBrRspRx             Counter64,
    jnxMbgSgwDelBrRspTx             Counter64,
    jnxMbgSgwDelConnSetReqRx        Counter64,
    jnxMbgSgwDelConnSetReqTx        Counter64,
    jnxMbgSgwDelConnSetRspRx        Counter64,
    jnxMbgSgwDelConnSetRspTx        Counter64,
    jnxMbgSgwUpdConnSetReqRx        Counter64,
    jnxMbgSgwUpdConnSetReqTx        Counter64,
    jnxMbgSgwUpdConnSetRspRx        Counter64,
    jnxMbgSgwUpdConnSetRspTx        Counter64,
    jnxMbgSgwModBrCmdRx             Counter64,
    jnxMbgSgwModBrCmdTx             Counter64,
    jnxMbgSgwModBrFlrIndRx          Counter64,
    jnxMbgSgwModBrFlrIndTx          Counter64,
    jnxMbgSgwDelBrCmdRx             Counter64,
    jnxMbgSgwDelBrCmdTx             Counter64,
    jnxMbgSgwDelBrFlrIndRx          Counter64,
    jnxMbgSgwDelBrFlrIndTx          Counter64,
    jnxMbgSgwBrResCmdRx             Counter64,
    jnxMbgSgwBrResCmdTx             Counter64,
    jnxMbgSgwBrResFlrIndRx          Counter64,
    jnxMbgSgwBrResFlrIndTx          Counter64,
    jnxMbgSgwRelAcsBrReqRx          Counter64,
    jnxMbgSgwRelAcsBrReqTx          Counter64,
    jnxMbgSgwRelAcsBrRespRx         Counter64,
    jnxMbgSgwRelAcsBrRespTx         Counter64,
    jnxMbgSgwCrIndTunReqRx          Counter64,
    jnxMbgSgwCrIndTunReqTx          Counter64,
    jnxMbgSgwCrIndTunRespRx         Counter64,
    jnxMbgSgwCrIndTunRespTx         Counter64,
    jnxMbgSgwDelIndTunReqRx         Counter64,
    jnxMbgSgwDelIndTunReqTx         Counter64,
    jnxMbgSgwDelIndTunRespRx        Counter64,
    jnxMbgSgwDelIndTunRespTx        Counter64,
    jnxMbgSgwDlDataNotifRx          Counter64,
    jnxMbgSgwDlDataNotifTx          Counter64,
    jnxMbgSgwDlDataAckRx            Counter64,
    jnxMbgSgwDlDataAckTx            Counter64,
    jnxMbgSgwDlDataNotiFlrIndRx     Counter64,
    jnxMbgSgwDlDataNotiFlrIndTx     Counter64,
    jnxMbgSgwStopPagingIndRx        Counter64,
    jnxMbgSgwStopPagingIndTx        Counter64,
    jnxMbgSgwGtpV2ICsPageRx         Counter64,
    jnxMbgSgwGtpV2ICsPageTx         Counter64,
    jnxMbgSgwGtpV2ICsReqAcceptRx    Counter64,
    jnxMbgSgwGtpV2ICsReqAcceptTx    Counter64,
    jnxMbgSgwGtpV2ICsAcceptPartRx   Counter64,
    jnxMbgSgwGtpV2ICsAcceptPartTx   Counter64,
    jnxMbgSgwGtpV2ICsNewPTNPrefRx   Counter64,
    jnxMbgSgwGtpV2ICsNewPTNPrefTx   Counter64,
    jnxMbgSgwGtpV2ICsNewPTSIAdbrRx  Counter64,
    jnxMbgSgwGtpV2ICsNewPTSIAdbrTx  Counter64,
    jnxMbgSgwGtpV2ICsCtxNotFndRx    Counter64,
    jnxMbgSgwGtpV2ICsCtxNotFndTx    Counter64,
    jnxMbgSgwGtpV2ICsInvMsgFmtRx    Counter64,
    jnxMbgSgwGtpV2ICsInvMsgFmtTx    Counter64,
    jnxMbgSgwGtpV2ICsVerNotSuppRx   Counter64,
    jnxMbgSgwGtpV2ICsVerNotSuppTx   Counter64,
    jnxMbgSgwGtpV2ICsInvLenRx       Counter64,
    jnxMbgSgwGtpV2ICsInvLenTx       Counter64,
    jnxMbgSgwGtpV2ICsServNotSuppRx  Counter64,
    jnxMbgSgwGtpV2ICsServNotSuppTx  Counter64,
    jnxMbgSgwGtpV2ICsManIEIncorrRx  Counter64,
    jnxMbgSgwGtpV2ICsManIEIncorrTx  Counter64,
    jnxMbgSgwGtpV2ICsManIEMissRx    Counter64,
    jnxMbgSgwGtpV2ICsManIEMissTx    Counter64,
    jnxMbgSgwGtpV2ICsOptIEIncorrRx  Counter64,
    jnxMbgSgwGtpV2ICsOptIEIncorrTx  Counter64,
    jnxMbgSgwGtpV2ICsSysFailRx      Counter64,
    jnxMbgSgwGtpV2ICsSysFailTx      Counter64,
    jnxMbgSgwGtpV2ICsNoResRx        Counter64,
    jnxMbgSgwGtpV2ICsNoResTx        Counter64,
    jnxMbgSgwGtpV2ICsTFTSMANTErRx   Counter64,
    jnxMbgSgwGtpV2ICsTFTSMANTErTx   Counter64,
    jnxMbgSgwGtpV2ICsTFTSysErrRx    Counter64,
    jnxMbgSgwGtpV2ICsTFTSysErrTx    Counter64,
    jnxMbgSgwGtpV2ICsPkFltManErrRx  Counter64,
    jnxMbgSgwGtpV2ICsPkFltManErrTx  Counter64,
    jnxMbgSgwGtpV2ICsPkFltSynErrRx  Counter64,
    jnxMbgSgwGtpV2ICsPkFltSynErrTx  Counter64,
    jnxMbgSgwGtpV2ICsMisUnknAPNRx   Counter64,
    jnxMbgSgwGtpV2ICsMisUnknAPNTx   Counter64,
    jnxMbgSgwGtpV2ICsUnexpRptIERx   Counter64,
    jnxMbgSgwGtpV2ICsUnexpRptIETx   Counter64,
    jnxMbgSgwGtpV2ICsGREKeyNtFdRx   Counter64,
    jnxMbgSgwGtpV2ICsGREKeyNtFdTx   Counter64,
    jnxMbgSgwGtpV2ICsRelocFailRx    Counter64,
    jnxMbgSgwGtpV2ICsRelocFailTx    Counter64,
    jnxMbgSgwGtpV2ICsDeniedINRatRx  Counter64,
    jnxMbgSgwGtpV2ICsDeniedINRatTx  Counter64,
    jnxMbgSgwGtpV2ICsPTNotSuppRx    Counter64,
    jnxMbgSgwGtpV2ICsPTNotSuppTx    Counter64,
    jnxMbgSgwGtpV2ICsAllDynAdOccRx  Counter64,
    jnxMbgSgwGtpV2ICsAllDynAdOccTx  Counter64,
    jnxMbgSgwGtpV2ICsNOTFTUECTXRx   Counter64,
    jnxMbgSgwGtpV2ICsNOTFTUECTXTx   Counter64,
    jnxMbgSgwGtpV2ICsProtoNtSupRx   Counter64,
    jnxMbgSgwGtpV2ICsProtoNtSupTx   Counter64,
    jnxMbgSgwGtpV2ICsUENotRespRx    Counter64,
    jnxMbgSgwGtpV2ICsUENotRespTx    Counter64,
    jnxMbgSgwGtpV2ICsUERefusesRx    Counter64,
    jnxMbgSgwGtpV2ICsUERefusesTx    Counter64,
    jnxMbgSgwGtpV2ICsServDeniedRx   Counter64,
    jnxMbgSgwGtpV2ICsServDeniedTx   Counter64,
    jnxMbgSgwGtpV2ICsUnabPageUERx   Counter64,
    jnxMbgSgwGtpV2ICsUnabPageUETx   Counter64,
    jnxMbgSgwGtpV2ICsNoMemRx        Counter64,
    jnxMbgSgwGtpV2ICsNoMemTx        Counter64,
    jnxMbgSgwGtpV2ICsUserAUTHFlRx   Counter64,
    jnxMbgSgwGtpV2ICsUserAUTHFlTx   Counter64,
    jnxMbgSgwGtpV2ICsAPNAcsDenRx    Counter64,
    jnxMbgSgwGtpV2ICsAPNAcsDenTx    Counter64,
    jnxMbgSgwGtpV2ICsReqRejRx       Counter64,
    jnxMbgSgwGtpV2ICsReqRejTx       Counter64,
    jnxMbgSgwGtpV2ICsPTMSISigMMRx   Counter64,
    jnxMbgSgwGtpV2ICsPTMSISigMMTx   Counter64,
    jnxMbgSgwGtpV2ICsIMSINotKnRx    Counter64,
    jnxMbgSgwGtpV2ICsIMSINotKnTx    Counter64,
    jnxMbgSgwGtpV2ICsCondIEMsRx     Counter64,
    jnxMbgSgwGtpV2ICsCondIEMsTx     Counter64,
    jnxMbgSgwGtpV2ICsAPNResTIncRx   Counter64,
    jnxMbgSgwGtpV2ICsAPNResTIncTx   Counter64,
    jnxMbgSgwGtpV2ICsUnknownRx      Counter64,
    jnxMbgSgwGtpV2ICsUnknownTx      Counter64,
    jnxMbgSgwGtpV2ICsLclDetRx       Counter64,
    jnxMbgSgwGtpV2ICsLclDetTx       Counter64,
    jnxMbgSgwGtpV2ICsCmpDetRx       Counter64,
    jnxMbgSgwGtpV2ICsCmpDetTx       Counter64,
    jnxMbgSgwGtpV2ICsRATChgRx       Counter64,
    jnxMbgSgwGtpV2ICsRATChgTx       Counter64,
    jnxMbgSgwGtpV2ICsISRDeactRx     Counter64,
    jnxMbgSgwGtpV2ICsISRDeactTx     Counter64,
    jnxMbgSgwGtpV2ICsEIFRNCEnRx     Counter64,
    jnxMbgSgwGtpV2ICsEIFRNCEnTx     Counter64,
    jnxMbgSgwGtpV2ICsSemErTADRx     Counter64,
    jnxMbgSgwGtpV2ICsSemErTADTx     Counter64,
    jnxMbgSgwGtpV2ICsSynErTADRx     Counter64,
    jnxMbgSgwGtpV2ICsSynErTADTx     Counter64,
    jnxMbgSgwGtpV2ICsRMValRcvRx     Counter64,
    jnxMbgSgwGtpV2ICsRMValRcvTx     Counter64,
    jnxMbgSgwGtpV2ICsRPrNtRspRx     Counter64,
    jnxMbgSgwGtpV2ICsRPrNtRspTx     Counter64,
    jnxMbgSgwGtpV2ICsColNWReqRx     Counter64,
    jnxMbgSgwGtpV2ICsColNWReqTx     Counter64,
    jnxMbgSgwGtpV2ICsUnPgUESusRx    Counter64,
    jnxMbgSgwGtpV2ICsUnPgUESusTx    Counter64,
    jnxMbgSgwGtpV2ICsInvTotLenRx    Counter64,
    jnxMbgSgwGtpV2ICsInvTotLenTx    Counter64,
    jnxMbgSgwGtpV2ICsDtForNtSupRx   Counter64,
    jnxMbgSgwGtpV2ICsDtForNtSupTx   Counter64,
    jnxMbgSgwGtpV2ICsInReFRePrRx    Counter64,
    jnxMbgSgwGtpV2ICsInReFRePrTx    Counter64,
    jnxMbgSgwGtpV2ICsInvPrRx        Counter64,
    jnxMbgSgwGtpV2ICsInvPrTx        Counter64,
    jnxMbgSgwGtpV1ProtocolErrRx     Counter64,
    jnxMbgSgwGtpV1UnSupMsgRx        Counter64,
    jnxMbgSgwGtpV1T3RespTmrExpRx    Counter64,
    jnxMbgSgwGtpV1EndMarkerRx       Counter64,
    jnxMbgSgwGtpV1EndMarkerTx       Counter64,
    jnxMbgSgwGtpV1EchoReqRx         Counter64,
    jnxMbgSgwGtpV1EchoReqTx         Counter64,
    jnxMbgSgwGtpV1EchoRespRx        Counter64,
    jnxMbgSgwGtpV1EchoRespTx        Counter64,
    jnxMbgSgwGtpV1ErrIndRx          Counter64,
    jnxMbgSgwGtpV1ErrIndTx          Counter64,
    jnxMbgSgwSuspNotifRx            Counter64,
    jnxMbgSgwSuspNotifTx            Counter64,
    jnxMbgSgwSuspAckRx              Counter64,
    jnxMbgSgwSuspAckTx              Counter64,
    jnxMbgSgwResumeNotifRx          Counter64,
    jnxMbgSgwResumeNotifTx          Counter64,
    jnxMbgSgwResumeAckRx            Counter64,
    jnxMbgSgwResumeAckTx            Counter64,
    jnxMbgSgwS11PiggybackMsgRx      Counter64,
    jnxMbgSgwS11PiggybackMsgTx      Counter64,
    jnxMbgSgwS4PiggybackMsgRx       Counter64,
    jnxMbgSgwS4PiggybackMsgTx       Counter64,
    jnxMbgSgwS5PiggybackMsgRx       Counter64,
    jnxMbgSgwS5PiggybackMsgTx       Counter64
}

jnxMbgSgwRxPacketsDropped  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Received Packets Dropped."
    ::= { jnxMbgSgwGtpGlbStatsEntry 1 }

jnxMbgSgwPacketAllocFail  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet allocation failures."
    ::= { jnxMbgSgwGtpGlbStatsEntry 2 }

jnxMbgSgwPacketSendFail OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet Send failures."
    ::= { jnxMbgSgwGtpGlbStatsEntry 3 }
 
jnxMbgSgwIPVerErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of IP Version Error Packets Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 4 }

jnxMbgSgwIPProtoErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of IP protocol Error packets Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 5 }

jnxMbgSgwGTPPortErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Port Error Packets Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 6 }

jnxMbgSgwGTPUnknVerRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Unknown Version Packets Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 7 }

jnxMbgSgwPcktLenErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet Length Error Packets Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 8 }

jnxMbgSgwUnknMsgRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Unknown Messages Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 9 }

jnxMbgSgwProtocolErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 Protocol Errors Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 10 }

jnxMbgSgwUnSupportedMsgRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 Unsupported Messages received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 11 }

jnxMbgSgwT3RespTmrExpRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 T3 timer expiries Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 12 }

jnxMbgSgwV2NumMsgRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 messages received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 13 }

jnxMbgSgwV2NumMsgTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of V2 messages sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 14 }

jnxMbgSgwV2NumBytesRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 bytes received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 15 }

jnxMbgSgwV2NumBytesTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of V2 bytes sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 16 }

jnxMbgSgwV2EchoReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Request received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 19 }

jnxMbgSgwV2EchoReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Request Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 20 }

jnxMbgSgwV2EchoRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Response received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 21 }

jnxMbgSgwV2EchoRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Response Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 22 }

jnxMbgSgwV2VerNotSupRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Version Not supported messages received"
    ::= { jnxMbgSgwGtpGlbStatsEntry 23 }

jnxMbgSgwV2VerNotSupTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 version not supported messages sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 24 }

jnxMbgSgwCreateSessReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Requests received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 25 }

jnxMbgSgwCreateSessReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Requests Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 26 }

jnxMbgSgwCreateSessRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Responses received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 27 }

jnxMbgSgwCreateSessRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Responses Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 28 }

jnxMbgSgwModBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Requests received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 29 }

jnxMbgSgwModBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Requests Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 30 }

jnxMbgSgwModBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Responses received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 31 }

jnxMbgSgwModBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Responses Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 32 }

jnxMbgSgwDelSessReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Requests received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 33 }

jnxMbgSgwDelSessReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Requests Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 34 }

jnxMbgSgwDelSessRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Responses received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 35 }

jnxMbgSgwDelSessRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Responses Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 36 }

jnxMbgSgwCrtBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Requests received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 37 }

jnxMbgSgwCrtBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Requests Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 38 }

jnxMbgSgwCrtBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Response received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 39 }

jnxMbgSgwCrtBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Response Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 40 }

jnxMbgSgwUpdBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Request received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 41 }

jnxMbgSgwUpdBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Request Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 42 }

jnxMbgSgwUpdBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Response received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 43 }

jnxMbgSgwUpdBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Response Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 44 }

jnxMbgSgwDelBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Request received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 45 }

jnxMbgSgwDelBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Request Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 46 }

jnxMbgSgwDelBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Response received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 47 }

jnxMbgSgwDelBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Response Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 48 }

jnxMbgSgwDelConnSetReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Request received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 49 }

jnxMbgSgwDelConnSetReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Request Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 50 }

jnxMbgSgwDelConnSetRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Response received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 51 }

jnxMbgSgwDelConnSetRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Response Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 52 }

jnxMbgSgwUpdConnSetReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Request received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 53 }

jnxMbgSgwUpdConnSetReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Request Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 54 }

jnxMbgSgwUpdConnSetRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Response received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 55 }

jnxMbgSgwUpdConnSetRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Response Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 56 }

jnxMbgSgwModBrCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Command received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 57 }

jnxMbgSgwModBrCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Command Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 58 }

jnxMbgSgwModBrFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Failure received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 59 }

jnxMbgSgwModBrFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Failure Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 60 }

jnxMbgSgwDelBrCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Command received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 61 }

jnxMbgSgwDelBrCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Command Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 62 }

jnxMbgSgwDelBrFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Failure received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 63 }

jnxMbgSgwDelBrFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Failure Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 64 }

jnxMbgSgwBrResCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Response Command received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 65 }

jnxMbgSgwBrResCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Response Command Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 66 }

jnxMbgSgwBrResFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Resource Failure received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 67 }

jnxMbgSgwBrResFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Resource Failure Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 68 }

jnxMbgSgwRelAcsBrReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Requests received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 69 }

jnxMbgSgwRelAcsBrReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Requests sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 70 }

jnxMbgSgwRelAcsBrRespRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Response received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 71 }

jnxMbgSgwRelAcsBrRespTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Response sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 72 }

jnxMbgSgwCrIndTunReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Request Received"
    ::= { jnxMbgSgwGtpGlbStatsEntry 73 }

jnxMbgSgwCrIndTunReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Request sent"
    ::= { jnxMbgSgwGtpGlbStatsEntry 74 }

jnxMbgSgwCrIndTunRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Response Received"
    ::= { jnxMbgSgwGtpGlbStatsEntry 75 }

jnxMbgSgwCrIndTunRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Response sent"
    ::= { jnxMbgSgwGtpGlbStatsEntry 76 }

jnxMbgSgwDelIndTunReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Request Received"
    ::= { jnxMbgSgwGtpGlbStatsEntry 77 }

jnxMbgSgwDelIndTunReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Request sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 78 }

jnxMbgSgwDelIndTunRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Response Received"
    ::= { jnxMbgSgwGtpGlbStatsEntry 79 }

jnxMbgSgwDelIndTunRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Response sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 80 }

jnxMbgSgwDlDataNotifRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 81 }

jnxMbgSgwDlDataNotifTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 82 }

jnxMbgSgwDlDataAckRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Acknowledgement received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 83 }

jnxMbgSgwDlDataAckTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Acknowledgement Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 84 }

jnxMbgSgwDlDataNotiFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notification fail received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 85 }

jnxMbgSgwDlDataNotiFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notification fail Sent."
    ::= { jnxMbgSgwGtpGlbStatsEntry 86 }

jnxMbgSgwStopPagingIndRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Number of Stop Paging Indication Messages Received."
    ::= { jnxMbgSgwGtpGlbStatsEntry 87 }

jnxMbgSgwStopPagingIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Number of Stop Paging Indicaton messages sent"
    ::= { jnxMbgSgwGtpGlbStatsEntry 88 }

jnxMbgSgwGtpV2ICsPageRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
    "Number of GTPV2 packets received with cause Page."
    ::= { jnxMbgSgwGtpGlbStatsEntry 89 }

jnxMbgSgwGtpV2ICsPageTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
    "Number of GTP packets sent with cause Page."
    ::= { jnxMbgSgwGtpGlbStatsEntry 90 }

jnxMbgSgwGtpV2ICsReqAcceptRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Request Accept."
    ::= { jnxMbgSgwGtpGlbStatsEntry 91 }

jnxMbgSgwGtpV2ICsReqAcceptTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Request Accept."
    ::= { jnxMbgSgwGtpGlbStatsEntry 92 }

jnxMbgSgwGtpV2ICsAcceptPartRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Accept Partial."
    ::= { jnxMbgSgwGtpGlbStatsEntry 93 }

jnxMbgSgwGtpV2ICsAcceptPartTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Accept Partial."
    ::= { jnxMbgSgwGtpGlbStatsEntry 94 }

jnxMbgSgwGtpV2ICsNewPTNPrefRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause New PDN type due to Network Preference."
    ::= { jnxMbgSgwGtpGlbStatsEntry 95 }

jnxMbgSgwGtpV2ICsNewPTNPrefTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause New PDN type due to Network Preference"
    ::= { jnxMbgSgwGtpGlbStatsEntry 96 }


jnxMbgSgwGtpV2ICsNewPTSIAdbrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause New PDN type due to Single Address Bearer."
    ::= { jnxMbgSgwGtpGlbStatsEntry 97 }

jnxMbgSgwGtpV2ICsNewPTSIAdbrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause New PDN type due to Single Address Bearer."
    ::= { jnxMbgSgwGtpGlbStatsEntry 98 }

jnxMbgSgwGtpV2ICsCtxNotFndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Context not found."
    ::= { jnxMbgSgwGtpGlbStatsEntry 99 }

jnxMbgSgwGtpV2ICsCtxNotFndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Context not found."
    ::= { jnxMbgSgwGtpGlbStatsEntry 100 }

jnxMbgSgwGtpV2ICsInvMsgFmtRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Invalid Message Format."
    ::= { jnxMbgSgwGtpGlbStatsEntry 101 }

jnxMbgSgwGtpV2ICsInvMsgFmtTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Invalid Message Format."
    ::= { jnxMbgSgwGtpGlbStatsEntry 102 }

jnxMbgSgwGtpV2ICsVerNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Version not Supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 103 }

jnxMbgSgwGtpV2ICsVerNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Version not Supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 104 }

jnxMbgSgwGtpV2ICsInvLenRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Invalid Length."
    ::= { jnxMbgSgwGtpGlbStatsEntry 105 }

jnxMbgSgwGtpV2ICsInvLenTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Invalid Length."
    ::= { jnxMbgSgwGtpGlbStatsEntry 106 }

jnxMbgSgwGtpV2ICsServNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Service Not supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 107 }

jnxMbgSgwGtpV2ICsServNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Service Not supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 108 }

jnxMbgSgwGtpV2ICsManIEIncorrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Mandatory IE incorrect."
    ::= { jnxMbgSgwGtpGlbStatsEntry 109 }

jnxMbgSgwGtpV2ICsManIEIncorrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Mandatory IE incorrect."
    ::= { jnxMbgSgwGtpGlbStatsEntry 110 }

jnxMbgSgwGtpV2ICsManIEMissRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Mandatory IE Missing."
    ::= { jnxMbgSgwGtpGlbStatsEntry 111 }

jnxMbgSgwGtpV2ICsManIEMissTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Mandatory IE Missing."
    ::= { jnxMbgSgwGtpGlbStatsEntry 112 }

jnxMbgSgwGtpV2ICsOptIEIncorrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Optional IE Incorrect."
    ::= { jnxMbgSgwGtpGlbStatsEntry 113 }

jnxMbgSgwGtpV2ICsOptIEIncorrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Optional IE Incorrect."
    ::= { jnxMbgSgwGtpGlbStatsEntry 114 }

jnxMbgSgwGtpV2ICsSysFailRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause System Failure."
    ::= { jnxMbgSgwGtpGlbStatsEntry 115 }

jnxMbgSgwGtpV2ICsSysFailTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause System Failure."
    ::= { jnxMbgSgwGtpGlbStatsEntry 116 }

jnxMbgSgwGtpV2ICsNoResRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause No Resource."
    ::= { jnxMbgSgwGtpGlbStatsEntry 117 }

jnxMbgSgwGtpV2ICsNoResTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause No Resource."
    ::= { jnxMbgSgwGtpGlbStatsEntry 118 }

jnxMbgSgwGtpV2ICsTFTSMANTErRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause TFT Symantic Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 119 }

jnxMbgSgwGtpV2ICsTFTSMANTErTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause TFT Symantic Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 120 }

jnxMbgSgwGtpV2ICsTFTSysErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause TFT System Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 121 }

jnxMbgSgwGtpV2ICsTFTSysErrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause TFT System Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 122 }

jnxMbgSgwGtpV2ICsPkFltManErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Packet Filter Symantic Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 123 }

jnxMbgSgwGtpV2ICsPkFltManErrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Packet Filter Symantic Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 124 }

jnxMbgSgwGtpV2ICsPkFltSynErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Packet Filter Syntax Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 125 }

jnxMbgSgwGtpV2ICsPkFltSynErrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Packet Filter Syntax Error."
    ::= { jnxMbgSgwGtpGlbStatsEntry 126 }

jnxMbgSgwGtpV2ICsMisUnknAPNRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unknown APN."
    ::= { jnxMbgSgwGtpGlbStatsEntry 127 }

jnxMbgSgwGtpV2ICsMisUnknAPNTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unknown APN."
    ::= { jnxMbgSgwGtpGlbStatsEntry 128 }

jnxMbgSgwGtpV2ICsUnexpRptIERx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unexpected Repeated IE."
    ::= { jnxMbgSgwGtpGlbStatsEntry 129 }

jnxMbgSgwGtpV2ICsUnexpRptIETx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unexpected Repeated IE."
    ::= { jnxMbgSgwGtpGlbStatsEntry 130 }

jnxMbgSgwGtpV2ICsGREKeyNtFdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause GRE Key Not Found."
    ::= { jnxMbgSgwGtpGlbStatsEntry 131 }

jnxMbgSgwGtpV2ICsGREKeyNtFdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause GRE Key Not Found."
    ::= { jnxMbgSgwGtpGlbStatsEntry 132 }

jnxMbgSgwGtpV2ICsRelocFailRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Relocation Failed."
    ::= { jnxMbgSgwGtpGlbStatsEntry 133 }

jnxMbgSgwGtpV2ICsRelocFailTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Relocation Failed."
    ::= { jnxMbgSgwGtpGlbStatsEntry 134 }

jnxMbgSgwGtpV2ICsDeniedINRatRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Denied in RAT."
    ::= { jnxMbgSgwGtpGlbStatsEntry 135 }

jnxMbgSgwGtpV2ICsDeniedINRatTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Denied in RAT."
    ::= { jnxMbgSgwGtpGlbStatsEntry 136 }

jnxMbgSgwGtpV2ICsPTNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause PDN Type Not Supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 137 }

jnxMbgSgwGtpV2ICsPTNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause PDN Type Not Supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 138 }

jnxMbgSgwGtpV2ICsAllDynAdOccRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Allocated Dynamic Address Occupied."
    ::= { jnxMbgSgwGtpGlbStatsEntry 139 }

jnxMbgSgwGtpV2ICsAllDynAdOccTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Allocated Dynamic Address Occupied."
    ::= { jnxMbgSgwGtpGlbStatsEntry 140 }

jnxMbgSgwGtpV2ICsNOTFTUECTXRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Context Without TFT Exists."
    ::= { jnxMbgSgwGtpGlbStatsEntry 141 }

jnxMbgSgwGtpV2ICsNOTFTUECTXTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Context Without TFT Exists."
    ::= { jnxMbgSgwGtpGlbStatsEntry 142 }

jnxMbgSgwGtpV2ICsProtoNtSupRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Protocol Not Supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 143 }

jnxMbgSgwGtpV2ICsProtoNtSupTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Protocol Not Supported."
    ::= { jnxMbgSgwGtpGlbStatsEntry 144 }

jnxMbgSgwGtpV2ICsUENotRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Not Responding."
    ::= { jnxMbgSgwGtpGlbStatsEntry 145 }

jnxMbgSgwGtpV2ICsUENotRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Not Responding."
    ::= { jnxMbgSgwGtpGlbStatsEntry 146 }

jnxMbgSgwGtpV2ICsUERefusesRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Refuses."
    ::= { jnxMbgSgwGtpGlbStatsEntry 147 }

jnxMbgSgwGtpV2ICsUERefusesTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Refuses."
    ::= { jnxMbgSgwGtpGlbStatsEntry 148 }

jnxMbgSgwGtpV2ICsServDeniedRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Service Denied."
    ::= { jnxMbgSgwGtpGlbStatsEntry 149 }

jnxMbgSgwGtpV2ICsServDeniedTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Service Denied."
    ::= { jnxMbgSgwGtpGlbStatsEntry 150 }

jnxMbgSgwGtpV2ICsUnabPageUERx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unable to Page UE."
    ::= { jnxMbgSgwGtpGlbStatsEntry 151 }

jnxMbgSgwGtpV2ICsUnabPageUETx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unable to Page UE."
    ::= { jnxMbgSgwGtpGlbStatsEntry 152 }

jnxMbgSgwGtpV2ICsNoMemRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause No Memory."
    ::= { jnxMbgSgwGtpGlbStatsEntry 153 }

jnxMbgSgwGtpV2ICsNoMemTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause No Memory."
    ::= { jnxMbgSgwGtpGlbStatsEntry 154 }

jnxMbgSgwGtpV2ICsUserAUTHFlRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause User AUTH Failed."
    ::= { jnxMbgSgwGtpGlbStatsEntry 155 }

jnxMbgSgwGtpV2ICsUserAUTHFlTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause User AUTH Failed."
    ::= { jnxMbgSgwGtpGlbStatsEntry 156 }

jnxMbgSgwGtpV2ICsAPNAcsDenRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause APN Access Denied."
    ::= { jnxMbgSgwGtpGlbStatsEntry 157 }

jnxMbgSgwGtpV2ICsAPNAcsDenTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause APN Access Denied."
    ::= { jnxMbgSgwGtpGlbStatsEntry 158 }

jnxMbgSgwGtpV2ICsReqRejRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Request Rejected."
    ::= { jnxMbgSgwGtpGlbStatsEntry 159 }

jnxMbgSgwGtpV2ICsReqRejTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Request Rejected."
    ::= { jnxMbgSgwGtpGlbStatsEntry 160 }

jnxMbgSgwGtpV2ICsPTMSISigMMRx OBJECT-TYPE
     SYNTAX      Counter64
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of GTPV2 packets received with cause P-TMSI Signature Mismatch."
    ::= { jnxMbgSgwGtpGlbStatsEntry 161 }

jnxMbgSgwGtpV2ICsPTMSISigMMTx OBJECT-TYPE
     SYNTAX      Counter64
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of GTP packets sent with cause P-TMSI Signature Mismatch"
    ::= { jnxMbgSgwGtpGlbStatsEntry 162 }

jnxMbgSgwGtpV2ICsIMSINotKnRx OBJECT-TYPE
      SYNTAX      Counter64
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
      "Number of GTPV2 packets received with cause IMSI Not Known."
    ::= { jnxMbgSgwGtpGlbStatsEntry 163 }

jnxMbgSgwGtpV2ICsIMSINotKnTx OBJECT-TYPE
      SYNTAX      Counter64
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
      "Number of GTP packets sent with cause IMSI Not Known."
    ::= { jnxMbgSgwGtpGlbStatsEntry 164 }

jnxMbgSgwGtpV2ICsCondIEMsRx OBJECT-TYPE
       SYNTAX      Counter64
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
       "Number of GTPV2 packets received with cause Conditional IE Missing."
     ::= { jnxMbgSgwGtpGlbStatsEntry 165 }

jnxMbgSgwGtpV2ICsCondIEMsTx OBJECT-TYPE
       SYNTAX      Counter64
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
       "Number of GTP packets sent with cause Conditional IE Missing."
     ::= { jnxMbgSgwGtpGlbStatsEntry 166 }

jnxMbgSgwGtpV2ICsAPNResTIncRx OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of GTPV2 packets received with cause APN Restriction Type Incompatible."
     ::= { jnxMbgSgwGtpGlbStatsEntry 167 }

jnxMbgSgwGtpV2ICsAPNResTIncTx OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of GTP packets sent with cause APN Restriction Type Incompatible."
     ::= { jnxMbgSgwGtpGlbStatsEntry 168 }

jnxMbgSgwGtpV2ICsUnknownRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPV2 packets received with cause Unknown."
     ::= { jnxMbgSgwGtpGlbStatsEntry 169 }

jnxMbgSgwGtpV2ICsUnknownTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Unknown."
     ::= { jnxMbgSgwGtpGlbStatsEntry 170 }

jnxMbgSgwGtpV2ICsLclDetRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Local Detach."
     ::= { jnxMbgSgwGtpGlbStatsEntry 171 }

jnxMbgSgwGtpV2ICsLclDetTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Local Detach."
     ::= { jnxMbgSgwGtpGlbStatsEntry 172 }

jnxMbgSgwGtpV2ICsCmpDetRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Complete Detach."
     ::= { jnxMbgSgwGtpGlbStatsEntry 173 }

jnxMbgSgwGtpV2ICsCmpDetTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Complete Detach."
     ::= { jnxMbgSgwGtpGlbStatsEntry 174 }

jnxMbgSgwGtpV2ICsRATChgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause RAT changed from 3GPP to non 3GPP."
     ::= { jnxMbgSgwGtpGlbStatsEntry 175 }

jnxMbgSgwGtpV2ICsRATChgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause RAT changed from 3GPP to non 3GPP."
     ::= { jnxMbgSgwGtpGlbStatsEntry 176 }

jnxMbgSgwGtpV2ICsISRDeactRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause ISR Deactivated."
     ::= { jnxMbgSgwGtpGlbStatsEntry 177 }

jnxMbgSgwGtpV2ICsISRDeactTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause ISR Deactivated."
     ::= { jnxMbgSgwGtpGlbStatsEntry 178 }

jnxMbgSgwGtpV2ICsEIFRNCEnRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Error Indication from RNC eNodeB."
     ::= { jnxMbgSgwGtpGlbStatsEntry 179 }

jnxMbgSgwGtpV2ICsEIFRNCEnTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Error Indication from RNC eNodeB."
     ::= { jnxMbgSgwGtpGlbStatsEntry 180 }

jnxMbgSgwGtpV2ICsSemErTADRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Semantic Error in TAD Operation."
     ::= { jnxMbgSgwGtpGlbStatsEntry 181 }

jnxMbgSgwGtpV2ICsSemErTADTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Semantic Error in TAD Operation."
     ::= { jnxMbgSgwGtpGlbStatsEntry 182 }

jnxMbgSgwGtpV2ICsSynErTADRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Syntactic Error in TAD Operation."
     ::= { jnxMbgSgwGtpGlbStatsEntry 183 }

jnxMbgSgwGtpV2ICsSynErTADTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Syntactic Error in TAD Operation."
     ::= { jnxMbgSgwGtpGlbStatsEntry 184 }

jnxMbgSgwGtpV2ICsRMValRcvRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Reserved Message Value Received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 185 }

jnxMbgSgwGtpV2ICsRMValRcvTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Reserved Message Value Received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 186 }

jnxMbgSgwGtpV2ICsRPrNtRspRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Remote peer not responding."
     ::= { jnxMbgSgwGtpGlbStatsEntry 187 }

jnxMbgSgwGtpV2ICsRPrNtRspTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Remote peer not responding."
     ::= { jnxMbgSgwGtpGlbStatsEntry 188 }

jnxMbgSgwGtpV2ICsColNWReqRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Collision with network initiated request."
     ::= { jnxMbgSgwGtpGlbStatsEntry 189 }

jnxMbgSgwGtpV2ICsColNWReqTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Collision with network initiated request."
     ::= { jnxMbgSgwGtpGlbStatsEntry 190 }

jnxMbgSgwGtpV2ICsUnPgUESusRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Unable to page UE due to suspension."
     ::= { jnxMbgSgwGtpGlbStatsEntry 191 }

jnxMbgSgwGtpV2ICsUnPgUESusTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Unable to page UE due to suspension."
     ::= { jnxMbgSgwGtpGlbStatsEntry 192 }

jnxMbgSgwGtpV2ICsInvTotLenRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid total len."
     ::= { jnxMbgSgwGtpGlbStatsEntry 193 }

jnxMbgSgwGtpV2ICsInvTotLenTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid total len."
     ::= { jnxMbgSgwGtpGlbStatsEntry 194 }

jnxMbgSgwGtpV2ICsDtForNtSupRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Data forwarding not supported."
     ::= { jnxMbgSgwGtpGlbStatsEntry 195 }

jnxMbgSgwGtpV2ICsDtForNtSupTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Data forwarding not supported."
     ::= { jnxMbgSgwGtpGlbStatsEntry 196 }

jnxMbgSgwGtpV2ICsInReFRePrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid Reply from Remote peer."
     ::= { jnxMbgSgwGtpGlbStatsEntry 197 }

jnxMbgSgwGtpV2ICsInReFRePrTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid Reply from Remote peer."
     ::= { jnxMbgSgwGtpGlbStatsEntry 198 }

jnxMbgSgwGtpV2ICsInvPrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid peer."
     ::= { jnxMbgSgwGtpGlbStatsEntry 199 }

jnxMbgSgwGtpV2ICsInvPrTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid peer."
     ::= { jnxMbgSgwGtpGlbStatsEntry 200 }

jnxMbgSgwGtpV1ProtocolErrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv1 Protocol Errors Received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 201 }

jnxMbgSgwGtpV1UnSupMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv1 Unsupported Messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 202 }

jnxMbgSgwGtpV1T3RespTmrExpRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 T3 timer expiries Received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 203 }

jnxMbgSgwGtpV1EndMarkerRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 end marker packets received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 204 }

jnxMbgSgwGtpV1EndMarkerTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 end marker packets sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 205 }

jnxMbgSgwGtpV1EchoReqRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo request packets received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 206 }

jnxMbgSgwGtpV1EchoReqTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo request packets sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 207 }

jnxMbgSgwGtpV1EchoRespRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo response packets received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 208 }

jnxMbgSgwGtpV1EchoRespTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo response packets sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 209 }

jnxMbgSgwGtpV1ErrIndRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 Error Indication packets received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 210 }

jnxMbgSgwGtpV1ErrIndTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 Error Indication packets sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 211 }

jnxMbgSgwSuspNotifRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Notification messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 212 } 

jnxMbgSgwSuspNotifTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Notification messages sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 213 } 

jnxMbgSgwSuspAckRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Acknowledgement messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 214 }

jnxMbgSgwSuspAckTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Acknowledgement messages sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 215 }

jnxMbgSgwResumeNotifRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Notification messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 216 }
    
jnxMbgSgwResumeNotifTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Notification messages sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 217 }

jnxMbgSgwResumeAckRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Acknowledgement messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 218 }
   
jnxMbgSgwResumeAckTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Acknowledgement messages sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 219 }

jnxMbgSgwS11PiggybackMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S11 Piggyback messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 220 }
    
jnxMbgSgwS11PiggybackMsgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S11 Piggyback messages sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 221 }

jnxMbgSgwS4PiggybackMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S4 Piggyback messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 222 }
    
jnxMbgSgwS4PiggybackMsgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S4 Piggyback messages sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 223 }

jnxMbgSgwS5PiggybackMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S5 Piggyback messages received."
     ::= { jnxMbgSgwGtpGlbStatsEntry 224 }
    
jnxMbgSgwS5PiggybackMsgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S5 Piggyback messages sent."
     ::= { jnxMbgSgwGtpGlbStatsEntry 225 }

--
-- GTP Object for showing GTP Per Peer Statistics
--

jnxMbgSgwGtpCPerPeerStatsTable  OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxMbgSgwGtpPerPeerStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry corresponds to a GTP per peer level control statistic."
    ::= { jnxMbgSgwGtpObjects 1 }

jnxMbgSgwGtpPerPeerStatsEntry OBJECT-TYPE
    SYNTAX      JnxMbgSgwGtpPerPeerStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A specification of the GTPC peer level Statistics."
    INDEX     { jnxMbgGwIndex,
                jnxMbgSgwPPGtpRmtAddr,
                jnxMbgSgwPPGtpLclAddr,
                jnxMbgSgwPPGtpRtgInst }
    ::= { jnxMbgSgwGtpCPerPeerStatsTable 1}

JnxMbgSgwGtpPerPeerStatsEntry ::= SEQUENCE {
    jnxMbgSgwPPGtpRmtAddr           IpAddress,
    jnxMbgSgwPPGtpLclAddr           IpAddress,
    jnxMbgSgwPPGtpRtgInst           Unsigned32,
    jnxMbgSgwPPRxPacketsDropped     Counter64,
    jnxMbgSgwPPPacketAllocFail      Counter64,
    jnxMbgSgwPPPacketSendFail       Counter64,
    jnxMbgSgwPPIPVerErrRx           Counter64,
    jnxMbgSgwPPIPProtoErrRx         Counter64,
    jnxMbgSgwPPGTPPortErrRx         Counter64,
    jnxMbgSgwPPGTPUnknVerRx         Counter64,
    jnxMbgSgwPPPcktLenErrRx         Counter64,
    jnxMbgSgwPPUnknMsgRx            Counter64,
    jnxMbgSgwPPProtocolErrRx        Counter64,
    jnxMbgSgwPPUnSupportedMsgRx     Counter64,
    jnxMbgSgwPPT3RespTmrExpRx       Counter64,
    jnxMbgSgwPPV2NumMsgRx           Counter64,
    jnxMbgSgwPPV2NumMsgTx           Counter64,
    jnxMbgSgwPPV2NumBytesRx         Counter64,
    jnxMbgSgwPPV2NumBytesTx         Counter64,
    jnxMbgSgwPPV2EchoReqRx          Counter64,
    jnxMbgSgwPPV2EchoReqTx          Counter64,
    jnxMbgSgwPPV2EchoRespRx         Counter64,
    jnxMbgSgwPPV2EchoRespTx         Counter64,
    jnxMbgSgwPPV2VerNotSupRx        Counter64,
    jnxMbgSgwPPV2VerNotSupTx        Counter64,
    jnxMbgSgwPPCreateSessReqRx      Counter64,
    jnxMbgSgwPPCreateSessReqTx      Counter64,
    jnxMbgSgwPPCreateSessRspRx      Counter64,
    jnxMbgSgwPPCreateSessRspTx      Counter64,
    jnxMbgSgwPPModBrReqRx           Counter64,
    jnxMbgSgwPPModBrReqTx           Counter64,
    jnxMbgSgwPPModBrRspRx           Counter64,
    jnxMbgSgwPPModBrRspTx           Counter64,
    jnxMbgSgwPPDelSessReqRx         Counter64,
    jnxMbgSgwPPDelSessReqTx         Counter64,
    jnxMbgSgwPPDelSessRspRx         Counter64,
    jnxMbgSgwPPDelSessRspTx         Counter64,
    jnxMbgSgwPPCrtBrReqRx           Counter64,
    jnxMbgSgwPPCrtBrReqTx           Counter64,
    jnxMbgSgwPPCrtBrRspRx           Counter64,
    jnxMbgSgwPPCrtBrRspTx           Counter64,
    jnxMbgSgwPPUpdBrReqRx           Counter64,
    jnxMbgSgwPPUpdBrReqTx           Counter64,
    jnxMbgSgwPPUpdBrRspRx           Counter64,
    jnxMbgSgwPPUpdBrRspTx           Counter64,
    jnxMbgSgwPPDelBrReqRx           Counter64,
    jnxMbgSgwPPDelBrReqTx           Counter64,
    jnxMbgSgwPPDelBrRspRx           Counter64,
    jnxMbgSgwPPDelBrRspTx           Counter64,
    jnxMbgSgwPPDelConnSetReqRx      Counter64,
    jnxMbgSgwPPDelConnSetReqTx      Counter64,
    jnxMbgSgwPPDelConnSetRspRx      Counter64,
    jnxMbgSgwPPDelConnSetRspTx      Counter64,
    jnxMbgSgwPPUpdConnSetReqRx      Counter64,
    jnxMbgSgwPPUpdConnSetReqTx      Counter64,
    jnxMbgSgwPPUpdConnSetRspRx      Counter64,
    jnxMbgSgwPPUpdConnSetRspTx      Counter64,
    jnxMbgSgwPPModBrCmdRx           Counter64,
    jnxMbgSgwPPModBrCmdTx           Counter64,
    jnxMbgSgwPPModBrFlrIndRx        Counter64,
    jnxMbgSgwPPModBrFlrIndTx        Counter64,
    jnxMbgSgwPPDelBrCmdRx           Counter64,
    jnxMbgSgwPPDelBrCmdTx           Counter64,
    jnxMbgSgwPPDelBrFlrIndRx        Counter64,
    jnxMbgSgwPPDelBrFlrIndTx        Counter64,
    jnxMbgSgwPPBrResCmdRx           Counter64,
    jnxMbgSgwPPBrResCmdTx           Counter64,
    jnxMbgSgwPPBrResFlrIndRx        Counter64,
    jnxMbgSgwPPBrResFlrIndTx        Counter64,
    jnxMbgSgwPPRelAcsBrReqRx        Counter64,
    jnxMbgSgwPPRelAcsBrReqTx        Counter64,
    jnxMbgSgwPPRelAcsBrRespRx       Counter64,
    jnxMbgSgwPPRelAcsBrRespTx       Counter64,
    jnxMbgSgwPPCrIndTunReqRx        Counter64,
    jnxMbgSgwPPCrIndTunReqTx        Counter64,
    jnxMbgSgwPPCrIndTunRespRx       Counter64,
    jnxMbgSgwPPCrIndTunRespTx       Counter64,
    jnxMbgSgwPPDelIndTunReqRx       Counter64,
    jnxMbgSgwPPDelIndTunReqTx       Counter64,
    jnxMbgSgwPPDelIndTunRespRx      Counter64,
    jnxMbgSgwPPDelIndTunRespTx      Counter64,
    jnxMbgSgwPPDlDataNotifRx        Counter64,
    jnxMbgSgwPPDlDataNotifTx        Counter64,
    jnxMbgSgwPPDlDataAckRx          Counter64,
    jnxMbgSgwPPDlDataAckTx          Counter64,
    jnxMbgSgwPPDlDataNotiFlrIndRx   Counter64,
    jnxMbgSgwPPDlDataNotiFlrIndTx   Counter64,
    jnxMbgSgwPPStopPagingIndRx      Counter64,
    jnxMbgSgwPPStopPagingIndTx      Counter64,
    jnxMbgSgwPPGtpV2ICsPageRx       Counter64,
    jnxMbgSgwPPGtpV2ICsPageTx       Counter64,
    jnxMbgSgwPPGtpV2ICsReqAcceptRx  Counter64,
    jnxMbgSgwPPGtpV2ICsReqAcceptTx  Counter64,
    jnxMbgSgwPPGtpV2ICsAcceptPartRx Counter64,
    jnxMbgSgwPPGtpV2ICsAcceptPartTx Counter64,
    jnxMbgSgwPPGtpV2ICsNewPTNPrefRx Counter64,
    jnxMbgSgwPPGtpV2ICsNewPTNPrefTx Counter64,
    jnxMbgSgwPPGtpV2ICsNPTSIAdbrRx  Counter64,
    jnxMbgSgwPPGtpV2ICsNPTSIAdbrTx  Counter64,
    jnxMbgSgwPPGtpV2ICsCtxNotFndRx  Counter64,
    jnxMbgSgwPPGtpV2ICsCtxNotFndTx  Counter64,
    jnxMbgSgwPPGtpV2ICsInvMsgFmtRx  Counter64,
    jnxMbgSgwPPGtpV2ICsInvMsgFmtTx  Counter64,
    jnxMbgSgwPPGtpV2ICsVerNotSuppRx Counter64,
    jnxMbgSgwPPGtpV2ICsVerNotSuppTx Counter64,
    jnxMbgSgwPPGtpV2ICsInvLenRx     Counter64,
    jnxMbgSgwPPGtpV2ICsInvLenTx     Counter64,
    jnxMbgSgwPPGtpV2ICsServNotSupRx Counter64,
    jnxMbgSgwPPGtpV2ICsServNotSupTx Counter64,
    jnxMbgSgwPPGtpV2ICsManIEIncorRx Counter64,
    jnxMbgSgwPPGtpV2ICsManIEIncorTx Counter64,
    jnxMbgSgwPPGtpV2ICsManIEMissRx  Counter64,
    jnxMbgSgwPPGtpV2ICsManIEMissTx  Counter64,
    jnxMbgSgwPPGtpV2ICsOptIEIncorRx Counter64,
    jnxMbgSgwPPGtpV2ICsOptIEIncorTx Counter64,
    jnxMbgSgwPPGtpV2ICsSysFailRx    Counter64,
    jnxMbgSgwPPGtpV2ICsSysFailTx    Counter64,
    jnxMbgSgwPPGtpV2ICsNoResRx      Counter64,
    jnxMbgSgwPPGtpV2ICsNoResTx      Counter64,
    jnxMbgSgwPPGtpV2ICsTFTSMANTErRx Counter64,
    jnxMbgSgwPPGtpV2ICsTFTSMANTErTx Counter64,
    jnxMbgSgwPPGtpV2ICsTFTSysErrRx  Counter64,
    jnxMbgSgwPPGtpV2ICsTFTSysErrTx  Counter64,
    jnxMbgSgwPPGtpV2ICsPkFltManErRx Counter64,
    jnxMbgSgwPPGtpV2ICsPkFltManErTx Counter64,
    jnxMbgSgwPPGtpV2ICsPkFltSynErRx Counter64,
    jnxMbgSgwPPGtpV2ICsPkFltSynErTx Counter64,
    jnxMbgSgwPPGtpV2ICsMisUnknAPNRx Counter64,
    jnxMbgSgwPPGtpV2ICsMisUnknAPNTx Counter64,
    jnxMbgSgwPPGtpV2ICsUnexpRptIERx Counter64,
    jnxMbgSgwPPGtpV2ICsUnexpRptIETx Counter64,
    jnxMbgSgwPPGtpV2ICsGREKeyNtFdRx Counter64,
    jnxMbgSgwPPGtpV2ICsGREKeyNtFdTx Counter64,
    jnxMbgSgwPPGtpV2ICsRelocFailRx  Counter64,
    jnxMbgSgwPPGtpV2ICsRelocFailTx  Counter64,
    jnxMbgSgwPPGtpV2ICsDenINRatRx   Counter64,
    jnxMbgSgwPPGtpV2ICsDenINRatTx   Counter64,
    jnxMbgSgwPPGtpV2ICsPTNotSuppRx  Counter64,
    jnxMbgSgwPPGtpV2ICsPTNotSuppTx  Counter64,
    jnxMbgSgwPPGtpV2ICsAllDynAdOcRx Counter64,
    jnxMbgSgwPPGtpV2ICsAllDynAdOcTx Counter64,
    jnxMbgSgwPPGtpV2ICsNOTFTUECTXRx Counter64,
    jnxMbgSgwPPGtpV2ICsNOTFTUECTXTx Counter64,
    jnxMbgSgwPPGtpV2ICsProtoNtSupRx Counter64,
    jnxMbgSgwPPGtpV2ICsProtoNtSupTx Counter64,
    jnxMbgSgwPPGtpV2ICsUENotRespRx  Counter64,
    jnxMbgSgwPPGtpV2ICsUENotRespTx  Counter64,
    jnxMbgSgwPPGtpV2ICsUERefusesRx  Counter64,
    jnxMbgSgwPPGtpV2ICsUERefusesTx  Counter64,
    jnxMbgSgwPPGtpV2ICsServDeniedRx Counter64,
    jnxMbgSgwPPGtpV2ICsServDeniedTx Counter64,
    jnxMbgSgwPPGtpV2ICsUnabPageUERx Counter64,
    jnxMbgSgwPPGtpV2ICsUnabPageUETx Counter64,
    jnxMbgSgwPPGtpV2ICsNoMemRx      Counter64,
    jnxMbgSgwPPGtpV2ICsNoMemTx      Counter64,
    jnxMbgSgwPPGtpV2ICsUserAUTHFlRx Counter64,
    jnxMbgSgwPPGtpV2ICsUserAUTHFlTx Counter64,
    jnxMbgSgwPPGtpV2ICsAPNAcsDenRx  Counter64,
    jnxMbgSgwPPGtpV2ICsAPNAcsDenTx  Counter64,
    jnxMbgSgwPPGtpV2ICsReqRejRx     Counter64,
    jnxMbgSgwPPGtpV2ICsReqRejTx     Counter64,
    jnxMbgSgwPPGtpV2ICsPTMSISigMMRx Counter64,
    jnxMbgSgwPPGtpV2ICsPTMSISigMMTx Counter64,
    jnxMbgSgwPPGtpV2ICsIMSINotKnRx  Counter64,
    jnxMbgSgwPPGtpV2ICsIMSINotKnTx  Counter64,
    jnxMbgSgwPPGtpV2ICsCondIEMsRx   Counter64,
    jnxMbgSgwPPGtpV2ICsCondIEMsTx   Counter64,
    jnxMbgSgwPPGtpV2ICsAPNResTIncRx Counter64,
    jnxMbgSgwPPGtpV2ICsAPNResTIncTx Counter64,
    jnxMbgSgwPPGtpV2ICsUnknownRx    Counter64,
    jnxMbgSgwPPGtpV2ICsUnknownTx    Counter64,
    jnxMbgSgwPPGtpV2ICsLclDetRx     Counter64,
    jnxMbgSgwPPGtpV2ICsLclDetTx     Counter64,
    jnxMbgSgwPPGtpV2ICsCmpDetRx     Counter64,
    jnxMbgSgwPPGtpV2ICsCmpDetTx     Counter64,
    jnxMbgSgwPPGtpV2ICsRATChgRx     Counter64,
    jnxMbgSgwPPGtpV2ICsRATChgTx     Counter64,
    jnxMbgSgwPPGtpV2ICsISRDeactRx   Counter64,
    jnxMbgSgwPPGtpV2ICsISRDeactTx   Counter64,
    jnxMbgSgwPPGtpV2ICsEIFRNCEnRx   Counter64,
    jnxMbgSgwPPGtpV2ICsEIFRNCEnTx   Counter64,
    jnxMbgSgwPPGtpV2ICsSemErTADRx   Counter64,
    jnxMbgSgwPPGtpV2ICsSemErTADTx   Counter64,
    jnxMbgSgwPPGtpV2ICsSynErTADRx   Counter64,
    jnxMbgSgwPPGtpV2ICsSynErTADTx   Counter64,
    jnxMbgSgwPPGtpV2ICsRMValRcvRx   Counter64,
    jnxMbgSgwPPGtpV2ICsRMValRcvTx   Counter64,
    jnxMbgSgwPPGtpV2ICsRPrNtRspRx   Counter64,
    jnxMbgSgwPPGtpV2ICsRPrNtRspTx   Counter64,
    jnxMbgSgwPPGtpV2ICsColNWReqRx   Counter64,
    jnxMbgSgwPPGtpV2ICsColNWReqTx   Counter64,
    jnxMbgSgwPPGtpV2ICsUnPgUESusRx  Counter64,
    jnxMbgSgwPPGtpV2ICsUnPgUESusTx  Counter64,
    jnxMbgSgwPPGtpV2ICsInvTotLenRx  Counter64,
    jnxMbgSgwPPGtpV2ICsInvTotLenTx  Counter64,
    jnxMbgSgwPPGtpV2ICsDtForNtSupRx Counter64,
    jnxMbgSgwPPGtpV2ICsDtForNtSupTx Counter64,
    jnxMbgSgwPPGtpV2ICsInReFRePrRx  Counter64,
    jnxMbgSgwPPGtpV2ICsInReFRePrTx  Counter64,
    jnxMbgSgwPPGtpV2ICsInvPrRx      Counter64,
    jnxMbgSgwPPGtpV2ICsInvPrTx      Counter64,
    jnxMbgSgwPPGtpV1ProtocolErrRx   Counter64,
    jnxMbgSgwPPGtpV1UnSupMsgRx      Counter64,
    jnxMbgSgwPPGtpV1T3RespTmrExpRx  Counter64,
    jnxMbgSgwPPGtpV1EndMarkerRx     Counter64,
    jnxMbgSgwPPGtpV1EndMarkerTx     Counter64,
    jnxMbgSgwPPGtpV1EchoReqRx       Counter64,
    jnxMbgSgwPPGtpV1EchoReqTx       Counter64,
    jnxMbgSgwPPGtpV1EchoRespRx      Counter64,
    jnxMbgSgwPPGtpV1EchoRespTx      Counter64,
    jnxMbgSgwPPGtpV1ErrIndRx        Counter64,
    jnxMbgSgwPPGtpV1ErrIndTx        Counter64,
    jnxMbgSgwPPSuspNotifRx          Counter64,
    jnxMbgSgwPPSuspNotifTx          Counter64,
    jnxMbgSgwPPSuspAckRx            Counter64,
    jnxMbgSgwPPSuspAckTx            Counter64,
    jnxMbgSgwPPResumeNotifRx        Counter64,
    jnxMbgSgwPPResumeNotifTx        Counter64,
    jnxMbgSgwPPResumeAckRx          Counter64,
    jnxMbgSgwPPResumeAckTx          Counter64,
    jnxMbgSgwPPPiggybackMsgRx       Counter64,
    jnxMbgSgwPPPiggybackMsgTx       Counter64
}

jnxMbgSgwPPGtpRmtAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Remote IP address of this GTP peer entry."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 1 }

jnxMbgSgwPPGtpLclAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Local IP address of this GTP peer entry."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 2 }

jnxMbgSgwPPGtpRtgInst OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The Routing Instance for this Peer."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 3 }

jnxMbgSgwPPRxPacketsDropped  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Received Packets Dropped."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 4 }

jnxMbgSgwPPPacketAllocFail  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet allocation failures."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 5 }

jnxMbgSgwPPPacketSendFail OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet Send failures."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 6 }

jnxMbgSgwPPIPVerErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of IP Version Error Packets Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 7 }

jnxMbgSgwPPIPProtoErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of IP Protocol Error packets Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 8 }

jnxMbgSgwPPGTPPortErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Port Error Packets Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 9 }

jnxMbgSgwPPGTPUnknVerRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Unknown Version Packets Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 10 }

jnxMbgSgwPPPcktLenErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet Length Error Packets Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 11 }

jnxMbgSgwPPUnknMsgRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Unknown Messages Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 12 }

jnxMbgSgwPPProtocolErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 Protocol Errors Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 13 }

jnxMbgSgwPPUnSupportedMsgRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 Unsupported Messages received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 14 }

jnxMbgSgwPPT3RespTmrExpRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 T3 timer expiries Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 15 }

jnxMbgSgwPPV2NumMsgRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 messages received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 16 }

jnxMbgSgwPPV2NumMsgTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 messages sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 17 }

jnxMbgSgwPPV2NumBytesRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 bytes received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 18 }

jnxMbgSgwPPV2NumBytesTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 bytes sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 19 }

jnxMbgSgwPPV2EchoReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Request received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 20 }

jnxMbgSgwPPV2EchoReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Request Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 21 }

jnxMbgSgwPPV2EchoRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Response received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 22 }

jnxMbgSgwPPV2EchoRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Response Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 23 }

jnxMbgSgwPPV2VerNotSupRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Version Not supported messages received"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 24 }

jnxMbgSgwPPV2VerNotSupTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Number of version not supported messages sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 25 }

jnxMbgSgwPPCreateSessReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Requests received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 26 }

jnxMbgSgwPPCreateSessReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Requests Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 27 }

jnxMbgSgwPPCreateSessRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Responses received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 28 }

jnxMbgSgwPPCreateSessRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Responses Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 29 }

jnxMbgSgwPPModBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Requests received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 30 }

jnxMbgSgwPPModBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Requests Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 31 }

jnxMbgSgwPPModBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Responses received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 32 }

jnxMbgSgwPPModBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Responses Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 33 }

jnxMbgSgwPPDelSessReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Requests received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 34 }

jnxMbgSgwPPDelSessReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Requests Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 35 }

jnxMbgSgwPPDelSessRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Responses received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 36 }

jnxMbgSgwPPDelSessRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Responses Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 37 }
jnxMbgSgwPPCrtBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Requests received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 38 }

jnxMbgSgwPPCrtBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Requests Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 39 }

jnxMbgSgwPPCrtBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Response received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 40 }

jnxMbgSgwPPCrtBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Response Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 41 }

jnxMbgSgwPPUpdBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Request received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 42 }

jnxMbgSgwPPUpdBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Request Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 43 }

jnxMbgSgwPPUpdBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Response received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 44 }

jnxMbgSgwPPUpdBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Response Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 45 }

jnxMbgSgwPPDelBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Request received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 46 }

jnxMbgSgwPPDelBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Request Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 47 }

jnxMbgSgwPPDelBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Response received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 48 }

jnxMbgSgwPPDelBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Response Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 49 }

jnxMbgSgwPPDelConnSetReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Request received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 50 }

jnxMbgSgwPPDelConnSetReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Request Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 51 }

jnxMbgSgwPPDelConnSetRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Response received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 52 }

jnxMbgSgwPPDelConnSetRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Response Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 53 }

jnxMbgSgwPPUpdConnSetReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Request received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 54 }

jnxMbgSgwPPUpdConnSetReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Request Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 55 }

jnxMbgSgwPPUpdConnSetRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Response received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 56 }

jnxMbgSgwPPUpdConnSetRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Response Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 57 }

jnxMbgSgwPPModBrCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Command received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 58 }

jnxMbgSgwPPModBrCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Command Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 59 }

jnxMbgSgwPPModBrFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Failure received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 60 }

jnxMbgSgwPPModBrFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Failure Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 61 }

jnxMbgSgwPPDelBrCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Command received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 62 }

jnxMbgSgwPPDelBrCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Command Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 63 }

jnxMbgSgwPPDelBrFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Failure received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 64 }

jnxMbgSgwPPDelBrFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Failure Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 65 }

jnxMbgSgwPPBrResCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Response Command received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 66 }

jnxMbgSgwPPBrResCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Response Command Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 67 }

jnxMbgSgwPPBrResFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Resource Failure received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 68 }

jnxMbgSgwPPBrResFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Resource Failure Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 69 }

jnxMbgSgwPPRelAcsBrReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Requests received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 70 }

jnxMbgSgwPPRelAcsBrReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Requests sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 71 }

jnxMbgSgwPPRelAcsBrRespRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Response received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 72 }

jnxMbgSgwPPRelAcsBrRespTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Response sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 73 }

jnxMbgSgwPPCrIndTunReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Request Received"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 74 }

jnxMbgSgwPPCrIndTunReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Request sent"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 75 }

jnxMbgSgwPPCrIndTunRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Response Received"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 76 }

jnxMbgSgwPPCrIndTunRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Response sent"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 77 }

jnxMbgSgwPPDelIndTunReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Request Received"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 78 }

jnxMbgSgwPPDelIndTunReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Request sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 79 }

jnxMbgSgwPPDelIndTunRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Response Received"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 80 }

jnxMbgSgwPPDelIndTunRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Response sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 81 }

jnxMbgSgwPPDlDataNotifRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 82 }

jnxMbgSgwPPDlDataNotifTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 83 }

jnxMbgSgwPPDlDataAckRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Acknowledgement received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 84 }

jnxMbgSgwPPDlDataAckTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Acknowledgement Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 85 }

jnxMbgSgwPPDlDataNotiFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notification fail received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 86 }

jnxMbgSgwPPDlDataNotiFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notification fail Sent."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 87 }

jnxMbgSgwPPStopPagingIndRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Number of Stop Paging Indication Messages Received."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 88 }

jnxMbgSgwPPStopPagingIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Number of Stop Paging Indicaton messages sent"
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 89 }

jnxMbgSgwPPGtpV2ICsPageRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
    "Number of GTPV2 packets received with cause Page."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 90 }

jnxMbgSgwPPGtpV2ICsPageTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      obsolete
    DESCRIPTION
    "Number of GTP packets sent with cause Page."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 91 }

jnxMbgSgwPPGtpV2ICsReqAcceptRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Request Accept."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 92 }

jnxMbgSgwPPGtpV2ICsReqAcceptTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Request Accept."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 93 }

jnxMbgSgwPPGtpV2ICsAcceptPartRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Accept Partial."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 94 }

jnxMbgSgwPPGtpV2ICsAcceptPartTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Accept Partial."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 95 }

jnxMbgSgwPPGtpV2ICsNewPTNPrefRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause New PDN type due to Network Preference."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 96 }

jnxMbgSgwPPGtpV2ICsNewPTNPrefTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause New PDN type due to Network Preference."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 97 }


jnxMbgSgwPPGtpV2ICsNPTSIAdbrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause New PDN type due to Single Address Bearer."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 98 }

jnxMbgSgwPPGtpV2ICsNPTSIAdbrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause New PDN type due to Single Address Bearer."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 99 }

jnxMbgSgwPPGtpV2ICsCtxNotFndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Context not found."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 100 }

jnxMbgSgwPPGtpV2ICsCtxNotFndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Context not found."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 101 }

jnxMbgSgwPPGtpV2ICsInvMsgFmtRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Invalid Message Format."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 102 }

jnxMbgSgwPPGtpV2ICsInvMsgFmtTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Invalid Message Format."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 103 }

jnxMbgSgwPPGtpV2ICsVerNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Version not Supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 104 }

jnxMbgSgwPPGtpV2ICsVerNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Version not Supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 105 }

jnxMbgSgwPPGtpV2ICsInvLenRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Invalid Length."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 106 }

jnxMbgSgwPPGtpV2ICsInvLenTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Invalid Length."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 107 }

jnxMbgSgwPPGtpV2ICsServNotSupRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Service Not supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 108 }

jnxMbgSgwPPGtpV2ICsServNotSupTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Service Not supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 109 }

jnxMbgSgwPPGtpV2ICsManIEIncorRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Mandatory IE incorrect."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 110 }

jnxMbgSgwPPGtpV2ICsManIEIncorTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Mandatory IE incorrect."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 111 }

jnxMbgSgwPPGtpV2ICsManIEMissRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Mandatory IE Missing."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 112 }

jnxMbgSgwPPGtpV2ICsManIEMissTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Mandatory IE Missing."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 113 }

jnxMbgSgwPPGtpV2ICsOptIEIncorRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Optional IE Incorrect."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 114 }

jnxMbgSgwPPGtpV2ICsOptIEIncorTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Optional IE Incorrect."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 115 }

jnxMbgSgwPPGtpV2ICsSysFailRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause System Failure."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 116 }

jnxMbgSgwPPGtpV2ICsSysFailTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause System Failure."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 117 }

jnxMbgSgwPPGtpV2ICsNoResRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause No Resource."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 118 }

jnxMbgSgwPPGtpV2ICsNoResTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause No Resource."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 119 }

jnxMbgSgwPPGtpV2ICsTFTSMANTErRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause TFT Symantic Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 120 }

jnxMbgSgwPPGtpV2ICsTFTSMANTErTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause TFT Symantic Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 121 }

jnxMbgSgwPPGtpV2ICsTFTSysErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause TFT System Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 122 }

jnxMbgSgwPPGtpV2ICsTFTSysErrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause TFT System Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 123 }

jnxMbgSgwPPGtpV2ICsPkFltManErRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Packet Filter Symantic Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 124 }

jnxMbgSgwPPGtpV2ICsPkFltManErTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Packet Filter Symantic Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 125 }

jnxMbgSgwPPGtpV2ICsPkFltSynErRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Packet Filter Syntax Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 126 }

jnxMbgSgwPPGtpV2ICsPkFltSynErTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Packet Filter Syntax Error."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 127 }

jnxMbgSgwPPGtpV2ICsMisUnknAPNRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unknown APN."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 128 }

jnxMbgSgwPPGtpV2ICsMisUnknAPNTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unknown APN."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 129 }

jnxMbgSgwPPGtpV2ICsUnexpRptIERx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unexpected Repeated IE."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 130 }

jnxMbgSgwPPGtpV2ICsUnexpRptIETx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unexpected Repeated IE."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 131 }

jnxMbgSgwPPGtpV2ICsGREKeyNtFdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause GRE Key Not Found."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 132 }

jnxMbgSgwPPGtpV2ICsGREKeyNtFdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause GRE Key Not Found."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 133 }

jnxMbgSgwPPGtpV2ICsRelocFailRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Relocation Failed."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 134 }

jnxMbgSgwPPGtpV2ICsRelocFailTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Relocation Failed."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 135 }

jnxMbgSgwPPGtpV2ICsDenINRatRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Denied in RAT."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 136 }

jnxMbgSgwPPGtpV2ICsDenINRatTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Denied in RAT."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 137 }

jnxMbgSgwPPGtpV2ICsPTNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause PDN Type Not Supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 138 }

jnxMbgSgwPPGtpV2ICsPTNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause PDN Type Not Supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 139 }

jnxMbgSgwPPGtpV2ICsAllDynAdOcRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Allocated Dynamic Address Occupied."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 140 }

jnxMbgSgwPPGtpV2ICsAllDynAdOcTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Allocated Dynamic Address Occupied."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 141 }

jnxMbgSgwPPGtpV2ICsNOTFTUECTXRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Context Without TFT Exists."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 142 }

jnxMbgSgwPPGtpV2ICsNOTFTUECTXTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Context Without TFT Exists."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 143 }

jnxMbgSgwPPGtpV2ICsProtoNtSupRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Protocol Not Supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 144 }

jnxMbgSgwPPGtpV2ICsProtoNtSupTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Protocol Not Supported."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 145 }

jnxMbgSgwPPGtpV2ICsUENotRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Not Responding."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 146 }

jnxMbgSgwPPGtpV2ICsUENotRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Not Responding."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 147 }

jnxMbgSgwPPGtpV2ICsUERefusesRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Refuses."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 148 }

jnxMbgSgwPPGtpV2ICsUERefusesTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Refuses."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 149 }

jnxMbgSgwPPGtpV2ICsServDeniedRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Service Denied."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 150 }

jnxMbgSgwPPGtpV2ICsServDeniedTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Service Denied."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 151 }

jnxMbgSgwPPGtpV2ICsUnabPageUERx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unable to Page UE."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 152 }

jnxMbgSgwPPGtpV2ICsUnabPageUETx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unable to Page UE."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 153 }

jnxMbgSgwPPGtpV2ICsNoMemRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause No Memory."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 154 }

jnxMbgSgwPPGtpV2ICsNoMemTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause No Memory."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 155 }

jnxMbgSgwPPGtpV2ICsUserAUTHFlRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause User AUTH Failed."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 156 }

jnxMbgSgwPPGtpV2ICsUserAUTHFlTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause User AUTH Failed."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 157 }

jnxMbgSgwPPGtpV2ICsAPNAcsDenRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause APN Access Denied."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 158 }

jnxMbgSgwPPGtpV2ICsAPNAcsDenTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause APN Access Denied."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 159 }

jnxMbgSgwPPGtpV2ICsReqRejRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Request Rejected."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 160 }

jnxMbgSgwPPGtpV2ICsReqRejTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Request Rejected."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 161 }

jnxMbgSgwPPGtpV2ICsPTMSISigMMRx OBJECT-TYPE
     SYNTAX      Counter64
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of GTPV2 packets received with cause P-TMSI Signature Mismatch."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 162 }

jnxMbgSgwPPGtpV2ICsPTMSISigMMTx OBJECT-TYPE
     SYNTAX      Counter64
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of GTP packets sent with cause P-TMSI Signature Mismatch."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 163 }

jnxMbgSgwPPGtpV2ICsIMSINotKnRx OBJECT-TYPE
      SYNTAX      Counter64
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
      "Number of GTPV2 packets received with cause IMSI Not Known."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 164 }

jnxMbgSgwPPGtpV2ICsIMSINotKnTx OBJECT-TYPE
      SYNTAX      Counter64
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
      "Number of GTP packets sent with cause IMSI Not Known."
    ::= { jnxMbgSgwGtpPerPeerStatsEntry 165 }

jnxMbgSgwPPGtpV2ICsCondIEMsRx OBJECT-TYPE
       SYNTAX      Counter64
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
       "Number of GTPV2 packets received with cause Conditional IE Missing."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 166 }

jnxMbgSgwPPGtpV2ICsCondIEMsTx OBJECT-TYPE
       SYNTAX      Counter64
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
       "Number of GTP packets sent with cause Conditional IE Missing."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 167 }

jnxMbgSgwPPGtpV2ICsAPNResTIncRx OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of GTPV2 packets received with cause APN Restriction Type Incompatible."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 168 }

jnxMbgSgwPPGtpV2ICsAPNResTIncTx OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of GTP packets sent with cause APN Restriction Type Incompatible."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 169 }

jnxMbgSgwPPGtpV2ICsUnknownRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPV2 packets received with cause Unknown."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 170 }

jnxMbgSgwPPGtpV2ICsUnknownTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Unknown."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 171 }

jnxMbgSgwPPGtpV2ICsLclDetRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Local Detach."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 172 }

jnxMbgSgwPPGtpV2ICsLclDetTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Local Detach."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 173 }

jnxMbgSgwPPGtpV2ICsCmpDetRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Complete Detach."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 174 }

jnxMbgSgwPPGtpV2ICsCmpDetTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Complete Detach."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 175 }

jnxMbgSgwPPGtpV2ICsRATChgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause RAT changed from 3GPP to non 3GPP."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 176 }

jnxMbgSgwPPGtpV2ICsRATChgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause RAT changed from 3GPP to non 3GPP."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 177 }

jnxMbgSgwPPGtpV2ICsISRDeactRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause ISR Deactivated."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 178 }

jnxMbgSgwPPGtpV2ICsISRDeactTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause ISR Deactivated."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 179 }

jnxMbgSgwPPGtpV2ICsEIFRNCEnRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Error Indication from RNC eNodeB."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 180 }

jnxMbgSgwPPGtpV2ICsEIFRNCEnTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Error Indication from RNC eNodeB."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 181 }

jnxMbgSgwPPGtpV2ICsSemErTADRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Semantic Error in TAD Operation."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 182 }

jnxMbgSgwPPGtpV2ICsSemErTADTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Semantic Error in TAD Operation."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 183 }

jnxMbgSgwPPGtpV2ICsSynErTADRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Syntactic Error in TAD Operation."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 184 }

jnxMbgSgwPPGtpV2ICsSynErTADTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Syntactic Error in TAD Operation."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 185 }

jnxMbgSgwPPGtpV2ICsRMValRcvRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Reserved Message Value Received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 186 }

jnxMbgSgwPPGtpV2ICsRMValRcvTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Reserved Message Value Received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 187 }

jnxMbgSgwPPGtpV2ICsRPrNtRspRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Remote peer not responding."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 188 }

jnxMbgSgwPPGtpV2ICsRPrNtRspTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Remote peer not responding."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 189 }

jnxMbgSgwPPGtpV2ICsColNWReqRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Collision with network initiated request."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 190 }

jnxMbgSgwPPGtpV2ICsColNWReqTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Collision with network initiated request."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 191 }

jnxMbgSgwPPGtpV2ICsUnPgUESusRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Unable to page UE due to suspension."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 192 }

jnxMbgSgwPPGtpV2ICsUnPgUESusTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Unable to page UE due to suspension."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 193 }

jnxMbgSgwPPGtpV2ICsInvTotLenRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid total len."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 194 }

jnxMbgSgwPPGtpV2ICsInvTotLenTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid total len."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 195 }

jnxMbgSgwPPGtpV2ICsDtForNtSupRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Data forwarding not supported."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 196 }

jnxMbgSgwPPGtpV2ICsDtForNtSupTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Data forwarding not supported."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 197 }

jnxMbgSgwPPGtpV2ICsInReFRePrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid Reply from Remote peer."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 198 }

jnxMbgSgwPPGtpV2ICsInReFRePrTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid Reply from Remote peer."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 199 }

jnxMbgSgwPPGtpV2ICsInvPrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid peer."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 200 }

jnxMbgSgwPPGtpV2ICsInvPrTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid peer."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 201 }

jnxMbgSgwPPGtpV1ProtocolErrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv1 Protocol Errors Received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 202 }

jnxMbgSgwPPGtpV1UnSupMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv1 Unsupported Messages received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 203 }

jnxMbgSgwPPGtpV1T3RespTmrExpRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 T3 timer expiries Received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 204 }

jnxMbgSgwPPGtpV1EndMarkerRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 end marker packets received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 205 }

jnxMbgSgwPPGtpV1EndMarkerTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 end marker packets sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 206 }

jnxMbgSgwPPGtpV1EchoReqRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo request packets received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 207 }

jnxMbgSgwPPGtpV1EchoReqTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP iV1 echo request packets sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 208 }

jnxMbgSgwPPGtpV1EchoRespRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo response packets received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 209 }

jnxMbgSgwPPGtpV1EchoRespTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo response packets sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 210 }

jnxMbgSgwPPGtpV1ErrIndRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 Error Indication packets received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 211 }

jnxMbgSgwPPGtpV1ErrIndTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 Error Indication packets sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 212 }

jnxMbgSgwPPSuspNotifRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Notification messages received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 213 } 

jnxMbgSgwPPSuspNotifTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Notification messages sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 214 } 

jnxMbgSgwPPSuspAckRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Acknowledgement messages received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 215 }

jnxMbgSgwPPSuspAckTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Acknowledgement messages sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 216 }

jnxMbgSgwPPResumeNotifRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Notification messages received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 217 }
    
jnxMbgSgwPPResumeNotifTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Notification messages sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 218 }

jnxMbgSgwPPResumeAckRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Acknowledgement messages received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 219 }
   
jnxMbgSgwPPResumeAckTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Acknowledgement messages sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 220 }

jnxMbgSgwPPPiggybackMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Piggyback messages received."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 221 }
    
jnxMbgSgwPPPiggybackMsgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S11 Piggyback messages sent."
     ::= { jnxMbgSgwGtpPerPeerStatsEntry 222 }

--
-- GTP Object for showing GTP Interface Statistics
--
jnxMbgSgwGtpIfStatsTable  OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxMbgSgwGtpIfStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry corresponds to an interface level GTP statistic."
    ::= { jnxMbgSgwGtpObjects 4 }

jnxMbgSgwGtpIfStatsEntry OBJECT-TYPE
    SYNTAX      JnxMbgSgwGtpIfStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A specification of the GTP interface level control Statistics."
    INDEX     { jnxMbgGwIndex,
                jnxMbgSgwIfIndex }
    ::= { jnxMbgSgwGtpIfStatsTable 1}

JnxMbgSgwGtpIfStatsEntry ::= SEQUENCE {
    jnxMbgSgwIfIndex                Unsigned32,
    jnxMbgSgwIfType                 DisplayString,
    jnxMbgSgwIfRxPacketsDropped     Counter64,
    jnxMbgSgwIfPacketAllocFail      Counter64,
    jnxMbgSgwIfPacketSendFail       Counter64,
    jnxMbgSgwIfIPVerErrRx           Counter64,
    jnxMbgSgwIfIPProtoErrRx         Counter64,
    jnxMbgSgwIfGTPPortErrRx         Counter64,
    jnxMbgSgwIfGTPUnknVerRx         Counter64,
    jnxMbgSgwIfPcktLenErrRx         Counter64,
    jnxMbgSgwIfUnknMsgRx            Counter64,
    jnxMbgSgwIfProtocolErrRx        Counter64,
    jnxMbgSgwIfUnSupportedMsgRx     Counter64,
    jnxMbgSgwIfT3RespTmrExpRx       Counter64,
    jnxMbgSgwIfV2NumMsgRx           Counter64,
    jnxMbgSgwIfV2NumMsgTx           Counter64,
    jnxMbgSgwIfV2NumBytesRx         Counter64,
    jnxMbgSgwIfV2NumBytesTx         Counter64,
    jnxMbgSgwIfV2EchoReqRx          Counter64,
    jnxMbgSgwIfV2EchoReqTx          Counter64,
    jnxMbgSgwIfV2EchoRespRx         Counter64,
    jnxMbgSgwIfV2EchoRespTx         Counter64,
    jnxMbgSgwIfV2VerNotSupRx        Counter64,
    jnxMbgSgwIfV2VerNotSupTx        Counter64,
    jnxMbgSgwIfCreateSessReqRx      Counter64,
    jnxMbgSgwIfCreateSessReqTx      Counter64,
    jnxMbgSgwIfCreateSessRspRx      Counter64,
    jnxMbgSgwIfCreateSessRspTx      Counter64,
    jnxMbgSgwIfModBrReqRx           Counter64,
    jnxMbgSgwIfModBrReqTx           Counter64,
    jnxMbgSgwIfModBrRspRx           Counter64,
    jnxMbgSgwIfModBrRspTx           Counter64,
    jnxMbgSgwIfDelSessReqRx         Counter64,
    jnxMbgSgwIfDelSessReqTx         Counter64,
    jnxMbgSgwIfDelSessRspRx         Counter64,
    jnxMbgSgwIfDelSessRspTx         Counter64,
    jnxMbgSgwIfCrtBrReqRx           Counter64,
    jnxMbgSgwIfCrtBrReqTx           Counter64,
    jnxMbgSgwIfCrtBrRspRx           Counter64,
    jnxMbgSgwIfCrtBrRspTx           Counter64,
    jnxMbgSgwIfUpdBrReqRx           Counter64,
    jnxMbgSgwIfUpdBrReqTx           Counter64,
    jnxMbgSgwIfUpdBrRspRx           Counter64,
    jnxMbgSgwIfUpdBrRspTx           Counter64,
    jnxMbgSgwIfDelBrReqRx           Counter64,
    jnxMbgSgwIfDelBrReqTx           Counter64,
    jnxMbgSgwIfDelBrRspRx           Counter64,
    jnxMbgSgwIfDelBrRspTx           Counter64,
    jnxMbgSgwIfDelConnSetReqRx      Counter64,
    jnxMbgSgwIfDelConnSetReqTx      Counter64,
    jnxMbgSgwIfDelConnSetRspRx      Counter64,
    jnxMbgSgwIfDelConnSetRspTx      Counter64,
    jnxMbgSgwIfUpdConnSetReqRx      Counter64,
    jnxMbgSgwIfUpdConnSetReqTx      Counter64,
    jnxMbgSgwIfUpdConnSetRspRx      Counter64,
    jnxMbgSgwIfUpdConnSetRspTx      Counter64,
    jnxMbgSgwIfModBrCmdRx           Counter64,
    jnxMbgSgwIfModBrCmdTx           Counter64,
    jnxMbgSgwIfModBrFlrIndRx        Counter64,
    jnxMbgSgwIfModBrFlrIndTx        Counter64,
    jnxMbgSgwIfDelBrCmdRx           Counter64,
    jnxMbgSgwIfDelBrCmdTx           Counter64,
    jnxMbgSgwIfDelBrFlrIndRx        Counter64,
    jnxMbgSgwIfDelBrFlrIndTx        Counter64,
    jnxMbgSgwIfBrResCmdRx           Counter64,
    jnxMbgSgwIfBrResCmdTx           Counter64,
    jnxMbgSgwIfBrResFlrIndRx        Counter64,
    jnxMbgSgwIfBrResFlrIndTx        Counter64,
    jnxMbgSgwIfRelAcsBrReqRx        Counter64,
    jnxMbgSgwIfRelAcsBrReqTx        Counter64,
    jnxMbgSgwIfRelAcsBrRespRx       Counter64,
    jnxMbgSgwIfRelAcsBrRespTx       Counter64,
    jnxMbgSgwIfCrIndTunReqRx        Counter64,
    jnxMbgSgwIfCrIndTunReqTx        Counter64,
    jnxMbgSgwIfCrIndTunRespRx       Counter64,
    jnxMbgSgwIfCrIndTunRespTx       Counter64,
    jnxMbgSgwIfDelIndTunReqRx       Counter64,
    jnxMbgSgwIfDelIndTunReqTx       Counter64,
    jnxMbgSgwIfDelIndTunRespRx      Counter64,
    jnxMbgSgwIfDelIndTunRespTx      Counter64,
    jnxMbgSgwIfDlDataNotifRx        Counter64,
    jnxMbgSgwIfDlDataNotifTx        Counter64,
    jnxMbgSgwIfDlDataAckRx          Counter64,
    jnxMbgSgwIfDlDataAckTx          Counter64,
    jnxMbgSgwIfDlDataNotiFlrIndRx   Counter64,
    jnxMbgSgwIfDlDataNotiFlrIndTx   Counter64,
    jnxMbgSgwIfStopPagingIndRx      Counter64,
    jnxMbgSgwIfStopPagingIndTx      Counter64,
    jnxMbgSgwIfGtpV2ICsReqAcceptRx  Counter64,
    jnxMbgSgwIfGtpV2ICsReqAcceptTx  Counter64,
    jnxMbgSgwIfGtpV2ICsAcceptPartRx Counter64,
    jnxMbgSgwIfGtpV2ICsAcceptPartTx Counter64,
    jnxMbgSgwIfGtpV2ICsNewPTNPrefRx Counter64,
    jnxMbgSgwIfGtpV2ICsNewPTNPrefTx Counter64,
    jnxMbgSgwIfGtpV2ICsNPTSIAdbrRx  Counter64,
    jnxMbgSgwIfGtpV2ICsNPTSIAdbrTx  Counter64,
    jnxMbgSgwIfGtpV2ICsCtxNotFndRx  Counter64,
    jnxMbgSgwIfGtpV2ICsCtxNotFndTx  Counter64,
    jnxMbgSgwIfGtpV2ICsInvMsgFmtRx  Counter64,
    jnxMbgSgwIfGtpV2ICsInvMsgFmtTx  Counter64,
    jnxMbgSgwIfGtpV2ICsVerNotSuppRx Counter64,
    jnxMbgSgwIfGtpV2ICsVerNotSuppTx Counter64,
    jnxMbgSgwIfGtpV2ICsInvLenRx     Counter64,
    jnxMbgSgwIfGtpV2ICsInvLenTx     Counter64,
    jnxMbgSgwIfGtpV2ICsSrvNotSuppRx Counter64,
    jnxMbgSgwIfGtpV2ICsSrvNotSuppTx Counter64,
    jnxMbgSgwIfGtpV2ICsManIEIncorRx Counter64,
    jnxMbgSgwIfGtpV2ICsManIEIncorTx Counter64,
    jnxMbgSgwIfGtpV2ICsManIEMissRx  Counter64,
    jnxMbgSgwIfGtpV2ICsManIEMissTx  Counter64,
    jnxMbgSgwIfGtpV2ICsOptIEIncorRx Counter64,
    jnxMbgSgwIfGtpV2ICsOptIEIncorTx Counter64,
    jnxMbgSgwIfGtpV2ICsSysFailRx    Counter64,
    jnxMbgSgwIfGtpV2ICsSysFailTx    Counter64,
    jnxMbgSgwIfGtpV2ICsNoResRx      Counter64,
    jnxMbgSgwIfGtpV2ICsNoResTx      Counter64,
    jnxMbgSgwIfGtpV2ICsTFTSMANTErRx Counter64,
    jnxMbgSgwIfGtpV2ICsTFTSMANTErTx Counter64,
    jnxMbgSgwIfGtpV2ICsTFTSysErrRx  Counter64,
    jnxMbgSgwIfGtpV2ICsTFTSysErrTx  Counter64,
    jnxMbgSgwIfGtpV2ICsPkFltManErRx Counter64,
    jnxMbgSgwIfGtpV2ICsPkFltManErTx Counter64,
    jnxMbgSgwIfGtpV2ICsPkFltSynErRx Counter64,
    jnxMbgSgwIfGtpV2ICsPkFltSynErTx Counter64,
    jnxMbgSgwIfGtpV2ICsMisUnknAPNRx Counter64,
    jnxMbgSgwIfGtpV2ICsMisUnknAPNTx Counter64,
    jnxMbgSgwIfGtpV2ICsUnexpRptIERx Counter64,
    jnxMbgSgwIfGtpV2ICsUnexpRptIETx Counter64,
    jnxMbgSgwIfGtpV2ICsGREKeyNtFdRx Counter64,
    jnxMbgSgwIfGtpV2ICsGREKeyNtFdTx Counter64,
    jnxMbgSgwIfGtpV2ICsRelocFailRx  Counter64,
    jnxMbgSgwIfGtpV2ICsRelocFailTx  Counter64,
    jnxMbgSgwIfGtpV2ICsDenINRatRx   Counter64,
    jnxMbgSgwIfGtpV2ICsDenINRatTx   Counter64,
    jnxMbgSgwIfGtpV2ICsPTNotSuppRx  Counter64,
    jnxMbgSgwIfGtpV2ICsPTNotSuppTx  Counter64,
    jnxMbgSgwIfGtpV2ICsAlDynAdOccRx Counter64,
    jnxMbgSgwIfGtpV2ICsAlDynAdOccTx Counter64,
    jnxMbgSgwIfGtpV2ICsNOTFTUECTXRx Counter64,
    jnxMbgSgwIfGtpV2ICsNOTFTUECTXTx Counter64,
    jnxMbgSgwIfGtpV2ICsProtoNtSupRx Counter64,
    jnxMbgSgwIfGtpV2ICsProtoNtSupTx Counter64,
    jnxMbgSgwIfGtpV2ICsUENotRespRx  Counter64,
    jnxMbgSgwIfGtpV2ICsUENotRespTx  Counter64,
    jnxMbgSgwIfGtpV2ICsUERefusesRx  Counter64,
    jnxMbgSgwIfGtpV2ICsUERefusesTx  Counter64,
    jnxMbgSgwIfGtpV2ICsServDeniedRx Counter64,
    jnxMbgSgwIfGtpV2ICsServDeniedTx Counter64,
    jnxMbgSgwIfGtpV2ICsUnabPageUERx Counter64,
    jnxMbgSgwIfGtpV2ICsUnabPageUETx Counter64,
    jnxMbgSgwIfGtpV2ICsNoMemRx      Counter64,
    jnxMbgSgwIfGtpV2ICsNoMemTx      Counter64,
    jnxMbgSgwIfGtpV2ICsUserAUTHFlRx Counter64,
    jnxMbgSgwIfGtpV2ICsUserAUTHFlTx Counter64,
    jnxMbgSgwIfGtpV2ICsAPNAcsDenRx  Counter64,
    jnxMbgSgwIfGtpV2ICsAPNAcsDenTx  Counter64,
    jnxMbgSgwIfGtpV2ICsReqRejRx     Counter64,
    jnxMbgSgwIfGtpV2ICsReqRejTx     Counter64,
    jnxMbgSgwIfGtpV2ICsPTMSISigMMRx Counter64,
    jnxMbgSgwIfGtpV2ICsPTMSISigMMTx Counter64,
    jnxMbgSgwIfGtpV2ICsIMSINotKnRx  Counter64,
    jnxMbgSgwIfGtpV2ICsIMSINotKnTx  Counter64,
    jnxMbgSgwIfGtpV2ICsCondIEMsRx   Counter64,
    jnxMbgSgwIfGtpV2ICsCondIEMsTx   Counter64,
    jnxMbgSgwIfGtpV2ICsAPNResTIncRx Counter64,
    jnxMbgSgwIfGtpV2ICsAPNResTIncTx Counter64,
    jnxMbgSgwIfGtpV2ICsUnknownRx    Counter64,
    jnxMbgSgwIfGtpV2ICsUnknownTx    Counter64,
    jnxMbgSgwIfGtpV2ICsLclDetRx     Counter64,
    jnxMbgSgwIfGtpV2ICsLclDetTx     Counter64,
    jnxMbgSgwIfGtpV2ICsCmpDetRx     Counter64,
    jnxMbgSgwIfGtpV2ICsCmpDetTx     Counter64,
    jnxMbgSgwIfGtpV2ICsRATChgRx     Counter64,
    jnxMbgSgwIfGtpV2ICsRATChgTx     Counter64,
    jnxMbgSgwIfGtpV2ICsISRDeactRx   Counter64,
    jnxMbgSgwIfGtpV2ICsISRDeactTx   Counter64,
    jnxMbgSgwIfGtpV2ICsEIFRNCEnRx   Counter64,
    jnxMbgSgwIfGtpV2ICsEIFRNCEnTx   Counter64,
    jnxMbgSgwIfGtpV2ICsSemErTADRx   Counter64,
    jnxMbgSgwIfGtpV2ICsSemErTADTx   Counter64,
    jnxMbgSgwIfGtpV2ICsSynErTADRx   Counter64,
    jnxMbgSgwIfGtpV2ICsSynErTADTx   Counter64,
    jnxMbgSgwIfGtpV2ICsRMValRcvRx   Counter64,
    jnxMbgSgwIfGtpV2ICsRMValRcvTx   Counter64,
    jnxMbgSgwIfGtpV2ICsRPrNtRspRx   Counter64,
    jnxMbgSgwIfGtpV2ICsRPrNtRspTx   Counter64,
    jnxMbgSgwIfGtpV2ICsColNWReqRx   Counter64,
    jnxMbgSgwIfGtpV2ICsColNWReqTx   Counter64,
    jnxMbgSgwIfGtpV2ICsUnPgUESusRx  Counter64,
    jnxMbgSgwIfGtpV2ICsUnPgUESusTx  Counter64,
    jnxMbgSgwIfGtpV2ICsInvTotLenRx  Counter64,
    jnxMbgSgwIfGtpV2ICsInvTotLenTx  Counter64,
    jnxMbgSgwIfGtpV2ICsDtForNtSupRx Counter64,
    jnxMbgSgwIfGtpV2ICsDtForNtSupTx Counter64,
    jnxMbgSgwIfGtpV2ICsInReFRePrRx  Counter64,
    jnxMbgSgwIfGtpV2ICsInReFRePrTx  Counter64,
    jnxMbgSgwIfGtpV2ICsInvPrRx      Counter64,
    jnxMbgSgwIfGtpV2ICsInvPrTx      Counter64,
    jnxMbgSgwIfGtpV1ProtocolErrRx   Counter64,
    jnxMbgSgwIfGtpV1UnSupMsgRx      Counter64,
    jnxMbgSgwIfGtpV1T3RespTmrExpRx  Counter64,
    jnxMbgSgwIfGtpV1EndMarkerRx     Counter64,
    jnxMbgSgwIfGtpV1EndMarkerTx     Counter64,
    jnxMbgSgwIfGtpV1EchoReqRx       Counter64,
    jnxMbgSgwIfGtpV1EchoReqTx       Counter64,
    jnxMbgSgwIfGtpV1EchoRespRx      Counter64,
    jnxMbgSgwIfGtpV1EchoRespTx      Counter64,
    jnxMbgSgwIfGtpV1ErrIndRx        Counter64,
    jnxMbgSgwIfGtpV1ErrIndTx        Counter64,
    jnxMbgSgwIfSuspNotifRx          Counter64,
    jnxMbgSgwIfSuspNotifTx          Counter64,
    jnxMbgSgwIfSuspAckRx            Counter64,
    jnxMbgSgwIfSuspAckTx            Counter64,
    jnxMbgSgwIfResumeNotifRx        Counter64,
    jnxMbgSgwIfResumeNotifTx        Counter64,
    jnxMbgSgwIfResumeAckRx          Counter64,
    jnxMbgSgwIfResumeAckTx          Counter64,
    jnxMbgSgwIfPiggybackMsgRx       Counter64,
    jnxMbgSgwIfPiggybackMsgTx       Counter64
}

jnxMbgSgwIfIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
    "GTP Interface Index"
    ::= { jnxMbgSgwGtpIfStatsEntry 1 }

jnxMbgSgwIfType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Interface Name."
    ::= { jnxMbgSgwGtpIfStatsEntry 2 }

jnxMbgSgwIfRxPacketsDropped  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Received GTP Packets Dropped by the Gateway."
    ::= { jnxMbgSgwGtpIfStatsEntry 3 }

jnxMbgSgwIfPacketAllocFail  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet allocation failures in the Gateway."
    ::= { jnxMbgSgwGtpIfStatsEntry 4 }

jnxMbgSgwIfPacketSendFail OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP Packet Send failures in the Gateway."
    ::= { jnxMbgSgwGtpIfStatsEntry 5 }

jnxMbgSgwIfIPVerErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of IP Version Error Packets Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 6 }

jnxMbgSgwIfIPProtoErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  IP Protocol Error packets Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 7 }

jnxMbgSgwIfGTPPortErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Port Error Packets Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 8 }

jnxMbgSgwIfGTPUnknVerRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Unknown Version Packets Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 9 }

jnxMbgSgwIfPcktLenErrRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of Packet Length Error Packets Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 10 }

jnxMbgSgwIfUnknMsgRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of  Unknown Messages Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 11 }

jnxMbgSgwIfProtocolErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 Protocol Errors Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 12 }

jnxMbgSgwIfUnSupportedMsgRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 Unsupported Messages received."
    ::= { jnxMbgSgwGtpIfStatsEntry 13 }

jnxMbgSgwIfT3RespTmrExpRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 T3 timer expiries Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 14 }

jnxMbgSgwIfV2NumMsgRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 messages received."
    ::= { jnxMbgSgwGtpIfStatsEntry 15 }

jnxMbgSgwIfV2NumMsgTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of V2 messages sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 16 }

jnxMbgSgwIfV2NumBytesRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPv2 bytes received."
    ::= { jnxMbgSgwGtpIfStatsEntry 17 }

jnxMbgSgwIfV2NumBytesTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of V2 bytes sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 18 }

jnxMbgSgwIfV2EchoReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Request received."
    ::= { jnxMbgSgwGtpIfStatsEntry 19 }

jnxMbgSgwIfV2EchoReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Request Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 20 }

jnxMbgSgwIfV2EchoRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Response received."
    ::= { jnxMbgSgwGtpIfStatsEntry 21 }

jnxMbgSgwIfV2EchoRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Echo Response Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 22 }

jnxMbgSgwIfV2VerNotSupRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Version Not supported messages received"
    ::= { jnxMbgSgwGtpIfStatsEntry 23 }

jnxMbgSgwIfV2VerNotSupTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 version not supported messages sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 24 }

jnxMbgSgwIfCreateSessReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Requests received."
    ::= { jnxMbgSgwGtpIfStatsEntry 25 }

jnxMbgSgwIfCreateSessReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Requests Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 26 }

jnxMbgSgwIfCreateSessRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Responses received."
    ::= { jnxMbgSgwGtpIfStatsEntry 27 }

jnxMbgSgwIfCreateSessRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Session Responses Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 28 }

jnxMbgSgwIfModBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Requests received."
    ::= { jnxMbgSgwGtpIfStatsEntry 29 }

jnxMbgSgwIfModBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Requests Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 30 }

jnxMbgSgwIfModBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Responses received."
    ::= { jnxMbgSgwGtpIfStatsEntry 31 }

jnxMbgSgwIfModBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Responses Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 32 }

jnxMbgSgwIfDelSessReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Requests received."
    ::= { jnxMbgSgwGtpIfStatsEntry 33 }

jnxMbgSgwIfDelSessReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Requests Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 34 }

jnxMbgSgwIfDelSessRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Responses received."
    ::= { jnxMbgSgwGtpIfStatsEntry 35 }

jnxMbgSgwIfDelSessRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Session Responses Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 36 }

jnxMbgSgwIfCrtBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Requests received."
    ::= { jnxMbgSgwGtpIfStatsEntry 37 }

jnxMbgSgwIfCrtBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Requests Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 38 }

jnxMbgSgwIfCrtBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Response received."
    ::= { jnxMbgSgwGtpIfStatsEntry 39 }

jnxMbgSgwIfCrtBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Bearer Response Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 40 }

jnxMbgSgwIfUpdBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Request received."
    ::= { jnxMbgSgwGtpIfStatsEntry 41 }

jnxMbgSgwIfUpdBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Request Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 42 }

jnxMbgSgwIfUpdBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Response received."
    ::= { jnxMbgSgwGtpIfStatsEntry 43 }

jnxMbgSgwIfUpdBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Bearer Response Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 44 }

jnxMbgSgwIfDelBrReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Request received."
    ::= { jnxMbgSgwGtpIfStatsEntry 45 }

jnxMbgSgwIfDelBrReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Request Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 46 }

jnxMbgSgwIfDelBrRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Response received."
    ::= { jnxMbgSgwGtpIfStatsEntry 47 }

jnxMbgSgwIfDelBrRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Response Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 48 }

jnxMbgSgwIfDelConnSetReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Request received."
    ::= { jnxMbgSgwGtpIfStatsEntry 49 }

jnxMbgSgwIfDelConnSetReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Request Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 50 }

jnxMbgSgwIfDelConnSetRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Response received."
    ::= { jnxMbgSgwGtpIfStatsEntry 51 }

jnxMbgSgwIfDelConnSetRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete PDN connection set Response Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 52 }

jnxMbgSgwIfUpdConnSetReqRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Request received."
    ::= { jnxMbgSgwGtpIfStatsEntry 53 }

jnxMbgSgwIfUpdConnSetReqTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Request Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 54 }

jnxMbgSgwIfUpdConnSetRspRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Response received."
    ::= { jnxMbgSgwGtpIfStatsEntry 55 }

jnxMbgSgwIfUpdConnSetRspTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Update Connection set Response Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 56 }

jnxMbgSgwIfModBrCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Command received."
    ::= { jnxMbgSgwGtpIfStatsEntry 57 }

jnxMbgSgwIfModBrCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Command Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 58 }

jnxMbgSgwIfModBrFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Failure received."
    ::= { jnxMbgSgwGtpIfStatsEntry 59 }

jnxMbgSgwIfModBrFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Modify Bearer Failure Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 60 }

jnxMbgSgwIfDelBrCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Command received."
    ::= { jnxMbgSgwGtpIfStatsEntry 61 }

jnxMbgSgwIfDelBrCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Command Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 62 }

jnxMbgSgwIfDelBrFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Failure received."
    ::= { jnxMbgSgwGtpIfStatsEntry 63 }

jnxMbgSgwIfDelBrFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Bearer Failure Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 64 }

jnxMbgSgwIfBrResCmdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Response Command received."
    ::= { jnxMbgSgwGtpIfStatsEntry 65 }

jnxMbgSgwIfBrResCmdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Response Command Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 66 }

jnxMbgSgwIfBrResFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Resource Failure received."
    ::= { jnxMbgSgwGtpIfStatsEntry 67 }

jnxMbgSgwIfBrResFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Bearer Resource Failure Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 68 }

jnxMbgSgwIfRelAcsBrReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Requests received."
    ::= { jnxMbgSgwGtpIfStatsEntry 69 }

jnxMbgSgwIfRelAcsBrReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Requests sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 70 }

jnxMbgSgwIfRelAcsBrRespRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Response received."
    ::= { jnxMbgSgwGtpIfStatsEntry 71 }

jnxMbgSgwIfRelAcsBrRespTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Release Access Bearer Response sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 72 }

jnxMbgSgwIfCrIndTunReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Request Received"
    ::= { jnxMbgSgwGtpIfStatsEntry 73 }

jnxMbgSgwIfCrIndTunReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Request sent"
    ::= { jnxMbgSgwGtpIfStatsEntry 74 }

jnxMbgSgwIfCrIndTunRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Response Received"
    ::= { jnxMbgSgwGtpIfStatsEntry 75 }

jnxMbgSgwIfCrIndTunRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Create Indirect Tunnel Forward Response sent"
    ::= { jnxMbgSgwGtpIfStatsEntry 76 }

jnxMbgSgwIfDelIndTunReqRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Request Received"
    ::= { jnxMbgSgwGtpIfStatsEntry 77 }

jnxMbgSgwIfDelIndTunReqTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Request sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 78 }

jnxMbgSgwIfDelIndTunRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Response Received"
    ::= { jnxMbgSgwGtpIfStatsEntry 79 }

jnxMbgSgwIfDelIndTunRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Delete Indirect Tunnel Forward Response sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 80 }

jnxMbgSgwIfDlDataNotifRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify received."
    ::= { jnxMbgSgwGtpIfStatsEntry 81 }

jnxMbgSgwIfDlDataNotifTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 82 }

jnxMbgSgwIfDlDataAckRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Acknowledgement received."
    ::= { jnxMbgSgwGtpIfStatsEntry 83 }

jnxMbgSgwIfDlDataAckTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notify Acknowledgement Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 84 }

jnxMbgSgwIfDlDataNotiFlrIndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notification fail received."
    ::= { jnxMbgSgwGtpIfStatsEntry 85 }

jnxMbgSgwIfDlDataNotiFlrIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Downlink Data Notification fail Sent."
    ::= { jnxMbgSgwGtpIfStatsEntry 86 }

jnxMbgSgwIfStopPagingIndRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Number of Stop Paging Indication Messages Received."
    ::= { jnxMbgSgwGtpIfStatsEntry 87 }

jnxMbgSgwIfStopPagingIndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP V2 Number of Stop Paging Indicaton messages sent"
    ::= { jnxMbgSgwGtpIfStatsEntry 88 }

jnxMbgSgwIfGtpV2ICsReqAcceptRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Request Accept."
    ::= { jnxMbgSgwGtpIfStatsEntry 89 }

jnxMbgSgwIfGtpV2ICsReqAcceptTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Request Accept."
    ::= { jnxMbgSgwGtpIfStatsEntry 90 }

jnxMbgSgwIfGtpV2ICsAcceptPartRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Accept Partial."
    ::= { jnxMbgSgwGtpIfStatsEntry 91 }

jnxMbgSgwIfGtpV2ICsAcceptPartTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Accept Partial."
    ::= { jnxMbgSgwGtpIfStatsEntry 92 }

jnxMbgSgwIfGtpV2ICsNewPTNPrefRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause New PDN type due to Network Preference."
    ::= { jnxMbgSgwGtpIfStatsEntry 93 }

jnxMbgSgwIfGtpV2ICsNewPTNPrefTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause New PDN type due to Network Preference"
    ::= { jnxMbgSgwGtpIfStatsEntry 94 }


jnxMbgSgwIfGtpV2ICsNPTSIAdbrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause New PDN type due to Single Address Bearer."
    ::= { jnxMbgSgwGtpIfStatsEntry 95 }

jnxMbgSgwIfGtpV2ICsNPTSIAdbrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause New PDN type due to Single Address Bearer."
    ::= { jnxMbgSgwGtpIfStatsEntry 96 }

jnxMbgSgwIfGtpV2ICsCtxNotFndRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Context not found."
    ::= { jnxMbgSgwGtpIfStatsEntry 97 }

jnxMbgSgwIfGtpV2ICsCtxNotFndTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Context not found."
    ::= { jnxMbgSgwGtpIfStatsEntry 98 }

jnxMbgSgwIfGtpV2ICsInvMsgFmtRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Invalid Message Format."
    ::= { jnxMbgSgwGtpIfStatsEntry 99 }

jnxMbgSgwIfGtpV2ICsInvMsgFmtTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Invalid Message Format."
    ::= { jnxMbgSgwGtpIfStatsEntry 100 }

jnxMbgSgwIfGtpV2ICsVerNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Version not Supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 101 }

jnxMbgSgwIfGtpV2ICsVerNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Version not Supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 102 }

jnxMbgSgwIfGtpV2ICsInvLenRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Invalid Length."
    ::= { jnxMbgSgwGtpIfStatsEntry 103 }

jnxMbgSgwIfGtpV2ICsInvLenTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Invalid Length."
    ::= { jnxMbgSgwGtpIfStatsEntry 104 }

jnxMbgSgwIfGtpV2ICsSrvNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Service Not supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 105 }

jnxMbgSgwIfGtpV2ICsSrvNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Service Not supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 106 }

jnxMbgSgwIfGtpV2ICsManIEIncorRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Mandatory IE incorrect."
    ::= { jnxMbgSgwGtpIfStatsEntry 107 }

jnxMbgSgwIfGtpV2ICsManIEIncorTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Mandatory IE incorrect."
    ::= { jnxMbgSgwGtpIfStatsEntry 108 }

jnxMbgSgwIfGtpV2ICsManIEMissRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Mandatory IE Missing."
    ::= { jnxMbgSgwGtpIfStatsEntry 109 }

jnxMbgSgwIfGtpV2ICsManIEMissTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Mandatory IE Missing."
    ::= { jnxMbgSgwGtpIfStatsEntry 110 }

jnxMbgSgwIfGtpV2ICsOptIEIncorRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Optional IE Incorrect."
    ::= { jnxMbgSgwGtpIfStatsEntry 111 }

jnxMbgSgwIfGtpV2ICsOptIEIncorTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Optional IE Incorrect."
    ::= { jnxMbgSgwGtpIfStatsEntry 112 }

jnxMbgSgwIfGtpV2ICsSysFailRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause System Failure."
    ::= { jnxMbgSgwGtpIfStatsEntry 113 }

jnxMbgSgwIfGtpV2ICsSysFailTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause System Failure."
    ::= { jnxMbgSgwGtpIfStatsEntry 114 }

jnxMbgSgwIfGtpV2ICsNoResRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause No Resource."
    ::= { jnxMbgSgwGtpIfStatsEntry 115 }

jnxMbgSgwIfGtpV2ICsNoResTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause No Resource."
    ::= { jnxMbgSgwGtpIfStatsEntry 116 }

jnxMbgSgwIfGtpV2ICsTFTSMANTErRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause TFT Symantic Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 117 }

jnxMbgSgwIfGtpV2ICsTFTSMANTErTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause TFT Symantic Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 118 }

jnxMbgSgwIfGtpV2ICsTFTSysErrRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause TFT System Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 119 }

jnxMbgSgwIfGtpV2ICsTFTSysErrTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause TFT System Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 120 }

jnxMbgSgwIfGtpV2ICsPkFltManErRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Packet Filter Symantic Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 121 }

jnxMbgSgwIfGtpV2ICsPkFltManErTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Packet Filter Symantic Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 122 }

jnxMbgSgwIfGtpV2ICsPkFltSynErRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Packet Filter Syntax Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 123 }

jnxMbgSgwIfGtpV2ICsPkFltSynErTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Packet Filter Syntax Error."
    ::= { jnxMbgSgwGtpIfStatsEntry 124 }

jnxMbgSgwIfGtpV2ICsMisUnknAPNRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unknown APN."
    ::= { jnxMbgSgwGtpIfStatsEntry 125 }

jnxMbgSgwIfGtpV2ICsMisUnknAPNTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unknown APN."
    ::= { jnxMbgSgwGtpIfStatsEntry 126 }

jnxMbgSgwIfGtpV2ICsUnexpRptIERx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unexpected Repeated IE."
    ::= { jnxMbgSgwGtpIfStatsEntry 127 }

jnxMbgSgwIfGtpV2ICsUnexpRptIETx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unexpected Repeated IE."
    ::= { jnxMbgSgwGtpIfStatsEntry 128 }

jnxMbgSgwIfGtpV2ICsGREKeyNtFdRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause GRE Key Not Found."
    ::= { jnxMbgSgwGtpIfStatsEntry 129 }

jnxMbgSgwIfGtpV2ICsGREKeyNtFdTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause GRE Key Not Found."
    ::= { jnxMbgSgwGtpIfStatsEntry 130 }

jnxMbgSgwIfGtpV2ICsRelocFailRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Relocation Failed."
    ::= { jnxMbgSgwGtpIfStatsEntry 131 }

jnxMbgSgwIfGtpV2ICsRelocFailTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Relocation Failed."
    ::= { jnxMbgSgwGtpIfStatsEntry 132 }

jnxMbgSgwIfGtpV2ICsDenINRatRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Denied in RAT."
    ::= { jnxMbgSgwGtpIfStatsEntry 133 }

jnxMbgSgwIfGtpV2ICsDenINRatTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Denied in RAT."
    ::= { jnxMbgSgwGtpIfStatsEntry 134 }

jnxMbgSgwIfGtpV2ICsPTNotSuppRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause PDN Type Not Supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 135 }

jnxMbgSgwIfGtpV2ICsPTNotSuppTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause PDN Type Not Supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 136 }

jnxMbgSgwIfGtpV2ICsAlDynAdOccRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Allocated Dynamic Address Occupied."
    ::= { jnxMbgSgwGtpIfStatsEntry 137 }

jnxMbgSgwIfGtpV2ICsAlDynAdOccTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Allocated Dynamic Address Occupied."
    ::= { jnxMbgSgwGtpIfStatsEntry 138 }

jnxMbgSgwIfGtpV2ICsNOTFTUECTXRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Context Without TFT Exists."
    ::= { jnxMbgSgwGtpIfStatsEntry 139 }

jnxMbgSgwIfGtpV2ICsNOTFTUECTXTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Context Without TFT Exists."
    ::= { jnxMbgSgwGtpIfStatsEntry 140 }

jnxMbgSgwIfGtpV2ICsProtoNtSupRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Protocol Not Supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 141 }

jnxMbgSgwIfGtpV2ICsProtoNtSupTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Protocol Not Supported."
    ::= { jnxMbgSgwGtpIfStatsEntry 142 }

jnxMbgSgwIfGtpV2ICsUENotRespRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Not Responding."
    ::= { jnxMbgSgwGtpIfStatsEntry 143 }

jnxMbgSgwIfGtpV2ICsUENotRespTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Not Responding."
    ::= { jnxMbgSgwGtpIfStatsEntry 144 }

jnxMbgSgwIfGtpV2ICsUERefusesRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause UE Refuses."
    ::= { jnxMbgSgwGtpIfStatsEntry 145 }

jnxMbgSgwIfGtpV2ICsUERefusesTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause UE Refuses."
    ::= { jnxMbgSgwGtpIfStatsEntry 146 }

jnxMbgSgwIfGtpV2ICsServDeniedRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Service Denied."
    ::= { jnxMbgSgwGtpIfStatsEntry 147 }

jnxMbgSgwIfGtpV2ICsServDeniedTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Service Denied."
    ::= { jnxMbgSgwGtpIfStatsEntry 148 }

jnxMbgSgwIfGtpV2ICsUnabPageUERx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Unable to Page UE."
    ::= { jnxMbgSgwGtpIfStatsEntry 149 }

jnxMbgSgwIfGtpV2ICsUnabPageUETx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Unable to Page UE."
    ::= { jnxMbgSgwGtpIfStatsEntry 150 }

jnxMbgSgwIfGtpV2ICsNoMemRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause No Memory."
    ::= { jnxMbgSgwGtpIfStatsEntry 151 }

jnxMbgSgwIfGtpV2ICsNoMemTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause No Memory."
    ::= { jnxMbgSgwGtpIfStatsEntry 152 }

jnxMbgSgwIfGtpV2ICsUserAUTHFlRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause User AUTH Failed."
    ::= { jnxMbgSgwGtpIfStatsEntry 153 }

jnxMbgSgwIfGtpV2ICsUserAUTHFlTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause User AUTH Failed."
    ::= { jnxMbgSgwGtpIfStatsEntry 154 }

jnxMbgSgwIfGtpV2ICsAPNAcsDenRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause APN Access Denied."
    ::= { jnxMbgSgwGtpIfStatsEntry 155 }

jnxMbgSgwIfGtpV2ICsAPNAcsDenTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause APN Access Denied."
    ::= { jnxMbgSgwGtpIfStatsEntry 156 }

jnxMbgSgwIfGtpV2ICsReqRejRx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTPV2 packets received with cause Request Rejected."
    ::= { jnxMbgSgwGtpIfStatsEntry 157 }

jnxMbgSgwIfGtpV2ICsReqRejTx  OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
    "Number of GTP packets sent with cause Request Rejected."
    ::= { jnxMbgSgwGtpIfStatsEntry 158 }

jnxMbgSgwIfGtpV2ICsPTMSISigMMRx OBJECT-TYPE
     SYNTAX      Counter64
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of GTPV2 packets received with cause P-TMSI Signature Mismatch."
    ::= { jnxMbgSgwGtpIfStatsEntry 159 }

jnxMbgSgwIfGtpV2ICsPTMSISigMMTx OBJECT-TYPE
     SYNTAX      Counter64
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of GTP packets sent with cause P-TMSI Signature Mismatch"
    ::= { jnxMbgSgwGtpIfStatsEntry 160 }

jnxMbgSgwIfGtpV2ICsIMSINotKnRx OBJECT-TYPE
      SYNTAX      Counter64
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
      "Number of GTPV2 packets received with cause IMSI Not Known."
    ::= { jnxMbgSgwGtpIfStatsEntry 161 }

jnxMbgSgwIfGtpV2ICsIMSINotKnTx OBJECT-TYPE
      SYNTAX      Counter64
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
      "Number of GTP packets sent with cause IMSI Not Known."
    ::= { jnxMbgSgwGtpIfStatsEntry 162 }

jnxMbgSgwIfGtpV2ICsCondIEMsRx OBJECT-TYPE
       SYNTAX      Counter64
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
       "Number of GTPV2 packets received with cause Conditional IE Missing."
     ::= { jnxMbgSgwGtpIfStatsEntry 163 }

jnxMbgSgwIfGtpV2ICsCondIEMsTx OBJECT-TYPE
       SYNTAX      Counter64
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION
       "Number of GTP packets sent with cause Conditional IE Missing."
     ::= { jnxMbgSgwGtpIfStatsEntry 164 }

jnxMbgSgwIfGtpV2ICsAPNResTIncRx OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of GTPV2 packets received with cause APN Restriction Type Incompatible."
     ::= { jnxMbgSgwGtpIfStatsEntry 165 }

jnxMbgSgwIfGtpV2ICsAPNResTIncTx OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Number of GTP packets sent with cause APN Restriction Type Incompatible."
     ::= { jnxMbgSgwGtpIfStatsEntry 166 }

jnxMbgSgwIfGtpV2ICsUnknownRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPV2 packets received with cause Unknown."
     ::= { jnxMbgSgwGtpIfStatsEntry 167 }

jnxMbgSgwIfGtpV2ICsUnknownTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Unknown."
     ::= { jnxMbgSgwGtpIfStatsEntry 168 }

jnxMbgSgwIfGtpV2ICsLclDetRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Local Detach."
     ::= { jnxMbgSgwGtpIfStatsEntry 169 }

jnxMbgSgwIfGtpV2ICsLclDetTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Local Detach."
     ::= { jnxMbgSgwGtpIfStatsEntry 170 }

jnxMbgSgwIfGtpV2ICsCmpDetRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Complete Detach."
     ::= { jnxMbgSgwGtpIfStatsEntry 171 }

jnxMbgSgwIfGtpV2ICsCmpDetTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Complete Detach."
     ::= { jnxMbgSgwGtpIfStatsEntry 172 }

jnxMbgSgwIfGtpV2ICsRATChgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause RAT changed from 3GPP to non 3GPP."
     ::= { jnxMbgSgwGtpIfStatsEntry 173 }

jnxMbgSgwIfGtpV2ICsRATChgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause RAT changed from 3GPP to non 3GPP."
     ::= { jnxMbgSgwGtpIfStatsEntry 174 }

jnxMbgSgwIfGtpV2ICsISRDeactRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause ISR Deactivated."
     ::= { jnxMbgSgwGtpIfStatsEntry 175 }

jnxMbgSgwIfGtpV2ICsISRDeactTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause ISR Deactivated."
     ::= { jnxMbgSgwGtpIfStatsEntry 176 }

jnxMbgSgwIfGtpV2ICsEIFRNCEnRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Error Indication from RNC eNodeB."
     ::= { jnxMbgSgwGtpIfStatsEntry 177 }

jnxMbgSgwIfGtpV2ICsEIFRNCEnTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Error Indication from RNC eNodeB."
     ::= { jnxMbgSgwGtpIfStatsEntry 178 }

jnxMbgSgwIfGtpV2ICsSemErTADRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Semantic Error in TAD Operation."
     ::= { jnxMbgSgwGtpIfStatsEntry 179 }

jnxMbgSgwIfGtpV2ICsSemErTADTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Semantic Error in TAD Operation."
     ::= { jnxMbgSgwGtpIfStatsEntry 180 }

jnxMbgSgwIfGtpV2ICsSynErTADRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Syntactic Error in TAD Operation."
     ::= { jnxMbgSgwGtpIfStatsEntry 181 }

jnxMbgSgwIfGtpV2ICsSynErTADTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Syntactic Error in TAD Operation."
     ::= { jnxMbgSgwGtpIfStatsEntry 182 }

jnxMbgSgwIfGtpV2ICsRMValRcvRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Reserved Message Value Received."
     ::= { jnxMbgSgwGtpIfStatsEntry 183 }

jnxMbgSgwIfGtpV2ICsRMValRcvTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Reserved Message Value Received."
     ::= { jnxMbgSgwGtpIfStatsEntry 184 }

jnxMbgSgwIfGtpV2ICsRPrNtRspRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Remote peer not responding."
     ::= { jnxMbgSgwGtpIfStatsEntry 185 }

jnxMbgSgwIfGtpV2ICsRPrNtRspTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Remote peer not responding."
     ::= { jnxMbgSgwGtpIfStatsEntry 186 }

jnxMbgSgwIfGtpV2ICsColNWReqRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Collision with network initiated request."
     ::= { jnxMbgSgwGtpIfStatsEntry 187 }

jnxMbgSgwIfGtpV2ICsColNWReqTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Collision with network initiated request."
     ::= { jnxMbgSgwGtpIfStatsEntry 188 }

jnxMbgSgwIfGtpV2ICsUnPgUESusRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Unable to page UE due to suspension."
     ::= { jnxMbgSgwGtpIfStatsEntry 189 }

jnxMbgSgwIfGtpV2ICsUnPgUESusTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Unable to page UE due to suspension."
     ::= { jnxMbgSgwGtpIfStatsEntry 190 }

jnxMbgSgwIfGtpV2ICsInvTotLenRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid total len."
     ::= { jnxMbgSgwGtpIfStatsEntry 191 }

jnxMbgSgwIfGtpV2ICsInvTotLenTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid total len."
     ::= { jnxMbgSgwGtpIfStatsEntry 192 }

jnxMbgSgwIfGtpV2ICsDtForNtSupRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Data forwarding not supported."
     ::= { jnxMbgSgwGtpIfStatsEntry 193 }

jnxMbgSgwIfGtpV2ICsDtForNtSupTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Data forwarding not supported."
     ::= { jnxMbgSgwGtpIfStatsEntry 194 }

jnxMbgSgwIfGtpV2ICsInReFRePrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid Reply from Remote peer."
     ::= { jnxMbgSgwGtpIfStatsEntry 195 }

jnxMbgSgwIfGtpV2ICsInReFRePrTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid Reply from Remote peer."
     ::= { jnxMbgSgwGtpIfStatsEntry 196 }

jnxMbgSgwIfGtpV2ICsInvPrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets received with cause Invalid peer."
     ::= { jnxMbgSgwGtpIfStatsEntry 197 }

jnxMbgSgwIfGtpV2ICsInvPrTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets sent with cause Invalid peer."
     ::= { jnxMbgSgwGtpIfStatsEntry 198 }

jnxMbgSgwIfGtpV1ProtocolErrRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv1 Protocol Errors Received."
     ::= { jnxMbgSgwGtpIfStatsEntry 199 }

jnxMbgSgwIfGtpV1UnSupMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv1 Unsupported Messages received."
     ::= { jnxMbgSgwGtpIfStatsEntry 200 }

jnxMbgSgwIfGtpV1T3RespTmrExpRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 T3 timer expiries Received."
     ::= { jnxMbgSgwGtpIfStatsEntry 201 }

jnxMbgSgwIfGtpV1EndMarkerRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 end marker packets received."
     ::= { jnxMbgSgwGtpIfStatsEntry 202 }

jnxMbgSgwIfGtpV1EndMarkerTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 end marker packets sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 203 }

jnxMbgSgwIfGtpV1EchoReqRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo request packets received."
     ::= { jnxMbgSgwGtpIfStatsEntry 204 }

jnxMbgSgwIfGtpV1EchoReqTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo request packets sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 205 }

jnxMbgSgwIfGtpV1EchoRespRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo response packets received."
     ::= { jnxMbgSgwGtpIfStatsEntry 206 }

jnxMbgSgwIfGtpV1EchoRespTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP V1 echo response packets sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 207 }

jnxMbgSgwIfGtpV1ErrIndRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets V1 Error Indication packets received."
     ::= { jnxMbgSgwGtpIfStatsEntry 208 }

jnxMbgSgwIfGtpV1ErrIndTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTP packets V1 Error Indication packets sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 209 }

jnxMbgSgwIfSuspNotifRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Notification messages received."
     ::= { jnxMbgSgwGtpIfStatsEntry 210 } 

jnxMbgSgwIfSuspNotifTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Notification messages sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 211 } 

jnxMbgSgwIfSuspAckRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Acknowledgement messages received."
     ::= { jnxMbgSgwGtpIfStatsEntry 212 }

jnxMbgSgwIfSuspAckTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Suspend Acknowledgement messages sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 213 }

jnxMbgSgwIfResumeNotifRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Notification messages received."
     ::= { jnxMbgSgwGtpIfStatsEntry 214 }
    
jnxMbgSgwIfResumeNotifTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Notification messages sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 215 }

jnxMbgSgwIfResumeAckRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Acknowledgement messages received."
     ::= { jnxMbgSgwGtpIfStatsEntry 216 }
   
jnxMbgSgwIfResumeAckTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Resume Acknowledgement messages sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 217 }

jnxMbgSgwIfPiggybackMsgRx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 Piggyback messages received."
     ::= { jnxMbgSgwGtpIfStatsEntry 218 }
    
jnxMbgSgwIfPiggybackMsgTx OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
         "Number of GTPv2 S11 Piggyback messages sent."
     ::= { jnxMbgSgwGtpIfStatsEntry 219 }


jnxMbgSgwGtpNotificationVars   OBJECT IDENTIFIER ::= {
                                             jnxMbgSgwGtpObjects 3 }
jnxMbgSgwGtpPeerName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
    "GTP Peer Name/IP"
   ::= { jnxMbgSgwGtpNotificationVars 1 }

jnxMbgSgwGtpAlarmStatCounter OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS    accessible-for-notify
    STATUS        current
    DESCRIPTION
    "Current Value of (Alarm) Statistics Counter
     eg: in jnxMbgSgwGtpPrDNTPerPrAlrmActv it spefies the number
         of times peer is down with in the monitoring interval"
   ::= { jnxMbgSgwGtpNotificationVars 2 }

jnxMbgSgwGtpInterfaceType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
    "GTP Interface Type which can be one of S5/S8/S11/S1U/S12/S4"
   ::= { jnxMbgSgwGtpNotificationVars 3 }

jnxMbgSgwGtpGwName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "A string that indicates the gateway name"
    ::= { jnxMbgSgwGtpNotificationVars 4 }

jnxMbgSgwGtpGwIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Current Gateway ID value"
    ::= { jnxMbgSgwGtpNotificationVars 5 }

jnxMbgSgwGtpPeerGwUpNotif NOTIFICATION-TYPE
     OBJECTS          { jnxMbgSgwGtpGwIndex,
                        jnxMbgSgwGtpGwName,
                        jnxMbgSgwGtpInterfaceType,
                        jnxMbgSgwGtpPeerName }
     STATUS      current
     DESCRIPTION
     "GTPC Peer UP Notification. This trap is sent when a new peer is added
       or an existing peer goes down and comes back up."
    ::= { jnxMbgSgwGtpNotifications 1 }

jnxMbgSgwGtpPeerGwDnNotif NOTIFICATION-TYPE
    OBJECTS          { jnxMbgSgwGtpGwIndex,
                       jnxMbgSgwGtpGwName,
                       jnxMbgSgwGtpInterfaceType,
                       jnxMbgSgwGtpPeerName }
    STATUS      current
    DESCRIPTION
     "GTPC Peer Down Notification. This trap is sent when a peer connection 
      goes down."
    ::= { jnxMbgSgwGtpNotifications 2 }

jnxMbgSgwGtpPrDnTPerPrAlrmActv NOTIFICATION-TYPE
    OBJECTS     { jnxMbgSgwGtpGwIndex,
                  jnxMbgSgwGtpGwName,
                  jnxMbgSgwGtpInterfaceType,
                  jnxMbgSgwGtpPeerName,
                  jnxMbgSgwGtpAlarmStatCounter }
    STATUS          current
    DESCRIPTION
        "Peer down Threshold trap Active. This is sent when a peer connection
         flaps for more than a higher threshold number of times with in a
         monitor interval."
    ::= { jnxMbgSgwGtpNotifications 3 }

jnxMbgSgwGtpPrDnTPerPrAlrmClr NOTIFICATION-TYPE
    OBJECTS     { jnxMbgSgwGtpGwIndex,
                  jnxMbgSgwGtpGwName,
                  jnxMbgSgwGtpInterfaceType,
                  jnxMbgSgwGtpPeerName,
                  jnxMbgSgwGtpAlarmStatCounter }
    STATUS          current
    DESCRIPTION
        "Peer down Threshold trap Cleared. This is sent when the number of 
         times a peer connection flaps in a monitor interval come down below
         the lower threshold."
    ::= { jnxMbgSgwGtpNotifications 4 }

END
