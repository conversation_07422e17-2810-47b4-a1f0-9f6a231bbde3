-- *******************************************************************
-- Juniper enterprise specific Event MIB. 
--
-- Copyright (c) 2002-2004, 2006, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
-- *******************************************************************

JUNIPER-EVENT-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-IDENTITY, OBJECT-TYPE, 
    NOTIFICATION-TYPE, Unsigned32
        FROM SNMPv2-SMI
    DisplayString
        FROM SNMPv2-TC
    jnxMibs, jnxEventNotifications 
        FROM JUNIPER-SMI;

jnxEvent MODULE-IDENTITY
    LAST-UPDATED "200608162153Z" -- Wed Aug 16 21:53:48 2005 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089
             E-mail: <EMAIL>"

    DESCRIPTION
            "This is Juniper Networks implementation of enterprise
             specific MIB for generic event notifications."

    REVISION      "200608162153Z"
    DESCRIPTION
               "Initial revision."
    ::= { jnxMibs 37 }


--
-- Notification objects
--

    jnxEventNotifyVars OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "Notification object definitions."
        ::= { jnxEvent 1 }

    jnxEventTrapDescr OBJECT-TYPE
      SYNTAX  DisplayString 
      MAX-ACCESS  accessible-for-notify 
      STATUS   current  
      DESCRIPTION
              "Description of the trap generated by op-script 
               or event-policies."
      ::= { jnxEventNotifyVars 1 }

    jnxEventAvTable  OBJECT-TYPE
      SYNTAX      SEQUENCE OF JnxEventAvEntry
      MAX-ACCESS  not-accessible
      STATUS      current
      DESCRIPTION
            "A table of attribute value pairs for the trap 
             generated by the op-scripts or event-policies."
      ::= { jnxEventNotifyVars 2 }

    jnxEventAvEntry   OBJECT-TYPE
      SYNTAX      JnxEventAvEntry 
      MAX-ACCESS  not-accessible
      STATUS      current
      DESCRIPTION
            "An entry of attribute value pair."
      INDEX     { jnxEventAvIndex }
      ::= { jnxEventAvTable 1 }

    JnxEventAvEntry ::=
        SEQUENCE {
          jnxEventAvIndex       Unsigned32,
          jnxEventAvAttribute   DisplayString,
          jnxEventAvValue       DisplayString
        }

    jnxEventAvIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Identifies the sequence number of attribute-value
             pair in the trap generated by  op-scripts or 
             event-policies."
        ::= { jnxEventAvEntry 1 }

    jnxEventAvAttribute OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Attribute name in the trap generated by op-script 
             or event-policies."
        ::= { jnxEventAvEntry 2 }

    jnxEventAvValue OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Value of the attribute identified by jnxEventAvAttribute."
        ::= { jnxEventAvEntry 3 }


--
-- Event Notification definitions
--
    jnxEventNotificationPrefix OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "All Event notifications are registered under 
             this branch."
        ::= { jnxEventNotifications 0 }

    jnxEventTrap NOTIFICATION-TYPE
        OBJECTS { jnxEventTrapDescr }
        STATUS  current
        DESCRIPTION
            "Notification generated by op-script or event-policies. Apart 
             from the jnxEventTrap objects, this notification can include 
	     one or more attribute-value pairs. The attribute-value pairs 
	     shall be identified by objects jnxEventAvAttribute and 
	     jnxEventAvValue."
        ::= { jnxEventNotificationPrefix 1 }

END
