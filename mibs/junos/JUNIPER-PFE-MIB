--
-- Juniper Enterprise Specific MIB: PFE MIB
-- 
-- Copyright (c) 2006-2012, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-PFE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, 
    Counter32, Counter64, Integer32
        FROM SNMPv2-SMI
    TEXTUAL-CONVENTION, DisplayString
        FROM SNMPv2-TC
    jnxPfeMibRoot
        FROM JUNIPER-SMI;

jnxPfeMib MODULE-IDENTITY
    LAST-UPDATED "201605310000Z"     -- Tue May  31 00:00:00 2016 UTC 
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"

    DESCRIPTION
            "The MIB provides PFE specific data."

    REVISION      "201411140000Z"
    DESCRIPTION 
               "Added jnxPfeMemoryTrapVars and jnxPfeMemoryNotifications."
    REVISION      "201403120000Z"
    DESCRIPTION 
               "Added new Table jnxPfeNotifyGlParAccSec which counts
                notifications for the packets parsed/processed by
                access-security." 
    REVISION      "201109090000Z"
    DESCRIPTION
               "Added new Table jnxPfeMemoryErrorsTable which gives parity and
                ecc errors.
                Added new Trap pfeMemoryErrors"
    REVISION      "201002070000Z"
    DESCRIPTION
               "Added new notification types."
    --REVISION      "201001070000Z"
    --DESCRIPTION
    --         "Added new notification types."
    --REVISION      "200607240000Z"
    --DESCRIPTION
    --           "Initial revision."

    REVISION    "201605310000Z" -- 31-May-16
    DESCRIPTION
            "Corrected type in sequence and actual"

    ::= { jnxPfeMibRoot 1 }
    
    JnxPfeMemoryTypeEnum ::= TEXTUAL-CONVENTION 
    STATUS      current
    DESCRIPTION
                "PFE memory type, nh (1), fw (2), encap (3)"
    SYNTAX      INTEGER { nh(1),
                        fw(2),
                        encap(3)}

    --
    -- This branch contains all PFE Notification statistics data.
    --
    jnxPfeNotification   OBJECT IDENTIFIER ::= { jnxPfeMib 1 }


    --
    -- This table provide global PFE notification stats for each PFE slot,
    -- exposing the data provided by the 'show pfe statistics notification'
    -- cli command.
    --
    jnxPfeNotifyGlTable OBJECT-TYPE
	SYNTAX          SEQUENCE OF JnxPfeNotifyGlEntry
	MAX-ACCESS      not-accessible
	STATUS          current
	DESCRIPTION
		"This table provides global PFE notification statistics."
	::= { jnxPfeNotification 1 }

    jnxPfeNotifyGlEntry OBJECT-TYPE
	SYNTAX          JnxPfeNotifyGlEntry
	MAX-ACCESS      not-accessible
	STATUS          current
	DESCRIPTION
		""
        INDEX { jnxPfeNotifyGlSlot }
	::= { jnxPfeNotifyGlTable 1 }

    JnxPfeNotifyGlEntry ::=
	SEQUENCE {
	    jnxPfeNotifyGlSlot          Integer32,
            jnxPfeNotifyGlParsed        Counter32,
            jnxPfeNotifyGlAged          Counter32,
            jnxPfeNotifyGlCorrupt       Counter32,
            jnxPfeNotifyGlIllegal       Counter32,
            jnxPfeNotifyGlSample        Counter32,
            jnxPfeNotifyGlGiants        Counter32,
            jnxPfeNotifyGlTtlExceeded   Counter32,
            jnxPfeNotifyGlTtlExcErrors  Counter32,
            jnxPfeNotifyGlSvcOptAsp     Counter32,
            jnxPfeNotifyGlSvcOptRe      Counter32,
            jnxPfeNotifyGlPostSvcOptOut Counter32,
            jnxPfeNotifyGlOptTtlExp     Counter32,
            jnxPfeNotifyGlDiscSample    Counter32,
            jnxPfeNotifyGlRateLimited   Counter32,
            jnxPfeNotifyGlPktGetFails   Counter32,
            jnxPfeNotifyGlDmaFails      Counter32,
            jnxPfeNotifyGlDmaTotals     Counter32,
            jnxPfeNotifyGlUnknowns      Counter32,
            jnxPfeNotifyGlParAccSec     Counter32
	}

    jnxPfeNotifyGlSlot OBJECT-TYPE
        SYNTAX      Integer32 (0..**********)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The PFE slot number for this set of global PFE notification
                statistics."
        ::= { jnxPfeNotifyGlEntry 1 }

    jnxPfeNotifyGlParsed OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications reported by the routing chip."
        ::= { jnxPfeNotifyGlEntry 2 }

    jnxPfeNotifyGlAged OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications that are dropped due to the fact 
                that the they have been in the system for too long and 
                hence not valid anymore."
        ::= { jnxPfeNotifyGlEntry 3 }

    jnxPfeNotifyGlCorrupt OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications dropped due to the fact that they 
                have an invalid notification result format. This counter
                is valid for Internet Processor-I and Internet Processor-II 
                only."
        ::= { jnxPfeNotifyGlEntry 4 }

    jnxPfeNotifyGlIllegal OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications dropped due to the fact that they 
                have an illegal notification type."
        ::= { jnxPfeNotifyGlEntry 5 }

    jnxPfeNotifyGlSample OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of sample notifications reported by the routing chip."
        ::= { jnxPfeNotifyGlEntry 6 }

    jnxPfeNotifyGlGiants OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications dropped that are larger than the 
                supported DMA size."
        ::= { jnxPfeNotifyGlEntry 7 }

    jnxPfeNotifyGlTtlExceeded OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of options/TTL-expired notifications that need to be 
                sent to service interfaces as transit packets. This counter 
                is valid for Internet Processor-I and Internet Processor-II
                only."
        ::= { jnxPfeNotifyGlEntry 8 }

    jnxPfeNotifyGlTtlExcErrors OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of options/TTL-expired packet notifications that could 
                not be sent as transit packets because the output interface 
                could not be determined.  This counter is valid for Internet 
                Processor-I and Internet Processor-II only."
        ::= { jnxPfeNotifyGlEntry 9 }

    jnxPfeNotifyGlSvcOptAsp OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of IP options packets that are sent out to a Services 
                PIC."
        ::= { jnxPfeNotifyGlEntry 10 }

    jnxPfeNotifyGlSvcOptRe OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of IP options packets that are sent out to the Routing
                Engine."
        ::= { jnxPfeNotifyGlEntry 11 }

    jnxPfeNotifyGlPostSvcOptOut OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications that were re-injected by the services 
                PIC after it had processed the associated packets.  These 
                notifications now need to be forwarded out to their actual 
                destination.  This counter is valid for Internet Processor-I 
                and Internet Processor-II only."
        ::= { jnxPfeNotifyGlEntry 12 }

    jnxPfeNotifyGlOptTtlExp OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of TTL-expired transit packets."
        ::= { jnxPfeNotifyGlEntry 13 }

    jnxPfeNotifyGlDiscSample OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of sample notifications that are dropped as they refer 
                to discarded packets in PFE."
        ::= { jnxPfeNotifyGlEntry 14 }

    jnxPfeNotifyGlRateLimited OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications ignored because of PFE software 
                throttling."
        ::= { jnxPfeNotifyGlEntry 15 }

    jnxPfeNotifyGlPktGetFails OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications where we could not allocate memory 
                for DMA."
        ::= { jnxPfeNotifyGlEntry 16 }

    jnxPfeNotifyGlDmaFails OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications where the DMA of associated packets 
                failed for miscellaneous reasons.  Valid for T-series Internet 
                Processor only."
        ::= { jnxPfeNotifyGlEntry 17 }

    jnxPfeNotifyGlDmaTotals OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications for which the packet DMA completed.  
                Valid for T-series Internet Processor only."
        ::= { jnxPfeNotifyGlEntry 18 }

    jnxPfeNotifyGlUnknowns OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications that could not be resolved to a known 
                next hop destination. Valid for T-series Internet Processor 
                only."
        ::= { jnxPfeNotifyGlEntry 19 }

    jnxPfeNotifyGlParAccSec OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications for the packets parsed/processed by
                access-security."
        ::= { jnxPfeNotifyGlEntry 20 }       

    --
    -- This table provides Type specific PFE notification stats for each PFE 
    -- slot, exposing the data provided by the 'show pfe statistics 
    -- notification' cli command.
    --
    jnxPfeNotifyTypeTable OBJECT-TYPE
	SYNTAX          SEQUENCE OF JnxPfeNotifyTypeEntry
	MAX-ACCESS      not-accessible
	STATUS          current
	DESCRIPTION
		"This provides type-specific PFE notification stats"
	::= { jnxPfeNotification 2 }

    jnxPfeNotifyTypeEntry OBJECT-TYPE
	SYNTAX          JnxPfeNotifyTypeEntry
	MAX-ACCESS      not-accessible
	STATUS          current
	DESCRIPTION
		""
        INDEX { jnxPfeNotifyGlSlot, jnxPfeNotifyTypeId }
	::= { jnxPfeNotifyTypeTable 1 }

    JnxPfeNotifyTypeEntry ::=
	SEQUENCE {
	    jnxPfeNotifyTypeId       INTEGER,
            jnxPfeNotifyTypeDescr    DisplayString,
            jnxPfeNotifyTypeParsed   Counter32, 
            jnxPfeNotifyTypeInput    Counter32, 
            jnxPfeNotifyTypeFailed   Counter32, 
            jnxPfeNotifyTypeIgnored  Counter32
	}

    jnxPfeNotifyTypeId  OBJECT-TYPE
        SYNTAX INTEGER {
                   illegal       (1),
                   unclassified  (2),
                   option        (3),
                   nextHop       (4),
                   discard       (5),
                   sample        (6),
                   redirect      (7),
                   dontFragment  (8),
                   cfdf          (9),
                   poison        (10),
                   unknown       (11),
                   specialMemPkt (12), 
                   autoConfig    (13),
                   reject        (14)
               }
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This identifies the PFE notification type for this row's stats.
            Below is a description of each notification type:
                1.  illegal       Packets with invalid notification type.

                2.  unclassified  Packets that did not have a key lookup 
                                  performed on them.

                3.  option        Packets which have L3 options present.  

                4.  nextHop       Packets that are destined to the host.

                5.  discard       Used when a discarded packet is sent to the 
                                  route processor.

                6.  sample        Unused.

                7.  redirect      This is used when a packet is being sent out 
                                  on the interface it came in on.

                8.  dontFragment  This is used that a packet needs to be 
                                   fragmented but the DF (don't fragment) bit
                                   is set.

                9.  cfdf           When an MTU exceeded indication is 
                                   triggered by the CF chip and the packet has 
                                   DF (don't fragment) set.

                10. poison         Packets that resolved to a poisoned next 
                                   hop index.

                11. unknown       Packets of unknown notification type.
  
                12. specialMemPkt Packets with special memory pkt type notification used in diagnostics.  

                13. autoconfig    Packets with autoconfig PFE notification type used for dynamic VLANs.  

                14. reject        Packets of reject PFE notification type."  
    ::= { jnxPfeNotifyTypeEntry 1 }

    jnxPfeNotifyTypeDescr OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..64))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The description of the Pfe Notification type for this entry."
        ::= { jnxPfeNotifyTypeEntry 2 }

    jnxPfeNotifyTypeParsed OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of successful parsing of notifications."
        ::= { jnxPfeNotifyTypeEntry 3 }

    jnxPfeNotifyTypeInput OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications whose associated packets were DMA'ed 
                into route processor memory."
        ::= { jnxPfeNotifyTypeEntry 4 }

    jnxPfeNotifyTypeFailed OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of failures in parsing the notifications."
        ::= { jnxPfeNotifyTypeEntry 5 }

    jnxPfeNotifyTypeIgnored OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Count of notifications where the notification type in the 
                message does not match any of the valid types."
        ::= { jnxPfeNotifyTypeEntry 6 }

    --
    -- This table provides error counters for each PFE
    --
    jnxPfeMemoryErrorsTable OBJECT-TYPE
    SYNTAX          SEQUENCE OF JnxPfeMemoryErrorsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        "This provides PFE memory errors"
    ::= { jnxPfeNotification 3 }

    jnxPfeMemoryErrorsEntry OBJECT-TYPE
    SYNTAX          JnxPfeMemoryErrorsEntry
    MAX-ACCESS      not-accessible
    STATUS          current
    DESCRIPTION
        ""
        INDEX { jnxPfeFpcSlot, jnxPfeSlot }
    ::= { jnxPfeMemoryErrorsTable 1 }

    JnxPfeMemoryErrorsEntry ::=
    SEQUENCE {
            jnxPfeFpcSlot         Integer32,
            jnxPfeSlot            Integer32,  
            jnxPfeParityErrors    Counter64,
            jnxPfeEccErrors       Counter64
    }

     jnxPfeFpcSlot OBJECT-TYPE
        SYNTAX      Integer32 (0..**********)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The FPC slot number for this set of PFE notification"
        ::= { jnxPfeMemoryErrorsEntry 1 }

    jnxPfeSlot OBJECT-TYPE
        SYNTAX      Integer32 (0..**********)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The pfe slot number for this set of errors"
        ::= { jnxPfeMemoryErrorsEntry 2 }

    jnxPfeParityErrors OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The parity error count"
        ::= { jnxPfeMemoryErrorsEntry 3 }

    jnxPfeEccErrors OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The ECC error count"
        ::= { jnxPfeMemoryErrorsEntry 4 }
     

pfeMemoryErrorsNotificationPrefix OBJECT IDENTIFIER ::= { jnxPfeNotification 0 }

pfeMemoryErrors NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeParityErrors,
        jnxPfeEccErrors
    }
    STATUS current
    DESCRIPTION
        "A pfeMemoryErrors notification is sent when the value
        of jnxPfeParityErrors or jnxPfeEccErrors increases." 

    ::= { pfeMemoryErrorsNotificationPrefix 1 }

--
-- This contains all PFE Memory statistics data.
--
jnxPfeMemory    OBJECT IDENTIFIER ::= { jnxPfeMib 2 }

--
-- This table provide global PFE Memory ASIC and Ukern stats for each PFE slot,
--
jnxPfeMemoryUkernTable OBJECT-TYPE
   SYNTAX          SEQUENCE OF JnxPfeMemoryUkernEntry
   MAX-ACCESS      not-accessible
   STATUS          current
   DESCRIPTION
            "This table provides global PFE ukern memory statistics for
specified slot."
   ::= { jnxPfeMemory 1 }

jnxPfeMemoryUkernEntry OBJECT-TYPE
   SYNTAX          JnxPfeMemoryUkernEntry
   MAX-ACCESS      not-accessible
   STATUS          current
   DESCRIPTION
            "Entry represent ukern memory percentage free."
    INDEX { jnxPfeGlSlot }
   ::= { jnxPfeMemoryUkernTable 1 }

JnxPfeMemoryUkernEntry ::=
   SEQUENCE {
        jnxPfeGlSlot                    Unsigned32,
        jnxPfeMemoryUkernFreePercent    Unsigned32
   }

jnxPfeMemoryUkernFreePercent  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    UNITS       "percent"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "The percent PFE ukern memory free within ukern heap."
    ::= { jnxPfeMemoryUkernEntry 2 }
--
-- This table provide global PFE NH (Trio) /JTREE Memory(I-Chip) and FW /
-- Filter and Encap (I-chip Iwo SRAM) memory stats for each PFE slot,
--
jnxPfeMemoryForwardingTable OBJECT-TYPE
   SYNTAX          SEQUENCE OF JnxPfeMemoryForwardingEntry
   MAX-ACCESS      not-accessible
   STATUS          current
   DESCRIPTION
            "This table provides PFE ASIC memory - NH/JTREE or FW/Filter or Encap 
             memory utilization statistics."
   ::= { jnxPfeMemory 2 }

jnxPfeMemoryForwardingEntry OBJECT-TYPE
   SYNTAX          JnxPfeMemoryForwardingEntry
   MAX-ACCESS      not-accessible
   STATUS          current
   DESCRIPTION
            "Entry represent ASIC memory free percent of a specific type 
             in specified pfe instance"
    INDEX { jnxPfeGlSlot, jnxPfeMemoryForwardingChipSlot, jnxPfeMemoryType }
   ::= { jnxPfeMemoryForwardingTable 1 }

JnxPfeMemoryForwardingEntry ::=
   SEQUENCE {
       jnxPfeMemoryForwardingChipSlot         Unsigned32,
       jnxPfeMemoryType                       JnxPfeMemoryTypeEnum,
       jnxPfeMemoryForwardingPercentFree      Unsigned32
   }

jnxPfeMemoryForwardingChipSlot  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..3)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "ASIC instance number or pfe complex instance number."
    ::= { jnxPfeMemoryForwardingEntry 1 }

jnxPfeMemoryType  OBJECT-TYPE
    SYNTAX      JnxPfeMemoryTypeEnum
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
            "PFE ASIC memory type, nh = 1, fw = 2, encap = 3."
    ::= { jnxPfeMemoryForwardingEntry 2 }

jnxPfeMemoryForwardingPercentFree  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    UNITS       "percent"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
            "Percentage ASIC memory free for a specific memory type. For Trio based linecards Encap memory is not available.Hence no value is returned"
    ::= { jnxPfeMemoryForwardingEntry 3 }

--
-- PFE Memory Notification Variables/Objects
--
    jnxPfeMemoryTrapVars OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "PFE notification object definitions."
        ::= { jnxPfeMemory 3 }
    jnxPfeGlSlot OBJECT-TYPE
        SYNTAX      Unsigned32 (0..4294967295)
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
                "Global slot number for line card resource monitoring."
        ::= { jnxPfeMemoryTrapVars 1 }

    jnxPfeInstanceNumber OBJECT-TYPE
        SYNTAX      Integer32 (0..3)
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "PFE instance number in pfe complex."
        ::= { jnxPfeMemoryTrapVars 2 }
 
    jnxPfeMemoryThreshold OBJECT-TYPE
        SYNTAX      Unsigned32 (0..100)
        UNITS       "percent"
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Configured high memory utilization threshold."
        ::= { jnxPfeMemoryTrapVars 3 }

jnxPfeMemoryNotificationsPrefix OBJECT IDENTIFIER ::= { jnxPfeMemory 4 }
jnxPfeMemoryNotifications OBJECT IDENTIFIER ::= { jnxPfeMemoryNotificationsPrefix 0 }

jnxPfeHeapMemoryThresholdExceeded NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the Heap Memory utilization has crossed the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 1 }

jnxPfeHeapMemoryThresholdAbated NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the Heap Memory utilization has fallen below the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 2 }

jnxPfeNextHopMemoryThresholdExceeded NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeInstanceNumber,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the Next Hop Memory utilization has crossed the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 3 }

jnxPfeNextHopMemoryThresholdAbated NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeInstanceNumber,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the Next Hop Memory utilization has fallen below the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 4 }

jnxPfeFilterMemoryThresholdExceeded NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeInstanceNumber,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the Filter Memory utilization has crossed the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 5 }

jnxPfeFilterMemoryThresholdAbated NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeInstanceNumber,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the Filter Memory utilization has fallen below the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 6 }

jnxPfeEncapMemoryThresholdExceeded NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeInstanceNumber,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the ENCAP Memory utilization has crossed the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 7 }

jnxPfeEncapMemoryThresholdAbated NOTIFICATION-TYPE
    OBJECTS {
        jnxPfeGlSlot,
        jnxPfeInstanceNumber,
        jnxPfeMemoryThreshold }
    STATUS      current
    DESCRIPTION
        "Indicates that the ENCAP Memory utilization has fallen below the
         configured watermark."
    ::= { jnxPfeMemoryNotifications 8 }

END
