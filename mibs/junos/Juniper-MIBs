
-- *****************************************************************************
-- MIB module identifier assignments for Juniper Networks E-series products.
--
-- Copyright (c) 1999, 2002 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2004 Juniper Networks, Inc.
--   All Rights Reserved.
-- *****************************************************************************

Juniper-MIBs  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY
        FROM SNMPv2-SMI
    juniperUniMibs
        FROM Juniper-UNI-SMI;

juniMibs  MODULE-IDENTITY
    LAST-UPDATED "200508191421Z"  -- 19-Aug-05 02:21 PM EDT
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        Email:  <EMAIL>"
    DESCRIPTION
        "The assignment of the MIB module object identifiers for Juniper
        E-series product MIBs located under the Juniper Networks JUNOSe
        top-level MIB module object identifier (juniMibs)."
    -- Revision History
	REVISION    "200601010000Z"  -- 01-Jan-06 00:00 PM EDT  - JUNOSe 7.3
    DESCRIPTION
        "Added MIB module OIDs for:
         - DOS Protection MIB
         - HTTP Protection Platform MIB."
    REVISION    "200508191421Z"  -- 19-Aug-05 02:21 PM EDT  - JUNOSe 8.0
    DESCRIPTION
        "Added MIB module OIDs for:
         - HTTP MIB 
         - HTTP Profile MIB."
    REVISION    "200506301803Z"  -- 30-Jun-05 02:03 PM EDT  - JUNOSe 7.2
    DESCRIPTION
        "Added MIB module OIDs for:
         - Packet Mirror MIB."
    REVISION    "200406072057Z"  -- 07-Jun-04 04:57 PM EDT  - JUNOSe 6.0
    DESCRIPTION
        "Added MIB module OIDs for:
         - RADIUS Proxy MIB 
         - RADIUS Initiated Request MIB."
    REVISION    "200311242102Z"  -- 24-Nov-03 04:02 PM EST  - JUNOSe 5.3
    DESCRIPTION
        "Added MIB module OIDs for:
         - Disman Event MIB
         - IP Security Tunnel MIB
         - Internet Key Exchange MIB
         - Tunnel Server Management MIB."
    REVISION    "200311241829Z"  -- 24-Nov-03 01:29 PM EST  - JUNOSe 5.1
    DESCRIPTION
        "Added MIB module OIDs for:
         - ATM 1483 Profile MIB
         - IPv6 Profile MIB
         - DHCPv6 MIB."
    REVISION    "200305052125Z"  -- 05-May-03 05:25 PM EDT  - JUNOSe 5.0
    DESCRIPTION
        "Replaced Unisphere names with Juniper names.
         Added MIB module OIDs for:
         - Bridge MIB
         - Bridging manager MIB
         - Multicast router MIB
         - Tunnel Server Manager MIB
         - RADIUS disconnect MIB."
    REVISION    "200304291418Z"  -- 29-Apr-03 10:18 AM EDT  - JUNOSe 4.1
    DESCRIPTION
        "Added MIB module OID for the TACACS+ client MIB."
    REVISION    "200304231356Z"  -- 23-Apr-03 09:56 AM EDT  - JUNOSe 4.0
    DESCRIPTION
        "Added MIB module OIDs for:
         - Multiprotocol Label Switching (MPLS) MIB
         - System Clock MIB
         - Quality of Service (QoS) MIB
         - X.21/V.35 interface MIB.
         Obsolete L2F MIB."
    REVISION    "200205311433Z"  -- 31-May-02 10:33 AM EDT  - JUNOSe 3.6
    DESCRIPTION
        "Added MIB module OID for the L2TP Dialout MIB."
    REVISION    "200111301412Z"  -- 30-Nov-01 09:12 AM EST  - JUNOSe 3.2
    DESCRIPTION
        "Added IP Tunnel (GRE/DVMRP) MIB, Connection-Based Forwarding (CBF) MIB
        and Layer 2 Forwarding (L2F) Protocol MIB module OIDs."
    REVISION    "200012271550Z"  -- 27-Dec-00 10:50 AM EST  - JUNOSe 3.1
    DESCRIPTION
        "Added SMDS MIB module OID."
    REVISION    "200011220000Z"  -- 22-Nov-00               - JUNOSe 3.0
    DESCRIPTION
        "Revised .mi2 filenames in descriptions for PPP and PPPOE Profile MIBs.
         Added AUTOCONFIGURE and SUBSCRIBER MIBs."
    REVISION    "200009191540Z"  -- 19-Sep-00 11:40 AM EDT  - JUNOSe 2.0
    DESCRIPTION
        "Replaced OBJECT-IDENTITYs with OBJECT IDENTIFIERs.
         Added new MIB module IDs."
    REVISION      "9912151544Z"  -- 15-Dec-99 10:44 AM EST  - JUNOSe 1.3
    DESCRIPTION
        "Added OBJECT-IDENTITY definitions for all MIB module IDs."
    REVISION      "9911080000Z"  -- 08-Nov-99               - JUNOSe 1.2
    DESCRIPTION
        "Initial version of this management information module."
    ::= { juniperUniMibs 2 }


-- *****************************************************************************
-- The assignment of all Juniper Networks E-series products' MIB module
-- identifiers are contained in this section of the SNMP object identifier
-- registration tree.  MIB object identifiers are allocated beneath a MIB's
-- MODULE-IDENTITY node.
-- *****************************************************************************
juniTextualConventions  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Textual Conventions module for Juniper Networks E-series products."
--  REFERENCE
--      "The textual convention are defined in the Juniper-TC module (in the
--      juniTc.mi2 file)."
    ::= { juniMibs 1 }

juniSystemMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The generic system MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-System-MIB module (in the
--      juniSystem.mi2 file)."
    ::= { juniMibs 2 }

juniIfMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Generic Interfaces MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-UNI-IF-MIB module (in the
--      juniIf.mi2 file)."
    ::= { juniMibs 3 }

juniDs3MIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "DS3/E3 MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DS3-MIB module (in the
--      juniDs3.mi2 file)."
    ::= { juniMibs 4 }

juniDs1MIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "DS1/E1 MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DS1-MIB module (in the
--      juniDs1.mi2 file)."
    ::= { juniMibs 5 }

juniFt1MIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Fractional T1 arrangements MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-FRACTIONAL-T1-MIB module (in
--      the juniFt1.mi2 file)."
    ::= { juniMibs 6 }

juniSonetMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "SONET MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-SONET-MIB module (in the
--      juniSonet.mi2 file)."
    ::= { juniMibs 7 }

juniAtmMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "ATM MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-UNI-ATM-MIB module (in the
--      juniAtm.mi2 file)."
    ::= { juniMibs 8 }

juniHdlcMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "HDLC MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-HDLC-MIB module (in the
--      juniHdlc.mi2 file)."
    ::= { juniMibs 9 }

juniFrameRelayMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Frame Relay MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-FRAME-RELAY-MIB module (in
--      the juniFr.mi2 file)."
    ::= { juniMibs 10 }

juniPppMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "PPP MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-PPP-MIB module (in the
--      juniPpp.mi2 file)."
    ::= { juniMibs 11 }

juniIpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "IP Protocol, Interfaces, Forwarding MIBs module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IP-MIB module (in the
--      juniIp.mi2 file)."
    ::= { juniMibs 12 }

juniIpPolicyMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "IP Access Lists, Route Maps MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IP-POLICY-MIB module (in the
--      juniIpPolicy.mi2 file)."
    ::= { juniMibs 13 }

juniOspfMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "OSPF MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-OSPF-MIB module (in the
--      juniOspf.mi2 file)."
    ::= { juniMibs 14 }

juniSlepMIBS  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "SLEP MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-SLEP-MIB module (in the
--      juniSlep.mi2 file)."
    ::= { juniMibs 15 }

juniSnmpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "SNMP MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-SNMP-MIB module (in the
--      juniSnmp.mi2 file)."
    ::= { juniMibs 16 }

juniERXSysMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "System MIB specific to the ERX1400/ERX700/ERX300 products module for
--      Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-ERX-System-MIB module (in
--      the usErxSystem.mi2 file)."
    ::= { juniMibs 17 }

juniPPPoEMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "PPP-over-Ethernet MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-PPPOE-MIB module (in the
--      juniPppoe.mi2 file)."
    ::= { juniMibs 18 }

juniRadiusClientMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "RADIUS Client MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-RADIUS-CLIENT-MIB module (in
--      the juniRadClient.mi2 file)."
    ::= { juniMibs 19 }

juniAaaMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Authentication, Authorization, and Accounting Server MIB module for
--      Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-AAA-MIB module (in the
--      juniAaa.mi2 file)."
    ::= { juniMibs 20 }

juniAddressPoolMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Address Pool MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-ADDRESS-POOL-MIB module (in
--      the juniAddrPool.mi2 file)."
    ::= { juniMibs 21 }

juniDhcpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "DHCP MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DHCP-MIB module (in the
--      juniDhcp.mi2 file)."
    ::= { juniMibs 22 }

juniFileXferMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "File Transfer MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-FILE-XFER-MIB module (in the
--      juniFileXfer.mi2 file)."
    ::= { juniMibs 23 }

juniAcctngMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Bulk Stats MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-ACCOUNTING-MIB module (in
--      the juniAcctng.mi2 file)."
    ::= { juniMibs 24 }

juniProfileMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Name/identifier mapping of configuration profiles MIB module for
--      Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-PROFILE-MIB module (in the
--      juniTmpl.mi2 file)."
    ::= { juniMibs 25 }

juniIpProfileMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "IP interface profile configuration MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IP-PROFILE-MIB module (in
--      the juniTmplIp.mi2 file)."
    ::= { juniMibs 26 }

juniPolicyMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Policy Management MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-POLICY-MIB module (in the
--      juniPolicy.mi2 file)."
    ::= { juniMibs 27 }

juniLogMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Logging Configuration and Monitoring MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-LOG-MIB module (in the
--      juniLog.mi2 file)."
    ::= { juniMibs 28 }

juniBgpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "BGP MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-BGP-MIB module (in the
--      juniBgp.mi2 file)."
    ::= { juniMibs 29 }

juniCliMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "CLI MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-CLI-MIB module (in the
--      juniCli.mi2 file)."
    ::= { juniMibs 30 }

juniBridgeEthernetMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Bridged Ethernet MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-BRIDGE-ETHERNET-MIB module
--      (in the juniBridgeEthernet.mi2 file)."
    ::= { juniMibs 31 }

juniRouterMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Router MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-ROUTER-MIB module (in the
--      juniRouter.mi2 file)."
    ::= { juniMibs 32 }

juniHostMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Host configuration MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-HOST-MIB module (in the
--      juniHost.mi2 file)."
    ::= { juniMibs 33 }

juniEthernetMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Ethernet MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-ETHERNET-MIB module (in the
--      juniEthernet.mi2 file)."
    ::= { juniMibs 34 }

juniL2tpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Layer 2 Tunneling Protocol (L2TP) MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-L2TP-MIB module (in the
--      juniL2tp.mi2 file)."
    ::= { juniMibs 35 }

juniSscClientMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "SSC MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-SSC-CLIENT-MIB module (in
--      the juniSscClient.mi2 file)."
    ::= { juniMibs 36 }

juniCopsProtocolMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "COPS MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-COPS-MIB module (in the
--      juniCops.mi2 file)."
    ::= { juniMibs 37 }

juniIsisMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "ISIS MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-ISIS-MIB module (in the
--      juniIsis.mi2 file)."
    ::= { juniMibs 38 }

juniPingMIB  OBJECT IDENTIFIER
--  STATUS      obsolete
--  DESCRIPTION
--      "Obsolete IETF Distributed Management Ping MIB folded into Juniper
--      Networks E-series products.  This MIB has been replaced by the Internet
--      Standards Track DISMAN-PING-MIB (RFC 2925)."
    ::= { juniMibs 39 }

juniIgmpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "IETF IGMP MIB folded into Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IGMP-MIB module (in the
--      juniIgmp.mi2 file)."
    ::= { juniMibs 40 }

juniTraceRouteMIB  OBJECT IDENTIFIER
--  STATUS      obsolete
--  DESCRIPTION
--      "Obsolete IETF Distributed Management Trace-route MIB folded into
--      Juniper Networks E-series products.  This MIB has been replaced by the
--      Internet Standards Track DISMAN-TRACEROUTE-MIB (RFC 2925)."
    ::= { juniMibs 41 }

juniLookupMIB  OBJECT IDENTIFIER
--  STATUS      obsolete
--  DESCRIPTION
--      "Obsolete IETF Distributed Management NS Lookup MIB folded into Juniper
--      Networks E-series products.  This MIB has been replaced by the Internet
--      Standards Track DISMAN-NSLOOKUP-MIB (RFC 2925)."
    ::= { juniMibs 42 }

juniPimMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "PIM MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-PIM-MIB module (in the
--      juniPim.mi2 file)."
    ::= { juniMibs 43 }

juniDvmrpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Distance Vector Multicast Routing Protocol (DVMRP) MIB for Juniper
--      Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DVMRP-MIB module (in the
--      juniDvmrp.mi2 file)."
    ::= { juniMibs 44 }

juniPppProfileMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "PPP interface profile configuration MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-PPP-Profile-MIB module (in
--      the juniTmplPpp.mi2 file)."
    ::= { juniMibs 45 }

juniPppoeProfileMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "PPP over Ethernet interface profile configuration MIB module for
--      Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-PPPoE-Profile-MIB module (in
--      the juniTmplPppoe.mi2 file)."
    ::= { juniMibs 46 }

juniDnsMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Domain Name System (DNS) management MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DNS-MIB module (in the
--      juniDns.mi2 file)."
    ::= { juniMibs 47 }

juniAutoConfMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Auto-Configuration management MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-AUTOCONFIGURE-MIB module (in
--      the juniAutoconf.mi2 file)."
    ::= { juniMibs 48 }

juniSubscriberMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Subscriber management MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-SUBSCRIBER-MIB module (in
--      the juniSubscriber.mi2 file)."
    ::= { juniMibs 49 }

juniSmdsMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Switched Multimegabit Data Service (SMDS) MIB module for Juniper
--      Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-SMDS-MIB module (in the
--      juniSmds.mi2 file)."
    ::= { juniMibs 50 }

juniIpTunnelMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "IP Tunnel (GRE/DVMRP) MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IP-TUNNEL-MIB module (in the
--      juniIpTunnel.mi2 file)."
    ::= { juniMibs 51 }

juniCbfMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Connection-Based Forwarding (CBF) MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-CBF-MIB module (in the
--      juniCbf.mi2 file)."
    ::= { juniMibs 52 }

juniL2fMIB  OBJECT IDENTIFIER
--  STATUS      obsolete
--  DESCRIPTION
--      "Obsolete Layer 2 Forwarding (L2F) Protocol MIB module for Juniper
--      Networks E-series products."
    ::= { juniMibs 53 }

juniMplsMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Multiprotocol Label Switching (MPLS) MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-MPLS-MIB module (in the
--      juniMpls.mi2 file)."
    ::= { juniMibs 54 }

juniMrxSystemMIB  OBJECT IDENTIFIER
--  STATUS      obsolete
--  DESCRIPTION
--      "This value is not used."
    ::= { juniMibs 55 }

juniSysClockMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "System Clock MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-System-Clock-MIB module (in
--      the juniSysClock.mi2 file)."
    ::= { juniMibs 56 }

juniQosMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Quality of Service (QoS) MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-QoS-MIB module (in the
--      juniQos.mi2 file)."
    ::= { juniMibs 57 }

juniAtm1483ProfileMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "ATM 1483 interface profile configuration MIB module for Juniper
--      Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-ATM-1483-Profile-MIB module
--      (in the juniTmplAtm.mi2 file)."
    ::= { juniMibs 58 }

juniV35MIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The X.21/V.35 interface configuration MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-V35-MIB module (in the
--      juniV35.mi2 file)."
    ::= { juniMibs 59 }

juniTacacsPlusClientMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Terminal Access Controller Access Control System Plus (TACACS+)
--      Client MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-TACACS-Plus-Client-MIB
--      module (in the juniTcsClient.mi2 file)."
    ::= { juniMibs 60 }

juniL2tpDialoutMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Layer 2 Tunneling Protocol (L2TP) Dialout MIB module for Juniper
--      Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-L2TP-Dialout-MIB module (in
--      the juniL2tpDialout.mi2 file)."
    ::= { juniMibs 62 }

juniBridgeMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Bridge MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-BRIDGE-MIB module (in the
--      juniBridge.mi2 file)."
    ::= { juniMibs 63 }

juniBridgingMgrMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Bridging manager MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-Bridging-Manager-MIB module
--      (in the juniBridgingMgr.mi2 file)."
    ::= { juniMibs 64 }

juniMRouterMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The IP multicast router MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-MROUTER-MIB module (in the
--      juniMRouter.mi2 file)."
    ::= { juniMibs 65 }

juniDismanEventMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Distributed Management (Disman) Event MIB extension module for
--      Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DISMAN-EVENT-MIB module (in
--      the juniDismanEvent.mi2 file)."
    ::= { juniMibs 66 }

juniRadiusDisconnectMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The RADIUS Disconnect MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-RADIUS-Disconnect-MIB module
--      (in the juniRadDiscon.mi2 file)."
    ::= { juniMibs 67 }

juniIpv6ProfileMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The IPv6 interface profile configuration MIB module for Juniper
--      Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IPV6-PROFILE-MIB module (in
--      the juniTmplIpv6.mi2 file)."
    ::= { juniMibs 68 }

juniDhcpv6MIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The DHCPv6 MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DHCPv6-MIB module (in the
--      juniDhcpv6.mi2 file)."
    ::= { juniMibs 69 }

juniIpsecTunnelMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The IP Security Tunnel MIB module for Juniper Networks E-series
--      products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IPsec-Tunnel-MIB module (in
--       the juniIpsecTunnel.mi2 file)."
    ::= { juniMibs 70 }

juniIkeMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Internet Key Exchange (IKE) MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-IKE-MIB module (in the
--      juniIke.mi2 file)."
    ::= { juniMibs 71 }

juniTsmMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "The Tunnel Server Management (TSM) MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-TSM-MIB module (in the
--      juniTsm.mi2 file)."
    ::= { juniMibs 72 }

juniRadiusProxyMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "RADIUS Proxy MIB module for Juniper Networks  E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-RADIUS-Proxy-MIB module (in
--      the juniRadProxy.mi2 file)."
    ::= { juniMibs 73 }

juniRedundancyMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "High Availability Redundancy MIB module for Juniper Networks edge 
--      router products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-REDUNDANCY-MIB module
--      (in the juniRedundancy.mi2 file)."
    ::= { juniMibs 74 }

juniRadiusRequestMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "RADIUS Initiated Request MIB module for Juniper Networks edge 
--      router products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-RADIUS-Initiated-Request-MIB
--       module (in the juniRadRequest.mi2 file)."
    ::= { juniMibs 75 }

juniLicenseMgrMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "License Manager MIB module for Juniper Networks edge router products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-LICENSE-MIB
--       module (in the juniLicense.mi2 file)."
    ::= { juniMibs 76 }

juniPacketMirrorMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "Packet Mirror MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-PACKET-MIRROR-MIB module (in the
--      juniPacketMirror.mi2 file)."
    ::= { juniMibs 77 }

juniHttpMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "HTTP server MIB module for Juniper Networks E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-HTTP-MIB module (in
--      the juniHttp.mi2 file)."
    ::= { juniMibs 78 }

juniHttpProfileMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "HTTP interface profile configuration MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-HTTP-PROFILE-MIB module (in
--      the juniTmplHttp.mi2 file)."
    ::= { juniMibs 79 }

juniDosProtectionMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "DOS Protection MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DOS-PROTECTION-MIB module (in
--      the juniTmplDosprotection.mi2 file)."
    ::= { juniMibs 80 }

juniDosProtectionPlatformMIB  OBJECT IDENTIFIER
--  STATUS      current
--  DESCRIPTION
--      "DOS Protection MIB module for Juniper Networks
--      E-series products."
--  REFERENCE
--      "The MIB objects are defined in the Juniper-DOS-PROTECTION-PLATFORM-MIB 
--      module (in the juniTmplDosprotectionplat.mi2 file)."
    ::= { juniMibs 81 } 
    
juniInetMIB   OBJECT IDENTIFIER
--  STATUS     current
--  DESCRIPTION
--      "IPv6 MIB for Juniper Networks E-Series products"
--  REFERENCE
--      "The MIB objects are defined in the Juniper-INET-MIB module (in the
--      juniInet.mi2 file)." 
    ::= { juniMibs 82 }  

END
