--
-- $Id: jnx-ipv6.mib,v 1.3 2003-07-18 22:57:37 dchuang Exp $
-- Juniper Enterprise Specific MIB: ipv6 MIB Extension
-- 
-- Copyright (c) 2001-2011, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-IPv6-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Counter64
        FROM SNMPv2-SMI
   ipv6IfEntry 
        FROM IPV6-MIB
    jnxMibs
        FROM JUNIPER-SMI;

jnxIpv6 MODULE-IDENTITY
    LAST-UPDATED "200307182153Z" -- Fri Jul 18 21:53:53 2003 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"

    DESCRIPTION
            "The MIB modules extends the ifTable as
             defined in IF-MIB."
    REVISION      "200108310000Z" 
    DESCRIPTION
               "Initial revision."
    ::= { jnxMibs 11 }



--
-- define branches for ipv6 stats
--
jnxIpv6Stats         OBJECT IDENTIFIER ::= { jnxIpv6 1 }
jnxIpv6GlobalStats   OBJECT IDENTIFIER ::= { jnxIpv6Stats 1 }
jnxIcmpv6GlobalStats OBJECT IDENTIFIER ::= { jnxIpv6Stats 2 }
jnxIpv6IfStats       OBJECT IDENTIFIER ::= { jnxIpv6Stats 3 }

--
-- Ipv6 Global Stats
--

    jnxIpv6StatsReceives OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received."
         ::= { jnxIpv6GlobalStats 1 }

    jnxIpv6StatsTooShorts OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with size smaller than minimum."
         ::= { jnxIpv6GlobalStats 2 }

    jnxIpv6StatsTooSmalls OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with data size less than data 
             length."
         ::= { jnxIpv6GlobalStats 3 }

    jnxIpv6StatsBadOptions OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets encountering errors in option 
             processing."
         ::= { jnxIpv6GlobalStats 4 }

    jnxIpv6StatsBadVersions OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with incorrect version number."
         ::= { jnxIpv6GlobalStats 5 }

    jnxIpv6StatsFragments OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of packet fragments received." 
         ::= { jnxIpv6GlobalStats 6 }

    jnxIpv6StatsFragDrops OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of fragments dropped (duplicates or out of 
             space)."
         ::= { jnxIpv6GlobalStats 7 }

    jnxIpv6StatsFragTimeOuts OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of fragments dropped due to timeouts."
         ::= { jnxIpv6GlobalStats 8 }

    jnxIpv6StatsFragOverFlows OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of fragments that exceeded limit."
         ::= { jnxIpv6GlobalStats 9 }

    jnxIpv6StatsReasmOKs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets reassembled successfully."
         ::= { jnxIpv6GlobalStats 10 }

    jnxIpv6StatsDelivers OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets delivered to upper layers."
         ::= { jnxIpv6GlobalStats 11 }

    jnxIpv6StatsForwards OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets forwarded."
         ::= { jnxIpv6GlobalStats 12 }

    jnxIpv6StatsUnreachables OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received for unreachable 
             destinations."
         ::= { jnxIpv6GlobalStats 13 }

    jnxIpv6StatsRedirects OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets forwarded on the same net as 
             received."
         ::= { jnxIpv6GlobalStats 14 }

    jnxIpv6StatsOutRequests OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets generated by this host."
         ::= { jnxIpv6GlobalStats 15 }

    jnxIpv6StatsRawOuts OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets sent with fabricated IPv6 
             headers."
         ::= { jnxIpv6GlobalStats 16 }

    jnxIpv6StatsOutDiscards OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of output IPv6 packets dropped due to no bufs, 
             etc."
         ::= { jnxIpv6GlobalStats 17 }

    jnxIpv6StatsOutNoRoutes OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of output IPv6 packets discarded because no route
             could be found."
         ::= { jnxIpv6GlobalStats 18 }

    jnxIpv6StatsOutFragOKs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of output IPv6 packets fragmented."
         ::= { jnxIpv6GlobalStats 19 }

    jnxIpv6StatsOutFragCreates OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of output IPv6 packets that have been generated
             as a result of fragmentation at the output interface."
         ::= { jnxIpv6GlobalStats 20 }

    jnxIpv6StatsOutFragFails OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of output IPv6 packets that can't be fragmented."
         ::= { jnxIpv6GlobalStats 21 }

    jnxIpv6StatsBadScopes OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets that violate scope rules."
         ::= { jnxIpv6GlobalStats 22 }

    jnxIpv6StatsNotMcastMembers OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 multicast packets which we don't join."
         ::= { jnxIpv6GlobalStats 23 }

    jnxIpv6StatsHdrNotContinuous OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets whose headers are not continuous."
         ::= { jnxIpv6GlobalStats 24 }

    jnxIpv6StatsNoGifs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of tunneling IPv6 packets that can't find gif."
         ::= { jnxIpv6GlobalStats 25 }

    jnxIpv6StatsTooManyHdrs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets discarded due to too many 
             headers."
         ::= { jnxIpv6GlobalStats 26 }

    jnxIpv6StatsForwCacheHits OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of forward cache hits."
         ::= { jnxIpv6GlobalStats 27 }

    jnxIpv6StatsForwCacheMisses OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of forward cache misses."
         ::= { jnxIpv6GlobalStats 28 }

    jnxIpv6StatsOutDeadNextHops OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of output IPv6 packets destined to dead next 
             hops."
         ::= { jnxIpv6GlobalStats 29 }

    jnxIpv6StatsOptRateDrops OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 option packets dropped due to rate 
             limits."
         ::= { jnxIpv6GlobalStats 30 }

    jnxIpv6StatsMCNoDests OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 Multicast packets dropped due to no 
             destination."
         ::= { jnxIpv6GlobalStats 31 }

    --
    -- The following objects provide counts of all the different IPv6
    -- next header types received 
    --

    jnxIpv6StatsInHopByHops OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with a Hop by Hop 
             next header." 
         ::= { jnxIpv6GlobalStats 32 }

    jnxIpv6StatsInIcmps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with an ICMP(v4) next
             header." 
         ::= { jnxIpv6GlobalStats 33 }

    jnxIpv6StatsInIgmps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with an IGMP next 
             header." 
         ::= { jnxIpv6GlobalStats 34 }

    jnxIpv6StatsInIps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with an IPv4 next 
             header." 
         ::= { jnxIpv6GlobalStats 35 }

    jnxIpv6StatsInTcps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with a TCP  next header." 
         ::= { jnxIpv6GlobalStats 36 }

    jnxIpv6StatsInUdps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with a UDP  next header." 
         ::= { jnxIpv6GlobalStats 37 }

    jnxIpv6StatsInIdps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with an xns IDP  
             next header." 
         ::= { jnxIpv6GlobalStats 38 }

    jnxIpv6StatsInTps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets received with a TP4  next header." 
         ::= { jnxIpv6GlobalStats 39 }

    jnxIpv6StatsInIpv6s OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an IPv6  next header." 
         ::= { jnxIpv6GlobalStats 40 }

    jnxIpv6StatsInRoutings OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an IPv6 Routing next header."
         ::= { jnxIpv6GlobalStats 41 }

    jnxIpv6StatsInFrags OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an IPv6 Fragment next 
             header." 
         ::= { jnxIpv6GlobalStats 42 }

    jnxIpv6StatsInEsps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an IPv6 ESP next header." 
         ::= { jnxIpv6GlobalStats 43 }

    jnxIpv6StatsInAhs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an IPv6 AH next header." 
         ::= { jnxIpv6GlobalStats 44 }

    jnxIpv6StatsInIcmpv6s OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an ICMPv6 next header." 
         ::= { jnxIpv6GlobalStats 45 }

    jnxIpv6StatsInNoNhs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with no next header." 
         ::= { jnxIpv6GlobalStats 46 }

    jnxIpv6StatsInDestOpts OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an IPv6 Destination Option 
             next header." 
         ::= { jnxIpv6GlobalStats 47 }

    jnxIpv6StatsInIsoIps OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an ISO CLNP next header." 
         ::= { jnxIpv6GlobalStats 48 }

    jnxIpv6StatsInOspfs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an OSPF next header." 
         ::= { jnxIpv6GlobalStats 49 }

    jnxIpv6StatsInEths OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with an Ethernet next header." 
         ::= { jnxIpv6GlobalStats 50 }

    jnxIpv6StatsInPims OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of IPv6 packets with a PIM next header." 
         ::= { jnxIpv6GlobalStats 51 }

--
-- Icmpv6 Global Stats
--

    jnxIcmpv6StatsErrors OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of calls to icmp_error."
         ::= { jnxIcmpv6GlobalStats 1 }

    jnxIcmpv6StatsCantErrors OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of errors not generated because the the old
            message was an icmp error."
         ::= { jnxIcmpv6GlobalStats 2 }

    jnxIcmpv6StatsTooFreqs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of errors not generated due to rate limitations."
         ::= { jnxIcmpv6GlobalStats 3 }

    jnxIcmpv6StatsBadCodes OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of messages with bad code fields."
         ::= { jnxIcmpv6GlobalStats 4 }

    jnxIcmpv6StatsTooShorts OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of messages less than the minimum length."
         ::= { jnxIcmpv6GlobalStats 5 }

    jnxIcmpv6StatsBadChecksums OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of messages with bad checksums."
         ::= { jnxIcmpv6GlobalStats 6 }

    jnxIcmpv6StatsBadLenths OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of messages with bad length."
         ::= { jnxIcmpv6GlobalStats 7 }

    jnxIcmpv6StatsNoRoutes OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'no route' messages generated."
         ::= { jnxIcmpv6GlobalStats 8 }

    jnxIcmpv6StatsAdminProhibits OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'administratively prohibited' messages 
            generated."
         ::= { jnxIcmpv6GlobalStats 9 }

    jnxIcmpv6StatsBeyondScopes OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'beyond scope' messages generated."
         ::= { jnxIcmpv6GlobalStats 10 }

    jnxIcmpv6StatsAddrUnreachs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'address unreachable' messages generated."
         ::= { jnxIcmpv6GlobalStats 11 }

    jnxIcmpv6StatsPortUnreachs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'port unreachable' messages generated."
         ::= { jnxIcmpv6GlobalStats 12 }

    jnxIcmpv6StatsTooBigs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'Packet Too Big' messages generated."
         ::= { jnxIcmpv6GlobalStats 13 }

    jnxIcmpv6StatsExceedTrans OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'time exceed transit' messages generated."
         ::= { jnxIcmpv6GlobalStats 14 }

    jnxIcmpv6StatsExceedReasms OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'time exceed reassembly' messages generated."
         ::= { jnxIcmpv6GlobalStats 15 }

    jnxIcmpv6StatsBadHdrFields OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'erroneous header field' messages generated."
         ::= { jnxIcmpv6GlobalStats 16 }

    jnxIcmpv6StatsBadNextHdrs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'unrecognized next header' messages generated."
         ::= { jnxIcmpv6GlobalStats 17 }

    jnxIcmpv6StatsBadOptions OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'unrecognized option' messages generated."
         ::= { jnxIcmpv6GlobalStats 18 }

    jnxIcmpv6StatsRedirects OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'redirect' messages generated."
         ::= { jnxIcmpv6GlobalStats 19 }

    jnxIcmpv6StatsOthers OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of generated messages types other than the ones 
             listed above."
         ::= { jnxIcmpv6GlobalStats 20 }

    jnxIcmpv6StatsResponses OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of message responses generated."
         ::= { jnxIcmpv6GlobalStats 21 }

    jnxIcmpv6StatsExcessNDOptions OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of messages processed with too many ND options."
         ::= { jnxIcmpv6GlobalStats 22 }

    --
    -- The following objects provide counts of all the different ICMPv6
    -- message types received and transmitted.
    --

    jnxIcmpv6StatsInUnreachables OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Unreachable messages received."
         ::= { jnxIcmpv6GlobalStats 23 }

    jnxIcmpv6StatsInPktTooBigs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'Packet Too Big' messages received."
         ::= { jnxIcmpv6GlobalStats 24 }

    jnxIcmpv6StatsInTimeExceeds OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Time Exceeded messages received."
         ::= { jnxIcmpv6GlobalStats 25 }

    jnxIcmpv6StatsInParamProbs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Parameter Problem messages received."
         ::= { jnxIcmpv6GlobalStats 26 }

    jnxIcmpv6StatsInEchoReqs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Echo Request messages received."
         ::= { jnxIcmpv6GlobalStats 27 }

    jnxIcmpv6StatsInEchoReplies OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Echo Reply messages received."
         ::= { jnxIcmpv6GlobalStats 28 }

    jnxIcmpv6StatsInMLQueries OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Multicast Listener Query messages received."
         ::= { jnxIcmpv6GlobalStats 29 }

    jnxIcmpv6StatsInMLReports OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Multicast Listener Report messages received."
         ::= { jnxIcmpv6GlobalStats 30 }

    jnxIcmpv6StatsInMLDones OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Multicast Listener Done messages received."
         ::= { jnxIcmpv6GlobalStats 31 }

    jnxIcmpv6StatsInRtrSolicits OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Router Solicitation messages received."
         ::= { jnxIcmpv6GlobalStats 32 }

    jnxIcmpv6StatsInRtrAdvs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Router Advertisment messages received."
         ::= { jnxIcmpv6GlobalStats 33 }

    jnxIcmpv6StatsInNbrSolicits OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Neighbor Solicitation messages received."
         ::= { jnxIcmpv6GlobalStats 34 }

    jnxIcmpv6StatsInNbrAdvs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Neighbor Advertisement messages received."
         ::= { jnxIcmpv6GlobalStats 35 }

    jnxIcmpv6StatsInRedirects OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Redirect messages received."
         ::= { jnxIcmpv6GlobalStats 36 }

    jnxIcmpv6StatsInRtrRenumbers OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Router Renumber messages received."
         ::= { jnxIcmpv6GlobalStats 37 }

    jnxIcmpv6StatsInNIReqs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Node Information Request messages received."
         ::= { jnxIcmpv6GlobalStats 38 }

    jnxIcmpv6StatsInNIReplies OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Node Information Report messages received."
         ::= { jnxIcmpv6GlobalStats 39 }

    jnxIcmpv6StatsOutUnreachables OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Unreachable messages transmitted."
         ::= { jnxIcmpv6GlobalStats 40 }

    jnxIcmpv6StatsOutPktTooBigs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of 'Packet Too Big' messages to be transmitted.
            Some of these icmp messages may not be actually sent out (problems
            with rate limits, source addresses, etc). jnxIcmpv6StatsTooBigs 
            represents the number of 'Too Big Packet' messages that actually
            get out."
         ::= { jnxIcmpv6GlobalStats 41 }

    jnxIcmpv6StatsOutTimeExceeds OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Time Exceeded messages transmitted."
         ::= { jnxIcmpv6GlobalStats 42 }

    jnxIcmpv6StatsOutParamProbs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Parameter Problem messages transmitted."
         ::= { jnxIcmpv6GlobalStats 43 }

    jnxIcmpv6StatsOutEchoReqs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Echo Request messages transmitted."
         ::= { jnxIcmpv6GlobalStats 44 }

    jnxIcmpv6StatsOutEchoReplies OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Echo Reply messages transmitted."
         ::= { jnxIcmpv6GlobalStats 45 }

    jnxIcmpv6StatsOutMLQueries OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Multicast Listener Query messages transmitted."
         ::= { jnxIcmpv6GlobalStats 46 }

    jnxIcmpv6StatsOutMLReports OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Multicast Listener Report messages 
             transmitted."
         ::= { jnxIcmpv6GlobalStats 47 }

    jnxIcmpv6StatsOutMLDones OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Multicast Listener Done messages transmitted."
         ::= { jnxIcmpv6GlobalStats 48 }

    jnxIcmpv6StatsOutRtrSolicits OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Router Solicitation messages transmitted."
         ::= { jnxIcmpv6GlobalStats 49 }

    jnxIcmpv6StatsOutRtrAdvs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Router Advertisment messages transmitted."
         ::= { jnxIcmpv6GlobalStats 50 }

    jnxIcmpv6StatsOutNbrSolicits OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Neighbor Solicitation messages transmitted."
         ::= { jnxIcmpv6GlobalStats 51 }

    jnxIcmpv6StatsOutNbrAdvs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Neighbor Advertisement messages transmitted."
         ::= { jnxIcmpv6GlobalStats 52 }

    jnxIcmpv6StatsOutRedirects OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Redirect messages transmitted."
         ::= { jnxIcmpv6GlobalStats 53 }

    jnxIcmpv6StatsOutRtrRenumbers OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Router Renumber messages transmitted."
         ::= { jnxIcmpv6GlobalStats 54 }

    jnxIcmpv6StatsOutNIReqs OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Node Information Request messages transmitted."
         ::= { jnxIcmpv6GlobalStats 55 }

    jnxIcmpv6StatsOutNIReplies OBJECT-TYPE
         SYNTAX      Counter64
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
            "The total number of Node Information Report messages transmitted."
         ::= { jnxIcmpv6GlobalStats 56 }

    jnxIpv6IfStatsTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF JnxIpv6IfStatsEntry 
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The (conceptual) table containing 
                 IPv6 statistics of an interface"
        ::= { jnxIpv6IfStats 1 }

    jnxIpv6IfStatsEntry OBJECT-TYPE
        SYNTAX      JnxIpv6IfStatsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "An entry (conceptual row) containing 
                 IPv6 statistics entry of an interface"
        AUGMENTS      { ipv6IfEntry }
        ::= { jnxIpv6IfStatsTable 1 }

    JnxIpv6IfStatsEntry ::= SEQUENCE {
            jnxIpv6IfInOctets   Counter64,
            jnxIpv6IfOutOctets  Counter64
        }

    jnxIpv6IfInOctets OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The total number of octets received on the interface"
        ::= { jnxIpv6IfStatsEntry 1 }

    jnxIpv6IfOutOctets OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The total number of octets transmitted out of the
                 interface"
        ::= { jnxIpv6IfStatsEntry 2 }

END
