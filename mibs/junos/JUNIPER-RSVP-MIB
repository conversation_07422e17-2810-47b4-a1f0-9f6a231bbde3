--
-- Juniper Enterprise specific MIB: RSVP MIB
--
-- Copyright (c) 2004, 2007, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
-- 

JUNIPER-RSVP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Unsigned32
        FROM SNMPv2-SMI
    DisplayString, TimeStamp
        FROM SNMPv2-TC
    jnxMibs
        FROM JUNIPER-SMI;


jnxRsvpMIB MODULE-IDENTITY
    LAST-UPDATED "200402031905Z" -- Tuesday Feb 03 19:05:42 2004 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
		     Juniper Networks, Inc.
		     1133 Innovation Way
		     Sunnyvale, CA 94089
		     E-mail: <EMAIL>"
    DESCRIPTION
        "The MIB modules for Resource ReSerVation Protocol."
    REVISION
        "200706280930Z"  -- June 28, 2007
    DESCRIPTION
        "Extended jnxRsvpSessionName to support names up to 64 characters"
    ::= { jnxMibs 30 }

-- For now, the RSVP MIB is an enterprise (Juniper Inc.) private MIB.

jnxRsvpOperation OBJECT IDENTIFIER ::= {jnxRsvpMIB 1}

jnxRsvpSessionTable OBJECT-TYPE
    SYNTAX	SEQUENCE OF JnxRsvpSessionEntry
    MAX-ACCESS	not-accessible
    STATUS	current
    DESCRIPTION	"Defines the jnxRsvpSession Table for RSVP Sessions."
    ::= { jnxRsvpOperation 1 }


jnxRsvpSessionEntry OBJECT-TYPE
    SYNTAX	JnxRsvpSessionEntry
    MAX-ACCESS	not-accessible
    STATUS	current
    DESCRIPTION	
	"Defines an entry in the jnxRsvpSessionTable. The first index
	element jnxRsvpSessionName is similar to the LSP name in the MPLS
	MIB and can be used to co-relate the mplsLspEntry to an RSVP session 
	entry. There could be multiple entries with the same
	jnxRsvpSessionName and hence the need for a secondary index which is
	just an Unsigned32 to identify each of them uniquely. A management
	application may walk through all entries with the same
	jnxRsvpSessionName and based on the other RSVP session information in
	each entry, such as jnxRsvpSessionFrom and/or jnxRsvpSessionTo may
	decide to query a particular RSVP session."
    INDEX	{ jnxRsvpSessionName,
		  jnxRsvpSessionIndex }
    ::= { jnxRsvpSessionTable 1 }

JnxRsvpSessionEntry ::= 
    SEQUENCE {
        -- Indices
	jnxRsvpSessionName	DisplayString,
	jnxRsvpSessionIndex	Unsigned32,
	-- RSVP session information for co-relation
	jnxRsvpSessionState	INTEGER,
	jnxRsvpSessionFrom	IpAddress,
	jnxRsvpSessionTo	IpAddress,
	jnxRsvpSessionLspId	Unsigned32,
	jnxRsvpSessionTunnelId	Unsigned32,
	jnxRsvpSessionPathType	INTEGER,
	jnxRsvpSessionRole	INTEGER,
	-- MPLS stats
	jnxRsvpSessionDiscontinuityTime	TimeStamp,
	jnxRsvpSessionMplsOctets Counter64,
	jnxRsvpSessionMplsPackets Counter64
    }	

jnxRsvpSessionName OBJECT-TYPE
    SYNTAX	DisplayString (SIZE (0..64))
    MAX-ACCESS	not-accessible
    STATUS	current
    DESCRIPTION	"Name of the RSVP Session. This is the same as LSP name."
    ::= { jnxRsvpSessionEntry 1 }

jnxRsvpSessionIndex OBJECT-TYPE
    SYNTAX	Unsigned32
    MAX-ACCESS	not-accessible
    STATUS	current
    DESCRIPTION	"RSVP Session index."
    ::= { jnxRsvpSessionEntry 2 }

jnxRsvpSessionState OBJECT-TYPE
    SYNTAX	INTEGER {
		  up(1),
		  down(2) }
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION	"The operational state of the RSVP Session."
    ::= { jnxRsvpSessionEntry 3 }

jnxRsvpSessionFrom OBJECT-TYPE
    SYNTAX     IpAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION "Source IP address of this RSVP session."
    ::= { jnxRsvpSessionEntry 4 }

jnxRsvpSessionTo OBJECT-TYPE
    SYNTAX     IpAddress
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION "Destination IP address of this RSVP session."
    ::= { jnxRsvpSessionEntry 5 }

jnxRsvpSessionLspId OBJECT-TYPE
    SYNTAX	Unsigned32 (1..65535)
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION "LSP ID of the sender for this RSVP session."
    ::= { jnxRsvpSessionEntry 6 }

jnxRsvpSessionTunnelId OBJECT-TYPE
    SYNTAX	Unsigned32 (1..65535)
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION "Tunnel ID for the RSVP session."
    ::= { jnxRsvpSessionEntry 7 }

jnxRsvpSessionPathType OBJECT-TYPE
    SYNTAX	INTEGER {
	primary(1),
	secondary(2),
	unknown(3)
    }
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION
	"If the head-end router signals the type of path 
	corresponding to an RSVP session; viz. primary or secondary
	path, then this information can be used on other routers as
	well to associate RSVP session information to an MPLS path
	of an LSP (tunnel)."
    ::= { jnxRsvpSessionEntry 8 }

jnxRsvpSessionRole OBJECT-TYPE
    SYNTAX	INTEGER {
        ingress(1),
	transit(2),
	egress(3)
    }
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION
	"This value signifies the role of an RSVP session with 
	respect to the start and end points of the session. This value 
	MUST be set to ingress(1) at the head-end (source) of this session.
	This value MUST be set to egress(3) at the tail-end (destination)
	of the RSVP session. This value MUST be set to transit(2) on any
	other intermediate nodes that this RSVP session exists on."
    ::= { jnxRsvpSessionEntry 9 }

jnxRsvpSessionDiscontinuityTime OBJECT-TYPE
    SYNTAX	TimeStamp
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION	
	"The value of sysUpTime on the most recent occasion at which any 
	one or more of this RSVP Session's counters suffered a 
	discontinuity.  The relevant counters are jnxRsvpSessionMplsOctets and 
	jnxRsvpSessionMplsPackets. If no such discontinuities have occurred
	since the last re-initialization of the local management subsystem,
	then then this object contains a zero value." 
    ::= { jnxRsvpSessionEntry 10 }

jnxRsvpSessionMplsOctets OBJECT-TYPE
    SYNTAX	Counter64
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION	
	"The number of MPLS octets that have been forwarded over this
	RSVP Session. The number reported is not realtime, may subject to
	several minutes delay. The delay is controllable by mpls statistics
	gathering interval, which by default is once every 5 minutes.
	If mpls statistics gathering is not enabled, this number will not
	increment. Discontinuities in the value of this counter can
	occur at re-initialization of the management system, and at other
	times as indicated by the value of jnxRsvpSessionDiscontinuityTime."
    ::= { jnxRsvpSessionEntry 11 }

jnxRsvpSessionMplsPackets OBJECT-TYPE
    SYNTAX	Counter64
    MAX-ACCESS	read-only
    STATUS	current
    DESCRIPTION	
	"The number of MPLS packets that have been forwarded over this
	RSVP Session. The number reported is not realtime, may subject to
	several minutes delay. The delay is controllable by mpls statistics
	gathering interval, which by default is once every 5 minutes.
	If mpls statistics gathering is not enabled, this number will not
	increment. Discontinuities in the value of this counter can
	occur at re-initialization of the management system, and at other
	times as indicated by the value of jnxRsvpSessionDiscontinuityTime."
    ::= { jnxRsvpSessionEntry 12 }

END
