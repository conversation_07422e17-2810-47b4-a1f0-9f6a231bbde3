--
-- Juniper Enterprise Specific MIB: Structure of Management Information
-- 
-- Copyright (c) 2010-2011, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--


JUNIPER-LSYSSP-POLICY-MIB DEFINITIONS ::= BEGIN
   IMPORTS
      MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, 
      Unsigned32
        FROM SNMPv2-SMI
      DisplayString
        FROM SNMPv2-TC
      jnxLsysSpPolicy                  
        FROM JUNIPER-LSYS-SECURITYPROFILE-MIB
    ;
    
    jnxLsysSpPolicyMIB MODULE-IDENTITY
        LAST-UPDATED  "201005191644Z" -- July 19, 2010
        ORGANIZATION  "Juniper Networks, Inc."
        CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089

             E-mail: <EMAIL>
             HTTP://www.juniper.net"
        DESCRIPTION
            "This module defines the policy-specific MIB for Juniper Enterprise 
             Logical-System (LSYS) security profiles.  Juniper documentation 
             is recommended as the reference. 

             The LSYS security profile provides various static and dynamic 
             resource management by observing resource quota limits. 
             Security policy resource is the focus in this MIB. 
            "
        ::= { jnxLsysSpPolicy 1 }

    jnxLsysSpPolicyObjects        OBJECT IDENTIFIER ::= { jnxLsysSpPolicyMIB 1 }
    jnxLsysSpPolicySummary        OBJECT IDENTIFIER ::= { jnxLsysSpPolicyMIB 2 }
    
 
-- **********************************************************************
-- Tabular policy resource information objects per LSYS:
--   Below are policy resource table indexed by LSYS name.
-- **********************************************************************

-- policy resource table per LSYS

    jnxLsysSpPolicyTable OBJECT-TYPE
        SYNTAX              SEQUENCE OF JnxLsysSpPolicyEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION 
            "LSYSPROFILE policy objects for policy resource consumption per LSYS."  
    ::= { jnxLsysSpPolicyObjects 1 }
    
    jnxLsysSpPolicyEntry OBJECT-TYPE
        SYNTAX              JnxLsysSpPolicyEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION  
            "An entry in policy resource table."
    INDEX { IMPLIED jnxLsysSpPolicyLsysName }          
    ::= { jnxLsysSpPolicyTable 1 }          

    JnxLsysSpPolicyEntry ::= 
       SEQUENCE {
          jnxLsysSpPolicyLsysName    DisplayString,
          jnxLsysSpPolicyProfileName DisplayString,
          jnxLsysSpPolicyUsage       Unsigned32,
          jnxLsysSpPolicyReserved    Unsigned32,
          jnxLsysSpPolicyMaximum     Unsigned32
    }   
 
-- Entry definitions for the policy resource table
 
    jnxLsysSpPolicyLsysName       OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..64))
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION
            "The name of the logical system for which policy resource information is retrieved. "
        ::= { jnxLsysSpPolicyEntry 1 }

    jnxLsysSpPolicyProfileName    OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..32))
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The security profile name string for the LSYS."
    ::= { jnxLsysSpPolicyEntry 2 }

    jnxLsysSpPolicyUsage          OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION  
            "The current resource usage count for the LSYS."
    ::= { jnxLsysSpPolicyEntry 3 }
    
    jnxLsysSpPolicyReserved       OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The reserved resource count for the LSYS."
    ::= { jnxLsysSpPolicyEntry 4 } 

    jnxLsysSpPolicyMaximum        OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The maximum allowed resource usage count for the LSYS."
    ::= { jnxLsysSpPolicyEntry 5 }


-- **********************************************************************
-- policy resource information summary:
-- **********************************************************************

    jnxLsysSpPolicyUsedAmount         OBJECT-TYPE
        SYNTAX                  Unsigned32 
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "The policy resource consumption over all LSYS."
    ::= { jnxLsysSpPolicySummary 1 }          

    jnxLsysSpPolicyMaxQuota           OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The policy resource maximum quota for the whole device for all LSYS."
    ::= { jnxLsysSpPolicySummary 2 }
    
    jnxLsysSpPolicyAvailableAmount    OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The policy resource available in the whole device."
    ::= { jnxLsysSpPolicySummary 3 }
    
    jnxLsysSpPolicyHeaviestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The most amount of policy resource consumed of a LSYS."
    ::= { jnxLsysSpPolicySummary 4 }
    
    jnxLsysSpPolicyHeaviestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the most policy resource."
    ::= { jnxLsysSpPolicySummary 5 }
    
    jnxLsysSpPolicyLightestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The least amount of policy resource consumed of a LSYS."
    ::= { jnxLsysSpPolicySummary 6 }
    
    jnxLsysSpPolicyLightestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the least policy resource."
    ::= { jnxLsysSpPolicySummary 7 }
    


 -- ***************************************************************
 -- definition of policy resource related traps. (TBD)
 -- ***************************************************************

--
-- End of File 
--

END
