--
-- Juniper Enterprise Specific MIB: SOAM
-- 
-- Copyright (c) 2012-2013, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-SOAM-PM-MIB DEFINITIONS ::= BEGIN
IMPORTS
    MODULE-IDENTITY,
    OBJECT-TYPE,
    NOTIFICATION-TYP<PERSON>,
    Integer32, <PERSON><PERSON>, <PERSON><PERSON>,
    Unsigned32,Gauge32               FROM SNMPv2-SMI    -- [RFC2578]
    TEXTUAL-CONVENTION,
    TimeInterval,
    TimeStamp, RowStatus,
    TruthValue, Mac<PERSON>ddress,
    TD<PERSON>in, <PERSON><PERSON><PERSON><PERSON>, DateAndTime   FROM SNMPv2-TC     -- [RFC2579]
    MODULE-COMPLIANCE,
    OBJECT-<PERSON><PERSON><PERSON>,
    NOTIFICATION-GROUP       FROM SNMPv2-CONF   -- [RFC2580]
    InterfaceIndex,
    InterfaceIndexOrZero     FROM IF-MIB        -- [RFC2863]
    LldpChassisId,
    LldpChassisIdSubtype,
    LldpPortId,
    LldpPortIdSubtype        FROM LLDP-MIB      -- [IEEExxx]
    ieee802dot1mibs, 
    IEEE8021VlanIndex        FROM IEEE8021-TC-MIB     
    dot1agCfmMdIndex,   
    dot1agCfmMaIndex,  
    dot1agCfmMepIdentifier,  
    dot1agCfmMepEntry,  
    Dot1afCfmIndexIntegerNextFree,  
    Dot1agCfmMepIdOrZero     FROM IEEE8021-CFM-MIB
    jnxMibs                  FROM JUNIPER-SMI
    IEEE8021PriorityValue FROM IEEE8021-TC-MIB
    ;

jnxSoamPmMib MODULE-IDENTITY
    LAST-UPDATED "201605310000Z"     -- Tue May  31 00:00:00 2016 UTC 
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "Juniper Technical Assistance Center
         Juniper Networks, Inc.
         1133 Innovation Way
         Sunnyvale, CA 94089
         E-mail: <EMAIL>"
    DESCRIPTION
        "This MIB module contains the management objects for the
        management of Ethernet Services Operations, Administration
        and Maintenance for Performance Monitoring.
        "
        REVISION "201201131200Z" -- January 13, 2012
        DESCRIPTION
         "Initial Version."

        REVISION    "201605310000Z" -- 31-May-16
        DESCRIPTION
            "Removed duplicate entries"

        ::= { jnxMibs 78 }

-- *****************************************************************************
-- Object definitions in the SOAM PM MIB Module
-- *****************************************************************************
jnxSoamPmNotifications  OBJECT IDENTIFIER ::= { jnxSoamPmMib 0 }
jnxSoamPmMibObjects     OBJECT IDENTIFIER ::= { jnxSoamPmMib 1 }
jnxSoamPmMibConformance OBJECT IDENTIFIER ::= { jnxSoamPmMib 2 }

-- *****************************************************************************
-- Groups in the SOAM PM MIB Module
-- *****************************************************************************
jnxSoamPmMep             OBJECT IDENTIFIER ::= { jnxSoamPmMibObjects 1 }
jnxSoamPmLmObjects       OBJECT IDENTIFIER ::= { jnxSoamPmMibObjects 2 }
jnxSoamPmDmObjects       OBJECT IDENTIFIER ::= { jnxSoamPmMibObjects 3 }
jnxSoamPmNotificationCfg OBJECT IDENTIFIER ::= { jnxSoamPmMibObjects 4 }
jnxSoamPmNotificationObj OBJECT IDENTIFIER ::= { jnxSoamPmMibObjects 5 }

-- ******************************************************************
-- Textual conventions
-- ******************************************************************


JnxSoamTcTestPatternType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This enumeration data type indicates the type of test pattern to be
         sent in an OAM PDU Test TLV.
         The valid enumerated values associated with this type are:
         null(1) Null signal without CRC-32
         nullCrc32(2) Null signal with CRC-32
         prbs(3) PRBS 2^31-1 without CRC-32
         prbsCrc32(4) PRBS 2^31-1 with CRC-32
        "
    REFERENCE
        "[MEF7.1], Appendix III.2 Enumeration, [Y.1731] 7.7"
    SYNTAX INTEGER {
                null(1),
                nullCrc32(2),
                prbs(3),
                prbsCrc32(4)
    }

JnxSoamTcDataPatternType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This enumeration data type indicates the type of data pattern to be
        sent in an OAM PDU Data TLV.
        The valid enumerated values associated with this type are:
        zeroPattern(1) indicates the Data TLV contains all zeros
        onesPattern(2) indicates the Data TLV contains all ones
        "
    SYNTAX INTEGER {
                zeroPattern(1),
                onesPattern(2)
    }

JnxSoamTcOperationTimeType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This enumeration data type indicates the operation type start
        or end time to indicate when an OAM operation is
        initiated or stopped.
        The valid enumerated values associated with this type are:
        none(1) The operation is never started or is stopped immediately
        if used to indicate a start time, or the operation never
        ends if it is used to indicate an end time
        immediate(2) The operation is to begin immediately
        relative(3) The operation is to begin at a relative time from the
        current time or stop a relative time after it has started
        fixed(4) The operation is to begin/stop at the given UTC time/date
        "
    REFERENCE
        "[SOAM-PM] R2, [SOAM-FM] 8.7"
    SYNTAX INTEGER {
                none(1),
                immediate(2),
                relative(3),
                fixed(4)
    }

JnxSoamTcAvailabilityType ::= TEXTUAL-CONVENTION 
    STATUS current 
    DESCRIPTION 
       "This enumeration data type defines the availability of a session,  
        measured by a loss measurement session. The valid enumerated values
        associated with this type are: 
        available(1)       indicates the MEP is available. 
        unavailable(2)     indicates the MEP is unavailable. 
        unknown(3)         indicates the availability is not known, for 
                           instance because insufficient time has passed to 
                           make an availability calculation, the time has been 
                           excluded because of a maintenance interval, or because  
                           availability measurement is not enabled. 
       " 
    SYNTAX INTEGER { 
                available(1), 
                unavailable(2), 
                unknown(3) 
    }

JnxSoamTcDelayMeasurementBinType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This enumeration data type is used to distinguish between
        measurement bins for Frame Delay, Frame Delay Range, and
        Inter-frame Delay variation.
       
        The valid enumerated values associated with this type are:
       
        twoWayFrameDelay(1) indicates a measurement bin for two-way
        Frame Delay.
        forwardFrameDelay(2) indicates a measurement bin for one-way
        Frame Delay in the forward direction.
        backwardFrameDelay(3) indicates a measurement bin for one-way
        Frame Delay in the backward direction.
        twoWayIfdv(4) indicates a measurement bin for two-way
        Inter-frame Delay Variation.
        forwardIfdv(5) indicates a measurement bin for one-way
        Inter-frame Delay Variation in the forward
        direction.
        backwardIfdv(6) indicates a measurement bin for one-way
        Inter-frame Delay Variation in the backward
        direction.
        twoWayFrameDelayRange(7) indicates a measurement bin for two-way
        Frame Delay Range.
        forwardFrameDelayRange(8) indicates a measurement bin for one-way
        Frame Delay Range in the forward direction.
        backwardFrameDelayRange(9) indicates a measurement bin for one-way
        Frame Delay Range in the backward direction.
        "
    SYNTAX INTEGER {
            twoWayFrameDelay(1),
            forwardFrameDelay(2),
            backwardFrameDelay(3),
            twoWayIfdv(4),
            forwardIfdv(5),
            backwardIfdv(6),
            twoWayFrameDelayRange(7),
            forwardFrameDelayRange(8),
            backwardFrameDelayRange(9)
    } 

-- *****************************************************************************
-- Ethernet MEP Performance Monitoring Configuration
-- *****************************************************************************

jnxSoamPmMepTable OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxSoamPmMepEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table is an extension of the dot1agCfmMepTable and rows
         are automatically added or deleted from this table based upon row
         creation and destruction of the dot1agCfmMepTable.

         This table represents the local MEP PM configuration table. The
         primary purpose of this table is provide local parameters for the
         SOAM PM function found in [Y.1731] and [MEF SOAM-PM] and instantiated
         at a MEP.
         "
    REFERENCE
         "[Y.1731], [MEF SOAM-PM]"
    ::= { jnxSoamPmMep 1 }

jnxSoamPmMepEntry OBJECT-TYPE
    SYNTAX JnxSoamPmMepEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The conceptual row of jnxSoamPmMepTable."
    AUGMENTS {
                  dot1agCfmMepEntry
    }
    --INDEX { dot1agCfmMdIndex,
    --        dot1agCfmMaIndex,
    --        dot1agCfmMepIdentifier
    --      }
    ::= { jnxSoamPmMepTable 1 }

JnxSoamPmMepEntry ::= SEQUENCE {
    jnxSoamPmMepOperNextIndex Dot1afCfmIndexIntegerNextFree,
    jnxSoamPmMepLmSingleEndedResponder TruthValue,
    jnxSoamPmMepSlmSingleEndedResponder TruthValue,
    jnxSoamPmMepDmSingleEndedResponder TruthValue
}

jnxSoamPmMepOperNextIndex OBJECT-TYPE
    SYNTAX Dot1afCfmIndexIntegerNextFree
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains an unused value for a PM session number on a
         MEP that can be used for either LM or DM sessions, or a zero to
         indicate that none exist. This value needs to be read in order to
         find an available index for row-creation of a PM session on a MEP and
         then used when a row is created. This value is automatically updated
         by the SNMP Agent after the row is created.

         Referential integrity is necessary, i.e., the index needs to be
         persistent upon a reboot or restart of a device. The index
         is never to be reused for other PM sessions on the same MEP while this
         session is active, or until it wraps to zero. The index value keeps
         increasing up to that time. This is to facilitate access control based
         on a fixed index for an EMS, since the index is not reused.

         This object is an extension of the dot1agCfmMepTable and the object is
         automatically added or deleted based upon row creation and destruction
         of the dot1agCfmMepTable.
         "
     ::= { jnxSoamPmMepEntry 1 }

jnxSoamPmMepLmSingleEndedResponder OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies whether the Loss Measurement (LMM) single-ended
          Responder is enabled.

          The value 'true' indicates the single-ended Loss Measurement Responder
          is enabled and if a LMM message is received a LMR will be sent in reply.

          The value 'false' indicates the single-ended Loss Measurement Responder
          is disabled. If a LMM message is received no response will be sent and
          the message will be discarded.

          This object needs to be persistent upon reboot or restart of a device.

          A MEP can be both a single-ended Responder and Controller simultaneously.
          "
     DEFVAL { true }
     ::= { jnxSoamPmMepEntry 2 }

jnxSoamPmMepSlmSingleEndedResponder OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies whether the Synthetic Loss Measurement (SLM)
          single-ended Responder is enabled.
          The value 'true' indicates the single-ended SLM Responder is enabled and
          if a SLM message is received a SLR will be sent in reply.
          The value 'false' indicates the single-ended SLM Responder is disabled.
          If a SLM message is received no response will be sent and the message
          will be discarded.
          This object needs to be persistent upon reboot or restart of a device.
          A MEP can be both a single-ended Responder and Controller simultaneously.
          "
     DEFVAL { true }
     ::= { jnxSoamPmMepEntry 3 }

jnxSoamPmMepDmSingleEndedResponder OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies whether the Delay Measurement (DMM) single
          ended Responder is enabled.
          The value 'true' indicates the single-ended Delay Measurement Responder
          is enabled and if a DMM message is received a DMR will be sent in reply.
          The value 'false' indicates the single-ended Delay Measurement Responder
          is disabled. If a DMM message is received no response will be sent and
          the message will be discarded.
          This object needs to be persistent upon reboot or restart of a device.
          A MEP can be both a single-ended Responder and Controller simultaneously.
          "
     DEFVAL { true }
     ::= { jnxSoamPmMepEntry 4 }

-- *****************************************************************************
-- Ethernet Loss Measurement Configuration Table
-- *****************************************************************************

jnxSoamLmCfgTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamLmCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table includes configuration objects and operations for the
          Frame Loss Measurement function defined in [Y.1731] and [MEF SOAM-PM].

          Each row in the table represents a Loss Measurement session for
          the defined MEP. This table uses four indices. The first three indices
          are the indices of the Maintenance Domain, MaNet, and MEP tables. The
          fourth index is the specific LM session on the selected MEP. A
          Loss Measurement session is created on an existing MEP by first
          accessing the jnxSoamPmMepOperNextIndex object and using this value as
          the jnxSoamLmCfgIndex in the row creation.

          Some writable objects in this table are only applicable in certain cases
          (as described under each object), and attempts to write values for them
          in other cases will be ignored.

          The writable objects in this table need to be persistent upon reboot
          or restart of a device.
          "
     REFERENCE
         "[MEF SOAM-PM] R68; [Y.1731]"
     ::= { jnxSoamPmLmObjects 1 }

jnxSoamLmCfgEntry OBJECT-TYPE
     SYNTAX JnxSoamLmCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamLmCfgTable."
     INDEX { dot1agCfmMdIndex,
             dot1agCfmMaIndex,
             dot1agCfmMepIdentifier,
             jnxSoamLmCfgIndex
     }
     ::= { jnxSoamLmCfgTable 1 }

JnxSoamLmCfgEntry ::= SEQUENCE {
     jnxSoamLmCfgIndex Unsigned32,
     jnxSoamLmCfgType INTEGER,
     jnxSoamLmCfgVersion Unsigned32,
     jnxSoamLmCfgEnabled TruthValue,
     jnxSoamLmCfgMeasurementEnable BITS,
     jnxSoamLmCfgMessagePeriod Integer32,
     jnxSoamLmCfgPriority IEEE8021PriorityValue,
     jnxSoamLmCfgFrameSize Unsigned32,
     jnxSoamLmCfgDataPattern JnxSoamTcDataPatternType,
     jnxSoamLmCfgTestTlvIncluded TruthValue,
     jnxSoamLmCfgTestTlvPattern JnxSoamTcTestPatternType,
     jnxSoamLmCfgNumIntervalsStored Unsigned32,
     jnxSoamLmCfgDestMepId Dot1agCfmMepIdOrZero,
     jnxSoamLmCfgDestIsMepId TruthValue,
     jnxSoamLmCfgStartTimeType JnxSoamTcOperationTimeType,
     jnxSoamLmCfgFixedStartDateAndTime DateAndTime,
     jnxSoamLmCfgRelativeStartTime TimeInterval,
     jnxSoamLmCfgRepetitionTime Unsigned32,
     jnxSoamLmCfgAlignMeasurementIntervals TruthValue,
     jnxSoamLmCfgAlignMeasurementOffset Unsigned32,
     jnxSoamLmCfgSessionType  OCTET STRING,
     jnxSoamLmCfgSessionStatus OCTET STRING,
     jnxSoamLmCfgHistoryClear TruthValue,
     jnxSoamLmCfgRowStatus RowStatus,
     jnxSoamLmCfgMeasurementInterval Unsigned32,
     jnxSoamLmCfgDestMacAddress MacAddress,
     jnxSoamLmCfgStopTimeType JnxSoamTcOperationTimeType,
     jnxSoamLmCfgFixedStopDateAndTime DateAndTime,
     jnxSoamLmCfgRelativeStopTime TimeInterval,
     jnxSoamLmCfgAvailabilityMeasurementInterval Unsigned32,
     jnxSoamLmCfgAvailabilityNumConsecutiveMeasPdus Unsigned32,
     jnxSoamLmCfgAvailabilityFlrThreshold Unsigned32,
     jnxSoamLmCfgAvailabilityNumConsecutiveIntervals Unsigned32,
     jnxSoamLmCfgAvailabilityNumConsecutiveHighFlr Unsigned32
}

jnxSoamLmCfgIndex
     OBJECT-TYPE
     SYNTAX Unsigned32(1..4294967295)
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "An index to the Loss Measurement Configuration table which indicates
          the specific measurement session for the MEP.

          jnxSoamPmMepOperNextIndex needs to be inspected to find an
          available index for row-creation.

          Referential integrity is necessary, i.e., the index needs to be
          persistent upon a reboot or restart of a device. The index
          is never reused for other PM sessions on the same MEP while this
          session is active. The index value keeps increasing until it
          wraps to 0. This is to facilitate access control based
          on a fixed index for an EMS, since the index is not reused.
          "
     ::= { jnxSoamLmCfgEntry 1 }

jnxSoamLmCfgType OBJECT-TYPE
     SYNTAX INTEGER {
                 lmLmm (1),
                 lmSlm (2),
                 lmCcm (3)
     }
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies what type of Loss Measurement
          will be performed.

          lmLmm(1) LMM SOAM PDU generated and received LMR responses tracked
          lmSlm(2) SLM SOAM PDU generated and received SLR responses tracked
          lmCcm(3) CCM SOAM PDU generated and received CCM PDUs tracked

          The lmSlm value is required. The lmLmm and lmCcm values are optional.

          The lmCcm loss measurement values are only valid for a point-to-point
          MEG. Multipoint MEGs may give unreliable loss measurements.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[Y.1731] [MEF SOAM-PM] R51, R68, O7, R102"
     DEFVAL { lmSlm }
     ::= { jnxSoamLmCfgEntry 2 }

jnxSoamLmCfgVersion OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object indicates the version of the PDUs used to perform
          Loss Measurement.

          The value is placed in the Version field of the PDU and indicates
          that the PDU format used is the format defined in Y.1731 with
          that version.

          The exact PDUs to use are specified by this object in combination with
          jnxSoamLmCfgType.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[Y.1731]"
     DEFVAL { 0 }
     ::= { jnxSoamLmCfgEntry 3 }

jnxSoamLmCfgEnabled OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies whether the Loss Measurement session
          is enabled.

          The value 'true' indicates the Loss Measurement session is enabled and
          SOAM PDUs are sent and/or measurements are collected when the session
          is running according to the scheduling objects (start time, stop time,
          etc.).

          The value 'false' indicates the Loss Measurement session is disabled
          and SOAM PDUs are not sent and/or measurements collected.

          For a Loss Measurement session to be removed the row is
          deleted in order to release internal resources.

          This object can written/modified after row creation time.

          If the LM session is enabled it resumes after shutdown/restart.

          If the LM session is disabled the current Measurement Interval is
          stopped, if it in process at the time, and all the in process calculations
          for the partially completed Measurement Interval are finalized.

          This object does not affect whether the single-ended Responder is
          enabled or not, which is enabled or disabled by the
          jnxSoamPmMepLmSingleEndedResponder and
          jnxSoamPmMepSlmSingleEndedResponder objects.
          "
     REFERENCE
         "[MEF SOAM-PM] R4, R5, R6, O1, R12, R14"
     DEFVAL { true }
     ::= { jnxSoamLmCfgEntry 4 }

jnxSoamLmCfgMeasurementEnable OBJECT-TYPE
     SYNTAX BITS {
                 bForwardTransmitedFrames(0),
                 bForwardReceivedFrames(1),
                 bForwardMinFlr(2),
                 bForwardMaxFlr(3),
                 bForwardAvgFlr(4),
                 bBackwardTransmitedFrames(5),
                 bBackwardReceivedFrames(6),
                 bBackwardMinFlr(7),
                 bBackwardMaxFlr(8),
                 bBackwardAvgFlr(9),
                 bSoamPdusSent(10),
                 bSoamPdusReceived (11),
                 bAvailForwardHighLoss(12),
                 bAvailForwardConsecutiveHighLoss(13),
                 bAvailForwardAvailable(14),
                 bAvailForwardUnavailable(15),
                 bAvailForwardMinFlr(16),
                 bAvailForwardMaxFlr(17),
                 bAvailForwardAvgFlr(18),
                 bAvailBackwardHighLoss(19),
                 bAvailBackwardConsecutiveHighLoss(20),
                 bAvailBackwardAvailable(21),
                 bAvailBackwardUnavailable(22),
                 bAvailBackwardMinFlr(23),
                 bAvailBackwardMaxFlr(24),
                 bAvailBackwardAvgFlr(25),
                 bMeasuredStatsForwardMeasuredFlr(26),
                 bMeasuredStatsBackwardMeasuredFlr(27),
                 bMeasuredStatsAvailForwardStatus(28),
                 bMeasuredStatsAvailBackwardStatus(29)
     }
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "A vector of bits that indicates the type of SOAM LM counters found
          in the jnxSoamLmMeasuredStatsTable, jnxSoamLmCurrentStatsTable,
          jnxSoamLmHistoryStatsTable that are enabled. 
          that are enabled.

          A bit set to '1' enables the specific SOAM LM counter. A bit set to
          '0' disables the SOAM LM counter.

          If a particular SOAM LM counter is not supported the BIT value is
          set to '0'.

          Not all SOAM LM counters are supported for all SOAM LM types.

          This object can only be written at row creation time and cannot be
          modified once it has been created.

          bForwardTransmitedFrames (0)
          Enables/disables the jnxSoamLmCurrentStatsForwardTransmittedFrames
          and jnxSoamLmHistoryStatsForwardTransmittedFrames counters.
          bForwardReceivedFrames(1)
          Enables/disables the jnxSoamLmCurrentStatsForwardReceivedFrames
          and jnxSoamLmHistoryStatsForwardReceivedFrames counters.
          bForwardMinFlr(2)
          Enables/disables the jnxSoamLmCurrentStatsForwardMinFlr
          and jnxSoamLmHistoryStatsForwardMinFlr counters.
          bForwardMaxFlr(3)
          Enables/disables the jnxSoamLmCurrentStatsForwardMaxFlr
          and jnxSoamLmHistoryStatsForwardMaxFlr counters.
          bForwardAvgFlr(4)
          Enables/disables the jnxSoamLmCurrentStatsForwardAvgFlr
          and jnxSoamLmHistoryStatsForwardAvgFlr counters.
          bBackwardTransmitedFrames(5)
          Enables/disables the jnxSoamLmCurrentStatsBackwardTransmittedFrames
          and jnxSoamLmHistoryStatsBackwardTransmittedFrames counters.
          bBackwardReceivedFrames(6)
          Enables/disables the jnxSoamLmCurrentStatsBackwardReceivedFrames
          and jnxSoamLmHistoryStatsBackwardReceivedFrames counters.
          bBackwardMinFlr(7)
          Enables/disables the jnxSoamLmCurrentStatsBackwardMinFlr
          and jnxSoamLmHistoryStatsBackwardMinFlr counters.
          bBackwardMaxFlr(8)
          Enables/disables the jnxSoamLmCurrentStatsBackwardMaxFlr
          and jnxSoamLmHistoryStatsBackwardMaxFlr counters.
          bBackwardAvgFlr(9)
          Enables/disables the jnxSoamLmCurrentStatsBackwardAvgFlr
          and jnxSoamLmHistoryStatsBackwardAvgFlr counters.
          bSoamPdusSent (10)
          Enables/disables the jnxSoamLmCurrentStatsSoamPdusSent
          and jnxSoamLmHistoryStatsSoamPdusSent counters.
          bSoamPdusReceivedbReceivedMeasurements (11)
          Enables/disables the jnxSoamLmCurrentStatsSoamPdusReceived
          and jnxSoamLmHistoryStatsSoamPdusReceived counters.
          bAvailForwardHighLoss(12)
          Enables/disables the jnxSoamLmCurrentAvailStatsForwardHighLoss
          and jnxSoamLmHistoryAvailStatsForwardHighLoss counters.
          bAvailForwardConsecutiveHighLoss(13)
          Enables/disables the jnxSoamLmCurrentAvailStatsForwardConsecutiveHighLoss
          and jnxSoamLmHistoryAvailStatsForwardConsecutiveHighLoss counters.
          bAvailForwardAvailable(14)
          Enables/disables the jnxSoamLmCurrentAvailStatsForwardAvailable
          and jnxSoamLmHistoryAvailStatsForwardAvailable counters.
          bAvailForwardUnavailable(15)
          Enables/disables the jnxSoamLmCurrentAvailStatsForwardUnavailable
          and jnxSoamLmHistoryAvailStatsForwardUnavailable counters.
          bAvailForwardMinFlr(16)
          Enables/disables the jnxSoamLmCurrentAvailStatsForwardMinFlr
          and jnxSoamLmHistoryAvailStatsForwardMinFlr counters.
          bAvailForwardMaxFlr(17)
          Enables/disables the jnxSoamLmCurrentAvailStatsForwardMaxFlr
          and jnxSoamLmHistoryAvailStatsForwardMaxFlr counters.
          bAvailForwardAvgFlr(18)
          Enables/disables the jnxSoamLmCurrentAvailStatsForwardAvgFlr
          and jnxSoamLmHistoryAvailStatsForwardAvgFlr counters.
          bAvailBackwardHighLoss(19)
          Enables/disables the jnxSoamLmCurrentAvailStatsBackwardHighLoss
          and jnxSoamLmHistoryAvailStatsBackwardHighLoss counters.
          bAvailBackwardConsecutiveHighLoss(20)
          Enables/disables the jnxSoamLmCurrentAvailStatsBackwardConsecutiveHighLoss
          and jnxSoamLmHistoryAvailStatsBackwardConsecutiveHighLoss counters.
          bAvailBackwardAvailable(21)
          Enables/disables the jnxSoamLmCurrentAvailStatsBackwardAvailable
          and jnxSoamLmHistoryAvailStatsBackwardAvailable counters.
          bAvailBackwardUnavailable(22)
          Enables/disables the jnxSoamLmCurrentAvailStatsBackwardUnavailable
          and jnxSoamLmHistoryAvailStatsBackwardUnavailable counters.
          bAvailBackwardMinFlr(23)
          Enables/disables the jnxSoamLmCurrentAvailStatsBackwardMinFlr
          and jnxSoamLmHistoryAvailStatsBackwardMinFlr counters.
          bAvailBackwardMaxFlr(24)
          Enables/disables the jnxSoamLmCurrentAvailStatsBackwardMaxFlr
          and jnxSoamLmHistoryAvailStatsBackwardMaxFlr counters.
          bAvailBackwardAvgFlr(25)
          Enables/disables the jnxSoamLmCurrentAvailStatsBackwardAvgFlr
          and jnxSoamLmHistoryAvailStatsBackwardAvgFlr counters.
          Enables/disables the jnxSoamLmCurrentStatsSoamPdusReceived
          and jnxSoamLmHistoryStatsSoamPdusReceived counters.
          bMeasuredStatsForwardMeasuredFlr(26)
          Enables/disables the jnxSoamLmMeasuredStatsForwardFlr counter.
          bMeasuredStatsBackwardMeasuredFlr(27)
          Enables/disables the jnxSoamLmMeasuredStatsBackwardFlr counter.
          bMeasuredStatsAvailForwardStatus(28)
          Enables/disables the jnxSoamLmMeasuredStatsAvailForwardStatus counter.
          bMeasuredStatsAvailBackwardStatus(29)
          Enables/disables the jnxSoamLmMeasuredStatsAvailBackwardStatus counter.
          "
     REFERENCE
         "[Y.1731]"
     DEFVAL { { } }
     ::= { jnxSoamLmCfgEntry 5 }

jnxSoamLmCfgMessagePeriod OBJECT-TYPE
     SYNTAX Integer32
     UNITS "ms"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the interval between Loss Measurement
          OAM message transmission. For Loss Measurement monitoring
          applications the default value is 1 sec.

          This object is not applicable if jnxSoamLmCfgType is set to lmCcm
          and is ignored for that Loss Measurement Type.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R76, R77, D29, D30"
     DEFVAL { 1000 }
     ::= { jnxSoamLmCfgEntry 6 }

jnxSoamLmCfgPriority OBJECT-TYPE
     SYNTAX IEEE8021PriorityValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the Loss Measurement OAM message priority
          as well as the priority of the service/OAM traffic to be monitored.
          Only frames of the same Class of Service are counted.

          The default value is to be the value which yields the lowest frame
          loss.

          This object is not applicable if jnxSoamLmCfgType is set to lmCcm.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R1, R2, R71, D28, R72, R73, R105-R109, D45;
         [MEF 10.2.1] Section 6.8"
     ::= { jnxSoamLmCfgEntry 7 }

jnxSoamLmCfgFrameSize OBJECT-TYPE
     SYNTAX Unsigned32 (64..9600)
     UNITS "bytes"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the Loss Measurement frame size between
          64 bytes and the maximum transmission unit of the EVC.

          The range of frame sizes from 64 through 2000 octets need to be
          supported, and the range of frame sizes from 2001 through 9600 octets
          is suggested be supported.

          The adjustment to the frame size of the standard frame size is
          accomplished by the addition of a Data or Test TLV. A Data or Test TLV
          is only added to the frame if the frame size is greater than 64 bytes.

          This object is only valid for the entity transmitting the Loss
          Measurement frames, type 'lmSlm', and is ignored by the
          entity receiving frames. It is not applicable for the 'lmCcm' or
          'lmLmm' types.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R78, R79, D31, D32 [Y.1731]"
     DEFVAL { 64 }
     ::= { jnxSoamLmCfgEntry 8 }

jnxSoamLmCfgDataPattern OBJECT-TYPE
     SYNTAX JnxSoamTcDataPatternType
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the LM data pattern included in a Data TLV
          when the size of the LM frame is determined by the
          jnxSoamLmFrameSize object and jnxoamLmTestTlvIncluded is 'false'.

          If the frame size object does not define the LM frame size or
          jnxSoamLmTestTlvIncluded is 'true' the value of this object is
          ignored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     DEFVAL { zeroPattern }
     ::= { jnxSoamLmCfgEntry 9 }

jnxSoamLmCfgTestTlvIncluded OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "Indicates whether a Test TLV or Data TLV is included when the size
          of the LM frame is determined by the jnxSoamLmFrameSize object.

          A value of 'true' indicates that the Test TLV is to be included. A
          value of 'false' indicates that the Data TLV is to be included.

          If the frame size object does not define the LM frame size
          the value of this object is ignored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[Y.1731] 9.3"
     DEFVAL { false }
     ::= { jnxSoamLmCfgEntry 10 }

jnxSoamLmCfgTestTlvPattern OBJECT-TYPE
     SYNTAX JnxSoamTcTestPatternType
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the type of test pattern to be
          sent in the LM frame Test TLV when the size of LM PDU is
          determined by the jnxSoamLmFrameSize object and
          jnxSoamLmTestTlvIncluded is 'true'. If the frame size object
          does not define the LM frame size or jnxSoamLmTestTlvIncluded
          is 'false' the value of this object is ignored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     DEFVAL { null }
     ::= { jnxSoamLmCfgEntry 11 }

jnxSoamLmCfgNumIntervalsStored OBJECT-TYPE
     SYNTAX Unsigned32 (1..1000)
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the number of completed Measurement Intervals
          to store in the history statistic table (jnxSoamLmHistoryStatsTable)

          At least 32 completed Measurement Intervals need to be stored. 96
          Measurement Intervals are recommended to be stored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R21, D8, D9"
     DEFVAL { 32 }
     ::= { jnxSoamLmCfgEntry 12 }

jnxSoamLmCfgDestMepId OBJECT-TYPE
     SYNTAX Dot1agCfmMepIdOrZero
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The Maintenance Association End Point Identifier of
          another MEP in the same Maintenance Association to which
          the SOAM LM frame is to be sent.

          This address will be used if the value of the column
          jnxSoamLmDestIsMepId is 'true'. A value of zero
          means that the destination MEP ID has not been configured.

          This object is only valid for the entity transmitting the Loss
          Measurement frames, types 'lmLmm' and 'lmSlm'. It is not applicable for
          the 'lmCcm' type.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R70, R104"
     DEFVAL { 0 }
     ::= { jnxSoamLmCfgEntry 13 }

jnxSoamLmCfgDestIsMepId OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "A value of 'true' indicates that MEPID of the target MEP is used for
          SOAM LM frame transmission.

          A value of 'false' indicates that the MAC address of the
          target MEP is used for SOAM LM frame transmission.

          This object is only valid for the entity transmitting the Loss
          Measurement frames, types 'lmLmm' and 'lmSlm'. It is not applicable for
          the 'lmCcm' type.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R70, R104"
     DEFVAL { true }
     ::= { jnxSoamLmCfgEntry 14 }

jnxSoamLmCfgStartTimeType OBJECT-TYPE
     SYNTAX JnxSoamTcOperationTimeType
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the type of start time of the SOAM LM
          session. The start time can be disabled (none), immediate, relative,
          or fixed.

          The value of 'none' is illegal and a write error will be returned
          if this value is used.

          The value of 'immediate' starts the SOAM LM session when the
          jnxSoamLmCfgEnabled is true.

          The value of 'fixed' starts the SOAM LM session when the
          jnxSoamLmFixedStartDateAndTime is less than or equal to the current
          system date and time and jnxSoamLmCfgEnabled is true. This value is used
          to implement an On-Demand fixed time PM session.

          The value of 'relative' starts the SOAM LM session when the current
          system date and time minus the jnxSoamLmRelativeStartTime is greater
          than or equal to the system date and time when the jnxSoamLmStartTimeType
          object was written and jnxSoamLmCfgEnabled is true. This value is used
          to implement an On-Demand relative time PM session.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R3, R7, R8, D1"
     DEFVAL { immediate }
     ::= { jnxSoamLmCfgEntry 15 }

jnxSoamLmCfgFixedStartDateAndTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the fixed start date/time for the
          SOAM Loss Measurement session. This object is used only used if
          jnxSoamLmStartTimeType is 'fixed' and is ignored otherwise.

          The default value is year 0000, month 01, day 01, time 00:00:00.00.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R9"
     DEFVAL { '0000010100000000'H }
     ::= { jnxSoamLmCfgEntry 16 }

jnxSoamLmCfgRelativeStartTime OBJECT-TYPE
     SYNTAX TimeInterval
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the relative start time, from the
          current system time, for the SOAM LM session. This
          object is used only if jnxSoamLmStartTimeType is 'relative' and is
          ignored otherwise.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R9"
     DEFVAL { 0 }
     ::= { jnxSoamLmCfgEntry 17 }

jnxSoamLmCfgRepetitionTime OBJECT-TYPE
     SYNTAX Unsigned32 (0..31536000)
     UNITS "seconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies a configurable repetition time between
          Measurement Intervals in a Loss Measurement session, in seconds.

          If the value is 0 (none), there is no time gap between the end of one
          Measurement Interval and the start of a new Measurement Interval.
          This is the normal usage case.

          If the value is greater than 0 but less than or equal to the measurement
          interval, an error is returned.

          If the value is greater than one Measurement Interval there is time gap
          between the end of one Measurement Interval and the start of the next
          Measurement Interval. The repetition time specifies the time between
          the start of consecutive Measurement Intervals; hence the gap between
          the end of one Measurement Interval and the start of the next is equal
          to the difference between the repetition time and the measurement
          interval. During this gap, no SOAM PDUs are sent for this session and
          no measurements are made.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R18, D3, R19, R20"
     DEFVAL { 0 }
     ::= { jnxSoamLmCfgEntry 18 }

jnxSoamLmCfgAlignMeasurementIntervals OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies whether the Measurement Intervals for
          the Loss Measurement session are aligned with a zero offset to
          real time.

          The value 'true' indicates that each Measurement Interval starts
          at a time which is aligned to NE time source hour, if the repetition
          time (or the Measurement Interval, if the repetition time is 0) is
          a factor of an hour, i.e. 60min/15min = 4. For instance, a
          Measurement Interval/repetition time of 15 minutes would stop/start
          the Measurement Interval at 0, 15, 30, and 45 minutes of an hour. A
          Measurement Interval/Repetition Time of 7 minutes would not align
          to the hour since 7 minutes is NOT a factor of an hour, i.e.
          60min/7min = 8.6. In this case the behavior is the same as if the
          object is set to 'false'.

          The value 'false' indicates that the first Measurement Interval starts
          at an arbitrary time and each subsequent Measurement Interval starts
          at a time which is determined by jnxSoamLmCfgRepetitionTime.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] D4, D5, D6"
     DEFVAL { true }
     ::= { jnxSoamLmCfgEntry 19 }

jnxSoamLmCfgAlignMeasurementOffset OBJECT-TYPE
     SYNTAX Unsigned32 (0..525600)
     UNITS "minutes"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the offset in minutes from the time of day value
          if jnxSoamLmCfgAlignMeasurementIntervals is 'true' and the repetition
          time is a factor of 60 minutes. If not, the value of this object
          is ignored.

          If the Measurement Interval is 15 minutes and
          jnxSoamLmCfgAlignMeasurementIntervals is true and if this object was
          set to 5 minutes, the Measurement Intervals would start at 5, 20, 35, 50
          minutes past each hour.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] D7"
     DEFVAL { 0 }
     ::= { jnxSoamLmCfgEntry 20 }

jnxSoamLmCfgSessionType OBJECT-TYPE
     SYNTAX OCTET STRING (SIZE(1..42))
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object indicates whether the current session is defined to
          be 'Proactive' or 'On-Demand'. A value of 'proactive'
          indicates the current session is 'Proactive'. A value of 'onDemand'
          indicates the current session is 'On-Demand'.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
         "[MEF SOAM-PM] R3"
     DEFVAL { "proactive" }
     ::= { jnxSoamLmCfgEntry 21 }

jnxSoamLmCfgSessionStatus OBJECT-TYPE
     SYNTAX OCTET STRING (SIZE(1..44))
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object indicates the current status of the LM session. A value
          of 'active' indicates the current LM session is active, i.e. the current
          time lies between the start time and the stop time, and
          jnxSoamLmCfgEnabled is true. A value of 'notActive'  indicates the
          current LM session is not active, i.e. it has not started yet, has
          stopped upon reaching the stop time, or is disabled.
          "
     ::= { jnxSoamLmCfgEntry 22 }

jnxSoamLmCfgHistoryClear OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object when written clears the Loss Measurement history
          Table (jnxSoamLmHistoryStatsTable) - all rows are deleted.
          When read the value always returns 'false'.

          Writing this value does not change the current stat table,
          nor any of the items in the configuration table.
          Writing this value during row creation has no effect.
          "
     DEFVAL { false }
     ::= { jnxSoamLmCfgEntry 23 }

jnxSoamLmCfgRowStatus OBJECT-TYPE
     SYNTAX RowStatus
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The status of the row.

          The writable columns in a row cannot be changed if the row
          is active, except for jnxSoamLmCfgHistoryClear and jnxSoamLmCfgEnabled
          objects. All columns must have a valid value before a row
          can be activated.
          "
     ::= { jnxSoamLmCfgEntry 24 }

jnxSoamLmCfgMeasurementInterval OBJECT-TYPE
    SYNTAX Unsigned32 (1..525600)
    UNITS "minutes"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object specifies the Measurement Interval for FLR statistics, 
         in minutes. A Measurement Interval of 15 minutes needs to be supported,
         other intervals may be supported.

         This object can only be written at row creation time and cannot be
         modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R16, R17, R110, R111, D46"
    DEFVAL { 15 }
    ::= { jnxSoamLmCfgEntry 25 }


jnxSoamLmCfgDestMacAddress OBJECT-TYPE
    SYNTAX MacAddress
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
    "The Target or Destination MAC Address Field to be transmitted.

     If jnxSoamLmCfgType is 'lmCcm', the destination MAC address is always a
     multicast address indicating the level of the MEG: 01-80-c2-00-00-3y,
     where y is the level of the MEG. An error is returned if this object
     is set to any other value.

     If jnxSoamLmCfgType is 'lmLmm' or 'lmSlm', the destination address is
     the unicast address of the destination MEP. An error is returned if
     this object is set to a multicast address.

     This address will be used if the value of the object
     mefSoamLmDestIsMepId is 'false'.

     This object is only valid for the entity transmitting the
     SOAM LM frames and is ignored by the entity receiving
     SOAM LM frames.

     This object can only be written at row creation time and cannot be
     modified once it has been created.
    "
    REFERENCE
        "[MEF SOAM-PM] R70, R104"
    ::= { jnxSoamLmCfgEntry 26 }

jnxSoamLmCfgStopTimeType OBJECT-TYPE
     SYNTAX JnxSoamTcOperationTimeType
     MAX-ACCESS read-create
     STATUS current
     DESCRIPTION
     "This object specifies the type of stop time to terminate the
      SOAM LM session. The stop time can be forever (none), relative, or
      fixed.

      The value of 'none' indicates that the SOAM LM session never ends once it
      has started unless it the session is disabled.

      The value of 'immediate' is illegal and a write error will be returned
      if this value is used.

      The value of 'fixed' stops the SOAM LM session when the
      jnxSoamLmFixedStopDateAndTime is less than or equal
      to the current system date and time. This
      value is used to implement an On-Demand fixed time PM session.

      The value of 'relative' stops the SOAM LM session when the time
      indicated by jnxSoamLmRelativeStopTime has passed since the session
      start time as determined by the jnxSoamLmCfgStartTimeType,
      jnxSoamLmCfgFixedStartDateAndTime and jnxSoamLmCfgRelativeStartTime
      objects. This value is used to implement an On-Demand relative time
      PM session.

      This object can only be written at row creation time and cannot be
      modified once it has been created.
     "
    REFERENCE
        "[MEF SOAM-PM] R3, R10, D2"
    DEFVAL { none }
    ::= { jnxSoamLmCfgEntry 27 }

jnxSoamLmCfgFixedStopDateAndTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the fixed stop date/time for the
          SOAM Loss Measurement session. This object is used only used
          if jnxSoamLmStopTimeType is 'fixed' and is ignored otherwise.
 
          The default value is year 0000, month 01, day 01, time 00:00:00.00.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
         "
     REFERENCE
         "[MEF SOAM-PM] R10, R13"
     DEFVAL { '0000010100000000'H }
     ::= { jnxSoamLmCfgEntry 28 }

jnxSoamLmCfgRelativeStopTime OBJECT-TYPE
     SYNTAX TimeInterval
     MAX-ACCESS read-create
     STATUS current
     DESCRIPTION
         "This object specifies the relative stop time, from the
          session start time, to stop the SOAM LM session. This
          object is used only if jnxSoamLmStopTimeType is 'relative' and is
          ignored otherwise.
          object can only be written at row creation time 1922 and cannot be
          modified once it has been created.
         "
     REFERENCE
         "[MEF SOAM-PM] R11"
     DEFVAL { 0 }
     ::= { jnxSoamLmCfgEntry 29 }

jnxSoamLmCfgAvailabilityMeasurementInterval OBJECT-TYPE
    SYNTAX Unsigned32 (1..525600)
    UNITS "minutes"
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the availability Measurement Interval in
         minutes.
         A Measurement Interval of 15 minutes is to be supported, other intervals
         can be supported.
         This object can only be written at row creation time and cannot be
         modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R16, R17"
    DEFVAL { 15 }
    ::= { jnxSoamLmCfgEntry 30 }

jnxSoamLmCfgAvailabilityNumConsecutiveMeasPdus OBJECT-TYPE
    SYNTAX Unsigned32 (1..1000000)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies a configurable number of consecutive
         loss measurement PDUs to be used in evaluating the
         availability/unavailability status of each availability
         indicator per MEF 10.2.1. Loss Measurement PDUs (LMMs, CCMs or
         SLMs) are sent regularly with a period defined by
         jnxSoamLmCfgMessagePeriod. Therefore, this object, when
         multiplied by mefSoamLmCfgMessagePeriod, 2050 is equivalent to
         is equivalent to the Availability parameter of 'delta_t' as 
         specified by MEF 10.2.1.

         If the jnxSoamLmCfgType is lmLMM or lmCCM, this object defines the
         number of LMM or CCM PDUs transmitted during each 'delta_t' period.
         The Availability flr for a given 'delta_t' can be calculated based
         on the counters in the last LMM/R or CCM during this 'delta_t' and
         the last LMM/R or CCM in the previous 'delta_t'.

         If the jnxSoamLmCfgType is lmSLM, this object defines the number
         of SLM PDUs transmitted during each 'delta_t' period. The
         Availability flr for a given 'delta_t' is calculated based on the
         number of those SLM PDUs that are lost.

         If the jnxSoamLmCfgType is lmLMM or lmCCM, the number range of 1
         through 10 must be supported. The number range of 10 through 1000000
         may be supported, but is not mandatory.

         If the jnxSoamLmCfgType is lmSLM, the number range of 10 through
         100 must be supported. The number range of 100 through 1000000
         may be supported, but is not mandatory.

         This object can only be written at row creation time and cannot be
         modified once it has been created.
        "
    REFERENCE
        "[MEF 10.2.1] Section 7.9.8; [MEF SOAM-PM] R80, D33, R81"
    DEFVAL { 10 }
    ::= { jnxSoamLmCfgEntry 31 }

jnxSoamLmCfgAvailabilityFlrThreshold OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies a configurable availability threshold to be
         used in evaluating the availability/unavailability status of an
         availability indicator per MEF 10.2.1. The availability threshold range
         of 0.00 (0) through 1.00 (100000) is supported. This parameter is
         equivalent to the Availability parameter of 'C' as specified by
         MEF 10.2.1.

         Units are in milli-percent, where 1 indicates 0.001 percent.

         This object can only be written at row creation time and cannot be
         modified once it has been created.
        "
    REFERENCE
        "[MEF 10.2.1] Section 7.9.8; [MEF SOAM-PM] R81, R82, D34"
    DEFVAL { 50000 }
    ::= { jnxSoamLmCfgEntry 32 }

jnxSoamLmCfgAvailabilityNumConsecutiveIntervals OBJECT-TYPE
    SYNTAX Unsigned32 (1..1000)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies a configurable number of consecutive
         availability indicators to be used to determine a change in the
         availability status as indicated by MEF 10.2.1. This parameter is
         equivalent to the Availability parameter of 'n' as specified
         by MEF 10.2.1.
         The number range of 1 through 10 must be supported. The number 
         range of 1 through 1000 may be supported, but is not mandatory.

         This object can only be written at row creation time and cannot be
         modified once it has been created.
        "
    REFERENCE
        "[MEF 10.2.1] Section 7.9.8; [MEF SOAM-PM] R80, D33"
    DEFVAL { 10 }
    ::= { jnxSoamLmCfgEntry 33 }

jnxSoamLmCfgAvailabilityNumConsecutiveHighFlr OBJECT-TYPE
    SYNTAX Unsigned32 (1..1000)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies a configurable number of consecutive
         availability indicators to be used for assessing CHLI. This
         parameter is equivalent to the Resilency parameter of 'p' as
         specified by MEF 10.2.1.

         jnxSoamLmCfgAvailabilityNumConsecutiveHighFlr must be strictly less than
         jnxSoamLmCfgAvailabilityNumConsecutiveIntervals. If not, the count of 
         high loss intervals over time, jnxSoamLmAvailabilityHighLoss, and the 
         count  of consecutive high loss levels, 
         jnxSoamLmAvailabilityConsecutiveHighLoss, is disabled.

         The number range of 1 through 10 must be supported. The number range
         of 1 through 1000 may be supported, but is not mandatory.

         This object can only be written at row creation time and cannot be
         modified once it has been created.
        "
    REFERENCE
        "[MEF 10.2.1] Section 7.9.8; [MEF SOAM-PM] R86, D35, D36"
    DEFVAL { 5 }
    ::= { jnxSoamLmCfgEntry 34 }

-- *****************************************************************************
-- Ethernet Loss Measurement Measured Statistic Table
-- *****************************************************************************

jnxSoamLmMeasuredStatsTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamLmMeasuredStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This object contains the last measured results for a SOAM Loss
          Measurement session.

          Each row in the table represents a Loss Measurement session for
          the defined MEP. This table uses four indices. The first three indices
          are the indices of the Maintenance Domain, MaNet, and MEP tables. The
          fourth index is the specific LM session on the selected MEP.

          Instances of this managed object are created automatically
          by the SNMP Agent when the Loss Measurement session is running.

          Each object in this table applies only if the corresponding bit is set in
          jnxSoamLmCfgMeasurementEnable.

          The objects in this table do not need to be persistent upon reboot
          or restart of a device.
          "
     REFERENCE
         "[MEF SOAM-PM] R7, R15, 8D18"
     ::= { jnxSoamPmLmObjects  2 }

jnxSoamLmMeasuredStatsEntry OBJECT-TYPE
     SYNTAX JnxSoamLmMeasuredStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamLmMeasuredStatsTable"
     INDEX {
                 dot1agCfmMdIndex,
                 dot1agCfmMaIndex,
                 dot1agCfmMepIdentifier,
                 jnxSoamLmCfgIndex
     }
     ::= { jnxSoamLmMeasuredStatsTable 1 }

JnxSoamLmMeasuredStatsEntry ::= SEQUENCE {
     jnxSoamLmMeasuredStatsForwardFlr Unsigned32,
     jnxSoamLmMeasuredStatsBackwardFlr Unsigned32,
     jnxSoamLmMeasuredStatsAvailForwardStatus JnxSoamTcAvailabilityType,
     jnxSoamLmMeasuredStatsAvailBackwardStatus JnxSoamTcAvailabilityType,
     jnxSoamLmMeasuredStatsAvailForwardLastTransitionTime DateAndTime,
     jnxSoamLmMeasuredStatsAvailBackwardLastTransitionTime DateAndTime
}

jnxSoamLmMeasuredStatsForwardFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the last frame loss ratio in the forward direction
          calculated by this MEP. The FLR value
          is a ratio that is expressed as a percent with a value of 0 (ratio
          0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmMeasuredStatsEntry 1 }

jnxSoamLmMeasuredStatsBackwardFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the last frame loss ratio in the backward direction
          calculated by this MEP. The FLR value
          is a ratio that is expressed as a percent with a value of 0 (ratio
          0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmMeasuredStatsEntry 2 }

jnxSoamLmMeasuredStatsAvailForwardStatus OBJECT-TYPE
      SYNTAX JnxSoamTcAvailabilityType
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
          "This object indicates the availability status (the outcome of the
          last known availability indicator) in the forward direction.
          Note that the status of an availability indicator is not known until
          the loss for a number of subsequent availability indicators has been
          calculated (as determined by
          jnxSoamLmCfgAvailabilityNumConsecutiveIntervals)
          "
      REFERENCE
          "[MEF SOAM-PM] R83"
      ::= { jnxSoamLmMeasuredStatsEntry 3 }
      
jnxSoamLmMeasuredStatsAvailBackwardStatus OBJECT-TYPE
      SYNTAX JnxSoamTcAvailabilityType
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
          "This object indicates the availability status (the outcome of the
          last availability indicator) in the backward direction.
          Note that the status of an availability indicator is not known until
          the loss for a number of subsequent availability indicators has been
          calculated (as determined by
          jnxSoamLmCfgAvailabilityNumConsecutiveIntervals)
          "
      REFERENCE
          "[MEF SOAM-PM] R83"
      ::= { jnxSoamLmMeasuredStatsEntry 4 }
      
jnxSoamLmMeasuredStatsAvailForwardLastTransitionTime OBJECT-TYPE
      SYNTAX DateAndTime
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
          "This object indicates the time of the last transition
          between available and unavailable in the forward direction.
      
          If there have been no transitions since the Loss Measurement
          Session was started, this is set to 0.
          "
      REFERENCE
          "[MEF SOAM-PM] R83"
      ::= { jnxSoamLmMeasuredStatsEntry 5 }
      
jnxSoamLmMeasuredStatsAvailBackwardLastTransitionTime OBJECT-TYPE
      SYNTAX DateAndTime
      MAX-ACCESS read-only
      STATUS current
      DESCRIPTION
          "This object indicates the time of the last transition
          between available and unavailable in the backward direction.
      
          If there have been no transitions since the Loss Measurement
          Session was started, this is set to 0.
          "
      REFERENCE
          "[MEF SOAM-PM] R83"
      ::= { jnxSoamLmMeasuredStatsEntry 6 }
      
-- *****************************************************************************
-- Ethernet Loss Measurement Current Statistic Table
-- *****************************************************************************
      
jnxSoamLmCurrentStatsTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamLmCurrentStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table contains the results for the current Measurement
          Interval in a SOAM Loss Measurement session gathered during the interval
          indicated by iterator counts.

          A row in this table is created automatically
          by the SNMP Agent when the Loss Measurement session is configured.

          Each row in the table represents the current statistics for a Loss
          Measurement session for the defined MEP. This table uses four indices.
          The first three indices are the indices of the Maintenance Domain, MaNet,
          and MEP tables. The fourth index is the specific LM session on the
          selected MEP. There may be more than one LM session per MEP. The
          main use case for this is to allow multiple CoS instances to be
          operating simultaneously for a MEP.

          The objects in this table apply regardless of the value of
          jnxSoamLmCfgType unless otherwise specified in the object description.

          Except for jnxSoamLmCurrentStatsIndex, jnxSoamLmCurrentStatsStartTime,
          jnxSoamLmCurrentStatsElapsedTime and jnxSoamLmCurrentStatsSuspect,
          each object in this table applies only if the corresponding bit is set in
          jnxSoamLmCfgMeasurementEnable.

          The objects in this table do not need to be persistent upon reboot or
          restart of a device.
          "
     REFERENCE
        "[MEF SOAM-PM] R7, R15, D9, D18"
     ::= { jnxSoamPmLmObjects 3 }

jnxSoamLmCurrentStatsEntry OBJECT-TYPE
     SYNTAX JnxSoamLmCurrentStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
        "The conceptual row of jnxSoamLmCurrentStatsTable"
     INDEX {
		dot1agCfmMdIndex,
		dot1agCfmMaIndex,
		dot1agCfmMepIdentifier,
		jnxSoamLmCfgIndex
     }
     ::= { jnxSoamLmCurrentStatsTable 1 }

JnxSoamLmCurrentStatsEntry ::= SEQUENCE {
     jnxSoamLmCurrentStatsIndex Unsigned32,
     jnxSoamLmCurrentStatsStartTime DateAndTime,
     jnxSoamLmCurrentStatsElapsedTime TimeInterval,
     jnxSoamLmCurrentStatsSuspect TruthValue,
     jnxSoamLmCurrentStatsForwardTransmittedFrames Gauge32,
     jnxSoamLmCurrentStatsForwardReceivedFrames Gauge32,
     jnxSoamLmCurrentStatsForwardMinFlr Unsigned32,
     jnxSoamLmCurrentStatsForwardMaxFlr Unsigned32,
     jnxSoamLmCurrentStatsForwardAvgFlr Unsigned32,
     jnxSoamLmCurrentStatsBackwardTransmittedFrames Gauge32,
     jnxSoamLmCurrentStatsBackwardReceivedFrames Gauge32,
     jnxSoamLmCurrentStatsBackwardMinFlr Unsigned32,
     jnxSoamLmCurrentStatsBackwardMaxFlr Unsigned32,
     jnxSoamLmCurrentStatsBackwardAvgFlr Unsigned32,
     jnxSoamLmCurrentStatsSoamPdusSent Gauge32,
     jnxSoamLmCurrentStatsSoamPdusReceived Gauge32
}

jnxSoamLmCurrentStatsIndex OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The index for the current Measurement Interval for this
          PM session. This value will become the value for
          jnxSoamLmHistoryStatsIndex once the Measurement Interval
          is completed.

          Measurement Interval indexes are assigned sequentially by
          the SNMP Agent. The first Measurement Interval that occurs after
          the session is started is assigned index 1.
          "
     ::= { jnxSoamLmCurrentStatsEntry 1 }

jnxSoamLmCurrentStatsStartTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
        "The time that the current Measurement Interval started.
        "
     REFERENCE
        "[MEF SOAM-PM] R22, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 2 }

jnxSoamLmCurrentStatsElapsedTime OBJECT-TYPE
     SYNTAX TimeInterval
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The time that the current Measurement Interval has been running, in 0.01
          seconds.
          "
     REFERENCE
        "[MEF SOAM-PM] R24, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 3 }

jnxSoamLmCurrentStatsSuspect OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "Whether the Measurement Interval has been marked as suspect.

          The object is set to false at the start of a measurement
          interval. It is set to true when there is a discontinuity in the
          performance measurements during the Measurement Interval. Conditions
          for a discontinuity include, but are not limited to the following:

          1 - The local time-of-day clock is adjusted by at least 10 seconds
          2 - The conducting of a performance measurement is halted before the
          current Measurement Interval is completed
          3 - A local test, failure, or reconfiguration that disrupts service
          "
     REFERENCE
        "[MEF SOAM-PM] R39, R40, R41"
     ::= { jnxSoamLmCurrentStatsEntry 4 }

jnxSoamLmCurrentStatsForwardTransmittedFrames OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames transmitted in the
          forward direction by this MEP.

          For a PM Session of types lmLmm and lmCcm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.
          For a PM Session of type lmSlm this includes the count of SOAM
          ETH-SLM frames only.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 5 }

jnxSoamLmCurrentStatsForwardReceivedFrames OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames received in the
          forward direction by this MEP.

          For a PM Session of types lmLmm and lmCcm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.

          For a PM Session of types lmSlm this includes the count of SOAM
          ETH-SLM frames only.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 6 }

jnxSoamLmCurrentStatsForwardMinFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame loss
          ratio in the forward direction calculated by this MEP for this
          Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
        "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmCurrentStatsEntry 7 }

jnxSoamLmCurrentStatsForwardMaxFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame loss
          ratio in the forward direction calculated by this MEP for this
          Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
        "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmCurrentStatsEntry 8 }

jnxSoamLmCurrentStatsForwardAvgFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way frame loss
          ratio in the forward direction calculated by this MEP for this
          Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
        "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmCurrentStatsEntry 9 }

jnxSoamLmCurrentStatsBackwardTransmittedFrames OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames transmitted in the
          backward direction by this MEP.

          For a PM Session of type lmLmm and lmCcm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.

          For a PM Session of type lmSlm this includes the count of SOAM
          ETH-SLM frames only.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 10 }

jnxSoamLmCurrentStatsBackwardReceivedFrames OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames received in the
          backward direction by this MEP.

          For a PM Session of type lmLmm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.

          For a PM Session of type lmSlm this includes the count of SOAM
          ETH-SLM frames only.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 11 }

jnxSoamLmCurrentStatsBackwardMinFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame loss
          ratio in the backward direction calculated by this MEP for
          this Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
        "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmCurrentStatsEntry 12 }

jnxSoamLmCurrentStatsBackwardMaxFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame loss
          ratio in the backward direction calculated by this MEP for
          this Measurement Interval. The FLR value is a ratio that is expressed
          as a percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
        "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmCurrentStatsEntry 13 }

jnxSoamLmCurrentStatsBackwardAvgFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way frame loss
          ratio in the backward direction calculated by this MEP for
          this Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
        "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmCurrentStatsEntry 14 }

jnxSoamLmCurrentStatsSoamPdusSent OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM PDUs sent
          during this Measurement Interval.

          This object applies when  jnxSoamLmCfgType is lmLmm, lmSlm, or
          lmCcm. It indicates the number of LMM, CCM, or SLM SOAM frames
          transmitted.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 15 }

jnxSoamLmCurrentStatsSoamPdusReceived OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM PDUs
          PDUs received in this Measurement Interval.

          This object applies when  jnxSoamLmCfgType is lmLmm, lmSlm, or
          lmCcm. This object indicates the number of LMR, CCM, or SLR SOAM
          frames received.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmCurrentStatsEntry 16 }

-- *****************************************************************************
-- Ethernet Loss Measurement Loss History Statistic Table
-- *****************************************************************************

jnxSoamLmHistoryStatsTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamLmHistoryStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table contains the results for history Measurement
          Intervals in a SOAM Loss Measurement session.

          Rows of this table object are created automatically
          by the SNMP Agent when the Loss Measurement session is running and a
          Measurement Interval is completed.

          Each row in the table represents the history statistics for a Loss
          Measurement session Measurement Interval for the defined MEP. This
          table uses five indices. The first three indices are the indices of
          the Maintenance Domain, MaNet, and MEP tables. The fourth index is the
          specific LM session on the selected MEP. The fifth index index the
          specific Measurement Interval.

          At least 32 completed Measurement Intervals are to be supported. 96
          completed Measurement Intervals are recommended to be supported. If
          there are at least 32 rows in the table and a new Measurement Interval
          completes and a new row is to be added to the table, the oldest completed
          Measurement Interval may be deleted (row deletion). If the measurement
          interval is other than 15 minutes then a minimum of 8 hours of
          completed Measurement Intervals are to be supported and 24 hours are
          recommended to be supported.

          Except for jnxSoamLmHistoryStatsIndex, jnxSoamLmHistoryStatsEndTime,
          jnxSoamLmHistoryStatsElapsedTime and jnxSoamLmHistoryStatsSuspect,
          each object in this table applies only if the corresponding bit is set in
          jnxSoamLmCfgMeasurementEnable.

          The rows and objects in this table are to be persistent upon reboot
          or restart of a device.
          "
     REFERENCE
         "[MEF SOAM-PM] R7, R15, R21, D8, R25"
     ::= { jnxSoamPmLmObjects 4 }

jnxSoamLmHistoryStatsEntry OBJECT-TYPE
     SYNTAX JnxSoamLmHistoryStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamLmHistoryStatsTable"
     INDEX {
            dot1agCfmMdIndex,
            dot1agCfmMaIndex,
            dot1agCfmMepIdentifier,
            jnxSoamLmCfgIndex,
            jnxSoamLmHistoryStatsIndex
     }
     ::= { jnxSoamLmHistoryStatsTable  1 }

JnxSoamLmHistoryStatsEntry ::= SEQUENCE {
     jnxSoamLmHistoryStatsIndex Unsigned32,
     jnxSoamLmHistoryStatsEndTime DateAndTime,
     jnxSoamLmHistoryStatsElapsedTime TimeInterval,
     jnxSoamLmHistoryStatsSuspect TruthValue,
     jnxSoamLmHistoryStatsForwardTransmittedFrames Gauge32,
     jnxSoamLmHistoryStatsForwardReceivedFrames Gauge32,
     jnxSoamLmHistoryStatsForwardMinFlr Unsigned32,
     jnxSoamLmHistoryStatsForwardMaxFlr Unsigned32,
     jnxSoamLmHistoryStatsForwardAvgFlr Unsigned32,
     jnxSoamLmHistoryStatsBackwardTransmittedFrames Gauge32,
     jnxSoamLmHistoryStatsBackwardReceivedFrames Gauge32,
     jnxSoamLmHistoryStatsBackwardMinFlr Unsigned32,
     jnxSoamLmHistoryStatsBackwardMaxFlr Unsigned32,
     jnxSoamLmHistoryStatsBackwardAvgFlr Unsigned32,
     jnxSoamLmHistoryStatsSoamPdusSent Gauge32,
     jnxSoamLmHistoryStatsSoamPdusReceived Gauge32
}

jnxSoamLmHistoryStatsIndex OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The index for the Measurement Interval within this
          PM session.

          Measurement Interval indexes are assigned sequentially by
          the SNMP Agent. The first Measurement Interval that occurs after
          the session is started is assigned index 1. Measurement Intervals
          for FLR (stored in this table) are based on
          iterator count and are indexed independently
          of Measurement Intervals for availability 

          Referential integrity is necessary, i.e., the index needs to be
          persistent upon a reboot or restart of a device. The index
          is never reused while this session is active until it wraps to zero.
          The index value keeps increasing up to that time.
          "
     ::= { jnxSoamLmHistoryStatsEntry 1 }

jnxSoamLmHistoryStatsEndTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The time that the Measurement Interval ended.
          "
     REFERENCE
        "[MEF SOAM-PM] R23, R87, R112"
     ::= { jnxSoamLmHistoryStatsEntry 2 }

jnxSoamLmHistoryStatsElapsedTime OBJECT-TYPE
     SYNTAX TimeInterval
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The length of time that the Measurement Interval ran for,
          in 0.01 seconds.
          "
     REFERENCE
         "[MEF SOAM-PM] R24, R87, R112"
     ::= { jnxSoamLmHistoryStatsEntry 3 }

jnxSoamLmHistoryStatsSuspect OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "Whether the Measurement Interval has been marked as suspect.

         The object is set to true when there is a discontinuity in the
         performance measurements during the Measurement Interval. Conditions
         for a discontinuity include, but are not limited to the following:

         1 - The local time-of-day clock is adjusted by at least 10 seconds
         2 - The conducting of a performance measurement is halted before the
         current Measurement Interval is completed
         3 - A local test, failure, or reconfiguration that disrupts service
         "
     REFERENCE
        "[MEF SOAM-PM] R39, R40, R41, R42"
     ::= { jnxSoamLmHistoryStatsEntry 4 }

jnxSoamLmHistoryStatsForwardTransmittedFrames OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames transmitted in the
          forward direction by this MEP.

          For a PM Session of types lmLmm and lmCcm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.

          For a PM Session of type lmSlm this includes the count of OAM
          ETH-SLM frames only.
          "
     REFERENCE
         "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmHistoryStatsEntry 5 }

jnxSoamLmHistoryStatsForwardReceivedFrames OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames received in the
          forward direction by this MEP.

          For a PM Session of types lmLmm and lmCcm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.

          For a PM Session of type lmSlm this includes the count of OAM
          ETH-SLM frames only.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmHistoryStatsEntry 6 }

jnxSoamLmHistoryStatsForwardMinFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame loss
          ratio in the forward direction calculated by this MEP for this
          Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmHistoryStatsEntry 7 }

jnxSoamLmHistoryStatsForwardMaxFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame loss
          ratio in the forward direction calculated by this MEP for this
          Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmHistoryStatsEntry 8 }

jnxSoamLmHistoryStatsForwardAvgFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way frame loss
          ratio in the forward direction calculated by this MEP for this
          Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmHistoryStatsEntry 9 }

jnxSoamLmHistoryStatsBackwardTransmittedFrames OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames transmitted in the
          backward direction by this MEP.

          For a PM Session of type lmLmm and lmCcm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.

          For a PM Session of types lmSlm this includes the count of SOAM
          ETH-SLM frames only.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmHistoryStatsEntry 10 }

jnxSoamLmHistoryStatsBackwardReceivedFrames  OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the number of frames received in the
          backward direction by this MEP.

          For a PM Session of type lmLmm and lmCcm this includes Ethernet
          Service Frames and SOAM PDUs that are in a higher MEG level only.

          For a PM Session of types lmSlm this includes the count of SOAM
          ETH-SLM frames only.
          "
     REFERENCE
         "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmHistoryStatsEntry 11 }

jnxSoamLmHistoryStatsBackwardMinFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame loss
          ratio in the backward direction calculated by this MEP for
          this Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] R69, R87, R112"
     ::= { jnxSoamLmHistoryStatsEntry 12 }

jnxSoamLmHistoryStatsBackwardMaxFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame loss
          ratio in the backward direction calculated by this MEP for
          this Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmHistoryStatsEntry 13 }

jnxSoamLmHistoryStatsBackwardAvgFlr OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way frame loss
          ratio in the backward direction calculated by this MEP for
          this Measurement Interval. The FLR value is a ratio that is expressed as a
          percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

          Units are in milli-percent, where 1 indicates 0.001 percent.
          "
     REFERENCE
         "[MEF SOAM-PM] D37"
     ::= { jnxSoamLmHistoryStatsEntry 14 }

jnxSoamLmHistoryStatsSoamPdusSent OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM PDUs sent
          during this Measurement Interval.

          This object applies when jnxSoamLmCfgType is lmLmm, lmSlm,
          or lmCcm. It indicates the number of LMM, CCM, or SLM SOAM frames
          transmitted.
          "
     REFERENCE
         "[MEF SOAM-PM] R69, R87"
     ::= { jnxSoamLmHistoryStatsEntry 15 }

jnxSoamLmHistoryStatsSoamPdusReceived OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM
          PDUs received in this Measurement Interval.

          This object applies when jnxSoamLmCfgType is lmLmm, lmSlm,
          or lmCcm. This object indicates the number of LMR, CCM, or SLR
          SOAM frames received.
          "
     REFERENCE
        "[MEF SOAM-PM] R69, R87"
     ::= { jnxSoamLmHistoryStatsEntry 16 }

-- *****************************************************************************
-- Ethernet Delay Measurement Configuration Table
-- *****************************************************************************

jnxSoamDmCfgTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamDmCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table includes configuration objects and operations for the
          Delay Measurement function.

          Each row in the table represents a Delay Measurement session for
          the defined MEP. This table uses four indices. The first three indices
          are the indices of the Maintenance Domain, MaNet, and MEP tables. The
          fourth index is the specific DM session on the selected MEP.

          A Delay Measurement session is created on an existing MEP by first
          accessing the jnxSoamDmOperNextIndex object and using this value as
          the jnxSoamDmCfgIndex in the row creation.

          Some writable objects in this table are only applicable in certain cases
          (as described under each object), and attempts to write values for them
          in other cases will be ignored.

          The writable objects in this table need to be persistent upon reboot
          or restart of a device.
          "
     REFERENCE
        "[MEF SOAM-PM] R50; [Y.1731]"
     ::= { jnxSoamPmDmObjects 1 }

jnxSoamDmCfgEntry OBJECT-TYPE
     SYNTAX JnxSoamDmCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamDmCfgTable."
     INDEX {
                dot1agCfmMdIndex,
                dot1agCfmMaIndex,
                dot1agCfmMepIdentifier,
                jnxSoamDmCfgIndex
     }
     ::= { jnxSoamDmCfgTable 1 }

JnxSoamDmCfgEntry ::= SEQUENCE {
     jnxSoamDmCfgIndex Unsigned32,
     jnxSoamDmCfgType INTEGER,
     jnxSoamDmCfgVersion Unsigned32,
     jnxSoamDmCfgEnabled TruthValue,
     jnxSoamDmCfgMeasurementEnable BITS,
     jnxSoamDmCfgMessagePeriod Integer32,
     jnxSoamDmCfgPriority IEEE8021PriorityValue,
     jnxSoamDmCfgFrameSize Unsigned32,
     jnxSoamDmCfgDataPattern JnxSoamTcDataPatternType,
     jnxSoamDmCfgTestTlvIncluded TruthValue,
     jnxSoamDmCfgTestTlvPattern JnxSoamTcTestPatternType,
     jnxSoamDmCfgNumIntervalsStored Unsigned32,
     jnxSoamDmCfgDestMepId Dot1agCfmMepIdOrZero,
     jnxSoamDmCfgDestIsMepId TruthValue,
     jnxSoamDmCfgStartTimeType JnxSoamTcOperationTimeType,
     jnxSoamDmCfgRepetitionTime Unsigned32,
     jnxSoamDmCfgAlignMeasurementIntervals TruthValue,
     jnxSoamDmCfgInterFrameDelayVariationSelectionOffset Unsigned32,
     jnxSoamDmCfgSessionType OCTET STRING,
     jnxSoamDmCfgSessionStatus OCTET STRING,
     jnxSoamDmCfgHistoryClear TruthValue,
     jnxSoamDmCfgRowStatus RowStatus,
     jnxSoamDmCfgMeasurementInterval Unsigned32,
     jnxSoamDmCfgDestMacAddress MacAddress,
     jnxSoamDmCfgSourceMacAddress MacAddress,
     jnxSoamDmCfgFixedStartDateAndTime DateAndTime,
     jnxSoamDmCfgRelativeStartTime TimeInterval,
     jnxSoamDmCfgStopTimeType JnxSoamTcOperationTimeType,
     jnxSoamDmCfgFixedStopDateAndTime DateAndTime,
     jnxSoamDmCfgRelativeStopTime TimeInterval,
     jnxSoamDmCfgAlignMeasurementOffset Unsigned32,
     jnxSoamDmCfgNumMeasBinsPerFrameDelayInterval Unsigned32,
     jnxSoamDmCfgNumMeasBinsPerInterFrameDelayVariationInterval Unsigned32,
     jnxSoamDmCfgNumMeasBinsPerFrameDelayRangeInterval Unsigned32
}

jnxSoamDmCfgIndex
     OBJECT-TYPE
     SYNTAX Unsigned32(1..4294967295)
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "An index to the Delay Measurement Configuration table which indicates
          the specific measurement session for the MEP.

          jnxSoamPmMepOperNextIndex needs to be inspected to find an
          available index for row-creation.

          Referential integrity is necessary, i.e., the index needs to be
          persistent upon a reboot or restart of a device. The index
          is never reused for other PM sessions on the same MEP while this
          session is active. The index value keeps increasing until it
          wraps to zero. This is to facilitate access control based
          on a fixed index for an EMS, since the index is not reused.
          "
     ::= { jnxSoamDmCfgEntry 1 }

jnxSoamDmCfgType OBJECT-TYPE
     SYNTAX INTEGER {
                dmDmm (1),
                dm1DmTx (2),
                dm1DmRx (3)
     }
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object indicates what type of Delay Measurement is to
          be performed.

          dmDmm(1) DMM SOAM PDU generated, DMR responses received
          (one-way or two-way measurements)
          dm1DmTx(2) 1DM SOAM PDU generated (one-way measurements are made by
          the receiver)
          dm1DmRx(3) 1DM SOAM PDU received and tracked (one-way measurements)

          The exact PDUs to use are specified by this object in combination with
          jnxSoamDmCfgVersion.

          The value dmDMM is required. The values dm1DmTx and dm1DmRx are optional.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R52, R53, R54, O5, R88"
     ::= { jnxSoamDmCfgEntry 2 }

jnxSoamDmCfgVersion OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object indicates the version of the PDUs used to perform
          Delay Measurement.

          Version 0 indicates the PDU formats defined in Y.1731-2008.
          Version 1 indicates the PDU formats defined in Y.1731-2011.

          The exact PDUs to use are specified by this object in combination with
          jnxSoamDmCfgType.

          This object can only be written at row creation time  and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[Y.1731]"
     DEFVAL { 0 }
     ::= { jnxSoamDmCfgEntry 3 }

jnxSoamDmCfgEnabled OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies whether the Delay Measurement session is
          enabled.

          The value 'true' indicates the Delay Measurement session is enabled AND
          SOAM PDUs are sent and/or measurements are collected when the session
          is running according to the scheduling objects (start time, stop time,
          etc.).

          The value 'false' indicates the Delay Measurement session is disabled
          AND SOAM PDUs are not sent and/or measurements collected.

          For a Delay Measurement session to be removed the row is deleted in
          order to release internal resources.

          This object can written/modified after row creation time.

          If the DM session is enabled it resumes after shutdown/restart.

          If the DM session is disabled the current Measurement Interval is
          stopped, if it in process at the time, and all the in process calculations
          for the partially completed Measurement Interval are finalized.

          This object does not affect whether the single-ended Responder is
          enabled or not, which is enabled or disabled by the
          jnxSoamPmMepDmSingleEndedResponder object.
          "
     REFERENCE
        "[MEF SOAM-PM] R4, R5, R6, O1, R12, R14"
     DEFVAL { true }
     ::= { jnxSoamDmCfgEntry 4 }

jnxSoamDmCfgMeasurementEnable OBJECT-TYPE
     SYNTAX BITS {
                bSoamPdusSent(0),
                bSoamPdusReceived(1),
                bFrameDelayTwoWayBins(2),
                bFrameDelayTwoWayMin(3),
                bFrameDelayTwoWayMax(4),
                bFrameDelayTwoWayAvg(5),
                bFrameDelayForwardBins(6),
                bFrameDelayForwardMin(7),
                bFrameDelayForwardMax(8),
                bFrameDelayForwardAvg(9),
                bFrameDelayBackwardBins(10),
                bFrameDelayBackwardMin(11),
                bFrameDelayBackwardMax(12),
                bFrameDelayBackwardAvg(13),
                bIfdvForwardBins(14),
                bIfdvForwardMin(15),
                bIfdvForwardMax(16),
                bIfdvForwardAvg(17),
                bIfdvBackwardBins(18),
                bIfdvBackwardMin(19),
                bIfdvBackwardMax(20),
                bIfdvBackwardAvg(21),
                bIfdvTwoWayBins(22),
                bIfdvTwoWayMin(23),
                bIfdvTwoWayMax(24),
                bIfdvTwoWayAvg(25),
                bFrameDelayRangeForwardBins(26),
                bFrameDelayRangeForwardMax(27),
                bFrameDelayRangeForwardAvg(28),
                bFrameDelayRangeBackwardBins(29),
                bFrameDelayRangeBackwardMax(30),
                bFrameDelayRangeBackwardAvg(31),
                bFrameDelayRangeTwoWayBins(32),
                bFrameDelayRangeTwoWayMax(33),
                bFrameDelayRangeTwoWayAvg(34),
                bMeasuredStatsFrameDelayTwoWay(35),
                bMeasuredStatsFrameDelayForward(36),
                bMeasuredStatsFrameDelayBackward(37),
                bMeasuredStatsIfdvTwoWay(38),
                bMeasuredStatsIfdvForward(39),
                bMeasuredStatsIfdvBackward(40)
     }
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "A vector of bits that indicates the type of SOAM DM counters that
          are enabled.

          A bit set to '1' enables the specific SOAM DM counter.

          A bit set to '0' disables the SOAM DM counter.

          If a particular SOAM DM counter is not supported the BIT value is
          set to '0'.

          Not all SOAM DM counters are supported for all SOAM DM types.

          This object can only be written at row creation time and cannot be
          modified once it has been created.

          bSoamPdusSent(0)
          Enables/disables the jnxSoamDmCurrentStatsSoamPdusSent
          and jnxSoamDmHistoryStatsSoamPdusSent counters.
          bSoamPdusReceived(1)
          Enables/disables the jnxSoamDmCurrentStatsSoamPdusReceived
          and jnxSoamDmHistoryStatsSoamPdusReceived counters.
          bFrameDelayTwoWayBins(2)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'twoWayFrameDelay'.
          bFrameDelayTwoWayMin(3)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayTwoWayMin
          and jnxSoamDmHistoryStatsFrameDelayTwoWayMin counters.
          bFrameDelayTwoWayMax(4)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayTwoWayMax
          and jnxSoamDmHistoryStatsFrameDelayTwoWayMax counters.
          bFrameDelayTwoWayAvg(5)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayTwoWayAvg
          and jnxSoamDmHistoryStatsFrameDelayTwoWayAvg counters.
          bFrameDelayForwardBins(6)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'forwardFrameDelay'.
          bFrameDelayForwardMin(7)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayForwardMin
          and jnxSoamDmHistoryStatsFrameDelayForwardMin counters.
          bFrameDelayForwardMax(8)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayForwardMax
          and jnxSoamDmHistoryStatsFrameDelayForwardMax counters.
          bFrameDelayForwardAvg(9)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayForwardAvg
          and jnxSoamDmHistoryStatsFrameDelayForwardAvg counters.
          bFrameDelayBackwardBins(10)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'backwardFrameDelay'.
          bFrameDelayBackwardMin(11)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayBackwardMin
          and jnxSoamDmHistoryStatsFrameDelayBackwardMin counters.
          bFrameDelayBackwardMax(12)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayBackwardMax
          and jnxSoamDmHistoryStatsFrameDelayBackwardMax counters.
          bFrameDelayBackwardAvg(13)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayBackwardAvg
          and jnxSoamDmHistoryStatsFrameDelayBackwardAvg counters.
          bIfdvForwardBins(14)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'forwardIfdv'.
          bIfdvForwardMin(15)
          Enables/disables the jnxSoamDmCurrentStatsIfdvForwardMin
          and jnxSoamDmHistoryStatsIfdvForwardMin counters.
          bIfdvForwardMax(16)
          Enables/disables the jnxSoamDmCurrentStatsIfdvForwardMax
          and jnxSoamDmHistoryStatsIfdvForwardMax counters.
          bIfdvForwardAvg(17)
          Enables/disables the jnxSoamDmCurrentStatsIfdvForwardAvg
          and jnxSoamDmHistoryStatsIfdvForwardAvg counters.
          bIfdvBackwardBins(18)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'backwardIfdv'.
          bIfdvBackwardMin(19)
          Enables/disables the jnxSoamDmCurrentStatsIfdvBackwardMin
          and jnxSoamDmHistoryStatsIfdvBackwardMin counters.
          bIfdvBackwardMax(20)
          Enables/disables the jnxSoamDmCurrentStatsIfdvBackwardMax
          and jnxSoamDmHistoryStatsIfdvBackwardMax counters.
          bIfdvBackwardAvg(21)
          Enables/disables the jnxSoamDmCurrentStatsIfdvBackwardAvg
          and jnxSoamDmHistoryStatsIfdvBackwardAvg counters.
          bIfdvTwoWayBins(22)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'twoWayIfdv'.
          bIfdvTwoWayMin(23)
          Enables/disables the jnxSoamDmCurrentStatsIfdvTwoWayMin
          and jnxSoamDmHistoryStatsIfdvTwoWayMin counters.
          bIfdvTwoWayMax(24)
          Enables/disables the jnxSoamDmCurrentStatsIfdvTwoWayMax
          and jnxSoamDmHistoryStatsIfdvTwoWayMax counters.
          bIfdvTwoWayAvg(25)
          Enables/disables the jnxSoamDmCurrentStatsIfdvTwoWayAvg
          and jnxSoamDmHistoryStatsIfdvTwoWayAvg counters.
          bFrameDelayRangeForwardBins(26)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'forwardFrameDelayRange'.
          bFrameDelayRangeForwardMax(27)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayRangeForwardMax
          and jnxSoamDmHistoryStatsFrameDelayRangeForwardMax counters.
          bFrameDelayRangeForwardAvg(28)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayRangeForwardAvg
          and jnxSoamDmHistoryStatsFrameDelayRangeForwardAvg counters.
          bFrameDelayRangeBackwardBins(29)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'backwardFrameDelayRange'.
          bFrameDelayRangeBackwardMax(30)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayRangeBackwardMax
          and jnxSoamDmHistoryStatsFrameDelayRangeBackwardMax counters.
          bFrameDelayRangeBackwardAvg(31)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayRangeBackwardAvg
          and jnxSoamDmHistoryStatsFrameDelayRangeBackwardAvg counters.
          bFrameDelayRangeTwoWayBins(32)
          Enables/disables the jnxSoamDmCurrentStatsBinsEntry counter
          and the jnxSoamDmHistoryStatsBinsEntry counter
          when the jnxSoamDmCfgMeasBinType is 'twoWayFrameDelayRange'.
          bFrameDelayRangeTwoWayMax(33)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayRangeTwoWayMax
          and jnxSoamDmHistoryStatsFrameDelayRangeTwoWayMax counters.
          bFrameDelayRangeTwoWayAvg(34)
          Enables/disables the jnxSoamDmCurrentStatsFrameDelayRangeTwoWayAvg
          and jnxSoamDmHistoryStatsFrameDelayRangeTwoWayAvg counters.
          bMeasuredStatsFrameDelayTwoWay(35)
          Enables/disables the jnxSoamDmMeasuredStatsFrameDelayTwoWay
          counter.
          bMeasuredStatsFrameDelayForward(36)
          Enables/disables the jnxSoamDmMeasuredStatsFrameDelayForward
          counter.
          bMeasuredStatsFrameDelayBackward(37)
          Enables/disables the jnxSoamDmMeasuredStatsFrameDelayBackward
          counter.
          bMeasuredStatsIfdvTwoWay(38)
          Enables/disables the jnxSoamDmMeasuredStatsIfdvTwoWay
          counter.
          bMeasuredStatsIfdvForward(39)
          Enables/disables the jnxSoamDmMeasuredStatsIfdvForward
          counter.
          bMeasuredStatsIfdvBackward(40)
          Enables/disables the jnxSoamDmMeasuredStatsIfdvBackward
          counter.
          "
     REFERENCE
        "[MEF SOAM-PM]"
     DEFVAL { { } }
     ::= { jnxSoamDmCfgEntry 5 }

jnxSoamDmCfgMessagePeriod OBJECT-TYPE
     SYNTAX Integer32
     UNITS "ms"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the interval between Delay Measurement
          OAM message transmission. For Delay Measurement monitoring
          applications, the default value is 100ms.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R61, R62, D22, R95, R96, D39"
     DEFVAL { 100 }
     ::= { jnxSoamDmCfgEntry 6 }

jnxSoamDmCfgPriority OBJECT-TYPE
     SYNTAX IEEE8021PriorityValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the priority of frames with
          Delay Measurement OAM message information.

          The default value is to be the value which yields the lowest frame
          loss.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R1, R2, R56, D21, R57, R58, R90-R94, D28;
        [MEF 10.2.1] Section 6.8"
     ::= { jnxSoamDmCfgEntry 7 }

jnxSoamDmCfgFrameSize OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the Delay Measurement frame size between
          64 bytes and the maximum transmission unit of the EVC.

          The range of frame sizes from 64 through 2000 octets need to be
          supported, and the range of frame sizes from 2001 through 9600 octets
          is suggested to be supported.

          The adjustment to the frame size of the standard frame size is
          accomplished by the addition of a Data or Test TLV. A Data or Test TLV
          is only added to the frame if the frame size is greater than 64 bytes.

          This object is only valid for the entity transmitting the Delay
          Measurement frames (dmDmm, dm1DmTx) and is ignored by the entity
          receiving frames.

          In addition, this object is not valid when jnxSoamDmCfgVersion is 0.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R63, R64, D23, D24, R97, R98, D40, D41"
     DEFVAL { 64 }
     ::= { jnxSoamDmCfgEntry 8 }

jnxSoamDmCfgDataPattern OBJECT-TYPE
     SYNTAX JnxSoamTcDataPatternType
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the DM data pattern included in a Data TLV
          when the size of the DM frame is determined by the
          jnxSoamDmFrameSize object and jnxSoamDmTestTlvIncluded is 'false'.
          If the frame size object does not define the DM frame size or
          jnxSoamDmTestTlvIncluded is 'true' the value  of this object is
          ignored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     DEFVAL { zeroPattern }
     ::= { jnxSoamDmCfgEntry 9 }

jnxSoamDmCfgTestTlvIncluded OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "Indicates whether a Test TLV or Data TLV is included when the size
          of the DM frame is determined by the jnxSoamDmFrameSize object.
          A value of 'true' indicates that the Test TLV is to be included. A
          value of 'false' indicates that the Data TLV is to be included.

          If the frame size object does not define the DM frame size
          the value of this object is ignored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[Y.1731] 9.3"
     DEFVAL { false }
     ::= { jnxSoamDmCfgEntry 10 }

jnxSoamDmCfgTestTlvPattern OBJECT-TYPE
     SYNTAX JnxSoamTcTestPatternType
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the type of test pattern to be
          sent in the DM frame Test TLV when the size
          of DM PDU is determined by the jnxSoamDmFrameSize object and
          jnxSoamDmTestTlvIncluded is 'true'. If the frame size object
          does not define the DM frame size or jnxSoamDmTestTlvIncluded
          is 'false' the value of this object is ignored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     DEFVAL { null }
     ::= { jnxSoamDmCfgEntry 11 }

jnxSoamDmCfgNumIntervalsStored OBJECT-TYPE
     SYNTAX Unsigned32 (1..1000)
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the number of completed Measurement Intervals
          to store in the history statistic table.

          At least 32 completed Measurement Intervals are to be stored. 96
          Measurement Intervals are recommended to be stored.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R21, D8, D9"
     DEFVAL { 32 }
     ::= { jnxSoamDmCfgEntry 12 }

jnxSoamDmCfgDestMepId OBJECT-TYPE
     SYNTAX Dot1agCfmMepIdOrZero
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The Maintenance Association End Point Identifier of
          another MEP in the same Maintenance Association to which
          the SOAM DM frame is to be sent.

          This address will be used if the value of the column
          jnxSoamDmDestIsMepId is 'true'. A value of zero
          means that the destination MEP ID has not been configured.

          This object is only valid for the entity transmitting the Delay
          Measurement frames, types 'dmDmm' and 'dm1DmTx'. It is not applicable
          for the 'dm1DmRx' type.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R55, R89"
     DEFVAL { 0 }
     ::= { jnxSoamDmCfgEntry 13 }

jnxSoamDmCfgDestIsMepId OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "A value of 'true' indicates that MEPID of the target MEP is used for
          SOAM DM frame transmission.

          A value of 'false' indicates that the destination MAC address of the
          target MEP is used for SOAM DM frame transmission.

          This object is only valid for the entity transmitting the Delay
          Measurement frames, types 'dmDmm' and 'dm1DmTx'. It is not applicable
          for the 'dm1DmRx type.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R55, R89"
     DEFVAL { true }
     ::= { jnxSoamDmCfgEntry 14 }

jnxSoamDmCfgStartTimeType OBJECT-TYPE
     SYNTAX JnxSoamTcOperationTimeType
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the type of start time  of the SOAM DM
          session. The start time can be disabled (none), immediate, relative,
          or fixed.

          The value of 'none' is illegal and a write error will be returned
          if this value is used.

          The value of 'immediate' starts the SOAM DM session when the
          jnxSoamDmCfgEnabled is true.

          The value of 'fixed' starts the SOAM DM session when the
          jnxSoamDmFixedStartDateAndTime is less than or equal to the current
          system date and time and jnxSoamDmCfgEnabled is true. This value is used
          to implement an On-Demand fixed time PM session.

          The value of 'relative' starts the SOAM DM session when the current
          system date and time minus the jnxSoamDmRelativeStartTime is greater than
          or equal to the system date and time when the jnxSoamDmStartTimeType
          object was written and jnxSoamDmCfgEnabled is true. This value is used
          to implement an On-Demand relative time PM session.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R3, R7, R8, D1"
     DEFVAL { immediate }
     ::= { jnxSoamDmCfgEntry 15 }

jnxSoamDmCfgRepetitionTime OBJECT-TYPE
     SYNTAX Unsigned32 (0..31536000)
     UNITS "seconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies a configurable repetition time between
          Measurement Intervals in a Delay Measurement session in seconds.

          If the value is 0 (none), there is no time gap between the end of one
          Measurement Interval and the start of a new Measurement Interval.
          This is the normal usage case.

          If the value is greater than one Measurement Interval there is time gap
          between the end of one Measurement Interval and the start of the next
          Measurement Interval. The repetition time specifies the time between
          the start of consecutive Measurement Intervals; hence the gap between
          the end of one Measurement Interval and the start of the next is equal
          to the difference between the repetition time and the measurement
          interval. During this gap, no SOAM PDUs are sent for this session and
          no measurements are made.

          If the value is greater 0 but less than or equal to the measurement
          interval, an error is returned.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R18, D3, R19, R20"
     DEFVAL { 0 }
     ::= { jnxSoamDmCfgEntry 16 }

jnxSoamDmCfgAlignMeasurementIntervals OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies whether the Measurement Intervals for
          the Delay Measurement session are aligned with a zero offset to
          real time.

          The value 'true' indicates that each Measurement Interval starts
          at a time which is aligned to NE time source hour, if the repetition
          time (or the Measurement Interval, if the repetition time is 0) is
          a factor of an hour, i.e. 60min/15min = 4. For instance, a
          Measurement Interval/Repetition Time of 15 minutes would stop/start
          the Measurement Interval at 0, 15, 30, and 45 minutes of an hour. A
          Measurement Interval/Repetition Time of 7 minutes would not align
          to the hour since 7 minutes is NOT a factor of an hour, i.e.
          60min/7min = 8.6. In this case the behavior is the same as if the
          object is set to 'false'.

          The value 'false' indicates that the first Measurement Interval starts
          at an arbitrary time and each subsequent Measurement Interval starts
          at a time which is determined by jnxSoamLmCfgRepetitionTime.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] D4, D5, D6"
     DEFVAL { true }
     ::= { jnxSoamDmCfgEntry 17 }

jnxSoamDmCfgInterFrameDelayVariationSelectionOffset OBJECT-TYPE
     SYNTAX Unsigned32 (1..100)
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object specifies the selection offset for
          Inter-Frame Delay Variation measurements. If this value
          is set to n, then the IFDV is calculated by taking the
          difference in frame delay between frame F and frame (F+n).

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] O4, D25, O6, D42"
     DEFVAL { 1 }
     ::= { jnxSoamDmCfgEntry 18 }

jnxSoamDmCfgSessionType OBJECT-TYPE
     SYNTAX OCTET STRING (SIZE(1..42))
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object indicates whether the current session is defined to
          be 'Proactive' or 'On-Demand'. A value of 'proactive'
          indicates the current session is 'Proactive'. A value of 'onDemand'
          indicates the current session is 'On-Demand'.

          This object can only be written at row creation time and cannot be
          modified once it has been created.
          "
     REFERENCE
        "[MEF SOAM-PM] R3"
     DEFVAL { "proactive" }
     ::= { jnxSoamDmCfgEntry 19 }

jnxSoamDmCfgSessionStatus OBJECT-TYPE
     SYNTAX OCTET STRING (SIZE(1..42))
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object indicates the current status of the DM session. A value
          of 'active' indicates the current DM session is active, i.e. the current
          time lies between the start time and the stop time, and
          jnxSoamDmCfgEnabled is true. A value of 'notActive' indicates the
          current DM session is not active, i.e. it has not started yet, has
          stopped upon reaching the stop time, or is disabled.
          "
     ::= { jnxSoamDmCfgEntry 20 }

jnxSoamDmCfgHistoryClear OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object when written clears the Delay Measurement history
          tables (jnxSoamDmHistoryStatsTable)
          - all rows are deleted. When read the value always returns 'false'.

          Writing this value does not change the current stat table,
          nor any of the items in the configuration table.

          Writing this object at row creation has no effect.
          "
     DEFVAL { false }
     ::= { jnxSoamDmCfgEntry 21 }

jnxSoamDmCfgRowStatus OBJECT-TYPE
     SYNTAX RowStatus
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The status of the row.

          The writable columns in a row cannot be changed if the row
          is active, except for jnxSoamDmCfgEnabled and jnxSoamDmCfgHistoryClear
          objects. All columns are to have a valid value before a row
          can be activated.
          "
     ::= { jnxSoamDmCfgEntry 22 }

jnxSoamDmCfgMeasurementInterval OBJECT-TYPE
    SYNTAX Unsigned32 (1..1440)
    UNITS "minutes"
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies a Measurement Interval in minutes.
       
        A Measurement Interval 15 minutes needs to be supported, other intervals
        may be supported.
       
        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R16, R17"
    DEFVAL { 15 }
    ::= { jnxSoamDmCfgEntry 23 }

jnxSoamDmCfgDestMacAddress OBJECT-TYPE
    SYNTAX MacAddress
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The Target or Destination MAC Address Field to be transmitted.

        If mefSoamDmCfgType is 'dmDmm', the destination address is to be the
        unicast address of the destination MEP. An error is returned if this
        object is set to a multicast address.

        If mefSoamDmCfgType is 'dm1DmTx', the destination address is normally the
        unicast address of the destination MEP, but can be a multicast address
        indicating the level of the MEG: 01-80-c2-00-00-3y, where y is the
        level of the MEG. An error is returned if this object is set to any
        other multicast address.

        If mefSoamDmCfgType is 'dm1DmRx', this object is ignored.

        This address will be used if the value of the object
        mefSoamDmDestIsMepId is 'false'.

        This object is only valid for the entity transmitting the
        SOAM DM frames and is ignored by the entity receiving
        SOAM DM frames.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R55, R89"
    ::= { jnxSoamDmCfgEntry 24 }

jnxSoamDmCfgSourceMacAddress OBJECT-TYPE
    SYNTAX MacAddress
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The Source MAC Address Field of the received SOAM DM session PDUs.

        If mefSoamDmCfgType is dm1DmRx this object indicates the source
        address of the dm1DmTx DM session.

        This object is only valid for mefSoamDmCfgType set to dm1DmRx. It is
        ignored for mefSoamDmCfgType set to dmDmm or dm1DmTx.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R55, R89"
    ::= { jnxSoamDmCfgEntry 25 }


jnxSoamDmCfgFixedStartDateAndTime OBJECT-TYPE
    SYNTAX DateAndTime
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the fixed start date/time for the
        SOAM Delay Measurement session. This object is used only used if
        mefSoamDmStartTimeType is 'fixed' and is ignored otherwise.

        The default value is year 0000, month 01, day 01, time 00:00:00.00.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R9"
    DEFVAL { '0000010100000000'H }
    ::= { jnxSoamDmCfgEntry 26 }

jnxSoamDmCfgRelativeStartTime OBJECT-TYPE
    SYNTAX TimeInterval
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the relative start time, from
        the current system time, for the SOAM DM session. This
        object is used only if mefSoamDmStartTimeType is 'relative'
        and is ignored otherwise.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R9"
    DEFVAL { 0 }
    ::= { jnxSoamDmCfgEntry 27 }

jnxSoamDmCfgStopTimeType OBJECT-TYPE
    SYNTAX JnxSoamTcOperationTimeType
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the type of stop time to terminate the
        SOAM DM session. The stop time can be forever (none), relative, or
        fixed.

        The value of 'none' indicates that the SOAM DM session never ends once it
        has started unless the session is disabled.

        The value of 'immediate' is illegal and a write error will be returned
        if this value is used.

        The value of 'fixed' stops the SOAM DM session when the
        mefSoamDmFixedStopDateAndTime is less than or equal
        to the current system date. This
        value is used to implement an On-Demand fixed time PM session.

        The value of 'relative' stops the SOAM DM session when the time
        indicated by mefSoamDmRelativeStopTime has passed since the session
        start time as determined by the mefSoamDmCfgStartTimeType,
        mefSoamDmCfgFixedStartDateAndTime and mefSoamDmCfgRelativeStartTime
        objects.
        This value is used to implement an On-Demand relative time PM session.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R3, R10, D2"
    DEFVAL { none }
    ::= { jnxSoamDmCfgEntry 28 }

jnxSoamDmCfgFixedStopDateAndTime OBJECT-TYPE
    SYNTAX DateAndTime
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the fixed stop date/time for the
        SOAM Delay Measurement session. This object is used only used
        if mefSoamDmStopTimeType is 'fixed' and is ignored otherwise.

        The default value is year 0000, month 01, day 01, time 00:00:00.00.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R10, R13"
    DEFVAL { '0000010100000000'H }
    ::= { jnxSoamDmCfgEntry 29 }

jnxSoamDmCfgRelativeStopTime OBJECT-TYPE
    SYNTAX TimeInterval
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the relative stop time, from the
        session start time, to stop the SOAM DM session. This
        object is used only if mefSoamDmStopTimeType is 'relative' and is
        otherwise.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R11"
    DEFVAL { 0 }
    ::= { jnxSoamDmCfgEntry 30 }

jnxSoamDmCfgAlignMeasurementOffset OBJECT-TYPE
    SYNTAX Unsigned32 (0..525600)
    UNITS "minutes"
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the offset in minutes from the time of day value
        if mefSoamDmCfgAlignMeasurementIntervals is 'true' and the repetition
        time is a factor of 60 minutes. If not, the value of this object
        is ignored.

        If the Measurement Interval is 15 minutes and
        mefSoamDmCfgAlignMeasurementIntervals is true and if this object was
        set to 5 minutes, the Measurement Intervals would start at 5, 20, 35, 50
        minutes past each hour.

        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] D7"
    DEFVAL { 0 }
    ::= { jnxSoamDmCfgEntry 31 }

jnxSoamDmCfgNumMeasBinsPerFrameDelayInterval OBJECT-TYPE
    SYNTAX Unsigned32 (2..100)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the number of measurement bins
        per Measurement Interval for Frame Delay measurements.
       
        At least 3 bins are to be supported; at least 10 bins are recommended
        to be supported.
       
        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
        REFERENCE
        "[MEF SOAM-PM] R27, D11, R28, D12"
        DEFVAL { 3 }
        ::= { jnxSoamDmCfgEntry 32 }
       
jnxSoamDmCfgNumMeasBinsPerInterFrameDelayVariationInterval OBJECT-TYPE
    SYNTAX Unsigned32 (2..100)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the number of measurement bins
        per Measurement Interval for Inter-Frame Delay Variation
        measurements.
       
        The minimum number of measurement bins to be supported is 2. The
        supported is 10.
       
        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R29, D13, R30, D14"
    DEFVAL { 2 }
        ::= { jnxSoamDmCfgEntry 33 }

jnxSoamDmCfgNumMeasBinsPerFrameDelayRangeInterval OBJECT-TYPE
    SYNTAX Unsigned32 (2..100)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "This object specifies the number of measurement bins
        per Measurement Interval for Frame Delay Range measurements.
       
        At least 2 bins are to be supported; at least 10 bins are recommended
        to be supported.
       
        This object can only be written at row creation time and cannot be
        modified once it has been created.
        "
    REFERENCE
        "[MEF SOAM-PM] R31, D15, R32, D16"
    DEFVAL { 2 }
    ::= { jnxSoamDmCfgEntry 34 }
       

-- *****************************************************************************
-- Ethernet Delay Measurement Measured Statistic Table
-- *****************************************************************************

jnxSoamDmMeasuredStatsTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamDmMeasuredStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This object contains the last measured results for a SOAM Delay
          Measurement session.

          Each row in the table represents a Delay Measurement session for
          the defined MEP. This table uses four indices. The first three indices
          are the indices of the Maintenance Domain, MaNet, and MEP tables. The
          fourth index is the specific DM session on the selected MEP.

          Instances of this managed object are created automatically
          by the SNMP Agent when the Delay Measurement session is running.

          Each object in this table applies only if the corresponding bit is set in
          jnxSoamDmCfgMeasurementEnable.

          The objects in this table do not need to be persistent upon reboot or restart
          of a device.
          "
     REFERENCE
        "[MEF SOAM-PM] R7, R15, D18"
     ::= { jnxSoamPmDmObjects 2 }

jnxSoamDmMeasuredStatsEntry OBJECT-TYPE
     SYNTAX JnxSoamDmMeasuredStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamDmMeasuredStatsTable"
     INDEX {
                dot1agCfmMdIndex,
                dot1agCfmMaIndex,
                dot1agCfmMepIdentifier,
                jnxSoamDmCfgIndex
     }
     ::= { jnxSoamDmMeasuredStatsTable 1 }

JnxSoamDmMeasuredStatsEntry ::= SEQUENCE {
     jnxSoamDmMeasuredStatsFrameDelayTwoWay Unsigned32,
     jnxSoamDmMeasuredStatsFrameDelayForward Unsigned32,
     jnxSoamDmMeasuredStatsFrameDelayBackward Unsigned32,
     jnxSoamDmMeasuredStatsIfdvTwoWay Unsigned32,
     jnxSoamDmMeasuredStatsIfdvForward Unsigned32,
     jnxSoamDmMeasuredStatsIfdvBackward Unsigned32
}

jnxSoamDmMeasuredStatsFrameDelayTwoWay OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the two-way frame delay calculated by this
          MEP from the last received SOAM PDU.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmMeasuredStatsEntry 1 }

jnxSoamDmMeasuredStatsFrameDelayForward OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the frame delay in the forward direction
          calculated by this MEP from the last received SOAM PDU. The value of this
          object may not be accurate in the absence of sufficiently precise clock
          synchronization.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmMeasuredStatsEntry 2 }

jnxSoamDmMeasuredStatsFrameDelayBackward OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the frame delay in the backward direction
          calculated by this MEP from the last received SOAM PDU. The value of this
          object may not be accurate in the absence of sufficiently precise clock
          synchronization.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmMeasuredStatsEntry 3 }

jnxSoamDmMeasuredStatsIfdvTwoWay OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the last two-way inter-frame delay
          interval calculated by this MEP.

          The value of this object is undefined when jnxSoamDmCfgType  
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmMeasuredStatsEntry 4 }

jnxSoamDmMeasuredStatsIfdvForward OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the last one-way inter-frame delay
          interval in the forward direction calculated by this MEP.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmMeasuredStatsEntry 5 }

jnxSoamDmMeasuredStatsIfdvBackward OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the last one-way inter-frame delay
          interval in the backward direction calculated by this MEP.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmMeasuredStatsEntry 6 }

-- *****************************************************************************
-- Ethernet Delay Measurement Current Statistic Table
-- *****************************************************************************

jnxSoamDmCurrentStatsTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamDmCurrentStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table contains the results for the current Measurement
          Interval in a SOAM Delay Measurement session gathered during the interval
          indicated by iterator count.

          A row in this table is created automatically
          by the SNMP Agent when the Delay Measurement session is configured.

          Each row in the table represents the current statistics for a Delay
          Measurement session for the defined MEP. This table uses four indices.
          The first three indices are the indices of the Maintenance Domain, MaNet,
          and MEP tables. The fourth index is the specific DM session on the
          selected MEP. There can be more than one DM session per MEP.

          The objects in this table apply regardless of the value of
          jnxSoamDmCfgType unless otherwise specified in the object description.
          Backward and two-way statistic objects are undefined if jnxSoamDmCfgType 
          is dm1DmRx.

          Except for jnxSoamDmCurrentStatsIndex, jnxSoamDmCurrentStatsStartTime
          jnxSoamDmCurrentStatsElapsedTime and jnxSoamDmCurrentStatsSuspect,
          each object in this table applies only if the corresponding bit is set in
          jnxSoamDmCfgMeasurementEnable.

          The objects in this table do not need to be persistent upon reboot or
          restart of a device.
          "
     REFERENCE
        "[MEF SOAM-PM] R7, R15, D9, D18"
     ::= { jnxSoamPmDmObjects 3 }

jnxSoamDmCurrentStatsEntry OBJECT-TYPE
     SYNTAX JnxSoamDmCurrentStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamDmCurrentStatsTable"
     INDEX {
                dot1agCfmMdIndex,
                dot1agCfmMaIndex,
                dot1agCfmMepIdentifier,
                jnxSoamDmCfgIndex
     }
     ::= { jnxSoamDmCurrentStatsTable 1 }

JnxSoamDmCurrentStatsEntry ::= SEQUENCE {
     jnxSoamDmCurrentStatsIndex Unsigned32,
     jnxSoamDmCurrentStatsStartTime DateAndTime,
     jnxSoamDmCurrentStatsElapsedTime TimeInterval,
     jnxSoamDmCurrentStatsSuspect TruthValue,
     jnxSoamDmCurrentStatsFrameDelayTwoWayMin Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayTwoWayMax Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayTwoWayAvg Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayForwardMin Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayForwardMax Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayForwardAvg Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayBackwardMin Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayBackwardMax Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayBackwardAvg Unsigned32,
     jnxSoamDmCurrentStatsIfdvForwardMin Unsigned32,
     jnxSoamDmCurrentStatsIfdvForwardMax Unsigned32,
     jnxSoamDmCurrentStatsIfdvForwardAvg Unsigned32,
     jnxSoamDmCurrentStatsIfdvBackwardMin Unsigned32,
     jnxSoamDmCurrentStatsIfdvBackwardMax Unsigned32,
     jnxSoamDmCurrentStatsIfdvBackwardAvg Unsigned32,
     jnxSoamDmCurrentStatsIfdvTwoWayMin Unsigned32,
     jnxSoamDmCurrentStatsIfdvTwoWayMax Unsigned32,
     jnxSoamDmCurrentStatsIfdvTwoWayAvg Unsigned32,
     jnxSoamDmCurrentStatsSoamPdusSent Gauge32,
     jnxSoamDmCurrentStatsSoamPdusReceived Gauge32,
     jnxSoamDmCurrentStatsFrameDelayRangeForwardMax Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayRangeForwardAvg Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayRangeBackwardMax Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayRangeBackwardAvg Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayRangeTwoWayMax Unsigned32,
     jnxSoamDmCurrentStatsFrameDelayRangeTwoWayAvg Unsigned32
}

jnxSoamDmCurrentStatsIndex OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The index for the current Measurement Interval for this
          PM session. This value will become the value for
          jnxSoamDmHistoryStatsIndex once the Measurement Interval
          is completed.

          Measurement Interval indexes are assigned sequentially by
          the SNMP Agent. The first Measurement Interval that occurs after
          the session is started is assigned index 1.
          "
     ::= { jnxSoamDmCurrentStatsEntry 1 }

jnxSoamDmCurrentStatsStartTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The time that the current Measurement Interval started.
          "
     REFERENCE
        "[MEF SOAM-PM] R22, R66, R100"
     ::= { jnxSoamDmCurrentStatsEntry 2 }

jnxSoamDmCurrentStatsElapsedTime OBJECT-TYPE
     SYNTAX TimeInterval
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The time that the current Measurement Interval has been running, in 0.01
          seconds.
          "
     REFERENCE
        "[MEF SOAM-PM] R24, R66, R100"
     ::= { jnxSoamDmCurrentStatsEntry 3 }

jnxSoamDmCurrentStatsSuspect OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "Whether the Measurement Interval has been marked as suspect.

          The object is to be set to false at the start of a measurement
          interval. It is set to true when there is a discontinuity in the
          performance measurements during the Measurement Interval. Conditions
          for a discontinuity include, but are not limited to the following:

          1 - The local time-of-day clock is adjusted by at least 10 seconds
          2 - The conducting of a performance measurement is halted before the
          current Measurement Interval is completed
          3 - A local test, failure, or reconfiguration that disrupts service
          "
     REFERENCE
        "[MEF SOAM-PM] R39, R40, R41"
     ::= { jnxSoamDmCurrentStatsEntry 4 }

jnxSoamDmCurrentStatsFrameDelayTwoWayMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum two-way frame delay
          calculated by this MEP for this Measurement Interval.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmCurrentStatsEntry 5 }

jnxSoamDmCurrentStatsFrameDelayTwoWayMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum two-way frame delay
          calculated by this MEP for this Measurement Interval.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmCurrentStatsEntry 6 }

jnxSoamDmCurrentStatsFrameDelayTwoWayAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average two-way frame delay
          calculated by this MEP for this Measurement Interval.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmCurrentStatsEntry 7 }

jnxSoamDmCurrentStatsFrameDelayForwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame delay
          in the forward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R67, R101"
     ::= { jnxSoamDmCurrentStatsEntry 8 }

jnxSoamDmCurrentStatsFrameDelayForwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame delay
          in the forward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R67, R101"
     ::= { jnxSoamDmCurrentStatsEntry 9 }

jnxSoamDmCurrentStatsFrameDelayForwardAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way frame delay
          in the forward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R67, R101"
     ::= { jnxSoamDmCurrentStatsEntry 10 }

jnxSoamDmCurrentStatsFrameDelayBackwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame delay
          in the backward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R67"
     ::= { jnxSoamDmCurrentStatsEntry 11 }

jnxSoamDmCurrentStatsFrameDelayBackwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame delay
          in the backward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
    REFERENCE
        "[MEF SOAM-PM] R67"
    ::= { jnxSoamDmCurrentStatsEntry 12 }

jnxSoamDmCurrentStatsFrameDelayBackwardAvg OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average one-way frame delay
         in the backward direction calculated by this MEP for this
         Measurement Interval. The value of this object may not be accurate
         in the absence of sufficiently precise clock synchronization.

         This object is undefined is jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
         "
     REFERENCE
        "[MEF SOAM-PM] R67"
     ::= { jnxSoamDmCurrentStatsEntry 13 }

jnxSoamDmCurrentStatsIfdvForwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way inter-frame delay
          interval in the forward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmCurrentStatsEntry 14 }

jnxSoamDmCurrentStatsIfdvForwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way inter-frame delay
          interval in the forward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmCurrentStatsEntry 15 }

jnxSoamDmCurrentStatsIfdvForwardAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way inter-frame delay
          interval in the forward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmCurrentStatsEntry 16 }

jnxSoamDmCurrentStatsIfdvBackwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way inter-frame delay
          interval in the backward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmCurrentStatsEntry 17 }

jnxSoamDmCurrentStatsIfdvBackwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way inter-frame delay
          interval in the backward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmCurrentStatsEntry 18 }

jnxSoamDmCurrentStatsIfdvBackwardAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way inter-frame delay
          interval in the backward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmCurrentStatsEntry 19 }

jnxSoamDmCurrentStatsIfdvTwoWayMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum two-way inter-frame delay
          interval calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when  jnxSoamDmCfgType
          is dm1DmTx or dm1DmRx.
          "
     ::= { jnxSoamDmCurrentStatsEntry 20 }

jnxSoamDmCurrentStatsIfdvTwoWayMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS  current
     DESCRIPTION
         "This object contains the maximum two-way inter-frame delay
          interval calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     ::= { jnxSoamDmCurrentStatsEntry 21 }

jnxSoamDmCurrentStatsIfdvTwoWayAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average two-way inter-frame delay
          interval calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     ::= { jnxSoamDmCurrentStatsEntry 22 }

jnxSoamDmCurrentStatsSoamPdusSent OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM PDUs sent
          during this Measurement  Interval.

          This object applies when  jnxSoamDmCfgType is dmDmm or dm1DmTx and
          is undefined if  jnxSoamDmCfgType is dm1DmRx. It indicates the
          number of DMM or 1DM SOAM frames transmitted.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmCurrentStatsEntry 23 }

jnxSoamDmCurrentStatsSoamPdusReceived OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM
          PDUs received in this Measurement Interval.

          This object indicates the number of DMR and 1DM SOAM frames
          received. This object applies when  jnxSoamDmCfgTypeis dmDmm or
          dm1DmRx and is undefined if  jnxSoamDmCfgTypeis dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmCurrentStatsEntry 24 }

jnxSoamDmCurrentStatsFrameDelayRangeForwardMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the maximum one-way frame delay range
        in the forward direction calculated by this MEP for this
        Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66, R100"
    ::= { jnxSoamDmCurrentStatsEntry 25 }

jnxSoamDmCurrentStatsFrameDelayRangeForwardAvg OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average one-way frame delay range
        in the forward direction calculated by this MEP for this
        Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66, R100"
    ::= { jnxSoamDmCurrentStatsEntry 26 }

jnxSoamDmCurrentStatsFrameDelayRangeBackwardMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the maximum one-way frame delay range
        in the backward direction calculated by this MEP for this
        Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66"
    ::= { jnxSoamDmCurrentStatsEntry 27 }

jnxSoamDmCurrentStatsFrameDelayRangeBackwardAvg OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average one-way frame delay range
        in the backward direction calculated by this MEP for this
        Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66"
    ::= { jnxSoamDmCurrentStatsEntry 28 }

jnxSoamDmCurrentStatsFrameDelayRangeTwoWayMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the maximum two-way frame delay range
        calculated by this MEP for this Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
    ::= { jnxSoamDmCurrentStatsEntry 29 }

jnxSoamDmCurrentStatsFrameDelayRangeTwoWayAvg OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average two-way frame delay range
        calculated by this MEP for this Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
    ::= { jnxSoamDmCurrentStatsEntry 30 }

-- *****************************************************************************
-- Ethernet Delay Measurement History Statistic Table
-- *****************************************************************************

jnxSoamDmHistoryStatsTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamDmHistoryStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table contains the results for history Measurement
          Intervals in a SOAM Delay Measurement session.

          Rows of this table are created automatically
          by the SNMP Agent when the Delay Measurement session is running and a
          Measurement Interval is completed.

          Each row in the table represents the Measurement Interval history
          statistics for a Delay Measurement session for the defined MEP. This
          table uses five indices. The first three indices are the indices of
          the Maintenance Domain, MaNet, and MEP tables. The fourth index is the
          specific DM session on the selected MEP. The fifth index is the
          Measurement Interval.

          At least 32 completed Measurement Intervals are to be supported. 96
          completed Measurement Intervals are recommended to be supported. If
          there are at least 32 rows in the table and a new Measurement Interval
          completes and a new row is to be added to the table, the oldest completed
          Measurement Interval can be deleted (row deletion). If the measurement
          interval is other than 15 minutes then a minimum of 8 hours of
          completed Measurement Intervals are to be supported and 24 hours are
          recommended to be supported.

          The objects in this table apply regardless of the value of
          jnxSoamDmCfgType unless otherwise specified in the object description.
          Backward and two-way statistic objects are undefined if jnxSoamDmCfgType 
          is dm1DmRx.

          Except for jnxSoamDmHistoryStatsIndex, jnxSoamDmHistoryStatsEndTime,
          jnxSoamDmHistoryStatsElapsedTime and jnxSoamDmHistoryStatsSuspect,
          each object in this table applies only if the corresponding bit is set in
          jnxSoamDmCfgMeasurementEnable.

          The rows and objects in this table are to be persistent upon reboot
          or restart of a device.
          "
     REFERENCE
        "[MEF SOAM-PM] R7, R15, R21, D8, R25"
     ::= { jnxSoamPmDmObjects 4 }

jnxSoamDmHistoryStatsEntry OBJECT-TYPE
     SYNTAX JnxSoamDmHistoryStatsEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamDmHistoryStatsTable"
     INDEX {
                dot1agCfmMdIndex,
                dot1agCfmMaIndex,
                dot1agCfmMepIdentifier,
                jnxSoamDmCfgIndex,
                jnxSoamDmHistoryStatsIndex
     }
     ::= { jnxSoamDmHistoryStatsTable 1 }

JnxSoamDmHistoryStatsEntry ::= SEQUENCE {
     jnxSoamDmHistoryStatsIndex Unsigned32,
     jnxSoamDmHistoryStatsEndTime DateAndTime,
     jnxSoamDmHistoryStatsElapsedTime TimeInterval,
     jnxSoamDmHistoryStatsSuspect TruthValue,
     jnxSoamDmHistoryStatsFrameDelayTwoWayMin Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayTwoWayMax Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayTwoWayAvg Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayForwardMin Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayForwardMax Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayForwardAvg Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayBackwardMin Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayBackwardMax Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayBackwardAvg Unsigned32,
     jnxSoamDmHistoryStatsIfdvForwardMin Unsigned32,
     jnxSoamDmHistoryStatsIfdvForwardMax Unsigned32,
     jnxSoamDmHistoryStatsIfdvForwardAvg Unsigned32,
     jnxSoamDmHistoryStatsIfdvBackwardMin Unsigned32,
     jnxSoamDmHistoryStatsIfdvBackwardMax Unsigned32,
     jnxSoamDmHistoryStatsIfdvBackwardAvg Unsigned32,
     jnxSoamDmHistoryStatsIfdvTwoWayMin Unsigned32,
     jnxSoamDmHistoryStatsIfdvTwoWayMax Unsigned32,
     jnxSoamDmHistoryStatsIfdvTwoWayAvg Unsigned32,
     jnxSoamDmHistoryStatsSoamPdusSent Gauge32,
     jnxSoamDmHistoryStatsSoamPdusReceived Gauge32,
     jnxSoamDmHistoryStatsFrameDelayRangeForwardMax Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayRangeForwardAvg Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayRangeBackwardMax Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayRangeBackwardAvg Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayRangeTwoWayMax Unsigned32,
     jnxSoamDmHistoryStatsFrameDelayRangeTwoWayAvg Unsigned32
}

jnxSoamDmHistoryStatsIndex OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The index for the Measurement Interval within this
          PM session.

          Measurement Interval indexes are assigned sequentially by
          the SNMP Agent. The first Measurement Interval that occurs after
          the session is started is assigned index 1.

          Referential integrity is necessary, i.e., the index needs to be
          persistent upon a reboot or restart of a device. The index
          is never reused while this session is active until it wraps to zero.
          The index value keeps increasing up to that time.
          "
     ::= { jnxSoamDmHistoryStatsEntry 1 }

jnxSoamDmHistoryStatsEndTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The time that the Measurement Interval ended.
          "
     REFERENCE
        "[MEF SOAM-PM] R23, R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 2 }

jnxSoamDmHistoryStatsElapsedTime OBJECT-TYPE
     SYNTAX TimeInterval
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The length of time that the Measurement Interval ran for,
          in 0.01 seconds.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 3 }

jnxSoamDmHistoryStatsSuspect OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "Whether the Measurement Interval has been marked as suspect.

          The object is set to true when there is a discontinuity in the
          performance measurements during the Measurement Interval. Conditions
          for a discontinuity include, but are not limited to the following:

          1 - The local time-of-day clock is adjusted by at least 10 seconds
          2 - The conducting of a performance measurement is halted before the
          current Measurement Interval is completed
          3 - A local test, failure, or reconfiguration that disrupts service
          "
     REFERENCE
        "[MEF SOAM-PM] R39, R40, R41, R42"
     ::= { jnxSoamDmHistoryStatsEntry 4 }

jnxSoamDmHistoryStatsFrameDelayTwoWayMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum two-way frame delay
          calculated by this MEP for this Measurement Interval.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 5 }

jnxSoamDmHistoryStatsFrameDelayTwoWayMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum two-way frame delay
          calculated by this MEP for this Measurement Interval.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 6 }

jnxSoamDmHistoryStatsFrameDelayTwoWayAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average two-way frame delay
          calculated by this MEP for this Measurement Interval.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 7 }

jnxSoamDmHistoryStatsFrameDelayForwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame delay
          in the forward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 8 }

jnxSoamDmHistoryStatsFrameDelayForwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame delay
          in the forward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 9 }

jnxSoamDmHistoryStatsFrameDelayForwardAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way frame delay
          in the forward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 10 }

jnxSoamDmHistoryStatsFrameDelayBackwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way frame delay
          in the backward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 11 }

jnxSoamDmHistoryStatsFrameDelayBackwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way frame delay
          in the backward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 12 }

jnxSoamDmHistoryStatsFrameDelayBackwardAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way frame delay
          in the backward direction calculated by this MEP for this
          Measurement Interval. The value of this object may not be accurate
          in the absence of sufficiently precise clock synchronization.

          This object is undefined is  jnxSoamDmCfgType is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 13 }

jnxSoamDmHistoryStatsIfdvForwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way inter-frame delay
          interval in the forward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 14 }

jnxSoamDmHistoryStatsIfdvForwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way inter-frame delay
          interval in the forward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 15 }

jnxSoamDmHistoryStatsIfdvForwardAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way inter-frame delay
          interval in the forward direction calculated by this MEP for this
          Measurement Interval.
          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 16 }

jnxSoamDmHistoryStatsIfdvBackwardMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum one-way inter-frame delay
          interval in the backward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 17 }

jnxSoamDmHistoryStatsIfdvBackwardMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum one-way inter-frame delay
          interval in the backward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 18 }

jnxSoamDmHistoryStatsIfdvBackwardAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average one-way inter-frame delay
          interval in the backward direction calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66"
     ::= { jnxSoamDmHistoryStatsEntry 19 }

jnxSoamDmHistoryStatsIfdvTwoWayMin OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the minimum two-way inter-frame delay
          interval calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     ::= { jnxSoamDmHistoryStatsEntry 20 }

jnxSoamDmHistoryStatsIfdvTwoWayMax OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the maximum two-way inter-frame delay
          interval calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     ::= { jnxSoamDmHistoryStatsEntry 21 }

jnxSoamDmHistoryStatsIfdvTwoWayAvg OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the average two-way inter-frame delay
          interval calculated by this MEP for this
          Measurement Interval.

          The value of this object is undefined when jnxSoamDmCfgType 
          is dm1DmTx or dm1DmRx.
          "
     ::= { jnxSoamDmHistoryStatsEntry 22 }

jnxSoamDmHistoryStatsSoamPdusSent OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM PDUs sent
          during this Measurement Interval.

          This object applies when  jnxSoamDmCfgType is dmDmm or dm1DmTx and
          is undefined if  jnxSoamDmCfgType is dm1DmRx. It indicates the
          number of DMM or 1DM SOAM frames transmitted.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 23 }

jnxSoamDmHistoryStatsSoamPdusReceived OBJECT-TYPE
     SYNTAX Gauge32
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object contains the count of the number of SOAM
          PDUs received in this Measurement Interval.

          This object indicates the number of DMR and 1DM SOAM frames
          received. This object applies when  jnxSoamDmCfgType is dmDmm or
          dm1DmRx and is undefined if  jnxSoamDmCfgType is dm1DmTx.
          "
     REFERENCE
        "[MEF SOAM-PM] R66, R100"
     ::= { jnxSoamDmHistoryStatsEntry 24 }

jnxSoamDmHistoryStatsFrameDelayRangeForwardMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the maximum one-way Frame Delay Range
        in the forward direction calculated by this MEP for this
        Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66, R100"
    ::= { jnxSoamDmHistoryStatsEntry 25 }

jnxSoamDmHistoryStatsFrameDelayRangeForwardAvg OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average one-way Frame Delay Range
        in the forward direction calculated by this MEP for this
        Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66, R100"
    ::= { jnxSoamDmHistoryStatsEntry 26 }

jnxSoamDmHistoryStatsFrameDelayRangeBackwardMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the maximum one-way Frame Delay Range
        in the backward direction calculated by this MEP for this
        Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66"
    ::= { jnxSoamDmHistoryStatsEntry 27 }

jnxSoamDmHistoryStatsFrameDelayRangeBackwardAvg OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average one-way Frame Delay Range
        in the backward direction calculated by this MEP for this
        Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
    REFERENCE
        "[MEF SOAM-PM] R66"
    ::= { jnxSoamDmHistoryStatsEntry 28 }

jnxSoamDmHistoryStatsFrameDelayRangeTwoWayMax OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the maximum two-way Frame Delay Range
        calculated by this MEP for this Measurement Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
        ::= { jnxSoamDmHistoryStatsEntry 29 }

jnxSoamDmHistoryStatsFrameDelayRangeTwoWayAvg OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average two-way Frame Delay Range
        Interval.

        The value of this object is undefined when jnxSoamDmCfgType
        is dm1DmTx or dm1DmRx.
        "
    ::= { jnxSoamDmHistoryStatsEntry 30 }

-- *****************************************************************************
-- Performance Measurement Loss Threshold Configuration Table
-- *****************************************************************************

jnxSoamLmThresholdCfgTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamLmThresholdCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table contains the list of Loss Measurement configuration threshold
          values for LM Performance Monitoring.
          The main purpose of the threshold configuration table is to configure
          threshold alarm notifications indicating that a specific performance
          metric is not being met.

          Each row in the table represents a Loss Measurement session threshold
          set for the defined MEP. This table uses five indices. The first three
          indices are the indices of the Maintenance Domain, MaNet, and MEP tables.
          The fourth index is the specific LM session on the selected MEP. The
          fifth index is the specific threshold set number.

          Rows in this table are not created automatically. A row is created in
          this table to set up a threshold set on a configured MEP that has a
          configured LM session.

          If two managers try to 'create' the same row at the same time, the first
          creation would succeed, the second creation attempt would result in an
          error. The second creation attempt would then need to select a new index
          value to successfully create a new row.

          An NE needs to support at least one threshold set for NE SOAM PM compliance. A
          second threshold set on the NE is desirable. More than two threshold
          sets can be configured on the NE if supported on the NE.

          All the objects in the row have a default value that disables the
          particular threshold measurement. In order to enable a threshold
          measurement the particular bit in the jnxSoamLmThresholdCfgEnable object
          is to be set to '1' and the selected threshold measurement is to have
          a threshold value configured. Non-configured threshold measurements
          are disabled by default.

          The writable objects in this table need to be persistent upon reboot
          or restart of a device.
          "
     ::= { jnxSoamPmLmObjects 5 }

jnxSoamLmThresholdCfgEntry OBJECT-TYPE
     SYNTAX JnxSoamLmThresholdCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamLmThresholdCfgTable."
     INDEX {
                dot1agCfmMdIndex,
                dot1agCfmMaIndex,
                dot1agCfmMepIdentifier,
                jnxSoamLmCfgIndex,
                jnxSoamLmThresholdCfgIndex
     }
     ::= {jnxSoamLmThresholdCfgTable 1 }

JnxSoamLmThresholdCfgEntry ::= SEQUENCE {
     jnxSoamLmThresholdCfgIndex Unsigned32,
     jnxSoamLmThresholdCfgEnable BITS,
     jnxSoamLmThresholdCfgAvgFlrForwardThreshold Unsigned32,
     jnxSoamLmThresholdCfgAvgFlrBackwardThreshold Unsigned32,
     jnxSoamLmThresholdCfgRowStatus RowStatus
}

jnxSoamLmThresholdCfgIndex OBJECT-TYPE
     SYNTAX Unsigned32(0..4294967295)
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The index of the threshold number for the specific LM
          threshold entry.

          An index value of '1' needs to be supported. Other index values
          can also be supported.
          "
     ::= { jnxSoamLmThresholdCfgEntry 1 }

jnxSoamLmThresholdCfgEnable OBJECT-TYPE
     SYNTAX BITS {
                bJnxSoamLmMeasuredFlrForwardThreshold(0),
                bJnxSoamLmMaxFlrForwardThreshold(1),
                bJnxSoamLmAvgFlrForwardThreshold(2),
                bJnxSoamLmMeasuredFlrBackwardThreshold(3),
                bJnxSoamLmMaxFlrBackwardThreshold(4),
                bJnxSoamLmAvgFlrBackwardThreshold(5)
     }
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "A vector of bits that indicates the type of SOAM LM thresholds
          notifications that are enabled.

          A bit set to '1' enables the specific SOAM LM threshold notification
          and when the specific counter is enabled and the threshold is crossed a
          notification is generated.

          A bit set to '0' disables the specific SOAM LM threshold notification.

          If a particular SOAM LM threshold is not supported the BIT value is
          set to '0'.

          bJnxSoamLmMeasuredFlrForwardThreshold(0)
          Enables/disables measured frame loss forward ratio threshold
          notification. The notification is sent immediately when the
          jnxSoamLmMeasuredStatsForwardFlr value is
          greater than or equal to the threshold value.
          bJnxSoamLmMaxFlrForwardThreshold(1)
          Enables/disables maximum frame loss forward ratio threshold
          notification. The notification is sent immediately when the
          jnxSoamLmCurrentStatsForwardMaxFlr value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamLmAvgFlrForwardThreshold(2)
          Enables/disables average frame loss forward ratio threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamLmCurrentStatsForwardAvgFlr value is greater
          than or equal to the threshold value.
          bJnxSoamLmMeasuredFlrBackwardThreshold(3)
          Enables/disables measured frame loss backward ratio threshold
          notification. The notification is sent immediately when the
          jnxSoamLmMeasuredStatsBackwardFlr value is
          greater than or equal to the threshold value.
          bJnxSoamLmMaxFlrBackwardThreshold(4)
          Enables/disables maximum frame loss backward ratio threshold
          notification. The notification is sent immediately when the
          jnxSoamLmCurrentStatsBackwardMaxFlr value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamLmAvgFlrBackwardThreshold(5)
          Enables/disables average frame loss backward ratio threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamLmCurrentStatsBackwardAvgFlr value is
          greater than or equal to the threshold value.
          "
     DEFVAL { { } }
     ::= { jnxSoamLmThresholdCfgEntry 2 }

jnxSoamLmThresholdCfgAvgFlrForwardThreshold OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object is used to set the average forward frame loss ratio
          threshold value that will be used to determine if a threshold
          notification is generated.
          "
     DEFVAL { 100000 }
     ::= { jnxSoamLmThresholdCfgEntry 3 }

jnxSoamLmThresholdCfgAvgFlrBackwardThreshold OBJECT-TYPE
     SYNTAX Unsigned32 (0..100000)
     UNITS "milli-percent"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object is used to set the average backward frame loss ratio
          threshold value that will be used to determine if a threshold
          notification is generated.
          "
     DEFVAL { 100000 }
     ::= { jnxSoamLmThresholdCfgEntry 4 }

jnxSoamLmThresholdCfgRowStatus OBJECT-TYPE
     SYNTAX RowStatus
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The status of the row.

          The writable columns in a row cannot be changed if the row
          is active. All columns are to have a valid value before a row
          can be activated.
          "
     ::= { jnxSoamLmThresholdCfgEntry 5 }

-- *****************************************************************************
-- Ethernet Loss Measurement Current Availability Statistic Table
-- *****************************************************************************

jnxSoamLmCurrentAvailStatsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxSoamLmCurrentAvailStatsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
       "This object contains the current results for a SOAM Loss Measurement
        session for availability statistics gathered during the interval
        indicated by jnxSoamLmCfgAvailabilityMeasurementInterval.

        Each row in the table represents a Loss Measurement session for
        the defined MEP. This table uses four indices. The first three indices
        are the indices of the Maintenance Domain, MaNet, and MEP tables. The
        fourth index is the specific LM session on the selected MEP.

        Instances of this managed object are created automatically
        by the SNMP Agent when the Loss Measurement session is running.

        The objects in this table apply regardless of the value of
        jnxSoamLmCfgType unless otherwise specified in the object description.

        Except for jnxSoamLmCurrentAvailStatsIndex,
        jnxSoamLmCurrentAvailStatsStartTime, jnxSoamLmCurrentAvailStatsElapsedTime
        and jnxSoamLmCurrentAvailStatsSuspect, each object in this table applies
        only if the corresponding bit is set in jnxSoamLmCfgMeasurementEnable.

        The objects in this table may be persistent upon reboot or restart
        of a device.
       "
    REFERENCE
        "[MEF SOAM-PM] D9, D18"
    ::= { jnxSoamPmLmObjects 6 }

jnxSoamLmCurrentAvailStatsEntry OBJECT-TYPE
    SYNTAX JnxSoamLmCurrentAvailStatsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
       "The conceptual row of jnxSoamLmCurrentAvailStatsTable"
    INDEX {
        dot1agCfmMdIndex,
        dot1agCfmMaIndex,
        dot1agCfmMepIdentifier,
        jnxSoamLmCfgIndex
    }
    ::= { jnxSoamLmCurrentAvailStatsTable 1 }

JnxSoamLmCurrentAvailStatsEntry ::= SEQUENCE {
     jnxSoamLmCurrentAvailStatsIndex Unsigned32,
     jnxSoamLmCurrentAvailStatsStartTime DateAndTime,
     jnxSoamLmCurrentAvailStatsElapsedTime TimeInterval,
     jnxSoamLmCurrentAvailStatsSuspect TruthValue,
   
     jnxSoamLmCurrentAvailStatsForwardHighLoss Unsigned32,
     jnxSoamLmCurrentAvailStatsBackwardHighLoss Unsigned32,
     jnxSoamLmCurrentAvailStatsForwardConsecutiveHighLoss Unsigned32,
     jnxSoamLmCurrentAvailStatsBackwardConsecutiveHighLoss Unsigned32,
   
     jnxSoamLmCurrentAvailStatsForwardAvailable Gauge32,
     jnxSoamLmCurrentAvailStatsBackwardAvailable Gauge32,
     jnxSoamLmCurrentAvailStatsForwardUnavailable Gauge32,
     jnxSoamLmCurrentAvailStatsBackwardUnavailable Gauge32,
     jnxSoamLmCurrentAvailStatsForwardMinFlr Unsigned32,
     jnxSoamLmCurrentAvailStatsForwardMaxFlr Unsigned32,
     jnxSoamLmCurrentAvailStatsForwardAvgFlr Unsigned32,
     jnxSoamLmCurrentAvailStatsBackwardMinFlr Unsigned32,
     jnxSoamLmCurrentAvailStatsBackwardMaxFlr Unsigned32,
     jnxSoamLmCurrentAvailStatsBackwardAvgFlr Unsigned32
}

jnxSoamLmCurrentAvailStatsIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "The index for the current availability Measurement Interval for this
        PM session. This value will become the value for
        jnxSoamLmHistoryAvailStatsIndex once the Measurement Interval
        is completed. The duration of the Measurement Interval is specified
        by jnxSoamLmCfgAvailabilityMeasurementInterval.

        Measurement Interval indexes are assigned sequentially by
        the SNMP Agent. The first Measurement Interval that occurs after
        the session is started is assigned index 1.
       "
    ::= { jnxSoamLmCurrentAvailStatsEntry 1 }

jnxSoamLmCurrentAvailStatsStartTime OBJECT-TYPE
    SYNTAX DateAndTime
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "The time that the current Measurement Interval started.
       "
    REFERENCE
       "[MEF SOAM-PM] R87, R112"
    ::= { jnxSoamLmCurrentAvailStatsEntry 2 }

jnxSoamLmCurrentAvailStatsElapsedTime OBJECT-TYPE
    SYNTAX TimeInterval
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "The time that the current Measurement Interval has been running, in 0.01
        seconds.
       "
    REFERENCE
        "[MEF SOAM-PM] R24, R87, R112"
    ::= { jnxSoamLmCurrentAvailStatsEntry 3 }

jnxSoamLmCurrentAvailStatsSuspect OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "Whether the Measurement Interval has been marked as suspect.

        The object is set to false at the start of a measurement
        interval. It is set to true when there is a discontinuity in the
        performance measurements during the Measurement Interval. Conditions
        for a discontinuity include, but are not limited to the following:

        1 - The local time-of-day clock is adjusted by at least 10 seconds
        2 - The conducting of a performance measurement is halted before the
            current Measurement Interval is completed
        3 - A local test, failure, or reconfiguration that disrupts service
       "
    REFERENCE
        "[MEF SOAM-PM] R39, R40, R41"
    ::= { jnxSoamLmCurrentAvailStatsEntry 4 }

jnxSoamLmCurrentAvailStatsForwardHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of high loss intervals (HLI) over
        time in the forward direction.

        The value starts at 0 and increments for every HLI that occurs.
        This parameter is equivalent to 'L Sub T' found in MEF 10.2.1.
       "
    REFERENCE
        "[MEF 10.2.1] 6.9.9; [MEF SOAM-PM] R87"
    ::= { jnxSoamLmCurrentAvailStatsEntry 5 }

jnxSoamLmCurrentAvailStatsBackwardHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of high loss intervals (HLI) over
        time in the backwards direction.

        The value starts at 0 and increments for every HLI that occurs.
        This parameter is equivalent to 'L Sub T' found in MEF 10.2.1.
       "
    REFERENCE
        "[MEF 10.2.1] 6.9.9; [MEF SOAM-PM] R87"
    ::= { jnxSoamLmCurrentAvailStatsEntry 6 }

jnxSoamLmCurrentAvailStatsForwardConsecutiveHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of consecutive high loss intervals
        (CHLI) over time in the forward direction.

        The value starts at 0 and increments for every HLI that occurs
        that is determined to fall within a CHLI.
        This parameter is equivalent to 'B Sub T' found in MEF 10.2.1.
       "
    REFERENCE
        "[MEF 10.2.1] 6.9.9; [MEF SOAM-PM] R87"
    ::= { jnxSoamLmCurrentAvailStatsEntry 7 }

jnxSoamLmCurrentAvailStatsBackwardConsecutiveHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of consecutive high loss intervals
        (CHLI) over time in the backward direction.

         The value starts at 0 and increments for every HLI that occurs
         that is determined to fall within a CHLI.
         This parameter is equivalent to 'B Sub T' found in MEF 10.2.1.
        "
    REFERENCE
        "[MEF 10.2.1] 6.9.9; [MEF SOAM-PM] R87"
    ::= { jnxSoamLmCurrentAvailStatsEntry 8 }

jnxSoamLmCurrentAvailStatsForwardAvailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as available in the forward direction by this MEP during
        this Measurement Interval.
       "
    REFERENCE
        "[MEF SOAM-PM] R87; [MEF 10.2.1]"
    ::= { jnxSoamLmCurrentAvailStatsEntry 9 }

jnxSoamLmCurrentAvailStatsBackwardAvailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as available in the backward direction by this MEP during
        this Measurement Interval.
       "
    REFERENCE
        "[MEF SOAM-PM] R87"
    ::= { jnxSoamLmCurrentAvailStatsEntry 10 }

jnxSoamLmCurrentAvailStatsForwardUnavailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as unavailable in the forward direction by this MEP during
        this Measurement Interval.
       "
    REFERENCE
        "[MEF SOAM-PM] R87"
    ::= { jnxSoamLmCurrentAvailStatsEntry 11 }

jnxSoamLmCurrentAvailStatsBackwardUnavailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as unavailable in the backward direction by this MEP
        during this Measurement Interval.
       "
    REFERENCE
        "[MEF SOAM-PM] R87"
    ::= { jnxSoamLmCurrentAvailStatsEntry 12 }

jnxSoamLmCurrentAvailStatsForwardMinFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the minimum one-way availability flr in the forward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 2626 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmCurrentAvailStatsEntry 13 }

jnxSoamLmCurrentAvailStatsForwardMaxFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the maximum one-way availability flr in the forward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmCurrentAvailStatsEntry 14 }

jnxSoamLmCurrentAvailStatsForwardAvgFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the average one-way availability flr in the forward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmCurrentAvailStatsEntry 15 }

jnxSoamLmCurrentAvailStatsBackwardMinFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the minimum one-way availability flr in the backward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmCurrentAvailStatsEntry 16 }

jnxSoamLmCurrentAvailStatsBackwardMaxFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the maximum one-way availability flr in the backward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed
        as a percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmCurrentAvailStatsEntry 17 }

jnxSoamLmCurrentAvailStatsBackwardAvgFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the average one-way availability flr in the backward
         direction, from among the set of availability flr values calculated by
         the MEP in this Measurement Interval. There is one availability flr
         value for each 'delta_t' time period within the Measurement Interval, as
         specified in MEF 10.2.1.

         The flr value is a ratio that is expressed as a
         percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

         Units are in milli-percent, where 1 indicates 0.001 percent.
        "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmCurrentAvailStatsEntry 18 }

-- *****************************************************************************
-- Ethernet Loss Measurement Availability History Statistic Table
-- *****************************************************************************

jnxSoamLmHistoryAvailStatsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxSoamLmHistoryAvailStatsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
       "This table contains the results for availability history Measurement
        Intervals in a SOAM Loss Measurement session.

        Rows of this table object are created automatically
        by the SNMP Agent when the Loss Measurement session is running and a
        Measurement Interval is completed.

        Each row in the table represents the history statistics for a Loss
        Measurement session availability Measurement Interval for the defined
        MEP. This table uses five indices. The first three indices are the indices
        of the Maintenance Domain, MaNet, and MEP tables. The fourth index is the
        specific LM session on the selected MEP. The fifth index index the
        specific Measurement Interval.

        At least 32 completed Measurement Intervals are to be supported. 96
        completed Measurement Intervals are recommended to be supported. If
        there are at least 32 rows in the table and a new Measurement Interval
        completes and a new row is to be added to the table, the oldest completed
        Measurement Interval can be deleted (row deletion). If the availability
        Measurement Interval is other than 15 minutes then a minimum of 8 hours of
        completed Measurement Intervals are to be supported and 24 hours are
        recommended to be supported.

        Except for jnxSoamLmHistoryAvailStatsIndex,
        jnxSoamLmHistoryAvailStatsEndTime, jnxSoamLmHistoryAvailStatsElapsedTime and
        jnxSoamLmHistoryAvailStatsSuspect, each object in this table applies only
        if the corresponding bit is set in jnxSoamLmCfgMeasurementEnable.

        The rows and objects in this table are to be persistent upon reboot
        or restart of a device.
       "
    REFERENCE
       "[MEF SOAM-PM] R7, R15, R21, D8, R25"
     ::= { jnxSoamPmLmObjects 7 }

jnxSoamLmHistoryAvailStatsEntry OBJECT-TYPE
    SYNTAX JnxSoamLmHistoryAvailStatsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
       "The conceptual row of jnxSoamLmHistoryAvailStatsTable"
    INDEX {
        dot1agCfmMdIndex,
        dot1agCfmMaIndex,
        dot1agCfmMepIdentifier,
        jnxSoamLmCfgIndex,
        jnxSoamLmHistoryAvailStatsIndex
    }
   ::= { jnxSoamLmHistoryAvailStatsTable 1 }
   
JnxSoamLmHistoryAvailStatsEntry ::= SEQUENCE {
    jnxSoamLmHistoryAvailStatsIndex Unsigned32,
    jnxSoamLmHistoryAvailStatsEndTime DateAndTime,
    jnxSoamLmHistoryAvailStatsElapsedTime TimeInterval,
    jnxSoamLmHistoryAvailStatsSuspect TruthValue,
    jnxSoamLmHistoryAvailStatsForwardHighLoss Unsigned32,
    jnxSoamLmHistoryAvailStatsBackwardHighLoss Unsigned32,
    jnxSoamLmHistoryAvailStatsForwardConsecutiveHighLoss Unsigned32,
    jnxSoamLmHistoryAvailStatsBackwardConsecutiveHighLoss Unsigned32,

    jnxSoamLmHistoryAvailStatsForwardAvailable Gauge32,
    jnxSoamLmHistoryAvailStatsBackwardAvailable Gauge32,
    jnxSoamLmHistoryAvailStatsForwardUnavailable Gauge32,
    jnxSoamLmHistoryAvailStatsBackwardUnavailable Gauge32,
    jnxSoamLmHistoryAvailStatsForwardMinFlr Unsigned32,
    jnxSoamLmHistoryAvailStatsForwardMaxFlr Unsigned32,
    jnxSoamLmHistoryAvailStatsForwardAvgFlr Unsigned32,
    jnxSoamLmHistoryAvailStatsBackwardMinFlr Unsigned32,
    jnxSoamLmHistoryAvailStatsBackwardMaxFlr Unsigned32,
    jnxSoamLmHistoryAvailStatsBackwardAvgFlr Unsigned32
}

jnxSoamLmHistoryAvailStatsIndex OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
       "The index for the availability Measurement Interval within this
        PM session.

        Measurement Interval indexes are assigned sequentially by
        the SNMP Agent. The first Measurement Interval that occurs after
        the session is started is assigned index 1. Measurement Intervals
        for availability (stored in this table) are based on
        jnxSoamLmCfgAvailabilityMeasurementInterval and are indexed independently
        of Measurement Intervals for FLR (stored in jnxSoamLmHistoryStatsTable).

        Referential integrity is necessary, i.e., the index needs to be
        persistent upon a reboot or restart of a device. The index
        is never reused while this session is active until it wraps to zero.
        The index value keeps increasing up to that time.
       "
    ::= { jnxSoamLmHistoryAvailStatsEntry 1 }

jnxSoamLmHistoryAvailStatsEndTime OBJECT-TYPE
    SYNTAX DateAndTime
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "The time that the Measurement Interval ended.
       "
    REFERENCE
        "[MEF SOAM-PM] R23, R87, R112"
    ::= { jnxSoamLmHistoryAvailStatsEntry 2 }

jnxSoamLmHistoryAvailStatsElapsedTime OBJECT-TYPE
    SYNTAX TimeInterval
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "The length of time that the Measurement Interval ran for,
        in 0.01 seconds.
       "
    REFERENCE
        "[MEF SOAM-PM] R24, R87, R112"
    ::= { jnxSoamLmHistoryAvailStatsEntry 3 }

jnxSoamLmHistoryAvailStatsSuspect OBJECT-TYPE
    SYNTAX TruthValue
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "Whether the Measurement Interval has been marked as suspect.

        The object is set to true when there is a discontinuity in the
        performance measurements during the Measurement Interval. Conditions
        for a discontinuity include, but are not limited to the following:

        1 - The local time-of-day clock is adjusted by at least 10 seconds
        2 - The conducting of a performance measurement is halted before the
            current Measurement Interval is completed
        3 - A local test, failure, or reconfiguration that disrupts service
       "
    REFERENCE
        "[MEF SOAM-PM] R39, R40, R41, R42"
    ::= { jnxSoamLmHistoryAvailStatsEntry 4 }

jnxSoamLmHistoryAvailStatsForwardHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of high loss intervals (HLI) over
        time in the forward direction.

        The value starts at 0 and increments for every HLI that occurs.
        This parameter is equivalent to 'L Sub T' found in MEF 10.2.1.
       "
    REFERENCE
        "[MEF 10.2.1] 6.9.9; [MEF SOAM-PM] R87"
    ::= { jnxSoamLmHistoryAvailStatsEntry 5 }

jnxSoamLmHistoryAvailStatsBackwardHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of high loss intervals (HLI) over
        time in the backward direction.

        The value starts at 0 and increments for every HLI that occurs.
        This parameter is equivalent to 'L Sub T' found in MEF 10.2.1.
       "
    REFERENCE
       "[MEF 10.2.1] 6.9.9; [MEF SOAM-PM] R87"
    ::= { jnxSoamLmHistoryAvailStatsEntry 6 }

jnxSoamLmHistoryAvailStatsForwardConsecutiveHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of consecutive high loss intervals
        (CHLI) over time in the forward direction.

        The value starts at 0 and increments for every HLI that occurs
        that is determined to fall within a CHLI.
        This parameter is equivalent to 'B Sub T' found in MEF 10.2.1.
       "
    REFERENCE
       "[MEF 10.2.1] 6.9.9; 3266 [MEF SOAM-PM] R87"
::= { jnxSoamLmHistoryAvailStatsEntry 7 }

jnxSoamLmHistoryAvailStatsBackwardConsecutiveHighLoss OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object is the number of consecutive high loss intervals
        (CHLI) over time in the forward direction.

        The value starts at 0 and increments for every HLI that occurs
        that is determined to fall within a CHLI.
        This parameter is equivalent to 'B Sub T' found in MEF 10.2.1.
       "
    REFERENCE
        "[MEF 10.2.1] 6.9.9; [MEF SOAM-PM] R87"
    ::= { jnxSoamLmHistoryAvailStatsEntry 8 }

jnxSoamLmHistoryAvailStatsForwardAvailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as available in the forward direction by this MEP during
        this Measurement Interval.
       "
    REFERENCE
        "[MEF SOAM-PM] R87; [MEF 10.2.1]"
    ::= { jnxSoamLmHistoryAvailStatsEntry 9 }

jnxSoamLmHistoryAvailStatsBackwardAvailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as available in the backward direction by this MEP during
        this Measurement Interval.
       "
    REFERENCE
        "[MEF SOAM-PM] R87"
    ::= { jnxSoamLmHistoryAvailStatsEntry 10 }

jnxSoamLmHistoryAvailStatsForwardUnavailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as unavailable in the forward direction by this MEP during
        this Measurement Interval.
       "
    REFERENCE
        "[MEF SOAM-PM] R87"
    ::= { jnxSoamLmHistoryAvailStatsEntry 11 }


jnxSoamLmHistoryAvailStatsBackwardUnavailable OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the number of availability indicators
        evaluated as unavailable in the backward direction by this MEP
        during this Measurement Interval.
       "
    REFERENCE
       "[MEF SOAM-PM] R87"
    ::= { jnxSoamLmHistoryAvailStatsEntry 12 }

jnxSoamLmHistoryAvailStatsForwardMinFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the minimum one-way availability flr in the forward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
       "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmHistoryAvailStatsEntry 13 }

jnxSoamLmHistoryAvailStatsForwardMaxFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the maximum one-way availability flr in the forward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.
    
        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).
    
        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmHistoryAvailStatsEntry 14 }
    
jnxSoamLmHistoryAvailStatsForwardAvgFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the average one-way availability flr in the forward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.
    
        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).
        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmHistoryAvailStatsEntry 15 }

jnxSoamLmHistoryAvailStatsBackwardMinFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the minimum one-way availability flr in the backward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmHistoryAvailStatsEntry 16 }

jnxSoamLmHistoryAvailStatsBackwardMaxFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the maximum one-way availability flr in the backward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed
        as a percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmHistoryAvailStatsEntry 17 }

jnxSoamLmHistoryAvailStatsBackwardAvgFlr OBJECT-TYPE
    SYNTAX Unsigned32 (0..100000)
    UNITS "milli-percent"
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
       "This object contains the average one-way availability flr in the backward
        direction, from among the set of availability flr values calculated by
        the MEP in this Measurement Interval. There is one availability flr
        value for each 'delta_t' time period within the Measurement Interval, as
        specified in MEF 10.2.1.

        The flr value is a ratio that is expressed as a
        percent with a value of 0 (ratio 0.00) through 100000 (ratio 1.00).

        Units are in milli-percent, where 1 indicates 3458 0.001 percent.
       "
    REFERENCE
        "[MEF SOAM-PM] D37"
    ::= { jnxSoamLmHistoryAvailStatsEntry 18 }


-- *****************************************************************************
-- Performance Measurement Delay Threshold Configuration Table
-- *****************************************************************************

jnxSoamDmThresholdCfgTable OBJECT-TYPE
     SYNTAX SEQUENCE OF JnxSoamDmThresholdCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "This table contains the list of Delay Measurement threshold configuration
          values for DM Performance Monitoring.

          The main purpose of the threshold configuration table is to configure
          threshold alarm notifications indicating that a specific performance
          metric is not being met.

          Each row in the table represents a Delay Measurement session threshold
          set for the defined MEP. This table uses five indices. The first three
          indices are the indices of the Maintenance Domain, MaNet, and MEP tables.
          The fourth index is the specific DM session on the selected MEP. The
          fifth index is the specific threshold set number.

          Rows in this table are not created automatically. A row is created in
          this table to set up a threshold set on a configured MEP that has a
          configured DM session.

          An NE needs to support at least one threshold set for NE SOAM PM compliance. A
          second threshold set on the NE is desirable. More than two threshold
          sets on the NE can be configured if supported on the NE.

          All the objects in the row have a default value that disables the
          particular threshold measurement. In order to enable a threshold
          measurement the particular bit in the jnxSoamDmThresholdCfgEnable object
          is to be set to '1' and the selected threshold measurement is to have
          a threshold value configured. Non-configured threshold measurements
          are disabled by default.

          The writable objects in this table need to be persistent upon reboot
          or restart of a device.
          "
     ::= { jnxSoamPmDmObjects 5 }

jnxSoamDmThresholdCfgEntry OBJECT-TYPE
     SYNTAX JnxSoamDmThresholdCfgEntry
     MAX-ACCESS not-accessible
     STATUS current
     DESCRIPTION
         "The conceptual row of jnxSoamDmThresholdCfgTable."
     INDEX {
                dot1agCfmMdIndex,
                dot1agCfmMaIndex,
                dot1agCfmMepIdentifier,
                jnxSoamDmCfgIndex,
                jnxSoamDmThresholdCfgIndex
     }
     ::= {jnxSoamDmThresholdCfgTable 1 }

JnxSoamDmThresholdCfgEntry ::= SEQUENCE {
     jnxSoamDmThresholdCfgIndex Unsigned32,
     jnxSoamDmThresholdCfgEnable BITS,
     jnxSoamDmThresholdCfgAvgFrameDelayTwoWayThreshold Unsigned32,
     jnxSoamDmThresholdCfgAvgIfdvTwoWayThreshold Unsigned32,
     jnxSoamDmThresholdCfgRowStatus RowStatus
}

jnxSoamDmThresholdCfgIndex OBJECT-TYPE
     SYNTAX Unsigned32(0..4294967295)
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The index of the threshold number for the specific DM
          threshold entry.

          An index value of '1' is to be supported. Other index values
          can be supported.
          "
     ::= { jnxSoamDmThresholdCfgEntry 1 }

jnxSoamDmThresholdCfgEnable OBJECT-TYPE
     SYNTAX BITS {
                bJnxSoamDmMeasuredFrameDelayTwoWayThreshold(0),
                bJnxSoamDmMaxFrameDelayTwoWayThreshold(1),
                bJnxSoamDmAvgFrameDelayTwoWayThreshold(2),
                bJnxSoamDmMeasuredIfdvTwoWayThreshold(3),
                bJnxSoamDmMaxIfdvTwoWayThreshold(4),
                bJnxSoamDmAvgIfdvTwoWayThreshold(5),
                bJnxSoamDmMaxFrameDelayRangeTwoWayThreshold(6),
                bJnxSoamDmAvgFrameDelayRangeTwoWayThreshold(7),
                bJnxSoamDmMeasuredFrameDelayForwardThreshold(8),
                bJnxSoamDmMaxFrameDelayForwardThreshold(9),
                bJnxSoamDmAvgFrameDelayForwardThreshold(10),
                bJnxSoamDmMeasuredIfdvForwardThreshold(11),
                bJnxSoamDmMaxIfdvForwardThreshold(12),
                bJnxSoamDmAvgIfdvForwardThreshold(13),
                bJnxSoamDmMaxFrameDelayRangeForwardThreshold(14),
                bJnxSoamDmAvgFrameDelayRangeForwardThreshold(15),
                bJnxSoamDmMeasuredFrameDelayBackwardThreshold(16),
                bJnxSoamDmMaxFrameDelayBackwardThreshold(17),
                bJnxSoamDmAvgFrameDelayBackwardThreshold(18),
                bJnxSoamDmMeasuredIfdvBackwardThreshold(19),
                bJnxSoamDmMaxIfdvBackwardThreshold(20),
                bJnxSoamDmAvgIfdvBackwardThreshold(21),
                bJnxSoamDmMaxFrameDelayRangeBackwardThreshold(22),
                bJnxSoamDmAvgFrameDelayRangeBackwardThreshold(23)
     }
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "A vector of bits that indicates the type of SOAM DM threshold
          notifications that are enabled.

          A bit set to '1' enables the specific SOAM DM threshold notification
          and when the specific counter is enabled and the threshold is crossed a
          notification is generated.

          A bit set to '0' disables the specific SOAM DM threshold notification.

          If a particular SOAM DM threshold is not supported the BIT value is
          set to '0'.

          bJnxSoamDmMeasuredFrameDelayTwoWayThreshold(0)
          Enables/disables measured frame two-way delay threshold
          notification. The notification is sent immediately when the
          jnxSoamDmMeasuredStatsFrameDelayTwoWay value is
          greater than or equal to threshold value.
          bJnxSoamDmMaxFrameDelayTwoWayThreshold(1)
          Enables/disables maximum frame two-way delay threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsFrameDelayTwoWayMax value is
          greater than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgFrameDelayTwoWayThreshold(2)
          Enables/disables average frame two-way delay threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsFrameDelayTwoWayAvg value is
          greater than or equal to the threshold value.
          bJnxSoamDmMeasuredIfdvTwoWayThreshold(3)
          Enables/disables measured frame IFDV two-way threshold
          notification. The notification is sent immediately when the
          jnxSoamDmMeasuredStatsIfdvTwoWay value is greater
          than or equal to threshold value.
          bJnxSoamDmMaxIfdvTwoWayThreshold(4)
          Enables/disables maximum frame IFDV two-way threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsIfdvTwoWayMax value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgIfdvTwoWayThreshold(5)
          Enables/disables average frame IFDV two-way threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsIfdvTwoWayAvg value is
          greater than or equal to the threshold value.
          bJnxSoamDmMaxFrameDelayRangeTwoWayThreshold(6)
          Enables/disables maximum Frame Delay Range two-way threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsFrameDelayRangeTwoWayMax value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgFrameDelayRangeTwoWayThreshold(7)
          Enables/disables average Frame Delay Range two-way threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsFrameDelayRangeTwoWayAvg value is
          greater than or equal to the threshold value.
          bJnxSoamDmMeasuredFrameDelayForwardThreshold(8)
          Enables/disables measured forward frame delay threshold
          notification. The notification is sent immediately when the
          jnxSoamDmMeasuredStatsFrameDelayForward value is
          greater than or equal to threshold value.
          bJnxSoamDmMaxFrameDelayForwardThreshold(9)
          Enables/disables maximum forward frame delay threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsFrameDelayForwardMax value is
          greater than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgFrameDelayForwardThreshold(10)
          Enables/disables average forward frame delay threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsFrameDelayForwardAvg value is
          greater than or equal to the threshold value.
          bJnxSoamDmMeasuredIfdvForwardThreshold(11)
          Enables/disables measured frame IFDV forward threshold
          notification. The notification is sent immediately when the
          jnxSoamDmMeasuredStatsIfdvForward value is greater
          than or equal to threshold value.
          bJnxSoamDmMaxIfdvForwardThreshold(12)
          Enables/disables maximum frame IFDV forward threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsIfdvForwardMax value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgIfdvForwardThreshold(13)
          Enables/disables average frame IFDV forward threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsIfdvForwardAvg value is
          greater than or equal to the threshold value.
          bJnxSoamDmMaxFrameDelayRangeForwardThreshold(14)
          Enables/disables maximum Frame Delay Range forward threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsFrameDelayRangeForwardMax value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgFrameDelayRangeForwardThreshold(15)
          Enables/disables average Frame Delay Range forward threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsFrameDelayRangeForwardAvg value is
          greater than or equal to the threshold value.
          bJnxSoamDmMeasuredFrameDelayBackwardThreshold(16)
          Enables/disables measured backward frame delay threshold
          notification. The notification is sent immediately when the
          jnxSoamDmMeasuredStatsFrameDelayBackward value is
          greater than or equal to threshold value.
          bJnxSoamDmMaxFrameDelayBackwardThreshold(17)
          Enables/disables maximum backward frame delay threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsFrameDelayBackwardMax value is
          greater than or equal to threshold value in a
          Measurement Interval.
          bJnxSoamDmAvgFrameDelayBackwardThreshold(18)
          Enables/disables average backward frame delay threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsFrameDelayBackwardAvg value is
          greater than or equal to the threshold value.
          bJnxSoamDmMeasuredIfdvBackwardThreshold(19)
          Enables/disables measured frame IFDV backward threshold
          notification. The notification is sent immediately when the
          jnxSoamDmMeasuredStatsIfdvBackward value is greater
          than or equal to threshold value.
          bJnxSoamDmMaxIfdvBackwardThreshold(20)
          Enables/disables maximum frame IFDV backward threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsIfdvBackwardMax value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgIfdvBackwardThreshold(21)
          Enables/disables average frame IFDV backward threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsIfdvBackwardAvg value is greater
          than or equal to the threshold value.
          bJnxSoamDmMaxFrameDelayRangeBackwardThreshold(22)
          Enables/disables maximum Frame Delay Range backward threshold
          notification. The notification is sent immediately when the
          jnxSoamDmCurrentStatsFrameDelayRangeBackwardMax value is greater
          than or equal to threshold value in a Measurement Interval.
          bJnxSoamDmAvgFrameDelayRangeBackwardThreshold(23)
          Enables/disables average Frame Delay Range backward threshold
          notification. The notification is sent when at the end of a
          Measurement Interval if the
          jnxSoamDmCurrentStatsFrameDelayRangeBackwardAvg value is greater
          than or equal to the threshold value.
          "
     DEFVAL { { } }
     ::= { jnxSoamDmThresholdCfgEntry 2 }

jnxSoamDmThresholdCfgAvgFrameDelayTwoWayThreshold OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object is used to set the average two-way delay threshold
          value that will be used to determine if a threshold notification is
          generated.
          "
     DEFVAL { 4294967295 }
     ::= { jnxSoamDmThresholdCfgEntry 3 }

jnxSoamDmThresholdCfgAvgIfdvTwoWayThreshold OBJECT-TYPE
     SYNTAX Unsigned32
     UNITS "microseconds"
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "This object is used to set the average two-way IFDV threshold
          value that will be used to determine if a threshold notification is
          generated.
          "
     DEFVAL { 4294967295 }
     ::= { jnxSoamDmThresholdCfgEntry 4 }

jnxSoamDmThresholdCfgRowStatus OBJECT-TYPE
     SYNTAX RowStatus
     MAX-ACCESS read-only
     STATUS current
     DESCRIPTION
         "The status of the row.

          The writable columns in a row cannot be changed if the row
          is active. All columns are to have a valid value before a row
          can be activated.
          "
     ::= { jnxSoamDmThresholdCfgEntry 5 }


-- *****************************************************************************
-- Ethernet Delay Measurement Bin Configuration Table
-- *****************************************************************************

jnxSoamDmCfgMeasBinTable OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxSoamDmCfgMeasBinEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table includes configuration objects for the Delay Measurement
        bins to collect stats.

        Each row in the table is automatically created when the Delay
        Measurement session is defined for the selected MEP. The number of rows
        created is based upon three items: the DM type, the number of bins
        defined for each type, and whether bins are enabled for each type.

        The first four indices are the same as used to create the DM session:
        Maintenance Domain, MaNet, MEP identification, and jnxSoamDmCfgIndex. The
        fifth index is the type of bin, and the sixth index is the bin number.

        For a dmDmm session all nine types of bins can be created. For a dm1DmmTx
        session no bins are created. For a dm1DmmRx session only types
        forwardFrameDelay, forwardIfdv, and forwardFrameDelayRange can be created.

        The number of bins created for a bin type is based upon: the
        jnxSoamDmCfgNumMeasBinsPerFrameDelayInterval object, the
        jnxSoamDmCfgNumMeasBinsPerInterFrameDelayVariationInterval object, the
        jnxSoamDmCfgNumMeasBinsPerFrameDelayRangeInterval object, and
        the jnxSoamDmCfgMeasurementEnable object.

        For instance, if a dmDmm session with Bins per Frame Delay Interval
        set to 5, Bins per Frame Delay Variation Interval set to 3, and Frame
        Delay Range set to 2 (default), all of the appropriate bits set in
        jnxSoamDmMeasurementCfgEnable, the following number of rows would be
        created:

        For bin types TwoWayFrameDelay(1), forwardFrameDelay(2), and
        backwardFrameDelay(3) = 5 bins * 3 types = 15 rows

        For bin types TwoWayIfdv(4), forwardIfdv(5), backwardIfdv(6) =
        3 bins * 3 types = 9 rows.

        For bins types twoWayFrameDelayRange(7), forwardFrameDelayRange(8),
        backwardFrameDelayRange(9) =
        2 bins * 3 types = 6 rows.

        This gives a total of 30 rows created for the DMM session example.

        Each value in the bin defaults to 5000us greater than the previous bin,
        with the first bin default value set to 0.

        For the delay example above (5 bins), the following default values
        would be written to the bins:
        bin 1: 0 (range is 0us <= measurement < 5,000us)
        bin 2: 5000 (range is 5,000us <= measurement < 10,000us)
        bin 3: 10000 (range is 10,000us <= measurement < 15,000us)
        bin 4: 15000 (range is 15,000us <= measurement < 20,000us)
        bin 5: 20000 (range is 20,000us <= measurement < infinity)

        For the delay variation example above (3 bins), the following default
        values would be written to the bins:
        bin 1: 0 (range is 0us <= measurement < 5,000us)
        bin 2: 5000 (range is 5,000us <= measurement < 10,000us)
        bin 3: 10000 (range is 10,000us <= measurement < infinity)

        For the frame delay range example above (2 bins), the following default
        values would be written to the bins:
        bin 1: 0 (range is 0us <= measurement < 5,000us)
        bin 2: 5000 (range is 5,000us <= measurement < infinity)

        The writable objects in this table need to be persistent upon reboot
        or restart of a device.

        Rows are only created if the corresponding measurement type has been enabled
        via the jnxSoamDmCfgMeasurementEnable object.
        "
    REFERENCE
        "[MEF SOAM-PM] R34, R36, R37, D17, R38, R65, D26, D27, R99, D43, D44"
    ::= { jnxSoamPmDmObjects 6 }

jnxSoamDmCfgMeasBinEntry OBJECT-TYPE
    SYNTAX JnxSoamDmCfgMeasBinEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The conceptual row of jnxSoamDmCfgMeasBinTable."
    INDEX {
        dot1agCfmMdIndex,
        dot1agCfmMaIndex,
        dot1agCfmMepIdentifier,
        jnxSoamDmCfgIndex,
        jnxSoamDmCfgMeasBinType,
        jnxSoamDmCfgMeasBinNumber
        }
    ::= { jnxSoamDmCfgMeasBinTable 1 }

JnxSoamDmCfgMeasBinEntry ::= SEQUENCE {
    jnxSoamDmCfgMeasBinType JnxSoamTcDelayMeasurementBinType,
    jnxSoamDmCfgMeasBinNumber Unsigned32,
    jnxSoamDmCfgMeasBinLowerBound Unsigned32
}

jnxSoamDmCfgMeasBinType OBJECT-TYPE
    SYNTAX JnxSoamTcDelayMeasurementBinType
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This object specifies whether the bin number is for
        Frame Delay or Inter-Frame Delay Variation.
        "
    ::= { jnxSoamDmCfgMeasBinEntry 1 }

jnxSoamDmCfgMeasBinNumber OBJECT-TYPE
    SYNTAX Unsigned32
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This object specifies the bin number for the
        configured boundary. The first bin has bin number 1.
        "
    ::= { jnxSoamDmCfgMeasBinEntry 2 }

jnxSoamDmCfgMeasBinLowerBound OBJECT-TYPE
    SYNTAX Unsigned32
    UNITS "microseconds (us)"
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "This object specifies the lower boundary for a
        measurement bin. The upper boundary is defined by the next bin
        value or infinite for the last bin defined.
        The measurement boundary for each measurement bin is to
        be larger than the measurement boundary of the preceding
        measurement bin. By default, the next bin is set to 5000us larger
        than the lower bin boundary.

        The values in a bin boundary object represents the time range
        used to segregate delay data into the appropriate statistical
        data bin. For five bins with default values, each bin has the
        following time range:

        bin 1 = 0, range is 0us <= measurement < 5,000us
        bin 2 = 5000, range is 5,000us <= measurement < 10,000us
        bin 3 = 10000, range is 10,000us <= measurement < 15,000us
        bin 4 = 15000, range is 15,000us <= measurement < 20,000us
        < infinity

        The first bin boundary (jnxSoamDmCfgBinNumber set to 1) always contains
        the value of 0. Attempting to write a non-zero value to this bin will
        result in an error.
        "
    REFERENCE
        "[MEF SOAM-PM] R33, R35, D17"
    ::= { jnxSoamDmCfgMeasBinEntry 3 }

-- *****************************************************************************
-- Ethernet Delay Measurement Current Bin Statistic Table
-- *****************************************************************************

jnxSoamDmCurrentStatsBinsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxSoamDmCurrentStatsBinsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table contains the result bins for the current Measurement
        Interval in a SOAM Delay Measurement session.

        Each row in the table represents the current bin statistics for a
        Delay Measurement session for the defined MEP. This table uses six
        indices. The first three indices are the indices of the Maintenance
        Domain, MaNet, and MEP tables. The fourth index is the specific DM
        session on the selected MEP. The fifth index indicates bin type and
        the sixth indicates the specific bin number.

        A row in this table is created automatically by the SNMP Agent when
        the Delay Measurement session is configured and the bin counter value
        is set to 0.

        The objects in this table are ignored if jnxSoamDmCfgType is 1DmTx.

        This table applies only if the corresponding bit is set in
        jnxSoamDmCfgMeasurementEnable.

        The objects in this table do not need to be persistent upon reboot
        or restart of a device.
        "
    REFERENCE
        "[MEF SOAM-PM] R7, R15, D9"
    ::= { jnxSoamPmDmObjects 7 }

jnxSoamDmCurrentStatsBinsEntry OBJECT-TYPE
    SYNTAX JnxSoamDmCurrentStatsBinsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The conceptual row of jnxSoamDmCurrentStatsBinsTable"
    INDEX {
        dot1agCfmMdIndex,
        dot1agCfmMaIndex,
        dot1agCfmMepIdentifier,
        jnxSoamDmCfgIndex,
        jnxSoamDmCfgMeasBinType,
        jnxSoamDmCfgMeasBinNumber
        }
    ::= { jnxSoamDmCurrentStatsBinsTable 1 }

JnxSoamDmCurrentStatsBinsEntry ::= SEQUENCE {
    jnxSoamDmCurrentStatsBinsCounter Gauge32
}

jnxSoamDmCurrentStatsBinsCounter OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the count of the number of completed
        measurements initiated in this Measurement Interval whose value
        falls within the range specified for this bin (that is, greater
        than or equal to the measurement boundary for the bin, and
        (unless the bin is the last bin) less than the measurement
        boundary for the following bin.
        "
    REFERENCE
        "[MEF SOAM-PM] R66, R67, R100, R101"
    ::= { jnxSoamDmCurrentStatsBinsEntry 1 }

-- *****************************************************************************
-- Ethernet Delay Measurement Bin History Statistic Table
-- *****************************************************************************

jnxSoamDmHistoryStatsBinsTable OBJECT-TYPE
    SYNTAX SEQUENCE OF JnxSoamDmHistoryStatsBinsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "This table contains the result bins for the history Measurement
        Intervals in a SOAM Delay Measurement session.

        Rows of this table are created automatically
        by the SNMP Agent when the Delay Measurement session is running and a
        Measurement Interval is completed.

        Each row in the table represents the Measurement Interval history
        statistics for a specific bin in a Delay Measurement session for the
        defined MEP. This table uses seven indices. The first three indices
        are the indices of the Maintenance Domain, MaNet, and MEP tables. The
        fourth index is the specific DM session on the selected MEP. The
        fifth index is the Measurement Interval. The sixth index is the
        specific bin type. The seventh index is the specific bin number.

        Rows in this table pertaining to a given Measurement Interval are
        row in the
        jnxSoamDmHistoryStatsTable is deleted.

        The objects in this table are ignored if jnxSoamDmCfgType is 1DmTx.

        This table applies only if the corresponding bit is set in
        jnxSoamDmCfgMeasurementEnable.

        The objects in this table need to be persistent upon reboot
        or restart of a device.
        "
    REFERENCE
        "[MEF SOAM-PM] R7, R15, R21, D8, R66, R67"
    ::= { jnxSoamPmDmObjects 8 }

jnxSoamDmHistoryStatsBinsEntry OBJECT-TYPE
    SYNTAX JnxSoamDmHistoryStatsBinsEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "The conceptual row of jnxSoamDmHistoryStatsBinsTable"
    INDEX {
        dot1agCfmMdIndex,
        dot1agCfmMaIndex,
        dot1agCfmMepIdentifier,
        jnxSoamDmCfgIndex,
        jnxSoamDmHistoryStatsIndex,
        jnxSoamDmCfgMeasBinType,
        jnxSoamDmCfgMeasBinNumber
        }
    ::= { jnxSoamDmHistoryStatsBinsTable 1 }

JnxSoamDmHistoryStatsBinsEntry ::= SEQUENCE {
    jnxSoamDmHistoryStatsBinsCounter Gauge32
}

jnxSoamDmHistoryStatsBinsCounter OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object contains the count of the number of completed
        measurements initiated in this Measurement Interval whose value
        falls within the range specified for this bin (that is, greater
        than or equal to the measurement boundary for the bin, and
        (unless the bin is the last bin) less than the measurement
        boundary for the following bin.
        "
    REFERENCE
        "[MEF SOAM-PM] R66, R67, R100, R101"
    ::= { jnxSoamDmHistoryStatsBinsEntry 1 }



-- *****************************************************************************
-- Notification Data Objects
-- *****************************************************************************

jnxSoamPmNotificationObjDateAndTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "This object contains the time and date at the time that
          the notification event is detected, not the time of the notification
          generation.

          This object is used only for notifications. The mechanism to set and keep
          current the date and time is not specified.
          "
     ::= { jnxSoamPmNotificationObj 1 }

jnxSoamPmNotificationObjThresholdId OBJECT-TYPE
     SYNTAX OBJECT IDENTIFIER
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The Object Identifier of the object that caused the generation of the
          notification from the jnxSoamLmThresholdEntry or jnxSoamDmThresholdEntry.

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 2 }

jnxSoamPmNotificationObjThresholdConfig OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The configured threshold value of the object that caused the generation
          of the notification.

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 3 }

jnxSoamPmNotificationObjThresholdValue OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The measured value of the object at the time of the generation of the
          Notification, from the jnxSoamLmMeasuredStatsTable,
          jnxSoamLmCurrentStatsTable,
          jnxSoamDmMeasuredStatsTable or jnxSoamDmCurrentStatsTable.

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 4 }

jnxSoamPmNotificationObjSuspect OBJECT-TYPE
     SYNTAX TruthValue
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The suspect flag for the current Measurement Interval in which the
          notification was generated from the jnxSoamLmCurrentStatsTable,
          or jnxSoamDmCurrentStatsTable.

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 5 }

jnxSoamPmNotificationObjCrossingType OBJECT-TYPE
     SYNTAX INTEGER {
                aboveAlarm (1),
                setAlarm (2),
                clearAlarm (3)
     }
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The Notification Crossing Type of the object that caused the generation
          of the notification from the jnxSoamLmThresholdEntry or
          jnxSoamDmThresholdEntry.

          aboveAlarm(1) indicates that the crossing type alarm was an above
          threshold

          setAlarm(2) indicates that the crossing type alarm was a set
          threshold

          clearAlarm(3) indicates that the crossing type alarm was a clear
          threshold

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 6 }

jnxSoamPmNotificationObjDestinationMep OBJECT-TYPE
     SYNTAX MacAddress
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The MAC address of the Destination MEP associated the notification found
          in either the jnxSoamDmCfgTable or jnxSoamLmCfgTable.

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 7 }

jnxSoamPmNotificationObjPriority OBJECT-TYPE
     SYNTAX MacAddress
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The CoS priority of the associated notification found
          in either the jnxSoamDmCfgTable or jnxSoamLmCfgTable.

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 8 }

jnxSoamPmNotificationTotalFlaps OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The measured value of total number of flaps occured during the
          flap trap timer itnerval.
          "
     ::= { jnxSoamPmNotificationObj 9 }

jnxSoamPmNotificationAccTotalFlaps OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The measured value of total number of accumulated flaps occured during the
          flap trap timer itnerval.
          "
     ::= { jnxSoamPmNotificationObj 10 }

jnxSoamPmNotificationObjThresholdLastValue OBJECT-TYPE
     SYNTAX Unsigned32
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The measured value of the object at the time of the generation of the
          last Notification during the flap trap timer interval.
                 
          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 11 }

jnxSoamPmNotificationObjCurrentState OBJECT-TYPE
     SYNTAX INTEGER {
                aboveAlarm (1),
                setAlarm (2),
                clearAlarm (3)
     }
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "The Current Notification Crossing Type of the 
          object that caused the generation
          of the notification at the end of timer interval 
          from the jnxSoamLmThresholdEntry or
          jnxSoamDmThresholdEntry.

          aboveAlarm(1) indicates that the crossing type alarm was an above
          threshold

          setAlarm(2) indicates that the crossing type alarm was a set
          threshold

          clearAlarm(3) indicates that the crossing type alarm was a clear
          threshold

          This object is only used for the notification.
          "
     ::= { jnxSoamPmNotificationObj 12 }

jnxSoamPmNotificationObjLastDateAndTime OBJECT-TYPE
     SYNTAX DateAndTime
     MAX-ACCESS accessible-for-notify
     STATUS current
     DESCRIPTION
         "This object contains the time and date at the time that
          the last notification event is detected, at the end of flap timer interval
          and not the time of the first notification
          generation.

          This object is used only for notifications. The mechanism to set and keep
          current the date and time is not specified.
          "
     ::= { jnxSoamPmNotificationObj 13 }

-- *****************************************************************************
-- NOTIFICATIONS (TRAPS)
-- *****************************************************************************

jnxSoamLmSessionStartStopAlarm NOTIFICATION-TYPE
	 OBJECTS {
		 jnxSoamLmCfgSessionStatus,
		 jnxSoamPmNotificationObjDateAndTime,
		 jnxSoamPmNotificationObjDestinationMep
	 }
	 STATUS current
	 DESCRIPTION
		 "An jnxSoamLmSessionStartStopAlarm notification is sent when the state of
		 jnxSoamLmCfgSessionStatus changes.

		 The management entity that receives the notification can identify
		 the system from the network source address of the notification,
		 and can identify the individual PM session reporting the start/stop
		 by the indices in the OID jnxSoamLmCfgSessionStatus, including
		 dot1agCfmMdIndex, dot1agCfmMaIndex, dot1agCfmMepIdentifier, and
		 jnxSoamLmCfgIndex.

		 An agent is not to generate more than one jnxSoamLmSessionStartStopAlarm
		 'notification-event' in a given time interval per LM session as specified
		 by the jnxSoamPmNotificationCfgAlarmInterval. A 'notification-event' is
		 the transmission of a single notification to a list of notification
		 destinations.

		 If additional operational state changes occur within the
		 jnxSoamPmNotificationCfgAlarmInterval period, then notification
		 generation for these changes are be suppressed by the agent until
		 the current alarm interval expires. At the end of an alarm interval
		 period, one notification-event is generated if any operational
		 state changes occurred since the start of the alarm interval period. In
		 such a case, another alarm interval period is started right away.
		 "
	 ::= { jnxSoamPmNotifications 1 }

jnxSoamDmSessionStartStopAlarm NOTIFICATION-TYPE
	 OBJECTS {
		 jnxSoamDmCfgSessionStatus,
		 jnxSoamPmNotificationObjDateAndTime,
		 jnxSoamPmNotificationObjDestinationMep
	 }
	 STATUS current
	 DESCRIPTION
		 "An jnxSoamDmSessionStartStopAlarm notification is sent when the state of
		 jnxSoamDmCfgSessionStatus changes.

		 The management entity that receives the notification can identify
		 the system from the network source address of the notification,
		 and can identify the individual PM session reporting the start/stop
		 by the indices in the OID jnxSoamDmCfgSessionStatus, including
		 dot1agCfmMdIndex, dot1agCfmMaIndex, dot1agCfmMepIdentifier, and
		 jnxSoamDmCfgIndex.

		 An agent is not to generate more than one jnxSoamDmSessionStartStopAlarm
		 'notification-event' in a given time interval per DM session as specified
		 by jnxSoamPmNotificationCfgAlarmInterval. A 'notification-event' is the
		 transmission of a single notification to a list of notification
		 destinations.

		 If additional operational state changes occur within the
		 jnxSoamPmNotificationCfgAlarmInterval period, then notification generation
		 for these changes are suppressed by the agent until the current alarm
		 interval expires. At the end of an alarm interval period, one
		 notification-event is generated if any operational state changes
		 occurred since the start of the alarm interval period. In such a case,
		 another alarm interval period is started right away.
		 "
	 ::= { jnxSoamPmNotifications 2 }

jnxSoamPmThresholdCrossingAlarm NOTIFICATION-TYPE
	 OBJECTS {
		 jnxSoamPmNotificationObjCrossingType,
		 jnxSoamPmNotificationObjThresholdId,
		 jnxSoamPmNotificationObjThresholdConfig,
		 jnxSoamPmNotificationObjThresholdValue,
		 jnxSoamPmNotificationObjSuspect,
		 jnxSoamPmNotificationObjDateAndTime,
		 jnxSoamPmNotificationObjDestinationMep
	 }
	 STATUS current
	 DESCRIPTION
		 "An jnxSoamPmThresholdCrossingAlarm notification is sent if the
		 following conditions are met for a particular type.

		 For an aboveAlarm five conditions need to be met:

		 a) measurement of the parameter is enabled via jnxSoamLmCfgMeasurementEnable
		 for a LM crossing or jnxSoamDmCfgMeasurementEnable for a DM crossing;
		 and

		 b) the parameter threshold is configured in the jnxSoamLmThresholdCfgTable
		 or jnxSoamDmThresholdCfgTable; and

		 c) the threshold crossing type of bPmThresholdAboveAlarm is enabled;
		 and

		 d) the measured value of the parameter exceeds the value configured in
		 the jnxSoamLmThresholdCfgTable for a LM crossing entry or
		 jnxSoamDmThresholdCfgTable for a DM crossing entry for a type of
		 bPmThresholdAboveAlarm; and

		 e) no previous jnxSoamPmThresholdCrossingAlarm notifications with type
		 aboveAlarm have been sent relating to the same threshold in the
		 jnxSoamLmThresholdCfgTable or jnxSoamDmThresholdCfgTable and the
		 same parameter, during this Measurement Interval.

		 For a setAlarm five conditions need to be met:

		 a) measurement of the parameter is enabled via jnxSoamLmCfgMeasurementEnable
		 for a LM crossing or jnxSoamDmCfgMeasurementEnable for a DM crossing;
		 and

		 b) the parameter threshold is configured in the jnxSoamLmThresholdCfgTable
		 or jnxSoamDmThresholdCfgTable; and

		 c) the threshold crossing type of bPmThresholdSetClearAlarm is enabled;
		 and

		 d) the measured value of the parameter exceeds the value configured in
		 the jnxSoamLmThresholdCfgTable for a LM crossing entry or
		 jnxSoamDmThresholdCfgTable for a DM crossing entry for a type of
		 bPmThresholdSetClearAlarm for the Measurement Interval; and

		 e) the previous measured value did not
		 exceed the value configured in the jnxSoamLmThresholdCfgTable for
		 a LM crossing entry or jnxSoamDmThresholdCfgTable for a DM crossing
		 entry for a type of bPmThresholdSetClearAlarm.

		 For a clearAlarm five conditions need to be met:

		 a) measurement of the parameter is enabled via jnxSoamLmCfgMeasurementEnable
		 for a LM crossing or jnxSoamDmCfgMeasurementEnable for a DM crossing;
		 and

		 b) the parameter threshold is configured in the jnxSoamLmThresholdCfgTable
		 or jnxSoamDmThresholdCfgTable; and

		 c) the threshold crossing type of bPmThresholdSetClearAlarm is enabled;
		 and

		 d) the measured value of the parameter did not exceed the value configured
		 in the jnxSoamLmThresholdCfgTable for a LM crossing entry or
		 jnxSoamDmThresholdCfgTable for a DM crossing entry for a type of
		 bPmThresholdSetClearAlarm for the Measurement Interval; and

		 e) the previous measured value did
		 exceed the value configured in the jnxSoamLmThresholdCfgTable for
		 a LM crossing entry or jnxSoamDmThresholdCfgTable for a DM crossing
		 entry for a type of bPmThresholdSetClearAlarm.

		 In the case of thresholds applied to a maximum or average measurement
		 counter, the previous measured value is the value of the counter at the
		 end of the preceding Measurement Interval. In the case of thresholds
		 applied to the last measured value, it is the previous measured value.

		 The management entity that receives the notification can identify
		 the system from the network source address of the notification,
		 and can identify the LM or DM session reporting the threshold
		 crossing by the indices in the jnxSoamPmNotificationCfgThresholdId object,
		 including dot1agCfmMdIndex, dot1agCfmMaIndex, dot1agCfmMepIdentifier,
		 and the jnxSoamLmCfgIndex or jnxSoamDmCfgIndex.

		 An agent is not to generate more than one jnxSoamLmThresholdCrossingAlarm
		 'notification-event' of a given type per LM or DM session as specified
		 by jnxSoamPmNotificationCfgAlarmInterval. A 'notification-event' is the
		 transmission of a single notification to a list of notification
		 destinations.

		 If additional threshold crossing events occur within the
		 jnxSoamPmNotificationCfgAlarmInterval period, then notification
		 generation for these changes are suppressed by the agent until
		 the current alarm interval expires. At the end of an alarm interval
		 period, one notification-event is generated if any threshold
		 crossing events occurred since the start of the alarm interval period.
		 In such a case, another alarm interval period is started right away.
		 "
	 ::= { jnxSoamPmNotifications 3 } 

jnxSoamPmThresholdFlapAlarm NOTIFICATION-TYPE
         OBJECTS {
                 jnxSoamPmNotificationObjThresholdId,
                 jnxSoamPmNotificationObjThresholdConfig,
                 jnxSoamPmNotificationObjThresholdLastValue,
                 jnxSoamPmNotificationTotalFlaps,
                 jnxSoamPmNotificationAccTotalFlaps,
                 jnxSoamPmNotificationObjCurrentState,
                 jnxSoamPmNotificationObjDestinationMep
         }
         STATUS current
         DESCRIPTION
                 " The jnxSoamPmThresholdFlapAlarm is sent when the CFM Threshold Flap 
                   Notification feature is enabled which would dampen the 
                   jnxSoamPmThresholdCrossingAlarm sent to NMS. The jnxSoamPmThresholdFlapAlarm
                   gives details about Flaps occured during the time interval. The 
                   jnxSoamPmThresholdFlapAlarm is sent for the follwing below conditions
                   a) Trap is sent if  one flap sequence has occured
                   b) Trap is sent if threshold or timer changes happens.
                   c) Trap is sent when finite iterations counts expires.
                 "

         ::= { jnxSoamPmNotifications 4 }
END
