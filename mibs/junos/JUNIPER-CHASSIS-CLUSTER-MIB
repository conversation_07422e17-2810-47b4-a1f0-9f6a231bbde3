-- *******************************************************************
-- Juniper enterprise specific Chassis Cluster objects MIB.
--
-- Copyright (c) 2008-2011, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
-- *******************************************************************

    JUNIPER-CHASSIS-CLUSTER-MIB DEFINITIONS ::= BEGIN

    IMPORTS
        Counter32, Counter64, Ip<PERSON>ddress, Integer32,
        NOTIFICATION-TYPE, MODULE-IDENTITY,
        OBJECT-TYPE
            FROM SNMPv2-SMI

        TEXTUAL-CONVENTION, DisplayString
            FROM SNMPv2-TC

        jnxJsChassisCluster
            FROM JUNIPER-JS-SMI;


    jnxJsChassisClusterMIB  MODULE-IDENTITY
        LAST-UPDATED  "201908290000Z"
        ORGANIZATION  "Juniper Networks, Inc."
        CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089

             E-mail: <EMAIL>
             HTTP://www.juniper.net"
        DESCRIPTION
            "Chassis Clustering is concerned with ensuring minimal disruption to
             data and control planes in case of a failover. If one of the chassis in
             a cluster fails, the other chassis in the cluster takes over the function
             of the failed chassis with minimal service interruption.
             This module defines the objects pertaining to Chassis Cluster."

        REVISION      "201908290000Z"
        DESCRIPTION   "Added trap jnxJsChHAPeerBfdTrap"

        REVISION      "201809180000Z"
        DESCRIPTION   "Added trap jnxJsChClusterHealthTrap"

        REVISION      "201309200000Z"
        DESCRIPTION   "Added trap jnxJsChClusterWeightTrap"

        REVISION      "201207200000Z"
        DESCRIPTION   "Added trap jnxJsChClusterSpuMismatchTrap"

        REVISION      "201106280000Z"
        DESCRIPTION   "Added trap jnxJsChClusterIntfTrap"

        REVISION      "200905270000Z"
        DESCRIPTION   "Added trap class for jnxJsChassisClusterSwitchover"

        REVISION      "200902030000Z"
        DESCRIPTION   "Creation Date"
    ::= { jnxJsChassisCluster 1 }


    jnxJsChassisClusterNotifications OBJECT IDENTIFIER ::=
                                     { jnxJsChassisClusterMIB 0 }
    jnxJsChassisClusterTrapObjects   OBJECT IDENTIFIER ::=
                                     { jnxJsChassisClusterMIB 1 }

    -- ***************************************************************
    -- definition of ChassisCluster related traps and objects.
    -- ***************************************************************

    jnxJsChassisClusterSwitchover NOTIFICATION-TYPE
        OBJECTS { jnxJsChClusterSwitchoverInfoRedundancyGroup,
                  jnxJsChClusterSwitchoverInfoClusterId,
                  jnxJsChClusterSwitchoverInfoNodeId,
                  jnxJsChClusterSwitchoverInfoPreviousState,
                  jnxJsChClusterSwitchoverInfoCurrentState,
                  jnxJsChClusterSwitchoverInfoReason }
        STATUS              current
        DESCRIPTION
            "Notification to signal switchover/failover."
        ::= { jnxJsChassisClusterNotifications 1 }
    -- ***************************************************************
    -- definition of a trap that notifies changes in fabric
    -- and control link status.
    -- ***************************************************************

    jnxJsChClusterIntfTrap  NOTIFICATION-TYPE
        OBJECTS { jnxJsChClusterSwitchoverInfoClusterId,
                  jnxJsChClusterIntfName,
                  jnxJsChClusterIntfState,
                  jnxJsChClusterIntfSeverity,
                  jnxJsChClusterIntfStateReason }
        STATUS              current
        DESCRIPTION
            "Notification to signal node health status change (healthy/unhealthy)."
        ::= { jnxJsChassisClusterNotifications 2 }

    -- ***************************************************************
    -- definition of a trap that notifies cluster having different
    -- SPU number
    -- ***************************************************************

    jnxJsChClusterSpuMismatchTrap NOTIFICATION-TYPE
        OBJECTS { jnxJsChClusterNodeZeroId,
                  jnxJsChClusterNodeZeroSpuCount,
                  jnxJsChClusterNodeOneId ,
                  jnxJsChClusterNodeOneSpuCount }
        STATUS              current
        DESCRIPTION
            "Notification to signal cluster having different SPU number."
        ::= { jnxJsChassisClusterNotifications 3 }

    -- ***************************************************************
    -- definition of a trap that notifies changes in node weight
    -- ***************************************************************

    jnxJsChClusterWeightTrap NOTIFICATION-TYPE
        OBJECTS { jnxJsChClusterClusterID,
                  jnxJsChClusterRedundancyGroupID,
                  jnxJsChClusterNodeID,
                  jnxJsChClusterWeightHealthStatus,
                  jnxJsChClusterWeightValue }
        STATUS              current
        DESCRIPTION
            "Notification to signal node health status change (healthy/unhealthy)."
        ::= { jnxJsChassisClusterNotifications 4 }

    -- ***************************************************************
    -- definition of a trap that notifies changes in HA health status
    -- ***************************************************************

    jnxJsChClusterHealthTrap NOTIFICATION-TYPE
        OBJECTS { jnxJsChClusterHealthNodeID,
                  jnxJsChClusterHealthSeverity,
                  jnxJsChClusterHealthReason }
        STATUS              current
        DESCRIPTION
            "Notification to signal cluster is in unhealth status."

        ::= { jnxJsChassisClusterNotifications 5 }

    -- ***************************************************************
    -- definition of a trap that notifies changes in L3 HA peer status
    -- ***************************************************************

    jnxJsChHAPeerBfdTrap NOTIFICATION-TYPE
        OBJECTS { jnxJsChHAPeerID,
                  jnxJsChHAPeerBfdSeverity,
                  jnxJsChHAPeerBfdReason }
        STATUS              current
        DESCRIPTION
            "Notification to signal HA peer status change."

        ::= { jnxJsChassisClusterNotifications 6 }

    jnxJsChClusterSwitchoverInfoRedundancyGroup OBJECT-TYPE

        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains identification of redundancy group
             that switched over."
        ::= { jnxJsChassisClusterTrapObjects 1 }

    jnxJsChClusterSwitchoverInfoClusterId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains cluster identification information
             where the switchover occured."
        ::= { jnxJsChassisClusterTrapObjects 2 }

    jnxJsChClusterSwitchoverInfoNodeId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains node identification information
             where the switchover occured."
        ::= { jnxJsChassisClusterTrapObjects 3 }

    jnxJsChClusterSwitchoverInfoPreviousState OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains the redundancy state of the cluster
             before the occurance of switchover."
        ::= { jnxJsChassisClusterTrapObjects 4 }

    jnxJsChClusterSwitchoverInfoCurrentState OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains the redundancy state of the cluster
             after the occurance of switchover."
        ::= { jnxJsChassisClusterTrapObjects 5 }

    jnxJsChClusterSwitchoverInfoReason OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains the cause for switchover."
        ::= { jnxJsChassisClusterTrapObjects 6 }

    jnxJsChClusterIntfName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains the name of the link
             that changed its state."
        ::= { jnxJsChassisClusterTrapObjects 7 }

    jnxJsChClusterIntfState OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains the state of the link;
             whether it is UP or DOWN."
        ::= { jnxJsChassisClusterTrapObjects 8 }

    jnxJsChClusterIntfSeverity OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object reflects the severity;
             whether it is minor or major."
        ::= { jnxJsChassisClusterTrapObjects 9 }

    jnxJsChClusterIntfStateReason OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains the reason why the link state
             changed."
        ::= { jnxJsChassisClusterTrapObjects 10 }

    jnxJsChClusterNodeZeroId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains remote node identification information
             where the event occured."
        ::= { jnxJsChassisClusterTrapObjects 11 }

    jnxJsChClusterNodeOneId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains remote node identification information
             where the event occured."
        ::= { jnxJsChassisClusterTrapObjects 12 }

    jnxJsChClusterNodeZeroSpuCount OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains local node total SPU number."
        ::= { jnxJsChassisClusterTrapObjects 13 }

    jnxJsChClusterNodeOneSpuCount OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains remote node total SPU number."
        ::= { jnxJsChassisClusterTrapObjects 14 }

    jnxJsChClusterClusterID OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains identification of cluster,
             in which the event occured."
        ::= { jnxJsChassisClusterTrapObjects 15 }

    jnxJsChClusterRedundancyGroupID OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains identification of RedundancyGroup,
             in which the event occured."
        ::= { jnxJsChassisClusterTrapObjects 16 }

    jnxJsChClusterNodeID OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains identification of node,
             in which the event occured."
        ::= { jnxJsChassisClusterTrapObjects 17 }

    jnxJsChClusterWeightHealthStatus OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains health status, whether is healthy
             or unhealthy."
        ::= { jnxJsChassisClusterTrapObjects 18 }

    jnxJsChClusterWeightValue OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains weight value."
        ::= { jnxJsChassisClusterTrapObjects 19 }

    jnxJsChClusterHealthNodeID OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains identification of node,
             in which the event occured."
        ::= { jnxJsChassisClusterTrapObjects 20 }

    jnxJsChClusterHealthSeverity OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object reflects the severity;
             whether it is minor or major."
        ::= { jnxJsChassisClusterTrapObjects 21 }

    jnxJsChClusterHealthReason OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains ha unhealth status reason."
        ::= { jnxJsChassisClusterTrapObjects 22 }

    jnxJsChHAPeerID OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains identification of peer,
             in which the event occured."
        ::= { jnxJsChassisClusterTrapObjects 23 }

    jnxJsChHAPeerBfdSeverity OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object reflects the severity;
             whether it is minor or major."
        ::= { jnxJsChassisClusterTrapObjects 24 }

    jnxJsChHAPeerBfdReason OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "This object contains HA peer BFD down reason."
        ::= { jnxJsChassisClusterTrapObjects 25 }

END
