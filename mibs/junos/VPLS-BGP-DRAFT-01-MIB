VPLS-BGP-DRAFT-01-MIB DEFINITIONS ::= BEGIN 

      IMPORTS 
      MODULE-IDENTITY, OBJECT-TYPE,  
       Unsigned32, transmission  
         FROM SNMPv2-SMI                    -- RFC2578

      OBJECT-GROUP
         FROM SNMPv2-CONF                   -- RFC2580 

      RowStatus, StorageType,  TEXTUAL-CONVENTION
         FROM SNMPv2-TC                     -- RFC2579 

      SnmpAdminString  
         FROM SNMP-FRAMEWORK-MIB            -- RFC3411

      jnxExperiment
	FROM JUNIPER-SMI

      jnxVplsConfigIndex, jnxVplsPwBindIndex 
               FROM VPLS-GENERIC-DRAFT-01-MIB;

   jnxVplsBgpDraft01MIB MODULE-IDENTITY  
      LAST-UPDATED "200612061200Z"  -- 06 Dec 2006 12:00:00 GMT
      ORGANIZATION "Layer 2 Virtual Private Networks (L2VPN) 
                                 Working  Group"  
      CONTACT-INFO  
          "  
           V<PERSON> <PERSON><PERSON> Shah  
           Email: <EMAIL>  

           The L2VPN Working Group (<NAME_EMAIL>,  
           http://www.ietf.org/html.charters/l2vpn-charter.html)  
           "  
      DESCRIPTION  
          "Copyright (C) The IETF Trust (2010). The initial  
           version of this MIB module was published in RFC XXXX.  
   -- RFC Editor: Please replace XXXX with RFC number & remove 
   --                    this note.  

           For full legal notices see the RFC itself or see: 
           http://www.ietf.org/copyrights/ianamib.html 

           This MIB module contains managed object definitions for   
           BGP signalled Virtual Private LAN Services as in 
           [RFC4761]

           This MIB module enables the use of any underlying PseudoWire 
           network. "  

      -- Revision history.  
      REVISION  
          "200612061200Z"  -- 06 Dec 2006 12:00:00 GMT
      DESCRIPTION "Initial version published as part of RFC YYYY." 
   -- RFC Editor: please replace YYYY with IANA assigned value, and  
   -- delete this note.  
         ::= { jnxExperiment 10 } 
   -- RFC Editor: please replace XXXX with IANA assigne value, and  
   -- delete this note.  

   -- VPLS BGP specific Textual Conventions.

   -- JnxVplsBgpRouteDistinguisher ::= TEXTUAL-CONVENTION
   -- STATUS        current
   -- DESCRIPTION
   --      "Syntax for a route distinguisher. For a complete
   --        definition of a route distinguisher, see [RFC4364].
   --        For more details on use of a route distinguisher
   --        for a VPLS service, see [RFC4761]"
   --   REFERENCE
   --       "[RFC4364]"
   --   SYNTAX  OCTET STRING(SIZE (0..256))

   -- JnxVplsBgpRouteTarget ::= TEXTUAL-CONVENTION
   --   STATUS        current
   --   DESCRIPTION
   --       "Syntax for a route target. For a complete
   --        definition of a route target, see [RFC4364]."
   --   REFERENCE
   --       "[RFC4364]"
   --   SYNTAX  OCTET STRING(SIZE (0..256))    

   -- Top-level components of this MIB.  

   -- Tables, Scalars  
   jnxVplsBgpObjects       OBJECT IDENTIFIER  
                                 ::= { jnxVplsBgpDraft01MIB 1 }  
   -- Conformance  
   jnxVplsBgpConformance   OBJECT IDENTIFIER   
                                 ::= { jnxVplsBgpDraft01MIB 2 }  

      -- Vpls Bgp Config Table

      jnxVplsBgpConfigTable OBJECT-TYPE 
          SYNTAX          SEQUENCE OF JnxVplsBgpConfigEntry 
          MAX-ACCESS      not-accessible 
          STATUS          current 
          DESCRIPTION     
               "This table specifies information for configuring
                and monitoring BGP specific parameters for 
                Virtual Private Lan Services(VPLS)." 
          ::= { jnxVplsBgpObjects 1 } 

      jnxVplsBgpConfigEntry OBJECT-TYPE 
          SYNTAX          JnxVplsBgpConfigEntry 
          MAX-ACCESS      not-accessible 
          STATUS          current 
          DESCRIPTION     
           "A row in this table represents BGP specific information 
            for Virtual Private Lan Service(VPLS) in a packet network. 
            It is indexed by jnxVplsConfigIndex, which uniquely 
            identifies a single instance of a VPLS service.   

            A row is automatically created when a VPLS service is 
            configured using BGP signalling.

            None of the read-create objects values can be 
            changed when jnxVplsRowStatus is in the active(1) 
            state. Changes are allowed when the jnxVplsRowStatus 
            is in notInService(2) or notReady(3) states only.   
            If the operator need to change one of the values 
            for an active row the jnxVplsConfigRowStatus should be 
            first changed to notInService(2), the objects may 
            be changed now, and later to active(1) in order to 
            re-initiate the signaling process with the new 
            values in effect.  
            "  
          INDEX           { jnxVplsConfigIndex } 
          ::= { jnxVplsBgpConfigTable 1 } 

     JnxVplsBgpConfigEntry ::= 
        SEQUENCE { 
         jnxVplsBgpConfigVERangeSize         Unsigned32
        } 

     jnxVplsBgpConfigVERangeSize   OBJECT-TYPE
        SYNTAX        Unsigned32 (0..65535)
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Specifies the size of the range of VE ids in this 
             VPLS service. This number controls the size of the 
             label block advertised for this VE by the PE.  
             A value of 0 indicates that the range is not 
             configured and the PE derives the range value 
             from received advertisements from other PEs."
        DEFVAL           { 0 }
        ::= { jnxVplsBgpConfigEntry 1 }

     -- Vpls Edge Device (VE) Identifier Table

     jnxVplsBgpVETable OBJECT-TYPE
         SYNTAX        SEQUENCE OF JnxVplsBgpVEEntry
         MAX-ACCESS    not-accessible
         STATUS        current
         DESCRIPTION
            "This table associates VPLS Edge devices to a VPLS service"
         ::= { jnxVplsBgpObjects 2 }

     jnxVplsBgpVEEntry OBJECT-TYPE
         SYNTAX        JnxVplsBgpVEEntry
         MAX-ACCESS    not-accessible
         STATUS        current
         DESCRIPTION
            "An entry in this table is created for each VE Id 
             configured on a PE for a particular VPLS service 
             instance."
         INDEX  { jnxVplsConfigIndex, jnxVplsBgpVEId }
         ::= { jnxVplsBgpVETable 1 }

     JnxVplsBgpVEEntry ::= 
        SEQUENCE {
          jnxVplsBgpVEId          Unsigned32,
          jnxVplsBgpVEName        SnmpAdminString,
          jnxVplsBgpVEPreference  Unsigned32,
          jnxVplsBgpVERowStatus   RowStatus,
          jnxVplsBgpVEStorageType StorageType
        }

     jnxVplsBgpVEId OBJECT-TYPE
        SYNTAX        Unsigned32 (1..65535)
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "A secondary index identifying a VE within an
             instance of a VPLS service."
        ::= { jnxVplsBgpVEEntry 1 }

     jnxVplsBgpVEName OBJECT-TYPE
        SYNTAX        SnmpAdminString
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Descriptive name for the site or u-PE assciated with 
             this VE Id."
        DEFVAL { "" }
        ::= { jnxVplsBgpVEEntry 2 }

     jnxVplsBgpVEPreference OBJECT-TYPE
        SYNTAX        Unsigned32 (0..65535)
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "Specifies the preference of the VE Id on this PE 
             if the site is multi-homed and VE Id is re-used."
        DEFVAL           { 0 }
        ::= { jnxVplsBgpVEEntry 3 }

     jnxVplsBgpVERowStatus OBJECT-TYPE
        SYNTAX        RowStatus
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "This variable is used to create, modify, and/or
             delete a row in this table.  When a row in this
             table is in active(1) state, no objects in that row
             can be modified except jnxVplsBgpSiteRowStatus."
        ::= { jnxVplsBgpVEEntry 5 }

     jnxVplsBgpVEStorageType OBJECT-TYPE   
          SYNTAX        StorageType  


          MAX-ACCESS    read-only  
          STATUS        current  
          DESCRIPTION  
               "This variable indicates the storage type for this row."  
          DEFVAL { volatile }
          ::= { jnxVplsBgpVEEntry 6 }  

      -- VPLS BGP PW Binding Table 

      jnxVplsBgpPwBindTable OBJECT-TYPE 
          SYNTAX          SEQUENCE OF JnxVplsBgpPwBindEntry 
          MAX-ACCESS      not-accessible 
          STATUS          current 
          DESCRIPTION    
               "This table provides BGP specific information for 
                an association between a VPLS service and the 
                corresponding Pseudo Wires. A service can have more 
                than one Pseudo Wire association. Pseudo Wires are 
                defined in the pwTable." 
          ::= { jnxVplsBgpObjects 3 } 

      jnxVplsBgpPwBindEntry OBJECT-TYPE 
          SYNTAX          JnxVplsBgpPwBindEntry 
          MAX-ACCESS      not-accessible 
          STATUS          current 
          DESCRIPTION     
               "Each row represents an association between a 
                VPLS instance and one or more Pseudo Wires 
                defined in the pwTable. Each index is unique 
                in describing an entry in this table. However
                both indexes are required to define the one 
                to many association of service to pseudowire.

                An entry in this table in instantiated only when
                BGP signalling is used to configure VPLS service.

                Each entry in this table provides BGP specific
                information for the VPlS represented by 
                jnxVplsConfigIndex." 
          INDEX  { jnxVplsConfigIndex, jnxVplsPwBindIndex } 
          ::= { jnxVplsBgpPwBindTable 1 } 

      JnxVplsBgpPwBindEntry ::= 
          SEQUENCE { 
              jnxVplsBgpPwBindLocalVEId        Unsigned32,
              jnxVplsBgpPwBindRemoteVEId       Unsigned32 
          } 



      jnxVplsBgpPwBindLocalVEId   OBJECT-TYPE 
           SYNTAX          Unsigned32 (1..65535)
           MAX-ACCESS      read-only 
           STATUS          current 
           DESCRIPTION     
                "Identifies the local VE that this Pseudo Wire
                 is associated with."
          ::= { jnxVplsBgpPwBindEntry 1 } 

      jnxVplsBgpPwBindRemoteVEId   OBJECT-TYPE 
           SYNTAX          Unsigned32 (1..65535)
           MAX-ACCESS      read-only 
           STATUS          current 
           DESCRIPTION     
                "Identifies the remote VE that this Pseudo Wire
                 is associated with."
          ::= { jnxVplsBgpPwBindEntry 2 } 

     END       

