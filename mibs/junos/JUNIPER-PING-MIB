--
-- Juniper Enterprise Specific MIB: Ping MIB
--
-- Copyright (c) 2001-2021, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-PING-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, OBJECT-<PERSON>ENTITY, Unsigned32,
    NOTIFICATION-TYPE
        FROM SNMPv2-SMI              -- RFC2578
    InterfaceIndexOrZero
        FROM IF-MIB                  -- RFC2863
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB      -- RFC2571
    DisplayString, DateAndTime, TruthValue
        FROM SNMPv2-TC
    pingResultsEntry, pingProbeHistoryEntry, pingCtlTargetAddressType,
    pingCtlTargetAddress, pingResultsOperStatus, pingResultsIpTargetAddressType,
    pingResultsIpTargetAddress, pingResultsMinRtt, pingResultsMaxRtt,
    pingResultsAverageRtt, pingResultsProbeResponses, pingResultsSentProbes,
    pingResultsRttSumOfSquares, pingResultsLastGoodProbe,
    OperationResponseStatus
        FROM DISMAN-PING-MIB
    InetPortNumber
        FROM INET-ADDRESS-MIB
    jnxMibs, jnxPingNotifications
        FROM JUNIPER-SMI;

jnxPingMIB MODULE-IDENTITY
    LAST-UPDATED "200911180000Z" -- November 18 00:00:00 2009 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
		     Juniper Networks, Inc.
		     1133 Innovation Way
		     Sunnyvale, CA 94089
		     E-mail: <EMAIL>"

    DESCRIPTION
            "This is Juniper Networks' implementation of enterprise specific
             portions of pingMib.  Any data stored in this MIB has directly
             related entries in mib-2, pingMIB."

    -- revision history
    REVISION "202107190000Z" -- July 19 00:00:00 2021 UTC
    DESCRIPTION
             "Updated RTT, Jitter Traps with results,
              Removed ununsed jnxPingMaxRttThreshold and stale entries."
    REVISION "201109200000Z" -- September 20 00:00:00 2011 UTC
    DESCRIPTION
            "Updated the jnxPingCtlTargetPort description."
    REVISION "200911180000Z" -- November 18 00:00:00 2009 UTC
    DESCRIPTION
            "Added jnxPingCtlEXseriesHWTimeStamp to jnxPingCtlTable."
    REVISION "200904200000Z" -- April 20 00:00:00 2009 UTC
    DESCRIPTION
            "Added jnxPingCtlTargetPort to jnxPingCtlTable."
    REVISION "200505010000Z" -- May 1 00:00:00 2005 UTC
    DESCRIPTION
            "Added jnxPingCtlTargetPort to jnxPingCtlTable."
    REVISION "200404150000Z" -- April 15 00:00:00 2004 UTC
    DESCRIPTION
            "Added traps plus additional results & history data."
    ::= { jnxMibs 7 }


jnxPingObjects      OBJECT IDENTIFIER ::= { jnxPingMIB 1 }

 -- The registration node for ping implementation types

jnxPingImplementationTypeDomains OBJECT IDENTIFIER ::= { jnxPingMIB 2 }

jnxPingIcmpTimeStamp OBJECT-IDENTITY
    STATUS      current
    DESCRIPTION
        "Indicates that an implementation is using the Internet
        Control Message Protocol (ICMP) 'TimeStamp' facility.
        This probe-type provides egress and ingress delay measurements."
    ::= { jnxPingImplementationTypeDomains 1 }

jnxPingHttpGet OBJECT-IDENTITY
    STATUS      current
    DESCRIPTION
        "Indicates that an implementation is using an HTTP GET query to
        calculate a round trip time."
    ::= { jnxPingImplementationTypeDomains 2 }

jnxPingHttpGetMetadata OBJECT-IDENTITY
    STATUS      current
    DESCRIPTION
        "Indicates that an implementation is using an HTTP GET Metadata query to
        calculate a round trip time."
    ::= { jnxPingImplementationTypeDomains 3 }

jnxPingDnsQuery OBJECT-IDENTITY
    STATUS      current
    DESCRIPTION
        "Indicates that an implementation is using a DNS query to
        calculate a round trip time."
    ::= { jnxPingImplementationTypeDomains 4 }

jnxPingNtpQuery OBJECT-IDENTITY
    STATUS      current
    DESCRIPTION
        "Indicates that an implementation is using an NTP query to
        calculate a round trip time."
    ::= { jnxPingImplementationTypeDomains 5 }

jnxPingUdpTimestamp OBJECT-IDENTITY
    STATUS      current
    DESCRIPTION
        "Indicates that an implementation is using a UDP timestamp query to
        calculate a round trip time. This probe-type provides egress and
        ingress delay measurements"
    ::= { jnxPingImplementationTypeDomains 6 }


--
-- pingCtlTable extensions
--

jnxPingCtlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines the jnxPing Control Table for providing enterprise specific
         options to the corresponding pingCtlTable entry."
   ::= { jnxPingObjects 2 }

jnxPingCtlEntry OBJECT-TYPE
    SYNTAX      JnxPingCtlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Defines an entry in the jnxPingCtlTable.  This essentially
         augments the pingCtlTable with additional objects."
    INDEX {
             jnxPingCtlOwnerIndex,
             jnxPingCtlTestName
          }
    ::= { jnxPingCtlTable 1 }

JnxPingCtlEntry ::=
    SEQUENCE {
        jnxPingCtlOwnerIndex             SnmpAdminString,
        jnxPingCtlTestName               SnmpAdminString,
        jnxPingCtlIfName                 DisplayString,
        jnxPingCtlRoutingIfIndex         InterfaceIndexOrZero,
        jnxPingCtlRoutingIfName          DisplayString,
        jnxPingCtlRoutingInstanceName    DisplayString,
        jnxPingCtlRttThreshold           Unsigned32,
        jnxPingCtlRttStdDevThreshold     Unsigned32,
        jnxPingCtlRttJitterThreshold     Unsigned32,
        jnxPingCtlEgressTimeThreshold    Unsigned32,
        jnxPingCtlEgressStdDevThreshold  Unsigned32,
        jnxPingCtlEgressJitterThreshold  Unsigned32,
        jnxPingCtlIngressTimeThreshold   Unsigned32,
        jnxPingCtlIngressStddevThreshold Unsigned32,
        jnxPingCtlIngressJitterThreshold Unsigned32,
        jnxPingCtlTrapGeneration         BITS,
        jnxPingCtlTargetPort             InetPortNumber,
        jnxPingCtlJseriesHWTimeStamp     TruthValue,
        jnxPingCtlOneWayHWTimeStamp      TruthValue,
        jnxPingCtlMovAvgSize             Unsigned32,
        jnxPingCtlMXseriesHWTimeStamp    TruthValue,
        jnxPingCtlEXseriesHWTimeStamp    TruthValue
    }

jnxPingCtlOwnerIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(0..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "To facilitate the provisioning of access control by a
       security administrator using the View-Based Access
       Control Model (RFC 2575, VACM) for tables in which
       multiple users may need to independently create or
       modify entries, the initial index is used as an 'owner
       index'.  Such an initial index has a syntax of
       SnmpAdminString, and can thus be trivially mapped to a
       securityName or groupName as defined in VACM, in
       accordance with a security policy.

       When used in conjunction with such a security policy all
       entries in the table belonging to a particular user (or
       group) will have the same value for this initial index.
       For a given user's entries in a particular table, the
       object identifiers for the information in these entries
       will have the same subidentifiers (except for the 'column'
       subidentifier) up to the end of the encoded owner index.
       To configure VACM to permit access to this portion of the
       table, one would create vacmViewTreeFamilyTable entries
       with the value of vacmViewTreeFamilySubtree including
       the owner index portion, and vacmViewTreeFamilyMask
       'wildcarding' the column subidentifier.  More elaborate
       configurations are possible."
    ::= { jnxPingCtlEntry 1 }

jnxPingCtlTestName OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(0..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the ping test.  This is locally unique, within
        the scope of an pingCtlOwnerIndex."
    ::= { jnxPingCtlEntry 2 }

jnxPingCtlIfName  OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..24))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Setting this object to an interface name prior to starting a remote
        ping operation directs the ping probes to be transmitted over the
        specified interface.  To specify the interface index instead, see
        pingCtlIfIndex.  The interface name must be specified under interfaces
        statement of the JUNOS configuration.  A zero length string value for
        this object means that this option is not enabled.  The following
        values may be set simultaneously, however, only one value is used.
        The precedence order is a follows:
            pingCtlIfIndex (see pingCtlTable in pingMIB)
            jnxPingCtlIfName
            jnxPingCtlRoutingIfIndex   (deprecated)
            jnxPingCtlRoutingIfName    (deprecated)
            jnxPingCtlRoutingInstanceName"
    DEFVAL { ''H }
    ::= { jnxPingCtlEntry 3 }

jnxPingCtlRoutingIfIndex OBJECT-TYPE
    SYNTAX      InterfaceIndexOrZero
    MAX-ACCESS  read-create
    STATUS      deprecated
    DESCRIPTION
        "Use this option to specify the routing instance used when directing
        outgoing ping packets.  The interface ifIndex specified should be in
        the desired routing instance table.  The interface specified will not
        necessarily be the interface packets are transmitted on.  By default,
        the source address of the packets will be set to the address of the
        interface chosen.  pingCtlSourceAddress should be used to override
        the choice for source address if necessary. A value of zero for this
        object means that this option is not enabled.
        NOTE: deprecated by jnxPingCtlRoutingInstanceName"
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 4 }

jnxPingCtlRoutingIfName OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..24))
    MAX-ACCESS  read-create
    STATUS      deprecated
    DESCRIPTION
        "Use this option to specify the routing instance used when directing
        outgoing ping packets.  The interface name specified should be in
        the desired routing instance table.  The interface specified will not
        necessarily be the interface packets are transmitted on.  By default,
        the source address of the packets will be set to the address of the
        interface chosen.  pingCtlSourceAddress should be used to override
        the choice for source address if necessary.
        NOTE: deprecated by jnxPingCtlRoutingInstanceName"
    DEFVAL { ''H }
    ::= { jnxPingCtlEntry 5 }

jnxPingCtlRoutingInstanceName  OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..31))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Use this option to specify the name of the routing instance used when
        directing outgoing ping packets.  The instance name specified must be
        configured under routing-instances of the JUNOS configuration."
    DEFVAL { ''H }
    ::= { jnxPingCtlEntry 6 }

jnxPingCtlRttThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..6000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Maximum round trip time allowed.  If this threshold is crossed
        by any probe, a jnxPingRttThresholdExceeded trap will be sent."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 7 }

jnxPingCtlRttStdDevThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..6000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum round trip time standard deviation allowed over the
        course of any test.  If the calculated standard deviation of the
        round trip time at the end of any test exceeds this threshold,
        a jnxPingRttStdDevThresholdExceeded trap will be sent."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 8 }

jnxPingCtlRttJitterThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..6000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum allowed jitter in the round trip time over the course
        of any test.  Jitter is defined as the difference between the
        maximum and minimum round trip times measured over the course of
        a single test (jnxPingResultsMaxRttUs minus jnxPingResultsMinRttUs).
        If the measured jitter exceeds this threshold, a
        jnxPingRttJitterThresholdExceeded trap will be sent."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 9 }

jnxPingCtlEgressTimeThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..60000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Maximum egress trip time allowed.  If this threshold is crossed by
        any probe, a jnxPingEgressThresholdExceeded trap will be sent.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.  Currently jnxPingIcmpTimeStamp is the only
        supported probe type with this property."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 10 }

jnxPingCtlEgressStdDevThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..60000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum egress trip time standard deviation allowed over the
        course of any test.  If the calculated standard deviation of the
        egress trip time at the end of any test exceeds this threshold,
        a jnxPingEgressStdDevThresholdExceeded trap will be sent.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.  Currently jnxPingIcmpTimeStamp is the only
        supported probe type with this property."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 11 }

jnxPingCtlEgressJitterThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..60000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum allowed jitter in the egress trip time over the course
        of any test.  Jitter is defined as the difference between the
        maximum and minimum egress trip times measured over the course of
        a single test (jnxPingResultsMaxSrcDstt minus jnxPingResultsMinSrcDstt).
        If the measured jitter exceeds this threshold, a
        jnxPingEgressJitterThresholdExceeded trap will be sent.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.  Currently jnxPingIcmpTimeStamp is the only
        supported probe type with this property."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 12 }

jnxPingCtlIngressTimeThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..60000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Maximum ingress trip time allowed.  If this threshold is crossed by
        any probe, a jnxPingIngressThresholdExceeded trap will be sent.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.  Currently jnxPingIcmpTimeStamp is the only
        supported probe type with this property."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 13 }

jnxPingCtlIngressStddevThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..60000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum ingress trip time standard deviation allowed over the
        course of any test.  If the calculated standard deviation of the
        ingress trip time at the end of any test exceeds this threshold,
        a jnxPingIngressStddevThresholdExceeded trap will be sent.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.  Currently jnxPingIcmpTimeStamp is the only
        supported probe type with this property."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 14 }

jnxPingCtlIngressJitterThreshold  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..60000000)
    UNITS       "microseconds"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum allowed jitter in the ingress trip time over the course
        of any test.  Jitter is defined as the difference between the
        maximum and minimum ingress trip times measured over the course of
        a single test (jnxPingResultsMaxDstSrct minus jnxPingResultsMinDstSrct).
        If the measured jitter exceeds this threshold, a
        jnxPingIngressJitterThresholdExceeded trap will be sent.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.  Currently jnxPingIcmpTimeStamp is the only
        supported probe type with this property."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 15 }

 jnxPingCtlTrapGeneration OBJECT-TYPE
    SYNTAX      BITS {
                   rttThreshold(0),
                   rttStdDevThreshold(1),
                   rttJitterThreshold(2),
                   egressThreshold(3),
                   egressStdDevThreshold(4),
                   egressJitterThreshold(5),
                   ingressThreshold(6),
                   ingressStdDevThreshold(7),
                   ingressJitterThreshold(8),
                   maxrttThreshold(9)
                  }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of this object determines when and if
        to generate a notification for this entry:

        rttThreshold(0)   - Generate a jnxPingRttThresholdExceeded
            notification when the configured rtt threshold
            is exceeded.
        rttStdDevThreshold(1) - Generate a jnxPingRttStdDevThresholdExceeded
            notification when the configured rtt standard deviation
            threshold is exceeded.
        rttJitterThreshold(2) - Generate a jnxPingRttJitterThresholdExceeded
            notification when the configured rtt jitter threshold
            is exceeded.
        egressThreshold(3)   - Generate a jnxPingEgressThresholdExceeded
            notification when the configured egress threshold is exceeded.
            This applies only if the probe type supports one way measurments.
        egressStdDevThreshold(4) - Generate a
            jnxPingEgressStdDevThresholdExceeded notification when the
            configured egress standard deviation threshold is exceeded.
            This applies only if the probe type supports one way measurments.
        egressJitterThreshold(5) - Generate a
            jnxPingEgressJitterThresholdExceeded notification when the
            configured egress jitter threshold is exceeded.
            This applies only if the probe type supports one way measurments.
        ingressThreshold(6)   - Generate a jnxPingIngressThresholdExceeded
            notification when the configured ingress threshold is exceeded.
            This applies only if the probe type supports one way measurments.
        ingressStdDevThreshold(7) - Generate a
            jnxPingIngressStdDevThresholdExceeded notification when the
            configured ingress standard deviation threshold is exceeded.
            This applies only if the probe type supports one way measurments.
        ingressJitterThreshold(8) - Generate a
            jnxPingIngressJitterThresholdExceeded notification when the
            configured ingress jitter threshold is exceeded.
            This applies only if the probe type supports one way measurments.
            The value of this object defaults to zero, indicating
            that none of the above options have been selected.
        maxrttThreshold(9) - Generate a jnxPingMaxRttThresholdExceeded
            notification at the end of test session when rtt of any of the probe
            exceeds the configured rtt threshold."
    ::= { jnxPingCtlEntry 16 }

jnxPingCtlTargetPort  OBJECT-TYPE
    SYNTAX      InetPortNumber (7 | 49152..65535)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The target UDP/TCP port used by the probe.
         When ICMP ping is used, jnxPingCtlTargetPort value will be shown as 0."
    ::= { jnxPingCtlEntry 17 }

jnxPingCtlJseriesHWTimeStamp  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Use to enable the RPM Hardware Timestamp feature on J-series routers.
         For M/T series routers, use the jnxPingCtlIfName and
         pingCtlByPassRouteTable objects to redirect probes to the AS Pic.
         For those routers, this object must have the value: false."
    DEFVAL { false }
    ::= { jnxPingCtlEntry 18 }

jnxPingCtlOneWayHWTimeStamp  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Use this object to enable Hardware Timestamp-based one-way
         measurements.  If this object is not enabled (ie, set to true),
         no Hardware Timestamp based one way measurements or calculations
         will be performed for this control entry.  This object applies to
         all Juniper routers.  Note, due to clock synchronization artifacts,
         many one-way jitter measurements & calculations may include signifacant
         variations, in some cases  orders of magnitude greater than the round
         trip times."
    DEFVAL { false }
    ::= { jnxPingCtlEntry 19 }

jnxPingCtlMovAvgSize  OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The Juniper RPM feature maintains a set of the most recent probe
        measurements & provides the same calculations over that collection
        as provided over a test (ie, average, standard deviation, etc).  This
        data is available via the jnx-rpm mib or via the CLI/XML.  The
        number of samples maintained in this moving collection is specified
        by this object.  This value must be less than the number of samples
        maintained in the history table (ie, pingCtlMaxRows)."
    DEFVAL { 0 }
    ::= { jnxPingCtlEntry 20 }

jnxPingCtlMXseriesHWTimeStamp  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Use to enable the RPM Hardware Timestamp feature on MX-series routers.
         For M/T series routers, use the jnxPingCtlIfName and
         pingCtlByPassRouteTable objects to redirect probes to the AS PIC.
         For those routers, this object must have the value: false."
    DEFVAL { false }
    ::= { jnxPingCtlEntry 21 }

jnxPingCtlEXseriesHWTimeStamp  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Use to enable the RPM Hardware Timestamp feature on EX-series switches.
         For thoses, this object must have the value: false."
    DEFVAL { false }
    ::= { jnxPingCtlEntry 22 }

--
-- Ping Results Table extensions
--

jnxPingResultsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxPingResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Augments the pingResultsTable with additional data."
   ::= { jnxPingObjects 3 }

jnxPingResultsEntry OBJECT-TYPE
    SYNTAX      JnxPingResultsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry provides additional ping test results."
    AUGMENTS { pingResultsEntry }
    ::= { jnxPingResultsTable 1 }

JnxPingResultsEntry ::=
    SEQUENCE {
        jnxPingResultsRttUs           Unsigned32,
        jnxPingResultsSumRttUs        Unsigned32,
        jnxPingResultsMinRttUs        Unsigned32,
        jnxPingResultsMaxRttUs        Unsigned32,
        jnxPingResultsAvgRttUs        Unsigned32,
        jnxPingResultsStdDevRttUs     Unsigned32,
        jnxPingResultsEgressUs        Unsigned32,
        jnxPingResultsMinEgressUs     Unsigned32,
        jnxPingResultsMaxEgressUs     Unsigned32,
        jnxPingResultsAvgEgressUs     Unsigned32,
        jnxPingResultsStddevEgressUs  Unsigned32,
        jnxPingResultsIngressUs       Unsigned32,
        jnxPingResultsMinIngressUs    Unsigned32,
        jnxPingResultsMaxIngressUs    Unsigned32,
        jnxPingResultsAvgIngressUs    Unsigned32,
        jnxPingResultsStddevIngressUs Unsigned32,
        jnxPingResultsJitterRttUs     Unsigned32,
        jnxPingResultsJitterEgressUs  Unsigned32,
        jnxPingResultsJitterIngressUs Unsigned32,
        jnxPingResultsStatus          OperationResponseStatus,
        jnxPingResultsTime            DateAndTime,
        jnxPingResultsOwnerIndex      SnmpAdminString,
        jnxPingResultsTestName        SnmpAdminString
     }

jnxPingResultsRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The round trip delays measured for the most recent successful probe
        during this test.  Measured in microseconds."
    ::= { jnxPingResultsEntry 1 }

jnxPingResultsSumRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The sum of the round trip delays measured for all the probes
        during this test.  Measured in microseconds."
    ::= { jnxPingResultsEntry 2 }

jnxPingResultsMinRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum of the round trip delays measured for all the probes
        during this test.  Measured in microseconds."
    ::= { jnxPingResultsEntry 3 }

jnxPingResultsMaxRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum of the round trip delays measured for all the probes
        during this test.  Measured in microseconds."
    ::= { jnxPingResultsEntry 4 }

jnxPingResultsAvgRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average of the round trip delays measured for all the probes
        during this test.  Measured in microseconds."
    ::= { jnxPingResultsEntry 5 }

jnxPingResultsStdDevRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The standard deviation of the round trip delays measured
        during this test.  Measured in microseconds."
    ::= { jnxPingResultsEntry 6 }

jnxPingResultsEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The egress trip delays measured for the most recent successful probe
        during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 7 }

jnxPingResultsMinEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum of the egress trip delays measured over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 8 }

jnxPingResultsMaxEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum of the egress trip delays measured over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 9 }

jnxPingResultsAvgEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average of the egress trip delays measured over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 10 }

jnxPingResultsStddevEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The standard deviation of the egress trip delays measured over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 11 }

jnxPingResultsIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The ingress trip delays measured for the most recent successful probe
        during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 12 }

jnxPingResultsMinIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum of the ingress trip delays measured for over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 13 }

jnxPingResultsMaxIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum of the ingress trip delays measured over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 14 }

jnxPingResultsAvgIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average of the ingress trip delays measured over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 15 }

jnxPingResultsStddevIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The standard deviation of the ingress trip delays measured over all
        probes during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 16 }

jnxPingResultsJitterRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The jitter of the round trip delays measured for all the probes
        during this test.  Measured in microseconds."
    ::= { jnxPingResultsEntry 17 }

jnxPingResultsJitterEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The jitter of the egress trip delays measured for all the probes
        during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 18 }

jnxPingResultsJitterIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The jitter of the ingress trip delays measured for all the probes
        during this test.  Measured in microseconds.
        This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingResultsEntry 19 }

jnxPingResultsStatus OBJECT-TYPE
    SYNTAX      OperationResponseStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The result of the most recent probe."
    ::= { jnxPingResultsEntry 20 }

jnxPingResultsTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Timestamp for when the most recent probe result was determined."
    ::= { jnxPingResultsEntry 21 }

jnxPingResultsOwnerIndex OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object has the same value as pingCtlOwnerIndex and is provided
        for those applications that are unable to parse the value of
        pingCtlOwnerIndex from the instance portion of the OIDs belonging to
        this table."
    ::= { jnxPingResultsEntry 22 }

jnxPingResultsTestName OBJECT-TYPE
    SYNTAX      SnmpAdminString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object has the same value as pingCtlTestName and is provided
        for those applications that are unable to parse the value of
        pingCtlTestName from the instance portion of the OIDs belonging to
        this table."
    ::= { jnxPingResultsEntry 23 }




--
-- Ping History Table extensions
--

jnxPingProbeHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxPingProbeHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Augments the pingHistoryTable with additional data."
   ::= { jnxPingObjects 4 }

jnxPingProbeHistoryEntry OBJECT-TYPE
    SYNTAX      JnxPingProbeHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry provides additional ping history data."
    AUGMENTS { pingProbeHistoryEntry }
    ::= { jnxPingProbeHistoryTable 1 }

JnxPingProbeHistoryEntry ::=
    SEQUENCE {
        jnxPingProbeHistoryResponseUs         Unsigned32,
        jnxPingProbeHistoryJitterUs           Unsigned32,
        jnxPingProbeHistoryResponseEgressUs   Unsigned32,
        jnxPingProbeHistoryResponseIngressUs  Unsigned32,
        jnxPingProbeHistoryEgressJitterUs     Unsigned32,
        jnxPingProbeHistoryIngressJitterUs    Unsigned32
     }

jnxPingProbeHistoryResponseUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in microseconds from when
        a probe was sent to when its response was received or
        when it timed out.  The value of this object is reported
        as 0 when it is not possible to transmit a probe."
    ::= { jnxPingProbeHistoryEntry 1 }

jnxPingProbeHistoryJitterUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time difference measured in microseconds between the maximum
        and minimum round trip times.  Each history entry provides a running
        calculation of the jitter (calculated over the current test) at the
        time a probe was completed."

    ::= { jnxPingProbeHistoryEntry 2 }

jnxPingProbeHistoryResponseEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in microseconds from when
        a probe was sent to when it was received by destination.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.
        For all other probe types, the value is irrelevant and will return 0."
    ::= { jnxPingProbeHistoryEntry 3 }

jnxPingProbeHistoryResponseIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The amount of time measured in microseconds from when
        a probe was sent from the destination to when it was received.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.
        For all other probe types, the value is irrelevant and will return 0."
    ::= { jnxPingProbeHistoryEntry 4 }

jnxPingProbeHistoryEgressJitterUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time difference measured in microseconds between the maximum
        and minimum egress trip times.  Each history entry provides a running
        calculation of the jitter (calculated over the current test) at the
        time a probe was completed.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.
        For all other probe types, the value is irrelevant and will return 0."
    ::= { jnxPingProbeHistoryEntry 5 }

jnxPingProbeHistoryIngressJitterUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time difference measured in microseconds between the maximum
        and minimum ingress trip times.  Each history entry provides a running
        calculation of the jitter (calculated over the current test) at the
        time a probe was completed.
        This applies only if the probe type (pingCtlType) provides one way
        delay measurements.
        For all other probe types, the value is irrelevant and will return 0."
    ::= { jnxPingProbeHistoryEntry 6 }


--
-- Last ping test Results Table
--

jnxPingLastTestResultTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxPingLastTestResultEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Stores result of the most recently completed test. Entry corresponding
         to a test will be created only after completion of first test."
   ::= { jnxPingObjects 5 }

jnxPingLastTestResultEntry OBJECT-TYPE
    SYNTAX      JnxPingLastTestResultEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry provides most recently completed test result."
    INDEX {
             jnxPingCtlOwnerIndex,
             jnxPingCtlTestName
          }
    ::= { jnxPingLastTestResultTable 1 }

JnxPingLastTestResultEntry ::=
    SEQUENCE {
        jnxPingLastTestResultProbeResponses            Unsigned32,
        jnxPingLastTestResultSentProbes                Unsigned32,
        jnxPingLastTestResultSumRttUs                  Unsigned32,
        jnxPingLastTestResultMinRttUs                  Unsigned32,
        jnxPingLastTestResultMaxRttUs                  Unsigned32,
        jnxPingLastTestResultAvgRttUs                  Unsigned32,
        jnxPingLastTestResultStdDevRttUs               Unsigned32,
        jnxPingLastTestResultMinEgressUs               Unsigned32,
        jnxPingLastTestResultMaxEgressUs               Unsigned32,
        jnxPingLastTestResultAvgEgressUs               Unsigned32,
        jnxPingLastTestResultStddevEgressUs            Unsigned32,
        jnxPingLastTestResultMinIngressUs              Unsigned32,
        jnxPingLastTestResultMaxIngressUs              Unsigned32,
        jnxPingLastTestResultAvgIngressUs              Unsigned32,
        jnxPingLastTestResultStddevIngressUs           Unsigned32,
        jnxPingLastTestResultPeakToPeakJitterRttUs     Unsigned32,
        jnxPingLastTestResultPeakToPeakJitterEgressUs  Unsigned32,
        jnxPingLastTestResultPeakToPeakJitterIngressUs Unsigned32,
        jnxPingLastTestResultTime                      DateAndTime
     }

jnxPingLastTestResultProbeResponses OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "responses"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of responses received in the most recently completed test."
    ::= { jnxPingLastTestResultEntry 1 }

jnxPingLastTestResultSentProbes OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "probes"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of this object reflects the number of probes sent in the
        most recently completed test."
    ::= { jnxPingLastTestResultEntry 2 }

jnxPingLastTestResultSumRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The sum of the round trip delays measured for all the probes
        during the most recently completed test. Measured in microseconds."
    ::= { jnxPingLastTestResultEntry 3 }

jnxPingLastTestResultMinRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum of the round trip delays measured for all the probes
        during the most recently completed test. Measured in microseconds."
    ::= { jnxPingLastTestResultEntry 4 }

jnxPingLastTestResultMaxRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum of the round trip delays measured for all the probes
        during the most recently completed test. Measured in microseconds."
    ::= { jnxPingLastTestResultEntry 5 }

jnxPingLastTestResultAvgRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average of the round trip delays measured for all the probes
        during the most recently completed test. Measured in microseconds."
    ::= { jnxPingLastTestResultEntry 6 }

jnxPingLastTestResultStdDevRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The standard deviation of the round trip delays measured during
        the most recently completed test. Measured in microseconds."
    ::= { jnxPingLastTestResultEntry 7 }

jnxPingLastTestResultMinEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum of the egress trip delays measured over all probes
        during the most recently completed test. Measured in microseconds.
        This applies only if the probe type (pingCtlType) provides
        one-way delay measurements.  For all other probe types, their
        values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 8 }

jnxPingLastTestResultMaxEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum of the egress trip delays measured over all probes
        during the most recently completed test. Measured in microseconds.
        This applies only if the probe type (pingCtlType) provides
        one-way delay measurements.  For all other probe types, their
        values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 9 }

jnxPingLastTestResultAvgEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average of the egress trip delays measured over all probes
        during the most recently completed test. Measured in microseconds.
        This applies only if the probe type (pingCtlType) provides
        one-way delay measurements.  For all other probe types, their
        values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 10 }

jnxPingLastTestResultStddevEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The standard deviation of the egress trip delays measured over all
        probes during the most recently completed test. Measured in
        microseconds. This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types, their
        values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 11 }

jnxPingLastTestResultMinIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum of the ingress trip delays measured for over all probes
        during the most recently completed test. Measured in microseconds.
        This applies only if the probe type (pingCtlType) provides
        one-way delay measurements.  For all other probe types, their
        values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 12 }

jnxPingLastTestResultMaxIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum of the ingress trip delays measured over all probes
        during the most recently completed test. Measured in microseconds.
        This applies only if the probe type (pingCtlType) provides
        one-way delay measurements.  For all other probe types, their
        values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 13 }

jnxPingLastTestResultAvgIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The average of the ingress trip delays measured over all probes
        during the most recently completed test. Measured in microseconds.
        This applies only if the probe type (pingCtlType) provides
        one-way delay measurements.  For all other probe types, their
        values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 14 }

jnxPingLastTestResultStddevIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The standard deviation of the ingress trip delays measured over all
        probes during the most recently completed test. Measured in
        microseconds. This applies only if the probe type (pingCtlType)
        provides one-way delay measurements.  For all other probe types,
        their values are irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 15 }

jnxPingLastTestResultPeakToPeakJitterRttUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the difference  between the min and max delays over
        the course of the last completed test. Measured in microseconds."
    ::= { jnxPingLastTestResultEntry 16 }

jnxPingLastTestResultPeakToPeakJitterEgressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the difference  between the min and max egress trip
        delays over the course of the last completed test.  Measured in
        microseconds. This applies only if the probe type (pingCtlType) provides
        one-way delay measurements. For all other probe types, their values are
        irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 17 }

jnxPingLastTestResultPeakToPeakJitterIngressUs OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "microseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the difference  between the min and max ingress trip
        delays over the course of the last completed test.  Measured in
        microseconds. This applies only if the probe type (pingCtlType) provides
        one-way delay measurements. For all other probe types, their values are
        irrelevant and will return 0."
    ::= { jnxPingLastTestResultEntry 18 }

jnxPingLastTestResultTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Timestamp for when the last test was completed."
    ::= { jnxPingLastTestResultEntry 19 }


--
-- Notification definitions
--

    jnxPingNotificationPrefix OBJECT-IDENTITY
        STATUS current
        DESCRIPTION
            "All Juniper-specific ping notifications are registered under
            this branch."
        ::= { jnxPingNotifications 0 }

    jnxPingRttThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinRttUs,
           jnxPingResultsMaxRttUs,
           jnxPingResultsAvgRttUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsRttSumOfSquares,
           pingResultsLastGoodProbe,
           jnxPingCtlRttThreshold,
           jnxPingResultsRttUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the round trip time
             (jnxPingCtlRttThreshold) exceeds the configured
             threshold (jnxPingCtlRttThreshold) and the rttThreshold bit is
             set in jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 1 }

    jnxPingRttStdDevThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinRttUs,
           jnxPingResultsMaxRttUs,
           jnxPingResultsAvgRttUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsRttSumOfSquares,
           pingResultsLastGoodProbe,
           jnxPingCtlRttStdDevThreshold,
           jnxPingResultsStdDevRttUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the standard deviation of
             the round trip time (jnxPingResultsStdDevRttUs) exceeds the
             configured threshold (jnxPingCtlRttStdDevThreshold) and the
             rttStdDevThreshold bit is set in jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 2 }

    jnxPingRttJitterThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinRttUs,
           jnxPingResultsMaxRttUs,
           jnxPingResultsAvgRttUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsRttSumOfSquares,
           pingResultsLastGoodProbe,
           jnxPingCtlRttJitterThreshold,
           jnxPingResultsJitterRttUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the round trip time jitter
             (jnxPingResultsMaxRttUs minus jnxPingResultsMinRttUs) exceeds the
             configured threshold (jnxPingCtlRttJitterThreshold) and the
             rttJitterThreshold bit is set in jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 3 }

    jnxPingEgressThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinEgressUs,
           jnxPingResultsMaxEgressUs,
           jnxPingResultsAvgEgressUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsLastGoodProbe,
           jnxPingCtlEgressTimeThreshold,
           jnxPingResultsEgressUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the egress time
             (jnxPingResultsEgressUs) exceeds the configured
             threshold (jnxPingCtlEgressTimeThreshold) and the
             egressThreshold bit is set in jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 4 }

    jnxPingEgressStdDevThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinEgressUs,
           jnxPingResultsMaxEgressUs,
           jnxPingResultsAvgEgressUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsLastGoodProbe,
           jnxPingResultsStddevEgressUs,
           jnxPingCtlEgressStdDevThreshold,
           jnxPingResultsStddevEgressUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the standard deviation of
             the egress time (jnxPingResultsStddevEgressUs) exceeds the
             configured threshold (jnxPingCtlEgressStdDevThreshold) and the
             egressStdDevThreshold bit is set in jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 5 }

    jnxPingEgressJitterThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinEgressUs,
           jnxPingResultsMaxEgressUs,
           jnxPingResultsAvgEgressUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsLastGoodProbe,
           jnxPingCtlEgressJitterThreshold,
           jnxPingResultsJitterEgressUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the egress time jitter
             (jnxPingResultsMaxEgressUs minus jnxPingResultsMinEgressUs)
             exceeds the configured threshold (jnxPingCtlEgressJitterThreshold)
             and the egressJitterThreshold bit is set in
             jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 6 }

    jnxPingIngressThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinIngressUs,
           jnxPingResultsMaxIngressUs,
           jnxPingResultsAvgIngressUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsLastGoodProbe,
           jnxPingCtlIngressTimeThreshold,
           jnxPingResultsIngressUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the ingress time
             (jnxPingResultsIngressUs) exceeds the configured
             threshold (jnxPingCtlIngressTimeThreshold) and the
             ingressThreshold bit is set in jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 7 }

    jnxPingIngressStddevThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinIngressUs,
           jnxPingResultsMaxIngressUs,
           jnxPingResultsAvgIngressUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsLastGoodProbe,
           jnxPingCtlIngressStddevThreshold,
           jnxPingResultsStddevIngressUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the standard deviation of
             the ingress time (jnxPingResultsStddevIngressUs) exceeds the
             configured threshold (jnxPingCtlIngressStddevThreshold) and the
             ingressStdDevThreshold bit is set in jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 8 }

    jnxPingIngressJitterThresholdExceeded NOTIFICATION-TYPE
         OBJECTS {
           pingCtlTargetAddressType,
           pingCtlTargetAddress,
           pingResultsOperStatus,
           pingResultsIpTargetAddressType,
           pingResultsIpTargetAddress,
           jnxPingResultsMinIngressUs,
           jnxPingResultsMaxIngressUs,
           jnxPingResultsAvgIngressUs,
           pingResultsProbeResponses,
           pingResultsSentProbes,
           pingResultsLastGoodProbe,
           jnxPingCtlIngressJitterThreshold,
           jnxPingResultsJitterIngressUs
         }
         STATUS  current
         DESCRIPTION
             "This notification is generated when the ingress time jitter
             (jnxPingResultsMaxIngressUs minus jnxPingResultsMinIngressUs)
             exceeds the configured threshold (jnxPingCtlIngressJitterThreshold)
             and the ingressJitterThreshold bit is set in
             jnxPingCtlTrapGeneration."
         ::= { jnxPingNotificationPrefix 9 }

    jnxPingMaxRttThresholdExceeded NOTIFICATION-TYPE
        OBJECTS {
          pingCtlTargetAddressType,
          pingCtlTargetAddress,
          pingResultsOperStatus,
          pingResultsIpTargetAddressType,
          pingResultsIpTargetAddress,
          jnxPingResultsMinRttUs,
          jnxPingResultsMaxRttUs,
          jnxPingResultsAvgRttUs,
          pingResultsProbeResponses,
          pingResultsSentProbes,
          pingResultsRttSumOfSquares,
          pingResultsLastGoodProbe,
          jnxPingCtlRttThreshold,
          jnxPingResultsRttUs
        }
        STATUS  current
        DESCRIPTION
            "This notification is generated when the max round trip time
            (jnxPingCtlMaxRttThreshold) exceeds the configured
            threshold (jnxPingCtlMaxRttThreshold) and the maxrttThreshold bit is
            set in jnxPingCtlTrapGeneration."
        ::= { jnxPingNotificationPrefix 10 }
END
