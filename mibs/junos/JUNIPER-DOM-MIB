--
-- Juniper Enterprise Specific MIB: SFF Digital Optical Monitor MIB
-- 
-- Copyright (c) 2010, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-DOM-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Integer32, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    DateAndTime, TEXTUAL-CONVENTION
        FROM SNMPv2-TC
    jnxDomMibRoot, jnxDomNotifications, jnxDomLaneNotifications
        FROM JUNIPER-SMI
    ifIndex, ifDescr
        FROM IF-MIB;

-- DOM Alarm and Warning Type

jnxDomMib MODULE-IDENTITY
    LAST-UPDATED "201403200000Z" -- Thu Mar 03 2009 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"

    DESCRIPTION
            "This MIB module defines objects used for Digital Optical 
             Monitor on  interface of Juniper products."
    REVISION      "201403200000Z"
    DESCRIPTION
            "Change XFP references to generic SFF in descriptions.  Add
             DOM lane table."
    REVISION      "200912230000Z" 
    DESCRIPTION
            "Initial revision."
    ::= { jnxDomMibRoot 1 }

JnxDomAlarmId ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION
            "Identifies specific DOM alarms that may exist on an
             interface."
    SYNTAX     BITS {
        domRxLossSignalAlarm(0),               -- Input Loss of signal
        domRxCDRLossLockAlarm(1),              -- Input Loss of Lock
        domRxNotReadyAlarm(2),                 -- Input rx path
        domRxLaserPowerHighAlarm(3),           -- Input laser power
        domRxLaserPowerLowAlarm(4),            -- Input laser power
        domTxLaserBiasCurrentHighAlarm(5),     -- Output laser bias current
        domTxLaserBiasCurrentLowAlarm(6),      -- Output laser bias current
        domTxLaserOutputPowerHighAlarm(7),     -- Output laser power
        domTxLaserOutputPowerLowAlarm(8),      -- Output laser power
        domTxDataNotReadyAlarm(9),             -- Output A/D data not ready
        domTxNotReadyAlarm(10),                -- Output tx path
        domTxLaserFaultAlarm(11),              -- Output laser safety
        domTxCDRLossLockAlarm(12),             -- Output CDR
        domModuleTemperatureHighAlarm(13),     -- Module temperature
        domModuleTemperatureLowAlarm(14),      -- Module temperature
        domModuleNotReadyAlarm(15),            -- Module MOD_NR
        domModulePowerDownAlarm(16),           -- Module P_DOWN
        domLinkDownAlarm(17),                  -- Wire Unplugged or Down
        domModuleRemovedAlarm(18),             -- Module Unplugged or Down
        domModuleVoltageHighAlarm(19),         -- Module Voltage High
        domModuleVoltageLowAlarm(20)           -- Module Voltage Low
    }

JnxDomWarningId ::= TEXTUAL-CONVENTION
    STATUS     current
    DESCRIPTION
            "Identifies specific DOM warnings that may exist on an
             interface."
    SYNTAX     BITS {
        domRxLaserPowerHighWarning(0),         -- Input laser power
        domRxLaserPowerLowWarning(1),          -- Input laser power
        domTxLaserBiasCurrentHighWarning(2),   -- Output laser bias current
        domTxLaserBiasCurrentLowWarning(3),    -- Output laser bias current
        domTxLaserOutputPowerHighWarning(4),   -- Output laser power
        domTxLaserOutputPowerLowWarning(5),    -- Output laser power
        domModuleTemperatureHighWarning(6),    -- Module temperature
        domModuleTemperatureLowWarning(7),     -- Module temperature
        domModuleVoltageHighWarning(8),        -- Module voltage
        domModuleVoltageLowWarning(9)          -- Module voltage
    }

JnxDomLaneAlarmId ::= TEXTUAL-CONVENTION
   STATUS      current
   DESCRIPTION
           "Identifies specific DOM alarms that may exist on
            a Lane of a SFF interface"
   SYNTAX      BITS {
       domLaneRxLaserPowerHighAlarm(0),
       domLaneRxLaserPowerLowAlarm(1),
       domLaneTxLaserBiasCurrentHighAlarm(2),
       domLaneTxLaserBiasCurrentLowAlarm(3),
       domLaneTxLaserOutputPowerHighAlarm(4),
       domLaneTxLaserOutputPowerLowAlarm(5),
       domLaneLaserTemperatureHighAlarm(6),
       domLaneLaserTemperatureLowAlarm(7)
   }

JnxDomLaneWarningId ::= TEXTUAL-CONVENTION
   STATUS      current
   DESCRIPTION
           "Identifies specific DOM warnings that may exist on
            a Lane of a SFF interface"
   SYNTAX      BITS {
       domLaneRxLaserPowerHighWarning(0),
       domLaneRxLaserPowerLowWarning(1),
       domLaneTxLaserBiasCurrentHighWarning(2),
       domLaneTxLaserBiasCurrentLowWarning(3),
       domLaneTxLaserOutputPowerHighWarning(4),
       domLaneTxLaserOutputPowerLowWarning(5),
       domLaneLaserTemperatureHighWarning(6),
       domLaneLaserTemperatureLowWarning(7)
   }

-- 
-- Current DOM Statistics
-- 

jnxDomDigitalMonitoring OBJECT IDENTIFIER ::= { jnxDomMib 1 }

jnxDomCurrentTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF JnxDomCurrentEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "Information about Digital Optical Monitoring for this
             interfaces on this router."
    ::= { jnxDomDigitalMonitoring 1 }

jnxDomCurrentEntry OBJECT-TYPE
    SYNTAX     JnxDomCurrentEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "Information about Digital Optical Monitoring 
             for this interfaces on this router."
    INDEX    { ifIndex }
    ::= { jnxDomCurrentTable 1 }

JnxDomCurrentEntry ::=
    SEQUENCE {
        jnxDomCurrentAlarms
              JnxDomAlarmId,
        jnxDomCurrentAlarmDate
              DateAndTime,
        jnxDomLastAlarms
              JnxDomAlarmId,
        jnxDomCurrentWarnings
              JnxDomWarningId,
        jnxDomCurrentRxLaserPower
              Integer32,
        jnxDomCurrentTxLaserBiasCurrent
              Integer32,
        jnxDomCurrentTxLaserOutputPower
              Integer32,
        jnxDomCurrentModuleTemperature
              Integer32,
        jnxDomCurrentRxLaserPowerHighAlarmThreshold
              Integer32,
        jnxDomCurrentRxLaserPowerLowAlarmThreshold
              Integer32,
        jnxDomCurrentRxLaserPowerHighWarningThreshold
              Integer32,
        jnxDomCurrentRxLaserPowerLowWarningThreshold
              Integer32,
        jnxDomCurrentTxLaserBiasCurrentHighAlarmThreshold
              Integer32,
        jnxDomCurrentTxLaserBiasCurrentLowAlarmThreshold
              Integer32,
        jnxDomCurrentTxLaserBiasCurrentHighWarningThreshold
              Integer32,
        jnxDomCurrentTxLaserBiasCurrentLowWarningThreshold
              Integer32,
        jnxDomCurrentTxLaserOutputPowerHighAlarmThreshold
              Integer32,
        jnxDomCurrentTxLaserOutputPowerLowAlarmThreshold
              Integer32,
        jnxDomCurrentTxLaserOutputPowerHighWarningThreshold
              Integer32,
        jnxDomCurrentTxLaserOutputPowerLowWarningThreshold
              Integer32,
        jnxDomCurrentModuleTemperatureHighAlarmThreshold
              Integer32,
        jnxDomCurrentModuleTemperatureLowAlarmThreshold
              Integer32,
        jnxDomCurrentModuleTemperatureHighWarningThreshold
              Integer32,
        jnxDomCurrentModuleTemperatureLowWarningThreshold
              Integer32,
        jnxDomCurrentModuleVoltage
              Integer32,
        jnxDomCurrentModuleVoltageHighAlarmThreshold
              Integer32,
        jnxDomCurrentModuleVoltageLowAlarmThreshold
              Integer32,
        jnxDomCurrentModuleVoltageHighWarningThreshold
              Integer32,
        jnxDomCurrentModuleVoltageLowWarningThreshold
              Integer32,
        jnxDomCurrentModuleLaneCount
              Integer32
    }

    jnxDomCurrentAlarms OBJECT-TYPE
        SYNTAX      JnxDomAlarmId
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object identifies all the active DOM alarms 
                 on a SFF physical interface on this router."
        ::= { jnxDomCurrentEntry 1 }

    jnxDomCurrentAlarmDate OBJECT-TYPE
        SYNTAX      DateAndTime
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The system date and time when the management subsystem learned
                 of the current alarm event."
        ::= { jnxDomCurrentEntry 2 }

    jnxDomLastAlarms OBJECT-TYPE
        SYNTAX      JnxDomAlarmId
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object identifies a copy of jnxDomCurrentAlarms 
                 before last set or clear."
        ::= { jnxDomCurrentEntry 3 }

    jnxDomCurrentWarnings OBJECT-TYPE
        SYNTAX      JnxDomWarningId
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object identifies all the active DOM warnings
                 on a SFF physical interface on this router."
        ::= { jnxDomCurrentEntry 4 }

    jnxDomCurrentRxLaserPower OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Receiver laser power."
        ::= { jnxDomCurrentEntry 5 }

    jnxDomCurrentTxLaserBiasCurrent OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 mA"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser bias current."
        ::= { jnxDomCurrentEntry 6 }


    jnxDomCurrentTxLaserOutputPower OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser output power."
        ::= { jnxDomCurrentEntry 7 }

    jnxDomCurrentModuleTemperature OBJECT-TYPE
        SYNTAX      Integer32
        UNITS  "Celsius (degrees C)"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module temperature."
        ::= { jnxDomCurrentEntry 8 }

    jnxDomCurrentRxLaserPowerHighAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Receiver laser power high alarm threshold."
        ::= { jnxDomCurrentEntry 9 }

    jnxDomCurrentRxLaserPowerLowAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Receiver laser power low alarm threshold."
        ::= { jnxDomCurrentEntry 10 }

    jnxDomCurrentRxLaserPowerHighWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Receiver laser power high warning threshold."
        ::= { jnxDomCurrentEntry 11 }

    jnxDomCurrentRxLaserPowerLowWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Receiver laser power low warning threshold."
        ::= { jnxDomCurrentEntry 12 }

    jnxDomCurrentTxLaserBiasCurrentHighAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 mA"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser bias current high alarm threshold."
        ::= { jnxDomCurrentEntry 13 }

    jnxDomCurrentTxLaserBiasCurrentLowAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 mA"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser bias current low alarm threshold."
        ::= { jnxDomCurrentEntry 14 }

    jnxDomCurrentTxLaserBiasCurrentHighWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 mA"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser bias current high warning threshold."
        ::= { jnxDomCurrentEntry 15 }

    jnxDomCurrentTxLaserBiasCurrentLowWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 mA"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser bias current low warning threshold."
        ::= { jnxDomCurrentEntry 16 }

    jnxDomCurrentTxLaserOutputPowerHighAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser power high alarm threshold."
        ::= { jnxDomCurrentEntry 17 }

    jnxDomCurrentTxLaserOutputPowerLowAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser power low alarm threshold."
        ::= { jnxDomCurrentEntry 18 }

    jnxDomCurrentTxLaserOutputPowerHighWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser power high warning threshold."
        ::= { jnxDomCurrentEntry 19 }

    jnxDomCurrentTxLaserOutputPowerLowWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Transmitter laser power low warning threshold."
        ::= { jnxDomCurrentEntry 20 }

    jnxDomCurrentModuleTemperatureHighAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "Celsius (degrees C)"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module temperature high alarm threshold."
        ::= { jnxDomCurrentEntry 21 }

    jnxDomCurrentModuleTemperatureLowAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "Celsius (degrees C)"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module temperature low alarm threshold."
        ::= { jnxDomCurrentEntry 22 }

    jnxDomCurrentModuleTemperatureHighWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "Celsius (degrees C)"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module temperature high warning threshold."
        ::= { jnxDomCurrentEntry 23 }

    jnxDomCurrentModuleTemperatureLowWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "Celsius (degrees C)"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module temperature low warning threshold."
        ::= { jnxDomCurrentEntry 24 }

    jnxDomCurrentModuleVoltage OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 V"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module voltage."
        ::= { jnxDomCurrentEntry 25 }

    jnxDomCurrentModuleVoltageHighAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 V"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module voltage high alarm threshold."
        ::= { jnxDomCurrentEntry 26 }
        
    jnxDomCurrentModuleVoltageLowAlarmThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 V"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module voltage low alarm threshold."
        ::= { jnxDomCurrentEntry 27 }
        
    jnxDomCurrentModuleVoltageHighWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 V"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module voltage high warning threshold."
        ::= { jnxDomCurrentEntry 28 }
        
    jnxDomCurrentModuleVoltageLowWarningThreshold OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 V"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Module voltage low warning threshold."
        ::= { jnxDomCurrentEntry 29 }

    jnxDomCurrentModuleLaneCount OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                " Number of Lanes (Lasers) in this module"
        ::= { jnxDomCurrentEntry 30 }

-- 
-- Current DOM Lane Statistics
-- 

jnxDomDigitalLaneMonitoring OBJECT IDENTIFIER ::= { jnxDomMib 2 }

jnxDomModuleLaneTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF JnxDomCurrentLaneEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "Table of information for this lane (Laser) of this
             SFF optical module."
    ::= { jnxDomDigitalLaneMonitoring 1 }

jnxDomCurrentLaneEntry OBJECT-TYPE
    SYNTAX     JnxDomCurrentLaneEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
            "Information about Digital Optical Monitoring for this
             lane of this SFF optical Module"
    INDEX { ifIndex, jnxDomLaneIndex }
    ::= { jnxDomModuleLaneTable 1 }

JnxDomCurrentLaneEntry ::=
    SEQUENCE {
            jnxDomLaneIndex
                      Integer32,
            jnxDomCurrentLaneAlarms
                      JnxDomLaneAlarmId,
            jnxDomCurrentLaneAlarmDate
                      DateAndTime,
            jnxDomLaneLastAlarms
                      JnxDomLaneAlarmId,
            jnxDomCurrentLaneWarnings
                      JnxDomLaneWarningId,
            jnxDomCurrentLaneRxLaserPower
                      Integer32,
            jnxDomCurrentLaneTxLaserBiasCurrent
                      Integer32,
            jnxDomCurrentLaneTxLaserOutputPower
                      Integer32,
            jnxDomCurrentLaneLaserTemperature
                      Integer32
    }

    jnxDomLaneIndex OBJECT-TYPE
        SYNTAX      Integer32 (0..1000)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "A lane (Laser) on this SFF optical interface"
        ::= {jnxDomCurrentLaneEntry 1}

    jnxDomCurrentLaneAlarms OBJECT-TYPE
        SYNTAX      JnxDomLaneAlarmId
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object identifies all the active DOM alarms
                 on a particular Lane of a SFF physical interface."
        ::= {jnxDomCurrentLaneEntry 2}

    jnxDomCurrentLaneAlarmDate OBJECT-TYPE
        SYNTAX      DateAndTime
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The system date and time when the management subsystem learned
                 of the current lane alarm event."
        ::= {jnxDomCurrentLaneEntry 3}

    jnxDomLaneLastAlarms OBJECT-TYPE
        SYNTAX      JnxDomLaneAlarmId
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object identifies a copy of jnxDomCurrentAlarms 
                 before last set or clear."
        ::= {jnxDomCurrentLaneEntry 4}

    jnxDomCurrentLaneWarnings OBJECT-TYPE
        SYNTAX      JnxDomLaneWarningId
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "This object identifies all the active DOM warnings
                 on a particular Lane of a SFF physical interface."
        ::= {jnxDomCurrentLaneEntry 5}

    jnxDomCurrentLaneRxLaserPower OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Receiver laser power on a particular Lane of a SFF
                 physcial interface."
        ::= {jnxDomCurrentLaneEntry 6}

    jnxDomCurrentLaneTxLaserBiasCurrent OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.001 mA"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Transmitter laser bias on a particular Lane of a SFF
                 physcial interface."
        ::= {jnxDomCurrentLaneEntry 7}

    jnxDomCurrentLaneTxLaserOutputPower OBJECT-TYPE
        SYNTAX      Integer32
        UNITS       "0.01 dbm"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Transmitter laser power on a particular Lane of a SFF
                 physcial interface."
        ::= {jnxDomCurrentLaneEntry 8}

    jnxDomCurrentLaneLaserTemperature OBJECT-TYPE
        SYNTAX      Integer32
        UNITS  "Celsius (degrees C)"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "Temperature of laser on a particular Lane of a SFF
                 physcial interface."
        ::= {jnxDomCurrentLaneEntry 9}

--
-- Configuration Management Notifications
--

jnxDomNotificationPrefix   OBJECT IDENTIFIER ::= { jnxDomNotifications 0 }

    jnxDomAlarmSet NOTIFICATION-TYPE
        OBJECTS { ifDescr,
                  jnxDomLastAlarms,
                  jnxDomCurrentAlarms,
                  jnxDomCurrentAlarmDate }
        STATUS  current
        DESCRIPTION
                "Notification of a recently set Dom alarm."
        ::= { jnxDomNotificationPrefix 1 }

    jnxDomAlarmCleared NOTIFICATION-TYPE
        OBJECTS { ifDescr,
                  jnxDomLastAlarms,
                  jnxDomCurrentAlarms,
                  jnxDomCurrentAlarmDate }
        STATUS  current
        DESCRIPTION
                "Notification of a recently cleared Dom alarm."
        ::= { jnxDomNotificationPrefix 2 }

jnxDomLaneNotificationPrefix   OBJECT IDENTIFIER ::= { jnxDomLaneNotifications 0 }

    jnxDomLaneAlarmSet NOTIFICATION-TYPE
        OBJECTS { ifDescr,
                  jnxDomLaneIndex,
                  jnxDomLaneLastAlarms,
                  jnxDomCurrentLaneAlarms,
                  jnxDomCurrentLaneAlarmDate }
        STATUS  current
        DESCRIPTION
                "Notification of a recently set Dom Lane alarm."
        ::= { jnxDomLaneNotificationPrefix 1 }

    jnxDomLaneAlarmCleared NOTIFICATION-TYPE
        OBJECTS { ifDescr,
                  jnxDomLaneIndex,
                  jnxDomLaneLastAlarms,
                  jnxDomCurrentLaneAlarms,
                  jnxDomCurrentLaneAlarmDate }
        STATUS  current
        DESCRIPTION
                "Notification of a recently cleared Dom Lane alarm."
        ::= { jnxDomLaneNotificationPrefix 2 }

END

