--
-- Juniper Enterprise Specific MIB: RPS 
--
-- Copyright (c) 2007-2009, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-RPS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Integer32
        FROM SNMPv2-SMI
    TEXTUAL-CONVENTION, DisplayString
        FROM SNMPv2-TC
    jnxRPS
        FROM JUNIPER-EX-SMI;


jnxRPSMIBObjects  MODULE-IDENTITY
    LAST-UPDATED "200708290000Z"
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "Juniper Technical Assistance Center
         Juniper Networks, Inc.
         1133 Innovation Way
         Sunnyvale, CA 94089
         E-mail: <EMAIL>"
    DESCRIPTION
        "This module contains definitions of management information for
        Redundant power supply."

    REVISION
        "200908070000Z" -- Fri Aug 07 00:00:00 2009 UTC

    DESCRIPTION
        "Data types of jnxRPSFanStatus and jnxRPSSystemStatus are 
         changed to JnxRPSStatus"

    ::= { jnxRPS 1 }

JnxRPSStatus ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION  "RPS Status."
    SYNTAX       INTEGER {
                     green(0),
                     red(1),
                     amber(2),
                     green-blink(3),
                     red-blink(4),
                     amber-blink(5),
                     off(6)
                 }

--
-- Definition of RPS Version information Table
--

    jnxRPSVersionTable OBJECT-TYPE
        SYNTAX SEQUENCE OF JnxRPSVersionEntry
        MAX-ACCESS     not-accessible
        STATUS         current
        DESCRIPTION
                "A Table containing RPS Version details."

        ::= { jnxRPSMIBObjects 1 }

    jnxRPSVersionEntry OBJECT-TYPE
        SYNTAX JnxRPSVersionEntry
        MAX-ACCESS     not-accessible
        STATUS         current
        DESCRIPTION
                "RPS Version details on a specific serial number."

        INDEX { jnxRPSSerialNumber }
        ::= { jnxRPSVersionTable 1 }

    JnxRPSVersionEntry ::= SEQUENCE {
        jnxRPSSerialNumber     DisplayString,
        jnxRPSModel            DisplayString,
        jnxRPSFirmwareVersion  DisplayString ,
        jnxRPSUBootVersion     DisplayString }

    jnxRPSSerialNumber OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (12))
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "The RPS Serial Number of the Chassis."
        ::= { jnxRPSVersionEntry 1 }

    jnxRPSModel OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..255))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Model name of the Chassis."
        ::= { jnxRPSVersionEntry 2 }

    jnxRPSFirmwareVersion OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..12))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Firmware version of the Chassis."
        ::= { jnxRPSVersionEntry 3 }

    jnxRPSUBootVersion OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..12))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS UBoot verison of the Chassis."
        ::= { jnxRPSVersionEntry 4 }

--
-- Definition of RPS Status Table
--

    jnxRPSStatusTable OBJECT-TYPE
        SYNTAX SEQUENCE OF JnxRPSStatusEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "A table containing RPS Fan and System Status for each slot."
        ::= { jnxRPSMIBObjects 2 }

    jnxRPSStatusEntry OBJECT-TYPE
        SYNTAX JnxRPSStatusEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "Status information for each FAN and SYSTEM slot."

        INDEX { jnxRPSSerialNumber }
        ::= { jnxRPSStatusTable 1 }

    JnxRPSStatusEntry ::= SEQUENCE {
        jnxRPSFanStatus     JnxRPSStatus,
        jnxRPSSystemStatus  JnxRPSStatus }

    jnxRPSFanStatus OBJECT-TYPE
        SYNTAX JnxRPSStatus
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Fan Status on the chassis ."
        ::= { jnxRPSStatusEntry 1 }

    jnxRPSSystemStatus OBJECT-TYPE
        SYNTAX JnxRPSStatus
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS System Status on the Chassis."
        ::= { jnxRPSStatusEntry 2 }

--
-- Definition of RPS Power Supply Table
--

    jnxRPSPowerSupplyTable OBJECT-TYPE
        SYNTAX SEQUENCE OF JnxRPSPowerSupplyEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "A table containing RPS Power Supply details."
        ::= { jnxRPSMIBObjects 3 }

    jnxRPSPowerSupplyEntry OBJECT-TYPE
        SYNTAX JnxRPSPowerSupplyEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "Information about Power Supply Status and Description."

        INDEX { jnxRPSSerialNumber, jnxRPSPowerSupplyIndex }
        ::= { jnxRPSPowerSupplyTable 1 }

    JnxRPSPowerSupplyEntry ::= SEQUENCE {
        jnxRPSPowerSupplyIndex        Integer32,
        jnxRPSPowerSupplySlotId       Integer32,
        jnxRPSPowerSupplyStatus       DisplayString,
        jnxRPSPowerSupplyDescription  DisplayString }

    jnxRPSPowerSupplyIndex OBJECT-TYPE
        SYNTAX          Integer32(1..3)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "The RPS Power Supply Index of the Chassis corresponding to RPS node."
        ::= { jnxRPSPowerSupplyEntry 1 }

    jnxRPSPowerSupplySlotId OBJECT-TYPE
        SYNTAX          Integer32(1..3)
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Power Supply Slot ID f the Chassis corresponding to RPS node."
        ::= { jnxRPSPowerSupplyEntry 2 }

    jnxRPSPowerSupplyStatus OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Power Supply Status of the Chassis corresponding to RPS node."
        ::= { jnxRPSPowerSupplyEntry 3 }

    jnxRPSPowerSupplyDescription OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Power Supply Description of the Chassis corresponding to RPS node."
        ::= { jnxRPSPowerSupplyEntry 4 }

--
-- Definition of RPS Led Status Table
--

    jnxRPSLedPortStatusTable OBJECT-TYPE
        SYNTAX SEQUENCE OF JnxRPSLedPortStatusEntry
        MAX-ACCESS       not-accessible
        STATUS           current
        DESCRIPTION
                "A table containing RPS LED Status details."
        ::= { jnxRPSMIBObjects 4 }

    jnxRPSLedPortStatusEntry OBJECT-TYPE
        SYNTAX JnxRPSLedPortStatusEntry
        MAX-ACCESS       not-accessible
        STATUS           current
        DESCRIPTION
                "LED Port status of each port."

        INDEX { jnxRPSSerialNumber, jnxRPSLedPortIndex }
        ::= { jnxRPSLedPortStatusTable 1 }

    JnxRPSLedPortStatusEntry ::= SEQUENCE {
        jnxRPSLedPortIndex   Integer32,
        jnxRPSLedPortStatus  DisplayString }

    jnxRPSLedPortIndex OBJECT-TYPE
        SYNTAX          Integer32(1..6)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "The RPS Led Port Index of the Chassis corresponding to RPS node."
        ::= { jnxRPSLedPortStatusEntry 1 }

    jnxRPSLedPortStatus OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Led Port Status of the Chassis corresponding to RPS node."
        ::= { jnxRPSLedPortStatusEntry 2 }

--
-- Definition of RPS Port Status Table
--

    jnxRPSPortStatusTable OBJECT-TYPE
        SYNTAX SEQUENCE OF JnxRPSPortStatusEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "A table containing RPS Port Status details."
        ::= { jnxRPSMIBObjects 5 }

    jnxRPSPortStatusEntry OBJECT-TYPE
        SYNTAX JnxRPSPortStatusEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "Port Priority and Status for each port ."

        INDEX { jnxRPSSerialNumber, jnxRPSPortIndex }
        ::= { jnxRPSPortStatusTable 1 }

    JnxRPSPortStatusEntry ::= SEQUENCE {
        jnxRPSPortIndex           Integer32,
        jnxRPSPortId              Integer32,
        jnxRPSPortStatus          Integer32,
        jnxRPSPortPriority        Integer32,
        jnxRPSPortPowerRequested  DisplayString }

    jnxRPSPortIndex OBJECT-TYPE
        SYNTAX          Integer32(1..6)
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
                "The RPS Port Index of the Chassis corresponding to RPS node."
        ::= { jnxRPSPortStatusEntry 1 }

    jnxRPSPortId OBJECT-TYPE
        SYNTAX          Integer32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Port ID of the Chassis corresponding to RPS node."
        ::= { jnxRPSPortStatusEntry 2 }


    jnxRPSPortStatus OBJECT-TYPE
        SYNTAX          Integer32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Port Status of the Chassis corresponding to RPS node."
        ::= { jnxRPSPortStatusEntry 3 }

    jnxRPSPortPriority OBJECT-TYPE
        SYNTAX          Integer32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Port Priority of the Chassis corresponding to RPS node."
        ::= { jnxRPSPortStatusEntry 4 }

    jnxRPSPortPowerRequested OBJECT-TYPE
        SYNTAX          DisplayString (SIZE (0..32))
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
                "The RPS Led Port Power Requested of the Chassis corresponding to RPS node."
        ::= { jnxRPSPortStatusEntry 5 }

END
