-- *******************************************************************
-- Juniper enterprise Flow MIB.
--
-- Copyright (c) 2017, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
-- *******************************************************************

JUNIPER-JS-FLOW-MIB DEFINITIONS ::= BEGIN
    IMPORTS
        Gauge32, Unsigned32,
        Counter64,
        NOTIFICATION-TYPE,
        MODULE-IDENTITY, OBJECT-TYPE    FROM SNMPv2-SMI
        DisplayString               FROM SNMPv2-TC
    jnxJsFlow                       FROM JUNIPER-JS-SMI;


    jnxJsFlowMIB    MODULE-IDENTITY
        LAST-UPDATED  "201701181022Z" -- January 18, 2017
        ORGANIZATION  "Juniper Networks, Inc."
        CONTACT-INFO
                    "Juniper Technical Assistance Center
                        Juniper Networks, Inc.
                        1133 Innovation Way
                        Sunnyvale, CA 94089

                        E-mail: <EMAIL>
                        HTTP://www.juniper.net"
        DESCRIPTION
            "This module defines the object that are used for flow
             related counters or attributes ."
    ::= { jnxJsFlow 1 }


    jnxJsFlowGenPostFrags OBJECT IDENTIFIER ::= { jnxJsFlowMIB 0 }
    jnxJsFlowGenPreFrags OBJECT IDENTIFIER ::= { jnxJsFlowMIB 1 }
    jnxJsFlowSofSummary OBJECT IDENTIFIER ::= { jnxJsFlowMIB 2 }


-- ***************************************************************
-- FLOW Table
-- ***************************************************************


-- **********************************************************************
-- Tunnel fragment counters information summary:
-- **********************************************************************
    jnxJsFlowGenPostFragsCounter OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "Total ipv4 post fragments generated."
    ::= { jnxJsFlowGenPostFrags 1 }

    jnxJsFlowGenPreFragsCounter OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "Total ipv4 pre fragments generated."
    ::= { jnxJsFlowGenPreFrags 1 }

    jnxJsFlowSofPktProcessedNum OBJECT-TYPE
        SYNTAX                  Counter64
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "Total number of services-offload packets processed."
    ::= { jnxJsFlowSofSummary 1 }

    jnxJsFlowSofSessNum OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "Total number of current services-offload sessions."
    ::= { jnxJsFlowSofSummary 2 }

-- ***************************************************************
--  END of File
-- ***************************************************************

END
