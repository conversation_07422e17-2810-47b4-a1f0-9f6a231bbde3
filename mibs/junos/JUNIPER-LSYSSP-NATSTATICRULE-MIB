--
-- Juniper Enterprise Specific MIB: Structure of Management Information
-- 
-- Copyright (c) 2010-2011, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--


JUNIPER-LSYSSP-NATSTATICRULE-MIB DEFINITIONS ::= BEGIN
   IMPORTS
      MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, 
      Unsigned32
        FROM SNMPv2-SMI
      DisplayString
        FROM SNMPv2-TC
      jnxLsysSpNATstaticrule                  
        FROM JUNIPER-LSYS-SECURITYPROFILE-MIB
    ;
    
    jnxLsysSpNATstaticruleMIB MODULE-IDENTITY
        LAST-UPDATED  "201005191644Z" -- May 19, 2010
        ORGANIZATION  "Juniper Networks, Inc."
        CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089

             E-mail: <EMAIL>
             HTTP://www.juniper.net"
        DESCRIPTION
            "This module defines the NAT-static-rule-specific MIB for 
             Juniper Enterprise Logical-System (LSYS) security profiles.  
             Juniper documentation is recommended as the reference. 

             The LSYS security profile provides various static and dynamic 
             resource management by observing resource quota limits. 
             Security NAT-static-rule resource is the focus in this MIB. 
            "
        ::= { jnxLsysSpNATstaticrule 1 }

    jnxLsysSpNATstaticruleObjects  OBJECT IDENTIFIER ::= { jnxLsysSpNATstaticruleMIB 1 }
    jnxLsysSpNATstaticruleSummary  OBJECT IDENTIFIER ::= { jnxLsysSpNATstaticruleMIB 2 }
    
 
-- **********************************************************************
-- Tabular NAT-static-rule resource information objects per LSYS:
--   Below are NAT-static-rule resource table indexed by LSYS name.
-- **********************************************************************

-- The NAT-static-rule resource table per LSYS

    jnxLsysSpNATstaticruleTable OBJECT-TYPE
        SYNTAX              SEQUENCE OF JnxLsysSpNATstaticruleEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION 
            "LSYSPROFILE NAT-static-rule objects for NAT-static-rule 
             resource consumption per LSYS."  
    ::= { jnxLsysSpNATstaticruleObjects 1 }
    
    jnxLsysSpNATstaticruleEntry OBJECT-TYPE
        SYNTAX              JnxLsysSpNATstaticruleEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION  
            "An entry in NAT-static-rule resource table."
    INDEX { IMPLIED jnxLsysSpNATstaticruleLsysName }          
    ::= { jnxLsysSpNATstaticruleTable 1 }          

    JnxLsysSpNATstaticruleEntry ::= 
       SEQUENCE {
          jnxLsysSpNATstaticruleLsysName    DisplayString,
          jnxLsysSpNATstaticruleProfileName DisplayString,
          jnxLsysSpNATstaticruleUsage       Unsigned32,
          jnxLsysSpNATstaticruleReserved    Unsigned32,
          jnxLsysSpNATstaticruleMaximum     Unsigned32
    }   
 
-- Entry definitions for the NAT-static-rule resource table
 
    jnxLsysSpNATstaticruleLsysName       OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..64))
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION
            "The name of the logical system for which NAT-static-rule 
             resource information is retrieved. "
        ::= { jnxLsysSpNATstaticruleEntry 1 }

    jnxLsysSpNATstaticruleProfileName    OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..32))
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The security profile name string for the LSYS."
    ::= { jnxLsysSpNATstaticruleEntry 2 }

    jnxLsysSpNATstaticruleUsage          OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION  
            "The current resource usage count for the LSYS."
    ::= { jnxLsysSpNATstaticruleEntry 3 }
    
    jnxLsysSpNATstaticruleReserved       OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The reserved resource count for the LSYS."
    ::= { jnxLsysSpNATstaticruleEntry 4 } 

    jnxLsysSpNATstaticruleMaximum        OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The maximum allowed resource usage count for the LSYS."
    ::= { jnxLsysSpNATstaticruleEntry 5 }


-- **********************************************************************
-- The NAT-static-rule resource information summary:
-- **********************************************************************

    jnxLsysSpNATstaticruleUsedAmount         OBJECT-TYPE
        SYNTAX                  Unsigned32 
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "The NAT-static-rule resource consumption over all LSYS."
    ::= { jnxLsysSpNATstaticruleSummary 1 }          

    jnxLsysSpNATstaticruleMaxQuota           OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The NAT-static-rule resource maximum quota for the whole 
            device for all LSYS."
    ::= { jnxLsysSpNATstaticruleSummary 2 }
    
    jnxLsysSpNATstaticruleAvailableAmount    OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The NAT-static-rule resource available in the whole device."
    ::= { jnxLsysSpNATstaticruleSummary 3 }
    
    jnxLsysSpNATstaticruleHeaviestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The most amount of NAT-static-rule resource consumed of a 
            LSYS."
    ::= { jnxLsysSpNATstaticruleSummary 4 }
    
    jnxLsysSpNATstaticruleHeaviestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the most NAT-static-rule resource."
    ::= { jnxLsysSpNATstaticruleSummary 5 }
    
    jnxLsysSpNATstaticruleLightestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The least amount of NAT-static-rule resource consumed of a 
            LSYS."
    ::= { jnxLsysSpNATstaticruleSummary 6 }
    
    jnxLsysSpNATstaticruleLightestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the least NAT-static-rule resource."
    ::= { jnxLsysSpNATstaticruleSummary 7 }
    


 -- ***************************************************************
 -- definition of NAT-static-rule resource related traps. (TBD)
 -- ***************************************************************

--
-- End of File 
--

END
