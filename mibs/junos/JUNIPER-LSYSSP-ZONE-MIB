--
-- Juniper Enterprise Specific MIB: Structure of Management Information
-- 
-- Copyright (c) 2010-2011, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--


JUNIPER-LSYSSP-ZONE-MIB DEFINITIONS ::= BEGIN
   IMPORTS
      MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, 
      Unsigned32
        FROM SNMPv2-SMI
      DisplayString
        FROM SNMPv2-TC
      jnxLsysSpZone                  
        FROM JUNIPER-LSYS-SECURITYPROFILE-MIB
    ;
    
    jnxLsysSpZoneMIB MODULE-IDENTITY
        LAST-UPDATED  "201005191644Z" -- May 19, 2010
        ORGANIZATION  "Juniper Networks, Inc."
        CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089

             E-mail: <EMAIL>
             HTTP://www.juniper.net"
        DESCRIPTION
            "This module defines the zone-specific MIB for Juniper Enterprise 
             Logical-System (LSYS) security profiles.  Juniper documentation 
             is recommended as the reference. 

             The LSYS security profile provides various static and dynamic 
             resource management by observing resource quota limits. 
             Security zone resource is the focus in this MIB. 
            "
        ::= { jnxLsysSpZone 1 }

    jnxLsysSpZoneObjects        OBJECT IDENTIFIER ::= { jnxLsysSpZoneMIB 1 }
    jnxLsysSpZoneSummary        OBJECT IDENTIFIER ::= { jnxLsysSpZoneMIB 2 }
    
 
-- **********************************************************************
-- Tabular zone resource information objects per LSYS:
--   Below are zone resource table indexed by LSYS name.
-- **********************************************************************

-- Zone resource table per LSYS

    jnxLsysSpZoneTable OBJECT-TYPE
        SYNTAX              SEQUENCE OF JnxLsysSpZoneEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION 
            "LSYSPROFILE zone objects for zone resource consumption per LSYS."  
    ::= { jnxLsysSpZoneObjects 1 }
    
    jnxLsysSpZoneEntry OBJECT-TYPE
        SYNTAX              JnxLsysSpZoneEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION  
            "An entry in zone resource table."
    INDEX { IMPLIED jnxLsysSpZoneLsysName }          
    ::= { jnxLsysSpZoneTable 1 }          

    JnxLsysSpZoneEntry ::= 
       SEQUENCE {
          jnxLsysSpZoneLsysName    DisplayString,
          jnxLsysSpZoneProfileName DisplayString,
          jnxLsysSpZoneUsage       Unsigned32,
          jnxLsysSpZoneReserved    Unsigned32,
          jnxLsysSpZoneMaximum     Unsigned32
    }   
 
-- Entry definitions for the zone resource table
 
    jnxLsysSpZoneLsysName       OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..64))
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION
            "The name of the logical system for which zone resource information is retrieved. "
        ::= { jnxLsysSpZoneEntry 1 }

    jnxLsysSpZoneProfileName    OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..32))
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The security profile name string for the LSYS."
    ::= { jnxLsysSpZoneEntry 2 }

    jnxLsysSpZoneUsage          OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION  
            "The current resource usage count for the LSYS."
    ::= { jnxLsysSpZoneEntry 3 }
    
    jnxLsysSpZoneReserved       OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The reserved resource count for the LSYS."
    ::= { jnxLsysSpZoneEntry 4 } 

    jnxLsysSpZoneMaximum        OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The maximum allowed resource usage count for the LSYS."
    ::= { jnxLsysSpZoneEntry 5 }


-- **********************************************************************
-- Zone resource information summary:
-- **********************************************************************

    jnxLsysSpZoneUsedAmount         OBJECT-TYPE
        SYNTAX                  Unsigned32 
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "The zone resource consumption over all LSYS."
    ::= { jnxLsysSpZoneSummary 1 }          

    jnxLsysSpZoneMaxQuota           OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The zone resource maximum quota for the whole device for all LSYS."
    ::= { jnxLsysSpZoneSummary 2 }
    
    jnxLsysSpZoneAvailableAmount    OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The zone resource available in the whole device."
    ::= { jnxLsysSpZoneSummary 3 }
    
    jnxLsysSpZoneHeaviestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The most amount of zone resource consumed of a LSYS."
    ::= { jnxLsysSpZoneSummary 4 }
    
    jnxLsysSpZoneHeaviestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the most zone resource."
    ::= { jnxLsysSpZoneSummary 5 }
    
    jnxLsysSpZoneLightestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The least amount of zone resource consumed of a LSYS."
    ::= { jnxLsysSpZoneSummary 6 }
    
    jnxLsysSpZoneLightestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the least zone resource."
    ::= { jnxLsysSpZoneSummary 7 }
    


 -- ***************************************************************
 -- definition of zone resource related traps. (TBD)
 -- ***************************************************************

--
-- End of File 
--

END
