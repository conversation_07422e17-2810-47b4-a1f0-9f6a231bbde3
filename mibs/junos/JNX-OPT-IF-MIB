JNX-OPT-IF-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, <PERSON><PERSON>ge32, Integer32,
    Unsigned32, transmission
        FROM SNMPv2-<PERSON>I
        TEXTUAL-CONVENTION, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, TruthValue
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM SNMPv2-CONF
    jnxoptIfMibRoot
        FROM JUNIPER-SMI
    ifIndex
        FROM IF-MIB;


--This is the MIB module for the OTN Interface objects. 


jnxoptIfMibModule MODULE-IDENTITY 
    LAST-UPDATED "200308130000Z" 
    ORGANIZATION "IETF AToM MIB Working Group" 
    CONTACT-INFO
            "WG charter: 
             http://www.ietf.org/html.charters/atommib-charter.html

             Mailing Lists: 
             General Discussion: <EMAIL> 
             To Subscribe: <EMAIL> 
             RFC 3591 Optical Interface Type MIB September 2003
             Editor: Hing-Ka<PERSON> Lam
             Postal: Lucent Technologies, Room 4C-616 
                     101 Crawfords Corner Road 
                     Holmdel, NJ 07733

              Tel: ****** 949 8338 
             Email: <EMAIL>" 
    DESCRIPTION 
        "The MIB module to describe pre-OTN and OTN interfaces.
         Copyright (C) The Internet Society (2003). This version 
         of this MIB module is part of RFC 3591; see the RFC 
         itself for full legal notices."

    REVISION "200308130000Z" 
    DESCRIPTION 
        "Initial version, published as RFC 3591." 
    ::={ jnxoptIfMibRoot 1 } 


-- textual conventions 


JnxoptIfAcTI ::= TEXTUAL-CONVENTION 
    STATUS       current 
    DESCRIPTION
         "The trace identifier (TI) accepted at the receiver." 
    SYNTAX       OCTET STRING (SIZE(64)) 


JnxoptIfBitRateK ::= TEXTUAL-CONVENTION 
    STATUS      current 
    DESCRIPTION
         "Indicates the index that is used to 
          represent a supported bit rate and the different 
          versions of OPUk, ODUk and OTUk. 
          Allowed values of k are defined in ITU-T G.709. 
          Currently allowed values in G.709 are:
           k=1 represents an approximate bit rate of 2.5 Gbit/s, 
          k=2 represents an approximate bit rate of 10 Gbit/s, 
          k=3 represents an approximate bit rate of 40 Gbit/s."
    SYNTAX      Integer32 

JnxoptIfDEGM ::= TEXTUAL-CONVENTION 
    STATUS      current 
    DESCRIPTION
      "Indicates the threshold level for declaring a Degraded Signal 
      defect (dDEG). A dDEG shall be declared if JnxoptIfDEGM 
      consecutive bad PM Seconds are detected."
    SYNTAX      Unsigned32 (2..10) 


JnxoptIfDEGThr ::= TEXTUAL-CONVENTION 
    STATUS     current 
    DESCRIPTION 
      "Indicates the threshold level for declaring a performance 
      monitoring (PM) Second to be bad. A PM Second is declared bad if 
      the percentage of detected errored blocks in that second is 
      greater than or equal to JnxoptIfDEGThr." 
    SYNTAX     Unsigned32 (1..100) 


JnxoptIfDirectionality ::= TEXTUAL-CONVENTION 
    STATUS    current 
    DESCRIPTION 
      "Indicates the directionality of an entity." 
    SYNTAX    INTEGER { 
         sink(1), 
         source(2), 
         bidirectional(3) 
    } 


JnxoptIfSinkOrSource ::= TEXTUAL-CONVENTION 
    STATUS    current 
    DESCRIPTION 
      "Indicates the directionality of an entity 
      that is allowed only to be a source or sink." 
    SYNTAX    INTEGER { 
         sink(1), 
         source(2) 
    } 


JnxoptIfExDAPI ::= TEXTUAL-CONVENTION 
    STATUS current 
    DESCRIPTION 
      "The Destination Access Point Identifier (DAPI) 
      expected by the receiver." 
    SYNTAX OCTET STRING (SIZE(16)) 


JnxoptIfExSAPI ::= TEXTUAL-CONVENTION 
    STATUS current 
    DESCRIPTION 
      "The Source Access Point Identifier (SAPI) 
      expected by the receiver." 
    SYNTAX OCTET STRING (SIZE(16)) 


JnxoptIfIntervalNumber ::= TEXTUAL-CONVENTION 
    STATUS current 
    DESCRIPTION 
      "Uniquely identifies a 15-minute interval. The interval 
      identified by 1 is the most recently completed interval, and 
      the interval identified by n is the interval immediately 
      preceding the one identified by n-1." 
    SYNTAX Unsigned32 (1..96) 


JnxoptIfTIMDetMode ::= TEXTUAL-CONVENTION 
    STATUS current 
    DESCRIPTION
      "Indicates the mode of the Trace Identifier Mismatch (TIM) 
      Detection function."
    SYNTAX INTEGER { 
        off(1), 
        dapi(2), 
        sapi(3), 
        both(4) 
    } 


JnxoptIfTxTI ::= TEXTUAL-CONVENTION 
    STATUS current 
    DESCRIPTION
      "The trace identifier (TI) transmitted." 
    SYNTAX OCTET STRING (SIZE(64)) 


-- object groups 


jnxoptIfObjects OBJECT IDENTIFIER ::= { jnxoptIfMibModule 1 } 
jnxoptIfConfs OBJECT IDENTIFIER ::= { jnxoptIfMibModule 2 } 


jnxoptIfOTMn OBJECT IDENTIFIER ::= { jnxoptIfObjects 1 } 
jnxoptIfPerfMon OBJECT IDENTIFIER ::= { jnxoptIfObjects 2 } 
jnxoptIfOTSn OBJECT IDENTIFIER ::= { jnxoptIfObjects 3 } 
jnxoptIfOMSn OBJECT IDENTIFIER ::= { jnxoptIfObjects 4 } 
jnxoptIfOChGroup OBJECT IDENTIFIER ::= { jnxoptIfObjects 5 } 
jnxoptIfOCh OBJECT IDENTIFIER ::= { jnxoptIfObjects 6 } 


jnxoptIfOTUk OBJECT IDENTIFIER ::= { jnxoptIfObjects 7 } 
jnxoptIfODUk OBJECT IDENTIFIER ::= { jnxoptIfObjects 8 } 
jnxoptIfODUkT OBJECT IDENTIFIER ::= { jnxoptIfObjects 9 } 


jnxoptIfGroups OBJECT IDENTIFIER ::= { jnxoptIfConfs 1 } 
jnxoptIfCompl OBJECT IDENTIFIER ::= { jnxoptIfConfs 2 } 


-- the jnxoptIfOTMn group 
-- This group defines the OTM structure information of an 
-- optical interface. 


-- OTMn Table 


jnxoptIfOTMnTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTMnEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTMn structure information." 
    ::= { jnxoptIfOTMn 1 } 


jnxoptIfOTMnEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTMnEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A conceptual row that contains the OTMn structure 
      information of an optical interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOTMnTable 1 } 


JnxoptIfOTMnEntry ::= 
    SEQUENCE { 
        jnxoptIfOTMnOrder Unsigned32, 
        jnxoptIfOTMnReduced TruthValue, 
        jnxoptIfOTMnBitRates BITS, 
        jnxoptIfOTMnInterfaceType SnmpAdminString, 
        jnxoptIfOTMnTcmMax Unsigned32, 
        jnxoptIfOTMnOpticalReach INTEGER 
    } 


jnxoptIfOTMnOrder OBJECT-TYPE 
    SYNTAX Unsigned32 (1..900) 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "This object indicates the order of the OTM, which 
      represents the maximum number of wavelengths that can be 
      supported at the bit rate(s) supported on the interface." 
    ::= { jnxoptIfOTMnEntry 1 } 


jnxoptIfOTMnReduced OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "This object indicates whether a reduced or full 
      functionality is supported at the interface. A value of 
      true means reduced. A value of false means full." 
    ::= { jnxoptIfOTMnEntry 2 } 


jnxoptIfOTMnBitRates OBJECT-TYPE 
    SYNTAX BITS { bitRateK1(0), bitRateK2(1), bitRateK3(2) } 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "This attribute is a bit map representing the bit 
      rate or set of bit rates supported on the interface. 
      The meaning of each bit position is as follows:
      bitRateK1(0) is set if the 2.5 Gbit/s rate is supported 
      bitRateK2(1) is set if the 10 Gbit/s rate is supported 
      bitRateK3(2) is set if the 40 Gbit/s rate is supported
      Note that each bit position corresponds to one possible 
      value of the type JnxoptIfBitRateK. 
      The default value of this attribute is system specific."
    ::= { jnxoptIfOTMnEntry 3 } 


jnxoptIfOTMnInterfaceType OBJECT-TYPE 
    SYNTAX SnmpAdminString 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "This object identifies the type of interface. The value of 
      this attribute will affect the behavior of the OTM with 
      respect to presence/absence of OTM Overhead Signal (OOS) 
      processing and TCM activation. For an IrDI interface, 
      there is no OOS processing and TCM activation is limited 
      to n levels as specified by a TCM level threshold.
      This object contains two fields that are separated by 
      whitespace. The possible values are: 
      field 1: one of the 4-character ASCII strings 
      'IrDI' or 'IaDI'
      field 2: free-form text consisting of printable 
      UTF-8 encoded characters
      Note that field 2 is optional. If it is not present then there 
      is no requirement for trailing whitespace after field 1.
      The default values are as follows: 
      field 1: 'IaDI' field 2: an empty string."
    ::= { jnxoptIfOTMnEntry 4 } 


jnxoptIfOTMnTcmMax OBJECT-TYPE 
    SYNTAX Unsigned32 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION 
      "This object identifies the maximum number of TCM 
      levels allowed for any Optical Channel contained 
      in this OTM. A new TCM activation will be rejected 
      if the requested level is greater than the threshold. 
      If InterfaceType object specifies a type of 'IaDI' 
      for this OTM, then this attribute is irrelevant.
      Possible values: unsigned integers in the range 
      from 0 to 6 inclusive. 
      Default value: 3."
    ::= { jnxoptIfOTMnEntry 5 } 


jnxoptIfOTMnOpticalReach OBJECT-TYPE 
    SYNTAX INTEGER { intraOffice(1), shortHaul(2), longHaul(3),
                     veryLongHaul(4), ultraLongHaul(5) } 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "This object indicates the length the optical signal 
      may travel before requiring termination or regeneration. 
      The meaning of the enumeration are:
      intraOffice(1) - intra-office (as defined in ITU-T G.957) 
      shortHaul(2) - short haul (as defined in ITU-T G.957) 
      longHaul(3) - long haul (as defined in ITU-T G.957) 
      veryLongHaul(4) - very long haul (as defined in ITU-T G.691) 
      ultraLongHaul(5)- ultra long haul (as defined in ITU-T G.691)"
    ::= { jnxoptIfOTMnEntry 6 } 


-- the jnxoptIfPerfMon group 
-- This group defines performance monitoring objects for all 
-- layers. 

-- PM interval table 

jnxoptIfPerfMonIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfPerfMonIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of 15-minute performance monitoring interval 
      information." 
    ::= { jnxoptIfPerfMon 1 } 


jnxoptIfPerfMonIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfPerfMonIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A conceptual row that contains 15-minute performance
       monitoring interval information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfPerfMonIntervalTable 1 } 


JnxoptIfPerfMonIntervalEntry ::=
    SEQUENCE { 
        jnxoptIfPerfMonCurrentTimeElapsed Gauge32, 
        jnxoptIfPerfMonCurDayTimeElapsed Gauge32,
        jnxoptIfPerfMonIntervalNumIntervals Unsigned32, 
        jnxoptIfPerfMonIntervalNumInvalidIntervals Unsigned32 
    } 

jnxoptIfPerfMonCurrentTimeElapsed OBJECT-TYPE 
    SYNTAX Gauge32 (0..900) 
    UNITS "seconds" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Number of seconds elapsed in the current 15-minute 
      performance monitoring interval. 
      If, for some reason, such as an adjustment in the NE's 
      time-of-day clock, the number of seconds elapsed exceeds 
      the maximum value, then the maximum value will be returned."
    ::= { jnxoptIfPerfMonIntervalEntry 1 } 


jnxoptIfPerfMonCurDayTimeElapsed OBJECT-TYPE 
    SYNTAX Gauge32 (0..86400) 
    UNITS "seconds" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Number of seconds elapsed in the current 24-hour interval 
      performance monitoring period. 
      If, for some reason, such as an adjustment in the NE 
      time-of-day clock, the number of seconds elapsed exceeds 
      the maximum value, then the maximum value will be returned."
    ::= { jnxoptIfPerfMonIntervalEntry 2 } 


jnxoptIfPerfMonIntervalNumIntervals OBJECT-TYPE 
    SYNTAX Unsigned32 (0..96) 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The number of 15-minute intervals for which performance 
      monitoring data is available. The number is the same for all 
      the associated sub layers of the interface. 
      An optical interface must be capable of supporting at least
      n intervals, where n is defined as follows: 
      The minimum value of n is 4. 
      The default of n is 32. 
      The maximum value of n is 96.
       The value of this object will be n unless performance 
      monitoring was (re-)started for the interface within the last 
      (n*15) minutes, in which case the value will be the number of 
      complete 15-minute intervals since measurement was 
      (re-)started."
    ::= { jnxoptIfPerfMonIntervalEntry 3 } 


jnxoptIfPerfMonIntervalNumInvalidIntervals OBJECT-TYPE 
    SYNTAX Unsigned32 (0..96) 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The number of intervals in the range from 0 to 
      jnxoptIfPerfMonIntervalNumIntervals for which no performance 
      monitoring data is available and/or the data is invalid."
    ::= { jnxoptIfPerfMonIntervalEntry 4 } 


-- the jnxoptIfOTSn group 
-- This group handles the configuration and performance 
-- monitoring objects for OTS layers. 


-- OTSn config table 

jnxoptIfOTSnConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTSn configuration information." 
    ::= { jnxoptIfOTSn 1 } 


jnxoptIfOTSnConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTSn configuration
      information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOTSnConfigTable 1 } 


JnxoptIfOTSnConfigEntry ::= 
    SEQUENCE { 
        jnxoptIfOTSnDirectionality               JnxoptIfDirectionality, 
        jnxoptIfOTSnAprStatus                    SnmpAdminString, 
        jnxoptIfOTSnAprControl                   SnmpAdminString, 
        jnxoptIfOTSnTraceIdentifierTransmitted   JnxoptIfTxTI, 
        jnxoptIfOTSnDAPIExpected                 JnxoptIfExDAPI, 
        jnxoptIfOTSnSAPIExpected                 JnxoptIfExSAPI, 
        jnxoptIfOTSnTraceIdentifierAccepted      JnxoptIfAcTI, 
        jnxoptIfOTSnTIMDetMode                   JnxoptIfTIMDetMode, 
        jnxoptIfOTSnTIMActEnabled                TruthValue, 
        jnxoptIfOTSnCurrentStatus                BITS 
    } 


jnxoptIfOTSnDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfDirectionality 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
     "Indicates the directionality of the entity." 
    ::= { jnxoptIfOTSnConfigEntry 1 } 


jnxoptIfOTSnAprStatus OBJECT-TYPE 
    SYNTAX SnmpAdminString 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "This attribute indicates the status of the Automatic 
      Power Reduction (APR) function of the entity. Valid 
      values are 'on' and 'off'." 
    ::= { jnxoptIfOTSnConfigEntry 2 } 


jnxoptIfOTSnAprControl OBJECT-TYPE 
    SYNTAX SnmpAdminString 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION 
      "This object is a UTF-8 encoded string that specifies Automatic 
      Power Reduction (APR) control actions requested of this entity 
      (when written) and that returns the current APR control state 
      of this entity (when read). The values are implementation-defined. 
      Any implementation that instantiates this object must document the 
      set of values that it allows to be written, the set of values 
      that it will return, and what each of those values means." 
    ::= { jnxoptIfOTSnConfigEntry 3 } 


jnxoptIfOTSnTraceIdentifierTransmitted OBJECT-TYPE 
    SYNTAX JnxoptIfTxTI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The trace identifier transmitted. 
      This object is applicable when jnxoptIfOTSnDirectionality has the 
      value source(2) or bidirectional(3). 
      This object does not apply to reduced-capability systems (i.e., 
      those for which jnxoptIfOTMnReduced has the value true(1)) or 
      at IrDI interfaces (i.e., when jnxoptIfOTMnInterfaceType field 1 
      has the value 'IrDI'). 
      If no value is ever set by a management entity for the object 
      jnxoptIfOTSnTraceIdentifierTransmitted, system-specific default 
      value will be used. Any implementation that instantiates this 
      object must document the system-specific default value or how it 
      is derived."
    ::= { jnxoptIfOTSnConfigEntry 4 } 


jnxoptIfOTSnDAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExDAPI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The DAPI expected by the receiver. 
      This object is applicable when jnxoptIfOTSnDirectionality has the 
      value sink(1) or bidirectional(3). It has no effect if 
      jnxoptIfOTSnTIMDetMode has the value off(1) or sapi(3). 
      This object does not apply to reduced-capability systems (i.e., 
      those for which jnxoptIfOTMnReduced has the value true(1)) or 
      at IrDI interfaces (i.e., when jnxoptIfOTMnInterfaceType field 1 
      has the value 'IrDI')."
    ::= { jnxoptIfOTSnConfigEntry 5 } 


jnxoptIfOTSnSAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExSAPI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
     "The SAPI expected by the receiver. 
      This object is applicable when jnxoptIfOTSnDirectionality has the 
      value sink(1) or bidirectional(3). It has no effect if 
      jnxoptIfOTSnTIMDetMode has the value off(1) or dapi(2). 
      This object does not apply to reduced-capability systems (i.e., 
      those for which jnxoptIfOTMnReduced has the value true(1)) or 
      at IrDI interfaces (i.e., when jnxoptIfOTMnInterfaceType field 1 
      has the value 'IrDI')."
    ::= { jnxoptIfOTSnConfigEntry 6 } 


jnxoptIfOTSnTraceIdentifierAccepted OBJECT-TYPE 
    SYNTAX JnxoptIfAcTI 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The actual trace identifier received. 
      This object is applicable when jnxoptIfOTSnDirectionality has the 
      value sink(1) or bidirectional(3). Its value is unspecified 
      if jnxoptIfOTSnCurrentStatus has either or both of the 
      losO(5) and los(6) bits set. 
      This object does not apply to reduced-capability systems (i.e., 
      those for which jnxoptIfOTMnReduced has the value true(1)) or 
      at IrDI interfaces (i.e., when jnxoptIfOTMnInterfaceType field 1 
      has the value 'IrDI')."
    ::= { jnxoptIfOTSnConfigEntry 7 } 


jnxoptIfOTSnTIMDetMode OBJECT-TYPE 
    SYNTAX JnxoptIfTIMDetMode 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates the mode of the Trace Identifier Mismatch (TIM) 
      Detection function. This object is applicable 
      when jnxoptIfOTSnDirectionality has the value sink(1) 
      or bidirectional(3). The default value is off(1). 
      This object does not apply to reduced-capability systems (i.e., 
      those for which jnxoptIfOTMnReduced has the value true(1)) or 
      at IrDI interfaces (i.e., when jnxoptIfOTMnInterfaceType field 1 
      has the value 'IrDI'). 
      The default value of this object is off(1)."
    ::= { jnxoptIfOTSnConfigEntry 8 } 


jnxoptIfOTSnTIMActEnabled OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
     "Indicates whether the Trace Identifier Mismatch (TIM) 
      Consequent Action function is enabled. This object 
      is applicable when jnxoptIfOTSnDirectionality has the 
      value sink(1) or bidirectional(3). It has no effect 
      when the value of jnxoptIfOTSnTIMDetMode is off(1). 
      This object does not apply to reduced-capability systems (i.e., 
      those for which jnxoptIfOTMnReduced has the value true(1)) or 
      at IrDI interfaces (i.e., when jnxoptIfOTMnInterfaceType field 1 
      has the value 'IrDI'). 
      The default value of this object is false(2)."
    ::= { jnxoptIfOTSnConfigEntry 9 } 


jnxoptIfOTSnCurrentStatus OBJECT-TYPE 
     SYNTAX BITS { 
        bdiP(0), 
        bdiO(1), 
        bdi(2), 
        tim(3), 
        losP(4), 
        losO(5), 
        los(6) 
    }
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
     "Indicates the defect condition of the entity, if any. 
      This object is applicable when jnxoptIfOTSnDirectionality 
      has the value sink(1) or bidirectional(3). In 
      reduced-capability systems or at IrDI interfaces 
      the only bit position that may be set is los(6)."
    ::= { jnxoptIfOTSnConfigEntry 10 } 


-- OTSn sink current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 

jnxoptIfOTSnSinkCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTSn sink performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOTSn 2 } 


jnxoptIfOTSnSinkCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTSn sink performance 
      monitoring information of an interface for the current 
      15-minute interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOTSnSinkCurrentTable 1 } 


JnxoptIfOTSnSinkCurrentEntry ::=
    SEQUENCE { 
        jnxoptIfOTSnSinkCurrentSuspectedFlag             TruthValue, 
        jnxoptIfOTSnSinkCurrentInputPower                Integer32, 
        jnxoptIfOTSnSinkCurrentLowInputPower             Integer32, 
        jnxoptIfOTSnSinkCurrentHighInputPower            Integer32, 
        jnxoptIfOTSnSinkCurrentLowerInputPowerThreshold  Integer32, 
        jnxoptIfOTSnSinkCurrentUpperInputPowerThreshold  Integer32, 
        jnxoptIfOTSnSinkCurrentOutputPower               Integer32, 
        jnxoptIfOTSnSinkCurrentLowOutputPower            Integer32, 
        jnxoptIfOTSnSinkCurrentHighOutputPower           Integer32,
        jnxoptIfOTSnSinkCurrentLowerOutputPowerThreshold Integer32, 
        jnxoptIfOTSnSinkCurrentUpperOutputPowerThreshold Integer32 
    } 


jnxoptIfOTSnSinkCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSinkCurrentEntry 1 } 


jnxoptIfOTSnSinkCurrentInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the input." 
    ::= { jnxoptIfOTSnSinkCurrentEntry 2 } 


jnxoptIfOTSnSinkCurrentLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSinkCurrentEntry 3 } 


jnxoptIfOTSnSinkCurrentHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSinkCurrentEntry 4 } 


jnxoptIfOTSnSinkCurrentLowerInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on input power. If 
      jnxoptIfOTSnSinkCurrentInputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
     ::= { jnxoptIfOTSnSinkCurrentEntry 5 } 


jnxoptIfOTSnSinkCurrentUpperInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on input power. If 
      jnxoptIfOTSnSinkCurrentInputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
     ::= { jnxoptIfOTSnSinkCurrentEntry 6 } 


jnxoptIfOTSnSinkCurrentOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the output." 
    ::= { jnxoptIfOTSnSinkCurrentEntry 7 } 


jnxoptIfOTSnSinkCurrentLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSinkCurrentEntry 8 } 


jnxoptIfOTSnSinkCurrentHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSinkCurrentEntry 9 } 


jnxoptIfOTSnSinkCurrentLowerOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on output power. If 
      jnxoptIfOTSnSinkCurrentOutputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOTSnSinkCurrentEntry 10 } 


jnxoptIfOTSnSinkCurrentUpperOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on output power. If 
      jnxoptIfOTSnSinkCurrentOutputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOTSnSinkCurrentEntry 11 } 


-- OTSn sink interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOTSnSinkIntervalTable OBJECT-TYPE 
   SYNTAX SEQUENCE OF JnxoptIfOTSnSinkIntervalEntry 
   MAX-ACCESS not-accessible 
   STATUS current 
   DESCRIPTION
     "A table of historical OTSn sink performance monitoring 
     information." 
   ::= { jnxoptIfOTSn 3 } 


jnxoptIfOTSnSinkIntervalEntry OBJECT-TYPE 
   SYNTAX JnxoptIfOTSnSinkIntervalEntry 
   MAX-ACCESS not-accessible 
   STATUS current 
   DESCRIPTION
     "A conceptual row that contains OTSn sink performance 
      monitoring information of an interface during a particular 
      historical interval."
   INDEX { ifIndex, jnxoptIfOTSnSinkIntervalNumber } 
   ::= { jnxoptIfOTSnSinkIntervalTable 1 } 

JnxoptIfOTSnSinkIntervalEntry ::=

    SEQUENCE { 
        jnxoptIfOTSnSinkIntervalNumber          JnxoptIfIntervalNumber, 
        jnxoptIfOTSnSinkIntervalSuspectedFlag   TruthValue, 
        jnxoptIfOTSnSinkIntervalLastInputPower  Integer32, 
        jnxoptIfOTSnSinkIntervalLowInputPower   Integer32, 
        jnxoptIfOTSnSinkIntervalHighInputPower  Integer32, 
        jnxoptIfOTSnSinkIntervalLastOutputPower Integer32, 
        jnxoptIfOTSnSinkIntervalLowOutputPower  Integer32, 
        jnxoptIfOTSnSinkIntervalHighOutputPower Integer32
    } 


jnxoptIfOTSnSinkIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 1 } 


jnxoptIfOTSnSinkIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 2 } 


jnxoptIfOTSnSinkIntervalLastInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 3 } 


jnxoptIfOTSnSinkIntervalLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 4 } 



jnxoptIfOTSnSinkIntervalHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 5 } 


jnxoptIfOTSnSinkIntervalLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 6 } 


jnxoptIfOTSnSinkIntervalLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 7 } 

 
jnxoptIfOTSnSinkIntervalHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOTSnSinkIntervalEntry 8 } 


-- OTSn sink current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOTSnSinkCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of OTSn sink performance monitoring information for 
      the current 24-hour interval." 
    ::= { jnxoptIfOTSn 4 } 


jnxoptIfOTSnSinkCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTSn sink performance 
      monitoring information of an interface for the current 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOTSnSinkCurDayTable 1 } 
 

JnxoptIfOTSnSinkCurDayEntry ::=
    SEQUENCE { 
        jnxoptIfOTSnSinkCurDaySuspectedFlag TruthValue, 
        jnxoptIfOTSnSinkCurDayLowInputPower Integer32, 
        jnxoptIfOTSnSinkCurDayHighInputPower Integer32, 
        jnxoptIfOTSnSinkCurDayLowOutputPower Integer32, 
        jnxoptIfOTSnSinkCurDayHighOutputPower Integer32 
    } 


jnxoptIfOTSnSinkCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSinkCurDayEntry 1 } 


jnxoptIfOTSnSinkCurDayLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSinkCurDayEntry 2 } 


jnxoptIfOTSnSinkCurDayHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The highest optical power monitored at the input during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSinkCurDayEntry 3 } 
 

jnxoptIfOTSnSinkCurDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSinkCurDayEntry 4 } 


jnxoptIfOTSnSinkCurDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSinkCurDayEntry 5 } 
 

-- OTSn sink previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOTSnSinkPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTSn sink performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOTSn 5 } 


jnxoptIfOTSnSinkPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTSn sink performance 
      monitoring information of an interface for the previous 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOTSnSinkPrevDayTable 1 } 




JnxoptIfOTSnSinkPrevDayEntry ::=
    SEQUENCE { 
        jnxoptIfOTSnSinkPrevDaySuspectedFlag      TruthValue, 
        jnxoptIfOTSnSinkPrevDayLastInputPower     Integer32, 
        jnxoptIfOTSnSinkPrevDayLowInputPower      Integer32, 
        jnxoptIfOTSnSinkPrevDayHighInputPower     Integer32, 
        jnxoptIfOTSnSinkPrevDayLastOutputPower    Integer32, 
        jnxoptIfOTSnSinkPrevDayLowOutputPower     Integer32, 
        jnxoptIfOTSnSinkPrevDayHighOutputPower    Integer32 
    } 


jnxoptIfOTSnSinkPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSinkPrevDayEntry 1 } 


jnxoptIfOTSnSinkPrevDayLastInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSinkPrevDayEntry 2 } 


jnxoptIfOTSnSinkPrevDayLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSinkPrevDayEntry 3 } 


jnxoptIfOTSnSinkPrevDayHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSinkPrevDayEntry 4 } 



jnxoptIfOTSnSinkPrevDayLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSinkPrevDayEntry 5 } 


jnxoptIfOTSnSinkPrevDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSinkPrevDayEntry 6 } 


jnxoptIfOTSnSinkPrevDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSinkPrevDayEntry 7 } 


-- OTSn source current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 


jnxoptIfOTSnSrcCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTSn source performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOTSn 6 } 
 

jnxoptIfOTSnSrcCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A conceptual row that contains OTSn source performance 
      monitoring information of an interface for the current
      15-minute interval." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOTSnSrcCurrentTable 1 } 


JnxoptIfOTSnSrcCurrentEntry ::=
    SEQUENCE { 
        jnxoptIfOTSnSrcCurrentSuspectedFlag             TruthValue,
        jnxoptIfOTSnSrcCurrentOutputPower               Integer32, 
        jnxoptIfOTSnSrcCurrentLowOutputPower            Integer32, 
        jnxoptIfOTSnSrcCurrentHighOutputPower           Integer32,
        jnxoptIfOTSnSrcCurrentLowerOutputPowerThreshold Integer32, 
        jnxoptIfOTSnSrcCurrentUpperOutputPowerThreshold Integer32, 
        jnxoptIfOTSnSrcCurrentInputPower                Integer32, 
        jnxoptIfOTSnSrcCurrentLowInputPower             Integer32,
        jnxoptIfOTSnSrcCurrentHighInputPower            Integer32, 
        jnxoptIfOTSnSrcCurrentLowerInputPowerThreshold  Integer32, 
        jnxoptIfOTSnSrcCurrentUpperInputPowerThreshold  Integer32
    } 


jnxoptIfOTSnSrcCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSrcCurrentEntry 1 } 


jnxoptIfOTSnSrcCurrentOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the output." 
    ::= { jnxoptIfOTSnSrcCurrentEntry 2 } 


jnxoptIfOTSnSrcCurrentLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current
    DESCRIPTION 
      "The lowest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSrcCurrentEntry 3 } 



jnxoptIfOTSnSrcCurrentHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSrcCurrentEntry 4 } 


jnxoptIfOTSnSrcCurrentLowerOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on output power. If 
      jnxoptIfOTSnSrcCurrentOutputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOTSnSrcCurrentEntry 5 } 


jnxoptIfOTSnSrcCurrentUpperOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on output power. If 
      jnxoptIfOTSnSrcCurrentOutputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOTSnSrcCurrentEntry 6 } 


jnxoptIfOTSnSrcCurrentInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the input." 
    ::= { jnxoptIfOTSnSrcCurrentEntry 7 } 


jnxoptIfOTSnSrcCurrentLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current
    DESCRIPTION 
      "The lowest optical power monitored at the input during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSrcCurrentEntry 8 } 
 

jnxoptIfOTSnSrcCurrentHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      current 15-minute interval." 
    ::= { jnxoptIfOTSnSrcCurrentEntry 9 } 


jnxoptIfOTSnSrcCurrentLowerInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on input power. If 
      jnxoptIfOTSnSrcCurrentInputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOTSnSrcCurrentEntry 10 } 


jnxoptIfOTSnSrcCurrentUpperInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on input power. If 
      jnxoptIfOTSnSrcCurrentInputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOTSnSrcCurrentEntry 11 } 


-- OTSn source interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOTSnSrcIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of historical OTSn source performance monitoring 
      information." 
    ::= { jnxoptIfOTSn 7 } 


jnxoptIfOTSnSrcIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTSn source performance 
      monitoring information of an interface during a particular 
      historical interval."
    INDEX { ifIndex, jnxoptIfOTSnSrcIntervalNumber } 
    ::= { jnxoptIfOTSnSrcIntervalTable 1 } 
 

JnxoptIfOTSnSrcIntervalEntry ::=
    SEQUENCE { 
        jnxoptIfOTSnSrcIntervalNumber          JnxoptIfIntervalNumber, 
        jnxoptIfOTSnSrcIntervalSuspectedFlag   TruthValue, 
        jnxoptIfOTSnSrcIntervalLastOutputPower Integer32, 
        jnxoptIfOTSnSrcIntervalLowOutputPower  Integer32, 
        jnxoptIfOTSnSrcIntervalHighOutputPower Integer32, 
        jnxoptIfOTSnSrcIntervalLastInputPower  Integer32, 
        jnxoptIfOTSnSrcIntervalLowInputPower   Integer32, 
        jnxoptIfOTSnSrcIntervalHighInputPower  Integer32 
    } 


jnxoptIfOTSnSrcIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 1 } 


jnxoptIfOTSnSrcIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 2 } 


jnxoptIfOTSnSrcIntervalLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 3 } 



jnxoptIfOTSnSrcIntervalLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 4 } 


jnxoptIfOTSnSrcIntervalHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 5 } 


jnxoptIfOTSnSrcIntervalLastInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 6 } 
 

jnxoptIfOTSnSrcIntervalLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 7 } 


jnxoptIfOTSnSrcIntervalHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOTSnSrcIntervalEntry 8 } 


-- OTSn source current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOTSnSrcCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTSn source performance monitoring information for 
      the current 24-hour interval." 
    ::= { jnxoptIfOTSn 8 } 


jnxoptIfOTSnSrcCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTSn source performance 
      monitoring information of an interface for the current 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOTSnSrcCurDayTable 1 } 


JnxoptIfOTSnSrcCurDayEntry ::=
    SEQUENCE { 
        jnxoptIfOTSnSrcCurDaySuspectedFlag   TruthValue, 
        jnxoptIfOTSnSrcCurDayLowOutputPower  Integer32, 
        jnxoptIfOTSnSrcCurDayHighOutputPower Integer32, 
        jnxoptIfOTSnSrcCurDayLowInputPower   Integer32, 
        jnxoptIfOTSnSrcCurDayHighInputPower  Integer32 
    } 


jnxoptIfOTSnSrcCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSrcCurDayEntry 1 } 


jnxoptIfOTSnSrcCurDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The lowest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSrcCurDayEntry 2 } 


jnxoptIfOTSnSrcCurDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSrcCurDayEntry 3 } 


jnxoptIfOTSnSrcCurDayLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSrcCurDayEntry 4 } 


jnxoptIfOTSnSrcCurDayHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      current 24-hour interval." 
    ::= { jnxoptIfOTSnSrcCurDayEntry 5 } 


-- OTSn source previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOTSnSrcPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTSnSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTSn source performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOTSn 9 } 



jnxoptIfOTSnSrcPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTSnSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTSn source performance 
      monitoring information of an interface for the previous 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOTSnSrcPrevDayTable 1 } 


JnxoptIfOTSnSrcPrevDayEntry ::=
    SEQUENCE { 
        jnxoptIfOTSnSrcPrevDaySuspectedFlag   TruthValue, 
        jnxoptIfOTSnSrcPrevDayLastOutputPower Integer32, 
        jnxoptIfOTSnSrcPrevDayLowOutputPower  Integer32, 
        jnxoptIfOTSnSrcPrevDayHighOutputPower Integer32, 
        jnxoptIfOTSnSrcPrevDayLastInputPower  Integer32, 
        jnxoptIfOTSnSrcPrevDayLowInputPower   Integer32, 
        jnxoptIfOTSnSrcPrevDayHighInputPower  Integer32 
    } 


jnxoptIfOTSnSrcPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOTSnSrcPrevDayEntry 1 } 


jnxoptIfOTSnSrcPrevDayLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
       previous 24-hour interval." 
    ::= { jnxoptIfOTSnSrcPrevDayEntry 2 } 


jnxoptIfOTSnSrcPrevDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSrcPrevDayEntry 3 } 


jnxoptIfOTSnSrcPrevDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSrcPrevDayEntry 4 } 


jnxoptIfOTSnSrcPrevDayLastInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSrcPrevDayEntry 5 } 
   

jnxoptIfOTSnSrcPrevDayLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSrcPrevDayEntry 6 } 


jnxoptIfOTSnSrcPrevDayHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOTSnSrcPrevDayEntry 7 } 


-- the jnxoptIfOMSn group 
-- This group handles the configuration and performance monitoring 
-- information for OMS layers. 


-- OMSn config table 



jnxoptIfOMSnConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OMSn configuration information." 
    ::= { jnxoptIfOMSn 1 } 


jnxoptIfOMSnConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn configuration
      information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOMSnConfigTable 1 } 


JnxoptIfOMSnConfigEntry ::=
    SEQUENCE { 
        jnxoptIfOMSnDirectionality JnxoptIfDirectionality, 
        jnxoptIfOMSnCurrentStatus  BITS 
    } 


jnxoptIfOMSnDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfDirectionality 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the directionality of the entity." 
    ::= { jnxoptIfOMSnConfigEntry 1 } 


jnxoptIfOMSnCurrentStatus OBJECT-TYPE
    SYNTAX BITS { 
       ssfP(0), 
       ssfO(1), 
       ssf(2), 
       bdiP(3), 
       bdiO(4), 
       bdi(5), 
       losP(6) 
    }
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the defect condition of the entity, if any. 
      This object is applicable only to full capability 
      systems whose interface type is IaDI and for which 
      jnxoptIfOMSnDirectionality has the value sink(1) or 
      bidirectional(3)." 
    ::= { jnxoptIfOMSnConfigEntry 2 } 


-- OMSn sink current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 


jnxoptIfOMSnSinkCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OMSn sink performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOMSn 2 } 


jnxoptIfOMSnSinkCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn sink performance 
      monitoring information of an interface for the current
      15-minute interval." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOMSnSinkCurrentTable 1 } 


JnxoptIfOMSnSinkCurrentEntry ::=
    SEQUENCE { 
        jnxoptIfOMSnSinkCurrentSuspectedFlag              TruthValue,
        jnxoptIfOMSnSinkCurrentAggregatedInputPower       Integer32,
        jnxoptIfOMSnSinkCurrentLowAggregatedInputPower    Integer32,
        jnxoptIfOMSnSinkCurrentHighAggregatedInputPower   Integer32,
        jnxoptIfOMSnSinkCurrentLowerInputPowerThreshold   Integer32,
        jnxoptIfOMSnSinkCurrentUpperInputPowerThreshold   Integer32,
        jnxoptIfOMSnSinkCurrentOutputPower                Integer32,
        jnxoptIfOMSnSinkCurrentLowOutputPower             Integer32,
        jnxoptIfOMSnSinkCurrentHighOutputPower            Integer32,
        jnxoptIfOMSnSinkCurrentLowerOutputPowerThreshold  Integer32,
        jnxoptIfOMSnSinkCurrentUpperOutputPowerThreshold  Integer32
    } 


jnxoptIfOMSnSinkCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOMSnSinkCurrentEntry 1 } 


jnxoptIfOMSnSinkCurrentAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The aggregated optical power of all the DWDM input 
      channels." 
    ::= { jnxoptIfOMSnSinkCurrentEntry 2 } 


jnxoptIfOMSnSinkCurrentLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels during the current 15-minute interval." 
    ::= { jnxoptIfOMSnSinkCurrentEntry 3 } 


jnxoptIfOMSnSinkCurrentHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power of all the DWDM input 
      channels during the current 15-minute interval." 
    ::= { jnxoptIfOMSnSinkCurrentEntry 4 } 


jnxoptIfOMSnSinkCurrentLowerInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on aggregated input power. If 
      jnxoptIfOMSnSinkCurrentAggregatedInputPower drops to this value 
      or below, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSinkCurrentEntry 5 } 


jnxoptIfOMSnSinkCurrentUpperInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on aggregated input power. If 
      jnxoptIfOMSnSinkCurrentAggregatedInputPower reaches or exceeds 
      this value, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSinkCurrentEntry 6 } 


jnxoptIfOMSnSinkCurrentOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the output." 
      ::= { jnxoptIfOMSnSinkCurrentEntry 7 } 
  

jnxoptIfOMSnSinkCurrentLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output 
      during the current 15-minute interval." 
    ::= { jnxoptIfOMSnSinkCurrentEntry 8 } 


jnxoptIfOMSnSinkCurrentHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output 
      during the current 15-minute interval." 
    ::= { jnxoptIfOMSnSinkCurrentEntry 9 } 


jnxoptIfOMSnSinkCurrentLowerOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on output power. If 
      jnxoptIfOMSnSinkCurrentOutputPower drops to this value 
      or below, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSinkCurrentEntry 10 } 


jnxoptIfOMSnSinkCurrentUpperOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on output power. If 
      jnxoptIfOMSnSinkCurrentOutputPower reaches or exceeds 
      this value, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSinkCurrentEntry 11 } 


-- OMSn sink interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOMSnSinkIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSinkIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of historical OMSn sink performance monitoring 
      information." 
    ::= { jnxoptIfOMSn 3 } 


jnxoptIfOMSnSinkIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSinkIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn sink performance 
      monitoring information of an interface during a particular 
      historical interval."
    INDEX { ifIndex, jnxoptIfOMSnSinkIntervalNumber } 
    ::= { jnxoptIfOMSnSinkIntervalTable 1 } 
  

JnxoptIfOMSnSinkIntervalEntry ::=
    SEQUENCE { 
        jnxoptIfOMSnSinkIntervalNumber                   JnxoptIfIntervalNumber,
        jnxoptIfOMSnSinkIntervalSuspectedFlag            TruthValue,
        jnxoptIfOMSnSinkIntervalLastAggregatedInputPower Integer32,
        jnxoptIfOMSnSinkIntervalLowAggregatedInputPower  Integer32,
        jnxoptIfOMSnSinkIntervalHighAggregatedInputPower Integer32,
        jnxoptIfOMSnSinkIntervalLastOutputPower          Integer32,
        jnxoptIfOMSnSinkIntervalLowOutputPower           Integer32,
        jnxoptIfOMSnSinkIntervalHighOutputPower          Integer32
    } 


jnxoptIfOMSnSinkIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 1 } 


jnxoptIfOMSnSinkIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 2 } 


jnxoptIfOMSnSinkIntervalLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power of all the DWDM input 
      channels during the interval." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 3 } 


jnxoptIfOMSnSinkIntervalLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels during the interval." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 4 } 


jnxoptIfOMSnSinkIntervalHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power of all the DWDM input 
      channels during the interval." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 5 } 


jnxoptIfOMSnSinkIntervalLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The last optical power at the output 
      during the interval." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 6 } 


jnxoptIfOMSnSinkIntervalLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power at the output 
      during the interval." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 7 } 


jnxoptIfOMSnSinkIntervalHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power at the output 
      during the interval." 
    ::= { jnxoptIfOMSnSinkIntervalEntry 8 } 


-- OMSn sink current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOMSnSinkCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OMSn sink performance monitoring information for 
      the current 24-hour interval." 
    ::= { jnxoptIfOMSn 4 } 


jnxoptIfOMSnSinkCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn sink performance 
      monitoring information of an interface for the current 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOMSnSinkCurDayTable 1 } 
 

JnxoptIfOMSnSinkCurDayEntry ::=
    SEQUENCE { 
        jnxoptIfOMSnSinkCurDaySuspectedFlag             TruthValue,
        jnxoptIfOMSnSinkCurDayLowAggregatedInputPower   Integer32,
        jnxoptIfOMSnSinkCurDayHighAggregatedInputPower  Integer32,
        jnxoptIfOMSnSinkCurDayLowOutputPower            Integer32,
        jnxoptIfOMSnSinkCurDayHighOutputPower           Integer32
    } 


jnxoptIfOMSnSinkCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
      ::= { jnxoptIfOMSnSinkCurDayEntry 1 } 
  

jnxoptIfOMSnSinkCurDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels during the current 24-hour interval." 
    ::= { jnxoptIfOMSnSinkCurDayEntry 2 } 


jnxoptIfOMSnSinkCurDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power of all the DWDM input 
      channels during the current 24-hour interval." 
    ::= { jnxoptIfOMSnSinkCurDayEntry 3 } 


jnxoptIfOMSnSinkCurDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power at the output 
      during the current 24-hour interval." 
    ::= { jnxoptIfOMSnSinkCurDayEntry 4 } 



jnxoptIfOMSnSinkCurDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power at the output 
      during the current 24-hour interval." 
    ::= { jnxoptIfOMSnSinkCurDayEntry 5 } 


-- OMSn sink previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOMSnSinkPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OMSn sink performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOMSn 5 } 


jnxoptIfOMSnSinkPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn sink performance 
      monitoring information of an interface for the previous 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOMSnSinkPrevDayTable 1 } 


JnxoptIfOMSnSinkPrevDayEntry ::=
    SEQUENCE { 
       jnxoptIfOMSnSinkPrevDaySuspectedFlag             TruthValue,
       jnxoptIfOMSnSinkPrevDayLastAggregatedInputPower  Integer32,
       jnxoptIfOMSnSinkPrevDayLowAggregatedInputPower   Integer32,
       jnxoptIfOMSnSinkPrevDayHighAggregatedInputPower  Integer32,
       jnxoptIfOMSnSinkPrevDayLastOutputPower           Integer32,
       jnxoptIfOMSnSinkPrevDayLowOutputPower            Integer32,
       jnxoptIfOMSnSinkPrevDayHighOutputPower           Integer32
    } 
 

jnxoptIfOMSnSinkPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOMSnSinkPrevDayEntry 1 } 


jnxoptIfOMSnSinkPrevDayLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power of all the DWDM input 
      channels during the previous 24-hour interval." 
    ::= { jnxoptIfOMSnSinkPrevDayEntry 2 } 


jnxoptIfOMSnSinkPrevDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels during the previous 24-hour interval." 
    ::= { jnxoptIfOMSnSinkPrevDayEntry 3 } 


jnxoptIfOMSnSinkPrevDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power of all the DWDM input 
      channels during the previous 24-hour interval." 
    ::= { jnxoptIfOMSnSinkPrevDayEntry 4 } 


jnxoptIfOMSnSinkPrevDayLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power at the output 
      during the previous 24-hour interval." 
    ::= { jnxoptIfOMSnSinkPrevDayEntry 5 } 


jnxoptIfOMSnSinkPrevDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power at the output 
      during the previous 24-hour interval." 
    ::= { jnxoptIfOMSnSinkPrevDayEntry 6 } 


jnxoptIfOMSnSinkPrevDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power at the output 
      during the previous 24-hour interval." 
    ::= { jnxoptIfOMSnSinkPrevDayEntry 7 } 


-- OMSn source current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 


jnxoptIfOMSnSrcCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OMSn source performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOMSn 6 } 


jnxoptIfOMSnSrcCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn source performance 
      monitoring information of an interface for the current
      15-minute interval." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOMSnSrcCurrentTable 1 } 
 
JnxoptIfOMSnSrcCurrentEntry ::= 
    SEQUENCE { 
        jnxoptIfOMSnSrcCurrentSuspectedFlag              TruthValue,
        jnxoptIfOMSnSrcCurrentOutputPower                Integer32,
        jnxoptIfOMSnSrcCurrentLowOutputPower             Integer32,
        jnxoptIfOMSnSrcCurrentHighOutputPower            Integer32,
        jnxoptIfOMSnSrcCurrentLowerOutputPowerThreshold  Integer32,
        jnxoptIfOMSnSrcCurrentUpperOutputPowerThreshold  Integer32,
        jnxoptIfOMSnSrcCurrentAggregatedInputPower       Integer32,
        jnxoptIfOMSnSrcCurrentLowAggregatedInputPower    Integer32,
        jnxoptIfOMSnSrcCurrentHighAggregatedInputPower   Integer32,
        jnxoptIfOMSnSrcCurrentLowerInputPowerThreshold   Integer32,
        jnxoptIfOMSnSrcCurrentUpperInputPowerThreshold   Integer32 
    }  


jnxoptIfOMSnSrcCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOMSnSrcCurrentEntry 1 } 


jnxoptIfOMSnSrcCurrentOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the output." 
    ::= { jnxoptIfOMSnSrcCurrentEntry 2 } 


jnxoptIfOMSnSrcCurrentLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      current 15-minute interval." 
      ::= { jnxoptIfOMSnSrcCurrentEntry 3 } 
  

jnxoptIfOMSnSrcCurrentHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOMSnSrcCurrentEntry 4 } 


jnxoptIfOMSnSrcCurrentLowerOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on output power. If 
      jnxoptIfOMSnSrcCurrentOutputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSrcCurrentEntry 5 } 


jnxoptIfOMSnSrcCurrentUpperOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on output power. If 
      jnxoptIfOMSnSrcCurrentOutputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSrcCurrentEntry 6 } 


jnxoptIfOMSnSrcCurrentAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The aggregated optical power at the input." 
    ::= { jnxoptIfOMSnSrcCurrentEntry 7 } 
  

jnxoptIfOMSnSrcCurrentLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power at the input 
      during the current 15-minute interval." 
    ::= { jnxoptIfOMSnSrcCurrentEntry 8 } 


jnxoptIfOMSnSrcCurrentHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power at the input 
      during the current 15-minute interval." 
    ::= { jnxoptIfOMSnSrcCurrentEntry 9 } 


jnxoptIfOMSnSrcCurrentLowerInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on aggregated input power. If 
      jnxoptIfOMSnSrcCurrentAggregatedInputPower drops to this value 
      or below, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSrcCurrentEntry 10 } 


jnxoptIfOMSnSrcCurrentUpperInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on aggregated input power. If 
      jnxoptIfOMSnSrcCurrentAggregatedInputPower reaches or exceeds 
      this value, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOMSnSrcCurrentEntry 11 } 


-- OMSn source interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOMSnSrcIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of historical OMSn source performance monitoring 
      information." 
    ::= { jnxoptIfOMSn 7 } 


jnxoptIfOMSnSrcIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn source performance 
      monitoring information of an interface during a particular 
      historical interval."
    INDEX { ifIndex, jnxoptIfOMSnSrcIntervalNumber } 
    ::= { jnxoptIfOMSnSrcIntervalTable 1 } 
 

JnxoptIfOMSnSrcIntervalEntry ::= 
    SEQUENCE { 
        jnxoptIfOMSnSrcIntervalNumber                   JnxoptIfIntervalNumber,
        jnxoptIfOMSnSrcIntervalSuspectedFlag            TruthValue,
        jnxoptIfOMSnSrcIntervalLastOutputPower          Integer32,
        jnxoptIfOMSnSrcIntervalLowOutputPower           Integer32,
        jnxoptIfOMSnSrcIntervalHighOutputPower          Integer32,
        jnxoptIfOMSnSrcIntervalLastAggregatedInputPower Integer32,
        jnxoptIfOMSnSrcIntervalLowAggregatedInputPower  Integer32,
        jnxoptIfOMSnSrcIntervalHighAggregatedInputPower Integer32
    }
 
jnxoptIfOMSnSrcIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 1 } 

jnxoptIfOMSnSrcIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 2 } 


jnxoptIfOMSnSrcIntervalLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 3 } 


jnxoptIfOMSnSrcIntervalLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 4 } 


jnxoptIfOMSnSrcIntervalHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 5 } 


jnxoptIfOMSnSrcIntervalLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power at the input 
      during the interval." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 6 } 


jnxoptIfOMSnSrcIntervalLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power at the input 
      during the interval." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 7 } 


jnxoptIfOMSnSrcIntervalHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power at the input 
      during the interval." 
    ::= { jnxoptIfOMSnSrcIntervalEntry 8 } 


-- OMSn source current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOMSnSrcCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of OMSn source performance monitoring information for 
      the current 24-hour interval." 
    ::= { jnxoptIfOMSn 8 } 


jnxoptIfOMSnSrcCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn source performance 
      monitoring information of an interface for the current 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOMSnSrcCurDayTable 1 } 


JnxoptIfOMSnSrcCurDayEntry ::=
    SEQUENCE { 
       jnxoptIfOMSnSrcCurDaySuspectedFlag             TruthValue,
       jnxoptIfOMSnSrcCurDayLowOutputPower            Integer32,
       jnxoptIfOMSnSrcCurDayHighOutputPower           Integer32,
       jnxoptIfOMSnSrcCurDayLowAggregatedInputPower   Integer32,
       jnxoptIfOMSnSrcCurDayHighAggregatedInputPower  Integer32
    } 
 

jnxoptIfOMSnSrcCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOMSnSrcCurDayEntry 1 } 


jnxoptIfOMSnSrcCurDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOMSnSrcCurDayEntry 2 } 


jnxoptIfOMSnSrcCurDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The highest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOMSnSrcCurDayEntry 3 } 


jnxoptIfOMSnSrcCurDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power at the input 
      during the current 24-hour interval." 
    ::= { jnxoptIfOMSnSrcCurDayEntry 4 } 


jnxoptIfOMSnSrcCurDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power at the input 
      during the current 24-hour interval." 
    ::= { jnxoptIfOMSnSrcCurDayEntry 5 } 
 

-- OMSn source previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOMSnSrcPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOMSnSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OMSn source performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOMSn 9 } 
 

jnxoptIfOMSnSrcPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOMSnSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OMSn source performance 
      monitoring information of an interface for the previous 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOMSnSrcPrevDayTable 1 } 



JnxoptIfOMSnSrcPrevDayEntry ::=
    SEQUENCE { 
        jnxoptIfOMSnSrcPrevDaySuspectedFlag             TruthValue,
        jnxoptIfOMSnSrcPrevDayLastOutputPower           Integer32,
        jnxoptIfOMSnSrcPrevDayLowOutputPower            Integer32,
        jnxoptIfOMSnSrcPrevDayHighOutputPower           Integer32,
        jnxoptIfOMSnSrcPrevDayLastAggregatedInputPower  Integer32,
        jnxoptIfOMSnSrcPrevDayLowAggregatedInputPower   Integer32,
        jnxoptIfOMSnSrcPrevDayHighAggregatedInputPower  Integer32
    }   


jnxoptIfOMSnSrcPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOMSnSrcPrevDayEntry 1 } 


jnxoptIfOMSnSrcPrevDayLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOMSnSrcPrevDayEntry 2 } 


jnxoptIfOMSnSrcPrevDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOMSnSrcPrevDayEntry 3 } 


jnxoptIfOMSnSrcPrevDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOMSnSrcPrevDayEntry 4 } 



jnxoptIfOMSnSrcPrevDayLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOMSnSrcPrevDayEntry 5 } 


jnxoptIfOMSnSrcPrevDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOMSnSrcPrevDayEntry 6 } 


jnxoptIfOMSnSrcPrevDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOMSnSrcPrevDayEntry 7 } 


-- the jnxoptIfOChGroup group 
-- This group handles the configuration and performance monitoring 
-- information for OChGroup layers. 


-- OChGroup config table 


jnxoptIfOChGroupConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OChGroup configuration information." 
    ::= { jnxoptIfOChGroup 1 } 


jnxoptIfOChGroupConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A conceptual row that contains OChGroup configuration
      information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOChGroupConfigTable 1 } 
  

JnxoptIfOChGroupConfigEntry ::=
    SEQUENCE { 
        jnxoptIfOChGroupDirectionality JnxoptIfDirectionality 
    } 


jnxoptIfOChGroupDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfDirectionality 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the directionality of the entity." 
    ::= { jnxoptIfOChGroupConfigEntry 1 } 


-- OChGroup sink current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 


jnxoptIfOChGroupSinkCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OChGroup sink performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOChGroup 2 } 


jnxoptIfOChGroupSinkCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup sink performance 
      monitoring information of an interface for the current
      15-minute interval." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOChGroupSinkCurrentTable 1 } 

JnxoptIfOChGroupSinkCurrentEntry ::= 
    SEQUENCE { 
        jnxoptIfOChGroupSinkCurrentSuspectedFlag              TruthValue,
        jnxoptIfOChGroupSinkCurrentAggregatedInputPower       Integer32,
        jnxoptIfOChGroupSinkCurrentLowAggregatedInputPower    Integer32,
        jnxoptIfOChGroupSinkCurrentHighAggregatedInputPower   Integer32,
        jnxoptIfOChGroupSinkCurrentLowerInputPowerThreshold   Integer32,
        jnxoptIfOChGroupSinkCurrentUpperInputPowerThreshold   Integer32,
        jnxoptIfOChGroupSinkCurrentOutputPower                Integer32,
        jnxoptIfOChGroupSinkCurrentLowOutputPower             Integer32,
        jnxoptIfOChGroupSinkCurrentHighOutputPower            Integer32,
        jnxoptIfOChGroupSinkCurrentLowerOutputPowerThreshold  Integer32,
        jnxoptIfOChGroupSinkCurrentUpperOutputPowerThreshold  Integer32
    } 


jnxoptIfOChGroupSinkCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSinkCurrentEntry 1 } 


jnxoptIfOChGroupSinkCurrentAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The aggregated optical power of all the DWDM input 
      channels in the OChGroup." 
    ::= { jnxoptIfOChGroupSinkCurrentEntry 2 } 


jnxoptIfOChGroupSinkCurrentLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the current 15-minute interval." 
    ::= { jnxoptIfOChGroupSinkCurrentEntry 3 } 


jnxoptIfOChGroupSinkCurrentHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the current 15-minute interval." 
    ::= { jnxoptIfOChGroupSinkCurrentEntry 4 } 




jnxoptIfOChGroupSinkCurrentLowerInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on aggregated input power. If 
      jnxoptIfOChGroupSinkCurrentAggregatedInputPower drops to this value 
      or below, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSinkCurrentEntry 5 } 


jnxoptIfOChGroupSinkCurrentUpperInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on aggregated input power. If 
      jnxoptIfOChGroupSinkCurrentAggregatedInputPower reaches or exceeds 
      this value, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSinkCurrentEntry 6 } 


jnxoptIfOChGroupSinkCurrentOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the output 
      in the OChGroup." 
    ::= { jnxoptIfOChGroupSinkCurrentEntry 7 } 


jnxoptIfOChGroupSinkCurrentLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output 
      in the OChGroup during the current 15-minute interval." 
    ::= { jnxoptIfOChGroupSinkCurrentEntry 8 } 


jnxoptIfOChGroupSinkCurrentHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The highest optical power monitored at the output 
      in the OChGroup during the current 15-minute interval." 
    ::= { jnxoptIfOChGroupSinkCurrentEntry 9 } 


jnxoptIfOChGroupSinkCurrentLowerOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on the output power. If 
      jnxoptIfOChGroupSinkCurrentOutputPower drops to this value 
      or below, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSinkCurrentEntry 10 } 


jnxoptIfOChGroupSinkCurrentUpperOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on the output power. If 
      jnxoptIfOChGroupSinkCurrentOutputPower reaches or exceeds 
      this value, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSinkCurrentEntry 11 } 


-- OChGroup sink interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOChGroupSinkIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSinkIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of historical OChGroup sink performance monitoring 
      information." 
    ::= { jnxoptIfOChGroup 3 } 


jnxoptIfOChGroupSinkIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSinkIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup sink performance 
      monitoring information of an interface during a particular 
      historical interval."
    INDEX { ifIndex, jnxoptIfOChGroupSinkIntervalNumber } 
    ::= { jnxoptIfOChGroupSinkIntervalTable 1 } 


JnxoptIfOChGroupSinkIntervalEntry ::=
    SEQUENCE { 
        jnxoptIfOChGroupSinkIntervalNumber           JnxoptIfIntervalNumber,
        jnxoptIfOChGroupSinkIntervalSuspectedFlag            TruthValue,
        jnxoptIfOChGroupSinkIntervalLastAggregatedInputPower Integer32,
        jnxoptIfOChGroupSinkIntervalLowAggregatedInputPower  Integer32,
        jnxoptIfOChGroupSinkIntervalHighAggregatedInputPower Integer32,
        jnxoptIfOChGroupSinkIntervalLastOutputPower          Integer32,
        jnxoptIfOChGroupSinkIntervalLowOutputPower           Integer32,
        jnxoptIfOChGroupSinkIntervalHighOutputPower          Integer32
    }   


jnxoptIfOChGroupSinkIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 1 } 


jnxoptIfOChGroupSinkIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 2 } 


jnxoptIfOChGroupSinkIntervalLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power of all the DWDM input 
      channels in the OChGroup during the interval." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 3 } 
  

jnxoptIfOChGroupSinkIntervalLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the interval." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 4 } 


jnxoptIfOChGroupSinkIntervalHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the interval." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 5 } 
 

jnxoptIfOChGroupSinkIntervalLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output 
      in the OChGroup during the interval." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 6 } 


jnxoptIfOChGroupSinkIntervalLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output 
      in the OChGroup during the interval." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 7 } 


jnxoptIfOChGroupSinkIntervalHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output 
      in the OChGroup during the interval." 
    ::= { jnxoptIfOChGroupSinkIntervalEntry 8 } 


-- OChGroup sink current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOChGroupSinkCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OChGroup sink performance monitoring information for 
      the current 24-hour interval." 
    ::= { jnxoptIfOChGroup 4 } 


jnxoptIfOChGroupSinkCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup sink performance 
      monitoring information of an interface for the current 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChGroupSinkCurDayTable 1 } 


JnxoptIfOChGroupSinkCurDayEntry ::=
     SEQUENCE { 
         jnxoptIfOChGroupSinkCurDaySuspectedFlag             TruthValue,
         jnxoptIfOChGroupSinkCurDayLowAggregatedInputPower   Integer32,
         jnxoptIfOChGroupSinkCurDayHighAggregatedInputPower  Integer32,
         jnxoptIfOChGroupSinkCurDayLowOutputPower            Integer32,
         jnxoptIfOChGroupSinkCurDayHighOutputPower           Integer32
    } 


jnxoptIfOChGroupSinkCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSinkCurDayEntry 1 } 


jnxoptIfOChGroupSinkCurDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the current 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkCurDayEntry 2 } 
 

jnxoptIfOChGroupSinkCurDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the current 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkCurDayEntry 3 } 


jnxoptIfOChGroupSinkCurDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output 
      in the OChGroup during the current 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkCurDayEntry 4 } 


jnxoptIfOChGroupSinkCurDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output 
      in the OChGroup during the current 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkCurDayEntry 5 } 
  

-- OChGroup sink previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOChGroupSinkPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OChGroup sink performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOChGroup 5 } 


jnxoptIfOChGroupSinkPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup sink performance 
      monitoring information of an interface for the previous 
      24-hour interval." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOChGroupSinkPrevDayTable 1 } 


JnxoptIfOChGroupSinkPrevDayEntry ::=
    SEQUENCE { 
        jnxoptIfOChGroupSinkPrevDaySuspectedFlag             TruthValue,
        jnxoptIfOChGroupSinkPrevDayLastAggregatedInputPower  Integer32,
        jnxoptIfOChGroupSinkPrevDayLowAggregatedInputPower   Integer32,
        jnxoptIfOChGroupSinkPrevDayHighAggregatedInputPower  Integer32,
        jnxoptIfOChGroupSinkPrevDayLastOutputPower           Integer32,
        jnxoptIfOChGroupSinkPrevDayLowOutputPower            Integer32,
        jnxoptIfOChGroupSinkPrevDayHighOutputPower           Integer32
    }   


jnxoptIfOChGroupSinkPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSinkPrevDayEntry 1 } 


jnxoptIfOChGroupSinkPrevDayLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power of all the DWDM input 
      channels in the OChGroup during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkPrevDayEntry 2 } 


jnxoptIfOChGroupSinkPrevDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkPrevDayEntry 3 } 
  

jnxoptIfOChGroupSinkPrevDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The highest aggregated optical power of all the DWDM input 
      channels in the OChGroup during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkPrevDayEntry 4 } 


jnxoptIfOChGroupSinkPrevDayLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output 
      in the OChGroup during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkPrevDayEntry 5 } 
  

jnxoptIfOChGroupSinkPrevDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output 
      in the OChGroup during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkPrevDayEntry 6 } 


jnxoptIfOChGroupSinkPrevDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output 
      in the OChGroup during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSinkPrevDayEntry 7 } 
 

-- OChGroup source current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 


jnxoptIfOChGroupSrcCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OChGroup source performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOChGroup 6 } 
  

jnxoptIfOChGroupSrcCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup source performance 
      monitoring information of an interface for the current 
      15-minute interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChGroupSrcCurrentTable 1 } 


JnxoptIfOChGroupSrcCurrentEntry ::=
    SEQUENCE { 
        jnxoptIfOChGroupSrcCurrentSuspectedFlag              TruthValue,
        jnxoptIfOChGroupSrcCurrentOutputPower                Integer32,
        jnxoptIfOChGroupSrcCurrentLowOutputPower             Integer32,
        jnxoptIfOChGroupSrcCurrentHighOutputPower            Integer32,
        jnxoptIfOChGroupSrcCurrentLowerOutputPowerThreshold  Integer32,
        jnxoptIfOChGroupSrcCurrentUpperOutputPowerThreshold  Integer32,
        jnxoptIfOChGroupSrcCurrentAggregatedInputPower       Integer32,
        jnxoptIfOChGroupSrcCurrentLowAggregatedInputPower    Integer32,
        jnxoptIfOChGroupSrcCurrentHighAggregatedInputPower   Integer32,
        jnxoptIfOChGroupSrcCurrentLowerInputPowerThreshold   Integer32,
        jnxoptIfOChGroupSrcCurrentUpperInputPowerThreshold   Integer32 
    } 
 

jnxoptIfOChGroupSrcCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSrcCurrentEntry 1 } 


jnxoptIfOChGroupSrcCurrentOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the output." 
    ::= { jnxoptIfOChGroupSrcCurrentEntry 2 } 
  

jnxoptIfOChGroupSrcCurrentLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The lowest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOChGroupSrcCurrentEntry 3 } 


jnxoptIfOChGroupSrcCurrentHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOChGroupSrcCurrentEntry 4 } 
 

jnxoptIfOChGroupSrcCurrentLowerOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on output power. If 
      jnxoptIfOChGroupSrcCurrentOutputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSrcCurrentEntry 5 } 


jnxoptIfOChGroupSrcCurrentUpperOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on output power. If 
      jnxoptIfOChGroupSrcCurrentOutputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSrcCurrentEntry 6 } 


jnxoptIfOChGroupSrcCurrentAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The aggregated optical power monitored at the input." 
    ::= { jnxoptIfOChGroupSrcCurrentEntry 7 } 
   

jnxoptIfOChGroupSrcCurrentLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power monitored at the input 
      during the current 15-minute interval." 
    ::= { jnxoptIfOChGroupSrcCurrentEntry 8 } 


jnxoptIfOChGroupSrcCurrentHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power monitored at the input 
      during the current 15-minute interval." 
    ::= { jnxoptIfOChGroupSrcCurrentEntry 9 } 


jnxoptIfOChGroupSrcCurrentLowerInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on input power. If 
      jnxoptIfOChGroupSrcCurrentAggregatedInputPower drops to this value 
      or below, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSrcCurrentEntry 10 } 


jnxoptIfOChGroupSrcCurrentUpperInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on input power. If 
      jnxoptIfOChGroupSrcCurrentAggregatedInputPower reaches or exceeds 
      this value, a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChGroupSrcCurrentEntry 11 } 


-- OChGroup source interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOChGroupSrcIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of historical OChGroup source performance monitoring 
      information." 
    ::= { jnxoptIfOChGroup 7 } 


jnxoptIfOChGroupSrcIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup source performance 
      monitoring information of an interface during a particular 
      historical interval."
    INDEX { ifIndex, jnxoptIfOChGroupSrcIntervalNumber } 
    ::= { jnxoptIfOChGroupSrcIntervalTable 1 } 


JnxoptIfOChGroupSrcIntervalEntry ::=
    SEQUENCE { 
        jnxoptIfOChGroupSrcIntervalNumber               JnxoptIfIntervalNumber,
        jnxoptIfOChGroupSrcIntervalSuspectedFlag            TruthValue,
        jnxoptIfOChGroupSrcIntervalLastOutputPower          Integer32,
        jnxoptIfOChGroupSrcIntervalLowOutputPower           Integer32,
        jnxoptIfOChGroupSrcIntervalHighOutputPower          Integer32,
        jnxoptIfOChGroupSrcIntervalLastAggregatedInputPower Integer32,
        jnxoptIfOChGroupSrcIntervalLowAggregatedInputPower  Integer32,
        jnxoptIfOChGroupSrcIntervalHighAggregatedInputPower Integer32 
     } 


jnxoptIfOChGroupSrcIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 1 } 


jnxoptIfOChGroupSrcIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 2 } 


jnxoptIfOChGroupSrcIntervalLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The last optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 3 } 


jnxoptIfOChGroupSrcIntervalLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 4 } 


jnxoptIfOChGroupSrcIntervalHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 5 } 


jnxoptIfOChGroupSrcIntervalLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power monitored at the input 
      during the interval." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 6 } 
 

jnxoptIfOChGroupSrcIntervalLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power monitored at the input 
      during the interval." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 7 } 


jnxoptIfOChGroupSrcIntervalHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power monitored at the input 
      during the interval." 
    ::= { jnxoptIfOChGroupSrcIntervalEntry 8 } 


-- OChGroup source current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOChGroupSrcCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OChGroup source performance monitoring information for 
      the current 24-hour interval." 
    ::= { jnxoptIfOChGroup 8 } 


jnxoptIfOChGroupSrcCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup source performance 
      monitoring information of an interface for the current 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChGroupSrcCurDayTable 1 } 


JnxoptIfOChGroupSrcCurDayEntry ::=
    SEQUENCE { 
        jnxoptIfOChGroupSrcCurDaySuspectedFlag             TruthValue,
        jnxoptIfOChGroupSrcCurDayLowOutputPower            Integer32,
        jnxoptIfOChGroupSrcCurDayHighOutputPower           Integer32,
        jnxoptIfOChGroupSrcCurDayLowAggregatedInputPower   Integer32,
        jnxoptIfOChGroupSrcCurDayHighAggregatedInputPower  Integer32
    } 


jnxoptIfOChGroupSrcCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSrcCurDayEntry 1 } 


jnxoptIfOChGroupSrcCurDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcCurDayEntry 2 } 


jnxoptIfOChGroupSrcCurDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcCurDayEntry 3 } 


jnxoptIfOChGroupSrcCurDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power monitored at the input 
      during the current 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcCurDayEntry 4 } 


jnxoptIfOChGroupSrcCurDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power monitored at the input 
      during the current 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcCurDayEntry 5 } 


-- OChGroup source previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOChGroupSrcPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChGroupSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of OChGroup source performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOChGroup 9 } 


jnxoptIfOChGroupSrcPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChGroupSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OChGroup source performance 
      monitoring information of an interface for the previous 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChGroupSrcPrevDayTable 1 } 


JnxoptIfOChGroupSrcPrevDayEntry ::=


    SEQUENCE { 
        jnxoptIfOChGroupSrcPrevDaySuspectedFlag            TruthValue, 
        jnxoptIfOChGroupSrcPrevDayLastOutputPower          Integer32, 
        jnxoptIfOChGroupSrcPrevDayLowOutputPower           Integer32, 
        jnxoptIfOChGroupSrcPrevDayHighOutputPower          Integer32, 
        jnxoptIfOChGroupSrcPrevDayLastAggregatedInputPower Integer32, 
        jnxoptIfOChGroupSrcPrevDayLowAggregatedInputPower  Integer32, 
        jnxoptIfOChGroupSrcPrevDayHighAggregatedInputPower Integer32 
    } 


jnxoptIfOChGroupSrcPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChGroupSrcPrevDayEntry 1 } 


jnxoptIfOChGroupSrcPrevDayLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcPrevDayEntry 2 } 


jnxoptIfOChGroupSrcPrevDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcPrevDayEntry 3 } 


jnxoptIfOChGroupSrcPrevDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcPrevDayEntry 4 } 


jnxoptIfOChGroupSrcPrevDayLastAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last aggregated optical power monitored at the input 
      during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcPrevDayEntry 5 } 


jnxoptIfOChGroupSrcPrevDayLowAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest aggregated optical power monitored at the input 
      during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcPrevDayEntry 6 } 


jnxoptIfOChGroupSrcPrevDayHighAggregatedInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest aggregated optical power monitored at the input 
      during the previous 24-hour interval." 
    ::= { jnxoptIfOChGroupSrcPrevDayEntry 7 } 


-- the jnxoptIfOCh group 


-- This group handles the configuration and 
-- performance monitoring information for OCh layers. 


-- OCh config table 


jnxoptIfOChConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OCh configuration information." 
    ::= { jnxoptIfOCh 1 } 


jnxoptIfOChConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh configuration
      information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOChConfigTable 1 } 
 

JnxoptIfOChConfigEntry ::=
    SEQUENCE { 
        jnxoptIfOChDirectionality JnxoptIfDirectionality, 
        jnxoptIfOChCurrentStatus  BITS  
    } 


jnxoptIfOChDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfDirectionality 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the directionality of the entity." 
    ::= { jnxoptIfOChConfigEntry 1 } 


jnxoptIfOChCurrentStatus OBJECT-TYPE
    SYNTAX BITS { 
        losP(0), 
        los(1), 
        oci(2), 
        ssfP(3), 
        ssfO(4), 
        ssf(5) 
    }
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the defect condition of the entity, if any. 
      This object is applicable when jnxoptIfOChDirectionality 
      has the value sink(1) or bidirectional(3). 
      In full-capability systems the bit position los(1) is not used. 
      In reduced-capability systems or at IrDI interfaces only 
      the bit positions los(1) and ssfP(3) are used."
    ::= { jnxoptIfOChConfigEntry 2 } 


-- OCh sink current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 


jnxoptIfOChSinkCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OCh sink performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOCh 2 } 


jnxoptIfOChSinkCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSinkCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh sink performance 
      monitoring information for an interface for the current 
      15-minute interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChSinkCurrentTable 1 } 


JnxoptIfOChSinkCurrentEntry ::=
    SEQUENCE { 
        jnxoptIfOChSinkCurrentSuspectedFlag            TruthValue, 
        jnxoptIfOChSinkCurrentInputPower               Integer32, 
        jnxoptIfOChSinkCurrentLowInputPower            Integer32, 
        jnxoptIfOChSinkCurrentHighInputPower           Integer32, 
        jnxoptIfOChSinkCurrentLowerInputPowerThreshold Integer32, 
        jnxoptIfOChSinkCurrentUpperInputPowerThreshold Integer32 
    } 


jnxoptIfOChSinkCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSinkCurrentEntry 1 } 


jnxoptIfOChSinkCurrentInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the input." 
    ::= { jnxoptIfOChSinkCurrentEntry 2 } 


jnxoptIfOChSinkCurrentLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      current 15-minute interval." 
    ::= { jnxoptIfOChSinkCurrentEntry 3 } 


jnxoptIfOChSinkCurrentHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      current 15-minute interval." 
    ::= { jnxoptIfOChSinkCurrentEntry 4 } 


jnxoptIfOChSinkCurrentLowerInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on input power. If 
      jnxoptIfOChSinkCurrentInputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChSinkCurrentEntry 5 } 


jnxoptIfOChSinkCurrentUpperInputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on input power. If 
      jnxoptIfOChSinkCurrentInputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChSinkCurrentEntry 6 } 


-- OCh sink interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOChSinkIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSinkIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of historical OCh sink performance monitoring 
      information." 
    ::= { jnxoptIfOCh 3 } 


jnxoptIfOChSinkIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSinkIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh sink performance 
      monitoring information of an interface during a particular 
      historical interval."
    INDEX { ifIndex, jnxoptIfOChSinkIntervalNumber } 
    ::= { jnxoptIfOChSinkIntervalTable 1 } 


JnxoptIfOChSinkIntervalEntry ::=
    SEQUENCE { 
        jnxoptIfOChSinkIntervalNumber          JnxoptIfIntervalNumber,
        jnxoptIfOChSinkIntervalSuspectedFlag   TruthValue,
        jnxoptIfOChSinkIntervalLastInputPower  Integer32,
        jnxoptIfOChSinkIntervalLowInputPower   Integer32,
        jnxoptIfOChSinkIntervalHighInputPower  Integer32
    } 


jnxoptIfOChSinkIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOChSinkIntervalEntry 1 } 
  

jnxoptIfOChSinkIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSinkIntervalEntry 2 } 
 

jnxoptIfOChSinkIntervalLastInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOChSinkIntervalEntry 3 } 


jnxoptIfOChSinkIntervalLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOChSinkIntervalEntry 4 } 


jnxoptIfOChSinkIntervalHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      interval." 
    ::= { jnxoptIfOChSinkIntervalEntry 5 } 


-- OCh sink current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOChSinkCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OCh sink performance monitoring information for 
      the current 24-hour interval." 
    ::= { jnxoptIfOCh 4 } 


jnxoptIfOChSinkCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSinkCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh sink performance 
      monitoring information of an interface for the current 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChSinkCurDayTable 1 } 


JnxoptIfOChSinkCurDayEntry ::=
    SEQUENCE { 
        jnxoptIfOChSinkCurDaySuspectedFlag   TruthValue,
        jnxoptIfOChSinkCurDayLowInputPower   Integer32,
        jnxoptIfOChSinkCurDayHighInputPower  Integer32 
    } 


jnxoptIfOChSinkCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSinkCurDayEntry 1 } 


jnxoptIfOChSinkCurDayLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      current 24-hour interval." 
    ::= { jnxoptIfOChSinkCurDayEntry 2 } 


jnxoptIfOChSinkCurDayHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
    "The highest optical power monitored at the input during the 
    current 24-hour interval." 
    ::= { jnxoptIfOChSinkCurDayEntry 3 } 



-- OCh sink previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOChSinkPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OCh sink performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOCh 5 } 


jnxoptIfOChSinkPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSinkPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh sink performance 
      monitoring information of an interface for the previous 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChSinkPrevDayTable 1 } 


JnxoptIfOChSinkPrevDayEntry ::=
    SEQUENCE { 
        jnxoptIfOChSinkPrevDaySuspectedFlag   TruthValue,
        jnxoptIfOChSinkPrevDayLastInputPower  Integer32,
        jnxoptIfOChSinkPrevDayLowInputPower   Integer32,
        jnxoptIfOChSinkPrevDayHighInputPower  Integer32 
    }   


jnxoptIfOChSinkPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSinkPrevDayEntry 1 } 


jnxoptIfOChSinkPrevDayLastInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChSinkPrevDayEntry 2 } 


jnxoptIfOChSinkPrevDayLowInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChSinkPrevDayEntry 3 } 


jnxoptIfOChSinkPrevDayHighInputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the input during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChSinkPrevDayEntry 4 } 


-- OCh source current table 
-- Contains data for the current 15-minute performance monitoring 
-- interval. 


jnxoptIfOChSrcCurrentTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OCh source performance monitoring information for 
      the current 15-minute interval." 
    ::= { jnxoptIfOCh 6 } 


jnxoptIfOChSrcCurrentEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSrcCurrentEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh source performance 
      monitoring information of an interface for the current 
      15-minute interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChSrcCurrentTable 1 } 


JnxoptIfOChSrcCurrentEntry ::= 
    SEQUENCE {    
        jnxoptIfOChSrcCurrentSuspectedFlag              TruthValue,
        jnxoptIfOChSrcCurrentOutputPower                Integer32,
        jnxoptIfOChSrcCurrentLowOutputPower             Integer32,
        jnxoptIfOChSrcCurrentHighOutputPower            Integer32,
        jnxoptIfOChSrcCurrentLowerOutputPowerThreshold  Integer32,
        jnxoptIfOChSrcCurrentUpperOutputPowerThreshold  Integer32 
    } 


jnxoptIfOChSrcCurrentSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSrcCurrentEntry 1 } 


jnxoptIfOChSrcCurrentOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The optical power monitored at the output." 
    ::= { jnxoptIfOChSrcCurrentEntry 2 } 


jnxoptIfOChSrcCurrentLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      current 15-minute interval." 
   ::= { jnxoptIfOChSrcCurrentEntry 3 } 


jnxoptIfOChSrcCurrentHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 15-minute interval." 
    ::= { jnxoptIfOChSrcCurrentEntry 4 } 


jnxoptIfOChSrcCurrentLowerOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The lower limit threshold on output power. If 
      jnxoptIfOChSrcCurrentOutputPower drops to this value or below, 
      a Threshold Crossing Alert (TCA) should be sent."
    ::= { jnxoptIfOChSrcCurrentEntry 5 } 


jnxoptIfOChSrcCurrentUpperOutputPowerThreshold OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The upper limit threshold on output power. If 
      jnxoptIfOChSrcCurrentOutputPower reaches or exceeds this value, 
      a Threshold Crossing Alert (TCA) should be sent."
      ::= { jnxoptIfOChSrcCurrentEntry 6 } 


-- OCh source interval table 
-- Contains data for previous 15-minute performance monitoring 
-- intervals. 


jnxoptIfOChSrcIntervalTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of historical OCh source performance monitoring 
      information." 
    ::= { jnxoptIfOCh 7 } 


jnxoptIfOChSrcIntervalEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSrcIntervalEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh source performance 
      monitoring information of an interface during a particular 
      historical interval."
    INDEX { ifIndex, jnxoptIfOChSrcIntervalNumber } 
    ::= { jnxoptIfOChSrcIntervalTable 1 } 


JnxoptIfOChSrcIntervalEntry ::=
    SEQUENCE { 
        jnxoptIfOChSrcIntervalNumber           JnxoptIfIntervalNumber,
        jnxoptIfOChSrcIntervalSuspectedFlag    TruthValue,
        jnxoptIfOChSrcIntervalLastOutputPower  Integer32,
        jnxoptIfOChSrcIntervalLowOutputPower   Integer32,
        jnxoptIfOChSrcIntervalHighOutputPower  Integer32
    } 


jnxoptIfOChSrcIntervalNumber OBJECT-TYPE 
    SYNTAX JnxoptIfIntervalNumber 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Uniquely identifies the interval." 
    ::= { jnxoptIfOChSrcIntervalEntry 1 } 


jnxoptIfOChSrcIntervalSuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSrcIntervalEntry 2 } 


jnxoptIfOChSrcIntervalLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOChSrcIntervalEntry 3 } 


jnxoptIfOChSrcIntervalLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOChSrcIntervalEntry 4 } 


jnxoptIfOChSrcIntervalHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      interval." 
    ::= { jnxoptIfOChSrcIntervalEntry 5 } 
 

-- OCh source current day table 
-- Contains data for the current 24-hour performance 
-- monitoring interval. 


jnxoptIfOChSrcCurDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OCh source performance monitoring information for 
      the current 24-hour interval." 
      ::= { jnxoptIfOCh 8 } 


jnxoptIfOChSrcCurDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSrcCurDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh source performance 
      monitoring information of an interface for the current 
      24-hour interval."
     INDEX { ifIndex } 
   ::= { jnxoptIfOChSrcCurDayTable 1 } 
 

JnxoptIfOChSrcCurDayEntry ::=
    SEQUENCE { 
        jnxoptIfOChSrcCurDaySuspectedFlag   TruthValue, 
        jnxoptIfOChSrcCurDayLowOutputPower  Integer32, 
        jnxoptIfOChSrcCurDayHighOutputPower Integer32 
    } 


jnxoptIfOChSrcCurDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSrcCurDayEntry 1 } 
 

jnxoptIfOChSrcCurDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOChSrcCurDayEntry 2 } 


jnxoptIfOChSrcCurDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
      current 24-hour interval." 
    ::= { jnxoptIfOChSrcCurDayEntry 3 } 


-- OCh source previous day table 
-- Contains data for the previous 24-hour performance 
-- monitoring interval. 


jnxoptIfOChSrcPrevDayTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOChSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OCh source performance monitoring information for 
      the previous 24-hour interval." 
    ::= { jnxoptIfOCh 9 } 


jnxoptIfOChSrcPrevDayEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOChSrcPrevDayEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OCh source performance 
      monitoring information of an interface for the previous 
      24-hour interval."
    INDEX { ifIndex } 
    ::= { jnxoptIfOChSrcPrevDayTable 1 } 


JnxoptIfOChSrcPrevDayEntry ::=
    SEQUENCE { 
        jnxoptIfOChSrcPrevDaySuspectedFlag    TruthValue,
        jnxoptIfOChSrcPrevDayLastOutputPower  Integer32,
        jnxoptIfOChSrcPrevDayLowOutputPower   Integer32,
        jnxoptIfOChSrcPrevDayHighOutputPower  Integer32
    } 


jnxoptIfOChSrcPrevDaySuspectedFlag OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "If true, the data in this entry may be unreliable." 
    ::= { jnxoptIfOChSrcPrevDayEntry 1 } 


jnxoptIfOChSrcPrevDayLastOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The last optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChSrcPrevDayEntry 2 } 


jnxoptIfOChSrcPrevDayLowOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The lowest optical power monitored at the output during the 
      previous 24-hour interval." 
    ::= { jnxoptIfOChSrcPrevDayEntry 3 } 


jnxoptIfOChSrcPrevDayHighOutputPower OBJECT-TYPE 
    SYNTAX Integer32 
    UNITS "0.1 dbm" 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The highest optical power monitored at the output during the 
       previous 24-hour interval." 
    ::= { jnxoptIfOChSrcPrevDayEntry 4 } 
  

-- the jnxoptIfOTUk group 
-- This group handles the configuration 
-- information for OTUk layers. 


-- OTUk config table 


jnxoptIfOTUkConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfOTUkConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of OTUk configuration information." 
    ::= { jnxoptIfOTUk 1 } 



jnxoptIfOTUkConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfOTUkConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains OTUk configuration
      information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfOTUkConfigTable 1 } 


JnxoptIfOTUkConfigEntry ::=
    SEQUENCE { 
        jnxoptIfOTUkDirectionality              JnxoptIfDirectionality,
        jnxoptIfOTUkBitRateK                    JnxoptIfBitRateK,
        jnxoptIfOTUkTraceIdentifierTransmitted  JnxoptIfTxTI,
        jnxoptIfOTUkDAPIExpected                JnxoptIfExDAPI,
        jnxoptIfOTUkSAPIExpected                JnxoptIfExSAPI,
        jnxoptIfOTUkTraceIdentifierAccepted     JnxoptIfAcTI,
        jnxoptIfOTUkTIMDetMode                  JnxoptIfTIMDetMode,
        jnxoptIfOTUkTIMActEnabled               TruthValue,
        jnxoptIfOTUkDEGThr                      JnxoptIfDEGThr,
        jnxoptIfOTUkDEGM                        JnxoptIfDEGM,
        jnxoptIfOTUkSinkAdaptActive             TruthValue,
        jnxoptIfOTUkSourceAdaptActive           TruthValue,
        jnxoptIfOTUkSinkFECEnabled              TruthValue,
        jnxoptIfOTUkCurrentStatus               BITS
    } 


jnxoptIfOTUkDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfDirectionality 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the directionality of the entity." 
    ::= { jnxoptIfOTUkConfigEntry 1 } 


jnxoptIfOTUkBitRateK OBJECT-TYPE 
    SYNTAX JnxoptIfBitRateK 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "Indicates the bit rate of the entity." 
    ::= { jnxoptIfOTUkConfigEntry 2 } 


jnxoptIfOTUkTraceIdentifierTransmitted OBJECT-TYPE 
    SYNTAX JnxoptIfTxTI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The trace identifier transmitted. 
      This object is applicable when jnxoptIfOTUkDirectionality 
      has the value source(2) or bidirectional(3). It must not 
      be instantiated in rows where jnxoptIfOTUkDirectionality 
      has the value sink(1). 
      If no value is ever set by a management entity for this 
      object, system-specific default value will be used. 
      Any implementation that instantiates this object must 
      document the system-specific default value or how it 
      is derived."
    ::= { jnxoptIfOTUkConfigEntry 3 } 


jnxoptIfOTUkDAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExDAPI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The DAPI expected by the receiver. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      This object has no effect when jnxoptIfOTUkTIMDetMode has 
      the value off(1)."
    ::= { jnxoptIfOTUkConfigEntry 4 } 


jnxoptIfOTUkSAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExSAPI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The SAPI expected by the receiver. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      This object has no effect when jnxoptIfOTUkTIMDetMode has 
      the value off(1)."
    ::= { jnxoptIfOTUkConfigEntry 5 } 


jnxoptIfOTUkTraceIdentifierAccepted OBJECT-TYPE 
    SYNTAX JnxoptIfAcTI 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The actual trace identifier accepted. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      The value of this object is unspecified when 
      jnxoptIfOTUkCurrentStatus indicates a near-end defect 
      (i.e., ssf(3), lof(4), ais(5), lom(6)) that prevents 
      extraction of the trace message."
    ::= { jnxoptIfOTUkConfigEntry 6 } 


jnxoptIfOTUkTIMDetMode OBJECT-TYPE 
    SYNTAX JnxoptIfTIMDetMode 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION 
      "Indicates the mode of the Trace Identifier Mismatch (TIM) 
      Detection function. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      The default value of this object is off(1)." 
    ::= { jnxoptIfOTUkConfigEntry 7 } 


jnxoptIfOTUkTIMActEnabled OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION 
      "Indicates whether the Trace Identifier Mismatch (TIM) 
      Consequent Action function is enabled. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      This object has no effect when jnxoptIfOTUkTIMDetMode has 
      the value off(1). 
      The default value of this object is false(2)." 
    ::= { jnxoptIfOTUkConfigEntry 8 } 


jnxoptIfOTUkDEGThr OBJECT-TYPE 
    SYNTAX JnxoptIfDEGThr 
    UNITS "percentage" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION 
      "Indicates the threshold level for declaring a performance 
      monitoring (PM) Second to be bad. A PM Second is declared bad if 
      the percentage of detected errored blocks in that second is 
      greater than or equal to jnxoptIfOTUkDEGThr. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      The default value of this object is Severely Errored Second 
      (SES) Estimator (See ITU-T G.7710)."
    ::= { jnxoptIfOTUkConfigEntry 9 } 


jnxoptIfOTUkDEGM OBJECT-TYPE 
    SYNTAX JnxoptIfDEGM 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates the threshold level for declaring a Degraded Signal 
      defect (dDEG). A dDEG shall be declared if jnxoptIfOTUkDEGM 
      consecutive bad PM Seconds are detected. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      The default value of this object is 7 (See ITU-T G.7710)."
    ::= { jnxoptIfOTUkConfigEntry 10 } 


jnxoptIfOTUkSinkAdaptActive OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates whether the sink adaptation function is activated or 
      not. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      The default value of this object is false(2)."
    ::= { jnxoptIfOTUkConfigEntry 11 } 


jnxoptIfOTUkSourceAdaptActive OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates whether the source adaptation function is activated or 
      not. 
      This object is only applicable to the source function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value source(2) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value sink(1). 
      The default value of this object is false(2)." 
    ::= { jnxoptIfOTUkConfigEntry 12 } 


jnxoptIfOTUkSinkFECEnabled OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION 
      "If Forward Error Correction (FEC) is supported, this object 
      indicates whether FEC at the OTUk sink adaptation function is 
      enabled or not. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2). 
      The default value of this object is true(1)." 
    ::= { jnxoptIfOTUkConfigEntry 13 } 


jnxoptIfOTUkCurrentStatus OBJECT-TYPE 
    SYNTAX BITS { 
        tim(0), 
        deg(1), 
        bdi(2), 
        ssf(3), 
        lof(4), 
        ais(5), 
        lom(6) 
    } 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "Indicates the defect condition of the entity, if any. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfOTUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfOTUkDirectionality has the value source(2)." 
    ::= { jnxoptIfOTUkConfigEntry 14 } 


-- GCC0 config table 


jnxoptIfGCC0ConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfGCC0ConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of GCC0 configuration information." 
    ::= { jnxoptIfOTUk 2 } 


jnxoptIfGCC0ConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfGCC0ConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains GCC0 configuration 
      information of an interface. Each instance must 
      correspond to an instance of jnxoptIfOTUkConfigEntry. 
      Separate source and/or sink instances may exist 
      for a given ifIndex value, or a single bidirectional 
      instance may exist, but a bidirectional instance may 
      not coexist with a source or sink instance. 
      Instances of this conceptual row persist across 
      agent restarts."
    INDEX { ifIndex, jnxoptIfGCC0Directionality } 
    ::= { jnxoptIfGCC0ConfigTable 1 } 


JnxoptIfGCC0ConfigEntry ::=
    SEQUENCE { 
        jnxoptIfGCC0Directionality JnxoptIfDirectionality, 
        jnxoptIfGCC0Application    SnmpAdminString, 
        jnxoptIfGCC0RowStatus      RowStatus 
    } 


jnxoptIfGCC0Directionality OBJECT-TYPE 
    SYNTAX JnxoptIfDirectionality 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Indicates the directionality of the entity. 
      The values source(2) and bidirectional(3) are 
      not allowed if the corresponding instance of 
      jnxoptIfOTUkDirectionality has the value sink(1). 
      The values sink(1) and bidirectional(3) are 
      not allowed if the corresponding instance of 
      jnxoptIfOTUkDirectionality has the value source(2)."
    ::= { jnxoptIfGCC0ConfigEntry 1 } 


jnxoptIfGCC0Application OBJECT-TYPE 
    SYNTAX SnmpAdminString 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "Indicates the application transported by the GCC0 entity. 
      Example applications are ECC, User data channel.
      The value of this object may not be changed when 
      jnxoptIfGCC0RowStatus has the value active(1)." 
    ::= { jnxoptIfGCC0ConfigEntry 2 } 


jnxoptIfGCC0RowStatus OBJECT-TYPE 
    SYNTAX RowStatus 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "This columnar object is used for creating and deleting a 
      conceptual row of the jnxoptIfGCC0 config table. 
      It is used to model the addGCC0Access and removeGCC0Access 
      operations of an OTUk_TTP for GCC0 access control as defined 
      in G.874.1. Setting RowStatus to createAndGo or createAndWait 
      implies addGCC0Access. Setting RowStatus to destroy implies 
      removeGCC0Access."
    ::= { jnxoptIfGCC0ConfigEntry 3 } 


-- the jnxoptIfODUk group 
-- This group handles the configuration information 
-- for the ODUk layers. 


-- ODUk config table 


jnxoptIfODUkConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfODUkConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of ODUk configuration information." 
    ::= { jnxoptIfODUk 1 } 


jnxoptIfODUkConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfODUkConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains ODUk configuration
      information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfODUkConfigTable 1 } 


JnxoptIfODUkConfigEntry ::=
    SEQUENCE { 
       jnxoptIfODUkDirectionality              JnxoptIfDirectionality,
       jnxoptIfODUkBitRateK                    JnxoptIfBitRateK,
       jnxoptIfODUkTcmFieldsInUse              BITS,
       jnxoptIfODUkPositionSeqCurrentSize      Unsigned32,
       jnxoptIfODUkTtpPresent                  TruthValue 
    } 


jnxoptIfODUkDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfDirectionality 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "Indicates the directionality of the entity." 
    ::= { jnxoptIfODUkConfigEntry 1 } 
 

jnxoptIfODUkBitRateK OBJECT-TYPE 
    SYNTAX JnxoptIfBitRateK 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "Indicates the bit rate of the entity." 
    ::= { jnxoptIfODUkConfigEntry 2 } 


jnxoptIfODUkTcmFieldsInUse OBJECT-TYPE 
    SYNTAX BITS { 
        tcmField1(0),
        tcmField2(1),
        tcmField3(2),
        tcmField4(3),
        tcmField5(4),
        tcmField6(5)
    } 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "Indicates the TCM field(s) that are currently in use. 
      The positions of the bits correspond to the TCM fields. 
      A bit that is set to 1 means that the corresponding TCM 
      field is used. This object will be updated when rows are 
      created in or deleted from the jnxoptIfODUkTConfigTable, or 
      the jnxoptIfODUkTNimConfigTable." 
    ::= { jnxoptIfODUkConfigEntry 3 } 


jnxoptIfODUkPositionSeqCurrentSize OBJECT-TYPE 
    SYNTAX Unsigned32 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "This variable indicates the current size of the position 
      sequence (i.e., number of TCM function and/or GCC12 
      access that have been created in the ODUk interface). 
      When the value of this variable is greater than zero, 
      it means that one or more TCM function and/or GCC12 
      access have been created in the ODUk interface. In this 
      case, there will be as many rows in the 
      jnxoptIfODUkPositionSeqTable as the value of 
      jnxoptIfODUkPositionSeqCurrentSize corresponding to this 
      ODUk interface, one row for each TCM function or GCC12 
      access. The position of the TCM function and/or 
      GCC12 access within the sequence is indicated by the 
      jnxoptIfODUkPositionSeqPosition variable in 
      jnxoptIfODUkPositionSeqTable. 
      The jnxoptIfODUkPositionSeqTable also provides pointers 
      to the corresponding TCM function (jnxoptIfODUkT) and 
      GCC12 access (jnxoptIfGCC12) entities."
    ::= { jnxoptIfODUkConfigEntry 4 } 


jnxoptIfODUkTtpPresent OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "This object has the value true(1) if the ifEntry under which 
      it is instantiated contains an ODUk Trail Termination Point, 
      i.e., is the endpoint of an ODUk path. In that case there 
      will be a corresponding row in the ODUk TTP config table and 
      it will not be possible to create corresponding rows in the 
      ODUk NIM config table. This object has the value false(2) 
      if the ifEntry under which it is instantiated contains an 
      intermediate ODUk Connection Termination Point. In that case 
      there is no corresponding row in the ODUk TTP config table, 
      but it will be possible to create corresponding rows in the 
      ODUk NIM config table. This object also affects the allowable 
      options in rows created in the GCC12 config table and in the 
      ODUkT config table, as specified in the DESCRIPTION clauses 
      of the columns in those tables." 
    ::= { jnxoptIfODUkConfigEntry 5 } 


-- ODUk Trail Termination Point (TTP) config table 


jnxoptIfODUkTtpConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfODUkTtpConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of ODUk TTP configuration information." 
    ::= { jnxoptIfODUk 2 } 
 

jnxoptIfODUkTtpConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfODUkTtpConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A conceptual row that contains ODUk TTP configuration
      information of an interface." 
    INDEX { ifIndex } 
    ::= { jnxoptIfODUkTtpConfigTable 1 } 


JnxoptIfODUkTtpConfigEntry ::=
    SEQUENCE { 
        jnxoptIfODUkTtpTraceIdentifierTransmitted  JnxoptIfTxTI,
        jnxoptIfODUkTtpDAPIExpected                JnxoptIfExDAPI,
        jnxoptIfODUkTtpSAPIExpected                JnxoptIfExSAPI,
        jnxoptIfODUkTtpTraceIdentifierAccepted     JnxoptIfAcTI,
        jnxoptIfODUkTtpTIMDetMode                  JnxoptIfTIMDetMode,
        jnxoptIfODUkTtpTIMActEnabled               TruthValue,
        jnxoptIfODUkTtpDEGThr                      JnxoptIfDEGThr,
        jnxoptIfODUkTtpDEGM                        JnxoptIfDEGM,
        jnxoptIfODUkTtpCurrentStatus               BITS
    } 
 

jnxoptIfODUkTtpTraceIdentifierTransmitted OBJECT-TYPE 
    SYNTAX JnxoptIfTxTI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The trace identifier transmitted. 
      This object is applicable when jnxoptIfODUkDirectionality 
      has the value source(2) or bidirectional(3). It must not 
      be instantiated in rows where jnxoptIfODUkDirectionality 
      has the value sink(1). 
      If no value is ever set by a management entity for this 
      object, system-specific default value will be used. 
      Any implementation that instantiates this object must 
      document the system-specific default value or how it 
      is derived."
   ::= { jnxoptIfODUkTtpConfigEntry 1 } 


jnxoptIfODUkTtpDAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExDAPI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The DAPI expected by the receiver. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2). 
      This object has no effect when jnxoptIfODUkTtpTIMDetMode has 
      the value off(1)."
    ::= { jnxoptIfODUkTtpConfigEntry 2 } 



jnxoptIfODUkTtpSAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExSAPI 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "The SAPI expected by the receiver. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2). 
      This object has no effect when jnxoptIfODUkTtpTIMDetMode has 
      the value off(1)."
    ::= { jnxoptIfODUkTtpConfigEntry 3 } 


jnxoptIfODUkTtpTraceIdentifierAccepted OBJECT-TYPE 
    SYNTAX JnxoptIfAcTI 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The actual trace identifier accepted. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2). 
      The value of this object is unspecified when 
      jnxoptIfODUkTtpCurrentStatus indicates a near-end defect 
      (i.e., oci(0), lck(1), ssf(5)) that prevents extraction 
      of the trace message."
    ::= { jnxoptIfODUkTtpConfigEntry 4 } 


jnxoptIfODUkTtpTIMDetMode OBJECT-TYPE 
    SYNTAX JnxoptIfTIMDetMode 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates the mode of the Trace Identifier Mismatch (TIM) 
      Detection function. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2). 
      The default value of this object is off(1)."
    ::= { jnxoptIfODUkTtpConfigEntry 5 } 


jnxoptIfODUkTtpTIMActEnabled OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates whether the Trace Identifier Mismatch (TIM) 
      Consequent Action function is enabled. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2). 
      This object has no effect when jnxoptIfODUkTtpTIMDetMode has 
      the value off(1). 
      The default value of this object is false(2)."
    ::= { jnxoptIfODUkTtpConfigEntry 6 } 


jnxoptIfODUkTtpDEGThr OBJECT-TYPE 
    SYNTAX JnxoptIfDEGThr 
    UNITS "percentage" 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates the threshold level for declaring a performance 
      monitoring (PM) Second to be bad. A PM Second is declared bad if 
      the percentage of detected errored blocks in that second is 
      greater than or equal to jnxoptIfODUkDEGThr. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2). 
      The default value of this object is Severely Errored Second 
      (SES) Estimator (See ITU-T G.7710)."
    ::= { jnxoptIfODUkTtpConfigEntry 7 } 


jnxoptIfODUkTtpDEGM OBJECT-TYPE 
    SYNTAX JnxoptIfDEGM 
    MAX-ACCESS read-write 
    STATUS current 
    DESCRIPTION
      "Indicates the threshold level for declaring a Degraded Signal 
      defect (dDEG). A dDEG shall be declared if jnxoptIfODUkDEGM 
      consecutive bad PM Seconds are detected. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2). 
      The default value of this object is 7 (See ITU-T G.7710)."
    ::= { jnxoptIfODUkTtpConfigEntry 8 } 


jnxoptIfODUkTtpCurrentStatus OBJECT-TYPE 
    SYNTAX BITS { 
        oci(0),
        lck(1),
        tim(2),
        deg(3),
        bdi(4),
        ssf(5)
    }
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the defect condition of the entity, if any. 
      This object is only applicable to the sink function, i.e., 
      only when jnxoptIfODUkDirectionality has the value sink(1) 
      or bidirectional(3). It must not be instantiated in rows 
      where jnxoptIfODUkDirectionality has the value source(2)."
    ::= { jnxoptIfODUkTtpConfigEntry 9 } 


-- ODUk Position Sequence table 


jnxoptIfODUkPositionSeqTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfODUkPositionSeqEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of ODUk Position Sequence information." 
    ::= { jnxoptIfODUk 3 } 


jnxoptIfODUkPositionSeqEntry OBJECT-TYPE 
    SYNTAX JnxoptIfODUkPositionSeqEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A conceptual row that contains ODUk position sequence 
      information of an ODUk interface. The ODUk interface 
      is identified by the ifIndex. Associated with each 
      ODUk interface there may be one of more conceptual 
      rows in the jnxoptIfODUkPositionSeqTable. Each row 
      represents a TCM or GCC12 access function within the 
      associated ODUk interface. Rows of the 
      jnxoptIfODUkPositionSeqTable table are created/deleted 
      as the result of the creation/deletion of the jnxoptIfODUkT 
      or jnxoptIfGCC12 entities." 
    INDEX { ifIndex, jnxoptIfODUkPositionSeqIndex } 
    ::= { jnxoptIfODUkPositionSeqTable 1 } 


JnxoptIfODUkPositionSeqEntry ::= 
    SEQUENCE { 
        jnxoptIfODUkPositionSeqIndex            Unsigned32,
        jnxoptIfODUkPositionSeqPosition         Unsigned32,
        jnxoptIfODUkPositionSeqPointer          RowPointer 
    } 


jnxoptIfODUkPositionSeqIndex OBJECT-TYPE 
    SYNTAX Unsigned32 (1..4294967295) 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "This variable identifies a row in the 
      jnxoptIfODUkPositionSeqTable Table. 
      Each row of the jnxoptIfODUkPositionSeqTable Table 
      represents a TCM or GCC12 access function within the 
      associated ODUk interface."
    ::= { jnxoptIfODUkPositionSeqEntry 1 } 


jnxoptIfODUkPositionSeqPosition OBJECT-TYPE 
    SYNTAX Unsigned32 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "This variable indicates the position of the TCM or 
      GCC12 access function within the sequence of TCMs & 
      GCC12 access functions of the associated ODUk 
      interface. The TCM or GCC12 presented by this row is 
      referenced by the jnxoptIfODUkPositionSeqPointer variable."
    ::= { jnxoptIfODUkPositionSeqEntry 2 } 


jnxoptIfODUkPositionSeqPointer OBJECT-TYPE 
    SYNTAX RowPointer 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "This variable identifies the TCM or GCC12 access function 
      by pointing to the corresponding jnxoptIfODUkT or jnxoptIfGCC12 
      entity."
    ::= { jnxoptIfODUkPositionSeqEntry 3 } 


-- ODUk Non-intrusive monitoring (Nim) config table 


jnxoptIfODUkNimConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfODUkNimConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of ODUkNim configuration information." 
    ::= { jnxoptIfODUk 4 } 


jnxoptIfODUkNimConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfODUkNimConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains ODUkNim configuration 
      information of an interface. Each instance must 
      correspond to an instance of jnxoptIfODUkConfigEntry 
      for which jnxoptIfODUkTtpPresent has the value false(2).
      Instances of this conceptual row persist across 
      agent restarts, and read-create columns other 
      than the status column may be modified while the 
      row is active."
    INDEX { ifIndex, jnxoptIfODUkNimDirectionality }
    ::= { jnxoptIfODUkNimConfigTable 1 } 


JnxoptIfODUkNimConfigEntry ::= 
    SEQUENCE { 
        jnxoptIfODUkNimDirectionality              JnxoptIfSinkOrSource,
        jnxoptIfODUkNimDAPIExpected                JnxoptIfExDAPI,
        jnxoptIfODUkNimSAPIExpected                JnxoptIfExSAPI,
        jnxoptIfODUkNimTraceIdentifierAccepted     JnxoptIfAcTI,
        jnxoptIfODUkNimTIMDetMode                  JnxoptIfTIMDetMode,
        jnxoptIfODUkNimTIMActEnabled               TruthValue,
        jnxoptIfODUkNimDEGThr                      JnxoptIfDEGThr,
        jnxoptIfODUkNimDEGM                        JnxoptIfDEGM,
        jnxoptIfODUkNimCurrentStatus               BITS,
        jnxoptIfODUkNimRowStatus                   RowStatus 
    } 


jnxoptIfODUkNimDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfSinkOrSource 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "Specifies the monitor point for the ODUk Path non-intrusive 
      monitoring function. The value source(2) is not allowed 
      if the corresponding instance of jnxoptIfODUkDirectionality 
      has the value sink(1), and the value sink(1) is not allowed 
      if the corresponding instance of jnxoptIfODUkDirectionality 
      has the value source(2). Either the value sink(1) or 
      source(2) is allowed if the corresponding instance of 
      jnxoptIfODUkDirectionality has the value bidirectional(3).
      The value sink(1) means monitoring at the sink direction 
      path signal of the ODUk CTP.
      The value source(2) means monitoring at the source direction 
      path signal of the ODUk CTP. Monitoring the source direction 
      of an ODUk CTP is necessary in those cases where the ODUk CTP 
      is at an SNCP (Subnetwork Connection Protection) end (e.g., see 
      Figure I.1.2/G.874.1). If one would like to get the performance 
      of the protected connection, one cannot use the NIM function 
      at both ODUk CTP sinks (before the matrix), instead one should 
      monitor the signal at the source ODUk CTP after the matrix."
    ::= { jnxoptIfODUkNimConfigEntry 1 } 


jnxoptIfODUkNimDAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExDAPI 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "The DAPI expected by the receiver. 
      This object has no effect if jnxoptIfODUkNimTIMDetMode has 
      the value off(1) or sapi(3)."
    ::= { jnxoptIfODUkNimConfigEntry 2 } 


jnxoptIfODUkNimSAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExSAPI 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "The SAPI expected by the receiver. 
      This object has no effect if jnxoptIfODUkNimTIMDetMode has 
      the value off(1) or dapi(2)."
    ::= { jnxoptIfODUkNimConfigEntry 3 } 


jnxoptIfODUkNimTraceIdentifierAccepted OBJECT-TYPE 
    SYNTAX JnxoptIfAcTI 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The actual trace identifier accepted. The value of 
      this object is unspecified if jnxoptIfODUkNimCurrentStatus 
      has any of the bit positions oci(0), lck(1), or ssf(5) 
      set or if jnxoptIfODUkNimRowStatus has any value other 
      than active(1)."
    ::= { jnxoptIfODUkNimConfigEntry 4 } 


jnxoptIfODUkNimTIMDetMode OBJECT-TYPE 
    SYNTAX JnxoptIfTIMDetMode 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "Indicates the mode of the Trace Identifier Mismatch (TIM) 
      Detection function." 
    ::= { jnxoptIfODUkNimConfigEntry 5 } 


jnxoptIfODUkNimTIMActEnabled OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Indicates whether the Trace Identifier Mismatch (TIM) 
      Consequent Action function is enabled." 
    ::= { jnxoptIfODUkNimConfigEntry 6 } 


jnxoptIfODUkNimDEGThr OBJECT-TYPE 
    SYNTAX JnxoptIfDEGThr 
    UNITS "percentage" 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Indicates the threshold level for declaring a performance 
      monitoring (PM) Second to be bad. A PM Second is declared bad 
      if the percentage of detected errored blocks in that second is 
      greater than or equal to jnxoptIfODUkNimDEGThr." 
    ::= { jnxoptIfODUkNimConfigEntry 7 } 


jnxoptIfODUkNimDEGM OBJECT-TYPE 
    SYNTAX JnxoptIfDEGM 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Indicates the threshold level for declaring a Degraded Signal 
      defect (dDEG). A dDEG shall be declared if jnxoptIfODUkNimDEGM 
      consecutive bad PM Seconds are detected." 
    ::= { jnxoptIfODUkNimConfigEntry 8 } 


jnxoptIfODUkNimCurrentStatus OBJECT-TYPE 
    SYNTAX BITS { 
        oci(0), 
        lck(1), 
        tim(2), 
        deg(3), 
        bdi(4), 
        ssf(5) 
    } 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "Indicates the defect condition of the entity, if 
      any. The value of this object is unspecified if 
      jnxoptIfODUkNimRowStatus has any value other than 
      active(1)." 
    ::= { jnxoptIfODUkNimConfigEntry 9 } 


jnxoptIfODUkNimRowStatus OBJECT-TYPE 
    SYNTAX RowStatus 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "This columnar object is used for creating and deleting 
      a conceptual row of the jnxoptIfODUkNim config table. 
      It is used to model the activateNim and deactivateNim 
      operations of an OTUk_CTP for non-intrusive monitoring 
      control as defined in G.874.1. Setting RowStatus to 
      createAndGo or createAndWait implies activateNim. 
      Setting RowStatus to destroy implies deactivateNim."
    ::= { jnxoptIfODUkNimConfigEntry 10 } 


-- GCC12 config table 


jnxoptIfGCC12ConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfGCC12ConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of GCC12 configuration information. 
      The GCC function processes the GCC overhead bytes passing 
      through them but leave the remainder of the ODUk overhead 
      and payload data alone."
    ::= { jnxoptIfODUk 5 } 


jnxoptIfGCC12ConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfGCC12ConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains GCC12 configuration 
      information of an interface. Each instance must 
      correspond to an instance of jnxoptIfODUkConfigEntry. 
      Separate instances providing GCC1-only access and 
      GCC2-only access may exist for a given ifIndex value, 
      or a single instance providing GCC1 + GCC2 may exist, 
      but a GCC1 + GCC2 instance may not coexist with a 
      GCC1-only or GCC2-only instance.
      Instances of this conceptual row persist across agent
      restarts." 
    INDEX { ifIndex, jnxoptIfGCC12Codirectional, jnxoptIfGCC12GCCAccess } 
    ::= { jnxoptIfGCC12ConfigTable 1 } 


JnxoptIfGCC12ConfigEntry ::= 
    SEQUENCE { 
        jnxoptIfGCC12Codirectional      TruthValue,
        jnxoptIfGCC12GCCAccess          INTEGER,
        jnxoptIfGCC12GCCPassThrough     TruthValue,
        jnxoptIfGCC12Application        SnmpAdminString,
        jnxoptIfGCC12RowStatus          RowStatus
    } 


jnxoptIfGCC12Codirectional OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "Indicates the directionality of the GCC12 termination with 
      respect to the associated ODUk CTP. The value true(1) means 
      that the sink part of the GCC12 extracts COMMS data from the 
      signal at the input to the ODUk CTP sink and the source part 
      of the GCC12 inserts COMMS data into the signal at the output 
      of the ODUk CTP source. The value false(2) means that the 
      sink part of the GCC12 extracts COMMS data from the signal at 
      the output of the ODUk CTP source and the source part of the 
      GCC12 inserts COMMS data into the signal at the input of the 
      ODUk CTP sink. This attribute may assume either value when 
      the corresponding instance of jnxoptIfODUkTtpPresent has the 
      value false(2). When the value of the corresponding instance 
      of jnxoptIfODUkTtpPresent is true(1) then the only value allowed 
      for this attribute is true(1)." 
    ::= { jnxoptIfGCC12ConfigEntry 1 } 


jnxoptIfGCC12GCCAccess OBJECT-TYPE 
    SYNTAX INTEGER { 
        gcc1 (1), 
        gcc2 (2), 
        gcc1and2 (3) 
    } 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "Indicates the GCC access represented by the entity." 
    ::= { jnxoptIfGCC12ConfigEntry 2 } 


jnxoptIfGCC12GCCPassThrough OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Controls whether the selected GCC overhead bytes are passed 
      through or modified. The value true(1) means that the selected 
      GCC overhead bytes are passed through unmodified from the ODUk 
      CTP input to the ODUk CTP output. The value false(2) means that 
      the selected GCC overhead bytes are set to zero at the ODUk CTP 
      output after the extraction of the COMMS data. This object has 
      no effect if the corresponding instance of jnxoptIfODUkTtpPresent 
      has the value true(1).
      The value of this object may not be changed when 
      jnxoptIfGCC12RowStatus has the value active(1)." 
    ::= { jnxoptIfGCC12ConfigEntry 3 } 


jnxoptIfGCC12Application OBJECT-TYPE 
    SYNTAX SnmpAdminString 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Indicates the application transported by the GCC12 entity. 
      Example applications are ECC, User data channel.
      The value of this object may not be changed when 
      jnxoptIfGCC12RowStatus has the value active(1)." 
    ::= { jnxoptIfGCC12ConfigEntry 4 } 


jnxoptIfGCC12RowStatus OBJECT-TYPE 
    SYNTAX RowStatus 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "This columnar object is used for creating and deleting 
      a conceptual row of the jnxoptIfGCC12 config table. It is 
      used to model the addGCC12Access and removeGCC12Access 
      operations of an ODUk_CTP or ODUk_TTP for GCC12 access 
      control as defined in G.874.1. Setting RowStatus to 
      createAndGo or createAndWait implies addGCC12Access. 
      Setting RowStatus to destroy implies removeGCC12Access. 
      Successful addition/removal of the GCC12 access function 
      will result in updating the 
      jnxoptIfODUkPositionSeqCurrentSize variable and the 
      jnxoptIfODUkPositionSeqTable table of the associated 
      ODUk entry in the jnxoptIfODUkConfigTable." 
    ::= { jnxoptIfGCC12ConfigEntry 5 } 


-- the jnxoptIfODUkT group 
-- This group handles the configuration information 
-- for the ODUkT layers. 


-- ODUkT config table 


jnxoptIfODUkTConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfODUkTConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A table of ODUkT configuration information." 
    ::= { jnxoptIfODUkT 1 } 


jnxoptIfODUkTConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfODUkTConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "A conceptual row that contains ODUkT configuration 
      information of an interface. Each instance must 
      correspond to an instance of jnxoptIfODUkConfigEntry. 
      Rows in this table are mutually exclusive with rows 
      in the ODUkT NIM config table -- in other words, this 
      row object may not be instantiated for a given pair 
      of ifIndex and TCM field values if a corresponding 
      instance of jnxoptIfODUkTNimConfigEntry already exists.
      Instances of this conceptual row persist across agent 
      restarts. Except where noted otherwise, read-create 
      columns other than the status column may be modified 
      while the row is active."
    INDEX { ifIndex, jnxoptIfODUkTTcmField, jnxoptIfODUkTCodirectional }
    ::= { jnxoptIfODUkTConfigTable 1 } 

JnxoptIfODUkTConfigEntry ::= 
    SEQUENCE { 
        jnxoptIfODUkTTcmField                    Unsigned32,
        jnxoptIfODUkTCodirectional               TruthValue,
        jnxoptIfODUkTTraceIdentifierTransmitted  JnxoptIfTxTI,
        jnxoptIfODUkTDAPIExpected                JnxoptIfExDAPI,
        jnxoptIfODUkTSAPIExpected                JnxoptIfExSAPI,
        jnxoptIfODUkTTraceIdentifierAccepted     JnxoptIfAcTI,
        jnxoptIfODUkTTIMDetMode                  JnxoptIfTIMDetMode,
        jnxoptIfODUkTTIMActEnabled               TruthValue,
        jnxoptIfODUkTDEGThr                      JnxoptIfDEGThr,
        jnxoptIfODUkTDEGM                        JnxoptIfDEGM,
        jnxoptIfODUkTSinkMode                    INTEGER,
        jnxoptIfODUkTSinkLockSignalAdminState    INTEGER,
        jnxoptIfODUkTSourceLockSignalAdminState  INTEGER,
        jnxoptIfODUkTCurrentStatus               BITS,
        jnxoptIfODUkTRowStatus                   RowStatus 
    } 


jnxoptIfODUkTTcmField OBJECT-TYPE 
    SYNTAX Unsigned32 (1..6) 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "Indicates the tandem connection monitoring 
      field of the ODUk OH. Valid values are 
      integers from 1 to 6." 
    ::= { jnxoptIfODUkTConfigEntry 1 } 


jnxoptIfODUkTCodirectional OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "Indicates the directionality of the ODUkT termination point with 
      respect to the associated ODUk CTP. The value true(1) means 
      that the sink part of the ODUkT TP extracts TCM data from the 
      signal at the input to the ODUk CTP sink and the source part 
      of the ODUkT TP inserts TCM data into the signal at the output 
      of the ODUk CTP source. The value false(2) means that the 
      sink part of the ODUkT TP extracts TCM data from the signal at 
      the output of the ODUk CTP source and the source part of the 
      ODUkT TP inserts TCM data into the signal at the input of the 
      ODUk CTP sink. This attribute may assume either value when 
      the corresponding instance of jnxoptIfODUkTtpPresent has the 
      value false(2). When the value of the corresponding instance 
      of jnxoptIfODUkTtpPresent is true(1) then the only value allowed 
      for this attribute is true(1)." 
    ::= { jnxoptIfODUkTConfigEntry 2 } 


jnxoptIfODUkTTraceIdentifierTransmitted OBJECT-TYPE 
    SYNTAX JnxoptIfTxTI 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "The trace identifier transmitted. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value false(2), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value true(1). 
      It must not be instantiated in rows for all other cases." 
    ::= { jnxoptIfODUkTConfigEntry 3 } 


jnxoptIfODUkTDAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExDAPI 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "The DAPI expected by the receiver. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases. 
      This object has no effect when jnxoptIfODUkTTIMDetMode has 
      the value off(1)."
    ::= { jnxoptIfODUkTConfigEntry 4 } 


jnxoptIfODUkTSAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExSAPI 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "The SAPI expected by the receiver. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases. 
      This object has no effect when jnxoptIfODUkTTIMDetMode has 
      the value off(1)."
    ::= { jnxoptIfODUkTConfigEntry 5 } 


jnxoptIfODUkTTraceIdentifierAccepted OBJECT-TYPE 
    SYNTAX JnxoptIfAcTI 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "The actual trace identifier accepted. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases. 
      The value of this object is unspecified when 
      jnxoptIfODUkTCurrentStatus indicates a near-end defect 
      (i.e., oci(0), lck(1), ssf(5)) that prevents extraction 
      of the trace message." 
    ::= { jnxoptIfODUkTConfigEntry 6 } 


jnxoptIfODUkTTIMDetMode OBJECT-TYPE 
    SYNTAX JnxoptIfTIMDetMode 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "Indicates the mode of the Trace Identifier Mismatch (TIM) 
      Detection function. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases. 
      The default value of this object is off(1)."
    ::= { jnxoptIfODUkTConfigEntry 7 } 


jnxoptIfODUkTTIMActEnabled OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "Indicates whether the Trace Identifier Mismatch (TIM) 
      Consequent Action function is enabled. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases. 
      This object has no effect when jnxoptIfODUkTTIMDetMode has 
      the value off(1). 
      The default value of this object is false(2)."
    ::= { jnxoptIfODUkTConfigEntry 8 } 


jnxoptIfODUkTDEGThr OBJECT-TYPE 
    SYNTAX JnxoptIfDEGThr 
    UNITS "percentage" 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "Indicates the threshold level for declaring a performance 
      monitoring (PM) Second to be bad. A PM Second is declared bad if 
      the percentage of detected errored blocks in that second is 
      greater than or equal to jnxoptIfODUkTDEGThr. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases. 
      The default value of this object is Severely Errored Second 
      (SES) Estimator (See ITU-T G.7710)."
    ::= { jnxoptIfODUkTConfigEntry 9 } 


jnxoptIfODUkTDEGM OBJECT-TYPE 
    SYNTAX JnxoptIfDEGM 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "Indicates the threshold level for declaring a Degraded Signal 
      defect (dDEG). A dDEG shall be declared if jnxoptIfODUkTDEGM 
      consecutive bad PM Seconds are detected. 
      This object is applicable only to the following three cases.
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases. 
      The default value of this object is 7 (See ITU-T G.7710)."
    ::= { jnxoptIfODUkTConfigEntry 10 } 


jnxoptIfODUkTSinkMode OBJECT-TYPE
    SYNTAX INTEGER { 
        operational (1), 
        monitor (2) 
    }
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "This variable specifies the TCM mode at the entity. 
      The value operational(1) means that TCM Overhead (TCMOH) 
      processes (see ITU-T G.798) shall be 
      performed and consequent actions for AIS, Trail 
      Signal Fail (TSF), Trail Signal Degraded (TSD) shall be 
      initiated in case of defects. 
      The value monitor(2) means that TCMOH processes shall be 
      performed but consequent actions for AIS, Trail 
      Server Failure (TSF), Trail Server Degraded (TSD) shall _not_ be 
      initiated in case of defects. 
      This object is applicable only when the value of 
      jnxoptIfODUkTtpPresent is false(2) and also either one of the 
      following three cases holds:
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases." 
    ::= { jnxoptIfODUkTConfigEntry 11 } 


jnxoptIfODUkTSinkLockSignalAdminState OBJECT-TYPE 
    SYNTAX INTEGER { 
        locked(1), 
        normal(2) 
    } 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Provides the capability to provision the LOCK signal, which 
      is one of the ODUk maintenance signals, at the ODUKT sink. When 
      a Tandem Connection endpoint is set to admin state locked, 
      it inserts the ODUk-LCK signal in the sink direction.
      This object is applicable only when the value of 
      jnxoptIfODUkTtpPresent is false(2) and also either one of the 
      following three cases holds:
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases." 
    ::= { jnxoptIfODUkTConfigEntry 12 } 


jnxoptIfODUkTSourceLockSignalAdminState OBJECT-TYPE 
    SYNTAX INTEGER { 
        locked(1), 
        normal(2) 
    } 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Provides the capability to provision the LOCK signal, which 
      is one of the ODUk maintenance signals, at the source. 
      When a Tandem Connection endpoint is set to admin state 
      locked, it inserts the ODUk-LCK signal in the source 
      direction. 
      This object is applicable only when either one of the 
      following three cases holds:
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value false(2), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value true(1). 
      It must not be instantiated in rows for all other cases." 
    ::= { jnxoptIfODUkTConfigEntry 13 } 


jnxoptIfODUkTCurrentStatus OBJECT-TYPE
    SYNTAX BITS { 
        oci(0), 
        lck(1), 
        tim(2), 
        deg(3), 
        bdi(4), 
        ssf(5) 
    }
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION
      "Indicates the defect condition of the entity, if any. 
      This object is applicable only when either one of the 
      following three cases holds:
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It must not be instantiated in rows for all other cases." 
    ::= { jnxoptIfODUkTConfigEntry 14 } 


jnxoptIfODUkTRowStatus OBJECT-TYPE 
    SYNTAX RowStatus 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "This columnar object is used for creating and deleting a 
      conceptual row of the jnxoptIfODUkT config table. 
      It is used to model the addTCM and removeTCM operations of an 
      ODUk_CTP or ODUk_TTP for Tandem connection monitoring as defined 
      in ITU-T G.874.1. 
      Setting RowStatus to createAndGo or createAndWait implies addTCM. 
      Setting RowStatus to destroy implies removeTCM. 
      Successful addition/removal of TCM will result in updating the 
      jnxoptIfODUkTcmFieldsInUse and jnxoptIfODUkPositionSeqCurrentSize 
      variables and the jnxoptIfODUkPositionSeqTable table of the 
      associated ODUk entry in the jnxoptIfODUkConfigTable." 
    ::= { jnxoptIfODUkTConfigEntry 15 } 


-- ODUkT Non-intrusive monitoring (Nim) config table 


jnxoptIfODUkTNimConfigTable OBJECT-TYPE 
    SYNTAX SEQUENCE OF JnxoptIfODUkTNimConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A table of ODUkTNim configuration information." 
    ::= { jnxoptIfODUkT 2 } 


jnxoptIfODUkTNimConfigEntry OBJECT-TYPE 
    SYNTAX JnxoptIfODUkTNimConfigEntry 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION 
      "A conceptual row that contains ODUkTNim configuration 
      information of an interface. Each instance must 
      correspond to an instance of jnxoptIfODUkConfigEntry. 
      Rows in this table are mutually exclusive with rows 
      in the ODUkT config table -- in other words, this 
      row object may not be instantiated for a given pair 
      of ifIndex and TCM field values if a corresponding 
      instance of jnxoptIfODUkTConfigEntry already exists.
      Instances of this conceptual row persist across 
      agent restarts, and read-create columns other 
      than the status column may be modified while the 
      row is active."
    INDEX {ifIndex, jnxoptIfODUkTNimTcmField, jnxoptIfODUkTNimDirectionality}
    ::= { jnxoptIfODUkTNimConfigTable 1 } 

JnxoptIfODUkTNimConfigEntry ::= 
    SEQUENCE { 
        jnxoptIfODUkTNimTcmField                    Unsigned32,
        jnxoptIfODUkTNimDirectionality              JnxoptIfSinkOrSource,
        jnxoptIfODUkTNimDAPIExpected                JnxoptIfExDAPI,
        jnxoptIfODUkTNimSAPIExpected                JnxoptIfExSAPI,
        jnxoptIfODUkTNimTraceIdentifierAccepted     JnxoptIfAcTI,
        jnxoptIfODUkTNimTIMDetMode                  JnxoptIfTIMDetMode,
        jnxoptIfODUkTNimTIMActEnabled               TruthValue,
        jnxoptIfODUkTNimDEGThr                      JnxoptIfDEGThr,
        jnxoptIfODUkTNimDEGM                        JnxoptIfDEGM,
        jnxoptIfODUkTNimCurrentStatus               BITS,
        jnxoptIfODUkTNimRowStatus                   RowStatus
    } 


jnxoptIfODUkTNimTcmField OBJECT-TYPE 
    SYNTAX Unsigned32 (1..6) 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Indicates the tandem connection monitoring 
      field of the ODUk OH on which non-intrusive monitoring 
      is performed. Valid values are 
      integers from 1 to 6."
    ::= { jnxoptIfODUkTNimConfigEntry 1 } 


jnxoptIfODUkTNimDirectionality OBJECT-TYPE 
    SYNTAX JnxoptIfSinkOrSource 
    MAX-ACCESS not-accessible 
    STATUS current 
    DESCRIPTION
      "Specifies the monitor point for the ODUk TCM non-intrusive 
      monitoring function. The value source(2) is not allowed 
      if the corresponding instance of jnxoptIfODUkDirectionality 
      has the value sink(1), and the value sink(1) is not allowed 
      if the corresponding instance of jnxoptIfODUkDirectionality 
      has the value source(2). Either the value sink(1) or 
      source(2) is allowed if the corresponding instance of 
      jnxoptIfODUkDirectionality has the value bidirectional(3). 
      The value sink(1) means monitoring at the sink direction 
      TCM signal of the ODUk CTP. 
      The value source(2) means monitoring at the source direction 
      path signal of the ODUk CTP."
    ::= { jnxoptIfODUkTNimConfigEntry 2 } 


jnxoptIfODUkTNimDAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExDAPI 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "The DAPI expected by the receiver. 
      This object has no effect if jnxoptIfODUkTNimTIMDetMode has 
      the value off(1) or sapi(3)."
    ::= { jnxoptIfODUkTNimConfigEntry 3 } 


jnxoptIfODUkTNimSAPIExpected OBJECT-TYPE 
    SYNTAX JnxoptIfExSAPI 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "The SAPI expected by the receiver. 
      This object has no effect if jnxoptIfODUkTNimTIMDetMode has 
      the value off(1) or dapi(2)." 
    ::= { jnxoptIfODUkTNimConfigEntry 4 } 


jnxoptIfODUkTNimTraceIdentifierAccepted OBJECT-TYPE 
    SYNTAX JnxoptIfAcTI 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "The actual trace identifier accepted. The value of 
      this object is unspecified if jnxoptIfODUkTNimCurrentStatus 
      has any of the bit positions oci(0), lck(1), or ssf(5) 
      set or if jnxoptIfODUkTNimRowStatus has any value other 
      than active(1)." 
    ::= { jnxoptIfODUkTNimConfigEntry 5 } 


jnxoptIfODUkTNimTIMDetMode OBJECT-TYPE 
    SYNTAX JnxoptIfTIMDetMode 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Indicates the mode of the Trace Identifier Mismatch (TIM) 
      Detection function." 
    ::= { jnxoptIfODUkTNimConfigEntry 6 } 


jnxoptIfODUkTNimTIMActEnabled OBJECT-TYPE 
    SYNTAX TruthValue 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Indicates whether the Trace Identifier Mismatch (TIM) 
      Consequent Action function is enabled." 
    ::= { jnxoptIfODUkTNimConfigEntry 7 } 


jnxoptIfODUkTNimDEGThr OBJECT-TYPE 
    SYNTAX JnxoptIfDEGThr 
    UNITS "percentage" 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "Indicates the threshold level for declaring a performance 
      monitoring (PM) Second to be bad. A PM Second is declared bad if 
      the percentage of detected errored blocks in that second is 
      greater than or equal to jnxoptIfODUkTNimDEGThr." 
    ::= { jnxoptIfODUkTNimConfigEntry 8 } 


jnxoptIfODUkTNimDEGM OBJECT-TYPE 
    SYNTAX JnxoptIfDEGM 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION
      "Indicates the threshold level for declaring a Degraded Signal 
      defect (dDEG). A dDEG shall be declared if jnxoptIfODUkTNimDEGM 
      consecutive bad PM Seconds are detected."
    ::= { jnxoptIfODUkTNimConfigEntry 9 } 


jnxoptIfODUkTNimCurrentStatus OBJECT-TYPE 
    SYNTAX BITS { 
        oci(0), 
        lck(1), 
        tim(2), 
        deg(3), 
        bdi(4), 
        ssf(5) 
    } 
    MAX-ACCESS read-only 
    STATUS current 
    DESCRIPTION 
      "Indicates the defect condition of the entity, if any. 
      The value of this object is unspecified if 
      jnxoptIfODUkTNimRowStatus has any value other than 
      active(1)." 
    ::= { jnxoptIfODUkTNimConfigEntry 10 } 


jnxoptIfODUkTNimRowStatus OBJECT-TYPE 
    SYNTAX RowStatus 
    MAX-ACCESS read-create 
    STATUS current 
    DESCRIPTION 
      "This columnar object is used for creating and deleting a 
      conceptual row of the jnxoptIfODUkTNim config table. 
      It is used to model the addTCM and removeTCM operations of an 
      ODUk_CTP or ODUk_TTP for non-intrusive Tandem connection 
      monitoring as defined in ITU-T G.874.1. 
      Setting RowStatus to createAndGo or createAndWait implies addTCM. 
      Setting RowStatus to destroy implies removeTCM. 
      Successful addition/removal of Nim TCM will result in updating 
      the jnxoptIfODUkPositionSeqCurrentSize variable and the 
      jnxoptIfODUkPositionSeqTable table of the associated ODUk entry 
      in the jnxoptIfODUkConfigTable." 
    ::= { jnxoptIfODUkTNimConfigEntry 11 } 


-- units of conformance 


jnxoptIfOTMnGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTMnOrder,
        jnxoptIfOTMnReduced,
        jnxoptIfOTMnBitRates,
        jnxoptIfOTMnInterfaceType,
        jnxoptIfOTMnTcmMax,
         jnxoptIfOTMnOpticalReach
    }
    STATUS current 
    DESCRIPTION 
      "A collection of OTMn structure information objects." 
    ::= { jnxoptIfGroups 1 } 


jnxoptIfPerfMonGroup OBJECT-GROUP 
    OBJECTS {     
        jnxoptIfPerfMonCurrentTimeElapsed,
        jnxoptIfPerfMonCurDayTimeElapsed,
        jnxoptIfPerfMonIntervalNumIntervals,
        jnxoptIfPerfMonIntervalNumInvalidIntervals
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of performance monitoring interval objects." 
    ::= { jnxoptIfGroups 2 } 
 

jnxoptIfOTSnCommonGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnDirectionality 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all OTSn interfaces." 
    ::= { jnxoptIfGroups 3 } 


jnxoptIfOTSnSourceGroupFull OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnTraceIdentifierTransmitted 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
       applicable to full-functionality/IaDI OTSn 
       interfaces that support source functions." 
    ::= { jnxoptIfGroups 4 } 


jnxoptIfOTSnAPRStatusGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnAprStatus 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of objects applicable to 
      OTSn interfaces that support Automatic 
      Power Reduction functions." 
    ::= { jnxoptIfGroups 5 } 

jnxoptIfOTSnAPRControlGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnAprControl 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of objects applicable to 
      OTSn interfaces that provide Automatic 
      Power Reduction control functions." 
    ::= { jnxoptIfGroups 6 } 
 
jnxoptIfOTSnSinkGroupBasic OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnCurrentStatus 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all OTSn interfaces that 
      support sink functions." 
    ::= { jnxoptIfGroups 7 } 

jnxoptIfOTSnSinkGroupFull OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnDAPIExpected, 
        jnxoptIfOTSnSAPIExpected, 
        jnxoptIfOTSnTraceIdentifierAccepted, 
        jnxoptIfOTSnTIMDetMode, 
        jnxoptIfOTSnTIMActEnabled 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to full-functionality/IaDI OTSn 
      interfaces that support sink functions." 
    ::= { jnxoptIfGroups 8 } 
   
jnxoptIfOTSnSinkPreOtnPMGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnSinkCurrentSuspectedFlag,
        jnxoptIfOTSnSinkCurrentInputPower,
        jnxoptIfOTSnSinkCurrentLowInputPower,
        jnxoptIfOTSnSinkCurrentHighInputPower,
        jnxoptIfOTSnSinkCurrentOutputPower,
        jnxoptIfOTSnSinkCurrentLowOutputPower,
        jnxoptIfOTSnSinkCurrentHighOutputPower,
        jnxoptIfOTSnSinkIntervalSuspectedFlag,
        jnxoptIfOTSnSinkIntervalLastInputPower,
        jnxoptIfOTSnSinkIntervalLowInputPower,
        jnxoptIfOTSnSinkIntervalHighInputPower,
        jnxoptIfOTSnSinkIntervalLastOutputPower,
        jnxoptIfOTSnSinkIntervalLowOutputPower,
        jnxoptIfOTSnSinkIntervalHighOutputPower,
        jnxoptIfOTSnSinkCurDaySuspectedFlag,
        jnxoptIfOTSnSinkCurDayLowInputPower,
        jnxoptIfOTSnSinkCurDayHighInputPower,
        jnxoptIfOTSnSinkCurDayLowOutputPower,
        jnxoptIfOTSnSinkCurDayHighOutputPower,
        jnxoptIfOTSnSinkPrevDaySuspectedFlag,
        jnxoptIfOTSnSinkPrevDayLastInputPower,
        jnxoptIfOTSnSinkPrevDayLowInputPower,
        jnxoptIfOTSnSinkPrevDayHighInputPower,
        jnxoptIfOTSnSinkPrevDayLastOutputPower,
        jnxoptIfOTSnSinkPrevDayLowOutputPower,
        jnxoptIfOTSnSinkPrevDayHighOutputPower
     }
     STATUS current
     DESCRIPTION
       "A collection of pre-OTN performance monitoring 
       objects applicable to OTSn interfaces that 
       support sink functions."
     ::= { jnxoptIfGroups 9 } 


jnxoptIfOTSnSinkPreOtnPMThresholdGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnSinkCurrentLowerInputPowerThreshold,
        jnxoptIfOTSnSinkCurrentUpperInputPowerThreshold,
        jnxoptIfOTSnSinkCurrentLowerOutputPowerThreshold,
        jnxoptIfOTSnSinkCurrentUpperOutputPowerThreshold
    }
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OTSn interfaces 
      that support sink functions." 
    ::= { jnxoptIfGroups 10 } 


jnxoptIfOTSnSourcePreOtnPMGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnSrcCurrentSuspectedFlag,
        jnxoptIfOTSnSrcCurrentOutputPower,
        jnxoptIfOTSnSrcCurrentLowOutputPower,
        jnxoptIfOTSnSrcCurrentHighOutputPower,
        jnxoptIfOTSnSrcCurrentInputPower,
        jnxoptIfOTSnSrcCurrentLowInputPower,
        jnxoptIfOTSnSrcCurrentHighInputPower,
        jnxoptIfOTSnSrcIntervalSuspectedFlag,
        jnxoptIfOTSnSrcIntervalLastOutputPower,
        jnxoptIfOTSnSrcIntervalLowOutputPower,
        jnxoptIfOTSnSrcIntervalHighOutputPower,
        jnxoptIfOTSnSrcIntervalLastInputPower,
        jnxoptIfOTSnSrcIntervalLowInputPower,
        jnxoptIfOTSnSrcIntervalHighInputPower,
        jnxoptIfOTSnSrcCurDaySuspectedFlag,
        jnxoptIfOTSnSrcCurDayLowOutputPower,
        jnxoptIfOTSnSrcCurDayHighOutputPower,
        jnxoptIfOTSnSrcCurDayLowInputPower,
        jnxoptIfOTSnSrcCurDayHighInputPower,
        jnxoptIfOTSnSrcPrevDaySuspectedFlag,
        jnxoptIfOTSnSrcPrevDayLastOutputPower,
        jnxoptIfOTSnSrcPrevDayLowOutputPower,
        jnxoptIfOTSnSrcPrevDayHighOutputPower,
        jnxoptIfOTSnSrcPrevDayLastInputPower,
        jnxoptIfOTSnSrcPrevDayLowInputPower,
        jnxoptIfOTSnSrcPrevDayHighInputPower
    }
    STATUS current
    DESCRIPTION
      "A collection of pre-OTN performance monitoring 
      objects applicable to OTSn interfaces that 
      support source functions."
    ::= { jnxoptIfGroups 11 } 


jnxoptIfOTSnSourcePreOtnPMThresholdGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOTSnSrcCurrentLowerOutputPowerThreshold,
        jnxoptIfOTSnSrcCurrentUpperOutputPowerThreshold,
        jnxoptIfOTSnSrcCurrentLowerInputPowerThreshold,
        jnxoptIfOTSnSrcCurrentUpperInputPowerThreshold
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OTSn interfaces 
      that support source functions." 
    ::= { jnxoptIfGroups 12 } 


jnxoptIfOMSnCommonGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOMSnDirectionality 
    }
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all OMSn interfaces." 
    ::= { jnxoptIfGroups 13 } 


jnxoptIfOMSnSinkGroupBasic OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOMSnCurrentStatus 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all OMSn interfaces that 
      support sink functions." 
    ::= { jnxoptIfGroups 14 } 


jnxoptIfOMSnSinkPreOtnPMGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOMSnSinkCurrentSuspectedFlag,
        jnxoptIfOMSnSinkCurrentAggregatedInputPower,
        jnxoptIfOMSnSinkCurrentLowAggregatedInputPower,
        jnxoptIfOMSnSinkCurrentHighAggregatedInputPower,
        jnxoptIfOMSnSinkCurrentOutputPower,
        jnxoptIfOMSnSinkCurrentLowOutputPower,
        jnxoptIfOMSnSinkCurrentHighOutputPower,
        jnxoptIfOMSnSinkIntervalSuspectedFlag,
        jnxoptIfOMSnSinkIntervalLastAggregatedInputPower,
        jnxoptIfOMSnSinkIntervalLowAggregatedInputPower,
        jnxoptIfOMSnSinkIntervalHighAggregatedInputPower,
        jnxoptIfOMSnSinkIntervalLastOutputPower,
        jnxoptIfOMSnSinkIntervalLowOutputPower,
        jnxoptIfOMSnSinkIntervalHighOutputPower,
        jnxoptIfOMSnSinkCurDaySuspectedFlag,
        jnxoptIfOMSnSinkCurDayLowAggregatedInputPower,
        jnxoptIfOMSnSinkCurDayHighAggregatedInputPower,
        jnxoptIfOMSnSinkCurDayLowOutputPower,
        jnxoptIfOMSnSinkCurDayHighOutputPower,
        jnxoptIfOMSnSinkPrevDaySuspectedFlag,
        jnxoptIfOMSnSinkPrevDayLastAggregatedInputPower,
        jnxoptIfOMSnSinkPrevDayLowAggregatedInputPower,
        jnxoptIfOMSnSinkPrevDayHighAggregatedInputPower,
        jnxoptIfOMSnSinkPrevDayLastOutputPower,
        jnxoptIfOMSnSinkPrevDayLowOutputPower,
        jnxoptIfOMSnSinkPrevDayHighOutputPower
    } 
    STATUS current 
    DESCRIPTION
      "A collection of pre-OTN performance monitoring 
      objects applicable to OMSn interfaces that 
      support sink functions."
    ::= { jnxoptIfGroups 15 } 


jnxoptIfOMSnSinkPreOtnPMThresholdGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOMSnSinkCurrentLowerInputPowerThreshold,
        jnxoptIfOMSnSinkCurrentUpperInputPowerThreshold,
        jnxoptIfOMSnSinkCurrentLowerOutputPowerThreshold,
        jnxoptIfOMSnSinkCurrentUpperOutputPowerThreshold 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OMSn interfaces 
      that support sink functions." 
    ::= { jnxoptIfGroups 16 } 


jnxoptIfOMSnSourcePreOtnPMGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOMSnSrcCurrentSuspectedFlag,
        jnxoptIfOMSnSrcCurrentOutputPower,
        jnxoptIfOMSnSrcCurrentLowOutputPower,
        jnxoptIfOMSnSrcCurrentHighOutputPower,
        jnxoptIfOMSnSrcCurrentAggregatedInputPower,
        jnxoptIfOMSnSrcCurrentLowAggregatedInputPower,
        jnxoptIfOMSnSrcCurrentHighAggregatedInputPower,
        jnxoptIfOMSnSrcIntervalSuspectedFlag,
        jnxoptIfOMSnSrcIntervalLastOutputPower,
        jnxoptIfOMSnSrcIntervalLowOutputPower,
        jnxoptIfOMSnSrcIntervalHighOutputPower,
        jnxoptIfOMSnSrcIntervalLastAggregatedInputPower,
        jnxoptIfOMSnSrcIntervalLowAggregatedInputPower,
        jnxoptIfOMSnSrcIntervalHighAggregatedInputPower,
        jnxoptIfOMSnSrcCurDaySuspectedFlag,
        jnxoptIfOMSnSrcCurDayLowOutputPower,
        jnxoptIfOMSnSrcCurDayHighOutputPower,
        jnxoptIfOMSnSrcCurDayLowAggregatedInputPower,
        jnxoptIfOMSnSrcCurDayHighAggregatedInputPower,
        jnxoptIfOMSnSrcPrevDaySuspectedFlag,
        jnxoptIfOMSnSrcPrevDayLastOutputPower,
        jnxoptIfOMSnSrcPrevDayLowOutputPower,
        jnxoptIfOMSnSrcPrevDayHighOutputPower,
        jnxoptIfOMSnSrcPrevDayLastAggregatedInputPower,
        jnxoptIfOMSnSrcPrevDayLowAggregatedInputPower,
        jnxoptIfOMSnSrcPrevDayHighAggregatedInputPower 
    }
    STATUS current
    DESCRIPTION
      "A collection of pre-OTN performance monitoring 
       objects applicable to OMSn interfaces that 
       support source functions."
    ::= { jnxoptIfGroups 17 } 


jnxoptIfOMSnSourcePreOtnPMThresholdGroup OBJECT-GROUP 
     OBJECTS { 
         jnxoptIfOMSnSrcCurrentLowerOutputPowerThreshold,
         jnxoptIfOMSnSrcCurrentUpperOutputPowerThreshold,
         jnxoptIfOMSnSrcCurrentLowerInputPowerThreshold,
         jnxoptIfOMSnSrcCurrentUpperInputPowerThreshold 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OMSn interfaces that 
      that support source functions." 
    ::= { jnxoptIfGroups 18 } 
  

jnxoptIfOChGroupCommonGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChGroupDirectionality 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all OChGroup interfaces." 
    ::= { jnxoptIfGroups 19 } 


jnxoptIfOChGroupSinkPreOtnPMGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChGroupSinkCurrentSuspectedFlag,
        jnxoptIfOChGroupSinkCurrentAggregatedInputPower,
        jnxoptIfOChGroupSinkCurrentLowAggregatedInputPower,
        jnxoptIfOChGroupSinkCurrentHighAggregatedInputPower,
        jnxoptIfOChGroupSinkCurrentOutputPower,
        jnxoptIfOChGroupSinkCurrentLowOutputPower,
        jnxoptIfOChGroupSinkCurrentHighOutputPower,
        jnxoptIfOChGroupSinkIntervalSuspectedFlag,
        jnxoptIfOChGroupSinkIntervalLastAggregatedInputPower,
        jnxoptIfOChGroupSinkIntervalLowAggregatedInputPower,
        jnxoptIfOChGroupSinkIntervalHighAggregatedInputPower,
        jnxoptIfOChGroupSinkIntervalLastOutputPower,
        jnxoptIfOChGroupSinkIntervalLowOutputPower,
        jnxoptIfOChGroupSinkIntervalHighOutputPower,
        jnxoptIfOChGroupSinkCurDaySuspectedFlag,
        jnxoptIfOChGroupSinkCurDayLowAggregatedInputPower,
        jnxoptIfOChGroupSinkCurDayHighAggregatedInputPower,
        jnxoptIfOChGroupSinkCurDayLowOutputPower,
        jnxoptIfOChGroupSinkCurDayHighOutputPower,
        jnxoptIfOChGroupSinkPrevDaySuspectedFlag,
        jnxoptIfOChGroupSinkPrevDayLastAggregatedInputPower,
        jnxoptIfOChGroupSinkPrevDayLowAggregatedInputPower,
        jnxoptIfOChGroupSinkPrevDayHighAggregatedInputPower,
        jnxoptIfOChGroupSinkPrevDayLastOutputPower,
        jnxoptIfOChGroupSinkPrevDayLowOutputPower,
        jnxoptIfOChGroupSinkPrevDayHighOutputPower 
    }
    STATUS current
    DESCRIPTION
      "A collection of pre-OTN performance monitoring 
      objects applicable to OChGroup interfaces that 
      support sink functions."
    ::= { jnxoptIfGroups 20 } 


jnxoptIfOChGroupSinkPreOtnPMThresholdGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChGroupSinkCurrentLowerInputPowerThreshold,
        jnxoptIfOChGroupSinkCurrentUpperInputPowerThreshold,
        jnxoptIfOChGroupSinkCurrentLowerOutputPowerThreshold,
        jnxoptIfOChGroupSinkCurrentUpperOutputPowerThreshold
 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OChGroup interfaces 
      that support sink functions." 
    ::= { jnxoptIfGroups 21 } 


jnxoptIfOChGroupSourcePreOtnPMGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChGroupSrcCurrentSuspectedFlag,
        jnxoptIfOChGroupSrcCurrentOutputPower,
        jnxoptIfOChGroupSrcCurrentLowOutputPower,
        jnxoptIfOChGroupSrcCurrentHighOutputPower,
        jnxoptIfOChGroupSrcCurrentAggregatedInputPower,
        jnxoptIfOChGroupSrcCurrentLowAggregatedInputPower,
        jnxoptIfOChGroupSrcCurrentHighAggregatedInputPower,
        jnxoptIfOChGroupSrcIntervalSuspectedFlag,
        jnxoptIfOChGroupSrcIntervalLastOutputPower,
        jnxoptIfOChGroupSrcIntervalLowOutputPower,
        jnxoptIfOChGroupSrcIntervalHighOutputPower,
        jnxoptIfOChGroupSrcIntervalLastAggregatedInputPower,
        jnxoptIfOChGroupSrcIntervalLowAggregatedInputPower,
        jnxoptIfOChGroupSrcIntervalHighAggregatedInputPower,
        jnxoptIfOChGroupSrcCurDaySuspectedFlag,
        jnxoptIfOChGroupSrcCurDayLowOutputPower,
        jnxoptIfOChGroupSrcCurDayHighOutputPower,
        jnxoptIfOChGroupSrcCurDayLowAggregatedInputPower,
        jnxoptIfOChGroupSrcCurDayHighAggregatedInputPower,
        jnxoptIfOChGroupSrcPrevDaySuspectedFlag,
        jnxoptIfOChGroupSrcPrevDayLastOutputPower,
        jnxoptIfOChGroupSrcPrevDayLowOutputPower,
        jnxoptIfOChGroupSrcPrevDayHighOutputPower,
        jnxoptIfOChGroupSrcPrevDayLastAggregatedInputPower,
        jnxoptIfOChGroupSrcPrevDayLowAggregatedInputPower,
        jnxoptIfOChGroupSrcPrevDayHighAggregatedInputPower
    }
    STATUS current
    DESCRIPTION
      "A collection of pre-OTN performance monitoring 
      objects applicable to OChGroup interfaces that 
      support source functions."
    ::= { jnxoptIfGroups 22 } 


jnxoptIfOChGroupSourcePreOtnPMThresholdGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChGroupSrcCurrentLowerOutputPowerThreshold,
        jnxoptIfOChGroupSrcCurrentUpperOutputPowerThreshold,
        jnxoptIfOChGroupSrcCurrentLowerInputPowerThreshold,
        jnxoptIfOChGroupSrcCurrentUpperInputPowerThreshold 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OChGroup interfaces that 
      that support source functions." 
    ::= { jnxoptIfGroups 23 } 


jnxoptIfOChCommonGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChDirectionality 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all OCh interfaces." 
    ::= { jnxoptIfGroups 24 } 



jnxoptIfOChSinkGroupBasic OBJECT-GROUP 
    OBJECTS { 
         jnxoptIfOChCurrentStatus 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all OCh interfaces that 
      support sink functions." 
    ::= { jnxoptIfGroups 25 } 


jnxoptIfOChSinkPreOtnPMGroup OBJECT-GROUP 
    OBJECTS {     
        jnxoptIfOChSinkCurrentSuspectedFlag,
        jnxoptIfOChSinkCurrentInputPower,
        jnxoptIfOChSinkCurrentLowInputPower,
        jnxoptIfOChSinkCurrentHighInputPower,
        jnxoptIfOChSinkIntervalSuspectedFlag,
        jnxoptIfOChSinkIntervalLastInputPower,
        jnxoptIfOChSinkIntervalLowInputPower,
        jnxoptIfOChSinkIntervalHighInputPower,
        jnxoptIfOChSinkCurDaySuspectedFlag,
        jnxoptIfOChSinkCurDayLowInputPower,
        jnxoptIfOChSinkCurDayHighInputPower,
        jnxoptIfOChSinkPrevDaySuspectedFlag,
        jnxoptIfOChSinkPrevDayLastInputPower,
        jnxoptIfOChSinkPrevDayLowInputPower,
        jnxoptIfOChSinkPrevDayHighInputPower 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      objects applicable to OCh interfaces that 
      support sink functions." 
    ::= { jnxoptIfGroups 26 } 


jnxoptIfOChSinkPreOtnPMThresholdGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChSinkCurrentLowerInputPowerThreshold, 
        jnxoptIfOChSinkCurrentUpperInputPowerThreshold 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OCh interfaces 
      that support sink functions." 
    ::= { jnxoptIfGroups 27 } 

jnxoptIfOChSourcePreOtnPMGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChSrcCurrentSuspectedFlag,
        jnxoptIfOChSrcCurrentOutputPower,
        jnxoptIfOChSrcCurrentLowOutputPower,
        jnxoptIfOChSrcCurrentHighOutputPower,
        jnxoptIfOChSrcIntervalSuspectedFlag,
        jnxoptIfOChSrcIntervalLastOutputPower,
        jnxoptIfOChSrcIntervalLowOutputPower,
        jnxoptIfOChSrcIntervalHighOutputPower,
        jnxoptIfOChSrcCurDaySuspectedFlag,
        jnxoptIfOChSrcCurDayLowOutputPower,
        jnxoptIfOChSrcCurDayHighOutputPower,
        jnxoptIfOChSrcPrevDaySuspectedFlag,
        jnxoptIfOChSrcPrevDayLastOutputPower,
        jnxoptIfOChSrcPrevDayLowOutputPower,
        jnxoptIfOChSrcPrevDayHighOutputPower 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      objects applicable to OCh interfaces that 
      support source functions." 
    ::= { jnxoptIfGroups 28 } 


jnxoptIfOChSourcePreOtnPMThresholdGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfOChSrcCurrentLowerOutputPowerThreshold, 
        jnxoptIfOChSrcCurrentUpperOutputPowerThreshold 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of pre-OTN performance monitoring 
      threshold objects applicable to OCh interfaces 
      that support source functions." 
    ::= { jnxoptIfGroups 29 } 


jnxoptIfOTUkCommonGroup OBJECT-GROUP 
   OBJECTS { 
       jnxoptIfOTUkDirectionality, 
       jnxoptIfOTUkBitRateK 
   } 
   STATUS current 
   DESCRIPTION 
     "A collection of configuration objects 
     applicable to all OTUk interfaces." 
   ::= { jnxoptIfGroups 30 } 



jnxoptIfOTUkSourceGroup OBJECT-GROUP
    OBJECTS { 
        jnxoptIfOTUkTraceIdentifierTransmitted, 
        jnxoptIfOTUkSourceAdaptActive 
    }
    STATUS current 
    DESCRIPTION
      "A collection of configuration objects 
      applicable to OTUk interfaces that 
      support source functions."
    ::= { jnxoptIfGroups 31 } 


jnxoptIfOTUkSinkGroup OBJECT-GROUP
    OBJECTS { 
         jnxoptIfOTUkDAPIExpected,
        jnxoptIfOTUkSAPIExpected,
        jnxoptIfOTUkTraceIdentifierAccepted,
        jnxoptIfOTUkTIMDetMode,
        jnxoptIfOTUkTIMActEnabled,
        jnxoptIfOTUkDEGThr,
        jnxoptIfOTUkDEGM,
        jnxoptIfOTUkSinkAdaptActive,
        jnxoptIfOTUkSinkFECEnabled,
        jnxoptIfOTUkCurrentStatus 
    }
    STATUS current 
    DESCRIPTION
      "A collection of configuration objects 
      applicable to OTUk interfaces that 
      support sink functions."
    ::= { jnxoptIfGroups 32 } 


jnxoptIfGCC0Group OBJECT-GROUP
    OBJECTS { 
        jnxoptIfGCC0Application, 
        jnxoptIfGCC0RowStatus 
    }
    STATUS current 
    DESCRIPTION 
      "A collection of GCC0 configuration objects." 
    ::= { jnxoptIfGroups 33 } 


jnxoptIfODUkGroup OBJECT-GROUP
   OBJECTS { 
        jnxoptIfODUkDirectionality,
        jnxoptIfODUkBitRateK,
        jnxoptIfODUkTcmFieldsInUse,
        jnxoptIfODUkPositionSeqCurrentSize,
        jnxoptIfODUkPositionSeqPosition,
        jnxoptIfODUkPositionSeqPointer,
        jnxoptIfODUkTtpPresent
    }
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all ODUk interfaces." 
    ::= { jnxoptIfGroups 34 } 


jnxoptIfODUkTtpSourceGroup OBJECT-GROUP
    OBJECTS { 
        jnxoptIfODUkTtpTraceIdentifierTransmitted 
    }
    STATUS current 
    DESCRIPTION
      "A collection of configuration objects 
      applicable to all interfaces that support 
      ODUk trail termination source functions."
    ::= { jnxoptIfGroups 35 } 


jnxoptIfODUkTtpSinkGroup OBJECT-GROUP
     OBJECTS { 
        jnxoptIfODUkTtpDAPIExpected,
        jnxoptIfODUkTtpSAPIExpected,
        jnxoptIfODUkTtpTraceIdentifierAccepted,
        jnxoptIfODUkTtpTIMDetMode,
        jnxoptIfODUkTtpTIMActEnabled,
        jnxoptIfODUkTtpDEGThr,
        jnxoptIfODUkTtpDEGM,
        jnxoptIfODUkTtpCurrentStatus 
    }
    STATUS current 
    DESCRIPTION
      "A collection of ODUk configuration objects 
      applicable to all interfaces that support 
      ODUk trail termination sink functions."
    ::= { jnxoptIfGroups 36 } 


jnxoptIfODUkNimGroup OBJECT-GROUP
     OBJECTS { 
        jnxoptIfODUkNimDAPIExpected,
        jnxoptIfODUkNimSAPIExpected,
        jnxoptIfODUkNimTraceIdentifierAccepted,
        jnxoptIfODUkNimTIMDetMode,
        jnxoptIfODUkNimTIMActEnabled,
        jnxoptIfODUkNimDEGThr,
        jnxoptIfODUkNimDEGM,
        jnxoptIfODUkNimCurrentStatus,
        jnxoptIfODUkNimRowStatus
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of ODUk Nim configuration objects." 
    ::= { jnxoptIfGroups 37 } 

jnxoptIfGCC12Group OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfGCC12GCCPassThrough, 
        jnxoptIfGCC12Application, 
        jnxoptIfGCC12RowStatus 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of GCC12 configuration objects." 
    ::= { jnxoptIfGroups 38 } 

jnxoptIfODUkTCommonGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfODUkTRowStatus 
    } 
    STATUS current 
    DESCRIPTION 
     "A collection of configuration objects 
     applicable to all ODUkT instances." 
    ::= { jnxoptIfGroups 39 } 

jnxoptIfODUkTSourceGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfODUkTTraceIdentifierTransmitted, 
        jnxoptIfODUkTSourceLockSignalAdminState 
    } 
    STATUS current 
    DESCRIPTION 
      "A collection of configuration objects 
      applicable to all ODUkT instances 
      that provide source functions." 
    ::= { jnxoptIfGroups 40 } 

jnxoptIfODUkTSinkGroup OBJECT-GROUP 
    OBJECTS { 
        jnxoptIfODUkTDAPIExpected,
        jnxoptIfODUkTSAPIExpected,
        jnxoptIfODUkTTraceIdentifierAccepted,
        jnxoptIfODUkTTIMDetMode,
        jnxoptIfODUkTTIMActEnabled,
        jnxoptIfODUkTDEGThr,
        jnxoptIfODUkTDEGM,
        jnxoptIfODUkTCurrentStatus
    }
    STATUS current
    DESCRIPTION
      "A collection of configuration objects 
      applicable to all ODUkT instances 
      that provide sink functions."
    ::= { jnxoptIfGroups 41 } 


jnxoptIfODUkTSinkGroupCtp OBJECT-GROUP 
   OBJECTS { 
       jnxoptIfODUkTSinkMode, 
       jnxoptIfODUkTSinkLockSignalAdminState 
   } 
   STATUS current 
   DESCRIPTION 
     "A collection of configuration objects 
     applicable to ODUkT instances not 
     colocated with an ODUk TTP that 
     provide sink functions." 
   ::= { jnxoptIfGroups 42 } 


jnxoptIfODUkTNimGroup OBJECT-GROUP 
   OBJECTS { 
       jnxoptIfODUkTNimDAPIExpected,
       jnxoptIfODUkTNimSAPIExpected,
       jnxoptIfODUkTNimTraceIdentifierAccepted,
       jnxoptIfODUkTNimTIMDetMode,
       jnxoptIfODUkTNimTIMActEnabled,
       jnxoptIfODUkTNimDEGThr,
       jnxoptIfODUkTNimDEGM,
       jnxoptIfODUkTNimCurrentStatus,
       jnxoptIfODUkTNimRowStatus 
   } 
   STATUS current 
   DESCRIPTION 
   "A collection of ODUkT Nim configuration objects." 
   ::= { jnxoptIfGroups 43 } 


-- compliance specifications 


jnxoptIfOtnConfigCompl MODULE-COMPLIANCE 
    STATUS current 
    DESCRIPTION 
      "Implementation requirements for the OTN configuration 
      functions defined in this MIB module." 
    MODULE -- this module
    MANDATORY-GROUPS { 
        jnxoptIfOTMnGroup, 
        jnxoptIfOTSnCommonGroup 
    }


GROUP jnxoptIfOTSnSourceGroupFull
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalTransport(196) for which the corresponding 
      instance of jnxoptIfOTSnDirectionality has the value 
      source(2) or bidirectional(3), the corresponding 
      instance of jnxoptIfOTMnReduced has the value false(2), 
      and the corresponding instance of jnxoptIfOTMnInterfaceType 
      specifies an OTMn interface type of 'IaDI'."


GROUP jnxoptIfOTSnAPRStatusGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalTransport(196) that support Automatic Power 
      Reduction functions."

GROUP jnxoptIfOTSnAPRControlGroup
    DESCRIPTION
      "This group is optional, but is recommended for interfaces 
      of ifType opticalTransport(196) that provide Automatic 
      Power Reduction control functions."


GROUP jnxoptIfOTSnSinkGroupBasic
     DESCRIPTION
       "This group is mandatory for interfaces of ifType 
       opticalTransport(196) for which the corresponding 
       instance of jnxoptIfOTSnDirectionality has the value 
       sink(1) or bidirectional(3)."

GROUP jnxoptIfOTSnSinkGroupFull
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalTransport(196) for which the corresponding 
      instance of jnxoptIfOTSnDirectionality has the value 
      sink(1) or bidirectional(3), the corresponding 
      instance of jnxoptIfOTMnReduced has the value false(2), 
      and the corresponding instance of jnxoptIfOTMnInterfaceType 
      specifies an OTMn interface type of 'IaDI'."


GROUP jnxoptIfOMSnCommonGroup 
    DESCRIPTION 
      "This group is mandatory for interfaces of ifType 
      opticalTransport(196) that support access to the OMS 
      overhead information within the OTN Supervisory Channel."

GROUP jnxoptIfOMSnSinkGroupBasic
    DESCRIPTION
     "This group is mandatory for interfaces of ifType 
      opticalTransport(196) that support access to the OMS Overhead 
      information within the OSC (OTN Supervisory Channel) 
      for which the corresponding 
      instance of jnxoptIfOMSnDirectionality has the value 
      sink(1) or bidirectional(3)."


GROUP jnxoptIfOChGroupCommonGroup 
    DESCRIPTION 
      "This group is mandatory for interfaces of ifType 
      opticalChannelGroup(219)."


GROUP jnxoptIfOChCommonGroup 
   DESCRIPTION 
      "This group is mandatory for interfaces of ifType 
      opticalTransport(195)."


GROUP jnxoptIfOChSinkGroupBasic
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) for which the corresponding 
      instance of jnxoptIfOChDirectionality has the value 
      sink(1) or bidirectional(3)."


GROUP jnxoptIfOTUkCommonGroup 
    DESCRIPTION 
       "This group is mandatory for interfaces of ifType 
       opticalChannel(195) that support OTUk layer functions."


GROUP jnxoptIfOTUkSourceGroup
     DESCRIPTION
       "This group is mandatory for interfaces of ifType 
       opticalChannel(195) that support OTUk layer functions 
       and for which the corresponding instance of 
       jnxoptIfOTUkDirectionality has the value source(2) or 
       bidirectional(3)."


GROUP jnxoptIfOTUkSinkGroup
     DESCRIPTION
       "This group is mandatory for interfaces of ifType 
       opticalChannel(195) that support OTUk layer functions 
       and for which the corresponding instance of 
       jnxoptIfOTUkDirectionality has the value sink(1) or
       bidirectional(3)."

GROUP jnxoptIfGCC0Group
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support GCC0 access functions. 
      It may be implemented only if the jnxoptIfOTUkCommonGroup 
      is also implemented."


GROUP jnxoptIfODUkGroup 
    DESCRIPTION 
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support ODUk layer functions."


GROUP jnxoptIfODUkTtpSourceGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) for which the corresponding 
      instance of jnxoptIfODUkTtpPresent has the value 
      true(1) and for which the corresponding instance of 
      jnxoptIfODUkDirectionality has the value source(2) or 
      bidirectional(3). It may be implemented only if the 
      jnxoptIfODUkGroup is also implemented."


GROUP jnxoptIfODUkTtpSinkGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) for which the corresponding 
      instance of jnxoptIfODUkTtpPresent has the value 
      true(1) and for which the corresponding instance of 
      jnxoptIfODUkDirectionality has the value sink(1) or 
      bidirectional(3). It may be implemented only if the 
      jnxoptIfODUkGroup is also implemented."


GROUP jnxoptIfODUkNimGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) for which the corresponding 
      instance of jnxoptIfODUkTtpPresent has the value 
      false(2). It may be implemented only if the 
      jnxoptIfODUkGroup is also implemented."


GROUP jnxoptIfGCC12Group
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support GCC12 access functions. 
      It may be implemented only if the jnxoptIfODUkGroup 
      is also implemented."


GROUP jnxoptIfODUkTCommonGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support intrusive 
      tandem connection monitoring. It may be implemented 
      only if the jnxoptIfODUkGroup is also implemented."


GROUP jnxoptIfODUkTSourceGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support intrusive 
      tandem connection monitoring and for which
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value false(2), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value true(1). 
      It may be implemented only if the jnxoptIfODUkGroup is 
      also implemented."


GROUP jnxoptIfODUkTSinkGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support intrusive 
      tandem connection monitoring and for which
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It may be implemented only if the jnxoptIfODUkGroup is 
      also implemented."


GROUP jnxoptIfODUkTSinkGroupCtp
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support intrusive 
      tandem connection monitoring and for which 
      jnxoptIfODUkTtpPresent is false(2) and
      (i) jnxoptIfODUkDirectionality has the value bidirectional(3), or 
      (ii) jnxoptIfODUkDirectionality has the value sink(1) and 
      jnxoptIfODUkTCodirectional has the value true(1), or 
      (iii) jnxoptIfODUkDirectionality has the value source(3) and 
      jnxoptIfODUkTCodirectional has the value false(2). 
      It may be implemented only if the jnxoptIfODUkGroup and 
      jnxoptIfODUkTSinkGroup are also implemented." 


GROUP jnxoptIfODUkTNimGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support non-intrusive 
      tandem connection monitoring. It may be implemented 
      only if the jnxoptIfODUkGroup is also implemented."
    ::= { jnxoptIfCompl 1 } 


jnxoptIfPreOtnPMCompl MODULE-COMPLIANCE 
    STATUS current 
    DESCRIPTION 
      "Implementation requirements for Pre-OTN performance 
      monitoring functions defined in this MIB module."
    MODULE -- this module
    MANDATORY-GROUPS { 
        jnxoptIfPerfMonGroup 
    }

GROUP jnxoptIfOTSnSinkPreOtnPMGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalTransport(196) that support OTSn sink 
      functions (i.e., for which the corresponding instance 
      of jnxoptIfOTSnDirectionality -- if implemented -- has 
      the value sink(1) or bidirectional(3))."


GROUP jnxoptIfOTSnSinkPreOtnPMThresholdGroup
    DESCRIPTION
      "This group is mandatory if and only if TCA notifications 
      are implemented. If the objects of this group are instantiated 
      then the implementation must also provide, in an 
      enterprise MIB, suitable TCA notification definitions and 
      notification control objects. Implementation of the 
      jnxoptIfOTSnSinkPreOtnPMGroup is a prerequisite for 
      implementing this group."


GROUP jnxoptIfOTSnSourcePreOtnPMGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalTransport(196) that support OTSn source 
      functions (i.e., for which the corresponding instance 
      of jnxoptIfOTSnDirectionality -- if implemented -- has 
      the value source(2) or bidirectional(3))."


GROUP jnxoptIfOTSnSourcePreOtnPMThresholdGroup 
    DESCRIPTION
      "This group is mandatory if and only if TCA notifications 
      are implemented. If the objects of this group are instantiated 
      then the implementation must also provide, in an 
      enterprise MIB, suitable TCA notification definitions and 
      notification control objects. Implementation of the 
      jnxoptIfOTSnSourcePreOtnPMGroup is a prerequisite for 
      implementing this group "


GROUP jnxoptIfOMSnSinkPreOtnPMGroup 
   DESCRIPTION
     "This group is optional. It may be implemented by systems 
      with the necessary instrumentation on interfaces of ifType 
      opticalTransport(196) that support OMSn sink functions 
      (i.e., for which the corresponding instance of 
      jnxoptIfOMSnDirectionality -- if implemented -- has the value 
      sink(1) or bidirectional(3))."


GROUP jnxoptIfOMSnSinkPreOtnPMThresholdGroup 
   DESCRIPTION
     "This group is mandatory if and only if TCA notifications 
     are implemented. If the objects of this group are instantiated 
     then the implementation must also provide, in an 
     enterprise MIB, suitable TCA notification definitions and 
     notification control objects. Implementation of the 
     jnxoptIfOMSnSinkPreOtnPMGroup is a prerequisite for 
     implementing this group "


GROUP jnxoptIfOMSnSourcePreOtnPMGroup 
    DESCRIPTION
      "This group is optional. It may be implemented by systems 
      with the necessary instrumentation on interfaces of ifType 
      opticalTransport(196) that support OMSn source functions 
      (i.e., for which the corresponding instance of 
      jnxoptIfOMSnDirectionality -- if implemented -- has the value 
      source(2) or bidirectional(3))."


GROUP jnxoptIfOMSnSourcePreOtnPMThresholdGroup 
    DESCRIPTION
      "This group is mandatory if and only if TCA notifications 
      are implemented. If the objects of this group are instantiated 
      then the implementation must also provide, in an 
      enterprise MIB, suitable TCA notification definitions and 
      notification control objects. Implementation of the 
      jnxoptIfOMSnSourcePreOtnPMGroup is a prerequisite for 
      implementing this group "


GROUP jnxoptIfOChGroupSinkPreOtnPMGroup 
    DESCRIPTION
      "This group is optional. It may be implemented by systems 
      with the necessary instrumentation on interfaces of ifType 
      opticalChannelGroup(219) that support OChGroup sink functions 
      (i.e., for which the corresponding instance of 
      jnxoptIfOChGroupDirectionality -- if implemented -- has the value 
      sink(1) or bidirectional(3))."


GROUP jnxoptIfOChGroupSinkPreOtnPMThresholdGroup
    DESCRIPTION
      "This group is mandatory if and only if TCA notifications 
      are implemented. If the objects of this group are instantiated 
      then the implementation must also provide, in an 
      enterprise MIB, suitable TCA notification definitions and 
      notification control objects. Implementation of the 
      jnxoptIfOChGroupSinkPreOtnPMGroup is a prerequisite for 
      implementing this group "


GROUP jnxoptIfOChGroupSourcePreOtnPMGroup
    DESCRIPTION
      "This group is optional. It may be implemented by systems 
      with the necessary instrumentation on interfaces of ifType 
      opticalChannelGroup(219) that support OChGroup source functions 
      (i.e., for which the corresponding instance of 
      jnxoptIfOChGroupDirectionality -- if implemented -- has the value 
      source(2) or bidirectional(3))."


GROUP jnxoptIfOChGroupSourcePreOtnPMThresholdGroup
    DESCRIPTION
      "This group is mandatory if and only if TCA notifications 
      are implemented. If the objects of this group are instantiated 
      then the implementation must also provide, in an 
      enterprise MIB, suitable TCA notification definitions and 
      notification control objects. Implementation of the 
      jnxoptIfOChGroupSourcePreOtnPMGroup is a prerequisite for 
      implementing this group "


GROUP jnxoptIfOChSinkPreOtnPMGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support OCh sink functions 
      (i.e., for which the corresponding instance of 
      jnxoptIfOChDirectionality -- if implemented -- has the 
      value sink(1) or bidirectional(3))."


GROUP jnxoptIfOChSinkPreOtnPMThresholdGroup 
    DESCRIPTION 
      "This group is mandatory if and only if TCA notifications 
      are implemented. If the objects of this group are instantiated 
      then the implementation must also provide, in an 
      enterprise MIB, suitable TCA notification definitions and 
      notification control objects. Implementation of the 
      jnxoptIfOChSinkPreOtnPMGroup is a prerequisite for 
      implementing this group "


GROUP jnxoptIfOChSourcePreOtnPMGroup
    DESCRIPTION
      "This group is mandatory for interfaces of ifType 
      opticalChannel(195) that support OCh source functions 
      (i.e., for which the corresponding instance of 
      jnxoptIfOChDirectionality -- if implemented -- has the 
      value source(2) or bidirectional(3))."


GROUP jnxoptIfOChSourcePreOtnPMThresholdGroup
    DESCRIPTION
      "This group is mandatory if and only if TCA notifications 
      are implemented. If the objects of this group are instantiated 
      then the implementation must also provide, in an 
      enterprise MIB, suitable TCA notification definitions and 
      notification control objects. Implementation of the 
      jnxoptIfOChSourcePreOtnPMGroup is a prerequisite for 
      implementing this group "
    ::= { jnxoptIfCompl 2 } 


END 







