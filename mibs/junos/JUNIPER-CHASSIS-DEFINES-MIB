
--
-- Juniper chassis mib definitions:
-- OIDs used to identify various platforms and chassis components.
--
-- Copyright (c) 1998-2016, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--


JUNIPER-CHASSIS-DEFINES-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY
        FROM SNMPv2-SMI
    jnxProducts, jnxMibs
        FROM JUNIPER-SMI;

jnxChassisDefines MODULE-IDENTITY

    LAST-UPDATED "202306140000Z" -- Wed Jun 14 00:00:00 2023 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
		     Juniper Networks, Inc.
		     1133 Innovation Way
		     Sunnyvale, CA 94089
		     E-mail: <EMAIL>"

    DESCRIPTION
            "The MIB modules defines OIDs used by chassis mib to
            identify platform and chassis components."
    REVISION
            "201002010000Z" -- Mon Feb 01 00:00:00 2010 UTC
    DESCRIPTION
            "PIC object for EX8200 PICS 36XS and 40XS are added."
    REVISION    "201102070000Z" -- 07-Feb-11
    DESCRIPTION
            "Added Quantum Fabric Series 3000 and 5000."
    REVISION
            "201207180000Z" -- 18-Jul-12
    DESCRIPTION
            "Added Altius-1 / MX104 chassis."
    REVISION    "201209130000Z" -- 13-Sept-12
    DESCRIPTION
            "Added QFX3100."
    REVISION    "201209130000Z" -- 13-Sep-12
    DESCRIPTION
            "Added EX4300 product."
    REVISION    "201301100000Z" -- 10-Jan-13
    DESCRIPTION
            "Added EX9206/EX9208/EX9204 chassis info"
    REVISION    "201310170000Z" -- 17-Oct-13
    DESCRIPTION
            "Added QFX5100 products.
             Added 96x10GE + 8x40GE PIC object(Cakebread).
             Added 48x10GBASET + 6x40GE PIC object(Nirvana)."
    REVISION    "201401270000Z" -- 27-Jan-14
    DESCRIPTION
            "Added EX4600 product (Ridge)."
    REVISION    "201404010000Z" -- 01-Apr-14
    DESCRIPTION
            "Added STOUT/CHIVAS/SRX5K/FORTIUS product"
    REVISION    "201406170000Z" -- 17-JUN-14
    DESCRIPTION
            "Added POLARIS MLC 24x10GE PIC"
    REVISION    "201407140000Z" -- 14-JUL-14
    DESCRIPTION
            "Added 1X100GE DWDM CFP2-ACO"
    REVISION    "201407160000Z" -- 16-July-14
    DESCRIPTION
            "Added MX104-40G product"
    REVISION    "201409050000Z" -- 09-SEP-14
    DESCRIPTION
            "Added AS5712xx switches"
    REVISION    "201409270000Z" -- 27-Sep-14
    DESCRIPTION
            "Added separate OIDs for ACX product models"
    REVISION    "201412250000Z" -- 25-Dec-14
    DESCRIPTION
            "Added IOC3 PICs & SHOCHU load MIC"
    REVISION    "201501080000Z" -- 08-Jan-15
    DESCRIPTION
            "Added PTX/EX MIC and PIC"
    REVISION    "201501200000Z" -- 20-Jan-15
    DESCRIPTION
            "PTX5K 2X100G OTN PIC"
    REVISION    "201502240000Z" -- 24-Feb-15
    DESCRIPTION
            "5X100GE DWDM CFP2-ACO"
    REVISION    "201503250000Z" -- 25-Mar-15
    DESCRIPTION
            "VMX ESR"
    REVISION    "201504140000Z" -- 14-Apr-15
    DESCRIPTION
            "Added PTX 3000 PIC"

    REVISION    "201505200000Z" -- 20-May-15
    DESCRIPTION
            "Add PIX1K PIC"
    REVISION    "201507120000Z" -- 12-Jul-15
    DESCRIPTION
            "Correction and Moving"
    REVISION    "201507230000Z" -- 23-Jul-15
    DESCRIPTION
            "Added PTX1000 Product"
    REVISION    "201507280000Z" -- 28-Jul-15
    DESCRIPTION
            "Add Virtual 10GE/40GE/100GE PIC"
    REVISION    "201508190000Z" -- 19-Aug-15
    DESCRIPTION
            "Added EX2300 products
             Added 4x10G PIC object
             Added 2x10G PIC object"

    REVISION    "201509020000Z" -- 02-Sep-15
    DESCRIPTION
            "Added Onager product
             Added T1E1 PIC
             Added VDSL PIC
             Added Serial PIC
             Added 16PORT GE POE PIC
             Added 8SFP PIC"
    REVISION    "201511170000Z" -- 17-Nov-15
    DESCRIPTION
            "Added VMX MIC description, removing duplicates, deleting 10x1GE from VMX MIC/PIC Description"

    REVISION    "201510120000Z" -- 12-Oct-15
    DESCRIPTION
            "Added Sword product.
             Added Sword-M/Sword-M-POE products.
             Added Trident/Trident+ products."
    REVISION    "201510130000Z" -- 13-Oct-15
    DESCRIPTION
            "Add SRX1500"
    REVISION    "201602080000Z" -- 08-Feb-16
    DESCRIPTION
            "Added Tornado MICs"
    REVISION    "201602190000Z" -- 19-Feb-16
    DESCRIPTION
            "Added NFX Product"
    REVISION    "201602230000Z" -- 23-Feb-16
    DESCRIPTION
            "Added MX104 FPM"

    REVISION    "201602230000Z" -- 24-Feb-16
    DESCRIPTION
            "Added MX 10003"
    REVISION    "201604060000Z" -- 06-Apr-16
    DESCRIPTION
            "Added SRX4600
             Added SRX4800"
    REVISION    "201604200000Z" -- 20-Apr-16
    DESCRIPTION
                "Added Pinnacle Products"
    REVISION    "201607150000Z" -- 15-Jul-16
    DESCRIPTION
             "Added MX 2008"

    REVISION    "201602230000Z" -- 24-Feb-16
    DESCRIPTION
            "Added MX 10003"

    REVISION    "201606060000Z" --06-Jun-16
    DESCRIPTION
            "Added MX 204"
    REVISION    "201605060000Z" -- 06-May-16
    DESCRIPTION
            "Added Rombauer QIC for OPUS"

    REVISION    "201605110000Z" -- 11-May-16
    DESCRIPTION
            "Added SRX4100
             Added SRX4200"
    REVISION    "201605310000Z" -- 31-May-16
    DESCRIPTION
            "Removed duplicate entries"

    REVISION    "201606060000Z" --06-Jun-16
    DESCRIPTION
             "Added MX 204"

    REVISION    "201606150000Z" -- 15-Jun-16
    DESCRIPTION
            "Add 3x400GE/12x100GE PIC to PTX"

    REVISION    "201607150000Z" -- 15-Jul-16
    DESCRIPTION
             "Added MX 2008"

    REVISION    "201608310000Z" -- 31-Aug-16
    DESCRIPTION
             "Added MXTSR80"

    REVISION    "201609150000Z" -- 15-Sep-16
    DESCRIPTION
            "Added sub OIDs for NFX inner software components"

    REVISION    "201609300000Z" -- 30-Sep-16
    DESCRIPTION
             "Removed unnecessary entries"

    REVISION    "201611030000Z" -- 03-Nov-16
    DESCRIPTION
             "Added LTE AA
              Added LTE AE"

    REVISION    "201611210000Z" -- 21-Nov-16
    DESCRIPTION
            "Added sub OIDs for AWS SKU for NFX inner software components"

    REVISION    "201611210000Z" -- 23-Nov-16
    DESCRIPTION
             "Added MX 5XQSFPP PIC"

    REVISION    "201612060000Z" -- 06-Dec-16
    DESCRIPTION
             "Added EX4300-48MP PIC objects"

    REVISION    "201612200000Z" -- 20-Dec-16
    DESCRIPTION
            "Added sub OIDs for S1E SKU for NFX inner software components"

    REVISION    "201702090000Z" -- 9-Feb-17
	DESCRIPTION
	"Added PTX10008 Product"

    REVISION    "201703300000Z" -- 30-Mar-17
    DESCRIPTION
             "Added ACX5448"

    REVISION    "201703060000Z" -- 14-Apr-17
    DESCRIPTION
             "Added EX4300-48MP SKU"

    REVISION    "201705030000Z" -- 03-May-17
    DESCRIPTION
             "Added Brackla PIC"

    REVISION    "201705150000Z" -- 15-May-17
    DESCRIPTION
             "Added MICs for ACX5448"

    REVISION    "201706020000Z" -- 02-Jun-17
    DESCRIPTION
             "Added Redbull PIC"

    REVISION    "201706030000Z" -- 03-Jun-17
    DESCRIPTION
            "Added 3x400GE FAKE PIC to PTX"

    REVISION    "201706190000Z" -- 19-Jun-17
    DESCRIPTION
            "Added ACX5448 ACCT-54X"

    REVISION    "201706280000Z" -- 28-Jun-17
    DESCRIPTION
	"Added PTX10016 Product"
    REVISION    "201707040000Z" -- 04-Jul-17
    DESCRIPTION
        "Added 7 new NFX Products"
    REVISION    "201708010000Z" -- 05-Aug-17
    DESCRIPTION
        "Added 2300-24MP/48MP SKUs and PICs"
    REVISION    "201709080000Z" -- 08-Sep-17
    DESCRIPTION
        "Added 3 new NFX Products"
    REVISION    "201710230000Z" -- 23-Oct-17
    DESCRIPTION
        "Added SPC3 and SPC3PIC"
    REVISION    "201711150000Z" -- 15-Nov-17
    DESCRIPTION
        "Added Gladiator PICS 96x10/24x40GE and 15x100GE/15x40GE/60x10GE"
    REVISION    "201711220000Z" -- 22-Nov-17
    DESCRIPTION
        "Added jnxChassisNFX,jnxNFXSlotFPC,jnxNFXSlotPIC,jnxNFXSlotHM,jnxNFXSlotPower,jnxNFXSlotFan"
    REVISION    "201712080000Z" -- 08-DEC-17
    DESCRIPTION
        "Added Onyx product (EX4650/QFX5120)."
    REVISION    "201712280000Z" -- 28-DEC-17
    DESCRIPTION
        "Added SYMPHONY And AZURITE PIC Information"
    REVISION    "201803200000Z" -- 20-MAR-18
    DESCRIPTION
        "Added MRATE-5xQSFPP PIC"
    REVISION    "201805110000Z" -- 11-MAY-18
    DESCRIPTION
        "Added JNP10K 36x QSFPDD PIC"
    REVISION    "201806150000Z" -- 15-Jun-18
    DESCRIPTION
        "Added Attella QSFP and CFP2DCO PICS"
    REVISION    "201804180000Z" -- 18-Apr-18
    DESCRIPTION
        "Added 2 new NFX350 Products and 1 NFX150 whitebox"
    REVISION    "201806260000Z" -- 26-Jun-18
    DESCRIPTION
        "Added NFX350-X Product"
    REVISION    "201807050000Z" -- 05-Jul-18
    DESCRIPTION
             "Added for ACX5448-M and ACX5448-D models"
    REVISION    "201809170000Z" -- 17-Sep-18
    DESCRIPTION
         "Added IOC4 PICs"
    REVISION    "201809240000Z" -- 24-Sep-18
    DESCRIPTION
        "Added MX SPC3 FPC and MX SPC3 PIC"
    REVISION    "201810120000Z" -- 12-Oct-18
    DESCRIPTION
        "Added JNP10K 36x QSFPDD DLC PIC"
    REVISION    "201811260000Z" -- 26-Nov-18
    DESCRIPTION
             "Added WAP US PIC
              Added WAP IS PIC
              Added WAP WW PIC"
    REVISION    "201812110000Z" -- 11-Dec-18
    DESCRIPTION
        "Added Added for ACX710 and ACX5800 models"
    REVISION    "201901100000Z" -- 10-Jan-19
    DESCRIPTION
        "Added Airbus SRX380 product"
    REVISION    "201906110000Z" -- 11-Jun-19
    DESCRIPTION
        "Added Lagavulin EX4400 product models and PICs"
    REVISION    "201908200000Z" -- 20-Aug-19
    DESCRIPTION
        "Added Absinthe QFX5120-48T product."
    REVISION    "201909300000Z" -- 30-Sept-19
    DESCRIPTION
        "Added Ardbeg PTX10001-36MR product models and PICs"
    REVISION    "201911060000Z" -- 06-Nov-19
    DESCRIPTION
        "Added Spectrolite product models and PICs"
    REVISION    "201911070000Z" -- 07-Nov-19
    DESCRIPTION
        "Added Monza EX9200 MPC10 line card product models and PICs"
    REVISION    "201911080000Z" -- 8-Nov-19
    DESCRIPTION
        "Added Lazurite 4xQSFPDD 32xQSFP PIC"
    REVISION    "201911100000Z" -- 10-Nov-19
    DESCRIPTION
        "Added Bolan ACX753 PICs"
    REVISION    "201911200000Z" -- 20-Nov-19
    DESCRIPTION
        "Added X-Men QFX5500 series PIC"
    REVISION    "201912200000Z" -- 20-Dec-19
    DESCRIPTION
        "Added MX10K8 Daniel Line card PIC"
    REVISION    "202001090000Z" -- 09-Jan-20
    DESCRIPTION
        "Added Pyrite QFX5120-48YM product."
    REVISION    "202002200000Z" -- 20-Feb-20
    DESCRIPTION
        "Added PTX10004 Product"
    REVISION    "202003120000Z" -- 12-Mar-20
    DESCRIPTION
        "Added Spectrolite Hillside QFX5009 product model and PICs
         Added Spectrolite Guardian ACX755 PIC"
    REVISION    "202004140000Z" -- 14-Apr-20
    DESCRIPTION
        "Added X-Men Wolverine Power-Saving-Mode PIC
         Added X-Men Storm Power-Saving-Mode PIC"
    REVISION    "202004240000Z" -- 24-Apr-20
    DESCRIPTION
        "Added ACX753 Product"
    REVISION    "202005080000Z" -- 08-May-20
    DESCRIPTION
        "Added Hummingbird SRX1800 Product model and PICs"
    REVISION    "202008260000Z" -- 26-Aug-20
    DESCRIPTION
        "Added ACX7K product line"
    REVISION    "202011080000Z" -- 08-Nov-20
    DESCRIPTION
        "Added jnxFEB, jnxQFXSwitchSlotFEB"
    REVISION    "202011160000Z" -- 16-Nov-20
    DESCRIPTION
        "Added 5G Modem PICs"
    REVISION    "202011180000Z" -- 18-Nov-20
    DESCRIPTION
        "Added Tabasco Crypto SPC4 PICs"
    REVISION    "202012072000Z" -- 07-Dec-20
    DESCRIPTION
        "Changed the product model QFX5009 to QFX5700"
    REVISION    "202101250000Z" -- 25-Jan-21
    DESCRIPTION
        "add product line ACX710048l and ACX710032c"
    REVISION    "202105180000Z" -- 18-May-21
    DESCRIPTION
        "add product line ACX7908"
    REVISION    "202106080000Z" -- 08-June-21
    DESCRIPTION
        "Added Spectrolite Hillside ACX755 PIC"
    REVISION    "202106230000Z" -- 23-June-21
    DESCRIPTION
        "Added Tomatin QFX5230-64CD PIC"
    REVISION    "202112070000Z" -- 07-Dec-21
    DESCRIPTION
        "Added Tomatin QFX5230-64CD PIC"
    REVISION    "202201180000Z" -- 18-Jan-22
    DESCRIPTION
        "Added SRX5k IOC5 line card PICs"
    REVISION    "202203080000Z" -- 08-Mar-22
    DESCRIPTION
        "Added Glenlivet QFX5130-48C PIC"
    REVISION    "202203170000Z" -- 17-Mar-22
    DESCRIPTION
        "Added EX4400-24X and Tyrconnel Module"
    REVISION    "202203180000Z" -- 18-Mar-22
    DESCRIPTION
        "Added Aegon Logical PIC"
    REVISION    "202204290000z" -- 29-Apr-22
    DESCRIPTION
        "Added I2C IDs for Fleming (SRX1600, SRX2300, SRX4300)"
    REVISION    "202203080000Z" -- 13-Jun-22
    DESCRIPTION
        "Added Glenlivet QFX5130-48CM PIC"
    REVISION    "202208010000Z" -- 01-Aug-22
    DESCRIPTION
        "Added ACX7332 and ACX7348 Chassis"
    REVISION    "202209230000Z" -- 23-Sep-22
    DESCRIPTION
        "Added Glendronach Logical PIC"
    REVISION    "202302130000Z" -- 13-Feb-23
    DESCRIPTION
        "Added ACX7024X "
    REVISION    "202304150000Z" -- 15-May-23
    DESCRIPTION
        "Added Garnet QFX5240-64xOSFP PIC"
    REVISION    "202304150000Z" -- 15-May-23
    DESCRIPTION
        "Added Garnet QFX5240-64xQSFPDD PIC"
    REVISION    "202305240000Z" -- 24-May-23
    DESCRIPTION
        "Added PTX10002-36QDD"
    REVISION    "202306140000Z" -- 14-Jun-23
    DESCRIPTION
        "Added Casio Royale (SRX4700)"
    ::= { jnxMibs 25 }


--
-- Products Classification
--
jnxClassification       OBJECT IDENTIFIER ::= { jnxProducts       1 }
jnxClassGeneral   	OBJECT IDENTIFIER ::= { jnxClassification 1 }
jnxClassContainers	OBJECT IDENTIFIER ::= { jnxClassification 2 }
jnxClassContents	OBJECT IDENTIFIER ::= { jnxClassification 3 }
jnxClassStatus		OBJECT IDENTIFIER ::= { jnxClassification 4 }

--
-- General Information
--

-- Product Line

jnxProductLine	        OBJECT IDENTIFIER ::= { jnxClassGeneral    1 }
  jnxProductLineM40	OBJECT IDENTIFIER ::= { jnxProductLine 1 }
  jnxProductLineM20 	OBJECT IDENTIFIER ::= { jnxProductLine 2 }
  jnxProductLineM160 	OBJECT IDENTIFIER ::= { jnxProductLine 3 }
  jnxProductLineM10 	OBJECT IDENTIFIER ::= { jnxProductLine 4 }
  jnxProductLineM5 	OBJECT IDENTIFIER ::= { jnxProductLine 5 }
  jnxProductLineT640 	OBJECT IDENTIFIER ::= { jnxProductLine 6 }
  jnxProductLineT320 	OBJECT IDENTIFIER ::= { jnxProductLine 7 }
  jnxProductLineM40e 	OBJECT IDENTIFIER ::= { jnxProductLine 8 }

-- Product Name

jnxProductName 	        OBJECT IDENTIFIER ::= { jnxClassGeneral 2 }
  jnxProductNameM40	OBJECT IDENTIFIER ::= { jnxProductName 1 }
  jnxProductNameM20	OBJECT IDENTIFIER ::= { jnxProductName 2 }
  jnxProductNameM160	OBJECT IDENTIFIER ::= { jnxProductName 3 }
  jnxProductNameM10	OBJECT IDENTIFIER ::= { jnxProductName 4 }
  jnxProductNameM5	OBJECT IDENTIFIER ::= { jnxProductName 5 }
  jnxProductNameT640	OBJECT IDENTIFIER ::= { jnxProductName 6 }
  jnxProductNameT320	OBJECT IDENTIFIER ::= { jnxProductName 7 }
  jnxProductNameM40e	OBJECT IDENTIFIER ::= { jnxProductName 8 }

-- Product Model

jnxProductModel         OBJECT IDENTIFIER ::= { jnxClassGeneral 3 }
  jnxProductModelM40	OBJECT IDENTIFIER ::= { jnxProductModel 1 }
  jnxProductModelM20	OBJECT IDENTIFIER ::= { jnxProductModel 2 }
  jnxProductModelM160	OBJECT IDENTIFIER ::= { jnxProductModel 3 }
  jnxProductModelM10	OBJECT IDENTIFIER ::= { jnxProductModel 4 }
  jnxProductModelM5	OBJECT IDENTIFIER ::= { jnxProductModel 5 }
  jnxProductModelT640	OBJECT IDENTIFIER ::= { jnxProductModel 6 }
  jnxProductModelT320	OBJECT IDENTIFIER ::= { jnxProductModel 7 }
  jnxProductModelM40e	OBJECT IDENTIFIER ::= { jnxProductModel 8 }

jnxProductVariation     OBJECT IDENTIFIER ::= { jnxClassGeneral 4 }
  jnxProductVariationM40    OBJECT IDENTIFIER ::= { jnxProductVariation 1  }
  jnxProductVariationM20    OBJECT IDENTIFIER ::= { jnxProductVariation 2  }
  jnxProductVariationM160   OBJECT IDENTIFIER ::= { jnxProductVariation 3  }
  jnxProductVariationM10    OBJECT IDENTIFIER ::= { jnxProductVariation 4  }
  jnxProductVariationM5     OBJECT IDENTIFIER ::= { jnxProductVariation 5  }
  jnxProductVariationT640   OBJECT IDENTIFIER ::= { jnxProductVariation 6  }
  jnxProductVariationT320   OBJECT IDENTIFIER ::= { jnxProductVariation 7  }
  jnxProductVariationM40e   OBJECT IDENTIFIER ::= { jnxProductVariation 8  }

--
-- Containers
--

-- Chassis

jnxChassis 	        OBJECT IDENTIFIER ::= { jnxClassContainers 1 }
  jnxChassisM40 	OBJECT IDENTIFIER ::= { jnxChassis 1 }
  jnxChassisM20 	OBJECT IDENTIFIER ::= { jnxChassis 2 }
  jnxChassisM160	OBJECT IDENTIFIER ::= { jnxChassis 3 }
  jnxChassisM10 	OBJECT IDENTIFIER ::= { jnxChassis 4 }
  jnxChassisM5  	OBJECT IDENTIFIER ::= { jnxChassis 5 }
  jnxChassisT640  	OBJECT IDENTIFIER ::= { jnxChassis 6 }
  jnxChassisT320  	OBJECT IDENTIFIER ::= { jnxChassis 7 }
  jnxChassisM40e  	OBJECT IDENTIFIER ::= { jnxChassis 8 }

-- Slot

jnxSlot 	          OBJECT IDENTIFIER ::= { jnxClassContainers 2 }
  jnxSlotM40	  	  OBJECT IDENTIFIER ::= { jnxSlot 1 }
    jnxSlotFPC 		  OBJECT IDENTIFIER ::= { jnxSlotM40 1 }
    jnxSlotSCB 		  OBJECT IDENTIFIER ::= { jnxSlotM40 2 }
    jnxSlotHostCtlr 	  OBJECT IDENTIFIER ::= { jnxSlotM40 3 }
    jnxSlotPowerSupply 	  OBJECT IDENTIFIER ::= { jnxSlotM40 4 }
    jnxSlotCoolingImpeller
			  OBJECT IDENTIFIER ::= { jnxSlotM40 5 }
    jnxSlotCoolingFan  	  OBJECT IDENTIFIER ::= { jnxSlotM40 6 }
    jnxSlotRoutingEngine  OBJECT IDENTIFIER ::= { jnxSlotM40 7 }

  jnxSlotM20	  	  OBJECT IDENTIFIER ::= { jnxSlot 2 }
    jnxM20SlotFPC	  OBJECT IDENTIFIER ::= { jnxSlotM20 1 }
			  -- Flexible Port Concentrator slot
    jnxM20SlotSSB	  OBJECT IDENTIFIER ::= { jnxSlotM20 2 }
			  -- System Switch Board slot
    jnxM20SlotRE	  OBJECT IDENTIFIER ::= { jnxSlotM20 3 }
			  -- Routing Engine slot
    jnxM20SlotPower	  OBJECT IDENTIFIER ::= { jnxSlotM20 4 }
    jnxM20SlotFan 	  OBJECT IDENTIFIER ::= { jnxSlotM20 5 }
    jnxM20SlotFrontPanel  OBJECT IDENTIFIER ::= { jnxSlotM20 6 }

  jnxSlotM160	  	  OBJECT IDENTIFIER ::= { jnxSlot 3 }
    jnxM160SlotFPC	  OBJECT IDENTIFIER ::= { jnxSlotM160 1 }
			  -- Flexible Port Concentrator slot
    jnxM160SlotSFM	  OBJECT IDENTIFIER ::= { jnxSlotM160 2 }
			  -- Switching and Forwarding Module slot
    jnxM160SlotHM	  OBJECT IDENTIFIER ::= { jnxSlotM160 3 }
			  -- Host Module (also called Routing Engine) slot
    jnxM160SlotPCG	  OBJECT IDENTIFIER ::= { jnxSlotM160 4 }
			  -- PFE Clock Generator slot
    jnxM160SlotPower	  OBJECT IDENTIFIER ::= { jnxSlotM160 5 }
    jnxM160SlotFan 	  OBJECT IDENTIFIER ::= { jnxSlotM160 6 }
    jnxM160SlotMCS 	  OBJECT IDENTIFIER ::= { jnxSlotM160 7 }
			  -- Miscellaneous Control System slot
    jnxM160SlotFPM        OBJECT IDENTIFIER ::= { jnxSlotM160 8 }
			  -- Front Panel Module
    jnxM160SlotCIP        OBJECT IDENTIFIER ::= { jnxSlotM160 9 }
			  -- Connector Interface Panel

  jnxSlotM10	  	  OBJECT IDENTIFIER ::= { jnxSlot 4 }
    jnxM10SlotFPC	  OBJECT IDENTIFIER ::= { jnxSlotM10 1 }
			  -- Flexible Port Concentrator slot
    jnxM10SlotFEB	  OBJECT IDENTIFIER ::= { jnxSlotM10 2 }
			  -- Forwarding Engine Board slot
    jnxM10SlotRE	  OBJECT IDENTIFIER ::= { jnxSlotM10 3 }
			  -- Routing Engine slot
    jnxM10SlotPower 	  OBJECT IDENTIFIER ::= { jnxSlotM10 4 }
    jnxM10SlotFan  	  OBJECT IDENTIFIER ::= { jnxSlotM10 5 }

  jnxSlotM5	  	  OBJECT IDENTIFIER ::= { jnxSlot 5 }
    jnxM5SlotFPC 	  OBJECT IDENTIFIER ::= { jnxSlotM5 1 }
			  -- Flexible Port Concentrator slot
    jnxM5SlotFEB	  OBJECT IDENTIFIER ::= { jnxSlotM5 2 }
			  -- Forwarding Engine Board slot
    jnxM5SlotRE 	  OBJECT IDENTIFIER ::= { jnxSlotM5 3 }
			  -- Routing Engine slot
    jnxM5SlotPower	  OBJECT IDENTIFIER ::= { jnxSlotM5 4 }
    jnxM5SlotFan   	  OBJECT IDENTIFIER ::= { jnxSlotM5 5 }

  jnxSlotT640	  	  OBJECT IDENTIFIER ::= { jnxSlot 6 }
    jnxT640SlotFPC	  OBJECT IDENTIFIER ::= { jnxSlotT640 1 }
			  -- Flexible Port Concentrator slot
    jnxT640SlotSIB	  OBJECT IDENTIFIER ::= { jnxSlotT640 2 }
			  -- Switch Interface Board slot
    jnxT640SlotHM	  OBJECT IDENTIFIER ::= { jnxSlotT640 3 }
			  -- Host Module (also called Routing Engine) slot
    jnxT640SlotSCG	  OBJECT IDENTIFIER ::= { jnxSlotT640 4 }
			  -- SONET Clock Generator slot
    jnxT640SlotPower	  OBJECT IDENTIFIER ::= { jnxSlotT640 5 }
    jnxT640SlotFan 	  OBJECT IDENTIFIER ::= { jnxSlotT640 6 }
    jnxT640SlotCB 	  OBJECT IDENTIFIER ::= { jnxSlotT640 7 }
			  -- Control Board slot
    jnxT640SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotT640 8 }
			  -- Front Panel Board
    jnxT640SlotCIP        OBJECT IDENTIFIER ::= { jnxSlotT640 9 }
			  -- Connector Interface Panel
    jnxT640SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotT640 10 }
			  -- Processor Mezzanine Board for SIB
    jnxT640SlotPSD        OBJECT IDENTIFIER ::= { jnxSlotT640 11 }
			  -- Protected System Domain slot

  jnxSlotT320	  	  OBJECT IDENTIFIER ::= { jnxSlot 7 }
    jnxT320SlotFPC	  OBJECT IDENTIFIER ::= { jnxSlotT320 1 }
			  -- Flexible Port Concentrator slot
    jnxT320SlotSIB	  OBJECT IDENTIFIER ::= { jnxSlotT320 2 }
			  -- Switch Interface Board slot
    jnxT320SlotHM	  OBJECT IDENTIFIER ::= { jnxSlotT320 3 }
			  -- Host Module (also called Routing Engine) slot
    jnxT320SlotSCG	  OBJECT IDENTIFIER ::= { jnxSlotT320 4 }
			  -- SONET Clock Generator slot
    jnxT320SlotPower	  OBJECT IDENTIFIER ::= { jnxSlotT320 5 }
    jnxT320SlotFan 	  OBJECT IDENTIFIER ::= { jnxSlotT320 6 }
    jnxT320SlotCB 	  OBJECT IDENTIFIER ::= { jnxSlotT320 7 }
			  -- Control Board slot
    jnxT320SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotT320 8 }
			  -- Front Panel Board
    jnxT320SlotCIP        OBJECT IDENTIFIER ::= { jnxSlotT320 9 }
			  -- Connector Interface Panel
    jnxT320SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotT320 10 }
			  -- Processor Mezzanine Board for SIB
    jnxT320SlotPSD        OBJECT IDENTIFIER ::= { jnxSlotT320 11 }
			  -- Protected System Domain slot

  jnxSlotM40e	  	  OBJECT IDENTIFIER ::= { jnxSlot 8 }
    jnxM40eSlotFPC	  OBJECT IDENTIFIER ::= { jnxSlotM40e 1 }
			  -- Flexible Port Concentrator slot
    jnxM40eSlotSFM	  OBJECT IDENTIFIER ::= { jnxSlotM40e 2 }
			  -- Switching and Forwarding Module slot
    jnxM40eSlotHM	  OBJECT IDENTIFIER ::= { jnxSlotM40e 3 }
			  -- Host Module (also called Routing Engine) slot
    jnxM40eSlotPCG	  OBJECT IDENTIFIER ::= { jnxSlotM40e 4 }
			  -- PFE Clock Generator slot
    jnxM40eSlotPower	  OBJECT IDENTIFIER ::= { jnxSlotM40e 5 }
    jnxM40eSlotFan	  OBJECT IDENTIFIER ::= { jnxSlotM40e 6 }
    jnxM40eSlotMCS	  OBJECT IDENTIFIER ::= { jnxSlotM40e 7 }
			  -- Miscellaneous Control System slot
    jnxM40eSlotFPM	  OBJECT IDENTIFIER ::= { jnxSlotM40e 8 }
			  -- Front Panel Module
    jnxM40eSlotCIP	  OBJECT IDENTIFIER ::= { jnxSlotM40e 9 }
			  -- Connector Interface Panel

-- Media Card Space, for holding Port Interface Card (PIC)

jnxMediaCardSpace   OBJECT IDENTIFIER ::= { jnxClassContainers 3 }

  jnxMediaCardSpaceM40	     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 1 }
    jnxMediaCardSpacePIC     OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM40 1 }

  jnxMediaCardSpaceM20	     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 2 }
    jnxM20MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM20 1 }

  jnxMediaCardSpaceM160	     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 3 }
    jnxM160MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM160 1 }

  jnxMediaCardSpaceM10       OBJECT IDENTIFIER ::= { jnxMediaCardSpace 4 }
    jnxM10MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM10 1 }

  jnxMediaCardSpaceM5	     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 5 }
    jnxM5MediaCardSpacePIC   OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM5 1 }

  jnxMediaCardSpaceT640      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 6 }
    jnxT640MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceT640 1 }

  jnxMediaCardSpaceT320      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 7 }
    jnxT320MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceT320 1 }

  jnxMediaCardSpaceM40e	     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 8 }
    jnxM40eMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM40e 1 }


-- Abstract Submodule Space, for holding other submodules

jnxSubSpace         OBJECT IDENTIFIER ::= { jnxClassContainers 4 }

   jnxSubSpaceM160        OBJECT IDENTIFIER ::= { jnxSubSpace 1 }
     jnxM160SubSpaceSFM   OBJECT IDENTIFIER ::= { jnxSubSpaceM160 1 }


--
-- Contents
--

-- Backplane/Midplane

jnxBackplane 	    OBJECT IDENTIFIER ::= { jnxClassContents 1 }
  jnxBackplaneM40 	OBJECT IDENTIFIER ::= { jnxBackplane 1 }
  jnxBackplaneM20 	OBJECT IDENTIFIER ::= { jnxBackplane 2 }
  jnxMidplaneM160 	OBJECT IDENTIFIER ::= { jnxBackplane 3 }
  jnxMidplaneM10 	OBJECT IDENTIFIER ::= { jnxBackplane 4 }
  jnxMidplaneM5  	OBJECT IDENTIFIER ::= { jnxBackplane 5 }
  jnxMidplaneT640  	OBJECT IDENTIFIER ::= { jnxBackplane 6 }
  jnxMidplaneT320  	OBJECT IDENTIFIER ::= { jnxBackplane 7 }
  jnxMidplaneM40e  	OBJECT IDENTIFIER ::= { jnxBackplane 8 }

-- Modules

jnxModule	    OBJECT IDENTIFIER ::= { jnxClassContents 2 }

  jnxModuleM40		OBJECT IDENTIFIER ::= { jnxModule 1 }

    jnxModuleSCB 	  OBJECT IDENTIFIER ::= { jnxModuleM40 1 }

    jnxModuleFPC	  OBJECT IDENTIFIER ::= { jnxModuleM40 2 }
      jnxCommonFPC        OBJECT IDENTIFIER ::= { jnxModuleFPC 1 }
      jnxOc48FPC          OBJECT IDENTIFIER ::= { jnxModuleFPC 2 }

    jnxModuleHostCtlr 	  OBJECT IDENTIFIER ::= { jnxModuleM40 3 }
      jnxHostCtlrMaxi     OBJECT IDENTIFIER ::= { jnxModuleHostCtlr 1 }
      jnxHostCtlrMini     OBJECT IDENTIFIER ::= { jnxModuleHostCtlr 2 }

    jnxModulePowerSupply  OBJECT IDENTIFIER ::= { jnxModuleM40 4 }
      jnxPowerSupplyAC    OBJECT IDENTIFIER ::= { jnxModulePowerSupply 1 }
      jnxPowerSupplyDC    OBJECT IDENTIFIER ::= { jnxModulePowerSupply 2 }

    jnxModuleCooling  	  OBJECT IDENTIFIER ::= { jnxModuleM40 5 }
      jnxCoolingImpeller  OBJECT IDENTIFIER ::= { jnxModuleCooling 1 }
      jnxCoolingFan	  OBJECT IDENTIFIER ::= { jnxModuleCooling 2 }

    jnxModuleFrontPanelDisplay OBJECT IDENTIFIER ::= { jnxModuleM40 6 }

    jnxModuleRoutingEngine     OBJECT IDENTIFIER ::= { jnxModuleM40 7 }

  jnxModuleM20		  OBJECT IDENTIFIER ::= { jnxModule 2 }
    jnxM20FPC	  	  OBJECT IDENTIFIER ::= { jnxModuleM20 1 }
			  -- Flexible Port Concentrator
    jnxM20SSB 	  	  OBJECT IDENTIFIER ::= { jnxModuleM20 2 }
			  -- System Switch Board
    jnxM20RE	 	  OBJECT IDENTIFIER ::= { jnxModuleM20 3 }
			  -- Routing Engine
    jnxM20Power	          OBJECT IDENTIFIER ::= { jnxModuleM20 4 }
      jnxM20PowerAC       OBJECT IDENTIFIER ::= { jnxM20Power 1 }
      jnxM20PowerDC       OBJECT IDENTIFIER ::= { jnxM20Power 2 }

    jnxM20Fan	  	  OBJECT IDENTIFIER ::= { jnxModuleM20 5 }
    jnxM20FrontPanel  	  OBJECT IDENTIFIER ::= { jnxModuleM20 6 }

  jnxModuleM160		  OBJECT IDENTIFIER ::= { jnxModule 3 }
    jnxM160FPC	  	  OBJECT IDENTIFIER ::= { jnxModuleM160 1 }
			  -- Flexible Port Concentrator
    jnxM160SFM 	  	  OBJECT IDENTIFIER ::= { jnxModuleM160 2 }
			  -- Switching and Forwarding Module
    jnxM160HM	 	  OBJECT IDENTIFIER ::= { jnxModuleM160 3 }
			  -- Host Module (also called Routing Engine)
    jnxM160PCG		  OBJECT IDENTIFIER ::= { jnxModuleM160 4 }
			  -- PFE Clock Generator
    jnxM160Power	  OBJECT IDENTIFIER ::= { jnxModuleM160 5 }
    jnxM160Fan  	  OBJECT IDENTIFIER ::= { jnxModuleM160 6 }
    jnxM160MCS  	  OBJECT IDENTIFIER ::= { jnxModuleM160 7 }
			  -- Miscellaneous Control System
    jnxM160FPM		  OBJECT IDENTIFIER ::= { jnxModuleM160 8 }
			  -- Front Panel Module
    jnxM160CIP		  OBJECT IDENTIFIER ::= { jnxModuleM160 9 }
			  -- Connector Interface Panel

  jnxModuleM10  	  OBJECT IDENTIFIER ::= { jnxModule 4 }
    jnxM10FPC		  OBJECT IDENTIFIER ::= { jnxModuleM10 1 }
			  -- Flexible Port Concentrator
    jnxM10FEB		  OBJECT IDENTIFIER ::= { jnxModuleM10 2 }
			  -- Forwarding Engine Board
    jnxM10RE		  OBJECT IDENTIFIER ::= { jnxModuleM10 3 }
			  -- Routing Engine
    jnxM10Power		  OBJECT IDENTIFIER ::= { jnxModuleM10 4 }
      jnxM10PowerAC       OBJECT IDENTIFIER ::= { jnxM10Power 1 }
      jnxM10PowerDC       OBJECT IDENTIFIER ::= { jnxM10Power 2 }

    jnxM10Fan    	  OBJECT IDENTIFIER ::= { jnxModuleM10 5 }

  jnxModuleM5		  OBJECT IDENTIFIER ::= { jnxModule 5 }
    jnxM5FPC		  OBJECT IDENTIFIER ::= { jnxModuleM5 1 }
			  -- Flexible Port Concentrator
    jnxM5FEB 		  OBJECT IDENTIFIER ::= { jnxModuleM5 2 }
			  -- Forwarding Engine Board
    jnxM5RE	 	  OBJECT IDENTIFIER ::= { jnxModuleM5 3 }
			  -- Routing Engine
    jnxM5Power  	  OBJECT IDENTIFIER ::= { jnxModuleM5 4 }
      jnxM5PowerAC        OBJECT IDENTIFIER ::= { jnxM5Power 1 }
      jnxM5PowerDC        OBJECT IDENTIFIER ::= { jnxM5Power 2 }

    jnxM5Fan     	  OBJECT IDENTIFIER ::= { jnxModuleM5 5 }


  jnxModuleT640	          OBJECT IDENTIFIER ::= { jnxModule 6 }
    jnxT640FPC  	  OBJECT IDENTIFIER ::= { jnxModuleT640 1 }
			  -- Flexible Port Concentrator
    jnxT640SIB  	  OBJECT IDENTIFIER ::= { jnxModuleT640 2 }
			  -- Swtich Interface Board
    jnxT640HM 	  	  OBJECT IDENTIFIER ::= { jnxModuleT640 3 }
			  -- Host Module (also called Routing Engine)
    jnxT640SCG	  	  OBJECT IDENTIFIER ::= { jnxModuleT640 4 }
			  -- SONET Clock Generator
    jnxT640Power	  OBJECT IDENTIFIER ::= { jnxModuleT640 5 }
    jnxT640Fan  	  OBJECT IDENTIFIER ::= { jnxModuleT640 6 }
    jnxT640CB  	  	  OBJECT IDENTIFIER ::= { jnxModuleT640 7 }
			  -- Control Board
    jnxT640FPB	  	  OBJECT IDENTIFIER ::= { jnxModuleT640 8 }
			  -- Front Panel Board
    jnxT640CIP	  	  OBJECT IDENTIFIER ::= { jnxModuleT640 9 }
			  -- Connector Interface Panel
    jnxT640SPMB	  	  OBJECT IDENTIFIER ::= { jnxModuleT640 10 }
			  -- Processor Mezzanine Board for SIB

  jnxModuleT320	          OBJECT IDENTIFIER ::= { jnxModule 7 }
    jnxT320FPC  	  OBJECT IDENTIFIER ::= { jnxModuleT320 1 }
			  -- Flexible Port Concentrator
    jnxT320SIB  	  OBJECT IDENTIFIER ::= { jnxModuleT320 2 }
			  -- Swtich Interface Board
    jnxT320HM 	  	  OBJECT IDENTIFIER ::= { jnxModuleT320 3 }
			  -- Host Module (also called Routing Engine)
    jnxT320SCG	  	  OBJECT IDENTIFIER ::= { jnxModuleT320 4 }
			  -- SONET Clock Generator
    jnxT320Power	  OBJECT IDENTIFIER ::= { jnxModuleT320 5 }
    jnxT320Fan  	  OBJECT IDENTIFIER ::= { jnxModuleT320 6 }
    jnxT320CB  	  	  OBJECT IDENTIFIER ::= { jnxModuleT320 7 }
			  -- Control Board
    jnxT320FPB	  	  OBJECT IDENTIFIER ::= { jnxModuleT320 8 }
			  -- Front Panel Board
    jnxT320CIP	  	  OBJECT IDENTIFIER ::= { jnxModuleT320 9 }
			  -- Connector Interface Panel
    jnxT320SPMB	  	  OBJECT IDENTIFIER ::= { jnxModuleT320 10 }
			  -- Processor Mezzanine Board for SIB

  jnxModuleM40e 	  OBJECT IDENTIFIER ::= { jnxModule 8 }
    jnxM40eFPC  	  OBJECT IDENTIFIER ::= { jnxModuleM40e 1 }
			  -- Flexible Port Concentrator
    jnxM40eSFM  	  OBJECT IDENTIFIER ::= { jnxModuleM40e 2 }
			  -- Switching and Forwarding Module
    jnxM40eHM   	  OBJECT IDENTIFIER ::= { jnxModuleM40e 3 }
			  -- Host Module (also called Routing Engine)
    jnxM40ePCG  	  OBJECT IDENTIFIER ::= { jnxModuleM40e 4 }
			  -- PFE Clock Generator
    jnxM40ePower	  OBJECT IDENTIFIER ::= { jnxModuleM40e 5 }
    jnxM40eFan  	  OBJECT IDENTIFIER ::= { jnxModuleM40e 6 }
    jnxM40eMCS  	  OBJECT IDENTIFIER ::= { jnxModuleM40e 7 }
			  -- Miscellaneous Control System
    jnxM40eFPM  	  OBJECT IDENTIFIER ::= { jnxModuleM40e 8 }
			  -- Front Panel Module
    jnxM40eCIP  	  OBJECT IDENTIFIER ::= { jnxModuleM40e 9 }



-- Submodules

jnxSubmodule 	          OBJECT IDENTIFIER ::= { jnxClassContents 3 }
  jnxSubmoduleM40	  OBJECT IDENTIFIER ::= { jnxSubmodule 1 }

--  ::= { jnxSubmoduleM40 1 }		This OID is obsolete.

    jnxM40PIC0		  OBJECT IDENTIFIER ::= { jnxSubmoduleM40 2 }
			  -- This is the quad-height PIC which takes
			  -- up the whole FPC slot of the M40.

      jnxM40SonetOc48 	  OBJECT IDENTIFIER ::= { jnxM40PIC0 1 }

    jnxM40PIC		  OBJECT IDENTIFIER ::= { jnxSubmoduleM40 3 }
			  -- This is the regular PIC of M40.

      jnxM40QuadSonetOc3    OBJECT IDENTIFIER ::= { jnxM40PIC 1 }
      jnxM40SonetOc12 	    OBJECT IDENTIFIER ::= { jnxM40PIC 2 }
      jnxM40GigEther 	    OBJECT IDENTIFIER ::= { jnxM40PIC 3 }
      jnxM40QuadT3 	    OBJECT IDENTIFIER ::= { jnxM40PIC 4 }
      jnxM40QuadE3 	    OBJECT IDENTIFIER ::= { jnxM40PIC 5 }
      jnxM40DualAtmOc3 	    OBJECT IDENTIFIER ::= { jnxM40PIC 6 }
      jnxM40AtmOc12 	    OBJECT IDENTIFIER ::= { jnxM40PIC 7 }
      jnxM40Tunnel 	    OBJECT IDENTIFIER ::= { jnxM40PIC 8 }
      jnxM40ChOc12toDs3	    OBJECT IDENTIFIER ::= { jnxM40PIC 9 }
      jnxM40QuadEther	    OBJECT IDENTIFIER ::= { jnxM40PIC 10 }
      jnxM40QuadE1          OBJECT IDENTIFIER ::= { jnxM40PIC 11 }
      jnxM40QuadT1          OBJECT IDENTIFIER ::= { jnxM40PIC 12 }
      jnxM40SonetOc48Sr     OBJECT IDENTIFIER ::= { jnxM40PIC 13 }
      jnxM40QuadChT3        OBJECT IDENTIFIER ::= { jnxM40PIC 14 }
      jnxM40SonetOc48Lr     OBJECT IDENTIFIER ::= { jnxM40PIC 15 }
      jnxM40QuadAtmE3       OBJECT IDENTIFIER ::= { jnxM40PIC 16 }
      jnxM40QuadAtmT3       OBJECT IDENTIFIER ::= { jnxM40PIC 17 }
      jnxM40GigEtherBundle  OBJECT IDENTIFIER ::= { jnxM40PIC 18 }
      jnxM40Multilink128    OBJECT IDENTIFIER ::= { jnxM40PIC 19 }
      jnxM40Multilink32     OBJECT IDENTIFIER ::= { jnxM40PIC 20 }
      jnxM40Multilink4      OBJECT IDENTIFIER ::= { jnxM40PIC 21 }
      jnxM40ChStm1          OBJECT IDENTIFIER ::= { jnxM40PIC 22 }
      jnxM40DenseEther12    OBJECT IDENTIFIER ::= { jnxM40PIC 24 }
			    -- 12-port Fast Ethernet
      jnxM40DecaChE1        OBJECT IDENTIFIER ::= { jnxM40PIC 25 }
			    -- 10-port channelized E1
      jnxM40ChDs3toDs0      OBJECT IDENTIFIER ::= { jnxM40PIC 26 }
      jnxM40DualChDs3toDs0  OBJECT IDENTIFIER ::= { jnxM40PIC 27 }
      jnxM40DenseEther8     OBJECT IDENTIFIER ::= { jnxM40PIC 28 }
			    -- 8-port Fast Ethernet
      jnxM40Crypto800       OBJECT IDENTIFIER ::= { jnxM40PIC 30 }
      jnxM40LsMultilink128  OBJECT IDENTIFIER ::= { jnxM40PIC 32 }
      jnxM40LsMultilink32   OBJECT IDENTIFIER ::= { jnxM40PIC 33 }
      jnxM40LsMultilink4    OBJECT IDENTIFIER ::= { jnxM40PIC 34 }
      jnxM40AtmIIOc12 	    OBJECT IDENTIFIER ::= { jnxM40PIC 35 }
      jnxM40DualAtmIIOc3    OBJECT IDENTIFIER ::= { jnxM40PIC 36 }
      jnxM40DualQChDS3      OBJECT IDENTIFIER ::= { jnxM40PIC 37 }
      jnxM40QuadQChT3       OBJECT IDENTIFIER ::= { jnxM40PIC 38 }
      jnxM40QChOc12         OBJECT IDENTIFIER ::= { jnxM40PIC 39 }
      jnxM40QChStm1         OBJECT IDENTIFIER ::= { jnxM40PIC 40 }
      jnxM40DualQChStm1     OBJECT IDENTIFIER ::= { jnxM40PIC 41 }
      jnxM40DecaQChE1       OBJECT IDENTIFIER ::= { jnxM40PIC 42 }
      jnxM40DualEIA530      OBJECT IDENTIFIER ::= { jnxM40PIC 43 }
      jnxM40DecaQChT1       OBJECT IDENTIFIER ::= { jnxM40PIC 44 }

  jnxSubmoduleM20	    OBJECT IDENTIFIER ::= { jnxSubmodule 2 }

    jnxM20PIC0		    OBJECT IDENTIFIER ::= { jnxSubmoduleM20 1 }
			    -- This is the quad-height PIC which takes
			    -- up the whole FPC slot of the M20.
      jnxM20SonetOc48 	    OBJECT IDENTIFIER ::= { jnxM20PIC0 1 }

    jnxM20PIC		    OBJECT IDENTIFIER ::= { jnxSubmoduleM20 2 }
			    -- This is the regular PIC of M20.

      jnxM20QuadSonetOc3    OBJECT IDENTIFIER ::= { jnxM20PIC 1 }
      jnxM20SonetOc12 	    OBJECT IDENTIFIER ::= { jnxM20PIC 2 }
      jnxM20GigEther 	    OBJECT IDENTIFIER ::= { jnxM20PIC 3 }
      jnxM20QuadT3 	    OBJECT IDENTIFIER ::= { jnxM20PIC 4 }
      jnxM20QuadE3 	    OBJECT IDENTIFIER ::= { jnxM20PIC 5 }
      jnxM20DualAtmOc3 	    OBJECT IDENTIFIER ::= { jnxM20PIC 6 }
      jnxM20AtmOc12 	    OBJECT IDENTIFIER ::= { jnxM20PIC 7 }
      jnxM20Tunnel 	    OBJECT IDENTIFIER ::= { jnxM20PIC 8 }
      jnxM20ChOc12toDs3	    OBJECT IDENTIFIER ::= { jnxM20PIC 9 }
      jnxM20QuadEther	    OBJECT IDENTIFIER ::= { jnxM20PIC 10 }
      jnxM20QuadE1          OBJECT IDENTIFIER ::= { jnxM20PIC 11 }
      jnxM20QuadT1          OBJECT IDENTIFIER ::= { jnxM20PIC 12 }
      jnxM20SonetOc48Sr     OBJECT IDENTIFIER ::= { jnxM20PIC 13 }
      jnxM20QuadChT3        OBJECT IDENTIFIER ::= { jnxM20PIC 14 }
      jnxM20SonetOc48Lr     OBJECT IDENTIFIER ::= { jnxM20PIC 15 }
      jnxM20QuadAtmE3       OBJECT IDENTIFIER ::= { jnxM20PIC 16 }
      jnxM20QuadAtmT3       OBJECT IDENTIFIER ::= { jnxM20PIC 17 }
      jnxM20GigEtherBundle  OBJECT IDENTIFIER ::= { jnxM20PIC 18 }
      jnxM20Multilink128    OBJECT IDENTIFIER ::= { jnxM20PIC 19 }
      jnxM20Multilink32     OBJECT IDENTIFIER ::= { jnxM20PIC 20 }
      jnxM20Multilink4      OBJECT IDENTIFIER ::= { jnxM20PIC 21 }
      jnxM20ChStm1          OBJECT IDENTIFIER ::= { jnxM20PIC 22 }
      jnxM20DenseEther12    OBJECT IDENTIFIER ::= { jnxM20PIC 24 }
			    -- 12-port Fast Ethernet
      jnxM20DecaChE1        OBJECT IDENTIFIER ::= { jnxM20PIC 25 }
			    -- 10-port channelized E1
      jnxM20ChDs3toDs0      OBJECT IDENTIFIER ::= { jnxM20PIC 26 }
      jnxM20DualChDs3toDs0  OBJECT IDENTIFIER ::= { jnxM20PIC 27 }
      jnxM20DenseEther8     OBJECT IDENTIFIER ::= { jnxM20PIC 28 }
			    -- 8-port Fast Ethernet
      jnxM20Crypto800       OBJECT IDENTIFIER ::= { jnxM20PIC 30 }
      jnxM20GgsnControl     OBJECT IDENTIFIER ::= { jnxM20PIC 31 }
      jnxM20GgsnData        OBJECT IDENTIFIER ::= { jnxM20PIC 32 }
      jnxM20LsMultilink128  OBJECT IDENTIFIER ::= { jnxM20PIC 34 }
      jnxM20LsMultilink32   OBJECT IDENTIFIER ::= { jnxM20PIC 35 }
      jnxM20LsMultilink4    OBJECT IDENTIFIER ::= { jnxM20PIC 36 }
      jnxM20AtmIIOc12 	    OBJECT IDENTIFIER ::= { jnxM20PIC 37 }
      jnxM20DualAtmIIOc3    OBJECT IDENTIFIER ::= { jnxM20PIC 38 }
      jnxM20DualQChDS3      OBJECT IDENTIFIER ::= { jnxM20PIC 39 }
      jnxM20QuadQChT3       OBJECT IDENTIFIER ::= { jnxM20PIC 40 }
      jnxM20QChOc12         OBJECT IDENTIFIER ::= { jnxM20PIC 41 }
      jnxM20QChStm1         OBJECT IDENTIFIER ::= { jnxM20PIC 42 }
      jnxM20DualQChStm1     OBJECT IDENTIFIER ::= { jnxM20PIC 43 }
      jnxM20DecaQChE1       OBJECT IDENTIFIER ::= { jnxM20PIC 44 }
      jnxM20DualEIA530      OBJECT IDENTIFIER ::= { jnxM20PIC 45 }
      jnxM20PassiveMonitor  OBJECT IDENTIFIER ::= { jnxM20PIC 46 }
      jnxM20DecaQChT1       OBJECT IDENTIFIER ::= { jnxM20PIC 47 }

  jnxSubmoduleM160	    OBJECT IDENTIFIER ::= { jnxSubmodule 3 }

--  ::= { jnxSubmoduleM160 1 }		This OID is obsolete.

    jnxM160SubSFM  	    OBJECT IDENTIFIER ::= { jnxSubmoduleM160 2 }
      jnxM160SPP            OBJECT IDENTIFIER ::= { jnxM160SubSFM 1 }
			    -- Switch Plane Processor
      jnxM160SPR            OBJECT IDENTIFIER ::= { jnxM160SubSFM 2 }
			    -- Switch Plane Router

    jnxM160SubFPM	    OBJECT IDENTIFIER ::= { jnxSubmoduleM160 3 }
      jnxM160FPMCMB         OBJECT IDENTIFIER ::= { jnxM160SubFPM 1 }
			    -- CMB part of FPM
      jnxM160FPMDisplay     OBJECT IDENTIFIER ::= { jnxM160SubFPM 2 }
			    -- Display part of FPM

    jnxM160PIC0	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleM160 4 }
			    -- This is the quad-height PIC which takes
			    -- up the whole FPC slot of the M160.

      jnxM160SonetOc192Sr   OBJECT IDENTIFIER ::= { jnxM160PIC0 1 }
      jnxM160SonetOc192Sr2  OBJECT IDENTIFIER ::= { jnxM160PIC0 2 }
      jnxM160SonetOc192Lr1  OBJECT IDENTIFIER ::= { jnxM160PIC0 3 }

    jnxM160PIC1	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleM160 5 }
			    -- This is FPC type 1 of PIC.

      jnxM160QuadSonetOc3   OBJECT IDENTIFIER ::= { jnxM160PIC1 1 }
      jnxM160SonetOc12 	    OBJECT IDENTIFIER ::= { jnxM160PIC1 2 }
      jnxM160GigEther 	    OBJECT IDENTIFIER ::= { jnxM160PIC1 3 }
      jnxM160QuadT3 	    OBJECT IDENTIFIER ::= { jnxM160PIC1 4 }
      jnxM160QuadE3 	    OBJECT IDENTIFIER ::= { jnxM160PIC1 5 }
      jnxM160DualAtmOc3	    OBJECT IDENTIFIER ::= { jnxM160PIC1 6 }
      jnxM160AtmOc12 	    OBJECT IDENTIFIER ::= { jnxM160PIC1 7 }
      jnxM160ChOc12toDs3    OBJECT IDENTIFIER ::= { jnxM160PIC1 8 }
      jnxM160QuadEther	    OBJECT IDENTIFIER ::= { jnxM160PIC1 9 }
      jnxM160QuadE1         OBJECT IDENTIFIER ::= { jnxM160PIC1 10 }
      jnxM160QuadT1         OBJECT IDENTIFIER ::= { jnxM160PIC1 11 }
      jnxM160QuadChT3       OBJECT IDENTIFIER ::= { jnxM160PIC1 12 }
      jnxM160QuadAtmE3      OBJECT IDENTIFIER ::= { jnxM160PIC1 13 }
      jnxM160QuadAtmT3      OBJECT IDENTIFIER ::= { jnxM160PIC1 14 }
      jnxM160GigEtherBundle OBJECT IDENTIFIER ::= { jnxM160PIC1 15 }
      jnxM160ChStm1         OBJECT IDENTIFIER ::= { jnxM160PIC1 16 }
      jnxM160DecaChE1       OBJECT IDENTIFIER ::= { jnxM160PIC1 17 }
			    -- 10-port channelized E1
      jnxM160ChDs3toDs0     OBJECT IDENTIFIER ::= { jnxM160PIC1 18 }
      jnxM160DualChDs3toDs0 OBJECT IDENTIFIER ::= { jnxM160PIC1 19 }
      jnxM160DenseEther8    OBJECT IDENTIFIER ::= { jnxM160PIC1 20 }
			    -- 8-port Fast Ethernet
      jnxM160AtmIIOc12 	    OBJECT IDENTIFIER ::= { jnxM160PIC1 23 }
      jnxM160DualAtmIIOc3   OBJECT IDENTIFIER ::= { jnxM160PIC1 24 }
      jnxM160DualQChDS3     OBJECT IDENTIFIER ::= { jnxM160PIC1 25 }
      jnxM160QuadQChT3      OBJECT IDENTIFIER ::= { jnxM160PIC1 26 }
      jnxM160QChOc12        OBJECT IDENTIFIER ::= { jnxM160PIC1 27 }
      jnxM160QChStm1        OBJECT IDENTIFIER ::= { jnxM160PIC1 28 }
      jnxM160DualQChStm1    OBJECT IDENTIFIER ::= { jnxM160PIC1 29 }
      jnxM160DecaQChE1      OBJECT IDENTIFIER ::= { jnxM160PIC1 30 }
      jnxM160DualEIA530     OBJECT IDENTIFIER ::= { jnxM160PIC1 31 }
      jnxM160PassiveMonitor OBJECT IDENTIFIER ::= { jnxM160PIC1 32 }
      jnxM160DecaQChT1      OBJECT IDENTIFIER ::= { jnxM160PIC1 33 }


    jnxM160PIC2	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleM160 6 }
			    -- This is FPC type 2 of PIC.

      jnxM160SonetOc48Sr    OBJECT IDENTIFIER ::= { jnxM160PIC2 1 }
      jnxM160Tunnel         OBJECT IDENTIFIER ::= { jnxM160PIC2 2 }
      jnxM160DualGigEther   OBJECT IDENTIFIER ::= { jnxM160PIC2 3 }
      jnxM160QuadSonetOc12  OBJECT IDENTIFIER ::= { jnxM160PIC2 4 }
      jnxM160SonetOc48Lr    OBJECT IDENTIFIER ::= { jnxM160PIC2 5 }
      jnxM160DenseEther48   OBJECT IDENTIFIER ::= { jnxM160PIC2 6 }
			    -- 48-port Fast Ethernet
      jnxM160QuadGigEther   OBJECT IDENTIFIER ::= { jnxM160PIC2 7 }
      jnxM160Crypto800      OBJECT IDENTIFIER ::= { jnxM160PIC2 9 }
      jnxM160QuadOc3        OBJECT IDENTIFIER ::= { jnxM160PIC2 10 }
      jnxM160DualQHGE       OBJECT IDENTIFIER ::= { jnxM160PIC2 11 }
      jnxM160DualAtmIIOc12  OBJECT IDENTIFIER ::= { jnxM160PIC2 12 }


  jnxSubmoduleM10	    OBJECT IDENTIFIER ::= { jnxSubmodule 4 }

    jnxM10PIC		    OBJECT IDENTIFIER ::= { jnxSubmoduleM10 1 }

      jnxM10QuadSonetOc3    OBJECT IDENTIFIER ::= { jnxM10PIC 1 }
      jnxM10SonetOc12 	    OBJECT IDENTIFIER ::= { jnxM10PIC 2 }
      jnxM10GigEther 	    OBJECT IDENTIFIER ::= { jnxM10PIC 3 }
      jnxM10QuadT3 	    OBJECT IDENTIFIER ::= { jnxM10PIC 4 }
      jnxM10QuadE3 	    OBJECT IDENTIFIER ::= { jnxM10PIC 5 }
      jnxM10DualAtmOc3 	    OBJECT IDENTIFIER ::= { jnxM10PIC 6 }
      jnxM10AtmOc12 	    OBJECT IDENTIFIER ::= { jnxM10PIC 7 }
      jnxM10Tunnel 	    OBJECT IDENTIFIER ::= { jnxM10PIC 8 }
      jnxM10ChOc12toDs3	    OBJECT IDENTIFIER ::= { jnxM10PIC 9 }
      jnxM10QuadEther	    OBJECT IDENTIFIER ::= { jnxM10PIC 10 }
      jnxM10QuadE1          OBJECT IDENTIFIER ::= { jnxM10PIC 11 }
      jnxM10QuadT1          OBJECT IDENTIFIER ::= { jnxM10PIC 12 }
      jnxM10SonetOc48Sr     OBJECT IDENTIFIER ::= { jnxM10PIC 13 }
      jnxM10QuadChT3        OBJECT IDENTIFIER ::= { jnxM10PIC 14 }
      jnxM10SonetOc48Lr     OBJECT IDENTIFIER ::= { jnxM10PIC 15 }
      jnxM10QuadAtmE3       OBJECT IDENTIFIER ::= { jnxM10PIC 16 }
      jnxM10QuadAtmT3       OBJECT IDENTIFIER ::= { jnxM10PIC 17 }
      jnxM10GigEtherBundle  OBJECT IDENTIFIER ::= { jnxM10PIC 18 }
      jnxM10Multilink128    OBJECT IDENTIFIER ::= { jnxM10PIC 19 }
      jnxM10Multilink32     OBJECT IDENTIFIER ::= { jnxM10PIC 20 }
      jnxM10Multilink4      OBJECT IDENTIFIER ::= { jnxM10PIC 21 }
      jnxM10ChStm1          OBJECT IDENTIFIER ::= { jnxM10PIC 22 }
      jnxM10DualChDs3       OBJECT IDENTIFIER ::= { jnxM10PIC 23 }
      jnxM10DualDs3         OBJECT IDENTIFIER ::= { jnxM10PIC 24 }
      jnxM10DualSonetOc3    OBJECT IDENTIFIER ::= { jnxM10PIC 25 }
      jnxM10DualE3          OBJECT IDENTIFIER ::= { jnxM10PIC 26 }
      jnxM10DenseEther12
			    OBJECT IDENTIFIER ::= { jnxM10PIC 28 }
			    -- 12-port Fast Ethernet
      jnxM10DecaChE1        OBJECT IDENTIFIER ::= { jnxM10PIC 29 }
			    -- 10-port channelized E1
      jnxM10ChDs3toDs0      OBJECT IDENTIFIER ::= { jnxM10PIC 30 }
      jnxM10DualChDs3toDs0  OBJECT IDENTIFIER ::= { jnxM10PIC 31 }
      jnxM10DenseEther8     OBJECT IDENTIFIER ::= { jnxM10PIC 32 }
			    -- 8-port Fast Ethernet
      jnxM10Crypto800       OBJECT IDENTIFIER ::= { jnxM10PIC 34 }
      jnxM10LsMultilink128  OBJECT IDENTIFIER ::= { jnxM10PIC 36 }
      jnxM10LsMultilink32   OBJECT IDENTIFIER ::= { jnxM10PIC 37 }
      jnxM10LsMultilink4    OBJECT IDENTIFIER ::= { jnxM10PIC 38 }
      jnxM10AtmIIOc12 	    OBJECT IDENTIFIER ::= { jnxM10PIC 39 }
      jnxM10DualAtmIIOc3    OBJECT IDENTIFIER ::= { jnxM10PIC 40 }
      jnxM10DualQChDs3      OBJECT IDENTIFIER ::= { jnxM10PIC 41 }
      jnxM10QuadQChT3       OBJECT IDENTIFIER ::= { jnxM10PIC 42 }
      jnxM10QChOc12         OBJECT IDENTIFIER ::= { jnxM10PIC 43 }
      jnxM10QChStm1         OBJECT IDENTIFIER ::= { jnxM10PIC 44 }
      jnxM10DualQChStm1     OBJECT IDENTIFIER ::= { jnxM10PIC 45 }
      jnxM10DecaQChE1       OBJECT IDENTIFIER ::= { jnxM10PIC 46 }
      jnxM10DualEIA530      OBJECT IDENTIFIER ::= { jnxM10PIC 47 }
      jnxM10DecaQChT1       OBJECT IDENTIFIER ::= { jnxM10PIC 48 }


  jnxSubmoduleM5	OBJECT IDENTIFIER ::= { jnxSubmodule 5 }

    jnxM5PIC		  OBJECT IDENTIFIER ::= { jnxSubmoduleM5 1 }

      jnxM5QuadSonetOc3     OBJECT IDENTIFIER ::= { jnxM5PIC 1 }
      jnxM5SonetOc12 	    OBJECT IDENTIFIER ::= { jnxM5PIC 2 }
      jnxM5GigEther 	    OBJECT IDENTIFIER ::= { jnxM5PIC 3 }
      jnxM5QuadT3 	    OBJECT IDENTIFIER ::= { jnxM5PIC 4 }
      jnxM5QuadE3 	    OBJECT IDENTIFIER ::= { jnxM5PIC 5 }
      jnxM5DualAtmOc3 	    OBJECT IDENTIFIER ::= { jnxM5PIC 6 }
      jnxM5AtmOc12 	    OBJECT IDENTIFIER ::= { jnxM5PIC 7 }
      jnxM5Tunnel 	    OBJECT IDENTIFIER ::= { jnxM5PIC 8 }
      jnxM5ChOc12toDs3	    OBJECT IDENTIFIER ::= { jnxM5PIC 9 }
      jnxM5QuadEther	    OBJECT IDENTIFIER ::= { jnxM5PIC 10 }
      jnxM5QuadE1           OBJECT IDENTIFIER ::= { jnxM5PIC 11 }
      jnxM5QuadT1           OBJECT IDENTIFIER ::= { jnxM5PIC 12 }
      jnxM5QuadChT3         OBJECT IDENTIFIER ::= { jnxM5PIC 14 }
      jnxM5QuadAtmE3        OBJECT IDENTIFIER ::= { jnxM5PIC 16 }
      jnxM5QuadAtmT3        OBJECT IDENTIFIER ::= { jnxM5PIC 17 }
      jnxM5GigEtherBundle   OBJECT IDENTIFIER ::= { jnxM5PIC 18 }
      jnxM5Multilink128     OBJECT IDENTIFIER ::= { jnxM5PIC 19 }
      jnxM5Multilink32      OBJECT IDENTIFIER ::= { jnxM5PIC 20 }
      jnxM5Multilink4       OBJECT IDENTIFIER ::= { jnxM5PIC 21 }
      jnxM5ChStm1           OBJECT IDENTIFIER ::= { jnxM5PIC 22 }
      jnxM5DualChDs3        OBJECT IDENTIFIER ::= { jnxM5PIC 23 }
      jnxM5DualDs3          OBJECT IDENTIFIER ::= { jnxM5PIC 24 }
      jnxM5DualSonetOc3     OBJECT IDENTIFIER ::= { jnxM5PIC 25 }
      jnxM5DualE3           OBJECT IDENTIFIER ::= { jnxM5PIC 26 }
      jnxM5DenseEther12	    OBJECT IDENTIFIER ::= { jnxM5PIC 28 }
			    -- 12-port Fast Ethernet
      jnxM5DecaChE1         OBJECT IDENTIFIER ::= { jnxM5PIC 29 }
			    -- 10-port channelized E1
      jnxM5ChDs3toDs0       OBJECT IDENTIFIER ::= { jnxM5PIC 30 }
      jnxM5DualChDs3toDs0   OBJECT IDENTIFIER ::= { jnxM5PIC 31 }
      jnxM5DenseEther8 	    OBJECT IDENTIFIER ::= { jnxM5PIC 32 }
			    -- 8-port Fast Ethernet
      jnxM5Crypto800        OBJECT IDENTIFIER ::= { jnxM5PIC 34 }
      jnxM5LsMultilink128   OBJECT IDENTIFIER ::= { jnxM5PIC 36 }
      jnxM5LsMultilink32    OBJECT IDENTIFIER ::= { jnxM5PIC 37 }
      jnxM5LsMultilink4     OBJECT IDENTIFIER ::= { jnxM5PIC 38 }
      jnxM5AtmIIOc12 	    OBJECT IDENTIFIER ::= { jnxM5PIC 39 }
      jnxM5DualAtmIIOc3     OBJECT IDENTIFIER ::= { jnxM5PIC 40 }
      jnxM5DualQChDs3       OBJECT IDENTIFIER ::= { jnxM5PIC 41 }
      jnxM5QuadQChT3        OBJECT IDENTIFIER ::= { jnxM5PIC 42 }
      jnxM5QChOc12          OBJECT IDENTIFIER ::= { jnxM5PIC 43 }
      jnxM5QChStm1          OBJECT IDENTIFIER ::= { jnxM5PIC 44 }
      jnxM5DualQChStm1      OBJECT IDENTIFIER ::= { jnxM5PIC 45 }
      jnxM5DecaQChE1        OBJECT IDENTIFIER ::= { jnxM5PIC 46 }
      jnxM5DualEIA530       OBJECT IDENTIFIER ::= { jnxM5PIC 47 }
      jnxM5DecaQChT1        OBJECT IDENTIFIER ::= { jnxM5PIC 48 }


  jnxSubmoduleT640	    OBJECT IDENTIFIER ::= { jnxSubmodule 6 }

    jnxT640PIC0  	    OBJECT IDENTIFIER ::= { jnxSubmoduleT640 1 }
			    -- This is the quad-height PIC which takes
			    -- up the whole FPC slot of the T640.

    jnxT640PIC1	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleT640 2 }
			    -- This is FPC type 1 (M40 native) of PIC.

    jnxT640PIC2	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleT640 3 }
			    -- This is FPC type 2 (M160 native) of PIC.
      jnxT640DualGigEther   OBJECT IDENTIFIER ::= { jnxT640PIC2 1 }
      jnxT640QuadGigEther   OBJECT IDENTIFIER ::= { jnxT640PIC2 2 }
      jnxT640QuadSonetOc12  OBJECT IDENTIFIER ::= { jnxT640PIC2 3 }
      jnxT640SonetOc48Sr    OBJECT IDENTIFIER ::= { jnxT640PIC2 4 }
      jnxT640SonetOc48Lr    OBJECT IDENTIFIER ::= { jnxT640PIC2 5 }
      jnxT640DualAtmIIOc12  OBJECT IDENTIFIER ::= { jnxT640PIC2 6 }
      jnxT640QuadOc3        OBJECT IDENTIFIER ::= { jnxT640PIC2 7 }
      jnxT640DualQHGE       OBJECT IDENTIFIER ::= { jnxT640PIC2 8 }

    jnxT640PIC3	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleT640 4 }
			    -- This is FPC type 3 (T640 native) of PIC.
      jnxT640SonetOc192Sr2  OBJECT IDENTIFIER ::= { jnxT640PIC3 1 }
      jnxT640Tunnel         OBJECT IDENTIFIER ::= { jnxT640PIC3 2 }
      jnxT640QuadSonetOc48  OBJECT IDENTIFIER ::= { jnxT640PIC3 3 }
      jnxT640SonetOc192Vsr  OBJECT IDENTIFIER ::= { jnxT640PIC3 4 }
      jnxT640SonetOc192Lr   OBJECT IDENTIFIER ::= { jnxT640PIC3 5 }
      jnxT640TenGigEther    OBJECT IDENTIFIER ::= { jnxT640PIC3 6 }
      jnxT640NX1GigEther    OBJECT IDENTIFIER ::= { jnxT640PIC3 7 }

  jnxSubmoduleT320	    OBJECT IDENTIFIER ::= { jnxSubmodule 7 }

    jnxT320PIC0  	    OBJECT IDENTIFIER ::= { jnxSubmoduleT320 1 }

    jnxT320PIC1	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleT320 2 }
      jnxT320DualAtmIIOc3   OBJECT IDENTIFIER ::= { jnxT320PIC1 1 }

      -- { jnxT320PIC1 2 } is reserved

      jnxT320QuadSonetOc3   OBJECT IDENTIFIER ::= { jnxT320PIC1 3 }
      jnxT320DualAtmOc3     OBJECT IDENTIFIER ::= { jnxT320PIC1 4 }
      jnxT320AtmOc12        OBJECT IDENTIFIER ::= { jnxT320PIC1 5 }
      jnxT320QuadEther      OBJECT IDENTIFIER ::= { jnxT320PIC1 6 }
      jnxT320SonetOc12      OBJECT IDENTIFIER ::= { jnxT320PIC1 7 }
      jnxT320AtmIIOc12      OBJECT IDENTIFIER ::= { jnxT320PIC1 8 }

    jnxT320PIC2	  	    OBJECT IDENTIFIER ::= { jnxSubmoduleT320 3 }
			  -- This is FPC type 2 (M160 native) of PIC.

      jnxT320DualGigEther   OBJECT IDENTIFIER ::= { jnxT320PIC2 1 }
      jnxT320QuadGigEther   OBJECT IDENTIFIER ::= { jnxT320PIC2 2 }
      jnxT320QuadSonetOc12  OBJECT IDENTIFIER ::= { jnxT320PIC2 3 }
      jnxT320SonetOc48Sr    OBJECT IDENTIFIER ::= { jnxT320PIC2 4 }
      jnxT320SonetOc48Lr    OBJECT IDENTIFIER ::= { jnxT320PIC2 5 }
      jnxT320DualAtmIIOc12  OBJECT IDENTIFIER ::= { jnxT320PIC2 6 }
      jnxT320QuadOc3        OBJECT IDENTIFIER ::= { jnxT320PIC2 7 }
      jnxT320DualQHGE       OBJECT IDENTIFIER ::= { jnxT320PIC2 8 }

    jnxT320PIC3	  	  OBJECT IDENTIFIER ::= { jnxSubmoduleT320 4 }
			  -- This is FPC type 3 (T320 native) of PIC.

      jnxT320SonetOc192Sr2  OBJECT IDENTIFIER ::= { jnxT320PIC3 1 }
      jnxT320Tunnel         OBJECT IDENTIFIER ::= { jnxT320PIC3 2 }
      jnxT320QuadSonetOc48  OBJECT IDENTIFIER ::= { jnxT320PIC3 3 }
      jnxT320SonetOc192Vsr  OBJECT IDENTIFIER ::= { jnxT320PIC3 4 }
      jnxT320SonetOc192Lr   OBJECT IDENTIFIER ::= { jnxT320PIC3 5 }
      jnxT320TenGigEther    OBJECT IDENTIFIER ::= { jnxT320PIC3 6 }
      jnxT320NX1GigEther    OBJECT IDENTIFIER ::= { jnxT320PIC3 7 }

  jnxSubmoduleM40e          OBJECT IDENTIFIER ::= { jnxSubmodule 8 }

    jnxM40eSubSFM           OBJECT IDENTIFIER ::= { jnxSubmoduleM40e 2 }

      jnxM40eSPP            OBJECT IDENTIFIER ::= { jnxM40eSubSFM 1 }
                            -- Switch Plane Processor
      jnxM40eSPR            OBJECT IDENTIFIER ::= { jnxM40eSubSFM 2 }
                            -- Switch Plane Router

    jnxM40eSubFPM           OBJECT IDENTIFIER ::= { jnxSubmoduleM40e 3 }
      jnxM40eFPMCMB         OBJECT IDENTIFIER ::= { jnxM40eSubFPM 1 }
                            -- CMB part of FPM
      jnxM40eFPMDisplay     OBJECT IDENTIFIER ::= { jnxM40eSubFPM 2 }
                            -- Display part of FPM

    jnxM40ePIC0             OBJECT IDENTIFIER ::= { jnxSubmoduleM40e 4 }
                            -- This is the quad-height PIC which takes
                            -- up the whole FPC slot of the M40e.


    jnxM40ePIC1             OBJECT IDENTIFIER ::= { jnxSubmoduleM40e 5 }
                            -- This is FPC type 1 of PIC.
      jnxM40eQuadSonetOc3   OBJECT IDENTIFIER ::= { jnxM40ePIC1 1 }
      jnxM40eSonetOc12      OBJECT IDENTIFIER ::= { jnxM40ePIC1 2 }
      jnxM40eGigEther       OBJECT IDENTIFIER ::= { jnxM40ePIC1 3 }
      jnxM40eQuadT3         OBJECT IDENTIFIER ::= { jnxM40ePIC1 4 }
      jnxM40eQuadE3         OBJECT IDENTIFIER ::= { jnxM40ePIC1 5 }
      jnxM40eDualAtmOc3     OBJECT IDENTIFIER ::= { jnxM40ePIC1 6 }
      jnxM40eAtmOc12        OBJECT IDENTIFIER ::= { jnxM40ePIC1 7 }
      jnxM40eChOc12toDs3    OBJECT IDENTIFIER ::= { jnxM40ePIC1 8 }
      jnxM40eQuadEther      OBJECT IDENTIFIER ::= { jnxM40ePIC1 9 }
      jnxM40eQuadE1         OBJECT IDENTIFIER ::= { jnxM40ePIC1 10 }
      jnxM40eQuadT1         OBJECT IDENTIFIER ::= { jnxM40ePIC1 11 }
      jnxM40eQuadChT3       OBJECT IDENTIFIER ::= { jnxM40ePIC1 12 }
      jnxM40eQuadAtmE3      OBJECT IDENTIFIER ::= { jnxM40ePIC1 13 }
      jnxM40eQuadAtmT3      OBJECT IDENTIFIER ::= { jnxM40ePIC1 14 }
      jnxM40eGigEtherBundle OBJECT IDENTIFIER ::= { jnxM40ePIC1 15 }
      jnxM40eChStm1         OBJECT IDENTIFIER ::= { jnxM40ePIC1 16 }
      jnxM40eDecaChE1       OBJECT IDENTIFIER ::= { jnxM40ePIC1 17 }
                            -- 10-port channelized E1
      jnxM40eChDs3toDs0     OBJECT IDENTIFIER ::= { jnxM40ePIC1 18 }
      jnxM40eDualChDs3toDs0 OBJECT IDENTIFIER ::= { jnxM40ePIC1 19 }
      jnxM40eDenseEther8    OBJECT IDENTIFIER ::= { jnxM40ePIC1 20 }
                            -- 8-port Fast Ethernet
      jnxM40eAtmIIOc12      OBJECT IDENTIFIER ::= { jnxM40ePIC1 23 }
      jnxM40eDualAtmIIOc3   OBJECT IDENTIFIER ::= { jnxM40ePIC1 24 }
      jnxM40eDualQChDS3     OBJECT IDENTIFIER ::= { jnxM40ePIC1 25 }
      jnxM40eQuadQChT3      OBJECT IDENTIFIER ::= { jnxM40ePIC1 26 }
      jnxM40eLsMultilink128 OBJECT IDENTIFIER ::= { jnxM40ePIC1 27 }
      jnxM40eLsMultilink32  OBJECT IDENTIFIER ::= { jnxM40ePIC1 28 }
      jnxM40eLsMultilink4   OBJECT IDENTIFIER ::= { jnxM40ePIC1 29 }
      jnxM40eQChOc12        OBJECT IDENTIFIER ::= { jnxM40ePIC1 30 }
      jnxM40eQChStm1        OBJECT IDENTIFIER ::= { jnxM40ePIC1 31 }
      jnxM40eDualQChStm1    OBJECT IDENTIFIER ::= { jnxM40ePIC1 32 }
      jnxM40eDecaQChE1      OBJECT IDENTIFIER ::= { jnxM40ePIC1 33 }
      jnxM40eDualEIA530     OBJECT IDENTIFIER ::= { jnxM40ePIC1 34 }
      jnxM40ePassiveMonitor OBJECT IDENTIFIER ::= { jnxM40ePIC1 35 }
      jnxM40eMultilink128   OBJECT IDENTIFIER ::= { jnxM40ePIC1 36 }
      jnxM40eMultilink32    OBJECT IDENTIFIER ::= { jnxM40ePIC1 37 }
      jnxM40eMultilink4     OBJECT IDENTIFIER ::= { jnxM40ePIC1 38 }
      jnxM40eDenseEther12   OBJECT IDENTIFIER ::= { jnxM40ePIC1 39 }
			    -- 12-port Fast Ethernet
      jnxM40eDecaQChT1      OBJECT IDENTIFIER ::= { jnxM40ePIC1 40 }

    jnxM40ePIC2           OBJECT IDENTIFIER ::= { jnxSubmoduleM40e 6 }
                          -- This is FPC type 2 of PIC.

      jnxM40eSonetOc48Sr    OBJECT IDENTIFIER ::= { jnxM40ePIC2 1 }
      jnxM40eTunnel         OBJECT IDENTIFIER ::= { jnxM40ePIC2 2 }
      jnxM40eDualGigEther   OBJECT IDENTIFIER ::= { jnxM40ePIC2 3 }
      jnxM40eQuadSonetOc12  OBJECT IDENTIFIER ::= { jnxM40ePIC2 4 }
      jnxM40eSonetOc48Lr    OBJECT IDENTIFIER ::= { jnxM40ePIC2 5 }
      jnxM40eDenseEther48   OBJECT IDENTIFIER ::= { jnxM40ePIC2 6 }
                            -- 48-port Fast Ethernet
      jnxM40eQuadGigEther   OBJECT IDENTIFIER ::= { jnxM40ePIC2 7 }
      jnxM40eCrypto800      OBJECT IDENTIFIER ::= { jnxM40ePIC2 9 }
      jnxM40eQuadOc3        OBJECT IDENTIFIER ::= { jnxM40ePIC2 10 }
      jnxM40eDualQHGE       OBJECT IDENTIFIER ::= { jnxM40ePIC2 11 }
      jnxM40eDualAtmIIOc12  OBJECT IDENTIFIER ::= { jnxM40ePIC2 12 }


-- Miscellaneous Components

jnxMiscComponent    OBJECT IDENTIFIER ::= { jnxClassContents 4 }
    jnxTempSensor	OBJECT IDENTIFIER ::= { jnxMiscComponent 1 }


-- Status Source

jnxStatusSource	    OBJECT IDENTIFIER ::= { jnxClassStatus 1 }
  jnxStatusSourceM40	OBJECT IDENTIFIER ::= { jnxStatusSource 1 }

    jnxChassisSlotLED 	  OBJECT IDENTIFIER ::= { jnxStatusSourceM40 1 }
    jnxChassisAlarmLED 	  OBJECT IDENTIFIER ::= { jnxStatusSourceM40 2 }
    jnxHostCtlrLED	  OBJECT IDENTIFIER ::= { jnxStatusSourceM40 3 }
    jnxChassisTempSensor  OBJECT IDENTIFIER ::= { jnxStatusSourceM40 4 }
    jnxRoutingEngineLED   OBJECT IDENTIFIER ::= { jnxStatusSourceM40 5 }


--
-- M320
--
  jnxProductLineM320      OBJECT IDENTIFIER ::= { jnxProductLine      9 }
  jnxProductNameM320      OBJECT IDENTIFIER ::= { jnxProductName      9 }
  jnxProductModelM320     OBJECT IDENTIFIER ::= { jnxProductModel     9 }
  jnxProductVariationM320 OBJECT IDENTIFIER ::= { jnxProductVariation 9 }
  jnxChassisM320          OBJECT IDENTIFIER ::= { jnxChassis          9 }

  jnxSlotM320             OBJECT IDENTIFIER ::= { jnxSlot     9 }
    jnxM320SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotM320 1 }
    jnxM320SlotSIB        OBJECT IDENTIFIER ::= { jnxSlotM320 2 }
    jnxM320SlotHM         OBJECT IDENTIFIER ::= { jnxSlotM320 3 }
    jnxM320SlotPower      OBJECT IDENTIFIER ::= { jnxSlotM320 4 }
    jnxM320SlotFan        OBJECT IDENTIFIER ::= { jnxSlotM320 5 }
    jnxM320SlotCB         OBJECT IDENTIFIER ::= { jnxSlotM320 6 }
    jnxM320SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotM320 7 }
    jnxM320SlotCIP        OBJECT IDENTIFIER ::= { jnxSlotM320 8 }

  jnxMediaCardSpaceM320      OBJECT IDENTIFIER ::= { jnxMediaCardSpace     9 }
    jnxM320MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM320 1 }
    jnxM320MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM320 2 }

  jnxMidplaneM320         OBJECT IDENTIFIER ::= { jnxBackplane  9 }

  jnxModuleM320           OBJECT IDENTIFIER ::= { jnxModule     9 }
    jnxM320FPC            OBJECT IDENTIFIER ::= { jnxModuleM320 1 }
    jnxM320SIB            OBJECT IDENTIFIER ::= { jnxModuleM320 2 }
    jnxM320HM             OBJECT IDENTIFIER ::= { jnxModuleM320 3 }
    jnxM320Power          OBJECT IDENTIFIER ::= { jnxModuleM320 4 }
    jnxM320Fan            OBJECT IDENTIFIER ::= { jnxModuleM320 5 }
    jnxM320CB             OBJECT IDENTIFIER ::= { jnxModuleM320 6 }
    jnxM320FPB            OBJECT IDENTIFIER ::= { jnxModuleM320 7 }
    jnxM320CIP            OBJECT IDENTIFIER ::= { jnxModuleM320 8 }




--
-- M7i
--
  jnxProductLineM7i      OBJECT IDENTIFIER ::= { jnxProductLine      10 }
  jnxProductNameM7i      OBJECT IDENTIFIER ::= { jnxProductName      10 }
  jnxProductModelM7i     OBJECT IDENTIFIER ::= { jnxProductModel     10 }
  jnxProductVariationM7i OBJECT IDENTIFIER ::= { jnxProductVariation 10 }
  jnxChassisM7i          OBJECT IDENTIFIER ::= { jnxChassis          10 }

  jnxSlotM7i             OBJECT IDENTIFIER ::= { jnxSlot    10 }
    jnxM7iSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotM7i 1  }
    jnxM7iSlotCFEB       OBJECT IDENTIFIER ::= { jnxSlotM7i 2  }
    jnxM7iSlotRE         OBJECT IDENTIFIER ::= { jnxSlotM7i 3  }
    jnxM7iSlotPower      OBJECT IDENTIFIER ::= { jnxSlotM7i 4  }
    jnxM7iSlotFan        OBJECT IDENTIFIER ::= { jnxSlotM7i 5  }

  jnxMediaCardSpaceM7i   OBJECT IDENTIFIER ::= { jnxMediaCardSpace       10 }
    jnxM7iMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM7i 1 }

  jnxMidplaneM7i         OBJECT IDENTIFIER ::= { jnxBackplane 10 }

  jnxModuleM7i           OBJECT IDENTIFIER ::= { jnxModule    10 }
    jnxM7iFPC            OBJECT IDENTIFIER ::= { jnxModuleM7i 1  }
    jnxM7iCFEB           OBJECT IDENTIFIER ::= { jnxModuleM7i 2  }
    jnxM7iRE             OBJECT IDENTIFIER ::= { jnxModuleM7i 3  }
    jnxM7iPower          OBJECT IDENTIFIER ::= { jnxModuleM7i 4  }
    jnxM7iPowerAC        OBJECT IDENTIFIER ::= { jnxM7iPower  1  }
    jnxM7iFan            OBJECT IDENTIFIER ::= { jnxModuleM7i 5  }

  jnxSubmoduleM7i        OBJECT IDENTIFIER ::= { jnxSubmodule    10 }
    jnxM7iPIC            OBJECT IDENTIFIER ::= { jnxSubmoduleM7i 2  }


--
-- M10i
--
  jnxProductLineM10i      OBJECT IDENTIFIER ::= { jnxProductLine      11 }
  jnxProductNameM10i      OBJECT IDENTIFIER ::= { jnxProductName      11 }
  jnxProductModelM10i     OBJECT IDENTIFIER ::= { jnxProductModel     11 }
  jnxProductVariationM10i OBJECT IDENTIFIER ::= { jnxProductVariation 11 }
  jnxChassisM10i          OBJECT IDENTIFIER ::= { jnxChassis          11 }

  jnxSlotM10i             OBJECT IDENTIFIER ::= { jnxSlot           11 }
    jnxM10iSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotM10i       1  }
    jnxM10iSlotCFEB       OBJECT IDENTIFIER ::= { jnxSlotM10i       2  }
    jnxM10iSlotRE         OBJECT IDENTIFIER ::= { jnxSlotM10i       3  }
    jnxM10iSlotPower      OBJECT IDENTIFIER ::= { jnxSlotM10i       4  }
    jnxM10iSlotFan        OBJECT IDENTIFIER ::= { jnxSlotM10i       5  }
    jnxM10iSlotHCM        OBJECT IDENTIFIER ::= { jnxSlotM10i       6  }

  jnxMediaCardSpaceM10i   OBJECT IDENTIFIER    ::= { jnxMediaCardSpace    11 }
    jnxM10iMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM10i 1 }

  jnxMidplaneM10i         OBJECT IDENTIFIER ::= { jnxBackplane  11 }

  jnxModuleM10i           OBJECT IDENTIFIER ::= { jnxModule     11 }
    jnxM10iFPC            OBJECT IDENTIFIER ::= { jnxModuleM10i 1  }
    jnxM10iCFEB           OBJECT IDENTIFIER ::= { jnxModuleM10i 2  }
    jnxM10iRE             OBJECT IDENTIFIER ::= { jnxModuleM10i 3  }
    jnxM10iPower          OBJECT IDENTIFIER ::= { jnxModuleM10i 4  }
    jnxM10iPowerAC        OBJECT IDENTIFIER ::= { jnxM10iPower  1  }
    jnxM10iFan            OBJECT IDENTIFIER ::= { jnxModuleM10i 5  }
    jnxM10iHCM            OBJECT IDENTIFIER ::= { jnxModuleM10i 6  }

--
-- J2300
--
  jnxProductLineJ2300       OBJECT IDENTIFIER ::= { jnxProductLine      13 }
  jnxProductNameJ2300       OBJECT IDENTIFIER ::= { jnxProductName      13 }
  jnxChassisJ2300           OBJECT IDENTIFIER ::= { jnxChassis          13 }

  jnxSlotJ2300              OBJECT IDENTIFIER ::= { jnxSlot    13 }
    jnxJ2300SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ2300  1  }
    jnxJ2300SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ2300  2  }
    jnxJ2300SlotFan         OBJECT IDENTIFIER ::= { jnxSlotJ2300  3  }

  jnxMediaCardSpaceJ2300    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       13 }
    jnxJ2300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ2300  1 }

  jnxMidplaneJ2300         OBJECT IDENTIFIER ::= { jnxBackplane 13 }

  jnxModuleJ2300            OBJECT IDENTIFIER ::= { jnxModule    13 }
    jnxJ2300FPC             OBJECT IDENTIFIER ::= { jnxModuleJ2300 1  }
    jnxJ2300RE              OBJECT IDENTIFIER ::= { jnxModuleJ2300 2  }
    jnxJ2300Fan             OBJECT IDENTIFIER ::= { jnxModuleJ2300 3  }

--
-- J4300
--
  jnxProductLineJ4300       OBJECT IDENTIFIER ::= { jnxProductLine      14 }
  jnxProductNameJ4300       OBJECT IDENTIFIER ::= { jnxProductName      14 }
  jnxChassisJ4300           OBJECT IDENTIFIER ::= { jnxChassis          14 }

  jnxSlotJ4300              OBJECT IDENTIFIER ::= { jnxSlot    14 }
    jnxJ4300SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ4300  1  }
    jnxJ4300SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ4300  2  }
    jnxJ4300SlotFan         OBJECT IDENTIFIER ::= { jnxSlotJ4300  3  }

  jnxMediaCardSpaceJ4300    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       14 }
    jnxJ4300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ4300  1 }

  jnxMidplaneJ4300         OBJECT IDENTIFIER ::= { jnxBackplane 14 }

  jnxModuleJ4300            OBJECT IDENTIFIER ::= { jnxModule    14 }
    jnxJ4300FPC             OBJECT IDENTIFIER ::= { jnxModuleJ4300 1  }
    jnxJ4300RE              OBJECT IDENTIFIER ::= { jnxModuleJ4300 2  }
    jnxJ4300Fan             OBJECT IDENTIFIER ::= { jnxModuleJ4300 3  }

--
-- J6300
--
  jnxProductLineJ6300       OBJECT IDENTIFIER ::= { jnxProductLine      15 }
  jnxProductNameJ6300       OBJECT IDENTIFIER ::= { jnxProductName      15 }
  jnxChassisJ6300           OBJECT IDENTIFIER ::= { jnxChassis          15 }

  jnxSlotJ6300              OBJECT IDENTIFIER ::= { jnxSlot    15 }
    jnxJ6300SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ6300  1  }
    jnxJ6300SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ6300  2  }
    jnxJ6300SlotFan         OBJECT IDENTIFIER ::= { jnxSlotJ6300  3  }

  jnxMediaCardSpaceJ6300    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       15 }
    jnxJ6300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ6300  1 }

  jnxMidplaneJ6300         OBJECT IDENTIFIER ::= { jnxBackplane 15 }

  jnxModuleJ6300            OBJECT IDENTIFIER ::= { jnxModule    15 }
    jnxJ6300FPC             OBJECT IDENTIFIER ::= { jnxModuleJ6300 1  }
    jnxJ6300RE              OBJECT IDENTIFIER ::= { jnxModuleJ6300 2  }
    jnxJ6300Fan             OBJECT IDENTIFIER ::= { jnxModuleJ6300 3  }

--
-- IRM
--
  jnxProductLineIRM      OBJECT IDENTIFIER ::= { jnxProductLine      16 }
  jnxProductNameIRM      OBJECT IDENTIFIER ::= { jnxProductName      16 }
  jnxProductModelIRM     OBJECT IDENTIFIER ::= { jnxProductModel     16 }
  jnxProductVariationIRM OBJECT IDENTIFIER ::= { jnxProductVariation 16 }
  jnxChassisIRM          OBJECT IDENTIFIER ::= { jnxChassis          16 }

  jnxSlotIRM             OBJECT IDENTIFIER ::= { jnxSlot    16 }
    jnxIRMSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIRM 1  }
    jnxIRMSlotCFEB       OBJECT IDENTIFIER ::= { jnxSlotIRM 2  }
    jnxIRMSlotRE         OBJECT IDENTIFIER ::= { jnxSlotIRM 3  }
    jnxIRMSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIRM 4  }

  jnxMediaCardSpaceIRM   OBJECT IDENTIFIER ::= { jnxMediaCardSpace       16 }
    jnxIRMMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIRM 1 }

  jnxMidplaneIRM         OBJECT IDENTIFIER ::= { jnxBackplane 16 }

  jnxModuleIRM           OBJECT IDENTIFIER ::= { jnxModule    16 }
    jnxIRMFPC            OBJECT IDENTIFIER ::= { jnxModuleIRM 1  }
    jnxIRMCFEB           OBJECT IDENTIFIER ::= { jnxModuleIRM 2  }
    jnxIRMRE             OBJECT IDENTIFIER ::= { jnxModuleIRM 3  }
    jnxIRMPower          OBJECT IDENTIFIER ::= { jnxModuleIRM 4  }
    jnxIRMPowerDC        OBJECT IDENTIFIER ::= { jnxIRMPower  1  }

--
-- TX
--
  jnxProductLineTX      OBJECT IDENTIFIER ::= { jnxProductLine      17 }
  jnxProductNameTX      OBJECT IDENTIFIER ::= { jnxProductName      17 }
  jnxProductModelTX     OBJECT IDENTIFIER ::= { jnxProductModel     17 }
  jnxProductVariationTX OBJECT IDENTIFIER ::= { jnxProductVariation 17 }
  jnxChassisTX          OBJECT IDENTIFIER ::= { jnxChassis          17 }

  jnxSlotTX             OBJECT IDENTIFIER ::= { jnxSlot    17 }
    jnxTXSlotSIB        OBJECT IDENTIFIER ::= { jnxSlotTX 1  }
    jnxTXSlotHM         OBJECT IDENTIFIER ::= { jnxSlotTX 2  }
    jnxTXSlotPower      OBJECT IDENTIFIER ::= { jnxSlotTX 3  }
    jnxTXSlotFan        OBJECT IDENTIFIER ::= { jnxSlotTX 4  }
    jnxTXSlotCB         OBJECT IDENTIFIER ::= { jnxSlotTX 5  }
    jnxTXSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotTX 6  }
    jnxTXSlotCIP        OBJECT IDENTIFIER ::= { jnxSlotTX 7  }
    jnxTXSlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotTX 8  }
    jnxTXSlotLCC        OBJECT IDENTIFIER ::= { jnxSlotTX 9  }

  jnxMediaCardSpaceTX      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    17 }
    jnxTXMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceTX 1  }

  jnxMidplaneTX         OBJECT IDENTIFIER ::= { jnxBackplane 17 }

  jnxModuleTX           OBJECT IDENTIFIER ::= { jnxModule    17 }
    jnxTXSIB            OBJECT IDENTIFIER ::= { jnxModuleTX 1  }
    jnxTXHM             OBJECT IDENTIFIER ::= { jnxModuleTX 2  }
    jnxTXPower          OBJECT IDENTIFIER ::= { jnxModuleTX 3  }
    jnxTXFan            OBJECT IDENTIFIER ::= { jnxModuleTX 4  }
    jnxTXCB             OBJECT IDENTIFIER ::= { jnxModuleTX 5  }
    jnxTXFPB            OBJECT IDENTIFIER ::= { jnxModuleTX 6  }
    jnxTXCIP            OBJECT IDENTIFIER ::= { jnxModuleTX 7  }
    jnxTXSPMB           OBJECT IDENTIFIER ::= { jnxModuleTX 8  }
    jnxTXLCC            OBJECT IDENTIFIER ::= { jnxModuleTX 9  }

--
-- M120
--

  jnxProductLineM120      OBJECT IDENTIFIER ::= { jnxProductLine      18 }
  jnxProductNameM120      OBJECT IDENTIFIER ::= { jnxProductName      18 }
  jnxProductModelM120     OBJECT IDENTIFIER ::= { jnxProductModel     18 }
  jnxProductVariationM120 OBJECT IDENTIFIER ::= { jnxProductVariation 18 }
  jnxChassisM120          OBJECT IDENTIFIER ::= { jnxChassis          18 }

  jnxSlotM120             OBJECT IDENTIFIER ::= { jnxSlot             18 }
    jnxM120SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotM120 1  }
    jnxM120SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotM120 2  }
    jnxM120SlotHM         OBJECT IDENTIFIER ::= { jnxSlotM120 3  }
    jnxM120SlotPower      OBJECT IDENTIFIER ::= { jnxSlotM120 4  }
    jnxM120SlotFan        OBJECT IDENTIFIER ::= { jnxSlotM120 5  }
    jnxM120SlotCB         OBJECT IDENTIFIER ::= { jnxSlotM120 6  }
    jnxM120SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotM120 7  }

  jnxMediaCardSpaceM120      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    18 }
    jnxM120MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceM120 1 }

  jnxMidplaneM120         OBJECT IDENTIFIER ::= { jnxBackplane        18 }

  jnxModuleM120           OBJECT IDENTIFIER ::= { jnxModule           18 }
    jnxM120FEB            OBJECT IDENTIFIER ::= { jnxModuleM120   1 }


--
-- J4350
--
  jnxProductLineJ4350       OBJECT IDENTIFIER ::= { jnxProductLine      19 }
  jnxProductNameJ4350       OBJECT IDENTIFIER ::= { jnxProductName      19 }
  jnxChassisJ4350           OBJECT IDENTIFIER ::= { jnxChassis          19 }

  jnxSlotJ4350              OBJECT IDENTIFIER ::= { jnxSlot    19 }
    jnxJ4350SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ4350  1  }
    jnxJ4350SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ4350  2  }
    jnxJ4350SlotPower       OBJECT IDENTIFIER ::= { jnxSlotJ4350  3  }
    jnxJ4350SlotFan         OBJECT IDENTIFIER ::= { jnxSlotJ4350  4  }

  jnxMediaCardSpaceJ4350    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       19 }
    jnxJ4350MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ4350  1 }

  jnxMidplaneJ4350         OBJECT IDENTIFIER ::= { jnxBackplane 19 }

  jnxModuleJ4350            OBJECT IDENTIFIER ::= { jnxModule    19 }
    jnxJ4350FPC             OBJECT IDENTIFIER ::= { jnxModuleJ4350 1  }
    jnxJ4350RE              OBJECT IDENTIFIER ::= { jnxModuleJ4350 2  }
    jnxJ4350Power           OBJECT IDENTIFIER ::= { jnxModuleJ4350 3  }
    jnxJ4350Fan             OBJECT IDENTIFIER ::= { jnxModuleJ4350 4  }

--
-- J6350
--
  jnxProductLineJ6350       OBJECT IDENTIFIER ::= { jnxProductLine      20 }
  jnxProductNameJ6350       OBJECT IDENTIFIER ::= { jnxProductName      20 }
  jnxChassisJ6350           OBJECT IDENTIFIER ::= { jnxChassis          20 }

  jnxSlotJ6350              OBJECT IDENTIFIER ::= { jnxSlot    20 }
    jnxJ6350SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ6350  1  }
    jnxJ6350SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ6350  2  }
    jnxJ6350SlotPower       OBJECT IDENTIFIER ::= { jnxSlotJ6350  3  }
    jnxJ6350SlotFan         OBJECT IDENTIFIER ::= { jnxSlotJ6350  4  }

  jnxMediaCardSpaceJ6350    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       20 }
    jnxJ6350MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ6350  1 }

  jnxMidplaneJ6350         OBJECT IDENTIFIER ::= { jnxBackplane 20 }

  jnxModuleJ6350            OBJECT IDENTIFIER ::= { jnxModule    20 }
    jnxJ6350FPC             OBJECT IDENTIFIER ::= { jnxModuleJ6350 1  }
    jnxJ6350RE              OBJECT IDENTIFIER ::= { jnxModuleJ6350 2  }
    jnxJ6350Power           OBJECT IDENTIFIER ::= { jnxModuleJ6350 3  }
    jnxJ6350Fan             OBJECT IDENTIFIER ::= { jnxModuleJ6350 4  }

--
-- MX960
--

  jnxProductLineMX960      OBJECT IDENTIFIER ::= { jnxProductLine      21 }
  jnxProductNameMX960      OBJECT IDENTIFIER ::= { jnxProductName      21 }
  jnxProductModelMX960     OBJECT IDENTIFIER ::= { jnxProductModel     21 }
  jnxProductVariationMX960 OBJECT IDENTIFIER ::= { jnxProductVariation 21 }
  jnxChassisMX960          OBJECT IDENTIFIER ::= { jnxChassis          21 }

  jnxSlotMX960             OBJECT IDENTIFIER ::= { jnxSlot             21 }
    jnxMX960SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX960 1  }
    jnxMX960SlotHM         OBJECT IDENTIFIER ::= { jnxSlotMX960 2  }
    jnxMX960SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX960 3  }
    jnxMX960SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX960 4  }
    jnxMX960SlotCB         OBJECT IDENTIFIER ::= { jnxSlotMX960 5  }
    jnxMX960SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotMX960 6  }

  jnxMediaCardSpaceMX960      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    21 }
    jnxMX960MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX960 1 }
    jnxMX960MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX960 2 }

  jnxMidplaneMX960         OBJECT IDENTIFIER ::= { jnxBackplane        21 }


--
-- J4320
--
  jnxProductLineJ4320       OBJECT IDENTIFIER ::= { jnxProductLine      22 }
  jnxProductNameJ4320       OBJECT IDENTIFIER ::= { jnxProductName      22 }
  jnxChassisJ4320           OBJECT IDENTIFIER ::= { jnxChassis          22 }

  jnxSlotJ4320              OBJECT IDENTIFIER ::= { jnxSlot    22 }
    jnxJ4320SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ4320  1  }
    jnxJ4320SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ4320  2  }

  jnxMediaCardSpaceJ4320    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       22 }
    jnxJ4320MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ4320  1 }

  jnxMidplaneJ4320         OBJECT IDENTIFIER ::= { jnxBackplane 22 }

  jnxModuleJ4320            OBJECT IDENTIFIER ::= { jnxModule    22 }
    jnxJ4320FPC             OBJECT IDENTIFIER ::= { jnxModuleJ4320 1  }
    jnxJ4320RE              OBJECT IDENTIFIER ::= { jnxModuleJ4320 2  }

--
-- J2320
--
  jnxProductLineJ2320       OBJECT IDENTIFIER ::= { jnxProductLine      23 }
  jnxProductNameJ2320       OBJECT IDENTIFIER ::= { jnxProductName      23 }
  jnxChassisJ2320           OBJECT IDENTIFIER ::= { jnxChassis          23 }

  jnxSlotJ2320              OBJECT IDENTIFIER ::= { jnxSlot    23 }
    jnxJ2320SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ2320  1  }
    jnxJ2320SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ2320  2  }
    jnxJ2320SlotPower       OBJECT IDENTIFIER ::= { jnxSlotJ2320  3  }
    jnxJ2320SlotFan         OBJECT IDENTIFIER ::= { jnxSlotJ2320  4  }

  jnxMediaCardSpaceJ2320    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       23 }
    jnxJ2320MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ2320  1 }

  jnxMidplaneJ2320         OBJECT IDENTIFIER ::= { jnxBackplane 23 }

  jnxModuleJ2320            OBJECT IDENTIFIER ::= { jnxModule    23 }
    jnxJ2320FPC             OBJECT IDENTIFIER ::= { jnxModuleJ2320 1  }
    jnxJ2320RE              OBJECT IDENTIFIER ::= { jnxModuleJ2320 2  }
    jnxJ2320Power           OBJECT IDENTIFIER ::= { jnxModuleJ2320 3  }
    jnxJ2320Fan             OBJECT IDENTIFIER ::= { jnxModuleJ2320 4  }

--
-- J2350
--
  jnxProductLineJ2350       OBJECT IDENTIFIER ::= { jnxProductLine      24 }
  jnxProductNameJ2350       OBJECT IDENTIFIER ::= { jnxProductName      24 }
  jnxChassisJ2350           OBJECT IDENTIFIER ::= { jnxChassis          24 }

  jnxSlotJ2350              OBJECT IDENTIFIER ::= { jnxSlot    24 }
    jnxJ2350SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotJ2350  1  }
    jnxJ2350SlotRE          OBJECT IDENTIFIER ::= { jnxSlotJ2350  2  }
    jnxJ2350SlotPower       OBJECT IDENTIFIER ::= { jnxSlotJ2350  3  }
    jnxJ2350SlotFan         OBJECT IDENTIFIER ::= { jnxSlotJ2350  4  }

  jnxMediaCardSpaceJ2350    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       24 }
    jnxJ2350MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJ2350  1 }

  jnxMidplaneJ2350         OBJECT IDENTIFIER ::= { jnxBackplane 24 }

  jnxModuleJ2350            OBJECT IDENTIFIER ::= { jnxModule    24 }
    jnxJ2350FPC             OBJECT IDENTIFIER ::= { jnxModuleJ2350 1  }
    jnxJ2350RE              OBJECT IDENTIFIER ::= { jnxModuleJ2350 2  }
    jnxJ2350Power           OBJECT IDENTIFIER ::= { jnxModuleJ2350 3  }
    jnxJ2350Fan             OBJECT IDENTIFIER ::= { jnxModuleJ2350 4  }

--
-- MX480
--

  jnxProductLineMX480      OBJECT IDENTIFIER ::= { jnxProductLine      25 }
  jnxProductNameMX480      OBJECT IDENTIFIER ::= { jnxProductName      25 }
  jnxProductModelMX480     OBJECT IDENTIFIER ::= { jnxProductModel     25 }
  jnxProductVariationMX480 OBJECT IDENTIFIER ::= { jnxProductVariation 25 }
  jnxChassisMX480          OBJECT IDENTIFIER ::= { jnxChassis          25 }

  jnxSlotMX480             OBJECT IDENTIFIER ::= { jnxSlot             25 }
    jnxMX480SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX480 1  }
    jnxMX480SlotHM         OBJECT IDENTIFIER ::= { jnxSlotMX480 2  }
    jnxMX480SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX480 3  }
    jnxMX480SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX480 4  }
    jnxMX480SlotCB         OBJECT IDENTIFIER ::= { jnxSlotMX480 5  }
    jnxMX480SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotMX480 6  }

  jnxMediaCardSpaceMX480      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    25 }
  jnxMX480MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX480 1 }
  jnxMX480MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX480 2 }

  jnxMidplaneMX480         OBJECT IDENTIFIER ::= { jnxBackplane        25 }

--
-- A40 (SRX5800)
--

  jnxProductLineSRX5800      OBJECT IDENTIFIER ::= { jnxProductLine      26 }
  jnxProductNameSRX5800      OBJECT IDENTIFIER ::= { jnxProductName      26 }
  jnxProductModelSRX5800     OBJECT IDENTIFIER ::= { jnxProductModel     26 }
  jnxProductVariationSRX5800 OBJECT IDENTIFIER ::= { jnxProductVariation 26 }
  jnxChassisSRX5800          OBJECT IDENTIFIER ::= { jnxChassis          26 }

  jnxSlotSRX5800             OBJECT IDENTIFIER ::= { jnxSlot             26 }
    jnxSRX5800SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX5800 1  }
    jnxSRX5800SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX5800 2  }
    jnxSRX5800SlotPower      OBJECT IDENTIFIER ::= { jnxSlotSRX5800 3  }
    jnxSRX5800SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX5800 4  }
    jnxSRX5800SlotCB         OBJECT IDENTIFIER ::= { jnxSlotSRX5800 5  }
    jnxSRX5800SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotSRX5800 6  }

  jnxMediaCardSpaceSRX5800      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    26 }
    jnxSRX5800MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX5800 1 }

  jnxMidplaneSRX5800         OBJECT IDENTIFIER ::= { jnxBackplane        26 }

--
-- T1600
--

  jnxProductLineT1600      OBJECT IDENTIFIER ::= { jnxProductLine      27 }
  jnxProductNameT1600      OBJECT IDENTIFIER ::= { jnxProductName      27 }
  jnxProductModelT1600     OBJECT IDENTIFIER ::= { jnxProductModel     27 }
  jnxProductVariationT1600 OBJECT IDENTIFIER ::= { jnxProductVariation 27 }
  jnxChassisT1600          OBJECT IDENTIFIER ::= { jnxChassis          27 }

  jnxSlotT1600             OBJECT IDENTIFIER ::= { jnxSlot             27 }
    jnxT1600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotT1600 1 }
                          -- Flexible Port Concentrator slot
    jnxT1600SlotSIB        OBJECT IDENTIFIER ::= { jnxSlotT1600 2 }
                          -- Switch Interface Board slot
    jnxT1600SlotHM         OBJECT IDENTIFIER ::= { jnxSlotT1600 3 }
                          -- Host Module (also called Routing Engine) slot
    jnxT1600SlotSCG        OBJECT IDENTIFIER ::= { jnxSlotT1600 4 }
                          -- SONET Clock Generator slot
    jnxT1600SlotPower      OBJECT IDENTIFIER ::= { jnxSlotT1600 5 }
    jnxT1600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotT1600 6 }
    jnxT1600SlotCB         OBJECT IDENTIFIER ::= { jnxSlotT1600 7 }
                          -- Control Board slot
    jnxT1600SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotT1600 8 }
                          -- Front Panel Board
    jnxT1600SlotCIP        OBJECT IDENTIFIER ::= { jnxSlotT1600 9 }
                          -- Connector Interface Panel
    jnxT1600SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotT1600 10 }
                          -- Processor Mezzanine Board for SIB
    jnxT1600SlotPSD        OBJECT IDENTIFIER ::= { jnxSlotT1600 11 }
			  -- Protected System Domain slot

  jnxMediaCardSpaceT1600      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    27 }
    jnxT1600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceT1600 1 }

  jnxMidplaneT1600         OBJECT IDENTIFIER ::= { jnxBackplane        27 }

  jnxModuleT1600           OBJECT IDENTIFIER ::= { jnxModule 27 }
    jnxT1600FPC            OBJECT IDENTIFIER ::= { jnxModuleT1600 1 }
                          -- Flexible Port Concentrator
    jnxT1600SIB            OBJECT IDENTIFIER ::= { jnxModuleT1600 2 }
                          -- Swtich Interface Board
    jnxT1600HM             OBJECT IDENTIFIER ::= { jnxModuleT1600 3 }
                          -- Host Module (also called Routing Engine)
    jnxT1600SCG            OBJECT IDENTIFIER ::= { jnxModuleT1600 4 }
                          -- SONET Clock Generator
    jnxT1600Power          OBJECT IDENTIFIER ::= { jnxModuleT1600 5 }
    jnxT1600Fan            OBJECT IDENTIFIER ::= { jnxModuleT1600 6 }
    jnxT1600CB             OBJECT IDENTIFIER ::= { jnxModuleT1600 7 }
                          -- Control Board
    jnxT1600FPB            OBJECT IDENTIFIER ::= { jnxModuleT1600 8 }
                          -- Front Panel Board
    jnxT1600CIP            OBJECT IDENTIFIER ::= { jnxModuleT1600 9 }
                          -- Connector Interface Panel
    jnxT1600SPMB           OBJECT IDENTIFIER ::= { jnxModuleT1600 10 }
                          -- Processor Mezzanine Board for SIB

--
-- A20 (SRX5600)
--

  jnxProductLineSRX5600      OBJECT IDENTIFIER ::= { jnxProductLine      28 }
  jnxProductNameSRX5600      OBJECT IDENTIFIER ::= { jnxProductName      28 }
  jnxProductModelSRX5600     OBJECT IDENTIFIER ::= { jnxProductModel     28 }
  jnxProductVariationSRX5600 OBJECT IDENTIFIER ::= { jnxProductVariation 28 }
  jnxChassisSRX5600          OBJECT IDENTIFIER ::= { jnxChassis          28 }

  jnxSlotSRX5600             OBJECT IDENTIFIER ::= { jnxSlot             28 }
    jnxSRX5600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX5600 1  }
    jnxSRX5600SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX5600 2  }
    jnxSRX5600SlotPower      OBJECT IDENTIFIER ::= { jnxSlotSRX5600 3  }
    jnxSRX5600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX5600 4  }
    jnxSRX5600SlotCB         OBJECT IDENTIFIER ::= { jnxSlotSRX5600 5  }
    jnxSRX5600SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotSRX5600 6  }

  jnxMediaCardSpaceSRX5600      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    28 }
  jnxSRX5600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX5600 1 }


  jnxMidplaneSRX5600         OBJECT IDENTIFIER ::= { jnxBackplane        28 }

--
-- MX240
--

  jnxProductLineMX240      OBJECT IDENTIFIER ::= { jnxProductLine      29 }
  jnxProductNameMX240      OBJECT IDENTIFIER ::= { jnxProductName      29 }
  jnxProductModelMX240     OBJECT IDENTIFIER ::= { jnxProductModel     29 }
  jnxProductVariationMX240 OBJECT IDENTIFIER ::= { jnxProductVariation 29 }
  jnxChassisMX240          OBJECT IDENTIFIER ::= { jnxChassis          29 }

  jnxSlotMX240             OBJECT IDENTIFIER ::= { jnxSlot             29 }
  jnxMX240SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotMX240 1  }
  jnxMX240SlotHM           OBJECT IDENTIFIER ::= { jnxSlotMX240 2  }
  jnxMX240SlotPower        OBJECT IDENTIFIER ::= { jnxSlotMX240 3  }
  jnxMX240SlotFan          OBJECT IDENTIFIER ::= { jnxSlotMX240 4  }
  jnxMX240SlotCB           OBJECT IDENTIFIER ::= { jnxSlotMX240 5  }
  jnxMX240SlotFPB          OBJECT IDENTIFIER ::= { jnxSlotMX240 6  }
  jnxMediaCardSpaceMX240   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    29 }
  jnxMX240MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX240 1 }
  jnxMX240MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX240 2 }

  jnxMidplaneMX240         OBJECT IDENTIFIER ::= { jnxBackplane         29 }


--
-- EX3200
--

  jnxProductLineEX3200      OBJECT IDENTIFIER ::= { jnxProductLine      30 }
  jnxProductNameEX3200      OBJECT IDENTIFIER ::= { jnxProductName      30 }
  jnxProductModelEX3200     OBJECT IDENTIFIER ::= { jnxProductModel     30 }
  jnxProductVariationEX3200 OBJECT IDENTIFIER ::= { jnxProductVariation 30 }
    jnxProductEX3200port24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX3200 1 }
    jnxProductEX3200port24P    OBJECT IDENTIFIER ::= { jnxProductVariationEX3200 2 }
    jnxProductEX3200port48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX3200 3 }
    jnxProductEX3200port48P    OBJECT IDENTIFIER ::= { jnxProductVariationEX3200 4 }

  jnxChassisEX3200          OBJECT IDENTIFIER ::= { jnxChassis          30 }

  jnxSlotEX3200             OBJECT IDENTIFIER ::= { jnxSlot             30 }
    jnxEX3200SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX3200 1 }
      jnxEX3200SlotPower    OBJECT IDENTIFIER ::= { jnxEX3200SlotFPC 1 }
      jnxEX3200SlotFan      OBJECT IDENTIFIER ::= { jnxEX3200SlotFPC 2 }
      jnxEX3200SlotRE       OBJECT IDENTIFIER ::= { jnxEX3200SlotFPC 3 }

  jnxMediaCardSpaceEX3200      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    30 }
    jnxEX3200MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX3200 1 }

  jnxModuleEX3200            OBJECT IDENTIFIER ::= { jnxModule    30 }
    jnxEX3200FPC             OBJECT IDENTIFIER ::= { jnxModuleEX3200 1 }
      jnxEX3200Power         OBJECT IDENTIFIER ::= { jnxEX3200FPC 1 }
      jnxEX3200Fan           OBJECT IDENTIFIER ::= { jnxEX3200FPC 2 }
      jnxEX3200RE            OBJECT IDENTIFIER ::= { jnxEX3200FPC 3 }

--
-- EX4200
--

  jnxProductLineEX4200      OBJECT IDENTIFIER ::= { jnxProductLine      31 }
  jnxProductNameEX4200      OBJECT IDENTIFIER ::= { jnxProductName      31 }
  jnxProductModelEX4200     OBJECT IDENTIFIER ::= { jnxProductModel     31 }
  jnxProductVariationEX4200 OBJECT IDENTIFIER ::= { jnxProductVariation 31 }
    jnxProductEX4200port24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4200 1 }
    jnxProductEX4200port24P    OBJECT IDENTIFIER ::= { jnxProductVariationEX4200 2 }
    jnxProductEX4200port48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4200 3 }
    jnxProductEX4200port48P    OBJECT IDENTIFIER ::= { jnxProductVariationEX4200 4 }
    jnxProductEX4200port24F    OBJECT IDENTIFIER ::= { jnxProductVariationEX4200 5 }
    jnxProductEX4200port24PX   OBJECT IDENTIFIER ::= { jnxProductVariationEX4200 6 }
    jnxProductEX4200port48PX   OBJECT IDENTIFIER ::= { jnxProductVariationEX4200 7 }

  jnxChassisEX4200          OBJECT IDENTIFIER ::= { jnxChassis          31 }
    jnxEX4200RE0            OBJECT IDENTIFIER ::= { jnxChassisEX4200 1  }
    jnxEX4200RE1            OBJECT IDENTIFIER ::= { jnxChassisEX4200 2  }
  jnxSlotEX4200             OBJECT IDENTIFIER ::= { jnxSlot             31 }
    jnxEX4200SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX4200 1 }
      jnxEX4200SlotPower    OBJECT IDENTIFIER ::= { jnxEX4200SlotFPC 1 }
      jnxEX4200SlotFan      OBJECT IDENTIFIER ::= { jnxEX4200SlotFPC 2 }

  jnxMediaCardSpaceEX4200      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    31 }
    jnxEX4200MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4200 1 }

 jnxModuleEX4200            OBJECT IDENTIFIER ::= { jnxModule    31 }
    jnxEX4200FPC            OBJECT IDENTIFIER ::= { jnxModuleEX4200 1 }
      jnxEX4200Power        OBJECT IDENTIFIER ::= { jnxEX4200FPC 1 }
      jnxEX4200Fan          OBJECT IDENTIFIER ::= { jnxEX4200FPC 2 }

--
-- EX8208
--

  jnxProductLineEX8208       OBJECT IDENTIFIER ::= { jnxProductLine      32 }
  jnxProductNameEX8208       OBJECT IDENTIFIER ::= { jnxProductName      32 }
  jnxProductModelEX8208      OBJECT IDENTIFIER ::= { jnxProductModel     32 }
  jnxProductVariationEX8208  OBJECT IDENTIFIER ::= { jnxProductVariation 32 }
  jnxChassisEX8208           OBJECT IDENTIFIER ::= { jnxChassis          32 }

  jnxSlotEX8208              OBJECT IDENTIFIER ::= { jnxSlot             32 }
    jnxEX8208SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotEX8208  1 }
                            -- Flexible Port Concentrator
      jnxEX8208Slot48S       OBJECT IDENTIFIER ::= { jnxEX8208SlotFPC  1 }
      jnxEX8208Slot48T       OBJECT IDENTIFIER ::= { jnxEX8208SlotFPC  2 }
      jnxEX8208Slot8XS       OBJECT IDENTIFIER ::= { jnxEX8208SlotFPC  3 }
    jnxEX8208HM              OBJECT IDENTIFIER ::= { jnxSlotEX8208  3 }
                            -- Host Module (also called Routing Engine)
    jnxEX8208SlotPower       OBJECT IDENTIFIER ::= { jnxSlotEX8208  4 }
    jnxEX8208SlotFan         OBJECT IDENTIFIER ::= { jnxSlotEX8208  5 }
      jnxEX8208SlotFT        OBJECT IDENTIFIER ::= { jnxEX8208SlotFan  1 }
    jnxEX8208SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotEX8208  6 }
                            -- Control Board

  jnxMediaCardSpaceEX8208    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   32 }
    jnxEX8208MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX8208 1 }

  jnxBackplaneEX8208         OBJECT IDENTIFIER ::= { jnxBackplane        32 }

--
-- EX8216
--

  jnxProductLineEX8216       OBJECT IDENTIFIER ::= { jnxProductLine      33 }
  jnxProductNameEX8216       OBJECT IDENTIFIER ::= { jnxProductName      33 }
  jnxProductModelEX8216      OBJECT IDENTIFIER ::= { jnxProductModel     33 }
  jnxProductVariationEX8216  OBJECT IDENTIFIER ::= { jnxProductVariation 33 }
  jnxChassisEX8216           OBJECT IDENTIFIER ::= { jnxChassis          33 }

  jnxSlotEX8216              OBJECT IDENTIFIER ::= { jnxSlot             33 }
    jnxEX8216SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotEX8216   1 }
                            -- Flexible Port Concentrator
      jnxEX8216Slot48S       OBJECT IDENTIFIER ::= { jnxEX8216SlotFPC   1 }
      jnxEX8216Slot48T       OBJECT IDENTIFIER ::= { jnxEX8216SlotFPC   2 }
      jnxEX8216Slot8XS       OBJECT IDENTIFIER ::= { jnxEX8216SlotFPC   3 }
    jnxEX8216SIB             OBJECT IDENTIFIER ::= { jnxSlotEX8216   2 }
                            -- Swtich Interface Board
    jnxEX8216HM              OBJECT IDENTIFIER ::= { jnxSlotEX8216   3 }
                            -- Host Module (also called Routing Engine)
    jnxEX8216SlotPower       OBJECT IDENTIFIER ::= { jnxSlotEX8216   4 }
    jnxEX8216SlotFan         OBJECT IDENTIFIER ::= { jnxSlotEX8216   5 }
      jnxEX8216SlotFT        OBJECT IDENTIFIER ::= { jnxEX8216SlotFan   1 }
      jnxEX8216SlotRFT       OBJECT IDENTIFIER ::= { jnxEX8216SlotFan   2 }
    jnxEX8216SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotEX8216   6 }
                            -- Control Board

  jnxMediaCardSpaceEX8216    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   33 }
    jnxEX8216MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX8216 1 }

  jnxMidplaneEX8216          OBJECT IDENTIFIER ::= { jnxBackplane         33 }

--
-- SRX3600
--

  jnxProductLineSRX3600      OBJECT IDENTIFIER ::= { jnxProductLine      34 }
  jnxProductNameSRX3600      OBJECT IDENTIFIER ::= { jnxProductName      34 }
  jnxProductModelSRX3600     OBJECT IDENTIFIER ::= { jnxProductModel     34 }
  jnxProductVariationSRX3600 OBJECT IDENTIFIER ::= { jnxProductVariation 34 }
  jnxChassisSRX3600          OBJECT IDENTIFIER ::= { jnxChassis          34 }

  jnxSlotSRX3600             OBJECT IDENTIFIER ::= { jnxSlot             34 }
    jnxSRX3600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX3600 1  }
    jnxSRX3600SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX3600 2  }
    jnxSRX3600SlotPower      OBJECT IDENTIFIER ::= { jnxSlotSRX3600 3  }
    jnxSRX3600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX3600 4  }
    jnxSRX3600SlotCB         OBJECT IDENTIFIER ::= { jnxSlotSRX3600 5  }
    jnxSRX3600SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotSRX3600 6  }

  jnxMediaCardSpaceSRX3600   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    34 }
  jnxSRX3600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX3600 1 }

  jnxMidplaneSRX3600         OBJECT IDENTIFIER ::= { jnxBackplane        34 }

--
-- SRX3400
--

  jnxProductLineSRX3400      OBJECT IDENTIFIER ::= { jnxProductLine      35 }
  jnxProductNameSRX3400      OBJECT IDENTIFIER ::= { jnxProductName      35 }
  jnxProductModelSRX3400     OBJECT IDENTIFIER ::= { jnxProductModel     35 }
  jnxProductVariationSRX3400 OBJECT IDENTIFIER ::= { jnxProductVariation 35 }
  jnxChassisSRX3400          OBJECT IDENTIFIER ::= { jnxChassis          35 }

  jnxSlotSRX3400             OBJECT IDENTIFIER ::= { jnxSlot             35 }
    jnxSRX3400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX3400 1  }
    jnxSRX3400SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX3400 2  }
    jnxSRX3400SlotPower      OBJECT IDENTIFIER ::= { jnxSlotSRX3400 3  }
    jnxSRX3400SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX3400 4  }
    jnxSRX3400SlotCB         OBJECT IDENTIFIER ::= { jnxSlotSRX3400 5  }
    jnxSRX3400SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotSRX3400 6  }

  jnxMediaCardSpaceSRX3400   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    35 }
  jnxSRX3400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX3400 1 }

  jnxMidplaneSRX3400         OBJECT IDENTIFIER ::= { jnxBackplane        35 }

--
-- SRX210
--
  jnxProductLineSRX210       OBJECT IDENTIFIER ::= { jnxProductLine      36 }
  jnxProductNameSRX210       OBJECT IDENTIFIER ::= { jnxProductName      36 }
  jnxChassisSRX210           OBJECT IDENTIFIER ::= { jnxChassis          36 }

  jnxSlotSRX210              OBJECT IDENTIFIER ::= { jnxSlot    36 }
    jnxSRX210SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX210  1  }
    jnxSRX210SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX210  2  }
    jnxSRX210SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX210  3  }
    jnxSRX210SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX210  4  }

  jnxMediaCardSpaceSRX210    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       36 }
    jnxSRX210MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX210  1 }

  jnxMidplaneSRX210         OBJECT IDENTIFIER ::= { jnxBackplane 36 }

  jnxModuleSRX210            OBJECT IDENTIFIER ::= { jnxModule    36 }
    jnxSRX210FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX210 1  }
    jnxSRX210RE              OBJECT IDENTIFIER ::= { jnxModuleSRX210 2  }
    jnxSRX210Power           OBJECT IDENTIFIER ::= { jnxModuleSRX210 3  }
    jnxSRX210Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX210 4  }

--
-- TXP - Absolut
--

  jnxProductLineTXP      OBJECT IDENTIFIER ::= { jnxProductLine      37 }
  jnxProductNameTXP      OBJECT IDENTIFIER ::= { jnxProductName      37 }
  jnxProductModelTXP     OBJECT IDENTIFIER ::= { jnxProductModel     37 }
  jnxProductVariationTXP OBJECT IDENTIFIER ::= { jnxProductVariation 37 }
  jnxChassisTXP          OBJECT IDENTIFIER ::= { jnxChassis          37 }

  jnxSlotTXP             OBJECT IDENTIFIER ::= { jnxSlot             37 }
    jnxTXPSlotSIB        OBJECT IDENTIFIER ::= { jnxSlotTXP 1  }
    jnxTXPSlotHM         OBJECT IDENTIFIER ::= { jnxSlotTXP 2  }
    jnxTXPSlotPower      OBJECT IDENTIFIER ::= { jnxSlotTXP 3  }
    jnxTXPSlotFan        OBJECT IDENTIFIER ::= { jnxSlotTXP 4  }
    jnxTXPSlotCB         OBJECT IDENTIFIER ::= { jnxSlotTXP 5  }
    jnxTXPSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotTXP 6  }
    jnxTXPSlotCIP        OBJECT IDENTIFIER ::= { jnxSlotTXP 7  }
    jnxTXPSlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotTXP 8  }
    jnxTXPSlotLCC        OBJECT IDENTIFIER ::= { jnxSlotTXP 9  }
    jnxTXPSlotSFC        OBJECT IDENTIFIER ::= { jnxSlotTXP 10  }

  jnxMediaCardSpaceTXP      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    37 }
    jnxTXPMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceTXP 1 }

  jnxMidplaneTXP         OBJECT IDENTIFIER ::= { jnxBackplane        37 }

  jnxModuleTXP           OBJECT IDENTIFIER ::= { jnxModule    37 }
    jnxTXPSIB            OBJECT IDENTIFIER ::= { jnxModuleTXP 1  }
    jnxTXPHM             OBJECT IDENTIFIER ::= { jnxModuleTXP 2  }
    jnxTXPPower          OBJECT IDENTIFIER ::= { jnxModuleTXP 3  }
    jnxTXPFan            OBJECT IDENTIFIER ::= { jnxModuleTXP 4  }
    jnxTXPCB             OBJECT IDENTIFIER ::= { jnxModuleTXP 5  }
    jnxTXPFPB            OBJECT IDENTIFIER ::= { jnxModuleTXP 6  }
    jnxTXPCIP            OBJECT IDENTIFIER ::= { jnxModuleTXP 7  }
    jnxTXPSPMB           OBJECT IDENTIFIER ::= { jnxModuleTXP 8  }
    jnxTXPLCC            OBJECT IDENTIFIER ::= { jnxModuleTXP 9  }
    jnxTXPSFC            OBJECT IDENTIFIER ::= { jnxModuleTXP 10 }

--
-- JCS - Juniper Control System
--

  jnxProductLineJCS      OBJECT IDENTIFIER ::= { jnxProductLine      38 }
  jnxProductNameJCS      OBJECT IDENTIFIER ::= { jnxProductName      38 }
  jnxProductModelJCS     OBJECT IDENTIFIER ::= { jnxProductModel     38 }
  jnxProductVariationJCS OBJECT IDENTIFIER ::= { jnxProductVariation 38 }
  jnxChassisJCS          OBJECT IDENTIFIER ::= { jnxChassis          38 }

  jnxSlotJCS             OBJECT IDENTIFIER ::= { jnxSlot             38 }
    jnxJCSSlotHM         OBJECT IDENTIFIER ::= { jnxSlotJCS 1  }
    jnxJCSSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotJCS 2  }

  jnxMediaCardSpaceJCS      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    38 }
    jnxJCSMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJCS 1 }

  jnxMidplaneJCS         OBJECT IDENTIFIER ::= { jnxBackplane        38 }

  jnxModuleJCS           OBJECT IDENTIFIER ::= { jnxModule    38 }
    jnxJCSHM             OBJECT IDENTIFIER ::= { jnxModuleJCS 1  }
    jnxJCSFPC            OBJECT IDENTIFIER ::= { jnxModuleJCS 2 }
    jnxJCSPIC            OBJECT IDENTIFIER ::= { jnxModuleJCS 3 }

  jnxJCSBBD              OBJECT IDENTIFIER ::= { jnxJCSHM     1 }

--
-- SRX240 (Vidar)
--
  jnxProductLineSRX240       OBJECT IDENTIFIER ::= { jnxProductLine 39 }
  jnxProductNameSRX240       OBJECT IDENTIFIER ::= { jnxProductName 39 }
  jnxChassisSRX240           OBJECT IDENTIFIER ::= { jnxChassis     39 }

  jnxSlotSRX240              OBJECT IDENTIFIER ::= { jnxSlot 39 }
    jnxSRX240SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX240  1  }
    jnxSRX240SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX240  2  }
    jnxSRX240SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX240  3  }
    jnxSRX240SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX240  4  }

  jnxMediaCardSpaceSRX240    OBJECT IDENTIFIER ::= { jnxMediaCardSpace 39 }
    jnxSRX240MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX240  1 }

  jnxMidplaneSRX240         OBJECT IDENTIFIER ::= { jnxBackplane 39 }

  jnxModuleSRX240            OBJECT IDENTIFIER ::= { jnxModule 39 }
    jnxSRX240FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX240 1  }
    jnxSRX240RE              OBJECT IDENTIFIER ::= { jnxModuleSRX240 2  }
    jnxSRX240Power           OBJECT IDENTIFIER ::= { jnxModuleSRX240 3  }
    jnxSRX240Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX240 4  }

--
-- SRX650 (Thor)
--
  jnxProductLineSRX650       OBJECT IDENTIFIER ::= { jnxProductLine 40 }
  jnxProductNameSRX650       OBJECT IDENTIFIER ::= { jnxProductName 40 }
  jnxChassisSRX650           OBJECT IDENTIFIER ::= { jnxChassis     40 }

  jnxSlotSRX650              OBJECT IDENTIFIER ::= { jnxSlot 40 }
    jnxSRX650SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX650  1  }
    jnxSRX650SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX650  2  }
    jnxSRX650SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX650  3  }
    jnxSRX650SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX650  4  }

  jnxMediaCardSpaceSRX650    OBJECT IDENTIFIER ::= { jnxMediaCardSpace 40 }
    jnxSRX650MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX650  1 }

  jnxMidplaneSRX650         OBJECT IDENTIFIER ::= { jnxBackplane 40 }

  jnxModuleSRX650            OBJECT IDENTIFIER ::= { jnxModule 40 }
    jnxSRX650FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX650 1  }
    jnxSRX650RE              OBJECT IDENTIFIER ::= { jnxModuleSRX650 2  }
    jnxSRX650Power           OBJECT IDENTIFIER ::= { jnxModuleSRX650 3  }
    jnxSRX650Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX650 4  }

--
-- SRX100
--
  jnxProductLineSRX100       OBJECT IDENTIFIER ::= { jnxProductLine      41 }
  jnxProductNameSRX100       OBJECT IDENTIFIER ::= { jnxProductName      41 }
  jnxChassisSRX100           OBJECT IDENTIFIER ::= { jnxChassis          41 }

  jnxSlotSRX100              OBJECT IDENTIFIER ::= { jnxSlot    41 }
    jnxSRX100SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX100  1  }
    jnxSRX100SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX100  2  }
    jnxSRX100SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX100  3  }
    jnxSRX100SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX100  4  }

  jnxMediaCardSpaceSRX100    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       41 }
    jnxSRX100MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX100  1 }

  jnxMidplaneSRX100         OBJECT IDENTIFIER ::= { jnxBackplane 41 }

  jnxModuleSRX100            OBJECT IDENTIFIER ::= { jnxModule    41 }
    jnxSRX100FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX100 1  }
    jnxSRX100RE              OBJECT IDENTIFIER ::= { jnxModuleSRX100 2  }
    jnxSRX100Power           OBJECT IDENTIFIER ::= { jnxModuleSRX100 3  }
    jnxSRX100Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX100 4  }

--
-- ESR1000V
--
  jnxProductLineLN1000V      OBJECT IDENTIFIER ::= { jnxProductLine 42 }
  jnxProductNameLN1000V      OBJECT IDENTIFIER ::= { jnxProductName 42 }
  jnxProductModelLN1000V     OBJECT IDENTIFIER ::= { jnxProductModel 42 }
  jnxProductVariationLN1000V OBJECT IDENTIFIER ::= { jnxProductVariation 42 }
  jnxChassisLN1000V          OBJECT IDENTIFIER ::= { jnxChassis 42 }

  jnxMediaCardSpaceLN1000V   OBJECT IDENTIFIER ::= { jnxMediaCardSpace 42 }
    jnxLN1000VMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceLN1000V 1 }

  jnxMidplaneLN1000V         OBJECT IDENTIFIER ::= { jnxBackplane 42 }

  jnxSlotLN1000V             OBJECT IDENTIFIER ::= { jnxSlot 42 }
    jnxLN1000VSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotLN1000V  1  }
    jnxLN1000VSlotRE         OBJECT IDENTIFIER ::= { jnxSlotLN1000V  2  }
    jnxLN1000VSlotPower      OBJECT IDENTIFIER ::= { jnxSlotLN1000V  3  }
    jnxLN1000VSlotFan        OBJECT IDENTIFIER ::= { jnxSlotLN1000V  4  }

--
--EX2200 (Jasmine)
--
  jnxProductLineEX2200      OBJECT IDENTIFIER ::= { jnxProductLine      43 }
  jnxProductNameEX2200      OBJECT IDENTIFIER ::= { jnxProductName      43 }
  jnxProductModelEX2200     OBJECT IDENTIFIER ::= { jnxProductModel     43 }
  jnxProductVariationEX2200 OBJECT IDENTIFIER ::= { jnxProductVariation 43 }
    jnxProductEX2200port24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX2200 1 }
    jnxProductEX2200port24P    OBJECT IDENTIFIER ::= { jnxProductVariationEX2200 2 }
    jnxProductEX2200port48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX2200 3 }
    jnxProductEX2200port48P    OBJECT IDENTIFIER ::= { jnxProductVariationEX2200 4 }
    jnxProductEX2200Cport12T   OBJECT IDENTIFIER ::= { jnxProductVariationEX2200 5 }
    jnxProductEX2200Cport12P   OBJECT IDENTIFIER ::= { jnxProductVariationEX2200 6 }
    jnxProductEX2200port24TDC    OBJECT IDENTIFIER ::= { jnxProductVariationEX2200 7 }

  jnxChassisEX2200          OBJECT IDENTIFIER ::= { jnxChassis          43 }

  jnxSlotEX2200             OBJECT IDENTIFIER ::= { jnxSlot             43 }
    jnxEX2200SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX2200 1 }
      jnxEX2200SlotPower    OBJECT IDENTIFIER ::= { jnxEX2200SlotFPC 1 }
      jnxEX2200SlotFan      OBJECT IDENTIFIER ::= { jnxEX2200SlotFPC 2 }
      jnxEX2200SlotRE       OBJECT IDENTIFIER ::= { jnxEX2200SlotFPC 3 }

  jnxMediaCardSpaceEX2200      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    43 }
    jnxEX2200MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX2200 1 }

  jnxModuleEX2200            OBJECT IDENTIFIER ::= { jnxModule    43 }
    jnxEX2200FPC             OBJECT IDENTIFIER ::= { jnxModuleEX2200 1 }
      jnxEX2200Power         OBJECT IDENTIFIER ::= { jnxEX2200FPC 1 }
      jnxEX2200Fan           OBJECT IDENTIFIER ::= { jnxEX2200FPC 2 }
      jnxEX2200RE            OBJECT IDENTIFIER ::= { jnxEX2200FPC 3 }

--
-- EX4500
--

  jnxProductLineEX4500      OBJECT IDENTIFIER ::= { jnxProductLine      44 }
  jnxProductNameEX4500      OBJECT IDENTIFIER ::= { jnxProductName      44 }
  jnxProductModelEX4500     OBJECT IDENTIFIER ::= { jnxProductModel     44 }
  jnxProductVariationEX4500 OBJECT IDENTIFIER ::= { jnxProductVariation 44 }
    jnxProductEX4500port40F    OBJECT IDENTIFIER ::= { jnxProductVariationEX4500 1 }
    jnxProductEX4500port20F    OBJECT IDENTIFIER ::= { jnxProductVariationEX4500 2 }

  jnxChassisEX4500          OBJECT IDENTIFIER ::= { jnxChassis          44 }
    jnxEX4500RE0            OBJECT IDENTIFIER ::= { jnxChassisEX4500 1  }
    jnxEX4500RE1            OBJECT IDENTIFIER ::= { jnxChassisEX4500 2  }
  jnxSlotEX4500             OBJECT IDENTIFIER ::= { jnxSlot             44 }
    jnxEX4500SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX4500 1 }
      jnxEX4500SlotPower    OBJECT IDENTIFIER ::= { jnxEX4500SlotFPC 1 }
      jnxEX4500SlotFan      OBJECT IDENTIFIER ::= { jnxEX4500SlotFPC 2 }
      jnxEX4500SlotRE       OBJECT IDENTIFIER ::= { jnxEX4500SlotFPC 3 }

  jnxMediaCardSpaceEX4500      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    44 }
    jnxEX4500MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4500 1 }

  jnxModuleEX4500            OBJECT IDENTIFIER ::= { jnxModule    44 }
    jnxEX4500FPC             OBJECT IDENTIFIER ::= { jnxModuleEX4500 1 }
      jnxEX4500Power         OBJECT IDENTIFIER ::= { jnxEX4500FPC 1 }
      jnxEX4500Fan           OBJECT IDENTIFIER ::= { jnxEX4500FPC 2 }
      jnxEX4500RE            OBJECT IDENTIFIER ::= { jnxEX4500FPC 3 }

--
--DCF (not used; see QFXInterconnect and QFXNode)
--

   jnxProductLineFXSeries       OBJECT IDENTIFIER ::= { jnxProductLine      45 }
   jnxProductNameFXSeries       OBJECT IDENTIFIER ::= { jnxProductName      45 }
   jnxProductModelFXSeries      OBJECT IDENTIFIER ::= { jnxProductModel     45 }
   jnxProductVariationFXSeries  OBJECT IDENTIFIER ::= { jnxProductVariation 45 }
   jnxChassisFXChassis          OBJECT IDENTIFIER ::= { jnxChassis          45 }
   jnxProductFX1600port         OBJECT IDENTIFIER ::= { jnxProductVariationFXSeries 1 }
   jnxProductFX2160port         OBJECT IDENTIFIER ::= { jnxProductVariationFXSeries 2 }

--
-- IBM4274M02J02M
--

  jnxProductLineIBM4274M02J02M      OBJECT IDENTIFIER ::= { jnxProductLine      46 }
  jnxProductNameIBM4274M02J02M      OBJECT IDENTIFIER ::= { jnxProductName      46 }
  jnxProductModelIBM4274M02J02M     OBJECT IDENTIFIER ::= { jnxProductModel     46 }
  jnxProductVariationIBM4274M02J02M OBJECT IDENTIFIER ::= { jnxProductVariation 46 }
  jnxChassisIBM4274M02J02M          OBJECT IDENTIFIER ::= { jnxChassis          46 }

  jnxSlotIBM4274M02J02M             OBJECT IDENTIFIER ::= { jnxSlot             46 }
  jnxIBM4274M02J02MSlotFPC          OBJECT IDENTIFIER ::= { jnxSlotIBM4274M02J02M 1  }
  jnxIBM4274M02J02MSlotHM           OBJECT IDENTIFIER ::= { jnxSlotIBM4274M02J02M 2  }
  jnxIBM4274M02J02MSlotPower        OBJECT IDENTIFIER ::= { jnxSlotIBM4274M02J02M 3  }
  jnxIBM4274M02J02MSlotFan          OBJECT IDENTIFIER ::= { jnxSlotIBM4274M02J02M 4  }
  jnxIBM4274M02J02MSlotCB           OBJECT IDENTIFIER ::= { jnxSlotIBM4274M02J02M 5  }
  jnxIBM4274M02J02MSlotFPB          OBJECT IDENTIFIER ::= { jnxSlotIBM4274M02J02M 6  }
  jnxMediaCardSpaceIBM4274M02J02M   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    46 }
  jnxIBM4274M02J02MMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274M02J02M 1 }
  jnxIBM4274M02J02MMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274M02J02M 2 }

  jnxMidplaneIBM4274M02J02M         OBJECT IDENTIFIER ::= { jnxBackplane         46 }

--
-- IBM4274M06J06M
--

  jnxProductLineIBM4274M06J06M      OBJECT IDENTIFIER ::= { jnxProductLine      47 }
  jnxProductNameIBM4274M06J06M      OBJECT IDENTIFIER ::= { jnxProductName      47 }
  jnxProductModelIBM4274M06J06M     OBJECT IDENTIFIER ::= { jnxProductModel     47 }
  jnxProductVariationIBM4274M06J06M OBJECT IDENTIFIER ::= { jnxProductVariation 47 }
  jnxChassisIBM4274M06J06M          OBJECT IDENTIFIER ::= { jnxChassis          47 }

  jnxSlotIBM4274M06J06M             OBJECT IDENTIFIER ::= { jnxSlot             47 }
    jnxIBM4274M06J06MSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM4274M06J06M 1  }
    jnxIBM4274M06J06MSlotHM         OBJECT IDENTIFIER ::= { jnxSlotIBM4274M06J06M 2  }
    jnxIBM4274M06J06MSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIBM4274M06J06M 3  }
    jnxIBM4274M06J06MSlotFan        OBJECT IDENTIFIER ::= { jnxSlotIBM4274M06J06M 4  }
    jnxIBM4274M06J06MSlotCB         OBJECT IDENTIFIER ::= { jnxSlotIBM4274M06J06M 5  }
    jnxIBM4274M06J06MSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotIBM4274M06J06M 6  }

  jnxMediaCardSpaceIBM4274M06J06M      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    47 }
  jnxIBM4274M06J06MMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274M06J06M 1 }
  jnxIBM4274M06J06MMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274M06J06M 2 }

  jnxMidplaneIBM4274M06J06M         OBJECT IDENTIFIER ::= { jnxBackplane        47 }

--
-- IBM4274M11J11M
--

  jnxProductLineIBM4274M11J11M      OBJECT IDENTIFIER ::= { jnxProductLine      48 }
  jnxProductNameIBM4274M11J11M      OBJECT IDENTIFIER ::= { jnxProductName      48 }
  jnxProductModelIBM4274M11J11M     OBJECT IDENTIFIER ::= { jnxProductModel     48 }
  jnxProductVariationIBM4274M11J11M OBJECT IDENTIFIER ::= { jnxProductVariation 48 }
  jnxChassisIBM4274M11J11M          OBJECT IDENTIFIER ::= { jnxChassis          48 }

  jnxSlotIBM4274M11J11M             OBJECT IDENTIFIER ::= { jnxSlot             48 }
    jnxIBM4274M11J11MSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM4274M11J11M 1  }
    jnxIBM4274M11J11MSlotHM         OBJECT IDENTIFIER ::= { jnxSlotIBM4274M11J11M 2  }
    jnxIBM4274M11J11MSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIBM4274M11J11M 3  }
    jnxIBM4274M11J11MSlotFan        OBJECT IDENTIFIER ::= { jnxSlotIBM4274M11J11M 4  }
    jnxIBM4274M11J11MSlotCB         OBJECT IDENTIFIER ::= { jnxSlotIBM4274M11J11M 5  }
    jnxIBM4274M11J11MSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotIBM4274M11J11M 6  }

  jnxMediaCardSpaceIBM4274M11J11M      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    48 }
    jnxIBM4274M11J11MMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274M11J11M 1 }
    jnxIBM4274M11J11MMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274M11J11M 2 }

  jnxMidplaneIBM4274M11J11M         OBJECT IDENTIFIER ::= { jnxBackplane        48 }

 --
 -- SRX1400
 --

   jnxProductLineSRX1400      OBJECT IDENTIFIER ::= { jnxProductLine      49 }
   jnxProductNameSRX1400      OBJECT IDENTIFIER ::= { jnxProductName      49 }
   jnxProductModelSRX1400     OBJECT IDENTIFIER ::= { jnxProductModel     49 }
   jnxProductVariationSRX1400 OBJECT IDENTIFIER ::= { jnxProductVariation 49 }
   jnxChassisSRX1400          OBJECT IDENTIFIER ::= { jnxChassis          49 }

   jnxSlotSRX1400             OBJECT IDENTIFIER ::= { jnxSlot             49 }
     jnxSRX1400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX1400 1  }
     jnxSRX1400SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX1400 2  }
     jnxSRX1400SlotPower      OBJECT IDENTIFIER ::= { jnxSlotSRX1400 3  }
     jnxSRX1400SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX1400 4  }
     jnxSRX1400SlotCB         OBJECT IDENTIFIER ::= { jnxSlotSRX1400 5  }
     jnxSRX1400SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotSRX1400 6  }

   jnxMediaCardSpaceSRX1400   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    49 }
   jnxSRX1400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX1400 1 }

   jnxMidplaneSRX1400         OBJECT IDENTIFIER ::= { jnxBackplane        49 }

--
-- IBM4274S58J58S (A40 IBM OEM)
--

  jnxProductLineIBM4274S58J58S      OBJECT IDENTIFIER ::= { jnxProductLine      50 }
  jnxProductNameIBM4274S58J58S      OBJECT IDENTIFIER ::= { jnxProductName      50 }
  jnxProductModelIBM4274S58J58S     OBJECT IDENTIFIER ::= { jnxProductModel     50 }
  jnxProductVariationIBM4274S58J58S OBJECT IDENTIFIER ::= { jnxProductVariation 50 }
  jnxChassisIBM4274S58J58S          OBJECT IDENTIFIER ::= { jnxChassis          50 }

  jnxSlotIBM4274S58J58S             OBJECT IDENTIFIER ::= { jnxSlot             50 }
    jnxIBM4274S58J58SSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S58J58S 1  }
    jnxIBM4274S58J58SSlotHM         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S58J58S 2  }
    jnxIBM4274S58J58SSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIBM4274S58J58S 3  }
    jnxIBM4274S58J58SSlotFan        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S58J58S 4  }
    jnxIBM4274S58J58SSlotCB         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S58J58S 5  }
    jnxIBM4274S58J58SSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S58J58S 6  }

  jnxMediaCardSpaceIBM4274S58J58S      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    50 }
    jnxIBM4274S58J58SMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274S58J58S 1 }

  jnxMidplaneIBM4274S58J58S         OBJECT IDENTIFIER ::= { jnxBackplane        50 }

--
-- IBM4274S56J56S (A20 IBM OEM)
--

  jnxProductLineIBM4274S56J56S      OBJECT IDENTIFIER ::= { jnxProductLine      51 }
  jnxProductNameIBM4274S56J56S      OBJECT IDENTIFIER ::= { jnxProductName      51 }
  jnxProductModelIBM4274S56J56S     OBJECT IDENTIFIER ::= { jnxProductModel     51 }
  jnxProductVariationIBM4274S56J56S OBJECT IDENTIFIER ::= { jnxProductVariation 51 }
  jnxChassisIBM4274S56J56S          OBJECT IDENTIFIER ::= { jnxChassis          51 }

  jnxSlotIBM4274S56J56S             OBJECT IDENTIFIER ::= { jnxSlot             51 }
    jnxIBM4274S56J56SSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S56J56S 1  }
    jnxIBM4274S56J56SSlotHM         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S56J56S 2  }
    jnxIBM4274S56J56SSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIBM4274S56J56S 3  }
    jnxIBM4274S56J56SSlotFan        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S56J56S 4  }
    jnxIBM4274S56J56SSlotCB         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S56J56S 5  }
    jnxIBM4274S56J56SSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S56J56S 6  }

  jnxMediaCardSpaceIBM4274S56J56S      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    51 }
  jnxIBM4274S56J56SMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274S56J56S 1 }

  jnxMidplaneIBM4274S56J56S         OBJECT IDENTIFIER ::= { jnxBackplane        51 }

--
-- IBM4274S36J36S (A10 IBM OEM)
--

  jnxProductLineIBM4274S36J36S      OBJECT IDENTIFIER ::= { jnxProductLine      52 }
  jnxProductNameIBM4274S36J36S      OBJECT IDENTIFIER ::= { jnxProductName      52 }
  jnxProductModelIBM4274S36J36S     OBJECT IDENTIFIER ::= { jnxProductModel     52 }
  jnxProductVariationIBM4274S36J36S OBJECT IDENTIFIER ::= { jnxProductVariation 52 }
  jnxChassisIBM4274S36J36S          OBJECT IDENTIFIER ::= { jnxChassis          52 }

  jnxSlotIBM4274S36J36S             OBJECT IDENTIFIER ::= { jnxSlot             52 }
    jnxIBM4274S36J36SSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S36J36S 1  }
    jnxIBM4274S36J36SSlotHM         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S36J36S 2  }
    jnxIBM4274S36J36SSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIBM4274S36J36S 3  }
    jnxIBM4274S36J36SSlotFan        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S36J36S 4  }
    jnxIBM4274S36J36SSlotCB         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S36J36S 5  }
    jnxIBM4274S36J36SSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S36J36S 6  }

  jnxMediaCardSpaceIBM4274S36J36S   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    52 }
  jnxIBM4274S36J36SMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274S36J36S 1 }

  jnxMidplaneIBM4274S36J36S         OBJECT IDENTIFIER ::= { jnxBackplane        52 }

--
-- IBM4274S34J34S (A2 IBM OEM)
--

  jnxProductLineIBM4274S34J34S      OBJECT IDENTIFIER ::= { jnxProductLine      53 }
  jnxProductNameIBM4274S34J34S      OBJECT IDENTIFIER ::= { jnxProductName      53 }
  jnxProductModelIBM4274S34J34S     OBJECT IDENTIFIER ::= { jnxProductModel     53 }
  jnxProductVariationIBM4274S34J34S OBJECT IDENTIFIER ::= { jnxProductVariation 53 }
  jnxChassisIBM4274S34J34S          OBJECT IDENTIFIER ::= { jnxChassis          53 }

  jnxSlotIBM4274S34J34S             OBJECT IDENTIFIER ::= { jnxSlot             53 }
    jnxIBM4274S34J34SSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S34J34S 1  }
    jnxIBM4274S34J34SSlotHM         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S34J34S 2  }
    jnxIBM4274S34J34SSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIBM4274S34J34S 3  }
    jnxIBM4274S34J34SSlotFan        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S34J34S 4  }
    jnxIBM4274S34J34SSlotCB         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S34J34S 5  }
    jnxIBM4274S34J34SSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S34J34S 6  }

  jnxMediaCardSpaceIBM4274S34J34S   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    53 }
  jnxIBM4274S34J34SMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274S34J34S 1 }

  jnxMidplaneIBM4274S34J34S         OBJECT IDENTIFIER ::= { jnxBackplane        53 }

--
-- IBM 427348E J48E (EX4200)
--

  jnxProductLineIBM427348EJ48E      OBJECT IDENTIFIER ::= { jnxProductLine      54 }
  jnxProductNameIBM427348EJ48E      OBJECT IDENTIFIER ::= { jnxProductName      54 }
  jnxProductModelIBM427348EJ48E     OBJECT IDENTIFIER ::= { jnxProductModel     54 }
  jnxProductVariationIBM427348EJ48E OBJECT IDENTIFIER ::= { jnxProductVariation 54 }
    jnxProductIBM427348EJ48Eport24T    OBJECT IDENTIFIER ::= { jnxProductVariationIBM427348EJ48E 1 }
    jnxProductIBM427348EJ48Eport24P    OBJECT IDENTIFIER ::= { jnxProductVariationIBM427348EJ48E 2 }
    jnxProductIBM427348EJ48Eport48T    OBJECT IDENTIFIER ::= { jnxProductVariationIBM427348EJ48E 3 }
    jnxProductIBM427348EJ48Eport48P    OBJECT IDENTIFIER ::= { jnxProductVariationIBM427348EJ48E 4 }
    jnxProductIBM427348EJ48Eport24F    OBJECT IDENTIFIER ::= { jnxProductVariationIBM427348EJ48E 5 }

  jnxChassisIBM427348EJ48E          OBJECT IDENTIFIER ::= { jnxChassis          54 }
    jnxIBM427348EJ48ERE0            OBJECT IDENTIFIER ::= { jnxChassisIBM427348EJ48E 1  }
    jnxIBM427348EJ48ERE1            OBJECT IDENTIFIER ::= { jnxChassisIBM427348EJ48E 2  }
  jnxSlotIBM427348EJ48E             OBJECT IDENTIFIER ::= { jnxSlot             54 }
    jnxIBM427348EJ48ESlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM427348EJ48E 1 }
      jnxIBM427348EJ48ESlotPower    OBJECT IDENTIFIER ::= { jnxIBM427348EJ48ESlotFPC 1 }
      jnxIBM427348EJ48ESlotFan      OBJECT IDENTIFIER ::= { jnxIBM427348EJ48ESlotFPC 2 }

  jnxMediaCardSpaceIBM427348EJ48E      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    54 }
    jnxIBM427348EJ48EMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM427348EJ48E 1 }

 jnxModuleIBM427348EJ48E            OBJECT IDENTIFIER ::= { jnxModule    54 }
    jnxIBM427348EJ48EFPC            OBJECT IDENTIFIER ::= { jnxModuleIBM427348EJ48E 1 }
      jnxIBM427348EJ48EPower        OBJECT IDENTIFIER ::= { jnxIBM427348EJ48EFPC 1 }
      jnxIBM427348EJ48EFan          OBJECT IDENTIFIER ::= { jnxIBM427348EJ48EFPC 2 }

--
-- IBM 4274E08 J08E  (EX8208)
--

  jnxProductLineIBM4274E08J08E        OBJECT IDENTIFIER ::= { jnxProductLine      55 }
  jnxProductNameIBM4274E08J08E        OBJECT IDENTIFIER ::= { jnxProductName      55 }
  jnxProductModelIBM4274E08J08E       OBJECT IDENTIFIER ::= { jnxProductModel     55 }
  jnxProductVariationIBM4274E08J08E   OBJECT IDENTIFIER ::= { jnxProductVariation 55 }
  jnxChassisIBM4274E08J08E            OBJECT IDENTIFIER ::= { jnxChassis          55 }

  jnxSlotIBM4274E08J08E               OBJECT IDENTIFIER ::= { jnxSlot             55 }
    jnxIBM4274E08J08ESlotFPC         OBJECT IDENTIFIER ::= { jnxSlotIBM4274E08J08E   1 }
                            -- Flexible Port Concentrator
      jnxIBM4274E08J08ESlot48S       OBJECT IDENTIFIER ::= { jnxIBM4274E08J08ESlotFPC  1 }
      jnxIBM4274E08J08ESlot48T       OBJECT IDENTIFIER ::= { jnxIBM4274E08J08ESlotFPC  2 }
      jnxIBM4274E08J08ESlot8XS       OBJECT IDENTIFIER ::= { jnxIBM4274E08J08ESlotFPC  3 }
    jnxIBM4274E08J08EHM              OBJECT IDENTIFIER ::= { jnxSlotIBM4274E08J08E   3 }
                            -- Host Module (also called Routing Engine)
    jnxIBM4274E08J08ESlotPower       OBJECT IDENTIFIER ::= { jnxSlotIBM4274E08J08E   4 }
    jnxIBM4274E08J08ESlotFan         OBJECT IDENTIFIER ::= { jnxSlotIBM4274E08J08E   5 }
      jnxIBM4274E08J08ESlotFT        OBJECT IDENTIFIER ::= { jnxIBM4274E08J08ESlotFan  1 }
    jnxIBM4274E08J08ESlotCBD         OBJECT IDENTIFIER ::= { jnxSlotIBM4274E08J08E   6 }
                            -- Control Board

  jnxMediaCardSpaceIBM4274E08J08E     OBJECT IDENTIFIER ::= { jnxMediaCardSpace   55 }
    jnxIBM4274E08J08EMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274E08J08E  1 }

  jnxBackplaneIBM4274E08J08E          OBJECT IDENTIFIER ::= { jnxBackplane        55 }

--
-- IBM 4274E16 J16E (EX8216)
--

  jnxProductLineIBM4274E16J16E       OBJECT IDENTIFIER ::= { jnxProductLine      56 }
  jnxProductNameIBM4274E16J16E       OBJECT IDENTIFIER ::= { jnxProductName      56 }
  jnxProductModelIBM4274E16J16E      OBJECT IDENTIFIER ::= { jnxProductModel     56 }
  jnxProductVariationIBM4274E16J16E  OBJECT IDENTIFIER ::= { jnxProductVariation 56 }
  jnxChassisIBM4274E16J16E           OBJECT IDENTIFIER ::= { jnxChassis          56 }

  jnxSlotIBM4274E16J16E              OBJECT IDENTIFIER ::= { jnxSlot             56 }
    jnxIBM4274E16J16ESlotFPC         OBJECT IDENTIFIER ::= { jnxSlotIBM4274E16J16E   1 }
                            -- Flexible Port Concentrator
      jnxIBM4274E16J16ESlot48S       OBJECT IDENTIFIER ::= { jnxIBM4274E16J16ESlotFPC   1 }
      jnxIBM4274E16J16ESlot48T       OBJECT IDENTIFIER ::= { jnxIBM4274E16J16ESlotFPC   2 }
      jnxIBM4274E16J16ESlot8XS       OBJECT IDENTIFIER ::= { jnxIBM4274E16J16ESlotFPC   3 }
    jnxIBM4274E16J16ESIB             OBJECT IDENTIFIER ::= { jnxSlotIBM4274E16J16E   2 }
                            -- Swtich Interface Board
    jnxIBM4274E16J16EHM              OBJECT IDENTIFIER ::= { jnxSlotIBM4274E16J16E   3 }
                            -- Host Module (also called Routing Engine)
    jnxIBM4274E16J16ESlotPower       OBJECT IDENTIFIER ::= { jnxSlotIBM4274E16J16E   4 }
    jnxIBM4274E16J16ESlotFan         OBJECT IDENTIFIER ::= { jnxSlotIBM4274E16J16E   5 }
      jnxIBM4274E16J16ESlotFT        OBJECT IDENTIFIER ::= { jnxIBM4274E16J16ESlotFan   1 }
      jnxIBM4274E16J16ESlotRFT       OBJECT IDENTIFIER ::= { jnxIBM4274E16J16ESlotFan   2 }
    jnxIBM4274E16J16ESlotCBD         OBJECT IDENTIFIER ::= { jnxSlotIBM4274E16J16E   6 }
                            -- Control Board

  jnxMediaCardSpaceIBM4274E16J16E    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   56 }
    jnxIBM4274E16J16EMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274E16J16E 1 }

  jnxMidplaneIBM4274E16J16E          OBJECT IDENTIFIER ::= { jnxBackplane         56 }

--
-- MX80
--

  jnxProductLineMX80      OBJECT IDENTIFIER ::= { jnxProductLine      57 }
  jnxProductNameMX80      OBJECT IDENTIFIER ::= { jnxProductName      57 }
  jnxProductModelMX80     OBJECT IDENTIFIER ::= { jnxProductModel     57 }
  jnxProductVariationMX80 OBJECT IDENTIFIER ::= { jnxProductVariation 57 }
    jnxProductMX80        OBJECT IDENTIFIER ::= { jnxProductVariationMX80 1 }
    jnxProductMX80-48T    OBJECT IDENTIFIER ::= { jnxProductVariationMX80 2 }
    jnxProductMX80-T      OBJECT IDENTIFIER ::= { jnxProductVariationMX80 3 }
    jnxProductMX80-P      OBJECT IDENTIFIER ::= { jnxProductVariationMX80 4 }
  jnxChassisMX80          OBJECT IDENTIFIER ::= { jnxChassis          57 }

  jnxSlotMX80             OBJECT IDENTIFIER ::= { jnxSlot     57 }
    jnxMX80SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX80 1  }
    jnxMX80SlotCFEB       OBJECT IDENTIFIER ::= { jnxSlotMX80 2  }
    jnxMX80SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMX80 3  }
    jnxMX80SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX80 4  }
    jnxMX80SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX80 5  }

  jnxMediaCardSpaceMX80      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    57 }
    jnxMX80MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX80 1 }
    jnxMX80MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX80 2 }

  jnxMidplaneMX80         OBJECT IDENTIFIER ::= { jnxBackplane        57 }

  jnxModuleMX80           OBJECT IDENTIFIER ::= { jnxModule     57 }
    jnxMX80FPC            OBJECT IDENTIFIER ::= { jnxModuleMX80 1  }
    jnxMX80CFEB           OBJECT IDENTIFIER ::= { jnxModuleMX80 2  }
    jnxMX80RE             OBJECT IDENTIFIER ::= { jnxModuleMX80 3  }
    jnxMX80Power          OBJECT IDENTIFIER ::= { jnxModuleMX80 4  }
    jnxMX80PowerAC        OBJECT IDENTIFIER ::= { jnxModuleMX80 5  }
    jnxMX80Fan            OBJECT IDENTIFIER ::= { jnxModuleMX80 6  }


--
-- SRX220 (VALI)
--
  jnxProductLineSRX220       OBJECT IDENTIFIER ::= { jnxProductLine      58 }
  jnxProductNameSRX220       OBJECT IDENTIFIER ::= { jnxProductName      58 }
  jnxChassisSRX220           OBJECT IDENTIFIER ::= { jnxChassis          58 }

  jnxSlotSRX220              OBJECT IDENTIFIER ::= { jnxSlot    58 }
    jnxSRX220SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX220  1  }
    jnxSRX220SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX220  2  }
    jnxSRX220SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX220  3  }
    jnxSRX220SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX220  4  }

  jnxMediaCardSpaceSRX220    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       58 }
    jnxSRX220MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX220  1 }

  jnxMidplaneSRX220         OBJECT IDENTIFIER ::= { jnxBackplane 58 }

  jnxModuleSRX220            OBJECT IDENTIFIER ::= { jnxModule    58 }
    jnxSRX220FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX220 1  }
    jnxSRX220RE              OBJECT IDENTIFIER ::= { jnxModuleSRX220 2  }
    jnxSRX220Power           OBJECT IDENTIFIER ::= { jnxModuleSRX220 3  }
    jnxSRX220Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX220 4  }




--
-- EX_XRE
--

  jnxProductLineEXXRE      OBJECT IDENTIFIER ::= { jnxProductLine      59 }
  jnxProductNameEXXRE      OBJECT IDENTIFIER ::= { jnxProductName      59 }
  jnxProductModelEXXRE     OBJECT IDENTIFIER ::= { jnxProductModel     59 }
  jnxProductVariationEXXRE OBJECT IDENTIFIER ::= { jnxProductVariation 59 }
    jnxProductEXXRE        OBJECT IDENTIFIER ::= { jnxProductVariationEXXRE 1 }
  jnxChassisEXXRE          OBJECT IDENTIFIER ::= { jnxChassis          59 }
    jnxEXXRERE0            OBJECT IDENTIFIER ::= { jnxChassisEXXRE 1  }
    jnxEXXRERE1            OBJECT IDENTIFIER ::= { jnxChassisEXXRE 2  }
  jnxSlotEXXRE             OBJECT IDENTIFIER ::= { jnxSlot             59 }
    jnxEXXRESlotPower      OBJECT IDENTIFIER ::= { jnxSlotEXXRE 1 }
    jnxEXXRESlotFan        OBJECT IDENTIFIER ::= { jnxSlotEXXRE 2 }
    jnxEXXRESlotHM         OBJECT IDENTIFIER ::= { jnxSlotEXXRE 3 }
    jnxEXXRESlotLCC        OBJECT IDENTIFIER ::= { jnxSlotEXXRE 4 }

  jnxMediaCardSpaceEXXRE      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    59 }
    jnxEXXREMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEXXRE 1 }

  jnxBackplaneEXXRE           OBJECT IDENTIFIER ::= { jnxBackplane 59 }

  jnxModuleEXXRE         OBJECT IDENTIFIER    ::=   { jnxModule    59 }
    jnxEXXREPower        OBJECT IDENTIFIER    ::=   { jnxModuleEXXRE 1 }
    jnxEXXREFan          OBJECT IDENTIFIER    ::=   { jnxModuleEXXRE 2 }
    jnxEXXREHM           OBJECT IDENTIFIER    ::=   { jnxModuleEXXRE 3 }
    jnxEXXRELCC          OBJECT IDENTIFIER    ::=   { jnxModuleEXXRE 4 }

--
-- QFXInterconnect
--

  jnxProductLineQFXInterconnect OBJECT IDENTIFIER ::= { jnxProductLine      60 }
  jnxProductNameQFXInterconnect OBJECT IDENTIFIER ::= { jnxProductName      60 }
  jnxProductModelQFXInterconnect OBJECT IDENTIFIER ::= { jnxProductModel     60 }
  jnxProductVariationQFXInterconnect  OBJECT IDENTIFIER ::= { jnxProductVariation 60 }
    jnxProductQFX3008           OBJECT IDENTIFIER ::= { jnxProductVariationQFXInterconnect 1 }
    jnxProductQFXC083008        OBJECT IDENTIFIER ::= { jnxProductVariationQFXInterconnect 2 }
    jnxProductQFX3008I          OBJECT IDENTIFIER ::= { jnxProductVariationQFXInterconnect 3 }

  jnxChassisQFXInterconnect     OBJECT IDENTIFIER ::= { jnxChassis          60 }

  jnxSlotQFXInterconnect        OBJECT IDENTIFIER ::= { jnxSlot             60 }
    jnxQFXInterconnectSlotFPC   OBJECT IDENTIFIER ::= { jnxSlotQFXInterconnect   1 }
    jnxQFXInterconnectSlotHM    OBJECT IDENTIFIER ::= { jnxSlotQFXInterconnect   2 }
    jnxQFXInterconnectSlotPower OBJECT IDENTIFIER ::= { jnxSlotQFXInterconnect   3 }
    jnxQFXInterconnectSlotFan   OBJECT IDENTIFIER ::= { jnxSlotQFXInterconnect   4 }
    jnxQFXInterconnectSlotCBD   OBJECT IDENTIFIER ::= { jnxSlotQFXInterconnect   5 }
    jnxQFXInterconnectSlotFPB   OBJECT IDENTIFIER ::= { jnxSlotQFXInterconnect   6 }

  jnxMediaCardSpaceQFXInterconnect    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   60 }
    jnxQFXInterconnectMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceQFXInterconnect 1 }

  jnxMidplaneQFXInterconnect    OBJECT IDENTIFIER ::= { jnxBackplane        60 }

--
-- QFXNode
--

  jnxProductLineQFXNode       OBJECT IDENTIFIER ::= { jnxProductLine      61 }
  jnxProductNameQFXNode       OBJECT IDENTIFIER ::= { jnxProductName      61 }
  jnxProductModelQFXNode      OBJECT IDENTIFIER ::= { jnxProductModel     61 }
  jnxProductVariationQFXNode  OBJECT IDENTIFIER ::= { jnxProductVariation 61 }
    jnxProductQFX3500         OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 1 }
    jnxProductQFX5500         OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 2 }
    jnxProductQFX360016Q      OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 3 }
    jnxProductQFX350048T4Q    OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 4 }
    jnxProductQFX510024QF     OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 5 }
    jnxProductQFX510048S6QF   OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 6 }
    jnxProductQFX510096S6QF   OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 7 }
    jnxProductQFX510048C6QF   OBJECT IDENTIFIER ::= { jnxProductVariationQFXNode 8 }

  jnxChassisQFXNode           OBJECT IDENTIFIER ::= { jnxChassis          61 }

  jnxSlotQFXNode              OBJECT IDENTIFIER ::= { jnxSlot             61 }
    jnxQFXNodeSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotQFXNode   1 }
    jnxQFXNodeSlotHM          OBJECT IDENTIFIER ::= { jnxSlotQFXNode   2 }
    jnxQFXNodeSlotPower       OBJECT IDENTIFIER ::= { jnxSlotQFXNode   3 }
    jnxQFXNodeSlotFan         OBJECT IDENTIFIER ::= { jnxSlotQFXNode   4 }
    jnxQFXNodeSlotFPB         OBJECT IDENTIFIER ::= { jnxSlotQFXNode   5 }

  jnxMediaCardSpaceQFXNode    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   61 }
    jnxQFXNodeMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceQFXNode 1 }

--
-- QFXJVRE
--

   jnxProductLineQFXJVRE      OBJECT IDENTIFIER ::= { jnxProductLine      62 }
   jnxProductNameQFXJVRE      OBJECT IDENTIFIER ::= { jnxProductName      62 }
   jnxProductModelQFXJVRE     OBJECT IDENTIFIER ::= { jnxProductModel     62 }
   jnxChassisQFXJVRE          OBJECT IDENTIFIER ::= { jnxChassis          62 }

   jnxSlotQFXJVRE             OBJECT IDENTIFIER ::= { jnxSlot             62 }
    jnxQFXJVRESlotFPC         OBJECT IDENTIFIER ::= { jnxSlotQFXJVRE   1 }
    jnxQFXJVRESlotHM          OBJECT IDENTIFIER ::= { jnxSlotQFXJVRE   2 }
    jnxQFXJVRESlotPower       OBJECT IDENTIFIER ::= { jnxSlotQFXJVRE   3 }
    jnxQFXJVRESlotFan         OBJECT IDENTIFIER ::= { jnxSlotQFXJVRE   4 }
    jnxQFXJVRESlotFPB         OBJECT IDENTIFIER ::= { jnxSlotQFXJVRE   5 }

  jnxMediaCardSpaceQFXJVRE    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   62 }
    jnxQFXJVREMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceQFXJVRE 1 }

--
-- EX4300
--

  jnxProductLineEX4300      OBJECT IDENTIFIER ::= { jnxProductLine      63 }
  jnxProductNameEX4300      OBJECT IDENTIFIER ::= { jnxProductName      63 }
  jnxProductModelEX4300     OBJECT IDENTIFIER ::= { jnxProductModel     63 }
  jnxProductVariationEX4300 OBJECT IDENTIFIER ::= { jnxProductVariation 63 }
    jnxProductEX4300port24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 1 }
    jnxProductEX4300port48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 2 }
    jnxProductEX4300port48TBF    OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 3 }
    jnxProductEX4300port48TDC    OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 4 }
    jnxProductEX4300port48TDCBF    OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 5 }
    jnxProductEX4300port24P   OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 6 }
    jnxProductEX4300port48P   OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 7 }

  jnxChassisEX4300          OBJECT IDENTIFIER ::= { jnxChassis          63 }
    jnxEX4300RE0            OBJECT IDENTIFIER ::= { jnxChassisEX4300 1  }
    jnxEX4300RE1            OBJECT IDENTIFIER ::= { jnxChassisEX4300 2  }
  jnxSlotEX4300             OBJECT IDENTIFIER ::= { jnxSlot             63 }
    jnxEX4300SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX4300 1 }
      jnxEX4300SlotPower    OBJECT IDENTIFIER ::= { jnxEX4300SlotFPC 1 }
      jnxEX4300SlotFan      OBJECT IDENTIFIER ::= { jnxEX4300SlotFPC 2 }

  jnxMediaCardSpaceEX4300      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    63 }
    jnxEX4300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4300 1 }

 jnxModuleEX4300            OBJECT IDENTIFIER ::= { jnxModule    63 }
    jnxEX4300FPC            OBJECT IDENTIFIER ::= { jnxModuleEX4300 1 }
      jnxEX4300Power        OBJECT IDENTIFIER ::= { jnxEX4300FPC 1 }
      jnxEX4300Fan          OBJECT IDENTIFIER ::= { jnxEX4300FPC 2 }


-- EX4300 Fiber switch MICs
--
    jnxProductEX4300port32F    OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 8 }

--
-- SRX110
--
  jnxProductLineSRX110       OBJECT IDENTIFIER ::= { jnxProductLine      64 }
  jnxProductNameSRX110       OBJECT IDENTIFIER ::= { jnxProductName      64 }
  jnxChassisSRX110           OBJECT IDENTIFIER ::= { jnxChassis          64 }

  jnxSlotSRX110              OBJECT IDENTIFIER ::= { jnxSlot    64 }
    jnxSRX110SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX110  1  }
    jnxSRX110SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX110  2  }
    jnxSRX110SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX110  3  }
    jnxSRX110SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX110  4  }

  jnxMediaCardSpaceSRX110    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       64 }
    jnxSRX110MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX110  1 }

  jnxMidplaneSRX110          OBJECT IDENTIFIER ::= { jnxBackplane 64 }

  jnxModuleSRX110            OBJECT IDENTIFIER ::= { jnxModule    64 }
    jnxSRX110FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX110 1  }
    jnxSRX110RE              OBJECT IDENTIFIER ::= { jnxModuleSRX110 2  }
    jnxSRX110Power           OBJECT IDENTIFIER ::= { jnxModuleSRX110 3  }
    jnxSRX110Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX110 4  }

--
-- SRX120
--
-- NOTE:  These platforms no longer exist. The definitions below are being
--        retained since the index number 65 has already been allocated to it
--
  jnxProductLineSRX120       OBJECT IDENTIFIER ::= { jnxProductLine      65 }
  jnxProductNameSRX120       OBJECT IDENTIFIER ::= { jnxProductName      65 }
  jnxChassisSRX120           OBJECT IDENTIFIER ::= { jnxChassis          65 }

  jnxSlotSRX120              OBJECT IDENTIFIER ::= { jnxSlot    65 }
    jnxSRX120SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX120  1  }
    jnxSRX120SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX120  2  }
    jnxSRX120SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX120  3  }
    jnxSRX120SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX120  4  }

  jnxMediaCardSpaceSRX120    OBJECT IDENTIFIER ::= { jnxMediaCardSpace       65 }
    jnxSRX120MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX120  1 }

  jnxMidplaneSRX120          OBJECT IDENTIFIER ::= { jnxBackplane 65 }

  jnxModuleSRX120            OBJECT IDENTIFIER ::= { jnxModule    65 }
    jnxSRX120FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX120 1  }
    jnxSRX120RE              OBJECT IDENTIFIER ::= { jnxModuleSRX120 2  }
    jnxSRX120Power           OBJECT IDENTIFIER ::= { jnxModuleSRX120 3  }
    jnxSRX120Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX120 4  }


--
-- MAG8600 (Agent00)
--
  jnxProductLineMAG8600      OBJECT IDENTIFIER ::= { jnxProductLine      66 }
  jnxProductNameMAG8600      OBJECT IDENTIFIER ::= { jnxProductName      66 }
  jnxProductModelMAG8600     OBJECT IDENTIFIER ::= { jnxProductModel     66 }
  jnxProductVariationMAG8600 OBJECT IDENTIFIER ::= { jnxProductVariation 66 }
  jnxChassisMAG8600          OBJECT IDENTIFIER ::= { jnxChassis          66 }

  jnxSlotMAG8600             OBJECT IDENTIFIER ::= { jnxSlot             66 }
    jnxMAG8600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMAG8600 1  }
    jnxMAG8600SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMAG8600 2  }
    jnxMAG8600SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMAG8600 3  }
    jnxMAG8600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMAG8600 4  }
    jnxMAG8600SlotCB         OBJECT IDENTIFIER ::= { jnxSlotMAG8600 5  }

  jnxMediaCardSpaceMAG8600   OBJECT IDENTIFIER ::= { jnxMediaCardSpace   66 }
  jnxMAG8600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMAG8600 1 }

  jnxMidplaneMAG8600         OBJECT IDENTIFIER ::= { jnxBackplane        66 }


--
-- MAG6611 (Habanero)
--

  jnxProductLineMAG6611      OBJECT IDENTIFIER ::= { jnxProductLine      67 }
  jnxProductNameMAG6611      OBJECT IDENTIFIER ::= { jnxProductName      67 }
  jnxProductModelMAG6611     OBJECT IDENTIFIER ::= { jnxProductModel     67 }
  jnxProductVariationMAG6611 OBJECT IDENTIFIER ::= { jnxProductVariation 67 }
  jnxChassisMAG6611          OBJECT IDENTIFIER ::= { jnxChassis          67 }

  jnxSlotMAG6611             OBJECT IDENTIFIER ::= { jnxSlot             67 }
    jnxMAG6611SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMAG6611 1  }
    jnxMAG6611SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMAG6611 2  }
    jnxMAG6611SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMAG6611 3  }
    jnxMAG6611SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMAG6611 4  }
    jnxMAG6611SlotCB         OBJECT IDENTIFIER ::= { jnxSlotMAG6611 5  }

  jnxMediaCardSpaceMAG6611   OBJECT IDENTIFIER ::= { jnxMediaCardSpace   67 }
  jnxMAG6611MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMAG6611 1 }

  jnxMidplaneMAG6611         OBJECT IDENTIFIER ::= { jnxBackplane        67 }

--
-- MAG6610 (Cayenne)
--

  jnxProductLineMAG6610      OBJECT IDENTIFIER ::= { jnxProductLine      68 }
  jnxProductNameMAG6610      OBJECT IDENTIFIER ::= { jnxProductName      68 }
  jnxProductModelMAG6610     OBJECT IDENTIFIER ::= { jnxProductModel     68 }
  jnxProductVariationMAG6610 OBJECT IDENTIFIER ::= { jnxProductVariation 68 }
  jnxChassisMAG6610          OBJECT IDENTIFIER ::= { jnxChassis          68 }

  jnxSlotMAG6610             OBJECT IDENTIFIER ::= { jnxSlot             68 }
    jnxMAG6610SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMAG6610 1  }
    jnxMAG6610SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMAG6610 2  }
    jnxMAG6610SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMAG6610 3  }
    jnxMAG6610SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMAG6610 4  }
    jnxMAG6610SlotCB         OBJECT IDENTIFIER ::= { jnxSlotMAG6610 5  }

  jnxMediaCardSpaceMAG6610   OBJECT IDENTIFIER ::= { jnxMediaCardSpace   68 }
  jnxMAG6610MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMAG6610 1 }

  jnxMidplaneMAG6610         OBJECT IDENTIFIER ::= { jnxBackplane        68 }


--
-- PTX5000 - 8 Slot
--

  jnxProductLinePTX5000      OBJECT IDENTIFIER ::= { jnxProductLine      69 }
  jnxProductNamePTX5000      OBJECT IDENTIFIER ::= { jnxProductName      69 }
  jnxProductModelPTX5000     OBJECT IDENTIFIER ::= { jnxProductModel     69 }
  jnxProductVariationPTX5000 OBJECT IDENTIFIER ::= { jnxProductVariation 69 }
  jnxChassisPTX5000          OBJECT IDENTIFIER ::= { jnxChassis          69 }

  jnxSlotPTX5000             OBJECT IDENTIFIER ::= { jnxSlot             69 }
    jnxPTX5000SlotSIB        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 1  }
    jnxPTX5000SlotHM         OBJECT IDENTIFIER ::= { jnxSlotPTX5000 2  }
    jnxPTX5000SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 3  }
    jnxPTX5000SlotFan        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 4  }
    jnxPTX5000SlotCB         OBJECT IDENTIFIER ::= { jnxSlotPTX5000 5  }
    jnxPTX5000SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 6  }
    jnxPTX5000SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotPTX5000 7  }
    jnxPTX5000SlotPDU        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 8  }
    jnxPTX5000SlotPSM        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 9  }
    jnxPTX5000SlotCCG        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 10 }
    jnxPTX5000SlotPIC        OBJECT IDENTIFIER ::= { jnxSlotPTX5000 11 }

  jnxMediaCardSpacePTX5000      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    69 }
    jnxPTX5000MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpacePTX5000 1 }

  jnxMidplanePTX5000         OBJECT IDENTIFIER ::= { jnxBackplane        69 }

  jnxModulePTX5000           OBJECT IDENTIFIER ::= { jnxModule    69 }
    jnxPTX5000SIB            OBJECT IDENTIFIER ::= { jnxModulePTX5000 1  }
    jnxPTX5000HM             OBJECT IDENTIFIER ::= { jnxModulePTX5000 2  }
    jnxPTX5000FPC            OBJECT IDENTIFIER ::= { jnxModulePTX5000 3  }
    jnxPTX5000Fan            OBJECT IDENTIFIER ::= { jnxModulePTX5000 4  }
    jnxPTX5000CB             OBJECT IDENTIFIER ::= { jnxModulePTX5000 5  }
    jnxPTX5000FPB            OBJECT IDENTIFIER ::= { jnxModulePTX5000 6  }
    jnxPTX5000SPMB           OBJECT IDENTIFIER ::= { jnxModulePTX5000 7  }
    jnxPTX5000PDU            OBJECT IDENTIFIER ::= { jnxModulePTX5000 8  }
    jnxPTX5000PSM            OBJECT IDENTIFIER ::= { jnxModulePTX5000 9  }
    jnxPTX5000CCG            OBJECT IDENTIFIER ::= { jnxModulePTX5000 10 }
    jnxPTX5000PIC            OBJECT IDENTIFIER ::= { jnxModulePTX5000 11 }


-- Release 10.4
--
-- IBM EX 4500
--

   jnxProductLineIBM0719J45E      OBJECT IDENTIFIER ::= { jnxProductLine      71 }
   jnxProductNameIBM0719J45E      OBJECT IDENTIFIER ::= { jnxProductName      71 }
   jnxProductModelIBM0719J45E     OBJECT IDENTIFIER ::= { jnxProductModel     71 }
   jnxProductVariationIBM0719J45E OBJECT IDENTIFIER ::= { jnxProductVariation 71 }
     jnxProductIBM0719J45Eport40F    OBJECT IDENTIFIER ::= { jnxProductVariationIBM0719J45E 1 }
     jnxProductIBM0719J45Eport20F    OBJECT IDENTIFIER ::= { jnxProductVariationIBM0719J45E 2 }

   jnxChassisIBM0719J45E          OBJECT IDENTIFIER ::= { jnxChassis          71 }
     jnxIBM0719J45ERE0            OBJECT IDENTIFIER ::= { jnxChassisIBM0719J45E 1  }
     jnxIBM0719J45ERE1            OBJECT IDENTIFIER ::= { jnxChassisIBM0719J45E 2  }
   jnxSlotIBM0719J45E             OBJECT IDENTIFIER ::= { jnxSlot             71 }
     jnxIBM0719J45ESlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM0719J45E 1 }
       jnxIBM0719J45ESlotPower    OBJECT IDENTIFIER ::= { jnxIBM0719J45ESlotFPC 1 }
       jnxIBM0719J45ESlotFan      OBJECT IDENTIFIER ::= { jnxIBM0719J45ESlotFPC 2 }
       jnxIBM0719J45ESlotRE       OBJECT IDENTIFIER ::= { jnxIBM0719J45ESlotFPC 3 }

   jnxMediaCardSpaceIBM0719J45E      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    71 }
     jnxIBM0719J45EMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM0719J45E 1 }

   jnxModuleIBM0719J45E            OBJECT IDENTIFIER ::= { jnxModule    71 }
     jnxIBM0719J45EFPC             OBJECT IDENTIFIER ::= { jnxModuleIBM0719J45E 1 }
       jnxIBM0719J45EPower         OBJECT IDENTIFIER ::= { jnxIBM0719J45EFPC 1 }
       jnxIBM0719J45EFan           OBJECT IDENTIFIER ::= { jnxIBM0719J45EFPC 2 }
       jnxIBM0719J45ERE            OBJECT IDENTIFIER ::= { jnxIBM0719J45EFPC 3 }


--
-- IBM Converged Switch J08F (QFXC08 3008)
--

  jnxProductLineIBMJ08F OBJECT IDENTIFIER ::= { jnxProductLine      72 }
  jnxProductNameIBMJ08F OBJECT IDENTIFIER ::= { jnxProductName      72 }
  jnxProductModelIBMJ08F OBJECT IDENTIFIER ::= { jnxProductModel     72 }
  jnxProductVariationIBMJ08F  OBJECT IDENTIFIER ::= { jnxProductVariation 72 }
    jnxProductIBM2413F08J08F  OBJECT IDENTIFIER ::= { jnxProductVariationIBMJ08F 1 }

  jnxChassisIBMJ08F     OBJECT IDENTIFIER ::= { jnxChassis          72 }

  jnxSlotIBMJ08F        OBJECT IDENTIFIER ::= { jnxSlot             72 }
    jnxIBMJ08FSlotFPC   OBJECT IDENTIFIER ::= { jnxSlotIBMJ08F   1 }
    jnxIBMJ08FSlotHM    OBJECT IDENTIFIER ::= { jnxSlotIBMJ08F   2 }
    jnxIBMJ08FSlotPower OBJECT IDENTIFIER ::= { jnxSlotIBMJ08F   3 }
    jnxIBMJ08FSlotFan   OBJECT IDENTIFIER ::= { jnxSlotIBMJ08F   4 }
    jnxIBMJ08FSlotCBD   OBJECT IDENTIFIER ::= { jnxSlotIBMJ08F   5 }
    jnxIBMJ08FSlotFPB   OBJECT IDENTIFIER ::= { jnxSlotIBMJ08F   6 }

  jnxMediaCardSpaceIBMJ08F    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   72 }
    jnxIBMJ08FMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBMJ08F 1 }

  jnxMidplaneIBMJ08F    OBJECT IDENTIFIER ::= { jnxBackplane        72 }

--
-- IBM Converged Switch J52F (QFX 3500)
--

  jnxProductLineIBMJ52F       OBJECT IDENTIFIER ::= { jnxProductLine      73 }
  jnxProductNameIBMJ52F       OBJECT IDENTIFIER ::= { jnxProductName      73 }
  jnxProductModelIBMJ52F      OBJECT IDENTIFIER ::= { jnxProductModel     73 }
  jnxProductVariationIBMJ52F  OBJECT IDENTIFIER ::= { jnxProductVariation 73 }
    jnxProductIBM2409F52J52F  OBJECT IDENTIFIER ::= { jnxProductVariationIBMJ52F 1 }
    jnxProductIBM8729HC1J52F  OBJECT IDENTIFIER ::= { jnxProductVariationIBMJ52F 2 }

  jnxChassisIBMJ52F           OBJECT IDENTIFIER ::= { jnxChassis          73 }

  jnxSlotIBMJ52F              OBJECT IDENTIFIER ::= { jnxSlot             73 }
    jnxIBMJ52FSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotIBMJ52F   1 }
    jnxIBMJ52FSlotHM          OBJECT IDENTIFIER ::= { jnxSlotIBMJ52F   2 }
    jnxIBMJ52FSlotPower       OBJECT IDENTIFIER ::= { jnxSlotIBMJ52F   3 }
    jnxIBMJ52FSlotFan         OBJECT IDENTIFIER ::= { jnxSlotIBMJ52F   4 }
    jnxIBMJ52FSlotFPB         OBJECT IDENTIFIER ::= { jnxSlotIBMJ52F   5 }

  jnxMediaCardSpaceIBMJ52F    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   73 }
    jnxIBMJ52FMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBMJ52F 1 }

--
-- EX6210
--

  jnxProductLineEX6210       OBJECT IDENTIFIER ::= { jnxProductLine      74 }
  jnxProductNameEX6210       OBJECT IDENTIFIER ::= { jnxProductName      74 }
  jnxProductModelEX6210      OBJECT IDENTIFIER ::= { jnxProductModel     74 }
  jnxProductVariationEX6210  OBJECT IDENTIFIER ::= { jnxProductVariation 74 }
  jnxChassisEX6210           OBJECT IDENTIFIER ::= { jnxChassis          74 }

  jnxSlotEX6210              OBJECT IDENTIFIER ::= { jnxSlot             74 }
    jnxEX6210SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotEX6210  1 }
                            -- Flexible Port Concentrator
      jnxEX6210Slot48P       OBJECT IDENTIFIER ::= { jnxEX6210SlotFPC  1 }
      jnxEX6210Slot48T       OBJECT IDENTIFIER ::= { jnxEX6210SlotFPC  2 }
    jnxEX6210HM              OBJECT IDENTIFIER ::= { jnxSlotEX6210  3 }
                            -- Host Module (also called Routing Engine)
    jnxEX6210SlotPower       OBJECT IDENTIFIER ::= { jnxSlotEX6210  4 }
    jnxEX6210SlotFan         OBJECT IDENTIFIER ::= { jnxSlotEX6210  5 }
      jnxEX6210SlotFT        OBJECT IDENTIFIER ::= { jnxEX6210SlotFan  1 }
    jnxEX6210SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotEX6210  6 }
                            -- Control Board

  jnxMediaCardSpaceEX6210    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   74 }
    jnxEX6210MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX6210 1 }

  jnxBackplaneEX6210         OBJECT IDENTIFIER ::= { jnxBackplane        74 }


--
-- DELL PowerConnect J-Series FX3500 (QFX 3500)
--

  jnxProductLineDellJFX3500   OBJECT IDENTIFIER ::= { jnxProductLine      75 }
  jnxProductNameDellJFX3500   OBJECT IDENTIFIER ::= { jnxProductName      75 }
  jnxProductModelDellJFX3500  OBJECT IDENTIFIER ::= { jnxProductModel     75 }
  jnxProductVariationDellJFX3500  OBJECT IDENTIFIER ::= { jnxProductVariation 75 }

  jnxChassisDellJFX3500       OBJECT IDENTIFIER ::= { jnxChassis          75 }

  jnxSlotDellJFX3500          OBJECT IDENTIFIER ::= { jnxSlot             75 }
    jnxDellJFX3500SlotFPC     OBJECT IDENTIFIER ::= { jnxSlotDellJFX3500   1 }
    jnxDellJFX3500SlotHM      OBJECT IDENTIFIER ::= { jnxSlotDellJFX3500   2 }
    jnxDellJFX3500SlotPower   OBJECT IDENTIFIER ::= { jnxSlotDellJFX3500   3 }
    jnxDellJFX3500SlotFan     OBJECT IDENTIFIER ::= { jnxSlotDellJFX3500   4 }
    jnxDellJFX3500SlotFPB     OBJECT IDENTIFIER ::= { jnxSlotDellJFX3500   5 }

  jnxMediaCardSpaceDellJFX3500 OBJECT IDENTIFIER ::= { jnxMediaCardSpace   75 }
    jnxDellJFX3500MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceDellJFX3500 1 }

--
-- EX3300 (Dragon-VC)
--

  jnxProductLineEX3300      OBJECT IDENTIFIER ::= { jnxProductLine      76 }
  jnxProductNameEX3300      OBJECT IDENTIFIER ::= { jnxProductName      76 }
  jnxProductModelEX3300     OBJECT IDENTIFIER ::= { jnxProductModel     76 }
  jnxProductVariationEX3300 OBJECT IDENTIFIER ::= { jnxProductVariation 76 }
    jnxProductEX3300port24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX3300 1 }
    jnxProductEX3300port24P    OBJECT IDENTIFIER ::= { jnxProductVariationEX3300 2 }
    jnxProductEX3300port48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX3300 3 }
    jnxProductEX3300port48P    OBJECT IDENTIFIER ::= { jnxProductVariationEX3300 4 }
    jnxProductEX3300port24TDC  OBJECT IDENTIFIER ::= { jnxProductVariationEX3300 5 }
    jnxProductEX3300port48TBF  OBJECT IDENTIFIER ::= { jnxProductVariationEX3300 6 }

  jnxChassisEX3300          OBJECT IDENTIFIER ::= { jnxChassis          76 }
    jnxEX3300RE0            OBJECT IDENTIFIER ::= { jnxChassisEX3300 1  }
    jnxEX3300RE1            OBJECT IDENTIFIER ::= { jnxChassisEX3300 2  }

  jnxSlotEX3300             OBJECT IDENTIFIER ::= { jnxSlot             76 }
    jnxEX3300SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX3300 1 }
      jnxEX3300SlotPower    OBJECT IDENTIFIER ::= { jnxEX3300SlotFPC 1 }
      jnxEX3300SlotFan      OBJECT IDENTIFIER ::= { jnxEX3300SlotFPC 2 }

  jnxMediaCardSpaceEX3300      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    76 }
    jnxEX3300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX3300 1 }

  jnxModuleEX3300            OBJECT IDENTIFIER ::= { jnxModule    76 }
    jnxEX3300FPC             OBJECT IDENTIFIER ::= { jnxModuleEX3300 1 }
      jnxEX3300Power         OBJECT IDENTIFIER ::= { jnxEX3300FPC 1 }
      jnxEX3300Fan           OBJECT IDENTIFIER ::= { jnxEX3300FPC 2 }
      jnxEX3300RE            OBJECT IDENTIFIER ::= { jnxEX3300FPC 3 }


--
-- DELLJSRX3600 (A10 DELL OEM)
--

  jnxProductLineDELLJSRX3600      OBJECT IDENTIFIER ::= { jnxProductLine      77 }
  jnxProductNameDELLJSRX3600      OBJECT IDENTIFIER ::= { jnxProductName      77 }
  jnxProductModelDELLJSRX3600     OBJECT IDENTIFIER ::= { jnxProductModel     77 }
  jnxProductVariationDELLJSRX3600 OBJECT IDENTIFIER ::= { jnxProductVariation 77 }
  jnxChassisDELLJSRX3600          OBJECT IDENTIFIER ::= { jnxChassis          77 }

  jnxSlotDELLJSRX3600             OBJECT IDENTIFIER ::= { jnxSlot             77 }
    jnxDELLJSRX3600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3600 1  }
    jnxDELLJSRX3600SlotHM         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3600 2  }
    jnxDELLJSRX3600SlotPower      OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3600 3  }
    jnxDELLJSRX3600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3600 4  }
    jnxDELLJSRX3600SlotCB         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3600 5  }
    jnxDELLJSRX3600SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3600 6  }

  jnxMediaCardSpaceDELLJSRX3600   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    77 }
  jnxDELLJSRX3600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceDELLJSRX3600 1 }

  jnxMidplaneDELLJSRX3600         OBJECT IDENTIFIER ::= { jnxBackplane        77 }

--
-- DELLJSRX3400 (A2 DELL OEM)
--

  jnxProductLineDELLJSRX3400      OBJECT IDENTIFIER ::= { jnxProductLine      78 }
  jnxProductNameDELLJSRX3400      OBJECT IDENTIFIER ::= { jnxProductName      78 }
  jnxProductModelDELLJSRX3400     OBJECT IDENTIFIER ::= { jnxProductModel     78 }
  jnxProductVariationDELLJSRX3400 OBJECT IDENTIFIER ::= { jnxProductVariation 78 }
  jnxChassisDELLJSRX3400          OBJECT IDENTIFIER ::= { jnxChassis          78 }

  jnxSlotDELLJSRX3400             OBJECT IDENTIFIER ::= { jnxSlot             78 }
    jnxDELLJSRX3400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3400 1  }
    jnxDELLJSRX3400SlotHM         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3400 2  }
    jnxDELLJSRX3400SlotPower      OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3400 3  }
    jnxDELLJSRX3400SlotFan        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3400 4  }
    jnxDELLJSRX3400SlotCB         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3400 5  }
    jnxDELLJSRX3400SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX3400 6  }

  jnxMediaCardSpaceDELLJSRX3400   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    78 }
  jnxDELLJSRX3400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceDELLJSRX3400 1 }

  jnxMidplaneDELLJSRX3400         OBJECT IDENTIFIER ::= { jnxBackplane        78 }

--
-- DELLJSRX1400 (A1 DELL OEM)
--

  jnxProductLineDELLJSRX1400      OBJECT IDENTIFIER ::= { jnxProductLine      79 }
  jnxProductNameDELLJSRX1400      OBJECT IDENTIFIER ::= { jnxProductName      79 }
  jnxProductModelDELLJSRX1400     OBJECT IDENTIFIER ::= { jnxProductModel     79 }
  jnxProductVariationDELLJSRX1400 OBJECT IDENTIFIER ::= { jnxProductVariation 79 }
  jnxChassisDELLJSRX1400          OBJECT IDENTIFIER ::= { jnxChassis          79 }

  jnxSlotDELLJSRX1400             OBJECT IDENTIFIER ::= { jnxSlot             79 }
    jnxDELLJSRX1400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX1400 1  }
    jnxDELLJSRX1400SlotHM         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX1400 2  }
    jnxDELLJSRX1400SlotPower      OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX1400 3  }
    jnxDELLJSRX1400SlotFan        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX1400 4  }
    jnxDELLJSRX1400SlotCB         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX1400 5  }
    jnxDELLJSRX1400SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX1400 6  }

  jnxMediaCardSpaceDELLJSRX1400   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    79 }
  jnxDELLJSRX1400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceDELLJSRX1400 1 }

  jnxMidplaneDELLJSRX1400         OBJECT IDENTIFIER ::= { jnxBackplane        79 }

--
-- DELLJSRX5800 (A40 DELL OEM)
--

  jnxProductLineDELLJSRX5800      OBJECT IDENTIFIER ::= { jnxProductLine      80 }
  jnxProductNameDELLJSRX5800      OBJECT IDENTIFIER ::= { jnxProductName      80 }
  jnxProductModelDELLJSRX5800     OBJECT IDENTIFIER ::= { jnxProductModel     80 }
  jnxProductVariationDELLJSRX5800 OBJECT IDENTIFIER ::= { jnxProductVariation 80 }
  jnxChassisDELLJSRX5800          OBJECT IDENTIFIER ::= { jnxChassis          80 }

  jnxSlotDELLJSRX5800             OBJECT IDENTIFIER ::= { jnxSlot             80 }
    jnxDELLJSRX5800SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5800 1  }
    jnxDELLJSRX5800SlotHM         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5800 2  }
    jnxDELLJSRX5800SlotPower      OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5800 3  }
    jnxDELLJSRX5800SlotFan        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5800 4  }
    jnxDELLJSRX5800SlotCB         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5800 5  }
    jnxDELLJSRX5800SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5800 6  }

  jnxMediaCardSpaceDELLJSRX5800      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    80 }
    jnxDELLJSRX5800MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceDELLJSRX5800 1 }

  jnxMidplaneDELLJSRX5800         OBJECT IDENTIFIER ::= { jnxBackplane        80 }

--
-- DELLJSRX5600 (A20 DELL OEM)
--

  jnxProductLineDELLJSRX5600      OBJECT IDENTIFIER ::= { jnxProductLine      81 }
  jnxProductNameDELLJSRX5600      OBJECT IDENTIFIER ::= { jnxProductName      81 }
  jnxProductModelDELLJSRX5600     OBJECT IDENTIFIER ::= { jnxProductModel     81 }
  jnxProductVariationDELLJSRX5600 OBJECT IDENTIFIER ::= { jnxProductVariation 81 }
  jnxChassisDELLJSRX5600          OBJECT IDENTIFIER ::= { jnxChassis          81 }

  jnxSlotDELLJSRX5600             OBJECT IDENTIFIER ::= { jnxSlot             81 }
    jnxDELLJSRX5600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5600 1  }
    jnxDELLJSRX5600SlotHM         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5600 2  }
    jnxDELLJSRX5600SlotPower      OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5600 3  }
    jnxDELLJSRX5600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5600 4  }
    jnxDELLJSRX5600SlotCB         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5600 5  }
    jnxDELLJSRX5600SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5600 6  }

  jnxMediaCardSpaceDELLJSRX5600      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    81 }
  jnxDELLJSRX5600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceDELLJSRX5600 1 }

  jnxMidplaneDELLJSRX5600         OBJECT IDENTIFIER ::= { jnxBackplane        81 }

--
-- QFXSwitch
--

  jnxProductLineQFXSwitch       OBJECT IDENTIFIER ::= { jnxProductLine      82 }
  jnxProductNameQFXSwitch       OBJECT IDENTIFIER ::= { jnxProductName      82 }
  jnxProductModelQFXSwitch      OBJECT IDENTIFIER ::= { jnxProductModel     82 }
  jnxProductVariationQFXSwitch  OBJECT IDENTIFIER ::= { jnxProductVariation 82 }
    jnxProductQFX3500s          OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 1 }
    jnxProductQFX360016QS       OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 2 }
    jnxProductQFX350048T4QS     OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 3 }
    jnxProductQFX510024Q        OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 4 }
    jnxProductQFX510048S6Q      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 5 }
    jnxProductQFX510096S8Q      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 6 }
    jnxProductQFX510048C6Q      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 7 }
    jnxProductQFX510024QHP      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 8 }
    jnxProductQFX510048T6Q      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 9 }
    jnxProductQFX1000236Q       OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 10 }
    jnxProductQFX1000272Q       OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 11 }
    jnxProductQFX10004          OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 12 }
    jnxProductQFX10008          OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 13 }
    jnxProductQFX10016          OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 14 }
    jnxProductQFX520032C32Q     OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 15 }
    jnxProductQFX520032C64Q     OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 16 }
    jnxProductQFX511048S4C      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 17 }
    jnxProductQFX511032Q        OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 18 }
    jnxProductNameQFX1000260C   OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 19 }
    jnxProductQFX521064C        OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 20 }
    jnxProductQFX520048Y        OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 21 }
    jnxProductQFX512048Y8C      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 22 }
    jnxProductAS781664X         OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 23 }
    jnxProductQFX512032C        OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 24 }
    jnxProductQFX522032CD       OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 25 }
    jnxProductQFX5220128C       OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 26 }
    jnxProductQFX512048T6C      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 27 }
    jnxProductQFX513032CD       OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 28 }
    jnxProductQFX513048C        OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 29 }
    jnxProductQFX512048YM8C     OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 30 }
    jnxProductQFX5700           OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 31 }
    jnxProductQFX523064CD       OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 32 }
    jnxProductQFX5130E32CD      OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 33 }
    jnxProductQFX524064OSFP     OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 34 }
    jnxProductQFX524064QSFPDD   OBJECT IDENTIFIER ::= { jnxProductVariationQFXSwitch 35 }

  jnxChassisQFXSwitch           OBJECT IDENTIFIER ::= { jnxChassis          82 }

  jnxSlotQFXSwitch              OBJECT IDENTIFIER ::= { jnxSlot             82 }
    jnxQFXSwitchSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   1 }
    jnxQFXSwitchSlotHM          OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   2 }
    jnxQFXSwitchSlotPower       OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   3 }
    jnxQFXSwitchSlotFan         OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   4 }
    jnxQFXSwitchSlotFPB         OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   5 }
    jnxQFXSwitchSlotCBD         OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   6 }
    jnxQFXSwitchSlotSIB         OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   7 }
    jnxQFXSwitchSlotFEB         OBJECT IDENTIFIER ::= { jnxSlotQFXSwitch   8 }

  jnxMediaCardSpaceQFXSwitch    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   82 }
    jnxQFXSwitchMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceQFXSwitch 1 }


--
-- T4000
--


  jnxProductLineT4000		OBJECT IDENTIFIER ::= { jnxProductLine		83 }
  jnxProductNameT4000		OBJECT IDENTIFIER ::= { jnxProductName		83 }
  jnxProductModelT4000		OBJECT IDENTIFIER ::= { jnxProductModel		83 }
  jnxProductVariationT4000	OBJECT IDENTIFIER ::= { jnxProductVariation 	83 }
  jnxChassisT4000		OBJECT IDENTIFIER ::= { jnxChassis		83 }

  jnxSlotT4000			OBJECT IDENTIFIER ::= { jnxSlot			83 }
    jnxT4000SlotFPC		OBJECT IDENTIFIER ::= { jnxSlotT4000 1 }
    			   	-- Flexible Port Concentrator slot
    jnxT4000SlotSIB		OBJECT IDENTIFIER ::= { jnxSlotT4000 2 }
				-- Switch Interface Board slot
    jnxT4000SlotHM		OBJECT IDENTIFIER ::= { jnxSlotT4000 3 }
				-- Host Module (also called Routing Engine) slot
    jnxT4000SlotSCG		OBJECT IDENTIFIER ::= { jnxSlotT4000 4 }
				-- SONET Clock Generator slot
    jnxT4000SlotPower		OBJECT IDENTIFIER ::= { jnxSlotT4000 5 }
    jnxT4000SlotFan		OBJECT IDENTIFIER ::= { jnxSlotT4000 6 }
    jnxT4000SlotCB		OBJECT IDENTIFIER ::= { jnxSlotT4000 7 }
				-- Control Board slot
    jnxT4000SlotFPB		OBJECT IDENTIFIER ::= { jnxSlotT4000 8 }
				-- Front Panel Board
    jnxT4000SlotCIP		OBJECT IDENTIFIER ::= { jnxSlotT4000 9 }
				-- Connector Interface Panel
    jnxT4000SlotSPMB		OBJECT IDENTIFIER ::= { jnxSlotT4000 10 }
				-- Processor Mezzanine Board for SIB
    jnxT4000SlotPSD		OBJECT IDENTIFIER ::= { jnxSlotT4000 11 }
				-- Protected System Domain slot

  jnxMediaCardSpaceT4000	OBJECT IDENTIFIER ::= { jnxMediaCardSpace	83 }
    jnxT4000MediaCardSpacePIC 	OBJECT IDENTIFIER ::= { jnxMediaCardSpaceT4000 1 }

  jnxMidplaneT4000		OBJECT IDENTIFIER ::= { jnxBackplane		83 }

  jnxModuleT4000		OBJECT IDENTIFIER ::= { jnxModule 		83 }
    jnxT4000SIB  		OBJECT IDENTIFIER ::= { jnxModuleT4000 1 }
				-- Switch Interface Board
    jnxT4000SCG  		OBJECT IDENTIFIER ::= { jnxModuleT4000 2 }
				-- SONET Clock Generator
    jnxT4000CB			OBJECT IDENTIFIER ::= { jnxModuleT4000 3 }
				-- Control Board
    jnxT4000SPMB		OBJECT IDENTIFIER ::= { jnxModuleT4000 4 }
				-- Processor Mezzanine Board for SIB

--
-- Quantum Fabric Series 3000 (Staten Island)
--

  jnxProductLineQFX3000      OBJECT IDENTIFIER ::= { jnxProductLine 84 }
  jnxProductNameQFX3000      OBJECT IDENTIFIER ::= { jnxProductName 84 }
  jnxProductModelQFX3000     OBJECT IDENTIFIER ::= { jnxProductModel 84 }
  jnxProductVariationQFX3000 OBJECT IDENTIFIER ::= { jnxProductVariation 84 }
    jnxProductQFX3000-G      OBJECT IDENTIFIER ::= { jnxProductVariationQFX3000 1 }
    jnxProductQFX3000-M      OBJECT IDENTIFIER ::= { jnxProductVariationQFX3000 2 }
  jnxChassisQFX3000          OBJECT IDENTIFIER ::= { jnxChassis          84 }

--
-- Quantum Fabric Series 5000 (Wall Street)
--

  jnxProductLineQFX5000 	 OBJECT IDENTIFIER ::= { jnxProductLine 85 }
  jnxProductNameQFX5000      OBJECT IDENTIFIER ::= { jnxProductName 85 }
  jnxProductModelQFX5000     OBJECT IDENTIFIER ::= { jnxProductModel 85 }
  jnxProductVariationQFX5000 OBJECT IDENTIFIER ::= { jnxProductVariation 85 }
  jnxChassisQFX5000          OBJECT IDENTIFIER ::= { jnxChassis          85 }

--
--
-- SRX550
--
  jnxProductLineSRX550       OBJECT IDENTIFIER ::= { jnxProductLine 86 }
  jnxProductNameSRX550       OBJECT IDENTIFIER ::= { jnxProductName 86 }
  jnxChassisSRX550           OBJECT IDENTIFIER ::= { jnxChassis     86 }

  jnxSlotSRX550              OBJECT IDENTIFIER ::= { jnxSlot 86 }
    jnxSRX550SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSRX550  1  }
    jnxSRX550SlotRE          OBJECT IDENTIFIER ::= { jnxSlotSRX550  2  }
    jnxSRX550SlotPower       OBJECT IDENTIFIER ::= { jnxSlotSRX550  3  }
    jnxSRX550SlotFan         OBJECT IDENTIFIER ::= { jnxSlotSRX550  4  }

  jnxMediaCardSpaceSRX550    OBJECT IDENTIFIER ::= { jnxMediaCardSpace 86 }
    jnxSRX550MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX550  1 }

  jnxMidplaneSRX550         OBJECT IDENTIFIER ::= { jnxBackplane 86 }

  jnxModuleSRX550            OBJECT IDENTIFIER ::= { jnxModule 86 }
    jnxSRX550FPC             OBJECT IDENTIFIER ::= { jnxModuleSRX550 1  }
    jnxSRX550RE              OBJECT IDENTIFIER ::= { jnxModuleSRX550 2  }
    jnxSRX550Power           OBJECT IDENTIFIER ::= { jnxModuleSRX550 3  }
    jnxSRX550Fan             OBJECT IDENTIFIER ::= { jnxModuleSRX550 4  }


--
-- ACX
--
  jnxProductLineACX      OBJECT IDENTIFIER ::= { jnxProductLine      87 }
  jnxProductNameACX      OBJECT IDENTIFIER ::= { jnxProductName      87 }
  jnxProductModelACX     OBJECT IDENTIFIER ::= { jnxProductModel     87 }
  jnxProductVariationACX OBJECT IDENTIFIER ::= { jnxProductVariation 87 }
    jnxProductACX500IDC  OBJECT IDENTIFIER ::= { jnxProductVariationACX 11 }
    jnxProductACX500IAC  OBJECT IDENTIFIER ::= { jnxProductVariationACX 12 }
  jnxChassisACX          OBJECT IDENTIFIER ::= { jnxChassis          87 }

  jnxSlotACX             OBJECT IDENTIFIER ::= { jnxSlot     87 }
    jnxACXSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX 1  }
    jnxACXSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX 2  }
    jnxACXSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX 3  }
    jnxACXSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX 4  }
    jnxACXSlotFan        OBJECT IDENTIFIER ::= { jnxSlotACX 5  }

  jnxMediaCardSpaceACX      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    87 }
    jnxACXMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX 1 }
    jnxACXMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX 2 }

  jnxMidplaneACX         OBJECT IDENTIFIER ::= { jnxBackplane        87 }

  jnxModuleACX           OBJECT IDENTIFIER ::= { jnxModule     87 }
    jnxACXFPC            OBJECT IDENTIFIER ::= { jnxModuleACX 1  }
    jnxACXFEB            OBJECT IDENTIFIER ::= { jnxModuleACX 2  }
    jnxACXRE             OBJECT IDENTIFIER ::= { jnxModuleACX 3  }
    jnxACXPower          OBJECT IDENTIFIER ::= { jnxModuleACX 4  }
       jnxACXPowerDC     OBJECT IDENTIFIER ::= { jnxACXPower 1  }
       jnxACXPowerAC     OBJECT IDENTIFIER ::= { jnxACXPower 2  }
    jnxACXFan            OBJECT IDENTIFIER ::= { jnxModuleACX 5  }


--
-- MX40
--

  jnxProductLineMX40      OBJECT IDENTIFIER ::= { jnxProductLine      88 }
  jnxProductNameMX40      OBJECT IDENTIFIER ::= { jnxProductName      88 }
  jnxProductModelMX40     OBJECT IDENTIFIER ::= { jnxProductModel     88 }
  jnxProductVariationMX40 OBJECT IDENTIFIER ::= { jnxProductVariation 88 }
    jnxProductMX40        OBJECT IDENTIFIER ::= { jnxProductVariationMX40 1 }
  jnxChassisMX40          OBJECT IDENTIFIER ::= { jnxChassis          88 }

  jnxSlotMX40             OBJECT IDENTIFIER ::= { jnxSlot     88 }
    jnxMX40SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX40 1  }
    jnxMX40SlotCFEB       OBJECT IDENTIFIER ::= { jnxSlotMX40 2  }
    jnxMX40SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMX40 3  }
    jnxMX40SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX40 4  }
    jnxMX40SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX40 5  }

  jnxMediaCardSpaceMX40      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    88 }
    jnxMX40MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX40 1 }
    jnxMX40MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX40 2 }

  jnxMidplaneMX40         OBJECT IDENTIFIER ::= { jnxBackplane        88 }

  jnxModuleMX40           OBJECT IDENTIFIER ::= { jnxModule     88 }
    jnxMX40FPC            OBJECT IDENTIFIER ::= { jnxModuleMX40 1  }
    jnxMX40CFEB           OBJECT IDENTIFIER ::= { jnxModuleMX40 2  }
    jnxMX40RE             OBJECT IDENTIFIER ::= { jnxModuleMX40 3  }
    jnxMX40Power          OBJECT IDENTIFIER ::= { jnxModuleMX40 4  }
    jnxMX40PowerAC        OBJECT IDENTIFIER ::= { jnxModuleMX40 5  }
    jnxMX40Fan            OBJECT IDENTIFIER ::= { jnxModuleMX40 6  }

--
-- MX10
--

  jnxProductLineMX10      OBJECT IDENTIFIER ::= { jnxProductLine      89 }
  jnxProductNameMX10      OBJECT IDENTIFIER ::= { jnxProductName      89 }
  jnxProductModelMX10     OBJECT IDENTIFIER ::= { jnxProductModel     89 }
  jnxProductVariationMX10 OBJECT IDENTIFIER ::= { jnxProductVariation 89 }
    jnxProductMX10        OBJECT IDENTIFIER ::= { jnxProductVariationMX10 1 }
  jnxChassisMX10          OBJECT IDENTIFIER ::= { jnxChassis          89 }

  jnxSlotMX10             OBJECT IDENTIFIER ::= { jnxSlot     89 }
    jnxMX10SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX10 1  }
    jnxMX10SlotCFEB       OBJECT IDENTIFIER ::= { jnxSlotMX10 2  }
    jnxMX10SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMX10 3  }
    jnxMX10SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX10 4  }
    jnxMX10SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX10 5  }

  jnxMediaCardSpaceMX10      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    89 }
    jnxMX10MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX10 1 }
    jnxMX10MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX10 2 }

  jnxMidplaneMX10         OBJECT IDENTIFIER ::= { jnxBackplane        89 }

  jnxModuleMX10           OBJECT IDENTIFIER ::= { jnxModule     89 }
    jnxMX10FPC            OBJECT IDENTIFIER ::= { jnxModuleMX10 1  }
    jnxMX10CFEB           OBJECT IDENTIFIER ::= { jnxModuleMX10 2  }
    jnxMX10RE             OBJECT IDENTIFIER ::= { jnxModuleMX10 3  }
    jnxMX10Power          OBJECT IDENTIFIER ::= { jnxModuleMX10 4  }
    jnxMX10PowerAC        OBJECT IDENTIFIER ::= { jnxModuleMX10 5  }
    jnxMX10Fan            OBJECT IDENTIFIER ::= { jnxModuleMX10 6  }


--
-- MX5
--

  jnxProductLineMX5      OBJECT IDENTIFIER ::= { jnxProductLine      90 }
  jnxProductNameMX5      OBJECT IDENTIFIER ::= { jnxProductName      90 }
  jnxProductModelMX5     OBJECT IDENTIFIER ::= { jnxProductModel     90 }
  jnxProductVariationMX5 OBJECT IDENTIFIER ::= { jnxProductVariation 90 }
    jnxProductMX5        OBJECT IDENTIFIER ::= { jnxProductVariationMX5 1 }
  jnxChassisMX5          OBJECT IDENTIFIER ::= { jnxChassis          90 }

  jnxSlotMX5             OBJECT IDENTIFIER ::= { jnxSlot     90 }
    jnxMX5SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX5 1  }
    jnxMX5SlotCFEB       OBJECT IDENTIFIER ::= { jnxSlotMX5 2  }
    jnxMX5SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMX5 3  }
    jnxMX5SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX5 4  }
    jnxMX5SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX5 5  }

  jnxMediaCardSpaceMX5      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    90 }
    jnxMX5MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX5 1 }
    jnxMX5MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX5 2 }

  jnxMidplaneMX5         OBJECT IDENTIFIER ::= { jnxBackplane        90 }

  jnxModuleMX5           OBJECT IDENTIFIER ::= { jnxModule     90 }
    jnxMX5FPC            OBJECT IDENTIFIER ::= { jnxModuleMX5 1  }
    jnxMX5CFEB           OBJECT IDENTIFIER ::= { jnxModuleMX5 2  }
    jnxMX5RE             OBJECT IDENTIFIER ::= { jnxModuleMX5 3  }
    jnxMX5Power          OBJECT IDENTIFIER ::= { jnxModuleMX5 4  }
    jnxMX5PowerAC        OBJECT IDENTIFIER ::= { jnxModuleMX5 5  }
    jnxMX5Fan            OBJECT IDENTIFIER ::= { jnxModuleMX5 6  }


--
-- QFXMInterconnect
--

  jnxProductLineQFXMInterconnect OBJECT IDENTIFIER ::= { jnxProductLine      91 }
  jnxProductNameQFXMInterconnect OBJECT IDENTIFIER ::= { jnxProductName      91 }
  jnxProductModelQFXMInterconnect OBJECT IDENTIFIER ::= { jnxProductModel     91 }
  jnxProductVariationQFXMInterconnect  OBJECT IDENTIFIER ::= { jnxProductVariation 91 }
    jnxProductQFX3600I          OBJECT IDENTIFIER ::= { jnxProductVariationQFXMInterconnect 1 }
    jnxProductQFX510024QI       OBJECT IDENTIFIER ::= { jnxProductVariationQFXMInterconnect 2 }

  jnxChassisQFXMInterconnect     OBJECT IDENTIFIER ::= { jnxChassis          91 }

  jnxSlotQFXMInterconnect        OBJECT IDENTIFIER ::= { jnxSlot             91 }
    jnxQFXMInterconnectSlotFPC   OBJECT IDENTIFIER ::= { jnxSlotQFXMInterconnect   1 }
    jnxQFXMInterconnectSlotHM    OBJECT IDENTIFIER ::= { jnxSlotQFXMInterconnect   2 }
    jnxQFXMInterconnectSlotPower OBJECT IDENTIFIER ::= { jnxSlotQFXMInterconnect   3 }
    jnxQFXMInterconnectSlotFan   OBJECT IDENTIFIER ::= { jnxSlotQFXMInterconnect   4 }
    jnxQFXMInterconnectSlotFPB   OBJECT IDENTIFIER ::= { jnxSlotQFXMInterconnect   5 }

  jnxMediaCardSpaceQFXMInterconnect    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   91 }
    jnxQFXMInterconnectMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceQFXMInterconnect 1 }


--
-- EX4550
--
  jnxProductLineEX4550      OBJECT IDENTIFIER ::= { jnxProductLine      92 }
  jnxProductNameEX4550      OBJECT IDENTIFIER ::= { jnxProductName      92 }
  jnxProductModelEX4550     OBJECT IDENTIFIER ::= { jnxProductModel     92 }
  jnxProductVariationEX4550 OBJECT IDENTIFIER ::= { jnxProductVariation 92 }
    jnxProductEX4550port32F    OBJECT IDENTIFIER ::= { jnxProductVariationEX4550 1 }
    jnxProductEX4550port32T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4550 2 }

  jnxChassisEX4550          OBJECT IDENTIFIER ::= { jnxChassis          92 }
    jnxEX4550RE0            OBJECT IDENTIFIER ::= { jnxChassisEX4550 1 }
    jnxEX4550RE1            OBJECT IDENTIFIER ::= { jnxChassisEX4550 2 }
  jnxSlotEX4550             OBJECT IDENTIFIER ::= { jnxSlot             92 }
    jnxEX4550SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX4550 1 }
      jnxEX4550SlotPower    OBJECT IDENTIFIER ::= { jnxEX4550SlotFPC 1 }
      jnxEX4550SlotFan      OBJECT IDENTIFIER ::= { jnxEX4550SlotFPC 2 }
      jnxEX4550SlotRE       OBJECT IDENTIFIER ::= { jnxEX4550SlotFPC 3 }

  jnxMediaCardSpaceEX4550      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    92 }
    jnxEX4550MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4550 1 }

  jnxModuleEX4550            OBJECT IDENTIFIER ::= { jnxModule    92 }
    jnxEX4550FPC             OBJECT IDENTIFIER ::= { jnxModuleEX4550 1 }
      jnxEX4550Power         OBJECT IDENTIFIER ::= { jnxEX4550FPC 1 }
      jnxEX4550Fan           OBJECT IDENTIFIER ::= { jnxEX4550FPC 2 }
      jnxEX4550RE            OBJECT IDENTIFIER ::= { jnxEX4550FPC 3 }


--
-- MX2020
--
  jnxProductLineMX2020      OBJECT IDENTIFIER ::= { jnxProductLine      93 }
  jnxProductNameMX2020      OBJECT IDENTIFIER ::= { jnxProductName      93 }
  jnxProductModelMX2020     OBJECT IDENTIFIER ::= { jnxProductModel     93 }
  jnxProductVariationMX2020 OBJECT IDENTIFIER ::= { jnxProductVariation 93 }
  jnxChassisMX2020          OBJECT IDENTIFIER ::= { jnxChassis          93 }

  jnxSlotMX2020             OBJECT IDENTIFIER ::= { jnxSlot             93 }
    jnxMX2020SlotSFB        OBJECT IDENTIFIER ::= { jnxSlotMX2020 1  }
                            -- Switch Fabric Board
    jnxMX2020SlotHM         OBJECT IDENTIFIER ::= { jnxSlotMX2020 2  }
                            -- Host Module (also called Routing Engine {RE})
    jnxMX2020SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX2020 3  }
                            -- Flexible Port Concentrator slot
    jnxMX2020SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX2020 4  }
    jnxMX2020SlotCB         OBJECT IDENTIFIER ::= { jnxSlotMX2020 5  }
                            -- Control Board (hosts RE, SPMB)
    jnxMX2020SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotMX2020 6  }
                            -- Front Panel Board
    jnxMX2020SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotMX2020 7  }
                            -- Processor Mezzanine Board for SFB
    jnxMX2020SlotPDM        OBJECT IDENTIFIER ::= { jnxSlotMX2020 8  }
                            -- Power Distribution Module
    jnxMX2020SlotPSM        OBJECT IDENTIFIER ::= { jnxSlotMX2020 9  }
                            -- Power Supply Module
    jnxMX2020SlotADC        OBJECT IDENTIFIER ::= { jnxSlotMX2020 10 }
                            -- Adapter Card (connects FPC to backplane)

  jnxMediaCardSpaceMX2020      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    93 }
    jnxMX2020MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX2020 1 }
    jnxMX2020MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX2020 2 }

  jnxBackplaneMX2020         OBJECT IDENTIFIER ::= { jnxBackplane        93 }
    jnxBackplaneLowerMX2020         OBJECT IDENTIFIER ::= { jnxBackplaneMX2020   1 }
    jnxBackplaneUpperMX2020         OBJECT IDENTIFIER ::= { jnxBackplaneMX2020   2 }
    jnxBackplaneLowerPowerMX2020    OBJECT IDENTIFIER ::= { jnxBackplaneMX2020   3 }
    jnxBackplaneUpperPowerMX2020    OBJECT IDENTIFIER ::= { jnxBackplaneMX2020   4 }

  jnxModuleMX2020           OBJECT IDENTIFIER ::= { jnxModule    93 }
    jnxMX2020SFB            OBJECT IDENTIFIER ::= { jnxModuleMX2020 1  }
    jnxMX2020HM             OBJECT IDENTIFIER ::= { jnxModuleMX2020 2  }
    jnxMX2020FPC            OBJECT IDENTIFIER ::= { jnxModuleMX2020 3  }
    jnxMX2020Fan            OBJECT IDENTIFIER ::= { jnxModuleMX2020 4  }
    jnxMX2020CB             OBJECT IDENTIFIER ::= { jnxModuleMX2020 5  }
    jnxMX2020FPB            OBJECT IDENTIFIER ::= { jnxModuleMX2020 6  }
    jnxMX2020SPMB           OBJECT IDENTIFIER ::= { jnxModuleMX2020 7  }
    jnxMX2020PDM            OBJECT IDENTIFIER ::= { jnxModuleMX2020 8  }
    jnxMX2020PSM            OBJECT IDENTIFIER ::= { jnxModuleMX2020 9  }
    jnxMX2020ADC            OBJECT IDENTIFIER ::= { jnxModuleMX2020 10 }




--
-- VJX
--

  jnxProductLineVseries      OBJECT IDENTIFIER ::= { jnxProductLine      94 }
  jnxProductNameVseries      OBJECT IDENTIFIER ::= { jnxProductName      94 }
  jnxChassisVseries          OBJECT IDENTIFIER ::= { jnxChassis          94 }
  jnxSlotVseries             OBJECT IDENTIFIER ::= { jnxSlot    94 }
    jnxVseriesSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotVseries  1  }
    jnxVseriesSlotRE          OBJECT IDENTIFIER ::= { jnxSlotVseries  2  }
    jnxVseriesSlotPower       OBJECT IDENTIFIER ::= { jnxSlotVseries  3  }
    jnxVseriesSlotFan         OBJECT IDENTIFIER ::= { jnxSlotVseries  4  }

  jnxMidplaneVseries         OBJECT IDENTIFIER ::= { jnxBackplane 94 }

  jnxModuleVseries            OBJECT IDENTIFIER ::= { jnxModule    94}
    jnxVseriesFPC             OBJECT IDENTIFIER ::= { jnxModuleVseries 1  }
    jnxVseriesRE              OBJECT IDENTIFIER ::= { jnxModuleVseries 2  }
    jnxVseriesPower           OBJECT IDENTIFIER ::= { jnxModuleVseries 3  }
    jnxVseriesFan             OBJECT IDENTIFIER ::= { jnxModuleVseries 4  }


--
-- LN2600
--
  jnxProductLineLN2600      OBJECT IDENTIFIER ::= { jnxProductLine 95 }
  jnxProductNameLN2600      OBJECT IDENTIFIER ::= { jnxProductName 95 }
  jnxProductModelLN2600     OBJECT IDENTIFIER ::= { jnxProductModel 95 }
  jnxProductVariationLN2600 OBJECT IDENTIFIER ::= { jnxProductVariation 95 }
  jnxChassisLN2600          OBJECT IDENTIFIER ::= { jnxChassis 95 }

  jnxMediaCardSpaceLN2600   OBJECT IDENTIFIER ::= { jnxMediaCardSpace 95 }
    jnxLN2600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceLN2600 1 }

  jnxMidplaneLN2600         OBJECT IDENTIFIER ::= { jnxBackplane 95 }

  jnxSlotLN2600             OBJECT IDENTIFIER ::= { jnxSlot 95 }
    jnxLN2600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotLN2600  1  }
    jnxLN2600SlotRE         OBJECT IDENTIFIER ::= { jnxSlotLN2600  2  }
    jnxLN2600SlotPower      OBJECT IDENTIFIER ::= { jnxSlotLN2600  3  }
    jnxLN2600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotLN2600  4  }



--
-- VSRX
--

  jnxProductLineFireflyPerimeter        OBJECT IDENTIFIER ::= { jnxProductLine  96 }
  jnxProductNameFireflyPerimeter        OBJECT IDENTIFIER ::= { jnxProductName  96 }
  jnxChassisFireflyPerimeter            OBJECT IDENTIFIER ::= { jnxChassis      96 }

  jnxSlotFireflyPerimeter               OBJECT IDENTIFIER ::= { jnxSlot         96 }
    jnxFireflyPerimeterSlotFPC          OBJECT IDENTIFIER ::= { jnxSlotFireflyPerimeter     1  }
    jnxFireflyPerimeterSlotRE           OBJECT IDENTIFIER ::= { jnxSlotFireflyPerimeter     2  }
    jnxFireflyPerimeterSlotPower        OBJECT IDENTIFIER ::= { jnxSlotFireflyPerimeter     3  }
    jnxFireflyPerimeterSlotFan          OBJECT IDENTIFIER ::= { jnxSlotFireflyPerimeter     4  }

  jnxMediaCardSpaceFireflyPerimeter      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    96 }
    jnxFireflyPerimeterMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceFireflyPerimeter 1 }

  jnxMidplaneFireflyPerimeter           OBJECT IDENTIFIER ::= { jnxBackplane    96 }

  jnxModuleFireflyPerimeter             OBJECT IDENTIFIER ::= { jnxModule       96 }
    jnxFireflyPerimeterFPC              OBJECT IDENTIFIER ::= { jnxModuleFireflyPerimeter   1  }
    jnxFireflyPerimeterRE               OBJECT IDENTIFIER ::= { jnxModuleFireflyPerimeter   2  }
    jnxFireflyPerimeterPower            OBJECT IDENTIFIER ::= { jnxModuleFireflyPerimeter   3  }
    jnxFireflyPerimeterFan              OBJECT IDENTIFIER ::= { jnxModuleFireflyPerimeter   4  }


--
-- MX104
--
  jnxProductLineMX104      OBJECT IDENTIFIER ::= { jnxProductLine      97 }
  jnxProductNameMX104      OBJECT IDENTIFIER ::= { jnxProductName      97 }
  jnxProductModelMX104     OBJECT IDENTIFIER ::= { jnxProductModel     97 }
  jnxProductVariationMX104 OBJECT IDENTIFIER ::= { jnxProductVariation 97 }
    jnxProductMX104   OBJECT IDENTIFIER ::= { jnxProductVariationMX104 1 }
  jnxChassisMX104          OBJECT IDENTIFIER ::= { jnxChassis          97 }

  jnxSlotMX104             OBJECT IDENTIFIER ::= { jnxSlot     97 }
    jnxMX104SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX104 1  }
    jnxMX104SlotAFEB       OBJECT IDENTIFIER ::= { jnxSlotMX104 2  }
    jnxMX104SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMX104 3  }
    jnxMX104SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX104 4  }
    jnxMX104SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX104 5  }
    jnxMX104SlotFPM        OBJECT IDENTIFIER ::= { jnxSlotMX104 6  }

  jnxMediaCardSpaceMX104      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    97 }
    jnxMX104MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX104 1 }
    jnxMX104MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX104 2 }

  jnxMidplaneMX104         OBJECT IDENTIFIER ::= { jnxBackplane        97 }

  jnxModuleMX104           OBJECT IDENTIFIER ::= { jnxModule     97 }
    jnxMX104FPC            OBJECT IDENTIFIER ::= { jnxModuleMX104 1  }
    jnxMX104FEB            OBJECT IDENTIFIER ::= { jnxModuleMX104 2  }
    jnxMX104RE             OBJECT IDENTIFIER ::= { jnxModuleMX104 3  }
    jnxMX104Power          OBJECT IDENTIFIER ::= { jnxModuleMX104 4  }
    jnxMX104PowerAC        OBJECT IDENTIFIER ::= { jnxModuleMX104 5  }
    jnxMX104Fan            OBJECT IDENTIFIER ::= { jnxModuleMX104 6  }
    jnxMX104FPM            OBJECT IDENTIFIER ::= { jnxModuleMX104 7  }


--
-- PTX3000 - Hendricks Chassis
--

  jnxProductLinePTX3000      OBJECT IDENTIFIER ::= { jnxProductLine      98 }
  jnxProductNamePTX3000      OBJECT IDENTIFIER ::= { jnxProductName      98 }
  jnxProductModelPTX3000     OBJECT IDENTIFIER ::= { jnxProductModel     98 }
  jnxProductVariationPTX3000 OBJECT IDENTIFIER ::= { jnxProductVariation 98 }
  jnxChassisPTX3000          OBJECT IDENTIFIER ::= { jnxChassis          98 }

  jnxSlotPTX3000             OBJECT IDENTIFIER ::= { jnxSlot             98 }
    jnxPTX3000SlotSIB        OBJECT IDENTIFIER ::= { jnxSlotPTX3000 1  }
    jnxPTX3000SlotHM         OBJECT IDENTIFIER ::= { jnxSlotPTX3000 2  }
    jnxPTX3000SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotPTX3000 3  }
    jnxPTX3000SlotFan        OBJECT IDENTIFIER ::= { jnxSlotPTX3000 4  }
    jnxPTX3000SlotCB         OBJECT IDENTIFIER ::= { jnxSlotPTX3000 5  }
    jnxPTX3000SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotPTX3000 6  }
    jnxPTX3000SlotPSM        OBJECT IDENTIFIER ::= { jnxSlotPTX3000 7  }
    jnxPTX3000SlotPIC        OBJECT IDENTIFIER ::= { jnxSlotPTX3000 8  }

  jnxMediaCardSpacePTX3000      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    98 }
    jnxPTX3000MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpacePTX3000 1 }

  jnxMidplanePTX3000         OBJECT IDENTIFIER ::= { jnxBackplane        98 }

  jnxModulePTX3000           OBJECT IDENTIFIER ::= { jnxModule    98 }
    jnxPTX3000SIB            OBJECT IDENTIFIER ::= { jnxModulePTX3000 1  }
    jnxPTX3000HM             OBJECT IDENTIFIER ::= { jnxModulePTX3000 2  }
    jnxPTX3000FPC            OBJECT IDENTIFIER ::= { jnxModulePTX3000 3  }
    jnxPTX3000Fan            OBJECT IDENTIFIER ::= { jnxModulePTX3000 4  }
    jnxPTX3000CB             OBJECT IDENTIFIER ::= { jnxModulePTX3000 5  }
    jnxPTX3000FPB            OBJECT IDENTIFIER ::= { jnxModulePTX3000 6  }
    jnxPTX3000PSM            OBJECT IDENTIFIER ::= { jnxModulePTX3000 7  }
    jnxPTX3000PIC            OBJECT IDENTIFIER ::= { jnxModulePTX3000 8  }



--
-- MX2010
--
  jnxProductLineMX2010      OBJECT IDENTIFIER ::= { jnxProductLine      99 }
  jnxProductNameMX2010      OBJECT IDENTIFIER ::= { jnxProductName      99 }
  jnxProductModelMX2010     OBJECT IDENTIFIER ::= { jnxProductModel     99 }
  jnxProductVariationMX2010 OBJECT IDENTIFIER ::= { jnxProductVariation 99 }
  jnxChassisMX2010          OBJECT IDENTIFIER ::= { jnxChassis          99 }
  jnxSlotMX2010             OBJECT IDENTIFIER ::= { jnxSlot             99 }
    jnxMX2010SlotSFB        OBJECT IDENTIFIER ::= { jnxSlotMX2010 1  }
                            -- Switch Fabric Board
    jnxMX2010SlotHM         OBJECT IDENTIFIER ::= { jnxSlotMX2010 2  }
                            -- Host Module (also called Routing Engine {RE})
    jnxMX2010SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX2010 3  }
                            -- Flexible Port Concentrator slot
    jnxMX2010SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX2010 4  }
    jnxMX2010SlotCB         OBJECT IDENTIFIER ::= { jnxSlotMX2010 5  }
                            -- Control Board (hosts RE, SPMB)
    jnxMX2010SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotMX2010 6  }
                            -- Front Panel Board
    jnxMX2010SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotMX2010 7  }
                            -- Processor Mezzanine Board for SFB
    jnxMX2010SlotPDM        OBJECT IDENTIFIER ::= { jnxSlotMX2010 8  }
                            -- Power Distribution Module
    jnxMX2010SlotPSM        OBJECT IDENTIFIER ::= { jnxSlotMX2010 9  }
                            -- Power Supply Module
    jnxMX2010SlotADC        OBJECT IDENTIFIER ::= { jnxSlotMX2010 10 }
                            -- Adapter Card (connects FPC to backplane)

  jnxMediaCardSpaceMX2010      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    99 }
    jnxMX2010MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX2010 1 }
    jnxMX2010MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX2010 2 }

  jnxBackplaneMX2010         OBJECT IDENTIFIER ::= { jnxBackplane        99 }
    jnxBackplaneLowerMX2010    OBJECT IDENTIFIER ::= { jnxBackplaneMX2010   1 }
    jnxBackplaneUpperMX2010    OBJECT IDENTIFIER ::= { jnxBackplaneMX2010   2 }
    jnxBackplanePowerMX2010    OBJECT IDENTIFIER ::= { jnxBackplaneMX2010   3 }

  jnxModuleMX2010           OBJECT IDENTIFIER ::= { jnxModule    99 }
    jnxMX2010SFB            OBJECT IDENTIFIER ::= { jnxModuleMX2010 1  }
    jnxMX2010HM             OBJECT IDENTIFIER ::= { jnxModuleMX2010 2  }
    jnxMX2010FPC            OBJECT IDENTIFIER ::= { jnxModuleMX2010 3  }
    jnxMX2010Fan            OBJECT IDENTIFIER ::= { jnxModuleMX2010 4  }
    jnxMX2010CB             OBJECT IDENTIFIER ::= { jnxModuleMX2010 5  }
    jnxMX2010FPB            OBJECT IDENTIFIER ::= { jnxModuleMX2010 6  }
    jnxMX2010SPMB           OBJECT IDENTIFIER ::= { jnxModuleMX2010 7  }
    jnxMX2010PDM            OBJECT IDENTIFIER ::= { jnxModuleMX2010 8  }
    jnxMX2010PSM            OBJECT IDENTIFIER ::= { jnxModuleMX2010 9  }
    jnxMX2010ADC            OBJECT IDENTIFIER ::= { jnxModuleMX2010 10 }


--
-- QFX3100
--
  jnxProductLineQFX3100      OBJECT IDENTIFIER ::= { jnxProductLine      100 }
  jnxProductNameQFX3100      OBJECT IDENTIFIER ::= { jnxProductName      100 }
  jnxProductModelQFX3100     OBJECT IDENTIFIER ::= { jnxProductModel     100 }
  jnxProductVariationQFX3100 OBJECT IDENTIFIER ::= { jnxProductVariation 100 }
  jnxChassisQFX3100          OBJECT IDENTIFIER ::= { jnxChassis          100 }

  jnxSlotQFX3100              OBJECT IDENTIFIER ::= { jnxSlot            100 }
    jnxQFX3100SlotCPU         OBJECT IDENTIFIER ::= { jnxSlotQFX3100   1 }
    jnxQFX3100SlotMemory      OBJECT IDENTIFIER ::= { jnxSlotQFX3100   2 }
    jnxQFX3100SlotPower       OBJECT IDENTIFIER ::= { jnxSlotQFX3100   3 }
    jnxQFX3100SlotFan         OBJECT IDENTIFIER ::= { jnxSlotQFX3100   4 }
    jnxQFX3100SlotHardDisk    OBJECT IDENTIFIER ::= { jnxSlotQFX3100   5 }
    jnxQFX3100SlotNIC         OBJECT IDENTIFIER ::= { jnxSlotQFX3100   6 }


-- LN2800 (Tesla_EU)
--
  jnxProductLineLN2800       OBJECT IDENTIFIER ::= { jnxProductLine 101 }
  jnxProductNameLN2800       OBJECT IDENTIFIER ::= { jnxProductName 101 }
  jnxChassisLN2800           OBJECT IDENTIFIER ::= { jnxChassis     101 }
  jnxSlotLN2800              OBJECT IDENTIFIER ::= { jnxSlot 101 }
    jnxLN2800SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotLN2800  1  }
    jnxLN2800SlotRE          OBJECT IDENTIFIER ::= { jnxSlotLN2800  2  }
    jnxLN2800SlotPower       OBJECT IDENTIFIER ::= { jnxSlotLN2800  3  }
    jnxLN2800SlotFan         OBJECT IDENTIFIER ::= { jnxSlotLN2800  4  }

  jnxMediaCardSpaceLN2800    OBJECT IDENTIFIER ::= { jnxMediaCardSpace 101 }
    jnxLN2800MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceLN2800  1 }

  jnxMidplaneLN2800         OBJECT IDENTIFIER ::= { jnxBackplane 101 }

  jnxModuleLN2800            OBJECT IDENTIFIER ::= { jnxModule 101 }
    jnxLN2800FPC             OBJECT IDENTIFIER ::= { jnxModuleLN2800 1  }
    jnxLN2800RE              OBJECT IDENTIFIER ::= { jnxModuleLN2800 2  }
    jnxLN2800Power           OBJECT IDENTIFIER ::= { jnxModuleLN2800 3  }


--
-- EX9214
--

  jnxProductLineEX9214      OBJECT IDENTIFIER ::= { jnxProductLine      102 }
  jnxProductNameEX9214      OBJECT IDENTIFIER ::= { jnxProductName      102 }
  jnxProductModelEX9214     OBJECT IDENTIFIER ::= { jnxProductModel     102 }
  jnxProductVariationEX9214 OBJECT IDENTIFIER ::= { jnxProductVariation 102 }
  jnxChassisEX9214          OBJECT IDENTIFIER ::= { jnxChassis          102 }

  jnxSlotEX9214             OBJECT IDENTIFIER ::= { jnxSlot             102 }
    jnxEX9214SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX9214 1  }
    jnxEX9214SlotHM         OBJECT IDENTIFIER ::= { jnxSlotEX9214 2  }
    jnxEX9214SlotPower      OBJECT IDENTIFIER ::= { jnxSlotEX9214 3  }
    jnxEX9214SlotFan        OBJECT IDENTIFIER ::= { jnxSlotEX9214 4  }
    jnxEX9214SlotCB         OBJECT IDENTIFIER ::= { jnxSlotEX9214 5  }
    jnxEX9214SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotEX9214 6  }

  jnxMediaCardSpaceEX9214      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    102 }
    jnxEX9214MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9214 1 }
    jnxEX9214MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9214 2 }

  jnxMidplaneEX9214         OBJECT IDENTIFIER ::= { jnxBackplane        102 }


--
-- EX9208
--

  jnxProductLineEX9208      OBJECT IDENTIFIER ::= { jnxProductLine      103 }
  jnxProductNameEX9208      OBJECT IDENTIFIER ::= { jnxProductName      103 }
  jnxProductModelEX9208     OBJECT IDENTIFIER ::= { jnxProductModel     103 }
  jnxProductVariationEX9208 OBJECT IDENTIFIER ::= { jnxProductVariation 103 }
  jnxChassisEX9208          OBJECT IDENTIFIER ::= { jnxChassis          103 }

  jnxSlotEX9208             OBJECT IDENTIFIER ::= { jnxSlot             103 }
    jnxEX9208SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX9208 1  }
    jnxEX9208SlotHM         OBJECT IDENTIFIER ::= { jnxSlotEX9208 2  }
    jnxEX9208SlotPower      OBJECT IDENTIFIER ::= { jnxSlotEX9208 3  }
    jnxEX9208SlotFan        OBJECT IDENTIFIER ::= { jnxSlotEX9208 4  }
    jnxEX9208SlotCB         OBJECT IDENTIFIER ::= { jnxSlotEX9208 5  }
    jnxEX9208SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotEX9208 6  }

  jnxMediaCardSpaceEX9208      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    103 }
  jnxEX9208MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9208 1 }
  jnxEX9208MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9208 2 }

  jnxMidplaneEX9208         OBJECT IDENTIFIER ::= { jnxBackplane        103 }


--
-- EX9204
--

  jnxProductLineEX9204      OBJECT IDENTIFIER ::= { jnxProductLine      104 }
  jnxProductNameEX9204      OBJECT IDENTIFIER ::= { jnxProductName      104 }
  jnxProductModelEX9204     OBJECT IDENTIFIER ::= { jnxProductModel     104 }
  jnxProductVariationEX9204 OBJECT IDENTIFIER ::= { jnxProductVariation 104 }
  jnxChassisEX9204          OBJECT IDENTIFIER ::= { jnxChassis          104 }

  jnxSlotEX9204             OBJECT IDENTIFIER ::= { jnxSlot             104 }
  jnxEX9204SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotEX9204 1  }
  jnxEX9204SlotHM           OBJECT IDENTIFIER ::= { jnxSlotEX9204 2  }
  jnxEX9204SlotPower        OBJECT IDENTIFIER ::= { jnxSlotEX9204 3  }
  jnxEX9204SlotFan          OBJECT IDENTIFIER ::= { jnxSlotEX9204 4  }
  jnxEX9204SlotCB           OBJECT IDENTIFIER ::= { jnxSlotEX9204 5  }
  jnxEX9204SlotFPB          OBJECT IDENTIFIER ::= { jnxSlotEX9204 6  }
  jnxMediaCardSpaceEX9204   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    104 }
  jnxEX9204MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9204 1 }
  jnxEX9204MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9204 2 }

  jnxMidplaneEX9204         OBJECT IDENTIFIER ::= { jnxBackplane         104 }


--
-- A15 (SRX5400)
--

  jnxProductLineSRX5400      OBJECT IDENTIFIER ::= { jnxProductLine      105 }
  jnxProductNameSRX5400      OBJECT IDENTIFIER ::= { jnxProductName      105 }
  jnxProductModelSRX5400     OBJECT IDENTIFIER ::= { jnxProductModel     105 }
  jnxProductVariationSRX5400 OBJECT IDENTIFIER ::= { jnxProductVariation 105 }
  jnxChassisSRX5400          OBJECT IDENTIFIER ::= { jnxChassis          105 }

  jnxSlotSRX5400             OBJECT IDENTIFIER ::= { jnxSlot             105 }
    jnxSRX5400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX5400 1  }
    jnxSRX5400SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX5400 2  }
    jnxSRX5400SlotPower      OBJECT IDENTIFIER ::= { jnxSlotSRX5400 3  }
    jnxSRX5400SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX5400 4  }
    jnxSRX5400SlotCB         OBJECT IDENTIFIER ::= { jnxSlotSRX5400 5  }
    jnxSRX5400SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotSRX5400 6  }

  jnxMediaCardSpaceSRX5400      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    105 }
  jnxSRX5400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX5400 1 }


  jnxMidplaneSRX5400         OBJECT IDENTIFIER ::= { jnxBackplane        105 }

--
-- IBM4274S54J54S (A15 IBM OEM)
--

  jnxProductLineIBM4274S54J54S      OBJECT IDENTIFIER ::= { jnxProductLine      106 }
  jnxProductNameIBM4274S54J54S      OBJECT IDENTIFIER ::= { jnxProductName      106 }
  jnxProductModelIBM4274S54J54S     OBJECT IDENTIFIER ::= { jnxProductModel     106 }
  jnxProductVariationIBM4274S54J54S OBJECT IDENTIFIER ::= { jnxProductVariation 106 }
  jnxChassisIBM4274S54J54S          OBJECT IDENTIFIER ::= { jnxChassis          106 }

  jnxSlotIBM4274S54J54S             OBJECT IDENTIFIER ::= { jnxSlot             106 }
    jnxIBM4274S54J54SSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S54J54S 1  }
    jnxIBM4274S54J54SSlotHM         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S54J54S 2  }
    jnxIBM4274S54J54SSlotPower      OBJECT IDENTIFIER ::= { jnxSlotIBM4274S54J54S 3  }
    jnxIBM4274S54J54SSlotFan        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S54J54S 4  }
    jnxIBM4274S54J54SSlotCB         OBJECT IDENTIFIER ::= { jnxSlotIBM4274S54J54S 5  }
    jnxIBM4274S54J54SSlotFPB        OBJECT IDENTIFIER ::= { jnxSlotIBM4274S54J54S 6  }

  jnxMediaCardSpaceIBM4274S54J54S      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    106 }
  jnxIBM4274S54J54SMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceIBM4274S54J54S 1 }

  jnxMidplaneIBM4274S54J54S         OBJECT IDENTIFIER ::= { jnxBackplane        106 }

--
-- DELLJSRX5400 (A15 DELL OEM)
--

  jnxProductLineDELLJSRX5400      OBJECT IDENTIFIER ::= { jnxProductLine      107 }
  jnxProductNameDELLJSRX5400      OBJECT IDENTIFIER ::= { jnxProductName      107 }
  jnxProductModelDELLJSRX5400     OBJECT IDENTIFIER ::= { jnxProductModel     107 }
  jnxProductVariationDELLJSRX5400 OBJECT IDENTIFIER ::= { jnxProductVariation 107 }
  jnxChassisDELLJSRX5400          OBJECT IDENTIFIER ::= { jnxChassis          107 }

  jnxSlotDELLJSRX5400             OBJECT IDENTIFIER ::= { jnxSlot             107 }
    jnxDELLJSRX5400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5400 1  }
    jnxDELLJSRX5400SlotHM         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5400 2  }
    jnxDELLJSRX5400SlotPower      OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5400 3  }
    jnxDELLJSRX5400SlotFan        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5400 4  }
    jnxDELLJSRX5400SlotCB         OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5400 5  }
    jnxDELLJSRX5400SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotDELLJSRX5400 6  }

  jnxMediaCardSpaceDELLJSRX5400      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    107 }
  jnxDELLJSRX5400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceDELLJSRX5400 1 }

  jnxMidplaneDELLJSRX5400         OBJECT IDENTIFIER ::= { jnxBackplane        107 }


--
-- VMX
--

  jnxProductLineVMX      OBJECT IDENTIFIER ::= { jnxProductLine      108 }
  jnxProductNameVMX      OBJECT IDENTIFIER ::= { jnxProductName      108 }
  jnxProductModelVMX     OBJECT IDENTIFIER ::= { jnxProductModel   108 }
  jnxChassisVMX          OBJECT IDENTIFIER ::= { jnxChassis          108 }

  jnxSlotVMX             OBJECT IDENTIFIER ::= { jnxSlot    108 }
    jnxVMXSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotVMX  1  }
    jnxVMxSlotPower       OBJECT IDENTIFIER ::= { jnxSlotVMX  2  }
    jnxVMXSlotFan         OBJECT IDENTIFIER ::= { jnxSlotVMX  3  }
    jnxVMXSlotCB          OBJECT IDENTIFIER ::= { jnxSlotVMX  4  }
    jnxVMXSlotHM          OBJECT IDENTIFIER ::= { jnxSlotVMX  5  }

  jnxMediaCardSpaceVMX      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    108 }
    jnxVMXMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceVMX 1 }
    jnxVMXMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceVMX 2 }

  jnxMidplaneVMX         OBJECT IDENTIFIER ::= { jnxBackplane 108 }


--
-- EX4600
--

  jnxProductLineEX4600      OBJECT IDENTIFIER ::= { jnxProductLine      109 }
  jnxProductNameEX4600      OBJECT IDENTIFIER ::= { jnxProductName      109 }
  jnxProductModelEX4600     OBJECT IDENTIFIER ::= { jnxProductModel     109 }
  jnxProductVariationEX4600 OBJECT IDENTIFIER ::= { jnxProductVariation 109 }
    jnxProductEX4600        OBJECT IDENTIFIER ::= { jnxProductVariationEX4600 1 }

  jnxChassisEX4600          OBJECT IDENTIFIER ::= { jnxChassis          109 }

  jnxSlotEX4600                 OBJECT IDENTIFIER ::= { jnxSlot             109 }
    jnxEX4600SlotFPC            OBJECT IDENTIFIER ::= { jnxSlotEX4600      1 }
    jnxEX4600HM                 OBJECT IDENTIFIER ::= { jnxSlotEX4600      2 }
    jnxEX4600SlotPower          OBJECT IDENTIFIER ::= { jnxSlotEX4600      3 }
    jnxEX4600SlotFan            OBJECT IDENTIFIER ::= { jnxSlotEX4600      4 }

  jnxMediaCardSpaceEX4600      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    109 }
    jnxEX4600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4600 1 }

--
-- VRR
--
    jnxProductLineVRR   OBJECT IDENTIFIER ::= { jnxProductLine  110 }
    jnxProductModelVRR  OBJECT IDENTIFIER ::= { jnxProductModel 110 }
    jnxProductNameVRR   OBJECT IDENTIFIER ::= { jnxProductName  110 }
    jnxChassisVRR       OBJECT IDENTIFIER ::= { jnxChassis      110 }
    jnxSlotVRR          OBJECT IDENTIFIER ::= { jnxSlot         110 }
      jnxVRRSlotFPC       OBJECT IDENTIFIER ::= { jnxSlotVRR      1 }
      jnxVRRSlotRE        OBJECT IDENTIFIER ::= { jnxSlotVRR      2 }
      jnxVRRSlotPower     OBJECT IDENTIFIER ::= { jnxSlotVRR      3 }
      jnxVRRSlotFan       OBJECT IDENTIFIER ::= { jnxSlotVRR      4 }
      jnxVRRSlotHM        OBJECT IDENTIFIER ::= { jnxSlotVRR      5 }
      jnxVRRSlotCB        OBJECT IDENTIFIER ::= { jnxSlotVRR      6 }
      jnxVRRSlotFPB       OBJECT IDENTIFIER ::= { jnxSlotVRR      7 }
    jnxMidplaneVRR      OBJECT IDENTIFIER ::= { jnxBackplane    110 }
    jnxModuleVRR        OBJECT IDENTIFIER ::= { jnxModule       110 }
      jnxVRRFPC         OBJECT IDENTIFIER ::= { jnxModuleVRR      1 }
      jnxVRRRE          OBJECT IDENTIFIER ::= { jnxModuleVRR      2 }
      jnxVRRPower       OBJECT IDENTIFIER ::= { jnxModuleVRR      3 }
      jnxVRRFan         OBJECT IDENTIFIER ::= { jnxModuleVRR      4 }


--
-- MX104-40G
--
    jnxProductNameMX10440G  OBJECT IDENTIFIER ::= { jnxProductName   111 }


-- OCPAccton Juniper branded AS5712xx switches
  jnxProductLineOCPAcc      OBJECT IDENTIFIER ::= { jnxProductLine      112 }
  jnxProductNameOCPAcc      OBJECT IDENTIFIER ::= { jnxProductName      112 }
  jnxProductModelOCPAcc     OBJECT IDENTIFIER ::= { jnxProductModel     112 }
  jnxProductVariationOCPAcc OBJECT IDENTIFIER ::= { jnxProductVariation 112 }
    jnxProductOCP48S        OBJECT IDENTIFIER ::= { jnxProductVariationOCPAcc 1 }
    jnxProductOCP48T        OBJECT IDENTIFIER ::= { jnxProductVariationOCPAcc 2 }

--
-- ACX1000
--
  jnxProductLineACX1000      OBJECT IDENTIFIER ::= { jnxProductLine      113 }
  jnxProductNameACX1000      OBJECT IDENTIFIER ::= { jnxProductName      113 }
  jnxProductModelACX1000     OBJECT IDENTIFIER ::= { jnxProductModel     113 }
  jnxProductVariationACX1000 OBJECT IDENTIFIER ::= { jnxProductVariation 113 }
    jnxProductACX1000        OBJECT IDENTIFIER ::= { jnxProductVariationACX1000 1 }
  jnxChassisACX1000          OBJECT IDENTIFIER ::= { jnxChassis          113 }

  jnxSlotACX1000             OBJECT IDENTIFIER ::= { jnxSlot        113 }
    jnxACX1000SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX1000 1  }
    jnxACX1000SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX1000 2  }
    jnxACX1000SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX1000 3  }
    jnxACX1000SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX1000 4  }

  jnxMediaCardSpaceACX1000      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    113 }
    jnxACX1000MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX1000 1 }
    jnxACX1000MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX1000 2 }

  jnxMidplaneACX1000         OBJECT IDENTIFIER ::= { jnxBackplane        113 }

  jnxModuleACX1000           OBJECT IDENTIFIER ::= { jnxModule     113 }
    jnxACX1000FPC            OBJECT IDENTIFIER ::= { jnxModuleACX1000 1  }
    jnxACX1000FEB            OBJECT IDENTIFIER ::= { jnxModuleACX1000 2  }
    jnxACX1000RE             OBJECT IDENTIFIER ::= { jnxModuleACX1000 3  }
    jnxACX1000Power          OBJECT IDENTIFIER ::= { jnxModuleACX1000 4  }
        jnxACX1000PowerDC    OBJECT IDENTIFIER ::= { jnxACX1000Power 1  }

--
-- ACX2000
--
  jnxProductLineACX2000      OBJECT IDENTIFIER ::= { jnxProductLine      114 }
  jnxProductNameACX2000      OBJECT IDENTIFIER ::= { jnxProductName      114 }
  jnxProductModelACX2000     OBJECT IDENTIFIER ::= { jnxProductModel     114 }
  jnxProductVariationACX2000 OBJECT IDENTIFIER ::= { jnxProductVariation 114 }
    jnxProductACX2000        OBJECT IDENTIFIER ::= { jnxProductVariationACX2000 1 }
  jnxChassisACX2000          OBJECT IDENTIFIER ::= { jnxChassis          114 }

  jnxSlotACX2000             OBJECT IDENTIFIER ::= { jnxSlot        114 }
    jnxACX2000SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX2000 1  }
    jnxACX2000SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX2000 2  }
    jnxACX2000SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX2000 3  }
    jnxACX2000SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX2000 4  }

  jnxMediaCardSpaceACX2000      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    114 }
    jnxACX2000MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX2000 1 }
    jnxACX2000MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX2000 2 }

  jnxMidplaneACX2000         OBJECT IDENTIFIER ::= { jnxBackplane        114 }

  jnxModuleACX2000           OBJECT IDENTIFIER ::= { jnxModule     114 }
    jnxACX2000FPC            OBJECT IDENTIFIER ::= { jnxModuleACX2000 1  }
    jnxACX2000FEB            OBJECT IDENTIFIER ::= { jnxModuleACX2000 2  }
    jnxACX2000RE             OBJECT IDENTIFIER ::= { jnxModuleACX2000 3  }
    jnxACX2000Power          OBJECT IDENTIFIER ::= { jnxModuleACX2000 4  }
        jnxACX2000PowerDC    OBJECT IDENTIFIER ::= { jnxACX2000Power 1  }

--
-- ACX1100
--
  jnxProductLineACX1100      OBJECT IDENTIFIER ::= { jnxProductLine      115 }
  jnxProductNameACX1100      OBJECT IDENTIFIER ::= { jnxProductName      115 }
  jnxProductModelACX1100     OBJECT IDENTIFIER ::= { jnxProductModel     115 }
  jnxProductVariationACX1100 OBJECT IDENTIFIER ::= { jnxProductVariation 115 }
    jnxProductACX1100        OBJECT IDENTIFIER ::= { jnxProductVariationACX1100 1 }
  jnxChassisACX1100          OBJECT IDENTIFIER ::= { jnxChassis          115 }

  jnxSlotACX1100             OBJECT IDENTIFIER ::= { jnxSlot        115 }
    jnxACX1100SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX1100 1  }
    jnxACX1100SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX1100 2  }
    jnxACX1100SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX1100 3  }
    jnxACX1100SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX1100 4  }

  jnxMediaCardSpaceACX1100      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    115 }
    jnxACX1100MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX1100 1 }
    jnxACX1100MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX1100 2 }

  jnxMidplaneACX1100         OBJECT IDENTIFIER ::= { jnxBackplane        115 }

  jnxModuleACX1100           OBJECT IDENTIFIER ::= { jnxModule     115 }
    jnxACX1100FPC            OBJECT IDENTIFIER ::= { jnxModuleACX1100 1  }
    jnxACX1100FEB            OBJECT IDENTIFIER ::= { jnxModuleACX1100 2  }
    jnxACX1100RE             OBJECT IDENTIFIER ::= { jnxModuleACX1100 3  }
    jnxACX1100Power          OBJECT IDENTIFIER ::= { jnxModuleACX1100 4  }
        jnxACX1100PowerDC    OBJECT IDENTIFIER ::= { jnxACX1100Power 1  }
        jnxACX1100PowerAC    OBJECT IDENTIFIER ::= { jnxACX1100Power 2  }

--
-- ACX2100
--
  jnxProductLineACX2100      OBJECT IDENTIFIER ::= { jnxProductLine      116 }
  jnxProductNameACX2100      OBJECT IDENTIFIER ::= { jnxProductName      116 }
  jnxProductModelACX2100     OBJECT IDENTIFIER ::= { jnxProductModel     116 }
  jnxProductVariationACX2100 OBJECT IDENTIFIER ::= { jnxProductVariation 116 }
    jnxProductACX2100        OBJECT IDENTIFIER ::= { jnxProductVariationACX2100 1 }
  jnxChassisACX2100          OBJECT IDENTIFIER ::= { jnxChassis          116 }

  jnxSlotACX2100             OBJECT IDENTIFIER ::= { jnxSlot        116 }
    jnxACX2100SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX2100 1  }
    jnxACX2100SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX2100 2  }
    jnxACX2100SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX2100 3  }
    jnxACX2100SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX2100 4  }

  jnxMediaCardSpaceACX2100      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    116 }
    jnxACX2100MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX2100 1 }
    jnxACX2100MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX2100 2 }

  jnxMidplaneACX2100         OBJECT IDENTIFIER ::= { jnxBackplane        116 }

  jnxModuleACX2100           OBJECT IDENTIFIER ::= { jnxModule     116 }
    jnxACX2100FPC            OBJECT IDENTIFIER ::= { jnxModuleACX2100 1  }
    jnxACX2100FEB            OBJECT IDENTIFIER ::= { jnxModuleACX2100 2  }
    jnxACX2100RE             OBJECT IDENTIFIER ::= { jnxModuleACX2100 3  }
    jnxACX2100Power          OBJECT IDENTIFIER ::= { jnxModuleACX2100 4  }
        jnxACX2100PowerDC    OBJECT IDENTIFIER ::= { jnxACX2100Power 1  }
        jnxACX2100PowerAC    OBJECT IDENTIFIER ::= { jnxACX2100Power 2  }

--
-- ACX2200
--
  jnxProductLineACX2200      OBJECT IDENTIFIER ::= { jnxProductLine      117 }
  jnxProductNameACX2200      OBJECT IDENTIFIER ::= { jnxProductName      117 }
  jnxProductModelACX2200     OBJECT IDENTIFIER ::= { jnxProductModel     117 }
  jnxProductVariationACX2200 OBJECT IDENTIFIER ::= { jnxProductVariation 117 }
    jnxProductACX2200        OBJECT IDENTIFIER ::= { jnxProductVariationACX2200 1 }
  jnxChassisACX2200          OBJECT IDENTIFIER ::= { jnxChassis          117 }

  jnxSlotACX2200             OBJECT IDENTIFIER ::= { jnxSlot        117 }
    jnxACX2200SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX2200 1  }
    jnxACX2200SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX2200 2  }
    jnxACX2200SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX2200 3  }
    jnxACX2200SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX2200 4  }

  jnxMediaCardSpaceACX2200      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    117 }
    jnxACX2200MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX2200 1 }
    jnxACX2200MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX2200 2 }

  jnxMidplaneACX2200         OBJECT IDENTIFIER ::= { jnxBackplane        117 }

  jnxModuleACX2200           OBJECT IDENTIFIER ::= { jnxModule     117 }
    jnxACX2200FPC            OBJECT IDENTIFIER ::= { jnxModuleACX2200 1  }
    jnxACX2200FEB            OBJECT IDENTIFIER ::= { jnxModuleACX2200 2  }
    jnxACX2200RE             OBJECT IDENTIFIER ::= { jnxModuleACX2200 3  }
    jnxACX2200Power          OBJECT IDENTIFIER ::= { jnxModuleACX2200 4  }
        jnxACX2200PowerDC    OBJECT IDENTIFIER ::= { jnxACX2200Power 1  }
        jnxACX2200PowerAC    OBJECT IDENTIFIER ::= { jnxACX2200Power 2  }

--
-- ACX4000
--
  jnxProductLineACX4000      OBJECT IDENTIFIER ::= { jnxProductLine      118 }
  jnxProductNameACX4000      OBJECT IDENTIFIER ::= { jnxProductName      118 }
  jnxProductModelACX4000     OBJECT IDENTIFIER ::= { jnxProductModel     118 }
  jnxProductVariationACX4000 OBJECT IDENTIFIER ::= { jnxProductVariation 118 }
    jnxProductACX4000        OBJECT IDENTIFIER ::= { jnxProductVariationACX4000 1}
  jnxChassisACX4000          OBJECT IDENTIFIER ::= { jnxChassis          118 }

  jnxSlotACX4000             OBJECT IDENTIFIER ::= { jnxSlot     118 }
    jnxACX4000SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX4000 1  }
    jnxACX4000SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX4000 2  }
    jnxACX4000SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX4000 3  }
    jnxACX4000SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX4000 4  }
    jnxACX4000SlotFan        OBJECT IDENTIFIER ::= { jnxSlotACX4000 5  }

  jnxMediaCardSpaceACX4000   OBJECT IDENTIFIER ::= { jnxMediaCardSpace    118 }
    jnxACX4000MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX4000 1 }
    jnxACX4000MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX4000 2 }

  jnxMidplaneACX4000         OBJECT IDENTIFIER ::= { jnxBackplane        118 }

  jnxModuleACX4000           OBJECT IDENTIFIER ::= { jnxModule     118 }
    jnxACX4000FPC            OBJECT IDENTIFIER ::= { jnxModuleACX4000 1  }
    jnxACX4000FEB            OBJECT IDENTIFIER ::= { jnxModuleACX4000 2  }
    jnxACX4000RE             OBJECT IDENTIFIER ::= { jnxModuleACX4000 3  }
    jnxACX4000Power          OBJECT IDENTIFIER ::= { jnxModuleACX4000 4  }
       jnxACX4000PowerDC     OBJECT IDENTIFIER ::= { jnxACX4000Power 1  }
       jnxACX4000PowerAC     OBJECT IDENTIFIER ::= { jnxACX4000Power 2  }
    jnxACX4000Fan            OBJECT IDENTIFIER ::= { jnxModuleACX4000 5  }

--
-- ACX500AC
--
  jnxProductLineACX500AC      OBJECT IDENTIFIER ::= { jnxProductLine      119 }
  jnxProductNameACX500AC      OBJECT IDENTIFIER ::= { jnxProductName      119 }
  jnxProductModelACX500AC     OBJECT IDENTIFIER ::= { jnxProductModel     119 }
  jnxProductVariationACX500AC OBJECT IDENTIFIER ::= { jnxProductVariation 119 }
    jnxProductACX500AC        OBJECT IDENTIFIER ::= { jnxProductVariationACX500AC 1 }
  jnxChassisACX500AC          OBJECT IDENTIFIER ::= { jnxChassis          119 }

  jnxSlotACX500AC             OBJECT IDENTIFIER ::= { jnxSlot        119 }
    jnxACX500ACSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX500AC 1  }
    jnxACX500ACSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX500AC 2  }
    jnxACX500ACSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX500AC 3  }
    jnxACX500ACSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX500AC 4  }

  jnxMediaCardSpaceACX500AC      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 119 }
    jnxACX500ACMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500AC 1 }
    jnxACX500ACMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500AC 2 }

  jnxMidplaneACX500AC         OBJECT IDENTIFIER ::= { jnxBackplane        119 }

  jnxModuleACX500AC           OBJECT IDENTIFIER ::= { jnxModule     119 }
    jnxACX500ACFPC            OBJECT IDENTIFIER ::= { jnxModuleACX500AC 1  }
    jnxACX500ACFEB            OBJECT IDENTIFIER ::= { jnxModuleACX500AC 2  }
    jnxACX500ACRE             OBJECT IDENTIFIER ::= { jnxModuleACX500AC 3  }
    jnxACX500ACPower          OBJECT IDENTIFIER ::= { jnxModuleACX500AC 4  }
        jnxACX500ACPowerAC    OBJECT IDENTIFIER ::= { jnxACX500ACPower 1  }

--
-- ACX500DC
--
  jnxProductLineACX500DC      OBJECT IDENTIFIER ::= { jnxProductLine      120 }
  jnxProductNameACX500DC      OBJECT IDENTIFIER ::= { jnxProductName      120 }
  jnxProductModelACX500DC     OBJECT IDENTIFIER ::= { jnxProductModel     120 }
  jnxProductVariationACX500DC OBJECT IDENTIFIER ::= { jnxProductVariation 120 }
    jnxProductACX500DC        OBJECT IDENTIFIER ::= { jnxProductVariationACX500DC 1 }
  jnxChassisACX500DC          OBJECT IDENTIFIER ::= { jnxChassis          120 }

  jnxSlotACX500DC             OBJECT IDENTIFIER ::= { jnxSlot        120 }
    jnxACX500DCSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX500DC 1  }
    jnxACX500DCSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX500DC 2  }
    jnxACX500DCSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX500DC 3  }
    jnxACX500DCSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX500DC 4  }

  jnxMediaCardSpaceACX500DC      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 120 }
    jnxACX500DCMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500DC 1 }
    jnxACX500DCMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500DC 2 }

  jnxMidplaneACX500DC         OBJECT IDENTIFIER ::= { jnxBackplane        120 }

  jnxModuleACX500DC           OBJECT IDENTIFIER ::= { jnxModule     120 }
    jnxACX500DCFPC            OBJECT IDENTIFIER ::= { jnxModuleACX500DC 1  }
    jnxACX500DCFEB            OBJECT IDENTIFIER ::= { jnxModuleACX500DC 2  }
    jnxACX500DCRE             OBJECT IDENTIFIER ::= { jnxModuleACX500DC 3  }
    jnxACX500DCPower          OBJECT IDENTIFIER ::= { jnxModuleACX500DC 4  }
        jnxACX500DCPowerDC    OBJECT IDENTIFIER ::= { jnxACX500DCPower 1  }

--
-- ACX500OAC
--
  jnxProductLineACX500OAC      OBJECT IDENTIFIER ::= { jnxProductLine      121 }
  jnxProductNameACX500OAC      OBJECT IDENTIFIER ::= { jnxProductName      121 }
  jnxProductModelACX500OAC     OBJECT IDENTIFIER ::= { jnxProductModel     121 }
  jnxProductVariationACX500OAC OBJECT IDENTIFIER ::= { jnxProductVariation 121 }
    jnxProductACX500OAC        OBJECT IDENTIFIER ::= { jnxProductVariationACX500OAC 1 }
  jnxChassisACX500OAC          OBJECT IDENTIFIER ::= { jnxChassis          121 }

  jnxSlotACX500OAC             OBJECT IDENTIFIER ::= { jnxSlot        121 }
    jnxACX500OACSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX500OAC 1  }
    jnxACX500OACSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX500OAC 2  }
    jnxACX500OACSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX500OAC 3  }
    jnxACX500OACSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX500OAC 4  }

  jnxMediaCardSpaceACX500OAC      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 121 }
    jnxACX500OACMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500OAC 1 }
    jnxACX500OACMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500OAC 2 }

  jnxMidplaneACX500OAC         OBJECT IDENTIFIER ::= { jnxBackplane        121 }

  jnxModuleACX500OAC           OBJECT IDENTIFIER ::= { jnxModule     121 }
    jnxACX500OACFPC            OBJECT IDENTIFIER ::= { jnxModuleACX500OAC 1  }
    jnxACX500OACFEB            OBJECT IDENTIFIER ::= { jnxModuleACX500OAC 2  }
    jnxACX500OACRE             OBJECT IDENTIFIER ::= { jnxModuleACX500OAC 3  }
    jnxACX500OACPower          OBJECT IDENTIFIER ::= { jnxModuleACX500OAC 4  }
        jnxACX500OACPowerAC    OBJECT IDENTIFIER ::= { jnxACX500OACPower 1  }

--
-- ACX500ODC
--
  jnxProductLineACX500ODC      OBJECT IDENTIFIER ::= { jnxProductLine      122 }
  jnxProductNameACX500ODC      OBJECT IDENTIFIER ::= { jnxProductName      122 }
  jnxProductModelACX500ODC     OBJECT IDENTIFIER ::= { jnxProductModel     122 }
  jnxProductVariationACX500ODC OBJECT IDENTIFIER ::= { jnxProductVariation 122 }
    jnxProductACX500ODC        OBJECT IDENTIFIER ::= { jnxProductVariationACX500ODC 1 }
  jnxChassisACX500ODC          OBJECT IDENTIFIER ::= { jnxChassis          122 }

  jnxSlotACX500ODC             OBJECT IDENTIFIER ::= { jnxSlot        122 }
    jnxACX500ODCSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX500ODC 1  }
    jnxACX500ODCSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX500ODC 2  }
    jnxACX500ODCSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX500ODC 3  }
    jnxACX500ODCSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX500ODC 4  }

  jnxMediaCardSpaceACX500ODC      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 122 }
    jnxACX500ODCMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500ODC 1 }
    jnxACX500ODCMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500ODC 2 }

  jnxMidplaneACX500ODC         OBJECT IDENTIFIER ::= { jnxBackplane        122 }

  jnxModuleACX500ODC           OBJECT IDENTIFIER ::= { jnxModule     122 }
    jnxACX500ODCFPC            OBJECT IDENTIFIER ::= { jnxModuleACX500ODC 1  }
    jnxACX500ODCFEB            OBJECT IDENTIFIER ::= { jnxModuleACX500ODC 2  }
    jnxACX500ODCRE             OBJECT IDENTIFIER ::= { jnxModuleACX500ODC 3  }
    jnxACX500ODCPower          OBJECT IDENTIFIER ::= { jnxModuleACX500ODC 4  }
        jnxACX500ODCPowerDC    OBJECT IDENTIFIER ::= { jnxACX500ODCPower 1  }

--
-- ACX500OPOEAC
--
  jnxProductLineACX500OPOEAC      OBJECT IDENTIFIER ::= { jnxProductLine 123 }
  jnxProductNameACX500OPOEAC      OBJECT IDENTIFIER ::= { jnxProductName 123 }
  jnxProductModelACX500OPOEAC     OBJECT IDENTIFIER ::= { jnxProductModel     123 }
  jnxProductVariationACX500OPOEAC OBJECT IDENTIFIER ::= { jnxProductVariation 123 }
    jnxProductACX500OPOEAC        OBJECT IDENTIFIER ::= { jnxProductVariationACX500OPOEAC 1 }
  jnxChassisACX500OPOEAC          OBJECT IDENTIFIER ::= { jnxChassis          123 }

  jnxSlotACX500OPOEAC             OBJECT IDENTIFIER ::= { jnxSlot        123 }
    jnxACX500OPOEACSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEAC 1  }
    jnxACX500OPOEACSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEAC 2 }
    jnxACX500OPOEACSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEAC 3  }
    jnxACX500OPOEACSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEAC 4  }

  jnxMediaCardSpaceACX500OPOEAC      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 123 }
    jnxACX500OPOEACMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500OPOEAC 1 }
    jnxACX500OPOEACMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500OPOEAC 2 }

  jnxMidplaneACX500OPOEAC         OBJECT IDENTIFIER ::= { jnxBackplane        123 }

  jnxModuleACX500OPOEAC           OBJECT IDENTIFIER ::= { jnxModule     123 }
    jnxACX500OPOEACFPC            OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEAC 1  }
    jnxACX500OPOEACFEB            OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEAC 2  }
    jnxACX500OPOEACRE             OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEAC 3  }
    jnxACX500OPOEACPower          OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEAC 4  }
        jnxACX500OPOEACPowerAC    OBJECT IDENTIFIER ::= { jnxACX500OPOEACPower 1  }

--
-- ACX500OPOEDC
--
  jnxProductLineACX500OPOEDC      OBJECT IDENTIFIER ::= { jnxProductLine      124 }
  jnxProductNameACX500OPOEDC      OBJECT IDENTIFIER ::= { jnxProductName      124 }
  jnxProductModelACX500OPOEDC     OBJECT IDENTIFIER ::= { jnxProductModel     124 }
  jnxProductVariationACX500OPOEDC OBJECT IDENTIFIER ::= { jnxProductVariation 124 }
    jnxProductACX500OPOEDC        OBJECT IDENTIFIER ::= { jnxProductVariationACX500OPOEDC 1 }
  jnxChassisACX500OPOEDC          OBJECT IDENTIFIER ::= { jnxChassis          124 }

  jnxSlotACX500OPOEDC             OBJECT IDENTIFIER ::= { jnxSlot        124 }
    jnxACX500OPOEDCSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEDC 1  }
    jnxACX500OPOEDCSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEDC 2 }
    jnxACX500OPOEDCSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEDC 3  }
    jnxACX500OPOEDCSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX500OPOEDC 4  }

  jnxMediaCardSpaceACX500OPOEDC      OBJECT IDENTIFIER ::= { jnxMediaCardSpace 124 }
    jnxACX500OPOEDCMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500OPOEDC 1 }
    jnxACX500OPOEDCMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX500OPOEDC 2 }

  jnxMidplaneACX500OPOEDC         OBJECT IDENTIFIER ::= { jnxBackplane        124 }

  jnxModuleACX500OPOEDC           OBJECT IDENTIFIER ::= { jnxModule     124 }
    jnxACX500OPOEDCFPC            OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEDC 1  }
    jnxACX500OPOEDCFEB            OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEDC 2  }
    jnxACX500OPOEDCRE             OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEDC 3  }
    jnxACX500OPOEDCPower          OBJECT IDENTIFIER ::= { jnxModuleACX500OPOEDC 4  }
        jnxACX500OPOEDCPowerDC    OBJECT IDENTIFIER ::= { jnxACX500OPOEDCPower 1  }


--
-- V44SatelliteDevice
--
  jnxProductLineSatelliteDevice       OBJECT IDENTIFIER ::= { jnxProductLine      125 }
  jnxProductNameSatelliteDevice       OBJECT IDENTIFIER ::= { jnxProductName      125 }
  jnxProductModelSatelliteDevice      OBJECT IDENTIFIER ::= { jnxProductModel     125 }
  jnxChassisSatelliteDevice           OBJECT IDENTIFIER ::= { jnxChassis          125 }
  jnxSlotSatelliteDevice              OBJECT IDENTIFIER ::= { jnxSlot             125 }
    jnxSatelliteDeviceSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotSatelliteDevice   1 }
    jnxSatelliteDeviceSlotPower       OBJECT IDENTIFIER ::= { jnxSlotSatelliteDevice   2 }
    jnxSatelliteDeviceSlotFan         OBJECT IDENTIFIER ::= { jnxSlotSatelliteDevice   3 }
  jnxMediaCardSpaceSatelliteDevice    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   125 }
    jnxSatelliteDeviceMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSatelliteDevice 1 }


--
-- ACX5048
--
  jnxProductLineACX5048       OBJECT IDENTIFIER ::= { jnxProductLine      126 }
  jnxProductNameACX5048       OBJECT IDENTIFIER ::= { jnxProductName      126 }
  jnxProductModelACX5048      OBJECT IDENTIFIER ::= { jnxProductModel     126 }
  jnxProductVariationACX5048  OBJECT IDENTIFIER ::= { jnxProductVariation 126 }
    jnxProductACX5048         OBJECT IDENTIFIER ::= { jnxProductVariationACX5048 1 }
  jnxChassisACX5048           OBJECT IDENTIFIER ::= { jnxChassis          126 }

  jnxSlotACX5048              OBJECT IDENTIFIER ::= { jnxSlot             126 }
    jnxACX5048SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotACX5048   1 }
    jnxACX5048SlotHM          OBJECT IDENTIFIER ::= { jnxSlotACX5048   2 }
    jnxACX5048SlotPower       OBJECT IDENTIFIER ::= { jnxSlotACX5048   3 }
    jnxACX5048SlotFan         OBJECT IDENTIFIER ::= { jnxSlotACX5048   4 }
    jnxACX5048SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotACX5048   5 }

  jnxMediaCardSpaceACX5048    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   126 }
    jnxACX5048MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5048 1 }

--
-- ACX5096
--
  jnxProductLineACX5096       OBJECT IDENTIFIER ::= { jnxProductLine      127 }
  jnxProductNameACX5096       OBJECT IDENTIFIER ::= { jnxProductName      127 }
  jnxProductModelACX5096      OBJECT IDENTIFIER ::= { jnxProductModel     127 }
  jnxProductVariationACX5096  OBJECT IDENTIFIER ::= { jnxProductVariation 127 }
    jnxProductACX5096         OBJECT IDENTIFIER ::= { jnxProductVariationACX5096 1 }
  jnxChassisACX5096           OBJECT IDENTIFIER ::= { jnxChassis          127 }

  jnxSlotACX5096              OBJECT IDENTIFIER ::= { jnxSlot             127 }
    jnxACX5096SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotACX5096   1 }
    jnxACX5096SlotHM          OBJECT IDENTIFIER ::= { jnxSlotACX5096   2 }
    jnxACX5096SlotPower       OBJECT IDENTIFIER ::= { jnxSlotACX5096   3 }
    jnxACX5096SlotFan         OBJECT IDENTIFIER ::= { jnxSlotACX5096   4 }
    jnxACX5096SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotACX5096   5 }

  jnxMediaCardSpaceACX5096    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   127 }
    jnxACX5096MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5096 1 }


--
-- LN1000CC
--
  jnxProductLineLN1000CC      OBJECT IDENTIFIER ::= { jnxProductLine 128 }
  jnxProductNameLN1000CC      OBJECT IDENTIFIER ::= { jnxProductName 128 }
  jnxProductModelLN1000CC     OBJECT IDENTIFIER ::= { jnxProductModel 128 }
  jnxProductVariationLN1000CC OBJECT IDENTIFIER ::= { jnxProductVariation 128 }
  jnxChassisLN1000CC          OBJECT IDENTIFIER ::= { jnxChassis 128 }

  jnxMediaCardSpaceLN1000CC   OBJECT IDENTIFIER ::= { jnxMediaCardSpace 128 }
    jnxLN1000CCMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceLN1000CC 1 }

  jnxMidplaneLN1000CC         OBJECT IDENTIFIER ::= { jnxBackplane 128 }

  jnxSlotLN1000CC             OBJECT IDENTIFIER ::= { jnxSlot 128 }
    jnxLN1000CCSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotLN1000CC  1  }
    jnxLN1000CCSlotRE         OBJECT IDENTIFIER ::= { jnxSlotLN1000CC  2  }
    jnxLN1000CCSlotPower      OBJECT IDENTIFIER ::= { jnxSlotLN1000CC  3  }
    jnxLN1000CCSlotFan        OBJECT IDENTIFIER ::= { jnxSlotLN1000CC  4  }


--
-- VSRX 2.0
--

  jnxProductLineVSRX        OBJECT IDENTIFIER ::= { jnxProductLine  129 }
  jnxProductNameVSRX        OBJECT IDENTIFIER ::= { jnxProductName  129 }
  jnxChassisVSRX            OBJECT IDENTIFIER ::= { jnxChassis      129 }

  jnxSlotVSRX               OBJECT IDENTIFIER ::= { jnxSlot         129 }
    jnxVSRXSlotFPC          OBJECT IDENTIFIER ::= { jnxSlotVSRX     1  }
    jnxVSRXSlotRE           OBJECT IDENTIFIER ::= { jnxSlotVSRX     2  }
    jnxVSRXSlotPower        OBJECT IDENTIFIER ::= { jnxSlotVSRX     3  }
    jnxVSRXSlotFan          OBJECT IDENTIFIER ::= { jnxSlotVSRX     4  }

  jnxMediaCardSpaceVSRX      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    129 }
    jnxVSRXMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceVSRX 1 }

  jnxMidplaneVSRX           OBJECT IDENTIFIER ::= { jnxBackplane    129 }

  jnxModuleVSRX             OBJECT IDENTIFIER ::= { jnxModule       129 }
    jnxVSRXFPC              OBJECT IDENTIFIER ::= { jnxModuleVSRX   1  }
    jnxVSRXRE               OBJECT IDENTIFIER ::= { jnxModuleVSRX   2  }
    jnxVSRXPower            OBJECT IDENTIFIER ::= { jnxModuleVSRX   3  }
    jnxVSRXFan              OBJECT IDENTIFIER ::= { jnxModuleVSRX   4  }


--
-- PTX1000
--

  jnxProductLinePTX1000       OBJECT IDENTIFIER ::= { jnxProductLine      130 }
  jnxProductNamePTX1000       OBJECT IDENTIFIER ::= { jnxProductName      130 }
  jnxProductModelPTX1000      OBJECT IDENTIFIER ::= { jnxProductModel     130 }
  jnxProductVariationPTX1000  OBJECT IDENTIFIER ::= { jnxProductVariation 130 }
  jnxChassisPTX1000           OBJECT IDENTIFIER ::= { jnxChassis          130 }

  jnxSlotPTX1000              OBJECT IDENTIFIER ::= { jnxSlot             130 }
    jnxPTX1000SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotPTX1000   1 }
    jnxPTX1000SlotHM          OBJECT IDENTIFIER ::= { jnxSlotPTX1000   2 }
    jnxPTX1000SlotPower       OBJECT IDENTIFIER ::= { jnxSlotPTX1000   3 }
    jnxPTX1000SlotFan         OBJECT IDENTIFIER ::= { jnxSlotPTX1000   4 }
    jnxPTX1000SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotPTX1000   5 }

  jnxMediaCardSpacePTX1000    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   130 }
    jnxPTX1000MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpacePTX1000 1 }


--
-- EX3400
--
  jnxProductLineEX3400      OBJECT IDENTIFIER ::= { jnxProductLine      131 }
  jnxProductNameEX3400      OBJECT IDENTIFIER ::= { jnxProductName      131 }
  jnxProductModelEX3400     OBJECT IDENTIFIER ::= { jnxProductModel     131 }
  jnxProductVariationEX3400 OBJECT IDENTIFIER ::= { jnxProductVariation 131 }
    jnxProductEX3400port24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX3400 1 }
    jnxProductEX3400port48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX3400 2 }
    jnxProductEX3400port24P    OBJECT IDENTIFIER ::= { jnxProductVariationEX3400 3 }
    jnxProductEX3400port48P    OBJECT IDENTIFIER ::= { jnxProductVariationEX3400 4 }

  jnxChassisEX3400          OBJECT IDENTIFIER ::= { jnxChassis          131 }
    jnxEX3400RE0            OBJECT IDENTIFIER ::= { jnxChassisEX3400 1  }
    jnxEX3400RE1            OBJECT IDENTIFIER ::= { jnxChassisEX3400 2  }
  jnxSlotEX3400             OBJECT IDENTIFIER ::= { jnxSlot             131 }
    jnxEX3400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX3400 1 }
      jnxEX3400SlotPower    OBJECT IDENTIFIER ::= { jnxEX3400SlotFPC 1 }
      jnxEX3400SlotFan      OBJECT IDENTIFIER ::= { jnxEX3400SlotFPC 2 }

  jnxMediaCardSpaceEX3400      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    131 }
    jnxEX3400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX3400 1 }

 jnxModuleEX3400            OBJECT IDENTIFIER ::= { jnxModule    131 }
    jnxEX3400FPC            OBJECT IDENTIFIER ::= { jnxModuleEX3400 1 }
      jnxEX3400Power        OBJECT IDENTIFIER ::= { jnxEX3400FPC 1 }
      jnxEX3400Fan          OBJECT IDENTIFIER ::= { jnxEX3400FPC 2 }

--
-- EX2300
--
  jnxProductLineEX2300      OBJECT IDENTIFIER ::= { jnxProductLine      132 }
  jnxProductNameEX2300      OBJECT IDENTIFIER ::= { jnxProductName      132 }
  jnxProductModelEX2300     OBJECT IDENTIFIER ::= { jnxProductModel     132 }
  jnxProductVariationEX2300 OBJECT IDENTIFIER ::= { jnxProductVariation 132 }
    jnxProductEX2300port24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 1 }
    jnxProductEX2300port48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 2 }
    jnxProductEX2300port24P    OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 3 }
    jnxProductEX2300port48P    OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 4 }
    jnxProductEX2300Cport12T   OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 5 }
    jnxProductEX2300Cport12P   OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 6 }
    jnxProductEX2300port24MP   OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 7 }
    jnxProductEX2300port48MP   OBJECT IDENTIFIER ::= { jnxProductVariationEX2300 8 }

  jnxChassisEX2300          OBJECT IDENTIFIER ::= { jnxChassis          132 }
    jnxEX2300RE0            OBJECT IDENTIFIER ::= { jnxChassisEX2300 1  }
    jnxEX2300RE1            OBJECT IDENTIFIER ::= { jnxChassisEX2300 2  }
  jnxSlotEX2300             OBJECT IDENTIFIER ::= { jnxSlot             132 }
    jnxEX2300SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX2300 1 }
      jnxEX2300SlotPower    OBJECT IDENTIFIER ::= { jnxEX2300SlotFPC 1 }
      jnxEX2300SlotFan      OBJECT IDENTIFIER ::= { jnxEX2300SlotFPC 2 }

  jnxMediaCardSpaceEX2300      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    132 }
    jnxEX2300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX2300 1 }

 jnxModuleEX2300            OBJECT IDENTIFIER ::= { jnxModule    132 }
    jnxEX2300FPC            OBJECT IDENTIFIER ::= { jnxModuleEX2300 1 }
      jnxEX2300Power        OBJECT IDENTIFIER ::= { jnxEX2300FPC 1 }
      jnxEX2300Fan          OBJECT IDENTIFIER ::= { jnxEX2300FPC 2 }

--
-- SRX300 (Sword)
--
    jnxProductLineSRX300        OBJECT IDENTIFIER ::= { jnxProductLine 133 }
    jnxProductNameSRX300        OBJECT IDENTIFIER ::= { jnxProductName 133 }
    jnxChassisSRX300            OBJECT IDENTIFIER ::= { jnxChassis     133 }

    jnxSlotSRX300               OBJECT IDENTIFIER ::= { jnxSlot 133 }
    jnxSRX300SlotFPC            OBJECT IDENTIFIER ::= { jnxSlotSRX300  1  }
    jnxSRX300SlotRE             OBJECT IDENTIFIER ::= { jnxSlotSRX300  2  }
    jnxSRX300SlotPower          OBJECT IDENTIFIER ::= { jnxSlotSRX300  3  }
    jnxSRX300SlotFan            OBJECT IDENTIFIER ::= { jnxSlotSRX300  4  }

    jnxMediaCardSpaceSRX300     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 133 }
    jnxSRX300MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX300  1 }

    jnxMidplaneSRX300           OBJECT IDENTIFIER ::= { jnxBackplane 133 }

    jnxModuleSRX300             OBJECT IDENTIFIER ::= { jnxModule 133 }
    jnxSRX300FPC                OBJECT IDENTIFIER ::= { jnxModuleSRX300 1  }
    jnxSRX300RE                 OBJECT IDENTIFIER ::= { jnxModuleSRX300 2  }
    jnxSRX300Power              OBJECT IDENTIFIER ::= { jnxModuleSRX300 3  }
    jnxSRX300Fan                OBJECT IDENTIFIER ::= { jnxModuleSRX300 4  }

--
-- SRX320 (Sword-M & Sword-M-POE)
--
    jnxProductLineSRX320        OBJECT IDENTIFIER ::= { jnxProductLine 134 }
    jnxProductNameSRX320        OBJECT IDENTIFIER ::= { jnxProductName 134 }
    jnxChassisSRX320            OBJECT IDENTIFIER ::= { jnxChassis     134 }

    jnxSlotSRX320               OBJECT IDENTIFIER ::= { jnxSlot 134 }
    jnxSRX320SlotFPC            OBJECT IDENTIFIER ::= { jnxSlotSRX320  1  }
    jnxSRX320SlotRE             OBJECT IDENTIFIER ::= { jnxSlotSRX320  2  }
    jnxSRX320SlotPower          OBJECT IDENTIFIER ::= { jnxSlotSRX320  3  }
    jnxSRX320SlotFan            OBJECT IDENTIFIER ::= { jnxSlotSRX320  4  }

    jnxMediaCardSpaceSRX320     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 134 }
    jnxSRX320MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX320  1 }

    jnxMidplaneSRX320           OBJECT IDENTIFIER ::= { jnxBackplane 134 }

    jnxModuleSRX320             OBJECT IDENTIFIER ::= { jnxModule 134 }
    jnxSRX320FPC                OBJECT IDENTIFIER ::= { jnxModuleSRX320 1  }
    jnxSRX320RE                 OBJECT IDENTIFIER ::= { jnxModuleSRX320 2  }
    jnxSRX320Power              OBJECT IDENTIFIER ::= { jnxModuleSRX320 3  }
    jnxSRX320Fan                OBJECT IDENTIFIER ::= { jnxModuleSRX320 4  }

--
-- SRX340 (Trident)
--
    jnxProductLineSRX340        OBJECT IDENTIFIER ::= { jnxProductLine 135 }
    jnxProductNameSRX340        OBJECT IDENTIFIER ::= { jnxProductName 135 }
    jnxChassisSRX340            OBJECT IDENTIFIER ::= { jnxChassis     135 }

    jnxSlotSRX340               OBJECT IDENTIFIER ::= { jnxSlot 135 }
    jnxSRX340SlotFPC            OBJECT IDENTIFIER ::= { jnxSlotSRX340  1  }
    jnxSRX340SlotRE             OBJECT IDENTIFIER ::= { jnxSlotSRX340  2  }
    jnxSRX340SlotPower          OBJECT IDENTIFIER ::= { jnxSlotSRX340  3  }
    jnxSRX340SlotFan            OBJECT IDENTIFIER ::= { jnxSlotSRX340  4  }

    jnxMediaCardSpaceSRX340     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 135 }
    jnxSRX340MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX340  1 }

    jnxMidplaneSRX340           OBJECT IDENTIFIER ::= { jnxBackplane 135 }

    jnxModuleSRX340             OBJECT IDENTIFIER ::= { jnxModule 135 }
    jnxSRX340FPC                OBJECT IDENTIFIER ::= { jnxModuleSRX340 1  }
    jnxSRX340RE                 OBJECT IDENTIFIER ::= { jnxModuleSRX340 2  }
    jnxSRX340Power              OBJECT IDENTIFIER ::= { jnxModuleSRX340 3  }
    jnxSRX340Fan                OBJECT IDENTIFIER ::= { jnxModuleSRX340 4  }

--
-- SRX345 (Trident+)
--
    jnxProductLineSRX345        OBJECT IDENTIFIER ::= { jnxProductLine 136 }
    jnxProductNameSRX345        OBJECT IDENTIFIER ::= { jnxProductName 136 }
    jnxChassisSRX345            OBJECT IDENTIFIER ::= { jnxChassis     136 }

    jnxSlotSRX345               OBJECT IDENTIFIER ::= { jnxSlot 136 }
    jnxSRX345SlotFPC            OBJECT IDENTIFIER ::= { jnxSlotSRX345  1  }
    jnxSRX345SlotRE             OBJECT IDENTIFIER ::= { jnxSlotSRX345  2  }
    jnxSRX345SlotPower          OBJECT IDENTIFIER ::= { jnxSlotSRX345  3  }
    jnxSRX345SlotFan            OBJECT IDENTIFIER ::= { jnxSlotSRX345  4  }

    jnxMediaCardSpaceSRX345     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 136 }
    jnxSRX345MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX345  1 }

    jnxMidplaneSRX345           OBJECT IDENTIFIER ::= { jnxBackplane 136 }

    jnxModuleSRX345             OBJECT IDENTIFIER ::= { jnxModule 136 }
    jnxSRX345FPC                OBJECT IDENTIFIER ::= { jnxModuleSRX345 1  }
    jnxSRX345RE                 OBJECT IDENTIFIER ::= { jnxModuleSRX345 2  }
    jnxSRX345Power              OBJECT IDENTIFIER ::= { jnxModuleSRX345 3  }
    jnxSRX345Fan                OBJECT IDENTIFIER ::= { jnxModuleSRX345 4  }


--
-- SRX1500
--

  jnxProductLineSRX1500       OBJECT IDENTIFIER ::= { jnxProductLine  137 }
  jnxProductNameSRX1500       OBJECT IDENTIFIER ::= { jnxProductName  137 }
  jnxChassisSRX1500           OBJECT IDENTIFIER ::= { jnxChassis      137 }

  jnxSlotSRX1500              OBJECT IDENTIFIER ::= { jnxSlot         137 }
    jnxSRX1500SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotSRX1500   1  }
    jnxSRX1500SlotRE           OBJECT IDENTIFIER ::= { jnxSlotSRX1500   2  }
    jnxSRX1500SlotPower        OBJECT IDENTIFIER ::= { jnxSlotSRX1500   3  }
    jnxSRX1500SlotFan          OBJECT IDENTIFIER ::= { jnxSlotSRX1500   4  }
    jnxSRX1500SlotCB           OBJECT IDENTIFIER ::= { jnxSlotSRX1500   5  }

  jnxMediaCardSpaceSRX1500    OBJECT IDENTIFIER ::= { jnxMediaCardSpace    137 }
    jnxSRX1500MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX1500 1 }

  jnxMidplaneSRX1500          OBJECT IDENTIFIER ::= { jnxBackplane    137 }

  jnxModuleSRX1500            OBJECT IDENTIFIER ::= { jnxModule       137 }
    jnxSRX1500FPC              OBJECT IDENTIFIER ::= { jnxModuleSRX1500   1  }
    jnxSRX1500RE               OBJECT IDENTIFIER ::= { jnxModuleSRX1500   2  }
    jnxSRX1500Power            OBJECT IDENTIFIER ::= { jnxModuleSRX1500   3  }
    jnxSRX1500Fan              OBJECT IDENTIFIER ::= { jnxModuleSRX1500   4  }
    jnxSRX1500CB               OBJECT IDENTIFIER ::= { jnxModuleSRX1500   5  }


--
-- NFX
--
  jnxProductLineNFX               OBJECT IDENTIFIER ::= { jnxProductLine  138 }
  jnxProductNameNFX               OBJECT IDENTIFIER ::= { jnxProductName  138 }
  jnxProductModelNFX               OBJECT IDENTIFIER ::= { jnxProductModel 138 }
  jnxProductVariationNFX      OBJECT IDENTIFIER ::= { jnxProductVariation 138 }

    jnxProductNFX250ATTS1     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 1 }
      jnxProductNFX250ATTS1SCHost OBJECT IDENTIFIER ::= { jnxProductNFX250ATTS1 1 }
      jnxProductNFX250ATTS1SCJdm OBJECT IDENTIFIER ::= { jnxProductNFX250ATTS1 2 }
      jnxProductNFX250ATTS1SCJcp OBJECT IDENTIFIER ::= { jnxProductNFX250ATTS1 3 }

    jnxProductNFX250ATTS2     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 2 }
      jnxProductNFX250ATTS2SCHost OBJECT IDENTIFIER ::= { jnxProductNFX250ATTS2 1 }
      jnxProductNFX250ATTS2SCJdm OBJECT IDENTIFIER ::= { jnxProductNFX250ATTS2 2 }
      jnxProductNFX250ATTS2SCJcp OBJECT IDENTIFIER ::= { jnxProductNFX250ATTS2 3 }

    jnxProductNFX250ATTLS1     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 3 }
      jnxProductNFX250ATTLS1SCHost OBJECT IDENTIFIER ::= { jnxProductNFX250ATTLS1 1 }
      jnxProductNFX250ATTLS1SCJdm OBJECT IDENTIFIER ::= { jnxProductNFX250ATTLS1 2 }
      jnxProductNFX250ATTLS1SCJcp OBJECT IDENTIFIER ::= { jnxProductNFX250ATTLS1 3 }

    jnxProductNFX250S1        OBJECT IDENTIFIER ::= { jnxProductVariationNFX 4 }
      jnxProductNFX250S1SCHost OBJECT IDENTIFIER ::= { jnxProductNFX250S1 1 }
      jnxProductNFX250S1SCJdm OBJECT IDENTIFIER ::= { jnxProductNFX250S1 2 }
      jnxProductNFX250S1SCJcp OBJECT IDENTIFIER ::= { jnxProductNFX250S1 3 }

    jnxProductNFX250S2        OBJECT IDENTIFIER ::= { jnxProductVariationNFX 5 }
      jnxProductNFX250S2SCHost OBJECT IDENTIFIER ::= { jnxProductNFX250S2 1 }
      jnxProductNFX250S2SCJdm OBJECT IDENTIFIER ::= { jnxProductNFX250S2 2 }
      jnxProductNFX250S2SCJcp OBJECT IDENTIFIER ::= { jnxProductNFX250S2 3 }

    jnxProductNFX250LS1        OBJECT IDENTIFIER ::= { jnxProductVariationNFX 6 }
      jnxProductNFX250LS1SCHost OBJECT IDENTIFIER ::= { jnxProductNFX250LS1 1 }
      jnxProductNFX250LS1SCJdm OBJECT IDENTIFIER ::= { jnxProductNFX250LS1 2 }
      jnxProductNFX250LS1SCJcp OBJECT IDENTIFIER ::= { jnxProductNFX250LS1 3 }

    jnxProductNFXVirtual     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 7 }
      jnxProductNFXVirtualSCHost OBJECT IDENTIFIER ::= { jnxProductNFXVirtual 1 }
      jnxProductNFXVirtualSCJdm OBJECT IDENTIFIER ::= { jnxProductNFXVirtual 2 }
      jnxProductNFXVirtualSCJcp OBJECT IDENTIFIER ::= { jnxProductNFXVirtual 3 }

    jnxProductNFX250S1E     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 8 }
      jnxProductNFX250S1ESCHost OBJECT IDENTIFIER ::= { jnxProductNFX250S1E 1 }
      jnxProductNFX250S1ESCJdm OBJECT IDENTIFIER ::= { jnxProductNFX250S1E 2 }
      jnxProductNFX250S1ESCJcp OBJECT IDENTIFIER ::= { jnxProductNFX250S1E 3 }

    jnxProductNFX150CS1     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 9 }
    jnxProductNFX150CS1AE     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 10 }
    jnxProductNFX150CS1AA     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 11 }
    jnxProductNFX150S1     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 12 }
    jnxProductNFX350S1     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 13 }
    jnxProductNFXWhiteBox1     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 14 }
    jnxProductNFX150CS1EAE     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 15 }
    jnxProductNFX150CS1EAA     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 16 }
    jnxProductNFX150S1E     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 17 }
    jnxProductNFX350S2     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 18 }
    jnxProductNFX350S3     OBJECT IDENTIFIER ::= { jnxProductVariationNFX 19 }
    jnxProductNFXOPAL      OBJECT IDENTIFIER ::= { jnxProductVariationNFX 20 }
    jnxProductNFX350X      OBJECT IDENTIFIER ::= { jnxProductVariationNFX 21 }

  jnxChassisNFX           OBJECT IDENTIFIER ::= { jnxChassis          138 }

  jnxSlotNFX              OBJECT IDENTIFIER ::= { jnxSlot             138 }
    jnxNFXSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotNFX   1 }
    jnxNFXSlotPIC         OBJECT IDENTIFIER ::= { jnxSlotNFX   2 }
    jnxNFXSlotHM          OBJECT IDENTIFIER ::= { jnxSlotNFX   3 }
    jnxNFXSlotPower       OBJECT IDENTIFIER ::= { jnxSlotNFX   4 }
    jnxNFXSlotFan         OBJECT IDENTIFIER ::= { jnxSlotNFX   5 }

--
-- MX10003
--
  jnxProductLineJNP10003      OBJECT IDENTIFIER ::= { jnxProductLine      139 }
  jnxProductNameJNP10003      OBJECT IDENTIFIER ::= { jnxProductName      139 }
  jnxProductModelJNP10003     OBJECT IDENTIFIER ::= { jnxProductModel     139 }
  jnxProductVariationJNP10003 OBJECT IDENTIFIER ::= { jnxProductVariation 139 }
  jnxChassisJNP10003          OBJECT IDENTIFIER ::= { jnxChassis          139 }
  jnxSlotJNP10003             OBJECT IDENTIFIER ::= { jnxSlot             139 }
    jnxJNP10003SlotHM         OBJECT IDENTIFIER ::= { jnxSlotJNP10003 1  }
    jnxJNP10003SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotJNP10003 2  }
    jnxJNP10003SlotFan        OBJECT IDENTIFIER ::= { jnxSlotJNP10003 3  }
    jnxJNP10003SlotCB       OBJECT IDENTIFIER ::= { jnxSlotJNP10003 4  }
    jnxJNP10003SlotPower        OBJECT IDENTIFIER ::= { jnxSlotJNP10003 5  }

  jnxMediaCardSpaceJNP10003     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    139 }
    jnxJNP10003MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJNP10003 1 }
    jnxPicJNP1000312xQSFP28MacsecTIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJNP10003 2 }
    jnxJNP10003MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJNP10003 3 }

  jnxModuleJNP10003           OBJECT IDENTIFIER ::= { jnxModule    139 }
    jnxJNP10003HM             OBJECT IDENTIFIER ::= { jnxModuleJNP10003 1  }
    jnxJNP10003FPC            OBJECT IDENTIFIER ::= { jnxModuleJNP10003 2  }
    jnxJNP10003Fan            OBJECT IDENTIFIER ::= { jnxModuleJNP10003 3  }
    jnxJNP10003CB           OBJECT IDENTIFIER ::= { jnxModuleJNP10003 4  }
    jnxJNP10003Power            OBJECT IDENTIFIER ::= { jnxModuleJNP10003 5  }



--
-- SRX4600
--
  jnxProductLineSRX4600      OBJECT IDENTIFIER ::= { jnxProductLine      140 }
  jnxProductNameSRX4600      OBJECT IDENTIFIER ::= { jnxProductName      140 }
  jnxProductModelSRX4600     OBJECT IDENTIFIER ::= { jnxProductModel     140 }
  jnxProductVariationSRX4600 OBJECT IDENTIFIER ::= { jnxProductVariation 140 }
  jnxChassisSRX4600          OBJECT IDENTIFIER ::= { jnxChassis          140 }
  jnxSlotSRX4600             OBJECT IDENTIFIER ::= { jnxSlot             140 }
    jnxSRX4600SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX4600 1  }
    jnxSRX4600SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX4600 2  }
    jnxSRX4600SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX4600 3  }
    jnxSRX4600SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotSRX4600 4  }
    jnxSRX4600SlotPSM        OBJECT IDENTIFIER ::= { jnxSlotSRX4600 5  }

  jnxMediaCardSpaceSRX4600     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    140 }
    jnxSRX4600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX4600 1 }

  jnxModuleSRX4600           OBJECT IDENTIFIER ::= { jnxModule    140 }
    jnxSRX4600HM             OBJECT IDENTIFIER ::= { jnxModuleSRX4600 1  }
    jnxSRX4600FPC            OBJECT IDENTIFIER ::= { jnxModuleSRX4600 2  }
    jnxSRX4600Fan            OBJECT IDENTIFIER ::= { jnxModuleSRX4600 3  }
    jnxSRX4600SPMB           OBJECT IDENTIFIER ::= { jnxModuleSRX4600 4  }
    jnxSRX4600PSM            OBJECT IDENTIFIER ::= { jnxModuleSRX4600 5  }
--

--
-- SRX4800
--
  jnxProductLineSRX4800      OBJECT IDENTIFIER ::= { jnxProductLine      141 }
  jnxProductNameSRX4800      OBJECT IDENTIFIER ::= { jnxProductName      141 }
  jnxProductModelSRX4800     OBJECT IDENTIFIER ::= { jnxProductModel     141 }
  jnxProductVariationSRX4800 OBJECT IDENTIFIER ::= { jnxProductVariation 141 }
  jnxChassisSRX4800          OBJECT IDENTIFIER ::= { jnxChassis          141 }
  jnxSlotSRX4800             OBJECT IDENTIFIER ::= { jnxSlot             141 }
    jnxSRX4800SlotHM         OBJECT IDENTIFIER ::= { jnxSlotSRX4800 1  }
    jnxSRX4800SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotSRX4800 2  }
    jnxSRX4800SlotFan        OBJECT IDENTIFIER ::= { jnxSlotSRX4800 3  }
    jnxSRX4800SlotSPMB       OBJECT IDENTIFIER ::= { jnxSlotSRX4800 4  }
    jnxSRX4800SlotPSM        OBJECT IDENTIFIER ::= { jnxSlotSRX4800 5  }

  jnxMediaCardSpaceSRX4800     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    141 }
    jnxSRX4800MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX4800 1 }
    jnxSRX4800MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX4800 2 }

  jnxModuleSRX4800           OBJECT IDENTIFIER ::= { jnxModule    141 }
    jnxSRX4800HM             OBJECT IDENTIFIER ::= { jnxModuleSRX4800 1  }
    jnxSRX4800FPC            OBJECT IDENTIFIER ::= { jnxModuleSRX4800 2  }
    jnxSRX4800Fan            OBJECT IDENTIFIER ::= { jnxModuleSRX4800 3  }
    jnxSRX4800SPMB           OBJECT IDENTIFIER ::= { jnxModuleSRX4800 4  }
    jnxSRX4800PSM            OBJECT IDENTIFIER ::= { jnxModuleSRX4800 5  }
--

--
-- SRX4100
--
  jnxProductLineSRX4100       OBJECT IDENTIFIER ::= { jnxProductLine  142 }
  jnxProductNameSRX4100       OBJECT IDENTIFIER ::= { jnxProductName  142 }
  jnxChassisSRX4100           OBJECT IDENTIFIER ::= { jnxChassis      142 }
  jnxSlotSRX4100              OBJECT IDENTIFIER ::= { jnxSlot         142 }
    jnxSRX4100SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotSRX4100   1  }
    jnxSRX4100SlotRE           OBJECT IDENTIFIER ::= { jnxSlotSRX4100   2  }
    jnxSRX4100SlotPower        OBJECT IDENTIFIER ::= { jnxSlotSRX4100   3  }
    jnxSRX4100SlotFan          OBJECT IDENTIFIER ::= { jnxSlotSRX4100   4  }

  jnxMediaCardSpaceSRX4100    OBJECT IDENTIFIER ::= { jnxMediaCardSpace    142 }
    jnxSRX4100MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX4100 1 }

  jnxMidplaneSRX4100          OBJECT IDENTIFIER ::= { jnxBackplane    142 }

  jnxModuleSRX4100            OBJECT IDENTIFIER ::= { jnxModule       142 }
    jnxSRX4100FPC              OBJECT IDENTIFIER ::= { jnxModuleSRX4100   1  }
    jnxSRX4100RE               OBJECT IDENTIFIER ::= { jnxModuleSRX4100   2  }
    jnxSRX4100Power            OBJECT IDENTIFIER ::= { jnxModuleSRX4100   3  }
    jnxSRX4100Fan              OBJECT IDENTIFIER ::= { jnxModuleSRX4100   4  }



--
-- SRX4200
--
  jnxProductLineSRX4200       OBJECT IDENTIFIER ::= { jnxProductLine  143 }
  jnxProductNameSRX4200       OBJECT IDENTIFIER ::= { jnxProductName  143 }
  jnxChassisSRX4200           OBJECT IDENTIFIER ::= { jnxChassis      143 }

  jnxSlotSRX4200              OBJECT IDENTIFIER ::= { jnxSlot         143 }
    jnxSRX4200SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotSRX4200   1  }
    jnxSRX4200SlotRE           OBJECT IDENTIFIER ::= { jnxSlotSRX4200   2  }
    jnxSRX4200SlotPower        OBJECT IDENTIFIER ::= { jnxSlotSRX4200   3  }
    jnxSRX4200SlotFan          OBJECT IDENTIFIER ::= { jnxSlotSRX4200   4  }

  jnxMediaCardSpaceSRX4200    OBJECT IDENTIFIER ::= { jnxMediaCardSpace    143 }

  jnxMidplaneSRX4200          OBJECT IDENTIFIER ::= { jnxBackplane    143 }

  jnxModuleSRX4200            OBJECT IDENTIFIER ::= { jnxModule       143 }
    jnxSRX4200FPC              OBJECT IDENTIFIER ::= { jnxModuleSRX4200   1  }
    jnxSRX4200RE               OBJECT IDENTIFIER ::= { jnxModuleSRX4200   2  }
    jnxSRX4200Power            OBJECT IDENTIFIER ::= { jnxModuleSRX4200   3  }
    jnxSRX4200Fan              OBJECT IDENTIFIER ::= { jnxModuleSRX4200   4  }


--
-- MX204
--
  jnxProductLineJNP204      OBJECT IDENTIFIER ::= { jnxProductLine      144 }
  jnxProductNameJNP204      OBJECT IDENTIFIER ::= { jnxProductName      144 }
  jnxProductModelJNP204     OBJECT IDENTIFIER ::= { jnxProductModel     144 }
  jnxProductVariationJNP204 OBJECT IDENTIFIER ::= { jnxProductVariation 144 }
  jnxChassisJNP204          OBJECT IDENTIFIER ::= { jnxChassis          144 }
  jnxSlotJNP204             OBJECT IDENTIFIER ::= { jnxSlot             144 }
  jnxJNP204SlotHM           OBJECT IDENTIFIER ::= { jnxSlotJNP204 1  }
  jnxJNP204SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotJNP204 2  }
  jnxJNP204SlotFan          OBJECT IDENTIFIER ::= { jnxSlotJNP204 3  }
  jnxJNP204SlotCB           OBJECT IDENTIFIER ::= { jnxSlotJNP204 4  }
  jnxJNP204SlotPower        OBJECT IDENTIFIER ::= { jnxSlotJNP204 5  }

  jnxMediaCardSpaceJNP204   OBJECT IDENTIFIER ::= { jnxMediaCardSpace     144 }
  jnxPicJNP204MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJNP204 1 }
  jnxPicJNP2048XSFPP        OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJNP204 2 }

  jnxModuleJNP204           OBJECT IDENTIFIER ::= { jnxModule    144 }
  jnxJNP204HM               OBJECT IDENTIFIER ::= { jnxModuleJNP204 1  }
  jnxJNP204FPC              OBJECT IDENTIFIER ::= { jnxModuleJNP204 2  }
  jnxJNP204Fan              OBJECT IDENTIFIER ::= { jnxModuleJNP204 3  }
  jnxJNP204CB               OBJECT IDENTIFIER ::= { jnxModuleJNP204 4  }
  jnxJNP204Power            OBJECT IDENTIFIER ::= { jnxModuleJNP204 5  }

--

--
-- MX2008 (Chotu)
--
  jnxProductLineMX2008      OBJECT IDENTIFIER ::= { jnxProductLine      145 }
  jnxProductNameMX2008      OBJECT IDENTIFIER ::= { jnxProductName      145 }
  jnxProductModelMX2008     OBJECT IDENTIFIER ::= { jnxProductModel     145 }
  jnxProductVariationMX2008 OBJECT IDENTIFIER ::= { jnxProductVariation 145 }
  jnxChassisMX2008          OBJECT IDENTIFIER ::= { jnxChassis          145 }
  jnxSlotMX2008             OBJECT IDENTIFIER ::= { jnxSlot             145 }
    jnxMX2008SlotSFB        OBJECT IDENTIFIER ::= { jnxSlotMX2008 1  }
                             -- Switch Fabric Board
    jnxMX2008SlotHM         OBJECT IDENTIFIER ::= { jnxSlotMX2008 2  }
                             -- Host Module (also called Routing Engine {RE})
    jnxMX2008SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX2008 3  }
                             -- Flexible Port Concentrator slot
    jnxMX2008SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX2008 4  }
    jnxMX2008SlotRCB        OBJECT IDENTIFIER ::= { jnxSlotMX2008 5  }
                             -- RE-CB board
    jnxMX2008SlotFPB        OBJECT IDENTIFIER ::= { jnxSlotMX2008 6  }
                             -- Front Panel Board
    jnxMX2008SlotPDM        OBJECT IDENTIFIER ::= { jnxSlotMX2008 7  }
                             -- Power Distribution Module
    jnxMX2008SlotPSM        OBJECT IDENTIFIER ::= { jnxSlotMX2008 8  }
                             -- Power Supply Module
    jnxMX2008SlotADC        OBJECT IDENTIFIER ::= { jnxSlotMX2008 9 }
                             -- Adapter Card (connects FPC to backplane)

  jnxMediaCardSpaceMX2008      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    145 }
    jnxMX2008MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX2008 1 }
    jnxMX2008MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX2008 2 }

  jnxMidplaneMX2008            OBJECT IDENTIFIER ::= { jnxBackplane        145 }
    jnxBackMidplaneMX2008      OBJECT IDENTIFIER ::= { jnxMidplaneMX2008   1 }
    jnxPowerMidplaneMX2008     OBJECT IDENTIFIER ::= { jnxMidplaneMX2008   2 }

  jnxModuleMX2008           OBJECT IDENTIFIER ::= { jnxModule    145 }
    jnxMX2008SFB            OBJECT IDENTIFIER ::= { jnxModuleMX2008 1  }
    jnxMX2008HM             OBJECT IDENTIFIER ::= { jnxModuleMX2008 2  }
    jnxMX2008FPC            OBJECT IDENTIFIER ::= { jnxModuleMX2008 3  }
    jnxMX2008Fan            OBJECT IDENTIFIER ::= { jnxModuleMX2008 4  }
    jnxMX2008RCB            OBJECT IDENTIFIER ::= { jnxModuleMX2008 5  }
    jnxMX2008FPB            OBJECT IDENTIFIER ::= { jnxModuleMX2008 6  }
    jnxMX2008PDM            OBJECT IDENTIFIER ::= { jnxModuleMX2008 7  }
    jnxMX2008PSM            OBJECT IDENTIFIER ::= { jnxModuleMX2008 8  }
    jnxMX2008ADC            OBJECT IDENTIFIER ::= { jnxModuleMX2008 9 }

--
-- MXTSR80
--
  jnxProductLineMXTSR80      OBJECT IDENTIFIER ::= { jnxProductLine      146 }
  jnxProductNameMXTSR80      OBJECT IDENTIFIER ::= { jnxProductName      146 }
  jnxProductModelMXTSR80     OBJECT IDENTIFIER ::= { jnxProductModel     146 }
  jnxProductVariationMXTSR80 OBJECT IDENTIFIER ::= { jnxProductVariation 146 }
  jnxProductMXTSR80          OBJECT IDENTIFIER ::= { jnxProductVariationMXTSR80 1 }
  jnxChassisMXTSR80          OBJECT IDENTIFIER ::= { jnxChassis          146 }

  jnxSlotMXTSR80             OBJECT IDENTIFIER ::= { jnxSlot     146 }
    jnxMXTSR80SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMXTSR80 1  }
    jnxMXTSR80SlotAFEB       OBJECT IDENTIFIER ::= { jnxSlotMXTSR80 2  }
    jnxMXTSR80SlotRE         OBJECT IDENTIFIER ::= { jnxSlotMXTSR80 3  }
    jnxMXTSR80SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMXTSR80 4  }
    jnxMXTSR80SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMXTSR80 5  }
    jnxMXTSR80SlotFPM        OBJECT IDENTIFIER ::= { jnxSlotMXTSR80 6  }

  jnxMediaCardSpaceMXTSR80      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    146 }
    jnxMXTSR80MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMXTSR80 1 }
    jnxMXTSR80MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMXTSR80 2 }

  jnxMidplaneMXTSR80         OBJECT IDENTIFIER ::= { jnxBackplane        146 }

  jnxModuleMXTSR80           OBJECT IDENTIFIER ::= { jnxModule     146 }
    jnxMXTSR80FPC            OBJECT IDENTIFIER ::= { jnxModuleMXTSR80 1  }
    jnxMXTSR80FEB            OBJECT IDENTIFIER ::= { jnxModuleMXTSR80 2  }
    jnxMXTSR80RE             OBJECT IDENTIFIER ::= { jnxModuleMXTSR80 3  }
    jnxMXTSR80Power          OBJECT IDENTIFIER ::= { jnxModuleMXTSR80 4  }
    jnxMXTSR80PowerAC        OBJECT IDENTIFIER ::= { jnxModuleMXTSR80 5  }
    jnxMXTSR80Fan            OBJECT IDENTIFIER ::= { jnxModuleMXTSR80 6  }
    jnxMXTSR80FPM            OBJECT IDENTIFIER ::= { jnxModuleMXTSR80 7  }

--
-- PTX10008
--
  jnxProductLinePTX10008       OBJECT IDENTIFIER ::= { jnxProductLine      147 }
  jnxProductNamePTX10008       OBJECT IDENTIFIER ::= { jnxProductName      147 }
  jnxProductModelPTX10008      OBJECT IDENTIFIER ::= { jnxProductModel     147 }
  jnxProductVariationPTX10008  OBJECT IDENTIFIER ::= { jnxProductVariation 147 }
  jnxChassisPTX10008           OBJECT IDENTIFIER ::= { jnxChassis          147 }
  jnxSlotPTX10008              OBJECT IDENTIFIER ::= { jnxSlot             147 }
    jnxPTX10008SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotPTX10008   1 }
    jnxPTX10008SlotHM          OBJECT IDENTIFIER ::= { jnxSlotPTX10008   2 }
    jnxPTX10008SlotPower       OBJECT IDENTIFIER ::= { jnxSlotPTX10008   3 }
    jnxPTX10008SlotFan         OBJECT IDENTIFIER ::= { jnxSlotPTX10008   4 }
    jnxPTX10008SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotPTX10008   5 }
    jnxPTX10008SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotPTX10008   6 }
    jnxPTX10008SlotSIB         OBJECT IDENTIFIER ::= { jnxSlotPTX10008   7 }
    jnxPTX10008SlotFPM         OBJECT IDENTIFIER ::= { jnxSlotPTX10008   8 }
    jnxPTX10008SlotFTC         OBJECT IDENTIFIER ::= { jnxSlotPTX10008   9 }
    jnxPTX10008SlotBackplane   OBJECT IDENTIFIER ::= { jnxSlotPTX10008   10 }
  jnxMediaCardSpacePTX10008    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   147 }
    jnxPTX10008MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpacePTX10008 1 }

  jnxModulePTX10008              OBJECT IDENTIFIER ::= { jnxModule             147 }
    jnxPTX10008FPC         OBJECT IDENTIFIER ::= { jnxModulePTX10008   1 }
    jnxPTX10008HM          OBJECT IDENTIFIER ::= { jnxModulePTX10008   2 }
    jnxPTX10008Power       OBJECT IDENTIFIER ::= { jnxModulePTX10008   3 }
    jnxPTX10008Fan         OBJECT IDENTIFIER ::= { jnxModulePTX10008   4 }
    jnxPTX10008FPB         OBJECT IDENTIFIER ::= { jnxModulePTX10008   5 }
    jnxPTX10008CBD         OBJECT IDENTIFIER ::= { jnxModulePTX10008   6 }
    jnxPTX10008SIB         OBJECT IDENTIFIER ::= { jnxModulePTX10008   7 }
    jnxPTX10008FPM         OBJECT IDENTIFIER ::= { jnxModulePTX10008   8 }
    jnxPTX10008FTC         OBJECT IDENTIFIER ::= { jnxModulePTX10008   9 }
    jnxPTX10008Backplane   OBJECT IDENTIFIER ::= { jnxModulePTX10008   10 }

--
-- ACX5448
--
  jnxProductLineACX5448      OBJECT IDENTIFIER ::= { jnxProductLine      148 }
  jnxProductNameACX5448      OBJECT IDENTIFIER ::= { jnxProductName      148 }
  jnxProductModelACX5448     OBJECT IDENTIFIER ::= { jnxProductModel     148 }
  jnxProductVariationACX5448 OBJECT IDENTIFIER ::= { jnxProductVariation 148 }
    jnxProductACX5448        OBJECT IDENTIFIER ::= { jnxProductVariationACX5448 1 }
  jnxChassisACX5448          OBJECT IDENTIFIER ::= { jnxChassis          148 }

  jnxSlotACX5448             OBJECT IDENTIFIER ::= { jnxSlot        148 }
    jnxACX5448SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX5448 1  }
    jnxACX5448SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX5448 2  }
    jnxACX5448SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX5448 3  }
    jnxACX5448SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX5448 4  }
    jnxACX5448SlotFan        OBJECT IDENTIFIER ::= { jnxSlotACX5448 5  }

  jnxMediaCardSpaceACX5448      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    148 }
    jnxACX5448MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5448 1 }
    jnxACX5448MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5448 2 }

  jnxMidplaneACX5448         OBJECT IDENTIFIER ::= { jnxBackplane        148 }

  jnxModuleACX5448           OBJECT IDENTIFIER ::= { jnxModule     148 }
    jnxACX5448FPC            OBJECT IDENTIFIER ::= { jnxModuleACX5448 1  }
    jnxACX5448FEB            OBJECT IDENTIFIER ::= { jnxModuleACX5448 2  }
    jnxACX5448RE             OBJECT IDENTIFIER ::= { jnxModuleACX5448 3  }
    jnxACX5448Power          OBJECT IDENTIFIER ::= { jnxModuleACX5448 4  }
        jnxACX5448PowerDC    OBJECT IDENTIFIER ::= { jnxACX5448Power 1  }
        jnxACX5448PowerAC    OBJECT IDENTIFIER ::= { jnxACX5448Power 2  }
    jnxACX5448Fan            OBJECT IDENTIFIER ::= { jnxModuleACX5448 5 }

--

--
-- PTX10016
--
  jnxProductLinePTX10016       OBJECT IDENTIFIER ::= { jnxProductLine      150 }
  jnxProductNamePTX10016       OBJECT IDENTIFIER ::= { jnxProductName      150 }
  jnxProductModelPTX10016      OBJECT IDENTIFIER ::= { jnxProductModel     150 }
  jnxProductVariationPTX10016  OBJECT IDENTIFIER ::= { jnxProductVariation 150 }
  jnxChassisPTX10016           OBJECT IDENTIFIER ::= { jnxChassis          150 }
  jnxSlotPTX10016              OBJECT IDENTIFIER ::= { jnxSlot             150 }
    jnxPTX10016SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotPTX10016   1 }
    jnxPTX10016SlotHM          OBJECT IDENTIFIER ::= { jnxSlotPTX10016   2 }
    jnxPTX10016SlotPower       OBJECT IDENTIFIER ::= { jnxSlotPTX10016   3 }
    jnxPTX10016SlotFan         OBJECT IDENTIFIER ::= { jnxSlotPTX10016   4 }
    jnxPTX10016SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotPTX10016   5 }
    jnxPTX10016SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotPTX10016   6 }
    jnxPTX10016SlotSIB         OBJECT IDENTIFIER ::= { jnxSlotPTX10016   7 }
    jnxPTX10016SlotFTC         OBJECT IDENTIFIER ::= { jnxSlotPTX10016   8 }
    jnxPTX10016SlotBackplane   OBJECT IDENTIFIER ::= { jnxSlotPTX10016   9 }
  jnxMediaCardSpacePTX10016    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   150 }
    jnxPTX10016MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpacePTX10016 1 }

  jnxModulePTX10016         OBJECT IDENTIFIER ::= { jnxModule           150 }
    jnxPTX10016FPC          OBJECT IDENTIFIER ::= { jnxModulePTX10016   1 }
    jnxPTX10016HM           OBJECT IDENTIFIER ::= { jnxModulePTX10016   2 }
    jnxPTX10016Power        OBJECT IDENTIFIER ::= { jnxModulePTX10016   3 }
    jnxPTX10016Fan          OBJECT IDENTIFIER ::= { jnxModulePTX10016   4 }
    jnxPTX10016FPB          OBJECT IDENTIFIER ::= { jnxModulePTX10016   5 }
    jnxPTX10016CBD          OBJECT IDENTIFIER ::= { jnxModulePTX10016   6 }
    jnxPTX10016SIB          OBJECT IDENTIFIER ::= { jnxModulePTX10016   7 }
    jnxPTX10016FTC          OBJECT IDENTIFIER ::= { jnxModulePTX10016   8 }
    jnxPTX10016Backplane    OBJECT IDENTIFIER ::= { jnxModulePTX10016   9 }

--
-- EX9251
--
  jnxProductLineEX9251      OBJECT IDENTIFIER ::= { jnxProductLine      151 }
  jnxProductNameEX9251      OBJECT IDENTIFIER ::= { jnxProductName      151 }
  jnxProductModelEX9251     OBJECT IDENTIFIER ::= { jnxProductModel     151 }
  jnxProductVariationEX9251 OBJECT IDENTIFIER ::= { jnxProductVariation 151 }
  jnxChassisEX9251          OBJECT IDENTIFIER ::= { jnxChassis          151 }
  jnxSlotEX9251             OBJECT IDENTIFIER ::= { jnxSlot             151 }
  jnxEX9251SlotHM           OBJECT IDENTIFIER ::= { jnxSlotEX9251 1  }
  jnxEX9251SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotEX9251 2  }
  jnxEX9251SlotFan          OBJECT IDENTIFIER ::= { jnxSlotEX9251 3  }
  jnxEX9251SlotCB           OBJECT IDENTIFIER ::= { jnxSlotEX9251 4  }
  jnxEX9251SlotPower        OBJECT IDENTIFIER ::= { jnxSlotEX9251 5  }

  jnxMediaCardSpaceEX9251   OBJECT IDENTIFIER ::= { jnxMediaCardSpace     151 }
  jnxPicEX92514xQSFP28      OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9251 1 }
  jnxPicEX92518XSFPP        OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9251 2 }

  jnxModuleEX9251           OBJECT IDENTIFIER ::= { jnxModule    151 }
  jnxEX9251HM               OBJECT IDENTIFIER ::= { jnxModuleEX9251 1  }
  jnxEX9251FPC              OBJECT IDENTIFIER ::= { jnxModuleEX9251 2  }
  jnxEX9251Fan              OBJECT IDENTIFIER ::= { jnxModuleEX9251 3  }
  jnxEX9251CB               OBJECT IDENTIFIER ::= { jnxModuleEX9251 4  }
  jnxEX9251Power            OBJECT IDENTIFIER ::= { jnxModuleEX9251 5  }

--

--
-- MX150
--

  jnxProductLineMX150      OBJECT IDENTIFIER ::= { jnxProductLine      152 }
  jnxProductNameMX150     OBJECT IDENTIFIER ::= { jnxProductName      152 }
  jnxProductModelMX150     OBJECT IDENTIFIER ::= { jnxProductModel   152 }
  jnxChassisMX150          OBJECT IDENTIFIER ::= { jnxChassis          152 }

  jnxSlotMX150             OBJECT IDENTIFIER ::= { jnxSlot    152 }
    jnxMX150SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotMX150  1  }
    jnxMX150SlotPower       OBJECT IDENTIFIER ::= { jnxSlotMX150  2  }
    jnxMX150SlotFan         OBJECT IDENTIFIER ::= { jnxSlotMX150  3  }
    jnxMX150SlotCB          OBJECT IDENTIFIER ::= { jnxSlotMX150  4  }
    jnxMX150SlotHM          OBJECT IDENTIFIER ::= { jnxSlotMX150  5  }

  jnxMediaCardSpaceMX150      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    152 }
    jnxMX150MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX150 1 }
    jnxMX150MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX150 2 }

  jnxMidplaneMX150         OBJECT IDENTIFIER ::= { jnxBackplane 152 }

--
-- JNP10001 PTX/QFX related definitions
--
  jnxProductLineJNP10001      OBJECT IDENTIFIER ::= { jnxProductLine      153 }
  jnxProductNameJNP10001      OBJECT IDENTIFIER ::= { jnxProductName      153 }
  jnxProductModelJNP10001     OBJECT IDENTIFIER ::= { jnxProductModel     153 }
  jnxProductVariationJNP10001 OBJECT IDENTIFIER ::= { jnxProductVariation 153 }
  jnxChassisJNP10001          OBJECT IDENTIFIER ::= { jnxChassis          153 }
  jnxSlotJNP10001             OBJECT IDENTIFIER ::= { jnxSlot             153 }
    jnxJNP10001SlotHM         OBJECT IDENTIFIER ::= { jnxSlotJNP10001 1  }
    jnxJNP10001SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotJNP10001 2  }
    jnxJNP10001SlotFan        OBJECT IDENTIFIER ::= { jnxSlotJNP10001 3  }
    jnxJNP10001SlotPower        OBJECT IDENTIFIER ::= { jnxSlotJNP10001 4  }

  jnxMediaCardSpaceJNP10001     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    153 }
    jnxJNP10001MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJNP10001 1 }
    jnxPicJNP1000116xQSFP28MacsecTIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceJNP10001 2 }

  jnxModuleJNP10001           OBJECT IDENTIFIER ::= { jnxModule    153 }
    jnxJNP10001HM             OBJECT IDENTIFIER ::= { jnxModuleJNP10001 1  }
    jnxJNP10001FPC            OBJECT IDENTIFIER ::= { jnxModuleJNP10001 2  }
    jnxJNP10001Fan            OBJECT IDENTIFIER ::= { jnxModuleJNP10001 3  }
    jnxJNP10001Power            OBJECT IDENTIFIER ::= { jnxModuleJNP10001 4  }


--

--
-- MX10008
--
  jnxProductLineMX10008       OBJECT IDENTIFIER ::= { jnxProductLine      154 }
  jnxProductNameMX10008       OBJECT IDENTIFIER ::= { jnxProductName      154 }
  jnxProductModelMX10008      OBJECT IDENTIFIER ::= { jnxProductModel     154 }
  jnxProductVariationMX10008  OBJECT IDENTIFIER ::= { jnxProductVariation 154 }
  jnxChassisMX10008           OBJECT IDENTIFIER ::= { jnxChassis          154 }
  jnxSlotMX10008              OBJECT IDENTIFIER ::= { jnxSlot             154 }
    jnxMX10008SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotMX10008   1 }
    jnxMX10008SlotHM          OBJECT IDENTIFIER ::= { jnxSlotMX10008   2 }
    jnxMX10008SlotPower       OBJECT IDENTIFIER ::= { jnxSlotMX10008   3 }
    jnxMX10008SlotFan         OBJECT IDENTIFIER ::= { jnxSlotMX10008   4 }
    jnxMX10008SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotMX10008   5 }
    jnxMX10008SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotMX10008   6 }
    jnxMX10008SlotSFB         OBJECT IDENTIFIER ::= { jnxSlotMX10008   7 }
    jnxMX10008SlotFTC         OBJECT IDENTIFIER ::= { jnxSlotMX10008   8 }

  jnxMediaCardSpaceMX10008    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   154 }
    jnxMX10008MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX10008 1 }
  jnxModuleMX10008           OBJECT IDENTIFIER ::= { jnxModule    154 }
    jnxMX10008SFB            OBJECT IDENTIFIER ::= { jnxModuleMX10008 1  }
    jnxMX10008HM             OBJECT IDENTIFIER ::= { jnxModuleMX10008 2  }
    jnxMX10008FPC            OBJECT IDENTIFIER ::= { jnxModuleMX10008 3  }
    jnxMX10008Fan            OBJECT IDENTIFIER ::= { jnxModuleMX10008 4  }
    jnxMX10008CBD            OBJECT IDENTIFIER ::= { jnxModuleMX10008 5  }
    jnxMX10008Power          OBJECT IDENTIFIER ::= { jnxModuleMX10008 6  }
    jnxMX10008FPB            OBJECT IDENTIFIER ::= { jnxModuleMX10008 7  }
    jnxMX10008FTC            OBJECT IDENTIFIER ::= { jnxModuleMX10008 8  }
--

--
-- MX10016
--
  jnxProductLineMX10016       OBJECT IDENTIFIER ::= { jnxProductLine      155 }
  jnxProductNameMX10016       OBJECT IDENTIFIER ::= { jnxProductName      155 }
  jnxProductModelMX10016      OBJECT IDENTIFIER ::= { jnxProductModel     155 }
  jnxProductVariationMX10016  OBJECT IDENTIFIER ::= { jnxProductVariation 155 }
  jnxChassisMX10016           OBJECT IDENTIFIER ::= { jnxChassis          155 }
  jnxSlotMX10016              OBJECT IDENTIFIER ::= { jnxSlot             155 }
    jnxMX10016SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotMX10016   1 }
    jnxMX10016SlotHM          OBJECT IDENTIFIER ::= { jnxSlotMX10016   2 }
    jnxMX10016SlotPower       OBJECT IDENTIFIER ::= { jnxSlotMX10016   3 }
    jnxMX10016SlotFan         OBJECT IDENTIFIER ::= { jnxSlotMX10016   4 }
    jnxMX10016SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotMX10016   5 }
    jnxMX10016SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotMX10016   6 }
    jnxMX10016SlotSFB         OBJECT IDENTIFIER ::= { jnxSlotMX10016   7 }
    jnxMX10016SlotFTC         OBJECT IDENTIFIER ::= { jnxSlotMX10016   8 }
  jnxMediaCardSpaceMX10016    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   155 }
    jnxMX10016MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX10016 1 }
  jnxModuleMX10016           OBJECT IDENTIFIER ::= { jnxModule    155 }
    jnxMX10016SFB            OBJECT IDENTIFIER ::= { jnxModuleMX10016 1  }
    jnxMX10016HM             OBJECT IDENTIFIER ::= { jnxModuleMX10016 2  }
    jnxMX10016FPC            OBJECT IDENTIFIER ::= { jnxModuleMX10016 3  }
    jnxMX10016Fan            OBJECT IDENTIFIER ::= { jnxModuleMX10016 4  }
    jnxMX10016CBD            OBJECT IDENTIFIER ::= { jnxModuleMX10016 5  }
    jnxMX10016Power          OBJECT IDENTIFIER ::= { jnxModuleMX10016 6  }
    jnxMX10016FPB            OBJECT IDENTIFIER ::= { jnxModuleMX10016 7  }
--

--
-- EX9253
--
  jnxProductLineEX9253      OBJECT IDENTIFIER ::= { jnxProductLine      156 }
  jnxProductNameEX9253      OBJECT IDENTIFIER ::= { jnxProductName      156 }
  jnxProductModelEX9253     OBJECT IDENTIFIER ::= { jnxProductModel     156 }
  jnxProductVariationEX9253 OBJECT IDENTIFIER ::= { jnxProductVariation 156 }
  jnxChassisEX9253          OBJECT IDENTIFIER ::= { jnxChassis          156 }
  jnxSlotEX9253             OBJECT IDENTIFIER ::= { jnxSlot             156 }
    jnxEX9253SlotHM         OBJECT IDENTIFIER ::= { jnxSlotEX9253 1  }
    jnxEX9253SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX9253 2  }
    jnxEX9253SlotFan        OBJECT IDENTIFIER ::= { jnxSlotEX9253 3  }
    jnxEX9253SlotCB       OBJECT IDENTIFIER ::= { jnxSlotEX9253 4  }
    jnxEX9253SlotPower        OBJECT IDENTIFIER ::= { jnxSlotEX9253 5  }

  jnxMediaCardSpaceEX9253     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    156 }
    jnxEX9253MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9253 1 }
    jnxPicEX925312xQSFP28MacsecTIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9253 2 }
    jnxEX9253MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX9253 3 }

  jnxModuleEX9253           OBJECT IDENTIFIER ::= { jnxModule    156 }
    jnxEX9253HM             OBJECT IDENTIFIER ::= { jnxModuleEX9253 1  }
    jnxEX9253FPC            OBJECT IDENTIFIER ::= { jnxModuleEX9253 2  }
    jnxEX9253Fan            OBJECT IDENTIFIER ::= { jnxModuleEX9253 3  }
    jnxEX9253CB           OBJECT IDENTIFIER ::= { jnxModuleEX9253 4  }
    jnxEX9253Power            OBJECT IDENTIFIER ::= { jnxModuleEX9253 5  }

--
 -- JRR200
 --
    jnxProductLineJRR200   OBJECT IDENTIFIER ::= { jnxProductLine  157 }
    jnxProductNameJRR200   OBJECT IDENTIFIER ::= { jnxProductName  157 }
    jnxChassisJRR200       OBJECT IDENTIFIER ::= { jnxChassis      157 }
    jnxSlotJRR200          OBJECT IDENTIFIER ::= { jnxSlot         157 }
      jnxJRR200SlotRE        OBJECT IDENTIFIER ::= { jnxSlotJRR200   1 }
      jnxJRR200SlotPower     OBJECT IDENTIFIER ::= { jnxSlotJRR200   2 }
      jnxJRR200SlotFan       OBJECT IDENTIFIER ::= { jnxSlotJRR200   3 }
    jnxMidplaneJRR200      OBJECT IDENTIFIER ::= { jnxBackplane    157 }
    jnxModuleJRR200        OBJECT IDENTIFIER ::= { jnxModule       157 }
      jnxJRR200RE          OBJECT IDENTIFIER ::= { jnxModuleJRR200   1 }
      jnxJRR200Power       OBJECT IDENTIFIER ::= { jnxModuleJRR200   2 }
      jnxJRR200Fan         OBJECT IDENTIFIER ::= { jnxModuleJRR200   3 }


--
--
-- ACX5448-M
--
  jnxProductLineACX5448M     OBJECT IDENTIFIER ::= { jnxProductLine      158 }
  jnxProductNameACX5448M      OBJECT IDENTIFIER ::= { jnxProductName      158 }
  jnxProductModelACX5448M     OBJECT IDENTIFIER ::= { jnxProductModel     158 }
  jnxProductVariationACX5448M OBJECT IDENTIFIER ::= { jnxProductVariation 158 }
    jnxProductACX5448M        OBJECT IDENTIFIER ::= { jnxProductVariationACX5448M 1 }
  jnxChassisACX5448M          OBJECT IDENTIFIER ::= { jnxChassis          158 }

  jnxSlotACX5448M            OBJECT IDENTIFIER ::= { jnxSlot        158 }
    jnxACX5448MSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX5448M 1  }
    jnxACX5448MSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX5448M 2  }
    jnxACX5448MSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX5448M 3  }
    jnxACX5448MSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX5448M 4  }
    jnxACX5448MSlotFan        OBJECT IDENTIFIER ::=  { jnxSlotACX5448M 5  }

  jnxMediaCardSpaceACX5448M      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    158 }
    jnxACX5448MMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5448M 1 }
    jnxACX5448MMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5448M 2 }

  jnxMidplaneACX5448M         OBJECT IDENTIFIER ::= { jnxBackplane        158 }

  jnxModuleACX5448M           OBJECT IDENTIFIER ::= { jnxModule     158 }
    jnxACX5448MFPC            OBJECT IDENTIFIER ::= { jnxModuleACX5448M 1  }
    jnxACX5448MFEB            OBJECT IDENTIFIER ::= { jnxModuleACX5448M 2  }
    jnxACX5448MRE             OBJECT IDENTIFIER ::= { jnxModuleACX5448M 3  }
    jnxACX5448MPower          OBJECT IDENTIFIER ::= { jnxModuleACX5448M 4  }
        jnxACX5448MPowerDC    OBJECT IDENTIFIER ::= { jnxACX5448MPower 1  }
        jnxACX5448MPowerAC    OBJECT IDENTIFIER ::= { jnxACX5448MPower 2  }
    jnxACX5448MFan            OBJECT IDENTIFIER ::= { jnxModuleACX5448M  5 }
--
-- ACX5448-D
--

  jnxProductLineACX5448D      OBJECT IDENTIFIER ::= { jnxProductLine      159 }
  jnxProductNameACX5448D      OBJECT IDENTIFIER ::= { jnxProductName      159 }
  jnxProductModelACX5448D     OBJECT IDENTIFIER ::= { jnxProductModel     159 }
  jnxProductVariationACX5448D OBJECT IDENTIFIER ::= { jnxProductVariation 159 }
    jnxProductACX5448D        OBJECT IDENTIFIER ::= { jnxProductVariationACX5448D 1 }
  jnxChassisACX5448D          OBJECT IDENTIFIER ::= { jnxChassis          159 }

  jnxSlotACX5448D             OBJECT IDENTIFIER ::= { jnxSlot        159 }
    jnxACX5448DSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX5448D 1  }
    jnxACX5448DSlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX5448D 2  }
    jnxACX5448DSlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX5448D 3  }
    jnxACX5448DSlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX5448D 4  }
    jnxACX5448DSlotFan        OBJECT IDENTIFIER ::= { jnxSlotACX5448D 5  }

  jnxMediaCardSpaceACX5448D      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    159 }
    jnxACX5448DMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5448D 1 }
    jnxACX5448DMediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5448D 2 }

  jnxMidplaneACX5448D         OBJECT IDENTIFIER ::= { jnxBackplane        159 }

  jnxModuleACX5448D           OBJECT IDENTIFIER ::= { jnxModule     159 }
    jnxACX5448DFPC            OBJECT IDENTIFIER ::= { jnxModuleACX5448D 1  }
    jnxACX5448DFEB            OBJECT IDENTIFIER ::= { jnxModuleACX5448D 2  }
    jnxACX5448DRE             OBJECT IDENTIFIER ::= { jnxModuleACX5448D 3  }
    jnxACX5448DPower          OBJECT IDENTIFIER ::= { jnxModuleACX5448D 4  }
        jnxACX5448DPowerDC    OBJECT IDENTIFIER ::= { jnxACX5448DPower 1  }
        jnxACX5448DPowerAC    OBJECT IDENTIFIER ::= { jnxACX5448DPower 2  }
    jnxACX5448DFan            OBJECT IDENTIFIER ::= { jnxModuleACX5448D  5 }

--

--
-- ACX6360OR related definitions
--
  jnxProductLineACX6360OR      OBJECT IDENTIFIER ::= { jnxProductLine      160 }
  jnxProductNameACX6360OR      OBJECT IDENTIFIER ::= { jnxProductName      160 }
  jnxProductModelACX6360OR     OBJECT IDENTIFIER ::= { jnxProductModel     160 }
  jnxProductVariationACX6360OR OBJECT IDENTIFIER ::= { jnxProductVariation 160 }
  jnxChassisACX6360OR          OBJECT IDENTIFIER ::= { jnxChassis          160 }
  jnxSlotACX6360OR             OBJECT IDENTIFIER ::= { jnxSlot             160 }
    jnxACX6360ORSlotHM         OBJECT IDENTIFIER ::= { jnxSlotACX6360OR 1  }
    jnxACX6360ORSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX6360OR 2  }
    jnxACX6360ORSlotFan        OBJECT IDENTIFIER ::= { jnxSlotACX6360OR 3  }
    jnxACX6360ORSlotPower        OBJECT IDENTIFIER ::= { jnxSlotACX6360OR 4  }

  jnxMediaCardSpaceACX6360OR     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    160 }
    jnxACX6360ORMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX6360OR 1 }
    jnxPicACX6360OR20xQSFP28TIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX6360OR 2 }
    jnxPicACX6360OR8xCFP2DCOTIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX6360OR 3 }

  jnxModuleACX6360OR           OBJECT IDENTIFIER ::= { jnxModule    160 }
    jnxACX6360ORHM             OBJECT IDENTIFIER ::= { jnxModuleACX6360OR 1  }
    jnxACX6360ORFPC            OBJECT IDENTIFIER ::= { jnxModuleACX6360OR 2  }
    jnxACX6360ORFan            OBJECT IDENTIFIER ::= { jnxModuleACX6360OR 3  }
    jnxACX6360ORPower            OBJECT IDENTIFIER ::= { jnxModuleACX6360OR 4  }
--

--
-- ACX6360OX related definitions
--
  jnxProductLineACX6360OX      OBJECT IDENTIFIER ::= { jnxProductLine      161 }
  jnxProductNameACX6360OX      OBJECT IDENTIFIER ::= { jnxProductName      161 }
  jnxProductModelACX6360OX     OBJECT IDENTIFIER ::= { jnxProductModel     161 }
  jnxProductVariationACX6360OX OBJECT IDENTIFIER ::= { jnxProductVariation 161 }
  jnxChassisACX6360OX          OBJECT IDENTIFIER ::= { jnxChassis          161 }
  jnxSlotACX6360OX             OBJECT IDENTIFIER ::= { jnxSlot             161 }
    jnxACX6360OXSlotHM         OBJECT IDENTIFIER ::= { jnxSlotACX6360OX 1  }
    jnxACX6360OXSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX6360OX 2  }
    jnxACX6360OXSlotFan        OBJECT IDENTIFIER ::= { jnxSlotACX6360OX 3  }
    jnxACX6360OXSlotPower        OBJECT IDENTIFIER ::= { jnxSlotACX6360OX 4  }

  jnxMediaCardSpaceACX6360OX     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    161 }
    jnxACX6360OXMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX6360OX 1 }
    jnxPicACX6360OX20xQSFP28TIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX6360OX 2 }
    jnxPicACX6360OX8xCFP2DCOTIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX6360OX 3 }

  jnxModuleACX6360OX           OBJECT IDENTIFIER ::= { jnxModule    161 }
    jnxACX6360OXHM             OBJECT IDENTIFIER ::= { jnxModuleACX6360OX 1  }
    jnxACX6360OXFPC            OBJECT IDENTIFIER ::= { jnxModuleACX6360OX 2  }
    jnxACX6360OXFan            OBJECT IDENTIFIER ::= { jnxModuleACX6360OX 3  }
    jnxACX6360OXPower            OBJECT IDENTIFIER ::= { jnxModuleACX6360OX 4  }
--
--

--
-- ACX710
--
  jnxProductLineACX710      OBJECT IDENTIFIER ::= { jnxProductLine      162 }
  jnxProductNameACX710      OBJECT IDENTIFIER ::= { jnxProductName      162 }
  jnxProductModelACX710     OBJECT IDENTIFIER ::= { jnxProductModel     162 }
  jnxProductVariationACX710 OBJECT IDENTIFIER ::= { jnxProductVariation 162 }
    jnxProductACX710        OBJECT IDENTIFIER ::= { jnxProductVariationACX710 1 }
  jnxChassisACX710          OBJECT IDENTIFIER ::= { jnxChassis          162 }

  jnxSlotACX710             OBJECT IDENTIFIER ::= { jnxSlot        162 }
    jnxACX710SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX710 1  }
    jnxACX710SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX710 2  }
    jnxACX710SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX710 3  }
    jnxACX710SlotFan        OBJECT IDENTIFIER ::= { jnxSlotACX710 4  }

  jnxMediaCardSpaceACX710      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    162 }
    jnxACX710MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX710 1 }
    jnxACX710MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX710 2 }

  jnxMidplaneACX710         OBJECT IDENTIFIER ::= { jnxBackplane        162 }

  jnxModuleACX710           OBJECT IDENTIFIER ::= { jnxModule     162 }
    jnxACX710FPC            OBJECT IDENTIFIER ::= { jnxModuleACX710 1  }
    jnxACX710RE             OBJECT IDENTIFIER ::= { jnxModuleACX710 2  }
    jnxACX710Power          OBJECT IDENTIFIER ::= { jnxModuleACX710 3  }
        jnxACX710PowerDC    OBJECT IDENTIFIER ::= { jnxACX710Power 1  }
        jnxACX710PowerAC    OBJECT IDENTIFIER ::= { jnxACX710Power 2  }
    jnxACX710Fan            OBJECT IDENTIFIER ::= { jnxModuleACX710  4 }

--
-- ACX5800
--

  jnxProductLineACX5800      OBJECT IDENTIFIER ::= { jnxProductLine      163 }
  jnxProductNameACX5800      OBJECT IDENTIFIER ::= { jnxProductName      163 }
  jnxProductModelACX5800     OBJECT IDENTIFIER ::= { jnxProductModel     163 }
  jnxProductVariationACX5800 OBJECT IDENTIFIER ::= { jnxProductVariation 163 }
    jnxProductACX5800        OBJECT IDENTIFIER ::= { jnxProductVariationACX5800 1 }
  jnxChassisACX5800          OBJECT IDENTIFIER ::= { jnxChassis          163 }

  jnxSlotACX5800             OBJECT IDENTIFIER ::= { jnxSlot        163 }
    jnxACX5800SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotACX5800 1  }
    jnxACX5800SlotFEB        OBJECT IDENTIFIER ::= { jnxSlotACX5800 2  }
    jnxACX5800SlotRE         OBJECT IDENTIFIER ::= { jnxSlotACX5800 3  }
    jnxACX5800SlotPower      OBJECT IDENTIFIER ::= { jnxSlotACX5800 4  }

  jnxMediaCardSpaceACX5800      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    163 }
    jnxACX5800MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5800 1 }
    jnxACX5800MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX5800 2 }

  jnxMidplaneACX5800         OBJECT IDENTIFIER ::= { jnxBackplane        163 }

  jnxModuleACX5800           OBJECT IDENTIFIER ::= { jnxModule     163 }
    jnxACX5800FPC            OBJECT IDENTIFIER ::= { jnxModuleACX5800 1  }
    jnxACX5800FEB            OBJECT IDENTIFIER ::= { jnxModuleACX5800 2  }
    jnxACX5800RE             OBJECT IDENTIFIER ::= { jnxModuleACX5800 3  }
    jnxACX5800Power          OBJECT IDENTIFIER ::= { jnxModuleACX5800 4  }
        jnxACX5800PowerDC    OBJECT IDENTIFIER ::= { jnxACX5800Power 1  }
        jnxACX5800PowerAC    OBJECT IDENTIFIER ::= { jnxACX5800Power 2  }

--

--
-- SRX380 (SRX380)
--
    jnxProductLineSRX380        OBJECT IDENTIFIER ::= { jnxProductLine 164 }
    jnxProductNameSRX380        OBJECT IDENTIFIER ::= { jnxProductName 164 }
    jnxChassisSRX380            OBJECT IDENTIFIER ::= { jnxChassis     164 }

    jnxSlotSRX380               OBJECT IDENTIFIER ::= { jnxSlot 164 }
    jnxSRX380SlotFPC            OBJECT IDENTIFIER ::= { jnxSlotSRX380  1  }
    jnxSRX380SlotRE             OBJECT IDENTIFIER ::= { jnxSlotSRX380  2  }
    jnxSRX380SlotPower          OBJECT IDENTIFIER ::= { jnxSlotSRX380  3  }
    jnxSRX380SlotFan            OBJECT IDENTIFIER ::= { jnxSlotSRX380  4  }

    jnxMediaCardSpaceSRX380     OBJECT IDENTIFIER ::= { jnxMediaCardSpace 164 }
    jnxSRX380MediaCardSpacePIC  OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX380  1 }

    jnxMidplaneSRX380           OBJECT IDENTIFIER ::= { jnxBackplane 164 }

    jnxModuleSRX380             OBJECT IDENTIFIER ::= { jnxModule 164 }
    jnxSRX380FPC                OBJECT IDENTIFIER ::= { jnxModuleSRX380 1  }
    jnxSRX380RE                 OBJECT IDENTIFIER ::= { jnxModuleSRX380 2  }
    jnxSRX380Power              OBJECT IDENTIFIER ::= { jnxModuleSRX380 3  }
    jnxSRX380Fan                OBJECT IDENTIFIER ::= { jnxModuleSRX380 4  }

--

--
-- R6675
--
  jnxProductLineR6675        OBJECT IDENTIFIER ::= { jnxProductLine      166 }
  jnxProductNameR6675        OBJECT IDENTIFIER ::= { jnxProductName      166 }
  jnxProductModelR6675       OBJECT IDENTIFIER ::= { jnxProductModel     166 }
  jnxProductVariationR6675   OBJECT IDENTIFIER ::= { jnxProductVariation 166 }
    jnxProductR6675          OBJECT IDENTIFIER ::= { jnxProductVariationR6675 1 }
  jnxChassisR6675            OBJECT IDENTIFIER ::= { jnxChassis          166 }

  jnxSlotR6675              OBJECT IDENTIFIER ::= { jnxSlot        166 }
    jnxR6675SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotR6675   1  }
    jnxR6675SlotRE          OBJECT IDENTIFIER ::= { jnxSlotR6675   2  }
    jnxR6675SlotPower       OBJECT IDENTIFIER ::= { jnxSlotR6675   3  }
    jnxR6675SlotFan         OBJECT IDENTIFIER ::= { jnxSlotR6675   4  }

  jnxMediaCardSpaceR6675      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    166 }
    jnxR6675MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceR6675  1 }
    jnxR6675MediaCardSpaceMIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceR6675  2 }

  jnxMidplaneR6675          OBJECT IDENTIFIER ::= { jnxBackplane        166 }

  jnxModuleR6675           OBJECT IDENTIFIER ::= { jnxModule     166 }
    jnxR6675FPC            OBJECT IDENTIFIER ::= { jnxModuleR6675   1 }
    jnxR6675RE             OBJECT IDENTIFIER ::= { jnxModuleR6675   2 }
    jnxR6675Power          OBJECT IDENTIFIER ::= { jnxModuleR6675   3 }
        jnxR6675PowerDC    OBJECT IDENTIFIER ::= { jnxR6675Power  1  }
        jnxR6675PowerAC    OBJECT IDENTIFIER ::= { jnxR6675Power  2  }
    jnxR6675Fan            OBJECT IDENTIFIER ::= { jnxModuleR6675   4 }


--
-- MX304
--
  jnxProductLineMX304      OBJECT IDENTIFIER ::= { jnxProductLine      167 }
  jnxProductNameMX304      OBJECT IDENTIFIER ::= { jnxProductName      167 }
  jnxProductModelMX304     OBJECT IDENTIFIER ::= { jnxProductModel     167 }
  jnxProductVariationMX304 OBJECT IDENTIFIER ::= { jnxProductVariation 167 }
  jnxChassisMX304          OBJECT IDENTIFIER ::= { jnxChassis          167 }
  jnxSlotMX304             OBJECT IDENTIFIER ::= { jnxSlot             167 }
    jnxMX304SlotHM         OBJECT IDENTIFIER ::= { jnxSlotMX304 1  }
    jnxMX304SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotMX304 2  }
    jnxMX304SlotFan        OBJECT IDENTIFIER ::= { jnxSlotMX304 3  }
    jnxMX304SlotPower      OBJECT IDENTIFIER ::= { jnxSlotMX304 4  }
    jnxMX304SlotPMB        OBJECT IDENTIFIER ::= { jnxSlotMX304 5  }
    jnxMX304SlotSFB        OBJECT IDENTIFIER ::= { jnxSlotMX304 6  }
    jnxMX304SlotTIB        OBJECT IDENTIFIER ::= { jnxSlotMX304 7  }
    jnxMX304SlotCBD        OBJECT IDENTIFIER ::= { jnxSlotMX304 8  }

  jnxMediaCardSpaceMX304     OBJECT IDENTIFIER ::= { jnxMediaCardSpace    167 }
    jnxMX304MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX304 1 }

  jnxModuleMX304           OBJECT IDENTIFIER ::= { jnxModule    167 }
    jnxMX304HM             OBJECT IDENTIFIER ::= { jnxModuleMX304 1  }
    jnxMX304FPC            OBJECT IDENTIFIER ::= { jnxModuleMX304 2  }
    jnxMX304Fan            OBJECT IDENTIFIER ::= { jnxModuleMX304 3  }
    jnxMX304Power          OBJECT IDENTIFIER ::= { jnxModuleMX304 4  }
    jnxMX304PMB            OBJECT IDENTIFIER ::= { jnxModuleMX304 5  }
    jnxMX304SFB            OBJECT IDENTIFIER ::= { jnxModuleMX304 6  }
    jnxMX304TIB            OBJECT IDENTIFIER ::= { jnxModuleMX304 7  }
    jnxMX304CBD            OBJECT IDENTIFIER ::= { jnxModuleMX304 8  }
--

--
-- MX10004
--
  jnxProductLineMX10004       OBJECT IDENTIFIER ::= { jnxProductLine      168 }
  jnxProductNameMX10004       OBJECT IDENTIFIER ::= { jnxProductName      168 }
  jnxProductModelMX10004      OBJECT IDENTIFIER ::= { jnxProductModel     168 }
  jnxProductVariationMX10004  OBJECT IDENTIFIER ::= { jnxProductVariation 168 }
  jnxChassisMX10004           OBJECT IDENTIFIER ::= { jnxChassis          168 }
  jnxSlotMX10004              OBJECT IDENTIFIER ::= { jnxSlot             168 }
    jnxMX10004SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotMX10004   1 }
    jnxMX10004SlotHM          OBJECT IDENTIFIER ::= { jnxSlotMX10004   2 }
    jnxMX10004SlotPower       OBJECT IDENTIFIER ::= { jnxSlotMX10004   3 }
    jnxMX10004SlotFan         OBJECT IDENTIFIER ::= { jnxSlotMX10004   4 }
    jnxMX10004SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotMX10004   5 }
    jnxMX10004SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotMX10004   6 }
    jnxMX10004SlotSFB         OBJECT IDENTIFIER ::= { jnxSlotMX10004   7 }
    jnxMX10004SlotFTC         OBJECT IDENTIFIER ::= { jnxSlotMX10004   8 }

  jnxMediaCardSpaceMX10004    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   168 }
    jnxMX10004MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceMX10004 1 }
  jnxModuleMX10004           OBJECT IDENTIFIER ::= { jnxModule    168 }
    jnxMX10004SFB            OBJECT IDENTIFIER ::= { jnxModuleMX10004 1  }
    jnxMX10004HM             OBJECT IDENTIFIER ::= { jnxModuleMX10004 2  }
    jnxMX10004FPC            OBJECT IDENTIFIER ::= { jnxModuleMX10004 3  }
    jnxMX10004Fan            OBJECT IDENTIFIER ::= { jnxModuleMX10004 4  }
    jnxMX10004CBD            OBJECT IDENTIFIER ::= { jnxModuleMX10004 5  }
    jnxMX10004Power          OBJECT IDENTIFIER ::= { jnxModuleMX10004 6  }
    jnxMX10004FPB            OBJECT IDENTIFIER ::= { jnxModuleMX10004 7  }
    jnxMX10004FTC            OBJECT IDENTIFIER ::= { jnxModuleMX10004 8  }
--

--
-- EX4100
--
    jnxProductLineEX4100      OBJECT IDENTIFIER ::= { jnxProductLine      169 }
    jnxProductNameEX4100      OBJECT IDENTIFIER ::= { jnxProductName      169 }
    jnxProductModelEX4100     OBJECT IDENTIFIER ::= { jnxProductModel     169 }
    jnxProductVariationEX4100 OBJECT IDENTIFIER ::= { jnxProductVariation 169 }
        jnxProductEX4100port48MP    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 1 }
        jnxProductEX4100port24MP    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 2 }
        jnxProductEX4100port48P     OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 3 }
        jnxProductEX4100port24P     OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 4 }
        jnxProductEX4100port48T     OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 5 }
        jnxProductEX4100port24T     OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 6 }
        jnxProductEX4100portF48P    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 7 }
        jnxProductEX4100portF24P    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 8 }
        jnxProductEX4100portF48T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 9 }
        jnxProductEX4100portF24T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 10 }
        jnxProductEX4100portF12P    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 11 }
        jnxProductEX4100portF12T    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 12 }
        jnxProductEX4100portH12MP   OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 13 }
        jnxProductEX4100portH24MP   OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 14 }
        jnxProductEX4100portH24F    OBJECT IDENTIFIER ::= { jnxProductVariationEX4100 15 }

    jnxChassisEX4100          OBJECT IDENTIFIER ::= { jnxChassis          169 }
        jnxEX4100RE0            OBJECT IDENTIFIER ::= { jnxChassisEX4100 1  }
        jnxEX4100RE1            OBJECT IDENTIFIER ::= { jnxChassisEX4100 2  }
    jnxSlotEX4100             OBJECT IDENTIFIER ::= { jnxSlot             169 }
        jnxEX4100SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX4100 1 }
            jnxEX4100SlotPower    OBJECT IDENTIFIER ::= { jnxEX4100SlotFPC 1 }
            jnxEX4100SlotFan      OBJECT IDENTIFIER ::= { jnxEX4100SlotFPC 2 }

    jnxMediaCardSpaceEX4100      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    169 }
        jnxEX4100MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4100 1 }

    jnxModuleEX4100            OBJECT IDENTIFIER ::= { jnxModule    169 }
        jnxEX4100FPC            OBJECT IDENTIFIER ::= { jnxModuleEX4100 1 }
        jnxEX4100Power        OBJECT IDENTIFIER ::= { jnxEX4100FPC 1 }
        jnxEX4100Fan          OBJECT IDENTIFIER ::= { jnxEX4100FPC 2 }
--

--
-- PLATFORM INDEPENDENT OIDs
--
-- As of release 6.0, all new Juniper routers will use the following
-- platform-independent OIDs to identify applicable chassis components.
-- Platform-specific OIDs will be defined for those components which
-- are not platform-independent.
--
-- All existing Juniper routers will continue to identify existing
-- chassis components with the existing OIDs.  All new components will
-- be identified with platform-independent OIDs where appropriate.
--

  jnxModuleGeneric    OBJECT IDENTIFIER ::=   { jnxModule        12 }
    jnxFPC              OBJECT IDENTIFIER ::= { jnxModuleGeneric 1  }
    jnxCBD              OBJECT IDENTIFIER ::= { jnxModuleGeneric 2  }
    jnxHM               OBJECT IDENTIFIER ::= { jnxModuleGeneric 3  }
    jnxPower            OBJECT IDENTIFIER ::= { jnxModuleGeneric 4  }
    jnxFan              OBJECT IDENTIFIER ::= { jnxModuleGeneric 5  }
    jnxFPB              OBJECT IDENTIFIER ::= { jnxModuleGeneric 6  }
    jnxCIP              OBJECT IDENTIFIER ::= { jnxModuleGeneric 7  }
    jnxSIB              OBJECT IDENTIFIER ::= { jnxModuleGeneric 8  }
    jnxSFB              OBJECT IDENTIFIER ::= { jnxModuleGeneric 9  }
    jnxFTC              OBJECT IDENTIFIER ::= { jnxModuleGeneric 10  }
    jnxFEB              OBJECT IDENTIFIER ::= { jnxModuleGeneric 11  }
    jnxTIB              OBJECT IDENTIFIER ::= { jnxModuleGeneric 12  }

  jnxPCMCIACard         OBJECT IDENTIFIER ::= { jnxHM             1 }
  jnxUSBHub             OBJECT IDENTIFIER ::= { jnxHM             2 }
  jnxRCompactFlash      OBJECT IDENTIFIER ::= { jnxHM             3 }

  jnxSubmoduleGeneric OBJECT IDENTIFIER ::= { jnxSubmodule        12 }
    jnxPic            OBJECT IDENTIFIER ::= { jnxSubmoduleGeneric 1  }
    jnxMic            OBJECT IDENTIFIER ::= { jnxSubmoduleGeneric 2  }
      --
      -- Generic PIC OIDs
      --
      jnxPicType3TenGigEther     OBJECT IDENTIFIER ::= { jnxPic 1   }
      jnxPicChDs3toDs0           OBJECT IDENTIFIER ::= { jnxPic 2   }
      jnxPicDualChDs3toDs0       OBJECT IDENTIFIER ::= { jnxPic 3   }
      jnxPicAtmIIOc12            OBJECT IDENTIFIER ::= { jnxPic 4   }
      jnxPicAtmOc12              OBJECT IDENTIFIER ::= { jnxPic 5   }
      jnxPicM7iTunnel            OBJECT IDENTIFIER ::= { jnxPic 6   }
      jnxPicChOc12toDs3          OBJECT IDENTIFIER ::= { jnxPic 7   }
      jnxPicCrypto800            OBJECT IDENTIFIER ::= { jnxPic 8   }
      jnxPicType2DualAtmIIOc12   OBJECT IDENTIFIER ::= { jnxPic 9   }
      jnxPicDualAtmIIOc3         OBJECT IDENTIFIER ::= { jnxPic 10  }
      jnxPicDualAtmOc3           OBJECT IDENTIFIER ::= { jnxPic 11  }
      jnxPicDualChDs3            OBJECT IDENTIFIER ::= { jnxPic 12  }
      jnxPicDualE3               OBJECT IDENTIFIER ::= { jnxPic 13  }
      jnxPicDualEia530           OBJECT IDENTIFIER ::= { jnxPic 14  }
      jnxPicDualQChStm1          OBJECT IDENTIFIER ::= { jnxPic 15  }
      jnxPicDualQChDs3           OBJECT IDENTIFIER ::= { jnxPic 16  }
      jnxPicType2DualQHGE        OBJECT IDENTIFIER ::= { jnxPic 17  }
      jnxPicDualSonetOc3         OBJECT IDENTIFIER ::= { jnxPic 18  }
      jnxPicDualDs3              OBJECT IDENTIFIER ::= { jnxPic 19  }
      jnxPicType1Tunnel          OBJECT IDENTIFIER ::= { jnxPic 20  }
      jnxPicGgsnControl          OBJECT IDENTIFIER ::= { jnxPic 21  }
      jnxPicGgsnData             OBJECT IDENTIFIER ::= { jnxPic 22  }
      jnxPicType3TenPortGigEther OBJECT IDENTIFIER ::= { jnxPic 23  }
      jnxPicType3SonetOc192Lr    OBJECT IDENTIFIER ::= { jnxPic 24  }
      jnxPicType3SonetOc192Sr2   OBJECT IDENTIFIER ::= { jnxPic 25  }
      jnxPicType3SonetOc192Vsr   OBJECT IDENTIFIER ::= { jnxPic 26  }
      jnxPicType3QuadSonetOc48   OBJECT IDENTIFIER ::= { jnxPic 27  }
      jnxPicType3Tunnel          OBJECT IDENTIFIER ::= { jnxPic 28  }
      jnxPicGigEther             OBJECT IDENTIFIER ::= { jnxPic 29  }
      jnxPicLsMultilink128       OBJECT IDENTIFIER ::= { jnxPic 30  }
      jnxPicLsMultilink32        OBJECT IDENTIFIER ::= { jnxPic 31  }
      jnxPicLsMultilink4         OBJECT IDENTIFIER ::= { jnxPic 32  }
      jnxPicType2DenseEther48    OBJECT IDENTIFIER ::= { jnxPic 33  }
      jnxPicType2DualGigEther    OBJECT IDENTIFIER ::= { jnxPic 34  }
      jnxPicType2SonetOc48Lr     OBJECT IDENTIFIER ::= { jnxPic 35  }
      jnxPicType2QuadGigEther    OBJECT IDENTIFIER ::= { jnxPic 36  }
      jnxPicType2QuadSonetOc12   OBJECT IDENTIFIER ::= { jnxPic 37  }
      jnxPicType2QuadSonetOc3    OBJECT IDENTIFIER ::= { jnxPic 38  }
      jnxPicType1SonetOc192Sr2   OBJECT IDENTIFIER ::= { jnxPic 39  }
      jnxPicType1SonetOc192Lr1   OBJECT IDENTIFIER ::= { jnxPic 40  }
      jnxPicType1SonetOc192Sr    OBJECT IDENTIFIER ::= { jnxPic 41  }
      jnxPicType1SonetOc192Vsr   OBJECT IDENTIFIER ::= { jnxPic 42  }
      jnxPicType2SonetOc48Sr     OBJECT IDENTIFIER ::= { jnxPic 43  }
      jnxPicType2Tunnel          OBJECT IDENTIFIER ::= { jnxPic 44  }
      jnxPicDecaChE1             OBJECT IDENTIFIER ::= { jnxPic 45  }
      jnxPicDenseEther12         OBJECT IDENTIFIER ::= { jnxPic 46  }
      jnxPicDenseEtherFX8        OBJECT IDENTIFIER ::= { jnxPic 48  }
      jnxPicGigEtherBundle       OBJECT IDENTIFIER ::= { jnxPic 49  }
      jnxPicSonetOc48Lr          OBJECT IDENTIFIER ::= { jnxPic 50  }
      jnxPicSonetOc48Sr          OBJECT IDENTIFIER ::= { jnxPic 51  }
      jnxPicMultilink128         OBJECT IDENTIFIER ::= { jnxPic 52  }
      jnxPicMultilink32          OBJECT IDENTIFIER ::= { jnxPic 53  }
      jnxPicMultilink4           OBJECT IDENTIFIER ::= { jnxPic 54  }
      jnxPicPassiveMonitor       OBJECT IDENTIFIER ::= { jnxPic 55  }
      jnxPicDecaQChE1            OBJECT IDENTIFIER ::= { jnxPic 56  }
      jnxPicQChOc12              OBJECT IDENTIFIER ::= { jnxPic 57  }
      jnxPicQuadAtmE3            OBJECT IDENTIFIER ::= { jnxPic 58  }
      jnxPicQuadAtmT3            OBJECT IDENTIFIER ::= { jnxPic 59  }
      jnxPicQuadChT3             OBJECT IDENTIFIER ::= { jnxPic 60  }
      jnxPicQuadE1               OBJECT IDENTIFIER ::= { jnxPic 61  }
      jnxPicQuadE3               OBJECT IDENTIFIER ::= { jnxPic 62  }
      jnxPicQuadEther            OBJECT IDENTIFIER ::= { jnxPic 63  }
      jnxPicQuadQChT3            OBJECT IDENTIFIER ::= { jnxPic 64  }
      jnxPicQuadSonetOc3         OBJECT IDENTIFIER ::= { jnxPic 65  }
      jnxPicQuadT1               OBJECT IDENTIFIER ::= { jnxPic 66  }
      jnxPicQuadT3               OBJECT IDENTIFIER ::= { jnxPic 67  }
      jnxPicChStm1               OBJECT IDENTIFIER ::= { jnxPic 68  }
      jnxPicQChStm1              OBJECT IDENTIFIER ::= { jnxPic 69  }
      jnxPicSingleQHGE           OBJECT IDENTIFIER ::= { jnxPic 70  }
      jnxPicSonetOc12            OBJECT IDENTIFIER ::= { jnxPic 71  }
      jnxPicSonetOc48            OBJECT IDENTIFIER ::= { jnxPic 72  }
      jnxPicTunnel               OBJECT IDENTIFIER ::= { jnxPic 73  }
      jnxPicGeneralServices      OBJECT IDENTIFIER ::= { jnxPic 74  }
      jnxPicPassiveMonitorAsp    OBJECT IDENTIFIER ::= { jnxPic 75  }
      jnxPicType1TenGigEther     OBJECT IDENTIFIER ::= { jnxPic 76  }
      jnxPicDualATMIIE3          OBJECT IDENTIFIER ::= { jnxPic 77  }
      jnxPicQuadATMIIE3          OBJECT IDENTIFIER ::= { jnxPic 78  }
      jnxPicQuadATMIIT3          OBJECT IDENTIFIER ::= { jnxPic 79  }
      jnxPicQuadQE3              OBJECT IDENTIFIER ::= { jnxPic 80  }
      jnxPicType1Oc48SFP         OBJECT IDENTIFIER ::= { jnxPic 81  }
      jnxPicType2Oc48SFP         OBJECT IDENTIFIER ::= { jnxPic 82  }
      jnxPicGgsnInspection       OBJECT IDENTIFIER ::= { jnxPic 83  }
      jnxPicType3QuadSonetOc48SFP OBJECT IDENTIFIER ::= { jnxPic 84  }
      jnxPicType3TenGigEtherXenpak OBJECT IDENTIFIER ::= { jnxPic 85  }
      jnxPicIntServices          OBJECT IDENTIFIER ::= { jnxPic 86 }
      jnxPicDualFicFE            OBJECT IDENTIFIER ::= { jnxPic 87 }
                                 -- Fixed interface card 2-port FE
      jnxPicFicGE                OBJECT IDENTIFIER ::= { jnxPic 88 }
                                 -- Fixed interface card GigE
      jnxPicSingleSGE            OBJECT IDENTIFIER ::= { jnxPic 89 }
      jnxPicDualSGE              OBJECT IDENTIFIER ::= { jnxPic 90 }
      jnxPicQuadSGE              OBJECT IDENTIFIER ::= { jnxPic 91 }
      jnxPicType3SonetOc192Sr1   OBJECT IDENTIFIER ::= { jnxPic 92 }
      jnxPicAdaptiveServicesII   OBJECT IDENTIFIER ::= { jnxPic 93 }
      jnxPicJseriesEthT1Combo    OBJECT IDENTIFIER ::= { jnxPic 94 }
      jnxPicJseriesEthE1Combo    OBJECT IDENTIFIER ::= { jnxPic 95 }
      jnxPicJseriesEthSerCombo   OBJECT IDENTIFIER ::= { jnxPic 96 }
      jnxPicJseriesDualEth       OBJECT IDENTIFIER ::= { jnxPic 97 }
      jnxPicJseriesDualT1        OBJECT IDENTIFIER ::= { jnxPic 98 }
      jnxPicJseriesDualE1        OBJECT IDENTIFIER ::= { jnxPic 99 }
      jnxPicJseriesDualSerial    OBJECT IDENTIFIER ::= { jnxPic 100 }
      jnxPicJseriesT3            OBJECT IDENTIFIER ::= { jnxPic 101 }
      jnxPicType2AtmIIOc48       OBJECT IDENTIFIER ::= { jnxPic 102 }
      jnxPicSonetOc768Sr         OBJECT IDENTIFIER ::= { jnxPic 103 }
      jnxPicQuadSonetOc192XFP    OBJECT IDENTIFIER ::= { jnxPic 104 }
      jnxPicType4Tunnel          OBJECT IDENTIFIER ::= { jnxPic 105 }
      jnxPicQChoc3               OBJECT IDENTIFIER ::= { jnxPic 106 }
                -- Unused jnxPic 107
      jnxPicType3DWDMTenGigEther OBJECT IDENTIFIER ::= { jnxPic 108 }
      jnxPicType4QuadOC192       OBJECT IDENTIFIER ::= { jnxPic 109 }
                -- Unused jnxPic 110
      jnxPicType1Load            OBJECT IDENTIFIER ::= { jnxPic 111 }
      jnxPicType2Load            OBJECT IDENTIFIER ::= { jnxPic 112 }
      jnxPicType3Load            OBJECT IDENTIFIER ::= { jnxPic 113 }
      jnxPicType4Load            OBJECT IDENTIFIER ::= { jnxPic 114 }
      jnxPicGgsnControlV1        OBJECT IDENTIFIER ::= { jnxPic 115 }
      jnxPicGgsnDataV1           OBJECT IDENTIFIER ::= { jnxPic 116 }
      jnxPicMonitoring3          OBJECT IDENTIFIER ::= { jnxPic 117 }
      jnxPicGgsnPhoenix          OBJECT IDENTIFIER ::= { jnxPic 118 }
      jnxPicAdaptiveServicesFips OBJECT IDENTIFIER ::= { jnxPic 119 }
      jnxPicMonitoring3V1        OBJECT IDENTIFIER ::= { jnxPic 120 }
      jnxPicGgsnPhoenixV1        OBJECT IDENTIFIER ::= { jnxPic 121 }
      jnxPicJseriesE3            OBJECT IDENTIFIER ::= { jnxPic 122 }
      jnxPicLinkServicesII       OBJECT IDENTIFIER ::= { jnxPic 123 }
      jnxPicDecaQChT1            OBJECT IDENTIFIER ::= { jnxPic 124 }
      jnxPicType3IQ21X10GE       OBJECT IDENTIFIER ::= { jnxPic 125 }
      jnxPicType2IQ28X1GE        OBJECT IDENTIFIER ::= { jnxPic 126 }
      jnxPicType1IQ24X1GE        OBJECT IDENTIFIER ::= { jnxPic 127 }
      jnxPic10GEUplink           OBJECT IDENTIFIER ::= { jnxPic 128 }
      jnxPicType2IQ21X10GE       OBJECT IDENTIFIER ::= { jnxPic 129 }
      jnxPicType1MultiServices   OBJECT IDENTIFIER ::= { jnxPic 130 }
      jnxPicType2MultiServices   OBJECT IDENTIFIER ::= { jnxPic 131 }
      jnxPicType3MultiServices   OBJECT IDENTIFIER ::= { jnxPic 132 }
      jnxPicSonetOc192Uplink     OBJECT IDENTIFIER ::= { jnxPic 133 }
      jnxPicXDpc10X1GE           OBJECT IDENTIFIER ::= { jnxPic 134 }
      jnxPicXQDpc10X1GE          OBJECT IDENTIFIER ::= { jnxPic 135 }
      jnxPicXDpc1X10GE           OBJECT IDENTIFIER ::= { jnxPic 136 }
      jnxPicXQDpc1X10GE          OBJECT IDENTIFIER ::= { jnxPic 137 }
      jnxPicType3SonetOc192Xfp   OBJECT IDENTIFIER ::= { jnxPic 138 }
      jnxPicType3IQ28X1GE        OBJECT IDENTIFIER ::= { jnxPic 139 }
      jnxPicType2Sonetoc48Sr2    OBJECT IDENTIFIER ::= { jnxPic 140 }
      jnxPicType2Sonetoc12Sr2    OBJECT IDENTIFIER ::= { jnxPic 141 }
      jnxPicType2Sonetoc3Sr2     OBJECT IDENTIFIER ::= { jnxPic 142 }
      jnxPicStoli4X10GE          OBJECT IDENTIFIER ::= { jnxPic 143 }
      jnxPicType1Sonet4Xoc3      OBJECT IDENTIFIER ::= { jnxPic 144 }
      jnxPicType1Sonet2Xoc3      OBJECT IDENTIFIER ::= { jnxPic 145 }
      jnxPicType1Sonet1Xoc12     OBJECT IDENTIFIER ::= { jnxPic 146 }
      jnxPicGgsnStargateType2    OBJECT IDENTIFIER ::= { jnxPic 147 }
      jnxPicUQDpc10X1GE          OBJECT IDENTIFIER ::= { jnxPic 148 }
      jnxPicUQDpc1X10GE          OBJECT IDENTIFIER ::= { jnxPic 149 }
      jnxPicNPC                  OBJECT IDENTIFIER ::= { jnxPic 150 }
      jnxPicIOC16xGETP           OBJECT IDENTIFIER ::= { jnxPic 151 }
      jnxPicIOC16xGESFP          OBJECT IDENTIFIER ::= { jnxPic 152 }
      jnxPicIOC2x10GEXFP         OBJECT IDENTIFIER ::= { jnxPic 153 }
      jnxPicIOC8xGETP4xGESFP     OBJECT IDENTIFIER ::= { jnxPic 154 }
      jnxPicSPCRMIx1             OBJECT IDENTIFIER ::= { jnxPic 155 }
      jnxPicType3EnhancedLoad    OBJECT IDENTIFIER ::= { jnxPic 156 }
      jnxPicCE4xCHOC3SFP         OBJECT IDENTIFIER ::= { jnxPic 157 }
      jnxPicCE12xT1E1            OBJECT IDENTIFIER ::= { jnxPic 158 }
      jnxPicXDpc10X1GERJ45       OBJECT IDENTIFIER ::= { jnxPic 159 }
      jnxPicQ2ChOc12             OBJECT IDENTIFIER ::= { jnxPic 160 }
      jnxPicQ2Oc12               OBJECT IDENTIFIER ::= { jnxPic 161 }
      jnxPicQ2ChOc3              OBJECT IDENTIFIER ::= { jnxPic 162 }
      jnxPicQ2Oc3                OBJECT IDENTIFIER ::= { jnxPic 163 }
      jnxPicQ2ChDs3              OBJECT IDENTIFIER ::= { jnxPic 164 }
      jnxPicQ2Ds3                OBJECT IDENTIFIER ::= { jnxPic 165 }
      jnxPicQ21xChOc48           OBJECT IDENTIFIER ::= { jnxPic 166 }
      jnxPicQ24xChOc12           OBJECT IDENTIFIER ::= { jnxPic 167 }
      jnxPicQ210xChE1T1          OBJECT IDENTIFIER ::= { jnxPic 168 }
      jnxPicOlivet               OBJECT IDENTIFIER ::= { jnxPic 169 }
      jnxPicType1IQ2E4X1GE       OBJECT IDENTIFIER ::= { jnxPic 170 }
      jnxPicType2IQ2E8X1GE       OBJECT IDENTIFIER ::= { jnxPic 171 }
      jnxPicType3IQ2E8X1GE       OBJECT IDENTIFIER ::= { jnxPic 172 }
      jnxPicType3IQ2E1X10GE      OBJECT IDENTIFIER ::= { jnxPic 173 }
      jnxPicASPCTYPE1            OBJECT IDENTIFIER ::= { jnxPic 174 }
      jnxPicASPCTYPE2            OBJECT IDENTIFIER ::= { jnxPic 175 }
      jnxPicASPCTYPE3            OBJECT IDENTIFIER ::= { jnxPic 176 }
      jnxPicFIOC16X1GETP         OBJECT IDENTIFIER ::= { jnxPic 177 }
      jnxPicFIOC16X1GESFP        OBJECT IDENTIFIER ::= { jnxPic 178 }
      jnxPicFIOC4X10GEXFP        OBJECT IDENTIFIER ::= { jnxPic 179 }
      jnxPicMIC20XGESFP          OBJECT IDENTIFIER ::= { jnxPic 180 }
      jnxPicMIC2X10GEXFP         OBJECT IDENTIFIER ::= { jnxPic 181 }
      jnxPicMIC40XGERJ45         OBJECT IDENTIFIER ::= { jnxPic 182 }
      jnxPicMIC4X10GEXFP         OBJECT IDENTIFIER ::= { jnxPic 183 }
      jnxPicMICLoad              OBJECT IDENTIFIER ::= { jnxPic 184 }
      jnxPicMICH10XGESFP         OBJECT IDENTIFIER ::= { jnxPic 185 }
      jnxPicMICH1X10GEXFP        OBJECT IDENTIFIER ::= { jnxPic 186 }
      jnxPicMICH10XGERJ45        OBJECT IDENTIFIER ::= { jnxPic 187 }
      jnxPicMICH2X10GEXFP        OBJECT IDENTIFIER ::= { jnxPic 188 }
      jnxPicMICHLoad             OBJECT IDENTIFIER ::= { jnxPic 189 }
      jnxPicOtn1X10GE            OBJECT IDENTIFIER ::= { jnxPic 190 }
      jnxPicStoli10X10GE         OBJECT IDENTIFIER ::= { jnxPic 191 }
      jnxPicStoli100GE           OBJECT IDENTIFIER ::= { jnxPic 192 }
      jnxPicType3Q24xChOc12      OBJECT IDENTIFIER ::= { jnxPic 193 }
      jnxPicStoli100GESlot1      OBJECT IDENTIFIER ::= { jnxPic 194 }
      jnxPicUplinkSFPplus1G4     OBJECT IDENTIFIER ::= { jnxPic 195 }
      jnxPicUplinkSFPplus10G2    OBJECT IDENTIFIER ::= { jnxPic 196 }
      jnxPicUplinkXFP2port       OBJECT IDENTIFIER ::= { jnxPic 197 }
      jnxPicUplinkSFP4port       OBJECT IDENTIFIER ::= { jnxPic 198 }
      jnxPicUplinkSFPplus4port   OBJECT IDENTIFIER ::= { jnxPic 199 }
      jnxPicXDpcCombo10X1GE      OBJECT IDENTIFIER ::= { jnxPic 200 }
      jnxPicXQDpcCombo10X1GE     OBJECT IDENTIFIER ::= { jnxPic 201 }
      jnxPicTAZ4X10GEXFP         OBJECT IDENTIFIER ::= { jnxPic 202 }
      jnxPicTAZ48XGERJ45         OBJECT IDENTIFIER ::= { jnxPic 203 }
      jnxPicStoli1X40GECFP       OBJECT IDENTIFIER ::= { jnxPic 204 }
      jnxPicOtnOc192             OBJECT IDENTIFIER ::= { jnxPic 205 }
      jnxPICStoli100GESNAP12     OBJECT IDENTIFIER ::= { jnxPic 206 }
      jnxPicEX820048S            OBJECT IDENTIFIER ::= { jnxPic 207 }
      jnxPicEX820048T            OBJECT IDENTIFIER ::= { jnxPic 208 }
      jnxPicEX82008XS            OBJECT IDENTIFIER ::= { jnxPic 209 }
      jnxPicMIC4X10GESFPPLUS     OBJECT IDENTIFIER ::= { jnxPic 210 }
      jnxPicEX4500UplinkSFPPlus4Port OBJECT IDENTIFIER ::= { jnxPic 211 }
      jnxPicSoho48X10GE          OBJECT IDENTIFIER ::= { jnxPic 212 }
      jnxPicM2LoopBack           OBJECT IDENTIFIER ::= { jnxPic 213 }
      jnxPicCtpGluon4xT1E1       OBJECT IDENTIFIER ::= { jnxPic 214 }
      jnxPicCtpGluon4xSerial     OBJECT IDENTIFIER ::= { jnxPic 215 }
      jnxPicSng24x10GE           OBJECT IDENTIFIER ::= { jnxPic 216 }
      jnxPicSng2x100GE           OBJECT IDENTIFIER ::= { jnxPic 217 }
      jnxPicSngLoad              OBJECT IDENTIFIER ::= { jnxPic 218 }
      jnxPicSysio6XGERJ456XGESFP OBJECT IDENTIFIER ::= { jnxPic 219 }
      jnxPicSysio6XGERJ453XGESFP3X10GESFPPlus OBJECT IDENTIFIER ::= { jnxPic 220 }
      jnxPicDualWideSPCNPC       OBJECT IDENTIFIER ::= { jnxPic 221 }
      jnxPicDualWideNPCSPC       OBJECT IDENTIFIER ::= { jnxPic 222 }
      jnxPicTAZ12XGERJ45         OBJECT IDENTIFIER ::= { jnxPic 223 }
      jnxPicType1MultiServicesFIPS OBJECT IDENTIFIER ::= { jnxPic 224 }
      jnxPicType2MultiServicesFIPS OBJECT IDENTIFIER ::= { jnxPic 225 }
      jnxPicType3MultiServicesFIPS OBJECT IDENTIFIER ::= { jnxPic 226 }
      jnxPicEX4500UplinkXFP4Port OBJECT IDENTIFIER ::= { jnxPic 227 }
      jnxPicEX4500M2Optical OBJECT IDENTIFIER ::= { jnxPic 228 }
      jnxPicEX4500M2Legacy OBJECT IDENTIFIER ::= { jnxPic 229 }
      jnxPicEX820036XS           OBJECT IDENTIFIER ::= { jnxPic 230 }
      jnxPicEX820040XS           OBJECT IDENTIFIER ::= { jnxPic 231 }
      jnxPicEX820048PL           OBJECT IDENTIFIER ::= { jnxPic 232 }
      jnxPicEX82002XS40P         OBJECT IDENTIFIER ::= { jnxPic 233 }
--
-- OIDs 232 and 233 are not defined here as they have been used on
-- the DEV_EX_1001_DOUBLECAP_BRANCH development branch.  Leaving
-- them undefined to prevent merge conflicts.
--

      jnxPicType1ASPCXLP         OBJECT IDENTIFIER ::= { jnxPic 234 }
      jnxPicType2ASPCXLP         OBJECT IDENTIFIER ::= { jnxPic 235 }
      jnxPicType3ASPCXLP         OBJECT IDENTIFIER ::= { jnxPic 236 }
      jnxPicSPCXLPx1             OBJECT IDENTIFIER ::= { jnxPic 237 }
      jnxPicStoli40GE            OBJECT IDENTIFIER ::= { jnxPic 238 }
      jnxPicHyp1X100GECFP        OBJECT IDENTIFIER ::= { jnxPic 239 }
      jnxPicHyp1X40GECFP         OBJECT IDENTIFIER ::= { jnxPic 240 }
      jnxPicHypX100GECXP         OBJECT IDENTIFIER ::= { jnxPic 241 }
      jnxPicHyp10X10GESFPP       OBJECT IDENTIFIER ::= { jnxPic 242 }
      jnxPic12x10GE              OBJECT IDENTIFIER ::= { jnxPic 243 }
      jnxPic1x100GE              OBJECT IDENTIFIER ::= { jnxPic 244 }
      jnxPicHyp2X40GEQSFP        OBJECT IDENTIFIER ::= { jnxPic 245 }
      jnxPicHercules24X10GE      OBJECT IDENTIFIER ::= { jnxPic 246 }
      jnxPicCTPGluonSerialMS     OBJECT IDENTIFIER ::= { jnxPic 247 }
      jnxPicAgent00SLC1X10GE     OBJECT IDENTIFIER ::= { jnxPic 248 }
      jnxPicAgent00SLC4X1GE      OBJECT IDENTIFIER ::= { jnxPic 249 }
      jnxPicQFXSFE16x40GEQSFP    OBJECT IDENTIFIER ::= { jnxPic 250 }
      jnxPicQFXSFI16x40GE        OBJECT IDENTIFIER ::= { jnxPic 251 }
      jnxPicQFXSRI16x40GE        OBJECT IDENTIFIER ::= { jnxPic 252 }
      jnxPicQFX48x10GESFPPlus    OBJECT IDENTIFIER ::= { jnxPic 253 }
      jnxPicQFX4x40GEQSFP        OBJECT IDENTIFIER ::= { jnxPic 254 }
      jnxPicQFX2x80GEQCXP        OBJECT IDENTIFIER ::= { jnxPic 255 }
      jnxPicType3IQECC4XOC48     OBJECT IDENTIFIER ::= { jnxPic 256 }
      jnxPicSng2x40GE            OBJECT IDENTIFIER ::= { jnxPic 257 }

--
-- pics added for IBM 4500
--
      jnxPicIBM0719J45EUplinkSFPPlus4Port OBJECT IDENTIFIER ::= { jnxPic 258 }
      jnxPicIBM0719J45EUplinkXFP4Port     OBJECT IDENTIFIER ::= { jnxPic 259 }
      jnxPicIBM0719J45EM2Optical          OBJECT IDENTIFIER ::= { jnxPic 260 }
      jnxPicIBM0719J45EM2Legacy           OBJECT IDENTIFIER ::= { jnxPic 261 }

--
-- pics added for IBM & Dell for QFX series
--
      jnxPicIBMJ08FSFE16x40GEQSFP    OBJECT IDENTIFIER ::= { jnxPic 262 }
      jnxPicIBMJ08FSFI16xFabric      OBJECT IDENTIFIER ::= { jnxPic 263 }
      jnxPicIBMJ08FSRI16xFabric      OBJECT IDENTIFIER ::= { jnxPic 264 }
      jnxPicIBMJ52F48x10GESFPPlus    OBJECT IDENTIFIER ::= { jnxPic 265 }
      jnxPicIBMJ52F4x40GEQSFP        OBJECT IDENTIFIER ::= { jnxPic 266 }
      jnxPicDellJFX350048x10GESFPPlus OBJECT IDENTIFIER ::= { jnxPic 267 }

---
--- #if REL11.1
--- pics added for rapidshot

      jnxPicEX820048TES         OBJECT IDENTIFIER ::= { jnxPic 268 }
      jnxPicEX820048SES         OBJECT IDENTIFIER ::= { jnxPic 269 }
      jnxPicEX82008XSES         OBJECT IDENTIFIER ::= { jnxPic 270 }
      jnxPicEX820040XSES        OBJECT IDENTIFIER ::= { jnxPic 271 }
      jnxPicEX820048TES4X       OBJECT IDENTIFIER ::= { jnxPic 272 }
      jnxPicEX820048SES4X       OBJECT IDENTIFIER ::= { jnxPic 273 }
      jnxPicEX82008XSES4X       OBJECT IDENTIFIER ::= { jnxPic 274 }
      jnxPicEX820040XSES4X      OBJECT IDENTIFIER ::= { jnxPic 275 }

--- #endif

--
-- pics added for EX62XX series
--
    jnxPicEX620048T                  OBJECT IDENTIFIER ::= { jnxPic 276 }
    jnxPicEX620048P                  OBJECT IDENTIFIER ::= { jnxPic 277 }
    jnxPicEX62004XS                  OBJECT IDENTIFIER ::= { jnxPic 278 }


--
-- pics added for DELL for QFX 3500
--
      jnxPicDellJFX35004x40GEQSFP     OBJECT IDENTIFIER ::= { jnxPic 279 }

--
-- pics added for EX82xx series
--
    jnxPicEX820048TL                 OBJECT IDENTIFIER ::= { jnxPic 280 }
    jnxPicEX82002XS40T               OBJECT IDENTIFIER ::= { jnxPic 281 }


    jnxPicType2MSPrism	             OBJECT IDENTIFIER ::= { jnxPic 282 }
    jnxPicMicMSPrism       	     OBJECT IDENTIFIER ::= { jnxPic 283 }

--
-- pics added for QFX series 3500
--
      jnxPicQFX16x10GESFPPlus           OBJECT IDENTIFIER ::= { jnxPic 284 }
      jnxPicIBMJ52F16x10GESFPPlus       OBJECT IDENTIFIER ::= { jnxPic 285 }
      jnxPicDellJFX350016x10GESFPPlus   OBJECT IDENTIFIER ::= { jnxPic 286 }

--
-- pics added for QFX series Ptunnel ports
--
      jnxPicQFX10xPTunnel               OBJECT IDENTIFIER ::= { jnxPic 287 }
      jnxPicIBMJ52F10xPTunnel           OBJECT IDENTIFIER ::= { jnxPic 288 }

--
-- pics added for Fortius platforms
--
      jnxPic16XT1E1CEMIC                OBJECT IDENTIFIER ::= { jnxPic 289 }
      jnxPic8XT1E1CEMIC                 OBJECT IDENTIFIER ::= { jnxPic 290 }
      jnxPic8xGERJ452xPOEMIC            OBJECT IDENTIFIER ::= { jnxPic 291 }
      jnxPic2xGESFPMIC                  OBJECT IDENTIFIER ::= { jnxPic 292 }
      jnxPic2x10GESFPPLUSMIC            OBJECT IDENTIFIER ::= { jnxPic 293 }
      jnxPic4xGESFPRJ45COMBOMIC         OBJECT IDENTIFIER ::= { jnxPic 294 }

      jnxPicUplinkDualMedia2port        OBJECT IDENTIFIER ::= { jnxPic 295 }

--
-- EX3300 (Dragon-VC)
--

      jnxPicEX3300UplinkSFPPlus4Port    OBJECT IDENTIFIER ::= { jnxPic 296 }


--
-- EX4500 (Tsunami)
--
      jnxPicEX4500UplinkSFP4Port        OBJECT IDENTIFIER ::= { jnxPic 297 }


--
-- EX4550
--
      jnxPicEX4550UplinkEm8XFP          OBJECT IDENTIFIER ::= { jnxPic 298 }
      jnxPicEX4550UplinkEm8XT           OBJECT IDENTIFIER ::= { jnxPic 299 }
      jnxPicEX4550UplinkEm2QSFP         OBJECT IDENTIFIER ::= { jnxPic 300 }
      jnxPicEX4550VC128G                OBJECT IDENTIFIER ::= { jnxPic 301 }


--
-- PIC added for QFX5000
--
      jnxPicQFX16x80GCXP               OBJECT IDENTIFIER ::= { jnxPic 302 }


-- pics added for QFX360016Q/QFX360016QS for QFX series
      jnxPicQFX63x10GESFPPlus           OBJECT IDENTIFIER ::= { jnxPic 303 }
      jnxPicQFX16x40GEQSFP              OBJECT IDENTIFIER ::= { jnxPic 304 }

--
-- Fortius MIC, MX MIC, and Hercules PIC
--
      jnxPic6xGESFPRJ45                OBJECT IDENTIFIER ::= { jnxPic 305 }
      jnxPicMXPISA16xT1E1RJ48          OBJECT IDENTIFIER ::= { jnxPic 306 }
      jnxPic6x40GEQSFPP                OBJECT IDENTIFIER ::= { jnxPic 307 }
      jnxPicACX1xOC124xOC3SFP          OBJECT IDENTIFIER ::= { jnxPic 308 }


--
-- Fortius MIC
--
      jnxPicACXPISA16xT1E1RJ48         OBJECT IDENTIFIER ::= { jnxPic 309 }


--
-- Snorkel MICs
--
      jnxPic8x10GESFPPMIC              OBJECT IDENTIFIER ::= { jnxPic 310 }
      jnxPic1x100GECFPMIC              OBJECT IDENTIFIER ::= { jnxPic 311 }
      jnxPic4x10GESFPPMIC              OBJECT IDENTIFIER ::= { jnxPic 312 }


--
-- PTX OTN PIC
--
      jnxPicPTX2x100GOTNPIC            OBJECT IDENTIFIER ::= { jnxPic 313 }


--
-- MX XLP MICs
--
      jnxPicMXXLPDPCPIC                OBJECT IDENTIFIER ::= { jnxPic 314 }
      jnxPicMXXLP8GMIC                 OBJECT IDENTIFIER ::= { jnxPic 315 }
      jnxPicMXXLP16GMIC                OBJECT IDENTIFIER ::= { jnxPic 316 }
      jnxPicMXXLP8GFIPSMIC             OBJECT IDENTIFIER ::= { jnxPic 317 }
      jnxPicMXXLP16GFIPSMIC            OBJECT IDENTIFIER ::= { jnxPic 318 }


--
-- EX4300 PICs
--
      jnxPicEX4300QSFP4Port             OBJECT IDENTIFIER ::= { jnxPic 319 }
      jnxPicEX4300UplinkSFPPlus4Port    OBJECT IDENTIFIER ::= { jnxPic 320 }


--
-- Australia A10/A2/A1 PICs
--
      jnxPicNPIOC2x10GESFPPLUSPIC      OBJECT IDENTIFIER ::= { jnxPic 321 }


--
-- Twister 4 Port DS3/E3 MIC
--
      jnxPic4CHDS3E3MICSR              OBJECT IDENTIFIER ::= { jnxPic 322 }


--
-- Twister 4 Port CHOC3 - 1 Port CHOC12 MIC
--
      jnxPic4CHOC31CHOC12MICSR         OBJECT IDENTIFIER ::= { jnxPic 323 }


--
-- PTX 24x10GE LAN/WAN/OTN PIC
--
      jnxPicSNG24x10GELWOPIC           OBJECT IDENTIFIER ::= { jnxPic 324 }


--
-- Fortius 8x GE RJ45/SFP MIC
--
      jnxPic8xGESFPRJ45COMBOMIC        OBJECT IDENTIFIER ::= { jnxPic 325 }


--
-- Altius 4X10 SFP+ Builtin MIC
--
      jnxPic4X10GESFPPLUSMIC        OBJECT IDENTIFIER ::= { jnxPic 326 }


--
-- Fortius 4x 1GE RJ45 MIC
--
      jnxPic4xGERJ45MIC                OBJECT IDENTIFIER ::= { jnxPic 327 }


--

-- Scuba 12X10GE SFPP Pseudo PIC (VSC8248-based)
-- Scuba 12X10GE SFPP OTN Pseudo PIC (VSC8496-based)
-- Scuba 2X100GE CFP2 OTN Pseudo PIC
-- Scuba 12X10GE SFPP Pseudo PIC (VSC8248-based)
-- Scuba 12X10GE SFPP OTN Pseudo PIC (VSC8496-based)
-- Scuba 2X100GE CFP2 OTN Pseudo PIC
-- Windsurf 12X10GE SFPP Pseudo PIC (VSC8248-based)
-- Windsurf NX10GE SFPP OTN Debug Pseudo PIC (VSC8494-based)
-- Windsurf 12X10GE SFPP OTN Pseudo PIC (VSC8496-based)
-- Windsurf 3X40GE QSFPP Pseudo PIC
-- Windsurf 1X100GE CFP2 OTN Pseudo PIC

      jnxPic24X10GESFPPMIC             OBJECT IDENTIFIER ::= { jnxPic 328 }
      jnxPic24X10GESFPPOTNMIC          OBJECT IDENTIFIER ::= { jnxPic 329 }
      jnxPic2X100GECFP2MIC             OBJECT IDENTIFIER ::= { jnxPic 330 }
      jnxPic12X10GESFPPPIC             OBJECT IDENTIFIER ::= { jnxPic 331 }
      jnxPic12X10GESFPPOTNPIC          OBJECT IDENTIFIER ::= { jnxPic 332 }
      jnxPic2X100GECFP2PIC             OBJECT IDENTIFIER ::= { jnxPic 333 }
      jnxPicWdSf12X10GESFPPPIC         OBJECT IDENTIFIER ::= { jnxPic 334 }
      jnxPicNX10GESFPPOTNDEBUGPIC      OBJECT IDENTIFIER ::= { jnxPic 335 }
      jnxPicWdSf12X10GESFPPOTNPIC      OBJECT IDENTIFIER ::= { jnxPic 336 }
      jnxPic3X40GEQSFPPPIC             OBJECT IDENTIFIER ::= { jnxPic 337 }
      jnxPic1X100GECFP2PIC             OBJECT IDENTIFIER ::= { jnxPic 338 }


-- pics added for QFX3500-48T4Q/QFX3500-48T4QS for QFX series
      jnxPicQFX48x10GESFP              OBJECT IDENTIFIER ::= { jnxPic 339 }


--
-- KingFisher Red IPC 16X10G pic
-- KingFisher Ultra 2X100G
--
      jnxPicKFIPCSFPPPIC                OBJECT IDENTIFIER ::= { jnxPic 341 }
      jnxPicKFIPCCFP2PIC                OBJECT IDENTIFIER ::= { jnxPic 342 }


--
-- Java 4x1g or 2x10g MACsec capable uplink PIC
--
      jnxPicJAVAxUplinkSFFPlusMACSEC4PORT  OBJECT IDENTIFIER ::= { jnxPic 343 }


-- Platform: EX8200 Morpheus
-- linecards:
--          MLC-48XSO 48 port 10G  SFP+  oversubscribed line card
--          MLC-12LQO 12 port 40G  QSFP+ oversubscribed line card
--          MLC-2CF    2 port 100G CFP
      jnxPicEX8200M48XSO               OBJECT IDENTIFIER ::= { jnxPic 344 }
      jnxPicEX8200M12LQO               OBJECT IDENTIFIER ::= { jnxPic 345 }
      jnxPicEX8200M2CF                 OBJECT IDENTIFIER ::= { jnxPic 346 }

--
-- Opus Removable QIC for Opus TORs
--
      jnxPicOpusQic4X40G               OBJECT IDENTIFIER ::= { jnxPic 347 }


--
-- Altius 20x1G SFP  Hardened  Mic
-- Altius 4xOC3/1xOC12 Channelized Hardened Mic
-- Altius 16X T1/E1 RJ48 CE Hardened MIC
--
      jnxPic20XGESfpEHMIC            OBJECT IDENTIFIER ::= { jnxPic 348 }
      jnxPic1XCOC124XCOC3CEHMIC      OBJECT IDENTIFIER ::= { jnxPic 349 }
      jnxPicPISA16XT1E1HMIC          OBJECT IDENTIFIER ::= { jnxPic 350 }
      jnxPic20XGESFPEMIC             OBJECT IDENTIFIER ::= { jnxPic 351 }



-- Platform: EX42XX
-- SFP+ MACsec Uplink Module
      jnxPicUplinkMacsecSFPplus1G4     OBJECT IDENTIFIER ::= { jnxPic 352 }
      jnxPicUplinkMacsecSFPplus10G2    OBJECT IDENTIFIER ::= { jnxPic 353 }
      jnxPicUplinkMacsecSFPplus4port   OBJECT IDENTIFIER ::= { jnxPic 354 }

--
-- VMX Virtual 10X1GE PIC
--
      jnxPicVMX10X1GEPIC                OBJECT IDENTIFIER ::= { jnxPic 355 }


--
-- Altius 10x1G SFP  Enhanced Hardened  Half Mic
-- Altius 10x1G SFP  Enhanced Half Mic
--
      jnxPic10XGESFPHALFEHMIC          OBJECT IDENTIFIER ::= { jnxPic 356 }
      jnxPic10XGESFPHALFEMIC           OBJECT IDENTIFIER ::= { jnxPic 357 }


--
-- pic added for MX platform
--
      jnxPic1xOC124xOC3SFP              OBJECT IDENTIFIER ::= { jnxPic 358 }

-- Platform: EX9200
-- MIC entries for 2x40GbE, 20x1GbE SFP and 40x1GbE Copper MICs
      jnxPicEX920040x1GbERJ45            OBJECT IDENTIFIER ::= { jnxPic 359 }
      jnxPicEX920020x1GbESFP             OBJECT IDENTIFIER ::= { jnxPic 360 }
      jnxPicEX92002x40GbEQSFPP           OBJECT IDENTIFIER ::= { jnxPic 361 }

--
-- Scuba 4X100GE CXP MIC
--
      jnxPic4X100GECXPMIC              OBJECT IDENTIFIER ::= { jnxPic 362 }


--
-- Opus Removable QIC for Opus ODM TORs
--
      jnxPicQFXEM4Q          OBJECT IDENTIFIER ::= { jnxPic 363 }
      jnxPicQFXEM8S          OBJECT IDENTIFIER ::= { jnxPic 364 }


--
-- Mics for SRX NG-IOC
--
      jnxPicSRXIOC21X100GECFP          OBJECT IDENTIFIER ::= { jnxPic 365 }
      jnxPicSRXIOC210X10GESFPP         OBJECT IDENTIFIER ::= { jnxPic 366 }
      jnxPicSRXIOC22X40GEQSFP          OBJECT IDENTIFIER ::= { jnxPic 367 }


-- Chivas 4X100G CFP2 Pic
-- Chivas Load Pic
--
     jnxPicCHV4X100GCFP2            OBJECT IDENTIFIER ::= { jnxPic 368 }
     jnxPicCHVLOAD                  OBJECT IDENTIFIER ::= { jnxPic 369 }

-- EX4300 Fiber switch MICs
--
      jnxPicEX4300UplinkSFPPlus8Port          OBJECT IDENTIFIER ::= { jnxPic 370 }
      jnxPicEX4300UplinkQSFP2Port             OBJECT IDENTIFIER ::= { jnxPic 371 }
      jnxPicEX4300QSFP2Port                   OBJECT IDENTIFIER ::= { jnxPic 379 }



-- I2C_ID_CHV_FAKE_4x100GE_PIC
--
      jnxPicCHVfake4X100GCFP2            OBJECT IDENTIFIER ::= { jnxPic 372 }


--
-- Platform: QFX5100
-- 24x40GE PIC for Dominus/Central Park

      jnxPicQFX510024Q        OBJECT IDENTIFIER ::= { jnxPic 373 }


--
-- Windsurf 2X10GE SFPP OTN Pseudo PIC
--
      jnxPicWdSf2X10GESFPPOTNPIC         OBJECT IDENTIFIER ::= { jnxPic 374 }


-- EX9200 24x10GE+6x40GE PICs
--
    jnxPicEX920012X10GESFPPPIC           OBJECT IDENTIFIER ::= { jnxPic 375 }
    jnxPicEX92003X40GEQSFPPPIC           OBJECT IDENTIFIER ::= { jnxPic 376 }

-- EX9200 20X1GE MACSEC MIC/Half-MIC
--
    jnxPicEX920020X1GESFPMACSECMIC      OBJECT IDENTIFIER ::= { jnxPic 377 }
    jnxPicEX920020X1GESFPMACSECHALFMIC  OBJECT IDENTIFIER ::= { jnxPic 378 }

-- I2C_ID_CHV_4X100G_OTN_PIC
--
      jnxPicCHV4X100GOTNCFP2              OBJECT IDENTIFIER ::= { jnxPic 380 }


-- I2C_ID_CHV_48X10G_12X40G_LWO_PIC
--
      jnxPicCHV48X10G12X40GLWOPIC         OBJECT IDENTIFIER ::= { jnxPic 381 }


-- Platform: QFX5100
--
      jnxPicQFX24x40GEFQSFP       OBJECT IDENTIFIER ::= { jnxPic 382 }
      jnxPicQFX48x10GEFSFP        OBJECT IDENTIFIER ::= { jnxPic 383 }
      jnxPicQFX6x40GEFQSFP        OBJECT IDENTIFIER ::= { jnxPic 384 }
      jnxPicQFX510048C6QF         OBJECT IDENTIFIER ::= { jnxPic 398 }
      jnxPicQFX510048C6QFQSFP     OBJECT IDENTIFIER ::= { jnxPic 399 }

      jnxPicQFX96X10GEFSFP8X40GEFQSFP        OBJECT IDENTIFIER ::= { jnxPic 385}
      jnxPicQFX48X10GECSFP6X40GEFQSFP        OBJECT IDENTIFIER ::= { jnxPic 386}

-- Platform: QFX5100
-- 48x10GE+6x40GE PIC for Lenoxhill/Caymus
-- 24x10GE+4x40GE PIC for Ridge
-- 96x10GE+8x40GE PIC for Cakebread
-- 48x10GBASET+6x40GE PIC for Nirvana
-- 24x10GE+4x40GE PIC for Ridge - EX4600
-- Opus Removable QIC for Ridge - EX4600
-- 24x10GE+4x40GE PIC for Ridge - EX4600
-- Opus Removable QIC for Ridge - EX4600
--
      jnxPicQFX510048S6Q           OBJECT IDENTIFIER ::= { jnxPic 387 }
      jnxPicQFX510024S4Q           OBJECT IDENTIFIER ::= { jnxPic 388 }
      jnxPicQFX510096S8Q           OBJECT IDENTIFIER ::= { jnxPic 389 }
      jnxPicQFX510048C6Q           OBJECT IDENTIFIER ::= { jnxPic 390 }
      jnxPicEX460024S4Q            OBJECT IDENTIFIER ::= { jnxPic 391 }
      jnxPicEX4600EM8F             OBJECT IDENTIFIER ::= { jnxPic 392 }

-- Platform: STOUT
-- MIC8-100G-CFP4
-- MIC8-40G-QSFPP
-- MPC8E Load MIC
-- MPC7E 6xQSFPP PIC
-- MPC7E 20x10FE PIC
--
      jnxPic8X100GECFP4MIC         OBJECT IDENTIFIER ::= { jnxPic 393 }
      jnxPic12X40GEQSFPPMIC        OBJECT IDENTIFIER ::= { jnxPic 394 }
      jnxPicMPC8LOADMIC            OBJECT IDENTIFIER ::= { jnxPic 395 }
      jnxPic6XQSFPP                OBJECT IDENTIFIER ::= { jnxPic 396 }
      jnxPic20X10GE                OBJECT IDENTIFIER ::= { jnxPic 397 }


-- Platform: CHIVAS
      jnxPicCHV12X40GLWOPIC        OBJECT IDENTIFIER ::= { jnxPic 400 }

-- Platform: SRX5K
      jnxPicSRXIOC220X1GESFP       OBJECT IDENTIFIER ::= { jnxPic 401 }
      jnxPicSRXIOC210X1GESFP       OBJECT IDENTIFIER ::= { jnxPic 402 }

-- Platform: FORTIUS
      jnxPic3xGERJ453xPOEMIC       OBJECT IDENTIFIER ::= { jnxPic 403 }
      jnxPic3xGERJ45MIC            OBJECT IDENTIFIER ::= { jnxPic 404 }
      jnxPic4xGESFPRJ453xPOEMIC    OBJECT IDENTIFIER ::= { jnxPic 405 }
      jnxPic3xGESFP                OBJECT IDENTIFIER ::= { jnxPic 406 }
      jnxPicMultiserviceBuiltin    OBJECT IDENTIFIER ::= { jnxPic 407 }

-- Platform: CHIVAS
-- I2C_ID_CHV_4X100G_CXP_PIC
      jnxPicCHV4X100GCXPPIC        OBJECT IDENTIFIER ::= { jnxPic 408 }

-- Platform: POLARIS
-- 24x10GE SFP+ MLC Virtual PIC
-- I2C_ID_POLARIS_MLC_24X10GE_SFPP_PIC
      jnxPicPTXMLC24X10GESFPP      OBJECT IDENTIFIER ::= { jnxPic 409 }

-- Platform: MX
-- I2C_ID_CORDOBA_1X100_DWDM_CFP2_ACO_MIC
      jnxPicCordoba1X100DwdmMIC    OBJECT IDENTIFIER ::= { jnxPic 410 }

-- Platform: PTX
-- I2C_ID_SHOCHU_LOAD_MIC
      jnxPicPTXLoadMIC             OBJECT IDENTIFIER ::= { jnxPic 411 }

-- Platform: SRX
-- I2C_ID_IOC3_12X10GE_SFPP_PIC
-- I2C_ID_IOC3_3X40GE_QSFPP_PIC
-- I2C_ID_IOC3_1X100GE_CFP2_PIC
-- I2C_ID_IOC3_2X10GE_SFPP_PIC
      jnxPicIOCIII12X10SFPP        OBJECT IDENTIFIER ::= { jnxPic 412 }
      jnxPicIOCIII4X40QSFPP        OBJECT IDENTIFIER ::= { jnxPic 413 }
      jnxPicIOCIII1X100CFP2        OBJECT IDENTIFIER ::= { jnxPic 414 }
      jnxPicIOCIII2X10SFPP         OBJECT IDENTIFIER ::= { jnxPic 415 }

-- Platform: EX
-- I2C_ID_EX9200_10X10GE_SFPP_MIC
-- I2C_ID_EX9200_20X10GE_SFPP_PIC
-- I2C_ID_EX9200_6XQSFPP_PIC
        jnxPicEX920010X10GESFPPMIC  OBJECT IDENTIFIER ::= { jnxPic 416 }
        jnxPicEX920020X10GESFPPMIC  OBJECT IDENTIFIER ::= { jnxPic 417 }
        jnxPicEX92006XQSFPPPIC      OBJECT IDENTIFIER ::= { jnxPic 418 }

-- Platform: PTX
-- I2C_ID_GLD_15X100GE_CFP4_REV1_PIC
-- I2C_ID_GLD_10X100GE_CFP4_REV1_PIC
        jnxPicPTX15X100GEREV1PIC    OBJECT IDENTIFIER ::= { jnxPic 419}
        jnxPicPTX10X100GEREV1PIC    OBJECT IDENTIFIER ::= { jnxPic 420}

-- Platform: PTX
-- I2C_ID_SNG_2x100G_OTN_METRO_PIC
        jnxPicPTX2X100GMETROOTNPIC  OBJECT IDENTIFIER ::= { jnxPic 421}

-- Platform: QFX5100
-- 24x40G PIC for Bedrock
-- 4x40G QIC for Ikon
--
      jnxPicQFX510024QAA           OBJECT IDENTIFIER ::= { jnxPic 422 }
      jnxPicQFXPFA4Q               OBJECT IDENTIFIER ::= { jnxPic 423 }

-- Platform: ACX5K
-- 48x10GE+6x40GE PIC for ACX5048
-- 96x10GE+8x40GE PIC for ACX5096
--
      jnxPicACX5048                OBJECT IDENTIFIER ::= { jnxPic 424 }
      jnxPicACX5096                OBJECT IDENTIFIER ::= { jnxPic 425 }

-- Platform: PTX3000 & PTX5000
-- I2C_ID_CORDOBA_5X100_DWDM_CFP2_ACO_PIC
      jnxPicCordoba5X100DwdmPIC    OBJECT IDENTIFIER ::= { jnxPic 426 }

-- Platform: SHOCHU
-- I2C_ID_SHOCHU_10x100GE_10x40GE_40x10GE_QSFP_PIC
        jnxPicSHO10X100GEQSFPPIC    OBJECT IDENTIFIER ::= { jnxPic 431}

-- Platform: QFX
-- I2C_ID_VQFX500_24X100GE_PIC
        jnxPicVQFX5C24X100GEPIC     OBJECT IDENTIFIER ::= { jnxPic 432}

-- Platform: PTX1000
-- I2C_ID_SPRINGBANK_72X40G
        jnxPicPTX1K72X40GEPIC       OBJECT IDENTIFIER ::= { jnxPic 433}

-- Platform: QFX10002
-- 72x40G PIC for Elit
-- 36x40G PIC for Elit-Lite
--
      jnxPicQFX1000236Q            OBJECT IDENTIFIER ::= { jnxPic 434 }
      jnxPicQFX1000272Q            OBJECT IDENTIFIER ::= { jnxPic 435 }

-- Platform: QFX5200/QFX5110
--
      jnxPicQFX520032C32Q          OBJECT IDENTIFIER ::= { jnxPic 436 }
      jnxPicQFX520032C64Q          OBJECT IDENTIFIER ::= { jnxPic 437 }
      jnxPicQ511048S4Q2C           OBJECT IDENTIFIER ::= { jnxPic 438 }
      jnxPicQ511032Q4C             OBJECT IDENTIFIER ::= { jnxPic 439 }

-- EX3400 PICs
--
      jnxPicEX3400QSFP2Port           OBJECT IDENTIFIER ::= { jnxPic 440 }
      jnxPicEX3400UplinkSFPPlus4Port  OBJECT IDENTIFIER ::= { jnxPic 441 }

-- Platform: VIRTUAL
-- I2C_ID_VIRTUAL_10GE_40GE_100GE_PIC
        jnxPic10GE40GE100GEPIC      OBJECT IDENTIFIER ::= { jnxPic 442}

-- EX2300 PICs
--
      jnxPicEX2300UplinkSFPPlus4Port  OBJECT IDENTIFIER ::= { jnxPic 443 }
      jnxPicEX2300UplinkSFPPlus2Port  OBJECT IDENTIFIER ::= { jnxPic 444 }

-- Platform: Onager
-- I2C_ID_SRXSME_T1E1_R_PIC
-- I2C_ID_SRXSME_VDSL_ANNEX_A_R_PIC
-- I2C_ID_SRXSME_SERIAL_R_PIC
-- I2C_ID_SRXSME_16PORT_GE_POE_R_PIC
-- I2C_ID_SRXSME_8SFP_R_PIC
      jnxPicSRXSMET1E1RPIC          OBJECT IDENTIFIER ::= { jnxPic 445 }
      jnxPicSRXSMEVDSLANNEXARPIC    OBJECT IDENTIFIER ::= { jnxPic 446 }
      jnxPicSRXSMESERIALRPIC        OBJECT IDENTIFIER ::= { jnxPic 447 }
      jnxPicSRXSME16PORTGEPOERPIC   OBJECT IDENTIFIER ::= { jnxPic 448 }
      jnxPicSRXSME8SFPRPIC          OBJECT IDENTIFIER ::= { jnxPic 449 }

-- Platform: QFX10004/QFX10008/QFX10016
-- ULC-36Q-12Q28
-- ULC-30Q28
--
      jnxPicULC36Q12Q28            OBJECT IDENTIFIER ::= { jnxPic 450 }
      jnxPicULC30Q28               OBJECT IDENTIFIER ::= { jnxPic 451 }

--
-- VMX Virtual MIC
--
      jnxPicVMXMIC                OBJECT IDENTIFIER ::= { jnxPic 452 }

      jnxPicMIC8OC3OC124OC48      OBJECT IDENTIFIER ::= { jnxPic 453 }
      jnxPicMIC4OC3OC121OC48      OBJECT IDENTIFIER ::= { jnxPic 454 }
      jnxPicMIC8DS3E3             OBJECT IDENTIFIER ::= { jnxPic 455 }
      jnxPicMIC8CHDS3E3           OBJECT IDENTIFIER ::= { jnxPic 456 }
      jnxPicMIC8CHOC34CHOC12      OBJECT IDENTIFIER ::= { jnxPic 457 }
      jnxPicMIC4CHOC32CHOC12      OBJECT IDENTIFIER ::= { jnxPic 458 }
      jnxPicMIC1CHOC48            OBJECT IDENTIFIER ::= { jnxPic 459 }
      jnxPicMIC12CHE1T1           OBJECT IDENTIFIER ::= { jnxPic 460 }
      jnxPicMIC1OC192HOVCAT       OBJECT IDENTIFIER ::= { jnxPic 461 }

-- Platform: SHOCHU GLADIATOR
-- I2C_ID_SHOCHU_10x100GE_10x40GE_40x10GE_QSFP_V2_PIC
-- I2C_ID_GLD_96x10GE_24x40GE_8x100GE_QSFP_V2_PIC
      jnxPicSHO10X100GEQSFPV2PIC                  OBJECT IDENTIFIER ::= { jnxPic 462}
      jnxPicGLD96x10GE24x40GE8x100GEQSFPV2PIC     OBJECT IDENTIFIER ::= { jnxPic 463}

-- Platform: SRX4600/SRX4800
-- I2C_ID_SUMMIT_SRX1RU_4XQSFP28_PIC
-- I2C_ID_SUMMIT_SRX1RU_8XSFPP_PIC
-- I2C_ID_SUMMIT_SRX3RU_4XQSFPP_PIC
      jnxPicSummitSRX1RU4xQSFP28PIC     OBJECT IDENTIFIER ::= { jnxPic 464 }
      jnxPicSummitSRX1RU8xSFPPPIC       OBJECT IDENTIFIER ::= { jnxPic 465 }
      jnxPicSummitSRX3RU4xQSFPPPIC      OBJECT IDENTIFIER ::= { jnxPic 466 }

-- Platform: QFX5100
-- I2C_ID_ODM_ROMBAUER_QIC_8X10G
--
      jnxPicOpusFcQic8X10G         OBJECT IDENTIFIER ::= { jnxPic 467 }

-- I2C_ID_ARRAN_3X400GE_12X100GE_CFP8_PIC
--
      jnxPicPTX3X400GE12X100GECFP8PIC          OBJECT IDENTIFIER ::= { jnxPic 468 }


-- Platform: SUMMIT LOADTIC
-- I2C_ID_LOAD_TIC
        jnxPicSummitLoadTIC        OBJECT IDENTIFIER ::= { jnxPic 469 }

-- Platform: MXTSR80
-- I2C_ID_MXTSR80_2XSFPP_2XSFP_SECURE_MIC
      jnxPicMXTSR802xSFPP2xSFPSecureMIC    OBJECT IDENTIFIER ::= { jnxPic 470 }

-- Platform: Vale Load Tic
-- I2C_ID_VALE_LOAD_TIC_S2S_AIR_FLOW
      jnxPicValeLoadTicS2SAirFlow 	OBJECT IDENTIFIER ::= { jnxPic 471 }

-- Platform: Sprite
-- I2C_ID_JSRXNLE_LTE_AA_PIC
-- I2C_ID_JSRXNLE_LTE_AE_PIC
      jnxPicSRXSMELTEAAPIC          OBJECT IDENTIFIER ::= { jnxPic 472 }
      jnxPicSRXSMELTEAEPIC          OBJECT IDENTIFIER ::= { jnxPic 473 }

-- Platform: Ferrari
-- I2C_ID_FERRARI_5XQSFPP_PIC
      jnxPic5XQSFPP                 OBJECT IDENTIFIER ::= { jnxPic 474 }

-- Platform: EX4300-48MP
-- I2C_ID_EX4300_48MP_4xSFPP_PIC
-- I2C_ID_EX4300_48MP_2xQSFP_PIC
-- I2C_ID_EX4300_48MP_1xQSFP28_PIC
    jnxPicEX4300MP4xSFPPlusPIC         OBJECT IDENTIFIER ::= { jnxPic 475 }
    jnxPicEX4300MP2xQSFPPIC            OBJECT IDENTIFIER ::= { jnxPic 476 }
    jnxPicEx4300MP1xQSFP28PIC          OBJECT IDENTIFIER ::= { jnxPic 477 }
    jnxPicEX4300MPQSFPPlus4Port        OBJECT IDENTIFIER ::= { jnxPic 489 }

-- Platform: MX10003
-- I2C_ID_SUMMIT_MX3RU_6XQSFPP_PIC
-- I2C_ID_SUMMIT_MX3RU_12XQSFP28_MACSEC_TIC
-- I2C_ID_SUMMIT_MX3RU_12XQSFP28_TIC
        jnxPicSummitMX3RU6xQSPPPIC                 OBJECT IDENTIFIER ::= { jnxPic 484 }
        jnxPicSummitMX3RU12xQSFP28MacsecTIC        OBJECT IDENTIFIER ::= { jnxPic 485 }
        jnxPicSummitMX3RU12xQSFP28TIC              OBJECT IDENTIFIER ::= { jnxPic 486 }
-- Platform: Porter Stage-3
-- I2C_ID_NFX_EM_8T
-- I2C_ID_NFX_EM_4XSFP
-- I2C_ID_NFX_EM_8XSFP
-- I2C_ID_NFX_EM_2T2SFP
-- I2C_ID_NFX_LTE_AE
-- I2C_ID_NFX_LTE_AA
    jnxPicNFXEM8T                 OBJECT IDENTIFIER ::= { jnxPic 478 }
    jnxPicNFXEM4XSFP              OBJECT IDENTIFIER ::= { jnxPic 479 }
    jnxPicNFXEM8XSFP              OBJECT IDENTIFIER ::= { jnxPic 480 }
    jnxPicNFXEM2T2SFP             OBJECT IDENTIFIER ::= { jnxPic 481 }
    jnxPicNFXEMLTEAE             OBJECT IDENTIFIER ::= { jnxPic 482 }
    jnxPicNFXEMLTEAA             OBJECT IDENTIFIER ::= { jnxPic 483 }

-- Platform: SRX4600/SRX4800
-- I2C_ID_SUMMIT_SRX_HA_4XSFPP_PIC
-- I2C_ID_SUMMIT_SRX_FLOW_PIC
      jnxPicSummitSRXHA4xSFPPPIC        OBJECT IDENTIFIER ::= { jnxPic 487 }
      jnxPicSummitSRXFLOWPIC            OBJECT IDENTIFIER ::= { jnxPic 495 }

-- Platform: ARGUS MACSEC 16QSFP28 TIC
-- I2C_ID_QFX5400_16QSFP28_TIC
    jnxPicQFX540016QSFP28TIC                 OBJECT IDENTIFIER ::= { jnxPic 488 }
-- Platform:EX4300MP
--
    jnxProductEX4300port48MP   OBJECT IDENTIFIER ::= { jnxProductVariationEX4300 9 }
    jnxEX4300MPSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX4300 2 }
        jnxEX4300MPSlotFan      OBJECT IDENTIFIER ::= { jnxEX4300MPSlotFPC 1 }
    jnxEX4300MPRE0            OBJECT IDENTIFIER ::= { jnxChassisEX4300 3  }

-- Platform: PTX
-- I2C_ID_BRACKLA_PIC
    jnxPicBracklaPIC             OBJECT IDENTIFIER ::= { jnxPic 490 }

-- Platform: ACX5448
-- I2C_ID_ACX5448_48X10GE_SFP_MIC
-- I2C_ID_ACX5448_4X100GE_SFP_MIC
    jnxPicACX544848X10GESFPMIC             OBJECT IDENTIFIER ::= { jnxPic 491 }
    jnxPicACX54484X100GESFPMIC             OBJECT IDENTIFIER ::= { jnxPic 492 }

-- Platform: MX2K
-- I2C_ID_REDBULL_10XQSFPP_PIC
    jnxPicRedbull10xQSFPPPIC             OBJECT IDENTIFIER ::= { jnxPic 493 }

-- I2C_ID_ARRAN_FAKE_3X400GE_PIC
--
      jnxPicPTXfake3X400GECFP8   OBJECT IDENTIFIER ::= { jnxPic 494 }


-- Platform: STOUT
-- I2C_ID_STOUT_12XQSFP28_MACSEC_TIC
    jnxPicStout12xQSFP28MacsecTIC          OBJECT IDENTIFIER ::= { jnxPic 497 }

-- Platform: PTX1000
-- I2C_ID_8X200GE_DWDM_CFP2_DCO_TIC
    jnxPicPTX10008XCFP2DCOTIC            OBJECT IDENTIFIER ::= { jnxPic 498 }
-- Platform: QFX10002-60C
    jnxPicQFX1000260C                    OBJECT IDENTIFIER ::= { jnxPic 499 }

-- EX2300MP PICs
--
      jnxPicEX2300MPUplinkSFPPlus6Port  OBJECT IDENTIFIER ::= { jnxPic 500 }
      jnxPicEX2300MPUplinkSFPPlus4Port  OBJECT IDENTIFIER ::= { jnxPic 501 }

-- Platform: SRX5K
-- I2C_ID_SPC3_TYPE1_PIC
-- I2C_ID_SPC3_TYPE2_PIC
-- I2C_ID_SPC3_TYPE3_PIC
    jnxPicSPC3SPUCPType1PIC         OBJECT IDENTIFIER ::= { jnxPic 502 }
    jnxPicSPC3SPUFlowType2PIC       OBJECT IDENTIFIER ::= { jnxPic 503 }
    jnxPicSPC3SPUCPFlowType3PIC     OBJECT IDENTIFIER ::= { jnxPic 504 }

-- Platform: PTX5K
-- I2C_ID_GLD_96x10GE_24x40GE_8x100GE_QSFP_PIC
-- I2C_ID_GLD_15x100GE_15x40GE_60x10GE_QSFP_PIC
      jnxPicGLD15x100GE15x40GE60x10GEQSFPPIC OBJECT IDENTIFIER ::= { jnxPic 505 }
      jnxPicGLD96x10GE24x40GEQSFPPIC  OBJECT IDENTIFIER ::= { jnxPic 506 }

-- Platform: MX10008
-- I2C_ID_INDUS_4XQSFPP_PIC
      jnxPicIndus4xQSFP28MacsecPIC      OBJECT IDENTIFIER ::= { jnxPic 507 }

-- Platform: EX4650
--
  jnxProductLineEX4650        OBJECT IDENTIFIER ::= { jnxProductLine      508 }
  jnxProductNameEX4650        OBJECT IDENTIFIER ::= { jnxProductName      508 }
  jnxProductModelEX4650       OBJECT IDENTIFIER ::= { jnxProductModel     508 }
  jnxProductVariationEX4650   OBJECT IDENTIFIER ::= { jnxProductVariation 508 }
    jnxProductEX465048Y8C     OBJECT IDENTIFIER ::= { jnxProductVariationEX4650 1 }

  jnxChassisEX4650            OBJECT IDENTIFIER ::= { jnxChassis          508 }

  jnxSlotEX4650               OBJECT IDENTIFIER ::= { jnxSlot          508 }
    jnxEX4650SlotFPC            OBJECT IDENTIFIER ::= { jnxSlotEX4650     1 }
    jnxEX4650SlotHM             OBJECT IDENTIFIER ::= { jnxSlotEX4650     2 }
    jnxEX4650SlotPower          OBJECT IDENTIFIER ::= { jnxSlotEX4650     3 }
    jnxEX4650SlotFan            OBJECT IDENTIFIER ::= { jnxSlotEX4650     4 }
    jnxEX4650SlotRE             OBJECT IDENTIFIER ::= { jnxSlotEX4650     5 }

  jnxMediaCardSpaceEX4650      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    508 }
    jnxEX4650MediaCardSpacePIC    OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4650 1 }

  jnxModuleEX4650             OBJECT IDENTIFIER ::= { jnxModule    508 }
    jnxEX4650FPC              OBJECT IDENTIFIER ::= { jnxModuleEX4650 1 }
      jnxEX4650Power          OBJECT IDENTIFIER ::= { jnxEX4650FPC 1 }
      jnxEX4650Fan            OBJECT IDENTIFIER ::= { jnxEX4650FPC 2 }
      jnxEX4650RE             OBJECT IDENTIFIER ::= { jnxEX4650FPC 3 }
-- Platform: Goose Island 1G/10G MACSec MIC
-- I2C_ID_GI_20X1GE_2X10GE_MACSEC_MIC
    jnxPic2x10GESFPP20xGESFPMACSecMIC  OBJECT IDENTIFIER ::= { jnxPic 509 }

-- Platform: MX10008
-- I2C_ID_INDUS_4XQSFPP_SYNCE_PIC
      jnxPicIndus4xQSFP28SyncePIC      OBJECT IDENTIFIER ::= { jnxPic 510 }

-- Platform: QFX52XX
-- I2C_ID_AZURITE_QFX5210_64C I2C_ID_SYMPHONY_QFX5200-48Y
--
    jnxPicQFX521064C            OBJECT IDENTIFIER ::= { jnxPic 511 }
    jnxPicQFX520048Y            OBJECT IDENTIFIER ::= { jnxPic 512 }

-- Platform: PTX10002-60C
    jnxPicPTX1000260C                    OBJECT IDENTIFIER ::= { jnxPic 513 }

  jnxProductLinePTX1000260C       OBJECT IDENTIFIER ::= { jnxProductLine      513 }
  jnxProductNamePTX1000260C       OBJECT IDENTIFIER ::= { jnxProductName      513 }
  jnxProductModelPTX1000260C      OBJECT IDENTIFIER ::= { jnxProductModel     513 }
  jnxProductVariationPTX1000260C  OBJECT IDENTIFIER ::= { jnxProductVariation 513 }
  jnxChassisPTX1000260C           OBJECT IDENTIFIER ::= { jnxChassis          513 }

  jnxSlotPTX1000260C              OBJECT IDENTIFIER ::= { jnxSlot             513 }
    jnxPTX1000260CSlotFPC         OBJECT IDENTIFIER ::= { jnxSlotPTX1000260C   1 }
    jnxPTX1000260CSlotHM          OBJECT IDENTIFIER ::= { jnxSlotPTX1000260C   2 }
    jnxPTX1000260CSlotPower       OBJECT IDENTIFIER ::= { jnxSlotPTX1000260C   3 }
    jnxPTX1000260CSlotFan         OBJECT IDENTIFIER ::= { jnxSlotPTX1000260C   4 }
    jnxPTX1000260CSlotFPB         OBJECT IDENTIFIER ::= { jnxSlotPTX1000260C   5 }

  jnxMediaCardSpacePTX1000260C    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   513 }
  jnxPTX1000260CMediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpacePTX1000260C 1 }


-- Platform: QFX5220
-- I2C_ID_SAPPHIRE_32CD_PIC I2C_ID_SAPPHIRE_128C_PIC
    jnxPicSapphire32CDPIC             OBJECT IDENTIFIER ::= { jnxPic 514 }
    jnxPicSapphire128CPIC             OBJECT IDENTIFIER ::= { jnxPic 515 }

-- Platform: QFX5200
-- I2C_ID_NAUTILUS_32C_PIC
    jnxPicDCOSQFX520032CPIC           OBJECT IDENTIFIER ::= { jnxPic 516 }

-- Platform: MX2020
-- I2C_ID_REDBULL_5XQSFPP_PIC
    jnxPicRB5xQSFPP             OBJECT IDENTIFIER ::= { jnxPic 517 }

-- Platform: PTX100XX
-- I2C_ID_JNP10K_36QDD_LC_PIC
    jnxPicJnp10k36xQSFPDD             OBJECT IDENTIFIER ::= { jnxPic 518 }

-- Platform: QFX5120/EX4650
-- I2C_ID_ONYX_48Y8C
    jnxPicQFX512048Y8C            OBJECT IDENTIFIER ::= { jnxPic 519 }
    jnxPicEX465048Y8C             OBJECT IDENTIFIER ::= { jnxPic 520 }

-- Platform: ACX6180-T
-- I2C_ID_ATTELLA_8XQSFP28_PIC I2C_ID_ATTELLA_4XCFP2DCO_PIC
    jnxPicAttella8XQSFP28PIC             OBJECT IDENTIFIER ::= { jnxPic 521 }
    jnxPicAttella4XCFP2DCOPIC            OBJECT IDENTIFIER ::= { jnxPic 522 }


-- Platform: PTX10003-80c - Brackla
--

  jnxProductLinePTX1000380c      OBJECT IDENTIFIER ::= { jnxProductLine      523 }
  jnxProductNamePTX1000380c      OBJECT IDENTIFIER ::= { jnxProductName      523 }
  jnxProductModelPTX1000380c     OBJECT IDENTIFIER ::= { jnxProductModel     523 }
  jnxProductVariationPTX1000380c OBJECT IDENTIFIER ::= { jnxProductVariation 523 }
  jnxChassisPTX1000380c          OBJECT IDENTIFIER ::= { jnxChassis          523 }

  jnxSlotPTX1000380c             OBJECT IDENTIFIER ::= { jnxSlot             523 }
    jnxPTX1000380cSlotRE         OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 1  }
    jnxPTX1000380cSlotCB         OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 2  }
    jnxPTX1000380cSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 3  }
    jnxPTX1000380cSlotFan        OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 4  }
    jnxPTX1000380cSlotFPM        OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 5  }
    jnxPTX1000380cSlotSIB        OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 6  }
    jnxPTX1000380cSlotPIC        OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 7  }
    jnxPTX1000380cSlotPDU        OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 8  }
    jnxPTX1000380cSlotPSM        OBJECT IDENTIFIER ::= { jnxSlotPTX1000380c 9  }

  jnxModulePTX1000380c           OBJECT IDENTIFIER ::= { jnxModule    523 }
    jnxPTX1000380cRE             OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   1  }
    jnxPTX1000380cCB             OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   2  }
    jnxPTX1000380cFPC            OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   3  }
    jnxPTX1000380cFan            OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   4  }
    jnxPTX1000380cFPM            OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   5  }
    jnxPTX1000380cSIB            OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   6  }
    jnxPTX1000380cPIC            OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   7  }
    jnxPTX1000380cPDU            OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   8  }
    jnxPTX1000380cPSM            OBJECT IDENTIFIER ::= { jnxModulePTX1000380c   9  }

-- Platform: PTX10003-160c - Brackla
--

  jnxProductLinePTX10003160c      OBJECT IDENTIFIER ::= { jnxProductLine      524 }
  jnxProductNamePTX10003160c      OBJECT IDENTIFIER ::= { jnxProductName      524 }
  jnxProductModelPTX10003160c     OBJECT IDENTIFIER ::= { jnxProductModel     524 }
  jnxProductVariationPTX10003160c OBJECT IDENTIFIER ::= { jnxProductVariation 524 }
  jnxChassisPTX10003160c          OBJECT IDENTIFIER ::= { jnxChassis          524 }

  jnxSlotPTX10003160c             OBJECT IDENTIFIER ::= { jnxSlot             524 }
    jnxPTX10003160cSlotRE         OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 1  }
    jnxPTX10003160cSlotCB         OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 2  }
    jnxPTX10003160cSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 3  }
    jnxPTX10003160cSlotFan        OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 4  }
    jnxPTX10003160cSlotFPM        OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 5  }
    jnxPTX10003160cSlotSIB        OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 6  }
    jnxPTX10003160cSlotPIC        OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 7  }
    jnxPTX10003160cSlotPDU        OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 8  }
    jnxPTX10003160cSlotPSM        OBJECT IDENTIFIER ::= { jnxSlotPTX10003160c 9  }

  jnxModulePTX10003160c           OBJECT IDENTIFIER ::= { jnxModule    524 }
    jnxPTX10003160cRE             OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   1  }
    jnxPTX10003160cCB             OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   2  }
    jnxPTX10003160cFPC            OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   3  }
    jnxPTX10003160cFan            OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   4  }
    jnxPTX10003160cFPM            OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   5  }
    jnxPTX10003160cSIB            OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   6  }
    jnxPTX10003160cPIC            OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   7  }
    jnxPTX10003160cPDU            OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   8  }
    jnxPTX10003160cPSM            OBJECT IDENTIFIER ::= { jnxModulePTX10003160c   9  }

-- Platform: QFX10003-80c - Brackla
--

  jnxProductLineQFX1000380c      OBJECT IDENTIFIER ::= { jnxProductLine      525 }
  jnxProductNameQFX1000380c      OBJECT IDENTIFIER ::= { jnxProductName      525 }
  jnxProductModelQFX1000380c     OBJECT IDENTIFIER ::= { jnxProductModel     525 }
  jnxProductVariationQFX1000380c OBJECT IDENTIFIER ::= { jnxProductVariation 525 }
  jnxChassisQFX1000380c          OBJECT IDENTIFIER ::= { jnxChassis          525 }

  jnxSlotQFX1000380c             OBJECT IDENTIFIER ::= { jnxSlot             525 }
    jnxQFX1000380cSlotRE         OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 1  }
    jnxQFX1000380cSlotCB         OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 2  }
    jnxQFX1000380cSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 3  }
    jnxQFX1000380cSlotFan        OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 4  }
    jnxQFX1000380cSlotFPM        OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 5  }
    jnxQFX1000380cSlotSIB        OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 6  }
    jnxQFX1000380cSlotPIC        OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 7  }
    jnxQFX1000380cSlotPDU        OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 8  }
    jnxQFX1000380cSlotPSM        OBJECT IDENTIFIER ::= { jnxSlotQFX1000380c 9  }

  jnxModuleQFX1000380c           OBJECT IDENTIFIER ::= { jnxModule    525 }
    jnxQFX1000380cRE             OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   1  }
    jnxQFX1000380cCB             OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   2  }
    jnxQFX1000380cFPC            OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   3  }
    jnxQFX1000380cFan            OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   4  }
    jnxQFX1000380cFPM            OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   5  }
    jnxQFX1000380cSIB            OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   6  }
    jnxQFX1000380cPIC            OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   7  }
    jnxQFX1000380cPDU            OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   8  }
    jnxQFX1000380cPSM            OBJECT IDENTIFIER ::= { jnxModuleQFX1000380c   9  }

-- Platform: QFX10003-160c - Brackla
--

  jnxProductLineQFX10003160c      OBJECT IDENTIFIER ::= { jnxProductLine      526 }
  jnxProductNameQFX10003160c      OBJECT IDENTIFIER ::= { jnxProductName      526 }
  jnxProductModelQFX10003160c     OBJECT IDENTIFIER ::= { jnxProductModel     526 }
  jnxProductVariationQFX10003160c OBJECT IDENTIFIER ::= { jnxProductVariation 526 }
  jnxChassisQFX10003160c          OBJECT IDENTIFIER ::= { jnxChassis          526 }

  jnxSlotQFX10003160c             OBJECT IDENTIFIER ::= { jnxSlot             526 }
    jnxQFX10003160cSlotRE         OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 1  }
    jnxQFX10003160cSlotCB         OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 2  }
    jnxQFX10003160cSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 3  }
    jnxQFX10003160cSlotFan        OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 4  }
    jnxQFX10003160cSlotFPM        OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 5  }
    jnxQFX10003160cSlotSIB        OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 6  }
    jnxQFX10003160cSlotPIC        OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 7  }
    jnxQFX10003160cSlotPDU        OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 8  }
    jnxQFX10003160cSlotPSM        OBJECT IDENTIFIER ::= { jnxSlotQFX10003160c 9  }

  jnxModuleQFX10003160c           OBJECT IDENTIFIER ::= { jnxModule    526 }
    jnxQFX10003160cRE             OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   1  }
    jnxQFX10003160cCB             OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   2  }
    jnxQFX10003160cFPC            OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   3  }
    jnxQFX10003160cFan            OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   4  }
    jnxQFX10003160cFPM            OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   5  }
    jnxQFX10003160cSIB            OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   6  }
    jnxQFX10003160cPIC            OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   7  }
    jnxQFX10003160cPDU            OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   8  }
    jnxQFX10003160cPSM            OBJECT IDENTIFIER ::= { jnxModuleQFX10003160c   9  }

-- Platform: ACX5448-M
-- I2C_ID_ACX5448_M_44X10GE_SFP_MACSEC_MIC
-- I2C_ID_ACX5448_M_6X100GE_QSFP_MIC
    jnxPicACX5448M44X10GESFPMIC             OBJECT IDENTIFIER ::= { jnxPic 527 }
    jnxPicACX5448M6X100GEQSFPMIC            OBJECT IDENTIFIER ::= { jnxPic 528 }

-- Platform: ACX5448-D
-- I2C_ID_ACX5448_D_36X10GE_SFP_MIC
-- I2C_ID_ACX5448_D_2X100GE_QSFP_MIC
-- I2C_ID_ACX5448_D_2X200GE_CFP2_DCO_MIC
    jnxPicACX5448D36X10GESFPMIC             OBJECT IDENTIFIER ::= { jnxPic 529 }
    jnxPicACX5448D2X100GEQSFPMIC            OBJECT IDENTIFIER ::= { jnxPic 530 }
    jnxPicACX5448D2X200GECFP2DCOMIC         OBJECT IDENTIFIER ::= { jnxPic 531 }

-- Platform: AS7816-64C
-- I2C_ID_AS7816_64X_CHASSIS
--
    jnxPicAS781664X             OBJECT IDENTIFIER ::= { jnxPic 532 }

-- Platform: QFX5120-32C
-- I2C_ID_AS7726_32X_CHASSIS
    jnxPicQFX512032C            OBJECT IDENTIFIER ::= { jnxPic 533 }

-- Platform: SRX5K
-- I2C_ID_IOC4_6XQSFPP_PIC
-- I2C_ID_IOC4_20X10GE_SFPP_PIC
      jnxPicIOCIV6XQSFPP       OBJECT IDENTIFIER ::= { jnxPic 534 }
      jnxPicIOCIV20X10GESFPP   OBJECT IDENTIFIER ::= { jnxPic 535 }

-- Platform: MX
-- I2C_ID_MX_SPC3_PIC
   jnxPicMXSPC3PIC            OBJECT IDENTIFIER ::= { jnxPic 536 }

-- Platform: PTX
-- I2C_ID_JNP10K_36QDD_DLC_PIC
   jnxPicJnp10k36xQDDPIC      OBJECT IDENTIFIER ::= { jnxPic 537 }

-- Platform: Rocket
-- I2C_ID_JSRXNLE_WAP_US_PIC
-- I2C_ID_JSRXNLE_WAP_IS_PIC
-- I2C_ID_JSRXNLE_WAP_WW_PIC
      jnxPicSRXSMEWAPUSPIC          OBJECT IDENTIFIER ::= { jnxPic 538 }
      jnxPicSRXSMEWAPISPIC          OBJECT IDENTIFIER ::= { jnxPic 539 }
      jnxPicSRXSMEWAPWWPIC          OBJECT IDENTIFIER ::= { jnxPic 540 }

-- Platform: ACX
-- I2C_ID_ACX710_24X10GE_SFP_MIC
-- I2C_ID_ACX710_4X100GE_SFP_MIC
-- I2C_ID_ACX5800_48X10GE_SFP_MIC
-- I2C_ID_ACX5800_32X10GE_SFP_MIC
-- I2C_ID_ACX5800_4X100GE_SFP_MIC

-- I2C_ID_R6675_24X10GE_SFP_MIC
-- I2C_ID_R6675_4X100GE_SFP_MIC
-- I2C_ID_R6274_48X10GE_SFP_MIC
-- I2C_ID_R6274_32X10GE_SFP_MIC
-- I2C_ID_R6274_4X100GE_SFP_MIC

   jnxPicACX71024X10GESFPMIC      OBJECT IDENTIFIER ::= { jnxPic 541 }
   jnxPicACX7104X100GESFPMIC      OBJECT IDENTIFIER ::= { jnxPic 542 }
   jnxPicACX580048X10GESFPMIC      OBJECT IDENTIFIER ::= { jnxPic 543 }
   jnxPicACX580032X10GESFPMIC      OBJECT IDENTIFIER ::= { jnxPic 544 }
   jnxPicACX58004X100GESFPMIC      OBJECT IDENTIFIER ::= { jnxPic 545 }
   jnxPicR667524X10GESFPMIC        OBJECT IDENTIFIER ::= { jnxPic 546 }
   jnxPicR66754X100GESFPMIC        OBJECT IDENTIFIER ::= { jnxPic 547 }
   jnxPicR627448X10GESFPMIC        OBJECT IDENTIFIER ::= { jnxPic 548 }
   jnxPicR627432X10GESFPMIC        OBJECT IDENTIFIER ::= { jnxPic 549 }
   jnxPicR62744X100GESFPMIC        OBJECT IDENTIFIER ::= { jnxPic 550 }


--
-- EX4400 Lagavulin
--
  jnxProductLineEX4400      OBJECT IDENTIFIER ::= { jnxProductLine      165 }
  jnxProductNameEX4400      OBJECT IDENTIFIER ::= { jnxProductName      165 }
  jnxProductModelEX4400     OBJECT IDENTIFIER ::= { jnxProductModel     165 }
  jnxProductVariationEX4400 OBJECT IDENTIFIER ::= { jnxProductVariation 165 }
    jnxProductEX4400port48MP    OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 1 }
    jnxProductEX4400port24MP    OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 2 }
    jnxProductEX4400port48P     OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 3 }
    jnxProductEX4400port24P     OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 4 }
    jnxProductEX4400port48T     OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 5 }
    jnxProductEX4400port24T     OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 6 }
    jnxProductEX4400port48F     OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 7 }
    jnxProductEX4400port24X     OBJECT IDENTIFIER ::= { jnxProductVariationEX4400 8 }

  jnxChassisEX4400          OBJECT IDENTIFIER ::= { jnxChassis          165 }
    jnxEX4400RE0            OBJECT IDENTIFIER ::= { jnxChassisEX4400 1  }
    jnxEX4400RE1            OBJECT IDENTIFIER ::= { jnxChassisEX4400 2  }
  jnxSlotEX4400             OBJECT IDENTIFIER ::= { jnxSlot             165 }
    jnxEX4400SlotFPC        OBJECT IDENTIFIER ::= { jnxSlotEX4400 1 }
      jnxEX4400SlotPower    OBJECT IDENTIFIER ::= { jnxEX4400SlotFPC 1 }
      jnxEX4400SlotFan      OBJECT IDENTIFIER ::= { jnxEX4400SlotFPC 2 }

  jnxMediaCardSpaceEX4400      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    165 }
    jnxEX4400MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceEX4400 1 }

 jnxModuleEX4400            OBJECT IDENTIFIER ::= { jnxModule    165 }
    jnxEX4400FPC            OBJECT IDENTIFIER ::= { jnxModuleEX4400 1 }
      jnxEX4400Power        OBJECT IDENTIFIER ::= { jnxEX4400FPC 1 }
      jnxEX4400Fan          OBJECT IDENTIFIER ::= { jnxEX4400FPC 2 }

-- Platform: EX4400 Lagavulin
-- I2C_ID_EX4400_4x25GE_SFP28_PIC
-- I2C_ID_EX4400_4x10GE_SFPP_PIC
 jnxPicEX44004x25GESFP28PIC        OBJECT IDENTIFIER ::= { jnxPic 551 }
 jnxPicEX44004x10GESFPPPIC         OBJECT IDENTIFIER ::= { jnxPic 552 }

-- Platform: QFX5120
-- I2C_ID_ABSINTHE_48T6C
    jnxPicQFX512048T6C            OBJECT IDENTIFIER ::= { jnxPic 553 }

-- Platform: PTX
-- I2C_ID_ARDBEG_CB_LOGICAL_PIC
    jnxPicArdbegPIC             OBJECT IDENTIFIER ::= { jnxPic 554 }

-- Platform: PTX10001-36mr - Ardbeg
--

  jnxProductLinePTX1000136mr      OBJECT IDENTIFIER ::= { jnxProductLine      555 }
  jnxProductNamePTX1000136mr      OBJECT IDENTIFIER ::= { jnxProductName      555 }
  jnxProductModelPTX1000136mr     OBJECT IDENTIFIER ::= { jnxProductModel     555 }
  jnxProductVariationPTX1000136mr OBJECT IDENTIFIER ::= { jnxProductVariation 555 }
  jnxChassisPTX1000136mr          OBJECT IDENTIFIER ::= { jnxChassis          555 }

  jnxSlotPTX1000136mr             OBJECT IDENTIFIER ::= { jnxSlot             555 }
    jnxPTX1000136mrSlotRE         OBJECT IDENTIFIER ::= { jnxSlotPTX1000136mr 1  }
    jnxPTX1000136mrSlotCB         OBJECT IDENTIFIER ::= { jnxSlotPTX1000136mr 2  }
    jnxPTX1000136mrSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotPTX1000136mr 3  }
    jnxPTX1000136mrSlotFan        OBJECT IDENTIFIER ::= { jnxSlotPTX1000136mr 4  }
    jnxPTX1000136mrSlotSIB        OBJECT IDENTIFIER ::= { jnxSlotPTX1000136mr 5  }
    jnxPTX1000136mrSlotPIC        OBJECT IDENTIFIER ::= { jnxSlotPTX1000136mr 6  }
    jnxPTX1000136mrSlotPSM        OBJECT IDENTIFIER ::= { jnxSlotPTX1000136mr 7  }

  jnxModulePTX1000136mr          OBJECT IDENTIFIER ::= { jnxModule    555 }
    jnxPTX1000136mrRE             OBJECT IDENTIFIER ::= { jnxModulePTX1000136mr   1  }
    jnxPTX1000136mrCB             OBJECT IDENTIFIER ::= { jnxModulePTX1000136mr   2  }
    jnxPTX1000136mrFPC            OBJECT IDENTIFIER ::= { jnxModulePTX1000136mr   3  }
    jnxPTX1000136mrFan            OBJECT IDENTIFIER ::= { jnxModulePTX1000136mr   4  }
    jnxPTX1000136mrSIB            OBJECT IDENTIFIER ::= { jnxModulePTX1000136mr   5  }
    jnxPTX1000136mrPIC            OBJECT IDENTIFIER ::= { jnxModulePTX1000136mr   6  }
    jnxPTX1000136mrPSM            OBJECT IDENTIFIER ::= { jnxModulePTX1000136mr   7  }

-- Platform: PTX10008
-- I2C_ID_JNP10K_4Q56DD_32Q28_LZ_LC_PIC
    jnxPicJnp10k4xQDD32xQ       OBJECT IDENTIFIER ::= { jnxPic 556 }

-- Platform: EX9200-15C
-- I2C_ID_EX9200_MPC10_5XQSFPP_PIC
      jnxPicEXMRATE5XQSFPP              OBJECT IDENTIFIER ::= { jnxPic 557 }

-- Platform: QFX5130
-- I2C_ID_SPECTROLITE_32CD_PIC I2C_ID_SPECTROLITE_48C_PIC
    jnxPicQFX513032CDPIC             OBJECT IDENTIFIER ::= { jnxPic 558 }
    jnxPicQFX513048CPIC              OBJECT IDENTIFIER ::= { jnxPic 559 }

-- Platform: ACX753
-- I2C_ID_BOLAN_8X25GE_SFP28_MIC
-- I2C_ID_BOLAN_2X100GE_QSFP28_MIC
-- I2C_ID_BOLAN_4X100GE_QSFP28_MIC
-- I2C_ID_BOLAN_8X10GE_SFP_MIC
    jnxPicACX7538x25GESFP28PIC             OBJECT IDENTIFIER ::= { jnxPic 560 }
    jnxPicACX7532x100GEQSFP28PIC           OBJECT IDENTIFIER ::= { jnxPic 561 }
    jnxPicACX7534x100GEQSFP28PIC           OBJECT IDENTIFIER ::= { jnxPic 562 }
    jnxPicACX7538x10GESFPPIC               OBJECT IDENTIFIER ::= { jnxPic 563 }

-- Platform: QFX7100-48L ACX7100-48L ACX7100-32C
-- I2C_ID_XMEN_JERRICO2_PIC
-- I2C_ID_XMEN_WOLVERINE_PIC
-- I2C_ID_XMEN_STORM_PIC
-- I2C_ID_XMEN_NIGHTCRAWLER_PIC
    jnxPicJerrico2PIC                      OBJECT IDENTIFIER ::= { jnxPic 564 }
    jnxPicJNP710048LPIC                    OBJECT IDENTIFIER ::= { jnxPic 565 }
    jnxPicACX710032CPIC                    OBJECT IDENTIFIER ::= { jnxPic 566 }
    jnxPicJNP710080PIC                     OBJECT IDENTIFIER ::= { jnxPic 567 }

-- Platform: MX10008
-- I2C_ID_DANIEL_24xSFPP_1G10G_PIC
    jnxPicDaniel24xSFPP1G10GPIC            OBJECT IDENTIFIER ::= { jnxPic 568 }

-- Platform: QFX5120
-- I2C_ID_PYRITE_48YM8C
    jnxPicQFX512048YM8C          OBJECT IDENTIFIER ::= { jnxPic 569}

-- Platform: MX10008
-- I2C_ID_ALFAROMEO_4XQDD_PIC
    jnxPicLC96004xQDDPIC                OBJECT IDENTIFIER ::= { jnxPic 570 }

-- Platform: PTX10004
--
  jnxProductLinePTX10004       OBJECT IDENTIFIER ::= { jnxProductLine      570 }
  jnxProductNamePTX10004       OBJECT IDENTIFIER ::= { jnxProductName      570 }
  jnxProductModelPTX10004      OBJECT IDENTIFIER ::= { jnxProductModel     570 }
  jnxProductVariationPTX10004  OBJECT IDENTIFIER ::= { jnxProductVariation 570 }
  jnxChassisPTX10004           OBJECT IDENTIFIER ::= { jnxChassis          570 }
  jnxSlotPTX10004              OBJECT IDENTIFIER ::= { jnxSlot             570 }
    jnxPTX10004SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotPTX10004   1 }
    jnxPTX10004SlotHM          OBJECT IDENTIFIER ::= { jnxSlotPTX10004   2 }
    jnxPTX10004SlotPower       OBJECT IDENTIFIER ::= { jnxSlotPTX10004   3 }
    jnxPTX10004SlotFan         OBJECT IDENTIFIER ::= { jnxSlotPTX10004   4 }
    jnxPTX10004SlotFPB         OBJECT IDENTIFIER ::= { jnxSlotPTX10004   5 }
    jnxPTX10004SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotPTX10004   6 }
    jnxPTX10004SlotSIB         OBJECT IDENTIFIER ::= { jnxSlotPTX10004   7 }
    jnxPTX10004SlotFPM         OBJECT IDENTIFIER ::= { jnxSlotPTX10004   8 }
    jnxPTX10004SlotFTC         OBJECT IDENTIFIER ::= { jnxSlotPTX10004   9 }
    jnxPTX10004SlotBackplane   OBJECT IDENTIFIER ::= { jnxSlotPTX10004   10 }
  jnxMediaCardSpacePTX10004    OBJECT IDENTIFIER ::= { jnxMediaCardSpace   570 }
    jnxPTX10004MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpacePTX10004 1 }

  jnxModulePTX10004              OBJECT IDENTIFIER ::= { jnxModule             570 }
    jnxPTX10004FPC         OBJECT IDENTIFIER ::= { jnxModulePTX10004   1 }
    jnxPTX10004HM          OBJECT IDENTIFIER ::= { jnxModulePTX10004   2 }
    jnxPTX10004Power       OBJECT IDENTIFIER ::= { jnxModulePTX10004   3 }
    jnxPTX10004Fan         OBJECT IDENTIFIER ::= { jnxModulePTX10004   4 }
    jnxPTX10004FPB         OBJECT IDENTIFIER ::= { jnxModulePTX10004   5 }
    jnxPTX10004CBD         OBJECT IDENTIFIER ::= { jnxModulePTX10004   6 }
    jnxPTX10004SIB         OBJECT IDENTIFIER ::= { jnxModulePTX10004   7 }
    jnxPTX10004FPM         OBJECT IDENTIFIER ::= { jnxModulePTX10004   8 }
    jnxPTX10004FTC         OBJECT IDENTIFIER ::= { jnxModulePTX10004   9 }
    jnxPTX10004Backplane   OBJECT IDENTIFIER ::= { jnxModulePTX10004   10 }

-- Platform: QFX5700
-- I2C_ID_HILLSIDE_16x100GE_QSFP28_PIC
-- I2C_ID_HILLSIDE_4x400GE_QSFP56DD_PIC
-- I2C_ID_GUARDIAN_20x1GE_10GE_25GE_50GE_SFP28_PIC
    jnxPicQFX570016x100QSFP28PIC            OBJECT IDENTIFIER ::= { jnxPic 571}
    jnxPicQFX57004x400QSFP56DDPIC           OBJECT IDENTIFIER ::= { jnxPic 572}
    jnxPicACX75520x1G10G25G50GSFP28PIC      OBJECT IDENTIFIER ::= { jnxPic 573}

-- Platform: QFX7100-48L ACX7100-48L ACX7100-32C
-- I2C_ID_XMEN_WOLVERINE_PSM_PIC
-- I2C_ID_XMEN_STORM_PSM_PIC
    jnxPicJNP710048LPSMPIC                     OBJECT IDENTIFIER ::= { jnxPic 574 }
    jnxPicACX710032CPSMPIC                     OBJECT IDENTIFIER ::= { jnxPic 575 }

--
-- ACX753
--
  jnxProductLineACX753       OBJECT IDENTIFIER ::= { jnxProductLine      576 }
  jnxProductNameACX753       OBJECT IDENTIFIER ::= { jnxProductName      576 }
  jnxProductModelACX753      OBJECT IDENTIFIER ::= { jnxProductModel     576 }
  jnxProductVariationACX753  OBJECT IDENTIFIER ::= { jnxProductVariation 576 }
  jnxChassisACX753           OBJECT IDENTIFIER ::= { jnxChassis          576 }

  jnxSlotACX753              OBJECT IDENTIFIER ::= { jnxSlot             576 }
    jnxACX753SlotRE          OBJECT IDENTIFIER ::= { jnxSlotACX753   1 }
    jnxACX753SlotPSM         OBJECT IDENTIFIER ::= { jnxSlotACX753   2 }
    jnxACX753SlotFan         OBJECT IDENTIFIER ::= { jnxSlotACX753   3 }
    jnxACX753SlotCBD         OBJECT IDENTIFIER ::= { jnxSlotACX753   4 }
    jnxACX753SlotBackplane   OBJECT IDENTIFIER ::= { jnxSlotACX753   5 }
    jnxACX753SlotFPC         OBJECT IDENTIFIER ::= { jnxSlotACX753   6 }
    jnxACX753SlotPIC         OBJECT IDENTIFIER ::= { jnxSlotACX753   7 }
    jnxACX753SlotFEB         OBJECT IDENTIFIER ::= { jnxSlotACX753   8 }

  jnxModuleACX753              OBJECT IDENTIFIER ::= { jnxModule             576 }
    jnxACX753RE          OBJECT IDENTIFIER ::= { jnxModuleACX753   1 }
    jnxACX753PSM         OBJECT IDENTIFIER ::= { jnxModuleACX753   2 }
    jnxACX753Fan         OBJECT IDENTIFIER ::= { jnxModuleACX753   3 }
    jnxACX753CBD         OBJECT IDENTIFIER ::= { jnxModuleACX753   4 }
    jnxACX753Backplane   OBJECT IDENTIFIER ::= { jnxModuleACX753   5 }
    jnxACX753FPC         OBJECT IDENTIFIER ::= { jnxModuleACX753   6 }
    jnxACX753PIC         OBJECT IDENTIFIER ::= { jnxModuleACX753   7 }
    jnxACX753FEB         OBJECT IDENTIFIER ::= { jnxModuleACX753   8 }
-- endif

--
-- SRX1800
--
  jnxProductLineSRX1800       OBJECT IDENTIFIER ::= { jnxProductLine  577 }
  jnxProductNameSRX1800       OBJECT IDENTIFIER ::= { jnxProductName  577 }
  jnxProductModelSRX1800      OBJECT IDENTIFIER ::= { jnxProductModel 577 }
  jnxChassisSRX1800           OBJECT IDENTIFIER ::= { jnxChassis      577 }
  jnxSlotSRX1800              OBJECT IDENTIFIER ::= { jnxSlot         577 }
    jnxSRX1800SlotFPC          OBJECT IDENTIFIER ::= { jnxSlotSRX1800   1  }
    jnxSRX1800SlotPIC          OBJECT IDENTIFIER ::= { jnxSlotSRX1800   2  }
    jnxSRX1800SlotHM           OBJECT IDENTIFIER ::= { jnxSlotSRX1800   3  }
    jnxSRX1800SlotPower        OBJECT IDENTIFIER ::= { jnxSlotSRX1800   4  }
    jnxSRX1800SlotFan          OBJECT IDENTIFIER ::= { jnxSlotSRX1800   5  }


-- I2C_ID_SRX_LTE_AA
-- I2C_ID_SRX_LTE_AE
-- I2C_ID_SRX_EM_4T2SFP
    jnxPicSRXEMLTEAA             OBJECT IDENTIFIER ::= { jnxPic 576 }
    jnxPicSRXEMLTEAE             OBJECT IDENTIFIER ::= { jnxPic 577 }
    jnxPicSRXEM4T2SFP            OBJECT IDENTIFIER ::= { jnxPic 578 }

--
-- ACX7KSwitch
--

  jnxProductLineACX7KSwitch           OBJECT IDENTIFIER ::= { jnxProductLine      578 }
  jnxProductNameACX7KSwitch           OBJECT IDENTIFIER ::= { jnxProductName      578 }
  jnxProductModelACX7KSwitch          OBJECT IDENTIFIER ::= { jnxProductModel     578 }
  jnxProductVariationACX7KSwitch      OBJECT IDENTIFIER ::= { jnxProductVariation 578 }
    jnxProductACX710032C              OBJECT IDENTIFIER ::= { jnxProductVariationACX7KSwitch 1 }
    jnxProductACX710048L              OBJECT IDENTIFIER ::= { jnxProductVariationACX7KSwitch 2 }
    jnxProductACX7509                 OBJECT IDENTIFIER ::= { jnxProductVariationACX7KSwitch 3 }

  jnxChassisACX7KSwitch               OBJECT IDENTIFIER ::= { jnxChassis          578 }

  jnxSlotACX7KSwitch                  OBJECT IDENTIFIER ::= { jnxSlot             578 }
    jnxACX7KSwitchSlotFPC             OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  1 }
    jnxACX7KSwitchSlotHM              OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  2 }
    jnxACX7KSwitchSlotPower           OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  3 }
    jnxACX7KSwitchSlotFan             OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  4 }
    jnxACX7KSwitchSlotFPB             OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  5 }
    jnxACX7KSwitchSlotCBD             OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  6 }
    jnxACX7KSwitchSlotSIB             OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  7 }
    jnxACX7KSwitchSlotFEB             OBJECT IDENTIFIER ::= { jnxSlotACX7KSwitch  8 }

  jnxMediaCardSpaceACX7KSwitch        OBJECT IDENTIFIER ::= { jnxMediaCardSpace   578 }
    jnxACX7KSwitchMediaCardSpacePIC   OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX7KSwitch 1 }


-- Platform: ACX7509
-- I2C_ID_GUARDIAN_16x100GE_QSFP28_PIC
-- I2C_ID_GUARDIAN_4x400GE_QSFP56DD_PIC
    jnxPicACX75516x100QSFP28PIC            OBJECT IDENTIFIER ::= { jnxPic 579}
    jnxPicACX7554x400QSFP56DDPIC           OBJECT IDENTIFIER ::= { jnxPic 580}
-- endif

-- Platform: WB ONL
-- I2C_ID_WBONL_PIC
    jnxPicWBONLPIC               OBJECT IDENTIFIER ::= { jnxPic 581 }

-- Platform: Siege SRX380 Porter
-- I2C_ID_JSRXNLE_5G_MODEM_S6_GL_PIC
-- I2C_ID_JSRXNLE_5G_MODEM_S6MW_NA_PIC
-- I2C_ID_JSRXNLE_5G_MODEM_S6MW_EA_PIC
      jnxPic5GS6GLPIC         OBJECT IDENTIFIER ::= { jnxPic 582 }
      jnxPic5GS6MWNAPIC       OBJECT IDENTIFIER ::= { jnxPic 583 }
      jnxPic5GS6MWEAPIC       OBJECT IDENTIFIER ::= { jnxPic 584 }


-- Platform: SRX5K
-- I2C_ID_TABASCO_CRYPTO_SPC4_TYPE1_PIC
-- I2C_ID_TABASCO_CRYPTO_SPC4_TYPE2_PIC
-- I2C_ID_TABASCO_CRYPTO_SPC4_TYPE3_PIC
-- I2C_ID_TABASCO_CRYPTO_SPC4_MX_PIC
    jnxPicTabascoCryptoSPC4Type1PIC         OBJECT IDENTIFIER ::= { jnxPic 585 }
    jnxPicTabascoCryptoSPC4Type2PIC         OBJECT IDENTIFIER ::= { jnxPic 586 }
    jnxPicTabascoCryptoSPC4Type3PIC         OBJECT IDENTIFIER ::= { jnxPic 587 }
    jnxPicTabascoCryptoSPC4MXPIC            OBJECT IDENTIFIER ::= { jnxPic 588 }

--
-- ACX710032c
--
  jnxProductLineACX710032c            OBJECT IDENTIFIER ::= { jnxProductLine      579 }
  jnxProductNameACX710032c            OBJECT IDENTIFIER ::= { jnxProductName      579 }
  jnxProductModelACX710032c           OBJECT IDENTIFIER ::= { jnxProductModel     579 }
  jnxProductVariationACX710032c       OBJECT IDENTIFIER ::= { jnxProductVariation 579 }
  jnxChassisACX710032c                OBJECT IDENTIFIER ::= { jnxChassis          579 }

  jnxSlotACX710032c                   OBJECT IDENTIFIER ::= { jnxSlot             579 }
    jnxACX710032cSlotFPC              OBJECT IDENTIFIER ::= { jnxSlotACX710032c     1 }
    jnxACX710032cSlotHM               OBJECT IDENTIFIER ::= { jnxSlotACX710032c     2 }
    jnxACX710032cSlotPower            OBJECT IDENTIFIER ::= { jnxSlotACX710032c     3 }
    jnxACX710032cSlotFan              OBJECT IDENTIFIER ::= { jnxSlotACX710032c     4 }
    jnxACX710032cSlotFPB              OBJECT IDENTIFIER ::= { jnxSlotACX710032c     5 }
    jnxACX710032cSlotCBD              OBJECT IDENTIFIER ::= { jnxSlotACX710032c     6 }
    jnxACX710032cSlotSIB              OBJECT IDENTIFIER ::= { jnxSlotACX710032c     7 }
    jnxACX710032cSlotFEB              OBJECT IDENTIFIER ::= { jnxSlotACX710032c     8 }

  jnxMediaCardSpaceACX710032c         OBJECT IDENTIFIER ::= { jnxMediaCardSpace   579 }
    jnxACX710032cMediaCardSpacePIC    OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX710032c 1 }

--
-- ACX710048l
--
  jnxProductLineACX710048l            OBJECT IDENTIFIER ::= { jnxProductLine      580 }
  jnxProductNameACX710048l            OBJECT IDENTIFIER ::= { jnxProductName      580 }
  jnxProductModelACX710048l           OBJECT IDENTIFIER ::= { jnxProductModel     580 }
  jnxProductVariationACX710048l       OBJECT IDENTIFIER ::= { jnxProductVariation 580 }
  jnxChassisACX710048l                OBJECT IDENTIFIER ::= { jnxChassis          580 }

  jnxSlotACX710048l                   OBJECT IDENTIFIER ::= { jnxSlot             580 }
    jnxACX710048lSlotFPC              OBJECT IDENTIFIER ::= { jnxSlotACX710048l     1 }
    jnxACX710048lSlotHM               OBJECT IDENTIFIER ::= { jnxSlotACX710048l     2 }
    jnxACX710048lSlotPower            OBJECT IDENTIFIER ::= { jnxSlotACX710048l     3 }
    jnxACX710048lSlotFan              OBJECT IDENTIFIER ::= { jnxSlotACX710048l     4 }
    jnxACX710048lSlotFPB              OBJECT IDENTIFIER ::= { jnxSlotACX710048l     5 }
    jnxACX710048lSlotCBD              OBJECT IDENTIFIER ::= { jnxSlotACX710048l     6 }
    jnxACX710048lSlotSIB              OBJECT IDENTIFIER ::= { jnxSlotACX710048l     7 }
    jnxACX710048lSlotFEB              OBJECT IDENTIFIER ::= { jnxSlotACX710048l     8 }

  jnxMediaCardSpaceACX710048l         OBJECT IDENTIFIER ::= { jnxMediaCardSpace   580 }
    jnxACX710048lMediaCardSpacePIC    OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX710048l 1 }

-- Platform: EX4400 Lagavulin
-- I2C_ID_EX4400_2xQSFP28_PIC
  jnxPicEX44002xQSFP28PIC           OBJECT IDENTIFIER ::= { jnxPic 589 }

-- Platform: QFX5700
-- I2C_ID_HILLSIDE_20x10GE_25GE_50GE_SFP28_PIC
  jnxPicACX75520x10G25G50GSFP28PIC           OBJECT IDENTIFIER ::= { jnxPic 590 }

-- Platform: QFX5230-64CD
-- I2C_ID_TOMATIN_PIC
  jnxPicQFX523064CDPIC                       OBJECT IDENTIFIER ::= { jnxPic 591 }

--
-- ACX7908
--
  jnxProductLineACX7908            OBJECT IDENTIFIER ::= { jnxProductLine      581 }
  jnxProductNameACX7908            OBJECT IDENTIFIER ::= { jnxProductName      581 }
  jnxProductModelACX7908           OBJECT IDENTIFIER ::= { jnxProductModel     581 }
  jnxProductVariationACX7908       OBJECT IDENTIFIER ::= { jnxProductVariation 581 }
  jnxChassisACX7908                OBJECT IDENTIFIER ::= { jnxChassis          581 }
  jnxSlotACX7908                   OBJECT IDENTIFIER ::= { jnxSlot             581 }
    jnxACX7908SlotFPC              OBJECT IDENTIFIER ::= { jnxSlotACX7908     1 }
    jnxACX7908SlotHM               OBJECT IDENTIFIER ::= { jnxSlotACX7908     2 }
    jnxACX7908SlotPower            OBJECT IDENTIFIER ::= { jnxSlotACX7908     3 }
    jnxACX7908SlotFan              OBJECT IDENTIFIER ::= { jnxSlotACX7908     4 }
    jnxACX7908SlotCBD              OBJECT IDENTIFIER ::= { jnxSlotACX7908     5 }
    jnxACX7908SlotSIB              OBJECT IDENTIFIER ::= { jnxSlotACX7908     6 }
    jnxACX7908SlotFPM              OBJECT IDENTIFIER ::= { jnxSlotACX7908     7 }
    jnxACX7908SlotBackplane        OBJECT IDENTIFIER ::= { jnxSlotACX7908     8 }
  jnxMediaCardSpaceACX7908         OBJECT IDENTIFIER ::= { jnxMediaCardSpace   581 }
    jnxACX7908MediaCardSpacePIC    OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX7908 1 }

  jnxProductLineACX7024            OBJECT IDENTIFIER ::= { jnxProductLine      582 }
  jnxProductNameACX7024            OBJECT IDENTIFIER ::= { jnxProductName      582 }
  jnxProductModelACX7024           OBJECT IDENTIFIER ::= { jnxProductModel     582 }
  jnxProductVariationACX7024       OBJECT IDENTIFIER ::= { jnxProductVariation 582 }
  jnxChassisACX7024                OBJECT IDENTIFIER ::= { jnxChassis          582 }
  jnxSlotACX7024                   OBJECT IDENTIFIER ::= { jnxSlot             582 }
    jnxACX7024SlotFPC              OBJECT IDENTIFIER ::= { jnxSlotACX7024     1 }
    jnxACX7024SlotHM               OBJECT IDENTIFIER ::= { jnxSlotACX7024     2 }
    jnxACX7024SlotPower            OBJECT IDENTIFIER ::= { jnxSlotACX7024     3 }
    jnxACX7024SlotFan              OBJECT IDENTIFIER ::= { jnxSlotACX7024     4 }
    jnxACX7024SlotFPB              OBJECT IDENTIFIER ::= { jnxSlotACX7024     5 }
    jnxACX7024SlotCBD              OBJECT IDENTIFIER ::= { jnxSlotACX7024     6 }
    jnxACX7024SlotSIB              OBJECT IDENTIFIER ::= { jnxSlotACX7024     7 }
    jnxACX7024SlotFEB              OBJECT IDENTIFIER ::= { jnxSlotACX7024     8 }
  jnxMediaCardSpaceACX7024         OBJECT IDENTIFIER ::= { jnxMediaCardSpace   582 }
    jnxACX7024MediaCardSpacePIC    OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX7024 1 }

-- Platform: MX304-PIC
-- I2C_ID_MX304_PIC
    jnxPicMX304PIC                 OBJECT IDENTIFIER ::= { jnxPic 592 }
--

-- EX4100 PICs
--
      jnxPicEX4100UplinkSFPPlus4Port  OBJECT IDENTIFIER ::= { jnxPic 593 }
      jnxPicEX4100UplinkSFPPlus2Port  OBJECT IDENTIFIER ::= { jnxPic 594 }
      jnxPicEX4100VCSFP284Port        OBJECT IDENTIFIER ::= { jnxPic 595 }

-- Platform: SRX5K
-- I2C_ID_IOC5_5XQSFPP_PIC
      jnxPicIOCV5XQSFPP       OBJECT IDENTIFIER ::= { jnxPic 596 }

-- Platform: QFX5130-48C
-- I2C_ID_GLENLIVET_PIC
    jnxPicGLQFX513048CPIC              OBJECT IDENTIFIER ::= { jnxPic 598 }

-- Platform: EX4400
-- I2C_ID_EX4400_1xQSFP28_PIC
 jnxPicEX44001xQSFP28PIC         OBJECT IDENTIFIER ::= { jnxPic 597 }


-- Platform: MX10008 MX10004
-- I2C_ID_LC4800_2xQSFPDD_12xSFPDD_PIC
-- I2C_ID_LC4800_16xSFPDD_PIC
-- I2C_ID_LC4800_4xQSFPDD_8xQSFP_PIC
    jnxPicLC48002xQDD12xSDDPIC                OBJECT IDENTIFIER ::= { jnxPic 600 }
    jnxPicLC480016xSDDPIC                     OBJECT IDENTIFIER ::= { jnxPic 601 }
    jnxPicLC48004xQDD8xQPIC                   OBJECT IDENTIFIER ::= { jnxPic 602 }


-- Platform: SRX1600
  jnxProductLineSRX1600         OBJECT IDENTIFIER ::= { jnxProductLine  583 }
  jnxProductNameSRX1600         OBJECT IDENTIFIER ::= { jnxProductName  583 }
  jnxChassisSRX1600             OBJECT IDENTIFIER ::= { jnxChassis      583 }
  jnxSlotSRX1600                OBJECT IDENTIFIER ::= { jnxSlot         583 }
    jnxSRX1600SlotFPC           OBJECT IDENTIFIER ::= { jnxSlotSRX1600   1  }
    jnxSRX1600SlotRE            OBJECT IDENTIFIER ::= { jnxSlotSRX1600   2  }
    jnxSRX1600SlotPower         OBJECT IDENTIFIER ::= { jnxSlotSRX1600   3  }
    jnxSRX1600SlotFan           OBJECT IDENTIFIER ::= { jnxSlotSRX1600   4  }
    jnxSRX1600SlotCBD           OBJECT IDENTIFIER ::= { jnxSlotSRX1600   5  }

  jnxMediaCardSpaceSRX1600      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    583 }
    jnxSRX1600MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX1600 1 }

  jnxMidplaneSRX1600            OBJECT IDENTIFIER ::= { jnxBackplane    583 }

  jnxModuleSRX1600              OBJECT IDENTIFIER ::= { jnxModule       583 }
    jnxSRX1600FPC               OBJECT IDENTIFIER ::= { jnxModuleSRX1600   1  }
    jnxSRX1600RE                OBJECT IDENTIFIER ::= { jnxModuleSRX1600   2  }
    jnxSRX1600Power             OBJECT IDENTIFIER ::= { jnxModuleSRX1600   3  }
    jnxSRX1600Fan               OBJECT IDENTIFIER ::= { jnxModuleSRX1600   4  }
    jnxSRX1600CBD               OBJECT IDENTIFIER ::= { jnxModuleSRX1600   5  }

-- Platform: SRX2300
  jnxProductLineSRX2300         OBJECT IDENTIFIER ::= { jnxProductLine  584 }
  jnxProductNameSRX2300         OBJECT IDENTIFIER ::= { jnxProductName  584 }
  jnxChassisSRX2300             OBJECT IDENTIFIER ::= { jnxChassis      584 }
  jnxSlotSRX2300                OBJECT IDENTIFIER ::= { jnxSlot         584 }
    jnxSRX2300SlotFPC           OBJECT IDENTIFIER ::= { jnxSlotSRX2300   1  }
    jnxSRX2300SlotRE            OBJECT IDENTIFIER ::= { jnxSlotSRX2300   2  }
    jnxSRX2300SlotPower         OBJECT IDENTIFIER ::= { jnxSlotSRX2300   3  }
    jnxSRX2300SlotFan           OBJECT IDENTIFIER ::= { jnxSlotSRX2300   4  }
    jnxSRX2300SlotCBD           OBJECT IDENTIFIER ::= { jnxSlotSRX2300   5  }

  jnxMediaCardSpaceSRX2300      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    584 }
    jnxSRX2300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX2300 1 }

  jnxMidplaneSRX2300            OBJECT IDENTIFIER ::= { jnxBackplane    584 }

  jnxModuleSRX2300              OBJECT IDENTIFIER ::= { jnxModule       584 }
    jnxSRX2300FPC               OBJECT IDENTIFIER ::= { jnxModuleSRX2300   1  }
    jnxSRX2300RE                OBJECT IDENTIFIER ::= { jnxModuleSRX2300   2  }
    jnxSRX2300Power             OBJECT IDENTIFIER ::= { jnxModuleSRX2300   3  }
    jnxSRX2300Fan               OBJECT IDENTIFIER ::= { jnxModuleSRX2300   4  }
    jnxSRX2300CBD               OBJECT IDENTIFIER ::= { jnxModuleSRX2300   5  }

-- Platform: SRX4300
  jnxProductLineSRX4300         OBJECT IDENTIFIER ::= { jnxProductLine  585 }
  jnxProductNameSRX4300         OBJECT IDENTIFIER ::= { jnxProductName  585 }
  jnxChassisSRX4300             OBJECT IDENTIFIER ::= { jnxChassis      585 }
  jnxSlotSRX4300                OBJECT IDENTIFIER ::= { jnxSlot         585 }
    jnxSRX4300SlotFPC           OBJECT IDENTIFIER ::= { jnxSlotSRX4300   1  }
    jnxSRX4300SlotRE            OBJECT IDENTIFIER ::= { jnxSlotSRX4300   2  }
    jnxSRX4300SlotPower         OBJECT IDENTIFIER ::= { jnxSlotSRX4300   3  }
    jnxSRX4300SlotFan           OBJECT IDENTIFIER ::= { jnxSlotSRX4300   4  }
    jnxSRX4300SlotCBD           OBJECT IDENTIFIER ::= { jnxSlotSRX4300   5  }

  jnxMediaCardSpaceSRX4300      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    585 }
    jnxSRX4300MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX4300 1 }

  jnxMidplaneSRX4300            OBJECT IDENTIFIER ::= { jnxBackplane    585 }

  jnxModuleSRX4300              OBJECT IDENTIFIER ::= { jnxModule       585 }
    jnxSRX4300FPC               OBJECT IDENTIFIER ::= { jnxModuleSRX4300   1  }
    jnxSRX4300RE                OBJECT IDENTIFIER ::= { jnxModuleSRX4300   2  }
    jnxSRX4300Power             OBJECT IDENTIFIER ::= { jnxModuleSRX4300   3  }
    jnxSRX4300Fan               OBJECT IDENTIFIER ::= { jnxModuleSRX4300   4  }
    jnxSRX4300CBD               OBJECT IDENTIFIER ::= { jnxModuleSRX4300   5  }

-- Platform: QFX5130-48CM
-- I2C_ID_GLENLIVET_MACSEC_PIC
    jnxPicGLQFX513048CMPIC              OBJECT IDENTIFIER ::= { jnxPic 603 }

-- Platform: QFX5130E-32CD
-- I2C_ID_GLENDRONACH_LOGICAL_PIC
    jnxPicGLDQFX5130E32CDPIC            OBJECT IDENTIFIER ::= { jnxPic 604 }

-- Platform: SRX1600
-- I2C_ID_SRX1600_PIC
-- I2C_ID_SRX1600_2xSFP28_PIC
-- I2C_ID_SRX1600_4xSFP_PIC
    jnxPicSRX160016x1GT                OBJECT IDENTIFIER ::= { jnxPic 605 }
    jnxPicSRX16002xSFP28               OBJECT IDENTIFIER ::= { jnxPic 606 }
    jnxPicSRX16004xSFP                 OBJECT IDENTIFIER ::= { jnxPic 607 }

-- Platform: SRX2300
-- I2C_ID_SRX2300_PIC
-- I2C_ID_SRX2300_8xSFP_PIC
-- I2C_ID_SRX2300_4xSFP28_PIC
-- I2C_ID_SRX2300_2xQSFP28_PIC
    jnxPicSRX23008x10GT                OBJECT IDENTIFIER ::= { jnxPic 608 }
    jnxPicSRX23008xSFP                 OBJECT IDENTIFIER ::= { jnxPic 609 }
    jnxPicSRX23004xSFP28               OBJECT IDENTIFIER ::= { jnxPic 610 }
    jnxPicSRX23002QSFP28               OBJECT IDENTIFIER ::= { jnxPic 611 }

-- Platform: SRX4300
-- I2C_ID_SRX4300_PIC
-- I2C_ID_SRX4300_8xSFP_PIC
-- I2C_ID_SRX4300_4xSFP28_PIC
-- I2C_ID_SRX4300_2xQSFP28_PIC
    jnxPicSRX43008x10GT                OBJECT IDENTIFIER ::= { jnxPic 612 }
    jnxPicSRX43008xSFP                 OBJECT IDENTIFIER ::= { jnxPic 613 }
    jnxPicSRX43004xSFP28               OBJECT IDENTIFIER ::= { jnxPic 614 }
    jnxPicSRX43006QSFP28               OBJECT IDENTIFIER ::= { jnxPic 615 }

-- Platform: QFX5240_64xOSFP
-- I2C_ID_GARNET_64xOSFP_PIC
    jnxPicQFX524064OSFPPIC             OBJECT IDENTIFIER ::= { jnxPic 616 }
-- I2C_ID_GARNET_64xOSFPDD_PIC
    jnxPicQFX524064QSFPDDPIC           OBJECT IDENTIFIER ::= { jnxPic 617 }

-- Platform: SRX4700
-- I2C_ID_SRX4700_1xQSFPDD_5xQSFP28_8xSFP56_PIC
    jnxPicSRX47001xQSFPDD5xQSFP288xSFP56     OBJECT IDENTIFIER ::= { jnxPic 618 }
    -- I2C_ID_SRX4700_FLOW_PIC
    jnxPicSRX4700FlowPIC                     OBJECT IDENTIFIER ::= {jnxPic  619 }

-- EX4100-H PICs
    jnxPicEX4100VCSFPSFPPlus2Port      OBJECT IDENTIFIER ::= { jnxPic 620 }
    jnxPicEX4100VCSFPSFPPlus4Port      OBJECT IDENTIFIER ::= { jnxPic 621 }
--

-- Platform: ACX7332
  jnxProductLineACX7332         OBJECT IDENTIFIER ::= { jnxProductLine      586 }
  jnxProductNameACX7332         OBJECT IDENTIFIER ::= { jnxProductName      586 }
  jnxProductModelACX7332        OBJECT IDENTIFIER ::= { jnxProductModel     586 }
  jnxProductVariationACX7332    OBJECT IDENTIFIER ::= { jnxProductVariation 586 }
  jnxChassisACX7332             OBJECT IDENTIFIER ::= { jnxChassis          586 }

  jnxSlotACX7332                OBJECT IDENTIFIER ::= { jnxSlot         586 }
    jnxACX7332SlotFPC           OBJECT IDENTIFIER ::= { jnxSlotACX7332   1  }
    jnxACX7332SlotRE            OBJECT IDENTIFIER ::= { jnxSlotACX7332   2  }
    jnxACX7332SlotPower         OBJECT IDENTIFIER ::= { jnxSlotACX7332   3  }
    jnxACX7332SlotFan           OBJECT IDENTIFIER ::= { jnxSlotACX7332   4  }
    jnxACX7332SlotFEB           OBJECT IDENTIFIER ::= { jnxSlotACX7332   5  }
    jnxACX7332SlotCBD           OBJECT IDENTIFIER ::= { jnxSlotACX7332   6  }


  jnxMediaCardSpaceACX7332      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    586 }
    jnxACX7332MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX7332 1 }

  jnxMidplaneACX7332            OBJECT IDENTIFIER ::= { jnxBackplane    586 }

  jnxModuleACX7332              OBJECT IDENTIFIER ::= { jnxModule       586 }
    jnxACX7332FPC               OBJECT IDENTIFIER ::= { jnxModuleACX7332   1  }
    jnxACX7332RE                OBJECT IDENTIFIER ::= { jnxModuleACX7332   2  }
    jnxACX7332Power             OBJECT IDENTIFIER ::= { jnxModuleACX7332   3  }
    jnxACX7332Fan               OBJECT IDENTIFIER ::= { jnxModuleACX7332   4  }
    jnxACX7332FEB               OBJECT IDENTIFIER ::= { jnxModuleACX7332   5  }
    jnxACX7332CBD               OBJECT IDENTIFIER ::= { jnxModuleACX7332   6  }

-- Platform: ACX7348
  jnxProductLineACX7348         OBJECT IDENTIFIER ::= { jnxProductLine      587 }
  jnxProductNameACX7348         OBJECT IDENTIFIER ::= { jnxProductName      587 }
  jnxProductModelACX7348        OBJECT IDENTIFIER ::= { jnxProductModel     587 }
  jnxProductVariationACX7348    OBJECT IDENTIFIER ::= { jnxProductVariation 587 }
  jnxChassisACX7348             OBJECT IDENTIFIER ::= { jnxChassis          587 }

  jnxSlotACX7348                OBJECT IDENTIFIER ::= { jnxSlot         587 }
    jnxACX7348SlotFPC           OBJECT IDENTIFIER ::= { jnxSlotACX7348   1  }
    jnxACX7348SlotRE            OBJECT IDENTIFIER ::= { jnxSlotACX7348   2  }
    jnxACX7348SlotPower         OBJECT IDENTIFIER ::= { jnxSlotACX7348   3  }
    jnxACX7348SlotFan           OBJECT IDENTIFIER ::= { jnxSlotACX7348   4  }
    jnxACX7348SlotFEB           OBJECT IDENTIFIER ::= { jnxSlotACX7348   5  }
    jnxACX7348SlotCBD           OBJECT IDENTIFIER ::= { jnxSlotACX7348   6  }


  jnxMediaCardSpaceACX7348      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    587 }
    jnxACX7348MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX7348 1 }

  jnxMidplaneACX7348            OBJECT IDENTIFIER ::= { jnxBackplane    587 }

  jnxModuleACX7348              OBJECT IDENTIFIER ::= { jnxModule       587 }
    jnxACX7348FPC               OBJECT IDENTIFIER ::= { jnxModuleACX7348   1  }
    jnxACX7348RE                OBJECT IDENTIFIER ::= { jnxModuleACX7348   2  }
    jnxACX7348Power             OBJECT IDENTIFIER ::= { jnxModuleACX7348   3  }
    jnxACX7348Fan               OBJECT IDENTIFIER ::= { jnxModuleACX7348   4  }
    jnxACX7348FEB               OBJECT IDENTIFIER ::= { jnxModuleACX7348   5  }
    jnxACX7348CBD               OBJECT IDENTIFIER ::= { jnxModuleACX7348   6  }

-- Platform: ACX7024X
  jnxProductLineACX7024X            OBJECT IDENTIFIER ::= { jnxProductLine      588 }
  jnxProductNameACX7024X            OBJECT IDENTIFIER ::= { jnxProductName      588 }
  jnxProductModelACX7024X           OBJECT IDENTIFIER ::= { jnxProductModel     588 }
  jnxProductVariationACX7024X       OBJECT IDENTIFIER ::= { jnxProductVariation 588 }
  jnxChassisACX7024X                OBJECT IDENTIFIER ::= { jnxChassis          588 }
  jnxSlotACX7024X                   OBJECT IDENTIFIER ::= { jnxSlot             588 }
    jnxACX7024XSlotFPC              OBJECT IDENTIFIER ::= { jnxSlotACX7024X     1 }
    jnxACX7024XSlotHM               OBJECT IDENTIFIER ::= { jnxSlotACX7024X     2 }
    jnxACX7024XSlotPower            OBJECT IDENTIFIER ::= { jnxSlotACX7024X     3 }
    jnxACX7024XSlotFan              OBJECT IDENTIFIER ::= { jnxSlotACX7024X     4 }
    jnxACX7024XSlotFPB              OBJECT IDENTIFIER ::= { jnxSlotACX7024X     5 }
    jnxACX7024XSlotCBD              OBJECT IDENTIFIER ::= { jnxSlotACX7024X     6 }
    jnxACX7024XSlotSIB              OBJECT IDENTIFIER ::= { jnxSlotACX7024X     7 }
    jnxACX7024XSlotFEB              OBJECT IDENTIFIER ::= { jnxSlotACX7024X     8 }
    jnxMediaCardSpaceACX7024X       OBJECT IDENTIFIER ::= { jnxMediaCardSpace   588 }
    jnxACX7024XMediaCardSpacePIC    OBJECT IDENTIFIER ::= { jnxMediaCardSpaceACX7024X 1 }

-- Platform: PTX10002-36QDD - Balerion
--
  jnxProductLinePTX1000236qdd      OBJECT IDENTIFIER ::= { jnxProductLine      589 }
  jnxProductNamePTX1000236qdd      OBJECT IDENTIFIER ::= { jnxProductName      589 }
  jnxProductModelPTX1000236qdd     OBJECT IDENTIFIER ::= { jnxProductModel     589 }
  jnxProductVariationPTX1000236qdd OBJECT IDENTIFIER ::= { jnxProductVariation 589 }
  jnxChassisPTX1000236qdd          OBJECT IDENTIFIER ::= { jnxChassis          589 }

  jnxSlotPTX1000236qdd             OBJECT IDENTIFIER ::= { jnxSlot             589 }
    jnxPTX1000236qddSlotRE         OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 1  }
    jnxPTX1000236qddSlotCB         OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 2  }
    jnxPTX1000236qddSlotFPC        OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 3  }
    jnxPTX1000236qddSlotFan        OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 4  }
    jnxPTX1000236qddSlotPIC        OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 5  }
    jnxPTX1000236qddSlotPSM        OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 6  }
    jnxPTX1000236qddSlotBackplane  OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 7  }
    jnxPTX1000236qddSlotFPM        OBJECT IDENTIFIER ::= { jnxSlotPTX1000236qdd 8  }

  jnxModulePTX1000236qdd           OBJECT IDENTIFIER ::= { jnxModule    589 }
    jnxPTX1000236qddRE             OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   1  }
    jnxPTX1000236qddCB             OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   2  }
    jnxPTX1000236qddFPC            OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   3  }
    jnxPTX1000236qddFan            OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   4  }
    jnxPTX1000236qddPIC            OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   5  }
    jnxPTX1000236qddPSM            OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   6  }
    jnxPTX1000236qddBackplane      OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   7  }
    jnxPTX1000236qddFPM            OBJECT IDENTIFIER ::= { jnxModulePTX1000236qdd   8  }

-- Platform: SRX4700
  jnxProductLineSRX4700         OBJECT IDENTIFIER ::= { jnxProductLine  590 }
  jnxProductNameSRX4700         OBJECT IDENTIFIER ::= { jnxProductName  590 }
  jnxChassisSRX4700             OBJECT IDENTIFIER ::= { jnxChassis      590 }
  jnxSlotSRX4700                OBJECT IDENTIFIER ::= { jnxSlot         590 }
    jnxSRX4700SlotFPC           OBJECT IDENTIFIER ::= { jnxSlotSRX4700   1  }
    jnxSRX4700SlotRE            OBJECT IDENTIFIER ::= { jnxSlotSRX4700   2  }
    jnxSRX4700SlotPower         OBJECT IDENTIFIER ::= { jnxSlotSRX4700   3  }
    jnxSRX4700SlotFan           OBJECT IDENTIFIER ::= { jnxSlotSRX4700   4  }
    jnxSRX4700SlotCBD           OBJECT IDENTIFIER ::= { jnxSlotSRX4700   5  }

  jnxMediaCardSpaceSRX4700      OBJECT IDENTIFIER ::= { jnxMediaCardSpace    590 }
    jnxSRX4700MediaCardSpacePIC OBJECT IDENTIFIER ::= { jnxMediaCardSpaceSRX4700 1 }

  jnxMidplaneSRX4700            OBJECT IDENTIFIER ::= { jnxBackplane    590 }

  jnxModuleSRX4700              OBJECT IDENTIFIER ::= { jnxModule       590 }
    jnxSRX4700FPC               OBJECT IDENTIFIER ::= { jnxModuleSRX4700   1  }
    jnxSRX4700RE                OBJECT IDENTIFIER ::= { jnxModuleSRX4700   2  }
    jnxSRX4700Power             OBJECT IDENTIFIER ::= { jnxModuleSRX4700   3  }
    jnxSRX4700Fan               OBJECT IDENTIFIER ::= { jnxModuleSRX4700   4  }
    jnxSRX4700CBD               OBJECT IDENTIFIER ::= { jnxModuleSRX4700   5  }


END
