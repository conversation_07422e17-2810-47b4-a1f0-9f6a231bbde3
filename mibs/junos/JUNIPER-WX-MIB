
-- End - JUNIPER-WX-COMMON-MIB Module

-- ========================================================================
-- Start - JUNIPER-WX-MIB Module

-- WX MIB for the WAN Acceleration
--
-- The convention described in "Understanding SNMP MIBs"
-- (<PERSON>, <PERSON>), Chapter 8, has generally been followed.

JUNIPER-WX-MIB DEFINITIONS ::= BEGIN

	IMPORTS
		MODULE-IDENTITY, OBJECT-IDENTITY,
		OBJECT-TYPE, NOTIFICATION-TYPE,
		Integer32, Unsigned32, Gauge32, Counter64, IpAddress
			FROM SNMPv2-SMI
--		MODULE-COMPLIANCE, OBJECT-GROUP
--			FROM SNMPv2-CONF
		TimeStamp
			FROM SNMPv2-TC
		jnxWxModules, jnxWxSpecificMib
			FROM JUNIPER-WX-GLOBAL-R<PERSON>
		TcAppName, TcQosIdentifier
			FROM JUNIPER-WX-GLOBAL-TC
		jnxWxCommonEventDescr
			FROM JUNIPER-WX-COMMON-MIB;

	jnxWxMibModule MODULE-IDENTITY
		LAST-UPDATED			"200203280000Z"
		ORGANIZATION			"Juniper Networks, Inc"
		CONTACT-INFO			"
					Customer Support
					Juniper Networks, Inc.
					1194 North Mathilda Avenue
					Sunnyvale, CA  94089

					+1 888-314-JTAC
					<EMAIL>"

		DESCRIPTION				"
			A MIB module containing definitions of managed objects
			for Juniper Networks' WAN Acceleration family of products."

		REVISION				"200405240000Z"
		DESCRIPTION				"
			Add jnxWxEventMultiPathStatusChange notification."

		REVISION				"200306230000Z"
		DESCRIPTION				"
			o Add compression and throughput statistics
			  to the jnxWxSysStats group.
			o Add bytes counts to jnxWxAsmStatsTable.
			o Add bytes counts and bytes-in percentage
			  to jnxWxAppStatsTable.
			o Add jnxWxAppAggrStatsTable."

		REVISION				"200203280000Z"
		DESCRIPTION				"
			Add SessionOpened, RegServerUnreachable notifications."

		REVISION				"200203270000Z"
		DESCRIPTION				"
			Add SessionClosed, BufferOverflow notifications."

		REVISION				"200112191200Z"
		DESCRIPTION				"
			Rev 1.0
			Initial version of MIB module JUNIPER-WX-MIB."

		::= { jnxWxModules 4 }

	jnxWxMib OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Sub-tree for WAN Acceleration MIB."
		::= { jnxWxSpecificMib 1 }

	jnxWxConfMib OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Sub-tree for WAN Acceleration MIB conformance statements."
		::= { jnxWxMib 1 }

	jnxWxObjs OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Sub-tree for WAN Acceleration MIB objects."
		::= { jnxWxMib 2 }

	jnxWxEvents OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Sub-tree for WAN Acceleration MIB events."
		::= { jnxWxMib 3 }

	----------------------------------------------------------------------
	-- Stats update time

	jnxWxStatsUpdateTime OBJECT-TYPE
		SYNTAX  TimeStamp
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"This is the value of sysUpTime when the WX stats (jnxWxSysStats,
			jnxWxAsm, jnxWxApp) were last updated. The management station
			can use this information to tell if it's downloading a consistent set
			of stats."
		::= { jnxWxObjs 1 }

	jnxWxStatsAsmCount OBJECT-TYPE
		SYNTAX      Integer32
		MAX-ACCESS  read-only
		STATUS      current
		DESCRIPTION
			"The number of Decompressors this Compressor has had a session
			with at any time during the last two months."
		::= { jnxWxObjs 2 }

	jnxWxStatsVirtEndptCount OBJECT-TYPE
		SYNTAX      Integer32
		MAX-ACCESS  read-only
		STATUS      current
		DESCRIPTION
			"The number of virtual endpoints configured
			on this WX"
		::= { jnxWxObjs 9 }

	jnxWxStatsAppCount OBJECT-TYPE
		SYNTAX      Integer32
		MAX-ACCESS  read-only
		STATUS      current
		DESCRIPTION
			"The number of applications currently being monitored on this Compressor."
		::= { jnxWxObjs 3 }

	jnxWxStatsAccelAppCount OBJECT-TYPE
		SYNTAX      Integer32
		MAX-ACCESS  read-only
		STATUS      current
		DESCRIPTION
			"The number of applications currently being accelerated on this Compressor."
		::= { jnxWxObjs 8 }

	jnxWxStatsQosClassCount OBJECT-TYPE
		SYNTAX      Integer32
		MAX-ACCESS  read-only
		STATUS      current
		DESCRIPTION
			"The number of QoS classes currently being monitored on this Compressor."
		::= { jnxWxObjs 11 }

	jnxWxStatsQosEndptCount OBJECT-TYPE
		SYNTAX      Integer32
		MAX-ACCESS  read-only
		STATUS      current
		DESCRIPTION
			"The number of QoS endpoints currently being monitored on this Compressor."
		::= { jnxWxObjs 12 }

	jnxWxStatsWpEndptCount OBJECT-TYPE
		SYNTAX      Integer32
		MAX-ACCESS  read-only
		STATUS      current
		DESCRIPTION
			"Number of endpoints with WAN performance monitoring
			enabled"
		::= { jnxWxObjs 13 }

	----------------------------------------------------------------------
	-- The WX System group

	jnxWxSysStats OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Sub-tree for WAN Acceleration system statistics."
	::= { jnxWxObjs 4 }

	-- Decompression stats

	jnxWxSysStatsBytesInAe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that entered the WX Assembly Engine
			since the system was started."
		::= { jnxWxSysStats 1 }

	jnxWxSysStatsBytesOutAe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that exited the WX Assembly Engine
			after assembly since the system was started."
		::= { jnxWxSysStats 2 }

	jnxWxSysStatsPktsInAe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets that entered the WX Assembly Engine
			since the system was started."
		::= { jnxWxSysStats 3 }

	jnxWxSysStatsPktsOutAe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets that exited the WX Assembly Engine
			after assembly since the system was started."
		::= { jnxWxSysStats 4 }

	-- Out-of-band stats

	jnxWxSysStatsBytesOutOob OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of out-of-band bytes sent to the control channel
			since the system was started."
		::= { jnxWxSysStats 5 }

	-- Pass-through stats

	jnxWxSysStatsBytesPtNoAe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that passed through the WX Compression Engine
			because there was no corresponding Assembly Engine WX, since the
			system was started."
		::= { jnxWxSysStats 6 }

	jnxWxSysStatsPktsPtNoAe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets that passed through the WX Compression Engine
			because there was no corresponding Assembly Engine WX, since the
			system was started."
		::= { jnxWxSysStats 7 }

	jnxWxSysStatsBytesPtFilter OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that passed through the WX Compression Engine
			due to a manually configured filter (such as an application
			filter), since the system was started."
		::= { jnxWxSysStats 8 }

	jnxWxSysStatsPktsPtFilter OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets that passed through the WX Compression Engine
			due to a manually configured filter (such as an application
			filter), since the system was started."
		::= { jnxWxSysStats 9 }

	jnxWxSysStatsBytesOfPt OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that passed through the WX Compression Engine
			due to device buffer overflow, since the system was started."
		::= { jnxWxSysStats 10 }

	jnxWxSysStatsPktsOfPt OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets that passed through the WX Compression Engine
			due to device buffer overflow, since the system was started."
		::= { jnxWxSysStats 11 }

	-- Peak stats

	jnxWxSysStatsBytesTpIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The number of bytes into the WX Compression Engine at the peak
			five-second interval of data input, since the system was started."
		::= { jnxWxSysStats 12 }

	jnxWxSysStatsPktsTpIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The number of packets into the WX Compression Engine at the peak
			five-second interval of data input, since the system was started."
		::= { jnxWxSysStats 13 }

	jnxWxSysStatsBytesTpOut OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The number of bytes out of the WX Compression Engine at the peak
			five-second interval of data input, since the system was started."
		::= { jnxWxSysStats 14 }

	jnxWxSysStatsPktsTpOut OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The number of packets out of the WX Compression Engine at the peak
			five-second interval of data input, since the system was started."
		::= { jnxWxSysStats 15 }

	jnxWxSysStatsBytesTpPt OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The number of bytes that passed through the WX Compression Engine
			at the peak five-second interval of data input, since the system
			was started."
		::= { jnxWxSysStats 16 }

	jnxWxSysStatsPktsTpPt OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The number of packets that passed through the WX Compression Engine
			at the peak five-second interval of data input, since the system
			was started."
		::= { jnxWxSysStats 17 }

	jnxWxSysStatsPeakRdn OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The maximum data reduction rate for a five second interval
			in the last minute.

			The value returned is (percentage * 1000). Applications should
			divide the returned value by 1000."
		::= { jnxWxSysStats 18 }

	-- Througput stats

	jnxWxSysStatsThruputIn OBJECT-TYPE
		SYNTAX  Gauge32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Average throughput of incoming data in Kbps since the
			system was started."
		::= { jnxWxSysStats 19 }

	jnxWxSysStatsThruputOut OBJECT-TYPE
		SYNTAX  Gauge32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Average throughput of outgoing data in Kbps since the
			system was started."
		::= { jnxWxSysStats 20 }

	-- Compression stats

	jnxWxSysStatsBytesInRe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that entered the WX Compression Engine
			since the system was started."
		::= { jnxWxSysStats 21 }

	jnxWxSysStatsBytesOutRe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that exited the WX Compression Engine
			after reduction since the system was started."
		::= { jnxWxSysStats 22 }

	jnxWxSysStatsPktsInRe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets that entered the WX Compression Engine
			since the system was started."
		::= { jnxWxSysStats 23 }

	jnxWxSysStatsPktsOutRe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets that exited the WX Compression Engine
			after reduction since the system was started."
		::= { jnxWxSysStats 24 }

	jnxWxSysStatsPktSizeIn1 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 25 }

	jnxWxSysStatsPktSizeIn2 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 26 }

	jnxWxSysStatsPktSizeIn3 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 27 }

	jnxWxSysStatsPktSizeIn4 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 28 }

	jnxWxSysStatsPktSizeIn5 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 29 }

	jnxWxSysStatsPktSizeIn6 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 30 }

	jnxWxSysStatsPktSizeOut1 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 31 }

	jnxWxSysStatsPktSizeOut2 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 32 }

	jnxWxSysStatsPktSizeOut3 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 33 }

	jnxWxSysStatsPktSizeOut4 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 34 }

	jnxWxSysStatsPktSizeOut5 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 35 }

	jnxWxSysStatsPktSizeOut6 OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxSysStats 36 }

	----------------------------------------------------------------------
	-- Information maintained by a Compressor about each Decompressor it has
	-- a session with.

	jnxWxAsm OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Information maintained by a Compressor for each Decompressor it has
			a session with."
		::= { jnxWxObjs 5 }

	jnxWxAsmTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxAsmEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"List of Decompressors the Compressor has sessions with."
		::= { jnxWxAsm 1 }

	jnxWxAsmEntry OBJECT-TYPE
		SYNTAX  JuniperWxAsmEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxAsmTable."
		INDEX   { jnxWxAsmIndex }
		::= { jnxWxAsmTable 1 }

	JuniperWxAsmEntry ::=
		SEQUENCE
		{
			jnxWxAsmIndex
				Integer32,
			jnxWxAsmIpAddress
				IpAddress
		}

	jnxWxAsmIndex OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS not-accessible
		STATUS  current
		DESCRIPTION
			"This row's number, with range (1..jnxWxStatsAsmCount)."
		::= { jnxWxAsmEntry 1 }

	jnxWxAsmIpAddress OBJECT-TYPE
		SYNTAX  IpAddress
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The IP address of the Decompressor."
		::= { jnxWxAsmEntry 2 }

	----------------------------------------------------------------------
	-- Stats maintained by a Compressor for each Decompressor it has
	-- a session with.

	jnxWxAsmStatsTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxAsmStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Statistics maintained by a Compressor for each Decompressor it has
			a session with."
		::= { jnxWxAsm 2 }

	jnxWxAsmStatsEntry OBJECT-TYPE
		SYNTAX  JuniperWxAsmStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxAsmStatsTable."
		AUGMENTS   { jnxWxAsmEntry }
		::= { jnxWxAsmStatsTable 1 }

	JuniperWxAsmStatsEntry ::=
		SEQUENCE
		{
			jnxWxAsmStatsPktsIn
				Counter64,
			jnxWxAsmStatsPktsOut
				Counter64,
			jnxWxAsmStatsBytesIn
				Counter64,
			jnxWxAsmStatsBytesOut
				Counter64
		}

	jnxWxAsmStatsPktsIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The total number of packets into this WX, since
			the system was started, that were identified for
			reduction and addressed to the WX whose IP address
			is in the row in jnxWxAsmTable that matches
			this row."

		::= { jnxWxAsmStatsEntry 1 }

	jnxWxAsmStatsPktsOut OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The total number of packets out of this WX, since
			the system was started, that were identified for
			reduction and addressed to the WX whose IP address
			is in the row in jnxWxAsmTable that matches
			this row."
		::= { jnxWxAsmStatsEntry 2 }

	jnxWxAsmStatsBytesIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The total number of bytes into this WX, since
			the system was started, that were identified for
			reduction and addressed to the WX whose IP address
			is in the row in jnxWxAsmTable that matches
			this row."
		::= { jnxWxAsmStatsEntry 3 }

	jnxWxAsmStatsBytesOut OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The total number of bytes out of this WX, since
			the system was started, that were identified for
			reduction and addressed to the WX whose IP address
			is in the row in jnxWxAsmTable that matches
			this row."
		::= { jnxWxAsmStatsEntry 4 }

	----------------------------------------------------------------------
	-- Virtual Endpoint definitions configured on the compressor

	jnxWxVirtEndptTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JnxWxVirtEndptEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Virtual Endpoints defined in this WX"
		::= { jnxWxAsm 3 }

	jnxWxVirtEndptEntry OBJECT-TYPE
		SYNTAX  JnxWxVirtEndptEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxVirtEndptTable."
		INDEX   { jnxWxVirtEndptIndex }
		::= { jnxWxVirtEndptTable 1 }

	JnxWxVirtEndptEntry ::=
		SEQUENCE
		{
			jnxWxVirtEndptIndex
				Integer32,
			jnxWxVirtEndptName
				TcAppName,
			jnxWxVirtEndptSubnetCount
				Integer32
		}

	jnxWxVirtEndptIndex OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS not-accessible
		STATUS  current
		DESCRIPTION
			"A row's number, with range (1..jnxWxStatsVirtEndptCount)"
		::= { jnxWxVirtEndptEntry 1 }

	jnxWxVirtEndptName OBJECT-TYPE
		SYNTAX  TcAppName
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The name of the configured virtual endpoint"
		::= { jnxWxVirtEndptEntry 2 }

	jnxWxVirtEndptSubnetCount OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The number of subnets associated with this
			virtual endpoint"
		::= { jnxWxVirtEndptEntry 3 }

	----------------------------------------------------------------------
	-- Application information maintained by a Compressor

	jnxWxApp OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Information maintained by a Compressor for each application
			that is monitored."
	::= { jnxWxObjs 6 }

	jnxWxAppTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxAppEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"List of applications currently monitored."
		::= { jnxWxApp 1 }

	jnxWxAppEntry OBJECT-TYPE
		SYNTAX  JuniperWxAppEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxAppTable."
		INDEX   { jnxWxAppIndex }
		::= { jnxWxAppTable 1 }

	JuniperWxAppEntry ::=
		SEQUENCE
		{
			jnxWxAppIndex
				Integer32,
			jnxWxAppAppName
				TcAppName
		}

	jnxWxAppIndex OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS not-accessible
		STATUS  current
		DESCRIPTION
			"This row's number, with range (1..jnxWxStatsAppCount)."
		::= { jnxWxAppEntry 1 }

	jnxWxAppAppName OBJECT-TYPE
		SYNTAX  TcAppName
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The name of the application from which the data was
			received (e.g., FTP, HTTP, Lotus Notes)."
		::= { jnxWxAppEntry 2 }

	----------------------------------------------------------------------
	-- Application stats maintained by a Compressor for each Decompressor
	-- it has a session with.

	jnxWxAppStatsTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxAppStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Statistics maintained by a Compressor for each application
			that is monitored."
		::= { jnxWxApp 2 }

	jnxWxAppStatsEntry OBJECT-TYPE
		SYNTAX  JuniperWxAppStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxAppStatsTable.
			Note that the indices are from jnxWxAsmTable and
			jnxWxAppTable. For each Decompressor this compressor has
			a session with (indexed by jnxWxAsmIndex), statistics
			are maintained for each application that is monitored
			(indexed by jnxWxAppIndex)."
		INDEX   { jnxWxAsmIndex, jnxWxAppIndex }
		::= { jnxWxAppStatsTable 1 }

	JuniperWxAppStatsEntry ::=
		SEQUENCE
		{
			jnxWxAppStatsBytesIn
				Counter64,
			jnxWxAppStatsBytesOut
				Counter64,
			jnxWxAppStatsBytesInPercent
				Gauge32,
			jnxWxAppStatsAppName
				TcAppName,
			jnxWxAppStatsAccelBytesIn
				Counter64,
			jnxWxAppStatsActiveSessionTime
				Counter64,
			jnxWxAppStatsEstBoostBytes
				Counter64,
			jnxWxAppStatsBytesOutWxc
				Counter64
		}

	jnxWxAppStatsBytesIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The total number of bytes into the WX, since the system
			was started, that were identified for reduction,
			and addressed to the WX whose IP address is in the
			row in jnxWxAsmTable that matches the index
			jnxWxAsmIndex, and that was received from the
			application whose name is in the row in jnxWxAppTable
			that matches the index jnxWxAppIndex."
		::= { jnxWxAppStatsEntry 1 }

	jnxWxAppStatsBytesOut OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The total number of bytes out of the WX, since the
			system was started, that were identified for reduction,
			and addressed to the WX whose IP address is in the
			row in jnxWxAsmTable that matches the index
			jnxWxAsmIndex, and that was received from the
			application whose name is in the row in jnxWxAppTable."
		::= { jnxWxAppStatsEntry 2 }

	jnxWxAppStatsBytesInPercent OBJECT-TYPE
		SYNTAX  Gauge32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The percentage of the bytes in for this application
			over the total bytes into the WX, since the system
			was started, that were identified for reduction,
			and addressed to the WX whose IP address is in the
			row in jnxWxAsmTable that matches the index
			jnxWxAsmIndex, and that was received from the
			application whose name is in the row in jnxWxAppTable
			that matches the index jnxWxAppIndex.

			The value returned is (percentage * 1000). Applications should
			divide the returned value by 1000."
		::= { jnxWxAppStatsEntry 3 }

	jnxWxAppStatsAppName OBJECT-TYPE
		SYNTAX  TcAppName
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The name of the application from which the data was
			received (e.g., FTP, HTTP, Lotus Notes).

			NOTE: This object is here even though it's also in
			jnxWxAppTable. This is for the convenience of network
			monitoring tools that require all columns to be from the
			same table."
		::= { jnxWxAppStatsEntry 4 }

	jnxWxAppStatsAccelBytesIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxAppStatsEntry 5 }

	jnxWxAppStatsActiveSessionTime OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxAppStatsEntry 6 }

	jnxWxAppStatsEstBoostBytes OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxAppStatsEntry 7 }

	jnxWxAppStatsBytesOutWxc OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxAppStatsEntry 8 }

	----------------------------------------------------------------------
	-- Aggregate application stats (across all Decompressors)
	-- maintained by a Compressor. This table augments jnxWxAppTable.

	jnxWxAppAggrStatsTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxAppAggrStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Aggregate statistics maintained by a Compressor for each application
			that is monitored. These statistics apply to the compressor as a whole
			and are summed over all decompressor sessions maintained by the compressor."
		::= { jnxWxApp 3 }

	jnxWxAppAggrStatsEntry OBJECT-TYPE
		SYNTAX  JuniperWxAppAggrStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxAppAggrStatsTable."
		AUGMENTS   { jnxWxAppEntry }
		::= { jnxWxAppAggrStatsTable 1 }

	JuniperWxAppAggrStatsEntry ::=
		SEQUENCE
		{
			jnxWxAppAggrStatsBytesInRe
				Counter64,
			jnxWxAppAggrStatsBytesOutRe
				Counter64,
			jnxWxAppAggrStatsBytesInPercent
				Gauge32
		}

	jnxWxAppAggrStatsBytesInRe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that entered the WX Compression Engine
			for this application since the system was started."
		::= { jnxWxAppAggrStatsEntry 1 }

	jnxWxAppAggrStatsBytesOutRe OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes that exited the WX Compression Engine
			after reduction for this application since the system was started."
		::= { jnxWxAppAggrStatsEntry 2 }

	jnxWxAppAggrStatsBytesInPercent OBJECT-TYPE
		SYNTAX  Gauge32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The percentage of the bytes in for this application over the
			total bytes into the WX Compression Engine.

			The value returned is (percentage * 1000). Applications should
			divide the returned value by 1000."
		::= { jnxWxAppAggrStatsEntry 3 }

	----------------------------------------------------------------------
	-- WAN stats per application

	jnxWxWanStatsTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxWanStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Per application WAN performance statistics"
		::= { jnxWxApp 4 }

	jnxWxWanStatsEntry OBJECT-TYPE
		SYNTAX  JuniperWxWanStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxWanStatsTable.
			Note that the indices are from jnxWxAsmTable and
			jnxWxAppTable. For each Decompressor this compressor has
			a session with (indexed by jnxWxAsmIndex), statistics
			are maintained for each application that is monitored
			(indexed by jnxWxAppIndex)."
		INDEX   { jnxWxAsmIndex, jnxWxAppIndex }
		::= { jnxWxWanStatsTable 1 }

	JuniperWxWanStatsEntry ::=
		SEQUENCE
		{
			jnxWxWanStatsBytesToWan
				Counter64,
			jnxWxWanStatsBytesFromWan
				Counter64
		}

	jnxWxWanStatsBytesToWan OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes sent to the wan on a per
			 application basis"
		::= { jnxWxWanStatsEntry 1 }

	jnxWxWanStatsBytesFromWan OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes received from the WAN
			on a per application basis"
		::= { jnxWxWanStatsEntry 2 }

	----------------------------------------------------------------------
	-- A list of Applications that can be Accelerated

	jnxWxAccelAppNameTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JnxWxAccelAppNameEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"List of applications that can be accelerated by
			the WX device."
		::= { jnxWxApp 5 }

	jnxWxAccelAppNameEntry OBJECT-TYPE
		SYNTAX JnxWxAccelAppNameEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxAccelAppNameTable. Each row contains
			the name of an application that can be accelerated."
		INDEX   { jnxWxAccelAppIndex }
		::= { jnxWxAccelAppNameTable 1 }

	JnxWxAccelAppNameEntry ::=
		SEQUENCE
		{
			jnxWxAccelAppIndex
				Integer32,
			jnxWxAccelAppName
				TcAppName
		}

	jnxWxAccelAppIndex OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS not-accessible
		STATUS  current
		DESCRIPTION
			"This row's number, with range (1..jnxWxStatsAccelAppCount)."
		::= { jnxWxAccelAppNameEntry 1 }

	jnxWxAccelAppName OBJECT-TYPE
		SYNTAX  TcAppName
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The name of the application from which the data was
			received (e.g., FTP, HTTP, Lotus Notes)."
		::= { jnxWxAccelAppNameEntry 2 }

	----------------------------------------------------------------------
	-- Application Acceleration Statistics. Lists time with and without
	-- acceleration in seconds.

	jnxWxAccelAppStatsTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JnxWxAccelAppStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Accelerated application performance statistics. Includes
			 time with and without acceleration in seconds"
		::= { jnxWxApp 6 }

	jnxWxAccelAppStatsEntry OBJECT-TYPE
		SYNTAX JnxWxAccelAppStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxAccelAppStatsTable. Indices are from
			jnxWxAsmTable and jnxWxAccelAppNameTable. For each Decompressor
			this compressor has a session with (indexed by jnxWxAsmIndex),
			statistics are maintained for each application that is
			accelereated (indexed by jnxWxAccelAppIndex)"
		INDEX   { jnxWxAsmIndex, jnxWxAccelAppIndex }
		::= { jnxWxAccelAppStatsTable 1 }

	JnxWxAccelAppStatsEntry ::=
		SEQUENCE
		{
			jnxWxAccelAppTimeWithAccel
				Unsigned32,
			jnxWxAccelAppTimeWithoutAccel
				Unsigned32
		}

	jnxWxAccelAppTimeWithAccel OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of seconds required to complete all
			 transactions with acceleration enabled."
		::= { jnxWxAccelAppStatsEntry 3 }

	jnxWxAccelAppTimeWithoutAccel OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of seconds that would be required to
			complete all transactions if acceleration was disabled."
		::= { jnxWxAccelAppStatsEntry 4 }

	----------------------------------------------------------------------
	-- The burst stats over the past minute.
	-- NOTE: jnxWxStatsUpdateTime does not apply to this group.

	jnxWxBurstStats OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Sub-tree for WAN Acceleration system statistics."
		::= { jnxWxObjs 7 }

	jnxWxBurstStatsStartTime OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The start of the minute in seconds since
			midnight (00:00:00), January 1, 1970, UTC
			over which the burst stats apply."
		::= { jnxWxBurstStats 1 }

	jnxWxBurstStatsBpsIn OBJECT-TYPE
		SYNTAX  Gauge32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Burst bits per second into reduction engine for the past minute."
		::= { jnxWxBurstStats 2 }

	jnxWxBurstStatsBpsOut OBJECT-TYPE
		SYNTAX  Gauge32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Burst bits per second out of reduction engine for the past minute."
		::= { jnxWxBurstStats 3 }

	jnxWxBurstStatsBpsPt OBJECT-TYPE
		SYNTAX  Gauge32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Burst pass-through bits per second for the past minute."
		::= { jnxWxBurstStats 4 }

	----------------------------------------------------------------------
	-- Information about endpoints being managed by QoS

 	jnxWxQos OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Information maintained by a Compressor for each QoS class
			that is monitored."
	::= { jnxWxObjs 10 }

	jnxWxQosEndptTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxQosEndptEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"List of endpoints managed by QoS."
		::= { jnxWxQos 1 }

	jnxWxQosEndptEntry OBJECT-TYPE
		SYNTAX  JuniperWxQosEndptEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in jnxWxQosEndptTable."
		INDEX   { jnxWxQosEndptIndex }
		::= { jnxWxQosEndptTable 1 }

	JuniperWxQosEndptEntry ::=
		SEQUENCE
		{
			jnxWxQosEndptIndex
				Integer32,
			jnxWxQosEndptIdentifier
				TcQosIdentifier
		}

	jnxWxQosEndptIndex OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS not-accessible
		STATUS  current
		DESCRIPTION
			"This row's number, with range (1..jnxWxStatsQosEndptCount)."
		::= { jnxWxQosEndptEntry 1 }

	jnxWxQosEndptIdentifier OBJECT-TYPE
		SYNTAX  TcQosIdentifier
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The IP address of an Decompressor or the name
			of a virtual endpoint."
		::= { jnxWxQosEndptEntry 2 }

	----------------------------------------------------------------------
	-- Qos Class information maintained by a Compressor

	jnxWxQosClassTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxQosClassEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"List of QoS classes currently monitored."
		::= { jnxWxQos 2 }

	jnxWxQosClassEntry OBJECT-TYPE
		SYNTAX  JuniperWxQosClassEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in the jnxWxQosClassTable"
		INDEX   { jnxWxQosClassIndex }
		::= { jnxWxQosClassTable 1 }

	JuniperWxQosClassEntry ::=
		SEQUENCE
		{
			jnxWxQosClassIndex
				Integer32,
			jnxWxQosClassName
				TcQosIdentifier
		}

	jnxWxQosClassIndex OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS not-accessible
		STATUS  current
		DESCRIPTION
			"This row's number, with range (1..jnxWxStatsQosClassCount)."
		::= { jnxWxQosClassEntry 1 }

	jnxWxQosClassName OBJECT-TYPE
		SYNTAX  TcQosIdentifier
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"The name of the QoS class"
		::= { jnxWxQosClassEntry 2 }

	----------------------------------------------------------------------
	-- Qos performance stats per class

	jnxWxQosStatsTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxQosStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Per class QoS performance statistics"
		::= { jnxWxQos 3 }

	jnxWxQosStatsEntry OBJECT-TYPE
		SYNTAX  JuniperWxQosStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"A row in the jnxWxQosStatsTable. Indexed by
			decompressor and the corresponding QoS class.
			Statistics are maintained per endpoint
			(indexed by jnxWxQosEndptIndex) and per QoS
			class associated with the endpoint (indexed
			by jnxWxQosClassIndex)"
		INDEX   { jnxWxQosEndptIndex, jnxWxQosClassIndex }
		::= { jnxWxQosStatsTable 1 }

	JuniperWxQosStatsEntry ::=
		SEQUENCE
		{
			jnxWxQosStatsBytesIn
				Counter64,
			jnxWxQosStatsBytesOut
				Counter64,
			jnxWxQosStatsBytesDropped
				Counter64,
			jnxWxQosStatsPktsIn
				Counter64,
			jnxWxQosStatsPktsOut
				Counter64,
			jnxWxQosStatsPktsDropped
				Counter64
		}

	jnxWxQosStatsBytesIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes received from the WAN
			on a per class basis"
		::= { jnxWxQosStatsEntry 3 }

	jnxWxQosStatsBytesOut OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes sent to the WAN on a per
			 class basis"
		::= { jnxWxQosStatsEntry 4 }

	jnxWxQosStatsBytesDropped OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of bytes dropped on a per
			 class basis"
		::= { jnxWxQosStatsEntry 5 }

	jnxWxQosStatsPktsIn OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets received from the WAN
			on a per class basis"
		::= { jnxWxQosStatsEntry 6 }

	jnxWxQosStatsPktsOut OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets sent to the wan on a per
			 class basis"
		::= { jnxWxQosStatsEntry 7 }

	jnxWxQosStatsPktsDropped OBJECT-TYPE
		SYNTAX  Counter64
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Total number of packets dropped
			on a per class basis"
		::= { jnxWxQosStatsEntry 8 }

	----------------------------------------------------------------------
	-- WAN performance statistics for monitored endpoints

 	jnxWxWanPerf OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			"
	::= { jnxWxObjs 14 }

	jnxWxWpEndptTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxWpEndptEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"List of endpoints managed by Multi-path"
		::= { jnxWxWanPerf 1 }

	jnxWxWpEndptEntry OBJECT-TYPE
		SYNTAX  JuniperWxWpEndptEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			""
		INDEX   { jnxWxWpEndptIndex }
		::= { jnxWxWpEndptTable 1 }

	JuniperWxWpEndptEntry ::=
		SEQUENCE
		{
			jnxWxWpEndptIndex
				Integer32,
			jnxWxWpEndptIp
				IpAddress
		}

	jnxWxWpEndptIndex OBJECT-TYPE
		SYNTAX  Integer32
		MAX-ACCESS not-accessible
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpEndptEntry 1 }

	jnxWxWpEndptIp OBJECT-TYPE
		SYNTAX  IpAddress
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpEndptEntry 2 }

	----------------------------------------------------------------------
	-- Multi-path performance stats per class

	jnxWxWpStatsTable OBJECT-TYPE
		SYNTAX  SEQUENCE OF JuniperWxWpStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWanPerf 2 }

	jnxWxWpStatsEntry OBJECT-TYPE
		SYNTAX  JuniperWxWpStatsEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			""
		INDEX   { jnxWxWpEndptIndex }
		::= { jnxWxWpStatsTable 1 }

	JuniperWxWpStatsEntry ::=
		SEQUENCE
		{
			jnxWxWpStatsLatencyThresh
				Unsigned32,
			jnxWxWpStatsAvgLatency
				Unsigned32,
			jnxWxWpStatsLatencyCount
				Unsigned32,
			jnxWxWpStatsLatencyAboveThresh
				Unsigned32,
			jnxWxWpStatsLatencyAboveThreshCount
				Unsigned32,
			jnxWxWpStatsLossPercent
				Unsigned32,
			jnxWxWpStatsLossCount
				Unsigned32,
			jnxWxWpStatsEventCount
				Unsigned32,
			jnxWxWpStatsDiversionCount
				Unsigned32,
			jnxWxWpStatsReturnCount
				Unsigned32,
			jnxWxWpStatsLastDown
				Unsigned32,
			jnxWxWpStatsUnavailableCount
				Unsigned32,
			jnxWxWpStatsMinuteCount
				Unsigned32
		}

	jnxWxWpStatsLatencyThresh OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 3 }

	jnxWxWpStatsAvgLatency OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 4 }

	jnxWxWpStatsLatencyCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 5 }

	jnxWxWpStatsLatencyAboveThresh OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 6 }

	jnxWxWpStatsLatencyAboveThreshCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 7 }

	jnxWxWpStatsLossPercent OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 8 }

	jnxWxWpStatsLossCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 9 }

	jnxWxWpStatsEventCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 10 }

	jnxWxWpStatsDiversionCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 11 }

	jnxWxWpStatsReturnCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 12 }

	jnxWxWpStatsLastDown OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 13 }

	jnxWxWpStatsUnavailableCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 14 }

	jnxWxWpStatsMinuteCount OBJECT-TYPE
		SYNTAX  Unsigned32
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			""
		::= { jnxWxWpStatsEntry 15 }

	----------------------------------------------------------------------
	-- Events

	jnxWxEventObjs OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Branch for objects meant only to be sent in event varbinds."
		::= { jnxWxEvents 1 }

	jnxWxEventEvents OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Branch for the events themselves."
		::= { jnxWxEvents 2 }

	jnxWxEventEventsV2 OBJECT-IDENTITY
		STATUS					current
		DESCRIPTION				"
			Branch for SNMPv2 events. The OIDs for SNMPv2 events should
			have a zero as the next-to-last sub-identifier (as specified
			in RFC1902)."
		::= { jnxWxEventEvents 0 }

	-- Objects sent only in events
	-- None so far.

	-- Event descriptions

	jnxWxEventRipAuthFailure NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"RIP authentication failure"
		::= { jnxWxEventEventsV2 1 }

	jnxWxEventCompressionBufferOverflow NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The compressor's input buffer is approaching full capacity.
			
			This trap is currently unused."
		::= { jnxWxEventEventsV2 2 }

	jnxWxEventCompressionSessionClosed NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The compressor's session to the device described in
			jnxWxCommonEventDescr was terminated."
		::= { jnxWxEventEventsV2 3 }

	jnxWxEventDecompressionSessionClosed NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The decompressor's session to the device described in
			jnxWxCommonEventDescr was terminated."
		::= { jnxWxEventEventsV2 4 }

	jnxWxEventCompressionSessionOpened NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The compressor's session to the device described in
			jnxWxCommonEventDescr was opened."
		::= { jnxWxEventEventsV2 5 }

	jnxWxEventDecompressionSessionOpened NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The decompressor's session to the device described in
			jnxWxCommonEventDescr was opened."
		::= { jnxWxEventEventsV2 6 }

	jnxWxEventPrimaryRegServerUnreachable NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The primary registration server is
			currently unreachable."
		::= { jnxWxEventEventsV2 7 }

	jnxWxEventSecondaryRegServerUnreachable NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The secondary registration server is
			currently unreachable."
		::= { jnxWxEventEventsV2 8 }

	jnxWxEventMultiNodeMasterUp NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The system designated as the 'master' of a multi-node
			came up. This notification is generated by the
			system that's designated as the 'master' of the
			multi-node.

			Note that the corresponding Down notification is
			generated by the designated 'master-backup' of the
			same multi-node.
			
			This trap is currently unused."
		::= { jnxWxEventEventsV2 9 }

	jnxWxEventMultiNodeMasterDown NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The system designated as the 'master' of a multi-node
			is currently down. This notification is generated by the
			system that's designated as the 'master-backup' of the
			same multi-node.

			Note that the corresponding Up notification is
			generated by the designated 'master' of the same
			multi-node.
			
			This trap is currently unused."
		::= { jnxWxEventEventsV2 10 }

	jnxWxEventMultiNodeLastUp NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The system designated as the 'last-node' of a multi-node
			came up. This notification is generated by the
			system that's designated as the 'last-node' of the
			multi-node.

			Note that the corresponding Down notification is
			generated by the designated 'master' of the
			same multi-node.
			
			This trap is currently unused."
		::= { jnxWxEventEventsV2 11 }

	jnxWxEventMultiNodeLastDown NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The system designated as the 'last-node' of a multi-node
			is currently down. This notification is generated by the
			system that's designated as the 'master' of the
			same multi-node.

			Note that the corresponding Up notification is
			generated by the designated 'last-node' of the same
			multi-node.
			
			This trap is currently unused."
		::= { jnxWxEventEventsV2 12 }
	jnxWxEventPrimaryDownBackupEngaged NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The system designated as the 'primary' is currently
			unreachable. This notification is generated by the
			system that's designated as the 'backup' device.
			The backup device is engaged for the primary device."
		::= { jnxWxEventEventsV2 13 }
	jnxWxEventPrimaryDownBackupEngageFailed NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The system designated as the 'primary' is currently
			unreachable. This notification is generated by the
			system that's designated as the 'backup' device.
			The backup device failed to engage for the primary device."
		::= { jnxWxEventEventsV2 14 }
	jnxWxEventPrimaryUpBackupDisengaged NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The system designated as the 'primary' is currently
			reachable. This notification is generated by the
			system that's designated as the 'backup' device.
			The backup device has disengaged itself."
		::= { jnxWxEventEventsV2 15 }

	jnxWxEventMultiPathStatusChange NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The status of the primary or secondary path to another
			multi-path enabled system changed. The path became inactive
			or failed after previously being active or vice versa.
			This might have caused traffic designated to flow over
			this path to be switched from or to this path."
		::= { jnxWxEventEventsV2 16 }

	jnxWxEventDiskFailure NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"A hard disk failed on the system. The system was rebooted,
			and will now operate without the downed disk, at a degraded
			performance. Dictionary accumulated until this point was lost."
		::= { jnxWxEventEventsV2 17 }

	jnxWxEventWanPerfStatusChange NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The status of the Path on which WAN Performance Monitoring is
 			enabled has changed. The performance of the path has changed either
			from acceptable to unacceptable or vice versa."
		::= { jnxWxEventEventsV2 18 }

	jnxWxEventDCQAboveHiWatermark NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The decompression queue has reached the high watermark."
		::= { jnxWxEventEventsV2 19 }

	jnxWxEventDCQBelowHiWatermark NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The decompression queue has fallen below the high watermark."
		::= { jnxWxEventEventsV2 20 }

	jnxWxEventPerformanceThreshCrossed NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"The threshold for a configured perfromance event has been
            crossed. This notification indicates that some system parameter
            being measured has reached a critical value."
		::= { jnxWxEventEventsV2 21 }
	
	jnxWxEventClientLinkDown NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"Link to the Client went down."
		::= { jnxWxEventEventsV2 22 }

	jnxWxEventClientLinkUp NOTIFICATION-TYPE
		OBJECTS { jnxWxCommonEventDescr }
		STATUS  current
		DESCRIPTION
			"Link to the client came up."
		::= { jnxWxEventEventsV2 23 }

END
