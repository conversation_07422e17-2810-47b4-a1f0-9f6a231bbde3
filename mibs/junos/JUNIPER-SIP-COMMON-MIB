--
-- Juniper Enterprise Specific MIB: Sip Common MIB
--
-- Copyright (c) 2009, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-SIP-COMMON-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Unsigned32, TimeTicks
        FROM SNMPv2-SMI
    DisplayString
        FROM SNMPv2-TC
    jnxVoip
        FROM JUNIPER-JS-SMI
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB
    InetPortNumber
        FROM INET-ADDRESS-MIB;

jnxSipCommonMIB MODULE-IDENTITY
    LAST-UPDATED "200902092000Z"
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089
             E-mail: <EMAIL>"

    DESCRIPTION
            "This is Juniper Networks' implementation of enterprise specific
             MIB for SIP. This module defines objects which may be common to
             all SIP entities."
    ::= { jnxSip 1 }

jnxSip                      OBJECT IDENTIFIER ::= { jnxVoip 2 }
jnxSipCommonMIBObjects      OBJECT IDENTIFIER ::= { jnxSipCommonMIB 1 }

-- Common Configuration Objects

jnxSipCommonCfgTable OBJECT-TYPE
     SYNTAX      SEQUENCE OF JnxSipCommonCfgEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "This table contains the common configuration objects applicable
         to all SIP entities."
     ::= { jnxSipCommonMIBObjects 1 }

jnxSipCommonCfgEntry OBJECT-TYPE
     SYNTAX      JnxSipCommonCfgEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
        "A row of common configuration.

         Each row represents objects for a particular SIP entity
         instance present in this system."
     INDEX { jnxSipCfgApplName }
     ::= { jnxSipCommonCfgTable 1 }

JnxSipCommonCfgEntry ::= SEQUENCE {
         jnxSipCfgApplName                   DisplayString,
         jnxSipCommonCfgProtocolVersion      SnmpAdminString,
         jnxSipCommonCfgServiceOperStatus    INTEGER,
         jnxSipCommonCfgServiceStartTime     TimeTicks,
         jnxSipCommonCfgServiceLastChange    TimeTicks,
         jnxSipCommonCfgOrganization         SnmpAdminString,
         jnxSipCommonCfgMaxTransactions      Unsigned32,
         jnxSipCommonCfgEntityType           BITS
    }

jnxSipCfgApplName      OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the network application which uniquely
         identifies the application to which this entry is
         applicable."
    ::= { jnxSipCommonCfgEntry 1 }


jnxSipCommonCfgProtocolVersion OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object will reflect the version of SIP supported by this
         SIP entity.  It will follow the same format as SIP version
         information contained in the SIP messages generated by this SIP
         entity.  For example, entities supporting SIP version 2 will
         return 'SIP/2.0' as dictated by the standard."
    ::= { jnxSipCommonCfgEntry 2 }

jnxSipCommonCfgServiceOperStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                  unknown(1),
                  up(2),
                  down(3)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the current operational state of
         the  SIP application.

         unknown    : The operational status cannot be determined
                      for some reason.
         up         : The application is operating normally, and is
                      processing (receiving and possibly issuing) SIP
                      requests and responses.
         down       : The application is currently unable to process
                      SIP messages."
    ::= { jnxSipCommonCfgEntry 3 }

jnxSipCommonCfgServiceStartTime OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime at the time the SIP entity was last
         started. If started prior to the last re-initialization of the
         local network management subsystem, then this object contains a
         zero value."
    ::= { jnxSipCommonCfgEntry 4 }

jnxSipCommonCfgServiceLastChange OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime at the time the SIP entity entered its
         current operational state.  If the current state was entered
         prior to the last re-initialization of the local network
         management subsystem, then this object contains a zero value."
    ::= { jnxSipCommonCfgEntry 5 }

jnxSipCommonCfgOrganization OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object contains the organization name which the SIP entity
         inserts into Organization headers of SIP messages processed by
         this system.  If the string is empty, no Organization header is
         to be generated."
    ::= { jnxSipCommonCfgEntry 6 }

jnxSipCommonCfgMaxTransactions OBJECT-TYPE
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object indicates the maximum number of simultaneous
         transactions per second that the SIP entity can manage.  In
         general, the value of this object SHOULD reflect a level of
         transaction processing per second that is considered high
         enough to impact the system's CPU and/or memory resources to
         the point of deteriorating SIP call processing but not high
         enough to cause catastrophic system failure."
    ::= { jnxSipCommonCfgEntry 7 }

jnxSipCommonCfgEntityType OBJECT-TYPE
    SYNTAX      BITS {
                      other(0),
                      userAgent(1),
                      proxyServer(2),
                      redirectServer(3),
                      registrarServer(4)
                     }

    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object identifies the list of SIP entities this row is
         related to. It is defined as a bit map.  Each bit represents a
         type of SIP entity.  If a bit has value 1, the SIP entity
         represented by this row plays the role of this entity type.  If
         a bit has value 0, the SIP entity represented by this row does
         not act as this entity type Combinations of bits can be set
         when the SIP entity plays multiple SIP roles."
    ::= { jnxSipCommonCfgEntry 8 }


 -- Support for multiple ports

jnxSipCommonPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxSipCommonPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the list of ports that each SIP entity in
         this system is allowed to use.  These ports can be advertised
         using the Contact header in a REGISTER request or response."
    ::= { jnxSipCommonMIBObjects 2 }

jnxSipCommonPortEntry OBJECT-TYPE
    SYNTAX      JnxSipCommonPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Specification of a particular port.
         Each row represents those objects for a particular SIP entity
         present in this system."
    INDEX { jnxSipPortApplName, jnxSipCommonPort }
    ::= { jnxSipCommonPortTable 1 }

JnxSipCommonPortEntry ::= SEQUENCE {
    jnxSipPortApplName               DisplayString,
    jnxSipCommonPort                 InetPortNumber,
    jnxSipCommonPortTransportRcv     BITS
    }

jnxSipPortApplName     OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the network application which uniquely
         identifies the application to which this entry is
         applicable."
    ::= { jnxSipCommonPortEntry 1 }

jnxSipCommonPort OBJECT-TYPE
    SYNTAX      InetPortNumber (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This object reflects a particular port that can be used by the
         SIP application."
    ::= { jnxSipCommonPortEntry 2 }

jnxSipCommonPortTransportRcv OBJECT-TYPE
    SYNTAX      BITS {
                      other(0),  -- none of the following
                      udp(1),
                      tcp(2),
                      sctp(3),
                      tlsTcp(4),
                      tlsSctp(5)
                     }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object will specify the transport protocol the SIP entity
         will use to receive SIP messages.
         This object is a bit map.  Each bit represents a transport
         protocol.  If a bit has value 1, then that transport protocol
         is currently being used.  If a bit has value 0, then that
         transport protocol is currently not being used."
    ::= { jnxSipCommonPortEntry 3 }


 -- Support for SIP option tags (SIP extensions).

jnxSipCommonOptionTagTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxSipCommonOptionTagEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains a list of the SIP option tags (SIP
         extensions) that either required, supported, or unsupported by
         the SIP entity.  These option tags are used in the Require,
         Proxy-Require, Supported and Unsupported header fields.

         Example: if a user agent client supports and requires the
         server to support reliability of provisional responses (IETF
         RFC 3262), this table contains a row with the option tag string
         '100rel' in jnxSipCommonOptionTag and the OCTET STRING value of
         '1010 0000' or '0xA0' in jnxSipCommonOptionTagHeaderField.

         If a server does not support the required feature (indicated in
         a Require header to a UAS, or in a Proxy-Require to a Proxy
         Server), the server returns a 420 Bad Extension listing the
         feature in an Unsupported header.

         Normally the list of such features supported by an entity is
         static (i.e. will not change over time)."

    ::= { jnxSipCommonMIBObjects 3 }

jnxSipCommonOptionTagEntry OBJECT-TYPE
    SYNTAX      JnxSipCommonOptionTagEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
       "A particular SIP option tag (extension) supported or
        unsupported by the SIP entity, and which may be supported or
        required by a peer.
        Each row represents those objects for a particular SIP entity
         present in this system."
    INDEX { jnxSipOptionTagApplName, jnxSipCommonOptionTagIndex }
    ::= { jnxSipCommonOptionTagTable 1 }

JnxSipCommonOptionTagEntry ::= SEQUENCE {
    jnxSipOptionTagApplName           DisplayString,
    jnxSipCommonOptionTagIndex        Unsigned32,
    jnxSipCommonOptionTag             SnmpAdminString,
    jnxSipCommonOptionTagHeaderField  BITS
    }

jnxSipOptionTagApplName                OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the network application which uniquely
         identifies the application to which this entry is
         applicable."
    ::= { jnxSipCommonOptionTagEntry 1 }

jnxSipCommonOptionTagIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This object uniquely identifies a conceptual row in the table."
    ::= { jnxSipCommonOptionTagEntry 2 }

jnxSipCommonOptionTag OBJECT-TYPE
    SYNTAX      SnmpAdminString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object indicates the SIP option tag.  The option tag names
         are registered with IANA and available at http://www.iana.org/."
    ::= { jnxSipCommonOptionTagEntry 3 }

jnxSipCommonOptionTagHeaderField OBJECT-TYPE
    SYNTAX      BITS {
                      require(0),       -- Require header
                      proxyRequire(1),  -- Proxy-Require header
                      supported(2),     -- Supported header
                      unsupported(3)    -- Unsupported header
                     }

    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object indicates whether the SIP option tag is supported
         (Supported header), unsupported (Unsupported header), required
         (Require or Proxy-Require header) by the SIP entity.  A SIP
         option tag may be both supported and required."
    ::= { jnxSipCommonOptionTagEntry 4 }



 -- Supported SIP Methods

jnxSipCommonMethodSupportedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxSipCommonMethodSupportedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains a list of methods supported by each SIP
         entity in this system (see the standard set of SIP methods in
         Section 7.1 of RFC 3261).  Any additional methods that may be
         incorporated into the SIP protocol can be represented by this
         table without any requirement to update this MIB module.

         The table is informational in nature; conveying to the NMS
         capabilities of the managed system."
    ::= { jnxSipCommonMIBObjects 4 }

jnxSipCommonMethodSupportedEntry OBJECT-TYPE
    SYNTAX      JnxSipCommonMethodSupportedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A particular method supported by the SIP entity.
         Each row represents those objects for a particular SIP entity
         present in this system."
    INDEX { jnxSipMethodSupportedApplName, jnxSipCommonMethodSupportedIndex }
    ::= { jnxSipCommonMethodSupportedTable 1 }

JnxSipCommonMethodSupportedEntry ::= SEQUENCE {
    jnxSipMethodSupportedApplName        DisplayString,
    jnxSipCommonMethodSupportedIndex     Unsigned32,
    jnxSipCommonMethodSupportedName      OCTET STRING
    }

jnxSipMethodSupportedApplName  OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the network application which uniquely
         identifies the application to which this entry is
         applicable."
    ::= { jnxSipCommonMethodSupportedEntry 1 }

jnxSipCommonMethodSupportedIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..4294967295)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This object uniquely identifies a conceptual row in the table
         for a specific SIP method."
    ::= { jnxSipCommonMethodSupportedEntry 2 }

jnxSipCommonMethodSupportedName OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (1..100))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the supported method's name.  The method
         name MUST be all upper case (e.g, 'INVITE')."
 ::= { jnxSipCommonMethodSupportedEntry 3 }


-- SIP Timer Configuration

jnxSipCommonCfgTimerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxSipCommonCfgTimerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains timer configuration objects applicable to
         SIP user agent and SIP stateful Proxy Server entities."
    ::= { jnxSipCommonMIBObjects 5 }

jnxSipCommonCfgTimerEntry OBJECT-TYPE
    SYNTAX      JnxSipCommonCfgTimerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A row of timer configuration.

         Each row represents those objects for a particular SIP entity
         present in this system."



    INDEX { jnxSipCfgTimerApplName }
    ::= { jnxSipCommonCfgTimerTable 1 }

JnxSipCommonCfgTimerEntry ::= SEQUENCE {
    jnxSipCfgTimerApplName              DisplayString,
    jnxSipCommonCfgTimerA               Unsigned32,
    jnxSipCommonCfgTimerB               Unsigned32,
    jnxSipCommonCfgTimerC               Unsigned32,
    jnxSipCommonCfgTimerD               Unsigned32,
    jnxSipCommonCfgTimerE               Unsigned32,
    jnxSipCommonCfgTimerF               Unsigned32,
    jnxSipCommonCfgTimerG               Unsigned32,
    jnxSipCommonCfgTimerH               Unsigned32,
    jnxSipCommonCfgTimerI               Unsigned32,
    jnxSipCommonCfgTimerJ               Unsigned32,
    jnxSipCommonCfgTimerK               Unsigned32,
    jnxSipCommonCfgTimerT1              Unsigned32,
    jnxSipCommonCfgTimerT2              Unsigned32,
    jnxSipCommonCfgTimerT4              Unsigned32
    }

jnxSipCfgTimerApplName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The name of the network application which uniquely
         identifies the application to which this entry is
         applicable."
    ::= { jnxSipCommonCfgTimerEntry 1 }

jnxSipCommonCfgTimerA OBJECT-TYPE
    SYNTAX      Unsigned32 (100..1000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the initial value for the retransmit timer
         for the INVITE method.  The retransmit timer doubles after each
         retransmission, ensuring an exponential backoff in network
         traffic.  This object represents the initial time a SIP entity
         will wait to receive a provisional response to an INVITE before
         resending the INVITE request."
    DEFVAL { 500 }
    ::= { jnxSipCommonCfgTimerEntry 2 }

jnxSipCommonCfgTimerB OBJECT-TYPE
    SYNTAX      Unsigned32 (32000..300000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the maximum time a SIP entity will wait to
         receive a final response to an INVITE.  The timer is started
         upon transmission of the initial INVITE request."
    DEFVAL { 32000 }
    ::= { jnxSipCommonCfgTimerEntry 3 }

jnxSipCommonCfgTimerC OBJECT-TYPE
    SYNTAX      Unsigned32 (180000..300000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the maximum time a SIP Proxy Server will
         wait to receive a provisional response to an INVITE.  The Timer
         C MUST be set for each client transaction when an INVITE
         request is proxied."
    DEFVAL { 180000 }
    ::= { jnxSipCommonCfgTimerEntry 4 }

jnxSipCommonCfgTimerD OBJECT-TYPE
    SYNTAX      Unsigned32 (0..300000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the amount of time that the server
         transaction can remain in the 'Completed' state when unreliable
         transports are used.  The default value MUST be equal to or
         greater than 32000 for UDP transport, and its value MUST be 0
         for TCP/SCTP transport."
    DEFVAL { 32000 }
    ::= { jnxSipCommonCfgTimerEntry 5 }

jnxSipCommonCfgTimerE OBJECT-TYPE
    SYNTAX      Unsigned32 (100..1000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the initial value for the retransmit timer
         for a non-INVITE method while in 'Trying' state.  The
         retransmit timer doubles after each retransmission until it
         reaches T2 to ensure an exponential backoff in network traffic.
         This object represents the initial time a SIP entity will wait
         to receive a provisional response to the request before
         resending the non-INVITE request."
    DEFVAL { 500 }
    ::= { jnxSipCommonCfgTimerEntry 6 }

jnxSipCommonCfgTimerF  OBJECT-TYPE
    SYNTAX      Unsigned32 (32000..300000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the maximum time a SIP entity will wait to
         receive a final response to a non-INVITE request.  The timer is
         started upon transmission of the initial request."
    DEFVAL { 32000 }

    ::= { jnxSipCommonCfgTimerEntry 7 }

jnxSipCommonCfgTimerG  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..1000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the initial value for the retransmit timer
         for final responses to INVITE requests.  If timer G fires, the
         response is passed to the transport layer again for
         retransmission, and timer G is set to fire in MIN(2*T1, T2)
         seconds.  From then on, when timer G fires, the response is
         passed to the transport again for transmission, and timer G is
         reset with a value that doubles, unless that value exceeds T2,
         in which case, it is reset with the value of T2.  The default
         value MUST be T1 for UDP transport, and its value MUST be 0 for
         reliable transport like TCP/SCTP."
    DEFVAL { 500 }
    ::= { jnxSipCommonCfgTimerEntry 8 }

jnxSipCommonCfgTimerH  OBJECT-TYPE
    SYNTAX      Unsigned32 (32000..300000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the maximum time a server will wait to
         receive an ACK before it abandons retransmitting the response.
         The timer is started upon entering the 'Completed' state."
    DEFVAL { 32000 }
    ::= { jnxSipCommonCfgTimerEntry 9 }

jnxSipCommonCfgTimerI  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..10000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the maximum time a SIP entity will wait to
         receive additional ACK message retransmissions.
         The timer is started upon entering the 'Confirmed' state.  The
         default value MUST be T4 for UDP transport and its value MUST
         be 0 for reliable transport like TCP/SCTP."
    DEFVAL { 5000 }
    ::= { jnxSipCommonCfgTimerEntry 10 }

jnxSipCommonCfgTimerJ  OBJECT-TYPE
    SYNTAX      Unsigned32 (32000..300000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the maximum time a SIP server will wait to
         receive retransmissions of non-INVITE requests.  The timer is
         started upon entering the 'Completed' state for non-INVITE
         transactions.  When timer J fires, the server MUST transition to
         the 'Terminated' state."
    DEFVAL { 32000 }
    ::= { jnxSipCommonCfgTimerEntry 11 }

jnxSipCommonCfgTimerK  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..10000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the maximum time a SIP client will wait to
         receive retransmissions of responses to non-INVITE requests.
         The timer is started upon entering the 'Completed' state for
         non-INVITE transactions.  When timer K fires, the server MUST
         transition to the 'Terminated' state.  The default value MUST
         be T4 for UDP transport, and its value MUST be 0 for reliable
         transport like TCP/SCTP."
    DEFVAL { 5000 }
    ::= { jnxSipCommonCfgTimerEntry 12 }

jnxSipCommonCfgTimerT1  OBJECT-TYPE
    SYNTAX      Unsigned32 (200..10000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the T1 timer for a SIP entity.  T1 is an
         estimate of the round-trip time (RTT) between the client and
         server transactions."
    DEFVAL { 500 }
    ::= { jnxSipCommonCfgTimerEntry 13 }

jnxSipCommonCfgTimerT2  OBJECT-TYPE
    SYNTAX      Unsigned32 (200..10000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the T2 timer for a SIP entity.  T2 is the
         maximum retransmit interval for non-INVITE requests and INVITE
         responses.  It's used in various parts of the protocol to reset
         other Timer* objects to this value."
    DEFVAL { 4000 }
    ::= { jnxSipCommonCfgTimerEntry 14 }

jnxSipCommonCfgTimerT4  OBJECT-TYPE
    SYNTAX      Unsigned32 (200..10000)
    UNITS      "milliseconds"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object reflects the T4 timer for a SIP entity.  T4 is the
         maximum duration a message will remain in the network.  It
         represents the amount of time the network will take to clear
         messages between client and server transactions.  It's used in
         various parts of the protocol to reset other Timer* objects to
         this value."
    DEFVAL { 5000 }
    ::= { jnxSipCommonCfgTimerEntry 15 }

END

