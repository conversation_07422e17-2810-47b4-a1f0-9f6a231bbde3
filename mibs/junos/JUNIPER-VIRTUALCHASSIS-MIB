--
-- Juniper Enterprise Specific MIB: Virtual Chassis
--
-- Copyright (c) 2007-2014, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-VIRTUALCHASSIS-MIB  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Counter64     FROM SNMPv2-SMI 
    OBJECT-IDENTITY                             FROM SNMPv2-SMI
    NOTIFICATION-TYPE                           FROM SNMPv2-SMI
    MacAddress,DisplayString                    FROM SNMPv2-TC
    JnxChassisId                                FROM JUNIPER-MIB
    jnxVccpNotifications                        FROM JUNIPER-SMI
    jnxExVirtualChassis                         FROM JUNIPER-EX-SMI;
    
    jnxVirtualChassisMemberMIB MODULE-IDENTITY
                                      
    LAST-UPDATED "201403180000Z" -- 18 March, 2014
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
         "Juniper Technical Assistance Center
          Juniper Networks, Inc.
          1133 Innovation Way
            Sunnyvale, CA 94089
            E-mail: <EMAIL>"

    DESCRIPTION
            "The MIB modules for Virtual Chassis Member.Virtual Chassis Member feature allows a set of
             Juniper switches to be connected together to form a Virtual Chassis. A virtual chassis
             will give all the features of a real chassis, potentially at a much lower cost points.
             A virtual chassis will be managed as a single box and will appear as a single network
             element to other switches and routers in the network. Some Juniper switches will have
             the special high speed stacking ports which are used to connect to each other to form a 
             stack. The Juniper switches can also be connected by the network ports to form a stack.
             These can be used by Juniper switches that don't have dedicated stacking ports."

    REVISION
        "201007130000Z" -- 13 July, 2010

    DESCRIPTION
        "Added jnxVccpMemberUp and jnxVccpMemberDown Traps."

    REVISION
        "201010140000Z" -- 14 October, 2010

    DESCRIPTION
        "Modified the range for jnxVirtualChassisMemberPriority from 0 to 255."

    REVISION
        "201403180000Z" -- 18 March, 2014

    DESCRIPTION
        "Generalize the MIB description for other Juniper platforms and update 
         the jnxVirtualChassisMemberId from 9 to 31."
 
       ::= { jnxExVirtualChassis 1 }

--
-- virtual chassis table
--

    jnxVirtualChassisMemberTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF JnxVirtualChassisMemberEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "A list of virtual chassis Member information entries."
    ::= { jnxVirtualChassisMemberMIB 1 }

    jnxVirtualChassisMemberEntry OBJECT-TYPE
    SYNTAX      JnxVirtualChassisMemberEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the virtual chassis Member information table."
        INDEX   { jnxVirtualChassisMemberId }
    ::= { jnxVirtualChassisMemberTable 1 }

    JnxVirtualChassisMemberEntry ::=
    SEQUENCE {
        jnxVirtualChassisMemberId                         INTEGER,
        jnxVirtualChassisMemberSerialnumber               DisplayString,
        jnxVirtualChassisMemberRole                       INTEGER,
        jnxVirtualChassisMemberMacAddBase                 MacAddress,
        jnxVirtualChassisMemberSWVersion                  DisplayString ,
        jnxVirtualChassisMemberPriority                   INTEGER,
        jnxVirtualChassisMemberUptime                     INTEGER,
        jnxVirtualChassisMemberModel                      DisplayString,
        jnxVirtualChassisMemberLocation                   DisplayString,
        jnxVirtualChassisMemberAlias                      DisplayString,
        jnxVirtualChassisMemberFabricMode                 DisplayString,
        jnxVirtualChassisMemberMixedMode                  DisplayString
    }

    jnxVirtualChassisMemberId OBJECT-TYPE
    SYNTAX      INTEGER (0..31)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Identifies the virtual-chassis member."
    ::= {  jnxVirtualChassisMemberEntry 1 }

    jnxVirtualChassisMemberSerialnumber OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
    DESCRIPTION
        "Serial number of the virtual-chassis member."
    ::= {  jnxVirtualChassisMemberEntry 2 }

    jnxVirtualChassisMemberRole OBJECT-TYPE
    SYNTAX        INTEGER {
                    master (1),
                    backup (2),
                    linecard (3)
                    }

    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Specifies whether the virtual-chassis member is of the type:
        master(1), backup(2), linecard (3) "
    ::= {  jnxVirtualChassisMemberEntry 3 }

    jnxVirtualChassisMemberMacAddBase OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Identifies Media Access Control Address base for the virtual-chassis member."
    ::= {  jnxVirtualChassisMemberEntry 4 }

    jnxVirtualChassisMemberSWVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Identifies the JUNOS Base OS Software suite running on the virtual-chassis
        member."
    ::= {  jnxVirtualChassisMemberEntry 5 }

    jnxVirtualChassisMemberPriority OBJECT-TYPE
    SYNTAX      INTEGER (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This specifies the priority of the virtual-chassis member which can
        vary from 0 to 255."
    ::= {  jnxVirtualChassisMemberEntry 6 }

    jnxVirtualChassisMemberUptime OBJECT-TYPE
    SYNTAX      INTEGER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the virtual-chassis member uptime. "
    ::= {  jnxVirtualChassisMemberEntry 7 }

    jnxVirtualChassisMemberModel OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the virtual-chassis member model. "
    ::= {  jnxVirtualChassisMemberEntry 8 }

    jnxVirtualChassisMemberLocation OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies the virtual-chassis member location. "
    ::= {  jnxVirtualChassisMemberEntry 9 }

    jnxVirtualChassisMemberAlias OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alias name for the virtual-chassis member."
    ::= {  jnxVirtualChassisMemberEntry 10 }

    jnxVirtualChassisMemberFabricMode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies virtual-chassis member's route mode. VC/VCF mode
         indicates multi-path routing is disabled/enabled for the member. "
    ::= {  jnxVirtualChassisMemberEntry 11 }

    jnxVirtualChassisMemberMixedMode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies virtual-chassis member's mixed mode. If mixed-mode is
         is enabled, devices from different product families are allowed
         to become virtual chassis member. "
    ::= {  jnxVirtualChassisMemberEntry 12 }

   -- jnxVirtualChassisPortTable holds the vccp port's admin/operation status


    jnxVirtualChassisPortTable OBJECT-TYPE
    SYNTAX  SEQUENCE OF JnxVirtualChassisPortEntry
    MAX-ACCESS  not-accessible
    STATUS  current
    DESCRIPTION
        "A list of virtual chassis port information entries."
    ::= { jnxVirtualChassisMemberMIB 2 }

    jnxVirtualChassisPortEntry OBJECT-TYPE
    SYNTAX      JnxVirtualChassisPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the virtual chassis port information table."
        INDEX   { jnxVirtualChassisFpcId, jnxVirtualChassisPortName }
    ::= { jnxVirtualChassisPortTable 1 }

    JnxVirtualChassisPortEntry ::=
    SEQUENCE {
        jnxVirtualChassisFpcId                          INTEGER,
        jnxVirtualChassisPortName                       DisplayString,
        jnxVirtualChassisPortAdminStatus                INTEGER,
        jnxVirtualChassisPortOperStatus                 INTEGER,
        jnxVirtualChassisPortInPkts                     Counter64,
        jnxVirtualChassisPortOutPkts                    Counter64,
        jnxVirtualChassisPortInOctets                   Counter64,
        jnxVirtualChassisPortOutOctets                  Counter64,
        jnxVirtualChassisPortInMcasts                   Counter64,
        jnxVirtualChassisPortOutMcasts                  Counter64,
        jnxVirtualChassisPortInPkts1secRate             Counter64,
        jnxVirtualChassisPortOutPkts1secRate            Counter64,
        jnxVirtualChassisPortInOctets1secRate           Counter64,
        jnxVirtualChassisPortOutOctets1secRate          Counter64,
        jnxVirtualChassisPortCarrierTrans               Counter64,
        jnxVirtualChassisPortInCRCAlignErrors           Counter64,
        jnxVirtualChassisPortUndersizePkts              Counter64,
        jnxVirtualChassisPortCollisions                 Counter64
    }
    
   jnxVirtualChassisFpcId OBJECT-TYPE
       SYNTAX      INTEGER (0..31)
       MAX-ACCESS  not-accessible
       STATUS      current
       DESCRIPTION
          "Identifies the virtual-chassis member."
    ::= {  jnxVirtualChassisPortEntry 1 }

   jnxVirtualChassisPortName OBJECT-TYPE
      SYNTAX        DisplayString (SIZE(0..127))
      MAX-ACCESS    not-accessible
      STATUS        current
      DESCRIPTION
           "Indicates the port name of this
             vccp port, which is typically but not limited to, a
             function of the state of individual segments of
             this port."
      ::= { jnxVirtualChassisPortEntry 2 }

   jnxVirtualChassisPortAdminStatus OBJECT-TYPE
      SYNTAX     INTEGER {
                  up(1),
                  down(2),
                  unknown(3)
                }
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the actual admin status of this
             vccp port, which is typically but not limited to, a
             function of the state of individual segments of
             this port."
      ::= { jnxVirtualChassisPortEntry 3 }

   jnxVirtualChassisPortOperStatus OBJECT-TYPE
      SYNTAX     INTEGER {
                  up(1),
                  down(2),
                  unknown(3)
                }
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the actual operational status of this
            vccp port, which is typically but not limited to, a
            function of the state of individual segments of
            this port."
      ::= { jnxVirtualChassisPortEntry 4 }
  
   jnxVirtualChassisPortInPkts OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of packets received on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortInPkts"
      ::= { jnxVirtualChassisPortEntry 5 }
  
   jnxVirtualChassisPortOutPkts OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of packets sent from the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortOutPkts"
      ::= { jnxVirtualChassisPortEntry 6 }
  
   jnxVirtualChassisPortInOctets OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of octets received on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortInOctets"
      ::= { jnxVirtualChassisPortEntry 7 }
  
   jnxVirtualChassisPortOutOctets OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of octets sent on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortOutOctets"
      ::= { jnxVirtualChassisPortEntry 8 }
  
   jnxVirtualChassisPortInMcasts OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of multicast packets received on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortInMcasts"
      ::= { jnxVirtualChassisPortEntry 9 }
  
   jnxVirtualChassisPortOutMcasts OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of multicast packets sent from the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortOutMcasts"
      ::= { jnxVirtualChassisPortEntry 10 }
  
   jnxVirtualChassisPortInPkts1secRate OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of packets received per second on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortInPkts1secRate"
      ::= { jnxVirtualChassisPortEntry 11 }
  
   jnxVirtualChassisPortOutPkts1secRate OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of packets sent per second from the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortOutPkts1secRate"
      ::= { jnxVirtualChassisPortEntry 12 }
  
   jnxVirtualChassisPortInOctets1secRate OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of octets received per secondon the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortInOctets1secRate"
      ::= { jnxVirtualChassisPortEntry 13 }
  
   jnxVirtualChassisPortOutOctets1secRate OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of octets sent per second on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortOutOctets1secRate"
      ::= { jnxVirtualChassisPortEntry 14 }
  
   jnxVirtualChassisPortCarrierTrans OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of Carrier errors on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortCarrierTrans"
      ::= { jnxVirtualChassisPortEntry 15 }
  
   jnxVirtualChassisPortInCRCAlignErrors OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of Input CRC Alignment errors on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortInCRCAlignErrors"
      ::= { jnxVirtualChassisPortEntry 16 }
  
   jnxVirtualChassisPortUndersizePkts OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of Undersize Packets on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortUndersizePkts"
      ::= { jnxVirtualChassisPortEntry 17 }
  
   jnxVirtualChassisPortCollisions OBJECT-TYPE
      SYNTAX        Counter64 
      MAX-ACCESS    read-only
      STATUS        current
      DESCRIPTION
           "Indicates the total number of Collisions on the 
           virtual-chassis port. This object is a 64-bit version of 
           jnxVirtualChassisPortCollisions"
      ::= { jnxVirtualChassisPortEntry 18 }
  
  jnxVccpNotificationsPrefix OBJECT-IDENTITY
      STATUS current
      DESCRIPTION 
      "All Virtual Chassis notifications are registered
      under this branch."
      ::= { jnxVccpNotifications 0 }

-- ***********************************************************

-- VCCP NOTIFICATIONS

-- ***********************************************************

jnxVccpPortUp NOTIFICATION-TYPE
    OBJECTS { jnxVirtualChassisPortAdminStatus, 
              jnxVirtualChassisPortOperStatus,
              jnxVirtualChassisMemberModel,
              jnxVirtualChassisMemberLocation }
    STATUS  current
    DESCRIPTION
            "A PortUp trap signifies that the SNMP entity, acting in an
            agent role, has detected that the jnxVirtualChassisPortOperStatus 
            object for one of its communication links left the  down state
            and transitioned into some other state (but not into the
            notPresent state).  This other state is indicated by the
            included value of jnxVirtualChassisPortOperStatus."
    ::= { jnxVccpNotificationsPrefix 1 }

jnxVccpPortDown NOTIFICATION-TYPE
    OBJECTS { jnxVirtualChassisPortAdminStatus, 
              jnxVirtualChassisPortOperStatus,
              jnxVirtualChassisMemberModel,
              jnxVirtualChassisMemberLocation }
    STATUS  current
    DESCRIPTION
            "A PortDown trap signifies that the SNMP entity, acting in an
            agent role, has detected that the jnxVirtualChassisPortOperStatus
            object for one of its communication links is about to enter the
            down state from some other state (but not into the
            notPresent state).  This other state is indicated by the
            included value of jnxVirtualChassisPortOperStatus."
    ::= { jnxVccpNotificationsPrefix 2 }

jnxVccpMemberUp NOTIFICATION-TYPE
    OBJECTS { jnxVirtualChassisMemberSerialnumber,
              jnxVirtualChassisMemberRole,
              jnxVirtualChassisMemberModel,
              jnxVirtualChassisMemberLocation }
    STATUS  current
    DESCRIPTION
            "A MemberUp trap signifies that the SNMP entity, acting in an
            agent role, has detected that the Member present at the location
            jnxVirtualChassisMemberLocation on one of the Virtual
            Chassis left the down state and transitioned into some other
            state (but not into the notPresent state)."
    ::= { jnxVccpNotificationsPrefix 3 }

jnxVccpMemberDown NOTIFICATION-TYPE
    OBJECTS { jnxVirtualChassisMemberSerialnumber,
              jnxVirtualChassisMemberRole,
              jnxVirtualChassisMemberModel,
              jnxVirtualChassisMemberLocation }
    STATUS  current
    DESCRIPTION
            "A MemberDown trap signifies that the SNMP entity, acting in an
            agent role, has detected that the Member present at the location
            jnxVirtualChassisMemberLocation on one of the Virtual
            Chassis is about to enter the down state (but not into the notPresent
            state)."
    ::= { jnxVccpNotificationsPrefix 4 }

END
