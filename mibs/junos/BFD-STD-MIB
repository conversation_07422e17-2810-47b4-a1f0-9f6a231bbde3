BFD-STD-MIB DEFINITIONS ::= BEGIN  
   IMPORTS 
      MODULE-IDENTITY, OBJECT-TYPE,  
      Unsigned32, Counter32, Counter64,  
      NOTIFICATION-TYPE 
         FROM SNMPv2-SMI                        -- [RFC2578]
    
      MODULE-COMPLIANC<PERSON>, OBJECT-G<PERSON><PERSON>, 
      NOTIFICATION-GROUP     
          FROM SNMPv2-CONF                      -- [RFC2580]
    
      TEXTUAL-CONVENTION, TruthValue, 
      RowStatus, StorageType, TimeStamp 
         FROM SNMPv2-TC                         -- [RFC2579]
    
      InetAddress, InetAddressType, InetPortNumber 
         FROM INET-ADDRESS-MIB                  -- [RFC3291]

      -- Juniper specific                                       *** JNX ***
      jnxBfdExperiment                                       -- *** JNX ***
         FROM JUNIPER-EXPERIMENT-MIB                         -- *** JNX ***
   ; 
    
   bfdMIB MODULE-IDENTITY 
      LAST-UPDATED "200507221200Z"  -- 04 July 2005 12:00:00 EST 
      ORGANIZATION "IETF" 
      CONTACT-INFO 
          "        Thomas <PERSON>u 
                   Cisco Systems, Inc. 
           Email:  <EMAIL> 
    
                   Zafar Ali  
                   Cisco Systems, Inc. 
           Email:  <EMAIL> 
          " 
      DESCRIPTION 
          "Bidirectional Forwarding Management Information Base." 
    
      -- Revision history. 
      REVISION 
          "200508221200Z"  -- 04 August 2005 12:00:00 EST 
      DESCRIPTION 
      "Initial version. Published as RFC xxxx." -- RFC-editor pls fill 
                                                -- in xxxx
   ::= { jnxBfdExperiment 1 }

   -- Top level components of this MIB module. 

   bfdNotifications OBJECT IDENTIFIER ::= { bfdMIB 0 } 
    
   bfdObjects       OBJECT IDENTIFIER ::= { bfdMIB 1 } 
    
   bfdConformance   OBJECT IDENTIFIER ::= { bfdMIB 3 } 
    
   bfdScalarObjects OBJECT IDENTIFIER ::= { bfdObjects 1 } 
    
    
   --  Textual Conventions 
    
   BfdSessIndexTC ::= TEXTUAL-CONVENTION 
      DISPLAY-HINT "d" 
      STATUS        current 
      DESCRIPTION 
          "An index used to uniquely identify BFD sessions." 
      SYNTAX Unsigned32 (1..4294967295) 
    
   BfdInterval ::= TEXTUAL-CONVENTION 
      STATUS        current 
      DESCRIPTION 
          "The BFD interval delay in microseconds." 
      SYNTAX        Unsigned32 (1..4294967295) 
    
   BfdDiag ::=      TEXTUAL-CONVENTION 
      STATUS        current 
      DESCRIPTION 
          "A common BFD diagnostic code." 
    
      SYNTAX INTEGER { noDiagnostic(1),  
                       controlDetectionTimeExpired(2),  
                       echoFunctionFailed(3),  
                       neighborSignaledSessionDown(4),  
                       forwardingPlaneReset(5), 
                       pathDown(6),  
                       concatenatedPathDown(7), 
                       administrativelyDown(8),  
                       reverseConcatenatedPathDown (9) 
                     } 
    
        --  BFD General Variables 
    
        --  These parameters apply globally to the Router's 
        --  BFD Process. 
    
   bfdAdminStatus OBJECT-TYPE 
      SYNTAX   INTEGER { enabled(1), disabled(2) }         
--      MAX-ACCESS   read-write 
      MAX-ACCESS   read-only 
      STATUS   current 
      DESCRIPTION 
              "The global administrative status of BFD in this router.  
               The value 'enabled' denotes that the BFD Process is 
               active on at least one interface; 'disabled' disables  
               it on all interfaces." 
      DEFVAL { enabled }  
      ::= { bfdScalarObjects 1 } 
    
   bfdVersionNumber OBJECT-TYPE 
      SYNTAX   Unsigned32 
      MAX-ACCESS   read-only 
      STATUS   current 
      DESCRIPTION 
              "The current version number of the BFD protocol." 
      REFERENCE 
              " BFD Version 0 (draft-katz-ward-bfd-02.txt)" 
      DEFVAL { 0 } 
      ::= { bfdScalarObjects 3 } 
    
    
   --  BFD Session Table 
   --  The BFD Session Table specifies BFD session specific 
   --  information.  
    
   bfdSessTable OBJECT-TYPE 
      SYNTAX   SEQUENCE OF BfdSessEntry 
      MAX-ACCESS   not-accessible 
      STATUS   current 
      DESCRIPTION 
              "The BFD Session Table describes the BFD sessions." 
      REFERENCE 
              "BFD Version 0 (draft-katz-ward-bfd-02.txt)" 
      ::= { bfdObjects 2 } 
    
   bfdSessEntry OBJECT-TYPE 
      SYNTAX      BfdSessEntry 
      MAX-ACCESS  not-accessible 
      STATUS      current 
      DESCRIPTION 
              "The BFD Session Entry describes BFD session." 
      INDEX { bfdSessIndex } 
      ::= { bfdSessTable 1 } 
    
   BfdSessEntry ::= SEQUENCE { 
      bfdSessIndex                    BfdSessIndexTC, 
      bfdSessApplicationId            Unsigned32, 
      bfdSessDiscriminator            Unsigned32, 
      bfdSessRemoteDiscr              Unsigned32,  
      bfdSessUdpPort                  InetPortNumber, 
      bfdSessState                    INTEGER, 
      bfdSessRemoteHeardFlag          TruthValue,   
      bfdSessDiag                     Unsigned32, 
      bfdSessOperMode                 INTEGER, 
      bfdSessDemandModeDesiredFlag    TruthValue, 
      bfdSessEchoFuncModeDesiredFlag  TruthValue, 
      bfdSessControlPlanIndepFlag     TruthValue, 
      bfdSessAddrType                 InetAddressType, 
      bfdSessAddr                     InetAddress,  
      bfdSessDesiredMinTxInterval     BfdInterval, 
      bfdSessDesiredMinRxInterval     BfdInterval, 
      bfdSessDesiredMinEchoRxInterval BfdInterval, 
      bfdSessDetectMult               Unsigned32, 
      bfdSessStorType                 StorageType, 
      bfdSessRowStatus                RowStatus, 
      bfdSessAuthPresFlag             TruthValue, 
      bfdSessAuthenticationType       INTEGER 
   } 
    
   bfdSessIndex  OBJECT-TYPE 
      SYNTAX        BfdSessIndexTC 
      MAX-ACCESS    not-accessible
      STATUS        current 
      DESCRIPTION 
              "This object contains an index used to represent a 
               unique BFD session on this device." 
      ::= { bfdSessEntry 1 } 
    
   bfdSessApplicationId  OBJECT-TYPE 
      SYNTAX        Unsigned32 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object contains an index used to indicate 
               a local application which owns or maintains this 
               BFD session. For instance, the MPLS VPN process may 
               maintain a subset of the total number of BFD 
               sessions.  This application ID provides a convenient 
               way to segregate sessions by the applications which 
               maintain them." 
      ::= { bfdSessEntry 2 } 
     
   bfdSessDiscriminator            OBJECT-TYPE 
      SYNTAX        Unsigned32 (1..4294967295) 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object specifies the local discriminator for this BFD  
               session, used to uniquely identify it." 
      ::= { bfdSessEntry 3 } 
    
   bfdSessRemoteDiscr OBJECT-TYPE 
      SYNTAX        Unsigned32 (1..4294967295) 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object specifies the session discriminator chosen  
               by the remote system for this BFD session." 
      ::= { bfdSessEntry 4 } 
    
   bfdSessUdpPort OBJECT-TYPE 
      SYNTAX      InetPortNumber 
--      MAX-ACCESS  read-create 
      MAX-ACCESS  read-only 
      STATUS      current 
      DESCRIPTION 
              "The UDP Port for BFD. The default value is the 
               well-known value for this port." 
      REFERENCE 
              "draft-katz-ward-bfd-02.txt and 
               draft-raggarwa-mpls-bfd-00.txt" 
      DEFVAL { 0 } 
      ::= { bfdSessEntry 5 } 
    
   bfdSessState OBJECT-TYPE 
      SYNTAX   INTEGER { 
               adminDown(1),  
               down(2),  
               init(3),  
               up(4)
      } 
      MAX-ACCESS   read-only 
      STATUS   current 
      DESCRIPTION 
              "The perceived state of the BFD session." 
      ::= { bfdSessEntry 6 } 
    
   bfdSessRemoteHeardFlag OBJECT-TYPE 
      SYNTAX        TruthValue 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object specifies status of BFD packet reception from  
               the remote system. Specifically, it is set to true(1) if 
               the local system is actively receiving BFD packets from the  
               remote system, and is set to false(0) if the local system  
               has not received BFD packets recently (within the detection  
               time) or if the local system is attempting to tear down 
               the BFD session." 
      ::= { bfdSessEntry 7 } 
    
   bfdSessDiag OBJECT-TYPE 
      SYNTAX      Unsigned32 
      MAX-ACCESS  accessible-for-notify 
      STATUS      current 
      DESCRIPTION 
              "A diagnostic code specifying the local system's reason  
               for the last transition of the session from up(1)  
               to some other state." 
      ::= { bfdSessEntry 8 } 

   bfdSessOperMode OBJECT-TYPE 
      SYNTAX   INTEGER { asyncModeWEchoFun(1),  
                         asynchModeWOEchoFun(2),  
                         demandModeWEchoFunction(3),  
                         demandModeWOEchoFunction(4)  
      } 
      MAX-ACCESS   read-only 
      STATUS   current 
      DESCRIPTION 
              "This object specifies current operating mode that BFD  
               session is operating in. 
        
               A value of AsyncModeWEchoFun(1) ... 
               A value of AsynchModeWOEchoFun(2) ... 
               A value of DemandModeWEchoFunction(3) ... 
               A value of DemandModeWOEchoFunction(4) ... 
              " 
      ::= { bfdSessEntry 9 } 
    
   bfdSessDemandModeDesiredFlag OBJECT-TYPE 
      SYNTAX        TruthValue 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object indicates that the local system's  
               desire to use Demand mode. Specifically, it is set  
               to true(1) if the local system wishes to use  
               Demand mode or false(0) if not" 
      DEFVAL { false } 
      ::= { bfdSessEntry 10 } 
    
   bfdSessEchoFuncModeDesiredFlag OBJECT-TYPE 
      SYNTAX        TruthValue 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object indicates that the local system's  
               desire to use Echo mode. Specifically, it is set  
               to true(1) if the local system wishes to use  
               Echo mode or false(0) if not" 
      DEFVAL { false } 
      ::= { bfdSessEntry 11 } 
    
   bfdSessControlPlanIndepFlag     OBJECT-TYPE 
      SYNTAX        TruthValue 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object indicates that the local system's  
               ability to continue to function through a disruption of  
               the control plane. Specifically, it is set  
               to true(1) if the local system BFD implementation is 
               independent of the control plane. Otherwise, the  
               value is set to false(0)" 
      DEFVAL { false } 
      ::= { bfdSessEntry 12 } 
    
   bfdSessAddrType  OBJECT-TYPE 
      SYNTAX        InetAddressType 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
           "This object specifies IP address of the interface  
            associated with this BFD session.  
    
            Only values unknown(0), ipv4(1) or ipv6(2) 
            have to be supported.  
    
            A value of unknown(0) is allowed only when  
            the outgoing interface is of type point-to-point, or 
            when the BFD session is not associated with a specific  
            interface. 
    
            If any other unsupported values are attempted in a set 
            operation, the agent MUST return an inconsistentValue  
            error. 
      " 
      ::= { bfdSessEntry 13 } 
    
   bfdSessAddr OBJECT-TYPE 
      SYNTAX        InetAddress 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
           "This object specifies IP address of the interface  
            associated with this BFD session.  
            It can also be used to enabled BFD on a specific  
            interface. The value is set to zero when BFD session is not  
            associated with a specific interface. " 
      ::= { bfdSessEntry 14 } 
    
   bfdSessDesiredMinTxInterval OBJECT-TYPE 
      SYNTAX    BfdInterval 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS    current 
      DESCRIPTION 
       "This object specifies the minimum interval, in  
        microseconds, that the local system would like to use when 
             transmitting BFD Control packets." 
      ::= { bfdSessEntry 15 } 
    
   bfdSessDesiredMinRxInterval OBJECT-TYPE 
      SYNTAX        BfdInterval 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
            "This object specifies the minimum interval, in  
             microseconds, between received  BFD Control packets the  
             local system is capable of supporting." 
      ::= { bfdSessEntry 16 } 
    
   bfdSessDesiredMinEchoRxInterval OBJECT-TYPE 
      SYNTAX        BfdInterval 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
            "This object specifies the minimum interval, in  
             microseconds, between received BFD Echo packets that this 
             system is capable of supporting." 
      ::= { bfdSessEntry 17 } 
    
   bfdSessDetectMult OBJECT-TYPE 
      SYNTAX        Unsigned32 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
            "This object specifies the Detect time multiplier." 
      ::= { bfdSessEntry 18 } 
    
   bfdSessStorType  OBJECT-TYPE 
      SYNTAX        StorageType 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
          "This variable indicates the storage type for this 
           object. Conceptual rows having the value  
           'permanent' need not allow write-access to any  
           columnar objects in the row."          
      ::= { bfdSessEntry 19 } 
    
   bfdSessRowStatus OBJECT-TYPE 
      SYNTAX        RowStatus 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
            "This variable is used to create, modify, and/or 
             delete a row in this table. When a row in this 
             table has a row in the active(1) state, no  
             objects in this row can be modified except the 
             bfdSessRowStatus and bfdSessStorageType." 
      ::= { bfdSessEntry 20 } 
    
   bfdSessAuthPresFlag OBJECT-TYPE 
      SYNTAX        TruthValue 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
              "This object indicates that the local system's  
               desire to use Authentication. Specifically, it is set  
               to true(1) if the local system wishes the session  
               to be authenticated or false(0) if not" 
      DEFVAL { false } 
      ::= { bfdSessEntry 21 } 
 
   bfdSessAuthenticationType OBJECT-TYPE 
      SYNTAX      INTEGER { simplePassword(1),  
                            keyedMD5(2),  
                            meticulousKeyedMD5(3),  
                            keyedSHA1(4),  
                            meticulousKeyedSHA1(5) 
      } 
--      MAX-ACCESS    read-create 
      MAX-ACCESS    read-only 
      STATUS      current 
      DESCRIPTION 
           "The Authentication Type used for this BFD session. This 
            field is valid only when the Authentication Present bit is set" 
      ::= { bfdSessEntry 22 } 
 
   -- BFD Session Performance Table 
    
   bfdSessPerfTable OBJECT-TYPE 
      SYNTAX        SEQUENCE OF BfdSessPerfEntry 
      MAX-ACCESS    not-accessible 
      STATUS        current 
      DESCRIPTION 
          "This table specifies BFD Session performance counters." 
      ::= { bfdObjects 3 } 
    
   bfdSessPerfEntry OBJECT-TYPE 
      SYNTAX        BfdSessPerfEntry 
      MAX-ACCESS    not-accessible 
      STATUS        current 
      DESCRIPTION 
          "An entry in this table is created by a BFD-enabled node for  
           every BFD Session. bfdCounterDiscontinuityTime is used to  
           indicate potential discontinuity for all counter objects  
           in this table." 
      AUGMENTS    { bfdSessEntry } 
      ::= { bfdSessPerfTable 1 } 
    
   BfdSessPerfEntry ::= SEQUENCE { 
     bfdSessPerfPktIn               Counter32,  
     bfdSessPerfPktOut              Counter32, 
     bfdSessUpTime                  TimeStamp,  
     bfdSessPerfLastSessDownTime    TimeStamp, 
     bfdSessPerfLastCommLostDiag    BfdDiag, 
     bfdSessPerfSessUpCount         Counter32, 
     bfdSessPerfDiscTime            TimeStamp, 
    
     -- High Capacity Counters 
     bfdSessPerfPktInHC             Counter64,  
     bfdSessPerfPktOutHC            Counter64 
     } 
    
   -- Ed Note: should we add per-diag code counts here,  
    
   bfdSessPerfPktIn OBJECT-TYPE  
      SYNTAX      Counter32 
      MAX-ACCESS  read-only 
      STATUS      current 
      DESCRIPTION 
           "The total number of BFD messages received for this BFD 
   session." 
      ::= { bfdSessPerfEntry 1 } 
    
   bfdSessPerfPktOut OBJECT-TYPE 
      SYNTAX      Counter32 
      MAX-ACCESS  read-only 
      STATUS      current 
      DESCRIPTION 
           "The total number of BFD messages sent for this BFD session." 
      ::= { bfdSessPerfEntry 2 } 
    
   bfdSessUpTime OBJECT-TYPE 
      SYNTAX       TimeStamp 
      MAX-ACCESS   read-only 
      STATUS       current 
      DESCRIPTION 
           "The value of sysUpTime on the most recent occasion at which 
            the session came up. If no such up event exists this object 
            contains a zero value." 
      ::= { bfdSessPerfEntry 3 } 
 
   bfdSessPerfLastSessDownTime OBJECT-TYPE 
      SYNTAX       TimeStamp 
      MAX-ACCESS   read-only 
      STATUS       current 
      DESCRIPTION 
           "The value of sysUpTime on the most recent occasion at which 
            the last time communication was lost with the neighbor. If  
            no such down event exist this object contains a zero value." 
      ::= { bfdSessPerfEntry 4 } 
    
   bfdSessPerfLastCommLostDiag  OBJECT-TYPE 
      SYNTAX       BfdDiag 
      MAX-ACCESS   read-only 
      STATUS       current 
      DESCRIPTION 
           "The BFD diag code for the last time communication was lost  
            with the neighbor. If no such down event exists this object  
            contains a zero value." 
      ::= { bfdSessPerfEntry 5 }     
    
   bfdSessPerfSessUpCount OBJECT-TYPE 
      SYNTAX       Counter32 
      MAX-ACCESS   read-only 
      STATUS       current 
      DESCRIPTION 
           "The number of times this session has gone into the Up 
            state since the router last rebooted." 
      ::= { bfdSessPerfEntry 6 } 
    
   bfdSessPerfDiscTime OBJECT-TYPE 
      SYNTAX       TimeStamp 
      MAX-ACCESS   read-only 
      STATUS       current 
      DESCRIPTION 
         "The value of sysUpTime on the most recent occasion at 
          which any one or more of the session counters suffered 
          a discontinuity.  

          The relevant counters are the specific instances associated  
          with this BFD session of any Counter32 object contained in 
          the BfdSessPerfTable. If no such discontinuities have occurred  
          since the last re-initialization of the local management 
          subsystem, then this object contains a zero value." 
      ::= { bfdSessPerfEntry 7 } 

   bfdSessPerfPktInHC OBJECT-TYPE  
      SYNTAX       Counter64 
      MAX-ACCESS   read-only 
      STATUS       current 
      DESCRIPTION 
           "This value represents the total number of BFD messages 
            received for this BFD session. It MUST be equal to the 
            least significant 32 bits of bfdSessPerfPktIn 
            if bfdSessPerfPktInHC is supported according to 
            the rules spelled out in RFC2863." 
      ::= { bfdSessPerfEntry 8 } 
    
   bfdSessPerfPktOutHC OBJECT-TYPE 
      SYNTAX       Counter64 
      MAX-ACCESS   read-only 
      STATUS       current 
      DESCRIPTION 
           "This value represents the total number of  
            total number of BFD messages transmitted for this  
            BFD session. It MUST be equal to the 
           least significant 32 bits of bfdSessPerfPktIn 
           if bfdSessPerfPktOutHC is supported according to 
           the rules spelled out in RFC2863." 
      ::= { bfdSessPerfEntry 9 } 
    
    
   -- BFD Session Mapping Table 
    
   bfdSessMapTable OBJECT-TYPE 
      SYNTAX   SEQUENCE OF BfdSessMapEntry 
      MAX-ACCESS   not-accessible 
      STATUS   current 
      DESCRIPTION 
              "The BFD Session Mapping Table maps the complex 
               indexing of the BFD sessions to the flat 
               BFDIndex used in the BfdSessionTable. 
    
               Implementors need to be aware that if the value of 
               the bfdSessAddr (an OID) has more  
               that 111 sub-identifiers, then OIDs of column 
               instances in this table will have more than 128 
               sub-identifiers and cannot be accessed using SNMPv1, 
               SNMPv2c, or SNMPv3. 
              " 
      REFERENCE 
              "BFD Version 0 (draft-katz-ward-bfd-02.txt)" 
      ::= { bfdObjects 4 } 
    
   bfdSessMapEntry OBJECT-TYPE 
      SYNTAX   BfdSessMapEntry 
      MAX-ACCESS   not-accessible 
      STATUS   current 
      DESCRIPTION 
              "The BFD Session Entry describes BFD session 
               that is mapped to this index.

               Implementors need to be aware that if the value of
               the mplsInSegmentMapLabelPtrIndex (an OID) has more
               that 111 sub-identifiers, then OIDs of column
               instances in this table will have more than 128
               sub-identifiers and cannot be accessed using SNMPv1,
               SNMPv2c, or SNMPv3." 
      INDEX { bfdSessApplicationId, 
              bfdSessDiscriminator, 
              bfdSessAddrType, 
              bfdSessAddr 
            } 
      ::= { bfdSessMapTable 1 } 
    
   BfdSessMapEntry ::= SEQUENCE { 
      bfdSessMapBfdIndex            BfdSessIndexTC 
   } 
    
   bfdSessMapBfdIndex OBJECT-TYPE 
      SYNTAX        BfdSessIndexTC 
      MAX-ACCESS    read-only 
      STATUS        current 
      DESCRIPTION 
          "This object specifies the BfdIndex referred to by 
           the indexes of this row. In essence, a mapping is 
           provided between these indexes and the BfdSessTable." 
      ::= { bfdSessMapEntry 1 } 
    
   -- Notification Configuration 
    
   bfdSessNotificationsEnable OBJECT-TYPE 
      SYNTAX        TruthValue 
--      MAX-ACCESS   read-write 
      MAX-ACCESS   read-only 
      STATUS        current 
      DESCRIPTION 
          "If this object is set to true(1), then it enables 
           the emission of bfdSessUp and bfdSessDown 
           notifications; otherwise these notifications are not 
           emitted." 
      REFERENCE 
          "See also RFC3413 for explanation that  
           notifications are under the ultimate control of the  
           MIB modules in this document." 
      DEFVAL { false } 
      ::= { bfdScalarObjects 4 } 
    
   bfdSessUp NOTIFICATION-TYPE 
      OBJECTS     { bfdSessDiag, -- low range value 
                    bfdSessDiag  -- high range value 
      } 
      STATUS      current 
      DESCRIPTION 
          "This notification is generated when the 
           bfdSessState object for one or more contiguous 
           entries in bfdSessTable are about to enter the up(2) 
           state from some other state. The included values of 
           bfdSessDiag MUST both be set equal to this 
           new state (i.e: up(1)).  The two instances of  
           bfdSessDiag in this notification indicate the range  
           of indexes that are affected.  Note that all the indexes  
           of the two ends of the range can be derived from the 
           instance identifiers of these two objects.  For the 
           cases where a contiguous range of sessions 
           have transitioned into the up(1) state at roughly 
           the same time, the device SHOULD issue a single 
           notification for each range of contiguous indexes in 
           an effort to minimize the emission of a large number 
           of notifications.  If a notification has to be 
           issued for just a single bfdSessEntry, then 
           the instance identifier (and values) of the two 
           bfdSessDiag objects MUST be the identical." 
      ::= { bfdNotifications 1 } 
    
   bfdSessDown NOTIFICATION-TYPE 
      OBJECTS     { bfdSessDiag, -- low range value 
                    bfdSessDiag  -- high range value 
      } 
      STATUS      current 
      DESCRIPTION 
          "This notification is generated when the 
           bfdSessState object for one or more contiguous 
           entries in bfdSessTable are about to enter the down(4) 
           or adminDown(5) states from some other state. The included  
           values of bfdSessDiag MUST both be set equal to this 
           new state (i.e: down(4) or adminDown(5)).  The two instances  
           of bfdSessDiag in this notification indicate the range  
           of indexes that are affected.  Note that all the indexes  
           of the two ends of the range can be derived from the 
           instance identifiers of these two objects.  For 
           cases where a contiguous range of sessions 
           have transitioned into the down(4) or adminDown(5) states  
           at roughly the same time, the device SHOULD issue a single 
           notification for each range of contiguous indexes in 
           an effort to minimize the emission of a large number 
           of notifications.  If a notification has to be 
           issued for just a single bfdSessEntry, then 
           the instance identifier (and values) of the two 
           bfdSessDiag objects MUST be the identical." 
      ::= { bfdNotifications 2 } 
    
   -- Ed Note: We need to add notification for changes  
   -- when the two ends automatically negotiate to a new detection time 
   -- value or when detection multiplier changes.  
   -- Similarly, changes in the operating mode (bfdSessOperMode)  
   -- also need to be notified.  
    
   -- Module compliance. 
 
   bfdGroups 
      OBJECT IDENTIFIER ::= { bfdConformance 1 } 
    
   bfdCompliances 
      OBJECT IDENTIFIER ::= { bfdConformance 2 } 
    
   -- Compliance requirement for fully compliant implementations.  
    
   bfdModuleFullCompliance MODULE-COMPLIANCE 
      STATUS       current 
      DESCRIPTION "Compliance statement for agents that provide full 
                   support for BFD-MIB. Such devices can 
                   then be monitored and also be configured using 
                   this MIB module." 
    
      MODULE -- This module. 
      MANDATORY-GROUPS { 
            bfdSessionGroup, 
            bfdSessionPerfGroup, 
            bfdSessionPerfHCGroup, 
            bfdNotificationGroup 
      } 
    
--      GROUP        bfdSessionPerfHCGroup 
--      DESCRIPTION "This group is mandatory for those bfdPerfTable  
--                   entries for which any of the objects  
--                   bfdSessPerfPktInHC or bfdSessPerfPktOutHC  
--                   wraps around too quickly  
--                   based on the criteria specified in RFC 2863 for  
--                   high-capacity counters." 
--    
--      GROUP        bfdNotificationGroup 
--      DESCRIPTION "This group is only mandatory for those  
--                   implementations which can efficiently implement  
--                   the notifications contained in this group." 
--    
      OBJECT       bfdSessAddrType 
      SYNTAX       InetAddressType {
                                    unknown(0),
                                    ipv4(1),
                                    ipv6(2)
                                    }
      DESCRIPTION "Only unknown(0), ipv4(1) and ipv6(2) support  
                   is required." 
    
      OBJECT       bfdSessAddr 
      SYNTAX       InetAddress (SIZE(0|4|16)) 
      DESCRIPTION "An implementation is only required to support  
                   unknown(0), ipv4(1) and ipv6(2) sizes." 
 
      ::= { bfdCompliances 1 } 
    
   -- Read-Only Conformance TBD... 
    
    
   -- Units of conformance. 
    
   bfdSessionGroup OBJECT-GROUP 
      OBJECTS { 
                bfdSessNotificationsEnable, 
                bfdAdminStatus, 
                bfdVersionNumber, 
                bfdSessApplicationId,  
                bfdSessDiscriminator,             
                bfdSessAddrType, 
                bfdSessAddr,                      
                bfdSessRemoteDiscr,               
                bfdSessUdpPort,
                bfdSessState,                     
                bfdSessRemoteHeardFlag,           
                bfdSessDiag,                      
                bfdSessOperMode,                  
                bfdSessDemandModeDesiredFlag,     
                bfdSessEchoFuncModeDesiredFlag, 
                bfdSessControlPlanIndepFlag, 
                bfdSessDesiredMinTxInterval,      
                bfdSessDesiredMinRxInterval,      
                bfdSessDesiredMinEchoRxInterval,  
                bfdSessDetectMult,                
                bfdSessStorType,                 
                bfdSessRowStatus, 
                bfdSessMapBfdIndex,  
                bfdSessAuthPresFlag,  
                bfdSessAuthenticationType 
      } 
      STATUS  current 
      DESCRIPTION 
             "Collection of objects needed for BFD sessions." 
      ::= { bfdGroups 1 } 
    
    
   bfdSessionPerfGroup OBJECT-GROUP 
      OBJECTS { 
                bfdSessPerfPktIn, 
                bfdSessPerfPktOut, 
                bfdSessUpTime,
                bfdSessPerfLastSessDownTime, 
                bfdSessPerfLastCommLostDiag, 
                bfdSessPerfSessUpCount,
                bfdSessPerfDiscTime 
      } 
      STATUS  current 
      DESCRIPTION 
             "Collection of objects needed to monitor the 
              performance of BFD sessions." 
      ::= { bfdGroups 2 } 
    
   bfdSessionPerfHCGroup OBJECT-GROUP 
      OBJECTS { 
               bfdSessPerfPktInHC, 
               bfdSessPerfPktOutHC  
      } 
      STATUS  current 
      DESCRIPTION 
             "Collection of objects needed to monitor the 
              performance of BFD sessions for which the 
              values of bfdSessPerfPktIn, bfdSessPerfPktOut  
              wrap around too quickly." 
      ::= { bfdGroups 3 } 
    
   bfdNotificationGroup NOTIFICATION-GROUP 
      NOTIFICATIONS { 
                bfdSessUp, 
                bfdSessDown 
      } 
      STATUS  current 
      DESCRIPTION 
             "Set of notifications implemented in this  
              module." 

      ::= { bfdGroups 4 }          
    
   END 

