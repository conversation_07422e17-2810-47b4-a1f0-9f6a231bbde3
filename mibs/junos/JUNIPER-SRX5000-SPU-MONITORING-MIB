--
-- Juniper Enterprise specific MIB: SRX5000 SPU monitoring MIB
--
-- Copyright (c) 2008-2010, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-SRX5000-SPU-MONITORING-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Gauge32, Unsigned32
        FROM SNMPv2-SMI
    CounterBasedGauge64
        FROM HCNUM-TC
    DisplayString 
        FROM SNMPv2-TC
    jnxJsSPUMonitoringRoot
        FROM JUNIPER-JS-SMI;

jnxJsSPUMonitoringMIB MODULE-IDENTITY
    LAST-UPDATED "201912090000Z" -- Dec 09 00:00:00 2019 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"
    DESCRIPTION
        "This is Juniper Networks' implementation of enterprise
                 specific MIB for SRX5000 SPU monitoring."
    REVISION
        "201207040000Z" -- Jul 04 00:00:00 2012 UTC
    DESCRIPTION
        "add MIB for session counters of IPv4 and IPv6 respectively."
    REVISION
        "201912090000Z" -- Dec 09 00:00:00 2019 UTC
    DESCRIPTION
        "add SPU thread statistics."

    ::= { jnxJsSPUMonitoringRoot 1 }

    jnxJsSPUMonitoringObjectsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF JnxJsSPUMonitoringObjectsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table exposes SPUs utilization statistics." 
        ::= { jnxJsSPUMonitoringMIB 1 }

    jnxJsSPUMonitoringObjectsEntry OBJECT-TYPE
        SYNTAX        JnxJsSPUMonitoringObjectsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "Each entry collects CPU/Memory utilization for a SPU."
        INDEX   { jnxJsSPUMonitoringIndex }
        ::= { jnxJsSPUMonitoringObjectsTable 1 }

    JnxJsSPUMonitoringObjectsEntry ::= SEQUENCE
    {
        jnxJsSPUMonitoringIndex         Unsigned32,
        jnxJsSPUMonitoringFPCIndex      Unsigned32,
        jnxJsSPUMonitoringSPUIndex      Unsigned32,
        jnxJsSPUMonitoringCPUUsage      Unsigned32,
        jnxJsSPUMonitoringMemoryUsage   Unsigned32,
        jnxJsSPUMonitoringCurrentFlowSession	Unsigned32,
        jnxJsSPUMonitoringMaxFlowSession	Unsigned32,
        jnxJsSPUMonitoringCurrentCPSession	Unsigned32,
        jnxJsSPUMonitoringMaxCPSession	Unsigned32,
        jnxJsSPUMonitoringNodeIndex     Unsigned32,
        jnxJsSPUMonitoringNodeDescr     DisplayString,
        jnxJsSPUMonitoringFlowSessIPv4	Unsigned32,
        jnxJsSPUMonitoringFlowSessIPv6	Unsigned32,
        jnxJsSPUMonitoringCPSessIPv4	Unsigned32,
        jnxJsSPUMonitoringCPSessIPv6	Unsigned32,
        jnxJsSPUMonitoringSPUThreadsNumber  Unsigned32
    }

    jnxJsSPUMonitoringIndex OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                "SPU's overall index in platform."
        ::= { jnxJsSPUMonitoringObjectsEntry 1 }

    jnxJsSPUMonitoringFPCIndex OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Which FPC SPU is on."
        ::= { jnxJsSPUMonitoringObjectsEntry 2 }

    jnxJsSPUMonitoringSPUIndex OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "SPU'Index inside the FPC."
        ::= { jnxJsSPUMonitoringObjectsEntry 3 }

    jnxJsSPUMonitoringCPUUsage OBJECT-TYPE
        SYNTAX  Unsigned32
        UNITS   "percent"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Current SPU(CPU) Utilization in percentage."
        ::= { jnxJsSPUMonitoringObjectsEntry 4 }

    jnxJsSPUMonitoringMemoryUsage OBJECT-TYPE
        SYNTAX  Unsigned32
        UNITS   "percent"
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Current memory usage of SPU(CPU) in percentage."
        ::= { jnxJsSPUMonitoringObjectsEntry 5 }
	
    jnxJsSPUMonitoringCurrentFlowSession OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Current flow session number of SPU."
        ::= { jnxJsSPUMonitoringObjectsEntry 6 }

    jnxJsSPUMonitoringMaxFlowSession OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Max flow session number of SPU."
        ::= { jnxJsSPUMonitoringObjectsEntry 7 }

    jnxJsSPUMonitoringCurrentCPSession OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Current CP session number of SPU."
        ::= { jnxJsSPUMonitoringObjectsEntry 8 }

    jnxJsSPUMonitoringMaxCPSession OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Max CP session number of SPU."
        ::= { jnxJsSPUMonitoringObjectsEntry 9 }

    jnxJsSPUMonitoringNodeIndex OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "This attribute is used to identify a chassis. A chassis can 
                 be configured in a single or cluster mode.  When it is in a 
                 cluster mode, the chassis can be denote as a cluster node."
        ::= { jnxJsSPUMonitoringObjectsEntry 10 }

    jnxJsSPUMonitoringNodeDescr OBJECT-TYPE
        SYNTAX        DisplayString (SIZE(1..255))
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "This attribute is used to describe the chassis/cluster 
             node information.  Chassis can be configured as a single, or 
             cluster node.  When it is cluster mode, the chassis can be 
             denoted as a cluster node."
        ::= { jnxJsSPUMonitoringObjectsEntry 11 }

    jnxJsSPUMonitoringFlowSessIPv4 OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
        "Current IPv4 flow session number of SPU."
        ::= { jnxJsSPUMonitoringObjectsEntry 12 }
          
 
    jnxJsSPUMonitoringFlowSessIPv6 OBJECT-TYPE
          SYNTAX  Unsigned32
          MAX-ACCESS  read-only
          STATUS  current
          DESCRIPTION
                  "Current IPv6 flow session number of SPU."
          ::= { jnxJsSPUMonitoringObjectsEntry 13 }
          
          
    jnxJsSPUMonitoringCPSessIPv4 OBJECT-TYPE
          SYNTAX  Unsigned32
          MAX-ACCESS  read-only
          STATUS  current
          DESCRIPTION
                  "Current IPv4 CP session number of SPU."
          ::= { jnxJsSPUMonitoringObjectsEntry 14 }
          
 
    jnxJsSPUMonitoringCPSessIPv6 OBJECT-TYPE
          SYNTAX  Unsigned32
          MAX-ACCESS  read-only
          STATUS  current
          DESCRIPTION
                  "Current IPv6 CP session number of SPU."
          ::= { jnxJsSPUMonitoringObjectsEntry 15 }

    jnxJsSPUMonitoringSPUThreadsNumber OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "Number of threads for an SPU."
        ::= { jnxJsSPUMonitoringObjectsEntry 16 }

    jnxJsSPUMonitoringCurrentTotalSession OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "System level total session in use."
        ::= { jnxJsSPUMonitoringMIB 2 }

    
    jnxJsSPUMonitoringMaxTotalSession OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "System level max session possible."
        ::= { jnxJsSPUMonitoringMIB 3 }


    jnxSPUClusterObjectsTable OBJECT-TYPE
        SYNTAX        SEQUENCE OF JnxSPUClusterObjectsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "This table exposes SPU monitoring objects in HA cluster."
        ::= { jnxJsSPUMonitoringMIB 4 }


    jnxSPUClusterObjectsEntry OBJECT-TYPE
        SYNTAX        JnxSPUClusterObjectsEntry
        MAX-ACCESS    not-accessible
        STATUS        current
        DESCRIPTION
            "Each entry collects SPU monitoring contents in HA cluster."
        INDEX   { jnxJsClusterMonitoringNodeIndex }
        ::= { jnxSPUClusterObjectsTable 1 }

    JnxSPUClusterObjectsEntry ::= SEQUENCE
    {
        jnxJsClusterMonitoringNodeIndex     Unsigned32,
        jnxJsClusterMonitoringNodeDescr     DisplayString,
        jnxJsNodeCurrentTotalSession 	Unsigned32,
        jnxJsNodeMaxTotalSession	Unsigned32,
        jnxJsNodeSessionCreationPerSecond	CounterBasedGauge64,
        jnxJsNodeSessCreationPerSecIPv4	    CounterBasedGauge64,
        jnxJsNodeSessCreationPerSecIPv6	    CounterBasedGauge64,
        jnxJsNodeCurrentTotalSessIPv4 	Unsigned32,
        jnxJsNodeCurrentTotalSessIPv6 	Unsigned32
    }

    jnxJsClusterMonitoringNodeIndex OBJECT-TYPE
        SYNTAX  Unsigned32
        MAX-ACCESS not-accessible
        STATUS  current
        DESCRIPTION
                "This attribute is used to identify a chassis. A chassis can 
                 be configured in a single or cluster mode.  When it is in a 
                 cluster mode, the chassis can be denote as a cluster node."
        ::= { jnxSPUClusterObjectsEntry 1 }

    jnxJsClusterMonitoringNodeDescr OBJECT-TYPE
        SYNTAX        DisplayString (SIZE(1..255))
        MAX-ACCESS    read-only
        STATUS        current
        DESCRIPTION
            "This attribute is used to describe the chassis/cluster 
             node information.  Chassis can be configured as a single, or 
             cluster node.  When it is cluster mode, the chassis can be 
             denoted as a cluster node."
        ::= { jnxSPUClusterObjectsEntry 2 }
	
    jnxJsNodeCurrentTotalSession OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Node total session in use."
        ::= { jnxSPUClusterObjectsEntry 3 }

    jnxJsNodeMaxTotalSession OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Node max session possible."
        ::= { jnxSPUClusterObjectsEntry 4 }
	
    jnxJsNodeSessionCreationPerSecond OBJECT-TYPE
	SYNTAX CounterBasedGauge64
	MAX-ACCESS  read-only
	STATUS  current
	DESCRIPTION
		"Node average session created in last 96 seconds."
	::= { jnxSPUClusterObjectsEntry 5 }

 
    jnxJsNodeSessCreationPerSecIPv4 OBJECT-TYPE
  	SYNTAX CounterBasedGauge64
  	MAX-ACCESS  read-only
  	STATUS  current
  	DESCRIPTION
  		"Node average IPv4 session created in last 96 seconds."
  	::= { jnxSPUClusterObjectsEntry 6 }

    jnxJsNodeSessCreationPerSecIPv6 OBJECT-TYPE
  	SYNTAX CounterBasedGauge64
  	MAX-ACCESS  read-only
  	STATUS  current
  	DESCRIPTION
  		"Node average IPv6 session created in last 96 seconds."
  	::= { jnxSPUClusterObjectsEntry 7 }

    jnxJsNodeCurrentTotalSessIPv4 OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Node total IPv4 session in use."
        ::= { jnxSPUClusterObjectsEntry 8 }
    
    jnxJsNodeCurrentTotalSessIPv6 OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "Node total IPv6 session in use."
        ::= { jnxSPUClusterObjectsEntry 9 }
    
    jnxJsSPUMonitoringTotalSessIPv4 OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "System level total IPv4 session in use."
        ::= { jnxJsSPUMonitoringMIB 5 }
    
    jnxJsSPUMonitoringTotalSessIPv6 OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS  read-only
        STATUS  current
        DESCRIPTION
                "System level total IPv6 session in use."
        ::= { jnxJsSPUMonitoringMIB 6 }

    jnxJsSPUMonitoringSPUThreadsTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF JnxJsSPUMonitoringSPUThreadsEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "This table exposes threads utilization statistics for an SPU."
        ::= { jnxJsSPUMonitoringMIB 7 }

    jnxJsSPUMonitoringSPUThreadsEntry OBJECT-TYPE
        SYNTAX          JnxJsSPUMonitoringSPUThreadsEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION
            "Entry collects CPU utilization statistics for a thread."
        INDEX
        {
            jnxJsSPUMonitoringIndex,
            jnxJsSPUMonitoringSPUThreadIndex
        }
        ::= { jnxJsSPUMonitoringSPUThreadsTable 1 }

    JnxJsSPUMonitoringSPUThreadsEntry ::= SEQUENCE {
        jnxJsSPUMonitoringSPUThreadIndex            Unsigned32,
        jnxJsSPUMonitoringSPUThreadLastMinUsage     Unsigned32,
        jnxJsSPUMonitoringSPUThreadLastHourUsage    Unsigned32,
        jnxJsSPUMonitoringSPUThreadLastDayUsage     Unsigned32
    }

    jnxJsSPUMonitoringSPUThreadIndex OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "Thread index on an SPU."
        ::= { jnxJsSPUMonitoringSPUThreadsEntry 1 }

    jnxJsSPUMonitoringSPUThreadLastMinUsage OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "Thread utilization of last minute."
        ::= { jnxJsSPUMonitoringSPUThreadsEntry 2 }

    jnxJsSPUMonitoringSPUThreadLastHourUsage OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "Thread utilization of last hour."
        ::= { jnxJsSPUMonitoringSPUThreadsEntry 3 }

    jnxJsSPUMonitoringSPUThreadLastDayUsage OBJECT-TYPE
        SYNTAX          Unsigned32
        MAX-ACCESS      read-only
        STATUS          current
        DESCRIPTION
            "Thread utilization of last day."
        ::= { jnxJsSPUMonitoringSPUThreadsEntry 4 }

END

