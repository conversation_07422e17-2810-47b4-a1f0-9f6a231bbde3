--
-- Juniper Enterprise Specific MIB: Destination Class Usage MIB
-- 
-- Copyright (c) 2000-2003, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-DCU-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Counter64
        FROM SNMPv2-SMI
    DisplayString
        FROM SNMPv2-TC
    SnmpAdminString
        FROM SNMP-FRAMEWORK-MIB      -- RFC 2571
    InterfaceIndex
        FROM IF-MIB
    jnxMibs
        FROM JUNIPER-SMI;

jnxDCUs MODULE-IDENTITY
    LAST-UPDATED "200307182153Z" -- Fri Jul 18 21:53:48 2003 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
		     Juniper Networks, Inc.
		     1133 Innovation Way
		     Sunnyvale, CA 94089
		     E-mail: <EMAIL>"

    DESCRIPTION
            "This is Juniper Networks' implementation of enterprise
	     specific MIB for Destination Class Usage (DCU)"
    REVISION "200212170000Z"    -- 17 December, 2002
    DESCRIPTION
            "Deprecated jnxDCUsTable.  Use jnxDcuStatsTable instead."
    REVISION "200202280000Z"    -- 28 February, 2002
    DESCRIPTION
            "Added jnxDcuStatsTable to expose per-address family stats.
            The current jnxDCUsTable will only apply to ipv4 stats."
    ::= { jnxMibs 6 }



    jnxDCUsTable    OBJECT-TYPE
	SYNTAX      SEQUENCE OF JnxDCUsEntry
	MAX-ACCESS  not-accessible
	STATUS      deprecated
	DESCRIPTION
		"A list of DCUs entries."


	::= { jnxDCUs 1 }

    jnxDCUsEntry    OBJECT-TYPE
	SYNTAX      JnxDCUsEntry
	MAX-ACCESS  not-accessible
	STATUS      deprecated
	DESCRIPTION
		"An entry of DCUs table."
	INDEX	{ jnxDCUSrcIfIndex,
		  jnxDCUDstClassName }
	::= { jnxDCUsTable 1 }

    JnxDCUsEntry ::=
	SEQUENCE {
	    jnxDCUSrcIfIndex		InterfaceIndex,
	    jnxDCUDstClassName		DisplayString,
	    jnxDCUPackets		Counter64,
	    jnxDCUBytes			Counter64
	}

    jnxDCUSrcIfIndex OBJECT-TYPE
	SYNTAX      InterfaceIndex
	MAX-ACCESS  read-only
	STATUS      deprecated
	DESCRIPTION
		"The source interface index.  This is the ifIndex
		that the traffic is coming from and the statistics 
		is counted for the specified DCU entry."
	::= { jnxDCUsEntry 1 }

    jnxDCUDstClassName OBJECT-TYPE
	SYNTAX      DisplayString (SIZE(0..24))
	MAX-ACCESS  read-only
	STATUS      deprecated
	DESCRIPTION
		"The name of the destination class.  This is the name
		of a valid configuration group defining a set of IP
		addresses and/or prefixes."
	::= { jnxDCUsEntry 2 }

    jnxDCUPackets OBJECT-TYPE
	SYNTAX      Counter64
	MAX-ACCESS  read-only
	STATUS      deprecated
	DESCRIPTION
		"The number of packets being counted pertaining to
		the specified DCU entry."
	::= { jnxDCUsEntry 3 }

    jnxDCUBytes OBJECT-TYPE
	SYNTAX      Counter64
	MAX-ACCESS  read-only
	STATUS      deprecated
	DESCRIPTION
		"The number of bytes being counted pertaining to
		the specified DCU entry."
	::= { jnxDCUsEntry 4 }




    jnxDcuStatsTable OBJECT-TYPE
        SYNTAX       SEQUENCE OF JnxDcuStatsEntry
        MAX-ACCESS   not-accessible
        STATUS       current
        DESCRIPTION
                "This table contains statistics for traffic that satisfies
                the rules in each configured Destination Class.  A separate
                set of statistics are kept for each Destination Class on
                each interface and address family on which this feature is 
                enabled."
        ::= { jnxDCUs 2 }

    jnxDcuStatsEntry    OBJECT-TYPE
        SYNTAX      JnxDcuStatsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "Each entry in this table contains a set of statistics for
                all traffic received on a specific interface and belonging
                to a specific address family that matches a specific
                Destination Class."
        INDEX   { jnxDcuStatsSrcIfIndex, jnxDcuStatsAddrFamily, 
                  jnxDcuStatsClassName }
        ::= { jnxDcuStatsTable 1 }

    JnxDcuStatsEntry ::=
        SEQUENCE {
            jnxDcuStatsSrcIfIndex       InterfaceIndex,
            jnxDcuStatsAddrFamily       INTEGER,
            jnxDcuStatsClassName        SnmpAdminString,
            jnxDcuStatsPackets          Counter64,
            jnxDcuStatsBytes            Counter64,
            jnxDcuStatsClName           SnmpAdminString
        }

    jnxDcuStatsSrcIfIndex OBJECT-TYPE
        SYNTAX      InterfaceIndex
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The ifIndex of the ingress interface for traffic counted in 
                this entry."
        ::= { jnxDcuStatsEntry 1 }

    jnxDcuStatsAddrFamily OBJECT-TYPE
        SYNTAX      INTEGER {
                        ipv4 (1),
                        ipv6 (2)
        }
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The address family of this entry's traffic."
        ::= { jnxDcuStatsEntry 2 }

    jnxDcuStatsClassName OBJECT-TYPE
        SYNTAX      SnmpAdminString (SIZE(0..112))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The name of the destination class."
        ::= { jnxDcuStatsEntry 3 }

    jnxDcuStatsPackets OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The number of packets received on this interface, belonging
                to this address family that match this Destination Class."
        ::= { jnxDcuStatsEntry 4 }

    jnxDcuStatsBytes OBJECT-TYPE
        SYNTAX      Counter64
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The number of bytes received on this interface, belonging
                to this address family that match this Destination Class."
        ::= { jnxDcuStatsEntry 5 }

    jnxDcuStatsClName OBJECT-TYPE
        SYNTAX      SnmpAdminString (SIZE(0..112))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The name of the destination class.  This object is a duplicate
                of jnxDcuStatsClassName and is included to satisfy those 
                NM applications that can't extract the name from the instance 
                portion of the OID."
        ::= { jnxDcuStatsEntry 6 }


END
