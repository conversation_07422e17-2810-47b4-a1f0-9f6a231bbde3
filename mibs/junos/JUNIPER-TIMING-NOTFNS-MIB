-------------------------------------------------------------------------------
-- Juniper Enterprise Specific MIB: Timing feature defect/event notification MIB
-- 
-- Copyright (c) 2001-2003, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
-------------------------------------------------------------------------------

JUNIPER-TIMING-NOTFNS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE,
    Integer32, Unsigned32, G<PERSON>ge32,
    TimeTicks, Counter32, Counter64,
    IpAddress
        FROM SNMPv2-SMI -- [RFC2578]
    TEXTUAL-CONVENTION, DisplayString
        FROM SNMPv2-TC  -- [RFC2579]
    InterfaceIndex, ifOperStatus
        FROM IF-MIB     -- [RFC2863a]
    InetAddress
        FROM INET-ADDRESS-MIB
    OBJECT-GROUP, NOTIFICATION-GROUP, MODULE-COMPLIANCE
        FROM SNMPv2-CONF
    jnxTimingNotfnsMIBRoot
        FROM JUNIPER-SMI;


jnxTimingNotfnsMIB MODULE-IDENTITY
    LAST-UPDATED "201909020408Z"  -- Mon Sep 02 20:08 2019 IST
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"

    DESCRIPTION
            "This is Juniper Networks' implementation of enterprise
             specific MIB for alarms from the router chassis box."
    --  Revision history
    REVISION     "201909020408Z" -- Tue Sep 02 20:08 2019 IST
    DESCRIPTION  "Modified to include additional traps for extensibility"
    REVISION     "201511140408Z" -- Tue Nov 15 04:08 2016 UTC
    DESCRIPTION  "Modifed jnxUtcValid to correct the value
                  when queried from external server"
    REVISION     "201510140000Z" -- Fri Mar 15 15:41 2013 UTC
    DESCRIPTION  "Modifed jnxClksyncQualityCode to include the list 
                  of possible values (similar to G.781 standard)"
    REVISION     "201303151541Z" -- Fri Mar 15 15:41 2013 UTC
    DESCRIPTION  "Initial Version"

    ::= { jnxTimingNotfnsMIBRoot 1 }


jnxTimingFaults          OBJECT IDENTIFIER ::= { jnxTimingNotfnsMIB 1 }
jnxTimingEvents           OBJECT IDENTIFIER ::= { jnxTimingNotfnsMIB 2 }
jnxTimingNotfObjects      OBJECT IDENTIFIER ::= { jnxTimingNotfnsMIB 3 }
jnxTimingConformance      OBJECT IDENTIFIER ::= { jnxTimingNotfnsMIB 4 }


-------------------------------------------------------------------------------
-- Textual Conventions
-------------------------------------------------------------------------------
JnxPtpClockIdTC ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
            "Clock Identifier."
    SYNTAX       OCTET STRING (SIZE (8))

JnxPtpPhaseOffsetTC ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
            "Phase Offset."
    SYNTAX       Integer32 (0..1000)


-------------------------------------------------------------------------------
-- Objects 
-------------------------------------------------------------------------------
jnxClksyncState OBJECT-TYPE
    SYNTAX      INTEGER { clear(0), set(1) }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "Fault status."
    ::= { jnxTimingNotfObjects 1 }

jnxClksyncIfIndex  OBJECT-TYPE
        SYNTAX      InterfaceIndex
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Snmp ifIndex of member interface."
        ::= { jnxTimingNotfObjects 2 }

jnxClksyncIntfName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Interface name."
        ::= { jnxTimingNotfObjects 3 }

jnxAcbFpgaRevMajor OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "Major version information."
    ::= { jnxTimingNotfObjects 4 }

jnxAcbFpgaRevMinor OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "Minor version information."
    ::= { jnxTimingNotfObjects 5 }

jnxBootCpldFpgaRevMajor OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "Major version information."
    ::= { jnxTimingNotfObjects 6 }

jnxBootCpldFpgaRevMinor OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "Minor version information."
    ::= { jnxTimingNotfObjects 7 }

jnxClksyncQualityCode OBJECT-TYPE
    SYNTAX      INTEGER {
        --eec1  
        prc(2),
        ssu-a(4),
        ssu-b(8),
        sec(11),
        dnu(15),
        --eec2
        prs(1),
        stu(0),
        st2(7),
        tnc(16),
        st3e(13),
        st3(10),
        smc(12),
        st4(14),
        dus(17) 
    }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "SSM/ESMC quality levels."
    ::= { jnxTimingNotfObjects 8 }

jnxClksyncDpllState OBJECT-TYPE
    SYNTAX      INTEGER {
        init-phase(0),
        acquiring-lock(1),
        locked(2),
        holdover(3),
        free-run(4),
        unknown(5)
    }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "BITS/SyncE DPLL states."
    ::= { jnxTimingNotfObjects 9 }

jnxPtpServoState OBJECT-TYPE
    SYNTAX      INTEGER {
        init(0),
        free-run(1),
        holdover(2),
        acquiring(3),
        freq-locked(4),
        phase-aligned(5)
    }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "PTP servo states."
    ::= { jnxTimingNotfObjects 10 }

jnxPtpClass OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "PTP clock status."
    ::= { jnxTimingNotfObjects 11 }

jnxPtpAccuracy OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "PTP clock accuracy."
    ::= { jnxTimingNotfObjects 12 }

jnxPtpGmId OBJECT-TYPE
    SYNTAX      JnxPtpClockIdTC
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "PTP Grand Master clock-id."
    ::= { jnxTimingNotfObjects 13 }

jnxPtpGmIpAddr OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "PTP Grand Master stream ip-address."
    ::= { jnxTimingNotfObjects 14 }

jnxClkStreamHandle OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "PTP clock accuracy."
    ::= { jnxTimingNotfObjects 15 }

jnxRemoteIpAddr OBJECT-TYPE
    SYNTAX      InetAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "PTP clock stream's remote ip-address."
    ::= { jnxTimingNotfObjects 16 }

jnxClksyncHybridState OBJECT-TYPE
    SYNTAX      INTEGER {
        init(0),
        freq-acq(1),
        freqLck-phaseAcq1(2),
        freqLck-phaseAcq2(3),
        freqLck-phaseAcq3(4),
        freq-phase-lck(5)
    }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "hybrid states."
    ::= { jnxTimingNotfObjects 17 }

jnxPtpPhaseOffset OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "Phase Offset."
    ::= { jnxTimingNotfObjects 18 }

jnxClksyncQualityCodeStr OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "SSM/ESMC quality levels in string format."
        ::= { jnxTimingNotfObjects 19 }

jnxClksyncDpllStateStr OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Dpll state in string format."
        ::= { jnxTimingNotfObjects 20 }

jnxPtpServoStateStr OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "PTP servo states in string format."
        ::= { jnxTimingNotfObjects 21 }

jnxClksyncHybridStateStr OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "hybrid states in string format."
        ::= { jnxTimingNotfObjects 22 }

jnxClksyncColorStr OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Color of the LED unit."
        ::= { jnxTimingNotfObjects 23 }

jnxPtpUtcOffset OBJECT-TYPE
    SYNTAX      JnxPtpPhaseOffsetTC
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "UTC Offset."
    ::= { jnxTimingNotfObjects 24 }

jnxGpsRecvStatus OBJECT-TYPE
       SYNTAX       DisplayString
       MAX-ACCESS   accessible-for-notify
       STATUS       current
       DESCRIPTION
            "Gps Receiver Status"
        ::= { jnxTimingNotfObjects 25 }

jnxPtpAdvClockClass OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
            "UTC Offset."
    ::= { jnxTimingNotfObjects 26 }


jnxClksyncSynceLockedIfIndex  OBJECT-TYPE
        SYNTAX      InterfaceIndex
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Snmp ifIndex of member interface."
        ::= { jnxTimingNotfObjects 27 }

jnxClksyncSynceLockedIntfName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Interface name."
        ::= { jnxTimingNotfObjects 28 }

jnxClksyncSynceQualityTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxClksyncSynceQualityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Synce Table to get Quality metrics for all configured sources"
    ::= { jnxTimingNotfObjects 29 }

jnxPtpUtcValid OBJECT-TYPE
    SYNTAX      INTEGER {
         false(0),
         true(1)
    }
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
       "PTP UTC Offset Valid."
    ::= { jnxTimingNotfObjects 30 }

jnxTimingFrequencyTraceability OBJECT-TYPE
   SYNTAX      INTEGER {
       false(0),
       true(1)
   }
   MAX-ACCESS  accessible-for-notify
   STATUS      current
   DESCRIPTION
        "Frequency Tracable Flag."
   ::= { jnxTimingNotfObjects 33 }

jnxTimingTimeTraceability OBJECT-TYPE
   SYNTAX      INTEGER {
       false(0),
       true(1)
   }
   MAX-ACCESS  accessible-for-notify
   STATUS      current
   DESCRIPTION
        "Time Tracable Flag."
   ::= { jnxTimingNotfObjects 34 }

jnxClksyncSynceQualityEntry OBJECT-TYPE
    SYNTAX     JnxClksyncSynceQualityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Synce Table to get Quality metrics for all configured sources"
    INDEX   { jnxClksyncSynceQualityTableIndex }
    ::= { jnxClksyncSynceQualityTable 1 }

JnxClksyncSynceQualityEntry ::= SEQUENCE {
  jnxClksyncSynceQualityTableIndex Integer32(1..4),
  jnxClksyncSynceQualityIntfName DisplayString,
  jnxClksyncSynceQualityValue DisplayString
 }

jnxClksyncSynceQualityTableIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The ifName of the configured source"
    ::= { jnxClksyncSynceQualityEntry 1 }

jnxClksyncSynceQualityIntfName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The ifName of the configured source"
    ::= { jnxClksyncSynceQualityEntry 2 }

jnxClksyncSynceQualityValue OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Quality Level of the configured source"
    ::= { jnxClksyncSynceQualityEntry 3 }

jnxClksyncPtpOperationalMasterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxClksyncPtpOperationalMasterEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "PTP Operational Master Table displays the upstream clock details"
    ::= { jnxTimingNotfObjects 35 }

jnxClksyncPtpOperationalMasterEntry OBJECT-TYPE
    SYNTAX     JnxClksyncPtpOperationalMasterEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "PTP Operational Master Entrydisplays the upstream clock details"
    INDEX   { jnxClksyncPtpOperationalMasterIndex, 
        jnxClksyncPtpOperationalMasterAttrIndex}
    ::= { jnxClksyncPtpOperationalMasterTable 1 }

JnxClksyncPtpOperationalMasterEntry ::= SEQUENCE {
    jnxClksyncPtpOperationalMasterIndex Integer32,
    jnxClksyncPtpOperationalMasterAttrIndex Integer32,
    jnxClksyncPtpOperationalMasters DisplayString 
}

jnxClksyncPtpOperationalMasterIndex OBJECT-TYPE
    SYNTAX      Integer32(1..512) 
    MAX-ACCESS  not-accessible 
    STATUS      current
    DESCRIPTION
        "The index for each operational master, not displayed for get/walk"
    ::= { jnxClksyncPtpOperationalMasterEntry 1 }

jnxClksyncPtpOperationalMasterAttrIndex OBJECT-TYPE
    SYNTAX      Integer32(1..512) 
    MAX-ACCESS  not-accessible 
    STATUS      current
    DESCRIPTION
        "The index for each attribute displayed for master, not displayed
         index not displayed for get/walk"
    ::= { jnxClksyncPtpOperationalMasterEntry 2 }

jnxClksyncPtpOperationalMasters OBJECT-TYPE
    SYNTAX      DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Attributes displayed for each master i.e ifl, localip, no_of_rem_clks 
         and follwed by remote ips" 
    ::= { jnxClksyncPtpOperationalMasterEntry 3 }

jnxClksyncPtpOperationalSlaveTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF JnxClksyncPtpOperationalSlaveEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "PTP Operational Slave Table displays the downstream clock details"
    ::= { jnxTimingNotfObjects 36 }

jnxClksyncAdditionalInformationStr OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
            "Additional information in string format."
        ::= { jnxTimingNotfObjects 37 }

jnxClksyncPtpOperationalSlaveEntry OBJECT-TYPE
    SYNTAX     JnxClksyncPtpOperationalSlaveEntry 
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "PTP Operational Slave Entry displays the downstream clock details"
    INDEX   { jnxClksyncPtpOperationalSlaveIndex, 
        jnxClksyncPtpOperationalSlaveAttrIndex}
    ::= { jnxClksyncPtpOperationalSlaveTable 1 }

JnxClksyncPtpOperationalSlaveEntry ::= SEQUENCE {
    jnxClksyncPtpOperationalSlaveIndex Integer32,
    jnxClksyncPtpOperationalSlaveAttrIndex Integer32,
    jnxClksyncPtpOperationalSlaves DisplayString 
}

jnxClksyncPtpOperationalSlaveIndex OBJECT-TYPE
    SYNTAX      Integer32(1..512) 
    MAX-ACCESS  not-accessible 
    STATUS      current
    DESCRIPTION
        "The index for each operational slave, not displayed for get/walk"
    ::= { jnxClksyncPtpOperationalSlaveEntry 1 }

jnxClksyncPtpOperationalSlaveAttrIndex OBJECT-TYPE
    SYNTAX      Integer32(1..512) 
    MAX-ACCESS  not-accessible 
    STATUS      current
    DESCRIPTION
        "The index for each attribute displayed for master, not displayed
         index not displayed for get/walk"
    ::= { jnxClksyncPtpOperationalSlaveEntry 2 }

jnxClksyncPtpOperationalSlaves OBJECT-TYPE
    SYNTAX      DisplayString 
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Attributes displayed for each slave i.e ifl, localip, no_of_rem_clks
         and follwed by remote ips" 
    ::= { jnxClksyncPtpOperationalSlaveEntry 3 }

-------------------------------------------------------------------------------
-- Faults
-------------------------------------------------------------------------------
    jnxTimingFaultLOSSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that Loss Of Signal has been detected.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 1 }

    jnxTimingFaultLOSClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that Loss Of Signal has been cleared.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 2 }

    jnxTimingFaultEFDSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Exceeded frequency deviation.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 3 }

    jnxTimingFaultEFDClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Exceeded frequency deviation cleared.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 4 }

    jnxTimingFaultLOESMCSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Loss of ESMC is set.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 5 }

    jnxTimingFaultLOESMCClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Loss of ESMC is cleared.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 6 }

    jnxTimingFaultQLFailSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies ESMC/SSM Quality Level failed is set.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 7 }

    jnxTimingFaultQLFailClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies ESMC/SSM Quality Level failed is cleared.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 8 }

    jnxTimingFaultLTISet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Loss of timing information is set.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 9 }

    jnxTimingFaultLTIClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Loss of timing information is cleared.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 10 }

    jnxTimingFaultAcbcFpgaVerNotCompatible NOTIFICATION-TYPE
        OBJECTS {
                jnxAcbFpgaRevMajor,
                jnxAcbFpgaRevMinor
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies ACBC FPGA version is not compatible.

             jnxBootCpldFpgaRevMajor indicates the current ACBC FPGA Major revision
             jnxBootCpldFpgaRevMinor indicates the current ACBC FPGA Minor revision."
    ::= { jnxTimingFaults 11 }

    jnxTimingFaultBootCpldVerNotCompatible NOTIFICATION-TYPE
        OBJECTS {
                jnxBootCpldFpgaRevMajor,
                jnxBootCpldFpgaRevMinor
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Boot-cpld version is not compatible.

             jnxClksyncVersion indicates the current Boot-cpld version."
    ::= { jnxTimingFaults 12 }

    jnxTimingFaultPriSrcFailed NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Primary source failed
             (Whenever PFM/CFM/SCM error occurs).

             jnxClksyncIfIndex is the Primary source interface index
             jnxClksyncIntfName is the Primary source interface name."
    ::= { jnxTimingFaults 13 }

    jnxTimingFaultSecSrcFailed NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Secondary source failed
             (Whenever PFM/CFM/SCM error occurs).

             jnxClksyncIfIndex is the Secondary source interface index
             jnxClksyncIntfName is the Secondary source interface name."
    ::= { jnxTimingFaults 14 }

    jnxTimingFaultPtpUniNegRateRejectSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClkStreamHandle,
                jnxRemoteIpAddr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies: 
             When acting as MASTER - Failing/rejecting clients for signaling messages
             When acting as SLAVE - Failing or receiving rejection for signaling messages

             jnxClkStreamHandle is the clock stream handle
             jnxRemoteIpAddr is the clock stream's remote ip-address."
    ::= { jnxTimingFaults  15 }

    jnxTimingFaultPtpUniNegRateRejectClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClkStreamHandle,
                jnxRemoteIpAddr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies: 
             When acting as MASTER - Failing/rejecting clients for signaling messages
             When acting as SLAVE - Failing or receiving rejection for signaling messages

             jnxClkStreamHandle is the clock stream handle
             jnxRemoteIpAddr is the clock stream's remote ip-address."
    ::= { jnxTimingFaults  16 }


-------------------------------------------------------------------------------
-- Events
-------------------------------------------------------------------------------
    jnxTimingEventPriSrcRecovered NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Primary source recovered.

             jnxClksyncIfIndex is the Primary source interface index
             jnxClksyncIntfName is the Primary source interface name."
    ::= { jnxTimingEvents 1 }

    jnxTimingEventSecSrcRecovered NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Secondary source recovered.

             jnxClksyncIfIndex is the Secondary source interface index
             jnxClksyncIntfName is the Secondary source interface name."
    ::= { jnxTimingEvents 2 }

    jnxTimingEventPriRefChanged NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,  -- new ref ifIndex
                jnxClksyncIntfName  -- new ref interface name
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Primary reference changed
             (Ifd name change or change from synce to BITS/external interface etc).

             jnxClksyncIfIndex is the Primary reference interface index
             jnxClksyncIntfName is the Primary reference interface name."
    ::= { jnxTimingEvents 3 }

    jnxTimingEventSecRefChanged NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,  -- new ref ifIndex
                jnxClksyncIntfName  -- new ref interface name
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies Secondary reference changed
             (Ifd name change or change from synce to BITS/external interface etc).

             jnxClksyncIfIndex is the Secondary reference interface index
             jnxClksyncIntfName is the Secondary reference interface name."
    ::= { jnxTimingEvents 4 }

    jnxTimingEventQLChangedRx NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName,
                jnxClksyncQualityCode, -- new quality code
                jnxClksyncQualityCodeStr -- new quality code string
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies RX SSM/ESMC quality level changed.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name
             jnxClksyncQualityCode is the SSM/ESMC quality level 
             jnxClksyncQualityCodeStr is the SSM/ESMC quality level in string format."
    ::= { jnxTimingEvents 5 }

    jnxTimingEventQLChangedTx NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName,
                jnxClksyncQualityCode, -- new quality code
                jnxClksyncQualityCodeStr -- new quality code string
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies TX SSM/ESMC quality level changed.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name
             jnxClksyncQualityCode is the SSM/ESMC quality level 
             jnxClksyncQualityCodeStr is the SSM/ESMC quality level in string format."
    ::= { jnxTimingEvents 6 }

    jnxTimingEventSynceHldovrToLck NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies SyncE Holdover to Locked EEC state.

             jnxClksyncIfIndex is the SyncE interface index
             jnxClksyncIntfName is the SyncE interface name."
    ::= { jnxTimingEvents 7 }

    jnxTimingEventSynceLckToHldovr NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies SyncE Locked to Holdover EEC state.

             jnxClksyncIfIndex is the SyncE interface index
             jnxClksyncIntfName is the SyncE interface name."
    ::= { jnxTimingEvents 8 }

    jnxTimingEventDpllStatus NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncDpllState,
                jnxClksyncDpllStateStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that DPLL state change
             (unknown, lock_acq, locked, holder, freerun).

             jnxClksyncDpllState indicates the Dpll status
             jnxClksyncDpllStateStr indicates the Dpll status in string format."
    ::= { jnxTimingEvents 9 }

    jnxTimingEventSynceDpllStatus NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName,
                jnxClksyncDpllState,
                jnxClksyncDpllStateStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that Synce DPLL state change
             (unknown, lock_acq, locked, holder, freerun).

             jnxClksyncIfIndex is the interface index from which Frequency is derived
             jnxClksyncIntfName is the interface name from which Frequency is derived
             jnxClksyncDpllState indicates the Dpll status
             jnxClksyncDpllStateStr indicates the Dpll status in string format."
    ::= { jnxTimingEvents 10 }

    jnxTimingEventBitsDpllStatus NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName,
                jnxClksyncDpllState,
                jnxClksyncDpllStateStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that Synce DPLL state change
             (unknown, lock_acq, locked, holder, freerun).

             jnxClksyncIfIndex is the interface index from which Frequency is derived
             jnxClksyncIntfName is the interface name from which Frequency is derived
             jnxClksyncDpllState indicates the Dpll status
             jnxClksyncDpllStateStr indicates the Dpll status in string format."
    ::= { jnxTimingEvents 11 }

    jnxTimingEventPtpServoStatus NOTIFICATION-TYPE
        OBJECTS {
                jnxPtpServoState,
                jnxPtpServoStateStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that PTP servo state
             (Init, Acquiring, PhaseAligned, FreeRun, Holdover). 

             jnxPtpServoState indicates the ptp servo status
             jnxPtpServoStateStr indicates the ptp servo status in string format."
    ::= { jnxTimingEvents 12 }

    jnxTimingEventPtpGMClockClassChange NOTIFICATION-TYPE
        OBJECTS {
                jnxPtpGmId,
                jnxPtpClass
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies PTP clock class changes.

             jnxPtpGmId indicates the ptp Grand Master clock-id
             jnxPtpClass indicates the ptp Grand Master clock status."
    ::= { jnxTimingEvents 13 }

    jnxTimingEventPtpGMClockAccuracyChange NOTIFICATION-TYPE
        OBJECTS {
                jnxPtpGmId,
                jnxPtpAccuracy
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies PTP clock accuracy changes.

             jnxPtpGmId indicates the ptp Grand Master clock-id
             jnxPtpAccuracy indicates the ptp Grand Master clock accuracy."
    ::= { jnxTimingEvents 14 }

    jnxTimingEventPtpGMChange NOTIFICATION-TYPE
        OBJECTS {
                jnxPtpGmId
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies PTP Grand Master changes.

             jnxPtpGmId indicates the ptp Grand Master clock-id."
    ::= { jnxTimingEvents 15 }

    jnxTimingEventHybridStatus NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncHybridState,
                jnxClksyncHybridStateStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that Hybrid state
             (Init, FreqAcq, FreqLckPhaseAcq, FreqPhaseLck). 

             jnxClksyncHybridState indicates the hybrid status
             jnxClksyncHybridStateStr indicates the hybrid status in string format."
    ::= { jnxTimingEvents 16 }

    jnxTimingEventSquelchSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that interface status changed to squelched.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 17 }

    jnxTimingEventSquelchClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that interface status changed to active.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name."
    ::= { jnxTimingFaults 18 }

    jnxTimingAlarmNoAnnounceMessageSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that announce messages are not being received.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 19 }

    jnxTimingAlarmNoAnnounceMessageClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that announce messages are now being received.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 20 }

    jnxTimingAlarmNoSyncMessageSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that sync messages are not being received.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 21 }

    jnxTimingAlarmNoSyncMessageClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that sync messages are now being received.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 22 }

    jnxTimingAlarmNoDelayResponseMessageSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that delay response messages are not being received.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 23 }

    jnxTimingAlarmNoDelayResponseMessageClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that delay response messages are now being received.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 24 }

    jnxTimingAlarmHighPDVDetectedSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that high PDV is being observed.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 25 }

    jnxTimingAlarmHighPDVDetectedClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that high PDV is now not being observed.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 26 }

    jnxTimingAlarmSlaveCandidateStateLoseLockSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that the slave candidate has lost lock.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 27 }

    jnxTimingAlarmSlaveCandidateStateLoseLockClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that the slave candidate has regained lock.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 28 }

    jnxTimingAlarmNoForeignMasterSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that there is no foreign master available.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 29 }

    jnxTimingAlarmNoForeignMasterClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that there is now a foreign master available.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 30 }

    jnxTimingAlarmPtpSyncFailSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that synchronization has failed with the master.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 31 }

    jnxTimingAlarmPtpSyncFailClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that synchronization failure with master has cleared.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 32 }

    jnxTimingAlarmPtpLocalClockOutOfSyncSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that the local clock is out of sync.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 33 }

    jnxTimingAlarmPtpLocalClockOutOfSyncClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that the local clock is no more out of sync.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 34 }

    jnxTimingAlarmLossOfReference1Set NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has lost reference1.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 35 }

    jnxTimingAlarmLossOfReference1Clear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has regained reference1.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 36 }

    jnxTimingAlarmLossOfReference2Set NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has lost reference2.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 37 }

    jnxTimingAlarmLossOfReference2Clear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has regained reference2.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 38 }

    jnxTimingAlarmLossOfReference3Set NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has lost reference3.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 39 }

    jnxTimingAlarmLossOfReference3Clear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has regained reference3.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 40 }

    jnxTimingAlarmLossOfReference4Set NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has lost reference4.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 41 }

    jnxTimingAlarmLossOfReference4Clear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has regained reference4.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 42 }

    jnxTimingAlarmNoMoreReferenceSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has no references to lock.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 43 }

    jnxTimingAlarmNoMoreReferenceClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC has a reference to lock.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 44 }

    jnxTimingAlarmQlBelowThresholdSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC QL has fallen below threshold.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 45 }

    jnxTimingAlarmQlBelowThresholdClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that EEC QL has regained the threshold.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 46 }

    jnxTimingAlarmDPLLNotLockedSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that DPLL is not locked anymore.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 47 }

    jnxTimingAlarmDPLLNotLockedClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that DPLL is locked again.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 48 }

    jnxTimingAlarmTodInSignalFailSet NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that TOD input signal has failed.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 49 }

    jnxTimingAlarmTodInSignalFailClear NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that TOD input signal is good.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 50 }

    jnxTimingEventDelayAsymmetryExceedMinor NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that Delay Asymmetry has exceeded minor threshold.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 51 }

    jnxTimingEventDelayAsymmetryExceedCritical NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncAdditionalInformationStr
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies that Delay Asymmetry has exceeded critical threshold.

             jnxClksyncAdditionalInformationStr has additional information for the trap."
    ::= { jnxTimingFaults 52 }

    jnxTimingEventLedColorChange NOTIFICATION-TYPE
        OBJECTS {
                jnxClksyncIfIndex,
                jnxClksyncIntfName,
                jnxClksyncColorStr -- new LED color
        }
        STATUS  current
        DESCRIPTION
            "A trap which signifies TX SSM/ESMC quality level changed.

             jnxClksyncIfIndex is the interface index
             jnxClksyncIntfName is the interface name
             jnxClksyncColorStr is the color of the LED unit."
    ::= { jnxTimingEvents 19 }


-------------------------------------------------------------------------------
-- Conformance
-------------------------------------------------------------------------------
jnxTimingCompliances      OBJECT IDENTIFIER ::= { jnxTimingConformance 1 }
jnxTimingGroups           OBJECT IDENTIFIER ::= { jnxTimingConformance 2 }

jnxTimingCompliance MODULE-COMPLIANCE
STATUS  current
DESCRIPTION
"The compliance statement for systems supporting
 the Timing Notification MIB."
MODULE -- this module
-- MANDATORY-GROUPS {
-- any mandatory groups should be mentioned here
-- }
GROUP       jnxTimingObjectsGroup
DESCRIPTION
"This group is optional."
GROUP       jnxTimingNotfnFaultsGroup
DESCRIPTION
"This group is optional."
GROUP       jnxTimingNotfnEventsGroup
DESCRIPTION
"This group is optional."
::= { jnxTimingCompliances 1 }

jnxTimingObjectsGroup OBJECT-GROUP
OBJECTS {
        jnxClksyncState,
        jnxClksyncIfIndex,
        jnxClksyncIntfName,
        jnxAcbFpgaRevMajor,
        jnxAcbFpgaRevMinor,
        jnxBootCpldFpgaRevMajor,
        jnxBootCpldFpgaRevMinor,
        jnxClksyncQualityCode,
        jnxClksyncDpllState,
        jnxPtpServoState,
        jnxPtpClass,
        jnxPtpAccuracy,
        jnxPtpGmId,
        jnxPtpGmIpAddr,
        jnxClkStreamHandle,
        jnxRemoteIpAddr,
        jnxClksyncHybridState,
        jnxPtpPhaseOffset,
        jnxClksyncQualityCodeStr,
        jnxClksyncDpllStateStr,
        jnxPtpServoStateStr,
        jnxClksyncHybridStateStr,
        jnxClksyncColorStr
}
STATUS current
DESCRIPTION
"Timing objects group."
::= { jnxTimingGroups 1 }

jnxTimingNotfnFaultsGroup NOTIFICATION-GROUP
NOTIFICATIONS {
        jnxTimingFaultLOSSet,
        jnxTimingFaultLOSClear,
        jnxTimingFaultEFDSet,
        jnxTimingFaultEFDClear,
        jnxTimingFaultLOESMCSet,
        jnxTimingFaultLOESMCClear,
        jnxTimingFaultQLFailSet,
        jnxTimingFaultQLFailClear,
        jnxTimingFaultLTISet,
        jnxTimingFaultLTIClear,
        jnxTimingFaultAcbcFpgaVerNotCompatible,
        jnxTimingFaultBootCpldVerNotCompatible,
        jnxTimingFaultPriSrcFailed,
        jnxTimingFaultSecSrcFailed,
        jnxTimingFaultPtpUniNegRateRejectSet,
        jnxTimingFaultPtpUniNegRateRejectClear,
        jnxTimingAlarmNoAnnounceMessageSet,
        jnxTimingAlarmNoAnnounceMessageClear,
        jnxTimingAlarmNoSyncMessageSet,
        jnxTimingAlarmNoSyncMessageClear,
        jnxTimingAlarmNoDelayResponseMessageSet,
        jnxTimingAlarmNoDelayResponseMessageClear,
        jnxTimingAlarmHighPDVDetectedSet,
        jnxTimingAlarmHighPDVDetectedClear,
        jnxTimingAlarmSlaveCandidateStateLoseLockSet,
        jnxTimingAlarmSlaveCandidateStateLoseLockClear,
        jnxTimingAlarmNoForeignMasterSet,
        jnxTimingAlarmNoForeignMasterClear,
        jnxTimingAlarmPtpSyncFailSet,
        jnxTimingAlarmPtpSyncFailClear,
        jnxTimingAlarmPtpLocalClockOutOfSyncSet,
        jnxTimingAlarmPtpLocalClockOutOfSyncClear,
        jnxTimingAlarmLossOfReference1Set,
        jnxTimingAlarmLossOfReference1Clear,
        jnxTimingAlarmLossOfReference2Set,
        jnxTimingAlarmLossOfReference2Clear,
        jnxTimingAlarmLossOfReference3Set,
        jnxTimingAlarmLossOfReference3Clear,
        jnxTimingAlarmLossOfReference4Set,
        jnxTimingAlarmLossOfReference4Clear,
        jnxTimingAlarmNoMoreReferenceSet,
        jnxTimingAlarmNoMoreReferenceClear,
        jnxTimingAlarmQlBelowThresholdSet,
        jnxTimingAlarmQlBelowThresholdClear,
        jnxTimingAlarmDPLLNotLockedSet,
        jnxTimingAlarmDPLLNotLockedClear,
        jnxTimingAlarmTodInSignalFailSet,
        jnxTimingAlarmTodInSignalFailClear
}
STATUS current
DESCRIPTION
"Timing defects notification group."
::= { jnxTimingGroups 2 }

jnxTimingNotfnEventsGroup NOTIFICATION-GROUP
NOTIFICATIONS {
        jnxTimingEventPriSrcRecovered,
        jnxTimingEventSecSrcRecovered,
        jnxTimingEventPriRefChanged,
        jnxTimingEventSecRefChanged,
        jnxTimingEventQLChangedRx,
        jnxTimingEventQLChangedTx,
        jnxTimingEventSynceHldovrToLck,
        jnxTimingEventSynceLckToHldovr,
        jnxTimingEventDpllStatus,
        jnxTimingEventSynceDpllStatus,
        jnxTimingEventBitsDpllStatus,
        jnxTimingEventPtpServoStatus,
        jnxTimingEventPtpGMClockClassChange,
        jnxTimingEventPtpGMClockAccuracyChange,
        jnxTimingEventPtpGMChange,
        jnxTimingEventHybridStatus,
        jnxTimingEventSquelchSet,
        jnxTimingEventSquelchClear,
        jnxTimingEventLedColorChange,
        jnxTimingEventDelayAsymmetryExceedMinor,
        jnxTimingEventDelayAsymmetryExceedCritical
}

STATUS current
DESCRIPTION
"Timing events notification group."
::= { jnxTimingGroups 3 }

END

