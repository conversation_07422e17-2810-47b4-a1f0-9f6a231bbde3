--
-- Juniper Enterprise Specific MIB: Structure of Management Information
-- 
-- Copyright (c) 2010-2011, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--


JUNIPER-LSYSSP-NATSRCNOPATAD-MIB DEFINITIONS ::= BEGIN
   IMPORTS
      MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, 
      Unsigned32
        FROM SNMPv2-SMI
      DisplayString
        FROM SNMPv2-TC
      jnxLsysSpNATsrcnopatad                  
        FROM JUNIPER-LSYS-SECURITYPROFILE-MIB
    ;
    
    jnxLsysSpNATsrcnopatadMIB MODULE-IDENTITY
        LAST-UPDATED  "201005191644Z" -- May 19, 2010
        ORGANIZATION  "Juniper Networks, Inc."
        CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089

             E-mail: <EMAIL>
             HTTP://www.juniper.net"
        DESCRIPTION
            "This module defines the NAT-src-no-pat-address-specific MIB for 
             Juniper Enterprise Logical-System (LSYS) security profiles.  
             Juniper documentation is recommended as the reference. 

             The LSYS security profile provides various static and dynamic 
             resource management by observing resource quota limits. 
             Security NAT-src-no-pat-address resource is the focus in this MIB. 
            "
        ::= { jnxLsysSpNATsrcnopatad 1 }

    jnxLsysSpNATsrcnopatadObjects  OBJECT IDENTIFIER ::= { jnxLsysSpNATsrcnopatadMIB 1 }
    jnxLsysSpNATsrcnopatadSummary  OBJECT IDENTIFIER ::= { jnxLsysSpNATsrcnopatadMIB 2 }
    
 
-- **********************************************************************
-- Tabular NAT-src-no-pat-address resource information objects per LSYS:
--   Below are NAT-src-no-pat-address resource table indexed by LSYS name.
-- **********************************************************************

-- The NAT-src-no-pat-address resource table per LSYS

    jnxLsysSpNATsrcnopatadTable OBJECT-TYPE
        SYNTAX              SEQUENCE OF JnxLsysSpNATsrcnopatadEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION 
            "LSYSPROFILE NAT-src-no-pat-address objects for NAT-src-no-pat-
             address resource consumption per LSYS."  
    ::= { jnxLsysSpNATsrcnopatadObjects 1 }
    
    jnxLsysSpNATsrcnopatadEntry OBJECT-TYPE
        SYNTAX              JnxLsysSpNATsrcnopatadEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION  
            "An entry in NAT-src-no-pat-address resource table."
    INDEX { IMPLIED jnxLsysSpNATsrcnopatadLsysName }          
    ::= { jnxLsysSpNATsrcnopatadTable 1 }          

    JnxLsysSpNATsrcnopatadEntry ::= 
       SEQUENCE {
          jnxLsysSpNATsrcnopatadLsysName    DisplayString,
          jnxLsysSpNATsrcnopatadProfileName DisplayString,
          jnxLsysSpNATsrcnopatadUsage       Unsigned32,
          jnxLsysSpNATsrcnopatadReserved    Unsigned32,
          jnxLsysSpNATsrcnopatadMaximum     Unsigned32
    }   
 
-- Entry definitions for the NAT-src-no-pat-address resource table
 
    jnxLsysSpNATsrcnopatadLsysName       OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..64))
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION
            "The name of the logical system for which NAT-src-no-pat-address 
             resource information is retrieved. "
        ::= { jnxLsysSpNATsrcnopatadEntry 1 }

    jnxLsysSpNATsrcnopatadProfileName    OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..32))
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The security profile name string for the LSYS."
    ::= { jnxLsysSpNATsrcnopatadEntry 2 }

    jnxLsysSpNATsrcnopatadUsage          OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION  
            "The current resource usage count for the LSYS."
    ::= { jnxLsysSpNATsrcnopatadEntry 3 }
    
    jnxLsysSpNATsrcnopatadReserved       OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The reserved resource count for the LSYS."
    ::= { jnxLsysSpNATsrcnopatadEntry 4 } 

    jnxLsysSpNATsrcnopatadMaximum        OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The maximum allowed resource usage count for the LSYS."
    ::= { jnxLsysSpNATsrcnopatadEntry 5 }


-- **********************************************************************
-- The NAT-src-no-pat-address resource information summary:
-- **********************************************************************

    jnxLsysSpNATsrcnopatadUsedAmount         OBJECT-TYPE
        SYNTAX                  Unsigned32 
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "The NAT-src-no-pat-address resource consumption over all LSYS."
    ::= { jnxLsysSpNATsrcnopatadSummary 1 }          

    jnxLsysSpNATsrcnopatadMaxQuota           OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The NAT-src-no-pat-address resource maximum quota for the whole 
            device for all LSYS."
    ::= { jnxLsysSpNATsrcnopatadSummary 2 }
    
    jnxLsysSpNATsrcnopatadAvailableAmount    OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The NAT-src-no-pat-address resource available in the whole device."
    ::= { jnxLsysSpNATsrcnopatadSummary 3 }
    
    jnxLsysSpNATsrcnopatadHeaviestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The most amount of NAT-src-no-pat-address resource consumed of a 
            LSYS."
    ::= { jnxLsysSpNATsrcnopatadSummary 4 }
    
    jnxLsysSpNATsrcnopatadHeaviestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the most NAT-src-no-pat-address resource."
    ::= { jnxLsysSpNATsrcnopatadSummary 5 }
    
    jnxLsysSpNATsrcnopatadLightestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The least amount of NAT-src-no-pat-address resource consumed of a 
            LSYS."
    ::= { jnxLsysSpNATsrcnopatadSummary 6 }
    
    jnxLsysSpNATsrcnopatadLightestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the least NAT-src-no-pat-address resource."
    ::= { jnxLsysSpNATsrcnopatadSummary 7 }
    


 -- ***************************************************************
 -- definition of NAT-src-no-pat-address resource related traps. (TBD)
 -- ***************************************************************

--
-- End of File 
--

END
