-- *****************************************************************************
-- draft-ietf-idr-bgp4-mibv2-03.txt
--
-- Copyright (c) 2002 The Internet Society.
-- Copyright (c) 2003-2013, Juniper Networks, Inc.
-- All rights reserved.
--
-- Juniper Networks edits to this MIB:                               *** JNX ***
--   09/09/03  Assigned an enterprise experimental OID and added "jnx" prefix.
--             Fixed AUGMENTS entry for the bgpM2CfgPeerConfedMemberTable.
--             Added bgpM2PeerRoutingInstance to bgpM2PeerTable.
--             Added REVISION clause to MODULE-IDENTITY.
--             Fixed conformance clauses.
--   12/12/17  Redefines the value for jnxBgpM2PrefixesInPrefixesRejected
--             so that it conforms to the definition.
--             Add a new counter in the same sequence to return the number
--             of activeprefixes received from the peer
--             jnxBgpM2PrefixInPrefixesActive.
-- *****************************************************************************

BGP4-V2-MIB-JUNIPER DEFINITIONS ::= BEGIN

    IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE,
        Counter32, Gauge32, Unsigned32, Integer32, TimeTicks      -- *** JNX ***
            FROM SNMPv2-SMI
        -- Note that the following reference to INET-ADDRESS-MIB
        -- refers to the version as published in the RFC 2851
        -- update internet draft.
        InetAddressType, InetAddress, InetPortNumber,
        InetAutonomousSystemNumber, InetAddressPrefixLength
            FROM INET-ADDRESS-MIB
    -- Juniper specific                                              *** JNX ***
    jnxBgpM2Experiment                                            -- *** JNX ***
        FROM JUNIPER-EXPERIMENT-MIB                               -- *** JNX ***
        TEXTUAL-CONVENTION, TruthValue, RowPointer, StorageType,
        RowStatus
            FROM SNMPv2-TC
        MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
            FROM SNMPv2-CONF
        SnmpAdminString
            FROM SNMP-FRAMEWORK-MIB;

    jnxBgpM2 MODULE-IDENTITY
        LAST-UPDATED "200309091508Z"  -- 09-Sep-03 11:08 AM EDT      *** JNX ***
        ORGANIZATION "IETF IDR Working Group"
        CONTACT-INFO "E-mail:  <EMAIL>

                      Jeffrey Haas  (Editor)
                      825 Victors Way, Suite 100
                      Ann Arbor, MI  48108
                      Tel: ****** 222-1600
                      Fax: ****** 222-1602
                      E-mail: <EMAIL>"

        DESCRIPTION
            "This MIB module defines management objects for
             the Border Gateway Protocol, Version 4."
        REVISION    "201212170000Z"                               -- *** JNX ***
        DESCRIPTION                                               -- *** JNX ***
            "This change redefines the value returned for the     -- *** JNX ***
            variable jnxBgpM2PrefixesInPrefixesRejected so that   -- *** JNX ***
            it conforms to the definition.                        -- *** JNX ***
            It also adds a new counter in the same sequence to    -- *** JNX ***
            return the number of active prefixes received from    -- *** JNX ***
            the peer:  jnxBgpM2PrefixInPrefixesActive"            -- *** JNX ***
        REVISION    "200309091508Z"  -- 09-Sep-03 11:08 AM EDT       *** JNX ***
        DESCRIPTION                                               -- *** JNX ***
            "This is a proprietary implementation of the          -- *** JNX ***
            draft-ietf-idr-bgp4-mibv2-03.txt MIB as written by    -- *** JNX ***
            the IETF Inter-Domain Routing Working Group.          -- *** JNX ***
            This Juniper experimental MIB will be retired when a  -- *** JNX ***
            valid oid branch is assigned."                        -- *** JNX ***
        REVISION    "200211040000Z"                               -- *** JNX ***
        DESCRIPTION                                               -- *** JNX ***
            "draft-ietf-idr-bgp4-mibv2-03.txt version written by  -- *** JNX ***
            the IETF Inter-Domain Routing Working Group."         -- *** JNX ***
--        ::= { mib-2 XXX }                                          *** JNX ***
     ::= { jnxBgpM2Experiment 1 }                                 -- *** JNX ***


    JnxBgpM2Identifier ::= TEXTUAL-CONVENTION
        DISPLAY-HINT "1d."
        STATUS       current
        DESCRIPTION
            "The representation of a BGP Identifier.  The BGP
             Identifier should be represented in the OCTET STRING
             as with the first OCTET of the string containing
             the first OCTET of the BGP Identifier received or sent
             in the OPEN packet and so on.

             Even though the BGP Identifier is trending away from
             an IP address it is still displayed as if it was one,
             even when it would be an illegal IP address."
        SYNTAX OCTET STRING(SIZE (4))


    JnxBgpM2Safi ::= TEXTUAL-CONVENTION
        DISPLAY-HINT "d"
        STATUS       current
        DESCRIPTION
            "The representation of a BGP Safi"
        SYNTAX Unsigned32(0..255)


    JnxBgpM2Community ::= TEXTUAL-CONVENTION
        DISPLAY-HINT "2d:"
        STATUS       current
        DESCRIPTION
            "The representation of a BGP Community."
        SYNTAX OCTET STRING(SIZE(4))


    JnxBgpM2ExtendedCommunity ::= TEXTUAL-CONVENTION
        DISPLAY-HINT "1x:"
        STATUS       current
        DESCRIPTION
            "The representation of a BGP Extended Community."
        SYNTAX OCTET STRING(SIZE(8))


    jnxBgpM2BaseScalars
        OBJECT IDENTIFIER ::= { jnxBgpM2 1 }


    --
    -- Notifications
    --

    jnxBgpM2BaseNotifications
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalars 0 }


    jnxBgpM2Established NOTIFICATION-TYPE
        OBJECTS {
            jnxBgpM2PeerLocalAddrType,
            jnxBgpM2PeerLocalAddr,
            jnxBgpM2PeerRemoteAddrType,
            jnxBgpM2PeerRemoteAddr,
            jnxBgpM2PeerLastErrorReceived,
            jnxBgpM2PeerState
        }
        STATUS  current
        DESCRIPTION
            "The BGP Established event is generated when
             the BGP FSM enters the ESTABLISHED state."
        ::= { jnxBgpM2BaseNotifications 1 }


    jnxBgpM2BackwardTransition NOTIFICATION-TYPE
        OBJECTS {
            jnxBgpM2PeerLocalAddrType,
            jnxBgpM2PeerLocalAddr,
            jnxBgpM2PeerRemoteAddrType,
            jnxBgpM2PeerRemoteAddr,
            jnxBgpM2PeerLastErrorReceived,
            jnxBgpM2PeerLastErrorReceivedText,
            jnxBgpM2PeerState
        }
        STATUS  current
        DESCRIPTION
            "The BGPBackwardTransition Event is generated
             when the BGP FSM moves from a higher numbered
             state to a lower numbered state."
        ::= { jnxBgpM2BaseNotifications 2 }


    --
    -- BGP Supported Version Table
    --
    jnxBgpM2Version
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalars 1 }


    jnxBgpM2VersionTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2VersionEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table of supported BGP versions."
        ::= { jnxBgpM2Version 1 }


    jnxBgpM2VersionEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2VersionEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing data on a given supported version
             of the Border Gateway Protocol and the level of
             support provided.  It is expected that any agent
             implementation supporting this MIB module will
             report support for Version 4 of the Border Gateway
             Protocol at the very minimum."
        INDEX {
            jnxBgpM2VersionIndex
        }
        ::= { jnxBgpM2VersionTable 1 }


    JnxBgpM2VersionEntry ::= SEQUENCE {
            jnxBgpM2VersionIndex
                Unsigned32,
            jnxBgpM2VersionSupported
                TruthValue
    }


    jnxBgpM2VersionIndex OBJECT-TYPE
        SYNTAX     Unsigned32(0..255)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The version number of the BGP Protocol."
        ::= { jnxBgpM2VersionEntry 1 }


    jnxBgpM2VersionSupported OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is TRUE if this version of the BGP protocol
             identified in 'jnxBgpM2VersionIndex' is supported.  The absence
             of a row for a particular jnxBgpM2VersionIndex indicates that
             that jnxBgpM2VersionIndex protocol version number is not
             supported."
        ::= { jnxBgpM2VersionEntry 2 }


    --
    -- Supported authentication mechanisms
    --

    jnxBgpM2SupportedAuthentication
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalars 2 }


    jnxBgpM2SupportedAuthTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2SupportedAuthEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The supported BGP authentication mechanisms."
        ::= { jnxBgpM2SupportedAuthentication 1 }


    jnxBgpM2SupportedAuthEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2SupportedAuthEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing information whether a given BGP
             authentication mechanism is supported by this
             implementation."
        INDEX {
            jnxBgpM2SupportedAuthCode
        }
        ::= { jnxBgpM2SupportedAuthTable 1 }


    JnxBgpM2SupportedAuthEntry ::= SEQUENCE {
        jnxBgpM2SupportedAuthCode
            Unsigned32,
        jnxBgpM2SupportedAuthValue
            TruthValue
    }


    jnxBgpM2SupportedAuthCode OBJECT-TYPE
        SYNTAX     Unsigned32(0..255)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The BGP authentication code."
        ::= { jnxBgpM2SupportedAuthEntry 1 }


    jnxBgpM2SupportedAuthValue OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is TRUE if a given authentication method
             is supported by the local implementation."
        ::= { jnxBgpM2SupportedAuthEntry 2 }


    --
    -- Supported BGP Capabilities
    --

    jnxBgpM2SupportedCapabilities
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalars 3 }


    jnxBgpM2CapabilitySupportAvailable OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is TRUE if capability support is
             available and is enabled."
        ::= { jnxBgpM2SupportedCapabilities 1 }


    jnxBgpM2SupportedCapabilitiesTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2SupportedCapabilitiesEntry -- *** JNX ***
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table of supported BGP-4 capabilities."
        ::= { jnxBgpM2SupportedCapabilities 2 }
    jnxBgpM2SupportedCapabilitiesEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2SupportedCapabilitiesEntry             -- *** JNX ***
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Information about supported capabilities indexed
             by capability number."
        INDEX {
            jnxBgpM2SupportedCapabilityCode
        }
        ::= { jnxBgpM2SupportedCapabilitiesTable 1 }


    JnxBgpM2SupportedCapabilitiesEntry ::= SEQUENCE {             -- *** JNX ***
        jnxBgpM2SupportedCapabilityCode
            Unsigned32,
        jnxBgpM2SupportedCapability
            TruthValue
    }


    jnxBgpM2SupportedCapabilityCode OBJECT-TYPE
        SYNTAX     Unsigned32 (0..255)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Index of supported capability.  The index directly
             corresponds with the BGP-4 Capability Advertisement
             Capability Code."
        ::= { jnxBgpM2SupportedCapabilitiesEntry 1 }


    jnxBgpM2SupportedCapability OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is True if this capability is supported,
             False otherwise."
        ::= { jnxBgpM2SupportedCapabilitiesEntry 2 }


    --
    -- Base Scalars
    --


    jnxBgpM2AsSize OBJECT-TYPE
        SYNTAX     INTEGER {
            twoOctet(1),
            fourOctet(2)
        }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The size of the AS value in this implementation.

             The semantics of this are determined as per the
             as-4bytes draft."
        REFERENCE
            "draft-ietf-idr-as4bytes-04"
        ::= { jnxBgpM2BaseScalars 4 }


    jnxBgpM2LocalAs OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The local autonomous system number.

             If the jnxBgpM2AsSize is twoOctet, then the range is
             constrained to be 0-65535."
        ::= { jnxBgpM2BaseScalars 5 }


    jnxBgpM2LocalIdentifier OBJECT-TYPE
        SYNTAX     JnxBgpM2Identifier
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The BGP Identifier of local system.

             Current practice is trending away from this value being
             treated as an IP address and more as a generic
             identifier."
        ::= { jnxBgpM2BaseScalars 6 }


    --
    -- Base Scalar Extensions
    --

    jnxBgpM2BaseScalarExtensions
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalars 7 }
    jnxBgpM2BaseScalarNonCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalarExtensions 1 }


    jnxBgpM2BaseScalarCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalarExtensions 2 }


    --
    -- Base Scalar Route Reflection Extensions
    --

    jnxBgpM2BaseScalarRouteReflectExts OBJECT IDENTIFIER ::=
        { jnxBgpM2BaseScalarNonCapExts 2796 }


    jnxBgpM2RouteReflector OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This value is TRUE if this implementation supports the
             BGP Route Reflection Extension and is enabled as a
             route reflector.  If the BGP Route Reflection extension
             is not supported this value must be FALSE."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2BaseScalarRouteReflectExts 1 }


    jnxBgpM2ClusterId OBJECT-TYPE
        SYNTAX      JnxBgpM2Identifier
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The configured Cluster-ID of the BGP Speaker.  This will
             default to the BGP Speaker's JnxBgpM2Identifier if this
             speaker is functioning as a route reflector and an
             explicit Cluster-ID has not been configured.

             A value of 0.0.0.0 will be present if Route Reflection is
             not enabled."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2BaseScalarRouteReflectExts 2 }


    --
    -- Base Scalar AS Confederation Extensions
    --

    jnxBgpM2BaseScalarASConfedExts OBJECT IDENTIFIER ::=
         { jnxBgpM2BaseScalarNonCapExts 3065 }


    jnxBgpM2ConfederationRouter OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is TRUE if this implementation supports the
             BGP AS Confederations Extension and this router is
             configured to be in a confederation."
        REFERENCE
            "RFC 3065 - Autonomous System Confederations for BGP"
        ::= { jnxBgpM2BaseScalarASConfedExts 1 }


    jnxBgpM2ConfederationId OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The local Confederation Identification Number.

             This value will be zero (0) if this BGP Speaker is not
             a confederation router."
        REFERENCE
            "RFC 3065 - Autonomous System Confederations for BGP"
        ::= { jnxBgpM2BaseScalarASConfedExts 2 }


    --
    -- Base Configuration Objects
    --

    jnxBgpM2BaseScalarConfiguration
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalars 8 }


    jnxBgpM2CfgBaseScalarStorageType OBJECT-TYPE
        SYNTAX     StorageType
        MAX-ACCESS read-write
        STATUS     current
        DESCRIPTION
            "This object specifies the intended storage type for
             all configurable base scalars."
        ::= { jnxBgpM2BaseScalarConfiguration 1 }


    jnxBgpM2CfgLocalAs OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-write
        STATUS     current
        DESCRIPTION
            "The local autonomous system number.

             If the jnxBgpM2AsSize is twoOctet, then the range is
             constrained to be 0-65535."
        ::= { jnxBgpM2BaseScalarConfiguration 2 }


    jnxBgpM2CfgLocalIdentifier OBJECT-TYPE
        SYNTAX     JnxBgpM2Identifier
        MAX-ACCESS read-write
        STATUS     current
        DESCRIPTION
            "The BGP Identifier of local system.

             Current practice is trending away from this value being
             treated as an IP address and more as a generic
             identifier."
        ::= { jnxBgpM2BaseScalarConfiguration 3 }


    --
    -- Base Scalar Extensions
    --

    jnxBgpM2CfgBaseScalarExtensions
        OBJECT IDENTIFIER ::= { jnxBgpM2BaseScalarConfiguration 4 }


    jnxBgpM2CfgBaseScalarNonCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgBaseScalarExtensions 1 }


    jnxBgpM2CfgBaseScalarCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgBaseScalarExtensions 2 }


    --
    -- Base Scalar Route Reflection Extensions
    --
    jnxBgpM2CfgBaseScalarReflectorExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgBaseScalarNonCapExts 2796 }


    jnxBgpM2CfgRouteReflector OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "This value is set to true if this implementation will
             be supporting route reflection."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2CfgBaseScalarReflectorExts 1 }


    jnxBgpM2CfgClusterId OBJECT-TYPE
        SYNTAX      JnxBgpM2Identifier
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "The configured Cluster-ID of the BGP Speaker.  This will
             default to the BGP Speaker's JnxBgpM2Identifier if this
             speaker is functioning as a route reflector and an
             explicit Cluster-ID has not been configured.

             A value of 0.0.0.0 will be present if Route Reflection is
             not enabled."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2CfgBaseScalarReflectorExts 2 }


    --
    -- Base Scalar AS Confederation Extensions
    --

    jnxBgpM2CfgBaseScalarASConfedExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgBaseScalarNonCapExts 3065 }


    jnxBgpM2CfgConfederationRouter OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-write
        STATUS     current
        DESCRIPTION
            "This value is set to true if this implementation will be
             supporting BGP AS Confederations."
        REFERENCE
            "RFC 3065 - Autonomous System Confederations for BGP"
        ::= { jnxBgpM2CfgBaseScalarASConfedExts 1 }


    jnxBgpM2CfgConfederationId OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-write
        STATUS     current
        DESCRIPTION
            "The local Confederation Identification Number.

             This value will be zero (0) if this BGP Speaker is not
             a confederation router."
        REFERENCE
            "RFC 3065 - Autonomous System Confederations for BGP"
        ::= { jnxBgpM2CfgBaseScalarASConfedExts 2 }


    --
    -- BGP Peer Data
    --

    jnxBgpM2Peer
        OBJECT IDENTIFIER ::= { jnxBgpM2 2 }


    jnxBgpM2PeerData
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 1 }


    jnxBgpM2PeerTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "BGP peer table.

             This table contains, one entry per remote BGP peer,
             any information about the connections with the remote
             BGP peers."
        ::= { jnxBgpM2PeerData 1 }


    jnxBgpM2PeerEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing information about the connection with
             a remote BGP peer."
        INDEX {
            jnxBgpM2PeerRoutingInstance,
            jnxBgpM2PeerLocalAddrType,
            jnxBgpM2PeerLocalAddr,
            jnxBgpM2PeerRemoteAddrType,
            jnxBgpM2PeerRemoteAddr
        }
        ::= { jnxBgpM2PeerTable 1 }


    JnxBgpM2PeerEntry ::= SEQUENCE {
        jnxBgpM2PeerIdentifier
            JnxBgpM2Identifier,
        jnxBgpM2PeerState
            INTEGER,
        jnxBgpM2PeerStatus
            INTEGER,
        jnxBgpM2PeerConfiguredVersion
            Unsigned32,
        jnxBgpM2PeerNegotiatedVersion
            Unsigned32,
        jnxBgpM2PeerLocalAddrType
            InetAddressType,
        jnxBgpM2PeerLocalAddr
            InetAddress,
        jnxBgpM2PeerLocalPort
            InetPortNumber,
        jnxBgpM2PeerLocalAs
            InetAutonomousSystemNumber,
        jnxBgpM2PeerRemoteAddrType
            InetAddressType,
        jnxBgpM2PeerRemoteAddr
            InetAddress,
        jnxBgpM2PeerRemotePort
            InetPortNumber,
        jnxBgpM2PeerRemoteAs
            InetAutonomousSystemNumber,
        jnxBgpM2PeerIndex
            Unsigned32,                                           -- *** JNX ***
        jnxBgpM2PeerRoutingInstance                               -- *** JNX ***
            Unsigned32
    }


    jnxBgpM2PeerIdentifier OBJECT-TYPE
        SYNTAX     JnxBgpM2Identifier
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The BGP Identifier of this entry's remote BGP peer.

             This entry should be 0.0.0.0 unless the jnxBgpM2PeerState is
             in the OpenConfirm or the Established state."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. 4.2"
        ::= { jnxBgpM2PeerEntry 1 }


    jnxBgpM2PeerState OBJECT-TYPE
        SYNTAX     INTEGER {
            idle(1),
            connect(2),
            active(3),
            opensent(4),
            openconfirm(5),
            established(6)
        }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The remote BGP peer's FSM state."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. 8"
        ::= { jnxBgpM2PeerEntry 2 }


    jnxBgpM2PeerStatus OBJECT-TYPE
        SYNTAX     INTEGER {
            halted(1),
            running(2)
        }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Whether or not the BGP FSM for this remote peer is halted
             or running.  The BGP FSM for a remote peer is halted after
             processing a Stop event.  Likewise, it is in the running
             state after a Start event.

             The jnxBgpM2PeerState will generally be in the idle state when
             the FSM is halted, although some extensions such as
             Graceful Restart will leave the peer in the Idle state
             but with the FSM running."
        ::= { jnxBgpM2PeerEntry 3 }
    jnxBgpM2PeerConfiguredVersion OBJECT-TYPE
        SYNTAX     Unsigned32 (1..255)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The configured version to originally start with this
             remote peer.  The BGP speaker may permit negotiation to a
             lower version number of the protocol."
        ::= { jnxBgpM2PeerEntry 4 }


    jnxBgpM2PeerNegotiatedVersion OBJECT-TYPE
        SYNTAX     Unsigned32 (1..255)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The negotiated version of BGP running between the two
             peers."
        ::= { jnxBgpM2PeerEntry 5 }


    jnxBgpM2PeerLocalAddrType OBJECT-TYPE
        SYNTAX     InetAddressType
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The address family of the local end of the peering
             session."
        ::= { jnxBgpM2PeerEntry 6 }


    jnxBgpM2PeerLocalAddr OBJECT-TYPE
        SYNTAX     InetAddress (SIZE(4..20))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The address of the local end of the peering session."
        ::= { jnxBgpM2PeerEntry 7 }


    jnxBgpM2PeerLocalPort OBJECT-TYPE
        SYNTAX     InetPortNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The local port for the TCP connection between the BGP
             peers."
        ::= { jnxBgpM2PeerEntry 8 }
    jnxBgpM2PeerLocalAs OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Some implementations of BGP can represent themselves
             as multiple ASs.  This is the AS that this peering
             session is representing itself as to the remote peer."
        ::= { jnxBgpM2PeerEntry 9 }


    jnxBgpM2PeerRemoteAddrType OBJECT-TYPE
        SYNTAX     InetAddressType
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The address family of the remote end of the peering
             session."
        ::= { jnxBgpM2PeerEntry 10 }


    jnxBgpM2PeerRemoteAddr OBJECT-TYPE
        SYNTAX     InetAddress (SIZE(4..20))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The address of the remote end of the peering session."
        ::= { jnxBgpM2PeerEntry 11 }


    jnxBgpM2PeerRemotePort OBJECT-TYPE
        SYNTAX     InetPortNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The remote port for the TCP connection between the BGP
             peers.  In the case of a transport for which the notion
             of 'port' is irrelevant, an instance value of -1
             should be returned by the agent for this object.

             Note that the objects jnxBgpM2PeerLocalAddr,
             jnxBgpM2PeerLocalPort, jnxBgpM2PeerRemoteAddr and
             jnxBgpM2PeerRemotePort provide the appropriate reference to
             the standard MIB TCP connection table. or even the ipv6
             TCP MIB as in rfc2452."
        REFERENCE
            "RFC 2012 - SNMPv2 Management Information Base for the
             Transmission Control Protocol using SMIv2.
             RFC 2542 - IP Version 6 Management Information Base
             for the Transmission Control Protocol."
        ::= { jnxBgpM2PeerEntry 12 }


    jnxBgpM2PeerRemoteAs OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The remote autonomous system number."
        ::= { jnxBgpM2PeerEntry 13 }


    jnxBgpM2PeerIndex OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This value is a unique index for the remote peer entry
             in the jnxBgpM2PeerTable.  It is assigned by the agent
             at the point of creation of the jnxBgpM2PeerTable row
             entry.  While its value is guaranteed to be unique at
             any time, it is otherwise opaque to the management
             application with respect to its value or the contiguity
             of jnxBgpM2PeerIndex row instance values across rows of
             the jnxBgpM2PeerTable.  It is used to provide an index
             structure for other tables whose data is logically
             per-peer.

             For explicitly configured peers, this value will remain
             consistent until this row is deleted by deleting the
             configured peers.  Unconfigured peers will generate
             a monotonically increasing number when a BGP FSM is
             built to process the peering session.  Values in the
             jnxBgpM2PeerTable and other tables utilizing jnxBgpM2PeerIndex
             are expected to remain in existence for an arbitrary
             time after the unconfigured peer has been deleted
             in order to allow management applications to extract
             useful management information for those peers.  Thus,
             an unconfigured peer using the same indices as the
             jnxBgpM2PeerTable that comes up while this row still
             exists will re-utilize the same row."
         ::= { jnxBgpM2PeerEntry 14 }

    jnxBgpM2PeerRoutingInstance OBJECT-TYPE                       -- *** JNX ***
        SYNTAX      Unsigned32                                    -- *** JNX ***
        MAX-ACCESS  read-only                                     -- *** JNX ***
        STATUS      current                                       -- *** JNX ***
        DESCRIPTION                                               -- *** JNX ***
        "Routing instance index."                                 -- *** JNX ***
         ::= { jnxBgpM2PeerEntry 15 }                             -- *** JNX ***

    --
    -- Errors
    --

    jnxBgpM2PeerErrors
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 2 }

    jnxBgpM2PeerErrorsTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerErrorsEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "On a per peer basis, this table reflects the last
             protocol-defined error encountered and reported on
             the peer session.  If no entry for a given peer,
             by its jnxBgpM2PeerIndex, exists in this table, then no
             such errors have been observed, reported, and
             recorded on the  session."
        ::= { jnxBgpM2PeerErrors 1 }


    jnxBgpM2PeerErrorsEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerErrorsEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Each entry contains information about errors sent
             and received for a particular BGP peer."
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerErrorsTable 1 }


    JnxBgpM2PeerErrorsEntry ::= SEQUENCE {
        jnxBgpM2PeerLastErrorReceived
            OCTET STRING,
        jnxBgpM2PeerLastErrorSent
            OCTET STRING,
        jnxBgpM2PeerLastErrorReceivedTime
            TimeTicks,
        jnxBgpM2PeerLastErrorSentTime
            TimeTicks,
        jnxBgpM2PeerLastErrorReceivedText
            SnmpAdminString,
        jnxBgpM2PeerLastErrorSentText
            SnmpAdminString,
        jnxBgpM2PeerLastErrorReceivedData
            OCTET STRING,
        jnxBgpM2PeerLastErrorSentData
            OCTET STRING
    }


    jnxBgpM2PeerLastErrorReceived OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE (2))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The last error code and subcode received by this BGP
             Speaker via a NOTIFICATION message for this peer.
             If no error has occurred, this field is zero.
             Otherwise, the first byte of this two byte
             OCTET STRING contains the error code, and the second
             byte contains the subcode."
        REFERENCE
            "draft-ietf-idr-bgp4-15.txt, Sec. 4.5"
        ::= { jnxBgpM2PeerErrorsEntry 1 }


    jnxBgpM2PeerLastErrorSent OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE (2))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The last error code and subcode sent by this BGP
             Speaker via a NOTIFICATION message to this peer.
             If no error has occurred, this field is zero.
             Otherwise, the first byte of this two byte
             OCTET STRING contains the error code, and the second
             byte contains the subcode."
        REFERENCE
            "draft-ietf-idr-bgp4-15.txt, Sec. 4.5"
        ::= { jnxBgpM2PeerErrorsEntry 2 }


    jnxBgpM2PeerLastErrorReceivedTime OBJECT-TYPE
        SYNTAX     TimeTicks
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The timestamp that the last NOTIFICATION was received from
             this peer."
        REFERENCE
            "draft-ietf-idr-bgp4-15.txt, Sec. 4.5"
        ::= { jnxBgpM2PeerErrorsEntry 3 }
    jnxBgpM2PeerLastErrorSentTime OBJECT-TYPE
        SYNTAX     TimeTicks
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The timestamp that the last NOTIFICATION was sent to
             this peer."
        REFERENCE
            "draft-ietf-idr-bgp4-15.txt, Sec. 4.5"
        ::= { jnxBgpM2PeerErrorsEntry 4 }


    jnxBgpM2PeerLastErrorReceivedText OBJECT-TYPE
        SYNTAX     SnmpAdminString
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This object contains an implementation specific
             explanation of the error that was reported."
        ::= { jnxBgpM2PeerErrorsEntry 5 }


    jnxBgpM2PeerLastErrorSentText OBJECT-TYPE
        SYNTAX     SnmpAdminString
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This object contains an implementation specific
             explanation of the error that is being reported."
        ::= { jnxBgpM2PeerErrorsEntry 6 }


    jnxBgpM2PeerLastErrorReceivedData OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE(0..4075))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The last error code's data seen by this peer."
        REFERENCE
            "draft-ietf-idr-bgp4-15.txt, Sec. 4.5"
        ::= { jnxBgpM2PeerErrorsEntry 7 }


    jnxBgpM2PeerLastErrorSentData OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE(0..4075))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The last error code's data sent to this peer."
        REFERENCE
            "draft-ietf-idr-bgp4-15.txt, Sec. 4.5"
        ::= { jnxBgpM2PeerErrorsEntry 8 }


    --
    -- Peer Authentication
    --

    jnxBgpM2PeerAuthentication
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 3 }


    jnxBgpM2PeerAuthTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerAuthEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "BGP peer authentication table.

             This table contains, one entry per BGP peer,
             information about the authentication with BGP peers."
        ::= { jnxBgpM2PeerAuthentication 1 }


    jnxBgpM2PeerAuthEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerAuthEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing information about the authentication
             with a BGP peer."
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerAuthTable 1 }


    JnxBgpM2PeerAuthEntry ::= SEQUENCE {
        jnxBgpM2PeerAuthSent
            TruthValue,
        jnxBgpM2PeerAuthSentCode
            Unsigned32,
        jnxBgpM2PeerAuthSentValue
            OCTET STRING,
        jnxBgpM2PeerAuthRcvd
            TruthValue,
        jnxBgpM2PeerAuthRcvdCode
            Unsigned32,
        jnxBgpM2PeerAuthRcvdValue
            OCTET STRING
    }


    jnxBgpM2PeerAuthSent OBJECT-TYPE
        SYNTAX TruthValue
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The local peer has sent authentication information
             to the remote peer in the BGP Authentication field."
        ::= { jnxBgpM2PeerAuthEntry 1 }


     jnxBgpM2PeerAuthSentCode OBJECT-TYPE
        SYNTAX  Unsigned32 (0..255)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The code of the authentication information sent to
             the remote peer."
        ::= { jnxBgpM2PeerAuthEntry 2 }


    jnxBgpM2PeerAuthSentValue OBJECT-TYPE
        SYNTAX  OCTET STRING (SIZE (0..252))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The payload of the authentication information
             from the remote peer."
        ::= { jnxBgpM2PeerAuthEntry 3 }


    jnxBgpM2PeerAuthRcvd OBJECT-TYPE
        SYNTAX TruthValue
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The local peer has received authentication information
             from the remote peer in the BGP Authentication field."
        ::= { jnxBgpM2PeerAuthEntry 4 }


    jnxBgpM2PeerAuthRcvdCode OBJECT-TYPE
        SYNTAX  Unsigned32 (0..255)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The code of the authentication information received from
             the remote peer."
        ::= { jnxBgpM2PeerAuthEntry 5 }


    jnxBgpM2PeerAuthRcvdValue OBJECT-TYPE
        SYNTAX  OCTET STRING (SIZE (0..252))
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The payload of the authentication information from
            the remote peer."
        ::= { jnxBgpM2PeerAuthEntry 6 }


    --
    -- Peer Event Times
    --

    jnxBgpM2PeerTimers
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 4 }


    jnxBgpM2PeerEventTimesTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerEventTimesEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "A table reporting the per-peering session amount
             of time elapsed and update events since the peering
             session advanced into the Established state."
        ::= { jnxBgpM2PeerTimers 1 }


    jnxBgpM2PeerEventTimesEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerEventTimesEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Each row contains a set of statistics about time
             spent and events encountered in the peer session
             Established state."
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerEventTimesTable 1 }


    JnxBgpM2PeerEventTimesEntry ::= SEQUENCE {
        jnxBgpM2PeerFsmEstablishedTime
            Gauge32,
        jnxBgpM2PeerInUpdatesElapsedTime
            Gauge32
    }


    jnxBgpM2PeerFsmEstablishedTime OBJECT-TYPE
        SYNTAX     Gauge32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This timer indicates how long (in seconds) this
             peer has been in the Established state or how long
             since this peer was last in the Established state.
             It is set to zero when a new peer is configured or
             the router is booted."
        ::= { jnxBgpM2PeerEventTimesEntry 1 }


    jnxBgpM2PeerInUpdatesElapsedTime OBJECT-TYPE
        SYNTAX     Gauge32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Elapsed time in seconds since the last BGP UPDATE
             message was received from the peer.  Each time
             jnxBgpM2PeerInUpdates is incremented, the value of this
             object is set to zero (0).  This value shall also be
             zero (0) when the peer is not in the Established state"
        ::= { jnxBgpM2PeerEventTimesEntry 2 }


    --
    -- Peer Configured Timers
    --

    jnxBgpM2PeerConfiguredTimersTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerConfiguredTimersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Per peer management data on BGP session timers."
        ::= { jnxBgpM2PeerTimers 2 }

    jnxBgpM2PeerConfiguredTimersEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerConfiguredTimersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Each entry corresponds to the current state of
             BGP timers on a given peering session."
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerConfiguredTimersTable 1 }


    JnxBgpM2PeerConfiguredTimersEntry ::= SEQUENCE {
        jnxBgpM2PeerConnectRetryInterval
            Unsigned32,
        jnxBgpM2PeerHoldTimeConfigured
            Unsigned32,
        jnxBgpM2PeerKeepAliveConfigured
            Unsigned32,
        jnxBgpM2PeerMinASOrigInterval
            Unsigned32,
        jnxBgpM2PeerMinRouteAdverInterval
            Unsigned32
    }


    jnxBgpM2PeerConnectRetryInterval OBJECT-TYPE
        SYNTAX     Unsigned32 (1..65535)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the ConnectRetry
             timer.  The suggested value for this timer is 120
             seconds."
        ::= { jnxBgpM2PeerConfiguredTimersEntry 1 }


    jnxBgpM2PeerHoldTimeConfigured OBJECT-TYPE
        SYNTAX     Unsigned32 ( 0 | 3..65535 )
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the Hold Time configured
             for this BGP speaker with this peer.  This value
             is placed in an OPEN message sent to this peer by
             this BGP speaker, and is compared with the Hold
             Time field in an OPEN message received from the
             peer when determining the Hold Time (jnxBgpM2PeerHoldTime)
             with the peer.  This value must not be less than
             three seconds if it is not zero (0) in which case
             the Hold Time is NOT to be established with the
             peer.  The suggested value for this timer is 90
             seconds."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Appendix 6.4"
        ::= { jnxBgpM2PeerConfiguredTimersEntry 2 }


    jnxBgpM2PeerKeepAliveConfigured OBJECT-TYPE
        SYNTAX     Unsigned32 ( 0 | 1..21845 )
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the KeepAlive timer
             configured for this BGP speaker with this peer.
             The value of this object will only determine the
             KEEPALIVE messages frequency relative to the value
             specified in jnxBgpM2PeerHoldTimeConfigured; the actual
             time interval for the KEEPALIVE messages is indicated
             by jnxBgpM2PeerKeepAlive.  A reasonable maximum value
             for this timer would be configured to be one third
             of that of jnxBgpM2PeerHoldTimeConfigured.

             If the value of this object is zero (0), no
             periodical KEEPALIVE messages are sent to the peer
             after the BGP connection has been established.
             The suggested value for this timer is 30 seconds."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Appendix 6.4"
        ::= { jnxBgpM2PeerConfiguredTimersEntry 3 }


    jnxBgpM2PeerMinASOrigInterval OBJECT-TYPE
        SYNTAX     Unsigned32 (0..65535)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the MinASOriginationInterval
             timer.  The suggested value for this timer is 15
             seconds."
        ::= { jnxBgpM2PeerConfiguredTimersEntry 4 }
    jnxBgpM2PeerMinRouteAdverInterval OBJECT-TYPE
        SYNTAX     Unsigned32 (0..65535)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the
             MinRouteAdvertisementInterval timer.  The suggested
             value for this timer is 30 seconds."
        ::= { jnxBgpM2PeerConfiguredTimersEntry 5 }


    --
    -- Peer Negotiated Timers
    --

    jnxBgpM2PeerNegotiatedTimersTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerNegotiatedTimersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Current values of per-peer timers which can be
             dynamically set in the jnxBgpM2PeerConfiguredTimersTable.
             Values reflected in this table are the current
             operational values, after negotiation from values
             derived from initial configuration or last set from
             jnxBgpM2PeerConfiguredTimersTable row instances."
        ::= { jnxBgpM2PeerTimers 3 }


    jnxBgpM2PeerNegotiatedTimersEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerNegotiatedTimersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Each entry reflects a value of the currently
             operational, negotiated timers as reflected in the
             JnxBgpM2PeerNegotiatedTimersEntry."
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerNegotiatedTimersTable 1 }


    JnxBgpM2PeerNegotiatedTimersEntry ::= SEQUENCE {
        jnxBgpM2PeerHoldTime
            Unsigned32,
        jnxBgpM2PeerKeepAlive
            Unsigned32
    }


    jnxBgpM2PeerHoldTime OBJECT-TYPE
        SYNTAX     Unsigned32  ( 0 | 3..65535 )
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The value of this object is calculated by this BGP
             Speaker as being;

             zero (0) - if this was the value sent by the peer and
             this value is permitted by this BGP Speaker.  In this
             case, no keepalive messages are sent and the Hold Timer
             is not set.

             At least three (3).  This value is the smaller of
             the value sent by this peer in the OPEN message and
             jnxBgpM2PeerHoldTimeConfigured for this peer.

             This value is only defined when the peering session is
             in the Established state."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. 4.2"
        ::= { jnxBgpM2PeerNegotiatedTimersEntry 1 }


    jnxBgpM2PeerKeepAlive OBJECT-TYPE
        SYNTAX     Unsigned32 ( 0 | 1..21845 )
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the KeepAlive timer
             established with the peer.  The value of this object
             is calculated by this BGP speaker such that, when
             compared with jnxBgpM2PeerHoldTime, it has the same
             proportion as what jnxBgpM2PeerKeepAliveConfigured has
             when compared with jnxBgpM2PeerHoldTimeConfigured.  If
             the value of this object is zero (0), it indicates
             that the KeepAlive timer has not been established
             with the peer, or, the value of
             jnxBgpM2PeerKeepAliveConfigured is zero (0).

             This value is only defined when the peering session is
             in the Established state."
        REFERENCE
            "draft-ietf-idr-bgp4-17, Sec. 4.4"
        ::= { jnxBgpM2PeerNegotiatedTimersEntry 2 }
    --
    -- Peer Capabilities
    --

    jnxBgpM2PeerCapabilities
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 5 }


    --
    -- Announced Capabilities
    --

    jnxBgpM2PeerCapsAnnouncedTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerCapsAnnouncedEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "This table contains the capabilities
             that are supported for a given peer."
        ::= { jnxBgpM2PeerCapabilities 1 }


    jnxBgpM2PeerCapsAnnouncedEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerCapsAnnouncedEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "These entries are keyed by a BGP-4 peer remote
             address and the BGP Capability Code"
        INDEX {
            jnxBgpM2PeerIndex,
            jnxBgpM2PeerCapAnnouncedCode,
            jnxBgpM2PeerCapAnnouncedIndex
        }
        ::= { jnxBgpM2PeerCapsAnnouncedTable 1 }


    JnxBgpM2PeerCapsAnnouncedEntry ::= SEQUENCE {
        jnxBgpM2PeerCapAnnouncedCode
            Unsigned32,
        jnxBgpM2PeerCapAnnouncedIndex
            Unsigned32,
        jnxBgpM2PeerCapAnnouncedValue
            OCTET STRING
    }


    jnxBgpM2PeerCapAnnouncedCode OBJECT-TYPE
        SYNTAX     Unsigned32 (0..255)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The BGP Capability Advertisement Capability Code."
        ::= { jnxBgpM2PeerCapsAnnouncedEntry 1 }


    jnxBgpM2PeerCapAnnouncedIndex OBJECT-TYPE
        SYNTAX     Unsigned32 (1..128)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Multiple instances of a given capability may be sent
             bgp a BGP speaker.  This variable is used to index them."
        ::= { jnxBgpM2PeerCapsAnnouncedEntry 2 }


    jnxBgpM2PeerCapAnnouncedValue OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE(0..255))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The value of the announced capability."
        ::= { jnxBgpM2PeerCapsAnnouncedEntry 3 }


    --
    -- Received Capabilities
    --

    jnxBgpM2PeerCapsReceivedTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerCapsReceivedEntry      -- *** JNX ***
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "This table contains the capabilities
            that are supported for a given peer."
        ::= { jnxBgpM2PeerCapabilities 2 }


    jnxBgpM2PeerCapsReceivedEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerCapsReceivedEntry                  -- *** JNX ***
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "These entries are keyed by a BGP-4 peer remote
            address and the BGP Capability Code"
        INDEX {
            jnxBgpM2PeerIndex,
            jnxBgpM2PeerCapReceivedCode,
            jnxBgpM2PeerCapReceivedIndex
        }
        ::= { jnxBgpM2PeerCapsReceivedTable 1 }


    JnxBgpM2PeerCapsReceivedEntry ::= SEQUENCE {                  -- *** JNX ***
        jnxBgpM2PeerCapReceivedCode
            Unsigned32,
        jnxBgpM2PeerCapReceivedIndex
            Unsigned32,
        jnxBgpM2PeerCapReceivedValue
            OCTET STRING
    }


    jnxBgpM2PeerCapReceivedCode OBJECT-TYPE
        SYNTAX     Unsigned32 (0..255)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The BGP Capability Advertisement Capability Code."
        ::= { jnxBgpM2PeerCapsReceivedEntry 1 }


    jnxBgpM2PeerCapReceivedIndex OBJECT-TYPE
        SYNTAX     Unsigned32 (1..128)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Multiple instances of a given capability may be sent
             bgp a BGP speaker.  This variable is used to index them."
        ::= { jnxBgpM2PeerCapsReceivedEntry 2 }


    jnxBgpM2PeerCapReceivedValue OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE(0..255))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The value of the announced capability."
        ::= { jnxBgpM2PeerCapsReceivedEntry 3 }


    --
    -- Per-peer counters
    --

    jnxBgpM2PeerCounters
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 6 }


    jnxBgpM2PeerCountersTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerCountersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The counters associated with a BGP Peer."
        ::= { jnxBgpM2PeerCounters 1 }


    jnxBgpM2PeerCountersEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerCountersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Each entry contains counters of message transmissions
             and FSM transitions for a given BGP Peering session."
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerCountersTable 1 }


    JnxBgpM2PeerCountersEntry ::= SEQUENCE {
        jnxBgpM2PeerInUpdates
            Counter32,
        jnxBgpM2PeerOutUpdates
            Counter32,
        jnxBgpM2PeerInTotalMessages
            Counter32,
        jnxBgpM2PeerOutTotalMessages
            Counter32,
        jnxBgpM2PeerFsmEstablishedTrans
            Counter32
    }


-- +++wayne need to describe what happens if connection is broken
-- and then reestablished. Does the prior counter value accumulate?
    jnxBgpM2PeerInUpdates OBJECT-TYPE
        SYNTAX     Counter32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of BGP UPDATE messages received on this
             connection.  This object should be initialized to zero
             (0) when the connection is established."
        ::= { jnxBgpM2PeerCountersEntry 1 }


    jnxBgpM2PeerOutUpdates OBJECT-TYPE
        SYNTAX     Counter32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of BGP UPDATE messages transmitted on this
             connection.  This object should be initialized to zero
             (0) when the connection is established."
        ::= { jnxBgpM2PeerCountersEntry 2 }


    jnxBgpM2PeerInTotalMessages OBJECT-TYPE
        SYNTAX     Counter32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The total number of messages received from the remote
             peer on this connection.  This object should be
             initialized to zero when the connection is established."
        ::= { jnxBgpM2PeerCountersEntry 3 }


    jnxBgpM2PeerOutTotalMessages OBJECT-TYPE
        SYNTAX     Counter32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The total number of messages transmitted to the remote
             peer on this connection.  This object should be
             initialized to zero when the connection is established."
        ::= { jnxBgpM2PeerCountersEntry 4 }


    jnxBgpM2PeerFsmEstablishedTrans OBJECT-TYPE
        SYNTAX     Counter32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
        "The total number of times the BGP FSM
        transitioned into the established state
        for this peer."
        ::= { jnxBgpM2PeerCountersEntry 5 }


    --
    -- Per-Peer Prefix Counters
    --

    jnxBgpM2PrefixCountersTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PrefixCountersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Additional per-peer, per AFI SAFI counters for prefixes"
        ::= { jnxBgpM2PeerCounters 2 }


    jnxBgpM2PrefixCountersEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PrefixCountersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing information about a bgp-peers prefix
             counters."
        INDEX {
            jnxBgpM2PeerIndex,
            jnxBgpM2PrefixCountersAfi,
            jnxBgpM2PrefixCountersSafi
        }
        ::= { jnxBgpM2PrefixCountersTable 1 }


    JnxBgpM2PrefixCountersEntry ::= SEQUENCE {
        jnxBgpM2PrefixCountersAfi
            InetAddressType,
        jnxBgpM2PrefixCountersSafi
            JnxBgpM2Safi,
        jnxBgpM2PrefixInPrefixes
            Gauge32,
        jnxBgpM2PrefixInPrefixesAccepted
            Gauge32,
        jnxBgpM2PrefixInPrefixesRejected
            Gauge32,
        jnxBgpM2PrefixOutPrefixes
            Gauge32,
        jnxBgpM2PrefixInPrefixesActive
            Gauge32
    }


    jnxBgpM2PrefixCountersAfi OBJECT-TYPE
        SYNTAX     InetAddressType
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The AFI index of the per-peer, per prefix counters"
        ::= { jnxBgpM2PrefixCountersEntry 1 }


    jnxBgpM2PrefixCountersSafi OBJECT-TYPE
        SYNTAX     JnxBgpM2Safi
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The SAFI index of the per-peer, per prefix counters"
        ::= { jnxBgpM2PrefixCountersEntry 2 }


    jnxBgpM2PrefixInPrefixes OBJECT-TYPE
        SYNTAX     Gauge32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of prefixes received from a peer and are
             stored in the Adj-Ribs-In for that peer."
             -- jmh - note that we are allowing stuff to be discarded
        ::= { jnxBgpM2PrefixCountersEntry 7 }


    jnxBgpM2PrefixInPrefixesAccepted OBJECT-TYPE
        SYNTAX     Gauge32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of prefixes for a peer that are installed
             in the Adj-Ribs-In and are eligible to become active
             in the Loc-Rib."
        ::= { jnxBgpM2PrefixCountersEntry 8 }


    jnxBgpM2PrefixInPrefixesRejected OBJECT-TYPE
        SYNTAX     Gauge32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of prefixes for a peer that are installed
             in the Adj-Ribs-In and are NOT eligible to become active
             in the Loc-Rib."
        ::= { jnxBgpM2PrefixCountersEntry 9 }

    jnxBgpM2PrefixOutPrefixes OBJECT-TYPE
        SYNTAX     Gauge32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of prefixes for a peer that are installed
             in that peers Adj-Ribs-Out."
        ::= { jnxBgpM2PrefixCountersEntry 10 }

    jnxBgpM2PrefixInPrefixesActive OBJECT-TYPE
        SYNTAX     Gauge32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of prefixes for a peer that are installed
             in the Adj-Ribs-In and are the active route
             in the Loc-Rib."
        ::= { jnxBgpM2PrefixCountersEntry 11 }


    jnxBgpM2PeerExtensions
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 7 }


    jnxBgpM2PeerNonCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PeerExtensions 1 }


    jnxBgpM2PeerCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PeerExtensions 2 }


    --
    -- Peer Route Reflection Extensions
    --

    jnxBgpM2PeerRouteReflectionExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PeerNonCapExts 2796 }


    jnxBgpM2PeerReflectorClientTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerReflectorClientEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table of route reflection client settings on a per-peer
             basis."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2PeerRouteReflectionExts 1 }


    jnxBgpM2PeerReflectorClientEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerReflectorClientEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing data on a per-peer basis on whether
             the peer is configured as a route reflector client."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerReflectorClientTable 1 }


    JnxBgpM2PeerReflectorClientEntry ::= SEQUENCE {
        jnxBgpM2PeerReflectorClient
            INTEGER
    }

    jnxBgpM2PeerReflectorClient OBJECT-TYPE
        SYNTAX    INTEGER {
            nonClient(0),
            client(1),
            meshedClient(2)
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This value indicates whether the given peer is a
             reflector client of this router, or not.  A value of
             nonClient indicates that this peer is not a reflector
             client.  A value of client indicates that this peer is a
             reflector client that is not fully meshed with other
             reflector clients.  A value of meshedClient indicates
             that the peer is a reflector client and is fully meshed
             with all other reflector clients.

             This value must be nonClient (0) for BGP external peers."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2PeerReflectorClientEntry 1 }


    --
    -- Peer AS Confederations Extensions
    --

    jnxBgpM2PeerASConfederationExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PeerNonCapExts 3065 }


    jnxBgpM2PeerConfedMemberTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PeerConfedMemberEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table of confederation member settings on a per-peer
             basis."
        REFERENCE
            "RFC 3065 - BGP Confederations"
        ::= { jnxBgpM2PeerASConfederationExts 1 }


    jnxBgpM2PeerConfedMemberEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PeerConfedMemberEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing data on a per-peer basis on whether
             the peer is configured as a BGP confederation member."
        REFERENCE
            "RFC 3065 - BGP Confederations"
        AUGMENTS {
            jnxBgpM2PeerEntry
        }
        ::= { jnxBgpM2PeerConfedMemberTable 1 }


    JnxBgpM2PeerConfedMemberEntry ::= SEQUENCE {
        jnxBgpM2PeerConfedMember
            TruthValue
    }


    jnxBgpM2PeerConfedMember OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This value indicates whether the given peer is in our
             confederation or not."
        REFERENCE
            "RFC 3065 - BGP Confederations"
        ::= { jnxBgpM2PeerConfedMemberEntry 1 }


    --
    -- Peer configuration objects
    --
    jnxBgpM2PeerConfiguration
        OBJECT IDENTIFIER ::= { jnxBgpM2Peer 8 }


    --
    -- Administering activated peering sessions
    --

    jnxBgpM2CfgPeerAdminStatusTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2CfgPeerAdminStatusEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table containing rows for administratively starting and
             stopping peering sessions."
        ::= { jnxBgpM2PeerConfiguration 1 }


    jnxBgpM2CfgPeerAdminStatusEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2CfgPeerAdminStatusEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing row for administratively starting and
             stopping peers."
        INDEX {
            jnxBgpM2PeerIndex
        }
        ::= { jnxBgpM2CfgPeerAdminStatusTable 1 }


    JnxBgpM2CfgPeerAdminStatusEntry ::= SEQUENCE {
        jnxBgpM2CfgPeerAdminStatus
            INTEGER
    }


    jnxBgpM2CfgPeerAdminStatus OBJECT-TYPE
        SYNTAX      INTEGER {
            stop(1),
            start(2)
        }
        MAX-ACCESS read-write
        STATUS  current
        DESCRIPTION
            "This object allows the Manual Stop and Manual Start
             events to be sent to an activated peering session."
        ::= { jnxBgpM2CfgPeerAdminStatusEntry 1 }
    --
    -- Peer Configuration
    --

    jnxBgpM2CfgPeerNextIndex OBJECT-TYPE
        SYNTAX     Integer32 (0..65535)
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This object contains the next appropriate value to
             use as an index for creation of a row instance in
             in the jnxBgpM2CfgPeerTable.  If the number of available
             entries in the jnxBgpM2CfgPeerTable is exhausted, a
             retrieval value of this object instance will return
             0.  A value of 0 may also be returned if the agent
             is otherwise incapable of jnxBgpM2CfgPeerTable row creation
             at the time of jnxBgpM2CfgPeerNextIndex retrieval."
        ::= { jnxBgpM2PeerConfiguration 2 }


    jnxBgpM2CfgPeerTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2CfgPeerEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "BGP configuration peer table.

             This table allows the configuration of the parameters
             for a session with a BGP peer.

             +++wayne provide description of how config should be done
             for a peer per table."
        ::= { jnxBgpM2PeerConfiguration 3 }


    jnxBgpM2CfgPeerEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2CfgPeerEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing information set up by a management
             entity to configure a connection with a BGP peer."
        INDEX { jnxBgpM2CfgPeerIndex }
        ::= { jnxBgpM2CfgPeerTable 1 }


    JnxBgpM2CfgPeerEntry ::= SEQUENCE {
        jnxBgpM2CfgPeerConfiguredVersion
            Unsigned32,
        jnxBgpM2CfgAllowVersionNegotiation
            TruthValue,
        jnxBgpM2CfgPeerLocalAddrType
            InetAddressType,
        jnxBgpM2CfgPeerLocalAddr
            InetAddress,
        jnxBgpM2CfgPeerLocalAs
            InetAutonomousSystemNumber,
        jnxBgpM2CfgPeerRemoteAddrType
            InetAddressType,
        jnxBgpM2CfgPeerRemoteAddr
            InetAddress,
        jnxBgpM2CfgPeerRemotePort
            Integer32,
        jnxBgpM2CfgPeerRemoteAs
            InetAutonomousSystemNumber,
        jnxBgpM2CfgPeerEntryStorageType
            StorageType,
        jnxBgpM2CfgPeerError
            INTEGER,
        jnxBgpM2CfgPeerBgpPeerEntry
            RowPointer,
        jnxBgpM2CfgPeerRowEntryStatus
            RowStatus,
        jnxBgpM2CfgPeerIndex
            Integer32,
        jnxBgpM2CfgPeerStatus
            INTEGER,
        jnxBgpM2CfgPeerRoutingInstance                            -- *** JNX ***
            Unsigned32                                            -- *** JNX ***
        }


    jnxBgpM2CfgPeerConfiguredVersion OBJECT-TYPE
        SYNTAX     Unsigned32 (1..255)
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "The configured version to originally start with
             this peer.  The BGP speaker may permit negotiation
             to a lower version number of the protocol depending on the
             set value of jnxBgpM2CfgAllowVersionNegotiation."
        DEFVAL     { 4 }
        ::= { jnxBgpM2CfgPeerEntry 1 }


    jnxBgpM2CfgAllowVersionNegotiation OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "If set to true, during session establishment with this
             peer, negotiation to a version lower than that specified
             in jnxBgpM2CfgPeerConfiguredVersion will be allowed."
        DEFVAL { false }
        ::= { jnxBgpM2CfgPeerEntry 2 }


    jnxBgpM2CfgPeerLocalAddrType OBJECT-TYPE
        SYNTAX     InetAddressType
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "The address family of the speakers of this BGP
             session."
        ::= { jnxBgpM2CfgPeerEntry 3 }


    jnxBgpM2CfgPeerLocalAddr OBJECT-TYPE
        SYNTAX     InetAddress (SIZE (4..20))
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "The address of the local end of the peering session."
        ::= { jnxBgpM2CfgPeerEntry 4 }


    jnxBgpM2CfgPeerLocalAs OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "Autonomous system represented to peer on peering
             session initialization.  Some implementations of
             BGP can represent themselves as multiple ASes.
             These implementations can set this to an alternate
             autonomous system.  If this object is set to zero
             (0) at the point this row instance is set to active,
             then the implementation will initialize this session
             representing itself as the value of jnxBgpM2CfgLocalAs."
        DEFVAL { 0 }
        ::= { jnxBgpM2CfgPeerEntry 5 }


    jnxBgpM2CfgPeerRemoteAddrType OBJECT-TYPE
        SYNTAX     InetAddressType
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "The address family of the speakers of the remote BGP
             session."
        ::= { jnxBgpM2CfgPeerEntry 6 }


    jnxBgpM2CfgPeerRemoteAddr OBJECT-TYPE
        SYNTAX      InetAddress (SIZE(4..20))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "The address of the remote end (destination address
             of peer) for peering session."
        ::= { jnxBgpM2CfgPeerEntry 7 }


    jnxBgpM2CfgPeerRemotePort  OBJECT-TYPE
        SYNTAX      Integer32 (-1 | 0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This is the remote port for the transport connection
             between the BGP peers.  In the case of a transport for
             which the notion of port is irrelevant, the value of
             -1 can be defaulted or set."
        DEFVAL { -1 }
        ::= { jnxBgpM2CfgPeerEntry 8 }


    jnxBgpM2CfgPeerRemoteAs OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "Autonomous system number of the remote peer."
        ::= { jnxBgpM2CfgPeerEntry 9 }


    jnxBgpM2CfgPeerEntryStorageType OBJECT-TYPE
        SYNTAX     StorageType
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "This object type specifies the intended storage
             type for the jnxBgpM2CfgPeerEntry row instance."
        ::= { jnxBgpM2CfgPeerEntry 10 }
    jnxBgpM2CfgPeerError  OBJECT-TYPE
        SYNTAX      INTEGER {
            unknown(0),
            notActivated (1),
            errDuplicatePeeringSession (2),
            activated (3)
            -- +++wayne more to follow
        }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "This value indicates the current error status of
             the row denoting the configured error status.

             If this row is still under creation (has not been activated
             jnxBgpM2CfgPeerRowEntryStatus), then this instance will be set to
             not-activated (1).

             At the point that the row is activated, jnxBgpM2CfgPeerError
             will reflect the error status of the row data itself.  If
             there is another session already activated with the same
             local and remote addresses as denoted by
             {jnxBgpM2CfgPeerLocalAddrType, jnxBgpM2CfgPeerLocalAddr,
             jnxBgpM2CfgPeerRemoteAddr, jnxBgpM2CfgPeerRemotePort}, then
             the value of this will be set to
             err-duplicate-peering-session (2).

             If this row is associated with a peer session whose
             initialization has been attempted, the value will be
             set to activated (3) (and, jnxBgpM2PeerCfgPeerEntry will
             be set to the row instance of the entry in the
             jnxBgpM2PeerTable which reflects the state of the peering
             session).

             Note that this object only reflects the error as a
             function of the attempted activation of this row as
             containing data for a bgp peering session.  The actual
             state of the session at the point of any protocol exchange
             or session state machine initiation is reflected in the
             jnxBgpM2PeerTable row instance (as reflected through
             jnxBgpM2CfgPeerPeerEntry) associated with this row instance."
        ::= { jnxBgpM2CfgPeerEntry 11 }


    jnxBgpM2CfgPeerBgpPeerEntry OBJECT-TYPE
        SYNTAX      RowPointer
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Upon activation of the session data contained in this
             row instance, this object points to an instance of a row
             within the jnxBgpM2PeerTable reflecting the session in its
             initializing or operational state.  Retrieval of this
             column instance will always yield a value of {0.0} unless
             the session has successfully been activated (via
             jnxBgpM2CfgPeerRowEntryStatus).  Such row instances will always
             have a value of jnxBgpM2CfgPeerError which is activated (3)."
        ::= { jnxBgpM2CfgPeerEntry 12 }


    jnxBgpM2CfgPeerRowEntryStatus  OBJECT-TYPE
        SYNTAX     RowStatus
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "This object type is used to control creation,
             activation, and deletion of this row instance."
            -- +++wayne need better directions for agent auto-removal
            -- of row instances which have moved to active or error
            -- state
        ::= { jnxBgpM2CfgPeerEntry 13 }


    jnxBgpM2CfgPeerIndex OBJECT-TYPE
        SYNTAX     Integer32 (1..65535)
        MAX-ACCESS not-accessible                                 -- *** JNX ***
        STATUS     current
        DESCRIPTION
            "Uniquely identifies an instance of a peer row, as
             an element of configuration."
        ::= { jnxBgpM2CfgPeerEntry 14 }


    jnxBgpM2CfgPeerStatus OBJECT-TYPE
        SYNTAX      INTEGER {
            halted(1),
            running(2)
        }
        MAX-ACCESS read-create
        STATUS  current
        DESCRIPTION
             "This specifies the state of the peering session upon
              activation.  If disabled, the FSM is in the halted
              state and no Automatic Start events are generated.
              If enabled, the FSM is in the running state and
              Automatic Start events may be generated."
        ::= { jnxBgpM2CfgPeerEntry 15 }
        
    jnxBgpM2CfgPeerRoutingInstance OBJECT-TYPE                    -- *** JNX ***
        SYNTAX      Unsigned32                                    -- *** JNX ***
        MAX-ACCESS  read-create                                   -- *** JNX ***
        STATUS      current                                       -- *** JNX ***
        DESCRIPTION                                               -- *** JNX ***
            "Routing instance index."                             -- *** JNX ***
        ::= { jnxBgpM2CfgPeerEntry 16 }                           -- *** JNX ***

    --
    -- Per-peer authentication table.
    --

    jnxBgpM2CfgPeerAuthTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2CfgPeerAuthEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table contain per peer configuration for BGP Authentication."
        ::= { jnxBgpM2PeerConfiguration 4 }


    jnxBgpM2CfgPeerAuthEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2CfgPeerAuthEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing information about a peer's BGP Authentication
             configuration."
        AUGMENTS {
            jnxBgpM2CfgPeerEntry
        }
        ::= { jnxBgpM2CfgPeerAuthTable 1 }


    JnxBgpM2CfgPeerAuthEntry ::= SEQUENCE {
        jnxBgpM2CfgPeerAuthEnabled
            TruthValue,
        jnxBgpM2CfgPeerAuthCode
            Unsigned32,
        jnxBgpM2CfgPeerAuthValue
            OCTET STRING
    }


    jnxBgpM2CfgPeerAuthEnabled OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "This value is true if BGP Authentication is enabled for
             this peer.  This is the authentication mechanism
             documented in the base BGP specification, not the MD5
             session protection mechanism."
        DEFVAL {
            false
        }
        ::= { jnxBgpM2CfgPeerAuthEntry 1 }


    jnxBgpM2CfgPeerAuthCode OBJECT-TYPE
        SYNTAX     Unsigned32(0..255)
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "The authentication code for the BGP Authentication
             mechanism."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. 4.1.a"
        ::= { jnxBgpM2CfgPeerAuthEntry 2 }


    jnxBgpM2CfgPeerAuthValue OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE(0..252))
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "The authentication payload for the BGP authentication
             mechanism.  This value has semantic meaning within
             the context of the authentication code."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. 4.1.a"
        ::= { jnxBgpM2CfgPeerAuthEntry 3 }


    --
    -- Per-peer timers table
    --

    jnxBgpM2CfgPeerTimersTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2CfgPeerTimersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table for configuration of per-peer timers."
        ::= {  jnxBgpM2PeerConfiguration 5 }


    jnxBgpM2CfgPeerTimersEntry OBJECT-TYPE
        SYNTAX      JnxBgpM2CfgPeerTimersEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing per-peer timer configuration."
        AUGMENTS {
            jnxBgpM2CfgPeerEntry
        }
        ::= { jnxBgpM2CfgPeerTimersTable 1 }


    JnxBgpM2CfgPeerTimersEntry ::= SEQUENCE {
        jnxBgpM2CfgPeerConnectRetryInterval
            Unsigned32,
        jnxBgpM2CfgPeerHoldTimeConfigured
            Unsigned32,
        jnxBgpM2CfgPeerKeepAliveConfigured
            Unsigned32,
        jnxBgpM2CfgPeerMinASOrigInterval
            Unsigned32,
        jnxBgpM2CfgPeerMinRouteAdverInter
            Unsigned32
    }


    jnxBgpM2CfgPeerConnectRetryInterval OBJECT-TYPE
        SYNTAX     Unsigned32 (1..65535)
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the ConnectRetry
             timer.  The suggested value for this timer is 120
             seconds."
        DEFVAL {
            120
        }
        ::= { jnxBgpM2CfgPeerTimersEntry 1 }


    jnxBgpM2CfgPeerHoldTimeConfigured OBJECT-TYPE
        SYNTAX     Unsigned32 ( 0 | 3..65535 )
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the Hold Time configured
             for this BGP speaker with this peer.  This value
             is placed in an OPEN message sent to this peer by
             this BGP speaker, and is compared with the Hold
             Time field in an OPEN message received from the
             peer when determining the Hold Time (jnxBgpM2PeerHoldTime)
             with the peer.  This value must not be less than
             three seconds if it is not zero (0) in which case
             the Hold Time is NOT to be established with the
             peer.  The suggested value for this timer is 90
             seconds."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Appendix 6.4"
        DEFVAL {
            90
        }
        ::= { jnxBgpM2CfgPeerTimersEntry 2 }


    jnxBgpM2CfgPeerKeepAliveConfigured OBJECT-TYPE
        SYNTAX     Unsigned32 ( 0 | 1..21845 )
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the KeepAlive timer
             configured for this BGP speaker with this peer.
             The value of this object will only determine the
             KEEPALIVE messages frequency relative to the value
             specified in jnxBgpM2PeerHoldTimeConfigured; the actual
             time interval for the KEEPALIVE messages is indicated
             by jnxBgpM2PeerKeepAlive.  A reasonable maximum value
             for this timer would be configured to be one third
             of that of jnxBgpM2PeerHoldTimeConfigured.

             If the value of this object is zero (0), no
             periodical KEEPALIVE messages are sent to the peer
             after the BGP connection has been established.
             The suggested value for this timer is 30 seconds."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Appendix 6.4"
        DEFVAL {
            30
        }
        ::= { jnxBgpM2CfgPeerTimersEntry 3 }


    jnxBgpM2CfgPeerMinASOrigInterval OBJECT-TYPE
        SYNTAX     Unsigned32 (0..65535)
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the MinASOriginationInterval
             timer.  The suggested value for this timer is 15
             seconds."
        DEFVAL {
            15
        }
        ::= { jnxBgpM2CfgPeerTimersEntry 4 }


    jnxBgpM2CfgPeerMinRouteAdverInter OBJECT-TYPE
        SYNTAX     Unsigned32 (0..65535)
        MAX-ACCESS read-create
        STATUS     current
        DESCRIPTION
            "Time interval in seconds for the
             MinRouteAdvertisementInterval timer.  The suggested
             value for this timer is 30 seconds."
        DEFVAL {
            30
        }
        ::= { jnxBgpM2CfgPeerTimersEntry 5 }


    --
    -- Per-peer configuration extensions
    --

    jnxBgpM2CfgPeerExtensions
        OBJECT IDENTIFIER ::= { jnxBgpM2PeerConfiguration 6 }


    jnxBgpM2CfgPeerNonCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgPeerExtensions 1 }


    jnxBgpM2CfgPeerCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgPeerExtensions 2 }


    --
    -- Peer route reflection configuration
    --

    jnxBgpM2CfgPeerRouteReflectionExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgPeerNonCapExts 2796 }


    jnxBgpM2CfgPeerReflectorClientTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2CfgPeerReflectorClientEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table of route reflection client settings on a per-peer
             basis."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2CfgPeerRouteReflectionExts 1 }


    jnxBgpM2CfgPeerReflectorClientEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2CfgPeerReflectorClientEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing data on a per-peer basis on whether
             the peer is configured as a route reflector client."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        AUGMENTS {
            jnxBgpM2CfgPeerEntry
        }
        ::= { jnxBgpM2CfgPeerReflectorClientTable 1 }


    JnxBgpM2CfgPeerReflectorClientEntry ::= SEQUENCE {
        jnxBgpM2CfgPeerReflectorClient
            INTEGER
    }

    jnxBgpM2CfgPeerReflectorClient OBJECT-TYPE
        SYNTAX    INTEGER {
            nonClient(0),
            client(1),
            meshedClient(2)
        }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This value indicates whether the given peer is a
             reflector client of this router, or not.  A value of
             nonClient indicates that this peer is not a reflector
             client.  A value of client indicates that this peer is a
             reflector client that is not fully meshed with other
             reflector clients.  A value of meshedClient indicates
             that the peer is a reflector client and is fully meshed
             with all other reflector clients.

             This value must be nonClient (0) for BGP external peers."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2CfgPeerReflectorClientEntry 1 }


    --
    -- Peer AS Confederations Extensions
    --

    jnxBgpM2CfgPeerASConfederationExts
        OBJECT IDENTIFIER ::= { jnxBgpM2CfgPeerNonCapExts 3065 }


    jnxBgpM2CfgPeerConfedMemberTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2CfgPeerConfedMemberEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table of confederation member settings on a per-peer
             basis."
        REFERENCE
            "RFC 3065 - BGP Confederations"
        ::= { jnxBgpM2CfgPeerASConfederationExts 1 }


    jnxBgpM2CfgPeerConfedMemberEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2CfgPeerConfedMemberEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing data on a per-peer basis on whether
             the peer is configured as a BGP confederation member."
        REFERENCE
            "RFC 3065 - BGP Confederations"
        AUGMENTS {
            jnxBgpM2CfgPeerEntry                                  -- *** JNX ***
        }
        ::= { jnxBgpM2CfgPeerConfedMemberTable 1 }


    JnxBgpM2CfgPeerConfedMemberEntry ::= SEQUENCE {
        jnxBgpM2CfgPeerConfedMember
            TruthValue
    }


    jnxBgpM2CfgPeerConfedMember OBJECT-TYPE
        SYNTAX      TruthValue
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This value indicates whether the given peer is in our
             confederation or not."
        REFERENCE
            "RFC 3065 - BGP Confederations"
        ::= { jnxBgpM2CfgPeerConfedMemberEntry 1 }


    --
    -- BGP NLRI Data
    --

    jnxBgpM2Rib
        OBJECT IDENTIFIER ::= { jnxBgpM2 3 }


    --
    -- NLRI Table
    --

    jnxBgpM2NlriTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2NlriEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The BGP-4 Received Path Attribute Table contains
             information about paths to destination networks
             received from all BGP4 peers.  Collectively, this
             represents the Adj-Ribs-In.  The route where
             jnxBgpM2NlriBest is TRUE represents, for this NLRI,
             the route that is installed in the LocRib from the
             Adj-Ribs-In."
        ::= { jnxBgpM2Rib 1 }


    jnxBgpM2NlriEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2NlriEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Information about a path to a network."
        INDEX {
            jnxBgpM2PeerIndex,
            jnxBgpM2NlriAfi,
            jnxBgpM2NlriSafi,
            jnxBgpM2NlriPrefix,
            jnxBgpM2NlriPrefixLen,
            jnxBgpM2NlriIndex
        }
        ::= { jnxBgpM2NlriTable 1 }


    JnxBgpM2NlriEntry ::= SEQUENCE {
        jnxBgpM2NlriIndex
            Unsigned32,
        jnxBgpM2NlriAfi
            InetAddressType,
        jnxBgpM2NlriSafi
            JnxBgpM2Safi,
        jnxBgpM2NlriPrefix
            InetAddress,
        jnxBgpM2NlriPrefixLen
            InetAddressPrefixLength,
        jnxBgpM2NlriBest
            TruthValue,
        jnxBgpM2NlriCalcLocalPref
            Unsigned32,
        jnxBgpM2PathAttrIndex
            Unsigned32,
        jnxBgpM2NlriOpaqueType
            INTEGER,
        jnxBgpM2NlriOpaquePointer
            RowPointer
    }


    jnxBgpM2NlriIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This index allows for multiple instances of a base
             prefix for a certain AFI SAFI from a given peer.
             This is currently useful for two things:
             1. Allowing for a peer in future implementations to
                send more than a single route instance.
             2. Allow for extensions which extend the NLRI field
                to send the same prefix while utilizing other
                extension specific information.  An example of
                this is RFC 3107 - Carrying MPLS labels in BGP."
        REFERENCE
            "RFC 3107 - Carrying Label Information in BGP-4"
        ::= { jnxBgpM2NlriEntry 1 }


    jnxBgpM2NlriAfi OBJECT-TYPE
        SYNTAX     InetAddressType
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The address family of the prefix for this NLRI."
        ::= { jnxBgpM2NlriEntry 2 }


    jnxBgpM2NlriSafi OBJECT-TYPE
        SYNTAX     JnxBgpM2Safi
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The subsequent address family of the prefix for
             this NLRI"
        REFERENCE
            "RFC 2858 - Multiprotocol Extensions for BGP-4"
        ::= { jnxBgpM2NlriEntry 3 }


    jnxBgpM2NlriPrefix OBJECT-TYPE
        SYNTAX     InetAddress  (SIZE (4..20))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "An IP address prefix in the Network Layer
             Reachability Information field.  This object
             is an IP address containing the prefix with
             length specified by
             jnxBgpM2PathAttrAddrPrefixLen.
             Any bits beyond the length specified by
             jnxBgpM2PathAttrAddrPrefixLen are zeroed."
        ::= { jnxBgpM2NlriEntry 4 }


    jnxBgpM2NlriPrefixLen OBJECT-TYPE
        SYNTAX     InetAddressPrefixLength
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Length in bits of the address prefix in
             the Network Layer Reachability Information field."
        ::= { jnxBgpM2NlriEntry 5 }


    jnxBgpM2NlriBest OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "An indication of whether or not this route
             was chosen as the best BGP4 route."
        ::= { jnxBgpM2NlriEntry 6 }


    jnxBgpM2NlriCalcLocalPref OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The degree of preference calculated by the
             receiving BGP4 speaker for an advertised
             route."
        ::= { jnxBgpM2NlriEntry 7 }


    jnxBgpM2PathAttrIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is a unique index for the per-NLRI entry
             in the jnxBgpM2PeerAttrTable.  It is assigned by the
             agent at the point of creation of the jnxBgpM2PeerAttrTable
             row entry.  While its value is guaranteed to be unique
             at any time, it is otherwise opaque to the management
             application with respect to its value or the contiguity
             of jnxBgpM2PeerAttrIndex row instance values across rows
             of the jnxBgpM2PeerAttrTable.  It is used to provide an
             index structure for other tables whose data is logically
             per-peer, per-NLRI."
        ::= { jnxBgpM2NlriEntry 8 }


    jnxBgpM2NlriOpaqueType OBJECT-TYPE
        SYNTAX     INTEGER {
            none(0),
            bgpMplsLabelStack(1)
        }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This object enumerates the type of the row that is
             pointed to in the table row jnxBgpM2NlriOpaquePointer
             instance, if jnxBgpM2NlriOpaquePointer is in fact not
             a zero length.  jnxBgpM2NlriOpaqueType is necessary since
             the data referenced by jnxBgpM2NlriOpaquePointer is
             opaque to BGP.  For example, in the case of RFC 3107,
             the label stack that is pointed to may occur in the
             mplsLabelStackTable from the MPLS-LSR-MIB, and the
             instance value of jnxBgpM2NlriOpaqueType would be
             bgpMplsLabelStack(1)."
        REFERENCE
            "RFC 3107 - Carrying Label Information in BGP-4
             draft-ietf-mpls-lsr-mib-08.txt"
        ::= { jnxBgpM2NlriEntry 9 }


    jnxBgpM2NlriOpaquePointer OBJECT-TYPE
        SYNTAX     RowPointer
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Pointer to a row that decomposes the data that is
             opaque to the BGP MIB but is sent in the NLRI.
             This RowPointer has zero (0) length data instance
             if jnxBgpM2NlriOpaqueType is none."
        ::= { jnxBgpM2NlriEntry 10 }


    --
    -- Adj-Ribs-Out Table
    --

    jnxBgpM2AdjRibsOutTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2AdjRibsOutEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "This table contains on a per-peer basis one or more
             routes from the jnxBgpM2NlriTable that have been
             placed in this peer's Adj-Ribs-Out."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. 3.2"
        ::= { jnxBgpM2Rib 2 }


    jnxBgpM2AdjRibsOutEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2AdjRibsOutEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "List of BGP routes that have been placed into a
             peer's Adj-Ribs-Out."
        INDEX {
            jnxBgpM2PeerIndex,
            jnxBgpM2NlriAfi,
            jnxBgpM2NlriSafi,
            jnxBgpM2NlriPrefix,
            jnxBgpM2NlriPrefixLen,
            jnxBgpM2AdjRibsOutIndex
        }
        ::= { jnxBgpM2AdjRibsOutTable 1 }


    JnxBgpM2AdjRibsOutEntry ::= SEQUENCE {
        jnxBgpM2AdjRibsOutIndex
            Unsigned32,
        jnxBgpM2AdjRibsOutRoute
            RowPointer
    }


    jnxBgpM2AdjRibsOutIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Certain extensions to BGP permit multiple instance of
             a per afi, per safi prefix to be advertised to a peer.
             This object allows the enumeration of them."
        ::= { jnxBgpM2AdjRibsOutEntry 1 }


    jnxBgpM2AdjRibsOutRoute OBJECT-TYPE
        SYNTAX     RowPointer
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This object points to the route in the jnxBgpM2NlriTable
             that corresponds to the entry in the peer's
             Adj-Rib-Out.  Outgoing route maps are not
             reflected at this point as those are part of the
             Update-Send process."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. 9.2"
        ::= { jnxBgpM2AdjRibsOutEntry 2 }


    --
    -- BGP Rib Path Attributes Table
    --
    --
    -- Path Attribute Counter
    --

    jnxBgpM2PathAttrCount OBJECT-TYPE
        SYNTAX     Counter32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The number of entries in the jnxBgpM2PathAttrTable."
        ::= { jnxBgpM2Rib 3 }


    --
    -- Path Attributes Table
    --

    jnxBgpM2PathAttrTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PathAttrEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Provides per advertised network-prefix attribute data,
             as advertised over a peering session."
        ::= { jnxBgpM2Rib 4 }


    jnxBgpM2PathAttrEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PathAttrEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Each entry contains data about a given network
             prefix, per-prefix and per-advertising peer."
        INDEX {
            jnxBgpM2PathAttrIndex
        }
        ::= { jnxBgpM2PathAttrTable 1 }


    JnxBgpM2PathAttrEntry ::= SEQUENCE {
        jnxBgpM2PathAttrOrigin
            INTEGER,
        jnxBgpM2PathAttrNextHopAddrType
            InetAddressType,
        jnxBgpM2PathAttrNextHop
            InetAddress,
        jnxBgpM2PathAttrMedPresent
            TruthValue,
        jnxBgpM2PathAttrMed
            Unsigned32,
        jnxBgpM2PathAttrLocalPrefPresent
            TruthValue,
        jnxBgpM2PathAttrLocalPref
            Unsigned32,
        jnxBgpM2PathAttrAtomicAggregate
            INTEGER,
        jnxBgpM2PathAttrAggregatorAS
            InetAutonomousSystemNumber,
        jnxBgpM2PathAttrAggregatorAddr
            JnxBgpM2Identifier,
        jnxBgpM2AsPathCalcLength
            Unsigned32,
        jnxBgpM2AsPathString
            SnmpAdminString,
        jnxBgpM2AsPathIndex
            Unsigned32
    }


    jnxBgpM2PathAttrOrigin OBJECT-TYPE
        SYNTAX     INTEGER {
            igp(1),-- networks are interior
            egp(2),-- networks learned via the EGP protocol
            incomplete(3) -- undetermined
            }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The ultimate origin of the path information."
        ::= { jnxBgpM2PathAttrEntry 1 }


    jnxBgpM2PathAttrNextHopAddrType OBJECT-TYPE
        SYNTAX      InetAddressType
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The address family of the address for
             the border router that should be used
             to access the destination network."
        ::= { jnxBgpM2PathAttrEntry 2 }


    jnxBgpM2PathAttrNextHop OBJECT-TYPE
        SYNTAX     InetAddress (SIZE(4..20))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The address of the border router that
             should be used to access the destination
             network.  This address is the nexthop
             address received in the UPDATE packet.
             The address family of this object will be the
             same as that of the prefix in this row.

             Note that for RFC2545 style double nexthops,
             this object will always contain the global scope
             nexthop.  jnxBgpM2LinkLocalNextHopTable will contain
             the linklocal scope nexthop.

             In the case that the mechanism documented in
             draft-kato-bgp-ipv6-link-local-00.txt is used and
             only a link local nexthop has been sent, ,
             jnxBgpM2LinkLocalNextHopPresent will be false
             and jnxBgpM2PathAttrNextHop will contain the link local
             nexthop."
        ::= { jnxBgpM2PathAttrEntry 3 }


    jnxBgpM2PathAttrMedPresent OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Whether or not the MED value is present.
             If it is not present, the jnxBgpM2PathAttrMed
             object has no useful value and should be set to 0."
        ::= { jnxBgpM2PathAttrEntry 4 }


    jnxBgpM2PathAttrMed OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This metric is used to discriminate
             between multiple exit points to an
             adjacent autonomous system."
        ::= { jnxBgpM2PathAttrEntry 5 }


    jnxBgpM2PathAttrLocalPrefPresent OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Whether or not the LocalPref value is present.
             If it is not present, the jnxBgpM2PathAttrLocalPref
             object has no useful value and should be set to 0."
        ::= { jnxBgpM2PathAttrEntry 6 }


    jnxBgpM2PathAttrLocalPref OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The originating BGP4 speakers degree of
             preference for an advertised route."
        ::= { jnxBgpM2PathAttrEntry 7 }


    jnxBgpM2PathAttrAtomicAggregate OBJECT-TYPE
        SYNTAX     INTEGER {
            atomicAggregatePresent(1),
            atomicAggregateMissing(2)
            }
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "When this object is set to atomicAggregatePresent,
          the ATOMIC_AGGREGATE Path Attribute is present
          and indicates that the NLRI MUST NOT be made
          more specific."
        ::= { jnxBgpM2PathAttrEntry 8 }


    jnxBgpM2PathAttrAggregatorAS OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The AS number of the last BGP4 speaker that
             performed route aggregation.  A value of
             zero (0) indicates the absence of this
             attribute.

             Note propagation of AS of zero is illegal in
             the Internet."
        ::= { jnxBgpM2PathAttrEntry 9 }
    jnxBgpM2PathAttrAggregatorAddr OBJECT-TYPE
        SYNTAX     JnxBgpM2Identifier
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The IP address of the last BGP4 speaker
             that performed route aggregation.  A
             value of 0.0.0.0 indicates the absence
             of this attribute."
        ::= { jnxBgpM2PathAttrEntry 10 }


    jnxBgpM2AsPathCalcLength OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value represents the calculated length of the
             AS Path according to the rules of the BGP specification.
             This value is used in route selection."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. *******.a"
        ::= { jnxBgpM2PathAttrEntry 11 }


    jnxBgpM2AsPathString OBJECT-TYPE
        SYNTAX     SnmpAdminString
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This is a string depicting the autonomous system
             path to this network which was received from the
             peer which advertised it.  The format of the string
             is implementation-dependent, and should be designed
             for operator readability."
        ::= { jnxBgpM2PathAttrEntry 12 }


    jnxBgpM2AsPathIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is a unique index for the decomposed AS Path
             in the jnxBgpM2AsPathTable.  It is assigned by the
             agent at the point of creation of the jnxBgpM2AsPathTable
             row entry.  While its value is guaranteed to be unique
             at any time, it is otherwise opaque to the management
             application with respect to its value or the contiguity
             of jnxBgpM2AsPathIndex row instance values across rows
             of the jnxBgpM2AsPathTable."
        ::= { jnxBgpM2PathAttrEntry 13 }


    --
    -- As-4 byte AS_PATH
    --

    jnxBgpM2AsPath4byteTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2AsPath4byteEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "This table is present for BGP speakers that support
             the AS 4byte specification and are functioning as
             a router between 2-byte and 4-byte AS space."
        REFERENCE
            "draft-ietf-idr-as4bytes-04.txt - BGP support for
             four-octet AS number space"
        ::= { jnxBgpM2Rib 5 }


    jnxBgpM2AsPath4byteEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2AsPath4byteEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Each row contains the information for the AS 4-byte
             extension's NEW_AS_PATH and NEW_AGGREGATOR attributes."
        AUGMENTS {
            jnxBgpM2PathAttrEntry
        }
        ::= { jnxBgpM2AsPath4byteTable 1 }


    JnxBgpM2AsPath4byteEntry ::= SEQUENCE {
        jnxBgpM2AsPath4bytePathPresent
            TruthValue,
        jnxBgpM2AsPath4byteAggregatorAS
            InetAutonomousSystemNumber,
        jnxBgpM2AsPath4byteCalcLength
            Unsigned32,
        jnxBgpM2AsPath4byteString
            SnmpAdminString,
        jnxBgpM2AsPath4byteIndex
            Unsigned32
    }


    jnxBgpM2AsPath4bytePathPresent OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value may only be true if this BGP Speaker
             is functioning as a router between ASs that
             are in 2-byte and 4-byte AS space.  If this
             value is true, then the NEW_AS_PATH attributes
             are present and the 4-byte versions of the
             appropriate path attributes are in this row.

             If this value is false, then the following values
             will be present in the row:

             jnxBgpM2PathAttrAggregatorAS - zero (0).
             jnxBgpM2AsPathCalcLength - zero (0).
             jnxBgpM2AsPathString - zero (0) length string.
             jnxBgpM2AsPathIndex - zero (0)."
        ::= { jnxBgpM2AsPath4byteEntry 1 }


    jnxBgpM2AsPath4byteAggregatorAS OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The NEW_AGGREGATOR AS number of the last BGP4 speaker
             that performed route aggregation.  A value of
             zero (0) indicates the absence of this
             attribute.

             Note propagation of AS of zero is illegal in
             the Internet."
        ::= { jnxBgpM2AsPath4byteEntry 2 }


    jnxBgpM2AsPath4byteCalcLength OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value represents the calculated length of the
             NEW_AS_PATH according to the rules of the BGP specification.
             This value is used in route selection."
        REFERENCE
            "draft-ietf-idr-bgp4-17.txt, Sec. *******.a"
        ::= { jnxBgpM2AsPath4byteEntry 3 }


    jnxBgpM2AsPath4byteString OBJECT-TYPE
        SYNTAX     SnmpAdminString
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This is a string depicting the autonomous system
             path to this network which was received from the
             peer which advertised it.  The format of the string
             is implementation-dependent, and should be designed
             for operator readability."
        ::= { jnxBgpM2AsPath4byteEntry 4 }


    jnxBgpM2AsPath4byteIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is a unique index for the decomposed AS Path
             in the jnxBgpM2AsPathTable.  It is assigned by the
             agent at the point of creation of the jnxBgpM2AsPathTable
             row entry.  While its value is guaranteed to be unique
             at any time, it is otherwise opaque to the management
             application with respect to its value or the contiguity
             of jnxBgpM2AsPathIndex row instance values across rows
             of the jnxBgpM2AsPathTable. "
        ::= { jnxBgpM2AsPath4byteEntry 5 }


    --    BGP 4 Path attribute AS Path Table.  There is one row in
    --    this table for each AS which is advertised for a given
    --    route as provided from a peer.

-- JMH
-- We need one of these for the NewAsPath for the 4byte draft

    jnxBgpM2AsPathTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2AsPathEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The BGP-4 Path Attribute AS Path Table
             contains the per network path (NLRI)
             AS PATH data received from the
             advertising BGP peer."
        ::= { jnxBgpM2Rib 6 }


    jnxBgpM2AsPathEntry OBJECT-TYPE                               -- *** JNX ***
        SYNTAX     JnxBgpM2AsPathEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Information about an AS path provided with a path to
             a network."
        INDEX {
            jnxBgpM2PathAttrIndex,
            jnxBgpM2AsPathSegmentIndex,
            jnxBgpM2AsPathElementIndex,
            jnxBgpM2AsPathElementValue
        }
        ::= { jnxBgpM2AsPathTable 1 }


    JnxBgpM2AsPathEntry ::= SEQUENCE {
        jnxBgpM2AsPathSegmentIndex
            Unsigned32,
        jnxBgpM2AsPathElementIndex
            Unsigned32,
        jnxBgpM2AsPathType
            INTEGER,
        jnxBgpM2AsPathElementValue
            InetAutonomousSystemNumber
    }


    jnxBgpM2AsPathSegmentIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "A per-AS path segment index.  This will index a set of
             autonomous systems in an AS path which are part
             of the same sequence or set (as determined by
             the row value of jnxBgpM2AsPathType, which
             should be the same value for each jnxBgpM2AsPathTable
             entry indexed by the same (jnxBgpM2PathAttrIndex,
             jnxBgpM2AsPathIndex) pair)."
        ::= { jnxBgpM2AsPathEntry 1 }                             -- *** JNX ***
    jnxBgpM2AsPathElementIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "A per-AS element index.  This will index a particular
             AS within a sequence or set of autonomous systems in
             an AS path."
        ::= { jnxBgpM2AsPathEntry 2 }                             -- *** JNX ***


    jnxBgpM2AsPathType OBJECT-TYPE
        SYNTAX  INTEGER {
            asSet(1),
            asSequence(2),
            confedSequence(3),
            confedSet(4)
         }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
             "The type of sequence in which this asPath
              was advertised as an attribute.  Note that
              all asPath row instances for a given (jnxBgpM2PathAttrIndex,
              jnxBgpM2AsPathIndex) index pair will have their
              jnxBgpM2AsPathType set to the same value.
              The values for jnxBgpM2AsPathType are
              interpreted as defined in the base BGP document
              and the BGP AS Confederations document."
        REFERENCE
            "draft-ietf-idr-bgp4-16
             RFC 3065 - BGP AS Confederations"
        ::= { jnxBgpM2AsPathEntry 3 }                             -- *** JNX ***


    jnxBgpM2AsPathElementValue OBJECT-TYPE
        SYNTAX     InetAutonomousSystemNumber
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "An AS value for an AS the related NLRI traversed
             in the propagation of its advertisement.  This
             value is to be interpreted in the context of the
             sequence implied by jnxBgpM2AsPathIndex and
             jnxBgpM2AsPathType (and, in sequence of the
             other table rows with the same value of
             jnxBgpM2PathAttrIndex and jnxBgpM2AsPathIndex)."
        ::= { jnxBgpM2AsPathEntry 4 }                             -- *** JNX ***


    --    BGP 4 Path unknown attribute.  There is one row in
    --    this table for each attribute not known by this BGP
    --    implementation (or agent instrumentation), but provided
    --    from a peer.

    jnxBgpM2PathAttrUnknownTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PathAttrUnknownEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The BGP-4 Path Attribute Unknown Table
             contains the per network path (NLRI)
             data on the path attributes advertised
             with a route but not known to the local BGP implementation
             or not otherwise capable of being returned from this agent.

             The absence of row data for a given index value for
             jnxBgpM2PathAttrIndex indicates a lack of such unknown
             attribute information for the indicated network path
             (as indexed by that jnxBgpM2PathAttrIndex value in the
             jnxBgpM2PathAttrTable)."
        ::= { jnxBgpM2Rib 7 }


    jnxBgpM2PathAttrUnknownEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PathAttrUnknownEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Information about an unknown attribute
             provided with a path to a network."
        INDEX {
            jnxBgpM2PathAttrIndex,
            jnxBgpM2PathAttrUnknownIndex
        }
        ::= { jnxBgpM2PathAttrUnknownTable 1 }


    JnxBgpM2PathAttrUnknownEntry ::= SEQUENCE {
        jnxBgpM2PathAttrUnknownIndex
            Unsigned32,
        jnxBgpM2PathAttrUnknownType
            Unsigned32,
        jnxBgpM2PathAttrUnknownValue
            OCTET STRING
    }
    jnxBgpM2PathAttrUnknownIndex OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "An integer index for a row in this table."
        ::= { jnxBgpM2PathAttrUnknownEntry 1 }


    jnxBgpM2PathAttrUnknownType OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "The attribute type advertised with this unknown
             attribute by the peer."
        ::= { jnxBgpM2PathAttrUnknownEntry 2 }


    -- Maximum size of the following is derived as
    --     4096   max message size
    --   -  16    BGP message marker bytes
    --   -   2    BGP message size
    --   -   1    BGP message type (UPDATE with unknown attr)
    --   -   2    UPDATE routes length (even assuming no routes)
    --   -   2    UPDATE path attributes length
    --   -   1    path attribute flag octet
    --   -   2    unknown path attr type (in jnxBgpM2PathAttrUnknownType)
    --  ---------
    --    4070 bytes maximum per-message attribute value data


    jnxBgpM2PathAttrUnknownValue OBJECT-TYPE
        SYNTAX     OCTET STRING (SIZE(0..4070))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "Value of path attribute not understood
             by the base BGP-4 document.

             Octets beyond the maximum size, if any,
             are not recorded by this row object. "
        ::= { jnxBgpM2PathAttrUnknownEntry 3 }


    --
    -- Path Attribute Extensions
    --
    jnxBgpM2PathAttrExtensions
        OBJECT IDENTIFIER ::= { jnxBgpM2Rib 8 }


    jnxBgpM2PathAttrNonCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PathAttrExtensions 1 }


    jnxBgpM2PathAttrCapExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PathAttrExtensions 2 }


    --
    -- Path Attribute Route Reflection Extensions
    --

    --
    -- Originator ID Table
    --

    jnxBgpM2PathAttrRouteReflectionExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PathAttrNonCapExts 2796 }


    jnxBgpM2PathAttrOriginatorIdTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF JnxBgpM2PathAttrOriginatorIdEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Per prefix data pertinent to advertisement of a
             network prefix through an originator."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        ::= { jnxBgpM2PathAttrRouteReflectionExts 1 }


    jnxBgpM2PathAttrOriginatorIdEntry OBJECT-TYPE
        SYNTAX      JnxBgpM2PathAttrOriginatorIdEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Each entry contains data pertinent to a network
             prefix as received through its originating BGP
             route reflector."
        REFERENCE
            "RFC 2796 - BGP Route Reflection"
        INDEX {
            jnxBgpM2PathAttrIndex
        }
        ::= { jnxBgpM2PathAttrOriginatorIdTable 1 }


    JnxBgpM2PathAttrOriginatorIdEntry ::= SEQUENCE {
        jnxBgpM2PathAttrOriginatorId
            JnxBgpM2Identifier
    }


    jnxBgpM2PathAttrOriginatorId OBJECT-TYPE
        SYNTAX      JnxBgpM2Identifier
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The Originator-ID identifying the router that initially
             advertised this destination to a Route Reflector.  A
             value of 0.0.0.0 indicates the absence of this attribute."
        REFERENCE
             "This attribute is defined in [RFC2796]."
        ::= { jnxBgpM2PathAttrOriginatorIdEntry 1 }


    --
    -- Cluster table
    --

    jnxBgpM2PathAttrClusterTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PathAttrClusterEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The BGP-4 Path Attribute Cluster Table
             contains the per network path (NLRI)
             data on the reflection path which a
             route has traversed.  The absence of row
             data for a given index value for jnxBgpM2PathAttrIndex
             indicates a lack of this attribute information
             for the indicated network path (as indexed by
             that jnxBgpM2PathAttrIndex value in the jnxBgpM2PathAttrTable)."
        ::= { jnxBgpM2PathAttrRouteReflectionExts 2 }


    jnxBgpM2PathAttrClusterEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PathAttrClusterEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Information about a cluster traversal
             provided with a path to a network."
        INDEX {
            jnxBgpM2PathAttrIndex,
            jnxBgpM2PathAttrClusterIndex
        }
        ::= { jnxBgpM2PathAttrClusterTable 1 }


    JnxBgpM2PathAttrClusterEntry ::= SEQUENCE {
        jnxBgpM2PathAttrClusterIndex
            Unsigned32,
        jnxBgpM2PathAttrClusterValue
            JnxBgpM2Identifier
    }


    jnxBgpM2PathAttrClusterIndex OBJECT-TYPE
        SYNTAX     Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "An integral index for a row in this table."
        ::= { jnxBgpM2PathAttrClusterEntry 1 }


    jnxBgpM2PathAttrClusterValue OBJECT-TYPE
        SYNTAX      JnxBgpM2Identifier
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "A four octet long value representing a part of the
            reflection path that the route has passed.  Each such four
            octet long value represents the ID of a cluster that
            the route has traversed.  The sequence of this path as
            received in the route advertisement will be preserved in
            the sequence of jnxBgpM2PathAttrClusterTable rows (and the
            jnxBgpM2PathAttrClusterValues in each row) as returned for
            a given jnxBgpM2PathAttrIndex value, and the monotonically
            increasing sequence of jnxBgpM2PathAttrClusterIndex values
            for that jnxBgpM2PathAttrIndex."
        REFERENCE
            "This attribute is defined in [RFC2796]."
        ::= { jnxBgpM2PathAttrClusterEntry 2 }


    --
    -- BGP Communities
    --

    jnxBgpM2PathAttrCommunityExts
        OBJECT IDENTIFIER ::= { jnxBgpM2PathAttrNonCapExts 1997 }


    jnxBgpM2PathAttrCommTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PathAttrCommEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The BGP-4 Path Attribute Community Table
             contains the per network path (NLRI)
             data on the community membership advertised
             with a route.  The absence of row
             data for a given index value for jnxBgpM2PathAttrIndex
             indicates a lack of this attribute information
             for the indicated network path (as indexed by
             that jnxBgpM2PathAttrIndex value in the jnxBgpM2PathAttrTable)."
        ::= { jnxBgpM2PathAttrCommunityExts 1 }


    jnxBgpM2PathAttrCommEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PathAttrCommEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Information about a community association
             provided with a path to a network."
        INDEX {
            jnxBgpM2PathAttrIndex,
            jnxBgpM2PathAttrCommIndex
        }
        ::= { jnxBgpM2PathAttrCommTable 1 }


    JnxBgpM2PathAttrCommEntry ::= SEQUENCE {
        jnxBgpM2PathAttrCommIndex
            Unsigned32,
        jnxBgpM2PathAttrCommValue
            JnxBgpM2Community
    }


    jnxBgpM2PathAttrCommIndex OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "An integer index for a row in this table."
        ::= { jnxBgpM2PathAttrCommEntry 1 }


    jnxBgpM2PathAttrCommValue  OBJECT-TYPE
        SYNTAX      JnxBgpM2Community
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "A value representing a community.   There are certain
             4-octet long values which could be returned in this
             columnar row data that carry additional semantics."
        REFERENCE
            "RFC 1997 - BGP Communities Attribute"
        ::= { jnxBgpM2PathAttrCommEntry 2 }


    --
    -- BGP Extended Communities
    --

    jnxBgpM2PathAttrExtCommTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2PathAttrExtCommEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "The BGP-4 Path Attribute Community Table
            contains the per network path (NLRI)
            data on the extended community membership advertised
            with a route.  The absence of row
            data for a given index value for jnxBgpM2PathAttrIndex
            indicates a lack of this attribute information
            for the indicated network path (as indexed by
            that jnxBgpM2PathAttrIndex value in the jnxBgpM2PathAttrTable).

            XXX JMH - can not assign the OID until an RFC is published."
        ::= { jnxBgpM2PathAttrNonCapExts 65001 }                  -- *** JNX ***


    jnxBgpM2PathAttrExtCommEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2PathAttrExtCommEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Information about an extended community association
             provided with a path to a network."
        INDEX {
            jnxBgpM2PathAttrIndex,
            jnxBgpM2PathAttrExtCommIndex
        }
        ::= { jnxBgpM2PathAttrExtCommTable 1 }


    JnxBgpM2PathAttrExtCommEntry ::= SEQUENCE {
        jnxBgpM2PathAttrExtCommIndex
            Unsigned32,
        jnxBgpM2PathAttrExtCommValue
            JnxBgpM2ExtendedCommunity
    }


    jnxBgpM2PathAttrExtCommIndex OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "An integral index for a row in this table."
        ::= { jnxBgpM2PathAttrExtCommEntry 1 }


    jnxBgpM2PathAttrExtCommValue  OBJECT-TYPE
        SYNTAX      JnxBgpM2ExtendedCommunity
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "A value representing an extended community which was
            received with the route implied by the jnxBgpM2PathAttr
            Index value of this row data.   There are certain
            8-octet long values which could be returned in this
            columnar row data that carry additional semantics."
        REFERENCE
            "BGP-EXTCOMM - BGP Extended Communities Attribute"
        ::= { jnxBgpM2PathAttrExtCommEntry 2 }


    --
    -- RFC 2545 link local nexthop
    --

    jnxBgpM2LinkLocalNextHopTable OBJECT-TYPE
        SYNTAX     SEQUENCE OF JnxBgpM2LinkLocalNextHopEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Table of link local nexthops as sent by RFC 2545
             for IPv6 BGP Speakers."
        REFERENCE
            "RFC 2545 - Use of BGP-4 Multiprotocol Extensions
             for IPv6 Inter-Domain Routing"
        ::= { jnxBgpM2PathAttrNonCapExts 2545 }


    jnxBgpM2LinkLocalNextHopEntry OBJECT-TYPE
        SYNTAX     JnxBgpM2LinkLocalNextHopEntry
        MAX-ACCESS not-accessible
        STATUS     current
        DESCRIPTION
            "Entry containing the link-local nexthops as sent
             by a BGP speaker running RFC 2545 extensions for
             double-nexthops."
        INDEX {
            jnxBgpM2PathAttrIndex
        }
        ::= { jnxBgpM2LinkLocalNextHopTable 1 }


    JnxBgpM2LinkLocalNextHopEntry ::= SEQUENCE {
        jnxBgpM2LinkLocalNextHopPresent
            TruthValue,
        jnxBgpM2LinkLocalNextHop
            InetAddress
    }


    jnxBgpM2LinkLocalNextHopPresent OBJECT-TYPE
        SYNTAX     TruthValue
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value is TRUE if and only if the BGP
             speaker is receiving IPv6 NLRI using the
             RFC 2545 double nexthop convention and it
             has received a link local scope nexthop in
             addition to the global scope nexthop."
        ::= { jnxBgpM2LinkLocalNextHopEntry 1 }


    jnxBgpM2LinkLocalNextHop OBJECT-TYPE
        SYNTAX     InetAddress (SIZE(20))
        MAX-ACCESS read-only
        STATUS     current
        DESCRIPTION
            "This value contains an IPv6 link local address of
            InetAddressType of type ipv6z.  This value is only
            present if jnxBgpM2LinkLocalNextHopPresent is true."
        ::= { jnxBgpM2LinkLocalNextHopEntry 2 }


    --
    -- Conformance Information
    --

    jnxBgpM2Conformance
        OBJECT IDENTIFIER ::= { jnxBgpM2 4 }


    jnxBgpM2MIBCompliances OBJECT IDENTIFIER ::=
        { jnxBgpM2Conformance 1 }


    jnxBgpM2MIBGroups      OBJECT IDENTIFIER ::=
        { jnxBgpM2Conformance 2 }


    jnxBgpM2MIBCompliance MODULE-COMPLIANCE
        STATUS  current
        DESCRIPTION
            "The compliance statement for entities which
            implement the BGP4 mib."
         MODULE  -- this module
         MANDATORY-GROUPS {
            jnxBgpM2TimersGroup,
            jnxBgpM2CountersGroup,
            jnxBgpM2CapabilitiesGroup,                            -- *** JNX ***
            jnxBgpM2AsPathGroup,
            jnxBgpM2As4byteGroup,
            jnxBgpM2BaseGroup,
            jnxBgpM2ErrorsGroup,
            jnxBgpM2PeerGroup,
            jnxBgpM2PathAttributesGroup
            }
        GROUP jnxBgpM2MIBNotificationsGroup
        DESCRIPTION
            "The notifications group is completely optional,
             but highly recommended."
        GROUP jnxBgpM2AuthenticationGroup
        DESCRIPTION
            "The authentication group is
             mandatory only for those implementations which
             support sending and receiving authentication
             information with peers in the BGP Authentication
             Field."
        GROUP jnxBgpM2CommunitiesGroup
        DESCRIPTION
            "The communities group is mandatory only for those
             which support the BGP community attribute."
        GROUP jnxBgpM2ExtCommunitiesGroup
        DESCRIPTION
            "The communities group is mandatory only for those
             which support the BGP extended community attribute."
        GROUP jnxBgpM2RouteReflectionGroup
        DESCRIPTION
            "The communities group is mandatory only for those
             which support the BGP route reflection relationships."
        GROUP jnxBgpM2AsConfederationGroup
        DESCRIPTION
            "The communities group is mandatory only for those
             which support the BGP confederation membership."
-- *** JNX ***   GROUP jnxBgpM2TimersGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2CountersGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2CapabilitiesGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2AsPathGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2As4byteGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2BaseGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2ErrorsGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2PeerGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
-- *** JNX ***   GROUP jnxBgpM2PathAttributesGroup
-- *** JNX ***   DESCRIPTION
-- *** JNX ***       "This group is mandatory for all agent implementations."
        GROUP jnxBgpM2PeerConfigurationGroup
        DESCRIPTION
            "This group is optional for implementations that wish to
             support configuration via SNMP."
        GROUP jnxBgpM2PeerAuthConfigurationGroup
        DESCRIPTION
            "This group is optional for implementations that wish to
             support configuration of BGP authentication via SNMP.
             Implementation of this feature requires support of the
             jnxBgpM2PeerConfigurationGroup."
        GROUP jnxBgpM2PeerRouteReflectorCfgGroup
        DESCRIPTION
            "This group is optional for implementations that wish to
             support configuration of route reflection via SNMP.
             Implementation of this feature requires support of the
             jnxBgpM2PeerConfigurationGroup."
        GROUP jnxBgpM2PeerAsConfederationCfgGroup
        DESCRIPTION
            "This group is optional for implementations that wish to
             support configuration of BGP AS Confederations via SNMP.
             Implementation of this feature requires support of the
             jnxBgpM2PeerConfigurationGroup."
        GROUP jnxBgpM2Rfc2545Group
        DESCRIPTION
            "This group is required for peers that support exchanging
             of IPv6 NLRI per RFC 2545."
        ::= { jnxBgpM2MIBCompliances 1 }


    jnxBgpM2AuthenticationGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2SupportedAuthCode,
            jnxBgpM2SupportedAuthValue,
            jnxBgpM2PeerAuthSent,
            jnxBgpM2PeerAuthSentCode,
            jnxBgpM2PeerAuthSentValue,
            jnxBgpM2PeerAuthRcvd,
            jnxBgpM2PeerAuthRcvdCode,
            jnxBgpM2PeerAuthRcvdValue
        }
        STATUS current
        DESCRIPTION
            "Objects associated with BGP authentication."
        ::= { jnxBgpM2MIBGroups 1 }


    jnxBgpM2CommunitiesGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2PathAttrCommIndex,
            jnxBgpM2PathAttrCommValue
        }
        STATUS current
        DESCRIPTION
            "Objects associated with BGP communities."
        ::= { jnxBgpM2MIBGroups 2 }


    jnxBgpM2ExtCommunitiesGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2PathAttrExtCommIndex,
            jnxBgpM2PathAttrExtCommValue
        }
        STATUS current
        DESCRIPTION
            "Objects associated with BGP extended communities."
        ::= { jnxBgpM2MIBGroups 3 }


    jnxBgpM2RouteReflectionGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2RouteReflector,
            jnxBgpM2ClusterId,
            jnxBgpM2PeerReflectorClient,
            jnxBgpM2PathAttrOriginatorId,
            jnxBgpM2PathAttrClusterIndex,
            jnxBgpM2PathAttrClusterValue
        }
        STATUS current
        DESCRIPTION
            "Objects associated with BGP route reflection."
        ::= { jnxBgpM2MIBGroups 4 }


    jnxBgpM2AsConfederationGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2ConfederationRouter,
            jnxBgpM2ConfederationId,
            jnxBgpM2PeerConfedMember
        }
        STATUS current
        DESCRIPTION
            "Objects associated with BGP confederation membership."
        ::= { jnxBgpM2MIBGroups 5 }


    jnxBgpM2TimersGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2PeerFsmEstablishedTime,
            jnxBgpM2PeerInUpdatesElapsedTime,
            jnxBgpM2PeerConnectRetryInterval,
            jnxBgpM2PeerHoldTimeConfigured,
            jnxBgpM2PeerKeepAliveConfigured,
            jnxBgpM2PeerMinASOrigInterval,
            jnxBgpM2PeerMinRouteAdverInterval,
            jnxBgpM2PeerHoldTime,
            jnxBgpM2PeerKeepAlive
        }
        STATUS current
        DESCRIPTION
            "Objects associated with BGP peering timers."
        ::= { jnxBgpM2MIBGroups 6 }


    jnxBgpM2CountersGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2PeerInUpdates,
            jnxBgpM2PeerOutUpdates,
            jnxBgpM2PeerInTotalMessages,
            jnxBgpM2PeerOutTotalMessages,
            jnxBgpM2PeerFsmEstablishedTrans,
            jnxBgpM2PrefixCountersAfi,
            jnxBgpM2PrefixCountersSafi,
            jnxBgpM2PrefixInPrefixes,
            jnxBgpM2PrefixInPrefixesAccepted,
            jnxBgpM2PrefixInPrefixesRejected,
            jnxBgpM2PrefixOutPrefixes
        }
        STATUS current
        DESCRIPTION
            "Objects to count discrete events and exchanges on BGP
             sessions."
         ::= { jnxBgpM2MIBGroups 7 }


    jnxBgpM2CapabilitiesGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2CapabilitySupportAvailable,
            jnxBgpM2SupportedCapabilityCode,
            jnxBgpM2SupportedCapability,
            jnxBgpM2PeerCapAnnouncedCode,
            jnxBgpM2PeerCapAnnouncedIndex,
            jnxBgpM2PeerCapAnnouncedValue,
            jnxBgpM2PeerCapReceivedCode,
            jnxBgpM2PeerCapReceivedIndex,
            jnxBgpM2PeerCapReceivedValue
        }
        STATUS current
        DESCRIPTION
            "Objects to report capabilities as received on BGP
             sessions."
        ::= { jnxBgpM2MIBGroups 8 }


    jnxBgpM2AsPathGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2AsPathSegmentIndex,
            jnxBgpM2AsPathElementIndex,
            jnxBgpM2AsPathType,
            jnxBgpM2AsPathElementValue
        }
        STATUS current
        DESCRIPTION
            "Objects to report AS paths received on BGP NLRIs."
        ::= { jnxBgpM2MIBGroups 9 }


    jnxBgpM2As4byteGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2AsSize,
            jnxBgpM2AsPath4bytePathPresent,
            jnxBgpM2AsPath4byteAggregatorAS,
            jnxBgpM2AsPath4byteCalcLength,
            jnxBgpM2AsPath4byteString,
            jnxBgpM2AsPath4byteIndex
        }
        STATUS current
        DESCRIPTION
            "AS Size objects."
        ::= { jnxBgpM2MIBGroups 10 }


    jnxBgpM2BaseGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2LocalAs,
            jnxBgpM2LocalIdentifier,
            jnxBgpM2VersionIndex,
            jnxBgpM2VersionSupported
        }
        STATUS current
        DESCRIPTION
            "Basic objects in local BGP implementation."
        ::= { jnxBgpM2MIBGroups 11 }


    jnxBgpM2ErrorsGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2PeerLastErrorReceived,
            jnxBgpM2PeerLastErrorReceivedData,
            jnxBgpM2PeerLastErrorReceivedTime,
            jnxBgpM2PeerLastErrorReceivedText,
            jnxBgpM2PeerLastErrorSent,
            jnxBgpM2PeerLastErrorSentData,
            jnxBgpM2PeerLastErrorSentTime,
            jnxBgpM2PeerLastErrorSentText
        }
        STATUS current
        DESCRIPTION
            "Errors received on BGP peering sessions."
        ::= { jnxBgpM2MIBGroups 12 }


    jnxBgpM2PeerGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2PeerIdentifier,
            jnxBgpM2PeerState,
            jnxBgpM2PeerStatus,
            jnxBgpM2PeerConfiguredVersion,
            jnxBgpM2PeerNegotiatedVersion,
            jnxBgpM2PeerLocalAddrType,
            jnxBgpM2PeerLocalAddr,
            jnxBgpM2PeerLocalPort,
            jnxBgpM2PeerLocalAs,
            jnxBgpM2PeerRemoteAddrType,
            jnxBgpM2PeerRemoteAddr,
            jnxBgpM2PeerRemotePort,
            jnxBgpM2PeerRemoteAs,
            jnxBgpM2PeerRoutingInstance,                          -- *** JNX ***
            jnxBgpM2PeerIndex
        }
        STATUS current
        DESCRIPTION
            "Core object types on BGP peering sessions."
        ::= { jnxBgpM2MIBGroups 13 }


    jnxBgpM2PathAttributesGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2PathAttrCount,
            jnxBgpM2AsPathCalcLength,
            jnxBgpM2AsPathElementValue,
            jnxBgpM2AsPathIndex,
            jnxBgpM2AsPathString,
            jnxBgpM2AsPathType,
            jnxBgpM2NlriAfi,
            jnxBgpM2NlriBest,
            jnxBgpM2NlriPrefix,
            jnxBgpM2NlriPrefixLen,
            jnxBgpM2NlriSafi,
            jnxBgpM2NlriOpaqueType,
            jnxBgpM2NlriOpaquePointer,
            jnxBgpM2NlriIndex,
            jnxBgpM2NlriCalcLocalPref,
            jnxBgpM2AdjRibsOutIndex,
            jnxBgpM2AdjRibsOutRoute,
            jnxBgpM2PathAttrAggregatorAS,
            jnxBgpM2PathAttrAggregatorAddr,
            jnxBgpM2PathAttrAtomicAggregate,
            jnxBgpM2PathAttrIndex,
            jnxBgpM2PathAttrLocalPref,
            jnxBgpM2PathAttrLocalPrefPresent,
            jnxBgpM2PathAttrMed,
            jnxBgpM2PathAttrMedPresent,
            jnxBgpM2PathAttrNextHop,
            jnxBgpM2PathAttrNextHopAddrType,
            jnxBgpM2PathAttrOrigin,
            jnxBgpM2PathAttrUnknownIndex,
            jnxBgpM2PathAttrUnknownType,
            jnxBgpM2PathAttrUnknownValue
        }
        STATUS current
        DESCRIPTION
            "Attributes received on BGP peering sessions."
        ::= { jnxBgpM2MIBGroups 14 }

    jnxBgpM2PeerConfigurationGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2CfgBaseScalarStorageType,
            jnxBgpM2CfgLocalAs,
            jnxBgpM2CfgLocalIdentifier,
            jnxBgpM2CfgPeerAdminStatus,
            jnxBgpM2CfgPeerNextIndex,
            jnxBgpM2CfgPeerConfiguredVersion,
            jnxBgpM2CfgAllowVersionNegotiation,
            jnxBgpM2CfgPeerLocalAddrType,
            jnxBgpM2CfgPeerLocalAddr,
            jnxBgpM2CfgPeerLocalAs,
            jnxBgpM2CfgPeerRemoteAddrType,
            jnxBgpM2CfgPeerRemoteAddr,
            jnxBgpM2CfgPeerRemotePort,
            jnxBgpM2CfgPeerRemoteAs,
            jnxBgpM2CfgPeerEntryStorageType,
            jnxBgpM2CfgPeerError,
            jnxBgpM2CfgPeerBgpPeerEntry,
            jnxBgpM2CfgPeerRowEntryStatus,
--            jnxBgpM2CfgPeerIndex,                                  *** JNX ***
            jnxBgpM2CfgPeerStatus,
            jnxBgpM2CfgPeerConnectRetryInterval,
            jnxBgpM2CfgPeerHoldTimeConfigured,
            jnxBgpM2CfgPeerKeepAliveConfigured,
            jnxBgpM2CfgPeerMinASOrigInterval,
            jnxBgpM2CfgPeerMinRouteAdverInter
        }
        STATUS current
        DESCRIPTION
            "Configuration objects for BGP peers."
        ::= { jnxBgpM2MIBGroups 15 }


    jnxBgpM2PeerAuthConfigurationGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2CfgPeerAuthEnabled,
            jnxBgpM2CfgPeerAuthCode,
            jnxBgpM2CfgPeerAuthValue
        }
        STATUS current
        DESCRIPTION
            "Configuration objects for BGP peers that support
             authentication."
        ::= { jnxBgpM2MIBGroups 16 }


    jnxBgpM2PeerRouteReflectorCfgGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2CfgRouteReflector,
            jnxBgpM2CfgClusterId,
            jnxBgpM2CfgPeerReflectorClient
        }
        STATUS current
        DESCRIPTION
            "Configuration objects for BGP peers that support route
             reflection."
        ::= { jnxBgpM2MIBGroups 17 }


    jnxBgpM2PeerAsConfederationCfgGroup OBJECT-GROUP
        OBJECTS {
            jnxBgpM2CfgConfederationRouter,
            jnxBgpM2CfgConfederationId,
            jnxBgpM2CfgPeerConfedMember
        }
        STATUS current
        DESCRIPTION
            "Configuration objects for BGP peers that support BGP
             confederations."
        ::= { jnxBgpM2MIBGroups 18 }


    jnxBgpM2MIBNotificationsGroup NOTIFICATION-GROUP
        NOTIFICATIONS {
            jnxBgpM2Established,
            jnxBgpM2BackwardTransition
        }
        STATUS  current
        DESCRIPTION
            "This group contains objects for notifications
             supported by this mib module."
        ::= { jnxBgpM2MIBGroups 19 }


    jnxBgpM2Rfc2545Group OBJECT-GROUP
        OBJECTS {
            jnxBgpM2LinkLocalNextHopPresent,
            jnxBgpM2LinkLocalNextHop
        }
        STATUS current
        DESCRIPTION
            "This group is required for peers that support exchanging
             of IPv6 NLRI per RFC 2545."
        ::= { jnxBgpM2MIBGroups 20 }

END
