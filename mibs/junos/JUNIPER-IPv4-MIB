--
-- $Id: jnx-ipv4.mib,v 1.3 2003-07-18 22:57:37 dchuang Exp $
-- Juniper Enterprise Specific MIB: ipv4 MIB Extension
-- 
-- Copyright (c) 2001-2003, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--

JUNIPER-IPv4-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, IpAddress
        FROM SNMPv2-SMI
    jnxMibs
        FROM JUNIPER-SMI;

jnxIpv4 MODULE-IDENTITY
    LAST-UPDATED "200307182153Z" -- Fri Jul 18 21:53:52 2003 UTC
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
            "        Juniper Technical Assistance Center
                     Juniper Networks, Inc.
                     1133 Innovation Way
                     Sunnyvale, CA 94089
                     E-mail: <EMAIL>"

    DESCRIPTION
            "The MIB modules extends the ifTable as
             defined in IF-MIB."
    REVISION      "200108310000Z" 
    DESCRIPTION
               "Initial revision."
    ::= { jnxMibs 12 }



--
-- define branches for ipv4 config info
--
jnxIpv4Config        OBJECT IDENTIFIER ::= { jnxIpv4 1 }

--
-- Ipv4 Address Table
--
    jnxIpv4AddrTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF JnxIpv4AddrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The table of addressing information relevant to this
                entity's IP addresses."
        ::= { jnxIpv4Config 1 }
    
    jnxIpv4AddrEntry OBJECT-TYPE
        SYNTAX      JnxIpv4AddrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The addressing information for one of this entity's IP
                addresses."
        INDEX      { jnxIpv4AdEntIfIndex, jnxIpv4AdEntAddr }
        ::= { jnxIpv4AddrTable 1 }
    
    JnxIpv4AddrEntry ::= SEQUENCE {
            jnxIpv4AdEntIfIndex       INTEGER,
            jnxIpv4AdEntAddr          IpAddress,
            jnxIpv4AdEntNetMask       IpAddress,
            jnxIpv4AdEntBcastAddr     INTEGER,
            jnxIpv4AdEntReasmMaxSize  INTEGER
        }
    
    jnxIpv4AdEntIfIndex OBJECT-TYPE
        SYNTAX      INTEGER (1..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The index value which uniquely identifies the interface to
                which this entry is applicable.  The interface identified by
                a particular value of this index is the same interface as
                identified by the same value of RFC 1573's ifIndex."
        ::= { jnxIpv4AddrEntry 1 }

    jnxIpv4AdEntAddr OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
                "The IP address to which this entry's addressing information
                pertains."
        ::= { jnxIpv4AddrEntry 2 }
    
    jnxIpv4AdEntNetMask OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The subnet mask associated with the IP address of this
                entry.  The value of the mask is an IP address with all the
                network bits set to 1 and all the hosts bits set to 0."
        ::= { jnxIpv4AddrEntry 3 }
    
    jnxIpv4AdEntBcastAddr OBJECT-TYPE
        SYNTAX      INTEGER (0..1)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The value of the least-significant bit in the IP broadcast
                address used for sending datagrams on the (logical)
                interface associated with the IP address of this entry.  For
                example, when the Internet standard all-ones broadcast
                address is used, the value will be 1.  This value applies to
                both the subnet and network broadcasts addresses used by the
                entity on this (logical) interface."
        ::= { jnxIpv4AddrEntry 4 }
    
    jnxIpv4AdEntReasmMaxSize OBJECT-TYPE
        SYNTAX      INTEGER (0..65535)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                "The size of the largest IP datagram which this entity can
                re-assemble from incoming IP fragmented datagrams received
                on this interface."
        ::= { jnxIpv4AddrEntry 5 }
    
    

END
