--
-- Juniper Enterprise Specific MIB: Structure of Management Information
-- 
-- Copyright (c) 2010-2011, Juniper Networks, Inc.
-- All rights reserved.
--
-- The contents of this document are subject to change without notice.
--


JUNIPER-LSYSSP-NATSRCPATAD-MIB DEFINITIONS ::= BEG<PERSON>
   IMPORTS
      MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, 
      Unsigned32
        FROM SNMPv2-SMI
      DisplayString
        FROM SNMPv2-TC
      jnxLsysSpNATsrcpatad                  
        FROM JUNIPER-LSYS-SECURITYPROFILE-MIB
    ;
    
    jnxLsysSpNATsrcpatadMIB MODULE-IDENTITY
        LAST-UPDATED  "201005191644Z" -- May 19, 2010
        ORGANIZATION  "Juniper Networks, Inc."
        CONTACT-INFO
            "Juniper Technical Assistance Center
             Juniper Networks, Inc.
             1133 Innovation Way
             Sunnyvale, CA 94089

             E-mail: <EMAIL>
             HTTP://www.juniper.net"
        DESCRIPTION
            "This module defines the NAT-src-pat-address-specific MIB for 
             Juniper Enterprise Logical-System (LSYS) security profiles.  
             Juniper documentation is recommended as the reference. 

             The LSYS security profile provides various static and dynamic 
             resource management by observing resource quota limits. 
             Security NAT-src-pat-address resource is the focus in this MIB. 
            "
        ::= { jnxLsysSpNATsrcpatad 1 }

    jnxLsysSpNATsrcpatadObjects  OBJECT IDENTIFIER ::= { jnxLsysSpNATsrcpatadMIB 1 }
    jnxLsysSpNATsrcpatadSummary  OBJECT IDENTIFIER ::= { jnxLsysSpNATsrcpatadMIB 2 }
    
 
-- **********************************************************************
-- Tabular NAT-src-pat-address resource information objects per LSYS:
--   Below are NAT-src-pat-address resource table indexed by LSYS name.
-- **********************************************************************

-- The NAT-src-pat-address resource table per LSYS

    jnxLsysSpNATsrcpatadTable OBJECT-TYPE
        SYNTAX              SEQUENCE OF JnxLsysSpNATsrcpatadEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION 
            "LSYSPROFILE NAT-src-pat-address objects for NAT-src-pat-address 
             resource consumption per LSYS."  
    ::= { jnxLsysSpNATsrcpatadObjects 1 }
    
    jnxLsysSpNATsrcpatadEntry OBJECT-TYPE
        SYNTAX              JnxLsysSpNATsrcpatadEntry
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION  
            "An entry in NAT-src-pat-address resource table."
    INDEX { IMPLIED jnxLsysSpNATsrcpatadLsysName }          
    ::= { jnxLsysSpNATsrcpatadTable 1 }          

    JnxLsysSpNATsrcpatadEntry ::= 
       SEQUENCE {
          jnxLsysSpNATsrcpatadLsysName    DisplayString,
          jnxLsysSpNATsrcpatadProfileName DisplayString,
          jnxLsysSpNATsrcpatadUsage       Unsigned32,
          jnxLsysSpNATsrcpatadReserved    Unsigned32,
          jnxLsysSpNATsrcpatadMaximum     Unsigned32
    }   
 
-- Entry definitions for the NAT-src-pat-address resource table
 
    jnxLsysSpNATsrcpatadLsysName       OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..64))
        MAX-ACCESS          not-accessible
        STATUS              current
        DESCRIPTION
            "The name of the logical system for which NAT-src-pat-address 
             resource information is retrieved. "
        ::= { jnxLsysSpNATsrcpatadEntry 1 }

    jnxLsysSpNATsrcpatadProfileName    OBJECT-TYPE
        SYNTAX              DisplayString (SIZE(1..32))
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The security profile name string for the LSYS."
    ::= { jnxLsysSpNATsrcpatadEntry 2 }

    jnxLsysSpNATsrcpatadUsage          OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION  
            "The current resource usage count for the LSYS."
    ::= { jnxLsysSpNATsrcpatadEntry 3 }
    
    jnxLsysSpNATsrcpatadReserved       OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The reserved resource count for the LSYS."
    ::= { jnxLsysSpNATsrcpatadEntry 4 } 

    jnxLsysSpNATsrcpatadMaximum        OBJECT-TYPE
        SYNTAX              Unsigned32
        MAX-ACCESS          read-only
        STATUS              current
        DESCRIPTION
            "The maximum allowed resource usage count for the LSYS."
    ::= { jnxLsysSpNATsrcpatadEntry 5 }


-- **********************************************************************
-- The NAT-src-pat-address resource information summary:
-- **********************************************************************

    jnxLsysSpNATsrcpatadUsedAmount         OBJECT-TYPE
        SYNTAX                  Unsigned32 
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION
           "The NAT-src-pat-address resource consumption over all LSYS."
    ::= { jnxLsysSpNATsrcpatadSummary 1 }          

    jnxLsysSpNATsrcpatadMaxQuota           OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The NAT-src-pat-address resource maximum quota for the whole 
            device for all LSYS."
    ::= { jnxLsysSpNATsrcpatadSummary 2 }
    
    jnxLsysSpNATsrcpatadAvailableAmount    OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The NAT-src-pat-address resource available in the whole device."
    ::= { jnxLsysSpNATsrcpatadSummary 3 }
    
    jnxLsysSpNATsrcpatadHeaviestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The most amount of NAT-src-pat-address resource consumed of a 
            LSYS."
    ::= { jnxLsysSpNATsrcpatadSummary 4 }
    
    jnxLsysSpNATsrcpatadHeaviestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the most NAT-src-pat-address resource."
    ::= { jnxLsysSpNATsrcpatadSummary 5 }
    
    jnxLsysSpNATsrcpatadLightestUsage      OBJECT-TYPE
        SYNTAX                  Unsigned32
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The least amount of NAT-src-pat-address resource consumed of a 
            LSYS."
    ::= { jnxLsysSpNATsrcpatadSummary 6 }
    
    jnxLsysSpNATsrcpatadLightestUser       OBJECT-TYPE
        SYNTAX                  DisplayString (SIZE(1..64))
        MAX-ACCESS              read-only
        STATUS                  current
        DESCRIPTION 
           "The LSYS name that consume the least NAT-src-pat-address resource."
    ::= { jnxLsysSpNATsrcpatadSummary 7 }
    


 -- ***************************************************************
 -- definition of NAT-src-pat-address resource related traps. (TBD)
 -- ***************************************************************

--
-- End of File 
--

END
