-- ****************************************************************************
--  COLUBRIS-DEVICE-IF-MIB definitions
--
--  Copyright (c) 2006, Colubris Networks, Inc.
--  All Rights Reserved.
--
--  Colubris Device Interface MIB file.
--
-- ****************************************************************************


COLUBRIS-DEVICE-IF-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE,
    Integer32, Counter32, Counter64, IpAddress
        FROM    SNMPv2-SMI
    DisplayString, MacAddress
        FROM    SNMPv2-TC
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM    SNMPv2-CONF
    colubrisMgmtV2
        FROM    COLUBRIS-SMI
    coDevDisIndex
        FROM    COLUBRIS-DEVICE-MIB
;


colubrisDeviceIfMIB MODULE-IDENTITY
    LAST-UPDATED    "200607050000Z"
    ORGANIZATION    "Colubris Networks, Inc."
    CONTACT-INFO    "Colubris Networks
                     Postal: 200 West Street Ste 300
                             Waltham, Massachusetts 02451-1121
                             UNITED STATES
                     Phone:  ****** 684 0001
                     Fax:    ****** 684 0009

                     E-mail: <EMAIL>"
    DESCRIPTION     "Colubris Device Interface MIB."

    ::= { colubrisMgmtV2 24 }


-- colubrisDeviceIfMIB definition
colubrisDeviceIfMIBObjects OBJECT IDENTIFIER ::= { colubrisDeviceIfMIB 1 }

-- colubris Device Interface groups
coDeviceIfStatusGroup OBJECT IDENTIFIER ::= { colubrisDeviceIfMIBObjects 1 }
coDeviceIfStatsGroup  OBJECT IDENTIFIER ::= { colubrisDeviceIfMIBObjects 2 }

-- The Device Interface Status Group
coDeviceIfStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CoDeviceIfStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Device interface status attributes."
    ::= { coDeviceIfStatusGroup 1 }

coDeviceIfStatusEntry OBJECT-TYPE
    SYNTAX      CoDeviceIfStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "An entry in the coDeviceIfStatusTable.
                 coDevDisIndex - Uniquely identifies a device ion the
                                 MultiService Controller.
                 coDevIfStaIfIndex - Uniquely identifies an interface on
                                     the device."
    INDEX       { coDevDisIndex, coDevIfStaIfIndex }
    ::= { coDeviceIfStatusTable 1 }

CoDeviceIfStatusEntry ::= SEQUENCE
{
    coDevIfStaIfIndex                Integer32,
    coDevIfStaFriendlyInterfaceName  DisplayString,
    coDevIfStaType                   INTEGER,
    coDevIfStaVLAN                   Integer32,
    coDevIfStaIpAddress              IpAddress,
    coDevIfStaNetworkMask            IpAddress,
    coDevIfStaMACAddress             MacAddress,
    coDevIfStaState                  INTEGER
}

coDevIfStaIfIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..2147483647)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Specifies the index of an interface on the
                 device."
    ::= { coDeviceIfStatusEntry 1 }

coDevIfStaFriendlyInterfaceName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The friendly name associated with the interface."
    ::= { coDeviceIfStatusEntry 2 }

coDevIfStaType OBJECT-TYPE
    SYNTAX      INTEGER
                {
                    other(1),
                    ethernet(2),
                    l2vlan(3),
                    bridge(4),
                    ieee80211(5),
                    ieee80211Wds(6)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The current state of the interface."
    ::= { coDeviceIfStatusEntry 3 }

coDevIfStaVLAN OBJECT-TYPE
    SYNTAX      Integer32 (0..4094)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Specifies the VLAN associated with the interface.
                 The value 0 is used when coDevIfStaType is not set to
                 l2vlan."
    ::= { coDeviceIfStatusEntry 4 }

coDevIfStaIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The IP address assigned to the interface."
    ::= { coDeviceIfStatusEntry 5 }

coDevIfStaNetworkMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The network mask assigned to the interface."
    ::= { coDeviceIfStatusEntry 6 }

coDevIfStaMACAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The MAC address assigned to the interface."
    ::= { coDeviceIfStatusEntry 7 }

coDevIfStaState OBJECT-TYPE
    SYNTAX      INTEGER
                {
                    up(1),
                    down(2)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The current state of the interface."
    ::= { coDeviceIfStatusEntry 8 }

-- The Device Interface Stats Group
coDeviceIfStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CoDeviceIfStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Device interface statistic attributes."
    ::= { coDeviceIfStatsGroup 1 }

coDeviceIfStatsEntry OBJECT-TYPE
    SYNTAX      CoDeviceIfStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "An entry in the coDeviceIfStatsTable.
                 coDevDisIndex - Uniquely identify a device in the
                                 MultiService Controller.
                 coDevIfStaIfIndex - Uniquely identify an interface on
                                     the device."
    AUGMENTS    { coDeviceIfStatusEntry }
    ::= { coDeviceIfStatsTable 1 }

CoDeviceIfStatsEntry ::= SEQUENCE
{
    coDevIfStsRxBytes                Counter64,
    coDevIfStsRxPackets              Counter32,
    coDevIfStsRxErrors               Counter32,
    coDevIfStsTxBytes                Counter64,
    coDevIfStsTxPackets              Counter32,
    coDevIfStsTxErrors               Counter32
}

coDevIfStsRxBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The total number of octets received on the interface."
    ::= { coDeviceIfStatsEntry 1 }

coDevIfStsRxPackets OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The number of packets delivered by this sub-layer to a
                 higher (sub-)layer."
    ::= { coDeviceIfStatsEntry 2 }

coDevIfStsRxErrors OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The number of inbound packets that contained errors
                 preventing them from being deliverable to a
                 higher-layer protocol."
    ::= { coDeviceIfStatsEntry 3 }

coDevIfStsTxBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The total number of octets transmitted by the
                 interface."
    ::= { coDeviceIfStatsEntry 4 }

coDevIfStsTxPackets OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The total number of packets that higher-level protocols
                 requested to be transmitted."
    ::= { coDeviceIfStatsEntry 5 }

coDevIfStsTxErrors OBJECT-TYPE
    SYNTAX      Counter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "The number of outbound packets that could not be
                 transmitted because of errors."
    ::= { coDeviceIfStatsEntry 6 }


-- Device Interface notifications
colubrisDeviceIfMIBNotificationPrefix OBJECT IDENTIFIER ::= { colubrisDeviceIfMIB 2 }
colubrisDeviceIfMIBNotifications OBJECT IDENTIFIER ::= { colubrisDeviceIfMIBNotificationPrefix 0 }


-- conformance information
colubrisDeviceIfMIBConformance OBJECT IDENTIFIER ::= { colubrisDeviceIfMIB 3 }
colubrisDeviceIfMIBCompliances OBJECT IDENTIFIER ::= { colubrisDeviceIfMIBConformance 1 }
colubrisDeviceIfMIBGroups      OBJECT IDENTIFIER ::= { colubrisDeviceIfMIBConformance 2 }


-- compliance statements
colubrisDeviceIfMIBCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION "The compliance statement for the device Interface MIB."
    MODULE      MANDATORY-GROUPS
                    {
                        colubrisDeviceIfStatusMIBGroup,
                        colubrisDeviceIfStatsMIBGroup
                    }
    ::= { colubrisDeviceIfMIBCompliances 1 }

-- units of conformance
colubrisDeviceIfStatusMIBGroup OBJECT-GROUP
    OBJECTS     {
                    coDevIfStaFriendlyInterfaceName,
                    coDevIfStaType,
                    coDevIfStaVLAN,
                    coDevIfStaIpAddress,
                    coDevIfStaNetworkMask,
                    coDevIfStaMACAddress,
                    coDevIfStaState
                }
    STATUS      current
    DESCRIPTION "A collection of objects for the device Interface
                 Status group."
    ::= { colubrisDeviceIfMIBGroups 1 }

-- units of conformance
colubrisDeviceIfStatsMIBGroup OBJECT-GROUP
    OBJECTS     {
                    coDevIfStsRxBytes,
                    coDevIfStsRxPackets,
                    coDevIfStsRxErrors,
                    coDevIfStsTxBytes,
                    coDevIfStsTxPackets,
                    coDevIfStsTxErrors
                }
    STATUS      current
    DESCRIPTION "A collection of objects for the device Interface
                 Stats group."
    ::= { colubrisDeviceIfMIBGroups 2 }

END
