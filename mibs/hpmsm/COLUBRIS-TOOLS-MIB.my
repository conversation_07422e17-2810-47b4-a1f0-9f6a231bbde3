-- ****************************************************************************
--  COLUBRIS-TOOLS-MIB definitions
--
--  Copyright (c) 2004, Colubris Networks, Inc.
--  All Rights Reserved.
--
--  Colubris Networks Tools MIB file.
--
-- ****************************************************************************


COLUBRIS-TOOLS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE,
    Unsigned32
        FROM    SNMPv2-SMI
    DisplayString
        FROM    SNMPv2-TC
    MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
        FROM    SNMPv2-CONF
    InterfaceIndex
        FROM    IF-MIB
    colubrisMgmtV2
        FROM    COLUBRIS-SMI
    ColubrisNotificationEnable
        FROM    COLUBRIS-TC
;


colubrisToolsMIB MODULE-IDENTITY
    LAST-UPDATED    "200402200000Z"
    ORGANIZATION    "Colubris Networks, Inc."
    CONTACT-INFO    "Colubris Networks
                     Postal: 200 West Street Ste 300
                             Waltham, Massachusetts 02451-1121
                             UNITED STATES
                     Phone:  ****** 684 0001
                     Fax:    ****** 684 0009

                     E-mail: <EMAIL>"
    DESCRIPTION     "Colubris Networks Tools MIB module."

    ::= { colubrisMgmtV2 12 }


-- colubrisToolsMIBObjects definition
colubrisToolsMIBObjects OBJECT IDENTIFIER ::= { colubrisToolsMIB 1 }

-- IP Trace groups
traceToolConfig OBJECT IDENTIFIER ::=  { colubrisToolsMIBObjects 1 }


-- IP Trace configuration
traceInterface OBJECT-TYPE
    SYNTAX      InterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies the interface to apply the trace to."
    ::= { traceToolConfig 1 }

traceCaptureDestination OBJECT-TYPE
    SYNTAX      INTEGER
                {
                    local(1),
                    remote(2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies if the traces shall be stored locally on the device
                 or remotely on a distant system.

                   'local': Stores the traces locally on the device.

                   'remote': Stores the traces in a remote file specified
                             by traceCaptureDestinationURL."
    ::= { traceToolConfig 2 }

traceCaptureDestinationURL OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies the URL of the file that trace data will be sent to.
                 If a valid URL is not defined, the trace data cannot be sent
                 and will be discarded."
    ::= { traceToolConfig 3 }

traceTimeout OBJECT-TYPE
    SYNTAX      Unsigned32 (0..99999)
    UNITS       "seconds"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies the amount of time the trace will capture data.
                 Once this limit is reached, the trace automatically stops."
    DEFVAL      { 600 }
    ::= { traceToolConfig 4 }

traceNumberOfPackets OBJECT-TYPE
    SYNTAX      Unsigned32 (0..99999)
    UNITS       "packets"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies the maximum number of packets (IP datagrams) the
                 trace should capture. Once this limit is reached, the trace
                 automatically stops."
    DEFVAL      { 100 }
    ::= { traceToolConfig 5 }

tracePacketSize OBJECT-TYPE
    SYNTAX      Unsigned32 (68..4096)
    UNITS       "bytes"
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies the maximum number of bytes to capture for each
                 packet. The remaining data is discarded."
    DEFVAL      { 128 }
    ::= { traceToolConfig 6 }

traceCaptureFilter OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies the packet filter to use to capture data.
                 The filter expression has the same format and behavior
                 as the expression parameter used by the well-known
                 TCPDUMP command."
    ::= { traceToolConfig 7 }

traceCaptureStatus OBJECT-TYPE
    SYNTAX      INTEGER
                {
                    stop(1),
                    start(2)
                }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "IP Trace tool action trigger.

                   'stop': Stops the trace tool from functioning. If any
                           capture was previously started it will end up.
                           if no capture was started, 'stop' has no effect.

                   'start': Starts to capture the packets following the
                            critera specified in the management tool and
                            in this MIB."
    DEFVAL      { stop }
    ::= { traceToolConfig 8 }

traceNotificationEnabled OBJECT-TYPE 
    SYNTAX      ColubrisNotificationEnable
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Specifies if IP trace notifications are generated."
    DEFVAL      { disable }
    ::= { traceToolConfig 9 }


-- IP trace notifications
colubrisToolsMIBNotificationPrefix OBJECT IDENTIFIER ::= { colubrisToolsMIB 2 }
colubrisToolsMIBNotifications OBJECT IDENTIFIER ::= { colubrisToolsMIBNotificationPrefix 0 }

traceStatusNotification NOTIFICATION-TYPE
    OBJECTS     {
                    traceCaptureStatus
                }
    STATUS      current
    DESCRIPTION "Sent when the user triggers the IP Trace tool either by starting
                 a new trace or stopping an existing session."
  --#SUMMARY "IP Trace status trap: %d"
  --#ARGUMENTS { 0 }
  --#SEVERITY INFORMATIONAL
  --#CATEGORY "Colubris Networks Alarms"
    ::= { colubrisToolsMIBNotifications 1 }


-- conformance information
colubrisToolsMIBConformance OBJECT IDENTIFIER ::= { colubrisToolsMIB 3 }
colubrisToolsMIBCompliances OBJECT IDENTIFIER ::= { colubrisToolsMIBConformance 1 }
colubrisToolsMIBGroups      OBJECT IDENTIFIER ::= { colubrisToolsMIBConformance 2 }

-- compliance statements
colubrisToolsMIBCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION "The compliance statement for entities which implement
                 the Colubris Networks Tools MIB."
    MODULE      MANDATORY-GROUPS
                    {
                        colubrisToolsMIBGroup,
                        colubrisToolsNotificationGroup
                    }
    ::= { colubrisToolsMIBCompliances 1 }

-- units of conformance
colubrisToolsMIBGroup OBJECT-GROUP
    OBJECTS     {
		    traceInterface,
                    traceCaptureDestination,
                    traceCaptureDestinationURL,
		    traceTimeout,
		    traceNumberOfPackets,
		    tracePacketSize,
                    traceCaptureFilter,
                    traceCaptureStatus,
                    traceNotificationEnabled
                }
    STATUS      current
    DESCRIPTION "A collection of objects providing the Tools MIB capability."
    ::= { colubrisToolsMIBGroups 1 }

colubrisToolsNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS   {
                        traceStatusNotification
                    }
    STATUS      current
    DESCRIPTION "A collection of supported notifications."
    ::= { colubrisToolsMIBGroups 2 }

END
