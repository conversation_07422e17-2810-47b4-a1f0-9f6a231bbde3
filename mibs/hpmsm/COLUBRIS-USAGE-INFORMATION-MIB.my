-- ****************************************************************************
--  COLUBRIS-USAGE-INFORMATION-MIB definitions
--
--  Copyright (c) 2006, Colubris Networks, Inc.
--  All Rights Reserved.
--
--  Colubris Usage Information MIB file.
--
-- ****************************************************************************


COLUBRIS-USAGE-INFORMATION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Unsigned32, TimeTicks
        FROM    SNMPv2-SMI
    MODULE-COMPLIANCE, OBJECT-GROUP
        FROM    SNMPv2-CONF
    colubrisMgmtV2
        FROM    COLUBRIS-SMI
;


colubrisUsageInformationMIB MODULE-IDENTITY
    LAST-UPDATED    "200605230000Z"
    ORGANIZATION    "Colubris Networks, Inc."
    CONTACT-INFO    "Colubris Networks
                     Postal: 200 West Street Ste 300
                             Waltham, Massachusetts 02451-1121
                             UNITED STATES
                     Phone:  ****** 684 0001
                     Fax:    ****** 684 0009

                     E-mail: <EMAIL>"
    DESCRIPTION     "Colubris Usage Information MIB."

    ::= { colubrisMgmtV2 21 }


-- colubrisUsageInformationMIB definition
colubrisUsageInformationMIBObjects OBJECT IDENTIFIER ::= { colubrisUsageInformationMIB 1 }

-- colubris Usage groups
coUsageInformationGroup OBJECT IDENTIFIER ::= { colubrisUsageInformationMIBObjects 1 }

-- The Usage Information Group
coUsInfoUpTime OBJECT-TYPE
    SYNTAX      TimeTicks
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Elapsed time after the device boot up."
    ::= { coUsageInformationGroup 1 }

coUsInfoLoadAverage1Min OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Average number of processes running during the last minute."
    ::= { coUsageInformationGroup 2 }

coUsInfoLoadAverage5Min OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Average number of processes running during the last 5 minutes."
    ::= { coUsageInformationGroup 3 }

coUsInfoLoadAverage15Min OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Average number of processes running during the last 15 minutes."
    ::= { coUsageInformationGroup 4 }

coUsInfoCpuUseNow OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Current CPU usage."
    ::= { coUsageInformationGroup 5 }

coUsInfoCpuUse5Sec OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Average CPU usage during the last 5 seconds."
    ::= { coUsageInformationGroup 6 }

coUsInfoCpuUse10Sec OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Average CPU usage during the last 10 seconds."
    ::= { coUsageInformationGroup 7 }

coUsInfoCpuUse20Sec OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Average CPU usage during the last 20 seconds."
    ::= { coUsageInformationGroup 8 }

coUsInfoRamTotal OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "Kb"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Total system RAM."
    ::= { coUsageInformationGroup 9 }

coUsInfoRamFree OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "Kb"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Available system RAM."
    ::= { coUsageInformationGroup 10 }

coUsInfoRamBuffer OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "Kb"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Memory used by the buffers."
    ::= { coUsageInformationGroup 11 }

coUsInfoRamCached OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "Kb"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Memory used by the system cache."
    ::= { coUsageInformationGroup 12 }

coUsInfoStorageUsePermanent OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Percentage of permanent storage that is in use."
    ::= { coUsageInformationGroup 13 }

coUsInfoStorageUseTemporary OBJECT-TYPE
    SYNTAX      Unsigned32
    UNITS       "%"
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Percentage of temporary storage that is in use."
    ::= { coUsageInformationGroup 14 }


-- Usage Information notifications
colubrisUsageInformationMIBNotificationPrefix OBJECT IDENTIFIER ::= { colubrisUsageInformationMIB 2 }
colubrisUsageInformationMIBNotifications OBJECT IDENTIFIER ::= { colubrisUsageInformationMIBNotificationPrefix 0 }


-- conformance information
colubrisUsageInformationMIBConformance OBJECT IDENTIFIER ::= { colubrisUsageInformationMIB 3 }
colubrisUsageInformationMIBCompliances OBJECT IDENTIFIER ::= { colubrisUsageInformationMIBConformance 1 }
colubrisUsageInformationMIBGroups      OBJECT IDENTIFIER ::= { colubrisUsageInformationMIBConformance 2 }


-- compliance statements
colubrisUsageInformationMIBCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION "The compliance statement for the Usage Information MIB."
    MODULE      MANDATORY-GROUPS
                    {
                        colubrisUsageInformationMIBGroup
                    }
    ::= { colubrisUsageInformationMIBCompliances 1 }

-- units of conformance
colubrisUsageInformationMIBGroup OBJECT-GROUP
    OBJECTS     {
                    coUsInfoUpTime,
                    coUsInfoLoadAverage1Min,
                    coUsInfoLoadAverage5Min,
                    coUsInfoLoadAverage15Min,
                    coUsInfoCpuUseNow,
                    coUsInfoCpuUse5Sec,
                    coUsInfoCpuUse10Sec,
                    coUsInfoCpuUse20Sec,
                    coUsInfoRamTotal,
                    coUsInfoRamFree,
                    coUsInfoRamBuffer,
                    coUsInfoRamCached,
                    coUsInfoStorageUsePermanent,
                    coUsInfoStorageUseTemporary
                }
    STATUS      current
    DESCRIPTION "A collection of objects for CPU and memory usage."
    ::= { colubrisUsageInformationMIBGroups 1 }

END
