--
-- OAP-NMU.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 4.0 Build 347
-- Thursday, November 26, 2015 at 11:39:48
--

--  MG-SOFT Corporation root SMI
-- 
-- Copyright (C) 1995, 1998 by MG-SOFT Corporation.
-- All rights reserved.
-- 
-- Comments to: <<EMAIL>>
-- Web URL: http://www.mg-soft.si/
-- 

	OAP-NMU DEFINITIONS ::= BEGIN
 
		IMPORTS
			enterprises, IpAddress, OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
		-- *******.4.1.40989
		tryin MODULE-IDENTITY 
			LAST-UPDATED "199811240100Z"		-- November 24, 1998 at 01:00 GMT
			ORGANIZATION 
				"MG-SOFT Corporation"
			CONTACT-INFO 
				"MG-SOFT Corporation
				Strossmayerjeva 32A
				2000 Maribor
				Slovenia
				
				Phone:  +386 62 2295050
				Fax:    +386 62 2295051
				Web:    http://www.mg-soft.si/
				E-Mail: <EMAIL>"
			DESCRIPTION 
				"MG-SOFT Corporation root MIB module."
			REVISION "201505081701Z"		-- May 08, 2015 at 17:01 GMT
			DESCRIPTION 
				"OAP MIB FILE"
			::= { enterprises 40989 }

		
	
--
-- Node definitions
--
	
		-- *******.4.1.40989.10
		device OBJECT IDENTIFIER ::= { tryin 10 }

		
		-- *******.4.1.40989.10.16
		oap OBJECT IDENTIFIER ::= { device 16 }

		
		-- *******.4.1.40989.10.16.20
		nmu OBJECT IDENTIFIER ::= { oap 20 }

		
		-- *******.4.1.40989.**********
		deviceType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 1 }

		
		-- *******.4.1.40989.**********
		ipAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 2 }

		
		-- *******.4.1.40989.**********
		subnetMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 3 }

		
		-- *******.4.1.40989.**********
		gateWay OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 4 }

		
		-- *******.4.1.40989.**********
		macAddress OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 5 }

		
		-- *******.4.1.40989.**********
		keyLock OBJECT-TYPE
			SYNTAX INTEGER
				{
				lock(0),
				unlock(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 6 }

		
		-- *******.4.1.40989.**********
		buzzerSet OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 7 }

		
		-- *******.4.1.40989.**********
		buzzerState OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 8 }

		
		-- *******.4.1.40989.**********
		fanSet OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 9 }

		
		-- *******.4.1.40989.**********0
		fanState OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 10 }

		
		-- *******.4.1.40989.**********1
		power1State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 11 }

		
		-- *******.4.1.40989.**********2
		power2State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 12 }

		
		-- *******.4.1.40989.**********3
		softwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 13 }

		
		-- *******.4.1.40989.**********4
		hardwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 14 }

		
		-- *******.4.1.40989.**********5
		serialNumber OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 15 }

		
		-- *******.4.1.40989.***********
		manufacturingdate OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { nmu 16 }

		
	
	END

--
-- OAP-NMU.my
--
