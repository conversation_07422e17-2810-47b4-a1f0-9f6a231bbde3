--
-- OAP-C1-OEO.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Monday, March 20, 2017 at 13:59:55
--

--  OAP-C1-OEO.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Monday, November 07, 2016 at 10:41:11
-- 
--  MG-SOFT Corporation root SMI
-- 
-- Copyright (C) 1995, 1998 by MG-SOFT Corporation.
-- All rights reserved.
-- 
-- Comments to: <<EMAIL>>
-- Web URL: http://www.mg-soft.si/
-- 

	OAP-C1-OEO DEFINITIONS ::= BEGIN
 
		IMPORTS
			enterprises, Integer32, OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI;
	
	
--  *******.4.1.40989
-- November 24, 1998 at 01:00 GMT
-- May 08, 2015 at 17:01 GMT
		-- *******.4.1.40989
		tryin MODULE-IDENTITY 
			LAST-UPDATED "199811240100Z"		-- November 24, 1998 at 01:00 GMT
			ORGANIZATION 
				"MG-SOFT Corporation"
			CONTACT-INFO 
				"MG-SOFT Corporation
				Strossmayerjeva 32A
				2000 Maribor
				Slovenia
				
				Phone:  +386 62 2295050
				Fax:    +386 62 2295051
				Web:    http://www.mg-soft.si/
				E-Mail: <EMAIL>"
			DESCRIPTION 
				"MG-SOFT Corporation root MIB module."
			REVISION "201505081701Z"		-- May 08, 2015 at 17:01 GMT
			DESCRIPTION 
				"OAP MIB FILE"
			::= { enterprises 40989 }

		
	
--
-- Node definitions
--
	
--  Node definitions
-- 
-- *******.4.1.40989.10
		-- *******.4.1.40989.10
		device OBJECT IDENTIFIER ::= { tryin 10 }

		
--  *******.4.1.40989.10.16
		-- *******.4.1.40989.10.16
		oap OBJECT IDENTIFIER ::= { device 16 }

		
--  *******.4.1.40989.10.16.1
		-- *******.4.1.40989.10.16.1
		card1 OBJECT IDENTIFIER ::= { oap 1 }

		
--  *******.4.1.40989.*********
		-- *******.4.1.40989.*********
		oeo OBJECT IDENTIFIER ::= { card1 2 }

		
--  *******.4.1.40989.*********.1
		-- *******.4.1.40989.*********.1
		vCardState OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oeo 1 }

		
--  *******.4.1.40989.*********.2
		-- *******.4.1.40989.*********.2
		vDeviceType OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oeo 2 }

		
--  *******.4.1.40989.*********.3
		-- *******.4.1.40989.*********.3
		vDeviceDescription OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oeo 3 }

		
--  *******.4.1.40989.*********.4
		-- *******.4.1.40989.*********.4
		vSoftwareVerion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oeo 4 }

		
--  *******.4.1.40989.*********.5
		-- *******.4.1.40989.*********.5
		vHardwareVerion OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oeo 5 }

		
--  *******.4.1.40989.*********.6
		-- *******.4.1.40989.*********.6
		vSerialNumber OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oeo 6 }

		
--  *******.4.1.40989.*********.7
		-- *******.4.1.40989.*********.7
		vFactoryDate OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oeo 7 }

		
--  *******.4.1.40989.*********.11
		-- *******.4.1.40989.*********.11
		vSFPA1 OBJECT IDENTIFIER ::= { oeo 11 }

		
--  *******.4.1.40989.*********.11.1
		-- *******.4.1.40989.*********.11.1
		vSFPA1State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 1 }

		
--  *******.4.1.40989.*********.11.2
		-- *******.4.1.40989.*********.11.2
		vSFPA1WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 2 }

		
--  *******.4.1.40989.*********.11.3
		-- *******.4.1.40989.*********.11.3
		vSFPA1TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 3 }

		
--  *******.4.1.40989.*********.11.4
		-- *******.4.1.40989.*********.11.4
		vSFPA1TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 4 }

		
--  *******.4.1.40989.*********.11.5
		-- *******.4.1.40989.*********.11.5
		vSFPA1RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 5 }

		
--  *******.4.1.40989.*********.11.6
		-- *******.4.1.40989.*********.11.6
		vSFPA1ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 6 }

		
--  *******.4.1.40989.*********.11.7
		-- *******.4.1.40989.*********.11.7
		vSFPA1ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 7 }

		
--  *******.4.1.40989.*********.11.8
		-- *******.4.1.40989.*********.11.8
		vSFPA1ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 8 }

		
--  *******.4.1.40989.*********.11.9
		-- *******.4.1.40989.*********.11.9
		vSFPA1ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 9 }

		
--  *******.4.1.40989.*********.11.10
		-- *******.4.1.40989.*********.11.10
		vSFPA1TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 10 }

		
--  *******.4.1.40989.*********.11.11
		-- *******.4.1.40989.*********.11.11
		vSFPA1RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 11 }

		
--  *******.4.1.40989.*********.11.12
		-- *******.4.1.40989.*********.11.12
		vSFPA1ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 12 }

		
--  *******.4.1.40989.*********.11.13
		-- *******.4.1.40989.*********.11.13
		vSFPA1RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA1 13 }

		
--  *******.4.1.40989.*********.12
		-- *******.4.1.40989.*********.12
		vSFPA2 OBJECT IDENTIFIER ::= { oeo 12 }

		
--  *******.4.1.40989.*********.12.1
		-- *******.4.1.40989.*********.12.1
		vSFPA2State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 1 }

		
--  *******.4.1.40989.*********.12.2
		-- *******.4.1.40989.*********.12.2
		vSFPA2WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 2 }

		
--  *******.4.1.40989.*********.12.3
		-- *******.4.1.40989.*********.12.3
		vSFPA2TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 3 }

		
--  *******.4.1.40989.*********.12.4
		-- *******.4.1.40989.*********.12.4
		vSFPA2TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 4 }

		
--  *******.4.1.40989.*********.12.5
		-- *******.4.1.40989.*********.12.5
		vSFPA2RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 5 }

		
--  *******.4.1.40989.*********.12.6
		-- *******.4.1.40989.*********.12.6
		vSFPA2ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 6 }

		
--  *******.4.1.40989.*********.12.7
		-- *******.4.1.40989.*********.12.7
		vSFPA2ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 7 }

		
--  *******.4.1.40989.*********.12.8
		-- *******.4.1.40989.*********.12.8
		vSFPA2ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 8 }

		
--  *******.4.1.40989.*********.12.9
		-- *******.4.1.40989.*********.12.9
		vSFPA2ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 9 }

		
--  *******.4.1.40989.*********.12.10
		-- *******.4.1.40989.*********.12.10
		vSFPA2TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 10 }

		
--  *******.4.1.40989.*********.12.11
		-- *******.4.1.40989.*********.12.11
		vSFPA2RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 11 }

		
--  *******.4.1.40989.*********.12.12
		-- *******.4.1.40989.*********.12.12
		vSFPA2ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 12 }

		
--  *******.4.1.40989.*********.12.13
		-- *******.4.1.40989.*********.12.13
		vSFPA2RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPA2 13 }

		
--  *******.4.1.40989.*********.13
		-- *******.4.1.40989.*********.13
		vSFPB1 OBJECT IDENTIFIER ::= { oeo 13 }

		
--  *******.4.1.40989.*********.13.1
		-- *******.4.1.40989.*********.13.1
		vSFPB1State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 1 }

		
--  *******.4.1.40989.*********.13.2
		-- *******.4.1.40989.*********.13.2
		vSFPB1WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 2 }

		
--  *******.4.1.40989.*********.13.3
		-- *******.4.1.40989.*********.13.3
		vSFPB1TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 3 }

		
--  *******.4.1.40989.*********.13.4
		-- *******.4.1.40989.*********.13.4
		vSFPB1TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 4 }

		
--  *******.4.1.40989.*********.13.5
		-- *******.4.1.40989.*********.13.5
		vSFPB1RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 5 }

		
--  *******.4.1.40989.*********.13.6
		-- *******.4.1.40989.*********.13.6
		vSFPB1ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 6 }

		
--  *******.4.1.40989.*********.13.7
		-- *******.4.1.40989.*********.13.7
		vSFPB1ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 7 }

		
--  *******.4.1.40989.*********.13.8
		-- *******.4.1.40989.*********.13.8
		vSFPB1ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 8 }

		
--  *******.4.1.40989.*********.13.9
		-- *******.4.1.40989.*********.13.9
		vSFPB1ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 9 }

		
--  *******.4.1.40989.*********.13.10
		-- *******.4.1.40989.*********.13.10
		vSFPB1TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 10 }

		
--  *******.4.1.40989.*********.13.11
		-- *******.4.1.40989.*********.13.11
		vSFPB1RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 11 }

		
--  *******.4.1.40989.*********.13.12
		-- *******.4.1.40989.*********.13.12
		vSFPB1ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 12 }

		
--  *******.4.1.40989.*********.13.13
		-- *******.4.1.40989.*********.13.13
		vSFPB1RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB1 13 }

		
--  *******.4.1.40989.*********.14
		-- *******.4.1.40989.*********.14
		vSFPB2 OBJECT IDENTIFIER ::= { oeo 14 }

		
--  *******.4.1.40989.*********.14.1
		-- *******.4.1.40989.*********.14.1
		vSFPB2State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 1 }

		
--  *******.4.1.40989.*********.14.2
		-- *******.4.1.40989.*********.14.2
		vSFPB2WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 2 }

		
--  *******.4.1.40989.*********.14.3
		-- *******.4.1.40989.*********.14.3
		vSFPB2TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 3 }

		
--  *******.4.1.40989.*********.14.4
		-- *******.4.1.40989.*********.14.4
		vSFPB2TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 4 }

		
--  *******.4.1.40989.*********.14.5
		-- *******.4.1.40989.*********.14.5
		vSFPB2RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 5 }

		
--  *******.4.1.40989.*********.14.6
		-- *******.4.1.40989.*********.14.6
		vSFPB2ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 6 }

		
--  *******.4.1.40989.*********.14.7
		-- *******.4.1.40989.*********.14.7
		vSFPB2ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 7 }

		
--  *******.4.1.40989.*********.14.8
		-- *******.4.1.40989.*********.14.8
		vSFPB2ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 8 }

		
--  *******.4.1.40989.*********.14.9
		-- *******.4.1.40989.*********.14.9
		vSFPB2ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 9 }

		
--  *******.4.1.40989.*********.14.10
		-- *******.4.1.40989.*********.14.10
		vSFPB2TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 10 }

		
--  *******.4.1.40989.*********.14.11
		-- *******.4.1.40989.*********.14.11
		vSFPB2RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 11 }

		
--  *******.4.1.40989.*********.14.12
		-- *******.4.1.40989.*********.14.12
		vSFPB2ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 12 }

		
--  *******.4.1.40989.*********.14.13
		-- *******.4.1.40989.*********.14.13
		vSFPB2RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPB2 13 }

		
--  *******.4.1.40989.*********.15
		-- *******.4.1.40989.*********.15
		vSFPC1 OBJECT IDENTIFIER ::= { oeo 15 }

		
--  *******.4.1.40989.*********.15.1
		-- *******.4.1.40989.*********.15.1
		vSFPC1State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 1 }

		
--  *******.4.1.40989.*********.15.2
		-- *******.4.1.40989.*********.15.2
		vSFPC1WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 2 }

		
--  *******.4.1.40989.*********.15.3
		-- *******.4.1.40989.*********.15.3
		vSFPC1TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 3 }

		
--  *******.4.1.40989.*********.15.4
		-- *******.4.1.40989.*********.15.4
		vSFPC1TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 4 }

		
--  *******.4.1.40989.*********.15.5
		-- *******.4.1.40989.*********.15.5
		vSFPC1RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 5 }

		
--  *******.4.1.40989.*********.15.6
		-- *******.4.1.40989.*********.15.6
		vSFPC1ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 6 }

		
--  *******.4.1.40989.*********.15.7
		-- *******.4.1.40989.*********.15.7
		vSFPC1ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 7 }

		
--  *******.4.1.40989.*********.15.8
		-- *******.4.1.40989.*********.15.8
		vSFPC1ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 8 }

		
--  *******.4.1.40989.*********.15.9
		-- *******.4.1.40989.*********.15.9
		vSFPC1ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 9 }

		
--  *******.4.1.40989.*********.15.10
		-- *******.4.1.40989.*********.15.10
		vSFPC1TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 10 }

		
--  *******.4.1.40989.*********.15.11
		-- *******.4.1.40989.*********.15.11
		vSFPC1RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 11 }

		
--  *******.4.1.40989.*********.15.12
		-- *******.4.1.40989.*********.15.12
		vSFPC1ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 12 }

		
--  *******.4.1.40989.*********.15.13
		-- *******.4.1.40989.*********.15.13
		vSFPC1RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC1 13 }

		
--  *******.4.1.40989.*********.16
		-- *******.4.1.40989.*********.16
		vSFPC2 OBJECT IDENTIFIER ::= { oeo 16 }

		
--  *******.4.1.40989.*********.16.1
		-- *******.4.1.40989.*********.16.1
		vSFPC2State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 1 }

		
--  *******.4.1.40989.*********.16.2
		-- *******.4.1.40989.*********.16.2
		vSFPC2WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 2 }

		
--  *******.4.1.40989.*********.16.3
		-- *******.4.1.40989.*********.16.3
		vSFPC2TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 3 }

		
--  *******.4.1.40989.*********.16.4
		-- *******.4.1.40989.*********.16.4
		vSFPC2TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 4 }

		
--  *******.4.1.40989.*********.16.5
		-- *******.4.1.40989.*********.16.5
		vSFPC2RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 5 }

		
--  *******.4.1.40989.*********.16.6
		-- *******.4.1.40989.*********.16.6
		vSFPC2ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 6 }

		
--  *******.4.1.40989.*********.16.7
		-- *******.4.1.40989.*********.16.7
		vSFPC2ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 7 }

		
--  *******.4.1.40989.*********.16.8
		-- *******.4.1.40989.*********.16.8
		vSFPC2ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 8 }

		
--  *******.4.1.40989.*********.16.9
		-- *******.4.1.40989.*********.16.9
		vSFPC2ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 9 }

		
--  *******.4.1.40989.*********.16.10
		-- *******.4.1.40989.*********.16.10
		vSFPC2TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 10 }

		
--  *******.4.1.40989.*********.16.11
		-- *******.4.1.40989.*********.16.11
		vSFPC2RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 11 }

		
--  *******.4.1.40989.*********.16.12
		-- *******.4.1.40989.*********.16.12
		vSFPC2ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 12 }

		
--  *******.4.1.40989.*********.16.13
		-- *******.4.1.40989.*********.16.13
		vSFPC2RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPC2 13 }

		
--  *******.4.1.40989.*********.17
		-- *******.4.1.40989.*********.17
		vSFPD1 OBJECT IDENTIFIER ::= { oeo 17 }

		
--  *******.4.1.40989.*********.17.1
		-- *******.4.1.40989.*********.17.1
		vSFPD1State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 1 }

		
--  *******.4.1.40989.*********.17.2
		-- *******.4.1.40989.*********.17.2
		vSFPD1WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 2 }

		
--  *******.4.1.40989.*********.17.3
		-- *******.4.1.40989.*********.17.3
		vSFPD1TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 3 }

		
--  *******.4.1.40989.*********.17.4
		-- *******.4.1.40989.*********.17.4
		vSFPD1TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 4 }

		
--  *******.4.1.40989.*********.17.5
		-- *******.4.1.40989.*********.17.5
		vSFPD1RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 5 }

		
--  *******.4.1.40989.*********.17.6
		-- *******.4.1.40989.*********.17.6
		vSFPD1ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 6 }

		
--  *******.4.1.40989.*********.17.7
		-- *******.4.1.40989.*********.17.7
		vSFPD1ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 7 }

		
--  *******.4.1.40989.*********.17.8
		-- *******.4.1.40989.*********.17.8
		vSFPD1ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 8 }

		
--  *******.4.1.40989.*********.17.9
		-- *******.4.1.40989.*********.17.9
		vSFPD1ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 9 }

		
--  *******.4.1.40989.*********.17.10
		-- *******.4.1.40989.*********.17.10
		vSFPD1TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 10 }

		
--  *******.4.1.40989.*********.17.11
		-- *******.4.1.40989.*********.17.11
		vSFPD1RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 11 }

		
--  *******.4.1.40989.*********.17.12
		-- *******.4.1.40989.*********.17.12
		vSFPD1ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 12 }

		
--  *******.4.1.40989.*********.17.13
		-- *******.4.1.40989.*********.17.13
		vSFPD1RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD1 13 }

		
--  *******.4.1.40989.*********.18
		-- *******.4.1.40989.*********.18
		vSFPD2 OBJECT IDENTIFIER ::= { oeo 18 }

		
--  *******.4.1.40989.*********.18.1
		-- *******.4.1.40989.*********.18.1
		vSFPD2State OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 1 }

		
--  *******.4.1.40989.*********.18.2
		-- *******.4.1.40989.*********.18.2
		vSFPD2WorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				loopback(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 2 }

		
--  *******.4.1.40989.*********.18.3
		-- *******.4.1.40989.*********.18.3
		vSFPD2TxPowerControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(0),
				on(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 3 }

		
--  *******.4.1.40989.*********.18.4
		-- *******.4.1.40989.*********.18.4
		vSFPD2TxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 4 }

		
--  *******.4.1.40989.*********.18.5
		-- *******.4.1.40989.*********.18.5
		vSFPD2RxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 5 }

		
--  *******.4.1.40989.*********.18.6
		-- *******.4.1.40989.*********.18.6
		vSFPD2ModeWave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 6 }

		
--  *******.4.1.40989.*********.18.7
		-- *******.4.1.40989.*********.18.7
		vSFPD2ModeTransmissionDistance OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 7 }

		
--  *******.4.1.40989.*********.18.8
		-- *******.4.1.40989.*********.18.8
		vSFPD2ModeTransmissionRate OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 8 }

		
--  *******.4.1.40989.*********.18.9
		-- *******.4.1.40989.*********.18.9
		vSFPD2ModeTemperature OBJECT-TYPE
			SYNTAX Integer32 (-9999..9999)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 9 }

		
--  *******.4.1.40989.*********.18.10
		-- *******.4.1.40989.*********.18.10
		vSFPD2TxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 10 }

		
--  *******.4.1.40989.*********.18.11
		-- *******.4.1.40989.*********.18.11
		vSFPD2RxPowerAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 11 }

		
--  *******.4.1.40989.*********.18.12
		-- *******.4.1.40989.*********.18.12
		vSFPD2ModeTemperatureAlarm OBJECT-TYPE
			SYNTAX INTEGER
				{
				alarm(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 12 }

		
--  *******.4.1.40989.*********.18.13
		-- *******.4.1.40989.*********.18.13
		vSFPD2RxPowerThreshold OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { vSFPD2 13 }

		
	
	END

--
-- OAP-C1-OEO.my
--
