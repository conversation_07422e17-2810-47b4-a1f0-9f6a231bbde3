-- **********************************************************
-- Copyright 2019-2022 VMware, Inc.  All rights reserved.
-- **********************************************************

VMWARE-NSX-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    MODULE-COMPLIANCE, OBJECT-GROUP, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    TEXTUAL-CONVENTION, DateAndTime
        FROM SNMPv2-TC
    vmwNSXsys
        FROM VMWARE-ROOT-MIB;

    vmwNSXsysMIB MODULE-IDENTITY
    LAST-UPDATED "202202140000Z"
    ORGANIZATION "VMware, Inc"
    CONTACT-INFO
        "VMware, Inc
        3401 Hillview Ave
        Palo Alto, CA 94304
        Tel: ************** or ************
        Fax: ************
        Web: http://kb.vmware.com/kb/1013445
        "

    DESCRIPTION
        "In much the same way that server virtualization programmatically
        creates, snapshots, deletes, and restores software-based virtual
        machines (VMs), NSX-T Data Center network virtualization
        programmatically creates, deletes, and restores software-based
        virtual networks.

        With network virtualization, the functional equivalent of a network
        hypervisor reproduces the complete set of Layer 2 through Layer 7
        networking services (for example, switching, routing, access control,
        firewalling, QoS) in software. As a result, these services can be
        programmatically assembled in any arbitrary combination, to produce
        unique, isolated virtual networks in a matter of seconds.

        For more information about NSX-T Data Center, please visit:
        https://docs.vmware.com/en/VMware-NSX-T-Data-Center/
        "

    REVISION "202202140000Z"
    DESCRIPTION
        "This revision adds 174 new notifications for NSX-T Data Center 3.2.0"

    REVISION "202108060000Z"
    DESCRIPTION
        "This revision adds 18 new notifications for NSX-T Data Center 3.1.3"

    REVISION "202104220000Z"
    DESCRIPTION
        "This revision adds 12 new notifications for NSX-T Data Center 3.1.2"

    REVISION "202103250000Z"
    DESCRIPTION
        "This revision adds 2 new notifications for NSX-T Data Center 3.0.3"

    REVISION "202101300000Z"
    DESCRIPTION
        "This revision adds 2 new notifications for NSX-T Data Center 3.1.1"

    REVISION "202010300000Z"
    DESCRIPTION
        "This revision adds 56 new notifications for NSX-T Data Center 3.1.0"

    REVISION "202009220000Z"
    DESCRIPTION
        "This revision adds 10 new notifications for NSX-T Data Center 3.0.2"

    REVISION "202006290000Z"
    DESCRIPTION
        "This revision adds 12 new notifications for NSX-T Data Center 3.0.1"

    REVISION "202003240000Z"
    DESCRIPTION
        "Initial revision of NSX-T Data Center SNMP event notifications."

   ::= { vmwNSXsys 1 }

-- **********************************************************
-- Top-level MIB groups
-- **********************************************************

vmwNsxTDataCenterNotifications OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
    "All event notifications from NSX-T Data Center fall under this oid."
    ::= { vmwNSXsysMIB 0 }

vmwNsxTDataCenterData OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
    "All variables that are sent in NSX-T Data Center event notifications
    fall under this oid."
    ::= { vmwNSXsysMIB 1 }

-- **********************************************************
-- Type definitions
-- **********************************************************

VmwNsxTDataCenterFeatureIdType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The NSX-T Data Center feature ID associated with the event.
        For example, this value identifies the feature is managerHealth,
        edgeHealth, loadBalancer, etc."
    SYNTAX INTEGER {
        managerHealth(1),
        edgeHealth(2),
        certificates(3),
        passwordManagement(4),
        licenses(5),
        intelligenceHealth(6),
        infrastructureCommunication(7),
        intelligenceCommunication(9),
        cniHealth(10),
        ncpHealth(11),
        nodeAgentsHealth(12),
        endpointProtection(13),
        vpn(15),
        alarmManagement(16),
        loadBalancer(17),
        transportNodeHealth(18),
        infrastructureService(19),
        dhcp(20),
        highAvailability(21),
        capacity(22),
        auditLogHealth(24),
        routing(28),
        dns(30),
        distributedFirewall(31),
        federation(32),
        distributedIdsIps(33),
        communication(35),
        identityFirewall(36),
        ipam(38),
        gatewayFirewall(39),
        clustering(40),
        nsxApplicationPlatformCommunication(41),
        mtuCheck(42),
        nsxApplicationPlatformHealth(43),
        edge(45),
        nat(46)
    }

VmwNsxTDataCenterEventTypeType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of the NSX-T Data Center event. The complete set of events
        are reported by the NSX API GET /api/v1/events."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSeverityType ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "The severity of the NSX-T Data Center event. Currently only the
        critical(2), error(3), warning(4) and informational(6) levels
        are used, but the other levels may be used in future releases."
    SYNTAX INTEGER {
        emergency(0),
        alert(1),
        critical(2),
        error(3),
        warning(4),
        notice(5),
        informational(6),
        debug(7)
    }

VmwNsxTDataCenterNodeIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The node ID from where a NSX-T Data Center event was emitted. A
        node ID can be for a cluster node as reported by the NSX API
        GET /api/v1/cluster. Or a node ID can be for a transport node
        as reported by the NSX API GET /api/v1/transport-nodes."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNodeTypeType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The type of node from where a NSX-T Data Center event was emitted."
    SYNTAX INTEGER {
        manager(0),
        edge(1),
        esx(2),
        kvm(3),
        publiccloudgateway(4),
        intelligence(5),
        autonomousedge(6),
        globalmanager(7)
    }

VmwNsxTDataCenterEntityIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The ID of a NSX-T Data Center entity an event pertains to. The event
        description field provides additional details of the entity. For
        example the ID can be for a specific Load Balancer rule as reported by
        the NSX API GET /api/v1/loadbalancer/rules or a specific certificate
        as reported by the NSX API GET /api/v1/trust-management/certificates."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSystemResourceUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The CPU, memory, or disk usage percentage of an NSX-T appliance. The
        value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDiskPartitionNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of a disk partition in an NSX-T appliance. For example, the
        name for the /tmp partition is '/tmp'."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterLicenseEditionTypeType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The edition type of an NSX-T license. For example, the type for an NSX-T
        evaluation license is 'NSX Data Center Evaluation'."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterApplianceAddressType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The NSX appliance's IP address. The value is an IPv4 address."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterCurrentGatewayStateType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The current gateway state. The value could be active/standby/admin down."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterCurrentServiceStateType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The current service state. The value could be start/stop/restart/crashed."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDatapathResourceUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The CPU, memory, or disk usage percentage of the datapath on a NSX-T appliance.
        The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDHCPPoolUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The usage of a DHCP pool. The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgeServiceNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of the service running on the Edge node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFailureReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The detailed reason of any module failure like BGP down, IKE down, etc."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterPreviousGatewayStateType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The previous gateway state. The value could be active/standby/admin down."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterPreviousServiceStateType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The previous service state. The value could be start/stop/restart/crashed."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSystemUsageThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The configured threshold value for the CPU, memory, or disk usage
        percentage of an NSX-T appliance. The value is an integer between 0 and
        100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterUsernameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of a user in an NSX-T appliance."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDHCPServerIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The UUID of a DHCP server."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterServiceNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of the service running in an NSX-T appliance."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterIntelligenceNodeIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The node ID of an NSX-T Intelligence node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterHostnameOrIPAddressWithPortType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Hostname or ip address with port of logging server."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEventIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The event identifier."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterActiveGlobalManagerType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The identifier for the active Global Manager node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterActiveGlobalManagersType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "A comma delimited list of identifiers for the active Global Manager nodes."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSessionDownReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Down reason of the IPsec VPN session."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterManagerNodeNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The display name of a NSX-T Manager node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTransportNodeAddressType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The IP address of a NSX-T Transport node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTransportNodeNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The display name of a NSX-T Transport node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterCentralControlPlaneIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The UUID of a NSX centrol control plane."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTunnelDownReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Down reason of the IPsec VPN tunnel."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterHeapTypeType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Heap type information for DFW memory usage like vsip-attr, vsip-flow, vsip-fqdni, etc."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterMempoolNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Name of a memory pool."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterPasswordExpirationDaysType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The number of days left before expiration of the password of an NSX-T
        appliance user. The value is an integer greater than 0."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterBGPNeighborIPType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "IP address of BGP Neighbor. This could be either IPv4 or IPv6 address."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterLDAPServerType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name or IP address of a LDAP server."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterPeerAddressType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "IP address of BFD peer."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterMaxIDSEventsAllowedType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The maximum number of IDS events that are allowed in the system, anything beyond this
        would be purged at regular intervals."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterStaticAddressType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "A Static IP address."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDuplicateIPAddressType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Duplicate IP address."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterCapacityDisplayNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Display name of type on capacity dashboard."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterCapacityUsageCountType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Current usage count of capacity usage type."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgeNICNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of the NIC whose link is down or which faced tx/rx ring buffer overflow."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterRxRingBufferOverflowPercentageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The NIC's receive descriptor ring buffer on a NSX-T Edge or Gateway node has no space left
        to accomodate new incoming packets and there is a significant drop due to it.
        The value is a float between 0 to 100 describing the overflow percentage due to the packet drops."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTxRingBufferOverflowPercentageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The NIC's transmit descriptor ring buffer on a NSX-T Edge or Gateway node has no space left
        to accomodate new outgoing packets and there is a significant drop due to it.
        The value is a float between 0 to 100 describing the overflow percentage due to the packet drops."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSrIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Service Router UUID."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterIDSEventsCountType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Count of the IDS events in the system currently."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterRemoteSiteNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Name of a remote site in federation. For more information on remote
        sites and an overview of the federation feature, please refer to:
        https://docs.vmware.com/en/VMware-NSX-T-Data-Center/3.0/administration/GUID-D5B6DC79-6733-44A7-8072-50221CF2122A.html"
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterBGPSourceIPType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Source IP address of BGP session. This could be either an IPv4 address
        in dotted-decimal format or an IPv6 address in IPv6 address format."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterRemoteSiteIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The identifier for a remote site."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSiteIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The identifier for a local site."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSiteNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Name of a local site in federation. For more information on local
        sites and an overview of the federation feature, please refer to:
        https://docs.vmware.com/en/VMware-NSX-T-Data-Center/3.0/administration/GUID-D5B6DC79-6733-44A7-8072-50221CF2122A.html"
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterLrIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Logical Router UUID."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterRxMissesType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The number of packets missed while receiving due to rx ring buffer being full."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterRxProcessedType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The number of successful incoming packets while polling for rx buffer full event."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTxMissesType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The number of packets missed while transmitting due to tx ring buffer being full."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTxProcessedType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The number of successful outgoing packets while polling for tx buffer full event."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterLrportIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Logical Router Port UUID."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterServiceIPType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The IP configured for the service running in an NSX-T appliance in dotted-decimal format."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterRemoteManagerNodeIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The remote Manager node UUID."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDirectoryDomainType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of a directory domain."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTimeoutInMinutesType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Timeout value in minutes."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterMaxCapacityThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Maximum threshold defined for capacity usage type."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterMinCapacityThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Minimum threshold defined for capacity usage type."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterMaxSupportedCapacityCountType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Maximum supported count for capacity usage type."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterLatencySourceType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The source of reported latency."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterLatencyThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Threshold value of reported IO latency."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterLatencyValueType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Value of reported IO latency."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterApplianceFQDNType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The NSX appliance's fully qualified domain name."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterRemoteApplianceAddressType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The remote NSX appliance's IP address. The value is an IPv4 address."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterManagerNodeIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The Manager node UUID."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDisplayedLicenseKeyType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The last 5 characters of the license key of an NSX-T license."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgeThreadNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of the Edge node thread."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterIntentPathType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The policy intent path."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFirewallHalfopenFlowUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Current firewall TCP half-open connection usage percentage of max supported value.
        The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFirewallICMPFlowUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Current firewall ICMP connection usage percentage of max supported value.
        The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterServiceDownReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Reason service is down."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFirewallUDPFlowUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Current firewall UDP connection usage percentage of max supported value.
        The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFirewallIPFlowUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Current firewall IP connection usage percentage of max supported value.
        The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDNSIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The DNS forwarder identifier."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterDNSUpstreamIPType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "IP address of one DNS forwarder upstream server."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterCABundleAgeThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The age threshold in days for the CA bundle."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterAPICollectionPathType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The API path of the collection containing entity_id."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgeNodeSettingMismatchReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "String containing Policy/MP intent of Edge transport node settings
        that differs from corresponding Edge transport node settings on Edge NSX CLI."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgeVMvSphereSettingsMismatchReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "String containing Policy/MP intent of Edge transport node vSphere
        deployment configuration that differs from corresponding Edge transport node
        configuration on vSphere."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFirewallSNATPortsUsageType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Current firewall SNAT ports usage percentage of max supported value.
        The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgevSphereLocationMismatchReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "String containing Policy/MP intent of vSphere deployment configuration that differs
        from corresponding Edge transport node configuration on vSphere when vMotion happens
        for Edge node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgeNodeAndvSphereSettingsMismatchReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "String containing Policy/MP intent of Edge transport node settings and vSphere
        deployment configuration that differs from corresponding Edge transport node
        settings on Edge NSX CLI and Edge transport node configuration on vSphere."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSNATIPAddressType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The SNAT IP address which ran out of port threshold."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNappClusterIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The cluster ID of the NSX Application Platform cluster."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNappMessagingLAGThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The configured threshold value for the number of pending messages
        in an NSX Application Platform messaging topic."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNappNodeIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The node ID of an NSX Application Platform node."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNappServiceNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The service name of an NSX Application Platform service."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFlowIdentifierType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "A flow identifier indicating the direction of flow of data which is one of following.
        - GM_2_LM
        - LM_2_GM_NOTIFICATION
        - LM_2_GM_ONBOARD_CONFIG"
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterSyncIssueReasonType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "A reason for data synchronization issue."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterQueueNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The name of the queue used to synchronize data between two sites."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterQueueSizeType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The occupancy size of queue used to synchronize data between two sites."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterQueueSizeThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Maximum threshold value of reported queue size."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterGroupTypeType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The group type of a GMLE cluster."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterManagerNodeIDSType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "A comma delimited list of identifiers for the Manager nodes."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterServiceRouterIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The ID of a service-router that is part of a tier0 or tier1 gateway
        spanning one or more edges"
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterTransportNodeIdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The Transport node UUID."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterFromGMPathType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Policy path of the Global Manager."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterToGMPathType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Policy path of the Global Manager."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNICThroughputType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The NIC throughput, as a percentage. The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNICThroughputThresholdType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The NIC throughput threshold, as a percentage. The value is an integer between 0 and 100."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterEdgeCryptoDrvNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "Name of the edge crypto driver."
    SYNTAX OCTET STRING (SIZE (0..256))

VmwNsxTDataCenterNappNodeNameType ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "256a"
    STATUS       current
    DESCRIPTION
        "The node name of an NSX Application Platform node."
    SYNTAX OCTET STRING (SIZE (0..256))

vmwNsxTDataCenterTimestamp OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The timestamp when the NSX-T Data Center event occurred."
    ::= { vmwNsxTDataCenterData 1 }

vmwNsxTDataCenterFeatureName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFeatureIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The NSX-T Data Center feature ID associated with the event."
    ::= { vmwNsxTDataCenterData 2 }

vmwNsxTDataCenterEventType OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEventTypeType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the NSX-T Data Center event."
    ::= { vmwNsxTDataCenterData 3 }

vmwNsxTDataCenterEventSeverity OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSeverityType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The severity of the NSX-T Data Center event."
    ::= { vmwNsxTDataCenterData 4 }

vmwNsxTDataCenterNodeId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNodeIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The node ID from where a NSX-T Data Center event was emitted."
    ::= { vmwNsxTDataCenterData 5 }

vmwNsxTDataCenterNodeType OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNodeTypeType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of node from where a NSX-T Data Center event was emitted."
    ::= { vmwNsxTDataCenterData 6 }

vmwNsxTDataCenterEntityId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEntityIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The ID of a NSX-T Data Center entity an event pertains to. The event
        description field provides additional details of the entity. For
        example the ID can be for a specific Load Balancer rule as reported by
        the NSX API GET /api/v1/loadbalancer/rules or a specific certificate
        as reported by the NSX API GET /api/v1/trust-management/certificates."
    ::= { vmwNsxTDataCenterData 21 }

vmwNsxTDataCenterSystemResourceUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSystemResourceUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The CPU, memory, or disk usage percentage of an NSX-T appliance. The
        value is an integer between 0 and 100."
    ::= { vmwNsxTDataCenterData 22 }

vmwNsxTDataCenterDiskPartitionName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDiskPartitionNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of a disk partition in an NSX-T appliance. For example, the
        name for the /tmp partition is '/tmp'."
    ::= { vmwNsxTDataCenterData 23 }

vmwNsxTDataCenterLicenseEditionType OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterLicenseEditionTypeType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The edition type of an NSX-T license. For example, the type for an NSX-T
        evaluation license is 'NSX Data Center Evaluation'."
    ::= { vmwNsxTDataCenterData 24 }

vmwNsxTDataCenterApplianceAddress OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterApplianceAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The NSX appliance's IP address. The value is an IPv4 address."
    ::= { vmwNsxTDataCenterData 25 }

vmwNsxTDataCenterCurrentGatewayState OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterCurrentGatewayStateType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The current gateway state. The value could be active/standby/admin down."
    ::= { vmwNsxTDataCenterData 26 }

vmwNsxTDataCenterCurrentServiceState OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterCurrentServiceStateType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The current service state. The value could be start/stop/restart/crashed."
    ::= { vmwNsxTDataCenterData 27 }

vmwNsxTDataCenterDatapathResourceUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDatapathResourceUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The CPU, memory, or disk usage percentage of the datapath on a NSX-T appliance.
        The value is an integer between 0 and 100."
    ::= { vmwNsxTDataCenterData 28 }

vmwNsxTDataCenterDHCPPoolUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDHCPPoolUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The usage of a DHCP pool. The value is an integer between 0 and 100."
    ::= { vmwNsxTDataCenterData 29 }

vmwNsxTDataCenterEdgeServiceName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgeServiceNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the service running on the Edge node."
    ::= { vmwNsxTDataCenterData 30 }

vmwNsxTDataCenterFailureReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFailureReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The detailed reason of any module failure like BGP down, IKE down, etc."
    ::= { vmwNsxTDataCenterData 31 }

vmwNsxTDataCenterPreviousGatewayState OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterPreviousGatewayStateType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The previous gateway state. The value could be active/standby/admin down."
    ::= { vmwNsxTDataCenterData 32 }

vmwNsxTDataCenterPreviousServiceState OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterPreviousServiceStateType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The previous service state. The value could be start/stop/restart/crashed."
    ::= { vmwNsxTDataCenterData 33 }

vmwNsxTDataCenterSystemUsageThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSystemUsageThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The configured threshold value for the CPU, memory, or disk usage
        percentage of an NSX-T appliance. The value is an integer between 0 and
        100."
    ::= { vmwNsxTDataCenterData 34 }

vmwNsxTDataCenterUsername OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterUsernameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of a user in an NSX-T appliance."
    ::= { vmwNsxTDataCenterData 35 }

vmwNsxTDataCenterDHCPServerId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDHCPServerIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The UUID of a DHCP server."
    ::= { vmwNsxTDataCenterData 36 }

vmwNsxTDataCenterServiceName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterServiceNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the service running in an NSX-T appliance."
    ::= { vmwNsxTDataCenterData 37 }

vmwNsxTDataCenterIntelligenceNodeId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterIntelligenceNodeIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The node ID of an NSX-T Intelligence node."
    ::= { vmwNsxTDataCenterData 38 }

vmwNsxTDataCenterHostnameOrIPAddressWithPort OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterHostnameOrIPAddressWithPortType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 39 }

vmwNsxTDataCenterEventId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEventIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The event identifier."
    ::= { vmwNsxTDataCenterData 40 }

vmwNsxTDataCenterActiveGlobalManager OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterActiveGlobalManagerType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 41 }

vmwNsxTDataCenterActiveGlobalManagers OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterActiveGlobalManagersType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 42 }

vmwNsxTDataCenterSessionDownReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSessionDownReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Down reason of the IPsec VPN session."
    ::= { vmwNsxTDataCenterData 43 }

vmwNsxTDataCenterManagerNodeName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterManagerNodeNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 44 }

vmwNsxTDataCenterTransportNodeAddress OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTransportNodeAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 45 }

vmwNsxTDataCenterTransportNodeName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTransportNodeNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 46 }

vmwNsxTDataCenterCentralControlPlaneId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterCentralControlPlaneIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 47 }

vmwNsxTDataCenterTunnelDownReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTunnelDownReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Down reason of the IPsec VPN tunnel."
    ::= { vmwNsxTDataCenterData 48 }

vmwNsxTDataCenterHeapType OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterHeapTypeType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Heap type information for DFW memory usage like vsip-attr, vsip-flow, vsip-fqdni, etc."
    ::= { vmwNsxTDataCenterData 49 }

vmwNsxTDataCenterMempoolName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterMempoolNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of a memory pool."
    ::= { vmwNsxTDataCenterData 50 }

vmwNsxTDataCenterPasswordExpirationDays OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterPasswordExpirationDaysType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The number of days left before expiration of the password of an NSX-T
        appliance user. The value is an integer greater than 0."
    ::= { vmwNsxTDataCenterData 51 }

vmwNsxTDataCenterBGPNeighborIP OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterBGPNeighborIPType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IP address of BGP Neighbor. This could be either IPv4 or IPv6 address."
    ::= { vmwNsxTDataCenterData 52 }

vmwNsxTDataCenterLDAPServer OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterLDAPServerType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 53 }

vmwNsxTDataCenterPeerAddress OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterPeerAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IP address of BFD peer."
    ::= { vmwNsxTDataCenterData 54 }

vmwNsxTDataCenterMaxIDSEventsAllowed OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterMaxIDSEventsAllowedType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 55 }

vmwNsxTDataCenterStaticAddress OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterStaticAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "A Static IP address."
    ::= { vmwNsxTDataCenterData 56 }

vmwNsxTDataCenterDuplicateIPAddress OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDuplicateIPAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Duplicate IP address."
    ::= { vmwNsxTDataCenterData 57 }

vmwNsxTDataCenterCapacityDisplayName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterCapacityDisplayNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 58 }

vmwNsxTDataCenterCapacityUsageCount OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterCapacityUsageCountType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 59 }

vmwNsxTDataCenterEdgeNICName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgeNICNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the NIC whose link is down or which faced tx/rx ring buffer overflow."
    ::= { vmwNsxTDataCenterData 60 }

vmwNsxTDataCenterRxRingBufferOverflowPercentage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterRxRingBufferOverflowPercentageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The NIC's receive descriptor ring buffer on a NSX-T Edge or Gateway node has no space left
        to accomodate new incoming packets and there is a significant drop due to it.
        The value is a float between 0 to 100 describing the overflow percentage due to the packet drops."
    ::= { vmwNsxTDataCenterData 61 }

vmwNsxTDataCenterTxRingBufferOverflowPercentage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTxRingBufferOverflowPercentageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The NIC's transmit descriptor ring buffer on a NSX-T Edge or Gateway node has no space left
        to accomodate new outgoing packets and there is a significant drop due to it.
        The value is a float between 0 to 100 describing the overflow percentage due to the packet drops."
    ::= { vmwNsxTDataCenterData 62 }

vmwNsxTDataCenterSrId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSrIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Service Router UUID."
    ::= { vmwNsxTDataCenterData 63 }

vmwNsxTDataCenterIDSEventsCount OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterIDSEventsCountType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 64 }

vmwNsxTDataCenterRemoteSiteName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterRemoteSiteNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 65 }

vmwNsxTDataCenterBGPSourceIP OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterBGPSourceIPType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 66 }

vmwNsxTDataCenterRemoteSiteId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterRemoteSiteIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 67 }

vmwNsxTDataCenterSiteId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSiteIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 68 }

vmwNsxTDataCenterSiteName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSiteNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 69 }

vmwNsxTDataCenterLrId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterLrIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 70 }

vmwNsxTDataCenterRxMisses OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterRxMissesType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 71 }

vmwNsxTDataCenterRxProcessed OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterRxProcessedType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 72 }

vmwNsxTDataCenterTxMisses OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTxMissesType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 73 }

vmwNsxTDataCenterTxProcessed OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTxProcessedType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 74 }

vmwNsxTDataCenterLrportId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterLrportIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical Router Port UUID."
    ::= { vmwNsxTDataCenterData 75 }

vmwNsxTDataCenterServiceIP OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterServiceIPType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The Ip configured for the service running in an NSX-T appliance."
    ::= { vmwNsxTDataCenterData 77 }

vmwNsxTDataCenterRemoteManagerNodeId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterRemoteManagerNodeIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 80 }

vmwNsxTDataCenterDirectoryDomain OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDirectoryDomainType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 81 }

vmwNsxTDataCenterTimeoutInMinutes OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTimeoutInMinutesType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 82 }

vmwNsxTDataCenterMaxCapacityThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterMaxCapacityThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 83 }

vmwNsxTDataCenterMinCapacityThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterMinCapacityThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 84 }

vmwNsxTDataCenterMaxSupportedCapacityCount OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterMaxSupportedCapacityCountType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 85 }

vmwNsxTDataCenterLatencySource OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterLatencySourceType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 86 }

vmwNsxTDataCenterLatencyThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterLatencyThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 87 }

vmwNsxTDataCenterLatencyValue OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterLatencyValueType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 88 }

vmwNsxTDataCenterApplianceFQDN OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterApplianceFQDNType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 89 }

vmwNsxTDataCenterRemoteApplianceAddress OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterRemoteApplianceAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 90 }

vmwNsxTDataCenterManagerNodeId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterManagerNodeIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 91 }

vmwNsxTDataCenterDisplayedLicenseKey OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDisplayedLicenseKeyType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 92 }

vmwNsxTDataCenterEdgeThreadName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgeThreadNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 93 }

vmwNsxTDataCenterIntentPath OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterIntentPathType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 94 }

vmwNsxTDataCenterFirewallHalfopenFlowUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFirewallHalfopenFlowUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Firewall TCP half-open connection usage."
    ::= { vmwNsxTDataCenterData 95 }

vmwNsxTDataCenterFirewallICMPFlowUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFirewallICMPFlowUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Firewall ICMP connection usage."
    ::= { vmwNsxTDataCenterData 96 }

vmwNsxTDataCenterServiceDownReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterServiceDownReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 97 }

vmwNsxTDataCenterFirewallUDPFlowUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFirewallUDPFlowUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Firewall UDP connection usage."
    ::= { vmwNsxTDataCenterData 98 }

vmwNsxTDataCenterFirewallIPFlowUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFirewallIPFlowUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Firewall IP connection usage."
    ::= { vmwNsxTDataCenterData 99 }

vmwNsxTDataCenterDNSId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDNSIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DNS forwarder identifier."
    ::= { vmwNsxTDataCenterData 100 }

vmwNsxTDataCenterDNSUpstreamIP OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterDNSUpstreamIPType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DNS forwarder upstream server IP address."
    ::= { vmwNsxTDataCenterData 101 }

vmwNsxTDataCenterCABundleAgeThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterCABundleAgeThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 121 }

vmwNsxTDataCenterAPICollectionPath OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterAPICollectionPathType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 122 }

vmwNsxTDataCenterEdgeNodeSettingMismatchReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgeNodeSettingMismatchReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 123 }

vmwNsxTDataCenterEdgeVMvSphereSettingsMismatchReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgeVMvSphereSettingsMismatchReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 124 }

vmwNsxTDataCenterFirewallSNATPortsUsage OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFirewallSNATPortsUsageType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 125 }

vmwNsxTDataCenterEdgevSphereLocationMismatchReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgevSphereLocationMismatchReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 126 }

vmwNsxTDataCenterEdgeNodeAndvSphereSettingsMismatchReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgeNodeAndvSphereSettingsMismatchReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 127 }

vmwNsxTDataCenterSNATIPAddress OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSNATIPAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 128 }

vmwNsxTDataCenterNappClusterId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNappClusterIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 129 }

vmwNsxTDataCenterNappMessagingLAGThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNappMessagingLAGThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 130 }

vmwNsxTDataCenterNappNodeId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNappNodeIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 131 }

vmwNsxTDataCenterNappServiceName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNappServiceNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 132 }

vmwNsxTDataCenterFlowIdentifier OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFlowIdentifierType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 133 }

vmwNsxTDataCenterSyncIssueReason OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterSyncIssueReasonType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 134 }

vmwNsxTDataCenterQueueName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterQueueNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 135 }

vmwNsxTDataCenterQueueSize OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterQueueSizeType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 136 }

vmwNsxTDataCenterQueueSizeThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterQueueSizeThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 137 }

vmwNsxTDataCenterGroupType OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterGroupTypeType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 138 }

vmwNsxTDataCenterManagerNodeIDS OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterManagerNodeIDSType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 139 }

vmwNsxTDataCenterServiceRouterId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterServiceRouterIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 140 }

vmwNsxTDataCenterTransportNodeId OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterTransportNodeIdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 141 }

vmwNsxTDataCenterFromGMPath OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterFromGMPathType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 142 }

vmwNsxTDataCenterToGMPath OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterToGMPathType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 143 }

vmwNsxTDataCenterNICThroughput OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNICThroughputType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 144 }

vmwNsxTDataCenterNICThroughputThreshold OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNICThroughputThresholdType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 145 }

vmwNsxTDataCenterEdgeCryptoDrvName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterEdgeCryptoDrvNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 146 }

vmwNsxTDataCenterNappNodeName OBJECT-TYPE
    SYNTAX      VmwNsxTDataCenterNappNodeNameType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This value is not expected to be empty."
    ::= { vmwNsxTDataCenterData 147 }

-- **********************************************************
-- AlarmManagement feature event notifications
-- **********************************************************

vmwNsxTAlarmManagementFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the AlarmManagement feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 16 }

vmwNsxTAlarmManagementFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for AlarmManagement feature notifications.
        "
    ::= { vmwNsxTAlarmManagementFeaturePrefix 0 }

vmwNsxTAlarmManagementAlarmServiceOverloaded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Due to heavy volume of alarms reported, the alarm service is temporarily
        overloaded. The NSX UI and GET /api/v1/alarms NSX API have stopped
        reporting new alarms; however, syslog entries and SNMP traps (if enabled)
        are still being emitted reporting the underlying event details. When the
        underlying issues causing the heavy volume of alarms are addressed, the
        alarm service will start reporting new alarms again.

        Action required:
        Review all active alarms using the Alarms page in the NSX UI or using the
        GET /api/v1/alarms?status=OPEN,ACKNOWLEDGED,SUPPRESSED NSX API. For each
        active alarm investigate the root cause by following the recommended action
        for the alarm. When sufficient alarms are resolved, the alarm service will
        start reporting new alarms again.
        "
    ::= { vmwNsxTAlarmManagementFeature 1 }

vmwNsxTAlarmManagementAlarmServiceOverloadedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The heavy volume of alarms has subsided and new alarms are being reported
        again.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTAlarmManagementFeature 2 }

vmwNsxTAlarmManagementHeavyVolumeOfAlarms NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEventId
    }
    STATUS current
    DESCRIPTION
        "Due to heavy volume of vmwNsxTDataCenterEventId alarms, the alarm service has
        temporarily stopped reporting alarms of this type. The NSX UI and
        GET /api/v1/alarms NSX API are not reporting new instances of these
        alarms; however, syslog entries and SNMP traps (if enabled) are
        still being emitted reporting the underlying event details. When the
        underlying issues causing the heavy volume of vmwNsxTDataCenterEventId alarms are
        addressed, the alarm service will start reporting new vmwNsxTDataCenterEventId
        alarms when new issues are detected again.

        Action required:
        Review all active alarms of type vmwNsxTDataCenterEventId using the
        Alarms page in the NSX UI or using the NSX API GET
        /api/v1/alarms?status=OPEN,ACKNOWLEDGED,SUPPRESSED. For each active
        alarm investigate the root cause by following the recommended action
        for the alarm. When sufficient alarms are resolved, the alarm service
        will start reporting new vmwNsxTDataCenterEventId alarms again.
        "
    ::= { vmwNsxTAlarmManagementFeature 3 }

vmwNsxTAlarmManagementHeavyVolumeOfAlarmsClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEventId
    }
    STATUS current
    DESCRIPTION
        "The heavy volume of vmwNsxTDataCenterEventId alarms has subsided and new alarms of
        this type are being reported again.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTAlarmManagementFeature 4 }

-- **********************************************************
-- AuditLogHealth feature event notifications
-- **********************************************************

vmwNsxTAuditLogHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the AuditLogHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 24 }

vmwNsxTAuditLogHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for AuditLogHealth feature notifications.
        "
    ::= { vmwNsxTAuditLogHealthFeaturePrefix 0 }

vmwNsxTAuditLogHealthAuditLogFileUpdateError NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "At least one of the monitored log files has read-only permissions or has
        incorrect user/group ownership or rsyslog.log is missing on Manager,
        Global Manager, Edge or Public Cloud Gateway nodes.

        Action required:
        1. On all NSX appliances, for example Manager nodes, Edge nodes, etc.,
        ensure the permissions for the /var/log directory is 775 and the ownership
        is root:syslog.
        2. On Manager and Global Manager nodes, ensure the file permissions
        for auth.log, nsx-audit.log, nsx-audit-write.log, rsyslog.log and syslog
        under /var/log is 640 and ownership is syslog:admin.
        3. On Edge and Public Cloud Gateway nodes, ensure the file permissions
        for rsyslog.log and syslog under /var/log is 640 and ownership is syslog:admin.
        4. On ESXi host nodes, ensure the file permissions of auth.log, nsx-syslog.log
        and syslog.log under /var/log is 755 and ownership is root:root.
        5. On KVM host nodes, ensure the file permissions of auth.log and syslog
        under /var/log is 775 and ownership is root:syslog.
        6. If any of these files have incorrect permissions or ownership, invoke the
        commands `chmod <mode> <path>` and `chown <user>:<group> <path>`.
        7. If rsyslog.log is missing on Manager, Global Manager, Edge or Public
        Cloud Gateway nodes, invoke the NSX CLI command `restart service syslog`
        which restarts the logging service and regenerates /var/log/rsyslog.log.
        "
    ::= { vmwNsxTAuditLogHealthFeature 1 }

vmwNsxTAuditLogHealthAuditLogFileUpdateErrorClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "All monitored log files have the correct file permissions and ownership
        and rsyslog.log exists on Manager, Global Manager, Edge or Public
        Cloud Gateway nodes.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTAuditLogHealthFeature 2 }

vmwNsxTAuditLogHealthRemoteLoggingServerError NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterHostnameOrIPAddressWithPort
    }
    STATUS current
    DESCRIPTION
        "Log messages to logging server vmwNsxTDataCenterHostnameOrIPAddressWithPort (vmwNsxTDataCenterEntityId)
        cannot be delivered possibly due to an unresolvable FQDN, an invalid TLS
        certificate or missing NSX appliance iptables rule.

        Action required:
        1. Ensure that vmwNsxTDataCenterHostnameOrIPAddressWithPort is the correct hostname or
        IP address and port.
        2. If the logging server is specified using a FQDN, ensure the FQDN is resolvable
        from the NSX appliance using the NSX CLI command `nslookup <fqdn>`. If not
        resolvable, verify the correct FQDN is specified and the network DNS server has
        the required entry for the FQDN.
        3. If the logging server is configured to use TLS, verify the specified certificate
        is valid. For example, ensure the logging server is actually using the certificate
        or verify the certificate has not expired using the openssl command
        `openssl x509 -in <cert-file-path> -noout -dates`.
        4. NSX appliances use iptables rules to explicitly allow outgoing traffic. Verify
        the iptables rule for the logging server is configured properly by invoking the
        NSX CLI command `verify logging-servers` which re-configures logging server
        iptables rules as needed.
        5. If for any reason the logging server is misconfigured, it should be deleted
        using the NSX CLI `del logging-server <hostname-or-ip-address[:port]>
        proto <proto> level <level>` command and re-added with the correct configuration.
        "
    ::= { vmwNsxTAuditLogHealthFeature 3 }

vmwNsxTAuditLogHealthRemoteLoggingServerErrorClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterHostnameOrIPAddressWithPort
    }
    STATUS current
    DESCRIPTION
        "Configuration for logging server vmwNsxTDataCenterHostnameOrIPAddressWithPort (vmwNsxTDataCenterEntityId)
        appear correct.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTAuditLogHealthFeature 4 }

-- **********************************************************
-- Capacity feature event notifications
-- **********************************************************

vmwNsxTCapacityFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Capacity feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 22 }

vmwNsxTCapacityFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Capacity feature notifications.
        "
    ::= { vmwNsxTCapacityFeaturePrefix 0 }

vmwNsxTCapacityMaximumCapacity NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCapacityDisplayName,
        vmwNsxTDataCenterCapacityUsageCount,
        vmwNsxTDataCenterMaxSupportedCapacityCount
    }
    STATUS current
    DESCRIPTION
        "The number of objects defined in the system for vmwNsxTDataCenterCapacityDisplayName has
        reached vmwNsxTDataCenterCapacityUsageCount which is above the maximum supported
        count of vmwNsxTDataCenterMaxSupportedCapacityCount.

        Action required:
        Ensure that the number of NSX objects created is within the limits
        supported by NSX. If there are any unused objects, delete them using the
        respective NSX UI or API from the system.
        Consider increasing the form factor of all Manager nodes and/or Edge
        nodes. Note that the form factor of each node type should be the
        same. If not the same, the capacity limits for the lowest form factor
        deployed are used.
        "
    ::= { vmwNsxTCapacityFeature 1 }

vmwNsxTCapacityMaximumCapacityClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCapacityDisplayName,
        vmwNsxTDataCenterCapacityUsageCount,
        vmwNsxTDataCenterMaxSupportedCapacityCount
    }
    STATUS current
    DESCRIPTION
        "The number of objects defined in the system for vmwNsxTDataCenterCapacityDisplayName has
        reached vmwNsxTDataCenterCapacityUsageCount and is at or below the maximum supported count of
        vmwNsxTDataCenterMaxSupportedCapacityCount.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCapacityFeature 2 }

vmwNsxTCapacityMaximumCapacityThreshold NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCapacityDisplayName,
        vmwNsxTDataCenterCapacityUsageCount,
        vmwNsxTDataCenterMaxCapacityThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of objects defined in the system for vmwNsxTDataCenterCapacityDisplayName has
        reached vmwNsxTDataCenterCapacityUsageCount which is above the maximum capacity
        threshold of vmwNsxTDataCenterMaxCapacityThreshold%.

        Action required:
        Navigate to the capacity page in the NSX UI and review current usage versus
        threshold limits. If the current usage is expected, consider increasing the
        maximum threshold values. If the current usage is unexpected, review the
        network policies configured to decrease usage at or below the maximum threshold.
        "
    ::= { vmwNsxTCapacityFeature 3 }

vmwNsxTCapacityMaximumCapacityThresholdClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCapacityDisplayName,
        vmwNsxTDataCenterCapacityUsageCount,
        vmwNsxTDataCenterMaxCapacityThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of objects defined in the system for vmwNsxTDataCenterCapacityDisplayName has
        reached vmwNsxTDataCenterCapacityUsageCount and is at or below the maximum capacity threshold
        of vmwNsxTDataCenterMaxCapacityThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCapacityFeature 4 }

vmwNsxTCapacityMinimumCapacityThreshold NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCapacityDisplayName,
        vmwNsxTDataCenterCapacityUsageCount,
        vmwNsxTDataCenterMinCapacityThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of objects defined in the system for vmwNsxTDataCenterCapacityDisplayName has
        reached vmwNsxTDataCenterCapacityUsageCount which is above the minimum capacity
        threshold of vmwNsxTDataCenterMinCapacityThreshold%.

        Action required:
        Navigate to the capacity page in the NSX UI and review current usage versus
        threshold limits. If the current usage is expected, consider increasing the
        minimum threshold values. If the current usage is unexpected, review the
        network policies configured to decrease usage at or below the minimum threshold.
        "
    ::= { vmwNsxTCapacityFeature 5 }

vmwNsxTCapacityMinimumCapacityThresholdClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCapacityDisplayName,
        vmwNsxTDataCenterCapacityUsageCount,
        vmwNsxTDataCenterMinCapacityThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of objects defined in the system for vmwNsxTDataCenterCapacityDisplayName has
        reached vmwNsxTDataCenterCapacityUsageCount and is at or below the minimum capacity threshold
        of vmwNsxTDataCenterMinCapacityThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCapacityFeature 6 }

-- **********************************************************
-- Certificates feature event notifications
-- **********************************************************

vmwNsxTCertificatesFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Certificates feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 3 }

vmwNsxTCertificatesFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Certificates feature notifications.
        "
    ::= { vmwNsxTCertificatesFeaturePrefix 0 }

vmwNsxTCertificatesCABundleUpdateRecommended NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCABundleAgeThreshold
    }
    STATUS current
    DESCRIPTION
        "The trusted CA bundle vmwNsxTDataCenterEntityId was updated more than
        vmwNsxTDataCenterCABundleAgeThreshold days ago.
        Update for the trusted CA bundle is recommended.

        Action required:
        Ensure services that are currently using the trusted CA bundle are updated
        to use a recently-updated trusted CA bundle. Unless it is system-provided
        bundle, the bundle can be updated using the
        PUT /policy/api/v1/infra/cabundles/vmwNsxTDataCenterEntityId NSX API.
        Once the expired bundle is no longer in use, it should be deleted (if not
        system-provided) by invoking the
        DELETE /policy/api/v1/infra/cabundles/vmwNsxTDataCenterEntityId NSX API.
        "
    ::= { vmwNsxTCertificatesFeature 7 }

vmwNsxTCertificatesCABundleUpdateRecommendedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The trusted CA bundle vmwNsxTDataCenterEntityId has been removed, updated, or is no longer
        in use.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCertificatesFeature 8 }

vmwNsxTCertificatesCABundleUpdateSuggested NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCABundleAgeThreshold
    }
    STATUS current
    DESCRIPTION
        "The trusted CA bundle vmwNsxTDataCenterEntityId was updated more than
        vmwNsxTDataCenterCABundleAgeThreshold days ago.
        Update for the trusted CA bundle is suggested.

        Action required:
        Ensure services that are currently using the trusted CA bundle are updated
        to use a recently-updated trusted CA bundle. Unless it is system-provided
        bundle, the bundle can be updated using the
        PUT /policy/api/v1/infra/cabundles/vmwNsxTDataCenterEntityId NSX API.
        Once the expired bundle is no longer in use, it should be deleted (if not
        system-provided) by invoking the
        DELETE /policy/api/v1/infra/cabundles/vmwNsxTDataCenterEntityId NSX API.
        "
    ::= { vmwNsxTCertificatesFeature 9 }

vmwNsxTCertificatesCABundleUpdateSuggestedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The trusted CA bundle vmwNsxTDataCenterEntityId has been removed, updated, or is no longer
        in use.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCertificatesFeature 10 }

vmwNsxTCertificatesCertificateExpirationApproaching NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterAPICollectionPath
    }
    STATUS current
    DESCRIPTION
        "Certificate vmwNsxTDataCenterEntityId is approaching expiration.

        Action required:
        Ensure services that are currently using the certificate are updated
        to use a new, non-expiring certificate. Once the expiring
        certificate is no longer in use, it should be deleted by invoking the
        DELETE vmwNsxTDataCenterAPICollectionPathvmwNsxTDataCenterEntityId NSX API.
        "
    ::= { vmwNsxTCertificatesFeature 1 }

vmwNsxTCertificatesCertificateExpirationApproachingClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The expiring certificate vmwNsxTDataCenterEntityId has been removed or is no longer
        approaching expiration.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCertificatesFeature 2 }

vmwNsxTCertificatesCertificateExpired NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterAPICollectionPath
    }
    STATUS current
    DESCRIPTION
        "Certificate vmwNsxTDataCenterEntityId has expired.

        Action required:
        Ensure services that are currently using the certificate are updated
        to use a new, non-expired certificate. Once the expired
        certificate is no longer in use, it should be deleted by invoking the
        DELETE vmwNsxTDataCenterAPICollectionPathvmwNsxTDataCenterEntityId NSX API.
        If the expired certificate is used by NAPP Platform,
        the connection is broken between NSX and NAPP Platform.
        Please check the NAPP Platform troubleshooting document
        to use a self-signed NAPP CA certificate for recovering the connection.
        "
    ::= { vmwNsxTCertificatesFeature 3 }

vmwNsxTCertificatesCertificateExpiredClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The expired certificate vmwNsxTDataCenterEntityId has been removed or is no longer
        expired.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCertificatesFeature 4 }

vmwNsxTCertificatesCertificateIsAboutToExpire NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterAPICollectionPath
    }
    STATUS current
    DESCRIPTION
        "Certificate vmwNsxTDataCenterEntityId is about to expire.

        Action required:
        Ensure services that are currently using the certificate are updated
        to use a new, non-expiring certificate. Once the expiring
        certificate is no longer in use, it should be deleted by invoking the
        DELETE vmwNsxTDataCenterAPICollectionPathvmwNsxTDataCenterEntityId NSX API.
        "
    ::= { vmwNsxTCertificatesFeature 5 }

vmwNsxTCertificatesCertificateIsAboutToExpireClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The expiring certificate vmwNsxTDataCenterEntityId has been removed or is no longer
        about to expire.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCertificatesFeature 6 }

-- **********************************************************
-- Clustering feature event notifications
-- **********************************************************

vmwNsxTClusteringFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Clustering feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 40 }

vmwNsxTClusteringFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Clustering feature notifications.
        "
    ::= { vmwNsxTClusteringFeaturePrefix 0 }

vmwNsxTClusteringClusterDegraded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterGroupType
    }
    STATUS current
    DESCRIPTION
        "Group member vmwNsxTDataCenterManagerNodeId of service vmwNsxTDataCenterGroupType is down.

        Action required:
        1. Invoke the NSX CLI command 'get cluster status' to view the status
           of group members of the cluster.
        2. Ensure the service for vmwNsxTDataCenterGroupType is running on node. Invoke the GET
           /api/v1/node/services/<service_name>/status NSX API or the
           `get service <service_name>` NSX CLI command to determine if the service is running.
           If not running, invoke the POST /api/v1/node/services/<service_name>?action=restart NSX API or
           the `restart service <service_name>` NSX CLI to restart the service.
        3. Check /var/log/ of service vmwNsxTDataCenterGroupType to see if there are errors reported.
        "
    ::= { vmwNsxTClusteringFeature 1 }

vmwNsxTClusteringClusterDegradedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterGroupType
    }
    STATUS current
    DESCRIPTION
        "Group member vmwNsxTDataCenterManagerNodeId of vmwNsxTDataCenterGroupType is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTClusteringFeature 2 }

vmwNsxTClusteringClusterUnavailable NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeIDS,
        vmwNsxTDataCenterGroupType
    }
    STATUS current
    DESCRIPTION
        "All group members vmwNsxTDataCenterManagerNodeIDS of service vmwNsxTDataCenterGroupType are down.

        Action required:
        1. Ensure the service for vmwNsxTDataCenterGroupType is running on node. Invoke the GET
           /api/v1/node/services/<service_name>/status NSX API or the
           `get service <service_name>` NSX CLI command to determine if the service is running.
           If not running, invoke the POST /api/v1/node/services/<service_name>?action=restart NSX API or
           the `restart service <service_name>` NSX CLI to restart the service.
        2. Check /var/log/ of service vmwNsxTDataCenterGroupType to see if there are errors reported.
        "
    ::= { vmwNsxTClusteringFeature 3 }

vmwNsxTClusteringClusterUnavailableClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeIDS,
        vmwNsxTDataCenterGroupType
    }
    STATUS current
    DESCRIPTION
        "All group members vmwNsxTDataCenterManagerNodeIDS of service vmwNsxTDataCenterGroupType are up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTClusteringFeature 4 }

-- **********************************************************
-- CniHealth feature event notifications
-- **********************************************************

vmwNsxTCniHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the CniHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 10 }

vmwNsxTCniHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for CniHealth feature notifications.
        "
    ::= { vmwNsxTCniHealthFeaturePrefix 0 }

vmwNsxTCniHealthHyperbusManagerConnectionDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Hyperbus cannot communicate with the Manager node.

        Action required:
        The hyperbus vmkernel interface (vmk50) may be missing. Please refer to
        Knowledge Base article https://kb.vmware.com/s/article/67432.
        "
    ::= { vmwNsxTCniHealthFeature 3 }

vmwNsxTCniHealthHyperbusManagerConnectionDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Hyperbus can communicate with the Manager node.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCniHealthFeature 4 }

-- **********************************************************
-- Communication feature event notifications
-- **********************************************************

vmwNsxTCommunicationFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Communication feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 35 }

vmwNsxTCommunicationFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Communication feature notifications.
        "
    ::= { vmwNsxTCommunicationFeaturePrefix 0 }

vmwNsxTCommunicationControlChannelToManagerNodeDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceAddress,
        vmwNsxTDataCenterTimeoutInMinutes
    }
    STATUS current
    DESCRIPTION
        "The Transport node vmwNsxTDataCenterEntityId control plane connection to Manager node vmwNsxTDataCenterApplianceAddress is
        down for at least vmwNsxTDataCenterTimeoutInMinutes minutes from the Transport node's point of view.

        Action required:
        1. Check the connectivity from Transport node vmwNsxTDataCenterEntityId to Manager node vmwNsxTDataCenterApplianceAddress
        interface via ping. If they are not pingable, check for flakiness in network connectivity.
        2. Check to see if the TCP connections are established using the netstat output to see if the
        Controller service on the Manager node vmwNsxTDataCenterApplianceAddress is listening for connections on port
        1235. If not, check firewall (or) iptables rules to see if port 1235 is blocking Transport node
        vmwNsxTDataCenterEntityId connection requests. Ensure that there are no host firewalls or network firewalls in
        the underlay are blocking the required IP ports between Manager nodes and Transport nodes.
        This is documented in our ports and protocols tool which is here: https://ports.vmware.com/.
        3. It is possible that the Transport node vmwNsxTDataCenterEntityId may still be in maintenance mode.
        You can check whether the Transport node is in maintenance mode via the following API:
        GET https://<nsx-mgr>/api/v1/transport-nodes/<tn-uuid>
        When maintenance mode is set, the Transport node will not be connected to the Controller
        service. This is usually the case when host upgrade is in progress. Wait for a few minutes and
        check connectivity again.
        Note: Please note that this alarm is not critical and should be resolved. GSS need not be
        contacted for the notification of this alarm unless the alarm remains unresolved over an
        extended period of time.
        "
    ::= { vmwNsxTCommunicationFeature 7 }

vmwNsxTCommunicationControlChannelToManagerNodeDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "The Transport node vmwNsxTDataCenterEntityId restores the control plane connection to Manager node
        vmwNsxTDataCenterApplianceAddress.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 8 }

vmwNsxTCommunicationControlChannelToManagerNodeDownTooLong NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceAddress,
        vmwNsxTDataCenterTimeoutInMinutes
    }
    STATUS current
    DESCRIPTION
        "The Transport node vmwNsxTDataCenterEntityId control plane connection to Manager node vmwNsxTDataCenterApplianceAddress is
        down for at least vmwNsxTDataCenterTimeoutInMinutes minutes from the Transport node's point of view.

        Action required:
        1. Check the connectivity from Transport node vmwNsxTDataCenterEntityId to Manager node vmwNsxTDataCenterApplianceAddress
        interface via ping. If they are not pingable, check for flakiness in network connectivity.
        2. Check to see if the TCP connections are established using the netstat output to see if the
        Controller service on the Manager node vmwNsxTDataCenterApplianceAddress is listening for connections on port
        1235. If not, check firewall (or) iptables rules to see if port 1235 is blocking Transport node
        vmwNsxTDataCenterEntityId connection requests. Ensure that there are no host firewalls or network firewalls in
        the underlay are blocking the required IP ports between Manager nodes and Transport nodes.
        This is documented in our ports and protocols tool which is here: https://ports.vmware.com/.
        3. It is possible that the Transport node vmwNsxTDataCenterEntityId may still be in maintenance mode.
        You can check whether the Transport node is in maintenance mode via the following API:
        GET https://<nsx-mgr>/api/v1/transport-nodes/<tn-uuid>.
        When maintenance mode is set, the Transport node will not be connected to the Controller
        service. This is usually the case when host upgrade is in progress. Wait for a few minutes and
        check connectivity again.
        "
    ::= { vmwNsxTCommunicationFeature 9 }

vmwNsxTCommunicationControlChannelToManagerNodeDownTooLongClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "The Transport node vmwNsxTDataCenterEntityId restores the control plane connection to Manager node
        vmwNsxTDataCenterApplianceAddress.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 10 }

vmwNsxTCommunicationControlChannelToTransportNodeDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCentralControlPlaneId
    }
    STATUS current
    DESCRIPTION
        "Controller service vmwNsxTDataCenterCentralControlPlaneId to Transport node vmwNsxTDataCenterEntityId down for at
        least three minutes from Controller service's point of view.

        Action required:
        1. Check the connectivity from the Controller service vmwNsxTDataCenterCentralControlPlaneId and
        Transport node vmwNsxTDataCenterEntityId. interface via ping. If they are not pingable, check for flakiness in
        network connectivity.
        2. Check to see if the TCP connections are established using the netstat output to see if the
        Controller service vmwNsxTDataCenterCentralControlPlaneId is listening for connections on port
        1235. If not, check firewall (or) iptables rules to see if port 1235 is blocking Transport node
        vmwNsxTDataCenterEntityId connection requests. Ensure that there are no host firewalls or network firewalls in
        the underlay are blocking the required IP ports between Manager nodes and Transport nodes.
        This is documented in our ports and protocols tool which is here: https://ports.vmware.com/.
        3. It is possible that the Transport node vmwNsxTDataCenterEntityId may still be in maintenance mode.
        You can check whether the Transport node is in maintenance mode via the following API:
        GET https://<nsx-mgr>/api/v1/transport-nodes/<tn-uuid>
        When maintenance mode is set, the Transport node will not be connected to the Controller
        service. This is usually the case when host upgrade is in progress. Wait for a few minutes and
        check connectivity again.
        Note: Please note that this alarm is not critical and should be resolved. GSS need not be
        contacted for the notification of this alarm unless the alarm remains unresolved over an
        extended period of time.
        "
    ::= { vmwNsxTCommunicationFeature 11 }

vmwNsxTCommunicationControlChannelToTransportNodeDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCentralControlPlaneId
    }
    STATUS current
    DESCRIPTION
        "Controller service vmwNsxTDataCenterCentralControlPlaneId restores connection to Transport node vmwNsxTDataCenterEntityId.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 12 }

vmwNsxTCommunicationControlChannelToTransportNodeDownLong NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCentralControlPlaneId
    }
    STATUS current
    DESCRIPTION
        "Controller service vmwNsxTDataCenterCentralControlPlaneId to Transport node vmwNsxTDataCenterEntityId down for at
        least 15 minutes from Controller service's point of view.

        Action required:
        1. Check the connectivity from the Controller service vmwNsxTDataCenterCentralControlPlaneId and
        Transport node vmwNsxTDataCenterEntityId. interface via ping. If they are not pingable, check for flakiness in
        network connectivity.
        2. Check to see if the TCP connections are established using the netstat output to see if the
        Controller service vmwNsxTDataCenterCentralControlPlaneId is listening for connections on port
        1235. If not, check firewall (or) iptables rules to see if port 1235 is blocking Transport node
        vmwNsxTDataCenterEntityId connection requests. Ensure that there are no host firewalls or network firewalls in
        the underlay are blocking the required IP ports between Manager nodes and Transport nodes.
        This is documented in our ports and protocols tool which is here: https://ports.vmware.com/.
        3. It is possible that the Transport node vmwNsxTDataCenterEntityId may still be in maintenance mode.
        You can check whether the Transport node is in maintenance mode via the following API:
        GET https://<nsx-mgr>/api/v1/transport-nodes/<tn-uuid>
        When maintenance mode is set, the Transport node will not be connected to the Controller
        service. This is usually the case when host upgrade is in progress. Wait for a few minutes and
        check connectivity again.
        "
    ::= { vmwNsxTCommunicationFeature 19 }

vmwNsxTCommunicationControlChannelToTransportNodeDownLongClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterCentralControlPlaneId
    }
    STATUS current
    DESCRIPTION
        "Controller service vmwNsxTDataCenterCentralControlPlaneId restores connection to Transport node vmwNsxTDataCenterEntityId.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 20 }

vmwNsxTCommunicationManagementChannelToManagerNodeDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterApplianceAddress,
        vmwNsxTDataCenterTransportNodeId
    }
    STATUS current
    DESCRIPTION
        "Management channel to Manager Node vmwNsxTDataCenterManagerNodeId
        (vmwNsxTDataCenterApplianceAddress) is down for 5 minutes.

        Action required:
        Ensure there is network connectivity between the
        Transport node vmwNsxTDataCenterTransportNodeId
        and master Manager node. Also ensure no firewalls are blocking traffic
        between the nodes. Ensure the messaging manager service is running on
        Manager nodes by invoking the command `/etc/init.d/messaging-manager status`.
        If the messaging manager is not running, restart it by
        invoking the command `/etc/init.d/messaging-manager restart`.
        "
    ::= { vmwNsxTCommunicationFeature 13 }

vmwNsxTCommunicationManagementChannelToManagerNodeDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "Management channel to Manager Node vmwNsxTDataCenterManagerNodeId
        (vmwNsxTDataCenterApplianceAddress) is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 14 }

vmwNsxTCommunicationManagementChannelToManagerNodeDownLong NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterApplianceAddress,
        vmwNsxTDataCenterTransportNodeId
    }
    STATUS current
    DESCRIPTION
        "Management channel to Manager Node vmwNsxTDataCenterManagerNodeId
        (vmwNsxTDataCenterApplianceAddress) is down for 15 minutes.

        Action required:
        Ensure there is network connectivity between the
        Transport node vmwNsxTDataCenterTransportNodeId
        and master Manager nodes. Also ensure no firewalls are blocking traffic
        between the nodes. Ensure the messaging manager service is running on
        Manager nodes by invoking the command `/etc/init.d/messaging-manager status`.
        If the messaging manager is not running, restart it by
        invoking the command `/etc/init.d/messaging-manager restart`.
        "
    ::= { vmwNsxTCommunicationFeature 25 }

vmwNsxTCommunicationManagementChannelToManagerNodeDownLongClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "Management channel to Manager Node vmwNsxTDataCenterManagerNodeId
        (vmwNsxTDataCenterApplianceAddress) is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 26 }

vmwNsxTCommunicationManagementChannelToTransportNodeDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName,
        vmwNsxTDataCenterTransportNodeAddress
    }
    STATUS current
    DESCRIPTION
        "Management channel to Transport Node vmwNsxTDataCenterTransportNodeName
        (vmwNsxTDataCenterTransportNodeAddress) is down for 5 minutes.

        Action required:
        Ensure there is network connectivity between the Manager nodes
        and Transport node vmwNsxTDataCenterTransportNodeName (vmwNsxTDataCenterTransportNodeAddress)
        and no firewalls are blocking traffic between the nodes.
        On Windows Transport nodes, ensure the nsx-proxy service is running on the
        Transport node by invoking the command `C:\NSX\nsx-proxy\nsx-proxy.ps1 status`
        in the Windows PowerShell. If it is not running, restart it by
        invoking the command `C:\NSX\nsx-proxy\nsx-proxy.ps1 restart`.
        On all other Transport nodes, ensure the nsx-proxy service is running on the
        Transport node by invoking the command `/etc/init.d/nsx-proxy status`.
        If it is not running, restart it by invoking the command
        `/etc/init.d/nsx-proxy restart`.
        "
    ::= { vmwNsxTCommunicationFeature 1 }

vmwNsxTCommunicationManagementChannelToTransportNodeDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName,
        vmwNsxTDataCenterTransportNodeAddress
    }
    STATUS current
    DESCRIPTION
        "Management channel to Transport Node vmwNsxTDataCenterTransportNodeName
        (vmwNsxTDataCenterTransportNodeAddress) is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 2 }

vmwNsxTCommunicationManagementChannelToTransportNodeDownLg NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName,
        vmwNsxTDataCenterTransportNodeAddress
    }
    STATUS current
    DESCRIPTION
        "Management channel to Transport Node vmwNsxTDataCenterTransportNodeName
        (vmwNsxTDataCenterTransportNodeAddress) is down for 15 minutes.

        Action required:
        Ensure there is network connectivity between the Manager nodes
        and Transport node vmwNsxTDataCenterTransportNodeName (vmwNsxTDataCenterTransportNodeAddress)
        and no firewalls are blocking traffic between the nodes.
        On Windows Transport nodes, ensure the nsx-proxy service is running on the
        Transport node by invoking the command `C:\NSX\nsx-proxy\nsx-proxy.ps1 status`
        in the Windows PowerShell. If it is not running, restart it by
        invoking the command `C:\NSX\nsx-proxy\nsx-proxy.ps1 restart`.
        On all other Transport nodes, ensure the nsx-proxy service is running on the
        Transport node by invoking the command `/etc/init.d/nsx-proxy status`.
        If it is not running, restart it by invoking the command
        `/etc/init.d/nsx-proxy restart`.
        "
    ::= { vmwNsxTCommunicationFeature 5 }

vmwNsxTCommunicationManagementChannelToTransportNodeDownLgClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName,
        vmwNsxTDataCenterTransportNodeAddress
    }
    STATUS current
    DESCRIPTION
        "Management channel to Transport Node vmwNsxTDataCenterTransportNodeName
        (vmwNsxTDataCenterTransportNodeAddress) is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 6 }

vmwNsxTCommunicationManagerClusterLatencyHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterApplianceAddress,
        vmwNsxTDataCenterRemoteManagerNodeId,
        vmwNsxTDataCenterRemoteApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "The average network latency between Manager nodes vmwNsxTDataCenterManagerNodeId (vmwNsxTDataCenterApplianceAddress)
        and vmwNsxTDataCenterRemoteManagerNodeId (vmwNsxTDataCenterRemoteApplianceAddress) is more than 10ms for the last 5 minutes.

        Action required:
        Ensure there are no firewall rules blocking ping traffic between the Manager nodes.
        If there are other high bandwidth servers and applications sharing the local network,
        consider moving these to a different network.
        "
    ::= { vmwNsxTCommunicationFeature 17 }

vmwNsxTCommunicationManagerClusterLatencyHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterApplianceAddress,
        vmwNsxTDataCenterRemoteManagerNodeId,
        vmwNsxTDataCenterRemoteApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "The average network latency between Manager nodes vmwNsxTDataCenterManagerNodeId (vmwNsxTDataCenterApplianceAddress)
        and vmwNsxTDataCenterRemoteManagerNodeId (vmwNsxTDataCenterRemoteApplianceAddress) is within 10ms.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 18 }

vmwNsxTCommunicationManagerControlChannelDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeName,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "The communication between the management function and the control
        function has failed on Manager node vmwNsxTDataCenterManagerNodeName (vmwNsxTDataCenterApplianceAddress).

        Action required:
        1. On Manager node vmwNsxTDataCenterManagerNodeName (vmwNsxTDataCenterApplianceAddress), please invoke the
        following NSX CLI command: `get service applianceproxy` to check the status
        of the service periodically for 60 minutes.

        2. If the service is not running for more than 60 minutes, invoke
        the following NSX CLI command: `restart service applianceproxy` and recheck the
        status. If the service is still down, please contact VMware support.
        "
    ::= { vmwNsxTCommunicationFeature 3 }

vmwNsxTCommunicationManagerControlChannelDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterManagerNodeName,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "The communication between the management function and the control function
        has been restored on Manager node vmwNsxTDataCenterManagerNodeName (vmwNsxTDataCenterApplianceAddress).

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 4 }

vmwNsxTCommunicationManagerFQDNLookupFailure NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceFQDN
    }
    STATUS current
    DESCRIPTION
        "DNS lookup failed for Manager node vmwNsxTDataCenterEntityId with FQDN
        vmwNsxTDataCenterApplianceFQDN and the publish_fqdns flag was set.

        Action required:
        1. Assign correct FQDNs to all Manager nodes and verify the DNS
        configuration is correct for successful lookup of all Manager
        nodes' FQDNs.
        2. Alternatively, disable the use of FQDNs by invoking the NSX API
        PUT /api/v1/configs/management with publish_fqdns set to false in the
        request body. After that calls from Transport nodes and from Federation
        to Manager nodes in this cluster will use only IP addresses.
        "
    ::= { vmwNsxTCommunicationFeature 21 }

vmwNsxTCommunicationManagerFQDNLookupFailureClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceFQDN
    }
    STATUS current
    DESCRIPTION
        "FQDN lookup succeeded for Manager node vmwNsxTDataCenterEntityId with FQDN
        vmwNsxTDataCenterApplianceFQDN or the publish_fqdns flag was cleared.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 22 }

vmwNsxTCommunicationManagerFQDNReverseLookupFailure NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "Reverse DNS lookup failed for Manager node vmwNsxTDataCenterEntityId with IP address
        vmwNsxTDataCenterApplianceAddress and the publish_fqdns flag was set.

        Action required:
        1. Assign correct FQDNs to all Manager nodes and verify the DNS
        configuration is correct for successful reverse lookup of the Manager
        node's IP address.
        2. Alternatively, disable the use of FQDNs by invoking the NSX API
        PUT /api/v1/configs/management with publish_fqdns set to false in the
        request body. After that calls from Transport nodes and from Federation
        to Manager nodes in this cluster will use only IP addresses.
        "
    ::= { vmwNsxTCommunicationFeature 23 }

vmwNsxTCommunicationManagerFQDNReverseLookupFailureClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterApplianceAddress
    }
    STATUS current
    DESCRIPTION
        "Reverse DNS lookup succeeded for Manager node vmwNsxTDataCenterEntityId with IP address
        vmwNsxTDataCenterApplianceAddress or the publish_fqdns flag was cleared.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTCommunicationFeature 24 }

-- **********************************************************
-- DHCP feature event notifications
-- **********************************************************

vmwNsxTDHCPFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the DHCP feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 20 }

vmwNsxTDHCPFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for DHCP feature notifications.
        "
    ::= { vmwNsxTDHCPFeaturePrefix 0 }

vmwNsxTDHCPPoolLeaseAllocationFailed NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDHCPServerId
    }
    STATUS current
    DESCRIPTION
        "The addresses in IP Pool vmwNsxTDataCenterEntityId of DHCP Server vmwNsxTDataCenterDHCPServerId have
        been exhausted. The last DHCP request has failed and future requests will
        fail.

        Action required:
        Review the DHCP pool configuration in the NSX UI or on the Edge node where
        the DHCP server is running by invoking the NSX CLI command `get dhcp ip-pool`.
        Also review the current active leases on the Edge node by invoking the NSX
        CLI command `get dhcp lease`.  Compare the leases to the number of active
        VMs. Consider reducing the lease time on the DHCP server configuration if
        the number of VMs are low compared to the number of active leases. Also
        consider expanding the pool range for the DHCP server by visiting the
        Networking | Segments | Segment page in the NSX UI.
        "
    ::= { vmwNsxTDHCPFeature 1 }

vmwNsxTDHCPPoolLeaseAllocationFailedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDHCPServerId
    }
    STATUS current
    DESCRIPTION
        "IP Pool vmwNsxTDataCenterEntityId of DHCP Server vmwNsxTDataCenterDHCPServerId is no longer exhausted.
        A lease is successfully allocated to the last DHCP request.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDHCPFeature 2 }

vmwNsxTDHCPPoolOverloaded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDHCPServerId,
        vmwNsxTDataCenterDHCPPoolUsage
    }
    STATUS current
    DESCRIPTION
        "The DHCP Server vmwNsxTDataCenterDHCPServerId IP Pool vmwNsxTDataCenterEntityId usage is approaching
        exhaustion with vmwNsxTDataCenterDHCPPoolUsage% IPs allocated.

        Action required:
        Review the DHCP pool configuration in the NSX UI or on the Edge node where
        the DHCP server is running by invoking the NSX CLI command `get dhcp ip-pool`.
        Also review the current active leases on the Edge node by invoking the NSX
        CLI command `get dhcp lease`.  Compare the leases to the number of active
        VMs. Consider reducing the lease time on the DHCP server configuration if
        the number of VMs are low compared to the number of active leases. Also
        consider expanding the pool range for the DHCP server by visiting the
        Networking | Segments | Segment page in the NSX UI.
        "
    ::= { vmwNsxTDHCPFeature 3 }

vmwNsxTDHCPPoolOverloadedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDHCPServerId
    }
    STATUS current
    DESCRIPTION
        "The DHCP Server vmwNsxTDataCenterDHCPServerId IP Pool vmwNsxTDataCenterEntityId has fallen below the
        high usage threshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDHCPFeature 4 }

-- **********************************************************
-- DistributedFirewall feature event notifications
-- **********************************************************

vmwNsxTDistributedFirewallFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the DistributedFirewall feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 31 }

vmwNsxTDistributedFirewallFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for DistributedFirewall feature notifications.
        "
    ::= { vmwNsxTDistributedFirewallFeaturePrefix 0 }

vmwNsxTDistributedFirewallDFWCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The DFW CPU usage on Transport node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Consider re-balancing the VM workloads on this host to other hosts.  Please
        review the security design for optimization. For example, use the apply-to
        configuration if the rules are not applicable to the entire datacenter.
        "
    ::= { vmwNsxTDistributedFirewallFeature 1 }

vmwNsxTDistributedFirewallDFWCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The DFW CPU usage on Transport node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedFirewallFeature 2 }

vmwNsxTDistributedFirewallDFWMemoryUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterHeapType,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The DFW Memory usage vmwNsxTDataCenterHeapType on Transport node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterSystemResourceUsage% which
        is at or above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        View the current DFW memory usage by invoking the NSX CLI command
        `get firewall thresholds` on the host. Consider re-balancing the
        workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedFirewallFeature 3 }

vmwNsxTDistributedFirewallDFWMemoryUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterHeapType,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The DFW Memory usage vmwNsxTDataCenterHeapType on Transport node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterSystemResourceUsage% which
        is below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedFirewallFeature 4 }

vmwNsxTDistributedFirewallDFWSessionCountHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The DFW session count is high on Transport node vmwNsxTDataCenterEntityId, it has
        reached vmwNsxTDataCenterSystemResourceUsage% which is at or above the threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Review the network traffic load level of the workloads on the host.
        Consider re-balancing the workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedFirewallFeature 5 }

vmwNsxTDistributedFirewallDFWSessionCountHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The DFW session count on Transport node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the the threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedFirewallFeature 6 }

vmwNsxTDistributedFirewallDFWVmotionFailure NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName
    }
    STATUS current
    DESCRIPTION
        "The DFW vMotion for DFW filter vmwNsxTDataCenterEntityId on destination host
        vmwNsxTDataCenterTransportNodeName has failed and the port for the entity has been
        disconnected.

        Action required:
        Check VMs on the host in NSX Manager, manually repush the DFW configuration
        through NSX Manager UI. The DFW policy to be repushed can be traced by the
        DFW filter vmwNsxTDataCenterEntityId. Also consider finding the VM to which the DFW filter
        is attached and restart it.
        "
    ::= { vmwNsxTDistributedFirewallFeature 7 }

vmwNsxTDistributedFirewallDFWVmotionFailureClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName
    }
    STATUS current
    DESCRIPTION
        "The DFW configuration for DFW filter vmwNsxTDataCenterEntityId on the destination host
        vmwNsxTDataCenterTransportNodeName has succeeded and error caused by DFW vMotion failure
        cleared.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedFirewallFeature 8 }

-- **********************************************************
-- DistributedIDSIPS feature event notifications
-- **********************************************************

vmwNsxTDistributedIDSIPSFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the DistributedIDSIPS feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 33 }

vmwNsxTDistributedIDSIPSFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for DistributedIDSIPS feature notifications.
        "
    ::= { vmwNsxTDistributedIDSIPSFeaturePrefix 0 }

vmwNsxTDistributedIDSIPSMaxEventsReached NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIDSEventsCount,
        vmwNsxTDataCenterMaxIDSEventsAllowed
    }
    STATUS current
    DESCRIPTION
        "The number of intrusion events in the system is vmwNsxTDataCenterIDSEventsCount
        which is higher than the maximum allowed value vmwNsxTDataCenterMaxIDSEventsAllowed.

        Action required:
        There is no manual intervention required. A purge job will kick in automatically
        every 3 minutes and delete 10% of the older records to bring the total intrusion events
        count in the system to below the threshold value of 1.5 million events.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 3 }

vmwNsxTDistributedIDSIPSMaxEventsReachedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIDSEventsCount,
        vmwNsxTDataCenterMaxIDSEventsAllowed
    }
    STATUS current
    DESCRIPTION
        "The number of intrusion events in the system is vmwNsxTDataCenterIDSEventsCount
        which is below the maximum allowed value vmwNsxTDataCenterMaxIDSEventsAllowed.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 4 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine CPU usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is at or above the high
        threshold value of 75%.

        Action required:
        Consider re-balancing the VM workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 7 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine CPU usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is below the high
        threshold value of 75%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 8 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageMediumHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine CPU usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is at or above the medium
        high threshold value of 85%.

        Action required:
        Consider re-balancing the VM workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 21 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageMediumHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine CPU usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is below the medium high
        threshold value of 85%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 22 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine CPU usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is at or above the very
        high threshold value of 95%.

        Action required:
        Consider re-balancing the VM workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 9 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine CPU usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is below the very
        high threshold value of 95%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 10 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "NSX IDPS is enabled via NSX policy and IDPS rules are configured, but
        NSX-IDPS engine is down.

        Action required:
        1. Check /var/log/nsx-syslog.log to see if there are errors reported.
        2. Invoke the NSX CLI command `get ids engine status` to check
           if NSX Distributed IDPS is in disabled state.  If so,
           invoke `/etc/init.d/nsx-idps start` to start the service.
        3. Invoke `/etc/init.d/nsx-vdpi status` to check if nsx-vdpi is running.
           If not, invoke `/etc/init.d/nsx-vdpi start` to start the service.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 13 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "NSX IDPS is in one of the cases below.
        1. NSX IDPS is disabled via NSX policy.
        2. NSX IDPS engine is enabled, NSX-IDPS engine and vdpi are up, and
           NSX IDPS has been enabled and IDPS rules are configured
           via NSX Policy.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 14 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine memory usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is at or above the high
        threshold value of 75%.

        Action required:
        Consider re-balancing the VM workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 15 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine memory usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is below the high threshold
        value of 75%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 16 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageMediumHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine memory usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is at or above the medium
        high threshold value of 85%.

        Action required:
        Consider re-balancing the VM workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 17 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageMediumHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine memory usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is below the medium
        high threshold value of 85%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 18 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine  memory usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is at or above the
        very high threshold value of 95%.

        Action required:
        Consider re-balancing the VM workloads on this host to other hosts.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 19 }

vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage
    }
    STATUS current
    DESCRIPTION
        "NSX-IDPS engine  memory usage has reached
        vmwNsxTDataCenterSystemResourceUsage%, which is below the very high
        threshold value of 95%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDistributedIDSIPSFeature 20 }

-- **********************************************************
-- DNS feature event notifications
-- **********************************************************

vmwNsxTDNSFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the DNS feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 30 }

vmwNsxTDNSFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for DNS feature notifications.
        "
    ::= { vmwNsxTDNSFeaturePrefix 0 }

vmwNsxTDNSForwarderDisabled NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "DNS forwarder vmwNsxTDataCenterEntityId is disabled.

        Action required:
        1. Invoke the NSX CLI command `get dns-forwarders status` to verify
        if the DNS forwarder is in the disabled state.
        2. Use NSX Policy API or Manager API to enable the DNS forwarder it
        should not be in the disabled state.
        "
    ::= { vmwNsxTDNSFeature 1 }

vmwNsxTDNSForwarderDisabledClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "DNS forwarder vmwNsxTDataCenterEntityId is enabled.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDNSFeature 2 }

vmwNsxTDNSForwarderDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "DNS forwarder vmwNsxTDataCenterEntityId is not running. This is impacting the
        identified DNS Forwarder that is currently enabled.

        Action required:
        1. Invoke the NSX CLI command `get dns-forwarders status` to verify
        if the DNS forwarder is in down state.
        2. Check /var/log/syslog to see if there are errors reported.
        3. Collect a support bundle and contact the NSX support team.
        "
    ::= { vmwNsxTDNSFeature 3 }

vmwNsxTDNSForwarderDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "DNS forwarder vmwNsxTDataCenterEntityId is running again.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDNSFeature 4 }

vmwNsxTDNSForwarderUpstreamServerTimeout NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntentPath,
        vmwNsxTDataCenterDNSId,
        vmwNsxTDataCenterDNSUpstreamIP
    }
    STATUS current
    DESCRIPTION
        "DNS forwarder vmwNsxTDataCenterIntentPath(vmwNsxTDataCenterDNSId) did not receive a timely response
        from upstream server vmwNsxTDataCenterDNSUpstreamIP. Compute instance connectivity to
        timed out FQDNs may be impacted.

        Action required:
        1. Invoke the NSX API GET /api/v1/dns/forwarders/vmwNsxTDataCenterDNSId/nslookup?
           address=<address>&server_ip=vmwNsxTDataCenterDNSUpstreamIP&source_ip=<source_ip>.
           This API request triggers a DNS lookup to the upstream server in the
           DNS forwarder's network namespace. <address> is the IP address or FQDN
           in the same domain as the upstream server. <source_ip> is an IP address
           in the upstream server's zone.
           If the API returns a connection timed out response, there is likely a
           network error or upstream server problem. Please check why DSN lookups
           are not reaching the upstream server or why the upstream server is not
           returning a response. If the API response indicates the upstream server
           is answering, proceed to step 2.
        2. Invoke the NSX API GET /api/v1/dns/forwarders/vmwNsxTDataCenterDNSId/nslookup?
           address=<address>.
           This API request triggers a DNS lookup to the DNS forwarder. If the API
           returns a valid response, the upstream server may have recovered and this
           alarm should get resolved within a few minutes. If the API returns a
           connection timed out response, proceed to step 3.
        3. Invoke the NSX CLI command `get dns-forwarder vmwNsxTDataCenterDNSId live-debug
           server-ip vmwNsxTDataCenterDNSUpstreamIP`.
           This command triggers live debugging on the upstream server and logs
           details and statistics showing why the DNS forwarder is not getting a
           response.
        "
    ::= { vmwNsxTDNSFeature 5 }

vmwNsxTDNSForwarderUpstreamServerTimeoutClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntentPath,
        vmwNsxTDataCenterDNSId,
        vmwNsxTDataCenterDNSUpstreamIP
    }
    STATUS current
    DESCRIPTION
        "DNS forwarder vmwNsxTDataCenterIntentPath(vmwNsxTDataCenterDNSId) upstream server vmwNsxTDataCenterDNSUpstreamIP

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTDNSFeature 6 }

-- **********************************************************
-- Edge feature event notifications
-- **********************************************************

vmwNsxTEdgeFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Edge feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 45 }

vmwNsxTEdgeFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Edge feature notifications.
        "
    ::= { vmwNsxTEdgeFeaturePrefix 0 }

vmwNsxTEdgeEdgeNodeSettingsAndvSphereSettingsAreChanged NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNodeAndvSphereSettingsMismatchReason
    }
    STATUS current
    DESCRIPTION
        "The Edge node vmwNsxTDataCenterEntityId settings and vSphere configuration are changed and does not
        match the policy intent configuration. The Edge node configuration visible to user
        on UI or API is not same as what is realized. The realized Edge node changes made by
        user outside of NSX Manager are shown in the details of this alarm and any edits in
        UI or API will overwrite the realized configuration. Fields that differ for Edge node
        settings and vSphere configuration are listed in runtime data vmwNsxTDataCenterEdgeNodeAndvSphereSettingsMismatchReason

        Action required:
        Please review the node settings and vSphere configuration of this Edge Transport Node vmwNsxTDataCenterEntityId.
        Please follow one of following actions to resolve alarm -
        1. Manually update Edge Transport Node setting Policy intent using
           API : PUT https://<manager-ip>/api/v1/transport-nodes/<tn-id>.
        2. Accept intent or vSphere realized Edge node configuration or realized
           Edge node settings for this Edge Transport Node through Edge Transport Node
           resolver to resolve this alarm.
        3. Resolve alarm by accepting the Edge node settings and vSphere realized configuration
           using refresh API - POST https://<manager-ip>/api/v1/transport-nodes/<tn-id>?action=refresh_node_configuration&resource_type=EdgeNode.
        "
    ::= { vmwNsxTEdgeFeature 1 }

vmwNsxTEdgeEdgeNodeSettingsAndvSphereSettingsAreChangedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Edge node vmwNsxTDataCenterEntityId node settings and vSphere settings are consistent with policy intent now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeFeature 2 }

vmwNsxTEdgeEdgeNodeSettingsMismatch NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNodeSettingMismatchReason
    }
    STATUS current
    DESCRIPTION
        "The Edge node vmwNsxTDataCenterEntityId settings configuration does not match the policy
        intent configuration. The Edge node configuration visible to user on
        UI or API is not same as what is realized. The realized Edge node changes
        made by user outside of NSX Manager are shown in the details of this alarm
        and any edits in UI or API will overwrite the realized configuration.
        Fields that differ for the Edge node are listed in runtime data
        vmwNsxTDataCenterEdgeNodeSettingMismatchReason

        Action required:
        Please review the node settings of this Edge transport node vmwNsxTDataCenterEntityId.
        Please follow one of following actions to resolve alarm -
        1. Manually update Edge transport node setting Policy intent
           using API - PUT https://<manager-ip>/api/v1/transport-nodes/<tn-id>.
        2. Accept intent or realized Edge node settings for this Edge transport node
           through Edge transport node resolver to resolve this alarm.
        3. Resolve alarm by accepting the Edge node settings configuration using
           refresh API - POST https://<manager-ip>/api/v1/transport-nodes/<tn-id>?action=refresh_node_configuration&resource_type=EdgeNode.
        "
    ::= { vmwNsxTEdgeFeature 3 }

vmwNsxTEdgeEdgeNodeSettingsMismatchClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Edge node vmwNsxTDataCenterEntityId node settings are consistent with policy intent now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeFeature 4 }

vmwNsxTEdgeEdgeVmvSphereSettingsMismatch NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeVMvSphereSettingsMismatchReason
    }
    STATUS current
    DESCRIPTION
        "The Edge node vmwNsxTDataCenterEntityId configuration on vSphere does not match the policy
        intent configuration. The Edge node configuration visible to user on UI or API
        is not same as what is realized. The realized Edge node changes made by user
        outside of NSX Manager are shown in the details of this alarm and any edits
        in UI or API will overwrite the realized configuration. Fields that differ for
        the Edge node are listed in runtime data vmwNsxTDataCenterEdgeVMvSphereSettingsMismatchReason

        Action required:
        Please review the vSphere configuration of this Edge Transport Node vmwNsxTDataCenterEntityId.
        Please follow one of following actions to resolve alarm -
        1. Accept intent or vSphere realized Edge node configuration for this Edge Transport Node
           through Edge Transport Node resolver to resolve this alarm.
        2. Resolve alarm by accepting the Edge node vSphere realized configuration using
           refresh API - POST https://<manager-ip>/api/v1/transport-nodes/<tn-id>?action=refresh_node_configuration&resource_type=EdgeNode.
        "
    ::= { vmwNsxTEdgeFeature 5 }

vmwNsxTEdgeEdgeVmvSphereSettingsMismatchClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Edge node vmwNsxTDataCenterEntityId VM vSphere settings are consistent with policy intent now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeFeature 6 }

vmwNsxTEdgeEdgevSphereLocationMismatch NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgevSphereLocationMismatchReason
    }
    STATUS current
    DESCRIPTION
        "The Edge node vmwNsxTDataCenterEntityId has been moved using vMotion. The Edge node vmwNsxTDataCenterEntityId, the configuration
        on vSphere does not match the policy intent configuration. The Edge node configuration visible to
        user on UI or API is not same as what is realized. The realized Edge node changes made by user outside
        of NSX Manager are shown in the details of this alarm. Fields that differ for the Edge node are listed
        in runtime data vmwNsxTDataCenterEdgevSphereLocationMismatchReason

        Action required:
        Please review the vSphere configuration of this Edge Transport Node vmwNsxTDataCenterEntityId.
        Please follow one of following actions to resolve alarm -
        1. Resolve alarm by accepting the Edge node vSphere realized config using
           refresh API - POST https://<manager-ip>/api/v1/transport-nodes/<tn-id>?action=refresh_node_configuration&resource_type=EdgeNode.
        2. If you want to return to the previous location please use
           NSX Redeploy API - POST https://<manager-ip>/api/v1/transport-nodes/<tn-id>?action=redeploy.
           vMotion back to the original host is not supported.
        "
    ::= { vmwNsxTEdgeFeature 7 }

vmwNsxTEdgeEdgevSphereLocationMismatchClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Edge node vmwNsxTDataCenterEntityId node vSphere settings are consistent with policy intent now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeFeature 8 }

-- **********************************************************
-- EdgeHealth feature event notifications
-- **********************************************************

vmwNsxTEdgeHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the EdgeHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 2 }

vmwNsxTEdgeHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for EdgeHealth feature notifications.
        "
    ::= { vmwNsxTEdgeHealthFeaturePrefix 0 }

vmwNsxTEdgeHealthDatapathThreadDeadlocked NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeThreadName
    }
    STATUS current
    DESCRIPTION
        "Edge node datapath thread vmwNsxTDataCenterEdgeThreadName is deadlocked.

        Action required:
        Restart the dataplane service by invoking the NSX CLI command `restart service dataplane`.
        "
    ::= { vmwNsxTEdgeHealthFeature 45 }

vmwNsxTEdgeHealthDatapathThreadDeadlockedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeThreadName
    }
    STATUS current
    DESCRIPTION
        "Edge node datapath thread vmwNsxTDataCenterEdgeThreadName is free from deadlock.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 46 }

vmwNsxTEdgeHealthEdgeCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this Edge
        node. Consider adjusting the Edge appliance form factor size or rebalancing
        services to other Edge nodes for the applicable workload.
        "
    ::= { vmwNsxTEdgeHealthFeature 1 }

vmwNsxTEdgeHealthEdgeCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 2 }

vmwNsxTEdgeHealthEdgeCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this Edge
        node. Consider adjusting the Edge appliance form factor size or rebalancing
        services to other Edge nodes for the applicable workload.
        "
    ::= { vmwNsxTEdgeHealthFeature 3 }

vmwNsxTEdgeHealthEdgeCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 4 }

vmwNsxTEdgeHealthEdgeDatapathConfigurationFailure NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Failed to enable the datapath on the Edge node after three attempts.

        Action required:
        Ensure the Edge node's connectivity to the Manager node is healthy.  From
        the Edge node's NSX CLI, invoke the command `get services` to check the
        health of services. If the dataplane service is stopped, invoke the
        command `start service dataplane` to start it.
        "
    ::= { vmwNsxTEdgeHealthFeature 21 }

vmwNsxTEdgeHealthEdgeDatapathConfigurationFailureClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Datapath on the Edge node has been successfully enabled.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 22 }

vmwNsxTEdgeHealthEdgeDatapathCPUHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDatapathResourceUsage
    }
    STATUS current
    DESCRIPTION
        "The datapath CPU usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterDatapathResourceUsage% which is at or above the high
        threshold for at least two minutes.

        Action required:
        Review the CPU statistics on the Edge node by invoking the NSX CLI command
        `get dataplane cpu stats` to show packet rates per CPU core.  Higher CPU
        usage is expected with higher packet rates. Consider increasing the Edge
        appliance form factor size and rebalancing services on this Edge node to
        other Edge nodes in the same cluster or other Edge clusters.
        "
    ::= { vmwNsxTEdgeHealthFeature 17 }

vmwNsxTEdgeHealthEdgeDatapathCPUHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Edge node vmwNsxTDataCenterEntityId has reached below the
        high threshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 18 }

vmwNsxTEdgeHealthEdgeDatapathCPUVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDatapathResourceUsage
    }
    STATUS current
    DESCRIPTION
        "The datapath CPU usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterDatapathResourceUsage% which is at or above the very high
        threshold for at least two minutes.

        Action required:
        Review the CPU statistics on the Edge node by invoking the NSX CLI command
        `get dataplane cpu stats` to show packet rates per CPU core.  Higher CPU
        usage is expected with higher packet rates. Consider increasing the Edge
        appliance form factor size and rebalancing services on this Edge node to
        other Edge nodes in the same cluster or other Edge clusters.
        "
    ::= { vmwNsxTEdgeHealthFeature 19 }

vmwNsxTEdgeHealthEdgeDatapathCPUVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Edge node vmwNsxTDataCenterEntityId has reached below the
        very high threshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 20 }

vmwNsxTEdgeHealthEdgeDatapathCryptodrvDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeCryptoDrvName
    }
    STATUS current
    DESCRIPTION
        "Edge node crypto driver vmwNsxTDataCenterEdgeCryptoDrvName is down.

        Action required:
        Upgrade the Edge node as needed.
        "
    ::= { vmwNsxTEdgeHealthFeature 23 }

vmwNsxTEdgeHealthEdgeDatapathCryptodrvDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeCryptoDrvName
    }
    STATUS current
    DESCRIPTION
        "Edge node crypto driver vmwNsxTDataCenterEdgeCryptoDrvName is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 24 }

vmwNsxTEdgeHealthEdgeDatapathMempoolHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterMempoolName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The datapath mempool usage for vmwNsxTDataCenterMempoolName on Edge node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterSystemResourceUsage% which
        is at or above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Log in as the root user and invoke the command
        `edge-appctl -t /var/run/vmware/edge/dpd.ctl mempool/show` and
        `edge-appctl -t /var/run/vmware/edge/dpd.ctl memory/show malloc_heap` to
        check DPDK memory usage.
        "
    ::= { vmwNsxTEdgeHealthFeature 25 }

vmwNsxTEdgeHealthEdgeDatapathMempoolHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterMempoolName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The datapath mempool usage for vmwNsxTDataCenterMempoolName on Edge node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterSystemResourceUsage% which
        is below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 26 }

vmwNsxTEdgeHealthEdgeDatapathNICThroughputHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName,
        vmwNsxTDataCenterNICThroughput,
        vmwNsxTDataCenterNICThroughputThreshold
    }
    STATUS current
    DESCRIPTION
        "The datapath NIC throughput for vmwNsxTDataCenterEdgeNICName on Edge node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterNICThroughput% which is at or above
        the high threshold value of vmwNsxTDataCenterNICThroughputThreshold%.

        Action required:
        Examine the traffic thoughput levels on the NIC and determine whether
        configuration changes are needed. The 'get dataplane thoughput <seconds>'
        command can be used to monitor throughput.
        "
    ::= { vmwNsxTEdgeHealthFeature 49 }

vmwNsxTEdgeHealthEdgeDatapathNICThroughputHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName,
        vmwNsxTDataCenterNICThroughput,
        vmwNsxTDataCenterNICThroughputThreshold
    }
    STATUS current
    DESCRIPTION
        "The datapath NIC throughput for vmwNsxTDataCenterEdgeNICName on Edge node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterNICThroughput% which
        is below the high threshold value of vmwNsxTDataCenterNICThroughputThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 50 }

vmwNsxTEdgeHealthEdgeDatapathNICThroughputVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName,
        vmwNsxTDataCenterNICThroughput,
        vmwNsxTDataCenterNICThroughputThreshold
    }
    STATUS current
    DESCRIPTION
        "The datapath NIC throughput for vmwNsxTDataCenterEdgeNICName on Edge node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterNICThroughput% which is at or above
        the very high threshold value of vmwNsxTDataCenterNICThroughputThreshold%.

        Action required:
        Examine the traffic thoughput levels on the NIC and determine whether
        configuration changes are needed. The 'get dataplane thoughput <seconds>'
        command can be used to monitor throughput.
        "
    ::= { vmwNsxTEdgeHealthFeature 51 }

vmwNsxTEdgeHealthEdgeDatapathNICThroughputVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName,
        vmwNsxTDataCenterNICThroughput,
        vmwNsxTDataCenterNICThroughputThreshold
    }
    STATUS current
    DESCRIPTION
        "The datapath NIC throughput for vmwNsxTDataCenterEdgeNICName on Edge node
        vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterNICThroughput% which
        is below the very high threshold value of vmwNsxTDataCenterNICThroughputThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 52 }

vmwNsxTEdgeHealthEdgeDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Edge node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is at or above
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Examine the partition with high usage and see if there are any
        unexpected large files that can be removed.
        "
    ::= { vmwNsxTEdgeHealthFeature 9 }

vmwNsxTEdgeHealthEdgeDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Edge node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 10 }

vmwNsxTEdgeHealthEdgeDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Edge node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is at or above
        the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Examine the partition with high usage and see if there are any
        unexpected large files that can be removed.
        "
    ::= { vmwNsxTEdgeHealthFeature 11 }

vmwNsxTEdgeHealthEdgeDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Edge node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is below the
        very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 12 }

vmwNsxTEdgeHealthEdgeGlobalARPTableUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDatapathResourceUsage
    }
    STATUS current
    DESCRIPTION
        "Global ARP table usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterDatapathResourceUsage% which is above the high threshold
        for over two minutes.

        Action required:
        Log in as the root user and invoke the command
        `edge-appctl -t /var/run/vmware/edge/dpd.ctl neigh/show` and check
        if neigh cache usage is normal. If it is normal, invoke the command
        `edge-appctl -t /var/run/vmware/edge/dpd.ctl neigh/set_param max_entries`
        to increase the ARP table size.
        "
    ::= { vmwNsxTEdgeHealthFeature 27 }

vmwNsxTEdgeHealthEdgeGlobalARPTableUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Global arp table usage on Edge node vmwNsxTDataCenterEntityId has reached
        below the high threshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 28 }

vmwNsxTEdgeHealthEdgeMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this Edge
        node. Consider adjusting the Edge appliance form factor size or rebalancing
        services to other Edge nodes for the applicable workload.
        "
    ::= { vmwNsxTEdgeHealthFeature 5 }

vmwNsxTEdgeHealthEdgeMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 6 }

vmwNsxTEdgeHealthEdgeMemoryUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the very
        high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this Edge
        node. Consider adjusting the Edge appliance form factor size or rebalancing
        services to other Edge nodes for the applicable workload.
        "
    ::= { vmwNsxTEdgeHealthFeature 7 }

vmwNsxTEdgeHealthEdgeMemoryUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Edge node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 8 }

vmwNsxTEdgeHealthEdgeNICLinkStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName
    }
    STATUS current
    DESCRIPTION
        "Edge node NIC vmwNsxTDataCenterEdgeNICName link is down.

        Action required:
        On the Edge node confirm if the NIC link is physically down by invoking
        the NSX CLI command `get interfaces`. If it is down, verify the cable
        connection.
        "
    ::= { vmwNsxTEdgeHealthFeature 29 }

vmwNsxTEdgeHealthEdgeNICLinkStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName
    }
    STATUS current
    DESCRIPTION
        "Edge node NIC vmwNsxTDataCenterEdgeNICName link is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 30 }

vmwNsxTEdgeHealthEdgeNICOutOfReceiveBuffer NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName,
        vmwNsxTDataCenterRxRingBufferOverflowPercentage,
        vmwNsxTDataCenterRxMisses,
        vmwNsxTDataCenterRxProcessed
    }
    STATUS current
    DESCRIPTION
        "Edge NIC vmwNsxTDataCenterEdgeNICName receive ring buffer has overflowed by
        vmwNsxTDataCenterRxRingBufferOverflowPercentage% on Edge node vmwNsxTDataCenterEntityId.
        The missed packet count is vmwNsxTDataCenterRxMisses and processed packet count
        is vmwNsxTDataCenterRxProcessed.

        Action required:
        Invoke the NSX CLI command `get dataplane cpu stats`, and check
        1. If cpu usage is high, i.e., > 90%, then take a packet capture on
           the interface using the command `start capture interface
           <interface-name> direction input` or `start capture interface
           <interface-name> direction input core <core-id>` (to capture
           packets ingressing on specific core whose usage is high).
           Then analyze the capture to see if there are majority of
           fragmented packets or ipsec packets. If yes, then it is expected
           behavior. If not probably datapath is stuck processing other bookkeeping
           operations and if alarm lasts or more than 2-3 min, contact VMware Support.
        2. If cpu usage is not high, i.e., < 90%, then check if rx pps is high
           using the command `get dataplane cpu stats` (just to make sure the
           traffic rate is increasing). Then increase the ring size by 1024
           using the command `set dataplane ring-size rx <ring-size>`.
           NOTE - The continuous increase of ring size by 1024 factor can lead
           to some performance issue due to cache line thrashing internally.
           If even after increasing the ring size, the issue persists then
           it is an indication that edge needs a larger form factor deployment
           to accommodate the traffic.
        3. If the alarm keeps on flapping i.e., triggers and resolves very soon,
           then it is due to bursty traffic. In this case check if rx pps as
           described above, if it is not high during the alarm active period
           then contact VMware Support. If pps is high it confirms bursty traffic.
           Consider suppressing the alarm.
           NOTE - There is no specific benchmark to decide what is regarded as a
           high pps value. It depends on infrastructure and type of traffic.
           The comparison can be made by noting down when alarm is inactive and
           when it is active.
        "
    ::= { vmwNsxTEdgeHealthFeature 31 }

vmwNsxTEdgeHealthEdgeNICOutOfReceiveBufferClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName
    }
    STATUS current
    DESCRIPTION
        "Edge NIC vmwNsxTDataCenterEdgeNICName receive ring buffer usage on Edge node
        vmwNsxTDataCenterEntityId is no longer overflowing.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 32 }

vmwNsxTEdgeHealthEdgeNICOutOfTransmitBuffer NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName,
        vmwNsxTDataCenterTxRingBufferOverflowPercentage,
        vmwNsxTDataCenterTxMisses,
        vmwNsxTDataCenterTxProcessed
    }
    STATUS current
    DESCRIPTION
        "Edge NIC vmwNsxTDataCenterEdgeNICName transmit ring buffer has overflowed by
        vmwNsxTDataCenterTxRingBufferOverflowPercentage% on Edge node vmwNsxTDataCenterEntityId.
        The missed packet count is vmwNsxTDataCenterTxMisses and processed packet count
        is vmwNsxTDataCenterTxProcessed.

        Action required:
        1. If a lot of VMs are accommodated along with edge by the hypervisor
           then edge VM might not get time to run, hence the packets might
           not be retrieved by hypervisor. Then probably migrating the edge VM
           to a host with fewer VMs.
        2. Increase the ring size by 1024 using the command `set dataplane
           ring-size tx <ring-size>`. If even after increasing the ring size,
           the issue persists then contact VMware Support as the ESX side
           transmit ring buffer might be of lower value. If there is no
           issue on ESX side, it indicates the edge needs to be scaled to
           a larger form factor deployment to accommodate the traffic.
        3. If the alarm keeps on flapping, i.e., triggers and resolves very soon,
           then it is due to bursty traffic. In this case check if tx pps using
           the command `get dataplane cpu stats`. If it is not high during the
           alarm active period then contact VMware Support.
           If pps is high it confirms bursty traffic. Consider suppressing the
           alarm.
           NOTE - There is no specific benchmark to decide what is regarded as a
           high pps value. It depends on infrastructure and type of traffic.
           The comparison can be made by noting down when alarm is inactive and
           when it is active.
        "
    ::= { vmwNsxTEdgeHealthFeature 33 }

vmwNsxTEdgeHealthEdgeNICOutOfTransmitBufferClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeNICName
    }
    STATUS current
    DESCRIPTION
        "Edge NIC vmwNsxTDataCenterEdgeNICName transmit ring buffer usage on Edge node
        vmwNsxTDataCenterEntityId is no longer overflowing.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 34 }

vmwNsxTEdgeHealthFailureDomainDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeId
    }
    STATUS current
    DESCRIPTION
        "All members of failure domain vmwNsxTDataCenterTransportNodeId are down.

        Action required:
        1. On the Edge node identified by vmwNsxTDataCenterTransportNodeId, check the connectivity
        to the management and control planes by invoking the NSX CLI command
        `get managers` and `get controllers`.
        2. Invoke the NSX CLI command `get interface eth0` to check the management
        interface status.
        3. Invoke the CLI `get services` to check the core services status like
        dataplane/local-controller/nestdb/router, etc.
        4. Inspect the /var/log/syslog to find the suspecting error.
        5. Reboot the Edge node.
        "
    ::= { vmwNsxTEdgeHealthFeature 53 }

vmwNsxTEdgeHealthFailureDomainDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeId
    }
    STATUS current
    DESCRIPTION
        "All members of failure domain vmwNsxTDataCenterTransportNodeId are reachable.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 54 }

vmwNsxTEdgeHealthStorageError NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName
    }
    STATUS current
    DESCRIPTION
        "The following disk partitions on the Edge node are in
        read-only mode: vmwNsxTDataCenterDiskPartitionName

        Action required:
        Examine the read-only partition to see if reboot resolves the issue
        or the disk needs to be replaced. Contact GSS for more information.
        "
    ::= { vmwNsxTEdgeHealthFeature 37 }

vmwNsxTEdgeHealthStorageErrorClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName
    }
    STATUS current
    DESCRIPTION
        "The following disk partitions on the Edge node have recovered from
        read-only mode: vmwNsxTDataCenterDiskPartitionName

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEdgeHealthFeature 38 }

-- **********************************************************
-- EndpointProtection feature event notifications
-- **********************************************************

vmwNsxTEndpointProtectionFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the EndpointProtection feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 13 }

vmwNsxTEndpointProtectionFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for EndpointProtection feature notifications.
        "
    ::= { vmwNsxTEndpointProtectionFeaturePrefix 0 }

vmwNsxTEndpointProtectionEAMStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "ESX Agent Manager (EAM) service on compute manager vmwNsxTDataCenterEntityId is down.

        Action required:
        Start the ESX Agent Manager (EAM) service. SSH into vCenter and invoke
        the command `service vmware-eam start`.
        "
    ::= { vmwNsxTEndpointProtectionFeature 1 }

vmwNsxTEndpointProtectionEAMStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "ESX Agent Manager (EAM) service on compute manager vmwNsxTDataCenterEntityId is either up
        or compute manager vmwNsxTDataCenterEntityId has been removed.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEndpointProtectionFeature 2 }

vmwNsxTEndpointProtectionPartnerChannelDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The connection between host module and Partner SVM vmwNsxTDataCenterEntityId
        is down.

        Action required:
        Refer to https://kb.vmware.com/s/article/85844 and make sure
        that Partner SVM vmwNsxTDataCenterEntityId is re-connected to the host module.
        "
    ::= { vmwNsxTEndpointProtectionFeature 3 }

vmwNsxTEndpointProtectionPartnerChannelDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The connection between host module and Partner SVM vmwNsxTDataCenterEntityId
        is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTEndpointProtectionFeature 4 }

-- **********************************************************
-- Federation feature event notifications
-- **********************************************************

vmwNsxTFederationFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Federation feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 32 }

vmwNsxTFederationFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Federation feature notifications.
        "
    ::= { vmwNsxTFederationFeaturePrefix 0 }

vmwNsxTFederationGMToGMLatencyWarning NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFromGMPath,
        vmwNsxTDataCenterToGMPath,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteId
    }
    STATUS current
    DESCRIPTION
        "Latency is higher than expected between Global Managers vmwNsxTDataCenterFromGMPath and vmwNsxTDataCenterToGMPath.

        Action required:
        Check the connectivity from Global Manager vmwNsxTDataCenterFromGMPath(vmwNsxTDataCenterSiteId) to the Global Manager
        vmwNsxTDataCenterToGMPath(vmwNsxTDataCenterRemoteSiteId) via ping. If they are not pingable, check for flakiness in WAN connectivity.
        "
    ::= { vmwNsxTFederationFeature 17 }

vmwNsxTFederationGMToGMLatencyWarningClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFromGMPath,
        vmwNsxTDataCenterToGMPath
    }
    STATUS current
    DESCRIPTION
        "Latency is below expected levels between Global Managers vmwNsxTDataCenterFromGMPath and vmwNsxTDataCenterToGMPath.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 18 }

vmwNsxTFederationGMToGMSplitBrain NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterActiveGlobalManagers
    }
    STATUS current
    DESCRIPTION
        "Multiple Global Manager nodes are active: vmwNsxTDataCenterActiveGlobalManagers. Only one Global Manager node must be active
        at any time.

        Action required:
        Configure only one Global Manager node as active and all other Global Manager nodes as standby.
        "
    ::= { vmwNsxTFederationFeature 9 }

vmwNsxTFederationGMToGMSplitBrainClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterActiveGlobalManager
    }
    STATUS current
    DESCRIPTION
        "Global Manager node vmwNsxTDataCenterActiveGlobalManager is the only active Global Manager node now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 10 }

vmwNsxTFederationGMToGMSynchronizationError NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFromGMPath,
        vmwNsxTDataCenterToGMPath,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteId
    }
    STATUS current
    DESCRIPTION
        "Active Global Manager vmwNsxTDataCenterFromGMPath to Standby Global Manager vmwNsxTDataCenterToGMPath cannot
         synchronize for more than 5 minutes.

        Action required:
        Check the connectivity from Global Manager vmwNsxTDataCenterFromGMPath(vmwNsxTDataCenterSiteId) to the Global Manager
        vmwNsxTDataCenterToGMPath(vmwNsxTDataCenterRemoteSiteId) via ping.
        "
    ::= { vmwNsxTFederationFeature 19 }

vmwNsxTFederationGMToGMSynchronizationErrorClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFromGMPath,
        vmwNsxTDataCenterToGMPath
    }
    STATUS current
    DESCRIPTION
        "Synchronization from active Global Manager vmwNsxTDataCenterFromGMPath to standby vmwNsxTDataCenterToGMPath is healthy.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 20 }

vmwNsxTFederationGMToGMSynchronizationWarning NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFromGMPath,
        vmwNsxTDataCenterToGMPath,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteId
    }
    STATUS current
    DESCRIPTION
        "Active Global Manager vmwNsxTDataCenterFromGMPath to Standby Global Manager vmwNsxTDataCenterToGMPath can not synchronize.

        Action required:
        Check the connectivity from Global Manager vmwNsxTDataCenterFromGMPath(vmwNsxTDataCenterSiteId) to the Global Manager
        vmwNsxTDataCenterToGMPath(vmwNsxTDataCenterRemoteSiteId) via ping.
        "
    ::= { vmwNsxTFederationFeature 21 }

vmwNsxTFederationGMToGMSynchronizationWarningClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFromGMPath,
        vmwNsxTDataCenterToGMPath
    }
    STATUS current
    DESCRIPTION
        "Synchronization from active Global Manager vmwNsxTDataCenterFromGMPath to standby vmwNsxTDataCenterToGMPath is healthy.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 22 }

vmwNsxTFederationGMToLMLatencyWarning NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterLatencyValue,
        vmwNsxTDataCenterLatencyThreshold
    }
    STATUS current
    DESCRIPTION
        "Latency between sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) has reached
        vmwNsxTDataCenterLatencyValue which is above the threshold value of vmwNsxTDataCenterLatencyThreshold.

        Action required:
        1. Check the network connectivity between remote site and local site via ping.
        2. Ensure port TCP/1236 traffic is allowed between the local and remote sites.
        3. Check /var/log/async-replicator/ar.log to see if there are errors reported.
        "
    ::= { vmwNsxTFederationFeature 27 }

vmwNsxTFederationGMToLMLatencyWarningClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterLatencyValue,
        vmwNsxTDataCenterLatencyThreshold
    }
    STATUS current
    DESCRIPTION
        "Latency between sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) has reached
        vmwNsxTDataCenterLatencyValue which below the threshold value of vmwNsxTDataCenterLatencyThreshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 28 }

vmwNsxTFederationGMToLMSynchronizationError NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterFlowIdentifier,
        vmwNsxTDataCenterSyncIssueReason
    }
    STATUS current
    DESCRIPTION
        "Data synchronization between sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId)
        failed for the vmwNsxTDataCenterFlowIdentifier for an extended period. Reason: vmwNsxTDataCenterSyncIssueReason.

        Action required:
        1. Check the network connectivity between remote site and local site via ping. 2. Ensure port TCP/1236 traffic is allowed between the local and remote sites. 3. Ensure the async-replicator service is running on both local and remote sites. Invoke the GET /api/v1/node/services/async_replicator/status NSX API or the `get service async_replicator` NSX CLI command to determine if the service is running. If not running, invoke the POST /api/v1/node/services/async_replicator?action=restart NSX API or the `restart service async_replicator` NSX CLI to restart the service. 4. Check /var/log/async-replicator/ar.log to see if there are errors reported. 5. Collect a support bundle and contact the NSX support team.
        "
    ::= { vmwNsxTFederationFeature 23 }

vmwNsxTFederationGMToLMSynchronizationErrorClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterFlowIdentifier
    }
    STATUS current
    DESCRIPTION
        "Sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) are now synchronized for vmwNsxTDataCenterFlowIdentifier.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 24 }

vmwNsxTFederationGMToLMSynchronizationWarning NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterFlowIdentifier,
        vmwNsxTDataCenterSyncIssueReason
    }
    STATUS current
    DESCRIPTION
        "Data synchronization between sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) failed for the
        vmwNsxTDataCenterFlowIdentifier. Reason: vmwNsxTDataCenterSyncIssueReason

        Action required:
        1. Check the network connectivity between remote site and local site via ping. 2. Ensure port TCP/1236 traffic is allowed between the local and remote sites. 3. Ensure the async-replicator service is running on both local and remote sites. Invoke the GET /api/v1/node/services/async_replicator/status NSX API or the `get service async_replicator` NSX CLI command to determine if the service is running. If not running, invoke the POST /api/v1/node/services/async_replicator?action=restart NSX API or the `restart service async_replicator` NSX CLI to restart the service. 4. Check /var/log/async-replicator/ar.log to see if there are errors reported.
        "
    ::= { vmwNsxTFederationFeature 25 }

vmwNsxTFederationGMToLMSynchronizationWarningClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterFlowIdentifier
    }
    STATUS current
    DESCRIPTION
        "Sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) are now synchronized for vmwNsxTDataCenterFlowIdentifier.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 26 }

vmwNsxTFederationLMRestoreWhileConfigImportInProgress NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId
    }
    STATUS current
    DESCRIPTION
        "Config import from site vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) is in progress. However site vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) is restored
        from backup by the administrator leaving it in an inconsistent state.

        Action required:
        1. Log in to NSX Global Manager appliance CLI.
        2. Switch to root.
        3. Invoke the NSX API DELETE http://localhost:64440/gm/api/v1/infra/sites/<site-name>/onboarding/status in local mode,
           this will delete site on-boarding status for Global Manager.
        4. Re-initiate config on-boarding again.
        "
    ::= { vmwNsxTFederationFeature 31 }

vmwNsxTFederationLMRestoreWhileConfigImportInProgressClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId
    }
    STATUS current
    DESCRIPTION
        "Config inconsistency at site vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) is resolved.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 32 }

vmwNsxTFederationLmToLmSynchronizationError NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId
    }
    STATUS current
    DESCRIPTION
        "The synchronization between vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId)
        failed for more than 5 minutes.

        Action required:
        1. Invoke the NSX CLI command `get site-replicator remote-sites` to get connection
           state between the remote locations. If a remote location is connected but not synchronized,
           it is possible that the location is still in the process of master resolution. In
           this case, wait for around 10 seconds and try invoking the CLI again to
           check for the state of the remote location. If a location is disconnected, try the next
           step.
        2. Check the connectivity from Local Manager (LM) in location vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) to the LMs
           in location vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) via ping. If they are not pingable,
           check for flakiness in WAN connectivity. If there are no physical network
           connectivity issues, try the next step.
        3. Check the /var/log/cloudnet/nsx-ccp.log file on the Manager nodes in the local cluster in
           location vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) that triggered the alarm to see if there are any cross-site
           communication errors. In addition, also look for errors being logged by the
           nsx-appl-proxy subcomponent within /var/log/syslog.
        "
    ::= { vmwNsxTFederationFeature 3 }

vmwNsxTFederationLmToLmSynchronizationErrorClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId
    }
    STATUS current
    DESCRIPTION
        "Remote sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) are now synchronized.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 4 }

vmwNsxTFederationLmToLmSynchronizationWarning NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId
    }
    STATUS current
    DESCRIPTION
        "The synchronization between vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) failed.

        Action required:
        1. Invoke the NSX CLI command `get site-replicator remote-sites` to get connection
           state between the remote locations. If a remote location is connected but not synchronized,
           it is possible that the location is still in the process of master resolution. In
           this case, wait for around 10 seconds and try invoking the CLI again to
           check for the state of the remote location. If a location is disconnected, try the next
           step.
        2. Check the connectivity from Local Manager (LM) in location vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) to the LMs
           in location vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) via ping. If they are not pingable,
           check for flakiness in WAN connectivity. If there are no physical network
           connectivity issues, try the next step.
        3. Check the /var/log/cloudnet/nsx-ccp.log file on the Manager nodes in the local cluster in
           location vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) that triggered the alarm to see if there are any cross-site
           communication errors. In addition, also look for errors being logged by the
           nsx-appl-proxy subcomponent within /var/log/syslog.
        "
    ::= { vmwNsxTFederationFeature 5 }

vmwNsxTFederationLmToLmSynchronizationWarningClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId
    }
    STATUS current
    DESCRIPTION
        "Remote locations vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId) are now synchronized.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 6 }

vmwNsxTFederationQueueOccupancyThresholdExceeded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterQueueName,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterQueueSize,
        vmwNsxTDataCenterQueueSizeThreshold
    }
    STATUS current
    DESCRIPTION
        "Queue (vmwNsxTDataCenterQueueName) used for syncing data between sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId)
        has reached size vmwNsxTDataCenterQueueSize which is at or above the maximum threshold of vmwNsxTDataCenterQueueSizeThreshold%.

        Action required:
        Queue size can exceed threshold due to communication issue with remote site or an overloaded system.
        Please check system performance and /var/log/async-replicator/ar.log to see if there are any errors reported.
        "
    ::= { vmwNsxTFederationFeature 29 }

vmwNsxTFederationQueueOccupancyThresholdExceededClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterQueueName,
        vmwNsxTDataCenterSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterQueueSize,
        vmwNsxTDataCenterQueueSizeThreshold
    }
    STATUS current
    DESCRIPTION
        "Queue (vmwNsxTDataCenterQueueName) used for syncing data between sites vmwNsxTDataCenterSiteName(vmwNsxTDataCenterSiteId) and vmwNsxTDataCenterRemoteSiteName(vmwNsxTDataCenterRemoteSiteId)
        has reached size vmwNsxTDataCenterQueueSize which is below the maximum threshold of vmwNsxTDataCenterQueueSizeThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 30 }

vmwNsxTFederationRtepBGPDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterBGPSourceIP,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterBGPNeighborIP,
        vmwNsxTDataCenterFailureReason
    }
    STATUS current
    DESCRIPTION
        "RTEP (Remote Tunnel Endpoint) BGP session from source IP vmwNsxTDataCenterBGPSourceIP
        to remote location vmwNsxTDataCenterRemoteSiteName neighbor IP vmwNsxTDataCenterBGPNeighborIP is down.
        Reason: vmwNsxTDataCenterFailureReason.

        Action required:
        1. Invoke the NSX CLI command `get logical-routers` on the affected edge node.
        2. Switch to REMOTE_TUNNEL_VRF context.
        3. Invoke the NSX CLI command `get bgp neighbor summary` to check the BGP neighbor status.
        4. Alternatively, invoke the NSX API GET /api/v1/transport-nodes/<transport-node-id>/inter-site/bgp/summary
           to get the BGP neighbor status.
        5. Invoke the NSX CLI command `get interfaces` and check if the correct RTEP IP address is assigned
           to the interface with name remote-tunnel-endpoint.
        6. Check if the ping is working successfully between assigned RTEP IP address (vmwNsxTDataCenterBGPSourceIP) and the
           remote location vmwNsxTDataCenterRemoteSiteName neighbor IP vmwNsxTDataCenterBGPNeighborIP.
        7. Check /var/log/syslog for any errors related to BGP.
        8. Invoke the NSX API GET or PUT /api/v1/transport-nodes/<transport-node-id> to get/update remote_tunnel_endpoint
           configuration on the edge node.
           This will update the RTEP IP assigned to the affected edge node.

        If the reason indicates `Edge is not ready`, check why edge is not in good state.
        1. Invoke the NSX CLI command `get edge-cluster status` to check reason why edge might be down.
        2. Invoke the NSX CLI commands `get bfd-config` and `get bfd-sessions` to check if BFD is running well.
        3. Check any edge health related alarms to get more information.
        "
    ::= { vmwNsxTFederationFeature 1 }

vmwNsxTFederationRtepBGPDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterBGPSourceIP,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterBGPNeighborIP
    }
    STATUS current
    DESCRIPTION
        "RTEP (Remote Tunnel Endpoint) BGP session from source IP vmwNsxTDataCenterBGPSourceIP
        to remote location vmwNsxTDataCenterRemoteSiteName neighbor IP vmwNsxTDataCenterBGPNeighborIP is established.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 2 }

vmwNsxTFederationRtepConnectivityLost NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName,
        vmwNsxTDataCenterRemoteSiteName
    }
    STATUS current
    DESCRIPTION
        "Edge node vmwNsxTDataCenterTransportNodeName lost RTEP (Remote Tunnel Endpoint) connectivity
        with remote location vmwNsxTDataCenterRemoteSiteName.

        Action required:
        1. Invoke the NSX CLI command `get logical-routers` on the affected edge node vmwNsxTDataCenterTransportNodeName.
        2. Switch to REMOTE_TUNNEL_VRF context.
        3. Invoke the NSX CLI command `get bgp neighbor summary` to check the BGP neighbor status.
        4. Alternatively, invoke the NSX API GET /api/v1/transport-nodes/<transport-node-id>/inter-site/bgp/summary
           to get the BGP neighbor status.
        5. Invoke the NSX CLI command `get interfaces` and check if the correct RTEP IP address is assigned
           to the interface with name remote-tunnel-endpoint.
        6. Check if the ping is working successfully between assigned RTEP IP address and the RTEP IP addresses
           on the remote location vmwNsxTDataCenterRemoteSiteName.
        7. Check /var/log/syslog for any errors related to BGP.
        8. Invoke the NSX API GET or PUT /api/v1/transport-nodes/<transport-node-id> to get/update remote_tunnel_endpoint
           configuration on the edge node.
           This will update the RTEP IP assigned to the affected edge node vmwNsxTDataCenterTransportNodeName.
        "
    ::= { vmwNsxTFederationFeature 7 }

vmwNsxTFederationRtepConnectivityLostClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeName,
        vmwNsxTDataCenterRemoteSiteName
    }
    STATUS current
    DESCRIPTION
        "Edge node vmwNsxTDataCenterTransportNodeName has restored RTEP (Remote Tunnel Endpoint) connectivity
        with remote location vmwNsxTDataCenterRemoteSiteName.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTFederationFeature 8 }

-- **********************************************************
-- GatewayFirewall feature event notifications
-- **********************************************************

vmwNsxTGatewayFirewallFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the GatewayFirewall feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 39 }

vmwNsxTGatewayFirewallFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for GatewayFirewall feature notifications.
        "
    ::= { vmwNsxTGatewayFirewallFeaturePrefix 0 }

vmwNsxTGatewayFirewallICMPFlowCountExceeded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallICMPFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for ICMP traffic on logical
        router vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterFirewallICMPFlowUsage% which is at or above the
        high threshold  value of vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usage for ICMP flows.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 21 }

vmwNsxTGatewayFirewallICMPFlowCountExceededClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage on logical router vmwNsxTDataCenterEntityId has reached
        below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 22 }

vmwNsxTGatewayFirewallICMPFlowCountHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallICMPFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for ICMP on logical router vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterFirewallICMPFlowUsage% which is at or above the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usege for ICMP flows.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 23 }

vmwNsxTGatewayFirewallICMPFlowCountHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for ICMP on logical router
        vmwNsxTDataCenterEntityId has reached below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 24 }

vmwNsxTGatewayFirewallIPFlowCountExceeded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallIPFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for IP traffic on logical router vmwNsxTDataCenterEntityId has
        reached vmwNsxTDataCenterFirewallIPFlowUsage% which is at or above the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usage for IP flows.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 25 }

vmwNsxTGatewayFirewallIPFlowCountExceededClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage on logical router vmwNsxTDataCenterEntityId has reached
        below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 26 }

vmwNsxTGatewayFirewallIPFlowCountHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallIPFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for IP on logical router vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterFirewallIPFlowUsage% which is at or above the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usege for IP flows.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 27 }

vmwNsxTGatewayFirewallIPFlowCountHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for non IP flows on logical router
        vmwNsxTDataCenterEntityId has reached below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 28 }

vmwNsxTGatewayFirewallTcpHalfOpenFlowCountExceeded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallHalfopenFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for TCP half-open traffic on logical
        router vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterFirewallHalfopenFlowUsage% which is at or above the
        high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usage for TCP half-open flows.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 29 }

vmwNsxTGatewayFirewallTcpHalfOpenFlowCountExceededClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage on logical router vmwNsxTDataCenterEntityId has reached
        below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 30 }

vmwNsxTGatewayFirewallTcpHalfOpenFlowCountHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallHalfopenFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for TCP on logical router vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterFirewallHalfopenFlowUsage% which is at or above the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usege for TCP half-open flow.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 31 }

vmwNsxTGatewayFirewallTcpHalfOpenFlowCountHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for TCP half-open on logical router
        vmwNsxTDataCenterEntityId has reached below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 32 }

vmwNsxTGatewayFirewallUDPFlowCountExceeded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallUDPFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for UDP traffic on logical
        router vmwNsxTDataCenterEntityId has reached vmwNsxTDataCenterFirewallUDPFlowUsage% which is at or above the
        high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usage for UDP flows.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 33 }

vmwNsxTGatewayFirewallUDPFlowCountExceededClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage on logical router vmwNsxTDataCenterEntityId has reached
        below the high threshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 34 }

vmwNsxTGatewayFirewallUDPFlowCountHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterFirewallUDPFlowUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for UDP on logical router vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterFirewallUDPFlowUsage% which is at or above the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.
        New flows will be dropped by Gateway firewall when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> interface stats  | json` by using
        right interface uuid and check flow table usege for UDP flows.
        Check traffic flows going through the gateway is not a DOS attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider increasing the alarm threshold or route new traffic to another Edge node.
        "
    ::= { vmwNsxTGatewayFirewallFeature 35 }

vmwNsxTGatewayFirewallUDPFlowCountHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Gateway firewall flow table usage for UDP on logical router
        vmwNsxTDataCenterEntityId has reached below the high threshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTGatewayFirewallFeature 36 }

-- **********************************************************
-- HighAvailability feature event notifications
-- **********************************************************

vmwNsxTHighAvailabilityFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the HighAvailability feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 21 }

vmwNsxTHighAvailabilityFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for HighAvailability feature notifications.
        "
    ::= { vmwNsxTHighAvailabilityFeaturePrefix 0 }

vmwNsxTHighAvailabilityTier0GatewayFailover NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterPreviousGatewayState,
        vmwNsxTDataCenterCurrentGatewayState,
        vmwNsxTDataCenterServiceRouterId
    }
    STATUS current
    DESCRIPTION
        "The tier0 gateway vmwNsxTDataCenterEntityId failover from vmwNsxTDataCenterPreviousGatewayState
        to vmwNsxTDataCenterCurrentGatewayState, service-router vmwNsxTDataCenterServiceRouterId.

        Action required:
        Invoke the NSX CLI command `get logical-router <service_router_id>` to
        identify the tier0 service-router vrf ID. Switch to the vrf context by
        invoking `vrf <vrf-id>` then invoke `get high-availability status`
        to determine the service that is down.
        "
    ::= { vmwNsxTHighAvailabilityFeature 9 }

vmwNsxTHighAvailabilityTier0GatewayFailoverClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The tier0 gateway vmwNsxTDataCenterEntityId is now up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTHighAvailabilityFeature 10 }

vmwNsxTHighAvailabilityTier1GatewayFailover NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterPreviousGatewayState,
        vmwNsxTDataCenterCurrentGatewayState,
        vmwNsxTDataCenterServiceRouterId
    }
    STATUS current
    DESCRIPTION
        "The tier1 gateway vmwNsxTDataCenterEntityId failover from vmwNsxTDataCenterPreviousGatewayState
        to vmwNsxTDataCenterCurrentGatewayState, service-router vmwNsxTDataCenterServiceRouterId.

        Action required:
        Invoke the NSX CLI command `get logical-router <service_router_id>` to
        identify the tier1 service-router vrf ID. Switch to the vrf context by
        invoking `vrf <vrf-id>` then invoke `get high-availability status`
        to determine the service that is down.
        "
    ::= { vmwNsxTHighAvailabilityFeature 11 }

vmwNsxTHighAvailabilityTier1GatewayFailoverClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The tier1 gateway vmwNsxTDataCenterEntityId is now up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTHighAvailabilityFeature 12 }

-- **********************************************************
-- IdentityFirewall feature event notifications
-- **********************************************************

vmwNsxTIdentityFirewallFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the IdentityFirewall feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 36 }

vmwNsxTIdentityFirewallFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for IdentityFirewall feature notifications.
        "
    ::= { vmwNsxTIdentityFirewallFeaturePrefix 0 }

vmwNsxTIdentityFirewallConnectivityToLDAPServerLost NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLDAPServer
    }
    STATUS current
    DESCRIPTION
        "The connectivity to LDAP server vmwNsxTDataCenterLDAPServer is lost.

        Action required:
        Please check
        1. The LDAP server is reachable from NSX nodes.
        2. The LDAP server details are configured correctly in NSX.
        3. The LDAP server is running correctly.
        4. There are no firewalls blocking access between the LDAP server
           and NSX nodes.
        Afer the issue is fixed, use TEST CONNECTION in NSX UI under
        Identity Firewall AD to test the connection.
        "
    ::= { vmwNsxTIdentityFirewallFeature 1 }

vmwNsxTIdentityFirewallConnectivityToLDAPServerLostClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLDAPServer
    }
    STATUS current
    DESCRIPTION
        "The connectivity to LDAP server vmwNsxTDataCenterLDAPServer is restored.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIdentityFirewallFeature 2 }

vmwNsxTIdentityFirewallErrorInDeltaSync NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDirectoryDomain
    }
    STATUS current
    DESCRIPTION
        "Errors occurred while performing delta sync with vmwNsxTDataCenterDirectoryDomain.

        Action required:
        1. Check if there are any connectivity to LDAP server lost alarms.
        2. Find the error details in /var/log/syslog. Around the alarm trigger
           time, search for text: Error happened when synchronize LDAP objects.
        3. Check with AD administrator if there are any recent AD changes which
           may cause the errors.
        4. If the errors persist, collect the technical support bundle and
           contact VMware technical support.
        "
    ::= { vmwNsxTIdentityFirewallFeature 3 }

vmwNsxTIdentityFirewallErrorInDeltaSyncClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDirectoryDomain
    }
    STATUS current
    DESCRIPTION
        "No errors occurred while performing delta sync with vmwNsxTDataCenterDirectoryDomain.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIdentityFirewallFeature 4 }

-- **********************************************************
-- InfrastructureCommunication feature event notifications
-- **********************************************************

vmwNsxTInfrastructureCommunicationFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the InfrastructureCommunication feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 7 }

vmwNsxTInfrastructureCommunicationFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for InfrastructureCommunication feature notifications.
        "
    ::= { vmwNsxTInfrastructureCommunicationFeaturePrefix 0 }

vmwNsxTInfrastructureCommunicationEdgeTunnelsDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The overall tunnel status of Edge node vmwNsxTDataCenterEntityId is down.

        Action required:
        Invoke the NSX CLI command `get tunnel-ports` to get all tunnel ports,
        then check each tunnel's stats by invoking NSX CLI command
        `get tunnel-port <UUID> stats` to check if there are any drops. Also
        check /var/log/syslog if there are tunnel related errors.
        "
    ::= { vmwNsxTInfrastructureCommunicationFeature 17 }

vmwNsxTInfrastructureCommunicationEdgeTunnelsDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The tunnels of Edge node vmwNsxTDataCenterEntityId have been restored.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTInfrastructureCommunicationFeature 18 }

-- **********************************************************
-- InfrastructureService feature event notifications
-- **********************************************************

vmwNsxTInfrastructureServiceFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the InfrastructureService feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 19 }

vmwNsxTInfrastructureServiceFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for InfrastructureService feature notifications.
        "
    ::= { vmwNsxTInfrastructureServiceFeaturePrefix 0 }

vmwNsxTInfrastructureServiceEdgeServiceStatusChanged NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeServiceName,
        vmwNsxTDataCenterPreviousServiceState,
        vmwNsxTDataCenterCurrentServiceState,
        vmwNsxTDataCenterServiceDownReason
    }
    STATUS current
    DESCRIPTION
        "The service vmwNsxTDataCenterEdgeServiceName changed from vmwNsxTDataCenterPreviousServiceState
        to vmwNsxTDataCenterCurrentServiceState.
        vmwNsxTDataCenterServiceDownReason

        Action required:
        On the Edge node, verify the service hasn't exited due to an error by
        looking for core files in the /var/log/core directory. In addition,
        invoke the NSX CLI command `get services` to confirm whether the service
        is stopped. If so, invoke `start service <service-name>` to restart the
        service.
        "
    ::= { vmwNsxTInfrastructureServiceFeature 1 }

vmwNsxTInfrastructureServiceEdgeServiceStatusChangedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeServiceName,
        vmwNsxTDataCenterPreviousServiceState,
        vmwNsxTDataCenterCurrentServiceState
    }
    STATUS current
    DESCRIPTION
        "The service vmwNsxTDataCenterEdgeServiceName changed from vmwNsxTDataCenterPreviousServiceState
        to vmwNsxTDataCenterCurrentServiceState.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTInfrastructureServiceFeature 2 }

vmwNsxTInfrastructureServiceEdgeServiceStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeServiceName,
        vmwNsxTDataCenterServiceDownReason
    }
    STATUS current
    DESCRIPTION
        "The service vmwNsxTDataCenterEdgeServiceName is down for at least one minute.
        vmwNsxTDataCenterServiceDownReason

        Action required:
        On the Edge node, verify the service hasn't exited due to an error by
        looking for core files in the /var/log/core directory. In addition,
        invoke the NSX CLI command `get services` to confirm whether the service
        is stopped. If so, invoke `start service <service-name>` to restart the
        service.
        "
    ::= { vmwNsxTInfrastructureServiceFeature 3 }

vmwNsxTInfrastructureServiceEdgeServiceStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEdgeServiceName
    }
    STATUS current
    DESCRIPTION
        "The service vmwNsxTDataCenterEdgeServiceName is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTInfrastructureServiceFeature 4 }

vmwNsxTInfrastructureServiceServiceStatusUnknown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterServiceName
    }
    STATUS current
    DESCRIPTION
        "The service vmwNsxTDataCenterServiceName has been unresponsive for 10 seconds.

        Action required:
        Verify vmwNsxTDataCenterServiceName service is still running by invoking `/etc/init.d/vmwNsxTDataCenterServiceName status`.
        If the service is reported as running, it may need to get restarted which can be done by
        `/etc/init.d/vmwNsxTDataCenterServiceName restart`. Rerun the status command to verify the service is now
        running. If the script `/etc/init.d/vmwNsxTDataCenterServiceName` is unavailable, please try invoking
        `systemctl vmwNsxTDataCenterServiceName status` and restart by `systemctl vmwNsxTDataCenterServiceName restart` with root
        priviledges. If restarting the service does not resolve the issue or if the issue reoccurs after
        a successful restart, please contact VMware Support.
        "
    ::= { vmwNsxTInfrastructureServiceFeature 7 }

vmwNsxTInfrastructureServiceServiceStatusUnknownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterServiceName
    }
    STATUS current
    DESCRIPTION
        "The service vmwNsxTDataCenterServiceName is responsive again.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTInfrastructureServiceFeature 8 }

-- **********************************************************
-- IntelligenceCommunication feature event notifications
-- **********************************************************

vmwNsxTIntelligenceCommunicationFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the IntelligenceCommunication feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 9 }

vmwNsxTIntelligenceCommunicationFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for IntelligenceCommunication feature notifications.
        "
    ::= { vmwNsxTIntelligenceCommunicationFeaturePrefix 0 }

vmwNsxTIntelligenceCommunicationTNFlowExporterDisconnected NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The flow exporter on Transport node vmwNsxTDataCenterEntityId is disconnected from
        the Intelligence node's messaging broker. Data collection is affected.

        Action required:
        Restart the messaging service if it is not running in the Intelligence
        node. Resolve the network connection failure between the Transport node
        flow exporter and the Intelligence node.
        "
    ::= { vmwNsxTIntelligenceCommunicationFeature 7 }

vmwNsxTIntelligenceCommunicationTNFlowExporterDisconnectedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The flow exporter on Transport node vmwNsxTDataCenterEntityId has reconnected to
        the Intelligence node's messaging broker.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceCommunicationFeature 8 }

-- **********************************************************
-- IntelligenceHealth feature event notifications
-- **********************************************************

vmwNsxTIntelligenceHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the IntelligenceHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 6 }

vmwNsxTIntelligenceHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for IntelligenceHealth feature notifications.
        "
    ::= { vmwNsxTIntelligenceHealthFeaturePrefix 0 }

vmwNsxTIntelligenceHealthCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Use the top command to check which processes have the most CPU usages, and
        then check /var/log/syslog and these processes' local logs to see if there
        are any outstanding errors to be resolved.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 41 }

vmwNsxTIntelligenceHealthCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 42 }

vmwNsxTIntelligenceHealthCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Use the top command to check which processes have the most CPU usages, and
        then check /var/log/syslog and these processes' local logs to see if there
        are any outstanding errors to be resolved.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 43 }

vmwNsxTIntelligenceHealthCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 44 }

vmwNsxTIntelligenceHealthDataDiskPartitionUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition /data on Intelligence node
        vmwNsxTDataCenterIntelligenceNodeId is above the high threshold value
        of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Stop NSX intelligence data collection until the disk usage is below
        the threshold. Examine disk partition /data and see if there are any
        unexpected large files that can be removed.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 45 }

vmwNsxTIntelligenceHealthDataDiskPartitionUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition /data on Intelligence node
        vmwNsxTDataCenterIntelligenceNodeId is below the high threshold value
        of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 46 }

vmwNsxTIntelligenceHealthDataDiskPartitionUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition /data on Intelligence node
        vmwNsxTDataCenterIntelligenceNodeId is above the very high threshold value
        of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Stop NSX intelligence data collection until the disk usage is below
        the threshold. In the NSX UI, navigate to System | Appliances | NSX
        Intelligence Appliance. Then click ACTONS, Stop Collecting Data.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 47 }

vmwNsxTIntelligenceHealthDataDiskPartitionUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition /data on Intelligence node
        vmwNsxTDataCenterIntelligenceNodeId is below the very high threshold value
        of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 48 }

vmwNsxTIntelligenceHealthDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition vmwNsxTDataCenterDiskPartitionName on
        Intelligence node vmwNsxTDataCenterIntelligenceNodeId is above
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Examine disk partition vmwNsxTDataCenterDiskPartitionName and see if there are any
        unexpected large files that can be removed.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 49 }

vmwNsxTIntelligenceHealthDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition vmwNsxTDataCenterDiskPartitionName on
        Intelligence node vmwNsxTDataCenterIntelligenceNodeId is below
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 50 }

vmwNsxTIntelligenceHealthDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition vmwNsxTDataCenterDiskPartitionName on
        Intelligence node vmwNsxTDataCenterIntelligenceNodeId is above
        the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Examine disk partition vmwNsxTDataCenterDiskPartitionName and see if there are any
        unexpected large files that can be removed.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 51 }

vmwNsxTIntelligenceHealthDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of disk partition vmwNsxTDataCenterDiskPartitionName on
        Intelligence node vmwNsxTDataCenterIntelligenceNodeId is below
        the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 52 }

vmwNsxTIntelligenceHealthMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Use the top command to check which processes have the most memory usages,
        and then check /var/log/syslog and these processes' local logs to see if
        there are any outstanding errors to be resolved.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 53 }

vmwNsxTIntelligenceHealthMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 54 }

vmwNsxTIntelligenceHealthMemoryUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Use the top command to check which processes have the most memory usages,
        and then check /var/log/syslog and these processes' local logs to see if
        there are any outstanding errors to be resolved.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 55 }

vmwNsxTIntelligenceHealthMemoryUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Intelligence node vmwNsxTDataCenterIntelligenceNodeId
        is below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 56 }

vmwNsxTIntelligenceHealthNodeStatusDegraded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterServiceName,
        vmwNsxTDataCenterIntelligenceNodeId
    }
    STATUS current
    DESCRIPTION
        "Intelligence node vmwNsxTDataCenterIntelligenceNodeId is degraded.

        Action required:
        Invoke the NSX API GET /napp/api/v1/platform/monitor/category/health to
        check which specific pod is down and the reason behind it. Invoke the following
        CLI command to restart the degraded service:
        `kubectl rollout restart <statefulset/deployment> <service_name> -n <namespace>`
        "
    ::= { vmwNsxTIntelligenceHealthFeature 57 }

vmwNsxTIntelligenceHealthNodeStatusDegradedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterServiceName,
        vmwNsxTDataCenterIntelligenceNodeId
    }
    STATUS current
    DESCRIPTION
        "Intelligence node vmwNsxTDataCenterIntelligenceNodeId is running properly.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 58 }

vmwNsxTIntelligenceHealthStorageLatencyHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The storage latency of disk partition vmwNsxTDataCenterDiskPartitionName on
        Intelligence node vmwNsxTDataCenterIntelligenceNodeId is above
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold milliseconds.

        Action required:
        Transient high storage latency may happen due to spike of I/O requests.
        If storage latency remains high for more than 30 minutes,
        consider deploying NSX Intelligence appliance in a low latency disk,
        or not sharing the same storage device with other VMs.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 63 }

vmwNsxTIntelligenceHealthStorageLatencyHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The storage latency of disk partition vmwNsxTDataCenterDiskPartitionName on
        Intelligence node vmwNsxTDataCenterIntelligenceNodeId is below
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold milliseconds.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIntelligenceHealthFeature 64 }

-- **********************************************************
-- IPAM feature event notifications
-- **********************************************************

vmwNsxTIPAMFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the IPAM feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 38 }

vmwNsxTIPAMFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for IPAM feature notifications.
        "
    ::= { vmwNsxTIPAMFeaturePrefix 0 }

vmwNsxTIPAMIPBlockUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntentPath
    }
    STATUS current
    DESCRIPTION
        "IP block usage of vmwNsxTDataCenterIntentPath is very high.
        IP block nearing its total capacity, creation of subnet using IP block might fail.

        Action required:
        Please review IP block usage. Use new IP block for resource creation or delete unused IP subnet from the IP block.
        To check subnet being used for IP Block. From NSX UI, navigate to Networking | IP Address pools | IP Address pools tab.
        Select IP pools where IP block being used, check Subnets and Allocated IPs column on UI. If no allocation has been used for the IP pool and it is not going to be used in future then delete subnet or IP pool.
        Use following API to check if IP block being used by IP pool and also check if any IP allocation done:
        To get configured subnets of an IP pool, invoke the NSX API
        GET /policy/api/v1/infra/ip-pools/<ip-pool>/ip-subnets
        To get IP allocations, invoke the NSX API
        GET /policy/api/v1/infra/ip-pools/<ip-pool>/ip-allocations
        Please note: Deletion of IP pool/subnet should only be done if it does not have any allocated IPs and it is not going to be used in future.
        "
    ::= { vmwNsxTIPAMFeature 1 }

vmwNsxTIPAMIPBlockUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntentPath
    }
    STATUS current
    DESCRIPTION
        "IP block usage of vmwNsxTDataCenterIntentPath is below threshold level.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIPAMFeature 2 }

vmwNsxTIPAMIPPoolUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntentPath
    }
    STATUS current
    DESCRIPTION
        "IP pool usage of vmwNsxTDataCenterIntentPath is very high.
        IP pool nearing its total capacity. Creation of entity/service depends on IP being allocated from IP pool might fail.

        Action required:
        Please review IP pool usage.
        Release unused ip allocations from IP pool or create new IP pool and use it.
        From NSX UI navigate to Networking | IP Address pools | IP Address pools tab.
        Select IP pools and check Allocated IPs column, this will show IPs allocated from the IP pool.
        If user see any IPs are not being used then those IPs can be released.
        To release unused IP allocations, invoke the NSX API
        DELETE /policy/api/v1/infra/ip-pools/<ip-pool>/ip-allocations/<ip-allocation>
        "
    ::= { vmwNsxTIPAMFeature 3 }

vmwNsxTIPAMIPPoolUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterIntentPath
    }
    STATUS current
    DESCRIPTION
        "IP pool usage of vmwNsxTDataCenterIntentPath is normal now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTIPAMFeature 4 }

-- **********************************************************
-- Licenses feature event notifications
-- **********************************************************

vmwNsxTLicensesFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Licenses feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 5 }

vmwNsxTLicensesFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Licenses feature notifications.
        "
    ::= { vmwNsxTLicensesFeaturePrefix 0 }

vmwNsxTLicensesLicenseExpired NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLicenseEditionType,
        vmwNsxTDataCenterDisplayedLicenseKey
    }
    STATUS current
    DESCRIPTION
        "The vmwNsxTDataCenterLicenseEditionType license key ending with
        vmwNsxTDataCenterDisplayedLicenseKey, has expired.

        Action required:
        Add a new, non-expired license using the NSX UI by navigating to
        System | Licenses then click ADD and specify the key of the new
        license. The expired license should be deleted by checking the
        checkbox of the license, then click DELETE.
        "
    ::= { vmwNsxTLicensesFeature 1 }

vmwNsxTLicensesLicenseExpiredClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLicenseEditionType,
        vmwNsxTDataCenterDisplayedLicenseKey
    }
    STATUS current
    DESCRIPTION
        "The expired vmwNsxTDataCenterLicenseEditionType license key ending with
        vmwNsxTDataCenterDisplayedLicenseKey, has been removed, updated or is no
        longer about to expire.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLicensesFeature 2 }

vmwNsxTLicensesLicenseIsAboutToExpire NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLicenseEditionType,
        vmwNsxTDataCenterDisplayedLicenseKey
    }
    STATUS current
    DESCRIPTION
        "The vmwNsxTDataCenterLicenseEditionType license key ending with
        vmwNsxTDataCenterDisplayedLicenseKey, is about to expire.

        Action required:
        The license is about to expire in several days. Please plan to add a
        new, non-expiring license using the NSX UI by navigating to System |
        Licenses then click ADD and specify the key of the new license. The
        expired license should be deleted by checking the checkbox of the
        license, then click DELETE.
        "
    ::= { vmwNsxTLicensesFeature 3 }

vmwNsxTLicensesLicenseIsAboutToExpireClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLicenseEditionType,
        vmwNsxTDataCenterDisplayedLicenseKey
    }
    STATUS current
    DESCRIPTION
        "The expiring vmwNsxTDataCenterLicenseEditionType license key ending with
        vmwNsxTDataCenterDisplayedLicenseKey, has been removed, updated or is no
        longer about to expire.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLicensesFeature 4 }

-- **********************************************************
-- LoadBalancer feature event notifications
-- **********************************************************

vmwNsxTLoadBalancerFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the LoadBalancer feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 17 }

vmwNsxTLoadBalancerFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for LoadBalancer feature notifications.
        "
    ::= { vmwNsxTLoadBalancerFeaturePrefix 0 }

vmwNsxTLoadBalancerDLBStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The distributed load balancer service vmwNsxTDataCenterEntityId is down.

        This notification is sent once when the down state is first detected.

        Action required:
        On ESXi host node, invoke the NSX CLI command `get load-balancer
        <lb-uuid> status`.
        If 'Conflict LSP' is reported, please check whether this LSP is attached
        to other load balancer service. Please check whether this conflict is
        acceptable.
        If 'Not Ready LSP' is reported, please check the status of this LSP by
        invoking NSX CLI command `get logical-switch-port status`.
        "
    ::= { vmwNsxTLoadBalancerFeature 17 }

vmwNsxTLoadBalancerDLBStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The distributed load balancer service vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 18 }

vmwNsxTLoadBalancerLBCPUVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of load balancer vmwNsxTDataCenterEntityId is vmwNsxTDataCenterSystemResourceUsage%
        which is higher than the very high threshold of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        If the load balancer CPU utilization is higher than vmwNsxTDataCenterSystemUsageThreshold%,
        the workload is too high for this load balancer. Rescale the load balancer
        service by changing the load balancer size from small to medium or from
        medium to large. If the CPU utilization of this load balancer is still high,
        consider adjusting the Edge appliance form factor size or moving load
        balancer services to other Edge nodes for the applicable workload.
        "
    ::= { vmwNsxTLoadBalancerFeature 1 }

vmwNsxTLoadBalancerLBCPUVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU utilization of load balancer vmwNsxTDataCenterEntityId is vmwNsxTDataCenterSystemResourceUsage%
        which is lower than the very high threshold of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 2 }

vmwNsxTLoadBalancerLBEdgeCapacityInUseHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The usage of load balancer service in Edge node vmwNsxTDataCenterEntityId is high.
        The threshold is vmwNsxTDataCenterSystemUsageThreshold%.

        This notification is sent once when the high usage state is first detected.

        Action required:
        If multiple LB instances have been configurerd in this Edge node, deploy a
        new Edge node and move some LB instances to that new Edge node. If only a
        single LB instance (small/medium/etc) has been configured in an Edge node of
        same size (small/medium/etc), deploy a new Edge of bigger size and move the
        LB instance to that new Edge node.
        "
    ::= { vmwNsxTLoadBalancerFeature 3 }

vmwNsxTLoadBalancerLBEdgeCapacityInUseHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The usage of load balancer service in Edge node vmwNsxTDataCenterEntityId is below
        the threshold of vmwNsxTDataCenterSystemUsageThreshold% for a sufficient number
        of samples over 2 minutes.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 4 }

vmwNsxTLoadBalancerLBPoolMemberCapacityInUseVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The usage of pool members in Edge node vmwNsxTDataCenterEntityId is very high.
        The threshold is vmwNsxTDataCenterSystemUsageThreshold%.

        This notification is sent once when the high usage state is first detected.

        Action required:
        Deploy a new Edge node and move the load balancer service from existing Edge
        nodes to the newly deployed Edge node.
        "
    ::= { vmwNsxTLoadBalancerFeature 5 }

vmwNsxTLoadBalancerLBPoolMemberCapacityInUseVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The usage of pool members in Edge node vmwNsxTDataCenterEntityId is below
        the threshold of vmwNsxTDataCenterSystemUsageThreshold% for a sufficient number
        of samples over 2 minutes.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 6 }

vmwNsxTLoadBalancerLBStatusDegraded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The load balancer service vmwNsxTDataCenterEntityId is degraded.

        This notification is sent once when the degraded state is first detected.

        Action required:
        For centralized load balancer:
        On standby Edge node, check load balancer status by invoking the NSX CLI
        command `get load-balancer <lb-uuid> status`. If the LB-State of load
        balancer service is not_ready or there is no output, make the Edge node
        enter maintenance mode, then exit maintenance mode.
        For distributed load balancer:
        1. Get detailed status by invoking NSX-T API GET
           /policy/api/v1/infra/lb-services/<LBService>/detailed-status?source=realtime
        2. From API output, find ESXi host reporting a non-zero instance_number
           with status NOT_READY or CONFLICT.
        3. On ESXi host node, invoke the NSX CLI command `get load-balancer
           <lb-uuid> status`.
           If 'Conflict LSP'is reported, please check whether this LSP is attached
           to other load balancer service. Please check whether this conflict is
           acceptable.
           If 'Not Ready LSP' is reported, please check the status of this LSP by
           invoking NSX CLI command `get logical-switch-port status`.
        "
    ::= { vmwNsxTLoadBalancerFeature 15 }

vmwNsxTLoadBalancerLBStatusDegradedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The load balancer service vmwNsxTDataCenterEntityId is not degraded.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 16 }

vmwNsxTLoadBalancerLBStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The load balancer service vmwNsxTDataCenterEntityId is down.

        Action required:
        Verify whether the load balancer service in the Edge node is running.
        If the status of load balancer service is not ready, make the Edge
        node enter maintenance mode, then exit maintenance mode. If the status
        of load balancer is still not recovered, please check whether there
        are any error log in /var/log/syslog.
        "
    ::= { vmwNsxTLoadBalancerFeature 7 }

vmwNsxTLoadBalancerLBStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The noad balancer service vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 8 }

vmwNsxTLoadBalancerConfigurationNotRealizedDueToLowMemory NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeId
    }
    STATUS current
    DESCRIPTION
        "The load balancer configuration vmwNsxTDataCenterEntityId is not realized,
        due to high memory usage on Edge node vmwNsxTDataCenterTransportNodeId.

        Action required:
        Prefer defining small and medium sized load balancers over large sized load balancers.
        Spread out load balancer services among the available Edge nodes.
        Reduce number of Virtual Servers defined.
        "
    ::= { vmwNsxTLoadBalancerFeature 19 }

vmwNsxTLoadBalancerConfigurationNotRealizedDueToLowMemoryClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTransportNodeId
    }
    STATUS current
    DESCRIPTION
        "The load balancer configuration vmwNsxTDataCenterEntityId is realized on vmwNsxTDataCenterTransportNodeId.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 20 }

vmwNsxTLoadBalancerPoolStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The load balancer pool vmwNsxTDataCenterEntityId status is down.

        Action required:
        Consult the load balancer pool to determine which members are down by
        invoking the NSX CLI command
        `get load-balancer <lb-uuid> pool <pool-uuid> status`
        or NSX API GET
        /policy/api/v1/infra/lb-services/<lb-service-id>/lb-pools/<lb-pool-id>/detailed-status
        If DOWN or UNKNOWN is reported, please verify the pool member.
        Check network connectivity from the load balancer to the impacted pool members.
        Validate application health of each pool member. Also validate the health
        of each pool member using the configured monitor. When the health of the
        member is established, the pool member status is updated to healthy based
        on the 'Rise Count' configuration in the monitor.
        Remediate the issue by rebooting the pool member or make the Edge node
        enter maintenance mode, then exit maintenance mode.
        "
    ::= { vmwNsxTLoadBalancerFeature 9 }

vmwNsxTLoadBalancerPoolStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The load balancer pool vmwNsxTDataCenterEntityId status is up

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 10 }

vmwNsxTLoadBalancerVirtualServerStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The load balancer virtual server vmwNsxTDataCenterEntityId is down.

        Action required:
        Consult the load balancer pool to determine its status and verify its
        configuration. If incorrectly configured, reconfigure it and remove the
        load balancer pool from the virtual server then re-add it to the
        virtual server again.
        "
    ::= { vmwNsxTLoadBalancerFeature 11 }

vmwNsxTLoadBalancerVirtualServerStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The load balancer virtual server vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTLoadBalancerFeature 12 }

-- **********************************************************
-- ManagerHealth feature event notifications
-- **********************************************************

vmwNsxTManagerHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the ManagerHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 1 }

vmwNsxTManagerHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for ManagerHealth feature notifications.
        "
    ::= { vmwNsxTManagerHealthFeaturePrefix 0 }

vmwNsxTManagerHealthDuplicateIPAddress NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDuplicateIPAddress
    }
    STATUS current
    DESCRIPTION
        "Manager node vmwNsxTDataCenterEntityId IP address vmwNsxTDataCenterDuplicateIPAddress is
        currently being used by another device in the network.

        Action required:
        1. Determine which device is using the Manager's IP address
        and assign the device a new IP address. Note, reconfiguring
        the Manager to use a new IP address is not supported.
        2. Ensure the static IP address pool/DHCP server is configured correctly.
        3. Correct the IP address of the device if it is manually assigned.
        "
    ::= { vmwNsxTManagerHealthFeature 17 }

vmwNsxTManagerHealthDuplicateIPAddressClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDuplicateIPAddress
    }
    STATUS current
    DESCRIPTION
        "The device using the IP address assigned to Manager node
        vmwNsxTDataCenterEntityId appears to no longer be using vmwNsxTDataCenterDuplicateIPAddress.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 18 }

vmwNsxTManagerHealthManagerConfigDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /config has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%. This can be an indication of rising disk
        usage by the NSX Datastore service under the /config/corfu directory.

        Action required:
        Please run the following tool and contact GSS if any issues are reported
        /opt/vmware/tools/support/inspect_checkpoint_issues.py
        "
    ::= { vmwNsxTManagerHealthFeature 13 }

vmwNsxTManagerHealthManagerConfigDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /config has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 14 }

vmwNsxTManagerHealthManagerConfigDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /config has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the very high threshold value
        of vmwNsxTDataCenterSystemUsageThreshold%. This can be an indication of high disk usage
        by the NSX Datastore service under the /config/corfu directory.

        Action required:
        Please run the following tool and contact GSS if any issues are reported
        /opt/vmware/tools/support/inspect_checkpoint_issues.py
        "
    ::= { vmwNsxTManagerHealthFeature 15 }

vmwNsxTManagerHealthManagerConfigDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /config has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the very high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 16 }

vmwNsxTManagerHealthManagerCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this
        Manager node. Consider adjusting the Manager appliance form factor size.
        "
    ::= { vmwNsxTManagerHealthFeature 1 }

vmwNsxTManagerHealthManagerCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 2 }

vmwNsxTManagerHealthManagerCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this
        Manager node. Consider adjusting the Manager appliance form factor size.
        "
    ::= { vmwNsxTManagerHealthFeature 3 }

vmwNsxTManagerHealthManagerCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 4 }

vmwNsxTManagerHealthManagerDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is at or above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Examine the partition with high usage and see if there are any
        unexpected large files that can be removed.
        "
    ::= { vmwNsxTManagerHealthFeature 9 }

vmwNsxTManagerHealthManagerDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 10 }

vmwNsxTManagerHealthManagerDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is at or above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Examine the partition with high usage and see if there are any
        unexpected large files that can be removed.
        "
    ::= { vmwNsxTManagerHealthFeature 11 }

vmwNsxTManagerHealthManagerDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition vmwNsxTDataCenterDiskPartitionName
        has reached vmwNsxTDataCenterSystemResourceUsage% which is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 12 }

vmwNsxTManagerHealthManagerMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this
        Manager node. Consider adjusting the Manager appliance form factor size.
        "
    ::= { vmwNsxTManagerHealthFeature 5 }

vmwNsxTManagerHealthManagerMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 6 }

vmwNsxTManagerHealthManagerMemoryUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the very
        high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Please review the configuration, running services and sizing of this
        Manager node. Consider adjusting the Manager appliance form factor size.
        "
    ::= { vmwNsxTManagerHealthFeature 7 }

vmwNsxTManagerHealthManagerMemoryUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage on Manager node vmwNsxTDataCenterEntityId has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 8 }

vmwNsxTManagerHealthOperationsDbDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /nonconfig has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%. This can be an indication of rising disk
        usage by the NSX Datastore service under the /nonconfig/corfu directory.

        Action required:
        Please run the following tool and contact GSS if any issues are reported
        /opt/vmware/tools/support/inspect_checkpoint_issues.py --nonconfig
        "
    ::= { vmwNsxTManagerHealthFeature 19 }

vmwNsxTManagerHealthOperationsDbDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /nonconfig has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 20 }

vmwNsxTManagerHealthOperationsDbDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /nonconfig has reached
        vmwNsxTDataCenterSystemResourceUsage% which is at or above the very high threshold value
        of vmwNsxTDataCenterSystemUsageThreshold%. This can be an indication of high disk usage
        by the NSX Datastore service under the /nonconfig/corfu directory.

        Action required:
        Please run the following tool and contact GSS if any issues are reported
        /opt/vmware/tools/support/inspect_checkpoint_issues.py --nonconfig
        "
    ::= { vmwNsxTManagerHealthFeature 21 }

vmwNsxTManagerHealthOperationsDbDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage for the Manager node disk partition /nonconfig has reached
        vmwNsxTDataCenterSystemResourceUsage% which is below the very high threshold value of
        vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 22 }

vmwNsxTManagerHealthStorageError NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName
    }
    STATUS current
    DESCRIPTION
        "The following disk partition on the Manager node vmwNsxTDataCenterEntityId is in
        read-only mode: vmwNsxTDataCenterDiskPartitionName

        Action required:
        Examine the read-only partition to see if reboot resolves the issue
        or the disk needs to be replaced. Contact GSS for more information.
        "
    ::= { vmwNsxTManagerHealthFeature 23 }

vmwNsxTManagerHealthStorageErrorClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterDiskPartitionName
    }
    STATUS current
    DESCRIPTION
        "The following disk partition on the Manager node vmwNsxTDataCenterEntityId
        has recovered from read-only mode: vmwNsxTDataCenterDiskPartitionName

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTManagerHealthFeature 24 }

-- **********************************************************
-- MTUCheck feature event notifications
-- **********************************************************

vmwNsxTMTUCheckFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the MTUCheck feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 42 }

vmwNsxTMTUCheckFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for MTUCheck feature notifications.
        "
    ::= { vmwNsxTMTUCheckFeaturePrefix 0 }

vmwNsxTMTUCheckGlobalRouterMTUTooBig NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The global router MTU configuration is bigger than MTU of switches in overlay Transport Zone which connects
        to Tier0 or Tier1. Global router MTU value should be less than all switches MTU value by at least
        a 100 as we require 100 quota for Geneve encapsulation.

        Action required:
        1. Navigate to System | Fabric | Settings | MTU Configuration Check | Inconsistent on the NSX UI to check
        more mismatch details.
        2. Set the bigger MTU value on switches by invoking the NSX API
        PUT /api/v1/host-switch-profiles/<host-switch-profile-id> with mtu in the
        request body, or API PUT /api/v1/global-configs/SwitchingGlobalConfig
        with physical_uplink_mtu in request body.
        3. Or set the smaller MTU value of global router configuration by invoking the NSX API PUT
        /api/v1/global-configs/RoutingGlobalConfig with logical_uplink_mtu in the request body.
        "
    ::= { vmwNsxTMTUCheckFeature 3 }

vmwNsxTMTUCheckGlobalRouterMTUTooBigClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The global router MTU is less than the MTU of overlay Transport Zone now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTMTUCheckFeature 4 }

vmwNsxTMTUCheckMTUMismatchWithinTransportZone NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "MTU configuration mismatch between Transport Nodes (ESXi, KVM and Edge) attached to the same Transport Zone.
        MTU values on all switches attached to the same Transport Zone not being consistent will cause
        connectivity issues.

        Action required:
        1. Navigate to System | Fabric | Settings | MTU Configuration Check | Inconsistent on the NSX UI to check
        more mismatch details.
        2. Set the same MTU value on all switches attached to the same Transport Zone
        by invoking the NSX API PUT /api/v1/host-switch-profiles/<host-switch-profile-id>
        with mtu in the request body, or API PUT /api/v1/global-configs/SwitchingGlobalConfig
        with physical_uplink_mtu in request body.
        "
    ::= { vmwNsxTMTUCheckFeature 1 }

vmwNsxTMTUCheckMTUMismatchWithinTransportZoneClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "All MTU values between Transport Nodes attached to the same Transport Zone are consistent now.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTMTUCheckFeature 2 }

-- **********************************************************
-- NAT feature event notifications
-- **********************************************************

vmwNsxTNATFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the NAT feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 46 }

vmwNsxTNATFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for NAT feature notifications.
        "
    ::= { vmwNsxTNATFeaturePrefix 0 }

vmwNsxTNATSNATPortUsageOnGatewayIsHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSNATIPAddress,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "SNAT ports usage on logical router vmwNsxTDataCenterEntityId for
        SNAT IP vmwNsxTDataCenterSNATIPAddress has reached
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%. New flows will
        not be SNATed when usage reaches the maximum limit.

        Action required:
        Log in as the admin user on Edge node and invoke the NSX CLI command
        `get firewall <LR_INT_UUID> connection state` by using the right interface
        uuid and check various SNAT mappings for the SNAT IP vmwNsxTDataCenterSNATIPAddress.
        Check traffic flows going through the gateway is not a denial-of-service
        attack or anomalous burst. If the traffic appears to be within
        the normal load but the alarm threshold is hit, consider adding more
        SNAT IP addresses to distribute the load or
        route new traffic to another Edge node.
        "
    ::= { vmwNsxTNATFeature 1 }

vmwNsxTNATSNATPortUsageOnGatewayIsHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSNATIPAddress,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "SNAT ports usage on logical router vmwNsxTDataCenterEntityId for SNAT IP vmwNsxTDataCenterSNATIPAddress
        has reached below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNATFeature 2 }

-- **********************************************************
-- NCPHealth feature event notifications
-- **********************************************************

vmwNsxTNCPHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the NCPHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 11 }

vmwNsxTNCPHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for NCPHealth feature notifications.
        "
    ::= { vmwNsxTNCPHealthFeaturePrefix 0 }

vmwNsxTNCPHealthNCPPluginDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Manager Node has detected the NCP is down or unhealthy.

        Action required:
        To find the clusters which are having issues, please use the
        NSX UI and navigate to the Alarms page. The Entity name value for
        this alarm instance identifies the cluster name. Or invoke the
        NSX API GET /api/v1/systemhealth/container-cluster/ncp/status
        to fetch all cluster statuses and determine the name of any clusters
        that report DOWN or UNKNOWN. Then on the NSX UI
        Inventory | Container | Clusters page find the cluster by name and
        click the Nodes tab which lists all Kubernetes and PAS cluster members.
        For Kubernetes cluster:
        1. Check NCP Pod liveness by finding the K8s master node from all the
        cluster members and log onto the master node. Then invoke the kubectl
        command `kubectl get pods --all-namespaces`. If there is an issue with
        the NCP Pod, please use kubectl logs command to check the issue and fix
        the error.
        2. Check the connection between NCP and Kubernetes API server. The
        NSX CLI can be used inside the NCP Pod to check this connection status
        by invoking the following commands from the master VM.
        `kubectl exec -it <NCP-Pod-Name> -n nsx-system bash`
        `nsxcli`
        `get ncp-k8s-api-server status`
        If there is an issue with the connection, please check both the network
        and NCP configurations.
        3. Check the connection between NCP and NSX Manager. The NSX CLI can be
        used inside the NCP Pod to check this connection status by invoking the
        following command from the master VM.
        `kubectl exec -it <NCP-Pod-Name> -n nsx-system bash`
        `nsxcli`
        `get ncp-nsx status`
        If there is an issue with the connection, please check both the network
        and NCP configurations.
        For PAS cluster:
        1. Check the network connections between virtual machines and fix any
        network issues.
        2. Check the status of both nodes and services and fix crashed nodes
        or services. Invoke the command `bosh vms` and `bosh instances -p` to
        check the status of nodes and services.
        "
    ::= { vmwNsxTNCPHealthFeature 3 }

vmwNsxTNCPHealthNCPPluginDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Manager Node has detected the NCP is up or healthy again.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNCPHealthFeature 4 }

-- **********************************************************
-- NodeAgentsHealth feature event notifications
-- **********************************************************

vmwNsxTNodeAgentsHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the NodeAgentsHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 12 }

vmwNsxTNodeAgentsHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for NodeAgentsHealth feature notifications.
        "
    ::= { vmwNsxTNodeAgentsHealthFeaturePrefix 0 }

vmwNsxTNodeAgentsHealthNodeAgentsDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The agents running inside the Node VM appear to be down.

        Action required:
        For ESX:
        1. If Vmk50 is missing, refer to this Knowledge Base article
         https://kb.vmware.com/s/article/67432.
        2. If Hyperbus 4094 is missing, restarting nsx-cfgagent or restarting the
        container host VM may help.
        3. If container host VIF is blocked, check the connection to the Controller
        to make sure all configurations are sent down.
        4. If nsx-cfg-agent has stopped, restart nsx-cfgagent.
        For KVM:
        1. If Hyperbus namespace is missing, restarting the  nsx-opsagent may help
        recreate the namespace.
        2. If Hyperbus interface is missing inside the hyperbus namespace,
        restarting the nsx-opsagent may help.
        3. If nsx-agent has stopped, restart nsx-agent.
        For Both ESX and KVM:
        1. If the node-agent package is missing, check whether node-agent package
        has been successfully installed in the container host vm.
        2. If the interface for node-agent in container host vm is down, check the
        eth1 interface status inside the container host vm.
        "
    ::= { vmwNsxTNodeAgentsHealthFeature 3 }

vmwNsxTNodeAgentsHealthNodeAgentsDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The agents inside the Node VM are running.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNodeAgentsHealthFeature 4 }

-- **********************************************************
-- NSXApplicationPlatformCommunication feature event notifications
-- **********************************************************

vmwNsxTNSXApplicationPlatformCommunicationFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the NSXApplicationPlatformCommunication feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 41 }

vmwNsxTNSXApplicationPlatformCommunicationFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for NSXApplicationPlatformCommunication feature notifications.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeaturePrefix 0 }

vmwNsxTNSXApplicationPlatformCommunicationDelayInOverflow NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappMessagingLAGThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of pending messages in the messaging topic Over Flow is above the
        pending message threshold of vmwNsxTDataCenterNappMessagingLAGThreshold.

        Action required:
        Add nodes and then scale up the NSX Application Platform cluster. If bottleneck can be attributed to a specific service,
        for example, the analytics service, then scale up the specific service when the new nodes are added.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 7 }

vmwNsxTNSXApplicationPlatformCommunicationDelayInOverflowClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappMessagingLAGThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of pending messages in the messaging topic Over Flow is below the pending
        message threshold of vmwNsxTDataCenterNappMessagingLAGThreshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 8 }

vmwNsxTNSXApplicationPlatformCommunicationDelayInRawflow NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappMessagingLAGThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of pending messages in the messaging topic Raw Flow is above the
        pending message threshold of vmwNsxTDataCenterNappMessagingLAGThreshold.

        Action required:
        Add nodes and then scale up the NSX Application Platform cluster. If the bottleneck can be attributed to a specific service,
        for example, the analytics service, then scale up the specific service when the new nodes are added.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 11 }

vmwNsxTNSXApplicationPlatformCommunicationDelayInRawflowClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappMessagingLAGThreshold
    }
    STATUS current
    DESCRIPTION
        "The number of pending messages in the messaging topic Raw Flow is below the pending
        message threshold of vmwNsxTDataCenterNappMessagingLAGThreshold.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 12 }

vmwNsxTNSXApplicationPlatformCommunicationMgrDisconnected NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId
    }
    STATUS current
    DESCRIPTION
        "The NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is disconnected
        from the NSX management cluster.

        Action required:
        Check whether the manager cluster certificate, manager node certificates,
        kafka certificate and ingress certificate match on both NSX Manager and the
        NSX Application Platform cluster. Check expiration dates of the above mentioned
        certificates to make sure they are valid. Check the network connection between
        NSX Manager and NSX Application Platform cluster and resolve any network connection failures.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 1 }

vmwNsxTNSXApplicationPlatformCommunicationMgrDisconnectedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId
    }
    STATUS current
    DESCRIPTION
        "The NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is reconnected
        to the NSX management cluster.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 2 }

vmwNsxTNSXApplicationPlatformCommunicationExpDisconnected NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The flow exporter on Transport node vmwNsxTDataCenterEntityId is disconnected from
        the NSX Application Platform cluster's messaging broker. Data collection is affected.

        Action required:
        Restart the messaging service if it is not running in the NSX Application
        Platform cluster. Resolve the network connection failure between the Transport node
        flow exporter and the NSX Application Platform cluster.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 13 }

vmwNsxTNSXApplicationPlatformCommunicationExpDisconnectedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The flow exporter on Transport node vmwNsxTDataCenterEntityId has reconnected to
        the NSX Application Platform cluster's messaging broker.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformCommunicationFeature 14 }

-- **********************************************************
-- NSXApplicationPlatformHealth feature event notifications
-- **********************************************************

vmwNsxTNSXApplicationPlatformHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the NSXApplicationPlatformHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 43 }

vmwNsxTNSXApplicationPlatformHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for NSXApplicationPlatformHealth feature notifications.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeaturePrefix 0 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Analytics service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Analytics service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 1 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Analytics service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 2 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Analytics service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Analytics service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 3 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Analytics service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 4 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Analytics service is above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services or the Analytics service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 5 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Analytics service is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 6 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Analytics service is above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services or the Analytics service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 7 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Analytics service is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 8 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Analytics service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Analytics service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 9 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Analytics service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 10 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsMemUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Analytics service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Analytics service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 11 }

vmwNsxTNSXApplicationPlatformHealthAnalyticsMemUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Analytics service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 12 }

vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the System Load field of individual services to see
        which service is under pressure. See if the load can be reduced. If more
        computing power is required, click on the Scale Out button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 13 }

vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 14 }

vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the System Load field of individual services to see
        which service is under pressure. See if the load can be reduced. If more
        computing power is required, click on the Scale Out button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 15 }

vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 16 }

vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Storage field of individual services to see
        which service is under pressure. See if the load can be reduced. If more
        disk storage is required, click on the Scale Out button to request more resources.
        If data storage service is under strain, another way is to click on the Scale Up button
        to increase disk size.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 17 }

vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 18 }

vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Storage field of individual services to see
        which service is under pressure. See if the load can be reduced. If more
        disk storage is required, click on the Scale Out button to request more resources.
        If data storage service is under strain, another way is to click on the Scale Up button
        to increase disk size.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 19 }

vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 20 }

vmwNsxTNSXApplicationPlatformHealthClusterMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Memory field of individual services to see
        which service is under pressure. See if the load can be reduced. If more
        memory is required, click on the Scale Out button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 21 }

vmwNsxTNSXApplicationPlatformHealthClusterMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 22 }

vmwNsxTNSXApplicationPlatformHealthClusterMemUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Memory field of individual services to see
        which service is under pressure. See if the load can be reduced. If more
        memory is required, click on the Scale Out button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 23 }

vmwNsxTNSXApplicationPlatformHealthClusterMemUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is
        below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 24 }

vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Configuration Database service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 31 }

vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Configuration Database service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 32 }

vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Configuration Database service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 33 }

vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Configuration Database service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 34 }

vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Configuration Database service is above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 35 }

vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Configuration Database service is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 36 }

vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Configuration Database service is above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 37 }

vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Configuration Database service is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 38 }

vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Configuration Database service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 109 }

vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Configuration Database service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 110 }

vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Configuration Database service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 39 }

vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Configuration Database service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 40 }

vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Data Storage service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Data Storage service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 41 }

vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Data Storage service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 42 }

vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Data Storage service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Data Storage service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 43 }

vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Data Storage service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 44 }

vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Data Storage service is above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out or scale up the data storage service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 45 }

vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Data Storage service is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 46 }

vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Data Storage service is above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out or scale up the data storage service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 47 }

vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Data Storage service is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 48 }

vmwNsxTNSXApplicationPlatformHealthDatastoreMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Data Storage service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Data Storage service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 49 }

vmwNsxTNSXApplicationPlatformHealthDatastoreMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Data Storage service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 50 }

vmwNsxTNSXApplicationPlatformHealthDatastoreMemUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Data Storage service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Data Storage service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 51 }

vmwNsxTNSXApplicationPlatformHealthDatastoreMemUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Data Storage service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 52 }

vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Messaging service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Messaging service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 53 }

vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Messaging service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 54 }

vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Messaging service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Messaging service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 55 }

vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Messaging service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 56 }

vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Messaging service is above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services or the Messaging service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 57 }

vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Messaging service is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 58 }

vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Messaging service is above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services or the Messaging service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 59 }

vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Messaging service is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 60 }

vmwNsxTNSXApplicationPlatformHealthMessagingMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Messaging service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Messaging service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 61 }

vmwNsxTNSXApplicationPlatformHealthMessagingMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Messaging service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 62 }

vmwNsxTNSXApplicationPlatformHealthMessagingMemUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Messaging service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services or the Messaging service.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 63 }

vmwNsxTNSXApplicationPlatformHealthMessagingMemUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Messaging service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 64 }

vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Metrics service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 97 }

vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Metrics service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 98 }

vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Metrics service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 99 }

vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Metrics service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 100 }

vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Metrics service is above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 101 }

vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Metrics service is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 102 }

vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Metrics service is above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 103 }

vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Metrics service is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 104 }

vmwNsxTNSXApplicationPlatformHealthMetricsMemUasgeHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Metrics service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 105 }

vmwNsxTNSXApplicationPlatformHealthMetricsMemUasgeHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Metrics service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 106 }

vmwNsxTNSXApplicationPlatformHealthMetricsMemUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Metrics service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 107 }

vmwNsxTNSXApplicationPlatformHealthMetricsMemUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Metrics service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 108 }

vmwNsxTNSXApplicationPlatformHealthNappStatusDegraded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform cluster vmwNsxTDataCenterNappClusterId overall status is degraded.

        Action required:
        Get more information from alarms of nodes and services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 117 }

vmwNsxTNSXApplicationPlatformHealthNappStatusDegradedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is running properly.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 118 }

vmwNsxTNSXApplicationPlatformHealthNappStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform cluster vmwNsxTDataCenterNappClusterId overall status is down.

        Action required:
        Get more information from alarms of nodes and services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 119 }

vmwNsxTNSXApplicationPlatformHealthNappStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappClusterId
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform cluster vmwNsxTDataCenterNappClusterId is running properly.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 120 }

vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the System Load field of individual services to see
        which service is under pressure. See if load can be reduced. If only a small minority
        of the nodes have high CPU usage, by default, Kubernetes will reschedule services automatically.
        If most nodes have high CPU usage and load cannot be reduced, click on the Scale Out
        button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 65 }

vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 66 }

vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the System Load field of individual services to see
        which service is under pressure. See if load can be reduced. If only a small minority
        of the nodes have high CPU usage, by default, Kubernetes will reschedule services automatically.
        If most nodes have high CPU usage and load cannot be reduced, click on the Scale Out
        button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 67 }

vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 68 }

vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName is above
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Storage field of individual services to see which service is
        under pressure. Clean up unused data or log to free up disk resources
        and see if the load can be reduced. If more disk storage is required, Scale Out the
        service under pressure. If data storage service is under strain, another way is to
        click on the Scale Up button to increase disk size.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 69 }

vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName is below
        the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 70 }

vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName is above
        the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Storage field of individual services to see
        which service is under pressure. Clean up unused data or log to free up disk resources
        and see if the load can be reduced. If more disk storage is required, Scale Out the
        service under pressure. If data storage service is under strain, another way is to
        click on the Scale Up button to increase disk size.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 71 }

vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName is below
        the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 72 }

vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is above the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Memory field of individual services to see
        which service is under pressure. See if load can be reduced. If only a small minority
        of the nodes have high Memory usage, by default, Kubernetes will reschedule services automatically.
        If most nodes have high Memory usage and load cannot be reduced, click on the Scale Out
        button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 73 }

vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is below the high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 74 }

vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is above the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services
        and check the Memory field of individual services to see
        which service is under pressure. See if load can be reduced. If only a small minority
        of the nodes have high Memory usage, by default, Kubernetes will reschedule services automatically.
        If most nodes have high Memory usage and load cannot be reduced, click on the Scale Out
        button to request more resources.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 75 }

vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of NSX Application Platform node vmwNsxTDataCenterNappNodeName
        is below the very high threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 76 }

vmwNsxTNSXApplicationPlatformHealthNodeStatusDegraded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform node vmwNsxTDataCenterNappNodeName is degraded.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Resources to
        check which node is degraded. Check network, memory and CPU usage of the node.
        Reboot the node if it is a worker node.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 77 }

vmwNsxTNSXApplicationPlatformHealthNodeStatusDegradedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform node vmwNsxTDataCenterNappNodeName is running properly.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 78 }

vmwNsxTNSXApplicationPlatformHealthNodeStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform node vmwNsxTDataCenterNappNodeName is not running.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Resources to
        check which node is down. Check network, memory and CPU usage of the node.
        Reboot the node if it is a worker node.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 79 }

vmwNsxTNSXApplicationPlatformHealthNodeStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappNodeName
    }
    STATUS current
    DESCRIPTION
        "NSX Application Platform node vmwNsxTDataCenterNappNodeName is running properly.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 80 }

vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Platform Services service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 81 }

vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Platform Services service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 82 }

vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageVeryHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Platform Services service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 83 }

vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageVeryHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The CPU usage of Platform Services service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 84 }

vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Platform Services service is above the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 85 }

vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Platform Services service is below the high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 86 }

vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Platform Services service is above the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Clean up files not needed.
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 87 }

vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The disk usage of Platform Services service is below the very high
        threshold value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 88 }

vmwNsxTNSXApplicationPlatformHealthPlatformMemoryUsageHigh NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Platform Services service is above the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 89 }

vmwNsxTNSXApplicationPlatformHealthPlatformMemoryUsageHighClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Platform Services service is below the high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 90 }

vmwNsxTNSXApplicationPlatformHealthPlatformMemUsageVeryHi NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Platform Services service is above the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        Scale out all services.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 91 }

vmwNsxTNSXApplicationPlatformHealthPlatformMemUsageVeryHiClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSystemUsageThreshold
    }
    STATUS current
    DESCRIPTION
        "The memory usage of Platform Services service is below the very high threshold
        value of vmwNsxTDataCenterSystemUsageThreshold%.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 92 }

vmwNsxTNSXApplicationPlatformHealthServiceStatusDegraded NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappServiceName
    }
    STATUS current
    DESCRIPTION
        "Service vmwNsxTDataCenterNappServiceName is degraded. The service may still be able to reach a quorum while
        pods associated with vmwNsxTDataCenterNappServiceName are not all stable. Resources consumed by these
        unstable pods may be released.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services to check which service
        is degraded. Invoke the NSX API GET /napp/api/v1/platform/monitor/feature/health to check which
        specific service is degraded and the reason behind it. Invoke the following CLI command to restart
        the degraded service if necessary:
        `kubectl rollout restart <statefulset/deployment> <service_name> -n <namespace>`
        Degraded services can function correctly but performance is sub-optimal.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 93 }

vmwNsxTNSXApplicationPlatformHealthServiceStatusDegradedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappServiceName
    }
    STATUS current
    DESCRIPTION
        "Service vmwNsxTDataCenterNappServiceName is running properly.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 94 }

vmwNsxTNSXApplicationPlatformHealthServiceStatusDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappServiceName
    }
    STATUS current
    DESCRIPTION
        "Service vmwNsxTDataCenterNappServiceName is not running.

        Action required:
        In the NSX UI, navigate to System | NSX Application Platform | Core Services to check which service
        is degraded. Invoke the NSX API GET /napp/api/v1/platform/monitor/feature/health to check which
        specific service is down and the reason behind it. Invoke the following CLI command to restart
        the degraded service:
        `kubectl rollout restart <statefulset/deployment> <service_name> -n <namespace>`
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 95 }

vmwNsxTNSXApplicationPlatformHealthServiceStatusDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterNappServiceName
    }
    STATUS current
    DESCRIPTION
        "Service vmwNsxTDataCenterNappServiceName is running properly.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTNSXApplicationPlatformHealthFeature 96 }

-- **********************************************************
-- PasswordManagement feature event notifications
-- **********************************************************

vmwNsxTPasswordManagementFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the PasswordManagement feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 4 }

vmwNsxTPasswordManagementFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for PasswordManagement feature notifications.
        "
    ::= { vmwNsxTPasswordManagementFeaturePrefix 0 }

vmwNsxTPasswordManagementPasswordExpirationApproaching NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterUsername,
        vmwNsxTDataCenterPasswordExpirationDays
    }
    STATUS current
    DESCRIPTION
        "The password for user vmwNsxTDataCenterUsername is approaching expiration in
        vmwNsxTDataCenterPasswordExpirationDays days.

        Action required:
        The password for the user vmwNsxTDataCenterUsername needs to be changed soon. For example,
        to apply a new password to a user, invoke the following NSX API with a valid
        password in the request body: PUT /api/v1/node/users/<userid> where <userid>
        is the ID of the user.
        "
    ::= { vmwNsxTPasswordManagementFeature 1 }

vmwNsxTPasswordManagementPasswordExpirationApproachingClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterUsername
    }
    STATUS current
    DESCRIPTION
        "The password for the user vmwNsxTDataCenterUsername has been changed successfully or
        is no longer expired.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTPasswordManagementFeature 2 }

vmwNsxTPasswordManagementPasswordExpired NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterUsername
    }
    STATUS current
    DESCRIPTION
        "The password for user vmwNsxTDataCenterUsername has expired.

        Action required:
        The password for user vmwNsxTDataCenterUsername must be changed now to access the
        system. For example, to apply a new password to a user, invoke the
        following NSX API with a valid password in the request body: PUT
        /api/v1/node/users/<userid> where <userid> is the ID of the user. If the
        admin user (with <userid> 10000) password has expired, admin must login to
        the system via SSH (if enabled) or console in order to change the password.
        Upon entering the current expired password, admin will be prompted to enter
        a new password.
        "
    ::= { vmwNsxTPasswordManagementFeature 3 }

vmwNsxTPasswordManagementPasswordExpiredClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterUsername
    }
    STATUS current
    DESCRIPTION
        "The password for user vmwNsxTDataCenterUsername has been changed successfully or
        is no longer expired.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTPasswordManagementFeature 4 }

vmwNsxTPasswordManagementPasswordIsAboutToExpire NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterUsername,
        vmwNsxTDataCenterPasswordExpirationDays
    }
    STATUS current
    DESCRIPTION
        "The password for user vmwNsxTDataCenterUsername is about to expire in
        vmwNsxTDataCenterPasswordExpirationDays days.

        Action required:
        Ensure the password for the user vmwNsxTDataCenterUsername is changed immediately. For
        example, to apply a new password to a user, invoke the following NSX API
        with a valid password in the request body: PUT /api/v1/node/users/<userid>
        where <userid> is the ID of the user.
        "
    ::= { vmwNsxTPasswordManagementFeature 5 }

vmwNsxTPasswordManagementPasswordIsAboutToExpireClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterUsername
    }
    STATUS current
    DESCRIPTION
        "The password for the user vmwNsxTDataCenterUsername has been changed successfully or
        is no longer expired.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTPasswordManagementFeature 6 }

-- **********************************************************
-- Routing feature event notifications
-- **********************************************************

vmwNsxTRoutingFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the Routing feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 28 }

vmwNsxTRoutingFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for Routing feature notifications.
        "
    ::= { vmwNsxTRoutingFeaturePrefix 0 }

vmwNsxTRoutingBFDDownOnExternalInterface NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLrId,
        vmwNsxTDataCenterPeerAddress,
        vmwNsxTDataCenterSrId
    }
    STATUS current
    DESCRIPTION
        "In router vmwNsxTDataCenterLrId, BFD session for peer vmwNsxTDataCenterPeerAddress is down.

        Action required:
        1. Invoke the NSX CLI command `get logical-routers`.
        2. Switch to the service router vmwNsxTDataCenterSrId
        3. Invoke the NSX CLI command `ping vmwNsxTDataCenterPeerAddress` to verify the
        connectivity.
        "
    ::= { vmwNsxTRoutingFeature 7 }

vmwNsxTRoutingBFDDownOnExternalInterfaceClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLrId,
        vmwNsxTDataCenterPeerAddress
    }
    STATUS current
    DESCRIPTION
        "In router vmwNsxTDataCenterLrId, BFD session for peer vmwNsxTDataCenterPeerAddress is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTRoutingFeature 8 }

vmwNsxTRoutingBGPDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLrId,
        vmwNsxTDataCenterBGPNeighborIP,
        vmwNsxTDataCenterFailureReason,
        vmwNsxTDataCenterSrId
    }
    STATUS current
    DESCRIPTION
        "In Router vmwNsxTDataCenterLrId, BGP neighbor vmwNsxTDataCenterEntityId (vmwNsxTDataCenterBGPNeighborIP) is down.
        Reason: vmwNsxTDataCenterFailureReason.

        Action required:
        1. Invoke the NSX CLI command `get logical-routers`.
        2. Switch to service-router vmwNsxTDataCenterSrId.
        If the reason indicates Network or config error -
        3. Invoke the NSX CLI command `get bgp neighbor summary` to check the
        BGP neighbor status.
        If the reason indicates `Edge is not ready`, check why edge is not in good state.
        4. Invoke the NSX CLI command `get edge-cluster status` to check reason why edge might be down.
        5. Invoke the NSX CLI commands `get bfd-config` and `get bfd-sessions` to check if BFD is running well.
        6. Check any edge health related alarms to get more information.
        Check /var/log/syslog to see if there are any errors related to BGP
        connectivity.
        "
    ::= { vmwNsxTRoutingFeature 1 }

vmwNsxTRoutingBGPDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLrId,
        vmwNsxTDataCenterBGPNeighborIP
    }
    STATUS current
    DESCRIPTION
        "In Router vmwNsxTDataCenterLrId, BGP neighbor vmwNsxTDataCenterEntityId (vmwNsxTDataCenterBGPNeighborIP) is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTRoutingFeature 2 }

vmwNsxTRoutingOSPFNeighborWentDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterPeerAddress
    }
    STATUS current
    DESCRIPTION
        "OSPF neighbor vmwNsxTDataCenterPeerAddress moved from full to another state.

        Action required:
        1. Invoke the NSX CLI command `get logical-routers` to get the vrf id and
        switch to TIER0 service router.
        2. Run `get ospf neighbor` to check the current state of this neighbor. If the
        neighbor is not listed in the output, the neighbor has gone down or out of
        the network.
        3. Invoke the NSX CLI command `ping <OSPF neighbor IP address>` to verify the
        connectivity.
        4. Also, verify the configuration for both NSX and peer router to ensure that
        timers and area-id match.
        5. Check /var/log/syslog to see if there are any errors related to connectivity.
        "
    ::= { vmwNsxTRoutingFeature 11 }

vmwNsxTRoutingOSPFNeighborWentDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterPeerAddress
    }
    STATUS current
    DESCRIPTION
        "OSPF neighbor vmwNsxTDataCenterPeerAddress moved to full state.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTRoutingFeature 12 }

vmwNsxTRoutingProxyARPNotConfiguredForServiceIP NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterServiceIP,
        vmwNsxTDataCenterLrportId,
        vmwNsxTDataCenterLrId
    }
    STATUS current
    DESCRIPTION
        "Proxy arp for Service IP vmwNsxTDataCenterServiceIP and Service entity vmwNsxTDataCenterEntityId
        is not configured as the number of arp proxy entries generated due to overlap of
        the Service IP with subnet of lrport vmwNsxTDataCenterLrportId on Router vmwNsxTDataCenterLrId has exceeded
        the allowed threshold limit of 16384.

        This notification is sent once when the threshold limit is exceeded.

        Action required:
        Reconfigure the Service IP vmwNsxTDataCenterServiceIP for the Service entity vmwNsxTDataCenterEntityId
        or change the subnet of the lrport vmwNsxTDataCenterLrportId on Router vmwNsxTDataCenterLrId so that the proxy arp entries
        generated due to the overlap between the Service IP and the subnet of lrport is less than
        the allowed threshold limit of 16384.
        "
    ::= { vmwNsxTRoutingFeature 13 }

vmwNsxTRoutingProxyARPNotConfiguredForServiceIPClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLrportId,
        vmwNsxTDataCenterLrId
    }
    STATUS current
    DESCRIPTION
        "Proxy arp for Service entity vmwNsxTDataCenterEntityId is generated successfully as the
        overlap of service Ip with subnet of lrport vmwNsxTDataCenterLrportId on Router vmwNsxTDataCenterLrId is
        within the allowed limit of 16384 entries.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTRoutingFeature 14 }

vmwNsxTRoutingRoutingDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "All BGP/BFD sessions are down.

        Action required:
        Invoke the NSX CLI command `get logical-routers` to get the tier0 service router
        and switch to this vrf, then invoke the following NSX CLI commands.
        1. `ping <BFD peer IP address>` to verify connectivity.
        2. `get bfd-config` and `get bfd-sessions` to check if BFD is running well.
        3. `get bgp neighbor summary` to check if BGP is running well.
        Also check /var/log/syslog to see if there are any errors related to BGP
        connectivity.
        "
    ::= { vmwNsxTRoutingFeature 9 }

vmwNsxTRoutingRoutingDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "At least one BGP/BFD session up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTRoutingFeature 10 }

vmwNsxTRoutingStaticRoutingRemoved NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLrId,
        vmwNsxTDataCenterStaticAddress,
        vmwNsxTDataCenterSrId
    }
    STATUS current
    DESCRIPTION
        "In router vmwNsxTDataCenterLrId, static route vmwNsxTDataCenterEntityId (vmwNsxTDataCenterStaticAddress) was removed because
        BFD was down.

        Action required:
        The static routing entry was removed because the BFD session was down.
        1. Invoke the NSX CLI command `get logical-routers`.
        2. Switch to the service-router vmwNsxTDataCenterSrId.
        3. Invoke the NSX CLI command `ping <BFD peer IP address>` to verify the
        connectivity.
        Also, verify the configuration in both NSX and the BFD peer to ensure that
        timers have not been changed.
        "
    ::= { vmwNsxTRoutingFeature 5 }

vmwNsxTRoutingStaticRoutingRemovedClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterLrId,
        vmwNsxTDataCenterStaticAddress
    }
    STATUS current
    DESCRIPTION
        "In router vmwNsxTDataCenterLrId, static route vmwNsxTDataCenterEntityId (vmwNsxTDataCenterStaticAddress) was re-added as BFD
        recovered.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTRoutingFeature 6 }

-- **********************************************************
-- TransportNodeHealth feature event notifications
-- **********************************************************

vmwNsxTTransportNodeHealthFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the TransportNodeHealth feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 18 }

vmwNsxTTransportNodeHealthFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for TransportNodeHealth feature notifications.
        "
    ::= { vmwNsxTTransportNodeHealthFeaturePrefix 0 }

vmwNsxTTransportNodeHealthLAGMemberDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "LACP reporting member down.

        Action required:
        Check the connection status of LAG members on hosts.
        1. In the NSX UI navigate to Fabric | Nodes | Transport Nodes |
        Host Transport Nodes.
        2. In the Host Transport Nodes list, check the Node Status column. Find the
        Transport node with the degraded or down Node Status.
        3. Select <transport node> | Monitor.  Find the bond(uplink) which is
        reporting degraded or down.
        4. Check the LACP member status details by logging into the failed host and
        invoking `esxcli network vswitch dvs vmware lacp status get` on an ESXi
        host or `ovs-appctl bond/show` and `ovs-appctl lacp/show` on a KVM host.
        "
    ::= { vmwNsxTTransportNodeHealthFeature 5 }

vmwNsxTTransportNodeHealthLAGMemberDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "LACP reporting member up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTTransportNodeHealthFeature 6 }

vmwNsxTTransportNodeHealthNVDSUplinkDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Uplink is going down.

        Action required:
        Check the physical NICs' status of uplinks on hosts.
        1. In the NSX UI navigate to Fabric | Nodes | Transport Nodes |
        Host Transport Nodes.
        2. In the Host Transport Nodes list, check the Node Status column. Find the
        Transport node with the degraded or down Node Status.
        3. Select <transport node> | Monitor. Check the status details of the
        bond(uplink) which is reporting degraded or down. To avoid a degraded state,
        ensure all uplink interfaces are connected and up regardless of whether they
        are in use or not.
        "
    ::= { vmwNsxTTransportNodeHealthFeature 3 }

vmwNsxTTransportNodeHealthNVDSUplinkDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Uplink is going up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTTransportNodeHealthFeature 4 }

vmwNsxTTransportNodeHealthTransportNodeUplinkDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Uplink is going down.

        Action required:
        Check the physical NICs' status of uplinks on hosts.
        1. In the NSX UI navigate to Fabric | Nodes | Transport Nodes |
        Host Transport Nodes.
        2. In the Host Transport Nodes list, check the Node Status column. Find the
        Transport node with the degraded or down Node Status.
        3. Select <transport node> | Monitor. Check the status details of the
        bond(uplink) which is reporting degraded or down. To avoid a degraded state,
        ensure all uplink interfaces are connected and up regardless of whether they
        are in use or not.
        "
    ::= { vmwNsxTTransportNodeHealthFeature 7 }

vmwNsxTTransportNodeHealthTransportNodeUplinkDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "Uplink is going up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTTransportNodeHealthFeature 8 }

-- **********************************************************
-- VPN feature event notifications
-- **********************************************************

vmwNsxTVPNFeaturePrefix OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Notifications applicable to the VPN feature have this OID prefix."
    ::= { vmwNsxTDataCenterNotifications 15 }

vmwNsxTVPNFeature OBJECT-IDENTITY
    STATUS current
    DESCRIPTION
        "Sub-identifier added to ensure the next-to-last sub-identifier is zero
        for VPN feature notifications.
        "
    ::= { vmwNsxTVPNFeaturePrefix 0 }

vmwNsxTVPNIPsecPolicyBasedSessionDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSessionDownReason
    }
    STATUS current
    DESCRIPTION
        "The policy based IPsec VPN session vmwNsxTDataCenterEntityId is down.
        Reason: vmwNsxTDataCenterSessionDownReason.

        Action required:
        Check IPsec VPN session configuration and resolve errors based on the
        session down reason.
        "
    ::= { vmwNsxTVPNFeature 7 }

vmwNsxTVPNIPsecPolicyBasedSessionDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The policy based IPsec VPN session vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTVPNFeature 8 }

vmwNsxTVPNIPsecPolicyBasedTunnelDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "One or more policy based IPsec VPN tunnels in session vmwNsxTDataCenterEntityId are down.

        Action required:
        Check IPsec VPN session configuration and resolve errors based on the
        tunnel down reason.
        "
    ::= { vmwNsxTVPNFeature 9 }

vmwNsxTVPNIPsecPolicyBasedTunnelDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "All policy based IPsec VPN tunnels in session vmwNsxTDataCenterEntityId are up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTVPNFeature 10 }

vmwNsxTVPNIPsecRouteBasedSessionDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterSessionDownReason
    }
    STATUS current
    DESCRIPTION
        "The route based IPsec VPN session vmwNsxTDataCenterEntityId is down.
        Reason: vmwNsxTDataCenterSessionDownReason.

        Action required:
        Check IPsec VPN session configuration and resolve errors based on the
        session down reason.
        "
    ::= { vmwNsxTVPNFeature 11 }

vmwNsxTVPNIPsecRouteBasedSessionDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The route based IPsec VPN session vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTVPNFeature 12 }

vmwNsxTVPNIPsecRouteBasedTunnelDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterTunnelDownReason
    }
    STATUS current
    DESCRIPTION
        "The route based IPsec VPN tunnel in session vmwNsxTDataCenterEntityId is down.
        Reason: vmwNsxTDataCenterTunnelDownReason.

        Action required:
        Check IPsec VPN session configuration and resolve errors based on the
        tunnel down reason.
        "
    ::= { vmwNsxTVPNFeature 13 }

vmwNsxTVPNIPsecRouteBasedTunnelDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The route based IPsec VPN tunnel in session vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTVPNFeature 14 }

vmwNsxTVPNIPsecServiceDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterServiceDownReason
    }
    STATUS current
    DESCRIPTION
        "The IPsec service vmwNsxTDataCenterEntityId is down. Reason: vmwNsxTDataCenterServiceDownReason.

        Action required:
        1. Disable and enable the IPsec service from NSX Manager UI.
        2. If the issue still persists, check syslog for error logs and contact
           VMware support.
        "
    ::= { vmwNsxTVPNFeature 17 }

vmwNsxTVPNIPsecServiceDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The IPsec service vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTVPNFeature 18 }

vmwNsxTVPNL2VpnSessionDown NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The L2VPN session vmwNsxTDataCenterEntityId is down.

        Action required:
        Check L2VPN session status for session down reason and resolve errors based
        on the reason.
        "
    ::= { vmwNsxTVPNFeature 15 }

vmwNsxTVPNL2VpnSessionDownClear NOTIFICATION-TYPE
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterEntityId
    }
    STATUS current
    DESCRIPTION
        "The L2VPN session vmwNsxTDataCenterEntityId is up.

        Action required:
        None, receipt of this notification indicates event cleared.
        "
    ::= { vmwNsxTVPNFeature 16 }

-- **********************************************************
-- Conformance information
-- **********************************************************

vmwNsxTDataCenterConformance OBJECT IDENTIFIER ::= { vmwNSXsysMIB 2 }
vmwNsxTDataCenterCompliances OBJECT IDENTIFIER ::= { vmwNsxTDataCenterConformance 1 }
vmwNsxTDataCenterSMIBGroups OBJECT IDENTIFIER ::= { vmwNsxTDataCenterConformance 2 }

-- **********************************************************
-- Compliance statements
-- **********************************************************

vmwNsxTDataCenterBasicCompliance MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationGroup
    }
    ::= { vmwNsxTDataCenterCompliances 1 }

vmwNsxTDataCenterNotificationInfoGroup OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterTimestamp,
        vmwNsxTDataCenterFeatureName,
        vmwNsxTDataCenterEventType,
        vmwNsxTDataCenterEventSeverity,
        vmwNsxTDataCenterNodeId,
        vmwNsxTDataCenterNodeType,
        vmwNsxTDataCenterApplianceAddress,
        vmwNsxTDataCenterBGPNeighborIP,
        vmwNsxTDataCenterCurrentGatewayState,
        vmwNsxTDataCenterCurrentServiceState,
        vmwNsxTDataCenterDHCPPoolUsage,
        vmwNsxTDataCenterDHCPServerId,
        vmwNsxTDataCenterDatapathResourceUsage,
        vmwNsxTDataCenterDiskPartitionName,
        vmwNsxTDataCenterDuplicateIPAddress,
        vmwNsxTDataCenterEdgeNICName,
        vmwNsxTDataCenterEdgeServiceName,
        vmwNsxTDataCenterEntityId,
        vmwNsxTDataCenterEventId,
        vmwNsxTDataCenterFailureReason,
        vmwNsxTDataCenterHeapType,
        vmwNsxTDataCenterIntelligenceNodeId,
        vmwNsxTDataCenterLicenseEditionType,
        vmwNsxTDataCenterMempoolName,
        vmwNsxTDataCenterPasswordExpirationDays,
        vmwNsxTDataCenterPeerAddress,
        vmwNsxTDataCenterPreviousGatewayState,
        vmwNsxTDataCenterPreviousServiceState,
        vmwNsxTDataCenterRxRingBufferOverflowPercentage,
        vmwNsxTDataCenterServiceName,
        vmwNsxTDataCenterSessionDownReason,
        vmwNsxTDataCenterSrId,
        vmwNsxTDataCenterStaticAddress,
        vmwNsxTDataCenterSystemResourceUsage,
        vmwNsxTDataCenterSystemUsageThreshold,
        vmwNsxTDataCenterTunnelDownReason,
        vmwNsxTDataCenterTxRingBufferOverflowPercentage,
        vmwNsxTDataCenterUsername
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 1 }

vmwNsxTDataCenterNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTAlarmManagementAlarmServiceOverloaded,
        vmwNsxTAlarmManagementAlarmServiceOverloadedClear,
        vmwNsxTAlarmManagementHeavyVolumeOfAlarms,
        vmwNsxTAlarmManagementHeavyVolumeOfAlarmsClear,
        vmwNsxTCertificatesCertificateExpirationApproaching,
        vmwNsxTCertificatesCertificateExpirationApproachingClear,
        vmwNsxTCertificatesCertificateExpired,
        vmwNsxTCertificatesCertificateExpiredClear,
        vmwNsxTCertificatesCertificateIsAboutToExpire,
        vmwNsxTCertificatesCertificateIsAboutToExpireClear,
        vmwNsxTCniHealthHyperbusManagerConnectionDown,
        vmwNsxTCniHealthHyperbusManagerConnectionDownClear,
        vmwNsxTDHCPPoolLeaseAllocationFailed,
        vmwNsxTDHCPPoolLeaseAllocationFailedClear,
        vmwNsxTDHCPPoolOverloaded,
        vmwNsxTDHCPPoolOverloadedClear,
        vmwNsxTDistributedFirewallDFWCPUUsageVeryHigh,
        vmwNsxTDistributedFirewallDFWCPUUsageVeryHighClear,
        vmwNsxTDistributedFirewallDFWMemoryUsageVeryHigh,
        vmwNsxTDistributedFirewallDFWMemoryUsageVeryHighClear,
        vmwNsxTDNSForwarderDisabled,
        vmwNsxTDNSForwarderDisabledClear,
        vmwNsxTDNSForwarderDown,
        vmwNsxTDNSForwarderDownClear,
        vmwNsxTEdgeHealthEdgeCPUUsageHigh,
        vmwNsxTEdgeHealthEdgeCPUUsageHighClear,
        vmwNsxTEdgeHealthEdgeCPUUsageVeryHigh,
        vmwNsxTEdgeHealthEdgeCPUUsageVeryHighClear,
        vmwNsxTEdgeHealthEdgeDatapathConfigurationFailure,
        vmwNsxTEdgeHealthEdgeDatapathConfigurationFailureClear,
        vmwNsxTEdgeHealthEdgeDatapathCPUHigh,
        vmwNsxTEdgeHealthEdgeDatapathCPUHighClear,
        vmwNsxTEdgeHealthEdgeDatapathCPUVeryHigh,
        vmwNsxTEdgeHealthEdgeDatapathCPUVeryHighClear,
        vmwNsxTEdgeHealthEdgeDatapathCryptodrvDown,
        vmwNsxTEdgeHealthEdgeDatapathCryptodrvDownClear,
        vmwNsxTEdgeHealthEdgeDatapathMempoolHigh,
        vmwNsxTEdgeHealthEdgeDatapathMempoolHighClear,
        vmwNsxTEdgeHealthEdgeDiskUsageHigh,
        vmwNsxTEdgeHealthEdgeDiskUsageHighClear,
        vmwNsxTEdgeHealthEdgeDiskUsageVeryHigh,
        vmwNsxTEdgeHealthEdgeDiskUsageVeryHighClear,
        vmwNsxTEdgeHealthEdgeGlobalARPTableUsageHigh,
        vmwNsxTEdgeHealthEdgeGlobalARPTableUsageHighClear,
        vmwNsxTEdgeHealthEdgeMemoryUsageHigh,
        vmwNsxTEdgeHealthEdgeMemoryUsageHighClear,
        vmwNsxTEdgeHealthEdgeMemoryUsageVeryHigh,
        vmwNsxTEdgeHealthEdgeMemoryUsageVeryHighClear,
        vmwNsxTEdgeHealthEdgeNICLinkStatusDown,
        vmwNsxTEdgeHealthEdgeNICLinkStatusDownClear,
        vmwNsxTEdgeHealthEdgeNICOutOfReceiveBuffer,
        vmwNsxTEdgeHealthEdgeNICOutOfReceiveBufferClear,
        vmwNsxTEdgeHealthEdgeNICOutOfTransmitBuffer,
        vmwNsxTEdgeHealthEdgeNICOutOfTransmitBufferClear,
        vmwNsxTEndpointProtectionEAMStatusDown,
        vmwNsxTEndpointProtectionEAMStatusDownClear,
        vmwNsxTEndpointProtectionPartnerChannelDown,
        vmwNsxTEndpointProtectionPartnerChannelDownClear,
        vmwNsxTHighAvailabilityTier0GatewayFailover,
        vmwNsxTHighAvailabilityTier0GatewayFailoverClear,
        vmwNsxTHighAvailabilityTier1GatewayFailover,
        vmwNsxTHighAvailabilityTier1GatewayFailoverClear,
        vmwNsxTInfrastructureCommunicationEdgeTunnelsDown,
        vmwNsxTInfrastructureCommunicationEdgeTunnelsDownClear,
        vmwNsxTInfrastructureServiceEdgeServiceStatusChanged,
        vmwNsxTInfrastructureServiceEdgeServiceStatusChangedClear,
        vmwNsxTInfrastructureServiceEdgeServiceStatusDown,
        vmwNsxTInfrastructureServiceEdgeServiceStatusDownClear,
        vmwNsxTIntelligenceCommunicationTNFlowExporterDisconnected,
        vmwNsxTIntelligenceCommunicationTNFlowExporterDisconnectedClear,
        vmwNsxTIntelligenceHealthCPUUsageHigh,
        vmwNsxTIntelligenceHealthCPUUsageHighClear,
        vmwNsxTIntelligenceHealthCPUUsageVeryHigh,
        vmwNsxTIntelligenceHealthCPUUsageVeryHighClear,
        vmwNsxTIntelligenceHealthDataDiskPartitionUsageHigh,
        vmwNsxTIntelligenceHealthDataDiskPartitionUsageHighClear,
        vmwNsxTIntelligenceHealthDataDiskPartitionUsageVeryHigh,
        vmwNsxTIntelligenceHealthDataDiskPartitionUsageVeryHighClear,
        vmwNsxTIntelligenceHealthDiskUsageHigh,
        vmwNsxTIntelligenceHealthDiskUsageHighClear,
        vmwNsxTIntelligenceHealthDiskUsageVeryHigh,
        vmwNsxTIntelligenceHealthDiskUsageVeryHighClear,
        vmwNsxTIntelligenceHealthMemoryUsageHigh,
        vmwNsxTIntelligenceHealthMemoryUsageHighClear,
        vmwNsxTIntelligenceHealthMemoryUsageVeryHigh,
        vmwNsxTIntelligenceHealthMemoryUsageVeryHighClear,
        vmwNsxTIntelligenceHealthNodeStatusDegraded,
        vmwNsxTIntelligenceHealthNodeStatusDegradedClear,
        vmwNsxTLicensesLicenseExpired,
        vmwNsxTLicensesLicenseExpiredClear,
        vmwNsxTLicensesLicenseIsAboutToExpire,
        vmwNsxTLicensesLicenseIsAboutToExpireClear,
        vmwNsxTLoadBalancerLBCPUVeryHigh,
        vmwNsxTLoadBalancerLBCPUVeryHighClear,
        vmwNsxTLoadBalancerLBStatusDown,
        vmwNsxTLoadBalancerLBStatusDownClear,
        vmwNsxTLoadBalancerPoolStatusDown,
        vmwNsxTLoadBalancerPoolStatusDownClear,
        vmwNsxTLoadBalancerVirtualServerStatusDown,
        vmwNsxTLoadBalancerVirtualServerStatusDownClear,
        vmwNsxTManagerHealthDuplicateIPAddress,
        vmwNsxTManagerHealthDuplicateIPAddressClear,
        vmwNsxTManagerHealthManagerConfigDiskUsageHigh,
        vmwNsxTManagerHealthManagerConfigDiskUsageHighClear,
        vmwNsxTManagerHealthManagerConfigDiskUsageVeryHigh,
        vmwNsxTManagerHealthManagerConfigDiskUsageVeryHighClear,
        vmwNsxTManagerHealthManagerCPUUsageHigh,
        vmwNsxTManagerHealthManagerCPUUsageHighClear,
        vmwNsxTManagerHealthManagerCPUUsageVeryHigh,
        vmwNsxTManagerHealthManagerCPUUsageVeryHighClear,
        vmwNsxTManagerHealthManagerDiskUsageHigh,
        vmwNsxTManagerHealthManagerDiskUsageHighClear,
        vmwNsxTManagerHealthManagerDiskUsageVeryHigh,
        vmwNsxTManagerHealthManagerDiskUsageVeryHighClear,
        vmwNsxTManagerHealthManagerMemoryUsageHigh,
        vmwNsxTManagerHealthManagerMemoryUsageHighClear,
        vmwNsxTManagerHealthManagerMemoryUsageVeryHigh,
        vmwNsxTManagerHealthManagerMemoryUsageVeryHighClear,
        vmwNsxTNCPHealthNCPPluginDown,
        vmwNsxTNCPHealthNCPPluginDownClear,
        vmwNsxTNodeAgentsHealthNodeAgentsDown,
        vmwNsxTNodeAgentsHealthNodeAgentsDownClear,
        vmwNsxTPasswordManagementPasswordExpirationApproaching,
        vmwNsxTPasswordManagementPasswordExpirationApproachingClear,
        vmwNsxTPasswordManagementPasswordExpired,
        vmwNsxTPasswordManagementPasswordExpiredClear,
        vmwNsxTPasswordManagementPasswordIsAboutToExpire,
        vmwNsxTPasswordManagementPasswordIsAboutToExpireClear,
        vmwNsxTRoutingBFDDownOnExternalInterface,
        vmwNsxTRoutingBFDDownOnExternalInterfaceClear,
        vmwNsxTRoutingBGPDown,
        vmwNsxTRoutingBGPDownClear,
        vmwNsxTRoutingRoutingDown,
        vmwNsxTRoutingRoutingDownClear,
        vmwNsxTRoutingStaticRoutingRemoved,
        vmwNsxTRoutingStaticRoutingRemovedClear,
        vmwNsxTTransportNodeHealthLAGMemberDown,
        vmwNsxTTransportNodeHealthLAGMemberDownClear,
        vmwNsxTTransportNodeHealthNVDSUplinkDown,
        vmwNsxTTransportNodeHealthNVDSUplinkDownClear,
        vmwNsxTVPNIPsecPolicyBasedSessionDown,
        vmwNsxTVPNIPsecPolicyBasedSessionDownClear,
        vmwNsxTVPNIPsecPolicyBasedTunnelDown,
        vmwNsxTVPNIPsecPolicyBasedTunnelDownClear,
        vmwNsxTVPNIPsecRouteBasedSessionDown,
        vmwNsxTVPNIPsecRouteBasedSessionDownClear,
        vmwNsxTVPNIPsecRouteBasedTunnelDown,
        vmwNsxTVPNIPsecRouteBasedTunnelDownClear,
        vmwNsxTVPNL2VpnSessionDown,
        vmwNsxTVPNL2VpnSessionDownClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 2 }

vmwNsxTDataCenterBasicCompliance2 MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2
    }
    ::= { vmwNsxTDataCenterCompliances 2 }

vmwNsxTDataCenterNotificationInfoGroup2 OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterBGPSourceIP,
        vmwNsxTDataCenterRemoteSiteId,
        vmwNsxTDataCenterRemoteSiteName,
        vmwNsxTDataCenterSiteId,
        vmwNsxTDataCenterSiteName
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 3 }

vmwNsxTDataCenterNotificationGroup2 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTEdgeHealthStorageError,
        vmwNsxTEdgeHealthStorageErrorClear,
        vmwNsxTFederationLmToLmSynchronizationError,
        vmwNsxTFederationLmToLmSynchronizationErrorClear,
        vmwNsxTFederationLmToLmSynchronizationWarning,
        vmwNsxTFederationLmToLmSynchronizationWarningClear,
        vmwNsxTFederationRtepBGPDown,
        vmwNsxTFederationRtepBGPDownClear,
        vmwNsxTManagerHealthOperationsDbDiskUsageHigh,
        vmwNsxTManagerHealthOperationsDbDiskUsageHighClear,
        vmwNsxTManagerHealthOperationsDbDiskUsageVeryHigh,
        vmwNsxTManagerHealthOperationsDbDiskUsageVeryHighClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 4 }

vmwNsxTDataCenterBasicCompliance3 MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationInfoGroup3,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2,
        vmwNsxTDataCenterNotificationGroup3
    }
    ::= { vmwNsxTDataCenterCompliances 3 }

vmwNsxTDataCenterNotificationInfoGroup3 OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterLrId,
        vmwNsxTDataCenterManagerNodeName,
        vmwNsxTDataCenterRxMisses,
        vmwNsxTDataCenterRxProcessed,
        vmwNsxTDataCenterTransportNodeAddress,
        vmwNsxTDataCenterTransportNodeName,
        vmwNsxTDataCenterTxMisses,
        vmwNsxTDataCenterTxProcessed
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 5 }

vmwNsxTDataCenterNotificationGroup3 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTCommunicationManagementChannelToTransportNodeDown,
        vmwNsxTCommunicationManagementChannelToTransportNodeDownClear,
        vmwNsxTCommunicationManagementChannelToTransportNodeDownLg,
        vmwNsxTCommunicationManagementChannelToTransportNodeDownLgClear,
        vmwNsxTCommunicationManagerControlChannelDown,
        vmwNsxTCommunicationManagerControlChannelDownClear,
        vmwNsxTFederationRtepConnectivityLost,
        vmwNsxTFederationRtepConnectivityLostClear,
        vmwNsxTManagerHealthStorageError,
        vmwNsxTManagerHealthStorageErrorClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 6 }

vmwNsxTDataCenterBasicCompliance4 MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationInfoGroup3,
        vmwNsxTDataCenterNotificationInfoGroup4,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2,
        vmwNsxTDataCenterNotificationGroup3,
        vmwNsxTDataCenterNotificationGroup4
    }
    ::= { vmwNsxTDataCenterCompliances 4 }

vmwNsxTDataCenterNotificationInfoGroup4 OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterActiveGlobalManager,
        vmwNsxTDataCenterActiveGlobalManagers,
        vmwNsxTDataCenterApplianceFQDN,
        vmwNsxTDataCenterCapacityDisplayName,
        vmwNsxTDataCenterCapacityUsageCount,
        vmwNsxTDataCenterCentralControlPlaneId,
        vmwNsxTDataCenterDirectoryDomain,
        vmwNsxTDataCenterDisplayedLicenseKey,
        vmwNsxTDataCenterEdgeThreadName,
        vmwNsxTDataCenterHostnameOrIPAddressWithPort,
        vmwNsxTDataCenterIDSEventsCount,
        vmwNsxTDataCenterLDAPServer,
        vmwNsxTDataCenterLatencySource,
        vmwNsxTDataCenterLatencyThreshold,
        vmwNsxTDataCenterLatencyValue,
        vmwNsxTDataCenterManagerNodeId,
        vmwNsxTDataCenterMaxCapacityThreshold,
        vmwNsxTDataCenterMaxIDSEventsAllowed,
        vmwNsxTDataCenterMaxSupportedCapacityCount,
        vmwNsxTDataCenterMinCapacityThreshold,
        vmwNsxTDataCenterRemoteApplianceAddress,
        vmwNsxTDataCenterRemoteManagerNodeId,
        vmwNsxTDataCenterTimeoutInMinutes
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 7 }

vmwNsxTDataCenterNotificationGroup4 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTAuditLogHealthAuditLogFileUpdateError,
        vmwNsxTAuditLogHealthAuditLogFileUpdateErrorClear,
        vmwNsxTAuditLogHealthRemoteLoggingServerError,
        vmwNsxTAuditLogHealthRemoteLoggingServerErrorClear,
        vmwNsxTCapacityMaximumCapacity,
        vmwNsxTCapacityMaximumCapacityClear,
        vmwNsxTCapacityMaximumCapacityThreshold,
        vmwNsxTCapacityMaximumCapacityThresholdClear,
        vmwNsxTCapacityMinimumCapacityThreshold,
        vmwNsxTCapacityMinimumCapacityThresholdClear,
        vmwNsxTCommunicationControlChannelToManagerNodeDown,
        vmwNsxTCommunicationControlChannelToManagerNodeDownClear,
        vmwNsxTCommunicationControlChannelToManagerNodeDownTooLong,
        vmwNsxTCommunicationControlChannelToManagerNodeDownTooLongClear,
        vmwNsxTCommunicationControlChannelToTransportNodeDown,
        vmwNsxTCommunicationControlChannelToTransportNodeDownClear,
        vmwNsxTCommunicationControlChannelToTransportNodeDownLong,
        vmwNsxTCommunicationControlChannelToTransportNodeDownLongClear,
        vmwNsxTCommunicationManagerClusterLatencyHigh,
        vmwNsxTCommunicationManagerClusterLatencyHighClear,
        vmwNsxTCommunicationManagerFQDNLookupFailure,
        vmwNsxTCommunicationManagerFQDNLookupFailureClear,
        vmwNsxTCommunicationManagerFQDNReverseLookupFailure,
        vmwNsxTCommunicationManagerFQDNReverseLookupFailureClear,
        vmwNsxTDistributedIDSIPSMaxEventsReached,
        vmwNsxTDistributedIDSIPSMaxEventsReachedClear,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageHigh,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageHighClear,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageMediumHigh,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageMediumHighClear,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageVeryHigh,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineCPUUsageVeryHighClear,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineDown,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineDownClear,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageHigh,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageHighClear,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageMediumHigh,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageMediumHighClear,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageVeryHigh,
        vmwNsxTDistributedIDSIPSNSXIDPSEngineMemoryUsageVeryHighClear,
        vmwNsxTEdgeHealthDatapathThreadDeadlocked,
        vmwNsxTEdgeHealthDatapathThreadDeadlockedClear,
        vmwNsxTFederationGMToGMSplitBrain,
        vmwNsxTFederationGMToGMSplitBrainClear,
        vmwNsxTIdentityFirewallConnectivityToLDAPServerLost,
        vmwNsxTIdentityFirewallConnectivityToLDAPServerLostClear,
        vmwNsxTIdentityFirewallErrorInDeltaSync,
        vmwNsxTIdentityFirewallErrorInDeltaSyncClear,
        vmwNsxTInfrastructureServiceServiceStatusUnknown,
        vmwNsxTInfrastructureServiceServiceStatusUnknownClear,
        vmwNsxTIntelligenceHealthStorageLatencyHigh,
        vmwNsxTIntelligenceHealthStorageLatencyHighClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 8 }

vmwNsxTDataCenterBasicCompliance5 MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationInfoGroup3,
        vmwNsxTDataCenterNotificationInfoGroup4,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2,
        vmwNsxTDataCenterNotificationGroup3,
        vmwNsxTDataCenterNotificationGroup4,
        vmwNsxTDataCenterNotificationGroup5
    }
    ::= { vmwNsxTDataCenterCompliances 5 }

vmwNsxTDataCenterNotificationGroup5 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTRoutingOSPFNeighborWentDown,
        vmwNsxTRoutingOSPFNeighborWentDownClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 10 }

vmwNsxTDataCenterBasicCompliance6 MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationInfoGroup3,
        vmwNsxTDataCenterNotificationInfoGroup4,
        vmwNsxTDataCenterNotificationInfoGroup6,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2,
        vmwNsxTDataCenterNotificationGroup3,
        vmwNsxTDataCenterNotificationGroup4,
        vmwNsxTDataCenterNotificationGroup5,
        vmwNsxTDataCenterNotificationGroup6
    }
    ::= { vmwNsxTDataCenterCompliances 6 }

vmwNsxTDataCenterNotificationInfoGroup6 OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterLrportId,
        vmwNsxTDataCenterServiceIP
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 11 }

vmwNsxTDataCenterNotificationGroup6 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTRoutingProxyARPNotConfiguredForServiceIP,
        vmwNsxTRoutingProxyARPNotConfiguredForServiceIPClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 12 }

vmwNsxTDataCenterBasicCompliance7 MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationInfoGroup3,
        vmwNsxTDataCenterNotificationInfoGroup4,
        vmwNsxTDataCenterNotificationInfoGroup6,
        vmwNsxTDataCenterNotificationInfoGroup7,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2,
        vmwNsxTDataCenterNotificationGroup3,
        vmwNsxTDataCenterNotificationGroup4,
        vmwNsxTDataCenterNotificationGroup5,
        vmwNsxTDataCenterNotificationGroup6,
        vmwNsxTDataCenterNotificationGroup7
    }
    ::= { vmwNsxTDataCenterCompliances 7 }

vmwNsxTDataCenterNotificationInfoGroup7 OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterIntentPath
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 13 }

vmwNsxTDataCenterNotificationGroup7 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTIPAMIPBlockUsageVeryHigh,
        vmwNsxTIPAMIPBlockUsageVeryHighClear,
        vmwNsxTIPAMIPPoolUsageVeryHigh,
        vmwNsxTIPAMIPPoolUsageVeryHighClear,
        vmwNsxTLoadBalancerDLBStatusDown,
        vmwNsxTLoadBalancerDLBStatusDownClear,
        vmwNsxTLoadBalancerLBEdgeCapacityInUseHigh,
        vmwNsxTLoadBalancerLBEdgeCapacityInUseHighClear,
        vmwNsxTLoadBalancerLBPoolMemberCapacityInUseVeryHigh,
        vmwNsxTLoadBalancerLBPoolMemberCapacityInUseVeryHighClear,
        vmwNsxTLoadBalancerLBStatusDegraded,
        vmwNsxTLoadBalancerLBStatusDegradedClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 14 }

vmwNsxTDataCenterBasicCompliance8 MODULE-COMPLIANCE
    STATUS deprecated
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationInfoGroup3,
        vmwNsxTDataCenterNotificationInfoGroup4,
        vmwNsxTDataCenterNotificationInfoGroup7,
        vmwNsxTDataCenterNotificationInfoGroup8,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2,
        vmwNsxTDataCenterNotificationGroup3,
        vmwNsxTDataCenterNotificationGroup4,
        vmwNsxTDataCenterNotificationGroup5,
        vmwNsxTDataCenterNotificationGroup6,
        vmwNsxTDataCenterNotificationGroup7,
        vmwNsxTDataCenterNotificationGroup8
    }
    ::= { vmwNsxTDataCenterCompliances 8 }

vmwNsxTDataCenterNotificationInfoGroup8 OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterDNSId,
        vmwNsxTDataCenterDNSUpstreamIP,
        vmwNsxTDataCenterFirewallHalfopenFlowUsage,
        vmwNsxTDataCenterFirewallICMPFlowUsage,
        vmwNsxTDataCenterFirewallIPFlowUsage,
        vmwNsxTDataCenterFirewallUDPFlowUsage
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 15 }

vmwNsxTDataCenterNotificationGroup8 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTDNSForwarderUpstreamServerTimeout,
        vmwNsxTDNSForwarderUpstreamServerTimeoutClear,
        vmwNsxTGatewayFirewallICMPFlowCountExceeded,
        vmwNsxTGatewayFirewallICMPFlowCountExceededClear,
        vmwNsxTGatewayFirewallICMPFlowCountHigh,
        vmwNsxTGatewayFirewallICMPFlowCountHighClear,
        vmwNsxTGatewayFirewallIPFlowCountExceeded,
        vmwNsxTGatewayFirewallIPFlowCountExceededClear,
        vmwNsxTGatewayFirewallIPFlowCountHigh,
        vmwNsxTGatewayFirewallIPFlowCountHighClear,
        vmwNsxTGatewayFirewallTcpHalfOpenFlowCountExceeded,
        vmwNsxTGatewayFirewallTcpHalfOpenFlowCountExceededClear,
        vmwNsxTGatewayFirewallTcpHalfOpenFlowCountHigh,
        vmwNsxTGatewayFirewallTcpHalfOpenFlowCountHighClear,
        vmwNsxTGatewayFirewallUDPFlowCountExceeded,
        vmwNsxTGatewayFirewallUDPFlowCountExceededClear,
        vmwNsxTGatewayFirewallUDPFlowCountHigh,
        vmwNsxTGatewayFirewallUDPFlowCountHighClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 16 }

vmwNsxTDataCenterBasicCompliance9 MODULE-COMPLIANCE
    STATUS current
    DESCRIPTION
        "The compliance statement for entities which implement VMWARE-NSX-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
        vmwNsxTDataCenterNotificationInfoGroup,
        vmwNsxTDataCenterNotificationInfoGroup2,
        vmwNsxTDataCenterNotificationInfoGroup3,
        vmwNsxTDataCenterNotificationInfoGroup4,
        vmwNsxTDataCenterNotificationInfoGroup7,
        vmwNsxTDataCenterNotificationInfoGroup8,
        vmwNsxTDataCenterNotificationInfoGroup9,
        vmwNsxTDataCenterNotificationGroup,
        vmwNsxTDataCenterNotificationGroup2,
        vmwNsxTDataCenterNotificationGroup3,
        vmwNsxTDataCenterNotificationGroup4,
        vmwNsxTDataCenterNotificationGroup5,
        vmwNsxTDataCenterNotificationGroup6,
        vmwNsxTDataCenterNotificationGroup7,
        vmwNsxTDataCenterNotificationGroup8,
        vmwNsxTDataCenterNotificationGroup9
    }
    ::= { vmwNsxTDataCenterCompliances 9 }

vmwNsxTDataCenterNotificationInfoGroup9 OBJECT-GROUP
    OBJECTS {
        vmwNsxTDataCenterAPICollectionPath,
        vmwNsxTDataCenterCABundleAgeThreshold,
        vmwNsxTDataCenterEdgeCryptoDrvName,
        vmwNsxTDataCenterEdgeNodeSettingMismatchReason,
        vmwNsxTDataCenterEdgeNodeAndvSphereSettingsMismatchReason,
        vmwNsxTDataCenterEdgeVMvSphereSettingsMismatchReason,
        vmwNsxTDataCenterEdgevSphereLocationMismatchReason,
        vmwNsxTDataCenterFirewallSNATPortsUsage,
        vmwNsxTDataCenterFlowIdentifier,
        vmwNsxTDataCenterFromGMPath,
        vmwNsxTDataCenterGroupType,
        vmwNsxTDataCenterManagerNodeIDS,
        vmwNsxTDataCenterNICThroughput,
        vmwNsxTDataCenterNICThroughputThreshold,
        vmwNsxTDataCenterNappClusterId,
        vmwNsxTDataCenterNappMessagingLAGThreshold,
        vmwNsxTDataCenterNappNodeId,
        vmwNsxTDataCenterNappNodeName,
        vmwNsxTDataCenterNappServiceName,
        vmwNsxTDataCenterQueueName,
        vmwNsxTDataCenterQueueSize,
        vmwNsxTDataCenterQueueSizeThreshold,
        vmwNsxTDataCenterSNATIPAddress,
        vmwNsxTDataCenterServiceDownReason,
        vmwNsxTDataCenterServiceRouterId,
        vmwNsxTDataCenterSyncIssueReason,
        vmwNsxTDataCenterToGMPath,
        vmwNsxTDataCenterTransportNodeId
    }
    STATUS current
    DESCRIPTION
        "These objects provide details in NSX-T Data Center event notifications."
    ::= { vmwNsxTDataCenterSMIBGroups 17 }

vmwNsxTDataCenterNotificationGroup9 NOTIFICATION-GROUP
    NOTIFICATIONS {
        vmwNsxTCertificatesCABundleUpdateRecommended,
        vmwNsxTCertificatesCABundleUpdateRecommendedClear,
        vmwNsxTCertificatesCABundleUpdateSuggested,
        vmwNsxTCertificatesCABundleUpdateSuggestedClear,
        vmwNsxTClusteringClusterDegraded,
        vmwNsxTClusteringClusterDegradedClear,
        vmwNsxTClusteringClusterUnavailable,
        vmwNsxTClusteringClusterUnavailableClear,
        vmwNsxTCommunicationManagementChannelToManagerNodeDown,
        vmwNsxTCommunicationManagementChannelToManagerNodeDownClear,
        vmwNsxTCommunicationManagementChannelToManagerNodeDownLong,
        vmwNsxTCommunicationManagementChannelToManagerNodeDownLongClear,
        vmwNsxTDistributedFirewallDFWSessionCountHigh,
        vmwNsxTDistributedFirewallDFWSessionCountHighClear,
        vmwNsxTDistributedFirewallDFWVmotionFailure,
        vmwNsxTDistributedFirewallDFWVmotionFailureClear,
        vmwNsxTEdgeEdgeNodeSettingsAndvSphereSettingsAreChanged,
        vmwNsxTEdgeEdgeNodeSettingsAndvSphereSettingsAreChangedClear,
        vmwNsxTEdgeEdgeNodeSettingsMismatch,
        vmwNsxTEdgeEdgeNodeSettingsMismatchClear,
        vmwNsxTEdgeEdgeVmvSphereSettingsMismatch,
        vmwNsxTEdgeEdgeVmvSphereSettingsMismatchClear,
        vmwNsxTEdgeEdgevSphereLocationMismatch,
        vmwNsxTEdgeEdgevSphereLocationMismatchClear,
        vmwNsxTEdgeHealthEdgeDatapathNICThroughputHigh,
        vmwNsxTEdgeHealthEdgeDatapathNICThroughputHighClear,
        vmwNsxTEdgeHealthEdgeDatapathNICThroughputVeryHigh,
        vmwNsxTEdgeHealthEdgeDatapathNICThroughputVeryHighClear,
        vmwNsxTEdgeHealthFailureDomainDown,
        vmwNsxTEdgeHealthFailureDomainDownClear,
        vmwNsxTFederationGMToGMLatencyWarning,
        vmwNsxTFederationGMToGMLatencyWarningClear,
        vmwNsxTFederationGMToGMSynchronizationError,
        vmwNsxTFederationGMToGMSynchronizationErrorClear,
        vmwNsxTFederationGMToGMSynchronizationWarning,
        vmwNsxTFederationGMToGMSynchronizationWarningClear,
        vmwNsxTFederationGMToLMLatencyWarning,
        vmwNsxTFederationGMToLMLatencyWarningClear,
        vmwNsxTFederationGMToLMSynchronizationError,
        vmwNsxTFederationGMToLMSynchronizationErrorClear,
        vmwNsxTFederationGMToLMSynchronizationWarning,
        vmwNsxTFederationGMToLMSynchronizationWarningClear,
        vmwNsxTFederationLMRestoreWhileConfigImportInProgress,
        vmwNsxTFederationLMRestoreWhileConfigImportInProgressClear,
        vmwNsxTFederationQueueOccupancyThresholdExceeded,
        vmwNsxTFederationQueueOccupancyThresholdExceededClear,
        vmwNsxTLoadBalancerConfigurationNotRealizedDueToLowMemory,
        vmwNsxTLoadBalancerConfigurationNotRealizedDueToLowMemoryClear,
        vmwNsxTMTUCheckGlobalRouterMTUTooBig,
        vmwNsxTMTUCheckGlobalRouterMTUTooBigClear,
        vmwNsxTMTUCheckMTUMismatchWithinTransportZone,
        vmwNsxTMTUCheckMTUMismatchWithinTransportZoneClear,
        vmwNsxTNATSNATPortUsageOnGatewayIsHigh,
        vmwNsxTNATSNATPortUsageOnGatewayIsHighClear,
        vmwNsxTNSXApplicationPlatformCommunicationDelayInOverflow,
        vmwNsxTNSXApplicationPlatformCommunicationDelayInOverflowClear,
        vmwNsxTNSXApplicationPlatformCommunicationDelayInRawflow,
        vmwNsxTNSXApplicationPlatformCommunicationDelayInRawflowClear,
        vmwNsxTNSXApplicationPlatformCommunicationMgrDisconnected,
        vmwNsxTNSXApplicationPlatformCommunicationMgrDisconnectedClear,
        vmwNsxTNSXApplicationPlatformCommunicationExpDisconnected,
        vmwNsxTNSXApplicationPlatformCommunicationExpDisconnectedClear,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsCPUUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsDiskUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsMemoryUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsMemoryUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsMemUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthAnalyticsMemUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthClusterCPUUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthClusterDiskUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthClusterMemoryUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthClusterMemoryUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthClusterMemUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthClusterMemUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageHi,
        vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageHiClear,
        vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthConfigDbCPUUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthConfigDbDiskUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthConfigDbMemUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthDatastoreCPUUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthDatastoreDiskUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthDatastoreMemoryUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthDatastoreMemoryUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthDatastoreMemUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthDatastoreMemUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthMessagingCPUUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthMessagingDiskUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthMessagingMemoryUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthMessagingMemoryUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthMessagingMemUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthMessagingMemUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthMetricsCPUUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageHi,
        vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageHiClear,
        vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthMetricsDiskUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthMetricsMemUasgeHi,
        vmwNsxTNSXApplicationPlatformHealthMetricsMemUasgeHiClear,
        vmwNsxTNSXApplicationPlatformHealthMetricsMemUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthMetricsMemUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthNappStatusDegraded,
        vmwNsxTNSXApplicationPlatformHealthNappStatusDegradedClear,
        vmwNsxTNSXApplicationPlatformHealthNappStatusDown,
        vmwNsxTNSXApplicationPlatformHealthNappStatusDownClear,
        vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthNodeCPUUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthNodeDiskUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthNodeMemoryUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthNodeStatusDegraded,
        vmwNsxTNSXApplicationPlatformHealthNodeStatusDegradedClear,
        vmwNsxTNSXApplicationPlatformHealthNodeStatusDown,
        vmwNsxTNSXApplicationPlatformHealthNodeStatusDownClear,
        vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageVeryHigh,
        vmwNsxTNSXApplicationPlatformHealthPlatformCPUUsageVeryHighClear,
        vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthPlatformDiskUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthPlatformMemoryUsageHigh,
        vmwNsxTNSXApplicationPlatformHealthPlatformMemoryUsageHighClear,
        vmwNsxTNSXApplicationPlatformHealthPlatformMemUsageVeryHi,
        vmwNsxTNSXApplicationPlatformHealthPlatformMemUsageVeryHiClear,
        vmwNsxTNSXApplicationPlatformHealthServiceStatusDegraded,
        vmwNsxTNSXApplicationPlatformHealthServiceStatusDegradedClear,
        vmwNsxTNSXApplicationPlatformHealthServiceStatusDown,
        vmwNsxTNSXApplicationPlatformHealthServiceStatusDownClear,
        vmwNsxTTransportNodeHealthTransportNodeUplinkDown,
        vmwNsxTTransportNodeHealthTransportNodeUplinkDownClear,
        vmwNsxTVPNIPsecServiceDown,
        vmwNsxTVPNIPsecServiceDownClear
    }
    STATUS current
    DESCRIPTION
        "Group of objects describing event notifications emitted by
        NSX-T Data Center.
        "
    ::= { vmwNsxTDataCenterSMIBGroups 18 }

END
