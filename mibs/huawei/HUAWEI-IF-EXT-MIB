-- ==================================================================
-- Copyright (C) 2022 by  HUAWEI TECHNOLOGIES. All rights reserved.
-- 
-- Description: HUAWEI Private Extended Interface MIB
-- Reference:
-- Version: V3.30
-- History:
--      V1.0 
-- ==================================================================
-- ==================================================================
-- 
-- Variables and types be imported
-- 
-- ==================================================================

    HUAWEI-IF-EXT-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            CounterBasedGauge64
                FROM HCNUM-TC
            hwDatacomm            
                FROM HUAWEI-MIB            
            ifIndex, ifName, InterfaceIndex,InterfaceIndexOrZero,ifAdminStatus,ifOperStatus,ifDescr          
                FROM IF-MIB            
            VlanIdOrNone, PortList            
                FROM Q-BRIDGE-MIB            
            OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP            
                FROM SNMPv2-CONF            
            IpAddress, Integer32, Unsigned32, Counter64, OBJECT-TYPE, Gauge32,
            MODULE-IDENTITY, NOTIFICATION-TYPE            
                FROM SNMPv2-SMI           
            sysUpTime
                FROM SNMPv2-MIB                 
            RowStatus, TruthValue, DisplayString,PhysAddress,TEXTUAL-CONVENTION,TimeStamp  
                FROM SNMPv2-TC
            AtmVpIdentifier, AtmVcIdentifier            
                FROM ATM-TC-MIB              
            ipAdEntNetMask
                FROM IP-MIB
            InetVersion
                FROM INET-ADDRESS-MIB;
    
    
        hwIFExtMib MODULE-IDENTITY 
            LAST-UPDATED "202206180000Z"
            ORGANIZATION 
                "Huawei Technologies Co.,Ltd."
            CONTACT-INFO 
                "Huawei Industrial Base
                  Bantian, Longgang
                   Shenzhen 518129
                   People's Republic of China
                   Website: http://www.huawei.com
                   Email: <EMAIL>
                 "
                DESCRIPTION 
                "V3.30 Add  hwIPv4StateChangeReason." 
                 REVISION     "202206180000Z"
                DESCRIPTION
                "V3.29 Add  hwIPv4IfStateAlarm and hwIPv4IfStateAlarmResume." 
                 REVISION     "202205270000Z"
                DESCRIPTION
                "V3.28  Add hwFlexeChannelNotSupportAlarm and hwFlexeChannelNotSupportResume."
                REVISION     "202204150000Z"
                DESCRIPTION
                "V3.27  Add hwVirtualEthernetChipMismatchAlarm and hwVirtualEthernetChipMismatchResume."
                REVISION     "202203250000Z"
                DESCRIPTION
                "V3.26  Add hwIfIPConflict and hwIfIPConflictResume."
                REVISION     "202203120000Z"
                DESCRIPTION
                "V3.25  Add hwModeFlexeBandwidth."
                REVISION     "202202220000Z"
                 DESCRIPTION 
                 "V3.24 Add hwIFExtPhyStatusUpTime." 
                 REVISION     "202112290000Z"
                 DESCRIPTION 
                 "V3.23 Update hwTrunkIfModel, add object-type roundRobin(21),resilient(22),dynProfile(23)" 
                 REVISION     "202107050000Z"
                 DESCRIPTION 
                 "V3.22 Add hwLacpStateDown and hwLacpStateDownResume." 
                 REVISION     "202105240000Z"
                 DESCRIPTION 
                 "V3.21 modify the range of hwIFExtFlowStatInterval"
                 REVISION     "202104141000Z"
                 DESCRIPTION 
                 "V3.20 add hwTrunkBandwidthAlarm,hwTrunkBandwidthResume"
                 REVISION     "202102011000Z"
                  DESCRIPTION 
                 "V3.19 Standardized description of hwIFExtTable." 
                 REVISION     "202101261000Z"
                  DESCRIPTION 
                 "V3.18 add hwIfDualStackStatsInPktRate,hwIfDualStackStatsInOctetRate,hwIfDualStackStatsOutPktRate,hwIfDualStackStatsOutOctetRate" 
                 REVISION     "202011091700Z"
                  DESCRIPTION 
                 "V3.17 Text style check." 
                 REVISION     "202008131000Z"
                  DESCRIPTION 
                 "V3.16 add hwTrunkBwChange." 
                 REVISION     "202008121000Z"
                  DESCRIPTION 
                 "V3.15 modiofy 'sended' to 'sent'." 
                 REVISION     "202007231000Z"
                  DESCRIPTION 
                 "V3.14 modiofy 'sended' to 'sent'." 
                 REVISION     "202007230000Z"
                  DESCRIPTION 
                 "V3.13 modiofy 'sended' to 'sent'." 
                 REVISION     "202007220000Z"
                  DESCRIPTION 
                 "V3.12 modiofy hwIFExtLayer." 
                 REVISION     "202004270000Z"                
                DESCRIPTION 
                 "V3.10 modiofy hwTrunkIfTimeoutReceive." 
                 REVISION     "202003201011Z"
                 DESCRIPTION 
                 "V3.09 add hwIfDualStatckStatsInUcastPkts,hwIfDualStatckStatsInUcastOctets,hwIfDualStatckStatsInMcastPkts,hwIfDualStatckStatsInMcastOctets,hwIfDualStatckStatsInBcastPkts,hwIfDualStatckStatsInBcastOctets,hwIfDualStatckStatsOutUcastPkts,hwIfDualStatckStatsOutUcastOctets,hwIfDualStatckStatsOutMcastPkts,
                 hwIfDualStatckStatsOutMcastOctets,hwIfDualStatckStatsOutBcastPkts,hwIfDualStatckStatsOutBcastOctets." 
                 REVISION     "202001080000Z"

                 DESCRIPTION 
                 "V3.08 add hwIFExtDampStatus hwIfDampSuppress hwIfDampResume." 
                 REVISION     "201910230000Z"

                 DESCRIPTION
               "V3.07 Modify hwRemoteIfInUcastPkts,hwRemoteIfInMulticastPkts,hwRemoteIfInBroadcastPkts,hwRemoteIfInErrorPkts,hwRemoteIfInDiscardPkts,hwRemoteIfOutUcastPkts,hwRemoteIfOutMulticastPkts,hwRemoteIfOutBroadcastPkts,hwRemoteIfOutErrorPkts,hwRemoteIfOutDiscardPkts DESCRIPTION."
                REVISION     "201909250000Z"

                 DESCRIPTION
               "V3.06 Modify hwLicenseEffectServiceAlarm DESCRIPTION."
                REVISION     "201909090000Z"

                DESCRIPTION
                "V3.05 Modify hwLogicIfTable hwLogicIfType, add virtualIf."
                REVISION     "201908090000Z"

                 DESCRIPTION
                "V3.04 add hwIFExtInputBitRate,hwIFExtOutputBitRate."
                REVISION     "201907240000Z"

                 DESCRIPTION
                "V3.03 Add hwTrunkIfStatusChange."
                REVISION     "201901290000Z"

                 DESCRIPTION
                "V3.02 remove hwTrunkIfStatus from hwTrunkMemNumberChange."
                REVISION     "201901280000Z"

                 DESCRIPTION
		 "V3.01 add hwTrunkMemberChange" 
		 REVISION     "201901090000Z" 
		 
		 DESCRIPTION 
                "V3.00 Add hwTrunkIfStatus."
                REVISION     "201901070000Z"

                 DESCRIPTION
                "V2.99 Modify hwLogicIfTable hwLogicIfParaOne, add l2subif."
                REVISION     "201808170000Z" 

                DESCRIPTION
                "V2.98  Add hwModeChannelBandwidth."
                REVISION     "201806130000Z"

                DESCRIPTION
                "V2.97  Add hwLacpPartnerMisconnect and hwLacpPartnerMisconnectResume."
                REVISION     "201806060000Z"

                DESCRIPTION
                "V2.96 Modify hwLogicIfTable hwLogicIfType, add gmplsUni."
                REVISION     "201802070000Z" 

                DESCRIPTION
                "V2.94 Modify hwPhysicalAdminIfDown and hwPhysicalAdminIfUp."
                REVISION     "201802070000Z"    

                DESCRIPTION
                "V2.93 add hwPhysicalAdminIfDown and hwPhysicalAdminIfUp."
                REVISION     "201801260000Z"                

                DESCRIPTION
                "V2.92  Modify subchannelThreshold, add subchannelThreshold kinds"
                REVISION     "201801250000Z"
				
                DESCRIPTION
                "V2.91  Modify hwLogicIfType, add virtualserial,pwve, subPwVe, vbdif"
                REVISION     "201801190000Z"

                DESCRIPTION
                "V2.90  Modify description."
                REVISION     "201801150000Z"

                DESCRIPTION
                "V2.89  Modify hwCppsPortPvcEnable and hwCppsPortVlanEnable DEFVAL."
                REVISION     "201712200000Z"

                DESCRIPTION
                "V2.88  add hwIFExtDualFlowRateTable."
                REVISION     "201711280000Z"
                 
                DESCRIPTION
                "V2.87consistent with code base."
                REVISION     "201711140000Z"
				
                DESCRIPTION
                "V2.86 mode channel sub-interface."
                REVISION     "201711060000Z"
				
                DESCRIPTION
                "V2.85 delete linkDown linkUp ipv6IfStateChange ipv6NotificationGroup."
                REVISION     "201710280000Z"

                DESCRIPTION
                "V2.84 Modify the description of hwIfDualStackStatsTable."
                REVISION     "201710250000Z"
                                 
                DESCRIPTION
                "V2.83  add hwIfIpStatisticsTable."
                REVISION     "201710120000Z"

                DESCRIPTION
                "V2.82  add hwIfDualStackStatsTable."
                REVISION     "201709240000Z"
		             
              DESCRIPTION
                "V2.81 add ipv6IfStateChange."
                REVISION     "201709140000Z"

              DESCRIPTION
                "V2.80 add PostFecAlarm and PostFecAlarmResume NOTIFICATION-TYPE."
                REVISION     "201708290000Z"
              DESCRIPTION
                "V2.79 add linkDown and linkUp NOTIFICATION-TYPE."
                REVISION     "201708180000Z"

             DESCRIPTION
                "V2.78 Modify the description of hwRemoteIfTable."
                REVISION     "201708170000Z"

             DESCRIPTION
                "V2.77 add hwTrunkAllMemUpNotify."
                REVISION     "201707270000Z"

            DESCRIPTION
                "V2.76 update the DESCRIPTION of hwIfEtherStatTable,hwIfSdhStatTable,hwIfAtmStatTable,hwIfPPPHDLCStatTable."
                REVISION     "201707240000Z"

            DESCRIPTION
                "V2.75 add hwLicenseEffectServiceAlarm, hwLicenseEffectServiceResume."
                REVISION     "201703290000Z"

            DESCRIPTION
                "V2.74 add hwRemoteIfTable."
                REVISION     "201703160000Z"

            DESCRIPTION
                "V2.73 add hwIFExtInputPkts,hwIFExtOutputPkts,hwIFExtInputUnicastBitRate,hwIFExtOutputUnicastBitRate,hwIFExtInputMulticastBitRate,hwIFExtOutputMulticastBitRate,hwIFExtInputBroadcastBitRate,hwIFExtOutputBroadcastBitRate,
                hwIFExtInputUnicastPktRate,hwIFExtOutputUnicastPktRate,hwIFExtInputMulticastPktRate,hwIFExtOutputMulticastPktRate,hwIFExtInputBroadcastPktRate,hwIFExtOutputBroadcastPktRate."
                REVISION     "201701240000Z"

           DESCRIPTION 
                "V2.72 rollback hwRemoteIfTable."
                REVISION     "201701240000Z" 

           DESCRIPTION 
                "V2.71 add hwRemoteIfTable."
                REVISION     "201701190000Z" 

           DESCRIPTION 
                "V2.70 add hwMruDiscardStatisticAttr."
                REVISION     "201612280000Z" 

            DESCRIPTION 
                "V2.69 Update the hwLinkDownReason, add from cfmSessionDown to cfmSessionUp."
                REVISION     "201610100000Z" 
            DESCRIPTION 
                "V2.57 Modify hwIfEtherStatInPkts64Octets hwIfEtherStatInPkts65to127Octets hwIfEtherStatInPkts128to255Octets hwIfEtherStatInPkts256to511Octets hwIfEtherStatInPkts512to1023Octets hwIfEtherStatInPkts1024to1518Octets DESCRIPTION"
            REVISION     "201609271000Z"
            DESCRIPTION
                "V2.68 modify hwIfDiffServMode range."
            REVISION     "201609232026Z"
            DESCRIPTION 
                "V2.64  add hwLagMemberDown and hwLagMemberDownResume."
                REVISION     "201608062026Z" 
            DESCRIPTION 
                 "V2.66 change hwIfMonitorAllStatistics" 
                REVISION     "201605210000Z" 
           DESCRIPTION 
                 "V2.65 add hwIfMonitorAllStatistics" 
                REVISION     "201512230000Z"
            DESCRIPTION 
                "V2.63  add hwTrunkSubinterfacesCount and hwTrunkSubinterfacesLimit."
                REVISION     "201512080000Z"
            DESCRIPTION
               "V2.62 (1) add hwIfMonitorTxPauseFrameStatistics, hwIfMonitorTxPauseFrameHighThreshold, hwIfMonitorTxPauseFrameLowThreshold, hwIfMonitorTxPauseFrameInterval,
                              hwIfMonitorTxPauseFrameRising, hwIfMonitorTxPauseFrameRisingResume;
                      (2) modify spelling mistake (recieved to received) in description."
                REVISION     "201509060000Z"
            DESCRIPTION 
               "V2.61 Update hwLogicIfType, add object-type lmpif(31),update  hwLoopbackBlock, change OBJECTS { ifDescr, hwNewIfTimeslot }."
                REVISION     "201508060000Z"
            DESCRIPTION 
                "V2.60 Update the hwLinkDownReason, add from linkHearBeatDown to triggerDown."
                REVISION     "201506290000Z"
            DESCRIPTION 
                "V2.59 Add Eth/Pos/Serial(PPP/HDLC)  Statistic "
                REVISION     "201505140000Z"
            DESCRIPTION 
                "V2.58 Modify hwIFExtSwitchPortIndex SYNTAX"
            REVISION     "201503272026Z"
            DESCRIPTION 
                "V2.57 Modify hwIfMonitorCrcErrorThreshold hwIfMonitorSdhErrorThreshold hwIfMonitorPauseFrameThreshold hwIfMonitorSymbolErrorThreshold SYNTAX"
            REVISION     "201503262026Z"
            DESCRIPTION 
                "V2.56 hwLogicIfType add nve, vt, fcoe."
            REVISION     "201502032026Z" 
            DESCRIPTION 
                "Some attribute of interface extended content, information of 
                interface IP, interface of trunk interface."
            REVISION     "201501052026Z" 
            DESCRIPTION 
                 "V2.55 hwIfMonitorBadBytesErrorRising hwIfMonitorPauseFrameRising add hwIfMonitorName." 
            REVISION     "201412252026Z" 
            DESCRIPTION 
                 "V2.54 add hwIFExtSuppressStatusIPv6 hwIfControlFlapSuppressIPv6 hwIfControlFlapResumeIPv6." 
            REVISION     "201411052026Z"
            DESCRIPTION
                "V2.53 add hwExtTrunkWorkingStatusChange."
            REVISION     "201409102026Z"
            DESCRIPTION
                "V2.52 Update hwTrunkIfModel, add object-type l4(20)."
            REVISION     "201408132026Z"
            DESCRIPTION
                "V2.51 add hwTrunkMemberCountUpperThreshold hwTrunkMemberCountLowerThreshold hwExtTrunkMemberCountExceedThreshold hwExtTrunkMemberCountExceedThresholdResume range."
            REVISION     "201408062026Z"
            DESCRIPTION
                "V2.50 modify hwIfDiffServMode range."
            REVISION     "201406092026Z"
            DESCRIPTION
                "Modify hwTrunkMemCount."
            REVISION     "201406062026Z"
            DESCRIPTION
                "Add hwTrunkMemCount and hwTrunkCount."
            REVISION     "201403252026Z"
            DESCRIPTION
                "Add hwIfEfmDown and hwIfEfmUp."
            REVISION     "201403061626Z"
            DESCRIPTION
                "Add hwLacpPDUChange and hwLacpPDUChangeResume."

            REVISION     "201403030939Z"
            DESCRIPTION
                "modify hwIpv6IfChangeDownReason  ."

            REVISION         "201402111900Z" 
            DESCRIPTION  
                "Modify description" 
            REVISION         "201402101900Z" 
            DESCRIPTION  
                "Update hwTrunkIfWorkingMode, add object-type port-standbyMode(5)." 
            REVISION         "201402071900Z" 
            DESCRIPTION  
                "add hwIfIpAddrTable" 
            REVISION     "201401261900Z"
            DESCRIPTION 
                "Update hwTrunkSelectStatus, add trunkIndep(3)."
            REVISION     "201401181200Z"
            DESCRIPTION
                "Update hwLogicIfType , add object-type globalVe(26), subGlobalVe(27)."
            REVISION     "201401161900Z"
            DESCRIPTION
                "Update hwTrunkIfWorkingMode, modify object-type lacpStaticMode(3) to lacpMode(3) and add object-type lacpCompatibleMode(4)."
            REVISION     "201401131600Z"
            DESCRIPTION
                "Update hwTrunkIfWorkingMode, modify object-type lacpMode(3) to lacpStaticMode(3); Update hwLogicIfType, add object-type subPosFr(25)." 
            REVISION     "201309051400Z"
            DESCRIPTION
                "Add hwLacpPartnerExpiredLoss."     
            REVISION     "201307021400Z"
            DESCRIPTION
                "Add hwTrunkMapTable and hwIFExtSwitchPortIndex."
            REVISION     "201306081830Z"
            DESCRIPTION
                "Update the hwLinkDownReason, add from veFlowDown to negotiationUnsupported."
            REVISION     "201306051100Z"
            DESCRIPTION
                "Update the hwLogicIfType, add globalImaGroup and subGlobalImaGroup."
            REVISION     "201305212000Z"
            DESCRIPTION
                "Update the hwLogicIfType, add remoteAp, vBridge,atmBundle and mtunnel."
            REVISION     "201301220000Z"
            DESCRIPTION
                "V2.30, modify hwVTrunkIfID range."
            ::= { hwDatacomm 41 }
        
    
--
-- Textual conventions
--
    
--  Textual Convention
        EnabledStatus ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "A simple status value for the object."
            SYNTAX INTEGER
                {
                enabled(1),
                disabled(2)
                }

        SnmpPasswdString ::= TEXTUAL-CONVENTION
            DISPLAY-HINT 
                "16a"
            STATUS current
            DESCRIPTION 
                "The password string"
            SYNTAX OCTET STRING (SIZE (0..16))
            
        HWDirectionType ::= TEXTUAL-CONVENTION
        STATUS current
        DESCRIPTION 
            "The Direction Flux Limit apply on."
        SYNTAX INTEGER
            {
            inbound(1),
            outbound(2)
            }

--  ============================================================================                        
-- interface IP address group
-- ============================================================================
        HwIpAddressType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "The type of IP address."
            SYNTAX INTEGER
                {
                primary(1),
                sub(2)
                }

    
--
-- Node definitions
--
    
        hwIFExtObjects OBJECT IDENTIFIER ::= { hwIFExtMib 1 }

        
--  ====================================================================    
-- basic interface extended definition group
-- ====================================================================
        hwIFExtBase OBJECT IDENTIFIER ::= { hwIFExtObjects 1 }

        
--  ===========================================================================
-- interface extended group
-- ============================================================================  
        hwIFExtTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIFExtEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "hwIFExtIndex is the unique identifier of the table. It is written when the table is created and cannot be modified later.
                 The table describes the extended attributes of an interface, such as the Layer 2/Layer 3 identifier and frame type of the interface."
            ::= { hwIFExtBase 1 }

        
        hwIFExtEntry OBJECT-TYPE
            SYNTAX HwIFExtEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entries of private extended interface table."
            INDEX { hwIFExtIndex }
            ::= { hwIFExtTable 1 }

        
        HwIFExtEntry ::=
            SEQUENCE { 
                hwIFExtIndex
                    InterfaceIndex,
                hwIFExtLayer
                    INTEGER,
                hwIFExtFrameType
                    INTEGER,
                hwIFExtFlowStatInterval
                    Integer32,
                hwIFExtFlushReceiveEnable
                    EnabledStatus,
                hwIFExtFlushVlanId
                    VlanIdOrNone,
                hwIFExtFlushPasswd
                    SnmpPasswdString,
                hwIFExtFlowStatus
                    INTEGER,                    
                hwIFExtMtu
                    Integer32,
                hwIFExtMacAddr
                    PhysAddress,
                hwIFExtBlockPriority
                    Integer32,
                hwIFExtMacShift
                    INTEGER,
                hwIFExtSuppressStatus
                    INTEGER,
                hwIFExtPoisonReverse
                    EnabledStatus,
                hwIFExtInputPktRate
                    Gauge32, 
                hwIFExtInputHighPktRate
                    Gauge32,
                hwIFExtOutputPktRate
                    Gauge32,
                hwIFExtOutputHighPktRate
                    Gauge32,
                hwIFExtInputOctetRate
                    Gauge32,
                hwIFExtInputHighOctetRate
                    Gauge32,
                hwIFExtOutputOctetRate
                    Gauge32,
                hwIFExtOutputHighOctetRate
                    Gauge32,
                hwIFExtSwitchPortIndex
                    Integer32,
                hwIFExtSuppressStatusIPv6
                    INTEGER,
                hwIFExtInputPkts
                    Counter64,
                hwIFExtOutputPkts
                    Counter64,
                hwIFExtInputUnicastBitRate
                    CounterBasedGauge64,
                hwIFExtOutputUnicastBitRate
                    CounterBasedGauge64,
                hwIFExtInputMulticastBitRate
                    CounterBasedGauge64,
                hwIFExtOutputMulticastBitRate
                    CounterBasedGauge64,
                hwIFExtInputBroadcastBitRate
                    CounterBasedGauge64,
                hwIFExtOutputBroadcastBitRate
                    CounterBasedGauge64,
                hwIFExtInputUnicastPktRate
                    CounterBasedGauge64,
                hwIFExtOutputUnicastPktRate
                    CounterBasedGauge64,
                hwIFExtInputMulticastPktRate
                    CounterBasedGauge64,
                hwIFExtOutputMulticastPktRate
                    CounterBasedGauge64,
                hwIFExtInputBroadcastPktRate
                    CounterBasedGauge64,
                hwIFExtOutputBroadcastPktRate
                    CounterBasedGauge64,
                hwIFExtInputBitRate
                    CounterBasedGauge64,
                hwIFExtOutputBitRate
                    CounterBasedGauge64,
                hwIFExtDampStatus
                    INTEGER,
                hwIFExtPhyStatusUpTime
                    Unsigned32
             }

        hwIFExtIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the interface index."
            ::= { hwIFExtEntry 1 }

        
        hwIFExtLayer OBJECT-TYPE
            SYNTAX INTEGER
                {
                layer2(1),
                layer3(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the Layer 2/Layer 3 switching flag of an interface.
                 According to the switching command, note the following:
                 When Layer2(1) is 1, the interface switches from Layer 3 mode to Layer 2 mode. When Layer3(2) is 2, the interface switches from Layer 2 mode to Layer 3 mode."
            ::= { hwIFExtEntry 2 }

        
        hwIFExtFrameType OBJECT-TYPE
            SYNTAX INTEGER
                {
                ethernetII(1),
                ethernetSnap(2),
                ethernet8022(3),
                ethernet8023(4),
                other(5)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the type of frames that a VLAN virtual interface receives. 
                 Frame formats include ethernetII(1), ethernetII(2), ethernet8022(3), and ethernet8023. 
                 The value for the ethernetII(1) format is 1. Currently, only ethernetII(1) is supported."
            ::= { hwIFExtEntry 3 }

        
-- metric: second
        hwIFExtFlowStatInterval OBJECT-TYPE
            SYNTAX Integer32 (0..600)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the interval for collecting traffic statistics for an interface. The value ranges from 0 to 600, in seconds. The default value is 300."
            DEFVAL { 300 }
            ::= { hwIFExtEntry 4 }

        
        hwIFExtFlushReceiveEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Whether or not permit the port to take over FLUSHDUs. Once it is enabled,
                the port will take over SmartLink FlushDUs from related vlans so as to delete
                the MAC forwarding table in the local equipment."
            DEFVAL { disabled }
            ::= { hwIFExtEntry 5 }

        
        hwIFExtFlushVlanId OBJECT-TYPE
            SYNTAX VlanIdOrNone
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The ID of Vlan the port belongs to.When it is 0, it indicates 
                the function of receiving FlushPDU is disabled."
            DEFVAL { 0 }
            ::= { hwIFExtEntry 6 }

        
        hwIFExtFlushPasswd OBJECT-TYPE
            SYNTAX SnmpPasswdString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "A 16-octet read-write value used to define the password for negotiation."
            DEFVAL { '00'h }
            ::= { hwIFExtEntry 7 }
            
        hwIFExtFlowStatus OBJECT-TYPE
            SYNTAX INTEGER
            {
              flowUp(1),
              flowDown(2)
            }       
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
                "This object indicates the traffic status of an interface."
            ::= { hwIFExtEntry 8 }            
            
        hwIFExtMtu OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION 
                "This object indicates the MTU of an interface.
                 The MTU value varies according to interface types. The default MTU value also varies.
                 The MTU value can be modified."
            ::= { hwIFExtEntry 9 }  
            
        hwIFExtMacAddr  OBJECT-TYPE
               SYNTAX PhysAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the MAC address of an interface."
            ::= { hwIFExtEntry 10 }

        hwIFExtBlockPriority OBJECT-TYPE
            SYNTAX Integer32 (0..255)      
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION 
           "The blocked priority of the interface."
            ::= { hwIFExtEntry 11 }            

        hwIFExtMacShift OBJECT-TYPE
            SYNTAX INTEGER
            {
              normal(1),
              macShift(2)
            }       
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION 
           "The mac-shift state of the interface.
               1:normal
               2:monitor mac-shift"
            ::= { hwIFExtEntry 12 }    
            
        hwIFExtSuppressStatus OBJECT-TYPE
            SYNTAX INTEGER
            {
              unsuppress(0),
              suppress(1)
            }       
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
                "This object indicates the suppression status of an interface.
                 0: The interface is not suppressed.
                 1: The interface is suppressed."
            ::= { hwIFExtEntry 13 }  
    
        hwIFExtPoisonReverse OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Whether or not enable Split-horizon forwarding "
            DEFVAL { disabled }
            ::= { hwIFExtEntry 14 }

        hwIFExtInputPktRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the incoming packets on an interface. 
                 The rate value is 64 bits. hwIFExtInputPktRate indicates the lower 32 bits, 
                 while hwIFExtInputHighPktRate indicates the higher 32 bits. "
            ::= { hwIFExtEntry 15 }
            
        hwIFExtInputHighPktRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the incoming packets on an interface. 
                 The rate value is 64 bits. hwIFExtInputPktRate indicates the lower 32 bits, 
                 while hwIFExtInputHighPktRate indicates the higher 32 bits. "
            ::= { hwIFExtEntry 16 }            

        hwIFExtOutputPktRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the outgoing packets on an interface. 
                 The rate value is 64 bits. hwIFExtOutputPktRate indicates the lower 32 bits, 
                 while hwIFExtOutputHighPktRate indicates the higher 32 bits. "
            ::= { hwIFExtEntry 17 }            
            
        hwIFExtOutputHighPktRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the outgoing packets on an interface. 
                 The rate value is 64 bits. hwIFExtOutputPktRate indicates the lower 32 bits, 
                 while hwIFExtOutputHighPktRate indicates the higher 32 bits. "
            ::= { hwIFExtEntry 18 }            
            
        hwIFExtInputOctetRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the octet rate of the incoming traffic on an interface. 
                 The rate value is 64 bits. hwIFExtInputOctetRate indicates the lower 32 bits, 
                 while hwIFExtInputHighOctetRate indicates the higher 32 bits. "
            ::= { hwIFExtEntry 19 }            
            
        hwIFExtInputHighOctetRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the octet rate of the incoming traffic on an interface. 
                 The rate value is 64 bits. hwIFExtInputOctetRate indicates the lower 32 bits, 
                 while hwIFExtInputHighOctetRate indicates the higher 32 bits."
            ::= { hwIFExtEntry 20 }
            
        hwIFExtOutputOctetRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the octet rate of the outgoing traffic on an interface. 
                 The rate value is 64 bits. hwIFExtOutputOctetRate indicates the lower 32 bits, 
                 while hwIFExtOutputHighOctetRate indicates the higher 32 bits. "
            ::= { hwIFExtEntry 21 }            
            
        hwIFExtOutputHighOctetRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the octet rate of the outgoing traffic on an interface. 
                 The rate value is 64 bits. hwIFExtOutputOctetRate indicates the lower 32 bits, 
                 while hwIFExtOutputHighOctetRate indicates the higher 32 bits. "
            ::= { hwIFExtEntry 22 }

         hwIFExtSwitchPortIndex OBJECT-TYPE   
            SYNTAX  Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
                "Number of layer2 interface port, a unique value,
                for each port.It is recommended that values are assigned 
                contiguously starting from 1. If the return value is -1, this interface is not a Layer 2 interface."
                ::= { hwIFExtEntry 23 }

        hwIFExtSuppressStatusIPv6 OBJECT-TYPE
            SYNTAX INTEGER
            {
              unsuppress(0),
              suppress(1)
            }       
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
           "The suppress state of the interface(IPv6).
               0:unsuppress
               1:suppress"
            ::= { hwIFExtEntry 24 }  

        hwIFExtInputPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of packets received on the interface. 
                 The counter value is 64 bits. "
            ::= { hwIFExtEntry 25 }            
            
        hwIFExtOutputPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of packets transmitted out of the interface. 
                 The counter value is 64 bits. "
            ::= { hwIFExtEntry 26 }
            
        hwIFExtInputUnicastBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the incoming unicast traffic on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 27 }
            
        hwIFExtOutputUnicastBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the outgoing unicast traffic on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 28 }
            
        hwIFExtInputMulticastBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the incoming multicast traffic on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 29 }
            
        hwIFExtOutputMulticastBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the outgoing multicast traffic on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 30 }
            
        hwIFExtInputBroadcastBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the incoming broadcast traffic on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 31 }
            
        hwIFExtOutputBroadcastBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the outgoing broadcast traffic on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 32 }
            
        hwIFExtInputUnicastPktRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the incoming unicast packets on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 33 }
            
        hwIFExtOutputUnicastPktRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the outgoing unicast packets on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 34 }
            
        hwIFExtInputMulticastPktRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the incoming multicast packets on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 35 }
            
        hwIFExtOutputMulticastPktRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the outgoing multicast packets on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 36 }
            
        hwIFExtInputBroadcastPktRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the incoming broadcast packets on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 37 }
            
        hwIFExtOutputBroadcastPktRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the outgoing broadcast packets on an interface. 
                The rate value is 64 bits. "
            ::= { hwIFExtEntry 38 }

        hwIFExtInputBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the incoming traffic on an interface. 
                 The rate value is 64 bits. "
            ::= { hwIFExtEntry 39 }            

        hwIFExtOutputBitRate OBJECT-TYPE
            SYNTAX CounterBasedGauge64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the bit rate of the outgoing traffic on an interface. 
                 The rate value is 64 bits.  "
            ::= { hwIFExtEntry 40 }            

        hwIFExtDampStatus  OBJECT-TYPE
            SYNTAX INTEGER
            {
              unsuppress(0),
              suppress(1)
            }       
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
               "This object indicates the physical suppression status of an interface.
                0: The interface is not suppressed.
                1: The interface is suppressed."
            ::= { hwIFExtEntry 41 }  

        hwIFExtPhyStatusUpTime OBJECT-TYPE
            SYNTAX Unsigned32
            UNITS "seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
               "This object indicates the accumulated time in seconds during which the physical status of the interface remains up after its creation."
            ::= { hwIFExtEntry 42 }

        hwIFExtPhyStatus OBJECT-TYPE
            SYNTAX INTEGER { up(1), down(2) }
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "The physical status of member interface."
            ::= { hwIFExtBase 2 }


        hwIFExtMemberOf OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "The description of the main interface which has the membership with the member interface."
            ::= { hwIFExtBase 3 }

    hwLinkModeChangeAutoCreateIfTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwLinkModeChangeAutoCreateIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Interface extended table of private mib."
            ::= { hwIFExtBase 4 }

        
        hwLinkModeChangeAutoCreateIfEntry OBJECT-TYPE
            SYNTAX HwLinkModeChangeAutoCreateIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entries of private extended interface table."
            INDEX { hwAutoIfIndex }
            ::= { hwLinkModeChangeAutoCreateIfTable 1 } 
            
        HwLinkModeChangeAutoCreateIfEntry ::=
            SEQUENCE { 
                hwAutoIfIndex
                    InterfaceIndex,
                hwNewIfTimeslot
                    BITS
             } 
             
        hwAutoIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Index of extended table of interface."
            ::= { hwLinkModeChangeAutoCreateIfEntry 1 }
            
        hwNewIfTimeslot OBJECT-TYPE
            SYNTAX BITS{
                      timeslot0(0),
                      timeslot1(1),
                      timeslot2(2),
                      timeslot3(3),
                      timeslot4(4),
                      timeslot5(5),
                      timeslot6(6),
                      timeslot7(7),
                      timeslot8(8),
                      timeslot9(9),
                      timeslot10(10),
                      timeslot11(11),
                      timeslot12(12),
                      timeslot13(13),
                      timeslot14(14),
                      timeslot15(15),
                      timeslot16(16),
                      timeslot17(17),
                      timeslot18(18),
                      timeslot19(19),
                      timeslot20(20),
                      timeslot21(21),
                      timeslot22(22),
                      timeslot23(23),
                      timeslot24(24),
                      timeslot25(25),
                      timeslot26(26),
                      timeslot27(27),
                      timeslot28(28),
                      timeslot29(29),
                      timeslot30(30),
                      timeslot31(31)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The time slot of the TDM interface:
                      timeslot0(0):The time slot 0 on the TDM interface,
                      timeslot1(1):The time slot 1 on the TDM interface,
                      timeslot2(2):The time slot 2 on the TDM interface,
                      timeslot3(3):The time slot 3 on the TDM interface,
                      timeslot4(4):The time slot 4 on the TDM interface,
                      timeslot5(5):The time slot 5 on the TDM interface,
                      timeslot6(6):The time slot 6 on the TDM interface,
                      timeslot7(7):The time slot 7 on the TDM interface,
                      timeslot8(8):The time slot 8 on the TDM interface,
                      timeslot9(9):The time slot 9 on the TDM interface,
                      timeslot10(10):The time slot 10 on the TDM interface,
                      timeslot11(11):The time slot 11 on the TDM interface,
                      timeslot12(12):The time slot 12 on the TDM interface,
                      timeslot13(13):The time slot 13 on the TDM interface,
                      timeslot14(14):The time slot 14 on the TDM interface,
                      timeslot15(15):The time slot 15 on the TDM interface,
                      timeslot16(16):The time slot 16 on the TDM interface,
                      timeslot17(17):The time slot 17 on the TDM interface,
                      timeslot18(18):The time slot 18 on the TDM interface,
                      timeslot19(19):The time slot 19 on the TDM interface,
                      timeslot20(20):The time slot 20 on the TDM interface,
                      timeslot21(21):The time slot 21 on the TDM interface,
                      timeslot22(22):The time slot 22 on the TDM interface,
                      timeslot23(23):The time slot 23 on the TDM interface,
                      timeslot24(24):The time slot 24 on the TDM interface,
                      timeslot25(25):The time slot 25 on the TDM interface,
                      timeslot26(26):The time slot 26 on the TDM interface,
                      timeslot27(27):The time slot 27 on the TDM interface,
                      timeslot28(28):The time slot 28 on the TDM interface,
                      timeslot29(29):The time slot 29 on the TDM interface,
                      timeslot30(30):The time slot 30 on the TDM interface,
                      timeslot31(31):The time slot 31 on the TDM interface."
            ::= { hwLinkModeChangeAutoCreateIfEntry 2 }

          hwIFExtPhyNumber OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of the physical interfaces."
            ::= { hwIFExtBase 5 }
        
        hwRemoteIfTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwRemoteIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "hwRemoteIfTable is a virtual access interface table."
            ::= { hwIFExtBase 6 }
        
        hwRemoteIfEntry OBJECT-TYPE
            SYNTAX HwRemoteIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entries of private extended remote interface table."
            INDEX { hwRemoteIfIndex }
            ::= { hwRemoteIfTable 1 } 
            
        HwRemoteIfEntry ::=
            SEQUENCE { 
                hwRemoteIfIndex
                    InterfaceIndex,
                hwRemoteIfInOctets
                    Counter64,
                hwRemoteIfInPkts
                    Counter64,
                hwRemoteIfInUcastPkts
                    Counter64,
                hwRemoteIfInMulticastPkts
                    Counter64,
                hwRemoteIfInBroadcastPkts
                    Counter64,
                hwRemoteIfInOctetRate
                    Counter64,
                hwRemoteIfInPktRate
                    Counter64,
                hwRemoteIfInErrorPkts
                    Counter64,                    
                hwRemoteIfInDiscardPkts
                    Counter64,
                hwRemoteIfOutOctets
                    Counter64,
                hwRemoteIfOutPkts
                    Counter64,
                hwRemoteIfOutUcastPkts
                    Counter64, 
                hwRemoteIfOutMulticastPkts
                    Counter64,
                hwRemoteIfOutBroadcastPkts
                    Counter64,
                hwRemoteIfOutOctetRate
                    Counter64,
                hwRemoteIfOutPktRate
                    Counter64,
                hwRemoteIfOutErrorPkts
                    Counter64,
                hwRemoteIfOutDiscardPkts
                    Counter64
             }

        hwRemoteIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Index of extended table of remote interface."
            ::= { hwRemoteIfEntry 1 }
            
        hwRemoteIfInOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets received on the interface."
            ::= { hwRemoteIfEntry 2 }
            
        hwRemoteIfInPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of packets received on the interface."
            ::= { hwRemoteIfEntry 3 }
            
        hwRemoteIfInUcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of unicast packets received on an interface."
            ::= { hwRemoteIfEntry 4 }
            
        hwRemoteIfInMulticastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of multicast packets received on an interface."
            ::= { hwRemoteIfEntry 5 }
            
        hwRemoteIfInBroadcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of broadcast packets received on an interface."
            ::= { hwRemoteIfEntry 6 }
            
        hwRemoteIfInOctetRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the octet rate of the incoming traffic on an interface."
            ::= { hwRemoteIfEntry 7 }
            
        hwRemoteIfInPktRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the incoming packets on an remote interface. 
                The rate value is 64 bits."
            ::= { hwRemoteIfEntry 8 }
            
        hwRemoteIfInErrorPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of error packets received on an interface."
            ::= { hwRemoteIfEntry 9 }
            
        hwRemoteIfInDiscardPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of received packets discarded on an interface."
            ::= { hwRemoteIfEntry 10 }
            
        hwRemoteIfOutOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "The total number of octets transmitted out of the interface."
            ::= { hwRemoteIfEntry 11 }
            
        hwRemoteIfOutPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of packets transmitted out of the interface."
            ::= { hwRemoteIfEntry 12 }
            
        hwRemoteIfOutUcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of unicast packets sent on an interface."
            ::= { hwRemoteIfEntry 13 }
            
        hwRemoteIfOutMulticastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of multicast packets sent on an interface."
            ::= { hwRemoteIfEntry 14 }
            
        hwRemoteIfOutBroadcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of broadcast packets sent on an interface."
            ::= { hwRemoteIfEntry 15 }
            
        hwRemoteIfOutOctetRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the octet rate of the outgoing traffic on an interface."
            ::= { hwRemoteIfEntry 16 }
            
        hwRemoteIfOutPktRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the rate of the outgoing packets on an interface."
            ::= { hwRemoteIfEntry 17 }
            
        hwRemoteIfOutErrorPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of error packets sent on an interface."
            ::= { hwRemoteIfEntry 18 }
            
        hwRemoteIfOutDiscardPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "Number of sent packets discarded on an interface."
            ::= { hwRemoteIfEntry 19 }
                    
        hwIFExtDualFlowRateTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIFExtDualFlowRateEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Interface Second Cycle Rate Table."
            ::= { hwIFExtBase 7 }

        hwIFExtDualFlowRateEntry OBJECT-TYPE
            SYNTAX HwIFExtDualFlowRateEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "An interface second cycle rate entry."
            INDEX { hwIFExtDualFlowIndex }
            ::= { hwIFExtDualFlowRateTable 1 }

        HwIFExtDualFlowRateEntry ::=
            SEQUENCE {         
                hwIFExtDualFlowIndex
                    InterfaceIndex,
                hwIFExtDualFlowInputPktRate
                    Counter64,
                hwIFExtDualFlowOutputPktRate
                    Counter64,
                hwIFExtDualFlowInputOctetRate
                    Counter64,
                hwIFExtDualFlowOutputOctetRate
                    Counter64
             }
    
        hwIFExtDualFlowIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index value that uniquely identifies the interface to which this entry is applicable."
            ::= { hwIFExtDualFlowRateEntry 1 }
            
        hwIFExtDualFlowInputPktRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the second rate of the incoming packets on an interface."
            ::= { hwIFExtDualFlowRateEntry 2 } 
            
        hwIFExtDualFlowOutputPktRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the second rate of the outgoing packets on an interface."
            ::= { hwIFExtDualFlowRateEntry 3 }
            
        hwIFExtDualFlowInputOctetRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the second octet rate of the incoming traffic on an interface."
            ::= { hwIFExtDualFlowRateEntry 4 }
            
        hwIFExtDualFlowOutputOctetRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the second octet rate of the outgoing traffic on an interface."
            ::= { hwIFExtDualFlowRateEntry 5 }                        

                    
        hwInterfaceIp OBJECT IDENTIFIER ::= { hwIFExtObjects 2 }

        
        hwIfIpTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfIpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "hwIfIpTable describes information about IP addresses configured for an interface. On an interface, one primary IP address and multiple secondary IP addresses can be configured. You can create, delete, and view information about the IP addresses of an interface."
            ::= { hwInterfaceIp 1 }

        
        hwIfIpEntry OBJECT-TYPE
            SYNTAX HwIfIpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Interface IP address configuration  "
            INDEX { hwIpAdEntAddr }
            ::= { hwIfIpTable 1 }

        
        HwIfIpEntry ::=
            SEQUENCE { 
                hwIpAdEntAddr
                    IpAddress,
                hwIpAdEntIfIndex
                    Integer32,
                hwIpAdEntNetMask
                    IpAddress,
                hwIpAdEntBcastAddr
                    Integer32,
                hwIpAdEntReasmMaxSize
                    Integer32,
                hwIpAdEntAddressType
                    HwIpAddressType,
                hwIfIpMethod
                    INTEGER,
                hwIpAdEntAddrStatus
                    RowStatus
             }

        hwIpAdEntAddr OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The IP address to which this entry's addressing
                information pertains."
            ::= { hwIfIpEntry 1 }

        
        hwIpAdEntIfIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The index value which uniquely identifies the
                interface to which this entry is applicable.  The
                interface identified by a particular value of this
                index is the same interface as identified by the
                same value of ifIndex."
            ::= { hwIfIpEntry 2 }

        
        hwIpAdEntNetMask OBJECT-TYPE
            SYNTAX IpAddress
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The subnet mask associated with the IP address of
                this entry.  The value of the mask is an IP
                address with all the network bits set to 1 and all
                the host bits set to 0."
            ::= { hwIfIpEntry 3 }

        
        hwIpAdEntBcastAddr OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of the least-significant bit in the IP
                broadcast address used for sending datagrams on
                the (logical) interface associated with the IP
                address of this entry.  For example, when the
                Internet standard all-ones broadcast address is
                used, the value will be 1.  This value applies to
                both the subnet and network broadcasts addresses
                used by the entity on this (logical) interface."
            ::= { hwIfIpEntry 4 }

        
        hwIpAdEntReasmMaxSize OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The size of the largest IP datagram which this
                entity can re-assemble from incoming IP fragmented
                datagram received on this interface."
            ::= { hwIfIpEntry 5 }

        
        hwIpAdEntAddressType OBJECT-TYPE
            SYNTAX HwIpAddressType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The flag indicates whether the IP address is primary IP address.
                One interface has only one primary IP address, but can have many sub IP address.
                So when add a primary IP address to an interface that has already a primary IP address,
                the new primary address will replace old primary address and the old primary address will be deleted."
            ::= { hwIfIpEntry 6 }

        
hwIfIpMethod OBJECT-TYPE
            SYNTAX INTEGER
                {
                 assignedIp(1),
                 dhcpIp(2),
                 bootpIp(3),
                 other(4),
                 linklayer(5),
                 random(6)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Interface IP address acquiring method.

                 assignedIp(1) indicates that the address was manually configured
                 to a specified address, for example, by user configuration.
                 
                 dhcpIp(2) indicates an address that was assigned to this
                 system by a DHCP server.
                 
                 bootpIp(3) indicates an address that was assigned to this
                 system by the bootp protocol.
                 
                 other(4) indicates an address created by another method.
                 
                 linklayer(5) indicates an address created by IPv6 stateless
                 auto-configuration.

                 random(6) indicates an address chosen by the system at
                 random, for example, an IPv4 address within 169.254/16, or an RFC
                 3041 privacy address."
            ::= { hwIfIpEntry 7 }
        
        hwIpAdEntAddrStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The row status variable."
            ::= { hwIfIpEntry 8 }  
            
            
        hwIfIpUnnumberedTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfIpUnnumberedEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION 
                "This table lists information about the unnumbered IP address of the interface that borrows an IP address from another interface.
You can create, delete, and view information about the borrowed IP address of an interface."
            ::= { hwInterfaceIp  2}
                    
                    
        hwIfIpUnnumberedEntry OBJECT-TYPE
            SYNTAX HwIfIpUnnumberedEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION 
                " Interface IP address unnumbered configuration  "
            INDEX   { hwUnnumberedIfIndex } 
            ::= {hwIfIpUnnumberedTable 1}
                    
                    
        HwIfIpUnnumberedEntry    ::=
        SEQUENCE {  
            hwUnnumberedIfIndex
                InterfaceIndex,
            hwLendIfIndex 
                InterfaceIndex,
            hwLendIpAddr 
                IpAddress,
            hwLendIpAddrNetMask  
                IpAddress,
            hwUnnumberedRowStatus
                RowStatus
    
            }    
            
         hwUnnumberedIfIndex OBJECT-TYPE  
            SYNTAX  InterfaceIndex
            MAX-ACCESS  not-accessible
            STATUS  current
            DESCRIPTION
                    "The index value of the unnumbered interface."
            ::= { hwIfIpUnnumberedEntry 1 }
               
               
         hwLendIfIndex OBJECT-TYPE    
            SYNTAX  InterfaceIndex
            MAX-ACCESS  read-create
            STATUS  current
            DESCRIPTION
                    "The index value of the lend interface."
            ::= { hwIfIpUnnumberedEntry 11 }
                 
                 
         hwLendIpAddr OBJECT-TYPE      
            SYNTAX  IpAddress
            MAX-ACCESS  read-only
            STATUS  current
            DESCRIPTION
                    "The IP address of the lend interface."
            ::= { hwIfIpUnnumberedEntry 12 }
                 
                 
         hwLendIpAddrNetMask OBJECT-TYPE    
            SYNTAX  IpAddress
            MAX-ACCESS  read-only
            STATUS  current
            DESCRIPTION
                    "The IP address mask of the lend interface."
            ::= { hwIfIpUnnumberedEntry 13 }
                    
                    
         hwUnnumberedRowStatus OBJECT-TYPE        
            SYNTAX  RowStatus
            MAX-ACCESS  read-create
            STATUS  current
            DESCRIPTION
                    "The row status variable."
            ::= { hwIfIpUnnumberedEntry 51 } 
            

         hwIfIpAddrTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfIpAddrEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table contains addressing information relevant to the
                 entity's interfaces."
            ::= { hwInterfaceIp 3 }	

        hwIfIpAddrEntry OBJECT-TYPE
            SYNTAX HwIfIpAddrEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Interface IP address configuration  "
            INDEX { hwIfIpAddrEntIfIndex, hwIfIpAddrEntAddr }
            ::= { hwIfIpAddrTable 1 }

        
        HwIfIpAddrEntry ::=
            SEQUENCE { 
            hwIfIpAddrEntIfIndex      
                InterfaceIndex,
            hwIfIpAddrEntAddr         
                IpAddress,
            hwIfIpAddrEntType         
                INTEGER,
            hwIfIpAddrEntPrefix       
                IpAddress,
            hwIfIpAddrEntOrigin       
                INTEGER,
            hwIfIpAddrEntStatus       
                INTEGER,
            hwIfIpAddrEntCreated      
                TimeStamp,
            hwIfIpAddrEntLastChanged  
                TimeStamp,		
            hwIfIpAddrEntBcastAddr
                Integer32,
            hwIfIpAddrEntReasmMaxSize
                Integer32,
            hwIfIpAddrEntAddrType
                HwIpAddressType,
            hwIfIpAddrEntVpn
                DisplayString
            }

 
        hwIfIpAddrEntIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS accessible-for-notify
            STATUS     current
            DESCRIPTION
                "The index value that uniquely identifies the interface to
                which this entry is applicable.  The interface identified by
                a particular value of this index is the same interface as
                identified by the same value of the IF-MIB's ifIndex."
            ::= { hwIfIpAddrEntry 1 }

        hwIfIpAddrEntAddr OBJECT-TYPE
            SYNTAX     IpAddress
            MAX-ACCESS accessible-for-notify
            STATUS     current
            DESCRIPTION
                "The IP address to which this entry's addressing
                information pertains."
            ::= { hwIfIpAddrEntry 2 }

        hwIfIpAddrEntType OBJECT-TYPE
            SYNTAX     INTEGER {
                                unicast(1),
                                anycast(2),
                                broadcast(3)
                            }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The type of address."
            ::= { hwIfIpAddrEntry 3 }

        hwIfIpAddrEntPrefix OBJECT-TYPE
            SYNTAX     IpAddress
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The subnet mask associated with the IP address of
                this entry.  The value of the mask is an IP
                address with all the network bits set to 1 and all
                the host bits set to 0."
            ::= { hwIfIpAddrEntry 4 }

        hwIfIpAddrEntOrigin OBJECT-TYPE
            SYNTAX  INTEGER{
                            assignedIp(1),
                            dhcpIp(2),
                            bootpIp(3),
                            other(4),
                            linklayer(5),
                            random(6)
                        }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Interface IP address acquiring method.

                assignedIp(1) indicates that the address was manually configured
                to a specified address, for example, by user configuration.

                dhcpIp(2) indicates an address that was assigned to this
                system by a DHCP server.

                bootpIp(3) indicates an address that was assigned to this
                system by the bootp protocol.

                other(4) indicates an address created by another method.

                linklayer(5) indicates an address created by IPv6 stateless
                auto-configuration.

                random(6) indicates an address chosen by the system at
                random, for example, an IPv4 address within 169.254/16, or an RFC
                3041 privacy address."
            ::= { hwIfIpAddrEntry 5 }

        hwIfIpAddrEntStatus OBJECT-TYPE
            SYNTAX     INTEGER {
                                    preferred(1),
                                    deprecated(2),
                                    invalid(3),
                                    inaccessible(4),
                                    unknown(5),
                                    tentative(6),
                                    duplicate(7),
                                    optimistic(8)
                            }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The status of the address, describing if the address can be
                used for communication.

                In the absence of other information, an IPv4 address is
                always preferred(1)."
            ::= { hwIfIpAddrEntry 6 }

        hwIfIpAddrEntCreated OBJECT-TYPE
            SYNTAX     TimeStamp
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The value of sysUpTime at the time this entry was created.
                If this entry was created prior to the last re-
                initialization of the local network management subsystem,
                then this object contains a zero value."
            ::= { hwIfIpAddrEntry 7 }

        hwIfIpAddrEntLastChanged OBJECT-TYPE
            SYNTAX     TimeStamp
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The value of sysUpTime at the time this entry was last
                updated.  If this entry was updated prior to the last re-
                initialization of the local network management subsystem,
                then this object contains a zero value."
            ::= { hwIfIpAddrEntry 8 }

        hwIfIpAddrEntBcastAddr OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of the least-significant bit in the IP
                broadcast address used for sending datagrams on
                the (logical) interface associated with the IP
                address of this entry.  For example, when the
                Internet standard all-ones broadcast address is
                used, the value will be 1.  This value applies to
                both the subnet and network broadcasts addresses
                used by the entity on this (logical) interface."
            ::= { hwIfIpAddrEntry 9 }
        
        hwIfIpAddrEntReasmMaxSize OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The size of the largest IP datagram which this
                entity can re-assemble from incoming IP fragmented
                datagram received on this interface."
            ::= { hwIfIpAddrEntry 10 }

        hwIfIpAddrEntAddrType OBJECT-TYPE
            SYNTAX HwIpAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The flag indicates whether the IP address is primary IP address."
            ::= { hwIfIpAddrEntry 11 }

        hwIfIpAddrEntVpn OBJECT-TYPE
            SYNTAX DisplayString (SIZE(0..31))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The vpn name of ip address.The public address does not have any value."
            ::= { hwIfIpAddrEntry 12 }

--  ============================================================================
-- interface trunk attribute group
-- ============================================================================
        hwTrunkAttr OBJECT IDENTIFIER ::= { hwIFExtObjects 3 }

        
        hwTrunkIfMax OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of Trunk interface"
            ::= { hwTrunkAttr 1 }

        
        hwTrunkNextIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The nextindex of Trunk interface, that means its position"
            ::= { hwTrunkAttr 2 }

        
        hwTrunkIfTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwTrunkIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "hwTrunkIfID uniquely identifies this table. It is set when the table is created. This table describes the information about the trunk interface."
            ::= { hwTrunkAttr 3 }

        
        hwTrunkIfEntry OBJECT-TYPE
            SYNTAX HwTrunkIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entries of the trunk table."
            INDEX { hwTrunkIndex }
            ::= { hwTrunkIfTable 1 }

        
        HwTrunkIfEntry ::=
            SEQUENCE { 
                hwTrunkIndex
                    Integer32,
                hwTrunkIfID
                    Integer32,
                hwTrunkIfType
                    INTEGER,
                hwTrunkIfIndex
                    InterfaceIndex,
                hwTrunkIfModel
                    INTEGER,
                hwTrunkIfBandWidthAffectLinkNum
                    Integer32,
                hwTrunkIfMinLinkNum
                    Integer32,
                hwTrunkIfRowStatus
                    RowStatus,
                hwTrunkIfWorkingMode
                    INTEGER,
                hwTrunkIfWorkingState
                    INTEGER,
                hwTrunkIfAutoRecover
                    INTEGER,
                hwTrunkIfPreemptEnable
                    INTEGER,
                hwTrunkIfPreemptDelay
                    Integer32,
                hwTrunkIfTimeoutReceive
                    INTEGER,
                hwTrunkIfFlushSendEnable
                    INTEGER,
                hwTrunkIfFlushVlanId
                    Integer32,
                hwTrunkIfFlushPasswd
                    SnmpPasswdString,
                hwTrunkIfForceSwitchEnable
                    INTEGER,
                hwTrunkIfStatReset
                    INTEGER,
                hwTrunkBandwidth                        -- the band width of the trunk interface
                    Integer32,
                hwTrunkIfArpSendSpeed                        
                    Integer32,
                hwTrunkIfLagSelectedPortStd
                    INTEGER,
                hwTrunkIfLagMaxActiveLinkNum
                    Integer32,
                hwTrunkETrunkPriority
                    Integer32,
                hwTrunkETrunkSysID
                    PhysAddress,
               hwTrunkETrunkPriorityReset
                   INTEGER,
               hwTrunkETrunkSysIDReset
                   INTEGER,
               hwTrunkLocalPrefMode
                   INTEGER,
               hwTrunkIfTrackVrrpVrid
                   Integer32,
               hwTrunkIfTrackVrrpIfIndex
                   InterfaceIndex,
               hwTrunkIfTrackVrrpReset
                   INTEGER,
               hwTrunkIfBackupPreemptEnable
                   EnabledStatus,
               hwTrunkIfBackupPreemptDelay
                   Integer32,
               hwTrunkIfCrcErrorSwitchEnable
                   EnabledStatus,
               hwTrunkIfLagTimeOut
                    Integer32,     
               hwTrunkMemCount
                   Integer32                       
             
             }

--  the max bandwidth-affected-linknumber
-- the least active-linknumber of the up port
        hwTrunkIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                " The value of this object identifies the index of a trunk interface. The currently supported value ranges from 0 to 511."
            ::= { hwTrunkIfEntry 1 }

        
        hwTrunkIfID OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " The identification of the Trunk interface. It may identify Trunk
                and is an index of the interface."
            ::= { hwTrunkIfEntry 2 }

        
        hwTrunkIfType OBJECT-TYPE
            SYNTAX INTEGER
                {
                ethTrunk(1),
                ipTrunk(2)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " The type of the Trunk interface includes eth-trunk and ip-trunk.
                The ip-trunk is only comprised by pos link, otherwise the eth-trunk
                 is only comprised by ethernet link."
            ::= { hwTrunkIfEntry 3 }

        
        hwTrunkIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " Index number of the Trunk interface, it is consistent with ifIndex
                of ifTable in IF-MIB."
            ::= { hwTrunkIfEntry 4 }

        hwTrunkIfModel OBJECT-TYPE
            SYNTAX INTEGER
                {
                packetAll(1),
                sourceDesMac(2),
                packetUdp(3),
                packetTcp(4),
                sourceDesIp(5),
                sourceMacIpv6(6),
                sourceIpIpv6(7),
                sourceIp(8),
                desIp(9),
                sourceMac(10),
                desMac(11),
                sourcePort(12),
                desPort(13),
                sourceDesPort(14),
                fwdType(15),
                qos(16),
                labelNum(17),
                label(18),
                enhanced(19),
                l4(20),
                roundRobin(21),
                resilient(22),
                dynProfile(23),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Participated model of interface load. sourceDesMac(2) and 
                sourceMac(10) and desMac(11) are Layer2 hash arithmetic."
            ::= { hwTrunkIfEntry 5 }

        
        hwTrunkIfBandWidthAffectLinkNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " The value of this object identifies the maximum number of trunk interfaces that can be created.
By default, the maximum number of trunk interfaces that can be created is 32.
This entry can be set only on a Layer 2 interface."
            ::= { hwTrunkIfEntry 6 }

        
        hwTrunkIfMinLinkNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " The value of this object identifies the minimum number of trunk interfaces that can be created.
The currently supported value ranges from 1 to 32. By default, the minimum number of trunk interfaces that can be created is 1. "
            ::= { hwTrunkIfEntry 7 }

        
        hwTrunkIfRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " Current operation status of the row "
            ::= { hwTrunkIfEntry 8 }

        
        hwTrunkIfWorkingMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                masterBackup(1),
                normalMode(2),
                lacpMode(3),
                lacpCompatibleMode(4),
                portStandbyMode(5),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " The value of this object identifies the working mode of an Eth-Trunk interface. 
                  1: master/backup access mode 
                  2: normal mode 
                  3: static LACP mode   
                  4: compatible LACP mode  
                  5: backup interface mode"
            ::= { hwTrunkIfEntry 9 }

        
        hwTrunkIfWorkingState OBJECT-TYPE
            SYNTAX INTEGER
                {
                generalMode(1),
                initialization(2),
                masterWorking(3),
                backupWorking(4),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " Current working state of backup-access trunk.
                1: general mode working state
                2: initialization 
                3: master working
                4: backup working"
            ::= { hwTrunkIfEntry 10 }

        
        hwTrunkIfAutoRecover OBJECT-TYPE
            SYNTAX INTEGER
                {
                generalMode(1),
                autoRecover(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " If auto-recover has been set, the master port will be
                working instead of the other port when it changes from 
                down state to up state.
                1: don't support auto-recover
                2: support auto-recover"
            ::= { hwTrunkIfEntry 11 }

        
        hwTrunkIfPreemptEnable OBJECT-TYPE
            SYNTAX INTEGER
                {
                enabled(1),
                disabled(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This object indicates whether LACP priority preemption in static mode is enabled.
                 By default, this function is not enabled."
            DEFVAL { disabled }
            ::= { hwTrunkIfEntry 12 }

        
        hwTrunkIfPreemptDelay OBJECT-TYPE
            SYNTAX Integer32 (0..180 | -1)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The value of this object identifies the delay for LACP priority preemption in static mode. You can set this object only after hwTrunkIfPreemptEnable is enabled. The value ranges from 10 to 180, in seconds. The default value is 30s. If LACP is not configured, the value of -1 is used."
            DEFVAL { 30 }
            ::= { hwTrunkIfEntry 13 }

        
        hwTrunkIfTimeoutReceive OBJECT-TYPE
            SYNTAX INTEGER
                {
                fast(1),
                slow(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The value of this object identifies the timeout period for receiving LACPDUs on an interface in static LACP mode. The value can be:
                 fast: specifies the timeout period for receiving packets to be 3s. 
                 slow: specifies the timeout period for receiving packets to be 90s. 
                The default timeout period for receiving packets is 90s.
                fast(1),slow(2). 
                The return value of -1 indicates invalid."
            ::= { hwTrunkIfEntry 14 }

        
        hwTrunkIfFlushSendEnable OBJECT-TYPE
            SYNTAX INTEGER
                {
                enabled(1),
                disabled(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Whether or not permit the trunk to transmit FlushDUs.It is only used 
                when it is in the handwork 1:1 mode.Once it is enabled, the newly active 
                interface in the trunk will send SmartLink FlushDUs so as to delete the 
                related MAC forwarding table of the layer2 equipment in network.Otherwise, 
                SmartLink FlushDUs will not be sent."
            DEFVAL { disabled }
            ::= { hwTrunkIfEntry 15 }

        
        hwTrunkIfFlushVlanId OBJECT-TYPE
            SYNTAX Integer32 (0..4094 | -1)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The ID of Vlan the partner port belongs to.When it is 0, it indicates 
                the function of sending FlushPDU is disabled. "
            DEFVAL { 0 }
            ::= { hwTrunkIfEntry 16 }

        
        hwTrunkIfFlushPasswd OBJECT-TYPE
            SYNTAX SnmpPasswdString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "A 16-octet read-create value used to define the password for negotiation."
            DEFVAL { '00'h }
            ::= { hwTrunkIfEntry 17 }

        
        hwTrunkIfForceSwitchEnable OBJECT-TYPE
            SYNTAX INTEGER
                {
                enabled(1),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "It is only used when it is in the handwork 1:1 mode and the current 
                working link is the backup link. At that time, working link will move 
                from backup link to master link. However, if the master link is down, 
                the operation will be forbidden."
            DEFVAL { enabled }
            ::= { hwTrunkIfEntry 18 }

        
        hwTrunkIfStatReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                reset(1),
                ready(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Reset the statistic information of LACPDUsRx,MarkerPDUsRx,LACPDUsTx,
                MarkerResponsePDUsTx for all the ports in the current trunk."
            ::= { hwTrunkIfEntry 19 }
        
        hwTrunkBandwidth  OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION 
                " Current bandwidth of trunk in units of 1,000,000 bits per second. This bandwidth is the peculiar property of Eth-Trunk. And to the other kinds of trunk, this bandwidth is -1."
            ::= { hwTrunkIfEntry 20 }        

        
        hwTrunkIfArpSendSpeed  OBJECT-TYPE
            SYNTAX Integer32(800..3000 | -1)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION 
                "Indicates the rate of sending gratuitous ARP packets (in pkts/sec) on the trunk member interface. "
            DEFVAL { 2000 }                
            ::= { hwTrunkIfEntry 21 }  

        hwTrunkIfLagSelectedPortStd OBJECT-TYPE
            SYNTAX INTEGER
                {
                speed(1),
                priority(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This object indicates the port selection standard of LACP: based either on the port rate or on the port priority. By default, LACP selects ports based on the port priority.
                  speed(1),priority(2). 
                 If LACP is not configured, the return value is invalid(-1)."
            DEFVAL { priority }
            ::= { hwTrunkIfEntry 22 }

       hwTrunkIfLagMaxActiveLinkNum  OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION 
                "The value of this object identifies the upper threshold of interfaces that can be selected by the Eth-Trunk in static mode."
            ::= { hwTrunkIfEntry 23 } 
         
        hwTrunkETrunkPriority OBJECT-TYPE
            SYNTAX Integer32 (0..65535 | -1)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The priority of the E-Trunk."
            ::= { hwTrunkIfEntry 24 }
            
        hwTrunkETrunkSysID OBJECT-TYPE
            SYNTAX PhysAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The system ID of the E-Trunk. If set ffff-ffff-ffff the system ID will be to default."
            ::= { hwTrunkIfEntry 25 }
         
        hwTrunkETrunkPriorityReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                yes(1),
                no(2),
                invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Status of operation, there are yes(1) and no(2).
                It expresses whether the configure of E-Trunk priority is resetted."
            DEFVAL { no }
            ::= { hwTrunkIfEntry 26 }       
            
        hwTrunkETrunkSysIDReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                yes(1),
                no(2),
                invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Status of operation, there are yes(1) and no(2).
                It expresses whether the configure of E-Trunk system ID is resetted."
            DEFVAL { no }
            ::= { hwTrunkIfEntry 27 }

        hwTrunkLocalPrefMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                enable(1),
                disable(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Local-preference transmit mode of eth-trunk, there are enable(1) and disable(2).The default value is enable(1)."
            ::= { hwTrunkIfEntry 28 }
            
        hwTrunkIfTrackVrrpVrid OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The VRID which the static mode Eth-trunk tracked to."
            ::= { hwTrunkIfEntry 29 }
        
        hwTrunkIfTrackVrrpIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The VRRP interface which the static mode Eth-trunk tracked to."
            ::= { hwTrunkIfEntry 30 }
            
        hwTrunkIfTrackVrrpReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                yes(1),
                no(2),
                invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Status of operation, there are yes(1) and no(2).
                The value of this object identifies whether 
                the operation of clearing the configuration 
                is performed. And the configuration is that 
                the Eth-Trunk interface in static LACP mode 
                is associated with an mVRRP backup group. 
                The value yes(1) indicates performing the operation. 
                This object indicates one operation but not one status,
                so when the operation is performed, the value is no(2).   
                By default, the value is no(2)."
            ::= { hwTrunkIfEntry 31 }

        hwTrunkIfBackupPreemptEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The preemption according to the master and slave port will be and only be enabled when eth-trunk is working in backup mode.
                In default condition, the preemption is disabled."
            DEFVAL { disabled }
            ::= { hwTrunkIfEntry 32 }
        
        hwTrunkIfBackupPreemptDelay OBJECT-TYPE
            SYNTAX Integer32 (0..30 | -1)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The waiting time for priority preemption; Unit: minute.
                It is enabled only when the priority preemption is enabled.
                It indicates, When it comes to priority preemption, the operation 
                of preemption will be delayed for hwTrunkIfBackupPreemptDelay time."
            DEFVAL { 0 }
            ::= { hwTrunkIfEntry 33 }
            
        hwTrunkIfCrcErrorSwitchEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "make trunk member port stop in use if the port have crc error exceed the config threshold, the trunk would be down if member port in use is low"
            DEFVAL { 2 }
            ::= { hwTrunkIfEntry 34 }
            
        hwTrunkIfLagTimeOut OBJECT-TYPE
            SYNTAX Integer32 (3..90 | 0)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The expired time(Unit is second) of the port to receive LACPDUS in the fast mode.
                The hwTrunkIfTimeoutReceive is the type of timeout and use hwTrunkIfLagTimeOut to change time in the fast mode.
                The default value is customized with products.
                If the mode is not fast, the value is always 0."
            ::= { hwTrunkIfEntry 35 }
            
        hwTrunkMemCount OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The count of Trunk member interfaces per Trunk."
            ::= { hwTrunkIfEntry 36 }

        hwTrunkSystemPriority OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The value of this object identifies the system priority defined in the Link Aggregation Control Protocol (LACP)."
            ::= { hwTrunkAttr 4 }
            
       hwTrunkUnknownUnicastIfModel OBJECT-TYPE
            SYNTAX INTEGER
                {
                packetAll(1),
                sourceDesMac(2),
                packetUdp(3),
                packetTcp(4),
                sourceDesIp(5),
                sourceMacIpv6(6),
                sourceIpIpv6(7),
                sourceIp(8),
                desIp(9),
                sourceMac(10),
                desMac(11),
                sourcePort(12),
                desPort(13),
                sourceDesPort(14),
                label(15),
        ipOrLabel(16)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Participated model of load for unknown unicast. sourceDesMac(2) and 
                sourceMac(10) and desMac(11) are Layer2 hash arithmetic."
            ::= { hwTrunkAttr 5 }


            hwTrunkETrunkSystemPriority OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The system priority of the E-Trunk."
            ::= { hwTrunkAttr 6 }
            
            
            hwTrunkETrunkSystemID OBJECT-TYPE
            SYNTAX PhysAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The system ID of the E-Trunk. If set ffff-ffff-ffff the system ID will be to default."
            ::= { hwTrunkAttr 7 }   

            hwTrunkCount OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Number of configured trunk interfaces on the device."
            ::= { hwTrunkAttr 8 } 

            hwTrunkMemberCountUpperThreshold OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Upper threshold for the number of members of the Eth-Trunk in LACP mode."
            ::= { hwTrunkAttr 9 }
            
            hwTrunkMemberCountLowerThreshold OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "Lower threshold for the number of members of the Eth-Trunk in LACP mode."
            ::= { hwTrunkAttr 10 }

            hwTrunkSubinterfacesCount OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Number of configured trunk sub-interfaces on the device."
            ::= { hwTrunkAttr 11 } 
            
            hwTrunkSubinterfacesLimit OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specification of trunk sub-interfaces on the device."
            ::= { hwTrunkAttr 12 }             
            
        
-- =========================================
-- Trunk member attribute Group
-- =========================================
        hwTrunkMemAttr OBJECT IDENTIFIER ::= { hwIFExtObjects 4 }

        
        hwTrunkMemTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwTrunkMemEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "hwTrunkMemifIndex uniquely identifies this table. It is set when the table is created. This table describes the information about the trunk members."
            ::= { hwTrunkMemAttr 1 }

        
        hwTrunkMemEntry OBJECT-TYPE
            SYNTAX HwTrunkMemEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Trunk member attribute information"
            INDEX { hwTrunkIndex, hwTrunkMemifIndex }
            ::= { hwTrunkMemTable 1 }

        
        HwTrunkMemEntry ::=
            SEQUENCE { 
                hwTrunkMemifIndex
                    Integer32,
                hwTrunkValidEntry
                    INTEGER,
                hwTrunkSelectStatus
                    INTEGER,
                hwTrunkLacpStatus
                    EnabledStatus,
                hwTrunkDeleteFlag
                    EnabledStatus,
                hwTrunkOperstatus
                    INTEGER,
                hwTrunkIsDefaultLagRecv
                    TruthValue,
                hwTrunkPortWeight
                    Unsigned32,
                hwTrunkPortStandby
                    Unsigned32,
                hwTrunkRowStatus
                    RowStatus,
                hwTrunkPortMaster
                    INTEGER,
                hwTrunkPortPriority
                    Integer32,
                hwTrunkPortStatReset
                    INTEGER,
                hwTrunkPortLacpMode
                    INTEGER
             }

        hwTrunkMemifIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "The value of this object identifies the index of a trunk member interface."
            ::= { hwTrunkMemEntry 1 }

        
        hwTrunkValidEntry OBJECT-TYPE
            SYNTAX INTEGER
                {
                valid(1),
                invalid(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of this object identifies the valid flag in the trunk. If the trunk has member interfaces added, the return value is valid(1)."
            DEFVAL { invalid }
            ::= { hwTrunkMemEntry 2 }

        
        hwTrunkSelectStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                trunkSelected(1),
                trunkDeselected(2),
                trunkIndep(3),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The value of this object identifies the status of a trunk. trunkSelected: A trunk member interface in this state can both forward data and receive LACPDUs from a remote device.
                  trunkDeselected: A trunk member interface in this state cannot forward data.
                  trunkIndep: A trunk member interface in this state can forward data but cannot receive LACPDUs from a remote device. This state applies only to Eth-Trunk interfaces that work in dynamic LACP mode."
            DEFVAL { trunkDeselected }
            ::= { hwTrunkMemEntry 3 }

        
        hwTrunkLacpStatus OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the LACP status.
                 enabled: An Eth-Trunk interface works in static LACP mode.
                 disabled: An Eth-Trunk interface does not work in static LACP mode."
            DEFVAL { disabled }
            ::= { hwTrunkMemEntry 4 }

        
        hwTrunkDeleteFlag OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Flag is set trunk_del_enable(1) or trunk_del_disable(2),
                according to operation."
            DEFVAL { disabled }
            ::= { hwTrunkMemEntry 5 }

        
        hwTrunkOperstatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                up(1),
                down(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Status of operation, indicates port status. There are trunk_up(1)
                and trunk_down(2). It expresses whether port is shutdown."
            DEFVAL { down }
            ::= { hwTrunkMemEntry 6 }

        
        hwTrunkIsDefaultLagRecv OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "We don't sustain now, default value is false."
            DEFVAL { false }
            ::= { hwTrunkMemEntry 7 }

        
        hwTrunkPortWeight OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The value of this object identifies the weight of a trunk member interface. The default weight of a Trunk member interface is 1. The sum weight of all member interfaces of a trunk cannot exceed the maximum number of member interfaces that a trunk supports."
            DEFVAL { 1 }
            ::= { hwTrunkMemEntry 8 }

        
        hwTrunkPortStandby OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The value is backup of Trunk Port."
            DEFVAL { 0 }
            ::= { hwTrunkMemEntry 9 }

        
        hwTrunkRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Current operation status of the row. "
            ::= { hwTrunkMemEntry 10 }

        
        hwTrunkPortMaster OBJECT-TYPE
            SYNTAX INTEGER
                {
                portSlave(1),
                portMaster(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Master port of backup-access eth-trunk or not
                1: port-slave
                2: port-master"
            ::= { hwTrunkMemEntry 11 }

        
        hwTrunkPortPriority OBJECT-TYPE
            SYNTAX Integer32 (0..65535 | -1)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The priority value assigned to this Trunk Port.
                This 16-bit value is read-write."
            ::= { hwTrunkMemEntry 12 }

        
        hwTrunkPortStatReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                reset(1),
                ready(2),
                invalid(-1)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This object indicates resetting LACPDU statistics on an interface. This object applies only to Eth-Trunk interfaces in static LACP mode. For Eth-Trunk interfaces in other modes, the return value is fixed at -1."
            ::= { hwTrunkMemEntry 13 }

         hwTrunkPortLacpMode OBJECT-TYPE
             SYNTAX INTEGER
                {
                invalid(1),
                active(2),
                passive(3)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The mode of port joining Lacp."
            ::= { hwTrunkMemEntry 14 }

        
--  ===========================================================================
-- Global interfacce flow stat interval group 
-- ===========================================================================
        hwIFFlowStat OBJECT IDENTIFIER ::= { hwIFExtObjects 5 }

        
-- metric: second
        hwIFFlowStatGlobalInterval OBJECT-TYPE
            SYNTAX Integer32 (10..600)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Global interval of interface flow stat.The interface interval is effective,
                when both interface interval and global interval are config.Global interval 
                is only effective in interfaces,which interface interval is not configured.
                Flow stat rate of interface will be affected by the value of interval.
                The default value is 300s."
            ::= { hwIFFlowStat 1 }


   
        
        
        
--  ===========================================================================
-- Global interface flow stat interval group 
-- ===========================================================================
        hwIfStatistics OBJECT IDENTIFIER ::= { hwIFExtObjects 6 }

        
        hwIfEtherStatTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfEtherStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " This table describes attributes of Ethernet interfaces."
            ::= { hwIfStatistics 1 }

        
        hwIfEtherStatEntry OBJECT-TYPE
            SYNTAX HwIfEtherStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of Ethernet Interface Statistic Table "
            INDEX { hwIfEtherStatIfIndex }
            ::= { hwIfEtherStatTable 1 }

        
        HwIfEtherStatEntry ::=
            SEQUENCE { 
                hwIfEtherStatIfIndex
                    InterfaceIndex,
        hwIfEtherStatInPkts64Octets
            Counter64,
        hwIfEtherStatInPkts65to127Octets
            Counter64,
        hwIfEtherStatInPkts128to255Octets
            Counter64,
        hwIfEtherStatInPkts256to511Octets
            Counter64,
        hwIfEtherStatInPkts512to1023Octets
            Counter64,
        hwIfEtherStatInPkts1024to1518Octets
            Counter64,
                hwIfEtherStatInJumboPkts
                    Counter64,
                hwIfEtherStatInCRCPkts
                    Counter64,
                hwIfEtherStatInLongPkts
                    Counter64,
                hwIfEtherStatInJabberPkts
                    Counter64,
                hwIfEtherStatInFragmentPkts
                    Counter64,
                hwIfEtherStatInUnderSizePkts
                    Counter64,
                hwIfEtherStatInOverRunPkts
                    Counter64,
                hwIfEtherStatInPausePkts
                    Counter64,
                hwIfEtherStatOutJumboPkts
                    Counter64,
                hwIfEtherStatOutOverflowPkts
                    Counter64,
                hwIfEtherStatOutUnderRunPkts
                    Counter64,
                hwIfEtherStatOutPausePkts
                    Counter64,
                hwIfEthIfStatReset
                    INTEGER, 
                hwIfEtherStatInDropEventPkts
                    Counter64, 
                hwIfEtherStatInAlignmentPkts
                    Counter64,
                hwIfEtherStatInSymbolPkts
                    Counter64,
                hwIfEtherStatInIgnoredPkts
                    Counter64, 
                hwIfEtherStatInFramePkts
                    Counter64,              
                hwIfEtherStatOutCollisionPkts
                    Counter64,
                hwIfEtherStatOutDeferredPkts
                    Counter64,
                hwIfEtherStatOutLateCollisionPkts
                    Counter64,
                hwIfEtherStatOutExcessiveCollisionPkts
                    Counter64,
                hwIfEtherStatOutBufferPurgationPkts
                    Counter64
             }

        hwIfEtherStatIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the index of a Layer 2 interface."
            ::= { hwIfEtherStatEntry 1 }

    
    -- *******.4.1.2011.*********.*******
    hwIfEtherStatInPkts64Octets OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "This object indicates the number of received data packets each with a length less than or equal to 64 bytes."
        ::= { hwIfEtherStatEntry 5 }

    
    -- *******.4.1.2011.*********.*******
    hwIfEtherStatInPkts65to127Octets OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "This object indicates the number of received data packets each with a length ranging from 65 bytes to 127 bytes."
        ::= { hwIfEtherStatEntry 6 }

    
    -- *******.4.1.2011.*********.*******
    hwIfEtherStatInPkts128to255Octets OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "This object indicates the number of received data packets each with a length ranging from 128 bytes to 255 bytes."
        ::= { hwIfEtherStatEntry 7 }

    
    -- *******.4.1.2011.*********.*******
    hwIfEtherStatInPkts256to511Octets OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "This object indicates the number of received data packets each with a length ranging from 256 bytes to 511 bytes."
        ::= { hwIfEtherStatEntry 8 }

    
    -- *******.4.1.2011.*********.*******
    hwIfEtherStatInPkts512to1023Octets OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "This object indicates the number of received data packets each with a length ranging from 512 bytes to 1023 bytes."
        ::= { hwIfEtherStatEntry 9 }

    
    -- *******.4.1.2011.*********.********
    hwIfEtherStatInPkts1024to1518Octets OBJECT-TYPE
        SYNTAX Counter64
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "This object indicates the number of received data packets each with a length ranging from 1024 bytes to 1518 bytes."
        ::= { hwIfEtherStatEntry 10 }

    
        hwIfEtherStatInJumboPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received data packets each with a length greater than 1518 bytes."
            ::= { hwIfEtherStatEntry 11 }

        
        hwIfEtherStatInCRCPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of packets experiencing the CRC error check."
            ::= { hwIfEtherStatEntry 12 }

        
        hwIfEtherStatInLongPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of packets each with a length greater than the maximum length."
            ::= { hwIfEtherStatEntry 13 }

        
        hwIfEtherStatInJabberPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of packets each with a length greater than the maximum length and experiencing the CRC error check."
            ::= { hwIfEtherStatEntry 14 }

        
        hwIfEtherStatInFragmentPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of data packets each with a length shorter than 64 bytes and experiencing the CRC error check."
            ::= { hwIfEtherStatEntry 15 }

        
        hwIfEtherStatInUnderSizePkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of data packets each with a length shorter than 64 bytes and without experiencing the CRC error check."
            ::= { hwIfEtherStatEntry 16 }

        
        hwIfEtherStatInOverRunPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received data packets with FIFO overflow errors."
            ::= { hwIfEtherStatEntry 17 }

        
        hwIfEtherStatInPausePkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received control frames."
            ::= { hwIfEtherStatEntry 18 }

        
        hwIfEtherStatOutJumboPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of sent packets each with a length greater than 1518 bytes."
            ::= { hwIfEtherStatEntry 19 }

        
        hwIfEtherStatOutOverflowPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of sent data packets with FIFO overflow errors."
            ::= { hwIfEtherStatEntry 20 }

        
        hwIfEtherStatOutUnderRunPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of data packets with an empty FIFO."
            ::= { hwIfEtherStatEntry 21 }

        
        hwIfEtherStatOutPausePkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of sent control frames."
            ::= { hwIfEtherStatEntry 22 }

        
        hwIfEthIfStatReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                reset(1),
                ready(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " Reset the Statistic information Counter "
            ::= { hwIfEtherStatEntry 23 }
         
                      
          hwIfEtherStatInDropEventPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of received Packets discarded for GBP full or back pressure discard "
            ::= { hwIfEtherStatEntry 24 }

 
          hwIfEtherStatInAlignmentPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received packets with error headers."
            ::= { hwIfEtherStatEntry 25 }


         hwIfEtherStatInSymbolPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received unknown packets."
            ::= { hwIfEtherStatEntry 26 }


         hwIfEtherStatInIgnoredPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of received MAC control frames which have unknown opecode "
            ::= { hwIfEtherStatEntry 27 }


         hwIfEtherStatInFramePkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of received frames whose actual length differs with 802.3 "
            ::= { hwIfEtherStatEntry 28 }
         

         hwIfEtherStatOutCollisionPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of sended collision Frames(only happen in the 10/100M port with half duplex mode) "
            ::= { hwIfEtherStatEntry 29 }
         

         hwIfEtherStatOutDeferredPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of sended deferred but not collision Frames(only happen in the 10/100M port) "
            ::= { hwIfEtherStatEntry 30 }
         

         hwIfEtherStatOutLateCollisionPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of sended deferred and collision Frames(only happen in the 10/100M port) "
            ::= { hwIfEtherStatEntry 31 }   
         

         hwIfEtherStatOutExcessiveCollisionPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of sended failure Frames whose collision more than 16(only happen in the 10/100M port) "
            ::= { hwIfEtherStatEntry 32 }
         
         
         hwIfEtherStatOutBufferPurgationPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of sent Frames which is purged from the buffer because of long time "
            ::= { hwIfEtherStatEntry 33 }       
      

hwIfSdhStatTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfSdhStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " This table describes attributes of SDH interfaces. "
            ::= { hwIfStatistics 2 }

        
        hwIfSdhStatEntry OBJECT-TYPE
            SYNTAX HwIfSdhStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of Sdh/Sonet Interface Statistic Table "
            INDEX { hwIfSdhStatIfIndex }
            ::= { hwIfSdhStatTable 1 }

        
        HwIfSdhStatEntry ::=
            SEQUENCE { 
                hwIfSdhStatIfIndex
                    InterfaceIndex,
                hwIfSdhStatInCRCPkts
                    Counter64,
                hwIfSdhStatInShortPkts
                    Counter64,
                hwIfSdhStatInLongPkts
                    Counter64,
                hwIfSdhStatOutOverRunPkts
                    Counter64,
                hwIfSdhStatOutUnderRunPkts
                    Counter64,
                hwIfSdhIfStatReset
                      INTEGER,
                hwIfSdhStatInOverRunPkts
                    Counter64
             }

        hwIfSdhStatIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the SDH interface index."
            ::= { hwIfSdhStatEntry 1 }
            
        hwIfSdhStatInCRCPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of CRC errors in received packets."
            ::= { hwIfSdhStatEntry 2 } 
            
        hwIfSdhStatInShortPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received packets with the length less than the minimum length allowed."
            ::= { hwIfSdhStatEntry 3 }
            
        hwIfSdhStatInLongPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received packets with the length greater than the maximum length allowed."
            ::= { hwIfSdhStatEntry 4 }
            
        hwIfSdhStatOutOverRunPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of sent packets with overflow errors."
            ::= { hwIfSdhStatEntry 5 }
            
        hwIfSdhStatOutUnderRunPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of sent packets with underrun errors."
            ::= { hwIfSdhStatEntry 6 }            

        hwIfSdhIfStatReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                reset(1),
                ready(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                " Reset the Statistic information Counter "
            ::= { hwIfSdhStatEntry 7 }
 
        hwIfSdhStatInOverRunPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of received Packets whose queue overflow "
            ::= { hwIfSdhStatEntry 8 } 

        hwIfAtmStatTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfAtmStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " This table contains ATM interface attributes."
            ::= { hwIfStatistics 3 }

        
        hwIfAtmStatEntry OBJECT-TYPE
            SYNTAX HwIfAtmStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of Atm Interface Statistic Table "
            INDEX { hwIfAtmStatIfIndex }
            ::= { hwIfAtmStatTable 1 }

        
        HwIfAtmStatEntry ::=
            SEQUENCE { 
                hwIfAtmStatIfIndex
                    InterfaceIndex,
                hwIfAtmStatInGoodCells
                    Counter64,
                hwIfAtmStatInIdleCells
                    Counter64,
                hwIfAtmStatInCorrectedCells
                    Counter64,
                hwIfAtmStatInUncorrectedCells
                    Counter64,
                hwIfAtmStatOutGoodCells
                    Counter64,
                hwIfAtmStatOutIdleCells
                    Counter64,
                hwIfAtmIfStatReset
                    INTEGER
             }

        hwIfAtmStatIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the interface index."
            ::= { hwIfAtmStatEntry 1 }
            
        hwIfAtmStatInGoodCells OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received normal cells."
            ::= { hwIfAtmStatEntry 2 } 
            
        hwIfAtmStatInIdleCells OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received idle cells."
            ::= { hwIfAtmStatEntry 3 }             
            
        hwIfAtmStatInCorrectedCells OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received corrected cells."
            ::= { hwIfAtmStatEntry 4 }
            
        hwIfAtmStatInUncorrectedCells OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received cells that cannot be corrected."
            ::= { hwIfAtmStatEntry 5 }
            
        hwIfAtmStatOutGoodCells OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of sent normal cells."
            ::= { hwIfAtmStatEntry 6 } 
            
        hwIfAtmStatOutIdleCells OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of sent idle cells."
            ::= { hwIfAtmStatEntry 7 }                                              

        hwIfAtmIfStatReset OBJECT-TYPE
            SYNTAX INTEGER
                {
                reset(1),
                ready(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the resetting of the counter."
            ::= { hwIfAtmStatEntry 8 }            

        hwIfPPPHDLCStatTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfPPPHDLCStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Serial Interface Statistic Table for PPP/HDLC "
            ::= { hwIfStatistics 4 }

        hwIfPPPHDLCStatEntry OBJECT-TYPE
            SYNTAX HwIfPPPHDLCStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " This table describes PPP or HDLC interface statistics."
            INDEX { hwIfPPPHDLCStatIfIndex }
            ::= { hwIfPPPHDLCStatTable 1 }

        HwIfPPPHDLCStatEntry ::=
            SEQUENCE { 
                hwIfPPPHDLCStatIfIndex
                    InterfaceIndex,
                hwIfPPPHDLCStatInCRCPkts
                    Counter64,
                hwIfPPPHDLCStatInShortPkts
                    Counter64,
                hwIfPPPHDLCStatInLongPkts
                    Counter64
             }

        hwIfPPPHDLCStatIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the serial interface index."
            ::= { hwIfPPPHDLCStatEntry 1 }
            
        hwIfPPPHDLCStatInCRCPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received data packets that experience a CRC check."
            ::= { hwIfPPPHDLCStatEntry 2 } 
            
        hwIfPPPHDLCStatInShortPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received data packets with each length less than the minimum length of 1 byte."
            ::= { hwIfPPPHDLCStatEntry 3 }
            
        hwIfPPPHDLCStatInLongPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received data packets with each length greater than the maximum length of 2036 bytes."
            ::= { hwIfPPPHDLCStatEntry 4 }
	    
        hwIfDualStackStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfDualStackStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Interface Statistic Table for IPv4/IPv6."
            ::= { hwIfStatistics 5 }

        hwIfDualStackStatsEntry OBJECT-TYPE
            SYNTAX HwIfDualStackStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "An interface statistics entry containing objects for a particular interface and version of IPv4/IPv6."
            INDEX { hwIfDualStackStatsIPVersion, hwIfDualStackStatsIfIndex }
            ::= { hwIfDualStackStatsTable 1 }

        HwIfDualStackStatsEntry ::=
            SEQUENCE { 
                hwIfDualStackStatsIPVersion
                    InetVersion,         
                hwIfDualStackStatsIfIndex
                    InterfaceIndex,
                hwIfDualStackStatsInReceives
                    Counter64,
                hwIfDualStackStatsInOctets
                    Counter64,
                hwIfDualStackStatsOutTransmits
                    Counter64,
                hwIfDualStackStatsOutOctets
                    Counter64,
                hwIfDualStatckStatsInUcastPkts
                    Counter64,
                hwIfDualStatckStatsInUcastOctets
                    Counter64,
                hwIfDualStatckStatsInMcastPkts
                    Counter64,
                hwIfDualStatckStatsInMcastOctets
                    Counter64,
                hwIfDualStatckStatsInBcastPkts
                    Counter64,
                hwIfDualStatckStatsInBcastOctets
                    Counter64,
                hwIfDualStatckStatsOutUcastPkts
                    Counter64,
                hwIfDualStatckStatsOutUcastOctets
                    Counter64,
                hwIfDualStatckStatsOutMcastPkts
                    Counter64,
                hwIfDualStatckStatsOutMcastOctets
                    Counter64,
                hwIfDualStatckStatsOutBcastPkts
                    Counter64,
                hwIfDualStatckStatsOutBcastOctets
                    Counter64,
                hwIfDualStackStatsInPktRate
                    Counter64,
                hwIfDualStackStatsInOctetRate
                    Counter64,
                hwIfDualStackStatsOutPktRate
                    Counter64,
                hwIfDualStackStatsOutOctetRate
                    Counter64
             }

        hwIfDualStackStatsIPVersion OBJECT-TYPE
            SYNTAX     InetVersion
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                   "The IP version of this row."
            ::= { hwIfDualStackStatsEntry 1 }
    
        hwIfDualStackStatsIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index value that uniquely identifies the interface to which this entry is applicable."
            ::= { hwIfDualStackStatsEntry 2 }
            
        hwIfDualStackStatsInReceives OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 datagrams received, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 3 } 
            
        hwIfDualStackStatsInOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets in received IPv4/IPv6 datagrams, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 4 }
            
        hwIfDualStackStatsOutTransmits OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 datagrams delivered to the lower layers for transmission, including those transmitted on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 5 }
            
        hwIfDualStackStatsOutOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets in IPv4/IPv6 datagrams delivered to the lower layers for transmission, including those transmitted on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 6 }

        hwIfDualStatckStatsInUcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 unicast datagrams received, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 7 }
			
        hwIfDualStatckStatsInUcastOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets received in IPv4/IPv6 unicast datagrams, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 8 }
			
        hwIfDualStatckStatsInMcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 multicast datagrams received, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 9 }
			
        hwIfDualStatckStatsInMcastOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets received in IPv4/IPv6 multicast datagrams, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 10 }
			
        hwIfDualStatckStatsInBcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 broadcast datagrams received, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 11 }
			
        hwIfDualStatckStatsInBcastOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets received in IPv4/IPv6 broadcast datagrams, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 12 }
			
        hwIfDualStatckStatsOutUcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 unicast datagrams transmitted, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 13 }
            
        hwIfDualStatckStatsOutUcastOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets transmitted in IPv4/IPv6 unicast datagrams, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 14 }
			
        hwIfDualStatckStatsOutMcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 multicast datagrams transmitted, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 15 }
			
        hwIfDualStatckStatsOutMcastOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets transmitted in IPv4/IPv6 multicast datagrams, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 16 }
			
        hwIfDualStatckStatsOutBcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of IPv4/IPv6 broadcast datagrams transmitted, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 17 }

        hwIfDualStatckStatsOutBcastOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total number of octets transmitted in IPv4/IPv6 broadcast datagrams, including those received on the interface's sub-interfaces."
            ::= { hwIfDualStackStatsEntry 18 }

        hwIfDualStackStatsInPktRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The rate of IPv4/IPv6 datagrams received, including those received on the interface's sub-interfaces.Unit:packets/s"
            ::= { hwIfDualStackStatsEntry 19 }

        hwIfDualStackStatsInOctetRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The rate of octets in received IPv4/IPv6 datagrams, including those received on the interface's sub-interfaces.Unit:octets/s"
            ::= { hwIfDualStackStatsEntry 20 }

        hwIfDualStackStatsOutPktRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The rate of IPv4/IPv6 datagrams delivered to the lower layers for transmission, including those transmitted on the interface's sub-interfaces.Unit:packets/s."
            ::= { hwIfDualStackStatsEntry 21 }

        hwIfDualStackStatsOutOctetRate OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The rate of octets in IPv4/IPv6 datagrams delivered to the lower layers for transmission, including those transmitted on the interface's sub-interfaces.Unit:octets/s."
            ::= { hwIfDualStackStatsEntry 22 }

    hwIfMonitorObject OBJECT IDENTIFIER ::= { hwIFExtObjects 7 }
    
    hwIfMonitorThresholdTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HwIfMonitorThresholdEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION 
            " Alarm monitor table "
        ::= { hwIfMonitorObject 1 }
    hwIfMonitorThresholdEntry OBJECT-TYPE  
        SYNTAX  HwIfMonitorThresholdEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION 
             " Entry of alarm monitor table "
        INDEX { hwIfMonitorIndex }
        ::= { hwIfMonitorThresholdTable 1 }
    
    HwIfMonitorThresholdEntry::=
        SEQUENCE { 
            hwIfMonitorIndex
                InterfaceIndex,
                        hwIfMonitorCrcErrorStatistics
                                 Counter64,
                        hwIfMonitorCrcErrorThreshold
                                 Unsigned32,
                        hwIfMonitorCrcErrorInterval
                                 Integer32,
                        hwIfMonitorSdhErrorStatistics
                                 Counter64,
                        hwIfMonitorSdhErrorThreshold
                                 Unsigned32,
                        hwIfMonitorSdhErrorInterval
                                 Integer32,
                        hwIfMonitorInputRate
                                 Integer32,
                        hwIfMonitorInputRateThreshold
                                 Integer32,
                        hwIfMonitorOutputRate
                                 Integer32,
                        hwIfMonitorOutputRateThreshold
                                 Integer32,
                        hwIfMonitorPauseFrameStatistics
                                 Counter64,
                        hwIfMonitorPauseFrameThreshold
                                 Unsigned32,
                        hwIfMonitorPauseFrameInterval
                                 Integer32,
                        hwIfMonitorDelayValue
                                 Integer32, 
                        hwIfMonitorDelayThreshold
                                 Integer32, 
                        hwIfMonitorJitterValue
                                 Integer32, 
                        hwIfMonitorJitterThreshold
                                 Integer32,
                        hwIfMonitorName
                                 DisplayString,
                        hwIfMonitorSdhB1ErrorStatistics
                                 Counter64,
                        hwIfMonitorSdhB1ErrorThreshold
                                 Integer32,
                        hwIfMonitorSdhB1ErrorInterval
                                 Integer32,
                        hwIfMonitorSdhB2ErrorStatistics
                                 Counter64,
                        hwIfMonitorSdhB2ErrorThreshold
                                 Integer32,
                        hwIfMonitorSdhB2ErrorInterval
                                 Integer32,
                        hwIfMonitorSymbolErrorStatistics
                                 Counter64,
                        hwIfMonitorSymbolErrorThreshold
                                 Unsigned32,
                        hwIfMonitorSymbolErrorInterval
                                 Integer32,
                        hwIfMonitorBadBytesErrorStatistics
                                 Counter64,
                        hwIfMonitorBadBytesErrorThreshold
                                 Integer32,
                        hwIfMonitorBadBytesErrorInterval
                                 Integer32,
                        hwIfMonitorTxPauseFrameStatistics
                                 Counter64,
                        hwIfMonitorTxPauseFrameHighThreshold
                                 Unsigned32,
                        hwIfMonitorTxPauseFrameLowThreshold
                                 Unsigned32,
                        hwIfMonitorTxPauseFrameInterval
                                 Integer32,
                        hwIfMonitorPostFECErrorStatistics
                                 Counter64,
                        hwIfMonitorAllStatistics
                                Unsigned32
                }

hwIfMonitorIndex OBJECT-TYPE
        SYNTAX InterfaceIndex
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            " Index of alarm monitor table "
        ::= { hwIfMonitorThresholdEntry 1 }

hwIfMonitorCrcErrorStatistics OBJECT-TYPE
        SYNTAX Counter64 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Crc error statistics value "
        ::= { hwIfMonitorThresholdEntry 2 }

    hwIfMonitorCrcErrorThreshold OBJECT-TYPE
        SYNTAX Unsigned32 (0..4294967295)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Crc error alarm threshold,default value is 3 and 0 is an invalid value "
        ::= { hwIfMonitorThresholdEntry 3 }

    hwIfMonitorCrcErrorInterval OBJECT-TYPE
        SYNTAX Integer32 (0..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Crc error alarm interval time(unit is second),default value is 10s and 0 is an invalid value "
        ::= { hwIfMonitorThresholdEntry 4 }
        
hwIfMonitorSdhErrorStatistics OBJECT-TYPE
        SYNTAX Counter64 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Sdh error statistics value only for B3"
             ::= { hwIfMonitorThresholdEntry 5 }

    hwIfMonitorSdhErrorThreshold OBJECT-TYPE
        SYNTAX Unsigned32 (0..4294967295)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Sdh error alarm threshold,only for B3,default value is 3 and 0 is an invalid value "  
        ::= { hwIfMonitorThresholdEntry 6 }

    hwIfMonitorSdhErrorInterval OBJECT-TYPE
        SYNTAX Integer32 (0..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Sdh error alarm interval time(unit is second),only for B3,default value is 10s and 0 is an invalid value "
        ::= { hwIfMonitorThresholdEntry 7 }
    
hwIfMonitorInputRate OBJECT-TYPE
        SYNTAX Integer32 (0..100) 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Input rate percentage value "
        ::= { hwIfMonitorThresholdEntry 8 }

hwIfMonitorInputRateThreshold OBJECT-TYPE
        SYNTAX Integer32 (1..100)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Input rate alarm threshold,default value is 100 "
        ::= { hwIfMonitorThresholdEntry 9 }
   
hwIfMonitorOutputRate OBJECT-TYPE
        SYNTAX Integer32 (0..100) 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Output rate percentage value "
        ::= { hwIfMonitorThresholdEntry 10 }

 hwIfMonitorOutputRateThreshold OBJECT-TYPE
        SYNTAX Integer32 (1..100)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Output rate alarm threshold,default value is 100 "
        ::= { hwIfMonitorThresholdEntry 11 }   
hwIfMonitorPauseFrameStatistics OBJECT-TYPE
        SYNTAX Counter64 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Pause frame statistics value "
             ::= { hwIfMonitorThresholdEntry 12 }  
             
hwIfMonitorPauseFrameThreshold OBJECT-TYPE
        SYNTAX Unsigned32 (0..4294967295)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Pause frame alarm threshold,and 0 is an invalid value "
        ::= { hwIfMonitorThresholdEntry 13 }
hwIfMonitorPauseFrameInterval OBJECT-TYPE
        SYNTAX Integer32 (0..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Pause frame alarm interval time(unit is second),and 0 is an invalid value "
        ::= { hwIfMonitorThresholdEntry 14 }  

hwIfMonitorDelayValue OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Delay value "
        ::= { hwIfMonitorThresholdEntry 15 }
           
hwIfMonitorDelayThreshold OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Delay alarm threshold "
        ::= { hwIfMonitorThresholdEntry 16 }  
        
hwIfMonitorJitterValue OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Jitter value "
        ::= { hwIfMonitorThresholdEntry 17 }
           
hwIfMonitorJitterThreshold OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Jitter alarm threshold "
        ::= { hwIfMonitorThresholdEntry 18 }

 hwIfMonitorName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Interface's name of alarm monitor table "
        ::= { hwIfMonitorThresholdEntry 19 }   
    
    hwIfMonitorSdhB1ErrorStatistics OBJECT-TYPE
        SYNTAX Counter64 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Sdh B1 error statistics value"
             ::= { hwIfMonitorThresholdEntry 20 }

    hwIfMonitorSdhB1ErrorThreshold OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Sdh B1 error alarm threshold,default value is 3"  
        ::= { hwIfMonitorThresholdEntry 21 }

    hwIfMonitorSdhB1ErrorInterval OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Sdh B1 error alarm interval time(unit is second),default value is 10s"
        ::= { hwIfMonitorThresholdEntry 22 }     
        
    hwIfMonitorSdhB2ErrorStatistics OBJECT-TYPE
        SYNTAX Counter64 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Sdh B2 error statistics value"
             ::= { hwIfMonitorThresholdEntry 23 }

    hwIfMonitorSdhB2ErrorThreshold OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Sdh error B2 alarm threshold,default value is 3"  
        ::= { hwIfMonitorThresholdEntry 24 }

    hwIfMonitorSdhB2ErrorInterval OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Sdh B2 error alarm interval time(unit is second),default value is 10s"
        ::= { hwIfMonitorThresholdEntry 25 }

    hwIfMonitorSymbolErrorStatistics OBJECT-TYPE
        SYNTAX Counter64 
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " Symbol error statistics value "
        ::= { hwIfMonitorThresholdEntry 26 }

    hwIfMonitorSymbolErrorThreshold OBJECT-TYPE
        SYNTAX Unsigned32 (0..4294967295)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Symbol error alarm threshold,default value is 3 "
        ::= { hwIfMonitorThresholdEntry 27 }

    hwIfMonitorSymbolErrorInterval OBJECT-TYPE
        SYNTAX Integer32 (1..65535)
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Symbol error alarm interval time(unit is second),default value is 10s"
        ::= { hwIfMonitorThresholdEntry 28 }
  
    hwIfMonitorAllStatistics OBJECT-TYPE
        SYNTAX Unsigned32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            " All statistics value "
        ::= { hwIfMonitorThresholdEntry 29 }
              
-- *******.4.1.2011.*********.********
hwIfMonitorBadBytesErrorStatistics OBJECT-TYPE
    SYNTAX Counter64
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Description."
    ::= { hwIfMonitorThresholdEntry 40 }


-- *******.4.1.2011.*********.********
hwIfMonitorBadBytesErrorThreshold OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Description."
    ::= { hwIfMonitorThresholdEntry 41 }


-- *******.4.1.2011.*********.********
hwIfMonitorBadBytesErrorInterval OBJECT-TYPE
    SYNTAX Integer32
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Description."
    ::= { hwIfMonitorThresholdEntry 42 }

-- *******.4.1.2011.*********.********
hwIfMonitorTxPauseFrameStatistics OBJECT-TYPE
    SYNTAX Counter64
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "TX pause frame statistics value."
    ::= { hwIfMonitorThresholdEntry 43 }

-- *******.4.1.2011.*********.********
hwIfMonitorTxPauseFrameHighThreshold OBJECT-TYPE
    SYNTAX Unsigned32 (0..4294967295)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TX pause frame high threshold, and 0 is an invalid value."
    ::= { hwIfMonitorThresholdEntry 44 }

-- *******.4.1.2011.*********.********
hwIfMonitorTxPauseFrameLowThreshold OBJECT-TYPE
    SYNTAX Unsigned32 (0..4294967295)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TX pause frame low threshold, and 0 is an invalid value."
    ::= { hwIfMonitorThresholdEntry 45 }

-- *******.4.1.2011.*********.********
hwIfMonitorTxPauseFrameInterval OBJECT-TYPE
    SYNTAX Integer32 (0..65535)
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "TX pause frame alarm interval time(unit is second), and 0 is an invalid value."
    ::= { hwIfMonitorThresholdEntry 46 }

-- *******.4.1.2011.*********.********
hwIfMonitorPostFECErrorStatistics OBJECT-TYPE
    SYNTAX Counter64
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "Post fec error corrected value."
    ::= { hwIfMonitorThresholdEntry 47 }


hwIfMonitorGeneral OBJECT IDENTIFIER ::= { hwIFExtObjects 8 }
    
hwIfMonitorCrcEnabledStatus OBJECT-TYPE
        SYNTAX EnabledStatus
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Enalbe or disable the crc error monitor, and the default value is disable."
        ::= { hwIfMonitorGeneral 1 }
        
hwIfMonitorSdhEnabledStatus OBJECT-TYPE
        SYNTAX EnabledStatus
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Enalbe or disable the sdh error monitor, and the default value is disable."
        ::= { hwIfMonitorGeneral 2 }
        
hwIfMonitorInputRateEnabledStatus  OBJECT-TYPE
        SYNTAX EnabledStatus
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Enalbe or disable the input rate monitor, and the default value is disable."
        ::= { hwIfMonitorGeneral 3}
        
hwIfMonitorOutputRateEnabledStatus  OBJECT-TYPE
        SYNTAX EnabledStatus
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Enalbe or disable the output rate monitor, and the default value is disable."
        ::= { hwIfMonitorGeneral 4 }

hwIfMonitorHalfDuplexEnabledStatus OBJECT-TYPE
        SYNTAX EnabledStatus
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Enalbe or disable the half duplex monitor, and the default value is disable."
        ::= { hwIfMonitorGeneral 5}
        
hwIfMonitorPauseRisingEnabledStatus OBJECT-TYPE
        SYNTAX EnabledStatus
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Enalbe or disable the pause frame rising monitor, and the default value is disable."
        ::= { hwIfMonitorGeneral 6 } 
        
hwIfMonitorPauseContinuingEnabledStatus OBJECT-TYPE
        SYNTAX EnabledStatus
        MAX-ACCESS read-write
        STATUS current
        DESCRIPTION
            " Enalbe or disable the pause frame continuing monitor, and the default value is disable."
        ::= { hwIfMonitorGeneral 7 }
        

-- *******.4.1.2011.*********.8.8
hwifMonitorBadBytesEnabledStatus OBJECT-TYPE
    SYNTAX EnabledStatus
    MAX-ACCESS read-write
    STATUS current
    DESCRIPTION
        "Description."
    ::= { hwIfMonitorGeneral 8 }


hwAdminVrrpMemberIf OBJECT IDENTIFIER ::= { hwIFExtObjects 9 }    

    hwIfFlowChangeTime OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS  accessible-for-notify
        STATUS  obsolete
        DESCRIPTION
                "The time indicates when the interface's flow status changes."
        ::= { hwAdminVrrpMemberIf 1 }
        
        
   hwAdminVrrpMemberIfTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HwAdminVrrpMemberIfEntry
        MAX-ACCESS not-accessible
        STATUS obsolete
        DESCRIPTION 
        "The interface bound to VRRP table."
        ::= { hwAdminVrrpMemberIf  2 }
        
    hwAdminVrrpMemberIfEntry  OBJECT-TYPE
        SYNTAX HwAdminVrrpMemberIfEntry
        MAX-ACCESS not-accessible
        STATUS obsolete
        DESCRIPTION 
               "Entries of the interface bound to VRRP table."
        INDEX{ hwAdminVrrpMemberIfIndex }
        ::= { hwAdminVrrpMemberIfTable 1}

    HwAdminVrrpMemberIfEntry  ::=
    SEQUENCE {
        hwAdminVrrpMemberIfIndex
                        InterfaceIndex,
        hwAdminVrrpVrid
                Integer32,
        hwAdminVrrpIfIndex
                InterfaceIndex,
        hwAdminVrrpMemberIfFlowStatus 
            INTEGER,                                
        hwAdminVrrpMemberIfRowStatus     
                        RowStatus          
        }

    hwAdminVrrpMemberIfIndex  OBJECT-TYPE
        SYNTAX  InterfaceIndex
        MAX-ACCESS not-accessible
        STATUS obsolete
        DESCRIPTION 
            "The number of the interface."
        ::= { hwAdminVrrpMemberIfEntry 1 }    

    hwAdminVrrpVrid  OBJECT-TYPE
        SYNTAX  Integer32
        MAX-ACCESS read-create
        STATUS obsolete
        DESCRIPTION 
            "The administrator VRRP vrid."
        ::= { hwAdminVrrpMemberIfEntry 2 }

    hwAdminVrrpIfIndex  OBJECT-TYPE
        SYNTAX  InterfaceIndex
        MAX-ACCESS read-create
        STATUS obsolete
        DESCRIPTION 
            "The administrator VRRP's configure interface index."
        ::= { hwAdminVrrpMemberIfEntry 3 }

    hwAdminVrrpMemberIfFlowStatus OBJECT-TYPE
        SYNTAX INTEGER
        {
          up(1),
          down(2)
        }       
        MAX-ACCESS read-only
        STATUS obsolete
        DESCRIPTION 
       " Current flow status of interface.
           1:up
           2:down"
        ::= { hwAdminVrrpMemberIfEntry 4 }         
                        
    hwAdminVrrpMemberIfRowStatus  OBJECT-TYPE
        SYNTAX  RowStatus
        MAX-ACCESS read-create
        STATUS obsolete
        DESCRIPTION 
        "Current operation status of the row."
        ::= { hwAdminVrrpMemberIfEntry 5 }
        
    hwIfFluxLimit OBJECT IDENTIFIER ::= { hwIFExtObjects 10 }
    
    hwIfFluxLimitTable  OBJECT-TYPE
        SYNTAX SEQUENCE OF HwIfFluxLimitEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION 
            " Flux limit table "
        ::= { hwIfFluxLimit  1 }

    hwIfFluxLimitEntry  OBJECT-TYPE  
        SYNTAX  HwIfFluxLimitEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION 
             " Entry of flux limit table "
        INDEX   { hwIfFluxIfIndex,hwIfFluxVlanId }
        ::= { hwIfFluxLimitTable 1}
    
    HwIfFluxLimitEntry ::=
        SEQUENCE { 
            hwIfFluxIfIndex
                InterfaceIndex,
            hwIfFluxVlanId
                VlanIdOrNone,
            hwIfFluxDirection
                HWDirectionType,
            hwIfFluxLimitType
                INTEGER,
            hwIfFluxCir
                Integer32,
            hwIfFluxCbs
                Integer32,
            hwIfFluxRowStatus
                RowStatus
         }

    hwIfFluxIfIndex OBJECT-TYPE
        SYNTAX InterfaceIndex
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            "Interface Index, same as ifIndex."
        ::= { hwIfFluxLimitEntry 1 }

    hwIfFluxVlanId OBJECT-TYPE
        SYNTAX VlanIdOrNone
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            "Vlan ID.
            0 is the default value"
        ::= { hwIfFluxLimitEntry 2 }
        
    hwIfFluxDirection OBJECT-TYPE
        SYNTAX HWDirectionType
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "Limit direction:
            inbound (1)
            outbound (2)"
        ::= { hwIfFluxLimitEntry 3 }
        
    hwIfFluxLimitType OBJECT-TYPE
        SYNTAX INTEGER 
        {
          broadcastSuppression(1),
          multicastSuppression(2),
          unknownUnicastSuppression(3)
        }
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "Flux limit type: broadcastSuppression(1),multicastSuppression(2),unknownUnicastSuppression(3)."
        ::= { hwIfFluxLimitEntry 4 }

    hwIfFluxCir OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "Committed Information Rate. Unit: kbps.
            software QoS: 8..155000
            hardware QoS: 100..10000000"
        ::= { hwIfFluxLimitEntry 5 }

    
    hwIfFluxCbs OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "Committed Burst Size. Unit: byte
            software QoS: 1875..19375000 
            hardware QoS: 64..33554432"
        ::= { hwIfFluxLimitEntry 6 }

 
    hwIfFluxRowStatus OBJECT-TYPE
        SYNTAX RowStatus
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "RowStatus. Three actions are used: active,
            createAndGo, destroy"
        ::= { hwIfFluxLimitEntry 7 }
        
        
        hwIfDiffServ OBJECT IDENTIFIER ::= { hwIFExtObjects 11 }
    
     hwIfDiffServTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HwIfDiffServEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION 
            "The table describes the different service mode of interfaces."
        ::= { hwIfDiffServ  1}


     hwIfDiffServEntry OBJECT-TYPE
        SYNTAX HwIfDiffServEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION 
            "Interface different service mode attribute configuration."
        INDEX   { hwIfDiffServIndex } 
        ::= {hwIfDiffServTable 1}

     HwIfDiffServEntry    ::=
        SEQUENCE { 
            hwIfDiffServIndex
                     InterfaceIndex,
            hwIfDiffServMode
                     INTEGER,
            hwIfDiffServServiceClass
                     INTEGER,
            hwIfDiffServColor
                     INTEGER
                 }
      hwIfDiffServIndex    OBJECT-TYPE
        SYNTAX  InterfaceIndex
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION   
            " The index of L2 interface. "
        ::= { hwIfDiffServEntry 1 }   
      
      hwIfDiffServMode OBJECT-TYPE
         SYNTAX  INTEGER
        {
                pipe(1),
                uniform(2),
                shortpipe(3),
                egresspipe(4),
                egressshortpipe(5),
                egressShortpipeTrustInnerVlan8021p(6),
                egressShortpipeTrustIpDscp(7) 
        }
         MAX-ACCESS  read-only
         STATUS  current
         DESCRIPTION
                "Different service mode of the interface binding to a 
                 VLL, default is uniform."
        ::= { hwIfDiffServEntry 2 }
    
      hwIfDiffServServiceClass OBJECT-TYPE
         SYNTAX INTEGER {
                                   default(0),
                                   be(1),
                                af1(2),
                                af2(3),
                                af3(4),
                                af4(5),
                                ef(6),
                                cs6(7),
                                cs7(8)
                        }
         MAX-ACCESS  read-only
         STATUS  current
         DESCRIPTION
                "PHB in the ingress PE, the value must be 
                 be,af1,af2,af3,af4,ef,cs6,cs7."
        ::= { hwIfDiffServEntry 3 }
    
      hwIfDiffServColor OBJECT-TYPE
         SYNTAX    INTEGER
            {
                default(0),
                green(1),
                yellow(2), 
                red(3)
            }
         MAX-ACCESS  read-only
         STATUS  current
         DESCRIPTION
                "Remarked color of packet in the ingress 
                 PE, the value must be green,yellow,red."
        ::= { hwIfDiffServEntry 4 }
        
-- ===========================================================================
--  ifname 4K VRF
-- ============================================================================ 
        hwIfQuery OBJECT IDENTIFIER ::= { hwIFExtObjects 12 }
         
        hwIfQueryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfQueryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table describes interface index information obtained based on the interface name. Currently, this table supports only the query function."
            ::= { hwIfQuery 1 }

        
        hwIfQueryEntry OBJECT-TYPE
            SYNTAX HwIfQueryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entries of private interface name table."
            INDEX { hwIfName }
            ::= { hwIfQueryTable 1 }

        
        HwIfQueryEntry ::=
            SEQUENCE {
                hwIfName
                    OCTET STRING,
                hwIfIndex
                    InterfaceIndex
             }
             
    hwIfName OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (1..47))
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
           "This object indicates an interface name."
    ::= { hwIfQueryEntry 1 }
                    
        hwIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "The value of this object identifies the interface index."
            ::= { hwIfQueryEntry 2 }
     
-- =========================================
-- Logic interface attribute Group
-- =========================================
        hwLogicIfAttrib OBJECT IDENTIFIER ::= { hwIFExtObjects 13 }

        
       hwLogicIfTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwLogicIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table describes how to create, delete, and display logical interfaces and sub-interfaces."
            ::= { hwLogicIfAttrib 1 }

        
        hwLogicIfEntry OBJECT-TYPE
            SYNTAX HwLogicIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Logic interface table."
            INDEX { hwLogicIfIndex }
            ::= { hwLogicIfTable 1 }

        
        HwLogicIfEntry ::=
            SEQUENCE { 
                hwLogicIfIndex
                    InterfaceIndexOrZero,
                hwLogicIfMainIndex
                    InterfaceIndexOrZero,
                hwLogicIfType
                    INTEGER,
                hwLogicIfName
                    DisplayString, 
                hwLogicIfParaOne 
                    INTEGER ,
                hwLogicIfRowStatus
                    RowStatus
             }

        hwLogicIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndexOrZero
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the interface index. The interface index to be created must be 0. Then the system automatically allocates an interface index."
            ::= { hwLogicIfEntry 1 }

        
        hwLogicIfMainIndex OBJECT-TYPE
            SYNTAX InterfaceIndexOrZero
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the interface index of the main interface to which the sub-interface belongs. For other interfaces, the interface index is 0."
            ::= { hwLogicIfEntry 11 }

        
        hwLogicIfType OBJECT-TYPE
            SYNTAX INTEGER
                {
                ve(1),
                loopback(2),
                vlanif(3),
                subVe(4),
                subEthTrunk(5),
                subEthernet(6),
                subAtm(7),
                imaGroup(8),
                subImaGroup(9),
                subSerial(10),
                tunnel(11),
                mpGroup(13),
                bridgeIf(14),
                subAtmTrunk(15),
                dslGroup(16),
                wlanEss(17),
                stackPort(18),
                globalImaGroup(19),
                subGlobalImaGroup(20),
                remoteAp(21),  
                vBridge(22),   
                atmBundle(23),
                mtunnel(24),
                subPosFr(25),
                globalVe(26),
                subGlobalVe(27),
                nve(28),
                vt(29),
                fcoe(30),
                lmpif(31),
                serviceIf(32),
                virtualSerial(33),
                pwVe(34),
                subPwVe(35),
                vbdIf(36),
                gmplsUni(37),
                virtualIf(38)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the interface type."
            ::= { hwLogicIfEntry 12 }         

        
        hwLogicIfName OBJECT-TYPE
            SYNTAX DisplayString (SIZE(1..64))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the interface name. The interface name must be a name without blank spaces and Tab characters."
            ::= { hwLogicIfEntry 13 }  
              
            
        hwLogicIfParaOne OBJECT-TYPE
            SYNTAX INTEGER  
                {
                p2p(1),
                p2mp(2),
                l2subif(3),
                none(255)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This object indicates the extension parameter of a logical interface. The default value is p2mp. For other interfaces, this object returns none after the get operation."
            ::= { hwLogicIfEntry 14 }


        
        hwLogicIfRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the status of the row."
            ::= { hwLogicIfEntry 51 }       
        
        
-- =========================================
-- Logic interface help attribute Group
-- =========================================
       
        hwLogicIfHelpTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwLogicIfHelpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Logic interface help table."
            ::= { hwLogicIfAttrib 2 }

        
        hwLogicIfHelpEntry OBJECT-TYPE
            SYNTAX HwLogicIfHelpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Logic interface help table."
            INDEX { hwLogicIfhelpType }
            ::= { hwLogicIfHelpTable 1 }

        
        HwLogicIfHelpEntry ::=
            SEQUENCE { 
                hwLogicIfhelpType
                    INTEGER,
                hwLogicIfChassisNumber
                    OCTET STRING,
                hwLogicIfSlotNumber
                    OCTET STRING,
                hwLogicIfCardNumber
                    OCTET STRING, 
                hwLogicIfMin 
                    Integer32,
                hwLogicIfMax
                    Integer32,   
                hwLogicIfTotal
                    Integer32
             }

        hwLogicIfhelpType OBJECT-TYPE
            SYNTAX INTEGER
               {
                ve(1),
                imaGroup(8),
                tunnel(11),
                mpGroup(13),
                dslGroup(16)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the types of logical interfaces that can be queried."
            ::= { hwLogicIfHelpEntry 1 }

        
        hwLogicIfChassisNumber OBJECT-TYPE
            SYNTAX  OCTET STRING(SIZE (1..2))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the ID of the chassis that supports the creation of the specified logical interface."
            ::= { hwLogicIfHelpEntry 2 }

        
        hwLogicIfSlotNumber OBJECT-TYPE
            SYNTAX OCTET STRING(SIZE (1..32))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the ID of the slot that supports the creation of the specified logical interface."
            ::= { hwLogicIfHelpEntry 3 }      

        
        hwLogicIfCardNumber OBJECT-TYPE
            SYNTAX OCTET STRING(SIZE (1..512))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of the card that supports the creation of the specified logical interface."
            ::= { hwLogicIfHelpEntry 4 }  
              
            
        hwLogicIfMin OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the minimum port number of the specified logical interface."
            ::= { hwLogicIfHelpEntry 5 }

        hwLogicIfMax OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the maximum port number of the specified logical interface."
            ::= { hwLogicIfHelpEntry 6 }

        hwLogicIfTotal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the maximum number of the specified logical interface."
            ::= { hwLogicIfHelpEntry 7 }   
    
-- =========================================
-- Logic interface dynamic help attribute Group
-- =========================================
       
        hwLogicIfDynamicHelpTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwLogicIfDynamicHelpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Logic interface dynamic help table."
            ::= { hwLogicIfAttrib 3 }

        
        hwLogicIfDynamicHelpEntry OBJECT-TYPE
            SYNTAX HwLogicIfDynamicHelpEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Logic interface dynamic help table."
            INDEX { hwLogicDynamicIfhelpType,
                    hwLogicDynamicIfChassisNumber,
                    hwLogicDynamicIfSlotNumber,
                    hwLogicDynamicIfCardNumber
                  }
            ::= { hwLogicIfDynamicHelpTable 1 }

        
        HwLogicIfDynamicHelpEntry ::=
            SEQUENCE { 
                hwLogicDynamicIfhelpType
                    INTEGER,
                hwLogicDynamicIfChassisNumber
                    Integer32,
                hwLogicDynamicIfSlotNumber
                    Integer32,
                hwLogicDynamicIfCardNumber
                    Integer32, 
                hwLogicDynamicIfMin 
                    Integer32,
                hwLogicDynamicIfMax
                    Integer32,   
                hwLogicDynamicIfTotal
                    Integer32
             }

        hwLogicDynamicIfhelpType OBJECT-TYPE
            SYNTAX INTEGER
               {
                ve(1),
                imaGroup(8),
                tunnel(11),
                mpGroup(13),
                dslGroup(16)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the types of logical interfaces that can be queried."
            ::= { hwLogicIfDynamicHelpEntry 1 }

        
        hwLogicDynamicIfChassisNumber OBJECT-TYPE
            SYNTAX  Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the ID of the chassis that that can be queried."
            ::= { hwLogicIfDynamicHelpEntry 2 }

        
        hwLogicDynamicIfSlotNumber OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the ID of the slot that can be queried."
            ::= { hwLogicIfDynamicHelpEntry 3 }      

        
        hwLogicDynamicIfCardNumber OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the ID of the card that can be queried."
            ::= { hwLogicIfDynamicHelpEntry 4 }  
              
            
        hwLogicDynamicIfMin OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the minimum port number of the specified logical interface."
            ::= { hwLogicIfDynamicHelpEntry 5 }

        hwLogicDynamicIfMax OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the maximum port number of the specified logical interface."
            ::= { hwLogicIfDynamicHelpEntry 6 }

        hwLogicDynamicIfTotal OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the maximum number of the specified logical interface."
            ::= { hwLogicIfDynamicHelpEntry 7 }   
      


-- ============================================================================
-- cpu packet statistic table
-- ============================================================================

        hwCppsObjects OBJECT IDENTIFIER ::= { hwIFExtObjects 14 }      


        hwCppsGlobalEnable  OBJECT-TYPE
             SYNTAX  EnabledStatus 
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Whether statistic function is enabled globally, and the default value is disable."
             ::= { hwCppsObjects 1 }
             
       
-- ======================= Cpps interface table =========================              
        hwCppsInterfaceTable  OBJECT-TYPE
            SYNTAX SEQUENCE OF HwCppsInterfaceEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "CPU packet and byte statistic interface configuration table."
            ::= { hwCppsObjects  2 }


        hwCppsInterfaceEntry  OBJECT-TYPE
            SYNTAX HwCppsInterfaceEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "The entry of hwCppsInterfaceTable."
            INDEX   { hwCppsInterfaceIndex }
            ::= { hwCppsInterfaceTable 1}

    
        HwCppsInterfaceEntry  ::=
        SEQUENCE {
              hwCppsInterfaceIndex    InterfaceIndex  ,
              hwCppsPortPvcEnable   EnabledStatus   ,
              hwCppsPortVlanEnable   EnabledStatus   
              }


        hwCppsInterfaceIndex  OBJECT-TYPE
             SYNTAX    InterfaceIndex 
             MAX-ACCESS not-accessible
             STATUS current
             DESCRIPTION "Index number of the interface."
             ::= { hwCppsInterfaceEntry 1 }


        hwCppsPortPvcEnable  OBJECT-TYPE
             SYNTAX  EnabledStatus 
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION "Whether statistic function is enabled on PVC."
             DEFVAL      { disabled }
             ::= { hwCppsInterfaceEntry 2 }


        hwCppsPortVlanEnable  OBJECT-TYPE
             SYNTAX  EnabledStatus 
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION "Whether statistic function is enabled on VLAN."
             DEFVAL      { disabled }
             ::= { hwCppsInterfaceEntry 3 }


-- ======================= Cpps interface statistics table =========================              
        hwCppsIfStatisticsTable  OBJECT-TYPE
            SYNTAX SEQUENCE OF HwCppsIfStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "CPU packet and byte statistic interface table."
            ::= { hwCppsObjects  3 }


        hwCppsIfStatisticsEntry  OBJECT-TYPE
            SYNTAX HwCppsIfStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "The entry of hwCppsIfStatisticsTable."
            INDEX   { hwCppsIfStatisticsIndex }
            ::= {  hwCppsIfStatisticsTable 1}

       
       HwCppsIfStatisticsEntry ::=
        SEQUENCE {
              hwCppsIfStatisticsIndex   InterfaceIndex  ,
              hwCppsInterfacePktStatisic  Counter64   ,
              hwCppsInterfaceByteStatisic  Counter64   ,
              hwCppsResetInterfaceStatisic  INTEGER
              }


       hwCppsIfStatisticsIndex   OBJECT-TYPE
             SYNTAX    InterfaceIndex 
             MAX-ACCESS not-accessible
             STATUS current
             DESCRIPTION "Index number of the interface."
             ::= { hwCppsIfStatisticsEntry  1 }


       hwCppsInterfacePktStatisic OBJECT-TYPE
            SYNTAX  Counter64 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION   "Packet statistic information of the interface sent to CPU."
            ::= { hwCppsIfStatisticsEntry  2 }


      hwCppsInterfaceByteStatisic OBJECT-TYPE
            SYNTAX  Counter64 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION   "Byte statistic information of the interface sent to CPU."
            ::= { hwCppsIfStatisticsEntry  3 } 


      hwCppsResetInterfaceStatisic OBJECT-TYPE
            SYNTAX  INTEGER{
            reset(1),
            unreset(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION   "Whether to reset statistics."
            DEFVAL      { unreset }
            ::= { hwCppsIfStatisticsEntry  4 }


-- ======================= Cpps ATM pvc table ========================= 
        hwCppsAtmPvcTable  OBJECT-TYPE
            SYNTAX SEQUENCE OF HwCppsAtmPvcEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "CPU packet and byte statistic ATM PVC table."
            ::= { hwCppsObjects  4 }


        hwCppsAtmPvcEntry  OBJECT-TYPE
            SYNTAX HwCppsAtmPvcEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "The entry of hwCppsAtmPvcTable."
            INDEX   { hwCppsAtmIfIndex, hwCppsAtmVpi, hwCppsAtmVci }
            ::= { hwCppsAtmPvcTable 1}


        HwCppsAtmPvcEntry  ::=
            SEQUENCE {                 
            hwCppsAtmIfIndex   InterfaceIndex  ,
            hwCppsAtmVpi    AtmVpIdentifier   ,
            hwCppsAtmVci  AtmVcIdentifier   ,
            hwCppsAtmPvcPktStatisic  Counter64   ,
            hwCppsAtmPvcByteStatisic  Counter64   ,
            hwCppsResetAtmPvcStatisic  INTEGER
            }


       hwCppsAtmIfIndex  OBJECT-TYPE
             SYNTAX    InterfaceIndex 
             MAX-ACCESS not-accessible
             STATUS current
             DESCRIPTION "Index number of the interface."
             ::= { hwCppsAtmPvcEntry 1 }


       hwCppsAtmVpi  OBJECT-TYPE
             SYNTAX    AtmVpIdentifier 
             MAX-ACCESS not-accessible
             STATUS current
             DESCRIPTION "VPI."
             ::= { hwCppsAtmPvcEntry 2 }


       hwCppsAtmVci  OBJECT-TYPE
             SYNTAX    AtmVcIdentifier 
             MAX-ACCESS not-accessible
             STATUS current
             DESCRIPTION "VCI."
             ::= { hwCppsAtmPvcEntry 3 }


       hwCppsAtmPvcPktStatisic OBJECT-TYPE
            SYNTAX  Counter64 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION   "Packet statistic information of the PVC sent to CPU."
            ::= { hwCppsAtmPvcEntry 4 } 

       hwCppsAtmPvcByteStatisic OBJECT-TYPE
            SYNTAX  Counter64 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION   "Byte statistic information of the PVC sent to CPU."
            ::= { hwCppsAtmPvcEntry 5 } 


       hwCppsResetAtmPvcStatisic OBJECT-TYPE
            SYNTAX  INTEGER{
            reset(1),
            unreset(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION   "Whether to reset statistics."
            DEFVAL      { unreset }
            ::= { hwCppsAtmPvcEntry 6 }


-- ======================= Cpps port vlan table ========================= 
        hwCppsPortVlanTable  OBJECT-TYPE
            SYNTAX SEQUENCE OF HwCppsPortVlanEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "CPU packet and byte statistic port VLAN table."
            ::= { hwCppsObjects  5 }


        hwCppsPortVlanEntry  OBJECT-TYPE
            SYNTAX HwCppsPortVlanEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION "The entry of hwCppsPortVlanTable."
            INDEX   { hwCppsPortIndex, hwCppsVlanId }
            ::= { hwCppsPortVlanTable 1}


        HwCppsPortVlanEntry  ::=
            SEQUENCE {                 
            hwCppsPortIndex   InterfaceIndex  ,
            hwCppsVlanId    VlanIdOrNone   ,
            hwCppsPortVlanPktStatisic  Counter64   ,
            hwCppsPortVlanByteStatisic  Counter64   ,
            hwCppsResetPortVlanStatisic  INTEGER
            }


        hwCppsPortIndex  OBJECT-TYPE
             SYNTAX    InterfaceIndex 
             MAX-ACCESS not-accessible
             STATUS current
             DESCRIPTION "Index number of the interface."
             ::= { hwCppsPortVlanEntry 1 }


        hwCppsVlanId  OBJECT-TYPE
             SYNTAX    VlanIdOrNone 
             MAX-ACCESS not-accessible
             STATUS current
             DESCRIPTION "VLAN ID."
             ::= { hwCppsPortVlanEntry 2 }


        hwCppsPortVlanPktStatisic OBJECT-TYPE
            SYNTAX  Counter64 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION   "Packet statistic information of the port sent to CPU."
            ::= { hwCppsPortVlanEntry 3 } 


        hwCppsPortVlanByteStatisic OBJECT-TYPE
            SYNTAX  Counter64 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION   "Byte statistic information of the port sent to CPU."
            ::= { hwCppsPortVlanEntry 4 } 


       hwCppsResetPortVlanStatisic OBJECT-TYPE
            SYNTAX  INTEGER{
            reset(1),
            unreset(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION   "Whether to reset statistics."
            DEFVAL      { unreset }
            ::= { hwCppsPortVlanEntry 5 }

                        
                        
-- =========================================
-- PortIsolation Group
-- =========================================
        hwPortIsolationGroupAttrib OBJECT IDENTIFIER ::= { hwIFExtObjects 15 }

        
       hwPortIsolationGroupTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwPortIsolationGroupEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Logic interface table."
            ::= { hwPortIsolationGroupAttrib 1 }

        
        hwPortIsolationGroupEntry OBJECT-TYPE
            SYNTAX HwPortIsolationGroupEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Logic interface table."
            INDEX { hwPortIsolationGroupIndex }
            ::= { hwPortIsolationGroupTable 1 }

        
        HwPortIsolationGroupEntry ::=
            SEQUENCE { 
                hwPortIsolationGroupIndex
                    Integer32 (0..63),
                hwPortIsolationGroupPortList
                    PortList, 
                hwPortIsolationGroupRowStatus
                    RowStatus
             }

        hwPortIsolationGroupIndex OBJECT-TYPE
            SYNTAX Integer32 (0..63)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "ID of Port-Isolation-Group."
            ::= { hwPortIsolationGroupEntry 1 }

        
        hwPortIsolationGroupPortList OBJECT-TYPE
            SYNTAX PortList
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "PortList of Port-Isolation-Group."
            ::= { hwPortIsolationGroupEntry 2 }

                
        hwPortIsolationGroupRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the status of the row."
            ::= { hwPortIsolationGroupEntry 3 }       
        

--  ============================================================================
-- interface vtrunk attribute group
-- ============================================================================
        hwVTrunkAttr OBJECT IDENTIFIER ::= { hwIFExtObjects 16 }

        
        hwVTrunkIfTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwVTrunkIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The vtrunk table."
            ::= { hwVTrunkAttr 1 }

        
        hwVTrunkIfEntry OBJECT-TYPE
            SYNTAX HwVTrunkIfEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entries of the vtrunk table."
            INDEX { hwVTrunkIfIndex }
            ::= { hwVTrunkIfTable 1 }

        
        HwVTrunkIfEntry ::=
            SEQUENCE { 
                hwVTrunkIfIndex
                    InterfaceIndex,
                hwVTrunkIfID
                    Integer32,
                hwVTrunkIfType
                    INTEGER,
                hwVTrunkIfRowStatus
                    RowStatus                
             }

        hwVTrunkIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                " The IfIndex of the VTrunk interface."
            ::= { hwVTrunkIfEntry 1 }

        
        hwVTrunkIfID OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " The identification of the VTrunk interface.It may identify VTrunk
                and is an index of the interface."
            ::= { hwVTrunkIfEntry 2 }

        
        hwVTrunkIfType OBJECT-TYPE
            SYNTAX INTEGER
                {
                posTrunk(1),
                cposTrunk(2),
                atmTrunk(3),
                atmBundle(4)
                }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " The type of the VTrunk interface includes pos-Trunk, cpos-Trunk, atm-trunk 
                and atm-bundle."
            ::= { hwVTrunkIfEntry 3 }      
        
        
        hwVTrunkIfRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " Current operation status of the row."
            ::= { hwVTrunkIfEntry 50 }


-- =========================================
-- VTrunk member attribute Group
-- =========================================
        hwVTrunkMemAttr OBJECT IDENTIFIER ::= { hwIFExtObjects 17 }

        
        hwVTrunkMemTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwVTrunkMemEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "VTrunk member attribute information table."
            ::= { hwVTrunkMemAttr 1 }

        
        hwVTrunkMemEntry OBJECT-TYPE
            SYNTAX HwVTrunkMemEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "VTrunk member attribute information."
            INDEX { hwVTrunkMemIfIndex }
            ::= { hwVTrunkMemTable 1 }

        
        HwVTrunkMemEntry ::=
            SEQUENCE { 
                hwVTrunkMemIfIndex
                    Integer32,
                hwVTrunkIfnetIndex
                    Integer32,
                hwVTrunkValidEntry
                    INTEGER,
                hwVTrunkOperstatus
                    INTEGER,
                hwVTrunkPortActive
                    INTEGER,
                hwVTrunkRowStatus
                    RowStatus
             }

        hwVTrunkMemIfIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "VTrunk port index."
            ::= { hwVTrunkMemEntry 1 }

        hwVTrunkIfnetIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The IfnetIndex of the VTrunk interface."
            ::= { hwVTrunkMemEntry 2 }

        hwVTrunkValidEntry OBJECT-TYPE
            SYNTAX INTEGER
                {
                valid(1),
                invalid(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Valid Entries of VTrunk interface."
            DEFVAL { invalid }
            ::= { hwVTrunkMemEntry 3 }

        
        hwVTrunkOperstatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                up(1),
                down(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Status of operation, indicates port status. There are vtrunk_up(1)
                and vtrunk_down(2).It expresses whether port is shutdown."
            DEFVAL { down }
            ::= { hwVTrunkMemEntry 4 }

        
        hwVTrunkPortActive OBJECT-TYPE
            SYNTAX INTEGER
                {
                portInactive(1),
                portActive(2)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Active port of backup-access vtrunk or not
                1: portInactive
                2: portActive."
            ::= { hwVTrunkMemEntry 5 }

        
        hwVTrunkRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Current operation status of the row. "
            ::= { hwVTrunkMemEntry 50 }

-- =========================================
-- SubInterfaceBackupTrunk attribute Group
-- =========================================
        hwMasterBackupTrunkSubinterfaceAttr OBJECT IDENTIFIER ::= { hwIFExtObjects 18 }

        
        hwMasterBackupTrunkSubinterfaceTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMasterBackupTrunkSubinterfaceEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Master-Backup Trunk Subinterface attribute information table."
            ::= { hwMasterBackupTrunkSubinterfaceAttr 1 }

        
        hwMasterBackupTrunkSubinterfaceEntry OBJECT-TYPE
            SYNTAX HwMasterBackupTrunkSubinterfaceEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Master-Backup Trunk Subinterface attribute information."
            INDEX { hwBackupTrunkIfIndex }
            ::= { hwMasterBackupTrunkSubinterfaceTable 1 }

        
        HwMasterBackupTrunkSubinterfaceEntry ::=
            SEQUENCE { 
                hwBackupTrunkIfIndex
                    InterfaceIndex,
                hwBackupStatus
                    INTEGER,
                hwRevertiveMode
                    INTEGER,
                hwWtrTime
                    Integer32,
                hwFlushVlanId
                    VlanIdOrNone
             }

        hwBackupTrunkIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates index of master/backup trunk sub-interface."
            ::= { hwMasterBackupTrunkSubinterfaceEntry 1 }

        hwBackupStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                init(1),
                master(2),
                backup(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of this object identifies backupStatus of master/backup trunk sub-interface."
            DEFVAL { init }
            ::= { hwMasterBackupTrunkSubinterfaceEntry 2 }

        
        hwRevertiveMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                revertive(1),
                nonRevertive(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The value of this object identifies revertive-mode of master/backup trunk sub-interface."
            DEFVAL { revertive }
            ::= { hwMasterBackupTrunkSubinterfaceEntry 3 }

        
        hwWtrTime OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The value of this object identifies the wait-to-restore time of master/backup trunk sub-interface."
            DEFVAL { 0 }
            ::= { hwMasterBackupTrunkSubinterfaceEntry 4 }
            
        hwFlushVlanId OBJECT-TYPE
            SYNTAX VlanIdOrNone
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates VLAN ID carried in a FlushPDU."
            DEFVAL { 0 }
            ::= { hwMasterBackupTrunkSubinterfaceEntry 5 }
        
                         
            hwVaspPort OBJECT IDENTIFIER ::= { hwIFExtObjects 19 }
        
            
            hwVaspPortPeerMacTable OBJECT-TYPE
               SYNTAX SEQUENCE OF HwVaspPortPeerMacEntry
               MAX-ACCESS not-accessible
               STATUS current
               DESCRIPTION
                 "Vasp port peer MAC table."
               ::= { hwVaspPort 1 }

    
        hwVaspPortPeerMacEntry OBJECT-TYPE
          SYNTAX HwVaspPortPeerMacEntry
          MAX-ACCESS not-accessible
          STATUS current
          DESCRIPTION
            "Vasp port peer MAC table entry."
          INDEX { hwVaspPortIfIndex }
          ::= { hwVaspPortPeerMacTable 1 }
    
        
        HwVaspPortPeerMacEntry ::=
          SEQUENCE { 
            hwVaspPortIfIndex
              InterfaceIndex,
            hwVaspPortName
              DisplayString,
            hwVaspPortPeerMac
              PhysAddress
           }
    
        hwVaspPortIfIndex OBJECT-TYPE
          SYNTAX InterfaceIndex
          MAX-ACCESS not-accessible
          STATUS current
          DESCRIPTION
            "The index of interface."
          ::= { hwVaspPortPeerMacEntry 1 }
    
        
        hwVaspPortName OBJECT-TYPE
          SYNTAX DisplayString
          MAX-ACCESS read-only
          STATUS current
          DESCRIPTION
            "The name of vasp port."
          ::= { hwVaspPortPeerMacEntry 2 }
    
        
        hwVaspPortPeerMac OBJECT-TYPE
          SYNTAX PhysAddress
          MAX-ACCESS read-only
          STATUS current
          DESCRIPTION
            "The peer MAC of port."
          ::= { hwVaspPortPeerMacEntry 3 }
          

        hwIFExtTrapObjects OBJECT IDENTIFIER ::= { hwIFExtObjects 20 }
    
 
        hwLinkDownReason  OBJECT-TYPE
            SYNTAX      INTEGER {
                        physicalLinkDown(1),        --  interface phycial link down
                        lacpNegotiationFailed(2),   --  LACP negotiation failed
                        receiveConfReqPacket(3),    --  receive ConfReq packet
                        receiveConfAckPacket(4),    --  receive ConfAck packet
                        receiveNakPacket(5),        --  receive Nak packet
                        receiveTermPacket(6),       --  receive Term packet
                        receiveTermAckPacket(7),    --  receive TermAck packet
                        receiveCodeRejPacket(8),    --  receive CodeRej packet
                        receiveProtoRejPacket(9),   --  receive ProtoRej packet
                        chapAuthenticationFailed(10),   --  CHAP authentication failed
                        papAuthenticationFailed(11),    --  PAP authentication failed
                        keepaliveOutOfTime(12),         --  Keepalive out of time
                        pvcDown(13),                    --  PVC down
                        efmSessionFailed(14),           --  EFM session failed
                        tunnelDownOrInexist(15),        --  tunnel down or tunnel not exist
                        admindown(16),                  --  shutdown
                        protocoldown(17),               -- protocol down
                        adminup(18),                    -- undo shutdown
                        protocolup(19),                 -- protocol up
                        mainifdown(20),                 -- main interface down
                        physicalLinkIsUp(21),                   -- Interface physical link is Up
                        conditionsForActivationNotMet(22),      -- The conditions for the activation of the interface are not met
                        conditionsForActivationAreMet(23),      -- The conditions for the activation of the interface are met
                        tunnelIsUp(24),                          -- The tunnel is Up
                        interfaceIsDeleted(25),                  -- The interface is deleted
                        bfdSessionDown(26),                  -- bfd session is down
                        bfdSessionUp(27),                  -- bfd session is up  
                        efmSessionUp(28),                  -- efm session is up
                        portAlarmDown(29),                 -- port alarm down
                        dldpIsDown(30),                    -- dldp down
                        dldpIsUp(31),                      -- dldp up
                        vrrpFlowDown(32),                  -- vrrp flow down
                        vrrpFlowUp(33),                    -- vrrp flow up
                        veFlowDown(34),                    -- ve flow down
                        veFlowUp(35),                      -- ve flow up
                        errorDown(36),                     -- The interface is error down
                        crcErrorDown(37),                  -- crc error down
                        crcErrorUp(38),                    -- crc error up
                        transceiverSpeedMismatch(39),      -- Tranceiver speed does not match the speed configured on the port
                        transceiverTypeMismatch(40),       -- The port does not support current tranceiver
                        negotiationUnsupported(41),         -- Tranceiver does not support the negotiation configuration on the port
                        linkHeartBeatDown(42) ,        -- link heart beat down
                        triggerDown(43),           -- The interface is trigger down
                        cfmSessionDown(48),       -- cfm session is down
                        cfmSessionUp(49)          -- cfm session is up
                }
        MAX-ACCESS  accessible-for-notify
        STATUS      current
        DESCRIPTION
               "The reason code of link changes."
        ::= { hwIFExtTrapObjects 1 }
       
       hwMainIfName  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The main interface name."
            ::= { hwIFExtTrapObjects 2 }
       
       hwCfmOverPhysicalName  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               "The configuration of the interface board or interface card is restored."
            ::= { hwIFExtTrapObjects 3 }

        hwIpv6IfChangeDownReason  OBJECT-TYPE
            SYNTAX      INTEGER {
                        interfaceIsDown(1),        
                        ipv6AddressUnavailable(2),       
                        ipv6AddressAvailable(3),    
                        pppIpcp6Down(4),        
                        disableIpv6ProtocolorDeleteInterface(6)
                }
	         MAX-ACCESS  read-only
	         STATUS      current
	         DESCRIPTION
	               "The reason code of link changes."
	         ::= { hwIFExtTrapObjects 4 }  
	                 
       hwTrunkIfDescr  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The name of trunk interface."
            ::= { hwIFExtTrapObjects 5 }    
            
       hwTrunkMemIfDescr  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The name of trunk member interface."
            ::= { hwIFExtTrapObjects 6 }  
                       
       hwTrunkActiveMember OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "The number of active trunk members."
            ::= { hwIFExtTrapObjects 7 }  
            
       hwIfExtTrapReason  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The reason of an event."
            ::= { hwIFExtTrapObjects 8 }
            
       hwLacpOldPDUInfo  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The old lacp PDU field."
            ::= { hwIFExtTrapObjects 9 } 
                        
       hwLacpNewPDUInfo  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The new lacp PDU field."
            ::= { hwIFExtTrapObjects 10 }
			
       hwLagMemberDownReason  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The lag member down reason."
            ::= { hwIFExtTrapObjects 11 }

        hwLicenseItemName  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The license item name."
            ::= { hwIFExtTrapObjects 12 }
            
        hwLicenseServiceDesc  OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               " The service description."
            ::= { hwIFExtTrapObjects 13 }
            
        hwTrunkIfStatus  OBJECT-TYPE
            SYNTAX INTEGER
                {
                up(1),
                down(2)
                }
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
               "The status of trunk."
            ::= { hwIFExtTrapObjects 14 }
	    
        hwType OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..32))
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "eth-trunk member changes type, only used for trap."
            ::= { hwIFExtTrapObjects 15 }		 
                 
        hwEthTrunkIfIndex OBJECT-TYPE
            SYNTAX Integer32 (1..65535)
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "eth-trunk Ifindex, only used for trap."
            ::= { hwIFExtTrapObjects 16 }
                         
        hwTrunkName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..32))
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "eth-trunk name, only used for trap."
            ::= { hwIFExtTrapObjects 17 }
                
        hwPortIfIndexList OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..128))
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "eth-trunk member ifindex list, only used for trap."
            ::= { hwIFExtTrapObjects 18 }
                
        hwPortNameList OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..256))
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                "eth-trunk member name list, only used for trap."
            ::= { hwIFExtTrapObjects 19 } 

        hwIfBandWidth OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The bandwidth of the interface. 
                The value is 64 bits. The unit is bit/s."
            ::= { hwIFExtTrapObjects 20 }
            
        hwIPv4StateChangeReason  OBJECT-TYPE
            SYNTAX      INTEGER {
                        interfaceIsDown(1),
                        isL2Interface(2),
                        protocolIsDown(3),
                        addressIsConflict(4),
                        addressIsDeleted(5),
                        protocolIsUp(6),
                        interfaceIsDeleted(7),
                        unnumberedIpIsConflict(8)
                }
	         MAX-ACCESS  accessible-for-notify
	         STATUS      current
	         DESCRIPTION
	               "The reason code of IPv4 state changes."
	         ::= { hwIFExtTrapObjects 21 }  
          			
--  ===========================================================================
-- Trunk map table
-- ===========================================================================
       hwTrunkMapAttr OBJECT IDENTIFIER ::= { hwIFExtObjects 21 }
		
	    hwTrunkMapTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwTrunkMapEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The trunk map table."
            ::= { hwTrunkMapAttr 1 }

        
        hwTrunkMapEntry OBJECT-TYPE
            SYNTAX HwTrunkMapEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Entries of the trunk map table."
            INDEX { hwTrunkMapType, hwTrunkMapID }
            ::= { hwTrunkMapTable 1 }
			
	HwTrunkMapEntry ::=
            SEQUENCE { 
                hwTrunkMapType
                    INTEGER,
		hwTrunkMapID
                    Integer32,
		hwTrunkMapIndex
                    Integer32              
             }
			 
	hwTrunkMapType OBJECT-TYPE
            SYNTAX INTEGER
                {
                ethTrunk(1),
                ipTrunk(2)
                }
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                " The type of the Trunk interface includes eth-trunk and ip-trunk.
                The ip-trunk is only comprised by pos link, otherwise the eth-trunk
                 is only comprised by ethernet link."
            ::= { hwTrunkMapEntry 1 }
			
	hwTrunkMapID OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS accessible-for-notify
            STATUS current
            DESCRIPTION
                " The identification of the Trunk interface.It may identify Trunk
                and is an index of the interface."
            ::= { hwTrunkMapEntry 2 }
        
        hwTrunkMapIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                " The number of the Trunk interface."
            ::= { hwTrunkMapEntry 3 }                     
			
--  ============================================================================   
        hwIFExtConformance OBJECT IDENTIFIER ::= { hwIFExtMib 2 }

        
        hwIFExtGroups OBJECT IDENTIFIER ::= { hwIFExtConformance 1 }

        
--  hwIFExtTraps OBJECT IDENTIFIER ::= { hwIFExtMib 2 }
-- hwIFExtConformance OBJECT IDENTIFIER ::= { hwIFExtMib 3 }
-- hwIFExtCompliances OBJECT IDENTIFIER ::= { hwIFExtConformance 1 }
-- ============================================================================    
        hwTrunkIfGroup OBJECT-GROUP
            OBJECTS { hwIFExtPhyStatus, hwIFExtMemberOf, hwTrunkIfMax, hwTrunkNextIndex, hwTrunkIndex, hwTrunkIfID, hwTrunkIfType, 
                hwTrunkIfIndex, hwTrunkIfModel, hwTrunkIfBandWidthAffectLinkNum, hwTrunkIfMinLinkNum, hwTrunkIfRowStatus, 
                hwTrunkIfWorkingMode, hwTrunkIfWorkingState, hwTrunkIfAutoRecover, hwTrunkIfPreemptEnable, hwTrunkIfPreemptDelay, 
                hwTrunkIfTimeoutReceive, hwTrunkBandwidth, hwTrunkIfFlushSendEnable, hwTrunkIfFlushVlanId, hwTrunkIfFlushPasswd, hwTrunkIfForceSwitchEnable, 
                hwTrunkIfStatReset, hwTrunkIfLagSelectedPortStd, hwTrunkIfLagMaxActiveLinkNum, hwTrunkETrunkPriority, hwTrunkETrunkSysID,hwTrunkETrunkPriorityReset,
                hwTrunkETrunkSysIDReset,hwTrunkLocalPrefMode, hwTrunkIfTrackVrrpVrid, hwTrunkIfTrackVrrpIfIndex, hwTrunkIfTrackVrrpReset }
            STATUS current
            DESCRIPTION 
                "A collection of objects indicating information of IP address of interface,
                contain IP address, IP address mask and IP address acquiring method."
            ::= { hwIFExtGroups 1 }

        
        hwIfIpAddressGroup OBJECT-GROUP
            OBJECTS { hwIpAdEntAddr, hwIpAdEntIfIndex, hwIpAdEntNetMask, hwIpAdEntBcastAddr, hwIpAdEntReasmMaxSize, 
                hwIpAdEntAddressType, hwIfIpMethod, hwIpAdEntAddrStatus,hwIfIpAddrEntIfIndex, hwIfIpAddrEntAddr, hwIfIpAddrEntType, 
                hwIfIpAddrEntPrefix, hwIfIpAddrEntOrigin, hwIfIpAddrEntStatus, hwIfIpAddrEntCreated, hwIfIpAddrEntLastChanged,
                hwIfIpAddrEntBcastAddr, hwIfIpAddrEntReasmMaxSize, hwIfIpAddrEntAddrType, hwIfIpAddrEntVpn  }
            STATUS current
            DESCRIPTION 
                "A collection of objects indicating attribute of the Trunk interface."
            ::= { hwIFExtGroups 2 }

        
-- hwIFExtHoldTime ,
        hwIFExtGroup OBJECT-GROUP
            OBJECTS { hwIFExtLayer, hwIFExtFrameType, hwIFExtFlowStatInterval, hwIFExtFlushReceiveEnable, hwIFExtFlushVlanId, 
hwIFExtFlushPasswd, hwTrunkSystemPriority, hwTrunkUnknownUnicastIfModel,
hwTrunkETrunkSystemPriority, hwTrunkETrunkSystemID, hwIFExtFlowStatus,
hwIFExtMtu,hwIFExtMacAddr, hwIFExtBlockPriority, hwIFExtMacShift,hwIFExtSuppressStatus, hwIFExtPoisonReverse, hwIFExtInputPktRate, hwIFExtInputHighPktRate, hwIFExtOutputPktRate, hwIFExtOutputHighPktRate, hwIFExtInputOctetRate, hwIFExtInputHighOctetRate, 
hwIFExtOutputOctetRate, hwIFExtOutputHighOctetRate, hwTrunkCount, hwTrunkMemberCountUpperThreshold, hwTrunkMemberCountLowerThreshold, hwIFExtSuppressStatusIPv6, hwTrunkSubinterfacesCount, hwTrunkSubinterfacesLimit}
            STATUS current
            DESCRIPTION 
                "A collection of objects indicating attribute of  interface extended."
            ::= { hwIFExtGroups 3 }

        
        hwTrunkMemGroup OBJECT-GROUP
            OBJECTS { hwTrunkMemifIndex, hwTrunkValidEntry, hwTrunkSelectStatus, hwTrunkLacpStatus, hwTrunkDeleteFlag, 
                hwTrunkOperstatus, hwTrunkIsDefaultLagRecv, hwTrunkPortWeight, hwTrunkPortStandby, hwTrunkPortMaster, 
                hwTrunkPortPriority, hwTrunkPortStatReset, hwTrunkRowStatus }
            STATUS current
            DESCRIPTION 
                "Trunk member attribute Group, now only can set hwTrunkPortWeight and hwTrunkPortStandby. hwTrunkSelectStatus and hwTrunkDeleteFlag can't be set. These attributes are for extend."
            ::= { hwIFExtGroups 4 }

        
        hwIFFlowStatGroup OBJECT-GROUP
            OBJECTS { hwIFFlowStatGlobalInterval }
            STATUS current
            DESCRIPTION 
                "A collection of objects indicating interval time of interface flow stat."
            ::= { hwIFExtGroups 5 }

    hwAdminVrrpMemberIfGroup OBJECT-GROUP
        OBJECTS {
            hwIfFlowChangeTime,
            hwAdminVrrpVrid,
            hwAdminVrrpIfIndex,
            hwAdminVrrpMemberIfFlowStatus,
            hwAdminVrrpMemberIfRowStatus          
        }
        STATUS      obsolete
        DESCRIPTION
            "A collection of objects indicating attribute of the interface track administrator VRRP."
        ::= { hwIFExtGroups 6 }  
        
        hwIfEtherStatGroup OBJECT-GROUP
            OBJECTS { hwIfEtherStatInPkts64Octets, hwIfEtherStatInPkts65to127Octets, hwIfEtherStatInPkts128to255Octets, hwIfEtherStatInPkts256to511Octets, 
                hwIfEtherStatInPkts512to1023Octets, hwIfEtherStatInPkts1024to1518Octets, 
                hwIfEtherStatInJumboPkts, hwIfEtherStatInCRCPkts, hwIfEtherStatInLongPkts, hwIfEtherStatInJabberPkts, hwIfEtherStatInFragmentPkts, 
                hwIfEtherStatInUnderSizePkts, hwIfEtherStatInOverRunPkts, hwIfEtherStatInPausePkts, hwIfEtherStatOutJumboPkts, hwIfEtherStatOutOverflowPkts, 
                hwIfEtherStatOutUnderRunPkts, hwIfEtherStatOutPausePkts, hwIfEthIfStatReset, hwIfEtherStatInDropEventPkts, hwIfEtherStatInAlignmentPkts, hwIfEtherStatInSymbolPkts,
                hwIfEtherStatInIgnoredPkts, hwIfEtherStatInFramePkts, hwIfEtherStatOutCollisionPkts, hwIfEtherStatOutDeferredPkts, hwIfEtherStatOutLateCollisionPkts,
                hwIfEtherStatOutExcessiveCollisionPkts, hwIfEtherStatOutBufferPurgationPkts }
            STATUS current
            DESCRIPTION 
                "Interface statistic Group."
            ::= { hwIFExtGroups 7 }

        
        hwIFExtCompliances OBJECT IDENTIFIER ::= { hwIFExtConformance 2 }

hwIfMonitorThresholdGroup OBJECT-GROUP
OBJECTS{
        hwIfMonitorCrcErrorStatistics   ,
        hwIfMonitorCrcErrorThreshold  ,
        hwIfMonitorCrcErrorInterval    ,
        hwIfMonitorSdhErrorStatistics   ,
        hwIfMonitorSdhErrorThreshold   ,
        hwIfMonitorSdhErrorInterval    ,
        hwIfMonitorInputRate  ,
        hwIfMonitorInputRateThreshold  ,
        hwIfMonitorOutputRate  ,
        hwIfMonitorOutputRateThreshold,
        hwIfMonitorPauseFrameStatistics,
        hwIfMonitorPauseFrameThreshold,
        hwIfMonitorPauseFrameInterval,
        hwIfMonitorDelayValue,
        hwIfMonitorDelayThreshold,
        hwIfMonitorJitterValue,
        hwIfMonitorJitterThreshold,
        hwIfMonitorSdhB1ErrorStatistics,
        hwIfMonitorSdhB1ErrorThreshold,
        hwIfMonitorSdhB1ErrorInterval,
        hwIfMonitorSdhB2ErrorStatistics,
        hwIfMonitorSdhB2ErrorThreshold,
        hwIfMonitorSdhB2ErrorInterval,
        hwIfMonitorSymbolErrorStatistics,
        hwIfMonitorSymbolErrorThreshold,
        hwIfMonitorSymbolErrorInterval,
        hwIfMonitorBadBytesErrorStatistics,
        hwIfMonitorBadBytesErrorThreshold,
        hwIfMonitorBadBytesErrorInterval,
        hwIfMonitorTxPauseFrameStatistics,
        hwIfMonitorTxPauseFrameHighThreshold,
        hwIfMonitorTxPauseFrameLowThreshold,
        hwIfMonitorTxPauseFrameInterval,
        hwIfMonitorPostFECErrorStatistics,
        hwIfMonitorName,
	hwIfMonitorAllStatistics
    }
    STATUS current
    DESCRIPTION
    "Alarm Monitor Group."
    ::= { hwIFExtGroups 8 } 
hwIfMonitorGeneralGroup OBJECT-GROUP
OBJECTS{
        hwIfMonitorCrcEnabledStatus ,    
        hwIfMonitorSdhEnabledStatus ,     
        hwIfMonitorInputRateEnabledStatus ,   
        hwIfMonitorOutputRateEnabledStatus ,    
        hwIfMonitorHalfDuplexEnabledStatus,
        hwIfMonitorPauseRisingEnabledStatus,
        hwIfMonitorPauseContinuingEnabledStatus,
        hwifMonitorBadBytesEnabledStatus 
    }
    STATUS current    
    DESCRIPTION
    "Alarm Monitor Enable Group."    
    ::= { hwIFExtGroups 9 }
    
    hwIfFluxLimitGroup OBJECT-GROUP
    OBJECTS{     
        hwIfFluxDirection ,   
        hwIfFluxLimitType ,    
        hwIfFluxCir,
        hwIfFluxCbs,
        hwIfFluxRowStatus
    }
    STATUS current    
    DESCRIPTION
    "Flux limit Group."    
    ::= { hwIFExtGroups 10 } 


    hwIfDiffServGroup OBJECT-GROUP
OBJECTS{
       hwIfDiffServMode, 
    hwIfDiffServServiceClass,
    hwIfDiffServColor    
    }
    STATUS current
    DESCRIPTION
    "Interface statistic Group."
    ::= { hwIFExtGroups 11 }    

    hwIfQueryGroup OBJECT-GROUP
 OBJECTS{
           hwIfIndex
    }
    STATUS current
    DESCRIPTION
    "Interface name Group."
    ::= { hwIFExtGroups 12 }
    
    hwLogicIfAttrGroup OBJECT-GROUP
 OBJECTS{
           hwLogicIfMainIndex,
           hwLogicIfType,
           hwLogicIfName,   
           hwLogicIfParaOne,
           hwLogicIfRowStatus
    }
    STATUS current
    DESCRIPTION
    "Logic interface table Group."
    ::= { hwIFExtGroups 13 }      
    
    hwIfIpUnnumberedGroup OBJECT-GROUP
 OBJECTS{
           hwLendIfIndex,
           hwLendIpAddr,
           hwLendIpAddrNetMask,
           hwUnnumberedRowStatus
    }
    STATUS current
    DESCRIPTION
    "Logic interface table Group."
    ::= { hwIFExtGroups 14 }

    hwLinkModeChangeAutoCreateIfGroup OBJECT-GROUP  
  OBJECTS{
           hwNewIfTimeslot
    }
    STATUS current
    DESCRIPTION
    "Interface time slot Group."
    ::= { hwIFExtGroups 15 }

    hwCppsGlobalEnableGroup OBJECT-GROUP
            OBJECTS { hwCppsGlobalEnable }
            STATUS current
            DESCRIPTION 
                "A collection of objects indicating global CPU packet statistic function."
            ::= { hwIFExtGroups 16 }

    
    hwCppsInterfaceGroup OBJECT-GROUP
    OBJECTS {
        hwCppsPortPvcEnable   ,
        hwCppsPortVlanEnable   
    }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing interface configuration function."
    ::= { hwIFExtGroups 17 }

 
    hwCppsIfStatisticsGroup OBJECT-GROUP
    OBJECTS {
        hwCppsInterfacePktStatisic   ,
        hwCppsInterfaceByteStatisic   ,
        hwCppsResetInterfaceStatisic
    }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing packet and byte statistic of the interface sent to CPU."
    ::= { hwIFExtGroups 18 }


    hwCppsAtmPvcGroup OBJECT-GROUP
    OBJECTS {
        hwCppsAtmPvcPktStatisic   ,
        hwCppsAtmPvcByteStatisic   ,
        hwCppsResetAtmPvcStatisic 
    }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing packet and byte statistic for the pvc sent to CPU."
    ::= { hwIFExtGroups 19 }


    hwCppsPortVlanGroup OBJECT-GROUP
    OBJECTS {
        hwCppsPortVlanPktStatisic   ,
        hwCppsPortVlanByteStatisic   ,
        hwCppsResetPortVlanStatisic
    }
    STATUS      current
    DESCRIPTION
        "A collection of objects providing packet and byte statistic for the port sent to CPU."
    ::= { hwIFExtGroups 20 }
    hwPortIsolationGroup OBJECT-GROUP
    OBJECTS {
        hwPortIsolationGroupPortList , 
        hwPortIsolationGroupRowStatus
    }
    STATUS      current
    DESCRIPTION
        "Port Isolation Group."
    ::= { hwIFExtGroups 21 }    
    
    
    hwVTrunkIfGroup OBJECT-GROUP
            OBJECTS { hwVTrunkIfIndex, hwVTrunkIfID, hwVTrunkIfType, hwVTrunkIfRowStatus }
            STATUS current
            DESCRIPTION 
                "VTrunk attribute Group."
            ::= { hwIFExtGroups 22 }


    hwVTrunkMemGroup OBJECT-GROUP
            OBJECTS { hwVTrunkMemIfIndex, hwVTrunkValidEntry, hwVTrunkOperstatus, hwVTrunkPortActive, hwVTrunkRowStatus }
            STATUS current
            DESCRIPTION 
                "VTrunk member attribute Group."
            ::= { hwIFExtGroups 23 }
 
    hwLogicIfHelpTableGroup OBJECT-GROUP
    OBJECTS{
           hwLogicIfhelpType,
           hwLogicIfChassisNumber,
           hwLogicIfSlotNumber,   
           hwLogicIfCardNumber,
           hwLogicIfMin,
           hwLogicIfMax,
           hwLogicIfTotal
    }
    STATUS current
    DESCRIPTION
    "Logic interface help table Group."
    ::= { hwIFExtGroups 24 }
    
    hwSubInterfaceBackupTrunkGroup OBJECT-GROUP
            OBJECTS { hwBackupStatus, hwRevertiveMode, hwWtrTime, hwFlushVlanId }
            STATUS current
            DESCRIPTION 
                "SubInterfaceBackupTrunk attribute Group."
            ::= { hwIFExtGroups 25 }

        hwVaspPortGroup OBJECT-GROUP
                 OBJECTS { hwVaspPortName, hwVaspPortPeerMac }
                 STATUS current
                 DESCRIPTION 
                     "The vasp port group."
                 ::= { hwIFExtGroups 26 }

   hwLogicIfDynamicHelpTableGroup OBJECT-GROUP
    OBJECTS{
           hwLogicDynamicIfhelpType,
           hwLogicDynamicIfChassisNumber,
           hwLogicDynamicIfSlotNumber,   
           hwLogicDynamicIfCardNumber,
           hwLogicDynamicIfMin,
           hwLogicDynamicIfMax,
           hwLogicDynamicIfTotal
    }
    STATUS current
    DESCRIPTION
    "Logic interface Dynamic help table Group."
    ::= { hwIFExtGroups 27 }
    
   hwIfExtGlobalbGroup OBJECT-GROUP
    OBJECTS{
           hwMainIfName,
           hwCfmOverPhysicalName,
           hwLinkDownReason,   
           hwIFExtPhyNumber,
           hwIpv6IfChangeDownReason,                 
           hwTrunkIfDescr,  
           hwTrunkMemIfDescr,              
           hwTrunkActiveMember,    
           hwIfExtTrapReason,
           hwLacpOldPDUInfo,
           hwLacpNewPDUInfo,
           hwLagMemberDownReason,
           hwLicenseItemName,
           hwLicenseServiceDesc,
           hwTrunkIfStatus,
           hwType,
           hwEthTrunkIfIndex,
           hwTrunkName,
           hwPortIfIndexList,
           hwPortNameList
    }
    STATUS current
    DESCRIPTION
    "Logic interface Dynamic help table Group."
    ::= { hwIFExtGroups 28 }    

  hwTrunkMapGroup OBJECT-GROUP
    OBJECTS{
   	   hwTrunkMapType,
   	   hwTrunkMapID,
   	   hwTrunkMapIndex
    }
    STATUS current
    DESCRIPTION
    "Tunk map group."
    ::= { hwIFExtGroups 29 }

  hwModeChannelBandwidthGroup OBJECT-GROUP
    OBJECTS{
                      hwModeChannelBandwidthIfIndex,              
                      hwModeChannelBandwidthValue,            
                      hwModeChannelSubIfBandwidthSum,           
                      hwModeChannelIfName                      
    }
    STATUS current
    DESCRIPTION
    "The total bandwidth of the channelized sub-interfaces alarm monitor group."
    ::= { hwIFExtGroups 30 }  
  hwTrunkBandwidthGroup OBJECT-GROUP
            OBJECTS{
                    hwTrunkBandwidthTrunkName,
                    hwTrunkBandwidthIfName
                    }
            STATUS current
            DESCRIPTION
            "The total bandwidth of the Trunk interfaces alarm monitor group."
            ::= { hwIFExtGroups 31 }
  hwModeFlexeBandwidthGroup OBJECT-GROUP
    OBJECTS{
                      hwModeFlexeBandwidthIfIndex,              
                      hwModeFlexeBandwidthValue,            
                      hwModeFlexeSubIfBandwidthSum,           
                      hwModeFlexeIfName                      
    }
    STATUS current
    DESCRIPTION
    "The total bandwidth of the sliced sub-interfaces alarm monitor group."
    ::= { hwIFExtGroups 32 }
    hwVirtualEthernetChipGroup  OBJECT-GROUP
            OBJECTS{
                    hwVirtualEthernetChipIfName,
                    hwVirtualEthernetChipIfIndex
                    }
            STATUS current
            DESCRIPTION
            "Virtual-Ethernet attribute Group."
            ::= { hwIFExtGroups 33 } 
    hwFlexeChannelNotSupportGroup OBJECT-GROUP
        OBJECTS{
                        hwFlexeChannelNotSupportIfName                      
        }
        STATUS current
        DESCRIPTION
        "The interface does not support sliced sub-interface alarm monitor group."
        ::= { hwIFExtGroups 34 }
-- =========================================
-- compliance statements
-- =========================================            
        hwIFExtCompliance MODULE-COMPLIANCE
            STATUS current
            DESCRIPTION 
                "The compliance statement for entities implementing
                Huawei extended interface management MIB."
            MODULE -- this module
                MANDATORY-GROUPS { hwTrunkIfGroup, hwIfIpAddressGroup, hwIFExtGroup, hwTrunkMemGroup, hwIFFlowStatGroup,
                hwIfQueryGroup,hwLogicIfAttrGroup, hwVTrunkIfGroup, hwVTrunkMemGroup
                     }
            ::= { hwIFExtCompliances 1 }

        
        hwIFExtTraps OBJECT IDENTIFIER ::= { hwIFExtMib 3 }

        
        hwTrunkWorkingSwitch NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, hwTrunkIfWorkingState }
            STATUS current
            DESCRIPTION 
                "This notification indicates that the trunk working mode is switched."
            ::= { hwIFExtTraps 1 }

        
        hwLacpNegotiateFailed NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "The member of LAG neither can take over PDU nor transmit PDUs. Send this trap when LACP protocol negotiation fails.           
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 2 }

        
        hwLacpTotalLinkLoss NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName }
            STATUS current
            DESCRIPTION 
                "This notification indicates that all the link bandwidths are lost. Some member interfaces in the LAG do not work, and the number of working interfaces is smaller than the hwTrunkIfMinLinkNum value. The Partial Link Loss (PLL) alarm is restricted by the Total Link Loss (TLL) alarm. This object can be bound to two variables: 
1. hwTrunkIfID: ID of a trunk interface. 
2. ifName: It is in the ifXTable of IF-MIB. 
Index: hwTrunkIndex."
            ::= { hwIFExtTraps 3 }

    
        
        hwLacpPartialLinkLoss NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName }
            STATUS current
            DESCRIPTION 
                "Part loss of link bandwidth: it means that some members in the LAG group aren't in the working-state, 
                making the number of ports in working-state smaller than the hwTrunkIfMinLinkNum. The trap of PLL(Partial Link Loss)
                will be restrained by the trap of TLL(Total Link Loss).            
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is in the ifXTable of IF-MIB.
                Index: hwTrunkIndex."
            ::= { hwIFExtTraps 4 }

    hwIfFlowDown NOTIFICATION-TYPE
            OBJECTS { sysUpTime, hwIFExtFlowStatus,ifName }
        STATUS current
        DESCRIPTION 
        "This notification indicates that the interface's flow status changes to flow Down. This object can be bound to two variables: 
1. sysUpTime: indicates the time when the event occurs. 
2. hwIFExtFlowStatus: The interface's flow status changes to Down."
        ::=  {hwIFExtTraps 5}    

    hwIfFlowUp NOTIFICATION-TYPE
            OBJECTS { sysUpTime, hwIFExtFlowStatus,ifName }
        STATUS current
        DESCRIPTION 
        "This notification indicates that the interface's flow status is restored to flow Up. 
This object can be bound to two variables: 
1. sysUpTime: indicates the time when the event occurs. 
2. hwIFExtFlowStatus: The interface's flow status is restored to Up."
        ::=  {hwIFExtTraps 6}    
        
    hwIfNameChange NOTIFICATION-TYPE
        STATUS current
        DESCRIPTION 
        "When a single-chassis device is upgraded to a multi-chassis device, the interface 
        information is changed and then the system sends a trap."
        ::=  {hwIFExtTraps 7}
        
    hwIfNameChangeResume NOTIFICATION-TYPE
        STATUS current
        DESCRIPTION 
        "When a multi-chassis restore a single-chassis device, the interface information 
        is changed and then the system sends a trap."
        ::=  {hwIFExtTraps 8}
    hwExtLinkDown NOTIFICATION-TYPE
           OBJECTS { ifIndex, ifAdminStatus, ifOperStatus, ifDescr, hwIFExtPhyStatus, hwIFExtMemberOf }
           STATUS  deprecated
           DESCRIPTION
           "This object indicates that the link protocol of a trunk member interface goes Down."
           ::=  {hwIFExtTraps 9} 
       
        hwExtLinkUp NOTIFICATION-TYPE
           OBJECTS { ifIndex, ifAdminStatus, ifOperStatus, ifDescr, hwIFExtPhyStatus, hwIFExtMemberOf }
           STATUS  deprecated
           DESCRIPTION
               "This object indicates that the link protocol of a trunk member interface goes Up."
           ::=  {hwIFExtTraps 10}
        
     hwLoopbackBlock NOTIFICATION-TYPE
           OBJECTS { ifDescr, hwNewIfTimeslot }
        STATUS current
        DESCRIPTION 
        "This notification indicates block state of the interface."
        ::=  {hwIFExtTraps 11}

     hwLoopbackResume NOTIFICATION-TYPE
            OBJECTS { hwIfName }
        STATUS current
        DESCRIPTION 
        "This notification indicates normal state of the interface."
        ::=  {hwIFExtTraps 12}
        
        hwLacpNegotiateResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "Negotiation failure alarm is resumed.
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 13 }

        
        hwLacpTotalLinkLossResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName }
            STATUS current
            DESCRIPTION 
                "Link bandwidth lost totally is resumed.
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is in the ifXTable of IF-MIB.
                Index: hwTrunkIndex."
            ::= { hwIFExtTraps 14 }
    
        
        hwLacpPartialLinkLossResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName }
            STATUS current
            DESCRIPTION 
                "Link bandwidth lost partly is resumed.
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is in the ifXTable of IF-MIB.
                Index: hwTrunkIndex."
            ::= { hwIFExtTraps 15 }

        hwTrunkSubIfStateToMaster NOTIFICATION-TYPE
            OBJECTS { hwIfName, hwBackupStatus }
            STATUS current
            DESCRIPTION 
            "This notification indicates BackupState of the Trunk subinterface.
            1.hwIfName: The identification of the Trunk subinterface;
            2.hwBackupStatus: Current working state of Trunk subinterface."
            ::= { hwIFExtTraps 24 }
            
        hwTrunkSubIfStateToSlave NOTIFICATION-TYPE
            OBJECTS { hwIfName, hwBackupStatus }
            STATUS current
            DESCRIPTION 
            "This notification indicates BackupState of the Trunk subinterface.
            1.hwIfName: The identification of the Trunk subinterface;
            2.hwBackupStatus: Current working state of Trunk subinterface."
            ::= { hwIFExtTraps 25 }
        
        hwEntityExtCfmOverSlot NOTIFICATION-TYPE
            OBJECTS { hwCfmOverPhysicalName }
            STATUS current
            DESCRIPTION 
                "The hwEntityExtCfmOverSlot notification will be displayed when the configuration of an interface board is restored. The object hwCfmOverPhysicalName idicates the name of the interface board."
            ::= { hwIFExtTraps 26 }
        
        hwEntityExtCfmOverCard NOTIFICATION-TYPE
            OBJECTS { hwCfmOverPhysicalName }
            STATUS current
            DESCRIPTION 
                "The hwEntityExtCfmOverCard notification will be displayed when the configuration of an interface card is restored. The object hwCfmOverPhysicalName idicates the name of the interface card."
            ::= { hwIFExtTraps 27 }  
            
        hwExtAllMemberDownNotify NOTIFICATION-TYPE
            OBJECTS { ifName }
            STATUS current
            DESCRIPTION 
                "This notification indicates the time when the last member interface link status changes to down.
                The ifName indicates the name of the Trunk interface."
            ::= { hwIFExtTraps 28 }  
            
        hwExtAllMemberDownResume NOTIFICATION-TYPE
            OBJECTS { ifName }
            STATUS current
            DESCRIPTION 
                "This notification indicates the time when the first member interface link status changes to up.
                The ifName indicates the name of the Trunk interface."
            ::= { hwIFExtTraps 29 } 
            
       hwTrunkMemNumberChange NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfDescr, hwTrunkMemIfDescr , hwTrunkActiveMember, hwIfExtTrapReason }
            STATUS current
            DESCRIPTION 
                " The number of  active trunk members changed. "
            ::= { hwIFExtTraps 30 }              
            
        hwIfControlFlapSuppress NOTIFICATION-TYPE
            OBJECTS { ifName, hwIFExtSuppressStatus }
            STATUS current
            DESCRIPTION 
                " This notification indicates the time when the interface's status changes from unsuppress to suppress. "
            ::= { hwIFExtTraps 31 }
            
        hwIfControlFlapResume NOTIFICATION-TYPE
            OBJECTS { ifName, hwIFExtSuppressStatus }
            STATUS current
            DESCRIPTION 
                " This notification indicates the time when the interface's status changes from suppress to unsuppress. "
            ::= { hwIFExtTraps 32 }    
       hwExtInterfaceDelete NOTIFICATION-TYPE
            OBJECTS { ifIndex, ifDescr}
            STATUS current
            DESCRIPTION 
                " This notification indicates the time when the interface was deleted. "
            ::= { hwIFExtTraps 33 }    
			
        hwLacpPartnerExpiredLoss NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "The member of LAG receive expired PDU from partner. Send this trap when LACP protocol negotiation fails. 
				1. hwTrunkIfID: The identification of the Trunk interface; 
				2. ifName: It is the name of the trunk in the ifXTable of IF-MIB. 
				3. ifName: It is the name of the port in the ifXTable of IF-MIB. 
				Indexes: 1. hwTrunkIndex; 
				         2. ifIndex."
            ::= { hwIFExtTraps 34 }
            			
        hwLacpPDUChange NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName, hwLacpOldPDUInfo, hwLacpNewPDUInfo, hwIfExtTrapReason}
            STATUS current
            DESCRIPTION 
                "The LACP member interface's status changed from selected to unselected due to the PDU change. 
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                4. hwLacpOldPDUInfo: It is the old PDU field of the port in the hwIFExtTrapObjects of hwIFExtMib.
                5. hwLacpNewPDUInfo: It is the new PDU field of the port in the hwIFExtTrapObjects of hwIFExtMib.
                6. hwIfExtTrapReason: It is the reason of this trap in the hwIFExtTrapObjects of hwIFExtMib.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 35 }
            
        hwLacpPDUChangeResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "The LACP member interface's status changed from unselected to selected.
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 36 }                    

        hwIfEfmDown NOTIFICATION-TYPE
            OBJECTS {ifName}
            STATUS current
            DESCRIPTION 
                "This notification indicates that the interface status changes to EFM Down because the interface has tracked EFM session and the EFM session goes Down."
            ::=  {hwIFExtTraps 37}    

        hwIfEfmUp NOTIFICATION-TYPE
            OBJECTS {ifName}
            STATUS current
            DESCRIPTION 
                "This notification indicates that the interface status changes to EFM Up because the interface has tracked EFM session and the EFM session goes Up."
            ::=  {hwIFExtTraps 38}  

        hwExtTrunkMemberCountExceedThreshold NOTIFICATION-TYPE
            OBJECTS {hwTrunkMemberCountUpperThreshold}
            STATUS current
            DESCRIPTION 
                "The number of members of the Eth-Trunk in LACP mode exceeded the upper threshold."
            ::=  {hwIFExtTraps 39}  

        hwExtTrunkMemberCountExceedThresholdResume NOTIFICATION-TYPE
            OBJECTS {hwTrunkMemberCountLowerThreshold}
            STATUS current
            DESCRIPTION 
                "The number of members of the Eth-Trunk in LACP mode fell below the lower threshold."
            ::=  {hwIFExtTraps 40} 

        hwExtTrunkWorkingStatusChange NOTIFICATION-TYPE
            OBJECTS {ifName, ifName}
            STATUS current
            DESCRIPTION 
                "This notification indicates that the working status of the backup trunk changed."
            ::=  {hwIFExtTraps 41}  

        hwIfControlFlapSuppressIPv6 NOTIFICATION-TYPE
            OBJECTS { ifName, hwIFExtSuppressStatusIPv6 }
            STATUS current
            DESCRIPTION 
                " This notification indicates the time when the interface's status changes from unsuppress to suppress(IPv6). "
            ::= { hwIFExtTraps 42 }
            
        hwIfControlFlapResumeIPv6 NOTIFICATION-TYPE
            OBJECTS { ifName, hwIFExtSuppressStatusIPv6 }
            STATUS current
            DESCRIPTION 
                " This notification indicates the time when the interface's status changes from suppress to unsuppress(IPv6). "
            ::= { hwIFExtTraps 43 }   
			
        hwLagMemberDown NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName, hwLagMemberDownReason}
            STATUS current
            DESCRIPTION 
                "The LAG member status changes. 
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                4. hwLagMemberDownReason: It is the down reason of the lag member port in the hwIFExtTrapObjects of hwIFExtMib.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 44 }
			
        hwLagMemberDownResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName, hwLagMemberDownReason}
            STATUS current
            DESCRIPTION 
                "The LAG member status resumes. 
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                4. hwLagMemberDownReason: It is the down reason of the lag member port in the hwIFExtTrapObjects of hwIFExtMib.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 45 }

        -- *******.4.1.2011.*********.46
        hwLicenseEffectServiceAlarm NOTIFICATION-TYPE
            OBJECTS { hwLicenseItemName, ifName, hwLicenseServiceDesc }
            STATUS current
            DESCRIPTION 
                "the license was not activated for the physical interface, the service of the corresponding interface or sub-interface is invalid"
            ::= { hwIFExtTraps 46 }
            
        -- *******.4.1.2011.*********.47
        hwLicenseEffectServiceResume NOTIFICATION-TYPE
            OBJECTS { hwLicenseItemName, ifName, hwLicenseServiceDesc }
            STATUS current
            DESCRIPTION 
                "The license has been activated for the physical interface or the service of the corresponding interface and sub-interface has been removed."
            ::= { hwIFExtTraps 47 }

        -- *******.4.1.2011.*********.48
        hwTrunkAllMemUpNotify NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfDescr }
            STATUS current
            DESCRIPTION 
                " All trunk members's status change to up. "
            ::= { hwIFExtTraps 48 }

        hwPhysicalAdminIfDown NOTIFICATION-TYPE
           OBJECTS { ifIndex, ifName, ifOperStatus}
           STATUS  current
           DESCRIPTION
               "The physical manage interface physical status changes to down."
           ::=  {hwIFExtTraps 49}

        hwPhysicalAdminIfUp NOTIFICATION-TYPE
           OBJECTS { ifIndex, ifName, ifOperStatus}
           STATUS  current
           DESCRIPTION
               "The physical manage interface physical status changes to up."
           ::=  {hwIFExtTraps 50}

           hwLacpPartnerMisconnect NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "The peer link of the LACP member interface might be incorrectly connected.
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 51 }
            
        hwLacpPartnerMisconnectResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "The peer link of the LACP member interface was correctly connected.
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 52 } 

        hwTrunkMemberChange NOTIFICATION-TYPE
            OBJECTS {hwType, hwEthTrunkIfIndex, hwTrunkName, hwPortIfIndexList, hwPortNameList}
            STATUS current
            DESCRIPTION 
               "Trap message is generated when eth-trunk member changed."
            ::= { hwIFExtTraps 53 }

        hwTrunkStatusChange NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfDescr, ifAdminStatus , hwTrunkIfStatus }
            STATUS current
            DESCRIPTION 
               "The trunk interface status changes."
            ::= { hwIFExtTraps 54 }
        
        hwIfDampSuppress NOTIFICATION-TYPE
            OBJECTS { ifName, hwIFExtDampStatus }
            STATUS current
            DESCRIPTION 
                " This notification indicates the time when the interface's physical status changes from unsuppress to suppress. "
            ::= { hwIFExtTraps 55 }
            
        hwIfDampResume NOTIFICATION-TYPE
            OBJECTS { ifName, hwIFExtDampStatus }
            STATUS current
            DESCRIPTION 
                " This notification indicates the time when the interface's physical status changes from suppress to unsuppress. "
            ::= { hwIFExtTraps 56 }  

        hwTrunkBwChange NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfDescr, hwIfBandWidth, hwIfBandWidth }
            STATUS current
            DESCRIPTION 
               "The trunk interface bandwidth changes."
            ::= { hwIFExtTraps 57 }

        hwLacpStateDown NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "he LACP status of the member port changes.           
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 58 }

        hwLacpStateDownResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkIfID, ifName, ifName }
            STATUS current
            DESCRIPTION 
                "he LACP status of the member port changes.           
                1. hwTrunkIfID: The identification of the Trunk interface;
                2. ifName: It is the name of the trunk in the ifXTable of IF-MIB.
                3. ifName: It is the name of the port in the ifXTable of IF-MIB.
                Indexes: 1. hwTrunkIndex;
                         2. ifIndex. "
            ::= { hwIFExtTraps 59 }

        hwIfIPConflict NOTIFICATION-TYPE
            OBJECTS { ifIndex, ifName}
            STATUS  current
            DESCRIPTION
                "This notification is generated when an IPv4 address of the interface conflicts with that of another interface."
            ::=  {hwIFExtTraps 60}

        hwIfIPConflictResume NOTIFICATION-TYPE
            OBJECTS { ifIndex, ifName}
            STATUS  current
            DESCRIPTION
                "The IPv4 address conflict is cleared."
            ::=  {hwIFExtTraps 61}

        hwIPv4IfStateAlarm NOTIFICATION-TYPE
            OBJECTS { ifIndex, ifName, ifOperStatus, hwIPv4StateChangeReason }
            STATUS current
            DESCRIPTION 
                "The interface IPv4 status changes."
            ::= { hwIFExtTraps 62 }

		hwIPv4IfStateAlarmResume NOTIFICATION-TYPE
            OBJECTS { ifIndex, ifName, ifOperStatus, hwIPv4StateChangeReason }
            STATUS current
            DESCRIPTION 
                "The interface IPv4 status changes."
            ::= { hwIFExtTraps 63 }

    hwMonitorNotifications OBJECT IDENTIFIER ::= { hwIFExtMib 4 }        

        hwIfMonitorCrcErrorRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorCrcErrorStatistics, hwIfMonitorCrcErrorThreshold, 
                      hwIfMonitorCrcErrorInterval, hwIfMonitorName, hwIfMonitorAllStatistics }
            STATUS current
            DESCRIPTION 
                "This object indicates that an alarm is generated when a CRC error occurs."
            ::= { hwMonitorNotifications 1 }
        
        hwIfMonitorCrcErrorResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorCrcErrorStatistics, hwIfMonitorCrcErrorThreshold,
                      hwIfMonitorCrcErrorInterval, hwIfMonitorName, hwIfMonitorAllStatistics }
            STATUS current
            DESCRIPTION 
                "This object indicates that a clear alarm is generated when the CRC error is removed."
            ::= { hwMonitorNotifications  2 }

        hwIfMonitorSdhErrorRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSdhErrorStatistics, hwIfMonitorSdhErrorThreshold
                      , hwIfMonitorSdhErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that an alarm is generated when an SDH B3 error occurs."
            ::= { hwMonitorNotifications 3 }
        
        hwIfMonitorSdhErrorResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSdhErrorStatistics, hwIfMonitorSdhErrorThreshold
                      , hwIfMonitorSdhErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that a clear alarm is generated when the SDH B3 error is removed."
            ::= { hwMonitorNotifications 4 }
        
        hwIfMonitorInputRateRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorInputRate, hwIfMonitorInputRateThreshold, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                " Input rate alarm notification "
            ::= { hwMonitorNotifications 5 }  

        hwIfMonitorInputRateResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorInputRate, hwIfMonitorInputRateThreshold, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                " Input rate alarm resume "
            ::= { hwMonitorNotifications 6}

         hwIfMonitorOutputRateRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorOutputRate, hwIfMonitorOutputRateThreshold, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                " Output rate alarm notification "
            ::= { hwMonitorNotifications 7}

         hwIfMonitorOutputRateResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorOutputRate, hwIfMonitorOutputRateThreshold, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                " Output rate alarm resume "
            ::= { hwMonitorNotifications 8 }
          
         hwIfMonitorHalfDuplexRising NOTIFICATION-TYPE
            OBJECTS { ifDescr }
            STATUS current
            DESCRIPTION 
                " Interface half duplex alarm norification "
            ::= { hwMonitorNotifications 9 }
            
        hwIfMonitorPauseFrameRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorPauseFrameStatistics, hwIfMonitorPauseFrameThreshold,
                      hwIfMonitorPauseFrameInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates the pause frame alarm."
            ::= { hwMonitorNotifications 10 }

        hwIfMonitorPauseFrameRisingResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorPauseFrameStatistics, hwIfMonitorPauseFrameThreshold,
                      hwIfMonitorPauseFrameInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that the pause frame alarm is cleared."
            ::= { hwMonitorNotifications 11 }  

        hwIfPortControlUp NOTIFICATION-TYPE
            OBJECTS { ifName }
            STATUS current
            DESCRIPTION 
                " Control Up alarm notification "
            ::= { hwMonitorNotifications 12 } 
            
        hwIfPortControlDown NOTIFICATION-TYPE
            OBJECTS { ifName }
            STATUS current
            DESCRIPTION 
                " Control Down alarm notification "
            ::= { hwMonitorNotifications 13 }      

        hwIfMonitorSdhB1ErrorRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSdhB1ErrorStatistics, hwIfMonitorSdhB1ErrorThreshold
                      , hwIfMonitorSdhB1ErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that an alarm is generated when the number of SDH B1 bit errors exceeds the upper threshold."
            ::= { hwMonitorNotifications 14 }
        
        hwIfMonitorSdhB1ErrorResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSdhB1ErrorStatistics, hwIfMonitorSdhB1ErrorThreshold
                      , hwIfMonitorSdhB1ErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that a clear alarm is generated when the number of SDH B1 bit errors falls below the upper threshold."
            ::= { hwMonitorNotifications 15 }    

        hwIfMonitorSdhB2ErrorRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSdhB2ErrorStatistics, hwIfMonitorSdhB2ErrorThreshold
                      , hwIfMonitorSdhB2ErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that an alarm is generated when the number of SDH B2 bit errors exceeds the upper threshold."
            ::= { hwMonitorNotifications 16 }
        
        hwIfMonitorSdhB2ErrorResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSdhB2ErrorStatistics, hwIfMonitorSdhB2ErrorThreshold
                      , hwIfMonitorSdhB2ErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that a clear alarm is generated when the number of SDH B2 bit errors falls below the upper threshold."
            ::= { hwMonitorNotifications 17 } 

        hwIfMonitorSymbolErrorRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSymbolErrorStatistics, hwIfMonitorSymbolErrorThreshold,
                      hwIfMonitorSymbolErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that an alarm is generated when a symbol error occurs on an interface."
            ::= { hwMonitorNotifications 18 }
        
        hwIfMonitorSymbolErrorResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorSymbolErrorStatistics, hwIfMonitorSymbolErrorThreshold,
                      hwIfMonitorSymbolErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that a clear alarm is generated when the symbol error on the interface is removed."
            ::= { hwMonitorNotifications  19 }
    
        -- *******.4.1.2011.*********.40
        hwIfMonitorBadBytesErrorRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorBadBytesErrorStatistics, hwIfMonitorBadBytesErrorThreshold, hwIfMonitorBadBytesErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that an alarm is generated when the number of bytes of error packets exceeds the upper threshold."
            ::= { hwMonitorNotifications 40 }

        -- *******.4.1.2011.*********.41
        hwIfMonitorBadBytesErrorResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorBadBytesErrorStatistics, hwIfMonitorBadBytesErrorThreshold, hwIfMonitorBadBytesErrorInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that a clear alarm is generated when the number of bytes of error packets falls below the upper threshold."
            ::= { hwMonitorNotifications 41 }
       
        -- *******.4.1.2011.*********.42
        hwIfMonitorTxPauseFrameRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorTxPauseFrameStatistics, hwIfMonitorTxPauseFrameHighThreshold, hwIfMonitorTxPauseFrameLowThreshold, hwIfMonitorTxPauseFrameInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that an alarm is generated when pause frames are sent."
            ::= { hwMonitorNotifications 42 }

        -- *******.4.1.2011.*********.43
        hwIfMonitorTxPauseFrameRisingResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorTxPauseFrameStatistics, hwIfMonitorTxPauseFrameHighThreshold, hwIfMonitorTxPauseFrameLowThreshold, hwIfMonitorTxPauseFrameInterval, hwIfMonitorName }
            STATUS current
            DESCRIPTION 
                "This object indicates that a clear alarm is generated when sending pause frames is stopped."
            ::= { hwMonitorNotifications 43 }
        -- *******.4.1.2011.*********.44
        hwIfMonitorPostfecErrorRising NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorName ,hwIfMonitorPostFECErrorStatistics}
            STATUS current
            DESCRIPTION
                "The post fec error crooected alarm appears."
            ::= { hwMonitorNotifications 44 }
			
        -- *******.4.1.2011.*********.45
        hwIfMonitorPostfecErrorRisingResume NOTIFICATION-TYPE
            OBJECTS { hwIfMonitorName ,hwIfMonitorPostFECErrorStatistics}
            STATUS current
            DESCRIPTION
                "The post fec error crooected alarm disappears."
            ::= { hwMonitorNotifications 45 }
		
        -- *******.4.1.2011.*********.46
        hwModeChannelRecvExceedThreshold NOTIFICATION-TYPE
            OBJECTS { hwModeChannelRecvFlowOverIfIndex ,hwModeChannelRecvFlowOverPercentage ,hwModeChannelRecvFlowOverThreshold ,hwModeChannelRecvFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the inflow at the channel mode subinterface exceeds the warning threshold."
            ::= { hwMonitorNotifications 46 }
			
        -- *******.4.1.2011.*********.47
        hwModeChannelRecvExceedThresholdResume NOTIFICATION-TYPE
            OBJECTS { hwModeChannelRecvFlowOverIfIndex ,hwModeChannelRecvFlowOverPercentage ,hwModeChannelRecvFlowOverThreshold ,hwModeChannelRecvFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the inflow at the channel mode subinterface lower than the warning threshold. "
            ::= { hwMonitorNotifications 47 }
			
        -- *******.4.1.2011.*********.48
        hwModeChannelSendExceedThreshold NOTIFICATION-TYPE
            OBJECTS { hwModeChannelSendFlowOverIfIndex ,hwModeChannelSendFlowOverPercentage ,hwModeChannelSendFlowOverThreshold ,hwModeChannelSendFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the inflow at the channel mode subinterface exceeds the warning threshold."
            ::= { hwMonitorNotifications 48 }
			
        -- *******.4.1.2011.*********.49
        hwModeChannelSendExceedThresholdResume NOTIFICATION-TYPE
            OBJECTS { hwModeChannelSendFlowOverIfIndex ,hwModeChannelSendFlowOverPercentage ,hwModeChannelSendFlowOverThreshold ,hwModeChannelSendFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the inflow at the channel mode subinterface lower than the warning threshold. "
            ::= { hwMonitorNotifications 49 }
			
        -- *******.4.1.2011.*********.50
        hwSubChannelBandwidthRising NOTIFICATION-TYPE
            OBJECTS { hwSubChannelBandwidthIfIndex ,hwSubChannelBandwidthValue}
            STATUS current
            DESCRIPTION
                "The alarm appear when the sum of channel mode subinterfaces bandwitch exceeded the bandwitch of father interface."
            ::= { hwMonitorNotifications 50 }
			
        -- *******.4.1.2011.*********.51
        hwSubChannelBandwidthResume NOTIFICATION-TYPE
            OBJECTS { hwSubChannelBandwidthIfIndex ,hwSubChannelBandwidthValue}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the sum of channel mode subinterfaces bandwitch lower than the bandwitch of father interface."
            ::= { hwMonitorNotifications 51 }
			
        -- *******.4.1.2011.*********.52
        hwModeChannelRecvExceedGeneralThreshold NOTIFICATION-TYPE
            OBJECTS { hwModeChannelRecvFlowOverIfIndex ,hwModeChannelRecvFlowOverPercentage ,hwModeChannelRecvFlowOverThreshold ,hwModeChannelRecvFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the inflow at the channel mode subinterface exceeds the general threshold."
            ::= { hwMonitorNotifications 52 }
			
        -- *******.4.1.2011.*********.53
        hwModeChannelRecvExceedGeneralThresholdResume NOTIFICATION-TYPE
            OBJECTS { hwModeChannelRecvFlowOverIfIndex ,hwModeChannelRecvFlowOverPercentage ,hwModeChannelRecvFlowOverThreshold ,hwModeChannelRecvFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the inflow at the channel mode subinterface lower than the general threshold. "
            ::= { hwMonitorNotifications 53 }
			
        -- *******.4.1.2011.*********.54
        hwModeChannelSendExceedGeneralThreshold NOTIFICATION-TYPE
            OBJECTS { hwModeChannelSendFlowOverIfIndex ,hwModeChannelSendFlowOverPercentage ,hwModeChannelSendFlowOverThreshold ,hwModeChannelSendFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the inflow at the channel mode subinterface exceeds the general threshold."
            ::= { hwMonitorNotifications 54 }
			
        -- *******.4.1.2011.*********.55
        hwModeChannelSendExceedGeneralThresholdResume NOTIFICATION-TYPE
            OBJECTS { hwModeChannelSendFlowOverIfIndex ,hwModeChannelSendFlowOverPercentage ,hwModeChannelSendFlowOverThreshold ,hwModeChannelSendFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the inflow at the channel mode subinterface lower than the general threshold. "
            ::= { hwMonitorNotifications 55 }
			
        -- *******.4.1.2011.*********.56
        hwModeChannelRecvExceedSeriousThreshold NOTIFICATION-TYPE
            OBJECTS { hwModeChannelRecvFlowOverIfIndex ,hwModeChannelRecvFlowOverPercentage ,hwModeChannelRecvFlowOverThreshold ,hwModeChannelRecvFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the inflow at the channel mode subinterface exceeds the serious threshold."
            ::= { hwMonitorNotifications 56 }
			
        -- *******.4.1.2011.*********.57
        hwModeChannelRecvExceedSeriousThresholdResume NOTIFICATION-TYPE
            OBJECTS { hwModeChannelRecvFlowOverIfIndex ,hwModeChannelRecvFlowOverPercentage ,hwModeChannelRecvFlowOverThreshold ,hwModeChannelRecvFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the inflow at the channel mode subinterface lower than the serious threshold. "
            ::= { hwMonitorNotifications 57 }
			
        -- *******.4.1.2011.*********.58
        hwModeChannelSendExceedSeriousThreshold NOTIFICATION-TYPE
            OBJECTS { hwModeChannelSendFlowOverIfIndex ,hwModeChannelSendFlowOverPercentage ,hwModeChannelSendFlowOverThreshold ,hwModeChannelSendFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the inflow at the channel mode subinterface exceeds the serious threshold."
            ::= { hwMonitorNotifications 58 }
			
        -- *******.4.1.2011.*********.59
        hwModeChannelSendExceedSeriousThresholdResume NOTIFICATION-TYPE
            OBJECTS { hwModeChannelSendFlowOverIfIndex ,hwModeChannelSendFlowOverPercentage ,hwModeChannelSendFlowOverThreshold ,hwModeChannelSendFlowOverInterfaceName}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the inflow at the channel mode subinterface lower than the serious threshold. "
            ::= { hwMonitorNotifications 59 }

        -- *******.4.1.2011.*********.60
        hwModeChannelBandwidthAlarm NOTIFICATION-TYPE
            OBJECTS { hwModeChannelBandwidthIfIndex ,hwModeChannelBandwidthValue, hwModeChannelSubIfBandwidthSum, hwModeChannelIfName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the total bandwidth of the channelized sub-interfaces on the same physical interface has exceeded the maximum available bandwidth of the physical interface."
            ::= { hwMonitorNotifications 60 }
			
        -- *******.4.1.2011.*********.61
        hwModeChannelBandwidthResume NOTIFICATION-TYPE
            OBJECTS { hwModeChannelBandwidthIfIndex ,hwModeChannelBandwidthValue, hwModeChannelSubIfBandwidthSum, hwModeChannelIfName}
            STATUS current
            DESCRIPTION
                "The alarm disappear when the total bandwidth of the channelized sub-interfaces on the same physical interface less than the maximum available bandwidth of the physical interface."
            ::= { hwMonitorNotifications 61 }
        -- *******.4.1.2011.*********.62
        hwTrunkBandwidthAlarm NOTIFICATION-TYPE
            OBJECTS { hwTrunkBandwidthTrunkName, hwTrunkBandwidthIfName}
            STATUS current
            DESCRIPTION
                "The alarm appear when the bandwidth of the member interfaces of trunk differs greatly."
            ::= {hwMonitorNotifications 62 }

        -- *******.4.1.2011.*********.63
        hwTrunkBandwidthResume NOTIFICATION-TYPE
            OBJECTS { hwTrunkBandwidthTrunkName, hwTrunkBandwidthIfName }
            STATUS current
            DESCRIPTION
                "The alarm disappear when the bandwidth ratio between trunk member interfaces is within the normal range."
            ::= {hwMonitorNotifications 63 }

        -- *******.4.1.2011.*********.64
        hwModeFlexeBandwidthAlarm NOTIFICATION-TYPE
            OBJECTS { hwModeFlexeBandwidthIfIndex ,hwModeFlexeBandwidthValue, hwModeFlexeSubIfBandwidthSum, hwModeFlexeIfName }
            STATUS current
            DESCRIPTION
                "The alarm appear when the total bandwidth of the sliced sub-interfaces on the same physical interface has exceeded the maximum available bandwidth of the physical interface."
            ::= { hwMonitorNotifications 64 }
            
        -- *******.4.1.2011.*********.65
        hwModeFlexeBandwidthResume NOTIFICATION-TYPE
            OBJECTS { hwModeFlexeBandwidthIfIndex ,hwModeFlexeBandwidthValue, hwModeFlexeSubIfBandwidthSum, hwModeFlexeIfName }
            STATUS current
            DESCRIPTION
                "The alarm disappear when the total bandwidth of the sliced sub-interfaces on the same physical interface less than the maximum available bandwidth of the physical interface."
            ::= { hwMonitorNotifications 65 }

        -- *******.4.1.2011.*********.66
        hwVirtualEthernetChipMismatchAlarm NOTIFICATION-TYPE
            OBJECTS { hwVirtualEthernetChipIfName, hwVirtualEthernetChipIfIndex }
            STATUS current
            DESCRIPTION
                "The alarm appear when the chip of the Virtual-Ethernet port is inconsistent with the chip of the internal binding channel port."
            ::= {hwMonitorNotifications 66 }
 
        -- *******.4.1.2011.*********.67
        hwVirtualEthernetChipMismatchResume NOTIFICATION-TYPE
            OBJECTS { hwVirtualEthernetChipIfName, hwVirtualEthernetChipIfIndex }
            STATUS current
            DESCRIPTION
                "The alarm disappear when this Virtual-Ethernet Interface configuration has been deleted."
            ::= {hwMonitorNotifications 67 }
        -- *******.4.1.2011.*********.68
        hwFlexeChannelNotSupportAlarm NOTIFICATION-TYPE
            OBJECTS { hwFlexeChannelNotSupportIfName }
            STATUS current
            DESCRIPTION
                "The alarm appear when the mode flexe enable is configured on the sub-interface, and the main interface is not a GigabitEthernet interface, or the main interface works in non-1GE mode."
            ::= { hwMonitorNotifications 68 }
            
        -- *******.4.1.2011.*********.69
        hwFlexeChannelNotSupportResume NOTIFICATION-TYPE
            OBJECTS { hwFlexeChannelNotSupportIfName }
            STATUS current
            DESCRIPTION
                "The alarm disappear when the mode flexe enable is not configured on the sub-interface, and the main interface is a GigabitEthernet interface, or the main interface works in 1GE mode."
            ::= { hwMonitorNotifications 69 }
        hwIFExtTrapConformance OBJECT IDENTIFIER ::= { hwIFExtMib 5 }

        
        hwIFExtTrapGroups OBJECT IDENTIFIER ::= { hwIFExtTrapConformance 1 }
  
        hwIFExtTrapGroup NOTIFICATION-GROUP
            NOTIFICATIONS { hwTrunkWorkingSwitch, hwLacpNegotiateFailed, hwLacpTotalLinkLoss, hwLacpPartialLinkLoss,hwIfFlowDown,
                        hwIfFlowUp, hwIfNameChange, hwIfNameChangeResume, hwExtLinkDown, hwExtLinkUp, hwLoopbackBlock, hwLoopbackResume,
                        hwLacpNegotiateResume,hwLacpTotalLinkLossResume,hwLacpPartialLinkLossResume,
                        hwExtAllMemberDownNotify, hwExtAllMemberDownResume, hwIfControlFlapSuppress,hwIfControlFlapResume,hwExtInterfaceDelete, hwIfIpAddressChange,
                        hwTrunkSubIfStateToMaster, hwTrunkSubIfStateToSlave, hwEntityExtCfmOverSlot, hwEntityExtCfmOverCard, hwTrunkMemNumberChange, hwLacpPartnerExpiredLoss,
                        hwLacpPDUChange, hwLacpPDUChangeResume, hwIfEfmDown, hwIfEfmUp, hwExtTrunkMemberCountExceedThreshold, hwExtTrunkMemberCountExceedThresholdResume, 
                        hwExtTrunkWorkingStatusChange, hwIfControlFlapSuppressIPv6,hwIfControlFlapResumeIPv6,hwLagMemberDown, hwLagMemberDownResume,hwLicenseEffectServiceAlarm,hwLicenseEffectServiceResume,
                        hwTrunkAllMemUpNotify,hwPhysicalAdminIfDown,hwPhysicalAdminIfUp,hwLacpPartnerMisconnect,hwLacpPartnerMisconnectResume, hwTrunkMemberChange,hwTrunkStatusChange,hwIfDampSuppress,hwIfDampResume,
                        hwLacpStateDown,hwLacpStateDownResume,hwIPv4IfStateAlarm,hwIPv4IfStateAlarmResume}
            STATUS current
            DESCRIPTION 
                "IF Trap Group."
            ::= { hwIFExtTrapGroups 1 }

        hwMonitorTrapGroup NOTIFICATION-GROUP
            NOTIFICATIONS {
                    hwIfMonitorCrcErrorRising,
                    hwIfMonitorCrcErrorResume,
                    hwIfMonitorSdhErrorRising,
                    hwIfMonitorSdhErrorResume,    
                    hwIfMonitorInputRateRising,
                    hwIfMonitorInputRateResume,
                    hwIfMonitorOutputRateRising,
                    hwIfMonitorOutputRateResume, 
                    hwIfMonitorHalfDuplexRising,
                    hwIfMonitorPauseFrameRising,
                    hwIfMonitorPauseFrameRisingResume,
                    hwIfPortControlUp,
                    hwIfPortControlDown,
                    hwIfMonitorSdhB1ErrorRising,
                    hwIfMonitorSdhB1ErrorResume,
                    hwIfMonitorSdhB2ErrorRising,
                    hwIfMonitorSdhB2ErrorResume,
                    hwIfMonitorSymbolErrorRising,
                    hwIfMonitorSymbolErrorResume,
                    hwIfMonitorBadBytesErrorRising,
                    hwIfMonitorBadBytesErrorResume,
                    hwIfMonitorTxPauseFrameRising,
                    hwIfMonitorTxPauseFrameRisingResume,
                    hwIfMonitorPostfecErrorRising,
                    hwIfMonitorPostfecErrorRisingResume,
                    hwModeChannelRecvExceedThreshold,
                    hwModeChannelRecvExceedThresholdResume,
                    hwModeChannelSendExceedThreshold,
                    hwModeChannelSendExceedThresholdResume,
                    hwSubChannelBandwidthRising,
                    hwSubChannelBandwidthResume,
                    hwModeChannelRecvExceedGeneralThreshold,
                    hwModeChannelRecvExceedGeneralThresholdResume,
                    hwModeChannelSendExceedGeneralThreshold,
                    hwModeChannelSendExceedGeneralThresholdResume,
                    hwModeChannelRecvExceedSeriousThreshold,
                    hwModeChannelRecvExceedSeriousThresholdResume,
                    hwModeChannelSendExceedSeriousThreshold,
                    hwModeChannelSendExceedSeriousThresholdResume,
                    hwModeChannelBandwidthAlarm,
                    hwModeChannelBandwidthResume,  
                    hwTrunkBandwidthAlarm,
                    hwTrunkBandwidthResume,
                    hwModeFlexeBandwidthAlarm,
                    hwModeFlexeBandwidthResume,
                    hwVirtualEthernetChipMismatchAlarm,
                    hwVirtualEthernetChipMismatchResume,
                    hwFlexeChannelNotSupportAlarm,
                    hwFlexeChannelNotSupportResume
                 }
            STATUS current
            DESCRIPTION
            "Monitor Trap Group."
            ::= { hwIFExtTrapGroups 2 }
        
          --add trap for IP address changed   
        hwIFIpNotifications OBJECT IDENTIFIER ::= { hwIFExtMib 6 } 
        
        hwIfIpAddressChange NOTIFICATION-TYPE
            OBJECTS { ipAdEntNetMask,ipAdEntNetMask,ifName }
            STATUS current
            DESCRIPTION 
                "IP Trap Group.
                 The trap will be generated when the primary IP address of an interface changes."
            ::= { hwIFIpNotifications 1 }

        hwMruDiscardStatisticAttr OBJECT IDENTIFIER ::= { hwIFExtObjects 22 }

	--
	-- The Mru Discard Statistic Table
	--
	hwMruDiscardStatisticTable OBJECT-TYPE
	  SYNTAX SEQUENCE OF HWMruDiscardStatisticEntry
	  MAX-ACCESS not-accessible
	  STATUS current
	  DESCRIPTION
	    "This table contains the mru discard statistic "
	  ::= { hwMruDiscardStatisticAttr 1 } 


	hwMruDiscardStatisticEntry OBJECT-TYPE
	  SYNTAX HWMruDiscardStatisticEntry
	  MAX-ACCESS not-accessible
	  STATUS current
	  DESCRIPTION
	    "Provides the information of the mru discard statistic ."
	  INDEX { hwMruDiscardStatisticIfIndex}
	  ::= { hwMruDiscardStatisticTable 1 }


	HWMruDiscardStatisticEntry ::=
	  SEQUENCE { 
	    hwMruDiscardStatisticIfIndex
	      InterfaceIndex, 
	    hwMruDiscardStatisticDropPack
	      Counter64,
	    hwMruDiscardStatisticDropByte
	      Counter64 
	}

	hwMruDiscardStatisticIfIndex OBJECT-TYPE
	  SYNTAX InterfaceIndex
	  MAX-ACCESS not-accessible
	  STATUS current
	  DESCRIPTION
	    "This object indicates the interface index."
	  ::= { hwMruDiscardStatisticEntry 1 } 

	hwMruDiscardStatisticDropPack OBJECT-TYPE
	  SYNTAX Counter64
	  MAX-ACCESS read-only
	  STATUS current
	  DESCRIPTION
	    "This object indicates the drop packets."
	  ::= { hwMruDiscardStatisticEntry 2 } 

	hwMruDiscardStatisticDropByte OBJECT-TYPE
	  SYNTAX Counter64
	  MAX-ACCESS read-only
	  STATUS current
	  DESCRIPTION
	    "This object indicates the drop bytes."
	  ::= { hwMruDiscardStatisticEntry 3 } 
   	
            
            
--  ============================================================================

--  ============================================================================


--  ===========================================================================
--  interface rate statistic 
-- ===========================================================================
        hwIfIpStatistics OBJECT IDENTIFIER ::= { hwIFExtObjects 23 }

        hwIfIpStatisticsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwIfIpStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Interface Rate Statistic Table "
            ::= { hwIfIpStatistics 1 }

        
        hwIfIpStatisticsEntry OBJECT-TYPE
            SYNTAX HwIfIpStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of Interface Rate Statistic Table "
            INDEX { hwIfIpStatsIPVersion, hwIfIpStatsIfIndex }
            ::= { hwIfIpStatisticsTable 1 }

        
        HwIfIpStatisticsEntry ::=
            SEQUENCE { 
                hwIfIpStatsIPVersion            InetVersion,
                hwIfIpStatsIfIndex              InterfaceIndex,
                hwIfIpStatsInHostPacketRates    Counter64,
                hwIfIpStatsInHostBitRates       Counter64,
                hwIfIpStatsOutHostPacketRates   Counter64,
                hwIfIpStatsOutHostBitRates      Counter64,
                hwIfIpStatsInFwdPacketRates     Counter64,
                hwIfIpStatsInFwdBitRates        Counter64,
                hwIfIpStatsOutFwdPacketRates    Counter64,
                hwIfIpStatsOutFwdBitRates       Counter64
             }

        hwIfIpStatsIPVersion OBJECT-TYPE
            SYNTAX     InetVersion
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The IP version of this row."
            ::= { hwIfIpStatisticsEntry 1 }

        hwIfIpStatsIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The index value that uniquely identifies the interface to
                which this entry is applicable.  The interface identified by
                a particular value of this index is the same interface as
                identified by the same value of the IF-MIB's ifIndex."
            ::= { hwIfIpStatisticsEntry 2 }

    
        -- *******.4.1.2011.*********.********
        hwIfIpStatsInHostPacketRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The packet rates of the interface which receive host packets."
            ::= { hwIfIpStatisticsEntry 3 }
        
        -- *******.4.1.2011.*********.********
        hwIfIpStatsInHostBitRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The bit rates of the interface which receive host packets."
            ::= { hwIfIpStatisticsEntry 4 }

        -- *******.4.1.2011.*********.********
        hwIfIpStatsOutHostPacketRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The packet rates of the interface which send host packets."
            ::= { hwIfIpStatisticsEntry 5 }
            
        -- *******.4.1.2011.*********.********
        hwIfIpStatsOutHostBitRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The bit rates of the interface which send host packets."
            ::= { hwIfIpStatisticsEntry 6 }
            
        -- *******.4.1.2011.*********.********
        hwIfIpStatsInFwdPacketRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The packet rates of the interface which receive forwarding packets."
            ::= { hwIfIpStatisticsEntry 7 }
            
        -- *******.4.1.2011.*********.********
        hwIfIpStatsInFwdBitRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The bit rates of the interface which receive forwarding packets."
            ::= { hwIfIpStatisticsEntry 8 }
    
        -- *******.4.1.2011.*********.********
        hwIfIpStatsOutFwdPacketRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The packet rates of the interface which send forwarding packets."
            ::= { hwIfIpStatisticsEntry 9 }
    
        -- *******.4.1.2011.*********.*********
        hwIfIpStatsOutFwdBitRates OBJECT-TYPE
            SYNTAX     Counter64
            UNITS "milli-seconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The bit rates of the interface which send forwarding packets."
            ::= { hwIfIpStatisticsEntry 10 }
   

--  ===========================================================================
--  Mode Channel  FlowOver 
-- ===========================================================================   
        -- hwModeChannelFlowOver *******.4.1.2011.*********.24
        hwModeChannelFlowOver OBJECT IDENTIFIER ::= { hwIFExtObjects 24 }
        
        -- hwModeChannelRecvFlowOverTable *******.4.1.2011.*********.24.1
        hwModeChannelRecvFlowOverTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwModeChannelRecvFlowOverEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Mode Channel Subinterface Received FlowOver Table "
            ::= { hwModeChannelFlowOver 1 }

        -- hwModeChannelRecvFlowOverEntry *******.4.1.2011.*********.24.1.1      
        hwModeChannelRecvFlowOverEntry OBJECT-TYPE
            SYNTAX HwModeChannelRecvFlowOverEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of Mode Channel Subinterface Received FlowOver Table "
            INDEX { hwModeChannelRecvFlowOverIfIndex}
            ::= { hwModeChannelRecvFlowOverTable 1 }
     
        HwModeChannelRecvFlowOverEntry ::=
            SEQUENCE { 
                hwModeChannelRecvFlowOverIfIndex           InterfaceIndex,
                hwModeChannelRecvFlowOverPercentage        Integer32,
                hwModeChannelRecvFlowOverThreshold         Integer32,
                hwModeChannelRecvFlowOverInterfaceName     DisplayString
             }

		--hwModeChannelRecvFlowOverIfIndex  *******.4.1.2011.*********.********
        hwModeChannelRecvFlowOverIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The Index Value of Mode Channel Subinterface Received FlowOver Table."
            ::= { hwModeChannelRecvFlowOverEntry 1 }

		-- hwModeChannelRecvFlowOverPercentage *******.4.1.2011.*********.********
        hwModeChannelRecvFlowOverPercentage OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Percentage of Mode Channel Subinterface Received FlowOver Table."
            ::= { hwModeChannelRecvFlowOverEntry 2 }

    
        -- hwModeChannelRecvFlowOverThreshold *******.4.1.2011.*********.********
        hwModeChannelRecvFlowOverThreshold OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Threshold of Mode Channel Subinterface Received FlowOver Table."
            ::= { hwModeChannelRecvFlowOverEntry 3 } 
            
        -- hwModeChannelRecvFlowOverInterfaceName *******.4.1.2011.*********.********
        hwModeChannelRecvFlowOverInterfaceName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Interface Name of Mode Channel Subinterface Received FlowOver Table."
            ::= { hwModeChannelRecvFlowOverEntry 7 }
            
        -- hwModeChannelSendFlowOverTable *******.4.1.2011.*********.24.2
        hwModeChannelSendFlowOverTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwModeChannelSendFlowOverEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Mode Channel Subinterface Send FlowOver Table "
            ::= { hwModeChannelFlowOver 2 }

        -- hwModeChannelSendFlowOverEntry *******.4.1.2011.*********.24.2.1      
        hwModeChannelSendFlowOverEntry OBJECT-TYPE
            SYNTAX HwModeChannelSendFlowOverEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of Mode Channel Subinterface Send FlowOver Table "
            INDEX { hwModeChannelSendFlowOverIfIndex}
            ::= { hwModeChannelSendFlowOverTable 1 }
     
        HwModeChannelSendFlowOverEntry ::=
            SEQUENCE { 
                hwModeChannelSendFlowOverIfIndex            InterfaceIndex,
                hwModeChannelSendFlowOverPercentage              Integer32,
                hwModeChannelSendFlowOverThreshold    Integer32,
                hwModeChannelSendFlowOverInterfaceName     DisplayString
             }

		--hwModeChannelSendFlowOverIfIndex  *******.4.1.2011.*********.********
        hwModeChannelSendFlowOverIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The Index Value of Mode Channel Subinterface Send FlowOver Table."
            ::= { hwModeChannelSendFlowOverEntry 1 }

		-- hwModeChannelSendFlowOverPercentage *******.4.1.2011.*********.********
        hwModeChannelSendFlowOverPercentage OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Percentage of Mode Channel Subinterface Send FlowOver Table."
            ::= { hwModeChannelSendFlowOverEntry 2 }

    
        -- hwModeChannelSendFlowOverThreshold *******.4.1.2011.*********.********
        hwModeChannelSendFlowOverThreshold OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Threshold of Mode Channel Subinterface Send FlowOver Table."
            ::= { hwModeChannelSendFlowOverEntry 3 } 
            
        -- hwModeChannelSendFlowOverInterfaceName *******.4.1.2011.*********.********
        hwModeChannelSendFlowOverInterfaceName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Interface Name of Mode Channel Subinterface Send FlowOver Table."
            ::= { hwModeChannelSendFlowOverEntry 4 }
	
        -- hwSubChannelIfBandwidthTable *******.4.1.2011.*********.24.3
        hwSubChannelIfBandwidthTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwSubChannelIfBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Mode Channel SubInterface Bandwidth Table "
            ::= { hwModeChannelFlowOver 3 }

        -- hwSubChannelIfBandwidthEntry *******.4.1.2011.*********.24.3.1      
        hwSubChannelIfBandwidthEntry OBJECT-TYPE
            SYNTAX HwSubChannelIfBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of Mode Channel SubInterface Bandwidth Table "
            INDEX { hwSubChannelBandwidthIfIndex}
            ::= { hwSubChannelIfBandwidthTable 1 }
     
        HwSubChannelIfBandwidthEntry ::=
            SEQUENCE { 
                hwSubChannelBandwidthIfIndex            InterfaceIndex,
                hwSubChannelBandwidthValue              Integer32
             }

		--hwSubChannelBandwidthIfIndex  *******.4.1.2011.*********.********
        hwSubChannelBandwidthIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The Index Value of Mode Channel SubInterface Bandwidth Table."
            ::= { hwSubChannelIfBandwidthEntry 1 }

		-- hwSubChannelBandwidthValue *******.4.1.2011.*********.********
        hwSubChannelBandwidthValue OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Bandwidth Value of Mode Channel SubInterface Bandwidth Table."
            ::= { hwSubChannelIfBandwidthEntry 2 }
			
-- ===========================================================================
--  Mode Channel Bandwidth 
-- ===========================================================================   
        -- hwModeChannelBandwidth *******.4.1.2011.*********.25
        hwModeChannelBandwidth OBJECT IDENTIFIER ::= { hwIFExtObjects 25 }
        
        -- hwModeChannelBandwidthTable *******.4.1.2011.*********.25.1
        hwModeChannelBandwidthTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwModeChannelBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " The total bandwidth of the channelized sub-interfaces alarm monitor table."
            ::= { hwModeChannelBandwidth 1 }

        -- hwModeChannelBandwidthEntry *******.4.1.2011.*********.25.1.1      
        hwModeChannelBandwidthEntry OBJECT-TYPE
            SYNTAX HwModeChannelBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of the total bandwidth of the channelized sub-interfaces alarm monitor table."
            INDEX { hwModeChannelBandwidthIfIndex}
            ::= { hwModeChannelBandwidthTable 1 }
     
        HwModeChannelBandwidthEntry ::=
            SEQUENCE { 
                hwModeChannelBandwidthIfIndex              InterfaceIndex,
                hwModeChannelBandwidthValue                Integer32,
                hwModeChannelSubIfBandwidthSum         Integer32,
                hwModeChannelIfName                              DisplayString
             }

        -- hwModeChannelBandwidthIfIndex  *******.4.1.2011.*********.********
        hwModeChannelBandwidthIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS accessible-for-notify
            STATUS     current
            DESCRIPTION
                "The Index Value of the total bandwidth of the channelized sub-interfaces alarm monitor table."
            ::= { hwModeChannelBandwidthEntry 1 }

        -- hwModeChannelBandwidthValue *******.4.1.2011.*********.********
        hwModeChannelBandwidthValue OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The maximum available bandwidth of the physical interface."
            ::= { hwModeChannelBandwidthEntry 2 }

    
        -- hwModeChannelSubIfBandwidthSum *******.4.1.2011.*********.********
        hwModeChannelSubIfBandwidthSum OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total bandwidth of the channelized sub-interfaces on the physical interface."
            ::= { hwModeChannelBandwidthEntry 3 } 
            
        -- hwModeChannelIfName *******.4.1.2011.*********.********
        hwModeChannelIfName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Interface name of physical interface."
            ::= { hwModeChannelBandwidthEntry 4 }
-- ===========================================================================
--  Trunk Bandwidth Alarm
-- ===========================================================================
        -- hwTrunkBandwidth
        hwTrunkBandWidth OBJECT IDENTIFIER ::= { hwIFExtObjects 26 }

        -- hwTrunkBandwidthTable
        hwTrunkBandwidthTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwTrunkBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " The total bandwidth of the Trunk sub-interfaces alarm monitor table."
            ::= { hwTrunkBandWidth 1 }

        -- hwTrunkBandwidthEntry
        hwTrunkBandwidthEntry OBJECT-TYPE
            SYNTAX HwTrunkBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of the total bandwidth of the Trunk sub-interfaces alarm monitor table."
            INDEX { hwTrunkBandwidthTrunkName}
            ::= { hwTrunkBandwidthTable 1 }

        HwTrunkBandwidthEntry ::=
            SEQUENCE { 
                hwTrunkBandwidthTrunkName       DisplayString,
                hwTrunkBandwidthIfName          DisplayString
             }

        -- hwTrunkBandwidthTrunkName
        hwTrunkBandwidthTrunkName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Interface name of Trunk interface."
            ::= { hwTrunkBandwidthEntry 1 }

        -- hwTrunkBandwidthIfName
        hwTrunkBandwidthIfName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Interface name of Trunk member interface."
            ::= { hwTrunkBandwidthEntry 2 }

-- ===========================================================================
--  Mode Flexe Bandwidth 
-- ===========================================================================   
        -- hwModeFlexeBandwidth *******.4.1.2011.*********.27
        hwModeFlexeBandwidth OBJECT IDENTIFIER ::= { hwIFExtObjects 27 }
        
        -- hwModeFlexeBandwidthTable *******.4.1.2011.*********.27.1
        hwModeFlexeBandwidthTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwModeFlexeBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " The total bandwidth of the sliced sub-interfaces alarm monitor table."
            ::= { hwModeFlexeBandwidth 1 }

        -- hwModeFlexeBandwidthEntry *******.4.1.2011.*********.27.1.1      
        hwModeFlexeBandwidthEntry OBJECT-TYPE
            SYNTAX HwModeFlexeBandwidthEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of the total bandwidth of the sliced sub-interfaces alarm monitor table."
            INDEX { hwModeFlexeBandwidthIfIndex}
            ::= { hwModeFlexeBandwidthTable 1 }
     
        HwModeFlexeBandwidthEntry ::=
            SEQUENCE { 
                hwModeFlexeBandwidthIfIndex              InterfaceIndex,
                hwModeFlexeBandwidthValue                Integer32,
                hwModeFlexeSubIfBandwidthSum         Integer32,
                hwModeFlexeIfName                              DisplayString
             }

        -- hwModeFlexeBandwidthIfIndex  *******.4.1.2011.*********.********
        hwModeFlexeBandwidthIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS accessible-for-notify
            STATUS     current
            DESCRIPTION
                "The Index Value of the total bandwidth of the sliced sub-interfaces alarm monitor table."
            ::= { hwModeFlexeBandwidthEntry 1 }

        -- hwModeFlexeBandwidthValue *******.4.1.2011.*********.********
        hwModeFlexeBandwidthValue OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The maximum available bandwidth of the main interface."
            ::= { hwModeFlexeBandwidthEntry 2 }

    
        -- hwModeFlexeSubIfBandwidthSum *******.4.1.2011.*********.********
        hwModeFlexeSubIfBandwidthSum OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The total bandwidth of the sliced sub-interfaces on the main interface."
            ::= { hwModeFlexeBandwidthEntry 3 } 
            
        -- hwModeFlexeIfName *******.4.1.2011.*********.********
        hwModeFlexeIfName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Interface name of main interface."
            ::= { hwModeFlexeBandwidthEntry 4 }

-- ===========================================================================
--  Virtual-Ethernet inconsistent chipId alarm
-- ===========================================================================
        -- hwVirtualEthernetChip
        hwVirtualEthernetChip OBJECT IDENTIFIER ::= { hwIFExtObjects 28 }
        
        -- hwVirtualEthernetChipTable
        hwVirtualEthernetChipTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwVirtualEthernetChipEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The inconsistent chipId of this Virtual-Ethernet intefaces alarm monitor table."
            ::= { hwVirtualEthernetChip 1 }
 
        -- hwVirtualEthernetChipEntry
        hwVirtualEthernetChipEntry OBJECT-TYPE
            SYNTAX HwVirtualEthernetChipEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of the inconsistent chipId of this Virtual-Ethernet intefaces alarm monitor table."
            INDEX { hwVirtualEthernetChipIfName}
            ::= { hwVirtualEthernetChipTable 1 }
 
        HwVirtualEthernetChipEntry ::=
            SEQUENCE { 
                hwVirtualEthernetChipIfName               DisplayString,
                hwVirtualEthernetChipIfIndex              InterfaceIndex
             }
 
        -- hwVirtualEthernetChipIfName
        hwVirtualEthernetChipIfName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Interface name of Virtual-Ethernet interface."
            ::= { hwVirtualEthernetChipEntry 1 }
 
        -- hwVirtualEthernetChipIfIndex
        hwVirtualEthernetChipIfIndex OBJECT-TYPE
            SYNTAX     InterfaceIndex
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The Index Value of Virtual-Ethernet interface."
            ::= { hwVirtualEthernetChipEntry 2 }
 -- ===========================================================================
--  Mode Flexe Not Supported 
-- ===========================================================================   
        -- hwModeFlexeNotSupport *******.4.1.2011.*********.29
        hwModeFlexeNotSupport OBJECT IDENTIFIER ::= { hwIFExtObjects 29 }
        
        -- hwModeFlexeNotSupportTable *******.4.1.2011.*********.29.1
        hwModeFlexeNotSupportTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwModeFlexeNotSupportEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " The interface does not support sliced sub-interface alarm monitor table."
            ::= { hwModeFlexeNotSupport 1 }

        -- hwModeFlexeNotSupportEntry *******.4.1.2011.*********.29.1.1      
        hwModeFlexeNotSupportEntry OBJECT-TYPE
            SYNTAX HwModeFlexeNotSupportEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " Entry of the interface does not support sliced sub-interface alarm monitor table."
            INDEX { hwFlexeChannelNotSupportIfName}
            ::= { hwModeFlexeNotSupportTable 1 }
     
        HwModeFlexeNotSupportEntry::=
            SEQUENCE {
                hwFlexeChannelNotSupportIfName                      DisplayString
             }

        -- hwFlexeChannelNotSupportIfName *******.4.1.2011.*********.********
        hwFlexeChannelNotSupportIfName OBJECT-TYPE
            SYNTAX     DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Interface name of the interface."
            ::= { hwModeFlexeNotSupportEntry 1 }
	
    END

--
-- HUAWEI-IF-EXT-MIB.my
--
