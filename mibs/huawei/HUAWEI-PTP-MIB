-- Copyright (C) 2017 by HUAWEI TECHNOLOGIES. All rights reserved.
--
-- HUAWEI-PTP-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Tuesday, Mar 08, 2016 at 18:00:00
-- Version: V2.40
--

	HUAWEI-PTP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			hwDatacomm			
				FROM HUAWEI-MIB			
			InterfaceIndex			
				FROM IF-MIB			
			OBJECT-GRO<PERSON>, MODULE-COMPLIANCE, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			IpAddress, Integer32, Unsigned32, OBJECT-TYPE, MODULE-IDENTITY, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TruthValue, MacAddress, RowStatus, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		hwPtpMIB MODULE-IDENTITY 
		        LAST-UPDATED "201706260000Z"                              -- June 26, 2017 at 14:00 GMT 
			ORGANIZATION 
				"Huawei Technologies Co.,Ltd."
			CONTACT-INFO 
				"Huawei Industrial Base
				Bantian, <PERSON><PERSON><PERSON> 518129
				 People's Republic of China
				 Website: http://www.huawei.com
				 Email: <EMAIL>
				"
                                                      DESCRIPTION 
            	                                            "Modify  hwPtpExtTimePortType."
			 REVISION
            	                                            "201706260000Z" --June 26, 2017 
                                                      DESCRIPTION 
            	                                            "Modify hwPtpExtTimePortStatus, hwPtpExtTimePortType, hwPtpPassiveFiberLengthChange."
			 REVISION
            	                                            "201706160000Z" --June 16, 2017 
                                                      DESCRIPTION 
            	                                            "Add hwPtpPortNonSupport, hwPtpPortNonSupportResume."
			 REVISION
            	                                            "201705230000Z" --May 23, 2017 
                                                      DESCRIPTION 
            	                                            "Modify hwPtpAdaptivePtsfStateChange."
			 REVISION
            	                                            "201704200000Z" --April 20, 2017                                
                                                     DESCRIPTION 
            	                                            "Add hwPtpPortAtrEnable hwPtpaATRLicenseInactive hwPtpaATRLicenseInactiveResume."
			 REVISION
            	                                            "201703280000Z" --Mar 28, 2017 
                                                      
                                                     DESCRIPTION 
            	                                            "Modify hwPtpAdaptivePtsfStateChange."
			 REVISION
            	                                            "201701020000Z" --Jan 02, 2017
                                                           DESCRIPTION 
            	                                            "Modify hwPtpAdaptiveFrequencyProfile."
			 REVISION
            	                                            "201607210000Z" --July 21, 2016
  			  DESCRIPTION 
            	                                            "Modify hwPtpAlarmThresholdOffsetSum."
			 REVISION
            	                                            "201605110000Z" -- May 11, 2016
                                                                   DESCRIPTION 
            	                                            "Modify hwPtpProfile,Add hwPtpMaxStepsRemoved hwPtpBitsGMClockId hwPtpBitsOffsetScaledLogVariance."
			 REVISION
            	                                            "201603080000Z" -- Mar 08, 2016
                                                                  DESCRIPTION 
            	                                            "The HUAWEI-PTP-MIB contains objects to manage PTP."
			 REVISION
            	                                            "201512240000Z" -- Dec 24, 2015
			 DESCRIPTION 
            	                                            "Modify hwPtpPortBmcInfoChange."
			 REVISION
            	                                            "201512010000Z" -- Dec 1, 2015
			 DESCRIPTION 
            	                                            "Modify hwPtpAdaptiveOldTraceSource hwPtpAdaptiveTraceSource."
                         REVISION
            	                                            "201511230000Z" -- Nov 23, 2015
			 DESCRIPTION 
            	                                            "Modify hwPtpAdaptiveClientListChange hwPtpAdaptiveClientIndex."
                         REVISION
            	                                            "201510110000Z" -- Oct 11, 2015
  			 DESCRIPTION 
            	                                            "Add hwPtpLcsResNotEnough hwPtpLcsResNotEnoughResume hwPtpChassisId hwPtpSlotId."	
                         REVISION
            	                                            "201510080000Z" -- Oct 08, 2015
  			 DESCRIPTION
            	                                            "Re-edit the default values of hwPtpPassiveAlarmThreshold."
                         REVISION
            	                                            "201506240000Z" -- Jun 24, 2015
  			 DESCRIPTION 
            	                                            "Modify hwPtpPortStatisticEntry."
                         REVISION
            	                                            "201501300000Z" -- Jan 30, 2015
  			 DESCRIPTION 
            	                                            "Modify hwPtpExtTimePortType."
                                                       REVISION
            	                                                              "201412090000Z" -- Dec 09, 2014
			DESCRIPTION 
            	                                                              "Modify hwPtpAdaptiveServerPriority1 hwPtpAdaptiveServerPriority2 hwPtpAdaptiveServerClockClass and hwPtpTimeOffsetSumP2P."  
                                                       REVISION
            	                                                              "201411250000Z" -- Nov 25, 2014
			DESCRIPTION 
            	                                                              "Modify hwPtpPortType."  
			REVISION
            	                                                              "201411030000Z" -- Nov 03, 2014
			DESCRIPTION 
            	                                                              "Add MIB and Trap of CCSA OAM."            	                                                              
			REVISION
            	                                                              "201410210000Z" -- Oct 21, 2014
			DESCRIPTION 
            	                                                              "Modify hwPtpPortType."
			REVISION
            	                                                              "201409050000Z" -- Sep 5, 2014
			DESCRIPTION 
            	                                                              "Add hwPtpPortSourceStepsRemoved hwPtpPortBmcInfoChange."
			REVISION
            	                                                              "201406090000Z" -- June 9, 2014
			DESCRIPTION 
            	                                                              "Add hwPtpProfile hwPtpPortNotSlave hwPtpLocalClockLocalPriority hwPtpBitsLocalPriority hwPtpPortLocalPriority and modify hwPtpDeviceType for G.8275.1."
			REVISION
            	                                                              "201311280000Z" -- NOV 28, 2013
			DESCRIPTION 
            	                                                              "Add enum nolicense(14) and modify noLPUsupportCentralized from 14 to 15 in hwPtpAdaptiveNegoErrorReason."
			REVISION
            	                                                              "201308130000Z" -- AUG 13, 2013
			DESCRIPTION
            	                                                              "Modify 1588ACR ms to ns."					
			REVISION
            	                                                              "201307170000Z" -- July 17, 2013
			DESCRIPTION
            	                                                              "Add enum noLPUsupportCentralized(14) in hwPtpAdaptiveNegoErrorReason."				
			REVISION
            	                                                              "201307020000Z" -- July 2, 2013
			DESCRIPTION
            	                                                              "Modify for V600R008C00."
			REVISION
            	                                                              "201306240000Z" -- June 24, 2013
			DESCRIPTION
            	                                                              "Some errors have been modified in clock perfermance wave datas."
			REVISION
            	                                                              "201304150000Z" -- April 15, 2013
			DESCRIPTION
            	                                                              "Re-edit the default values of hwPtpPortSyncInterval node."
			REVISION
            	                                                              "201304020000Z" -- April 02, 2013
			DESCRIPTION
            	                                                              "Re-edit the hwPtpUtc node."
			REVISION
            	                                                              "201303200000Z" -- March 20, 2013
			DESCRIPTION
            	                                                              "Some errors have been modified in current version."
			::= { hwDatacomm 187 }
		
	
--
-- Textual conventions
--
	
		EnabledStatus ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"A simple status value for the object."
			SYNTAX INTEGER
				{
				enabled(1),
				disabled(2)
				}
			
		VlanIdOrNone ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"VlanIdOrNone."
			SYNTAX Integer32 (0 | 1..4094)
			
	
--
-- Node definitions
--
	
		-- *******.4.1.2011.**********
		hwPtpGlobalObjects OBJECT IDENTIFIER ::= { hwPtpMIB 1 }
		
		-- *******.4.1.2011.**********.1
		hwPtpEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable PTP function."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 1 }
		
		-- *******.4.1.2011.**********.2
		hwPtpDomain OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's domain attribute,this attribute is used by PTP Device to join
				BMC compute."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 2 }
		
		-- *******.4.1.2011.**********.3
		hwPtpDeviceType OBJECT-TYPE
			SYNTAX INTEGER
				{
				oc(1),
				bc(2),
				p2ptc(3),
				e2etc(4),
				p2ptcoc(5),
				e2etcoc(6),
				tcandbc(7),
                                                                                         tgm(8),
                                                                                         tbc(9),
                                                                                         ttsc(10),
				invalid(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's type attribute."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 3 }
		
		-- *******.4.1.2011.**********.4
		hwPtpSlaveOnly OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's SlaveOnly attribute."
			DEFVAL { false }
			::= { hwPtpGlobalObjects 4 }
		
		-- *******.4.1.2011.**********.5
		hwPtpLocalClockId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Local PTP device's ClockId."
			::= { hwPtpGlobalObjects 5 }
		
		-- *******.4.1.2011.**********.6
		hwPtpLocalClockAccuracy OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's clock accuracy attribute."
			DEFVAL { 49 }
			::= { hwPtpGlobalObjects 6 }
		
		-- *******.4.1.2011.**********.7
		hwPtpLocalClockClass OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's clock class attribute."
			DEFVAL { 187 }
			::= { hwPtpGlobalObjects 7 }
		
		-- *******.4.1.2011.**********.8
		hwPtpLocalClockPriority1 OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's clock priority1 attribute."
			DEFVAL { 128 }
			::= { hwPtpGlobalObjects 8 }
		
		-- *******.4.1.2011.**********.9
		hwPtpLocalClockPriority2 OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's clock priority2 attribute."
			DEFVAL { 128 }
			::= { hwPtpGlobalObjects 9 }
		
		-- *******.4.1.2011.**********.10
		hwPtpLocalClockTimeSource OBJECT-TYPE
			SYNTAX INTEGER
				{
				atomicclock(1),
				gps(2),
				terrestrialradio(3),
				ptp(4),
				ntp(5),
				handset(6),
				other(7),
				internaloscillator(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PTP device's clock time source attribute."
			DEFVAL { internaloscillator }
			::= { hwPtpGlobalObjects 10 }
		
		-- *******.4.1.2011.**********.11
		hwPtpUtc OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates or set current UTC."
			::= { hwPtpGlobalObjects 11 }
		
		-- *******.4.1.2011.**********.12
		hwPtpCurrentUtcOffset OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The offset between TAI and UTC. CurrentUtcOffset=TAI-UTC."
			DEFVAL { 65535 }
			::= { hwPtpGlobalObjects 12 }
		
		-- *******.4.1.2011.**********.13
		hwCurrentUtcOffsetValid OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"CurrentUtcOffsetValid."
			DEFVAL { false }
			::= { hwPtpGlobalObjects 13 }
		
		-- *******.4.1.2011.**********.14
		hwPtpOldMasterClockId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Old grandmaster clock's clockId"
			::= { hwPtpGlobalObjects 14 }
		
		-- *******.4.1.2011.**********.15
		hwPtpCurrentMasterClockReceivePortType OBJECT-TYPE
			SYNTAX INTEGER
				{
				local(1),
				bits(2),
				line(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current grandmaster clock received port type."
			::= { hwPtpGlobalObjects 15 }
		
		-- *******.4.1.2011.**********.16
		hwPtpCurrentMasterClockReceivePort OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current grandmaster clock received from which port."
			::= { hwPtpGlobalObjects 16 }
		
		-- *******.4.1.2011.**********.17
		hwPtpCurrentMasterClockStepRemoved OBJECT-TYPE
			SYNTAX Integer32 (0..127)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Steps between current grandmaster clock device and the local clock device."
			::= { hwPtpGlobalObjects 17 }
		
		-- *******.4.1.2011.**********.18
		hwPtpVersion OBJECT-TYPE
			SYNTAX INTEGER
				{
				ieee1588v2(1),
				invalid(10)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The version of PTP."
			DEFVAL { 1 }
			::= { hwPtpGlobalObjects 18 }
		
		-- *******.4.1.2011.**********.19
		hwPtpTimeScale OBJECT-TYPE
			SYNTAX INTEGER
				{
				ptp(1),
				arb(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time scale of PTP."
			DEFVAL { ptp }
			::= { hwPtpGlobalObjects 19 }
		
		-- *******.4.1.2011.**********.20
		hwPtpFrequencyTraceable OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The frequency trace is enable."
			DEFVAL { false }
			::= { hwPtpGlobalObjects 20 }
		
		-- *******.4.1.2011.**********.21
		hwPtpTimeTraceable OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The time trace is enable."
			DEFVAL { false }
			::= { hwPtpGlobalObjects 21 }
		
		-- *******.4.1.2011.**********.22
		hwPtpTimeSynchronizationStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				unsynchronization(1),
				synchronization(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates TimeSynchronizationStatus is synchronization or not."
			DEFVAL { unsynchronization }
			::= { hwPtpGlobalObjects 22 }
		
		-- *******.4.1.2011.**********.23
		hwPtpGrandMasterClockPriority1 OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PTP GrandMaster clock priority1 attribute."
			::= { hwPtpGlobalObjects 23 }
		
		-- *******.4.1.2011.**********.24
		hwPtpGrandMasterClockPriority2 OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PTP GrandMaster clock priority2 attribute."
			::= { hwPtpGlobalObjects 24 }
		
		-- *******.4.1.2011.**********.25
		hwPtpGrandMasterClockAccuracy OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PTP GrandMaster clock accuracy attribute."
			::= { hwPtpGlobalObjects 25 }
		
		-- *******.4.1.2011.**********.26
		hwPtpGrandMasterClockClass OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PTP GrandMaster clock class attribute."
			::= { hwPtpGlobalObjects 26 }
		
		-- *******.4.1.2011.**********.27
		hwPtpGrandMasterClockTimeSource OBJECT-TYPE
			SYNTAX INTEGER
				{
				atomicclock(1),
				gps(2),
				terrestrialradio(3),
				ptp(4),
				ntp(5),
				handset(6),
				other(7),
				internaloscillator(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"PTP GrandMaster clock timesource attribute."
			::= { hwPtpGlobalObjects 27 }
		
		-- *******.4.1.2011.**********.28
		hwPtpTimeSyncTime OBJECT-TYPE
			SYNTAX INTEGER
				{
				on(1),
				off(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Indicates PTP time is synchronization or not."
			DEFVAL { unsynchronization }
			::= { hwPtpGlobalObjects 28 }
		
		-- *******.4.1.2011.**********.29
		hwPtpFrequencyRecoverMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				packetRecover(1),
				phyRecover(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The recover mode of  PTP."
			DEFVAL { unsynchronization }
			::= { hwPtpGlobalObjects 29 }
		
		-- *******.4.1.2011.**********.30
		hwPtpAclEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable PTP acl function."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 30 }
		
		-- *******.4.1.2011.**********.31
		hwPtpSetPortStateEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable PTP set port state function."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 31 }
		
		-- *******.4.1.2011.**********.32
		hwPtpCurrentMasterClockId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current grandmaster clockId."
			::= { hwPtpGlobalObjects 32 }
		
		-- *******.4.1.2011.**********.33
		hwPtpBits1ppsReceiveDelay OBJECT-TYPE
			SYNTAX Integer32 (0..2000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Bits1ppsReceiveDelay."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 33 }
		
		-- *******.4.1.2011.**********.34
		hwPtpBits1ppsSendDelay OBJECT-TYPE
			SYNTAX Integer32 (0..1600)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Bits1ppsSendDelay."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 34 }
		
		-- *******.4.1.2011.**********.35
		hwPtpBitsDclsReceiveDelay OBJECT-TYPE
			SYNTAX Integer32 (0..2000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BitsDclsReceiveDelay."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 35 }
		
		-- *******.4.1.2011.**********.36
		hwPtpBitsDclsSendDelay OBJECT-TYPE
			SYNTAX Integer32 (0..1600)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BitsDclsSendDelay."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 36 }
		
		-- *******.4.1.2011.**********.37
		hwPtpAdaptiveEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable ptp adaptive function dynamic mode."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 37 }
		
		-- *******.4.1.2011.**********.38
		hwPtpAdaptiveUserMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				primaryserver(1),
				standbyserver(2),
				client(3),
				server(4),
				invalid(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive set user mode attribute."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 38 }
		
		-- *******.4.1.2011.**********.39
		hwPtpAdaptiveUserState OBJECT-TYPE
			SYNTAX INTEGER
				{
				master(1),
				slave(2),
				invalid(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive user state."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 39 }
		
		-- *******.4.1.2011.**********.40
		hwPtpAdaptiveSyncMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				atr(1),
				acr(2),
				acrtwoway(3),
				invalid(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive sync mode."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 40 }
		
		-- *******.4.1.2011.**********.41
		hwPtpAdaptiveDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive UdpEgress Dscp."
			DEFVAL { 63 }
			::= { hwPtpGlobalObjects 41 }
		
		-- *******.4.1.2011.**********.42
		hwPtpAdaptiveDomain OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's domain attribute,this attribute is used by ptp adaptive device to join BMC compute."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 42 }
		
		-- *******.4.1.2011.**********.43
		hwPtpAdaptiveLocalIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's local ip attribute."
			::= { hwPtpGlobalObjects 43 }
		
		-- *******.4.1.2011.**********.44
		hwPtpAdaptiveAnnounceInterval OBJECT-TYPE
			SYNTAX Integer32 (7..14)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Client device's requested send interval of announce packet."
			DEFVAL { 11 }
			::= { hwPtpGlobalObjects 44 }
		
		-- *******.4.1.2011.**********.45
		hwPtpAdaptiveSyncInterval OBJECT-TYPE
			SYNTAX Integer32 (3..14)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Client device's requested send interval of sync packet."
			DEFVAL { 3 }
			::= { hwPtpGlobalObjects 45 }
		
		-- *******.4.1.2011.**********.46
		hwPtpAdaptiveDlyRespInterval OBJECT-TYPE
			SYNTAX Integer32 (3..14)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Client device's requested send interval of delay_req packet."
			DEFVAL { 3 }
			::= { hwPtpGlobalObjects 46 }
		
		-- *******.4.1.2011.**********.47
		hwPtpAdaptiveAnnounceReceiptTimeout OBJECT-TYPE
			SYNTAX Integer32 (4 | 8 | 16 | 32 | 64 | 128 | 256)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's announce receipt timeout attribute."
			DEFVAL { 4 }
			::= { hwPtpGlobalObjects 47 }
		
		-- *******.4.1.2011.**********.48
		hwPtpAdaptiveRemoteServer1Ip OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server1 ip attribute."
			::= { hwPtpGlobalObjects 48 }
		
		-- *******.4.1.2011.**********.49
		hwPtpAdaptiveRemoteServer1KeepAliveEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2),
				invalid(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server1 keepalive attribute."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 49 }
		
		-- *******.4.1.2011.**********.50
		hwPtpAdaptiveRemoteServer1NegoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				init(0),
				success(1),
				error(2),
				none(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server1 negotiate state."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 50 }
		
		-- *******.4.1.2011.**********.51
		hwPtpAdaptiveRemoteServer2Ip OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server2 ip attribute."
			::= { hwPtpGlobalObjects 51 }
		
		-- *******.4.1.2011.**********.52
		hwPtpAdaptiveRemoteServer2KeepAliveEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(1),
				disable(2),
				invalid(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server2 keepalive attribute."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 52 }
		
		-- *******.4.1.2011.**********.53
		hwPtpAdaptiveRemoteServer2NegoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				init(0),
				success(1),
				error(2),
				none(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server2 negotiate state."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 53 }
		
		-- *******.4.1.2011.**********.54
		hwPtpAdaptiveVpnInstance OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's vpn instance."
			::= { hwPtpGlobalObjects 54 }
		
		-- *******.4.1.2011.**********.55
		hwPtpAdaptiveClientIpChangeFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				added(0),
				deleted(1)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Indicates the master's client ip list change state, added or deleted."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 55 }
		
		-- *******.4.1.2011.**********.56
		hwPtpAdaptiveAnnounceDuration OBJECT-TYPE
			SYNTAX Integer32 (60..1000)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's announce packet duration attribute."
			DEFVAL { 300 }
			::= { hwPtpGlobalObjects 56 }
		
		-- *******.4.1.2011.**********.57
		hwPtpAdaptiveSyncDuration OBJECT-TYPE
			SYNTAX Integer32 (60..1000)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's sync packet duration attribute."
			DEFVAL { 300 }
			::= { hwPtpGlobalObjects 57 }
		
		-- *******.4.1.2011.**********.58
		hwPtpAdaptiveDelayRespDuration OBJECT-TYPE
			SYNTAX Integer32 (60..1000)
			UNITS "s"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's delay-resp packet duration attribute."
			DEFVAL { 300 }
			::= { hwPtpGlobalObjects 58 }
		
		-- *******.4.1.2011.**********.59
		hwPtpDfxDlyMeasureEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable delay-measure function."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 59 }
		
		-- *******.4.1.2011.**********.60
		hwPtpDfxPhyPhaseSubCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Physics frequences synchronization offset current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 60 }
		
		-- *******.4.1.2011.**********.61
		hwPtpDfxPhyPhaseSubMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Physics frequences synchronization offset min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 61 }
		
		-- *******.4.1.2011.**********.62
		hwPtpDfxPhyPhaseSubMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Physics frequences synchronization offset max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 62 }
		
		-- *******.4.1.2011.**********.63
		hwPtpDfxPhyPhaseSubMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Physics frequences synchronization offset mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 63 }
		
		-- *******.4.1.2011.**********.64
		hwPtpDfxFreqOffsetCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 frequency synchronization offset current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 64 }
		
		-- *******.4.1.2011.**********.65
		hwPtpDfxFreqOffsetMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 frequency synchronization offset min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 65 }
		
		-- *******.4.1.2011.**********.66
		hwPtpDfxFreqOffsetMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 frequency synchronization offset max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 66 }
		
		-- *******.4.1.2011.**********.67
		hwPtpDfxFreqOffsetMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 frequency synchronization offset mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 67 }
		
		-- *******.4.1.2011.**********.68
		hwPtpDfxACRMaxPdCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization max path delay current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 68 }
		
		-- *******.4.1.2011.**********.69
		hwPtpDfxACRMaxPdMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization max path delay min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 69 }
		
		-- *******.4.1.2011.**********.70
		hwPtpDfxACRMaxPdMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization max path delay max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 70 }
		
		-- *******.4.1.2011.**********.71
		hwPtpDfxACRMaxPdMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization max path delay mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 71 }
		
		-- *******.4.1.2011.**********.72
		hwPtpDfxACRMinPdCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization min path delay current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 72 }
		
		-- *******.4.1.2011.**********.73
		hwPtpDfxACRMinPdMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization min path delay min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 73 }
		
		-- *******.4.1.2011.**********.74
		hwPtpDfxACRMinPdMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization min path delay max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 74 }
		
		-- *******.4.1.2011.**********.75
		hwPtpDfxACRMinPdMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization min path delay mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 75 }
		
		-- *******.4.1.2011.**********.76
		hwPtpDfxTimeSyncOffsetCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization offset current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 76 }
		
		-- *******.4.1.2011.**********.77
		hwPtpDfxTimeSyncOffsetMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization offset min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 77 }
		
		-- *******.4.1.2011.**********.78
		hwPtpDfxTimeSyncOffsetMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization offset max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 78 }
		
		-- *******.4.1.2011.**********.79
		hwPtpDfxTimeSyncOffsetMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization offset mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 79 }
		
		-- *******.4.1.2011.**********.80
		hwPtpDfxTimeSyncMPDlyCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization mean path delay current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 80 }
		
		-- *******.4.1.2011.**********.81
		hwPtpDfxTimeSyncMPDlyMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization mean path delay min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 81 }
		
		-- *******.4.1.2011.**********.82
		hwPtpDfxTimeSyncMPDlyMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization mean path delay max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 82 }
		
		-- *******.4.1.2011.**********.83
		hwPtpDfxTimeSyncMPDlyMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588V2 time synchronization mean path delay mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 83 }
		
		-- *******.4.1.2011.**********.84
		hwPtpDfxFreqSyncMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				phyfreq(1),
				ptpfreq(2),
				ptpacrfreq(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current frequence synchronization mode."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 84 }
		
		-- *******.4.1.2011.**********.85
		hwPtpDfxTimeSyncMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				ptp(1),
				ptpatr(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current time synchronization mode."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 85 }
		
		-- *******.4.1.2011.**********.86
		hwPtpTimeLockStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				unlock(0),
				lock(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Describe current the time lock status."
			DEFVAL { 1 }
			::= { hwPtpGlobalObjects 86 }
		
		-- *******.4.1.2011.**********.87
		hwPtpTimeStampStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				abnormal(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Describe the time stamp changing status."
			DEFVAL { 1 }
			::= { hwPtpGlobalObjects 87 }
		
		-- *******.4.1.2011.**********.88
		hwPtpFreqLockStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				unlock(0),
				lock(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Describe current the frequency lock status."
			DEFVAL { 1 }
			::= { hwPtpGlobalObjects 88 }
		
		-- *******.4.1.2011.**********.89
		hwPtpExtTimePortStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				portDown(1),
				todSecUnChange(2),
				todSecpulseInvalid(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Describe the port status of the external time source."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 89 }
		
		-- *******.4.1.2011.**********.90
		hwPtpExtTimePortType OBJECT-TYPE
			SYNTAX INTEGER
				{
				bits0(0),
				bits1(1),
				bits2(2),
				bits3(3),
				bits1slot4(1025),
				bits1slot5(1281),
				bits1slot6(1537),
				bits1slot7(1793),
				bits1slot9(2305),
				bits1slot10(2561),
				bits1slot11(2817),
				bits1slot12(3073),
				bits1slot17(4353),
				bits1slot18(4609),
				bits1slot19(4865),
				bits1slot20(5121),
				bits1slot21(5377),
				bits1slot22(5633)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Describe the port type  of the external time source."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 90 }
		
		-- *******.4.1.2011.**********.91
		hwPtpPassiveMeasureEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Set Passive Measure Enable."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 91 }
		
		-- *******.4.1.2011.**********.92
		hwPtpPassiveAlarmThreshold OBJECT-TYPE
			SYNTAX Integer32 (55..10000)
			UNITS "ns"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Specify Passive Measure Alarm Threshold."
			DEFVAL { 200 }
			::= { hwPtpGlobalObjects 92 }
		
		-- *******.4.1.2011.**********.93
		hwPtpAcrEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable ptp adaptive function static mode."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 93 }
		
		-- *******.4.1.2011.**********.94
		hwPtpDfxACRNegMaxPdCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative max packet delay current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 94 }
		
		-- *******.4.1.2011.**********.95
		hwPtpDfxACRNegMaxPdMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative max packet delay min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 95 }
		
		-- *******.4.1.2011.**********.96
		hwPtpDfxACRNegMaxPdMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative max packet delay max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 96 }
		
		-- *******.4.1.2011.**********.97
		hwPtpDfxACRNegMaxPdMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative max packet delay mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 97 }
		
		-- *******.4.1.2011.**********.98
		hwPtpDfxACRNegMinPdCur OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative min packet delay current value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 98 }
		
		-- *******.4.1.2011.**********.99
		hwPtpDfxACRNegMinPdMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative min packet delay min value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 99 }
		
		-- *******.4.1.2011.**********.100
		hwPtpDfxACRNegMinPdMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative min packet delay max value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 100 }
		
		-- *******.4.1.2011.**********.101
		hwPtpDfxACRNegMinPdMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative min packet delay mean value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 101 }
		
		-- *******.4.1.2011.**********.102
		hwPtpDfxACRPosMinPdv OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization positive min packet delay variation value."
			DEFVAL { '0'b }
			::= { hwPtpGlobalObjects 102 }
		
		-- *******.4.1.2011.**********.103
		hwPtpDfxACRPosMaxPdv OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization positive max packet delay variation value. The default value is '0'b."
			::= { hwPtpGlobalObjects 103 }
		
		-- *******.4.1.2011.**********.104
		hwPtpDfxACRNegMinPdv OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative min packet delay variation value. The default value is '0'b."
			::= { hwPtpGlobalObjects 104 }
		
		-- *******.4.1.2011.**********.105
		hwPtpDfxACRNegMaxPdv OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"1588 ACR frequency synchronization negative max packet delay variation value. The default value is '0'b."
			::= { hwPtpGlobalObjects 105 }
		
		-- *******.4.1.2011.**********.106
		hwPtpAcrSyncBadStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				abnormal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Describe 1588 ACR sync bad status. The default value is normal(0)."
			::= { hwPtpGlobalObjects 106 }
		
		-- *******.4.1.2011.**********.107
		hwPtpPdvLimitExceedStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				limitExceed(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Describe 1588 ACR PDV limit exceed status. The default value is normal(0)."
			::= { hwPtpGlobalObjects 107 }
		
		-- *******.4.1.2011.**********.108
		hwPtpAdaptiveTraceSource OBJECT-TYPE
			SYNTAX INTEGER
				{
                                local(0),
				server1(1),
				server2(2),
				none(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive client current trace source. The default value is none(99)."
			::= { hwPtpGlobalObjects 108 }
		
		-- *******.4.1.2011.**********.109
		hwPtpAdaptiveRemoteServerId OBJECT-TYPE
			SYNTAX INTEGER
				{
				server1(1),
				server2(2)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server ID."
			::= { hwPtpGlobalObjects 109 }
		
		-- *******.4.1.2011.**********.110
		hwPtpAdaptiveRemoteServerNegoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				init(0),
				success(1),
				error(2),
				none(3)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server negotiate state."
			::= { hwPtpGlobalObjects 110 }
		
		-- *******.4.1.2011.**********.111
		hwPtpAdaptiveNegoErrorReason OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				deleteLink(1),
				announceTimeout(2),
				announceNegoTimeout(3),
				announceNegoDeny(4),
				syncNegoTimeout(5),
				syncNegoDeny(6),
				delayrespNegoTimeout(7),
				delayrespNegoDeny(8),
				ifcannotsupportPtp(9),
				servicemodenotsupportPtp(10),
				routeUnreach(11),
				bmcFailed(12),
				masterSynchronizationFault(13),
				nolicense(14),
				noLPUsupportCentralized(15)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server negotiate error reason."
			::= { hwPtpGlobalObjects 111 }
		
		-- *******.4.1.2011.**********.112
		hwPtpAdaptiveFrequencyProfile OBJECT-TYPE
			SYNTAX INTEGER
				{
				oldmode(0),
				newmode(1),
				timemode(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"1588ACR VISP frequecy profile."
			::= { hwPtpGlobalObjects 112 }
		
		-- *******.4.1.2011.**********.113
		hwPtpAdaptiveAnnReceiptTimeout OBJECT-TYPE
			SYNTAX Integer32 (2..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's announce receipt timeout attribute."
			DEFVAL { 3 }
			::= { hwPtpGlobalObjects 113 }
		
		-- *******.4.1.2011.**********.114
		hwPtpCLKBoardType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				oldclkboard(1),
				newclkboard(2),
				mpuhclkboard(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Clk time board."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 114 }
		
		-- *******.4.1.2011.**********.115
		hwPtpAdaptiveClockclassSsmMapping OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enable clockclass to ssm mapping function."
			DEFVAL { disable }
			::= { hwPtpGlobalObjects 115 }
		
		-- *******.4.1.2011.**********.116
		hwPtpAdaptiveForwardMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				distributed(1),
				centralized(2),
				init(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's forward mode.  Default value is init(99)."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 116 }
		
		-- *******.4.1.2011.**********.117
		hwPtpAdaptiveOldTraceSource OBJECT-TYPE
			SYNTAX INTEGER
				{
                                local(0),
				server1(1),
				server2(2),
				none(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive client history trace source. The default value is none(99)."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 117 }
			
		-- *******.4.1.2011.**********.118
		hwPtpProfile OBJECT-TYPE
			SYNTAX INTEGER
				{
				ieee1588v2(1),
				g8275dot1(2),
				cu106(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The PTP profile. The default value is ieee1588v2(1)."
			DEFVAL { 1 }
			::= { hwPtpGlobalObjects 118 }

		-- *******.4.1.2011.**********.119
		hwPtpLocalClockLocalPriority OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The local-priority attribute of the local clock."
			DEFVAL { 128 }
			::= { hwPtpGlobalObjects 119 }

		-- *******.4.1.2011.**********.120
		hwPtpAlarmThresholdClockClass OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The clock-class threshold of the time-source."
			DEFVAL { 6 }
			::= { hwPtpGlobalObjects 120 }
		
		-- *******.4.1.2011.**********.121
		hwPtpPktType OBJECT-TYPE
			SYNTAX INTEGER
				{
				sync(1),
				delayreq(2),
				pdelayreq(3),
				pdelayresp(4),
				followup(5),
				delayresp(6),
				pdelayrespfollowup(7),
				announce(8),
				signaling(9),
				management(10),
				invalid(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The types of ptp packets."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 121 }
		
		-- *******.4.1.2011.**********.122
		hwPtpStandardTimePort OBJECT-TYPE
			SYNTAX INTEGER
				{
				bits0(1),
				bits1(2),
				bits2(3),
				bits3(4),
				bits4(5),
				bits5(6),
				bits6(7),
				bits7(8),
				bits8(9),
				bits9(10),
				invalid(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The reference port of standard time."
			DEFVAL { 99 }
			::= { hwPtpGlobalObjects 122 }
		
		-- *******.4.1.2011.**********.123
		hwPtpAlarmThresholdStandardTimeOffset OBJECT-TYPE
			SYNTAX Integer32 (200..1000)
			UNITS "ns"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The alarm threshold of standard ptp time offset."
			DEFVAL { 500 }
			::= { hwPtpGlobalObjects 123 }
		
		-- *******.4.1.2011.**********.124
		hwPtpStandardTimeOffsetMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The max value of standard ptp time offset."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 124 }
		
		-- *******.4.1.2011.**********.125
		hwPtpStandardTimeOffsetMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The min value of standard ptp time offset."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 125 }
		
		-- *******.4.1.2011.**********.126
		hwPtpStandardTimeOffsetMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The mean value of standard ptp time offset."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 126 }
		
		-- *******.4.1.2011.**********.127
		hwPtpStandardTimeOffset OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of standard ptp time offset."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 127 }
		
		-- *******.4.1.2011.**********.128
		hwPtpAlarmThresholdOffsetSum OBJECT-TYPE
			SYNTAX Integer32 (0..2000)
			UNITS "ns"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The alarm threshold of ptp time offset sum."
			DEFVAL { 500 }
			::= { hwPtpGlobalObjects 128 }
		
		-- *******.4.1.2011.**********.129
		hwPtpTimeOffsetSumP2P OBJECT-TYPE
			SYNTAX Unsigned32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The peak-to-peak value of ptp time offset sum."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 129 }
		
		-- *******.4.1.2011.**********.130
		hwPtpTimeOffsetSumMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The mean value of ptp time offset sum."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 130 }
		
		-- *******.4.1.2011.**********.131
		hwPtpTimeOffsetSumEnd OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The end value of ptp time offset sum."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 131 }
		
		-- *******.4.1.2011.**********.132
		hwPtpTimeOffsetSum OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of ptp time offset sum."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 132 }
		
		-- *******.4.1.2011.**********.133
		hwPtpT2SubT1Value OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of T2-T1."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 133 }
		
		-- *******.4.1.2011.**********.134
		hwPtpT4SubT3Value OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of T4-T3."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 134 }
		
		-- *******.4.1.2011.**********.135
		hwPtpT2SubT1ValueMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The max value of T2-T1."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 135 }
		
		-- *******.4.1.2011.**********.136
		hwPtpT2SubT1ValueMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The min value of T2-T1."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 136 }
		
		-- *******.4.1.2011.**********.137
		hwPtpT2SubT1ValueMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The mean value of T2-T1."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 137 }
		
		-- *******.4.1.2011.**********.138
		hwPtpT4SubT3ValueMax OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The max value of T4-T3."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 138 }
		
		-- *******.4.1.2011.**********.139
		hwPtpT4SubT3ValueMin OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The min value of T4-T3."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 139 }
		
		-- *******.4.1.2011.**********.140
		hwPtpT4SubT3ValueMean OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The mean value of T4-T3."
			DEFVAL { 0 }
			::= { hwPtpGlobalObjects 140 }

		-- *******.4.1.2011.**********.141
		hwPtpChassisId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The chassis ID."
			::= { hwPtpGlobalObjects 141 }

		-- *******.4.1.2011.**********.142
		hwPtpSlotId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The slot ID."
			::= { hwPtpGlobalObjects 142 }

		-- *******.4.1.2011.**********.143
		hwPtpMaxStepsRemoved OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Max step removed."
			DEFVAL { 255 }
			::= { hwPtpGlobalObjects 143 }
						
		-- *******.4.1.2011.**********
		hwPtpPortObjects OBJECT IDENTIFIER ::= { hwPtpMIB 2 }
		
		-- *******.4.1.2011.**********.1
		hwPtpPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PtpPortTable."
			::= { hwPtpPortObjects 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwPtpPortEntry OBJECT-TYPE
			SYNTAX HwPtpPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in hwPtpPortTable."
			INDEX { hwPtpPortIfIndex }
			::= { hwPtpPortTable 1 }
		
		HwPtpPortEntry ::=
			SEQUENCE { 
				hwPtpPortIfIndex
					InterfaceIndex,
				hwPtpPortEnable
					EnabledStatus,
				hwPtpPortDelayMechanism
					INTEGER,
				hwPtpPortType
					INTEGER,
				hwPtpPortDomain
					Integer32,
				hwPtpPortTcOcStaticClockId
					OCTET STRING,
				hwPtpPortTcOcStaticClockPortNum
					Integer32,
				hwPtpPortTcOcStaticClockSlot
					Integer32,
				hwPtpPortTcOcStaticClockCard
					Integer32,
				hwPtpPortTcOcStaticClockPort
					Integer32,
				hwPtpPortAnnounceInterval
					Integer32,
				hwPtpPortAnnounceReceiptTimeout
					Integer32,
				hwPtpPortSyncInterval
					Integer32,
				hwPtpPortMinDelayReqInterval
					Integer32,
				hwPtpPortMinPdelayReqInterval
					Integer32,
				hwPtpPortAsymmetryNegativeCorrection
					Unsigned32,
				hwPtpPortAsymmetryPositiveCorrection
					Unsigned32,
				hwPtpPortMacEgressDestinationMac
					MacAddress,
				hwPtpPortMacEgressVlanId
					VlanIdOrNone,
				hwPtpPortMacEgressPacketPriority
					Integer32,
				hwPtpPortUdpEgressSourceIp
					IpAddress,
				hwPtpPortUdpEgressDestinationIp
					IpAddress,
				hwPtpPortUdpEgressDestinationMac
					MacAddress,
				hwPtpPortUdpEgressDscp
					Integer32,
				hwPtpPortUdpEgressVlanId
					VlanIdOrNone,
				hwPtpPortUdpEgressPacketPriority
					Integer32,
				hwPtpPortAnnounceDrop
					EnabledStatus,
				hwPtpOldPortState
					INTEGER,
				hwPtpPortSourcePortClockId
					OCTET STRING,
				hwPtpPortSourcePortNum
					Integer32,
				hwPtpPortSourcePortSlot
					Integer32,
				hwPtpPortSourcePortCard
					Integer32,
				hwPtpPortSourcePort
					Integer32,
				hwPtpPortNumber
					Integer32,
				hwPtpPortPortVlan
					Integer32,
				hwPtpPortCfgLinkStatus
					INTEGER,
				hwPtpPortCfgExtInterfaceMode
					INTEGER,
				hwPtpPortCfgMsgFormat
					INTEGER,
				hwPtpPortAnnounceReceiptTimeout2
					Integer32,
				hwPtpPortClockStep
					INTEGER,
				hwPtpPortState
					INTEGER,
				hwPtpPeerAnnounceSendInterval
					Integer32,
				hwPtpPortName
					OCTET STRING,
				hwPtpPortCfgState
					INTEGER,
				hwPtpPortRingFiberLengthChangeValue
					Integer32,
				hwPtpPortRingFiberLengthChangeValueFlag
					INTEGER,
				hwPtpPortOldSourcePortNum
					Integer32,
				hwPtpOldPortName
					OCTET STRING,
				hwPtpPortAnnReceiptTimeout
					Integer32,
                                                                                         hwPtpPortNotSlave
                                                                                                              EnabledStatus,
                                                                                         hwPtpPortLocalPriority
                                                                                                              Integer32 ,
				hwPtpPortSourceStepsRemoved
					Integer32,
                                                                                         hwPtpPortAtrEnable
					EnabledStatus,
				hwPtpPortRowStatus
					RowStatus
			 }

		-- *******.4.1.2011.**********.1.1.1
		hwPtpPortIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Port ifIndex."
			::= { hwPtpPortEntry 1 }
		
		-- *******.4.1.2011.**********.1.1.2
		hwPtpPortEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The port enable PTP function."
			DEFVAL { disable }
			::= { hwPtpPortEntry 2 }
		
		-- *******.4.1.2011.**********.1.1.3
		hwPtpPortDelayMechanism OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(1),
				delay(2),
				pdelay(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The port delay mechanism."
			DEFVAL { 1 }
			::= { hwPtpPortEntry 3 }
		
		-- *******.4.1.2011.**********.1.1.4
		hwPtpPortType OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(1),
				tc(2),
				bc(3),
				tcoc(4),
				oc(5),
                                                                                     tgm(6),
                                                                                     tbc(7),
                                                                                     ttsc(8)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"PTP port type. If the device type is set to tcandbc, the PTP port type can be configured and queried. If the device type is set to another value, the PTP port type can only be queried."
			DEFVAL { none }
			::= { hwPtpPortEntry 4 }
		
		-- *******.4.1.2011.**********.1.1.5
		hwPtpPortDomain OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This port's domain,only used in the PTP device type is tcandbc."
			DEFVAL { 0 }
			::= { hwPtpPortEntry 5 }
		
		-- *******.4.1.2011.**********.1.1.6
		hwPtpPortTcOcStaticClockId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"TcOc device sourceport clockId."
			::= { hwPtpPortEntry 6 }
		
		-- *******.4.1.2011.**********.1.1.7
		hwPtpPortTcOcStaticClockPortNum OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"TcOc device sourceport number."
			::= { hwPtpPortEntry 7 }
		
		-- *******.4.1.2011.**********.1.1.8
		hwPtpPortTcOcStaticClockSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"TcOc device portnumber slot."
			::= { hwPtpPortEntry 8 }
		
		-- *******.4.1.2011.**********.1.1.9
		hwPtpPortTcOcStaticClockCard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"TcOc device portnumber card."
			::= { hwPtpPortEntry 9 }
		
		-- *******.4.1.2011.**********.1.1.10
		hwPtpPortTcOcStaticClockPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"TcOc device portnumber port."
			::= { hwPtpPortEntry 10 }
		
		-- *******.4.1.2011.**********.1.1.11
		hwPtpPortAnnounceInterval OBJECT-TYPE
			SYNTAX Integer32 (0..20)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Interval of sending announce message."
			DEFVAL { 7 }
			::= { hwPtpPortEntry 11 }
		
		-- *******.4.1.2011.**********.1.1.12
		hwPtpPortAnnounceReceiptTimeout OBJECT-TYPE
			SYNTAX Integer32 (4 | 8 | 16 | 32 | 64 | 128 | 256)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Timeout of receiving announce message."
			DEFVAL { 4 }
			::= { hwPtpPortEntry 12 }
		
		-- *******.4.1.2011.**********.1.1.13
		hwPtpPortSyncInterval OBJECT-TYPE
			SYNTAX Integer32 (0..20)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Interval of sending sync message."
			DEFVAL { 3 }
			::= { hwPtpPortEntry 13 }
		
		-- *******.4.1.2011.**********.1.1.14
		hwPtpPortMinDelayReqInterval OBJECT-TYPE
			SYNTAX Integer32 (0..20)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Interval of sending DelayReq message."
			DEFVAL { 7 }
			::= { hwPtpPortEntry 14 }
		
		-- *******.4.1.2011.**********.1.1.15
		hwPtpPortMinPdelayReqInterval OBJECT-TYPE
			SYNTAX Integer32 (0..20)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Interval of sending PdelayReq message."
			DEFVAL { 7 }
			::= { hwPtpPortEntry 15 }
		
		-- *******.4.1.2011.**********.1.1.16
		hwPtpPortAsymmetryNegativeCorrection OBJECT-TYPE
			SYNTAX Unsigned32 (0..2000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Negtive asymmetry-correction value"
			DEFVAL { 0 }
			::= { hwPtpPortEntry 16 }
		
		-- *******.4.1.2011.**********.1.1.17
		hwPtpPortAsymmetryPositiveCorrection OBJECT-TYPE
			SYNTAX Unsigned32 (0..2000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Positive asymmetry-correction value"
			DEFVAL { 0 }
			::= { hwPtpPortEntry 17 }
		
		-- *******.4.1.2011.**********.1.1.18
		hwPtpPortMacEgressDestinationMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"MacEgress destination Mac."
			DEFVAL { ''h }
			::= { hwPtpPortEntry 18 }
		
		-- *******.4.1.2011.**********.1.1.19
		hwPtpPortMacEgressVlanId OBJECT-TYPE
			SYNTAX VlanIdOrNone
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"MacEgress VlanId."
			DEFVAL { 0 }
			::= { hwPtpPortEntry 19 }
		
		-- *******.4.1.2011.**********.1.1.20
		hwPtpPortMacEgressPacketPriority OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"MacEgress Packet Priority."
			DEFVAL { 7 }
			::= { hwPtpPortEntry 20 }
		
		-- *******.4.1.2011.**********.1.1.21
		hwPtpPortUdpEgressSourceIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"UdpEgress SourceIp."
			::= { hwPtpPortEntry 21 }
		
		-- *******.4.1.2011.**********.1.1.22
		hwPtpPortUdpEgressDestinationIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"UdpEgress DestinationIp."
			::= { hwPtpPortEntry 22 }
		
		-- *******.4.1.2011.**********.1.1.23
		hwPtpPortUdpEgressDestinationMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"UdpEgress Destination Mac."
			DEFVAL { ''h }
			::= { hwPtpPortEntry 23 }
		
		-- *******.4.1.2011.**********.1.1.24
		hwPtpPortUdpEgressDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"UdpEgress Dscp."
			DEFVAL { 0 }
			::= { hwPtpPortEntry 24 }
		
		-- *******.4.1.2011.**********.1.1.25
		hwPtpPortUdpEgressVlanId OBJECT-TYPE
			SYNTAX VlanIdOrNone
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"UdpEgress VlanId."
			DEFVAL { 0 }
			::= { hwPtpPortEntry 25 }
		
		-- *******.4.1.2011.**********.1.1.26
		hwPtpPortUdpEgressPacketPriority OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"UdpEgress Packet Priority."
			DEFVAL { 7 }
			::= { hwPtpPortEntry 26 }
		
		-- *******.4.1.2011.**********.1.1.27
		hwPtpPortAnnounceDrop OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Enable AnnounceDrop function."
			DEFVAL { disable }
			::= { hwPtpPortEntry 27 }
		
		-- *******.4.1.2011.**********.1.1.28
		hwPtpOldPortState OBJECT-TYPE
			SYNTAX INTEGER
				{
				master(1),
				slave(2),
				passive(3),
				listening(4),
				faulty(5),
				initializing(6),
				premaster(7),
				disabled(8),
				uncalibrated(9)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Old port state."
			::= { hwPtpPortEntry 28 }
		
		-- *******.4.1.2011.**********.1.1.29
		hwPtpPortSourcePortClockId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates sourceportclockid of the port."
			::= { hwPtpPortEntry 29 }
		
		-- *******.4.1.2011.**********.1.1.30
		hwPtpPortSourcePortNum OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates sourceport number of the port."
			::= { hwPtpPortEntry 30 }
		
		-- *******.4.1.2011.**********.1.1.31
		hwPtpPortSourcePortSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates sourceport slot of the port."
			::= { hwPtpPortEntry 31 }
		
		-- *******.4.1.2011.**********.1.1.32
		hwPtpPortSourcePortCard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates sourceport card of the port."
			::= { hwPtpPortEntry 32 }
		
		-- *******.4.1.2011.**********.1.1.33
		hwPtpPortSourcePort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates sourceport port of the port."
			::= { hwPtpPortEntry 33 }
		
		-- *******.4.1.2011.**********.1.1.34
		hwPtpPortNumber OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port number."
			::= { hwPtpPortEntry 34 }
		
		-- *******.4.1.2011.**********.1.1.35
		hwPtpPortPortVlan OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The VLAN of packet sent from port."
			::= { hwPtpPortEntry 35 }
		
		-- *******.4.1.2011.**********.1.1.36
		hwPtpPortCfgLinkStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				down(0),
				up(1)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The link status of port."
			::= { hwPtpPortEntry 36 }
		
		-- *******.4.1.2011.**********.1.1.37
		hwPtpPortCfgExtInterfaceMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				extclock(1),
				exttime(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The mode of interface."
			DEFVAL { extclock }
			::= { hwPtpPortEntry 37 }
		
		-- *******.4.1.2011.**********.1.1.38
		hwPtpPortCfgMsgFormat OBJECT-TYPE
			SYNTAX INTEGER
				{
				ptpeth(1),
				ptpip(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The format of massage."
			DEFVAL { ptpeth }
			::= { hwPtpPortEntry 38 }
		
		-- *******.4.1.2011.**********.1.1.39
		hwPtpPortAnnounceReceiptTimeout2 OBJECT-TYPE
			SYNTAX Integer32 (0..20)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Timeout of receiving announce message."
			::= { hwPtpPortEntry 39 }
		
		-- *******.4.1.2011.**********.1.1.40
		hwPtpPortClockStep OBJECT-TYPE
			SYNTAX INTEGER
				{
				onestep(1),
				twostep(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Port clock step."
			DEFVAL { 1 }
			::= { hwPtpPortEntry 40 }
		
		-- *******.4.1.2011.**********.1.1.41
		hwPtpPortState OBJECT-TYPE
			SYNTAX INTEGER
				{
				master(1),
				slave(2),
				passive(3),
				listening(4),
				faulty(5),
				initializing(6),
				premaster(7),
				disabled(8),
				uncalibrated(9)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current port state."
			::= { hwPtpPortEntry 41 }
		
		-- *******.4.1.2011.**********.1.1.42
		hwPtpPeerAnnounceSendInterval OBJECT-TYPE
			SYNTAX Integer32 (0..20)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"peer announce send interval."
			::= { hwPtpPortEntry 42 }
		
		-- *******.4.1.2011.**********.1.1.43
		hwPtpPortName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port name."
			::= { hwPtpPortEntry 43 }
		
		-- *******.4.1.2011.**********.1.1.44
		hwPtpPortCfgState OBJECT-TYPE
			SYNTAX INTEGER
				{
				master(1),
				slave(2),
				passive(3),
				listening(4),
				faulty(5),
				initializing(6),
				premaster(7),
				disabled(8),
				uncalibrated(9)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Cfg port state value."
			DEFVAL { 6 }
			::= { hwPtpPortEntry 44 }
		
		-- *******.4.1.2011.**********.1.1.45
		hwPtpPortRingFiberLengthChangeValue OBJECT-TYPE
			SYNTAX Integer32
			UNITS "ns"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ring fiber length change value. The default value is '0'b."
			::= { hwPtpPortEntry 45 }
		
		-- *******.4.1.2011.**********.1.1.46
		hwPtpPortRingFiberLengthChangeValueFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				negative(0),
				positive(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ring fiber length change value flag."
			::= { hwPtpPortEntry 46 }
		
		-- *******.4.1.2011.**********.1.1.47
		hwPtpPortOldSourcePortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwPtpPortEntry 47 }
		
		-- *******.4.1.2011.**********.1.1.48
		hwPtpOldPortName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwPtpPortEntry 48 }
		
		-- *******.4.1.2011.**********.1.1.49
		hwPtpPortAnnReceiptTimeout OBJECT-TYPE
			SYNTAX Integer32 (2..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Timeout of receiving announce message."
			DEFVAL { 3 }
			::= { hwPtpPortEntry 49 }

		-- *******.4.1.2011.**********.1.1.50
		hwPtpPortNotSlave OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The notslave attribute of the port.The default value is enabled(1)."
			DEFVAL { enabled }
			::= { hwPtpPortEntry 50 }

		-- *******.4.1.2011.**********.1.1.51
		hwPtpPortLocalPriority OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The local-priority attribute of the port."
			DEFVAL { 128 }
			::= { hwPtpPortEntry 51 }

		-- *******.4.1.2011.**********.1.1.52
		hwPtpPortSourceStepsRemoved OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of stepsRemoved shall be the value of currentDS.stepsRemoved."
			DEFVAL { 0 }
			::= { hwPtpPortEntry 52 }
                                   
                                   -- *******.4.1.2011.**********.1.1.53
		hwPtpPortAtrEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The port enable ATR function."
			::= { hwPtpPortEntry 53 }
		
		-- *******.4.1.2011.**********.1.1.100
		hwPtpPortRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status."
			::= { hwPtpPortEntry 100 }
		
		-- *******.4.1.2011.**********.2
		hwPtpPortStatisticTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpPortStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PortStatisticTable."
			::= { hwPtpPortObjects 2 }
		
		-- *******.4.1.2011.**********.2.1
		hwPtpPortStatisticEntry OBJECT-TYPE
			SYNTAX HwPtpPortStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PortStatisticEntry."
			INDEX { hwPtpPortStatisticIfIndex }
			::= { hwPtpPortStatisticTable 1 }
		
		HwPtpPortStatisticEntry ::=
			SEQUENCE { 
				hwPtpPortStatisticIfIndex
					InterfaceIndex,
				hwPtpPortRecvTransparent
					Unsigned32,
				hwPtpPortRecvCorrectend
					Unsigned32,
				hwPtpPortRecvAnnounce
					Unsigned32,
				hwPtpPortRecvSync
					Unsigned32,
				hwPtpPortRecvReq
					Unsigned32,
				hwPtpPortRecvRespCnt
					Unsigned32,
				hwPtpPortRecvFollowup
					Unsigned32,
				hwPtpPortRecvPdelayrespfollowup
					Unsigned32,
				hwPtpPortSendTotal1588
					Unsigned32,
				hwPtpPortSendAnnounce
					Unsigned32,
				hwPtpPortSendSync
					Unsigned32,
				hwPtpPortSendReq
					Unsigned32,
				hwPtpPortSendResp
					Unsigned32,
				hwPtpPortSendFollowup
					Unsigned32,
				hwPtpPortSendPdelayrespfollowup
					Unsigned32,
				hwPtpPortDiscardTotal1588
					Unsigned32,
				hwPtpPortDiscardAnnounce
					Unsigned32,
				hwPtpPortDiscardSync
					Unsigned32,
				hwPtpPortDiscardDelayreq
					Unsigned32,
				hwPtpPortDiscardPdelayreq
					Unsigned32,
				hwPtpPortDiscardResp
					Unsigned32,
				hwPtpPortDiscardPdelayresp
					Unsigned32,
				hwPtpPortDiscardFollowup
					Unsigned32,
				hwPtpPortDiscardPdelayrespfollowup
					Unsigned32,
				hwPtpPortStaticPktReset
					INTEGER,
				hwPtpPortPassiveTimeOffsetMax
					Integer32,
				hwPtpPortPassiveTimeOffsetMin
					Integer32,	
				hwPtpPortPassiveTimeOffsetMean
					Integer32,
				hwPtpPortPassiveTimeOffset
				        Integer32	
			 }

		-- *******.4.1.2011.**********.2.1.1
		hwPtpPortStatisticIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The PortStatisticIfIndex."
			::= { hwPtpPortStatisticEntry 1 }
		
		-- *******.4.1.2011.**********.2.1.2
		hwPtpPortRecvTransparent OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the PTP packets Transparent in this port."
			::= { hwPtpPortStatisticEntry 2 }
		
		-- *******.4.1.2011.**********.2.1.3
		hwPtpPortRecvCorrectend OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the PTP packets Correct_end in this port."
			::= { hwPtpPortStatisticEntry 3 }
		
		-- *******.4.1.2011.**********.2.1.4
		hwPtpPortRecvAnnounce OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the PTP packets Announce in this port."
			::= { hwPtpPortStatisticEntry 4 }
		
		-- *******.4.1.2011.**********.2.1.5
		hwPtpPortRecvSync OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the PTP packets Sync in this port."
			::= { hwPtpPortStatisticEntry 5 }
		
		-- *******.4.1.2011.**********.2.1.6
		hwPtpPortRecvReq OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the packets Req in this port."
			::= { hwPtpPortStatisticEntry 6 }
		
		-- *******.4.1.2011.**********.2.1.7
		hwPtpPortRecvRespCnt OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the Resp packets in this port."
			::= { hwPtpPortStatisticEntry 7 }
		
		-- *******.4.1.2011.**********.2.1.8
		hwPtpPortRecvFollowup OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the PTP Followup packets in this port."
			::= { hwPtpPortStatisticEntry 8 }
		
		-- *******.4.1.2011.**********.2.1.9
		hwPtpPortRecvPdelayrespfollowup OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of Pdelay_resp_followup in this port ."
			::= { hwPtpPortStatisticEntry 9 }
		
		-- *******.4.1.2011.**********.2.1.10
		hwPtpPortSendTotal1588 OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of SendTotal1588 in this port."
			::= { hwPtpPortStatisticEntry 10 }
		
		-- *******.4.1.2011.**********.2.1.11
		hwPtpPortSendAnnounce OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the SendAnnounce in this port."
			::= { hwPtpPortStatisticEntry 11 }
		
		-- *******.4.1.2011.**********.2.1.12
		hwPtpPortSendSync OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the SendSync in this port."
			::= { hwPtpPortStatisticEntry 12 }
		
		-- *******.4.1.2011.**********.2.1.13
		hwPtpPortSendReq OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the SendReq in this port."
			::= { hwPtpPortStatisticEntry 13 }
		
		-- *******.4.1.2011.**********.2.1.14
		hwPtpPortSendResp OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of SendResp in this port."
			::= { hwPtpPortStatisticEntry 14 }
		
		-- *******.4.1.2011.**********.2.1.15
		hwPtpPortSendFollowup OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of SendFollowup in this port."
			::= { hwPtpPortStatisticEntry 15 }
		
		-- *******.4.1.2011.**********.2.1.16
		hwPtpPortSendPdelayrespfollowup OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of SendPdelay_resp_followup in this port."
			::= { hwPtpPortStatisticEntry 16 }
		
		-- *******.4.1.2011.**********.2.1.17
		hwPtpPortDiscardTotal1588 OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the DiscardTotal1588 in this port."
			::= { hwPtpPortStatisticEntry 17 }
		
		-- *******.4.1.2011.**********.2.1.18
		hwPtpPortDiscardAnnounce OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of DiscardAnnounce in this port."
			::= { hwPtpPortStatisticEntry 18 }
		
		-- *******.4.1.2011.**********.2.1.19
		hwPtpPortDiscardSync OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of DiscardSync in this port."
			::= { hwPtpPortStatisticEntry 19 }
		
		-- *******.4.1.2011.**********.2.1.20
		hwPtpPortDiscardDelayreq OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of DiscardDelayreq in this port."
			::= { hwPtpPortStatisticEntry 20 }
		
		-- *******.4.1.2011.**********.2.1.21
		hwPtpPortDiscardPdelayreq OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of DiscardPdelayreq in this port."
			::= { hwPtpPortStatisticEntry 21 }
		
		-- *******.4.1.2011.**********.2.1.22
		hwPtpPortDiscardResp OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of DiscardResp in this port."
			::= { hwPtpPortStatisticEntry 22 }
		
		-- *******.4.1.2011.**********.2.1.23
		hwPtpPortDiscardPdelayresp OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of DiscardPdelayresp in this port."
			::= { hwPtpPortStatisticEntry 23 }
		
		-- *******.4.1.2011.**********.2.1.24
		hwPtpPortDiscardFollowup OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of DiscardFollowup in this port."
			::= { hwPtpPortStatisticEntry 24 }
		
		-- *******.4.1.2011.**********.2.1.25
		hwPtpPortDiscardPdelayrespfollowup OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of the DiscardPdelay_resp_followup in this port."
			::= { hwPtpPortStatisticEntry 25 }
		
		-- *******.4.1.2011.**********.2.1.26
		hwPtpPortStaticPktReset OBJECT-TYPE
			SYNTAX INTEGER
				{
				reset(1),
				unused(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Reset port PTP packets statistic."
			::= { hwPtpPortStatisticEntry 26 }
			
		-- *******.4.1.2011.**********.2.1.27
		hwPtpPortPassiveTimeOffsetMax OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The max value of passive port time offset."
			::= { hwPtpPortStatisticEntry 27 }
			
		-- *******.4.1.2011.**********.2.1.28
		hwPtpPortPassiveTimeOffsetMin OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The min value of passive port time offset."
			::= { hwPtpPortStatisticEntry 28 }
			
		-- *******.4.1.2011.**********.2.1.29
		hwPtpPortPassiveTimeOffsetMean OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The mean value of passive port time offset."
			::= { hwPtpPortStatisticEntry 29 }
			
		-- *******.4.1.2011.**********.2.1.30
		hwPtpPortPassiveTimeOffset OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The value of passive port time offset."
			::= { hwPtpPortStatisticEntry 30 }
		
		-- *******.4.1.2011.**********.3
		hwPtpBitsClockSourceTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpBitsClockSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"BitsClockSourceTable."
			::= { hwPtpPortObjects 3 }
		
		-- *******.4.1.2011.**********.3.1
		hwPtpBitsClockSourceEntry OBJECT-TYPE
			SYNTAX HwPtpBitsClockSourceEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in hwPtpPortStatisticTable."
			INDEX { hwPtpBitsPortIndex }
			::= { hwPtpBitsClockSourceTable 1 }
		
		HwPtpBitsClockSourceEntry ::=
			SEQUENCE { 
				hwPtpBitsPortIndex
					Integer32,
				hwPtpBitsClockAccuracy
					Integer32,
				hwPtpBitsClockClass
					Integer32,
				hwPtpBitsPriority1
					Integer32,
				hwPtpBitsPriority2
					Integer32,
				hwPtpBitsTimeSource
					INTEGER,
				hwPtpBitsSignal
					INTEGER,
				hwPtpBitsSwitch
					INTEGER,
				hwPtpBitsDirection
					INTEGER,
				hwPtpBitsNormalStatus
					INTEGER,
				hwPtpBitsReceiveDelay
					Integer32,
				hwPtpBitsSendDelay
					Integer32,
                                                                                     hwPtpBitsLocalPriority
                                                                                                              Integer32,
                                                                                    hwPtpBitsGMClockId
					OCTET STRING,
			                    hwPtpBitsGMOffsetScaledLogVariance
					INTEGER
			 }

		-- *******.4.1.2011.**********.3.1.1
		hwPtpBitsPortIndex OBJECT-TYPE
			SYNTAX Integer32 (1..10)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The BitsPortIndex."
			::= { hwPtpBitsClockSourceEntry 1 }
		
		-- *******.4.1.2011.**********.3.1.2
		hwPtpBitsClockAccuracy OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BitsClockAccuracy."
			DEFVAL { 32 }
			::= { hwPtpBitsClockSourceEntry 2 }
		
		-- *******.4.1.2011.**********.3.1.3
		hwPtpBitsClockClass OBJECT-TYPE
            SYNTAX Integer32 (0..255 | 1024..1279)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BitsClockClass."
			DEFVAL { 6 }
			::= { hwPtpBitsClockSourceEntry 3 }
		
		-- *******.4.1.2011.**********.3.1.4
		hwPtpBitsPriority1 OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BitsPriority1."
			DEFVAL { 128 }
			::= { hwPtpBitsClockSourceEntry 4 }
		
		-- *******.4.1.2011.**********.3.1.5
		hwPtpBitsPriority2 OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BitsPriority2."
			DEFVAL { 128 }
			::= { hwPtpBitsClockSourceEntry 5 }
		
		-- *******.4.1.2011.**********.3.1.6
		hwPtpBitsTimeSource OBJECT-TYPE
			SYNTAX INTEGER
				{
				atomicclock(1),
				gps(2),
				terrestrialradio(3),
				ptp(4),
				ntp(5),
				handset(6),
				other(7),
				internaloscillator(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BitsTimeSource."
			DEFVAL { gps }
			::= { hwPtpBitsClockSourceEntry 6 }
		
		-- *******.4.1.2011.**********.3.1.7
		hwPtpBitsSignal OBJECT-TYPE
			SYNTAX INTEGER
				{
				onepps(1),
				twomhz(2),
				twombps(3),
				dcls(4),
				none(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The signal of BITS."
			::= { hwPtpBitsClockSourceEntry 7 }
		
		-- *******.4.1.2011.**********.3.1.8
		hwPtpBitsSwitch OBJECT-TYPE
			SYNTAX INTEGER
				{
				off(1),
				on(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The switch status."
			DEFVAL { off }
			::= { hwPtpBitsClockSourceEntry 8 }
		
		-- *******.4.1.2011.**********.3.1.9
		hwPtpBitsDirection OBJECT-TYPE
			SYNTAX INTEGER
				{
				in(1),
				out(2),
				both(3),
				none(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The direction of BITS."
			::= { hwPtpBitsClockSourceEntry 9 }
		
		-- *******.4.1.2011.**********.3.1.10
		hwPtpBitsNormalStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				abnormal(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The normal status of BITS."
			::= { hwPtpBitsClockSourceEntry 10 }
		
		-- *******.4.1.2011.**********.3.1.11
		hwPtpBitsReceiveDelay OBJECT-TYPE
			SYNTAX Integer32 (0..2000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Bits receive delay correction."
			DEFVAL { 0 }
			::= { hwPtpBitsClockSourceEntry 11 }
		
		-- *******.4.1.2011.**********.3.1.12
		hwPtpBitsSendDelay OBJECT-TYPE
			SYNTAX Integer32 (0..1600)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Bits send delay correction."
			DEFVAL { 0 }
			::= { hwPtpBitsClockSourceEntry 12 }

		-- *******.4.1.2011.**********.3.1.13
		hwPtpBitsLocalPriority OBJECT-TYPE
			SYNTAX Integer32 (1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The local-priority attribute of the bits."
			DEFVAL { 128 }
			::= { hwPtpBitsClockSourceEntry 13 }

		-- *******.4.1.2011.**********.3.1.14
		hwPtpBitsGMClockId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The grandmaster-clockid of BITS."
			::= { hwPtpBitsClockSourceEntry 14 }

		-- *******.4.1.2011.**********.3.1.15
		hwPtpBitsGMOffsetScaledLogVariance OBJECT-TYPE
			SYNTAX INTEGER
				{
				hFFFF(1),
				h4E5D(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The OffsetScaledLogVariance of BITS."
			DEFVAL { 1 }
			::= { hwPtpBitsClockSourceEntry 15 }
		
		-- *******.4.1.2011.**********
		hwPtpLeapObjects OBJECT IDENTIFIER ::= { hwPtpMIB 3 }
		
		-- *******.4.1.2011.**********.1
		hwPtpLeapTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpLeapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Ptp Leap Table."
			::= { hwPtpLeapObjects 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwPtpLeapEntry OBJECT-TYPE
			SYNTAX HwPtpLeapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PtpLeapEntry."
			INDEX { hwPtpLeapInfo }
			::= { hwPtpLeapTable 1 }
		
		HwPtpLeapEntry ::=
			SEQUENCE { 
				hwPtpLeapInfo
					OCTET STRING,
				hwPtpLeap59or61
					INTEGER,
				hwPtpLeapRowStatus
					RowStatus
			 }

		-- *******.4.1.2011.**********.1.1.1
		hwPtpLeapInfo OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"leap59 or leap61 adjust time."
			::= { hwPtpLeapEntry 1 }
		
		-- *******.4.1.2011.**********.1.1.2
		hwPtpLeap59or61 OBJECT-TYPE
			SYNTAX INTEGER
				{
				leap59(1),
				leap61(2),
				none(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"leap59 or leap61,default is none. "
			DEFVAL { 3 }
			::= { hwPtpLeapEntry 2 }
		
		-- *******.4.1.2011.**********.1.1.3
		hwPtpLeapRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"leap table rowstatus."
			::= { hwPtpLeapEntry 3 }
		
		-- *******.4.1.2011.**********
		hwPtpAclPermitClkIdObjects OBJECT IDENTIFIER ::= { hwPtpMIB 4 }
		
		-- *******.4.1.2011.**********.1
		hwPtpAclPermitClkIdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpAclPermitClkIdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PtpAclPermitClkIdTable."
			::= { hwPtpAclPermitClkIdObjects 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwPtpAclPermitClkIdEntry OBJECT-TYPE
			SYNTAX HwPtpAclPermitClkIdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PtpAclPermitClkIdEntry."
			INDEX { hwPtpAclPermitClkIdInfo }
			::= { hwPtpAclPermitClkIdTable 1 }
		
		HwPtpAclPermitClkIdEntry ::=
			SEQUENCE { 
				hwPtpAclPermitClkIdInfo
					OCTET STRING,
				hwPtpAclPermitClkIdRowStatus
					RowStatus
			 }

		-- *******.4.1.2011.**********.1.1.1
		hwPtpAclPermitClkIdInfo OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"permit join BMC's clockID"
			::= { hwPtpAclPermitClkIdEntry 1 }
		
		-- *******.4.1.2011.**********.1.1.2
		hwPtpAclPermitClkIdRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"aclclockid's rowstatus."
			::= { hwPtpAclPermitClkIdEntry 2 }
		
		-- *******.4.1.2011.**********
		hwPtpNotifications OBJECT IDENTIFIER ::= { hwPtpMIB 5 }
		
		-- *******.4.1.2011.**********.1
		hwPtpPortStateChange NOTIFICATION-TYPE
			OBJECTS { hwPtpPortName, hwPtpPortState, hwPtpOldPortState }
			STATUS current
			DESCRIPTION 
				"Port status change notification."
			::= { hwPtpNotifications 1 }
		
		-- *******.4.1.2011.**********.2
		hwPtpClockSourceChange NOTIFICATION-TYPE
			OBJECTS { hwPtpOldMasterClockId, hwPtpCurrentMasterClockId, hwPtpPortOldSourcePortNum, hwPtpPortSourcePortNum, hwPtpOldPortName, 
				hwPtpPortName }
			STATUS current
			DESCRIPTION 
				"Clock source change notification."
			::= { hwPtpNotifications 2 }
		
		-- *******.4.1.2011.**********.3
		hwPtpTimeSyncFaulty NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeSynchronizationStatus }
			STATUS current
			DESCRIPTION 
				"This is a private node,for time synchronization status is faulty."
			::= { hwPtpNotifications 3 }
		
		-- *******.4.1.2011.**********.4
		hwPtpTimeSyncResume NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeSynchronizationStatus }
			STATUS current
			DESCRIPTION 
				"This is a private node,for time synchronization status is resume."
			::= { hwPtpNotifications 4 }
		
		-- *******.4.1.2011.**********.8
		hwPtpAdaptiveServerStateChange NOTIFICATION-TYPE
			OBJECTS { hwPtpAdaptiveUserMode, hwPtpAdaptiveUserState }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { hwPtpNotifications 8 }
		
		-- *******.4.1.2011.**********.9
		hwPtpAdaptiveClientListChange NOTIFICATION-TYPE
			OBJECTS { hwPtpAdaptiveClientIpList, hwPtpAdaptiveClientIpChangeFlag, hwPtpAdaptiveClientId }
			STATUS current
			DESCRIPTION 
				"Indicates the master's client ip list"
			::= { hwPtpNotifications 9 }
		
		-- *******.4.1.2011.**********.10
		hwPtpAdaptiveNegoStateChange NOTIFICATION-TYPE
			OBJECTS { hwPtpAdaptiveRemoteServer1NegoState, hwPtpAdaptiveRemoteServer2NegoState }
			STATUS current
			DESCRIPTION 
				"Indicates the client device's negotiate state with remote-server."
			::= { hwPtpNotifications 10 }
		
		-- *******.4.1.2011.**********.11
		hwPTPRingFiberLengthChange NOTIFICATION-TYPE
			OBJECTS { hwPtpPortName, hwPtpPortRingFiberLengthChangeValue, hwPtpPortRingFiberLengthChangeValueFlag }
			STATUS current
			DESCRIPTION 
				"Ring fiber length value change notification."
			::= { hwPtpNotifications 11 }
		
		-- *******.4.1.2011.**********.12
		hwPTPRingFiberLengthChangeResume NOTIFICATION-TYPE
			OBJECTS { hwPtpPortName, hwPtpPortRingFiberLengthChangeValue, hwPtpPortRingFiberLengthChangeValueFlag }
			STATUS current
			DESCRIPTION 
				"Ring fiber length value change resume notification."
			::= { hwPtpNotifications 12 }
		
		-- *******.4.1.2011.**********.13
		hwPtpTimeLockFail NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeLockStatus }
			STATUS current
			DESCRIPTION 
				"Current time synchronization lock failed."
			::= { hwPtpNotifications 13 }
		
		-- *******.4.1.2011.**********.14
		hwPtpTimeLockFailResume NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeLockStatus }
			STATUS current
			DESCRIPTION 
				"Current time synchronization lock resumed."
			::= { hwPtpNotifications 14 }
		
		-- *******.4.1.2011.**********.15
		hwPtpTimeStampUnChanged NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeStampStatus }
			STATUS current
			DESCRIPTION 
				"Current time stamp in the sync-message isn't changed."
			::= { hwPtpNotifications 15 }
		
		-- *******.4.1.2011.**********.16
		hwPtpTimeStampUnChangedResume NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeStampStatus }
			STATUS current
			DESCRIPTION 
				"Current time stamp in the sync-message is changed."
			::= { hwPtpNotifications 16 }
		
		-- *******.4.1.2011.**********.17
		hwPtpFrequencyLockFail NOTIFICATION-TYPE
			OBJECTS { hwPtpFreqLockStatus }
			STATUS current
			DESCRIPTION 
				"Current frequency synchronization lock failed."
			::= { hwPtpNotifications 17 }
		
		-- *******.4.1.2011.**********.18
		hwPtpFrequencyLockResume NOTIFICATION-TYPE
			OBJECTS { hwPtpFreqLockStatus }
			STATUS current
			DESCRIPTION 
				"Current frequency synchronization lock resumed."
			::= { hwPtpNotifications 18 }
		
		-- *******.4.1.2011.**********.19
		hwPtpExtTimePortLost NOTIFICATION-TYPE
			OBJECTS { hwPtpExtTimePortStatus, hwPtpExtTimePortType }
			STATUS current
			DESCRIPTION 
				"External time port lost."
			::= { hwPtpNotifications 19 }
		
		-- *******.4.1.2011.**********.20
		hwPtpExtTimePortLostResume NOTIFICATION-TYPE
			OBJECTS { hwPtpExtTimePortStatus, hwPtpExtTimePortType }
			STATUS current
			DESCRIPTION 
				"External time port lost resume."
			::= { hwPtpNotifications 20 }
		
		-- *******.4.1.2011.**********.21
		hwPtpPdvOverflow NOTIFICATION-TYPE
			STATUS current
			DESCRIPTION 
				"Packet delay variety overflow."
			::= { hwPtpNotifications 21 }
		
		-- *******.4.1.2011.**********.22
		hwPtpPdvOverflowResume NOTIFICATION-TYPE
			STATUS current
			DESCRIPTION 
				"Packet delay variety overflow resume."
			::= { hwPtpNotifications 22 }
		
		-- *******.4.1.2011.**********.23
		hwPtpPassiveFiberLengthChange NOTIFICATION-TYPE
			OBJECTS { hwPtpPortName, hwPtpPortRingFiberLengthChangeValue, hwPtpPortRingFiberLengthChangeValueFlag }
			STATUS current
			DESCRIPTION 
				"This object indicates the alarm about performance change on Passive ports."
			::= { hwPtpNotifications 23 }
		
		-- *******.4.1.2011.**********.24
		hwPtpAcrSyncBad NOTIFICATION-TYPE
			OBJECTS { hwPtpAcrSyncBadStatus }
			STATUS current
			DESCRIPTION 
				"1588 ACR sync bad."
			::= { hwPtpNotifications 24 }
		
		-- *******.4.1.2011.**********.25
		hwPtpAcrSyncBadResume NOTIFICATION-TYPE
			OBJECTS { hwPtpAcrSyncBadStatus }
			STATUS current
			DESCRIPTION 
				"1588 ACR sync bad resume."
			::= { hwPtpNotifications 25 }
		
		-- *******.4.1.2011.**********.26
		hwPtpPdvLimitExceed NOTIFICATION-TYPE
			OBJECTS { hwPtpPdvLimitExceedStatus }
			STATUS current
			DESCRIPTION 
				"1588 ACR PDV limit exceed."
			::= { hwPtpNotifications 26 }
		
		-- *******.4.1.2011.**********.27
		hwPtpPdvLimitExceedResume NOTIFICATION-TYPE
			OBJECTS { hwPtpPdvLimitExceedStatus }
			STATUS current
			DESCRIPTION 
				"1588 ACR PDV limit exceed resume."
			::= { hwPtpNotifications 27 }
		
		-- *******.4.1.2011.**********.28
		hwPtpAdaptiveNegoInfoChange NOTIFICATION-TYPE
			OBJECTS { hwPtpAdaptiveRemoteServerId, hwPtpAdaptiveRemoteServerNegoState, hwPtpAdaptiveNegoErrorReason }
			STATUS current
			DESCRIPTION 
				"Ptp adaptive device's remote server negotiate state and nego error reason."
			::= { hwPtpNotifications 28 }
		
		-- *******.4.1.2011.**********.29
		hwPtpAdaptiveTraceSourceChange NOTIFICATION-TYPE
			OBJECTS { hwPtpAdaptiveOldTraceSource, hwPtpAdaptiveTraceSource }
			STATUS current
			DESCRIPTION 
				"Ptp adaptive client trace source change notification."
			::= { hwPtpNotifications 29 }

		-- *******.4.1.2011.**********.30
		hwPtpPortBmcInfoChange NOTIFICATION-TYPE
			OBJECTS { hwPtpPortName, hwPtpPortSourcePortClockId, hwPtpPortSourcePortNum, hwPtpPortSourceStepsRemoved, hwPtpCurrentMasterClockId }
			STATUS current
			DESCRIPTION 
				"Port bmc info changed."
			::= { hwPtpNotifications 30 }
		
		-- *******.4.1.2011.**********.31
		hwPtpTimeSourceClockClassDecline NOTIFICATION-TYPE
			STATUS current
			DESCRIPTION 
				"The clock-class of input time source is below threshold."
			::= { hwPtpNotifications 31 }
		
		-- *******.4.1.2011.**********.32
		hwPtpTimeSourceClockClassDeclineResume NOTIFICATION-TYPE
			STATUS current
			DESCRIPTION 
				"The clock-class of input time source is above or equal to threshold."
			::= { hwPtpNotifications 32 }
		
		-- *******.4.1.2011.**********.33
		hwPtpPktLos NOTIFICATION-TYPE
			OBJECTS { hwPtpPktType }
			STATUS current
			DESCRIPTION 
				"The ptp packet of the trace source is lost."
			::= { hwPtpNotifications 33 }
		
		-- *******.4.1.2011.**********.34
		hwPtpPktLosResume NOTIFICATION-TYPE
			OBJECTS { hwPtpPktType }
			STATUS current
			DESCRIPTION 
				"The ptp packet of the trace port is normal."
			::= { hwPtpNotifications 34 }
		
		-- *******.4.1.2011.**********.35
		hwPtpStandardTimeOffsetOver NOTIFICATION-TYPE
			OBJECTS { hwPtpStandardTimeOffset, hwPtpAlarmThresholdStandardTimeOffset }
			STATUS current
			DESCRIPTION 
				"The ptp standard time offset is abnormal."
			::= { hwPtpNotifications 35 }
		
		-- *******.4.1.2011.**********.36
		hwPtpStandardTimeOffsetOverResume NOTIFICATION-TYPE
			OBJECTS { hwPtpStandardTimeOffset, hwPtpAlarmThresholdStandardTimeOffset }
			STATUS current
			DESCRIPTION 
				"The ptp standard time offset is normal."
			::= { hwPtpNotifications 36 }
		
		-- *******.4.1.2011.**********.37
		hwPtpTimeOffsetSumOver NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeOffsetSumP2P, hwPtpAlarmThresholdOffsetSum }
			STATUS current
			DESCRIPTION 
				"The ptp time offset sum is abnormal."
			::= { hwPtpNotifications 37 }
		
		-- *******.4.1.2011.**********.38
		hwPtpTimeOffsetSumOverResume NOTIFICATION-TYPE
			OBJECTS { hwPtpTimeOffsetSumP2P, hwPtpAlarmThresholdOffsetSum }
			STATUS current
			DESCRIPTION 
				"The ptp time offset sum is normal."
			::= { hwPtpNotifications 38 }

		-- *******.4.1.2011.**********.39
		hwPtpLcsResNotEnough NOTIFICATION-TYPE
			OBJECTS { hwPtpChassisId, hwPtpSlotId }
			STATUS current
			DESCRIPTION 
				"PTP license resources are not enough."
			::= { hwPtpNotifications 39 }

		-- *******.4.1.2011.**********.40
		hwPtpLcsResNotEnoughResume NOTIFICATION-TYPE
			OBJECTS { hwPtpChassisId, hwPtpSlotId }
			STATUS current
			DESCRIPTION 
				"PTP license resources are enough."
			::= { hwPtpNotifications 40 }

		-- *******.4.1.2011.**********.41
		hwPtpAdaptivePtsfStateChange NOTIFICATION-TYPE
			OBJECTS { hwPtpAdaptiveServerIndex, hwPtpAdaptivePtsfType, hwPtpAdaptivePtsfState }
			STATUS current
			DESCRIPTION 
				"PTP adaptive PTSF state change notification."
			::= { hwPtpNotifications 41 }

                                             -- *******.4.1.2011.**********.42
		hwPtpaATRLicenseInactive NOTIFICATION-TYPE
			STATUS current
			DESCRIPTION 
				"The 1588V2 ATR function license was not activated. To ensure you can properly use 1588V2 ATR function, please purchase and activate 1588V2 ATR license."
			::= { hwPtpNotifications 42 }
		
		-- *******.4.1.2011.**********.43
		hwPtpaATRLicenseInactiveResume NOTIFICATION-TYPE
			STATUS current
			DESCRIPTION 
				"The 1588V2 ATR function license inactivation alarm was cleared."
			::= { hwPtpNotifications 43}

	                     -- *******.4.1.2011.**********.44
		hwPtpPortNonSupport NOTIFICATION-TYPE
			OBJECTS { hwPtpChassisId, hwPtpPortName }
			STATUS current
			DESCRIPTION 
				"In current link mode, the hardware, such as boards and optical modules, do not support PTP time synchronization."
			::= { hwPtpNotifications 44 }

		
		-- *******.4.1.2011.**********.45
		hwPtpPortNonSupportResume NOTIFICATION-TYPE
			OBJECTS { hwPtpChassisId, hwPtpPortName }
			STATUS current
			DESCRIPTION 
				"The alarm that the hardware do not support PTP time synchronization is resumed."
			::= { hwPtpNotifications 45 }

		-- *******.4.1.2011.**********
		hwPtpConformance OBJECT IDENTIFIER ::= { hwPtpMIB 6 }
		
		-- *******.4.1.2011.**********.1
		hwPtpCompliance OBJECT IDENTIFIER ::= { hwPtpConformance 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwPtpComliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"This is the PTP module compliance."
			MODULE -- this module
				MANDATORY-GROUPS { hwPtpGlobalObjectsGroup, hwPtpPortObjectsGroup, hwPtpNotificationsGroup, hwPtpLeapObjectsGroup, hwPtpAclPermitClkIdObjectsGroup
					 }
			::= { hwPtpCompliance 1 }
		
		-- *******.4.1.2011.**********.2
		hwPtpGroups OBJECT IDENTIFIER ::= { hwPtpConformance 2 }
		
		-- *******.4.1.2011.**********.2.1
		hwPtpGlobalObjectsGroup OBJECT-GROUP
			OBJECTS { hwPtpEnable, hwPtpDomain, hwPtpSlaveOnly, hwPtpDeviceType, hwPtpLocalClockId, 
				hwPtpLocalClockClass, hwPtpLocalClockAccuracy, hwPtpLocalClockPriority1, hwPtpLocalClockPriority2, hwPtpLocalClockTimeSource, 
				hwPtpUtc, hwPtpCurrentUtcOffset, hwPtpCurrentMasterClockId, hwPtpCurrentMasterClockReceivePort, hwPtpCurrentMasterClockStepRemoved, 
				hwPtpTimeSynchronizationStatus, hwPtpTimeTraceable, hwPtpTimeScale, hwPtpVersion, hwPtpProfile, hwPtpLocalClockLocalPriority, hwPtpFrequencyRecoverMode, 
				hwPtpTimeSyncTime, hwPtpGrandMasterClockTimeSource, hwPtpGrandMasterClockClass, hwPtpGrandMasterClockAccuracy, hwPtpGrandMasterClockPriority2, 
				hwPtpGrandMasterClockPriority1, hwPtpCurrentMasterClockReceivePortType, hwPtpBitsDclsSendDelay, hwPtpBitsDclsReceiveDelay, hwPtpBits1ppsSendDelay, 
				hwPtpBits1ppsReceiveDelay, hwPtpOldMasterClockId, hwCurrentUtcOffsetValid, hwPtpFrequencyTraceable, hwPtpAclEnable, 
				hwPtpSetPortStateEnable, hwPtpAdaptiveRemoteServerId, hwPtpDfxFreqOffsetMin, hwPtpDfxPhyPhaseSubMean, hwPtpPassiveAlarmThreshold, 
				hwPtpAdaptiveRemoteServer1KeepAliveEnable, hwPtpDfxTimeSyncMPDlyMax, hwPtpDfxTimeSyncMPDlyMin, hwPtpDfxFreqOffsetMax, hwPtpAdaptiveAnnReceiptTimeout, 
				hwPtpAdaptiveDscp, hwPtpDfxACRNegMaxPdCur, hwPtpDfxACRMinPdCur, hwPtpAcrSyncBadStatus, hwPtpExtTimePortType, 
				hwPtpAdaptiveRemoteServerNegoState, hwPtpDfxACRMaxPdCur, hwPtpAdaptiveAnnounceReceiptTimeout, hwPtpDfxFreqOffsetMean, hwPtpDfxPhyPhaseSubCur, 
				hwPtpDfxACRMaxPdMin, hwPtpAdaptiveUserState, hwPtpAdaptiveUserMode, hwPtpAdaptiveClockclassSsmMapping, hwPtpDfxACRMinPdMin, 
				hwPtpDfxACRMinPdMax, hwPtpDfxACRNegMinPdv, hwPtpCLKBoardType, hwPtpExtTimePortStatus, hwPtpAdaptiveAnnounceDuration, 
				hwPtpDfxFreqSyncMode, hwPtpDfxTimeSyncOffsetMean, hwPtpDfxFreqOffsetCur, hwPtpAdaptiveRemoteServer2NegoState, hwPtpPdvLimitExceedStatus, 
				hwPtpAdaptiveLocalIp, hwPtpAdaptiveRemoteServer2Ip, hwPtpDfxTimeSyncOffsetMax, hwPtpAdaptiveRemoteServer2KeepAliveEnable, hwPtpAdaptiveFrequencyProfile, 
				hwPtpDfxACRNegMaxPdMean, hwPtpAdaptiveRemoteServer1Ip, hwPtpDfxACRNegMinPdMean, hwPtpAdaptiveSyncInterval, hwPtpDfxACRNegMaxPdv, 
				hwPtpDfxACRMaxPdMean, hwPtpAdaptiveEnable, hwPtpDfxTimeSyncOffsetMin, hwPtpDfxDlyMeasureEnable, hwPtpAdaptiveDlyRespInterval, 
				hwPtpAdaptiveSyncDuration, hwPtpDfxACRNegMaxPdMin, hwPtpFreqLockStatus, hwPtpAcrEnable, hwPtpAdaptiveSyncMode, 
				hwPtpDfxTimeSyncMPDlyCur, hwPtpDfxTimeSyncMPDlyMean, hwPtpTimeStampStatus, hwPtpDfxPhyPhaseSubMin, hwPtpDfxPhyPhaseSubMax, 
				hwPtpAdaptiveNegoErrorReason, hwPtpDfxACRPosMinPdv, hwPtpDfxACRNegMinPdCur, hwPtpDfxTimeSyncMode, hwPtpAdaptiveClientIpChangeFlag, 
				hwPtpDfxACRNegMinPdMin, hwPtpDfxACRNegMinPdMax, hwPtpDfxACRPosMaxPdv, hwPtpPassiveMeasureEnable, hwPtpAdaptiveDomain, 
				hwPtpDfxACRMinPdMean, hwPtpDfxTimeSyncOffsetCur, hwPtpTimeLockStatus, hwPtpAdaptiveAnnounceInterval, hwPtpDfxACRNegMaxPdMax, 
				hwPtpAdaptiveRemoteServer1NegoState, hwPtpDfxACRMaxPdMax, hwPtpAdaptiveOldTraceSource, hwPtpAdaptiveForwardMode, hwPtpAdaptiveTraceSource, 
				hwPtpAdaptiveVpnInstance, hwPtpAdaptiveDelayRespDuration }
			STATUS current
			DESCRIPTION 
				"This is the hwPtpGlobalObjectsGroups."
			::= { hwPtpGroups 1 }
		
		-- *******.4.1.2011.**********.2.2
		hwPtpPortObjectsGroup OBJECT-GROUP
			OBJECTS { hwPtpPortEnable, hwPtpPortDelayMechanism, hwPtpPortDomain, hwPtpPortTcOcStaticClockId, hwPtpPortAnnounceInterval, 
				hwPtpPortAnnounceReceiptTimeout, hwPtpPortSyncInterval, hwPtpPortMinDelayReqInterval, hwPtpPortMinPdelayReqInterval, hwPtpPortAsymmetryNegativeCorrection, 
				hwPtpPortAsymmetryPositiveCorrection, hwPtpPortMacEgressDestinationMac, hwPtpPortMacEgressVlanId, hwPtpPortMacEgressPacketPriority, hwPtpPortUdpEgressSourceIp, 
				hwPtpPortUdpEgressDestinationIp, hwPtpPortUdpEgressDestinationMac, hwPtpPortUdpEgressDscp, hwPtpPortUdpEgressVlanId, hwPtpPortUdpEgressPacketPriority, 
				hwPtpPortAnnounceDrop, hwPtpPortState, hwPtpPortSourcePortClockId, hwPtpBitsDirection, hwPtpBitsSignal, 
				hwPtpBitsSwitch, hwPtpBitsNormalStatus, hwPtpPortRowStatus, hwPtpPortStaticPktReset, hwPtpPortType, 
				hwPtpBitsClockAccuracy, hwPtpBitsClockClass, hwPtpPortCfgMsgFormat, hwPtpPortCfgExtInterfaceMode, hwPtpPortCfgLinkStatus, 
				hwPtpPortPortVlan, hwPtpPortNumber, hwPtpPortSourcePort, hwPtpPortSourcePortCard, hwPtpPortSourcePortSlot, 
				hwPtpPortSourcePortNum, hwPtpPortTcOcStaticClockPort, hwPtpPortTcOcStaticClockCard, hwPtpPortTcOcStaticClockSlot, hwPtpPortTcOcStaticClockPortNum, 
				hwPtpPortAnnounceReceiptTimeout2, hwPtpBitsPriority2, hwPtpBitsPriority1, hwPtpBitsTimeSource, hwPtpPortClockStep, 
				hwPtpPeerAnnounceSendInterval, hwPtpOldPortState, hwPtpPortRecvCorrectend, hwPtpPortRecvAnnounce, hwPtpPortRecvSync, 
				hwPtpPortRecvReq, hwPtpPortRecvRespCnt, hwPtpPortRecvFollowup, hwPtpPortRecvPdelayrespfollowup, hwPtpPortSendTotal1588, 
				hwPtpPortSendAnnounce, hwPtpPortSendSync, hwPtpPortSendReq, hwPtpPortSendResp, hwPtpPortSendFollowup, 
				hwPtpPortSendPdelayrespfollowup, hwPtpPortDiscardTotal1588, hwPtpPortDiscardAnnounce, hwPtpPortDiscardSync, hwPtpPortDiscardDelayreq, 
				hwPtpPortDiscardPdelayreq, hwPtpPortDiscardResp, hwPtpPortDiscardPdelayresp, hwPtpPortDiscardFollowup, hwPtpPortDiscardPdelayrespfollowup, 
                                hwPtpPortPassiveTimeOffsetMax, hwPtpPortPassiveTimeOffsetMin, hwPtpPortPassiveTimeOffsetMean, hwPtpPortPassiveTimeOffset,				
				hwPtpPortCfgState, hwPtpPortRecvTransparent, hwPtpPortName }
			STATUS current
			DESCRIPTION 
				"This is the group of PTP port table."
			::= { hwPtpGroups 2 }
		
		-- *******.4.1.2011.**********.2.3
		hwPtpLeapObjectsGroup OBJECT-GROUP
			OBJECTS { hwPtpLeapRowStatus, hwPtpLeap59or61 }
			STATUS current
			DESCRIPTION 
				"This is the group of PTP leap table."
			::= { hwPtpGroups 3 }
		
		-- *******.4.1.2011.**********.2.4
		hwPtpAclPermitClkIdObjectsGroup OBJECT-GROUP
			OBJECTS { hwPtpAclPermitClkIdRowStatus }
			STATUS current
			DESCRIPTION 
				"This is the group of PTP acl table."
			::= { hwPtpGroups 4 }
		
		-- *******.4.1.2011.**********.2.5
		hwPtpNotificationsGroup NOTIFICATION-GROUP
			NOTIFICATIONS { hwPtpClockSourceChange, hwPtpTimeSyncResume, hwPtpTimeSyncFaulty, hwPtpPortStateChange, hwPtpTimeStampUnChanged, 
				hwPtpTimeStampUnChangedResume, hwPtpFrequencyLockFail, hwPtpFrequencyLockResume, hwPtpExtTimePortLost, hwPtpExtTimePortLostResume, 
				hwPTPRingFiberLengthChange, hwPTPRingFiberLengthChangeResume, hwPtpPassiveFiberLengthChange, hwPtpTimeLockFail, hwPtpTimeLockFailResume, 
				hwPtpPdvOverflow, hwPtpPdvOverflowResume, hwPtpPdvLimitExceed, hwPtpPdvLimitExceedResume, hwPtpAcrSyncBad, 
				hwPtpAcrSyncBadResume, hwPtpAdaptiveTraceSourceChange, hwPtpAdaptiveClientListChange, hwPtpAdaptiveServerStateChange, hwPtpAdaptiveNegoStateChange, 
				hwPtpAdaptiveNegoInfoChange }
			STATUS current
			DESCRIPTION 
				"This is the group of PTP notification."
			::= { hwPtpGroups 5 }
		
		-- *******.4.1.2011.**********
		hwPtpAdaptiveClientList OBJECT IDENTIFIER ::= { hwPtpMIB 7 }
		
		-- *******.4.1.2011.**********.1
		hwPtpAdaptiveClientListTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpAdaptiveClientListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PtpAdaptiveClientListTable."
			::= { hwPtpAdaptiveClientList 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwPtpAdaptiveClientListEntry OBJECT-TYPE
			SYNTAX HwPtpAdaptiveClientListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in hwPtpAdaptiveClientListTable."
			INDEX { hwPtpAdaptiveClientIpList }
			::= { hwPtpAdaptiveClientListTable 1 }
		
		HwPtpAdaptiveClientListEntry ::=
			SEQUENCE { 
				hwPtpAdaptiveClientIpList
					IpAddress
			 }

		-- *******.4.1.2011.**********.1.1.1
		hwPtpAdaptiveClientIpList OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's client info."
			::= { hwPtpAdaptiveClientListEntry 1 }
		
		-- *******.4.1.2011.**********
		hwPtpAdaptiveClient OBJECT IDENTIFIER ::= { hwPtpMIB 8 }
		
		-- *******.4.1.2011.**********.1
		hwPtpAdaptiveClientTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpAdaptiveClientEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PtpAdaptiveClientTable."
			::= { hwPtpAdaptiveClient 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwPtpAdaptiveClientEntry OBJECT-TYPE
			SYNTAX HwPtpAdaptiveClientEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in hwPtpAdaptiveClientTable."
			INDEX { hwPtpAdaptiveClientIndex }
			::= { hwPtpAdaptiveClientTable 1 }
		
		HwPtpAdaptiveClientEntry ::=
			SEQUENCE { 
				hwPtpAdaptiveClientIndex
					Integer32,
				hwPtpAdaptiveClientId
					Integer32,
				hwPtpAdaptiveClientIp
					IpAddress,
				hwPtpAdaptiveClientClockId
					OCTET STRING,
				hwPtpAdaptiveClientMode
					INTEGER,
				hwPtpAdaptiveClientAnnInterval
					Integer32,
				hwPtpAdaptiveClientSyncInterval
					Integer32,
				hwPtpAdaptiveClientDelayRespInterval
					Integer32,
				hwPtpAdaptiveClientAnnDuration
					Integer32,
				hwPtpAdaptiveClientSyncDuration
					Integer32,
				hwPtpAdaptiveClientDelayRespDuration
					Integer32
			 }

		-- *******.4.1.2011.**********.1.1.1
		hwPtpAdaptiveClientIndex OBJECT-TYPE
			SYNTAX Integer32 (0..512)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's client info."
			::= { hwPtpAdaptiveClientEntry 1 }
		
		-- *******.4.1.2011.**********.1.1.2
		hwPtpAdaptiveClientId OBJECT-TYPE
			SYNTAX Integer32 (0..511)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's client id."
			::= { hwPtpAdaptiveClientEntry 2 }
		
		-- *******.4.1.2011.**********.1.1.3
		hwPtpAdaptiveClientIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's client ip address."
			::= { hwPtpAdaptiveClientEntry 3 }
		
		-- *******.4.1.2011.**********.1.1.4
		hwPtpAdaptiveClientClockId OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's client ClockId."
			::= { hwPtpAdaptiveClientEntry 4 }
		
		-- *******.4.1.2011.**********.1.1.5
		hwPtpAdaptiveClientMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				atr(1),
				acroneway(2),
				acrtwoway(3),
				invalid(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's client negoiate mode. The default value is invalid(99)."
			::= { hwPtpAdaptiveClientEntry 5 }
		
		-- *******.4.1.2011.**********.1.1.6
		hwPtpAdaptiveClientAnnInterval OBJECT-TYPE
			SYNTAX Integer32 (-3..4)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Client device's requested send interval of announce packet."
			::= { hwPtpAdaptiveClientEntry 6 }
		
		-- *******.4.1.2011.**********.1.1.7
		hwPtpAdaptiveClientSyncInterval OBJECT-TYPE
			SYNTAX Integer32 (-7..4)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Client device's requested send interval of sync packet."
			::= { hwPtpAdaptiveClientEntry 7 }
		
		-- *******.4.1.2011.**********.1.1.8
		hwPtpAdaptiveClientDelayRespInterval OBJECT-TYPE
			SYNTAX Integer32 (-7..4)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Client device's requested send interval of delay_resp packet."
			::= { hwPtpAdaptiveClientEntry 8 }
		
		-- *******.4.1.2011.**********.1.1.9
		hwPtpAdaptiveClientAnnDuration OBJECT-TYPE
			SYNTAX Integer32 (60..1000)
			UNITS "s"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's announce packet duration attribute. The default value is 300."
			::= { hwPtpAdaptiveClientEntry 9 }
		
		-- *******.4.1.2011.**********.1.1.10
		hwPtpAdaptiveClientSyncDuration OBJECT-TYPE
			SYNTAX Integer32 (60..1000)
			UNITS "s"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's sync packet duration attribute. The default value is 300."
			::= { hwPtpAdaptiveClientEntry 10 }
		
		-- *******.4.1.2011.**********.1.1.11
		hwPtpAdaptiveClientDelayRespDuration OBJECT-TYPE
			SYNTAX Integer32 (0 | 60..1000)
			UNITS "s"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's delay_resp packet duration attribute. The default value is 300."
			::= { hwPtpAdaptiveClientEntry 11 }
		
		-- *******.4.1.2011.**********
		hwPtpAdaptiveServerList OBJECT IDENTIFIER ::= { hwPtpMIB 9 }
		
		-- *******.4.1.2011.**********.1
		hwPtpAdaptiveServerListTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwPtpAdaptiveServerListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"PtpAdaptiveServerListTable."
			::= { hwPtpAdaptiveServerList 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwPtpAdaptiveServerListEntry OBJECT-TYPE
			SYNTAX HwPtpAdaptiveServerListEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"An entry in hwPtpAdaptiveServerListTable"
			INDEX { hwPtpAdaptiveServerIndex }
			::= { hwPtpAdaptiveServerListTable 1 }
		
		HwPtpAdaptiveServerListEntry ::=
			SEQUENCE { 
				hwPtpAdaptiveServerIndex
					Integer32,
				hwPtpAdaptiveServerIp
					IpAddress,
				hwPtpAdaptiveNegotiateState
					INTEGER,
				hwPtpAdaptiveServerSSM
					INTEGER,
				hwPtpAdaptiveServerPriority
					Integer32,
				hwPtpAdaptiveServerPTSF
					INTEGER,
				hwPtpAdaptiveServerPriority1
					Integer32,
				hwPtpAdaptiveServerPriority2
					Integer32,
				hwPtpAdaptiveServerClockClass
					Integer32,
				hwPtpAdaptiveServerClockAccuracy
					INTEGER,
				hwPtpAdaptiveServerTimeSource
					INTEGER,
				hwPtpAdaptiveServerTwoStepFlag
					INTEGER,
				hwPtpAdaptiveServerNegoErrorReason
					INTEGER,
				hwPtpAdaptivePtsfType
					INTEGER,
				hwPtpAdaptivePtsfState
					INTEGER
			 }

		-- *******.4.1.2011.**********.1.1.1
		hwPtpAdaptiveServerIndex OBJECT-TYPE
			SYNTAX Integer32 (1..2)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's server index."
			::= { hwPtpAdaptiveServerListEntry 1 }
		
		-- *******.4.1.2011.**********.1.1.2
		hwPtpAdaptiveServerIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's server ip address."
			::= { hwPtpAdaptiveServerListEntry 2 }
		
		-- *******.4.1.2011.**********.1.1.3
		hwPtpAdaptiveNegotiateState OBJECT-TYPE
			SYNTAX INTEGER
				{
				init(0),
				success(1),
				error(2),
				none(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server negotiate state."
			::= { hwPtpAdaptiveServerListEntry 3 }
		
		-- *******.4.1.2011.**********.1.1.4
		hwPtpAdaptiveServerSSM OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				inv1(1),
				prc(2),
				inv3(3),
				ssua(4),
				inv5(5),
				inv6(6),
				inv7(7),
				ssub(8),
				inv9(9),
				inv10(10),
				sec(11),
				inv12(12),
				inv13(13),
				inv14(14),
				dnu(15),
				none(16),
				invalid(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's server clock ssm."
			::= { hwPtpAdaptiveServerListEntry 4 }
		
		-- *******.4.1.2011.**********.1.1.5
		hwPtpAdaptiveServerPriority OBJECT-TYPE
			SYNTAX Integer32 (1..2)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server clock priority attribute."
			::= { hwPtpAdaptiveServerListEntry 5 }
		
		-- *******.4.1.2011.**********.1.1.6
		hwPtpAdaptiveServerPTSF OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				abnormal(1),
				invalid(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwPtpAdaptiveServerListEntry 6 }
		
		-- *******.4.1.2011.**********.1.1.7
		hwPtpAdaptiveServerPriority1 OBJECT-TYPE
			SYNTAX Integer32 (0..256)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server clock priority1 attribute."
			::= { hwPtpAdaptiveServerListEntry 7 }
		
		-- *******.4.1.2011.**********.1.1.8
		hwPtpAdaptiveServerPriority2 OBJECT-TYPE
			SYNTAX Integer32 (0..256)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server clock priority2 attribute."
			::= { hwPtpAdaptiveServerListEntry 8 }
		
		-- *******.4.1.2011.**********.1.1.9
		hwPtpAdaptiveServerClockClass OBJECT-TYPE
			SYNTAX Integer32 (0..256)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server clock class."
			::= { hwPtpAdaptiveServerListEntry 9 }
		
		-- *******.4.1.2011.**********.1.1.10
		hwPtpAdaptiveServerClockAccuracy OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				accuracy25ns(32),
				accuracy100ns(33),
				accuracy250ns(34),
				accuracy1us(35),
				accuracy2p5us(36),
				accuracy10us(37),
				accuracy25us(38),
				accuracy100us(39),
				accuracy250us(40),
				accuracy1ms(41),
				accuracy2p5ms(42),
				accuracy10ms(43),
				accuracy25ms(44),
				accuracy100ms(45),
				accuracy250ms(46),
				accuracy1s(47),
				accuracy10s(48),
				accuracym10s(49)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server clock accuracy attribute."
			::= { hwPtpAdaptiveServerListEntry 10 }
		
		-- *******.4.1.2011.**********.1.1.11
		hwPtpAdaptiveServerTimeSource OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(0),
				atomicclock(16),
				gps(32),
				terrestrialradio(48),
				ptp(64),
				ntp(80),
				handset(96),
				other(144),
				internaloscillator(160)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server timesource."
			::= { hwPtpAdaptiveServerListEntry 11 }
		
		-- *******.4.1.2011.**********.1.1.12
		hwPtpAdaptiveServerTwoStepFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				oneStep(0),
				twoStep(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Ptp adaptive device's remote server twostep flag."
			::= { hwPtpAdaptiveServerListEntry 12 }
        -- *******.4.1.2011.**********.1.1.13
        hwPtpAdaptiveServerNegoErrorReason OBJECT-TYPE
            SYNTAX INTEGER
                {
                none(0),
                deletelink(1),
                announcetimeout(2),
                announcenegotimeout(3),
                announcenegodeny(4),
                syncnegotimeout(5),
                syncnegodeny(6),
                delayrespnegotimeout(7),
                delayrespnegodeny(8),
                ifcannotsupportptp(9),
                servicemodenotsupportptp(10),
                routeunreach(11),
                bmcfailed(12),
                mastersynchronizationfault(13)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Ptp adaptive device's remote server negotiate error reason."
            ::= { hwPtpAdaptiveServerListEntry 13 }

		-- *******.4.1.2011.**********.1.1.14
		hwPtpAdaptivePtsfType OBJECT-TYPE
		                   SYNTAX INTEGER
		                                      {
		                                      lossAnnounce(1),
		                                      lossSyncOrFollowup(2),
		                                      lossResp(3),
		                                      unusable(4)
		                                      }
		                   MAX-ACCESS read-only
		                   STATUS current
		                   DESCRIPTION
		                                     "Ptp adaptive PTSF type."
		                  ::= { hwPtpAdaptiveServerListEntry 14 }			

		-- *******.4.1.2011.**********.1.1.15
		hwPtpAdaptivePtsfState OBJECT-TYPE
		               SYNTAX INTEGER
		                                  {
		                                  true(1),
		                                  false(2)
		                                  }
		              MAX-ACCESS read-only
		              STATUS current
		              DESCRIPTION
		                                 "Ptp adaptive PTSF state."
		              ::= { hwPtpAdaptiveServerListEntry 15 } 
	END

--
-- HUAWEI-PTP-MIB.my
--