--
-- ISM-TRAP-MIB.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 4.0 Build 347
-- Friday, March 28, 2014 at 17:13:02
--

	ISM-TRAP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			OBJECT-G<PERSON><PERSON>, MODULE-COMP<PERSON><PERSON>NC<PERSON>, NOTIFICATION-G<PERSON>UP			
				FROM SNMPv2-CONF			
			enterprises, IpAddress, Integer32, Unsigned32, 
			Gauge32, Counter64, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			RowStatus, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		huaweistorage MODULE-IDENTITY 
			LAST-UPDATED "201210101725Z"		-- October 10, 2012 at 17:25 GMT
			ORGANIZATION 
				"Huawei Technologies Co.,Ltd."
			CONTACT-INFO 
				"Huawei Industrial Base
				Bantian, Longgang
				<PERSON> 518129
				People's Republic of China
				Website: http://www.huawei.com
				Email: <EMAIL>"
			DESCRIPTION 
				"Description."
			REVISION "201304071915Z"		-- April 07, 2013 at 19:15 GMT
			DESCRIPTION 
				" "
			::= { enterprises 34774 }

		
	
--
-- Textual conventions
--
	
		NodeCodeString ::= TEXTUAL-CONVENTION
			DISPLAY-HINT 
				"255a"
			STATUS current
			DESCRIPTION 
				"characters in length."
			SYNTAX OCTET STRING (SIZE (15..17))

	
--
-- Node definitions
--
	
		hwStorage OBJECT IDENTIFIER ::= { huaweistorage 4 }

		
		hwISM OBJECT IDENTIFIER ::= { hwStorage 1 }

		
		trapAddress OBJECT IDENTIFIER ::= { hwISM 2 }

		
		forwardAddrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF ForwardAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapAddress 1 }

		
		forwardAddrEntry OBJECT-TYPE
			SYNTAX ForwardAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { forwardAddrIndex }
			::= { forwardAddrTable 1 }

		
		ForwardAddrEntry ::=
			SEQUENCE { 
				forwardAddrIndex
					OCTET STRING,
				forwardAddrIP
					IpAddress,
				forwardAddrPort
					Gauge32,
				forwardAddrTrapVer
					Integer32,
				forwardAddrRowStatus
					RowStatus,
				forwardAddrIPNew
					OCTET STRING,
				forwardAddrTrapType
					Integer32
			 }

		forwardAddrIndex OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { forwardAddrEntry 1 }

		
		forwardAddrIP OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { forwardAddrEntry 2 }

		
		forwardAddrPort OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { forwardAddrEntry 3 }

		
		forwardAddrTrapVer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description.
				1: V1 Trap
				2: V2 Trap
				3: V3 Trap"
			::= { forwardAddrEntry 4 }

		
		forwardAddrRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { forwardAddrEntry 5 }

		
		forwardAddrIPNew OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { forwardAddrEntry 6 }

		
		forwardAddrTrapType OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				" "
			::= { forwardAddrEntry 7 }
--  
		
		event OBJECT IDENTIFIER ::= { hwISM 3 }

		
		eventTable OBJECT-TYPE
			SYNTAX SEQUENCE OF EventEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { event 1 }

		
		eventEntry OBJECT-TYPE
			SYNTAX EventEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { hwIsmEventSequence }
			::= { eventTable 1 }

		
		EventEntry ::=
			SEQUENCE { 
				hwIsmEventType
					Unsigned32,
				hwIsmEventID
					Counter64,
				hwIsmEventLevel
					Unsigned32,
				hwIsmEventSequence
					Unsigned32,
				hwIsmEventTime
					Unsigned32,
				hwIsmEventRecoveryTime
					Unsigned32,
				hwIsmEventParameter
					OCTET STRING,
				hwIsmEventRowStatus
					RowStatus
			 }

		hwIsmEventType OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 1 }

		
		hwIsmEventID OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 2 }

		
		hwIsmEventLevel OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 3 }

		
		hwIsmEventSequence OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 4 }

		
		hwIsmEventTime OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 5 }

		
		hwIsmEventRecoveryTime OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 6 }

		
		hwIsmEventParameter OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 7 }

		
		hwIsmEventRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { eventEntry 20 }

		
		trapEvent OBJECT IDENTIFIER ::= { event 3 }

		
		hwIsmTrapEventType OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 1 }

		
		hwIsmTrapEventID OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 2 }

		
		hwIsmTrapEventLevel OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 3 }

		
		hwIsmTrapEventSequence OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 4 }

		
		hwIsmTrapEventTime OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 5 }

		
		hwIsmTrapEventRecoveryTime OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 6 }

		
		hwIsmTrapEventParameter OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 7 }

		
		hwIsmTrapEventID32Bit OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 8 }

		
		hwIsmTrapEventTimeStr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 9 }

		
		hwIsmTrapEventRecoveryTimeStr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { trapEvent 10 }

		
		notificationType OBJECT IDENTIFIER ::= { hwISM 4 }

		
		eventType NOTIFICATION-TYPE
			OBJECTS { hwIsmTrapEventType, hwIsmTrapEventID, hwIsmTrapEventLevel, hwIsmTrapEventSequence, hwIsmTrapEventTime, 
				hwIsmTrapEventRecoveryTime, hwIsmTrapEventParameter }
			STATUS current
			DESCRIPTION 
				" "
			::= { notificationType 2 }

		
		isoConformance OBJECT IDENTIFIER ::= { iso 6 }

		
		isoGroups OBJECT IDENTIFIER ::= { isoConformance 1 }

		
		currentObjectGroup OBJECT-GROUP
			OBJECTS { forwardAddrIndex, forwardAddrIP, forwardAddrPort, forwardAddrTrapVer, forwardAddrRowStatus, 
				hwIsmTrapEventType, hwIsmTrapEventID, hwIsmTrapEventLevel, hwIsmTrapEventSequence, hwIsmTrapEventTime, 
				hwIsmTrapEventRecoveryTime, forwardAddrIPNew, forwardAddrTrapType, hwIsmTrapEventID32Bit, hwIsmTrapEventTimeStr, 
				hwIsmTrapEventRecoveryTimeStr, hwIsmTrapEventParameter, hwIsmEventType, hwIsmEventID, hwIsmEventLevel, 
				hwIsmEventSequence, hwIsmEventTime, hwIsmEventRecoveryTime, hwIsmEventParameter, hwIsmEventRowStatus
				 }
			STATUS current
			DESCRIPTION 
				"Enter the description of the created OBJECT-GROUP."
			::= { isoGroups 1 }

		
		currentNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { eventType }
			STATUS current
			DESCRIPTION 
				"Enter the description of the created NOTIFICATION-GROUP."
			::= { isoGroups 2 }

		
		isoCompliances OBJECT IDENTIFIER ::= { isoConformance 2 }

		
		basicCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"Enter the description of the created MODULE-COMPLIANCE."
			MODULE -- this module
				MANDATORY-GROUPS { currentObjectGroup, currentNotificationGroup }
			::= { isoCompliances 1 }

		
	
	END

--
-- ISM-TRAP-MIB.mib
--
