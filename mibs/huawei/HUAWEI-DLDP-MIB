--
-- HUAWEI-DLDP-MIB.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Tuesday, September 23, 2014 at 09:06:22
--

--   =================================================================
-- Copyright (C) 2014 by  HUAWEI TECHNOLOGIES. All rights reserved
-- 
-- Description: HUAWEI DLDP MIB, this mib will maintain information of DLDP 
--              protocol for datacomm product.  
-- Reference:
-- Version:     V2.02
-- History:
--  
--  V2.00 2008-07-15 initial version
-- =================================================================

	HUAWEI-DLDP-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			hwDatacomm			
				FROM HUAWEI-MIB			
			InterfaceIndex			
				FROM IF-MIB			
			EnabledStatus			
				FROM P-BRIDGE-MIB			
			OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			Integer32, Counter32, OBJECT-TYPE, MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			TruthValue, RowStatus, MacAddress, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
--  Revision history
		hwDldpMIB MODULE-IDENTITY 
			LAST-UPDATED "201410210000Z"		-- October 21, 2014 at 00:00 GMT
			ORGANIZATION 
				"Huawei Technologies Co.,Ltd."
			CONTACT-INFO 
				"Huawei Industrial Base
				Bantian, Longgang
				Shenzhen 518129
				People's Republic of China
				Website: http://www.huawei.com
				Email: <EMAIL>
				"
			DESCRIPTION 
				"This file is a DLDP-MIB. It provides the functions such as
				globally enabling or disabling the DLDP protocol, enabling the global
				alarm, clearing statistics on ports and configuring work mode."
			REVISION "201410210000Z"		-- October 21, 2014 at 00:00 GMT
			DESCRIPTION
				"V2.02, hhhhhh."
			REVISION "200807151430Z"		-- July 15, 2008 at 14:30 GMT
			DESCRIPTION
				"V2.00, initial version."
			REVISION "201311300000Z"		-- November 30, 2013 at 00:00 GMT
			DESCRIPTION
				"V2.01, added hwDldpIfTable, hwDldpIfNeighbourTable and hwDldpIfStatisticsTable."
			::= { hwDatacomm 173 }
		
	
--
-- Textual conventions
--
	
--  Textual Convention
		PortIndex ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Each port is uniquely identified by a port number. The port number ranges from 0
				to 575."
			SYNTAX Integer32 (0..575)
			
	
--
-- Node definitions
--
	
--  ============================================================================
-- Node definitions
-- ============================================================================ 
		-- *******.4.1.2011.**********
		hwDldpObjects OBJECT IDENTIFIER ::= { hwDldpMIB 1 }
		
--  ============================================================================
-- 
-- ======================= Objects definitions=================================
-- 
-- ============================================================================  
		-- *******.4.1.2011.**********.1
		hwDldpConfiguration OBJECT IDENTIFIER ::= { hwDldpObjects 1 }
		
		-- *******.4.1.2011.**********.1.1
		hwDldpEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Globally enable or disable the DLDP configuration. If the hwDldpEnable 
				is 1, DLDP is enabled. If the hwDldpEnable is 2, DLDP is disabled. 
				By default, DLDP is disabled."
			DEFVAL { 2 }
			::= { hwDldpConfiguration 1 }
		
		-- *******.4.1.2011.**********.1.2
		hwDldpUnidirectionalShutdown OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(1),
				manual(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"When the device discovers a one-way link, the shutdown mode of port. The modes include
				auto and manual. By default, DLDP is auto."
			DEFVAL { 1 }
			::= { hwDldpConfiguration 2 }
		
		-- *******.4.1.2011.**********.1.3
		hwDldpWorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				enhance(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"It configures the work mode of the DLDP protocol, including normal and enhanced mode.
				By default, the mode is enhanced."
			DEFVAL { 2 }
			::= { hwDldpConfiguration 3 }
		
		-- *******.4.1.2011.**********.1.4
		hwDldpAdvertInterval OBJECT-TYPE
			SYNTAX Integer32 (1..100)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Global interval for sending advertisement packets for the DLDP configuration.
				By default, the interval is 5s."
			DEFVAL { 5 }
			::= { hwDldpConfiguration 4 }
		
		-- *******.4.1.2011.**********.1.5
		hwDelayDownTimer OBJECT-TYPE
			SYNTAX Integer32 (1..5)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Global timeout of DelayDown timer. The value rangs from 1s to 5s,
				By default, the time is 1s."
			DEFVAL { 1 }
			::= { hwDldpConfiguration 5 }
		
		-- *******.4.1.2011.**********.1.6
		hwDldpAuthenMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(1),
				md5(2),
				simple(3),
				sha(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Global authentication mode of the DLDP configuration. It has three authentication
				modes, including none, md5, simple, and sha. By default the authentication mode
				is none."
			DEFVAL { 1 }
			::= { hwDldpConfiguration 6 }
		
		-- *******.4.1.2011.**********.1.7
		hwDldpMd5Password OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..24))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Global md5 password for authentication when authentication is md5."
			::= { hwDldpConfiguration 7 }
		
		-- *******.4.1.2011.**********.1.8
		hwDldpSimplePassword OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Global simple password for authentication when authentication is simple."
			::= { hwDldpConfiguration 8 }
		
		-- *******.4.1.2011.**********.1.9
		hwDldpPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwDldpPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DLDP port configuration table."
			::= { hwDldpConfiguration 9 }
		
		-- *******.4.1.2011.**********.1.9.1
		hwDldpPortEntry OBJECT-TYPE
			SYNTAX HwDldpPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entries of the DLDP port configuration table."
			INDEX { hwDldpPortIndex }
			::= { hwDldpPortTable 1 }
		
		HwDldpPortEntry ::=
			SEQUENCE { 
				hwDldpPortIndex
					PortIndex,
				hwDldpPortStateReset
					TruthValue,
				hwDldpPortState
					INTEGER,
				hwDldpPortLinkState
					INTEGER,
				hwDldpResetStatistics
					TruthValue,
				hwDldpRowStatus
					RowStatus
			 }

		-- *******.4.1.2011.**********.*******
		hwDldpPortIndex OBJECT-TYPE
			SYNTAX PortIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"It describes enabled DLDP port index. Each port is uniquely identified by a port number. It ranges from 0
				to 575."
			::= { hwDldpPortEntry 1 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortStateReset OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"It describes the DLDP status of the reset port."
			DEFVAL { false }
			::= { hwDldpPortEntry 2 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortState OBJECT-TYPE
			SYNTAX INTEGER
				{
				initial(1),
				inactive(2),
				active(3),
				advertisement(4),
				probe(5),
				disable(6),
				delayDown(7),
				loop(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port state has eight states, including initial, inactive, active,
				advertisement, probe, disable, delayDown, and loop."
			::= { hwDldpPortEntry 3 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortLinkState OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port state has two modes, including up and down."
			::= { hwDldpPortEntry 4 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpResetStatistics OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"It clears the statistics of packets received and sent on the current 
				port."
			::= { hwDldpPortEntry 5 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				" Operation of CreateAndGo can be
				used to create a new instance, and operation of Destroy be 
				used to destroy an existent index. But these operations 
				will not take effect if they are not activated by running the
				command of activating or setting mib node of hwDldpEnable."
			::= { hwDldpPortEntry 6 }
		
		-- *******.4.1.2011.**********.1.10
		hwDldpNeighbourTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwDldpNeighbourEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DLDP Neighbour configuration table."
			::= { hwDldpConfiguration 10 }
		
		-- *******.4.1.2011.**********.1.10.1
		hwDldpNeighbourEntry OBJECT-TYPE
			SYNTAX HwDldpNeighbourEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entries of the DLDP Neighbour configuration table."
			INDEX { hwDldpPortIndex, hwDldpNeighbourMacAddr, hwDldpNeighbourPortIndex }
			::= { hwDldpNeighbourTable 1 }
		
		HwDldpNeighbourEntry ::=
			SEQUENCE { 
				hwDldpNeighbourMacAddr
					MacAddress,
				hwDldpNeighbourPortIndex
					Integer32,
				hwDldpNeighbourPortName
					OCTET STRING,
				hwDldpNeighbourState
					INTEGER,
				hwDldpNeighbourAgeTime
					Integer32
			 }

		-- *******.4.1.2011.**********.********
		hwDldpNeighbourMacAddr OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the neighbor information, including
				MAC address of neighbor. The port may detect multiple neighbors."
			::= { hwDldpNeighbourEntry 1 }
		
		-- *******.4.1.2011.**********.********
		hwDldpNeighbourPortIndex OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the port index of the neighbour."
			::= { hwDldpNeighbourEntry 2 }
		
		-- *******.4.1.2011.**********.********
		hwDldpNeighbourPortName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the port name of the neighbour."
			::= { hwDldpNeighbourEntry 3 }
		
		-- *******.4.1.2011.**********.********
		hwDldpNeighbourState OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(1),
				oneWay(2),
				twoWay(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the state of the neighbour, and its value includes unknown, one way, and two way."
			::= { hwDldpNeighbourEntry 4 }
		
		-- *******.4.1.2011.**********.********
		hwDldpNeighbourAgeTime OBJECT-TYPE
			SYNTAX Integer32 (3..300)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the aging time of the neighbor. The aging time
				is three times the interval for sending advertisement packets."
			DEFVAL { 15 }
			::= { hwDldpNeighbourEntry 5 }
		
		-- *******.4.1.2011.**********.1.11
		hwDldpIfTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwDldpIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DLDP interface configuration table."
			::= { hwDldpConfiguration 11 }
		
		-- *******.4.1.2011.**********.1.11.1
		hwDldpIfEntry OBJECT-TYPE
			SYNTAX HwDldpIfEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entries of the DLDP interface configuration table."
			INDEX { hwDldpIfIndex }
			::= { hwDldpIfTable 1 }
		
		HwDldpIfEntry ::=
			SEQUENCE { 
				hwDldpIfIndex
					InterfaceIndex,
				hwDldpIfStateReset
					TruthValue,
				hwDldpIfState
					INTEGER,
				hwDldpIfLinkState
					INTEGER,
				hwDldpIfResetStatistics
					TruthValue,
				hwDldpIfRowStatus
					RowStatus
			 }

		-- *******.4.1.2011.**********.********
		hwDldpIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"It describes enabled DLDP interface index. Each port is uniquely identified by a port number. It ranges from 0
				to 62535."
			::= { hwDldpIfEntry 1 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfStateReset OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"It describes the DLDP status of the reset port."
			DEFVAL { false }
			::= { hwDldpIfEntry 2 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfState OBJECT-TYPE
			SYNTAX INTEGER
				{
				initial(1),
				inactive(2),
				active(3),
				advertisement(4),
				probe(5),
				disable(6),
				delayDown(7),
				loop(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port state has eight states, including initial, inactive, active,
				advertisement, probe, disable, delayDown, and loop."
			::= { hwDldpIfEntry 3 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfLinkState OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port state has two modes, including up and down."
			::= { hwDldpIfEntry 4 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfResetStatistics OBJECT-TYPE
			SYNTAX TruthValue
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"It clears the statistics of packets received and sent on the current 
				port."
			::= { hwDldpIfEntry 5 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				" Operation of CreateAndGo can be
				used to create a new instance, and operation of Destroy be 
				used to destroy an existent index. But these operations 
				will not take effect if they are not activated by running the
				command of activating or setting mib node of hwDldpEnable."
			::= { hwDldpIfEntry 6 }
		
		-- *******.4.1.2011.**********.1.12
		hwDldpIfNeighbourTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwDldpIfNeighbourEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DLDP Neighbour configuration table."
			::= { hwDldpConfiguration 12 }
		
		-- *******.4.1.2011.**********.1.12.1
		hwDldpIfNeighbourEntry OBJECT-TYPE
			SYNTAX HwDldpIfNeighbourEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entries of the DLDP Neighbour configuration table."
			INDEX { hwDldpIfIndex, hwDldpIfNeighbourMacAddr, hwDldpIfNeighbourIfIndex }
			::= { hwDldpIfNeighbourTable 1 }
		
		HwDldpIfNeighbourEntry ::=
			SEQUENCE { 
				hwDldpIfNeighbourMacAddr
					MacAddress,
				hwDldpIfNeighbourIfIndex
					Integer32,
				hwDldpIfNeighbourPortName
					OCTET STRING,
				hwDldpIfNeighbourState
					INTEGER,
				hwDldpIfNeighbourAgeTime
					Integer32
			 }

		-- *******.4.1.2011.**********.********
		hwDldpIfNeighbourMacAddr OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the neighbor information, including
				MAC address of neighbor. The port may detect multiple neighbors."
			::= { hwDldpIfNeighbourEntry 1 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfNeighbourIfIndex OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the port index of the neighbour."
			::= { hwDldpIfNeighbourEntry 2 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfNeighbourPortName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the port name of the neighbour."
			::= { hwDldpIfNeighbourEntry 3 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfNeighbourState OBJECT-TYPE
			SYNTAX INTEGER
				{
				unknown(1),
				oneWay(2),
				twoWay(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the state of the neighbour, and its value includes unknown, one way, and two way."
			::= { hwDldpIfNeighbourEntry 4 }
		
		-- *******.4.1.2011.**********.********
		hwDldpIfNeighbourAgeTime OBJECT-TYPE
			SYNTAX Integer32 (3..300)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"When the activated port detects a neighbor, it can record the aging time of the neighbor. The aging time
				is three times the interval for sending advertisement packets."
			DEFVAL { 15 }
			::= { hwDldpIfNeighbourEntry 5 }
		
		-- *******.4.1.2011.**********.1.13
		hwDldpShaPassword OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Global sha password for authentication when authentication is sha."
			::= { hwDldpConfiguration 13 }
		
		-- *******.4.1.2011.**********.2
		hwDldpStatistics OBJECT IDENTIFIER ::= { hwDldpObjects 2 }
		
		-- *******.4.1.2011.**********.2.1
		hwDldpPortStatisticsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwDldpPortStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DLDP port statics configuration table."
			::= { hwDldpStatistics 1 }
		
		-- *******.4.1.2011.**********.2.1.1
		hwDldpPortStatisticsEntry OBJECT-TYPE
			SYNTAX HwDldpPortStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entries of the table of the packets sent or received on the DLDP port."
			INDEX { hwDldpPortIndex }
			::= { hwDldpPortStatisticsTable 1 }
		
		HwDldpPortStatisticsEntry ::=
			SEQUENCE { 
				hwDldpPortStatisticsTx
					Counter32,
				hwDldpPortStatisticsRxTotal
					Counter32,
				hwDldpPortStatisticsRxError
					Counter32,
				hwDldpPortStatisticsRxLoop
					Counter32,
				hwDldpPortStatisticsRxValid
					Counter32,
				hwDldpPortStatisticsRxAuthenFail
					Counter32
			 }

		-- *******.4.1.2011.**********.*******
		hwDldpPortStatisticsTx OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of packets sent on the activated port."
			::= { hwDldpPortStatisticsEntry 1 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortStatisticsRxTotal OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of packets received on the activated port."
			::= { hwDldpPortStatisticsEntry 2 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortStatisticsRxError OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of error packets received on the activated port."
			::= { hwDldpPortStatisticsEntry 3 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortStatisticsRxLoop OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of loop packets received on the activated port."
			::= { hwDldpPortStatisticsEntry 4 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortStatisticsRxValid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of valid packets received on the activated port."
			::= { hwDldpPortStatisticsEntry 5 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpPortStatisticsRxAuthenFail OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of authentication failure packets received on the activated port."
			::= { hwDldpPortStatisticsEntry 6 }
		
		-- *******.4.1.2011.**********.2.2
		hwDldpIfStatisticsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwDldpIfStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DLDP port statics configuration table."
			::= { hwDldpStatistics 2 }
		
		-- *******.4.1.2011.**********.2.2.1
		hwDldpIfStatisticsEntry OBJECT-TYPE
			SYNTAX HwDldpIfStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entries of the table of the packets sent or received on the DLDP port."
			INDEX { hwDldpIfIndex }
			::= { hwDldpIfStatisticsTable 1 }
		
		HwDldpIfStatisticsEntry ::=
			SEQUENCE { 
				hwDldpIfStatisticsTx
					Counter32,
				hwDldpIfStatisticsRxTotal
					Counter32,
				hwDldpIfStatisticsRxError
					Counter32,
				hwDldpIfStatisticsRxLoop
					Counter32,
				hwDldpIfStatisticsRxValid
					Counter32,
				hwDldpIfStatisticsRxAuthenFail
					Counter32
			 }

		-- *******.4.1.2011.**********.*******
		hwDldpIfStatisticsTx OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of packets sent on the activated port."
			::= { hwDldpIfStatisticsEntry 1 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpIfStatisticsRxTotal OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of packets received on the activated port."
			::= { hwDldpIfStatisticsEntry 2 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpIfStatisticsRxError OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of error packets received on the activated port."
			::= { hwDldpIfStatisticsEntry 3 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpIfStatisticsRxLoop OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of loop packets received on the activated port."
			::= { hwDldpIfStatisticsEntry 4 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpIfStatisticsRxValid OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of valid packets received on the activated port."
			::= { hwDldpIfStatisticsEntry 5 }
		
		-- *******.4.1.2011.**********.*******
		hwDldpIfStatisticsRxAuthenFail OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"It describes the number of authentication failure packets received on the activated port."
			::= { hwDldpIfStatisticsEntry 6 }
		
		-- *******.4.1.2011.**********
		hwDldpPortTrapObjects OBJECT IDENTIFIER ::= { hwDldpMIB 2 }
		
		-- *******.4.1.2011.**********.1
		hwDldpTrapInterfaceIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"It describes the interface index of the activated port that detected one way or found that two way is resumed."
			::= { hwDldpPortTrapObjects 1 }
		
		-- *******.4.1.2011.**********.2
		hwDldpTrapIfName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..64))
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"It describes the interface name of the activated port that detected one way or found that two way is resumed."
			::= { hwDldpPortTrapObjects 2 }
		
		-- *******.4.1.2011.**********.3
		hwDldpTrapFaultReason OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..64))
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"It describes the reason interface fault."
			::= { hwDldpPortTrapObjects 3 }
		
		-- *******.4.1.2011.**********
		hwDldpTraps OBJECT IDENTIFIER ::= { hwDldpMIB 3 }
		
-- -Notifycation        
		-- *******.4.1.2011.**********.1
		hwDldpUnidirectionalLink NOTIFICATION-TYPE
			OBJECTS { hwDldpTrapInterfaceIndex, hwDldpTrapIfName, hwDldpTrapFaultReason }
			STATUS current
			DESCRIPTION 
				"Notify the NMS that the DLDP detected one way. The hwDldpTrapInterfaceIndex node is the interface index."
			::= { hwDldpTraps 1 }
		
		-- *******.4.1.2011.**********.2
		hwDldpLinkResume NOTIFICATION-TYPE
			OBJECTS { hwDldpTrapInterfaceIndex, hwDldpTrapIfName }
			STATUS current
			DESCRIPTION 
				"Notify the NMS that the DLDP detected that unidirectional link was resumed. The hwDldpTrapInterfaceIndex node is interface index."
			::= { hwDldpTraps 2 }
		
		-- *******.4.1.2011.**********.3
		hwDldpLoopDetect NOTIFICATION-TYPE
			OBJECTS { hwDldpTrapInterfaceIndex, hwDldpTrapIfName }
			STATUS current
			DESCRIPTION 
				"Notify the NMS that the DLDP detected Loop State. The hwDldpTrapInterfaceIndex node is the interface index."
			::= { hwDldpTraps 3 }
		
		-- *******.4.1.2011.**********.4
		hwDldpLoopResume NOTIFICATION-TYPE
			OBJECTS { hwDldpTrapInterfaceIndex, hwDldpTrapIfName }
			STATUS current
			DESCRIPTION 
				"Notify the NMS that the DLDP detected Loop State was resumed. The hwDldpTrapInterfaceIndex node is interface index."
			::= { hwDldpTraps 4 }
		
		-- *******.4.1.2011.**********
		hwDldpConformance OBJECT IDENTIFIER ::= { hwDldpMIB 4 }
		
--  ***********************************************************
-- 
-- HAUWEIDLDPMIBCONFORMANCE
-- 
-- ***********************************************************
-- 
		-- *******.4.1.2011.**********.1
		hwDldpCompliances OBJECT IDENTIFIER ::= { hwDldpConformance 1 }
		
--  compliance statements
-- this module
		-- *******.4.1.2011.**********.1.1
		hwDldpCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"The compliance statement for SNMP entities which implement
				the HUAWEI-DLDP-MIB."
			MODULE -- this module
				MANDATORY-GROUPS { hwDldpConfigGroup, hwDldpStatisticsGroup, hwDldpPortGroup, hwDldpPortTrapGroup, hwDldpTrapGroup
					 }
			::= { hwDldpCompliances 1 }
		
		-- *******.4.1.2011.**********.2
		hwDldpGroups OBJECT IDENTIFIER ::= { hwDldpConformance 2 }
		
--  MIB groupings
		-- *******.4.1.2011.**********.2.1
		hwDldpConfigGroup OBJECT-GROUP
			OBJECTS { hwDldpEnable, hwDldpUnidirectionalShutdown, hwDldpWorkMode, hwDldpAdvertInterval, hwDelayDownTimer, 
				hwDldpAuthenMode, hwDldpMd5Password, hwDldpSimplePassword, hwDldpShaPassword }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to configure the
				DLDP implementation behavior.
				This group is mandatory for agents which implement the DLDP."
			::= { hwDldpGroups 1 }
		
		-- *******.4.1.2011.**********.2.2
		hwDldpStatisticsGroup OBJECT-GROUP
			OBJECTS { hwDldpPortStatisticsTx, hwDldpPortStatisticsRxTotal, hwDldpPortStatisticsRxError, hwDldpPortStatisticsRxLoop, hwDldpPortStatisticsRxValid, 
				hwDldpPortStatisticsRxAuthenFail, hwDldpIfStatisticsTx, hwDldpIfStatisticsRxTotal, hwDldpIfStatisticsRxError, hwDldpIfStatisticsRxLoop, 
				hwDldpIfStatisticsRxValid, hwDldpIfStatisticsRxAuthenFail }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to represent DLDP
				statistics.
				This group is mandatory for agents which implement the DLDP
				and have the capability of receiving and transmitting DLDP frames."
			::= { hwDldpGroups 2 }
		
		-- *******.4.1.2011.**********.2.3
		hwDldpPortGroup OBJECT-GROUP
			OBJECTS { hwDldpPortStateReset, hwDldpPortState, hwDldpPortLinkState, hwDldpResetStatistics, hwDldpRowStatus, 
				hwDldpNeighbourPortName, hwDldpNeighbourState, hwDldpNeighbourAgeTime, hwDldpIfStateReset, hwDldpIfState, 
				hwDldpIfLinkState, hwDldpIfResetStatistics, hwDldpIfRowStatus, hwDldpIfNeighbourPortName, hwDldpIfNeighbourState, 
				hwDldpIfNeighbourAgeTime }
			STATUS current
			DESCRIPTION 
				"The collection of objects indicates the information of port."
			::= { hwDldpGroups 3 }
		
		-- *******.4.1.2011.**********.2.4
		hwDldpPortTrapGroup OBJECT-GROUP
			OBJECTS { hwDldpTrapInterfaceIndex, hwDldpTrapIfName }
			STATUS current
			DESCRIPTION 
				"The collection of objects indicates that the activated port index detected one way or found that two way is resumed."
			::= { hwDldpGroups 4 }
		
		-- *******.4.1.2011.**********.2.5
		hwDldpTrapGroup NOTIFICATION-GROUP
			NOTIFICATIONS { hwDldpUnidirectionalLink, hwDldpLinkResume, hwDldpLoopDetect, hwDldpLoopResume }
			STATUS current
			DESCRIPTION 
				"The collection of notifications used to indicate that the HUAWEI-DLDP-MIB
				data is consistent and indicate the general status information.
				This group is mandatory for agents which implement the DLDP
				and have the capability of receiving DLDP frames."
			::= { hwDldpGroups 5 }
		
	
	END

--
-- HUAWEI-DLDP-MIB.mib
--
