-- Copyright (C) 2017 by HUAWEI TECHNOLOGIES. All rights reserved.
--
-- HUAWEI-CLOCK-MIB.2.17.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 4.0 Build 341
-- Wednesday, March 20, 2013 at 16:03:18
-- Version: V2.35
--

	HUAWEI-CLOCK-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			PhysicalIndex			
				FROM ENTITY-MIB			
			hwDatacomm			
				FROM HUAWEI-MIB			
			InterfaceIndex			
				FROM IF-MIB			
			EnabledStatus			
				FROM P-BRIDGE-MIB			
			OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP
				FROM SNMPv2-CO<PERSON>			
			Integer32, OBJECT-TYPE, NOTIFICATION-TYPE, MODULE-IDENTITY
				FROM SNMPv2-SMI			
			RowStatus			
				FROM SNMPv2-TC;
	
	
		-- *******.4.1.2011.5.25.186
		hwClockMIB MODULE-IDENTITY 
                                                          LAST-UPDATED "201707120000Z"          -- Jul 12, 2017 at 11:00 GMT  
			ORGANIZATION 
				"Huawei Technologies Co.,Ltd.
				"
			CONTACT-INFO 
				"Huawei Industrial Base
				Bantian, Longgang
				Shenzhen 518129
				People's Republic of China
				Website: http://www.huawei.com
				Email: <EMAIL>
				"
                                                     DESCRIPTION
		                  "Add hwClockGnssModelChange, hwClockPortName,  hwClockGnssModel, hwClockOldGnssModel ."
                                                                        REVISION
            			"201707120000Z" -- Jul 12, 2017

                                                    DESCRIPTION
		                  "Add hwClockPortNonSupport, hwClockPortNonSupportResume, hwClockPortIfIndex ."
                                                                        REVISION
            			"201705230000Z" -- May 23, 2017

                                                     DESCRIPTION
		                  "Add hwClockBoardFreqSwitchEnable, hwClockSrcCfgFreqDeviationRecover."
                                                                        REVISION
            			"201701200000Z" -- Jan 20, 2017
          
                                                     DESCRIPTION
		                    "Add hwClockFMSwitch, hwClockFMSwitchResume, hwClockSyncBad, hwClockSyncBadResume."
                                                                        REVISION
            			"201608270000Z" -- Aug 27, 2016
                                                               DESCRIPTION
				"The MIB contains objects of module clock management and 1588 interface."
                                                                        REVISION
            			"201607210000Z" -- July 21, 2016                                                               
            		                     DESCRIPTION
            			"Add mib hwClockCesSerialCfgTable." 
                                                                        REVISION
                                                               "201602040000Z" -- Feb 04, 2016
			DESCRIPTION
			"Modify hwClockClusterNewTopoType, hwClockClusterNewTopoLinkType."
			        REVISION
            		                     "201512170000Z" -- Dec 17, 2015
            		                     DESCRIPTION
            			"Add hwClockSmartPlugInFlag, hwClockSmartClockLatitude, hwClockSmartClockLongitude, hwClockSmartClockAltitude, hwClockSmartClockWorkMode, hwClockSmartClockLeapSecond, hwClockSmartClockSatelliteCno, hwClockSmartClockGpsTime."
                                                                        REVISION
            			"201512010000Z" -- Dec 1, 2015
            		                     DESCRIPTION
            			"Modify hwClockSrcCfgSourceTypeIndex." 
                                                                        REVISION
            			"201511170000Z" -- Nov 17, 2015
            		                     DESCRIPTION
            			"Modify hwClockChassisId, hwClockPllId, hwClockAttributeOutThreshold, hwClockAttributeOutValue, hwClockCurSourceName." 
                                                                        REVISION
            			"201510220000Z" -- Oct 22, 2015
            		                     DESCRIPTION
            			"Modify hwClockSmartClockPtpPriority1,hwClockSmartClockPtpPriority2,hwClockSmartClockPtpDomain." 	
	                                                        REVISION
            			"201510200000Z" -- Oct 20, 2015
            		                     DESCRIPTION
            			"Add hwClockClusterNewSyncType, hwClockClusterNewTopoType, hwClockClusterNewTopoLinkType, hwClockClusterNewTopoStatus." 				 
                                                                                     REVISION
            			"201510160000Z" -- Oct 16, 2015
            		                     DESCRIPTION
            			"Add mib hwClockSmartClockPortCfgTableRow. AirBits changed to SmartClock. " 
			                     REVISION
				"201510090000Z" -- Oct 9, 2015
            		                     DESCRIPTION
            			"Add mib hwClockSourceSsmChange, hwClockOldSourceSsm, hwClockNewSourceSsm . " 
            			                    REVISION
            			"201509180000Z" -- Sept 18, 2015
            		                     DESCRIPTION
            			"Modified hwClockAirBitsPortCfgTable. " 
	                                           REVISION
            			"201509160000Z" -- Sept 16, 2015
            		                     DESCRIPTION
            			"Add mib hwClockAirBitsPortCfgTable. " 
	                                         REVISION
            			"201411290000Z" -- Nov 29, 2014
            		                     DESCRIPTION
            			"Modify alarm hwClockSourceInputBelowThreshold, hwClockSourceInputBelowThresholdResume." 
	                                         REVISION
            			"201411030000Z" -- Nov 3, 2014
            		                     DESCRIPTION
            			"Add alarm hwClockCesDcrMasterPwChange, hwClockCesDcrLockFail,hwClockCesDcrLockFailResume,hwClockSsmPktLos,hwClockSsmPktLosResume and add mib hwClockCesDcrSlot,hwClockCesDcrCard,hwClockCesDcrDomain,hwClockCesDcrOldMasterPwName,hwClockCesDcrNewMasterPwName,hwClockCesDcrLockState,hwClockCesMode" 
	                                         REVISION
            			"201408130000Z" -- Aug 13, 2014
            		                     DESCRIPTION
            			"Add alarm hwClockSourceInputBelowThreshold, hwClockSourceInputBelowThresholdResume." 
	                                         REVISION
            			"201404210000Z" -- Apr 21, 2014
            		                     DESCRIPTION
            			"Add alarm hwClockClusterTopoFail, hwClockClusterTopoFailResume and table hwClockClusterTopoTable." 
	                                         REVISION
            			"201401070000Z" -- Jan 07, 2014
            		                     DESCRIPTION
            			"Edit  the range of hwClockCesAcrDomianInfoDomain." 
	                                         REVISION
            			"201311120000Z" -- Nov 12, 2013
            		                     DESCRIPTION
            			"Add mib hwClockBitsCfgFrameFormat, hwClockAttributeLtiSquelch and hwClockAttributeInputThreshold." 
                                                               REVISION
            			"201310310000Z" -- Oct 31, 2013
            		                     DESCRIPTION
           			"Edit  the range of  hwClockCesAcrRecoveryDomain."
                                                               REVISION
            			"201305230000Z" -- May 23, 2013
            		                     DESCRIPTION
            			"Re-edit  the range of  some nodes."
                                                               REVISION
            			"201305140000Z" -- May 14, 2013
            		                     DESCRIPTION
            			"Re-edit the default values of hwClockAttributeTodProtocol node." 
			REVISION
            			"201303200000Z" -- March 20, 2013
            		                     DESCRIPTION
            			"Some errors have been modified in current version and some nodes have been added into the current version." 
			::= { hwDatacomm 186 }  

	
	
--
-- Node definitions
--
	
		-- *******.4.1.2011.**********
		hwClockManageObjects OBJECT IDENTIFIER ::= { hwClockMIB 1 }

		
		-- *******.4.1.2011.**********.1
		hwClockGlobalObjects OBJECT IDENTIFIER ::= { hwClockManageObjects 1 }

		
		-- *******.4.1.2011.**********.1.1
		hwClockSourceEthClkEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The flag indicates that the ethernet clock is globally enabled."
			::= { hwClockGlobalObjects 1 }

		
		-- *******.4.1.2011.**********.1.2
		hwClockSourceSsmUnknown OBJECT-TYPE
			SYNTAX INTEGER
				{
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The quality level of unknown SSM."
			DEFVAL { dnu }
			::= { hwClockGlobalObjects 2 }

		
		-- *******.4.1.2011.**********.1.3
		hwClockSourceSysClkWorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				trace(1),
				hold(2),
				freeoscillate(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The work mode of system clock."
			::= { hwClockGlobalObjects 3 }

		
		-- *******.4.1.2011.**********.1.4
		hwClockSourceForceCloseEnableStatus OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable status of export forced close."
			::= { hwClockGlobalObjects 4 }

		
		-- *******.4.1.2011.**********.1.5
		hwClockSourceSsmControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				on(1),
				off(2),
				extend(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The flag whether SSM is concerned with the clock source selection."
			::= { hwClockGlobalObjects 5 }

		
		-- *******.4.1.2011.**********.1.6
		hwClockSourceHoldMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				hold24Hours(1),
				holdForever(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The hold mode of clock source."
			::= { hwClockGlobalObjects 6 }

		
		-- *******.4.1.2011.**********.1.7
		hwClockSourceFreqCheckEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable flag of frequency check."
			::= { hwClockGlobalObjects 7 }

		
		-- *******.4.1.2011.**********.1.8
		hwClockSourceFreqCheckLeftRange OBJECT-TYPE
			SYNTAX Integer32 (50..1000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The left range of frequency check, unit in 0.01ppm."
			::= { hwClockGlobalObjects 8 }

		
		-- *******.4.1.2011.**********.1.9
		hwClockSourceFreqCheckRightRange OBJECT-TYPE
			SYNTAX Integer32 (50..1000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The right range of frequency check, unit in 0.01ppm."
			::= { hwClockGlobalObjects 9 }

		
		-- *******.4.1.2011.**********.1.10
		hwClockSourceRetrieveMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				retrieve(1),
				noRetrieve(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The retrieve mode of clock source."
			::= { hwClockGlobalObjects 10 }

		
		-- *******.4.1.2011.**********.1.11
		hwClockTimeUsedSource OBJECT-TYPE
			SYNTAX INTEGER
				{
				srcDclsTimeBit0(1),
				srcDclsTimeBit1(2),
				src1ppsTodBit0(3),
				src1ppsTodBit1(4),
				srcPtp(5),
				srcFreeRun(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The clock time used source."
			::= { hwClockGlobalObjects 11 }

		
		-- *******.4.1.2011.**********.1.12
		hwClockExtTimeInputType OBJECT-TYPE
			SYNTAX INTEGER
				{
				typeDclsTime(1),
				type1ppsTodRs232(2),
				type1ppsTodGps(3),
				typeNone(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The input time type of clock extern time."
			::= { hwClockGlobalObjects 12 }

		
		-- *******.4.1.2011.**********.1.13
		hwClockExtTimeOutputType OBJECT-TYPE
			SYNTAX INTEGER
				{
				typeDclsTime(1),
				type1ppsTodRs232(2),
				type1ppsTodGps(3),
				typeNone(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The output time type of clock extern time."
			::= { hwClockGlobalObjects 13 }

		
		-- *******.4.1.2011.**********.1.14
		hwClockAlarmThresholdFrequencyOffset OBJECT-TYPE
			SYNTAX Integer32 (10..92)
                                                     UNITS "100ppb"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Threshold of clock alarm."
                                                                DEFVAL { 92 }
			::= { hwClockGlobalObjects 14 }

		
		-- *******.4.1.2011.**********.1.15
		hwClockFrequencyOffsetMax OBJECT-TYPE
			SYNTAX Integer32
                                                     UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The max offset of clock frequency."
			::= { hwClockGlobalObjects 15 }

		
		-- *******.4.1.2011.**********.1.16
		hwClockFrequencyOffsetMin OBJECT-TYPE
			SYNTAX Integer32
                                                     UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The min offset of clock frequency."
			::= { hwClockGlobalObjects 16 }

		
		-- *******.4.1.2011.**********.1.17
		hwClockFrequencyOffsetMean OBJECT-TYPE
			SYNTAX Integer32
                                                     UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The mean offset of clock frequency."
			::= { hwClockGlobalObjects 17 }

		
		-- *******.4.1.2011.**********.1.18
		hwClockFrequencyOffset OBJECT-TYPE
			SYNTAX Integer32
                                                     UNITS "ppb"
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The current offset of clock frequency."
			::= { hwClockGlobalObjects 18 }

		-- *******.4.1.2011.**********.1.19
		hwClockBoardFreqSwitchEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable flag of board frequency deviation swtich."
			::= { hwClockGlobalObjects 19 }
		
		-- *******.4.1.2011.**********.2
		hwClockSourceSelTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockSourceSelEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The system clock source selection table."
			::= { hwClockManageObjects 2 }

		
		-- *******.4.1.2011.**********.2.1
		hwClockSourceSelEntry OBJECT-TYPE
			SYNTAX HwClockSourceSelEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of system clock source selection table."
			INDEX { hwClockSourceSelChassisIndex, hwClockSourceSelType }
			::= { hwClockSourceSelTable 1 }

		
		HwClockSourceSelEntry ::=
			SEQUENCE { 
				hwClockSourceSelChassisIndex
					PhysicalIndex,
				hwClockSourceSelType
					Integer32,
				hwClockSourceSelMode
					INTEGER,
				hwClockSourceSelSourceId
					Integer32
			 }

		-- *******.4.1.2011.**********.2.1.1
		hwClockSourceSelChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The chassis index."
			::= { hwClockSourceSelEntry 1 }

		
		-- *******.4.1.2011.**********.2.1.2
		hwClockSourceSelType OBJECT-TYPE
			SYNTAX Integer32 (1..100)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The select type."
			::= { hwClockSourceSelEntry 2 }

		
		-- *******.4.1.2011.**********.2.1.3
		hwClockSourceSelMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(1),
				manual(2),
				force(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The mode of clock source selection."
			::= { hwClockSourceSelEntry 3 }

		
		-- *******.4.1.2011.**********.2.1.4
		hwClockSourceSelSourceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The source ID of the clock traced."
			::= { hwClockSourceSelEntry 4 }

		
		-- *******.4.1.2011.**********.3
		hwClockSourceCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockSourceCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The clock source config table."
			::= { hwClockManageObjects 3 }

		
		-- *******.4.1.2011.**********.3.1
		hwClockSourceCfgEntry OBJECT-TYPE
			SYNTAX HwClockSourceCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of clock source config table."
			INDEX { hwClockCfgChassisIndex, hwClockCfgSourceIndex }
			::= { hwClockSourceCfgTable 1 }

		
		HwClockSourceCfgEntry ::=
			SEQUENCE { 
				hwClockCfgChassisIndex
					PhysicalIndex,
				hwClockCfgSourceIndex
					Integer32,
				hwClockCfgSourceId
					Integer32,
				hwClockCfgSourceDescr
					OCTET STRING,
				hwClockCfgWtrTime
					Integer32,
				hwClockCfgBadDetect
					EnabledStatus,
				hwClockCfgSystemPriority
					Integer32,
				hwClockCfgBits0Priority
					Integer32,
				hwClockCfgBits1Priority
					Integer32,
				hwClockCfgSystemLockOut
					Integer32,
				hwClockCfgBits0LockOut
					Integer32,
				hwClockCfgBits1LockOut
					Integer32,
				hwClockCfgSourceSsm
					INTEGER,
				hwClockCfgSourceSsmSetMode
					INTEGER,
				hwClockCfgExportEnableStatus
					EnabledStatus,
				hwClockCfgSwiEnableStatus
					EnabledStatus,
				hwClockCfgSourceState
					INTEGER,
				hwClockCfgSsmThreshold
					INTEGER,
				hwClockCfgSourceS1Id
					Integer32,
				hwClockCfgFreqCheckResult
					Integer32,
				hwClockCfgHoldOffTime
					Integer32,
				hwClockCfgPriRvtEnableStatus
					EnabledStatus,
				hwClockCfgSwitchCondition
					INTEGER,
				hwClockCfgClkSourceType
					INTEGER
			 }

		-- *******.4.1.2011.**********.3.1.1
		hwClockCfgChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The index of the chassis whitch the clock source belongs to."
			::= { hwClockSourceCfgEntry 1 }

		
		-- *******.4.1.2011.**********.3.1.2
		hwClockCfgSourceIndex OBJECT-TYPE
			SYNTAX Integer32 (1..20)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The clock source index."
			::= { hwClockSourceCfgEntry 2 }

		
		-- *******.4.1.2011.**********.3.1.3
		hwClockCfgSourceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The clock source ID."
			::= { hwClockSourceCfgEntry 3 }

		
		-- *******.4.1.2011.**********.3.1.4
		hwClockCfgSourceDescr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The clock source description."
			::= { hwClockSourceCfgEntry 4 }

		
		-- *******.4.1.2011.**********.3.1.5
		hwClockCfgWtrTime OBJECT-TYPE
			SYNTAX Integer32 (0..12)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The waiting for restore time of clock source."
			::= { hwClockSourceCfgEntry 5 }

		
		-- *******.4.1.2011.**********.3.1.6
		hwClockCfgBadDetect OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable status of clock source bad detecting."
			::= { hwClockSourceCfgEntry 6 }

		
		-- *******.4.1.2011.**********.3.1.7
		hwClockCfgSystemPriority OBJECT-TYPE
			SYNTAX Integer32 (-1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The priority of system clock source."
			::= { hwClockSourceCfgEntry 7 }

		
		-- *******.4.1.2011.**********.3.1.8
		hwClockCfgBits0Priority OBJECT-TYPE
			SYNTAX Integer32 (-1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The priority of BITS0 clock source."
			::= { hwClockSourceCfgEntry 8 }

		
		-- *******.4.1.2011.**********.3.1.9
		hwClockCfgBits1Priority OBJECT-TYPE
			SYNTAX Integer32 (-1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The priority of BITS1 clock source."
			::= { hwClockSourceCfgEntry 9 }

		
		-- *******.4.1.2011.**********.3.1.10
		hwClockCfgSystemLockOut OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The lock out of system clock source."
			::= { hwClockSourceCfgEntry 10 }

		
		-- *******.4.1.2011.**********.3.1.11
		hwClockCfgBits0LockOut OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The lock out of BITS0 clock source."
			::= { hwClockSourceCfgEntry 11 }

		
		-- *******.4.1.2011.**********.3.1.12
		hwClockCfgBits1LockOut OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The lock out of BITS1 clock source."
			::= { hwClockSourceCfgEntry 12 }

		
		-- *******.4.1.2011.**********.3.1.13
		hwClockCfgSourceSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				ssmPrc(1),
				ssmSsut(2),
				ssmSsul(3),
				ssmSec(4),
				ssmDnu(5),
				ssmUnknown(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The SSM quality of clock source."
			::= { hwClockSourceCfgEntry 13 }

		
		-- *******.4.1.2011.**********.3.1.14
		hwClockCfgSourceSsmSetMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(1),
				auto(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The set mode of SSM."
			::= { hwClockSourceCfgEntry 14 }

		
		-- *******.4.1.2011.**********.3.1.15
		hwClockCfgExportEnableStatus OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable status of clock source export."
			::= { hwClockSourceCfgEntry 15 }

		
		-- *******.4.1.2011.**********.3.1.16
		hwClockCfgSwiEnableStatus OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"he enable status of clock source switch."
			::= { hwClockSourceCfgEntry 16 }

		
		-- *******.4.1.2011.**********.3.1.17
		hwClockCfgSourceState OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				abnormal(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The state of clock source."
			::= { hwClockSourceCfgEntry 17 }

		
		-- *******.4.1.2011.**********.3.1.18
		hwClockCfgSsmThreshold OBJECT-TYPE
			SYNTAX INTEGER
				{
				qlDnu(1),
				qlSec(2),
				qlSsub(3),
				qlSsua(4),
				qlPrc(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The SSM quality level threshold of clock source."
			::= { hwClockSourceCfgEntry 18 }

		
		-- *******.4.1.2011.**********.3.1.19
		hwClockCfgSourceS1Id OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The S1 byte of the clock."
			::= { hwClockSourceCfgEntry 19 }

		
		-- *******.4.1.2011.**********.3.1.20
		hwClockCfgFreqCheckResult OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The result of frequency check, unit in 0.01ppm."
			::= { hwClockSourceCfgEntry 20 }

		
		-- *******.4.1.2011.**********.3.1.21
		hwClockCfgHoldOffTime OBJECT-TYPE
			SYNTAX Integer32 (3..18)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The hold off time of clock, unit in 100ms."
			::= { hwClockSourceCfgEntry 21 }

		
		-- *******.4.1.2011.**********.3.1.22
		hwClockCfgPriRvtEnableStatus OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable status of switch according priority."
			::= { hwClockSourceCfgEntry 22 }

		
		-- *******.4.1.2011.**********.3.1.23
		hwClockCfgSwitchCondition OBJECT-TYPE
			SYNTAX INTEGER
				{
				noSwitch(1),
				switch(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The condition of clock switch."
			::= { hwClockSourceCfgEntry 23 }

		
		-- *******.4.1.2011.**********.3.1.24
		hwClockCfgClkSourceType OBJECT-TYPE
			SYNTAX INTEGER
				{
				bits(1),
				line(2),
				inner(3),
				system(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of clock source."
			::= { hwClockSourceCfgEntry 24 }

		
		-- *******.4.1.2011.**********.4
		hwClockBitsCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockBitsCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The clock bits congfig table."
			::= { hwClockManageObjects 4 }

		
		-- *******.4.1.2011.**********.4.1
		hwClockBitsCfgEntry OBJECT-TYPE
			SYNTAX HwClockBitsCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of clock bits congfig table."
			INDEX { hwClockBitsCfgChassisIndex, hwClockBitsCfgBitsIndex }
			::= { hwClockBitsCfgTable 1 }

		
		HwClockBitsCfgEntry ::=
			SEQUENCE { 
				hwClockBitsCfgChassisIndex
					PhysicalIndex,
				hwClockBitsCfgBitsIndex
					Integer32,
				hwClockBitsCfgName
					OCTET STRING,
				hwClockBitsCfgBitsPortType
					INTEGER,
				hwClockBitsCfgBitsType
					INTEGER,
				hwClockBitsCfgDirection
					INTEGER,
				hwClockBitsCfgRecvSaBit
					INTEGER,
				hwClockBitsCfgSendSaBit
					INTEGER,
				hwClockBitsCfgForceOutS1
					INTEGER,
				hwClockBitsCfgSaBit
					INTEGER,
				hwClockBitsCfgInputMode
					INTEGER,
				hwClockBitsCfgOutputMode
					INTEGER,
				hwClockBitsCfgInvalidCond
					INTEGER,
				hwClockBitsCfgSourceId
					Integer32,
				hwClockBitsCfgTodSignal
					INTEGER,
				hwClockBitsCfgFrameFormat
					INTEGER
			 }

		-- *******.4.1.2011.**********.4.1.1
		hwClockBitsCfgChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The index of the chassis whitch the clock source belongs to."
			::= { hwClockBitsCfgEntry 1 }

		
		-- *******.4.1.2011.**********.4.1.2
		hwClockBitsCfgBitsIndex OBJECT-TYPE
			SYNTAX Integer32 (1..10)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The index of BITS clock."
			::= { hwClockBitsCfgEntry 2 }

		
		-- *******.4.1.2011.**********.4.1.3
		hwClockBitsCfgName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The name of clock."
			::= { hwClockBitsCfgEntry 3 }

		
		-- *******.4.1.2011.**********.4.1.4
		hwClockBitsCfgBitsPortType OBJECT-TYPE
			SYNTAX INTEGER
				{
				portRj45(1),
				portSMB(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The BITS port type."
			::= { hwClockBitsCfgEntry 4 }

		
		-- *******.4.1.2011.**********.4.1.5
		hwClockBitsCfgBitsType OBJECT-TYPE
			SYNTAX INTEGER
				{
				type2Mbps(0),
				type2Mhz(1),
				typeDclsTime(2),
				type1ppsTod(3),
				none(4),
				type1544Mbps(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The BITS type."
			::= { hwClockBitsCfgEntry 5 }

		
		-- *******.4.1.2011.**********.4.1.6
		hwClockBitsCfgDirection OBJECT-TYPE
			SYNTAX INTEGER
				{
				in(1),
				out(2),
				inAndOut(3),
				none(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The direction of BITS."
			::= { hwClockBitsCfgEntry 6 }

		
		-- *******.4.1.2011.**********.4.1.7
		hwClockBitsCfgRecvSaBit OBJECT-TYPE
			SYNTAX INTEGER
				{
				sa4(4),
				sa5(5),
				sa6(6),
				sa7(7),
				sa8(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The received SA bit."
			::= { hwClockBitsCfgEntry 7 }

		
		-- *******.4.1.2011.**********.4.1.8
		hwClockBitsCfgSendSaBit OBJECT-TYPE
			SYNTAX INTEGER
				{
				sa4(4),
				sa5(5),
				sa6(6),
				sa7(7),
				sa8(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The sent SA bit."
			::= { hwClockBitsCfgEntry 8 }

		
		-- *******.4.1.2011.**********.4.1.9
		hwClockBitsCfgForceOutS1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The S1 byte of forcing out."
			::= { hwClockBitsCfgEntry 9 }

		
		-- *******.4.1.2011.**********.4.1.10
		hwClockBitsCfgSaBit OBJECT-TYPE
			SYNTAX INTEGER
				{
				sa4(4),
				sa5(5),
				sa6(6),
				sa7(7),
				sa8(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The SA bit of SSM information."
			::= { hwClockBitsCfgEntry 10 }

		
		-- *******.4.1.2011.**********.4.1.11
		hwClockBitsCfgInputMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				clk2MBits(0),
				clk2MHz(1),
				dclsTime(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The input mode of clock source."
			::= { hwClockBitsCfgEntry 11 }

		
		-- *******.4.1.2011.**********.4.1.12
		hwClockBitsCfgOutputMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				clk2MBits(0),
				clk2MHz(1),
				dclsTime(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The output mode of clock source."
			::= { hwClockBitsCfgEntry 12 }

		
		-- *******.4.1.2011.**********.4.1.13
		hwClockBitsCfgInvalidCond OBJECT-TYPE
			SYNTAX INTEGER
				{
				no(1),
				ais(2),
				lof(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The invalid condition of clock source."
			::= { hwClockBitsCfgEntry 13 }

		
		-- *******.4.1.2011.**********.4.1.14
		hwClockBitsCfgSourceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The clock source ID."
			::= { hwClockBitsCfgEntry 14 }

		
		-- *******.4.1.2011.**********.4.1.15
		hwClockBitsCfgTodSignal OBJECT-TYPE
			SYNTAX INTEGER
				{
				nmea(1),
				ubx(2),
				none(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The tod signal of clock source."
			::= { hwClockBitsCfgEntry 15 }

		-- *******.4.1.2011.**********.4.1.16
		hwClockBitsCfgFrameFormat OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				pcm30nocrc(1),
				pcm30crc(2),
				pcm31nocrc(3),
				pcm31crc(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Encoding type and frame check format of the extern clock port."
			DEFVAL { 4 }
			::= { hwClockBitsCfgEntry 16 }
		
		-- *******.4.1.2011.**********.5
		hwClockPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The clock port config table."
			::= { hwClockManageObjects 5 }

		
		-- *******.4.1.2011.**********.5.1
		hwClockPortCfgEntry OBJECT-TYPE
			SYNTAX HwClockPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of clock port config table."
			INDEX { hwClockPortCfgIfIndex }
			::= { hwClockPortCfgTable 1 }

		
		HwClockPortCfgEntry ::=
			SEQUENCE { 
				hwClockPortCfgIfIndex
					InterfaceIndex,
				hwClockPortCfgLeftFramePri
					Integer32,
				hwClockPortCfgRightFramePri
					Integer32,
				hwClockPortCfgForceOutS1
					Integer32
			 }

		-- *******.4.1.2011.**********.5.1.1
		hwClockPortCfgIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The interface index."
			::= { hwClockPortCfgEntry 1 }

		
		-- *******.4.1.2011.**********.5.1.2
		hwClockPortCfgLeftFramePri OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The clock priority of left frame."
			::= { hwClockPortCfgEntry 2 }

		
		-- *******.4.1.2011.**********.5.1.3
		hwClockPortCfgRightFramePri OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The clock priority of right frame."
			::= { hwClockPortCfgEntry 3 }

		
		-- *******.4.1.2011.**********.5.1.4
		hwClockPortCfgForceOutS1 OBJECT-TYPE
			SYNTAX Integer32 (-1..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The S1 byte of forcing out."
			::= { hwClockPortCfgEntry 4 }

		
		-- *******.4.1.2011.**********.6
		hwClockLineClkCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockLineClkCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The line clock config table."
			::= { hwClockManageObjects 6 }

		
		-- *******.4.1.2011.**********.6.1
		hwClockLineClkCfgEntry OBJECT-TYPE
			SYNTAX HwClockLineClkCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of line clock config table."
			INDEX { hwClockLineClkCfgChassisIndex, hwClockLineClkCfgSlotIndex }
			::= { hwClockLineClkCfgTable 1 }

		
		HwClockLineClkCfgEntry ::=
			SEQUENCE { 
				hwClockLineClkCfgChassisIndex
					PhysicalIndex,
				hwClockLineClkCfgSlotIndex
					Integer32,
				hwClockLineClkCfgCardId
					Integer32,
				hwClockLineClkCfgPortId
					Integer32,
				hwClockLineClkCfgRecvS1
					Integer32,
				hwClockLineClkCfgSendS1
					Integer32,
				hwClockLineCfgSoureId
					Integer32
			 }

		-- *******.4.1.2011.**********.6.1.1
		hwClockLineClkCfgChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The chassis index."
			::= { hwClockLineClkCfgEntry 1 }

		
		-- *******.4.1.2011.**********.6.1.2
		hwClockLineClkCfgSlotIndex OBJECT-TYPE
			SYNTAX Integer32 (1..200)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The slot index of the line clock."
			::= { hwClockLineClkCfgEntry 2 }

		
		-- *******.4.1.2011.**********.6.1.3
		hwClockLineClkCfgCardId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The card index witch is seleced to provide line clock."
			::= { hwClockLineClkCfgEntry 3 }

		
		-- *******.4.1.2011.**********.6.1.4
		hwClockLineClkCfgPortId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The port index witch is seleced to provide line clock."
			::= { hwClockLineClkCfgEntry 4 }

		
		-- *******.4.1.2011.**********.6.1.5
		hwClockLineClkCfgRecvS1 OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The S1 byte value received."
			::= { hwClockLineClkCfgEntry 5 }

		
		-- *******.4.1.2011.**********.6.1.6
		hwClockLineClkCfgSendS1 OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The S1 byte value sent."
			::= { hwClockLineClkCfgEntry 6 }

		
		-- *******.4.1.2011.**********.6.1.7
		hwClockLineCfgSoureId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwClockLineClkCfgEntry 7 }

		
		-- *******.4.1.2011.**********.7
		hwClockTrapOid OBJECT IDENTIFIER ::= { hwClockManageObjects 7 }

		
		-- *******.4.1.2011.**********.7.1
		hwClockLastSourceName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The last clock source name."
			::= { hwClockTrapOid 1 }

		
		-- *******.4.1.2011.**********.7.2
		hwClockCurSourceName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The current clock source name."
			::= { hwClockTrapOid 2 }

		
		-- *******.4.1.2011.**********.7.3
		hwClockSourceOldLockMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				freeRun(0),
				fastLock(1),
				lock(2),
				hold(3),
				freeRunJudge(16),
				holdJudge(19)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The old lock mode of clock source."
			::= { hwClockTrapOid 3 }

		
		-- *******.4.1.2011.**********.7.4
		hwClockChassisId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The chassis ID."
			::= { hwClockTrapOid 4 }

		
		-- *******.4.1.2011.**********.7.5
		hwClockOldSourceState OBJECT-TYPE
			SYNTAX INTEGER
				{
				initial(0),
				normal(1),
				abnormal(2),
				wtr(3),
				holdoff(4)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The old state of clock source."
			::= { hwClockTrapOid 5 }

		
		-- *******.4.1.2011.**********.7.6
		hwClockPllId OBJECT-TYPE
			SYNTAX INTEGER
				{
				system(1),
				sync2M1(2),
				sync2M2(3)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The id of pll."
			::= { hwClockTrapOid 6 }

		
		-- *******.4.1.2011.**********.7.7
		hwClockAttributeOutValue OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The current output value."
			::= { hwClockTrapOid 7 }

		
		-- *******.4.1.2011.**********.7.8
		hwClockCesAcrSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The slot ID of CES ACR clock source."
			::= { hwClockTrapOid 8 }

		
		-- *******.4.1.2011.**********.7.9
		hwClockCesAcrCard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The card ID of CES ACR clock source."
			::= { hwClockTrapOid 9 }

		
		-- *******.4.1.2011.**********.7.10
		hwClockCesAcrDomain OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The recovery domain value of CES ACR clock source."
			::= { hwClockTrapOid 10 }

		
		-- *******.4.1.2011.**********.7.11
		hwClockCesAcrOldMasterPwName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The master pw SerialPort name of CES ACR old clock source."
			::= { hwClockTrapOid 11 }

		
		-- *******.4.1.2011.**********.7.12
		hwClockCesAcrNewMasterPwName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The master pw SerialPort name of CES ACR new clock source."
			::= { hwClockTrapOid 12 }

		
		-- *******.4.1.2011.**********.7.13
		hwClockCesAcrLockState OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The lock state of the CES ACR."
			::= { hwClockTrapOid 13 }

		
		-- *******.4.1.2011.**********.7.14
		hwClockCesDcrSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The slot ID of CES DCR clock source."
			::= { hwClockTrapOid 14 }

		
		-- *******.4.1.2011.**********.7.15
		hwClockCesDcrCard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The card ID of CES DCR clock source."
			::= { hwClockTrapOid 15 }


		-- *******.4.1.2011.**********.7.16
		hwClockCesDcrDomain OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The recovery domain value of CES DCR clock source."
			::= { hwClockTrapOid 16 }

		
		-- *******.4.1.2011.**********.7.17
		hwClockCesDcrOldMasterPwName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The master pw SerialPort name of CES DCR old clock source."
			::= { hwClockTrapOid 17 }

		
		-- *******.4.1.2011.**********.7.18
		hwClockCesDcrNewMasterPwName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The master pw SerialPort name of CES DCR new clock source."
			::= { hwClockTrapOid 18 }


		-- *******.4.1.2011.**********.7.19
		hwClockCesDcrLockState OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The lock state of the CES DCR."
			::= { hwClockTrapOid 19 }
		
		-- *******.4.1.2011.**********.7.20
		hwClockOldSourceSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				ssmUnk(0),
				ssmPrc(2),
				ssmSsua(4),
				ssmSsub(8),
				ssmSec(11),
				ssmDnu(15),
				ssmInvalid(255)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The old SSM quality of clock source."
			::= { hwClockTrapOid 20 }
		
		-- *******.4.1.2011.**********.7.21
		hwClockNewSourceSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				ssmUnk(0),
				ssmPrc(2),
				ssmSsua(4),
				ssmSsub(8),
				ssmSec(11),
				ssmDnu(15),
				ssmInvalid(255)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The new SSM quality of clock source."
			::= { hwClockTrapOid 21 }


		-- *******.4.1.2011.**********.7.22
		hwClockClusterNewSyncType OBJECT-TYPE
			SYNTAX INTEGER
				{
				frequency(1),
				time(2)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The type of clock inter-chassis sync."
			::= { hwClockTrapOid 22 }

		
		-- *******.4.1.2011.**********.7.23
		hwClockClusterNewTopoType OBJECT-TYPE
			SYNTAX INTEGER
				  { 
				  interlink(1),
				  externInject(2)
				  }
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The type of clock inter-chassis topo."
			::= { hwClockTrapOid 23 }

		
		-- *******.4.1.2011.**********.7.24
		hwClockClusterNewTopoLinkType OBJECT-TYPE
			SYNTAX INTEGER
				 {
				 bits(1),
				 port(2)
				 }
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The type of clock inter-chassis link."
			::= { hwClockTrapOid 24 }

		
		-- *******.4.1.2011.**********.7.25
		hwClockClusterNewTopoStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				fail(1),
				success(2)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The status of clock inter-chassis topo."
			::= { hwClockTrapOid 25 }


		-- *******.4.1.2011.**********.7.26
		hwClockPortIfIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Port index."
			::= { hwClockTrapOid 26 }

		-- *******.4.1.2011.**********.7.27
		hwClockPortName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The port name."
			::= { hwClockTrapOid 27 }

		-- *******.4.1.2011.**********.7.28
		hwClockGnssModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				gps(1),
				glonass(2),
				beidou(3),
				gpsglonass(4),
				gpsbeidou(5)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The gnss model."
			::= { hwClockTrapOid 28 }
			
		-- *******.4.1.2011.**********.7.29
		hwClockOldGnssModel OBJECT-TYPE
			SYNTAX INTEGER
				{
				gps(1),
				glonass(2),
				beidou(3),
				gpsglonass(4),
				gpsbeidou(5)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The old gnss model."
			::= { hwClockTrapOid 29 }
			
		-- *******.4.1.2011.**********.8
		hwClockNotifications OBJECT IDENTIFIER ::= { hwClockManageObjects 8 }

		
		-- *******.4.1.2011.**********.8.1
		hwClockSourceSwitch NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPllId, hwClockLastSourceName, hwClockCurSourceName, hwClockSrcSelMode
				 }
			STATUS current
			DESCRIPTION 
				"Clock source switch notification."
			::= { hwClockNotifications 1 }

		
		-- *******.4.1.2011.**********.8.2
		hwClockSourceSysClkLockModeChange NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockSourceOldLockMode, hwClockAttributeSysClkLockMode }
			STATUS current
			DESCRIPTION 
				"The lock mode of system clock source change notification."
			::= { hwClockNotifications 2 }

		
		-- *******.4.1.2011.**********.8.3
		hwClockSourceStateChange NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockCurSourceName, hwClockOldSourceState, hwClockSrcCfgSourceState }
			STATUS current
			DESCRIPTION 
				"The state of clock source change notification."
			::= { hwClockNotifications 3 }

		
		-- *******.4.1.2011.**********.8.4
		hwClockSourceStateResume NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockCurSourceName, hwClockOldSourceState, hwClockSrcCfgSourceState }
			STATUS current
			DESCRIPTION 
				"The state of clock source resume notification."
			::= { hwClockNotifications 4 }

		
		-- *******.4.1.2011.**********.8.5
		hwClockSourceFreqCheck NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockSrcCfgSourceDescr, hwClockSrcCfgFreqCheckResult }
			STATUS current
			DESCRIPTION 
				"The result of clock source frequnce check abnormal notification."
			::= { hwClockNotifications 5 }

		
		-- *******.4.1.2011.**********.8.6
		hwClockSourceOutputBelowThreshold NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPllId, hwClockAttributeOutThreshold, hwClockAttributeOutValue, hwClockCurSourceName
				 }
			STATUS current
			DESCRIPTION 
				"The SSM of output below threshold notification."
			::= { hwClockNotifications 6 }

		
		-- *******.4.1.2011.**********.8.7
		hwClockNotInLockedMode NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockAttributeSysClkLockMode }
			STATUS current
			DESCRIPTION 
				"The work mode of system clock is not in locked mode."
			::= { hwClockNotifications 7 }

		
		-- *******.4.1.2011.**********.8.8
		hwClockInLockedMode NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockAttributeSysClkLockMode }
			STATUS current
			DESCRIPTION 
				"The work mode of system clock is in locked mode."
			::= { hwClockNotifications 8 }

		
		-- *******.4.1.2011.**********.8.11
		hwClockSourceFailed NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockCurSourceName, hwClockSrcCfgSourceState }
			STATUS current
			DESCRIPTION 
				"The state of clock source is failed."
			::= { hwClockNotifications 11 }

		
		-- *******.4.1.2011.**********.8.12
		hwClockSourceValid NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockCurSourceName, hwClockSrcCfgSourceState }
			STATUS current
			DESCRIPTION 
				"The state of clock source is valid."
			::= { hwClockNotifications 12 }

		
		-- *******.4.1.2011.**********.8.13
		hwClockSourceFreqCheckResume NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockSrcCfgSourceDescr, hwClockSrcCfgFreqCheckResult }
			STATUS current
			DESCRIPTION 
				"The result of clock source frequnce check normal notification."
			::= { hwClockNotifications 13 }

		
		-- *******.4.1.2011.**********.8.14
		hwClockSourceOutputBelowThresholdResume NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPllId, hwClockAttributeOutThreshold, hwClockAttributeOutValue, hwClockCurSourceName
				 }
			STATUS current
			DESCRIPTION 
				"The SSM of output above threshold notification."
			::= { hwClockNotifications 14 }

		
		-- *******.4.1.2011.**********.8.15
		hwClockCesAcrMasterPwChange NOTIFICATION-TYPE
			OBJECTS { hwClockCesAcrSlot, hwClockCesAcrCard, hwClockCesAcrDomain, hwClockCesAcrOldMasterPwName, hwClockCesAcrNewMasterPwName
				 }
			STATUS current
			DESCRIPTION 
				"CES ACR master PW status change."
			::= { hwClockNotifications 15 }

		
		-- *******.4.1.2011.**********.8.16
		hwClockCesAcrLockFail NOTIFICATION-TYPE
			OBJECTS { hwClockCesAcrSlot, hwClockCesAcrCard, hwClockCesAcrDomain, hwClockCesAcrLockState }
			STATUS current
			DESCRIPTION 
				"CES ACR clock source lock fail."
			::= { hwClockNotifications 16 }

		
		-- *******.4.1.2011.**********.8.17
		hwClockCesAcrLockFailResume NOTIFICATION-TYPE
			OBJECTS { hwClockCesAcrSlot, hwClockCesAcrCard, hwClockCesAcrDomain, hwClockCesAcrLockState }
			STATUS current
			DESCRIPTION 
				"CES ACR clock source lock fail resume."
			::= { hwClockNotifications 17 }

		-- *******.4.1.2011.**********.8.22
		hwClockClusterTopoFail NOTIFICATION-TYPE
			OBJECTS { hwClockClusterNewSyncType, hwClockClusterNewTopoType, hwClockClusterNewTopoLinkType, hwClockClusterNewTopoStatus }
			STATUS current
			DESCRIPTION 
				"Clock cluster inter-chassis synchronization topo compute failed."
			::= { hwClockNotifications 22 }
		
		-- *******.4.1.2011.**********.8.23
		hwClockClusterTopoFailResume NOTIFICATION-TYPE
			OBJECTS { hwClockClusterNewSyncType, hwClockClusterNewTopoType, hwClockClusterNewTopoLinkType, hwClockClusterNewTopoStatus }
			STATUS current
			DESCRIPTION 
				"Clock inter-chassis synchronization topo compute successfully."
			::= { hwClockNotifications 23 }

		-- *******.4.1.2011.**********.8.24
		hwClockSourceInputBelowThreshold NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPllId, hwClockAttributeInputThreshold, hwClockSrcCfgSourceSsm}
			STATUS current
			DESCRIPTION 
				"The SSM of input below threshold notification."
			::= { hwClockNotifications 24 }

		-- *******.4.1.2011.**********.8.25
		hwClockSourceInputBelowThresholdResume NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPllId, hwClockAttributeInputThreshold, hwClockSrcCfgSourceSsm}
			STATUS current
			DESCRIPTION 
				"The SSM of input above or equal threshold notification."
			::= { hwClockNotifications 25 }

		-- *******.4.1.2011.**********.8.26
		hwClockSsmPktLos NOTIFICATION-TYPE
			OBJECTS {hwClockCurSourceName}
			STATUS current
			DESCRIPTION 
				"The ssm packet of clock source is lost."
			::= { hwClockNotifications 26 }

		-- *******.4.1.2011.**********.8.27
		hwClockSsmPktLosResume NOTIFICATION-TYPE
			OBJECTS {hwClockCurSourceName}
			STATUS current
			DESCRIPTION 
				"The ssm packet of clock source is normal."
			::= { hwClockNotifications 27 }
	
		-- *******.4.1.2011.**********.8.28
		hwClockCesDcrMasterPwChange NOTIFICATION-TYPE
			OBJECTS { hwClockCesDcrSlot, hwClockCesDcrCard, hwClockCesDcrDomain, hwClockCesDcrOldMasterPwName, hwClockCesDcrNewMasterPwName
				 }
			STATUS current
			DESCRIPTION 
				"CES DCR master PW status change."
			::= { hwClockNotifications 28 }


		-- *******.4.1.2011.**********.8.29
		hwClockCesDcrLockFail NOTIFICATION-TYPE
			OBJECTS { hwClockCesDcrSlot, hwClockCesDcrCard, hwClockCesDcrDomain, hwClockCesDcrLockState }
			STATUS current
			DESCRIPTION 
				"CES DCR clock source lock fail."
			::= { hwClockNotifications 29 }

		
		-- *******.4.1.2011.**********.8.30
		hwClockCesDcrLockFailResume NOTIFICATION-TYPE
			OBJECTS { hwClockCesDcrSlot, hwClockCesDcrCard, hwClockCesDcrDomain, hwClockCesDcrLockState }
			STATUS current
			DESCRIPTION 
				"CES DCR clock source lock fail resume."
			::= { hwClockNotifications 30 }

		
		-- *******.4.1.2011.**********.8.31
		hwClockSourceSsmChange NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockCurSourceName, hwClockOldSourceSsm, hwClockNewSourceSsm }
			STATUS current
			DESCRIPTION 
				"The SSM quality of clock source change notification."
			::= { hwClockNotifications 31 }
		
		-- *******.4.1.2011.**********.8.32
		hwClockFMSwitch NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPllId }
			STATUS current
			DESCRIPTION 
				"Clock select mode is force or manual."
			::= { hwClockNotifications 32 }
		
		-- *******.4.1.2011.**********.8.33
		hwClockFMSwitchResume NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPllId }
			STATUS current
			DESCRIPTION 
				"Clock select mode is automative."
			::= { hwClockNotifications 33 }
		
		-- *******.4.1.2011.**********.8.34
		hwClockSyncBad NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId }
			STATUS current
			DESCRIPTION 
				"Clock frequency synchronization bad."
			::= { hwClockNotifications 34 }
		
		-- *******.4.1.2011.**********.8.35
		hwClockSyncBadResume NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId }
			STATUS current
			DESCRIPTION 
				"Clock frequency synchronization bad resume."
			::= { hwClockNotifications 35 }

		-- *******.4.1.2011.**********.8.36
		hwClockPortNonSupport NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPortIfIndex, hwClockCurSourceName}
			STATUS current
			DESCRIPTION 
				"In current link mode, the hardware, such as boards and optical modules, do not support physical-layer clock synchronization."
			::= { hwClockNotifications 36 }

		
		-- *******.4.1.2011.**********.8.37
		hwClockPortNonSupportResume NOTIFICATION-TYPE
			OBJECTS { hwClockChassisId, hwClockPortIfIndex, hwClockCurSourceName}
			STATUS current
			DESCRIPTION 
				"The alarm that the hardware do not support physical-layer clock synchronization is resumed."
			::= { hwClockNotifications 37 }

		-- *******.4.1.2011.**********.8.38
		hwClockGnssModelChange NOTIFICATION-TYPE
			OBJECTS { hwClockPortIfIndex, hwClockPortName, hwClockGnssModel, hwClockOldGnssModel}
			STATUS current
			DESCRIPTION 
				"The smart clock gnss model changed."
			::= { hwClockNotifications 38 }
		
		-- *******.4.1.2011.**********.9
		hwClockAttributeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockAttributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The clock Attribute table."
			::= { hwClockManageObjects 9 }

		
		-- *******.4.1.2011.**********.9.1
		hwClockAttributeEntry OBJECT-TYPE
			SYNTAX HwClockAttributeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of clock Attribute table."
			INDEX { hwClockAttributeChassisIndex }
			::= { hwClockAttributeTable 1 }

		
		HwClockAttributeEntry ::=
			SEQUENCE { 
				hwClockAttributeChassisIndex
					PhysicalIndex,
				hwClockAttributeSysClkRunMode
					INTEGER,
				hwClockAttributeSsmControl
					INTEGER,
				hwClockAttributeFreqCheckEnable
					EnabledStatus,
				hwClockAttributeRetrieveMode
					INTEGER,
				hwClockAttributeWtrTime
					Integer32,
				hwClockAttributeHoldOffTime
					Integer32,
				hwClockAttributeOutThreshold
					INTEGER,
				hwClockAttributeSysMaxOutSsm
					INTEGER,
				hwClockAttribute2M1MaxOutSsm
					INTEGER,
				hwClockAttribute2M2MaxOutSsm
					INTEGER,
				hwClockAttributeSysClkLockMode
					INTEGER,
				hwClockAttributeExtendSsmControl
					INTEGER,
				hwClockAttributeInternalClockId
					Integer32,
				hwClockAttributeTodProtocol
					INTEGER,
				hwClockAttributeLtiSquelch
					EnabledStatus,
				hwClockAttributeInputThreshold
					INTEGER
			 }

		-- *******.4.1.2011.**********.9.1.1
		hwClockAttributeChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The chassis index."
			::= { hwClockAttributeEntry 1 }

		
		-- *******.4.1.2011.**********.9.1.2
		hwClockAttributeSysClkRunMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(0),
				freeRun(1),
				hold(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The run mode of system clock."
			::= { hwClockAttributeEntry 2 }

		
		-- *******.4.1.2011.**********.9.1.3
		hwClockAttributeSsmControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				on(0),
				off(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The flag whether SSM is concerned with the clock source selection."
			::= { hwClockAttributeEntry 3 }

		
		-- *******.4.1.2011.**********.9.1.4
		hwClockAttributeFreqCheckEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable flag of frequency check."
			::= { hwClockAttributeEntry 4 }

		
		-- *******.4.1.2011.**********.9.1.5
		hwClockAttributeRetrieveMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				retrieve(0),
				noRetrieve(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The retrieve mode of system clock."
			DEFVAL { retrieve }
			::= { hwClockAttributeEntry 5 }

		
		-- *******.4.1.2011.**********.9.1.6
		hwClockAttributeWtrTime OBJECT-TYPE
			SYNTAX Integer32 (0..12)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The time waiting for retrieve."
			DEFVAL { 5 }
			::= { hwClockAttributeEntry 6 }

		
		-- *******.4.1.2011.**********.9.1.7
		hwClockAttributeHoldOffTime OBJECT-TYPE
			SYNTAX Integer32 (300..1800)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The holdoff-time when the system source is lost."
			DEFVAL { 1000 }
			::= { hwClockAttributeEntry 7 }

		
		-- *******.4.1.2011.**********.9.1.8
		hwClockAttributeOutThreshold OBJECT-TYPE
			SYNTAX INTEGER
				{
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The Threshold of out put."
			::= { hwClockAttributeEntry 8 }

		
		-- *******.4.1.2011.**********.9.1.9
		hwClockAttributeSysMaxOutSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The max ssm of system out put."
			::= { hwClockAttributeEntry 9 }

		
		-- *******.4.1.2011.**********.9.1.10
		hwClockAttribute2M1MaxOutSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The max ssm of 2msync-1 out put."
			::= { hwClockAttributeEntry 10 }

		
		-- *******.4.1.2011.**********.9.1.11
		hwClockAttribute2M2MaxOutSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The max ssm of 2msync-2 out put."
			::= { hwClockAttributeEntry 11 }

		
		-- *******.4.1.2011.**********.9.1.12
		hwClockAttributeSysClkLockMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				freeRun(0),
				fastLock(1),
				lock(2),
				hold(3),
				freeRunJudge(16),
				holdJudge(19)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Lock mode of system clock."
			::= { hwClockAttributeEntry 12 }

		
		-- *******.4.1.2011.**********.9.1.13
		hwClockAttributeExtendSsmControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				on(0),
				off(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The flag whether Extend SSM is concerned with the clock source selection."
			::= { hwClockAttributeEntry 13 }

		
		-- *******.4.1.2011.**********.9.1.14
		hwClockAttributeInternalClockId OBJECT-TYPE
			SYNTAX Integer32 (0..15)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The internal clockid of the device."
			DEFVAL { 0 }
			::= { hwClockAttributeEntry 14 }

		
		-- *******.4.1.2011.**********.9.1.15
		hwClockAttributeTodProtocol OBJECT-TYPE
			SYNTAX INTEGER
				{
				nmea(1),
				ubx(2),
				none(3),
				ccsa(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"1pps bits tod protocol."
			DEFVAL { 2 }
			::= { hwClockAttributeEntry 15 }

		-- *******.4.1.2011.**********.9.1.16
		hwClockAttributeLtiSquelch OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The frequency signal output squelch flag upon the frequency loss."
			DEFVAL { 2 }
			::= { hwClockAttributeEntry 16 }
		
		-- *******.4.1.2011.**********.9.1.17
		hwClockAttributeInputThreshold OBJECT-TYPE
			SYNTAX INTEGER
				{
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The squelch threshold of the external input source."
			DEFVAL { 15 }
			::= { hwClockAttributeEntry 17 }
		
		-- *******.4.1.2011.**********.10
		hwClockSrcSelTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockSrcSelEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The system clock source selection table."
			::= { hwClockManageObjects 10 }

		
		-- *******.4.1.2011.**********.10.1
		hwClockSrcSelEntry OBJECT-TYPE
			SYNTAX HwClockSrcSelEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of system clock source selection table."
			INDEX { hwClockSrcSelChassisIndex, hwClockSrcSelType }
			::= { hwClockSrcSelTable 1 }

		
		HwClockSrcSelEntry ::=
			SEQUENCE { 
				hwClockSrcSelChassisIndex
					PhysicalIndex,
				hwClockSrcSelType
					INTEGER,
				hwClockSrcSelMode
					INTEGER,
				hwClockSrcSelSrcName
					OCTET STRING,
				hwClockSrcTraceSrcName
					OCTET STRING
			 }

		-- *******.4.1.2011.**********.10.1.1
		hwClockSrcSelChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The chassis index."
			::= { hwClockSrcSelEntry 1 }

		
		-- *******.4.1.2011.**********.10.1.2
		hwClockSrcSelType OBJECT-TYPE
			SYNTAX INTEGER
				{
				system(1),
				sync2M1(2),
				sync2M2(3)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The PLL Id."
			::= { hwClockSrcSelEntry 2 }

		
		-- *******.4.1.2011.**********.10.1.3
		hwClockSrcSelMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(0),
				manual(1),
				force(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The mode of clock source selection."
			::= { hwClockSrcSelEntry 3 }

		
		-- *******.4.1.2011.**********.10.1.4
		hwClockSrcSelSrcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The name of clock source for selection."
			::= { hwClockSrcSelEntry 4 }

		
		-- *******.4.1.2011.**********.10.1.5
		hwClockSrcTraceSrcName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The name of trace source."
			::= { hwClockSrcSelEntry 5 }

		
		-- *******.4.1.2011.**********.11
		hwClockSrcCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockSrcCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The clock source config table."
			::= { hwClockManageObjects 11 }

		
		-- *******.4.1.2011.**********.11.1
		hwClockSrcCfgEntry OBJECT-TYPE
			SYNTAX HwClockSrcCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of clock source config table."
			INDEX { hwClockSrcCfgChassisIndex, hwClockSrcCfgSourceTypeIndex, hwClockSrcCfgSourceIndex }
			::= { hwClockSrcCfgTable 1 }

		
		HwClockSrcCfgEntry ::=
			SEQUENCE { 
				hwClockSrcCfgChassisIndex
					PhysicalIndex,
				hwClockSrcCfgSourceTypeIndex
					INTEGER,
				hwClockSrcCfgSourceIndex
					Integer32,
				hwClockSrcCfgSourceDescr
					OCTET STRING,
				hwClockSrcCfgClkEnable
					EnabledStatus,
				hwClockSrcCfgSystemPriority
					Integer32,
				hwClockSrcCfg2M1Priority
					Integer32,
				hwClockSrcCfg2M2Priority
					Integer32,
				hwClockSrcCfgSourceSsm
					INTEGER,
				hwClockSrcCfgSsmSetMode
					INTEGER,
				hwClockSrcCfgSourceState
					INTEGER,
				hwClockSrcCfgFreqCheckResult
					INTEGER,
				hwClockSrcCfgSsmInterval
					Integer32,
				hwClockSrcCfgSsmTimeout
					Integer32,
				hwClockSrcCfgSabit
					INTEGER,
				hwClockSrcCfgClockId
					Integer32,
				hwClockSrcCfgClockIdSetMode
					INTEGER,
				hwClockSrcCfgOutSsm
					INTEGER,
				hwClockSrcCfgOutClockId
					INTEGER,
				hwClockSrcCfgRowStatus
					RowStatus,
				hwClockSrcCfgFreqDeviation
					OCTET STRING,
				hwClockSrcCfgPhyState
					INTEGER,
				hwClockSrcCfgNegotiationSlave
					INTEGER,
				hwClockSrcCfgFreqDeviationRecover
					INTEGER
			 }

		-- *******.4.1.2011.**********.11.1.1
		hwClockSrcCfgChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The chassis index."
			::= { hwClockSrcCfgEntry 1 }

		
		-- *******.4.1.2011.**********.11.1.2
		hwClockSrcCfgSourceTypeIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				bits(1),
				ptp(2),
				interface(3),
                                cesacr(4)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"The type of clock source."
			::= { hwClockSrcCfgEntry 2 }

		
		-- *******.4.1.2011.**********.11.1.3
		hwClockSrcCfgSourceIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The index of clock source."
			::= { hwClockSrcCfgEntry 3 }

		
		-- *******.4.1.2011.**********.11.1.4
		hwClockSrcCfgSourceDescr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The description of clock source."
			::= { hwClockSrcCfgEntry 4 }

		
		-- *******.4.1.2011.**********.11.1.5
		hwClockSrcCfgClkEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The enable flag of clock source."
			::= { hwClockSrcCfgEntry 5 }

		
		-- *******.4.1.2011.**********.11.1.6
		hwClockSrcCfgSystemPriority OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The priority of system clock source."
			DEFVAL { 0 }
			::= { hwClockSrcCfgEntry 6 }

		
		-- *******.4.1.2011.**********.11.1.7
		hwClockSrcCfg2M1Priority OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The priority of 2msync-1 clock source."
			DEFVAL { 0 }
			::= { hwClockSrcCfgEntry 7 }

		
		-- *******.4.1.2011.**********.11.1.8
		hwClockSrcCfg2M2Priority OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The priority of 2msync-2 clock source."
			DEFVAL { 0 }
			::= { hwClockSrcCfgEntry 8 }

		
		-- *******.4.1.2011.**********.11.1.9
		hwClockSrcCfgSourceSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15),
				unknown(16)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The SSM quality of clock source."
			::= { hwClockSrcCfgEntry 9 }

		
		-- *******.4.1.2011.**********.11.1.10
		hwClockSrcCfgSsmSetMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(1),
				auto(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The set mode of SSM."
			::= { hwClockSrcCfgEntry 10 }

		
		-- *******.4.1.2011.**********.11.1.11
		hwClockSrcCfgSourceState OBJECT-TYPE
			SYNTAX INTEGER
				{
				initial(0),
				normal(1),
				abnormal(2),
				waitwtr(3),
				holdoff(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The state of clock source."
			::= { hwClockSrcCfgEntry 11 }

		
		-- *******.4.1.2011.**********.11.1.12
		hwClockSrcCfgFreqCheckResult OBJECT-TYPE
			SYNTAX INTEGER
				{
				abnormal(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of frequency check."
			::= { hwClockSrcCfgEntry 12 }

		
		-- *******.4.1.2011.**********.11.1.13
		hwClockSrcCfgSsmInterval OBJECT-TYPE
			SYNTAX Integer32 (512..8000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwClockSrcCfgEntry 13 }

		
		-- *******.4.1.2011.**********.11.1.14
		hwClockSrcCfgSsmTimeout OBJECT-TYPE
			SYNTAX Integer32 (2000..32000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwClockSrcCfgEntry 14 }

		
		-- *******.4.1.2011.**********.11.1.15
		hwClockSrcCfgSabit OBJECT-TYPE
			SYNTAX INTEGER
				{
				sa4(4),
				sa5(5),
				sa6(6),
				sa7(7),
				sa8(8),
				invalid(99)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The SA bit of E1 Port SSM information."
			DEFVAL { 4 }
			::= { hwClockSrcCfgEntry 15 }

		
		-- *******.4.1.2011.**********.11.1.16
		hwClockSrcCfgClockId OBJECT-TYPE
			SYNTAX Integer32 (0..15)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The clockid of clock source."
			DEFVAL { 0 }
			::= { hwClockSrcCfgEntry 16 }

		
		-- *******.4.1.2011.**********.11.1.17
		hwClockSrcCfgClockIdSetMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(1),
				auto(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The set mode of clockid."
			::= { hwClockSrcCfgEntry 17 }

		
		-- *******.4.1.2011.**********.11.1.18
		hwClockSrcCfgOutSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15),
				unknown(16),
				invalid(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current output ssm."
			::= { hwClockSrcCfgEntry 18 }

		
		-- *******.4.1.2011.**********.11.1.19
		hwClockSrcCfgOutClockId OBJECT-TYPE
			SYNTAX INTEGER
				{
				clockid0(0),
				clockid1(1),
				clockid2(2),
				clockid3(3),
				clockid4(4),
				clockid5(5),
				clockid6(6),
				clockid7(7),
				clockid8(8),
				clockid9(9),
				clockid10(10),
				clockid11(11),
				clockid12(12),
				clockid13(13),
				clockid14(14),
				clockid15(15),
				notsupport(99)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Current output clockid."
			::= { hwClockSrcCfgEntry 19 }

		
		-- *******.4.1.2011.**********.11.1.20
		hwClockSrcCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status."
			::= { hwClockSrcCfgEntry 20 }

		
		-- *******.4.1.2011.**********.11.1.21
		hwClockSrcCfgFreqDeviation OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Freqdeviation value of clock source."
			::= { hwClockSrcCfgEntry 21 }

		
		-- *******.4.1.2011.**********.11.1.22
		hwClockSrcCfgPhyState OBJECT-TYPE
			SYNTAX INTEGER
				{
				cardTypeNotSupport(0),
				slave(1),
				master(2),
				speedNotSupport(3),
				portDown(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The PHY clock state of ports."
			::= { hwClockSrcCfgEntry 22 }

		
		-- *******.4.1.2011.**********.11.1.23
		hwClockSrcCfgNegotiationSlave OBJECT-TYPE
			SYNTAX INTEGER
				{
				notSupport(0),
				enable(1),
				disable(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Set PHY clock state to slave."
			::= { hwClockSrcCfgEntry 23 }
                                   
                 
                                   -- *******.4.1.2011.**********.11.1.24
		hwClockSrcCfgFreqDeviationRecover OBJECT-TYPE
			SYNTAX INTEGER { recover(1) }
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The recovery of clock source freq-deviation state."
			::= { hwClockSrcCfgEntry 24 }

		
		-- *******.4.1.2011.**********.12
		hwClockCesAcrPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockCesAcrPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The CES ACR clock port config table."
			::= { hwClockManageObjects 12 }

		
		-- *******.4.1.2011.**********.12.1
		hwClockCesAcrPortCfgEntry OBJECT-TYPE
			SYNTAX HwClockCesAcrPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of CES ACR clock port config table."
			INDEX { hwClockCesAcrParentIfIndex, hwClockCesAcrChannelId, hwClockCesAcrIfIndex }
			::= { hwClockCesAcrPortCfgTable 1 }

		
		HwClockCesAcrPortCfgEntry ::=
			SEQUENCE { 
				hwClockCesAcrParentIfIndex
					InterfaceIndex,
				hwClockCesAcrChannelId
					Integer32,
				hwClockCesAcrIfIndex
					InterfaceIndex,
				hwClockCesAcrPortName
					OCTET STRING,
				hwClockCesAcrChannelType
					INTEGER,
				hwClockCesAcrSourceMode
					INTEGER,
				hwClockCesAcrRecoveryDomain
					Integer32,
				hwClockCesAcrPwDomain
					Integer32,
				hwClockCesAcrPortCfgRowStatus
					RowStatus,
				hwClockCesAcrMasterDomain
					Integer32,
                                                                                         hwClockCesMode
                                                                                                               INTEGER
			 }

		-- *******.4.1.2011.**********.12.1.1
		hwClockCesAcrParentIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Indicates the index of the parent interface."
			::= { hwClockCesAcrPortCfgEntry 1 }

		
		-- *******.4.1.2011.**********.12.1.2
		hwClockCesAcrChannelId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Indicates the channel ID."
			::= { hwClockCesAcrPortCfgEntry 2 }

		
		-- *******.4.1.2011.**********.12.1.3
		hwClockCesAcrIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Indicates the interface index."
			::= { hwClockCesAcrPortCfgEntry 3 }

		
		-- *******.4.1.2011.**********.12.1.4
		hwClockCesAcrPortName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port name."
			::= { hwClockCesAcrPortCfgEntry 4 }

		
		-- *******.4.1.2011.**********.12.1.5
		hwClockCesAcrChannelType OBJECT-TYPE
			SYNTAX INTEGER
				{
				t1(1),
				e1(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Indicates the interface type. The type can be E1/CE1 or T1/CT1."
			::= { hwClockCesAcrPortCfgEntry 5 }

		
		-- *******.4.1.2011.**********.12.1.6
		hwClockCesAcrSourceMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				master(1),
				slave(2),
				recoveryDomain(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Indicates the clock mode of the interface.
				master: indicates that the clock works in master mode and uses the internal clock signal.
				slave: indicates that the clock works in slave mode and uses the line clock signal.
				recovery-domain: indicates that the clock works in slave mode and uses the recovery domain clock signal.
				"
			::= { hwClockCesAcrPortCfgEntry 6 }

		
		-- *******.4.1.2011.**********.12.1.7
		hwClockCesAcrRecoveryDomain OBJECT-TYPE
			SYNTAX Integer32 (0..16)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Indicates the clock recovery domain of the interface. DEFVAL is 0."
			::= { hwClockCesAcrPortCfgEntry 7 }

		
		-- *******.4.1.2011.**********.12.1.8
		hwClockCesAcrPwDomain OBJECT-TYPE
			SYNTAX Integer32 (0..8)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Indicates the clock PW domain of the interface. DEFVAL is 0."
			::= { hwClockCesAcrPortCfgEntry 8 }

		
		-- *******.4.1.2011.**********.12.1.9
		hwClockCesAcrPortCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status."
			::= { hwClockCesAcrPortCfgEntry 9 }

		
		-- *******.4.1.2011.**********.12.1.10
		hwClockCesAcrMasterDomain OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Indicates the clock master domain of the interface. DEFVAL is 0."
			::= { hwClockCesAcrPortCfgEntry 10 }

		-- *******.4.1.2011.**********.12.1.11
		hwClockCesMode OBJECT-TYPE
			SYNTAX INTEGER
                                                                      {
                                                                      acr(1),
                                                                      dcr(2)
                                                                      }
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Indicates the clock CES recovery mode of the interface. DEFVAL is 0."
			::= { hwClockCesAcrPortCfgEntry 11 }

		
		-- *******.4.1.2011.**********.13
		hwClockCesAcrCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockCesAcrCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The CES ACR clock source config table."
			::= { hwClockManageObjects 13 }

		
		-- *******.4.1.2011.**********.13.1
		hwClockCesAcrCfgEntry OBJECT-TYPE
			SYNTAX HwClockCesAcrCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of CES ACR clock source config table."
			INDEX { hwClockCesAcrCfgSlot, hwClockCesAcrCfgCard, hwClockCesAcrCfgDomain }
			::= { hwClockCesAcrCfgTable 1 }

		
		HwClockCesAcrCfgEntry ::=
			SEQUENCE { 
				hwClockCesAcrCfgSlot
					Integer32,
				hwClockCesAcrCfgCard
					Integer32,
				hwClockCesAcrCfgDomain
					Integer32,
				hwClockCesAcrCfgDescr
					OCTET STRING,
				hwClockCesAcrCfgSyncEnable
					EnabledStatus,
				hwClockCesAcrCfgSystemPriority
					Integer32,
				hwClockCesAcrCfgSsm
					INTEGER,
				hwClockCesAcrCfgClockId
					Integer32,
				hwClockCesAcrCfgSourceState
					INTEGER,
				hwClockCesAcrCfgFreqCheckResult
					INTEGER,
				hwClockCesAcrCfgRowStatus
					RowStatus
			 }

		-- *******.4.1.2011.**********.13.1.1
		hwClockCesAcrCfgSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The slot ID of CES ACR clock source."
			::= { hwClockCesAcrCfgEntry 1 }

		
		-- *******.4.1.2011.**********.13.1.2
		hwClockCesAcrCfgCard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The card ID of CES ACR clock source."
			::= { hwClockCesAcrCfgEntry 2 }

		
		-- *******.4.1.2011.**********.13.1.3
		hwClockCesAcrCfgDomain OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The recovery domain value of CES ACR clock source."
			::= { hwClockCesAcrCfgEntry 3 }

		
		-- *******.4.1.2011.**********.13.1.4
		hwClockCesAcrCfgDescr OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The description of clock source."
			::= { hwClockCesAcrCfgEntry 4 }

		
		-- *******.4.1.2011.**********.13.1.5
		hwClockCesAcrCfgSyncEnable OBJECT-TYPE
			SYNTAX EnabledStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The enable flag of CES ACR clock source."
			::= { hwClockCesAcrCfgEntry 5 }

		
		-- *******.4.1.2011.**********.13.1.6
		hwClockCesAcrCfgSystemPriority OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The priority of system CES ACR clock source. DEFVAL is 0."
			::= { hwClockCesAcrCfgEntry 6 }

		
		-- *******.4.1.2011.**********.13.1.7
		hwClockCesAcrCfgSsm OBJECT-TYPE
			SYNTAX INTEGER
				{
				unk(0),
				prc(2),
				ssua(4),
				ssub(8),
				sec(11),
				dnu(15),
				unknown(16)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The SSM quality of CES ACR clock source."
			::= { hwClockCesAcrCfgEntry 7 }

		
		-- *******.4.1.2011.**********.13.1.8
		hwClockCesAcrCfgClockId OBJECT-TYPE
			SYNTAX Integer32 (0..15)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The clockid of clock source. DEFVAL is 0."
			::= { hwClockCesAcrCfgEntry 8 }

		
		-- *******.4.1.2011.**********.13.1.9
		hwClockCesAcrCfgSourceState OBJECT-TYPE
			SYNTAX INTEGER
				{
				initial(0),
				normal(1),
				abnormal(2),
				waitwtr(3),
				holdoff(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The state of CES ACR clock source."
			::= { hwClockCesAcrCfgEntry 9 }

		
		-- *******.4.1.2011.**********.13.1.10
		hwClockCesAcrCfgFreqCheckResult OBJECT-TYPE
			SYNTAX INTEGER
				{
				abnormal(0),
				normal(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The result of CES ACR clock source frequency check."
			::= { hwClockCesAcrCfgEntry 10 }

		
		-- *******.4.1.2011.**********.13.1.11
		hwClockCesAcrCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status."
			::= { hwClockCesAcrCfgEntry 11 }

		
		-- *******.4.1.2011.**********.14
		hwClockCesAcrDomainInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockCesAcrDomainInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The CES ACR domain infomation table."
			::= { hwClockManageObjects 14 }

		
		-- *******.4.1.2011.**********.14.1
		hwClockCesAcrDomainInfoEntry OBJECT-TYPE
			SYNTAX HwClockCesAcrDomainInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of CES ACR domain infomation table."
			INDEX { hwClockCesAcrDomianInfoSlot, hwClockCesAcrDomianInfoCard, hwClockCesAcrDomianInfoDomain }
			::= { hwClockCesAcrDomainInfoTable 1 }

		
		HwClockCesAcrDomainInfoEntry ::=
			SEQUENCE { 
				hwClockCesAcrDomianInfoSlot
					Integer32,
				hwClockCesAcrDomianInfoCard
					Integer32,
				hwClockCesAcrDomianInfoDomain
					Integer32,
				hwClockCesAcrDomianInfoMasterPwName
					OCTET STRING,
				hwClockCesAcrDomianInfoChannelId
					Integer32,
				hwClockCesAcrDomianInfoState
					INTEGER
			 }

		-- *******.4.1.2011.**********.14.1.1
		hwClockCesAcrDomianInfoSlot OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The slot ID of CES ACR clock source."
			::= { hwClockCesAcrDomainInfoEntry 1 }

		
		-- *******.4.1.2011.**********.14.1.2
		hwClockCesAcrDomianInfoCard OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The card ID of CES ACR clock source."
			::= { hwClockCesAcrDomainInfoEntry 2 }

		
		-- *******.4.1.2011.**********.14.1.3
		hwClockCesAcrDomianInfoDomain OBJECT-TYPE
			SYNTAX Integer32 (1..16)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The recovery domain value of CES ACR clock source."
			::= { hwClockCesAcrDomainInfoEntry 3 }

		
		-- *******.4.1.2011.**********.14.1.4
		hwClockCesAcrDomianInfoMasterPwName OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Port name."
			::= { hwClockCesAcrDomainInfoEntry 4 }

		
		-- *******.4.1.2011.**********.14.1.5
		hwClockCesAcrDomianInfoChannelId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Indicates the channel ID."
			::= { hwClockCesAcrDomainInfoEntry 5 }

		
		-- *******.4.1.2011.**********.14.1.6
		hwClockCesAcrDomianInfoState OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(1),
				wait(2),
				lock(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The state of CES ACR clock source."
			::= { hwClockCesAcrDomainInfoEntry 6 }

		-- *******.4.1.2011.**********.15
		hwClockClusterTopoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockClusterTopoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The CES ACR domain infomation table."
			::= { hwClockManageObjects 15 }
		
		-- *******.4.1.2011.**********.15.1
		hwClockClusterTopoEntry OBJECT-TYPE
			SYNTAX HwClockClusterTopoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { hwClockClusterSyncType, hwClockClusterTopoType, hwClockClusterTopoLinkType }
			::= { hwClockClusterTopoTable 1 }
		
		HwClockClusterTopoEntry ::=
			SEQUENCE { 
				hwClockClusterSyncType
					INTEGER,
				hwClockClusterTopoType
					INTEGER,
				hwClockClusterTopoLinkType
					INTEGER,
				hwClockClusterTopoStatus
					INTEGER
			 }

		-- *******.4.1.2011.**********.15.1.1
		hwClockClusterSyncType OBJECT-TYPE
			SYNTAX INTEGER
				{
				frequency(1),
				time(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of clock inter-chassis sync."
			::= { hwClockClusterTopoEntry 1 }
		
		-- *******.4.1.2011.**********.15.1.2
		hwClockClusterTopoType OBJECT-TYPE
			SYNTAX INTEGER { interlink(1) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of clock inter-chassis topo.."
			::= { hwClockClusterTopoEntry 2 }
		
		-- *******.4.1.2011.**********.15.1.3
		hwClockClusterTopoLinkType OBJECT-TYPE
			SYNTAX INTEGER { bits(1) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The type of clock inter-chassis link."
			::= { hwClockClusterTopoEntry 3 }
		
		-- *******.4.1.2011.**********.15.1.4
		hwClockClusterTopoStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				fail(1),
				success(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The status of clock inter-chassis topo."
			::= { hwClockClusterTopoEntry 4 }
		         
	
	    -- *******.4.1.2011.**********.16
		hwClockSmartClockPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockSmartClockPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table is used to read and set parameters related to the SmartClock module."
			::= { hwClockManageObjects 16 }

		
		-- *******.4.1.2011.**********.16.1
		hwClockSmartClockPortCfgEntry OBJECT-TYPE
			SYNTAX HwClockSmartClockPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of SmartClock clock port config table."
			INDEX { hwClockSmartClockIfIndex }
			::= { hwClockSmartClockPortCfgTable 1 }

		
		HwClockSmartClockPortCfgEntry ::=
			SEQUENCE { 
				hwClockSmartClockIfIndex
					InterfaceIndex,
				hwClockSmartClockPtpPriority1
					Integer32,
				hwClockSmartClockPtpPriority2
					Integer32,
				hwClockSmartClockPtpDomain
					Integer32,
				hwClockSmartClockSatelliteNumber
					Integer32,
				hwClockSmartClockPtpClockClass
					Integer32,
				hwClockSmartClockSyncESsm
					INTEGER,
				hwClockSmartClockFreqLockStat
					INTEGER,
				hwClockSmartClockTimeLockStat
					INTEGER,
				hwClockSmartClockPortCfgRowStatus
					RowStatus,
				hwClockSmartPlugInFlag
					INTEGER,
				hwClockSmartClockLatitude
					OCTET STRING,
				hwClockSmartClockLongitude
					OCTET STRING,
				hwClockSmartClockAltitude
					OCTET STRING,
				hwClockSmartClockWorkMode
					INTEGER,
				hwClockSmartClockLeapSecond
					Integer32,
				hwClockSmartClockSatelliteCno
					OCTET STRING,
				hwClockSmartClockGpsTime
					OCTET STRING
			 }

		-- *******.4.1.2011.**********.16.1.1
		hwClockSmartClockIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object indicates a port index."
			::= { hwClockSmartClockPortCfgEntry 1 }

		
		-- *******.4.1.2011.**********.16.1.2
		hwClockSmartClockPtpPriority1 OBJECT-TYPE
			SYNTAX Integer32  (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object indicates the time source priority 1 for the SmartClock module."
			::= { hwClockSmartClockPortCfgEntry 2 }

		   
		-- *******.4.1.2011.**********.16.1.3
		hwClockSmartClockPtpPriority2 OBJECT-TYPE
			SYNTAX Integer32  (0..255)

			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object indicates the time source priority 2 for the SmartClock module."
			::= { hwClockSmartClockPortCfgEntry 3 }

        
                                           -- *******.4.1.2011.**********.16.1.4
		hwClockSmartClockPtpDomain OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object indicates the domain to which the SmartClock module's time source belongs."
			::= { hwClockSmartClockPortCfgEntry 4 }

		  
		-- *******.4.1.2011.**********.16.1.5
		hwClockSmartClockSatelliteNumber OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the number of satellites that the SmartClock module traces. If the module is not installed, the default value is invalid(255)."
			::= { hwClockSmartClockPortCfgEntry 5 }
  
		
		-- *******.4.1.2011.**********.16.1.6
		hwClockSmartClockPtpClockClass OBJECT-TYPE
			SYNTAX Integer32 (0..256)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the quality level of the time source traced by the SmartClock module. If the module is not installed, the default value is invalid(256)."
			::= { hwClockSmartClockPortCfgEntry 6 }


                                           -- *******.4.1.2011.**********.16.1.7
		hwClockSmartClockSyncESsm OBJECT-TYPE
			SYNTAX INTEGER 
				{
				invalid(0),
  				ssmPrc(1),
  				ssmSsut(2),
  				ssmSsul(3), 
  				ssmSec(4), 
  				ssmDnu(5), 
  				ssmUnknown(6)				
  				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the SSM value of the time source traced by the SmartClock module. If the module is not installed, the default value is invalid(0)."
			::= { hwClockSmartClockPortCfgEntry 7 }
                
                
                                           -- *******.4.1.2011.**********.16.1.8
		hwClockSmartClockFreqLockStat OBJECT-TYPE
			SYNTAX INTEGER
				{
				unlock(0),
				lock(1),
				invalid(2)				
  				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the frequency lockout flag of the SmartClock module. If the  module is not installed, the default value is invalid(2)."
			::= { hwClockSmartClockPortCfgEntry 8 }
            
		-- *******.4.1.2011.**********.16.1.9
		hwClockSmartClockTimeLockStat OBJECT-TYPE
			SYNTAX INTEGER 
				{
				unlock(0),
				lock(1),
				invalid(2)				
  				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the time lockout flag of the SmartClock module. If the  module is not installed, the default value is invalid(2)."
			::= { hwClockSmartClockPortCfgEntry 9 }

                                           -- *******.4.1.2011.**********.16.1.10
		hwClockSmartClockPortCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status."
			::= { hwClockSmartClockPortCfgEntry 10 }
		
		-- *******.4.1.2011.**********.16.1.11
		hwClockSmartPlugInFlag OBJECT-TYPE
			SYNTAX INTEGER
				{
				offline(1),
				online(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates whether the SmartClock module is installed. If the module is not installed, the default value is offline(1)."
			::= { hwClockSmartClockPortCfgEntry 11 }
		
		-- *******.4.1.2011.**********.16.1.12
		hwClockSmartClockLatitude OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the latitude of the SmartClock module. If the module is not installed, the default value is invalid."
			::= { hwClockSmartClockPortCfgEntry 12 }
		
		-- *******.4.1.2011.**********.16.1.13
		hwClockSmartClockLongitude OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the longitude of the SmartClock module. If the module is not installed, the default value is invalid."
			::= { hwClockSmartClockPortCfgEntry 13 }
		
		-- *******.4.1.2011.**********.16.1.14
		hwClockSmartClockAltitude OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the altitude of the SmartClock module. If the module is not installed, the default value is invalid."
			::= { hwClockSmartClockPortCfgEntry 14 }
		
		-- *******.4.1.2011.**********.16.1.15
		hwClockSmartClockWorkMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				invalid(1),
				position(2),
				hold(3),
				auto(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the working mode of the GPS receiver on the SmartClock module. If the module is not installed, the default value is invalid(1)."
			::= { hwClockSmartClockPortCfgEntry 15 }
		
		-- *******.4.1.2011.**********.16.1.16
		hwClockSmartClockLeapSecond OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the leap second of the SmartClock module. If the module is not installed, the default value is invalid(255)."
			::= { hwClockSmartClockPortCfgEntry 16 }
		
		-- *******.4.1.2011.**********.16.1.17
		hwClockSmartClockSatelliteCno OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the signal-to-noise ratio of the tracing satellite for the SmartClock module. If the module is not installed, the default value is invalid."
			::= { hwClockSmartClockPortCfgEntry 17 }
		
		-- *******.4.1.2011.**********.16.1.18
		hwClockSmartClockGpsTime OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the GPS time of the SmartClock module. If the module is not installed, the default value is invalid."
			::= { hwClockSmartClockPortCfgEntry 18 }


	    -- *******.4.1.2011.**********.17
		hwClockCesSerialCfgTable  OBJECT-TYPE
			SYNTAX SEQUENCE OF HwClockCesSerialCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table is used to configure the CES clock mode for serial interfaces."
			::= { hwClockManageObjects 17 }

		-- *******.4.1.2011.**********.17.1
		hwClockCesSerialCfgEntry OBJECT-TYPE
			SYNTAX HwClockCesSerialCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of CES Serial Cfg table."
			INDEX { hwClockCesSerialChassisIndex,hwClockCesSerialIfIndex }
			::= { hwClockCesSerialCfgTable 1 }


		HwClockCesSerialCfgEntry ::=
			SEQUENCE { 
				hwClockCesSerialChassisIndex
					PhysicalIndex,
				hwClockCesSerialIfIndex
					InterfaceIndex,
				hwClockCesSerialName
					OCTET STRING,
				hwClockCesSerialRowStatus
					RowStatus,
				hwClockCesSerialMode
					INTEGER			
			 }

		-- *******.4.1.2011.**********.17.1.1
		hwClockCesSerialChassisIndex OBJECT-TYPE
			SYNTAX PhysicalIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object indicates the chassis index."
			::= { hwClockCesSerialCfgEntry 1 }

		
		-- *******.4.1.2011.**********.17.1.2
		hwClockCesSerialIfIndex OBJECT-TYPE
			SYNTAX InterfaceIndex
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This object indicates the serial  interface index."
			::= { hwClockCesSerialCfgEntry 2 }

		   
		-- *******.4.1.2011.**********.17.1.3
		hwClockCesSerialName OBJECT-TYPE
			SYNTAX OCTET STRING  
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This object indicates the serial interface name."
			::= { hwClockCesSerialCfgEntry 3 }

        
		-- *******.4.1.2011.**********.17.1.4
		hwClockCesSerialRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status."
			::= { hwClockCesSerialCfgEntry 4 }

		  
		-- *******.4.1.2011.**********.17.1.5
		hwClockCesSerialMode OBJECT-TYPE
			SYNTAX INTEGER 
				{
				acr(1),
				dcr(2),
				none(3)				
  			}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This object is used to set serial interface clock mode."
			::= { hwClockCesSerialCfgEntry 5 }

		
        -- *******.4.1.2011.**********0
		hwClockConformance OBJECT IDENTIFIER ::= { hwClockMIB 10 }

		
		-- *******.4.1.2011.**********0.1
		hwClockSourceCompliances OBJECT IDENTIFIER ::= { hwClockConformance 1 }

		
		-- *******.4.1.2011.**********0.1.1
		hwClockSourceCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"The compliance of clock MIB."
			MODULE -- this module
				MANDATORY-GROUPS { hwClockManageSysGroup, hwClockSourceCfgGroup, hwClockPortCfgGroup, hwClockBitsCfgGroup, hwClockNotificationsGroup, 
					hwClockSysSelGroup, hwClockTrapOidGroup, hwClockLineCfgGroup }
			::= { hwClockSourceCompliances 1 }

		
		-- *******.4.1.2011.**********0.2
		hwClockSourceGroups OBJECT IDENTIFIER ::= { hwClockConformance 2 }

		
		-- *******.4.1.2011.**********0.2.8
		hwClockManageSysGroup OBJECT-GROUP
			OBJECTS { hwClockSourceSysClkWorkMode, hwClockSourceFreqCheckEnable, hwClockSourceHoldMode, hwClockSourceSsmControl, hwClockSourceFreqCheckRightRange, 
				hwClockSourceFreqCheckLeftRange, hwClockSourceRetrieveMode, hwClockSourceForceCloseEnableStatus, hwClockSourceSsmUnknown, hwClockExtTimeOutputType, 
				hwClockExtTimeInputType, hwClockTimeUsedSource, hwClockSourceEthClkEnable,hwClockAlarmThresholdFrequencyOffset,hwClockFrequencyOffsetMax,hwClockFrequencyOffsetMin,hwClockFrequencyOffsetMean,hwClockFrequencyOffset }
			STATUS current
			DESCRIPTION 
				"The manage group."
			::= { hwClockSourceGroups 8 }

		
		-- *******.4.1.2011.**********0.2.9
		hwClockSysSelGroup OBJECT-GROUP
			OBJECTS { hwClockSourceSelMode, hwClockSourceSelSourceId, hwClockCurSourceName, hwClockLastSourceName, hwClockPllId, 
				hwClockSourceOldLockMode, hwClockCesAcrOldMasterPwName, hwClockCesAcrNewMasterPwName, hwClockAttributeOutValue, hwClockCesAcrSlot, 
				hwClockCesAcrLockState, hwClockCesAcrDomain, hwClockCesAcrCard,hwClockCesDcrSlot,hwClockCesDcrCard,hwClockCesDcrDomain,hwClockCesDcrOldMasterPwName,hwClockCesDcrNewMasterPwName,hwClockCesDcrLockState }
			STATUS current
			DESCRIPTION 
				"The system selection group."
			::= { hwClockSourceGroups 9 }

		
		-- *******.4.1.2011.**********0.2.10
		hwClockSourceCfgGroup OBJECT-GROUP
			OBJECTS { hwClockCfgSourceId, hwClockCfgPriRvtEnableStatus, hwClockCfgSwitchCondition, hwClockCfgWtrTime, hwClockCfgBadDetect, 
				hwClockCfgSourceSsm, hwClockCfgExportEnableStatus, hwClockCfgSwiEnableStatus, hwClockCfgSourceState, hwClockCfgSourceDescr, 
				hwClockCfgFreqCheckResult, hwClockCfgHoldOffTime, hwClockCfgBits0Priority, hwClockCfgBits1Priority, hwClockCfgSystemPriority, 
				hwClockCfgSourceSsmSetMode, hwClockCfgSourceS1Id, hwClockCfgClkSourceType, hwClockCfgSsmThreshold, hwClockCfgSystemLockOut, 
				hwClockCfgBits0LockOut, hwClockCfgBits1LockOut, hwClockBitsCfgTodSignal }
			STATUS current
			DESCRIPTION 
				"The clock source group."
			::= { hwClockSourceGroups 10 }

		
		-- *******.4.1.2011.**********0.2.13
		hwClockPortCfgGroup OBJECT-GROUP
			OBJECTS { hwClockPortCfgLeftFramePri, hwClockPortCfgRightFramePri, hwClockPortCfgForceOutS1 }
			STATUS current
			DESCRIPTION 
				"The port config of clock source group."
			::= { hwClockSourceGroups 13 }

		
		-- *******.4.1.2011.**********0.2.14
		hwClockBitsCfgGroup OBJECT-GROUP
			OBJECTS { hwClockBitsCfgRecvSaBit, hwClockBitsCfgSendSaBit, hwClockBitsCfgForceOutS1, hwClockBitsCfgName, hwClockBitsCfgBitsType, 
				hwClockBitsCfgDirection, hwClockBitsCfgSaBit, hwClockBitsCfgInputMode, hwClockBitsCfgOutputMode, hwClockBitsCfgSourceId, 
				hwClockBitsCfgInvalidCond, hwClockBitsCfgBitsPortType }
			STATUS current
			DESCRIPTION 
				"The BITS clock source group."
			::= { hwClockSourceGroups 14 }

		
		-- *******.4.1.2011.**********0.2.15
		hwClockTrapOidGroup OBJECT-GROUP
			OBJECTS { hwClockLastSourceName, hwClockCurSourceName, hwClockSourceOldLockMode, hwClockChassisId, hwClockOldSourceState
				 }
			STATUS current
			DESCRIPTION 
				"The clock trap group."
			::= { hwClockSourceGroups 15 }

		
		-- *******.4.1.2011.**********0.2.16
		hwClockNotificationsGroup NOTIFICATION-GROUP
			NOTIFICATIONS { hwClockSourceSwitch, hwClockSourceStateChange, hwClockSourceStateResume, hwClockSourceFreqCheck, hwClockSourceFreqCheckResume, 
				hwClockSourceOutputBelowThreshold, hwClockSourceOutputBelowThresholdResume, hwClockCesAcrLockFail, hwClockCesAcrLockFailResume, hwClockCesAcrMasterPwChange, 
				hwClockSourceValid, hwClockInLockedMode, hwClockClusterTopoFailResume, hwClockClusterTopoFail, hwClockNotInLockedMode, hwClockSourceSysClkLockModeChange, hwClockSourceFailed,hwClockSourceInputBelowThreshold,hwClockSourceInputBelowThresholdResume,hwClockCesDcrMasterPwChange,hwClockCesDcrLockFail,hwClockCesDcrLockFailResume,hwClockSsmPktLos,hwClockSsmPktLosResume
				 }
			STATUS current
			DESCRIPTION 
				"This is the group of clock notification."
			::= { hwClockSourceGroups 16 }

		
		-- *******.4.1.2011.**********0.2.17
		hwClockLineCfgGroup OBJECT-GROUP
			OBJECTS { hwClockLineClkCfgRecvS1, hwClockLineClkCfgSendS1, hwClockLineClkCfgCardId, hwClockLineClkCfgPortId }
			STATUS current
			DESCRIPTION 
				"The line clock group.."
			::= { hwClockSourceGroups 17 }

		
	
	END

--
-- HUAWEI-CLOCK-MIB.2.11.mib
--