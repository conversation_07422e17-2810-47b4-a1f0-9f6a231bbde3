-- =================================================================                                                  
-- Copyright (C) 2013 by HUAWEI TECHNOLOGIES. All rights reserved                                                   
-- Description:  The MIB is used for defining Huawei's GPON management common MIB objects for both                 
--               profile-mode and distributing-mode.                                                             
-- Reference:                                                                                                 
-- Version: V1.34
-- ================================================================                    
    HUAWEI-XPON-COMMON-MIB DEFINITIONS ::= BEGIN
        IMPORTS
            MODULE-IDENTITY, mib-2, OBJECT-TYPE, Counter32, Counter64,
            Unsigned32, Integer32, zeroDotZero, IpAddress, Gauge32, NOTIFICATION-TYPE
                FROM SNMPv2-SMI
            TruthValue, DateAndTime, RowStatus, Mac<PERSON><PERSON>ress,TEXTUAL-CONVENTION
                FROM SNMPv2-TC
            ifIndex
                FROM IF-MIB
            MODULE-COMPLIANCE, OBJECT-GROUP
                FROM SNMPv2-CONF
            OwnerString, EntryStatus
                FROM RMON-MIB
            huaweiMgmt, huaweiUtility
                FROM HUAWEI-MIB;


        hwXponCommonMIB MODULE-IDENTITY
            LAST-UPDATED "201307151200Z"
            ORGANIZATION "Huawei Technologies Co.,Ltd."
            CONTACT-INFO                                        
                         "Huawei Industrial Base                
                          Bantian, Longgang                     
                          Shenzhen 518129                       
                          People's Republic of China            
                          Website: http://www.huawei.com        
                          Email: <EMAIL>             
                         "                                      
            DESCRIPTION  "The MIB is used for defining Huawei's GPON management common MIB objects for both profile-mode and distributing-mode."

    --  Revision history  
            REVISION     "201307151200Z"   
            DESCRIPTION  "V1.34, changes in this revision:
                          - Add the enumerate of the leaf hwXponOntInfoAppLoadState.
                         "
                         
            REVISION     "201305291200Z"   
            DESCRIPTION  "V1.33, changes in this revision:
                          - Add a leaf hwGponOntPriorityQueueMappingPolicySwitch in table hwGponDeviceCommonGlobalObjects.
                          - Add the table hwXponDeviceOntNoAuthConfigTable.
                         " 

            REVISION     "201305202300Z"   
            DESCRIPTION  "V1.32, changes in this revision:
                          - Rename hwXponOntPppoeSimuEthPortID to hwXponOntPppoeSimuPortID, and modify the description.
                          - Rename hwGponOntifEthType to hwGponOntifType, and add the enumerate of the leaf hwGponOntifType. 
                          - Rename hwGponOntifEthPort to hwGponOntifPort, and modify the description.
                          - modify the description of hwGponOntPortMulticastVlanTranslationTable.
                          - modify the description of hwGponOntPortMulticastVlanTranslationEntry.
                          - modify the description of hwGponOntPortMulticastVlanIndex.
                          - modify the description of hwGponOntPortMulticastVlanCfgTranslatedVlan.
                          - modify the description of hwGponOntPortMulticastStripSwitch.
                          - Add leaf hwGponDeviceOntCapInfoVdslPortNum in table hwGponDeviceOntCapabilityInfoTable.
                          - Add a table of hwGponOntPortServiceCfgTable.
                          - modify the description of hwXponOntStatProbeConfigParameter to support VDSL port.
                          - Add a table of hwXponOntPortEthernetStatsTable.
                          - Add the enumerate of the leaf hwGponDeviceOntCapInfoDeviceType.
			  - Add the leaf node hwXponOntInfoUsedMutualAuth in table hwXponOntInfoTable.
                         "
                         
            REVISION     "201305072300Z"   
            DESCRIPTION  "V1.31, changes in this revision:
                          - Add a table of hwXponOltOpticsModuleExtInfoTable.
                          - Add a leaf hwGponDeviceTcontAutoCreateSwitch in table hwGponDeviceCommonGlobalObjects.
                         "
    
            REVISION     "201304170900Z"   
            DESCRIPTION  "V1.30, changes in this revision:
                          - Add the enumerate of the leaf hwXponOntBatchQueryInfo. 
                          - rename hwXponPortControlTable to hwGponPortControlTable.
                          - rename hwXponPortControlDbaAssignMode to hwGponPortControlDbaAssignMode.
                          - Add leaf hwGponPortControlPonIdSwitch in table hwGponPortControlTable.
                          - Add leaf hwGponPortControlPonIdInputMode in table hwGponPortControlTable.
                          - Add leaf hwGponPortControlPonIdIdentifier in table hwGponPortControlTable.
			  - Add leaf hwGponDeviceOntCapInfoEthOamSupport in table hwGponDeviceOntCapabilityInfoTable.
                          - Add a leaf hwGponDeviceOntObjectExtendFrameID in table hwXponOntInfoTable.  
                          - Modify the name of leaf hwXponOntFtpServerProfileName to hwXponOntFtpServerProfileNameIndex in table hwXponOntFtpServerProfileTable.
                          - Add the enumerate of the leaf hwXponOntInfoInteroperabilityStandard.
                          - Add the table hwGponOntTdmPortPerf15MinTable.
                          - Add leaf hwXponOntInfoInteroperabilityStandard in table hwXponOntInfoTable.
                          - Add the enumerate of the leaf hwGponInteroperModeSwitch.
                          - Add the enumerate of the leaf hwGponOntInteroperabilityMode.
                          - Add a table of hwGponOntMulticastGemPortStatisticTable.
                          - Add a leaf hwGponOntInteroperModeActiveMode in table hwGponDeviceCommonGlobalObjects.
                          - Add the enumerate of the leaf hwXponOntInfoAppLoadState.                         
                         "
                         
            REVISION     "201301080900Z"   
            DESCRIPTION  "V1.29, changes in this revision:
                          - Modify the description of leaf hwXponOntInfoAppLoadState.
                          - Add the table hwXponOntStatProbeConfigTable.
                          - Add the table hwXponOntProbeStatTable.   
                          - Add leaf hwXponOntWanExtendInfoDetail in table hwXponOntWanInfoTable.
                          - Modify the description of leaf hwXponDeviceOtdrTestFilterPara.
                         "
			  
            REVISION     "201212040900Z"   
            DESCRIPTION  "V1.28, changes in this revision:
                          - Modify the description of leaf hwXponDeviceOtdrTestDownPowerUsage.
                          - Modify the description of leaf hwXponDeviceOtdrTestPulseWidth.
                          - Modify the description of leaf hwXponDeviceOtdrTestResult.
                         "
    
            REVISION     "201210181600Z"   
            DESCRIPTION  "V1.27, changes in this revision:
                          - Add leaf hwXponOntInfoXmlLoadErrorInfo in table hwXponOntInfoTable.
                          - Add table hwGponInteroperModeConfigTable to config ONT interoperability mode and multicust-auth mode.
                         "   
                         
            REVISION     "201209060900Z"   
            DESCRIPTION  "V1.26, changes in this revision:
                          - Add leaf hwXponDeviceOtdrTestFilterOrder in table hwXponDeviceOtdrTestTable.
                         "  
			 
            REVISION     "201209040900Z"   
            DESCRIPTION  "V1.25, changes in this revision:
                          - Modify the description of leaf hwXponOntBatchQueryItemMask and hwXponOntBatchQueryInfo.
                         "    
             
            REVISION     "201207280900Z"   
            DESCRIPTION  "V1.24, changes in this revision:
                          - Modify the description of leaf hwXponDeviceOtdrTestOpticalFibreLength.
                          - Modify the description of leaf hwXponOpticalModuleType, hwXponOpticalModuleOltOntType and hwXponOpticalModuleBandwidthType.
                         "  
                           
            REVISION     "201207200000Z"   
            DESCRIPTION  "V1.23, changes in this revision:
                          - Delete unnecessary en dashes.
                         "
                         
            REVISION     "201206190900Z"   
            DESCRIPTION  "V1.22, changes in this revision:
                          - Add leaf hwGponOnuTcontPriorityQueuePriorityReverse in table hwGponDeviceCommonGlobalObjects. 
                          - Add leaf hwXponDeviceAllowDifferentRangeSwitch in table hwXponDeviceCommonGlobalObjects.
                          - Add table hwXponOpticsParameterRangeTable for xpon optical module info.
                         "
                         
            REVISION     "201204130900Z"   
            DESCRIPTION  "V1.21, changes in this revision:
                          - Add the table of hwXponDeviceOtdrTestTable 
                            and the trap of hwXponPortOtdrResultTrap for OTDR test.
                          - Modify the table name of hwXponPortStateTable from hwXponPortStateTable to hwXponPortInfoTable.
                          - Add the leaf nodes hwXponPortOtdrCapability in table hwXponPortInfoTable.                                 
                          - Delete table hwXponDeviceOntFtpCfgServerTable, the table has never been used.
                          - Delete table hwXponDeviceOntFtpCfgTable, the table has never been used.
                          - Add table hwXponOntVoipConfigTable to config the ONT's voip service.
                          - Add table hwXponOntFtpServerProfileTable to config the FTP server profile.
                          - Modify the name of the leaf node hwGponDeviceOntVoipCfgResult to hwGponDeviceOntVoipFtpCfgResult. 
                          - Add the table hwXponOpticsDdmInfoExTable.                               
                         "
    
    	    REVISION     "201203120900Z"   
            DESCRIPTION  "V1.20, changes in this revision:
                          - Modify MAX-ACCESS of leaf hwXponOntTr069ServerProfileName to not-accessible.
                          - Modify the string length of hwXponOntTr069ServerProfileUserName from 16 to 50.
                          - Modify the string length of hwXponOntTr069ServerProfilePassword from 16 to 25. 
                          - Delete leaf hwXponOntVlanIdForTr069Server from table hwXponOntConfigTable.
                          - Delete leaf hwXponOntPriorityForTr069Server from table hwXponOntConfigTable.  
                          - Delete leaf hwXponOntAdminStateForTr069Server from table hwXponOntConfigTable. 
                          - Add leaf hwXponOntAlarmPolicyAppendIpAddress, hwXponOntAlarmPolicyAppendMac, 
                            hwXponOntAlarmPolicyAppendSn, hwXponOntAlarmPolicyAppendLoid,
                            hwXponOntAlarmPolicyAppendPolicyName in table hwXponOntAlarmPolicyTable. 
                          - Add table hwXponOntAlarmPolicyAlarmTable to set the ONT alarm level.
                          - Modify the range of leaf hwXponOntIpConfigPppoeUserName and hwXponOntIpConfigPppoePassword.
                         "

            REVISION     "201203010900Z"
            DESCRIPTION  "V1.19, changes in this revision:
                          - Add leaf hwXponOntUsedTr069ServerProfName in table hwXponOntConfigTable. 
                          - Add leaf hwXponOntVlanIdForTr069Server in table hwXponOntConfigTable.
                          - Add leaf hwXponOntPriorityForTr069Server in table hwXponOntConfigTable. 
                          - Add leaf hwXponOntAdminStateForTr069Server in table hwXponOntConfigTable. 
                          - Add the table of hwXponOntTr069ServerProfileTable for gpon ont server management. 
                          - Add leaf hwGponOntMutlicastAuthMode in hwXponDeviceCommonGlobalObjects for the ONT multicust-auth mode.    
                          - Add leaf hwXponOntControlGracefulReset in hwXponOntControlTable to reset the ONT gracefully.                          
                          - Add leaf hwXponOntControlGraceTime in hwXponOntControlTable to specify the maximum time to wait for the ONT to reset gracefully.                          
                          - Modify the chinese description of leaf hwXGponOltOpticsModuleXponTemperatureLevel into English. 
                          - Add the table hwXponDeviceOntPowerSheddingProfileTable to set the power shedding interval.                    
                          - Add the table hwXponOntPowerSheddingStatusQueryTable to query the power shedding status of the ONT. 
                          - Add leaf hwXponOntPowerSheddingProfName in table hwXponOntConfigTable.          
                          - Add leaf hwXponOntDeleteVasService in hwXponOntControlTable for omci/oam.
                          - Add the table of hwXponOntCapabilityInfoTable for omci/oam. 
                          - Modify the table name of hwXponOntIpConfigTable to hwXponOntIpMaintainTable.  
                          - Add the table of hwXponOntIpConfigTable for omci/oam.                    
                          - Add leaf hwXponPortMacChipState in hwXponPortStateTable.
                          - Add table hwGponOntPerfEverbeforeDataTable for XGPON everbefore statistics.
                          - Add leaf hwGponOntEverbeforeDownFrameFecCorrectedBytes in table hwGponOntPerfEverbeforeDataTable.
                          - Add leaf hwGponOntEverbeforeDownFrameFecCorrectedCodeWords in table hwGponOntPerfEverbeforeDataTable.
                          - Add leaf hwGponOntEverbeforeDownFrameFecUncorrectableCodeWords in table hwGponOntPerfEverbeforeDataTable. 
                          - Add leaf hwGponOntEverbeforeDownFrameTotalRecCodeWords in table hwGponOntPerfEverbeforeDataTable. 
                          - Add leaf hwGponOntEverbeforeDownFrameFecSeconds in table hwGponOntPerfEverbeforeDataTable. 
                          - Add leaf hwGponOntEverbeforeRangingTimeCount in table hwGponOntPerfEverbeforeDataTable. 
                          - Add leaf hwGponOntEverbeforeTransmittedGemFrames in table hwGponOntPerfEverbeforeDataTable. 
                          - Add leaf hwGponOntEverbeforeXgemKeyErrorCount in table hwGponOntPerfEverbeforeDataTable. 
                          - Add leaf hwGponOntEverbeforeXgemHecErrorCount in table hwGponOntPerfEverbeforeDataTable. 
                          - Modify hwGponOntPerfDataTable to hwGponOntPerfCurr15MinDataTable. 
                          - Modify the description of hwGponOntPerfCurr15MinDataTable. 
                          - Modify HwGponOntPerfDataEntry to HwGponOntPerfCurr15MinDataEntry. 
                          - Modify the description of HwGponOntPerfCurr15MinDataEntry.                          
                          - Modify hwGponOntPerfCurr15MinFECCorrectByte to hwGponOntPerfCurr15MinFecCorrectByte.
                          - Modify hwGponOntPerfCurr15MinFECCorrectCodeWords to hwGponOntPerfCurr15MinFecCorrectCodeWords.
                          - Modify hwGponOntPerfCurr15MinFECUncorrectCodeWords to hwGponOntPerfCurr15MinFecUncorrectCodeWords.
                          - Modify hwGponOntPerfCurr15MinFECTotalCodeWords to hwGponOntPerfCurr15MinFecTotalCodeWords.
                          - Modify hwGponOntPerfCurr15MinFECSeconds to hwGponOntPerfCurr15MinFecSeconds.
                          - Add leaf hwGponOntPerfCurr15MinRangingTimeCount in table hwGponOntPerfCurr15MinDataTable.
                          - Add leaf hwGponOntPerfCurr15MinTransmittedGemFrames in table hwGponOntPerfCurr15MinDataTable.
                          - Add leaf hwGponOntPerfCurr15MinXgemKeyErrorCount in table hwGponOntPerfCurr15MinDataTable.
                          - Add leaf hwGponOntPerfCurr15MinXgemHecErrorCount in table hwGponOntPerfCurr15MinDataTable.
                          - Modify hwGponOntPerfHis15MinFECCorrectByte to hwGponOntPerfHis15MinFecCorrectByte.
                          - Modify hwGponOntPerfHis15MinFECCorrectCodeWords to hwGponOntPerfHis15MinFecCorrectCodeWords.
                          - Modify hwGponOntPerfHis15MinFECUncorrectCodeWords to hwGponOntPerfHis15MinFecUncorrectCodeWords.
                          - Modify hwGponOntPerfHis15MinFECTotalCodeWords to hwGponOntPerfHis15MinFecTotalCodeWords.
                          - Modify hwGponOntPerfHis15MinFECSeconds to hwGponOntPerfHis15MinFecSeconds.
                          - Add leaf hwGponOntPerfHis15MinRangingTimeCount in table hwGponOntPerfHis15MinTable.
                          - Add leaf hwGponOntPerfHis15MinTransmittedGemFrames in table hwGponOntPerfHis15MinTable.
                          - Add leaf hwGponOntPerfHis15MinXgemKeyErrorCount in table hwGponOntPerfHis15MinTable.
                          - Add leaf hwGponOntPerfHis15MinXgemHecErrorCount in table hwGponOntPerfHis15MinTable.
                          - Modify the description of the object hwXponOntInfoAppLoadState.
                          - Modify the description of the object hwXponOntBatchQueryInfo.
                          - Add a table hwXGponOltOpticsModuleInformationTable.  
                          - Modify the description of leaf hwGponOntPerfHis15MinFecCorrectCodeWords. 
                          - Modify the description of leaf hwGponOntPerfHis15MinFecUncorrectCodeWords and hwGponOntPerfHis15MinFecTotalCodeWords.  
                          - Add leaf hwXponDeviceAlarmClearOnShutdown in hwXponDeviceCommonGlobalObjects for clear on shutdown switch.
                          - Add table hwGponCommonOntStatisticTable for the information about the priority queue alarm.
                          - Modify the name of every leaf in table hwXGponOltOpticsModuleInformationTable, modify XGPON  to 10GPON.
                          - Add the table hwGponDeviceOntCapabilityInfoTable to query the capability of the GPON ONT.        
                          - Add leaf hwXponOntInfoNoOnLineReason in hwXponOntInfoTable.              
                          - Modify the description option of hwXponPortSignalDetect.
                          - Change the name of hwXponOntActiveAlarmEntry to hwXponDeviceOntActiveAlarmEntry
                          - Add leaf hwXponDeviceSuppressInitialAlarmState in hwXponDeviceCommonGlobalObjects to suppress xpon initial alarm. 
                          - Add the enumerate of the leaf hw10GponOltOpticsModuleXponSubType.
                         "  
    	    REVISION     "201109010900Z"   
            DESCRIPTION  "V1.18, changes in this revision:
                          - Add leaf hwXponDeviceModifyBoundProfileSwitch in hwXponDeviceCommonGlobalObjects for enable or disable modify bound profile control.                      
                         "  
                         
            REVISION     "201107250900Z"   
            DESCRIPTION  "V1.17, changes in this revision:
                          - Delete the node hwXponPortControlDbaCalculatePeriod from table hwXponPortControlTable.                          
                         "  
                         
            REVISION     "201107080900Z"   
            DESCRIPTION  "V1.16, changes in this revision:
                          - Add leaf hwXponDeviceAutofindConflictCheckSwitch in hwXponDeviceCommonGlobalObjects for the ONT autofind conflict-check switch.
                         "  
                         
            REVISION     "201106250900Z"   
            DESCRIPTION  "V1.15, changes in this revision:
                          - Add leaf hwGponOntInteroperabilityMode in hwGponDeviceCommonGlobalObjects for interoperability mode.
                          - Add leaf hwGponDeviceOntDefaultLineProfName in hwGponDeviceCommonGlobalObjects for default profile.
                          - Add leaf hwGponDeviceOntDefaultSrvProfName in hwGponDeviceCommonGlobalObjects for default profile.
                          - Add the table HwGponOntIphostStatsTable to querying and clearing the information about the IPHOST port.
                          - Add the table of hwXponPortControlTable.
                          - Add the table of hwXponDeviceOntActiveAlarmTable. 
                          - Add hwXponCommonOntCatvDefaultState to support the global settings for ONT CATV ports.
                          - Add leaf hwXponDeviceGroupPowerOffControlState in hwXponDeviceCommonGlobalObjects for group power off report switch.
                          - Add the leaf hwXponOntLastDistance in table hwXponOntInfoTable.
                          - Move all TRAP objects from HUAWEI-XPON-COMMON-MIB.mib to HUAWEI-XPON-TRAP-MIB.mib.
                         "
    
            REVISION     "201105180900Z"
            DESCRIPTION  "V1.14, changes in this revision:
                          - Modify the description of leaf hwXponOntBatchQueryInfo. 
                          - Modify the description of leaf hwXponOntInfoXmlLoadState and hwXponOntInfoAppLoadState. 
                          - Add the object of hwXponDeviceCommonProfileObjects.
                          - Add the table of hwXponOntAlarmPolicyTable. 
                          - Add the table of hwXponOntConfigTable.  
                          - Modify the description of leaf hwXponOntBatchInfoTable to query the reason why the ONT last went offline.
                          - Add table hwXponOntWanInfoTable to query the information about WAN ports.
                          - Add trap hwGponOntFECUncorrectCodeWordsAlarmTrap,hwGponOntFECUncorrectCodeWordsRecoverAlarmTrap,
                            hwGponOntFECCorrectCodeWordsAlarmTrap and hwGponOntFECCorrectCodeWordsRecoverAlarmTrap.
                          - Add two leaves hwXponCommonOntFECUncorrectCodeWordsThreshold and hwXponCommonOntFECCorrectCodeWordsThreshold in hwXponCommonTrapsVbOids
                         "
                          
            REVISION     "201102280900Z"
            DESCRIPTION  "V1.14, changes in this revision:
                          - Add the table of hwGponDevicePerfDataTable.
                          - Add the table of hwGponOntPortMulticastVlanTranslationTable for the multicast vlan translation of GPON ONT ETH port. 
                          - Add a leaf hwGponDeviceOntVoipCfgResult in hwGponDeviceOntVoipCfgFileInfoTable.
                          - Add the table of hwXponDeviceOntFtpCfgServerTable.
                          - Add the table of hwXponDeviceOntFtpCfgTable.
                          - Add a leaf hwXponOntInfoSupportXmlVersion.  
                          - Add the table of hwGponOntPerfDataTable.
                          - Add the table of hwGponOntPerfHis15MinTable.
                          - Delete the table of hwGponDevicePerfDataTable.
                          - Add trap hwGponDeviceOntVersionTrap.
                          - Add trap hwGponProfOntVersionTrap.
                          - Add trap hwEponProfOntUpVersionTrap.
                          - Add a leaf hwXponRogueOntManualDetectState in hwXponCommonTrapsVbOids.
                          - Add a trap node hwXponCommonRogueOntManualDetectTrap for notifying the result of rogue ont manual detect.			  
                         "

            REVISION     "201012100900Z"
            DESCRIPTION  "V1.13, changes in this revision:
                          - Add the hwXponOntSurvivalAlarmTrap and hwXponOntSurvivalRecoverAlarmTrap
                            to support the work mode of ONT.
                          - Modify the table of hwXponOntBatchInfoTable to optimize thebatch query.
                          - Modify the nodes in the table of hwXponOntPppoeSimuTestInfoTable to support the priority of vlan.
                          - Modify the trap of hwXponCommonDeviceOntPppoeSimuTrap.
                         "

            REVISION     "201011250900Z"
            DESCRIPTION  "V1.12, changes in this revision:
                          - Modify the description of the flowing leaves:
                            hwGponOltMinBandwidth
                            hwGponOltAutoUpdateOntFlag
                          - Add a leaf hwGponOltChangePasswordIntervalTime in hwXponCommonTrapsVbOids for the trap hwXponConfigPmConfigTrap
                          - Modify the member of the trap hwXponConfigPmConfigTrap
                          - Add the table hwXponDeviceCommonGlobalObjects for setting or obtaining device common global information of XPON.
                          - Add leaf hwXponDeviceLosAlarmControlState in hwXponDeviceCommonGlobalObjects for alarm los-control.
                         "
                         
            REVISION     "201011030900Z"
            DESCRIPTION  "V1.11, changes in this revision:
                          - Add the table of hwXponOntBatchInfoTable to query all of the ont information.
                          - Add the table of hwXponOntPppoeSimuTestInfoTable 
                            and the trap of hwXponCommonDeviceOntPppoeSimuTrap for PPPOE test.
                          - Modify the node of the hwXponCommonDeviceOntPingResult, 
                            add the node of hwXponCommonDeviceReceivedErrorCode for the test of remote ping
                          - Add the table of the hwXponOntControlTable for ONT configration.
                          - Add the node of the hwXponOntInfoCfgFileCrc
                          - Add the node of the hwXponOntInfoAppLoadState and hwXponOntInfoXmlLoadState 
                            for ONT load.
                          - Modify the description of the hwGponConfigOntPortNativeVlanTrap.
                         "

            REVISION     "201009280900Z"
            DESCRIPTION  "V1.10, changes in this revision:
                          - Modify the description of the MIB file.
                         "

            REVISION     "201008250900Z"
            DESCRIPTION  "V1.09, changes in this revision:
                          - Modify the value range of the leaf hwGponOntPortTDMCodeMode.
                         "

            REVISION     "201008110900Z"
            DESCRIPTION  "V1.08, changes in this revision:
                          - Add leaf hwGponDeviceTcontAutoCombineSwitch in hwGponDeviceCommonGlobalObjects for T-CONT auto-combine."

            REVISION     "201007070900Z"
            DESCRIPTION  "V1.07, changes in this revision:
                          Add the leaf nodes hwXponModeSwitchResult, hwXponConfigMode, hwGponOltMinBandwidth, hwGponOltNearestDistance,hwGponOltFarestDistance,
                          hwGponOltAutoFindOntFlag,hwGponOltAutoUpdateOntFlag,hwGponOltDownFecEnableFlag,hwGponOntPortTDMCodeMode,hwGponOntPortManagementStatus,
                          hwGponOntEthPortLoopback,hwGponOntE1PortLoopback,hwGponOntPortVlanDefault, hwGponOntPortVlanDefaultPriority in hwXponCommonTrapsVbOids
                          for standard traps.
                          Add the definition of the trap node: hwXponSwitchModeResultTrap,hwXponConfigModeTrap,hwXponConfigChangeSnmpProfileTrap,
                          hwXponConfigPmConfigTrap,hwXponConfigOltTypeBDparentDelTrap,hwGponConfigOltTypeBDparentTrap,hwGponConfigOltTypeBDparentCreateTrap,
                          hwGponConfigOltTypeBDparentCreateTrap,hwGponConfigOntPortNativeTrap,in hwXponCommonGeneralTraps for standard traps.
                          Modify the definition of the leaf node, include:hwXponCommonDeviceOntRemotePingTrap for trap optimization.
                          Modify the attribute value,include node:hwGponDeviceOntPotsPortPTPSrvState,hwGponDeviceOntPotsPortPTPAdminState,
                          hwGponDeviceOntPotsPortPTPHookState,hwGponDeviceOntPotsPortCTPSrvState,hwGponDeviceOntPotsPortCTPSrvType,hwGponDeviceOntPotsPortCTPSrvCode.
                         "

            REVISION     "201006070900Z"
            DESCRIPTION  "V1.06, changes in this revision:
                          Modify the definition difference between MIB file and tree.c                          
                         "

            REVISION     "201004290900Z"
            DESCRIPTION  "V1.05, changes in this revision:
                          Add the table hwGponDeviceCommonGlobalObjects for setting or obtaining device common global information of GPON.
                          Add the leaf nodes hwXponOntInfoMemoryOccupation,hwXponOntInfoCpuOccupation,hwXponOntInfoTemperature,hwXponOntInfoProductDescription
                          in table hwXponOntInfoTable for obtaining ONT information.
                          Add the leaf nodes hwXponDeviceOntControlPrimaryStatus ,hwXponDeviceOntControlSecondaryStatus
                          in table hwXponOntInfoTable for obtaining ONT status of the Board.
                          Add the table hwXponOntIpConfigTable to config the ONT.
                          Add the table hwXponCommonTrapsVbOids and hwXponCommonTraps for standard traps.
                          Add leaf nodes hwXponPortStateChangeTrap and hwXponOntCfgStateChangeTrap in hwXponCommonGeneralTraps for standard traps.
                          Add the table hwXponPortStateTable to query the port state information.
                         "

            REVISION     "201004070900Z"
            DESCRIPTION  "V1.04, changes in this revision:
                          Add the table hwXponOntInfoTable to query the ont information.
                         "

            REVISION     "201003170900Z"
            DESCRIPTION  "V1.03, changes in this revision:
                          Modify the attribute value,include node:hwGponDeviceOntPotsPortPTPSrvState,hwGponDeviceOntPotsPortPTPAdminState,
                          hwGponDeviceOntPotsPortPTPHookState,hwGponDeviceOntPotsPortCTPSrvState,hwGponDeviceOntPotsPortCTPSrvType,
                          hwGponDeviceOntPotsPortCTPSrvCode.
                         "

            REVISION     "201003100900Z"
            DESCRIPTION  "V1.02, changes in this revision:
                          Add the leaf node hwXponOntTransmittingFileLength in table hwXponOntFileTransmitionTable for setting or obtaining
                          the length of the file to be transmit.
                         "

            REVISION     "201001210900Z"
            DESCRIPTION  "V1.01, invariant."

            REVISION     "200912300900Z"
            DESCRIPTION  "V1.00, Initial version."
            ::= { huaweiUtility  145 }

        hwXponCommonObjectMIB OBJECT IDENTIFIER ::= { hwXponCommonMIB 1}

        hwXponCommonObjects OBJECT IDENTIFIER ::= { hwXponCommonObjectMIB  1}

        hwXponCommonControlObjects OBJECT IDENTIFIER ::= { hwXponCommonObjects 1}

        hwXponCommonStatisticObjects OBJECT IDENTIFIER ::= { hwXponCommonObjects 2}

        hwGponDeviceCommonGlobalObjects OBJECT IDENTIFIER ::= { hwXponCommonObjects 3}
        
        hwXponDeviceCommonGlobalObjects OBJECT IDENTIFIER ::= { hwXponCommonObjects 4}
        
        hwXponDeviceCommonProfileObjects OBJECT IDENTIFIER ::= { hwXponCommonObjects 5}

   --Table hwGponDeviceOntVoipCfgFileInfoTable
        hwGponDeviceOntVoipCfgFileInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponDeviceOntVoipCfgFileInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwGponDeviceOntVoipCfgFileInfoTable is used to query the version information and validation status of the ONT voice configuration file.
                 The indexes of this table are ifIndex and hwGponDeviceOntObjIndex.
                "
            ::= { hwXponCommonControlObjects 1 }

        hwGponDeviceOntVoipCfgFileInfoEntry OBJECT-TYPE
            SYNTAX HwGponDeviceOntVoipCfgFileInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwGponDeviceOntVoipCfgFileInfoTable is used to query the version information and validation status of the ONT voice configuration file.
                 The indexes of this entry are ifIndex and hwGponDeviceOntObjIndex.
                "
            INDEX       { ifIndex, hwGponDeviceOntObjIndex }
            ::= { hwGponDeviceOntVoipCfgFileInfoTable 1 }

        HwGponDeviceOntVoipCfgFileInfoEntry ::=
            SEQUENCE
                {
                hwGponDeviceOntObjIndex               Integer32,
                hwGponDeviceOntVoipCfgFileState       INTEGER,
                hwGponDeviceOntVoipCfgFileVersion     OCTET STRING,
                hwGponDeviceOntVoipFtpCfgResult       INTEGER
                }

        hwGponDeviceOntObjIndex  OBJECT-TYPE
            SYNTAX       Integer32
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "ONT ID, range: 0-127."
            ::= { hwGponDeviceOntVoipCfgFileInfoEntry 1}

        hwGponDeviceOntVoipCfgFileState OBJECT-TYPE
            SYNTAX      INTEGER
                {
                inactive (1),
                active (2),
                initializing (3),
                fault (4),
                invalid (-1)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "ONT VOIP configuration file state.
                 Options:
                 1. inactive(1)     - Configuration retrieval has not been attempted
                 2. active(2)       - Configuration was retrieved
                 3. initializing(3) - Configuration is now being retrieved
                 4. fault(4)        - Configuration retrieval process failed
                 5. invalid(-1)     - Indicates that the query fails or no information is detected
                "
            ::= { hwGponDeviceOntVoipCfgFileInfoEntry 2 }

        hwGponDeviceOntVoipCfgFileVersion OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE(0..25))
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "ONT VOIP file version."
            ::= { hwGponDeviceOntVoipCfgFileInfoEntry 3 }

       hwGponDeviceOntVoipFtpCfgResult OBJECT-TYPE
            SYNTAX     INTEGER
                {
                success(0),
                resolvefail(1),
                notreach(2),
                notconnect(3),
                notvalidate(4),
                notauthenticate(5),
                responsetimeout(6),
                responsefail(7),
                configfileerror(8),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "Query the failed alarm information of config XML file.
                 Options:
                 1. success(0)            - The ONT configuration is successful
                 2. resolvefail(1)        - Failed to resolve the configuration server name
                 3. notreach(2)           - Cannot reach configuration server(The port cannot be reached, ICMP errors)
                 4. notconnect(3)         - Cannot connect to configuration server (due to bad credentials or other fault after the port responded)
                 5. notvalidate(4)        - Cannot validate configuration server
                 6. notauthenticate(5)    - Cannot authenticate configuration session (e.g. missing credentials)
                 7. responsetimeout(6)    - Timeout waiting for response from configuration server
                 8. responsefail(7)       - Failure response received from configuration server
                 9. configfileerror(8)    - Configuration file received has an error
                 10. invalid(-1)          - Other errors
                 "
            ::= { hwGponDeviceOntVoipCfgFileInfoEntry  4 } 
            
  -- Table hwGponDeviceOntPotsPortStateTable
        hwGponDeviceOntPotsPortStateTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponDeviceOntPotsPortStateEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "Query the state of ONT POTS port table.The table is used to query the
                 state of ONT POTS port.
                 The indexes of this table are ifIndex, hwGponDeviceOntObjIndex and hwGponDeviceOntPotsPortId.
                "
            ::= { hwXponCommonControlObjects 2 }

        hwGponDeviceOntPotsPortStateEntry OBJECT-TYPE
            SYNTAX HwGponDeviceOntPotsPortStateEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "Query the state of ONT POTS port table.The table is used to query the
                 state of ONT POTS port.
                 The indexes of this entry are ifIndex, hwGponDeviceOntObjIndex and hwGponDeviceOntPotsPortId.
                "
            INDEX       { ifIndex, hwGponDeviceOntObjIndex, hwGponDeviceOntPotsPortId }
            ::= { hwGponDeviceOntPotsPortStateTable 1 }

        HwGponDeviceOntPotsPortStateEntry ::=
            SEQUENCE
                {
                hwGponDeviceOntPotsPortId                Integer32,
                hwGponDeviceOntPotsPortPTPSrvState       INTEGER,
                hwGponDeviceOntPotsPortPTPAdminState     INTEGER,
                hwGponDeviceOntPotsPortPTPHookState      INTEGER,
                hwGponDeviceOntPotsPortCTPSrvState       INTEGER,
                hwGponDeviceOntPotsPortCTPSrvType        INTEGER,
                hwGponDeviceOntPotsPortCTPSrvCode        INTEGER
                }

        hwGponDeviceOntPotsPortId OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "ONT POTS port ID."
            ::= { hwGponDeviceOntPotsPortStateEntry 1 }

        hwGponDeviceOntPotsPortPTPSrvState OBJECT-TYPE
            SYNTAX   INTEGER
                {
                normal (1),
                fail (2),
                invalidState (-1)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "ONT POTS port physics running state.
                 Options:
                 1. normal(1)        - Normal state
                 2. fail(2)          - Fail state
                 3. invalidState(-1) - Returned in case of query failure or error
                "
            ::= { hwGponDeviceOntPotsPortStateEntry 2 }

        hwGponDeviceOntPotsPortPTPAdminState OBJECT-TYPE
            SYNTAX      INTEGER
                {
                lock (1),
                unlock (2),
                invalidState (-1)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "ONT POTS port physics admin state.
                 Options:
                 1. lock(1)          - All user functions of this circuit pack are blocked, and alarm for this managed entity
                                       are no longer generated
                 2. unlock(2)        - Unlock state, all user functions of this circuit pack are normal
                 3. invalidState(-1) - This value is return when can't query or have some errors in query
                "
            ::= { hwGponDeviceOntPotsPortStateEntry 3 }

        hwGponDeviceOntPotsPortPTPHookState OBJECT-TYPE
            SYNTAX   INTEGER
                {
                onHook (1),
                offHook (2),
                invalidState (-1)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                   "ONT POTS port physics hook state.
                    Options:
                    1. onHook(1)        - ONT POTS port physics is onHook
                    2. offHook(2)       - ONT POTS port physics is offHook
                    3. invalidState(-1) - Returned in case of query failure or error
                   "
            ::= { hwGponDeviceOntPotsPortStateEntry 4 }

        hwGponDeviceOntPotsPortCTPSrvState OBJECT-TYPE
            SYNTAX   INTEGER
                {
                noneOrinitial (1),
                registered (2),
                inSession (3),
                failedRegistration (4),
                failedInvite (5),
                portNotconfigured (6),
                configDone (7),
                invalidState (-1)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "ONT POTS port service running state.
                 Options:
                 1. noneOrinitial(1)      - None or initial state
                 2. registered(2)         - Registered state
                 3. inSession(3)          - In session
                 4. failedRegistration(4) - Failed in registration
                 5. failedInvite(5)       - Failed in invite
                 6. portNotconfigured(6)  - Port doesn't configure
                 7. configDone(7)         - Config has been done
                 8. invalidState(-1)      - Returned in case of query failure or error
                "
            ::= { hwGponDeviceOntPotsPortStateEntry 5 }

        hwGponDeviceOntPotsPortCTPSrvType OBJECT-TYPE
            SYNTAX   INTEGER
                {
                idleOrnone (1),
                twoWay (2),
                threeWay (3),
                fax (4),
                telem (5),
                conference (6),
                invalidType (-1)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "ONT POTS port service type.
                 Options:
                 1. idleOrnone(1)       - Idle or none type
                 2. twoWay(2)           - Two way type
                 3. threeWay(3)         - Three way type
                 4. fax(4)              - Fax type
                 5. telem(5)            - Telem type
                 6. conference(6)       - Conference type
                 7. invalidType(-1)     - Invalid type
                "
            ::= { hwGponDeviceOntPotsPortStateEntry 6 }

        hwGponDeviceOntPotsPortCTPSrvCode OBJECT-TYPE
            SYNTAX   INTEGER
                {
                pcmu (1),
                reserved1 (2),
                reserved2 (3),
                gsm (4),
                g723 (5),
                dvi4ClockRate8000 (6),
                dvi4ClockRate16000 (7),
                lpc (8),
                pcma (9),
                g722 (10),
                l16TwoChannels  (11),
                l16OneChannel (12),
                qcelp (13),
                cn (14),
                mpa (15),
                g728 (16),
                dvi4ClockRate11025 (17),
                dvi4ClockRate22050 (18),
                g729 (19),
                t38(20),
                invalidCode (-1)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "ONT POTS port code mode.
                 Options:
                 1.  pcmu(1)                - Audio data is encoded as eight bits per sample, after logarithmic scaling.
                                              PCMU denotes mu-law scaling
                 2.  reserved1(2)           - Reserved
                 3.  reserved2(3)           - Reserved
                 4.  gsm(4)                 - GSM denotes the European GSM 06.10 standard for full-rate
                                              speech transcoding, ETS 300 961, which is based on RPE/LTP (residual pulse
                                              excitation/long term prediction) coding at a rate of 13 kb/
                 5.  g723(5)                - Dual-rate speech coder for multimedia communications transmitting at 5.3 and 6.3
                                              kbit/s
                 6.  dvi4ClockRate8000(6)   - DVI4 uses an adaptive delta pulse code modulation (ADPCM) encoding scheme that was
                                              specified by the Interactive Multimedia Association (IMA) as the IMA ADPCM wave type,
                                              and the clock rate is 8000HZ
                 7.  dvi4ClockRate16000(7)  - DVI4 uses an adaptive delta pulse code modulation (ADPCM) encoding scheme that was
                                              specified by the Interactive Multimedia Association (IMA) as the IMA ADPCM wave type,
                                              and the clock rate is 16000HZ
                 8.  lpc(8)                 - LPC designates an experimental linear predictive encoding contributed by Ron Frederick,
                                              which is based on an implementation written by Ron Zuckerman posted to the Usenet group
                                              comp
                 9.  pcma(9)                - Audio data is encoded as eight bits per sample, after logarithmic scaling. PCMA denotes
                                              A-law scaling
                 10. g722(10)               - 7 kHz audio-coding within 64 kbit/s
                 11. l16TwoChannels(11)     - Two channel encoding
                 12. l16OneChannel(12)      - One channel encoding
                 13. qcelp(13)              - QCELP CODEC compresses each 20 milliseconds of 8,000 Hz
                 14. cn(14)                 - Comfort Noise
                 15. mpa(15)                - MPA denotes MPEG-1 or MPEG-2 audio encapsulated as elementary streams
                 16. g728(16)               - Coding of speech at 16 kbit/s using low-delay code excited linear prediction
                 17. dvi4ClockRate11025(17) - DVI4 uses an adaptive delta pulse code modulation (ADPCM) encoding scheme that was specified
                                              by the Interactive Multimedia Association (IMA) as the IMA ADPCM wave type, and the clock rate
                                              is 11025HZ.
                 18. dvi4ClockRate22050(18) - DVI4 uses an adaptive delta pulse code modulation (ADPCM) encoding  scheme that was specified
                                              by the Interactive Multimedia Association (IMA) as the IMA ADPCM wave type, and the clock rate
                                              is 11025HZ.
                 19. g729(19)               - Coding of speech at 8 kbit/s using conjugate structure-algebraic code excited linear prediction
                 20. t38(20)                - T.38 is used by the IFP peer to verify message alignment. It is identified by an ASN.1 Application tag
                 21. invalidCode(-1)        - Invalid code
                "
            ::= { hwGponDeviceOntPotsPortStateEntry 7 }

  --Table hwXponOntFileTransmitionTable
        hwXponOntFileTransmitionTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntFileTransmitionEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This is ONT file transmission table. This table is used to
                 transmit the config or diagnose files between BMS and ONU.
                 The indexes of this table are ifIndex, hwXponOntIndex and
                 hwXponOntTransmittingSerialNo.
                "
            ::= { hwXponCommonControlObjects 3 }

        hwXponOntFileTransmitionEntry OBJECT-TYPE
            SYNTAX     HwXponOntFileTransmitionEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This is ONT file transmission table. This table is used to
                 transmit the config or diagnose files between BMS and ONU.
                 The indexes of this entry are ifIndex, hwXponOntIndex and
                 hwXponOntTransmittingSerialNo.
                "
            INDEX       { ifIndex, hwXponOntIndex, hwXponOntTransmittingSerialNo }
            ::= { hwXponOntFileTransmitionTable 1 }

        HwXponOntFileTransmitionEntry  ::=
            SEQUENCE
                {
                hwXponOntIndex                            Integer32,
                hwXponOntTransmittingSerialNo             Integer32,
                hwXponOntTransmittingFileValue            OCTET STRING,
                hwXponOntTransmittingFileLength           Unsigned32
                }

        hwXponOntIndex   OBJECT-TYPE
            SYNTAX       Integer32
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "ONT ID, range: 0-254."
            ::= { hwXponOntFileTransmitionEntry 1}

        hwXponOntTransmittingSerialNo OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "The serial number of transmission."
            ::= { hwXponOntFileTransmitionEntry  2 }

        hwXponOntTransmittingFileValue OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..8192))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The file to be transmitted."
            ::= { hwXponOntFileTransmitionEntry  3 }

        hwXponOntTransmittingFileLength OBJECT-TYPE
            SYNTAX      Unsigned32
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The length of the file to be transmitted."
            ::= { hwXponOntFileTransmitionEntry  4 }

  --Table hwXponOntInfoTable
        hwXponOntInfoTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The ont info table. This table is used to query the ont information.
                 The indexes of this table are ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 4 }

        hwXponOntInfoEntry OBJECT-TYPE
            SYNTAX     HwXponOntInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The ont info table. This table is used to query the ont information.
                 The indexes of this entry are ifIndex and hwXponOntIndex.
                "
            INDEX       { ifIndex, hwXponOntIndex}
            ::= { hwXponOntInfoTable 1 }

        HwXponOntInfoEntry  ::=
            SEQUENCE
                {
                hwXponOntInfoOnlineDuration            Gauge32,
                hwXponOntInfoMemoryOccupation          Integer32,
                hwXponOntInfoCpuOccupation             Integer32,
                hwXponOntInfoTemperature               Integer32,
                hwXponOntInfoProductDescription        OCTET STRING,
                hwXponDeviceOntControlPrimaryStatus    INTEGER,
                hwXponDeviceOntControlSecondaryStatus  BITS,
                hwXponOntInfoCfgFileCrc                Integer32,
                hwXponOntInfoAppLoadState              Integer32,
                hwXponOntInfoXmlLoadState              Integer32,
                hwXponOntInfoSupportXmlVersion         OCTET STRING,
                hwXponOntLastDistance                  Integer32,
                hwXponOntInfoNoOnLineReason            Integer32,
                hwXponOntInfoXmlLoadErrorInfo          OCTET STRING,
                hwXponOntInfoInteroperabilityStandard      INTEGER,
                hwGponDeviceOntObjectExtendFrameID     Integer32,
                hwXponOntInfoUsedMutualAuth            INTEGER    
                }

        hwXponOntInfoOnlineDuration OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "second"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The one-time online duration of the ONT.
                 Unit: second
                "
            ::= { hwXponOntInfoEntry  1 }

        hwXponOntInfoMemoryOccupation OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The Memory occupation of the ONT, range: 0-100."
            ::= { hwXponOntInfoEntry  2 }

        hwXponOntInfoCpuOccupation OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The CPU occupation of the ONT, range: 0-100."
            ::= { hwXponOntInfoEntry  3 }

        hwXponOntInfoTemperature OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "degree centigrade"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The temperature of the ONT.
                 Unit: degree centigrade
                "
            ::= { hwXponOntInfoEntry  4 }

        hwXponOntInfoProductDescription  OBJECT-TYPE
            SYNTAX       OCTET STRING (SIZE (0..256))
            MAX-ACCESS   read-only
            STATUS       current
            DESCRIPTION
                "The product description of the ONT."
            ::= { hwXponOntInfoEntry 5 }

        hwXponDeviceOntControlPrimaryStatus OBJECT-TYPE
            SYNTAX     INTEGER
                {
                is-nr(1),
                is-anr(2),
                is-rst(3),
                is-anrst(4),
                oos-au(5),
                oos-ma(6),
                oos-auma(7),
                oos-aurst(8),
                oos-maanr(9)
                }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "The primary status of the Board.This parameter indicates the current overall
                 service condition of an entity, i.e., whether it is in-service or out-of-service.
                 Options:
                 1. is-nr(1)     - Normal
                 2. is-anr(2)    - Abnormal
                 3. is-rst(3)    - Restricted
                 4. is-anrst(4)  - Abnormal & Restricted
                 5. oos-au(5)    - Autonomous
                 6. oos-ma(6)    - Management
                 7. oos-auma(7)  - Autonomous & Management
                 8. oos-aurst(8) - Autonomous & Restricted
                 9. oos-maanr(9) - Management & Abnormal
                "
            ::= { hwXponOntInfoEntry  6 }

        hwXponDeviceOntControlSecondaryStatus OBJECT-TYPE
            SYNTAX     BITS
                {
                sst-ains(0),
                sst-faf(1),
                sst-flt(2),
                sst-lpbk(3),
                sst-mea(4),
                sst-sgeo(5),
                sst-stbyh(6),
                sst-ts(7),
                sst-uas(8),
                sst-ueq(9),
                sst-wrk(10),
                sst-pwr(11)
                }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "This parameter indicates the secondary status of the board. The parameter provides additional
                 information pertaining to PST and PSTQ. For example, it may indicate the type and/or reason of
                 the external command, the event that occurred in the Network Element, etc. Multiple values may
                 apply to an entity at a given moment. The highest bit of SST value indicates the sst-ains(0)
                 status;
                 The second highest bit of SST value indicates the sst-faf(1) status, and the rest can be deduced
                 by analogy.
                 This BITS structure can report the following status:
                 1.  sst-ains(0)  - this bit position positively reports that the status of the board is automatic in-service
                 2.  sst-faf(1)   - Facility Failure
                 3.  sst-flt(2)   - Fault
                 4.  sst-lpbk(3)  - Loopback
                 5.  sst-mea(4)   - Mismatch of Equipment and Attributes
                 6.  sst-sgeo(5)  - Supporting Entity Outage
                 7.  sst-stbyh(6) - Standby-Hot
                 8.  sst-ts(7)    - Test
                 9.  sst-uas(8)   - Unassigned
                 10. sst-ueq(9)   - Unequipped
                 11. sst-wrk(10)  - Working
                 12. sst-pwr(11)  - Power
                 "
            ::= { hwXponOntInfoEntry  7 }

        hwXponOntInfoCfgFileCrc OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The CRC value of the ONT."
            ::= { hwXponOntInfoEntry  8 }

        hwXponOntInfoAppLoadState OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The state of the app load for ONT.
                 0x01 initstate
                 0x02 process %0
                 0x03 ftp load fail
                 0x04 loaded to mainboard process %10
                 0x05 loaded to ponboard process %20
                 0x06 loaded to ponboard fail
                 0x07 process %80
                 0x08 fail:user stop
                 0x09 fail:ont offline 
                 0x0a fail:ont response fail
                 0x0b fail:ont response timeout
                 0x0c fail:pon inner error
                 0x0d process %100,ont restart
                 0x0e Process %100 the ont now is in survival mode
                 0x0f fail:system is busy because the ponboard's channel is occupied
                 0x10 fail:failed to verify the version information
                 0x11 fail:processing the loading task timed out
                 0x12 fail:ont file check failure
                 0x13 fail:code file validate failure
                 0x14 fail:system buffer is insufficient
                 0x15 fail:ont not support load
                 0x16 fail:ont storage space insufficient
                 0x17 fail:ont image file error
                 0x18 fail:ont image file existed
                 0x19 fail:ont activate image file fail
                 0x1a fail:ont commit image file fail
                 0x1b fail:ont image file crc error
                 0xff fail:unknown reason
                "
            ::= { hwXponOntInfoEntry  9 }

        hwXponOntInfoXmlLoadState OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The state of the xml load for ONT.
                 0x01 initstate
                 0x02 process %0
                 0x03 ftp load fail
                 0x04 loaded to mainboard process %10
                 0x05 loaded to ponboard process %20
                 0x06 loaded to ponboard fail
                 0x07 process %80
                 0x08 process %100
                 0x09 fail:user stop
                 0x0a fail:ont not support xml
                 0x0b fail:ont offline 
                 0x0c fail:ont response unknown fail
                 0x0d fail:ont response timeout
                 0x0e fail:xml error ont will reconfigure fail
                 0x0f fail:xml format error 
                 0x10 fail:xml content error
                 0x11 fail:ont find xml transfer error
                 0x12 fail:unknown error from ont
                 0x13 fail:unknown error from ponboard
                 0x14 fail:system is busy because the ponboard's channel is occupied
                 0xff fail:unknown reason
		"
            ::= { hwXponOntInfoEntry  10 }
            
        hwXponOntInfoSupportXmlVersion OBJECT-TYPE
            SYNTAX       OCTET STRING (SIZE (0..25))
            MAX-ACCESS   read-only
            STATUS       current
            DESCRIPTION
                "The xml version that the ONT supports, it is a V.R.C.SP string."
            ::= { hwXponOntInfoEntry 11 }          
                     
        hwXponOntLastDistance OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "m"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The last ranging value of the ONT.
                 Unit: m
                 Options: 
                 1. invalid (-1)            - Indicates that the query fails or no information is detected."
            ::= { hwXponOntInfoEntry  12 }

        hwXponOntInfoNoOnLineReason OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the reason why the ONT went offline.
                 The value (1) indicates that the reason is lacking third party ont license,
                 the value (-1) indicates that the reason is not lacking third party ont license."
            ::= { hwXponOntInfoEntry  13 }
        hwXponOntInfoXmlLoadErrorInfo  OBJECT-TYPE
            SYNTAX       OCTET STRING (SIZE (0..280))
            MAX-ACCESS   read-only
            STATUS       current
            DESCRIPTION
                "Indicates the error information when the ONT XML configuration fails to be loaded."
            ::= { hwXponOntInfoEntry 14 }
            
        hwXponOntInfoInteroperabilityStandard  OBJECT-TYPE
            SYNTAX       INTEGER
                {
                itu-t(1),
                ctc(2),
                eric-v1(3),
                eric-v2(4),
                itu-t-g984(5),
                itu-t-g988(6),
                invalid(-1)
                }
            MAX-ACCESS   read-only
            STATUS       current
            DESCRIPTION
                "The interoperability mode of the ONT.
                 Options:
                 1. itu-t(1)    - Indicates that the interoperability standard of the ONT is itu-t
                 2. ctc(2)      - Indicates that the interoperability standard of the ONT ctc
                 3. eric-v1(3)     - Indicates that the interoperability standard of the ONT is eric-v1
		 4. eric-v2(4) - Indicates that the interoperability standard of the ONT is eric-v2
                 5. itu-t-g984(5) - Indicates that the interoperability standard of the ONT is itu-t-g984
                 6. itu-t-g988(6) - Indicates that the interoperability standard of the ONT is itu-t-g988      
                 7. invalid(-1) - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOntInfoEntry 15 } 
                       
 		    hwGponDeviceOntObjectExtendFrameID  OBJECT-TYPE
		        SYNTAX      Integer32
		        MAX-ACCESS  read-only
		        STATUS      current
		        DESCRIPTION
		            "Extend Frame ID. The issued value (-1) of Extend Frame ID indicates that the ONT is not specified to any Extend Frame."
		        ::= { hwXponOntInfoEntry 16 } 
		        
	hwXponOntInfoUsedMutualAuth OBJECT-TYPE
            SYNTAX INTEGER {
                   yes (1),
                   no (2),
                   invalid (-1)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                 "Indicates whether the ONT uses mutual authentication.
                  Options:
                  1. yes (1)           -Indicates the ONT uses mutual authentication.
                  2. no (2)            -Indicates the ONT does not use mutual authentication.
                  3. invalid (-1)      -Indicates the invalid value.
                 "
            ::= { hwXponOntInfoEntry 17 }
	                  
   --Table hwXponOntIpMaintainTable
        hwXponOntIpMaintainTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntIpMaintainEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                    "The ONT IP maintain table. This table is used to config the ONT.
                     The indexes of this table are ifIndex hwXponOntIndex and hwXponOntIpMaintainIndex.
                    "
            ::= { hwXponCommonControlObjects 5 }

        hwXponOntIpMaintainEntry OBJECT-TYPE
            SYNTAX     HwXponOntIpMaintainEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                    "The ONT IP maintain table. This table is used to config the ONT.
                     The indexes of this entry are ifIndex hwXponOntIndex and hwXponOntIpMaintainIndex.
                    "
            INDEX       { ifIndex, hwXponOntIndex, hwXponOntIpMaintainIndex}
            ::= { hwXponOntIpMaintainTable 1 }

        HwXponOntIpMaintainEntry  ::=
            SEQUENCE
                {
                hwXponOntIpMaintainIndex                 Integer32,
                hwXponOntIpRemotePingIpAddress           IpAddress            
                }

        hwXponOntIpMaintainIndex OBJECT-TYPE
            SYNTAX       Integer32
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "The IP index of the ont IP host."
            ::= { hwXponOntIpMaintainEntry  1 }

        hwXponOntIpRemotePingIpAddress OBJECT-TYPE
            SYNTAX      IpAddress
            MAX-ACCESS   read-write
            STATUS       current
            DESCRIPTION
                "The destination IP address of the ONT ping."
            ::= { hwXponOntIpMaintainEntry  2 }                       

  --Table hwXponPortInfoTable
        hwXponPortInfoTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponPortInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "XPON port Info table. This table is used to query the port information.
                 The index of this table is ifIndex.
                "
            ::= { hwXponCommonControlObjects 6 }

        hwXponPortInfoEntry OBJECT-TYPE
            SYNTAX     HwXponPortInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "XPON port Info table. This table is used to query the port information.
                 The index of this entry is ifIndex.
                "
            INDEX       { ifIndex}
            ::= { hwXponPortInfoTable 1 }

        HwXponPortInfoEntry  ::=
            SEQUENCE
                {
                hwXponPortSignalDetect               INTEGER,
                hwXponPortMacChipState               INTEGER,
                hwXponPortOtdrCapability             INTEGER
                }

        hwXponPortSignalDetect OBJECT-TYPE
            SYNTAX      INTEGER
                {
                normal (1),
                fault (2),
                unsupport(255)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Query the signal detect info of the port.
                 Options:
                 1. normal(1)      - Optical signals are detected
                 2. fault(2)       - Optical signals are not detected
                 3. unsupport(255) - Signal detection is not supported
                "
            ::= { hwXponPortInfoEntry  1 }

        hwXponPortMacChipState OBJECT-TYPE
            SYNTAX      INTEGER
                {
                normal (1),
                fail (2)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Query the mac chipset state info of the port.
                 Options:
                 1. normal(1)      - The XPON MAC chipset is normal
                 2. fail(2)       - The XPON MAC chipset is fault
                "
            ::= { hwXponPortInfoEntry  2 }
                        
        hwXponPortOtdrCapability OBJECT-TYPE
            SYNTAX      INTEGER
                {
                support(1),
                notSupport(2),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates whether the optical module supports the OTDR test.
                 Options:
                 1. support(1)                  - Indicates that the optical module supports the OTDR test.
                 2. notSupport(2)               - Indicates that the optical module does not support the EOTDR test.
                 3. invalid(-1)                 - Indicates that the query fails or no information is detected.
                "
            ::= { hwXponPortInfoEntry  3 }    

  --Table hwXponOntBatchInfoTable
        hwXponOntBatchInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntBatchInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to query the ont run state, config state, admin state, load state, 
                 equipment ID, software version, system status, CRC value and offline reason. The length of query result can not exceed 7000 BYTEs.
                 This table can not support the operation of get next.
                 The index of this table is hwXponOntBatchQueryIndex.
                "
            ::= { hwXponCommonControlObjects 7 }

        hwXponOntBatchInfoEntry OBJECT-TYPE
            SYNTAX HwXponOntBatchInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to query the ont run state, config state, admin state, load state, 
                 equipment ID, software version, system status, CRC value and offline reason. The length of query result can not exceed 7000 BYTEs.
                 This table can not support the operation of get next.
                 The index of this entry is hwXponOntBatchQueryIndex.
                "
            INDEX { hwXponOntBatchQueryIndex }
            ::= { hwXponOntBatchInfoTable 1 }

        HwXponOntBatchInfoEntry ::=
            SEQUENCE 
            {
            hwXponOntBatchQueryIndex          Unsigned32,
            hwXponOntBatchQueryItemMask       Unsigned32,
            hwXponOntBatchQueryList           OCTET STRING,
            hwXponOntBatchQueryInfo           OCTET STRING
            }

        hwXponOntBatchQueryIndex OBJECT-TYPE
            SYNTAX Unsigned32 
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The object indicates the index of the table hwXponOntBatchInfoTable."
            ::= { hwXponOntBatchInfoEntry 1 }

        hwXponOntBatchQueryItemMask OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The items of ont status need query, include ont run state, config state, 
                 admin state, app load state, xml load state, equipment ID, software version, system status, 
                 CRC value, offline reason and hardware version.
                "
            ::= { hwXponOntBatchInfoEntry 2 }

        hwXponOntBatchQueryList OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..4096))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The object indicates the ONT list, and each ONT takes 4 bytes, include 
                 frame(4bit), slot(6bit), port(6bit) and ontid(16bits) and the most number of ONT is 1024.
                "
            ::= { hwXponOntBatchInfoEntry 3 }

        hwXponOntBatchQueryInfo OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of ont status:
                 run state(1byte)         : 1-online,2-offline,255-invalid
                 config state(1byte)      : 1-init,2-normal,3-fail,4-noresume,5-config,255-invalid
                 admin state(1byte)       : 1-activated,2-deactivated,255-invalid
                 app load state(1byte)    : 1-init,2-start,3-ftpfail,4-loadtoscu,5-loadtopon,6-loadtoponfail,
                                            7-loadtoont,8-userstop,9-ontoffline,10-ontackfail,11-ontresponsetimeout,
                                            12-errpon,13-ontreset,14-survival,15-channeloccupied,
                                            16-errversion,17-tasktimeout,18-ontfilecheckerr,19-ontfilevalidatefail,
                                            20-bufferinsufficient,255-invalid
                 xml load state(1byte)    : 1-init,2-start,3-ftpfail,4-loadtoscu,5-loadtopon,6-loadtoponfail,7-loadtoont,
                                            8-crcreport,9-userstop,10-notsupport,11-ontoffline,12-ontackfail,
                                            13-ontresponsetimeout,14-cfgfail,15-errformat,16-errcontent,17-errtransfer,
                                            18-errontinner,19-errponboardinner,20-channeloccupied,255-invalid
                 equipment ID(20bytes)    : the equipment ID of the ONT
                 software version(16bytes): the software version of the ONT
                 system status(1byte)     : 1-normal,2-survival
                 CRC value(4bytes)        : the CRC value report from the ONT
                 offline reason(1byte)    : 1-LOS(Loss of signal),2-LOSi(Loss of signal for ONUi),
                                            3-LOFi(Loss of frame of ONUi),4-SFi(Signal fail of ONUi),
                                            5-LOAi(Loss of acknowledge with ONUi),6-LOAMi(Loss of PLOAM for ONUi),
                                            7-deactive ONT fails,8-deactive ONT success,9-reset ONT,10-re-register ONT,
                                            11-pop up fail,12-authentication fail,13-dying-gasp,255-invalid
                 hardware version(16bytes): the hardware version of the ONT
                "
            ::= { hwXponOntBatchInfoEntry 4 }
            
  --Table hwXponOntPppoeSimuTestInfoTable
        hwXponOntPppoeSimuTestInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntPppoeSimuTestInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used for the PPPoE testing.
                 The index of this table is a combination of ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 8 }

        hwXponOntPppoeSimuTestInfoEntry OBJECT-TYPE
            SYNTAX HwXponOntPppoeSimuTestInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used for the PPPoE testing.
                 The index of this entry is a combination of ifIndex and hwXponOntIndex.
                "
            INDEX { ifIndex, hwXponOntIndex }
            ::= { hwXponOntPppoeSimuTestInfoTable 1 }
		
        HwXponOntPppoeSimuTestInfoEntry ::=
            SEQUENCE { 
            hwXponOntPppoeSimuPortID               Integer32,
            hwXponOntPppoeSimuOuterCVlanID         Integer32,
            hwXponOntPppoeOuterSimuCVlanPri        Integer32,
            hwXponOntPppoeSimuPppoeUsername        OCTET STRING,
            hwXponOntPppoeSimuPppoePassword        OCTET STRING,
            hwXponOntPppoeSimuPppoeAuthProtocol    INTEGER,
            hwXponOntPppoeSimuSwitch               INTEGER
            }

        hwXponOntPppoeSimuPortID OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The object indicates the type and ID of the ONT port.
                 port type(2 bytes) + port ID(2 bytes)."
            ::= { hwXponOntPppoeSimuTestInfoEntry 1 }

        hwXponOntPppoeSimuOuterCVlanID OBJECT-TYPE
            SYNTAX Integer32 (-1..4095)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The object indicts the vlan ID, GPON : 0 - 4095; EPON : 1 - 4094; the value -1 indicts that the query fails."
            ::= { hwXponOntPppoeSimuTestInfoEntry 2 }
		
        hwXponOntPppoeOuterSimuCVlanPri OBJECT-TYPE
            SYNTAX Integer32 ( -1 | 0..7 )
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The priority of vlan."
            ::= { hwXponOntPppoeSimuTestInfoEntry 3 }

        hwXponOntPppoeSimuPppoeUsername OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..64))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The user name of PPPoE test."
            ::= { hwXponOntPppoeSimuTestInfoEntry 4 }
		
        hwXponOntPppoeSimuPppoePassword OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..64))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The password of PPPoE test."
            ::= { hwXponOntPppoeSimuTestInfoEntry 5 }
		
        hwXponOntPppoeSimuPppoeAuthProtocol OBJECT-TYPE
            SYNTAX INTEGER
                {
                chap(1),
                pap(2),
                invalid (-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The protocol type of PPPoE test.
                 Options: 
                 1. chap (1)                - The protocol type of PPPoE test is chap.
                 2. pap (2)                 - The protocol type of PPPoE test is pap.
                 3. invalid (-1)            - Indicates that the query fails.
                "
            ::= { hwXponOntPppoeSimuTestInfoEntry 6 }
		
        hwXponOntPppoeSimuSwitch OBJECT-TYPE
            SYNTAX INTEGER
                {
                 start(1),
                 stop(2),
                 invalid (-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The object is used to start or stop the PPPoE test.
                 Options: 
                 1. start (1)                - Start the PPPoE test.
                 2. stop (2)                 - Stop the PPPoE test.
                 3. invalid (-1)             - Indicates that the query fails.
                "
            ::= { hwXponOntPppoeSimuTestInfoEntry 7 }
		
   --Table hwXponOntControlTable
        hwXponOntControlTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntControlEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponOntControlTable is used to config the ONT.
                 The indexes of this table are ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 9 }

        hwXponOntControlEntry OBJECT-TYPE
            SYNTAX HwXponOntControlEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponOntControlTable is used to config the ONT.
                 The indexes of this entry are ifIndex and hwXponOntIndex.
                "
            INDEX       { ifIndex, hwXponOntIndex }
            ::= { hwXponOntControlTable 1 }

        HwXponOntControlEntry ::=
            SEQUENCE
                {
                hwXponOntConfigFactoryConfiguration    INTEGER,
                hwXponOntControlGracefulReset          INTEGER,
                hwXponOntControlGraceTime              Integer32,
                hwXponOntDeleteVasService              INTEGER
                }
		
        hwXponOntConfigFactoryConfiguration OBJECT-TYPE
            SYNTAX   INTEGER{
                    restore (1),
                    invalid (-1)
                }
            MAX-ACCESS read-write
            STATUS     current
            DESCRIPTION
                "This parameter is used to restore the ONT to factory configuration.
                 Options:
                 1. restore (1)       - Restore 
                 2. invalid (-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOntControlEntry  1 }

        hwXponOntControlGracefulReset OBJECT-TYPE
            SYNTAX   INTEGER{
                    gracefullyreset (1),
                    invalid (-1)
                }
            MAX-ACCESS read-write
            STATUS     current
            DESCRIPTION
                "This parameter is used to reset the ONT gracefully. 
                 Options:
                 1. gracefullyreset (1) - Indicates that the ONT resets gracefully
                 2. invalid (-1)        - Indicates the invalid value
                "
            ::= { hwXponOntControlEntry  2 }

        hwXponOntControlGraceTime OBJECT-TYPE
            SYNTAX   Integer32 (-1 | 1..65535)
            MAX-ACCESS read-write
            STATUS     current
            DESCRIPTION
                "This parameter is used to specify the maximum time to wait for the ONT to reset gracefully. 
                 The default maximum time for wait is 14400 seconds.
                 The invalid value is -1.
                "
            ::= { hwXponOntControlEntry  3 }
                
         hwXponOntDeleteVasService OBJECT-TYPE
             SYNTAX INTEGER {
                delete(1),
                invalid(-1)
                }
             MAX-ACCESS read-write
             STATUS  current
             DESCRIPTION
                "Delete the vas service of the ONT.
                 Options:
                 1. delete(1)                    - Delete the vas service of the ONT
                 2. invalid(-1)                  - invalid value
                 "
             ::= { hwXponOntControlEntry 4 }
		
   --Table hwGponOntPortMulticastVlanTranslationTable
        hwGponOntPortMulticastVlanTranslationTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponOntPortMulticastVlanTranslationEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwGponOntPortMulticastVlanTranslationTable is used to config the multicast vlan translation of GPON ONT port.
                 The indexes of this table are ifindex, hwGponDeviceOntObjectIndex, hwGponOntifType, hwGponOntifPort, hwGponOntPortMulticastVlanIndex.
                "
            ::= { hwXponCommonControlObjects 10 }  
            
        hwGponOntPortMulticastVlanTranslationEntry OBJECT-TYPE
            SYNTAX HwGponOntPortMulticastVlanTranslationEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwGponOntPortMulticastVlanTranslationTable is used to config the multicast vlan translation of GPON ONT port.
                 The indexes of this entry are ifindex, hwGponDeviceOntObjectIndex, hwGponOntifType, hwGponOntifPort, hwGponOntPortMulticastVlanIndex.
                "
            INDEX       
                { 
                ifIndex, 
                hwGponDeviceOntObjIndex,
                hwGponOntifType,
                hwGponOntifPort,
                hwGponOntPortMulticastVlanIndex
                }
            ::= { hwGponOntPortMulticastVlanTranslationTable 1 }

        HwGponOntPortMulticastVlanTranslationEntry ::=
            SEQUENCE
                {
                hwGponOntifType                               INTEGER,
                hwGponOntifPort                               Integer32,
                hwGponOntPortMulticastVlanIndex               Integer32,
                hwGponOntPortMulticastVlanCfgTranslatedVlan   Integer32,
                hwGponOntPortMulticastStripSwitch             INTEGER,
                hwGponOntPortMulticastVlanCfgRowStatus        RowStatus
                }

        hwGponOntifType OBJECT-TYPE
            SYNTAX INTEGER {
                           vdsl(37),
                           eth(47)
                           }
            MAX-ACCESS    not-accessible
            STATUS      current
            DESCRIPTION
                "The ONT port type.
                 Options:
                 1. vdsl(37) -The ONT port type is vdsl
                 2. eth(47) -The ONT port type is eth
                "
            ::= { hwGponOntPortMulticastVlanTranslationEntry 1 }

        hwGponOntifPort OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS    not-accessible
            STATUS      current
            DESCRIPTION
                "The ONT port ID."
            ::= { hwGponOntPortMulticastVlanTranslationEntry 2 }

        hwGponOntPortMulticastVlanIndex OBJECT-TYPE
            SYNTAX        Integer32
            MAX-ACCESS    not-accessible
            STATUS        current
            DESCRIPTION
               "The multicast vlan index of ONT port.
                Ranging from 1 to 4095.
               "
            ::= { hwGponOntPortMulticastVlanTranslationEntry  3 }

        hwGponOntPortMulticastVlanCfgTranslatedVlan OBJECT-TYPE
            SYNTAX        Integer32 (-1 | 1..4095)
            MAX-ACCESS    read-write
            STATUS        current
            DESCRIPTION
               "The translation vlan of ONT port, when the GPON ONT port multicast vlan transmit mode is translation."
            ::= { hwGponOntPortMulticastVlanTranslationEntry  4 }

        hwGponOntPortMulticastStripSwitch OBJECT-TYPE
            SYNTAX INTEGER {
                untag(1),
                tag(2),
                translation(3),  
                invalid(-1)
                }
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "This object indicates the multicast vlan transmit mode of GPON ONT port.
                  Options:
                  1. untag(1)          - Indicates that the GPON ONT port multicast vlan transmit mode is untag          
                  2. tag(2)            - Indicates that the GPON ONT port multicast vlan transmit mode is tag     
                  3. translation(3)    - Indicates that the GPON ONT port multicast vlan transmit mode is translation
                  4. invalid(-1)       - Indicates that the query fails or no information is detected
                 "
            ::= { hwGponOntPortMulticastVlanTranslationEntry  5 }

        hwGponOntPortMulticastVlanCfgRowStatus OBJECT-TYPE
            SYNTAX        RowStatus
            MAX-ACCESS    read-write
            STATUS        current
            DESCRIPTION
                "Row status. This object is used to differentiate the
                 creation, modification and deletion operations for an object.
                "
            ::= { hwGponOntPortMulticastVlanTranslationEntry 6 } 

   --Table hwXponOntVoipConfigTable
        hwXponOntVoipConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntVoipConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponOntVoipConfigTable is used to config the FTP Server and config the configuration files name.
                 The indexes of this table are ifindex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 12 }  
            
        hwXponOntVoipConfigEntry OBJECT-TYPE
            SYNTAX HwXponOntVoipConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponOntVoipConfigTable is used to config the FTP Server and config the configuration files name.
                 The indexes of this entry are ifindex and hwXponOntIndex.
                "
            INDEX       
                { 
                ifIndex, 
                hwXponOntIndex
                }
            ::= { hwXponOntVoipConfigTable 1 }

        HwXponOntVoipConfigEntry ::=
            SEQUENCE
                { 
                hwXponOntVoipUsedFtpServerProfile             OCTET STRING,
                hwXponOntVoipFtpFileName                      OCTET STRING,
                hwXponOntVoipFtpFileTrigger                   Integer32,
                hwXponOntVoipConfigMethod                     INTEGER
                }
                     
        hwXponOntVoipUsedFtpServerProfile OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..15))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The server of used."
            ::= { hwXponOntVoipConfigEntry 1 }

        hwXponOntVoipFtpFileName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..63))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The configuration file name."
            ::= { hwXponOntVoipConfigEntry 2 }

        hwXponOntVoipFtpFileTrigger OBJECT-TYPE
            SYNTAX        Integer32
            MAX-ACCESS    read-write
            STATUS        current
            DESCRIPTION
               "This leaf set 1 when config the FTP Server and config the configuration file name.
               "
            ::= { hwXponOntVoipConfigEntry  3 }
            
        hwXponOntVoipConfigMethod OBJECT-TYPE
             SYNTAX INTEGER {
                default(1),
                omci(2),
                ftp(3),
                tr069(4)
                }
             MAX-ACCESS read-write
             STATUS  current
             DESCRIPTION
                "Config tht ONT's voip service.
                 Options:
                 1. default(1)        - do not configure, ont default           
                 2. omci(2)           - OMCI
                 3. ftp(3)            - configuration file retrieval
                 4. tr069(4)          - tr069
                 The default value is default(1).                
                 "
             ::= { hwXponOntVoipConfigEntry 4 }
  
  --Table hwXponOntConfigTable
        hwXponOntConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponOntConfigTable is used to config the ONT.
                 The indexes of this table are ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 13 }

        hwXponOntConfigEntry OBJECT-TYPE
            SYNTAX HwXponOntConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponOntConfigTable is used to config the ONT.
                 The indexes of this entry are ifIndex and hwXponOntIndex.
                "
            INDEX       { ifIndex, hwXponOntIndex }
            ::= { hwXponOntConfigTable 1 }

        HwXponOntConfigEntry ::=
            SEQUENCE
                {
                hwXponOntUsedAlarmPolicyName                  OCTET STRING,
                hwXponOntPowerSheddingProfName                OCTET STRING,
                hwXponOntUsedTr069ServerProfName              OCTET STRING
                }
		
        hwXponOntUsedAlarmPolicyName OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..32))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The name of alarm policy profile which used by the ONT."
            ::= { hwXponOntConfigEntry 1 }
            
        hwXponOntPowerSheddingProfName OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..32))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The name of the power shedding profile that is bound to the ONT."
            ::= { hwXponOntConfigEntry 2 }

        hwXponOntUsedTr069ServerProfName OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..32))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the name of the tr069 service profile that is bound to the ONT."
            ::= { hwXponOntConfigEntry 3}

  --Table hwXponOntWanInfoTable
        hwXponOntWanInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntWanInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponOntWanInfoTable is used to query the information about WAN ports.
                 The indexes of this table are ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 14 }

        hwXponOntWanInfoEntry OBJECT-TYPE
            SYNTAX HwXponOntWanInfoEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponOntWanInfoTable is used to query the information about WAN ports.
                 The indexes of this entry are ifIndex and hwXponOntIndex.
                "
            INDEX       { ifIndex, hwXponOntIndex }
            ::= { hwXponOntWanInfoTable 1 }

        HwXponOntWanInfoEntry ::=
            SEQUENCE
                {
                hwXponOntWanInfoDetail    OCTET STRING,
                hwXponOntWanExtendInfoDetail   OCTET STRING
                }
		
        hwXponOntWanInfoDetail OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..800))
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the information about WAN ports."
            ::= { hwXponOntWanInfoEntry 1 }     
                    
        hwXponOntWanExtendInfoDetail OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..1500))
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the information about WAN ports including IPv6 WAN.
                 This object consists of length, version, the number of WAN ports (n)
                 and n * WAN ports information. 
                 Principles to extend this object:
                 1. to add parameters, version should be upgraded, and the new parameters should 
                 be appended to the tail of each WAN information.
                 2. if the the version of OLT device or NMS is old, the OLT device or NMS
                 only have to parse the content they can recognize.   
                 The format of this object is as follows:
                 --------------------------------------------------------------------------------------
                 |field          |field length |                  field description                   |
                 --------------------------------------------------------------------------------------
                 |Total_Length   |2            |Length from Version to WAN_port_N_info.               |  
                 --------------------------------------------------------------------------------------
                 |Version        |1            |Version indicates the format of WAN port info, which  |                                               
                 |               |             |will be described detailly in SOI document.           |    
                 --------------------------------------------------------------------------------------
                 |WAN_port_num   |1            |Total number of WAN ports.                            | 
                 --------------------------------------------------------------------------------------
                 |WAN_port_1_info|x            | x = (Total_Length - 2)/WAN_port_num                  |  
                 --------------------------------------------------------------------------------------  
                 |WAN_port_2_info|x            |                                                      |  
                 --------------------------------------------------------------------------------------
                 |......         |x            |                                                      |  
                 --------------------------------------------------------------------------------------
                 |WAN_port_N_info|x            |                                                      |  
                 --------------------------------------------------------------------------------------
                "
            ::= { hwXponOntWanInfoEntry 2 } 
                        
  --Table hwGponPortControlTable
        hwGponPortControlTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponPortControlEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwGponPortControlTable is used to config the information about ports.
                 The index of this table is ifIndex.
                "
            ::= { hwXponCommonControlObjects 15 }

        hwGponPortControlEntry OBJECT-TYPE
            SYNTAX HwGponPortControlEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwGponPortControlTable is used to config the information about ports.
                 The index of this entry is ifIndex.
                "
            INDEX       { ifIndex }
            ::= { hwGponPortControlTable 1 }

        HwGponPortControlEntry ::=
            SEQUENCE
                {
                hwGponPortControlDbaAssignMode      INTEGER,
                hwGponPortControlPonIdSwitch        INTEGER,
                hwGponPortControlPonIdInputMode     INTEGER,
                hwGponPortControlPonIdIdentifier    OCTET STRING
                }
                
        hwGponPortControlDbaAssignMode OBJECT-TYPE
          SYNTAX     INTEGER {
               maxbandwidthusage(1),
               minloopdelay(2),
               manual(3),
               default(4),
               invalid(-1)
          }
          MAX-ACCESS   read-write
          STATUS      current
          DESCRIPTION
            "This object is used to configure the DBA bandwidth assignment mode.
             Options:
             1. maxbandwidthusage(1) - max bandwidth usage mode 
             2. minloopdelay(2)      - min loop delay mode
             3. manual(3)            - manual mode, this value is read-only             
             4. default(4)           - default mode, the same with global DBA assignment mode
             5. invalid(-1)          - Indicates that the query fails or no information is detected
            "
          ::= { hwGponPortControlEntry 1 }

        hwGponPortControlPonIdSwitch OBJECT-TYPE
          SYNTAX     INTEGER {
               enable(1),
               disable(2),
               invalid(-1)
          }
          MAX-ACCESS   read-write
          STATUS      current
          DESCRIPTION
            "This object is used to configure the PON-ID switch.
             Options:
             1. enable(1)       - Indicates that the PON-ID switch is enabled
             2. disable(2)      - Indicates that the PON-ID switch is disabled
             3. invalid(-1)     - Indicates that the query fails or no information is detected
            "
          ::= { hwGponPortControlEntry 2 }

        hwGponPortControlPonIdInputMode OBJECT-TYPE
          SYNTAX     INTEGER {
               text(1),
               hex(2),
               invalid(-1)
          }
          MAX-ACCESS   read-write
          STATUS      current
          DESCRIPTION
            "This object is used to configure the input mode of PON-ID identifier.
             Options:
             1. text(1)         - Indicates that the input mode of PON-ID identifier is text
             2. hex(2)          - Indicates that the input mode of PON-ID identifier is hex
             3. invalid(-1)     - Indicates that the query fails or no information is detected
            "
          ::= { hwGponPortControlEntry 3 }
          
        hwGponPortControlPonIdIdentifier OBJECT-TYPE
          SYNTAX      OCTET STRING (SIZE (1..7))
          MAX-ACCESS  read-write
          STATUS      current
          DESCRIPTION
            "This object is used to configure the PON-ID identifier.
            "
          ::= { hwGponPortControlEntry 4 }
          
  --Table hwXponDeviceOntActiveAlarmTable
        hwXponDeviceOntActiveAlarmTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponDeviceOntActiveAlarmEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponDeviceOntActiveAlarmTable is used to query the active alarm about ONT.
                 The indexes of this table are ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 16 }

        hwXponDeviceOntActiveAlarmEntry OBJECT-TYPE
            SYNTAX HwXponDeviceOntActiveAlarmEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponDeviceOntActiveAlarmTable is used to query the active alarm about ONT.
                 The indexes of this entry are ifIndex and hwXponOntIndex.
                "
            INDEX       { ifIndex, hwXponOntIndex }
            ::= { hwXponDeviceOntActiveAlarmTable 1 }

        HwXponDeviceOntActiveAlarmEntry ::=
            SEQUENCE
                {
                hwXponDeviceOntActiveAlarmNum  Integer32,
                hwXponDeviceOntActiveAlarmList    OCTET STRING,
                hwXponDeviceOntPortActiveAlarmNum Integer32,
                hwXponDeviceOntPortActiveAlarmList OCTET STRING
                }
		
        hwXponDeviceOntActiveAlarmNum OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the active alarm number about ONT."
            ::= { hwXponDeviceOntActiveAlarmEntry 1 }

        hwXponDeviceOntActiveAlarmList OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..400))
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the active alarm list about ONT."
            ::= { hwXponDeviceOntActiveAlarmEntry 2 }
            
        hwXponDeviceOntPortActiveAlarmNum OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates active alarm number about ONT ports."
            ::= { hwXponDeviceOntActiveAlarmEntry 3 }
            
        hwXponDeviceOntPortActiveAlarmList OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..600))
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the active alarm list about ONT ports."
            ::= { hwXponDeviceOntActiveAlarmEntry 4 }
  
  --Table hwXponOntIpConfigTable
        hwXponOntIpConfigTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntIpConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                    "The ONT IP config table. This table is used to configure and query
 	             the IP configuration mode and IP address of an ONT.
                     The indexes of this table are ifIndex, hwXponOntIndex and hwXponOntIpIndex.
                    "
            ::= { hwXponCommonControlObjects 17 }

        hwXponOntIpConfigEntry OBJECT-TYPE
            SYNTAX     HwXponOntIpConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                    "The ONT IP config table. This table is used to configure and query
 	             the IP configuration mode and IP address of an ONT.
                     The indexes of this entry are ifIndex, hwXponOntIndex and hwXponOntIpIndex.
                    "
            INDEX       { ifIndex, hwXponOntIndex, hwXponOntIpIndex}
            ::= { hwXponOntIpConfigTable 1 }

        HwXponOntIpConfigEntry  ::=
            SEQUENCE
                {
                hwXponOntIpIndex                         Integer32,
                hwXponOntIpConfigMode                    INTEGER,
                hwXponOntIpConfigVlan                    Integer32, 	     
                hwXponOntIpConfigPriority                Integer32,
                hwXponOntIpconfigDscpDefaultPriority     Integer32,
                hwXponOntIpconfigDscpProfIndex           Integer32,			 
                hwXponOntIpAddress                       IpAddress,
                hwXponOntNetMask                         IpAddress,
                hwXponOntNetGateway                      IpAddress,             
                hwXponOntMasterDNS                       IpAddress,
                hwXponOntSlaveDNS                        IpAddress,
                hwXponOntIpConfigPppoeAccountMode        INTEGER,
                hwXponOntIpConfigPppoeUserName           OCTET STRING,		       		    
                hwXponOntIpConfigPppoePassword           OCTET STRING,
                hwXponOntIpconfigDhcpReset               INTEGER,
                hwXponOntIpconfigRowStatus               RowStatus
                }

        hwXponOntIpIndex OBJECT-TYPE
            SYNTAX       Integer32
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "The IP index of the ont IP host.
                 range(GPON): 0-1
                 range(EPON): 0
                 "
            ::= { hwXponOntIpConfigEntry  1 }  
            
        hwXponOntIpConfigMode  OBJECT-TYPE
                SYNTAX     INTEGER{
                            dhcp(1),
                            static(2),
                            nonOLTconfiguration(3),
                            pppoe(4)
                            }
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                    "IP configuration mode.
                     Options:
                     1. dhcp(1)                 - Indicates the dynamic allocation mode
                     2. static(2)               - Indicates the static allocation mode
                     3. nonOLTconfiguration(3)  - Indicates the nonOLTconfiguration mode(IP configuration mode is not set in the OLT)
                     4. pppoe(4)                - Indicates the pppoe allocation mode
                     "
                ::= { hwXponOntIpConfigEntry 2 } 
            
        hwXponOntIpConfigVlan  OBJECT-TYPE
                SYNTAX     Integer32
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                    "The VLAN of ONT IP host.
                     range: 0-4095"
               ::= { hwXponOntIpConfigEntry 3 }  
               
        hwXponOntIpConfigPriority  OBJECT-TYPE
                SYNTAX     Integer32
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                    "The priority of ONT IP host. range: 0-7, 0xfe: dscp-mapping."
                     DEFVAL { 0 }
                ::= { hwXponOntIpConfigEntry 4 } 
                
        hwXponOntIpconfigDscpDefaultPriority OBJECT-TYPE
               SYNTAX      Integer32
               MAX-ACCESS  read-write
               STATUS      current
               DESCRIPTION
                   "The default priority for dscp-mapping policy.
                    range: 0-7"
               ::= { hwXponOntIpConfigEntry 5 }

         hwXponOntIpconfigDscpProfIndex OBJECT-TYPE
               SYNTAX      Integer32
               MAX-ACCESS  read-write
               STATUS      current
               DESCRIPTION
                       "The dscp profile index of the ONT ip host, and the range is 1~50"
               ::= { hwXponOntIpConfigEntry 6 }                    
                        
          hwXponOntIpAddress  OBJECT-TYPE
                SYNTAX     IpAddress
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                "ONT ip address."
                ::= { hwXponOntIpConfigEntry 7 }     
                      
          hwXponOntNetMask  OBJECT-TYPE
                SYNTAX     IpAddress
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                    "Subnet mask."
                ::= { hwXponOntIpConfigEntry 8 }   
                 
         hwXponOntNetGateway  OBJECT-TYPE
                SYNTAX     IpAddress
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                    "The default network gateway."
                ::= { hwXponOntIpConfigEntry 9 }
         
         hwXponOntMasterDNS  OBJECT-TYPE
                SYNTAX     IpAddress
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                    "The active DNS address."
                ::= { hwXponOntIpConfigEntry 10 }       
                        
         hwXponOntSlaveDNS  OBJECT-TYPE
                SYNTAX     IpAddress
                MAX-ACCESS   read-write
                STATUS      current
                DESCRIPTION
                    "The slave DNS address."
                ::= { hwXponOntIpConfigEntry 11 } 
         
         hwXponOntIpConfigPppoeAccountMode          OBJECT-TYPE
               SYNTAX     INTEGER{
                          oltcfg(1),
                          ontcfg(2),
                          invalid(-1)
                          }
               MAX-ACCESS   read-write
               STATUS      current
               DESCRIPTION
                   "The input method of pppoe user account.
                    Options:
                    1. oltcfg(1)              - Indicates the user account is input on the ont
                    2. ontcfg(2)              - Indicates the user account is input on the olt
                    3. invalid(-1)            - Indicates that the query fails
                   "       
               DEFVAL { 2 }
               ::= { hwXponOntIpConfigEntry 12 }
          
         hwXponOntIpConfigPppoeUserName OBJECT-TYPE
               SYNTAX OCTET STRING (SIZE (0..64))
               MAX-ACCESS read-write
               STATUS current
               DESCRIPTION
                   "The username of the pppoe allocation mode."
               ::= { hwXponOntIpConfigEntry 13 }
                
         hwXponOntIpConfigPppoePassword OBJECT-TYPE
               SYNTAX OCTET STRING (SIZE (0..64))
               MAX-ACCESS read-write
               STATUS current
               DESCRIPTION
                   "The password of the pppoe allocation mode."
               ::= { hwXponOntIpConfigEntry 14 }         
         
         hwXponOntIpconfigDhcpReset     OBJECT-TYPE
              SYNTAX      INTEGER
              {
                 reset(1),
                 invalid(-1)
              }
              MAX-ACCESS  read-write	
	      STATUS      current
	      DESCRIPTION
                  "This object is used for resetting the ip host in the DHCP mode.
                  Options:
                   1. reset(1)            - DHCP reset in the DHCP mode
                   2. invalid(-1)         - invalid 
                   "
              ::= { hwXponOntIpConfigEntry 15 }  
                        
         hwXponOntIpconfigRowStatus OBJECT-TYPE
              SYNTAX RowStatus
              MAX-ACCESS read-create
              STATUS current
              DESCRIPTION
                  "The hwXponOntIpconfigRowStatus is used to create a new row
                   or to modify or delete an existing row in this table.
                  "
              ::= { hwXponOntIpConfigEntry 16 }                  
  
  --Table hwXponOntCapabilityInfoTable
        hwXponOntCapabilityInfoTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntCapabilityInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The ont info table. This table is used to query capability of the ont.
                 The indexes of this table are ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 18 }

        hwXponOntCapabilityInfoEntry OBJECT-TYPE
            SYNTAX     HwXponOntCapabilityInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The ont info table. This table is used to query capability of the ont.
                 The indexes of this entry are ifIndex and hwXponOntIndex.
                "
            INDEX       { ifIndex, hwXponOntIndex}
            ::= { hwXponOntCapabilityInfoTable 1 }

        HwXponOntCapabilityInfoEntry  ::=
            SEQUENCE
                {
                hwXponOntSupportVoipCfgMethod          BITS,
                hwXponOntSupportVoipSignalProtocol     BITS
                }
        
        hwXponOntSupportVoipCfgMethod OBJECT-TYPE
            SYNTAX     BITS
                {
                omcioam(0),
                configurationFile(1),
                tr069(2)
                }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "This parameter indicates the voip configuration mode that the ONT supports.
                 This BITS structure can report the following status:
                 1.  omcioam(0)                  - This bit position positively reports that the ONT supports omci/oam configuration mode
                 2.  configurationFile(ftp)(1)   - This bit position positively reports that the ONT supports configurationFile(ftp) configuration mode
                 3.  tr069(2)                    - This bit position positively reports that the ONT supports tr069 configuration mode                 
                 "
            ::= { hwXponOntCapabilityInfoEntry  1 }
         
         hwXponOntSupportVoipSignalProtocol OBJECT-TYPE
            SYNTAX     BITS
                {
                sip(0),
                h248(1),
                mgcp(2)
                }
            MAX-ACCESS read-only
            STATUS     current
            DESCRIPTION
                "This parameter indicates the voip signal protocol that the ONT supports.
                 This BITS structure can report the following status:
                 1.  sip(0)          - This bit position positively reports that the ONT supports SIP Protocol
                 2.  h248(1)         - This bit position positively reports that the ONT supports H.248 Protocol
                 3.  mgcp(2)         - This bit position positively reports that the ONT supports MGCP Protocol
                 "
            ::= { hwXponOntCapabilityInfoEntry  2 } 
                        
  --Table hwXponOntPowerSheddingStatusQueryTable  begin
        hwXponOntPowerSheddingStatusQueryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntPowerSheddingStatusQueryEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponOntPowerSheddingStatusQueryTable is used to query the power shedding status of the ONT.
                 The indexes of this table are ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonControlObjects 19 }

        hwXponOntPowerSheddingStatusQueryEntry OBJECT-TYPE
            SYNTAX HwXponOntPowerSheddingStatusQueryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponOntPowerSheddingStatusQueryTable is used to query the power shedding status of the ONT.
                 The indexes of this entry are ifIndex and hwXponOntIndex.
                "
            INDEX       { ifIndex, hwXponOntIndex }
            ::= { hwXponOntPowerSheddingStatusQueryTable 1 }

        HwXponOntPowerSheddingStatusQueryEntry ::=
            SEQUENCE
                {
                hwXponDeviceOntPowerSheddingStatus    Integer32
                }
		
        hwXponDeviceOntPowerSheddingStatus OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Binary indication of power shedding status for each shedding class.
                 The ONU sets each bit to 1 when power shedding is active, and clears it to 0 when the service is restored.
                 bit0:      Data class
		         bit1:      Voice class
		         bit2:      Video overlay class
		         bit3:      Video return class
		         bit4:      DSL class
		         bit5:      ATM class
		         bit6:      CES class
		         bit7:      Frame class
		         bit8:      Sdh-sonet class
		         bit[15,9]: Reserved and set to 0
		         others:    Unconcern  
                "
            ::={ hwXponOntPowerSheddingStatusQueryEntry 1 }
            
  --Table hwXponOntPowerSheddingStatusQueryTable  end

  --Table hwGponDeviceOntCapabilityInfoTable  begin
        hwGponDeviceOntCapabilityInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponDeviceOntCapabilityInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The ont capability table. This table is used to query capability of the gpon ont.
                 The indexes of this table are ifIndex and hwGponDeviceOntObjIndex.
                "
            ::= { hwXponCommonControlObjects 20 }

        hwGponDeviceOntCapabilityInfoEntry OBJECT-TYPE
            SYNTAX HwGponDeviceOntCapabilityInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The ont capability table. This table is used to query capability of the gpon ont.
                 The indexes of this entry are ifIndex and hwGponDeviceOntObjIndex.
                "
            INDEX       { ifIndex, hwGponDeviceOntObjIndex }
            ::= { hwGponDeviceOntCapabilityInfoTable 1 }

        HwGponDeviceOntCapabilityInfoEntry ::=
            SEQUENCE
                { 
                hwGponDeviceOntCapInfoEquipmentID          OCTET STRING,
                hwGponDeviceOntCapInfoUplinkPonPortNum     Integer32,
                hwGponDeviceOntCapInfoPotsPortNum          Integer32,
                hwGponDeviceOntCapInfoEthPortNum           Integer32,
                hwGponDeviceOntCapInfoTdmPortNum           Integer32,
                hwGponDeviceOntCapInfoMocaPortNum          Integer32,
                hwGponDeviceOntCapInfoCatvAniPortNum       Integer32,
                hwGponDeviceOntCapInfoCatvUniPortNum       Integer32,
                hwGponDeviceOntCapInfoGemPortNum           Integer32,
                hwGponDeviceOntCapInfoIpConfigurationMode  INTEGER,
                hwGponDeviceOntCapInfoTrafficSchedulerNum  Integer32,
                hwGponDeviceOntCapInfoTcontNum             Integer32,
                hwGponDeviceOntCapInfoTypeOfFlowControl    INTEGER,
                hwGponDeviceOntCapInfoDeviceType           INTEGER,
                hwGponDeviceOntCapInfoPowerSupplyControl   INTEGER,
                hwGponDeviceOntCapInfoExtOmciMsgFormat     INTEGER,
                hwGponDeviceOntCapInfoNumberOfPqInTcont    OCTET STRING,
                hwGponDeviceOntCapInfoEthOamSupport        Integer32,
                hwGponDeviceOntCapInfoVdslPortNum          Integer32
                }

         hwGponDeviceOntCapInfoEquipmentID OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..20))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The ONT equipment ID."
            ::= { hwGponDeviceOntCapabilityInfoEntry 1 }
         
         hwGponDeviceOntCapInfoUplinkPonPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of uplink PON ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 2 }
         
         hwGponDeviceOntCapInfoPotsPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of POTS ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 3 }
         
         hwGponDeviceOntCapInfoEthPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of ETH ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 4 }
         
         hwGponDeviceOntCapInfoTdmPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of TDM ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 5 }

         hwGponDeviceOntCapInfoMocaPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of MOCA ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 6 }

         hwGponDeviceOntCapInfoCatvAniPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of CATV ANI ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 7 }

         hwGponDeviceOntCapInfoCatvUniPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of CATV UNI ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 8 }

         hwGponDeviceOntCapInfoGemPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of GEM ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 9 }

         hwGponDeviceOntCapInfoIpConfigurationMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                notsupport(1),
                support(2),
                dhcp(3),
                static(4),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The mode of IP configuration.
                 Options:
                 1. notsupport(1)          - Not support
                 2. support(2)             - Support
                 3. dhcp(3)                - DHCP mode
                 4. static(4)              - Static mode
                 5. invalid(-1)            - Indicates the invalid value
                "
            ::= { hwGponDeviceOntCapabilityInfoEntry 10 }

         hwGponDeviceOntCapInfoTrafficSchedulerNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of the traffic scheduler."
            ::= { hwGponDeviceOntCapabilityInfoEntry 11 }

         hwGponDeviceOntCapInfoTcontNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of T-CONTs.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 12 }

         hwGponDeviceOntCapInfoTypeOfFlowControl OBJECT-TYPE
            SYNTAX INTEGER
                {
                pq(1),
                gemportcar(2),
                flowcar(3),
                gemportcarandpq(4),
                gemportcarorpq(5),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The type of the ONT flow control.
                 Options:
                 1. pq(1)                - PQ 
                 2. gemportcar(2)        - GEMPORT CAR
                 3. flowcar(3)           - FLOW CAR
                 4. gemportcarandpq(4)   - GEMPORT CAR and PQ
                 5. gemportcarorpq(5)    - GEMPORT CAR or PQ
                 6. invalid(-1)          - Indicates the invalid value
                "
            ::= { hwGponDeviceOntCapabilityInfoEntry 13 }

         hwGponDeviceOntCapInfoDeviceType OBJECT-TYPE
            SYNTAX INTEGER
                {
                sfu(1),
                hgu(2),
                sbu(3),
                cbu(4),
                mdu(5),
                mtu(6),    
                sfu-1fe-1pots(7),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the ONT type.
                 Options:
                 1. sfu(1)            - The ONT type is SFU 
                 2. hgu(2)            - The ONT type is HGU
                 3. sbu(3)            - The ONT type is SBU
                 4. cbu(4)            - The ONT type is CBU
                 5. mdu(5)            - The ONT type is MDU
                 6. mtu(6)            - The ONT type is MTU    
                 7. sfu-1fe-1pots(7)  - The ONT type is SFU(1FE+1POTS) 
                 8. invalid(-1)       - Indicates the invalid value
                "
            ::= { hwGponDeviceOntCapabilityInfoEntry 14 }

         hwGponDeviceOntCapInfoPowerSupplyControl OBJECT-TYPE
            SYNTAX INTEGER
                {
                notsupport(1),
                support(2),
                txrxcoupling(3),
                txrxindependent(4),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates if the ONT supports the power control.
                 Options:
                 1. notsupport(1)            - The ONT doesn't support the power control 
                 2. support(2)               - The ONT supports the power control
                 3. txrxcoupling(3)          - The ONT supports the power control.The TX and RX are coupling
                 4. txrxindependent(4)       - The ONT supports the power control.The TX and RX are independent
                 5. invalid(-1)              - Indicates the invalid value
                "
            ::= { hwGponDeviceOntCapabilityInfoEntry 15 }

         hwGponDeviceOntCapInfoExtOmciMsgFormat OBJECT-TYPE
            SYNTAX INTEGER
                {
                notsupport(1),
                support(2),
                invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates if the ONT supports Extended OMCI message format.
                 Options:
                 1. notsupport(1)                         - The ONT doesn't support the Extended OMCI message format 
                 2. support(2)                            - The ONT supports the Extended OMCI message format
                 3. invalid(-1)                           - Indicates the invalid value
                "
            ::= { hwGponDeviceOntCapabilityInfoEntry 16 }

         hwGponDeviceOntCapInfoNumberOfPqInTcont OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..512))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of PQs in T-CONT."
            ::= { hwGponDeviceOntCapabilityInfoEntry 17 }
                      
         hwGponDeviceOntCapInfoEthOamSupport OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates if the ONT supports the type of ETHOAM by each bit.
                 The ONT sets each bit to 1 when the ONT supports the type of ETHOAM.
                 bit0:      Y.1731
		 bit[31,1]: Reserved and set to 0
		"
            ::= { hwGponDeviceOntCapabilityInfoEntry 19 }
            
        hwGponDeviceOntCapInfoVdslPortNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of VDSL ports.
                -1 indicates the invalid value."
            ::= { hwGponDeviceOntCapabilityInfoEntry 20 }
                       
  --Table hwGponDeviceOntCapabilityInfoTable  end
 
 --Table hwXponDeviceOtdrTestTable
        hwXponDeviceOtdrTestTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponDeviceOtdrTestEntry 
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used for the OTDR testing.
                 The index of this table is ifIndex.
                "
            ::= { hwXponCommonControlObjects 21 }

        hwXponDeviceOtdrTestEntry OBJECT-TYPE
            SYNTAX HwXponDeviceOtdrTestEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used for the OTDR testing.
                 The index of this entry is ifIndex.
                "
            INDEX { ifIndex }
            ::= { hwXponDeviceOtdrTestTable 1 }
		
        HwXponDeviceOtdrTestEntry ::=
            SEQUENCE { 
            hwXponDeviceOtdrTestOper                  INTEGER,                             
            hwXponDeviceOtdrTestStatus                INTEGER,
            hwXponDeviceOtdrTestResult                Integer32,
            hwXponDeviceOtdrTestWave                  INTEGER,
            hwXponDeviceOtdrTestPulseWidth            Integer32,
            hwXponDeviceOtdrClockRate                 INTEGER,
            hwXponDeviceOtdrTestSignalType            INTEGER,
            hwXponDeviceOtdrTestFreq                  INTEGER,               
            hwXponDeviceOtdrTestFilterSwitch          INTEGER,
            hwXponDeviceOtdrTestFilterMode            INTEGER,
            hwXponDeviceOtdrTestFilterPara            OCTET STRING,
            hwXponDeviceOtdrTestAverageTime           INTEGER,
            hwXponDeviceOtdrTestOpticalFibreLength    Integer32,
            hwXponDeviceOtdrTestDownPowerUsage        Integer32,
            hwXponDeviceOtdrUpTestMode                INTEGER,
            hwXponDeviceOtdrServerIpAddress           IpAddress,
            hwXponDeviceOtdrProtocol                  INTEGER,
            hwXponDeviceOtdrUserName                  OCTET STRING,
            hwXponDeviceOtdrPassword                  OCTET STRING,
            hwXponDeviceOtdrFilePath                  OCTET STRING,
            hwXponDeviceOtdrTestFilterOrder           INTEGER
            }

        hwXponDeviceOtdrTestOper OBJECT-TYPE
            SYNTAX INTEGER 
                {
                 start(1),
                 stop(2),
                 invalid(-1)      
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "OTDR test operation of a port.
                 Options: 
                 1. start(1)                - Indicates that the OTDR test is started on the port.
                 2. stop(2)                 - Indicates that the OTDR test is stopped on the port.
                 3. invalid(-1)             - Indicates that the query fails or no information is detected.
                "
            ::= { hwXponDeviceOtdrTestEntry 1 }

        hwXponDeviceOtdrTestStatus OBJECT-TYPE
            SYNTAX INTEGER 
                {
                 untested(1),
                 testing(2),
                 testFinish(3)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The state of OTDR test.
                 Options: 
                 1. untested (1)                - Indicates that the OTDR test is not performed for the port.
                 2. testing (2)                 - Indicates that the OTDR test is being performed for the port.
                 3. testFinish (3)              - Indicates that the OTDR test is complete.
                "
             ::= { hwXponDeviceOtdrTestEntry 2 }
             
        hwXponDeviceOtdrTestResult OBJECT-TYPE
            SYNTAX  Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the result of OTDR test.
                 Options:
                 1. Success(1)                               - Indicates that the OTDR test successfully.
                 2. FtpFail(2)                               - Indicates that the result file of OTDR test upload failed.
                 3. BoardFail(3)                             - Indicates that the board is faulty during the test.
                 4. OpticalModuleFail(4)                     - Indicates that the optical module is faulty during the test.
                 5. PortShutdown(5)                          - Indicates that the port is shutdown during the test.
                 6. SystemSwitchOver(6)                      - Indicates that the system switchover occurs during the test.
                 7. unknow(240)                              - Indicates that an unknown error occurs during the test.
                 8. invalid(-1)                              - Indicates that the query fails or no information is detected.
                "
            ::= { hwXponDeviceOtdrTestEntry 3 }    
            		
        hwXponDeviceOtdrTestWave OBJECT-TYPE
            SYNTAX INTEGER 
                {
                 upWave(1),
                 downWave(2),
                 all(3),
                 invalid(-1)
                }             
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Test wave for the OTDR test.
                 Options:
                 1.upWave(1)                   - Indicates that the upstream wavelength is selected for the OTDR test.
                 2.downWave(2)                 - Indicates that the downstream wavelength is selected for the OTDR test.
                 3.all(3)                      - Indicates that both the upstream and downstream wavelengths are selected for the OTDR test.
                 4.invalid(-1)                 - Indicates that the query fails or no information is detected.
                 Currently, only the downstream wavelength can be selected for the OTDR test.
                 Default:downWave(2).
                "
            ::= { hwXponDeviceOtdrTestEntry 4 }

        hwXponDeviceOtdrTestPulseWidth OBJECT-TYPE
            SYNTAX Integer32
            UNITS  "0.01ns"
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Pulse width of test signals.
                 Unit: 0.01ns
                 The value of this node can be -1 indicates that query fails or no information is detected.
                 Default:2500.
                "
            ::= { hwXponDeviceOtdrTestEntry 5 }
		
        hwXponDeviceOtdrClockRate OBJECT-TYPE
            SYNTAX INTEGER 
                {
                 fortyMHz(1),
                 eightyMHz(2),
                 invalid(-1)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Clock rate of the OTDR test.
                 Options:
                 1.fortyMHz(1)                      - Indicates that the clock rate of the OTDR test is 40 MHz, at which, the system works in the low power consumption mode.
                 2.eightyMHz(2)                     - Indicates that the clock rate of the OTDR test is 80 MHz, at which, the system works in the high resolution mode.
                 3.invalid(-1)                      - Indicates that the query fails or no information is detected.
                 The clock rate is determined by hardware.
                "
            ::= { hwXponDeviceOtdrTestEntry 6 }
		
        hwXponDeviceOtdrTestSignalType OBJECT-TYPE
            SYNTAX INTEGER
                {
                 singlePulse(1),
                 singlePnSequence(2),
                 singleGrayCodeSequence(3),
                 consecutivePnSequence(4),
                 consecutiveGrayCodeSequence(5),
                 invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Type of sent OTDR test signals.
                 Options: 
                 1.singlePulse(1)                       - Indicates single pulse
                 2.singlePnSequence(2)                  - Indicates single PN sequence.
                 3.singleGrayCodeSequence(3)            - Indicates single gray code sequence.
                 4.consecutivePnSequence(4)             - Indicates consecutive PN sequence.
                 5.consecutiveGrayCodeSequence(5)       - Indicates consecutive gray code sequence.
                 6.invalid(-1)                          - Indicates that the query fails or no information is detected.
                 Default: consecutiveGrayCodeSequence(5).
                "
            ::= { hwXponDeviceOtdrTestEntry 7 }
		
        hwXponDeviceOtdrTestFreq OBJECT-TYPE
            SYNTAX INTEGER
                {
                 doubleFclk(1),
                 fclk(2),
                 halfFclk(3),
                 quarterFclk(4),
                 eighthFclk(5),
                 invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Sampling frequency of the OTDR test; Fclk indicates the clock frequency.
                 Options: 
                 1.doubleFclk(1)                   - Indicates that the sampling frequency of OTDR test is 2 x Fclk.
                 2.fclk(2)                         - Indicates that the sampling frequency of OTDR test is Fclk.
                 3.halfFclk(3)                     - Indicates that the sampling frequency of OTDR test is 1/2 x Fclk.
                 4.quarterFclk(4)                  - Indicates that the sampling frequency of OTDR test is 1/4 x Fclk.
                 5.eighthFclk(5)                   - Indicates that the sampling frequency of OTDR test is 1/8 x Fclk.
                 6.invalid(-1)                     - Indicates that the query fails or no information is detected.
                 Default:quarterFclk(4).
                "
            ::= { hwXponDeviceOtdrTestEntry 8 }
            
         hwXponDeviceOtdrTestFilterSwitch OBJECT-TYPE
            SYNTAX INTEGER
                {
                 enable(1),
                 disable(2),
                 invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Enable status of the OTDR FIR filter.
                 Options: 
                 1.enable(1)                   - Indicates that the OTDR FIR filter is enabled.
                 2.disable(2)                  - Indicates that the OTDR FIR filter is disabled.
                 3.invalid(-1)                 - Indicates that the query fails or no information is detected.
                 Default:enable(1).
                "
            ::= { hwXponDeviceOtdrTestEntry 9 }
            
         hwXponDeviceOtdrTestFilterMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                 systemDefault(1),
                 issuedParameters(2),
                 invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Parameter configuration principle of the FIR filter.
                 Options: 
                 1.systemDefault(1)                   - Indicates that the system default parameters are used for the FIR filter.
                 2.issuedParameters(2)                - Indicates that the issued parameters are used for the FIR filter.
                 3.invalid(-1)                        - Indicates that the query fails or no information is detected.
                 Default:systemDefault(1).
                "
            ::= { hwXponDeviceOtdrTestEntry 10 } 
       
         hwXponDeviceOtdrTestFilterPara OBJECT-TYPE
            SYNTAX OCTET STRING 
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Parameters of the FIR filter.
                 If the parameter of hwXponDeviceOtdrTestFilterMode is set to the issued parameter,
                 this parameter is forwarded to the OTDR chip.
                 The first character of this node represents the number of parameters. 
                 Starting from the second character, these characters represent parameters.
                 Every four characters (32 bits) from left to right represent a parameter.
                 If a parameter is set with invalid value FFFFFFFF, its default value will be issued. Default values of parameters are as follows: 
                 0x00C5AC58,0x0208F564,0x049C37F2,0x090BF767,0x0F72A0E4,0x17065730,0x1E03A4E2,0x224243C2.
                "
            ::= { hwXponDeviceOtdrTestEntry 11 }  
            
         hwXponDeviceOtdrTestAverageTime OBJECT-TYPE
            SYNTAX INTEGER
                {
                 thirtySeconds(1),
                 oneMinute(2),
                 twoMinutes(3),
                 threeeMinutes(4),
                 sixMinutes(5),
                 thirtyMinutes(6),
                 invalid(-1)                
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Average sampling times for OTDR test.
                 Options: 
                 1.thirtySeconds(1)                 - Indicates that the OTDR test is executed every 30 seconds.               
                 2.oneMinute(2)                     - Indicates that the OTDR test is executed every 1 minute.
                 3.twoMinutes(3)                    - Indicates that the OTDR test is executed every 2 minutes.
                 4.threeeMinutes(4)                 - Indicates that the OTDR test is executed every 3 minutes.
                 5.sixMinutes(5)                    - Indicates that the OTDR test is executed every 6 minutes.
                 6.thirtyMinutes(6)                 - Indicates that the OTDR test is executed every 30 minutes.
                 7.invalid(-1)                      - Indicates that the query fails or no information is detected.
                 Only test time is issued for this node, and the test times are calculated by the board. 
                 The board queries the test status of the optical module after the test time elapses.
                 If the test is complete, the board reads the data. 
                 Otherwise, the board will retry to query the test status of the optical module for three times.
                 If the test is always not complete, the test will be canceled, and a failure message will be returned.
                 Default:thirtySeconds(1).
                "
            ::= { hwXponDeviceOtdrTestEntry 12 }    
            
         hwXponDeviceOtdrTestOpticalFibreLength OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Length of the optical fiber to be tested.
                 Options: 
                 1.(1)                  - Indicates that the length of the optical fiber to be tested is 5 km.
                 2.(2)                  - Indicates that the length of the optical fiber to be tested is 10 km.
                 3.(3)                  - Indicates that the length of the optical fiber to be tested is 20 km.
                 4.(4)                  - Indicates that the length of the optical fiber to be tested is 30 km.
                 5.(5)                  - Indicates that the length of the optical fiber to be tested is 40 km.
                 6.(6)                  - Indicates that the length of the optical fiber to be tested is 80 km.
                 7.(-1)                 - Indicates that the query fails or no information is detected.
                 The length of the optical fiber to be tested is displayed as the value selected by the user. 
                 However, the actual length equals the value selected by the user plus 10 km in background processing.
                 Default:4(30km).
                "
            ::= { hwXponDeviceOtdrTestEntry 13 } 
                
         hwXponDeviceOtdrTestDownPowerUsage OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Modulation depth of downstream test signals
                 Currently, this value can be set to 10, 20, 30, 40, 50 and 100.
                 which indicates that the modulation depth is 10%, 20%, 30%, 40%, 50% and 100%.
                 The vlaue of this node can be -1 indicates that query fails or no information is detected.
                 Default:10
                "
            ::= { hwXponDeviceOtdrTestEntry 14 }     
            
         hwXponDeviceOtdrUpTestMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                 onlineTest(1),
                 quickTest(2),
                 invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Upstream test mode.
                 Options: 
                 1.onlineTest(1)                   - Indicates that the upstream test mode is online test.
                 2.quickTest(2)                    - Indicates that the upstream test mode is quick test.
                 3.invalid(-1)                     - Indicates that the query fails or no information is detected.
                 Based on the selected test mode, the OLT can determine, 
                 work out the regularity of sending Trig signals and configure the optical module.
                 Currently, this node cannot be configured.
                "
            ::= { hwXponDeviceOtdrTestEntry 15 }  
      
         hwXponDeviceOtdrServerIpAddress OBJECT-TYPE
            SYNTAX      IpAddress
            MAX-ACCESS   read-write
            STATUS       current
            DESCRIPTION
                "The destination IP address of the OTDR.
                 The vlaue of this node can be FFFFFFFF indicates that query fails or no information is detected.
                " 
            ::= { hwXponDeviceOtdrTestEntry 16 }  
            
         hwXponDeviceOtdrProtocol OBJECT-TYPE
            SYNTAX INTEGER
                {
                 ftp(1),
                 tftp(2),
                 sftp(3),
                 invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Transfer protocol of test result.
                 Options: 
                 1.ftp(1)                   - Indicates that the test result file transfer mode is ftp.
                 2.tftp(2)                  - Indicates that the test result file transfer mode is tftp.
                 3.sftp(3)                  - Indicates that the test result file transfer mode is sftp.
                 4.invalid(-1)              - Indicates that the query fails or no information is detected.
                 Default:ftp(1).
                "
            ::= { hwXponDeviceOtdrTestEntry 17 }               
                  
        hwXponDeviceOtdrUserName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..64))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The user name of OTDR test.
                "
            ::= { hwXponDeviceOtdrTestEntry 18 }
		
        hwXponDeviceOtdrPassword OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..64))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The password of OTDR test.
                "
            ::= { hwXponDeviceOtdrTestEntry 19 } 
           
        hwXponDeviceOtdrFilePath OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..256))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The file path of OTDR test.
                "
            ::= { hwXponDeviceOtdrTestEntry 20 }   
                     
        hwXponDeviceOtdrTestFilterOrder OBJECT-TYPE
            SYNTAX INTEGER
                {
                 fifteenOrder(1),
                 sixteenOrder(2),
                 invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Value of the FIR filter order.
                 Options: 
                 1.fifteenOrder(1)                   - Indicates that the FIR filter order is fifteen.
                 2.sixteenOrder(2)                   - Indicates that the FIR filter order is sixteen.
                 3.invalid(-1)                       - Indicates that the query fails or no information is detected.
                 Default:fifteenOrder(1).
                "
            ::= { hwXponDeviceOtdrTestEntry 21 }    
                                                  
-- hwXponDeviceOtdrTestTable  end		 

 --Table hwXponOpticsDdmInfoExTable
        hwXponOpticsDdmInfoExTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOpticsDdmInfoExEntry 
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table supports the query of the information about the optical transceiver.
 	         The index of this table is ifIndex.
 	        "
            ::= { hwXponCommonControlObjects 22 }

        hwXponOpticsDdmInfoExEntry OBJECT-TYPE
            SYNTAX HwXponOpticsDdmInfoExEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table supports the query of the information about the optical transceiver.
 	         The index of this entry is ifIndex.
                "
            INDEX { ifIndex }
            ::= { hwXponOpticsDdmInfoExTable 1 }
		
        HwXponOpticsDdmInfoExEntry ::=
            SEQUENCE { 
                     hwXponOpticsDdmInfoExTemperatureHighAlarmThreshold          Integer32,
                     hwXponOpticsDdmInfoExTemperatureLowAlarmThreshold           Integer32,
                     hwXponOpticsDdmInfoExTemperatureHighWarningThreshold        Integer32,
                     hwXponOpticsDdmInfoExTemperatureLowWarningThreshold         Integer32,
                     hwXponOpticsDdmInfoExSupplyVoltageHighAlarmThreshold        Integer32,
                     hwXponOpticsDdmInfoExSupplyVoltageLowAlarmThreshold         Integer32,
                     hwXponOpticsDdmInfoExSupplyVoltageHighWarningThreshold      Integer32,
                     hwXponOpticsDdmInfoExSupplyVoltageLowWarningThreshold       Integer32, 
                     hwXponOpticsDdmInfoExTxBiasCurrentHighAlarmThreshold        Integer32,
                     hwXponOpticsDdmInfoExTxBiasCurrentLowAlarmThreshold         Integer32,
                     hwXponOpticsDdmInfoExTxBiasCurrentHighWarningThreshold      Integer32,
                     hwXponOpticsDdmInfoExTxBiasCurrentLowWarningThreshold       Integer32,
                     hwXponOpticsDdmInfoExTxPowerHighAlarmThreshold              Integer32,
                     hwXponOpticsDdmInfoExTxPowerLowAlarmThreshold               Integer32,
                     hwXponOpticsDdmInfoExTxPowerHighWarningThreshold            Integer32,
                     hwXponOpticsDdmInfoExTxPowerLowWarningThreshold             Integer32,
                     hwXponOpticsDdmInfoExRxPowerHighAlarmThreshold              Integer32,
                     hwXponOpticsDdmInfoExRxPowerLowAlarmThreshold               Integer32,
                     hwXponOpticsDdmInfoExRxPowerHighWarningThreshold            Integer32,
                     hwXponOpticsDdmInfoExRxPowerLowWarningThreshold             Integer32,
                     hwXponOpticsDdmInfoEx10GTxBiasCurrent                       Integer32,
                     hwXponOpticsDdmInfoEx10GTxBiasCurrentHighAlarmThreshold     Integer32,
                     hwXponOpticsDdmInfoEx10GTxBiasCurrentLowAlarmThreshold      Integer32,
                     hwXponOpticsDdmInfoEx10GTxBiasCurrentHighWarningThreshold   Integer32,
                     hwXponOpticsDdmInfoEx10GTxBiasCurrentLowWarningThreshold    Integer32,
                     hwXponOpticsDdmInfoEx10GTxPower                             Integer32,
                     hwXponOpticsDdmInfoEx10GTxPowerHighAlarmThreshold           Integer32,
                     hwXponOpticsDdmInfoEx10GTxPowerLowAlarmThreshold            Integer32,
                     hwXponOpticsDdmInfoEx10GTxPowerHighWarningThreshold         Integer32,
                     hwXponOpticsDdmInfoEx10GTxPowerLowWarningThreshold          Integer32,
                     hwXponOpticsDdmInfoExCatvRxPowerHighAlarmThreshold          Integer32,                     
                     hwXponOpticsDdmInfoExCatvRxPowerLowAlarmThreshold           Integer32,
                     hwXponOpticsDdmInfoExModuleType                             INTEGER,
                     hwXponOpticsDdmInfoExSubType                                INTEGER,
                     hwXponOpticsDdmInfoExPrecision                              Integer32,
                     hwXponOpticsDdmInfoExEncapsulationType                      INTEGER,
                     hwXponOpticsDdmInfoExVendorPN                               OCTET STRING,
                     hwXponOpticsDdmInfoExUsedType                               INTEGER
            }

        hwXponOpticsDdmInfoExTemperatureHighAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001 C(centigrade)"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper alarm threshold of the temperature. 
                 Unit: 0.000001 C(centigrade)
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  1 }

        hwXponOpticsDdmInfoExTemperatureLowAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001 C(centigrade)"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower alarm threshold of the temperature. 
                 Unit: 0.000001 C(centigrade) 
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  2 }

        hwXponOpticsDdmInfoExTemperatureHighWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001 C(centigrade)"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper warning threshold of the temperature. 
                 Unit: 0.000001 C(centigrade) 
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  3 }

        hwXponOpticsDdmInfoExTemperatureLowWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001 C(centigrade)"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower warning threshold of the temperature. 
                 Unit: 0.000001 C(centigrade)
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  4 }

        hwXponOpticsDdmInfoExSupplyVoltageHighAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001V"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper alarm threshold of the voltage. 
                 Unit: 0.000001V
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  5 }

        hwXponOpticsDdmInfoExSupplyVoltageLowAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001V"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower alarm threshold of the voltage. 
                 Unit: 0.000001V
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  6 }

        hwXponOpticsDdmInfoExSupplyVoltageHighWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001V"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper warning threshold of the voltage.
                 Unit: 0.000001V
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  7 }

        hwXponOpticsDdmInfoExSupplyVoltageLowWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001V"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower warning threshold of the voltage. 
                 Unit: 0.000001V
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  8 }

        hwXponOpticsDdmInfoExTxBiasCurrentHighAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper alarm threshold of the bias current. 
                 Unit: 0.000001A 
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  9 }

        hwXponOpticsDdmInfoExTxBiasCurrentLowAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower alarm threshold of the bias current. 
                 Unit: 0.000001A
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  10 }

        hwXponOpticsDdmInfoExTxBiasCurrentHighWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper warning threshold of the bias current. 
                 Unit: 0.000001A
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  11 }

        hwXponOpticsDdmInfoExTxBiasCurrentLowWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower warning threshold of the bias current. 
                 Unit: 0.000001A 
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  12 }

        hwXponOpticsDdmInfoExTxPowerHighAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper alarm threshold of the Tx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  13 }

        hwXponOpticsDdmInfoExTxPowerLowAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower alarm threshold of the Tx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  14 }

        hwXponOpticsDdmInfoExTxPowerHighWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper warning threshold of the Tx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  15 }

        hwXponOpticsDdmInfoExTxPowerLowWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower warning threshold of the Tx power.
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  16 }

        hwXponOpticsDdmInfoExRxPowerHighAlarmThreshold   OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper alarm threshold of the Rx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected                 
                "
            ::= { hwXponOpticsDdmInfoExEntry  17 }

        hwXponOpticsDdmInfoExRxPowerLowAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower alarm threshold of the Rx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected                 
                "
            ::= { hwXponOpticsDdmInfoExEntry  18 }

        hwXponOpticsDdmInfoExRxPowerHighWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper warning threshold of the Rx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected                 
                "
            ::= { hwXponOpticsDdmInfoExEntry  19 }

        hwXponOpticsDdmInfoExRxPowerLowWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower warning threshold of the Rx power. 
                 Unit: 0.000001dBm   
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected                 
                "
            ::= { hwXponOpticsDdmInfoExEntry  20 }
            
        hwXponOpticsDdmInfoEx10GTxBiasCurrent OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Current of the 10G EPON optical transceiver. 
                 Unit: 0.000001A
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  21 }

        hwXponOpticsDdmInfoEx10GTxBiasCurrentHighAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper alarm threshold of the 10G EPON optical transceiver bias current. 
                 Unit: 0.000001A 
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  22 }

        hwXponOpticsDdmInfoEx10GTxBiasCurrentLowAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower alarm threshold of the 10G EPON optical transceiver bias current. 
                 Unit: 0.000001A
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  23 }

        hwXponOpticsDdmInfoEx10GTxBiasCurrentHighWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper warning threshold of the 10G EPON optical transceiver bias current. 
                 Unit: 0.000001A
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  24 }

        hwXponOpticsDdmInfoEx10GTxBiasCurrentLowWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001A"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower warning threshold of the 10G EPON optical transceiver bias current. 
                 Unit: 0.000001A 
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  25 }    
            
        hwXponOpticsDdmInfoEx10GTxPower OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "0.01dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Tx optical power of the 10G EPON optical transceiver. 
                 Unit: 0.01dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  26 }            

        hwXponOpticsDdmInfoEx10GTxPowerHighAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper alarm threshold of the 10G EPON optical transceiver Tx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  27 }

        hwXponOpticsDdmInfoEx10GTxPowerLowAlarmThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower alarm threshold of the 10G EPON optical transceiver Tx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  28 }

        hwXponOpticsDdmInfoEx10GTxPowerHighWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper warning threshold of the 10G EPON optical transceiver Tx power. 
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  29 }

        hwXponOpticsDdmInfoEx10GTxPowerLowWarningThreshold OBJECT-TYPE
            SYNTAX   Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower warning threshold of the 10G EPON optical transceiver Tx power.
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  30 }
                    
         hwXponOpticsDdmInfoExCatvRxPowerHighAlarmThreshold OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Upper AlarmThreshold threshold of the CATV Rx power.
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  31 } 

        hwXponOpticsDdmInfoExCatvRxPowerLowAlarmThreshold OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "0.000001dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Lower AlarmThreshold threshold of the CATV Rx power.
                 Unit: 0.000001dBm
                 Options:
                 1. invalid(0x7FFFFFFF)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  32 }            
            
        hwXponOpticsDdmInfoExModuleType OBJECT-TYPE
            SYNTAX INTEGER
            {
                gpon(1),
                epon(2),
                epon10g(3),
                gpon10g(4),
                wdmpon(5),
                hybridpon(6),
                gponepon(7),
                unknown(8),
                invalid(-1)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "Options:
             1. gpon(1)          - GPON
             2. epon(2)          - EPON
             3. epon10g(3)       - 10G EPON
             4. gpon10g(4)       - 10G GPON
             5. wdmpon(5)        - WDM PON
             6. hybridpon(6)     - hybrid PON
             7. gponepon(7)      - GPON/EPON 
             8. unknown(8)       - Unknown
             9. invalid(-1)      - Indicates that the query fails or no information is detected
            "
            ::= { hwXponOpticsDdmInfoExEntry  33 } 
            
        hwXponOpticsDdmInfoExSubType OBJECT-TYPE
            SYNTAX INTEGER
            {
                classbplus(1),
                classcplus(2),
                classb(3),
                n2a(33),
                n2b(34),
                n1(35),
                px20(65),
                px20plus(66),
                pr30(97),
                pr20(98),
                prx30(99),
                prx20(100),
                classbPlusAndPx20Plus(129),
                classbAndPx20(130),
                unknown(145),
                invalid(-1)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "Options:
             1.  classbplus(1)              - CLASS B+
             2.  classcplus(2)              - CLASS C+
             3.  classb(3)                  - CLASS B
             4.  n2a(33)                    - N2a  
             5.  n2b(34)                    - N2b
             6.  n1(35)                     - N1
             7.  px20(65)                   - PX20
             8.  px20plus(66)               - PX20+
             9.  pr30(97)                   - PR30
             10. pr20(98)                   - PR20
             11. prx30(99)                  - PRX30
             12. prx20(100)                 - PRX20
             13. classbPlusAndPx20Plus(129) - GPON CLASS B+/EPON PX20+
             14. classbAndPx20(130)         - GPON CLASS B/EPON PX20
             15. unknown(145)               - Unknown
             16. invalid(-1)                - Indicates that the query fails or no information is detected
            "
            ::= { hwXponOpticsDdmInfoExEntry  34 } 
            
        hwXponOpticsDdmInfoExPrecision OBJECT-TYPE
            SYNTAX      Integer32
            UNITS       "0.1dBm"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the precision of optical power. 
                 Unit: 0.1dBm
                 Options:
                 1. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  35 } 
      
        hwXponOpticsDdmInfoExEncapsulationType OBJECT-TYPE
            SYNTAX INTEGER
            {
                sff2x5(1),
                sff2x10(2),
                sfp(3),
                xfp(4),
                lxfp(5),
                bosaonboard(6),
                unknown(7),
                invalid(-1)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "Options:
             1. sff2x5(1)               - SFF 2x5
             2. sff2x10(2)              - SFF 2x10
             3. sfp(3)                  - SFP
             4. xfp(4)                  - XFP
             5. lxfp(5)                 - LXFP
             6. bosaonboard(6)          - BOSA ON BOARD  
             7. unknown(7)              - Unknown
             8. invalid(-1)             - Indicates that the query fails or no information is detected
            "
            ::= { hwXponOpticsDdmInfoExEntry  36 } 
            
        hwXponOpticsDdmInfoExVendorPN OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..17))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the part number."
            ::= { hwXponOpticsDdmInfoExEntry  37 } 
                    
        hwXponOpticsDdmInfoExUsedType OBJECT-TYPE
            SYNTAX INTEGER {
                olt(1),
                onu(3),
                catvtriplex(4), 
                unknown(5),
                invalid(-1)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the application scenario of the optical transceiver.
                 Options:
                 1. olt(1)                 - OLT
                 2. onu(3)                 - ONU
                 3. catvtriplex(4)         - CATV TriPlex
                 4. unknown(5)             - Unknown
                 5. invalid(-1)            - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOpticsDdmInfoExEntry  38 }                                
                                                              
-- hwXponOpticsDdmInfoExTable  end	
--Table hwXponOpticsParameterRangeTable  begin
        hwXponOpticsParameterRangeTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOpticsParameterRangeEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The xpon optics parameter range table. This table is used to query range of the optics parameter.
                 The indexes of this table are hwXponOpticalModuleType, hwXponOpticalModuleSubType, hwXponOpticalModuleOltOntType, and hwXponOpticalModuleBandwidthType.
                "
            ::= { hwXponCommonControlObjects 23 }

        hwXponOpticsParameterRangeEntry OBJECT-TYPE
            SYNTAX HwXponOpticsParameterRangeEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The xpon optics parameter range table. This table is used to query range of the optics parameter.
                 The indexes of this entry are hwXponOpticalModuleType, hwXponOpticalModuleSubType, hwXponOpticalModuleOltOntType, and hwXponOpticalModuleBandwidthType.
                "
            INDEX       { hwXponOpticalModuleType, hwXponOpticalModuleSubType, hwXponOpticalModuleOltOntType, hwXponOpticalModuleBandwidthType }
            ::= { hwXponOpticsParameterRangeTable 1 }

        HwXponOpticsParameterRangeEntry ::=
            SEQUENCE
                { 
                hwXponOpticalModuleType                  INTEGER,
                hwXponOpticalModuleSubType               Integer32,
                hwXponOpticalModuleOltOntType            INTEGER,
                hwXponOpticalModuleBandwidthType         INTEGER,
                hwXponOpticalModuleRxPowerValidValueMax  Integer32,
                hwXponOpticalModuleRxPowerValidValueMin  Integer32
                }

        hwXponOpticalModuleType OBJECT-TYPE
            SYNTAX INTEGER
                {
                gpon(2),
                epon(3),
                tengepon(4),
                xgpon(5),
                wdmpon(6),
                mixpon(7),
                gponepondoublemode(8)
                }
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the xpon optical module type.
                 Options:
                 1. gpon(2)                - gpon type.
                 2. epon(3)                - epon type.
                 3. tengepon(4)            - 10gepon type.
                 4. xgpon(5)               - xgpon type.
                 5. wdmpon(6)              - wdmpon type.
                 6. mixpon(7)              - mixpon type.
                 7. gponepondoublemode(8)  - gponepondoublemode type.
                "
            ::= { hwXponOpticsParameterRangeEntry 1 }

        hwXponOpticalModuleSubType OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The xpon optical module sub type.
                GPON:0x2 - CLASS B+,
                     0x3 - CLASS C+,
                     0x4 - CLASS B,
                EPON:0x2 - PX20,
                     0x3 - PX20+,
                10GEPON:0x2 - PR30,
                        0x3 - PR20,
                        0x4 - PRX30,
                        0x5 - PRX20,
                XG-PON:0x2 to 0x5 - reserved,
                       0x6        - N2a,
                       0x7        - N2b,
                       0x8        - N1,
                Gpon and Epon double mode: 0x2 - double mode, compatible with GPON CLASS B+ and EPON PX20+."
            ::= { hwXponOpticsParameterRangeEntry 2 }    

        hwXponOpticalModuleOltOntType OBJECT-TYPE
            SYNTAX INTEGER
                {
                olt(2),
                onu(4)
                }
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the xpon optical module olt ont type.
                 Options:
                 1. olt(2)                 - olt type.
                 2. onu(4)                 - onu type.
                "
            ::= { hwXponOpticsParameterRangeEntry 3 }

        hwXponOpticalModuleBandwidthType OBJECT-TYPE
            SYNTAX INTEGER
                {
                bw1g(1),
                bw10g(2)
                }
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the xpon optical module bandwidth type.
                 Options:
                 1. bw1g(1)               - bw1g type.
                 2. bw10g(2)              - bw10g type.
                "
            ::= { hwXponOpticsParameterRangeEntry 4 }

         hwXponOpticalModuleRxPowerValidValueMax OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The xpon optical module Rx power maximum valid value, units of 0.01 dBm."
            ::= { hwXponOpticsParameterRangeEntry 5 }
 
         hwXponOpticalModuleRxPowerValidValueMin OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The xpon optical module Rx power minimum valid value, units of 0.01 dBm."
            ::= { hwXponOpticsParameterRangeEntry 6 }                                               
--Table hwXponOpticsParameterRangeTable  end 

--Table hwGponInteroperModeConfigTable
        hwGponInteroperModeConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponInteroperModeConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to configure the interoperability mode of the ONT.
                 The indexes of this table are hwGponInteroperModeOntVenderIDInfoIndex, hwGponInteroperModeOntEquipmentIDInfoIndex and hwGponInteroperModeOntSoftwareVersionInfoIndex.
                "
            ::= { hwXponCommonControlObjects 24 }

        hwGponInteroperModeConfigEntry OBJECT-TYPE
            SYNTAX HwGponInteroperModeConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to configure the interoperability mode of the ONT.
                 The indexes of this entry are hwGponInteroperModeOntVenderIDInfoIndex, hwGponInteroperModeOntEquipmentIDInfoIndex and hwGponInteroperModeOntSoftwareVersionInfoIndex.
                "
            INDEX       { hwGponInteroperModeOntVenderIDInfoIndex, hwGponInteroperModeOntEquipmentIDInfoIndex, hwGponInteroperModeOntSoftwareVersionInfoIndex }
            ::= { hwGponInteroperModeConfigTable 1 }
            
        HwGponInteroperModeConfigEntry ::=
            SEQUENCE
                {
                hwGponInteroperModeOntVenderIDInfoIndex           OCTET STRING,
                hwGponInteroperModeOntEquipmentIDInfoIndex        OCTET STRING,
                hwGponInteroperModeOntSoftwareVersionInfoIndex    OCTET STRING,
                hwGponInteroperModeSwitch                         INTEGER,
                hwGponInteroperModeOntMutlicastAuthModeSwitch     INTEGER,
                hwGponInteroperModeActiveMode                     INTEGER,
                hwGponInteroperModeConfigRowStatus                RowStatus
                }
                
        hwGponInteroperModeOntVenderIDInfoIndex OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..4))
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "This object indicates the vender ID of the ONT.
                "
            ::= { hwGponInteroperModeConfigEntry 1 }
            
        hwGponInteroperModeOntEquipmentIDInfoIndex OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..20))
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "This object indicates the equipment ID of the ONT.
                 0 indicates that the equipment ID of the ONT is not specified.
                "
            ::= { hwGponInteroperModeConfigEntry 2 }
            
        hwGponInteroperModeOntSoftwareVersionInfoIndex OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..14))
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "This object indicates the software version of the ONT.
                 0 indicates that the software version of the ONT is not specified.
                "
            ::= { hwGponInteroperModeConfigEntry 3 }
        
        hwGponInteroperModeSwitch OBJECT-TYPE
            SYNTAX INTEGER
                {
                itu-t(1),
                ctc(2),
                eric-v1(3),
                eric-v2(4),
                itu-t-g984(5),
                itu-t-g988(6)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The switch mode of gpon ONT interoperability.
                 Options:
                 1. itu-t(1)      - The switch mode of gpon ONT interoperability is itu-t
                 2. ctc(2)        - The switch mode of gpon ONT interoperability is ctc
                 3. eric-v1(3)       - The switch mode of gpon ONT interoperability is eric-v1
                 4. eric-v2(4)   - The switch mode of gpon ONT interoperability is eric-v2
                 5. itu-t-g984(5)- The switch mode of gpon ONT interoperability is itu-t-g984
                 6. itu-t-g988(6)- The switch mode of gpon ONT interoperability is itu-t-g988
                "
            ::= { hwGponInteroperModeConfigEntry 4 }

        hwGponInteroperModeOntMutlicastAuthModeSwitch OBJECT-TYPE
            SYNTAX INTEGER
                {
                ont-control(1),
                olt-control(2)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The switch mode of gpon ONT multicast authentication.
                 Options:
                 1. ont-control(1)  - The switch mode of gpon ONT multicast authentication is ont-control
                 2. olt-control(2)  - The switch mode of gpon ONT multicast authentication is olt-control
                "
            ::= { hwGponInteroperModeConfigEntry 5 }
            
        hwGponInteroperModeActiveMode OBJECT-TYPE
            SYNTAX INTEGER
                {
                immediate(1),
                next-startup(2),
                invalid(-1)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The active mode of gpon ONT interoperability mode.
                 Options:
                 1. immediate(1)     - Indicates that the ONT takes effect immediately
                 2. next-startup(2)  - Indicates that the ONT takes effect after next startup
                 3. invalid(-1)      - Indicates that the query fails or no information is detected
                "
            ::= { hwGponInteroperModeConfigEntry 6 }
            
        hwGponInteroperModeConfigRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The hwGponInteroperModeConfigRowStatus is used to create a new row
                 or delete an existing row in this table.
                "
            ::= { hwGponInteroperModeConfigEntry 7 }

--Table hwGponInteroperModeConfigTable end

--Table hwXponOltOpticsModuleExtInfoTable
        hwXponOltOpticsModuleExtInfoTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOltOpticsModuleExtInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to query the information about the optical transceiver.
                 The index of this table is ifIndex.
                "
            ::= { hwXponCommonControlObjects 25 }

        hwXponOltOpticsModuleExtInfoEntry OBJECT-TYPE
            SYNTAX HwXponOltOpticsModuleExtInfoEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to query the information about the optical transceiver.
                 The index of this entry is ifIndex.
                "
            INDEX       { ifIndex }
            ::= { hwXponOltOpticsModuleExtInfoTable 1 }
            
        HwXponOltOpticsModuleExtInfoEntry ::=
            SEQUENCE {
                hwXponOltOpticsModuleInfoRateIdentifier           INTEGER,
                hwXponOltOpticsModuleInfoLength50MicronOM3        Integer32,
                hwXponOltOpticsModuleInfoCcBaseState              INTEGER,
                hwXponOltOpticsModuleInfoCcExtState               INTEGER
            }
                
        hwXponOltOpticsModuleInfoRateIdentifier OBJECT-TYPE
            SYNTAX INTEGER {
                unspecified (1),               
                rateId4g2g1gRateSelectAs0As1(2),
                rateId8g4g2gRxRateSelect (3),   
                rateId8g4g2gTxRateSelect (4),   
                rateId8g4g2gRxTxRateSelect (5),
                rateId16g8g4gRxRateSelect (6),  
                rateId16g8g4gRxTxRateSelect (7),
                invalid (-1)                        
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the type of rate select functionality.
                 Options:
                 1. unspecified (1)                    - Unspecified
                 2. rateId4g2g1gRateSelectAs0As1(2)    - 4/2/1G Rate_Select & AS0/AS1
                 3. rateId8g4g2gRxRateSelect (3)       - 8/4/2G Rx Rate_Select only
                 4. rateId8g4g2gTxRateSelect (4)       - 8/4/2G Tx Rate_Select only
                 5. rateId8g4g2gRxTxRateSelect (5)     - 8/4/2G Independent Rx & Tx Rate_select
                 6. rateId16g8g4gRxRateSelect (6)      - 16/8/4G Rx Rate_select only
                 7. rateId16g8g4gRxTxRateSelect (7)    - 16/8/4G Independent Rx, Tx Rate_select
                 8. invalid (-1)                       - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOltOpticsModuleExtInfoEntry 1 }

        hwXponOltOpticsModuleInfoLength50MicronOM3 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the link length supported for 50 um OM3 fiber, units of 10 m."
            ::= { hwXponOltOpticsModuleExtInfoEntry 2 }
          
        hwXponOltOpticsModuleInfoCcBaseState OBJECT-TYPE
            SYNTAX INTEGER {
                correct (1), 
                incorrect(2),
                invalid (-1)    
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the CC_BASE match state.
                 Options:
                 1. correct (1)             - Correct
                 2. incorrect(2)            - Incorrect
                 3. invalid (-1)            - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOltOpticsModuleExtInfoEntry 3 }          
            
        hwXponOltOpticsModuleInfoCcExtState OBJECT-TYPE
            SYNTAX INTEGER {
                correct (1), 
                incorrect(2),
                invalid (-1)    
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the CC_EXT match state.
                 Options:
                 1. correct (1)             - Correct
                 2. incorrect(2)            - Incorrect
                 3. invalid (-1)            - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOltOpticsModuleExtInfoEntry 4 } 
                                      
--Table hwXponOltOpticsModuleExtInfoTable end 

--Table hwGponOntPortServiceCfgTable
        hwGponOntPortServiceCfgTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponOntPortServiceCfgEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to configure the service parameter of the ONT port.
                 The indexes of this table are ifIndex, hwXponOntIndex, hwGponOntifType and hwGponOntifPort.
                "
            ::= { hwXponCommonControlObjects 26 }

        hwGponOntPortServiceCfgEntry OBJECT-TYPE
            SYNTAX HwGponOntPortServiceCfgEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to configure the service parameter of the ONT port.
                 The indexes of this entry are ifIndex, hwXponOntIndex, hwGponOntifType and hwGponOntifPort.
                "
            INDEX { ifIndex, hwXponOntIndex, hwGponOntifType, hwGponOntifPort }
            ::= { hwGponOntPortServiceCfgTable 1 }
            
        HwGponOntPortServiceCfgEntry ::=
            SEQUENCE
                {
                hwGponOntPortNativeVlan            Integer32,
                hwGponOntPortNativeVlanPriority    Integer32,
                hwGponOntPortIgmpForwardMode       INTEGER
                }
                
        hwGponOntPortNativeVlan OBJECT-TYPE
            SYNTAX     Integer32 (-1|0..4095)
            MAX-ACCESS    read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the native VLAN of the port.
                "
            ::= { hwGponOntPortServiceCfgEntry 1 }
            
        hwGponOntPortNativeVlanPriority OBJECT-TYPE
            SYNTAX     Integer32 (-1|0..7)
            MAX-ACCESS    read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the native VLAN priority of the port.
                "
            ::= { hwGponOntPortServiceCfgEntry 2 }
            
        hwGponOntPortIgmpForwardMode OBJECT-TYPE
            SYNTAX    INTEGER{
                disable(1),
                enable(2),
                invalid (-1)
                }
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The object is used to enable or disable the switch of ONT port IGMP forward mode.
                Options:
                1. disable(1)     - Disable the switch of ONT port IGMP forward mode
                2. enable(2)      - Enable the switch of ONT port IGMP forward mode
                3. invalid(-1)    - Indicates that the query fails or no information is detected
                "
            ::= { hwGponOntPortServiceCfgEntry 3 }
  --Table hwGponOntPortServiceCfgTable end
  
  --Table hwXponDeviceOntNoAuthConfigTable
        hwXponDeviceOntNoAuthConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponDeviceOntNoAuthConfigEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponDeviceOntNoAuthConfigTable is used to configure the type of ONT to be no authentication.
                 The indexs of this table are hwXponDeviceOntAccessType and hwXponDeviceOntModel.
                "
            ::= { hwXponCommonControlObjects 27 }

        hwXponDeviceOntNoAuthConfigEntry OBJECT-TYPE
            SYNTAX HwXponDeviceOntNoAuthConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponDeviceOntNoAuthConfigTable is used to configure the type of ONT to be no authentication.
                 The indexs of this table are hwXponDeviceOntAccessType and hwXponDeviceOntModel.
                "
            INDEX       { hwXponDeviceOntAccessType, hwXponDeviceOntModel }
            ::= { hwXponDeviceOntNoAuthConfigTable 1 }

        HwXponDeviceOntNoAuthConfigEntry ::=
            SEQUENCE
                {
                hwXponDeviceOntAccessType              INTEGER,
                hwXponDeviceOntModel                   OCTET STRING,
                hwXponDeviceOntNoAuthRowStatus         RowStatus
                }
                
        hwXponDeviceOntAccessType OBJECT-TYPE
            SYNTAX INTEGER {
                           gpon(1),
                           epon(2)
                           }
            MAX-ACCESS    not-accessible
            STATUS      current
            DESCRIPTION
                "This object indicates the ONT access type.
                 Options:
                 1. gpon(1) - The ONT access type is GPON
                 2. epon(2) - The ONT access type is EPON
                "
            ::= { hwXponDeviceOntNoAuthConfigEntry 1 }

        hwXponDeviceOntModel  OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..20))
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "This object indicates the ONT model."
            ::= { hwXponDeviceOntNoAuthConfigEntry 2 }

        hwXponDeviceOntNoAuthRowStatus OBJECT-TYPE
            SYNTAX     RowStatus
            MAX-ACCESS    read-write
            STATUS current
            DESCRIPTION
                "This object is used to create a new row or to modify or delete an existing row in this table."
            ::= { hwXponDeviceOntNoAuthConfigEntry 3 }
            
  --Table hwXponDeviceOntNoAuthConfigTable end       
                   
  --Table hwGponDevicePotsPortStatisticTable
        hwGponDevicePotsPortStatisticTable OBJECT-TYPE
            SYNTAX      SEQUENCE OF HwGponDevicePotsPortStatisticEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "VOIP perform monitor statistic table. This table is used to query Pots
                 port statistic.
                 The indexes of this table are ifIndex, hwGponDeviceOntObjIndex and
                 hwGponDeviceOntPhyIndex.
                "
            ::= { hwXponCommonStatisticObjects 1 }

        hwGponDevicePotsPortStatisticEntry OBJECT-TYPE
            SYNTAX      HwGponDevicePotsPortStatisticEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "VOIP perform monitor statistic table. This table is used to query Pots
                 port statistic.
                 The indexes of this entry are ifIndex, hwGponDeviceOntObjIndex and
                 hwGponDeviceOntPhyIndex.
                "
            INDEX
                {
                ifIndex,
                hwGponDeviceOntObjIndex,
                hwGponDeviceOntPhyIndex
                }
            ::= { hwGponDevicePotsPortStatisticTable 1 }

        HwGponDevicePotsPortStatisticEntry ::=
            SEQUENCE
                {
                hwGponDeviceOntPhyIndex                       Integer32,
                hwGponDevicePotsStatisticRtpError             Gauge32,
                hwGponDevicePotsStatisticPacketLoss           Gauge32,
                hwGponDevicePotsPortMaxJitter                 Gauge32,
                hwGponDevicePotsPortMaxTimeRTCP               Gauge32,
                hwGponDevicePotsStatisticUnderFlowBuff        Gauge32,
                hwGponDevicePotsStatisticOverFlowBuff         Gauge32,
                hwGponDevicePotsStatisticCallSetupFails       Gauge32,
                hwGponDevicePotsPortCallSetupTimer            Gauge32,
                hwGponDevicePotsStatisticCallTerminateFails   Gauge32,
                hwGponDevicePotsStatisticAnalogPortRelease    Gauge32,
                hwGponDevicePotsPortAnalogPortOffHookTimer    Gauge32
                }

        hwGponDeviceOntPhyIndex OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Port index of the ONT."
            ::={ hwGponDevicePotsPortStatisticEntry 1 }

        hwGponDevicePotsStatisticRtpError OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The statistic of RTP packet errors."
            ::={ hwGponDevicePotsPortStatisticEntry 2 }

        hwGponDevicePotsStatisticPacketLoss OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The statistic of the fraction of packets lost."
            ::={ hwGponDevicePotsPortStatisticEntry 3 }

        hwGponDevicePotsPortMaxJitter OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The maximum jitter identified during the measured interval."
            ::={ hwGponDevicePotsPortStatisticEntry 4 }

        hwGponDevicePotsPortMaxTimeRTCP OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The maximum time between RTCP packets."
            ::={ hwGponDevicePotsPortStatisticEntry 5 }

        hwGponDevicePotsStatisticUnderFlowBuff OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The statistic of times the reassembly buffer underflows."
            ::={ hwGponDevicePotsPortStatisticEntry 6 }

        hwGponDevicePotsStatisticOverFlowBuff OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The statistic of times the reassembly buffer overflows."
            ::={ hwGponDevicePotsPortStatisticEntry 7 }

        hwGponDevicePotsStatisticCallSetupFails OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The statistic of call setup failures."
            ::={ hwGponDevicePotsPortStatisticEntry 8}

        hwGponDevicePotsPortCallSetupTimer OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The longest duration of a single call setup detected during this interval."
            ::={ hwGponDevicePotsPortStatisticEntry 9 }

        hwGponDevicePotsStatisticCallTerminateFails OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The statistic of calls that were terminated with cause."
            ::={ hwGponDevicePotsPortStatisticEntry 10 }

        hwGponDevicePotsStatisticAnalogPortRelease OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The statistic of analog port releases without dialling detected."
            ::={ hwGponDevicePotsPortStatisticEntry 11 }

        hwGponDevicePotsPortAnalogPortOffHookTimer OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The longest period of a single off-hook detected on the analog port."
            ::={ hwGponDevicePotsPortStatisticEntry 12 }

  --Table hwGponOntPerfCurr15MinDataTable
        hwGponOntPerfCurr15MinDataTable OBJECT-TYPE
            SYNTAX      SEQUENCE OF HwGponOntPerfCurr15MinDataEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Ont performance monitor statistic table. This table is used to query ont
                 statistic in the current 15 minutes. The indexes of this table are 
                 ifIndex and hwXponOntIndex.
                "
            ::= { hwXponCommonStatisticObjects 2 }

        hwGponOntPerfCurr15MinDataEntry OBJECT-TYPE
            SYNTAX      HwGponOntPerfCurr15MinDataEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Ont performance monitor statistic table. This table is used to query ont 
                 statistic in the current 15 minutes. The indexes of this entry are 
                 ifIndex and hwXponOntIndex.
                "
            INDEX
                {
                ifIndex,
                hwXponOntIndex
                }
            ::= { hwGponOntPerfCurr15MinDataTable 1 }

        HwGponOntPerfCurr15MinDataEntry ::=
            SEQUENCE
                {
                hwGponOntPerfCurr15MinFecCorrectByte            Integer32,
                hwGponOntPerfCurr15MinFecCorrectCodeWords       Integer32,
                hwGponOntPerfCurr15MinFecUncorrectCodeWords     Integer32,
                hwGponOntPerfCurr15MinFecTotalCodeWords         Integer32,
                hwGponOntPerfCurr15MinFecSeconds                Integer32,
                hwGponOntPerfCurr15MinRangingTimeCount          Counter64,
                hwGponOntPerfCurr15MinTransmittedGemFrames      Counter64,
                hwGponOntPerfCurr15MinXgemKeyErrorCount         Counter64,
                hwGponOntPerfCurr15MinXgemHecErrorCount         Counter64 
                }

        hwGponOntPerfCurr15MinFecCorrectByte OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of bytes that were corrected by 
                the FEC function in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 1 }

        hwGponOntPerfCurr15MinFecCorrectCodeWords OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of code words that were corrected by the FEC 
                function in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 2 }

        hwGponOntPerfCurr15MinFecUncorrectCodeWords OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of code words that were not corrected by the FEC function 
                in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 3 }

        hwGponOntPerfCurr15MinFecTotalCodeWords  OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of total received code words in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 4 }

        hwGponOntPerfCurr15MinFecSeconds OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of seconds in the current 15 minutes, when there 
                was a forward error correction anomaly."
            ::={ hwGponOntPerfCurr15MinDataEntry 5 }   
            
        hwGponOntPerfCurr15MinRangingTimeCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of rangingtime in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 6 }
            
        hwGponOntPerfCurr15MinTransmittedGemFrames OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of transmitted XGEM frames in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 7 }
            
        hwGponOntPerfCurr15MinXgemKeyErrorCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of XGEM key errors in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 8 }   
            
        hwGponOntPerfCurr15MinXgemHecErrorCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of XGEM hec error in the current 15 minutes."
            ::={ hwGponOntPerfCurr15MinDataEntry 9 }

  --Table hwGponOntPerfHis15MinTable
        hwGponOntPerfHis15MinTable OBJECT-TYPE
            SYNTAX      SEQUENCE OF HwGponOntPerfHis15MinEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Ont performance monitor statistic table. This table is used to query ont
                 statistic. The indexes of this table are ifIndex, hwXponOntIndex and 
                 hwGponOntPerfHis15MinNumber.
                "
            ::= { hwXponCommonStatisticObjects 3 }

        hwGponOntPerfHis15MinEntry OBJECT-TYPE
            SYNTAX      HwGponOntPerfHis15MinEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Ont performance monitor statistic table. This table is used to query ont 
                 statistic. The indexes of this entry are ifIndex,hwXponOntIndex and 
                 hwGponOntPerfHis15MinNumber.
                "
            INDEX
                {
                ifIndex,
                hwXponOntIndex,
                hwGponOntPerfHis15MinNumber
                }
            ::= { hwGponOntPerfHis15MinTable 1 }

        HwGponOntPerfHis15MinEntry ::=
            SEQUENCE
                {     
                hwGponOntPerfHis15MinNumber                     Unsigned32,
                hwGponOntPerfHis15MinFecCorrectByte             Integer32,
                hwGponOntPerfHis15MinFecCorrectCodeWords        Integer32,
                hwGponOntPerfHis15MinFecUncorrectCodeWords      Integer32,
                hwGponOntPerfHis15MinFecTotalCodeWords          Integer32,
                hwGponOntPerfHis15MinFecSeconds                 Integer32,
                hwGponOntPerfHis15MinRangingTimeCount           Counter64,
                hwGponOntPerfHis15MinTransmittedGemFrames       Counter64,
                hwGponOntPerfHis15MinXgemKeyErrorCount          Counter64,
                hwGponOntPerfHis15MinXgemHecErrorCount          Counter64
				}        

        hwGponOntPerfHis15MinNumber OBJECT-TYPE
            SYNTAX      Unsigned32 (1)
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Performance data interval number 1 is the most recent previous 
                15 minutes interval. Rang:1."
            ::={ hwGponOntPerfHis15MinEntry 1 }
            
        hwGponOntPerfHis15MinFecCorrectByte OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of bytes that were corrected by 
                the FEC function in this interval."
            ::={ hwGponOntPerfHis15MinEntry 2 }

        hwGponOntPerfHis15MinFecCorrectCodeWords OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of code words that were corrected by the FEC 
                 function in this interval.
                 -1 indicates the invalid value.
                "
            ::={ hwGponOntPerfHis15MinEntry 3 }

        hwGponOntPerfHis15MinFecUncorrectCodeWords OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of code words that were not corrected by the FEC 
                function in this interval.
                -1 indicates the invalid value.
                "
            ::={ hwGponOntPerfHis15MinEntry 4}

        hwGponOntPerfHis15MinFecTotalCodeWords OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of total received code words in this interval.
                 -1 indicates the invalid value.
                "
            ::={ hwGponOntPerfHis15MinEntry 5 }

        hwGponOntPerfHis15MinFecSeconds OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of seconds during this interval, when there 
                was a forward error correction anomaly."
            ::={ hwGponOntPerfHis15MinEntry 6 }   
            
        hwGponOntPerfHis15MinRangingTimeCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of rangingtime in this interval."
            ::={ hwGponOntPerfHis15MinEntry 7 }
            
        hwGponOntPerfHis15MinTransmittedGemFrames OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of transmitted XGEM frames in this interval."
            ::={ hwGponOntPerfHis15MinEntry 8 }
            
        hwGponOntPerfHis15MinXgemKeyErrorCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of XGEM key errors in this interval."
            ::={ hwGponOntPerfHis15MinEntry 9 }   
            
        hwGponOntPerfHis15MinXgemHecErrorCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of XGEM hec error in this interval."
            ::={ hwGponOntPerfHis15MinEntry 10 }
            
       --Table hwGponOntIphostStatsTable
        hwGponOntIphostStatsTable OBJECT-TYPE
            SYNTAX      SEQUENCE OF HwGponOntIphostStatsEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "The ONT IPHOST port statistics table.
                 The index of this table is a combination of ifIndex,
                 hwGponDeviceOntObjIndex.
                "
            ::= { hwXponCommonStatisticObjects 4 }
            
        hwGponOntIphostStatsEntry OBJECT-TYPE
            SYNTAX      HwGponOntIphostStatsEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "The ONT IPHOST port statistics table.
                 The index of this entry is a combination of ifIndex,
                 hwGponDeviceOntObjIndex.
                "
            INDEX
                {
                ifIndex,
                hwGponDeviceOntObjIndex
                }
            ::= { hwGponOntIphostStatsTable 1 }

        HwGponOntIphostStatsEntry ::=
            SEQUENCE
                {     
                hwGponOntIphostEthernetStatisticRecvOctets                    Counter64,
                hwGponOntIphostEthernetStatisticRecvPkts                      Counter64,
                hwGponOntIphostEthernetStatisticRecvMulticastPkts             Counter64,
                hwGponOntIphostEthernetStatisticRecvBroadcastPkts             Counter64,                                              
                hwGponOntIphostEthernetStatisticRecvPkts64Octets              Counter64,
                hwGponOntIphostEthernetStatisticRecvPkts65to127Octets         Counter64,
                hwGponOntIphostEthernetStatisticRecvPkts128to255Octets        Counter64,
                hwGponOntIphostEthernetStatisticRecvPkts256to511Octets        Counter64,
                hwGponOntIphostEthernetStatisticRecvPkts512to1023Octets       Counter64,
                hwGponOntIphostEthernetStatisticRecvPkts1024to1518Octets      Counter64,
                hwGponOntIphostEthernetStatisticRecvPktsOversize              Counter64,
                hwGponOntIphostEthernetStatisticRecvPktsUndersize             Counter64,
                hwGponOntIphostEthernetStatisticRecvFCSErrors                 Counter64,
                hwGponOntIphostEthernetStatisticRecvDropEvents                Counter64,                                         
                hwGponOntIphostEthernetStatisticSendOctets                    Counter64,
                hwGponOntIphostEthernetStatisticSendPkts                      Counter64,
                hwGponOntIphostEthernetStatisticMulticastSendPkts             Counter64,
                hwGponOntIphostEthernetStatisticSendBroadcastPkts             Counter64,                                           
                hwGponOntIphostEthernetStatisticSendPkts64Octets              Counter64,
                hwGponOntIphostEthernetStatisticSendPkts65to127Octets         Counter64,
                hwGponOntIphostEthernetStatisticSendPkts128to255Octets        Counter64,
                hwGponOntIphostEthernetStatisticSendPkts256to511Octets        Counter64,
                hwGponOntIphostEthernetStatisticSendPkts512to1023Octets       Counter64,
                hwGponOntIphostEthernetStatisticSendPkts1024to1518Octets      Counter64,
                hwGponOntIphostEthernetStatisticSendPktsOversize              Counter64,
                hwGponOntIphostEthernetStatisticForwardDropEvents             Counter64,
                hwGponOntIphostEthernetStatisticClear                         INTEGER
                }    
                    
       hwGponOntIphostEthernetStatisticRecvOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received bytes."
            ::= { hwGponOntIphostStatsEntry 1 }
            
        hwGponOntIphostEthernetStatisticRecvPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received frames."
            ::= { hwGponOntIphostStatsEntry 2 }   
      
       hwGponOntIphostEthernetStatisticRecvMulticastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received multicast frames."
            ::= { hwGponOntIphostStatsEntry 3 }
            
       hwGponOntIphostEthernetStatisticRecvBroadcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received broadcast frames."
            ::= { hwGponOntIphostStatsEntry 4 }
            
         hwGponOntIphostEthernetStatisticRecvPkts64Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 64-octet frames"
            ::= { hwGponOntIphostStatsEntry 5 }  
            
      hwGponOntIphostEthernetStatisticRecvPkts65to127Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 65~127-octet frames"
            ::= { hwGponOntIphostStatsEntry 6 }

      hwGponOntIphostEthernetStatisticRecvPkts128to255Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 128~255-octet frames"
            ::= { hwGponOntIphostStatsEntry 7 }

        hwGponOntIphostEthernetStatisticRecvPkts256to511Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 256~511-octet frames."
            ::= { hwGponOntIphostStatsEntry 8 }

        hwGponOntIphostEthernetStatisticRecvPkts512to1023Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 512~1023-octet frames."
            ::= { hwGponOntIphostStatsEntry 9 }

        hwGponOntIphostEthernetStatisticRecvPkts1024to1518Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 1024~1518-octet frames."
            ::= { hwGponOntIphostStatsEntry 10 }
            
        hwGponOntIphostEthernetStatisticRecvPktsOversize OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received oversize frames."
            ::= { hwGponOntIphostStatsEntry 11 }

         hwGponOntIphostEthernetStatisticRecvPktsUndersize OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received undersize frames."
            ::= { hwGponOntIphostStatsEntry 12 }
          
          hwGponOntIphostEthernetStatisticRecvFCSErrors  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received FCS error frames."
            ::= { hwGponOntIphostStatsEntry 13 }
            
          hwGponOntIphostEthernetStatisticRecvDropEvents  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received drop events."
            ::= { hwGponOntIphostStatsEntry 14 }
            
            hwGponOntIphostEthernetStatisticSendOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent bytes."
            ::= { hwGponOntIphostStatsEntry 15 }

        hwGponOntIphostEthernetStatisticSendPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent frames."
            ::= { hwGponOntIphostStatsEntry 16 }
        
        hwGponOntIphostEthernetStatisticMulticastSendPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent multicast frames."
            ::= { hwGponOntIphostStatsEntry 17 }
            
       hwGponOntIphostEthernetStatisticSendBroadcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent broadcast frames."
            ::= { hwGponOntIphostStatsEntry 18 }
            
          hwGponOntIphostEthernetStatisticSendPkts64Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 64-octet frames"
            ::= { hwGponOntIphostStatsEntry 19 }

        hwGponOntIphostEthernetStatisticSendPkts65to127Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 65~127-octet frames"
            ::= { hwGponOntIphostStatsEntry 20 }

        hwGponOntIphostEthernetStatisticSendPkts128to255Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 128~255-octet frames"
            ::= { hwGponOntIphostStatsEntry 21 }

        hwGponOntIphostEthernetStatisticSendPkts256to511Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 256~511-octet frames."
            ::= { hwGponOntIphostStatsEntry 22 }

        hwGponOntIphostEthernetStatisticSendPkts512to1023Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 512~1023-octet frames."
            ::= { hwGponOntIphostStatsEntry 23}

         hwGponOntIphostEthernetStatisticSendPkts1024to1518Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 1024~1518-octet frames."
            ::= { hwGponOntIphostStatsEntry 24 }
            
        hwGponOntIphostEthernetStatisticSendPktsOversize OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent oversize frames."
            ::= { hwGponOntIphostStatsEntry 25 }
  
        hwGponOntIphostEthernetStatisticForwardDropEvents OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent drop events."
            ::= { hwGponOntIphostStatsEntry 26}           
        
        hwGponOntIphostEthernetStatisticClear  OBJECT-TYPE
            SYNTAX INTEGER{
            	clear(1),
                invalid(-1)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object is used to clear the statistics.
                 Options:
                 1. clear(1)                     - Indicates that clear the statistics
                 2. invalid(-1)                 - Indicates that the query fails or no information is detected
		"
            ::= { hwGponOntIphostStatsEntry 60 }           

  --Table hwGponOntPerfEverbeforeDataTable
        hwGponOntPerfEverbeforeDataTable OBJECT-TYPE
            SYNTAX      SEQUENCE OF HwGponOntPerfEverbeforeDataEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Ont performance monitor statistic table. This table is used to query ont 
                 ever before statistic. 
                 The indexes of this table are a combination of ifIndex and hwXponOntIndex.
                " 
             ::= { hwXponCommonStatisticObjects 5 }

        hwGponOntPerfEverbeforeDataEntry OBJECT-TYPE
            SYNTAX      HwGponOntPerfEverbeforeDataEntry
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "Ont performance monitor statistic table. This table is used to query ont 
                 ever before statistic. 
                 The indexes of this entry are a combination of ifIndex and hwXponOntIndex.
                " 
            INDEX
                {
                ifIndex,
                hwXponOntIndex
                }
            ::= { hwGponOntPerfEverbeforeDataTable 1 }  
	    HwGponOntPerfEverbeforeDataEntry ::=
			SEQUENCE
	        {      
	        hwGponOntEverbeforeDownFrameFecCorrectedBytes           Counter64,
                hwGponOntEverbeforeDownFrameFecCorrectedCodeWords       Counter64,
                hwGponOntEverbeforeDownFrameFecUncorrectableCodeWords   Counter64,
                hwGponOntEverbeforeDownFrameTotalRecCodeWords           Counter64,
                hwGponOntEverbeforeDownFrameFecSeconds                  Counter64,
                hwGponOntEverbeforeRangingTimeCount                     Counter64,
                hwGponOntEverbeforeTransmittedGemFrames                 Counter64,
                hwGponOntEverbeforeXgemKeyErrorCount                    Counter64,
                hwGponOntEverbeforeXgemHecErrorCount                    Counter64
	        } 
	    hwGponOntEverbeforeDownFrameFecCorrectedBytes OBJECT-TYPE
            SYNTAX      Counter64 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "Count of bytes that were corrected by 
            the FEC function ever before"
            ::={ hwGponOntPerfEverbeforeDataEntry 1 }  
            
        hwGponOntEverbeforeDownFrameFecCorrectedCodeWords OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of code words that were corrected by the FEC 
                function ever before."
            ::={ hwGponOntPerfEverbeforeDataEntry 2 }

        hwGponOntEverbeforeDownFrameFecUncorrectableCodeWords OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of code words that were not corrected by the FEC 
                function ever before."
            ::={ hwGponOntPerfEverbeforeDataEntry 3}

        hwGponOntEverbeforeDownFrameTotalRecCodeWords OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of total received code words ever before."
            ::={ hwGponOntPerfEverbeforeDataEntry 4 }

        hwGponOntEverbeforeDownFrameFecSeconds OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of seconds ever before, when there 
                was a forward error correction anomaly."
            ::={ hwGponOntPerfEverbeforeDataEntry 5 }   
            
        hwGponOntEverbeforeRangingTimeCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of rangingtime ever before."
            ::={ hwGponOntPerfEverbeforeDataEntry 6 }
            
        hwGponOntEverbeforeTransmittedGemFrames OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of transmitted XGEM frames ever before."
            ::={ hwGponOntPerfEverbeforeDataEntry 7 }
            
        hwGponOntEverbeforeXgemKeyErrorCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of XGEM key errors ever before."
            ::={ hwGponOntPerfEverbeforeDataEntry 8 }   
            
        hwGponOntEverbeforeXgemHecErrorCount OBJECT-TYPE
            SYNTAX      Counter64
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Count of XGEM hec error ever before."
            ::={ hwGponOntPerfEverbeforeDataEntry 9 }
            
    --Table hw10GponOltOpticsModuleInformationTable
        hw10GponOltOpticsModuleInformationTable OBJECT-TYPE
            SYNTAX SEQUENCE OF Hw10GponOltOpticsModuleInformationEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to query the information about the optical transceiver.
 	         The index of this table is ifIndex."     
            ::= { hwXponCommonStatisticObjects 6 }
        
        hw10GponOltOpticsModuleInformationEntry OBJECT-TYPE
            SYNTAX Hw10GponOltOpticsModuleInformationEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to query the information about the optical transceiver.
 	         The index of this entry is ifIndex.
                "
            INDEX       { ifIndex }
            ::= { hw10GponOltOpticsModuleInformationTable 1 }
            
        Hw10GponOltOpticsModuleInformationEntry ::=
         SEQUENCE
                {
                hw10GponOltOpticsModuleInfoIdentifier              		 INTEGER,
                hw10GponOltOpticsModuleInfoExtCLEICode             		 INTEGER,
                hw10GponOltOpticsModuleInfoExtTXRefClock           		 INTEGER,
                hw10GponOltOpticsModuleInfoExtCDRFunc              		 INTEGER,
                hw10GponOltOpticsModuleInfoExtPowerLevel           		 INTEGER,
                hw10GponOltOpticsModuleInfoConnector               		 INTEGER,
                hw10GponOltOpticsModuleInfoEncodingRZ              		 INTEGER,
                hw10GponOltOpticsModuleInfoEncodingNRZ             		 INTEGER,
                hw10GponOltOpticsModuleInfoEncoding8B10B           		 INTEGER,
                hw10GponOltOpticsModuleInfoEncoding64B             		 INTEGER,
                hw10GponOltOpticsModuleInfoBrMin                   		 Integer32,
                hw10GponOltOpticsModuleInfoBrMax                   		 Integer32,
                hw10GponOltOpticsModuleInfoSupportLenSMF           		 Integer32,
                hw10GponOltOpticsModuleInfoSupportLenE50um 	  		 Integer32,
                hw10GponOltOpticsModuleInfoSupportLen50um 	  		 Integer32,
                hw10GponOltOpticsModuleInfoSupportLen62p5um 	  		 Integer32,
                hw10GponOltOpticsModuleInfoSupportLenCopper 	   		 Integer32,
                hw10GponOltOpticsModuleInfoTransmitterSupport       		 INTEGER,
                hw10GponOltOpticsModuleInfoDetector 		  		 INTEGER,
                hw10GponOltOpticsModuleInfoCooledTransmitterDevice 		 INTEGER,
                hw10GponOltOpticsModuleInfoWavelengthControl 	  		 INTEGER,
                hw10GponOltOpticsModuleInfoTransmitterTechnologySupport 	 INTEGER,
                hw10GponOltOpticsModuleInfoVendorName 				 OCTET STRING,
                hw10GponOltOpticsModuleInfoSupportXFIMode 			 INTEGER,
                hw10GponOltOpticsModuleInfoSupportLinesideMode 			 INTEGER,
                hw10GponOltOpticsModuleInfoMaxBitRateSupportedByCDR 	         INTEGER,
                hw10GponOltOpticsModuleInfoVendorOUI 				 Integer32,
                hw10GponOltOpticsModuleInfoVendorPN 				 OCTET STRING,
                hw10GponOltOpticsModuleInfoVendorRev 				 OCTET STRING,
                hw10GponOltOpticsModuleInfoF51 					 Integer32,
                hw10GponOltOpticsModuleInfoWaveTolerance 			 Integer32,
                hw10GponOltOpticsModuleInfoMaxCaseTemperature 			 Integer32,
                hw10GponOltOpticsModuleInfoCcbase 				 Integer32,
                hw10GponOltOpticsModuleInfoMaxDissipation 			 Integer32,
                hw10GponOltOpticsModuleInfoMaxDissipationPowerDownMode 		 Integer32,
                hw10GponOltOpticsModuleInfoMaximum5P0VSupply 			 Integer32,
                hw10GponOltOpticsModuleInfoMaximum3P3VSupply 			 Integer32,
                hw10GponOltOpticsModuleInfoMaximum1P8VSupply 			 Integer32,
                hw10GponOltOpticsModuleInfoMaximum5P2VSupply 			 Integer32,
                hw10GponOltOpticsModuleInfoVendorSN 				 OCTET STRING,
                hw10GponOltOpticsModuleInfoDateCode 				 OCTET STRING,
                hw10GponOltOpticsModuleInfoDiagnoMonitorPowerMeasure 		 INTEGER,
                hw10GponOltOpticsModuleInfoDiagnoMonitorTypeFECBER 		 INTEGER,
                hw10GponOltOpticsModuleInfoEnhancedOptions 			 Integer32,
                hw10GponOltOpticsModuleInfoAuxInputOne                           INTEGER,
                hw10GponOltOpticsModuleInfoAuxInputTwo 				 INTEGER,
                hw10GponOltOpticsModuleInfoCCEXT 				 Integer32,
                hw10GponOltOpticsModuleInfoWavelengthFor2d5G 			 Integer32,
                hw10GponOltOpticsModuleInfoWavelengthFor1d25G 			 Integer32,
                hw10GponOltOpticsModuleInfoVendorSpecific 			 OCTET STRING,
                hw10GponOltOpticsModuleXponType 			         INTEGER,
                hw10GponOltOpticsModuleXponSubType 				 INTEGER,
                hw10GponOltOpticsModuleXponUsedType 				 INTEGER,
                hw10GponOltOpticsModuleXponEncapsulationType 			 INTEGER,
                hw10GponOltOpticsModuleXponTemperatureLevel 			 INTEGER,
                hw10GponOltOpticsModuleXponOPMprecision 		         INTEGER
                }
                
        hw10GponOltOpticsModuleInfoIdentifier OBJECT-TYPE
          SYNTAX INTEGER {
              unknown(0), 
	      gbic(1), 
              solderedToMotherBoard(2),
	      sfp(3), 
              pin300XBI(4), 
	      xenpak(5), 
	      xfp(6), 
	      xff(7), 
	      xfep(8), 
	      xpak(9), 
	      x2(10), 
	      invalid(-1)
            }
          MAX-ACCESS read-only
          STATUS current
          DESCRIPTION
              "This object indicates the type of serial transceiver.
               Options:
               1. unknown(0)                      - Unknown or unspecified
               2. gbic(1)                         - GBIC
               3. solderedToMotherBoard(2)        - Module soldered to motherboard
               4. sfp(3)                          - SFP
               5. pin300XBI(4)                    - PIN300XBI
               6. xenpak(5)                       - XENPAK
               7. xfp(6)                          - XFP
               8. xff(7)                          - XFF
               9. xfep(8)                         - XFEP
               10. xpak(9)                        - XPAK
               11. x2(10)                         - X2
               12. invalid(-1)                    - Indicates that the query fails or no information is detected
               "
          ::= { hw10GponOltOpticsModuleInformationEntry 1 }
            
          hw10GponOltOpticsModuleInfoExtCLEICode OBJECT-TYPE
              SYNTAX INTEGER {
              	  nonExtCLEICode(0), 
		  extCLEICode(1),
		  invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the extclei code.
                 Options:
                 1. nonExtCLEICode(0)                - No CLEI code present in Table 02h
                 2. extCLEICode(1)                   - CLEI code present in Table 02h 
                 3. invalid(-1)                      - Indicates that the query fails or no information is detected
		"
  	  ::= { hw10GponOltOpticsModuleInformationEntry 2 }
  	  
  	hw10GponOltOpticsModuleInfoExtTXRefClock OBJECT-TYPE
  	    SYNTAX INTEGER {
              	  nonExtTXRefClock(0), 
		  extTXRefClock(1),
		  invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "This object indicates the txref clock.
             Options:
             1. nonExtTXRefClock(0)              - TX Ref Clock Input Not Required
             2. extTXRefClock(1)                 - TX Ref Clock Input Required 
             3. invalid(-1)                      - Indicates that the query fails or no information is detected		
             "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 3 }
  	  
  	hw10GponOltOpticsModuleInfoExtCDRFunc OBJECT-TYPE
  	    SYNTAX INTEGER {
              	  nonExtCDRFunc(0),
     		  extCDRFunc(1),
     		  invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "This object indicates the CDR function.
             Options:
             1. nonExtCDRFunc(0)               - Non-CDR version of XFP
             2. extCDRFunc(1)                  - Module with CDR function 
             3. invalid(-1)                    - Indicates that the query fails or no information is detected		
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 4 }
  	  
  	hw10GponOltOpticsModuleInfoExtPowerLevel OBJECT-TYPE
  	    SYNTAX INTEGER {
              	extPowerLevel1(0), 
		extPowerLevel2(1), 
		extPowerLevel3(2), 
		extPowerLevel4(3), 
		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "This object indicates the power level.
             Options:
             1. extPowerLevel1(0)               - Power Level 1 Module (1.5 W max. power dissipation.)
             2. extPowerLevel2(1)               - Power Level 2 Module (2.5W Max) 
             3. extPowerLevel3(2)               - Power Level 3 Module (3.5W max. power dissipation.)
             4. extPowerLevel4(3)               - Power Level 4 Module (>3.5W max. power dissipation.) 
             5. invalid(-1)                     - Indicates that the query fails or no information is detected		
             "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 5 }
  	  
  	hw10GponOltOpticsModuleInfoConnector OBJECT-TYPE
  	    SYNTAX INTEGER {
              	unknown(1), 
		sc(2), 
		style1Connector(3), 
		style2Connector(4), 
		bnctnc(5) ,
		channelCoaxial(6), 
		fiberJack(7), 
		lc(8) ,
		mtrj(9), 
		mu(10), 
		sg(11), 
		opticalPigtail(12), 
		hssdc(13), 
		copperPigtail(14), 
		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
               This object indicates the code for connector type.
               Options:
               1.  unknown(1)                - Unknown
               2.  sc(2)                     - SC
               3.  style1Connector(3)        - Fibre Channel Style 1 copper connector
               4.  style2Connector(4)        - Fibre Channel Style 2 copper connector
               5.  bnctnc(5)                 - BNC/TNC
               6.  channelCoaxial(6)         - Fibre Channel coaxial headers
               7.  fiberJack(7)              - FiberJack
               8.  lc(8)                     - LC
               9.  mtrj(9)                   - MT-RJ
               10. mu(10)                    - MU
               11. sg(11)                    - SG
               12. opticalPigtail(12)        - Optical pigtail
               13. hssdc(13)                 - HSSDC II
               14. copperPigtail(14)         - Copper Pigtail
               15. invalid(-1)               - Indicates that the query fails or no information is detected
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 6 }
  	  
  	hw10GponOltOpticsModuleInfoEncodingRZ OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonEncodingRZ(0), 
  		encodingRZ(1),
  		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "This object indicates the EncodingRZ.
             Options:
             1. nonEncodingRZ(0)             - Encoding Don't Support RZ
             2. encodingRZ(1)                - Encoding Support RZ 
             3. invalid(-1)                  - Indicates that the query fails or no information is detected		
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 7 }
  	  
  	hw10GponOltOpticsModuleInfoEncodingNRZ OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonEncodingNRZ(0), 
  		encodingNRZ(1),
  		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "This object indicates the EncodingNRZ.
             Options:
             1. nonEncodingNRZ(0)       - Encoding Don't Support NRZ
             2. encodingNRZ(1)          - Encoding Support NRZ 
             3. invalid(-1)             - Indicates that the query fails or no information is detected		
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 8 }
  	  
  	hw10GponOltOpticsModuleInfoEncoding8B10B OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonEncoding8B10B(0), 
  		encoding8B10B(1),
  		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "This object indicates the Encoding8B10B.
             Options:
             1. nonEncoding8B10B(0)          - Encoding Don't Support 8B10B
             2. encoding8B10B(1)             - Encoding Support 8B10B
             3. invalid(-1)                  - Indicates that the query fails or no information is detected		
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 9 }
  	  
  	hw10GponOltOpticsModuleInfoEncoding64B OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonEncoding64B(0), 
  		encoding64B(1),
  		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "This object indicates the Encoding64B.
             Options:
             1. nonEncoding64B(0)          - Encoding Don't Support 64B/66B
             2. encoding64B(1)             - Encoding Support 64B/66B 
             3. invalid(-1)                - Indicates that the query fails or no information is detected		
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 10 }
  	  
  	hw10GponOltOpticsModuleInfoBrMin OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
                Minimum bit rate, units of 100 MBits/s
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 11 }
  	
  	hw10GponOltOpticsModuleInfoBrMax OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
                Maximum bit rate, units of 100 MBits/s
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 12 }
  	
  	hw10GponOltOpticsModuleInfoSupportLenSMF OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
                Link length supported for SMF fiber in km 
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 13 }
  	
  	hw10GponOltOpticsModuleInfoSupportLenE50um OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
                Link length supported for EBW 50/125 ��m fiber, units of 2 m
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 14 }
  	
  	hw10GponOltOpticsModuleInfoSupportLen50um OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
                Link length supported for 50/125 ��m fiber, units of 1 m 
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 15 }
  	
  	hw10GponOltOpticsModuleInfoSupportLen62p5um OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
                Link length supported for 62.5/125 ��m fiber, units of 1 m
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 16 }
  	
  	hw10GponOltOpticsModuleInfoSupportLenCopper OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            "
                Link length supported for copper, units of 1m
            "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 17 }
  	  
  	hw10GponOltOpticsModuleInfoTransmitterSupport OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonTunable(0),
     		tunable(1),
     		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the transmitter support.
                 Options:
                 1. nonTunable(0)      - Transmitter Not Support Tunable
                 2. tunable(1)         - Transmitter Support Tunable 
                 3. invalid(-1)        - Indicates that the query fails or no information is detected		
		"
  	  ::= { hw10GponOltOpticsModuleInformationEntry 18 }
  	  
  	hw10GponOltOpticsModuleInfoDetector OBJECT-TYPE
  	    SYNTAX INTEGER {
              	pin(0),
		apd(1),
		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the transmitter support.
                 Options:
                 1. pin(0)              - PIN
                 2. apd(1)              - APD 
                 3. invalid(-1)         - Indicates that the query fails or no information is detected		
		"
  	  ::= { hw10GponOltOpticsModuleInformationEntry 19 }
  	  
  	hw10GponOltOpticsModuleInfoCooledTransmitterDevice OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonSupport(0),
     		support(1),
     		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the wavelength control.
                 Options:
                 1. nonSupport(0)       - Wavelength control NonSupport
                 2. support(1)          - Wavelength control Support 
                 3. invalid(-1)         - Indicates that the query fails or no information is detected		
		"
  	  ::= { hw10GponOltOpticsModuleInformationEntry 20 }
  	  
  	hw10GponOltOpticsModuleInfoWavelengthControl OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonSupport(0),
    		support(1),
    		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the cooled transmitter device.
                 Options:
                 1. nonSupport(0)         - Cooled transmitter device NonSupport
                 2. support(1)            - Cooled transmitter device Support 
                 3. invalid(-1)           - Indicates that the query fails or no information is detected		
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 21 }
  	  
  	hw10GponOltOpticsModuleInfoTransmitterTechnologySupport OBJECT-TYPE
  	    SYNTAX INTEGER {
              	vCSEL850(0),
     		vCSEL1310(1),
		vCSEL1550(2),
		fP1310(3),
		dFB1310(4),
		dFB1550(5),
		eML1310(6),
		eML1550(7),
		cOPPER(8),
		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the transmitter technology support.
                 Options:
                 1. vCSEL850(0)          - vCSEL850
                 2. vCSEL1310(1)         - vCSEL1310 
                 3. vCSEL1550(2)         - vCSEL1550
                 4. fP1310(3)            - fP1310
                 5. dFB1310(4)           - dFB1310
                 6. dFB1550(5)           - dFB1550 
                 7. eML1310(6)           - eML1310
                 8. eML1550(7)           - eML1550 	
                 9. cOPPER(8)            - cOPPER 	
                 10. invalid(-1)         - Indicates that the query fails or no information is detected		
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 22 }
  	
  	hw10GponOltOpticsModuleInfoVendorName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..17))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the SFP vendor name (ASCII)."
            ::= { hw10GponOltOpticsModuleInformationEntry 23 }
  	
  	hw10GponOltOpticsModuleInfoSupportXFIMode OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonSupportXFIMode(0),
     		supportXFIMode(1),
		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the XFI mode.
                 Options:
                 1. nonSupportXFIMode(0)      - Don't Support XFI Loopback Mode
                 2. supportXFIMode(1)         - XFI Loopback Mode Supported
                 3. invalid(-1)               - Indicates that the query fails or no information is detected		
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 24 }
  	  
  	hw10GponOltOpticsModuleInfoSupportLinesideMode OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonSupportLinesideMode(0),
     		supportLinesideMode(1),
		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the XFI mode.
                 Options:
                 1. nonSupportLinesideMode(0)     - Don't Support Lineside Loopback Mode
                 2. supportLinesideMode(1)        - Lineside Loopback Mode Supported 
                 3. invalid(-1)                   - Indicates that the query fails or no information is detected		
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 25 }
  	  
  	hw10GponOltOpticsModuleInfoMaxBitRateSupportedByCDR OBJECT-TYPE
  	    SYNTAX INTEGER {
              	cdrNosupport(0),
	    	maxBitRateSupportedByCDR11p1(1),
	    	maxBitRateSupportedByCDR10p7(3),
	    	maxBitRateSupportedByCDR10p5(7),
	    	maxBitRateSupportedByCDR10p3(15),
	    	maxBitRateSupportedByCDR9p95(31),
	    	invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the max bit rate by CDR.
                 Options:
                 1. cdrNosupport(0)                       - CDR not support
                 2. maxBitRateSupportedByCDR11p1(1)       - CDR support for 11.1 Gb/s 
                 3. maxBitRateSupportedByCDR10p7(3)       - CDR support for 10.7 Gb/s
                 4. maxBitRateSupportedByCDR10p5(7)       - CDR support for 10.5 Gb/s 
                 5. maxBitRateSupportedByCDR10p3(15)      - CDR support for 10.3 Gb/s		
                 6. maxBitRateSupportedByCDR9p95(31)      - CDR support for 9.95 Gb/s	
                 7. invalid(-1)                           - Indicates that the query fails or no information is detected		
		"
  	  ::= { hw10GponOltOpticsModuleInformationEntry 26 }
  	  
  	hw10GponOltOpticsModuleInfoVendorOUI OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Vendor OUI."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 27 }
  	  
  	hw10GponOltOpticsModuleInfoVendorPN OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..17))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the Vendor PN."
          ::= { hw10GponOltOpticsModuleInformationEntry 28 }

	hw10GponOltOpticsModuleInfoVendorRev OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..17))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Vendor rev."
          ::= { hw10GponOltOpticsModuleInformationEntry 29 }
          
        hw10GponOltOpticsModuleInfoF51 OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the Wavelength."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 30 }	
  	  
  	hw10GponOltOpticsModuleInfoWaveTolerance OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Wavelength."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 31 }
  	  
  	hw10GponOltOpticsModuleInfoMaxCaseTemperature OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Maximum Case Temperature in Degrees C."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 32 }
  	  
  	hw10GponOltOpticsModuleInfoCcbase OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Cc_base."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 33 }
  	  
  	hw10GponOltOpticsModuleInfoMaxDissipation OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Maximum Power Dissipation."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 34 }
  	  
  	hw10GponOltOpticsModuleInfoMaxDissipationPowerDownMode OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Maximum Total Power Dissipation in Power Down Mode."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 35 }
  	  
  	hw10GponOltOpticsModuleInfoMaximum5P0VSupply OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Maximum current required by +5V Supply."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 36 }
  	  
  	hw10GponOltOpticsModuleInfoMaximum3P3VSupply OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Maximum current required by +3.3V Supply."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 37 }
  	  
  	hw10GponOltOpticsModuleInfoMaximum1P8VSupply OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Maximum current required by +1.8V Supply."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 38 }
  	  
  	hw10GponOltOpticsModuleInfoMaximum5P2VSupply OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Maximum current required by -5.2V Supply."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 39 }
  	  
  	hw10GponOltOpticsModuleInfoVendorSN OBJECT-TYPE
  	    SYNTAX OCTET STRING (SIZE (0..17))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Vendor SN."
          ::= { hw10GponOltOpticsModuleInformationEntry 40 } 
          
        hw10GponOltOpticsModuleInfoDateCode OBJECT-TYPE
  	    SYNTAX OCTET STRING (SIZE (0..9))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Date code."
          ::= { hw10GponOltOpticsModuleInformationEntry 41 }
          
        hw10GponOltOpticsModuleInfoDiagnoMonitorPowerMeasure OBJECT-TYPE
  	    SYNTAX INTEGER {
              	supportOMA(0),
     		supportAveragePower(1), 
     		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Received power measurement type.
                Options:
                1. supportOMA(0)               - OMA
                2. supportAveragePower(1)      - Average Power 
                3. invalid(-1)                 - Indicates that the query fails or no information is detected		
               "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 42 }
  	  
  	hw10GponOltOpticsModuleInfoDiagnoMonitorTypeFECBER OBJECT-TYPE
  	    SYNTAX INTEGER {
              	nonSupportBER(0),
     		supportBER(1),
     		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the Module Respond to FEC BER.
                Options:
                1. nonSupportBER(0)         - No BER Support
                2. supportBER(1)            - BER Support 
                3. invalid(-1)              - Indicates that the query fails or no information is detected		
               "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 43 }
  	  
  	hw10GponOltOpticsModuleInfoEnhancedOptions OBJECT-TYPE
  	    SYNTAX Integer32 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates which optional enhanced features are implemented.
                 bit                Description of Enhanced Options
                  7                  -Module Supports Optional VPS
                  6                  -Optional Soft TX_DISABLE implemented
                  5                  -Optional Soft P_down implemented
                  4                  -Supports VPS LV regulator mode
                  3                  -Supports VPS bypassed regulator Mode
                  2                  -Active FEC control functions implemented
                  1                  -Wavelength tunability implemented
                  0                  -Optional CMU Support Mode 
                  invalid(-1)        -Indicates that the query fails or no information is detected		
               "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 44 }
  	  
  	  hw10GponOltOpticsModuleInfoAuxInputOne OBJECT-TYPE
  	    SYNTAX INTEGER {
              	auxInputType1(0),
	   	auxInputType2(1),
	   	auxInputType3(2),
	   	auxInputType4(3),
	   	auxInputType5(4),
	   	auxInputType6(5),
	  	auxInputType7(6),
	  	auxInputType8(7),
	  	auxInputType9(8),
	   	auxInputType10(9),
	   	auxInputType11(10),
	  	auxInputType14(13),
	   	auxInputType15(14),
	    	auxInputType16(15),
	    	invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the optics Aux Input One.
                 Options:
                 1. auxInputType1(0)          - Auxiliary monitoring not implemented
                 2. auxInputType2(1)          - APD Bias Voltage 
                 3. auxInputType3(2)          - 1G Tx Bias Current		
                 4. auxInputType4(3)          - TEC Current (mA)
                 5. auxInputType5(4)          - Laser Temperature 
                 6. auxInputType6(5)          - Laser Wavelength		
                 7. auxInputType7(6)          - +5V Supply Voltage
                 8. auxInputType8(7)          - 1G Tx Power 
                 9. auxInputType9(8)          - +1.8V Supply Voltage		
                 10. auxInputType10(9)        - -5.2V Supply Voltage 
                 11. auxInputType11(10)       - +5V Supply Current		
                 12. auxInputType14(13)       - +3.3V Supply Current
                 13. auxInputType15(14)       - +1.8V Supply Current 
                 14. auxInputType16(15)       - -5.2V Supply Current		
                 15. invalid(-1)              - Indicates that the query fails or no information is detected		
		"
  	  ::= { hw10GponOltOpticsModuleInformationEntry 45 }
  	  
  	hw10GponOltOpticsModuleInfoAuxInputTwo OBJECT-TYPE
  	    SYNTAX INTEGER {
              	auxInputType1(0),
	   	auxInputType2(1),
	   	auxInputType3(2),
	   	auxInputType4(3),
	   	auxInputType5(4),
	   	auxInputType6(5),
	  	auxInputType7(6),
	  	auxInputType8(7),
	  	auxInputType9(8),
	   	auxInputType10(9),
	   	auxInputType11(10),
	  	auxInputType14(13),
	   	auxInputType15(14),
	    	auxInputType16(15),
	    	invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
            	"This object indicates the optics Aux Input One.
                 Options:
                 1. auxInputType1(0)          - Auxiliary monitoring not implemented
                 2. auxInputType2(1)          - APD Bias Voltage 
                 3. auxInputType3(2)          - 1G Tx Bias Current		
                 4. auxInputType4(3)          - TEC Current (mA)
                 5. auxInputType5(4)          - Laser Temperature 
                 6. auxInputType6(5)          - Laser Wavelength		
                 7. auxInputType7(6)          - +5V Supply Voltage
                 8. auxInputType8(7)          - 1G Tx Power 
                 9. auxInputType9(8)          - +1.8V Supply Voltage		
                 10. auxInputType10(9)        - -5.2V Supply Voltage 
                 11. auxInputType11(10)       - +5V Supply Current		
                 12. auxInputType14(13)       - +3.3V Supply Current
                 13. auxInputType15(14)       - +1.8V Supply Current 
                 14. auxInputType16(15)       - -5.2V Supply Current		
                 15. invalid(-1)              - Indicates that the query fails or no information is detected		
		"
  	  ::= { hw10GponOltOpticsModuleInformationEntry 46 }
  	  
  	hw10GponOltOpticsModuleInfoCCEXT OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the CC_EXT ."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 47 }
  	  
  	hw10GponOltOpticsModuleInfoWavelengthFor2d5G OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the Wavelength for 2.5G."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 48 }
  	  
  	hw10GponOltOpticsModuleInfoWavelengthFor1d25G OBJECT-TYPE
  	    SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the Wavelength for 1.25G."
  	  ::= { hw10GponOltOpticsModuleInformationEntry 49 }
  	  
  	hw10GponOltOpticsModuleInfoVendorSpecific OBJECT-TYPE
  	    SYNTAX OCTET STRING (SIZE (0..33))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the Vendor specific."
          ::= { hw10GponOltOpticsModuleInformationEntry 50 }
          
        hw10GponOltOpticsModuleXponType OBJECT-TYPE
  	    SYNTAX INTEGER 
  	        {
  	        unknown(1),
  	        gpon(2),
  	        epon(3),
	  	epon10g(4),
	  	xgpon(5),
	  	wdmpon(6),
	  	hybridpon(7),
	  	gponOrEpon(8),
	  	invalid(-1)
	  	}
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the module type.
                 Options:
                 1. unknown(1)            - Unknown
                 2. gpon(2)               - GPON 
                 3. epon(3)               - EPON		
                 4. epon10g(4)            - 10G EPON
                 5. xgpon(5)              - XG-PON 
                 6. wdmpon(6)             - WDM PON		
                 7. hybridpon(7)          - Hybrid PON
                 8. gponOrEpon(8)         - GPON/EPON 
                 9. invalid(-1)           - Indicates that the query fails or no information is detected		
               "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 51 }
  	  
  	hw10GponOltOpticsModuleXponSubType OBJECT-TYPE
  	    SYNTAX INTEGER {
              	unknown(1), 
                classbplus(101), 
                classcplus(102), 
                classb(103), 
                px20(201), 
                px20plus(202), 
                pr30(301), 
                pr20(302), 
                prx30(303), 
                prx20(304), 
                n2a(405), 
                n2b(406), 
                n1(407), 
		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sub module type.
                 Options:
                 1. unknown(1)            - Unknown
                 2. classbplus(101)       - CLASS B+ 
                 3. classcplus(102)       - CLASS C+		
                 4. classb(103)           - CLASS B
                 5. px20(201)             - PX20 
                 6. px20plus(202)         - PX20+		
                 7. pr30(301)             - PR30
                 8. pr20(302)             - PR20 
                 9. prx30(303)            - PRX30
                 10. prx20(304)           - PRX20
                 11. n2a(405)             - N2a 
                 12. n2b(406)             - N2b
                 13. n1(407)              - N1 				 
                 14. invalid(-1)          - Indicates that the query fails or no information is detected		
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 52 }
  	  
  	hw10GponOltOpticsModuleXponUsedType OBJECT-TYPE
  	    SYNTAX INTEGER {
              	unknown(1), 
		olt(2), 
		ont(3), 
		onu(4), 
		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the application scenario of the optical transceiver.
                 Options:
                 1. unknown(1)        - Unknown
                 2. olt(2)            - OLT
                 3. ont(3)            - ONT
                 4. onu(4)            - ONU
                 5. invalid(-1)       - Indicates that the query fails or no information is detected
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 53 }
  	  
  	hw10GponOltOpticsModuleXponEncapsulationType OBJECT-TYPE
  	    SYNTAX INTEGER {
              	unknown(1), 
  		sff2x5(2), 
  		sff2x10(3), 
  		sfp(4), 
  		xfp(5), 
  		lxfp(6), 
  		bosaonboard(7), 
  		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the encapsulation information about the optical transceiver.
                 Options:
                 1. unknown(1)        - Unknown
                 2. sff2x5(2)         - SFF 2x5
                 3. sff2x10(3)        - SFF 2x10
                 4. sfp(4)            - SFP
                 5. xfp(5)            - XFP
                 6. lxfp(6)           - LXFP
                 7. bosaonboard(7)    - BOSA ON BOARD
                 8. invalid(-1)       - Indicates that the query fails or no information is detected
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 54 }
  	  
  	hw10GponOltOpticsModuleXponTemperatureLevel OBJECT-TYPE
  	    SYNTAX INTEGER {
              	unknown(1),
                commercial(2),
                industry(3),
		expandTemperature(4),
		invalid(-1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the application scenario of the optical transceiver.
                 Options:
                 1. unknown(1)              - Unknown
                 2. commercial(2)           - commercial
                 3. industry(3)             - industry
                 4. expandTemperature(4)    - expand temperature
                 5. invalid(-1)             - Indicates that the query fails or no information is detected
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 55 }
  	  
  	hw10GponOltOpticsModuleXponOPMprecision OBJECT-TYPE
  	    SYNTAX INTEGER {
              	unknown(1),
                db3(2),
                db1(3),
		invalid(-1) 
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the OPM precision.
                 Options:
                 1. unknown(1)        - Unknown
                 2. db3(2)            - 3db
                 3. db1(3)            - 1db
                 4. invalid(-1)       - Indicates that the query fails or no information is detected
                "
  	  ::= { hw10GponOltOpticsModuleInformationEntry 56 }
  
      --Table hwGponCommonOntStatisticTable  
        hwGponCommonOntStatisticTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponCommonOntStatisticEntry
            MAX-ACCESS not-accessible      
            STATUS     current
            DESCRIPTION
                "This table is used to query the information about the priority queue alarm.
 	         The indexes of this table are ifIndex and hwXponOntIndex."     
            ::= { hwXponCommonStatisticObjects 7 }
        
        hwGponCommonOntStatisticEntry OBJECT-TYPE
            SYNTAX HwGponCommonOntStatisticEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to query the information about the priority queue alarm.
 	         The indexes of this entry are ifIndex and hwXponOntIndex."
            INDEX       
            { 
            ifIndex,
            hwXponOntIndex
            }
            ::= { hwGponCommonOntStatisticTable 1 }
            
        HwGponCommonOntStatisticEntry ::=
         SEQUENCE
                {
                    hwXponOntUpstreamPQDiscardedBytesAlarmCount 		 OCTET STRING,
                    hwXponOntDownstreamPQDiscardedBytesAlarmCount 		 OCTET STRING,
                    hwXponOntPQDiscardedBytesAlarmCountClear                     INTEGER
                }
                
        hwXponOntUpstreamPQDiscardedBytesAlarmCount OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..8192))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of the upstream priority queue alarm due to buffer overflow.
                 One record uses 6 bytes : T-CONT index(1byte)+PQ number(1byte)+alarm number(4bytes)."
          ::= { hwGponCommonOntStatisticEntry 1 }
            
        hwXponOntDownstreamPQDiscardedBytesAlarmCount OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..1280))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of the downstream priority queue alarm due to buffer overflow.
                 One record uses 8 bytes : port type(1byte)+port index(1byte)+reserve(1byte)+PQ number(1byte)+alarm number(4byte)."
          ::= { hwGponCommonOntStatisticEntry 2 }
            
        hwXponOntPQDiscardedBytesAlarmCountClear  OBJECT-TYPE
            SYNTAX     INTEGER{
            	clear(1),
                invalid(-1)
            }
            MAX-ACCESS   read-write
            STATUS      current
            DESCRIPTION
                "This object is used to clear the statistics.
                 Options:
                 1. clear(1)                    - Indicates that clear the statistics
                 2. invalid(-1)                 - Indicates that the query fails or no information is detected
		"
            ::= { hwGponCommonOntStatisticEntry 3 }

      --Table hwXponOntStatProbeConfigTable  
        hwXponOntStatProbeConfigTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntStatProbeConfigEntry
            MAX-ACCESS not-accessible      
            STATUS     current
            DESCRIPTION
                "This table is used to configure statistical probe.
 	          The indexes of this table are ifIndex, hwXponOntIndex and hwXponOntStatProbeConfigIndex."     
            ::= { hwXponCommonStatisticObjects 8 }
        
        hwXponOntStatProbeConfigEntry OBJECT-TYPE
            SYNTAX HwXponOntStatProbeConfigEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to configure statistical probe.
 	          The indexes of this entry are ifIndex, hwXponOntIndex and hwXponOntStatProbeConfigIndex."
            INDEX       
            { 
                ifIndex,
                hwXponOntIndex,
                hwXponOntStatProbeConfigIndex
            }
            ::= { hwXponOntStatProbeConfigTable 1 }
            
        HwXponOntStatProbeConfigEntry ::=
         SEQUENCE
                {
                    hwXponOntStatProbeConfigIndex        Integer32,
                    hwXponOntStatProbeConfigType         INTEGER,
                    hwXponOntStatProbeConfigParameter    OCTET STRING,
                    hwXponOntStatProbeConfigRowStatus    RowStatus
                }
                
        hwXponOntStatProbeConfigIndex  OBJECT-TYPE
            SYNTAX       Integer32
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "Index of the statistical probe. Range: 1-2147483647, and -1. 
                 Value -1 must be issued when this index is configured. Then, the device allocates actual index to the NMS."
            ::= { hwXponOntStatProbeConfigEntry 1}
                
        hwXponOntStatProbeConfigType  OBJECT-TYPE
            SYNTAX     INTEGER{
                vlanBased(1),
                gemPortBased(2),
                invalid(-1)
            }
            MAX-ACCESS   read-write
            STATUS      current
            DESCRIPTION
                "This object Indicates the type of statistic probe.
                 Options:
                 1. vlanBased(1)                    - Indicates that the statistics are collected by ONT ETH port, VLAN and VLAN priority.
                 2. gemPortBased(2)                 - Indicates that the statistics are collected by GEM port.
                 3. invalid(-1)                     - Indicates that the query fails or no information is detected.
                "
            ::= { hwXponOntStatProbeConfigEntry 2 }
            
        hwXponOntStatProbeConfigParameter OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..128))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This node indicates the detailed configurations of the statistical probe.
                 If the type of the probe is vlanBased, the format of this node is 'AABBCCCCDD'.
                 AA: Indicates the hexadecimal value of the ONT port type, VDSL(37) and ETH(47) is valid.
                 BB: Indicates the hexadecimal value of an ONT port.
                 CCCC: Indicates the hexadecimal value of a VLAN.
                 DD: Indicates the hexadecimal value of the VLAN priority. 'FF' identifies all VLAN priorities, indicating that the VLAN priority is not configured. 
                 If the type of the probe is gemPortBased, the format of this node is 'AAAA'.
                 AAAA: Indicates the hexadecimal value of GEM port ID for distributing-mode or GEM index for profile-mode.
                 For example, to configure probe with ONT ETH port 1, VLAN 1000, all VLAN priorities, issue '2F0103E8FF'."
            ::= { hwXponOntStatProbeConfigEntry 3}
            
        hwXponOntStatProbeConfigRowStatus OBJECT-TYPE
              SYNTAX RowStatus
              MAX-ACCESS read-create
              STATUS current
              DESCRIPTION
                  "this object is used to create a new row or to modify or delete an existing row in this table.
                  "
              ::= { hwXponOntStatProbeConfigEntry 4 }
              
      --Table hwXponOntProbeStatTable  
        hwXponOntProbeStatTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntProbeStatEntry
            MAX-ACCESS not-accessible      
            STATUS     current
            DESCRIPTION
                "This table is used to query probe statistics.
 	          The indexes of this table are ifIndex, hwXponOntIndex and hwXponOntStatProbeConfigIndex."     
            ::= { hwXponCommonStatisticObjects 9 }
        
        hwXponOntProbeStatEntry OBJECT-TYPE
            SYNTAX HwXponOntProbeStatEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to query probe statistics.
 	          The indexes of this entry are ifIndex, hwXponOntIndex and hwXponOntStatProbeConfigIndex."
            INDEX       
            { 
                ifIndex,
                hwXponOntIndex,
                hwXponOntStatProbeConfigIndex
            }
            ::= { hwXponOntProbeStatTable 1 }
            
        HwXponOntProbeStatEntry ::=
         SEQUENCE
                {
                    hwXponOntProbeStatResult       Integer32,
                    hwXponOntProbeStatRecvBytes    Counter64,
                    hwXponOntProbeStatRecvFrames   Counter64,
                    hwXponOntProbeStatSendBytes    Counter64,
                    hwXponOntProbeStatSendFrames   Counter64,
                    hwXponOntProbeStatClear        INTEGER
                }
                
        hwXponOntProbeStatResult  OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS   read-only
            STATUS      current
            DESCRIPTION
                "This node identifies the result of queried probe statistics.
                 Value 0 indicates that probe statistics are queried successfully.
                "
            ::= { hwXponOntProbeStatEntry 1 }
            
        hwXponOntProbeStatRecvBytes OBJECT-TYPE
            SYNTAX      Counter64 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the received bytes.
            "
            ::={ hwXponOntProbeStatEntry 2 }
            
        hwXponOntProbeStatRecvFrames OBJECT-TYPE
            SYNTAX      Counter64 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the received frames.
            "
            ::={ hwXponOntProbeStatEntry 3 }
            
        hwXponOntProbeStatSendBytes OBJECT-TYPE
            SYNTAX      Counter64 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the sent bytes.
            "
            ::={ hwXponOntProbeStatEntry 4 }
            
        hwXponOntProbeStatSendFrames OBJECT-TYPE
            SYNTAX      Counter64 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the sent frames.
            "
            ::={ hwXponOntProbeStatEntry 5 }
            
        hwXponOntProbeStatClear  OBJECT-TYPE
            SYNTAX     INTEGER{
                clear(1),
                invalid(-1)
            }
            MAX-ACCESS   read-write
            STATUS      current
            DESCRIPTION
                "This node is used to clear probe statistics.
                 Options:
                 1. clear(1)                    - Indicates that clear probe statistics.
                 2. invalid(-1)                 - Indicates that the query fails or no information is detected.
                "
            ::= { hwXponOntProbeStatEntry 6 }
            
       hwGponOntMulticastGemPortStatisticTable OBJECT-TYPE
            SYNTAX  SEQUENCE OF HwGponOntMulticastGemPortStatisticEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The ONT Multicast GEM Port statistics table.
                 The index of this table is a combination of ifIndex and
                 hwXponOntIndex.
                "
            ::= { hwXponCommonStatisticObjects 10 }

        hwGponOntMulticastGemPortStatisticEntry OBJECT-TYPE
            SYNTAX HwGponOntMulticastGemPortStatisticEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The ONT Multicast GEM Port statistics table.
                 The index of this entry is a combination of ifIndex and
                 hwXponOntIndex.
                "
            INDEX { ifIndex, hwXponOntIndex }
            ::= { hwGponOntMulticastGemPortStatisticTable 1 }

        HwGponOntMulticastGemPortStatisticEntry ::=
            SEQUENCE {
                hwGponOntMulticastGemPortStatisticRecvLostFrames         Counter64,
                hwGponOntMulticastGemPortStatisticRecvMisinsertedFrames  Counter64,
                hwGponOntMulticastGemPortStatisticRecvFrames             Counter64,
                hwGponOntMulticastGemPortStatisticRecvBlocks             Counter64,
                hwGponOntMulticastGemPortStatisticSendBlocks             Counter64,
                hwGponOntMulticastGemPortStatisticImpairedBlocks         Counter64, 
                hwGponOntMulticastGemPortStatisticSendGemFrames          Counter64, 
                hwGponOntMulticastGemPortStatisticClear                  INTEGER
            }

        hwGponOntMulticastGemPortStatisticRecvLostFrames OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the loss of frames."
            ::= { hwGponOntMulticastGemPortStatisticEntry 1 }

        hwGponOntMulticastGemPortStatisticRecvMisinsertedFrames OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the misinserted frames."
            ::= { hwGponOntMulticastGemPortStatisticEntry 2 }

        hwGponOntMulticastGemPortStatisticRecvFrames OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received frames."
            ::= { hwGponOntMulticastGemPortStatisticEntry 3 }

        hwGponOntMulticastGemPortStatisticRecvBlocks OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received blocks."
            ::= { hwGponOntMulticastGemPortStatisticEntry 4 }

        hwGponOntMulticastGemPortStatisticSendBlocks OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent blocks."
            ::= { hwGponOntMulticastGemPortStatisticEntry 5 }

        hwGponOntMulticastGemPortStatisticImpairedBlocks OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of received impaired data blocks."
            ::= { hwGponOntMulticastGemPortStatisticEntry 6 }   
            
        hwGponOntMulticastGemPortStatisticSendGemFrames OBJECT-TYPE
	    SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
               "This object indicates the number of transmitted GEM frames."
               ::= { hwGponOntMulticastGemPortStatisticEntry 7 }

        hwGponOntMulticastGemPortStatisticClear OBJECT-TYPE
            SYNTAX INTEGER{
            	clear(1),
                invalid(-1)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object is used to clear the statistics.
                 Options:
                 1. clear(1)                    - Indicates that clear the statistics
                 2. invalid(-1)                 - Indicates that the query fails or no information is detected
		"
            ::= { hwGponOntMulticastGemPortStatisticEntry 20 }
            
      --Table hwGponOntTdmPortPerf15MinTable  
        hwGponOntTdmPortPerf15MinTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwGponOntTdmPortPerf15MinEntry
            MAX-ACCESS not-accessible      
            STATUS     current
            DESCRIPTION
                "This table is used to query ONT TDM port statistics in history 15 minutes.
 	          The indexes of this table are ifIndex, hwXponOntIndex, hwGponDeviceOntPhyIndex and hwGponOntTdmPortPerf15minIntervalNum."     
            ::= { hwXponCommonStatisticObjects 11 }
        
        hwGponOntTdmPortPerf15MinEntry OBJECT-TYPE
            SYNTAX HwGponOntTdmPortPerf15MinEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This table is used to query ONT TDM port statistics in history 15 minutes.
 	          The indexes of this entry are ifIndex, hwXponOntIndex, hwGponDeviceOntPhyIndex and hwGponOntTdmPortPerf15minIntervalNum."
            INDEX       
            { 
                ifIndex,
                hwXponOntIndex,
                hwGponDeviceOntPhyIndex,
                hwGponOntTdmPortPerf15minIntervalNum
            }
            ::= { hwGponOntTdmPortPerf15MinTable 1 }
            
        HwGponOntTdmPortPerf15MinEntry ::=
         SEQUENCE
                {
                    hwGponOntTdmPortPerf15minIntervalNum  Integer32,
                    hwGponOntTdmPortPerf15minES    Gauge32,
                    hwGponOntTdmPortPerf15minSES   Gauge32,
                    hwGponOntTdmPortPerf15minUAS   Gauge32,
                    hwGponOntTdmPortPerf15minEFS   Gauge32
                }
                
        hwGponOntTdmPortPerf15minIntervalNum OBJECT-TYPE
            SYNTAX     Integer32
            MAX-ACCESS   not-accessible
            STATUS      current
            DESCRIPTION
                "This object indicates the history 15 minutes interval number. 
                 value 0 indicates current 15 minutes interval.
                "
            ::= { hwGponOntTdmPortPerf15MinEntry 1 }
            
        hwGponOntTdmPortPerf15minES OBJECT-TYPE
            SYNTAX      Gauge32 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the statistic of the errored seconds.
             -1 indicates the invalid value.
            "
            ::={ hwGponOntTdmPortPerf15MinEntry 2 }
            
        hwGponOntTdmPortPerf15minSES OBJECT-TYPE
            SYNTAX      Gauge32 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the statistic of the serverly erroeds.
             -1 indicates the invalid value.
            "
            ::={ hwGponOntTdmPortPerf15MinEntry 3 }
            
        hwGponOntTdmPortPerf15minUAS OBJECT-TYPE
            SYNTAX      Gauge32 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the statistic of the unavailable seconds.
             -1 indicates the invalid value.
            "
            ::={ hwGponOntTdmPortPerf15MinEntry 4}
            
        hwGponOntTdmPortPerf15minEFS OBJECT-TYPE
            SYNTAX      Gauge32 
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "This object indicates the statistic of the error free seconds.
             -1 indicates the invalid value.
            "
            ::={ hwGponOntTdmPortPerf15MinEntry 5 }
            
       --Table hwXponOntPortEthernetStatsTable  
        hwXponOntPortEthernetStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntPortEthernetStatsEntry
            MAX-ACCESS not-accessible      
            STATUS     current
            DESCRIPTION
                "The ONT port statistics table.
 	          The indexes of this table are ifIndex, hwXponOntIndex, hwGponOntifType and hwGponOntifPort."     
            ::= { hwXponCommonStatisticObjects 13 }
        
        hwXponOntPortEthernetStatsEntry OBJECT-TYPE
            SYNTAX HwXponOntPortEthernetStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The ONT port statistics table.
 	          The indexes of this entry are ifIndex, hwXponOntIndex, hwGponOntifType and hwGponOntifPort."
            INDEX       
            { 
                ifIndex,
                hwXponOntIndex,
                hwGponOntifType,
                hwGponOntifPort
            }
            ::= { hwXponOntPortEthernetStatsTable 1 }
            
        HwXponOntPortEthernetStatsEntry ::=
         SEQUENCE
                {
                    hwXponOntPortEthernetStatisticRecvOctets                Counter64,
                    hwXponOntPortEthernetStatisticRecvPkts                  Counter64,
                    hwXponOntPortEthernetStatisticRecvBroadcastPkts         Counter64,
                    hwXponOntPortEthernetStatisticRecvMulticastPkts         Counter64,
                    hwXponOntPortEthernetStatisticRecvPkts64Octets          Counter64,
                    hwXponOntPortEthernetStatisticRecvPkts65to127Octets     Counter64,
                    hwXponOntPortEthernetStatisticRecvPkts128to255Octets    Counter64,
                    hwXponOntPortEthernetStatisticRecvPkts256to511Octets    Counter64,
                    hwXponOntPortEthernetStatisticRecvPkts512to1023Octets   Counter64,
                    hwXponOntPortEthernetStatisticRecvPkts1024to1518Octets  Counter64,
                    hwXponOntPortEthernetStatisticRecvPktsOversize          Counter64,
                    hwXponOntPortEthernetStatisticRecvPktsUndersize         Counter64,
                    hwXponOntPortEthernetStatisticRecvFCSErrors             Counter64,
                    hwXponOntPortEthernetStatisticRecvDropEvents            Counter64,
                    hwXponOntPortEthernetStatisticSendOctets                Counter64,
                    hwXponOntPortEthernetStatisticSendPkts                  Counter64,
                    hwXponOntPortEthernetStatisticSendBroadcastPkts         Counter64,
                    hwXponOntPortEthernetStatisticSendMulticastPkts         Counter64,
                    hwXponOntPortEthernetStatisticSendPkts64Octets          Counter64,
                    hwXponOntPortEthernetStatisticSendPkts65to127Octets     Counter64,
                    hwXponOntPortEthernetStatisticSendPkts128to255Octets    Counter64,
                    hwXponOntPortEthernetStatisticSendPkts256to511Octets    Counter64,
                    hwXponOntPortEthernetStatisticSendPkts512to1023Octets   Counter64,
                    hwXponOntPortEthernetStatisticSendPkts1024to1518Octets  Counter64,
                    hwXponOntPortEthernetStatisticSendPktsOversize          Counter64,
                    hwXponOntPortEthernetStatisticForwardDropEvents         Counter64,
                    hwXponOntPortEthernetStatisticClear                     INTEGER
                }
                
        hwXponOntPortEthernetStatisticRecvOctets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received bytes."
            ::= { hwXponOntPortEthernetStatsEntry 1 }

        hwXponOntPortEthernetStatisticRecvPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received frames."
            ::= { hwXponOntPortEthernetStatsEntry 2 }
            
        hwXponOntPortEthernetStatisticRecvBroadcastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received broadcast frames."
            ::= { hwXponOntPortEthernetStatsEntry 3 }

        hwXponOntPortEthernetStatisticRecvMulticastPkts OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received multicast frames."
            ::= { hwXponOntPortEthernetStatsEntry 4 }

        hwXponOntPortEthernetStatisticRecvPkts64Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 64-octet frames"
            ::= { hwXponOntPortEthernetStatsEntry 5 }

        hwXponOntPortEthernetStatisticRecvPkts65to127Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 65~127-octet frames"
            ::= { hwXponOntPortEthernetStatsEntry 6 }

        hwXponOntPortEthernetStatisticRecvPkts128to255Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 128~255-octet frames"
            ::= { hwXponOntPortEthernetStatsEntry 7 }

        hwXponOntPortEthernetStatisticRecvPkts256to511Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 256~511-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 8 }

        hwXponOntPortEthernetStatisticRecvPkts512to1023Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 512~1023-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 9 }

        hwXponOntPortEthernetStatisticRecvPkts1024to1518Octets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received 1024~1518-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 10 }
            
        hwXponOntPortEthernetStatisticRecvPktsOversize OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received oversize frames."
            ::= { hwXponOntPortEthernetStatsEntry 11 }
            
        hwXponOntPortEthernetStatisticRecvPktsUndersize OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received undersize frames."
            ::= { hwXponOntPortEthernetStatsEntry 12 }

        hwXponOntPortEthernetStatisticRecvFCSErrors  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received FCS error frames."
            ::= { hwXponOntPortEthernetStatsEntry 13 }
            
        hwXponOntPortEthernetStatisticRecvDropEvents  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the received drop events."
            ::= { hwXponOntPortEthernetStatsEntry 14 }

        hwXponOntPortEthernetStatisticSendOctets  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the send drop bytes."
            ::= { hwXponOntPortEthernetStatsEntry 15 }

        hwXponOntPortEthernetStatisticSendPkts  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the send frames."
            ::= { hwXponOntPortEthernetStatsEntry 16 }

        hwXponOntPortEthernetStatisticSendBroadcastPkts  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent broadcast frames."
            ::= { hwXponOntPortEthernetStatsEntry 17 }

        	hwXponOntPortEthernetStatisticSendMulticastPkts  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent multicast frames."
            ::= { hwXponOntPortEthernetStatsEntry 18 }
            
         hwXponOntPortEthernetStatisticSendPkts64Octets  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 64-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 19 }            
            
         hwXponOntPortEthernetStatisticSendPkts65to127Octets  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 65~127-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 20 }            
            
        hwXponOntPortEthernetStatisticSendPkts128to255Octets  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 128~255-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 21}
            
         hwXponOntPortEthernetStatisticSendPkts256to511Octets   OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 256~511-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 22 }
            
      hwXponOntPortEthernetStatisticSendPkts512to1023Octets   OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 512~1023-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 23 }
            
      hwXponOntPortEthernetStatisticSendPkts1024to1518Octets   OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent 1024~1518-octet frames."
            ::= { hwXponOntPortEthernetStatsEntry 24 }      
       
       hwXponOntPortEthernetStatisticSendPktsOversize    OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent oversize frames."
            ::= { hwXponOntPortEthernetStatsEntry 25 }
            
       hwXponOntPortEthernetStatisticForwardDropEvents  OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sent drop events."
            ::= { hwXponOntPortEthernetStatsEntry 26 }
             
       hwXponOntPortEthernetStatisticClear   OBJECT-TYPE
            SYNTAX INTEGER{
            	clear(1),
                invalid(-1)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object is used to clear the statistics.
                 Options:
                 1. clear(1)       - Indicates that clear the statistics
                 2. invalid(-1)    - Indicates that the query fails or no information is detected
                "
            ::= { hwXponOntPortEthernetStatsEntry 27 }

  --hwGponDeviceCommonGlobalObjects   begin
        hwGponDeviceChangePasswordIntervalTime OBJECT-TYPE
            SYNTAX     Gauge32 (0 | 5..1440 | 65535)
            UNITS      "minutes"
            MAX-ACCESS read-write
            STATUS     current
            DESCRIPTION
                "The interval time of ONT's password renew, 0 means no update, 65535 means no
                 configration data.
                 Unit: minutes
                "
            ::= { hwGponDeviceCommonGlobalObjects 1 }

        hwGponDeviceTcontAutoCombineSwitch  OBJECT-TYPE
            SYNTAX  INTEGER
              {
                  enable(1),
                  disable(2)
              }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The switch value of T-CONT auto-combine.
                 Options:
                 1. enable(1)  - The switch value of T-CONT auto-combine is enable
                 2. disable(2) - The switch value of T-CONT auto-combine is disable
                "
            ::= { hwGponDeviceCommonGlobalObjects 2 }
            
         hwGponOntInteroperabilityMode   OBJECT-TYPE
            SYNTAX  INTEGER
              {
                  itu-t(1),
                  ctc(2),
                  eric-v1(3),
                  eric-v2(4),
                  itu-t-g984(5),
                  itu-t-g988(6)
              }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The switch mode of gpon ont interoperability.
                 Options:
                 1. itu-t(1)      - The switch mode of gpon ont interoperability is itu-t
                 2. ctc(2)        - The switch mode of gpon ont interoperability is ctc
                 3. eric-v1(3)       - The switch mode of gpon ont interoperability is eric-v1
                 4. eric-v2(4)   - The switch mode of gpon ont interoperability is eric-v2
                 5. itu-t-g984(5) - The switch mode of gpon ont interoperability is itu-t-g984
                 6. itu-t-g988(6) - The switch mode of gpon ont interoperability is itu-t-g988
                "
            ::= { hwGponDeviceCommonGlobalObjects 3 }  
            
        hwGponDeviceOntDefaultLineProfName  OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..32))
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the name of the default line profile that is bound to the ONT."
            ::= { hwGponDeviceCommonGlobalObjects 4 }

        hwGponDeviceOntDefaultSrvProfName  OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..32))
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the name of the default service profile that is bound to the ONT."
            ::= { hwGponDeviceCommonGlobalObjects 5 }
           
       hwGponOntMutlicastAuthMode   OBJECT-TYPE
            SYNTAX  INTEGER
              {
                  ont-control(1),
                  olt-control(2)
              }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The mode of gpon ont multicast authentication.
                 Options:
                 1. ont-control(1)  - The mode of gpon multicast authentication is ont-control
                 2. olt-control(2)  - The mode of gpon multicast authentication is olt-control
                "
            ::= { hwGponDeviceCommonGlobalObjects 6 }       
                      
       hwGponOnuTcontPriorityQueuePriorityReverse   OBJECT-TYPE
            SYNTAX  INTEGER
              {                  
                  enable(1),
                  disable(2)
              }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The priority reverse switch of gpon ont priority queue.
                 Options:
                 1. enable(1)  - The priority reverse switch is enable
                 2. disable(2)   - The priority reverse switch is disable
                "
            ::= { hwGponDeviceCommonGlobalObjects 7 }
              
       hwGponOntInteroperModeActiveMode   OBJECT-TYPE
            SYNTAX  INTEGER
              {                  
                  immediate(1),
                  next-startup(2),
                  invalid(-1)
              }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                 "The active mode of gpon ONT interoperability mode.
                 Options:
                 1. immediate(1)     - Indicates that the ONT takes effect immediately
                 2. next-startup(2)  - Indicates that the ONT takes effect after next startup
                 3. invalid(-1)      - Indicates that the query fails or no information is detected
		 "
            ::= { hwGponDeviceCommonGlobalObjects 8 }         

       hwGponDeviceTcontAutoCreateSwitch  OBJECT-TYPE
            SYNTAX  INTEGER
              {
                  enable(1),
                  disable(2)
              }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The switch value of T-CONT auto-create.
                 Options:
                 1. enable(1)  - The switch value of T-CONT auto-create is enable
                 2. disable(2) - The switch value of T-CONT auto-create is disable
                 The default value is disable(2)
                "
            ::= { hwGponDeviceCommonGlobalObjects 9 }       
            
       hwGponOntPriorityQueueMappingPolicySwitch  OBJECT-TYPE
            SYNTAX  INTEGER
              {
                  gem-map(1),
                  cos-map(2)
              }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The switch value of GPON ONT priority queue mapping policy.
                 Options:
                 1. gem-map(1)  - GPON ONT priority queue mapping policy is gem-map
                 2. cos-map(2)  - GPON ONT priority queue mapping policy is cos-map
                 The default value is gem-map(1)
                "
            ::= { hwGponDeviceCommonGlobalObjects 10 } 
                          
  --hwGponDeviceCommonGlobalObjects   end   
  
  --hwXponDeviceCommonGlobalObjects   begin 
        hwXponDeviceLosAlarmControlState  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "The object is used to open or close the switch of the advanced los alarm.
                   Options:
                   1.enable(1)     - Open the switch of the advanced los alarm
                   2.disable(2)    - Close the switch of the advanced los alarm
                   The default value is disable(2).
                   "
            ::= { hwXponDeviceCommonGlobalObjects 1 }

        hwXponCommonOntCatvDefaultState OBJECT-TYPE
            SYNTAX     INTEGER {
                on(1),
                off(2),
                invalid(-1)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object is used to set the default status of ONT CATV ports.
                 Options:
                 1. on(1)             - The default status of ONT CATV ports is on
                 2. off(2)            - The default status of ONT CATV ports is off
                 3. invalid(-1)       - Indicates that the query fails
                "
            ::= { hwXponDeviceCommonGlobalObjects 2 }
            
        hwXponDeviceGroupPowerOffControlState OBJECT-TYPE
            SYNTAX     INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object is used to open or close the group power off report switch.
                 Options:
                 1.enable(1)     - Open the group power off report switch      
                 2.disable(2)    - Close the group power off report switch
                 The default value is disable(2)
                "
            ::= { hwXponDeviceCommonGlobalObjects 3 }
               
        hwXponDeviceAutofindConflictCheckSwitch OBJECT-TYPE
            SYNTAX INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object is used to open or close the ONT autofind conflict-check switch.
                 Options:
                 1.enable(1)     - Open the switch of the ONT autofind conflict-check
                 2.disable(2)    - Close the switch of the ONT autofind conflict-check
                 The default value is disable(2)
                "
            ::= { hwXponDeviceCommonGlobalObjects 4 }
            
        hwXponDeviceModifyBoundProfileSwitch  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2),
                invalid(-1)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "This object is used to enable or disable the bound profile modification switch.
                   Options:
                   1.enable(1)     - Enable to modify bound profile
                   2.disable(2)    - Disable to modify bound profile
                   3.invalid(-1)   - Indicates that the query fails
                   The default value is enable(1).
                   "
            ::= { hwXponDeviceCommonGlobalObjects 5 }
            
        hwXponDeviceAlarmClearOnShutdown  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "The object is used to set whether clear active alarm on shutdown object.
                   Options:
                   1.enable(1)     - Open the switch, clear active alarm on shutdown object
                   2.disable(2)    - Close the switch, does not clear active alarm on shutdown object
                   The default value is disable(2).
                   "
            ::= { hwXponDeviceCommonGlobalObjects 6 }
            
        hwXponDeviceSuppressInitialAlarmState  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "The object is used to set whether suppress initial alarm.
                   Options:
                   1.enable(1)     - Open the switch, suppress initial alarm state
                   2.disable(2)    - Close the switch, does not suppress initial alarm state
                   The default value is enable(1).
                   "
            ::= { hwXponDeviceCommonGlobalObjects 7 }
            
        hwXponDeviceAllowDifferentRangeSwitch  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "This object is used to enable or disable the switch of allowing protect group member ports have different distances.
                   Options:
                   1.enable(1)     - Enable the switch of allowing protect group member ports have different distances
                   2.disable(2)    - Disable the switch of allowing protect group member ports have different distances
                   The default value is disable(2).
                   "
            ::= { hwXponDeviceCommonGlobalObjects 8 }

  --hwXponDeviceCommonGlobalObjects   end
  
  --Table hwXponOntAlarmPolicyTable
        hwXponOntAlarmPolicyTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponOntAlarmPolicyEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponOntAlarmPolicyTable is used to config the ONT alarm policy profile.
                 The index of this table is hwXponOntAlarmPolicyNameIndex.
                "
            ::= { hwXponDeviceCommonProfileObjects 1 }

        hwXponOntAlarmPolicyEntry OBJECT-TYPE
            SYNTAX HwXponOntAlarmPolicyEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponOntAlarmPolicyTable is used to config the ONT alarm policy profile.
                 The index of this entry is hwXponOntAlarmPolicyNameIndex.
                "
            INDEX       { hwXponOntAlarmPolicyNameIndex }
            ::= { hwXponOntAlarmPolicyTable 1 }

        HwXponOntAlarmPolicyEntry ::=
            SEQUENCE
                {
                hwXponOntAlarmPolicyNameIndex                OCTET STRING,
                hwXponOntAlarmPolicyBindNum                  Integer32,
                hwXponOntAlarmPolicyFilterAlarmNum           Integer32,
                hwXponOntAlarmPolicyFilterAlarmList          OCTET STRING,
                hwXponOntAlarmPolicyUnFilterAlarmNum         Integer32,
                hwXponOntAlarmPolicyUnFilterAlarmList        OCTET STRING,
                hwXponOntAlarmPolicyRowStatus                RowStatus,
                hwXponOntAlarmPolicyAppendPolicyName         INTEGER,
                hwXponOntAlarmPolicyAppendIpAddress          INTEGER,
                hwXponOntAlarmPolicyAppendMac                INTEGER,
                hwXponOntAlarmPolicyAppendSn                 INTEGER,
                hwXponOntAlarmPolicyAppendLoid               INTEGER
                }

        hwXponOntAlarmPolicyNameIndex  OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..32))
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "This object indicates the ONT alarm policy profile name index."
            ::= { hwXponOntAlarmPolicyEntry 1 }

        hwXponOntAlarmPolicyBindNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of ONT bound with alarm policy profile."
            ::= { hwXponOntAlarmPolicyEntry 2 }

        hwXponOntAlarmPolicyFilterAlarmNum OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the number of filtered alarm of the alarm policy profile."
            ::= { hwXponOntAlarmPolicyEntry 3 }
            
        hwXponOntAlarmPolicyFilterAlarmList OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..400))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the filtered alarm list of the alarm policy profile,and each alarm ID takes 4 bytes."
            ::= { hwXponOntAlarmPolicyEntry 4 }
            
        hwXponOntAlarmPolicyUnFilterAlarmNum OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the number of unfiltered alarm of the alarm policy profile."
            ::= { hwXponOntAlarmPolicyEntry 5 }
       
        hwXponOntAlarmPolicyUnFilterAlarmList OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (0..400))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the unfiltered alarm list of the alarm policy profile,and each alarm ID takes 4 bytes."
            ::= { hwXponOntAlarmPolicyEntry 6 }
            
        hwXponOntAlarmPolicyRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The hwXponOntAlarmPolicyRowStatus is used to create a new row
                 or to modify or delete an existing row in this table.
                "
            ::= { hwXponOntAlarmPolicyEntry 7 }

        hwXponOntAlarmPolicyAppendPolicyName  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "This object indicates if the ONT alarm appends ONT alarm policy profile name.
                    Options:
                    1.enable(1)     - The ONT alarm appends  ONT alarm policy profile name..
                    2.disable(2)    - The ONT alarm does not append  ONT alarm policy profile name..
                    The default value is enable(1).
                   "
            ::= { hwXponOntAlarmPolicyEntry 8 }

        hwXponOntAlarmPolicyAppendIpAddress  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "This object indicates if the ONT alarm appends IP address.
                    Options:
                    1.enable(1)     - The ONT alarm appends IP address.
                    2.disable(2)    - The ONT alarm does not append IP address.
                    The default value is disable(2).
                   "
            ::= { hwXponOntAlarmPolicyEntry 9 }

        hwXponOntAlarmPolicyAppendMac  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "This object indicates if the ONT alarm appends MAC.
                    Options:
                    1.enable(1)     - The ONT alarm appends MAC.
                    2.disable(2)    - The ONT alarm does not append MAC.
                    The default value is disable(2).
                   "
            ::= { hwXponOntAlarmPolicyEntry 10 }

        hwXponOntAlarmPolicyAppendSn  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "This object indicates if the ONT alarm appends SN.
                    Options:
                    1.enable(1)     - The ONT alarm appends SN.
                    2.disable(2)    - The ONT alarm does not append SN.
                    The default value is disable(2).
                   "
            ::= { hwXponOntAlarmPolicyEntry 11}

        hwXponOntAlarmPolicyAppendLoid  OBJECT-TYPE
            SYNTAX  INTEGER {
                enable(1),
                disable(2)
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                   "This object indicates if the ONT alarm appends LOID.
                    Options:
                    1.enable(1)     - The ONT alarm appends LOID.
                    2.disable(2)    - The ONT alarm does not append LOID.
                    The default value is disable(2).
                   "
            ::= { hwXponOntAlarmPolicyEntry 12 }
            
  --Table hwXponDeviceOntPowerSheddingProfileTable  begin  
        hwXponDeviceOntPowerSheddingProfileTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwXponDeviceOntPowerSheddingProfileEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "The hwXponDeviceOntPowerSheddingProfileTable is used to config the ONT power shedding profile.
                 The index of this table is hwXponDeviceOntPowerSheddingProfileNameIndex.
                "
            ::= { hwXponDeviceCommonProfileObjects 2 }

        hwXponDeviceOntPowerSheddingProfileEntry OBJECT-TYPE
            SYNTAX HwXponDeviceOntPowerSheddingProfileEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hwXponDeviceOntPowerSheddingProfileTable is used to config the ONT power shedding profile.
                 The index of this entry is hwXponDeviceOntPowerSheddingProfileNameIndex.
                "
            INDEX       { hwXponDeviceOntPowerSheddingProfileNameIndex }
            ::= { hwXponDeviceOntPowerSheddingProfileTable 1 }

        HwXponDeviceOntPowerSheddingProfileEntry ::=
            SEQUENCE
                {
                hwXponDeviceOntPowerSheddingProfileNameIndex                OCTET STRING,
                hwXponDeviceOntPowerSheddingProfileBindNum                  Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassData               Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassVoice              Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassVideoOverlay       Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassVideoReturn        Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassAtm                Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassDsl                Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassCes                Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassFrame              Integer32,
                hwXponDeviceOntPowerSheddingIntervalClassSdhSonet           Integer32,
                hwXponDeviceOntPowerSheddingRestoreInterval                 Integer32,
                hwXponDeviceOntPowerSheddingProfileRowStatus                RowStatus
                }
                
        hwXponDeviceOntPowerSheddingProfileNameIndex  OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..32))
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "This object indicates the ONT power shedding profile name index."
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 1 }

        hwXponDeviceOntPowerSheddingProfileBindNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the number of ONT bound with power shedding profile."
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 2 }
            
        hwXponDeviceOntPowerSheddingIntervalClassData OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of data class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 3 }
       
        hwXponDeviceOntPowerSheddingIntervalClassVoice OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of voice class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 4 }
            
        hwXponDeviceOntPowerSheddingIntervalClassVideoOverlay OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of video overlay class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 5 }
            
        hwXponDeviceOntPowerSheddingIntervalClassVideoReturn OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of video return class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 6 }            

        hwXponDeviceOntPowerSheddingIntervalClassDsl OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of DSL class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 7 }  
            
        hwXponDeviceOntPowerSheddingIntervalClassAtm OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of ATM class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 8 }
       
        hwXponDeviceOntPowerSheddingIntervalClassCes OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of CES class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 9 }
            
        hwXponDeviceOntPowerSheddingIntervalClassFrame OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of frame class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 10 }
            
        hwXponDeviceOntPowerSheddingIntervalClassSdhSonet OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the power shedding interval of sdh-sonet class service.
                 Options:
                 1: disables power shedding(0) 
                 2: enables immediate power shed(1)
                 3. 2-65535       - Indicates the time, in seconds, to keep the service active after AC failure before shutting them down and shedding power.                
                "
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 11 }            

        hwXponDeviceOntPowerSheddingRestoreInterval OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the time delay, in seconds, before resetting the power-shedding timers after full power restoration."
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 12 }  
             
        hwXponDeviceOntPowerSheddingProfileRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The hwXponDeviceOntPowerSheddingProfileRowStatus is used to create a new row or to modify or delete an existing row in this table."
            ::= { hwXponDeviceOntPowerSheddingProfileEntry 13 }            
                                    
  --Table hwXponDeviceOntPowerSheddingProfileTable  end
  
  --Table hwXponOntTr069ServerProfileTable 
        hwXponOntTr069ServerProfileTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntTr069ServerProfileEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to create, delete or modify the tr069 server profile.
 	         The index of this table is hwXponOntTr069ServerProfileName.          
                "
            ::= { hwXponDeviceCommonProfileObjects 3 }
                
        hwXponOntTr069ServerProfileEntry OBJECT-TYPE
            SYNTAX     HwXponOntTr069ServerProfileEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to create, delete or modify the tr069 server profile.
 	         The index of this entry is hwXponOntTr069ServerProfileName.          
                "
            INDEX       { hwXponOntTr069ServerProfileName }
            ::= { hwXponOntTr069ServerProfileTable 1 }

        HwXponOntTr069ServerProfileEntry ::=
            SEQUENCE {
                hwXponOntTr069ServerProfileName                     OCTET STRING,
                hwXponOntTr069ServerProfileBindTimes                Integer32,
                hwXponOntTr069ServerProfileUrl                      OCTET STRING,
                hwXponOntTr069ServerProfileUserName                 OCTET STRING,
                hwXponOntTr069ServerProfilePassword                 OCTET STRING,
                hwXponOntTr069ServerProfileRealm                    OCTET STRING,
                hwXponOntTr069ServerProfileRowStatus                RowStatus
            }
            
        hwXponOntTr069ServerProfileName OBJECT-TYPE
            SYNTAX   OCTET STRING (SIZE (1..32))
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "This object indicates the tr069-server-profile name."
            ::= { hwXponOntTr069ServerProfileEntry 1 }
            
        hwXponOntTr069ServerProfileBindTimes OBJECT-TYPE
            SYNTAX   Integer32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "This object indicates the number of ONT bound with tr069-server-profile."
            ::= { hwXponOntTr069ServerProfileEntry 2 }

        hwXponOntTr069ServerProfileUrl OBJECT-TYPE
            SYNTAX   OCTET STRING (SIZE (1..63))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the server URL.
                 For example: you can set the value of the leaf like 'www.huawei.com'."
            ::= { hwXponOntTr069ServerProfileEntry 3 }

        hwXponOntTr069ServerProfileUserName OBJECT-TYPE
            SYNTAX   OCTET STRING (SIZE (1..50))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the user name."
            ::= { hwXponOntTr069ServerProfileEntry 4 }

        hwXponOntTr069ServerProfilePassword OBJECT-TYPE
            SYNTAX   OCTET STRING (SIZE (1..25))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the user password."
            ::= { hwXponOntTr069ServerProfileEntry 5 }
        
        hwXponOntTr069ServerProfileRealm OBJECT-TYPE
            SYNTAX   OCTET STRING (SIZE (0..24))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the server realm."
            ::= { hwXponOntTr069ServerProfileEntry 6 }
                 
        hwXponOntTr069ServerProfileRowStatus OBJECT-TYPE
            SYNTAX   RowStatus
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "TRow status. This object is used to differentiate the creation, modification and deletion operations for an object.
                "
            ::= { hwXponOntTr069ServerProfileEntry 7 }
-- hwXponOntTr069ServerProfileTable  end

  --Table hwXponOntAlarmPolicyAlarmTable 
        hwXponOntAlarmPolicyAlarmTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntAlarmPolicyAlarmEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to set the ONT alarm level.
 	         The index of this table is hwXponOntAlarmPolicyCfgNameIndex and hwXponOntAlarmPolicyAlarmId.
                "
            ::= { hwXponDeviceCommonProfileObjects 4 }
                
        hwXponOntAlarmPolicyAlarmEntry OBJECT-TYPE
            SYNTAX     HwXponOntAlarmPolicyAlarmEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to set the ONT alarm level.
 	         The index of this entry is hwXponOntAlarmPolicyCfgNameIndex and hwXponOntAlarmPolicyAlarmId.
                "
            INDEX    { hwXponOntAlarmPolicyCfgNameIndex, hwXponOntAlarmPolicyAlarmId }
            ::= { hwXponOntAlarmPolicyAlarmTable 1 }

        HwXponOntAlarmPolicyAlarmEntry ::=
            SEQUENCE {
                hwXponOntAlarmPolicyCfgNameIndex           OCTET STRING,
                hwXponOntAlarmPolicyAlarmId                Integer32,
                hwXponOntAlarmPolicyAlarmLevel             INTEGER
            }
            
        hwXponOntAlarmPolicyCfgNameIndex  OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..32))
            MAX-ACCESS   not-accessible
            STATUS       current
            DESCRIPTION
                "This object indicates the ONT alarm policy profile name index."
            ::= { hwXponOntAlarmPolicyAlarmEntry 1 }

        hwXponOntAlarmPolicyAlarmId OBJECT-TYPE
            SYNTAX   Integer32
            MAX-ACCESS  not-accessible
            STATUS      current
            DESCRIPTION
                "This object indicates the ONT alarm ID."
            ::= { hwXponOntAlarmPolicyAlarmEntry 2 }
            
        hwXponOntAlarmPolicyAlarmLevel OBJECT-TYPE
            SYNTAX   INTEGER {
                critical(1),
                major(2),
                minor(3),
                warning(4),
                unconcern(-1)
            }
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "This object indicates the ONT alarm level.
                 Options:
                 1. critical(1)             - The ONT alarm level is critical.
                 2. major(2)                - The ONT alarm level is major.
                 3. minor(3)                - The ONT alarm level is minor.
                 4. warning(4)              - The ONT alarm level is warning.
                 5. unconcern(-1)           - The ONT alarm severity is unconcerned in the ONT alarm policy profile.
                 The default value is unconcern(-1).
                "
            ::= { hwXponOntAlarmPolicyAlarmEntry 3 }
-- hwXponOntAlarmPolicyAlarmTable  end

  --Table hwXponOntFtpServerProfileTable 
        hwXponOntFtpServerProfileTable OBJECT-TYPE
            SYNTAX     SEQUENCE OF HwXponOntFtpServerProfileEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to create, delete or modify the FTP server profile.
 	         The index of this table is hwXponOntFtpServerProfileNameIndex.          
                "
            ::= { hwXponDeviceCommonProfileObjects 5 }
            
        hwXponOntFtpServerProfileEntry OBJECT-TYPE
            SYNTAX     HwXponOntFtpServerProfileEntry
            MAX-ACCESS not-accessible
            STATUS     current
            DESCRIPTION
                "This table is used to create, delete or modify the FTP server profile.
 	         The index of this entry is hwXponOntFtpServerProfileNameIndex.          
                "
            INDEX    { hwXponOntFtpServerProfileNameIndex }
            ::= { hwXponOntFtpServerProfileTable 1 }
            
        HwXponOntFtpServerProfileEntry ::=
            SEQUENCE {
                hwXponOntFtpServerProfileNameIndex             OCTET STRING,
                hwXponOntFtpServerProfileAddress          OCTET STRING,
                hwXponOntFtpServerProfilePort             Integer32,
                hwXponOntFtpServerProfileUserName         OCTET STRING,
                hwXponOntFtpServerProfilePassword         OCTET STRING,
                hwXponOntFtpServerProfileRowStatus        RowStatus
            }
            
        hwXponOntFtpServerProfileNameIndex OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..15))
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the name of FTP server profile."
            ::= { hwXponOntFtpServerProfileEntry 1 }
        
        hwXponOntFtpServerProfileAddress OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..63))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the address of FTP server profile.
                the format of string should be like ftp://127.0.0.1 or ftps://127.0.0.1
                "
            ::= { hwXponOntFtpServerProfileEntry 2 }
            
        hwXponOntFtpServerProfilePort OBJECT-TYPE
           SYNTAX      Integer32 (0..65535)
           MAX-ACCESS  read-write
           STATUS      current
           DESCRIPTION
                   "This object indicates the port of FTP server profile.
                   The default value is 21."
           ::= { hwXponOntFtpServerProfileEntry 3 }
           
        hwXponOntFtpServerProfileUserName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..15))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the user name of FTP server."
            ::= { hwXponOntFtpServerProfileEntry 4 }
            
        hwXponOntFtpServerProfilePassword OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..15))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object indicates the password of FTP server."
            ::= { hwXponOntFtpServerProfileEntry 5 }
        
        hwXponOntFtpServerProfileRowStatus OBJECT-TYPE
           SYNTAX RowStatus
           MAX-ACCESS read-create
           STATUS current
           DESCRIPTION
               "The hwXponOntFtpServerProfileRowStatus is used to create a new row
                 or to modify or to delete an existing row in this table.
                "
           ::= { hwXponOntFtpServerProfileEntry 6 }
            
  --hwXponOntFtpServerProfileTable end
  
        END
