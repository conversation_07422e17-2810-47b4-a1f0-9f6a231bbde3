--  =================================================================
-- Copyright (C) 2016 by  HUAWEI TECHNOLOGIES. All rights reserved
-- 
-- Description: HUAWEI DAD MIB, this mib will maintain information of DAD 
--              protocol for datacomm product.  
-- Reference:
-- Version:     V1.02
-- History:
--  
--  V1.00 2011-09-14 initial version
-- =================================================================

HUAWEI-DAD-MIB DEFINITIONS ::= BEGIN
    IMPORTS
    TruthValue,MacAddress,TEXTUAL-CONVENTION,RowStatus
        FROM SNMPv2-TC

	OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP            
        FROM SNMPv2-CONF
				
    MODULE-IDENTITY, OBJECT-TYPE, Integer32, Counter32,
    OBJECT-IDENTITY, Unsigned32, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
                
    hwDatacomm
        FROM HUAWEI-MIB
        		 		
    InterfaceIndex
        FROM IF-MIB

    EnabledStatus
        FROM P-BRIDGE-MIB;
 
    hwDadMIB MODULE-IDENTITY
        LAST-UPDATED "201607111230Z"     
        ORGANIZATION 
			"Huawei Technologies Co.,Ltd."
        CONTACT-INFO 
            "Huawei Industrial Base
			 Bantian, Longgang
			 Shenzhen 518129
			 People's Republic of China
			 Website: http://www.huawei.com
			 Email: <EMAIL>
            "
        DESCRIPTION
            "This MIB contains private managed object definitions for dual-active
            detection."
        REVISION "201607111230Z"   
        DESCRIPTION "Updated to include support for DAD" 

        REVISION "201601201658Z"   
        DESCRIPTION "Updated to include support for DAD" 
            ::= { hwDatacomm 246 }  
         
      -- Textual Convention
                 
        PortIndex ::= TEXTUAL-CONVENTION
               STATUS    current
            DESCRIPTION
                "Each port is uniquely identified by a port number. The port number ranges from 0
                to 575."
              SYNTAX Integer32(0..575) 
                      
                      
    -- ============================================================================
    -- Node definitions
    -- ============================================================================ 

    hwDadTraps OBJECT IDENTIFIER ::= { hwDadMIB 1 }
    hwDadConformance OBJECT IDENTIFIER ::= { hwDadMIB 2 }   
    hwDadObject OBJECT IDENTIFIER ::= { hwDadMIB 3}                           
                       
  -- ============================================================================
  --
  -- ======================= Objects definitions=================================
  --
  -- ============================================================================  
                                     
---Notifycation        
      hwDadConflictDetect  NOTIFICATION-TYPE
      STATUS current    
    DESCRIPTION 
          "Notify the NMS that dual-active scenario is detected."
    ::= { hwDadTraps 1}

      hwDadConflictResume  NOTIFICATION-TYPE
      STATUS current    
    DESCRIPTION 
          "Notify the NMS that dual-active scenario is merged."
    ::= { hwDadTraps 2}  
 
      hwDadPortProtocolAlarm NOTIFICATION-TYPE
	OBJECTS {hwDadDetectPort}
	STATUS current
	DESCRIPTION     
		"Notify the NMS that the protocol status of the dual-active port change to down."
	::= { hwDadTraps 3}  

      hwDadPortProtocolAlarmResume NOTIFICATION-TYPE
	OBJECTS {hwDadDetectPort}
	STATUS current
	DESCRIPTION     
		"Notify the NMS that the protocol status of the dual-active port change to up, or the dual-active port does not exist."
	::= { hwDadTraps 4}		
	
      hwDadTrapObject OBJECT IDENTIFIER ::= { hwDadObject 1}

      hwDadDetectPort OBJECT-TYPE
	SYNTAX OCTET STRING
	MAX-ACCESS accessible-for-notify
	STATUS current
	DESCRIPTION
		"The interface name of dual-active port."
	::= { hwDadTrapObject 1 }
    --
    -- ***********************************************************
    --
    -- HAUWEIDLDPMIBCONFORMANCE
    --
    -- ***********************************************************
    --
     
    hwDadCompliances OBJECT IDENTIFIER ::= { hwDadConformance 1 }
    hwDadGroups OBJECT IDENTIFIER ::= { hwDadConformance 2 }
    
    -- compliance statements
    hwDadCompliance MODULE-COMPLIANCE
    STATUS current
    DESCRIPTION
        "The compliance statement for SNMP entities which implement
        the HUAWEI-DAD-MIB."
    MODULE -- this module
    MANDATORY-GROUPS {
    hwDadTrapGroup,hwDadObjectGroup
    }
    ::= { hwDadCompliances 1 }    
       
    -- MIB groupings         
    hwDadTrapGroup NOTIFICATION-GROUP
    NOTIFICATIONS {  
    hwDadConflictDetect,
    hwDadConflictResume,
    hwDadPortProtocolAlarm,
    hwDadPortProtocolAlarmResume          
    }
    STATUS current
    DESCRIPTION
        "The collection of notifications used to indicate that the HUAWEI-DAD-MIB
        data is consistent and indicate the general status information.
        This group is mandatory for agents which implement the DAD
        and have the capability of receiving DAD frames."
    ::= { hwDadGroups 1 }  
    hwDadObjectGroup OBJECT-GROUP
        OBJECTS {
	 hwDadDetectPort
                }
        STATUS current
        DESCRIPTION
            "Enter the description of the created OBJECT-GROUP."
        ::= { hwDadGroups 2 }  
    
END     
    
--
-- HUAWEI-DAD-MIB.mib
-- 