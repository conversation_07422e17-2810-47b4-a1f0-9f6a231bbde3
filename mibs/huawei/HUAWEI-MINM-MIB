--  =================================================================================
-- Copyright (C) 2006 by HUAWEI TECHNOLOGIES. All rights reserved
-- 
-- Description: This mib file is used for configuration of MINM 
--              information.
-- Reference:
-- Version:     V1.0
-- History:
--              V1.0 wuchunqiang 60020627, 2006.10.30, publish
--                   pengpeng    60021576,2006.10.30 
--                   yuwei       43165,   2006.11.15                                                                  
--                   xiebojie   60021221 ,2007.05.22       
--                     zhangyinjuan 64060,  2008.06.18
-- =================================================================================

    HUAWEI-MINM-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            hwDatacomm            
                FROM HUAWEI-MIB            
            InterfaceIndex            
                FROM IF-MIB            
            EnabledStatus            
                FROM P-BRIDGE-MIB                        
            OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP            
                FROM SNMPv2-CONF            
            Integer32, Unsigned32, Counter32, Counter64, OBJECT-TYPE, 
            MODULE-IDENTITY, NOTIFICATION-TYPE            
                FROM SNMPv2-SMI            
            MacAddress, RowStatus, TruthValue, TimeStamp, TEXTUAL-CONVENTION            
                FROM SNMPv2-TC    
            VlanIdOrNone, VlanId     
                FROM Q-BRIDGE-MIB  -- [RFC4363]
            AddressFamilyNumbers     
                FROM IANA-ADDRESS-FAMILY-NUMBERS-MIB
            TransportDomain,TransportAddress         
                FROM TRANSPORT-ADDRESS-MIB -- [RFC3419] 
            VlanList
                FROM HUAWEI-L2IF-MIB 
            InterfaceIndexOrZero
                FROM IF-MIB;
            
    
    
        hwMinMMIB MODULE-IDENTITY 
            LAST-UPDATED "200611230000Z"        -- November 23, 2006 at 00:00 GMT
            ORGANIZATION 
                "Huawei Technologies Co., Ltd."
            CONTACT-INFO 
                "R&D Beijing, Huawei Technologies Co., Ltd.
                Huawei Bld., NO.3 Xinxi Rd, 
                Shang-Di Information Industry Base,
                Hai-Dian District Beijing P.R. China
                Zip: 100085 
                Http://www.huawei.com                                       
                E-mail:<EMAIL>"
            DESCRIPTION 
                "The HUAWEI-MINM-MIB contains objects to 
                Manage configuration for MINM feature.
                                                
                "
            ::= { hwMINM 1 }  

-- 
-- type definitions
--   
                    
        HWAdminStatus ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the administration status as follows:
                up(1),
                down(2)
                "
            SYNTAX INTEGER
                {
                up(1),
                down(2)
                }
            
        HWOperStatus ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the operation status as follows:
                up(1),
                down(2)
                "
            SYNTAX INTEGER
                {
                up(1),
                down(2)
                }
            
      HwDot1agCfmCcmInterval ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the interval at which CCMs are sent by a MEP.
                The possible values are:
                intervalInvalid(1) No CCMs are sent (disabled).
                interval300Hz(2)   CCMs are sent every 3 1/3 milliseconds
                                   (300Hz).
                interval10ms(3)    CCMs are sent every 10 milliseconds.
                interval100ms(4)   CCMs are sent every 100 milliseconds.
                interval1s(5)      CCMs are sent every 1 second.
                interval10s(6)     CCMs are sent every 10 seconds.
                interval1min(7)    CCMs are sent every minute.
                interval10min(8)   CCMs are sent every 10 minutes.
                interval20ms(9)    CCMs are sent every 10 milliseconds.
                interval30ms(10)    CCMs are sent every 10 milliseconds.
                interval50ms(11)   CCMs are sent every 10 milliseconds.
                
                Note: enumerations start at zero to match the 'CCM Interval
                      field' protocol field.
                "
            SYNTAX INTEGER
                {
                intervalInvalid(1),
                interval300Hz(2),
                interval10ms(3),
                interval100ms(4),
                interval1s(5),
                interval10s(6),
                interval1min(7),
                interval10min(8),
                interval20ms(9),
                interval30ms(10),
                interval50ms(11)
                }
            
        HwDot1agCfmRelayActionFieldValue ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the possible values the Relay action field can take."
            REFERENCE 
                "802.1ag clauses *********.3 g), *********, and Table 21-24.
                RlyHit(1) The LTM reached an MP whose MAC address matches the target MAC address. 
                RlyFDB(2) The Egress Port was determined by consulting the Filtering Database(*********:a).
                RlyMPDB(3) The Egress Port was determined by consulting the MIP CCM Database(*********:b).
                "
            SYNTAX INTEGER
                {
                rlyHit(1),
                rlyFdb(2),
                rlyMpdb(3)
                }    
                            
        HwLldpChassisIdSubtype ::= TEXTUAL-CONVENTION
        STATUS      current
        DESCRIPTION
            "This TC describes the source of a chassis identifier.

            The enumeration 'chassisComponent(1)' represents a chassis
            identifier based on the value of entPhysicalAlias object
            (defined in IETF RFC 2737) for a chassis component (i.e.,
            an entPhysicalClass value of 'chassis(3)').

            The enumeration 'interfaceAlias(2)' represents a chassis
            identifier based on the value of ifAlias object (defined in
            IETF RFC 2863) for an interface on the containing chassis.

            The enumeration 'portComponent(3)' represents a chassis
            identifier based on the value of entPhysicalAlias object
            (defined in IETF RFC 2737) for a port or backplane
            component (i.e., entPhysicalClass value of 'port(10)' or
            'backplane(4)'), within the containing chassis.

            The enumeration 'macAddress(4)' represents a chassis
            identifier based on the value of a unicast source address
            (encoded in network byte order and IEEE 802.3 canonical bit
            order), of a port on the containing chassis as defined in
            IEEE Std 802-2001.

            The enumeration 'networkAddress(5)' represents a chassis
            identifier based on a network address, associated with
            a particular chassis.  The encoded address is actually
            composed of two fields.  The first field is a single octet,
            representing the IANA AddressFamilyNumbers value for the
            specific address type, and the second field is the network
            address value.

            The enumeration 'interfaceName(6)' represents a chassis
            identifier based on the value of ifName object (defined in
            IETF RFC 2863) for an interface on the containing chassis.

            The enumeration 'local(7)' represents a chassis identifier
            based on a locally defined value."
    SYNTAX  INTEGER {
            chassisComponent(1),
            interfaceAlias(2),
            portComponent(3),
            macAddress(4),
            networkAddress(5),
            interfaceName(6),
            local(7)
    }

    HwLldpChassisId ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
            "This TC describes the format of a chassis identifier string.
            Objects of this type are always used with an associated
            LldpChassisIdSubtype object, which identifies the format of
            the particular LldpChassisId object instance.

            If the associated LldpChassisIdSubtype object has a value of
            'chassisComponent(1)', then the octet string identifies
            a particular instance of the entPhysicalAlias object
            (defined in IETF RFC 2737) for a chassis component (i.e.,
            an entPhysicalClass value of 'chassis(3)').

            If the associated LldpChassisIdSubtype object has a value
            of 'interfaceAlias(2)', then the octet string identifies
            a particular instance of the ifAlias object (defined in
            IETF RFC 2863) for an interface on the containing chassis.
            If the particular ifAlias object does not contain any values,
            another chassis identifier type should be used.

            If the associated LldpChassisIdSubtype object has a value
            of 'portComponent(3)', then the octet string identifies a
            particular instance of the entPhysicalAlias object (defined
            in IETF RFC 2737) for a port or backplane component within
            the containing chassis.

            If the associated LldpChassisIdSubtype object has a value of
            'macAddress(4)', then this string identifies a particular
            unicast source address (encoded in network byte order and
            IEEE 802.3 canonical bit order), of a port on the containing
            chassis as defined in IEEE Std 802-2001.

            If the associated LldpChassisIdSubtype object has a value of
            'networkAddress(5)', then this string identifies a particular
            network address, encoded in network byte order, associated
            with one or more ports on the containing chassis.  The first
            octet contains the IANA Address Family Numbers enumeration
            value for the specific address type, and octets 2 through
            N contain the network address value in network byte order.

            If the associated LldpChassisIdSubtype object has a value
            of 'interfaceName(6)', then the octet string identifies
            a particular instance of the ifName object (defined in
            IETF RFC 2863) for an interface on the containing chassis.
            If the particular ifName object does not contain any values,
            another chassis identifier type should be used.

            If the associated LldpChassisIdSubtype object has a value of
            'local(7)', then this string identifies a locally assigned
            Chassis ID."
    SYNTAX      OCTET STRING (SIZE (1..255))

    HwLldpPortIdSubtype ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
            "This TC describes the source of a particular type of port
            identifier used in the LLDP MIB.

            The enumeration 'interfaceAlias(1)' represents a port
            identifier based on the ifAlias MIB object, defined in IETF
            RFC 2863.

            The enumeration 'portComponent(2)' represents a port
            identifier based on the value of entPhysicalAlias (defined in
            IETF RFC 2737) for a port component (i.e., entPhysicalClass
            value of 'port(10)'), within the containing chassis.

            The enumeration 'macAddress(3)' represents a port identifier
            based on a unicast source address (encoded in network
            byte order and IEEE 802.3 canonical bit order), which has
            been detected by the agent and associated with a particular
            port (IEEE Std 802-2001).

            The enumeration 'networkAddress(4)' represents a port
            identifier based on a network address, detected by the agent
            and associated with a particular port.

            The enumeration 'interfaceName(5)' represents a port
            identifier based on the ifName MIB object, defined in IETF
            RFC 2863.

            The enumeration 'agentCircuitId(6)' represents a port
            identifier based on the agent-local identifier of the circuit
            (defined in RFC 3046), detected by the agent and associated
            with a particular port.

            The enumeration 'local(7)' represents a port identifier
            based on a value locally assigned."

    SYNTAX  INTEGER {
            interfaceAlias(1),
            portComponent(2),
            macAddress(3),
            networkAddress(4),
            interfaceName(5),
            agentCircuitId(6),
            local(7)
    }

    HwLldpPortId ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
            "This TC describes the format of a port identifier string.
            Objects of this type are always used with an associated
            LldpPortIdSubtype object, which identifies the format of the
            particular LldpPortId object instance.

            If the associated LldpPortIdSubtype object has a value of
            'interfaceAlias(1)', then the octet string identifies a
            particular instance of the ifAlias object (defined in IETF
            RFC 2863).  If the particular ifAlias object does not contain
            any values, another port identifier type should be used.

            If the associated LldpPortIdSubtype object has a value of
            'portComponent(2)', then the octet string identifies a
            particular instance of the entPhysicalAlias object (defined
            in IETF RFC 2737) for a port or backplane component.

            If the associated LldpPortIdSubtype object has a value of
            'macAddress(3)', then this string identifies a particular
            unicast source address (encoded in network byte order
            and IEEE 802.3 canonical bit order) associated with the port
            (IEEE Std 802-2001).

            If the associated LldpPortIdSubtype object has a value of
            'networkAddress(4)', then this string identifies a network
            address associated with the port.  The first octet contains
            the IANA AddressFamilyNumbers enumeration value for the
            specific address type, and octets 2 through N contain the
            networkAddress address value in network byte order.

            If the associated LldpPortIdSubtype object has a value of
            'interfaceName(5)', then the octet string identifies a
            particular instance of the ifName object (defined in IETF
            RFC 2863).  If the particular ifName object does not contain
            any values, another port identifier type should be used.

            If the associated LldpPortIdSubtype object has a value of
            'agentCircuitId(6)', then this string identifies a agent-local
            identifier of the circuit (defined in RFC 3046).

            If the associated LldpPortIdSubtype object has a value of
            'local(7)', then this string identifies a locally
            assigned port ID."
    SYNTAX      OCTET STRING (SIZE (1..255))

    HwLldpManAddrIfSubtype ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
            "This TC describes the basis of a particular type of
            interface associated with the management address.

            The enumeration 'unknown(1)' represents the case where the
            interface is not known.

            The enumeration 'ifIndex(2)' represents interface identifier
            based on the ifIndex MIB object.

            The enumeration 'systemPortNumber(3)' represents interface
            identifier based on the system port numbering convention."
    REFERENCE 
            "IEEE 802.1AB-2005 *******"
            
    SYNTAX  INTEGER {
            unknown(1),
            ifIndex(2),
            systemPortNumber(3)
    }

    HwLldpManAddress ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
            "The value of a management address associated with the LLDP
            agent that may be used to reach higher layer entities to
            assist discovery by network management.

            It should be noted that appropriate security credentials,
            such as SNMP engineId, may be required to access the LLDP
            agent using a management address.  These necessary credentials
            should be known by the network management and the objects
            associated with the credentials are not included in the
            LLDP agent."
    SYNTAX      OCTET STRING (SIZE (1..31))

        
                
    HwDot1agCfmIngressActionFieldValue ::= TEXTUAL-CONVENTION
             STATUS      current
             DESCRIPTION
                 "Possible values returned in the ingress action field."
             REFERENCE
                  "802.1ag clauses *********.3 k), ********, ********* and
                 Table 21-26.
                  "
             SYNTAX      INTEGER {
                  ingOk       (1),
                  ingDown     (2),
                  ingBlocked  (3),
                  ingVid      (4)
                }
                         
                         
        HwDot1agCfmEgressActionFieldValue ::= TEXTUAL-CONVENTION
             STATUS      current
             DESCRIPTION
               "Possible values returned in the egress action field"
             REFERENCE
                 "802.1ag clauses *********.3 o), ********* and Table 21-28"
             SYNTAX      INTEGER {
                  egrOK       (1),
                  egrDown     (2),
                  egrBlocked  (3),
                  egrVid      (4)
                }
            
        HWApsInterval ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "  Indicates the time interval for fast sending of Aps packets described in the G.8031. 
                By default, it is 3.3 ms.
                Optional values for sending interval are as follows:
                apsInterval3dot3ms(1): indicates a sending interval of 3.3 ms.
                apsInterval5ms(2): indicates a sending interval of 5 ms.
                apsInterval10ms(3): indicates a sending interval of 10 ms.
                apsInterval15ms(4): indicates a sending interval of 15 ms.
                apsInterval20ms(5): indicates a sending interval of 20 ms.
                apsInterval30ms(6): indicates a sending interval of 30 ms.
                "
            SYNTAX INTEGER
                {
                apsInterval3dot3ms(1),
                apsInterval5ms(2),
                apsInterval10ms(3),
                apsInterval15ms(4),
                apsInterval20ms(5),
                apsInterval30ms(6)
                }
                    
        HWProtectMode ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the protection modes of the G.8031 Aps protection group of mac-tunnels.
                oneplusonebidirectional(1): 1 + 1 switchover protection modes on both ends
                oneplusoneunidirectional(2): 1 + 1 switchover protection modes on a single end
                onetoone(3): 1:1 protection mode
                By the default, the 1:1 protection mode is used.
                
                "
            SYNTAX INTEGER
                {
                onePlusOneBidirectional(1),
                onePlusOneUnidirectional(2),
                oneToOne(3)
                }
            
        HWSwitchOperation ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the switchover commands for the Aps protection group of mac-tunnels.
                The priority levels in a descending order are: clear, lock, force and manual.
                clear(1): clears the lock, force and manual commands, and WTR state on the local end. 
                After the local commands are cleared, the WTR state cannot be entered.
                lock(2): locks the services on the working tunnel.
                force(3): forcibly switches the services to the protection tunnel when the protection tunnel is in sound state.
                manual(4): forcibly switches the services to the protection channel when the working and the protection tunnel are in sound state. 
                null(5):there is not manual commands.
                "
            SYNTAX INTEGER
                {
                clear(1),
                lock(2),
                force(3),
                manual(4),
                null(5)
                }
            
        HWProtectProtocol ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the protection protocol of the protection group of mac-tunnels.
                protocolAps(1): use an APS protocol to enhance the protection switching.
                protocolOam(2): not using any APS protocol to enhance the protection switching.
                By the default, the Protocol OAM is used.            
                "
            SYNTAX INTEGER
                { 
                protocolAps(1),                
                protocolOam(2)
                }                
        HWServiceType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the type of a service instance as follows:
                p2p(1): indicates the type of a point-to-point service instance.
                mp2mp(2): indicates the type of a multi-point to multi-point service instance.
                By default, the service type is mp2mp.
                "
            SYNTAX INTEGER
                {
                p2p(1),
                mp2mp(2)
                }
            
        HWInterfaceType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the interface type of a service instance. 
                The encApsulation mapping modes of the service instance can be configured as follows:
                transparent(1): indicates the transparent transmission mode.
                oneToOne(2): indicates the one-to-one in the s-tagged mode.
                bundling(3): indicates the bundling in the s-tagged mode.
                By default, the s-tagged bundling mode is used.
                
                "
            SYNTAX INTEGER
                {
                transparent(1),
                oneToOne(2),
                bundling(3)
                }
            
        HWProcessBehavior ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Indicates the processing behavior of packets as follows:
                discard(1),
                forward(2)
                "
            SYNTAX INTEGER
                {
                discard(1),
                forward(2)
                }
                         
        HWStaticMacFwdType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                " 
                Indicates the type of the static MAC forwarding table of a service instance:
                static(1): indicates a static entry.
                blackhole(2): indicates a blackhole entry.
                "
            SYNTAX INTEGER
                {
                static(1),
                blackhole(2)
                }
            
        HwDot1agCfmMepIdOrZero ::= TEXTUAL-CONVENTION
            DISPLAY-HINT 
                "d"
            STATUS current
            DESCRIPTION 
                "Indicates the Maintenance association End Point Identifier (MEPID): A small
                integer, unique over a given Maintenance Association,
                identifying a specific MEP.
                
                The special value 0 is allowed to indicate special cases, for
                example that no MEPID is configured in a given Maintenance
                Association point.
                
                Whenever an object is defined with this SYNTAX, then the
                DESCRIPTION clause of such an object MUST specify what the
                special value of 0 means.
                "
            SYNTAX Unsigned32 (0 | 1..8191)
            
    
-- 
-- end of type definitions
--       

-- 
--  Node definitions
--   

    
        hwMINM OBJECT IDENTIFIER ::= { hwDatacomm 133 }
        


        hwMinMObjects OBJECT IDENTIFIER ::= { hwMinMMIB 1 }
        

        
        hwMinMSystemObjects OBJECT IDENTIFIER ::= { hwMinMObjects 1 }
        

    
        hwMinMVirtualMac OBJECT-TYPE
            SYNTAX MacAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Indicates the virtual MAC address of the device. 
                By default, there is no virtual MAC address."
            ::= { hwMinMSystemObjects 1 }
        

    
        hwMinMMacTnlBVlanListLow OBJECT-TYPE
            SYNTAX  VlanList (SIZE(256))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Indicates the starting value of the backbone VLAN that is assigned to the mac-tunnel."
            ::= { hwMinMSystemObjects 2 }
        
        
        hwMinMMacTnlBVlanListHigh OBJECT-TYPE
            SYNTAX  VlanList (SIZE(256))
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Indicates the ending value of the backbone VLAN that is assigned to the mac-tunnel."
            ::= { hwMinMSystemObjects 3 }
        


        hwMinMTrapEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Indicates that the Snmp-Agent Trap is enabled on the Mac-in-Mac.By default, it is disable."
            DEFVAL { 2 }
            ::= { hwMinMSystemObjects 4 }
        

    
        hwMinMMacTnlObjects OBJECT IDENTIFIER ::= { hwMinMObjects 2 }
        

        hwMinMMacTnlCfgObjects OBJECT IDENTIFIER ::= { hwMinMMacTnlObjects 1 }
        

        hwMinMMacTnlIndexNext OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the Index of the Next Mac Tunnel.It begins with one."
            ::= { hwMinMMacTnlCfgObjects 1 }
        

        hwMinMMacTnlCfgTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlCfgEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates a configuration information table of Mac-tunnel."
            ::= { hwMinMMacTnlCfgObjects 2 }
    
    
        hwMinMMacTnlCfgEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlCfgEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the entry in the tunnel configuration table of the mac-tunnel."
            INDEX { hwMinMMacTnlIndex }
            ::= { hwMinMMacTnlCfgTable 1 }
        
        HwMinMMacTnlCfgEntry ::=
            SEQUENCE { 
                hwMinMMacTnlIndex
                    Unsigned32,
                hwMinMMacTnlName
                    OCTET STRING,
                hwMinMMacTnlDMac
                    MacAddress,
                hwMinMMacTnlBVlanID
                    VlanIdOrNone,
                hwMinMMacTnlBVlanType
                    OCTET STRING,
                hwMinMMacTnlPriorityValue
                    Integer32,
                hwMinMMacTnlOutgoingIfIndex
                    InterfaceIndexOrZero,
                hwMinMMacTnlSplitHorizonEnable
                    EnabledStatus,
                hwMinMMacTnlAdminStatus
                    HWAdminStatus,
                hwMinMMacTnlOperStatus
                    HWOperStatus,
                hwMinMMacTnlDescription
                    OCTET STRING, 
                    hwMinMMacTnlStatisticsReset
                    EnabledStatus,  
                hwMinMMacTnlPriorityTrustITag
                        TruthValue, 
                hwMinMMacTnlDeiTrustIDei 
                        TruthValue,     
                hwMinMMacTnlDeiValue 
                        Integer32,
                hwMinMMacTnlRowStatus
                    RowStatus
             }
        
        hwMinMMacTnlIndex OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the index of Mac Tunnel. It begins with one."
            ::= { hwMinMMacTnlCfgEntry 1 }
        

        hwMinMMacTnlName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..31))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the name of the mac-tunnel. 
                It is a character string with a maximum of 31 bytes and a minimum of 1 byte."
            ::= { hwMinMMacTnlCfgEntry 11 }
        

        hwMinMMacTnlDMac OBJECT-TYPE
            SYNTAX MacAddress
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the remote MAC address of the current mac-tunnel. 
                By default, there is no such configuration."
            ::= { hwMinMMacTnlCfgEntry 12 }
        

        hwMinMMacTnlBVlanID OBJECT-TYPE
            SYNTAX VlanIdOrNone
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates BVLAN value of the current BVLAN used by the mac-tunnel: It ranges from 1 to 4094. By default, BVLAN value is not configured.
                The VLAN here must have been created and be in the backbone VLAN used by the mac-tunnel. 
                The special value of zero is used to indicate that no VLAN-ID is present or used.
                "
            ::= { hwMinMMacTnlCfgEntry 13 }
        
        
        hwMinMMacTnlBVlanType OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE(2))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "               
                Indicates the B-TAG type of the BVLAN. By default, it is 88a8.
                "  
            ::= { hwMinMMacTnlCfgEntry 14 }
        

        hwMinMMacTnlPriorityValue OBJECT-TYPE
            SYNTAX Integer32 (0..8)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates priority level of the mac-tunnel. 
                It is used to set the priority field of B-TAG.
                There are eight priority levels. By default, the I-TAG priority is used.
                The special value of eight is used to indicate that priority was unknown or none.
                "
            ::= { hwMinMMacTnlCfgEntry 15 }
        

        hwMinMMacTnlOutgoingIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndexOrZero 
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the port used in the mac-tunnel. By default, it is not configured.
                The value zero is used to indicate that interface was unknown or none."
            ::= { hwMinMMacTnlCfgEntry 16 }
        


        hwMinMMacTnlSplitHorizonEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the split horizon of tunnels. By default, it is enabled.
                When split horizon is enabled on two mac-tunnels, the two tunnels cannot exchange packets.
                "
            ::= { hwMinMMacTnlCfgEntry 17 }
        
        
        hwMinMMacTnlAdminStatus OBJECT-TYPE
            SYNTAX HWAdminStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the administration status of the mac-tunnel."
            ::= { hwMinMMacTnlCfgEntry 18 }
    
        hwMinMMacTnlOperStatus OBJECT-TYPE
            SYNTAX HWOperStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the physical state of the mac-tunnel."
            ::= { hwMinMMacTnlCfgEntry 19 }
        
        hwMinMMacTnlDescription OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..80))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates descriptive information of the static Mac-In-Mac tunnel. 
                Its length ranges from 1 to 80 bytes and the first byte cannot be a space.
                "
            ::= { hwMinMMacTnlCfgEntry 20 }   
            
        hwMinMMacTnlStatisticsReset OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the reset on traffic statistics of the mac-tunnel."
            ::= { hwMinMMacTnlCfgEntry 21 } 
            
        hwMinMMacTnlPriorityTrustITag OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that the I-TAG priority is copied to the mac-tunnel. 
                By default, the I-TAG priority is not copied to the mac-tunnel."    
            DEFVAL { 2 }    
            ::= { hwMinMMacTnlCfgEntry 22 }
            
        hwMinMMacTnlDeiTrustIDei OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that the I-DEI priority is copied to the mac-tunnel. 
                By default, the I-DEI priority is not copied to the mac-tunnel." 
            DEFVAL { 2 }     
            ::= { hwMinMMacTnlCfgEntry 23 }   
            
        hwMinMMacTnlDeiValue OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates DEI of the mac-tunnel. It is used to set the DEI field of B-TAG. 
                When DEI is true, the value is set to 1. 
                When DEI is false, the value is set to 0. 
                By default, the I-DEI priority is not copied to the mac-tunnel, and the DEI is false. 
                When the value of the DEI is set to 2, it indicates that no DEI 
                is configured on the tunnel. "      
            DEFVAL { 0 }       
            ::= { hwMinMMacTnlCfgEntry 24 }            
    
        hwMinMMacTnlRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the RowStatus. 
                The following three actions are used: active, createAndGo, destroy"
            ::= { hwMinMMacTnlCfgEntry 51 }
        
        
        hwMinMMacTnlStatisticsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates a query for the hwMinMMacTnlStatisticsTable of the mac-tunnel. "
            ::= { hwMinMMacTnlCfgObjects 3 }
                
        hwMinMMacTnlStatisticsEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates an entry query for the hwMinMMacTnlStatisticsTable of the mac-tunnel."
            INDEX { hwMinMMacTnlIndex }
            ::= { hwMinMMacTnlStatisticsTable 1 }
        
        HwMinMMacTnlStatisticsEntry ::=
            SEQUENCE { 
                hwMinMMacTnlInPackets
                    Counter64,
                hwMinMMacTnlInBytes
                    Counter64,
                hwMinMMacTnlOutPackets
                    Counter64,
                hwMinMMacTnlOutBytes
                    Counter64
            }
        
        hwMinMMacTnlInPackets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of packets received by the mac-tunnel. "
            ::= { hwMinMMacTnlStatisticsEntry 11 }
            
        hwMinMMacTnlInBytes OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of bytes received by the mac-tunnel."
            ::= { hwMinMMacTnlStatisticsEntry 12 }
            
        hwMinMMacTnlOutPackets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of packets sent by the mac-tunnel. "
            ::= { hwMinMMacTnlStatisticsEntry 13 }

        hwMinMMacTnlOutBytes OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of bytes sent by the mac-tunnel."
            ::= { hwMinMMacTnlStatisticsEntry 14 }
        
        
        hwMacTnlNameToIndexMappingTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMacTnlNameToIndexMappingEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the mapping table of the tunnel name and tunnel index."
            ::= { hwMinMMacTnlCfgObjects 4 }
        
        hwMacTnlNameToIndexMappingEntry OBJECT-TYPE
            SYNTAX HwMacTnlNameToIndexMappingEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the mapping table entry of the tunnel name and tunnel index."
            INDEX { hwMacTnlName }
            ::= { hwMacTnlNameToIndexMappingTable 1 }
        
        HwMacTnlNameToIndexMappingEntry ::=
            SEQUENCE { 
                hwMacTnlName
                    OCTET STRING,
                hwMacTnlIndex
                    Unsigned32
             }

    
        hwMacTnlName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..31))
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the name of the mac-tunnel. 
                It is a character string with a maximum of 31 bytes and a minimum of 1 byte. "
            ::= { hwMacTnlNameToIndexMappingEntry 1 }
            
        hwMacTnlIndex OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the tunnel index.It begins with one."
            ::= { hwMacTnlNameToIndexMappingEntry 11 }
        

    
        hwMinMMacTnlOamObjects OBJECT IDENTIFIER ::= { hwMinMMacTnlObjects 2 }
        
        hwMinMMacTnlCCTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlCCEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the parameters used to describe CC packets.
                "
            ::= { hwMinMMacTnlOamObjects 1 }
        
        hwMinMMacTnlCCEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlCCEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the CC table entry."
            INDEX { hwMinMMacTnlIndex }
            ::= { hwMinMMacTnlCCTable 1 }
        
        HwMinMMacTnlCCEntry ::=
            SEQUENCE { 
                hwMinMMacTnlCfmEnable
                    EnabledStatus,
                hwMinMMacTnlCCInterval
                    HwDot1agCfmCcmInterval,
                hwMinMMacTnlSomeRMepCcmDefect
                    TruthValue,
                hwMinMMacTnlSomeRdiDefect
                    TruthValue,   
                hwMinMMacTnlCcReceiveEnabled
                    TruthValue, 
                hwMinMMacTnlCCRowStatus
                    RowStatus
             }


        hwMinMMacTnlCfmEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that CFM is enabled."
            ::= { hwMinMMacTnlCCEntry 11 }
        
        hwMinMMacTnlCCInterval OBJECT-TYPE
            SYNTAX HwDot1agCfmCcmInterval
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the time interval of CC packets."
            DEFVAL { 3 }
            ::= { hwMinMMacTnlCCEntry 12 }
        
        hwMinMMacTnlSomeRMepCcmDefect OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates a connectivity failure of tunnels."
            ::= { hwMinMMacTnlCCEntry 13 }
        
        hwMinMMacTnlSomeRdiDefect OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates whether the RDI packet from the remote end is received."
            ::= { hwMinMMacTnlCCEntry 14 }   
        
        hwMinMMacTnlCcReceiveEnabled OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Whether CC reception is enabled."
            ::= { hwMinMMacTnlCCEntry 15 }   
        
        hwMinMMacTnlCCRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the row status."
            ::= { hwMinMMacTnlCCEntry 51 }
        
        hwMinMMacTnlLbTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlLbEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the parameters used to describe Lb packets."
            ::= { hwMinMMacTnlOamObjects 2 }
        
        hwMinMMacTnlLbEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlLbEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the loopback table entry."
            INDEX { hwMinMMacTnlIndex }
            ::= { hwMinMMacTnlLbTable 1 }
        
        HwMinMMacTnlLbEntry ::=
            SEQUENCE { 
                hwMinMMacTnlLbmEnable
                    EnabledStatus,
                hwMinMMacTnlLbmTimeStamp
                    TimeStamp,
                hwMinMMacTnlLbmTimeOut
                    Integer32,
                hwMinMMacTnlLbmTimes
                    Integer32,       
                hwMinMMacTnlLbmSize
                    Integer32,     
                hwMinMMacTnlLbrIn
                    Counter32,
                hwMinMMacTnlLbmResult 
                    TruthValue,
                hwMinMMacTnlLbRowStatus
                    RowStatus
             }

        hwMinMMacTnlLbmEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "
                Indicates that the MAC Ping is started inside the tunnel.
                "
            ::= { hwMinMMacTnlLbEntry 11 }
        
        hwMinMMacTnlLbmTimeStamp OBJECT-TYPE
            SYNTAX TimeStamp
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the operation time."
            ::= { hwMinMMacTnlLbEntry 12 }
        
        hwMinMMacTnlLbmTimeOut OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the timeout period entered by a user, in ms. By default, it is 2000."
            DEFVAL { 2000 }
            ::= { hwMinMMacTnlLbEntry 13 }
        
        hwMinMMacTnlLbmTimes OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "
                Indicates the number of times that Ping is entered.
                "
            ::= { hwMinMMacTnlLbEntry 14 }
        
        hwMinMMacTnlLbmSize OBJECT-TYPE
            SYNTAX Integer32 (64..1480)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "
                Indicates the size of an Lbm packet.
                "                     
            DEFVAL { 64 }
            ::= { hwMinMMacTnlLbEntry 15 }

        hwMinMMacTnlLbrIn OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of valid LBR packets received "
            ::= { hwMinMMacTnlLbEntry 16 }  
            

       hwMinMMacTnlLbmResult OBJECT-TYPE
            SYNTAX TruthValue                         
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                Whether the MAC ping operation for a tunnel is over.
                "
            ::= { hwMinMMacTnlLbEntry 17 }

        hwMinMMacTnlLbRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the row status."
            ::= { hwMinMMacTnlLbEntry 51 }   
            
           hwMinMMacTnlLbResultTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlLbResultEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " 
                Performance statistics returned by using the MAC ping command for a tunnel.
                "
            ::= { hwMinMMacTnlOamObjects 3 } 
            
        hwMinMMacTnlLbResultEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlLbResultEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the loopback table entry."
            INDEX { hwMinMMacTnlIndex }
            ::= { hwMinMMacTnlLbResultTable 1 }
            
        HwMinMMacTnlLbResultEntry ::= 
            SEQUENCE { 
                hwMinMMacTnlMacPingRTTMin
                    Gauge32,
                hwMinMMacTnlMacPingRTTMax
                    Gauge32,
                hwMinMMacTnlMacPingRTTAvg
                    Gauge32,
                hwMinMMacTnlMacPingPacketLossRatio
                    Gauge32            
                     }   
                     
        hwMinMMacTnlMacPingRTTMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                The minimum round trip time.
                "
            ::= { hwMinMMacTnlLbResultEntry 11 }
        
        hwMinMMacTnlMacPingRTTMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                The maximum round trip time.
                "
            ::= { hwMinMMacTnlLbResultEntry 12 }   
            
        hwMinMMacTnlMacPingRTTAvg  OBJECT-TYPE
            SYNTAX   Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                The average round trip time.
                "
            ::= { hwMinMMacTnlLbResultEntry 13 }  

        hwMinMMacTnlMacPingPacketLossRatio  OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                The packet loss ratio.
                "
            ::= { hwMinMMacTnlLbResultEntry 14 } 
            
        
        hwMinMMacTnlLtmTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlLtmEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the parameters used to describe Ltm packets. "
            ::= { hwMinMMacTnlOamObjects 4 }
        
        hwMinMMacTnlLtmEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlLtmEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the linktrace table entry."
            INDEX { hwMinMMacTnlIndex }
            ::= { hwMinMMacTnlLtmTable 1 }
        
        HwMinMMacTnlLtmEntry ::=
            SEQUENCE { 
                hwMinMMacTnlLtmEnable
                    EnabledStatus,
                hwMinMMacTnlLtmTimeStamp
                    TimeStamp,
                hwMinMMacTnlLtmTimeOut
                    Integer32,    
                hwMinMMacTnlLtmFlags     
                    BITS,
                hwMinMMacTnlLtmTtl
                    Unsigned32,    
                hwMinMMacTnlLtmSeqNumber
                    Unsigned32,
                hwMinMMacTnlLtmEgressIdentifier
                    Unsigned32,
                hwMinMMacTnlLtmResult
                    TruthValue,
                hwMinMMacTnlLtmRowStatus
                    RowStatus
             }

        hwMinMMacTnlLtmEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "
                Indicates that the MAC Trace is started inside the tunnel. 
                "
            ::= { hwMinMMacTnlLtmEntry 11 }
        
     hwMinMMacTnlLtmTimeStamp OBJECT-TYPE
            SYNTAX TimeStamp
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                The time stamp for enabling the MAC trace operation for a tunnel.
                "
            ::= { hwMinMMacTnlLtmEntry 12 }
        
        hwMinMMacTnlLtmTimeOut OBJECT-TYPE
            SYNTAX Integer32 (0..65535)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "
                Indicates the timeout period entered by a user, in ms. By default, it is 2000.
                "
            DEFVAL { 2000 }
            ::= { hwMinMMacTnlLtmEntry 13 }   
            
        hwMinMMacTnlLtmTtl OBJECT-TYPE
            SYNTAX Unsigned32 (0..255)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "
                Indicates the living cycle of Ltm packets.
                "
            DEFVAL { 64 }
            ::= { hwMinMMacTnlLtmEntry 14 } 
        
        hwMinMMacTnlLtmFlags OBJECT-TYPE
            SYNTAX      BITS {
                  useFDBonly   (0)
                }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "
                The flags field of an LTM packet.
                "
            DEFVAL { {useFDBonly } }
                ::= { hwMinMMacTnlLtmEntry 15 }
    
        hwMinMMacTnlLtmSeqNumber OBJECT-TYPE
            SYNTAX      Unsigned32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
            "
            The sequence number of an LTM packet.
            "
           ::= { hwMinMMacTnlLtmEntry 16 }
        
        hwMinMMacTnlLtmEgressIdentifier OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the egress ID of Ltm packets."
            ::= { hwMinMMacTnlLtmEntry 17 }
        
        hwMinMMacTnlLtmResult OBJECT-TYPE
            SYNTAX TruthValue                         
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                Two conditions are used to determine whether the MAC trace operation to a tunnel is over: (1) The peer of a tunnel has been traced. (2) The preset timer for timeout is triggered.
                Either condition indicates the completion of an operation.
                "
            ::= { hwMinMMacTnlLtmEntry 18 }
        

        hwMinMMacTnlLtmRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the row status."
            ::= { hwMinMMacTnlLtmEntry 51 }
        
        hwMinMMacTnlLtrTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlLtrEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the parameters used to describe Ltr packets. "
            ::= { hwMinMMacTnlOamObjects 5 }
        
        hwMinMMacTnlLtrEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlLtrEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "
                Indicates the linktraceRelay table entry."
            INDEX { hwMinMMacTnlIndex,
                    hwMinMMacTnlLtrSeqNumber,
                    hwMinMMacTnlLtrReceiveOrder }
            ::= { hwMinMMacTnlLtrTable 1 }
        
        HwMinMMacTnlLtrEntry ::=
            SEQUENCE { 
                hwMinMMacTnlLtrSeqNumber                  Unsigned32,
                hwMinMMacTnlLtrReceiveOrder               Unsigned32,    
                hwMinMMacTnlLtrTtl                        Unsigned32,
                hwMinMMacTnlLtrForwarded                  TruthValue,
                hwMinMMacTnlLtrLastEgressIdentifier       OCTET STRING,
                hwMinMMacTnlLtrNextEgressIdentifier       OCTET STRING,
                hwMinMMacTnlLtrRelay                      HwDot1agCfmRelayActionFieldValue,
                   hwMinMMacTnlLtrIngress                    HwDot1agCfmIngressActionFieldValue,
                  hwMinMMacTnlLtrIngressMac                 MacAddress,
                  hwMinMMacTnlLtrIngressPortIdSubtype       HwLldpPortIdSubtype,
                  hwMinMMacTnlLtrIngressPortId              HwLldpPortId,
                  hwMinMMacTnlLtrEgress                     HwDot1agCfmEgressActionFieldValue,
                  hwMinMMacTnlLtrEgressMac                  MacAddress,
                  hwMinMMacTnlLtrEgressPortIdSubtype        HwLldpPortIdSubtype,
                   hwMinMMacTnlLtrEgressPortId               HwLldpPortId
              }

              hwMinMMacTnlLtrSeqNumber OBJECT-TYPE
                    SYNTAX      Unsigned32                       
                    MAX-ACCESS  not-accessible
                    STATUS      current
                    DESCRIPTION
                       "
                       Indicates the parameters used to describe Ltr packets.
                       "
                    ::= { hwMinMMacTnlLtrEntry 1}
                
                hwMinMMacTnlLtrReceiveOrder OBJECT-TYPE
                    SYNTAX      Unsigned32                    
                    MAX-ACCESS  not-accessible
                    STATUS      current
                    DESCRIPTION
                       "An index to distinguish among multiple LTRs with the same LTR
                        Transaction Identifier field value.
                       "
                    REFERENCE
                       "802.1ag clause *********.2"
                    ::= { hwMinMMacTnlLtrEntry 2 }  
                    
                hwMinMMacTnlLtrTtl OBJECT-TYPE
                      SYNTAX      Unsigned32 (0..255)
                      MAX-ACCESS  read-only
                      STATUS      current
                      DESCRIPTION
                      "TTL field value for a returned LTR."
                      ::= { hwMinMMacTnlLtrEntry 11 }
                    
                
        
        hwMinMMacTnlLtrForwarded OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "
                Indicates an identifier that shows whether the LTM packet should be forwarded to the next hop.    
                "
            ::= { hwMinMMacTnlLtrEntry 12 }
        
        hwMinMMacTnlLtrLastEgressIdentifier OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (8))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "LTR packet is a response packet to a LTM packet. 
                The LTM packet is the last packet sent by a port on a device. 
                This variable indicates the port identifier of the LTM packet. 
                The last six bytes indicate the MAC address, and the first two bytes represent the board number and outbound port number respectively."
            ::= { hwMinMMacTnlLtrEntry 13 }
        
       hwMinMMacTnlLtrNextEgressIdentifier OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (8))
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "If the LTM packet needs to be forwarded to the next hop, the port identifier of the next hop should be added to the TVL field in the response LTR packet. The last six bytes indicate the MAC address, and the first two bytes represent the board number and outbound port number respectively."
            ::= { hwMinMMacTnlLtrEntry 14 }
        
                hwMinMMacTnlLtrRelay OBJECT-TYPE
            SYNTAX HwDot1agCfmRelayActionFieldValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the value in the Relay Action field."
            ::= { hwMinMMacTnlLtrEntry 15 }  

        hwMinMMacTnlLtrIngress OBJECT-TYPE
                    SYNTAX      HwDot1agCfmIngressActionFieldValue
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "The value returned in the Ingress Action Field of the LTM."
                    ::= { hwMinMMacTnlLtrEntry 16 }
                
                hwMinMMacTnlLtrIngressMac OBJECT-TYPE
                    SYNTAX      MacAddress
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "MAC address returned in the ingress MAC address field."
                    ::= { hwMinMMacTnlLtrEntry 17 }
                
                hwMinMMacTnlLtrIngressPortIdSubtype OBJECT-TYPE
                    SYNTAX      HwLldpPortIdSubtype
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "Format of the Ingress Port ID."
                   ::= { hwMinMMacTnlLtrEntry 18 }
                
                hwMinMMacTnlLtrIngressPortId OBJECT-TYPE
                    SYNTAX      HwLldpPortId
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "Ingress Port ID. The format of this object is determined by
                        the value of the dot1agCfmLtrIngressPortIdSubtype object.
                       "
                    ::= { hwMinMMacTnlLtrEntry 19 }
                
                hwMinMMacTnlLtrEgress OBJECT-TYPE
                    SYNTAX      HwDot1agCfmEgressActionFieldValue
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "The value returned in the Egress Action Field of the LTM."
                    ::= { hwMinMMacTnlLtrEntry 20 }
                
                hwMinMMacTnlLtrEgressMac OBJECT-TYPE
                    SYNTAX      MacAddress
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "MAC address returned in the egress MAC address field."
                    ::= { hwMinMMacTnlLtrEntry 21 }
                
                hwMinMMacTnlLtrEgressPortIdSubtype OBJECT-TYPE
                    SYNTAX      HwLldpPortIdSubtype
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "Format of the egress Port ID."
                    ::= { hwMinMMacTnlLtrEntry 22 }
                
                hwMinMMacTnlLtrEgressPortId OBJECT-TYPE
                    SYNTAX      HwLldpPortId
                    MAX-ACCESS  read-only
                    STATUS      current
                    DESCRIPTION
                       "Egress Port ID. The format of this object is determined by
                        the value of the dot1agCfmLtrEgressPortIdSubtype object.
                       "
                    ::= { hwMinMMacTnlLtrEntry 23 }

                
        hwMinMMacTnlApsObjects OBJECT IDENTIFIER ::= { hwMinMMacTnlObjects 3 }
                
        hwMinMMacTnlApsCfgTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMMacTnlApsCfgEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the configuration table for the Aps protection of the mac-tunnel."
            ::= { hwMinMMacTnlApsObjects 1 }
        

        hwMinMMacTnlApsCfgEntry OBJECT-TYPE
            SYNTAX HwMinMMacTnlApsCfgEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the configuration table entry for the Aps protection of the mac-tunnel. "
            INDEX { hwMinMMacTnlIndex }
            ::= { hwMinMMacTnlApsCfgTable 1 }
        
        HwMinMMacTnlApsCfgEntry ::=
            SEQUENCE { 
                hwMinMProtectMacTnlIndex
                    Unsigned32,
                hwMinMProtectMacTnlName
                    OCTET STRING,
                hwMinMProtectMacTnlDMac
                    MacAddress,
                hwMinMProtectMacTnlBVlanID
                    VlanId,
                hwMinMProtectApsSwitchMode
                    HWProtectMode,
                hwMinMProtectProtocolApsEnable
                    EnabledStatus,
                hwMinMProtectApsFastInterval
                    HWApsInterval,
                hwMinMProtectHoldoffTime
                    Integer32,
                hwMinMProtectRevMode
                    EnabledStatus,
                hwMinMProtectRevWtrTime
                    Integer32,
                hwMinMProtectSwitchOperation
                    HWSwitchOperation,
                hwMinMProtectProtocol
                        HWProtectProtocol,
                hwMinMProtectRowStatus
                    RowStatus
             }
                     
        hwMinMProtectMacTnlIndex OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the index of ProtectMacTunnel. It begins with one."
            ::= { hwMinMMacTnlApsCfgEntry 11 }

        hwMinMProtectMacTnlName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..31))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the tunnel name of the protective mac-tunnel."
            ::= { hwMinMMacTnlApsCfgEntry 12 }
        

        hwMinMProtectMacTnlDMac OBJECT-TYPE
            SYNTAX MacAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the remote MAC address of the protecting mac-tunnel."
            ::= { hwMinMMacTnlApsCfgEntry 13 }
        

        hwMinMProtectMacTnlBVlanID OBJECT-TYPE
            SYNTAX VlanId
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates BVLAN value used by the protecting mac-tunnel: It ranges from 1 to 4094."
            ::= { hwMinMMacTnlApsCfgEntry 14 }        

        hwMinMProtectApsSwitchMode OBJECT-TYPE
            SYNTAX HWProtectMode
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the protection modes of the G.8031 Aps protection group of mac-tunnels.
                oneplusonebidirectional(1): 1 + 1 switchover protection modes on both ends
                oneplusoneunidirectional(2): 1 + 1 switchover protection modes on a single end
                onetoone(3): 1:1 protection mode
                By the default, the 1:1 protection mode is used.
                "    
            DEFVAL { 3 }
            ::= { hwMinMMacTnlApsCfgEntry 15 }
        

        hwMinMProtectProtocolApsEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that the G.8031 protocol of Aps protection group in the mac-tunnel is enabled. 
                By default, the protocol is disabled.
                "  
            DEFVAL { 2 }
            ::= { hwMinMMacTnlApsCfgEntry 16 }
        
        hwMinMProtectApsFastInterval OBJECT-TYPE
            SYNTAX HWApsInterval
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "  Indicates the configuration of fast sending time interval for each protection group.
                The time intervals for fast sending of Aps packets described in the G.8031 are as follows:
                By default, the time interval for fast sending of Aps packets in G.8031 is 3.3 ms.
                Optional values for sending interval are as follows:
                ApsInterval3dot3ms(1): indicates a sending interval of 3.3 ms.
                ApsInterval5ms(3): indicates a sending interval of 5 ms.
                ApsInterval10ms(3): indicates a sending interval of 10 ms.
                ApsInterval5ms(4): indicates a sending interval of 15 ms.
                ApsInterval20ms(5): indicates a sending interval of 20 ms.
                ApsInterval30ms(6): indicates a sending interval of 30 ms." 
            DEFVAL { 1 }
            ::= { hwMinMMacTnlApsCfgEntry 17 }
        
        hwMinMProtectHoldoffTime OBJECT-TYPE
            SYNTAX Integer32 (0..100)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the switchover restoration time of Aps protection groups in the mac-tunnel, in 100 ms. 
                It ranges from 0 to 100.
                By default, the switchover restoration time is not delayed. The value of delay is zero.
                "     
            DEFVAL { 0 }
            ::= { hwMinMMacTnlApsCfgEntry 18 }
        

        hwMinMProtectRevMode OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the non-restoration protection switchover mode of Aps protection group in mac-tunnels."
            ::= { hwMinMMacTnlApsCfgEntry 19 }
        

        hwMinMProtectRevWtrTime OBJECT-TYPE
            SYNTAX Integer32 (0..120)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the restoration time of restoration protection switchover mode of Aps protection group in mac-tunnels.
                By default, the switchover mode is restoration. The restoration time is five minutes." 
            DEFVAL { 5 }
            ::= { hwMinMMacTnlApsCfgEntry 20 }
        

        hwMinMProtectSwitchOperation OBJECT-TYPE
            SYNTAX HWSwitchOperation
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the switchover commands for the Aps protection group of mac-tunnels.
                The priority levels in a descending order are: clear, lock, force and manual.
                clear(1): clears the lock, force and manual commands, and WTR state on the local end. 
                After the local commands are cleared, the WTR state cannot be entered.
                lock(2): locks the services on the working tunnel.
                force(3): forcibly switches the services to the protection tunnel when the protection tunnel is in sound state.
                manual(4): forcibly switches the services to the protection channel when the working and the protection tunnel are in sound state. 
                null(5):there is not manual commands."
            ::= { hwMinMMacTnlApsCfgEntry 21 }
        
        hwMinMProtectProtocol OBJECT-TYPE
            SYNTAX HWProtectProtocol
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the protection protocol of the protection group of mac-tunnels.
                ProtocolAPS(1): use an APS protocol to enhance the protection switching.
                ProtocolOAM(2): not using any APS protocol to enhance the protection switching.
                By the default, the ProtocolOAM is used.            
                "
            ::= { hwMinMMacTnlApsCfgEntry 22 }


        hwMinMProtectRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the RowStatus. 
                The following three actions are used: active, createAndGo, destroy"
            ::= { hwMinMMacTnlApsCfgEntry 51 }    
            
        
        hwMinMSIObjects OBJECT IDENTIFIER ::= { hwMinMObjects 3 }
        
        hwMinMSIIndexNext OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the index of the next Service Instance. It begins with one."
            ::= { hwMinMSIObjects 1 }
        

        hwMinMSICfgTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMSICfgEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the configuration table of a service instance."
            ::= { hwMinMSIObjects 2 }
        

        hwMinMSICfgEntry OBJECT-TYPE
            SYNTAX HwMinMSICfgEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the configuration table entry of a service instance."
            INDEX { hwMinMSIIndex }
            ::= { hwMinMSICfgTable 1 }
        
        HwMinMSICfgEntry ::=
            SEQUENCE { 
                hwMinMSIIndex
                    Unsigned32,
                hwMinMSIID
                    Integer32,
                hwMinMSIName
                    OCTET STRING,
                hwMinMSIServiceType
                    HWServiceType,
                hwMinMSIPriorityTrust8021p
                    TruthValue,
                hwMinMSIPriorityValue
                    Integer32,
                hwMinMSIInterfaceType
                    HWInterfaceType,
                hwMinMSIAdminStatus
                    HWAdminStatus,
                hwMinMSIOperStatus
                    HWOperStatus,
                hwMinMSIMacLearningEnable
                    EnabledStatus,
                hwMinMSIMacLimitAction
                    HWProcessBehavior,
                hwMinMSIMacLimitAlarm
                    EnabledStatus,
                hwMinMSIMacLimitMaxinum
                    Integer32,
                hwMinMSIL2CtrlProProcess
                    BITS,
                hwMinMSIUnknownUnicastEnbale
                    EnabledStatus,
                hwMinMSIMulticastEnable
                    EnabledStatus,
                hwMinMSIBroadcastEnable
                    EnabledStatus,
                hwMinMSIDescription
                    OCTET STRING,
                hwMinMSIStatisticsEnable
                    EnabledStatus,
                hwMinMSIStatisticsReset
                    EnabledStatus,
                hwMinMSIFcsTransparentEnable
                        EnabledStatus, 
                hwMinMSIIngressPriorityValue 
                        Integer32,
                hwMinMSIEgressPriorityTrustBTag 
                        TruthValue, 
                hwMinMSIIngressDeiValue 
                        Integer32,
                hwMinMSIEgressDeiTrustBDei
                        TruthValue,                                  
                hwMinMSIIsolateAll
                        EnabledStatus,                                 
                hwMinMSIRowStatus
                    RowStatus
             }

        hwMinMSIIndex OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the Index of Service Instance.It begins with one."
            ::= { hwMinMSICfgEntry 1 }
        

        hwMinMSIID OBJECT-TYPE
            SYNTAX Integer32 (0..16777216)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the ID of a service instance. It can be any value within 24 bits. 
                By default, the value is null.
                One ID can be configured to one service instance only.
                "
            ::= { hwMinMSICfgEntry 11 }
        

        hwMinMSIName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..31))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the name of a service instance. 
                It is a character string with a maximum of 31 bytes and a minimum of 1 byte. 
                "
            ::= { hwMinMSICfgEntry 12 }
        
        hwMinMSIServiceType OBJECT-TYPE
            SYNTAX HWServiceType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the type of a service instance as follows:
                p2p: indicates the type of a point-to-point service instance.
                mp2mp: indicates the type of a multi-point to multi-point service instance.
                By default, the service type is mp2mp.
                " 
            DEFVAL { 2 }
            ::= { hwMinMSICfgEntry 13 }
        

        hwMinMSIPriorityTrust8021p OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that the priority of user packet based on 802.1Q is trusted. 
                By default, no user priority is trusted.
                " 
            DEFVAL { 2 }
            ::= { hwMinMSICfgEntry 14 }
        

        hwMinMSIPriorityValue OBJECT-TYPE
            SYNTAX Integer32 (0..8)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates priority level of a service instance. 
                 It is used to set the priority field of I-TAG.
                 By default, no user priority is trusted and the priority is zero.
                 The special value of eight is used to indicate that priority of user is trusted.
                "   
            DEFVAL { 0 }
            ::= { hwMinMSICfgEntry 15 }
        

        hwMinMSIInterfaceType OBJECT-TYPE
            SYNTAX HWInterfaceType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the interface type of a service instance.
                transparent: indicates the transparent transmission mode.
                one-to-one: indicates the one-to-one in the s-tagged mode.
                bundling: indicates the bundling in the s-tagged mode.
                By default, the s-tagged bundling mode is used.
                
                "  
            DEFVAL { 3}
            ::= { hwMinMSICfgEntry 16 }
        

        hwMinMSIAdminStatus OBJECT-TYPE
            SYNTAX HWAdminStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the administration status of a service instance."
            ::= { hwMinMSICfgEntry 17 }
        

        hwMinMSIOperStatus OBJECT-TYPE
            SYNTAX HWOperStatus
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the physical status of a service instance."
            ::= { hwMinMSICfgEntry 18 }
        
        hwMinMSIMacLearningEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Disable the MAC learning of a service instance. By default, the MAC learning is enabled.
                This object applies to the service instance of mp2mp only. 
                It is invalid in the service type of p2p.
                "  
            DEFVAL { 1 }
            ::= { hwMinMSICfgEntry 19 }
        
        hwMinMSIMacLimitAction OBJECT-TYPE
            SYNTAX HWProcessBehavior
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates MAC learning restriction of a service instance.
                After the number of MAC address entries reaches the limit, the system takes the following actions:
                discard: indicates that packets with new MAC address are discarded.
                forward: indicates that packets with new MAC address are forwarded, but the address is not added to the MAC address table.
                "
            ::= { hwMinMSICfgEntry 20 }
        

        hwMinMSIMacLimitAlarm OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates MAC learning restriction of a service instance.
                Indicates whether alarm should be sent after the number of MAC address entries reaches the limit as follows:
                disable: indicates no alarm is sent.
                enable: indicates alarm is sent in syslog.
                "
            ::= { hwMinMSICfgEntry 21 }
        

        hwMinMSIMacLimitMaxinum OBJECT-TYPE
            SYNTAX Integer32 (0..131072)
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates MAC learning restriction of a service instance.
                The number of MAC addresses that can be learnt by the current service instance ranges from 0 to 131072.
                When the number is set to zero, no restriction is imposed on the address learning.  
                "
            ::= { hwMinMSICfgEntry 22 }
                

        hwMinMSIL2CtrlProProcess OBJECT-TYPE
            SYNTAX BITS { 
                          all(0),
                          stp(1),
                          lldp(2),
                          lacp(3),
                          dot3ah(4),
                          dot1ag(5)                      
                         }
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "This configuration is unsuitable for the case of port+vlan+cos mapping. Layer 2 control packets of a service instance are handled as follows: By default, all layer 2 control packets are transmitted transparently and all bits are 0; if a bit is 1, it indicates that the packets of this protocol will be discarded.
                 If bit 0 is 1, it indicates that all layer 2 control packets will be discarded.
                 If bit 1 is 1, it indicates that the STP packets will be discarded.
                 If bit 2 is 1, it indicates that the LLAP packets will be discarded.
                 If bit 3 is 1, it indicates that the LACP packets will be discarded.
                 If bit 4 is 1, it indicates that the DOT3AH packets will be discarded.
                 If bit 5 is 1, it indicates that the DOTLAG packets will be discarded.            
                " 
            DEFVAL { {all } }
            ::= { hwMinMSICfgEntry 23}
        

        hwMinMSIUnknownUnicastEnbale OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates how a service instance processes an unknown unicast packet.
                By default, the service instance is allowed to broadcast the unknown unicast packet. 
                This object applies to the mp2mp service instance only.
                " 
            DEFVAL { 1 }
            ::= { hwMinMSICfgEntry 24 }
        

        hwMinMSIMulticastEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates how a service instance processes an unknown multicast packet.
                By default, the service instance is allowed to broadcast the unknown multicast packet. 
                This object applies to the mp2mp service instance only." 
            DEFVAL { 1 }
            ::= { hwMinMSICfgEntry 25 }
        
        hwMinMSIBroadcastEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates how a service instance processes a broadcast packet.
                By default, the service instance is allowed to forward the broadcast packet. 
                This object applies to the mp2mp service instance only.
                 "   
            DEFVAL { 1 }
            ::= { hwMinMSICfgEntry 26 }
        

        hwMinMSIDescription OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..80))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates descriptive information of a service instance. 
                Its length ranges from 1 to 80 bytes and the first byte cannot be a space.
                "
            ::= { hwMinMSICfgEntry 27 }
        

        hwMinMSIStatisticsEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Indicates that the traffic statistics is enabled on a service instance." 
            DEFVAL { 2 }
            ::= { hwMinMSICfgEntry 28 }
        
        hwMinMSIStatisticsReset OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the reset on traffic statistics of a service instance."
            ::= { hwMinMSICfgEntry 29 }
        
        hwMinMSIFcsTransparentEnable OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The forwarded packets need to carry the CRC mark. By default, the forwarded packets do not carry the CRC mark." 
            DEFVAL { 2 }
            ::= { hwMinMSICfgEntry 30 }
            
        hwMinMSIIngressPriorityValue OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the source priority of a service instance.
                 It is used to set the I-TAG priority field when packets are 
                 encapsulated for transmission on the tunnel. 
                 The source priority has eight levels that range from 0 to 7. 
                 By default, the source priority is copied to the mac-tunnel. 
                 When the source priority is set to 8, 
                 it indicates that the service instance is not configured with a priority, 
                 and the priority does not exist." 
            ::= { hwMinMSICfgEntry 31 } 
        hwMinMSIEgressPriorityTrustBTag OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that the destination priority of a service 
                instance is copied from the B-TAG priority. 
                It is used to set whether the I-TAG priority is copied from the B-TAG 
                priority when packets reach the end of the tunnel and are decapsulated. 
                By default, the B-TAG priority is not copied to I-TAG."    
            DEFVAL { 2 }                 
            ::= { hwMinMSICfgEntry 32 } 
        hwMinMSIIngressDeiValue OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the source DEI of a service instance. 
                It is used to set the DEI field when packets are encapsulated for transmission 
                on the tunnel. When DEI is true, the value is set to 1. 
                When DEI is false, the value is set to 0. By default, 
                the S-DEI priority is copied to the mac-tunnel. 
                When the value of the DEI is set to 2, 
                it indicates that no DEI is configured on the tunnel." 
            DEFVAL { 1 }
            ::= { hwMinMSICfgEntry 33 } 
            
        hwMinMSIEgressDeiTrustBDei OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that the destination DEI of a service instance 
                is copied from the B-DEI priority. 
                It is used to set whether the DEI of I-TAG is coped from B-DEI 
                when packets reach the end of the tunnel and are decpasulated. 
                By default, B-DEI is not copied to I-TAG."
            DEFVAL { 2 }                 
            ::= { hwMinMSICfgEntry 34 }  
            
        hwMinMSIIsolateAll     OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that all the mapping users of a service instance are isolated. By default, the isolation is disabled. This object applies to the service instance of mp2mp only. It is invalid in the service type of p2p."
            DEFVAL { 2 }                 
            ::= { hwMinMSICfgEntry 35 }
                    
        hwMinMSIRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the RowStatus. 
                The following three actions are used: active, createAndGo, destroy"
            ::= { hwMinMSICfgEntry 51 }        
                                          
                                          
        hwMinMSIMappingTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMSIMappingEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the configuration table of a service instance user. "
            ::= { hwMinMSIObjects 3 }
        

        hwMinMSIMappingEntry OBJECT-TYPE
            SYNTAX HwMinMSIMappingEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the configuration table entry of a service instance user. "
            INDEX { hwMinMSIIndex, hwMinMSIMappingIfIndex,hwMinMSIMappingVlanPriority,hwMinMSIMappingGlobalVlanID}
            ::= { hwMinMSIMappingTable 1 }
        
        HwMinMSIMappingEntry ::=
            SEQUENCE { 
        
                hwMinMSIMappingIfIndex
                    InterfaceIndexOrZero, 
                hwMinMSIMappingVlanPriority
                    Integer32,
        hwMinMSIMappingGlobalVlanID
                    VlanIdOrNone,
                hwMinMSIMappingVlanListLow
                    VlanList,
                hwMinMSIMappingVlanListHigh
                    VlanList,
        hwMinMSIMappingUserIsolate
          EnabledStatus,    
                hwMinMSIMappingRowStatus
                    RowStatus
             }        

        hwMinMSIMappingIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndexOrZero
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The port that is mapped to the current service instance. If the value is 0, it means that port mapping is not supported."
            ::= { hwMinMSIMappingEntry 1 }
        
        hwMinMSIMappingVlanPriority OBJECT-TYPE
            SYNTAX Integer32 (0..8)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " The priority of a user's packets that are mapped to the current service instance. If the value is 8, it means that the priority mapping is not supported."
            ::= { hwMinMSIMappingEntry 2}    
            
        hwMinMSIMappingGlobalVlanID OBJECT-TYPE
            SYNTAX  VlanIdOrNone
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                " The global VLAN ID that is mapped to the current service instance. If the value is 0, it means that the global VLAN mapping is not supported."
            ::= { hwMinMSIMappingEntry 3 }


        hwMinMSIMappingVlanListLow OBJECT-TYPE
            SYNTAX  VlanList (SIZE(256))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the VLAN ID starting value of a service instance user. "
            ::= { hwMinMSIMappingEntry 11 }
        
        hwMinMSIMappingVlanListHigh OBJECT-TYPE
            SYNTAX  VlanList (SIZE(256))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the VLAN ID ending value of a service instance user. "
            ::= { hwMinMSIMappingEntry 12 }
        
        hwMinMSIMappingUserIsolate     OBJECT-TYPE
            SYNTAX EnabledStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates that the specified mapping user of a service instance is isolated. By default, the isolation is enabled. This object applies to the service instance of mp2mp only. It is invalid in the service type of p2p"
            DEFVAL { 2 }                 
            ::= { hwMinMSIMappingEntry 13 }
                
        hwMinMSIMappingRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the RowStatus. 
                The following three actions are used: active, createAndGo, destroy"
            ::= { hwMinMSIMappingEntry 51 }
        
                                       
        hwMinMSIBindMacTnlTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMSIBindMacTnlEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the mac-tunnel table bound with a service instance."
            ::= { hwMinMSIObjects 4 }
        

        hwMinMSIBindMacTnlEntry OBJECT-TYPE
            SYNTAX HwMinMSIBindMacTnlEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the mac-tunnel table entry bound with a service instance."
            INDEX { hwMinMSIIndex,hwMinMSIBindMacTnlIndex }
            ::= { hwMinMSIBindMacTnlTable 1 }
        
        HwMinMSIBindMacTnlEntry ::=
            SEQUENCE {   
                hwMinMSIBindMacTnlIndex
                      Unsigned32,                    
                hwMinMSIBindMacTnlRowStatus
                    RowStatus
             }
        
        hwMinMSIBindMacTnlIndex OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the index of Mac Tunnel. It begins with one."
            ::= { hwMinMSIBindMacTnlEntry 1 }

        hwMinMSIBindMacTnlRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the RowStatus. 
                The following three actions are used: active, createAndGo, destroy"
            ::= { hwMinMSIBindMacTnlEntry 51 }
        

        hwMinMSIStatisticsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMSIStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the hwMinMSIStatisticsTable of a service instance. "
            ::= { hwMinMSIObjects 5 }
        

        hwMinMSIStatisticsEntry OBJECT-TYPE
            SYNTAX HwMinMSIStatisticsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the hwMinMSIStatisticsTable entry of a service instance."
            INDEX { hwMinMSIIndex }
            ::= { hwMinMSIStatisticsTable 1 }
        
        HwMinMSIStatisticsEntry ::=
            SEQUENCE { 
                hwMinMSIInPackets
                    Counter64,
                hwMinMSIInBytes
                    Counter64,
                hwMinMSIOutPackets
                    Counter64,
                hwMinMSIOutBytes
                    Counter64
             }


        hwMinMSIInPackets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of packets received by a user of the current service instance."
            ::= { hwMinMSIStatisticsEntry 11 }
        

        hwMinMSIInBytes OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of bytes received by a user of the current service instance."
            ::= { hwMinMSIStatisticsEntry 12 }
        

        hwMinMSIOutPackets OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of packets sent by a user of the current service instance."
            ::= { hwMinMSIStatisticsEntry 13 }
        

        hwMinMSIOutBytes OBJECT-TYPE
            SYNTAX Counter64
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the number of bytes sent by a user of the current service instance."
            ::= { hwMinMSIStatisticsEntry 14 }
        

        hwMinMSIStaticMacFwdTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwMinMSIStaticMacFwdEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the static MAC forwarding table of a service instance:"
            ::= { hwMinMSIObjects 6 }
        
        hwMinMSIStaticMacFwdEntry OBJECT-TYPE
            SYNTAX HwMinMSIStaticMacFwdEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the static MAC forwarding table entry of a service instance:"
            INDEX { hwMinMSIIndex, hwMinMSIStaticMacFwdCDMac }
            ::= { hwMinMSIStaticMacFwdTable 1 }
        
        HwMinMSIStaticMacFwdEntry ::=
            SEQUENCE { 
                hwMinMSIStaticMacFwdCDMac
                    MacAddress,
                hwMinMSIStaticMacFwdMacTnlName
                    OCTET STRING,
                hwMinMSIStaticMacFwdOutgoingIfIndex
                    InterfaceIndexOrZero,
                hwMinMSIStaticMacFwdVlanID
                    VlanIdOrNone,
                hwMinMSIStaticMacFwdType
                    HWStaticMacFwdType,
                hwMinMSIStaticMacFwdRowStatus
                    RowStatus
             }

        hwMinMSIStaticMacFwdCDMac OBJECT-TYPE
            SYNTAX MacAddress
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the destination MAC address of a customer."
            ::= { hwMinMSIStaticMacFwdEntry 1 }
        

        hwMinMSIStaticMacFwdMacTnlName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..31))
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " Indicates the name of the mac-tunnel. 
                It is a character string with a maximum of 31 bytes and a minimum of 1 byte. "
            ::= { hwMinMSIStaticMacFwdEntry 11 }
        
        hwMinMSIStaticMacFwdOutgoingIfIndex OBJECT-TYPE
            SYNTAX InterfaceIndexOrZero 
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates a outbound port.
                 The value zero is used to indicate that interface was unknown or none."
            ::= { hwMinMSIStaticMacFwdEntry 12 }
        

        hwMinMSIStaticMacFwdVlanID OBJECT-TYPE
            SYNTAX VlanIdOrNone
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the downstream vlanid.
                 The special value of zero is used to indicate that no VLAN-ID is present or used. "
            ::= { hwMinMSIStaticMacFwdEntry 13 }
        

        hwMinMSIStaticMacFwdType OBJECT-TYPE
            SYNTAX HWStaticMacFwdType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                " Indicates the type of the static MAC forwarding table of a service instance:
                static(1): indicates a static entry.
                blackhole(2): indicates a blackhole entry. "
            ::= { hwMinMSIStaticMacFwdEntry 14 }
        

        hwMinMSIStaticMacFwdRowStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Indicates the RowStatus. 
                The following three actions are used: active, createAndGo, destroy."
            ::= { hwMinMSIStaticMacFwdEntry 51 }
        

        hwSINameToIndexMappingTable OBJECT-TYPE
            SYNTAX SEQUENCE OF HwSINameToIndexMappingEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the name and the index mapping table of a service instance."
            ::= { hwMinMSIObjects 7  }
        
        hwSINameToIndexMappingEntry OBJECT-TYPE
            SYNTAX HwSINameToIndexMappingEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the name and the index mapping table entry of a service instance."
            INDEX { hwSIName }
            ::= { hwSINameToIndexMappingTable 1 }
        
        HwSINameToIndexMappingEntry ::=
            SEQUENCE { 
                hwSIName
                    OCTET STRING,
                hwSIIndex
                    Unsigned32
             }

        hwSIName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE (1..31))
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Indicates the name of a service instance. 
                It is a character string with a maximum of 31 bytes and a minimum of 1 byte.
                "
            ::= { hwSINameToIndexMappingEntry 1 }
        

        hwSIIndex OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Indicates the index of a service instance.It begins with one."
            ::= { hwSINameToIndexMappingEntry 11 }
        
        hwMinMNotifications OBJECT IDENTIFIER ::= { hwMinMMIB 2 }
        
--  definition of MINM-related trAps

        hwMinMMacTnlUp NOTIFICATION-TYPE
            OBJECTS { hwMinMMacTnlName, hwMinMMacTnlDMac, hwMinMMacTnlBVlanID, hwMinMMacTnlAdminStatus, hwMinMMacTnlOperStatus
                 }
            STATUS current
            DESCRIPTION 
                "Indicates the Up alarm of the mac-tunnel. 
                This object forms a pair with the following hwMinMMacTnlDown.
                "
            ::= { hwMinMNotifications 1 }
        
        hwMinMMacTnlDown NOTIFICATION-TYPE
            OBJECTS { hwMinMMacTnlName, hwMinMMacTnlDMac, hwMinMMacTnlBVlanID, hwMinMMacTnlAdminStatus, hwMinMMacTnlOperStatus
                 }
            STATUS current
            DESCRIPTION 
                "Indicates the Down alarm of the mac-tunnel. 
                This object forms a pair with the previous hwMinMMacTnlUp.
                "
            ::= { hwMinMNotifications 2 }
        

        hwMinMSIUp NOTIFICATION-TYPE
            OBJECTS { hwMinMSIID, hwMinMSIName, hwMinMSIAdminStatus, hwMinMSIOperStatus }
            STATUS current
            DESCRIPTION 
                "Indicates the Up alarm of a service instance. 
                This object forms a pair with the following hwMinMSIDown.
                "
            ::= { hwMinMNotifications 3 }
        

        hwMinMSIDown NOTIFICATION-TYPE
            OBJECTS { hwMinMSIID, hwMinMSIName, hwMinMSIAdminStatus, hwMinMSIOperStatus }
            STATUS current
            DESCRIPTION 
                "Indicates the Down alarm of a service instance. 
                This object forms a pair with the previous hwMinMSIUp.
                "
            ::= { hwMinMNotifications 4 }
        
        hwMinMMacTnlCCFaultAlarm NOTIFICATION-TYPE
            OBJECTS { hwMinMMacTnlName, hwMinMMacTnlDMac, hwMinMMacTnlBVlanID, hwMinMMacTnlSomeRMepCcmDefect, hwMinMMacTnlSomeRdiDefect
                 }
            STATUS current
            DESCRIPTION 
                "Indicates an alarm on the connectivity of fault.
                "
            ::= { hwMinMNotifications 5 }
        
        hwMinMMacTnlSwitch NOTIFICATION-TYPE
            OBJECTS { hwMinMMacTnlName, hwMinMMacTnlDMac, hwMinMMacTnlBVlanID, hwMinMProtectMacTnlName, hwMinMProtectMacTnlDMac, 
                hwMinMProtectMacTnlBVlanID, hwMinMProtectSwitchOperation }
            STATUS current
            DESCRIPTION 
                "Indicates the Aps protection group switchover alarm of the mac-tunnel. 
                This object forms a pair with the following hwMinMMacTnlRevertive.
                hwMinMMacTnlName: indicates the name of the primary tunnel.
                hwMinMMacTnlDMac: indicates the destination MAC address of the primary tunnel.
                hwMinMMacTnlBVlanID: indicates the BVLANID of the primary tunnel.
                hwMinMProtectMacTnlName: Indicates the name of the backup tunnel.
                hwMinMProtectSwitchOperation: Indicates the switchover commands for the Aps protection group of mac-tunnels.
                hwMinMProtectMacTnlBVlanID: indicates the BVLANID of the backup tunnel.
                hwMinMProtectMacTnlDMac:indicates the destination MAC address of the backup tunnel.
                "
            ::= { hwMinMNotifications 6 }
        
        hwMinMMacTnlRevertive NOTIFICATION-TYPE
            OBJECTS { hwMinMMacTnlName, hwMinMMacTnlDMac, hwMinMMacTnlBVlanID, hwMinMProtectMacTnlName, hwMinMProtectMacTnlDMac, 
                hwMinMProtectMacTnlBVlanID, hwMinMProtectSwitchOperation }
            STATUS current
            DESCRIPTION 
                " 
                Indicates the Aps protection group switchover alarm of the mac-tunnel. 
                This object forms a pair with the previous hwMinMMacTnlSwitch.
                hwMinMMacTnlName: indicates the name of the primary tunnel.
                hwMinMMacTnlDMac: indicates the destination MAC address of the primary tunnel.
                hwMinMMacTnlBVlanID: indicates the BVLANID of the primary tunnel.
                hwMinMProtectMacTnlName: Indicates the name of the backup tunnel.
                hwMinMProtectSwitchOperation: Indicates the switchover commands for the Aps protection group of mac-tunnels.
                   hwMinMProtectMacTnlBVlanID: indicates the BVLANID of the backup tunnel.
                hwMinMProtectMacTnlDMac:indicates the destination MAC address of the backup tunnel."
            ::= { hwMinMNotifications 7 }
        

        hwMinMSIMacLimitNumRaisingThreshold NOTIFICATION-TYPE
            OBJECTS { hwMinMSIName, hwMinMSIID, hwMinMSIMacLimitMaxinum }
            STATUS current
            DESCRIPTION 
                "Indicates the  alarm of the mac limiting number beyond the Threshold."
            ::= { hwMinMNotifications 8 }
        
--   -------------------------------------------------------------
-- HUAWEI-MINM-MIB - Conformance Information
-- -------------------------------------------------------------

        hwMinMConformance OBJECT IDENTIFIER ::= { hwMinMMIB 3 }
        

        hwMinMGroups OBJECT IDENTIFIER ::= { hwMinMConformance 1 }
        
--   -------------------------------------------------------------
-- units of conformance
-- -------------------------------------------------------------
        hwMinMSystemGroup OBJECT-GROUP
            OBJECTS { hwMinMVirtualMac, hwMinMMacTnlBVlanListLow, hwMinMMacTnlBVlanListHigh,hwMinMTrapEnable }
            STATUS current
            DESCRIPTION 
                "A collection of objects providing the System configuration of  the MAC-in-MAC
                capability."
            ::= { hwMinMGroups 1 }
        
        hwMinMMacTnlCfgGroup OBJECT-GROUP
            OBJECTS { hwMinMVirtualMac, hwMinMMacTnlBVlanListLow, hwMinMMacTnlBVlanListHigh, hwMinMMacTnlIndexNext, hwMinMMacTnlName, 
                hwMinMMacTnlDMac, hwMinMMacTnlBVlanID, hwMinMMacTnlBVlanType, hwMinMMacTnlPriorityValue, hwMinMMacTnlOutgoingIfIndex, 
                hwMinMMacTnlSplitHorizonEnable, hwMinMMacTnlAdminStatus, hwMinMMacTnlOperStatus, hwMinMMacTnlDescription, hwMinMMacTnlRowStatus, 
                hwMacTnlIndex,hwMinMMacTnlStatisticsReset,hwMinMMacTnlPriorityTrustITag,hwMinMMacTnlDeiTrustIDei,hwMinMMacTnlDeiValue }
            STATUS current
            DESCRIPTION 
                "A collection of objects providing the configuration of  the MAC TUNNEL
                capability."
            ::= { hwMinMGroups 2 }
        
        hwMinMMacTnlStatisticsGroup OBJECT-GROUP
            OBJECTS { hwMinMMacTnlInPackets, hwMinMMacTnlInBytes, hwMinMMacTnlOutPackets, hwMinMMacTnlOutBytes
                 }
            STATUS current
            DESCRIPTION 
                "A collection of objects providing the Statistics of the Service Instance
                capability."
            ::= { hwMinMGroups 3 }
        

        hwMinMMacTnlOAMGroup OBJECT-GROUP
            OBJECTS { hwMinMMacTnlCfmEnable, hwMinMMacTnlCCInterval, hwMinMMacTnlSomeRMepCcmDefect, hwMinMMacTnlSomeRdiDefect, hwMinMMacTnlCcReceiveEnabled,hwMinMMacTnlCCRowStatus, 
                hwMinMMacTnlLbmEnable, hwMinMMacTnlLbmTimeStamp, hwMinMMacTnlLbmTimeOut, hwMinMMacTnlLbmTimes,
                hwMinMMacTnlLbmSize, hwMinMMacTnlLbrIn, hwMinMMacTnlMacPingRTTMin,hwMinMMacTnlMacPingRTTMax,hwMinMMacTnlMacPingRTTAvg,
                hwMinMMacTnlMacPingPacketLossRatio, hwMinMMacTnlLbmResult,hwMinMMacTnlLbRowStatus, hwMinMMacTnlLtmEnable, hwMinMMacTnlLtmTimeStamp, 
                hwMinMMacTnlLtmTimeOut, hwMinMMacTnlLtmTtl, hwMinMMacTnlLtmFlags,hwMinMMacTnlLtmSeqNumber,hwMinMMacTnlLtmEgressIdentifier,hwMinMMacTnlLtmResult, hwMinMMacTnlLtmRowStatus, 
                hwMinMMacTnlLtrTtl,hwMinMMacTnlLtrForwarded,hwMinMMacTnlLtrLastEgressIdentifier,
                hwMinMMacTnlLtrNextEgressIdentifier,hwMinMMacTnlLtrRelay,hwMinMMacTnlLtrIngress,hwMinMMacTnlLtrIngressMac,
                  hwMinMMacTnlLtrIngressPortIdSubtype,hwMinMMacTnlLtrIngressPortId,hwMinMMacTnlLtrEgress,hwMinMMacTnlLtrEgressMac,hwMinMMacTnlLtrEgressPortIdSubtype,
                   hwMinMMacTnlLtrEgressPortId
                    }
            STATUS current
            DESCRIPTION 
                "A collection of objects providing the OAM of the MAC TUNNEL
                capability."
            ::= { hwMinMGroups 4 }
        

        hwMinMMacTnlApsGroup OBJECT-GROUP
            OBJECTS { hwMinMProtectMacTnlName, hwMinMProtectApsSwitchMode, hwMinMProtectProtocolApsEnable, 
                hwMinMProtectApsFastInterval, hwMinMProtectHoldoffTime, hwMinMProtectRevMode, hwMinMProtectRevWtrTime, hwMinMProtectSwitchOperation, 
                hwMinMProtectProtocol,hwMinMProtectMacTnlDMac, hwMinMProtectMacTnlBVlanID, hwMinMProtectRowStatus,hwMinMProtectMacTnlIndex }
            STATUS current
            DESCRIPTION 
                "A collection of objects providing the Aps of the MAC TUNNEL
                capability."
            ::= { hwMinMGroups 5 }
        

        hwMinMSICfgGroup OBJECT-GROUP
            OBJECTS { hwMinMSIIndexNext, hwMinMSIID, hwMinMSIName, hwMinMSIServiceType, hwMinMSIPriorityTrust8021p, 
                hwMinMSIPriorityValue, hwMinMSIInterfaceType, hwMinMSIAdminStatus, hwMinMSIOperStatus, hwMinMSIMacLearningEnable, 
                hwMinMSIMacLimitAction, hwMinMSIMacLimitAlarm, hwMinMSIMacLimitMaxinum,  hwMinMSIL2CtrlProProcess, 
                hwMinMSIUnknownUnicastEnbale, hwMinMSIMulticastEnable, hwMinMSIBroadcastEnable, hwMinMSIDescription, hwMinMSIRowStatus, 
                hwMinMSIStaticMacFwdOutgoingIfIndex, hwMinMSIStaticMacFwdVlanID, hwMinMSIStaticMacFwdType,hwMinMSIFcsTransparentEnable, 
                hwMinMSIStaticMacFwdRowStatus, hwSIIndex, hwMinMSIBindMacTnlRowStatus, hwMinMSIStaticMacFwdMacTnlName,hwMinMSIMappingVlanListLow,hwMinMSIMappingVlanListHigh,
                hwMinMSIMappingUserIsolate, hwMinMSIIngressPriorityValue,hwMinMSIEgressPriorityTrustBTag,hwMinMSIIngressDeiValue,hwMinMSIEgressDeiTrustBDei,hwMinMSIIsolateAll,hwMinMSIMappingRowStatus }
            STATUS current
            DESCRIPTION 
                "A collection of objects providing the configuration of Service Instance
                capability."
            ::= { hwMinMGroups 6 }
        

        hwMinMSIStatisticsGroup OBJECT-GROUP
            OBJECTS { hwMinMSIInPackets, hwMinMSIInBytes, hwMinMSIOutPackets, hwMinMSIOutBytes, hwMinMSIStatisticsReset, 
                hwMinMSIStatisticsEnable }
            STATUS current
            DESCRIPTION 
                "A collection of objects providing the Statistics of the Service Instance
                capability."
            ::= { hwMinMGroups 7 }       

        
        hwMinMNotificationGroup NOTIFICATION-GROUP
            NOTIFICATIONS { hwMinMMacTnlUp, hwMinMMacTnlDown, hwMinMSIUp, hwMinMSIDown, hwMinMMacTnlCCFaultAlarm, 
                hwMinMMacTnlSwitch, hwMinMMacTnlRevertive, hwMinMSIMacLimitNumRaisingThreshold }
            STATUS current
            DESCRIPTION 
                "Collection of notification objects."
            ::= { hwMinMGroups 8 }
        
                    
        hwMinMCompliances MODULE-COMPLIANCE
            STATUS current
            DESCRIPTION 
                "The compliance statement for entities implementing
                the Huawei MINM MIB"
            MODULE -- this module
                MANDATORY-GROUPS { hwMinMSystemGroup, hwMinMMacTnlCfgGroup, hwMinMMacTnlOAMGroup, hwMinMMacTnlApsGroup, hwMinMSICfgGroup, 
                    hwMinMMacTnlStatisticsGroup, hwMinMSIStatisticsGroup, hwMinMNotificationGroup }
            ::= { hwMinMConformance 2 }
        
    
    END

--
-- HUAWEI-MINM-MIB.mib
--
