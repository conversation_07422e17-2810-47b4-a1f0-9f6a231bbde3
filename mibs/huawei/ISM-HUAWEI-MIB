--
-- ISM-HUAWEI-MIB.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 4.0 Build 347
-- Thursday, March 27, 2014 at 11:36:30
--

	ISM-HUAWEI-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			OBJECT-GROUP, MODULE-COMP<PERSON>IANC<PERSON>, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			enterprises, IpAddress, Integer32, Gauge32, 
			Counter64, OBJECT-TYPE, MODULE-IDENTITY, OBJECT-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			DisplayString, DateAndTime, RowStatus, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
		hwISMCommon MODULE-IDENTITY 
			LAST-UPDATED "201303061010Z"		-- March 06, 2013 at 10:10 GMT
			ORGANIZATION 
				"Huawei Technologies Co.,Ltd."
			CONTACT-INFO 
				"Huawei Industrial Base
				Bantian, <PERSON><PERSON><PERSON> 518129
				People's Republic of China
				Website: http://www.huawei.com
				Email: <EMAIL>"
			DESCRIPTION 
				"Description."
			REVISION "200809171629Z"		-- September 17, 2008 at 16:29 GMT
			DESCRIPTION 
				"V1R1
				V1R2"
			::= { products 91 }

		
	
--
-- Textual conventions
--
	
		NodeCodeString ::= TEXTUAL-CONVENTION
			DISPLAY-HINT 
				"255a"
			STATUS current
			DESCRIPTION 
				"characters in length."
			SYNTAX OCTET STRING (SIZE (15..17))

	
--
-- Node definitions
--
	
		huawei OBJECT IDENTIFIER ::= { enterprises 2011 }

		
		products OBJECT IDENTIFIER ::= { huawei 2 }

		
		hwIsmTopo OBJECT IDENTIFIER ::= { hwISMCommon 9 }

		
		hwIsmAccessNodeInfo OBJECT IDENTIFIER ::= { hwIsmTopo 1 }

		
		hwIsmAccessNodeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwIsmAccessNodeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Access Node Information Table"
			::= { hwIsmAccessNodeInfo 1 }

		
		hwIsmAccessNodeEntry OBJECT-TYPE
			SYNTAX HwIsmAccessNodeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entry of Access Node Information Table
				
				inex:
				1. hwIsmNENodeCode  
				"
			INDEX { hwIsmNENodeCode }
			::= { hwIsmAccessNodeTable 1 }

		
		HwIsmAccessNodeEntry ::=
			SEQUENCE { 
				hwIsmNENodeCode
					NodeCodeString,
				hwIsmNENodeType
					Integer32,
				hwIsmNENodeWorkingMode
					INTEGER,
				hwIsmNENodeIPAddress
					IpAddress,
				hwIsmNENodeContextName
					DisplayString,
				hwIsmNENodeContextEngineID
					DisplayString,
				hwIsmNENodeClusterName
					DisplayString,
				hwIsmNENodeRunningStatus
					INTEGER
			 }

		hwIsmNENodeCode OBJECT-TYPE
			SYNTAX NodeCodeString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Node Code"
			::= { hwIsmAccessNodeEntry 1 }

		
		hwIsmNENodeType OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Node Type"
			::= { hwIsmAccessNodeEntry 2 }

		
		hwIsmNENodeWorkingMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				mode1(1),
				mode2(2),
				mode3(3),
				mode4(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Working Mode"
			::= { hwIsmAccessNodeEntry 3 }

		
		hwIsmNENodeIPAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Node IP Address"
			::= { hwIsmAccessNodeEntry 4 }

		
		hwIsmNENodeContextName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Node Context Name
				"
			::= { hwIsmAccessNodeEntry 5 }

		
		hwIsmNENodeContextEngineID OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Node Context EngineID"
			::= { hwIsmAccessNodeEntry 6 }

		
		hwIsmNENodeClusterName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Cluster Name"
			::= { hwIsmAccessNodeEntry 7 }

		
		hwIsmNENodeRunningStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				admin(1),
				freedom(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"NE Running Status"
			::= { hwIsmAccessNodeEntry 8 }

		
		hwIsmNotification OBJECT IDENTIFIER ::= { hwISMCommon 10 }

		
		hwIsmActiveAlarmInfo OBJECT IDENTIFIER ::= { hwIsmNotification 1 }

		
		hwIsmActiveAlarmInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwIsmActiveAlarmInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ActiveAlarm Information Table
				"
			::= { hwIsmActiveAlarmInfo 1 }

		
		hwIsmActiveAlarmInfoEntry OBJECT-TYPE
			SYNTAX HwIsmActiveAlarmInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Entry of Active Alarm Information Table
				"
			INDEX { hwIsmActiveAlarmInfoNodeCode, hwIsmActiveAlarmInfoSerialNo }
			::= { hwIsmActiveAlarmInfoTable 1 }

		
		HwIsmActiveAlarmInfoEntry ::=
			SEQUENCE { 
				hwIsmActiveAlarmInfoNodeCode
					NodeCodeString,
				hwIsmActiveAlarmInfoLocationInfo
					DisplayString,
				hwIsmActiveAlarmInfoRestoreAdvice
					DisplayString,
				hwIsmActiveAlarmInfoTitle
					DisplayString,
				hwIsmActiveAlarmInfoType
					INTEGER,
				hwIsmActiveAlarmInfoLevel
					INTEGER,
				hwIsmActiveAlarmInfoAlarmID
					Gauge32,
				hwIsmActiveAlarmInfoOccurTime
					DateAndTime,
				hwIsmActiveAlarmInfoSerialNo
					Gauge32,
				hwIsmActiveAlarmInfoAddtionInfo
					OCTET STRING,
				hwIsmActiveAlarmInfoCategory
					INTEGER,
				hwIsmActiveAlarmInfoLocalAlarmID
					Counter64
			 }

		hwIsmActiveAlarmInfoNodeCode OBJECT-TYPE
			SYNTAX NodeCodeString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm NodeCode"
			::= { hwIsmActiveAlarmInfoEntry 1 }

		
		hwIsmActiveAlarmInfoLocationInfo OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Location Information"
			::= { hwIsmActiveAlarmInfoEntry 2 }

		
		hwIsmActiveAlarmInfoRestoreAdvice OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Restore Advice"
			::= { hwIsmActiveAlarmInfoEntry 3 }

		
		hwIsmActiveAlarmInfoTitle OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Title"
			::= { hwIsmActiveAlarmInfoEntry 4 }

		
		hwIsmActiveAlarmInfoType OBJECT-TYPE
			SYNTAX INTEGER { equipmentFault(2) }
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Type"
			::= { hwIsmActiveAlarmInfoEntry 5 }

		
		hwIsmActiveAlarmInfoLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				criticalAlarm(1),
				majorAlarm(2),
				minorAlarm(3),
				warningAlarm(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Level
				
				
				1-criticalAlarm 
				2-majorAlarm 
				3-minorAlarm 
				4-warningAlarm 
				
				"
			::= { hwIsmActiveAlarmInfoEntry 6 }

		
		hwIsmActiveAlarmInfoAlarmID OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm ID"
			::= { hwIsmActiveAlarmInfoEntry 7 }

		
		hwIsmActiveAlarmInfoOccurTime OBJECT-TYPE
			SYNTAX DateAndTime
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Occurrence Time of Alarm"
			::= { hwIsmActiveAlarmInfoEntry 8 }

		
		hwIsmActiveAlarmInfoSerialNo OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Serial No."
			::= { hwIsmActiveAlarmInfoEntry 9 }

		
		hwIsmActiveAlarmInfoAddtionInfo OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Addtion Information"
			::= { hwIsmActiveAlarmInfoEntry 10 }

		
		hwIsmActiveAlarmInfoCategory OBJECT-TYPE
			SYNTAX INTEGER
				{
				faultAlarm(1),
				resumeAlarm(2),
				eventAlarm(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active Alarm Category
				
				
				1-faultAlarm  
				2-resumeAlarm 
				3-eventAlarm  "
			::= { hwIsmActiveAlarmInfoEntry 11 }

		
		hwIsmActiveAlarmInfoLocalAlarmID OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Active original Alarm ID"
			::= { hwIsmActiveAlarmInfoEntry 12 }

		
		hwIsmClearedAlarmConfirm OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Confirm active alarm's status"
			::= { hwIsmActiveAlarmInfo 2 }

		
		hwIsmNotificationType OBJECT IDENTIFIER ::= { hwIsmNotification 2 }

		
		hwinfoFaultNotificationType OBJECT IDENTIFIER ::= { hwIsmNotificationType 1 }

		
		hwIsmFaultNotificationTypeV2 OBJECT-IDENTITY
			STATUS current
			DESCRIPTION 
				"Root node for the converted TRAP-TYPEs."
			::= { hwinfoFaultNotificationType 0 }

		
		hwIsmAlarmReporting NOTIFICATION-TYPE
			OBJECTS { hwIsmReportingAlarmNodeCode, hwIsmReportingAlarmLocationInfo, hwIsmReportingAlarmRestoreAdvice, hwIsmReportingAlarmFaultTitle, hwIsmReportingAlarmFaultType, 
				hwIsmReportingAlarmFaultLevel, hwIsmReportingAlarmAlarmID, hwIsmReportingAlarmFaultTime, hwIsmReportingAlarmSerialNo, hwIsmReportingAlarmLocationAlarmID, 
				hwIsmReportingAlarmFaultCategory, hwIsmReportingAlarmAdditionInfo }
			STATUS current
			DESCRIPTION 
				"Alarm Reporting"
			::= { hwIsmFaultNotificationTypeV2 1 }

		
		hwIsmTrapNotification OBJECT IDENTIFIER ::= { hwIsmNotification 3 }

		
-- .3.1
		hwIsmFaultNotification OBJECT IDENTIFIER ::= { hwIsmTrapNotification 1 }

		
		hwIsmReportingAlarm OBJECT IDENTIFIER ::= { hwIsmFaultNotification 1 }

		
		hwIsmReportingAlarmNodeCode OBJECT-TYPE
			SYNTAX NodeCodeString
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Alarm Node Code"
			REFERENCE
				"Location info of  Fault Alarm"
			::= { hwIsmReportingAlarm 1 }

		
		hwIsmReportingAlarmLocationInfo OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Reporting Alarm Location Information"
			::= { hwIsmReportingAlarm 2 }

		
		hwIsmReportingAlarmRestoreAdvice OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..256))
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Advice of Restore Reporting Alarm "
			::= { hwIsmReportingAlarm 3 }

		
		hwIsmReportingAlarmFaultTitle OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..256))
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Fault Alarm Title "
			::= { hwIsmReportingAlarm 4 }

		
		hwIsmReportingAlarmFaultType OBJECT-TYPE
			SYNTAX INTEGER
				{
				communicationQuality(1),
				equipmentFault(2),
				processError(3),
				serviceQuality(4),
				environmentFault(5),
				performanceLimit(6)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Fault Alarm  Type"
			::= { hwIsmReportingAlarm 5 }

		
		hwIsmReportingAlarmFaultLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				criticalAlarm(1),
				majorAlarm(2),
				minorAlarm(3),
				warningAlarm(4)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Level of Fault Alarm
				
				
				CriticalAlarm(1)      
				MajorAlarm(2)         
				MinorAlarm(3)         
				WarningAlarm(4)       
				"
			::= { hwIsmReportingAlarm 6 }

		
		hwIsmReportingAlarmAlarmID OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Fault Alarm ID"
			::= { hwIsmReportingAlarm 7 }

		
		hwIsmReportingAlarmFaultTime OBJECT-TYPE
			SYNTAX DateAndTime
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Occurrence Time of Fault Alarm"
			::= { hwIsmReportingAlarm 8 }

		
		hwIsmReportingAlarmSerialNo OBJECT-TYPE
			SYNTAX Gauge32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Fault Alarm Serial No."
			::= { hwIsmReportingAlarm 9 }

		
		hwIsmReportingAlarmAdditionInfo OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Addition Info of Fault Alarm"
			::= { hwIsmReportingAlarm 10 }

		
		hwIsmReportingAlarmFaultCategory OBJECT-TYPE
			SYNTAX INTEGER
				{
				faultAlarm(1),
				resumeAlarm(2),
				eventAlarm(3)
				}
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Category of Fault Alarm
				
				
				faultAlarm (1):
				resumeAlarm (2):
				eventAlarm (3):
				"
			::= { hwIsmReportingAlarm 11 }

		
		hwIsmReportingAlarmLocationAlarmID OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwIsmReportingAlarm 12 }

		
		hwIsmTrapForwardControl OBJECT IDENTIFIER ::= { hwIsmNotification 4 }

		
		hwIsmTrapTargetAddrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwIsmTrapTargetAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { hwIsmTrapForwardControl 1 }

		
		hwIsmTrapTargetAddrEntry OBJECT-TYPE
			SYNTAX HwIsmTrapTargetAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { hwIsmTrapTargetAddrIndex }
			::= { hwIsmTrapTargetAddrTable 1 }

		
		HwIsmTrapTargetAddrEntry ::=
			SEQUENCE { 
				hwIsmTrapTargetAddrIPAddr
					IpAddress,
				hwIsmTrapTargetAddrPort
					Integer32,
				hwIsmTrapTargetAddrRowStatus
					RowStatus,
				hwIsmTrapTargetAddrIndex
					OCTET STRING,
				hwIsmTrapTargetAddrTrapVer
					Integer32,
				hwIsmTrapTargetAddrIPAddrNew
					OCTET STRING,
				hwIsmTrapTargetAddrTrapType
					Integer32
			 }

		hwIsmTrapTargetAddrIPAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"IP Address of Trap Target "
			::= { hwIsmTrapTargetAddrEntry 1 }

		
		hwIsmTrapTargetAddrPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Port of Trap Target "
			::= { hwIsmTrapTargetAddrEntry 2 }

		
		hwIsmTrapTargetAddrRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"RowStatus"
			::= { hwIsmTrapTargetAddrEntry 3 }

		
		hwIsmTrapTargetAddrIndex OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Trap Version of Trap Target "
			::= { hwIsmTrapTargetAddrEntry 4 }

		
		hwIsmTrapTargetAddrTrapVer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Index of Trap Target "
			::= { hwIsmTrapTargetAddrEntry 5 }

		
		hwIsmTrapTargetAddrIPAddrNew OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"New IP Address of Trap Target "
			::= { hwIsmTrapTargetAddrEntry 6 }

		
		hwIsmTrapTargetAddrTrapType OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				" "
			::= { hwIsmTrapTargetAddrEntry 7 }

		
		isoConformance OBJECT IDENTIFIER ::= { iso 6 }

		
		isoGroups OBJECT IDENTIFIER ::= { isoConformance 1 }

		
		currentObjectGroup OBJECT-GROUP
			OBJECTS { hwIsmActiveAlarmInfoNodeCode, hwIsmActiveAlarmInfoLocationInfo, hwIsmActiveAlarmInfoRestoreAdvice, hwIsmActiveAlarmInfoTitle, hwIsmActiveAlarmInfoType, 
				hwIsmActiveAlarmInfoLevel, hwIsmActiveAlarmInfoAlarmID, hwIsmActiveAlarmInfoOccurTime, hwIsmActiveAlarmInfoSerialNo, hwIsmActiveAlarmInfoCategory, 
				hwIsmReportingAlarmNodeCode, hwIsmReportingAlarmLocationInfo, hwIsmReportingAlarmRestoreAdvice, hwIsmReportingAlarmFaultTitle, hwIsmReportingAlarmFaultType, 
				hwIsmReportingAlarmFaultLevel, hwIsmReportingAlarmAlarmID, hwIsmReportingAlarmFaultTime, hwIsmReportingAlarmSerialNo, hwIsmReportingAlarmFaultCategory, 
				hwIsmReportingAlarmAdditionInfo, hwIsmNENodeCode, hwIsmNENodeType, hwIsmNENodeIPAddress, hwIsmNENodeContextName, 
				hwIsmNENodeContextEngineID, hwIsmClearedAlarmConfirm, hwIsmActiveAlarmInfoAddtionInfo, hwIsmTrapTargetAddrIPAddr, hwIsmTrapTargetAddrPort, 
				hwIsmTrapTargetAddrRowStatus, hwIsmReportingAlarmLocationAlarmID, hwIsmActiveAlarmInfoLocalAlarmID, hwIsmTrapTargetAddrIndex, hwIsmTrapTargetAddrTrapVer, 
				hwIsmTrapTargetAddrIPAddrNew, hwIsmTrapTargetAddrTrapType, hwIsmNENodeRunningStatus, hwIsmNENodeWorkingMode, hwIsmNENodeClusterName
				 }
			STATUS current
			DESCRIPTION 
				"Enter the description of the created OBJECT-GROUP."
			::= { isoGroups 1 }

		
		currentNotificationGroup NOTIFICATION-GROUP
			NOTIFICATIONS { hwIsmAlarmReporting }
			STATUS current
			DESCRIPTION 
				"Enter the description of the created NOTIFICATION-GROUP."
			::= { isoGroups 2 }

		
		isoCompliances OBJECT IDENTIFIER ::= { isoConformance 2 }

		
		basicCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"Enter the description of the created MODULE-COMPLIANCE."
			MODULE -- this module
				MANDATORY-GROUPS { currentObjectGroup, currentNotificationGroup }
			::= { isoCompliances 1 }

		
	
	END

--
-- ISM-HUAWEI-MIB.mib
--
