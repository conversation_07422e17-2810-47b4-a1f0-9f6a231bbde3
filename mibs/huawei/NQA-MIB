-- =================================================================
-- Copyright (C) 2006 by  HUAWEI TECHNOLOGIES. All rights reserved
-- 
-- Description: Network Quality Analyse MIB. 
-- Reference:
-- Version:     V1.1
-- History:
--              V1.0 liyonggang 2006.6.7,publish 
--              V1.1         
--                  modified by liyonggang
-- =================================================================

    NQA-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            IpAddress
                FROM RFC1155-SMI     
            InterfaceIndexOrZero                 -- RFC2863
            FROM IF-MIB
            hwDatacomm            
                FROM HUAWEI-MIB            
            InetAddressType, InetAddress, InetPortNumber            
                FROM INET-ADDRESS-MIB            
            OBJECT-GROUP, MODULE-<PERSON>MPL<PERSON>NCE            
                FROM SNMPv2-CONF            
            TimeTicks, Integer32, Gauge32, Counter32, Unsigned32, OBJECT-TYPE,mib-2,  NOTIFICATION-TYPE, NOTIFICATION-GROUP,
            MODULE-IDENTITY            
                FROM SNMPv2-SMI            
            DisplayString, TruthValue, TimeStamp, RowStatus, TimeInterval, StorageType,MacAddress, 
            DateAndTime, TEXTUAL-CONVENTION            
                FROM SNMPv2-TC
            SnmpAdminString
                FROM SNMP-FRAMEWORK-MIB          -- RFC2571
            Dot1agCfmMepIdOrZero, Dot1agCfmMaintDomainName, Dot1agCfmMaintAssocName  
                FROM IEEE802171-CFM-MIB
            EnabledStatus
                FROM P-BRIDGE-MIB  
            VlanIdOrNone     
                FROM Q-BRIDGE-MIB  -- [RFC4363]
        HWDot1agCfmRelayActionFieldValue, HWDot1agCfmIngressActionFieldValue, HWDot1agCfmEgressActionFieldValue
            FROM HUAWEI-ETHOAM-MIB;

    
        nqa MODULE-IDENTITY 
            LAST-UPDATED "200601091739Z"-- January 09, 2006 at 17:39 GMT
            ORGANIZATION 
                "Huawei Technologies Co., Ltd."
            CONTACT-INFO 
                "R&D BeiJing, Huawei Technologies co.,Ltd.
                Huawei Bld.,NO.3 Xinxi Rd., 
                Shang-Di Information Industry Base,
                Hai-Dian District Beijing P.R. China
                Zip:100085 
                Http://www.huawei.com                                       
                E-mail:<EMAIL>"
            DESCRIPTION 
                "HUAWEI service quality detect funcion."
            ::= { hwDatacomm 111 }
        
    
--
-- Textual conventions
--
    
        NqaType ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Select test type"
            SYNTAX INTEGER
                {
                unknown(0),
                tcpConnect(1),
                udpEcho(2),
                httpAppl(3),
                ftpAppl(4),
                jitterAppl(5),
                icmpAppl(6),
                snmpAppl(7),
                traceRoute(8),
                lspPing(9),
                lspTraceRoute(10),
                dnsAppl(11),
                dhcpAppl(12),
                dlswAppl(13),
                pwe3Ping(14),
                pwe3Tracert(15),
                mPing(16),
                mTracert(17),
                macPing(18),
                macTunnelPing(19),
                lspJitter(20),
                pathMtu(21),
                icmpJitter(22),
                pathJitter(23),
                pppoe(24),
                vplsmPing(25),
                vplsmacPing(26),
                vplsmacTrace(27),
                vplsMTrace(28),
                gmacping(29),
                gmactrace(30),
                mactrace(31),
                vplspwping(32),
                vplspwtrace(33)
                }  
                
   EnableValue ::= TEXTUAL-CONVENTION
     STATUS current
     DESCRIPTION 
        "Represents a boolean value."
     SYNTAX INTEGER
          {
          enable(1),
           disable(2)
           }  


NqaDistanceNodeType ::= TEXTUAL-CONVENTION
     STATUS current
     DESCRIPTION 
        "Selection of destination addresses when MACPing is performed."
     SYNTAX INTEGER
        {
        macAddress(1),
           mepID(2)
           }  

        HWLldpPortIdSubtype ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "LLDP interface ID sub type"
            SYNTAX INTEGER
                {
                interfaceAlias(1),
                portComponent(2),
                macAddress(3),
                networkAddress(4),
                interfaceName(5),
                agentCircuitId(6),
                local(7)
                }

        HWLldpPortId ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "This TC describes the format of a port identifier string.
                Objects of this type are always used with an associated
                LldpPortIdSubtype object, which identifies the format of the
                particular LldpPortId object instance.
                
                If the associated LldpPortIdSubtype object has a value of
                'interfaceAlias(1)', then the octet string identifies a
                particular instance of the ifAlias object (defined in IETF
                RFC 2863).  If the particular ifAlias object does not contain
                any values, another port identifier type should be used.
                
                If the associated LldpPortIdSubtype object has a value of
                'portComponent(2)', then the octet string identifies a
                particular instance of the entPhysicalAlias object (defined
                in IETF RFC 2737) for a port or backplane component.
                
                If the associated LldpPortIdSubtype object has a value of
                'macAddress(3)', then this string identifies a particular
                unicast source address (encoded in network byte order
                and IEEE 802.3 canonical bit order) associated with the port
                (IEEE Std 802-2001).
                
                If the associated LldpPortIdSubtype object has a value of
                'networkAddress(4)', then this string identifies a network
                address associated with the port.  The first octet contains
                the IANA AddressFamilyNumbers enumeration value for the
                specific address type, and octets 2 through N contain the
                networkAddress address value in network byte order.
                
                If the associated LldpPortIdSubtype object has a value of
                'interfaceName(5)', then the octet string identifies a
                particular instance of the ifName object (defined in IETF
                RFC 2863).  If the particular ifName object does not contain
                any values, another port identifier type should be used.
                
                If the associated LldpPortIdSubtype object has a value of
                'agentCircuitId(6)', then this string identifies a agent-local
                identifier of the circuit (defined in RFC 3046).
                
                If the associated LldpPortIdSubtype object has a value of
                'local(7)', then this string identifies a locally
                assigned port ID."
            SYNTAX OCTET STRING (SIZE (1..255))
            
        NqaOperation ::= TEXTUAL-CONVENTION
            STATUS current
            DESCRIPTION 
                "Paras for specified test."
            SYNTAX INTEGER
                {
                noOperation(1),
                httpGet(2),
                httpPost(3),
                ftpGet(4),
                ftpPut(5)
                }
            
    
--
-- Node definitions
--
    
        nqaBase OBJECT IDENTIFIER ::= { nqa 1 }
        
        nqaVersion OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The NQA version (for example, 1.1). 
                It can be used for version management."
            ::= { nqaBase 1 }
        
        nqaEnable OBJECT-TYPE
            SYNTAX EnableValue
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Indicates the switch of enabling NQA client.
                 (The value range: enable:1; disable:2.The default value is enable(1))"
            ::= { nqaBase 2 }
        
        nqaReset OBJECT-TYPE
            SYNTAX EnableValue
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Clears all the enabled test configurations.
                (The value range: enable:1; disable:2. The default value is disable(2))"
            ::= { nqaBase 3 }
        
        nqaTimeOfLastSetError OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The time of the last configuration error. 
                (0 indicates that the configuration does not fail)."
            ::= { nqaBase 4 }
        
        nqaLastSetError OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The time of the last configuration error.
                (0 indicates that the configuration does not fail)."
            ::= { nqaBase 5 }
        
        nqaNumOfCurrentCtrlEntry OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The total number of current configuration tests."
            ::= { nqaBase 6 }
            
        nqaMaxConcurrentRequests OBJECT-TYPE
            SYNTAX Unsigned32
            UNITS       "requests"
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The maximum number of concurrent active requests
                 that are allowed within an agent implementation."
            ::= { nqaBase 7 }            
            
        nqaMaxNumOfRequests OBJECT-TYPE
            SYNTAX Unsigned32
            UNITS       "requests"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum number of requests
                 that are allowed within an agent implementation. "
            ::= { nqaBase 8 }            
            
        nqaJitterVersion OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Selects the version of jitter test. 
                (The value range: old version:1, new version:2. The default value is old version(1))"
            ::= { nqaBase 9 } 

            nqaSupportTestType OBJECT-TYPE
        SYNTAX   BITS
{ 
                icmp(0),
                tcp(1),
                udp(2),
                http(3),
                ftp(4),
                jitter(5),
                snmp(6),
                trace(7),
                lspPing(8),
                lspTrace(9),
                dns(10),
                dhcp(11),
                dlsw(12),
                pwe3Ping(13),
                pwe3Trace(14),
                mPing(15),
                mTrace(16),
                macPing(17),
                macTunnelPing(18),
                lspJitter(19),
                icmpJitter(20),
                pathJitter(21),
                pathMtu(22),
                pppoe(23),
                vplsmPing(24),
                vplsmacPing(25),
                vplsmacTrace(26),
                vplsMTrace(27),
                gmacping(28),
                gmactrace(29),
                mactrace(30),
                vplspwping(31),
                vplspwtrace(32)
                }
        MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
                "Test type that the current device supports.
                Data structure of the type is in the format of bits. 
                Each bit represents a test type. 
                The mappings between bits and tests are as follows:
                icmp(0),
                tcp(1),
                udp(2),
                http(3),
                ftp(4),
                jitter(5),
                snmp(6),
                trace(7),
                lspPing(8),
                lspTrace(9),
                dns(10),
                dhcp(11),
                dlsw(12),
                pwe3Ping(13),
                pwe3Trace(14),
                mPing(15),
                mTrace(16),
                macPing(17),
                macTunnelPing(18),
                lspJitter(19),
                icmpJitter(20),
                pathJitter(21),
                pathMtu(22),
                pppoe(23),
                vplsmPing(24),
                vplsmacPing(25),
                vplsmacTrace(26),
                vplsMTrace(27),
                gmacping(28),
                gmactrace(29),
                mactrace(30),
                vplspwping(31),
                vplspwtrace(32)
The value #0x00 indicates don't suppourt all testtypes."
            ::= { nqaBase 10}  
            nqaSupportServerType OBJECT-TYPE
        SYNTAX   BITS
{ 
tcpServer(0), 
                        udpServer(1),
                        icmpServer(2)
                        }
MAX-ACCESS read-only
            STATUS current
            DESCRIPTION 
            "Test type that the current device supports.
             Data structure of the type is in the format of bits. 
             Each bit represents a test type. 
             tcpServer(0),
             udpServer(1),
             icmpServer(2)
             The value #0x00 indicates don't suppourt all servertypes."
            ::= { nqaBase 11}
                    
        nqaCtrl OBJECT IDENTIFIER ::= { nqa 2 }
        
        nqaAdminCtrlTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaAdminCtrlEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table of NQA monitoring definitions."
            ::= { nqaCtrl 1 }
        
        nqaAdminCtrlEntry OBJECT-TYPE
            SYNTAX NqaAdminCtrlEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the nqaAdminCtrlTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName }
            ::= { nqaAdminCtrlTable 1 }
        
        NqaAdminCtrlEntry ::=
            SEQUENCE { 
                nqaAdminCtrlOwnerIndex
                    SnmpAdminString,
                nqaAdminCtrlTestName
                    SnmpAdminString,
                nqaAdminCtrlTag
                    DisplayString,
                nqaAdminCtrlType
                    NqaType,
                nqaAdminCtrlFrequency
                    Integer32,
                nqaAdminCtrlTimeOut
                    Integer32,
                nqaAdminCtrlThreshold1
                    Integer32,
                nqaAdminCtrlThreshold2
                    Integer32,
                nqaAdminCtrlThreshold3
                    Integer32,
                nqaAdminCtrlStatus
                    RowStatus
             }

        nqaAdminCtrlOwnerIndex OBJECT-TYPE
            SYNTAX SnmpAdminString (SIZE(0..32))
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "To facilitate the provisioning of access control by a
                security administrator using the View-Based Access
                Control Model for tables in which multiple users may 
                need to independently create or modify entries, 
                the initial index is used as an 'ownerindex'. 

                When used in conjunction with such a security policy
                all entries in the table belonging to a particular user
               (or group) will have the same value for this initial
               index.  For a given user's entries in a particular
               table, the object identifiers for the information in
               these entries will have the same subidentifiers (except
               for the 'column' subidentifier) up to the end of the
               encoded owner index."
            ::= { nqaAdminCtrlEntry 1 }
        
        nqaAdminCtrlTestName OBJECT-TYPE
            SYNTAX SnmpAdminString (SIZE(0..32))
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The name of a test.  This is locally unique,
                within the scope of an nqaAdminCtrlOwnerIndex."
            ::= { nqaAdminCtrlEntry 2 }
        
        nqaAdminCtrlTag OBJECT-TYPE
            SYNTAX DisplayString
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "A string which is the description of the test instance."
            ::= { nqaAdminCtrlEntry 3 }
        
        nqaAdminCtrlType OBJECT-TYPE
            SYNTAX NqaType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "The type of the test instance."
            ::= { nqaAdminCtrlEntry 4 }
        
        nqaAdminCtrlFrequency OBJECT-TYPE      
            SYNTAX Integer32
            UNITS "seconds"
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the interval when the test instance repeats."
            DEFVAL { 0 }
            ::= { nqaAdminCtrlEntry 5 }
        
        nqaAdminCtrlTimeOut OBJECT-TYPE
      SYNTAX      Integer32 (1..60)
       UNITS       "seconds"
   MAX-ACCESS  read-create
   STATUS      current
            DESCRIPTION
                "Specifies the duration to wait for the completion of test 
                 instance. The default value of DHCP-type and FTP-type test 
                 instance is 15s, The default value of PPPoE-type test 
                 instance is 30s, and that of other types is 3s."   
            ::= { nqaAdminCtrlEntry 6 }
        
        nqaAdminCtrlThreshold1 OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Represents the threshold of RTD (Round Trip Delay). If the 
                 RTT violates the threshold, the corresponding counter will 
                 increase. It applies to any type of test instance."
            DEFVAL { 0 }
            ::= { nqaAdminCtrlEntry 7 }
        
        nqaAdminCtrlThreshold2 OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Represents the threshold of OWD (One Way Delay) from source 
                 to destination. If the OWD violates the threshold, the 
                 corresponding counter will increase. It applies only to 
                 jitter-type test instances."
            DEFVAL { 0 }
            ::= { nqaAdminCtrlEntry 8 }
            
        nqaAdminCtrlThreshold3 OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Represents the threshold of OWD (One Way Delay) from 
                 destination to source. If the OWD violates the threshold, 
                 the corresponding counter will increase. It applies only 
                 to jitter-type test instances."
            DEFVAL { 0 }
            ::= { nqaAdminCtrlEntry 9 }
            
        nqaAdminCtrlStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
               "To set the value of this object can create or delete the 
                specified test instance and the records belonging to it. 
                Before deleting a test instance, the custom must make sure 
                that the status of the test instance is inactive through 
                nqaScheduleOperStatus. The value can be set to 
                'CreateAndGo (4)', 'Destroy (6)' and 'Active (1)'."
           REFERENCE
              "See definition of RowStatus in RFC 2579, 'Textual
              Conventions for SMIv2.'"
            ::= { nqaAdminCtrlEntry 10 }
        
        nqaAdminParaTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaAdminParaEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Set parameter for a test. "
            ::= { nqaCtrl 2 }
        
        nqaAdminParaEntry OBJECT-TYPE
            SYNTAX NqaAdminParaEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "set parameter in the nqaAdminParaTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName }
            ::= { nqaAdminParaTable 1 }
        
        NqaAdminParaEntry ::=
            SEQUENCE { 
                nqaAdminParaTargetAddressType
                    InetAddressType,
                nqaAdminParaTargetAddress
                    InetAddress,
                nqaAdminParaTargetPort
                    InetPortNumber,
                nqaAdminParaSourceAddressType
                    InetAddressType,
                nqaAdminParaSourceAddress
                    InetAddress,
                nqaAdminParaSourcePort
                    InetPortNumber,
                nqaAdminParaMaxTtl
                    Unsigned32,
                nqaAdminParaInitialTtl
                    Unsigned32,
                nqaAdminParaStorageType
                    StorageType,
                nqaAdminParaMaxFailures
                    Unsigned32,
                nqaAdminParaDontFragment
                    TruthValue,
                nqaAdminParaDataSize
                    Unsigned32,
                nqaAdminParaDataFill
                    OCTET STRING,
                nqaAdminParaIfIndex
                    InterfaceIndexOrZero,
                nqaAdminParaByPassRouteTable
                    TruthValue,
                nqaAdminParaMiscOptions
                    SnmpAdminString,
                nqaAdminParaProbeCount
                    Unsigned32,
                nqaAdminParaTrapGeneration
                    BITS,
                nqaAdminParaTrapProbeFailureFilter
                    Unsigned32,
                nqaAdminParaTrapTestFailureFilter
                    Unsigned32,
                nqaAdminParaDSField
                    Integer32,
                nqaAdminParaDnsServerAddressType
                    InetAddressType,
                nqaAdminParaDnsServerAddress
                    InetAddress,
                nqaAdminParaOperation
                    NqaOperation,
                nqaAdminParaHttpVersion
                    DisplayString,
                nqaAdminParaHttpOperationString
                    DisplayString,
                nqaAdminParaTestFailurePercent             
                    Unsigned32,
                nqaAdminParaFtpUserName
                    DisplayString,
                nqaAdminParaFtpPassword
                    DisplayString,
                nqaAdminParaFtpFilePath
                    DisplayString,               
                nqaAdminParaFtpFileSize       
                    Integer32,                
                nqaAdminParaInterval
                    Integer32,
                nqaAdminParaNumPackets
                    Integer32,
                nqaAdminParaVrfName
                    DisplayString,
        nqaAdminParaLspAddressType 
            INTEGER,           
        nqaAdminParaLspAddressMask 
            Integer32,
        nqaAdminParaLspIpAddress 
            InetAddress,   
        nqaAdminParaLspPWE3VcId 
            Unsigned32,        
        nqaAdminParaLspPWE3Type 
            INTEGER,          
        nqaAdminParaLspPWE3Option 
            INTEGER,        
            nqaAdminParaLspPWE3RemoteVcId 
                Unsigned32,            
             nqaAdminParaLspPWE3RemoteAddress 
            InetAddress,        
             nqaAdminParaLspExp 
                Unsigned32,   
                nqaAdminParaLspReplyMode 
                    INTEGER,
            nqaAdminParaResultRowMax                  
                    Integer32,
                nqaAdminParaHistoryRowMax
                    Integer32,
                nqaAdminParaCreateHopsEntries
                    EnableValue,
                nqaAdminParaLspVCType
    INTEGER,   
        nqaAdminParaMTraceLastHopAddress 
    InetAddress,
nqaAdminParaMTraceSourceAddress 
    InetAddress,
nqaAdminParaMTraceGroupAddress  
    InetAddress,
nqaAdminParaMTraceMaxTtl  
    Unsigned32,
nqaAdminParaMTraceSendMode  
    INTEGER,
nqaAdminParaMTraceResponseTtl  
    Unsigned32,
nqaAdminParaMTraceResponseAddressType 
    InetAddressType,
nqaAdminParaMTraceResponseAddress
                InetAddress,
            nqaAdminParaDistanceNodeType 
                    NqaDistanceNodeType,
            nqaAdminParaMacAddress
                    MacAddress,
                nqaAdminParaRMepID 
                    Dot1agCfmMepIdOrZero,  
            nqaAdminParaMDName 
                OCTET STRING,
                nqaAdminParaMAName 
                    OCTET STRING,  
                nqaAdminParaMacTunnelName 
                    OCTET STRING,
                nqaAdminParaPathMtuStep
                    Integer32,
                nqaAdminParaPathMtuDiscoveryPathMtuMax
                    Integer32,
                nqaAdminParaIcmpJitterMode
                    INTEGER,                                     
                nqaAdminParaCodecType
                    INTEGER,
                nqaAdminParaIcpifAdvFactor 
                    Integer32,
                nqaAdminParaFtpMode
                    INTEGER,    
                nqaAdminParaHardwareBased
                EnabledStatus,
                nqaAdminParaPppoeUserName
                OCTET STRING,     
                nqaAdminParaPppoePassword
                OCTET STRING,
                nqaAdminParaPppoeVlanIf
                Integer32,
                nqaAdminParaPppoeAuthenticationMode
                INTEGER,
                nqaAdminParaPppoeRedialUpTimes
                Integer32,
                nqaAdminParaPppoeInterval
                Integer32,         
                nqaAdminParaVsiName

                    OCTET STRING,  
                nqaAdminParaVlanId
                    VlanIdOrNone,
                nqaAdminParaLspTunnelType
                    INTEGER,                       
                nqaAdminParaLspNextHopAddress
                    InetAddress,
                nqaAdminParaLspVersion 
                INTEGER,
                nqaAdminParaRemoteAddressType 
                    InetAddressType,
                nqaAdminParaRemoteAddress
                    InetAddress,                                      
                nqaAdminParaTimeUnit
                INTEGER                                      
             }

        nqaAdminParaTargetAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Represents the address type of destination."
            DEFVAL { unknown }              
            ::= { nqaAdminParaEntry 1 }
        
        nqaAdminParaTargetAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Represents the address of destination."
            ::= { nqaAdminParaEntry 2 }
        
        nqaAdminParaTargetPort OBJECT-TYPE
            SYNTAX InetPortNumber
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Represents the port number of destination."
            ::= { nqaAdminParaEntry 3 }
        
        nqaAdminParaSourceAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Represents the address type of source."
            DEFVAL { unknown }
            ::= { nqaAdminParaEntry 4 }
        
        nqaAdminParaSourceAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Represents the address of source."
            ::= { nqaAdminParaEntry 5 }
        
        nqaAdminParaSourcePort OBJECT-TYPE
            SYNTAX InetPortNumber
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
               "Represents the port number of source."
            ::= { nqaAdminParaEntry 6 }
            
        nqaAdminParaMaxTtl OBJECT-TYPE
            SYNTAX Unsigned32 (0..255)
            UNITS       "time-to-live value"
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the maximum value of TTL. The value cannot be set to 0 unless the test-type is pwe3ping, the lsp-version is rfc4379, and the ttl-copymode is pipe."
            ::= { nqaAdminParaEntry 7 }
            
        nqaAdminParaInitialTtl OBJECT-TYPE
            SYNTAX Unsigned32 (1..255)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the initial value of TTL."
            DEFVAL { 1 }
            ::= { nqaAdminParaEntry 8 }
            
        nqaAdminParaStorageType OBJECT-TYPE
            SYNTAX      StorageType
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The storage type for this conceptual row. Conceptual rows 
                 having the value 'permanent' need not allow write-access 
                 to any columnar objects in the row."
            DEFVAL { nonVolatile }
            ::= { nqaAdminParaEntry 9 }
        
        nqaAdminParaMaxFailures OBJECT-TYPE
            SYNTAX      Unsigned32 (1..255)
            UNITS       "timeouts"
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The value of this object indicates the maximum number of 
                 consecutive timeouts allowed before terminating a remote 
                 traceroute request. A value of 255 (maximum hop 
                 count/possible TTL value) indicates that the function 
                 of terminating a remote traceroute request when a specific 
                 number of successive timeouts are detected is disabled."
            DEFVAL { 5 }
            ::= { nqaAdminParaEntry 10 }
            
        nqaAdminParaDontFragment OBJECT-TYPE
            SYNTAX         TruthValue
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "This object enables setting of Don't Fragment (DF) flag in 
                 the IP header for a probe. Use of this object enables 
                 performing a manual PATH MTU test."
            DEFVAL  { false }
            ::= { nqaAdminParaEntry 11 }
            
        nqaAdminParaDataSize OBJECT-TYPE
            SYNTAX      Unsigned32 
            UNITS       "octets"
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The size of data field in packet."
            DEFVAL { 0 }
            ::= { nqaAdminParaEntry 12 }    

        nqaAdminParaDataFill OBJECT-TYPE
            SYNTAX      OCTET STRING 
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Represents the characters used for filling the data field. 
                 This object is used together with the corresponding object, 
                 nqaAdminParaDataSize."
            DEFVAL { '00'H }
            ::= { nqaAdminParaEntry 13 }    

        nqaAdminParaIfIndex OBJECT-TYPE
            SYNTAX      InterfaceIndexOrZero
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Specified the source interface or tunnel, from which probes 
                 of the test instance are sent. The object applies to DHCP, 
                 jitter, icmpjitter, ICMP, lspping or lsptrace type test instance.
                 The value zero implies that the object is disabled."
            DEFVAL { 0 }
            ::= { nqaAdminParaEntry 14 }    

        nqaAdminParaByPassRouteTable OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
               "The purpose of this object is to optionally enable bypassing 
                the route table. If enabled, the remote host will bypass the 
                normal routing tables and send directly to a host on an attached 
                network. If the host is not on a directly-attached network, an 
                error is returned. This option can be used to perform the 
                ping/traceroute operation to a local host through an interface 
                that has no route defined (e.g., after the interface was dropped 
                by routed)."
            DEFVAL { false }
            ::= { nqaAdminParaEntry 15 }
            
        nqaAdminParaMiscOptions OBJECT-TYPE
            SYNTAX      SnmpAdminString
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Enables an application to specify implementation dependent options."
            DEFVAL { ''H }
            ::= { nqaAdminParaEntry 16 }
            
        nqaAdminParaProbeCount OBJECT-TYPE
            SYNTAX      Unsigned32 (1..15) 
            UNITS       "probes"
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Specifies the number of packets sent by the test instance, or times 
                 of probe repeated, in which may send several packets. The second case 
                 applies to jitter-type test instances."
            ::= { nqaAdminParaEntry 17 }

            
        nqaAdminParaTrapGeneration OBJECT-TYPE
             SYNTAX      BITS {
                   probeFailure(0),
                   testFailure(1),
                   testCompletion(2),
                   overRtdThreshold(3),
                   overOwdThresholdSd(4),
                   overOwdThresholdDs(5)
                 }
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
               "Switch of notification. Determine whether and how to send notifications 
                for the test instance:
                - probeFailure(0)     - Generate a PathChange notification when the current 
                  path varies from a previously determined path.
                - testFailure(1)    - Generate a TestFailed notification when the full 
                  path to a target can't be determined.
                - testCompletion(2) - Generate a TestCompleted notification when the path 
                  to a target has been determined.
                - overRtdThreshold(3)  - Generate a Overthreshold of RTD notification when 
                  the statistic results exceed threshold.
                - overOwdThresholdSd(4)  - Generate a Overthreshold of OWD-SD notification 
                  when the statistic results exceed threshold.
                - overOwdThresholdDs(5)  - Generate a Overthreshold of OWD-DS notification 
                  when the statistic results exceed threshold.
                The value #0x00 indicates disable all notifications."
             ::= { nqaAdminParaEntry 18 } 

        nqaAdminParaTrapProbeFailureFilter OBJECT-TYPE
             SYNTAX      Unsigned32 (1..15)
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "Specifies the condition when to trigger an nqaProbeFailed notification. 
                  When the number of consecutive failed probes violates the threshold indicated 
                  by nqaAdminParaTrapProbeFailureFilter, a notification will be created. This 
                  object is used together with nqaAdminParaTrapGeneration."
             DEFVAL { 1 }
             ::= { nqaAdminParaEntry 19 }  
                   
        nqaAdminParaTrapTestFailureFilter OBJECT-TYPE
             SYNTAX      Unsigned32 (1..15)
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "Specifies the condition when to trigger an nqaTestFailed notification. When 
                  the number of failed tests violates the threshold indicated by 
                  nqaAdminParaTrapTestFailureFilter, a notification will be created. There are 
                  usually several probes in each test. This object is used together with 
                  nqaAdminParaTrapGeneration."
             DEFVAL { 1 }
             ::= { nqaAdminParaEntry 20 }                     
        
        nqaAdminParaDSField OBJECT-TYPE
             SYNTAX Integer32 (0..255)
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the Differentiated Services (DS) field in the IP packet. The DS 
                  field is defined as the Type Of Service (TOS) octet in IPv4 header or as 
                  the Traffic Class octet in IPv6 header."
             DEFVAL { 0 }
             ::= { nqaAdminParaEntry 21 }
        
        nqaAdminParaDnsServerAddressType OBJECT-TYPE
             SYNTAX InetAddressType
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the address type of DNS server."
             DEFVAL { unknown }
             ::= { nqaAdminParaEntry 22 }
        
        nqaAdminParaDnsServerAddress OBJECT-TYPE
             SYNTAX InetAddress
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the address of DNS server."
             ::= { nqaAdminParaEntry 23 }
        
        nqaAdminParaOperation OBJECT-TYPE
             SYNTAX NqaOperation
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the operation of FTP or HTTP type test instance. The 
                  operations of FTP-type test instance include get and put. The 
                  operations of HTTP-type test instance include get and post."
             DEFVAL { noOperation }
             ::= { nqaAdminParaEntry 24 }
        
        nqaAdminParaHttpVersion OBJECT-TYPE
             SYNTAX DisplayString
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the version of HTTP."
             ::= { nqaAdminParaEntry 25 }
        
        nqaAdminParaHttpOperationString OBJECT-TYPE
             SYNTAX DisplayString
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "A string that specifies the HTTP operation which will be sent 
                  to the HTTP server."
             ::= { nqaAdminParaEntry 26 }
                                                 
        nqaAdminParaTestFailurePercent OBJECT-TYPE
             SYNTAX      Unsigned32 (0..100)
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                "Specifies the scale over which the test will be regarded as failed."
            DEFVAL { 100 }                                                  
             ::= { nqaAdminParaEntry 27 }       
             
        nqaAdminParaFtpUserName OBJECT-TYPE
             SYNTAX DisplayString
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the user name of the FTP server."
             ::= { nqaAdminParaEntry 28 }
        
        nqaAdminParaFtpPassword OBJECT-TYPE
             SYNTAX DisplayString
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the password of the user of the FTP server."
             ::= { nqaAdminParaEntry 29 }
        
        nqaAdminParaFtpFilePath OBJECT-TYPE
             SYNTAX DisplayString
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the full name of the file which will be transmitted 
                  by the FTP-type test instance."
             ::= { nqaAdminParaEntry 30 }          

        nqaAdminParaFtpFileSize OBJECT-TYPE
             SYNTAX Integer32 (0..10000)
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the size that the file will be transmitted by the 
                  FTP-type test instance. It may be part of the file."
             ::= { nqaAdminParaEntry 31 }
        
        nqaAdminParaInterval OBJECT-TYPE
             SYNTAX Integer32 (0..60000)
             UNITS "millseconds"
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the interval between two consecutive probe packets."
             ::= { nqaAdminParaEntry 32 }
        
        nqaAdminParaNumPackets OBJECT-TYPE
             SYNTAX Integer32 
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the number of packet sent in each probe. This object 
                  is used together with nqaAdminParaProbeCount, and only apples to 
                  jitter-type test instance."
             DEFVAL { 20 }
             ::= { nqaAdminParaEntry 33 }
        
        nqaAdminParaVrfName OBJECT-TYPE
             SYNTAX DisplayString
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the VRF name."
             ::= { nqaAdminParaEntry 34 }
 
nqaAdminParaLspAddressType OBJECT-TYPE
             SYNTAX      INTEGER {
         ipv4(1), 
         te(3),
         ring(255)
         }
     MAX-ACCESS  read-write
     STATUS      current
     DESCRIPTION
         "Specifies the address type of LSP. The object applies to lspping, 
          lsptrace and lspjitter type test instance."
     ::= { nqaAdminParaEntry 35 }
           
         nqaAdminParaLspAddressMask OBJECT-TYPE
             SYNTAX   Integer32
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the address mask of LSP. The object apples to lspping, 
                  lsptrace and lspjitter type test instance."
             ::= { nqaAdminParaEntry 36 }   
            
 nqaAdminParaLspIpAddress OBJECT-TYPE
             SYNTAX      InetAddress
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "IP Address filled in the ip header, must be a 127/8 address."
             ::= { nqaAdminParaEntry 37 }            
            
          nqaAdminParaLspPWE3VcId OBJECT-TYPE
             SYNTAX      Unsigned32
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "The local Pseudo wire ID in PWE3 Ping test"
             ::= { nqaAdminParaEntry 38 }  
            
          nqaAdminParaLspPWE3Type OBJECT-TYPE
             SYNTAX      INTEGER {
                 fr(1), 
                 atmAal5Sdu(2),
                 atm-cell-transport(3),
                 vlan(4),
                 ethernet(5),
                 hdlc(6),
                 ppp(7),
                 atm-nto1-vcc(9),
                 atm-nto1-vpc(10),
                 ip-layer2(11),
                 atm-1to1-vcc(12),
                 atm-1to1-vpc(13),
                 satop-e1(17),
                 satop-t1(18),
                 satop-e3(19),
                 cesopsn-basic(21),
                 ipInterworking(64)
                 }
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "The encapsulation type of Pseudo wire ID in the PWE3 Ping test"
             ::= { nqaAdminParaEntry 39 }   
        
        nqaAdminParaLspPWE3Option OBJECT-TYPE
             SYNTAX      INTEGER {
                 labelAlert(1), 
                 controlWord(2),
                 normal(3)
                 }
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "The label type of PWE3 Ping and PWE3 Tracert test."
             ::= { nqaAdminParaEntry 40 }
        
        nqaAdminParaLspPWE3RemoteVcId OBJECT-TYPE
             SYNTAX      Unsigned32
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "The remote Pseudo wire ID in PWE Ping test."
             ::= { nqaAdminParaEntry 41 }
            
          nqaAdminParaLspPWE3RemoteAddress OBJECT-TYPE
             SYNTAX      InetAddress
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "The remote IP address  in PWE3 Ping test."
             ::= { nqaAdminParaEntry 42 } 
        
        nqaAdminParaLspExp OBJECT-TYPE
             SYNTAX      Unsigned32 (0..7)
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "The 3bit EXP field of the MPLS echo packet label."
             DEFVAL { 0 }
             ::= { nqaAdminParaEntry 43 } 
        
        nqaAdminParaLspReplyMode OBJECT-TYPE
             SYNTAX      INTEGER {
                 noReply(1), 
                 udp(2),
                 udpRouterAlert(3),
                 levelControlChannel(4),
                 udpviaVPLS(5)
                 }
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "ReplyMode
                  Value    Meaning
                  -----    -------
  1    Do not reply
  2    Reply via an IPv4/IPv6 UDP packet
  3    Reply via an IPv4/IPv6 UDP packet with Router Alert
  4    Reply via application level control channel
  5    Reply via a VPLS IPv4 UDP packet"
             DEFVAL { udp }
             ::= { nqaAdminParaEntry 44 }                       
        
         nqaAdminParaResultRowMax OBJECT-TYPE
             SYNTAX Integer32 
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the maximum number of result records."
             DEFVAL { 5 }
             ::= { nqaAdminParaEntry 45 }
        
         nqaAdminParaHistoryRowMax OBJECT-TYPE
             SYNTAX Integer32 
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specifies the maximum number of history records."
             DEFVAL { 50 }
             ::= { nqaAdminParaEntry 46 }
            
        nqaAdminParaCreateHopsEntries OBJECT-TYPE
            SYNTAX      EnableValue
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "When set to 'enable (1)', the result of each hop 
                 will be kept in nqaResultsTable. The object applies 
                 to trace, pwe3trace, lsptrace, mtrace type test instance."
            DEFVAL { disable }
            ::= { nqaAdminParaEntry 47 }
            
          nqaAdminParaLspVCType OBJECT-TYPE
             SYNTAX      INTEGER {
                 ldp(1), 
                 bgp(2),
                 bgpad(3)
                 }
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                 "The type of protocol which establishes PW in the pwe3tracert test"
             ::= { nqaAdminParaEntry 48 }   
            
        nqaAdminParaMTraceLastHopAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The last hop router's IP address in Mtrace test."
            ::= { nqaAdminParaEntry 49 }

        nqaAdminParaMTraceSourceAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The IP address of the multicast source for the path being 
                traced in Mtrace test."
            ::= { nqaAdminParaEntry 50 }

        nqaAdminParaMTraceGroupAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The group address to be traced in Mtrace test, or zero if no
                 group-specific information is desired."
            ::= { nqaAdminParaEntry 51 }

        nqaAdminParaMTraceMaxTtl OBJECT-TYPE
            SYNTAX      Unsigned32 (1..255)
            MAX-ACCESS  read-write
            STATUS      current 
            DESCRIPTION
                "The maximum number of hops that the requester wants to 
                trace in Mtrace test." 
            DEFVAL { 255 }
            ::= { nqaAdminParaEntry 52 }

        nqaAdminParaMTraceSendMode OBJECT-TYPE
            SYNTAX      INTEGER {
             multicastTree(1),
                 allRouter(2), 
                 destination (3),
                 lastHop(4)
                 }

            MAX-ACCESS  read-write
            STATUS      current   
            DESCRIPTION
                "The sending mode of the Query pamessage in Mtrace test."   
            DEFVAL { multicastTree }
            ::= { nqaAdminParaEntry 53 }

        nqaAdminParaMTraceResponseTtl OBJECT-TYPE
            SYNTAX      Unsigned32 (1..255)
            MAX-ACCESS  read-write
            STATUS      current 
            DESCRIPTION
                "The TTL at which to multicast the response in Mtrace test,
                 if the response address is a multicast address." 
            DEFVAL { 64 }
            ::= { nqaAdminParaEntry 54 } 
              
        nqaAdminParaMTraceResponseAddressType OBJECT-TYPE
            SYNTAX      InetAddressType
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Mtrace response address type"
            DEFVAL { unknown }
            ::= { nqaAdminParaEntry 55 }   
            
        nqaAdminParaMTraceResponseAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "This field specifies where the completed traceroute response 
                packet gets sent in Mtrace test. It can be a unicast address or 
                a multicast address"
            ::= { nqaAdminParaEntry 56 }   

    nqaAdminParaDistanceNodeType OBJECT-TYPE
            SYNTAX      NqaDistanceNodeType
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "The destination of MAC ping(mac address or rmepid)."
            ::= { nqaAdminParaEntry 57 } 
             
        nqaAdminParaMacAddress OBJECT-TYPE
            SYNTAX      MacAddress
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Mac address of the destination of MAC PING."
            ::= { nqaAdminParaEntry 58 }  
                                                  
       nqaAdminParaRMepID OBJECT-TYPE
            SYNTAX      Dot1agCfmMepIdOrZero
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Rmepid of the destination of MAC PING."
            ::= { nqaAdminParaEntry 59 }    
                 
        nqaAdminParaMDName OBJECT-TYPE
            SYNTAX      OCTET STRING(SIZE(0..43))  
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Indicate the MD name in which the MAC Ping operated."
            ::= { nqaAdminParaEntry 60 }  
            
        nqaAdminParaMAName OBJECT-TYPE
            SYNTAX      OCTET STRING(SIZE(0..45))       
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Indicate the MA name in which the MAC Ping operated."
            ::= { nqaAdminParaEntry 61 }       
            
       nqaAdminParaMacTunnelName OBJECT-TYPE
            SYNTAX      OCTET STRING
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Indicate the MAC Tunnel name in which the MAC Ping operated."
            ::= { nqaAdminParaEntry 62 }  
            
         nqaAdminParaPathMtuStep OBJECT-TYPE
             SYNTAX Integer32 (1..512)
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specify the step of the path MTU test.If the last response
                  was received successful,the next packet's length will add 
                  the step."
             ::= { nqaAdminParaEntry 63 }     
             
         nqaAdminParaPathMtuDiscoveryPathMtuMax OBJECT-TYPE
             SYNTAX Integer32 (48..9198)
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Specify the max value of the discovery field in the path MTU test."
             ::= { nqaAdminParaEntry 64 }   
             
         nqaAdminParaIcmpJitterMode OBJECT-TYPE
             SYNTAX INTEGER 
             {
               icmpTimestamp(1), 
               icmpEcho(2)
             }
             MAX-ACCESS read-write
             STATUS current
             DESCRIPTION
                 "Select the type of ICMP packets to be sent in ICMP jitter and path 
                  jitter test.The value can be icmpTimestamp(1) or icmpEcho(2)."
             ::= { nqaAdminParaEntry 65 }     
             
        nqaAdminParaCodecType OBJECT-TYPE
            SYNTAX      INTEGER
            {
                    notDefined(1),     -- no codec type is defined or codec is unknown
                    g711Alaw(2),       -- uses G.711 A-Law
                    g711Ulaw(3),       -- uses G.711 muHmm-Law
                    g729A(4)           -- uses G.729A
               }
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "To indicate the codec type to be used with jitter probe. This is
                applicable only for the jitter probe." 
            DEFVAL { notDefined }
            ::= { nqaAdminParaEntry 66 }
          
         nqaAdminParaIcpifAdvFactor OBJECT-TYPE
            SYNTAX  Integer32
            MAX-ACCESS read-write
            STATUS  current
            DESCRIPTION
                "The advantage factor depends on the type of access and how the service
                is to be used. This object will be used while calculating the ICPIF
                (Calculated Planning Impairment Factor) values.

                Suggestion values:
                |---------------------------------------------------|-------|
                |          Service                                  |factor |
                |---------------------------------------------------|-------|
                |   conventional wire-line                          |   0   |
                |---------------------------------------------------|-------|
                |   mobility within Building                        |   5   |
                |---------------------------------------------------|-------|
                |   mobility within geographical area               |  10   |
                |---------------------------------------------------|-------|
                |   access to hard-to-reach location                |  20   |
                |---------------------------------------------------|-------|
                "
            DEFVAL  {0}
            ::= { nqaAdminParaEntry 67 }  
            
        nqaAdminParaFtpMode OBJECT-TYPE
            SYNTAX      INTEGER
            {
                    active(1),       -- active mode is defined in ftp test
                    passive(2)      -- passive mode is defined in ftp test
               }
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "To indicate the mode to be used with ftp probe. This is
                applicable only for the ftp probe." 
            DEFVAL { active }
            ::= { nqaAdminParaEntry 68 } 
            
        nqaAdminParaHardwareBased OBJECT-TYPE
            SYNTAX EnabledStatus            
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Indicates that the hardware is enabled to send packets.
                 (The value range: enable:1;disable:2.The default value is disable(2))"            
            ::= { nqaAdminParaEntry 69 }
 
        nqaAdminParaPppoeUserName OBJECT-TYPE
            SYNTAX OCTET STRING(SIZE(0..32))           
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the username of PPPoE dial-up. For jitter,icmp,traceroute,
                 tcp,udp,ftp,dns,http probes, this parameter indicates that the probe 
                 uses PPPoE encapsulation.
                 This parameter accepts the characters in sight only."            
            ::= { nqaAdminParaEntry 70 }
   
        nqaAdminParaPppoePassword OBJECT-TYPE
            SYNTAX OCTET STRING(SIZE(0..16))           
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the password of PPPoE dial-up. This is 
        applicable only for the PPPoE probe.
        This parameter accepts the characters in sight only."            
            ::= { nqaAdminParaEntry 71 }  
            
        nqaAdminParaPppoeVlanIf OBJECT-TYPE
            SYNTAX Integer32(0..4093)           
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the vlanif of PPPoE dial-up. For jitter, icmp, traceroute, tcp, 
                 udp, ftp, dns, http probes, this parameter indicates that the probe uses 
                 PPPoE encapsulation."           
            ::= { nqaAdminParaEntry 72 }
   
        nqaAdminParaPppoeAuthenticationMode OBJECT-TYPE
            SYNTAX INTEGER
            {
            chap(1),
            pap(2)
            }          
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the authentication mode of PPPoE dial-up. This is 
        applicable only for the PPPoE probe."            
            ::= { nqaAdminParaEntry 73 }

        nqaAdminParaPppoeRedialUpTimes OBJECT-TYPE
            SYNTAX Integer32(0..10)
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "Specifies the redialup times of PPPoE dial-up. This is 
        applicable only for the PPPoE probe." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaEntry 74 }

        nqaAdminParaPppoeInterval OBJECT-TYPE
            SYNTAX Integer32(1..600)
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the redialup interval of PPPoE dial-up. This is 
        applicable only for the PPPoE probe." 
DEFVAL { 60 }           
            ::= { nqaAdminParaEntry 75 }

        nqaAdminParaVsiName OBJECT-TYPE
            SYNTAX OCTET STRING (SIZE(0..31))           
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specify the name of the VSI(Virtual Switch Instance)"            
            ::= { nqaAdminParaEntry 76 }

        nqaAdminParaVlanId OBJECT-TYPE
            SYNTAX VlanIdOrNone           
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specify the Vlan ID"            
            ::= { nqaAdminParaEntry 77 }

        nqaAdminParaLspTunnelType OBJECT-TYPE
            SYNTAX      INTEGER
        {
                    main(0),            -- ping/trace the main lsp tunnel
                    hotstandby(1)      -- ping/trace the hot-standby lsp tunnel
            }
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "To indicate ping/trace which lsp tunnel." 
            DEFVAL { main }
            ::= { nqaAdminParaEntry 78 } 
            
nqaAdminParaLspNextHopAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Represents the address of nexthop."
            ::= { nqaAdminParaEntry 79 }    

 nqaAdminParaLspVersion OBJECT-TYPE
             SYNTAX      INTEGER
             {
         draft6(1), 
         rfc4379(2),
         ptnmode(3)
         }
     MAX-ACCESS  read-write
     STATUS      current
     DESCRIPTION
         "Specify lsp version of protocol. Draft-ietf-mpls-lsp-ping-06 protocol, RFC4379 protocol or ptn mode." 
     DEFVAL { 1 }
     ::= { nqaAdminParaEntry 80 }

        nqaAdminParaRemoteAddressType OBJECT-TYPE
            SYNTAX      InetAddressType
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Represents the address type of remote."
            DEFVAL { unknown }
            ::= { nqaAdminParaEntry 81 }   
            
        nqaAdminParaRemoteAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
                "Represents the address of remote."
            ::= { nqaAdminParaEntry 82 } 
     nqaAdminParaTimeUnit OBJECT-TYPE            
            SYNTAX      INTEGER
            {
                    us(1),       -- The unit of the timestamp is us
                    ms(2)        -- The unit of the timestamp is ms
            }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "The unit of the timestamp, ms or us."
            ::= { nqaAdminParaEntry 83 }
     
            
        nqaAdminParaExtTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaAdminParaExtEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Set parameter for a test."
            ::= { nqaCtrl 5 }
        
        nqaAdminParaExtEntry OBJECT-TYPE
            SYNTAX NqaAdminParaExtEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Set parameter in the nqaAdminParaExtTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName }
            ::= { nqaAdminParaExtTable 1 }
        
        NqaAdminParaExtEntry ::=
            SEQUENCE { 
                nqaAdminExtPara1
                    OCTET STRING,
                nqaAdminExtPara2
                    Integer32,
                nqaAdminExtPara3
                    OCTET STRING,
                nqaAdminExtPara4
                    Integer32,
                nqaAdminExtPara5
                    OCTET STRING,
                nqaAdminExtPara6
                    Integer32,
                nqaAdminExtPara7
                    OCTET STRING,   
                nqaAdminExtPara8
                    Integer32,
                nqaAdminExtPara9
                    OCTET STRING,
                nqaAdminExtPara10
                    Integer32,
                nqaAdminExtPara11
                    OCTET STRING,   
                nqaAdminExtPara12
                    Integer32,   
                nqaAdminExtPara13
                    OCTET STRING,   
                nqaAdminExtPara14
                    Integer32,   
                nqaAdminExtPara15
                    OCTET STRING,   
                nqaAdminExtPara16
                    Integer32,   
                nqaAdminExtPara17
                    OCTET STRING,    
                nqaAdminExtPara18
                    Integer32,   
                nqaAdminExtPara19
                    OCTET STRING,
                nqaAdminExtPara20
                    Integer32,
                nqaAdminExtPara21
                    OCTET STRING,   
                nqaAdminExtPara22
                    Integer32,   
                nqaAdminExtPara23
                    OCTET STRING,   
                nqaAdminExtPara24
                    Integer32,   
                nqaAdminExtPara25
                    OCTET STRING,   
                nqaAdminExtPara26
                    Integer32,   
                nqaAdminExtPara27
                    OCTET STRING,    
                nqaAdminExtPara28
                    Integer32,   
                nqaAdminExtPara29
                    OCTET STRING,
                nqaAdminExtPara30
                    Integer32,
                nqaAdminExtPara31
                    OCTET STRING,  
                nqaAdminExtPara32
                    OCTET STRING,  
                nqaAdminExtPara33
                    OCTET STRING,  
                nqaAdminExtPara34
                    OCTET STRING,  
                nqaAdminExtPara35
                    OCTET STRING,  
                nqaAdminExtPara36
                    OCTET STRING,  
                nqaAdminExtPara37
                    OCTET STRING,   
                nqaAdminExtPara38
                    OCTET STRING,  
                nqaAdminExtPara39
                    OCTET STRING,
                nqaAdminExtPara40
                    OCTET STRING, 
                nqaAdminExtPara41
                    OCTET STRING,  
                nqaAdminExtPara42
                    OCTET STRING,  
                nqaAdminExtPara43
                    OCTET STRING,  
                nqaAdminExtPara44
                    OCTET STRING,  
                nqaAdminExtPara45
                    OCTET STRING,  
                nqaAdminExtPara46
                    OCTET STRING,  
                nqaAdminExtPara47
                    OCTET STRING,   
                nqaAdminExtPara48
                    OCTET STRING,  
                nqaAdminExtPara49
                    OCTET STRING,
                nqaAdminExtPara50
                    OCTET STRING                
             }    
             
         nqaAdminExtPara1 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara2 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 1 }
        
        nqaAdminExtPara2 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara1 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 2 }
            
        nqaAdminExtPara3 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara4 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 3 }
        
        nqaAdminExtPara4 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara3 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 4 }
            
        nqaAdminExtPara5 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara6 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 5 }
        
        nqaAdminExtPara6 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara5 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 6 }
            
            nqaAdminExtPara7 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara8 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 7 }
        
        nqaAdminExtPara8 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara7 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 8 }
            
        nqaAdminExtPara9 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara10 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 9 }
        
        nqaAdminExtPara10 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara9 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 10 }
            
        nqaAdminExtPara11 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara12 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 11 }
        
        nqaAdminExtPara12 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara11 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 12 }
            
        nqaAdminExtPara13 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara14 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 13 }
        
        nqaAdminExtPara14 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara13 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 14 }
            
        nqaAdminExtPara15 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara16 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 15 }
        
        nqaAdminExtPara16 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara15 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 16 }
            
        nqaAdminExtPara17 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara18 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 17 }
        
        nqaAdminExtPara18 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara17 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 18 }
            
        nqaAdminExtPara19 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara20 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 19 }
        
        nqaAdminExtPara20 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara19 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 20 }
            
        nqaAdminExtPara21 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara22 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 21 }
        
        nqaAdminExtPara22 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara21 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 22 }
            
        nqaAdminExtPara23 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara24 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 23 }
        
        nqaAdminExtPara24 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara23 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 24 }
            
        nqaAdminExtPara25 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara26 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 25 }
        
        nqaAdminExtPara26 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara25 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 26 }
            
        nqaAdminExtPara27 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara28 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 27 }
        
        nqaAdminExtPara28 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara27 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 28 }
            
        nqaAdminExtPara29 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara30 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 29 }
        
        nqaAdminExtPara30 OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara29 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 30 }
            
       nqaAdminExtPara31 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara32 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 31 }
        
        nqaAdminExtPara32 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara31 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 32 }
            
        nqaAdminExtPara33 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara34 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 33 }
        
        nqaAdminExtPara34 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara33 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 34 }
            
        nqaAdminExtPara35 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara36 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 35 }
        
        nqaAdminExtPara36 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara35 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 36 }
            
        nqaAdminExtPara37 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara38 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 37 }
        
        nqaAdminExtPara38 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara37 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 38 }
            
        nqaAdminExtPara39 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara40 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 39 }
        
        nqaAdminExtPara40 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara39 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 40 }
            
        nqaAdminExtPara41 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara42 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 41 }
        
        nqaAdminExtPara42 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara41 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 42 }
            
        nqaAdminExtPara43 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara44 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 43 }
        
        nqaAdminExtPara44 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara43 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 44 }
            
        nqaAdminExtPara45 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara46 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 45 }
        
        nqaAdminExtPara46 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara45 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 46 }
            
        nqaAdminExtPara47 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara48 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 47 }
        
        nqaAdminExtPara48 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara47 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 48 }
            
        nqaAdminExtPara49 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current 
            DESCRIPTION
                "This object indicates the description of the nqaAdminExtPara50 object. 
                The value is automatically assigned by the system after the type of the test case is ascertained." 
            ::= { nqaAdminParaExtEntry 49 }
        
        nqaAdminExtPara50 OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-write
            STATUS current 
            DESCRIPTION
                "This object is a configurable NQA parameter object. 
                Its value and meaning are assigned by the nqaAdminExtPara49 object after the test case is ascertained." 
            DEFVAL { 0 }         
            ::= { nqaAdminParaExtEntry 50 }
            
            
        nqaScheduleTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaScheduleEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Schedule test ."
            ::= { nqaCtrl 3 }
        
        nqaScheduleEntry OBJECT-TYPE
            SYNTAX NqaScheduleEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Set start-up in the nqaAdminParaTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName }
            ::= { nqaScheduleTable 1 }
        
        NqaScheduleEntry ::=
            SEQUENCE { 
                nqaScheduleStartType
                    INTEGER,
                nqaScheduleStartTime
                    Unsigned32,
                nqaScheduleEndType
                    INTEGER,
                nqaScheduleEndTime
                    Unsigned32,
                nqaScheduleAgeTime
                    Integer32,
                nqaScheduleElapsedTime
                    TimeInterval,
                nqaScheduleNumOfInitiations
                    Integer32,
                nqaScheduleLastFinishIndex
                    Integer32,
                nqaScheduleOperStatus
                    INTEGER,
                nqaScheduleLastCollectIndex
                    Integer32
             }
                         
        nqaScheduleStartType OBJECT-TYPE
            SYNTAX INTEGER{
                 default(0),
                 startNow(1), 
                 startAt(2),
                 startAfter(3)
                 }  
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the start type of the test instance. The object 
                 is used together with nqaScheduleStartTime."
            DEFVAL { 0 }
            ::= { nqaScheduleEntry 1 }
        
        nqaScheduleStartTime OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the start time of the test instance. Setting 
                 the value to 0 indicates to start the test instance 
                 immediately. After configuring this object, the status 
                 of the test instance is active, and the parameters of 
                 the test instance can't be changed."
            ::= { nqaScheduleEntry 2 }
        
        nqaScheduleEndType OBJECT-TYPE
            SYNTAX INTEGER{
             default(0),
                 endAt(1),
                 endAfter(2),
                 endLifetime(3)
                 }  
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the end type of the test instance. The object 
                 is used together with nqaScheduleEndTime."
            DEFVAL { 0 }
            ::= { nqaScheduleEntry 3 }
        
        nqaScheduleEndTime OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the end time of the test instance."
            ::= { nqaScheduleEntry 4 }
            
        nqaScheduleAgeTime OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the aging time of the test instance, the time 
                 how long the test instance will be reserved in NQA, after 
                 the test instance is inactive. Setting the value to 0 
                 indicates reserving the test instance eternally."
            ::= { nqaScheduleEntry 5 }
                    
        nqaScheduleElapsedTime OBJECT-TYPE
            SYNTAX TimeInterval
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The elapsed time (the period of time after task dispatch)."
            ::= { nqaScheduleEntry 6 }
        
        nqaScheduleNumOfInitiations OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies the times that the test instance has been initialed. 
                 This object is used as the index of the result table, HTTP 
                 statistics table, jitter statistics table, FTP statistics 
                 table and history table."
            ::= { nqaScheduleEntry 7 }
         
        nqaScheduleLastFinishIndex OBJECT-TYPE
            SYNTAX Integer32 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies the latest finished test of the test instance, 
                 which is used as the index of the result table."
            ::= { nqaScheduleEntry 8 }
             
        nqaScheduleOperStatus OBJECT-TYPE
            SYNTAX INTEGER
                {
                reset(1),
                stop(2),
                restart(3),
                active(4),
                inactive(5)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the status of dispatching operation:
                 - reset: Clear all the records of the test instance.
                 - stop: Stop the test instance, if it's running.
                 - restart: Stop the test instance, if it's running, 
                  and restart the test instance immediately. 
                 - active: The test instance is running; the parameters 
                  of the test instances can't be changed. 
                 - inactive: The test instance doesn't be scheduled; 
                  the parameters of the test instances can be changed."
            ::= { nqaScheduleEntry 9 }
        nqaScheduleLastCollectIndex OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies the index of the latest collection statistics 
                 result record in the collection result table."
            ::= { nqaScheduleEntry 10 }
         nqaGroupTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaGroupEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Set group for a test."
            ::= { nqaCtrl 4 }
        
        nqaGroupEntry OBJECT-TYPE
            SYNTAX NqaGroupEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Set parameters in the nqaGroupTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName }
            ::= { nqaGroupTable 1 }
        
        NqaGroupEntry ::=
            SEQUENCE { 
                nqaGroupStatusType
                    INTEGER,
                nqaGroupPeriod
                    Integer32,
                nqaGroupLeaderOwnerIndex
                    SnmpAdminString,
                nqaGroupLeaderTestName
                    SnmpAdminString,
                nqaGroupMemberNum
                    Integer32,           
                nqaGroupMemberFree        
                    EnableValue
              }

        nqaGroupStatusType OBJECT-TYPE
            SYNTAX INTEGER
                {
                normal(1),
                leader(2),
                member(3)
                }
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the group status of the test instance. The 
                 value can be normal (1), leader (2) or member (3)."
            DEFVAL { 1 }
            ::= { nqaGroupEntry 1 }
        
        nqaGroupPeriod OBJECT-TYPE
            SYNTAX Integer32             
            UNITS "seconds"
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the interval during which all members in 
                 the specified group will be executed."     
            DEFVAL { 60 }
            ::= { nqaGroupEntry 2 }
        
        nqaGroupLeaderOwnerIndex OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the owner index of the group leader, if the 
                 test instance is a member of the group."
            ::= { nqaGroupEntry 3 }
        
        nqaGroupLeaderTestName OBJECT-TYPE
            SYNTAX SnmpAdminString
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Specifies the test name of the group leader, if the 
                 test instance is a member of the group."
            ::= { nqaGroupEntry 4 }
        
        nqaGroupMemberNum OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Specifies the number of member in the group."
            ::= { nqaGroupEntry 5 }
 
         nqaGroupMemberFree OBJECT-TYPE
            SYNTAX EnableValue
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Free all members in the group."
            ::= { nqaGroupEntry 6 }
        
        nqaServer OBJECT IDENTIFIER ::= { nqa 3 }
        
        nqaServerEnable OBJECT-TYPE
            SYNTAX EnableValue 
            MAX-ACCESS read-write
            STATUS current
            DESCRIPTION
                "Enables NQA server, which can be the echo server of TCP, 
                 UDP and jitter type test instance."
            ::= { nqaServer 1 }
        
        nqaTcpServerTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaTcpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of nqa tcp server 
                configuration information."
            ::= { nqaServer 2 }
        
        nqaTcpServerEntry OBJECT-TYPE
            SYNTAX NqaTcpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of nqa tcp server 
                configuration information.
                 
              This entry is included not only the tcp server ip address 
              but also the tcp server tcp port. If used for VPN it is also 
               included the VPN name."
            INDEX { 
            nqaTcpServerAddress,
            nqaTcpServerPort,
            nqaTcpServerVrfName
            }
            ::= { nqaTcpServerTable 1 }
        
        NqaTcpServerEntry ::=
            SEQUENCE { 
                nqaTcpServerAddressType
                    InetAddressType,
                nqaTcpServerAddress
                    InetAddress,     
                nqaTcpServerPort
                    InetPortNumber,
                nqaTcpServerVrfName
                    DisplayString,
                nqaTcpServerStatus
                    RowStatus
             }

        nqaTcpServerAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the address type of TCP server."
            DEFVAL { unknown }
            ::= { nqaTcpServerEntry 1 }
        
        nqaTcpServerAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                "Specifies the address of TCP server."                
            ::= { nqaTcpServerEntry 2 }
                      
        nqaTcpServerPort OBJECT-TYPE
            SYNTAX InetPortNumber
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                 "Specifies the port number on which TCP server is listening."
            ::= { nqaTcpServerEntry 3 }              
        nqaTcpServerVrfName OBJECT-TYPE
            SYNTAX      DisplayString(SIZE(1..31))
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                "Specifies the VRF (VPN Routing & Forwarding) instances name 
                 of the TCP server."
            ::= { nqaTcpServerEntry 4 }
        
        nqaTcpServerStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
               "Specifies the operation of the TCP server, creating or deleting."
           REFERENCE
              "See definition of RowStatus in RFC 2579, 'Textual
              Conventions for SMIv2.'"
            ::= { nqaTcpServerEntry 5 }
            
        nqaUdpServerTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaUdpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of nqa udp server 
                configuration information."
            ::= { nqaServer 3 }
        
        nqaUdpServerEntry OBJECT-TYPE
            SYNTAX NqaUdpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of nqa udp server 
                configuration information.
 
                This entry is includeed not only the udp server ip address 
                but also the udp server udp port.If used for VPN it is also 
                included the VPN name."
            INDEX { 
            nqaUdpServerAddress,
            nqaUdpServerPort,
            nqaUdpServerVrfName
            }
            ::= { nqaUdpServerTable 1 }
        
        NqaUdpServerEntry ::=
            SEQUENCE { 
                nqaUdpServerAddressType
                    InetAddressType,
                nqaUdpServerAddress
                    InetAddress,     
                nqaUdpServerPort
                    InetPortNumber,
                nqaUdpServerVrfName
                    DisplayString,
                nqaUdpServerStatus
                    RowStatus
             }    
        
        nqaUdpServerAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the address type of UDP server."
            DEFVAL { unknown }
            ::= { nqaUdpServerEntry 1 }
        
        nqaUdpServerAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                "Specifies the address of UDP server."                
            ::= { nqaUdpServerEntry 2 }
                      
        nqaUdpServerPort OBJECT-TYPE
            SYNTAX InetPortNumber
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                 "Specifies the port number on which UDP server is listening."
            ::= { nqaUdpServerEntry 3 }              
        nqaUdpServerVrfName OBJECT-TYPE
            SYNTAX      DisplayString(SIZE(1..31))
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                "Specifies the VRF (VPN Routing & Forwarding) instances name 
                 of the UDP server."
            ::= { nqaUdpServerEntry 4 }
        
        nqaUdpServerStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
               "Specifies the operation of the UDP server, creating or deleting."
           REFERENCE
              "See definition of RowStatus in RFC 2579, 'Textual
              Conventions for SMIv2.'"                
            ::= { nqaUdpServerEntry 5 }
            
        nqaIcmpServerTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaIcmpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of nqa ICMP server 
                configuration information."
            ::= { nqaServer 4 }
        
        nqaIcmpServerEntry OBJECT-TYPE
            SYNTAX NqaIcmpServerEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of nqa ICMP server 
                configuration information.
 
                This entry is included the ICMP server IP address.
                If used for VPN it is also included the VPN name."
            INDEX { 
            nqaIcmpServerAddress,
            nqaIcmpServerVrfName
            }
            ::= { nqaIcmpServerTable 1 }
        
        NqaIcmpServerEntry ::=
            SEQUENCE { 
                nqaIcmpServerAddress
                    InetAddress,     
                nqaIcmpServerVrfName
                    DisplayString,
                nqaIcmpServerAddressType
                    InetAddressType,
                 nqaIcmpServerStatus
                    RowStatus
             }    

        nqaIcmpServerAddress OBJECT-TYPE
            SYNTAX      InetAddress
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                "Specifies the address of ICMP server."                
            ::= { nqaIcmpServerEntry 1 }                      
            
        nqaIcmpServerVrfName OBJECT-TYPE
            SYNTAX      DisplayString(SIZE(1..31))
            MAX-ACCESS  not-accessible
            STATUS current
            DESCRIPTION
                "Specifies the VRF (VPN Routing & Forwarding) instances name 
                 of the ICMP server."
            ::= { nqaIcmpServerEntry 2 }

        
        nqaIcmpServerAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
                "Specifies the address type of ICMP server."
            DEFVAL { unknown }
            ::= { nqaIcmpServerEntry 3 }
        
        
        nqaIcmpServerStatus OBJECT-TYPE
            SYNTAX RowStatus
            MAX-ACCESS read-create
            STATUS current
            DESCRIPTION
               "Specifies the operation of the ICMP server, creating or deleting."
           REFERENCE
              "See definition of RowStatus in RFC 2579, 'Textual
              Conventions for SMIv2.'"                
            ::= { nqaIcmpServerEntry 51 }            
            
            
            
                    
        nqaStats OBJECT IDENTIFIER ::= { nqa 4 }
        
        nqaResultsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaResultsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines the test Results Extend Table for providing
              the capability of performing test operations at
              a remote host.  The results of these operations are
              stored in the nqaResultsTable . The operation of this 
              table is same as that of nqaResultsTable."
            ::= { nqaStats 1 }
        
        nqaResultsEntry OBJECT-TYPE
            SYNTAX NqaResultsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The operation of this table is same as that of nqaResultsTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaResultsIndex, nqaResultsHopIndex }
            ::= { nqaResultsTable 1 }
        
        NqaResultsEntry ::=
            SEQUENCE { 
                nqaResultsIndex
                    Integer32,
                nqaResultsHopIndex
                    Integer32,
                nqaResultsCompletions
                    INTEGER,
                nqaResultsTestAttempts
                    Counter32,
                nqaResultsCurHopCount
                    Gauge32,
                nqaResultsCurProbeCount
                    Gauge32,
                nqaResultsRTDOverThresholds
                    Counter32,
                nqaResultsSumCompletionTime
                    Counter32,
                nqaResultsSumCompletionTime2Low
                    Counter32,
                nqaResultsSumCompletionTime2High
                    Counter32,
                nqaResultsCompletionTimeMin
                    Gauge32,
                nqaResultsCompletionTimeMax
                    Gauge32,
                nqaResultsDisconnects
                    Counter32,
                nqaResultsTimeouts
                    Counter32,
                nqaResultsBusies
                    Counter32,
                nqaResultsNoConnections
                    Counter32,
                nqaResultsSequenceErrors
                    Counter32,
                nqaResultsDrops
                    Counter32,
                nqaResultsAddressType
                    InetAddressType,
                nqaResultsAddress
                    InetAddress,
                nqaResultsProbeResponses
                    Counter32,
                nqaResultsSentProbes
                    Counter32,
                nqaResultsLastGoodProbe
                    DateAndTime,
                nqaResultsLastGoodPath
                    DateAndTime,
                nqaResultsTestFinished
                    INTEGER ,
                nqaResultsRttAvg
                Gauge32,
                nqaResultsLostPacketRatio
                    Gauge32                     
             }

        nqaResultsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The result table index and times of dispatching dependency tests. 
                Each test can only reserve 5 records. The result table only contains 
                icmp/dns/dlsw/lspPing/Traceroute/LSP Traceroute/tcp/udp/snmp/dhcp Traceroute information. "
            ::= { nqaResultsEntry 1 }
        
        nqaResultsHopIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hop index. If the test type is Traceroute or LSP Traceroute, 
                the entry only defines one hop. For other types, the default value is 1."
            ::= { nqaResultsEntry 2 }
        
        nqaResultsCompletions OBJECT-TYPE
            SYNTAX INTEGER
                {
                   noResult(0),
   success(1),
           failure(2)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test."
            ::= { nqaResultsEntry 3 }
        
        nqaResultsTestAttempts OBJECT-TYPE
            SYNTAX      Counter32
            UNITS       "tests"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
               "The times of executing tests, which includes the times of successful execution, 
               failed execution and interruption (interrupted by human or system).
               The current number of test attempts. The value of this object MUST be started at 0."
            ::= { nqaResultsEntry 4 }

        nqaResultsCurHopCount OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "hops"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
               "It is equal to the hop count index executed
                by current Traceroute or LSP Traceroute tests. 
                Ping/LSP Ping does not use this entry. The default value is 0."
            ::= { nqaResultsEntry 5 }

        nqaResultsCurProbeCount OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "probes"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
               "The total count of probe packets of Traceroute 
               or LSP Traceroute test in a hop count index."
            ::= { nqaResultsEntry 6 }       
        
        nqaResultsRTDOverThresholds OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of successful statistics test RTD (Round Trip Delay)
                 over the threshold."
            ::= { nqaResultsEntry 7 }
        
        nqaResultsSumCompletionTime OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of RTT (response time) of all packets of each hop for executing Traceroute,
                 LSP Traceroute tests. The sum of RTT (response time) of sent packets for executing ping, 
                 lsp ping and disman-ping tests and so on."
            ::= { nqaResultsEntry 8 }
        
        nqaResultsSumCompletionTime2Low OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The low order 32 bits of the accumulated squares 
                 of completion times (in milliseconds) of NQA 
                 operations which complete successfully.
  
                 Low/High order is defined where the binary number
                 will look as follows:
                 -------------------------------------------------
                 | High order 32 bits    | Low order 32 bits     |
                 -------------------------------------------------
                 For example the number 4294967296 would have all
                 Low order bits as '0' and the rightmost High
                 order bit will be 1 (zeros,1).
                "
            ::= { nqaResultsEntry 9 }
        
        nqaResultsSumCompletionTime2High OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The high order 32 bits of the accumulated squares 
                 of completion times (in milliseconds) of NQA 
                 operations which complete successfully.
  
                 See the nqaResultsSumCompletionTime2Low object
                 for a definition of Low/High Order.
                "
            ::= { nqaResultsEntry 10 }
        
        nqaResultsCompletionTimeMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum RTT (for Traceroute and LSP Traceroute,
                 it is the minimum RTT among all the packet of next hop) of all packets for executing a test. "
            ::= { nqaResultsEntry 11 }
        
        nqaResultsCompletionTimeMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum RTT (for Traceroute and LSP Traceroute, 
                it is the maximum RTT among all the packets of next hop) of all packets in executing a test."
            ::= { nqaResultsEntry 12 }
        
        nqaResultsDisconnects OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of consecutive failures. For connectionless protocol type of test,this number should be zero."
            ::= { nqaResultsEntry 13 }
        
        nqaResultsTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a NQA operation was not
                 completed before a timeout occurred, i.e.
                 nqaAdminCtrlTimeOut was exceeded."
            ::= { nqaResultsEntry 14 }
        
        nqaResultsBusies OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of failure to apply resource due to the busy system.
                such as apply memory or create socket failure."
            ::= { nqaResultsEntry 15 }
        
        nqaResultsNoConnections OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a NQA operation could not be
                 initiated because the connection to the target has not 
                 been established. For all other nqaAdminCtrlType this
                 object will remain zero.
  
                 This cannot occur for connectionless protocols, but may
                 occur for connection oriented protocols, such as TCP.
  
                 Since a NQA operation was never initiated, the completion
                 time of these operations are not accumulated, nor do they
                 increment nqaResultsCompletions. 
                "
            ::= { nqaResultsEntry 16 }
        
        nqaResultsSequenceErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of NQA operation completions received with 
                 an unexpected sequence identifier.  For all other values
                 of nqaAdminCtrlType this object will remain zero.
  
                 When this has occurred some of the possible reasons maybe:  
                    - a duplicate packet was received
                    - a response was received after it had timed-out
                    - a corrupted packet was received and was not detected
                "
            ::= { nqaResultsEntry 17 }
        
        nqaResultsDrops OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times probes of the test dropped due to sending or connecting failure."
            ::= { nqaResultsEntry 18 }
            
        nqaResultsAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address type, and the value range can be unknown(0), 
                ipv4(1) and dns(16)."
            ::= { nqaResultsEntry 19 }
        
        nqaResultsAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address (TraceRoute is the destination address of each hop,
                 and ping is the destination address of tests)."
            ::= { nqaResultsEntry 20 }
            
        nqaResultsProbeResponses OBJECT-TYPE
            SYNTAX      Counter32
            UNITS       "responses"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Number of responses received for the corresponding
                 test.  The value of this object
                MUST be reported as 0 when no probe responses have been
                received."
            ::= { nqaResultsEntry 21 }        
        
        nqaResultsSentProbes OBJECT-TYPE
            SYNTAX      Counter32
            UNITS       "probes"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The value of this object reflects the number of probes sent
                for the corresponding test.
                The value of this object MUST be reported as 0 when no probes
                have been sent."
            ::= { nqaResultsEntry 22 }      
                       
        nqaResultsLastGoodProbe OBJECT-TYPE
            SYNTAX      DateAndTime
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Date and time when the last response was received for a probe."
            ::= { nqaResultsEntry 23 }  
              
        nqaResultsLastGoodPath OBJECT-TYPE
            SYNTAX      DateAndTime
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The date and time when the last complete path was determined."
            ::= { nqaResultsEntry 24 }    

        nqaResultsTestFinished OBJECT-TYPE
            SYNTAX INTEGER
                {
           noFinish(0),
                   finish(1)
              }
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The status of test execution."
            ::= { nqaResultsEntry 25 }                    

        nqaResultsRttAvg OBJECT-TYPE 
            SYNTAX Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of RTTs that were successfully measured by tests."
            ::= { nqaResultsEntry 26}  

          nqaResultsLostPacketRatio OBJECT-TYPE
            SYNTAX Gauge32  
    MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of the packets lost to all packets sent in the test."
            ::= { nqaResultsEntry 27 }                    

        nqaHTTPStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaHTTPStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The statistics collection database.
                 Defines the HTTP Operations Statistics Table for
          storing the statistics of a HTTP test."
            ::= { nqaStats 2 }
        
        nqaHTTPStatsEntry OBJECT-TYPE
            SYNTAX NqaHTTPStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the nqaHTTPStatsTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaHTTPStatsIndex }
            ::= { nqaHTTPStatsTable 1 }

        NqaHTTPStatsEntry ::=
            SEQUENCE { 
                nqaHTTPStatsIndex
                    Integer32,
                nqaHTTPStatsCompletions
                    INTEGER,
                nqaHTTPStatsRTDOverThresholds
                    Counter32,
                nqaHTTPStatsRTTSum
                    Counter32,
                nqaHTTPStatsRTTMin
                    Gauge32,
                nqaHTTPStatsRTTMax
                    Gauge32,
                nqaHTTPStatsDNSRTTSum
                    Counter32,                           
                nqaHTTPStatsDNSRTTMin
                    Gauge32,                        
                nqaHTTPStatsDNSRTTMax
                    Gauge32,
                nqaHTTPStatsTCPConnectRTTSum
                    Counter32,       
                nqaHTTPStatsTCPConnectRTTMin    
                    Gauge32,                           
                nqaHTTPStatsTCPConnectRTTMax    
                    Gauge32,
                nqaHTTPStatsTransactionRTTSum                  
                    Counter32,                                          
                nqaHTTPStatsTransactionRTTMin                  
                    Gauge32,       
                nqaHTTPStatsTransactionRTTMax                  
                    Gauge32,
                nqaHTTPStatsMessageBodyOctetsSum
                    Counter32,
                nqaHTTPStatsDNSServerTimeouts
                    Counter32,
                nqaHTTPStatsTCPConnectTimeouts
                    Counter32,
                nqaHTTPStatsTransactionTimeouts
                    Counter32,
                nqaHTTPStatsDNSQueryErrors
                    Counter32,
                nqaHTTPStatsErrors
                    Counter32,
                nqaHTTPStatsTcpConnErrors
                    Counter32,
                nqaHTTPStatsProbeResponses
                    Counter32,                    
                nqaHTTPStatsSendProbes
                    Counter32,
                nqaHTTPStatsBusies
                    Counter32,                                               
                nqaHTTPStatsTestFinished
                    INTEGER,
                nqaHTTPStatsRttAvg
                     Gauge32,
                nqaHTTPStatsLostPacketRatio
                     Gauge32
             }

        nqaHTTPStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The The HTTP statistics table index and times of dispatching dependency tests."
            ::= { nqaHTTPStatsEntry 1 }
        
        nqaHTTPStatsCompletions OBJECT-TYPE
            SYNTAX INTEGER
                {
                   noResult(0),
   success(1),
           failure(2)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of HTTP test."
            ::= { nqaHTTPStatsEntry 2 }
        
        nqaHTTPStatsRTDOverThresholds OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of HTTP operations RTD (Round Trip Delay) that violate threshold."
            ::= { nqaHTTPStatsEntry 3 }
        
        nqaHTTPStatsRTTSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of delay time of successful HTTP probes, including the sum of time of DNS query, 
                TCP establishment and packets transmission."
            ::= { nqaHTTPStatsEntry 4 }
        
        nqaHTTPStatsRTTMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum value of HTTP probe time."
            ::= { nqaHTTPStatsEntry 5 }
        
        nqaHTTPStatsRTTMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum value of HTTP probe time."
            ::= { nqaHTTPStatsEntry 6 }
        
        nqaHTTPStatsDNSRTTSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of DNS query time."
            ::= { nqaHTTPStatsEntry 7 }
        
        nqaHTTPStatsDNSRTTMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of DNS query time."
            ::= { nqaHTTPStatsEntry 8 }
        
        nqaHTTPStatsDNSRTTMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of DNS query time."
            ::= { nqaHTTPStatsEntry 9 }
        
        nqaHTTPStatsTCPConnectRTTSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of TCP connection time."
            ::= { nqaHTTPStatsEntry 10 }
        
        nqaHTTPStatsTCPConnectRTTMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum value of TCP connection time."
            ::= { nqaHTTPStatsEntry 11 }
        
        nqaHTTPStatsTCPConnectRTTMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum value of TCP connection time."
            ::= { nqaHTTPStatsEntry 12 }               
            
        nqaHTTPStatsTransactionRTTSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of transaction time."
            ::= { nqaHTTPStatsEntry 13 }
        
        nqaHTTPStatsTransactionRTTMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum value of transaction time."
            ::= { nqaHTTPStatsEntry 14 }
        
        nqaHTTPStatsTransactionRTTMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum value of transaction time."
            ::= { nqaHTTPStatsEntry 15 }   
            
        nqaHTTPStatsMessageBodyOctetsSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of the size of the message body received as a 
                 response to the HTTP request."
            ::= { nqaHTTPStatsEntry 16 }

        
        nqaHTTPStatsDNSServerTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of requests that timed out during DNS query."
            ::= { nqaHTTPStatsEntry 17 }
        
        nqaHTTPStatsTCPConnectTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of requests that timed out during HTTP connection."
            ::= { nqaHTTPStatsEntry 18 }
        
        nqaHTTPStatsTransactionTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of requests that timed out during HTTP transaction."
            ::= { nqaHTTPStatsEntry 19 }
        
        nqaHTTPStatsDNSQueryErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of requests that had DNS query errors."
            ::= { nqaHTTPStatsEntry 20 }
        
        nqaHTTPStatsErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of HTTP transaction failures."
            ::= { nqaHTTPStatsEntry 21 }
        
        nqaHTTPStatsTcpConnErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of TCP connection failures."
            ::= { nqaHTTPStatsEntry 22 }
            
        nqaHTTPStatsProbeResponses OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Number of responses received for the corresponding test. The value of this object
                MUST be reported as 0 when no probe responses have been received."
            ::= { nqaHTTPStatsEntry 23 }
                        
        nqaHTTPStatsSendProbes OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of sending probes."
            ::= { nqaHTTPStatsEntry 24 }
                    
        nqaHTTPStatsBusies OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a HTTP operation could not
                  be initiated because an internal error."
            ::= { nqaHTTPStatsEntry 25 }       
            
         nqaHTTPStatsTestFinished OBJECT-TYPE
            SYNTAX INTEGER
                {
          noFinish(0),
          finish(1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test execution."
            ::= { nqaHTTPStatsEntry 26 }

          nqaHTTPStatsRttAvg OBJECT-TYPE 
            SYNTAX Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of RTTs that were successfully measured by tests."
            ::= { nqaHTTPStatsEntry  27}  

          nqaHTTPStatsLostPacketRatio OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS  read-only
               STATUS      current
            DESCRIPTION
                "The ratio of the packets lost to all packets sent in the test."
            ::= { nqaHTTPStatsEntry  28 }  
            
        nqaJitterStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaJitterStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of latest Jitter operation."
            ::= { nqaStats 3 }
        
        nqaJitterStatsEntry OBJECT-TYPE
            SYNTAX NqaJitterStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of latest Jitter operation.
               This entry is created only if the nqaAdminCtrlType is jitterAppl."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaJitterStatsIndex }
            ::= { nqaJitterStatsTable 1 }
        
        NqaJitterStatsEntry ::=
            SEQUENCE { 
                nqaJitterStatsIndex
                    Integer32,
                nqaJitterStatsCompletions
                    INTEGER,
                nqaJitterStatsRTDOverThresholds
                    Counter32,
                nqaJitterStatsNumOfRTT
                    Counter32,
                nqaJitterStatsRTTSum
                    Counter32,
                nqaJitterStatsRTTSum2Low
                    Counter32,
                nqaJitterStatsRTTSum2High
                    Counter32,
                nqaJitterStatsRTTMin
                    Gauge32,
                nqaJitterStatsRTTMax
                    Gauge32,
                nqaJitterStatsMinOfPositivesSD
                    Gauge32,
                nqaJitterStatsMaxOfPositivesSD
                    Gauge32,
                nqaJitterStatsNumOfPositivesSD
                    Counter32,
                nqaJitterStatsSumOfPositivesSD
                    Counter32,
                nqaJitterStatsSum2OfPositivesSDLow
                    Counter32,
                nqaJitterStatsSum2OfPositivesSDHigh
                    Counter32,
                nqaJitterStatsMinOfNegativesSD
                    Gauge32,
                nqaJitterStatsMaxOfNegativesSD
                    Gauge32,
                nqaJitterStatsNumOfNegativesSD
                    Counter32,
                nqaJitterStatsSumOfNegativesSD
                    Counter32,
                nqaJitterStatsSum2OfNegativesSDLow
                    Counter32,
                nqaJitterStatsSum2OfNegativesSDHigh
                    Counter32,
                nqaJitterStatsMinOfPositivesDS
                    Gauge32,
                nqaJitterStatsMaxOfPositivesDS
                    Gauge32,
                nqaJitterStatsNumOfPositivesDS
                    Counter32,
                nqaJitterStatsSumOfPositivesDS
                    Counter32,
                nqaJitterStatsSum2OfPositivesDSLow
                    Counter32,
                nqaJitterStatsSum2OfPositivesDSHigh
                    Counter32,
                nqaJitterStatsMinOfNegativesDS
                    Gauge32,
                nqaJitterStatsMaxOfNegativesDS
                    Gauge32,
                nqaJitterStatsNumOfNegativesDS
                    Counter32,
                nqaJitterStatsSumOfNegativesDS
                    Counter32,
                nqaJitterStatsSum2OfNegativesDSLow
                    Counter32,
                nqaJitterStatsSum2OfNegativesDSHigh
                    Counter32,
                nqaJitterStatsPacketLossSD
                    Counter32,
                nqaJitterStatsPacketLossDS
                    Counter32,
                nqaJitterStatsPacketOutOfSequences
                    Counter32,
                nqaJitterStatsErrors
                    Counter32,
                nqaJitterStatsBusies
                    Counter32,   
                nqaJitterStatsTimeouts
                    Counter32,   
                nqaJitterStatsProbeResponses
                    Counter32,   
                nqaJitterStatsSentProbes
                    Counter32,   
                nqaJitterStatsDrops
                    Counter32,                                                 
                nqaJitterStatsTestFinished
                    INTEGER,
                nqaJitterStatsMaxDelaySD
                    Gauge32,
                nqaJitterStatsMaxDelayDS
                    Gauge32,                        
                nqaJitterStatsRTTAvg
                    Gauge32,
                nqaJitterStatsPacketLossRatio
                    Gauge32,
                nqaJitterStatsAvgJitter
                    Gauge32,
                nqaJitterStatsAvgJitterSD   
                    Gauge32,
                nqaJitterStatsAvgJitterDS
                    Gauge32,
                nqaJitterStatsJitterOut
                    OCTET STRING,
                nqaJitterStatsJitterIn      
                    OCTET STRING,
                nqaJitterStatsOWDOverThresholdsSD
                    Counter32,
                nqaJitterStatsPktLossUnknown
                    Counter32,
                nqaJitterStatsNumOfOWD
                    Counter32,
                nqaJitterStatsOWSumSD
                    Counter32,
                nqaJitterStatsOWSumDS
                    Counter32,                    
                nqaJitterStatsOWDOverThresholdsDS
                    Counter32,
                nqaJitterStatsOperOfIcpif
                    Gauge32,
                nqaJitterStatsOperOfMos
                    Gauge32,
                nqaJitterStatsMinDelaySD
                    Gauge32,
                nqaJitterStatsSum2DelaySDLow
                    Gauge32,
                nqaJitterStatsSum2DelaySDHigh
                    Counter32,
                nqaJitterStatsMinDelayDS 
                    Gauge32,
                nqaJitterStatsSum2DelayDSLow
                    Gauge32,
                nqaJitterStatsSum2DelayDSHigh 
                    Counter32,
                nqaJitterStatsTimeUnit
                    INTEGER,
                nqaJitterStatsAvgDelaySD
                    Gauge32,
                nqaJitterStatsAvgDelayDS
                    Gauge32,
                nqaJitterStatsPktRewriteNum
                    Counter32,
                nqaJitterStatsPktRewriteRatio
                    Gauge32,
                nqaJitterStatsPktDisorderNum
                    Counter32,
                nqaJitterStatsPktDisorderRatio
                    Gauge32,
                nqaJitterStatsFragPktDisorderNum
                    Counter32,
                nqaJitterStatsFragPktDisorderRatio
                    Gauge32
             }

        nqaJitterStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The third index of jitter statistics table."
            ::= { nqaJitterStatsEntry 1 }
        
        nqaJitterStatsCompletions OBJECT-TYPE
            SYNTAX INTEGER
                {
   noResult(0),
   success(1),
          failure(2),
           negotiateFailed(3)   
        }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The result of the test, which value can be noResult (0), 
                 success (1), failure (2) or negotiateFailed (3). If the test is still running, 
                 the value will be noResult (0)."
            ::= { nqaJitterStatsEntry 2 }
        
        nqaJitterStatsRTDOverThresholds OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate RTD (Round Trip Delay) threshold."
            ::= { nqaJitterStatsEntry 3 }
        
        nqaJitterStatsNumOfRTT OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of RTTs that are successfully measured by tests."
            ::= { nqaJitterStatsEntry 4 }
        
        nqaJitterStatsRTTSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of RTTs that are successfully measured."
            ::= { nqaJitterStatsEntry 5 }
        
        nqaJitterStatsRTTSum2Low OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of RTTs that are successfully measured by 
                 tests (low order 32 bits)."
            ::= { nqaJitterStatsEntry 6 }
        
        nqaJitterStatsRTTSum2High OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of RTTs that are successfully measured by 
                 tests (high order 32 bits)."
            ::= { nqaJitterStatsEntry 7 }
        
        nqaJitterStatsRTTMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of RTTs that were successfully measured by tests."
            ::= { nqaJitterStatsEntry 8 }
        
        nqaJitterStatsRTTMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of RTTs that were successfully measured by tests."
            ::= { nqaJitterStatsEntry 9 }
        
        nqaJitterStatsMinOfPositivesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all positive jitter values from source to destination."
            ::= { nqaJitterStatsEntry 10 }
        
        nqaJitterStatsMaxOfPositivesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all positive jitter values from source to destination."
            ::= { nqaJitterStatsEntry 11 }
        
        nqaJitterStatsNumOfPositivesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all positive jitter values from source to destination."
            ::= { nqaJitterStatsEntry 12 }
        
        nqaJitterStatsSumOfPositivesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all positive jitter values from source to destination."
            ::= { nqaJitterStatsEntry 13 }
        
        nqaJitterStatsSum2OfPositivesSDLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter values 
                 from source to destination (low order 32 bits)."
            ::= { nqaJitterStatsEntry 14 }
        
        nqaJitterStatsSum2OfPositivesSDHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter values 
                 from source to destination (high order 32 bits)."
            ::= { nqaJitterStatsEntry 15 }
        
        nqaJitterStatsMinOfNegativesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all negative jitter values from 
                 source to destination."
            ::= { nqaJitterStatsEntry 16 }
        
        nqaJitterStatsMaxOfNegativesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all negative jitter values from 
                 source to destination."
            ::= { nqaJitterStatsEntry 17 }
        
        nqaJitterStatsNumOfNegativesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all negative jitter values from source to destination."
            ::= { nqaJitterStatsEntry 18 }
        
        nqaJitterStatsSumOfNegativesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all negative jitter values from source 
                 to destination."
            ::= { nqaJitterStatsEntry 19 }
        
        nqaJitterStatsSum2OfNegativesSDLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values from source 
                 to destination (low order 32 bits)."
            ::= { nqaJitterStatsEntry 20 }
        
        nqaJitterStatsSum2OfNegativesSDHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values from source to 
                 destination (high order 32 bits)."
            ::= { nqaJitterStatsEntry 21 }
        
        nqaJitterStatsMinOfPositivesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all positive jitter values from destination 
                 to source."
            ::= { nqaJitterStatsEntry 22 }
        
        nqaJitterStatsMaxOfPositivesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all positive jitter values from destination 
                 to source."
            ::= { nqaJitterStatsEntry 23 }
        
        nqaJitterStatsNumOfPositivesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all positive jitter values from destination to source."
            ::= { nqaJitterStatsEntry 24 }
        
        nqaJitterStatsSumOfPositivesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all positive jitter values from destination 
                 to source."
            ::= { nqaJitterStatsEntry 25 }
        
        nqaJitterStatsSum2OfPositivesDSLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter values from 
                 destination to source (low order 32 bits)."
            ::= { nqaJitterStatsEntry 26 }
        
        nqaJitterStatsSum2OfPositivesDSHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter values from 
                 destination to source (high order 32 bits)."
            ::= { nqaJitterStatsEntry 27 }
        
        nqaJitterStatsMinOfNegativesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all negative jitter values from 
                 destination to source."
            ::= { nqaJitterStatsEntry 28 }
        
        nqaJitterStatsMaxOfNegativesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all negative jitter values from 
                 destination to source."
            ::= { nqaJitterStatsEntry 29 }
        
        nqaJitterStatsNumOfNegativesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all negative jitter values from destination to source."
            ::= { nqaJitterStatsEntry 30 }
        
        nqaJitterStatsSumOfNegativesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all negative jitter values from destination 
                 to source."
            ::= { nqaJitterStatsEntry 31 }
        
        nqaJitterStatsSum2OfNegativesDSLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values from destination 
                 to source (low order 32 bits)."
            ::= { nqaJitterStatsEntry 32 }
        
        nqaJitterStatsSum2OfNegativesDSHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values from destination 
                 to source (high order 32 bits)."
            ::= { nqaJitterStatsEntry 33 }
        
        nqaJitterStatsPacketLossSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets lost when sent from source to destination."
            ::= { nqaJitterStatsEntry 34 }
        
        nqaJitterStatsPacketLossDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets lost when sent from destination to source."
            ::= { nqaJitterStatsEntry 35 }
        
        nqaJitterStatsPacketOutOfSequences OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets arrived out of sequence."
            ::= { nqaJitterStatsEntry 36 }
        
        nqaJitterStatsErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of errors occurred in the test."
            ::= { nqaJitterStatsEntry 37 }
        
        nqaJitterStatsBusies OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a test couldn't be initialized 
                 because the previous test has not completed."
            ::= { nqaJitterStatsEntry 38 }      
            
        nqaJitterStatsTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of responses arrived over the time."
            ::= { nqaJitterStatsEntry 39 }    
            
        nqaJitterStatsProbeResponses OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of responses from echo-server for the packets sent 
                 by the test."
            ::= { nqaJitterStatsEntry 40 }    
            
        nqaJitterStatsSentProbes OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets sent in the test."
            ::= { nqaJitterStatsEntry 41 }    
            
        nqaJitterStatsDrops OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets that were sent failed in the test."
            ::= { nqaJitterStatsEntry 42 }                
                                                                                               
        nqaJitterStatsTestFinished OBJECT-TYPE
            SYNTAX INTEGER
                {
          noFinish(0),
          finish(1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of the test, which value can be noFinish (0) or finish (1)."
            ::= { nqaJitterStatsEntry 43  }         
        
        nqaJitterStatsMaxDelaySD OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The maximum of all OWD (One Way Delay) from source to destination."
            ::= { nqaJitterStatsEntry 44 }
    
        nqaJitterStatsMaxDelayDS OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The maximum of all OWD (One Way Delay) from destination to source."
            ::= { nqaJitterStatsEntry 45 }                       
            
         nqaJitterStatsRTTAvg  OBJECT-TYPE
            SYNTAX   Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of RTTs that were successfully measured by tests."
            ::= { nqaJitterStatsEntry 46  }  

         nqaJitterStatsPacketLossRatio  OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The ratio of the packets lost to all packets sent in the test."
            ::= { nqaJitterStatsEntry 47  }  

         nqaJitterStatsAvgJitter   OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of jitter values that were successfully measured by tests."
            ::= { nqaJitterStatsEntry 48  }  

         nqaJitterStatsAvgJitterSD   OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of jitter values from source to destination that were 
                 successfully measured by tests."
            ::= { nqaJitterStatsEntry 49  }  

         nqaJitterStatsAvgJitterDS   OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of jitter values from destination to source that were 
                 successfully measured by tests."
            ::= { nqaJitterStatsEntry 50  }  

        nqaJitterStatsJitterOut OBJECT-TYPE
            SYNTAX      OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Jitter (RFC1889) at responder."
            ::= { nqaJitterStatsEntry 51 }
                       
        nqaJitterStatsJitterIn OBJECT-TYPE
            SYNTAX      OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Jitter (RFC1889) at sender."
            ::= { nqaJitterStatsEntry 52 }
            
        nqaJitterStatsOWDOverThresholdsSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate OWD (One Way Delay) threshold from 
                 source to destination."
            ::= { nqaJitterStatsEntry 53 }                    
                                                                                                    
        nqaJitterStatsPktLossUnknown OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets lost for which we can't determine the direction."
            ::= { nqaJitterStatsEntry 54 }
        nqaJitterStatsNumOfOWD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of OWDs that were successfully measured by tests."
            ::= { nqaJitterStatsEntry 55 }
        nqaJitterStatsOWSumSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of OWDs that were successfully measured by tests from source 
                 to destination."
            ::= { nqaJitterStatsEntry 56 }
        nqaJitterStatsOWSumDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of OWDs that were successfully measured by tests from 
                 destination to source."
            ::= { nqaJitterStatsEntry 57 }   
        nqaJitterStatsOWDOverThresholdsDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate OWD (One Way Delay) threshold 
                 from destination to source."
            ::= { nqaJitterStatsEntry 58 } 
                
        nqaJitterStatsOperOfIcpif OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The ICPIF (Calculated Planning Impairment Factor) value for the latest
                jitter test."
            ::= { nqaJitterStatsEntry 59 }    
            
        nqaJitterStatsOperOfMos OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The MOS (Mean Opinion Score) value for the latest jitter test. "
            ::= { nqaJitterStatsEntry 60}

        nqaJitterStatsMinDelaySD OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The minimum of all one way time from source to destination."
            ::= { nqaJitterStatsEntry 61 }  
            
        nqaJitterStatsSum2DelaySDLow OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The low order 32 bits of the sum of squares of one way 
                time from source to destination."
            ::= { nqaJitterStatsEntry 62 }

        nqaJitterStatsSum2DelaySDHigh OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The high order 32 bits of the sum of squares of one 
                way time from source to destination."
            ::= { nqaJitterStatsEntry 63 }          
            
        nqaJitterStatsMinDelayDS OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The minimum of all one way time from destination to source."
            ::= { nqaJitterStatsEntry 64 }     
            
        nqaJitterStatsSum2DelayDSLow OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The low order 32 bits of the sum of squares of one way time
                 from destination to source."
            ::= { nqaJitterStatsEntry 65 }

        nqaJitterStatsSum2DelayDSHigh OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The low order 32 bits of the sum of squares of one way time from destination to source."
            ::= { nqaJitterStatsEntry 66 }
            
        nqaJitterStatsTimeUnit OBJECT-TYPE            
            SYNTAX      INTEGER
            {
                    us(1),       -- The unit of the timestamp is us
                    ms(2)        -- The unit of the timestamp is ms
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The unit of the timestamp, ms or us."
            ::= { nqaJitterStatsEntry 67 }

        nqaJitterStatsAvgDelaySD OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of one way time from source to destination."
            ::= { nqaJitterStatsEntry 68 }     

        nqaJitterStatsAvgDelayDS OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of one way time from destination to source."
            ::= { nqaJitterStatsEntry 69 }     

        nqaJitterStatsPktRewriteNum OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of packets have been rewrited."
            ::= { nqaJitterStatsEntry 70 }    
          
        nqaJitterStatsPktRewriteRatio OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of packets have been rewrited."
            ::= { nqaJitterStatsEntry 71 }    
                    
        nqaJitterStatsPktDisorderNum OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of packets have been disordered."
            ::= { nqaJitterStatsEntry 72 }    
                        
        nqaJitterStatsPktDisorderRatio OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of packets have been disordered."
            ::= { nqaJitterStatsEntry 73 }    
                                                
        nqaJitterStatsFragPktDisorderNum OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of packets whose fragments have been disordered."
            ::= { nqaJitterStatsEntry 74 }    
                                        
        nqaJitterStatsFragPktDisorderRatio OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of packets whose fragments have been disordered."
            ::= { nqaJitterStatsEntry 75 }    
                                                                                                                                         
        nqaFTPStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaFTPStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The statistics collection database.
                 Defines the FTP Operations Statistics Table for
          storing the statistics of a FTP test.
                "
            ::= { nqaStats 4 }

        
        nqaFTPStatsEntry OBJECT-TYPE
            SYNTAX NqaFTPStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the nqaFTPStatsTable. "
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaFTPStatsIndex }
            ::= { nqaFTPStatsTable 1 }
        
        NqaFTPStatsEntry ::=
            SEQUENCE { 
                nqaFTPStatsIndex
                    Integer32,
                nqaFTPStatsCompletions
                    INTEGER,
                nqaFTPStatsRTDOverThresholds
                    Counter32,
                nqaFTPStatsCtrlConnMaxTime                  
                    Gauge32,                    
                nqaFTPStatsCtrlConnMinTime                  
                    Gauge32,                    
                nqaFTPStatsCtrlConnAveTime                  
                    Gauge32,
                nqaFTPStatsDataConnMaxTime
                    Gauge32,                    
                nqaFTPStatsDataConnMinTime
                    Gauge32,  
                nqaFTPStatsDataConnAveTime
                    Gauge32,    
                nqaFTPStatsConnectSumTimeMax
                    Gauge32,    
                nqaFTPStatsConnectSumTimeMin
                    Gauge32,    
                nqaFTPStatsConnectSumTimeAve
                    Gauge32,                                                                                                  
                nqaFTPStatsMessageBodyOctetsSum
                    Counter32,
                nqaFTPStatsErrors
                    Counter32,   
                nqaFTPStatsTimeouts
                    Counter32,                     
                nqaFTPStatsDiscontinued
                    Counter32, 
                nqaFTPStatsProbeResponses
                    Counter32,        
                nqaFTPStatsSendProbes
                    Counter32,         
                nqaFTPStatsTestFinished                    
                    INTEGER,
                nqaFTPStatsRttAvg
                    Gauge32,
                 nqaFTPStatsLostPacketRatio
                 Gauge32
               }

        nqaFTPStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "FTP statistics table index and times of dispatching dependency tests."
            ::= { nqaFTPStatsEntry 1 }
        
        nqaFTPStatsCompletions OBJECT-TYPE
            SYNTAX INTEGER
                   {
    noResult(0),
    success(1),
            failure(2)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of FTP test."
            ::= { nqaFTPStatsEntry 2 }
        
        nqaFTPStatsRTDOverThresholds OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of successful statistics test RTD (Round Trip Delay) over the threshold."
            ::= { nqaFTPStatsEntry 3 }
        
        nqaFTPStatsCtrlConnMaxTime OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum value of the control connection time."
            ::= { nqaFTPStatsEntry 4 }
            
        nqaFTPStatsCtrlConnMinTime OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum value of the control connection time."
            ::= { nqaFTPStatsEntry 5 }
            
        nqaFTPStatsCtrlConnAveTime OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average value of the control connection time."
            ::= { nqaFTPStatsEntry 6 }
                                
        nqaFTPStatsDataConnMaxTime OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum value of the data connection time."
            ::= { nqaFTPStatsEntry 7 }
        
        nqaFTPStatsDataConnMinTime OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum value of the data connection time."
            ::= { nqaFTPStatsEntry 8 }
            
        nqaFTPStatsDataConnAveTime OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average value of the data connection time."
            ::= { nqaFTPStatsEntry 9 }   
            
        nqaFTPStatsConnectSumTimeMax OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum value of the FTP probe time."
            ::= { nqaFTPStatsEntry 10 }  
            
        nqaFTPStatsConnectSumTimeMin OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The mininum value of the FTP probe time."
            ::= { nqaFTPStatsEntry 11 }  
            
        nqaFTPStatsConnectSumTimeAve OBJECT-TYPE
            SYNTAX Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average value of the FTP probe time."
            ::= { nqaFTPStatsEntry 12 }  
                                            
        nqaFTPStatsMessageBodyOctetsSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The accumulated octets of a FTP test."
            ::= { nqaFTPStatsEntry 13 }
        
        
     
        nqaFTPStatsErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of some other errors."
            ::= { nqaFTPStatsEntry 14 }    
            
        nqaFTPStatsTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a FTP operation was not
                 completed before a timeout occurred, i.e.
                 nqaAdminCtrlTimeOut was exceeded."
            ::= { nqaFTPStatsEntry 15 }   
                        
        nqaFTPStatsDiscontinued OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a FTP test was interrupted by the user."
            ::= { nqaFTPStatsEntry 16 }
            
        nqaFTPStatsProbeResponses OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Number of responses received for the corresponding test.  The value of this object
                MUST be reported as 0 when no probe responses have been received."
            ::= { nqaFTPStatsEntry 17 }   
                     
        nqaFTPStatsSendProbes OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of sending probe"
            ::= { nqaFTPStatsEntry 18 }
            
        nqaFTPStatsTestFinished OBJECT-TYPE
            SYNTAX INTEGER
                {
   noFinish(0),
   finish(1)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test execution."
            ::= { nqaFTPStatsEntry 19 }

          nqaFTPStatsRttAvg OBJECT-TYPE 
            SYNTAX Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of RTTs that were successfully measured by tests."
            ::= { nqaFTPStatsEntry  20}  

          nqaFTPStatsLostPacketRatio OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of the packets lost to all packets sent in the test."
            ::= { nqaFTPStatsEntry  21 }

        nqaMpingStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaMpingStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The statistics collection database.
                 Defines the Mping Operations Statistics Table for
          storing the statistics of a Mping test."
            ::= { nqaStats 5 }
        
        nqaMpingStatsEntry OBJECT-TYPE
            SYNTAX NqaMpingStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the nqaMpingStatsTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaMpingStatsIndex ,nqaMpingStatsReceiverIndex }
            ::= { nqaMpingStatsTable 1 }

        NqaMpingStatsEntry ::=
            SEQUENCE {     
nqaMpingStatsIndex  
Integer32,
nqaMpingStatsReceiverIndex
Integer32,
nqaMpingStatsTargetAddressType
InetAddressType, 
nqaMpingStatsTargetAddress
InetAddress,
nqaMpingStatsReceiverAddress
InetAddress,
nqaMpingStatsCompletions
        INTEGER,
nqaMpingStatsRTDOverThresholds
               Counter32,
nqaMpingStatsSumCompletionTime
Counter32,
nqaMpingStatsSumCompletionTime2Low
Counter32,
nqaMpingStatsSumCompletionTime2High
Counter32,
nqaMpingStatsCompletionTimeMin
Gauge32,
nqaMpingStatsCompletionTimeMax
Gauge32,
nqaMpingStatsTimeouts
Counter32,
nqaMpingStatsBusies
Counter32,
nqaMpingStatsSequenceErrors
Counter32,
nqaMpingStatsDrops
Counter32,
nqaMpingStatsProbeResponses
Counter32,
nqaMpingStatsSentProbes
Counter32,
nqaMpingStatsLastGoodProbe
DateAndTime,
nqaMpingStatsTestFinished
                                INTEGER,
                           nqaMpingStatsReceiverCount
Gauge32,
                           nqaMpingStatsLastFibHit
                                    TruthValue,
                           nqaMpingStatsRttAvg
                                 Gauge32,
                           nqaMpingStatsLostPacketRatio
                                   Gauge32                            
                     } 

        nqaMpingStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of mping statistics table, and the times of dispatching dependency tests."
            ::= { nqaMpingStatsEntry 1 } 
            
      nqaMpingStatsReceiverIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of receiver ,that received the mping request packet and response the reply packet ."
            ::= { nqaMpingStatsEntry 2 }
        
  nqaMpingStatsTargetAddressType  OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address type, and the value range can be unknown(0), 
                ipv4(1) and dns(16)."
            ::= { nqaMpingStatsEntry 3 }
        
        nqaMpingStatsTargetAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address ."
            ::= { nqaMpingStatsEntry 4 }       
            
 nqaMpingStatsReceiverAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The address of mping receiver"
            ::= { nqaMpingStatsEntry 5 } 
            
        nqaMpingStatsCompletions  OBJECT-TYPE
            SYNTAX INTEGER
                {
   noResult(1),
           success(2),
   failure(3)
              }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test."
            ::= { nqaMpingStatsEntry 6 }
                    
        nqaMpingStatsRTDOverThresholds  OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of successful statistics test RTD (Round Trip Delay) over 
                the threshold."
            ::= { nqaMpingStatsEntry 7 }        
        
  nqaMpingStatsSumCompletionTime  OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of RTT (response time) of sent packets."
            ::= { nqaMpingStatsEntry 8 }
        
        nqaMpingStatsSumCompletionTime2Low  OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The low order 32 bits of the sum of squares of RTT (response time)
                 for each packet of test execution.                "
            ::= { nqaMpingStatsEntry 9 }
        
        nqaMpingStatsSumCompletionTime2High OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The high order 32 bits of the sum of squares 
                of RTT (response time) for each packet of test execution.                 "
            ::= { nqaMpingStatsEntry 10 }
        
        nqaMpingStatsCompletionTimeMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum RTT of all packets for executing a test. "
            ::= { nqaMpingStatsEntry 11 }
        
        nqaMpingStatsCompletionTimeMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum RTT of all packets in executing a test. "
            ::= { nqaMpingStatsEntry 12 }        
        
        nqaMpingStatsTimeouts  OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a NQA operation was not completed before a timeout occurred, 
                i.e.  nqaAdminCtrlTimeOut was exceeded."
            ::= { nqaMpingStatsEntry 13 }
        
        nqaMpingStatsBusies OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of failure to apply resource due to the busy system. 
                such as apply memory or create socket failure."
            ::= { nqaMpingStatsEntry 14 }
        
        nqaMpingStatsSequenceErrors OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The times that sequence error happends."
            ::= { nqaMpingStatsEntry 15 }
        
        nqaMpingStatsDrops OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times probes of the test dropped due to sending or connecting failure . "
            ::= { nqaMpingStatsEntry 16 }        
      
      
        nqaMpingStatsProbeResponses OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of received reponse packets."
            ::= { nqaMpingStatsEntry 17 }        
        
        nqaMpingStatsSentProbes OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of sent packets."
            ::= { nqaMpingStatsEntry 18 }  
                        
        nqaMpingStatsLastGoodProbe OBJECT-TYPE
            SYNTAX      DateAndTime
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Date and time when the last response was received for a probe."
            ::= { nqaMpingStatsEntry 19 }    
            
        nqaMpingStatsTestFinished OBJECT-TYPE
            SYNTAX INTEGER
                {
                  noFinish(1),
             finish(2)
         }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test execution."
            ::= { nqaMpingStatsEntry 20 }     
                                       
   nqaMpingStatsReceiverCount OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The current serial number of receivers."
            ::= { nqaMpingStatsEntry 21 }    

          nqaMpingStatsLastFibHit OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Whether the fib is hit on the device when receiving the last packet."
            ::= {nqaMpingStatsEntry 22}

          nqaMpingStatsRttAvg OBJECT-TYPE 
            SYNTAX Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of RTTs that were successfully measured by tests."
            ::= { nqaMpingStatsEntry 23}  

          nqaMpingStatsLostPacketRatio OBJECT-TYPE
            SYNTAX Gauge32  
    MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of the packets lost to all packets sent in the test."
            ::= { nqaMpingStatsEntry 24 }

 nqaMtracertStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaMtracertStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The statistics collection database.
                 Defines the Mping Operations Statistics Table for
          storing the statistics of a Mping test."
            ::= { nqaStats 6 }
         
 
        nqaMtracertStatsEntry OBJECT-TYPE
            SYNTAX NqaMtracertStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The operation of this table is same as that of nqaResultsTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaMtracertStatsIndex, nqaMtracertStatsHopIndex }
            ::= { nqaMtracertStatsTable 1 } 
 
        NqaMtracertStatsEntry ::=
            SEQUENCE {   
nqaMtracertStatsIndex
Integer32,
nqaMtracertStatsHopIndex
Integer32,
nqaMtracertStatsAddressType
InetAddressType,
nqaMtracertStatsAddress 
InetAddress,
nqaMtracertStatsCompletions 
INTEGER,
nqaMtracertStatsCurHopCount 
Gauge32,
nqaMtracertStatsCurProbeCount 
Gauge32,
nqaMtracertStatsRTDOverThresholds
Counter32,
nqaMtracertStatsTimeouts
Counter32,
nqaMtracertStatsBusies
Counter32,
nqaMtracertStatsSequenceErrors
Counter32,
nqaMtracertStatsDrops
Counter32,
nqaMtracertStatsProbeResponses
Counter32,
nqaMtracertStatsSentProbes
Counter32,
nqaMtracertStatsLastGoodProbe
DateAndTime,
nqaMtracertStatsLastGoodPath
DateAndTime,
nqaMtracertStatsTestFinished
INTEGER,     
nqaMtracertStatsCurPathTTL
Gauge32,
nqaMtracertStatsMaxPathTTL
Gauge32,
nqaMtracertStatsInPkgLossRate
Gauge32,
nqaMtracertStatsSGPkgLossRate
Gauge32,
nqaMtracertStatsInPkgRate
Gauge32,
nqaMtracertStatsOutPkgRate
Gauge32,
nqaMtracertStatsTimeDelay
Gauge32
                     } 
 
nqaMtracertStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of mping statistics table, and the times of dispatching dependency tests."
            ::= { nqaMtracertStatsEntry 1 } 
 
        nqaMtracertStatsHopIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of Mtracert hop "
            ::= { nqaMtracertStatsEntry 2 } 
                                        
nqaMtracertStatsAddressType  OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address type,
                 and the value range can be unknown(0), ipv4(1) and dns(16)."
            ::= { nqaMtracertStatsEntry 3 }
        
        nqaMtracertStatsAddress  OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address 
                (TraceRoute is the source address of the last valuable response package)."
            ::= { nqaMtracertStatsEntry 4 }       
            
    
        nqaMtracertStatsCompletions  OBJECT-TYPE
            SYNTAX INTEGER
                {
     noResult(1),
     success(2),
     failure(3)
        }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test."
            ::= { nqaMtracertStatsEntry 5 }    
    
        nqaMtracertStatsCurHopCount OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
               "It is equal to the hop count index executed
                by current tests. The default value is 0."
            ::= { nqaMtracertStatsEntry 6 }    
    
        nqaMtracertStatsCurProbeCount OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
               "The total count of probe packets in a hop count index."
            ::= { nqaMtracertStatsEntry 7 }           
    
        nqaMtracertStatsRTDOverThresholds OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of successful statistics test RTD (Round Trip Delay) over the threshold."
            ::= { nqaMtracertStatsEntry 8 }   
   
   
         nqaMtracertStatsTimeouts  OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a NQA operation was not completed before a timeout occurred, 
                i.e.  nqaAdminCtrlTimeOut was exceeded."
            ::= { nqaMtracertStatsEntry 9}
        
        nqaMtracertStatsBusies OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of failure to apply resource due to the busy system. 
                such as apply memory or create socket failure."
            ::= { nqaMtracertStatsEntry 10 }
        
        nqaMtracertStatsSequenceErrors OBJECT-TYPE
        SYNTAX Counter32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
                "The times that sequence error happends."
            ::= { nqaMtracertStatsEntry 11 }
        
        nqaMtracertStatsDrops OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times probes of the test dropped due to sending or connecting failure . "
            ::= { nqaMtracertStatsEntry 12 }        
      
      
        nqaMtracertStatsProbeResponses OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of received reponse packets."
            ::= { nqaMtracertStatsEntry 13 }        
        
        nqaMtracertStatsSentProbes OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of sent packets."
            ::= { nqaMtracertStatsEntry 14 }  
       
       nqaMtracertStatsLastGoodProbe OBJECT-TYPE
            SYNTAX      DateAndTime
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "Date and time when the last response was received for a probe."
            ::= { nqaMtracertStatsEntry 15 } 
               
       nqaMtracertStatsLastGoodPath OBJECT-TYPE
            SYNTAX      DateAndTime
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The date and time when the last complete path was determined."
            ::= { nqaMtracertStatsEntry 16 }    
                                                            
        nqaMtracertStatsTestFinished OBJECT-TYPE
            SYNTAX INTEGER
                {
    noFinish(1),
            finish(2)
        }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test execution."
            ::= { nqaMtracertStatsEntry 17 }   

        nqaMtracertStatsCurPathTTL OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The TTL(time to live) of the current path. "
            ::= { nqaMtracertStatsEntry 18 }        

        nqaMtracertStatsMaxPathTTL OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum TTL(time to live) of the path. "
            ::= { nqaMtracertStatsEntry 19 }        

        nqaMtracertStatsInPkgLossRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The loss rate of the incoming packets."
            ::= { nqaMtracertStatsEntry 20 }        

        nqaMtracertStatsSGPkgLossRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The loss rate of the packets from source to multicast group."
            ::= { nqaMtracertStatsEntry 21}        

        nqaMtracertStatsInPkgRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The rate of incoming packets."
            ::= { nqaMtracertStatsEntry 22 }        

        nqaMtracertStatsOutPkgRate OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The rate of outgoing packets."
            ::= { nqaMtracertStatsEntry 23 }        

        nqaMtracertStatsTimeDelay OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The time delay of packets from hop to hop."
            ::= { nqaMtracertStatsEntry 24 }        

        nqaPathMtuStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaPathMtuStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The statistics collection database.
                 Defines the path MTU Operations Statistics Table for
                 storing the statistics of a path MTU test."
            ::= { nqaStats 7 }
        
        nqaPathMtuStatsEntry OBJECT-TYPE
            SYNTAX NqaPathMtuStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines an entry in the nqaPathMtuStatsTable."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaPathMtuStatsIndex }
            ::= { nqaPathMtuStatsTable 1 }

        NqaPathMtuStatsEntry ::=
            SEQUENCE {     
                nqaPathMtuStatsIndex  
                    Integer32,
                nqaPathMtuStatsAddressType
                    InetAddressType,
                nqaPathMtuStatsAddress
                    InetAddress,
                nqaPathMtuStatsCompletions
                    INTEGER,
                nqaPathMtuStatsSentProbes 
                    Counter32,            
                nqaPathMtuStatsProbeResponses
                    Counter32, 
                nqaPathMtuStatsDiscoveryPathMtuMin
                    Gauge32,
                nqaPathMtuStatsDiscoveryPathMtuMax
                    Gauge32,
                nqaPathMtuStatsOptimumFirstStep
                    Gauge32,
                nqaPathMtuStatsBusies
                    Counter32,
                nqaPathMtuStatsTimeouts
                    Counter32,
                nqaPathMtuStatsDrops
                    Counter32,
                nqaPathMtuStatsPathMtu
                    Gauge32, 
                nqaPathMtuStatsTestFinished
                    INTEGER                                       
            } 

        nqaPathMtuStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of path MTU statistics table, and the times 
                 of dispatching dependency tests."
            ::= { nqaPathMtuStatsEntry 1 }  
                  
        nqaPathMtuStatsAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address type,
                 and the value range can be unknown(0), ipv4(1) and dns(16)."
            ::= { nqaPathMtuStatsEntry 2 } 

        nqaPathMtuStatsAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address.
                 It is the source address of the ICMP echo reply packet."
            ::= { nqaPathMtuStatsEntry 3 }    
              

        nqaPathMtuStatsCompletions OBJECT-TYPE
            SYNTAX INTEGER
            {
                noResult(0),
                success(1),
                failure(2)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test."
            ::= { nqaPathMtuStatsEntry 4 } 
                                   
        nqaPathMtuStatsSentProbes OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of sent packets."
            ::= { nqaPathMtuStatsEntry 5 }         
            
        nqaPathMtuStatsProbeResponses OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of received reponse packets."
            ::= { nqaPathMtuStatsEntry 6 } 

      nqaPathMtuStatsDiscoveryPathMtuMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The initial length of packet in the path MTU test.
                 It is the minimal length of a ICMP packet."
            ::= { nqaPathMtuStatsEntry 7 }   

      nqaPathMtuStatsDiscoveryPathMtuMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximal length of packet in the path MTU test.
                 It is the maximal MTU of local out-interface."
            ::= { nqaPathMtuStatsEntry 8 } 
            
      nqaPathMtuStatsOptimumFirstStep OBJECT-TYPE
            SYNTAX Gauge32  
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The optimum first step of the path MTU test. 
                 It will take effect if the specified step is lower than it."
            ::= { nqaPathMtuStatsEntry 9 }  
            
      nqaPathMtuStatsBusies OBJECT-TYPE
            SYNTAX Counter32  
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of failure to apply resource due to the busy system. 
                 such as apply memory or create socket failure."
            ::= { nqaPathMtuStatsEntry 10 }  
            
      nqaPathMtuStatsTimeouts OBJECT-TYPE
            SYNTAX Counter32  
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a NQA operation was not completed before a timeout occurred, 
                 i.e. nqaAdminCtrlTimeOut was exceeded."
            ::= { nqaPathMtuStatsEntry 11 } 
            
        nqaPathMtuStatsDrops OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times probes of the test dropped due to sending or connecting failure."
            ::= { nqaPathMtuStatsEntry 12 }                                                      
                        
        nqaPathMtuStatsPathMtu OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The value of current path MTU."
            ::= { nqaPathMtuStatsEntry 13 }
                        
        nqaPathMtuStatsTestFinished  OBJECT-TYPE
            SYNTAX INTEGER
            {
                nofinish(0),
                finish(1)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test execution."
            ::= { nqaPathMtuStatsEntry 14 }   

       nqaPathJitterStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaPathJitterStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of latest path jitter operation."
            ::= { nqaStats 8 }
        
        nqaPathJitterStatsEntry OBJECT-TYPE
            SYNTAX NqaPathJitterStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the status of latest path jitter operation.
                 This entry is created only if the nqaAdminCtrlType is path jitter."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaPathJitterStatsIndex, nqaPathJitterStatsHopIndex }
            ::= { nqaPathJitterStatsTable 1 }
        
        NqaPathJitterStatsEntry ::=
            SEQUENCE { 
                nqaPathJitterStatsIndex
                    Integer32,
                nqaPathJitterStatsHopIndex
                    Integer32,
                nqaPathJitterStatsCompletions
                    INTEGER,
                nqaPathJitterStatsAddressType
                    InetAddressType,
                nqaPathJitterStatsAddress
                    InetAddress,                    
                nqaPathJitterStatsRtdOverThresholds
                    Counter32,
                nqaPathJitterStatsNumOfRtt
                    Counter32,
                nqaPathJitterStatsRttSum
                    Counter32,
                nqaPathJitterStatsRttSum2Low
                    Counter32,
                nqaPathJitterStatsRttSum2High
                    Counter32,
                nqaPathJitterStatsRttMin
                    Gauge32,
                nqaPathJitterStatsRttMax
                    Gauge32,
                nqaPathJitterStatsMinOfPositivesSD
                    Gauge32,
                nqaPathJitterStatsMaxOfPositivesSD
                    Gauge32,
                nqaPathJitterStatsNumOfPositivesSD
                    Counter32,
                nqaPathJitterStatsSumOfPositivesSD
                    Counter32,
                nqaPathJitterStatsSum2OfPositivesSDLow
                    Counter32,
                nqaPathJitterStatsSum2OfPositivesSDHigh
                    Counter32,
                nqaPathJitterStatsMinOfNegativesSD
                    Gauge32,
                nqaPathJitterStatsMaxOfNegativesSD
                    Gauge32,
                nqaPathJitterStatsNumOfNegativesSD
                    Counter32,
                nqaPathJitterStatsSumOfNegativesSD
                    Counter32,
                nqaPathJitterStatsSum2OfNegativesSDLow
                    Counter32,
                nqaPathJitterStatsSum2OfNegativesSDHigh
                    Counter32,
                nqaPathJitterStatsMinOfPositivesDS
                    Gauge32,
                nqaPathJitterStatsMaxOfPositivesDS
                    Gauge32,
                nqaPathJitterStatsNumOfPositivesDS
                    Counter32,
                nqaPathJitterStatsSumOfPositivesDS
                    Counter32,
                nqaPathJitterStatsSum2OfPositivesDSLow
                    Counter32,
                nqaPathJitterStatsSum2OfPositivesDSHigh
                    Counter32,
                nqaPathJitterStatsMinOfNegativesDS
                    Gauge32,
                nqaPathJitterStatsMaxOfNegativesDS
                    Gauge32,
                nqaPathJitterStatsNumOfNegativesDS
                    Counter32,
                nqaPathJitterStatsSumOfNegativesDS
                    Counter32,
                nqaPathJitterStatsSum2OfNegativesDSLow
                    Counter32,
                nqaPathJitterStatsSum2OfNegativesDSHigh
                    Counter32,
                nqaPathJitterStatsPacketLossSD
                    Counter32,
                nqaPathJitterStatsPacketLossDS
                    Counter32,
                nqaPathJitterStatsPacketOutOfSequences
                    Counter32,
                nqaPathJitterStatsErrors
                    Counter32,
                nqaPathJitterStatsBusies
                    Counter32,   
                nqaPathJitterStatsTimeouts
                    Counter32,   
                nqaPathJitterStatsProbeResponses
                    Counter32,   
                nqaPathJitterStatsSentProbes
                    Counter32,   
                nqaPathJitterStatsDrops
                    Counter32,                                                 
                nqaPathJitterStatsTestFinished
                    INTEGER,
                nqaPathJitterStatsMaxDelaySD
                    Gauge32,
                nqaPathJitterStatsMaxDelayDS
                    Gauge32,                        
                nqaPathJitterStatsRttAvg
                    Gauge32,
                nqaPathJitterStatsPacketLossRatio
                    Gauge32,
                nqaPathJitterStatsAvgJitter
                    Gauge32,
                nqaPathJitterStatsAvgJitterSD   
                    Gauge32,
                nqaPathJitterStatsAvgJitterDS
                    Gauge32,
                nqaPathJitterStatsJitterOut
                    OCTET STRING,
                nqaPathJitterStatsJitterIn      
                    OCTET STRING,
                nqaPathJitterStatsOwdOverThresholdsSD
                    Counter32,
                nqaPathJitterStatsPktLossUnknown
                    Counter32,
                nqaPathJitterStatsNumOfOwd
                    Counter32,
                nqaPathJitterStatsOwdSumSD
                    Counter32,
                nqaPathJitterStatsOwdSumDS
                    Counter32,
                nqaPathJitterStatsOwdOverThresholdsDS
                    Counter32
             }

        nqaPathJitterStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of path jitter statistics table, and the times of dispatching dependency tests."
            ::= { nqaPathJitterStatsEntry 1 } 
            
        nqaPathJitterStatsHopIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of path jitter hop."
            ::= { nqaPathJitterStatsEntry 2 }
        
        nqaPathJitterStatsCompletions OBJECT-TYPE
            SYNTAX INTEGER
            {
                noResult(0),
                success(1),
                failure(2)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of path jitter test."
            ::= { nqaPathJitterStatsEntry 3 }
            
        nqaPathJitterStatsAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address type in a hop of path jitter test, 
                 and the value range can be unknown(0), ipv4(1) and dns(16)."
            ::= { nqaPathJitterStatsEntry 4 }
        
        nqaPathJitterStatsAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 5 }
        
        nqaPathJitterStatsRtdOverThresholds OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times of successful executions who's RTD over the threshold in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 6 }
        
        nqaPathJitterStatsNumOfRtt OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times that statistics RTT successfully in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 7 }
        
        nqaPathJitterStatsRttSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of RTTs in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 8 }
        
        nqaPathJitterStatsRttSum2Low OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The low order 32 bits of RTT's square sum that successfully measured in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 9 }
        
        nqaPathJitterStatsRttSum2High OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The high order 32 bits of RTT's square sum that successfully measured in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 10 }
        
        nqaPathJitterStatsRttMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of RTTs that successfully measured in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 11 }
        
        nqaPathJitterStatsRttMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of RTTs that successfully measured in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 12 }
        
        nqaPathJitterStatsMinOfPositivesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of positive values of jitter that measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 13 }
        
        nqaPathJitterStatsMaxOfPositivesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of positive values of jitter that measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 14 }
        
        nqaPathJitterStatsNumOfPositivesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of positive values of jitter that measured from packets sent from
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 15 }
        
        nqaPathJitterStatsSumOfPositivesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of positive values of jitter that measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 16 }
        
        nqaPathJitterStatsSum2OfPositivesSDLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The low order 32 bits of square sum of positive values of jitter that
                 measured from packets sent from source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 17 }
        
        nqaPathJitterStatsSum2OfPositivesSDHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The high order 32 bits of square sum of positive values of jitter that 
                 measured from packets sent from source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 18 }
        
        nqaPathJitterStatsMinOfNegativesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of negative values of jitter that measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 19 }
        
        nqaPathJitterStatsMaxOfNegativesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of negative values of jitter that measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 20 }
        
        nqaPathJitterStatsNumOfNegativesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of negative values of jitter that measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 21 }
        
        nqaPathJitterStatsSumOfNegativesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of negative values of jitter that measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 22 }
        
        nqaPathJitterStatsSum2OfNegativesSDLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The low order 32 bits of square sum of negative values of jitter that  
                 measured from packets sent from source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 23 }
        
        nqaPathJitterStatsSum2OfNegativesSDHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The high order 32 bits of square sum of negative values of jitter that  
                 measured from packets sent from source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 24 }
        
        nqaPathJitterStatsMinOfPositivesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of positive values of jitter that measured from packets sent from 
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 25 }
        
        nqaPathJitterStatsMaxOfPositivesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of positive values of jitter that measured from packets sent from 
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 26 }
        
        nqaPathJitterStatsNumOfPositivesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of positive values of jitter that measured from packets sent from
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 27 }
        
        nqaPathJitterStatsSumOfPositivesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of positive values of jitter that measured from packets sent from
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 28 }
        
        nqaPathJitterStatsSum2OfPositivesDSLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The low order 32 bits of square sum of positive values of jitter that
                 measured from packets sent from destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 29 }
        
        nqaPathJitterStatsSum2OfPositivesDSHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The high order 32 bits of square sum of positive values of jitter that 
                 measured from packetssent from destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 30 }
        
        nqaPathJitterStatsMinOfNegativesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of negative values of jitter that measured from packets sent from 
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 31 }
        
        nqaPathJitterStatsMaxOfNegativesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of negative values of jitter that measured from packets sent from
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 32 }
        
        nqaPathJitterStatsNumOfNegativesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of negative values of jitter that measured from packets sent from 
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 33 }
        
        nqaPathJitterStatsSumOfNegativesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of negative values of jitter that measured from packets sent from
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 34 }
        
        nqaPathJitterStatsSum2OfNegativesDSLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The low order 32 bits of square sum of negative values of jitter that  
                 measured from packets sent from destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 35 }
        
        nqaPathJitterStatsSum2OfNegativesDSHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The high order 32 bits of square sum of negative values of jitter that 
                 measured from packets sent from destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 36 }
        
        nqaPathJitterStatsPacketLossSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of dropped packets sent from source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 37 }
        
        nqaPathJitterStatsPacketLossDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of dropped packets sent from destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 38 }
        
        nqaPathJitterStatsPacketOutOfSequences OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets that return out of sequence."
            ::= { nqaPathJitterStatsEntry 39 }
        
        nqaPathJitterStatsErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of some other errors type."
            ::= { nqaPathJitterStatsEntry 40 }
        
        nqaPathJitterStatsBusies OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a path jitter operation could 
                 not be initiated because an internal error."
            ::= { nqaPathJitterStatsEntry 41 }      
            
        nqaPathJitterStatsTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a NQA operation was not completed
                 before a timeout occurred, i.e. nqaAdminCtrlTimeOut was exceeded."
            ::= { nqaPathJitterStatsEntry 42 }    
            
        nqaPathJitterStatsProbeResponses OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of responses received for the corresponding test.  
                 The value of this object MUST be reported as 0 when no probe responses have been received."
            ::= { nqaPathJitterStatsEntry 43 }    
            
        nqaPathJitterStatsSentProbes OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of this object reflects the number of packets sent for the corresponding test.
                 The value of this object MUST be reported as 0 when no probes have been sent."
            ::= { nqaPathJitterStatsEntry 44 }    
            
        nqaPathJitterStatsDrops OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The times probes of the test dropped due to sending failure."
            ::= { nqaPathJitterStatsEntry 45 }                
                                                                                               
        nqaPathJitterStatsTestFinished OBJECT-TYPE
            SYNTAX INTEGER
            {
                noFinish(0),
                finish(1)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The status of test execution."
            ::= { nqaPathJitterStatsEntry 46 }         
        
        nqaPathJitterStatsMaxDelaySD OBJECT-TYPE
            SYNTAX Gauge32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of all OWD (One Way Delay) 
                 that measured from packets sent from source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 47 }
    
        nqaPathJitterStatsMaxDelayDS OBJECT-TYPE
            SYNTAX Gauge32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of all OWD (One Way Delay)
                 that measured from packets sent from destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 48 }                       
            
         nqaPathJitterStatsRttAvg OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of RTTs that were successfully measured in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 49 }  

         nqaPathJitterStatsPacketLossRatio OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The ratio of the packets lost to all packets sent in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 50 }  

         nqaPathJitterStatsAvgJitter OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of jitter values that were successfully measured in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 51 }  

         nqaPathJitterStatsAvgJitterSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average value of jitters that measured from packets sent from
                 sourse to destination in a hop of path jitter test."                
            ::= { nqaPathJitterStatsEntry 52 }  

         nqaPathJitterStatsAvgJitterDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average value of jitters that measured from packets sent from
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 53 }  

        nqaPathJitterStatsJitterOut OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "jitter (RFC1889) at responder."
            ::= { nqaPathJitterStatsEntry 54 }
                       
        nqaPathJitterStatsJitterIn OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Jitter (RFC1889) at sender."
            ::= { nqaPathJitterStatsEntry 55 }
            
        nqaPathJitterStatsOwdOverThresholdsSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate OWD (One Way Delay)
                 threshold from source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 56 }                    
                                                                                                    
        nqaPathJitterStatsPktLossUnknown OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of lost packets for which we can't determine the direction
                 in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 57 }
            
        nqaPathJitterStatsNumOfOwd OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of OWDs that were successfully measured in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 58 }

        nqaPathJitterStatsOwdSumSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of OWDs that were successfully measured from packets sent from 
                 source to destination in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 59 }

        nqaPathJitterStatsOwdSumDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of OWDs that were successfully measured from packets sent from 
                 destination to source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 60 }   
            
        nqaPathJitterStatsOwdOverThresholdsDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate OWD (One Way Delay) threshold from destination to
                 source in a hop of path jitter test."
            ::= { nqaPathJitterStatsEntry 61 }                                              
        
        nqaPppoeStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaPppoeStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the rolling accumulated history of the PPPoE operation."
            ::= { nqaStats 9 }
        
        nqaPppoeStatsEntry OBJECT-TYPE
            SYNTAX NqaPppoeStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the rolling accumulated history of the PPPoE operation.
       This entry is created only if the nqaAdminCtrlType is pppoe."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaPppoeStatsIndex, nqaPppoeRedialIndex }
            ::= { nqaPppoeStatsTable 1 }  
            
        NqaPppoeStatsEntry ::=
            SEQUENCE {
                nqaPppoeStatsIndex
                    Integer32,
                nqaPppoeRedialIndex
                Integer32, 
                nqaPppoeStatsCompletions
                INTEGER,          
                nqaPppoeStatsCurrentPhase
                INTEGER,
                nqaPppoeStatsErrorMessage
                INTEGER,   
                nqaPppoeDiscoveryTimeout
                Gauge32,
                nqaPppoeLcpTimeout
                Gauge32,
                nqaPppoeAuthorizationTimeout
                Gauge32,
                nqaPppoeNcpTimeout
                Gauge32,
                nqaPppoeConnectionTime
                Gauge32,  
                nqaPppoeClientSessionId
                Gauge32,
                nqaPppoeClientIpAddress
                InetAddress,
                nqaPppoeGatewayIpAddress
                InetAddress
                     }
                           
nqaPppoeStatsIndex OBJECT-TYPE
SYNTAX Integer32 (1..2147483647)
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The index of PPPoE statistics table, and the executing times of dispatching dependency tests."                        
::= { nqaPppoeStatsEntry 1 }

nqaPppoeRedialIndex OBJECT-TYPE
SYNTAX Integer32 (1..2147483647)
MAX-ACCESS not-accessible
STATUS current
DESCRIPTION
"The index of PPPoE statistics table, and the redialing times of dispatching dependency tests."                        
::= { nqaPppoeStatsEntry 2 }

nqaPppoeStatsCompletions OBJECT-TYPE
SYNTAX INTEGER 
{
dialupSuccess(1),
dialupFail(2),
stop(3),
exceptionStop(4)
}
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The status of PPPoE operation."                        
::= { nqaPppoeStatsEntry 3 }

nqaPppoeStatsCurrentPhase OBJECT-TYPE
SYNTAX INTEGER 
{
discovery(1),
lcp(2),
authorization(3),
ncp(4),
online(5),
stop(6)
}
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The current phase of PPPoE operation."                        
::= { nqaPppoeStatsEntry 4 }

nqaPppoeStatsErrorMessage OBJECT-TYPE
SYNTAX INTEGER 
{ 
other(1),
timeout(2),
paramNegotiateFail(3),
userAuthenticationFail(4),
peerDownRequest(5),
noError(255)
}
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The error message result of PPPoE operation."                        
::= { nqaPppoeStatsEntry 5 }

nqaPppoeDiscoveryTimeout  OBJECT-TYPE
SYNTAX Gauge32 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The time for discovery of PPPoE operation."                        
::= { nqaPppoeStatsEntry 6 }

nqaPppoeLcpTimeout  OBJECT-TYPE
SYNTAX Gauge32 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The time for lcp negotiate of PPPoE operation."                        
::= { nqaPppoeStatsEntry 7 }

nqaPppoeAuthorizationTimeout  OBJECT-TYPE
SYNTAX Gauge32 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The time for authentication of PPPoE operation."                        
::= { nqaPppoeStatsEntry 8 }

 nqaPppoeNcpTimeout  OBJECT-TYPE
SYNTAX Gauge32 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The time for ncp negotiate of PPPoE operation."                        
::= { nqaPppoeStatsEntry 9 }

nqaPppoeConnectionTime OBJECT-TYPE
SYNTAX Gauge32 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The time for connection of PPPoE operation."                        
::= { nqaPppoeStatsEntry 10 }

nqaPppoeClientSessionId  OBJECT-TYPE
SYNTAX Gauge32 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The client sessionID of PPPoE operation."                        
::= { nqaPppoeStatsEntry 11 }

nqaPppoeClientIpAddress  OBJECT-TYPE
SYNTAX InetAddress 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The client ip address of PPPoE operation."                        
::= { nqaPppoeStatsEntry 12 }

nqaPppoeGatewayIpAddress  OBJECT-TYPE
SYNTAX InetAddress 
MAX-ACCESS read-only 
STATUS current
DESCRIPTION
"The gateway ip address of PPPoE operation."                        
::= { nqaPppoeStatsEntry 13 } 
        
        nqaHistory OBJECT IDENTIFIER ::= { nqa 5 }
        
        nqaHistoryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines the Remote Operations test History Table for
                storing the results of a test operation."
            ::= { nqaHistory 1 }
        
        nqaHistoryEntry OBJECT-TYPE
            SYNTAX NqaHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines a table for storing the results of a test
                operation.  Entries in this table are limited by
                the value of the corresponding nqaAdminParaHistoryRowMax."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaHistoryIndex, nqaHistoryHopIndex, nqaHistoryProbeIndex }
            ::= { nqaHistoryTable 1 }
        
        NqaHistoryEntry ::=
            SEQUENCE { 
                nqaHistoryIndex
                    Integer32,
                nqaHistoryHopIndex
                    Integer32,
                nqaHistoryProbeIndex
                    Integer32,
                nqaHistoryTimeStamp
                    DateAndTime,            
                nqaHistoryAddressType
                    InetAddressType,
                nqaHistoryAddress
                    InetAddress,
                nqaHistoryCompletionTime
                    Integer32,
                nqaHistoryFinishState
                    INTEGER,      
                nqaHistoryLastRC                    
                    Integer32
             }

        nqaHistoryIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The history table index and  times for dispatching dependency tests."
            ::= { nqaHistoryEntry 1 }
        
        nqaHistoryHopIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The hop index. If the test type is Traceroute or LSP Traceroute, 
                the entry only defines one hop. For other types, the default value is 1."
            ::= { nqaHistoryEntry 2 }
        
        nqaHistoryProbeIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The probe index of tests."
            ::= { nqaHistoryEntry 3 }
            
      nqaHistoryTimeStamp OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The start time stamp of a probe."
            ::= { nqaHistoryEntry 4 }
                 
        nqaHistoryAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The address type of history records."
            ::= { nqaHistoryEntry 5 }
        
        nqaHistoryAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address of tests. For Traceroute, it is the destination address of each hop. "
            ::= { nqaHistoryEntry 6 }
        
        nqaHistoryCompletionTime OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The amount of time measured in milliseconds from when
                 a probe was sent to when its response was received or
                 when it timed out. The value of this object is reported
                 as 0 when it is not possible to transmit a probe."
            ::= { nqaHistoryEntry 7 }
        
        nqaHistoryFinishState OBJECT-TYPE
            SYNTAX INTEGER
                {
                success(1),
                timeout(2),
                drop(3),
                busy(4),
                overThreshold(5),
                disconnected(6),
                noConnected(7)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The result of a test operation made by a remote
          host for a particular probe."
            ::= { nqaHistoryEntry 8 }
        
        nqaHistoryLastRC OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The last implementation method specific reply code received.
          If the ICMP Echo capability is being used then a successful
          probe ends when an ICMP response is received that contains
          the code ICMP_ECHOREPLY(0).  The ICMP responses are defined
          normally in the ip_icmp include file."
            ::= { nqaHistoryEntry 9 }
            
            
                               
        nqaMpingHistoryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaMpingHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines the Remote Operations Mping test History Table for
          storing the results of a test operation."
            ::= { nqaHistory 2 }
        
        nqaMpingHistoryEntry OBJECT-TYPE
            SYNTAX NqaMpingHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines a table for storing the results of a test
          operation.  Entries in this table are limited by
          the value of the corresponding nqaAdminParaHistoryRowMax."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaMpingHistoryIndex, nqaMpingHistoryReceiverIndex, nqaMpingHistoryResponseIndex }
            ::= { nqaMpingHistoryTable 1 }
        
        NqaMpingHistoryEntry ::=
            SEQUENCE {  
nqaMpingHistoryIndex
Integer32,
nqaMpingHistoryReceiverIndex
Integer32,
nqaMpingHistoryResponseIndex
Integer32,
nqaMpingHistoryAddressType
InetAddressType,
nqaMpingHistoryAddress
        InetAddress,
nqaMpingHistoryReceiverAddress
InetAddress,
nqaMpingHistoryTimeStamp
DateAndTime,
nqaMpingHistoryCompletionTime
Integer32,
nqaMpingHistoryFinishState
INTEGER, 
nqaMpingHistoryLastRC
Integer32,
nqaMpingHistoryFibHit
TruthValue
                  }

        nqaMpingHistoryIndex OBJECT-TYPE
            SYNTAX Integer32(1..'7FFFFFFF'h) 
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The Mping history table index and  times for dispatching dependency tests."
            ::= { nqaMpingHistoryEntry 1 }
        
        nqaMpingHistoryReceiverIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of receiver ,that received  mping request packets and response  reply packets ."
            ::= { nqaMpingHistoryEntry 2 }
        
        nqaMpingHistoryResponseIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of response packets send by receiver"
            ::= { nqaMpingHistoryEntry 3 } 
            
        nqaMpingHistoryAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The address type of history records."
            ::= { nqaMpingHistoryEntry 4 }
        
        nqaMpingHistoryAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address of tests. "
            ::= { nqaMpingHistoryEntry 5 }
  
        nqaMpingHistoryReceiverAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The source address of response package of tests. "
            ::= { nqaMpingHistoryEntry 6 }
  
            
      nqaMpingHistoryTimeStamp OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The start time stamp of a probe."
            ::= { nqaMpingHistoryEntry 7 }
                 
        
        nqaMpingHistoryCompletionTime OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The completion time of a probe in milliseconds."
            ::= { nqaMpingHistoryEntry 8 }
        
        nqaMpingHistoryFinishState OBJECT-TYPE
            SYNTAX INTEGER
                {
                success(1),
                timeout(2),
                drop(3),
                busy(4),
                overThreshold(5),
                disconnected(6),
                noConnected(7)
                }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The result of a test operation made by a remote
                host for a particular probe."
            ::= { nqaMpingHistoryEntry 9 }
        
        nqaMpingHistoryLastRC OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The last reply code received."
            ::= { nqaMpingHistoryEntry 10 }
            
        nqaMpingHistoryFibHit OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Whether the fib is hit on the device which received the packet."
            ::= { nqaMpingHistoryEntry 11 }    
    
        nqaMtracertHistoryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaMtracertHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines the Remote MtracertOperations test History Table for
                storing the results of a test operation."
            ::= { nqaHistory 3 }
        
        nqaMtracertHistoryEntry OBJECT-TYPE
            SYNTAX NqaMtracertHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines a table for storing the results of a test
                operation.  Entries in this table are limited by
                the value of the corresponding nqaAdminParaHistoryRowMax."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaMtracertHistoryIndex, nqaMtracertHistoryHopIndex }
            ::= { nqaMtracertHistoryTable 1 }   

         
        NqaMtracertHistoryEntry ::=
            SEQUENCE {
nqaMtracertHistoryIndex
Integer32,
nqaMtracertHistoryHopIndex
Integer32,
nqaMtracertHistoryAddressType
InetAddressType, 
nqaMtracertHistoryAddress
        InetAddress,
nqaMtracertHistoryTimeStamp
                DateAndTime,
nqaMtracertHistoryCompletionTime
        Integer32,
nqaMtracertHistoryLastRC
        Integer32,
nqaMtracertHistoryCurQueryMode
                        INTEGER,
nqaMtracertHistoryQueryArrivalTime
Unsigned32,
nqaMtracertHistoryIncomingIfAddress
        InetAddress,
nqaMtracertHistoryOutgoingIfAddress
        InetAddress,
nqaMtracertHistoryPreHopRouterAddress
        InetAddress,
nqaMtracertHistoryInputPacketCount
Gauge32,
nqaMtracertHistoryOutputPacketCount
Gauge32,
nqaMtracertHistoryTotalSGPacketCount
Gauge32,
nqaMtracertHistoryRtgProtocol
INTEGER,
nqaMtracertHistoryFwdTTL
Gauge32,
nqaMtracertHistoryFwdCode
INTEGER,
nqaMtracertHistroyFinishState
INTEGER
             }

        nqaMtracertHistoryIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The Mtracert history table index and  times for dispatching dependency tests."
            ::= { nqaMtracertHistoryEntry 1 }
        
        nqaMtracertHistoryHopIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of Mtracert hop. "
            ::= { nqaMtracertHistoryEntry 2 }

        nqaMtracertHistoryAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The address type of history records."
            ::= { nqaMtracertHistoryEntry 3 }
        
        nqaMtracertHistoryAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address of tests. For Traceroute, it is the destination address of each hop.  "
            ::= { nqaMtracertHistoryEntry 4 }
        
            
      nqaMtracertHistoryTimeStamp OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The start time stamp of a probe."
            ::= { nqaMtracertHistoryEntry 5 }
                 
        
        nqaMtracertHistoryCompletionTime OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The amount of time measured in milliseconds from when
               a probe was sent to when its response was received or
                when it timed out. The value of this object is reported
              as 0 when it is not possible to transmit a probe."
            ::= { nqaMtracertHistoryEntry 6 }
        
       
        nqaMtracertHistoryLastRC OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The last implementation method specific reply code received.
          If the ICMP Echo capability is being used then a successful
          probe ends when an ICMP response is received that contains
          the code ICMP_ECHOREPLY(0). The ICMP responses are defined
          normally in the ip_icmp include file."
            ::= { nqaMtracertHistoryEntry 7 }
    
    
        nqaMtracertHistoryCurQueryMode OBJECT-TYPE
            SYNTAX INTEGER { 
             maxHops (1)  ,
             hopByHop (2)

            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The current query mode."
            ::= { nqaMtracertHistoryEntry 8 }
    
        nqaMtracertHistoryQueryArrivalTime OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Query Arrival Time is a 32-bit NTP timestamp specifying 
                the arrival time of the traceroute request packet at this router."
            ::= { nqaMtracertHistoryEntry 9 }
    
        nqaMtracertHistoryIncomingIfAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field specifies the address of the interface on which packets from 
                this source and group are expected to arrive, or 0 if unknown."
            ::= { nqaMtracertHistoryEntry 10 }
    
        nqaMtracertHistoryOutgoingIfAddress OBJECT-TYPE
            SYNTAX InetAddress 
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field specifies the address of the interface on which packets from this source 
                and group flow to the specified destination, or 0 if unknown."
            ::= { nqaMtracertHistoryEntry 11 }    
            
        nqaMtracertHistoryPreHopRouterAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field specifies the router from 
                which this router expects packets from this source. "
            ::= { nqaMtracertHistoryEntry 12 }  
            
        nqaMtracertHistoryInputPacketCount OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field contains the number of multicast packets received for 
                all groups and sources on the incoming interface, or 0xffffffff if 
                no count can be reported.  This counter should have the same 
                value as ifInMulticastPkts from the IF-MIB for this interface."
            ::= { nqaMtracertHistoryEntry 13 }  


        nqaMtracertHistoryOutputPacketCount OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field contains the number of multicast packets that have 
                 been transmitted or queued for transmission for all groups and 
                 sources on the outgoing interface, or 0xffffffff if no count can
                 be reported. This counter should have the same value as 
                 ifOutMulti-castPkts from the IF-MIB for this interface."
            ::= { nqaMtracertHistoryEntry 14 }  
   
               
        nqaMtracertHistoryTotalSGPacketCount OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field counts the number of packets from the specified source 
                forwarded by this router to the specified group, or 0xffffffff if 
                no count can be reported. This counter should have the same value as 
                ipMRoutePkts from the IPMROUTE-STD-MIB for this forwarding entry."
            ::= { nqaMtracertHistoryEntry 15 }  
            
            
        nqaMtracertHistoryRtgProtocol OBJECT-TYPE
            SYNTAX INTEGER { 
        dvmrp(1),
mospf(2),
pim(3),
cbt(4)  ,
pimUsingSpecRteTab(5)  ,
pimUsingStaticRte(6)  ,
dvmrpUsingStaticRte(7), 
pimUsingMBGPRte(8)  ,
cbtUsingSpecRteTab(9), 
cbtUsingStaticRte(10) ,
pimUsingState(11), 
unknownProtocol(255) 
            }

            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field describes the routing protocol in use between this
                 router and the previous-hop router."
            ::= { nqaMtracertHistoryEntry 16 }  
            
            
        nqaMtracertHistoryFwdTTL OBJECT-TYPE
            SYNTAX  Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field contains the TTL that a packet is required to 
                have before it will be forwarded over the outgoing interface."
            ::= { nqaMtracertHistoryEntry 17 }   
            
            
        nqaMtracertHistoryFwdCode OBJECT-TYPE
            SYNTAX  INTEGER  
            { 
noError(1),
wrongIf(2),
pruneSent(3),
pruneRCVD(4),
scoped(5),
noRoute(6),
wrongLastHop(7),
notForwarding(8) ,
reachedRP(9) ,
noMulticast(11),
infoHidden(12),
noSpace(130),
oldRouter(131) ,
adminProhib(132),
unknownError(255) 
            }
            
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This field contains a forwarding information/error code.  
noError(1):      No error
wrongIf(2):      Traceroute request arrived on an interface to
         which this router would not forward for this
         source,group,destination.
pruneSent(3):    This router has sent a prune upstream which
         applies to the source and group in the tracer-
         oute request.
pruneRCVD(4):    This router has stopped forwarding for this
         source and group in response to a request from
         the next hop router.
scoped(5):       The group is subject to administrative scoping
         at this hop.
noRoute(6):      This router has no route for the source or
         group and no way to determine a potential
         route.
wrongLastHop(7): This router is not the proper last-hop router.
notForwarding(8):This router is not forwarding this
         source,group out the outgoing interface for an
         unspecified reason.
reachedRP(9):    Reached Rendez-vous Point or Core
rpfIf(10):       Traceroute request arrived on the expected RPF
         interface for this source,group.
noMulticast(11): Traceroute request arrived on an interface
         which is not enabled for multicast.
infoHidden(12):  One or more hops have been hidden from this
         trace.
noSpace(13):     There was not enough room to insert another
         response data block in the packet.
oldRouter(14):   The previous hop router does not understand
         traceroute requests.
adminProhib(15): Traceroute is administratively prohibited.
                "
            ::= { nqaMtracertHistoryEntry 18 }   
  
        nqaMtracertHistroyFinishState OBJECT-TYPE
            SYNTAX  INTEGER  
            { 
success(1), 
timeout(2), 
busy(3), 
drop(4),
overThreshold(5),
disconnected(6), 
noConnected(7)            
    }
            
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The finish state of a probe."
            ::= { nqaMtracertHistoryEntry 19 }   
    
        nqaVplsMacTracertHistoryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaVplsMacTracertHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines the VPLS mac trace Operations test History Table for
                storing the results of a test operation."
            ::= { nqaHistory 4 }
        
        nqaVplsMacTracertHistoryEntry OBJECT-TYPE
            SYNTAX NqaVplsMacTracertHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines a table for storing the results of a test
                operation.  Entries in this table are limited by
                the value of the corresponding nqaAdminParaHistoryRowMax."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaVplsMacTracertHistoryIndex, nqaVplsMacTracertHistoryHopIndex, nqaVplsMacTracertHistoryResponseIndex }
              
            ::= { nqaVplsMacTracertHistoryTable 1 }   

         
        NqaVplsMacTracertHistoryEntry ::=
            SEQUENCE {
nqaVplsMacTracertHistoryIndex
Integer32,
nqaVplsMacTracertHistoryHopIndex
Integer32,               
nqaVplsMacTracertHistoryResponseIndex
Integer32,
nqaVplsMacTracertHistoryTimeStamp
DateAndTime,
nqaVplsMacTracertHistoryAddressType
InetAddressType, 
nqaVplsMacTracertHistoryAddress
InetAddress,
nqaVplsMacTracertHistoryCompletionTime
Integer32,
nqaVplsMacTracertHistoryFinishState
INTEGER,
nqaVplsMacTracertHistoryHitFlag
TruthValue,
nqaVplsMacTracertHistoryDSCount
Integer32,
nqaVplsMacTracertHistorySuccessPathNode
TruthValue
             }

        nqaVplsMacTracertHistoryIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The VPLS mac trace history table index and  times for dispatching dependency tests."
            ::= { nqaVplsMacTracertHistoryEntry 1 }
        
        nqaVplsMacTracertHistoryHopIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of VPLS mac trace hop. "
            ::= { nqaVplsMacTracertHistoryEntry 2 }

        nqaVplsMacTracertHistoryResponseIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of VPLS mac trace reply packet per hop."
            ::= { nqaVplsMacTracertHistoryEntry 3 }

      nqaVplsMacTracertHistoryTimeStamp  OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The start time stamp of a probe."
            ::= { nqaVplsMacTracertHistoryEntry 4 }
 
        nqaVplsMacTracertHistoryAddressType  OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The address type of history records."
            ::= { nqaVplsMacTracertHistoryEntry 5 }
        
        nqaVplsMacTracertHistoryAddress  OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The destination address of tests. For Traceroute, it is the destination address of each hop.  "
            ::= { nqaVplsMacTracertHistoryEntry 6 }
                                         
        nqaVplsMacTracertHistoryCompletionTime OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The completion time of a probe in milliseconds.
                The amount of time measured in milliseconds from when
               a probe was sent to when its response was received or
                when it timed out.  The value of this object is reported
              as 0 when it is not possible to transmit a probe."
            ::= { nqaVplsMacTracertHistoryEntry 7 }
        
  
        nqaVplsMacTracertHistoryFinishState OBJECT-TYPE
            SYNTAX  INTEGER  
        { 
success(1), 
timeout(2),
drop(3), 
busy(4), 
overThreshold(5),
disconnected(6), 
noConnected(7)            
    }
            
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The finish state of a probe."
            ::= { nqaVplsMacTracertHistoryEntry 8 }   
    

        nqaVplsMacTracertHistoryHitFlag OBJECT-TYPE
            SYNTAX  TruthValue  
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Hit Flag."
            ::= { nqaVplsMacTracertHistoryEntry 9 }   
    

        nqaVplsMacTracertHistoryDSCount OBJECT-TYPE
            SYNTAX Integer32 (0..'7FFFFFFF'h)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The count of Downstreams."
            ::= { nqaVplsMacTracertHistoryEntry 10 }   
    

        nqaVplsMacTracertHistorySuccessPathNode OBJECT-TYPE
            SYNTAX  TruthValue  
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates whether it is a node along the path that passes the Trace test. By default, the value is false, indicating that the object is not a node along the path that passes the Trace test."         
            -- DEFVAL { false }               
            ::= { nqaVplsMacTracertHistoryEntry 11 }   




        nqaVplsMacTracertHistoryDSTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaVplsMacTracertHistoryDSEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines the VPLS mac trace Operations test History Downstreanm Table for
                storing the results of a test operation."
            ::= { nqaHistory 5 }
        
        nqaVplsMacTracertHistoryDSEntry OBJECT-TYPE
            SYNTAX NqaVplsMacTracertHistoryDSEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines a table for storing the results of a test
                operation.  Entries in this table are limited by
                the value of the corresponding nqaAdminParaHistoryRowMax."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaVplsMacTracertHistoryIndex, nqaVplsMacTracertHistoryHopIndex, nqaVplsMacTracertHistoryResponseIndex, nqaVplsMacTracertHistoryDSIndex }
              
            ::= { nqaVplsMacTracertHistoryDSTable 1 }   

         
        NqaVplsMacTracertHistoryDSEntry ::=
            SEQUENCE {
nqaVplsMacTracertHistoryDSIndex
Integer32,
nqaVplsMacTracertHistoryDSAddress 
InetAddress            
             }

        nqaVplsMacTracertHistoryDSIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The VPLS mac trace history DS table index."
            ::= { nqaVplsMacTracertHistoryDSEntry 1 }
        
        nqaVplsMacTracertHistoryDSAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "DS address."
            ::= { nqaVplsMacTracertHistoryDSEntry 2 }

 nqaVplsMTraceHistoryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaVplsMTraceHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines the remote vpls multicast trace operations test history table for
                storing the results of a test operation."
            ::= { nqaHistory 6 }
        
        nqaVplsMTraceHistoryEntry OBJECT-TYPE
            SYNTAX NqaVplsMTraceHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "Defines a table for storing the results of a test
                operation.  Entries in this table are limited by
                the value of the corresponding nqaAdminParaHistoryRowMax."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaVplsMTraceHistoryIndex, nqaVplsMTraceHistoryHopIndex, nqaVplsMTraceHistoryResponseIndex }
            ::= { nqaVplsMTraceHistoryTable 1 }
         
        NqaVplsMTraceHistoryEntry ::=
            SEQUENCE {
nqaVplsMTraceHistoryIndex
Integer32,
nqaVplsMTraceHistoryHopIndex
Integer32,
nqaVplsMTraceHistoryResponseIndex
Integer32,
nqaVplsMTraceHistoryResponserAddressType
InetAddressType, 
nqaVplsMTraceHistoryResponserAddress
        InetAddress,
nqaVplsMTraceHistoryUpStreamAddressType
InetAddressType, 
nqaVplsMTraceHistoryUpStreamAddress
        InetAddress,    
nqaVplsMTraceHistoryReceivedTtl
        Unsigned32,       
nqaVplsMTraceHistoryIGMPVersion
        INTEGER, 
nqaVplsMTraceHistoryIGMPSnpgEnable
        EnableValue,
nqaVplsMTraceHistoryIGMPProxyEnable
        EnableValue,
nqaVplsMTraceHistoryIGMPRouterPortLearningEnable
        EnableValue,
nqaVplsMTraceHistoryRequireRouterAlertEnable
        EnableValue,   
nqaVplsMTraceHistoryForwardMode
        INTEGER,
nqaVplsMTraceHistoryHitFlag
        TruthValue,
nqaVplsMTraceHistoryPWExist
        TruthValue, 
nqaVplsMTraceHistoryGroupPolicy
        INTEGER,
nqaVplsMTraceHistoryCACExist
        INTEGER,
nqaVplsMTraceHistoryRcvQueryCount
        Gauge32,
nqaVplsMTraceHistoryRcvReportCount
        Gauge32,
nqaVplsMTraceHistoryRcvLeaveCount
        Gauge32,
nqaVplsMTraceHistoryTimeStamp
        DateAndTime,        
nqaVplsMTraceHistoryCompletionTime
        Integer32,
nqaVplsMTraceHistoryLastRC
        Integer32,
nqaVplsMTraceHistoryLastRSC
        Integer32,
nqaVplsMTraceHistoryFinishState
INTEGER,
nqaVplsMTraceHistorySuccessPathNode
INTEGER
             }

        nqaVplsMTraceHistoryIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The vpls multicast trace history table index and  times for dispatching dependency tests."
            ::= { nqaVplsMTraceHistoryEntry 1 }
        
        nqaVplsMTraceHistoryHopIndex OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of vpls multicast trace hop. "
            ::= { nqaVplsMTraceHistoryEntry 2 }

        nqaVplsMTraceHistoryResponseIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The index of vpls multicast trace responser. "
            ::= { nqaVplsMTraceHistoryEntry 3 }
            
        nqaVplsMTraceHistoryResponserAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The type of history record's Responser address."
            ::= { nqaVplsMTraceHistoryEntry 4 }
        
        nqaVplsMTraceHistoryResponserAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The Responser address of tests."
            ::= { nqaVplsMTraceHistoryEntry 5 } 
            
        nqaVplsMTraceHistoryUpStreamAddressType OBJECT-TYPE
            SYNTAX InetAddressType
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The type of history records' last hop address."
            ::= { nqaVplsMTraceHistoryEntry 6 }
        
        nqaVplsMTraceHistoryUpStreamAddress OBJECT-TYPE
            SYNTAX InetAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The last hop address of tests."
            ::= { nqaVplsMTraceHistoryEntry 7 }                                 

        nqaVplsMTraceHistoryReceivedTtl OBJECT-TYPE
            SYNTAX Unsigned32 (1..255)
            UNITS       "time-to-live value"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The value of ttl in the received packet."
            ::= { nqaVplsMTraceHistoryEntry 8 } 
            
        nqaVplsMTraceHistoryIGMPVersion OBJECT-TYPE
            SYNTAX INTEGER
            {
            igmpv1(1),
            igmpv2(2),
            igmpv3(3)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The IGMP protocol version."
            ::= { nqaVplsMTraceHistoryEntry 9 }
            
        nqaVplsMTraceHistoryIGMPSnpgEnable OBJECT-TYPE
            SYNTAX EnableValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The IGMP snooping enable state."
            ::= { nqaVplsMTraceHistoryEntry 10 }

        nqaVplsMTraceHistoryIGMPProxyEnable OBJECT-TYPE
            SYNTAX EnableValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The IGMP proxy enable switch."
            ::= { nqaVplsMTraceHistoryEntry 11 }
            
        nqaVplsMTraceHistoryIGMPRouterPortLearningEnable OBJECT-TYPE
            SYNTAX EnableValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The IGMP router port learning enable switch."
            ::= { nqaVplsMTraceHistoryEntry 12 }
            
        nqaVplsMTraceHistoryRequireRouterAlertEnable OBJECT-TYPE
            SYNTAX EnableValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The route alert requirement enable switch."
            ::= { nqaVplsMTraceHistoryEntry 13 }

        nqaVplsMTraceHistoryForwardMode OBJECT-TYPE
            SYNTAX INTEGER
            {
            mac(1),
            ip(2)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The forward mode value."
            ::= { nqaVplsMTraceHistoryEntry 14 }        

        nqaVplsMTraceHistoryHitFlag OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Whether the fib is hit on the device which received the packet."
            ::= { nqaVplsMTraceHistoryEntry 15 }
            
        nqaVplsMTraceHistoryPWExist OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Whether the forward PW is exist."
            ::= { nqaVplsMTraceHistoryEntry 16 }
            
        nqaVplsMTraceHistoryGroupPolicy OBJECT-TYPE
            SYNTAX INTEGER
            {
            permit(1),
            deny(2)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The ACL judge result."
            ::= { nqaVplsMTraceHistoryEntry 17 }

        nqaVplsMTraceHistoryCACExist OBJECT-TYPE
            SYNTAX INTEGER
            {
            yes(1),
            no(2)
            }
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Whether the connection admission control comfiguration of current VSI is exist."
            ::= { nqaVplsMTraceHistoryEntry 18 }            

        nqaVplsMTraceHistoryRcvQueryCount OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The received query packet number for current IGMP version of current VSI."
            ::= { nqaVplsMTraceHistoryEntry 19 }
             
        nqaVplsMTraceHistoryRcvReportCount OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The received report packet number for current IGMP version of current VSI."
            ::= { nqaVplsMTraceHistoryEntry 20 } 
            
        nqaVplsMTraceHistoryRcvLeaveCount OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The received leave packet number for current IGMP version of current VSI."
            ::= { nqaVplsMTraceHistoryEntry 21 }            

        nqaVplsMTraceHistoryTimeStamp OBJECT-TYPE
            SYNTAX DateAndTime
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The start time stamp of a probe."
            ::= { nqaVplsMTraceHistoryEntry 22 }                            
        
        nqaVplsMTraceHistoryCompletionTime OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The amount of time measured in milliseconds from when
                 a probe was sent to when its response was received or
                 when it timed out. The value of this object is reported
                 as 0 when it is not possible to transmit a probe."
            ::= { nqaVplsMTraceHistoryEntry 23 }

        nqaVplsMTraceHistoryLastRC OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The last implementation method specific reply code received."
            ::= { nqaVplsMTraceHistoryEntry 24 } 

        nqaVplsMTraceHistoryLastRSC OBJECT-TYPE
            SYNTAX Integer32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The last implementation method specific reply sub code received."
            ::= { nqaVplsMTraceHistoryEntry 25 }

        nqaVplsMTraceHistoryFinishState OBJECT-TYPE
            SYNTAX  INTEGER  
            { 
            success(1), 
            timeout(2),
            drop(3),
            busy(4), 
            overThreshold(5),
            disconnected(6), 
            noConnected(7)            
            }            
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The finish state of a probe."
            ::= { nqaVplsMTraceHistoryEntry 26 }

        nqaVplsMTraceHistorySuccessPathNode OBJECT-TYPE
            SYNTAX  INTEGER  
            { 
            onPath(1), 
            notonPath(2)            
            }            
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Whether the response router is on the path of a probe. The default value is notonPath(2)."
            -- DEFVAL { 2 }
            ::= { nqaVplsMTraceHistoryEntry 27 }                

        
        nqaMacTraceHistoryTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaMacTraceHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object defines a list of historical MAC trace test instances for storing the running results of test instances."
            ::= { nqaHistory 7 }

        
        nqaMacTraceHistoryEntry OBJECT-TYPE
            SYNTAX NqaMacTraceHistoryEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object defines a table for storing the running results of test instances. 
                The number of entries in this table is limited by the value of nqaAdminParaHistoryRowMax.
                "
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaMacTraceHistoryIndex, nqaMacTraceHistoryReceiveOrder }
            ::= { nqaMacTraceHistoryTable 1 }

        
        NqaMacTraceHistoryEntry ::=
            SEQUENCE { 
                nqaMacTraceHistoryIndex
                    Integer32,
                nqaMacTraceHistoryReceiveOrder
                    Integer32,
                nqaMacTraceHistoryTTL
                    Integer32,
                nqaMacTraceHistorySeqNumber
                    Unsigned32,
        nqaMacTraceHistoryCompletionTime
           Integer32,                                            
                nqaMacTraceHistoryForwarded
                    TruthValue,
                nqaMacTraceHistoryTerminalMep
                    TruthValue,
                nqaMacTraceHistoryRelayAction
                    HWDot1agCfmRelayActionFieldValue,
                nqaMacTraceHistoryIngressAction
                    HWDot1agCfmIngressActionFieldValue,
                nqaMacTraceHistoryIngressMac
                    MacAddress,
                nqaMacTraceHistoryIngressIfName
                    OCTET STRING,
                nqaMacTraceHistoryEgressAction
                    HWDot1agCfmEgressActionFieldValue,
                nqaMacTraceHistoryEgressMac
                    MacAddress,
                nqaMacTraceHistoryEgressIfName
                    OCTET STRING
            }

        nqaMacTraceHistoryIndex OBJECT-TYPE
            SYNTAX Integer32 (1..2147483647)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The object indicates the index of the MAC tract history table and the number of times for dispatching dependency tests."
            ::= { nqaMacTraceHistoryEntry 1 }

        
        nqaMacTraceHistoryReceiveOrder OBJECT-TYPE
            SYNTAX Integer32 (1..2147483647)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "This object indicates the sequence in which LTRs are received."
            ::= { nqaMacTraceHistoryEntry 2 }

        
        nqaMacTraceHistoryTTL OBJECT-TYPE
            SYNTAX Integer32 (1..255)
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the TTL of the LTR."
            ::= { nqaMacTraceHistoryEntry 3 }

        
        nqaMacTraceHistorySeqNumber OBJECT-TYPE
            SYNTAX Unsigned32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the sequence number of the LTR."
            REFERENCE
                "802.1ag clause *********"
            ::= { nqaMacTraceHistoryEntry 4 }
            
        nqaMacTraceHistoryCompletionTime OBJECT-TYPE
            SYNTAX Integer32
            UNITS "milliseconds"
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object  indicates the RTT (ms) of the response packet."                
            ::= { nqaMacTraceHistoryEntry 5 }
        
        nqaMacTraceHistoryForwarded OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates whether the device that sends LTRs continues to forward LTMs."
            ::= { nqaMacTraceHistoryEntry 6 }

        
        nqaMacTraceHistoryTerminalMep OBJECT-TYPE
            SYNTAX TruthValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates whether the LTR is sent by an MEP."
            ::= { nqaMacTraceHistoryEntry 7 }

        
        nqaMacTraceHistoryRelayAction OBJECT-TYPE
            SYNTAX HWDot1agCfmRelayActionFieldValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the value of the Relay Action field in the LTR."
            ::= { nqaMacTraceHistoryEntry 8 }

        
        nqaMacTraceHistoryIngressAction OBJECT-TYPE
            SYNTAX HWDot1agCfmIngressActionFieldValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the value of the Ingress Action field in the LTR."
            ::= { nqaMacTraceHistoryEntry 9 }

        
        nqaMacTraceHistoryIngressMac OBJECT-TYPE
            SYNTAX MacAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the physical MAC address carried in the ingress MAC address field in the LTR."
            ::= { nqaMacTraceHistoryEntry 10 }

         
        nqaMacTraceHistoryIngressIfName OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the name of the inbound interface of the LTR."
            ::= { nqaMacTraceHistoryEntry 11 }

        
        nqaMacTraceHistoryEgressAction OBJECT-TYPE
            SYNTAX HWDot1agCfmEgressActionFieldValue
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the value of the Egress Action field in the LTR."
            ::= { nqaMacTraceHistoryEntry 12 }

       
        nqaMacTraceHistoryEgressMac OBJECT-TYPE
            SYNTAX MacAddress
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the MAC address in the Egress MAC address field of the LTR."
            ::= { nqaMacTraceHistoryEntry 13 }

        
        nqaMacTraceHistoryEgressIfName OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "This object indicates the name of the interface that forwards the LTR."
            ::= { nqaMacTraceHistoryEntry 14 }

        
        
                                                    
                                                    
        nqaNotifications OBJECT IDENTIFIER ::= { nqa 6}
        
        nqaResultsProbeFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaResultsAddressType,
                 nqaResultsAddress,
                 nqaResultsCompletionTimeMin,
                 nqaResultsCompletionTimeMax,
                 nqaResultsSumCompletionTime,
                 nqaResultsProbeResponses,
                 nqaResultsSentProbes,
                 nqaResultsSumCompletionTime2Low,
                 nqaResultsSumCompletionTime2High,
                 nqaResultsLastGoodProbe,
                 nqaResultsLastGoodPath
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a probe failure is detected when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 probeFailure(0) subject to the value of
                 nqaAdminParaTrapProbeFailureFilter.  The object
                 nqaAdminParaTrapProbeFailureFilter can be used to specify the
                 number of successive probe failures that are required
                 before this notification can be generated.(except for HTTP or Jitter or FTP)"
            ::= { nqaNotifications 1 }

         nqaResultsTestFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaResultsAddressType,
                 nqaResultsAddress,
                 nqaResultsCompletionTimeMin,
                 nqaResultsCompletionTimeMax,
                 nqaResultsSumCompletionTime,
                 nqaResultsProbeResponses,
                 nqaResultsSentProbes,
                 nqaResultsSumCompletionTime2Low,
                 nqaResultsSumCompletionTime2High,
                 nqaResultsLastGoodProbe,
                 nqaResultsLastGoodPath
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a nqa test is determined to have failed
                 when the corresponding nqaAdminParaTrapGeneration object is
                 set to testFailure(1).  In this instance
                 nqaAdminParaTrapTestFailureFilter should specify the number of
                 probes in a test required to have failed in order to
                 consider the test as failed. (Except for HTTP or Jitter or FTP)"
            ::= { nqaNotifications 2 }

         nqaResultsTestCompleted NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaResultsAddressType,
                 nqaResultsAddress,
                 nqaResultsCompletionTimeMin,
                 nqaResultsCompletionTimeMax,
                 nqaResultsSumCompletionTime,
                 nqaResultsProbeResponses,
                 nqaResultsSentProbes,
                 nqaResultsSumCompletionTime2Low,
                 nqaResultsSumCompletionTime2High,
                 nqaResultsLastGoodProbe,
                 nqaResultsLastGoodPath
               }
            STATUS  current
            DESCRIPTION
                 "Generated at the completion of a nqa test when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 testCompletion(2).(except for HTTP or Jitter or FTP)"
            ::= { nqaNotifications 3 } 
            
         nqaResultsThresholdNotification NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaResultsAddressType,
                 nqaResultsAddress,
                 nqaAdminCtrlThreshold1,
                 nqaResultsCompletionTimeMax,
                 nqaResultsRTDOverThresholds        
               }
            STATUS  current
            DESCRIPTION
                 "If the time of executing tests exceeds the nqaAdminCtrlThreshold1,
                 the system sends trap information.(except for HTTP or Jitter or FTP)"
            ::= { nqaNotifications 4 }
            
                                    
        nqaHTTPStatsProbeFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaHTTPStatsDNSRTTSum,
                 nqaHTTPStatsTCPConnectRTTSum,
                 nqaHTTPStatsTransactionRTTSum,
                 nqaHTTPStatsDNSServerTimeouts,
                 nqaHTTPStatsTCPConnectTimeouts,
                 nqaHTTPStatsTransactionTimeouts,
                 nqaHTTPStatsDNSQueryErrors, 
                 nqaHTTPStatsTcpConnErrors,
                 nqaHTTPStatsErrors,
                 nqaHTTPStatsProbeResponses,
                 nqaHTTPStatsSendProbes       
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a probe failure is detected when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 probeFailure(0) subject to the value of
                 nqaAdminParaTrapProbeFailureFilter.  The object
                 nqaAdminParaTrapProbeFailureFilter can be used to specify the
                 number of successive probe failures that are required
                 before this notification can be generated.(Only for HTTP)"
            ::= { nqaNotifications 5 } 
            
         nqaHTTPStatsTestFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaHTTPStatsDNSRTTSum,
                 nqaHTTPStatsTCPConnectRTTSum,
                 nqaHTTPStatsTransactionRTTSum,
                 nqaHTTPStatsDNSServerTimeouts,
                 nqaHTTPStatsTCPConnectTimeouts,
                 nqaHTTPStatsTransactionTimeouts,
                 nqaHTTPStatsDNSQueryErrors, 
                 nqaHTTPStatsTcpConnErrors,
                 nqaHTTPStatsErrors,
                 nqaHTTPStatsProbeResponses,
                 nqaHTTPStatsSendProbes
                }
            STATUS  current
            DESCRIPTION
                 "Generated when a HTTP test is determined to have failed
                 when the corresponding nqaAdminParaTrapGeneration object is
                 set to testFailure(1).  In this instance
                 nqaAdminParaTrapTestFailureFilter should specify the number of
                 probes in a test required to have failed in order to
                 consider the test as failed.(Only for HTTP)"
            ::= { nqaNotifications 6 }
            
         nqaHTTPStatsTestCompleted NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaHTTPStatsDNSRTTSum,
                 nqaHTTPStatsTCPConnectRTTSum,
                 nqaHTTPStatsTransactionRTTSum,
                 nqaHTTPStatsDNSServerTimeouts,
                 nqaHTTPStatsTCPConnectTimeouts,
                 nqaHTTPStatsTransactionTimeouts,
                 nqaHTTPStatsDNSQueryErrors, 
                 nqaHTTPStatsTcpConnErrors,
                 nqaHTTPStatsErrors,
                 nqaHTTPStatsProbeResponses,
                 nqaHTTPStatsSendProbes
               }
            STATUS  current
            DESCRIPTION
                 "Generated at the completion of a HTTP test when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 testCompletion(2)."
            ::= { nqaNotifications 7 }     
            
         nqaHTTPStatsThresholdNotification NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaAdminCtrlThreshold1,
                 nqaAdminCtrlThreshold2,
                 nqaAdminCtrlThreshold3,
                 nqaHTTPStatsDNSRTTMax,
                 nqaHTTPStatsTCPConnectRTTMax,
                 nqaHTTPStatsTransactionRTTMax,
                 nqaHTTPStatsRTDOverThresholds   
               }
            STATUS  current
            DESCRIPTION
                 "If the time of executing tests exceeds the nqaAdminCtrlThreshold1 
                 or nqaAdminCtrlThreshold2 or nqaAdminCtrlThreshold3,
                 the system sends trap information.(Only for HTTP)"
            ::= { nqaNotifications 8 }   
                                                
        nqaJitterStatsProbeFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaJitterStatsRTTSum,
                 nqaJitterStatsRTTSum2Low,
                 nqaJitterStatsRTTSum2High,
                 nqaJitterStatsRTTMin,
                 nqaJitterStatsRTTMax,
                 nqaJitterStatsPacketOutOfSequences,
                 nqaJitterStatsErrors, 
                 nqaJitterStatsBusies,
                 nqaJitterStatsTimeouts,
                 nqaJitterStatsDrops,
                 nqaJitterStatsProbeResponses,
                 nqaJitterStatsSentProbes,
                 nqaJitterStatsMaxDelaySD,
                 nqaJitterStatsMaxDelayDS,
                 nqaJitterStatsJitterOut,
                 nqaJitterStatsJitterIn,
                 nqaJitterStatsOWSumSD,
                 nqaJitterStatsOWSumDS
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a probe failure is detected when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 probeFailure(0) subject to the value of
                 nqaAdminParaTrapProbeFailureFilter.  The object
                 nqaAdminParaTrapProbeFailureFilter can be used to specify the
                 number of successive probe failures that are required
                 before this notification can be generated.(Only for Jitter)"
            ::= { nqaNotifications 9 }    
            
         nqaJitterStatsTestFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaJitterStatsRTTSum,
                 nqaJitterStatsRTTSum2Low,
                 nqaJitterStatsRTTSum2High,
                 nqaJitterStatsRTTMin,
                 nqaJitterStatsRTTMax,
                 nqaJitterStatsPacketOutOfSequences,
                 nqaJitterStatsErrors, 
                 nqaJitterStatsBusies,
                 nqaJitterStatsTimeouts,
                 nqaJitterStatsDrops,
                 nqaJitterStatsProbeResponses,
                 nqaJitterStatsSentProbes,
                 nqaJitterStatsMaxDelaySD,
                 nqaJitterStatsMaxDelayDS,
                 nqaJitterStatsJitterOut,
                 nqaJitterStatsJitterIn,
                 nqaJitterStatsOWSumSD,
                 nqaJitterStatsOWSumDS
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a Jitter test is determined to have failed
                 when the corresponding nqaAdminParaTrapGeneration object is
                 set to testFailure(1).  In this instance
                 nqaAdminParaTrapTestFailureFilter should specify the number of
                 probes in a test required to have failed in order to
                 consider the test as failed.(Only for Jitter)"
            ::= { nqaNotifications 10 }
            
     nqaJitterStatsTestCompleted NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaJitterStatsRTTSum,
                 nqaJitterStatsRTTSum2Low,
                 nqaJitterStatsRTTSum2High,
                 nqaJitterStatsRTTMin,
                 nqaJitterStatsRTTMax,
                 nqaJitterStatsPacketOutOfSequences,
                 nqaJitterStatsErrors, 
                 nqaJitterStatsBusies,
                 nqaJitterStatsTimeouts,
                 nqaJitterStatsDrops,
                 nqaJitterStatsProbeResponses,
                 nqaJitterStatsSentProbes,
                 nqaJitterStatsMaxDelaySD,
                 nqaJitterStatsMaxDelayDS,
                 nqaJitterStatsJitterOut,
                 nqaJitterStatsJitterIn,
                 nqaJitterStatsOWSumSD,
                 nqaJitterStatsOWSumDS
               }
            STATUS  current
            DESCRIPTION
                 "Generated at the completion of a Jitter test when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 testCompletion(2). (Only for Jitter)"
            ::= { nqaNotifications 11 }                  
                        
        nqaFTPStatsProbeFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaFTPStatsCtrlConnMaxTime,
                 nqaFTPStatsDataConnMaxTime,
                 nqaFTPStatsConnectSumTimeMax,
                 nqaFTPStatsErrors,
                 nqaFTPStatsTimeouts,
                 nqaFTPStatsProbeResponses,
                 nqaFTPStatsSendProbes
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a probe failure is detected when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 probeFailure(0) subject to the value of
                 nqaAdminParaTrapProbeFailureFilter.  The object
                 nqaAdminParaTrapProbeFailureFilter can be used to specify the
                 number of successive probe failures that are required
                 before this notification can be generated.(Only for FTP)"
            ::= { nqaNotifications 12 }   
                                
         nqaFTPStatsTestFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaFTPStatsCtrlConnMaxTime,
                 nqaFTPStatsDataConnMaxTime,
                 nqaFTPStatsConnectSumTimeMax,
                 nqaFTPStatsErrors,
                 nqaFTPStatsTimeouts,
                 nqaFTPStatsProbeResponses,
                 nqaFTPStatsSendProbes
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a FTP test is determined to have failed
                 when the corresponding nqaAdminParaTrapGeneration object is
                 set to testFailure(1).  In this instance
                 nqaAdminParaTrapTestFailureFilter should specify the number of
                 probes in a test required to have failed in order to
                 consider the test as failed.(Only for FTP)"
            ::= { nqaNotifications 13 }

         nqaFTPStatsTestCompleted NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaScheduleOperStatus,
                 nqaFTPStatsCtrlConnMaxTime,
                 nqaFTPStatsDataConnMaxTime,
                 nqaFTPStatsConnectSumTimeMax,
                 nqaFTPStatsErrors,
                 nqaFTPStatsTimeouts,
                 nqaFTPStatsProbeResponses,
                 nqaFTPStatsSendProbes
               }
            STATUS  current
            DESCRIPTION
                 "Generated at the completion of a FTP test when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 testCompletion(2). (Only for FTP)"
            ::= { nqaNotifications 14 }             
            
         nqaFTPStatsThresholdNotification NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaAdminCtrlThreshold1,
                 nqaAdminCtrlThreshold2,
                 nqaFTPStatsCtrlConnMaxTime,
                 nqaFTPStatsDataConnMaxTime,
                 nqaFTPStatsRTDOverThresholds             
               }
            STATUS  current
            DESCRIPTION
                 "If the time of executing tests exceeds the nqaAdminCtrlThreshold1 or nqaAdminCtrlThreshold2,
                 the system sends trap information.(Only for FTP)"
            ::= { nqaNotifications 15 }
                  
         nqaJitterStatsRTDThresholdNotification NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaAdminCtrlThreshold1,
                nqaJitterStatsRTTMax,
                 nqaJitterStatsMaxDelaySD,
                 nqaJitterStatsMaxDelayDS,
                 nqaJitterStatsRTDOverThresholds
              }
            STATUS  current
            DESCRIPTION
                 "If the time of executing tests exceeds the nqaAdminCtrlThreshold1, 
                  the system sends trap information. (Only for jitter)"
            ::= { nqaNotifications 16 }  
                                                
         nqaJitterStatsOWDThresholdNotificationSD NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaAdminCtrlThreshold2,
                 nqaJitterStatsRTTMax,
                 nqaJitterStatsMaxDelaySD,
                 nqaJitterStatsMaxDelayDS,
                 nqaJitterStatsOWDOverThresholdsSD
               }
            STATUS  current
            DESCRIPTION
                 "If the time of executing tests exceeds the nqaAdminCtrlThreshold2 
                  the system sends trap information. (Only for jitter)"
            ::= { nqaNotifications 17 } 
         nqaJitterStatsOWDThresholdNotificationDS NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaAdminCtrlThreshold3,
                 nqaJitterStatsRTTMax,
                 nqaJitterStatsMaxDelaySD,
                 nqaJitterStatsMaxDelayDS,
                 nqaJitterStatsOWDOverThresholdsDS   
               }
            STATUS  current
            DESCRIPTION
                 "If the time of executing tests exceeds the nqaAdminCtrlThreshold3 
                  the system sends trap information. (Only for jitter)"
            ::= { nqaNotifications 18 } 
            
         nqaNegotiateFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaAdminParaTargetAddressType,
                 nqaAdminParaTargetAddress,
                 nqaAdminParaTargetPort, 
                 nqaAdminParaVrfName                
               }
            STATUS  current
            DESCRIPTION
                 "If negotiation between client and server failed,the system sends trap information."
            ::= { nqaNotifications 19 }    

         nqaRisingAlarmNotification NOTIFICATION-TYPE
        OBJECTS {
               nqaAlarmVariable,
               nqaAlarmSampleType,
               nqaAlarmValue, 
               nqaAlarmRisingThreshold,
               nqaAlarmDescription
                 }
        STATUS current
        DESCRIPTION 
                "Sending trap messages when the value of the monitored object exceeds nqaAlarmUpperThreshold."
        ::= { nqaNotifications 20}

 nqaFallingAlarmNotification NOTIFICATION-TYPE
        OBJECTS { 
             nqaAlarmVariable, 
             nqaAlarmSampleType, 
             nqaAlarmValue, 
             nqaAlarmFallingThreshold,
             nqaAlarmDescription
                 }
        STATUS current
        DESCRIPTION 
                "Sending trap messages when the value of the monitored object is under nqaAlarmLowerThreshold."
        ::= { nqaNotifications 21}

        nqaFtpSaveRecordNotification NOTIFICATION-TYPE
            OBJECTS {
                 nqaFtpSaveRecordLastFileName
               }
            STATUS  current
            DESCRIPTION
                 "Sending trap messages when saving a test result to the FTP server is completed."
            ::= { nqaNotifications 22 } 
            
         nqaPppoeStatsTestFailed NOTIFICATION-TYPE
            OBJECTS {
                 nqaPppoeStatsCompletions,
                 nqaPppoeStatsCurrentPhase,
                 nqaPppoeStatsErrorMessage,
                 nqaPppoeDiscoveryTimeout,
                 nqaPppoeLcpTimeout,
                 nqaPppoeAuthorizationTimeout,
                 nqaPppoeNcpTimeout,
                 nqaPppoeConnectionTime,
                 nqaPppoeClientSessionId,
                 nqaPppoeClientIpAddress,
                 nqaPppoeGatewayIpAddress
               }
            STATUS  current
            DESCRIPTION
                 "Generated when a PPPoE test is determined to have failed
                 when the corresponding nqaAdminParaTrapGeneration object is
                 set to testFailure(1). (Only for PPPoE))"
            ::= { nqaNotifications 23 }

         nqaPppoeStatsTestCompleted NOTIFICATION-TYPE
            OBJECTS {
                 nqaPppoeStatsCompletions,
                 nqaPppoeStatsCurrentPhase,
                 nqaPppoeStatsErrorMessage,
                 nqaPppoeDiscoveryTimeout,
                 nqaPppoeLcpTimeout,
                 nqaPppoeAuthorizationTimeout,
                 nqaPppoeNcpTimeout,
                 nqaPppoeConnectionTime,
                 nqaPppoeClientSessionId,
                 nqaPppoeClientIpAddress,
                 nqaPppoeGatewayIpAddress
               }
            STATUS  current
            DESCRIPTION
                 "Generated at the completion of a PPPoE test when the
                 corresponding nqaAdminParaTrapGeneration object is set to
                 testCompletion(2). (Only for PPPoE)"
            ::= { nqaNotifications 24 }             
                             
        nqaConformance OBJECT IDENTIFIER ::= { nqa 7 }
        
        nqaGroups OBJECT IDENTIFIER ::= { nqaConformance 1 }
        
        nqaBaseGroup OBJECT-GROUP
            OBJECTS { nqaVersion, nqaReset, nqaTimeOfLastSetError, nqaLastSetError, nqaEnable, 
                nqaNumOfCurrentCtrlEntry, nqaMaxConcurrentRequests, nqaMaxNumOfRequests, nqaJitterVersion,nqaSupportTestType,nqaSupportServerType}
            STATUS current
            DESCRIPTION 
                "Description."
            ::= { nqaGroups 1 }
        
        nqaAdminGroup OBJECT-GROUP
            OBJECTS {nqaAdminCtrlTag, nqaAdminCtrlType, nqaAdminCtrlFrequency, nqaAdminCtrlTimeOut, nqaAdminCtrlThreshold1, 
                nqaAdminCtrlThreshold2,nqaAdminCtrlThreshold3,nqaAdminCtrlStatus, nqaAdminParaTargetAddressType, nqaAdminParaTargetAddress, 
                nqaAdminParaTargetPort, nqaAdminParaSourceAddressType, nqaAdminParaSourceAddress, nqaAdminParaSourcePort, nqaAdminParaMaxTtl, 
                nqaAdminParaInitialTtl, nqaAdminParaStorageType, nqaAdminParaMaxFailures, nqaAdminParaDontFragment, nqaAdminParaDataSize, 
                nqaAdminParaDataFill, nqaAdminParaIfIndex, nqaAdminParaByPassRouteTable, nqaAdminParaMiscOptions, nqaAdminParaProbeCount, 
                nqaAdminParaTrapGeneration, nqaAdminParaTrapProbeFailureFilter, nqaAdminParaTrapTestFailureFilter, nqaAdminParaDSField, nqaAdminParaDnsServerAddressType, 
                nqaAdminParaDnsServerAddress, nqaAdminParaOperation, nqaAdminParaHttpVersion, nqaAdminParaHttpOperationString, nqaAdminParaTestFailurePercent, 
                nqaAdminParaFtpUserName, nqaAdminParaFtpPassword, nqaAdminParaFtpFilePath, nqaAdminParaFtpFileSize,nqaAdminParaInterval, 
                nqaAdminParaNumPackets, nqaAdminParaVrfName, nqaAdminParaLspAddressType, nqaAdminParaLspAddressMask, nqaAdminParaLspIpAddress, 
                nqaAdminParaLspPWE3VcId, nqaAdminParaLspPWE3Type, nqaAdminParaLspPWE3Option, nqaAdminParaLspPWE3RemoteVcId, nqaAdminParaLspPWE3RemoteAddress, 
                nqaAdminParaLspExp, nqaAdminParaLspReplyMode, nqaAdminParaResultRowMax, nqaAdminParaHistoryRowMax, nqaAdminParaCreateHopsEntries, 
                nqaAdminParaLspVCType, nqaAdminParaMTraceLastHopAddress,nqaAdminParaMTraceSourceAddress,nqaAdminParaMTraceGroupAddress,nqaAdminParaMTraceMaxTtl,
                nqaAdminParaMTraceSendMode,nqaAdminParaMTraceResponseTtl,nqaAdminParaMTraceResponseAddressType,nqaAdminParaMTraceResponseAddress, nqaAdminParaDistanceNodeType, 
                nqaAdminParaMacAddress, nqaAdminParaRMepID, nqaAdminParaMDName, nqaAdminParaMAName, nqaAdminParaMacTunnelName, 
                nqaAdminParaCodecType, nqaAdminParaIcpifAdvFactor, nqaAdminParaFtpMode, 
                nqaScheduleStartType, nqaScheduleStartTime, nqaScheduleEndType, nqaScheduleEndTime,nqaScheduleAgeTime,  
                nqaScheduleNumOfInitiations, nqaAdminParaIcmpJitterMode, nqaAdminParaPathMtuDiscoveryPathMtuMax, nqaAdminParaPathMtuStep, nqaScheduleOperStatus, 
                nqaScheduleElapsedTime,nqaScheduleLastFinishIndex,nqaScheduleLastCollectIndex,nqaGroupStatusType,nqaGroupPeriod,
                nqaGroupLeaderOwnerIndex,nqaGroupLeaderTestName,nqaGroupMemberNum,nqaGroupMemberFree,nqaAdminParaHardwareBased,nqaAdminParaPppoeUserName,
                nqaAdminParaPppoePassword,nqaAdminParaPppoeVlanIf,nqaAdminParaPppoeAuthenticationMode,nqaAdminParaPppoeRedialUpTimes,nqaAdminParaPppoeInterval,nqaAdminParaVsiName,
                nqaAdminParaVlanId,nqaAdminParaLspTunnelType,nqaAdminParaLspNextHopAddress,nqaAdminParaLspVersion,nqaAdminParaRemoteAddressType,nqaAdminParaRemoteAddress,nqaAdminParaTimeUnit,
                nqaAdminExtPara1,nqaAdminExtPara2,nqaAdminExtPara3,nqaAdminExtPara4,nqaAdminExtPara5,nqaAdminExtPara6,nqaAdminExtPara7,nqaAdminExtPara8,nqaAdminExtPara9,nqaAdminExtPara10,
                nqaAdminExtPara11,nqaAdminExtPara12,nqaAdminExtPara13,nqaAdminExtPara14,nqaAdminExtPara15,nqaAdminExtPara16,nqaAdminExtPara17,nqaAdminExtPara18,nqaAdminExtPara19,nqaAdminExtPara20,
                nqaAdminExtPara21,nqaAdminExtPara22,nqaAdminExtPara23,nqaAdminExtPara24,nqaAdminExtPara25,nqaAdminExtPara26,nqaAdminExtPara27,nqaAdminExtPara28,nqaAdminExtPara29,nqaAdminExtPara30,
                nqaAdminExtPara31,nqaAdminExtPara32,nqaAdminExtPara33,nqaAdminExtPara34,nqaAdminExtPara35,nqaAdminExtPara36,nqaAdminExtPara37,nqaAdminExtPara38,nqaAdminExtPara39,nqaAdminExtPara40,
                nqaAdminExtPara41,nqaAdminExtPara42,nqaAdminExtPara43,nqaAdminExtPara44,nqaAdminExtPara45,nqaAdminExtPara46,nqaAdminExtPara47,nqaAdminExtPara48,nqaAdminExtPara49,nqaAdminExtPara50
            }
            STATUS current
            DESCRIPTION 
                "Description."
            ::= { nqaGroups 2 }
        
        nqaServerGroup OBJECT-GROUP
            OBJECTS { nqaTcpServerAddressType, nqaTcpServerStatus, nqaUdpServerAddressType, nqaUdpServerStatus, nqaIcmpServerAddressType, nqaIcmpServerStatus, nqaServerEnable }
            STATUS current
            DESCRIPTION 
                "Description."
            ::= { nqaGroups 3 }
        
        nqaStatsGroup OBJECT-GROUP
            OBJECTS { nqaResultsCompletions, nqaResultsTestAttempts, nqaResultsCurHopCount, nqaResultsCurProbeCount, nqaResultsRTDOverThresholds, 
                nqaResultsCompletionTimeMin, nqaResultsCompletionTimeMax, nqaResultsDisconnects, nqaResultsTimeouts, nqaResultsBusies, 
                nqaResultsNoConnections, nqaResultsSequenceErrors, nqaResultsDrops, nqaResultsAddressType, nqaResultsAddress, 
                nqaResultsProbeResponses, nqaResultsSentProbes, nqaResultsLastGoodProbe, nqaResultsLastGoodPath, nqaResultsTestFinished, 
                nqaHTTPStatsCompletions, nqaHTTPStatsRTDOverThresholds, nqaHTTPStatsRTTSum, nqaHTTPStatsRTTMin, nqaHTTPStatsRTTMax, 
                nqaHTTPStatsDNSRTTSum, nqaHTTPStatsDNSRTTMin, nqaHTTPStatsDNSRTTMax, nqaHTTPStatsTCPConnectRTTSum, nqaHTTPStatsTCPConnectRTTMin, 
                nqaHTTPStatsTCPConnectRTTMax, nqaHTTPStatsTransactionRTTSum, nqaHTTPStatsTransactionRTTMin, nqaHTTPStatsTransactionRTTMax, nqaHTTPStatsMessageBodyOctetsSum, 
                nqaHTTPStatsDNSServerTimeouts, nqaHTTPStatsTCPConnectTimeouts, nqaHTTPStatsTransactionTimeouts, nqaHTTPStatsDNSQueryErrors, nqaHTTPStatsErrors, 
                nqaHTTPStatsTcpConnErrors, nqaHTTPStatsProbeResponses, nqaHTTPStatsSendProbes, nqaHTTPStatsBusies, nqaHTTPStatsTestFinished, 
                nqaJitterStatsCompletions, nqaJitterStatsRTDOverThresholds, nqaJitterStatsNumOfRTT, nqaJitterStatsRTTSum, nqaJitterStatsRTTSum2Low, 
                nqaJitterStatsRTTSum2High, nqaJitterStatsRTTMin, nqaJitterStatsRTTMax, nqaJitterStatsMinOfPositivesSD, nqaJitterStatsMaxOfPositivesSD, 
                nqaJitterStatsNumOfPositivesSD, nqaJitterStatsSumOfPositivesSD, nqaJitterStatsSum2OfPositivesSDLow, nqaJitterStatsSum2OfPositivesSDHigh, nqaJitterStatsMinOfNegativesSD, 
                nqaJitterStatsMaxOfNegativesSD, nqaJitterStatsNumOfNegativesSD, nqaJitterStatsSumOfNegativesSD, nqaJitterStatsSum2OfNegativesSDLow, nqaJitterStatsSum2OfNegativesSDHigh, 
                nqaJitterStatsMinOfPositivesDS, nqaJitterStatsMaxOfPositivesDS, nqaJitterStatsNumOfPositivesDS, nqaJitterStatsSumOfPositivesDS, nqaJitterStatsSum2OfPositivesDSLow, 
                nqaJitterStatsSum2OfPositivesDSHigh, nqaJitterStatsMinOfNegativesDS, nqaJitterStatsMaxOfNegativesDS, nqaJitterStatsNumOfNegativesDS, nqaJitterStatsSumOfNegativesDS, 
                nqaJitterStatsSum2OfNegativesDSLow, nqaJitterStatsSum2OfNegativesDSHigh, nqaJitterStatsPacketLossSD, nqaJitterStatsPacketLossDS, nqaJitterStatsPacketOutOfSequences, 
                nqaJitterStatsErrors, nqaJitterStatsBusies, nqaJitterStatsTimeouts, nqaJitterStatsProbeResponses, nqaJitterStatsSentProbes, 
                nqaJitterStatsDrops, nqaJitterStatsTestFinished, nqaJitterStatsMaxDelaySD, nqaJitterStatsMaxDelayDS, nqaJitterStatsRTTAvg, 
                nqaJitterStatsPacketLossRatio, nqaJitterStatsAvgJitter, nqaJitterStatsAvgJitterSD, nqaJitterStatsAvgJitterDS, nqaJitterStatsJitterOut, 
                nqaJitterStatsJitterIn, nqaJitterStatsOWDOverThresholdsSD, nqaJitterStatsOWDOverThresholdsDS, nqaJitterStatsPktLossUnknown, nqaJitterStatsNumOfOWD, 
                nqaJitterStatsOWSumSD, nqaPathJitterStatsCompletions, nqaPathJitterStatsAddressType, nqaPathJitterStatsAddress, nqaPathJitterStatsRtdOverThresholds, nqaPathJitterStatsNumOfRtt, nqaPathJitterStatsRttSum, 
                nqaJitterStatsOperOfIcpif, nqaJitterStatsOperOfMos, nqaJitterStatsMinDelaySD, nqaJitterStatsSum2DelaySDLow,
                nqaJitterStatsSum2DelaySDHigh, nqaJitterStatsMinDelayDS, nqaJitterStatsSum2DelayDSLow, nqaJitterStatsSum2DelayDSHigh,nqaJitterStatsTimeUnit,nqaJitterStatsAvgDelaySD,nqaJitterStatsAvgDelayDS,
                nqaJitterStatsPktRewriteNum, nqaJitterStatsPktRewriteRatio, nqaJitterStatsPktDisorderNum, nqaJitterStatsPktDisorderRatio, nqaJitterStatsFragPktDisorderNum, nqaJitterStatsFragPktDisorderRatio, 
                nqaPathJitterStatsRttSum2Low, nqaPathJitterStatsRttSum2High, nqaPathJitterStatsRttMin, nqaPathJitterStatsRttMax, nqaPathJitterStatsMinOfPositivesSD, 
                nqaPathJitterStatsMaxOfPositivesSD, nqaPathJitterStatsNumOfPositivesSD, nqaPathJitterStatsSumOfPositivesSD, nqaPathJitterStatsSum2OfPositivesSDLow, nqaPathJitterStatsSum2OfPositivesSDHigh, 
                nqaPathJitterStatsMinOfNegativesSD, nqaPathJitterStatsMaxOfNegativesSD, nqaPathJitterStatsNumOfNegativesSD, nqaPathJitterStatsSumOfNegativesSD, nqaPathJitterStatsSum2OfNegativesSDLow, 
                nqaPathJitterStatsSum2OfNegativesSDHigh, nqaPathJitterStatsMinOfPositivesDS, nqaPathJitterStatsMaxOfPositivesDS, nqaPathJitterStatsNumOfPositivesDS, nqaPathJitterStatsSumOfPositivesDS, 
                nqaPathJitterStatsSum2OfPositivesDSLow, nqaPathJitterStatsSum2OfPositivesDSHigh, nqaPathJitterStatsMinOfNegativesDS, nqaPathJitterStatsMaxOfNegativesDS, nqaPathJitterStatsNumOfNegativesDS, 
                nqaPathJitterStatsSumOfNegativesDS, nqaPathJitterStatsSum2OfNegativesDSLow, nqaPathJitterStatsSum2OfNegativesDSHigh, nqaPathJitterStatsPacketLossSD, nqaPathJitterStatsPacketLossDS, 
                nqaPathJitterStatsPacketOutOfSequences, nqaPathJitterStatsErrors, nqaPathJitterStatsBusies, nqaPathJitterStatsTimeouts, nqaPathJitterStatsProbeResponses, 
                nqaPathJitterStatsSentProbes, nqaPathJitterStatsDrops, nqaPathJitterStatsTestFinished, nqaPathJitterStatsMaxDelaySD, nqaPathJitterStatsMaxDelayDS, 
                nqaPathJitterStatsRttAvg, nqaPathJitterStatsPacketLossRatio, nqaPathJitterStatsAvgJitter, nqaPathJitterStatsAvgJitterSD, nqaPathJitterStatsAvgJitterDS, 
                nqaPathJitterStatsJitterOut, nqaPathJitterStatsJitterIn, nqaPathJitterStatsOwdOverThresholdsSD, nqaPathJitterStatsPktLossUnknown, nqaPathJitterStatsNumOfOwd, 
                nqaPathJitterStatsOwdSumSD, nqaPathJitterStatsOwdSumDS, nqaPathJitterStatsOwdOverThresholdsDS, nqaPathMtuStatsAddressType, nqaPathMtuStatsAddress, 
                nqaPathMtuStatsCompletions, nqaPathMtuStatsSentProbes, nqaPathMtuStatsDiscoveryPathMtuMin, nqaPathMtuStatsDiscoveryPathMtuMax, nqaPathMtuStatsOptimumFirstStep, 
                nqaPathMtuStatsBusies, nqaPathMtuStatsTimeouts, nqaPathMtuStatsDrops, nqaPathMtuStatsProbeResponses, nqaPathMtuStatsPathMtu, 
                nqaPathMtuStatsTestFinished, nqaJitterStatsOWSumDS, nqaResultsSumCompletionTime, nqaResultsSumCompletionTime2Low, nqaResultsSumCompletionTime2High, 
                nqaFTPStatsCompletions, nqaFTPStatsRTDOverThresholds, nqaFTPStatsCtrlConnMaxTime, nqaFTPStatsCtrlConnMinTime, nqaFTPStatsCtrlConnAveTime, 
                nqaFTPStatsDataConnMaxTime, nqaFTPStatsDataConnMinTime, nqaFTPStatsDataConnAveTime, nqaFTPStatsConnectSumTimeMax, nqaFTPStatsConnectSumTimeMin, 
                nqaFTPStatsConnectSumTimeAve, nqaFTPStatsMessageBodyOctetsSum, nqaFTPStatsErrors, nqaFTPStatsTimeouts, nqaFTPStatsDiscontinued, 
                nqaFTPStatsProbeResponses, nqaFTPStatsSendProbes, nqaFTPStatsTestFinished, nqaMpingStatsTargetAddressType, nqaMpingStatsTargetAddress, 
                nqaMpingStatsReceiverAddress, nqaMpingStatsCompletions, nqaMpingStatsRTDOverThresholds, nqaMpingStatsSumCompletionTime, nqaMpingStatsSumCompletionTime2Low, 
                nqaMpingStatsSumCompletionTime2High, nqaMpingStatsCompletionTimeMin, nqaMpingStatsCompletionTimeMax, nqaMpingStatsTimeouts, nqaMpingStatsBusies, 
                nqaMpingStatsSequenceErrors, nqaMpingStatsDrops, nqaMpingStatsProbeResponses, nqaMpingStatsSentProbes, nqaMpingStatsLastGoodProbe, 
                nqaMpingStatsTestFinished, nqaMpingStatsReceiverCount, nqaMpingStatsLastFibHit, nqaMpingStatsRttAvg, nqaMpingStatsLostPacketRatio, nqaMtracertStatsAddressType, nqaMtracertStatsAddress, nqaMtracertStatsCompletions, nqaMtracertStatsCurHopCount, 
                nqaMtracertStatsCurProbeCount, nqaMtracertStatsRTDOverThresholds, nqaMtracertStatsTimeouts, nqaMtracertStatsBusies, nqaMtracertStatsSequenceErrors, 
                nqaMtracertStatsDrops, nqaMtracertStatsProbeResponses, nqaMtracertStatsSentProbes, nqaMtracertStatsLastGoodProbe, nqaMtracertStatsLastGoodPath, 
                nqaMtracertStatsTestFinished, nqaMtracertStatsCurPathTTL, nqaMtracertStatsMaxPathTTL, nqaMtracertStatsInPkgLossRate, nqaMtracertStatsSGPkgLossRate, 
                nqaMtracertStatsInPkgRate, nqaMtracertStatsOutPkgRate, nqaMtracertStatsTimeDelay,nqaResultsRttAvg,nqaResultsLostPacketRatio,nqaHTTPStatsRttAvg,nqaHTTPStatsLostPacketRatio,nqaFTPStatsRttAvg,nqaFTPStatsLostPacketRatio,
                nqaPppoeStatsCompletions, nqaPppoeStatsCurrentPhase, nqaPppoeStatsErrorMessage, nqaPppoeDiscoveryTimeout, nqaPppoeLcpTimeout, nqaPppoeAuthorizationTimeout, nqaPppoeNcpTimeout, nqaPppoeConnectionTime, nqaPppoeClientSessionId,
                nqaPppoeClientIpAddress, nqaPppoeGatewayIpAddress}
            STATUS current
            DESCRIPTION 
                "Description."
            ::= { nqaGroups 4 }
        
        nqaHistoryGroup OBJECT-GROUP
            OBJECTS { nqaHistoryTimeStamp, nqaHistoryAddressType, nqaHistoryAddress, nqaHistoryCompletionTime, nqaHistoryFinishState, 
                nqaHistoryLastRC, nqaMpingHistoryAddressType, nqaMpingHistoryAddress, nqaMpingHistoryReceiverAddress, nqaMpingHistoryTimeStamp, 
                nqaMpingHistoryCompletionTime, nqaMpingHistoryFinishState, nqaMpingHistoryLastRC, nqaMpingHistoryFibHit, nqaMtracertHistoryAddressType, 
                nqaMtracertHistoryAddress, nqaMtracertHistoryTimeStamp, nqaMtracertHistoryCompletionTime, nqaMtracertHistoryLastRC, nqaMtracertHistoryCurQueryMode, 
                nqaMtracertHistoryQueryArrivalTime, nqaMtracertHistoryIncomingIfAddress, nqaMtracertHistoryOutgoingIfAddress, nqaMtracertHistoryPreHopRouterAddress, nqaMtracertHistoryInputPacketCount, 
                nqaMtracertHistoryOutputPacketCount, nqaMtracertHistoryTotalSGPacketCount, nqaMtracertHistoryRtgProtocol, nqaMtracertHistoryFwdTTL, nqaMtracertHistoryFwdCode, 
                nqaMtracertHistroyFinishState, nqaVplsMacTracertHistoryTimeStamp, nqaVplsMacTracertHistoryAddressType, nqaVplsMacTracertHistoryAddress, nqaVplsMacTracertHistoryCompletionTime, 
                nqaVplsMacTracertHistoryFinishState, nqaVplsMacTracertHistoryHitFlag, nqaVplsMacTracertHistoryDSCount, nqaVplsMacTracertHistorySuccessPathNode, nqaVplsMacTracertHistoryDSAddress, 
                nqaVplsMTraceHistoryResponserAddressType, nqaVplsMTraceHistoryResponserAddress, nqaVplsMTraceHistoryUpStreamAddressType, nqaVplsMTraceHistoryUpStreamAddress, nqaVplsMTraceHistoryReceivedTtl, 
                nqaVplsMTraceHistoryIGMPVersion, nqaVplsMTraceHistoryIGMPSnpgEnable, nqaVplsMTraceHistoryIGMPProxyEnable, nqaVplsMTraceHistoryIGMPRouterPortLearningEnable, nqaVplsMTraceHistoryRequireRouterAlertEnable, 
                nqaVplsMTraceHistoryForwardMode, nqaVplsMTraceHistoryHitFlag, nqaVplsMTraceHistoryPWExist, nqaVplsMTraceHistoryGroupPolicy, nqaVplsMTraceHistoryRcvQueryCount, 
                nqaVplsMTraceHistoryRcvReportCount, nqaVplsMTraceHistoryRcvLeaveCount, nqaVplsMTraceHistoryTimeStamp, nqaVplsMTraceHistoryCompletionTime, nqaVplsMTraceHistoryLastRC, 
                nqaVplsMTraceHistoryLastRSC, nqaVplsMTraceHistoryFinishState, nqaVplsMTraceHistorySuccessPathNode,nqaMacTraceHistoryTTL,nqaMacTraceHistorySeqNumber,nqaMacTraceHistoryForwarded,nqaMacTraceHistoryCompletionTime,
                nqaMacTraceHistoryTerminalMep,nqaMacTraceHistoryRelayAction,nqaMacTraceHistoryIngressAction,nqaMacTraceHistoryIngressMac,nqaMacTraceHistoryIngressIfName,nqaMacTraceHistoryEgressAction,
                nqaMacTraceHistoryEgressMac,nqaMacTraceHistoryEgressIfName
                }
            STATUS current
            DESCRIPTION 
                "Description."
            ::= { nqaGroups 5 }
            
        nqaNotificationsGroup NOTIFICATION-GROUP
            NOTIFICATIONS {
                nqaResultsProbeFailed,
                nqaResultsTestFailed,
                nqaResultsTestCompleted,
                nqaResultsThresholdNotification,
                nqaHTTPStatsProbeFailed,
                nqaHTTPStatsTestFailed,
                nqaHTTPStatsTestCompleted,
                nqaHTTPStatsThresholdNotification,
                nqaJitterStatsProbeFailed,
                nqaJitterStatsTestFailed,
                nqaJitterStatsTestCompleted,                                
                nqaFTPStatsProbeFailed,
                nqaFTPStatsTestFailed,                
                nqaFTPStatsTestCompleted,              
                nqaFTPStatsThresholdNotification,
                nqaJitterStatsRTDThresholdNotification,
                nqaJitterStatsOWDThresholdNotificationSD,
                nqaJitterStatsOWDThresholdNotificationDS,
                nqaNegotiateFailed ,
                nqaRisingAlarmNotification,
                nqaFallingAlarmNotification,
                nqaFtpSaveRecordNotification,
                nqaPppoeStatsTestFailed,
                nqaPppoeStatsTestCompleted                             
              }
            STATUS        current
            DESCRIPTION
                "The notification which are required to be supported by
                implementations of this MIB."
            ::= { nqaGroups 6 }
        nqaCollectStatsGroup OBJECT-GROUP
            OBJECTS {
                nqaJitterCollectStatsCompletions,nqaJitterCollectStatsRTDOverThresholds,nqaJitterCollectStatsOWDOverThresholdsSD,nqaJitterCollectStatsOWDOverThresholdsDS,nqaJitterCollectStatsNumOfRTT,nqaJitterCollectStatsRTTSum,
                nqaJitterCollectStatsRTTSum2Low,nqaJitterCollectStatsRTTSum2High,nqaJitterCollectStatsRTTMin,nqaJitterCollectStatsRTTMax,nqaJitterCollectStatsMinOfPositivesSD,nqaJitterCollectStatsMaxOfPositivesSD,
                nqaJitterCollectStatsNumOfPositivesSD,nqaJitterCollectStatsSumOfPositivesSD,nqaJitterCollectStatsSum2OfPositivesSDLow,nqaJitterCollectStatsSum2OfPositivesSDHigh,nqaJitterCollectStatsMinOfNegativesSD,
                nqaJitterCollectStatsMaxOfNegativesSD,nqaJitterCollectStatsNumOfNegativesSD,nqaJitterCollectStatsSumOfNegativesSD,nqaJitterCollectStatsSum2OfNegativesSDLow,nqaJitterCollectStatsSum2OfNegativesSDHigh,
                nqaJitterCollectStatsMinOfPositivesDS,nqaJitterCollectStatsMaxOfPositivesDS,nqaJitterCollectStatsNumOfPositivesDS,nqaJitterCollectStatsSumOfPositivesDS,nqaJitterCollectStatsSum2OfPositivesDSLow,
                nqaJitterCollectStatsSum2OfPositivesDSHigh,nqaJitterCollectStatsMinOfNegativesDS,nqaJitterCollectStatsMaxOfNegativesDS,nqaJitterCollectStatsNumOfNegativesDS,nqaJitterCollectStatsSumOfNegativesDS,
                nqaJitterCollectStatsSum2OfNegativesDSLow,nqaJitterCollectStatsSum2OfNegativesDSHigh,nqaJitterCollectStatsMaxDelaySD,nqaJitterCollectStatsMaxDelayDS,nqaJitterCollectStatsNumOfOWD,nqaJitterCollectStatsOWSumSD,
                nqaJitterCollectStatsOWSumDS,nqaJitterCollectStatsPacketLossSD,nqaJitterCollectStatsPacketLossDS,nqaJitterCollectStatsPacketLossUnknown,nqaJitterCollectStatsPacketOutOfSequences,nqaJitterCollectStatsPacketLossRatio,
                nqaJitterCollectStatsErrors,nqaJitterCollectStatsBusies,nqaJitterCollectStatsTimeouts,nqaJitterCollectStatsProbeResponses,nqaJitterCollectStatsSentProbes,nqaJitterCollectStatsDrops,nqaJitterCollectStatsRTTAvg,
                nqaJitterCollectStatsAvgJitter,nqaJitterCollectStatsAvgJitterSD,nqaJitterCollectStatsAvgJitterDS,nqaJitterCollectStatsJitterOut,nqaJitterCollectStatsJitterIn,
                nqaJitterCollectStatsMinDelaySD,nqaJitterCollectStatsMinDelayDS,nqaJitterCollectStatsAvgDelaySD,nqaJitterCollectStatsAvgDelayDS, nqaJitterCollectStatsPktRewriteNum, nqaJitterCollectStatsPktRewriteRatio, 
                nqaJitterCollectStatsPktDisorderNum, nqaJitterCollectStatsPktDisorderRatio, nqaJitterCollectStatsFragPktDisorderNum, nqaJitterCollectStatsFragPktDisorderRatio
            }
            STATUS current
            DESCRIPTION
                "Description."
            ::= { nqaGroups 7 } 

          nqaAlarmGroup OBJECT-GROUP
            OBJECTS { 
            nqaMaxAlarmNum,nqaMaxEventNum,nqaAlarmVariable,nqaAlarmSampleType,nqaAlarmValue,nqaAlarmStartUpNqaAlarm,nqaAlarmRisingThreshold,nqaAlarmDescription,
nqaAlarmFallingThreshold,nqaAlarmRisingEventIndex,nqaAlarmFallingEventIndex,nqaAlarmStatus,nqaEventDescription,nqaEventAdminName,nqaEventOperationTag,nqaEventType,nqaEventStatus}
            STATUS current
            DESCRIPTION
                "Description."
            ::= { nqaGroups 8 } 

         nqaFtpSaveRecordGroup OBJECT-GROUP
            OBJECTS { 
                nqaFtpSaveRecordEnable,nqaFtpSaveRecordIpAddr,nqaFtpSaveRecordVrfName,nqaFtpSaveRecordUserName,nqaFtpSaveRecordPassword,nqaFtpSaveRecordFileName,nqaFtpSaveRecordItemNum,nqaFtpSaveRecordTime,nqaFtpSaveRecordNotificationEnable,nqaFtpSaveRecordLastFileName}
            STATUS current                                                                                                                                                                                               
            DESCRIPTION
                "Description."
            ::= { nqaGroups 9 }

        nqaCompliances OBJECT IDENTIFIER ::= { nqaConformance 2 }
        
        nqaCompliance MODULE-COMPLIANCE
            STATUS current
            DESCRIPTION 
                "Description."
            MODULE -- this module
                MANDATORY-GROUPS { nqaBaseGroup, nqaAdminGroup, nqaStatsGroup,nqaAlarmGroup,nqaFtpSaveRecordGroup}
            ::= { nqaCompliances 1 }
        
        nqaCollectStats OBJECT IDENTIFIER ::= { nqa 8 }
        nqaJitterCollectStatsTable OBJECT-TYPE
            SYNTAX SEQUENCE OF NqaJitterCollectStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the rolling accumulated history of the Jitter operation."
            ::= { nqaCollectStats 1 }
        
        nqaJitterCollectStatsEntry OBJECT-TYPE
            SYNTAX NqaJitterCollectStatsEntry
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "A table which contains the rolling accumulated history of the Jitter operation.
               This entry is created only if the nqaAdminCtrlType is jitterAppl."
            INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaJitterCollectStatsIndex }
            ::= { nqaJitterCollectStatsTable 1 }
        NqaJitterCollectStatsEntry ::=
            SEQUENCE {
                nqaJitterCollectStatsIndex
                    Integer32,
                nqaJitterCollectStatsCompletions
                    Counter32,
                nqaJitterCollectStatsRTDOverThresholds
                    Counter32,
                nqaJitterCollectStatsOWDOverThresholdsSD
                    Counter32,    
                nqaJitterCollectStatsOWDOverThresholdsDS
                    Counter32,
                nqaJitterCollectStatsNumOfRTT
                    Counter32,
                nqaJitterCollectStatsRTTSum
                    Counter32,
                nqaJitterCollectStatsRTTSum2Low
                    Counter32,
                nqaJitterCollectStatsRTTSum2High
                    Counter32,
                nqaJitterCollectStatsRTTMin
                    Gauge32,
                nqaJitterCollectStatsRTTMax
                    Gauge32,
                nqaJitterCollectStatsMinOfPositivesSD
                    Gauge32,
                nqaJitterCollectStatsMaxOfPositivesSD
                    Gauge32,
                nqaJitterCollectStatsNumOfPositivesSD
                    Counter32,
                nqaJitterCollectStatsSumOfPositivesSD
                    Counter32,
                nqaJitterCollectStatsSum2OfPositivesSDLow
                    Counter32,
                nqaJitterCollectStatsSum2OfPositivesSDHigh
                    Counter32,
                nqaJitterCollectStatsMinOfNegativesSD
                    Gauge32,
                nqaJitterCollectStatsMaxOfNegativesSD
                    Gauge32,
                nqaJitterCollectStatsNumOfNegativesSD
                    Counter32,
                nqaJitterCollectStatsSumOfNegativesSD
                    Counter32,
                nqaJitterCollectStatsSum2OfNegativesSDLow
                    Counter32,
                nqaJitterCollectStatsSum2OfNegativesSDHigh
                    Counter32,
                nqaJitterCollectStatsMinOfPositivesDS
                    Gauge32,
                nqaJitterCollectStatsMaxOfPositivesDS
                    Gauge32,
                nqaJitterCollectStatsNumOfPositivesDS
                    Counter32,
                nqaJitterCollectStatsSumOfPositivesDS
                    Counter32,
                nqaJitterCollectStatsSum2OfPositivesDSLow
                    Counter32,
                nqaJitterCollectStatsSum2OfPositivesDSHigh
                    Counter32,
                nqaJitterCollectStatsMinOfNegativesDS
                    Gauge32,
                nqaJitterCollectStatsMaxOfNegativesDS
                    Gauge32,
                nqaJitterCollectStatsNumOfNegativesDS
                    Counter32,
                nqaJitterCollectStatsSumOfNegativesDS
                    Counter32,
                nqaJitterCollectStatsSum2OfNegativesDSLow
                    Counter32,
                nqaJitterCollectStatsSum2OfNegativesDSHigh
                    Counter32,
                nqaJitterCollectStatsMaxDelaySD
                    Gauge32,
                nqaJitterCollectStatsMaxDelayDS
                    Gauge32,                        
                nqaJitterCollectStatsNumOfOWD
                    Counter32,
                nqaJitterCollectStatsOWSumSD
                    Counter32,
                nqaJitterCollectStatsOWSumDS
                    Counter32,
                nqaJitterCollectStatsPacketLossSD
                    Counter32,
                nqaJitterCollectStatsPacketLossDS
                    Counter32,
                nqaJitterCollectStatsPacketLossUnknown
                    Counter32,
                nqaJitterCollectStatsPacketOutOfSequences
                    Counter32,
                nqaJitterCollectStatsPacketLossRatio
                    Gauge32,
                nqaJitterCollectStatsErrors
                    Counter32,
                nqaJitterCollectStatsBusies
                    Counter32,   
                nqaJitterCollectStatsTimeouts
                    Counter32,   
                nqaJitterCollectStatsProbeResponses
                    Counter32,   
                nqaJitterCollectStatsSentProbes
                    Counter32,   
                nqaJitterCollectStatsDrops
                    Counter32,                                                 
                nqaJitterCollectStatsRTTAvg
                    Gauge32,
                nqaJitterCollectStatsAvgJitter
                    Gauge32,
                nqaJitterCollectStatsAvgJitterSD   
                    Gauge32,
                nqaJitterCollectStatsAvgJitterDS
                    Gauge32,
                nqaJitterCollectStatsJitterOut
                    OCTET STRING,
                nqaJitterCollectStatsJitterIn      
                    OCTET STRING,
                nqaJitterCollectStatsMinDelaySD
                    Gauge32,
                nqaJitterCollectStatsMinDelayDS
                    Gauge32,                        
                nqaJitterCollectStatsAvgDelaySD
                    Gauge32,
                nqaJitterCollectStatsAvgDelayDS
                    Gauge32,
                nqaJitterCollectStatsPktRewriteNum
                    Counter32,
                nqaJitterCollectStatsPktRewriteRatio
                    Gauge32,
                nqaJitterCollectStatsPktDisorderNum
                    Counter32,
                nqaJitterCollectStatsPktDisorderRatio
                    Gauge32,
                nqaJitterCollectStatsFragPktDisorderNum
                    Counter32,
                nqaJitterCollectStatsFragPktDisorderRatio
                    Gauge32
            }
        nqaJitterCollectStatsIndex OBJECT-TYPE
            SYNTAX Integer32 (1..'7FFFFFFF'h)
            MAX-ACCESS not-accessible
            STATUS current
            DESCRIPTION
                "The third index of jitter collection statistics table."
            ::= { nqaJitterCollectStatsEntry 1 }
        nqaJitterCollectStatsCompletions OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of the tests that have completed successfully."
            ::= { nqaJitterCollectStatsEntry 2 }
        nqaJitterCollectStatsRTDOverThresholds OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate RTD (Round Trip Delay) threshold."
            ::= { nqaJitterCollectStatsEntry 3 }
        nqaJitterCollectStatsOWDOverThresholdsSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate OWD (One Way Delay) threshold from source to destination."
            ::= { nqaJitterCollectStatsEntry 4 }                    
        nqaJitterCollectStatsOWDOverThresholdsDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of tests that violate OWD (One Way Delay) threshold from destination to source."
            ::= { nqaJitterCollectStatsEntry 5 }  
        nqaJitterCollectStatsNumOfRTT OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of RTTs that are successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 6 }
        nqaJitterCollectStatsRTTSum OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of RTTs that are successfully measured."
            ::= { nqaJitterCollectStatsEntry 7 }
        nqaJitterCollectStatsRTTSum2Low OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of RTTs that are successfully measured by tests (low order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 8 }
        nqaJitterCollectStatsRTTSum2High OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of RTTs that are successfully measured by tests (high order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 9 }
        
        nqaJitterCollectStatsRTTMin OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of RTTs that were successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 10 }
        
        nqaJitterCollectStatsRTTMax OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of RTTs that were successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 11 }
        
        nqaJitterCollectStatsMinOfPositivesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all positive jitter values from source to destination."
            ::= { nqaJitterCollectStatsEntry 12 }
        
        nqaJitterCollectStatsMaxOfPositivesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all positive jitter values from source to destination."
            ::= { nqaJitterCollectStatsEntry 13 }
        
        nqaJitterCollectStatsNumOfPositivesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all positive jitter values from source to destination."
            ::= { nqaJitterCollectStatsEntry 14 }
        
        nqaJitterCollectStatsSumOfPositivesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all positive jitter values from source to destination."
            ::= { nqaJitterCollectStatsEntry 15 }
        
        nqaJitterCollectStatsSum2OfPositivesSDLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter values 
                 from source to destination (low order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 16 }
        
        nqaJitterCollectStatsSum2OfPositivesSDHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter values 
                 from source to destination (high order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 17 }
        
        nqaJitterCollectStatsMinOfNegativesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all negative jitter values from 
                 source to destination."
            ::= { nqaJitterCollectStatsEntry 18 }
        
        nqaJitterCollectStatsMaxOfNegativesSD OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all negative jitter values from 
                 source to destination."
            ::= { nqaJitterCollectStatsEntry 19 }
        
        nqaJitterCollectStatsNumOfNegativesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all negative jitter values from source to destination."
            ::= { nqaJitterCollectStatsEntry 20 }
        
        nqaJitterCollectStatsSumOfNegativesSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all negative jitter values from source 
                 to destination."
            ::= { nqaJitterCollectStatsEntry 21 }
        
        nqaJitterCollectStatsSum2OfNegativesSDLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values from source 
                 to destination (low order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 22 }
        
        nqaJitterCollectStatsSum2OfNegativesSDHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values from source 
                 to destination (high order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 23 }
        
        nqaJitterCollectStatsMinOfPositivesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all positive jitter values from 
                 destination to source."
            ::= { nqaJitterCollectStatsEntry 24 }
        
        nqaJitterCollectStatsMaxOfPositivesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all positive jitter values from 
                 destination to source."
            ::= { nqaJitterCollectStatsEntry 25 }
        
        nqaJitterCollectStatsNumOfPositivesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all positive jitter values from destination to source."
            ::= { nqaJitterCollectStatsEntry 26 }
        
        nqaJitterCollectStatsSumOfPositivesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all positive jitter values from 
                 destination to source."
            ::= { nqaJitterCollectStatsEntry 27 }
        
        nqaJitterCollectStatsSum2OfPositivesDSLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter 
                 values from destination to source (low order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 28 }
        
        nqaJitterCollectStatsSum2OfPositivesDSHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all positive jitter 
                 values from destination to source (high order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 29 }
        
        nqaJitterCollectStatsMinOfNegativesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The minimum of absolute value of all negative jitter values 
                 from destination to source."
            ::= { nqaJitterCollectStatsEntry 30 }
        
        nqaJitterCollectStatsMaxOfNegativesDS OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The maximum of absolute value of all negative jitter values 
                 from destination to source."
            ::= { nqaJitterCollectStatsEntry 31 }
        
        nqaJitterCollectStatsNumOfNegativesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of all negative jitter values from destination 
                 to source."
            ::= { nqaJitterCollectStatsEntry 32 }
        
        nqaJitterCollectStatsSumOfNegativesDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of absolute value of all negative jitter values 
                 from destination to source."
            ::= { nqaJitterCollectStatsEntry 33 }
        
        nqaJitterCollectStatsSum2OfNegativesDSLow OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values 
                 from destination to source (low order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 34 }
        
        nqaJitterCollectStatsSum2OfNegativesDSHigh OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of squares of absolute value of all negative values 
                 from destination to source (high order 32 bits)."
            ::= { nqaJitterCollectStatsEntry 35 }
        
        nqaJitterCollectStatsMaxDelaySD OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The maximum of all OWD (One Way Delay) from source to destination."
            ::= { nqaJitterCollectStatsEntry 36 }
    
        nqaJitterCollectStatsMaxDelayDS OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The maximum of all OWD (One Way Delay) from destination to source."
            ::= { nqaJitterCollectStatsEntry 37 }                       
            
        nqaJitterCollectStatsNumOfOWD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of OWDs that were successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 38 }

        nqaJitterCollectStatsOWSumSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of OWDs that were successfully measured by tests from 
                 source to destination."
            ::= { nqaJitterCollectStatsEntry 39 }

        nqaJitterCollectStatsOWSumDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The sum of OWDs that were successfully measured by tests from 
                 destination to source."
            ::= { nqaJitterCollectStatsEntry 40 }

        nqaJitterCollectStatsPacketLossSD OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets lost when sent from source to destination."
            ::= { nqaJitterCollectStatsEntry 41 }
        
        nqaJitterCollectStatsPacketLossDS OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets lost when sent from destination to source."
            ::= { nqaJitterCollectStatsEntry 42 }
        
        nqaJitterCollectStatsPacketLossUnknown OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets lost for which we can't determine the direction."
            ::= { nqaJitterCollectStatsEntry 43 }
            
        nqaJitterCollectStatsPacketOutOfSequences OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets arrived out of sequence."
            ::= { nqaJitterCollectStatsEntry 44 }
        
         nqaJitterCollectStatsPacketLossRatio  OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The ratio of the packets lost to all packets sent in the test."
            ::= { nqaJitterCollectStatsEntry 45 }  

        nqaJitterCollectStatsErrors OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of errors occurred in the test."
            ::= { nqaJitterCollectStatsEntry 46 }
        
        nqaJitterCollectStatsBusies OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of occasions when a test couldn't be initialized because 
                 the previous test has not completed."
            ::= { nqaJitterCollectStatsEntry 47 }      
            
        nqaJitterCollectStatsTimeouts OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of responses arrived over the time."
            ::= { nqaJitterCollectStatsEntry 48 }    
            
        nqaJitterCollectStatsProbeResponses OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of responses from echo-server for the packets sent by the test."
            ::= { nqaJitterCollectStatsEntry 49 }    
            
        nqaJitterCollectStatsSentProbes OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets sent in the test."
            ::= { nqaJitterCollectStatsEntry 50 }    
            
        nqaJitterCollectStatsDrops OBJECT-TYPE
            SYNTAX Counter32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The number of packets that were sent failed in the test."
            ::= { nqaJitterCollectStatsEntry 51 }                
                                                                                               
         nqaJitterCollectStatsRTTAvg  OBJECT-TYPE
            SYNTAX   Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of RTTs that were successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 52 }  

         nqaJitterCollectStatsAvgJitter   OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of jitter values that were successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 53 }  

         nqaJitterCollectStatsAvgJitterSD   OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of jitter values from source to destination that were 
                 successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 54 }  

         nqaJitterCollectStatsAvgJitterDS   OBJECT-TYPE
            SYNTAX Gauge32
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "The average of jitter values from destination to source that were 
                 successfully measured by tests."
            ::= { nqaJitterCollectStatsEntry 55 }  

        nqaJitterCollectStatsJitterOut OBJECT-TYPE
            SYNTAX      OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Jitter (RFC1889) at responder."
            ::= { nqaJitterCollectStatsEntry 56 }
                       
        nqaJitterCollectStatsJitterIn OBJECT-TYPE
            SYNTAX      OCTET STRING
            MAX-ACCESS read-only
            STATUS current
            DESCRIPTION
                "Jitter (RFC1889) at sender."
            ::= { nqaJitterCollectStatsEntry 57 } 

        nqaJitterCollectStatsMinDelaySD OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The minimum of all OWD (One Way Delay) from source to destination."
            ::= { nqaJitterCollectStatsEntry 58 }

        nqaJitterCollectStatsMinDelayDS OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The minimum of all OWD (One Way Delay) from destination to source."
            ::= { nqaJitterCollectStatsEntry 59 }

        nqaJitterCollectStatsAvgDelaySD OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of all OWD (One Way Delay) from source to destination."
            ::= { nqaJitterCollectStatsEntry 60 }

        nqaJitterCollectStatsAvgDelayDS OBJECT-TYPE
            SYNTAX      Gauge32
            UNITS       "milliseconds"
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The average of all OWD (One Way Delay) from destination to source."
            ::= { nqaJitterCollectStatsEntry 61 }
            
        nqaJitterCollectStatsPktRewriteNum OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of all the packets have been rewrited."
            ::= { nqaJitterCollectStatsEntry 62 }

        nqaJitterCollectStatsPktRewriteRatio OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of all the packets have been rewrited."
            ::= { nqaJitterCollectStatsEntry 63 }

        nqaJitterCollectStatsPktDisorderNum OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of all the packets have been disordered."
            ::= { nqaJitterCollectStatsEntry 64 }            
            
        nqaJitterCollectStatsPktDisorderRatio OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of all the packets have been disordered."
            ::= { nqaJitterCollectStatsEntry 65 }            
            
        nqaJitterCollectStatsFragPktDisorderNum OBJECT-TYPE
            SYNTAX      Counter32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The number of all the packets whose fragments have been disordered."
            ::= { nqaJitterCollectStatsEntry 66 }            
                        
        nqaJitterCollectStatsFragPktDisorderRatio OBJECT-TYPE
            SYNTAX      Gauge32
            MAX-ACCESS  read-only
            STATUS      current
            DESCRIPTION
                "The ratio of all the packets whose fragments have been disordered."
            ::= { nqaJitterCollectStatsEntry 67 }               

            nqaAlarm OBJECT IDENTIFIER ::= { nqa 9 } 

              nqaMaxAlarmNum OBJECT-TYPE
                        SYNTAX  Integer32 (1..2147483647)
                        MAX-ACCESS read-only
                        STATUS current
                        DESCRIPTION
                                 "Maximum number of alarms."
                        ::= { nqaAlarm 1 }

               nqaMaxEventNum OBJECT-TYPE
                        SYNTAX  Integer32 (1..2147483647)
                        MAX-ACCESS read-only
                        STATUS current
                        DESCRIPTION
                                 "Maximum number of events that alarms can trigger."
                        ::= { nqaAlarm 2} 

                nqaAlarmTable OBJECT-TYPE
                        SYNTAX SEQUENCE OF NqaAlarmEntry
                        MAX-ACCESS not-accessible
                        STATUS current
                        DESCRIPTION
                                 "A list of alarm entries."
                        ::= { nqaAlarm 3 }

                nqaAlarmEntry OBJECT-TYPE
                        SYNTAX NqaAlarmEntry
                        MAX-ACCESS not-accessible
                        STATUS current
                        DESCRIPTION
                                 "A list of parameters that set up a periodic checking for alarm conditions."                        
                        INDEX { nqaAdminCtrlOwnerIndex, nqaAdminCtrlTestName, nqaAlarmIndex }
                        ::= { nqaAlarmTable 1 }

                
                NqaAlarmEntry ::=
                        SEQUENCE { 
                                nqaAlarmIndex
                                        Integer32,
                                nqaAlarmVariable
                                        INTEGER,
                                nqaAlarmSampleType
                                        INTEGER,
                                nqaAlarmValue
                                        Integer32,
                                nqaAlarmStartUpNqaAlarm 
                                        INTEGER,
                                nqaAlarmRisingThreshold
                                        Integer32,
                                nqaAlarmFallingThreshold
                                        Integer32,
                                nqaAlarmRisingEventIndex
                                        Integer32,
                                nqaAlarmFallingEventIndex
                                        Integer32,
                                nqaAlarmDescription
                                        OCTET STRING,        
                                nqaAlarmStatus
                                        RowStatus
                         }

                nqaAlarmIndex OBJECT-TYPE
                        SYNTAX Integer32 (1..65535)
                        MAX-ACCESS not-accessible
                        STATUS current
                        DESCRIPTION
                                 "Uniquely identifies a row in the nqaAlarmTable."                        
                        ::= { nqaAlarmEntry 1 }

                nqaAlarmVariable OBJECT-TYPE
                        SYNTAX INTEGER
                                {
                                 rttAvg(1),
                                 lostPacketRatio(2),
                                 packetLossSd(3),
                                 packetLossDs(4),
                                 jitterRavg(5),
                                 jitterSdAvg(6),
                                 jitterDsAvg(7) 
                                }
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "The type of the particular variable to be sampled."                   
                        ::= { nqaAlarmEntry 11 }

                
                nqaAlarmSampleType OBJECT-TYPE
                        SYNTAX INTEGER
                                {
                                delta(1),
                                absolute(2)
                                }
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "delta(1): relative value
  absolute(2): absolute value
  Relative value: indicates the relative value to the last sampling.  
  Currently, only absolute(2) is supported."
                        ::= { nqaAlarmEntry 12 }
   
                nqaAlarmValue OBJECT-TYPE
                        SYNTAX Integer32 (0..2147483647)
                        MAX-ACCESS read-only
                        STATUS current
                        DESCRIPTION
                                 "The actual value of the monitored object is compared with the upper limit and lower limit."
                        ::= { nqaAlarmEntry 13 }

                nqaAlarmStartUpNqaAlarm OBJECT-TYPE
                        SYNTAX INTEGER
                                {
                                risingAlarm(1),
                                fallingAlarm(2),
                                risingOrFallingAlarm(3)
                                }
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "The alarm that may be sent when this entry is first
 set to valid.  If the first sample after this entry
 becomes valid is greater than or equal to the
 risingThreshold and alarmStartupAlarm is equal to
 risingAlarm(1) or risingOrFallingAlarm(3), then a single
 rising alarm will be generated.  If the first sample
 after this entry becomes valid is less than or equal
 to the fallingThreshold and alarmStartupAlarm is equal
 to fallingAlarm(2) or risingOrFallingAlarm(3), then a
 single falling alarm will be generated."
                        ::= { nqaAlarmEntry 14 }       

                nqaAlarmRisingThreshold OBJECT-TYPE
                        SYNTAX Integer32 (0..2147483647)
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Set the upper limit of the alarm."
                        ::= { nqaAlarmEntry 15 }

                nqaAlarmFallingThreshold OBJECT-TYPE
                        SYNTAX Integer32 (0..2147483647)
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Set the lower limit of the alarm."
                        ::= { nqaAlarmEntry 16 }        

                nqaAlarmRisingEventIndex OBJECT-TYPE
                        SYNTAX Integer32 (1..65535)
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Index of the event triggered when the node value exceeds the upper limit."
                        ::= { nqaAlarmEntry 17 } 

                nqaAlarmFallingEventIndex OBJECT-TYPE
                        SYNTAX Integer32 (1..65535)
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Index of the event triggered when the node value is under the lower limit."
                        ::= { nqaAlarmEntry 18 }

                 nqaAlarmDescription OBJECT-TYPE
                        SYNTAX OCTET STRING (SIZE (0..127))
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Alarm Description."                       
                        ::= { nqaAlarmEntry 19}

                nqaAlarmStatus OBJECT-TYPE
                        SYNTAX RowStatus
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "The status of the alarm row."                        
                        ::= { nqaAlarmEntry 51 }

                nqaEventTable OBJECT-TYPE
                 SYNTAX SEQUENCE OF NqaEventEntry
                 MAX-ACCESS not-accessible
                 STATUS current
                 DESCRIPTION
                          "A list of events to be generated."
                  ::= { nqaAlarm 4 }

                
                nqaEventEntry OBJECT-TYPE
                        SYNTAX NqaEventEntry
                        MAX-ACCESS not-accessible
                        STATUS current
                        DESCRIPTION
                                 "A set of parameters that describe an event to be generated when certain conditions are met. "
                        INDEX { nqaEventIndex }
                        ::= { nqaEventTable 1 }

                
                NqaEventEntry ::=
                        SEQUENCE { 
                                nqaEventIndex
                                        Integer32,
                                nqaEventType
                                        INTEGER, 
                                nqaEventDescription
                                        OCTET STRING,
                                nqaEventAdminName
                                        OCTET STRING,
                                nqaEventOperationTag
                                        OCTET STRING,
                                nqaEventStatus
                                        RowStatus
                         }

                nqaEventIndex OBJECT-TYPE
                        SYNTAX Integer32 (1..65535)
                        MAX-ACCESS not-accessible
                        STATUS current
                        DESCRIPTION
                                 "Uniquely identifies a row in the nqaEventTable."                        
                        ::= { nqaEventEntry 1 }

                nqaEventType OBJECT-TYPE
                        SYNTAX INTEGER
                                {
                                none(1),
                                log(2),
                                trap(3),
                                logAndTrap(4),
                                linkage(5)
                                }
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Event type, that is, behaviors for processing events:
                                  none(1): no behavior
                                  log(2): keeping logs
                                  trap(3): sending trap messages
                                  logandtrap(4): keeping logs and sending trap messages
                                  linkage(5): start the linkaged test-instance."
                        ::= { nqaEventEntry 11 }
                          
                nqaEventDescription OBJECT-TYPE
                        SYNTAX OCTET STRING (SIZE (0..127))
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Event description."                       
                        ::= { nqaEventEntry 12 }

                nqaEventAdminName OBJECT-TYPE
                        SYNTAX OCTET STRING (SIZE (0..32))
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Administrator name of event test instance."                  
                        ::= { nqaEventEntry 13 }                        
                
                nqaEventOperationTag OBJECT-TYPE
                        SYNTAX OCTET STRING (SIZE (0..32))
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "Operation tag of event test instance."                       
                        ::= { nqaEventEntry 14 }

                 nqaEventStatus OBJECT-TYPE
                        SYNTAX RowStatus
                        MAX-ACCESS read-create
                        STATUS current
                        DESCRIPTION
                                 "The status of the event row."                        
                        ::= { nqaEventEntry 51} 
                        
                 nqaSaveRecord OBJECT IDENTIFIER ::= { nqa 10} 

                 nqaFtpSaveRecordEnable  OBJECT-TYPE
              SYNTAX  EnabledStatus
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "Identifies whether saving test results to the FTP server through FTP is enabled.
enable(1)
disable(2)
By default, the value is disable(2)."
              ::= { nqaSaveRecord  1}           
              
                 nqaFtpSaveRecordIpAddr OBJECT-TYPE
              SYNTAX  InetAddress
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "Set the IP address of the FTP server to which test results are saved."
              ::= { nqaSaveRecord 2}        

                 nqaFtpSaveRecordVrfName OBJECT-TYPE
              SYNTAX  OCTET STRING (SIZE(0..31))
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "Set the name of the VRF instance for the FTP server to which test results are saved."
              ::= { nqaSaveRecord  3}
              
                 nqaFtpSaveRecordUserName OBJECT-TYPE
              SYNTAX  OCTET STRING (SIZE(0..32))
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "Set the user name of the FTP server to which test results are saved."
              ::= { nqaSaveRecord 4}
                 
                 nqaFtpSaveRecordPassword OBJECT-TYPE
              SYNTAX  OCTET STRING (SIZE(0..32))
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "Set the password used when the test results are saved to the FTP server."
              ::= { nqaSaveRecord  5}        
              
                  nqaFtpSaveRecordFileName OBJECT-TYPE
              SYNTAX  OCTET STRING (SIZE(0..200))
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "Set the file name of used by the FTP server to save test results."
              ::= { nqaSaveRecord  6}       
              
                 nqaFtpSaveRecordItemNum OBJECT-TYPE
              SYNTAX Integer32 (10000..2147483647)
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "The value of this object identifies the number of NQA test results that can be saved in a file created on the FTP server. The default value is 100000."     
              -- DEFVAL { 100000 }         
              ::= { nqaSaveRecord  7} 
                
                 nqaFtpSaveRecordTime OBJECT-TYPE
              SYNTAX Integer32 (1..43200)      
              UNITS "minutes"
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "The value of this object identifies the duration that NQA test results can be saved in a file created on the FTP server. The default duration is 60 minutes."     
              -- DEFVAL { 60}         
              ::= { nqaSaveRecord  8}  
                                            
                 nqaFtpSaveRecordNotificationEnable  OBJECT-TYPE
              SYNTAX EnabledStatus
              MAX-ACCESS read-write
              STATUS current
              DESCRIPTION
                       "Set whether to send trap messages when saving test results to the FTP server is completed.
                        By default, the value is disable(2)."
              ::= { nqaSaveRecord  9}                                             
                                            
                  nqaFtpSaveRecordLastFileName OBJECT-TYPE
              SYNTAX  OCTET STRING (SIZE(0..220))
              MAX-ACCESS read-only
              STATUS current
              DESCRIPTION
                       "Set the name of the file used to save the last test record to the FTP server."
              ::= { nqaSaveRecord  10}           
    END

--
-- NQA-MIB.mib
-- 
