
-- ==================================================================
-- Copyright (C) 2002 by  HUAWEI TECHNOLOGIES. All rights reserved.
-- 
-- Description: MIB generated by MG-SOFT Visual MIB Builder Version 4.0 Build 349
-- Reference:
-- Version: V1.0
-- History:
--      V1.0 
-- ==================================================================

	HUAWEI-EPON-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			hwDatacomm, huaweiUtility			
				FROM HUAWEI-MIB			
			ifIndex			
				FROM IF-MIB			
			OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			Ip<PERSON>ddress, Integer32, Counter64, OBJECT-TYPE, MODULE-<PERSON>EN<PERSON><PERSON>, 
			NOTIFICATION-TYPE			
				FROM SNMPv2-<PERSON><PERSON>ddress, RowStatus, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC
		        EnabledStatus
                                FROM P-BRIDGE-MIB;
	
	
		hwEponMIB MODULE-IDENTITY 
			LAST-UPDATED "200904240000Z"
			ORGANIZATION 
				"Research & Development Dept, AN, Huawei Technologies Co.,Ltd."
			CONTACT-INFO 
				"Block 4, R&D Building,
				Huawei Longgang Production Base,
				Shenzhen,   P.R.C.
				http://www.huawei.com
				Zip:518129"
			DESCRIPTION 
				"Epon MIB, contain Epon."
			::= { hwDatacomm 179 }

		
	
--
-- Type definitions
--
	
		DisplayString ::= OCTET STRING

	
--
-- Textual conventions
--
	
		PortList ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Each octet within this value specifies a set of eight
				ports, with the first octet specifying ports 1 through
				8, the second octet specifying ports 9 through 16 and others.
				Within each octet, the most significant bit represents
				the lowest numbered port, and the least significant bit
				represents the highest numbered port.  Thus, each port
				of the bridge is represented by a single bit within the
				value of this object.  If that bit has a value of '1'
				then that port is included in the set of ports; the port
				is not included if its bit has a value of '0'."
			SYNTAX OCTET STRING

	
--
-- Node definitions
--
	
		hwEponObjects OBJECT IDENTIFIER ::= { hwEponMIB 1 }

		
		hwEponGlobalObjects OBJECT IDENTIFIER ::= { hwEponObjects 1 }

		
		hwEponAutoFindOnuAge OBJECT-TYPE
			SYNTAX Integer32 (100..300)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The aging time of the auto-found ONU, ranging from 100(s) to 300(s)"
			DEFVAL { 300 }
			::= { hwEponGlobalObjects 1 }

		
		hwEponCtcOuiId OBJECT-TYPE
			SYNTAX Integer32(0..4294967295)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The OUI identifier"
			DEFVAL { 111111 }
			::= { hwEponGlobalObjects 2 }

		
		hwEponChangePasswordAge OBJECT-TYPE
			SYNTAX Integer32 (1..254)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The interval time of ONU's password renew, ranging from 1(s) to 254(s)"
			DEFVAL { 10 }
			::= { hwEponGlobalObjects 3 }

		
		hwEponControlObjects OBJECT IDENTIFIER ::= { hwEponObjects 2 }

		
		hwEponOltControlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOltControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The control table of the EPON OLT."
			::= { hwEponControlObjects 1 }

		
		hwEponOltControlEntry OBJECT-TYPE
			SYNTAX HwEponOltControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of the control table of the EPON OLT."
			INDEX { ifIndex }
			::= { hwEponOltControlTable 1 }

		
		HwEponOltControlEntry ::=
			SEQUENCE { 
				hwEponOltControlfarthest
					Integer32,
				hwEponOltControlAutofindOnuEnable
					EnabledStatus,
				hwEponOltControlStatus
					INTEGER,
				hwEponOltControlUpStreamBandWidth
					Counter64,
				hwEponOltControlDownStreamBandWidth
					Counter64
			 }

		hwEponOltControlfarthest OBJECT-TYPE
			SYNTAX Integer32(0..40)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This is the distance of the fiber between the OLT and the farthest ONU, 
				ranging from 0km to 40km."
			DEFVAL { 20 }
			::= { hwEponOltControlEntry 1 }

		
		hwEponOltControlAutofindOnuEnable OBJECT-TYPE
			SYNTAX EnabledStatus				
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"ONU auto-find is enabled."
			::= { hwEponOltControlEntry 2 }

		
		hwEponOltControlStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The Epon port status in the OLT.
				up (1): normal
				down (2): fault"
			::= { hwEponOltControlEntry 3 }

		
		hwEponOltControlUpStreamBandWidth OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The up-stream bandwidth of the OLT port."
			::= { hwEponOltControlEntry 4 }

		
		hwEponOltControlDownStreamBandWidth OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The down-stream bandwidth of the OLT port."
			::= { hwEponOltControlEntry 5 }

		
		hwEponOnuConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table is used to create, modify, delete or query an ONU."
			::= { hwEponControlObjects 2 }

		
		hwEponOnuConfigEntry OBJECT-TYPE
			SYNTAX HwEponOnuConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU configuration entry."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOnuConfigTable 1 }

		
		HwEponOnuConfigEntry ::=
			SEQUENCE { 
				hwEponOnuIndex
					Integer32,
				hwEponOnuId
					Integer32,
				hwEponOnuAuthMode
					INTEGER,
				hwEponOnuMacAddress
					MacAddress,
				hwEponOnuPassword
					DisplayString,
				hwEponOnuTimeout
					Integer32,
				hwEponOnuManagementMode
					INTEGER,
				hwEponOnuLineProfName
					DisplayString,
				hwEponOnuServiceProfName
					DisplayString,
				hwEponOnuSnmpProfName
					DisplayString,
				hwEponOnuDescription
					DisplayString,
				hwEponOnuActiveStatus
			                INTEGER,				
				hwEponOnuRowStatus
					RowStatus
			 }

		hwEponOnuIndex OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the index."
			::= { hwEponOnuConfigEntry 1 }

		hwEponOnuId OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This is the ONU ID."
			::= { hwEponOnuConfigEntry 2 }
		
		hwEponOnuAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				sn(1),				
				alwaysOn(3),
				onceOn(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The authentication mode for the ONU."
			::= { hwEponOnuConfigEntry 3 }

		
		hwEponOnuMacAddress OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This is the MAC address of the ONU."
			::= { hwEponOnuConfigEntry 4 }

		
		hwEponOnuPassword OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..10))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This is the password of the ONU ."
			::= { hwEponOnuConfigEntry 5 }

		
		hwEponOnuTimeout OBJECT-TYPE
			SYNTAX Integer32 (1..168)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The online duration of the ONU, ranging from 1 to 168 hours."
			::= { hwEponOnuConfigEntry 6 }

		
		hwEponOnuManagementMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				oam(1),
				snmp(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The ONU managemet mode, including OAM mode and SNMP mode."
			DEFVAL { 1 }
			::= { hwEponOnuConfigEntry 7 }

		
		hwEponOnuLineProfName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The line profile name, which is bound to ONU."
			::= { hwEponOnuConfigEntry 8 }

		
		hwEponOnuServiceProfName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The service profile name, which is bound to ONU."
			::= { hwEponOnuConfigEntry 9 }

		
		hwEponOnuSnmpProfName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The SNMP profile name, which is bound to ONU."
			::= { hwEponOnuConfigEntry 10 }

		
		hwEponOnuDescription OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..64))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The description of the ONU."
			::= { hwEponOnuConfigEntry 11 }
			
	        hwEponOnuActiveStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				active(1),
				deactive(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to activate or deactivate an ONU. "
			DEFVAL { 1 }
			::= { hwEponOnuConfigEntry 12 }	
		
		hwEponOnuRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status, which is used to differentiate the creation, modification and deletion of a row."
			::= { hwEponOnuConfigEntry 51 }

		
		hwEponOnuVersionTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuVersionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the ONU version information table."
			::= { hwEponControlObjects 3 }

		
		hwEponOnuVersionEntry OBJECT-TYPE
			SYNTAX HwEponOnuVersionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU version information entry."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOnuVersionTable 1 }

		
		HwEponOnuVersionEntry ::=
			SEQUENCE { 
				hwEponOnuVendorId
					DisplayString,
				hwEponOnuModel
					Integer32,
				hwEponOnuOnuIdentifier
					DisplayString,
				hwEponOnuHardwareVersion
					DisplayString,
				hwEponOnuSoftwareVersion
					DisplayString,
				hwEponOnuChipVenderId
					DisplayString,
				hwEponOnuChipModel
					Integer32,
				hwEponOnuChipVersion
					Integer32,
				hwEponOnuChipDesignDate
					DisplayString,
				hwEponOnuFirmwareVersion
					Integer32
			 }

		hwEponOnuVendorId OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"This is the ONU vendor ID."
			::= { hwEponOnuVersionEntry 1 }

		
		hwEponOnuModel OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU model."
			::= { hwEponOnuVersionEntry 2 }

		
		hwEponOnuOnuIdentifier OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU identifier."
			::= { hwEponOnuVersionEntry 3 }

		
		hwEponOnuHardwareVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The hardware version of the ONU."
			::= { hwEponOnuVersionEntry 4 }

		
		hwEponOnuSoftwareVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The software version of the ONU."
			::= { hwEponOnuVersionEntry 5 }

		
		hwEponOnuChipVenderId OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The verder ID of the ONU's chip."
			::= { hwEponOnuVersionEntry 6 }

		
		hwEponOnuChipModel OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The model of the ONU's chip."
			::= { hwEponOnuVersionEntry 7 }

		
		hwEponOnuChipVersion OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The verion of the ONU's chip."
			::= { hwEponOnuVersionEntry 8 }

		
		hwEponOnuChipDesignDate OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The production date of the ONU's chip."
			::= { hwEponOnuVersionEntry 9 }

		
		hwEponOnuFirmwareVersion OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The firmware version of the ONU's chip."
			::= { hwEponOnuVersionEntry 10 }

		
		hwEponOnuControlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the control table of the ONU. The indexes are IF index and ONU ID."
			::= { hwEponControlObjects 4 }

		
		hwEponOnuControlEntry OBJECT-TYPE
			SYNTAX HwEponOnuControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU control entry, including a set of operations performed on the ONU."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOnuControlTable 1 }

		
		HwEponOnuControlEntry ::=
			SEQUENCE { 
				hwEponOnuReset
					Integer32,
				hwEponOnuReRegister
					Integer32,
				hwEponOnuReDiscovery
					Integer32,				
				hwEponOnuRunStatus
					INTEGER,
				hwEponOnuDistance
					Integer32,
				hwEponOnuRtt
					Integer32,				
				hwEponOnuLastUpTime
					DisplayString,
				hwEponOnuLastDownTime
					DisplayString,
				hwEponOnuLastDownCause
					Integer32
			 }

		hwEponOnuReset OBJECT-TYPE
			SYNTAX Integer32 (1)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to reset an ONU."
			DEFVAL { 1 }
			::= { hwEponOnuControlEntry 1 }

		
		hwEponOnuReRegister OBJECT-TYPE
			SYNTAX Integer32 (1)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to re-register an ONU."
			DEFVAL { 1 }
			::= { hwEponOnuControlEntry 2 }

		
		hwEponOnuReDiscovery OBJECT-TYPE
			SYNTAX Integer32 (1)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"This object is used to reset the discovery status of an ONU. 
				If the authentication mode is once-on(4), 
				after performing the rediscovery operation, 
				the online duration is re-timed."
			DEFVAL { 1 }
			::= { hwEponOnuControlEntry 3 }

		
		hwEponOnuRunStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The running status of the ONU: up means normal and down means faulty."
			::= { hwEponOnuControlEntry 4 }

		
		hwEponOnuDistance OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The distance of the ONU (km)."
			::= { hwEponOnuControlEntry 5 }

		
		hwEponOnuRtt OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The RTT of the ONU (TQ)."
			::= { hwEponOnuControlEntry 6 }

		
		hwEponOnuLastUpTime OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Last online time of the ONU, including the year (2 bytes), 
				month (1 byte), date (1 byte), hour (1 byte), minute (1 byte), 
				second (1 byte) and week (1 byte)."
			::= { hwEponOnuControlEntry 7 }

		
		hwEponOnuLastDownTime OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Last offline time of the ONU, including the year (2 bytes), 
				month (1 byte), date (1 byte), hour (1 byte), minute (1 byte), 
				second (1 byte), and the week (1 byte)."
			::= { hwEponOnuControlEntry 8 }

		
		hwEponOnuLastDownCause OBJECT-TYPE
			SYNTAX Integer32 (1 | 7..10 | 13)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The last offline reason."
			::= { hwEponOnuControlEntry 9 }

		
		hwEponAutoFindOnuInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponAutoFindOnuInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the auto find table of the ONU."
			::= { hwEponControlObjects 5 }

		
		hwEponAutoFindOnuInfoEntry OBJECT-TYPE
			SYNTAX HwEponAutoFindOnuInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU auto-find entry, including a set of operations of the ONU auto-find."
			INDEX { ifIndex, hwEponAutoFindOnuOrder }
			::= { hwEponAutoFindOnuInfoTable 1 }

		
		HwEponAutoFindOnuInfoEntry ::=
			SEQUENCE { 
				hwEponAutoFindOnuOrder
					Integer32,
				hwEponAutoFindOnuInfoMacAddress
					MacAddress,
				hwEponAutoFindOnuInfoPasswordValue
					DisplayString
			 }

		hwEponAutoFindOnuOrder OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The SN of the auto-found ONU."
			::= { hwEponAutoFindOnuInfoEntry 1 }

		
		hwEponAutoFindOnuInfoMacAddress OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The MAC address of the auto-found ONU."
			::= { hwEponAutoFindOnuInfoEntry 2 }

		
		hwEponAutoFindOnuInfoPasswordValue OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The password of the auto-found ONU."
			::= { hwEponAutoFindOnuInfoEntry 3 }

		
		hwEponOnuCapabilityInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuCapabilityInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The capability table of the ONU."
			::= { hwEponControlObjects 6 }

		
		hwEponOnuCapabilityInfoEntry OBJECT-TYPE
			SYNTAX HwEponOnuCapabilityInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the entry of the ONU capbility."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOnuCapabilityInfoTable 1 }

		
		HwEponOnuCapabilityInfoEntry ::=
			SEQUENCE { 
				hwEponOnuPotsPortNum
					Integer32,
				hwEponOnuFePortsNum
					Integer32,
				hwEponOnuGePortsNum
					Integer32,
				hwEponOnuTdmPortsNum
					Integer32,
				hwEponOnuFecSupport
					Integer32,
				hwEponOnuSupportBackupBattery
					Integer32,
				hwEponOnuUpQueueNum
					Integer32,
				hwEponOnuUpQueueNumPerPort
					Integer32,
				hwEponOnuDownQueueNum
					Integer32,
				hwEponOnuDownQueueNumPerPort
					Integer32,
				hwEponOnuFePortList
					PortList,
				hwEponOnuGePortList
					PortList,
				hwEponOnuSupportMulticastQuickLeave
					Integer32
			 }

		hwEponOnuPotsPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of POTS ports of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 1 }

		
		hwEponOnuFePortsNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of FE ports of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 2 }

		
		hwEponOnuGePortsNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of GE ports of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 3 }

		
		hwEponOnuTdmPortsNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of TDM ports of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 4 }

		
		hwEponOnuFecSupport OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Does the ONU support FEC."
			::= { hwEponOnuCapabilityInfoEntry 5 }

		
		hwEponOnuSupportBackupBattery OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Does the ONU support backup batter."
			::= { hwEponOnuCapabilityInfoEntry 6 }

		
		hwEponOnuUpQueueNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of upstream queues of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 7 }

		
		hwEponOnuUpQueueNumPerPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of queues on the upstream port of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 8 }

		
		hwEponOnuDownQueueNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of downstram queues of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 9 }

		
		hwEponOnuDownQueueNumPerPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The number of queues on the downstream port of the ONU."
			::= { hwEponOnuCapabilityInfoEntry 10 }

		
		hwEponOnuFePortList OBJECT-TYPE
			SYNTAX PortList
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The bit-map of FE port."
			::= { hwEponOnuCapabilityInfoEntry 11 }

		
		hwEponOnuGePortList OBJECT-TYPE
			SYNTAX PortList
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The bit-map of GE port."
			::= { hwEponOnuCapabilityInfoEntry 12 }

		
		hwEponOnuSupportMulticastQuickLeave OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Does the ONU support multicast quick-leave."
			::= { hwEponOnuCapabilityInfoEntry 13 }

		
		hwEponOnuIpConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuIpConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the ONU IP configuration table. 
				This table is used to configure and query 
				the IP configuration mode and IP address of an ONU."
			::= { hwEponControlObjects 7 }

		
		hwEponOnuIpConfigEntry OBJECT-TYPE
			SYNTAX HwEponOnuIpConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU IP config entry."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOnuIpConfigTable 1 }

		
		HwEponOnuIpConfigEntry ::=
			SEQUENCE { 
				hwEponOnuIpAddress
					IpAddress,
				hwEponOnuNetMask
					IpAddress,
				hwEponOnuNetGateway
					IpAddress,
				hwEponOnuIpManageVlan
					Integer32,
				hwEponOnuIpRowStates
					RowStatus
			 }

		hwEponOnuIpAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"IP address."
			::= { hwEponOnuIpConfigEntry 1 }

		
		hwEponOnuNetMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Sub-net mask."
			::= { hwEponOnuIpConfigEntry 2 }

		
		hwEponOnuNetGateway OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"ONU NMS gateway."
			::= { hwEponOnuIpConfigEntry 3 }

		
		hwEponOnuIpManageVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The management VLAN of the ONU."
			::= { hwEponOnuIpConfigEntry 4 }

		
		hwEponOnuIpRowStates OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status, which is used to differentiate the creation, modification and deletion of a row."
			::= { hwEponOnuIpConfigEntry 51 }

		
		hwEponOnuEthObjectCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuEthObjectCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU ETH port config table."
			::= { hwEponControlObjects 8 }

		
		hwEponOnuEthObjectCfgEntry OBJECT-TYPE
			SYNTAX HwEponOnuEthObjectCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU ETH port config entry."
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuEthPortId }
			::= { hwEponOnuEthObjectCfgTable 1 }

		
		HwEponOnuEthObjectCfgEntry ::=
			SEQUENCE { 
				hwEponOnuEthPortId
					Integer32,
				hwEponOnuEthOperateStatus
					INTEGER,
				hwEponOnuEthFlowcontrolSwitch
					INTEGER
			 }

		hwEponOnuEthPortId OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ETH port number of the ONU."
			::= { hwEponOnuEthObjectCfgEntry 1 }

		
		hwEponOnuEthOperateStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				undoshutdown(1),
				shutdown(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The operation status of the port."
			DEFVAL { 1 }
			::= { hwEponOnuEthObjectCfgEntry 2 }

		
		hwEponOnuEthFlowcontrolSwitch OBJECT-TYPE
			SYNTAX INTEGER
				{
				open(1),
				close(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enabling or disabling flow control on the ONU port."
			::= { hwEponOnuEthObjectCfgEntry 3 }

		
		hwEponOnuTdmPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuTdmPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU TDM port config table."
			::= { hwEponControlObjects 9 }

		
		hwEponOnuTdmPortCfgEntry OBJECT-TYPE
			SYNTAX HwEponOnuTdmPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU TDM port config entry."
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuTdmPortId }
			::= { hwEponOnuTdmPortCfgTable 1 }

		
		HwEponOnuTdmPortCfgEntry ::=
			SEQUENCE { 
				hwEponOnuTdmPortId
					Integer32,
				hwEponOnuTdmPortOperateStatus
					INTEGER
			 }

		hwEponOnuTdmPortId OBJECT-TYPE
			SYNTAX Integer32 (1..16)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU TDM port ID."
			::= { hwEponOnuTdmPortCfgEntry 1 }

		
		hwEponOnuTdmPortOperateStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The enable switch of the port."
			::= { hwEponOnuTdmPortCfgEntry 2 }

		
		hwEponOnuPotsPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuPotsPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU POTS port config table."
			::= { hwEponControlObjects 10 }

		
		hwEponOnuPotsPortCfgEntry OBJECT-TYPE
			SYNTAX HwEponOnuPotsPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU POTS port config entry."
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuPotsPortId }
			::= { hwEponOnuPotsPortCfgTable 1 }

		
		HwEponOnuPotsPortCfgEntry ::=
			SEQUENCE { 
				hwEponOnuPotsPortId
					Integer32,
				hwEponOnuPotsPortOperateStatus
					INTEGER
			 }

		hwEponOnuPotsPortId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU POTS port number."
			::= { hwEponOnuPotsPortCfgEntry 1 }

		
		hwEponOnuPotsPortOperateStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Operation status of the ONU POTS port."
			::= { hwEponOnuPotsPortCfgEntry 2 }

		
		hwEponOnuCfgCarTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuCfgCarEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU CAR config table."
			::= { hwEponControlObjects 11 }

		
		hwEponOnuCfgCarEntry OBJECT-TYPE
			SYNTAX HwEponOnuCfgCarEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU CAR config entry."
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuCfgCarDirection }
			::= { hwEponOnuCfgCarTable 1 }

		
		HwEponOnuCfgCarEntry ::=
			SEQUENCE { 
				hwEponOnuCfgCarDirection
					INTEGER,
				hwEponOnuCarProfileNameIndex
					DisplayString,
				hwEponOnuTrafficPolicyNameIndex
					DisplayString,
				hwEponOnuCfgCarRowStatus
					RowStatus
			 }

		hwEponOnuCfgCarDirection OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The direction of packets where CAR is performed."
			::= { hwEponOnuCfgCarEntry 1 }

		
		hwEponOnuCarProfileNameIndex OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"CAR profile name."
			::= { hwEponOnuCfgCarEntry 2 }

		
		hwEponOnuTrafficPolicyNameIndex OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Traffic policy name."
			::= { hwEponOnuCfgCarEntry 3 }

		
		hwEponOnuCfgCarRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status, which is used to differentiate the creation, modification and deletion of a row."
			::= { hwEponOnuCfgCarEntry 51 }

		
		hwEponOltPortDefaultVlanTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOltPortDefaultVlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"OLT default VLAN config table."
			::= { hwEponControlObjects 12 }

		
		hwEponOltPortDefaultVlanEntry OBJECT-TYPE
			SYNTAX HwEponOltPortDefaultVlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"OLT default VLAN config entry."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOltPortDefaultVlanTable 1 }

		
		HwEponOltPortDefaultVlanEntry ::=
			SEQUENCE { 
				hwEponOltPortDefaultVlanId
					Integer32,
				hwEponOltPortDefaultVlanBatch
					INTEGER,
				hwEponOltPortDefaultVlanOnuStartId
					Integer32,
				hwEponOltPortDefaultVlanOnuEndId
					Integer32,
				hwEponOltPortDefaultVlanRowStatus
					RowStatus
			 }

		hwEponOltPortDefaultVlanId OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Default VLAN ID on the port."
			::= { hwEponOltPortDefaultVlanEntry 1 }

		
		hwEponOltPortDefaultVlanBatch OBJECT-TYPE
			SYNTAX INTEGER
				{
				batch(1),
				notBatch(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Batch config."
			::= { hwEponOltPortDefaultVlanEntry 2 }

		
		hwEponOltPortDefaultVlanOnuStartId OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Start ONU ID in batch configuration."
			::= { hwEponOltPortDefaultVlanEntry 3 }

		
		hwEponOltPortDefaultVlanOnuEndId OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"End ONU ID in batch configuration."
			::= { hwEponOltPortDefaultVlanEntry 4 }

		
		hwEponOltPortDefaultVlanRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The row status, which is used to differentiate the creation, modification and deletion of a row."
			::= { hwEponOltPortDefaultVlanEntry 51 }

		
		hwEponVlanStackingAndMappingTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponVlanStackingAndMappingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU stacking and mapping config table."
			::= { hwEponControlObjects 13 }

		
		hwEponVlanStackingAndMappingEntry OBJECT-TYPE
			SYNTAX HwEponVlanStackingAndMappingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU stacking and mapping config entry."
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuExtSVlanId, hwEponOnuIntSStartVlanId, hwEponOnuIntSEndVlanId}
			::= { hwEponVlanStackingAndMappingTable 1 }

		
		HwEponVlanStackingAndMappingEntry ::=
			SEQUENCE { 
				hwEponOnuExtSVlanId
					Integer32,
				hwEponOnuIntSStartVlanId
					Integer32,
				hwEponOnuIntSEndVlanId
					Integer32,
				hwEponVlanStackingOrMapping
					INTEGER,
				hwEponOnuDextVlanId
					Integer32,
				hwEponOnuDintVlanId
					Integer32,
				hwEponOnuPopExtVlanId
					INTEGER,
				hwEponOnuVlanCopyPri
					INTEGER,
				hwEponOnuIntVlanRemarkPri
					INTEGER,
				hwEponOnuExtVlanRemarkPri
					INTEGER,
				hwEponOnuIntVlanPri
					Integer32,
				hwEponOnuExtVlanPri
					Integer32,
				hwEponVlanMappingBatch
					INTEGER,
				hwEponVlanMappingOnuStartId
					Integer32,
				hwEponVlanMappingOnuEndId
					Integer32,
				hwEponVlanMappingRowStatus
					RowStatus
			 }

		
		hwEponOnuExtSVlanId OBJECT-TYPE
			SYNTAX Integer32 (0..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Source VLAN ID."
			::= { hwEponVlanStackingAndMappingEntry 1 }

		
		hwEponOnuIntSStartVlanId OBJECT-TYPE
			SYNTAX Integer32 (0..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Source inner start VLAN ID."
			::= { hwEponVlanStackingAndMappingEntry 2 }

		
		hwEponOnuIntSEndVlanId OBJECT-TYPE
			SYNTAX Integer32 (0..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Source inner end VLAN ID."
			::= { hwEponVlanStackingAndMappingEntry 3 }

		hwEponVlanStackingOrMapping OBJECT-TYPE
			SYNTAX INTEGER
				{
				singleMapping(1),
				doubleMapping(2),
				stacking(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Stacking or mapping."
			::= { hwEponVlanStackingAndMappingEntry 4 }
		
		hwEponOnuDextVlanId OBJECT-TYPE
			SYNTAX Integer32 (0..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Mapping the outer VLAN ID of destination."
			::= { hwEponVlanStackingAndMappingEntry 5 }

		
		hwEponOnuDintVlanId OBJECT-TYPE
			SYNTAX Integer32 (0..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Mapping the inner VLAN ID of destination."
			::= { hwEponVlanStackingAndMappingEntry 6 }

		
		hwEponOnuPopExtVlanId OBJECT-TYPE
			SYNTAX INTEGER
				{
				pop(1),
				notPop(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Whether to remove the outer VLAN ID."
			::= { hwEponVlanStackingAndMappingEntry 7 }

		
		hwEponOnuVlanCopyPri OBJECT-TYPE
			SYNTAX INTEGER
				{
				copy(1),
				notCopy(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Copy VLAN COS value."
			::= { hwEponVlanStackingAndMappingEntry 8 }

		
		hwEponOnuIntVlanRemarkPri OBJECT-TYPE
			SYNTAX INTEGER
				{
				remark(1),
				notRemark(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Remark inner VLAN COS value."
			::= { hwEponVlanStackingAndMappingEntry 9 }

		
		hwEponOnuExtVlanRemarkPri OBJECT-TYPE
			SYNTAX INTEGER
				{
				remark(1),
				notRemark(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Remark outer VLAN COS value."
			::= { hwEponVlanStackingAndMappingEntry 10 }

		
		hwEponOnuIntVlanPri OBJECT-TYPE
			SYNTAX Integer32 (0..8)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The inner VLAN COS vlaue."
			::= { hwEponVlanStackingAndMappingEntry 11 }

		
		hwEponOnuExtVlanPri OBJECT-TYPE
			SYNTAX Integer32 (0..8)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The outer VLAN COS vlaue."
			::= { hwEponVlanStackingAndMappingEntry 12 }

		
		hwEponVlanMappingBatch OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Batch config."
			::= { hwEponVlanStackingAndMappingEntry 13 }

		
		hwEponVlanMappingOnuStartId OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Start ONU ID."
			::= { hwEponVlanStackingAndMappingEntry 14 }

		
		hwEponVlanMappingOnuEndId OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"End ONU ID."
			::= { hwEponVlanStackingAndMappingEntry 15 }

		
		hwEponVlanMappingRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
			"The row status, which is used to differentiate the creation, modification and deletion of a row. "
			::= { hwEponVlanStackingAndMappingEntry 51 }

		
		hwEponOltPortStaticMacTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOltPortStaticMacEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"OLT static MAC address table."
			::= { hwEponControlObjects 14 }

		
		hwEponOltPortStaticMacEntry OBJECT-TYPE
			SYNTAX HwEponOltPortStaticMacEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"OLT static MAC address entry"
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuUserMacAddressOrder }
			::= { hwEponOltPortStaticMacTable 1 }

		
		HwEponOltPortStaticMacEntry ::=
			SEQUENCE { 
				hwEponOnuUserMacAddressOrder
					Integer32,
				hwEponOnuUserMacAddress
					MacAddress,
				hwEponOltPortStaticMacRowStatus
					RowStatus
			 }

		hwEponOnuUserMacAddressOrder OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The index of the static MAC address."
			::= { hwEponOltPortStaticMacEntry 1 }

		
		hwEponOnuUserMacAddress OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"MAC address."
			::= { hwEponOltPortStaticMacEntry 2 }

		
		hwEponOltPortStaticMacRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
		        "The row status, which is used to differentiate the creation, modification and deletion of a row. "
			::= { hwEponOltPortStaticMacEntry 51 }

		
		hwEponOltPortMacLimitTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOltPortMacLimitEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"MAC address learning limit table."
			::= { hwEponControlObjects 15 }

		
		hwEponOltPortMacLimitEntry OBJECT-TYPE
			SYNTAX HwEponOltPortMacLimitEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"MAC address learning limit entry."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOltPortMacLimitTable 1 }

		
		HwEponOltPortMacLimitEntry ::=
			SEQUENCE { 
				hwEponOnuUserMacAddressNumber
					Integer32,
				hwEponOnuForwardAction
					INTEGER,
				hwEponOnuAlarmAction
					INTEGER,
				hwEponOnuMacLimitRowStatus
					RowStatus
			 }

		hwEponOnuUserMacAddressNumber OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Maximum number of MAC addresses that can be learned."
			DEFVAL { 4094 }
			::= { hwEponOltPortMacLimitEntry 1 }

		
		hwEponOnuForwardAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				forward(1),
				discard(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Forwarding action."
			DEFVAL { 2 }
			::= { hwEponOltPortMacLimitEntry 2 }

		
		hwEponOnuAlarmAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				warning(1),
				notWarning(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Alarm action."
			DEFVAL { 1 }
			::= { hwEponOltPortMacLimitEntry 3 }

		
		hwEponOnuMacLimitRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
			"The row status, which is used to differentiate the creation, modification and deletion of a row. "
			::= { hwEponOltPortMacLimitEntry 51 }

		
		hwEponProfileObjects OBJECT IDENTIFIER ::= { hwEponObjects 3 }

		
		hwEponLineProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponLineProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the EPON line profile table. This table is 
				used to create a EPON line profile."
			::= { hwEponProfileObjects 1 }

		
		hwEponLineProfileInfoEntry OBJECT-TYPE
			SYNTAX HwEponLineProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The EPON line profile entry."
			INDEX { hwEponLineProfileNameIndex }
			::= { hwEponLineProfileInfoTable 1 }

		
		HwEponLineProfileInfoEntry ::=
			SEQUENCE { 
				hwEponLineProfileNameIndex
					DisplayString,
				hwEponLineProfileBindNum
					Integer32,
				hwEponLineProfileDbaProfileName								                 
					DisplayString,
				hwEponLineProfileEncryptMode
					INTEGER,
				hwEponLineProfileQueueSetIndex1Threshold
					DisplayString,
				hwEponLineProfileQueueSetIndex2Threshold
					DisplayString,
				hwEponLineProfileQueueSetIndex3Threshold
					DisplayString,
				hwEponLineProfileRowStatus
					RowStatus
			 }

		hwEponLineProfileNameIndex OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Index of the EPON line profile."
			::= { hwEponLineProfileInfoEntry 1 }

		
		hwEponLineProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of EPON line profiles bound to the ONU."
			::= { hwEponLineProfileInfoEntry 2 }

		
		hwEponLineProfileDbaProfileName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Name of the EPON line DBA profile."
			::= { hwEponLineProfileInfoEntry 3 }

		
		hwEponLineProfileEncryptMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				aes(1),
				tripleChurining(2),
				off(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Encryption mode of the EPON line profile."
			::= { hwEponLineProfileInfoEntry 4 }

		
		hwEponLineProfileQueueSetIndex1Threshold OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..500))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Threshold of queue set 1 in the EPON line profile."
			::= { hwEponLineProfileInfoEntry 5 }

		
		hwEponLineProfileQueueSetIndex2Threshold OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..500))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Threshold of queue set 2 in the EPON line profile."
			::= { hwEponLineProfileInfoEntry 6 }

		
		hwEponLineProfileQueueSetIndex3Threshold OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..500))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Threshold of queue set 3 in the EPON line profile."
			::= { hwEponLineProfileInfoEntry 7 }

		
		hwEponLineProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponLineProfileInfoEntry 51 }

		
		hwEponOnuSrvProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuSrvProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the EPON service profile table."
			::= { hwEponProfileObjects 2 }

		
		hwEponOnuSrvProfileInfoEntry OBJECT-TYPE
			SYNTAX HwEponOnuSrvProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The EPON service profile entry."
			INDEX { hwEponOnuSrvProfNameIndex }
			::= { hwEponOnuSrvProfileInfoTable 1 }

		
		HwEponOnuSrvProfileInfoEntry ::=
			SEQUENCE { 
				hwEponOnuSrvProfNameIndex
					DisplayString,
				hwEponOnuSrvProfileBindNum
					Integer32,
				hwEponOnuSrvProfileRowStatus
					RowStatus
			 }

		hwEponOnuSrvProfNameIndex OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Index of the EPON service profile."
			::= { hwEponOnuSrvProfileInfoEntry 1 }

		
		hwEponOnuSrvProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Number of EPON service profiles bound to the ONU."
			::= { hwEponOnuSrvProfileInfoEntry 2 }

		
		hwEponOnuSrvProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponOnuSrvProfileInfoEntry 51 }

		
		hwEponSrvProfileOnuCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponSrvProfileOnuCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the EPON service profile ONU config table."
			::= { hwEponProfileObjects 3 }

		
		hwEponSrvProfileOnuCfgEntry OBJECT-TYPE
			SYNTAX HwEponSrvProfileOnuCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The EPON service profile ONU config entry."
			INDEX { hwEponOnuSrvProfNameIndex }
			::= { hwEponSrvProfileOnuCfgTable 1 }

		
		HwEponSrvProfileOnuCfgEntry ::=
			SEQUENCE { 
				hwEponSrvProfileFecMode
					EnabledStatus,
				hwEponSrvProfileMulticastMode
					INTEGER,
				hwEponSrvProfileMulticastQuickLeaveSwitch
					EnabledStatus
			 }

		hwEponSrvProfileFecMode OBJECT-TYPE
			SYNTAX EnabledStatus				
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"FEC mode of the EPON service profile."
			::= { hwEponSrvProfileOnuCfgEntry 4 }

		
		hwEponSrvProfileMulticastMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				ctc(1),
				igmpsnooping(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Multicast mode of the EPON service profile."
			::= { hwEponSrvProfileOnuCfgEntry 5 }

		
		hwEponSrvProfileMulticastQuickLeaveSwitch OBJECT-TYPE
			SYNTAX EnabledStatus				
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Enabling or disabling multicast quickleave of the EPON service profile."
			::= { hwEponSrvProfileOnuCfgEntry 6 }

		
		hwEponSrvProfOnuPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponSrvProfOnuPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the EPON service profile ONU port config table."
			::= { hwEponProfileObjects 4 }

		
		hwEponSrvProfOnuPortCfgEntry OBJECT-TYPE
			SYNTAX HwEponSrvProfOnuPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The EPON service profile ONU port config entry."
			INDEX { hwEponOnuSrvProfNameIndex, hwEponOnuPortTypeIndex, hwEponOnuPortIdIndex }
			::= { hwEponSrvProfOnuPortCfgTable 1 }

		
		HwEponSrvProfOnuPortCfgEntry ::=
			SEQUENCE { 
				hwEponOnuPortTypeIndex
					INTEGER,        
				hwEponOnuPortIdIndex
					Integer32,
				hwEponSrvProfOnuPortCfgMaxMacAddressNum
					Integer32,
				hwEponSrvProfOnuPortCfgMulticastStripSwitch
					INTEGER
			 }

		hwEponOnuPortTypeIndex OBJECT-TYPE
			SYNTAX INTEGER 
			{
				eth1(1), 
				eth2(2),
				eth3(3),
				eth4(48)
				
			}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Port type of the ONU."
			::= { hwEponSrvProfOnuPortCfgEntry 1 }

		
		hwEponOnuPortIdIndex OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Port index of the ONU."
			::= { hwEponSrvProfOnuPortCfgEntry 2 }

		
		hwEponSrvProfOnuPortCfgMaxMacAddressNum OBJECT-TYPE
			SYNTAX Integer32 (0..1023)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Max number of MAC addresses that can be learned on an ONU port."
			::= { hwEponSrvProfOnuPortCfgEntry 3 }

		
		hwEponSrvProfOnuPortCfgMulticastStripSwitch OBJECT-TYPE
			SYNTAX INTEGER
				{
				strip(1),
				notStrip(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Whether to remove VLAN tags from multicast packets on an ONU port."
			::= { hwEponSrvProfOnuPortCfgEntry 4 }

		
		hwEponSrvProfMulticastVlanCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponSrvProfMulticastVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the EPON service profile multicast VLAN config table."
			::= { hwEponProfileObjects 5 }

		
		hwEponSrvProfMulticastVlanCfgEntry OBJECT-TYPE
			SYNTAX HwEponSrvProfMulticastVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The EPON service profile multi-cast VLAN config entry."
			INDEX { hwEponOnuSrvProfNameIndex, hwEponOnuPortTypeIndex, hwEponOnuPortIdIndex, hwEponSrvProfMulticastVlanCfgMulticastVlan }
			::= { hwEponSrvProfMulticastVlanCfgTable 1 }

		
		HwEponSrvProfMulticastVlanCfgEntry ::=
			SEQUENCE { 
				hwEponSrvProfMulticastVlanCfgMulticastVlan
					Integer32,
				hwEponSrvProfMulticastVlanCfgRowStatus
					RowStatus
			 }

		hwEponSrvProfMulticastVlanCfgMulticastVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Multicast VLAN ID in the EPON service profile."
			::= { hwEponSrvProfMulticastVlanCfgEntry 1 }

		
		hwEponSrvProfMulticastVlanCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponSrvProfMulticastVlanCfgEntry 51 }

		
		hwEponSrvProfOnuPortVlanCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponSrvProfOnuPortVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU port's remote native VLAN info table."
			::= { hwEponProfileObjects 6 }

		
		hwEponSrvProfOnuPortVlanCfgEntry OBJECT-TYPE
			SYNTAX HwEponSrvProfOnuPortVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU port's remote native VLAN info entry."
			INDEX { hwEponOnuSrvProfNameIndex, hwEponOnuPortType, hwEponOnuPortId }
			::= { hwEponSrvProfOnuPortVlanCfgTable 1 }

		
		HwEponSrvProfOnuPortVlanCfgEntry ::=
			SEQUENCE { 
				hwEponOnuPortType
					INTEGER,
				hwEponOnuPortId
					Integer32,
				hwEponSrvProfOnuPortVlanMode
					INTEGER,
				hwEponSrvProfOnuPortAddToVlanId
					Integer32,
				hwEponSrvProfOnuPortDefaultVlanId
					Integer32,
				hwEponSrvProfOnuPortVlanRowStatus
					RowStatus
			 }

		hwEponOnuPortType OBJECT-TYPE
			SYNTAX INTEGER { eth(1) }
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU port type."
			::= { hwEponSrvProfOnuPortVlanCfgEntry 1 }

		
		hwEponOnuPortId OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU port ID."
			::= { hwEponSrvProfOnuPortVlanCfgEntry 2 }

		
		hwEponSrvProfOnuPortVlanMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				transparent(1),
				translation(2),
				transmit(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The VLAN mode of an ONU port, 0, 2, or 3."
			::= { hwEponSrvProfOnuPortVlanCfgEntry 3 }

		
		hwEponSrvProfOnuPortAddToVlanId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"VLAN IDs allowed by the ONU port."
			::= { hwEponSrvProfOnuPortVlanCfgEntry 4 }

		
		hwEponSrvProfOnuPortDefaultVlanId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Default VLAN ID of the ONU port."
			::= { hwEponSrvProfOnuPortVlanCfgEntry 5 }

		
		hwEponSrvProfOnuPortVlanRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponSrvProfOnuPortVlanCfgEntry 51 }

		
		hwEponSrvProfOnuPortVlanTranslationTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponSrvProfOnuPortVlanTranslationEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU port's remote VLAN translation information table."
			::= { hwEponProfileObjects 7 }

		
		hwEponSrvProfOnuPortVlanTranslationEntry OBJECT-TYPE
			SYNTAX HwEponSrvProfOnuPortVlanTranslationEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The ONU port's remote VLAN translation information entry."
			INDEX { hwEponOnuSrvProfNameIndex, hwEponOnuPortType, hwEponOnuPortId, hwEponSrvProfOnuPortVlanTranslationCvlanId }
			::= { hwEponSrvProfOnuPortVlanTranslationTable 1 }

		
		HwEponSrvProfOnuPortVlanTranslationEntry ::=
			SEQUENCE { 
				hwEponSrvProfOnuPortVlanTranslationCvlanId
					Integer32,
				hwEponSrvProfOnuPortVlanTranslationSVlanId
					Integer32,
				hwEponSrvProfOnuPortVlanTranslationRowStatus
					RowStatus
			 }

		hwEponSrvProfOnuPortVlanTranslationCvlanId OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"C-VLAN ID on the ONU port."
			::= { hwEponSrvProfOnuPortVlanTranslationEntry 1 }

		
		hwEponSrvProfOnuPortVlanTranslationSVlanId OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"S-VLAN ID on the ONU port."
			::= { hwEponSrvProfOnuPortVlanTranslationEntry 2 }

		
		hwEponSrvProfOnuPortVlanTranslationRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponSrvProfOnuPortVlanTranslationEntry 51 }

		
		hwEponDbaProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponDbaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the DBA profile table. This table is 
				used to create a DBA profile,
				and is used to modify or delete a DBA profile
				which is not bound."
			::= { hwEponProfileObjects 8 }

		
		hwEponDbaProfileInfoEntry OBJECT-TYPE
			SYNTAX HwEponDbaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The DBA profile entry."
			INDEX { hwEponDbaProfileInfoNameIndex }
			::= { hwEponDbaProfileInfoTable 1 }

		
		HwEponDbaProfileInfoEntry ::=
			SEQUENCE { 
				hwEponDbaProfileInfoNameIndex
					DisplayString,
				hwEponDbaTypeIndex
					INTEGER,
				hwEponDbaProfileFixedRate
					Integer32,
				hwEponDbaProfileAssuredRate
					Integer32,
				hwEponDbaProfileMaxRate
					Integer32,
				hwEponDbaProfileReferenceNum
					Integer32,
				hwEponDbaProfileEntryStatus
					RowStatus
			 }

		hwEponDbaProfileInfoNameIndex OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"DBA profile name."
			::= { hwEponDbaProfileInfoEntry 1 }

		
		hwEponDbaTypeIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				fix(1),
				assure(2),
				assureAndmax(3),				
				max(4),
				fixAndassureAndMax(5)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The DBA type index."
			::= { hwEponDbaProfileInfoEntry 2 }

		
		hwEponDbaProfileFixedRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Fixed bandwidth of the profile."
			::= { hwEponDbaProfileInfoEntry 3 }

		
		hwEponDbaProfileAssuredRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Assured bandwidth of the profile."
			::= { hwEponDbaProfileInfoEntry 4 }

		
		hwEponDbaProfileMaxRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Max bandwidth of the profile."
			::= { hwEponDbaProfileInfoEntry 5 }

		
		hwEponDbaProfileReferenceNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of bound DBA profiles."
			::= { hwEponDbaProfileInfoEntry 6 }

		
		hwEponDbaProfileEntryStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponDbaProfileInfoEntry 51 }

		
		hwEponOnuSnmpProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuSnmpProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"EPON SNMP profile config table."
			::= { hwEponProfileObjects 9 }

		
		hwEponOnuSnmpProfileInfoEntry OBJECT-TYPE
			SYNTAX HwEponOnuSnmpProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"EPON SNMP profile config table entry."
			INDEX { hwEponOnuSnmpProfileInfoNameIndex }
			::= { hwEponOnuSnmpProfileInfoTable 1 }

		
		HwEponOnuSnmpProfileInfoEntry ::=
			SEQUENCE { 
				hwEponOnuSnmpProfileInfoNameIndex
					DisplayString,
				hwEponOnuSnmpProfileVersion
					INTEGER,
				hwEponOnuSnmpProfileReadCommunityName
					DisplayString,
				hwEponOnuSnmpProfileWriteCommunityName
					DisplayString,
				hwEponOnuSnmpProfileTrapHostIp
					IpAddress,
				hwEponOnuSnmpProfileTrapHostSrcUdpPort
					Integer32,
				hwEponOnuSnmpProfileSecurityName
					DisplayString,
				hwEponOnuSnmpProfileRowStatus
					RowStatus
			 }

		hwEponOnuSnmpProfileInfoNameIndex OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"SNMP profile name."
			::= { hwEponOnuSnmpProfileInfoEntry 1 }

		
		hwEponOnuSnmpProfileVersion OBJECT-TYPE
			SYNTAX INTEGER
				{
				v1(1),
				v2(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"SNMP version of the profile."
			DEFVAL { 2 }
			::= { hwEponOnuSnmpProfileInfoEntry 2 }

		
		hwEponOnuSnmpProfileReadCommunityName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..32))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Name of the read community."
			::= { hwEponOnuSnmpProfileInfoEntry 3 }

		
		hwEponOnuSnmpProfileWriteCommunityName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..32))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Name of the write community."
			::= { hwEponOnuSnmpProfileInfoEntry 4 }

		
		hwEponOnuSnmpProfileTrapHostIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"IP address of the host sending the trap."
			::= { hwEponOnuSnmpProfileInfoEntry 5 }

		
		hwEponOnuSnmpProfileTrapHostSrcUdpPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"UDP port number, ranging from 1 to 65535."
			::= { hwEponOnuSnmpProfileInfoEntry 6 }

		
		hwEponOnuSnmpProfileSecurityName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..32))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The securityname of SNMP message."
			::= { hwEponOnuSnmpProfileInfoEntry 7 }

		
		hwEponOnuSnmpProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponOnuSnmpProfileInfoEntry 51 }

		
		hwEponSrvProfOnuPortClassTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponSrvProfOnuPortClassEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The configuration table of the EPON classification."
			::= { hwEponProfileObjects 10 }

		
		hwEponSrvProfOnuPortClassEntry OBJECT-TYPE
			SYNTAX HwEponSrvProfOnuPortClassEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The entry of the configuration table of the Epon classification."
			INDEX { hwEponOnuSrvProfNameIndex, hwEponOnuClassifEthPortIndex, hwEponOnuPortClassRuleIndexId }
			::= { hwEponSrvProfOnuPortClassTable 1 }

		
		HwEponSrvProfOnuPortClassEntry ::=
			SEQUENCE { 
				hwEponOnuClassifEthPortIndex
					Integer32,
				hwEponOnuPortClassRuleIndexId
					Integer32,
				hwEponOnuPortClassConditionNum
					INTEGER,
				hwEponOnuPortClassQueueIndexId
					Integer32,
				hwEponOnuPortClassPriMark
					Integer32,
				hwEponOnuPortClassFieldSelect1
					INTEGER,
				hwEponOnuPortClassOperator1
					INTEGER,
				hwEponOnuPortClassMatchValue1
					DisplayString,
				hwEponOnuPortClassFieldSelect2
					INTEGER,
				hwEponOnuPortClassOperator2
					INTEGER,
				hwEponOnuPortClassMatchValue2
					DisplayString,
				hwEponOnuPortClassFieldSelect3
					INTEGER,
				hwEponOnuPortClassOperator3
					INTEGER,
				hwEponOnuPortClassMatchValue3
					DisplayString,
				hwEponOnuPortClassFieldSelect4
					INTEGER,
				hwEponOnuPortClassOperator4
					INTEGER,
				hwEponOnuPortClassMatchValue4
					DisplayString,
				hwEponOnuPortClassProfileRowStatus
					RowStatus
			 }

		hwEponOnuClassifEthPortIndex OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ETH port ID of the ONU ."
			::= { hwEponSrvProfOnuPortClassEntry 1 }

		
		hwEponOnuPortClassRuleIndexId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Index of the traffic classification rule."
			::= { hwEponSrvProfOnuPortClassEntry 2 }

		
		hwEponOnuPortClassConditionNum OBJECT-TYPE
			SYNTAX INTEGER
				{
				one(1),
				two(2),
				three(3),
				four(4)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Number of conditions in a rule"
			::= { hwEponSrvProfOnuPortClassEntry 3 }

		
		hwEponOnuPortClassQueueIndexId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The ID of the queue."
			::= { hwEponSrvProfOnuPortClassEntry 4 }

		
		hwEponOnuPortClassPriMark OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"This is the remark priority of the rule."
			::= { hwEponSrvProfOnuPortClassEntry 5 }

		
		hwEponOnuPortClassFieldSelect1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				dstMac(1),
				srcMac(2),
				ethPri(3),
				vlanId(4),
				ethType(5),
				dstIp(6),
				srcIp(7),
				ipType(8),
				ipTosDscp(9),
				ipPrecedence(10),
				srcPort(11),
				dstPort(12)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The field selector of the first condition."
			::= { hwEponSrvProfOnuPortClassEntry 6 }

		
		hwEponOnuPortClassOperator1 OBJECT-TYPE
			SYNTAX INTEGER
				{
				neverMatch(1),
				equal(2),
				notEqual(3),
				lessOrEqual(4),
				greaterOrEqual(5),
				exists(6),
				notExists(7),
				alwaysMatch(8)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The operator of the first condition."
			::= { hwEponSrvProfOnuPortClassEntry 7 }

		
		hwEponOnuPortClassMatchValue1 OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The match value of the first condition."
			::= { hwEponSrvProfOnuPortClassEntry 8 }

		
		hwEponOnuPortClassFieldSelect2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				dstMac(1),
				srcMac(2),
				ethPri(3),
				vlanId(4),
				ethType(5),
				dstIp(6),
				srcIp(7),
				ipType(8),
				ipTosDscp(9),
				ipPrecedence(10),
				srcPort(11),
				dstPort(12)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The field selector of the second condition."
			::= { hwEponSrvProfOnuPortClassEntry 9 }

		
		hwEponOnuPortClassOperator2 OBJECT-TYPE
			SYNTAX INTEGER
				{
				neverMatch(1),
				equal(2),
				notEqual(3),
				lessOrEqual(4),
				greaterOrEqual(5),
				exists(6),
				notExists(7),
				alwaysMatch(8)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The operator of the second condition."
			::= { hwEponSrvProfOnuPortClassEntry 10 }

		
		hwEponOnuPortClassMatchValue2 OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The match value of the second condition."
			::= { hwEponSrvProfOnuPortClassEntry 11 }

		
		hwEponOnuPortClassFieldSelect3 OBJECT-TYPE
			SYNTAX INTEGER
				{
				dstMac(1),
				srcMac(2),
				ethPri(3),
				vlanId(4),
				ethType(5),
				dstIp(6),
				srcIp(7),
				ipType(8),
				ipTosDscp(9),
				ipPrecedence(10),
				srcPort(11),
				dstPort(12)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The field selector of the third condtion."
			::= { hwEponSrvProfOnuPortClassEntry 12 }

		
		hwEponOnuPortClassOperator3 OBJECT-TYPE
			SYNTAX INTEGER
				{
				neverMatch(1),
				equal(2),
				notEqual(3),
				lessOrEqual(4),
				greaterOrEqual(5),
				exists(6),
				notExists(7),
				alwaysMatch(8)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The operator of the third conditon."
			::= { hwEponSrvProfOnuPortClassEntry 13 }

		
		hwEponOnuPortClassMatchValue3 OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The match value of the third conditon."
			::= { hwEponSrvProfOnuPortClassEntry 14 }

		
		hwEponOnuPortClassFieldSelect4 OBJECT-TYPE
			SYNTAX INTEGER
				{
				dstMac(1),
				srcMac(2),
				ethPri(3),
				vlanId(4),
				ethType(5),
				dstIp(6),
				srcIp(7),
				ipType(8),
				ipTosDscp(9),
				ipPrecedence(10),
				srcPort(11),
				dstPort(12)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The field select of the fourth conditon."
			::= { hwEponSrvProfOnuPortClassEntry 15 }

		
		hwEponOnuPortClassOperator4 OBJECT-TYPE
			SYNTAX INTEGER
				{
				neverMatch(1),
				equal(2),
				notEqual(3),
				lessOrEqual(4),
				greaterOrEqual(5),
				exists(6),
				notExists(7),
				alwaysMatch(8)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The operator of the fourth conditon."
			::= { hwEponSrvProfOnuPortClassEntry 16 }

		
		hwEponOnuPortClassMatchValue4 OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The match value the fourth conditon."
			::= { hwEponSrvProfOnuPortClassEntry 17 }

		
		hwEponOnuPortClassProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponSrvProfOnuPortClassEntry 51 }

		
		hwEponSrvProfOnuPortCfgCarTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponSrvProfOnuPortCfgCarEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The service profile CAR config table."
			::= { hwEponProfileObjects 11 }

		
		hwEponSrvProfOnuPortCfgCarEntry OBJECT-TYPE
			SYNTAX HwEponSrvProfOnuPortCfgCarEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Service profile CAR config entry."
			INDEX { hwEponOnuSrvProfNameIndex, hwEponOnuPortTypeIndex, hwEponOnuPortIdIndex, hwEponSrvProfOnuPortCarCfgDirection }
			::= { hwEponSrvProfOnuPortCfgCarTable 1 }

		
		HwEponSrvProfOnuPortCfgCarEntry ::=
			SEQUENCE { 
				hwEponSrvProfOnuPortCarCfgDirection
					INTEGER,
				hwEponSrvProfOnuPortCarCfgCir
					Integer32,
				hwEponSrvProfOnuPortCarCfgPir
					Integer32,
				hwEponSrvProfOnuPortCarCfgCbs
					Integer32,
				hwEponSrvProfOnuPortCarCfgEbs
					Integer32,
				hwEponSrvProfOnuPortCarCfgRowStatus
					RowStatus
			 }

		hwEponSrvProfOnuPortCarCfgDirection OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The direction of the CAR."
			::= { hwEponSrvProfOnuPortCfgCarEntry 1 }

		
		hwEponSrvProfOnuPortCarCfgCir OBJECT-TYPE
			SYNTAX Integer32 (640..1000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The CIR on the ONU port."
			::= { hwEponSrvProfOnuPortCfgCarEntry 2 }

		
		hwEponSrvProfOnuPortCarCfgPir OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The PIR on the ONU port."
			::= { hwEponSrvProfOnuPortCfgCarEntry 3 }

		
		hwEponSrvProfOnuPortCarCfgCbs OBJECT-TYPE
			SYNTAX Integer32 (1568..1000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The CBS on the ONU port."
			::= { hwEponSrvProfOnuPortCfgCarEntry 4 }

		
		hwEponSrvProfOnuPortCarCfgEbs OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"The EBS on the ONU port."
			::= { hwEponSrvProfOnuPortCfgCarEntry 5 }

		
		hwEponSrvProfOnuPortCarCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { hwEponSrvProfOnuPortCfgCarEntry 51 }

		
		hwEponStatisticObjects OBJECT IDENTIFIER ::= { hwEponObjects 4 }

		
		hwEponOltStatisticTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOltStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The OLT statisitcs table."
			::= { hwEponStatisticObjects 1 }

		
		hwEponOltStatisticEntry OBJECT-TYPE
			SYNTAX HwEponOltStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"The OLT statistics entry."
			INDEX { ifIndex , hwEponOnuIndex}
			::= { hwEponOltStatisticTable 1 }

		HwEponOltStatisticEntry ::=
			SEQUENCE { 
			        hwEponOltStatisticRecvDataFrames
					Counter64,		            
				hwEponOltStatisticRecvDataBytes
					Counter64,				
				hwEponOltStatisticRecvMulticastFrames
					Counter64,
				hwEponOltStatisticRecvBoardcastFrames
					Counter64,
				hwEponOltStatisticRecvErrorFrames
					Counter64,
				hwEponOltStatisticRecvErrorBytes
				        Counter64,
				hwEponOltStatisticRecv64ByteFrames
					Counter64,
				hwEponOltStatisticRecv65To127ByteFrames
					Counter64,
				hwEponOltStatisticRecv128To255ByteFrames
					Counter64,
				hwEponOltStatisticRecv256To511ByteFrames
					Counter64,
				hwEponOltStatisticRecv512To1023ByteFrames
					Counter64,
				hwEponOltStatisticRecv1024To1518ByteFrames
					Counter64,
				hwEponOltStatisticRecvOver1518ByteFrames
					Counter64,
				hwEponOltStatisticRecvUndersizeFrames
					Counter64,
				hwEponOltStatisticRecvOversizeFrames
					Counter64,
				hwEponOltStatisticRecvFcsErrorFrames
					Counter64,
			        hwEponOltStatisticUniCastFrames
					Counter64,
			        hwEponOltStatisticRecvOkFrameCnt
			                Counter64,
			        hwEponOltStatisticRecvOkByteCnt
			                Counter64,       
				hwEponOltStatisticTransDataFrames
					Counter64,
				hwEponOltStatisticTransDataBytes
					Counter64,
				hwEponOltStatisticTransUnicastFrames
					Counter64,
				hwEponOltStatisticTransMulticastFrames
					Counter64,					
				hwEponOltStatisticTransBoardcastFrames
					Counter64,
				hwEponOltStatisticTrans64ByteFrames
					Counter64,
				hwEponOltStatisticTrans65To127ByteFrames
					Counter64,
				hwEponOltStatisticTrans128To255ByteFrames
					Counter64,
				hwEponOltStatisticTrans256To511ByteFrames
					Counter64,
				hwEponOltStatisticTrans512To1023ByteFrames
					Counter64,
				hwEponOltStatisticTrans1024To1518ByteFrames
					Counter64,
				hwEponOltStatisticTransOver1518ByteFrames
					Counter64,
			        hwEponOltStatisticTransFcsErrorFrames
					Counter64,
			        hwEponOltStatisticClear
			        	INTEGER
			               				
			 }
               hwEponOltStatisticRecvDataFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received data frames."
			::= { hwEponOltStatisticEntry 1 }

		
		hwEponOltStatisticRecvDataBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received data bytes."
			::= { hwEponOltStatisticEntry 2 }

		
		hwEponOltStatisticRecvMulticastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received multi-cast frames."
			::= { hwEponOltStatisticEntry 3 }

		
		hwEponOltStatisticRecvBoardcastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received board cast frames."
			::= { hwEponOltStatisticEntry 4 }

		
		hwEponOltStatisticRecvErrorFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received errored frames."
			::= { hwEponOltStatisticEntry 5 }

		
		hwEponOltStatisticRecvErrorBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received errored bytes."
			::= { hwEponOltStatisticEntry 6 }

		
		hwEponOltStatisticRecv64ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 64 bytes frames."
			::= { hwEponOltStatisticEntry 7 }

		
		hwEponOltStatisticRecv65To127ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 65-127 bytes frames."
			::= { hwEponOltStatisticEntry 8 }

		
		hwEponOltStatisticRecv128To255ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 128-255 bytes frames."
			::= { hwEponOltStatisticEntry 9 }

		
		hwEponOltStatisticRecv256To511ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 256~511 bytes frames."
			::= { hwEponOltStatisticEntry 10 }

		
		hwEponOltStatisticRecv512To1023ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 512-1023 bytes frames."
			::= { hwEponOltStatisticEntry 11 }

		
		hwEponOltStatisticRecv1024To1518ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 1024-1518 bytes frames."
			::= { hwEponOltStatisticEntry 12 }

		
		hwEponOltStatisticRecvOver1518ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received frames containing more than 1518 bytes."
			::= { hwEponOltStatisticEntry 13 }

		
		hwEponOltStatisticRecvUndersizeFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received undersize frames."
			::= { hwEponOltStatisticEntry 14 }

		
		hwEponOltStatisticRecvOversizeFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received oversize frames."
			::= { hwEponOltStatisticEntry 15 }

		
		hwEponOltStatisticRecvFcsErrorFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received FCS errored frames."
			::= { hwEponOltStatisticEntry 16 }
			
                hwEponOltStatisticUniCastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received unicast frames."
			::= { hwEponOltStatisticEntry 17 }
			
	        hwEponOltStatisticRecvOkFrameCnt OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received correct frames."
			::= { hwEponOltStatisticEntry 18 }
			
	        hwEponOltStatisticRecvOkByteCnt OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received correct bytes."
			::= { hwEponOltStatisticEntry 19 }
		
		hwEponOltStatisticTransDataFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent data frames."
			::= { hwEponOltStatisticEntry 20 }
               
		
		hwEponOltStatisticTransDataBytes OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent data bytes."
			::= { hwEponOltStatisticEntry 21 }

		
		hwEponOltStatisticTransUnicastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent uni cast frames."
			::= { hwEponOltStatisticEntry 22 }

		
		hwEponOltStatisticTransMulticastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent multicast frames."
			::= { hwEponOltStatisticEntry 23 }

		
		hwEponOltStatisticTransBoardcastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent broadcast frames."
			::= { hwEponOltStatisticEntry 24 }

		
		hwEponOltStatisticTrans64ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 64 bytes frames."
			::= { hwEponOltStatisticEntry 25 }

		
		hwEponOltStatisticTrans65To127ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 65-127 bytes frames."
			::= { hwEponOltStatisticEntry 26 }

		
		hwEponOltStatisticTrans128To255ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 128-255 bytes frames."
			::= { hwEponOltStatisticEntry 27 }

		
		hwEponOltStatisticTrans256To511ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 256-511 bytes frames."
			::= { hwEponOltStatisticEntry 28 }

		
		hwEponOltStatisticTrans512To1023ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 512-1023 bytes frames."
			::= { hwEponOltStatisticEntry 29 }

		
		hwEponOltStatisticTrans1024To1518ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 1024-1518 bytes frames."
			::= { hwEponOltStatisticEntry 30 }

		
		hwEponOltStatisticTransOver1518ByteFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent frames containing more than 1518 bytes."
			::= { hwEponOltStatisticEntry 31 }
			
	        hwEponOltStatisticTransFcsErrorFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent FCS errored bytes."
			::= { hwEponOltStatisticEntry 32 }
		
		hwEponOltStatisticClear  OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Clear the statistics."
			::= { hwEponOltStatisticEntry 33 }			
			
			
		hwEponOnuPonStatisticTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuPonStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"OLT LLID statistics table."
			::= { hwEponStatisticObjects 2 }

		
		hwEponOnuPonStatisticEntry OBJECT-TYPE
			SYNTAX HwEponOnuPonStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"OLT LLID statistics entry."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOnuPonStatisticTable 1 }

		
		HwEponOnuPonStatisticEntry ::=
			SEQUENCE { 
				hwEponOnuPonStatisticRcv1024To1518byteFrm
					Counter64,
				hwEponOnuPonStatisticRcv128To255byteFrm
					Counter64,
				hwEponOnuPonStatisticRcv256To511byteFrm
					Counter64,
				hwEponOnuPonStatisticRcv512To1023byteFrm
					Counter64,
				hwEponOnuPonStatisticRcv64byteFrm
					Counter64,
				hwEponOnuPonStatisticRcv65To127byteFrm
					Counter64,
				hwEponOnuPonStatisticRcvBcFrame
					Counter64,
				hwEponOnuPonStatisticRcvByte
					Counter64,				
				hwEponOnuPonStatisticRcvCrc8Err
				        Counter64,
				hwEponOnuPonStatisticRcvDelayByte 
				        Counter64,
				hwEponOnuPonStatisticRcvDelayMax 
				        Counter64,
			        hwEponOnuPonStatisticRcvDelayThreshold
				        Counter64,
				hwEponOnuPonStatisticRcvDropByte
				        Counter64,
				hwEponOnuPonStatisticRcvDropFrm
				        Counter64,
				hwEponOnuPonStatisticRcvErrFrm
				        Counter64,
				hwEponOnuPonStatisticRcvErrOntDestinedByte 
				        Counter64,
				hwEponOnuPonStatisticRcvFcsErr 
				        Counter64,
				hwEponOnuPonStatisticRcvFrame
				        Counter64,
				hwEponOnuPonStatisticRcvGreatThan1518byteFrm 
				        Counter64,
				hwEponOnuPonStatisticRcvInvalidSldFrm  
				        Counter64,				        
				hwEponOnuPonStatisticRcvLaserPower 
				        Counter64,				        
				hwEponOnuPonStatisticRcvLineCodeErr
				        Counter64,
				hwEponOnuPonStatisticRcvMcFrame 
				        Counter64,
				hwEponOnuPonStatisticRcvOntDestinedByte
				        Counter64,
				hwEponOnuPonStatisticRcvUcFrame   
				        Counter64,
				hwEponOnuPonStatisticRcvUndersizeFrm  
				        Counter64,
				hwEponOnuPonStatisticSend1024To1518byteFrm  
				        Counter64,
				hwEponOnuPonStatisticSend128To255byteFrm 
				        Counter64,
				hwEponOnuPonStatisticSend256To511byteFrm  
				        Counter64,
				hwEponOnuPonStatisticSend512To1023byteFrm  
				        Counter64,
				hwEponOnuPonStatisticSend64byteFrm 
				        Counter64,
				hwEponOnuPonStatisticSend65To127byteFrm  
				        Counter64,
				hwEponOnuPonStatisticSendBcFrame
				        Counter64,
				hwEponOnuPonStatisticSendByte  
				        Counter64,
				hwEponOnuPonStatisticSendDelayByte
				        Counter64,
				hwEponOnuPonStatisticSendDelayMax 
				        Counter64,				
				hwEponOnuPonStatisticSendDelayThreshold
				        Counter64,
				hwEponOnuPonStatisticSendDropByte
				        Counter64,
				hwEponOnuPonStatisticSendDropFrm 
				        Counter64,
				hwEponOnuPonStatisticSendFrame 
				        Counter64,
				hwEponOnuPonStatisticSendGreatThan1518byteFrm
				        Counter64,
				hwEponOnuPonStatisticSendMcFrame 
				        Counter64,
				hwEponOnuPonStatisticSendUcFrame  
				        Counter64,
				hwEponOnuPonStatisticSendUnusedByte 
				        Counter64,
				hwEponOnuPonStatisticClear
					INTEGER
			 }
		hwEponOnuPonStatisticRcv1024To1518byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 1024-1518 bytes frames."
			::= { hwEponOnuPonStatisticEntry 1 }

		
		hwEponOnuPonStatisticRcv128To255byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 128-255 bytes frames."
			::= { hwEponOnuPonStatisticEntry 2 }

		
		hwEponOnuPonStatisticRcv256To511byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 256-511 bytes frames."
			::= { hwEponOnuPonStatisticEntry 3 }

		
		hwEponOnuPonStatisticRcv512To1023byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 512-1023 bytes frames."
			::= { hwEponOnuPonStatisticEntry 4 }

		
		hwEponOnuPonStatisticRcv64byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 64 bytes frames."
			::= { hwEponOnuPonStatisticEntry 5 }

		
		hwEponOnuPonStatisticRcv65To127byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 65-127 bytes frames."
			::= { hwEponOnuPonStatisticEntry 6 }

		
		hwEponOnuPonStatisticRcvBcFrame OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received broad cast frames."
			::= { hwEponOnuPonStatisticEntry 7 }

		
		hwEponOnuPonStatisticRcvByte OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received bytes."
			::= { hwEponOnuPonStatisticEntry 8 }

		
		hwEponOnuPonStatisticRcvCrc8Err OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received CRC errored bytes."
			::= { hwEponOnuPonStatisticEntry 9 }
			
                hwEponOnuPonStatisticRcvDelayByte OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received delayed bytes."
			::= { hwEponOnuPonStatisticEntry 10 }			
	       
	       hwEponOnuPonStatisticRcvDelayMax OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received delayed MAX bytes."
			::= { hwEponOnuPonStatisticEntry 11 }
			
		hwEponOnuPonStatisticRcvDelayThreshold OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received delayed threshold bytes."
			::= { hwEponOnuPonStatisticEntry 12 }
			
		hwEponOnuPonStatisticRcvDropByte  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received dropped bytes."
			::= { hwEponOnuPonStatisticEntry 13 }
			
		hwEponOnuPonStatisticRcvDropFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received dropped frames."
			::= { hwEponOnuPonStatisticEntry 14 }			
					
		hwEponOnuPonStatisticRcvErrFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received errored frames."
			::= { hwEponOnuPonStatisticEntry 15 }
			
		hwEponOnuPonStatisticRcvErrOntDestinedByte OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received errored destined bytes."
			::= { hwEponOnuPonStatisticEntry 16 }
		hwEponOnuPonStatisticRcvFcsErr  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received FEC errored frames."
			::= { hwEponOnuPonStatisticEntry 17 }
		hwEponOnuPonStatisticRcvFrame OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received frames."
			::= { hwEponOnuPonStatisticEntry 18 }
		hwEponOnuPonStatisticRcvGreatThan1518byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received frames containing more than 1518 bytes."
			::= { hwEponOnuPonStatisticEntry 19 }
			
		hwEponOnuPonStatisticRcvInvalidSldFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received invalid SID frames."
			::= { hwEponOnuPonStatisticEntry 20 }
			
		hwEponOnuPonStatisticRcvLaserPower OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received laser power frames."
			::= { hwEponOnuPonStatisticEntry 21 }
			
		hwEponOnuPonStatisticRcvLineCodeErr OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received frames incorrectly coded."
			::= { hwEponOnuPonStatisticEntry 22 }
			
		hwEponOnuPonStatisticRcvMcFrame OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received multicast frames."
			::= { hwEponOnuPonStatisticEntry 23 }
			
		hwEponOnuPonStatisticRcvOntDestinedByte OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received ONU destined bytes."
			::= { hwEponOnuPonStatisticEntry 24 }
			
		hwEponOnuPonStatisticRcvUcFrame OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received unicast frames."
			::= { hwEponOnuPonStatisticEntry 25 }
			
		hwEponOnuPonStatisticRcvUndersizeFrm  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received under size frames."
			::= { hwEponOnuPonStatisticEntry 26 }
		
		hwEponOnuPonStatisticSend1024To1518byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 1024-1518 bytes frames."
			::= { hwEponOnuPonStatisticEntry 27 }
			
		hwEponOnuPonStatisticSend128To255byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 128-255 bytes frames."
			::= { hwEponOnuPonStatisticEntry 28 }
			
		hwEponOnuPonStatisticSend256To511byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 256-511 bytes frames."
			::= { hwEponOnuPonStatisticEntry 29 }
			
		hwEponOnuPonStatisticSend512To1023byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 512-1023 bytes frames."
			::= { hwEponOnuPonStatisticEntry 30 }
			
		hwEponOnuPonStatisticSend64byteFrm  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 64 bytes frames."
			::= { hwEponOnuPonStatisticEntry 31 }
			
		hwEponOnuPonStatisticSend65To127byteFrm OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent 65-127 bytes frames."
			::= { hwEponOnuPonStatisticEntry 32 }
			
		hwEponOnuPonStatisticSendBcFrame  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent broadcast frames."
			::= { hwEponOnuPonStatisticEntry 33 }
			
		hwEponOnuPonStatisticSendByte OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent bytes."
			::= { hwEponOnuPonStatisticEntry 34 }
						
		hwEponOnuPonStatisticSendDelayByte OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent delayed bytes."
			::= { hwEponOnuPonStatisticEntry 35 }
			
		hwEponOnuPonStatisticSendDelayMax OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent delayed MAX bytes."
			::= { hwEponOnuPonStatisticEntry 36 }
			
		hwEponOnuPonStatisticSendDelayThreshold OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent delayed threshold bytes."
			::= { hwEponOnuPonStatisticEntry 37 }
			
		hwEponOnuPonStatisticSendDropByte  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent dropped bytes."
			::= { hwEponOnuPonStatisticEntry 38 }
			
		hwEponOnuPonStatisticSendDropFrm  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent dropped frames."
			::= { hwEponOnuPonStatisticEntry 39 }
			
		hwEponOnuPonStatisticSendFrame OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent delayed bytes."
			::= { hwEponOnuPonStatisticEntry 40 }
			
		hwEponOnuPonStatisticSendGreatThan1518byteFrm    OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent frames containing more than 1518 bytes."
			::= { hwEponOnuPonStatisticEntry 41 }
			
		hwEponOnuPonStatisticSendMcFrame OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent multicast frames."
			::= { hwEponOnuPonStatisticEntry 42 }
			
		hwEponOnuPonStatisticSendUcFrame OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent unicast frames."
			::= { hwEponOnuPonStatisticEntry 43 }
			
		hwEponOnuPonStatisticSendUnusedByte  OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent unused bytes."
			::= { hwEponOnuPonStatisticEntry 44 }
			
		hwEponOnuPonStatisticClear  OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Clear the statistics."
			::= { hwEponOnuPonStatisticEntry 45 }	

		
		hwEponOnuUniStatisticTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuUniStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU ETH port statistics table."
			::= { hwEponStatisticObjects 3 }

		
		hwEponOnuUniStatisticEntry OBJECT-TYPE
			SYNTAX HwEponOnuUniStatisticEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU ETH port statistics entry."
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuEthPortId }
			::= { hwEponOnuUniStatisticTable 1 }

		
		HwEponOnuUniStatisticEntry ::=
			SEQUENCE { 
				hwEponOnuUniStatisticRecvFrames
					Counter64,
				hwEponOnuUniStatisticRecvMulticastFrames
					Counter64,
				hwEponOnuUniStatisticRecvBroadcastFrames
					Counter64,
				hwEponOnuUniStatisticRecv64OctetFrames
					Counter64,
				hwEponOnuUniStatisticRecv65To127OctetFrames
					Counter64,
				hwEponOnuUniStatisticRecv128To255OctetFrames
					Counter64,
				hwEponOnuUniStatisticRecv256To511OctetFrames
					Counter64,
				hwEponOnuUniStatisticRecv512To1023OctetFrames
					Counter64,
				hwEponOnuUniStatisticRecv1024To1518OctetFrames
					Counter64,
				hwEponOnuUniStatisticRecvUndersizeFrames
					Counter64,
				hwEponOnuUniStatisticRecvTooLongFrames
					Counter64,
				hwEponOnuUniStatisticTransDropFrames
					Counter64,
				hwEponOnuUniStatisticTransFrames
					Counter64,
				hwEponOnuUniStatisticTransMtuExceededDiscardFrames
					Counter64,
				hwEponOnuUniStatisticClear
					INTEGER
			 }

		hwEponOnuUniStatisticRecvFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received frames."
			::= { hwEponOnuUniStatisticEntry 1 }

		
		hwEponOnuUniStatisticRecvMulticastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received multicast frames."
			::= { hwEponOnuUniStatisticEntry 2 }

		
		hwEponOnuUniStatisticRecvBroadcastFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received boardcast frames."
			::= { hwEponOnuUniStatisticEntry 3 }

		
		hwEponOnuUniStatisticRecv64OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 64 bytes frames."
			::= { hwEponOnuUniStatisticEntry 4 }

		
		hwEponOnuUniStatisticRecv65To127OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 65-127 bytes frames."
			::= { hwEponOnuUniStatisticEntry 5 }

		
		hwEponOnuUniStatisticRecv128To255OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 128-255 bytes frames."
			::= { hwEponOnuUniStatisticEntry 6 }

		
		hwEponOnuUniStatisticRecv256To511OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 256-511 bytes frames."
			::= { hwEponOnuUniStatisticEntry 7 }

		
		hwEponOnuUniStatisticRecv512To1023OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 512-1023 bytes frames."
			::= { hwEponOnuUniStatisticEntry 8 }

		
		hwEponOnuUniStatisticRecv1024To1518OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received 1024-1518 bytes frames."
			::= { hwEponOnuUniStatisticEntry 9 }

		
		hwEponOnuUniStatisticRecvUndersizeFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received undersize frames."
			::= { hwEponOnuUniStatisticEntry 10 }

		
		hwEponOnuUniStatisticRecvTooLongFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received oversize frames."
			::= { hwEponOnuUniStatisticEntry 11 }

		
		hwEponOnuUniStatisticTransDropFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Received discarded frames."
			::= { hwEponOnuUniStatisticEntry 12 }

		
		hwEponOnuUniStatisticTransFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Sent frames."
			::= { hwEponOnuUniStatisticEntry 13 }

		
		hwEponOnuUniStatisticTransMtuExceededDiscardFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Frames failed to be sent due to oversize."
			::= { hwEponOnuUniStatisticEntry 14 }

		
		hwEponOnuUniStatisticClear OBJECT-TYPE
			SYNTAX INTEGER { clear(1) }
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Clear the statistics."
			::= { hwEponOnuUniStatisticEntry 15 }

		
		hwEponDisplayAlarmObjects OBJECT IDENTIFIER ::= { hwEponObjects 5 }

		
		hwEponOnuAlarmStateTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuAlarmStateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the ONU alarm state table. 
				This table is used to query the alarm state of an ONU."
			::= { hwEponDisplayAlarmObjects 1 }

		
		hwEponOnuAlarmStateEntry OBJECT-TYPE
			SYNTAX HwEponOnuAlarmStateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU alarm state."
			INDEX { ifIndex, hwEponOnuIndex }
			::= { hwEponOnuAlarmStateTable 1 }

		
		HwEponOnuAlarmStateEntry ::=
			SEQUENCE { 
				hwEponOnuAlarmStateKeyExchangeFail
					INTEGER,
				hwEponOnuAlarmStateDyingGasp
					INTEGER,
				hwEponOnuAlarmStateLinkFault
					INTEGER,
				hwEponOnuFirmWareLoadStateSucc
					INTEGER,
				hwEponOnuFirmWareLoadStateFault
					INTEGER
			 }

		hwEponOnuAlarmStateKeyExchangeFail OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Fail to exchange key."
			::= { hwEponOnuAlarmStateEntry 1 }

		
		hwEponOnuAlarmStateDyingGasp OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The dying-gasp of ONU is generated."
			::= { hwEponOnuAlarmStateEntry 2 }

		
		hwEponOnuAlarmStateLinkFault OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Link fault occurs at OLT."
			::= { hwEponOnuAlarmStateEntry 3 }

		
		hwEponOnuFirmWareLoadStateSucc OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Loading ONU firmware successfully."
			::= { hwEponOnuAlarmStateEntry 4 }

		
		hwEponOnuFirmWareLoadStateFault OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Fail to load ONU firmwar."
			::= { hwEponOnuAlarmStateEntry 5 }

		
		hwEponOnuUniAlarmStateTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOnuUniAlarmStateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the UNI of an ONU alarm state table. 
				This table is used to query the alarm state of the UNI of an ONU."
			::= { hwEponDisplayAlarmObjects 2 }

		
		hwEponOnuUniAlarmStateEntry OBJECT-TYPE
			SYNTAX HwEponOnuUniAlarmStateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU alarm state."
			INDEX { ifIndex, hwEponOnuIndex, hwEponOnuPortId }
			::= { hwEponOnuUniAlarmStateTable 1 }

		
		HwEponOnuUniAlarmStateEntry ::=
			SEQUENCE { 
				hwEponOnuUniAlarmStateTransmitFail
					INTEGER,
				hwEponOnuUniAlarmStateLos
					INTEGER
			 }

		hwEponOnuUniAlarmStateTransmitFail OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Fail to transmit."
			::= { hwEponOnuUniAlarmStateEntry 2 }

		
		hwEponOnuUniAlarmStateLos OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The loss of frame of ONU occurs."
			::= { hwEponOnuUniAlarmStateEntry 3 }

		
		hwEponOltAlarmStateTable OBJECT-TYPE
			SYNTAX SEQUENCE OF HwEponOltAlarmStateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This is the OLT alarm state table. 
				This table is used to query the alarm state of an OLT."
			::= { hwEponDisplayAlarmObjects 3 }

		
		hwEponOltAlarmStateEntry OBJECT-TYPE
			SYNTAX HwEponOltAlarmStateEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"OLT alarm state entry."
			INDEX { ifIndex }
			::= { hwEponOltAlarmStateTable 1 }

		
		HwEponOltAlarmStateEntry ::=
			SEQUENCE { 				
				hwEponOltAlarmStateDegrade
					INTEGER
			 }
		
		hwEponOltAlarmStateDegrade OBJECT-TYPE
			SYNTAX INTEGER
				{
				yes(1),
				no(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"The dying-gasp of ONU is generated."
			::= { hwEponOltAlarmStateEntry 1 }

		
		hwEponTrapObjects OBJECT IDENTIFIER ::= { hwEponObjects 6 }

		
		hwEponSlotIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Slot ID of the EPON interface."
			::= { hwEponTrapObjects 1 }

		
		hwEponCardIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Card ID of the EPON interface."
			::= { hwEponTrapObjects 2 }

		
		hwEponPortIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"Port ID of the EPON interface."
			::= { hwEponTrapObjects 3 }

		
		hwEponTrapOnuId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"ONU ID."
			::= { hwEponTrapObjects 4 }

		
		hwEponTrapMac OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"MAC address of the ONU."
			::= { hwEponTrapObjects 5 }

		
		hwEponTrapPwd OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS accessible-for-notify
			STATUS current
			DESCRIPTION
				"ONU password."
			::= { hwEponTrapObjects 6 }

		
		hwEponTrap OBJECT IDENTIFIER ::= { hwEponObjects 7 }

		
		hwEponOltAlarmLosTrap NOTIFICATION-TYPE
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex }
			STATUS current
			DESCRIPTION 
				"Los alarm."
			::= { hwEponTrap 1 }

		
		hwEponOltAlarmLosResumeTrap NOTIFICATION-TYPE
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex }
			STATUS current
			DESCRIPTION 
				"Los alarm resume alarm."
			::= { hwEponTrap 2 }

		
		hwEponOltAutoFindTrap NOTIFICATION-TYPE
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex, hwEponTrapMac, hwEponTrapPwd
				 }
			STATUS current
			DESCRIPTION 
				"ONU auto finded alarm."
			::= { hwEponTrap 3 }

		
		hwEponOltAlarmTransmitFaultTrap NOTIFICATION-TYPE
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex }
			STATUS current
			DESCRIPTION 
				"OLT send failed."
			::= { hwEponTrap 4 }
			

		
		hwEponOnuAlarmPwdConlictTrap NOTIFICATION-TYPE
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex, hwEponTrapOnuId }
			STATUS current
			DESCRIPTION 
				"Regist conflicted by password alarm."
			::= { hwEponTrap 5 }

		
		hwEponOnuOnlineTrap NOTIFICATION-TYPE
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex, hwEponTrapOnuId, hwEponTrapMac, 
				hwEponTrapPwd }
			STATUS current
			DESCRIPTION 
				"ONU on line alarm."
			::= { hwEponTrap 6 }

		
		hwEponOnuOfflineTrap NOTIFICATION-TYPE
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex, hwEponTrapOnuId, hwEponTrapMac, 
				hwEponTrapPwd }
			STATUS current
			DESCRIPTION 
				"ONU off line alarm."
			::= { hwEponTrap 7 }

		
		hwEponConformance OBJECT IDENTIFIER ::= { hwEponObjects 8 }

		
		hwEponCompliances OBJECT IDENTIFIER ::= { hwEponConformance 1 }

		
		hwEponCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"The compliance statement for SNMP entities which implement
				the HUAWEI-EPON-MIB."
			MODULE -- this module
				MANDATORY-GROUPS { hwEponGlobalGroup, hwEponControlGroup, hwEponProfileGroup, hwEponStatisticGroup, hwEponDisplayAlarmObjectsGroup, 
					hwEponTrapObjectsGroup, hwEponTrapGroup }
			::= { hwEponCompliances 1 }

		
		hwEponGroups OBJECT IDENTIFIER ::= { hwEponConformance 2 }

		
		hwEponGlobalGroup OBJECT-GROUP
			OBJECTS { hwEponAutoFindOnuAge, hwEponCtcOuiId, hwEponChangePasswordAge }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to configure the
				EPON implementation behavior.
				This group is mandatory for agents which implement the EPON."
			::= { hwEponGroups 1 }

		
		hwEponControlGroup OBJECT-GROUP
			OBJECTS { hwEponOltControlfarthest, hwEponOltControlAutofindOnuEnable, hwEponOltControlStatus, hwEponOltControlUpStreamBandWidth, 
				hwEponOltControlDownStreamBandWidth, hwEponOnuId, hwEponOnuAuthMode, hwEponOnuMacAddress, hwEponOnuPassword, hwEponOnuTimeout, 
				hwEponOnuManagementMode, hwEponOnuLineProfName, hwEponOnuServiceProfName, hwEponOnuActiveStatus,hwEponOnuDescription, hwEponOnuVendorId, 
				hwEponOnuModel, hwEponOnuOnuIdentifier, hwEponOnuHardwareVersion, hwEponOnuSoftwareVersion, hwEponOnuChipVenderId, 
				hwEponOnuChipModel, hwEponOnuChipVersion, hwEponOnuChipDesignDate, hwEponOnuFirmwareVersion, 
				hwEponOnuReset, hwEponOnuReRegister, hwEponOnuReDiscovery, hwEponOnuRunStatus, hwEponOnuDistance, 
				hwEponOnuRtt, hwEponOnuLastUpTime, hwEponOnuLastDownTime, 
				hwEponOnuLastDownCause, hwEponAutoFindOnuInfoMacAddress, hwEponAutoFindOnuInfoPasswordValue, hwEponOnuPotsPortNum, hwEponOnuFePortsNum, 
				hwEponOnuGePortsNum, hwEponOnuTdmPortsNum, hwEponOnuFecSupport, hwEponOnuSupportBackupBattery, hwEponOnuUpQueueNum, 
				hwEponOnuUpQueueNumPerPort, hwEponOnuDownQueueNum, hwEponOnuDownQueueNumPerPort, hwEponOnuFePortList, hwEponOnuGePortList, 
				hwEponOnuSupportMulticastQuickLeave, hwEponOnuIpAddress, hwEponOnuNetMask, hwEponOnuNetGateway, hwEponOnuEthOperateStatus, 
				hwEponOnuEthFlowcontrolSwitch, hwEponOnuTdmPortOperateStatus, hwEponOnuPotsPortOperateStatus, hwEponOltPortDefaultVlanId, hwEponOltPortDefaultVlanBatch, 
				hwEponOltPortDefaultVlanOnuStartId, hwEponOltPortDefaultVlanOnuEndId, hwEponOnuCarProfileNameIndex, hwEponOnuTrafficPolicyNameIndex, hwEponVlanStackingOrMapping,hwEponOnuDextVlanId, 
				hwEponOnuDintVlanId, hwEponOnuPopExtVlanId, hwEponOnuVlanCopyPri, hwEponOnuIntVlanRemarkPri, hwEponOnuExtVlanRemarkPri, 
				hwEponOnuIntVlanPri, hwEponOnuExtVlanPri, hwEponVlanMappingBatch, hwEponVlanMappingOnuStartId, hwEponVlanMappingOnuEndId, 
				hwEponOnuUserMacAddress, hwEponOnuUserMacAddressNumber, hwEponOnuForwardAction, hwEponOnuAlarmAction, hwEponOnuRowStatus, 
				hwEponOnuCfgCarRowStatus, hwEponOltPortDefaultVlanRowStatus, hwEponVlanMappingRowStatus, hwEponOnuIpRowStates, hwEponOltPortStaticMacRowStatus, 
				hwEponOnuMacLimitRowStatus, hwEponOnuIpManageVlan }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to control the EPON implementation behavior."
			::= { hwEponGroups 2 }

		
		hwEponProfileGroup OBJECT-GROUP
			OBJECTS { hwEponLineProfileBindNum, hwEponLineProfileDbaProfileName, hwEponLineProfileEncryptMode, hwEponLineProfileQueueSetIndex1Threshold, hwEponLineProfileQueueSetIndex2Threshold, 
				hwEponLineProfileQueueSetIndex3Threshold, hwEponOnuSrvProfileBindNum, hwEponSrvProfileMulticastMode, hwEponSrvProfileFecMode, hwEponSrvProfileMulticastQuickLeaveSwitch, 
				hwEponSrvProfOnuPortCfgMaxMacAddressNum, hwEponSrvProfOnuPortCfgMulticastStripSwitch, hwEponDbaProfileFixedRate, hwEponDbaProfileAssuredRate, hwEponDbaProfileMaxRate, 
				hwEponDbaProfileReferenceNum, hwEponDbaProfileEntryStatus, hwEponOnuSnmpProfileVersion, hwEponOnuSnmpProfileReadCommunityName, hwEponOnuSnmpProfileWriteCommunityName, 
				hwEponOnuSnmpProfileTrapHostIp, hwEponOnuSnmpProfileTrapHostSrcUdpPort, hwEponOnuSnmpProfileSecurityName, hwEponOnuPortClassPriMark, hwEponOnuPortClassConditionNum, 
				hwEponOnuPortClassFieldSelect1, hwEponOnuPortClassOperator1, hwEponOnuPortClassMatchValue1, hwEponOnuPortClassFieldSelect2, hwEponOnuPortClassOperator2, 
				hwEponOnuPortClassMatchValue2, hwEponSrvProfOnuPortCarCfgCir, hwEponOnuPortClassFieldSelect3, hwEponOnuPortClassOperator3, hwEponOnuPortClassMatchValue3, 
				hwEponSrvProfOnuPortCarCfgPir, hwEponSrvProfOnuPortCarCfgCbs, hwEponSrvProfOnuPortCarCfgEbs, hwEponOnuPortClassQueueIndexId, hwEponLineProfileRowStatus, 
				hwEponOnuSrvProfileRowStatus, hwEponOnuSnmpProfileRowStatus, hwEponSrvProfOnuPortCarCfgRowStatus, hwEponOnuPortClassProfileRowStatus, hwEponSrvProfMulticastVlanCfgRowStatus, 
				hwEponOnuSnmpProfName, hwEponDbaTypeIndex, hwEponOnuPortClassFieldSelect4, hwEponOnuPortClassOperator4, hwEponOnuPortClassMatchValue4, 
				hwEponSrvProfOnuPortVlanMode, hwEponSrvProfOnuPortVlanTranslationRowStatus, hwEponSrvProfOnuPortDefaultVlanId, hwEponSrvProfOnuPortVlanRowStatus, hwEponSrvProfOnuPortVlanTranslationSVlanId, 
				hwEponSrvProfOnuPortAddToVlanId }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to control the EPON profile behavior."				
			::= { hwEponGroups 3 }

		
		hwEponStatisticGroup OBJECT-GROUP
			OBJECTS {hwEponOltStatisticRecvDataFrames,hwEponOltStatisticRecvDataBytes,hwEponOltStatisticRecvMulticastFrames,hwEponOltStatisticRecvBoardcastFrames
                                ,hwEponOltStatisticRecvErrorFrames,hwEponOltStatisticRecvErrorBytes,hwEponOltStatisticRecv64ByteFrames,hwEponOltStatisticRecv65To127ByteFrames
				,hwEponOltStatisticRecv128To255ByteFrames,hwEponOltStatisticRecv256To511ByteFrames,hwEponOltStatisticRecv512To1023ByteFrames,
				hwEponOltStatisticRecv1024To1518ByteFrames,hwEponOltStatisticRecvOver1518ByteFrames,hwEponOltStatisticRecvUndersizeFrames,hwEponOltStatisticRecvOversizeFrames,
				hwEponOltStatisticRecvFcsErrorFrames,hwEponOltStatisticUniCastFrames, hwEponOltStatisticRecvOkFrameCnt,hwEponOltStatisticRecvOkByteCnt,
				hwEponOltStatisticTransDataFrames,hwEponOltStatisticTransDataBytes,hwEponOltStatisticTransUnicastFrames,hwEponOltStatisticTransMulticastFrames,
				hwEponOltStatisticTransBoardcastFrames,hwEponOltStatisticTrans64ByteFrames,hwEponOltStatisticTrans65To127ByteFrames,hwEponOltStatisticTrans128To255ByteFrames,
				hwEponOltStatisticTrans256To511ByteFrames,hwEponOltStatisticTrans512To1023ByteFrames,hwEponOltStatisticTrans1024To1518ByteFrames,
				hwEponOltStatisticTransOver1518ByteFrames, hwEponOltStatisticTransFcsErrorFrames,hwEponOltStatisticClear,hwEponOnuPonStatisticRcv1024To1518byteFrm,
				hwEponOnuPonStatisticRcv128To255byteFrm,hwEponOnuPonStatisticRcv256To511byteFrm,hwEponOnuPonStatisticRcv512To1023byteFrm,hwEponOnuPonStatisticRcv64byteFrm,
				hwEponOnuPonStatisticRcv65To127byteFrm,hwEponOnuPonStatisticRcvBcFrame,hwEponOnuPonStatisticRcvByte,hwEponOnuPonStatisticRcvCrc8Err,
				hwEponOnuPonStatisticRcvDelayByte ,hwEponOnuPonStatisticRcvDelayMax ,hwEponOnuPonStatisticRcvDelayThreshold,hwEponOnuPonStatisticRcvDropByte,
				hwEponOnuPonStatisticRcvDropFrm,hwEponOnuPonStatisticRcvErrFrm,hwEponOnuPonStatisticRcvErrOntDestinedByte ,hwEponOnuPonStatisticRcvFcsErr ,
				hwEponOnuPonStatisticRcvFrame,	hwEponOnuPonStatisticRcvGreatThan1518byteFrm ,	hwEponOnuPonStatisticRcvInvalidSldFrm, hwEponOnuPonStatisticRcvLaserPower, 
				hwEponOnuPonStatisticRcvLineCodeErr,hwEponOnuPonStatisticRcvMcFrame, hwEponOnuPonStatisticRcvOntDestinedByte,hwEponOnuPonStatisticRcvUcFrame,  
				hwEponOnuPonStatisticRcvUndersizeFrm, hwEponOnuPonStatisticSend1024To1518byteFrm,hwEponOnuPonStatisticSend128To255byteFrm ,hwEponOnuPonStatisticSend256To511byteFrm,  	
				hwEponOnuPonStatisticSend512To1023byteFrm , hwEponOnuPonStatisticSend64byteFrm ,hwEponOnuPonStatisticSend65To127byteFrm ,hwEponOnuPonStatisticSendBcFrame,
				hwEponOnuPonStatisticSendByte , hwEponOnuPonStatisticSendDelayByte,hwEponOnuPonStatisticSendDelayMax ,hwEponOnuPonStatisticSendDelayThreshold,			
				hwEponOnuPonStatisticSendDropByte,hwEponOnuPonStatisticSendDropFrm ,hwEponOnuPonStatisticSendFrame ,hwEponOnuPonStatisticSendGreatThan1518byteFrm,
				hwEponOnuPonStatisticSendMcFrame ,hwEponOnuPonStatisticSendUcFrame,hwEponOnuPonStatisticSendUnusedByte , hwEponOnuPonStatisticClear,
				hwEponOnuUniStatisticRecvFrames,hwEponOnuUniStatisticRecvMulticastFrames,hwEponOnuUniStatisticRecvBroadcastFrames,hwEponOnuUniStatisticRecv64OctetFrames,
				hwEponOnuUniStatisticRecv65To127OctetFrames,hwEponOnuUniStatisticRecv128To255OctetFrames,hwEponOnuUniStatisticRecv256To511OctetFrames,
				hwEponOnuUniStatisticRecv512To1023OctetFrames,hwEponOnuUniStatisticRecv1024To1518OctetFrames,hwEponOnuUniStatisticRecvUndersizeFrames,
				hwEponOnuUniStatisticRecvTooLongFrames,hwEponOnuUniStatisticTransDropFrames,hwEponOnuUniStatisticTransFrames,hwEponOnuUniStatisticTransMtuExceededDiscardFrames,			 
                                hwEponOnuUniStatisticClear	 }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to control the EPON Statistic behavior."
			::= { hwEponGroups 4 }

		
		hwEponDisplayAlarmObjectsGroup OBJECT-GROUP
			OBJECTS {hwEponOnuAlarmStateKeyExchangeFail,hwEponOnuAlarmStateDyingGasp, hwEponOnuAlarmStateLinkFault, hwEponOnuFirmWareLoadStateSucc, hwEponOnuFirmWareLoadStateFault, 
				 hwEponOltAlarmStateDegrade, hwEponOnuUniAlarmStateTransmitFail, hwEponOnuUniAlarmStateLos }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to control the EPON alarm behavior."
			::= { hwEponGroups 5 }

		
		hwEponTrapObjectsGroup OBJECT-GROUP
			OBJECTS { hwEponSlotIndex, hwEponCardIndex, hwEponPortIndex, hwEponTrapOnuId, hwEponTrapMac, 
				hwEponTrapPwd }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to control the EPON trap object behavior."
			::= { hwEponGroups 6 }

		
		hwEponTrapGroup NOTIFICATION-GROUP
			NOTIFICATIONS { hwEponOltAlarmLosTrap, hwEponOltAlarmLosResumeTrap, hwEponOltAutoFindTrap, hwEponOltAlarmTransmitFaultTrap,  
				 hwEponOnuAlarmPwdConlictTrap, hwEponOnuOnlineTrap, hwEponOnuOfflineTrap }
			STATUS current
			DESCRIPTION 
				"The collection of objects which are used to control the EPON trap behavior."
			::= { hwEponGroups 7 }
		
	
	END

--
-- HUAWEI-EPON-MIB.mib
--
