--  =================================================================
-- Copyright (C) 2003 by  HUAWEI TECHNOLOGIES. All rights reserved
-- 
-- Description: HUAWEI-SZONE-MIB
-- Reference:   Security Zone
-- Version:     V1.0
-- History:
--  
--  V1.20 2005-05-30 <PERSON>(22510) added mplsVpnVrfName as table index,
--              Added fields to HwSZoneInterZoneEntry and hwSZoneInterZoneCfgGroup.
--  V1.00 2003-03-18 <PERSON>(28193)  initial version
-- =================================================================

HUAWEI-SZONE-MIB DEFINITIONS ::= BEGIN

    IMPORTS
        OBJECT-GROUP            
            FROM SNMPv2-CONF            
        Integer32, G<PERSON>ge32, OBJECT-TYPE, MODULE-IDENTITY            
            FROM SNMPv2-SMI     
        TruthValue            
            FROM SNMPv2-TC       
        RowStatus            
            FROM SNMPv2-TC
        mplsVpnVrfName            
            FROM MPLS-VPN-MIB            
        hwDatacomm
            FROM HUAWEI-MIB;

    hwSZONE MODULE-IDENTITY 
        LAST-UPDATED "200304080900Z"        -- April 08, 2003 at 09:00 GMT
        ORGANIZATION 
            "Huawei Technologies co.,Ltd."
        CONTACT-INFO 
            "
            R&D BeiJing, Huawei Technologies co.,Ltd.
            Huawei Bld.,NO.3 Xinxi Rd.,
            Shang-Di Information Industry Base,
            Hai-Dian District Beijing P.R. China
            Zip:100085
            Http://www.huawei.com
            E-mail:<EMAIL>
            "
        DESCRIPTION 
            "
            V1.00
            The HUAWEI-SZONE-MIB contains objects to manage the security zone for firewall product.
            "
        ::= { hwDatacomm 15 }

    --
    -- Node definitions
    --
    
    -- *******.4.1.2011.*********
    hwSZoneZoneCfg OBJECT IDENTIFIER ::= { hwSZONE 1 }
    
    -- *******.4.1.2011.*********.1
    hwSZoneZoneTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HwSZoneZoneEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            "This table contains all the security zones defined in the system."
        ::= { hwSZoneZoneCfg 1 }
    
    -- *******.4.1.2011.*********.1.1
    hwSZoneZoneEntry OBJECT-TYPE
        SYNTAX HwSZoneZoneEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            "Define the parameters of security zone."
        INDEX { mplsVpnVrfName, hwSZoneZoneID }
        ::= { hwSZoneZoneTable 1 }
    
    HwSZoneZoneEntry ::=
        SEQUENCE { 
            hwSZoneZoneID
                Integer32,
            hwSZoneZoneName
                OCTET STRING,
            hwSZoneSecPriority
                Integer32,
            hwSZoneZoneStatus
                RowStatus
            }

    -- *******.4.1.2011.*********.1.1.1
    hwSZoneZoneID OBJECT-TYPE
        SYNTAX Integer32 (0..128)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "
            An internal number to distinguish security zone.
            Different zone has different number.
            "
        ::= { hwSZoneZoneEntry 1 }
    
    -- *******.4.1.2011.*********.1.1.2
    hwSZoneZoneName OBJECT-TYPE
        SYNTAX OCTET STRING (SIZE (1..31))
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "
            The name of security zone.
            Different zone has different name."
        ::= { hwSZoneZoneEntry 2 }
    
    -- *******.4.1.2011.*********.1.1.3
    hwSZoneSecPriority OBJECT-TYPE
        SYNTAX Integer32 (0..200)
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "
            The priority of security zone.
            Different zone has different priority.
            "
        ::= { hwSZoneZoneEntry 3 }
    
    -- *******.4.1.2011.*********.1.1.4
    hwSZoneZoneStatus OBJECT-TYPE
        SYNTAX RowStatus
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "Row status, can be CreateAndGo or Destroy."
        ::= { hwSZoneZoneEntry 4 }
    
    -- *******.4.1.2011.*********.2
    hwSZoneZoneIFTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HwSZoneZoneIFEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            "
            This table define the interfaces belong to which security zone.
            An interface can only belong to one security zone."
        ::= { hwSZoneZoneCfg 2 }
    
    -- *******.4.1.2011.*********.2.1
    hwSZoneZoneIFEntry OBJECT-TYPE
        SYNTAX HwSZoneZoneIFEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            " "
        INDEX { hwSZoneIFZoneID, hwSZoneZoneIFIndex }
        ::= { hwSZoneZoneIFTable 1 }
    
    HwSZoneZoneIFEntry ::=
        SEQUENCE { 
            hwSZoneIFZoneID
                Integer32,
            hwSZoneZoneIFIndex
                Gauge32,
            hwSZoneZoneIFStatus
                RowStatus
         }

    -- *******.4.1.2011.*********.2.1.1
    hwSZoneIFZoneID OBJECT-TYPE
        SYNTAX Integer32 (0..128)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The ID of security zone."
        ::= { hwSZoneZoneIFEntry 1 }
    
    -- *******.4.1.2011.*********.2.1.2
    hwSZoneZoneIFIndex OBJECT-TYPE
        SYNTAX Gauge32
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The index of interface."
        ::= { hwSZoneZoneIFEntry 2 }
    
    -- *******.4.1.2011.*********.2.1.3
    hwSZoneZoneIFStatus OBJECT-TYPE
        SYNTAX RowStatus
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "Row status, can be CreateAndGo or Destroy."
        ::= { hwSZoneZoneIFEntry 3 }
    
    -- *******.4.1.2011.*********
    hwSZoneInterZoneCfg OBJECT IDENTIFIER ::= { hwSZONE 2 }
    
    -- *******.4.1.2011.*********.1
    hwSZoneInterZoneTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HwSZoneInterZoneEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            "
            This table define all inter zone of system.
            The inter zone can't be create directly, 
            when a security zone is created, the related inter zones are created automatically,
            and when deleting a zone, the related inter zones are deleted.
            "
        ::= { hwSZoneInterZoneCfg 1 }
    
    -- *******.4.1.2011.*********.1.1
    hwSZoneInterZoneEntry OBJECT-TYPE
        SYNTAX HwSZoneInterZoneEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
            " "
        INDEX { mplsVpnVrfName, hwSZoneInterZoneZoneID1, hwSZoneInterZoneZoneID2 }
        ::= { hwSZoneInterZoneTable 1 }
    
    HwSZoneInterZoneEntry ::=
        SEQUENCE { 
            hwSZoneInterZoneZoneID1
                Integer32,
            hwSZoneInterZoneZoneID2
                Integer32,
            hwSZoneInterZoneEnableFW
                TruthValue,
            hwSZoneInterZoneStatus
                RowStatus
            }

    -- *******.4.1.2011.*********.1.1.1
    hwSZoneInterZoneZoneID1 OBJECT-TYPE
        SYNTAX Integer32 (0..128)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The ID of first zone that compose the inter-zone."
        ::= { hwSZoneInterZoneEntry 1 }
    
    -- *******.4.1.2011.*********.1.1.2
    hwSZoneInterZoneZoneID2 OBJECT-TYPE
        SYNTAX Integer32 (0..128)
        MAX-ACCESS read-only
        STATUS current
        DESCRIPTION
            "The ID of second zone that compose the inter-zone."
        ::= { hwSZoneInterZoneEntry 2 }
    
    -- *******.4.1.2011.*********.1.1.3
    hwSZoneInterZoneEnableFW OBJECT-TYPE
        SYNTAX TruthValue
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "The switch indicates whether the interzone enables firewall."
        DEFVAL { false }
        ::= { hwSZoneInterZoneEntry 3 }
                    
    -- *******.4.1.2011.*********.1.1.4
    hwSZoneInterZoneStatus OBJECT-TYPE
        SYNTAX RowStatus
        MAX-ACCESS read-create
        STATUS current
        DESCRIPTION
            "Row status, can be CreateAndGo."
        ::= { hwSZoneInterZoneEntry 4 }
        
    -- *******.4.1.2011.*********
    hwSZoneConformance OBJECT IDENTIFIER ::= { hwSZONE 3 }
    
    -- *******.4.1.2011.*********.1
    hwSZoneCompliance OBJECT IDENTIFIER ::= { hwSZoneConformance 1 }
    
    -- *******.4.1.2011.*********.2
    hwSZoneMibGroups OBJECT IDENTIFIER ::= { hwSZoneConformance 2 }
    
    -- *******.4.1.2011.*********.2.1
    hwSZoneZoneCfgGroup OBJECT-GROUP
        OBJECTS { hwSZoneZoneName, hwSZoneSecPriority, hwSZoneZoneStatus, hwSZoneZoneIFIndex, hwSZoneZoneIFStatus, 
            hwSZoneZoneID, hwSZoneIFZoneID }
        STATUS current
        DESCRIPTION 
            "Description."
        ::= { hwSZoneMibGroups 1 }
    
    -- *******.4.1.2011.*********.2.2
    hwSZoneInterZoneCfgGroup OBJECT-GROUP
        OBJECTS { hwSZoneInterZoneZoneID1, hwSZoneInterZoneZoneID2, hwSZoneInterZoneEnableFW, hwSZoneInterZoneStatus}
        STATUS current
        DESCRIPTION 
            "Description."
        ::= { hwSZoneMibGroups 2 }

END
