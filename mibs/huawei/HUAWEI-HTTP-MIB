--  =================================================================
-- Copyright (C) 2009 by  HUAWEI TECHNOLOGIES. All rights reserved
-- 
-- Description: HUAWEI HTTP MIB, this mib will maintain information of HTTP 
--              protocol for datacomm product.  
-- Reference:
-- Version:     V1.00
-- History:
--  
--  V1.00 2009-03-27 initial version
-- =================================================================
HUAWEI-HTTP-MIB DEFINITIONS ::= BEGIN
	IMPORTS
    DateAndTime
        FROM SNMPv2-TC
        
		OBJECT-GROUP			
        FROM SNMPv2-CONF        

    MODULE-IDENTITY, OBJECT-TYPE, Integer32,OBJECT-IDENTITY,
    IpAddress, Unsigned32, NOTIFICATION-TYPE
        FROM SNMPv2-SMI
        
 	EnabledStatus            
        FROM P-BRIDGE-MIB         
                
    hwDatacomm
        FROM HUAWEI-MIB;
               
    hwHttpMIB MODULE-IDENTITY
        LAST-UPDATED "200903271630Z"		-- July 15, 2008 at 14:30 GMT
        ORGANIZATION 
            "Huawei Technologies co.,Ltd."
        CONTACT-INFO 
            "R&D NanJing, Huawei Technologies co.,Ltd.
				High hope mansion, 
				Baixia road,
				Nanjing city
				Zip:100085
				Http://www.huawei.com
				E-mail:<EMAIL>        
				Zip:100000
            "
        DESCRIPTION
            "This file is a HTTP MIB. It provides the functions such as
           globally enabling or disabling the HTTP service, configuring the global
           port number, showing http server and users information."
            ::= { hwDatacomm 192 }
            
    -- ============================================================================
    -- Node definitions
    -- ============================================================================ 
		hwHttpObjects  OBJECT IDENTIFIER ::= { hwHttpMIB 1 }
		hwHttpConformance  OBJECT IDENTIFIER ::= { hwHttpMIB 2 }
		
	  -- ============================================================================
	  --
	  -- ======================= Objects definitions=================================
	  --
	  -- ============================================================================ 		
		hwHttpConfiguration  OBJECT IDENTIFIER ::= { hwHttpObjects 1 }
		
    hwHttpEnable OBJECT-TYPE 
    SYNTAX   EnabledStatus
		{
			 enabled(1),
			 disabled(2)
		}
		MAX-ACCESS read-write
		STATUS current
		DESCRIPTION
			"The object indicates globally enable or disable the HTTP configuration. If the hwHttpEnable 
       	 	is 1, HTTP server is enabled. If the hwHttpEnable is 2, HTTP server is 
       	 	disabled. By default, HTTP server is enabled."   
		DEFVAL { 1 }
		::= { hwHttpConfiguration 1 }
		
	hwHttpPortNum OBJECT-TYPE 
    SYNTAX   Integer32 (80|1025..55535)
		MAX-ACCESS read-write
		STATUS current
		DESCRIPTION
			"The object indicates globally port number the HTTP configuration.  The value rangs from 1025 to 55535,
			   User can modify HTTP server listen in port number, 
			   By default, HTTP server listen in 80 port."
		DEFVAL { 80 }
		::= { hwHttpConfiguration 2 } 
		
	hwHttpAclNum OBJECT-TYPE 
    SYNTAX   Integer32 (0|2000..2999)
		MAX-ACCESS read-write
		STATUS current
		DESCRIPTION
			"The object indicates globally ACL the HTTP configuration.  The value rangs from 2000 to 2999,
			   user can modify HTTP server ACL number,By default, the ACL number is 0."
		::= { hwHttpConfiguration 3 }
		
	hwHttpTimeOut OBJECT-TYPE 
    SYNTAX   Integer32 (1..35791)
		MAX-ACCESS read-write
		STATUS current
		DESCRIPTION
			"The object indicates globally overtime the HTTP configuration.  The value rangs from 1 to 35791,
			   User can modify HTTP server overtime interval, 
			   By default, The time is 3 minutes."
		DEFVAL { 3 }
		::= { hwHttpConfiguration 4 }
		
	hwHttpOnlineUserNum OBJECT-TYPE 
    SYNTAX   Integer32 (0..5)
		MAX-ACCESS read-only
		STATUS current
		DESCRIPTION
			"The object indicates the number of concurrent server users online."
		::= { hwHttpConfiguration 5 }
		
	hwHttpMaxUserNum OBJECT-TYPE 
    SYNTAX   Integer32
		MAX-ACCESS read-only
		STATUS current
		DESCRIPTION
			"The object indicates maximum number of concurrent server users allowed."
		::= { hwHttpConfiguration 6 }	
		
  hwHttpUserInfoTable  OBJECT-TYPE
    SYNTAX SEQUENCE OF HwHttpUserInfoEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION 
	  "HTTP user infomation configuration table."
    ::= { hwHttpConfiguration  7 }
      
  hwHttpUserInfoEntry OBJECT-TYPE
    SYNTAX HwHttpUserInfoEntry
		MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION 
    "Entries of the HTTP user infomation configuration table."
    INDEX{ hwHttpUserIndex }
    ::= { hwHttpUserInfoTable 1}  
  
  HwHttpUserInfoEntry ::=
  	SEQUENCE{
  			hwHttpUserIndex            Integer32,   
  	    	hwHttpUserName             OCTET STRING,
  	    	hwHttpUserIpAddr           IpAddress,
			hwHttpUserLoginTime        DateAndTime,
			hwHttpUserTimeOut          Integer32 					      		
      		}
  
   hwHttpUserIndex  OBJECT-TYPE
  	SYNTAX Integer32(1..5)
  	MAX-ACCESS not-accessible
  	STATUS current
 		DESCRIPTION 
 				"The object indicates the user index of user has logined HTTP server. The value rangs from 1 to 5."     
   		::= {hwHttpUserInfoEntry 1}
     		
  hwHttpUserName  OBJECT-TYPE
  	SYNTAX OCTET STRING(SIZE(1..64))
  	MAX-ACCESS read-only
  	STATUS current
 		DESCRIPTION 
 				"The object indicates the user name of user has logined HTTP server. It ranges from 1
     		     to 64."     
   		::= {hwHttpUserInfoEntry 2}
   		
  hwHttpUserIpAddr  OBJECT-TYPE
  	SYNTAX IpAddress
  	MAX-ACCESS read-only
  	STATUS current
 		DESCRIPTION 
 				"The object indicates the source IP address of user has logined HTTP server."     
   		::= {hwHttpUserInfoEntry 3}
   		
  hwHttpUserLoginTime  OBJECT-TYPE
  	SYNTAX DateAndTime
  	MAX-ACCESS read-only
  	STATUS current
 		DESCRIPTION 
 				"The object indicates the date and time of user logined HTTP server."     
   		::= {hwHttpUserInfoEntry 4}
   		   		   		
   hwHttpUserTimeOut  OBJECT-TYPE
  	SYNTAX Integer32
  	MAX-ACCESS read-only
  	STATUS current
 		DESCRIPTION 
 				"The object indicates the overtime of user logined HTTP server."     
   		::= {hwHttpUserInfoEntry 5}

    --
	-- ***********************************************************
	--
	-- HAUWEIDLDPMIBCONFORMANCE
	--
	-- ***********************************************************
	--
     
  hwHttpCompliances OBJECT IDENTIFIER ::= { hwHttpConformance 1 }
	hwHttpGroups OBJECT IDENTIFIER ::= { hwHttpConformance 2 }
	-- compliance statements
	hwHttpCompliance MODULE-COMPLIANCE
	STATUS current
	DESCRIPTION
		"The compliance statement for SNMP entities which implement
		the HUAWEI-HTTP-MIB."
	MODULE -- this module
	MANDATORY-GROUPS { 
	hwHttpConfigGroup,
	hwHttpInfoGroup
	}
	::= { hwHttpCompliances 1 } 
	
	-- MIB groupings
	hwHttpConfigGroup OBJECT-GROUP
	OBJECTS {  
	hwHttpEnable,
	hwHttpPortNum,
	hwHttpAclNum,
	hwHttpTimeOut
	}
	STATUS current
	DESCRIPTION
		"The collection of objects which are used to configure the
		HTTP implementation behavior.
		This group is mandatory for agents which implement the HTTP."
	::= { hwHttpGroups 1 }
	
	hwHttpInfoGroup OBJECT-GROUP
	OBJECTS {  
	hwHttpOnlineUserNum,
	hwHttpMaxUserNum,
	hwHttpUserName,
	hwHttpUserIpAddr,
	hwHttpUserLoginTime,
	hwHttpUserTimeOut
	}
	STATUS current
	DESCRIPTION
		"The collection of objects indicates the information of HTTP server and users."
	::= { hwHttpGroups 2 }	
	
	END     
    
--
-- HUAWEI-HTTP-MIB.mib
--    		 						 						 		
						 		
            


