--
-- FD-ONU-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Friday, July 07, 2017 at 17:25:47
--

--  FD-ONU-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, May 17, 2017 at 18:58:46
-- 
--  FD-ONU-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, July 14, 2015 at 17:19:55
-- 
--  FD-ONU-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Wednesday, June 10, 2015 at 13:57:02
-- 
--  FD-ONU-MIB.my
-- MIB generated by MG-<PERSON>OFT Visual MIB Builder Version 6.0  Build 88
-- Friday, May 15, 2015 at 11:07:25
-- 
--  FD-ONU-MIB.txt
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, November 04, 2014 at 15:12:17
-- 
--  FD-ONU-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Tuesday, November 04, 2014 at 14:03:23
-- 
--  FD-ONU-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Tuesday, January 08, 2013 at 20:37:51
-- 
--  FD-ONU-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Thursday, December 27, 2012 at 19:17:08
-- 
--  FD-ONU-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Thursday, July 19, 2012 at 17:36:42
-- 
--  FD-ONU-MIB.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Thursday, May 27, 2010 at 18:35:03
-- 

	FD-ONU-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			epon, DeviceType, DeviceStatus, OperSwitch, DeviceOperation			
				FROM EPON-EOC-MIB			
			oltId, linkId			
				FROM FD-OLT-MIB			
			ponCardSlotId			
				FROM FD-SYSTEM-MIB			
			OBJECT-GROUP, MODULE-COMPLIANCE			
				FROM SNMPv2-CONF			
			IpAddress, Integer32, Counter32, OBJECT-TYPE, MODULE-IDENTITY			
				FROM SNMPv2-SMI			
			DisplayString, MacAddress			
				FROM SNMPv2-TC;
	
	
--    *******.4.1.34592.1.3.4
-- November 04, 2014 at 13:58 GMT
-- *******.4.1.34592.1.3.4
-- November 04, 2014 at 13:58 GMT
		-- *******.4.1.34592.1.3.4
		fdOnu MODULE-IDENTITY 
			LAST-UPDATED "201411041358Z"		-- November 04, 2014 at 13:58 GMT
			ORGANIZATION 
				"epon eoc factory."
			CONTACT-INFO 
				"  "
			DESCRIPTION 
				"ONU mib module"
			::= { epon 4 }

		
	
	
--
-- Node definitions
--
	
--  Node definitions
-- 
-- Node definitions
-- 
-- *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		onuBaseManageTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuBaseManageEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { fdOnu 1 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		onuBaseManageEntry OBJECT-TYPE
			SYNTAX OnuBaseManageEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuBaseManageTable 1 }

		
		OnuBaseManageEntry ::=
			SEQUENCE { 
				onuId
					Integer32,
				onuDeviceType
					DeviceType,
				onuFactorySerial
					OCTET STRING,
				onuUserInfo
					DisplayString,
				onuHwRev
					DisplayString,
				onuFwRev
					DisplayString,
				onuBaseMac
					MacAddress,
				maxAllowedLLIDs
					Integer32,
				registeredLLIDNum
					Integer32,
				onuOnLineStatus
					DeviceStatus,
				onuUserTrafficEnable
					OperSwitch,
				onuRangeValue
					Integer32,
				supportUniPorts
					Integer32,
				onuOperation
					DeviceOperation,
				onuUpgradeStat
					INTEGER,
				onuLinkIdMap
					OCTET STRING,
				onuMgmtType
					INTEGER,
				onuLaserRxPower
					Integer32,
				onuLaserTxPower
					Integer32,
				onuLaserVoltage
					Integer32,
				onuLaserBias
					Integer32,
				onuLaserTemperature
					Integer32,
				onuVenderId
					Integer32,
				onuCtcDeviceType
					INTEGER,
				onuLoid
					OCTET STRING,
				onuPasswd
					OCTET STRING
			 }

--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		onuId OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Serial No allocated for each ONU which register on each
				PON port for identify them conveniencly. The ID allocated
				for each ONU will stay until the ONU is deleted."
			::= { onuBaseManageEntry 1 }

		
--    *******.4.1.34592.*******.1.2
-- *******.4.1.34592.*******.1.2
		-- *******.4.1.34592.*******.1.2
		onuDeviceType OBJECT-TYPE
			SYNTAX DeviceType
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 2 }

		
--    *******.4.1.34592.*******.1.3
-- *******.4.1.34592.*******.1.3
		-- *******.4.1.34592.*******.1.3
		onuFactorySerial OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..30))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 3 }

		
--    *******.4.1.34592.*******.1.4
-- *******.4.1.34592.*******.1.4
		-- *******.4.1.34592.*******.1.4
		onuUserInfo OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 4 }

		
--    *******.4.1.34592.*******.1.5
-- *******.4.1.34592.*******.1.5
		-- *******.4.1.34592.*******.1.5
		onuHwRev OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 5 }

		
--    *******.4.1.34592.*******.1.6
-- *******.4.1.34592.*******.1.6
		-- *******.4.1.34592.*******.1.6
		onuFwRev OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 6 }

		
--    *******.4.1.34592.*******.1.7
-- *******.4.1.34592.*******.1.7
		-- *******.4.1.34592.*******.1.7
		onuBaseMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 7 }

		
--    *******.4.1.34592.*******.1.9
-- *******.4.1.34592.*******.1.9
		-- *******.4.1.34592.*******.1.9
		maxAllowedLLIDs OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 9 }

		
--    *******.4.1.34592.*******.1.10
-- *******.4.1.34592.*******.1.10
		-- *******.4.1.34592.*******.1.10
		registeredLLIDNum OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 10 }

		
--    *******.4.1.34592.*******.1.11
-- *******.4.1.34592.*******.1.11
		-- *******.4.1.34592.*******.1.11
		onuOnLineStatus OBJECT-TYPE
			SYNTAX DeviceStatus
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 11 }

		
--    *******.4.1.34592.*******.1.12
-- *******.4.1.34592.*******.1.12
		-- *******.4.1.34592.*******.1.12
		onuUserTrafficEnable OBJECT-TYPE
			SYNTAX OperSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 12 }

		
--    *******.4.1.34592.*******.1.13
-- *******.4.1.34592.*******.1.13
		-- *******.4.1.34592.*******.1.13
		onuRangeValue OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuBaseManageEntry 13 }

		
--    *******.4.1.34592.*******.1.14
-- *******.4.1.34592.*******.1.14
		-- *******.4.1.34592.*******.1.14
		supportUniPorts OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 14 }

		
--    *******.4.1.34592.*******.1.32
-- *******.4.1.34592.*******.1.32
		-- *******.4.1.34592.*******.1.32
		onuOperation OBJECT-TYPE
			SYNTAX DeviceOperation
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Only off line ONUs can be delete by seting the value of this object to 'delete', for the OnuS , add 'deregister(7)' operation."
			::= { onuBaseManageEntry 32 }

		
--    *******.4.1.34592.*******.1.33
-- *******.4.1.34592.*******.1.33
		-- *******.4.1.34592.*******.1.33
		onuUpgradeStat OBJECT-TYPE
			SYNTAX INTEGER
				{
				booting(1),
				normalRun(2),
				upgrading(6),
				upgradeOk(7),
				upgradeErr(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 33 }

		
--    *******.4.1.34592.*******.1.34
-- *******.4.1.34592.*******.1.34
		-- *******.4.1.34592.*******.1.34
		onuLinkIdMap OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..17))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 34 }

		
--    *******.4.1.34592.*******.1.35
-- *******.4.1.34592.*******.1.35
		-- *******.4.1.34592.*******.1.35
		onuMgmtType OBJECT-TYPE
			SYNTAX INTEGER
				{
				tk(1),
				ctc(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 35 }

		
--    *******.4.1.34592.*******.1.36
-- *******.4.1.34592.*******.1.36
		-- *******.4.1.34592.*******.1.36
		onuLaserRxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 36 }

		
--    *******.4.1.34592.*******.1.37
-- *******.4.1.34592.*******.1.37
		-- *******.4.1.34592.*******.1.37
		onuLaserTxPower OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 37 }

		
		-- *******.4.1.34592.*******.1.38
		onuLaserVoltage OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 38 }

		
		-- *******.4.1.34592.*******.1.39
		onuLaserBias OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 39 }

		
		-- *******.4.1.34592.*******.1.40
		onuLaserTemperature OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 40 }

		
--  *******.4.1.34592.*******.1.38
		-- *******.4.1.34592.*******.1.41
		onuVenderId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 41 }

		
		-- *******.4.1.34592.*******.1.42
		onuCtcDeviceType OBJECT-TYPE
			SYNTAX INTEGER
				{
				sfu(1),
				hgu(2),
				sbu(3),
				fixMdu(4),
				ethChassisMdu(5),
				dslChassisMdu(6),
				mediumDslChassisMdu(7),
				mixChassisMdu(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 42 }

		
		-- *******.4.1.34592.*******.1.43
		onuLoid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..25))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 43 }

		
		-- *******.4.1.34592.*******.1.44
		onuPasswd OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..25))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuBaseManageEntry 44 }

		
--    *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		onuAdvancedManage OBJECT IDENTIFIER ::= { fdOnu 2 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		onuChipInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuChipInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuAdvancedManage 1 }

		
--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		onuChipInfoEntry OBJECT-TYPE
			SYNTAX OnuChipInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuChipInfoTable 1 }

		
		OnuChipInfoEntry ::=
			SEQUENCE { 
				onuChipProCode
					Integer32,
				onuChipProVer
					Integer32,
				onuChipId
					Integer32,
				onuChipVer
					Integer32,
				onuBootVer
					Integer32,
				onuPersVer
					Integer32,
				onuChipApp0Ver
					Integer32,
				onuChipApp1Ver
					Integer32,
				onuChipDiagVer
					Integer32
			 }

--    *******.4.1.34592.*******.1.1.1
-- *******.4.1.34592.*******.1.1.1
		-- *******.4.1.34592.*******.1.1.1
		onuChipProCode OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 1 }

		
--    *******.4.1.34592.*******.1.1.2
-- *******.4.1.34592.*******.1.1.2
		-- *******.4.1.34592.*******.1.1.2
		onuChipProVer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 2 }

		
--    *******.4.1.34592.*******.1.1.3
-- *******.4.1.34592.*******.1.1.3
		-- *******.4.1.34592.*******.1.1.3
		onuChipId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 3 }

		
--    *******.4.1.34592.*******.1.1.4
-- *******.4.1.34592.*******.1.1.4
		-- *******.4.1.34592.*******.1.1.4
		onuChipVer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 4 }

		
--    *******.4.1.34592.*******.1.1.5
-- *******.4.1.34592.*******.1.1.5
		-- *******.4.1.34592.*******.1.1.5
		onuBootVer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 5 }

		
--    *******.4.1.34592.*******.1.1.6
-- *******.4.1.34592.*******.1.1.6
		-- *******.4.1.34592.*******.1.1.6
		onuPersVer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 6 }

		
--    *******.4.1.34592.*******.1.1.7
-- *******.4.1.34592.*******.1.1.7
		-- *******.4.1.34592.*******.1.1.7
		onuChipApp0Ver OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 7 }

		
--    *******.4.1.34592.*******.1.1.8
-- *******.4.1.34592.*******.1.1.8
		-- *******.4.1.34592.*******.1.1.8
		onuChipApp1Ver OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 8 }

		
--    *******.4.1.34592.*******.1.1.9
-- *******.4.1.34592.*******.1.1.9
		-- *******.4.1.34592.*******.1.1.9
		onuChipDiagVer OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuChipInfoEntry 9 }

		
--    *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
		-- *******.4.1.34592.*******.2
		onuAdvancedConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuAdvancedConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"This table contain the information which is not normally
				used but may be usefull in some special circumstance"
			::= { onuAdvancedManage 2 }

		
--    *******.4.1.34592.*******.2.1
-- *******.4.1.34592.*******.2.1
		-- *******.4.1.34592.*******.2.1
		onuAdvancedConfigEntry OBJECT-TYPE
			SYNTAX OnuAdvancedConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Each entry in this table contain the information for one ONU"
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuAdvancedConfigTable 1 }

		
		OnuAdvancedConfigEntry ::=
			SEQUENCE { 
				onuAddiVlanEthType
					OCTET STRING,
				onuRstpEnable
					OperSwitch,
				onuLocalSwitch
					OperSwitch,
				onuCatv
					OperSwitch,
				onuCatvRfLevel
					INTEGER,
				onuMgmtIpAddr
					IpAddress,
				onuMgmtNetmask
					IpAddress,
				onuMgmtGateway
					IpAddress,
				onuMgmtCVlan
					Integer32,
				onuMgmtSVlan
					Integer32,
				onuMgmtPriority
					Integer32
			 }

--    *******.4.1.34592.*******.2.1.1
-- *******.4.1.34592.*******.2.1.1
		-- *******.4.1.34592.*******.2.1.1
		onuAddiVlanEthType OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (4))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description. Reserved.
				Additional Ethernet type to identify vlan frames.
				
				 0     1          2                3
				---------------------------------------------
				vlan EthType| tag upstream | tag downstream |
				---------------------------------------------
				   2byte         1byte            1byte
				
				vlan EthType: default 0x8100
				tag upstream: 1->use this type to tag upstream, 0->untag
				tag downstream: 1->use this type to tag downstream, 0->untag"
			::= { onuAdvancedConfigEntry 1 }

		
--    *******.4.1.34592.*******.2.1.2
-- *******.4.1.34592.*******.2.1.2
		-- *******.4.1.34592.*******.2.1.2
		onuRstpEnable OBJECT-TYPE
			SYNTAX OperSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 2 }

		
--    *******.4.1.34592.*******.2.1.3
-- *******.4.1.34592.*******.2.1.3
		-- *******.4.1.34592.*******.2.1.3
		onuLocalSwitch OBJECT-TYPE
			SYNTAX OperSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 3 }

		
--    *******.4.1.34592.*******.2.1.4
-- *******.4.1.34592.*******.2.1.4
		-- *******.4.1.34592.*******.2.1.4
		onuCatv OBJECT-TYPE
			SYNTAX OperSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 4 }

		
--    *******.4.1.34592.*******.2.1.5
-- *******.4.1.34592.*******.2.1.5
		-- *******.4.1.34592.*******.2.1.5
		onuCatvRfLevel OBJECT-TYPE
			SYNTAX INTEGER
				{
				level0(0),
				level1(1),
				level2(2),
				level3(3),
				level4(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ONU Catv RF Level:
				Level0:		P0<=-9dBm
				Level1:		-9dBm<=P0<-6dBm
				Level2: 	-6dBm<=P0<-3dBm
				Level3: 	-3dBm<=P0<0dBm
				Level4:		P0>=0dBm"
			::= { onuAdvancedConfigEntry 5 }

		
--  *******.4.1.34592.*******.2.1.6
		-- *******.4.1.34592.*******.2.1.6
		onuMgmtIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 6 }

		
--  *******.4.1.34592.*******.2.1.7
		-- *******.4.1.34592.*******.2.1.7
		onuMgmtNetmask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 7 }

		
--  *******.4.1.34592.*******.2.1.8
		-- *******.4.1.34592.*******.2.1.8
		onuMgmtGateway OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 8 }

		
--  *******.4.1.34592.*******.2.1.9
		-- *******.4.1.34592.*******.2.1.9
		onuMgmtCVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 9 }

		
--  *******.4.1.34592.*******.2.1.10
		-- *******.4.1.34592.*******.2.1.10
		onuMgmtSVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 10 }

		
--  *******.4.1.34592.*******.2.1.11
		-- *******.4.1.34592.*******.2.1.11
		onuMgmtPriority OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuAdvancedConfigEntry 11 }

		
--    *******.4.1.34592.*******.3
-- *******.4.1.34592.*******.3
		-- *******.4.1.34592.*******.3
		onuStormCtrl OBJECT IDENTIFIER ::= { onuAdvancedManage 3 }

		
--    *******.4.1.34592.*******.3.1
-- *******.4.1.34592.*******.3.1
		-- *******.4.1.34592.*******.3.1
		onuStormCtrlIntervalTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuStormCtrlIntervalEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStormCtrl 1 }

		
--    *******.4.1.34592.*******.3.1.1
-- *******.4.1.34592.*******.3.1.1
		-- *******.4.1.34592.*******.3.1.1
		onuStormCtrlIntervalEntry OBJECT-TYPE
			SYNTAX OnuStormCtrlIntervalEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuStormCtrlIntervalTable 1 }

		
		OnuStormCtrlIntervalEntry ::=
			SEQUENCE { 
				onuStormCtrlInterval
					Integer32
			 }

--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuStormCtrlInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStormCtrlIntervalEntry 1 }

		
--    *******.4.1.34592.*******.3.2
-- *******.4.1.34592.*******.3.2
		-- *******.4.1.34592.*******.3.2
		onuUniStormCtrlBroadcastTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniStormCtrlBroadcastEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStormCtrl 2 }

		
--    *******.4.1.34592.*******.3.2.1
-- *******.4.1.34592.*******.3.2.1
		-- *******.4.1.34592.*******.3.2.1
		onuUniStormCtrlBroadcastEntry OBJECT-TYPE
			SYNTAX OnuUniStormCtrlBroadcastEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { onuUniStormCtrlBroadcastTable 1 }

		
		OnuUniStormCtrlBroadcastEntry ::=
			SEQUENCE { 
				onuUniStormCtrlBroadcastEnable
					INTEGER,
				onuUniStormCtrlBroadcastMode
					INTEGER,
				onuUniStormCtrlBroadcastAction
					INTEGER,
				onuUniStormCtrlBroadcastInRate
					Integer32,
				onuUniStormCtrlBroadcastOutRate
					Integer32
			 }

--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlBroadcastEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"0-disable,1-enable."
			::= { onuUniStormCtrlBroadcastEntry 1 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlBroadcastMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				bps(0),
				pps(1),
				ratio(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"0-bps,1-pps,2-ratio."
			::= { onuUniStormCtrlBroadcastEntry 2 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlBroadcastAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				block(0),
				shutdown(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlBroadcastEntry 3 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlBroadcastInRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlBroadcastEntry 4 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlBroadcastOutRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlBroadcastEntry 5 }

		
--    *******.4.1.34592.*******.3.3
-- *******.4.1.34592.*******.3.3
		-- *******.4.1.34592.*******.3.3
		onuUniStormCtrlMulticastTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniStormCtrlMulticastEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStormCtrl 3 }

		
--    *******.4.1.34592.*******.3.3.1
-- *******.4.1.34592.*******.3.3.1
		-- *******.4.1.34592.*******.3.3.1
		onuUniStormCtrlMulticastEntry OBJECT-TYPE
			SYNTAX OnuUniStormCtrlMulticastEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { onuUniStormCtrlMulticastTable 1 }

		
		OnuUniStormCtrlMulticastEntry ::=
			SEQUENCE { 
				onuUniStormCtrlMulticastEnable
					INTEGER,
				onuUniStormCtrlMulticastMode
					INTEGER,
				onuUniStormCtrlMulticastAction
					INTEGER,
				onuUniStormCtrlMulticastInRate
					Integer32,
				onuUniStormCtrlMulticastOutRate
					Integer32
			 }

--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlMulticastEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"0-disable,1-enable."
			::= { onuUniStormCtrlMulticastEntry 1 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlMulticastMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				bps(0),
				pps(1),
				ratio(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"0-bps,1-pps,2-ratio."
			::= { onuUniStormCtrlMulticastEntry 2 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlMulticastAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				block(0),
				shutdown(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlMulticastEntry 3 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlMulticastInRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlMulticastEntry 4 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlMulticastOutRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlMulticastEntry 5 }

		
--    *******.4.1.34592.*******.3.4
-- *******.4.1.34592.*******.3.4
		-- *******.4.1.34592.*******.3.4
		onuUniStormCtrlUnicastTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniStormCtrlUnicastEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStormCtrl 4 }

		
--    *******.4.1.34592.*******.3.4.1
-- *******.4.1.34592.*******.3.4.1
		-- *******.4.1.34592.*******.3.4.1
		onuUniStormCtrlUnicastEntry OBJECT-TYPE
			SYNTAX OnuUniStormCtrlUnicastEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { onuUniStormCtrlUnicastTable 1 }

		
		OnuUniStormCtrlUnicastEntry ::=
			SEQUENCE { 
				onuUniStormCtrlUnicastEnable
					INTEGER,
				onuUniStormCtrlUnicastMode
					INTEGER,
				onuUniStormCtrlUnicastAction
					INTEGER,
				onuUniStormCtrlUnicastInRate
					Integer32,
				onuUniStormCtrlUnicastOutRate
					Integer32
			 }

--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlUnicastEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"0-disable,1-enable."
			::= { onuUniStormCtrlUnicastEntry 1 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlUnicastMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				bps(0),
				pps(1),
				ratio(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"0-bps,1-pps,2-ratio."
			::= { onuUniStormCtrlUnicastEntry 2 }

		
--    *******.4.1.34592.*******.3.4.1.3
-- *******.4.1.34592.*******.3.4.1.3
		-- *******.4.1.34592.*******.3.4.1.3
		onuUniStormCtrlUnicastAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				block(0),
				shutdown(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlUnicastEntry 3 }

		
--    *******.4.1.34592.*******.3.4.1.4
-- *******.4.1.34592.*******.3.4.1.4
		-- *******.4.1.34592.*******.3.4.1.4
		onuUniStormCtrlUnicastInRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlUnicastEntry 4 }

		
--    *******.4.1.34592.*******.3.4.1.5
-- *******.4.1.34592.*******.3.4.1.5
		-- *******.4.1.34592.*******.3.4.1.5
		onuUniStormCtrlUnicastOutRate OBJECT-TYPE
			SYNTAX Integer32 (0..1000000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStormCtrlUnicastEntry 5 }

		
--    *******.4.1.34592.*******.3.5
-- *******.4.1.34592.*******.3.5
		-- *******.4.1.34592.*******.3.5
		onuUniStormCtrlMixTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniStormCtrlMixEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStormCtrl 5 }

		
--    *******.4.1.34592.*******.3.5.1
-- *******.4.1.34592.*******.3.5.1
		-- *******.4.1.34592.*******.3.5.1
		onuUniStormCtrlMixEntry OBJECT-TYPE
			SYNTAX OnuUniStormCtrlMixEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { onuUniStormCtrlMixTable 1 }

		
		OnuUniStormCtrlMixEntry ::=
			SEQUENCE { 
				onuUniStormCtrlType
					INTEGER,
				onuUniStormCtrlMode
					INTEGER,
				onuUniStormCtrlAction
					INTEGER,
				onuUniStormCtrlInRate
					Integer32,
				onuUniStormCtrlOutRate
					Integer32
			 }

--    *******.4.1.34592.*******.3.5.1.2
-- *******.4.1.34592.*******.3.5.1.2
		-- *******.4.1.34592.*******.3.5.1.2
		onuUniStormCtrlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				broadcast(1),
				multicast(2),
				bcMc(3),
				unicast(4),
				bcUc(5),
				mcUc(6),
				bcMcUc(7)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Storm filter control type. bc:broadcast,umc:unknown multicast,uuc:unknown unitcast. 0-disable,1-bc,2-umc,3-bc+umc,4-uuc,5-bc+uuc,6-umc+uuc,7-bc+umc+uuc."
			::= { onuUniStormCtrlMixEntry 2 }

		
--    *******.4.1.34592.*******.3.5.1.3
-- *******.4.1.34592.*******.3.5.1.3
		-- *******.4.1.34592.*******.3.5.1.3
		onuUniStormCtrlMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				kbps(0),
				pps(1),
				ratio(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Storm filter control mode:  pps,kbps OR ratio."
			::= { onuUniStormCtrlMixEntry 3 }

		
--    *******.4.1.34592.*******.3.5.1.4
-- *******.4.1.34592.*******.3.5.1.4
		-- *******.4.1.34592.*******.3.5.1.4
		onuUniStormCtrlAction OBJECT-TYPE
			SYNTAX INTEGER
				{
				block(0),
				shutdown(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"the storm control action: block OR shutdown ."
			::= { onuUniStormCtrlMixEntry 4 }

		
--    *******.4.1.34592.*******.3.5.1.5
-- *******.4.1.34592.*******.3.5.1.5
		-- *******.4.1.34592.*******.3.5.1.5
		onuUniStormCtrlInRate OBJECT-TYPE
			SYNTAX Integer32 (8..1048568)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Input stream :
				The rate unit is 1 kbps and the range is from 8k to 1048568k.
				The granularity of rate is 8 kbps. The ifg_include parameter is used
				"
			::= { onuUniStormCtrlMixEntry 5 }

		
--    *******.4.1.34592.*******.*******
-- *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		onuUniStormCtrlOutRate OBJECT-TYPE
			SYNTAX Integer32 (8..1048568)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Output stream :
				The rate unit is 1 kbps and the range is from 8k to 1048568k.
				The granularity of rate is 8 kbps. The ifg_include parameter is used"
			::= { onuUniStormCtrlMixEntry 6 }

		
--    *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		onuUniPortTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { fdOnu 3 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		onuUniPortEntry OBJECT-TYPE
			SYNTAX OnuUniPortEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
			""
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { onuUniPortTable 1 }

		
		OnuUniPortEntry ::=
			SEQUENCE { 
				uniPortId
					INTEGER,
				uniPortUserInfo
					DisplayString,
				uniPortLink
					INTEGER,
				uniPortAutoNego
					OperSwitch,
				uniPortSpeed
					INTEGER,
				uniPortDuplex
					INTEGER,
				uniPortFlowCtrl
					OperSwitch,
				uniPortMacEntryLimit
					Integer32,
				uniPortMacAgeTime
					Integer32,
				uniPortFowardMode
					INTEGER,
				uniPortEnable
					OperSwitch,
				uniPortRstpState
					INTEGER,
				uniPortName
					DisplayString,
				uniPortType
					INTEGER,
				uniPortIndex
					INTEGER	
			 }

--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		uniPortId OBJECT-TYPE
			SYNTAX INTEGER
				{
				uniPort1(1),
				uniPort2(2),
				uniPort3(3),
				uniPort4(4),
				uniPort5(5),
				uniPort6(6),
				uniPort7(7),
				uniPort8(8),
				uniPort9(9),
				uniPort10(10),
				uniPort11(11),
				uniPort12(12),
				uniPort13(13),
				uniPort14(14),
				uniPort15(15),
				uniPort16(16),
				uniPort17(17),
				uniPort18(18),
				uniPort19(19),
				uniPort20(20),
				uniPort21(21),
				uniPort22(22),
				uniPort23(23),
				uniPort24(24),
				uniPort25(25),
				uniPort26(26),
				uniPort27(27),
				uniPort28(28),
				uniPort29(29),
				uniPort30(30),
				uniPort31(31),
				uniPort32(32),
				uniPort33(33),
				uniPort34(34),
				uniPort35(35),
				uniPort36(36),
				uniPort37(37),
				uniPort38(38),
				uniPort39(39),
				uniPort40(40),
				uniPort41(41),
				uniPort42(42),
				uniPort43(43),
				uniPort44(44),
				uniPort45(45),
				uniPort46(46),
				uniPort47(47),
				uniPort48(48)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 1 }

		
--    *******.4.1.34592.*******.1.2
-- *******.4.1.34592.*******.1.2
		-- *******.4.1.34592.*******.1.2
		uniPortUserInfo OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 2 }

		
--    *******.4.1.34592.*******.1.3
-- *******.4.1.34592.*******.1.3
		-- *******.4.1.34592.*******.1.3
		uniPortLink OBJECT-TYPE
			SYNTAX INTEGER
				{
				linkup(1),
				linkdown(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 3 }

		
--    *******.4.1.34592.*******.1.4
-- *******.4.1.34592.*******.1.4
		-- *******.4.1.34592.*******.1.4
		uniPortAutoNego OBJECT-TYPE
			SYNTAX OperSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 4 }

		
--    *******.4.1.34592.*******.1.5
-- *******.4.1.34592.*******.1.5
		-- *******.4.1.34592.*******.1.5
		uniPortSpeed OBJECT-TYPE
			SYNTAX INTEGER
				{
				mbps10(1),
				mbps100(2),
				mbps1000(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 5 }

		
--    *******.4.1.34592.*******.1.6
-- *******.4.1.34592.*******.1.6
		-- *******.4.1.34592.*******.1.6
		uniPortDuplex OBJECT-TYPE
			SYNTAX INTEGER
				{
				full(1),
				half(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 6 }

		
--    *******.4.1.34592.*******.1.7
-- *******.4.1.34592.*******.1.7
		-- *******.4.1.34592.*******.1.7
		uniPortFlowCtrl OBJECT-TYPE
			SYNTAX OperSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 7 }

		
--    *******.4.1.34592.*******.1.8
-- *******.4.1.34592.*******.1.8
		-- *******.4.1.34592.*******.1.8
		uniPortMacEntryLimit OBJECT-TYPE
			SYNTAX Integer32 (0..64)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 8 }

		
--    *******.4.1.34592.*******.1.9
-- *******.4.1.34592.*******.1.9
		-- *******.4.1.34592.*******.1.9
		uniPortMacAgeTime OBJECT-TYPE
			SYNTAX Integer32 (0..286)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" aging timeout, unit is 1s"
			::= { onuUniPortEntry 9 }

		
--    *******.4.1.34592.*******.1.10
-- *******.4.1.34592.*******.1.10
		-- *******.4.1.34592.*******.1.10
		uniPortFowardMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				d8021mode(1),
				dropUntilLearned(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 10 }

		
--    *******.4.1.34592.*******.1.11
-- *******.4.1.34592.*******.1.11
		-- *******.4.1.34592.*******.1.11
		uniPortEnable OBJECT-TYPE
			SYNTAX OperSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortEntry 11 }

		
--    *******.4.1.34592.*******.1.12
-- *******.4.1.34592.*******.1.12
		-- *******.4.1.34592.*******.1.12
		uniPortRstpState OBJECT-TYPE
			SYNTAX INTEGER
				{
				normal(1),
				blocking(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"onu port rstp state:normal or blocking"
			::= { onuUniPortEntry 12 }

		
		-- *******.4.1.34592.*******.1.13
		uniPortName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortEntry 13 }

		
		-- *******.4.1.34592.*******.1.14
		uniPortType OBJECT-TYPE
			SYNTAX INTEGER
				{
				fe(1),
				ge(2),
				pon(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortEntry 14 }

		-- *******.4.1.34592.*******.1.15
		uniPortIndex OBJECT-TYPE
			SYNTAX INTEGER
				{
				uniPort1(1),
				uniPort2(2),
				uniPort3(3),
				uniPort4(4),
				uniPort5(5),
				uniPort6(6),
				uniPort7(7),
				uniPort8(8),
				uniPort9(9),
				uniPort10(10),
				uniPort11(11),
				uniPort12(12),
				uniPort13(13),
				uniPort14(14),
				uniPort15(15),
				uniPort16(16),
				uniPort17(17),
				uniPort18(18),
				uniPort19(19),
				uniPort20(20),
				uniPort21(21),
				uniPort22(22),
				uniPort23(23),
				uniPort24(24),
				uniPort25(25),
				uniPort26(26),
				uniPort27(27),
				uniPort28(28),
				uniPort29(29),
				uniPort30(30),
				uniPort31(31),
				uniPort32(32),
				uniPort33(33),
				uniPort34(34),
				uniPort35(35),
				uniPort36(36),
				uniPort37(37),
				uniPort38(38),
				uniPort39(39),
				uniPort40(40),
				uniPort41(41),
				uniPort42(42),
				uniPort43(43),
				uniPort44(44),
				uniPort45(45),
				uniPort46(46),
				uniPort47(47),
				uniPort48(48)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortEntry 15 }
		
		
		
--    *******.4.1.34592.1.3.4.4
-- *******.4.1.34592.1.3.4.4
		-- *******.4.1.34592.1.3.4.4
		onuQueueCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuQueueCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { fdOnu 4 }

		
--    *******.4.1.34592.1.3.4.4.1
-- *******.4.1.34592.1.3.4.4.1
		-- *******.4.1.34592.1.3.4.4.1
		onuQueueCfgEntry OBJECT-TYPE
			SYNTAX OnuQueueCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuQueueCfgTable 1 }

		
		OnuQueueCfgEntry ::=
			SEQUENCE { 
				onuQueueCfgData
					OCTET STRING
			 }

--    *******.4.1.34592.1.3.4.4.1.1
-- *******.4.1.34592.1.3.4.4.1.1
		-- *******.4.1.34592.1.3.4.4.1.1
		onuQueueCfgData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"				contain all queue config information for one ONU.
				the value format is:
				Get/SET:
				--------------------------------------------------------------
				| value length | number of links | link0 queues |queue0 size |...
				--------------------------------------------------------------
				       1	             <USER>               <GROUP>            1
				                                                |<-repeat queues->			
				                                 |<---repeat link's numbers--->
				 ---------------------------------------------------------------------------------------------
				 ...| port numbers | port0 queues |   queue0 size  |...|flooding queues | flooding queue size |
				 ---------------------------------------------------------------------------------------------
				            1             <USER>               <GROUP>                     1                    1
				                                  |<-repeat queues->|				                                  
				                   |<-----repeat port number times-->|
				 para comment:
				 value length: dont include itself's size
				 port numbers: for FD104B, fixed value, 1 
				 flooding queues: for now, fixed 1
				 flooding queue id: 
				"
			::= { onuQueueCfgEntry 1 }

		
--    *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		onuAclRuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuAclRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { fdOnu 5 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		onuAclRuleEntry OBJECT-TYPE
			SYNTAX OnuAclRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, onuIntPortId }
			::= { onuAclRuleTable 1 }

		
		OnuAclRuleEntry ::=
			SEQUENCE { 
				onuIntPortId
					INTEGER,
				onuAclRuleCfgData
					OCTET STRING
			 }

--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		onuIntPortId OBJECT-TYPE
			SYNTAX INTEGER
				{
				onuPonPort(1),
				onuIntUniPort(2)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuAclRuleEntry 1 }

		
--    *******.4.1.34592.*******.1.2
-- *******.4.1.34592.*******.1.2
		-- *******.4.1.34592.*******.1.2
		onuAclRuleCfgData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"				onu ACL rule data format.
				when read or write, the data format is:
				-------------------------------------------------------------------------------
				| data length | global flags | rule numbers | rule flag | precedence | action |
				-------------------------------------------------------------------------------
				       2             <USER>               <GROUP>            1           1          1
				                                            |<----repeat rule number times-----
				----------------------------------------------------------------
				| action para | clause number | field | match value | operator |
				----------------------------------------------------------------
				      2              <USER>            <GROUP>          8            1
				                              |<---repeat clause number times-->|
				---------------------------------------------------------------->  
				
				 
				para comment:  
				data length: don't include the length area itself.
				"
			::= { onuAclRuleEntry 2 }

		
--    *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		onuPortVlanTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuPortVlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { fdOnu 6 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		onuPortVlanEntry OBJECT-TYPE
			SYNTAX OnuPortVlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, onuPortId }
			::= { onuPortVlanTable 1 }

		
		OnuPortVlanEntry ::=
			SEQUENCE { 
				onuPortId
					INTEGER,
				onuPortVlanData
					OCTET STRING
			 }

--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		onuPortId OBJECT-TYPE
			SYNTAX INTEGER
				{
				ponPort(1),
				uniPort1(2),
				uniPort2(3),
				uniPort3(4),
				uniPort4(5),
				uniPort5(6),
				uniPort6(7),
				uniPort7(8),
				uniPort8(9),
				uniPort9(10),
				uniPort10(11),
				uniPort11(12),
				uniPort12(13),
				uniPort13(14),
				uniPort14(15),
				uniPort15(16),
				uniPort16(17),
				uniPort17(18),
				uniPort18(19),
				uniPort19(20),
				uniPort20(21),
				uniPort21(22),
				uniPort22(23),
				uniPort23(24),
				uniPort24(25),
				uniPort25(26),
				uniPort26(27),
				uniPort27(28),
				uniPort28(29),
				uniPort29(30),
				uniPort30(31),
				uniPort31(32),
				uniPort32(33),
				uniPort33(34),
				uniPort34(35),
				uniPort35(36),
				uniPort36(37),
				uniPort37(38),
				uniPort38(39),
				uniPort39(40),
				uniPort40(41),
				uniPort41(42),
				uniPort42(43),
				uniPort43(44),
				uniPort44(45),
				uniPort45(46),
				uniPort46(47),
				uniPort47(48)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPortVlanEntry 1 }

		
--    *******.4.1.34592.*******.1.2
-- *******.4.1.34592.*******.1.2
		-- *******.4.1.34592.*******.1.2
		onuPortVlanData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"				Self defines data strucure for onu port vlan information.
				Data format:  
				size(unit:byte)|    1             2             1               2         2       ...
				  --------------------------------------------------------------------------
				para		  | data length |option bit| forwarding policy |  pvid | vlan number |...
				           --------------------------------------------------------------------------  
				       2               <USER>                 <GROUP>              2                 1                 1
				  ---------------------------------------------     ----------------------------------------------
				  | vlan tag |egress tag modification| reserved|...|vlan tag | egress tag modification | reserved |    
				  ---------------------------------------------     ----------------------------------------------
				  |<.................vlan 1...................>|...|<...............vlan n.......................>|
				  
				  So, the total data length should be: 8+4*(vlan nun).
				"
			::= { onuPortVlanEntry 2 }

		
--    *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		onuPortQos OBJECT IDENTIFIER ::= { fdOnu 7 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		portEgressShappingTable OBJECT-TYPE
			SYNTAX SEQUENCE OF PortEgressShappingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuPortQos 1 }

		
--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		portEgressShappingEntry OBJECT-TYPE
			SYNTAX PortEgressShappingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { portEgressShappingTable 1 }

		
		PortEgressShappingEntry ::=
			SEQUENCE { 
				scheduleAlgorithm
					INTEGER,
				maxTrafficOutputRate
					Integer32,
				outputModule
					INTEGER
			 }

--    *******.4.1.34592.*******.1.1.1
-- *******.4.1.34592.*******.1.1.1
		-- *******.4.1.34592.*******.1.1.1
		scheduleAlgorithm OBJECT-TYPE
			SYNTAX INTEGER
				{
				weightedFair(1),
				strictPriority(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { portEgressShappingEntry 1 }

		
--    *******.4.1.34592.*******.1.1.2
-- *******.4.1.34592.*******.1.1.2
		-- *******.4.1.34592.*******.1.1.2
		maxTrafficOutputRate OBJECT-TYPE
			SYNTAX Integer32 (0..1024000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { portEgressShappingEntry 2 }

		
--    *******.4.1.34592.*******.1.1.3
-- *******.4.1.34592.*******.1.1.3
		-- *******.4.1.34592.*******.1.1.3
		outputModule OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { portEgressShappingEntry 3 }

		
--    *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
		-- *******.4.1.34592.*******.2
		portIngressPolicingTable OBJECT-TYPE
			SYNTAX SEQUENCE OF PortIngressPolicingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuPortQos 2 }

		
--    *******.4.1.34592.*******.2.1
-- *******.4.1.34592.*******.2.1
		-- *******.4.1.34592.*******.2.1
		portIngressPolicingEntry OBJECT-TYPE
			SYNTAX PortIngressPolicingEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { portIngressPolicingTable 1 }

		
		PortIngressPolicingEntry ::=
			SEQUENCE { 
				policingTrafficType
					INTEGER,
				maxTrafficInputRate
					Integer32,
				inputModule
					INTEGER
			 }

--    *******.4.1.34592.*******.2.1.1
-- *******.4.1.34592.*******.2.1.1
		-- *******.4.1.34592.*******.2.1.1
		policingTrafficType OBJECT-TYPE
			SYNTAX INTEGER
				{
				broadCast(1),
				multiCast(2),
				broadcastMulticastAndFloodedUnicast(3),
				allFrameTypes(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { portIngressPolicingEntry 1 }

		
--    *******.4.1.34592.*******.2.1.2
-- *******.4.1.34592.*******.2.1.2
		-- *******.4.1.34592.*******.2.1.2
		maxTrafficInputRate OBJECT-TYPE
			SYNTAX Integer32 (0..1024000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { portIngressPolicingEntry 2 }

		
--    *******.4.1.34592.*******.2.1.3
-- *******.4.1.34592.*******.2.1.3
		-- *******.4.1.34592.*******.2.1.3
		inputModule OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { portIngressPolicingEntry 3 }

		
--    *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		igmpSnooping OBJECT IDENTIFIER ::= { fdOnu 8 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		igmpSnoopParaTable OBJECT-TYPE
			SYNTAX SEQUENCE OF IgmpSnoopParaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { igmpSnooping 1 }

		
--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		igmpSnoopParaEntry OBJECT-TYPE
			SYNTAX IgmpSnoopParaEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId }
			::= { igmpSnoopParaTable 1 }

		
		IgmpSnoopParaEntry ::=
			SEQUENCE { 
				igmpSnoopParaData
					OCTET STRING
			 }

--    *******.4.1.34592.*******.1.1.1
-- *******.4.1.34592.*******.1.1.1
		-- *******.4.1.34592.*******.1.1.1
		igmpSnoopParaData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"				IGMP SNOOPING paras value. format:
				--------------------------------------------------------------
				| robustness count | last member query count | port number |...
				---------------------------------------------------------------
				         1                      <USER>                   <GROUP>     
				------------------------------------------------------------------------
				| groups num 1 | relative queue 1 |...|groups num n | relative queue n |
				------------------------------------------------------------------------
				        1                <USER>                    <GROUP>               1 
				--------------------------------------
				| qualifier bitmatp | option bit map |
				--------------------------------------
				         2                   <USER>
				                  
				<GROUP> explain: 
				para               value range          comment
				robust count:          0~12               
				last member count:     0~12 
				port number:                            for FD104B, port number is 4
				groups number :        0~64             this two paras repeast port number times
				relative queue:
				qualifier bit:         0                reserved   
				option bit map:        0                reserved   
				"
			::= { igmpSnoopParaEntry 1 }

		
--    *******.4.1.34592.*******.2
-- *******.4.1.34592.*******.2
		-- *******.4.1.34592.*******.2
		igmpSnoopGroupTable OBJECT-TYPE
			SYNTAX SEQUENCE OF IgmpSnoopGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { igmpSnooping 2 }

		
--    *******.4.1.34592.*******.2.1
-- *******.4.1.34592.*******.2.1
		-- *******.4.1.34592.*******.2.1
		igmpSnoopGroupEntry OBJECT-TYPE
			SYNTAX IgmpSnoopGroupEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { igmpSnoopGroupTable 1 }

		
		IgmpSnoopGroupEntry ::=
			SEQUENCE { 
				igmpSnoopGroupData
					OCTET STRING
			 }

--    *******.4.1.34592.*******.2.1.1
-- *******.4.1.34592.*******.2.1.1
		-- *******.4.1.34592.*******.2.1.1
		igmpSnoopGroupData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"				igmp group data. this variable is used to add, delete or get igmp groups.
				the data contained in this variable is different for get and add/delete.
				when get, the data format is:
				-------------------------------------------------------------
				| group numbers | group ip address | port bit map |.... repeat group number times
				-------------------------------------------------------------
				       1                 <USER>                 <GROUP>
				
				when add or delete, the data format is:
				-----------------------------------------------------------------
				| operate code | group numbers | group pvid | port bit map | 
				------------------------------------------------------------------
				       1               <USER>                 <GROUP>                4
				
				para comment:
				operate code:    1->add, 2->delete
				"
			::= { igmpSnoopGroupEntry 1 }

		
--    *******.4.1.34592.*******
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		onuLoopTestTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuLoopTestEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { fdOnu 9 }

		
--    *******.4.1.34592.*******.1
-- *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		onuLoopTestEntry OBJECT-TYPE
			SYNTAX OnuLoopTestEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuLoopTestTable 1 }

		
		OnuLoopTestEntry ::=
			SEQUENCE { 
				onuLoopTestData
					OCTET STRING,
				onuLoopTestResult
					OCTET STRING
			 }

--    *******.4.1.34592.*******.1.1
-- *******.4.1.34592.*******.1.1
		-- *******.4.1.34592.*******.1.1
		onuLoopTestData OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"				 test cmd data, format:
				---------------------------------------------------------------
				| location | frame number | frame length | reserved | vlan tag |
				---------------------------------------------------------------  
				     1            <USER>              <GROUP>             1          2 
				para comment:
				               value range         comment
				location:         1,2
				frame number:   1~10000
				frame length:   46~1500
				reserved:          0               this area is not used for now, must be 0
				vlan tag:       0~4094 
				"
			::= { onuLoopTestEntry 1 }

		
--    *******.4.1.34592.*******.1.2
-- *******.4.1.34592.*******.1.2
		-- *******.4.1.34592.*******.1.2
		onuLoopTestResult OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"				 loopback test results.
				Data Format:
				-----------------------------------------------------------------------------------------
				|status|frames sent|frames received ok|corrupted frames|min delay|max delay|average delay|
				-----------------------------------------------------------------------------------------
				   1         <USER>               <GROUP>                   2           2         2            2
				
				para comment:
				status:
				1->finished ok
				2->testing
				3->time out 
				4->test command error
				"
			::= { onuLoopTestEntry 2 }

		
--    *******.4.1.34592.*******0
-- *******.4.1.34592.*******0
		-- *******.4.1.34592.*******0
		onuDynMac OBJECT IDENTIFIER ::= { fdOnu 10 }

		
--    *******.4.1.34592.*******0.1
-- *******.4.1.34592.*******0.1
		-- *******.4.1.34592.*******0.1
		onuDynMacOperTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuDynMacOperEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuDynMac 1 }

		
--    *******.4.1.34592.*******0.1.1
-- *******.4.1.34592.*******0.1.1
		-- *******.4.1.34592.*******0.1.1
		onuDynMacOperEntry OBJECT-TYPE
			SYNTAX OnuDynMacOperEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { onuDynMacOperTable 1 }

		
		OnuDynMacOperEntry ::=
			SEQUENCE { 
				onuDynMacOperation
					INTEGER
			 }

--    *******.4.1.34592.*******0.1.1.1
-- *******.4.1.34592.*******0.1.1.1
		-- *******.4.1.34592.*******0.1.1.1
		onuDynMacOperation OBJECT-TYPE
			SYNTAX INTEGER
				{
				refresh(2),
				clear(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuDynMacOperEntry 1 }

		
--    *******.4.1.34592.*******0.2
-- *******.4.1.34592.*******0.2
		-- *******.4.1.34592.*******0.2
		onuDynMacTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuDynMacEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuDynMac 2 }

		
--    *******.4.1.34592.*******0.2.1
-- *******.4.1.34592.*******0.2.1
		-- *******.4.1.34592.*******0.2.1
		onuDynMacEntry OBJECT-TYPE
			SYNTAX OnuDynMacEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId, onuDynMacIndex
				 }
			::= { onuDynMacTable 1 }

		
		OnuDynMacEntry ::=
			SEQUENCE { 
				onuDynMacIndex
					Integer32,
				onuDynMacAddr
					MacAddress
			 }

--    *******.4.1.34592.*******0.2.1.1
-- *******.4.1.34592.*******0.2.1.1
		-- *******.4.1.34592.*******0.2.1.1
		onuDynMacIndex OBJECT-TYPE
			SYNTAX Integer32 (1..64)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Administratorly assigned entry index object"
			::= { onuDynMacEntry 1 }

		
--    *******.4.1.34592.*******0.2.1.2
-- *******.4.1.34592.*******0.2.1.2
		-- *******.4.1.34592.*******0.2.1.2
		onuDynMacAddr OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuDynMacEntry 2 }

		
--    *******.4.1.34592.*******1
-- *******.4.1.34592.*******1
		-- *******.4.1.34592.*******1
		onuVoiceService OBJECT IDENTIFIER ::= { fdOnu 11 }

		
--    *******.4.1.34592.*******1.1
-- *******.4.1.34592.*******1.1
		-- *******.4.1.34592.*******1.1
		onuIADInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuIADInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" IAD Info"
			::= { onuVoiceService 1 }

		
--    *******.4.1.34592.*******1.1.1
-- *******.4.1.34592.*******1.1.1
		-- *******.4.1.34592.*******1.1.1
		onuIADInfoEntry OBJECT-TYPE
			SYNTAX OnuIADInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" IAD Info"
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuIADInfoTable 1 }

		
		OnuIADInfoEntry ::=
			SEQUENCE { 
				onuIADMac
					MacAddress,
				onuIADProtocol
					INTEGER,
				onuIADSwVersion
					DisplayString,
				onuIADSwTime
					DisplayString,
				onuIADVoipNum
					Integer32
			 }

--    *******.4.1.34592.*******1.1.1.1
-- *******.4.1.34592.*******1.1.1.1
		-- *******.4.1.34592.*******1.1.1.1
		onuIADMac OBJECT-TYPE
			SYNTAX MacAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IAD MAC address"
			::= { onuIADInfoEntry 1 }

		
--    *******.4.1.34592.*******1.1.1.2
-- *******.4.1.34592.*******1.1.1.2
		-- *******.4.1.34592.*******1.1.1.2
		onuIADProtocol OBJECT-TYPE
			SYNTAX INTEGER
				{
				h248(0),
				sip(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ProtocolSupported 
				0x00: H.248
				0x01: SIP"
			::= { onuIADInfoEntry 2 }

		
--    *******.4.1.34592.*******1.1.1.3
-- *******.4.1.34592.*******1.1.1.3
		-- *******.4.1.34592.*******1.1.1.3
		onuIADSwVersion OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Iad Software Version"
			::= { onuIADInfoEntry 3 }

		
--    *******.4.1.34592.*******1.1.1.4
-- *******.4.1.34592.*******1.1.1.4
		-- *******.4.1.34592.*******1.1.1.4
		onuIADSwTime OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Iad software time
				Version time format uses: YYYYMMDDHHMMSS"
			::= { onuIADInfoEntry 4 }

		
--    *******.4.1.34592.*******1.1.1.5
-- *******.4.1.34592.*******1.1.1.5
		-- *******.4.1.34592.*******1.1.1.5
		onuIADVoipNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"VoipUserCount
				Indicate POTS number of IAD module"
			::= { onuIADInfoEntry 5 }

		
--    *******.4.1.34592.*******1.2
-- *******.4.1.34592.*******1.2
		-- *******.4.1.34592.*******1.2
		onuIADParamCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuIADParamCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU IAD  Global Parameter Co nfigure"
			::= { onuVoiceService 2 }

		
--    *******.4.1.34592.*******1.2.1
-- *******.4.1.34592.*******1.2.1
		-- *******.4.1.34592.*******1.2.1
		onuIADParamCfgEntry OBJECT-TYPE
			SYNTAX OnuIADParamCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"ONU IAD  Global Parameter Co nfigure"
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuIADParamCfgTable 1 }

		
		OnuIADParamCfgEntry ::=
			SEQUENCE { 
				onuIADMode
					INTEGER,
				onuIADIpAddr
					IpAddress,
				onuIADNetMask
					IpAddress,
				onuIADDefaultGw
					IpAddress,
				onuIADPppoeMode
					INTEGER,
				onuIADPppoeUsrnm
					DisplayString,
				onuIADPppoePw
					DisplayString,
				onuIADTagMode
					INTEGER,
				onuIADVoiceCVlan
					Integer32,
				onuIADVoiceSVlan
					Integer32,
				onuIADVoicePriority
					Integer32
			 }

--    *******.4.1.34592.*******1.2.1.1
-- *******.4.1.34592.*******1.2.1.1
		-- *******.4.1.34592.*******1.2.1.1
		onuIADMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				static(0),
				dhcp(1),
				pppoe(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IP address configuration mode:
				0x00: Static IP
				0x01: DHCP
				0x02: PPPoE/PPPOE+"
			::= { onuIADParamCfgEntry 1 }

		
--    *******.4.1.34592.*******1.2.1.2
-- *******.4.1.34592.*******1.2.1.2
		-- *******.4.1.34592.*******1.2.1.2
		onuIADIpAddr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"When Voice IP Mode= 0x00, indicate the device 
				static configured IP address, otherwise, this field 
				is invalid, and the value is 0x00."
			::= { onuIADParamCfgEntry 2 }

		
--    *******.4.1.34592.*******1.2.1.3
-- *******.4.1.34592.*******1.2.1.3
		-- *******.4.1.34592.*******1.2.1.3
		onuIADNetMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"When Voice IP Mode = 0x00, indicate the device 
				static configured IP address mask, otherwise, 
				this field is invalid, and the value is 0x00."
			::= { onuIADParamCfgEntry 3 }

		
--    *******.4.1.34592.*******1.2.1.4
-- *******.4.1.34592.*******1.2.1.4
		-- *******.4.1.34592.*******1.2.1.4
		onuIADDefaultGw OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"When Voice IP Mode = 0x00, indicate the device 
				static configured IAD default GW, otherwise, the 
				field is invalid, and the value is 0x00"
			::= { onuIADParamCfgEntry 4 }

		
--    *******.4.1.34592.*******1.2.1.5
-- *******.4.1.34592.*******1.2.1.5
		-- *******.4.1.34592.*******1.2.1.5
		onuIADPppoeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				auto(0),
				chap(1),
				pap(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PPPOE mode:
				0x00: AUTO (default)
				0x01: Challenge Handshake Authentication Protocol (CHAP)
				0x02: Password Authentication Protocol (PAP)"
			::= { onuIADParamCfgEntry 5 }

		
--    *******.4.1.34592.*******1.2.1.6
-- *******.4.1.34592.*******1.2.1.6
		-- *******.4.1.34592.*******1.2.1.6
		onuIADPppoeUsrnm OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PPPOE username"
			::= { onuIADParamCfgEntry 6 }

		
--    *******.4.1.34592.*******1.2.1.7
-- *******.4.1.34592.*******1.2.1.7
		-- *******.4.1.34592.*******1.2.1.7
		onuIADPppoePw OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"PPPOE password"
			::= { onuIADParamCfgEntry 7 }

		
--    *******.4.1.34592.*******1.2.1.8
-- *******.4.1.34592.*******1.2.1.8
		-- *******.4.1.34592.*******1.2.1.8
		onuIADTagMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				passThrough(0),
				tag(1),
				vlanStack(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Tagged flag 
				Indicate whether Voice data uses tag, and the default is 0x01:
				0x00: Pass through
				0x01: Tag
				0x02: VLAN stacking"
			::= { onuIADParamCfgEntry 8 }

		
--    *******.4.1.34592.*******1.2.1.9
-- *******.4.1.34592.*******1.2.1.9
		-- *******.4.1.34592.*******1.2.1.9
		onuIADVoiceCVlan OBJECT-TYPE
			SYNTAX Integer32 (0..4096)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Voice CVlan"
			::= { onuIADParamCfgEntry 9 }

		
--    *******.4.1.34592.*******1.2.1.10
-- *******.4.1.34592.*******1.2.1.10
		-- *******.4.1.34592.*******1.2.1.10
		onuIADVoiceSVlan OBJECT-TYPE
			SYNTAX Integer32 (0..4096)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Voice SVLAN
				If it is single layer of VLAN, the value is 0x00."
			::= { onuIADParamCfgEntry 10 }

		
--    *******.4.1.34592.*******1.2.1.11
-- *******.4.1.34592.*******1.2.1.11
		-- *******.4.1.34592.*******1.2.1.11
		onuIADVoicePriority OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Voice Priority"
			::= { onuIADParamCfgEntry 11 }

		
--    *******.4.1.34592.*******1.3
-- *******.4.1.34592.*******1.3
		-- *******.4.1.34592.*******1.3
		onuIADH248Param OBJECT IDENTIFIER ::= { onuVoiceService 3 }

		
--    *******.4.1.34592.*******1.3.1
-- *******.4.1.34592.*******1.3.1
		-- *******.4.1.34592.*******1.3.1
		h248ParamCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF H248ParamCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuIADH248Param 1 }

		
--    *******.4.1.34592.*******1.3.1.1
-- *******.4.1.34592.*******1.3.1.1
		-- *******.4.1.34592.*******1.3.1.1
		h248ParamCfgEntry OBJECT-TYPE
			SYNTAX H248ParamCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId }
			::= { h248ParamCfgTable 1 }

		
		H248ParamCfgEntry ::=
			SEQUENCE { 
				h248MgPort
					Integer32,
				h248MgcIp
					IpAddress,
				h248MgcPort
					Integer32,
				h248BakMacIp
					IpAddress,
				h248BakMgcPort
					Integer32,
				h248ActiveMgc
					INTEGER,
				h248RegMode
					INTEGER,
				h248MID
					DisplayString,
				h248HbMode
					INTEGER,
				h248HbCycle
					Integer32,
				h248HbCount
					Integer32
			 }

--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248MgPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"MGPortNo
				The default is 2944."
			::= { h248ParamCfgEntry 1 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248MgcIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"MGCIP 
				Active softswitch platform IP address.
				"
			::= { h248ParamCfgEntry 2 }

		
--    *******.4.1.34592.*******1.3.1.1.3
-- *******.4.1.34592.*******1.3.1.1.3
		-- *******.4.1.34592.*******1.3.1.1.3
		h248MgcPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"MgcComPortNo 
				Active softswitch platform port number."
			::= { h248ParamCfgEntry 3 }

		
--    *******.4.1.34592.*******1.3.1.1.4
-- *******.4.1.34592.*******1.3.1.1.4
		-- *******.4.1.34592.*******1.3.1.1.4
		h248BakMacIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Backup MgcIp 
				Backup softswitch platform IP address. If it is 
				0x00000000, dual homing is not enabled."
			::= { h248ParamCfgEntry 4 }

		
--    *******.4.1.34592.*******1.3.1.1.5
-- *******.4.1.34592.*******1.3.1.1.5
		-- *******.4.1.34592.*******1.3.1.1.5
		h248BakMgcPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Backup MgcComPortNo 
				If it is 0x0000, dual homing is not enabled."
			::= { h248ParamCfgEntry 5 }

		
--    *******.4.1.34592.*******1.3.1.1.6
-- *******.4.1.34592.*******1.3.1.1.6
		-- *******.4.1.34592.*******1.3.1.1.6
		h248ActiveMgc OBJECT-TYPE
			SYNTAX INTEGER
				{
				backup(0),
				active(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Active MGC 
				0x00: Backup softswitch platform
				0x01: Active softswitch platform
				When SET, ONU ignores this field.
				When GET, ONU returns registered MGC."
			::= { h248ParamCfgEntry 6 }

		
--    *******.4.1.34592.*******1.3.1.1.7
-- *******.4.1.34592.*******1.3.1.1.7
		-- *******.4.1.34592.*******1.3.1.1.7
		h248RegMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipAddress(0),
				domainName(1),
				deviceName(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"RegMode 
				0x00: IP address
				0x01: domain name
				0x02: device name"
			::= { h248ParamCfgEntry 7 }

		
--    *******.4.1.34592.*******1.3.1.1.8
-- *******.4.1.34592.*******1.3.1.1.8
		-- *******.4.1.34592.*******1.3.1.1.8
		h248MID OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..64))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"MID 
				If RegMode=0x00, the field is empty.
				If the registration mode is device name or 
				domain name, the corresponding strings are 
				padded."
			::= { h248ParamCfgEntry 8 }

		
--    *******.4.1.34592.*******1.3.1.1.9
-- *******.4.1.34592.*******1.3.1.1.9
		-- *******.4.1.34592.*******1.3.1.1.9
		h248HbMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				close(0),
				h248ctc(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Heartbeat Mode 
				0x00: Close
				0x01: H.248-CTC standard Notify command"
			::= { h248ParamCfgEntry 9 }

		
--    *******.4.1.34592.*******1.*******0
-- *******.4.1.34592.*******1.*******0
		-- *******.4.1.34592.*******1.*******0
		h248HbCycle OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"HeartbeatCycle 
				The default is 60s."
			::= { h248ParamCfgEntry 10 }

		
--    *******.4.1.34592.*******1.*******1
-- *******.4.1.34592.*******1.*******1
		-- *******.4.1.34592.*******1.*******1
		h248HbCount OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"HeartbeatCount 
				The default is 3."
			::= { h248ParamCfgEntry 11 }

		
--    *******.4.1.34592.*******1.3.2
-- *******.4.1.34592.*******1.3.2
		-- *******.4.1.34592.*******1.3.2
		h248UserTIDTable OBJECT-TYPE
			SYNTAX SEQUENCE OF H248UserTIDEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuIADH248Param 2 }

		
--    *******.4.1.34592.*******1.3.2.1
-- *******.4.1.34592.*******1.3.2.1
		-- *******.4.1.34592.*******1.3.2.1
		h248UserTIDEntry OBJECT-TYPE
			SYNTAX H248UserTIDEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId, onuIADPotsId }
			::= { h248UserTIDTable 1 }

		
		H248UserTIDEntry ::=
			SEQUENCE { 
				onuVoipPortId
					Integer32,
				h248UserTIDName
					DisplayString
			 }

--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		onuVoipPortId OBJECT-TYPE
			SYNTAX Integer32 (1..2048)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { h248UserTIDEntry 1 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248UserTIDName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"User TID Name"
			::= { h248UserTIDEntry 2 }

		
--    *******.4.1.34592.*******1.3.3
-- *******.4.1.34592.*******1.3.3
		-- *******.4.1.34592.*******1.3.3
		h248RtpTIDCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF H248RtpTIDCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuIADH248Param 3 }

		
--    *******.4.1.34592.*******1.3.3.1
-- *******.4.1.34592.*******1.3.3.1
		-- *******.4.1.34592.*******1.3.3.1
		h248RtpTIDCfgEntry OBJECT-TYPE
			SYNTAX H248RtpTIDCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId }
			::= { h248RtpTIDCfgTable 1 }

		
		H248RtpTIDCfgEntry ::=
			SEQUENCE { 
				h248RtpTIDNum
					Integer32,
				h248RtpTIDPrefix
					DisplayString,
				h248RtpTIDDigitBegin
					Integer32,
				h248RtpTIDMode
					INTEGER,
				h248RtpTIDDigitLen
					Integer32
			 }

--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248RtpTIDNum OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Number of RTP TID"
			::= { h248RtpTIDCfgEntry 1 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248RtpTIDPrefix OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"RTP TID Prefix"
			::= { h248RtpTIDCfgEntry 2 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248RtpTIDDigitBegin OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"RTP TID Digit Begin
				RTP TID digital portion start value"
			::= { h248RtpTIDCfgEntry 3 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248RtpTIDMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				align(0),
				nonAlign(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"RTP TID Mode 
				0x00: alignment mode
				0x01: non-alignment mode"
			::= { h248RtpTIDCfgEntry 4 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		h248RtpTIDDigitLen OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"RTP TID Digit Length"
			::= { h248RtpTIDCfgEntry 5 }

		
--    *******.4.1.34592.*******1.3.4
-- *******.4.1.34592.*******1.3.4
		-- *******.4.1.34592.*******1.3.4
		h248RtpTIDInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF H248RtpTIDInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" H248 RTP TID Info"
			::= { onuIADH248Param 4 }

		
--    *******.4.1.34592.**************
-- *******.4.1.34592.**************
		-- *******.4.1.34592.**************
		h248RtpTIDInfoEntry OBJECT-TYPE
			SYNTAX H248RtpTIDInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" H248 RTP TID Info"
			INDEX { ponCardSlotId, oltId, onuId }
			::= { h248RtpTIDInfoTable 1 }

		
		H248RtpTIDInfoEntry ::=
			SEQUENCE { 
				h248RtpTIDCount
					Integer32,
				h248FstRtpTIDName
					DisplayString
			 }

--    *******.4.1.34592.**************.1
-- *******.4.1.34592.**************.1
		-- *******.4.1.34592.**************.1
		h248RtpTIDCount OBJECT-TYPE
			SYNTAX Integer32 (0..255)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of RTP TID"
			::= { h248RtpTIDInfoEntry 1 }

		
--    *******.4.1.34592.**************.2
-- *******.4.1.34592.**************.2
		-- *******.4.1.34592.**************.2
		h248FstRtpTIDName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"First RTP TID Name"
			::= { h248RtpTIDInfoEntry 2 }

		
--    *******.4.1.34592.*******1.4
-- *******.4.1.34592.*******1.4
		-- *******.4.1.34592.*******1.4
		onuIADSipParam OBJECT IDENTIFIER ::= { onuVoiceService 4 }

		
--          *******.4.1.34592.*******1.4.1
-- *******.4.1.34592.*******1.4.1
-- *******.4.1.34592.*******1.4.1
		-- *******.4.1.34592.*******1.4.1
		sipParamCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SipParamCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" SIP Parameter Configure"
			::= { onuIADSipParam 1 }

		
--    *******.4.1.34592.*******1.4.1.1
-- *******.4.1.34592.*******1.4.1.1
		-- *******.4.1.34592.*******1.4.1.1
		sipParamCfgEntry OBJECT-TYPE
			SYNTAX SipParamCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" SIP Parameter Configure"
			INDEX { ponCardSlotId, oltId, onuId }
			::= { sipParamCfgTable 1 }

		
		SipParamCfgEntry ::=
			SEQUENCE { 
				sipMgPort
					Integer32,
				sipProxySvrIp
					IpAddress,
				sipProxySvrPort
					Integer32,
				sipBakProxySvrIp
					IpAddress,
				sipBakProxySvrPort
					Integer32,
				sipActiveProxySvr
					IpAddress,
				sipRegSvrIp
					IpAddress,
				sipRegSvrPort
					Integer32,
				sipBakRegSvrIp
					IpAddress,
				sipBakRegSvrPort
					Integer32,
				sipOutBoundSvrIp
					IpAddress,
				sipOutBoundSvrPort
					Integer32,
				sipRegInterval
					Integer32,
				sipHbSwitch
					INTEGER,
				sipHbCycle
					Integer32,
				sipHbCount
					Integer32
			 }

--    *******.4.1.34592.*******1.4.1.1.1
-- *******.4.1.34592.*******1.4.1.1.1
		-- *******.4.1.34592.*******1.4.1.1.1
		sipMgPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"MGPortNo 
				The default is 5060."
			::= { sipParamCfgEntry 1 }

		
--    *******.4.1.34592.*******1.4.1.1.2
-- *******.4.1.34592.*******1.4.1.1.2
		-- *******.4.1.34592.*******1.4.1.1.2
		sipProxySvrIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SipProxyServIp 
				Active SIP agent server IP address"
			::= { sipParamCfgEntry 2 }

		
--    *******.4.1.34592.*******1.4.1.1.3
-- *******.4.1.34592.*******1.4.1.1.3
		-- *******.4.1.34592.*******1.4.1.1.3
		sipProxySvrPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SipProxyServComPortNo 
				Active SIP agent server port number"
			::= { sipParamCfgEntry 3 }

		
--    *******.4.1.34592.*******1.4.1.1.4
-- *******.4.1.34592.*******1.4.1.1.4
		-- *******.4.1.34592.*******1.4.1.1.4
		sipBakProxySvrIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BackupSipProxyServIp 
				Backup SIP agent server IP address, if this field is 
				0x00000000, do not enable dual homing function."
			::= { sipParamCfgEntry 4 }

		
--    *******.4.1.34592.*******1.4.1.1.5
-- *******.4.1.34592.*******1.4.1.1.5
		-- *******.4.1.34592.*******1.4.1.1.5
		sipBakProxySvrPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BackupSipProxyServComPortNo 
				Backup SIP agent server port number. If this field 
				is 0x00000000, do not enable dual homing function."
			::= { sipParamCfgEntry 5 }

		
--    *******.4.1.34592.*******1.4.1.1.6
-- *******.4.1.34592.*******1.4.1.1.6
		-- *******.4.1.34592.*******1.4.1.1.6
		sipActiveProxySvr OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"ActiveSipProxyServer 
				When SET, ONU ignores to process this field.
				When GET, ONU returns registered MGC."
			::= { sipParamCfgEntry 6 }

		
--    *******.4.1.34592.*******1.4.1.1.7
-- *******.4.1.34592.*******1.4.1.1.7
		-- *******.4.1.34592.*******1.4.1.1.7
		sipRegSvrIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SipRegServIP 
				Active SIP registration server IP address"
			::= { sipParamCfgEntry 7 }

		
--    *******.4.1.34592.*******1.4.1.1.8
-- *******.4.1.34592.*******1.4.1.1.8
		-- *******.4.1.34592.*******1.4.1.1.8
		sipRegSvrPort OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SipRegServComPortNo 
				Active SIP registration server port number"
			::= { sipParamCfgEntry 8 }

		
--    *******.4.1.34592.*******1.4.1.1.9
-- *******.4.1.34592.*******1.4.1.1.9
		-- *******.4.1.34592.*******1.4.1.1.9
		sipBakRegSvrIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BackupSipRegServIp 
				Backup SIP registration server IP address. If this 
				field is 0x00000000, do not enable dual homing."
			::= { sipParamCfgEntry 9 }

		
--    *******.4.1.34592.*******1.4.1.1.10
-- *******.4.1.34592.*******1.4.1.1.10
		-- *******.4.1.34592.*******1.4.1.1.10
		sipBakRegSvrPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"BackupSipRegServComPortNo 
				Active SIP registration server port number. If this 
				field is 0x00000000, do not enable dual homing."
			::= { sipParamCfgEntry 10 }

		
--    *******.4.1.34592.*******1.4.1.1.11
-- *******.4.1.34592.*******1.4.1.1.11
		-- *******.4.1.34592.*******1.4.1.1.11
		sipOutBoundSvrIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"OutBoundServIP"
			::= { sipParamCfgEntry 11 }

		
--    *******.4.1.34592.*******1.********
-- *******.4.1.34592.*******1.********
		-- *******.4.1.34592.*******1.********
		sipOutBoundSvrPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"OutBoundServPortNo"
			::= { sipParamCfgEntry 12 }

		
--    *******.4.1.34592.*******1.********
-- *******.4.1.34592.*******1.********
		-- *******.4.1.34592.*******1.********
		sipRegInterval OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SipRegIntervalSIP 
				Registration refresh cycle, unit is second, and the 
				default value is 3600s."
			::= { sipParamCfgEntry 13 }

		
--    *******.4.1.34592.*******1.********
-- *******.4.1.34592.*******1.********
		-- *******.4.1.34592.*******1.********
		sipHbSwitch OBJECT-TYPE
			SYNTAX INTEGER
				{
				enable(0),
				disable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"HeartbeatSwitch 
				0x00: enable heartbeat switch
				0x01: disable heartbeat switch"
			::= { sipParamCfgEntry 14 }

		
--    *******.4.1.34592.*******1.********
-- *******.4.1.34592.*******1.********
		-- *******.4.1.34592.*******1.********
		sipHbCycle OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"HeartbeatCycle 
				the default value is 60s"
			::= { sipParamCfgEntry 15 }

		
--    *******.4.1.34592.*******1.********
-- *******.4.1.34592.*******1.********
		-- *******.4.1.34592.*******1.********
		sipHbCount OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"HeartbeatCount 
				the default value is 3"
			::= { sipParamCfgEntry 16 }

		
--    *******.4.1.34592.*******1.4.2
-- *******.4.1.34592.*******1.4.2
		-- *******.4.1.34592.*******1.4.2
		sipUserCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SipUserCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" SIP User Parameter Configure"
			::= { onuIADSipParam 2 }

		
--    *******.4.1.34592.*******1.4.2.1
-- *******.4.1.34592.*******1.4.2.1
		-- *******.4.1.34592.*******1.4.2.1
		sipUserCfgEntry OBJECT-TYPE
			SYNTAX SipUserCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" SIP User Parameter Configure"
			INDEX { ponCardSlotId, oltId, onuId, onuIADPotsId }
			::= { sipUserCfgTable 1 }

		
		SipUserCfgEntry ::=
			SEQUENCE { 
				sipUserAccount
					DisplayString,
				sipUserName
					DisplayString,
				sipUserPasswd
					DisplayString
			 }

--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		sipUserAccount OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"User Account 
				User phone number, and should user ASCII code"
			::= { sipUserCfgEntry 1 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		sipUserName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..32))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"User name 
				SIP port username, and should use ASCII code."
			::= { sipUserCfgEntry 2 }

		
--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		sipUserPasswd OBJECT-TYPE
			SYNTAX DisplayString (SIZE (0..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"User Password 
				SIP port password, and should use ASCII code."
			::= { sipUserCfgEntry 3 }

		
--    *******.4.1.34592.*******1.4.3
-- *******.4.1.34592.*******1.4.3
		-- *******.4.1.34592.*******1.4.3
		sipDigitMapTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SipDigitMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuIADSipParam 3 }

		
--    *******.4.1.34592.*******1.4.3.1
-- *******.4.1.34592.*******1.4.3.1
		-- *******.4.1.34592.*******1.4.3.1
		sipDigitMapEntry OBJECT-TYPE
			SYNTAX SipDigitMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId }
			::= { sipDigitMapTable 1 }

		
		SipDigitMapEntry ::=
			SEQUENCE { 
				sipDigitMapLen
					Integer32,
				sipDigitMap
					DisplayString
			 }

--    *******.4.1.34592.*******1.*******
-- *******.4.1.34592.*******1.*******
		-- *******.4.1.34592.*******1.*******
		sipDigitMapLen OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Map length"
			::= { sipDigitMapEntry 1 }

		
--    *******.4.1.34592.*******1.4.3.1.2
-- *******.4.1.34592.*******1.4.3.1.2
		-- *******.4.1.34592.*******1.4.3.1.2
		sipDigitMap OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"SIP DigitalMap 
				SIP protocol digital map, ASCII code type"
			::= { sipDigitMapEntry 2 }

		
--    *******.4.1.34592.*******1.5
-- *******.4.1.34592.*******1.5
		-- *******.4.1.34592.*******1.5
		onuIADFaxCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuIADFaxCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" Fax/Modem Configure"
			::= { onuVoiceService 5 }

		
--    *******.4.1.34592.*******1.5.1
-- *******.4.1.34592.*******1.5.1
		-- *******.4.1.34592.*******1.5.1
		onuIADFaxCfgEntry OBJECT-TYPE
			SYNTAX OnuIADFaxCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuIADFaxCfgTable 1 }

		
		OnuIADFaxCfgEntry ::=
			SEQUENCE { 
				onuIADVoiceFaxMode
					INTEGER,
				onuIADVoiceFaxControl
					INTEGER
			 }

--    *******.4.1.34592.*******1.5.1.1
-- *******.4.1.34592.*******1.5.1.1
		-- *******.4.1.34592.*******1.5.1.1
		onuIADVoiceFaxMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				passthrough(0),
				t38(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VoiceT38Enable 
				0x00: voice passthrough mode (T30)
				0x01: T38 mode"
			::= { onuIADFaxCfgEntry 1 }

		
--    *******.4.1.34592.*******1.5.1.2
-- *******.4.1.34592.*******1.5.1.2
		-- *******.4.1.34592.*******1.5.1.2
		onuIADVoiceFaxControl OBJECT-TYPE
			SYNTAX INTEGER
				{
				negotiation(0),
				autoVBD(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"VoiceFax/ModemControl 
				At default, use negotiation mode.
				0x00: negotiation mode
				0x01: Auto VBD"
			::= { onuIADFaxCfgEntry 2 }

		
--    *******.4.1.34592.*******1.6
-- *******.4.1.34592.*******1.6
		-- *******.4.1.34592.*******1.6
		onuIADOperTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuIADOperEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" IAD Operation Status "
			::= { onuVoiceService 6 }

		
--    *******.4.1.34592.*******1.6.1
-- *******.4.1.34592.*******1.6.1
		-- *******.4.1.34592.*******1.6.1
		onuIADOperEntry OBJECT-TYPE
			SYNTAX OnuIADOperEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" IAD Operation Status "
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuIADOperTable 1 }

		
		OnuIADOperEntry ::=
			SEQUENCE { 
				onuIADOperStatusSet
					INTEGER,
				onuIADOperStatus
					INTEGER
			 }

--    *******.4.1.34592.*******1.6.1.1
-- *******.4.1.34592.*******1.6.1.1
		-- *******.4.1.34592.*******1.6.1.1
		onuIADOperStatusSet OBJECT-TYPE
			SYNTAX INTEGER
				{
				reregister(0),
				logout(1),
				reset(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"iadOperation 
				0x00000000: Reregister for softswitch platform
				0x00000001: Logout from softswitch platform
				0x00000002: Reset, only for voice module"
			::= { onuIADOperEntry 1 }

		
--    *******.4.1.34592.*******1.6.1.2
-- *******.4.1.34592.*******1.6.1.2
		-- *******.4.1.34592.*******1.6.1.2
		onuIADOperStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				registering(0),
				regSuccess(1),
				iadFault(2),
				logout(3),
				iadRestarting(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"iadOperStatus 
				0x00000000: Registering
				0x00000001: Registration successful
				0x00000002: IAD fault
				0x00000003: logout
				0x00000004: IAD is restarting"
			::= { onuIADOperEntry 2 }

		
--    *******.4.1.34592.*******1.7
-- *******.4.1.34592.*******1.7
		-- *******.4.1.34592.*******1.7
		onuIADPOTSStatusTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuIADPOTSStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"POTS Status"
			::= { onuVoiceService 7 }

		
--    *******.4.1.34592.*******1.7.1
-- *******.4.1.34592.*******1.7.1
		-- *******.4.1.34592.*******1.7.1
		onuIADPOTSStatusEntry OBJECT-TYPE
			SYNTAX OnuIADPOTSStatusEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"POTS Status"
			INDEX { ponCardSlotId, oltId, onuId, onuIADPotsId }
			::= { onuIADPOTSStatusTable 1 }

		
		OnuIADPOTSStatusEntry ::=
			SEQUENCE { 
				onuIADPotsStatus
					INTEGER,
				onuIADPotsServiceState
					INTEGER,
				onuIADPotsCodeMode
					INTEGER,
				onuIADPotsId
					INTEGER
			 }

--    *******.4.1.34592.*******1.7.1.1
-- *******.4.1.34592.*******1.7.1.1
		-- *******.4.1.34592.*******1.7.1.1
		onuIADPotsStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				registering(0),
				idle(1),
				pickUp(2),
				dialing(3),
				ringing(4),
				ringBack(5),
				connecting(6),
				connected(7),
				releasing(8),
				regFailure(9),
				notActivated(10)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"IADPortStauts 
				0x00000000: port is registering
				0x00000001: port is idle
				0x00000002: pick up
				0x00000003: dialing
				0x00000004: ringing
				0x00000005: ring back
				0x00000006: connecting
				0x00000007: connected
				0x00000008: releasing connection
				0x00000009: port registration failure
				0x0000000A: port is not activated"
			::= { onuIADPOTSStatusEntry 1 }

		
--    *******.4.1.34592.*******1.7.1.2
-- *******.4.1.34592.*******1.7.1.2
		-- *******.4.1.34592.*******1.7.1.2
		onuIADPotsServiceState OBJECT-TYPE
			SYNTAX INTEGER
				{
				endLocal(0),
				endRemote(1),
				endAuto(2),
				normal(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"iadPortServiceState 
				0x00000000: endLocal, local end terminates service, caused by user disable port
				0x00000001: endRemote, remote end terminates service, caused by MGC sends down command
				0x00000002: endAuto, automatically terminate service, caused by MGC fault.
				0x00000003: normal service normal"
			::= { onuIADPOTSStatusEntry 2 }

		
--    *******.4.1.34592.*******1.7.1.3
-- *******.4.1.34592.*******1.7.1.3
		-- *******.4.1.34592.*******1.7.1.3
		onuIADPotsCodeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				g711a(0),
				g729(1),
				g711u(2),
				g723(3),
				g726(4),
				t38(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"iadPortCodecMode 0x00000000: G.711 A
				0x00000001: G.729
				0x00000002: G.711U
				0x00000003: G.723
				0x00000004: G.726
				0x00000005: T.38"
			::= { onuIADPOTSStatusEntry 3 }

		
--    *******.4.1.34592.*******1.7.1.4
-- *******.4.1.34592.*******1.7.1.4
		-- *******.4.1.34592.*******1.7.1.4
		onuIADPotsId OBJECT-TYPE
			SYNTAX INTEGER
				{
				iadPots1(1),
				iadPots2(2),
				iadPots3(3),
				iadPots4(4),
				iadPots5(5),
				iadPots6(6),
				iadPots7(7),
				iadPots8(8)
				}
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuIADPOTSStatusEntry 4 }

		
--    *******.4.1.34592.*******1.8
-- *******.4.1.34592.*******1.8
		-- *******.4.1.34592.*******1.8
		onuIADPOTSEnableTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuIADPOTSEnableEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"POTS Enable"
			::= { onuVoiceService 8 }

		
--    *******.4.1.34592.*******1.8.1
-- *******.4.1.34592.*******1.8.1
		-- *******.4.1.34592.*******1.8.1
		onuIADPOTSEnableEntry OBJECT-TYPE
			SYNTAX OnuIADPOTSEnableEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"POTS Enable"
			INDEX { ponCardSlotId, oltId, onuId, onuIADPotsId }
			::= { onuIADPOTSEnableTable 1 }

		
		OnuIADPOTSEnableEntry ::=
			SEQUENCE { 
				potsId
					INTEGER,
				onuIADPotsEnable
					INTEGER
			 }

--    *******.4.1.34592.*******1.8.1.1
-- *******.4.1.34592.*******1.8.1.1
		-- *******.4.1.34592.*******1.8.1.1
		potsId OBJECT-TYPE
			SYNTAX INTEGER
				{
				iadPots1(1),
				iadPots2(2),
				iadPots3(3),
				iadPots4(4),
				iadPots5(5),
				iadPots6(6),
				iadPots7(7),
				iadPots8(8)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Pots Id
				"
			::= { onuIADPOTSEnableEntry 1 }

		
--    *******.4.1.34592.*******1.8.1.2
-- *******.4.1.34592.*******1.8.1.2
		-- *******.4.1.34592.*******1.8.1.2
		onuIADPotsEnable OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(1),
				enable(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"IADPortEnable
				1: disable
				2: enable
				"
			::= { onuIADPOTSEnableEntry 2 }

		
		-- *******.4.1.34592.*******2
		onuStatisticsTable OBJECT IDENTIFIER ::= { fdOnu 12 }

		
		-- *******.4.1.34592.*******2.1
		onuPonStatisticsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuPonStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStatisticsTable 1 }

		
		-- *******.4.1.34592.*******2.1.1
		onuPonStatisticsEntry OBJECT-TYPE
			SYNTAX OnuPonStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId }
			::= { onuPonStatisticsTable 1 }

		
		OnuPonStatisticsEntry ::=
			SEQUENCE { 
				onuPonStatDnDropEvents
					Counter32,
				onuPonStatUpDropEvents
					Counter32,
				onuPonStatDnOctets
					Counter32,
				onuPonStatUpOctets
					Counter32,
				onuPonStatDnFrames
					Counter32,
				onuPonStatUpFrames
					Counter32,
				onuPonStatDnBcFrames
					Counter32,
				onuPonStatUpBcFrames
					Counter32,
				onuPonStatDnMcFrames
					Counter32,
				onuPonStatUpMcFrames
					Counter32,
				onuPonStatDnCrcErrFrames
					Counter32,
				onuPonStatUpCrcErrFrames
					Counter32
			 }

		-- *******.4.1.34592.*******2.1.1.1
		onuPonStatDnDropEvents OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 1 }

		
		-- *******.4.1.34592.*******2.1.1.2
		onuPonStatUpDropEvents OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 2 }

		
		-- *******.4.1.34592.*******2.1.1.3
		onuPonStatDnOctets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 3 }

		
		-- *******.4.1.34592.*******2.1.1.4
		onuPonStatUpOctets OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 4 }

		
		-- *******.4.1.34592.*******2.1.1.5
		onuPonStatDnFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 5 }

		
		-- *******.4.1.34592.*******2.1.1.6
		onuPonStatUpFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 6 }

		
		-- *******.4.1.34592.*******2.1.1.7
		onuPonStatDnBcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 7 }

		
		-- *******.4.1.34592.*******2.1.1.8
		onuPonStatUpBcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 8 }

		
		-- *******.4.1.34592.*******2.1.1.9
		onuPonStatDnMcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 9 }

		
		-- *******.4.1.34592.*******2.1.1.10
		onuPonStatUpMcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 10 }

		
		-- *******.4.1.34592.*******2.1.1.11
		onuPonStatDnCrcErrFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 11 }

		
		-- *******.4.1.34592.*******2.1.1.12
		onuPonStatUpCrcErrFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuPonStatisticsEntry 12 }

		
		-- *******.4.1.34592.*******2.2
		onuUniStatisticsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuStatisticsTable 2 }

		
		-- *******.4.1.34592.*******2.2.1
		onuUniStatisticsEntry OBJECT-TYPE
			SYNTAX OnuUniStatisticsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId, uniPortId }
			::= { onuUniStatisticsTable 1 }

		
		OnuUniStatisticsEntry ::=
			SEQUENCE { 
				onuUniStatDnDropEvents
					Counter32,
				onuUniStatUpDropEvents
					Counter32,
				onuUniStatDnOctects
					Counter32,
				onuUniStatUpOctects
					Counter32,
				onuUniStatDnFrames
					Counter32,
				onuUniStatUpFrames
					Counter32,
				onuUniStatDnBcFrames
					Counter32,
				onuUniStatUpBcFrames
					Counter32,
				onuUniStatDnMcFrames
					Counter32,
				onuUniStatUpMcFrames
					Counter32,
				onuUniStatDnCrcErrFrames
					Counter32,
				onuUniStatUpCrcErrFrames
					Counter32
			 }

		-- *******.4.1.34592.*******2.2.1.1
		onuUniStatDnDropEvents OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 1 }

		
		-- *******.4.1.34592.*******2.2.1.2
		onuUniStatUpDropEvents OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 2 }

		
		-- *******.4.1.34592.*******2.2.1.3
		onuUniStatDnOctects OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 3 }

		
		-- *******.4.1.34592.*******2.2.1.4
		onuUniStatUpOctects OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 4 }

		
		-- *******.4.1.34592.*******2.2.1.5
		onuUniStatDnFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 5 }

		
		-- *******.4.1.34592.*******2.2.1.6
		onuUniStatUpFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 6 }

		
		-- *******.4.1.34592.*******2.2.1.7
		onuUniStatDnBcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 7 }

		
		-- *******.4.1.34592.*******2.2.1.8
		onuUniStatUpBcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 8 }

		
		-- *******.4.1.34592.*******2.2.1.9
		onuUniStatDnMcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 9 }

		
		-- *******.4.1.34592.*******2.2.1.10
		onuUniStatUpMcFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 10 }

		
		-- *******.4.1.34592.*******2.2.1.11
		onuUniStatDnCrcErrFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 11 }

		
		-- *******.4.1.34592.*******2.2.1.12
		onuUniStatUpCrcErrFrames OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniStatisticsEntry 12 }

		
--    *******.4.1.34592.*******5
-- *******.4.1.34592.*******5
		-- *******.4.1.34592.*******5
		fdOnuConformance OBJECT IDENTIFIER ::= { fdOnu 15 }

		
--    *******.4.1.34592.*******5.1
-- *******.4.1.34592.*******5.1
		-- *******.4.1.34592.*******5.1
		fdOnuGroups OBJECT IDENTIFIER ::= { fdOnuConformance 1 }

		
--    *******.4.1.34592.*******5.1.1
-- *******.4.1.34592.*******5.1.1
		-- *******.4.1.34592.*******5.1.1
		fdOnuBaseManageGroup OBJECT-GROUP
			OBJECTS { onuDeviceType, onuFactorySerial, onuUserInfo, onuHwRev, onuFwRev, 
				onuBaseMac, maxAllowedLLIDs, registeredLLIDNum, onuOnLineStatus, onuUserTrafficEnable, 
				onuRangeValue, onuMgmtType, onuLaserRxPower, onuLaserTxPower, onuOperation, 
				onuRstpEnable, onuQueueCfgData, onuAclRuleCfgData, onuPortVlanData, maxTrafficOutputRate, 
				outputModule, scheduleAlgorithm, policingTrafficType, maxTrafficInputRate, inputModule, 
				onuDynMacOperation, supportUniPorts, onuLinkIdMap, onuVenderId, onuLaserTemperature, 
				onuLaserBias, onuLaserVoltage, onuPasswd, onuLoid, onuCtcDeviceType, 
				onuDynMacAddr, onuUpgradeStat }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing basic fd ONU
				management"
			::= { fdOnuGroups 1 }

		
--    *******.4.1.34592.*******5.1.2
-- *******.4.1.34592.*******5.1.2
		-- *******.4.1.34592.*******5.1.2
		fdOnuAdvanceManageGroup OBJECT-GROUP
			OBJECTS { onuChipProCode, onuChipProVer, onuChipId, onuChipVer, onuBootVer, 
				onuPersVer, onuChipApp0Ver, onuChipApp1Ver, onuChipDiagVer, onuAddiVlanEthType, 
				onuRstpEnable, onuLocalSwitch, onuCatv, onuCatvRfLevel, onuStormCtrlInterval, 
				onuUniStormCtrlBroadcastEnable, onuUniStormCtrlBroadcastMode, onuUniStormCtrlBroadcastAction, onuUniStormCtrlBroadcastInRate, onuUniStormCtrlBroadcastOutRate, 
				onuUniStormCtrlMulticastEnable, onuUniStormCtrlMulticastMode, onuUniStormCtrlMulticastAction, onuUniStormCtrlMulticastInRate, onuUniStormCtrlMulticastOutRate, 
				onuUniStormCtrlUnicastEnable, onuUniStormCtrlUnicastMode, onuUniStormCtrlUnicastAction, onuMgmtGateway, onuMgmtNetmask, 
				onuMgmtIpAddr, onuUniStormCtrlUnicastOutRate, onuUniStormCtrlUnicastInRate, onuUniStormCtrlType, onuUniStormCtrlMode, 
				onuUniStormCtrlAction, onuUniStormCtrlInRate, onuUniStormCtrlOutRate, onuMgmtPriority, onuMgmtSVlan, 
				onuMgmtCVlan }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing advanced ONU feature
				management"
			::= { fdOnuGroups 2 }

		
--    *******.4.1.34592.*******5.1.3
-- *******.4.1.34592.*******5.1.3
		-- *******.4.1.34592.*******5.1.3
		fdOnuPortParaGroup OBJECT-GROUP
			OBJECTS { uniPortUserInfo, uniPortLink, uniPortAutoNego, uniPortSpeed, uniPortDuplex, 
				uniPortFlowCtrl, uniPortMacEntryLimit, uniPortMacAgeTime, uniPortFowardMode, uniPortEnable, 
				uniPortRstpState, uniPortName, uniPortType }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing ONU port parameters management"
			::= { fdOnuGroups 3 }

		
--    *******.4.1.34592.*******5.1.4
-- *******.4.1.34592.*******5.1.4
		-- *******.4.1.34592.*******5.1.4
		onuIgmpSnoopGroup OBJECT-GROUP
			OBJECTS { igmpSnoopParaData, igmpSnoopGroupData }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing igmp snooping feature
				management"
			::= { fdOnuGroups 4 }

		
--    *******.4.1.34592.*******5.1.5
-- *******.4.1.34592.*******5.1.5
		-- *******.4.1.34592.*******5.1.5
		fdOnuLpTestGroup OBJECT-GROUP
			OBJECTS { onuLoopTestData, onuLoopTestResult }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing onu loopback test feature
				management"
			::= { fdOnuGroups 5 }

		
--    *******.4.1.34592.*******5.1.6
-- *******.4.1.34592.*******5.1.6
		-- *******.4.1.34592.*******5.1.6
		fdOnuVoiceGroup OBJECT-GROUP
			OBJECTS { onuIADMac, onuIADProtocol, onuIADSwVersion, onuIADSwTime, onuIADVoipNum, 
				onuIADMode, onuIADIpAddr, onuIADNetMask, onuIADDefaultGw, onuIADPppoeMode, 
				onuIADPppoeUsrnm, onuIADPppoePw, onuIADTagMode, onuIADVoiceCVlan, onuIADVoiceSVlan, 
				onuIADVoicePriority, h248MgPort, h248MgcIp, h248MgcPort, h248BakMacIp, 
				h248BakMgcPort, h248ActiveMgc, h248RegMode, h248MID, h248HbMode, 
				h248HbCycle, h248HbCount, onuVoipPortId, h248UserTIDName, h248RtpTIDNum, 
				h248RtpTIDPrefix, h248RtpTIDDigitBegin, h248RtpTIDMode, h248RtpTIDDigitLen, h248RtpTIDCount, 
				h248FstRtpTIDName, sipMgPort, sipProxySvrIp, sipProxySvrPort, sipBakProxySvrIp, 
				sipBakProxySvrPort, sipActiveProxySvr, sipRegSvrIp, sipRegSvrPort, sipBakRegSvrIp, 
				sipBakRegSvrPort, sipOutBoundSvrIp, sipOutBoundSvrPort, sipRegInterval, sipHbSwitch, 
				sipHbCycle, sipHbCount, sipUserAccount, sipUserName, sipUserPasswd, 
				sipDigitMapLen, sipDigitMap, onuIADVoiceFaxMode, onuIADVoiceFaxControl, onuIADOperStatusSet, 
				onuIADOperStatus, onuIADPotsStatus, onuIADPotsServiceState, onuIADPotsCodeMode, onuIADPotsEnable, 
				potsId }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { fdOnuGroups 6 }

		
		-- *******.4.1.34592.*******5.1.7
		fdOnuStatisticsTable OBJECT-GROUP
			OBJECTS { onuPonStatUpOctets, onuPonStatDnOctets, onuPonStatUpFrames, onuPonStatDnBcFrames, onuPonStatUpBcFrames, 
				onuPonStatDnMcFrames, onuPonStatUpMcFrames, onuPonStatDnCrcErrFrames, onuPonStatUpCrcErrFrames, onuUniStatDnDropEvents, 
				onuUniStatUpDropEvents, onuUniStatDnFrames, onuUniStatUpFrames, onuUniStatDnBcFrames, onuUniStatUpBcFrames, 
				onuUniStatDnMcFrames, onuUniStatUpMcFrames, onuUniStatDnCrcErrFrames, onuUniStatUpCrcErrFrames, onuPonStatDnFrames, 
				onuPonStatUpDropEvents, onuPonStatDnDropEvents, onuUniStatUpOctects, onuUniStatDnOctects }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { fdOnuGroups 7 }

		
--    *******.4.1.34592.*******5.2
-- *******.4.1.34592.*******5.2
		-- *******.4.1.34592.*******5.2
		fdOnuCompliances OBJECT IDENTIFIER ::= { fdOnuConformance 2 }

		
--    *******.4.1.34592.*******5.2.1
-- this module
-- this module
-- *******.4.1.34592.*******5.2.1
-- this module
		-- *******.4.1.34592.*******5.2.1
		fdOnuCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"The compliance statement"
			MODULE -- this module
				MANDATORY-GROUPS { fdOnuBaseManageGroup, fdOnuPortParaGroup, onuIgmpSnoopGroup, fdOnuLpTestGroup, fdOnuVoiceGroup
					 }
				GROUP fdOnuAdvanceManageGroup
					DESCRIPTION 
						"This group may or may not be implemented"
			::= { fdOnuCompliances 1 }

		
	
	END

--
-- FD-ONU-MIB.my
--
