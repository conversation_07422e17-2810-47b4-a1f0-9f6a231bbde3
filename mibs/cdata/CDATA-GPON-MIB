--
-- CDATA-GPON-MIB_180930.mib
-- MIB generated by MG-SO<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Sunday, September 30, 2018 at 16:46:57
--

--  CDATA-GPON-MIB_161013_2.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Thursday, October 13, 2016 at 17:48:34
-- 

	CDATA-GPON-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			gpon			
				FROM CDATA-COMMON-SMI			
			OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP			
				FROM SNMPv2-CONF			
			sysDescr, sysObjectID, sysUpTime, sysContact, sysName, 
			sysLocation, sysServices, sysORLastChange, sysORIndex, sysORID, 
			sysORDescr, sysORUpTime, snmpInPkts, snmpOutPkts, snmpInBadVersions, 
			snmpInBadCommunityNames, snmpInBadCommunityUses, snmpInASNParseErrs, snmpInTooBigs, snmpInNoSuchNames, 
			snmpInBadValues, snmpInReadOnlys, snmpInGenErrs, snmpInTotalReqVars, snmpInTotalSetVars, 
			snmpInGetRequests, snmpInGetNexts, snmpInSetRequests, snmpInGetResponses, snmpInTraps, 
			snmpOutTooBigs, snmpOutNoSuchNames, snmpOutBadValues, snmpOutGenErrs, snmpOutGetRequests, 
			snmpOutGetNexts, snmpOutSetRequests, snmpOutGetResponses, snmpOutTraps, snmpEnableAuthenTraps, 
			snmpSilentDrops, snmpProxyDrops, snmpTrapOID, snmpTrapEnterprise, coldStart, 
			warmStart, authenticationFailure, snmpSetSerialNo, snmpBasicCompliance, snmpSetGroup, 
			systemGroup, snmpBasicNotificationsGroup, snmpGroup, snmpCommunityGroup, snmpObsoleteGroup			
				FROM SNMPv2-MIB			
			IpAddress, Integer32, Unsigned32, BITS, OBJECT-TYPE, 
			MODULE-IDENTITY, NOTIFICATION-TYPE			
				FROM SNMPv2-SMI			
			DisplayString, TimeStamp, RowStatus, TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
--  *******.4.1.34592.1.5.1
-- March 02, 2016 at 16:06 GMT
		-- *******.4.1.34592.1.5.1
		gponMIB MODULE-IDENTITY 
			LAST-UPDATED "201603021606Z"		-- March 02, 2016 at 16:06 GMT
			ORGANIZATION 
				"Organization."
			CONTACT-INFO 
				"Contact-info."
			DESCRIPTION 
				"Description."
			::= { gpon 1 }

		
	
--
-- Textual conventions
--
	
--  Dba profile index,
-- 1-128:user dba profile
-- 65535:default dba profile
		GponDbaProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..128 | 65535)

		GponDbaProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..16))

		GponLinePorfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..512)

		GponLinePorfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..32))

		GponLinePorfileTcontId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..3 | 65535)

		GponLinePorfileGemId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..24)

		GponLinePorfileGemMapId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..8)

		GponVlanPriority ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (0..7)

		GponSrvProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..512)

		GponSrvProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..16))

		GponVlanId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..4094)

		GponTrafficProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..256)

		GponTrafficProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..16))

		GponSipAgentProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..16 | 65535)

		GponSipAgentProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..16))

		GponSipUri ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..63))

		GponSipRightFlagProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..16 | 65535)

		GponSipRightFlagProfileName ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..16))

		GponDigitMapProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..16 | 65535)

		GponPotsProfileId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..16 | 65535)

		GponDeviceId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..8)

		GponCardId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (0..8)

		GponOltPortId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32

		GponSwitch ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				enable(1),
				disable(2)
				}

--  Textual conventions
-- 
		GponOltAutoAuthRuleId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..20)

		GponOnuId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..128)

		GponOnuSn ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (12))

		GponOnuPassword ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..10))

		GponOnuLoid ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..24))

		GponOnuLoidPassword ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (1..12))

		GponOnuEthPortId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..8)

		GponMac ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX OCTET STRING (SIZE (6))

		GponOnuPotsPortId ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX Integer32 (1..8)

	
--
-- Node definitions
--
	
--  Node definitions
-- 
-- *******.4.1.34592.*******
		-- *******.4.1.34592.*******
		gponObjects OBJECT IDENTIFIER ::= { gponMIB 1 }

		
--  *******.4.1.34592.*******.1
		-- *******.4.1.34592.*******.1
		gponProfileObjects OBJECT IDENTIFIER ::= { gponObjects 1 }

		
--  *******.4.1.34592.*******.1.2
		-- *******.4.1.34592.*******.1.2
		gponDbaProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 2 }

		
--  *******.4.1.34592.*******.1.2.1
		-- *******.4.1.34592.*******.1.2.1
		gponDbaProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponDbaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDbaProfileObjects 1 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponDbaProfileInfoEntry OBJECT-TYPE
			SYNTAX GponDbaProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponDbaProfileId }
			::= { gponDbaProfileInfoTable 1 }

		
		GponDbaProfileInfoEntry ::=
			SEQUENCE { 
				gponDbaProfileId
					GponDbaProfileId,
				gponDbaProfileRowStatus
					RowStatus,
				gponDbaProfileName
					GponDbaProfileName,
				gponDbaProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponDbaProfileId OBJECT-TYPE
			SYNTAX GponDbaProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Dba profile index,
				1-128:user dba profile
				65535:default dba profile, profile id is 0
				"
			::= { gponDbaProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponDbaProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Row status. This object is used to differentiate the creation, modification and deletion operations for an object."
			::= { gponDbaProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponDbaProfileName OBJECT-TYPE
			SYNTAX GponDbaProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDbaProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponDbaProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Number of bound DBA profiles."
			::= { gponDbaProfileInfoEntry 4 }

		
--  *******.4.1.34592.*******.1.2.2
		-- *******.4.1.34592.*******.1.2.2
		gponDbaProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponDbaProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDbaProfileObjects 2 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponDbaProfileCfgEntry OBJECT-TYPE
			SYNTAX GponDbaProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponDbaProfileId }
			::= { gponDbaProfileCfgTable 1 }

		
		GponDbaProfileCfgEntry ::=
			SEQUENCE { 
				gponDbaProfileType
					INTEGER,
				gponDbaProfileFixRate
					Integer32,
				gponDbaProfileAssureRate
					Integer32,
				gponDbaProfileMaxRate
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponDbaProfileType OBJECT-TYPE
			SYNTAX INTEGER
				{
				fix(1),
				assure(2),
				assureAndMax(3),
				max(4),
				fixAndAssureAndMax(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"The DBA type index."
			::= { gponDbaProfileCfgEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponDbaProfileFixRate OBJECT-TYPE
			SYNTAX Integer32 (0 | 128..1060864)
			UNITS "kbps"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Fixed bandwidth of the profile."
			::= { gponDbaProfileCfgEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponDbaProfileAssureRate OBJECT-TYPE
			SYNTAX Integer32 (0 | 256..1060864)
			UNITS "kbps"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Assured bandwidth of the profile."
			::= { gponDbaProfileCfgEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponDbaProfileMaxRate OBJECT-TYPE
			SYNTAX Integer32 (0 | 128..1274880)
			UNITS "kbps"
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Max bandwidth of the profile."
			::= { gponDbaProfileCfgEntry 4 }

		
--  *******.4.1.34592.*******.1.2.3
		-- *******.4.1.34592.*******.1.2.3
		gponDbaProfileBindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponDbaProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDbaProfileObjects 3 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponDbaProfileBindInfoEntry OBJECT-TYPE
			SYNTAX GponDbaProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponDbaProfileId, gponLineProfileId }
			::= { gponDbaProfileBindInfoTable 1 }

		
		GponDbaProfileBindInfoEntry ::=
			SEQUENCE { 
				gponDbaProfileBindLineProfileTcontList
					DisplayString
			 }

--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponDbaProfileBindLineProfileTcontList OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDbaProfileBindInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.3
		-- *******.4.1.34592.*******.1.3
		gponLineProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 3 }

		
--  *******.4.1.34592.*******.1.3.1
		-- *******.4.1.34592.*******.1.3.1
		gponLineProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponLineProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileObjects 1 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponLineProfileInfoEntry OBJECT-TYPE
			SYNTAX GponLineProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponLineProfileId }
			::= { gponLineProfileInfoTable 1 }

		
		GponLineProfileInfoEntry ::=
			SEQUENCE { 
				gponLineProfileId
					GponLinePorfileId,
				gponLineProfileRowStatus
					RowStatus,
				gponLineProfileName
					GponLinePorfileName,
				gponLineProfileMappingMode
					INTEGER,
				gponLineProfileTcontNum
					Integer32,
				gponLineProfileGemNum
					Integer32,
				gponLineProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponLineProfileId OBJECT-TYPE
			SYNTAX GponLinePorfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Line profile index,
				1-512:user line profile
				65535:default line profile, it is reserve."
			::= { gponLineProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponLineProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponLineProfileName OBJECT-TYPE
			SYNTAX GponLinePorfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponLineProfileMappingMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				vlan(1),
				priority(2),
				vlanAndPriority(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileInfoEntry 4 }

		
--  *******.4.1.34592.*******.*******.5
		-- *******.4.1.34592.*******.*******.5
		gponLineProfileTcontNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileInfoEntry 5 }

		
--  *******.4.1.34592.*******.*******.6
		-- *******.4.1.34592.*******.*******.6
		gponLineProfileGemNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileInfoEntry 6 }

		
--  *******.4.1.34592.*******.*******.7
		-- *******.4.1.34592.*******.*******.7
		gponLineProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileInfoEntry 7 }

		
--  *******.4.1.34592.*******.1.3.2
		-- *******.4.1.34592.*******.1.3.2
		gponLineProfileTcontTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponLineProfileTcontEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileObjects 2 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponLineProfileTcontEntry OBJECT-TYPE
			SYNTAX GponLineProfileTcontEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponLineProfileId, gponLineProfileTcontId }
			::= { gponLineProfileTcontTable 1 }

		
		GponLineProfileTcontEntry ::=
			SEQUENCE { 
				gponLineProfileTcontId
					GponLinePorfileTcontId,
				gponLineProfileTcontRowStatus
					RowStatus,
				gponLineProfileTcontDbaProfileId
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponLineProfileTcontId OBJECT-TYPE
			SYNTAX GponLinePorfileTcontId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileTcontEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponLineProfileTcontRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileTcontEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponLineProfileTcontDbaProfileId OBJECT-TYPE
			SYNTAX Integer32 (1..128 | 65535)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileTcontEntry 3 }

		
--  *******.4.1.34592.*******.1.3.3
		-- *******.4.1.34592.*******.1.3.3
		gponLineProfileGemTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponLineProfileGemEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileObjects 3 }

		
--  *******.4.1.34592.*******.1.3.3.1
		-- *******.4.1.34592.*******.1.3.3.1
		gponLineProfileGemEntry OBJECT-TYPE
			SYNTAX GponLineProfileGemEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponLineProfileId, gponLineProfileGemId }
			::= { gponLineProfileGemTable 1 }

		
		GponLineProfileGemEntry ::=
			SEQUENCE { 
				gponLineProfileGemId
					GponLinePorfileGemId,
				gponLineProfileGemRowStatus
					RowStatus,
				gponLineProfileGemTcontId
					Integer32,
				gponLineProfileGemUpCar
					Integer32,
				gponLineProfileGemDownCar
					Integer32,
				gponLineProfileGemMapNum
					Integer32
			 }

--  *******.4.1.34592.*******.1.3.3.1.1
		-- *******.4.1.34592.*******.1.3.3.1.1
		gponLineProfileGemId OBJECT-TYPE
			SYNTAX GponLinePorfileGemId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemEntry 1 }

		
--  *******.4.1.34592.*******.1.3.3.1.2
		-- *******.4.1.34592.*******.1.3.3.1.2
		gponLineProfileGemRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemEntry 2 }

		
--  *******.4.1.34592.*******.1.3.3.1.3
		-- *******.4.1.34592.*******.1.3.3.1.3
		gponLineProfileGemTcontId OBJECT-TYPE
			SYNTAX Integer32 (1..3)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemEntry 3 }

		
--  *******.4.1.34592.*******.1.3.3.1.4
		-- *******.4.1.34592.*******.1.3.3.1.4
		gponLineProfileGemUpCar OBJECT-TYPE
			SYNTAX Integer32 (1..256)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemEntry 4 }

		
--  *******.4.1.34592.*******.1.3.3.1.5
		-- *******.4.1.34592.*******.1.3.3.1.5
		gponLineProfileGemDownCar OBJECT-TYPE
			SYNTAX Integer32 (1..256)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemEntry 5 }

		
--  *******.4.1.34592.*******.1.3.3.1.6
		-- *******.4.1.34592.*******.1.3.3.1.6
		gponLineProfileGemMapNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemEntry 6 }

		
--  *******.4.1.34592.*******.1.3.4
		-- *******.4.1.34592.*******.1.3.4
		gponLineProfileGemMapTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponLineProfileGemMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileObjects 4 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponLineProfileGemMapEntry OBJECT-TYPE
			SYNTAX GponLineProfileGemMapEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponLineProfileId, gponLineProfileGemId, gponLineProfileGemMapId }
			::= { gponLineProfileGemMapTable 1 }

		
		GponLineProfileGemMapEntry ::=
			SEQUENCE { 
				gponLineProfileGemMapId
					GponLinePorfileGemMapId,
				gponLineProfileGemMapRowStatus
					RowStatus,
				gponLineProfileGemMapVlan
					INTEGER,
				gponLineProfileGemMapPriority
					GponVlanPriority
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponLineProfileGemMapId OBJECT-TYPE
			SYNTAX GponLinePorfileGemMapId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemMapEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponLineProfileGemMapRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemMapEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponLineProfileGemMapVlan OBJECT-TYPE
			SYNTAX INTEGER (1..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemMapEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponLineProfileGemMapPriority OBJECT-TYPE
			SYNTAX GponVlanPriority
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileGemMapEntry 4 }

		
--  *******.4.1.34592.*******.1.3.5
		-- *******.4.1.34592.*******.1.3.5
		gponLineProfileBindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponLineProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileObjects 5 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponLineProfileBindInfoEntry OBJECT-TYPE
			SYNTAX GponLineProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponLineProfileId, gponOltDeviceId, gponOltCardId, gponOltPortId }
			::= { gponLineProfileBindInfoTable 1 }

		
		GponLineProfileBindInfoEntry ::=
			SEQUENCE { 
				gponLineProfileBindOnuList
					DisplayString
			 }

--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponLineProfileBindOnuList OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponLineProfileBindInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.4
		-- *******.4.1.34592.*******.1.4
		gponSrvProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 4 }

		
--  *******.4.1.34592.*******.1.4.1
		-- *******.4.1.34592.*******.1.4.1
		gponSrvProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSrvProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileObjects 1 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponSrvProfileInfoEntry OBJECT-TYPE
			SYNTAX GponSrvProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSrvProfileId }
			::= { gponSrvProfileInfoTable 1 }

		
		GponSrvProfileInfoEntry ::=
			SEQUENCE { 
				gponSrvProfileId
					GponSrvProfileId,
				gponSrvProfileRowStatus
					RowStatus,
				gponSrvProfileName
					GponSrvProfileName,
				gponSrvProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponSrvProfileId OBJECT-TYPE
			SYNTAX GponSrvProfileId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponSrvProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponSrvProfileName OBJECT-TYPE
			SYNTAX GponSrvProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.*******.8
		-- *******.4.1.34592.*******.*******.8
		gponSrvProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileInfoEntry 8 }

		
--  *******.4.1.34592.*******.1.4.2
		-- *******.4.1.34592.*******.1.4.2
		gponSrvProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSrvProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileObjects 2 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponSrvProfileCfgEntry OBJECT-TYPE
			SYNTAX GponSrvProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSrvProfileId }
			::= { gponSrvProfileCfgTable 1 }

		
		GponSrvProfileCfgEntry ::=
			SEQUENCE { 
				gponSrvProfileMcMode
					INTEGER,
				gponSrvProfileUpIgmpFwdMode
					INTEGER,
				gponSrvProfileUpIgmpTCI
					Integer32,
				gponSrvProfileDnMcMode
					INTEGER,
				gponSrvProfileDnMcTCI
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponSrvProfileMcMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				igmp-snooping(0),
				igmp-snooping-proxy(1),
				igmp-proxy(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileCfgEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponSrvProfileUpIgmpFwdMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				transparent(0),
				default(1),
				translation(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileCfgEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponSrvProfileUpIgmpTCI OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileCfgEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponSrvProfileDnMcMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				transparent(0),
				untag(1),
				translation(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileCfgEntry 4 }

		
--  *******.4.1.34592.*******.*******.5
		-- *******.4.1.34592.*******.*******.5
		gponSrvProfileDnMcTCI OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileCfgEntry 5 }

		
--  *******.4.1.34592.*******.1.4.3
		-- *******.4.1.34592.*******.1.4.3
		gponSrvProfilePortNumTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSrvProfilePortNumEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileObjects 3 }

		
--  *******.4.1.34592.*******.1.4.3.1
		-- *******.4.1.34592.*******.1.4.3.1
		gponSrvProfilePortNumEntry OBJECT-TYPE
			SYNTAX GponSrvProfilePortNumEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSrvProfileId }
			::= { gponSrvProfilePortNumTable 1 }

		
		GponSrvProfilePortNumEntry ::=
			SEQUENCE { 
				gponSrvProfileEthNum
					Integer32,
				gponSrvProfilePotsNum
					Integer32,
				gponSrvProfileCatvNum
					Integer32
			 }

--  *******.4.1.34592.*******.1.4.3.1.1
		-- *******.4.1.34592.*******.1.4.3.1.1
		gponSrvProfileEthNum OBJECT-TYPE
			SYNTAX Integer32 (0..8 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortNumEntry 1 }

		
--  *******.4.1.34592.*******.1.4.3.1.2
		-- *******.4.1.34592.*******.1.4.3.1.2
		gponSrvProfilePotsNum OBJECT-TYPE
			SYNTAX Integer32 (0..8 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortNumEntry 2 }

		
--  *******.4.1.34592.*******.1.4.3.1.3
		-- *******.4.1.34592.*******.1.4.3.1.3
		gponSrvProfileCatvNum OBJECT-TYPE
			SYNTAX Integer32 (0..8 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortNumEntry 3 }

		
--  *******.4.1.34592.*******.1.4.4
		-- *******.4.1.34592.*******.1.4.4
		gponSrvProfilePortVlanCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSrvProfilePortVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileObjects 4 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponSrvProfilePortVlanCfgEntry OBJECT-TYPE
			SYNTAX GponSrvProfilePortVlanCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSrvProfileId, gponSrvProfilePortType, gponSrvProfilePortId, gponSrvProfilePortVlanEntryId }
			::= { gponSrvProfilePortVlanCfgTable 1 }

		
		GponSrvProfilePortVlanCfgEntry ::=
			SEQUENCE { 
				gponSrvProfilePortType
					INTEGER,
				gponSrvProfilePortId
					Integer32,
				gponSrvProfilePortVlanEntryId
					Integer32,
				gponSrvProfilePortVlanRowStatus
					RowStatus,
				gponSrvProfilePortVlanMode
					INTEGER,
				gponSrvProfilePortVlanSvlan
					Integer32,
				gponSrvProfilePortVlanSpri
					Integer32,
				gponSrvProfilePortCvlan
					GponVlanId,
				gponSrvProfilePortVlanCpri
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponSrvProfilePortType OBJECT-TYPE
			SYNTAX INTEGER { eth(1) }
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponSrvProfilePortId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponSrvProfilePortVlanEntryId OBJECT-TYPE
			SYNTAX Integer32 (1..9)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponSrvProfilePortVlanRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 4 }

		
--  *******.4.1.34592.*******.*******.5
		-- *******.4.1.34592.*******.*******.5
		gponSrvProfilePortVlanMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				transparent(1),
				qinq(2),
				translation(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 5 }

		
--  *******.4.1.34592.*******.*******.6
		-- *******.4.1.34592.*******.*******.6
		gponSrvProfilePortVlanSvlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 6 }

		
--  *******.4.1.34592.*******.*******.7
		-- *******.4.1.34592.*******.*******.7
		gponSrvProfilePortVlanSpri OBJECT-TYPE
			SYNTAX Integer32 (0..7 | 255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 7 }

		
--  *******.4.1.34592.*******.*******.8
		-- *******.4.1.34592.*******.*******.8
		gponSrvProfilePortCvlan OBJECT-TYPE
			SYNTAX GponVlanId (1..4094)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 8 }

		
--  *******.4.1.34592.*******.*******.9
		-- *******.4.1.34592.*******.*******.9
		gponSrvProfilePortVlanCpri OBJECT-TYPE
			SYNTAX Integer32 (0..7 | 255)
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfilePortVlanCfgEntry 9 }

		
--  *******.4.1.34592.*******.1.4.5
		-- *******.4.1.34592.*******.1.4.5
		gponSrvProfileBindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSrvProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileObjects 5 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponSrvProfileBindInfoEntry OBJECT-TYPE
			SYNTAX GponSrvProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSrvProfileId, gponOltDeviceId, gponOltCardId, gponOltPortId }
			::= { gponSrvProfileBindInfoTable 1 }

		
		GponSrvProfileBindInfoEntry ::=
			SEQUENCE { 
				gponSrvProfileBindOnuList
					DisplayString
			 }

--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponSrvProfileBindOnuList OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSrvProfileBindInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.6
		-- *******.4.1.34592.*******.1.6
		gponTrafficProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 6 }

		
--  *******.4.1.34592.*******.1.6.1
		-- *******.4.1.34592.*******.1.6.1
		gponTrafficProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponTrafficProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileObjects 1 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponTrafficProfileInfoEntry OBJECT-TYPE
			SYNTAX GponTrafficProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponTrafficProfileId }
			::= { gponTrafficProfileInfoTable 1 }

		
		GponTrafficProfileInfoEntry ::=
			SEQUENCE { 
				gponTrafficProfileId
					GponTrafficProfileId,
				gponTrafficProfileRowStatus
					RowStatus,
				gponTrafficProfileName
					GponTrafficProfileName,
				gponTrafficProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponTrafficProfileId OBJECT-TYPE
			SYNTAX GponTrafficProfileId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponTrafficProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponTrafficProfileName OBJECT-TYPE
			SYNTAX GponTrafficProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponTrafficProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileInfoEntry 4 }

		
--  *******.4.1.34592.*******.1.6.3
		-- *******.4.1.34592.*******.1.6.3
		gponTrafficProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponTrafficProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileObjects 3 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponTrafficProfileCfgEntry OBJECT-TYPE
			SYNTAX GponTrafficProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponTrafficProfileId }
			::= { gponTrafficProfileCfgTable 1 }

		
		GponTrafficProfileCfgEntry ::=
			SEQUENCE { 
				gponTrafficProfileCfgCir
					Integer32,
				gponTrafficProfileCfgPir
					Integer32,
				gponTrafficProfileCfgCbs
					Integer32,
				gponTrafficProfileCfgPbs
					Integer32,
				gponTrafficProfileCfgPriority
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponTrafficProfileCfgCir OBJECT-TYPE
			SYNTAX Integer32 (64..10240000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileCfgEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponTrafficProfileCfgPir OBJECT-TYPE
			SYNTAX Integer32 (64..10240000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileCfgEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponTrafficProfileCfgCbs OBJECT-TYPE
			SYNTAX Integer32 (2000..10240000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileCfgEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponTrafficProfileCfgPbs OBJECT-TYPE
			SYNTAX Integer32 (2000..10240000)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileCfgEntry 4 }

		
--  *******.4.1.34592.*******.*******.5
		-- *******.4.1.34592.*******.*******.5
		gponTrafficProfileCfgPriority OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrafficProfileCfgEntry 5 }

		
--  *******.4.1.34592.*******.1.8
		-- *******.4.1.34592.*******.1.8
		gponSipAgentProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 8 }

		
--  *******.4.1.34592.*******.1.8.1
		-- *******.4.1.34592.*******.1.8.1
		gponSipAgentProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSipAgentProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileObjects 1 }

		
--  *******.4.1.34592.*******.1.8.1.1
		-- *******.4.1.34592.*******.1.8.1.1
		gponSipAgentProfileInfoEntry OBJECT-TYPE
			SYNTAX GponSipAgentProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSipAgentProfileId }
			::= { gponSipAgentProfileInfoTable 1 }

		
		GponSipAgentProfileInfoEntry ::=
			SEQUENCE { 
				gponSipAgentProfileId
					GponSipAgentProfileId,
				gponSipAgentProfileRowStatus
					RowStatus,
				gponSipAgentProfileName
					GponSipAgentProfileName,
				gponSipAgentProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.1.8.1.1.1
		-- *******.4.1.34592.*******.1.8.1.1.1
		gponSipAgentProfileId OBJECT-TYPE
			SYNTAX GponSipAgentProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.1.8.1.1.2
		-- *******.4.1.34592.*******.1.8.1.1.2
		gponSipAgentProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.8.1.1.3
		-- *******.4.1.34592.*******.1.8.1.1.3
		gponSipAgentProfileName OBJECT-TYPE
			SYNTAX GponSipAgentProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.1.8.1.1.4
		-- *******.4.1.34592.*******.1.8.1.1.4
		gponSipAgentProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileInfoEntry 4 }

		
--  *******.4.1.34592.*******.1.8.2
		-- *******.4.1.34592.*******.1.8.2
		gponSipAgentProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSipAgentProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileObjects 2 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponSipAgentProfileCfgEntry OBJECT-TYPE
			SYNTAX GponSipAgentProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSipAgentProfileId }
			::= { gponSipAgentProfileCfgTable 1 }

		
		GponSipAgentProfileCfgEntry ::=
			SEQUENCE { 
				gponSipAgentProfileProxyServerUri
					GponSipUri,
				gponSipAgentProfileRtpDscp
					Integer32,
				gponSipAgentProfileRtpMinPort
					Integer32,
				gponSipAgentProfileRtpMaxPort
					Integer32,
				gponSipAgentProfileSignalDscp
					Integer32,
				gponSipAgentProfileSignalPort
					Integer32,
				gponSipAgentProfileSignalTransferMode
					INTEGER,
				gponSipAgentProfileRegistrationExpiration
					Unsigned32,
				gponSipAgentProfileRegistrationReregHeadStartTime
					Unsigned32,
				gponSipAgentProfileRegistrationServerUri
					GponSipUri,
				gponSipAgentProfileVoiceMailServerUri
					GponSipUri,
				gponSipAgentProfileVoiceMailSubscriptionExpiration
					Unsigned32,
				gponSipAgentProfileConfFactory
					GponSipUri,
				gponSipAgentProfileBridgedLineAgent
					GponSipUri,
				gponSipAgentProfileAuthRealm
					OCTET STRING,
				gponSipAgentProfileProxyServerPort
					Integer32,
				gponSipAgentProfileRegistrationServerPort
					Integer32
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponSipAgentProfileProxyServerUri OBJECT-TYPE
			SYNTAX GponSipUri
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponSipAgentProfileRtpDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponSipAgentProfileRtpMinPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponSipAgentProfileRtpMaxPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 4 }

		
--  *******.4.1.34592.*******.*******.5
		-- *******.4.1.34592.*******.*******.5
		gponSipAgentProfileSignalDscp OBJECT-TYPE
			SYNTAX Integer32 (0..63)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 5 }

		
--  *******.4.1.34592.*******.*******.6
		-- *******.4.1.34592.*******.*******.6
		gponSipAgentProfileSignalPort OBJECT-TYPE
			SYNTAX Integer32 (1..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 6 }

		
--  *******.4.1.34592.*******.*******.7
		-- *******.4.1.34592.*******.*******.7
		gponSipAgentProfileSignalTransferMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				udp(0),
				tcp(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 7 }

		
--  *******.4.1.34592.*******.*******.8
		-- *******.4.1.34592.*******.*******.8
		gponSipAgentProfileRegistrationExpiration OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 8 }

		
--  *******.4.1.34592.*******.*******.9
		-- *******.4.1.34592.*******.*******.9
		gponSipAgentProfileRegistrationReregHeadStartTime OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 9 }

		
--  *******.4.1.34592.*******.*******.10
		-- *******.4.1.34592.*******.*******.10
		gponSipAgentProfileRegistrationServerUri OBJECT-TYPE
			SYNTAX GponSipUri
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 10 }

		
--  *******.4.1.34592.*******.*******.11
		-- *******.4.1.34592.*******.*******.11
		gponSipAgentProfileVoiceMailServerUri OBJECT-TYPE
			SYNTAX GponSipUri
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 11 }

		
--  *******.4.1.34592.*******.*******.12
		-- *******.4.1.34592.*******.*******.12
		gponSipAgentProfileVoiceMailSubscriptionExpiration OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 12 }

		
--  *******.4.1.34592.*******.*******.13
		-- *******.4.1.34592.*******.*******.13
		gponSipAgentProfileConfFactory OBJECT-TYPE
			SYNTAX GponSipUri
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 13 }

		
--  *******.4.1.34592.*******.*******.14
		-- *******.4.1.34592.*******.*******.14
		gponSipAgentProfileBridgedLineAgent OBJECT-TYPE
			SYNTAX GponSipUri
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 14 }

		
--  *******.4.1.34592.*******.*******.15
		-- *******.4.1.34592.*******.*******.15
		gponSipAgentProfileAuthRealm OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..24))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 15 }

		
		-- *******.4.1.34592.*******.*******.16
		gponSipAgentProfileProxyServerPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 16 }

		
		-- *******.4.1.34592.*******.*******.17
		gponSipAgentProfileRegistrationServerPort OBJECT-TYPE
			SYNTAX Integer32 (0..65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileCfgEntry 17 }

		
--  *******.4.1.34592.*******.1.8.3
		-- *******.4.1.34592.*******.1.8.3
		gponSipAgentProfileBindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSipAgentProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileObjects 3 }

		
--  *******.4.1.34592.*******.1.8.3.1
		-- *******.4.1.34592.*******.1.8.3.1
		gponSipAgentProfileBindInfoEntry OBJECT-TYPE
			SYNTAX GponSipAgentProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSipAgentProfileId, gponSipAgentProfileBindInfoOnuId }
			::= { gponSipAgentProfileBindInfoTable 1 }

		
		GponSipAgentProfileBindInfoEntry ::=
			SEQUENCE { 
				gponSipAgentProfileBindInfoOnuId
					Integer32,
				gponSipAgentProfileBindPotsList
					DisplayString
			 }

--  *******.4.1.34592.*******.1.8.3.1.1
		-- *******.4.1.34592.*******.1.8.3.1.1
		gponSipAgentProfileBindInfoOnuId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileBindInfoEntry 1 }

		
--  *******.4.1.34592.*******.1.8.3.1.2
		-- *******.4.1.34592.*******.1.8.3.1.2
		gponSipAgentProfileBindPotsList OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipAgentProfileBindInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.9
		-- *******.4.1.34592.*******.1.9
		gponSipRightFlagProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 9 }

		
--  *******.4.1.34592.*******.1.9.1
		-- *******.4.1.34592.*******.1.9.1
		gponSipRightFlagProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSipRightFlagProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileObjects 1 }

		
--  *******.4.1.34592.*******.1.9.1.1
		-- *******.4.1.34592.*******.1.9.1.1
		gponSipRightFlagProfileInfoEntry OBJECT-TYPE
			SYNTAX GponSipRightFlagProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSipRightFlagProfileId }
			::= { gponSipRightFlagProfileInfoTable 1 }

		
		GponSipRightFlagProfileInfoEntry ::=
			SEQUENCE { 
				gponSipRightFlagProfileId
					GponSipRightFlagProfileId,
				gponSipRightFlagProfileRowStatus
					RowStatus,
				gponSipRightFlagProfileName
					GponSipRightFlagProfileName,
				gponSipRightFlagProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.1.9.1.1.1
		-- *******.4.1.34592.*******.1.9.1.1.1
		gponSipRightFlagProfileId OBJECT-TYPE
			SYNTAX GponSipRightFlagProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.1.9.1.1.2
		-- *******.4.1.34592.*******.1.9.1.1.2
		gponSipRightFlagProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.9.1.1.3
		-- *******.4.1.34592.*******.1.9.1.1.3
		gponSipRightFlagProfileName OBJECT-TYPE
			SYNTAX GponSipRightFlagProfileName
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.1.9.1.1.4
		-- *******.4.1.34592.*******.1.9.1.1.4
		gponSipRightFlagProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileInfoEntry 4 }

		
--  *******.4.1.34592.*******.1.9.2
		-- *******.4.1.34592.*******.1.9.2
		gponSipRightFlagProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSipRightFlagProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileObjects 2 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponSipRightFlagProfileCfgEntry OBJECT-TYPE
			SYNTAX GponSipRightFlagProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSipRightFlagProfileId }
			::= { gponSipRightFlagProfileCfgTable 1 }

		
		GponSipRightFlagProfileCfgEntry ::=
			SEQUENCE { 
				gponSipRightFlagProfileCallWaiting
					GponSwitch,
				gponSipRightFlagProfileCallProcess
					BITS,
				gponSipRightFlagProfileCallPresentation
					GponSwitch,
				gponSipRightFlagProfileHotline
					BITS,
				gponSipRightFlagProfileHotlineNum
					DisplayString
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponSipRightFlagProfileCallWaiting OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileCfgEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponSipRightFlagProfileCallProcess OBJECT-TYPE
			SYNTAX BITS
				{
				threeParty(0),
				callTransfer(1),
				callHold(2),
				callPark(3),
				doNotDisturb(4),
				conference(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileCfgEntry 2 }

		
--  *******.4.1.34592.*******.*******.3
		-- *******.4.1.34592.*******.*******.3
		gponSipRightFlagProfileCallPresentation OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileCfgEntry 3 }

		
--  *******.4.1.34592.*******.*******.4
		-- *******.4.1.34592.*******.*******.4
		gponSipRightFlagProfileHotline OBJECT-TYPE
			SYNTAX BITS
				{
				hotline(0),
				hotlineDelay(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileCfgEntry 4 }

		
--  *******.4.1.34592.*******.*******.5
		-- *******.4.1.34592.*******.*******.5
		gponSipRightFlagProfileHotlineNum OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..31))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileCfgEntry 5 }

		
--  *******.4.1.34592.*******.1.9.3
		-- *******.4.1.34592.*******.1.9.4
		gponSipRightFlagProfileBindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponSipRightFlagProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileObjects 4 }

		
--  *******.4.1.34592.*******.*******
		-- *******.4.1.34592.*******.*******
		gponSipRightFlagProfileBindInfoEntry OBJECT-TYPE
			SYNTAX GponSipRightFlagProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponSipRightFlagProfileId, gponSipRightFlagProfileBindInfoOnuId }
			::= { gponSipRightFlagProfileBindInfoTable 1 }

		
		GponSipRightFlagProfileBindInfoEntry ::=
			SEQUENCE { 
				gponSipRightFlagProfileBindInfoOnuId
					Integer32,
				gponSipRightFlagProfileBindPotsList
					DisplayString
			 }

--  *******.4.1.34592.*******.*******.1
		-- *******.4.1.34592.*******.*******.1
		gponSipRightFlagProfileBindInfoOnuId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileBindInfoEntry 1 }

		
--  *******.4.1.34592.*******.*******.2
		-- *******.4.1.34592.*******.*******.2
		gponSipRightFlagProfileBindPotsList OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponSipRightFlagProfileBindInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.10
		-- *******.4.1.34592.*******.1.10
		gponDigitMapProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 10 }

		
--  *******.4.1.34592.*******.1.10.1
		-- *******.4.1.34592.*******.1.10.1
		gponDigitMapProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponDigitMapProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileObjects 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponDigitMapProfileInfoEntry OBJECT-TYPE
			SYNTAX GponDigitMapProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponDigitMapProfileId }
			::= { gponDigitMapProfileInfoTable 1 }

		
		GponDigitMapProfileInfoEntry ::=
			SEQUENCE { 
				gponDigitMapProfileId
					GponDigitMapProfileId,
				gponDigitMapProfileRowStatus
					RowStatus,
				gponDigitMapProfileName
					OCTET STRING,
				gponDigitMapProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponDigitMapProfileId OBJECT-TYPE
			SYNTAX GponDigitMapProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponDigitMapProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponDigitMapProfileName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponDigitMapProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileInfoEntry 4 }

		
--  *******.4.1.34592.*******.1.10.2
		-- *******.4.1.34592.*******.1.10.2
		gponDigitMapProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponDigitMapProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileObjects 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponDigitMapProfileCfgEntry OBJECT-TYPE
			SYNTAX GponDigitMapProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponDigitMapProfileId }
			::= { gponDigitMapProfileCfgTable 1 }

		
		GponDigitMapProfileCfgEntry ::=
			SEQUENCE { 
				gponDigitMapProfileCriticalDialTime
					Integer32,
				gponDigitMapProfileCfgPartialDialTime
					Integer32,
				gponDigitMapProfileCfgDigitmapFormat
					INTEGER
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponDigitMapProfileCriticalDialTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileCfgEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponDigitMapProfileCfgPartialDialTime OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileCfgEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponDigitMapProfileCfgDigitmapFormat OBJECT-TYPE
			SYNTAX INTEGER
				{
				notDefined(0),
				h248(1),
				ncs(2),
				vendorSpecific(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileCfgEntry 3 }

		
--  *******.4.1.34592.*******.1.10.3
		-- *******.4.1.34592.*******.1.10.3
		gponDigitMapProfileDialPlanTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponDigitMapProfileDialPlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileObjects 3 }

		
--  *******.4.1.34592.*******.1.10.3.1
		-- *******.4.1.34592.*******.1.10.3.1
		gponDigitMapProfileDialPlanEntry OBJECT-TYPE
			SYNTAX GponDigitMapProfileDialPlanEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponDigitMapProfileId, gponDigitMapProfileDialPlanId }
			::= { gponDigitMapProfileDialPlanTable 1 }

		
		GponDigitMapProfileDialPlanEntry ::=
			SEQUENCE { 
				gponDigitMapProfileDialPlanId
					Integer32,
				gponDigitMapProfileDialPlanRowStatus
					RowStatus,
				gponDigitMapProfileDialPlanToken
					DisplayString
			 }

--  *******.4.1.34592.*******.1.10.3.1.1
		-- *******.4.1.34592.*******.1.10.3.1.1
		gponDigitMapProfileDialPlanId OBJECT-TYPE
			SYNTAX Integer32 (1..10)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileDialPlanEntry 1 }

		
--  *******.4.1.34592.*******.1.10.3.1.2
		-- *******.4.1.34592.*******.1.10.3.1.2
		gponDigitMapProfileDialPlanRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileDialPlanEntry 2 }

		
--  *******.4.1.34592.*******.1.10.3.1.3
		-- *******.4.1.34592.*******.1.10.3.1.3
		gponDigitMapProfileDialPlanToken OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..27))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileDialPlanEntry 3 }

		
--  *******.4.1.34592.*******.1.10.4
		-- *******.4.1.34592.*******.1.10.4
		gponDigitMapProfileBindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponDigitMapProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileObjects 4 }

		
--  *******.4.1.34592.*******.1.10.4.1
		-- *******.4.1.34592.*******.1.10.4.1
		gponDigitMapProfileBindInfoEntry OBJECT-TYPE
			SYNTAX GponDigitMapProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponDigitMapProfileId, gponDigitMapProfileBindInfoOnuId }
			::= { gponDigitMapProfileBindInfoTable 1 }

		
		GponDigitMapProfileBindInfoEntry ::=
			SEQUENCE { 
				gponDigitMapProfileBindInfoOnuId
					Integer32,
				gponDigitMapProfileBindPotsList
					DisplayString
			 }

--  *******.4.1.34592.*******.1.10.4.1.1
		-- *******.4.1.34592.*******.1.10.4.1.1
		gponDigitMapProfileBindInfoOnuId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileBindInfoEntry 1 }

		
--  *******.4.1.34592.*******.1.10.4.1.2
		-- *******.4.1.34592.*******.1.10.4.1.2
		gponDigitMapProfileBindPotsList OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponDigitMapProfileBindInfoEntry 2 }

		
--  *******.4.1.34592.*******.1.11
		-- *******.4.1.34592.*******.1.11
		gponPotsProfileObjects OBJECT IDENTIFIER ::= { gponProfileObjects 11 }

		
--  *******.4.1.34592.*******.1.11.1
		-- *******.4.1.34592.*******.1.11.1
		gponPotsProfileInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponPotsProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileObjects 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponPotsProfileInfoEntry OBJECT-TYPE
			SYNTAX GponPotsProfileInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponPotsProfileId }
			::= { gponPotsProfileInfoTable 1 }

		
		GponPotsProfileInfoEntry ::=
			SEQUENCE { 
				gponPotsProfileId
					GponPotsProfileId,
				gponPotsProfileRowStatus
					RowStatus,
				gponPotsProfileName
					OCTET STRING,
				gponPotsProfileBindNum
					Integer32
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponPotsProfileId OBJECT-TYPE
			SYNTAX GponPotsProfileId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponPotsProfileRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponPotsProfileName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..16))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponPotsProfileBindNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileInfoEntry 4 }

		
--  *******.4.1.34592.*******.1.11.2
		-- *******.4.1.34592.*******.1.11.2
		gponPotsProfileCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponPotsProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileObjects 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponPotsProfileCfgEntry OBJECT-TYPE
			SYNTAX GponPotsProfileCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponPotsProfileId }
			::= { gponPotsProfileCfgTable 1 }

		
		GponPotsProfileCfgEntry ::=
			SEQUENCE { 
				gponPotsProfileImpedance
					INTEGER,
				gponPotsProfileSignallingCode
					INTEGER,
				gponPotsProfileRxGain
					INTEGER,
				gponPotsProfileTxGain
					INTEGER
			 }

--  *******.4.1.34592.*******.1.1*******
		-- *******.4.1.34592.*******.1.1*******
		gponPotsProfileImpedance OBJECT-TYPE
			SYNTAX INTEGER
				{
				imp600Ohms(0),
				imp900Ohms(1),
				imp150nf750Ohm270Ohm(2),
				imp115nf820Ohm220Ohm(3),
				imp230nf1050Ohm320Ohm(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileCfgEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponPotsProfileSignallingCode OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				loopStart(1),
				groundStart(2),
				loopReverseBattery(3),
				coinFirst(4),
				dialToneFirst(5),
				multiParty(6)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileCfgEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponPotsProfileRxGain OBJECT-TYPE
			SYNTAX INTEGER
				{
				gainN12d0dB(0),
				gainN11d5dB(1),
				gainN11d0dB(2),
				gainN10d5dB(3),
				gainN10d0dB(4),
				gainN9d5dB(5),
				gainN9d0dB(6),
				gainN8d5dB(7),
				gainN8d0dB(8),
				gainN7d5dB(9),
				gainN7d0dB(10),
				gainN6d5dB(11),
				gainN6d0dB(12),
				gainN5d5dB(13),
				gainN5d0dB(14),
				gainN4d5dB(15),
				gainN4d0dB(16),
				gainN3d5dB(17),
				gainN3d0dB(18),
				gainN2d5dB(19),
				gainN2d0dB(20),
				gainN1d5dB(21),
				gainN1d0dB(22),
				gainN0d5dB(23),
				gain0dB(24),
				gainP0d5dB(25),
				gainP1d0dB(26),
				gainP1d5dB(27),
				gainP2d0dB(28),
				gainP2d5dB(29),
				gainP3d0dB(30),
				gainP3d5dB(31),
				gainP4d0dB(32),
				gainP4d5dB(33),
				gainP5d0dB(34),
				gainP5d5dB(35),
				gainP6d0dB(36)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileCfgEntry 3 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.4
		gponPotsProfileTxGain OBJECT-TYPE
			SYNTAX INTEGER
				{
				gainN12d0dB(0),
				gainN11d5dB(1),
				gainN11d0dB(2),
				gainN10d5dB(3),
				gainN10d0dB(4),
				gainN9d5dB(5),
				gainN9d0dB(6),
				gainN8d5dB(7),
				gainN8d0dB(8),
				gainN7d5dB(9),
				gainN7d0dB(10),
				gainN6d5dB(11),
				gainN6d0dB(12),
				gainN5d5dB(13),
				gainN5d0dB(14),
				gainN4d5dB(15),
				gainN4d0dB(16),
				gainN3d5dB(17),
				gainN3d0dB(18),
				gainN2d5dB(19),
				gainN2d0dB(20),
				gainN1d5dB(21),
				gainN1d0dB(22),
				gainN0d5dB(23),
				gain0dB(24),
				gainP0d5dB(25),
				gainP1d0dB(26),
				gainP1d5dB(27),
				gainP2d0dB(28),
				gainP2d5dB(29),
				gainP3d0dB(30),
				gainP3d5dB(31),
				gainP4d0dB(32),
				gainP4d5dB(33),
				gainP5d0dB(34),
				gainP5d5dB(35),
				gainP6d0dB(36)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileCfgEntry 4 }

		
--  *******.4.1.34592.*******.1.11.3
		-- *******.4.1.34592.*******.1.11.3
		gponPotsProfileBindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponPotsProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileObjects 3 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponPotsProfileBindInfoEntry OBJECT-TYPE
			SYNTAX GponPotsProfileBindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponPotsProfileId, gponPotsProfileBindInfoOnuId }
			::= { gponPotsProfileBindInfoTable 1 }

		
		GponPotsProfileBindInfoEntry ::=
			SEQUENCE { 
				gponPotsProfileBindInfoOnuId
					Integer32,
				gponPotsProfileBindPotsList
					DisplayString
			 }

--  *******.4.1.34592.*******.1.1*******
		-- *******.4.1.34592.*******.1.1*******
		gponPotsProfileBindInfoOnuId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileBindInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponPotsProfileBindPotsList OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponPotsProfileBindInfoEntry 2 }

		
--  *******.4.1.34592.*******.2
		-- *******.4.1.34592.*******.2
		gponControlObjects OBJECT IDENTIFIER ::= { gponObjects 2 }

		
--  *******.4.1.34592.*******.2.17
		-- *******.4.1.34592.*******.2.17
		gponOltObjects OBJECT IDENTIFIER ::= { gponControlObjects 17 }

		
--  *******.4.1.34592.*******.2.17.1
		-- *******.4.1.34592.*******.2.17.1
		gponOltControlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOltControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltObjects 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOltControlEntry OBJECT-TYPE
			SYNTAX GponOltControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId }
			::= { gponOltControlTable 1 }

		
		GponOltControlEntry ::=
			SEQUENCE { 
				gponOltDeviceId
					GponDeviceId,
				gponOltCardId
					GponCardId,
				gponOltPortId
					GponOltPortId,
				gponOltPortAutoFindSwitch
					GponSwitch,
				gponOltPortSwitch
					GponSwitch,
				gponOltPortStatus
					INTEGER
			 }

		-- *******.4.1.34592.*******.********.1
		gponOltDeviceId OBJECT-TYPE
			SYNTAX GponDeviceId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltControlEntry 1 }

		
		-- *******.4.1.34592.*******.********.2
		gponOltCardId OBJECT-TYPE
			SYNTAX GponCardId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltControlEntry 2 }

		
--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.3
		gponOltPortId OBJECT-TYPE
			SYNTAX GponOltPortId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltControlEntry 3 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.4
		gponOltPortAutoFindSwitch OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltControlEntry 4 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.5
		gponOltPortSwitch OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltControlEntry 5 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.6
		gponOltPortStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltControlEntry 6 }

		
--  *******.4.1.34592.*******.2.17.2
		-- *******.4.1.34592.*******.2.17.2
		gponOltPortDdmInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOltPortDdmInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltObjects 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOltPortDdmInfoEntry OBJECT-TYPE
			SYNTAX GponOltPortDdmInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId }
			::= { gponOltPortDdmInfoTable 1 }

		
		GponOltPortDdmInfoEntry ::=
			SEQUENCE { 
				gponOltPortDdmTemperature
					OCTET STRING,
				gponOltPortDdmVoltage
					OCTET STRING,
				gponOltPortDdmTxBiasCurrent
					OCTET STRING,
				gponOltPortDdmTxPower
					OCTET STRING,
				gponOltPortDdmRxPower
					OCTET STRING
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOltPortDdmTemperature OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltPortDdmInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOltPortDdmVoltage OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltPortDdmInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOltPortDdmTxBiasCurrent OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltPortDdmInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOltPortDdmTxPower OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltPortDdmInfoEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOltPortDdmRxPower OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..32))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltPortDdmInfoEntry 5 }

		
--  *******.4.1.34592.*******.2.17.3
		-- *******.4.1.34592.*******.2.17.3
		gponOltTransceiverInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOltTransceiverInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltObjects 3 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOltTransceiverInfoEntry OBJECT-TYPE
			SYNTAX GponOltTransceiverInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId }
			::= { gponOltTransceiverInfoTable 1 }

		
		GponOltTransceiverInfoEntry ::=
			SEQUENCE { 
				gponOltTransceiverVendor
					DisplayString,
				gponOltTransceiverProductName
					DisplayString,
				gponOltTransceiverVersion
					DisplayString,
				gponOltTransceiverSerialNumber
					DisplayString
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOltTransceiverVendor OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltTransceiverInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOltTransceiverProductName OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltTransceiverInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOltTransceiverVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltTransceiverInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOltTransceiverSerialNumber OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltTransceiverInfoEntry 4 }

		
--  *******.4.1.34592.*******.2.17.4
		-- *******.4.1.34592.*******.2.17.4
		gponOltAutoAuthTable OBJECT IDENTIFIER ::= { gponOltObjects 4 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOltAutoAuthControlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOltAutoAuthControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthTable 1 }

		
--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOltAutoAuthControlEntry OBJECT-TYPE
			SYNTAX GponOltAutoAuthControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId }
			::= { gponOltAutoAuthControlTable 1 }

		
		GponOltAutoAuthControlEntry ::=
			SEQUENCE { 
				gponOltAutoAuthMode
					INTEGER,
				gponOltAutoAuthSwitch
					GponSwitch,
				gponOltPortAutoAuthSwitchBitMap
					OCTET STRING,
				gponOltAutoAuthRuleNumber
					Integer32
			 }

--  *******.4.1.34592.*******.********.1.2
		-- *******.4.1.34592.*******.********.1.1
		gponOltAutoAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				all(1),
				equid-auth(2),
				vendor-auth(3),
				equid-swver-auth(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthControlEntry 1 }

		
		-- *******.4.1.34592.*******.********.1.2
		gponOltAutoAuthSwitch OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthControlEntry 2 }

		
--  *******.4.1.34592.*******.********.1.3
		-- *******.4.1.34592.*******.********.1.3
		gponOltPortAutoAuthSwitchBitMap OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthControlEntry 3 }

		
		-- *******.4.1.34592.*******.********.1.4
		gponOltAutoAuthRuleNumber OBJECT-TYPE
			SYNTAX Integer32 (1..20)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthControlEntry 4 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOltAutoAuthRuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOltAutoAuthRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthTable 2 }

		
--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOltAutoAuthRuleEntry OBJECT-TYPE
			SYNTAX GponOltAutoAuthRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltAutoAuthRuleId }
			::= { gponOltAutoAuthRuleTable 1 }

		
		GponOltAutoAuthRuleEntry ::=
			SEQUENCE { 
				gponOltAutoAuthRuleId
					GponOltAutoAuthRuleId,
				gponOltAutoAuthRuleRowStatus
					RowStatus,
				gponOltAutoAuthVendorId
					OCTET STRING,
				gponOltAutoAuthEquipmentID
					OCTET STRING,
				gponOltAutoAuthSoftwareVersion
					OCTET STRING,
				gponOltAutoAuthLineProfileId
					GponLinePorfileId,
				gponOltAutoAuthSrvProfileId
					GponSrvProfileId,
				gponOltAutoAuthOnuNumber
					Integer32
			 }

--  *******.4.1.34592.*******.********.1.1
		-- *******.4.1.34592.*******.********.1.2
		gponOltAutoAuthRuleId OBJECT-TYPE
			SYNTAX GponOltAutoAuthRuleId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 2 }

		
		-- *******.4.1.34592.*******.********.1.3
		gponOltAutoAuthRuleRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 3 }

		
--  *******.4.1.34592.*******.********.1.2
		-- *******.4.1.34592.*******.********.1.4
		gponOltAutoAuthVendorId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (4))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 4 }

		
--  *******.4.1.34592.*******.********.1.3
		-- *******.4.1.34592.*******.********.1.5
		gponOltAutoAuthEquipmentID OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..20))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 5 }

		
--  *******.4.1.34592.*******.********.1.4
		-- *******.4.1.34592.*******.********.1.6
		gponOltAutoAuthSoftwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..14))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 6 }

		
		-- *******.4.1.34592.*******.********.1.7
		gponOltAutoAuthLineProfileId OBJECT-TYPE
			SYNTAX GponLinePorfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 7 }

		
		-- *******.4.1.34592.*******.********.1.8
		gponOltAutoAuthSrvProfileId OBJECT-TYPE
			SYNTAX GponSrvProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 8 }

		
		-- *******.4.1.34592.*******.********.1.9
		gponOltAutoAuthOnuNumber OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOltAutoAuthRuleEntry 9 }

		
--  *******.4.1.34592.*******.2.18
		-- *******.4.1.34592.*******.2.18
		gponOnuObjects OBJECT IDENTIFIER ::= { gponControlObjects 18 }

		
--  *******.4.1.34592.*******.2.18.1
		-- *******.4.1.34592.*******.2.18.1
		gponOnuConfigTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuObjects 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuConfigEntry OBJECT-TYPE
			SYNTAX GponOnuConfigEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId }
			::= { gponOnuConfigTable 1 }

		
		GponOnuConfigEntry ::=
			SEQUENCE { 
				gponOnuId
					GponOnuId,
				gponOnuRowStatus
					RowStatus,
				gponOnuAuthMode
					INTEGER,
				gponOnuSn
					DisplayString,
				gponOnuPassword
					DisplayString,
				gponOnuLineProfileId
					GponLinePorfileId,
				gponOnuServiceProfileId
					GponSrvProfileId,
				gponOnuDescription
					DisplayString
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuId OBJECT-TYPE
			SYNTAX GponOnuId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				sn(1),
				password(2),
				sn-and-password(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuSn OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOnuPassword OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 5 }

		
--  *******.4.1.34592.*******.********.6
		-- *******.4.1.34592.*******.********.6
		gponOnuLineProfileId OBJECT-TYPE
			SYNTAX GponLinePorfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 6 }

		
--  *******.4.1.34592.*******.********.7
		-- *******.4.1.34592.*******.********.7
		gponOnuServiceProfileId OBJECT-TYPE
			SYNTAX GponSrvProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 7 }

		
--  *******.4.1.34592.*******.********.8
		-- *******.4.1.34592.*******.********.8
		gponOnuDescription OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuConfigEntry 8 }

		
--  *******.4.1.34592.*******.2.18.2
		-- *******.4.1.34592.*******.2.18.2
		gponOnuInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuObjects 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuInfoEntry OBJECT-TYPE
			SYNTAX GponOnuInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId }
			::= { gponOnuInfoTable 1 }

		
		GponOnuInfoEntry ::=
			SEQUENCE { 
				gponOnuRunState
					INTEGER,
				gponOnuConfigState
					INTEGER,
				gponOnuMatchState
					INTEGER,
				gponOnuDistance
					Integer32,
				gponOnuInfoDescription
					OCTET STRING,
				gponOnuInfoLastUpTime
					DisplayString,
				gponOnuInfoLastDownTime
					DisplayString
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuRunState OBJECT-TYPE
			SYNTAX INTEGER
				{
				online(1),
				offline(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuConfigState OBJECT-TYPE
			SYNTAX INTEGER
				{
				initial(1),
				success(2),
				failed(3),
				configing(4)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuMatchState OBJECT-TYPE
			SYNTAX INTEGER
				{
				initial(1),
				match(2),
				mismatch(3)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuDistance OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuInfoEntry 4 }

		
		-- *******.4.1.34592.*******.********.5
		gponOnuInfoDescription OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..64))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuInfoEntry 5 }

		
		-- *******.4.1.34592.*******.********.6
		gponOnuInfoLastUpTime OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuInfoEntry 6 }

		
		-- *******.4.1.34592.*******.********.7
		gponOnuInfoLastDownTime OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuInfoEntry 7 }

		
--  *******.4.1.34592.*******.2.18.3
		-- *******.4.1.34592.*******.2.18.3
		gponOnuCapabilityInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuCapabilityInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuObjects 3 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuCapabilityInfoEntry OBJECT-TYPE
			SYNTAX GponOnuCapabilityInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId }
			::= { gponOnuCapabilityInfoTable 1 }

		
		GponOnuCapabilityInfoEntry ::=
			SEQUENCE { 
				gponOnuType
					INTEGER,
				gponOnuPonPortNum
					Integer32,
				gponOnuEthPortNum
					Integer32,
				gponOnuVeipPortNum
					Integer32,
				gponOnuPotsPortNum
					Integer32,
				gponOnuCatvPortNum
					Integer32,
				gponOnuGemPortNum
					Integer32,
				gponOnuTcontNum
					Integer32,
				gponOnuFlowControlType
					INTEGER
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuType OBJECT-TYPE
			SYNTAX INTEGER
				{
				sfu(1),
				mdu(2),
				hgu(3),
				sfu-hgu(4),
				mdu-hgu(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.2
		gponOnuPonPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.3
		gponOnuEthPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.4
		gponOnuVeipPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 4 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.5
		gponOnuPotsPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 5 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.6
		gponOnuCatvPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 6 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.7
		gponOnuGemPortNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 7 }

		
--  *******.4.1.34592.*******.********.6
		-- *******.4.1.34592.*******.********.8
		gponOnuTcontNum OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 8 }

		
--  *******.4.1.34592.*******.********.7
		-- *******.4.1.34592.*******.********.9
		gponOnuFlowControlType OBJECT-TYPE
			SYNTAX INTEGER
				{
				pq(0),
				car(1),
				pqAndCar(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCapabilityInfoEntry 9 }

		
--  *******.4.1.34592.*******.2.18.4
		-- *******.4.1.34592.*******.2.18.4
		gponOnuControlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuObjects 4 }

		
--  *******.4.1.34592.*******.2.18.4.1
		-- *******.4.1.34592.*******.2.18.4.1
		gponOnuControlEntry OBJECT-TYPE
			SYNTAX GponOnuControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId }
			::= { gponOnuControlTable 1 }

		
		GponOnuControlEntry ::=
			SEQUENCE { 
				gponOnuReset
					INTEGER,
				gponOnuDeactive
					INTEGER
			 }

--  *******.4.1.34592.*******.2.18.4.1.1
		-- *******.4.1.34592.*******.2.18.4.1.1
		gponOnuReset OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				reset(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuControlEntry 1 }

		
--  *******.4.1.34592.*******.2.18.4.1.2
		-- *******.4.1.34592.*******.2.18.4.1.2
		gponOnuDeactive OBJECT-TYPE
			SYNTAX INTEGER
				{
				active(0),
				deactive(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuControlEntry 2 }

		
--  *******.4.1.34592.*******.2.18.5
		-- *******.4.1.34592.*******.2.18.5
		gponOnuAutofindInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuAutofindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuObjects 5 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuAutofindInfoEntry OBJECT-TYPE
			SYNTAX GponOnuAutofindInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuAutofindId }
			::= { gponOnuAutofindInfoTable 1 }

		
		GponOnuAutofindInfoEntry ::=
			SEQUENCE { 
				gponOnuAutofindId
					Integer32,
				gponOnuAutofindRowStatus
					INTEGER,
				gponOnuAutofindSn
					GponOnuSn,
				gponOnuAutofindPassword
					GponOnuPassword,
				gponOnuAutofindVendorId
					OCTET STRING,
				gponOnuAutofindVersion
					OCTET STRING,
				gponOnuAutofindEquipmentID
					OCTET STRING,
				gponOnuAutofindSoftwareVersion
					OCTET STRING,
				gponOnuAutofindTime
					DisplayString
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuAutofindId OBJECT-TYPE
			SYNTAX Integer32 (1..128)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuAutofindRowStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				active(1),
				destory(6)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuAutofindSn OBJECT-TYPE
			SYNTAX GponOnuSn
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuAutofindPassword OBJECT-TYPE
			SYNTAX GponOnuPassword
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOnuAutofindVendorId OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (4))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 5 }

		
--  *******.4.1.34592.*******.********.6
		-- *******.4.1.34592.*******.********.6
		gponOnuAutofindVersion OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..14))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 6 }

		
--  *******.4.1.34592.*******.********.7
		-- *******.4.1.34592.*******.********.7
		gponOnuAutofindEquipmentID OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..20))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 7 }

		
--  *******.4.1.34592.*******.********.8
		-- *******.4.1.34592.*******.********.8
		gponOnuAutofindSoftwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..14))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 8 }

		
--  *******.4.1.34592.*******.********.9
		-- *******.4.1.34592.*******.********.9
		gponOnuAutofindTime OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAutofindInfoEntry 9 }

		
--  *******.4.1.34592.*******.2.18.6
		-- *******.4.1.34592.*******.2.18.6
		gponOnuOpticalInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuOpticalInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuObjects 6 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuOpticalInfoEntry OBJECT-TYPE
			SYNTAX GponOnuOpticalInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId }
			::= { gponOnuOpticalInfoTable 1 }

		
		GponOnuOpticalInfoEntry ::=
			SEQUENCE { 
				gponOnuOpticalVoltage
					DisplayString,
				gponOnuOpticalTxPower
					DisplayString,
				gponOnuOpticalRxPower
					DisplayString,
				gponOnuOpticalLaserBiasCurrent
					DisplayString,
				gponOnuOpticalTemperature
					DisplayString
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuOpticalVoltage OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuOpticalInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuOpticalTxPower OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuOpticalInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuOpticalRxPower OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuOpticalInfoEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOnuOpticalLaserBiasCurrent OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuOpticalInfoEntry 5 }

		
--  *******.4.1.34592.*******.********.6
		-- *******.4.1.34592.*******.********.6
		gponOnuOpticalTemperature OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuOpticalInfoEntry 6 }

		
--  *******.4.1.34592.*******.2.18.7
		-- *******.4.1.34592.*******.2.18.7
		gponOnuVersionTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuVersionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuObjects 7 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuVersionEntry OBJECT-TYPE
			SYNTAX GponOnuVersionEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId }
			::= { gponOnuVersionTable 1 }

		
		GponOnuVersionEntry ::=
			SEQUENCE { 
				gponOnuMainSoftwareVersion
					DisplayString,
				gponOnuStandbySoftwareVersion
					DisplayString
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuMainSoftwareVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuVersionEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuStandbySoftwareVersion OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuVersionEntry 2 }

		
		-- *******.4.1.34592.*******.2.18.8
		gponOnuAuthenticationManagement OBJECT IDENTIFIER ::= { gponOnuObjects 8 }

		
		-- *******.4.1.34592.*******.********
		gponOnuAuthenticationConfirmTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuAuthenticationConfirmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationManagement 2 }

		
		-- *******.4.1.34592.*******.********.1
		gponOnuAuthenticationConfirmEntry OBJECT-TYPE
			SYNTAX GponOnuAuthenticationConfirmEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOnuAuthenticationConfirmDeviceId, gponOnuAuthenticationConfirmCardId, gponOnuAuthenticationConfirmPortId }
			::= { gponOnuAuthenticationConfirmTable 1 }

		
		GponOnuAuthenticationConfirmEntry ::=
			SEQUENCE { 
				gponOnuAuthenticationConfirmDeviceId
					Integer32,
				gponOnuAuthenticationConfirmCardId
					Integer32,
				gponOnuAuthenticationConfirmPortId
					Integer32,
				gponOnuAuthenConfirmType
					INTEGER,
				gponOnuAuthenConfirmSn
					DisplayString,
				gponOnuAuthenConfirmPassword
					DisplayString,
				gponOnuAuthenConfirmLoid
					GponOnuLoid,
				gponOnuAuthenConfirmLoidPassword
					GponOnuLoidPassword,
				gponOnuAuthenConfirmLineProfileId
					Unsigned32,
				gponOnuAuthenConfirmServiceProfileId
					Unsigned32,
				gponOnuAuthenConfirmTimeMode
					INTEGER,
				gponOnuAuthenConfirmAgingDuration
					Unsigned32,
				gponOnuAuthenticationConfirmRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.********.1.1
		gponOnuAuthenticationConfirmDeviceId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 1 }

		
		-- *******.4.1.34592.*******.********.1.2
		gponOnuAuthenticationConfirmCardId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 2 }

		
		-- *******.4.1.34592.*******.********.1.3
		gponOnuAuthenticationConfirmPortId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 3 }

		
		-- *******.4.1.34592.*******.********.1.4
		gponOnuAuthenConfirmType OBJECT-TYPE
			SYNTAX INTEGER
				{
				snAuth(1),
				snPwdAuth(2),
				pwdAuth(3),
				loidAuth(4),
				loidPwdAuth(5),
				snAuthAll(6),
				snPwdAuthAll(7),
				pwdAuthAll(8),
				loidAuthAll(9),
				loidPwdAuthAll(10)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 4 }

		
		-- *******.4.1.34592.*******.********.1.5
		gponOnuAuthenConfirmSn OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 5 }

		
		-- *******.4.1.34592.*******.********.1.6
		gponOnuAuthenConfirmPassword OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 6 }

		
		-- *******.4.1.34592.*******.********.1.7
		gponOnuAuthenConfirmLoid OBJECT-TYPE
			SYNTAX GponOnuLoid
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 7 }

		
		-- *******.4.1.34592.*******.********.1.8
		gponOnuAuthenConfirmLoidPassword OBJECT-TYPE
			SYNTAX GponOnuLoidPassword
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 8 }

		
		-- *******.4.1.34592.*******.********.1.9
		gponOnuAuthenConfirmLineProfileId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 9 }

		
		-- *******.4.1.34592.*******.********.1.10
		gponOnuAuthenConfirmServiceProfileId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 10 }

		
		-- *******.4.1.34592.*******.********.1.11
		gponOnuAuthenConfirmTimeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				always(0),
				onceAging(1),
				onceNoAging(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 11 }

		
		-- *******.4.1.34592.*******.********.1.12
		gponOnuAuthenConfirmAgingDuration OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 12 }

		
		-- *******.4.1.34592.*******.********.1.13
		gponOnuAuthenticationConfirmRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuAuthenticationConfirmEntry 13 }

		
		-- *******.4.1.34592.*******.********
		gponOnuPolicyAuthTable OBJECT IDENTIFIER ::= { gponOnuAuthenticationManagement 3 }

		
		-- *******.4.1.34592.*******.********.1
		gponOnuPolicyAuthControlTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuPolicyAuthControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthTable 1 }

		
		-- *******.4.1.34592.*******.********.1.1
		gponOnuPolicyAuthControlEntry OBJECT-TYPE
			SYNTAX GponOnuPolicyAuthControlEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOnuPolicyAuthControlDeviceIndex }
			::= { gponOnuPolicyAuthControlTable 1 }

		
		GponOnuPolicyAuthControlEntry ::=
			SEQUENCE { 
				gponOnuPolicyAuthControlDeviceIndex
					Integer32,
				gponOnuPolicyAuthSwitch
					GponSwitch,
				gponOnuPolicyAuthPortSwtichBitMap
					OCTET STRING,
				gponOnuPolicyAuthMode
					INTEGER,
				gponOnuPolicyAuthTargetMode
					INTEGER,
				gponOnuPolicyAuthTimeMode
					INTEGER
			 }

		-- *******.4.1.34592.*******.********.1.1.1
		gponOnuPolicyAuthControlDeviceIndex OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthControlEntry 1 }

		
		-- *******.4.1.34592.*******.********.1.1.2
		gponOnuPolicyAuthSwitch OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthControlEntry 2 }

		
		-- *******.4.1.34592.*******.********.1.1.3
		gponOnuPolicyAuthPortSwtichBitMap OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..16))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthControlEntry 3 }

		
		-- *******.4.1.34592.*******.********.1.1.4
		gponOnuPolicyAuthMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				all(0),
				equid(1),
				vendor(2),
				equidAndSwver(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthControlEntry 4 }

		
		-- *******.4.1.34592.*******.********.1.1.5
		gponOnuPolicyAuthTargetMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				snAuth(0),
				snPasswordAuth(1),
				passwordAuth(2),
				loidAuth(3),
				loidPasswordAuth(4)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthControlEntry 5 }

		
		-- *******.4.1.34592.*******.********.1.1.6
		gponOnuPolicyAuthTimeMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				always(0),
				onceNoAging(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthControlEntry 6 }

		
		-- *******.4.1.34592.*******.********.2
		gponOnuPolicyAuthRuleTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuPolicyAuthRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthTable 2 }

		
		-- *******.4.1.34592.*******.********.2.1
		gponOnuPolicyAuthRuleEntry OBJECT-TYPE
			SYNTAX GponOnuPolicyAuthRuleEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOnuPolicyAuthDeviceIndex, gponOnuPolicyAuthRuleIndex }
			::= { gponOnuPolicyAuthRuleTable 1 }

		
		GponOnuPolicyAuthRuleEntry ::=
			SEQUENCE { 
				gponOnuPolicyAuthDeviceIndex
					Unsigned32,
				gponOnuPolicyAuthRuleIndex
					Unsigned32,
				gponOnuPolicyAuthRuleMatchMode
					INTEGER,
				gponOnuPolicyAuthRuleEquid
					OCTET STRING,
				gponOnuPolicyAuthRuleVendorid
					OCTET STRING,
				gponOnuPolicyAuthRuleSoftwareVersion
					OCTET STRING,
				gponOnuPolicyAuthRuleLineProfileId
					Unsigned32,
				gponOnuPolicyAuthRuleSrvProfileId
					Unsigned32,
				gponOnuPolicyAuthRuleRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.********.2.1.1
		gponOnuPolicyAuthDeviceIndex OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 1 }

		
		-- *******.4.1.34592.*******.********.2.1.2
		gponOnuPolicyAuthRuleIndex OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 2 }

		
		-- *******.4.1.34592.*******.********.2.1.3
		gponOnuPolicyAuthRuleMatchMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				all(0),
				equid(1),
				vendor(2),
				equidAndSwver(3)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 3 }

		
		-- *******.4.1.34592.*******.********.2.1.4
		gponOnuPolicyAuthRuleEquid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..20))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 4 }

		
		-- *******.4.1.34592.*******.********.2.1.5
		gponOnuPolicyAuthRuleVendorid OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (4))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 5 }

		
		-- *******.4.1.34592.*******.********.2.1.6
		gponOnuPolicyAuthRuleSoftwareVersion OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..14))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 6 }

		
		-- *******.4.1.34592.*******.********.2.1.7
		gponOnuPolicyAuthRuleLineProfileId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 7 }

		
		-- *******.4.1.34592.*******.********.2.1.8
		gponOnuPolicyAuthRuleSrvProfileId OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 8 }

		
		-- *******.4.1.34592.*******.********.2.1.9
		gponOnuPolicyAuthRuleRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPolicyAuthRuleEntry 9 }

		
		-- *******.4.1.34592.*******.2.18.9
		gponOnuWanConfig OBJECT IDENTIFIER ::= { gponOnuObjects 9 }

		
		-- *******.4.1.34592.*******.********
		gponOnuWanConfigInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuWanConfigInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfig 1 }

		
		-- *******.4.1.34592.*******.********.1
		gponOnuWanConfigInfoEntry OBJECT-TYPE
			SYNTAX GponOnuWanConfigInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltPortId, gponOnuId, gponOnuWanInfoWanId }
			::= { gponOnuWanConfigInfoTable 1 }

		
		GponOnuWanConfigInfoEntry ::=
			SEQUENCE { 
				gponOnuWanInfoWanId
					Integer32,
				gponOnuWanConfigConnectionName
					DisplayString,
				gponOnuWanConfigConnectionStatus
					INTEGER,
				gponOnuWanConfigConnectionAdmin
					GponSwitch,
				gponOnuWanConfigConnectType
					INTEGER,
				gponOnuWanConfigConnectMode
					INTEGER,
				gponOnuWanConfigIpVersion
					INTEGER,
				gponOnuWanConfigVlanAdmin
					INTEGER,
				gponOnuWanConfigVlan
					Integer32,
				gponOnuWanConfigVlanPriority
					Integer32,
				gponOnuWanConfigServiceType
					INTEGER,
				gponOnuWanConfigMtu
					Integer32,
				gponOnuWanConfigNatAdmin
					GponSwitch,
				gponOnuWanConfigLanDhcpAdmin
					GponSwitch,
				gponOnuWanConfigBinding
					BITS,
				gponOnuWanConfigPppoeUserName
					DisplayString,
				gponOnuWanConfigPppoePassWord
					DisplayString,
				gponOnuWanConfigPppoeServiceName
					DisplayString,
				gponOnuWanConfigPppoeDialOnDemandAdmin
					INTEGER,
				gponOnuWanConfigPppoeDialOnDemandInActiveTime
					Integer32,
				gponOnuWanConfigRowStatus
					RowStatus
			 }

		-- *******.4.1.34592.*******.********.1.1
		gponOnuWanInfoWanId OBJECT-TYPE
			SYNTAX Integer32 (0..32)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 1 }

		
		-- *******.4.1.34592.*******.********.1.2
		gponOnuWanConfigConnectionName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..50))
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 2 }

		
		-- *******.4.1.34592.*******.********.1.3
		gponOnuWanConfigConnectionStatus OBJECT-TYPE
			SYNTAX INTEGER
				{
				down(0),
				up(1)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 3 }

		
		-- *******.4.1.34592.*******.********.1.4
		gponOnuWanConfigConnectionAdmin OBJECT-TYPE
			SYNTAX GponSwitch
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 4 }

		
		-- *******.4.1.34592.*******.********.1.5
		gponOnuWanConfigConnectType OBJECT-TYPE
			SYNTAX INTEGER
				{
				bridge(1),
				route(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 5 }

		
		-- *******.4.1.34592.*******.********.1.6
		gponOnuWanConfigConnectMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				dhcp(1),
				static(2),
				pppoe(3),
				pppoebridge(4),
				pppoemix(5)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 6 }

		
		-- *******.4.1.34592.*******.********.1.7
		gponOnuWanConfigIpVersion OBJECT-TYPE
			SYNTAX INTEGER
				{
				ipv4(1),
				ipv6(2),
				ipv4-ipv6(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 7 }

		
		-- *******.4.1.34592.*******.********.1.8
		gponOnuWanConfigVlanAdmin OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 8 }

		
		-- *******.4.1.34592.*******.********.1.9
		gponOnuWanConfigVlan OBJECT-TYPE
			SYNTAX Integer32 (1..4094)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 9 }

		
		-- *******.4.1.34592.*******.********.1.10
		gponOnuWanConfigVlanPriority OBJECT-TYPE
			SYNTAX Integer32 (0..7)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 10 }

		
		-- *******.4.1.34592.*******.********.1.11
		gponOnuWanConfigServiceType OBJECT-TYPE
			SYNTAX INTEGER
				{
				internet(1),
				voip(2),
				internet-voip(3),
				tr069(4),
				internet-tr069(5),
				voip-tr069(6),
				internet-voip-tr069(7),
				other(8)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 11 }

		
		-- *******.4.1.34592.*******.********.1.12
		gponOnuWanConfigMtu OBJECT-TYPE
			SYNTAX Integer32 (64..1540)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 12 }

		
		-- *******.4.1.34592.*******.********.1.13
		gponOnuWanConfigNatAdmin OBJECT-TYPE
			SYNTAX GponSwitch
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 13 }

		
		-- *******.4.1.34592.*******.********.1.14
		gponOnuWanConfigLanDhcpAdmin OBJECT-TYPE
			SYNTAX GponSwitch
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 14 }

		
		-- *******.4.1.34592.*******.********.1.15
		gponOnuWanConfigBinding OBJECT-TYPE
			SYNTAX BITS
				{
				port1(0),
				port2(1),
				port3(2),
				port4(3),
				ssid1(8),
				ssid2(9),
				ssid3(10),
				ssid4(11)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 15 }

		
		-- *******.4.1.34592.*******.********.1.16
		gponOnuWanConfigPppoeUserName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..50))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 16 }

		
		-- *******.4.1.34592.*******.********.1.17
		gponOnuWanConfigPppoePassWord OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..50))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 17 }

		
		-- *******.4.1.34592.*******.********.1.18
		gponOnuWanConfigPppoeServiceName OBJECT-TYPE
			SYNTAX DisplayString (SIZE (1..50))
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 18 }

		
		-- *******.4.1.34592.*******.********.1.19
		gponOnuWanConfigPppoeDialOnDemandAdmin OBJECT-TYPE
			SYNTAX INTEGER
				{
				disable(0),
				enable(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 19 }

		
		-- *******.4.1.34592.*******.********.1.20
		gponOnuWanConfigPppoeDialOnDemandInActiveTime OBJECT-TYPE
			SYNTAX Integer32 (1..86400)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 20 }

		
		-- *******.4.1.34592.*******.********.1.21
		gponOnuWanConfigRowStatus OBJECT-TYPE
			SYNTAX RowStatus
				{
				active(1),
				notInService(2),
				notReady(3),
				createAndGo(4),
				createAndWait(5),
				destroy(6)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigInfoEntry 21 }

		
		-- *******.4.1.34592.*******.********
		gponOnuWanConfigIpv4Table OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuWanConfigIpv4Entry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfig 3 }

		
		-- *******.4.1.34592.*******.********.1
		gponOnuWanConfigIpv4Entry OBJECT-TYPE
			SYNTAX GponOnuWanConfigIpv4Entry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltPortId, gponOnuId, gponOnuWanInfoWanId }
			::= { gponOnuWanConfigIpv4Table 1 }

		
		GponOnuWanConfigIpv4Entry ::=
			SEQUENCE { 
				gponOnuWanConfigIpAddress
					IpAddress,
				gponOnuWanConfigMask
					IpAddress,
				gponOnuWanConfigGateWay
					IpAddress,
				gponOnuWanConfigDhcpDnsMode
					INTEGER,
				gponOnuWanConfigPrimaryDns
					IpAddress,
				gponOnuWanConfigSecondaryDns
					IpAddress
			 }

		-- *******.4.1.34592.*******.********.1.1
		gponOnuWanConfigIpAddress OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigIpv4Entry 1 }

		
		-- *******.4.1.34592.*******.********.1.2
		gponOnuWanConfigMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigIpv4Entry 2 }

		
		-- *******.4.1.34592.*******.********.1.3
		gponOnuWanConfigGateWay OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigIpv4Entry 3 }

		
		-- *******.4.1.34592.*******.********.1.4
		gponOnuWanConfigDhcpDnsMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				manual(0),
				autoget(1)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigIpv4Entry 4 }

		
		-- *******.4.1.34592.*******.********.1.5
		gponOnuWanConfigPrimaryDns OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigIpv4Entry 5 }

		
		-- *******.4.1.34592.*******.********.1.6
		gponOnuWanConfigSecondaryDns OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuWanConfigIpv4Entry 6 }

		
--  *******.4.1.34592.*******.2.19
		-- *******.4.1.34592.*******.2.19
		gponOnuEthPortObjects OBJECT IDENTIFIER ::= { gponControlObjects 19 }

		
--  *******.4.1.34592.*******.2.19.1
		-- *******.4.1.34592.*******.2.19.1
		gponOnuEthPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuEthPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortObjects 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuEthPortCfgEntry OBJECT-TYPE
			SYNTAX GponOnuEthPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuEthPortId
				 }
			::= { gponOnuEthPortCfgTable 1 }

		
		GponOnuEthPortCfgEntry ::=
			SEQUENCE { 
				gponOnuEthPortId
					GponOnuEthPortId,
				gponOnuEthPortNativeVlanId
					GponVlanId,
				gponOnuEthPortNativeVlanPriority
					GponVlanPriority,
				gponOnuEthPortInboundCarId
					Integer32,
				gponOnuEthPortOutboundCarId
					Integer32,
				gponOnuEthPortSpeedAndDuplex
					INTEGER,
				gponOnuEthPortFlowCtrl
					GponSwitch,
				gponOnuEthPortOperationalState
					GponSwitch
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuEthPortId OBJECT-TYPE
			SYNTAX GponOnuEthPortId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuEthPortNativeVlanId OBJECT-TYPE
			SYNTAX GponVlanId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuEthPortNativeVlanPriority OBJECT-TYPE
			SYNTAX GponVlanPriority
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuEthPortInboundCarId OBJECT-TYPE
			SYNTAX Integer32 (1..256 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOnuEthPortOutboundCarId OBJECT-TYPE
			SYNTAX Integer32 (1..256 | 65535)
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 5 }

		
--  *******.4.1.34592.*******.********.6
		-- *******.4.1.34592.*******.********.6
		gponOnuEthPortSpeedAndDuplex OBJECT-TYPE
			SYNTAX INTEGER
				{
				autoNeg(0),
				full10(1),
				full100(2),
				full1000(3),
				half10(17),
				half100(18),
				half1000(19)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 6 }

		
--  *******.4.1.34592.*******.********.8
		-- *******.4.1.34592.*******.********.8
		gponOnuEthPortFlowCtrl OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 8 }

		
--  *******.4.1.34592.*******.********.9
		-- *******.4.1.34592.*******.********.9
		gponOnuEthPortOperationalState OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortCfgEntry 9 }

		
--  *******.4.1.34592.*******.2.19.2
		-- *******.4.1.34592.*******.2.19.2
		gponOnuEthPortInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuEthPortInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortObjects 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuEthPortInfoEntry OBJECT-TYPE
			SYNTAX GponOnuEthPortInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuEthPortId
				 }
			::= { gponOnuEthPortInfoTable 1 }

		
		GponOnuEthPortInfoEntry ::=
			SEQUENCE { 
				gponOnuEthPortSpeedAndDuplexInfo
					INTEGER,
				gponOnuEthPortState
					INTEGER
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuEthPortSpeedAndDuplexInfo OBJECT-TYPE
			SYNTAX INTEGER
				{
				full10(1),
				full100(2),
				full1000(3),
				half10(17),
				half100(18),
				half1000(19)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuEthPortState OBJECT-TYPE
			SYNTAX INTEGER
				{
				up(1),
				down(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortInfoEntry 2 }

		
--  *******.4.1.34592.*******.2.19.3
		-- *******.4.1.34592.*******.2.19.3
		gponOnuEthPortMacAddressTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuEthPortMacAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortObjects 3 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuEthPortMacAddressEntry OBJECT-TYPE
			SYNTAX GponOnuEthPortMacAddressEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuEthPortId, 
				gponOnuEthPortMacAddressId }
			::= { gponOnuEthPortMacAddressTable 1 }

		
		GponOnuEthPortMacAddressEntry ::=
			SEQUENCE { 
				gponOnuEthPortMacAddressId
					Integer32,
				gponOnuEthPortMacAddress
					GponMac
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuEthPortMacAddressId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortMacAddressEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuEthPortMacAddress OBJECT-TYPE
			SYNTAX GponMac
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortMacAddressEntry 2 }

		
--  *******.4.1.34592.*******.2.19.2
		-- *******.4.1.34592.*******.2.19.5
		gponOnuEthPortRTInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuEthPortRTInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortObjects 5 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuEthPortRTInfoEntry OBJECT-TYPE
			SYNTAX GponOnuEthPortRTInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuEthPortId
				 }
			::= { gponOnuEthPortRTInfoTable 1 }

		
		GponOnuEthPortRTInfoEntry ::=
			SEQUENCE { 
				gponOnuEthPortStatusInfo
					OCTET STRING
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuEthPortStatusInfo OBJECT-TYPE
			SYNTAX OCTET STRING
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuEthPortRTInfoEntry 1 }

		
--  *******.4.1.34592.*******.2.20
		-- *******.4.1.34592.*******.2.20
		gponOnuPotsPortObjects OBJECT IDENTIFIER ::= { gponControlObjects 20 }

		
--  *******.4.1.34592.*******.2.20.1
		-- *******.4.1.34592.*******.2.20.1
		gponOnuPotsPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuPotsPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPotsPortObjects 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuPotsPortCfgEntry OBJECT-TYPE
			SYNTAX GponOnuPotsPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuPotsPortId
				 }
			::= { gponOnuPotsPortCfgTable 1 }

		
		GponOnuPotsPortCfgEntry ::=
			SEQUENCE { 
				gponOnuPotsPortId
					GponOnuPotsPortId,
				gponOnuPotsPortOperationalState
					GponSwitch,
				gponOnuPotsPortCfgPotsProfileId
					GponPotsProfileId
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuPotsPortId OBJECT-TYPE
			SYNTAX GponOnuPotsPortId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPotsPortCfgEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuPotsPortOperationalState OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPotsPortCfgEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuPotsPortCfgPotsProfileId OBJECT-TYPE
			SYNTAX GponPotsProfileId
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 65535 }
			::= { gponOnuPotsPortCfgEntry 3 }

		
--  *******.4.1.34592.*******.2.20.2
		-- *******.4.1.34592.*******.2.20.2
		gponOnuIpCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuIpCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPotsPortObjects 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuIpCfgEntry OBJECT-TYPE
			SYNTAX GponOnuIpCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuIpHostId
				 }
			::= { gponOnuIpCfgTable 1 }

		
		GponOnuIpCfgEntry ::=
			SEQUENCE { 
				gponOnuIpHostId
					Integer32,
				gponOnuIpCfgRowStatus
					RowStatus,
				gponOnuIpCfgMode
					INTEGER,
				gponOnuIpCfgVlanId
					Integer32,
				gponOnuIpCfgVlanPri
					Integer32,
				gponOnuIpCfgIp
					IpAddress,
				gponOnuIpCfgMask
					IpAddress,
				gponOnuIpCfgGateway
					IpAddress,
				gponOnuIpCfgPriDns
					IpAddress,
				gponOnuIpCfgSlaveDns
					IpAddress,
				gponOnuIpCfgMacAddress
					GponMac
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuIpHostId OBJECT-TYPE
			SYNTAX Integer32 (1..2)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuIpCfgRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuIpCfgMode OBJECT-TYPE
			SYNTAX INTEGER
				{
				dhcp(1),
				static(2)
				}
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuIpCfgVlanId OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOnuIpCfgVlanPri OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 5 }

		
--  *******.4.1.34592.*******.********.6
		-- *******.4.1.34592.*******.********.6
		gponOnuIpCfgIp OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 6 }

		
--  *******.4.1.34592.*******.********.7
		-- *******.4.1.34592.*******.********.7
		gponOnuIpCfgMask OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 7 }

		
--  *******.4.1.34592.*******.********.8
		-- *******.4.1.34592.*******.********.8
		gponOnuIpCfgGateway OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 8 }

		
--  *******.4.1.34592.*******.********.9
		-- *******.4.1.34592.*******.********.9
		gponOnuIpCfgPriDns OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 9 }

		
--  *******.4.1.34592.*******.********.10
		-- *******.4.1.34592.*******.********.10
		gponOnuIpCfgSlaveDns OBJECT-TYPE
			SYNTAX IpAddress
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 10 }

		
--  *******.4.1.34592.*******.********.11
		-- *******.4.1.34592.*******.********.11
		gponOnuIpCfgMacAddress OBJECT-TYPE
			SYNTAX GponMac
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuIpCfgEntry 11 }

		
--  *******.4.1.34592.*******.2.20.3
		-- *******.4.1.34592.*******.2.20.3
		gponOnuSipPstnUserTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuSipPstnUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPotsPortObjects 3 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuSipPstnUserEntry OBJECT-TYPE
			SYNTAX GponOnuSipPstnUserEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuSipPstnUserId
				 }
			::= { gponOnuSipPstnUserTable 1 }

		
		GponOnuSipPstnUserEntry ::=
			SEQUENCE { 
				gponOnuSipPstnUserId
					GponOnuPotsPortId,
				gponOnuSipPstnUserRowStatus
					RowStatus,
				gponOnuSipPstnUserName
					OCTET STRING,
				gponOnuSipPstnUserPassword
					OCTET STRING,
				gponOnuSipPstnUserTelephoneNumber
					OCTET STRING,
				gponOnuSipPstnUserAgentProfileId
					GponSipAgentProfileId,
				gponOnuSipPstnUserDigitMapProfileId
					GponDigitMapProfileId,
				gponOnuSipPstnUserSipRightFlagProfileId
					GponSipRightFlagProfileId
			 }

--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuSipPstnUserId OBJECT-TYPE
			SYNTAX GponOnuPotsPortId
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuSipPstnUserRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuSipPstnUserName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..24))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOnuSipPstnUserPassword OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..24))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserEntry 5 }

		
--  *******.4.1.34592.*******.********.6
		-- *******.4.1.34592.*******.********.6
		gponOnuSipPstnUserTelephoneNumber OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..31))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserEntry 6 }

		
--  *******.4.1.34592.*******.********.7
		-- *******.4.1.34592.*******.********.7
		gponOnuSipPstnUserAgentProfileId OBJECT-TYPE
			SYNTAX GponSipAgentProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 65535 }
			::= { gponOnuSipPstnUserEntry 7 }

		
--  *******.4.1.34592.*******.********.8
		-- *******.4.1.34592.*******.********.8
		gponOnuSipPstnUserDigitMapProfileId OBJECT-TYPE
			SYNTAX GponDigitMapProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 65535 }
			::= { gponOnuSipPstnUserEntry 8 }

		
--  *******.4.1.34592.*******.********.9
		-- *******.4.1.34592.*******.********.9
		gponOnuSipPstnUserSipRightFlagProfileId OBJECT-TYPE
			SYNTAX GponSipRightFlagProfileId
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			DEFVAL { 65535 }
			::= { gponOnuSipPstnUserEntry 9 }

		
--  *******.4.1.34592.*******.2.20.4
		-- *******.4.1.34592.*******.2.20.4
		gponOnuSipPstnUserInfoTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuSipPstnUserInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuPotsPortObjects 4 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuSipPstnUserInfoEntry OBJECT-TYPE
			SYNTAX GponOnuSipPstnUserInfoEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuSipPstnUserId
				 }
			::= { gponOnuSipPstnUserInfoTable 1 }

		
		GponOnuSipPstnUserInfoEntry ::=
			SEQUENCE { 
				gponOnuSipPstnUserState
					INTEGER,
				gponOnuSipPstnUserHookState
					INTEGER,
				gponOnuSipPstnUserCodec
					DisplayString,
				gponOnuSipPstnUserServerStatus
					DisplayString,
				gponOnuSipPstnUserSessionType
					DisplayString
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuSipPstnUserState OBJECT-TYPE
			SYNTAX INTEGER
				{
				lock(1),
				unlock(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserInfoEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuSipPstnUserHookState OBJECT-TYPE
			SYNTAX INTEGER
				{
				onhook(1),
				offhook(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserInfoEntry 2 }

		
--  *******.4.1.34592.*******.********.3
		-- *******.4.1.34592.*******.********.3
		gponOnuSipPstnUserCodec OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserInfoEntry 3 }

		
--  *******.4.1.34592.*******.********.4
		-- *******.4.1.34592.*******.********.4
		gponOnuSipPstnUserServerStatus OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserInfoEntry 4 }

		
--  *******.4.1.34592.*******.********.5
		-- *******.4.1.34592.*******.********.5
		gponOnuSipPstnUserSessionType OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuSipPstnUserInfoEntry 5 }

		
--  *******.4.1.34592.*******.2.21
		-- *******.4.1.34592.*******.2.21
		gponOnuCatvPortObjects OBJECT IDENTIFIER ::= { gponControlObjects 21 }

		
--  *******.4.1.34592.*******.2.21.1
		-- *******.4.1.34592.*******.2.21.1
		gponOnuCatvPortCfgTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponOnuCatvPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCatvPortObjects 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponOnuCatvPortCfgEntry OBJECT-TYPE
			SYNTAX GponOnuCatvPortCfgEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponOltDeviceId, gponOltCardId, gponOltPortId, gponOnuId, gponOnuCatvPortId
				 }
			::= { gponOnuCatvPortCfgTable 1 }

		
		GponOnuCatvPortCfgEntry ::=
			SEQUENCE { 
				gponOnuCatvPortId
					Integer32,
				gponOnuCatvPortOperationalState
					GponSwitch
			 }

--  *******.4.1.34592.*******.********.1
		-- *******.4.1.34592.*******.********.1
		gponOnuCatvPortId OBJECT-TYPE
			SYNTAX Integer32 (1..8)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCatvPortCfgEntry 1 }

		
--  *******.4.1.34592.*******.********.2
		-- *******.4.1.34592.*******.********.2
		gponOnuCatvPortOperationalState OBJECT-TYPE
			SYNTAX GponSwitch
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponOnuCatvPortCfgEntry 2 }

		
--  *******.4.1.34592.*******.4
		-- *******.4.1.34592.*******.4
		gponAlarmObjects OBJECT IDENTIFIER ::= { gponObjects 4 }

		
--  *******.4.1.34592.*******.4.2
		-- *******.4.1.34592.*******.4.1
		gponNotifications OBJECT IDENTIFIER ::= { gponAlarmObjects 1 }

		
		-- *******.4.1.34592.*******.4.1.1
		gponAlarmNotification NOTIFICATION-TYPE
			OBJECTS { gponTrapId, gponTrapState, gponTrapOltPortId, gponTrapOnuId, gponTrapOnuSn, 
				gponTrapOnuPassword, gponTrapRogueOnuSn, gponTrapParameters }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponNotifications 1 }

		
--  *******.4.1.34592.*******.4.1
		-- *******.4.1.34592.*******.4.6
		gponTrapObjects OBJECT IDENTIFIER ::= { gponAlarmObjects 6 }

		
		-- *******.4.1.34592.*******.4.6.1
		gponTrapId OBJECT-TYPE
			SYNTAX INTEGER
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 1 }

		
		-- *******.4.1.34592.*******.4.6.2
		gponTrapState OBJECT-TYPE
			SYNTAX INTEGER
				{
				none(0),
				report(1),
				clear(2)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 2 }

		
		-- *******.4.1.34592.*******.4.6.3
		gponTrapParameters OBJECT-TYPE
			SYNTAX BITS
				{
				none(0),
				oltPortId(1),
				onuId(2),
				onuSn(3),
				password(4),
				rogueOnuSn(5)
				}
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 3 }

		
--  *******.4.1.34592.*******.4.1.3
		-- *******.4.1.34592.*******.4.6.4
		gponTrapOltPortId OBJECT-TYPE
			SYNTAX Integer32 (1..8 | 65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 4 }

		
--  *******.4.1.34592.*******.4.1.4
		-- *******.4.1.34592.*******.4.6.5
		gponTrapOnuId OBJECT-TYPE
			SYNTAX Integer32 (1..128 | 65535)
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 5 }

		
--  *******.4.1.34592.*******.4.1.5
		-- *******.4.1.34592.*******.4.6.6
		gponTrapOnuSn OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 6 }

		
		-- *******.4.1.34592.*******.4.6.7
		gponTrapOnuPassword OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 7 }

		
		-- *******.4.1.34592.*******.4.6.8
		gponTrapRogueOnuSn OBJECT-TYPE
			SYNTAX DisplayString
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponTrapObjects 8 }

		
		-- *******.4.1.34592.*******.4.8
		gponManagementObjects OBJECT IDENTIFIER ::= { gponAlarmObjects 8 }

		
		-- *******.4.1.34592.*******.4.8.1
		gponManagementAddrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF GponManagementAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponManagementObjects 1 }

		
		-- *******.4.1.34592.*******.4.8.1.1
		gponManagementAddrEntry OBJECT-TYPE
			SYNTAX GponManagementAddrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { gponManagementAddrName }
			::= { gponManagementAddrTable 1 }

		
		GponManagementAddrEntry ::=
			SEQUENCE { 
				gponManagementAddrName
					OCTET STRING,
				gponManagementAddrRowStatus
					RowStatus,
				gponManagementAddrTAddress
					OCTET STRING,
				gponManagementAddrCommunity
					OCTET STRING
			 }

		-- *******.4.1.34592.*******.4.8.1.1.1
		gponManagementAddrName OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (1..32))
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponManagementAddrEntry 1 }

		
		-- *******.4.1.34592.*******.4.8.1.1.2
		gponManagementAddrRowStatus OBJECT-TYPE
			SYNTAX RowStatus
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponManagementAddrEntry 2 }

		
		-- *******.4.1.34592.*******.4.8.1.1.3
		gponManagementAddrTAddress OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (8))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponManagementAddrEntry 3 }

		
		-- *******.4.1.34592.*******.4.8.1.1.4
		gponManagementAddrCommunity OBJECT-TYPE
			SYNTAX OCTET STRING (SIZE (0..64))
			MAX-ACCESS read-create
			STATUS current
			DESCRIPTION
				"Description."
			::= { gponManagementAddrEntry 4 }

		
--  *******.4.1.34592.*******.64
		-- *******.4.1.34592.*******.64
		gponConformance OBJECT IDENTIFIER ::= { gponObjects 64 }

		
--  *******.4.1.34592.*******.64.1
		-- *******.4.1.34592.*******.64.1
		gponCompliances OBJECT IDENTIFIER ::= { gponConformance 1 }

		
--  *******.4.1.34592.*******.64.1.1
-- this module
		-- *******.4.1.34592.*******.64.1.1
		gponCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"Description."
			MODULE -- this module
				MANDATORY-GROUPS { gponDbaProfileGroup, gponLineProfileGroup, gponSrvProfileGroup, gponTrafficProfileGroup, gponSipAgentProfileGroup, 
					gponSipRightFlagProfileGroup, gponDigitMapProfileGroup, gponPotsProfileGroup }
			::= { gponCompliances 1 }

		
--  *******.4.1.34592.*******.64.2
		-- *******.4.1.34592.*******.64.2
		gponGroups OBJECT IDENTIFIER ::= { gponConformance 2 }

		
--  *******.4.1.34592.*******.64.2.1
		-- *******.4.1.34592.*******.64.2.1
		gponProfileGroups OBJECT IDENTIFIER ::= { gponGroups 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponDbaProfileGroup OBJECT-GROUP
			OBJECTS { gponDbaProfileFixRate, gponDbaProfileBindNum, gponDbaProfileName, gponDbaProfileRowStatus, gponDbaProfileMaxRate, 
				gponDbaProfileType, gponDbaProfileAssureRate }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponLineProfileGroup OBJECT-GROUP
			OBJECTS { gponLineProfileRowStatus, gponLineProfileName, gponLineProfileMappingMode, gponLineProfileTcontNum, gponLineProfileGemNum, 
				gponLineProfileBindNum, gponLineProfileTcontId, gponLineProfileTcontRowStatus, gponLineProfileTcontDbaProfileId, gponLineProfileGemId, 
				gponLineProfileGemRowStatus, gponLineProfileGemTcontId, gponLineProfileGemUpCar, gponLineProfileGemDownCar, gponLineProfileGemMapNum, 
				gponLineProfileGemMapId, gponLineProfileGemMapRowStatus, gponLineProfileGemMapPriority, gponLineProfileGemMapVlan }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponSrvProfileGroup OBJECT-GROUP
			OBJECTS { gponSrvProfileId, gponSrvProfileRowStatus, gponSrvProfileName, gponSrvProfileBindNum, gponSrvProfileMcMode, 
				gponSrvProfileUpIgmpFwdMode, gponSrvProfileUpIgmpTCI, gponSrvProfileDnMcMode, gponSrvProfileDnMcTCI, gponSrvProfileEthNum, 
				gponSrvProfilePotsNum, gponSrvProfilePortType, gponSrvProfilePortId, gponSrvProfilePortCvlan, gponSrvProfilePortVlanRowStatus, 
				gponSrvProfilePortVlanMode, gponSrvProfilePortVlanSvlan }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 3 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponTrafficProfileGroup OBJECT-GROUP
			OBJECTS { gponTrafficProfileId, gponTrafficProfileRowStatus, gponTrafficProfileName, gponTrafficProfileBindNum, gponTrafficProfileCfgCir, 
				gponTrafficProfileCfgPir, gponTrafficProfileCfgCbs, gponTrafficProfileCfgPbs }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 4 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponSipAgentProfileGroup OBJECT-GROUP
			OBJECTS { gponSipAgentProfileRowStatus, gponSipAgentProfileName, gponSipAgentProfileBindNum, gponSipAgentProfileProxyServerUri, gponSipAgentProfileRtpDscp, 
				gponSipAgentProfileRtpMinPort, gponSipAgentProfileRtpMaxPort, gponSipAgentProfileSignalDscp, gponSipAgentProfileSignalPort, gponSipAgentProfileSignalTransferMode, 
				gponSipAgentProfileRegistrationExpiration, gponSipAgentProfileRegistrationReregHeadStartTime, gponSipAgentProfileRegistrationServerUri, gponSipAgentProfileVoiceMailServerUri, gponSipAgentProfileVoiceMailSubscriptionExpiration, 
				gponSipAgentProfileConfFactory, gponSipAgentProfileBridgedLineAgent, gponSipAgentProfileAuthRealm, gponSipAgentProfileBindPotsList }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 5 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponSipRightFlagProfileGroup OBJECT-GROUP
			OBJECTS { gponSipRightFlagProfileId, gponSipRightFlagProfileRowStatus, gponSipRightFlagProfileName, gponSipRightFlagProfileBindNum, gponSipRightFlagProfileCallWaiting, 
				gponSipRightFlagProfileCallProcess, gponSipRightFlagProfileCallPresentation, gponSipRightFlagProfileHotline, gponSipRightFlagProfileHotlineNum, gponSipRightFlagProfileBindPotsList
				 }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 6 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponDigitMapProfileGroup OBJECT-GROUP
			OBJECTS { gponDigitMapProfileId, gponDigitMapProfileRowStatus, gponDigitMapProfileName, gponDigitMapProfileBindNum, gponDigitMapProfileCriticalDialTime, 
				gponDigitMapProfileDialPlanId, gponDigitMapProfileDialPlanRowStatus, gponDigitMapProfileDialPlanToken, gponDigitMapProfileBindPotsList }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 7 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponPotsProfileGroup OBJECT-GROUP
			OBJECTS { gponPotsProfileId, gponPotsProfileRowStatus, gponPotsProfileName, gponPotsProfileBindNum, gponPotsProfileImpedance, 
				gponPotsProfileSignallingCode, gponPotsProfileRxGain, gponPotsProfileTxGain, gponPotsProfileBindPotsList }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponProfileGroups 8 }

		
--  *******.4.1.34592.*******.64.2.2
		-- *******.4.1.34592.*******.64.2.2
		gponControlGroups OBJECT IDENTIFIER ::= { gponGroups 2 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponControlGroup OBJECT-GROUP
			OBJECTS { gponOltPortAutoFindSwitch, gponOltPortSwitch, gponOltPortStatus, gponOltPortDdmTemperature, gponOltPortDdmVoltage, 
				gponOltPortDdmTxBiasCurrent, gponOltPortDdmTxPower, gponOltPortDdmRxPower, gponOltTransceiverVendor, gponOltTransceiverProductName, 
				gponOltTransceiverVersion, gponOltTransceiverSerialNumber, gponOnuId, gponOnuRowStatus, gponOnuAuthMode, 
				gponOnuSn, gponOnuPassword, gponOnuLineProfileId, gponOnuServiceProfileId, gponOnuDescription, 
				gponOnuRunState, gponOnuConfigState, gponOnuMatchState, gponOnuDistance, gponOnuPonPortNum, 
				gponOnuEthPortNum, gponOnuPotsPortNum, gponOnuGemPortNum, gponOnuTcontNum, gponOnuFlowControlType, 
				gponOnuReset, gponOnuDeactive, gponOnuAutofindRowStatus, gponOnuAutofindSn, gponOnuAutofindPassword, 
				gponOnuAutofindVendorId, gponOnuAutofindVersion, gponOnuAutofindEquipmentID, gponOnuAutofindSoftwareVersion, gponOnuAutofindTime, 
				gponOnuOpticalVoltage, gponOnuOpticalTxPower, gponOnuOpticalRxPower, gponOnuOpticalLaserBiasCurrent, gponOnuOpticalTemperature, 
				gponOnuMainSoftwareVersion, gponOnuStandbySoftwareVersion, gponOnuEthPortId, gponOnuEthPortNativeVlanId, gponOnuEthPortNativeVlanPriority, 
				gponOnuEthPortInboundCarId, gponOnuEthPortOutboundCarId, gponOnuEthPortSpeedAndDuplex, gponOnuEthPortFlowCtrl, gponOnuEthPortOperationalState, 
				gponOnuEthPortSpeedAndDuplexInfo, gponOnuEthPortState, gponOnuEthPortMacAddressId, gponOnuEthPortMacAddress, gponOnuPotsPortId, 
				gponOnuPotsPortOperationalState, gponOnuPotsPortCfgPotsProfileId, gponOnuIpCfgRowStatus, gponOnuIpCfgMode, gponOnuIpCfgVlanId, 
				gponOnuIpCfgVlanPri, gponOnuIpCfgIp, gponOnuIpCfgMask, gponOnuIpCfgGateway, gponOnuIpCfgPriDns, 
				gponOnuIpCfgSlaveDns, gponOnuIpCfgMacAddress, gponOnuSipPstnUserRowStatus, gponOnuSipPstnUserName, gponOnuSipPstnUserPassword, 
				gponOnuSipPstnUserTelephoneNumber, gponOnuSipPstnUserAgentProfileId, gponOnuSipPstnUserDigitMapProfileId, gponOnuSipPstnUserSipRightFlagProfileId, gponOnuSipPstnUserState, 
				gponOnuSipPstnUserHookState, gponOnuSipPstnUserCodec, gponOnuSipPstnUserServerStatus, gponOnuSipPstnUserSessionType }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponControlGroups 1 }

		
--  *******.4.1.34592.*******.64.2.4
		-- *******.4.1.34592.*******.64.2.4
		gponTrapGroups OBJECT IDENTIFIER ::= { gponGroups 4 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponTrapGroup OBJECT-GROUP
			OBJECTS { gponTrapOltPortId, gponTrapOnuId, gponTrapOnuSn }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponTrapGroups 1 }

		
--  *******.4.1.34592.*******.********
		-- *******.4.1.34592.*******.********
		gponTrapTypeGroup NOTIFICATION-GROUP
		NOTIFICATIONS { gponOltAlarmLosTrap, gponOltAlarmLosResumeTrap, gponOnuAutofindTrap, gponOnuOnlineTrap, gponOnuOfflineTrap, 
				gponOnuDyingGasp }
			STATUS current
			DESCRIPTION 
				"Description."
			::= { gponTrapGroups 2 }

		
	
	END

--
-- CDATA-GPON-MIB_180930.mib
--
