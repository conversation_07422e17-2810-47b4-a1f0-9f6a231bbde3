--
-- FD-PERFORMANCE-MIB.my
-- MIB generated by MG-<PERSON>O<PERSON> Visual MIB Builder Version 6.0  Build 88
-- Tuesday, July 14, 2015 at 17:00:02
--

--  FD-PERFORMANCE-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Monday, July 13, 2015 at 20:14:55
-- 
--  FD-PERFORMANCE-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, May 22, 2015 at 13:52:29
-- 
--  FD-PERFORMANCE-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 6.0  Build 88
-- Friday, May 15, 2015 at 11:32:44
-- 
--  FD-PERFORMANCE-MIB.my
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Wednesday, June 15, 2011 at 18:07:32
-- 
--  FD-PERFORMANCE-MIB.mib
-- MIB generated by MG-SOFT Visual MIB Builder Version 3.0 Build 253
-- Thursday, May 27, 2010 at 18:36:40
-- 

	FD-PERFORMANCE-MIB DEFINITIONS ::= BEGIN
 
		IMPORTS
			mediaConverter, epon			
				FROM EPON-EOC-MIB			
			oltId, directionId, linkId			
				FROM FD-OLT-MIB			
			onuId, uniPortId			
				FROM FD-ONU-MIB			
			swSniPortId			
				FROM FD-SWITCH-MIB			
			ponCardSlotId			
				FROM FD-SYSTEM-MIB			
			OBJECT-GROUP, MODULE-COMPLIANCE			
				FROM SNMPv2-CONF			
			Integer32, Unsigned32, Counter32, Counter64, OBJECT-TYPE, 
			MODULE-IDENTITY			
				FROM SNMPv2-SMI			
			TEXTUAL-CONVENTION			
				FROM SNMPv2-TC;
	
	
-- *******.4.1.34592.1.3.5
		performance MODULE-IDENTITY 
			LAST-UPDATED "201505221351Z"		-- May 22, 2015 at 13:51 GMT
			ORGANIZATION 
				"epon eoc factory."
			CONTACT-INFO 
				" "
			DESCRIPTION 
				"Performance mib module"
			::= { epon 5 }

		
	
--
-- Textual conventions
--

		StatsCollection ::= TEXTUAL-CONVENTION
			STATUS current
			DESCRIPTION 
				"Description."
			SYNTAX INTEGER
				{
				octetTrans(1),
				totFrameTrans(2),
				ucFrameTrans(3),
				bcFrameTrans(4),
				mcFrameTrans(5),
				crc32Errors(6),
				undersizeFrames(7),
				oversizeFrames(8),
				framesDropped(9),
				octetsDropped(10),
				bandwidth(11)
				}

	
--
-- Node definitions
--
	
-- *******.4.1.34592.*******
		alarmThreshHold OBJECT IDENTIFIER ::= { performance 1 }

		
--  *******.4.1.34592.*******.1
		swThresholdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SwThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { alarmThreshHold 1 }

		
--  *******.4.1.34592.*******.1.1
		swThresholdEntry OBJECT-TYPE
			SYNTAX SwThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { statsId }
			::= { swThresholdTable 1 }

		
		SwThresholdEntry ::=
			SEQUENCE { 
				statsId
					StatsCollection,
				sniPortThresholdLo
					Counter32,
				sniPortThresholdHi
					Counter32
			 }

--  *******.4.1.34592.*******.1.1.2
		statsId OBJECT-TYPE
			SYNTAX StatsCollection
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { swThresholdEntry 2 }

		
--  *******.4.1.34592.*******.1.1.3
		sniPortThresholdLo OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { swThresholdEntry 3 }

		
--  *******.4.1.34592.*******.1.1.4
		sniPortThresholdHi OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { swThresholdEntry 4 }

		
--  *******.4.1.34592.*******.2
		oltThresholdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OltThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { alarmThreshHold 2 }

		
--  *******.4.1.34592.*******.2.1
		oltThresholdEntry OBJECT-TYPE
			SYNTAX OltThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { statsId }
			::= { oltThresholdTable 1 }

		
		OltThresholdEntry ::=
			SEQUENCE { 
				oltThresholdLo
					Counter32,
				oltThresholdHi
					Counter32
			 }

--  *******.4.1.34592.*******.2.1.2
		oltThresholdLo OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { oltThresholdEntry 2 }

		
--  *******.4.1.34592.*******.2.1.3
		oltThresholdHi OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { oltThresholdEntry 3 }

		
--  *******.4.1.34592.*******.3
		onuUniThresholdTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { alarmThreshHold 3 }

		
--  *******.4.1.34592.*******.3.1
		onuUniThresholdEntry OBJECT-TYPE
			SYNTAX OnuUniThresholdEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { statsId }
			::= { onuUniThresholdTable 1 }

		
		OnuUniThresholdEntry ::=
			SEQUENCE { 
				onuUniThresholdLo
					Counter32,
				onuUniThresholdHi
					Counter32
			 }

--  *******.4.1.34592.*******.3.1.2
		onuUniThresholdLo OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniThresholdEntry 2 }

		
--  *******.4.1.34592.*******.3.1.3
		onuUniThresholdHi OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniThresholdEntry 3 }

		
--  *******.4.1.34592.*******.4
		swTrafficChangeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SwTrafficChangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmThreshHold 4 }

		
--  *******.4.1.34592.*******.4.1
		swTrafficChangeEntry OBJECT-TYPE
			SYNTAX SwTrafficChangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { swSniPortId, statsId }
			::= { swTrafficChangeTable 1 }

		
		SwTrafficChangeEntry ::=
			SEQUENCE { 
				swSniPortTrafficChangeVal
					Counter32
			 }

--  *******.4.1.34592.*******.4.1.1
		swSniPortTrafficChangeVal OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swTrafficChangeEntry 1 }

		
--  *******.4.1.34592.*******.5
		oltTrafficChangeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OltTrafficChangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmThreshHold 5 }

		
--  *******.4.1.34592.*******.5.1
		oltTrafficChangeEntry OBJECT-TYPE
			SYNTAX OltTrafficChangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, statsId }
			::= { oltTrafficChangeTable 1 }

		
		OltTrafficChangeEntry ::=
			SEQUENCE { 
				oltTrafficChangeVal
					Counter32
			 }

--  *******.4.1.34592.*******.5.1.1
		oltTrafficChangeVal OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltTrafficChangeEntry 1 }

		
--  *******.4.1.34592.*******.6
		onuTrafficChangeTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuTrafficChangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			::= { alarmThreshHold 6 }

		
--  *******.4.1.34592.*******.6.1
		onuTrafficChangeEntry OBJECT-TYPE
			SYNTAX OnuTrafficChangeEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				"Description."
			INDEX { ponCardSlotId, oltId, onuId, uniPortId, statsId
				 }
			::= { onuTrafficChangeTable 1 }

		
		OnuTrafficChangeEntry ::=
			SEQUENCE { 
				onuTrafficChangeVal
					Counter32
			 }

--  *******.4.1.34592.*******.6.1.1
		onuTrafficChangeVal OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuTrafficChangeEntry 1 }

		
--  *******.4.1.34592.*******
		statistics OBJECT IDENTIFIER ::= { performance 2 }

		
--  *******.4.1.34592.*******.1
		llidStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF LlidStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { statistics 1 }

		
--  *******.4.1.34592.*******.1.1
		llidStatsEntry OBJECT-TYPE
			SYNTAX LlidStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, linkId, directionId }
			::= { llidStatsTable 1 }

		
		LlidStatsEntry ::=
			SEQUENCE { 
				llidOctetsTransferred
					Counter64,
				llidTotFrameTransferred
					Counter64,
				llidUniFrametransferred
					Counter64,
				llidBroadFrametransferred
					Counter64,
				llidMulFrametransferred
					Counter64,
				llidCrc32Errors
					Counter64,
				llidUndersizeFrames
					Counter64,
				llidOversizeFrames
					Counter64,
				llidFrom0to64OctetFrames
					Counter64,
				llidFrom65to127OctetFrames
					Counter64,
				llidFrom128to255OctetFrames
					Counter64,
				llidFrom256to511OctetFrames
					Counter64,
				llidFrom512to1023OctetFrames
					Counter64,
				llidFrom1024to1518OctetFrames
					Counter64,
				llidMore1519ctetFrames
					Counter64,
				llidFramesDropped
					Counter64,
				llidOctetsDropped
					Counter64,
				llidOctetsDelayed
					Counter64,
				llidOctetsGranted
					Counter64,
				llidUnusedGrantedOctets
					Counter64,
				llidMaximumDelay
					Counter64,
				llidLineCodeViolation
					Counter64,
				llidErrFrameSecond
					Counter64,
				llidErrFramePeriod
					Counter64,
				llidSumErrFrameSecond
					Counter64,
				llidStatsOperation
					INTEGER
			 }

--  *******.4.1.34592.*******.1.1.3
		llidOctetsTransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { llidStatsEntry 3 }

		
--  *******.4.1.34592.*******.1.1.4
		llidTotFrameTransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 4 }

		
--  *******.4.1.34592.*******.1.1.5
		llidUniFrametransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 5 }

		
--  *******.4.1.34592.*******.1.1.6
		llidBroadFrametransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 6 }

		
--  *******.4.1.34592.*******.1.1.7
		llidMulFrametransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 7 }

		
--  *******.4.1.34592.*******.1.1.8
		llidCrc32Errors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 8 }

		
--  *******.4.1.34592.*******.1.1.9
		llidUndersizeFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 9 }

		
--  *******.4.1.34592.*******.1.1.10
		llidOversizeFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 10 }

		
--  *******.4.1.34592.*******.1.1.11
		llidFrom0to64OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 11 }

		
--  *******.4.1.34592.*******.1.1.12
		llidFrom65to127OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 12 }

		
--  *******.4.1.34592.*******.1.1.13
		llidFrom128to255OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 13 }

		
--  *******.4.1.34592.*******.1.1.14
		llidFrom256to511OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 14 }

		
--  *******.4.1.34592.*******.1.1.15
		llidFrom512to1023OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 15 }

		
--  *******.4.1.34592.*******.1.1.16
		llidFrom1024to1518OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 16 }

		
--  *******.4.1.34592.*******.1.1.17
		llidMore1519ctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 17 }

		
--  *******.4.1.34592.*******.1.1.18
		llidFramesDropped OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 18 }

		
--  *******.4.1.34592.*******.1.1.19
		llidOctetsDropped OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 19 }

		
--  *******.4.1.34592.*******.1.1.20
		llidOctetsDelayed OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 20 }

		
--  *******.4.1.34592.*******.1.1.21
		llidOctetsGranted OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 21 }

		
--  *******.4.1.34592.*******.1.1.22
		llidUnusedGrantedOctets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 22 }

		
--  *******.4.1.34592.*******.1.1.23
		llidMaximumDelay OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 23 }

		
--  *******.4.1.34592.*******.1.1.24
		llidLineCodeViolation OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 24 }

		
--  *******.4.1.34592.*******.1.1.25
		llidErrFrameSecond OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 25 }

		
--  *******.4.1.34592.*******.1.1.26
		llidErrFramePeriod OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 26 }

		
--  *******.4.1.34592.*******.1.1.27
		llidSumErrFrameSecond OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { llidStatsEntry 27 }

		
--  *******.4.1.34592.*******.1.1.37
		llidStatsOperation OBJECT-TYPE
			SYNTAX INTEGER
				{
				fresh(1),
				clear(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { llidStatsEntry 37 }

		
--  *******.4.1.34592.*******.2
		onuUniPortStatsTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniPortStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { statistics 2 }

		
--  *******.4.1.34592.*******.2.1
		onuUniPortStatsEntry OBJECT-TYPE
			SYNTAX OnuUniPortStatsEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId, directionId
				 }
			::= { onuUniPortStatsTable 1 }

		
		OnuUniPortStatsEntry ::=
			SEQUENCE { 
				onuUniOctetsTransferred
					Counter64,
				onuUniTotFrameTransferred
					Counter64,
				onuUniUniFrametransferred
					Counter64,
				onuUniBroadFrametransferred
					Counter64,
				onuUniMulFrametransferred
					Integer32,
				onuUniCrc32Errors
					Counter64,
				onuUniUndersizeFrames
					Counter64,
				onuUniOversizeFrames
					Counter64,
				onuUniCollosions
					Counter64,
				onuUniFrom0to64OctetFrames
					Counter64,
				onuUniFrom65to127OctetFrames
					Counter64,
				onuUniFrom128to255OctetFrames
					Counter64,
				onuUniFrom256to511OctetFrames
					Counter64,
				onuUniFrom512to1023OctetFrames
					Counter64,
				onuUniFrom1024to1518OctetFrames
					Counter64,
				onuUniMore1519ctetFrames
					Counter64,
				onuUniFramesDropped
					Counter64,
				onuUniOctetsDropped
					Counter64,
				onuUniOctetsDelayed
					Counter64,
				onuUniOctetsGranted
					Counter64,
				onuUniUnusedGrantedOctets
					Counter64,
				onuUniCrc8Errors
					Counter64,
				onuUniPauseFrames
					Counter64,
				onuUniStatsOperation
					INTEGER
			 }

--  *******.4.1.34592.*******.2.1.3
		onuUniOctetsTransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortStatsEntry 3 }

		
--  *******.4.1.34592.*******.2.1.4
		onuUniTotFrameTransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 4 }

		
--  *******.4.1.34592.*******.2.1.5
		onuUniUniFrametransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 5 }

		
--  *******.4.1.34592.*******.2.1.6
		onuUniBroadFrametransferred OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 6 }

		
--  *******.4.1.34592.*******.2.1.7
		onuUniMulFrametransferred OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 7 }

		
--  *******.4.1.34592.*******.2.1.8
		onuUniCrc32Errors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 8 }

		
--  *******.4.1.34592.*******.2.1.9
		onuUniUndersizeFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 9 }

		
--  *******.4.1.34592.*******.2.1.10
		onuUniOversizeFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 10 }

		
--  *******.4.1.34592.*******.2.1.11
		onuUniCollosions OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 11 }

		
--  *******.4.1.34592.*******.2.1.12
		onuUniFrom0to64OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 12 }

		
--  *******.4.1.34592.*******.2.1.13
		onuUniFrom65to127OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 13 }

		
--  *******.4.1.34592.*******.2.1.14
		onuUniFrom128to255OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 14 }

		
--  *******.4.1.34592.*******.2.1.15
		onuUniFrom256to511OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 15 }

		
--  *******.4.1.34592.*******.2.1.16
		onuUniFrom512to1023OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 16 }

		
--  *******.4.1.34592.*******.2.1.17
		onuUniFrom1024to1518OctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 17 }

		
--  *******.4.1.34592.*******.2.1.18
		onuUniMore1519ctetFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 18 }

		
--  *******.4.1.34592.*******.2.1.19
		onuUniFramesDropped OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 19 }

		
--  *******.4.1.34592.*******.2.1.20
		onuUniOctetsDropped OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 20 }

		
--  *******.4.1.34592.*******.2.1.21
		onuUniOctetsDelayed OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 21 }

		
--  *******.4.1.34592.*******.2.1.22
		onuUniOctetsGranted OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 22 }

		
--  *******.4.1.34592.*******.2.1.23
		onuUniUnusedGrantedOctets OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 23 }

		
--  *******.4.1.34592.*******.2.1.24
		onuUniCrc8Errors OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 24 }

		
--  *******.4.1.34592.*******.2.1.25
		onuUniPauseFrames OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { onuUniPortStatsEntry 25 }

		
--  *******.4.1.34592.*******.2.1.35
		onuUniStatsOperation OBJECT-TYPE
			SYNTAX INTEGER
				{
				fresh(1),
				clear(2)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniPortStatsEntry 35 }

		
--  *******.4.1.34592.*******
		performanceMonitor OBJECT IDENTIFIER ::= { performance 3 }

		
--  *******.4.1.34592.*******.1
		hisPerformanceMonitor OBJECT IDENTIFIER ::= { performanceMonitor 1 }

		
--  *******.4.1.34592.*******.1.1
		swSniHisMonitorTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SwSniHisMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { hisPerformanceMonitor 1 }

		
--  *******.4.1.34592.*******.1.1.1
		swSniHisMonitorEntry OBJECT-TYPE
			SYNTAX SwSniHisMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { swSniPortId, directionId, swSniHisMonitorTimeSerial }
			::= { swSniHisMonitorTable 1 }

		
		SwSniHisMonitorEntry ::=
			SEQUENCE { 
				swSniHisMonitorTimeSerial
					INTEGER,
				swSniOctetTransHis
					Counter32,
				swSniTotalFrameTransHis
					Counter32,
				swSniUCFrameTransHis
					Counter32,
				swSniBCFrameTransHis
					Counter32,
				swSniMCFrameTransHis
					Counter32,
				swSniCRC32ErrorsHis
					Counter32,
				swSniUndersizeFramesHis
					Counter32,
				swSniOversizeFramesHis
					Counter32,
				swSniFramesDroppedHis
					Counter32,
				swSniOctetsDroppedHis
					Counter32,
				swSniBandwidthHis
					Counter32
			 }

--  *******.4.1.34592.*******.*******
		swSniHisMonitorTimeSerial OBJECT-TYPE
			SYNTAX INTEGER (1..192)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { swSniHisMonitorEntry 2 }

		
--  *******.4.1.34592.*******.*******
		swSniOctetTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { swSniHisMonitorEntry 5 }

		
--  *******.4.1.34592.*******.*******
		swSniTotalFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 6 }

		
--  *******.4.1.34592.*******.*******
		swSniUCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 7 }

		
--  *******.4.1.34592.*******.*******
		swSniBCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 8 }

		
--  *******.4.1.34592.*******.*******
		swSniMCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 9 }

		
--  *******.4.1.34592.*******.********
		swSniCRC32ErrorsHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 10 }

		
--  *******.4.1.34592.*******.********
		swSniUndersizeFramesHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 11 }

		
--  *******.4.1.34592.*******.********
		swSniOversizeFramesHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 12 }

		
--  *******.4.1.34592.*******.********
		swSniFramesDroppedHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 13 }

		
--  *******.4.1.34592.*******.********
		swSniOctetsDroppedHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 14 }

		
--  *******.4.1.34592.*******.********
		swSniBandwidthHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniHisMonitorEntry 15 }

		
--  *******.4.1.34592.*******.1.2
		oltHisMonitorTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OltHisMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { hisPerformanceMonitor 2 }

		
--  *******.4.1.34592.*******.1.2.1
		oltHisMonitorEntry OBJECT-TYPE
			SYNTAX OltHisMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, directionId, oltHisMonitorTimeSerial }
			::= { oltHisMonitorTable 1 }

		
		OltHisMonitorEntry ::=
			SEQUENCE { 
				oltHisMonitorTimeSerial
					Integer32,
				oltOctetTransHis
					Counter32,
				oltTotalFrameTransHis
					Counter32,
				oltUCFrameTransHis
					Counter32,
				oltBCFrameTransHis
					Counter32,
				oltMCFrameTransHis
					Counter32,
				oltCRC32ErrorsHis
					Counter32,
				oltUndersizeFramesHis
					Counter32,
				oltOversizeFramesHis
					Counter32,
				oltFramesDroppedHis
					Counter32,
				oltOctetsDroppedHis
					Counter32,
				oltBandwidthHis
					Counter32
			 }

--  *******.4.1.34592.*******.*******
		oltHisMonitorTimeSerial OBJECT-TYPE
			SYNTAX Integer32 (1..192)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { oltHisMonitorEntry 2 }

		
--  *******.4.1.34592.*******.*******
		oltOctetTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { oltHisMonitorEntry 5 }

		
--  *******.4.1.34592.*******.*******
		oltTotalFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 6 }

		
--  *******.4.1.34592.*******.*******
		oltUCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 7 }

		
--  *******.4.1.34592.*******.*******
		oltBCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 8 }

		
--  *******.4.1.34592.*******.*******
		oltMCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 9 }

		
--  *******.4.1.34592.*******.********
		oltCRC32ErrorsHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 10 }

		
--  *******.4.1.34592.*******.********
		oltUndersizeFramesHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 11 }

		
--  *******.4.1.34592.*******.********
		oltOversizeFramesHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 12 }

		
--  *******.4.1.34592.*******.********
		oltFramesDroppedHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 13 }

		
--  *******.4.1.34592.*******.********
		oltOctetsDroppedHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 14 }

		
--  *******.4.1.34592.*******.********
		oltBandwidthHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltHisMonitorEntry 15 }

		
--  *******.4.1.34592.*******.1.3
		onuUniHisMonitorTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniHisMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { hisPerformanceMonitor 3 }

		
--  *******.4.1.34592.*******.1.3.1
		onuUniHisMonitorEntry OBJECT-TYPE
			SYNTAX OnuUniHisMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId, directionId, 
				onuUniHisMonitorTimeSerial }
			::= { onuUniHisMonitorTable 1 }

		
		OnuUniHisMonitorEntry ::=
			SEQUENCE { 
				onuUniHisMonitorTimeSerial
					INTEGER,
				onuUniOctetTransHis
					Counter32,
				onuUniTotalFrameTransHis
					Counter32,
				onuUniUCFrameTransHis
					Counter32,
				onuUniBCFrameTransHis
					Counter32,
				onuUniMCFrameTransHis
					Counter32,
				onuUniCRC32ErrorsHis
					Counter32,
				onuUniUndersizeFramesHis
					Counter32,
				onuUniOversizeFramesHis
					Counter32,
				onuUniFramesDroppedHis
					Counter32,
				onuUniOctetsDroppedHis
					Counter32,
				onuUniBandwidthHis
					Counter32
			 }

--  *******.4.1.34592.*******.*******
		onuUniHisMonitorTimeSerial OBJECT-TYPE
			SYNTAX INTEGER (1..192)
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 1 }

		
--  *******.4.1.34592.*******.*******
		onuUniOctetTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 5 }

		
--  *******.4.1.34592.*******.1.3.1.6
		onuUniTotalFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 6 }

		
--  *******.4.1.34592.*******.1.3.1.7
		onuUniUCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 7 }

		
--  *******.4.1.34592.*******.1.3.1.8
		onuUniBCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 8 }

		
--  *******.4.1.34592.*******.1.3.1.9
		onuUniMCFrameTransHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 9 }

		
--  *******.4.1.34592.*******.*******0
		onuUniCRC32ErrorsHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 10 }

		
--  *******.4.1.34592.*******.*******1
		onuUniUndersizeFramesHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 11 }

		
--  *******.4.1.34592.*******.********
		onuUniOversizeFramesHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 12 }

		
--  *******.4.1.34592.*******.********
		onuUniFramesDroppedHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 13 }

		
--  *******.4.1.34592.*******.********
		onuUniOctetsDroppedHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 14 }

		
--  *******.4.1.34592.*******.********
		onuUniBandwidthHis OBJECT-TYPE
			SYNTAX Counter32
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniHisMonitorEntry 15 }

		
--  *******.4.1.34592.*******.2
		realTimePerformanceMonitor OBJECT IDENTIFIER ::= { performanceMonitor 2 }

		
--  *******.4.1.34592.*******.2.1
		monitorTimeout OBJECT-TYPE
			SYNTAX Integer32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { realTimePerformanceMonitor 1 }

		
--  *******.4.1.34592.*******.2.2
		monitorOperator OBJECT-TYPE
			SYNTAX INTEGER
				{
				monitorStart(1),
				monitorStop(2),
				monitorResultClear(3)
				}
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				"Description."
			::= { realTimePerformanceMonitor 2 }

		
--  *******.4.1.34592.*******.2.3
		swSniMonitorTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SwSniMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { realTimePerformanceMonitor 3 }

		
--  *******.4.1.34592.*******.2.3.1
		swSniMonitorEntry OBJECT-TYPE
			SYNTAX SwSniMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { swSniPortId, directionId }
			::= { swSniMonitorTable 1 }

		
		SwSniMonitorEntry ::=
			SEQUENCE { 
				swSniOctetTransRel
					Counter64,
				swSniTotalFrameTransRel
					Counter64,
				swSniUCFrameTransRel
					Counter64,
				swSniBCFrameTransRel
					Counter64,
				swSniMCFrameTransRel
					Counter64,
				swSniCRC32ErrorsRel
					Counter64,
				swSniUndersizeFramesRel
					Counter64,
				swSniOversizeFramesRel
					Counter64,
				swSniFramesDroppedRel
					Counter64,
				swSniOctetsDroppedRel
					Counter64,
				swSniBandwidthRel
					Counter64
			 }

--  *******.4.1.34592.*******.*******
		swSniOctetTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { swSniMonitorEntry 5 }

		
--  *******.4.1.34592.*******.*******
		swSniTotalFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 6 }

		
--  *******.4.1.34592.*******.*******
		swSniUCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 7 }

		
--  *******.4.1.34592.*******.*******
		swSniBCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 8 }

		
--  *******.4.1.34592.*******.*******
		swSniMCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 9 }

		
--  *******.4.1.34592.*******.********
		swSniCRC32ErrorsRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 10 }

		
--  *******.4.1.34592.*******.********
		swSniUndersizeFramesRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 11 }

		
--  *******.4.1.34592.*******.********
		swSniOversizeFramesRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 12 }

		
--  *******.4.1.34592.*******.********
		swSniFramesDroppedRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 13 }

		
--  *******.4.1.34592.*******.********
		swSniOctetsDroppedRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 14 }

		
--  *******.4.1.34592.*******.********
		swSniBandwidthRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { swSniMonitorEntry 15 }

		
--  *******.4.1.34592.*******.2.4
		swSniMonitorCtrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF SwSniMonitorCtrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { realTimePerformanceMonitor 4 }

		
--  *******.4.1.34592.*******.2.4.1
		swSniMonitorCtrEntry OBJECT-TYPE
			SYNTAX SwSniMonitorCtrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { swSniPortId, directionId }
			::= { swSniMonitorCtrTable 1 }

		
		SwSniMonitorCtrEntry ::=
			SEQUENCE { 
				swSniMonitorMap
					Unsigned32
			 }

--  *******.4.1.34592.*******.*******
		swSniMonitorMap OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { swSniMonitorCtrEntry 1 }

		
--  *******.4.1.34592.*******.2.5
		oltMonitorTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OltMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { realTimePerformanceMonitor 5 }

		
--  *******.4.1.34592.*******.2.5.1
		oltMonitorEntry OBJECT-TYPE
			SYNTAX OltMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, directionId }
			::= { oltMonitorTable 1 }

		
		OltMonitorEntry ::=
			SEQUENCE { 
				oltOctetTransRel
					Counter64,
				oltTotalFrameTransRel
					Counter64,
				oltUCFrameTransRel
					Counter64,
				oltBCFrameTransRel
					Counter64,
				oltMCFrameTransRel
					Counter64,
				oltCRC32ErrorsRel
					Counter64,
				oltUndersizeFramesRel
					Counter64,
				oltOversizeFramesRel
					Counter64,
				oltFramesDroppedRel
					Counter64,
				oltOctetsDroppedRel
					Counter64,
				oltBandwidthRel
					Counter64
			 }

--  *******.4.1.34592.*******.*******
		oltOctetTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { oltMonitorEntry 5 }

		
--  *******.4.1.34592.*******.*******
		oltTotalFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 6 }

		
--  *******.4.1.34592.*******.*******
		oltUCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 7 }

		
--  *******.4.1.34592.*******.*******
		oltBCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 8 }

		
--  *******.4.1.34592.*******.*******
		oltMCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 9 }

		
--  *******.4.1.34592.*******.********
		oltCRC32ErrorsRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 10 }

		
--  *******.4.1.34592.*******.********
		oltUndersizeFramesRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 11 }

		
--  *******.4.1.34592.*******.********
		oltOversizeFramesRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 12 }

		
--  *******.4.1.34592.*******.********
		oltFramesDroppedRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 13 }

		
--  *******.4.1.34592.*******.********
		oltOctetsDroppedRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 14 }

		
--  *******.4.1.34592.*******.2.5.1.15
		oltBandwidthRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				"Description."
			::= { oltMonitorEntry 15 }

		
--  *******.4.1.34592.*******.2.6
		oltMonitorCtrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OltMonitorCtrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { realTimePerformanceMonitor 6 }

		
--  *******.4.1.34592.*******.2.6.1
		oltMonitorCtrEntry OBJECT-TYPE
			SYNTAX OltMonitorCtrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, directionId }
			::= { oltMonitorCtrTable 1 }

		
		OltMonitorCtrEntry ::=
			SEQUENCE { 
				oltMonitorMap
					Unsigned32
			 }

--  *******.4.1.34592.*******.2.6.1.1
		oltMonitorMap OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { oltMonitorCtrEntry 1 }

		
--  *******.4.1.34592.*******.2.7
		onuUniMonitorTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { realTimePerformanceMonitor 7 }

		
--  *******.4.1.34592.*******.2.7.1
		onuUniMonitorEntry OBJECT-TYPE
			SYNTAX OnuUniMonitorEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId, directionId
				 }
			::= { onuUniMonitorTable 1 }

		
		OnuUniMonitorEntry ::=
			SEQUENCE { 
				onuUniOctetTransRel
					Counter64,
				onuUniTotalFrameTransRel
					Counter64,
				onuUniUCFrameTransRel
					Counter64,
				onuUniBCFrameTransRel
					Counter64,
				onuUniMCFrameTransRel
					Counter64,
				onuUniCRC32ErrorsRel
					Counter64,
				onuUniUndersizeRel
					Counter64,
				onuUniOversizeFramesRel
					Counter64,
				onuUniFramesDroppedRel
					Counter64,
				onuUniOctetsDroppedRel
					Counter64,
				onuUniBandwidthRel
					Counter64
			 }

--  *******.4.1.34592.*******.*******
		onuUniOctetTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 5 }

		
--  *******.4.1.34592.*******.*******
		onuUniTotalFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 6 }

		
--  *******.4.1.34592.*******.*******
		onuUniUCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 7 }

		
--  *******.4.1.34592.*******.*******
		onuUniBCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 8 }

		
--  *******.4.1.34592.*******.*******
		onuUniMCFrameTransRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 9 }

		
--  *******.4.1.34592.*******.********
		onuUniCRC32ErrorsRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 10 }

		
--  *******.4.1.34592.*******.********
		onuUniUndersizeRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 11 }

		
--  *******.4.1.34592.*******.********
		onuUniOversizeFramesRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 12 }

		
--  *******.4.1.34592.*******.********
		onuUniFramesDroppedRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 13 }

		
--  *******.4.1.34592.*******.********
		onuUniOctetsDroppedRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 14 }

		
--  *******.4.1.34592.*******.********
		onuUniBandwidthRel OBJECT-TYPE
			SYNTAX Counter64
			MAX-ACCESS read-only
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorEntry 15 }

		
--  *******.4.1.34592.*******.2.8
		onuUniMonitorCtrTable OBJECT-TYPE
			SYNTAX SEQUENCE OF OnuUniMonitorCtrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			::= { realTimePerformanceMonitor 8 }

		
--  *******.4.1.34592.*******.2.8.1
		onuUniMonitorCtrEntry OBJECT-TYPE
			SYNTAX OnuUniMonitorCtrEntry
			MAX-ACCESS not-accessible
			STATUS current
			DESCRIPTION
				" "
			INDEX { ponCardSlotId, oltId, onuId, uniPortId, directionId
				 }
			::= { onuUniMonitorCtrTable 1 }

		
		OnuUniMonitorCtrEntry ::=
			SEQUENCE { 
				onuUniMonitorMap
					Unsigned32
			 }

--  *******.4.1.34592.*******.*******
		onuUniMonitorMap OBJECT-TYPE
			SYNTAX Unsigned32
			MAX-ACCESS read-write
			STATUS current
			DESCRIPTION
				" "
			::= { onuUniMonitorCtrEntry 1 }

		
--  *******.4.1.34592.*******
		performanceConform OBJECT IDENTIFIER ::= { performance 6 }

		
--  *******.4.1.34592.*******.1
		performanceGroups OBJECT IDENTIFIER ::= { performanceConform 1 }

		
--  *******.4.1.34592.*******.1.1
		alarmThresholdGroup OBJECT-GROUP
			OBJECTS { sniPortThresholdLo, sniPortThresholdHi, swSniPortTrafficChangeVal, oltThresholdLo, oltThresholdHi, 
				oltTrafficChangeVal, onuUniThresholdLo, onuUniThresholdHi, onuTrafficChangeVal }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing alarm threshold config"
			::= { performanceGroups 1 }

		
--  *******.4.1.34592.*******.1.2
		fdOltStatsGroup OBJECT-GROUP
			OBJECTS { llidOctetsTransferred, llidTotFrameTransferred, llidUniFrametransferred, llidBroadFrametransferred, llidMulFrametransferred, 
				llidCrc32Errors, llidUndersizeFrames, llidOversizeFrames, llidFrom0to64OctetFrames, llidFrom65to127OctetFrames, 
				llidFrom128to255OctetFrames, llidFrom256to511OctetFrames, llidFrom512to1023OctetFrames, llidFrom1024to1518OctetFrames, llidMore1519ctetFrames, 
				llidFramesDropped, llidOctetsDropped, llidOctetsDelayed, llidOctetsGranted, llidUnusedGrantedOctets, 
				llidMaximumDelay, llidLineCodeViolation, llidErrFrameSecond, llidErrFramePeriod, llidSumErrFrameSecond, 
				llidStatsOperation }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing olt statistics management"
			::= { performanceGroups 2 }

		
--  *******.4.1.34592.*******.1.3
		fdOnuStatsGroup OBJECT-GROUP
			OBJECTS { onuUniOctetsTransferred, onuUniTotFrameTransferred, onuUniUniFrametransferred, onuUniBroadFrametransferred, onuUniMulFrametransferred, 
				onuUniCrc32Errors, onuUniUndersizeFrames, onuUniOversizeFrames, onuUniCollosions, onuUniFrom0to64OctetFrames, 
				onuUniFrom65to127OctetFrames, onuUniFrom128to255OctetFrames, onuUniFrom256to511OctetFrames, onuUniFrom512to1023OctetFrames, onuUniFrom1024to1518OctetFrames, 
				onuUniMore1519ctetFrames, onuUniFramesDropped, onuUniOctetsDropped, onuUniOctetsDelayed, onuUniOctetsGranted, 
				onuUniUnusedGrantedOctets, onuUniCrc8Errors, onuUniPauseFrames, onuUniStatsOperation }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing onu statistics management"
			::= { performanceGroups 3 }

		
--  *******.4.1.34592.*******.1.4
		swHisMonitorGroup OBJECT-GROUP
			OBJECTS { swSniOctetTransHis, swSniTotalFrameTransHis, swSniUCFrameTransHis, swSniBCFrameTransHis, swSniMCFrameTransHis, 
				swSniCRC32ErrorsHis, swSniUndersizeFramesHis, swSniOversizeFramesHis, swSniFramesDroppedHis, swSniOctetsDroppedHis, 
				swSniBandwidthHis }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing switch SNI ports historic statistics management"
			::= { performanceGroups 4 }

		
--  *******.4.1.34592.*******.1.5
		oltHisMonitorGroup OBJECT-GROUP
			OBJECTS { oltOctetTransHis, oltTotalFrameTransHis, oltUCFrameTransHis, oltBCFrameTransHis, oltMCFrameTransHis, 
				oltCRC32ErrorsHis, oltUndersizeFramesHis, oltOversizeFramesHis, oltFramesDroppedHis, oltOctetsDroppedHis, 
				oltBandwidthHis }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing olt historic statistics management"
			::= { performanceGroups 5 }

		
--  *******.4.1.34592.*******.1.6
		onuHisMonitorGroup OBJECT-GROUP
			OBJECTS { onuUniOctetTransHis, onuUniTotalFrameTransHis, onuUniUCFrameTransHis, onuUniBCFrameTransHis, onuUniMCFrameTransHis, 
				onuUniCRC32ErrorsHis, onuUniUndersizeFramesHis, onuUniOversizeFramesHis, onuUniFramesDroppedHis, onuUniOctetsDroppedHis, 
				onuUniBandwidthHis }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing onu historic statistics management"
			::= { performanceGroups 6 }

		
--  *******.4.1.34592.*******.1.7
		swRelMonitorGroup OBJECT-GROUP
			OBJECTS { swSniOctetTransRel, swSniTotalFrameTransRel, swSniUCFrameTransRel, swSniBCFrameTransRel, swSniMCFrameTransRel, 
				swSniCRC32ErrorsRel, swSniUndersizeFramesRel, swSniOversizeFramesRel, swSniFramesDroppedRel, swSniOctetsDroppedRel, 
				swSniBandwidthRel }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing switch SNI ports realtime statistics management"
			::= { performanceGroups 7 }

		
--  *******.4.1.34592.*******.1.8
		oltRelMonitorGroup OBJECT-GROUP
			OBJECTS { oltOctetTransRel, oltTotalFrameTransRel, oltUCFrameTransRel, oltBCFrameTransRel, oltMCFrameTransRel, 
				oltCRC32ErrorsRel, oltUndersizeFramesRel, oltOversizeFramesRel, oltFramesDroppedRel, oltOctetsDroppedRel, 
				oltBandwidthRel }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing olt realtime statistics management"
			::= { performanceGroups 8 }

		
--  *******.4.1.34592.*******.1.9
		onuRelMonitorGroup OBJECT-GROUP
			OBJECTS { onuUniOctetTransRel, onuUniTotalFrameTransRel, onuUniUCFrameTransRel, onuUniBCFrameTransRel, onuUniMCFrameTransRel, 
				onuUniCRC32ErrorsRel, onuUniUndersizeRel, onuUniOversizeFramesRel, onuUniFramesDroppedRel, onuUniOctetsDroppedRel, 
				onuUniBandwidthRel }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing onu realtime statistics management"
			::= { performanceGroups 9 }

		
--  *******.4.1.34592.*******.1.10
		monitorMapGroup OBJECT-GROUP
			OBJECTS { onuUniMonitorMap, monitorTimeout, monitorOperator, oltMonitorMap, swSniMonitorMap
				 }
			STATUS current
			DESCRIPTION 
				"A collection of objects providing device monitor map configuration"
			::= { performanceGroups 10 }

		
--  *******.4.1.34592.*******.2
		performanceCompliances OBJECT IDENTIFIER ::= { performanceConform 2 }

		
--  *******.4.1.34592.*******.2.1
-- this module
		performanceCompliance MODULE-COMPLIANCE
			STATUS current
			DESCRIPTION 
				"The compliance statement"
			MODULE -- this module
				MANDATORY-GROUPS { alarmThresholdGroup, fdOltStatsGroup, fdOnuStatsGroup, swHisMonitorGroup, oltHisMonitorGroup, 
					onuHisMonitorGroup, swRelMonitorGroup, oltRelMonitorGroup, onuRelMonitorGroup, monitorMapGroup
					 }
			::= { performanceCompliances 1 }

		
	
	END

--
-- FD-PERFORMANCE-MIB.my
--
