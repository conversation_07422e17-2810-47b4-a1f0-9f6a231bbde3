--
-- SCS-ks.MIB
-- MIB generated by https://github.com/Torch09
-- Thursday, 05 November 2020
--

    SCS-ks-MIB DEFINITIONS ::= BEGIN
 
        IMPORTS
            OBJECT-GROUP
                FROM SNMPv2-CONF            
            sysContact, sysName, sysLocation            
                FROM SNMPv2-MIB
            enterprises, MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, Integer32
                FROM SNMPv2-SMI
            DisplayString
                FROM RFC1213-MIB
	    enterprises, IpAddress, Counter, Gauge
		FROM RFC1155-SMI
	    ifIndex, ifEntry
		FROM IF-MIB;
        
        scs-ks MODULE-IDENTITY
            LAST-UPDATED  "202011051805Z" 
            ORGANIZATION  "None"
            CONTACT-INFO
                "
                        None
                        GH: https://github.com/Torch09
                        Mail: <EMAIL>
                "
            DESCRIPTION
                "This is the unofficial MIB module for the SCS KS."
            REVISION    "202011051805Z" 
            DESCRIPTION "scs ks MIB"

            ::= { systm 1 }
    
    
--
-- Node definitions
--
    
        -- *******.4.1.9839
        carel OBJECT IDENTIFIER ::= { enterprises 9839 }
        
        -- *******.4.1.9839.1
        systm OBJECT IDENTIFIER ::= { carel 1 } 
        
        -- *******.4.1.9839.1.2
        scs OBJECT IDENTIFIER ::= { systm 2 }       

    
    -- SCS VARIABLE

        -- *******.4.1.9839.1.2.1
        status-evaporator-fan OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Evaporator Fan alarm 0=OK;1=Error"
                    ::= { scs 1 }
                    
        -- *******.4.1.9839.1.2.2
        status-condenser-fan OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Condenser Fan alarm 0=OK;1=Error"
                    ::= { scs 2 }       

        -- *******.4.1.9839.1.2.3
        status-compressor OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Compressor alarm 0=OK;1=Error"
                    ::= { scs 3 }                       
                                         
        -- *******.4.1.9839.1.2.4
        status-heater-thermal-protection OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Heater thermal protection alarm 0=OK;1=Error"
                    ::= { scs 4 }        
                    
        -- *******.4.1.9839.1.2.5
        status-exhaust-air-sensor OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Exhaust air sensor alarm 0=OK;1=Error"
                    ::= { scs 5 }   

        -- *******.4.1.9839.1.2.6
        status-supply-air-sensor OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Supply air sensor alarm 0=OK;1=Error"
                    ::= { scs 6 }   

        -- *******.4.1.9839.1.2.7
        status-outside-sensor OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Outside temperature sensor alarm 0=OK;1=Error"
                    ::= { scs 7 }   
                    
        -- *******.4.1.9839.1.2.8
        status-room-temperature-sensor OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Temperature room sensor alarm 0=OK;1=Error"
                    ::= { scs 8 }   
                    
        -- *******.4.1.9839.1.2.9
        status-room-humidity-sensor OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Humidity room sensor alarm 0=OK;1=Error"
                    ::= { scs 9 }   

        -- *******.4.1.9839.1.2.10
        status-min-exhaust-air-temperature OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Minimum exhaust air temperature alarm 0=OK;1=Error"
                    ::= { scs 10 }  

        -- *******.4.1.9839.1.2.11
        status-max-exhaust-air-temperature OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Maximum exhaust air temperature alarm 0=OK;1=Error"
                    ::= { scs 11 }  

        -- *******.4.1.9839.1.2.12
        status-min-supply-air-temperature OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Minimum supply air temperature alarm 0=OK;1=Error"
                    ::= { scs 12 }  

        -- *******.4.1.9839.1.2.13
        status-water-sensor OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Water sensor alarm 0=OK;1=Error"
                    ::= { scs 13 }  

        -- *******.4.1.9839.1.2.14
        status-filter-monitoring OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Filter monitoring alarm 0=OK;1=Error"
                    ::= { scs 14 }  

        -- *******.4.1.9839.1.2.15
        exhaust-air-temperature OBJECT-TYPE
                    SYNTAX Integer32 (-999..999)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Exhaust air temperature 235 = 23.5 degree Celsius"
                    ::= { scs 15 }  

        -- *******.4.1.9839.1.2.16
        supply-air-temperature OBJECT-TYPE
                    SYNTAX Integer32 (-999..999)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Supply air temperature 235 = 23.5 degree Celsius"
                    ::= { scs 16 }  

        -- *******.4.1.9839.1.2.17
        outside-air-temperature OBJECT-TYPE
                    SYNTAX Integer32 (-999..999)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Outside air temperature 235 = 23.5 degree Celsius"
                    ::= { scs 17 }  

        -- *******.4.1.9839.1.2.18
        room-temperature OBJECT-TYPE
                    SYNTAX Integer32 (-999..999)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Room temperature 235 = 23.5 degree Celsius"
                    ::= { scs 18 }  

        -- *******.4.1.9839.1.2.19
        room-humidity OBJECT-TYPE
                    SYNTAX Integer32 (-999..999)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Room humidity 235 = 23.5 %"
                    ::= { scs 19 }  

        -- *******.4.1.9839.1.2.20
        status-operational-status OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Operational status 0=Off;1=Running"
                    ::= { scs 20 }  

        -- *******.4.1.9839.1.2.21
        status-collective-fault OBJECT-TYPE
                    SYNTAX Integer32 (0..1)
                    UNITS "N/A"
                    MAX-ACCESS read-only
                    STATUS current
                    DESCRIPTION "Collective fault alarm 0=Error;1=Ok"
                    ::= { scs 21 }                      
            END

--
-- SCS-ks.MIB
--
