LUM-WDM-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Unsigned32, Integer32, NOTIFICATION-TYPE,
    <PERSON><PERSON><PERSON>32, <PERSON>p<PERSON>ddress
        FROM SNMPv2-SMI
    OBJECT-GROUP, MODULE-COMPLIANCE, NOTIFICATION-GROUP
        FROM SNMPv2-CONF
    RowStatus, TestAndIncr, DisplayString, DateAndTime
        FROM SNMPv2-TC
    lumModules, lumWdmMIB
        FROM LUM-REG
    FaultStatus, MgmtNameString, SubrackNumber, SlotNumber, PortNumber,
    LambdaFrequency, LambdaType, PortType, BoardOrInterfaceAdminStatus,
    BoardOrInterfaceOperStatus, CommandString, ObjectProperty, SignalFormat,
    BerLevel, ResetWithNA, Signed32WithNA, EnabledDisabledWithNA,
    AdminStatusWithNA, OperStatusWith<PERSON>, TruthValueWithNA
        FROM LUM-TC;

lumWdmMIBModule MODULE-IDENTITY
    LAST-UPDATED
        "201903310000Z" -- March 31st 2019
    ORGANIZATION
        "Infinera Corporation"
    CONTACT-INFO
        "<EMAIL>"
    DESCRIPTION
        "The WDM trunk interface and protection group MIB.
        - General
        - WDM trunk interfaces
        - WDM passive interfaces
        - Protection groups
"
    REVISION
        "201903310000Z" -- March 31st 2019
    DESCRIPTION
        "Changes made for release r32.1:
         - added wdmIfTrxTunable"
    REVISION
        "201804240000Z" -- Apr 24th 2018
    DESCRIPTION
        "Changes made for release r31.1:
        - Added passiveIfIfNo"
    REVISION
        "201712150000Z" -- Dec 15th 2017
    DESCRIPTION
        "Changes made for release r30.0:
        - Added ChannelStartupCommand in CtrlGroup
		- Added attrs MaxAttenuation/MinAttenuation/AttenControlOffset/AttenControlDegraded in WdmCtrlChannel
		- Added wdmCtrlChannelNotFound alarm in WdmCtrlChannel"
    REVISION
        "201706220000Z" -- June 22nd 2017
    DESCRIPTION
        "Changes made for release r29.0:
        - Added wdmCtrlGroupTotalPower
        - Changed descr for wdmCtrlGroupOutputPowerMismatch from 1.5dB to 1.4dB
        - Changed ORGANIZATION and CONTACT-INFO"
    REVISION
        "201704170000Z" -- April 17th 2017
    DESCRIPTION
        "Changes made for release r28.1:
        - Adjusted limits for current and wanted link delay compensation."
    REVISION
        "201611300000Z" -- November 30th 2016
    DESCRIPTION
        "Changes made for release r28.0:
        - Added channel descr field on optical ctr groups.
        - added AID and physical location"
    REVISION
        "201607290000Z" -- Jul 29th 2016
    DESCRIPTION
        "Changes made for release r27.1:
        - Added delay compensation table.
        - Added delay compensation link table."
    REVISION
        "201601110000Z" -- Jan 11th 2016
    DESCRIPTION
        "Added default value notPresent for operStatus."
    REVISION
    	"201511300000Z" -- Nov 30th 2015
    DESCRIPTION
        "Changes made for release r26:
        - Removed possibility to see tpddgbe broadcast ring since that is
          no longer supported."
    REVISION
    	"201408150000Z" -- Aug 15th 2014
    DESCRIPTION
        "Changes made for release r23.1:
        - Added a new alarm to wdm ctrl group that will tell if the output
          power of the OA differs by more than 1.5dB from the monitor port
          insertion loss."
    REVISION
        "201405160000Z" -- May 16th 2014
    DESCRIPTION
        "Changes made for release r23:
        - NearEndLoopback for msMxpQMS2G5
        - Added two traffic combinations for fhmxp10g board."
    REVISION
        "201309260000Z" -- September 26th 2013
    DESCRIPTION
        "Changes made for release r22:
        - Added revertive switching for gbe9mxp10gfec, tpq10gfec(i) and msmxp10g
        - Extended loopback times to 120 hours"
    REVISION
        "201305010000Z" -- May 1st 2013
    DESCRIPTION
        "Changes made for release r21:
        - Added far end line loopback for tpq10gfec and tpq10gfeci.
        - Added Near end line loopback for msMxp (3 images),
          gbeMxp10GFEC, tpD10GbE.
        - Added tpq10gfeci board.
        - Added tpq10gfecregi board.
        - Signal degraded threshold added and
          Signal Degraded Protection implemented
          on tpD10GbE and gbeMxp10GFEC.
        - Added Far end loopback for msMxp (3 images),
          gbeMxp10GFEC and tpD10GbE."
    REVISION
        "201212200000Z" -- Dec 20th 2012
    DESCRIPTION
        "Changes made for release r20:
        - Boards msTp40G, msMxp40G added.
        - Attributes Signal degraded threshold and Signal Degraded Protection
          added."
    REVISION
        "201209210000Z" -- Sept 21st 2012
    DESCRIPTION
        "Changes made for release r19.0.2:
        - Traffic combination gbEx3Stm4Oc12x1Stm1Oc3x3Basic
          and gbESyncEx3Stm4Oc12Stm1Oc3x1Basic
          added for msmxp."
    REVISION
        "201203300000Z" -- March 30th 2012
    DESCRIPTION
        "Changes made for release r19:
        -Optical control loop - channel alarms added."
    REVISION
        "201112200000Z" -- Dec 20th 2011
    DESCRIPTION
        "Changes made for release r18:
        - Signal format description updated.
        - Boards tpqmp and tpq10GfecReg added.
        - Attribute Actual signal format added.
        - Basic implementation requirements for the WDM MIB updated to R18."
    REVISION
        "201104120000Z" -- April 12th 2011
    DESCRIPTION
        "Added new compliance group to mark adding write support for trail trace."
    REVISION
        "200601270000Z" -- January 27th 2006
    DESCRIPTION
        "Included protection groups in minimal compliance groups for TS-family.
        Added TS-1100 specific alarms."
    REVISION
        "200509260000Z" -- September 26th 2005
    DESCRIPTION
        "Added minimal compliance groups for TS-family"
    REVISION
        "200507070000Z" -- July 7th 2005
    DESCRIPTION
        "Added minimal compliance statements for TS-family"
    REVISION
        "200212040000Z" -- December 4th 2002
    DESCRIPTION
        "Added trace identifier attributes in the wdm interface table."
    REVISION
        "200205310000Z" -- May 31st 2002
    DESCRIPTION
        "Added wdmIfUnexpectedTxLambda alarm."
    REVISION
        "200205160000Z" -- May 16th 2002
    DESCRIPTION
        "Moved PM thresholds to PM-mib.
        Added complete set of PM alarms."
    REVISION
        "200205150000Z" -- May 15th 2002
    DESCRIPTION
        "Added PM alarm thresholds."
    REVISION
        "200202200000Z" -- February 20th 2002
    DESCRIPTION
        "Replaced old protection traps with one trap including status
        attributes."
    REVISION
        "200202010000Z" -- February 1st 2002
    DESCRIPTION
        "Deprecated inbandStatus. Corresponding info will be available in the IP MIB.
        Changed default value for inbandMode to down.
        Changed default value for laser temp threshold to 3 (0.3 degrees centigrade).
        AIS and FDI severity changed to minor."
    REVISION
        "200201240000Z" -- January 24th 2002
    DESCRIPTION
        "Updated protection administrative and operational status.
        Added fec mode on wdm interfaces."
    REVISION
        "200201170000Z" -- January 17th 2002
    DESCRIPTION
        "Deprecated wdmPassiveIfLambdaMax.
        Changed wdmPassiveIfLambdaMin alias to just lambda.
        Removed '(SDH)' from alarm texts."
    REVISION
        "200201160000Z" -- January 16th 2002
    DESCRIPTION
        "Added expected lambda for passive and wdm i/f."
    REVISION
        "200201090000Z" -- January 9th 2002
    DESCRIPTION
        "Deprecated passiveIfLastChange, added ifInbandStatus."
    REVISION
        "200112030000Z" -- December 3rd 2001
    DESCRIPTION
        "Moved admin and operStatus definitions to LUM-TC"
    REVISION
        "200111220000Z" -- November 22nd 2001
    DESCRIPTION
        "Added inband mode and status.
        Added traps on laserStatus and changed the protection definitions."
    REVISION
        "200111090000Z" -- November 9th 2001
    DESCRIPTION
        "lossOfFEC hidden; not supported yet.
        Added bitrate mismatch.
        Changed name of protection notification.
        Added laserBias meter and threshold.
        Added lossOfSignal threshold.
        Added j0PathTrace."
    REVISION
        "200110300000Z" -- October 30th 2001
    DESCRIPTION
        "MIB versions and testAndIncr hidden - not supported yet.
        Updated conformance."
    REVISION
        "200110230000Z" -- October 23d 2001
    DESCRIPTION
        "Added wdmIfRx port, changed wdmPort to wdmTxPort.
        Adapted passive interfaces to one port per interface."
    REVISION
        "200110100000Z" -- October 10th 2001
    DESCRIPTION
        "Introduced restriction on one wdm trunk i/f per board.
        Renamed protection group admin- and operStatus values.
        Moved passive i/f table from the LAMBDA-mib.
        Updated max-access to indicate attributes for creation.
        Introduced service admin and operStatus.
        Updated laserMode and laserStatus.
        Changed alarm names to 'long' names.
        Adapted to changes in other MIBs."
    REVISION
        "200109050000Z" -- September 5th 2001
    DESCRIPTION
        "Moved lossOfLock to transponder MIB.
        Added lossOfForwardErrorCorrection."
    REVISION
        "200109040000Z" -- September 4th 2001
    DESCRIPTION
        "Default values for protections physical postition
        is now 0 (undefined)."
    REVISION
        "200108240000Z" -- August 24th 2001
    DESCRIPTION
        "Added notification on protection status change.
        Added time stamp for the protection entry."
    REVISION
        "200108140000Z" -- August 14th 2001
    DESCRIPTION
        "Changes after review.
        Removed most lambda info from the interfaces.
        Interfaces are rxTx with fixed txLambda and
        broadband rxLambda.
        Renamed laserTempDefault to laserTemp.
        Merged temperature offset thresholds in to one attribute.
        Added descriptions for all alarms.
        Added compliance info.
        Added protection group configuration group proposal."
    REVISION
        "200108080000Z" -- August 9th 2001
    DESCRIPTION
        "The initial revision of this module."
    ::= { lumModules 6 }


-- ----------------------------------------------------
-- Conformance area, containing groups and complicance
-- specifications.
-- ----------------------------------------------------

lumWdmConfs OBJECT IDENTIFIER ::= { lumWdmMIB 1 }
lumWdmGroups OBJECT IDENTIFIER ::= { lumWdmConfs 1 }
lumWdmCompl OBJECT IDENTIFIER ::= { lumWdmConfs 2 }
lumWdmMinimalGroups OBJECT IDENTIFIER ::= { lumWdmConfs 3 }
lumWdmMinimalCompl OBJECT IDENTIFIER ::= { lumWdmConfs 4 }


-- ----------------------------------------------------
-- Root for objects in the wdm MIB
-- ----------------------------------------------------

lumWdmMIBObjects OBJECT IDENTIFIER ::= { lumWdmMIB 2 }


-- ----------------------------------------------------
-- This MIB contains the following groups:
-- ----------------------------------------------------

wdmGeneral OBJECT IDENTIFIER ::= { lumWdmMIBObjects 1 }
wdmIfList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 2 }
wdmProtList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 3 }
lumentisWdmNotifications OBJECT IDENTIFIER ::= { lumWdmMIBObjects 4 }
wdmPassiveIfList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 5 }
wdmVc4List OBJECT IDENTIFIER ::= { lumWdmMIBObjects 6 }
wdmRemoteProtList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 7 }
wdmCtrlChannelList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 8 }
wdmCtrlGroupList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 9 }
wdmSubChannelList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 10 }
wdmCtrlGlobal OBJECT IDENTIFIER ::= { lumWdmMIBObjects 11 }
wdmDelayCompPGList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 12 }
wdmDelayCompLinkList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 13 }
wdmMeanChannelPowerControlGlobalList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 14 }
wdmMeanChannelPowerControlList OBJECT IDENTIFIER ::= { lumWdmMIBObjects 15 }

-- ----------------------------------------------------
-- Textual Conventions
-- ----------------------------------------------------

-- ----------------------------------------------------
-- General group
-- ----------------------------------------------------

wdmGeneralTestAndIncr OBJECT-TYPE
    SYNTAX      TestAndIncr
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Protection against simultaneous access from
        multiple managers. See SNMPv2-TC.
"
    ::= { wdmGeneral 1 }

wdmGeneralMibSpecVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The MIB specification version.

        tbd: persistent?
"
    DEFVAL { "" }
    ::= { wdmGeneral 2 }

wdmGeneralMibImplVersion OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The MIB implementation version.

        tbd: persistent?
"
    DEFVAL { "" }
    ::= { wdmGeneral 3 }

wdmGeneralLastChangeTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time when the configuration of the MIB was
        last changed.
"
    ::= { wdmGeneral 4 }

wdmGeneralStateLastChangeTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time when the state of the MIB was last
        changed.
"
    ::= { wdmGeneral 5 }

wdmGeneralWdmIfTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 6 }

wdmGeneralWdmPassiveIfTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 7 }

wdmGeneralWdmProtTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 8 }

wdmGeneralWdmVc4TableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 9 }

wdmGeneralWdmRemoteProtTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 10 }

wdmGeneralWdmCtrlChannelTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 11 }

wdmGeneralWdmCtrlGroupTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 12 }

wdmGeneralWdmSubChannelTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 13 }

wdmGeneralWdmDelayCompPGTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 14 }

wdmGeneralWdmDelayCompLinkTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 15 }

wdmGeneralWdmMeanChannelPowerControlTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 16 }

wdmGeneralWdmMeanChannelPowerControlGlobalTableSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Size of table
"
    ::= { wdmGeneral 17 }

-- ----------------------------------------------------
-- WDM trunk interfaces
-- ----------------------------------------------------

wdmIfTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The interface list."
    ::= { wdmIfList 1 }

wdmIfEntry OBJECT-TYPE
    SYNTAX      WdmIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the interface list.
"
    INDEX { wdmIfIndex }
    ::= { wdmIfTable 1 }

WdmIfEntry ::=
    SEQUENCE {
        wdmIfIndex                      Unsigned32,
        wdmIfName                       MgmtNameString,
        wdmIfDescr                      DisplayString,
        wdmIfSubrack                    SubrackNumber,
        wdmIfSlot                       SlotNumber,
        wdmIfTxPort                     PortNumber,
        wdmIfInvPhysIndexOrZero         Unsigned32,
        wdmIfTxLambda                   LambdaFrequency,
        wdmIfHighSpeedMin               Gauge32,
        wdmIfHighSpeedMax               Gauge32,
        wdmIfPowerLevel                 Integer32,
        wdmIfPowerLevelHighThreshold    Integer32,
        wdmIfPowerLevelLowThreshold     Integer32,
        wdmIfLaserTemp                  Unsigned32,
        wdmIfLaserTempOffset            Integer32,
        wdmIfLaserTempOffsetThreshold   Unsigned32,
        wdmIfLaserMode                  INTEGER,
        wdmIfLaserStatus                INTEGER,
        wdmIfAdminStatus                BoardOrInterfaceAdminStatus,
        wdmIfOperStatus                 BoardOrInterfaceOperStatus,
        wdmIfLossOfSignal               FaultStatus,
        wdmIfReceivedPowerHigh          FaultStatus,
        wdmIfReceivedPowerLow           FaultStatus,
        wdmIfLaserBiasHigh              FaultStatus,
        wdmIfErroredSeconds             FaultStatus,
        wdmIfSeverelyErroredSeconds     FaultStatus,
        wdmIfBackgroundBlockErrors      FaultStatus,
        wdmIfUnavailableSeconds         FaultStatus,
        wdmIfForwardDefectIndication    FaultStatus,
        wdmIfBackwardDefectIndication   FaultStatus,
        wdmIfLossOfFrame                FaultStatus,
        wdmIfAlarmIndicationSignal      FaultStatus,
        wdmIfRemoteDefectIndication     FaultStatus,
        wdmIfLossOfSync                 FaultStatus,
        wdmIfLossOfForwardingErrorCorrection FaultStatus,
        wdmIfLaserTempHigh              FaultStatus,
        wdmIfLaserTempLow               FaultStatus,
        wdmIfRxPort                     PortNumber,
        wdmIfBitrateMismatch            FaultStatus,
        wdmIfLaserBias                  Unsigned32,
        wdmIfLaserBiasThreshold         Unsigned32,
        wdmIfLossOfSignalThreshold      Integer32,
        wdmIfJ0PathTrace                OCTET STRING,
        wdmIfInbandMode                 INTEGER,
        wdmIfInbandStatus               INTEGER,
        wdmIfExpectedTxLambda           LambdaFrequency,
        wdmIfForwardingErrorCorrectionMode INTEGER,
        wdmIfUnexpectedTxLambda         FaultStatus,
        wdmIfTraceIntrusionMode         INTEGER,
        wdmIfTraceTransmitted           DisplayString,
        wdmIfTraceReceived              DisplayString,
        wdmIfTraceExpected              DisplayString,
        wdmIfTraceAlarmMode             INTEGER,
        wdmIfTraceMismatch              FaultStatus,
        wdmIfLaserStatusLastChangeTime  DateAndTime,
        wdmIfSuppressRemoteAlarms       INTEGER,
        wdmIfSerialNumberMismatch       FaultStatus,
        wdmIfOptimizeDecisionThreshold  CommandString,
        wdmIfThresholdOptimizationState INTEGER,
        wdmIfUseHwDefaultDecisionThreshold INTEGER,
        wdmIfFecCorrectedZeros          Unsigned32,
        wdmIfFecCorrectedOnes           Unsigned32,
        wdmIfOptimizedForSerialNumber   DisplayString,
        wdmIfRelativeDecisionThreshold  Integer32,
        wdmIfTrxCodeMismatch            FaultStatus,
        wdmIfTrxBitrateUnavailable      FaultStatus,
        wdmIfTrxMissing                 FaultStatus,
        wdmIfTrxClass                   DisplayString,
        wdmIfLaserTempHighRelativeThreshold  Integer32,
        wdmIfLaserTempLowRelativeThreshold Integer32,
        wdmIfTransmitterFailed          FaultStatus,
        wdmIfReceiverSensitivity        Integer32,
        wdmIfPowerLevelLowRelativeThreshold Integer32,
        wdmIfIllegalFrequency           FaultStatus,
        wdmIfLaserForcedOn              INTEGER,
        wdmIfTrafficCombination         INTEGER,
        wdmIfSelectTrafficCombination   CommandString,
        wdmIfObjectProperty             ObjectProperty,
        wdmIfTxPowerLevel               Integer32,
        wdmIfLaserTempActual            Integer32,
        wdmIfTrxFailed                  FaultStatus,
        wdmIfDisabled                   FaultStatus,
        wdmIfLoopback                   FaultStatus,
        wdmIfContinousOptimization      INTEGER,
        wdmIfThresholdOptimizationResultCause DisplayString,
        wdmIfDistributionRole            INTEGER,
        wdmIfConfigurationCommand       CommandString,
        wdmIfNoFrequencySet             FaultStatus,
        wdmIfFormat                     SignalFormat,
        wdmIfConfigurationFormatCommand CommandString,
        wdmIfOHTransparency             INTEGER,
        wdmIfLinkDown                   FaultStatus,
        wdmIfAutoNegotiationMode        INTEGER,
        wdmIfAutoNegotiationStatus      INTEGER,
        wdmIfFlowControlMode            INTEGER,
        wdmIfGroupLineMode              INTEGER,
        wdmIfFecType                    INTEGER,
        wdmIfFarEndLoopback             INTEGER,
        wdmIfFarEndLoopbackTimeout      Integer32,
        wdmIfFarEndLoopbackEnabled      FaultStatus,
        wdmIfChangeLoopbackCommand      CommandString,
        wdmIfFecFailure                 FaultStatus,
        wdmIfTxSignalStatus             INTEGER,
        wdmIfRxSignalStatus             INTEGER,
        wdmIfNearEndLoopback            INTEGER,
        wdmIfNearEndLoopbackTimeout     Integer32,
        wdmIfNearEndLoopbackEnabled     FaultStatus,
        wdmIfChangeNearEndLoopbackCommand CommandString,
        wdmIfSignalDegraded             FaultStatus,
        wdmIfHubProtectionMode          INTEGER,
        wdmIfActualFormat               SignalFormat,
        wdmIfTdcDispersion              INTEGER,
        wdmIfTdcDispersionCommand       CommandString,
        wdmIfTdcDispersionMode          INTEGER,
        wdmIfLineControlLoopCurrentState  DisplayString,
        wdmIfSignalDegradeThreshold     BerLevel,
        wdmIfTrxThresholdOptimizationState INTEGER,
        wdmIfTrxDecisionThreshold       Integer32,
        wdmIfSwControlledLaserShutdown  INTEGER,
        wdmIfChangeSwControlledLaserShutdownCommand CommandString,
        wdmIfControlledLaserShutdownEnabled  FaultStatus,
        wdmIfAid                        DisplayString,
        wdmIfPhysicalLocation           DisplayString,
        wdmIfTrxTunable                 TruthValueWithNA }

wdmIfIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An arbitrary index assigned to each if entry.
"
    ::= { wdmIfEntry 1 }

wdmIfName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the interface, for example
        'wdm:1:2:1'.
"
    ::= { wdmIfEntry 2 }

wdmIfDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.
"
    DEFVAL { "" }
    ::= { wdmIfEntry 3 }

wdmIfSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the subrack where the interface
        is located.
"
    ::= { wdmIfEntry 4 }

wdmIfSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the slot where the interface is
        located.
"
    ::= { wdmIfEntry 5 }

wdmIfTxPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the port where the interface is
        located.
"
    ::= { wdmIfEntry 6 }

wdmIfInvPhysIndexOrZero OBJECT-TYPE
    SYNTAX      Unsigned32 (0..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The row in the invPhysTable for this interface.
        Set to 0 if not known.
"
    ::= { wdmIfEntry 7 }

wdmIfTxLambda OBJECT-TYPE
    SYNTAX      LambdaFrequency
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The transmitted frequency given in hundreds of
        GHz (0.01 GHz).
"
    ::= { wdmIfEntry 8 }

wdmIfHighSpeedMin OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum speed in units of 1,000,000 bits per
        second. If this object reports a value of 'n' then
        the speed of the interface is somewhere in the
        range of 'n-500,000' to 'n+499,999'.
"
    ::= { wdmIfEntry 9 }

wdmIfHighSpeedMax OBJECT-TYPE
    SYNTAX      Gauge32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum speed in units of 1,000,000 bits per
        second. If this object reports a value of 'n' then
        the speed of the interface is somewhere in the
        range of 'n-500,000' to 'n+499,999'.
"
    ::= { wdmIfEntry 10 }

wdmIfPowerLevel OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The received power level in units of 0.1 dBm.
"
    ::= { wdmIfEntry 11 }

wdmIfPowerLevelHighThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-380..-60)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The upper threshold for received power level in
        units of 0.1 dBm.

        Note: Depending on the type of board and interface
        the value range may vary.
"
    DEFVAL { -80 }
    ::= { wdmIfEntry 12 }

wdmIfPowerLevelLowThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-380..-60)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The lower threshold for received power level in
        units of 0.1 dBm.

        Note: Depending on the type of board and interface
        the value range may vary.
"
    DEFVAL { -270 }
    ::= { wdmIfEntry 13 }

wdmIfLaserTemp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The pre-set laser temperature in units of
        0.1 degrees centigrade.
"
    ::= { wdmIfEntry 14 }

wdmIfLaserTempOffset OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current offset from the pre-set temperature
        in units of 0.1 degrees centigrade.
"
    ::= { wdmIfEntry 15 }

wdmIfLaserTempOffsetThreshold OBJECT-TYPE
    SYNTAX      Unsigned32 (1..100)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The absolute value of the offset for the LTL
        and LTH alarms in units of 0.1 degrees centigrade.
"
    DEFVAL { 3 }
    ::= { wdmIfEntry 16 }

wdmIfLaserMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    on (1),
                    als (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state of the TX-side laser.

        on - The laser is turned on.

        als - Automatic Laser Shutdown/Turn-Off
        when lossOfSignal is detected on the RX side of
        the trunk interface.
        Note, als is not available for MBA boards.

"
    DEFVAL { als }
    ::= { wdmIfEntry 17 }

wdmIfLaserStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    off (1),
                    on (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state of the TX-side laser.

        off - The laser is turned off.

        on - The laser is turned on.
"
    ::= { wdmIfEntry 18 }

wdmIfAdminStatus OBJECT-TYPE
    SYNTAX      BoardOrInterfaceAdminStatus
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the interface.

        down - The interface is de-activated.

        service - The interface is activated but alarms
        are suppressed. Intended for use during service
        or re-configuration. When service is concluded
        adminStatus should be set to 'up' again. Note that
        there is no difference between 'up' and 'service',
        if no alarms can be raised from this object.

        up - The interface will be activated when
        available. Alarms are not suppressed.

        This attribute can be written via SNMP.
"
    DEFVAL { up }
    ::= { wdmIfEntry 19 }

wdmIfOperStatus OBJECT-TYPE
    SYNTAX      BoardOrInterfaceOperStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the interface.

        notPresent - The interface is not available.

        down - The interface is de-activated or there are
        faults preventing its transition to the 'up' state.

        up - The interface is active.
"
    DEFVAL { notPresent }
    ::= { wdmIfEntry 20 }

wdmIfLossOfSignal OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Loss of signal.
        Applicable for all signal formats.

        A(ctivation): Active loss of signal HW indication.

        D(e-activation): Inactive loss of signal HW
        indication.
"
    ::= { wdmIfEntry 21 }

wdmIfReceivedPowerHigh OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received power level is too high.
        Applicable for all signal formats.

        A: The received power level exceeds the
        the built-in overload threshold.

        D: The received power level is 0.5 dB below the
        overload threshold.
"
    ::= { wdmIfEntry 22 }

wdmIfReceivedPowerLow OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received power level low threshold exceeded.
        Applicable for all signal formats.

        A: The received power level is below the
        associated threshold.

        D: The received power level is 0.5 dB above the
        threshold.
"
    ::= { wdmIfEntry 23 }

wdmIfLaserBiasHigh OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Laser bias high threshold exceeded.

        A: The laser bias current exceeds the associated
        threshold.

        D: The laser bias current is 0.5 mA below the
        associated threshold.
"
    ::= { wdmIfEntry 24 }

wdmIfErroredSeconds OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!

        Errored seconds (ES) threshold exceeded.

        A: The number of errored seconds during a period
        exceeds the associated threshold.

        D: At the start of a new period.
"
    ::= { wdmIfEntry 25 }

wdmIfSeverelyErroredSeconds OBJECT-TYPE
    SYNTAX FaultStatus
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!

        Severely errored seconds (SES) threshold exceeded.

        A: The number of severely errored seconds during
        a period exceeds the associated threshold.

        D: At the start of a new period.
"
    ::= { wdmIfEntry 26 }

wdmIfBackgroundBlockErrors OBJECT-TYPE
    SYNTAX FaultStatus
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!

        Background block errors (BBE) threshold exceeded.

        A: The number of errors during a period
        exceeds the associated threshold.

        D: At the start of a new period.
"
    ::= { wdmIfEntry 27 }

wdmIfUnavailableSeconds OBJECT-TYPE
    SYNTAX FaultStatus
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!

        Unavailable seconds (UAS) threshold exceeded.

        A: The number of unavailable seconds during a
        period exceeds the associated threshold.

        D: At the start of a new period.
"
    ::= { wdmIfEntry 28 }

wdmIfForwardDefectIndication OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Forward defect indication.
        Applicable for all signal formats; detected at
        WDM level. This alarm suppresses the
        corresponding SDH/Sonet alarm.

        A: Forward defect indication (in wrapper) active.

        D: Forward defect indication inactive.
"
    ::= { wdmIfEntry 29 }

wdmIfBackwardDefectIndication OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Backward defect indication.
        Applicable for all signal formats; detected at
        WDM level. This alarm suppresses the
        corresponding SDH/Sonet alarm.

        A: Backward defect indication (in wrapper) active.

        D: Backward defect indication inactive.
"
    ::= { wdmIfEntry 30 }

wdmIfLossOfFrame OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Loss of frame.
        Applicable for SDH/Sonet signal formats.

        A: SDH frame can not be located.

        D: SDH frame is located.
"
    ::= { wdmIfEntry 31 }

wdmIfAlarmIndicationSignal OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm indication signal.
        Applicable for SDH/Sonet signal formats.
        For other proprietary formats, e.g. for
        the tpDDGbE line signal, a similar
        functionality is emulated.

        A: An 'all ones' SDH/Sonet signal is detected.

        D: SDH/Sonet signal recovered.
"
    ::= { wdmIfEntry 32 }

wdmIfRemoteDefectIndication OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Remote defect indication.
        Applicable for SDH/Sonet signal formats.

        A: RDI indication (in SDH/Sonet frame) active.

        D: RDI indication inactive.
"
    ::= { wdmIfEntry 33 }

wdmIfLossOfSync OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Loss of sync (GbE).
        Applicable for GbE signal formats.

        A: GbE 'frame' can not be located.

        D: GbE 'frame' is located.
"
    ::= { wdmIfEntry 34 }

wdmIfLossOfForwardingErrorCorrection OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Loss of forwarding error correction (FEC).
        Applicable for all signal formats.

        A: The interface is unable to perform forwarding
        error correction.

        D: The interface is able to perform FEC.
"
    ::= { wdmIfEntry 35 }

wdmIfLaserTempHigh OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Laser temperature high threshold exceeded.

        A: The temperature exceeds the associated
        threshold.

        D: Temperature is 0.5 degrees centigrade below
        the associated threshold.
"
    ::= { wdmIfEntry 36 }

wdmIfLaserTempLow OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Laser temperatur low threshold exceeded.
        The temperature exceeds the associated threshold.

        A: The temperature exceeds the associated
        threshold.

        D: Temperature is 0.5 degrees centigrade above the
        associated threshold.
"
    ::= { wdmIfEntry 37 }

wdmIfRxPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the port where the interface is
        located.
"
    ::= { wdmIfEntry 38 }

wdmIfBitrateMismatch OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bitrate mismatch (Other).
        Applicable for other signal formats.

        A: The interface is unable to lock to the
        inserted signal.

        D: The interface is able to lock to the inserted
        signal.
"
    ::= { wdmIfEntry 39 }

wdmIfLaserBias OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Measures laser bias current value in tenths of
        mA (0.1 mA).
"
    ::= { wdmIfEntry 40 }

wdmIfLaserBiasThreshold OBJECT-TYPE
    SYNTAX      Unsigned32 (0..300)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Sets the threshold for the laser bias alarm.
        in percentage relative to the value set during
        production. The preset value is 100%.
"
    DEFVAL { 200 }
    ::= { wdmIfEntry 41 }

wdmIfLossOfSignalThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-380..-220)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The upper threshold received power level in
        units of 0.1 dBm.

        Note: Depending on the type of board and interface
        the value range may vary.
"
    DEFVAL { -350 }
    ::= { wdmIfEntry 42 }

wdmIfJ0PathTrace OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (1 | 16))
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "The received J0 path trace string.
        Applicable for SDH/Sonet signal formats.
"
    ::= { wdmIfEntry 44 }

wdmIfInbandMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    down (1),
                    up (2) }
    MAX-ACCESS  read-write
    STATUS      deprecated
    DESCRIPTION
        "The administrative state the in-band management
        channel of the WDM interface.

        down - The management channel is disabled.

        up - The management channel is in use.
"
    DEFVAL { down }
    ::= { wdmIfEntry 45 }

wdmIfInbandStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    down (1),
                    up (2) }
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "The operational state the in-band management
        channel of the WDM interface.

        down - The management channel is disabled.

        up - The management channel is in use.
"
    ::= { wdmIfEntry 46 }

wdmIfExpectedTxLambda OBJECT-TYPE
    SYNTAX      LambdaFrequency
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The expected frequency or wavelength.
        ch871 means the frequency  187.1 THz.
        w1310 means the  wavelength 1310 nm.

        The semantic of this attribute is different depending
        on the laser frequency type.

        Fixed frequency: 'Unexpected frequency' is raised if
        the expected frequency is not equal to actual
        frequency.

        Tunable frequency: The attribute is used to tune the
        laser frequency. 'No frequency set' is raised if no
        frequency has been selected.

        This attribute can be written via SNMP.
"
    DEFVAL { 0 }
    ::= { wdmIfEntry 47 }

wdmIfForwardingErrorCorrectionMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (1),
                    enabled (2),
                    auto (3) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state of the forwarding error
        correction function.

        disabled - FEC is disabled.

        enabled - FEC is activated.

        auto - autonegotiation is used to enable or disable
               FEC.  Note, auto is not available for all
               boards.
"
    DEFVAL { enabled }
    ::= { wdmIfEntry 48 }

wdmIfUnexpectedTxLambda OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual frequency does not match the
        pre-configured frequency.

        A pre-configured frequency of '0' matches
        all actual frequencies.

        A: The configured frequency does not match the
        actual frequency.

        D: The configured frequency matches the actual
        frequency.
"
    ::= { wdmIfEntry 49 }

wdmIfTraceIntrusionMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (1),
                    enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether the trace identifier should be
        inserted in the transmitted signal.

        disabled - The trace identifier is taken from
        the received signal if possible.

        enabled -  The trace identifier set is inserted in
        the transmitted signal.
"
    DEFVAL { disabled }
    ::= { wdmIfEntry 50 }

wdmIfTraceTransmitted   OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..62))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The trace identifier to be transmitted from this
        interface.

        The identifier length is by default 0 to 15. For
        mxp8iiSonet boards the length is 0 to 62.

        Set to empty string for default value:

        <IP address>:<subrack>:<slot>:<port>

        Note that only the last part of the address
        is used.
"
    DEFVAL { "" }
    ::= { wdmIfEntry 51 }

wdmIfTraceReceived OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..62))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The received trace identifier for this interface.
"
    ::= { wdmIfEntry 52 }

wdmIfTraceExpected OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..62))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Trace identifier used for matching against
        the received trace identifier.

        The identifier length is by default 0 to 15. For
        mxp8iiSonet boards the length is 0 to 62.

"
    DEFVAL { "" }
    ::= { wdmIfEntry 53 }

wdmIfTraceAlarmMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (1),
                    enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls if the trace identifier mismatch alarm
        should be raised.

        disabled - The alarm is disabled.

        enabled - The alarm is raised if expected trace
        identifier differs from the received trace
        identifier.
"
    DEFVAL { disabled }
    ::= { wdmIfEntry 54 }

wdmIfTraceMismatch OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the expected trace identifier
        differs from the received trace identifier.

        A: The received trace identifier differs from
        the expected trace identifier.

        D: The identifiers match.
"
    ::= { wdmIfEntry 55 }

wdmIfLaserStatusLastChangeTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time when the laser status was last changed.
"
    ::= { wdmIfEntry 56 }

wdmIfSuppressRemoteAlarms OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (1),
                    enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Suppress AIS, FDI and RDI in the alarm list. The
        fault status on the port object can, however, be
        seen.

        disabled - alarms are not suppressed.

        enabled - alarms are suppressed.
"
    DEFVAL { disabled }
    ::= { wdmIfEntry 57 }

wdmIfSerialNumberMismatch OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Wrong HW unit installed. The link has not been
         optimized for this individual, wrong serial number.

        Action: The installation optimization procedure must
        be run again.

        A: The installed board does not match the
        installation.

        D: The installed board matches the installation.
"
    ::= { wdmIfEntry 58 }


wdmIfOptimizeDecisionThreshold OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Optimize the threshold for distinguishing between
        1s and 0s.

        This attribute is of 'action type' - it will always
        have the value 'normal'

        normal - just a placeholder, is never set by the
                 user.

        run - run the optimization procedure.
"
    DEFVAL { "normal" }
    ::= { wdmIfEntry 59 }

wdmIfThresholdOptimizationState OBJECT-TYPE
    SYNTAX      INTEGER {
                    normal (1),
                    started (2),
                    searchingFirstLow (3),
                    searchingSecondLow (4),
                    searchingThirdLow (5),
                    searchingFirstHigh (6),
                    searchingSecondHigh (7),
                    searchingThirdHigh (8),
                    finishedFailed (9),
                    finishedOk (10),
                    searchingFrameLow(11),
                    searchingFrameHigh(12),
                    foundFrame(13),
                    waitOptimize(14) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "State of the optimization procedure

        normal              - no operation has been started

        started             - the operation is started

        searchingFirstLow   - Search for the first value of
                              the lower flank

        searchingSecondLow  - Search for the second value of
                              the lower flank

        searchingThirdLow   - Search for the third value of
                              the lower flank

        searchingFirstHigh  - Search for the first value of
                              the upper flank

        searchingSecondHigh - Search for the second value of
                              the upper flank

        searchingThirdHigh  - Search for the third value of
                              the upper flank

        finishedOk          - the operation has been
                              successfully finished

        finishedFailed      - the operation failed

        searchingFrameLow   - Search for the frame at the
                              lower end

        searchingFrameHigh  - Search for the frame at the
                              upper end

        foundFrame          - frame has been found

        waitOptimize        - wait for HW-optimize procedure
"
    ::= { wdmIfEntry 60 }

wdmIfUseHwDefaultDecisionThreshold OBJECT-TYPE
    SYNTAX      INTEGER {
                    normal (1),
                    reset (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Use the HW default decision threshold.

        This attribute is of 'action type' - it will always
        have the value 'normal'

        normal - just a placeholder, is never set by the
                 user.

        reset - reset to the default value stored in HW unit.
"
    DEFVAL { normal }
    ::= { wdmIfEntry 61 }

wdmIfFecCorrectedZeros OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of corrected zeros.
"
    ::= { wdmIfEntry 62 }

wdmIfFecCorrectedOnes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of corrected ones.
"
    ::= { wdmIfEntry 63 }

wdmIfOptimizedForSerialNumber   OBJECT-TYPE
    SYNTAX      DisplayString (SIZE(0..40))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This field contains the serial number of
        the transponder unit, for which the decision
        threshold is optimized and stored.
"
    ::= { wdmIfEntry 64 }

wdmIfRelativeDecisionThreshold  OBJECT-TYPE
    SYNTAX      Integer32 (-1000000..1000000)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The 1/0 decision threshold computed at installation.

        The threshold is automatically optimized if the
        'Continous optimization' functionality is enabled.
        Press the 'Optimize' button to manually trigger
        the optimization routine once.
"
    ::= { wdmIfEntry 65 }

wdmIfTrxCodeMismatch OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The inserted transceiver does not have a legal
        manufacturer code.

        A: A transceiver with illegal code is inserted.

        D: The transceiver with illegal code is removed.
"
    ::= { wdmIfEntry 66 }

wdmIfTrxBitrateUnavailable OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The inserted transceiver does not support the
        requested bit rate.

        A: A transceiver with other supported bit rate than
           the requested bit rate is inserted.

        D: The transceiver with the non-requested bit rate is
           removed, or the object is reconfigured.
"
    ::= { wdmIfEntry 67 }

wdmIfTrxMissing OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An expected transceiver is missing.

        A: An expected transceiver is missing.

        D: The missing transceiver is inserted.
"
    ::= { wdmIfEntry 68 }

wdmIfTrxClass OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "String that describes the transceiver class.
"
    DEFVAL { "" }
    ::= { wdmIfEntry 69 }

wdmIfLaserTempHighRelativeThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-100..100)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Threshold for the Laser Temp High alarm, expressed
         as an offset from a pre-set nominal temperature,
         in units of 0.1 degrees centigrade.
"
    DEFVAL { 10 }
    ::= { wdmIfEntry 70 }

wdmIfLaserTempLowRelativeThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-100..100)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Threshold for the Laser Temp Low alarm, expressed
         as an offset from a pre-set nominal temperature,
         in units of 0.1 degrees centigrade.
"
    DEFVAL { -10 }
    ::= { wdmIfEntry 71 }

wdmIfTransmitterFailed OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The transceiver HW has detected a failure
         in the transmitter part.

        A: The transmitter has failed

        D: The transmitter is OK again
"
    ::= { wdmIfEntry 72 }

wdmIfReceiverSensitivity OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The lowest power that the receiver is guaranteed
        to handle, in units of 0.1 dBm.
"
    ::= { wdmIfEntry 73 }

wdmIfPowerLevelLowRelativeThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-50..100)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The lower threshold for received power level in
        units of 0.1 dB, relative to the receiver
        sensitivity, or loss of signal threshold.

        - For fixed transceivers it is measured with respect
          to the loss threshold.
        - For pluggable transceivers (SFP/XFP) it is
          measured with respect to the receiver sensitivity.

"
    DEFVAL { 30 }
    ::= { wdmIfEntry 74 }

wdmIfIllegalFrequency OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The frequency supported by the transceiver is not
        recognized by the system.

        A: Invalid or unknown frequency information is
           detected in the (pluggable) transceiver or
           board production data.

        D: The (pluggable) transceiver or board is
           replaced.
"
    ::= { wdmIfEntry 75 }

wdmIfLaserForcedOn OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (1),
                    enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When enabled, the laser is on regardless of whether
         there is a client signal or not.

        disabled - the laser is only on when there is a
                   client signal.

        enabled  - the laser is always on. This is intended
                   to be used at installation to generate
                   light on the WDM-side for the QMR board
                   even if there are not client equipment
                   available.
"
    DEFVAL { disabled }
    ::= { wdmIfEntry 76 }

wdmIfTrafficCombination OBJECT-TYPE
    SYNTAX INTEGER {
        undefined (0),
        fcGbEx2 (1),
        fc2G (2),
        dvbEsconx8 (3),
        esconx10 (4),
        framedGbEx10 (5),
        esconx6FcGbE (6),
        dvbEsconx4FcGbE (7),
        mixed (8),
        framedGbEx10Vc4 (9),
        fcx2VcatVc4 (10),
        fc2GVcatVc4 (11),
        dvbEsconx8VcatVc4 (12),
        mixedVcatVc4 (13),
        fcGbEx2VcatVc4 (14),
        gbEx3Stm1x5 (15),
        gbEx2Stm4x2Stm1x4 (16),
        gbEx3Stm4Oc12x1Stm1Oc3x3 (17),
        gbEx1Stm16Oc48x1Stm1Oc3x3 (18),
        gbEx4x2 (19),
        gbEx4Stm16Oc48x2 (20),
        stm16Oc48x4 (21),
        gbEx2Fcx2x2 (22),
        gbESyncEx3Stm4Oc12Stm1Oc3x1 (23),
        gbEStm16Oc48Stm4Oc12Stm1Oc3 (24),
        gbEStm16Oc48 (25),
        syncEx14GLinex2 (26),
        syncEx10 (27),
        gbEx3Stm4Oc12x1Stm1Oc3x3Basic (28),
        gbESyncEx3Stm4Oc12Stm1Oc3x1Basic (29),
        cpri3x3syncEx2 (30),
        syncEx1Cpri4x3 (31)}
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
      "Specifies how different traffic formats can be
      combined on different ports on gxp2500, gxp2500Sfp,
      fhmxp10g, msMxp10G and msMxp.  All combinations
      can not be used on all boards.

        undefined            - no format selected yet

        fcGbEx2              - 2 ports can carry either GbE
                               or Fc, or 1 port can carry 2G
                               Fc.

        dvbEsconx8           - 8 ports can carry either DvB,
                               Escon, or Escon-LL.

        esconx10             - 10 ports can carry Escon.

        framedGbEx10         - 10 ports can carry a rate
                               limited GbE.

        esconx6FcGbE         - 6 ports can carry Escon,
                               combined with 1 port carrying
                               Fc or GbE.

        dvbEsconx4FcGbE      - 4 ports can carry DvB, Escon,
                               Escon-LL, combined with 1 port
                               carrying Fc or GbE.

        mixed                - All ports can be freely
                               combined, for experimental
                               use.

        framedGbEx10Vc4      - Like framedGbEx10, but uses
                               VC4s instead of VC-3

        fcx2VcatVc4          - 2 x FC over VCAT VC-4


        fc2GVcatVc4          - 2G FC over VCAT VC-4

        dvbEsconx8VcatVc4    - Like dvbEsconx8, but uses VCAT
                               VC-4 instead

        mixedVcatVc4         - Like mixed but uses VCAT VC-4
                               instead

        fcGbEx2VcatVc4       - Like fcGbEx2 but uses VCAT
                               VC-4 instead


        gbEx3Stm4Oc12x1Stm1Oc3x3 - 3 ports carry GbE, 1 port can
                               carry Stm4/Oc12 or Stm1/Oc3, 3 ports
                               can carry only Stm1/Oc3 (MsMxp)

        gbEx1Stm16Oc48x1Stm1Oc3x3 - 1 port carries GbE, 1 port
                               carries Stm16/Oc48, 3 ports carry
                               Stm1/Oc3 (MsMxp)

        gbESyncEx3Stm4Oc12Stm1Oc3x1 - 3 ports carry synchron GbE, 1
                               port can carry Stm4/Oc12 or Stm1/Oc3
                               (MsMxp)


        gbEx4Stm16Oc48x2     - 4 ports carry GbE, 2 ports
                               carry Stm16/Oc48 (MsMxp10GTCEr,
                               MsMxp10G)

        stm16Oc48x4          - 4 ports carry Stm16/Oc48
                               (MsMxp10GTCEr, MsMxp10G)


        gbEx4x2              - 2 x (4 ports carry GbE)
                               (MsMxpDQgbe)

        gbEx2Fcx2x2          - 2 x (2 ports carry GbE, 2
                               ports carry 1G Fc or 1 port
                               carries 2G Fc) (MsMxpDQgbe)

        gbEStm16Oc48Stm4Oc12Stm1Oc3 - 2 transponders carry GbE or
                               Stm16/Oc48, 2 transponders carry
                               Stm4/Oc12 or Stm1/Oc3 (MsMxpQMS2G5)

        gbEStm16Oc48         - 4 transponders carry GbE or
                               Stm16/Oc48 (MsMxpQMS2G5)

        syncEx14GLinex2        - 2 ports carry 4G MSMXP Lines
                               w. Protection, 1 port carry
                               Synchron GbE (MsMxp10G)

        syncEx10             - 10 ports carry synchron GbE
                               (MsMxp10G)

        gbEx3Stm4Oc12x1Stm1Oc3x3Basic - Like gbEx3Stm4Oc12x1Stm1Oc3x3,
                                        with an alternative processing
                                        of the STM client signal (MsMxp)

        gbESyncEx3Stm4Oc12Stm1Oc3x1Basic - Like
                                           gbESyncEx3Stm4Oc12Stm1Oc3x1,
                                           with an alternative processing
                                           of STM client signal (MsMxp)
        cpri3x3syncEx2         - 3 ports carry cpri3 (2457.6 Mbps),
                                 2 ports carry synchron GbE (fhmxp10g)


        syncEx1Cpri4x3         - 3 ports carry cpri4 (3072.0 Mbps),
                                 1 port carries synchron GbE (fhmxp10g)

"
    DEFVAL { undefined }
    ::= { wdmIfEntry 77 }

wdmIfSelectTrafficCombination OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Select the traffic combination of the board.
"
    DEFVAL { "normal" }
    ::= { wdmIfEntry 78 }

wdmIfObjectProperty  OBJECT-TYPE
    SYNTAX      ObjectProperty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Property mask.
"
    ::= { wdmIfEntry 79 }

wdmIfTxPowerLevel OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The transmitted power level in units of 0.1 dBm.
"
    ::= { wdmIfEntry 80 }

wdmIfLaserTempActual OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual laser temperature in units of
        0.1 degrees centigrade.
"
    ::= { wdmIfEntry 81 }

wdmIfTrxFailed OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A transceiver hardware failure is detected.

        A: The transceiver fails.

        D: The transceiver is replaced.
"
    ::= { wdmIfEntry 82 }

wdmIfDisabled OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface is disabled.

        A: Interface disabled.

        D: Interface enabled.
"
    ::= { wdmIfEntry 83 }

wdmIfLoopback OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface is set in loopback mode.

        A: Loopback enabled.

        D: Loopback disabled.
"
    ::= { wdmIfEntry 84 }

wdmIfContinousOptimization OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (1),
                    enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Continuously run the optimization of the
         decision threshold.

         enabled - continous optimization enabled.

         disabled - continous optimization disabled.
"
    DEFVAL { enabled }
    ::= { wdmIfEntry 85 }

wdmIfThresholdOptimizationResultCause OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This text descibes the cause why the latest
         optimize command failed. The information
         is only valid if the thresholdOptimizationState
         is finishedFailed.
"
    ::= { wdmIfEntry 86 }

wdmIfDistributionRole OBJECT-TYPE
    SYNTAX      INTEGER {
                    terminalMultiplexor (1),
                    broadcastHub (2),
                    broadcastSatellite (3) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Note: This attribute is deprecated!
         The distribution role of the line function

        - terminal: normal point-to-point operation
        - broadcastHub: broadcast on both lines.
        - broadcastSatellite: drop-and-continue
"
    DEFVAL { terminalMultiplexor }
    ::= { wdmIfEntry 87 }

wdmIfConfigurationCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Note: This attribute is deprecated!

        Select the operation mode of the line.

        - terminal: normal point-to-point operation
        - broadcastHub: broadcast on both lines.
        - broadcastSatellite: drop-and-continue
"
    ::= { wdmIfEntry 88 }

wdmIfNoFrequencySet OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tunable laser frequency has not been set.

        A: Tunable laser frequency is not set.

        D: Tunable laser frequency is set.
"
    ::= { wdmIfEntry 89 }

wdmIfFormat OBJECT-TYPE
    SYNTAX      SignalFormat
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The signal format

         FecLan10GbE1A: iWDM 10G framing format type 1A (TPD10GBE-BU, GBE9/MXP10GFEC)
         FecLan10GbE1B: iWDM 10G framing format type 1B (TPQ10GFEC, MS-MXP/10G,TPQ10GFECI)
         Iwdm40Gb:  iWDM 40G framing format (MS-TP/40G, MS-MXP/40G)

"
    DEFVAL { gbE }
    ::= { wdmIfEntry 90 }

wdmIfConfigurationFormatCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION

        "Command to change the signal format.

        OTU-2 can be selected.

"
    ::= { wdmIfEntry 91 }

wdmIfOHTransparency OBJECT-TYPE
    SYNTAX      INTEGER {
                    off (1),
                    on (2) }
    MAX-ACCESS  read-write
    STATUS      deprecated
    DESCRIPTION
       "Note: This attribute is deprecated!

        The 'OH Transparency' setting shall be set to 'off'
        for standard SDH/SONET mode. Does not work when
        client wrapper is enabled.

        When set to 'on', the following tributary OH-bytes
        are transparently transported: E1, E2, F1, D1-D3,
        D4-D12, K1, K2, J0

        The following are always terminated:
        B1, B2, M1 and S1.
"
   DEFVAL { off }
   ::= { wdmIfEntry 92 }

wdmIfLinkDown OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!

        A: Failure to negotiate a connection with the
        other party.

         D: A connection is negotiated.
"
    ::= { wdmIfEntry 93 }

wdmIfAutoNegotiationMode OBJECT-TYPE
    SYNTAX      INTEGER {
                         off (1),
                         on (2) }
    MAX-ACCESS  read-write
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!


        The wanted auto negotiation mode.

        off - auto negotiation process is disabled

        on - auto negotiation process is enabled
"
    DEFVAL { on }
    ::= { wdmIfEntry 94 }

wdmIfAutoNegotiationStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                         incomplete (1),
                         half (2),
                         full (3) }
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!


        The result of the auto negotiation procedure

         incomplete - the result is still pending

         half - half duplex

         full - full duplex
"
    ::= { wdmIfEntry 95 }

wdmIfFlowControlMode OBJECT-TYPE
    SYNTAX      INTEGER {
                        noPause (1),
                        rxPause (2),
                        txPause (3),
                        bothPause (4) }
    MAX-ACCESS  read-write
    STATUS      deprecated
    DESCRIPTION
        "Note: This attribute is deprecated!


        The mode for handling of pause messages between
        the peers.

         noPause - PAUSE frame reception and
         transmission is disabled

         rxPause - PAUSE frame reception is enabled
         (asymmetric)

         txPause - PAUSE frame transmission is enabled
         (asymetric)

         bothPause - send and accept pause messages
         (symmetric).
"
    DEFVAL { noPause }
    ::= { wdmIfEntry 96 }

wdmIfGroupLineMode OBJECT-TYPE
    SYNTAX      INTEGER {
                        disabled (1),
                        enabled (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether the interface is grouped
        into a ROADM-Line-group.

        disabled - The interface is not grouped into
        a ROADM-Line-group.

        enabled - The interface is grouped into a
        ROADM-Line-group.
"
    DEFVAL { disabled }
    ::= { wdmIfEntry 97 }

wdmIfFecType OBJECT-TYPE
    SYNTAX      INTEGER {
                        enhancedFec (1),
                        g709Fec (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set type of FEC.

        enhancedFec - 2nd generation Enhanced FEC
                      (rate compatible with G.709
                      but > 2 dB additional coding
                      gain)

        g709 -        1st  generation FEC according to
                      ITU-T G.709 (near 6 dB coding gain).
"
    DEFVAL { enhancedFec }
    ::= { wdmIfEntry 98 }

wdmIfFarEndLoopback OBJECT-TYPE
    SYNTAX      INTEGER {
                        disabled (1),
                        enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Loop the wdm RX signal back to the wdm TX.
        This is also called Facility loopback.

        disabled - the signal is not looped

        enabled - the signal is looped.
"
    DEFVAL { disabled }
    ::= { wdmIfEntry 99 }

wdmIfFarEndLoopbackTimeout OBJECT-TYPE
    SYNTAX      Integer32 (0..1200)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Timeout for far end loopback (a.k.a. facility
        loopback) to make sure connection to node is
        not lost if ppp link is broken.
        The unit is hour. 0.1 means 6 minutes.
"
    DEFVAL { 10 }
    ::= { wdmIfEntry 100 }

wdmIfFarEndLoopbackEnabled OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Far end loopback (a.k.a. facility loopback)
        is enabled.
"
    ::= { wdmIfEntry 101 }

wdmIfChangeLoopbackCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Command to change Loopback and loopbacktimeout
"
    ::= { wdmIfEntry 102 }

wdmIfFecFailure OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Too many errors to get valid FEC statistics. It is not
         possible to run control loops based on FEC statistics.

        A: Too many errors to get valid FEC statistics.

        D: The FEC statistics is valid.

"
    ::= { wdmIfEntry 103 }

wdmIfTxSignalStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                        down (1),
                        degraded (2),
                        up (3),
                        notApplicable (2147483647) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for outgoing (TX) signal
        of the interface.

        down - A fault that would lead to a protection
        switch has occurred.

        degraded - The signal quality is impaired.

        up - The signal is OK.

        notApplicable (2147483647) - Attribute is not used on board.
"
    ::= { wdmIfEntry 104 }

wdmIfRxSignalStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                        down (1),
                        degraded (2),
                        up (3),
                        notApplicable (2147483647) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for incoming (RX) signal
        of the interface.

        down - A major fault has occurred

        degraded - The signal quality is impaired.

        up - The signal is OK.

        notApplicable (2147483647) - Attribute is not used on board
"
    ::= { wdmIfEntry 105 }

wdmIfNearEndLoopback OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (1),
                    enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Loop the wdm TX signal back to the wdm RX.
        This is also called Terminal loopback.

        disabled - the signal is not looped

        enabled - the signal is looped.
"
    DEFVAL { disabled }
    ::= { wdmIfEntry 106 }

wdmIfNearEndLoopbackTimeout OBJECT-TYPE
    SYNTAX      Integer32 (0..1200)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Timeout for near end loopback (a.k.a. terminal
        loopback) to make sure connection to node is
        not lost if ppp link is broken.
        The unit is hours. 0.1 means 6 minutes.
"
    DEFVAL { 10 }
    ::= { wdmIfEntry 107 }

wdmIfNearEndLoopbackEnabled OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Near end loopback (a.k.a. terminal loopback)
        is enabled.
"
    ::= { wdmIfEntry 108 }

wdmIfChangeNearEndLoopbackCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Command to change near end loopback (a.k.a.
        terminal loopback) and loopbacktimeout
"
    ::= { wdmIfEntry 109 }

wdmIfSignalDegraded OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Too many error corrected.
        If more errors are corrected
        it might effect the client signal
        in a negative way.
"
    ::= { wdmIfEntry 110 }

wdmIfHubProtectionMode OBJECT-TYPE
    SYNTAX      INTEGER {
                         disabled (1),
                         enabled (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Choose if the line should be used in
        hub protection. (needs to be setup in mba)

        disabled - Port is not in hub protection (default)

        enabled - Hub protection is enabled

"
    DEFVAL { disabled }
    ::= { wdmIfEntry 111 }

wdmIfActualFormat OBJECT-TYPE
    SYNTAX      SignalFormat
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual signal format.

"
    DEFVAL { unused }
    ::= { wdmIfEntry 112 }

wdmIfTdcDispersion OBJECT-TYPE
    SYNTAX      Integer32 (-800..800)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Shows current dispersion value in the unit.

"
    DEFVAL { 0 }
    ::= { wdmIfEntry 113 }

wdmIfTdcDispersionCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Sets state and dispersion in the unit.
"
    ::= { wdmIfEntry 114 }

wdmIfTdcDispersionMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    auto (0),
                    startValue(1),
                    manual (2) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "States the current mode of the TDC dispersion.

        Mode:
            auto - The TDC is in auto optimising state.

            startValue - Starts with the desired value,
                         then it goes to auto afterward.

            manula - The TDC is in manual/forced mode.

"
    DEFVAL { auto }
    ::= { wdmIfEntry 115 }

wdmIfLineControlLoopCurrentState OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current state of the unit. The unit
        is ready if state=Automatic fine-tuning.

        The states could be one of the followings:

        Init DPSK.

        DPSK Tx Locked.

        Init EDFA.

        Coarse-tune DPSK.

        Coarse-tune TDC, first step.

        Coarse-tune TDC, second step.

        Fine-tune TDC.

        Fine-tune DPSK.

        TDC in manual/forced mode.

        Automatic fine-tuning.

"
    DEFVAL { "" }
    ::= { wdmIfEntry 116 }

wdmIfSignalDegradeThreshold OBJECT-TYPE
    SYNTAX      BerLevel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Threshold for signal degraded alarm,
         based on BER value.

         The threshold is settable on the first
         line port, affecting all line ports.

         Predefined BER levels
         rxBerLevel1 = 1E-12
         rxBerLevel2 = 1E-13
         rxBerLevel3 = 1E-15

"
    DEFVAL { rxBerLevel2 }
    ::= { wdmIfEntry 117 }

wdmIfTrxThresholdOptimizationState OBJECT-TYPE
    SYNTAX      INTEGER {
                        idle (1),
                        searching (2),
                        optimizing (3),
                        steadyState (4),
                        failedTrafficLoss (5),
                        failedLOS (6) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Threshold Optimization State.

        Idle                - No regulation done

        Searching           - Rough threshold regulation
                              is being performed

        Optimizing          - Fine threshold regulation
                              is being performed

        Steady State        - Regulation at steady state

        Failed(trafficLoss) - Failure due to traffic loss
                              (FEC failure, Loss of sync,
                              LOF, Bitrate Mismatch)

        Failed(LOS)         - Failure due to loss of singal

"
    DEFVAL { idle }
    ::= { wdmIfEntry 118 }

wdmIfTrxDecisionThreshold OBJECT-TYPE
    SYNTAX       Integer32 (0..100)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The threshold is automatically optimized if the
        'Continous optimization' functionality is enabled.
        Press the 'Optimize' button to manually trigger
        the optimization routine once.

"
    DEFVAL { 0 }
    ::= { wdmIfEntry 119 }

wdmIfSwControlledLaserShutdown OBJECT-TYPE
    SYNTAX      INTEGER {
                        disabled (1),
                        enabled (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "enabled - the laser is turned off for a specified time, until the timer expires
         disabled - normal state

"
    DEFVAL { disabled }
    ::= { wdmIfEntry 120 }

wdmIfChangeSwControlledLaserShutdownCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Command to shut the laser down for a user-defined period of time.
         This setting will over-ride other laser control functions like ALS and protection.
         The timer can be selected in minutes: [1..30] minutes.
         When the timer expires, the normal laser control functions will take over
         the control of the laser.
         When in the shut-down state, the same command can be issued to cancel the
         operation prematurely.

"
    ::= { wdmIfEntry 121 }

wdmIfControlledLaserShutdownEnabled OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Software controlled laser shutdown is enabled.
         Activation - When the command is successfully applied.
         De-activation - When the timer expires, or the operation is manually cancelled.

"
    ::= { wdmIfEntry 122 }

wdmIfAid OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The access identifier (AID) of the interface.
         The format is according to GR-833.

"
    ::= { wdmIfEntry 123 }

wdmIfPhysicalLocation OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The physical location of the if.

"
    ::= { wdmIfEntry 124 }

wdmIfTrxTunable OBJECT-TYPE
    SYNTAX      TruthValueWithNA
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The transceiver is of tunable type.

"
    DEFVAL { notAvailable }
    ::= { wdmIfEntry 125 }

-- ----------------------------------------------------
-- WDM VC-4 objects
-- ----------------------------------------------------

wdmVc4Table  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmVc4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The interface list."
    ::= { wdmVc4List 1 }

wdmVc4Entry OBJECT-TYPE
    SYNTAX      WdmVc4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the interface list.
"
    INDEX { wdmVc4Index }
    ::= { wdmVc4Table 1 }

WdmVc4Entry ::=
    SEQUENCE {
        wdmVc4Index                     Unsigned32,
        wdmVc4Name                      MgmtNameString,
        wdmVc4Descr                     DisplayString,
        wdmVc4Subrack                   SubrackNumber,
        wdmVc4Slot                      SlotNumber,
        wdmVc4TxPort                    PortNumber,
        wdmVc4RxPort                    PortNumber,
        wdmVc4Vc4                       Unsigned32,
        wdmVc4ObjectProperty            ObjectProperty,
        wdmVc4AuAlarmIndicationSignal   INTEGER,
        wdmVc4AuLossOfPointer           INTEGER,
        wdmVc4RxSignalStatus            INTEGER,
        wdmVc4ConcatenationStatus       INTEGER,
        wdmVc4PayloadStatus             INTEGER,
        wdmVc4ConnectionStatus          DisplayString,
        wdmVc4ConnectedForeignIndex     Unsigned32,
        wdmVc4AdminStatus               INTEGER }

wdmVc4Index OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An arbitrary index assigned to each if entry.
"
    ::= { wdmVc4Entry 1 }

wdmVc4Name OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the vc4/vc3/STS-1,
        e.g. vc4:1:2:1-2:8 where the first number
        indicates sub-rack, the second slot number and
        the third/forth are the port numbers. The last
        number is the vc4/vc3/STS-1 id within the STM16/STS-48 frame.
"
    ::= { wdmVc4Entry 2 }

wdmVc4Descr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.
"
    DEFVAL { "" }
    ::= { wdmVc4Entry 3 }

wdmVc4Subrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the subrack where the vc4/vc3/STS-1 is
        located.
"
    ::= { wdmVc4Entry 4 }

wdmVc4Slot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the slot where the vc4/vc3/STS-1 is
        located.
"
    ::= { wdmVc4Entry 5 }

wdmVc4TxPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the port where the TX-side of the
        vc4/vc3/STS-1 is located.
"
    ::= { wdmVc4Entry 6 }

wdmVc4RxPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the port where the RX-side of the
        vc4/vc3/STS-1 is located.
"
    ::= { wdmVc4Entry 7 }

wdmVc4Vc4 OBJECT-TYPE
    SYNTAX      Unsigned32 (1..64)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The id of the vc4 within the STM64/STM16 frame.  When
        Sonet, the id of the STS3 within the OC192/OC48 frame.
"
    ::= { wdmVc4Entry 8 }

wdmVc4ObjectProperty OBJECT-TYPE
    SYNTAX      ObjectProperty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Property mask.
"
    ::= { wdmVc4Entry 9 }

wdmVc4AuAlarmIndicationSignal OBJECT-TYPE
    SYNTAX      INTEGER {
                    ok (1),
                    alarm (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm indication signal (AIS).
        Applicable for SDH/Sonet signal formats.

        A: An 'all ones' SDH/Sonet signal is detected.

        D: SDH/Sonet signal recovered.
"
    ::= { wdmVc4Entry 10 }

wdmVc4AuLossOfPointer OBJECT-TYPE
    SYNTAX      INTEGER {
                    ok (1),
                    alarm (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Loss of pointer detected.
        Applicable for SDH/Sonet signal formats.

        A: A loss of pointer is detected.

        D: SDH/Sonet signal recovered.
"
    ::= { wdmVc4Entry 11 }

wdmVc4RxSignalStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                        down (1),
                        degraded (2),
                        up (3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for incoming (RX) signal
        of the interface.

        down - A fault that would lead to a protection
        switch has occurred.

        degraded - The signal quality is impaired.

        up - The signal is OK.

"
    ::= { wdmVc4Entry 12 }

wdmVc4ConcatenationStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                        on (1),
                        off (2),
                        vc3 (3),
                        vc4 (4),
                        vc4x4c (5),
                        vc4x16c (6),
                        vc4x64c (7),
                        sts1 (8),
                        sts3c (9),
                        sts12c (10),
                        unknown (11) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Describes if and how the VC4s are concatenated.
        In case of sonet how the STS3s are concatenated.

        on - Concatenation is used.

        off - No concatenation is used.

        can be vc3 (STS-1),
        vc4 (STS-3, STM1/OC3),
        vc4x4c (STM4/OC12),
        vc4x16c (STS-48, STM16/OC48),
        vc4x64c (STS-128, STM64/OC192),
        sts1    (STS-1),
        sts3c   (STS-3 concatenated),
        sts12c  (STS-12 concatenated).
"
    ::= { wdmVc4Entry 13 }

wdmVc4PayloadStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                        equipped (1),
                        unequipped (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Describes if the VC4/STS3 contains payload.

        equipped - VC4/STS3 contains payload.

        unequipped - No payload in VC4/STS3.
"
    ::= { wdmVc4Entry 14 }

wdmVc4ConnectionStatus OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the name of the client port and
        possible subchannel/vc4 that it is connected to
        or Unconnected if not connected.
"
    DEFVAL { "Not connected" }
    ::= { wdmVc4Entry 15 }

wdmVc4ConnectedForeignIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Shows the index of the connected interface.
        Or 0 if unconnected.
"
    DEFVAL { 0 }
    ::= { wdmVc4Entry 16 }

wdmVc4AdminStatus OBJECT-TYPE
    SYNTAX  INTEGER {
        down (1),
        up (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the vc4/vc3/STS-1.

        down - The vc3/vc4/STS-1 should not be used. This
        vc4/vc3 will not affect rxSignalStatus.

        up - The vc4/vc3/STS-1 should be used. This
        vc4/vc3/STS-1 will affect rxSignalStatus.
"
    DEFVAL { up }
    ::= { wdmVc4Entry 17 }

-- ----------------------------------------------------
-- Protection group definitions
-- ----------------------------------------------------

wdmProtTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmProtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The protection group table."
    ::= { wdmProtList 1 }

wdmProtEntry OBJECT-TYPE
    SYNTAX      WdmProtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the protection group table.
"
    INDEX { wdmProtIndex }
    ::= { wdmProtTable 1 }

WdmProtEntry ::=
    SEQUENCE {
        wdmProtIndex                    Unsigned32,
        wdmProtName                     MgmtNameString,
        wdmProtDescr                    DisplayString,
        wdmProtRightSubrack             SubrackNumber,
        wdmProtRightSlot                SlotNumber,
        wdmProtRightPort                PortNumber,
        wdmProtLeftSubrack              SubrackNumber,
        wdmProtLeftSlot                 SlotNumber,
        wdmProtLeftPort                 PortNumber,
        wdmProtLastChangeTime           DateAndTime,
        wdmProtAdminStatus              INTEGER,
        wdmProtOperStatus               INTEGER, -- deprecated
        wdmProtRowStatus                RowStatus,
        wdmProtServiceDegraded          FaultStatus,
        wdmProtServiceFailure           FaultStatus,
        wdmProtActiveSide               INTEGER,
        wdmProtLeftStatus               INTEGER,
        wdmProtRightStatus              INTEGER,
        wdmProtProtectionType           INTEGER,
        wdmProtObjectProperty           ObjectProperty,
        wdmProtWrapperMode              INTEGER,
        wdmProtWrapperState             INTEGER,
        wdmProtLeftCommSubrack          SubrackNumber,
        wdmProtLeftCommSlot             SlotNumber,
        wdmProtLeftCommPort             PortNumber,
        wdmProtRightCommSubrack         SubrackNumber,
        wdmProtRightCommSlot            SlotNumber,
        wdmProtRightCommPort            PortNumber,
        wdmProtLeftCommInterface        DisplayString,
        wdmProtRightCommInterface       DisplayString,
        wdmProtCommunicationFailure     FaultStatus,
        wdmProtHubTrafficConfigMismatch FaultStatus,
        wdmProtSignalDegradeProtection  INTEGER,
        wdmProtRevertiveSwitch          INTEGER,
        wdmProtRevertiveSwitchWtrTimer  Unsigned32,
        wdmProtRevertiveSwitchPrimaryPath INTEGER,
        wdmProtRevertiveSwitchSecondaryPath INTEGER,
        wdmProtSecondaryPathUsed        FaultStatus }

wdmProtIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An arbitrary index assigned to each entry.
"
    ::= { wdmProtEntry 1 }

wdmProtName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The name of the object, for example
        'pg:1:2:1-2:1:3:1-2', where the numbers indicate the
        subrack, slot and TX-RX port number for the left and
        right interface of the group.

        The interface is either a client interface
        or a line interface. This depends on the
        protection type, which in turn depends on
        the type of board that is involved.
"
    DEFVAL { "" }
    ::= { wdmProtEntry 2 }

wdmProtDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.
"
    DEFVAL { "" }
    ::= { wdmProtEntry 3 }

wdmProtRightSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number for this protected
        interface.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 4 }

wdmProtRightSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number for this protected
        interface.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 5 }

wdmProtRightPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The port number for the TX side of this
        protected interface. Not necessary for boards
        with only one interface.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 6 }

wdmProtLeftSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number for the other protected
        interface.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 7 }

wdmProtLeftSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number for the other protected
        interface.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 8 }

wdmProtLeftPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The port number for the TX side of the other
        protected interface. Not necessary for boards
        with only one interface.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 9 }

wdmProtLastChangeTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time the operational state of the protection
        group was last changed.
"
    ::= { wdmProtEntry 10 }

wdmProtAdminStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    down (1),
                    leftForced (2),
                    rightForced (3),
                    auto (4),
                    toggle (5) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the protection
        group.

        down - The protection group is inactive.

        leftForced - The left side is forced active.

        rightForced - The right side is forced active.

        auto - The application choses which side should
        be active. This should be the normal mode of
        operation.

        toggle - Switch the active side of the protection
        group. Only applicable in 'auto' state.
        After setting adminStatus to 'toggle' it again
        receives the 'auto' value.
"
    DEFVAL { down }
    ::= { wdmProtEntry 11 }

wdmProtOperStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    bothDown (1),
                    leftDownRightUp (2),
                    leftDownRightStandby (3),
                    leftStandbyRightDown (4),
                    leftStandbyRightUp (5),
                    leftUpRightDown (6),
                    leftUpRightStandby (7) }
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "Note: This object is deprecated.

        The operational state for the protection group.

        bothDown - both sides down

        leftDownRightUp - left side down, right side up

        leftDownRightStandby - left side down (forced),
        right side standby

        leftStandbyRightDown - left side standby, right
        side down (forced)

        leftStandbyRightUp - left side standby, right
        side up

        leftUpRightDown - left side up, right side down

        leftUpRightStandby - left side up, right side
        standby
"
    ::= { wdmProtEntry 12 }

wdmProtRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Manages creation and deletion of conceptual rows.
        See also SNMPv2-TC.
"
    ::= { wdmProtEntry 13 }

wdmProtServiceDegraded OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection service degraded.

        A: One of the sides of the protection group has
        signal failure or one side is forced active.

        D: Both sides of the protection group are
        available (The signal is recovered or the
        forced active side is released).
"
    ::= { wdmProtEntry 14 }

wdmProtServiceFailure OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection service failure.

        A: Both sides of the protection group has
        signal failure or the side that is forced
        active has signal failure.

        D: One of the sides of the protection group
        is available (The signal is recovered or the
        forced active side is released).
"
    ::= { wdmProtEntry 15 }

wdmProtActiveSide OBJECT-TYPE
    SYNTAX      INTEGER {
                    none (1),
                    left (2),
                    right (3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the protection group.

        none - no side is active.

        left - the left side is active.

        right - the right side is active.
"
    ::= { wdmProtEntry 16 }

wdmProtLeftStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    down (1),
                    up (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the left side of the
        protection group.

        down - this side is out of service.

        up - this side is active.
"
    ::= { wdmProtEntry 17 }

wdmProtRightStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    down (1),
                    up (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the right side of
        the protection group.

        down - this side is out of service.

        up - this side is active.
"
    ::= { wdmProtEntry 18 }

wdmProtProtectionType OBJECT-TYPE
    SYNTAX      INTEGER {
                    eqAndFiberProtection (0),
                    fiberProtectionI (1),
                    fiberProtectionII (2),
                    fiberProtectionIII (3),
                    fiberProtectionIIII (4),
                    singleEndedBiDirLineProtection (5)}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Different types of protection realisation
         and modelling. The information is intended
         for the TNM.

        eqAndFiberProtection - multiple boards,
                               e.g. two tpMr2500 or tpMr25v2

        fiberProtectionI     - single board,
                               e.g. FPU

        fiberProtectionII    - single board,
                               e.g. QMR

        fiberProtectionIII   - single board,
                               e.g. tpDDGbE

        fiberProtectionIIII  - single board,
                               e.g. tpD10GbE

        singleEndedBiDirLineProtection - single ended bidirection line protection,
                                         e.g. fhau1

"
    ::= { wdmProtEntry 19 }


wdmProtObjectProperty OBJECT-TYPE
    SYNTAX      ObjectProperty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Property mask.
"
    ::= { wdmProtEntry 20 }

wdmProtWrapperMode OBJECT-TYPE
    SYNTAX      INTEGER {
                    followTraffic (1),
                    fixedToDefault (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines how the wrapper will operate
         in correlation with the traffic switch.

         followTraffic - use the same interface
         as the traffic is selected from.

         fixedToDefaultInterface - wrapper is fixed
         to the predefined interface. This is the
         interface over which the PPP-link is defined.
"
    DEFVAL { followTraffic }
    ::= { wdmProtEntry 21 }

wdmProtWrapperState OBJECT-TYPE
    SYNTAX      INTEGER {
                    none (1),
                    left (2),
                    right (3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows which interface that currently
         is used for wrapper communication.
"
    ::= { wdmProtEntry 22 }

wdmProtLeftCommSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number of the left communication link
         for this protection group.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 23 }

wdmProtLeftCommSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number of the left communication link for
         this protection group.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 24 }

wdmProtLeftCommPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The port number of the left communication link for
         this protection group
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 25 }

wdmProtRightCommSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number of the right communication link
         for this protection group.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 26 }

wdmProtRightCommSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number of the right communication link for
         this protection group.
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 27 }

wdmProtRightCommPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The port number of the right communication link for
         this protection group
"
    DEFVAL { 0 }
    ::= { wdmProtEntry 28 }

wdmProtLeftCommInterface OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface used for the left communication link
         for this protection group.
"
    ::= { wdmProtEntry 29 }

wdmProtRightCommInterface OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION

        "The interface used for the right communication link
         for this protection group.
"
    ::= { wdmProtEntry 30 }

wdmProtCommunicationFailure OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection group communication failure

        A: There are several underlying criterias for this:
           - Loss of signal on the communication link
           - Errors on the communication link
           - Supervision failures
           - Missing status messages for the protection group

        D: Status messages are coming in as they should and
           the link supervision works.
"
    ::= { wdmProtEntry 31 }

wdmProtHubTrafficConfigMismatch OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Traffic configuration received over the two lines differ.

        A: The traffic configuration received
        over the two lines do not match.

        D: Both lines have the same traffic configuration

"
    ::= { wdmProtEntry 32 }

wdmProtSignalDegradeProtection OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (0),
                    enabled (1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Include signal degraded alarm as a protection
         switch criteria.
"
    ::= { wdmProtEntry 33 }

wdmProtRevertiveSwitchWtrTimer OBJECT-TYPE
    SYNTAX      Unsigned32 (1..1440)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When revertive mode is enabled and traffic is running on
         secondary path, this timer decides when to switch back to
         primary path (when the primary path is up and working again).

         Please note! To make sure that the desired timer value is used,
         always change this separatly from other settings.
"
    DEFVAL { 5 }
    ::= { wdmProtEntry 34 }

wdmProtRevertiveSwitch OBJECT-TYPE
    SYNTAX      INTEGER {
                    disabled (0),
                    enabled (1) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If revertive protection switch is enabled, the primary
         path will automaticly be chosen as working path
         if it is operational. A switch back to the primary
         path will however be delayed with the period of time
         given by the user.
"
    DEFVAL { disabled }
    ::= { wdmProtEntry 35 }

wdmProtRevertiveSwitchPrimaryPath OBJECT-TYPE
    SYNTAX      INTEGER {
                    left (2),
                    right (3) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The interface in the protection group that should be used
         as prefered working path.
"
    DEFVAL { left }
    ::= { wdmProtEntry 36 }

wdmProtRevertiveSwitchSecondaryPath OBJECT-TYPE
    SYNTAX      INTEGER {
                    left (2),
                    right (3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface in the protection group that is used as
         prefered protection path. This is set automatically when
         choosing the primary working path.
"
    DEFVAL { right }
    ::= { wdmProtEntry 37 }

wdmProtSecondaryPathUsed OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm indicating that the alternative secondary path is currently used,
         when revertive switching is enabled.

        A: Secondary path is used as working path.

        D: Primary path is used as working path.
"
    ::= { wdmProtEntry 38 }


-- ----------------------------------------------------
-- Remote protection group definitions
-- ----------------------------------------------------

wdmRemoteProtTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmRemoteProtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The protection group table."
    ::= { wdmRemoteProtList 1 }

wdmRemoteProtEntry OBJECT-TYPE
    SYNTAX      WdmRemoteProtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the protection group table.
"
    INDEX { wdmRemoteProtIndex }
    ::= { wdmRemoteProtTable 1 }

WdmRemoteProtEntry ::=
    SEQUENCE {
        wdmRemoteProtIndex                      Unsigned32,
        wdmRemoteProtName                       MgmtNameString,
        wdmRemoteProtDescr                      DisplayString,
        wdmRemoteProtLocalSubrack               SubrackNumber,
        wdmRemoteProtLocalSlot                  SlotNumber,
        wdmRemoteProtLocalPort                  PortNumber,
        wdmRemoteProtCommSubrack                SubrackNumber,
        wdmRemoteProtCommSlot                   SlotNumber,
        wdmRemoteProtCommPort                   PortNumber,
        wdmRemoteProtCommInterface              DisplayString,
        wdmRemoteProtLastChangeTime             DateAndTime,
        wdmRemoteProtIpAddress                  IpAddress,
        wdmRemoteProtIdentifier                 DisplayString,
        wdmRemoteProtRole                       INTEGER,
        wdmRemoteProtAdminStatus                INTEGER,
        wdmRemoteProtRowStatus                  RowStatus,
        wdmRemoteProtActiveSide                 INTEGER,
        wdmRemoteProtLocalStatus                INTEGER,
        wdmRemoteProtRemoteStatus               INTEGER,
        wdmRemoteProtObjectProperty             ObjectProperty,
        wdmRemoteProtServiceDegraded            FaultStatus,
        wdmRemoteProtServiceFailure             FaultStatus,
        wdmRemoteProtSetup                      CommandString,
        wdmRemoteProtSetupFailure               FaultStatus,
        wdmRemoteProtRoleConflict               FaultStatus,
        wdmRemoteProtCommunicationFailure       FaultStatus }

wdmRemoteProtIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An arbitrary index assigned to each entry.
"
    ::= { wdmRemoteProtEntry 1 }

wdmRemoteProtName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The name of the object, for example
        'rpg:1:2:1', where the numbers indicate the
        subrack, slot and TX port number for the local
        interface of the group.

        The interface is either a client interface
        or a line interface. This depends on the
        protection type, which in turn depends on
        the type of board that is involved.
"
    DEFVAL { "" }
    ::= { wdmRemoteProtEntry 2 }

wdmRemoteProtDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.
"
    DEFVAL { "" }
    ::= { wdmRemoteProtEntry 3 }

wdmRemoteProtLocalSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number for this protected
        interface.
"
    DEFVAL { 0 }
    ::= { wdmRemoteProtEntry 4 }

wdmRemoteProtLocalSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number for this protected
        interface.
"
    DEFVAL { 0 }
    ::= { wdmRemoteProtEntry 5 }

wdmRemoteProtLocalPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The port number for the TX side of this
        protected interface.
"
    DEFVAL { 0 }
    ::= { wdmRemoteProtEntry 6 }

wdmRemoteProtCommSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number of the communication link for
         this protection group.
"
    DEFVAL { 0 }
    ::= { wdmRemoteProtEntry 7 }

wdmRemoteProtCommSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number of the communication link for
         this protection group.
"
    DEFVAL { 0 }
    ::= { wdmRemoteProtEntry 8 }

wdmRemoteProtCommPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The port number of the communication link for
         this protection group
"
    DEFVAL { 0 }
    ::= { wdmRemoteProtEntry 9 }

wdmRemoteProtCommInterface OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The port number of the communication link for
         this protection group
"
    ::= { wdmRemoteProtEntry 10 }

wdmRemoteProtLastChangeTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time the operational state of the protection
        group was last changed.
"
    ::= { wdmRemoteProtEntry 11 }

wdmRemoteProtIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The IP-address of the remote node.
"
    DEFVAL { '00000000'H }
    ::= { wdmRemoteProtEntry 12 }

wdmRemoteProtIdentifier OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The identifier (name) of the protection group
         that is understod by both the local and the remote
         node.
"
    DEFVAL { "" }
    ::= { wdmRemoteProtEntry 13 }

wdmRemoteProtRole OBJECT-TYPE
    SYNTAX      INTEGER {
                    undefined (0),
                    master (1),
                    slave (2) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The role of the node in this protection group.

        master - the manager should read info from this node.

        slave - the manager should read from the other node
"
    DEFVAL { undefined }
    ::= { wdmRemoteProtEntry 14 }

wdmRemoteProtAdminStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    down (1),
                    localForced (2),
                    remoteForced (3),
                    auto (4),
                    toggle (5) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the protection
        group.

        down - The protection group is inactive.

        localForced - The local side is forced active.

        remoteForced - The remote side is forced active.

        auto - The application choses which side should
        be active. This should be the normal mode of
        operation.

        toggle - Switch the active side of the protection
        group. Only applicable in 'auto' state.
        After setting adminStatus to 'toggle' it again
        receives the 'auto' value.
"
    DEFVAL { down }
    ::= { wdmRemoteProtEntry 15 }

wdmRemoteProtRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Manages creation and deletion of conceptual rows.
        See also SNMPv2-TC.
"
    ::= { wdmRemoteProtEntry 16 }

wdmRemoteProtActiveSide OBJECT-TYPE
    SYNTAX      INTEGER {
                    none (1),
                    local (2),
                    remote (3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the protection group.

        none - no side is active.

        local - the local side is active.

        remote - the remote side is active.
"
    ::= { wdmRemoteProtEntry 17 }

wdmRemoteProtLocalStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (0),
                    down (1),
                    up (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the local side of the
        protection group.

        down - this side is out of service.

        up - this side is working.
"
    ::= { wdmRemoteProtEntry 18 }

wdmRemoteProtRemoteStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    unknown (0),
                    down (1),
                    up (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the remote side of
        the protection group.

        down - this side is out of service.

        up - this side is working.
"
    ::= { wdmRemoteProtEntry 19 }


wdmRemoteProtObjectProperty OBJECT-TYPE
    SYNTAX      ObjectProperty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Property mask.
"
    ::= { wdmRemoteProtEntry 20 }

wdmRemoteProtServiceDegraded OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection service degraded.

        A: One of the sides of the protection group has
        signal failure or one side is forced active.

        D: Both sides of the protection group are
        available (The signal is recovered or the
        forced active side is released).
"
    ::= { wdmRemoteProtEntry 21 }

wdmRemoteProtServiceFailure OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection service failure.

        A: Both sides of the protection group has
        signal failure or the side that is forced
        active has signal failure.

        D: One of the sides of the protection group
        is available (The signal is recovered or the
        forced active side is released).
"
    ::= { wdmRemoteProtEntry 22 }

wdmRemoteProtSetup OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Procedure to setup the protection group.

         - Communication Interface
           Select from a list of available interfaces

         - Remote IP-address
           Select from a list of discovered addresses or
           enter a value

         - Identifier
           Select from a list of identifiers that have been
           published by the node with the IP-address
           selected above, or enter a unique value.

           The identifier must be unique both in the local
           node and in the remote node.

         - Role
           Select whether the local end of the protection
           group shall be master or slave.
"
    ::= { wdmRemoteProtEntry 23 }

wdmRemoteProtSetupFailure OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection group setup failure.

        A: The defined remote IP-address and/or protection
           group identifier can not be resolved with the
           peer node.

        D: The protection group can be resolved
"
    ::= { wdmRemoteProtEntry 24 }

wdmRemoteProtRoleConflict OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection group master/slave conflict.

        A: The protection group is resolved, but the two
           peers have the same role defined - both master,
           or both slave

        D: Their respective roles differ
"
    ::= { wdmRemoteProtEntry 25 }

wdmRemoteProtCommunicationFailure OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection group communication failure

        A: There are several underlying criterias for this:
           - Loss of signal on the communication link
           - Errors on the communication link
           - Supervision failures
           - Missing status messages for the protection group

        D: Status messages are coming in as they should and the
           link supervision works.
"
    ::= { wdmRemoteProtEntry 26 }


-- ----------------------------------------------------
-- Interface table for passive equipment
-- ----------------------------------------------------

wdmPassiveIfTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmPassiveIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The interface list."
    ::= { wdmPassiveIfList 1 }

wdmPassiveIfEntry OBJECT-TYPE
    SYNTAX      WdmPassiveIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the interface list.
        Each interface consists of a uni- or
        bi-directional port.
"
    INDEX { wdmPassiveIfIndex }
    ::= { wdmPassiveIfTable 1 }

WdmPassiveIfEntry ::=
    SEQUENCE {
        wdmPassiveIfIndex               Unsigned32,
        wdmPassiveIfName                MgmtNameString,
        wdmPassiveIfDescr               DisplayString,
        wdmPassiveIfSubrack             SubrackNumber,
        wdmPassiveIfSlot                SlotNumber,
        wdmPassiveIfPort                PortNumber,
        wdmPassiveIfInvPhysIndexOrZero  Unsigned32,
        wdmPassiveIfDirection           PortType,
        wdmPassiveIfLambdaType          LambdaType,
        wdmPassiveIfLambda              LambdaFrequency,
        wdmPassiveIfLambdaMax           LambdaFrequency,
        wdmPassiveIfLastChangeTime      DateAndTime, -- deprecated
        wdmPassiveIfExpectedLambda      LambdaFrequency,
        wdmPassiveIfUnexpectedLambda    FaultStatus,
        wdmPassiveIfAdminStatus         INTEGER,
        wdmPassiveIfOperStatus          BoardOrInterfaceOperStatus,
        wdmPassiveIfObjectProperty      ObjectProperty,
        wdmPassiveIfExpectedLambdaMax   LambdaFrequency,
        wdmPassiveIfAid                 DisplayString,
        wdmPassiveIfPhysicalLocation    DisplayString,
        wdmPassiveIfIfNo                PortNumber}

wdmPassiveIfIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An arbitrary index assigned to each entry.
"
    ::= { wdmPassiveIfEntry 1 }

wdmPassiveIfName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the interface, for
        example 'wdmi:1:2:1', where the first number
        indicates sub-rack, the second slot number and
        the third is the port number.
"
    ::= { wdmPassiveIfEntry 2 }

wdmPassiveIfDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.
"
    DEFVAL { "" }
    ::= { wdmPassiveIfEntry 3 }

wdmPassiveIfSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the subrack where the interface
        is located.
"
    ::= { wdmPassiveIfEntry 5 }

wdmPassiveIfSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the slot where the interface is
        located.
"
    ::= { wdmPassiveIfEntry 6 }

wdmPassiveIfPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the port where the interface
        is located.
"
    ::= { wdmPassiveIfEntry 7 }

wdmPassiveIfInvPhysIndexOrZero OBJECT-TYPE
    SYNTAX      Unsigned32 (0..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The row in the invPhysTable for this interface.
        Set to 0 if not known.
"
    ::= { wdmPassiveIfEntry 8 }

wdmPassiveIfDirection OBJECT-TYPE
    SYNTAX      PortType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The type of signal the interface expects.

        rx - Only ingoing signal.
        LambdaType, LambdaMin and LambdaMax indicates the
        frequency properties for the ingoing signal.

        tx - Only outgoing signal.
        LambdaType, LambdaMin and LambdaMax indicates the
        frequency properties for the outgoing signal.

        biDi - Ingoing and outgoing signal on the same
        port. LambdaType, LambdaMin and LambdaMax
        indicates the frequency properties for the
        bi-directional signal.
"
    ::= { wdmPassiveIfEntry 9 }

wdmPassiveIfLambdaType OBJECT-TYPE
    SYNTAX      LambdaType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The type of wavelengths the port of the
        interface can accomodate.

        fixed - The port received or transmits a fixed
        frequency.

        range - The port can handle a range of
        frequencies. Not used.

        transparent - There are no frequency properties
        associated with the port.
"
    ::= { wdmPassiveIfEntry 10 }

wdmPassiveIfLambda OBJECT-TYPE
    SYNTAX      LambdaFrequency
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The frequency given in hundreds of GHz
        (0.01 GHz). Set to 0 for transparent ports.
"
    ::= { wdmPassiveIfEntry 11 }

wdmPassiveIfLambdaMax OBJECT-TYPE
    SYNTAX      LambdaFrequency
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum frequency (for the 'wavelength')
        in GHz.
"
    ::= { wdmPassiveIfEntry 12 }

wdmPassiveIfLastChangeTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      deprecated
    DESCRIPTION
        "The time when the state of the interface entry
        was last changed.
"
    ::= { wdmPassiveIfEntry 13 }

wdmPassiveIfExpectedLambda OBJECT-TYPE
    SYNTAX      LambdaFrequency
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The frequency of the 'wavelength' given in
        hundreds of GHz (0.01 GHz).
        This attribute can be written via SNMP.
"
    DEFVAL { 0 }
    ::= { wdmPassiveIfEntry 14 }

wdmPassiveIfUnexpectedLambda OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual frequency does not match the
        hundreds of GHz (0.01 GHz).

        The actual frequency does not match the
        pre-configured frequency.

        A pre-configured frequency of '0' matches
        all actual frequencies.

        A: The configured frequency does not match the
        actual frequency.

        D: The configured frequency matches the actual
        frequency.
"
    ::= { wdmPassiveIfEntry 15 }

wdmPassiveIfAdminStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    down (1),
                    up (2) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the
        port.

        down - The alarm are inhibited

        up - Alarms are reported
"
    DEFVAL { up }
    ::= { wdmPassiveIfEntry 16 }

wdmPassiveIfOperStatus OBJECT-TYPE
    SYNTAX      BoardOrInterfaceOperStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the
        port.

        notPresent - The board is missing

        down - Admin down

        up - The board is present and admin up
"
    DEFVAL { notPresent }
    ::= { wdmPassiveIfEntry 17 }

wdmPassiveIfObjectProperty  OBJECT-TYPE
    SYNTAX      ObjectProperty
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Property mask.
"
    ::= { wdmPassiveIfEntry 18 }

wdmPassiveIfExpectedLambdaMax OBJECT-TYPE
    SYNTAX      LambdaFrequency
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Max frequency of the 'wavelength' range
        given in hundreds of GHz (0.01 GHz).
        This attribute can be written via SNMP.
"
    DEFVAL { 0 }
    ::= { wdmPassiveIfEntry 19 }


wdmPassiveIfAid OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The access identifier (AID) of the interface.
         The format is according to GR-833.

"
    ::= { wdmPassiveIfEntry 20 }

wdmPassiveIfPhysicalLocation OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The physical location of the if.
         The format is according to GR-833.

"
    ::= { wdmPassiveIfEntry 21 }

wdmPassiveIfIfNo OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of the port where the interface
        is located.
"
    ::= { wdmPassiveIfEntry 22 }


-- ----------------------------------------------------
-- Table for optical control loop channels
-- ----------------------------------------------------

wdmCtrlChannelTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmCtrlChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The interface list."
    ::= { wdmCtrlChannelList 1 }

wdmCtrlChannelEntry OBJECT-TYPE
    SYNTAX      WdmCtrlChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the interface list.
"
    INDEX { wdmCtrlChannelIndex }
    ::= { wdmCtrlChannelTable 1 }

WdmCtrlChannelEntry ::=
    SEQUENCE {
        wdmCtrlChannelIndex                      Unsigned32,
        wdmCtrlChannelName                       MgmtNameString,
        wdmCtrlChannelSubrack                    SubrackNumber,
        wdmCtrlChannelSlot                       SlotNumber,
        wdmCtrlChannelTxPort                     PortNumber,
        wdmCtrlChannelChannel                    LambdaFrequency,
        wdmCtrlChannelGroupNumber                Unsigned32,
        wdmCtrlChannelAdminStatus                BoardOrInterfaceAdminStatus,
        wdmCtrlChannelWantedOutputPower          Integer32,
        wdmCtrlChannelCurrentOutputPower         Integer32,
        wdmCtrlChannelCurrentAttenuation         Unsigned32,
        wdmCtrlChannelForceRegulationCommand     CommandString,
        wdmCtrlChannelOuputPowerControlFailure   FaultStatus,
        wdmCtrlChannelCurrentPowerOutOfRange     FaultStatus,
        wdmCtrlChannelAttenuationOutOfRange      FaultStatus,
        wdmCtrlChannelStartupChannel             INTEGER,
        wdmCtrlChannelStatus                     INTEGER,
        wdmCtrlChannelMonitorIndex               Unsigned32,
        wdmCtrlChannelStartupCommand             CommandString,
        wdmCtrlChannelSfpMissing                 FaultStatus,
        wdmCtrlChannelSfpMediaMismatch           FaultStatus,
        wdmCtrlChannelLossOfSignal               FaultStatus,
        wdmCtrlChannelDescr                      DisplayString,
        wdmCtrlChannelMaxAttenuation             Unsigned32,
        wdmCtrlChannelMinAttenuation             Unsigned32,
        wdmCtrlChannelAttenControlOffset         Unsigned32,
        wdmCtrlChannelAttenControlDegraded       FaultStatus,
        wdmCtrlChannelNotFound                   FaultStatus
    }


wdmCtrlChannelIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An index assigned to each entry. The index contains information
        about subrack, slot and port for controlled channel.
"
    ::= { wdmCtrlChannelEntry 1 }

wdmCtrlChannelName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the channel, for example
         'ch:1:2:1:959'
         (ch:[subrack]:[slot]:[port]:[channel]).
"
    ::= { wdmCtrlChannelEntry 2 }

wdmCtrlChannelSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number of the unit where the controlled
        channel is attenuated.
"
    ::= { wdmCtrlChannelEntry 3 }

wdmCtrlChannelSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number of the unit where the controlled
        channel is attenuated.
"
    ::= { wdmCtrlChannelEntry 4 }

wdmCtrlChannelTxPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The port number of the controlled channel in the unit where
        the channel is attenuated.
"
    ::= { wdmCtrlChannelEntry 5 }

wdmCtrlChannelChannel OBJECT-TYPE
    SYNTAX      LambdaFrequency
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The frequency of the 'wavelength' given in
        hundreds of GHz (0.01 GHz).
"
    DEFVAL { 0 }
    ::= { wdmCtrlChannelEntry 6 }

wdmCtrlChannelGroupNumber OBJECT-TYPE
    SYNTAX      Unsigned32 (1..64)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The number of the group to which the controlled
        channel belongs.
"
    DEFVAL { 1 }
    ::= { wdmCtrlChannelEntry 7 }

wdmCtrlChannelAdminStatus OBJECT-TYPE
    SYNTAX      BoardOrInterfaceAdminStatus
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the channel control.

        down - The channel control is inactive. Channel
        attenuation is set to its maximum value.

        service - The channel control is activated but
        alarms are suppressed. Intended for use during
        service or re-configuration. When service is
        concluded adminStatus should be set to 'up' again.

        up - The channel control is active. Alarms are
        not suppressed.
"
    DEFVAL { down }
    ::= { wdmCtrlChannelEntry 8 }

wdmCtrlChannelWantedOutputPower OBJECT-TYPE
    SYNTAX      Integer32 (-260..120)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The wanted output power.
"
    DEFVAL { 0 }
    ::= { wdmCtrlChannelEntry 9 }

wdmCtrlChannelCurrentOutputPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual output power.
"
    ::= { wdmCtrlChannelEntry 10 }

wdmCtrlChannelCurrentAttenuation OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current attenuation.
"
    ::= { wdmCtrlChannelEntry 11 }

wdmCtrlChannelForceRegulationCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Starts a regulation manually
"
    ::= { wdmCtrlChannelEntry 12 }

wdmCtrlChannelOuputPowerControlFailure OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm raised when the wanted output power
         can not be obtained. That is

         a) The wanted power has not been obtained after the
         maximum number of consequent control cycles.

         b) Failure to read the current output power.

         c) Failure to set attenuation when regulating
         channel.
"
    ::= { wdmCtrlChannelEntry 13 }

wdmCtrlChannelCurrentPowerOutOfRange OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm raised when the current output power
         is outside the 'regulation range' for the group.

        At this point, no regulation will be performed on
        the channel. The operator must change the
        wantedOutputPower or the regulationRange.
"
    ::= { wdmCtrlChannelEntry 14 }

wdmCtrlChannelAttenuationOutOfRange OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm raised when the attenuation is at the maximum
        or minimum values for the controlled device and the
        wantedOutputPower cannot be obtained.

        At this point, no regulation will be performed on
        the channel.
"
    ::= { wdmCtrlChannelEntry 15 }

wdmCtrlChannelStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                        initial (1),
                        searching (2),
                        regulating (3),
                        ok (4),
                        notFound (5),
                        error (6),
                        waiting (7)}
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The regulation status of the channel.

        initial    - No regulation has been performed yet.

        searching  - The system is trying to detect the channel.

        regulating - The system is regulating the output
                        power of the channel to the wanted
                        level.

        ok         - The output power of the channel is at the
                        wanted level.

        notFound   - Channel could not be detected in monitor.

        error      - Regulation of channel output power failed.

        waiting    - Channel is in queue for regulation startup
                     and will be started when ongoing
                     regulations and prior elements in the
                     startup queue have been processed.
"
    DEFVAL { initial }
    ::= { wdmCtrlChannelEntry 16 }

wdmCtrlChannelStartupChannel OBJECT-TYPE
    SYNTAX      INTEGER {
                        normal (1),
                        start (2),
                        blocked (3) }
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Initiates a safe startup procedure for the channel
        when set to 'start'.

        If the administrative status of the channel is
        'down' or the channel is not detected by the monitor
        unit, the system attempts to detect the channel as
        follows:

        1) The attenuation is set to its maximum value.

        2) The attenuation is decreased until the channel is
        detected by the monitor.

        When the channel is detected, regulation of the
        channel is started, ignoring the regulation range.

        If the channel cannot be detected, channelStatus is
        set to notFound. If regulation in fails,
        channelStatus is set to error and an alarm is
        raised.

        When the startup sequence is finished, the
        administrative status of the channel is set to 'up'.

        If a regulation is ongoing when the startup
        procedure is initiated, the channel is put on a
        waiting queue until the ongoing regulation is
        finished. The startup queue has a limited length. If
        the queue is filled up with waiting channels,
        further startup commands will be blocked until all
        the queued channels have been started.

        At times when it is not allowed to start the
        channel, e.g. in commissioning mode or when the
        queue is full, this attribute is set to 'blocked'
        and cannot be set to start.

        NOTE: The 'blocked' state is handled entirely by the
        system and cannot be set or reset by the user.

        The state of this attribute immediately returns to
        'normal' when the startup procedure has been
        initiated or the channel is put on the startup
        queue.
"
    DEFVAL { normal }
    ::= { wdmCtrlChannelEntry 17 }


wdmCtrlChannelMonitorIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (0..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the index of the connected monitor interface.
"
    DEFVAL { 0 }
    ::= { wdmCtrlChannelEntry 18 }


wdmCtrlChannelStartupCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Initiates a safe startup sequence for the
        channel. If the administrative status of the channel
        is 'down' or the channel is not detected by the
        monitor unit, the system attempts to detect the
        channel as follows:

        1) The attenuation is set to its maximum value.

        2) The attenuation is decreased until the channel is
        detected by the monitor.

        When the channel is detected, regulation of the
        channel is started, ignoring the regulation range.

        If the channel cannot be detected, channelStatus is
        set to notFound. If regulation fails, channelStatus
        is set to 'error' and an alarm is raised.

        When the startup sequence is finished, the
        administrative status of the channel is set to 'up'.

        If a regulation is ongoing when the startup
        procedure is initiated, the channel is put on a
        waiting queue until the ongoing regulation is
        finished. The startup queue has a limited length. If
        the queue is filled up with waiting channels,
        further startup commands will be blocked until all
        the queued channels have been started.
"
    ::= { wdmCtrlChannelEntry 19 }

wdmCtrlChannelSfpMissing OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An expected SFP is missing.

        A(ctivation): An expected SFP is missing.

        D(e-activation): The missing SFP is inserted.

"
    ::= { wdmCtrlChannelEntry 20 }

wdmCtrlChannelSfpMediaMismatch OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The type of the SFP used for the channel port
         is not the expected.

        A(ctivation): Invalid or mismatching SFP media is
           detected.

        D(e-activation): The correct SFP is used.

"
    ::= { wdmCtrlChannelEntry 21 }

wdmCtrlChannelLossOfSignal OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Loss of signal at the channel port.

        A(ctivation): Active loss of signal HW
        indication.

        D(e-activation): Inactive loss of signal HW
        indication.

"
    ::= { wdmCtrlChannelEntry 22 }

wdmCtrlChannelDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.

        This attribute can be written via SNMP.
"
    DEFVAL { "" }
    ::= { wdmCtrlChannelEntry 23 }

wdmCtrlChannelMaxAttenuation OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The max attenuation limit on channel.
"
    ::= { wdmCtrlChannelEntry 24}


wdmCtrlChannelMinAttenuation OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The min attenuation limit on channel.
"
    ::= { wdmCtrlChannelEntry 25}


wdmCtrlChannelAttenControlOffset OBJECT-TYPE
    SYNTAX      Unsigned32 (0..1000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The threshold offset to max/min attenuation on the channel.
		If the threshold is reached, AttenControlDegraded alarm will be raised.
"
	DEFVAL { 10 }
   ::= { wdmCtrlChannelEntry 26}


wdmCtrlChannelAttenControlDegraded OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm is raised when channel attenuation
		is crossing threshold to max/min attenuation.

        A(ctivation): Active the alarm indication.

        D(e-activation): Inactive the alarm indication.

"
    ::= { wdmCtrlChannelEntry 27}

wdmCtrlChannelNotFound OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Channel not found.
"
    ::= { wdmCtrlChannelEntry 28 }


-- ----------------------------------------------------
-- Table for optical control loop groups
-- ----------------------------------------------------

wdmCtrlGroupTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmCtrlGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The interface list."
    ::= { wdmCtrlGroupList 1 }

wdmCtrlGroupEntry OBJECT-TYPE
    SYNTAX      WdmCtrlGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the interface list.
"
    INDEX { wdmCtrlGroupIndex }
    ::= { wdmCtrlGroupTable 1 }

WdmCtrlGroupEntry ::=
    SEQUENCE {
        wdmCtrlGroupIndex                      Unsigned32,
        wdmCtrlGroupName                       MgmtNameString,
        wdmCtrlGroupDescr                      DisplayString,
        wdmCtrlGroupGroupNumber                Unsigned32,
        wdmCtrlGroupSubrack                    SubrackNumber,
        wdmCtrlGroupSlot                       SlotNumber,
        wdmCtrlGroupPort                       INTEGER,
        wdmCtrlGroupMonitorName                MgmtNameString,
        wdmCtrlGroupAdminStatus                BoardOrInterfaceAdminStatus,
        wdmCtrlGroupControlMode                INTEGER,
        wdmCtrlGroupConfigurationCommand       CommandString,
        wdmCtrlGroupForceRegulationCommand     CommandString,
        wdmCtrlGroupLockedRange                INTEGER,
        wdmCtrlGroupRegulationRange            Unsigned32,
        wdmCtrlGroupRegulationLastChangeTime   DateAndTime,
        wdmCtrlGroupCommissioningMode          FaultStatus,
        wdmCtrlGroupAssociateChannel           CommandString,
        wdmCtrlGroupNoOfChannels               Unsigned32,
        wdmCtrlGroupStatus                     INTEGER,
        wdmCtrlGroupTimeLeft                   Unsigned32,
        wdmCtrlGroupOutputPowerMismatch        FaultStatus,
        wdmCtrlGroupTotalPower                 Integer32,
        wdmCtrlGroupChannelStartupCommand      CommandString}

wdmCtrlGroupIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An index assigned to each entry. Index contains
        information about group number, subrack, slot and
        port.
"
    ::= { wdmCtrlGroupEntry 1 }

wdmCtrlGroupName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the group,
        for example 'group:1'  (group:[groupNumber]).
"
    ::= { wdmCtrlGroupEntry 2 }

wdmCtrlGroupDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.
"
    DEFVAL { "" }
    ::= { wdmCtrlGroupEntry 3 }

wdmCtrlGroupGroupNumber OBJECT-TYPE
    SYNTAX      Unsigned32 (1..64)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "A unique number identifying the group.
"
    DEFVAL { 1 }
    ::= { wdmCtrlGroupEntry 4 }

wdmCtrlGroupSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The number of the subrack where the monitor unit is
        located.
"
    ::= { wdmCtrlGroupEntry 5 }

wdmCtrlGroupSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The number of the slot where the monitor is located.

"
    ::= { wdmCtrlGroupEntry 6 }

wdmCtrlGroupPort OBJECT-TYPE
    SYNTAX      INTEGER {
                    a (1),
                    b (2),
                    c (3),
                    d (4),
                    e (5),
                    f (6),
                    g (7),
                    h (8) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The monitor port where the group is connected.
        Each character represents a port number, as shown:
        a->1, b->2, c->3, d->4, e->5, f->6, g->7, h->8.
"
    DEFVAL { a }
    ::= { wdmCtrlGroupEntry 7 }

wdmCtrlGroupMonitorName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the monitor port,
        for example 'ocm:1:2:1-A'
        ([monitortype]:[subrack]:[slot]:[port]).
"
    ::= { wdmCtrlGroupEntry 8 }

wdmCtrlGroupAdminStatus OBJECT-TYPE
    SYNTAX      BoardOrInterfaceAdminStatus
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the group.

        down - The group is de-activated. No regulations
        will be performed.

        service - The group is activated but alarms
        are suppressed. Intended for use during service
        or re-configuration. When service is concluded
        adminStatus should be set to 'up' again.

        up - The group is active. Regulations will be
        performed regularly and on user orders.
        Alarms are not suppressed.
"
    DEFVAL { up }
    ::= { wdmCtrlGroupEntry 9 }

wdmCtrlGroupControlMode OBJECT-TYPE
    SYNTAX      INTEGER {
                   normal (1),
                   commissioning (2) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current control group mode.

        normal -  regulation is performed at the interval
        defined in General -> Regulation interval

        commissioning - the regulation is performed
        continuously. After 15 minutes it will change back to
        normal mode.

        Note! When control group is in commissioning mode
        the corresponding monitor port will still be in
        'normal' mode.
"
    DEFVAL { normal }
    ::= { wdmCtrlGroupEntry 10 }

wdmCtrlGroupConfigurationCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Select which control mode to use:

        normal -  regulation is performed at the interval
        defined in General -> Regulation interval

        commissioning - the regulation is performed
        continuously. After 15 minutes it will change back to
        normal mode.
"
    ::= { wdmCtrlGroupEntry 11 }

wdmCtrlGroupForceRegulationCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Starts a regulation manually
"
    ::= { wdmCtrlGroupEntry 12 }

wdmCtrlGroupLockedRange OBJECT-TYPE
    SYNTAX      Integer32 (1..30)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "To avoid frequent changes of attenuation, a range
         can be defined around the wanted output power. A
         control loop will not try to adjust power values
         within the range.
"
    DEFVAL { 10 }
    ::= { wdmCtrlGroupEntry 13 }

wdmCtrlGroupRegulationRange OBJECT-TYPE
    SYNTAX      Unsigned32 (0..1000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "An offset below the wanted output power
         at which the 'Current Power Out Of Range'
         alarm shall be raised.

         When the current output power is so
         far below the wanted output power,
         the regulation stops and the alarm
         is raised.

         Applicable for normal mode.
"
    DEFVAL { 40 }
    ::= { wdmCtrlGroupEntry 14 }

wdmCtrlGroupRegulationLastChangeTime OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time when the group was last regulated.
"
    ::= { wdmCtrlGroupEntry 15 }

wdmCtrlGroupCommissioningMode OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm raised when the group is put in commissioning
         mode.

        A: Commissioning mode is used

        D: Normal mode
"
    ::= { wdmCtrlGroupEntry 16 }

wdmCtrlGroupAssociateChannel OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Creates one or several control channel objects and
        associates them to the selected group.

        User needs to specify:

        - The board where the added channels are attenuated.
        - The port(s) or channel(s) of that board to add.
        - The wanted output power of the channel.
"
    ::= { wdmCtrlGroupEntry 17 }

wdmCtrlGroupNoOfChannels OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current number of channels associated with this
        group.
"
    ::= { wdmCtrlGroupEntry 18 }

wdmCtrlGroupStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                    starting (1),
                    noRegulation (2),
                    reading (3),
                    regulation (4),
                    idle (5) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The regulation status for the group

        starting - the system is starting up

        noRegulation - the group contains no channels to
                       regulate

        reading - the control loop is reading the power from
                  the monitor

        regulating - the control loop is calculating and
                     setting new attenuations

        idle - the control loop is inactive until next
               regulation
"
    DEFVAL { starting }
    ::= { wdmCtrlGroupEntry 19 }

wdmCtrlGroupTimeLeft OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The remaining time (in seconds) to next regulation.
"
    DEFVAL { 1800 }
    ::= { wdmCtrlGroupEntry 20 }

wdmCtrlGroupOutputPowerMismatch OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The total output power of the OCM differs by
        more than 1.4dB from the monitor port
        insertion loss value of the OA.

        A: The total output power of the OCM differs from
        the monitor port insertion loss by more than
        1.4dB

        D: Output power of OCM is within 1.4dB of the
        monitor port insertion loss value again

"
    ::= { wdmCtrlGroupEntry 21 }

wdmCtrlGroupTotalPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total power of channels associated to
        this group, based on OCM measurements.

"
    ::= { wdmCtrlGroupEntry 22 }

wdmCtrlGroupChannelStartupCommand OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Initiates a safe startup sequence for all the
        channels of selected group. If the administrative
        status of the channel is 'down' or the channel is
        not detected by the monitor unit, the system attempts
        to detect the channel as follows:

        1) The attenuation is set to its maximum value.

        2) The attenuation is decreased until the channel is
        detected by the monitor.

        When the channel is detected, regulation of the
        channel is started, ignoring the regulation range.

        If the channel cannot be detected, channelStatus is
        set to notFound. If regulation fails, channelStatus
        is set to 'error' and an alarm is raised.

        When the startup sequence is finished, the
        administrative status of the channel is set to 'up'.

        If a regulation is ongoing when the startup
        procedure is initiated, the channel is put on a
        waiting queue until the ongoing regulation is
        finished. The startup queue has a limited length. If
        the queue is filled up with waiting channels,
        further startup commands will be blocked until all
        the queued channels have been started.
"
    ::= { wdmCtrlGroupEntry 23 }

-- ----------------------------------------------------
-- Table for local link adjustment
-- ----------------------------------------------------
wdmMeanChannelPowerControlTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmMeanChannelPowerControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The list of local links to balance channel power on"
    ::= { wdmMeanChannelPowerControlList 1 }

wdmMeanChannelPowerControlEntry OBJECT-TYPE
    SYNTAX      WdmMeanChannelPowerControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of links where the channel power will be adjusted
"
    INDEX { wdmMeanChannelPowerControlIndex }
    ::= { wdmMeanChannelPowerControlTable 1 }

WdmMeanChannelPowerControlEntry ::=
    SEQUENCE {
        wdmMeanChannelPowerControlIndex                             Unsigned32,
        wdmMeanChannelPowerControlName                              MgmtNameString,
        wdmMeanChannelPowerControlDescr                             DisplayString,
        wdmMeanChannelPowerControlOcmSubrack                        SubrackNumber,
        wdmMeanChannelPowerControlOcmSlot                           SlotNumber,
        wdmMeanChannelPowerControlOcmPort                           INTEGER,
        wdmMeanChannelPowerControlOaSubrack                         SubrackNumber,
        wdmMeanChannelPowerControlOaSlot                            SlotNumber,
        wdmMeanChannelPowerControlOaPort                            PortNumber,
        wdmMeanChannelPowerControlMonitorName                       MgmtNameString,
        wdmMeanChannelPowerControlAdminStatus                       AdminStatusWithNA,
        wdmMeanChannelPowerControlOperStatus                        BoardOrInterfaceOperStatus,
        wdmMeanChannelPowerControlStartRegulation                   CommandString,
        wdmMeanChannelPowerControlRegulationRange                   Unsigned32,
        wdmMeanChannelPowerControlLatestRegulation                  DateAndTime,
        wdmMeanChannelPowerControlLatestChange                      DateAndTime,
        wdmMeanChannelPowerControlMonitorOffsetCalibrationFailed    FaultStatus,
        wdmMeanChannelPowerControlRegulationState                   INTEGER,
        wdmMeanChannelPowerControlTimeToNextRegulation              Unsigned32,
        wdmMeanChannelPowerControlWantedChannelPower                Integer32,
        wdmMeanChannelPowerControlCurrentChannelPower               Integer32,
        wdmMeanChannelPowerControlCurrentGain                       Integer32,
        wdmMeanChannelPowerControlTotalChannelOutputPower           Integer32,
        wdmMeanChannelPowerControlNumberOfChannels                  Unsigned32,
        wdmMeanChannelPowerControlAbsolutePowerOffset               Integer32,
        wdmMeanChannelPowerControlRemainingPowerOffset              Integer32,
        wdmMeanChannelPowerControlMonitorOffsetTooLarge             FaultStatus,
        wdmMeanChannelPowerControlChannelPowerOutOfRange            FaultStatus,
        wdmMeanChannelPowerControlRegulationInterval                Unsigned32,
        wdmMeanChannelPowerControlAmplifierOutputPort               MgmtNameString,
        wdmMeanChannelPowerControlLatestAmplifierRxPower            Integer32,
        wdmMeanChannelPowerControlLatestAmplifierTxPower            Integer32,
        wdmMeanChannelPowerControlLocalId                           Integer32
    }

wdmMeanChannelPowerControlIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An index assigned to each entry. The index contains information
        about subrack, slot and port for controlled channel.
"
    ::= { wdmMeanChannelPowerControlEntry 1 }

wdmMeanChannelPowerControlName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the local link adjustment
         entry 'meanChannelPowerControl:1:2:1'
         (meanChannelPowerControl[oa subrack]:[oa slot]:[oa port]).
"
    ::= { wdmMeanChannelPowerControlEntry 2 }

wdmMeanChannelPowerControlDescr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User configurable label.
"
    DEFVAL { "" }
    ::= { wdmMeanChannelPowerControlEntry 3 }

wdmMeanChannelPowerControlOcmSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The subrack number of the ocm reading the oa
        monitor port.
"
    ::= { wdmMeanChannelPowerControlEntry 4 }

wdmMeanChannelPowerControlOcmSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The slot number of the ocm reading the oa
        monitor port.
        channel is attenuated.
"
    ::= { wdmMeanChannelPowerControlEntry 5 }

wdmMeanChannelPowerControlOcmPort OBJECT-TYPE
    SYNTAX      INTEGER {
                    a (1),
                    b (2),
                    c (3),
                    d (4),
                    e (5),
                    f (6),
                    g (7),
                    h (8) }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The monitor port of this local link
        adjustment loop.
"
    DEFVAL { a }
    ::= { wdmMeanChannelPowerControlEntry 6 }

wdmMeanChannelPowerControlOaSubrack OBJECT-TYPE
    SYNTAX      SubrackNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The subrack number of the oa that is being
        controlled by this local link adjustment
        entry
"
    ::= { wdmMeanChannelPowerControlEntry 7 }

wdmMeanChannelPowerControlOaSlot OBJECT-TYPE
    SYNTAX      SlotNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The slot number of the oa that is being
        controlled by this local link adjustment
        entry
"
    ::= { wdmMeanChannelPowerControlEntry 8 }

wdmMeanChannelPowerControlOaPort OBJECT-TYPE
    SYNTAX      PortNumber
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The port number of the oa that is being
        controlled by this local link adjustment
        entry
"
    ::= { wdmMeanChannelPowerControlEntry 9 }


wdmMeanChannelPowerControlMonitorName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the monitor port,
        for example 'ocm:1:2:1-A'
        ([monitortype]:[subrack]:[slot]:[port]).
"
    ::= { wdmMeanChannelPowerControlEntry 10 }

wdmMeanChannelPowerControlAdminStatus OBJECT-TYPE
    SYNTAX      AdminStatusWithNA
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the local link
        adjustment entry.

        down - The link adjustment is disabled

        service - The link is adjusting the channel
        power but alarms are suppressed.
        Intended for use during service or
        re-configuration. When service is concluded
        adminStatus should be set to 'up' again.

        up - The link adjustment is active.
        Regulations will be performed regularly and
        on user orders. Alarms are not suppressed.
"
    DEFVAL { down }
    ::= { wdmMeanChannelPowerControlEntry 11 }

wdmMeanChannelPowerControlOperStatus OBJECT-TYPE
    SYNTAX      BoardOrInterfaceOperStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the control group

        down - The control group cannot operate due
        to missing resources or other complications

        up - The control group is active
"
-- TEMPORARY FOR ALARMS TODO fix this
        DEFVAL { up }
    ::= { wdmMeanChannelPowerControlEntry 12 }

wdmMeanChannelPowerControlStartRegulation OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Starts a regulation manually
"
    ::= { wdmMeanChannelPowerControlEntry 13 }

wdmMeanChannelPowerControlRegulationRange OBJECT-TYPE
    SYNTAX      Unsigned32 (0..1000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "An offset below the wanted output power
        at which the 'Current Power Out Of Range'
        alarm shall be raised.

        When the current output power is so
        far below the wanted output power,
        the regulation stops and the alarm
        is raised.

        Applicable for normal mode.
"
    DEFVAL { 40 }
    ::= { wdmMeanChannelPowerControlEntry 14 }

wdmMeanChannelPowerControlLatestRegulation OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time when the link was last regulated.
"
    ::= { wdmMeanChannelPowerControlEntry 15 }

wdmMeanChannelPowerControlLatestChange OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time when the link was latest adjusted
"
    ::= { wdmMeanChannelPowerControlEntry 16 }

wdmMeanChannelPowerControlMonitorOffsetCalibrationFailed OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm raised when calibration of the OCM port fails.

        A: calibration of OCM port failed

        D: Normal mode
"
    ::= { wdmMeanChannelPowerControlEntry 17 }

wdmMeanChannelPowerControlRegulationState OBJECT-TYPE
    SYNTAX      INTEGER {
                    starting (1),
                    regulating (2),
                    idle (3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The regulation status for the link

        starting - the local link adjustment loop is
                   reading the power from the
                   monitor

        regulating - the local link adjustment loop
                     is calculating and setting new
                     attenuation / wanted gain

        idle - the local link adjustment loop is
               inactive until next regulation
"
    DEFVAL { starting }
    ::= { wdmMeanChannelPowerControlEntry 18 }

wdmMeanChannelPowerControlTimeToNextRegulation OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The remaining time (in minutes) to next regulation.
"
    DEFVAL { 30 }
    ::= { wdmMeanChannelPowerControlEntry 19 }

wdmMeanChannelPowerControlWantedChannelPower OBJECT-TYPE
    SYNTAX      Integer32 (-150..100)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The wanted output power.
"
    DEFVAL { 5 }
    ::= { wdmMeanChannelPowerControlEntry 20 }


wdmMeanChannelPowerControlCurrentChannelPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual output power.
"
    ::= { wdmMeanChannelPowerControlEntry 21 }

wdmMeanChannelPowerControlCurrentGain OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "OA signal gain set by the local link
        adjustment loop

"
    DEFVAL { 200 }
    ::= { wdmMeanChannelPowerControlEntry 22 }

wdmMeanChannelPowerControlTotalChannelOutputPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total channel output power

"
    DEFVAL { 0 }
    ::= { wdmMeanChannelPowerControlEntry 23 }

wdmMeanChannelPowerControlNumberOfChannels OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of channels read from OCM

"
    DEFVAL { 0 }
    ::= { wdmMeanChannelPowerControlEntry 24 }

wdmMeanChannelPowerControlAbsolutePowerOffset OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Absolute power offset

"
    DEFVAL { 0 }
    ::= { wdmMeanChannelPowerControlEntry 25 }

wdmMeanChannelPowerControlRemainingPowerOffset OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Remaining power offset

"
    DEFVAL { 0 }
    ::= { wdmMeanChannelPowerControlEntry 26 }


wdmMeanChannelPowerControlMonitorOffsetTooLarge OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm raised when the OCM monitor port offset is too large

        A: OCM port offset too large

        D: Normal mode
"
    ::= { wdmMeanChannelPowerControlEntry 27 }

wdmMeanChannelPowerControlChannelPowerOutOfRange OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Alarm raised when the incomming channelpower is
        too high or too low to regulate to the wanted
        mean channel power

        A: channel power out of range

        D: Normal mode
"
    ::= { wdmMeanChannelPowerControlEntry 28 }

wdmMeanChannelPowerControlRegulationInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (10..30)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Minimum time between two regulations, a random
        wait time of up to 15 seconds is added to this
        value when calculating when the next regulation
        should occurr.
"
    DEFVAL { 30 }
    ::= { wdmMeanChannelPowerControlEntry 29 }

wdmMeanChannelPowerControlAmplifierOutputPort OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the OA port controlled by
        this control loop
"
    ::= { wdmMeanChannelPowerControlEntry 30 }

wdmMeanChannelPowerControlLatestAmplifierRxPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The latest amplifier input power
"
    ::= { wdmMeanChannelPowerControlEntry 31 }

wdmMeanChannelPowerControlLatestAmplifierTxPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The latest amplifier output power
"
    ::= { wdmMeanChannelPowerControlEntry 32 }

wdmMeanChannelPowerControlLocalId OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Store the local ID since it will be needed when reading backup
"
    ::= { wdmMeanChannelPowerControlEntry 33 }


-- ----------------------------------------------------
-- Table for local link adjustment global
-- ----------------------------------------------------
wdmMeanChannelPowerControlGlobalTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmMeanChannelPowerControlGlobalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Bogus table to hold one entry with a dialog attribute to create
        entries in another table.
"
    ::= { wdmMeanChannelPowerControlGlobalList 1 }

wdmMeanChannelPowerControlGlobalEntry OBJECT-TYPE
    SYNTAX      WdmMeanChannelPowerControlGlobalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of links where the channel power will be adjusted
"
    INDEX { wdmMeanChannelPowerControlGlobalIndex }
    ::= { wdmMeanChannelPowerControlGlobalTable 1 }

WdmMeanChannelPowerControlGlobalEntry ::=
    SEQUENCE {
        wdmMeanChannelPowerControlGlobalIndex           Unsigned32,
        wdmMeanChannelPowerControlGlobalName            MgmtNameString,
        wdmMeanChannelPowerControlGlobalEntryCreate     CommandString }

wdmMeanChannelPowerControlGlobalIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An index assigned to each entry. The index contains information
        about subrack, slot and port for controlled channel.
"
    ::= { wdmMeanChannelPowerControlGlobalEntry 1 }

wdmMeanChannelPowerControlGlobalName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of this bogus entry, that only
        exists to create other entries.
        'bogus'
"
    ::= { wdmMeanChannelPowerControlGlobalEntry 2 }


wdmMeanChannelPowerControlGlobalEntryCreate OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Creates a wdmMeanChannelPowerControl entry in another table
"
    ::= { wdmMeanChannelPowerControlGlobalEntry 3 }


-- ----------------------------------------------------
-- Table for subChannels
-- ----------------------------------------------------

wdmSubChannelTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmSubChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The interface list."
    ::= { wdmSubChannelList 1 }

wdmSubChannelEntry OBJECT-TYPE
    SYNTAX      WdmSubChannelEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the interface list.
"
    INDEX { wdmSubChannelIndex }
    ::= { wdmSubChannelTable 1 }

WdmSubChannelEntry ::=
    SEQUENCE {
        wdmSubChannelIndex                      Unsigned32,
        wdmSubChannelName                       MgmtNameString,
        wdmSubChannelId                         Unsigned32,
        wdmSubChannelType                       INTEGER,
        wdmSubChannelUnequipped                 FaultStatus,
        wdmSubChannelConnectionStatus           DisplayString,
        wdmSubChannelConnectedForeignIndex      Unsigned32,
        wdmSubChannelCrossConnect               CommandString,
        wdmSubChannelDisconnect                 CommandString,
        wdmSubChannelRemoteAccessInterface      DisplayString,
        wdmSubChannelProtectedChannelIndex      Unsigned32 }

wdmSubChannelIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An index assigned to each entry. The index contains
        information about subrack, slot and port for
        controlled channel.
"
    ::= { wdmSubChannelEntry 1 }

wdmSubChannelName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the interface, for example
         'channel:1:2:1-2:19'
         (channel:[subrack]:[slot]:[port]:[channel]).
"
    ::= { wdmSubChannelEntry 2 }

wdmSubChannelId OBJECT-TYPE
    SYNTAX      Unsigned32 (1..30)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The channelId within the line signal.
"
    ::= { wdmSubChannelEntry 3 }

wdmSubChannelType OBJECT-TYPE
    SYNTAX      INTEGER {
                    e1t1 (1),
                    fe (2),
                    gbeFe (3) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows what format(s) this channel is used for.
        (e1t1, fe or gbeFe)
"
    ::= { wdmSubChannelEntry 4 }

wdmSubChannelUnequipped OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows whether the remote node has
        inserted payload into this channel.

        A: No payload is received on this channel

        D: Payload is received again
"
    ::= { wdmSubChannelEntry 5 }

wdmSubChannelConnectionStatus OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the name of the client port and
        possible subchannel that it is connected to
        or Unconnected if not connected.
"
    DEFVAL { "Not connected" }
    ::= { wdmSubChannelEntry 6 }

wdmSubChannelConnectedForeignIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Shows the index of the connected interface.
        Or 0 if unconnected.
"
    DEFVAL { 0 }
    ::= { wdmSubChannelEntry 7 }

wdmSubChannelCrossConnect OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Connect a subchannel to a specific client and
        subchannel
"
    ::= { wdmSubChannelEntry 8 }

wdmSubChannelDisconnect OBJECT-TYPE
    SYNTAX      CommandString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Disconnect existing crossConnections
"
    ::= { wdmSubChannelEntry 9 }

wdmSubChannelRemoteAccessInterface OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the name of the client port in the remote
         access node that terminated the sub channel.

         For sub channels that are not terminated in the
         remote node, 'Not Terminated' is shown.
"
    DEFVAL { "Not connected" }
    ::= { wdmSubChannelEntry 10 }

wdmSubChannelProtectedChannelIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Shows the index of the protected chanel.
        Or 0 if not active protection.
        updates when protection state is changed.
"
    DEFVAL { 0 }
    ::= { wdmSubChannelEntry 11 }

-- ----------------------------------------------------
-- CtrlGlobal group
-- ----------------------------------------------------

wdmCtrlGlobalRegulationInterval OBJECT-TYPE
    SYNTAX      INTEGER {interval5min (5),
                         interval30min (30)}
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval between regulations in normal
        mode.
        interval5min  - 5 minutes interval
        interval30min - 30 minutes interval

"
    DEFVAL { 30 }
        ::= { wdmCtrlGlobal 1 }

wdmCtrlGlobalRegulationStatus OBJECT-TYPE
    SYNTAX      INTEGER {
                        initial (1),
                        searching (2),
                        reading (3),
                        regulating (4),
                        idle (5) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current status of the control loop.

        initial    - No regulation has been performed yet.

        searching  - Trying to detect channels.

        reading    - Reading power values from monitor unit.

        regulating - Adjusting power levels.

        idle       - No regulation is ongoing.
"
    DEFVAL { initial }
        ::= { wdmCtrlGlobal 2 }

wdmCtrlGlobalLastRegulation OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The date and time of the last regulation.
"
    ::= { wdmCtrlGlobal 3 }


wdmCtrlGlobalTimeLeft OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time left to next scheduled regulation.

        Unit: seconds.
"
    ::= { wdmCtrlGlobal 4 }

-- ----------------------------------------------------
-- Table for delay compensation for protection groups
-- ----------------------------------------------------

wdmDelayCompPGTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmDelayCompPGEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Delay compensation for protection groups table."
    ::= { wdmDelayCompPGList 1 }

wdmDelayCompPGEntry OBJECT-TYPE
    SYNTAX      WdmDelayCompPGEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the table.
"
    INDEX { wdmDelayCompPGIndex }
    ::= { wdmDelayCompPGTable 1 }

WdmDelayCompPGEntry ::=
    SEQUENCE {
        wdmDelayCompPGIndex                     Unsigned32,
        wdmDelayCompPGName                      MgmtNameString,
        wdmDelayCompPGUpId                      Unsigned32,
        wdmDelayCompPGAdminStatus               AdminStatusWithNA,
        wdmDelayCompPGOperStatus                OperStatusWithNA,
        wdmDelayCompPGAutoCompensationMode      EnabledDisabledWithNA,
        wdmDelayCompPGAutoCompensationState     INTEGER,
        wdmDelayCompPGDelayDifference           Signed32WithNA,
        wdmDelayCompPGDelayCompensationOOR      FaultStatus,
        wdmDelayCompPGFiberLengthDifferenceOOR  FaultStatus,
        wdmDelayCompPGDelayCompensationReset    ResetWithNA }

wdmDelayCompPGIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An index assigned to each entry. The index contains
        information about subrack, slot and port for
        controlled channel.
"
    ::= { wdmDelayCompPGEntry 1 }

wdmDelayCompPGName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the entry, for example
         'dcpg:1:3:17-18:1:3:19-20'
         (dcpg:[subrack]:[slot]:[leftporttx]-[leftportrx]:[subrack]:[slot]:[rightporttx]-[rightportrx]).
"
    ::= { wdmDelayCompPGEntry 2 }

wdmDelayCompPGUpId OBJECT-TYPE
    SYNTAX      Unsigned32 (1..30)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The UpId within the table.
"
    ::= { wdmDelayCompPGEntry 3 }

wdmDelayCompPGAdminStatus OBJECT-TYPE
    SYNTAX      AdminStatusWithNA
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state for the object.

        service - The object is activated but alarms
        are suppressed. Intended for use during service
        or re-configuration. When service is concluded
        adminStatus should be set to 'up' again.

        up - The object is active. Regulations will be
        performed regularly and on user orders.
        Alarms are not suppressed.
"
    DEFVAL { up }
    ::= { wdmDelayCompPGEntry 4 }

wdmDelayCompPGOperStatus OBJECT-TYPE
    SYNTAX      OperStatusWithNA
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state for the object.

        notPresent - The object is not available.

        down - The object is de-activated or there are
        faults preventing its transition to the 'up' state.

        up - The object is active.
"
    DEFVAL { up }
    ::= { wdmDelayCompPGEntry 5 }

wdmDelayCompPGAutoCompensationMode OBJECT-TYPE
    SYNTAX      EnabledDisabledWithNA
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The automatic delay compensation mode.
"
    DEFVAL { disabled }
    ::= { wdmDelayCompPGEntry 6 }

wdmDelayCompPGAutoCompensationState OBJECT-TYPE
    SYNTAX INTEGER {
        state1 (1),
        state2 (2),
        notApplicable (2147483647) }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The automatic delay compensation state.
"
    ::= { wdmDelayCompPGEntry 7 }

wdmDelayCompPGDelayDifference OBJECT-TYPE
    SYNTAX      Signed32WithNA
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current delay difference in nanoseconds, after compensation.
        The difference is calculated as (right line current delay) - (left line current delay).
"
    ::= { wdmDelayCompPGEntry 8 }

wdmDelayCompPGDelayCompensationOOR OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Delay compensation out-of-range.

        alarm: The delay compensation is out-of-range.

        ok: The delay compensation is in range.

"
    ::= { wdmDelayCompPGEntry 9 }

wdmDelayCompPGFiberLengthDifferenceOOR OBJECT-TYPE
    SYNTAX      FaultStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Fiber length difference out-of-range.

        alarm: The fiber length difference is out-of-range.

        ok: The fiber length difference is in range.

"
    ::= { wdmDelayCompPGEntry 10 }

wdmDelayCompPGDelayCompensationReset OBJECT-TYPE
    SYNTAX      ResetWithNA
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Resets the automatic delay compensation.
         After a successful reset the default value
         (normal) is restored in this attribute.

"
    DEFVAL { normal }
    ::= { wdmDelayCompPGEntry 11 }

-- ----------------------------------------------------
-- Table for delay compensation for links
-- ----------------------------------------------------

wdmDelayCompLinkTable  OBJECT-TYPE
    SYNTAX      SEQUENCE OF WdmDelayCompLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Delay compensation for protection group links table."
    ::= { wdmDelayCompLinkList 1 }

wdmDelayCompLinkEntry OBJECT-TYPE
    SYNTAX      WdmDelayCompLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry in the table.
"
    INDEX { wdmDelayCompLinkIndex }
    ::= { wdmDelayCompLinkTable 1 }

WdmDelayCompLinkEntry ::=
    SEQUENCE {
        wdmDelayCompLinkIndex                     Unsigned32,
        wdmDelayCompLinkName                      MgmtNameString,
        wdmDelayCompLinkUpId                      Unsigned32,
        wdmDelayCompLinkCurrentDelayCompensation  Signed32WithNA,
        wdmDelayCompLinkWantedDelayCompensation   Signed32WithNA }

wdmDelayCompLinkIndex OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2147483647)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An index assigned to each entry. The index contains
        information about subrack, slot and port for
        controlled channel.
"
    ::= { wdmDelayCompLinkEntry 1 }

wdmDelayCompLinkName OBJECT-TYPE
    SYNTAX      MgmtNameString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The management name of the entry, for example
         'dclink:1:3:17-18'
         (dclink:[subrack]:[slot]:[porttx]-[portrx]).
"
    ::= { wdmDelayCompLinkEntry 2 }

wdmDelayCompLinkUpId OBJECT-TYPE
    SYNTAX      Unsigned32 (1..30)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The UpId within the table.
"
    ::= { wdmDelayCompLinkEntry 3 }


wdmDelayCompLinkCurrentDelayCompensation OBJECT-TYPE
    SYNTAX      Signed32WithNA (0..47233)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current delay compensation in nanoseconds.
        Note, the granularity of this value is determined
        by the hardware capabilities.
"
    ::= { wdmDelayCompLinkEntry 4 }

wdmDelayCompLinkWantedDelayCompensation OBJECT-TYPE
    SYNTAX      Signed32WithNA (0..47233)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The wanted delay compensation in nanoseconds.
        Note, this value is only used by the system when
        automatic delay compensation mode is disabled.
"
    DEFVAL { 0 }
    ::= { wdmDelayCompLinkEntry 5 }

-- ----------------------------------------------------
-- Notifications
-- ----------------------------------------------------

wdmNotifyPrefix OBJECT IDENTIFIER ::= { lumentisWdmNotifications 0 }

wdmProtOperStatusBothDown NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName }
    STATUS      deprecated
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 1 }

wdmProtOperStatusLeftDownRightUp NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName }
    STATUS      deprecated
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 2 }

wdmProtOperStatusLeftDownRightStandby NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName }
    STATUS      deprecated
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 3 }

wdmProtOperStatusLeftStandbyRightDown NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName }
    STATUS      deprecated
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 4 }

wdmProtOperStatusLeftStandbyRightUp NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName }
    STATUS      deprecated
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 5 }

wdmProtOperStatusLeftUpRightDown NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName }
    STATUS      deprecated
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 6 }

wdmProtOperStatusLeftUpRightStandby NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName }
    STATUS      deprecated
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 7 }

wdmIfLaserStatusOn NOTIFICATION-TYPE
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfLaserStatusLastChangeTime }
    STATUS      current
    DESCRIPTION
        "Sent when the laser of the interface is turned
        on.
"
    ::= { wdmNotifyPrefix 8 }

wdmIfLaserStatusOff NOTIFICATION-TYPE
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfLaserStatusLastChangeTime }
    STATUS      current
    DESCRIPTION
        "Sent when the laser of the interface is turned
        off.
"
    ::= { wdmNotifyPrefix 9 }

wdmProtStatusChanged NOTIFICATION-TYPE
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtLastChangeTime }
    STATUS      current
    DESCRIPTION
        "Sent when the protection group changes
        operational status.
"
    ::= { wdmNotifyPrefix 10 }

-- ----------------------------------------------------
-- Object and event groups
-- ----------------------------------------------------

wdmGeneralGroup OBJECT-GROUP
    OBJECTS {
        wdmGeneralTestAndIncr,
        wdmGeneralMibSpecVersion,
        wdmGeneralMibImplVersion,
        wdmGeneralLastChangeTime }
    STATUS      current
    DESCRIPTION
        "The general objects."
    ::= { lumWdmGroups 1 }

wdmIfGroup OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfErroredSeconds,
        wdmIfSeverelyErroredSeconds,
        wdmIfBackgroundBlockErrors,
        wdmIfUnavailableSeconds,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects."
    ::= { lumWdmGroups 2 }

wdmProtGroup OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtOperStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure }
    STATUS      deprecated
    DESCRIPTION
        "Note: This group is deprecated.

        The protection group objects."
    ::= { lumWdmGroups 3 }

wdmNotificationGroup NOTIFICATION-GROUP
    NOTIFICATIONS {
        wdmProtOperStatusBothDown,
        wdmProtOperStatusLeftDownRightUp,
        wdmProtOperStatusLeftDownRightStandby,
        wdmProtOperStatusLeftStandbyRightDown,
        wdmProtOperStatusLeftStandbyRightUp,
        wdmProtOperStatusLeftUpRightDown,
        wdmProtOperStatusLeftUpRightStandby,
        wdmIfLaserStatusOn,
        wdmIfLaserStatusOff
    }
    STATUS      deprecated
    DESCRIPTION
        "The WDM notifications."
    ::= { lumWdmGroups 4 }

wdmPassiveIfGroup OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfLambdaMax,
        wdmPassiveIfLastChangeTime }
    STATUS      deprecated
    DESCRIPTION
        "The passive i/f group objects."
    ::= { lumWdmGroups 5 }

wdmGeneralGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmGeneralLastChangeTime }
    STATUS      deprecated
    DESCRIPTION
        "The WDM general objects v2."
    ::= { lumWdmGroups 6 }

wdmIfGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfErroredSeconds,
        wdmIfSeverelyErroredSeconds,
        wdmIfBackgroundBlockErrors,
        wdmIfUnavailableSeconds,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfJ0PathTrace,
        wdmIfInbandMode }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V2."
    ::= { lumWdmGroups 7 }

wdmPassiveIfGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfLambdaMax }
    STATUS      deprecated
    DESCRIPTION
        "The passive i/f group objects V2."
    ::= { lumWdmGroups 8 }

wdmIfGroupV3 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfErroredSeconds,
        wdmIfSeverelyErroredSeconds,
        wdmIfBackgroundBlockErrors,
        wdmIfUnavailableSeconds,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfJ0PathTrace,
        wdmIfInbandMode,
        wdmIfInbandStatus }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V3."
    ::= { lumWdmGroups 9 }

wdmPassiveIfGroupV3 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfLambdaMax,
        wdmPassiveIfExpectedLambda }
    STATUS      deprecated
    DESCRIPTION
        "The passive i/f group objects V3."
    ::= { lumWdmGroups 10 }

wdmIfGroupV4 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfErroredSeconds,
        wdmIfSeverelyErroredSeconds,
        wdmIfBackgroundBlockErrors,
        wdmIfUnavailableSeconds,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfJ0PathTrace,
        wdmIfInbandMode,
        wdmIfInbandStatus,
        wdmIfExpectedTxLambda }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V4."
    ::= { lumWdmGroups 11 }

wdmPassiveIfGroupV4 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfExpectedLambda }
    STATUS      deprecated
    DESCRIPTION
        "The passive i/f group objects V4."
    ::= { lumWdmGroups 12 }

wdmIfGroupV5 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfErroredSeconds,
        wdmIfSeverelyErroredSeconds,
        wdmIfBackgroundBlockErrors,
        wdmIfUnavailableSeconds,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfJ0PathTrace,
        wdmIfInbandMode,
        wdmIfInbandStatus,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V5."
    ::= { lumWdmGroups 13 }

wdmProtGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus }
    STATUS      deprecated
    DESCRIPTION
        "The protection group objects v2."
    ::= { lumWdmGroups 14 }

wdmIfGroupV6 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfErroredSeconds,
        wdmIfSeverelyErroredSeconds,
        wdmIfBackgroundBlockErrors,
        wdmIfUnavailableSeconds,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfJ0PathTrace,
        wdmIfInbandMode,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V6."
    ::= { lumWdmGroups 15 }


wdmNotificationGroupV2 NOTIFICATION-GROUP
    NOTIFICATIONS {
        wdmIfLaserStatusOn,
        wdmIfLaserStatusOff,
        wdmProtStatusChanged
    }
    STATUS      current
    DESCRIPTION
        "The WDM notifications V2."
    ::= { lumWdmGroups 16 }

wdmIfGroupV7 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfJ0PathTrace,
        wdmIfInbandMode,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfUnexpectedTxLambda }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V7."
    ::= { lumWdmGroups 17 }

wdmGeneralGroupV3 OBJECT-GROUP
    OBJECTS {
        wdmGeneralLastChangeTime,
        wdmGeneralStateLastChangeTime }
    STATUS      deprecated
    DESCRIPTION
        "The WDM general objects v3."
    ::= { lumWdmGroups 18 }

wdmIfGroupV8 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfInbandMode,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V8."
    ::= { lumWdmGroups 19 }

wdmIfGroupV9 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfInbandMode,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V9."
    ::= { lumWdmGroups 20 }

wdmIfGroupV10 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V10."
    ::= { lumWdmGroups 21 }

wdmIfGroupV11 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V11."
    ::= { lumWdmGroups 22 }

wdmIfGroupV12 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V12. (2.2)"
    ::= { lumWdmGroups 23 }

wdmPassiveIfGroupV5 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfExpectedLambda,
        wdmPassiveIfUnexpectedLambda,
        wdmPassiveIfAdminStatus,
        wdmPassiveIfOperStatus }
    STATUS      deprecated
    DESCRIPTION
        "The passive i/f group objects V5."
    ::= { lumWdmGroups 24 }

wdmIfGroupV13 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V13. (3.0)"
    ::= { lumWdmGroups 25 }

wdmGeneralGroupV4 OBJECT-GROUP
    OBJECTS {
        wdmGeneralLastChangeTime,
        wdmGeneralStateLastChangeTime,
        wdmGeneralWdmIfTableSize,
        wdmGeneralWdmPassiveIfTableSize,
        wdmGeneralWdmProtTableSize }
    STATUS      deprecated
    DESCRIPTION
        "The WDM general objects v4 (3.1)."
    ::= { lumWdmGroups 26 }

wdmProtGroupV3 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtProtectionType }
    STATUS      deprecated
    DESCRIPTION
        "The protection group objects v3 (3.1)."
    ::= { lumWdmGroups 27 }

wdmIfGroupV14 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V14. (4.0)"
    ::= { lumWdmGroups 28 }

wdmPassiveIfGroupV6 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfExpectedLambda,
        wdmPassiveIfUnexpectedLambda,
        wdmPassiveIfAdminStatus,
        wdmPassiveIfOperStatus,
        wdmPassiveIfObjectProperty }
    STATUS      current
    DESCRIPTION
        "The passive i/f group objects V6 (4.0)."
    ::= { lumWdmGroups 29 }

wdmProtGroupV4 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtProtectionType,
        wdmProtObjectProperty }
    STATUS      deprecated
    DESCRIPTION
        "The protection group objects v4 (4.0)."
    ::= { lumWdmGroups 30 }

wdmProtGroupV5 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtProtectionType,
        wdmProtObjectProperty,
        wdmProtWrapperMode,
        wdmProtWrapperState }
    STATUS      deprecated
    DESCRIPTION
        "The protection group objects v5 (4.1)."
    ::= { lumWdmGroups 31 }

wdmIfGroupV15 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V15. (5.0)"
    ::= { lumWdmGroups 32 }

wdmIfGroupV16 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V16. (6.0)"
    ::= { lumWdmGroups 33 }

wdmIfGroupV17 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V17. (9.0)"
    ::= { lumWdmGroups 34 }

wdmIfGroupV18 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V18. (9.1)"
    ::= { lumWdmGroups 35 }

wdmIfGroupV19 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
        wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V19. (10.0)"
    ::= { lumWdmGroups 36 }

wdmVc4Group OBJECT-GROUP
    OBJECTS {
        wdmVc4Index,
        wdmVc4Name,
        wdmVc4Descr,
        wdmVc4Subrack,
        wdmVc4Slot,
        wdmVc4TxPort,
        wdmVc4RxPort,
        wdmVc4Vc4,
        wdmVc4ObjectProperty,
        wdmVc4AuAlarmIndicationSignal,
        wdmVc4AuLossOfPointer,
        wdmVc4RxSignalStatus,
        wdmVc4ConcatenationStatus,
        wdmVc4PayloadStatus }
    STATUS    deprecated
    DESCRIPTION
        "The vc4 objects (10.0)."
    ::= { lumWdmGroups 38 }

wdmGeneralGroupV5 OBJECT-GROUP
    OBJECTS {
        wdmGeneralLastChangeTime,
        wdmGeneralStateLastChangeTime,
        wdmGeneralWdmIfTableSize,
        wdmGeneralWdmPassiveIfTableSize,
        wdmGeneralWdmProtTableSize,
        wdmGeneralWdmVc4TableSize }
    STATUS      current
    DESCRIPTION
        "The WDM general objects v5 (10.0)."
    ::= { lumWdmGroups 39 }

wdmIfGroupV20 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
        wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V20. (11.0)"
    ::= { lumWdmGroups 40 }

wdmIfGroupV21 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
        wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V21. (12.0)"
    ::= { lumWdmGroups 41 }

wdmIfGroupV22 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V22. (13.0)"
    ::= { lumWdmGroups 42 }

wdmRemoteProtGroup OBJECT-GROUP
    OBJECTS {
        wdmRemoteProtIndex,
        wdmRemoteProtName,
        wdmRemoteProtDescr,
        wdmRemoteProtLocalSubrack,
        wdmRemoteProtLocalSlot,
        wdmRemoteProtLocalPort,
        wdmRemoteProtCommSubrack,
        wdmRemoteProtCommSlot,
        wdmRemoteProtCommPort,
        wdmRemoteProtCommInterface,
        wdmRemoteProtLastChangeTime,
        wdmRemoteProtIpAddress,
        wdmRemoteProtIdentifier,
        wdmRemoteProtRole,
        wdmRemoteProtAdminStatus,
        wdmRemoteProtRowStatus,
        wdmRemoteProtActiveSide,
        wdmRemoteProtLocalStatus,
        wdmRemoteProtRemoteStatus,
        wdmRemoteProtObjectProperty,
        wdmRemoteProtServiceDegraded,
        wdmRemoteProtServiceFailure,
        wdmRemoteProtSetup,
        wdmRemoteProtSetupFailure,
        wdmRemoteProtRoleConflict,
        wdmRemoteProtCommunicationFailure
    }
    STATUS      current
    DESCRIPTION
        "The remote protection group objects. (13.0)"
    ::= { lumWdmGroups 43 }

wdmProtGroupV6 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtProtectionType,
        wdmProtObjectProperty,
        wdmProtWrapperMode,
        wdmProtWrapperState,
        wdmProtLeftCommSubrack,
        wdmProtLeftCommSlot,
        wdmProtLeftCommPort,
        wdmProtRightCommSubrack,
        wdmProtRightCommSlot,
        wdmProtRightCommPort,
        wdmProtLeftCommInterface,
        wdmProtRightCommInterface,
        wdmProtCommunicationFailure }

    STATUS      current
    DESCRIPTION
        "The protection group objects v6 (13.0)."
    ::= { lumWdmGroups 44 }

wdmCtrlChannelGroup OBJECT-GROUP
    OBJECTS {
        wdmCtrlChannelIndex,
        wdmCtrlChannelName,
        wdmCtrlChannelSubrack,
        wdmCtrlChannelSlot,
        wdmCtrlChannelTxPort,
        wdmCtrlChannelChannel,
        wdmCtrlChannelGroupNumber,
        wdmCtrlChannelAdminStatus,
        wdmCtrlChannelWantedOutputPower,
        wdmCtrlChannelCurrentOutputPower,
        wdmCtrlChannelCurrentAttenuation,
        wdmCtrlChannelForceRegulationCommand,
        wdmCtrlChannelOuputPowerControlFailure,
        wdmCtrlChannelCurrentPowerOutOfRange,
        wdmCtrlChannelAttenuationOutOfRange
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm optical control channel objects. (13.0)"
    ::= { lumWdmGroups 45 }

wdmCtrlGroupGroup OBJECT-GROUP
    OBJECTS {
        wdmCtrlGroupIndex,
        wdmCtrlGroupName,
        wdmCtrlGroupDescr,
        wdmCtrlGroupGroupNumber,
        wdmCtrlGroupSubrack,
        wdmCtrlGroupSlot,
        wdmCtrlGroupPort,
        wdmCtrlGroupMonitorName,
        wdmCtrlGroupAdminStatus,
        wdmCtrlGroupControlMode,
        wdmCtrlGroupConfigurationCommand,
        wdmCtrlGroupForceRegulationCommand,
        wdmCtrlGroupLockedRange,
        wdmCtrlGroupRegulationRange,
        wdmCtrlGroupRegulationLastChangeTime,
        wdmCtrlGroupCommissioningMode
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm optical control group objects. (13.0)"
    ::= { lumWdmGroups 46 }

wdmCtrlGroupGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmCtrlGroupIndex,
        wdmCtrlGroupName,
        wdmCtrlGroupDescr,
        wdmCtrlGroupGroupNumber,
        wdmCtrlGroupSubrack,
        wdmCtrlGroupSlot,
        wdmCtrlGroupPort,
        wdmCtrlGroupMonitorName,
        wdmCtrlGroupAdminStatus,
        wdmCtrlGroupControlMode,
        wdmCtrlGroupConfigurationCommand,
        wdmCtrlGroupForceRegulationCommand,
        wdmCtrlGroupLockedRange,
        wdmCtrlGroupRegulationRange,
        wdmCtrlGroupRegulationLastChangeTime,
        wdmCtrlGroupCommissioningMode,
        wdmCtrlGroupAssociateChannel,
        wdmCtrlGroupNoOfChannels,
        wdmCtrlGroupStatus,
        wdmCtrlGroupTimeLeft
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm optical control group objects. (14.0)"
    ::= { lumWdmGroups 48 }

wdmSubChannelGroup OBJECT-GROUP
    OBJECTS {
        wdmSubChannelIndex,
        wdmSubChannelName,
        wdmSubChannelId,
        wdmSubChannelType,
        wdmSubChannelUnequipped,
        wdmSubChannelConnectionStatus,
        wdmSubChannelConnectedForeignIndex,
        wdmSubChannelCrossConnect,
        wdmSubChannelDisconnect,
        wdmSubChannelRemoteAccessInterface
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm subchannel objects. (15.0)"
    ::= { lumWdmGroups 49 }

wdmGeneralGroupV6 OBJECT-GROUP
    OBJECTS {
        wdmGeneralLastChangeTime,
        wdmGeneralStateLastChangeTime,
        wdmGeneralWdmIfTableSize,
        wdmGeneralWdmPassiveIfTableSize,
        wdmGeneralWdmProtTableSize,
        wdmGeneralWdmVc4TableSize,
        wdmGeneralWdmCtrlChannelTableSize,
        wdmGeneralWdmCtrlGroupTableSize,
        wdmGeneralWdmSubChannelTableSize }
    STATUS      deprecated
    DESCRIPTION
        "The WDM general objects v6 (15.0)."
    ::= { lumWdmGroups 50 }

wdmIfGroupV23 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded
         }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V23. (15.0)"
    ::= { lumWdmGroups 51 }

wdmVc4GroupV2 OBJECT-GROUP
    OBJECTS {
        wdmVc4Index,
        wdmVc4Name,
        wdmVc4Descr,
        wdmVc4Subrack,
        wdmVc4Slot,
        wdmVc4TxPort,
        wdmVc4RxPort,
        wdmVc4Vc4,
        wdmVc4ObjectProperty,
        wdmVc4AuAlarmIndicationSignal,
        wdmVc4AuLossOfPointer,
        wdmVc4RxSignalStatus,
        wdmVc4ConcatenationStatus,
        wdmVc4PayloadStatus,
        wdmVc4ConnectionStatus,
        wdmVc4ConnectedForeignIndex,
        wdmVc4AdminStatus }
    STATUS    current
    DESCRIPTION
        "The vc4 objects (16.0)."
    ::= { lumWdmGroups 52 }

wdmCtrlChannelGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmCtrlChannelIndex,
        wdmCtrlChannelName,
        wdmCtrlChannelSubrack,
        wdmCtrlChannelSlot,
        wdmCtrlChannelTxPort,
        wdmCtrlChannelChannel,
        wdmCtrlChannelGroupNumber,
        wdmCtrlChannelAdminStatus,
        wdmCtrlChannelWantedOutputPower,
        wdmCtrlChannelCurrentOutputPower,
        wdmCtrlChannelCurrentAttenuation,
        wdmCtrlChannelForceRegulationCommand,
        wdmCtrlChannelOuputPowerControlFailure,
        wdmCtrlChannelCurrentPowerOutOfRange,
        wdmCtrlChannelAttenuationOutOfRange,
        wdmCtrlChannelStatus,
        wdmCtrlChannelStartupChannel,
        wdmCtrlChannelMonitorIndex,
        wdmCtrlChannelStartupCommand
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm optical control channel objects. (16.0)"
    ::= { lumWdmGroups 53 }


wdmCtrlGlobalGroup OBJECT-GROUP
    OBJECTS {
        wdmCtrlGlobalRegulationInterval,
        wdmCtrlGlobalRegulationStatus,
        wdmCtrlGlobalLastRegulation,
        wdmCtrlGlobalTimeLeft
    }
    STATUS      current
    DESCRIPTION
        "The wdm optical control general objects. (16.0)"
    ::= { lumWdmGroups 54 }

wdmSubChannelGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmSubChannelIndex,
        wdmSubChannelName,
        wdmSubChannelId,
        wdmSubChannelType,
        wdmSubChannelUnequipped,
        wdmSubChannelConnectionStatus,
        wdmSubChannelConnectedForeignIndex,
        wdmSubChannelCrossConnect,
        wdmSubChannelDisconnect,
        wdmSubChannelRemoteAccessInterface,
        wdmSubChannelProtectedChannelIndex
    }
    STATUS      current
    DESCRIPTION
        "The wdm subchannel objects. (16.0)"
    ::= { lumWdmGroups 55 }

wdmProtGroupV7 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtProtectionType,
        wdmProtObjectProperty,
        wdmProtWrapperMode,
        wdmProtWrapperState,
        wdmProtLeftCommSubrack,
        wdmProtLeftCommSlot,
        wdmProtLeftCommPort,
        wdmProtRightCommSubrack,
        wdmProtRightCommSlot,
        wdmProtRightCommPort,
        wdmProtLeftCommInterface,
        wdmProtRightCommInterface,
        wdmProtCommunicationFailure,
        wdmProtHubTrafficConfigMismatch
    }
    STATUS      deprecated
    DESCRIPTION
        "The protection group objects v6 (17.0)."
    ::= { lumWdmGroups 56 }

wdmIfGroupV24 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
            wdmIfHubProtectionMode
         }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V24. (17.0)"
    ::= { lumWdmGroups 57 }

wdmIfGroupV25 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
        wdmIfHubProtectionMode,
        wdmIfActualFormat
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V25. (18.0)"
    ::= { lumWdmGroups 58 }

wdmCtrlChannelGroupV3 OBJECT-GROUP
    OBJECTS {
        wdmCtrlChannelIndex,
        wdmCtrlChannelName,
        wdmCtrlChannelSubrack,
        wdmCtrlChannelSlot,
        wdmCtrlChannelTxPort,
        wdmCtrlChannelChannel,
        wdmCtrlChannelGroupNumber,
        wdmCtrlChannelAdminStatus,
        wdmCtrlChannelWantedOutputPower,
        wdmCtrlChannelCurrentOutputPower,
        wdmCtrlChannelCurrentAttenuation,
        wdmCtrlChannelForceRegulationCommand,
        wdmCtrlChannelOuputPowerControlFailure,
        wdmCtrlChannelCurrentPowerOutOfRange,
        wdmCtrlChannelAttenuationOutOfRange,
        wdmCtrlChannelStatus,
        wdmCtrlChannelStartupChannel,
        wdmCtrlChannelMonitorIndex,
        wdmCtrlChannelStartupCommand,
        wdmCtrlChannelSfpMissing,
        wdmCtrlChannelSfpMediaMismatch,
        wdmCtrlChannelLossOfSignal
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm optical control channel objects. (19.0)"
    ::= { lumWdmGroups 59 }

wdmIfGroupV26 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
        wdmIfHubProtectionMode,
        wdmIfActualFormat,
        wdmIfTdcDispersion,
        wdmIfTdcDispersionCommand,
        wdmIfTdcDispersionMode,
        wdmIfLineControlLoopCurrentState,
        wdmIfSignalDegradeThreshold
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V26. (20.0)"
    ::= { lumWdmGroups 60 }

wdmProtGroupV8 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtProtectionType,
        wdmProtObjectProperty,
        wdmProtWrapperMode,
        wdmProtWrapperState,
        wdmProtLeftCommSubrack,
        wdmProtLeftCommSlot,
        wdmProtLeftCommPort,
        wdmProtRightCommSubrack,
        wdmProtRightCommSlot,
        wdmProtRightCommPort,
        wdmProtLeftCommInterface,
        wdmProtRightCommInterface,
        wdmProtCommunicationFailure,
        wdmProtHubTrafficConfigMismatch,
        wdmProtSignalDegradeProtection
    }
    STATUS      deprecated
    DESCRIPTION
        "The protection group objects v8 (20.0)."
    ::= { lumWdmGroups 61 }

wdmProtGroupV9 OBJECT-GROUP
    OBJECTS {
        wdmProtIndex,
        wdmProtName,
        wdmProtDescr,
        wdmProtLeftSubrack,
        wdmProtLeftSlot,
        wdmProtLeftPort,
        wdmProtRightSubrack,
        wdmProtRightSlot,
        wdmProtRightPort,
        wdmProtLastChangeTime,
        wdmProtAdminStatus,
        wdmProtRowStatus,
        wdmProtServiceDegraded,
        wdmProtServiceFailure,
        wdmProtActiveSide,
        wdmProtLeftStatus,
        wdmProtRightStatus,
        wdmProtProtectionType,
        wdmProtObjectProperty,
        wdmProtWrapperMode,
        wdmProtWrapperState,
        wdmProtLeftCommSubrack,
        wdmProtLeftCommSlot,
        wdmProtLeftCommPort,
        wdmProtRightCommSubrack,
        wdmProtRightCommSlot,
        wdmProtRightCommPort,
        wdmProtLeftCommInterface,
        wdmProtRightCommInterface,
        wdmProtCommunicationFailure,
        wdmProtHubTrafficConfigMismatch,
        wdmProtSignalDegradeProtection,
        wdmProtRevertiveSwitch,
        wdmProtRevertiveSwitchWtrTimer,
        wdmProtRevertiveSwitchPrimaryPath,
        wdmProtRevertiveSwitchSecondaryPath,
        wdmProtSecondaryPathUsed
    }
    STATUS      current
    DESCRIPTION
        "The protection group objects v9 (22.0)."
    ::= { lumWdmGroups 62 }

wdmCtrlGroupGroupV3 OBJECT-GROUP
    OBJECTS {
        wdmCtrlGroupIndex,
        wdmCtrlGroupName,
        wdmCtrlGroupDescr,
        wdmCtrlGroupGroupNumber,
        wdmCtrlGroupSubrack,
        wdmCtrlGroupSlot,
        wdmCtrlGroupPort,
        wdmCtrlGroupMonitorName,
        wdmCtrlGroupAdminStatus,
        wdmCtrlGroupControlMode,
        wdmCtrlGroupConfigurationCommand,
        wdmCtrlGroupForceRegulationCommand,
        wdmCtrlGroupLockedRange,
        wdmCtrlGroupRegulationRange,
        wdmCtrlGroupRegulationLastChangeTime,
        wdmCtrlGroupCommissioningMode,
        wdmCtrlGroupAssociateChannel,
        wdmCtrlGroupNoOfChannels,
        wdmCtrlGroupStatus,
        wdmCtrlGroupTimeLeft,
        wdmCtrlGroupOutputPowerMismatch
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm optical control group objects. (23.0)"
    ::= { lumWdmGroups 63 }


wdmCtrlGroupGroupV4 OBJECT-GROUP
    OBJECTS {
        wdmCtrlGroupIndex,
        wdmCtrlGroupName,
        wdmCtrlGroupDescr,
        wdmCtrlGroupGroupNumber,
        wdmCtrlGroupSubrack,
        wdmCtrlGroupSlot,
        wdmCtrlGroupPort,
        wdmCtrlGroupMonitorName,
        wdmCtrlGroupAdminStatus,
        wdmCtrlGroupControlMode,
        wdmCtrlGroupConfigurationCommand,
        wdmCtrlGroupForceRegulationCommand,
        wdmCtrlGroupLockedRange,
        wdmCtrlGroupRegulationRange,
        wdmCtrlGroupRegulationLastChangeTime,
        wdmCtrlGroupCommissioningMode,
        wdmCtrlGroupAssociateChannel,
        wdmCtrlGroupNoOfChannels,
        wdmCtrlGroupStatus,
        wdmCtrlGroupTimeLeft,
        wdmCtrlGroupOutputPowerMismatch,
        wdmCtrlGroupTotalPower
    }
    STATUS     deprecated
    DESCRIPTION
        "The wdm optical control group objects. (29.0)"
    ::= { lumWdmGroups 75 }


wdmCtrlGroupGroupV5 OBJECT-GROUP
    OBJECTS {
        wdmCtrlGroupIndex,
        wdmCtrlGroupName,
        wdmCtrlGroupDescr,
        wdmCtrlGroupGroupNumber,
        wdmCtrlGroupSubrack,
        wdmCtrlGroupSlot,
        wdmCtrlGroupPort,
        wdmCtrlGroupMonitorName,
        wdmCtrlGroupAdminStatus,
        wdmCtrlGroupControlMode,
        wdmCtrlGroupConfigurationCommand,
        wdmCtrlGroupForceRegulationCommand,
        wdmCtrlGroupLockedRange,
        wdmCtrlGroupRegulationRange,
        wdmCtrlGroupRegulationLastChangeTime,
        wdmCtrlGroupCommissioningMode,
        wdmCtrlGroupAssociateChannel,
        wdmCtrlGroupNoOfChannels,
        wdmCtrlGroupStatus,
        wdmCtrlGroupTimeLeft,
        wdmCtrlGroupOutputPowerMismatch,
        wdmCtrlGroupTotalPower,
        wdmCtrlGroupChannelStartupCommand
    }
    STATUS      current
    DESCRIPTION
        "The wdm optical control group objects. (R30.0)"
    ::= { lumWdmGroups 76 }

wdmIfGroupV27 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
        wdmIfHubProtectionMode,
        wdmIfActualFormat,
        wdmIfTdcDispersion,
        wdmIfTdcDispersionCommand,
        wdmIfTdcDispersionMode,
        wdmIfLineControlLoopCurrentState,
        wdmIfSignalDegradeThreshold,
        wdmIfTrxThresholdOptimizationState
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V27. (23.0)"
    ::= { lumWdmGroups 64 }

wdmIfGroupV28 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
        wdmIfHubProtectionMode,
        wdmIfActualFormat,
        wdmIfTdcDispersion,
        wdmIfTdcDispersionCommand,
        wdmIfTdcDispersionMode,
        wdmIfLineControlLoopCurrentState,
        wdmIfSignalDegradeThreshold,
        wdmIfTrxThresholdOptimizationState,
        wdmIfTrxDecisionThreshold
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V28. (23.1)"
    ::= { lumWdmGroups 65 }

wdmIfGroupV29 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
        wdmIfHubProtectionMode,
        wdmIfActualFormat,
        wdmIfTdcDispersion,
        wdmIfTdcDispersionCommand,
        wdmIfTdcDispersionMode,
        wdmIfLineControlLoopCurrentState,
        wdmIfSignalDegradeThreshold,
        wdmIfTrxThresholdOptimizationState,
        wdmIfTrxDecisionThreshold,
        wdmIfSwControlledLaserShutdown,
        wdmIfChangeSwControlledLaserShutdownCommand,
        wdmIfControlledLaserShutdownEnabled
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V29. (25.0)"
    ::= { lumWdmGroups 66 }

    wdmDelayCompPGGroup OBJECT-GROUP
    OBJECTS {
        wdmDelayCompPGIndex,
        wdmDelayCompPGName,
        wdmDelayCompPGUpId,
        wdmDelayCompPGAdminStatus,
        wdmDelayCompPGOperStatus,
        wdmDelayCompPGAutoCompensationMode,
        wdmDelayCompPGAutoCompensationState,
        wdmDelayCompPGDelayDifference,
        wdmDelayCompPGDelayCompensationOOR,
        wdmDelayCompPGFiberLengthDifferenceOOR,
        wdmDelayCompPGDelayCompensationReset
    }
    STATUS      current
    DESCRIPTION
        "The wdm delay compensation for PG objects. (27.1)"
    ::= { lumWdmGroups 67 }

    wdmDelayCompLinkGroup OBJECT-GROUP
    OBJECTS {
        wdmDelayCompLinkIndex,
        wdmDelayCompLinkName,
        wdmDelayCompLinkUpId,
        wdmDelayCompLinkCurrentDelayCompensation,
        wdmDelayCompLinkWantedDelayCompensation
    }
    STATUS      current
    DESCRIPTION
        "The wdm delay compensation for link objects. (27.1)"
    ::= { lumWdmGroups 68 }

wdmCtrlChannelGroupV4 OBJECT-GROUP
    OBJECTS {
        wdmCtrlChannelIndex,
        wdmCtrlChannelName,
        wdmCtrlChannelSubrack,
        wdmCtrlChannelSlot,
        wdmCtrlChannelTxPort,
        wdmCtrlChannelChannel,
        wdmCtrlChannelGroupNumber,
        wdmCtrlChannelAdminStatus,
        wdmCtrlChannelWantedOutputPower,
        wdmCtrlChannelCurrentOutputPower,
        wdmCtrlChannelCurrentAttenuation,
        wdmCtrlChannelForceRegulationCommand,
        wdmCtrlChannelOuputPowerControlFailure,
        wdmCtrlChannelCurrentPowerOutOfRange,
        wdmCtrlChannelAttenuationOutOfRange,
        wdmCtrlChannelStatus,
        wdmCtrlChannelStartupChannel,
        wdmCtrlChannelMonitorIndex,
        wdmCtrlChannelStartupCommand,
        wdmCtrlChannelSfpMissing,
        wdmCtrlChannelSfpMediaMismatch,
        wdmCtrlChannelLossOfSignal,
        wdmCtrlChannelDescr
    }
    STATUS     deprecated
    DESCRIPTION
        "The wdm optical control channel objects. (28.0)"
    ::= { lumWdmGroups 69 }

wdmIfGroupV30 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
        wdmIfHubProtectionMode,
        wdmIfActualFormat,
        wdmIfTdcDispersion,
        wdmIfTdcDispersionCommand,
        wdmIfTdcDispersionMode,
        wdmIfLineControlLoopCurrentState,
        wdmIfSignalDegradeThreshold,
        wdmIfTrxThresholdOptimizationState,
        wdmIfTrxDecisionThreshold,
        wdmIfSwControlledLaserShutdown,
        wdmIfChangeSwControlledLaserShutdownCommand,
        wdmIfControlledLaserShutdownEnabled,
        wdmIfAid,
        wdmIfPhysicalLocation
    }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface objects V30. (28.0)"
    ::= { lumWdmGroups 70 }

wdmPassiveIfGroupV7 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfExpectedLambda,
        wdmPassiveIfUnexpectedLambda,
        wdmPassiveIfAdminStatus,
        wdmPassiveIfOperStatus,
        wdmPassiveIfObjectProperty,
        wdmPassiveIfAid,
        wdmPassiveIfPhysicalLocation }
    STATUS      current
    DESCRIPTION
        "The passive i/f group objects V7 (28.0)."
    ::= { lumWdmGroups 71 }

wdmMeanChannelPowerControlGroupV1 OBJECT-GROUP
    OBJECTS {
        wdmMeanChannelPowerControlIndex,
        wdmMeanChannelPowerControlName,
        wdmMeanChannelPowerControlDescr,
        wdmMeanChannelPowerControlOcmSubrack,
        wdmMeanChannelPowerControlOcmSlot,
        wdmMeanChannelPowerControlOcmPort,
        wdmMeanChannelPowerControlOaSubrack,
        wdmMeanChannelPowerControlOaSlot,
        wdmMeanChannelPowerControlOaPort,
        wdmMeanChannelPowerControlMonitorName,
        wdmMeanChannelPowerControlAdminStatus,
        wdmMeanChannelPowerControlOperStatus,
        wdmMeanChannelPowerControlStartRegulation,
        wdmMeanChannelPowerControlRegulationRange,
        wdmMeanChannelPowerControlLatestRegulation,
        wdmMeanChannelPowerControlLatestChange,
        wdmMeanChannelPowerControlMonitorOffsetCalibrationFailed,
        wdmMeanChannelPowerControlRegulationState,
        wdmMeanChannelPowerControlTimeToNextRegulation,
        wdmMeanChannelPowerControlWantedChannelPower,
        wdmMeanChannelPowerControlCurrentChannelPower,
        wdmMeanChannelPowerControlCurrentGain,
        wdmMeanChannelPowerControlTotalChannelOutputPower,
        wdmMeanChannelPowerControlNumberOfChannels,
        wdmMeanChannelPowerControlAbsolutePowerOffset,
        wdmMeanChannelPowerControlRemainingPowerOffset,
        wdmMeanChannelPowerControlMonitorOffsetTooLarge,
        wdmMeanChannelPowerControlChannelPowerOutOfRange,
        wdmMeanChannelPowerControlRegulationInterval,
        wdmMeanChannelPowerControlAmplifierOutputPort,
        wdmMeanChannelPowerControlLatestAmplifierRxPower,
        wdmMeanChannelPowerControlLatestAmplifierTxPower,
        wdmMeanChannelPowerControlLocalId
    }
    STATUS      current
    DESCRIPTION
        "The wdm mean channel power control loop objects version 1 (27.0)"
    ::= { lumWdmGroups 72 }

wdmMeanChannelPowerControlGlobalGroupV1 OBJECT-GROUP
    OBJECTS {
        wdmMeanChannelPowerControlGlobalIndex,
        wdmMeanChannelPowerControlGlobalName,
        wdmMeanChannelPowerControlGlobalEntryCreate
    }
    STATUS      current
    DESCRIPTION
        "The bogus table used only to create entries in
        wdmMeanChannelPowerControl v1 (28.0)"
    ::= { lumWdmGroups 73 }

wdmGeneralGroupV7 OBJECT-GROUP
    OBJECTS {
        wdmGeneralLastChangeTime,
        wdmGeneralStateLastChangeTime,
        wdmGeneralWdmIfTableSize,
        wdmGeneralWdmPassiveIfTableSize,
        wdmGeneralWdmProtTableSize,
        wdmGeneralWdmVc4TableSize,
        wdmGeneralWdmCtrlChannelTableSize,
        wdmGeneralWdmCtrlGroupTableSize,
        wdmGeneralWdmSubChannelTableSize,
        wdmGeneralWdmMeanChannelPowerControlTableSize,
        wdmGeneralWdmMeanChannelPowerControlGlobalTableSize
    }
    STATUS      current
    DESCRIPTION
        "The WDM general objects v7 (28.0)."
    ::= { lumWdmGroups 74 }

wdmCtrlChannelGroupV5 OBJECT-GROUP
    OBJECTS {
        wdmCtrlChannelIndex,
        wdmCtrlChannelName,
        wdmCtrlChannelSubrack,
        wdmCtrlChannelSlot,
        wdmCtrlChannelTxPort,
        wdmCtrlChannelChannel,
        wdmCtrlChannelGroupNumber,
        wdmCtrlChannelAdminStatus,
        wdmCtrlChannelWantedOutputPower,
        wdmCtrlChannelCurrentOutputPower,
        wdmCtrlChannelCurrentAttenuation,
        wdmCtrlChannelForceRegulationCommand,
        wdmCtrlChannelOuputPowerControlFailure,
        wdmCtrlChannelCurrentPowerOutOfRange,
        wdmCtrlChannelAttenuationOutOfRange,
        wdmCtrlChannelStatus,
        wdmCtrlChannelStartupChannel,
        wdmCtrlChannelMonitorIndex,
        wdmCtrlChannelStartupCommand,
        wdmCtrlChannelSfpMissing,
        wdmCtrlChannelSfpMediaMismatch,
        wdmCtrlChannelLossOfSignal,
        wdmCtrlChannelDescr,
        wdmCtrlChannelMaxAttenuation,
        wdmCtrlChannelMinAttenuation,
        wdmCtrlChannelAttenControlOffset,
        wdmCtrlChannelAttenControlDegraded
    }
    STATUS      current
    DESCRIPTION
        "The wdm optical control channel objects. (30.0)"
    ::= { lumWdmGroups 77}

wdmIfGroupV31 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfContinousOptimization,
        wdmIfThresholdOptimizationResultCause,
        wdmIfDistributionRole,
        wdmIfConfigurationCommand,
        wdmIfNoFrequencySet,
        wdmIfFormat,
        wdmIfConfigurationFormatCommand,
--      wdmIfOHTransparency,
        wdmIfLinkDown,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback,
        wdmIfAutoNegotiationMode,
        wdmIfAutoNegotiationStatus,
        wdmIfFlowControlMode,
        wdmIfGroupLineMode,
        wdmIfFecType,
        wdmIfFarEndLoopback,
        wdmIfFarEndLoopbackTimeout,
        wdmIfFarEndLoopbackEnabled,
        wdmIfChangeLoopbackCommand,
        wdmIfFecFailure,
        wdmIfTxSignalStatus,
        wdmIfRxSignalStatus,
        wdmIfNearEndLoopback,
        wdmIfNearEndLoopbackTimeout,
        wdmIfNearEndLoopbackEnabled,
        wdmIfChangeNearEndLoopbackCommand,
        wdmIfSignalDegraded,
        wdmIfHubProtectionMode,
        wdmIfActualFormat,
        wdmIfTdcDispersion,
        wdmIfTdcDispersionCommand,
        wdmIfTdcDispersionMode,
        wdmIfLineControlLoopCurrentState,
        wdmIfSignalDegradeThreshold,
        wdmIfTrxThresholdOptimizationState,
        wdmIfTrxDecisionThreshold,
        wdmIfSwControlledLaserShutdown,
        wdmIfChangeSwControlledLaserShutdownCommand,
        wdmIfControlledLaserShutdownEnabled,
        wdmIfAid,
        wdmIfPhysicalLocation,
        wdmIfTrxTunable
    }
    STATUS      current
    DESCRIPTION
        "The wdm trunk interface objects V31. (32.1)"
    ::= { lumWdmGroups 78 }

-- ----------------------------------------------------
-- Compliance
-- ----------------------------------------------------

lumWdmBasicComplV1 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroup,
            wdmIfGroup,
            wdmProtGroup,
            wdmNotificationGroup }
    ::= { lumWdmCompl 1 }

lumWdmBasicComplV2 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V2."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroup,
            wdmIfGroup,
            wdmProtGroup,
            wdmNotificationGroup,
            wdmPassiveIfGroup }
    ::= { lumWdmCompl 2 }

lumWdmBasicComplV3 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V3."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroup,
            wdmProtGroup,
            wdmNotificationGroup,
            wdmPassiveIfGroup }
    ::= { lumWdmCompl 3 }

lumWdmBasicComplV4 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V4."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV2,
            wdmProtGroup,
            wdmNotificationGroup,
            wdmPassiveIfGroup }
    ::= { lumWdmCompl 4 }

lumWdmBasicComplV5 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V5."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV3 ,
            wdmProtGroup,
            wdmNotificationGroup,
            wdmPassiveIfGroupV2 }
    ::= { lumWdmCompl 5 }

lumWdmBasicComplV6 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V6."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV4,
            wdmProtGroup,
            wdmNotificationGroup,
            wdmPassiveIfGroupV3 }
    ::= { lumWdmCompl 6 }

lumWdmBasicComplV7 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V7."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV4,
            wdmProtGroup,
            wdmNotificationGroup,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 7 }

lumWdmBasicComplV8 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V8."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV5,
            wdmProtGroupV2,
            wdmNotificationGroup,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 8 }

lumWdmBasicComplV9 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V9."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV6,
            wdmProtGroupV2,
            wdmNotificationGroup,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 9 }

lumWdmBasicComplV10 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V10."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV6,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 10 }

lumWdmBasicComplV11 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V11."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV2,
            wdmIfGroupV7,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 11 }

lumWdmBasicComplV12 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V12."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV3,
            wdmIfGroupV7,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 12 }

lumWdmBasicComplV13 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V13."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV3,
            wdmIfGroupV8,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 13 }

lumWdmBasicComplV14 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V14."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV3,
            wdmIfGroupV9,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 14 }

lumWdmBasicComplV15 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V15."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV3,
            wdmIfGroupV10,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 15 }

lumWdmBasicComplV16 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V16."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV3,
            wdmIfGroupV11,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV4 }
    ::= { lumWdmCompl 16 }

lumWdmBasicComplV17 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V17. (2.2)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV3,
            wdmIfGroupV12,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV5 }
    ::= { lumWdmCompl 17 }

lumWdmBasicComplV18 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V18. (3.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV3,
            wdmIfGroupV13,
            wdmProtGroupV2,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV5 }
    ::= { lumWdmCompl 18 }

lumWdmBasicComplV19 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V19. (3.1)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV13,
            wdmProtGroupV3,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV5 }
    ::= { lumWdmCompl 19 }

lumWdmBasicComplV20 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V20. (4.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV14,
            wdmProtGroupV4,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6 }
    ::= { lumWdmCompl 20 }

lumWdmBasicComplV21 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V21. (4.1)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV14,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6 }
    ::= { lumWdmCompl 21 }

lumWdmBasicComplV22 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V22. (5.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV15,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6 }
    ::= { lumWdmCompl 22 }

lumWdmBasicComplV23 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V23. (6.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV16,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6 }
    ::= { lumWdmCompl 23 }

lumWdmBasicComplV24 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V24. (8.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV16,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6 }
    ::= { lumWdmCompl 24 }

lumWdmBasicComplV25 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V25. (9.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV17,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6 }
    ::= { lumWdmCompl 25 }

lumWdmBasicComplV26 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V26. (9.1)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV4,
            wdmIfGroupV18,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6 }
    ::= { lumWdmCompl 26 }

lumWdmBasicComplV27 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V27. (10.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV5,
            wdmIfGroupV19,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4Group }
    ::= { lumWdmCompl 27 }

lumWdmBasicComplV28 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V28. (11.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV5,
            wdmIfGroupV20,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4Group }
    ::= { lumWdmCompl 28 }

lumWdmBasicComplV29 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V29. (12.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV5,
            wdmIfGroupV21,
            wdmProtGroupV5,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4Group }
    ::= { lumWdmCompl 29 }

lumWdmBasicComplV30 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V30. (13.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV5,
            wdmIfGroupV22,
            wdmProtGroupV6,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4Group,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroup,
            wdmCtrlGroupGroup
 }
    ::= { lumWdmCompl 30 }

lumWdmBasicComplV31 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V31. (14.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV5,
            wdmIfGroupV22,
            wdmProtGroupV6,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4Group,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroup,
            wdmCtrlGroupGroupV2
 }
    ::= { lumWdmCompl 31 }

lumWdmBasicComplV32 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V32. (15.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV23,
            wdmProtGroupV6,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4Group,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroup,
            wdmCtrlGroupGroupV2,
            wdmSubChannelGroup
 }
    ::= { lumWdmCompl 32 }

lumWdmBasicComplV33 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V33. (16.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV23,
            wdmProtGroupV6,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV2,
            wdmCtrlGroupGroupV2,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 33 }

lumWdmBasicComplV34 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V34. (17.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV24,
            wdmProtGroupV7,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV2,
            wdmCtrlGroupGroupV2,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 34 }

lumWdmBasicComplV35 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V35. (18.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV25,
            wdmProtGroupV7,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV2,
            wdmCtrlGroupGroupV2,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 35 }


lumWdmBasicComplV36 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V36. (19.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV25,
            wdmProtGroupV7,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV3,
            wdmCtrlGroupGroupV2,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 36 }

lumWdmBasicComplV37 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V37. (20.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV26,
            wdmProtGroupV8,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV3,
            wdmCtrlGroupGroupV2,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 37 }

lumWdmBasicComplV38 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V38. (22.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV26,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV3,
            wdmCtrlGroupGroupV2,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 38 }

lumWdmBasicComplV39 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V38. (22.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV27,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV3,
            wdmCtrlGroupGroupV3,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 39 }

lumWdmBasicComplV40 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V40. (23.1)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV28,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV3,
            wdmCtrlGroupGroupV3,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 40 }

lumWdmBasicComplV41 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V41. (25.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV29,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV3,
            wdmCtrlGroupGroupV3,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup
  }
    ::= { lumWdmCompl 41 }

lumWdmBasicComplV42 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V42. (27.1)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV7,
            wdmIfGroupV29,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV6,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV4,
            wdmCtrlGroupGroupV3,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup,
            wdmDelayCompPGGroup,
            wdmDelayCompLinkGroup,
            wdmMeanChannelPowerControlGroupV1,
            wdmMeanChannelPowerControlGlobalGroupV1
        }
    ::= { lumWdmCompl 42 }

lumWdmBasicComplV43 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V43. (28.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV30,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV7,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV4,
            wdmCtrlGroupGroupV3,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup,
            wdmDelayCompPGGroup,
            wdmDelayCompLinkGroup
        }
    ::= { lumWdmCompl 43 }

lumWdmBasicComplV44 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V44. (29.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV30,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV7,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV4,
            wdmCtrlGroupGroupV4,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup,
            wdmDelayCompPGGroup,
            wdmDelayCompLinkGroup
        }
    ::= { lumWdmCompl 44 }

lumWdmBasicComplV45 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V45. (30.0)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV30,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV7,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV5,
            wdmCtrlGroupGroupV5,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup,
            wdmDelayCompPGGroup,
            wdmDelayCompLinkGroup
        }
    ::= { lumWdmCompl 45 }

lumWdmBasicComplV46 MODULE-COMPLIANCE
    STATUS     deprecated
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V46. (30.1)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV30,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV7,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV5,
            wdmCtrlGroupGroupV5,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup,
            wdmDelayCompPGGroup,
            wdmDelayCompLinkGroup
        }
    ::= { lumWdmCompl 46 }

lumWdmBasicComplV47 MODULE-COMPLIANCE
    STATUS     current
    DESCRIPTION
        "Basic implementation requirements for the WDM MIB V47. (32.1)"
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralGroupV6,
            wdmIfGroupV31,
            wdmProtGroupV9,
            wdmNotificationGroupV2,
            wdmPassiveIfGroupV7,
            wdmVc4GroupV2,
            wdmRemoteProtGroup,
            wdmCtrlChannelGroupV5,
            wdmCtrlGroupGroupV5,
            wdmSubChannelGroupV2,
            wdmCtrlGlobalGroup,
            wdmDelayCompPGGroup,
            wdmDelayCompLinkGroup
        }
    ::= { lumWdmCompl 47 }
    	
-- ----------------------------------------------------
-- Minimal object and event groups
-- ----------------------------------------------------

wdmGeneralMinimalGroupV1 OBJECT-GROUP
    OBJECTS {
        wdmGeneralLastChangeTime,
        wdmGeneralStateLastChangeTime,
        wdmGeneralWdmIfTableSize,
        wdmGeneralWdmPassiveIfTableSize }
    STATUS      current
    DESCRIPTION
        "The minimal general wdm objects."
    ::= { lumWdmMinimalGroups 1 }

wdmIfMinimalGroupV1 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual }
    STATUS      deprecated
    DESCRIPTION
        "The wdm trunk interface minimal objects."
    ::= { lumWdmMinimalGroups 2 }

wdmPassiveIfMinimalGroupV1 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfExpectedLambda,
        wdmPassiveIfUnexpectedLambda,
        wdmPassiveIfAdminStatus,
        wdmPassiveIfOperStatus }
    STATUS      deprecated
    DESCRIPTION
        "The passive i/f group minimal objects."
    ::= { lumWdmMinimalGroups 3 }

wdmIfMinimalGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmIfIndex,
        wdmIfName,
        wdmIfDescr,
        wdmIfSubrack,
        wdmIfSlot,
        wdmIfTxPort,
        wdmIfInvPhysIndexOrZero,
        wdmIfTxLambda,
        wdmIfHighSpeedMin,
        wdmIfHighSpeedMax,
        wdmIfPowerLevel,
        wdmIfPowerLevelHighThreshold,
        wdmIfPowerLevelLowThreshold,
        wdmIfLaserTemp,
        wdmIfLaserTempOffset,
        wdmIfLaserTempOffsetThreshold,
        wdmIfLaserMode,
        wdmIfLaserStatus,
        wdmIfAdminStatus,
        wdmIfOperStatus,
        wdmIfLossOfSignal,
        wdmIfReceivedPowerHigh,
        wdmIfReceivedPowerLow,
        wdmIfLaserBiasHigh,
        wdmIfForwardDefectIndication,
        wdmIfBackwardDefectIndication,
        wdmIfLossOfFrame,
        wdmIfAlarmIndicationSignal,
        wdmIfRemoteDefectIndication,
        wdmIfLossOfSync,
        wdmIfLossOfForwardingErrorCorrection,
        wdmIfLaserTempHigh,
        wdmIfLaserTempLow,
        wdmIfRxPort,
        wdmIfBitrateMismatch,
        wdmIfLaserBias,
        wdmIfLaserBiasThreshold,
        wdmIfLossOfSignalThreshold,
        wdmIfExpectedTxLambda,
        wdmIfForwardingErrorCorrectionMode,
        wdmIfTraceIntrusionMode,
        wdmIfTraceTransmitted,
        wdmIfTraceReceived,
        wdmIfTraceExpected,
        wdmIfTraceAlarmMode,
        wdmIfTraceMismatch,
        wdmIfLaserStatusLastChangeTime,
        wdmIfSuppressRemoteAlarms,
        wdmIfSerialNumberMismatch,
        wdmIfOptimizeDecisionThreshold,
        wdmIfThresholdOptimizationState,
        wdmIfUseHwDefaultDecisionThreshold,
        wdmIfFecCorrectedZeros,
        wdmIfFecCorrectedOnes,
        wdmIfOptimizedForSerialNumber,
        wdmIfRelativeDecisionThreshold,
        wdmIfTrxCodeMismatch,
        wdmIfTrxBitrateUnavailable,
        wdmIfTrxMissing,
        wdmIfTrxClass,
        wdmIfLaserTempHighRelativeThreshold,
        wdmIfLaserTempLowRelativeThreshold,
        wdmIfTransmitterFailed,
        wdmIfReceiverSensitivity,
        wdmIfPowerLevelLowRelativeThreshold,
        wdmIfUnexpectedTxLambda,
        wdmIfIllegalFrequency,
        wdmIfLaserForcedOn,
        wdmIfTrafficCombination,
        wdmIfSelectTrafficCombination,
        wdmIfObjectProperty,
        wdmIfTxPowerLevel,
        wdmIfLaserTempActual,
        wdmIfTrxFailed,
        wdmIfDisabled,
        wdmIfLoopback }
    STATUS      current
    DESCRIPTION
        "The wdm trunk interface minimal objects."
    ::= { lumWdmMinimalGroups 4 }

wdmPassiveIfMinimalGroupV2 OBJECT-GROUP
    OBJECTS {
        wdmPassiveIfIndex,
        wdmPassiveIfName,
        wdmPassiveIfDescr,
        wdmPassiveIfInvPhysIndexOrZero,
        wdmPassiveIfSubrack,
        wdmPassiveIfSlot,
        wdmPassiveIfPort,
        wdmPassiveIfDirection,
        wdmPassiveIfLambdaType,
        wdmPassiveIfLambda,
        wdmPassiveIfExpectedLambda,
        wdmPassiveIfUnexpectedLambda,
        wdmPassiveIfAdminStatus,
        wdmPassiveIfOperStatus,
        wdmPassiveIfIfNo}
    STATUS      current
    DESCRIPTION
        "The passive i/f group minimal objects."
    ::= { lumWdmMinimalGroups 5 }


-- ----------------------------------------------------
-- Minimal Compliance
-- ----------------------------------------------------

lumWdmMinimalComplV1 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Minimal implementation requirements for the wdm MIB v1."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralMinimalGroupV1,
            wdmIfMinimalGroupV1,
            wdmPassiveIfMinimalGroupV1 }
    ::= { lumWdmMinimalCompl 1 }

lumWdmMinimalComplV2 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Minimal implementation requirements for the wdm MIB v2."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralMinimalGroupV1,
            wdmIfMinimalGroupV2,
            wdmProtGroupV5,
            wdmPassiveIfMinimalGroupV1 }
    ::= { lumWdmMinimalCompl 2 }

lumWdmMinimalComplV3 MODULE-COMPLIANCE
    STATUS      deprecated
    DESCRIPTION
        "Minimal implementation requirements for the wdm MIB v3 (30.1)."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralMinimalGroupV1,
            wdmIfMinimalGroupV2,
            wdmProtGroupV5,
            wdmPassiveIfMinimalGroupV1 }
    ::= { lumWdmMinimalCompl 3 }

lumWdmMinimalComplV4 MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "Minimal implementation requirements for the wdm MIB v4 (31.1)."
    MODULE
        MANDATORY-GROUPS {
            wdmGeneralMinimalGroupV1,
            wdmIfMinimalGroupV2,
            wdmProtGroupV5,
            wdmPassiveIfMinimalGroupV2 }
    ::= { lumWdmMinimalCompl 4 }
END



